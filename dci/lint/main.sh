#!/bin/bash
set -x

# A print function with blank lines before and after
function print(){
    echo ""
    echo "==dci-lint==$*"
    echo ""
}

print "避免NPE是程序员的基本修养 ！"
print "开始style checking"

wd=$(pwd)
print "当前工作目录：$wd"

check_jar_path="$wd/dci/lint/checkstyle-9.3-all.jar"
check_xml_path="$wd/dci/lint/check_style.xml"
temp_path="$wd/dci/out/lint/temp/"
final_temp_path="$wd/dci/out/lint.out.txt"
diff_path="$wd/dci/out/lint.diff.txt"

# Clear temp files
rm -rf $temp_path $diff_path $final_temp_path
mkdir -p $wd/dci/out

is_err=0
is_warn=0
# Extract changed lines
#if [ -z "$CI_DEFAULT_BRANCH" ]; then
#  git diff --cached -U0 --no-color --no-merges | grep -E "^\+\+\+|^@@|\+" >$diff_path
#else
#  git diff -U0 --no-color remotes/origin/$CI_DEFAULT_BRANCH | grep -E "^\+\+\+|^@@|\+" > $diff_path
#fi

git diff -U0 --no-color remotes/origin/$CI_DEFAULT_BRANCH | grep -E "^\+\+\+|^@@|\+" > $diff_path

if [ -s "$diff_path" ]; then
    print "检查文件的差异："
    # 读取整个文件到数组
    mapfile -t lines < "$diff_path"

    # 创建一个数组来保存进程ID
    pids=()

    # 第一个循环：并行执行检查
    for line in "${lines[@]}"; do
        if [[ $line == +++* ]]; then
            file=$(echo "$line" | sed 's|+++ b/||')
            echo "检查文件: $file"
            check_temp_result_path="$temp_path$file"
            directory=$(dirname "$check_temp_result_path")

            # 检查并创建目录
            mkdir -p "$directory" || { echo "Failed to create directory: $directory"; exit 1; }

            {
                java -jar "$check_jar_path" -c "$check_xml_path" "$file" > "$check_temp_result_path"
            } &
            pids+=($!)
        fi
    done

    # 等待所有后台进程完成
    wait "${pids[@]}"

    # 第二个循环：获取检测结果
    current_line=0
    for line in "${lines[@]}"; do
        if [[ $line == +++* ]]; then
            check_temp_result_path="$temp_path$(echo "$line" | sed 's|+++ b/||')"
        elif [[ $line == @@* ]]; then
            start_line=$(echo "$line" | awk '{print $3}' | awk -F+ '{print $2}' | awk -F, '{print $1}')
            current_line=$start_line
        elif [[ $line == +* && $line != "+++"* ]]; then
            if [[ -s "$check_temp_result_path" ]]; then
                grep -E "java:$current_line:" "$check_temp_result_path" >> "$final_temp_path"
                ((current_line++))
            fi
        fi
    done

    if [ -s "$final_temp_path" ]; then
        err=$(grep "ERROR" "$final_temp_path")
        warn=$(grep "WARN" "$final_temp_path")

        if [[ -n $err ]]; then
            print "$err"
            is_err=1
        fi

        if [[ -n $warn ]]; then
            print "$warn"
            is_warn=1
        fi
    fi
fi

# Check if $temp_path is not empty
if [ ! -f "$final_temp_path" ] || [ ! -s "$final_temp_path" ]; then
    print "No style violations detected or an error occurred."
    exit 0
fi

print "检查完成，祝你好运"

# Uncomment the line below to remove temp files after checking
# rm -rf $temp_path $diff_path

if [ $is_err -ne 0 ] || [ $is_warn -ne 0 ]; then
    print "请先符合style才能提交！"
    exit 1
fi
rm -rf $temp_path $diff_path $final_temp_path
exit 0
