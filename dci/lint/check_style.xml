<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
    "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
    "https://checkstyle.org/dtds/configuration_1_3.dtd">

<module name="Checker">
  <property name="charset" value="UTF-8"/>
  
  <!-- 排除测试文件 -->
  <module name="BeforeExecutionExclusionFileFilter">
    <property name="fileNamePattern" value=".*Test\.java$"/>
  </module>

  <module name="TreeWalker">
     <property name="fileExtensions" value="java"/>
    <!-- 规则7：常量命名 -->
    <module name="ConstantName">
      <property name="format" value="^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$"/>
    </module>

    <!-- 规则4：方法行数限制 -->
    <module name="MethodLength">
      <property name="max" value="200"/>
      <property name="countEmpty" value="false"/>
    </module>

		
 <!-- 规则6：禁用三目运算符 -->
     <module name="IllegalToken">
      <property name="tokens" value="QUESTION"/>
    </module>

   <!-- ========= 自定义XPath规则 ========= -->
        <!-- 规则1：Controller类校验 -->
        <module name="RegexpSinglelineJava">
      <!-- . matches any character, so we need to
           escape it and use \. to match dots. -->
      <property name="format" value=".*@(Controller|GetMapping|PutMapping).*"/>
      <property name="ignoreComments" value="true"/>
    </module>
    
    <!-- 检查方法体内是否包含字段赋值，这个规则会出先大量误报，原因有get开头的业务方法名 -->
    <!-- 检查浮点数的直接 == 比较 -->
  <module name="RegexpSinglelineJava">
    <property name="format" value="(\bdouble\b|\bfloat\b|\bDouble\b|\bFloat\b).*==|==.*(\bdouble\b|\bfloat\b|\bDouble\b|\bFloat\b)"/>
    <property name="message" value="禁止直接使用 == 比较浮点数（需使用误差允许范围比较）"/>
    <property name="severity" value="error"/>
  </module>

  <!-- 检查浮点数的 equals 方法比较 -->
  <module name="RegexpSinglelineJava">
    <property name="format" value="\.equals\(.*(double|float|Double|Float)"/>
    <property name="message" value="禁止使用 equals 比较浮点数（精度问题）"/>
    <property name="severity" value="error"/>
  </module>

  <!-- ban select * -->
  <module name="RegexpSinglelineJava">
    <property name="format" value="(S|s)(E|e)(L|l)(E|e)(C|c)(T|t)\W+\*"/>
    <property name="message" value="禁止使用select *"/>
    <property name="severity" value="error"/>
  </module>
	
 <!-- 规则5：远程调用超时检查，目前无法处理，因为远程调用线程池是内部配置无法精确扫描 -->
</module>
</module>