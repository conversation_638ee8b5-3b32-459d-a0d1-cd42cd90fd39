#!/bin/bash
# A print function with blank lines before and after
function print(){
    echo "==dci-ban==$*"
}

wd=$(pwd)
print "当前工作目录：$wd"

gitlabciallows="$wd/dci/ban/.gitlab-ci.yml.allows"
pomallows="$wd/dci/ban/pom.xml.allows"
final_temp_path="$wd/dci/out/ban.out.txt"
temp_path="$wd/dci/out/ban.temp.txt"
diff_path="$wd/dci/out/ban.diff.txt"

if [ -z "$CI_DEFAULT_BRANCH" ]; then
  current_email=`git config user.email`
  git diff --cached -U0 --no-color | grep -E "^\+\+\+|^@@|\+" >$diff_path
else
  current_email=`git log -1 --format="%ae"`
  git diff -U0 --no-color remotes/origin/$CI_DEFAULT_BRANCH | grep -E "^\+\+\+|^@@|\+" > $diff_path
fi

while IFS= read -r line; do
  if [[ $line == +++* ]]; then
    file=$(echo $line | sed 's|+++ b/||')
    print "检查文件: $file"
    file_base_name=`basename $file`
    if [ $file_base_name = ".gitlab-ci.yml" ]; then
      grep $current_email $gitlabciallows
      if [ $? -ne 0 ]; then
        print "禁止修改.gitlab-ci.yml文件，如需修改${file},${line}, 请联系运维。"
        exit 1
      fi
    fi
  fi
done < $diff_path

