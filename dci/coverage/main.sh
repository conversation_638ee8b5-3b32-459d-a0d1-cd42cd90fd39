#!/bin/bash

# A print function with blank lines before and after
function print(){
    echo "==coverage==$*"
}

print "开始coverage checking"

wd=$(pwd)
print "当前工作目录：$wd"

coverage_rate_path="$wd/dci/coverage/coverage_rate.txt"
temp_path="$wd/dci/out/coverage.temp.txt"
final_temp_path="$wd/dci/out/coverage.out.txt"
diff_path="$wd/dci/out/coverage.diff.txt"
pass_rate=10

# Clear temp files
rm -f $temp_path $diff_path $final_temp_path

coverage_rate="cat $coverage_rate_path"
is_err=0

# Extract changed lines
if [ -z "$CI_DEFAULT_BRANCH" ]; then
  git diff --cached -U0 --no-color --no-merges | grep -E "^\+\+\+|^@@|\+" >$diff_path
else
  git diff -U0 --no-color remotes/origin/$CI_DEFAULT_BRANCH | grep -E "^\+\+\+|^@@|\+" > $diff_path
fi

if [ -s "$diff_path" ]; then
    print "检查文件的差异："
    current_line=0
    while IFS= read -r line; do
        if [[ $line == +++* ]]; then
          if [[ $file != *src/main/java/* ]]; then
            rm -f $temp_path
            continue
          fi

          file=$(echo $line | sed 's|+++ b/||')
          print "检查文件: $file"


          # module
          echo $file | awk -F 'src/main/java/' '{print $1}' > $temp_path;
          module_name=`cat $temp_path`
          # package
          echo $file | awk -F 'src/main/java/' '{print $2}' > $temp_path;
          file_name=`cat $temp_path|xargs basename`
          package_name=`cat $temp_path|xargs dirname|sed s/\//./g`
          coverage_file="${module_name}/target/site/jacoco/${package_name}/${file_name}.html"
          if [ -e $coverage_file ]; then
            curl -XPOST -F "file=@${coverage_file}" https://ci-tools.meiyunji.net/jacoco-file-handler.txt >$temp_path
          else
            rm -f $temp_path
          fi
        elif [[ $line == @@* ]]; then
          start_line=$(echo $line | awk '{print $3}' |awk -F+ '{print $2}' |awk -F, '{print $1}')
          current_line=$start_line
        elif [[ $line == +* && $line != "+++"* ]]; then
          if [ -s "$temp_path" ]; then
            grep "java:$current_line:" "$temp_path" >> $final_temp_path
            current_line=$((current_line + 1))
          fi
        fi
    done < $diff_path

    if [ -s "$final_temp_path" ]; then
            cat "$final_temp_path"|sed "s/:NC/:未覆盖/g"|sed "s/:C/:已覆盖/g"

            nc_count=$(grep ":NC" "$final_temp_path")
            c_count=$(grep ":C" "$final_temp_path")
            total_count=$(($nc_count + $c_count))

            rate=$(( $c_count * 100 / $total_count ))
            if [ $rate -lt $coverage_rate ]; then
                print "覆盖率 $rate 低于阈值 $$coverage_rate"
                is_err=1
            fi
        fi
fi

# Uncomment the line below to remove temp files after checking
# rm -rf $temp_path $diff_path

if [ $is_err -ne 0 ]; then
    print "请先符合通过率 $coverage_rate 才能提交！"
    exit 1
fi

exit 0
