package com.meiyunji.sponsored.api;

import com.meiyunji.sellfox.aadas.types.enumeration.TaskTimeType;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyScheduleDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusSequenceDao;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategySchedule;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatus;
import com.meiyunji.sponsored.service.taskGrpcApi.AadasApiFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@SpringBootTest
@RunWith(SpringRunner.class)
public class AdvertiseStrategyApiTest {

    @Autowired
    AadasApiFactory aadasApiFactory;

    @Autowired
    private IShopAuthService shopAuthService;

    @Autowired
    private AdvertiseStrategyStatusSequenceDao advertiseStrategyStatusSequenceDao;
    @Autowired
    private AdvertiseStrategyStatusDao advertiseStrategyStatusDao;
    @Autowired
    private AdvertiseStrategyScheduleDao advertiseStrategyScheduleDao;

    @Test
    public void genId() {
        Long aLong = advertiseStrategyStatusSequenceDao.genId();
        System.out.println(aLong);
    }

    @Test
    public void setScheduleTask() throws Exception {
        List<AdvertiseStrategyStatus> advertiseStrategyStatuses = advertiseStrategyStatusDao
                .listByCondition(19, new ConditionBuilder.Builder().equalTo("puid", 19)
                        .equalTo("status", "ENABLED").build());
        for (AdvertiseStrategyStatus status : advertiseStrategyStatuses) {
            List<AdvertiseStrategySchedule> list = advertiseStrategyScheduleDao.listByTaskIds(19,
                    Collections.singletonList(status.getTaskId()), status.getItemType());

            List<AdvertiseStrategySchedule> collect1 = list.stream().filter(o -> status.getItemType().equalsIgnoreCase(o.getItemType())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect1)) {
                continue;
            }
            List<AdvertiseStrategySchedule> collect = collect1.stream().filter(o-> status.getItemType().equalsIgnoreCase(o.getItemType())).map(item -> {
                AdvertiseStrategySchedule schedule = new AdvertiseStrategySchedule();
                schedule.setDay(item.getDay());
                schedule.setCampaignId(item.getCampaignId());
                schedule.setAdType(item.getAdType());
                schedule.setAdGroupId(item.getAdGroupId());
                schedule.setStart(item.getStart());
                schedule.setEnd(item.getEnd());
                schedule.setOriginValue(item.getOriginValue());
                schedule.setNewValue(item.getNewValue());
                schedule.setShopId(item.getShopId());
                schedule.setPuid(item.getPuid());
                schedule.setItemId(item.getItemId());
                return schedule;
            }).collect(Collectors.toList());
            TaskTimeType taskTimeType = TaskTimeType.targetBid;
            if ("CAMPAIGN".equalsIgnoreCase(status.getItemType())) {
                taskTimeType = TaskTimeType.campaignBudget;
            } else if ("CAMPAIGN_PLACEMENT".equalsIgnoreCase(status.getItemType())) {
                taskTimeType = TaskTimeType.campaignPlacement;
            } else if ("TARGET".equalsIgnoreCase(status.getItemType()) && "keywordTarget".equalsIgnoreCase(status.getTargetType())) {
                taskTimeType = TaskTimeType.keywordBid;
            }
            aadasApiFactory.getStrategyApi(taskTimeType).setSchedule(status.getTaskId(),1L, collect, false);
        }



    }

    @Test
    public void removeSchedule() {
        try {
            aadasApiFactory.getStrategyApi(TaskTimeType.targetBid).removeSchedule(100, 355, 11L, false);
        } catch (Exception exception) {
            exception.printStackTrace();
        }

    }

}
