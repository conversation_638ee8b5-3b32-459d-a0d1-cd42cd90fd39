package com.meiyunji.sponsored.interceptor;

import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import io.grpc.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcGlobalInterceptor;
import org.slf4j.MDC;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.UUID;

/**
 * @author: wade
 * @date: 2021/10/23 15:37
 * @describe: 自定义grpc拦截器
 */
@Configuration
@Slf4j
public class InterceptorConfig {



    @GRpcGlobalInterceptor
    @Order(1)
    public class CustomGlobalInterceptor implements ServerInterceptor {

        @Override
        public <ReqT, RespT> ServerCall.Listener<ReqT> interceptCall(ServerCall<ReqT, RespT> serverCall, Metadata metadata, ServerCallHandler<ReqT, RespT> serverCallHandler) {
            // 存入MDC中
            setGrpcLogInfo(metadata);
//            // 添加流ID追踪
//            String streamId = "unknown";
//            try {
//                // 尝试1.44+版本的方法
//                Method method = serverCall.getClass().getMethod("getStreamId");
//                streamId = String.valueOf(method.invoke(serverCall));
//            } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e1) {
//                log.error("getStreamId error:", e1);
//            }
//
//            log.info("🟢 INTERCEPT START [stream={}]", streamId);
//            MDC.put("streamId", streamId);
            ServerCall.Listener<ReqT> listener = serverCallHandler.startCall(serverCall, metadata);
            return new ExceptionHandlingServerCallListener<>(listener, serverCall, metadata);
        }
    }

    private static final Metadata.Key<String> GRPC_TRACE_ID_FIELD = Metadata.Key.of(Constants.TRACE_ID, Metadata.ASCII_STRING_MARSHALLER);
    private static final Metadata.Key<String> GRPC_PUID_FIELD = Metadata.Key.of(Constants.PUID, Metadata.ASCII_STRING_MARSHALLER);
    private static final Metadata.Key<String> GRPC_UID_FIELD = Metadata.Key.of(Constants.UID, Metadata.ASCII_STRING_MARSHALLER);
    private static final Metadata.Key<String> GRPC_ADMIN_USER = Metadata.Key.of(Constants.ADMIN_USER, Metadata.ASCII_STRING_MARSHALLER);


    /**
     * grpc 头部获取traceId
     */
    private void setGrpcLogInfo(Metadata metadata) {
        // 从元grpc head 注入也可以
        String grpcTraceId = metadata.get(GRPC_TRACE_ID_FIELD);
        log.info("grpcTraceId = {}", grpcTraceId);
        if (StringUtils.isBlank(grpcTraceId)) {
            grpcTraceId = UUID.randomUUID().toString().replaceAll("-", "");
        }
        log.info("traceId = {}", grpcTraceId);
        MDC.put(Constants.TRACE_ID, grpcTraceId);
        String grpcPuId = metadata.get(GRPC_PUID_FIELD);
        String grpcUid = metadata.get(GRPC_UID_FIELD);
        String grpcAdminUser = metadata.get(GRPC_ADMIN_USER);
        if (StringUtils.isNotBlank(grpcPuId)) {
            MDC.put(Constants.PUID, grpcPuId);
        }
        if (StringUtils.isNotBlank(grpcUid)) {
            MDC.put(Constants.UID, grpcUid);
        }
        if (StringUtils.isNotBlank(grpcAdminUser)) {
            MDC.put(Constants.ADMIN_USER, grpcAdminUser);
        }
    }

    /**
     * 移除日志信息
     */
    private void removeGrpcLogInfo(){
        log.info("removeGrpcLogInfo  traceId: {}", MDC.get(Constants.TRACE_ID));
        MDC.remove(Constants.TRACE_ID);
        MDC.remove(Constants.PUID);
        MDC.remove(Constants.UID);
        MDC.remove(Constants.ADMIN_USER);
    }

    private class ExceptionHandlingServerCallListener<ReqT, RespT>
            extends ForwardingServerCallListener.SimpleForwardingServerCallListener<ReqT> {
        private ServerCall<ReqT, RespT> serverCall;
        private Metadata metadata;

        ExceptionHandlingServerCallListener(ServerCall.Listener<ReqT> listener, ServerCall<ReqT, RespT> serverCall,
                                            Metadata metadata) {
            super(listener);
            this.serverCall = serverCall;
            this.metadata = metadata;
        }

        @Override
        public void onHalfClose() {
            try {
                String traceId = MDC.get(Constants.TRACE_ID);
                log.info("onHalfClose traceId = {}", traceId);
                super.onHalfClose();
            } catch (Exception ex) {
                handleException(ex, serverCall, metadata);
                if(ex instanceof SponsoredBizException){
                    // 业务异常不抛异常
                    return;
                }
                if (ex instanceof RuntimeException) {
                    // 运行时异常不抛异常，避免springboot框架重复关闭连接
                    return;
                }
                throw ex;
            }
        }

        @Override
        public void onMessage(ReqT message) {
//            setGrpcLogInfo(metadata);
            super.onMessage(message);
        }

        @Override
        public void onReady() {
            try {
                super.onReady();
            } catch (Exception ex) {
                handleException(ex, serverCall, metadata);
                throw ex;
            }
        }

        @Override
        public void onComplete() {
            log.info("remove traceId");
            removeGrpcLogInfo();
            super.onComplete();
        }

        /**
         * 旧代码  serviceException异常抛出处理
         *
         * @param exception
         * @param serverCall
         * @param metadata
         */
        private void handleException(Exception exception, ServerCall<ReqT, RespT> serverCall, Metadata metadata) {
            if(exception instanceof SponsoredBizException){
                log.info("业务异常:", exception);
                removeGrpcLogInfo();;
                serverCall.close(Status.UNAVAILABLE.withDescription(exception.getMessage()).withCause(new ServiceException()), metadata);
            }else if (exception instanceof ServiceException) {
                log.error("error:", exception);
                removeGrpcLogInfo();;
                serverCall.close(Status.UNAVAILABLE.withDescription(exception.getMessage()).withCause(new ServiceException()), metadata);
            } else {
                log.error("error:", exception);
                removeGrpcLogInfo();;
                serverCall.close(Status.UNKNOWN, metadata);
            }
        }

    }

}