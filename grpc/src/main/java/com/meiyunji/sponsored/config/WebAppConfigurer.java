package com.meiyunji.sponsored.config;

import com.meiyunji.sellfox.right.interceptor.DataMockInterceptor;
import com.meiyunji.sellfox.right.interceptor.HeadInterceptor;
import com.meiyunji.sellfox.right.interceptor.UserInterceptor;
import com.meiyunji.sellfox.right.interceptor.right.CheckRightInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * http请求拦截配置
 */
@Configuration
public class WebAppConfigurer implements WebMvcConfigurer {

    @Autowired
    private HeadInterceptor headInterceptor;
    @Autowired
    private UserInterceptor userInterceptor;
    @Autowired
    private CheckRightInterceptor checkRightInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(headInterceptor);
        registry.addInterceptor(userInterceptor);
        // sellfox权限控制拦截器
        registry.addInterceptor(checkRightInterceptor);
    }
}