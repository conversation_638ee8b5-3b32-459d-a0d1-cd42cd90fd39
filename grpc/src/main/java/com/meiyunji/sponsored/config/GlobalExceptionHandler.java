package com.meiyunji.sponsored.config;

import com.alibaba.fastjson.JSONObject;
import com.meiyunji.sellfox.core.exception.BizServiceException;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.tiktok.advertising.exception.TikTokApiException;
import com.tiktok.advertising.exception.TikTokSDKException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/07/03 17:00
 * @description 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<Object> handleValidationExceptions(MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        log.error("参数校验错误:{}", JSONObject.toJSONString(errors));
        return ResultUtil.returnErr(JSONObject.toJSONString(errors));
    }

    /**
     * 处理未知异常
     *
     * @param e 异常
     * @return 结果
     */
    @ExceptionHandler(Throwable.class)
    @ResponseBody
    public Result<Object> exceptionHandler(Throwable e) {
        log.error("controller层异常", e);
        return Result.builder().error("系统异常,请联系系统管理员").build();
    }

    /**
     * 处理未知异常
     *
     * @param e 异常
     * @return 结果
     */
    @ExceptionHandler(SponsoredBizException.class)
    public Result<Object> exceptionHandler(SponsoredBizException e) {
        log.info("业务异常", e);
        return Result.builder().error(e.getMsg()).build();
    }


    /**
     * 捕获BizServiceException
     * @param e 异常
     * @return
     */
    @ExceptionHandler(BizServiceException.class)
    public Result<Object> bizServiceException(BizServiceException e) {
        log.info("BizServiceException,code:{},msg:{},e", e.getCode() , e.getMessage(),e);
        return Result.builder().error(e.getMessage()).build();
    }

    // todo chenheng 处理token过期

    @ExceptionHandler(TikTokApiException.class)
    public Result<Object> handlerTikTokApiException(TikTokApiException e) {
        log.info("TikTokSDKException msg:{}", e.getMessage(), e);
        return Result.builder().error("tiktok调用异常 " + e.getMessage()).build();
    }

    @ExceptionHandler(TikTokSDKException.class)
    public Result<Object> handlerTikTokSDKException(TikTokSDKException e) {
        log.info("TikTokSDKException msg:{}", e.getMessage(), e);
        return Result.builder().error("tiktok调用异常 " + e.getMessage()).build();
    }

}
