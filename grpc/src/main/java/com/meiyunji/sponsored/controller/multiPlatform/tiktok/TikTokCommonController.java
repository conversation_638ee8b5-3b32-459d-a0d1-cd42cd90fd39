package com.meiyunji.sponsored.controller.multiPlatform.tiktok;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.sellfox.right.annotation.CheckRight;
import com.meiyunji.sellfox.right.holder.dto.UserDTO;
import com.meiyunji.sellfox.right.utils.RightContextUtil;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.security.SellfoxRightResource;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.service.multiPlatform.shop.dao.IMultiPlatformShopAuthDao;
import com.meiyunji.sponsored.service.multiPlatform.shop.po.MultiPlatformShopAuth;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.dao.TikTokAdvertiserAccountDao;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.dao.TikTokStoreInfoDao;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.TikTokAdvertiser;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.TikTokShop;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokAdvertiserAccount;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokStoreInfo;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request.TikTokCommonRequest;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.service.TikTokCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/sellfox/tiktok/common")
public class TikTokCommonController {

    @Autowired
    private TikTokStoreInfoDao tiktokStoreInfoDao;
    @Autowired
    private IMultiPlatformShopAuthDao multiPlatformShopAuthDao;
    @Autowired
    private TikTokAdvertiserAccountDao tikTokAdvertiserAccountDao;

    @Autowired
    private TikTokCommonService tikTokCommonService;

    @PostMapping("/shop/list")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__VIEW_LIST})
    public Result<?> shopList(@RequestBody TikTokCommonRequest param) {
        UserDTO user = RightContextUtil.getUser();
        int puid = user.getPuid().intValue();
        int uid = user.getId().intValue();
        List<String> advertiserIds = Objects.nonNull(param.getAdvertiserId()) ? Lists.newArrayList(param.getAdvertiserId()) : Collections.emptyList();
        List<Integer> shopIds = tikTokCommonService.checkShopRight(uid, (Objects.nonNull(param.getShopId()) ? Lists.newArrayList(param.getShopId()) : Collections.emptyList()));
        List<TikTokStoreInfo> storeInfo = tiktokStoreInfoDao.getStoreInfo(puid, shopIds, advertiserIds);
        if (CollectionUtils.isEmpty(storeInfo)) {
            return ResultUtil.success(Collections.singletonMap("shopList", Collections.emptyList()));
        }
        List<MultiPlatformShopAuth> list = multiPlatformShopAuthDao.listByPuidAndShopIds(puid, shopIds);
        Map<Integer, String> shopNameMap = StreamUtil.toMap(list, MultiPlatformShopAuth::getId, MultiPlatformShopAuth::getName);

        Map<String, List<String>> storeAdvertiserMap = StreamUtil.groupingBy(storeInfo, TikTokStoreInfo::getStoreId, TikTokStoreInfo::getAdvertiserId);

        Set<String> storeIdSet = new HashSet<>();
        List<TikTokShop> shopList = storeInfo.stream().map(i -> {
            if (storeIdSet.contains(i.getStoreId())) {
                return null;
            }
            storeIdSet.add(i.getStoreId());
            TikTokShop shop = new TikTokShop();
            shop.setShopId(i.getShopId());
            shop.setShopName(shopNameMap.getOrDefault(i.getShopId(), ""));
            shop.setStoreId(i.getStoreId());
            shop.setStoreName(i.getStoreName());
            shop.setAdvertiserIds(storeAdvertiserMap.getOrDefault(i.getStoreId(), Collections.emptyList()));
            return shop;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return ResultUtil.success(Collections.singletonMap("shopList", shopList));
    }

    @PostMapping("/advertiser/list")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__VIEW_LIST})
    public Result<?> advertiserList(@RequestBody TikTokCommonRequest param) {
        UserDTO user = RightContextUtil.getUser();
        int puid = user.getPuid().intValue();
        int uid = user.getId().intValue();
        List<Integer> shopIds = tikTokCommonService.checkShopRight(uid, (Objects.nonNull(param.getShopId()) ? Lists.newArrayList(param.getShopId()) : Collections.emptyList()));
        List<TikTokAdvertiserAccount> advertisers = tikTokAdvertiserAccountDao.listAuthAccountByPuidAndShopIds(puid, shopIds);
        if (CollectionUtils.isEmpty(advertisers)) {
            return ResultUtil.success(Collections.singletonMap("advertiserList", Collections.emptyList()));
        }
        List<String> advertiserIds = advertisers.stream().map(TikTokAdvertiserAccount::getAdvertiserId).distinct().collect(Collectors.toList());
        List<TikTokStoreInfo> storeInfo = tiktokStoreInfoDao.getStoreInfo(puid, null, advertiserIds);
        Map<String, List<Integer>> map = StreamUtil.groupingBy(storeInfo, TikTokStoreInfo::getAdvertiserId, TikTokStoreInfo::getShopId);

        List<TikTokAdvertiser> advertiserList = advertisers.stream().map(i -> {
            TikTokAdvertiser advertiser = new TikTokAdvertiser();
            advertiser.setAdvertiserId(i.getAdvertiserId());
            advertiser.setAdvertiserName(i.getName());
            advertiser.setShopIdList(map.getOrDefault(i.getAdvertiserId(), Collections.emptyList()));
            advertiser.setCurrency(i.getCurrency());
            advertiser.setAuthStatus(i.getAuthStatus());
            return advertiser;
        }).collect(Collectors.toList());
        return ResultUtil.success(Collections.singletonMap("advertiserList", advertiserList));
    }


}
