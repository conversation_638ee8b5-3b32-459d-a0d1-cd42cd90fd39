package com.meiyunji.sponsored.controller.amazon.ads;

import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.AssertUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.rpc.sp.campaign.TargetInfoResp;
import com.meiyunji.sponsored.rpc.sp.campaign.TargetResp;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.amc.req.*;
import com.meiyunji.sponsored.service.amc.resp.*;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dto.sp.CreateSpTargetDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.SpTargetService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.KeywordsVo;
import com.meiyunji.sponsored.service.cpc.vo.TargetingVo;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: hejh
 * @Date: 2025/2/12 13:24
 */
@Slf4j
@RestController
@RequestMapping("/sp/create")
public class CreateSpAdsController {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;
    @Autowired
    private SpTargetService spTargetService;

    /**
     * 创建投放
     *
     * @param req
     * @return
     */
    @PostMapping("/target/create")
    @ResponseBody
    public Result<CreateSpTargetResp> createTarget(@RequestBody @Valid CreateSpTargetReq req) {
        log.info("/sp/create/target/create req: {}", req);
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(req.getShopId(), req.getPuid());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        // 校验配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(req.getPuid(), req.getShopId());
        if (amazonAdProfile == null) {
            AssertUtil.fail("没有站点对应的配置信息");
        }
        AmazonAdGroup adGroup = amazonAdGroupDao.getByAdGroupId(req.getPuid(), req.getShopId(), req.getGroupId());
        if (adGroup == null) {
            AssertUtil.fail("没有对应的广告组");
        }
        CreateSpTargetDto spTargetDto = new CreateSpTargetDto();
        spTargetDto.setUid(req.getUid());
        spTargetDto.setPuid(req.getPuid());
        spTargetDto.setShopId(req.getShopId());
        spTargetDto.setLoginIp(req.getLoginIp());
        spTargetDto.setGroupId(req.getGroupId());
        spTargetDto.setAutoTarget(false);
        spTargetDto.setKeywordTarget(req.getKeywordTarget());
        spTargetDto.setKeywords(req.getKeywords());
        spTargetDto.setTargetings(req.getTargetings());

        TargetResp spTarget = spTargetService.createSpTarget(amazonAdProfile, spTargetDto, shopAuth, adGroup);
        if (spTarget.getCode() == 1) {
            return ResultUtil.error(spTarget.getTargetErrMsg());
        }
        CreateSpTargetResp resp = new CreateSpTargetResp();
        if (CollectionUtils.isNotEmpty(spTarget.getTargetListList())) {
            List<CreateSpTargetResp.TargetVo> targetVos = new ArrayList<>();
            List<CreateSpTargetResp.ErrMsg> errMsgs = new ArrayList<>();
            for (TargetInfoResp targetInfoResp: spTarget.getTargetListList()) {
                if(req.getKeywordTarget()){
                    CreateSpTargetResp.TargetVo createSpTargetResp = new CreateSpTargetResp.TargetVo();
                    KeywordsVo sourceKeywordsVo = req.getKeywords().get(targetInfoResp.getIndex());
                    createSpTargetResp.setMatchType(sourceKeywordsVo.getMatchType());
                    createSpTargetResp.setTargetText(sourceKeywordsVo.getKeywordText());
                    if (StringUtils.isNotBlank(sourceKeywordsVo.getTargetId())) {
                        createSpTargetResp.setTargetId(sourceKeywordsVo.getTargetId());
                        targetVos.add(createSpTargetResp);
                        continue;
                    }
                    if (targetInfoResp.getCode() != 0) {
                        CreateSpTargetResp.ErrMsg errMsg = new CreateSpTargetResp.ErrMsg();
                        errMsg.setField(sourceKeywordsVo.getKeywordText());
                        errMsg.setMsg(AmazonErrorUtils.getError(targetInfoResp.getErrMsg()));
                        errMsgs.add(errMsg);
                    }
                }else{
                    CreateSpTargetResp.TargetVo createSpTargetResp = new CreateSpTargetResp.TargetVo();
                    TargetingVo targetingVo = req.getTargetings().get(targetInfoResp.getIndex());
                    if (StringUtils.isNotBlank(targetingVo.getTargetId())) {
                        createSpTargetResp.setTargetId(targetingVo.getTargetId());
                        createSpTargetResp.setTargetText(targetingVo.getAsin() == null ? targetingVo.getCategory() : targetingVo.getAsin());
                        targetVos.add(createSpTargetResp);
                        continue;
                    }
                    if (targetInfoResp.getCode() != 0) {
                        CreateSpTargetResp.ErrMsg errMsg = new CreateSpTargetResp.ErrMsg();
                        errMsg.setField(targetingVo.getAsin() == null ? targetingVo.getCategory() : targetingVo.getAsin());
                        errMsg.setMsg(AmazonErrorUtils.getError(targetInfoResp.getErrMsg()));
                        errMsgs.add(errMsg);
                    }
                }
            }
            resp.setTargetList(targetVos);
            resp.setTargetErrMsg(errMsgs);
        }
        return ResultUtil.success(resp);
    }

    /**
     * 高价值词列表接口
     *
     * @param req
     * @return
     */
    @PostMapping("/group/info")
    @ResponseBody
    public Result<GetGroupInfoResp> getGroupInfoRespResult(@RequestBody @Valid GetGroupInfoReq req) {
        log.info("/sp/create/group/info req: {}", req);
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(req.getShopId(), req.getPuid());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        if (!Constants.SP.equalsIgnoreCase(req.getType())) {
            return ResultUtil.error("目前尚未支持" + req.getType() + "类型广告组信息查询");
        }
        AmazonAdGroup spInfo = amazonAdGroupDao.getByCampaignIdAndAdGroupId(req.getPuid(), req.getShopId(), req.getCampaignId(), req.getGroupId());
        if (spInfo == null) {
            return ResultUtil.error("未查询到该广告组！！！");
        }
        List<String> asinByGroup = amazonAdProductDao.getAsinByGroup(req.getPuid(), req.getShopId(), req.getCampaignId(), req.getGroupId());
        asinByGroup = asinByGroup.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        return ResultUtil.success(buildResp(spInfo, asinByGroup));
    }
    private GetGroupInfoResp buildResp(AmazonAdGroup spInfo, List<String> asinByGroup){
        GetGroupInfoResp resp = new GetGroupInfoResp();
        resp.setCampaignId(spInfo.getCampaignId());
        resp.setGroupId(spInfo.getAdGroupId());
        resp.setGroupName(spInfo.getName());
        resp.setGroupType(spInfo.getAdGroupType());
        resp.setState(spInfo.getState());
        resp.setBid(spInfo.getDefaultBid().toString());
        resp.setAsins(asinByGroup);
        return resp;
    }

}
