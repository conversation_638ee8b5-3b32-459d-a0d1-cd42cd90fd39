package com.meiyunji.sponsored.controller.amazon.theme;

import com.amazon.advertising.sb.entity.themeTargeting.ThemeUpdateResult;
import com.amazon.advertising.sb.entity.themeTargeting.UpdateThemeTargetingResponse;
import com.amazon.advertising.sb.mode.themeTargeting.SbTheme;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.right.holder.UserHolder;
import com.meiyunji.sellfox.right.holder.dto.UserDTO;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdKeywordDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdKeyword;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbKeywordApiService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdKeywordSb;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.modelObject.req.ModifySbThemeReq;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SB主题广告编辑controller
 * @Author: hejh
 * @Date: 2025/2/12 13:24
 */
@Slf4j
@RestController
@RequestMapping("/sb/theme")
public class SBThemeController {

    @Autowired
    private IAmazonSbAdKeywordDao keywordDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private CpcSbKeywordApiService cpcSbKeywordApiService;
    @Autowired
    private IDorisService dorisService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;

    /**
     * （单个）调整主题投放竞价和状态
     *
     * @param req
     * @return
     */
    @PostMapping("/modify")
    @ResponseBody
    public Result<Void> modify(@RequestBody @Valid ModifySbThemeReq req) {
        log.info("/sb/theme/modify req: {}", req);
        UserDTO user = UserHolder.get();
        Integer puid = user.getPuid().intValue();
        AmazonSbAdKeyword sbAdKeyword = keywordDao.getByPuidAndId(puid, req.getId());
        if (sbAdKeyword == null) {
            return ResultUtil.error("没有关键词信息");
        }
        String keywordId = sbAdKeyword.getKeywordId();
        if (StringUtils.isBlank(keywordId)) {
            return ResultUtil.error("平台keyword id 为空, 请同步该活动在操作");
        }
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(req.getShopId(), puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, req.getShopId());
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        AmazonSbAdKeyword oldKeyword = new AmazonSbAdKeyword();
        BeanUtils.copyProperties(sbAdKeyword, oldKeyword);
        if (StringUtils.isNotBlank(req.getState())) {
            sbAdKeyword.setState(req.getState());
        }
        if (req.getBid() != null) {
            sbAdKeyword.setBid(BigDecimal.valueOf(req.getBid()));
        }

        SbTheme sbTheme = new SbTheme();
        sbTheme.setThemeId(keywordId);
        sbTheme.setAdGroupId(sbAdKeyword.getAdGroupId());
        sbTheme.setCampaignId(sbAdKeyword.getCampaignId());
        sbTheme.setState(req.getState());
        sbTheme.setBid(req.getBid());

        UpdateThemeTargetingResponse response = cpcSbKeywordApiService.modifyTheme(shop, profile, Collections.singletonList(sbTheme));

        ThemeUpdateResult result = response.getResult();
        if (CollectionUtils.isEmpty(result.getSuccess())) {
            Result error = ResultUtil.error(AmazonErrorUtils.getError(StringUtils.isNotBlank(result.getError().get(0).getDetails()) ?
                result.getError().get(0).getDetails() : result.getError().get(0).getDescription()));
            logSbKeywordsUpdate(oldKeyword, sbAdKeyword, user.getLoginIp(), error);
            return error;
        }
        sbAdKeyword.setUpdateId(user.getId().intValue());
        keywordDao.updateByIdAndPuid(puid, sbAdKeyword);
        saveDorisForUpdate(Collections.singletonList(sbAdKeyword));
        logSbKeywordsUpdate(oldKeyword, sbAdKeyword, user.getLoginIp(), ResultUtil.success());

        return ResultUtil.success();
    }

    private void logSbKeywordsUpdate(AmazonSbAdKeyword oldKeyword, AmazonSbAdKeyword keyword, String ip, Result result) {
        try {
            //SB主题广告转换为中文返回
            if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(keyword.getKeywordText())) {
                keyword.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
            } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(keyword.getKeywordText())) {
                keyword.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
            }
            AdManageOperationLog operationLog = adManageOperationLogService.getSbKeywordLog(oldKeyword, keyword);
            operationLog.setIp(ip);
            if (result.success()) {
                operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                operationLog.setResultInfo(result.getMsg());
            }
            adManageOperationLogService.printAdOperationLog(Lists.newArrayList(operationLog));
        } catch (Exception e) {
            log.error("sb关键词日志异常", e);
        }
    }

    /**
     * 写入doris
     *
     * @param amazonSbAdKeywords
     */
    private void saveDorisForUpdate(List<AmazonSbAdKeyword> amazonSbAdKeywords) {
        try {
            List<OdsAmazonAdKeywordSb> collect = amazonSbAdKeywords.stream().map(x -> {
                OdsAmazonAdKeywordSb odsAmazonAdKeywordSb = new OdsAmazonAdKeywordSb();
                BeanUtils.copyProperties(x, odsAmazonAdKeywordSb);
                odsAmazonAdKeywordSb.setUpdateTime(new Date());
                if (StringUtils.isNotBlank(odsAmazonAdKeywordSb.getState())) {
                    odsAmazonAdKeywordSb.setState(odsAmazonAdKeywordSb.getState().toLowerCase());
                }
                return odsAmazonAdKeywordSb;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sb keyword save doris error", e);
        }
    }
}
