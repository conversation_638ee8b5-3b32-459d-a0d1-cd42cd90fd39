package com.meiyunji.sponsored.controller.amazon.ads;


import com.meiyunji.sellfox.right.annotation.CheckRight;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.security.SellfoxRightResource;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.adActivity.dto.AdCampaignProductQueryReq;
import com.meiyunji.sponsored.service.adActivity.service.AdCampaignService;
import com.meiyunji.sponsored.service.adActivity.vo.AdCampaignProductQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/sellfox/adCampaign")
public class AdCampaignController {

    @Autowired
    private AdCampaignService adCampaignService;


    /**
     * 获取广告活动下的产品列表
     *
     * @param req 查询参数
     * @return 产品信息列表
     */
    @PostMapping("/getGroupProductList")
    @CheckRight(value =  {SellfoxRightResource.MOD_AD_MANAGE__VIEW_LIST})
    public Result<List<AdCampaignProductQueryVo>> getGroupProductList(@RequestBody @Valid AdCampaignProductQueryReq req) {
        return ResultUtil.success(adCampaignService.getGroupProductList(req));
    }




}
