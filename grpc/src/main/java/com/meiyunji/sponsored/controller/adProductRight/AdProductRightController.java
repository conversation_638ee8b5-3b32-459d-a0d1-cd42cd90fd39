package com.meiyunji.sponsored.controller.adProductRight;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.fastjson.JSON;
import com.meiyunji.sellfox.right.utils.RightContextUtil;
import com.meiyunji.sellfox.right.utils.ShopRightUtils;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.adProductRight.request.*;
import com.meiyunji.sponsored.service.adProductRight.response.*;
import com.meiyunji.sponsored.service.adProductRight.service.impl.AdProductRightService;
import com.meiyunji.sponsored.service.post.response.GetUserInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.BufferedReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 产品权限 controller入口
 *
 * @Author: heqiwen
 * @Date: 2025/05/21 15:32
 */
@RestController
@RequestMapping("/sellfox/adProduct")
@Slf4j
public class AdProductRightController {

    @Autowired
    private IShopAuthService shopAuthService;

    @Resource
    private ShopRightUtils shopRightUtils;

    @Autowired
    private AdProductRightService adProductRightService;

    @Autowired
    private IScVcShopAuthDao scVcShopAuthDao;

    /**
     * 站点/店铺下拉筛选
     */
    @PostMapping("/shopSiteList")
    public Result<List<ShopSitesResponse>> shopSiteList(Boolean isAdd) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        log.info("广告产品权限 - 站点店铺下拉框接口 puid：{}", puid);
        List<Integer> shopIdList = scVcShopAuthDao.listAllValidOrExpireAdShopByIds(puid, shopRightUtils.checkScAndVcShopRight(uid, null));
        if (CollectionUtils.isEmpty(shopIdList)) {
            return ResultUtil.error(-1, "广告产品权限站点店铺下拉框接口无已授权店铺");
        }
        return ResultUtil.success(adProductRightService.getShopSiteList(puid, isAdd, shopIdList));
    }

    /**
     * 账号下拉筛选
     */
    @PostMapping("/userList")
    public Result<List<GetUserInfoResponse.UserInfo>> userList(@RequestBody @Valid UserListRequest request) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        log.info("广告产品权限 - 账号下拉框接口 puid：{}", puid);
        List<Integer> shopIdList = scVcShopAuthDao.listAllValidOrExpireAdShopByIds(puid, shopRightUtils.checkScAndVcShopRight(uid, request.getShopIdList()));
        if (CollectionUtils.isEmpty(shopIdList)) {
            return ResultUtil.error(-1, "广告产品权限账号下拉框接口无已授权店铺");
        }
        if (CollectionUtils.isEmpty(request.getShopIdList())) {
            request.setShopIdList(shopIdList);
        }
        return ResultUtil.success(adProductRightService.getUserList(puid, request));
    }

    /**
     * 添加页面下拉筛选
     */
    @PostMapping("/getUser")
    public Result<List<GetUserInfoResponse.UserInfo>> getUser() {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        log.info("广告产品权限 - 添加页面账号下拉框接口 puid：{}", puid);
        List<Integer> shopIdList = scVcShopAuthDao.listAllValidOrExpireAdShopByIds(puid, shopRightUtils.checkScAndVcShopRight(uid, null));
        if (CollectionUtils.isEmpty(shopIdList)) {
            return ResultUtil.error(-1, "广告产品权限账号下拉框接口无已授权店铺");
        }
        return ResultUtil.success(adProductRightService.getUser(puid));
    }

    /**
     * 广告产品权限列表页 - 列表页查询
     */
    @PostMapping("/pageList")
    public Result<GetRightListResponse> pageList(@RequestBody @Valid GetRightListRequest request) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        log.info("广告产品权限 - 列表页接口 puid:{}", puid);
        List<Integer> shopIdList = scVcShopAuthDao.listAllValidOrExpireAdShopByIds(puid, shopRightUtils.checkScAndVcShopRight(uid, request.getShopIdList()));
        if (CollectionUtils.isEmpty(shopIdList)) {
            return ResultUtil.error(-1, "广告产品权限列表页接口无已授权店铺");
        }
        if (CollectionUtils.isEmpty(request.getShopIdList())) {
            request.setShopIdList(shopIdList);
        }
        return ResultUtil.success(adProductRightService.pageList(puid, request));
    }

    /**
     * 广告产品权限列表页 - 商品详情查询
     */
    @PostMapping("/getAsinDetail")
    public Result<AsinDetailResponse> getAsinDetail(@RequestBody @Valid AsinDetailRequest request) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        log.info("广告产品权限 - 商品详情接口 puid:{}", puid);
        List<Integer> shopIdList = scVcShopAuthDao.listAllValidOrExpireAdShopByIds(puid, shopRightUtils.checkScAndVcShopRight(uid, new ArrayList<>(request.getShopId())));
        if (CollectionUtils.isEmpty(shopIdList)) {
            return ResultUtil.error(-1, "广告产品权限商品详情接口接口无已授权店铺");
        }
        AsinDetailResponse response = new AsinDetailResponse();
        response = adProductRightService.getAsinDetail(puid, request);
        return ResultUtil.success(response);
    }

    /**
     * 删除权限
     */
    @PostMapping("/deletePermission")
    public Result<String> deleteRight(@RequestBody @Valid DeleteRightRequest request) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        log.info("广告产品权限 - 删除账号权限接口 puid:{}", puid);
        List<Integer> shopIdList = scVcShopAuthDao.listAllValidOrExpireAdShopByIds(puid, shopRightUtils.checkScAndVcShopRight(uid, new ArrayList<>(request.getShopId())));
        if (CollectionUtils.isEmpty(shopIdList)) {
            return ResultUtil.error(-1, "广告产品权限列表页接口无已授权店铺");
        }
        return adProductRightService.deleteRight(puid, request.getUid(), request);
    }

    /**
     * 添加广告产品权限 - 提交接口
     * 用GZIP进行参数压缩
     */
    @PostMapping("addRight")
    public Result<String> addRight(HttpServletRequest request) {
        AddRightRequest param = null;
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        log.info("添加广告产品 - 提交接口 puid:{}", puid);
        List<Integer> shopIdList = scVcShopAuthDao.listAllValidOrExpireAdShopByIds(puid, shopRightUtils.checkScAndVcShopRight(uid, null));
        if (CollectionUtils.isEmpty(shopIdList)) {
            return ResultUtil.error(-1, "添加广告产品提交接口无已授权店铺");
        }
        try {
            // 接受请求参数
            BufferedReader reader = request.getReader();
            char[] buf = new char[1024];
            int len = 0;
            StringBuilder contentBuffer = new StringBuilder();
            while ((len = reader.read(buf)) != -1) {
                contentBuffer.append(buf, 0, len);
            }
            String compressData = contentBuffer.toString();
            if (StringUtils.isBlank(compressData)) {
                log.error("添加广告产品权限异常：请求体数据为空");
                return ResultUtil.error("请求参数错误");
            }

            // base64解码
            byte[] gzip = Base64.decode(compressData);
            // gzip解压
            String unGzip = ZipUtil.unGzip(gzip, CharsetUtil.UTF_8);
            // uri解码
            String params = URLUtil.decode(unGzip, CharsetUtil.UTF_8);

            if (StringUtils.isBlank(params) || Objects.isNull(param = JSON.parseObject(params, AddRightRequest.class))) {
                log.error("添加广告产品压缩接口异常：{}", params);
                return ResultUtil.error("请求参数错误");
            }
        } catch (Exception e) {
            log.error("添加广告产品权限异常：", e);
            return ResultUtil.error("请求参数错误");
        }
        return adProductRightService.addRight(puid, param);
    }

    /**
     * 添加/编辑广告产品权限 - 左侧产品详情
     */
    @PostMapping("/getAsinList")
    public Result<AsinListResponse> getAsinList(@RequestBody @Valid AsinListRequest request) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        log.info("添加广告产品 - 左侧产品详情接口 puid:{}", puid);
        List<Integer> shopIdList = scVcShopAuthDao.listAllValidOrExpireAdShopByIds(puid, shopRightUtils.checkScAndVcShopRight(uid, request.getShopIdList()));
        if (CollectionUtils.isEmpty(shopIdList)) {
            return ResultUtil.error(-1, "添加广告产品左侧产品详情接口无已授权店铺");
        }
        if (Objects.isNull(request.getPageNo()) || Objects.isNull(request.getPageSize())) {
            return ResultUtil.error(-1, "请求参数错误，缺少分页页码和页大小");
        }
        if (CollectionUtils.isEmpty(request.getShopIdList())) {
            request.setShopIdList(shopIdList);
        }
        AsinListResponse response = new AsinListResponse();
        response = adProductRightService.getAsinList(puid, request);
        return ResultUtil.success(response);
    }

    /**
     * 编辑广告产品权限 - 右侧产品详情
     */
    @PostMapping("/getAsinInfo")
    public Result<AsinListResponse> getAsinInfo(@RequestBody @Valid AsinInfoRequest request) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        log.info("编辑广告产品权限 - 右侧产品详情接口 puid:{}", puid);
        List<Integer> shopIdList = scVcShopAuthDao.listAllValidOrExpireAdShopByIds(puid, shopRightUtils.checkScAndVcShopRight(uid, new ArrayList<>(request.getShopId())));
        if (CollectionUtils.isEmpty(shopIdList)) {
            return ResultUtil.error(-1, "编辑广告产品权限右侧产品详情接口无已授权店铺");
        }
        AsinListResponse response = new AsinListResponse();
        response = adProductRightService.getAsinInfo(puid, request);
        return ResultUtil.success(response);
    }

    /**
     * 编辑广告产品权限 - 提交接口
     * 用GZIP进行参数压缩
     */
    @PostMapping("updateRight")
    public Result<String> updateRight(HttpServletRequest request) {
        AddRightRequest param = null;
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        log.info("编辑广告产品权限 - 提交接口 puid:{}", puid);
        List<Integer> shopIdList = scVcShopAuthDao.listAllValidOrExpireAdShopByIds(puid, shopRightUtils.checkScAndVcShopRight(uid, null));
        if (CollectionUtils.isEmpty(shopIdList)) {
            return ResultUtil.error(-1, "编辑广告产品权限提交接口无已授权店铺");
        }
        try {
            // 接受请求参数
            BufferedReader reader = request.getReader();
            char[] buf = new char[1024];
            int len = 0;
            StringBuilder contentBuffer = new StringBuilder();
            while ((len = reader.read(buf)) != -1) {
                contentBuffer.append(buf, 0, len);
            }
            String compressData = contentBuffer.toString();
            if (StringUtils.isBlank(compressData)) {
                log.error("编辑广告产品权限异常：请求体数据为空");
                return ResultUtil.error("请求参数错误");
            }

            // base64解码
            byte[] gzip = Base64.decode(compressData);
            // gzip解压
            String unGzip = ZipUtil.unGzip(gzip, CharsetUtil.UTF_8);
            // uri解码
            String params = URLUtil.decode(unGzip, CharsetUtil.UTF_8);

            if (StringUtils.isBlank(params) || Objects.isNull(param = JSON.parseObject(params, AddRightRequest.class))) {
                log.error("编辑广告产品权限压缩接口异常：{}", params);
                return ResultUtil.error("请求参数错误");
            }
        } catch (Exception e) {
            log.error("编辑广告产品权限异常：", e);
            return ResultUtil.error("请求参数错误");
        }
        return adProductRightService.updateRight(puid, param);
    }

}
