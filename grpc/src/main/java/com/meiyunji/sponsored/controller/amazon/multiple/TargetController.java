package com.meiyunji.sponsored.controller.amazon.multiple;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.meiyunji.sellfox.right.annotation.CheckRight;
import com.meiyunji.sellfox.right.utils.RightContextUtil;
import com.meiyunji.sellfox.right.utils.ShopRightUtils;
import com.meiyunji.sponsored.api.WordFrequency.WordFrequencyService;
import com.meiyunji.sponsored.api.common.AdCommonRpcService;
import com.meiyunji.sponsored.api.export.AdExportRpcService;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.base.SellfoxRightResource;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.multiple.common.resp.AdvanceCountResp;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetReqDto;
import com.meiyunji.sponsored.service.multiple.targets.enums.TargetTypeEnum;
import com.meiyunji.sponsored.service.multiple.targets.req.TargetReq;
import com.meiyunji.sponsored.service.multiple.targets.resp.TargetAggregateResp;
import com.meiyunji.sponsored.service.multiple.targets.resp.TargetPageResp;
import com.meiyunji.sponsored.service.multiple.targets.resp.TargetResp;
import com.meiyunji.sponsored.service.multiple.targets.service.AbstractTargetProcessor;
import com.meiyunji.sponsored.service.wordFrequency.vo.WordRootTopVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 投放层级多店铺 controller
 *
 * @Author: zzh
 * @Date: 2025/4/17 13:24
 */
@Slf4j
@RestController
@RequestMapping("/sellfox/multiple/target")
public class TargetController {

    @Resource
    private Map<String, AbstractTargetProcessor> abstractTargetProcessorMap;

    @Resource
    private ShopRightUtils shopRightUtils;

    /**
     * 多店铺关键词投放层级-查询词根频次接口，获取topN词根
     * 单店铺接口 {@link WordFrequencyService#getKeywordTopList}
     */
    @PostMapping("/getKeywordTopList")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE__VIEW_LIST})
    public Result<List<WordRootTopVo>> getKeywordTopList(@RequestBody @Valid TargetReq req) {
        // 前端请求参数 -》 内部传递参数
        TargetReqDto dto = reqToDto(req);
        if (!"keyword".equals(dto.getTargetType())) {
            throw new SponsoredBizException("非法的投放类型！");
        }
        AbstractTargetProcessor targetProcessor = abstractTargetProcessorMap.get(dto.getTargetTypeEnum().getBeanName());
        return ResultUtil.success(targetProcessor.getKeywordTopList(dto));
    }

    /**
     * 多店铺投放层级高级筛选模板统计个数接口
     * 单店铺此逻辑写在列表页接口，多店铺抽离出来 保证接口单一职责
     */
    @PostMapping("/getAllTargetCount")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE__VIEW_LIST})
    public Result<AdvanceCountResp> getAllTargetCount(@RequestBody @Valid TargetReq req) {
        // 前端请求参数 -》 内部传递参数
        TargetReqDto dto = reqToDto(req);
        AbstractTargetProcessor targetProcessor = abstractTargetProcessorMap.get(dto.getTargetTypeEnum().getBeanName());
        return ResultUtil.success(targetProcessor.getAllTargetCount(dto));
    }

    /**
     * 多店铺投放层级列表页接口
     * 单店铺关键词投放接口 {@link AdCommonRpcService#getAllKeyWordData}
     * 单店铺商品词投放接口 {@link AdCommonRpcService#getAllTargetData}
     */
    @PostMapping("/getAllTargetData")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE__VIEW_LIST})
    public Result<TargetPageResp> getAllTargetData(@RequestBody @Valid TargetReq req) {
        // 前端请求参数 -》 内部传递参数
        TargetReqDto dto = reqToDto(req);
        AbstractTargetProcessor targetProcessor = abstractTargetProcessorMap.get(dto.getTargetTypeEnum().getBeanName());
        Page<TargetResp> targetData = targetProcessor.getAllTargetData(dto, Boolean.FALSE);
        TargetPageResp resp = new TargetPageResp();
        resp.setPage(targetData);
        return ResultUtil.success(resp);
    }

    /**
     * 多店铺投放层级汇总接口
     * 单店铺关键词投放接口 {@link AdCommonRpcService#getAllKeyWordAggregateData}
     * 单店铺商品词投放接口 {@link AdCommonRpcService#getAllTargetAggregateData}
     */
    @PostMapping("/getAllTargetAggregateData")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE__VIEW_LIST})
    public Result<TargetAggregateResp> getAllTargetAggregateData(@RequestBody @Valid TargetReq req) {
        // 前端请求参数 -》 内部传递参数
        TargetReqDto dto = reqToDto(req);
        AbstractTargetProcessor targetProcessor = abstractTargetProcessorMap.get(dto.getTargetTypeEnum().getBeanName());
        return ResultUtil.success(targetProcessor.getAllTargetAggregateData(dto));
    }

    /**
     * 多店铺投放层级导出接口
     * 单店铺关键词投放接口 {@link AdExportRpcService#keywordData}
     * 单店铺商品词投放接口 {@link AdExportRpcService#targetingData}
     */
    @PostMapping("/exportAllTargetData")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE__VIEW_LIST})
    public Result<String> exportAllTargetData(@RequestBody @Valid TargetReq req) {
        // 前端请求参数 -》 内部传递参数
        TargetReqDto dto = reqToDto(req);
        AbstractTargetProcessor targetProcessor = abstractTargetProcessorMap.get(dto.getTargetTypeEnum().getBeanName());
        return ResultUtil.success("success", targetProcessor.exportAllTargetData(dto));
    }

    /**
     * 前端请求参数 -》 内部传递参数
     */
    private TargetReqDto reqToDto(TargetReq req) {
        TargetReqDto dto = BeanUtil.copyProperties(req, TargetReqDto.class);
        TargetTypeEnum targetTypeEnum = TargetTypeEnum.getEnumByType(req.getAdType(), req.getTargetType());
        dto.setTargetTypeEnum(targetTypeEnum);
        // 用户id
        dto.setPuid(RightContextUtil.getPuid().intValue());
        dto.setUid(RightContextUtil.getUser().getId().intValue());
        // 店铺权限校验
        dto.setShopIdList(shopRightUtils.checkScAndVcShopRight(dto.getUid(), req.getShopIdList()));
        log.info("投放层级多店铺列表请求参数：{}", JSONUtil.toJsonStr(dto));
        return dto;
    }
}
