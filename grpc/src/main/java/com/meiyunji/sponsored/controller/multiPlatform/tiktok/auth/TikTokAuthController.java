package com.meiyunji.sponsored.controller.multiPlatform.tiktok.auth;

import com.meiyunji.sellfox.right.annotation.CheckRight;
import com.meiyunji.sellfox.right.holder.dto.UserDTO;
import com.meiyunji.sellfox.right.utils.RightContextUtil;
import com.meiyunji.sellfox.right.utils.ShopRightUtils;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.security.SellfoxRightResource;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.multiPlatform.resp.AdvertiserResp;
import com.meiyunji.sponsored.service.multiPlatform.resp.AuthAdvertiserResp;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request.AdTikTokAuthReq;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request.GetAdTikTokAdvertiserListReq;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request.GetAdTikTokAuthAdvertiserListReq;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request.RevokeAdTikTokAuthReq;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.service.TikTokAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * TikTok广告授权接口（网关接口，url前缀需要都加上/api/gw/sellfox/sellfox-cpc/）
 */
@Slf4j
@RestController
@RequestMapping("/sellfox/tiktok/auth")
public class TikTokAuthController {

    @Autowired
    private TikTokAuthService tikTokAuthService;
    @Autowired
    private ShopRightUtils shopRightUtils;

    /**
     * 获取授权地址
     * @param shopId
     * @return
     */
    @PostMapping("/getAdTikTokAuthUrl")
    @CheckRight(value = {SellfoxRightResource.MOD_MULTI_PLATFORM_TIKTOK_SHOP_EDIT})
    public Result<String> getAdTikTokAuthUrl(Integer shopId) {
        if (shopId == null) {
            return ResultUtil.error("店铺ID不能为空，请刷新页面重试");
        }
        // 店铺权限校验
        UserDTO user = RightContextUtil.getUser();
        shopRightUtils.checkShopRight(user.getId().intValue(), Collections.singletonList(shopId));
        return tikTokAuthService.getAuthUrl(user.getPuid().intValue(), user.getId().intValue(), shopId);
    }

    /**
     * 授权成功后获取账号列表
     * @param req
     * @return
     */
    @PostMapping("/getAdTikTokAuthAdvertiserList")
    @CheckRight(value = {SellfoxRightResource.MOD_MULTI_PLATFORM_TIKTOK_SHOP_EDIT})
    public Result<AuthAdvertiserResp> getAdTikTokAuthAdvertiserList(@RequestBody GetAdTikTokAuthAdvertiserListReq req) {
        UserDTO user = RightContextUtil.getUser();
        return tikTokAuthService.getAdTikTokAuthAdvertiserList(user.getPuid().intValue(), user.getId().intValue(), req);
    }


    /**
     * 选择账户后确定授权接口
     * @param req
     * @return
     */
    @PostMapping("/adTikTokAuth")
    @CheckRight(value = {SellfoxRightResource.MOD_MULTI_PLATFORM_TIKTOK_SHOP_EDIT})
    public Result<String> adTikTokAuth(@RequestBody AdTikTokAuthReq req) {
        UserDTO user = RightContextUtil.getUser();
        return tikTokAuthService.adTikTokAuth(user.getPuid().intValue(), user.getId().intValue(), req);
    }


    /**
     * 广告账号管理列表接口
     * @param req
     * @return
     */
    @PostMapping("/getAdTikTokAdvertiserList")
    @CheckRight(value = {SellfoxRightResource.MOD_MULTI_PLATFORM_TIKTOK_SHOP_EDIT})
    public Result<List<AdvertiserResp>> getAdTikTokAdvertiserList(@RequestBody GetAdTikTokAdvertiserListReq req) {
        if (req.getShopId() == null) {
            return ResultUtil.error("店铺ID不能为空");
        }
        return tikTokAuthService.getAdTikTokAdvertiserList(RightContextUtil.getUser().getPuid().intValue(), req);
    }


    /**
     * 解除账号授权接口
     * @param req
     * @return
     */
    @PostMapping("/revokeAdTikTokAuth")
    @CheckRight(value = {SellfoxRightResource.MOD_MULTI_PLATFORM_TIKTOK_SHOP_EDIT})
    public Result<String> revokeAdTikTokAuth(@RequestBody RevokeAdTikTokAuthReq req) {
        if (req.getShopId() == null || req.getAdvertiserId() == null) {
            return ResultUtil.error("店铺ID和广告账号ID不能为空");
        }
        return tikTokAuthService.revokeAdTikTokAuthReq(RightContextUtil.getUser().getPuid().intValue(), req);
    }

}
