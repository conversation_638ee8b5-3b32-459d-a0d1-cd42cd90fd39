package com.meiyunji.sponsored.controller.adProductRight;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.controller.BaseController;
import com.meiyunji.sponsored.service.cpc.vo.SPPageParam;
import com.meiyunji.sponsored.service.cpc.vo.SPPageVo;
import com.meiyunji.sponsored.service.product.service.IProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/product")
@RestController
public class CpcProductController extends BaseController {

    @Autowired
    private IProductService productService;


    /**
     * 可推广的产品列表页 vc店铺不适用
     * @param param:
     */
    @PostMapping(value = "/productRightSpPageList")
    @ResponseBody
    public Result<Page<SPPageVo>> spPageList(@RequestBody SPPageParam param) {
        int puid = getPuid();
        Page<SPPageVo> spPageVoPage = productService.getScAdvertisablePageList(puid, param);
        return ResultUtil.returnSucc(spPageVoPage);
    }
}
