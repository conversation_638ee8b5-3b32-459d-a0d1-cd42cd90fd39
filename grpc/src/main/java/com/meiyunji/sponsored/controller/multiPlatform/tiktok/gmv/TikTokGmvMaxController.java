package com.meiyunji.sponsored.controller.multiPlatform.tiktok.gmv;

import com.meiyunji.sellfox.core.exception.BizServiceException;
import com.meiyunji.sellfox.right.annotation.CheckRight;
import com.meiyunji.sellfox.right.holder.UserHolder;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.security.SellfoxRightResource;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.dao.TikTokGmvMaxCampaignDao;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.GmvMaxShop;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.GmvMaxVideoItem;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokGmvMaxCampaign;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request.*;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.response.GmvMaxIdentityGetResponse;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.response.GmvMaxVideoResponse;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.service.GmvMaxService;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.service.TikTokCommonService;
import com.tiktok.advertising.exception.TikTokSDKException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/sellfox/tiktok/gmv_max")
public class TikTokGmvMaxController {


    @Autowired
    private GmvMaxService gmvMaxService;
    @Autowired
    private TikTokGmvMaxCampaignDao tiktokGmvMaxCampaignDao;
    @Autowired
    private TikTokCommonService tkCommonService;

    private Integer getPuid() {
        return UserHolder.get().getPuid().intValue();
    }

    private Integer getUid() {
        return UserHolder.get().getId().intValue();
    }

    /**
     * 可以使用GMV MAX广告的店铺
     */
    @PostMapping("/shop/list")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__VIEW_LIST})
    public Result<?> shopList(@RequestBody TikTokCommonRequest param) {
        List<GmvMaxShop> gmvMaxShops;
        try {
            gmvMaxShops = gmvMaxService.gmvStoreList(getPuid(), param.getAdvertiserId());
        } catch (BizServiceException e) {
            return ResultUtil.success(Collections.singletonMap("msg", e.getMessage()));
        } catch (TikTokSDKException e) {
            return ResultUtil.success(Collections.singletonMap("msg", e.getErrorMessage()));
        }
        return ResultUtil.success(Collections.singletonMap("shopList", gmvMaxShops));
    }

    @PostMapping("/shop_ad_usage_check")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__VIEW_LIST})
    public Result<?> shopAdUsageCheck(@RequestBody TikTokCommonRequest param) {
        String advertiserId = param.getAdvertiserId();
        Integer shopId = param.getShopId();
        checkShopRight(shopId);

        return ResultUtil.success(Collections.singletonMap("promoteAllProductsAllowed", gmvMaxService.shopAdUsageCheck(getPuid(), advertiserId, shopId)));
    }

    @PostMapping("/shop/product/list")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__VIEW_LIST})
    public Result<?> shopProductList(@RequestBody @Valid ShopProductParam param) {
        log.info("shopProductList param :{}", param);

        checkShopRight(param.getShopId());

        if (Objects.isNull(param.getPageNo()) || param.getPageNo() < 1) {
            param.setPageNo(1);
        }
        if (Objects.isNull(param.getPageSize()) || param.getPageSize() < 1) {
            param.setPageSize(100);
        }
        return ResultUtil.success(gmvMaxService.shopProductList(getPuid(), param));
    }

    @PostMapping("/identity/get")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__VIEW_LIST})
    public Result<GmvMaxIdentityGetResponse> identityGet(@RequestBody IdentityGetParam param) {
        String advertiserId = param.getAdvertiserId();
        Integer shopId = param.getShopId();
        if (Objects.isNull(param.getPageNo()) || param.getPageNo() < 1) {
            param.setPageNo(1);
        }
        if (Objects.isNull(param.getPageSize()) || param.getPageSize() < 1) {
            param.setPageSize(10);
        }
        checkShopRight(shopId);
        return ResultUtil.success(gmvMaxService.identityGet(getPuid(), advertiserId, shopId, param.getStoreAuthorizedBcId(),  param.getPageNo(), param.getPageSize()));
    }

    @PostMapping("/video/get")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__VIEW_LIST})
    public Result<?> videoGet(@RequestBody @Valid GmvMaxVideoGetParam param) {
        checkShopRight(param.getShopId());

        if (Objects.isNull(param.getPageNo()) || param.getPageNo() < 1) {
            param.setPageNo(1);
        }
        if (Objects.isNull(param.getPageSize()) || param.getPageSize() < 1) {
            param.setPageSize(50);
        }

        if (Boolean.TRUE.equals(param.getCustomPostsEligible())) {
            if (CollectionUtils.size(param.getSpuIdList()) != 1) {
                throw new BizServiceException("查询自定义作品时，仅支持选择一个spu");
            }
        }

        //mock数据
//        if (Boolean.FALSE.equals(param.getCustomPostsEligible())) {
//            if (CollectionUtils.isEmpty(param.getSpuIdList())) {
//                param.setSpuIdList(new ArrayList<>());
//                param.getSpuIdList().add("1729602825716337399");
//                param.getSpuIdList().add("1729602825597979383");
//            }
//            GmvMaxVideoItem gmvMaxVideoItem1 = JSONUtil.jsonToObject("{\"id\":null,\"itemId\":\"7496165367568469290\",\"requestSpuIdList\":[],\"text\":\"ONDAS SIN CALOR, tendrás RISOS sin maltratar tu cabello  . . . . . #cuidadodelcabello #haircare #ondas #parati #viral #cabello #hairstyle #belleza\",\"spuIdList\":null,\"identityInfo\":{\"identityId\":\"6961268031273403397\",\"identityType\":\"授权用户\",\"displayName\":\"Davelytabar\",\"userName\":\"-\",\"productGmvMaxAvailable\":false,\"profileImage\":\"https://p16-sign-va.tiktokcdn.com/tos-maliva-avt-0068/357e4960076b5f8a874b6481998b6665~tplv-tiktokx-cropcenter:100:100.webp?dr=14579&refresh_token=f134e586&x-expires=1749193200&x-signature=f3KEQ8zu%2FyVdb8rUIPk0x8bwN%2Bk%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=135aa7c6&idc=my\"},\"videoInfo\":{\"videoId\":\"v12044gd0000d03rj0vog65m942d2hv0\",\"videoCoverUrl\":\"https://p16-sign-va.tiktokcdn.com/tos-maliva-p-0068c799-us/oMmksAfgSBQO8dRgEDgmQiFEeUmj3oPcDYD7BG~tplv-noop.image?x-expires=1749109409&x-signature=XV12ZrHCiLcQ0Bd%2FcL0ZIonOmpo%3D\",\"previewUrl\":\"https://v19-tt4b.tiktokcdn.com/742bcb11526a85b83272bdb42fbbfbee/68414aa1/video/tos/maliva/tos-maliva-ve-0068c799-us/ocxtPDQiAHAQHeD8C7WkeegKxVMaVIASMC3jBy/?a=1233&bti=cGRicXNydmV3MWRmb2RkZm1lKzFxYTo%3D&ch=0&cr=0&dr=0&lr=tiktok_m&cd=0%7C0%7C1%7C0&cv=1&br=2986&bt=1493&cs=0&ds=3&ft=.bvrXInz7Thg_UJKXq8Zmo&mime_type=video_mp4&qs=0&rc=MzQ2NDc1OjxlOjNmZDs6NkBpM2tnN3A5cjN1MzMzZzczNEBfYmMzNTQ0NjExXjUzNmMzYSNzLy82MmRzZzBhLS1kMS9zcw%3D%3D&vvpl=1&l=202506040742117F1D158EC7B4542356E4&btag=e00090000\",\"height\":3840,\"width\":2160,\"duration\":77,\"size\":288111588,\"format\":\"mp4\"}}", GmvMaxVideoItem.class);
//            GmvMaxVideoItem gmvMaxVideoItem2 = JSONUtil.jsonToObject("{\"id\":null,\"itemId\":\"7197420273019243803\",\"requestSpuIdList\":[],\"text\":\"Antigores Iphone Paling Top, Mudah Ditempel, Murah Meriah! #pelindunglayarhp #antigores #iphone #mistergadged \",\"spuIdList\":[],\"identityInfo\":{\"identityId\":\"7192222001603494938\",\"identityType\":\"TikTok Shop 关联的 TikTok 用户\",\"displayName\":\"Mister GadgeD\",\"userName\":\"-\",\"productGmvMaxAvailable\":false,\"storeId\":\"7494910468218456823\",\"profileImage\":\"https://p16-sign-useast2a.tiktokcdn.com/tos-useast2a-avt-0068-giso/a54cbd9022857e33ec88ea255f8d8327~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=d953b1a8&x-expires=1749193200&x-signature=MhsD0gBoyYU0%2FuM5ChFZK9tR2fU%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=maliva\"},\"videoInfo\":{\"videoId\":\"v0f025gc0000cfh5tobc77uc44dof270\",\"videoCoverUrl\":\"https://p16-vod-sign-useast2a.tiktokcdn-eu.com/tos-useast2a-p-0037-aiso/dbc5ba4862b347de810f692beb855c0a_1675780003~tplv-noop.image?x-expires=1749109357&x-signature=GpCCUz%2Fbbn9sZZKTqqukt2YqqOw%3D\",\"previewUrl\":\"https://v16-tt4b.tiktokcdn.com/17d27dad662d8f078cadcba223a39e49/68414a6d/video/tos/useast2a/tos-useast2a-pve-0037c001-aiso/oQc7ogPnjBvYETPeDDmlhAQQCDcib4WQjVo6fh/?a=1233&bti=cGRicXNydmV3MWRmb2RkZm1lKzFxYTo%3D&ch=0&cr=0&dr=0&lr=tiktok_m&cd=0%7C0%7C1%7C0&cv=1&br=1210&bt=605&cs=0&ds=3&ft=.bvrXInz7Th__UJKXq8Zmo&mime_type=video_mp4&qs=0&rc=NGU4OTtnNjg2aDw2OWRoM0BpMzVyN2Y6ZnI4aTMzZjgzM0BeNF5gNC40Ni4xX2AtYjMxYSM0Y2ExcjRfcWVgLS1kL2Nzcw%3D%3D&vvpl=1&l=202506040742117F1D158EC7B4542356E4&btag=e000b8000\",\"height\":870,\"width\":576,\"duration\":24,\"size\":1614117,\"format\":\"mp4\"}}", GmvMaxVideoItem.class);
//            GmvMaxVideoItem gmvMaxVideoItem3 = JSONUtil.jsonToObject("{\"id\":null,\"itemId\":\"7197064558555434267\",\"requestSpuIdList\":[],\"text\":\"Antigores Iphone Anti Statis Anti Air, Buruan Dicoba! #mistergadged #iphone #antigores #pelindunglayarhp \",\"spuIdList\":null,\"identityInfo\":{\"identityId\":\"7192222001603494938\",\"identityType\":\"TikTok Shop 关联的 TikTok 用户\",\"displayName\":\"Mister GadgeD\",\"userName\":\"-\",\"productGmvMaxAvailable\":false,\"storeId\":\"7494910468218456823\",\"profileImage\":\"https://p16-sign-useast2a.tiktokcdn.com/tos-useast2a-avt-0068-giso/a54cbd9022857e33ec88ea255f8d8327~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=d953b1a8&x-expires=1749193200&x-signature=MhsD0gBoyYU0%2FuM5ChFZK9tR2fU%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=maliva\"},\"videoInfo\":{\"videoId\":\"v0f025gc0000cfghd0rc77u1ldg9far0\",\"videoCoverUrl\":\"https://p16-vod-sign-useast2a.tiktokcdn-eu.com/tos-useast2a-p-0037-aiso/0d1bbaa4e6bf424bbb83586841336c50_1675697180~tplv-noop.image?x-expires=1749109358&x-signature=22M0yJq%2FsioTpSdEFKDqFVZGdyg%3D\",\"previewUrl\":\"https://v16-tt4b.tiktokcdn.com/6a4207f66d4aec543b5756a22e7165aa/68414a6e/video/tos/useast2a/tos-useast2a-pve-0037-aiso/oM78nIBUzCCtIAdqohtEAPQ5Xfq2DxhBiB2Kwh/?a=1233&bti=cGRicXNydmV3MWRmb2RkZm1lKzFxYTo%3D&ch=0&cr=0&dr=0&lr=tiktok_m&cd=0%7C0%7C1%7C0&cv=1&br=2494&bt=1247&cs=0&ds=3&ft=.bvrXInz7Th__UJKXq8Zmo&mime_type=video_mp4&qs=0&rc=O2c5NTQ5aTY3MzY2OWZnOkBpM2Q8ZzQ6ZjNraTMzZjgzM0BiXmEtNjBjXzYxYDAxMDVfYSNvY2RpcjRvYWRgLS1kL2Nzcw%3D%3D&vvpl=1&l=202506040742117F1D158EC7B4542356E4&btag=e000b8000\",\"height\":1024,\"width\":576,\"duration\":25,\"size\":3610363,\"format\":\"mp4\"}}", GmvMaxVideoItem.class);
//            GmvMaxVideoItem gmvMaxVideoItem4 = JSONUtil.jsonToObject("{\"id\":null,\"itemId\":\"74547160817903895351\",\"requestSpuIdList\":[],\"text\":\"#wearableblanket #hoodedblanked #coldweather #hoodieseason \",\"spuIdList\":null,\"identityInfo\":{\"identityId\":\"6716338603133944838\",\"identityType\":\"授权用户\",\"displayName\":\"Melissa_Mariche\",\"userName\":\"-\",\"productGmvMaxAvailable\":false,\"profileImage\":\"https://p16-sign-va.tiktokcdn.com/tos-maliva-avt-0068/d2316d2ba39507174baeaf3ce06c855f~tplv-tiktokx-cropcenter:100:100.webp?dr=14579&refresh_token=ba821582&x-expires=1749196800&x-signature=kNa2Yx1LlytYEfNRtu6ofW0U%2FTM%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=135aa7c6&idc=my\"},\"videoInfo\":{\"videoId\":\"v15044gf0000ctq7gvnog65ser17k0mg\",\"videoCoverUrl\":\"https://p16-sign-va.tiktokcdn.com/tos-maliva-p-0068c799-us/ogrIneoNQ2xIqp6CCoIGeI0jD9ALGfPA3MrOgw~tplv-noop.image?x-expires=1749112484&x-signature=11yiBNqG4Qpt3zWAW0fO5EyJvQA%3D\",\"previewUrl\":\"https://v16-tt4b.tiktokcdn.com/62d6f3ad67e4cb542f92e31bbe05269a/684156a4/video/tos/maliva/tos-maliva-ve-0068c799-us/oUjCDFIFgYefz467cOeLAaqolyQIIF0TDP8BEA/?a=1233&bti=cGRicXNydmV3MWRmb2RkZm1lKzFxYTo%3D&ch=0&cr=0&dr=0&lr=tiktok_m&cd=0%7C0%7C1%7C0&cv=1&br=2130&bt=1065&cs=0&ds=3&ft=.bvrXInz7ThH3mgKXq8Zmo&mime_type=video_mp4&qs=0&rc=NDlkM2doOjYzPGUzPDw7PEBpajM6dXY5cnk6dzMzaTczNEAzYTAyNjMwX2ExX2MwYGEwYSNqaC5iMmRrZG5gLS1kMTJzcw%3D%3D&vvpl=1&l=202506040834205A7EA92C6E4FF8272492&btag=e000b8000\",\"height\":1920,\"width\":1080,\"duration\":22,\"size\":19291347,\"format\":\"mp4\"}}", GmvMaxVideoItem.class);
//            GmvMaxVideoItem gmvMaxVideoItem5 = JSONUtil.jsonToObject("{\"id\":null,\"itemId\":\"74532414793230779311\",\"requestSpuIdList\":[],\"text\":\"shoutouts to my beautiful model @Jazlyn \\uD83D\\uDC9B go follow her \\uD83E\\uDD70\",\"spuIdList\":null,\"identityInfo\":{\"identityId\":\"6640253540533665798\",\"identityType\":\"授权用户\",\"displayName\":\"Nayimis\",\"userName\":\"-\",\"productGmvMaxAvailable\":false,\"profileImage\":\"https://p16-sign-va.tiktokcdn.com/tos-maliva-avt-0068/8c6203ca04b499bbdb92793c958caad3~tplv-tiktokx-cropcenter:100:100.webp?dr=14579&refresh_token=ff76bd82&x-expires=1749196800&x-signature=ivOE0VIh2PCirLuTTeF9Mz8ImPA%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=135aa7c6&idc=my\"},\"videoInfo\":{\"videoId\":\"v12044gd0000ctnjkp7og65j715blahg\",\"videoCoverUrl\":\"https://p16-sign-va.tiktokcdn.com/tos-maliva-p-0068c799-us/oAfnhG35gAkJ4qVIMH7ItqTUegGjABLrO8CSMe~tplv-noop.image?x-expires=1749112531&x-signature=wh9HTRvZ1TJlvoCjNKv5TaGyR20%3D\",\"previewUrl\":\"https://v16-tt4b.tiktokcdn.com/f33825d9c0dca7eedca96f1e8b46e352/684156d3/video/tos/maliva/tos-maliva-ve-0068c799-us/o0AqSQ86EZEB4BiFuPUZ6IpEj2vCRiizbgqlO/?a=1233&bti=cGRicXNydmV3MWRmb2RkZm1lKzFxYTo%3D&ch=0&cr=0&dr=0&lr=tiktok_m&cd=0%7C0%7C1%7C0&cv=1&br=4376&bt=2188&cs=0&ds=3&ft=.bvrXInz7ThH3mgKXq8Zmo&mime_type=video_mp4&qs=0&rc=ZTU0ZGU3aGg3Nzo4Zjs3OkBpamRlNG05cnNtdzMzZzczNEBeNTZeLzVhNjMxMGMxM2IyYSNlaTI0MmQ0aGtgLS1kMS9zcw%3D%3D&vvpl=1&l=202506040834205A7EA92C6E4FF8272492&btag=e00090000\",\"height\":1920,\"width\":1080,\"duration\":69,\"size\":1735024576,\"format\":\"mp4\"}}", GmvMaxVideoItem.class);
//            GmvMaxVideoItem gmvMaxVideoItem22 = JSONUtil.jsonToObject("{\"id\":null,\"itemId\":\"74525154593976353701\",\"requestSpuIdList\":[],\"text\":\"THIS IS A GREAT GIFT IDEA\\uD83D\\uDD25\\uD83E\\uDD1D #tiktokshop #fyp #trending #foryou #hoodieblanket #hoodie #soft #softblanket #blankethoodie #softhoodie \",\"spuIdList\":[],\"identityInfo\":{\"identityId\":\"7150405496604083243\",\"identityType\":\"授权用户\",\"displayName\":\"Unforgettable clips\",\"userName\":\"-\",\"productGmvMaxAvailable\":false,\"profileImage\":\"https://p16-sign-va.tiktokcdn.com/tos-maliva-avt-0068/2ff8e3a5e0cc45843ac3507ab722b720~tplv-tiktokx-cropcenter:100:100.webp?dr=14579&refresh_token=68c393a0&x-expires=1749196800&x-signature=yUxMXHx9IS%2BvrZGkilZqzdd39YM%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=135aa7c6&idc=my\"},\"videoInfo\":{\"videoId\":\"v12044gd0000ctmae5vog65q5bvf6ci0\",\"videoCoverUrl\":\"https://p16-sign-va.tiktokcdn.com/tos-maliva-p-0068c799-us/o4L4qLfO2AUmHZvFIofUzjTCkvZeQX3oIqIAIG~tplv-noop.image?x-expires=1749112489&x-signature=vcabJM86MZ9o8knDyODQhpZ0Ht8%3D\",\"previewUrl\":\"https://v16-tt4b.tiktokcdn.com/f7dc7b29670a5a5b77a07ed937628b7a/684156a9/video/tos/maliva/tos-maliva-ve-0068c799-us/oA5IC2mkiiAKNtQwEIvBpL7zlMpEO6zssBvfA6/?a=1233&bti=cGRicXNydmV3MWRmb2RkZm1lKzFxYTo%3D&ch=0&cr=0&dr=0&lr=tiktok_m&cd=0%7C0%7C1%7C0&cv=1&br=2932&bt=1466&cs=0&ds=3&ft=.bvrXInz7ThH3mgKXq8Zmo&mime_type=video_mp4&qs=0&rc=OmU6Z2U6aTUzZzk1aWU7aUBpM2ZpZXQ5cjhkdzMzZzczNEAuNi81MGBiX2MxLWEwXjVhYSNmM3MyMmRzYmpgLS1kMS9zcw%3D%3D&vvpl=1&l=202506040834205A7EA92C6E4FF8272492&btag=e000b8000\",\"height\":3840,\"width\":2160,\"duration\":27,\"size\":150496779,\"format\":\"mp4\"}}", GmvMaxVideoItem.class);
//
//            // 1729602825716337399 gmvMaxVideoItem1 gmvMaxVideoItem4 gmvMaxVideoItem2 gmvMaxVideoItem22
//            // 1729602825597979383 gmvMaxVideoItem3 gmvMaxVideoItem5 gmvMaxVideoItem2 gmvMaxVideoItem22
//            List<GmvMaxVideoItem> list = new ArrayList<>();
//            if (param.getSpuIdList().contains("1729602825716337399")) {
//                gmvMaxVideoItem1.setSpuIdList(Collections.singletonList("1729602825716337399"));
//                gmvMaxVideoItem1.setId(gmvMaxVideoItem1.buildGmvMaxVideoItemIdentifyId());
//                gmvMaxVideoItem1.setRequestSpuIdList(param.getSpuIdList());
//                list.add(gmvMaxVideoItem1);
//                gmvMaxVideoItem4.setSpuIdList(Collections.singletonList("1729602825716337399"));
//                gmvMaxVideoItem4.setId(gmvMaxVideoItem4.buildGmvMaxVideoItemIdentifyId());
//                gmvMaxVideoItem4.setRequestSpuIdList(param.getSpuIdList());
//                list.add(gmvMaxVideoItem4);
//                gmvMaxVideoItem2.getSpuIdList().add("1729602825716337399");
//                gmvMaxVideoItem22.getSpuIdList().add("1729602825716337399");
//            }
//            if (param.getSpuIdList().contains("1729602825597979383")) {
//                gmvMaxVideoItem3.setSpuIdList(Collections.singletonList("1729602825597979383"));
//                gmvMaxVideoItem3.setId(gmvMaxVideoItem3.buildGmvMaxVideoItemIdentifyId());
//                gmvMaxVideoItem3.setRequestSpuIdList(param.getSpuIdList());
//                list.add(gmvMaxVideoItem3);
//                gmvMaxVideoItem5.setSpuIdList(Collections.singletonList("1729602825716337399"));
//                gmvMaxVideoItem5.setId(gmvMaxVideoItem5.buildGmvMaxVideoItemIdentifyId());
//                gmvMaxVideoItem5.setRequestSpuIdList(param.getSpuIdList());
//                list.add(gmvMaxVideoItem5);
//                gmvMaxVideoItem2.getSpuIdList().add("1729602825597979383");
//                gmvMaxVideoItem22.getSpuIdList().add("1729602825597979383");
//            }
//            if (param.getSpuIdList().contains("1729602825716337399") || param.getSpuIdList().contains("1729602825597979383")) {
//                gmvMaxVideoItem2.setId(gmvMaxVideoItem2.buildGmvMaxVideoItemIdentifyId());
//                gmvMaxVideoItem2.setRequestSpuIdList(param.getSpuIdList());
//                list.add(gmvMaxVideoItem2);
//                gmvMaxVideoItem22.setId(gmvMaxVideoItem22.buildGmvMaxVideoItemIdentifyId());
//                gmvMaxVideoItem22.setRequestSpuIdList(param.getSpuIdList());
//                list.add(gmvMaxVideoItem22);
//            }
//            GmvMaxVideoResponse resp = new GmvMaxVideoResponse();
//            resp.setPageNum(param.getPageNo());
//            resp.setPageSize(param.getPageSize());
//            resp.setItemList(list);
//            resp.setTotalNum(list.size());
//            resp.setTotalPage(1);
//            return ResultUtil.success(resp);
//        }
        return ResultUtil.success(gmvMaxService.gmvMaxVideoGet(getPuid(), param));
    }

    @PostMapping("/create")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__ADD_DATA})
    public Result<?> create(@RequestBody @Valid GmvMaxCampaignCreateParam param) {
        checkShopRight(param.getShopId());
        if (StringUtils.equalsIgnoreCase(param.getProductSpecificType(), "CUSTOMIZED_PRODUCTS") && CollectionUtils.isEmpty(param.getSpuIdList())) {
            throw new BizServiceException("请选择需要推广的商品");
        }
        // todo tiktok 商品类型的需要校验 本次只做商品类型，所以需要校验
        if (!StringUtils.equalsAnyIgnoreCase(param.getProductVideoSpecificType(), "AUTO_SELECTION", "CUSTOM_SELECTION")) {
            throw new BizServiceException("创意模式参数不正确");
        }
        return ResultUtil.success(Collections.singletonMap("campaignId", gmvMaxService.create(getPuid(), param)));
    }

    @PostMapping("/campaign/get")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__VIEW_LIST})
    public Result<?> campaignGet(@RequestBody TikTokCommonRequest param) {
        String advertiserId = param.getAdvertiserId();
        Integer shopId = param.getShopId();
        String campaignId = param.getCampaignId();
        checkShopRight(shopId);
        return ResultUtil.success(gmvMaxService.campaignInfoGet(getPuid(), advertiserId, shopId, campaignId));
    }

    @PostMapping("/campaign/status/update")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__ADD_DATA})
    public Result<?> campaignStatusUpdate(@RequestBody @Valid GmvMaxCampaignStatusUpdateParam param) {
        checkShopRight(param.getShopId());
        Integer puid = getPuid();
        return ResultUtil.success(Collections.singletonMap("campaignId", gmvMaxService.campaignStatusUpdate(puid, param)));
    }

    @PostMapping("/update")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__ADD_DATA})
    public Result<?> update(@RequestBody @Valid GmvMaxCampaignUpdateParam param) {
        checkShopRight(param.getShopId());
        Integer puid = getPuid();
        TikTokGmvMaxCampaign campaign = tiktokGmvMaxCampaignDao.getByCampaignId(puid, param.getShopId(), param.getAdvertiserId(), param.getCampaignId());
        if (Objects.isNull(campaign)) {
            throw new BizServiceException("未查询到该广告");
        }
        return ResultUtil.success(Collections.singletonMap("campaignId", gmvMaxService.update(puid, param)));
    }

    @PostMapping("/list")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__VIEW_LIST})
    public Result<?> list(@RequestBody @Valid GmvMaxCampaignListParam param) {
        Integer puid = getPuid();
        if (Objects.isNull(param.getPageNo()) || param.getPageNo() < 1) {
            param.setPageNo(1);
        }
        if (Objects.isNull(param.getPageSize()) || param.getPageSize() < 1) {
            param.setPageSize(10);
        }
        return ResultUtil.success(gmvMaxService.list(puid, checkShopRight(param.getShopId()), param));
    }

    private List<Integer> checkShopRight(Integer shopId) {
        List<Integer> shopIds = (shopId == null ? null : Collections.singletonList(shopId));
        shopIds = tkCommonService.checkShopRight(getUid(), shopIds);
        shopIds = tkCommonService.checkAdShopAuthList(getPuid(), shopIds);
        return shopIds;
    }

}
