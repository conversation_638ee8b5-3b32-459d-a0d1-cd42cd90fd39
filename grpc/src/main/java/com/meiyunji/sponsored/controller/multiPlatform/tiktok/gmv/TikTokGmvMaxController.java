package com.meiyunji.sponsored.controller.multiPlatform.tiktok.gmv;

import com.meiyunji.sellfox.core.exception.BizServiceException;
import com.meiyunji.sellfox.right.annotation.CheckRight;
import com.meiyunji.sellfox.right.holder.UserHolder;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.security.SellfoxRightResource;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.dao.TikTokGmvMaxCampaignDao;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.GmvMaxShop;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokGmvMaxCampaign;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request.*;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.response.GmvMaxIdentityGetResponse;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.response.GmvMaxVideoResponse;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.service.GmvMaxService;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.service.TikTokCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/sellfox/tiktok/gmv_max")
public class TikTokGmvMaxController {


    @Autowired
    private GmvMaxService gmvMaxService;
    @Autowired
    private TikTokGmvMaxCampaignDao tiktokGmvMaxCampaignDao;
    @Autowired
    private TikTokCommonService tkCommonService;

    private Integer getPuid() {
        return UserHolder.get().getPuid().intValue();
    }

    private Integer getUid() {
        return UserHolder.get().getId().intValue();
    }

    /**
     * 可以使用GMV MAX广告的店铺
     */
    @PostMapping("/shop/list")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__VIEW_LIST})
    public Result<?> shopList(@RequestBody TikTokCommonRequest param) {
        List<GmvMaxShop> gmvMaxShops = gmvMaxService.gmvStoreList(getPuid(), param.getAdvertiserId());
        return ResultUtil.success(Collections.singletonMap("shopList", gmvMaxShops));
    }

    @PostMapping("/shop_ad_usage_check")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__VIEW_LIST})
    public Result<?> shopAdUsageCheck(@RequestBody TikTokCommonRequest param) {
        String advertiserId = param.getAdvertiserId();
        Integer shopId = param.getShopId();
        checkShopRight(shopId);

        return ResultUtil.success(Collections.singletonMap("promoteAllProductsAllowed", gmvMaxService.shopAdUsageCheck(getPuid(), advertiserId, shopId)));
    }

    @PostMapping("/shop/product/list")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__VIEW_LIST})
    public Result<?> shopProductList(@RequestBody @Valid ShopProductParam param) {
        log.info("shopProductList param :{}", param);

        checkShopRight(param.getShopId());

        if (Objects.isNull(param.getPageNo()) || param.getPageNo() < 1) {
            param.setPageNo(1);
        }
        if (Objects.isNull(param.getPageSize()) || param.getPageSize() < 1) {
            param.setPageSize(100);
        }
        return ResultUtil.success(gmvMaxService.shopProductList(getPuid(), param));
    }

    @PostMapping("/identity/get")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__VIEW_LIST})
    public Result<GmvMaxIdentityGetResponse> identityGet(@RequestBody IdentityGetParam param) {
        String advertiserId = param.getAdvertiserId();
        Integer shopId = param.getShopId();
        if (Objects.isNull(param.getPageNo()) || param.getPageNo() < 1) {
            param.setPageNo(1);
        }
        if (Objects.isNull(param.getPageSize()) || param.getPageSize() < 1) {
            param.setPageSize(10);
        }
        checkShopRight(shopId);
        return ResultUtil.success(gmvMaxService.identityGet(getPuid(), advertiserId, shopId, param.getStoreAuthorizedBcId(),  param.getPageNo(), param.getPageSize()));
    }

    @PostMapping("/video/get")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__VIEW_LIST})
    public Result<?> videoGet(@RequestBody @Valid GmvMaxVideoGetParam param) {
        checkShopRight(param.getShopId());

        if (Objects.isNull(param.getPageNo()) || param.getPageNo() < 1) {
            param.setPageNo(1);
        }
        if (Objects.isNull(param.getPageSize()) || param.getPageSize() < 1) {
            param.setPageSize(50);
        }

        if (Boolean.TRUE.equals(param.getCustomPostsEligible())) {
            if (CollectionUtils.size(param.getSpuIdList()) != 1) {
                throw new BizServiceException("查询自定义作品时，仅支持选择一个spu");
            }
        }
        return ResultUtil.success(gmvMaxService.gmvMaxVideoGet(getPuid(), param));
    }

    @PostMapping("/create")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__ADD_DATA})
    public Result<?> create(@RequestBody @Valid GmvMaxCampaignCreateParam param) {
        checkShopRight(param.getShopId());
        if (StringUtils.equalsIgnoreCase(param.getProductSpecificType(), "CUSTOMIZED_PRODUCTS") && CollectionUtils.isEmpty(param.getSpuIdList())) {
            throw new BizServiceException("请选择需要推广的商品");
        }
        // todo tiktok 商品类型的需要校验 本次只做商品类型，所以需要校验
        if (!StringUtils.equalsAnyIgnoreCase(param.getProductVideoSpecificType(), "AUTO_SELECTION", "CUSTOM_SELECTION")) {
            throw new BizServiceException("创意模式参数不正确");
        }
        return ResultUtil.success(Collections.singletonMap("campaignId", gmvMaxService.create(getPuid(), param)));
    }

    @PostMapping("/campaign/get")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__VIEW_LIST})
    public Result<?> campaignGet(@RequestBody TikTokCommonRequest param) {
        String advertiserId = param.getAdvertiserId();
        Integer shopId = param.getShopId();
        String campaignId = param.getCampaignId();
        checkShopRight(shopId);
        return ResultUtil.success(gmvMaxService.campaignInfoGet(getPuid(), advertiserId, shopId, campaignId));
    }

    @PostMapping("/campaign/status/update")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__ADD_DATA})
    public Result<?> campaignStatusUpdate(@RequestBody @Valid GmvMaxCampaignStatusUpdateParam param) {
        checkShopRight(param.getShopId());
        Integer puid = getPuid();
        return ResultUtil.success(Collections.singletonMap("campaignId", gmvMaxService.campaignStatusUpdate(puid, param)));
    }

    @PostMapping("/update")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__ADD_DATA})
    public Result<?> update(@RequestBody @Valid GmvMaxCampaignUpdateParam param) {
        checkShopRight(param.getShopId());
        Integer puid = getPuid();
        TikTokGmvMaxCampaign campaign = tiktokGmvMaxCampaignDao.getByCampaignId(puid, param.getShopId(), param.getAdvertiserId(), param.getCampaignId());
        if (Objects.isNull(campaign)) {
            throw new BizServiceException("未查询到该广告");
        }
        return ResultUtil.success(Collections.singletonMap("campaignId", gmvMaxService.update(puid, param)));
    }

    @PostMapping("/list")
    @CheckRight(value = {SellfoxRightResource.MOD_AD_MANAGE_TIKTOK__VIEW_LIST})
    public Result<?> list(@RequestBody @Valid GmvMaxCampaignListParam param) {
        Integer puid = getPuid();
        if (Objects.isNull(param.getPageNo()) || param.getPageNo() < 1) {
            param.setPageNo(1);
        }
        if (Objects.isNull(param.getPageSize()) || param.getPageSize() < 1) {
            param.setPageSize(10);
        }
        return ResultUtil.success(gmvMaxService.list(puid, checkShopRight(param.getShopId()), param));
    }

    private List<Integer> checkShopRight(Integer shopId) {
        List<Integer> shopIds = (shopId == null ? null : Collections.singletonList(shopId));
        shopIds = tkCommonService.checkShopRight(getUid(), shopIds);
        shopIds = tkCommonService.checkAdShopAuthList(getPuid(), shopIds);
        return shopIds;
    }

}
