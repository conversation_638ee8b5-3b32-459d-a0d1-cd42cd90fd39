package com.meiyunji.sponsored.api.pricing;

import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Constants;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.pricing.template.*;
import com.meiyunji.sponsored.rpc.vo.CommonResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleEnableStatusEnum;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.log.enums.ItemTypeEnum;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyAdGroupDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTemplateDao;
import com.meiyunji.sponsored.service.strategy.enums.*;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.strategy.service.IAdvertiseStrategyTemplateService;
import com.meiyunji.sponsored.service.strategy.vo.*;
import com.meiyunji.sponsored.service.util.PbUtil;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@GRpcService
@Slf4j
public class AdvertiseStrategyTemplateRpcService extends RPCAdStrategyTemplateServiceGrpc.RPCAdStrategyTemplateServiceImplBase {

    @Autowired
    private IAdvertiseStrategyTemplateService strategyTemplateService;
    @Autowired
    private AdvertiseStrategyTemplateDao advertiseStrategyTemplateDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private AdvertiseStrategyAdGroupDao advertiseStrategyAdGroupDao;

    @Override
    public void pageList(AdStrategyTemplatePageParamRequest request, StreamObserver<AdStrategyTemplatePageListResponse> responseObserver) {
        log.info("分页查询分时调价模板执行记录 request {}", request);
        AdStrategyTemplatePageListResponse.Builder responseBuilder = AdStrategyTemplatePageListResponse.newBuilder();
        if (!request.hasPuid() || !request.hasPageNo() || !request.hasPageSize()) {
            responseBuilder.setMsg("请求参数错误");
            responseBuilder.setCode(Result.ERROR);
        } else {
            AdvertiseStrategyTemplateRequest param = new AdvertiseStrategyTemplateRequest();
            param.setPageNo(request.getPageNo());
            param.setPageSize(request.getPageSize());
            param.setShopIdList(request.getShopIdList());
            param.setSearchValue(request.getTemplateName());
            param.setItemType(request.getItemType());
            param.setAddWayType(request.getAddWayType());
            param.setTemplateId(request.getTemplateId());
            param.setChildrenItemType(request.getChildrenItemType());
            param.setTraceId(request.getTraceId());
            param.setStartStopItemType(request.getStartStopItemType());
            param.setApiType(request.getApiType());
            if (StringUtils.isNotBlank(request.getUpdateTimeStart())) {
                param.setUpdateTimeStart(DateUtil.strToDate(request.getUpdateTimeStart(), DateUtil.PATTERN));
            }
            if (StringUtils.isNotBlank(request.getUpdateTimeEnd())) {
                param.setUpdateTimeEnd(DateUtil.getDayMaxDate(DateUtil.strToDate(request.getUpdateTimeEnd(), DateUtil.PATTERN)));
            }
            if (StringUtils.isNotBlank(request.getUpdateUid())) {
                param.setUpdateUidList(Arrays.stream(request.getUpdateUid().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
            }
            if (StringUtils.isNotBlank(request.getCreateUid())) {
                param.setCreateUidList(Arrays.stream(request.getCreateUid().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
            }
            Result<Page<AdvertiseStrategyTemplate>> result = strategyTemplateService.pageList(request.getPuid(), param);
            if (result.getCode() == Result.ERROR) {
                responseBuilder.setMsg(result.getMsg());
                responseBuilder.setCode(result.getCode());
            } else {
                Page<AdvertiseStrategyTemplate> page = result.getData();
                AdStrategyTemplatePage.Builder pageBuilder = AdStrategyTemplatePage.newBuilder();
                pageBuilder.setPageNo(page.getPageNo());
                pageBuilder.setPageSize(page.getPageSize());
                pageBuilder.setTotalPage(page.getTotalPage());
                pageBuilder.setTotalSize(page.getTotalSize());
                List<AdvertiseStrategyTemplate> strategyTemplateList = page.getRows();
                if (CollectionUtils.isEmpty(strategyTemplateList)) {
                    responseBuilder.setMsg("当前无数据");
                    responseBuilder.setData(pageBuilder.build());
                    responseBuilder.setCode(Result.SUCCESS);
                } else {
                    List<AdStrategyTemplateRpcVo> list = new ArrayList<>(strategyTemplateList.size());
                    List<ShopAuth> shops = shopAuthDao.getListByPuid(request.getPuid());
                    Map<Integer, ShopAuth> shopAuthMap = null;
                    if (CollectionUtils.isNotEmpty(shops)) {
                        shopAuthMap = shops.stream().
                                collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
                    }
                    for (AdvertiseStrategyTemplate strategyTemplate : strategyTemplateList) {
                        AdStrategyTemplateRpcVo.Builder builder = AdStrategyTemplateRpcVo.newBuilder();
                        builder.setId(strategyTemplate.getId());
                        builder.setPuid(strategyTemplate.getPuid());
                        builder.setShopId(strategyTemplate.getShopId());
                        if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(strategyTemplate.getShopId())) {
                            builder.setShopName(shopAuthMap.get(strategyTemplate.getShopId()).getName());
                        }
                        builder.setMarketplaceId(strategyTemplate.getMarketplaceId());
                        builder.setVersion(strategyTemplate.getVersion());
                        if (strategyTemplate.getState() == null || !strategyTemplate.getState().equals(0)) {
                            builder.setTaskState(1);
                        } else {
                            builder.setTaskState(0);
                        }
                        if (StringUtils.isNotBlank(strategyTemplate.getAddWayType())) {
                            builder.setAddWayType(strategyTemplate.getAddWayType());
                        }
                        if (AdItemType.CAMPAIGN.name().equals(strategyTemplate.getItemType())) {
                            List<CampaignRuleVo> campaignRuleVoList = JSONUtil.jsonToArray(strategyTemplate.getRule(),CampaignRuleVo.class);
                            List<CampaignRule> campaignRuleList = new ArrayList<>(campaignRuleVoList.size());
                            Map<Integer,List<CampaignRuleVo>> map = campaignRuleVoList.stream().
                                    collect(Collectors.groupingBy(CampaignRuleVo::getSiteDate));
                            for (Integer siteDate : map.keySet()) {
                                CampaignRule.Builder campaignRuleBuilder = CampaignRule.newBuilder();
                                campaignRuleBuilder.setSiteDate(siteDate);
                                for (CampaignRuleVo campaignRuleVo : map.get(siteDate)) {
                                    Campaign.Builder campaign = Campaign.newBuilder();
                                    campaign.setBudgetValue(campaignRuleVo.getBudgetValue().doubleValue());
                                    campaign.setStartTimeSite(campaignRuleVo.getStartTimeSite());
                                    campaign.setEndTimeSite(campaignRuleVo.getEndTimeSite());
                                    if ("numerical".equals(request.getChildrenItemType())) {
                                        campaign.setBudgetType(campaignRuleVo.getBudgetType());
                                        MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(strategyTemplate.getMarketplaceId());
                                        if (null != m) {
                                            campaign.setSymbol(m.getCurrencyCode());
                                        }
                                    } else {
                                        campaign.setSymbol("%");
                                    }
                                    campaignRuleBuilder.addCampaigns(campaign);
                                }
                                if (siteDate == 0) {
                                    campaignRuleBuilder.setSiteDateName("每日");
                                } else {
                                    campaignRuleBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                            cpc.util.Constants.getDateMap().get(siteDate));
                                }
                                campaignRuleList.add(campaignRuleBuilder.build());
                            }
                            builder.addAllCampaignRuleList(campaignRuleList);
                        } else if (AdItemType.CAMPAIGN_PLACEMENT.name().equals(strategyTemplate.getItemType())) {
                            List<CampaignPlacementRuleVo> campaignPlacementRuleVoList = JSONUtil.jsonToArray(strategyTemplate.getRule(),CampaignPlacementRuleVo.class);
                            List<CampaignPlacementRule> campaignPlacementRuleList = new ArrayList<>(campaignPlacementRuleVoList.size());
                            Map<Integer,List<CampaignPlacementRuleVo>> map = campaignPlacementRuleVoList.stream().
                                    collect(Collectors.groupingBy(CampaignPlacementRuleVo::getSiteDate));
                            for (Integer siteDate : map.keySet()) {
                                CampaignPlacementRule.Builder campaignPlacementRuleBuilder = CampaignPlacementRule.newBuilder();
                                campaignPlacementRuleBuilder.setSiteDate(siteDate);
                                for (CampaignPlacementRuleVo campaignPlacementRuleVo : map.get(siteDate)) {
                                    CampaignPlacement.Builder campaignPlacement = CampaignPlacement.newBuilder();
                                    if (campaignPlacementRuleVo.getAdPlaceProductType() != null) {
                                        campaignPlacement.setAdPlaceProductType(campaignPlacementRuleVo.getAdPlaceProductType());
                                    }
                                    campaignPlacement.setStartTimeSite(campaignPlacementRuleVo.getStartTimeSite());
                                    campaignPlacement.setEndTimeSite(campaignPlacementRuleVo.getEndTimeSite());
                                    if (campaignPlacementRuleVo.getAdPlaceProductValue() != null) {
                                        campaignPlacement.setAdPlaceProductValue(campaignPlacementRuleVo.getAdPlaceProductValue().doubleValue());
                                    }
                                    if (campaignPlacementRuleVo.getAdPlaceTopMaxValue() != null) {
                                        campaignPlacement.setAdPlaceTopMaxValue(campaignPlacementRuleVo.getAdPlaceTopMaxValue().doubleValue());
                                    }
                                    if (campaignPlacementRuleVo.getAdPlaceTopMinValue() != null) {
                                        campaignPlacement.setAdPlaceTopMinValue(campaignPlacementRuleVo.getAdPlaceTopMinValue().doubleValue());
                                    }
                                    if (campaignPlacementRuleVo.getAdPlaceProductMaxValue() != null) {
                                        campaignPlacement.setAdPlaceProductMaxValue(campaignPlacementRuleVo.getAdPlaceProductMaxValue().doubleValue());
                                    }
                                    if (campaignPlacementRuleVo.getAdPlaceProductMinValue() != null) {
                                        campaignPlacement.setAdPlaceProductMinValue(campaignPlacementRuleVo.getAdPlaceProductMinValue().doubleValue());
                                    }
                                    if (campaignPlacementRuleVo.getAdPlaceTopType() != null) {
                                        campaignPlacement.setAdPlaceTopType(campaignPlacementRuleVo.getAdPlaceTopType());
                                    }
                                    if (campaignPlacementRuleVo.getAdPlaceTopValue() != null) {
                                        campaignPlacement.setAdPlaceTopValue(campaignPlacementRuleVo.getAdPlaceTopValue().doubleValue());
                                    }
                                    if (StringUtils.isNotBlank(campaignPlacementRuleVo.getStrategy())) {
                                        campaignPlacement.setStrategy(campaignPlacementRuleVo.getStrategy());
                                    }
                                    if (campaignPlacementRuleVo.getAdOtherType() != null) {
                                        campaignPlacement.setAdOtherType(campaignPlacementRuleVo.getAdOtherType());
                                    } else {
                                        campaignPlacement.setAdOtherType(4);
                                    }
                                    if (campaignPlacementRuleVo.getAdOtherValue() != null) {
                                        campaignPlacement.setAdOtherValue(campaignPlacementRuleVo.getAdOtherValue().doubleValue());
                                    } else {
                                        campaignPlacement.setAdOtherValue(0);
                                    }
                                    campaignPlacement.setSymbol("%");
                                    campaignPlacementRuleBuilder.addCampaignPlacements(campaignPlacement);
                                }
                                if (siteDate == 0) {
                                    campaignPlacementRuleBuilder.setSiteDateName("每日");
                                } else {
                                    campaignPlacementRuleBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                            cpc.util.Constants.getDateMap().get(siteDate));
                                }
                                campaignPlacementRuleList.add(campaignPlacementRuleBuilder.build());
                            }
                            builder.addAllCampaignPlacementRuleList(campaignPlacementRuleList);
                        } else if (AdItemType.TARGET.name().equals(strategyTemplate.getItemType())) {
                            List<TargetRuleVo> targetRuleVoList = JSONUtil.jsonToArray(strategyTemplate.getRule(),TargetRuleVo.class);
                            List<TargetRule> targetRuleList = new ArrayList<>(targetRuleVoList.size());
                            Map<Integer,List<TargetRuleVo>> map = targetRuleVoList.stream().
                                    collect(Collectors.groupingBy(TargetRuleVo::getSiteDate));
                            for (Integer siteDate : map.keySet()) {
                                TargetRule.Builder targetRuleBuilder = TargetRule.newBuilder();
                                targetRuleBuilder.setSiteDate(siteDate);
                                for (TargetRuleVo targetRuleVo:map.get(siteDate)) {
                                    Target.Builder target = Target.newBuilder();
                                    target.setStartTimeSite(targetRuleVo.getStartTimeSite());
                                    target.setEndTimeSite(targetRuleVo.getEndTimeSite());
                                    target.setBiddingType(targetRuleVo.getBiddingType());
                                    target.setBiddingValue(targetRuleVo.getBiddingValue().doubleValue());
                                    if (targetRuleVo.getBiddingMaxValue() != null) {
                                        target.setBiddingMaxValue(targetRuleVo.getBiddingMaxValue().doubleValue());
                                    }
                                    if (targetRuleVo.getBiddingMinValue() != null) {
                                        target.setBiddingMinValue(targetRuleVo.getBiddingMinValue().doubleValue());
                                    }
                                    MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(strategyTemplate.getMarketplaceId());
                                    if(null != m){
                                        target.setSymbol(m.getCurrencyCode());
                                    }
                                    targetRuleBuilder.addTargets(target);
                                }
                                if (siteDate == 0) {
                                    targetRuleBuilder.setSiteDateName("每日");
                                } else {
                                    targetRuleBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                            cpc.util.Constants.getDateMap().get(siteDate));
                                }
                                targetRuleList.add(targetRuleBuilder.build());
                            }
                            builder.addAllTargetRuleList(targetRuleList);
                        } else if (AdItemType.START_STOP.name().equals(strategyTemplate.getItemType())) {
                            List<StartStopRuleVo> startStopRuleVoList = JSONUtil.jsonToArray(strategyTemplate.getRule(), StartStopRuleVo.class);
                            List<StartStopStateRule> startStopStateRules = Lists.newArrayList();
                            Map<Integer,List<StartStopRuleVo>> map = startStopRuleVoList.stream().
                                    collect(Collectors.groupingBy(StartStopRuleVo::getSiteDate));
                            for (Integer siteDate : map.keySet()) {
                                StartStopStateRule.Builder startStopBuilder = StartStopStateRule.newBuilder();
                                startStopBuilder.setSiteDate(siteDate);
                                if (siteDate == 0) {
                                    startStopBuilder.setSiteDateName("每日");
                                } else {
                                    startStopBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                            cpc.util.Constants.getDateMap().get(siteDate));
                                }
                                for (StartStopRuleVo startStopRuleVo : map.get(siteDate)) {
                                    StateRule.Builder stateBuilder = StateRule.newBuilder();
                                    stateBuilder.setStartTimeSite(startStopRuleVo.getStartTimeSite());
                                    stateBuilder.setEndTimeSite(startStopRuleVo.getEndTimeSite());
                                    stateBuilder.setState(startStopRuleVo.getState());
                                    startStopBuilder.addStateRules(stateBuilder);
                                }
                                startStopStateRules.add(startStopBuilder.build());
                            }
                            builder.addAllStartStopStateRuleList(startStopStateRules);
                        } else if (AdItemType.PORTFOLIO.name().equals(strategyTemplate.getItemType())) {
                            builder.setReturnValue(PbUtil.buildPortfolioByJson(strategyTemplate.getReturnValue()));
                            if (PbUtil.isPortfolioHour(strategyTemplate.getChildrenItemType())) {
                                builder.addAllPortfolioHourRuleList(PbUtil.buildPortfolioHourRuleByJson(strategyTemplate.getRule(), strategyTemplate.getMarketplaceId()));
                                builder.setChildrenItemType(strategyTemplate.getChildrenItemType());
                            } else {
                                List<PortfolioRuleVo> portfolioRuleList = JSONUtil.jsonToArray(strategyTemplate.getRule(), PortfolioRuleVo.class);
                                List<PortfolioRule> portfolioRules = Lists.newArrayList();
                                Map<Integer,List<PortfolioRuleVo>> map = new LinkedHashMap<>();
                                Map<Integer,List<PortfolioRuleVo>> listMap = new LinkedHashMap<>();
                                if ("MONTH_DAY".equals(strategyTemplate.getType())) {
                                    portfolioRuleList.stream().collect(Collectors.groupingBy(e->
                                                    Integer.valueOf(LocalDate.parse(e.getStartDate(),DateTimeFormatter.ofPattern(DateUtil.PATTERN)).
                                                            format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD))))).entrySet().stream()
                                            .sorted(Map.Entry.comparingByKey()).forEachOrdered(x->listMap.put(x.getKey(),x.getValue()));
                                } else {
                                    map = portfolioRuleList.stream().collect(Collectors.groupingBy(PortfolioRuleVo::getSiteDate));
                                }
                                if (MapUtils.isNotEmpty(map)) {
                                    for (Integer siteDate : map.keySet()) {
                                        PortfolioRule.Builder portfolioBuilder = PortfolioRule.newBuilder();
                                        portfolioBuilder.setSiteDate(siteDate);
                                        if (siteDate == 0) {
                                            portfolioBuilder.setSiteDateName("每日");
                                        } else {
                                            portfolioBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                                    cpc.util.Constants.getDateMap().get(siteDate));
                                        }
                                        for (PortfolioRuleVo portfolioRuleVo : map.get(siteDate)) {
                                            Portfolio.Builder portfolio = Portfolio.newBuilder();
                                            if (!"noBudget".equals(portfolioRuleVo.getAmount())) {
                                                portfolio.setAmount(portfolioRuleVo.getAmount().doubleValue());
                                            }
                                            portfolio.setPolicy(portfolioRuleVo.getPolicy());
                                            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(strategyTemplate.getMarketplaceId());
                                            if(null != m){
                                                portfolio.setSymbol(m.getCurrencyCode());
                                            }
                                            portfolioBuilder.addPortfolios(portfolio.build());
                                        }
                                        portfolioRules.add(portfolioBuilder.build());
                                    }
                                }
                                if (MapUtils.isNotEmpty(listMap)) {
                                    for (Integer countDate : listMap.keySet()) {
                                        PortfolioRule.Builder portfolioBuilder = PortfolioRule.newBuilder();
                                        portfolioBuilder.setStartDate((listMap.get(countDate).get(0).getStartDate()));
                                        portfolioBuilder.setEndDate(listMap.get(countDate).get(0).getEndDate());
                                        for (PortfolioRuleVo portfolioRuleVo : listMap.get(countDate)) {
                                            Portfolio.Builder portfolio = Portfolio.newBuilder();
                                            if (!"noBudget".equals(portfolioRuleVo.getAmount())) {
                                                portfolio.setAmount(portfolioRuleVo.getAmount().doubleValue());
                                            }
                                            portfolio.setPolicy(portfolioRuleVo.getPolicy());
                                            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(strategyTemplate.getMarketplaceId());
                                            if(null != m){
                                                portfolio.setSymbol(m.getCurrencyCode());
                                            }
                                            portfolioBuilder.addPortfolios(portfolio.build());
                                        }
                                        portfolioRules.add(portfolioBuilder.build());
                                    }
                                }
                                builder.addAllPortfolioRuleList(portfolioRules);
                            }
                        }
                        builder.setMarketplaceName(strategyTemplate.getMarketplaceName());
                        builder.setType(strategyTemplate.getType());
                        builder.setItemType(strategyTemplate.getItemType());
                        builder.setTemplateName(strategyTemplate.getTemplateName());
                        builder.setUsageAmount(Optional.ofNullable(strategyTemplate.getUsageAmount()).orElse(0));
                        builder.setExecutePreview(Optional.ofNullable(strategyTemplate.getExecutePreview()).orElse(0));
                        builder.setStatus(strategyTemplate.getStatus());
                        if (strategyTemplate.getCreateUid() != null) {
                            builder.setCreateUid(strategyTemplate.getCreateUid());
                        }
                        if (strategyTemplate.getUpdateUid() != null) {
                            builder.setUpdateUid(strategyTemplate.getUpdateUid());
                        }
                        if (StringUtils.isNotBlank(strategyTemplate.getCreateName())) {
                            builder.setCreateName(strategyTemplate.getCreateName());
                        }
                        if (StringUtils.isNotBlank(strategyTemplate.getUpdateName())){
                            builder.setUpdateName(strategyTemplate.getUpdateName());
                        }
                        if (StringUtils.isNotBlank(strategyTemplate.getStartStopItemType())) {
                            builder.setStartStopItemType(strategyTemplate.getStartStopItemType());
                        } else {
                            builder.setStartStopItemType("");
                        }
                        builder.setCreateAt(strategyTemplate.getCreateAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        builder.setLastUpdateAt(strategyTemplate.getLastUpdateAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        list.add(builder.build());
                    }
                    pageBuilder.addAllRows(list);
                    responseBuilder.setCode(Result.SUCCESS);
                    responseBuilder.setData(pageBuilder.build());
                }
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void independentPageTemplate(IndependentPageTemplateRequest request, StreamObserver<IndependentPageTemplateResponse> responseObserver) {
        log.info("分页查询分时调价模板执行记录 request {}", request);
        IndependentPageTemplateResponse.Builder responseBuilder = IndependentPageTemplateResponse.newBuilder();
        if (!request.hasPuid() || !request.hasPageNo() || !request.hasPageSize()) {
            responseBuilder.setMsg("请求参数错误");
            responseBuilder.setCode(Result.ERROR);
        } else {
            AdvertiseStrategyTemplateRequest param = new AdvertiseStrategyTemplateRequest();
            param.setPageNo(request.getPageNo());
            param.setPageSize(request.getPageSize());
            param.setShopIdList(request.getShopIdList());
            param.setSearchValue(request.getTemplateName());
            param.setItemType(request.getItemType());
            param.setTemplateId(request.getTemplateId());
            param.setChildrenItemType(request.getChildrenItemType());
            Result<Page<AdvertiseStrategyTemplate>> result = strategyTemplateService.pageList(request.getPuid(), param);
            if (result.getCode() == Result.ERROR) {
                responseBuilder.setMsg(result.getMsg());
                responseBuilder.setCode(result.getCode());
            } else {
                Page<AdvertiseStrategyTemplate> page = result.getData();
                IndependentPageTemplate.Builder pageBuilder = IndependentPageTemplate.newBuilder();
                pageBuilder.setPageNo(page.getPageNo());
                pageBuilder.setPageSize(page.getPageSize());
                pageBuilder.setTotalPage(page.getTotalPage());
                pageBuilder.setTotalSize(page.getTotalSize());
                List<AdvertiseStrategyTemplate> strategyTemplateList = page.getRows();
                if (CollectionUtils.isEmpty(strategyTemplateList)) {
                    responseBuilder.setMsg("当前无数据");
                    responseBuilder.setCode(Result.SUCCESS);
                } else {
                    AdvertiseStrategyTemplate advertiseStrategyTemplate = advertiseStrategyTemplateDao.
                            selectByPrimaryKey(request.getPuid(),request.getTemplateId());
                    if (AdItemType.CAMPAIGN.name().equals(advertiseStrategyTemplate.getItemType())) {
                        List<CampaignRuleVo> campaignRuleVoList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(),CampaignRuleVo.class);
                        List<CampaignRule> campaignRuleList = new ArrayList<>(campaignRuleVoList.size());
                        Map<Integer,List<CampaignRuleVo>> map = campaignRuleVoList.stream().
                                collect(Collectors.groupingBy(CampaignRuleVo::getSiteDate));
                        for (Integer siteDate : map.keySet()) {
                            CampaignRule.Builder campaignRuleBuilder = CampaignRule.newBuilder();
                            campaignRuleBuilder.setSiteDate(siteDate);
                            for (CampaignRuleVo campaignRuleVo : map.get(siteDate)) {
                                Campaign.Builder campaign = Campaign.newBuilder();
                                campaign.setBudgetValue(campaignRuleVo.getBudgetValue().doubleValue());
                                campaign.setStartTimeSite(campaignRuleVo.getStartTimeSite());
                                campaign.setEndTimeSite(campaignRuleVo.getEndTimeSite());
                                if ("numerical".equals(request.getChildrenItemType())) {
                                    campaign.setBudgetType(campaignRuleVo.getBudgetType());
                                    MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(advertiseStrategyTemplate.getMarketplaceId());
                                    if(null != m){
                                        campaign.setSymbol(m.getCurrencyCode());
                                    }
                                } else {
                                    campaign.setSymbol("%");
                                }
                                campaignRuleBuilder.addCampaigns(campaign);
                            }
                            if (siteDate == 0) {
                                campaignRuleBuilder.setSiteDateName("每日");
                            } else {
                                campaignRuleBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                        cpc.util.Constants.getDateMap().get(siteDate));
                            }
                            campaignRuleList.add(campaignRuleBuilder.build());
                        }
                        pageBuilder.addAllCampaignRuleList(campaignRuleList);
                    } else if (AdItemType.CAMPAIGN_PLACEMENT.name().equals(advertiseStrategyTemplate.getItemType())) {
                        List<CampaignPlacementRuleVo> campaignPlacementRuleVoList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(),CampaignPlacementRuleVo.class);
                        List<CampaignPlacementRule> campaignPlacementRuleList = new ArrayList<>(campaignPlacementRuleVoList.size());
                        Map<Integer,List<CampaignPlacementRuleVo>> map = campaignPlacementRuleVoList.stream().
                                collect(Collectors.groupingBy(CampaignPlacementRuleVo::getSiteDate));
                        for (Integer siteDate : map.keySet()) {
                            CampaignPlacementRule.Builder campaignPlacementRuleBuilder = CampaignPlacementRule.newBuilder();
                            campaignPlacementRuleBuilder.setSiteDate(siteDate);
                            for (CampaignPlacementRuleVo campaignPlacementRuleVo : map.get(siteDate)) {
                                CampaignPlacement.Builder campaignPlacement = CampaignPlacement.newBuilder();
                                if (campaignPlacementRuleVo.getAdPlaceProductType() != null) {
                                    campaignPlacement.setAdPlaceProductType(campaignPlacementRuleVo.getAdPlaceProductType());
                                }
                                campaignPlacement.setStartTimeSite(campaignPlacementRuleVo.getStartTimeSite());
                                campaignPlacement.setEndTimeSite(campaignPlacementRuleVo.getEndTimeSite());
                                if (campaignPlacementRuleVo.getAdPlaceProductValue() != null) {
                                    campaignPlacement.setAdPlaceProductValue(campaignPlacementRuleVo.getAdPlaceProductValue().doubleValue());
                                }
                                if (campaignPlacementRuleVo.getAdPlaceTopMaxValue() != null) {
                                    campaignPlacement.setAdPlaceTopMaxValue(campaignPlacementRuleVo.getAdPlaceTopMaxValue().doubleValue());
                                }
                                if (campaignPlacementRuleVo.getAdPlaceTopMinValue() != null) {
                                    campaignPlacement.setAdPlaceTopMinValue(campaignPlacementRuleVo.getAdPlaceTopMinValue().doubleValue());
                                }
                                if (campaignPlacementRuleVo.getAdPlaceProductMaxValue() != null) {
                                    campaignPlacement.setAdPlaceProductMaxValue(campaignPlacementRuleVo.getAdPlaceProductMaxValue().doubleValue());
                                }
                                if (campaignPlacementRuleVo.getAdPlaceProductMinValue() != null) {
                                    campaignPlacement.setAdPlaceProductMinValue(campaignPlacementRuleVo.getAdPlaceProductMinValue().doubleValue());
                                }
                                if (campaignPlacementRuleVo.getAdPlaceTopType() != null) {
                                    campaignPlacement.setAdPlaceTopType(campaignPlacementRuleVo.getAdPlaceTopType());
                                }
                                if (campaignPlacementRuleVo.getAdPlaceTopValue() != null) {
                                    campaignPlacement.setAdPlaceTopValue(campaignPlacementRuleVo.getAdPlaceTopValue().doubleValue());
                                }
                                if (StringUtils.isNotBlank(campaignPlacementRuleVo.getStrategy())) {
                                    campaignPlacement.setStrategy(campaignPlacementRuleVo.getStrategy());
                                }
                                if (campaignPlacementRuleVo.getAdOtherType() != null) {
                                    campaignPlacement.setAdOtherType(campaignPlacementRuleVo.getAdOtherType());
                                } else {
                                    campaignPlacement.setAdOtherType(4);
                                }
                                if (campaignPlacementRuleVo.getAdOtherValue() != null) {
                                    campaignPlacement.setAdOtherValue(campaignPlacementRuleVo.getAdOtherValue().doubleValue());
                                } else {
                                    campaignPlacement.setAdOtherValue(0);
                                }
                                campaignPlacement.setSymbol("%");
                                campaignPlacementRuleBuilder.addCampaignPlacements(campaignPlacement);
                            }
                            if (siteDate == 0) {
                                campaignPlacementRuleBuilder.setSiteDateName("每日");
                            } else {
                                campaignPlacementRuleBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                        cpc.util.Constants.getDateMap().get(siteDate));
                            }
                            campaignPlacementRuleList.add(campaignPlacementRuleBuilder.build());
                        }
                        pageBuilder.addAllCampaignPlacementRuleList(campaignPlacementRuleList);
                    } else if (AdItemType.TARGET.name().equals(advertiseStrategyTemplate.getItemType())) {
                        List<TargetRuleVo> targetRuleVoList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(),TargetRuleVo.class);
                        List<TargetRule> targetRuleList = new ArrayList<>(targetRuleVoList.size());
                        Map<Integer,List<TargetRuleVo>> map = targetRuleVoList.stream().
                                collect(Collectors.groupingBy(TargetRuleVo::getSiteDate));
                        for (Integer siteDate : map.keySet()) {
                            TargetRule.Builder targetRuleBuilder = TargetRule.newBuilder();
                            targetRuleBuilder.setSiteDate(siteDate);
                            for (TargetRuleVo targetRuleVo:map.get(siteDate)) {
                                Target.Builder target = Target.newBuilder();
                                target.setStartTimeSite(targetRuleVo.getStartTimeSite());
                                target.setEndTimeSite(targetRuleVo.getEndTimeSite());
                                target.setBiddingType(targetRuleVo.getBiddingType());
                                target.setBiddingValue(targetRuleVo.getBiddingValue().doubleValue());
                                if (targetRuleVo.getBiddingMaxValue() != null) {
                                    target.setBiddingMaxValue(targetRuleVo.getBiddingMaxValue().doubleValue());
                                }
                                if (targetRuleVo.getBiddingMinValue() != null) {
                                    target.setBiddingMinValue(targetRuleVo.getBiddingMinValue().doubleValue());
                                }
                                MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(advertiseStrategyTemplate.getMarketplaceId());
                                if(null != m){
                                    target.setSymbol(m.getCurrencyCode());
                                }
                                targetRuleBuilder.addTargets(target);
                            }
                            if (siteDate == 0) {
                                targetRuleBuilder.setSiteDateName("每日");
                            } else {
                                targetRuleBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                        cpc.util.Constants.getDateMap().get(siteDate));
                            }
                            targetRuleList.add(targetRuleBuilder.build());
                        }
                        pageBuilder.addAllTargetRuleList(targetRuleList);
                    } else if (AdItemType.START_STOP.name().equals(advertiseStrategyTemplate.getItemType())) {
                        List<StartStopRuleVo> startStopRuleVoList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(), StartStopRuleVo.class);
                        List<StartStopStateRule> startStopStateRules = Lists.newArrayList();
                        Map<Integer,List<StartStopRuleVo>> map = startStopRuleVoList.stream().
                                collect(Collectors.groupingBy(StartStopRuleVo::getSiteDate));
                        for (Integer siteDate : map.keySet()) {
                            StartStopStateRule.Builder startStopBuilder = StartStopStateRule.newBuilder();
                            startStopBuilder.setSiteDate(siteDate);
                            if (siteDate == 0) {
                                startStopBuilder.setSiteDateName("每日");
                            } else {
                                startStopBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                        cpc.util.Constants.getDateMap().get(siteDate));
                            }
                            for (StartStopRuleVo startStopRuleVo : map.get(siteDate)) {
                                StateRule.Builder stateBuilder = StateRule.newBuilder();
                                stateBuilder.setStartTimeSite(startStopRuleVo.getStartTimeSite());
                                stateBuilder.setEndTimeSite(startStopRuleVo.getEndTimeSite());
                                stateBuilder.setState(startStopRuleVo.getState());
                                startStopBuilder.addStateRules(stateBuilder);
                            }
                            startStopStateRules.add(startStopBuilder.build());
                        }
                        pageBuilder.addAllStartStopStateRuleList(startStopStateRules);
                    }  else if (AdItemType.PORTFOLIO.name().equals(advertiseStrategyTemplate.getItemType())) {
                        //返回额外属性
                        pageBuilder.setReturnValue(PbUtil.buildPortfolioByJson(advertiseStrategyTemplate.getReturnValue()));
                        pageBuilder.setType(advertiseStrategyTemplate.getType());
                        if (PbUtil.isPortfolioHour(advertiseStrategyTemplate.getChildrenItemType())) {
                            pageBuilder.addAllPortfolioHourRuleList(PbUtil.buildPortfolioHourRuleByJson(advertiseStrategyTemplate.getRule(), advertiseStrategyTemplate.getMarketplaceId()));
                            pageBuilder.setChildrenItemType(advertiseStrategyTemplate.getChildrenItemType());
                        } else {
                            List<PortfolioRuleVo> portfolioRuleList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(), PortfolioRuleVo.class);
                            List<PortfolioRule> portfolioRules = Lists.newArrayList();
                            Map<Integer, List<PortfolioRuleVo>> map = new LinkedHashMap<>();
                            Map<Integer, List<PortfolioRuleVo>> listMap = new LinkedHashMap<>();
                            if ("MONTH_DAY".equals(advertiseStrategyTemplate.getType())) {
                                portfolioRuleList.stream().collect(Collectors.groupingBy(e ->
                                                Integer.valueOf(LocalDate.parse(e.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN)).
                                                        format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD))))).entrySet().stream()
                                        .sorted(Map.Entry.comparingByKey()).forEachOrdered(x -> listMap.put(x.getKey(), x.getValue()));
                            } else {
                                map = portfolioRuleList.stream().collect(Collectors.groupingBy(PortfolioRuleVo::getSiteDate));
                            }
                            if (MapUtils.isNotEmpty(map)) {
                                for (Integer siteDate : map.keySet()) {
                                    PortfolioRule.Builder portfolioBuilder = PortfolioRule.newBuilder();
                                    portfolioBuilder.setSiteDate(siteDate);
                                    if (siteDate == 0) {
                                        portfolioBuilder.setSiteDateName("每日");
                                    } else {
                                        portfolioBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                                cpc.util.Constants.getDateMap().get(siteDate));
                                    }
                                    for (PortfolioRuleVo portfolioRuleVo : map.get(siteDate)) {
                                        Portfolio.Builder portfolio = Portfolio.newBuilder();
                                        if (!"noBudget".equals(portfolioRuleVo.getAmount())) {
                                            portfolio.setAmount(portfolioRuleVo.getAmount().doubleValue());
                                        }
                                        portfolio.setPolicy(portfolioRuleVo.getPolicy());
                                        MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(advertiseStrategyTemplate.getMarketplaceId());
                                        if (null != m) {
                                            portfolio.setSymbol(m.getCurrencyCode());
                                        }
                                        portfolioBuilder.addPortfolios(portfolio.build());
                                    }
                                    portfolioRules.add(portfolioBuilder.build());
                                }
                            }
                            if (MapUtils.isNotEmpty(listMap)) {
                                for (Integer countDate : listMap.keySet()) {
                                    PortfolioRule.Builder portfolioBuilder = PortfolioRule.newBuilder();
                                    portfolioBuilder.setStartDate(listMap.get(countDate).get(0).getStartDate());
                                    portfolioBuilder.setEndDate(listMap.get(countDate).get(0).getEndDate());
                                    for (PortfolioRuleVo portfolioRuleVo : listMap.get(countDate)) {
                                        Portfolio.Builder portfolio = Portfolio.newBuilder();
                                        if (!"noBudget".equals(portfolioRuleVo.getAmount())) {
                                            portfolio.setAmount(portfolioRuleVo.getAmount().doubleValue());
                                        }
                                        portfolio.setPolicy(portfolioRuleVo.getPolicy());
                                        MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(advertiseStrategyTemplate.getMarketplaceId());
                                        if (null != m) {
                                            portfolio.setSymbol(m.getCurrencyCode());
                                        }
                                        portfolioBuilder.addPortfolios(portfolio);
                                    }
                                    portfolioRules.add(portfolioBuilder.build());
                                }
                            }
                            pageBuilder.addAllPortfolioRuleList(portfolioRules);
                        }
                    }
                    List<AdStrategyTemplateRpcVo> list = new ArrayList<>(strategyTemplateList.size());
                    List<ShopAuth> shops = shopAuthDao.getListByPuid(request.getPuid());
                    Map<Integer, ShopAuth> shopAuthMap = null;
                    if (CollectionUtils.isNotEmpty(shops)) {
                        shopAuthMap = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
                    }
                    for (AdvertiseStrategyTemplate strategyTemplate : strategyTemplateList) {
                        AdStrategyTemplateRpcVo.Builder builder = AdStrategyTemplateRpcVo.newBuilder();
                        builder.setId(strategyTemplate.getId());
                        builder.setPuid(strategyTemplate.getPuid());
                        builder.setShopId(strategyTemplate.getShopId());
                        if (StringUtils.isNotBlank(strategyTemplate.getAddWayType())) {
                            builder.setAddWayType(strategyTemplate.getAddWayType());
                        }
                        if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(strategyTemplate.getShopId())) {
                            builder.setShopName(shopAuthMap.get(strategyTemplate.getShopId()).getName());
                        }
                        builder.setMarketplaceId(strategyTemplate.getMarketplaceId());
                        builder.setVersion(strategyTemplate.getVersion());
                        if (AdItemType.CAMPAIGN.name().equals(strategyTemplate.getItemType())) {
                            List<CampaignRuleVo> campaignRuleVoList = JSONUtil.jsonToArray(strategyTemplate.getRule(),CampaignRuleVo.class);
                            List<CampaignRule> campaignRuleList = new ArrayList<>(campaignRuleVoList.size());
                            Map<Integer,List<CampaignRuleVo>> map = campaignRuleVoList.stream().
                                    collect(Collectors.groupingBy(CampaignRuleVo::getSiteDate));
                            for (Integer siteDate : map.keySet()) {
                                CampaignRule.Builder campaignRuleBuilder = CampaignRule.newBuilder();
                                campaignRuleBuilder.setSiteDate(siteDate);
                                for (CampaignRuleVo campaignRuleVo : map.get(siteDate)) {
                                    Campaign.Builder campaign = Campaign.newBuilder();
                                    campaign.setBudgetValue(campaignRuleVo.getBudgetValue().doubleValue());
                                    campaign.setStartTimeSite(campaignRuleVo.getStartTimeSite());
                                    campaign.setEndTimeSite(campaignRuleVo.getEndTimeSite());
                                    if ("numerical".equals(request.getChildrenItemType())) {
                                        campaign.setBudgetType(campaignRuleVo.getBudgetType());
                                        MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(strategyTemplate.getMarketplaceId());
                                        if(null != m){
                                            campaign.setSymbol(m.getCurrencyCode());
                                        }
                                    } else {
                                        campaign.setSymbol("%");
                                    }
                                    campaignRuleBuilder.addCampaigns(campaign);
                                }
                                if (siteDate == 0) {
                                    campaignRuleBuilder.setSiteDateName("每日");
                                } else {
                                    campaignRuleBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                            cpc.util.Constants.getDateMap().get(siteDate));
                                }
                                campaignRuleList.add(campaignRuleBuilder.build());
                            }
                            builder.addAllCampaignRuleList(campaignRuleList);
                        } else if (AdItemType.CAMPAIGN_PLACEMENT.name().equals(strategyTemplate.getItemType())) {
                            List<CampaignPlacementRuleVo> campaignPlacementRuleVoList = JSONUtil.jsonToArray(strategyTemplate.getRule(),CampaignPlacementRuleVo.class);
                            List<CampaignPlacementRule> campaignPlacementRuleList = new ArrayList<>(campaignPlacementRuleVoList.size());
                            Map<Integer,List<CampaignPlacementRuleVo>> map = campaignPlacementRuleVoList.stream().
                                    collect(Collectors.groupingBy(CampaignPlacementRuleVo::getSiteDate));
                            for (Integer siteDate : map.keySet()) {
                                CampaignPlacementRule.Builder campaignPlacementRuleBuilder = CampaignPlacementRule.newBuilder();
                                campaignPlacementRuleBuilder.setSiteDate(siteDate);
                                for (CampaignPlacementRuleVo campaignPlacementRuleVo : map.get(siteDate)) {
                                    CampaignPlacement.Builder campaignPlacement = CampaignPlacement.newBuilder();
                                    if (campaignPlacementRuleVo.getAdPlaceProductType() != null) {
                                        campaignPlacement.setAdPlaceProductType(campaignPlacementRuleVo.getAdPlaceProductType());
                                    }
                                    campaignPlacement.setStartTimeSite(campaignPlacementRuleVo.getStartTimeSite());
                                    campaignPlacement.setEndTimeSite(campaignPlacementRuleVo.getEndTimeSite());
                                    if (campaignPlacementRuleVo.getAdPlaceProductValue() != null) {
                                        campaignPlacement.setAdPlaceProductValue(campaignPlacementRuleVo.getAdPlaceProductValue().doubleValue());
                                    }
                                    if (campaignPlacementRuleVo.getAdPlaceTopMaxValue() != null) {
                                        campaignPlacement.setAdPlaceTopMaxValue(campaignPlacementRuleVo.getAdPlaceTopMaxValue().doubleValue());
                                    }
                                    if (campaignPlacementRuleVo.getAdPlaceTopMinValue() != null) {
                                        campaignPlacement.setAdPlaceTopMinValue(campaignPlacementRuleVo.getAdPlaceTopMinValue().doubleValue());
                                    }
                                    if (campaignPlacementRuleVo.getAdPlaceProductMaxValue() != null) {
                                        campaignPlacement.setAdPlaceProductMaxValue(campaignPlacementRuleVo.getAdPlaceProductMaxValue().doubleValue());
                                    }
                                    if (campaignPlacementRuleVo.getAdPlaceProductMinValue() != null) {
                                        campaignPlacement.setAdPlaceProductMinValue(campaignPlacementRuleVo.getAdPlaceProductMinValue().doubleValue());
                                    }
                                    if (campaignPlacementRuleVo.getAdPlaceTopType() != null) {
                                        campaignPlacement.setAdPlaceTopType(campaignPlacementRuleVo.getAdPlaceTopType());
                                    }
                                    if (campaignPlacementRuleVo.getAdPlaceTopValue() != null) {
                                        campaignPlacement.setAdPlaceTopValue(campaignPlacementRuleVo.getAdPlaceTopValue().doubleValue());
                                    }
                                    if (StringUtils.isNotBlank(campaignPlacementRuleVo.getStrategy())) {
                                        campaignPlacement.setStrategy(campaignPlacementRuleVo.getStrategy());
                                    }
                                    if (campaignPlacementRuleVo.getAdOtherType() != null) {
                                        campaignPlacement.setAdOtherType(campaignPlacementRuleVo.getAdOtherType());
                                    } else {
                                        campaignPlacement.setAdOtherType(4);
                                    }
                                    if (campaignPlacementRuleVo.getAdOtherValue() != null) {
                                        campaignPlacement.setAdOtherValue(campaignPlacementRuleVo.getAdOtherValue().doubleValue());
                                    } else {
                                        campaignPlacement.setAdOtherValue(0);
                                    }
                                    campaignPlacement.setSymbol("%");
                                    campaignPlacementRuleBuilder.addCampaignPlacements(campaignPlacement);
                                }
                                if (siteDate == 0) {
                                    campaignPlacementRuleBuilder.setSiteDateName("每日");
                                } else {
                                    campaignPlacementRuleBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                            cpc.util.Constants.getDateMap().get(siteDate));
                                }
                                campaignPlacementRuleList.add(campaignPlacementRuleBuilder.build());
                            }
                            builder.addAllCampaignPlacementRuleList(campaignPlacementRuleList);
                        } else if (AdItemType.TARGET.name().equals(strategyTemplate.getItemType())) {
                            List<TargetRuleVo> targetRuleVoList = JSONUtil.jsonToArray(strategyTemplate.getRule(),TargetRuleVo.class);
                            List<TargetRule> targetRuleList = new ArrayList<>(targetRuleVoList.size());
                            Map<Integer,List<TargetRuleVo>> map = targetRuleVoList.stream().
                                    collect(Collectors.groupingBy(TargetRuleVo::getSiteDate));
                            for (Integer siteDate : map.keySet()) {
                                TargetRule.Builder targetRuleBuilder = TargetRule.newBuilder();
                                targetRuleBuilder.setSiteDate(siteDate);
                                for (TargetRuleVo targetRuleVo:map.get(siteDate)) {
                                    Target.Builder target = Target.newBuilder();
                                    target.setStartTimeSite(targetRuleVo.getStartTimeSite());
                                    target.setEndTimeSite(targetRuleVo.getEndTimeSite());
                                    target.setBiddingType(targetRuleVo.getBiddingType());
                                    target.setBiddingValue(targetRuleVo.getBiddingValue().doubleValue());
                                    if (targetRuleVo.getBiddingMaxValue() != null) {
                                        target.setBiddingMaxValue(targetRuleVo.getBiddingMaxValue().doubleValue());
                                    }
                                    if (targetRuleVo.getBiddingMinValue() != null) {
                                        target.setBiddingMinValue(targetRuleVo.getBiddingMinValue().doubleValue());
                                    }
                                    MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(strategyTemplate.getMarketplaceId());
                                    if(null != m){
                                        target.setSymbol(m.getCurrencyCode());
                                    }
                                    targetRuleBuilder.addTargets(target);
                                }
                                if (siteDate == 0) {
                                    targetRuleBuilder.setSiteDateName("每日");
                                } else {
                                    targetRuleBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                            cpc.util.Constants.getDateMap().get(siteDate));
                                }
                                targetRuleList.add(targetRuleBuilder.build());
                            }
                            builder.addAllTargetRuleList(targetRuleList);
                        } else if (AdItemType.START_STOP.name().equals(strategyTemplate.getItemType())) {
                            List<StartStopRuleVo> startStopRuleVoList = JSONUtil.jsonToArray(strategyTemplate.getRule(), StartStopRuleVo.class);
                            List<StartStopStateRule> startStopStateRules = Lists.newArrayList();
                            Map<Integer,List<StartStopRuleVo>> map = startStopRuleVoList.stream().
                                    collect(Collectors.groupingBy(StartStopRuleVo::getSiteDate));
                            for (Integer siteDate : map.keySet()) {
                                StartStopStateRule.Builder startStopBuilder = StartStopStateRule.newBuilder();
                                startStopBuilder.setSiteDate(siteDate);
                                if (siteDate == 0) {
                                    startStopBuilder.setSiteDateName("每日");
                                } else {
                                    startStopBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                            cpc.util.Constants.getDateMap().get(siteDate));
                                }
                                for (StartStopRuleVo startStopRuleVo : map.get(siteDate)) {
                                    StateRule.Builder stateBuilder = StateRule.newBuilder();
                                    stateBuilder.setStartTimeSite(startStopRuleVo.getStartTimeSite());
                                    stateBuilder.setEndTimeSite(startStopRuleVo.getEndTimeSite());
                                    stateBuilder.setState(startStopRuleVo.getState());
                                    startStopBuilder.addStateRules(stateBuilder);
                                }
                                startStopStateRules.add(startStopBuilder.build());
                            }
                            builder.addAllStartStopStateRuleList(startStopStateRules);
                        } else if (AdItemType.PORTFOLIO.name().equals(strategyTemplate.getItemType())) {
                            if (PbUtil.isPortfolioHour(strategyTemplate.getChildrenItemType())) {
                                builder.setReturnValue(PbUtil.buildPortfolioByJson(strategyTemplate.getReturnValue()));
                                builder.addAllPortfolioHourRuleList(PbUtil.buildPortfolioHourRuleByJson(strategyTemplate.getRule(), strategyTemplate.getMarketplaceId()));
                                builder.setChildrenItemType(strategyTemplate.getChildrenItemType());
                            } else {
                                List<PortfolioRuleVo> portfolioRuleList = JSONUtil.jsonToArray(strategyTemplate.getRule(), PortfolioRuleVo.class);
                                List<PortfolioRule> portfolioRules = Lists.newArrayList();
                                Map<Integer,List<PortfolioRuleVo>> map = new LinkedHashMap<>();
                                Map<Integer,List<PortfolioRuleVo>> listMap = new LinkedHashMap<>();
                                if ("MONTH_DAY".equals(strategyTemplate.getType())) {
                                    portfolioRuleList.stream().collect(Collectors.groupingBy(e->
                                                    Integer.valueOf(LocalDate.parse(e.getStartDate(),DateTimeFormatter.ofPattern(DateUtil.PATTERN)).
                                                            format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD))))).entrySet().stream()
                                            .sorted(Map.Entry.comparingByKey()).forEachOrdered(x->listMap.put(x.getKey(),x.getValue()));
                                } else {
                                    map = portfolioRuleList.stream().collect(Collectors.groupingBy(PortfolioRuleVo::getSiteDate));
                                }
                                if (MapUtils.isNotEmpty(map)) {
                                    for (Integer siteDate : map.keySet()) {
                                        PortfolioRule.Builder portfolioBuilder = PortfolioRule.newBuilder();
                                        portfolioBuilder.setSiteDate(siteDate);
                                        if (siteDate == 0) {
                                            portfolioBuilder.setSiteDateName("每日");
                                        } else {
                                            portfolioBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                                    cpc.util.Constants.getDateMap().get(siteDate));
                                        }
                                        for (PortfolioRuleVo portfolioRuleVo : map.get(siteDate)) {
                                            Portfolio.Builder portfolio = Portfolio.newBuilder();
                                            if (!"noBudget".equals(portfolioRuleVo.getAmount())) {
                                                portfolio.setAmount(portfolioRuleVo.getAmount().doubleValue());
                                            }
                                            portfolio.setPolicy(portfolioRuleVo.getPolicy());
                                            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(strategyTemplate.getMarketplaceId());
                                            if(null != m){
                                                portfolio.setSymbol(m.getCurrencyCode());
                                            }
                                            portfolioBuilder.addPortfolios(portfolio.build());
                                        }
                                        portfolioRules.add(portfolioBuilder.build());
                                    }
                                }
                                if (MapUtils.isNotEmpty(listMap)) {
                                    for (Integer countDate : listMap.keySet()) {
                                        PortfolioRule.Builder portfolioBuilder = PortfolioRule.newBuilder();
                                        portfolioBuilder.setStartDate(listMap.get(countDate).get(0).getStartDate());
                                        portfolioBuilder.setEndDate(listMap.get(countDate).get(0).getEndDate());
                                        for (PortfolioRuleVo portfolioRuleVo : listMap.get(countDate)) {
                                            Portfolio.Builder portfolio = Portfolio.newBuilder();
                                            if (!"noBudget".equals(portfolioRuleVo.getAmount())) {
                                                portfolio.setAmount(portfolioRuleVo.getAmount().doubleValue());
                                            }
                                            portfolio.setPolicy(portfolioRuleVo.getPolicy());
                                            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(strategyTemplate.getMarketplaceId());
                                            if(null != m){
                                                portfolio.setSymbol(m.getCurrencyCode());
                                            }
                                            portfolioBuilder.addPortfolios(portfolio.build());
                                        }
                                        portfolioRules.add(portfolioBuilder.build());
                                    }
                                }
                                builder.addAllPortfolioRuleList(portfolioRules);
                            }
                        }
                        builder.setMarketplaceName(strategyTemplate.getMarketplaceName());
                        builder.setType(strategyTemplate.getType());
                        builder.setItemType(strategyTemplate.getItemType());
                        builder.setTemplateName(strategyTemplate.getTemplateName());
                        builder.setUsageAmount(Optional.ofNullable(strategyTemplate.getUsageAmount()).orElse(0));
                        builder.setExecutePreview(Optional.ofNullable(strategyTemplate.getExecutePreview()).orElse(0));
                        builder.setStatus(strategyTemplate.getStatus());
                        if (strategyTemplate.getCreateUid() != null) {
                            builder.setCreateUid(strategyTemplate.getCreateUid());
                        }
                        if (strategyTemplate.getUpdateUid() != null) {
                            builder.setUpdateUid(strategyTemplate.getUpdateUid());
                        }
                        if (StringUtils.isNotBlank(strategyTemplate.getCreateName())) {
                            builder.setCreateName(strategyTemplate.getCreateName());
                        }
                        if (StringUtils.isNotBlank(strategyTemplate.getUpdateName())) {
                            builder.setUpdateName(strategyTemplate.getUpdateName());
                        }
                        builder.setCreateAt(strategyTemplate.getCreateAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        builder.setLastUpdateAt(strategyTemplate.getLastUpdateAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        list.add(builder.build());
                    }
                    pageBuilder.addAllRows(list);
                    responseBuilder.setCode(Result.SUCCESS);
                    responseBuilder.setData(pageBuilder.build());
                }
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void addTemplate(AdStrategyTemplateAddRequest request, StreamObserver<AdStrategyTemplateAddResponse> responseObserver) {
        log.info("模板 addTemplate request {}", request);
        AdStrategyTemplateAddResponse.Builder responseBuilder = AdStrategyTemplateAddResponse.newBuilder();
        //检验参数
        if (!request.hasPuid()  || !request.hasCreateUid() || !request.hasShopId() || StringUtils.isBlank(request.getTemplateName())) {
            responseBuilder.setMsg("请求参数错误");
            responseBuilder.setCode(Result.ERROR);
        } else {
            //拼接插入参数
            AdvertiseStrategyTemplate template = new AdvertiseStrategyTemplate();
            template.setPuid(request.getPuid());
            template.setShopId(request.getShopId());
            template.setMarketplaceId(request.getMarketplaceId());
            template.setTemplateName(request.getTemplateName());
            template.setType(request.getType());
            template.setItemType(request.getItemType());
            if (AdItemType.CAMPAIGN.name().equals(request.getItemType())) {
                List<CampaignRule> campaignRuleList = request.getCampaignRuleListList().stream()
                        .sorted(Comparator.comparing(CampaignRule::getSiteDate)).collect(Collectors.toList());
                List<CampaignRuleVo> campaignRuleVoList = Lists.newArrayListWithExpectedSize(campaignRuleList.size());
                for (CampaignRule campaignRule : campaignRuleList) {
                    List<Campaign> campaignList = campaignRule.getCampaignsList().
                            stream().sorted(Comparator.comparing(Campaign::getStartTimeSite)).collect(Collectors.toList());
                    for (Campaign campaign : campaignList) {
                        CampaignRuleVo campaignRuleVo = new CampaignRuleVo();
                        campaignRuleVo.setSiteDate(campaignRule.getSiteDate());
                        campaignRuleVo.setStartTimeSite(campaign.getStartTimeSite());
                        campaignRuleVo.setEndTimeSite(campaign.getEndTimeSite());
                        campaignRuleVo.setBudgetValue(BigDecimal.valueOf(campaign.getBudgetValue()));
                        if (StrategyChildrenItemTypeEnum.NUMERICAL.getValue().equals(request.getChildrenItemType())) {
                            campaignRuleVo.setBudgetType(campaign.getBudgetType());
                        }
                        campaignRuleVoList.add(campaignRuleVo);
                    }
                }
                String json = JSONUtil.objectToJson(campaignRuleVoList);
                template.setRule(json);
                template.setChildrenItemType(request.getChildrenItemType());
            } else if (AdItemType.CAMPAIGN_PLACEMENT.name().equals(request.getItemType())) {
                List<CampaignPlacementRule> campaignPlacementRuleList = request.getCampaignPlacementRuleListList()
                        .stream().sorted(Comparator.comparing(CampaignPlacementRule::getSiteDate)).collect(Collectors.toList());
                List<CampaignPlacementRuleVo> campaignPlacementRuleVoList = Lists.
                        newArrayListWithExpectedSize(campaignPlacementRuleList.size());
                for (CampaignPlacementRule campaignPlacementRule : campaignPlacementRuleList) {
                    List<CampaignPlacement> campaignPlacementList = campaignPlacementRule.getCampaignPlacementsList()
                            .stream().sorted(Comparator.comparing(CampaignPlacement::getStartTimeSite)).collect(Collectors.toList());
                    for (CampaignPlacement campaignPlacement:campaignPlacementList) {
                        CampaignPlacementRuleVo campaignPlacementRuleVo = new CampaignPlacementRuleVo();
                        campaignPlacementRuleVo.setSiteDate(campaignPlacementRule.getSiteDate());
                        campaignPlacementRuleVo.setStartTimeSite(campaignPlacement.getStartTimeSite());
                        campaignPlacementRuleVo.setEndTimeSite(campaignPlacement.getEndTimeSite());
                        if (campaignPlacement.hasStrategy()) {
                            campaignPlacementRuleVo.setStrategy(campaignPlacement.getStrategy());
                        }
                        if (campaignPlacement.hasAdPlaceProductType()) {
                            campaignPlacementRuleVo.setAdPlaceProductType(campaignPlacement.getAdPlaceProductType());
                        }
                        if (campaignPlacement.hasAdPlaceProductValue()) {
                            campaignPlacementRuleVo.setAdPlaceProductValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceProductValue()));
                        }
                        if (campaignPlacement.hasAdPlaceTopType()) {
                            campaignPlacementRuleVo.setAdPlaceTopType(campaignPlacement.getAdPlaceTopType());
                        }
                        if (campaignPlacement.hasAdPlaceTopMaxValue()) {
                            campaignPlacementRuleVo.setAdPlaceTopMaxValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceTopMaxValue()));
                        }
                        if (campaignPlacement.hasAdPlaceTopMinValue()) {
                            campaignPlacementRuleVo.setAdPlaceTopMinValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceTopMinValue()));
                        }
                        if (campaignPlacement.hasAdPlaceProductMaxValue()) {
                            campaignPlacementRuleVo.setAdPlaceProductMaxValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceProductMaxValue()));
                        }
                        if (campaignPlacement.hasAdPlaceProductMinValue()) {
                            campaignPlacementRuleVo.setAdPlaceProductMinValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceProductMinValue()));
                        }
                        if (campaignPlacement.hasAdPlaceTopValue()) {
                            campaignPlacementRuleVo.setAdPlaceTopValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceTopValue()));
                        }
                        if (campaignPlacement.hasAdOtherType()) {
                            campaignPlacementRuleVo.setAdOtherType(campaignPlacement.getAdOtherType());
                        }
                        if (campaignPlacement.hasAdOtherValue()) {
                            campaignPlacementRuleVo.setAdOtherValue(BigDecimal.valueOf(campaignPlacement.getAdOtherValue()));
                        }
                        campaignPlacementRuleVoList.add(campaignPlacementRuleVo);
                    }
                }
                String json = JSONUtil.objectToJson(campaignPlacementRuleVoList);
                template.setRule(json);
            } else if (AdItemType.TARGET.name().equals(request.getItemType())){
                List<TargetRule> targetRuleList = request.getTargetRuleListList()
                        .stream().sorted(Comparator.comparing(TargetRule::getSiteDate)).collect(Collectors.toList());
                List<TargetRuleVo> targetRuleVoList = Lists.newArrayListWithExpectedSize(targetRuleList.size());
                for (TargetRule targetRule : targetRuleList) {
                    List<Target> targetList = targetRule.getTargetsList()
                            .stream().sorted(Comparator.comparing(Target::getStartTimeSite)).collect(Collectors.toList());
                    for (Target target : targetList) {
                        TargetRuleVo targetRuleVo = new TargetRuleVo();
                        targetRuleVo.setSiteDate(targetRule.getSiteDate());
                        targetRuleVo.setStartTimeSite(target.getStartTimeSite());
                        targetRuleVo.setEndTimeSite(target.getEndTimeSite());
                        targetRuleVo.setBiddingType(target.getBiddingType());
                        targetRuleVo.setBiddingValue(BigDecimal.valueOf(target.getBiddingValue()));
                        if (target.hasBiddingMaxValue()) {
                            targetRuleVo.setBiddingMaxValue(BigDecimal.valueOf(target.getBiddingMaxValue()));
                        }
                        if (target.hasBiddingMinValue()) {
                            targetRuleVo.setBiddingMinValue(BigDecimal.valueOf(target.getBiddingMinValue()));
                        }
                        targetRuleVoList.add(targetRuleVo);
                    }
                }
                String json = JSONUtil.objectToJson(targetRuleVoList);
                template.setRule(json);
            } else if (AdItemType.START_STOP.name().equals(request.getItemType())) {
                List<StartStopStateRule> list = request.getStartStopStateRuleListList();
                List<StartStopStateRule> startStopStateRuleList = Lists.newArrayList();
                startStopStateRuleList.addAll(list);
                if ("WEEKLY".equals(request.getType())) {
                    List<Integer> dayList = startStopStateRuleList.stream().map(StartStopStateRule::getSiteDate).distinct().
                            collect(Collectors.toList());
                    for (int i = 1;i < 8;i++) {
                        if (!dayList.contains(i)) {
                            StartStopStateRule.Builder startStopBuilder = StartStopStateRule.newBuilder();
                            startStopBuilder.setSiteDate(i);
                            StateRule.Builder stopRuleBuilder = StateRule.newBuilder();
                            stopRuleBuilder.setStartTimeSite(0);
                            stopRuleBuilder.setEndTimeSite(24);
                            stopRuleBuilder.setState("paused");
                            startStopBuilder.addStateRules(stopRuleBuilder.build());
                            startStopStateRuleList.add(startStopBuilder.build());
                        }
                    }
                }
                startStopStateRuleList =  startStopStateRuleList.stream().sorted(Comparator.comparing(StartStopStateRule::getSiteDate)).collect(Collectors.toList());
                List<StartStopRuleVo> startStopRuleVoList = Lists.newArrayListWithExpectedSize(startStopStateRuleList.size());
                for (StartStopStateRule startStopStateRule : startStopStateRuleList) {
                    List<StateRule> stateRuleList = startStopStateRule.getStateRulesList()
                            .stream().sorted(Comparator.comparing(StateRule::getStartTimeSite)).collect(Collectors.toList());
                    Map<Integer,Integer> timeMap = stateRuleList.stream().
                            collect(Collectors.toMap(StateRule::getStartTimeSite,StateRule::getEndTimeSite));
                    List<Integer> startList = stateRuleList.stream().map(StateRule::getStartTimeSite).
                            collect(Collectors.toList());
                    Map<Integer,Integer> newTimeMap = getNewTimeMap(startList,timeMap);
                    if (MapUtils.isNotEmpty(newTimeMap)) {
                        for (Integer startTime : newTimeMap.keySet()) {
                            StateRule.Builder stopRuleBuilder = StateRule.newBuilder();
                            stopRuleBuilder.setStartTimeSite(startTime);
                            stopRuleBuilder.setEndTimeSite(newTimeMap.get(startTime));
                            stopRuleBuilder.setState("paused");
                            stateRuleList.add(stopRuleBuilder.build());
                        }
                        //获取暂停时间段后再度排序
                        stateRuleList = stateRuleList.stream().sorted(Comparator.comparing(StateRule::getStartTimeSite)).collect(Collectors.toList());
                    }

                    for (StateRule stateRule : stateRuleList) {
                        StartStopRuleVo startStopRuleVo = new StartStopRuleVo();
                        startStopRuleVo.setSiteDate(startStopStateRule.getSiteDate());
                        startStopRuleVo.setStartTimeSite(stateRule.getStartTimeSite());
                        startStopRuleVo.setEndTimeSite(stateRule.getEndTimeSite());
                        startStopRuleVo.setState(stateRule.getState());
                        startStopRuleVoList.add(startStopRuleVo);
                    }
                }
                String json = JSONUtil.objectToJson(startStopRuleVoList);
                template.setRule(json);

            } else if (AdItemType.PORTFOLIO.name().equals(request.getItemType())) {
                // 处理小时级广告组合
                if (PbUtil.isPortfolioHour(request.getChildrenItemType())) {
                    this.dealPortfolioHour(request.getPortfolioHourRuleListList(), request.getChildrenItemType(), request.getReturnValue(), template);
                } else {
                    List<PortfolioRule> portfolioRuleList = request.getPortfolioRuleListList();
                    List<PortfolioRuleVo> portfolioRuleVoList = Lists.newArrayListWithExpectedSize(portfolioRuleList.size());
                    for (PortfolioRule portfolioRule : portfolioRuleList) {
                        for (Portfolio portfolio : portfolioRule.getPortfoliosList()) {
                            PortfolioRuleVo portfolioRuleVo = new PortfolioRuleVo();
                            if (portfolioRule.hasSiteDate()) {
                                portfolioRuleVo.setSiteDate(portfolioRule.getSiteDate());
                            }
                            if (portfolioRule.hasStartDate()) {
                                portfolioRuleVo.setStartDate(portfolioRule.getStartDate());
                            }
                            if (portfolioRule.hasEndDate()) {
                                portfolioRuleVo.setEndDate(portfolioRule.getEndDate());
                            }
                            if (portfolio.hasAmount()) {
                                portfolioRuleVo.setAmount(BigDecimal.valueOf(portfolio.getAmount()));
                            }
                            if (portfolio.hasPolicy()) {
                                portfolioRuleVo.setPolicy(portfolio.getPolicy());
                            }
                            portfolioRuleVoList.add(portfolioRuleVo);
                        }
                    }
                    if (AdStrategyType.MONTH_DAY.name().equals(request.getType())) {
                        portfolioRuleVoList = portfolioRuleVoList.stream().sorted(Comparator.comparing(PortfolioRuleVo::getStartDate)).collect(Collectors.toList());
                    } else {
                        portfolioRuleVoList = portfolioRuleVoList.stream().sorted(Comparator.comparing(PortfolioRuleVo::getSiteDate)).collect(Collectors.toList());
                    }
                    String json = JSONUtil.objectToJson(portfolioRuleVoList);
                    template.setRule(json);
                    OriginValueVo valueVo = new OriginValueVo();
                    valueVo.setAmount(BigDecimal.valueOf(request.getReturnValue().getAmount()));
                    valueVo.setPolicy(request.getReturnValue().getPolicy());
                    template.setReturnValue(JSONUtil.objectToJson(valueVo));
                }
            }
            template.setVersion(1);
            template.setCreateAt(LocalDateTime.now());
            template.setCreateUid(request.getCreateUid());
            template.setCreateName(request.getCreateName());
            template.setUpdateUid(request.getUpdateUid());
            template.setUpdateName(request.getUpdateName());
            template.setStatus(AdStrategyEnableStatusEnum.ENABLED.getCode());
            Result<Long> result = strategyTemplateService.insertTemplate(request.getPuid(), template, request.getLoginIp(), request.getTraceId());
            if (result.getCode() == Result.ERROR) {
                responseBuilder.setMsg(result.getMsg());
                responseBuilder.setCode(result.getCode());
            } else {
                responseBuilder.setMsg("新增模板:" + result.getData());
                responseBuilder.setCode(Result.SUCCESS);
                responseBuilder.setData(result.getData());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }


    /**
     * 重复代码 可抽离出指定对象
     */
    private void dealPortfolioHour(List<PortfolioHourRule> portfolioHourRules, String childrenItemType, Portfolio portfolio, AdvertiseStrategyTemplate template) {
        portfolioHourRules = portfolioHourRules.stream().sorted(Comparator.comparing(PortfolioHourRule::getSiteDate)).collect(Collectors.toList());
        List<PortfolioHourRuleVo> portfolioHourRuleVos = Lists.newArrayListWithExpectedSize(portfolioHourRules.size());
        for (PortfolioHourRule portfolioHourRule : portfolioHourRules) {
            List<Portfolio> portfolioHours = portfolioHourRule.getPortfoliosList().
                    stream().sorted(Comparator.comparing(Portfolio::getStartTimeSite)).collect(Collectors.toList());
            for (Portfolio portfolioHour : portfolioHours) {
                PortfolioHourRuleVo portfolioHourRuleVo = new PortfolioHourRuleVo();
                portfolioHourRuleVo.setSiteDate(portfolioHourRule.getSiteDate());
                portfolioHourRuleVo.setStartTimeSite(portfolioHour.getStartTimeSite());
                portfolioHourRuleVo.setEndTimeSite(portfolioHour.getEndTimeSite());
                portfolioHourRuleVo.setAmount(BigDecimal.valueOf(portfolioHour.getAmount()));
                portfolioHourRuleVo.setPolicy(portfolioHour.getPolicy());
                portfolioHourRuleVos.add(portfolioHourRuleVo);
            }
        }
        String json = JSONUtil.objectToJson(portfolioHourRuleVos);
        // 设置规则
        template.setRule(json);
        // 设置子类型 DAILY 或者 WEEKLY
        template.setChildrenItemType(childrenItemType);
        OriginValueVo valueVo = new OriginValueVo();
        valueVo.setAmount(BigDecimal.valueOf(portfolio.getAmount()));
        valueVo.setPolicy(portfolio.getPolicy());
        // 设置空闲值
        template.setReturnValue(JSONUtil.objectToJson(valueVo));
    }

    @Override
    public void updateTemplate(AdStrategyTemplateUpdateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("模板 updateTemplate request {}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        //检验参数
        if (!request.hasPuid() || !request.hasUpdateUid() || StringUtils.isBlank(request.getTemplateName())) {
            responseBuilder.setMsg("请求参数错误");
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
        } else {
            //拼接插入参数
            AdvertiseStrategyTemplate template = new AdvertiseStrategyTemplate();
            template.setId(request.getId());
            template.setPuid(request.getPuid());
            template.setId(request.getId());
            template.setType(request.getType());
            List<Rule> ruleList = request.getRulesList();
            if ("CAMPAIGN".equals(request.getItemType())) {
                List<CampaignRule> campaignRuleList = request.getCampaignRuleListList().stream()
                        .sorted(Comparator.comparing(CampaignRule::getSiteDate)).collect(Collectors.toList());
                List<CampaignRuleVo> campaignRuleVoList = Lists.newArrayListWithExpectedSize(campaignRuleList.size());
                for (CampaignRule campaignRule : campaignRuleList) {
                    List<Campaign> campaignList = campaignRule.getCampaignsList().
                            stream().sorted(Comparator.comparing(Campaign::getStartTimeSite)).collect(Collectors.toList());
                    for (Campaign campaign : campaignList) {
                        CampaignRuleVo campaignRuleVo = new CampaignRuleVo();
                        campaignRuleVo.setSiteDate(campaignRule.getSiteDate());
                        campaignRuleVo.setStartTimeSite(campaign.getStartTimeSite());
                        campaignRuleVo.setEndTimeSite(campaign.getEndTimeSite());
                        campaignRuleVo.setBudgetValue(BigDecimal.valueOf(campaign.getBudgetValue()));
                        if ("numerical".equals(request.getChildrenItemType())) {
                            campaignRuleVo.setBudgetType(campaign.getBudgetType());
                        }
                        campaignRuleVoList.add(campaignRuleVo);
                    }
                }
                String json = JSONUtil.objectToJson(campaignRuleVoList);
                template.setRule(json);
                template.setChildrenItemType(request.getChildrenItemType());
            } else if ("CAMPAIGN_PLACEMENT".equals(request.getItemType())) {
                List<CampaignPlacementRule> campaignPlacementRuleList = request.getCampaignPlacementRuleListList()
                         .stream().sorted(Comparator.comparing(CampaignPlacementRule::getSiteDate)).collect(Collectors.toList());
                List<CampaignPlacementRuleVo> campaignPlacementRuleVoList = Lists.
                        newArrayListWithExpectedSize(campaignPlacementRuleList.size());
                for (CampaignPlacementRule campaignPlacementRule : campaignPlacementRuleList) {
                    List<CampaignPlacement> campaignPlacementList = campaignPlacementRule.getCampaignPlacementsList()
                            .stream().sorted(Comparator.comparing(CampaignPlacement::getStartTimeSite)).collect(Collectors.toList());
                    for (CampaignPlacement campaignPlacement : campaignPlacementList) {
                        CampaignPlacementRuleVo campaignPlacementRuleVo = new CampaignPlacementRuleVo();
                        campaignPlacementRuleVo.setSiteDate(campaignPlacementRule.getSiteDate());
                        campaignPlacementRuleVo.setStartTimeSite(campaignPlacement.getStartTimeSite());
                        campaignPlacementRuleVo.setEndTimeSite(campaignPlacement.getEndTimeSite());
                        if (campaignPlacement.hasStrategy()) {
                            campaignPlacementRuleVo.setStrategy(campaignPlacement.getStrategy());
                        }
                        if (campaignPlacement.hasAdPlaceProductType()) {
                            campaignPlacementRuleVo.setAdPlaceProductType(campaignPlacement.getAdPlaceProductType());
                        }
                        if (campaignPlacement.hasAdPlaceProductValue()) {
                            campaignPlacementRuleVo.setAdPlaceProductValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceProductValue()));
                        }
                        if (campaignPlacement.hasAdPlaceTopType()) {
                            campaignPlacementRuleVo.setAdPlaceTopType(campaignPlacement.getAdPlaceTopType());
                        }
                        if (campaignPlacement.hasAdPlaceTopMaxValue()) {
                            campaignPlacementRuleVo.setAdPlaceTopMaxValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceTopMaxValue()));
                        }
                        if (campaignPlacement.hasAdPlaceTopMinValue()) {
                            campaignPlacementRuleVo.setAdPlaceTopMinValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceTopMinValue()));
                        }
                        if (campaignPlacement.hasAdPlaceProductMaxValue()) {
                            campaignPlacementRuleVo.setAdPlaceProductMaxValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceProductMaxValue()));
                        }
                        if (campaignPlacement.hasAdPlaceProductMinValue()) {
                            campaignPlacementRuleVo.setAdPlaceProductMinValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceProductMinValue()));
                        }
                        if (campaignPlacement.hasAdPlaceTopValue()) {
                            campaignPlacementRuleVo.setAdPlaceTopValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceTopValue()));
                        }
                        if (campaignPlacement.hasAdOtherType()) {
                            campaignPlacementRuleVo.setAdOtherType(campaignPlacement.getAdOtherType());
                        }
                        if (campaignPlacement.hasAdOtherValue()) {
                            campaignPlacementRuleVo.setAdOtherValue(BigDecimal.valueOf(campaignPlacement.getAdOtherValue()));
                        }
                        campaignPlacementRuleVoList.add(campaignPlacementRuleVo);
                    }
                }
                String json = JSONUtil.objectToJson(campaignPlacementRuleVoList);
                template.setRule(json);
            } else if ("TARGET".equals(request.getItemType())){
                List<TargetRule> targetRuleList = request.getTargetRuleListList()
                        .stream().sorted(Comparator.comparing(TargetRule::getSiteDate)).collect(Collectors.toList());
                List<TargetRuleVo> targetRuleVoList = Lists.newArrayListWithExpectedSize(targetRuleList.size());
                for (TargetRule targetRule : targetRuleList) {
                    List<Target> targetList = targetRule.getTargetsList()
                            .stream().sorted(Comparator.comparing(Target::getStartTimeSite)).collect(Collectors.toList());
                    for (Target target : targetList) {
                        TargetRuleVo targetRuleVo = new TargetRuleVo();
                        targetRuleVo.setSiteDate(targetRule.getSiteDate());
                        targetRuleVo.setStartTimeSite(target.getStartTimeSite());
                        targetRuleVo.setEndTimeSite(target.getEndTimeSite());
                        targetRuleVo.setBiddingType(target.getBiddingType());
                        targetRuleVo.setBiddingValue(BigDecimal.valueOf(target.getBiddingValue()));
                        if (target.hasBiddingMaxValue()) {
                            targetRuleVo.setBiddingMaxValue(BigDecimal.valueOf(target.getBiddingMaxValue()));
                        }
                        if (target.hasBiddingMinValue()) {
                            targetRuleVo.setBiddingMinValue(BigDecimal.valueOf(target.getBiddingMinValue()));
                        }
                        targetRuleVoList.add(targetRuleVo);
                    }
                }
                String json = JSONUtil.objectToJson(targetRuleVoList);
                template.setRule(json);
            } else if ("START_STOP".equals(request.getItemType())) {
                List<StartStopStateRule> list = request.getStartStopStateRuleListList();
                List<StartStopStateRule> startStopStateRuleList = Lists.newArrayList();
                startStopStateRuleList.addAll(list);
                if ("WEEKLY".equals(request.getType())) {
                    List<Integer> dayList = startStopStateRuleList.stream().map(StartStopStateRule::getSiteDate).distinct().
                            collect(Collectors.toList());
                    for (int i = 1;i < 8;i++) {
                        if (!dayList.contains(i)) {
                            StartStopStateRule.Builder startStopBuilder = StartStopStateRule.newBuilder();
                            startStopBuilder.setSiteDate(i);
                            StateRule.Builder stateRuleBuilder = StateRule.newBuilder();
                            stateRuleBuilder.setStartTimeSite(0);
                            stateRuleBuilder.setEndTimeSite(24);
                            stateRuleBuilder.setState("paused");
                            startStopBuilder.addStateRules(stateRuleBuilder);
                            startStopStateRuleList.add(startStopBuilder.build());
                        }
                    }
                }
                startStopStateRuleList =  startStopStateRuleList.stream().sorted(Comparator.comparing(StartStopStateRule::getSiteDate)).collect(Collectors.toList());
                List<StartStopRuleVo> startStopRuleVoList = Lists.newArrayListWithExpectedSize(startStopStateRuleList.size());
                for (StartStopStateRule startStopStateRule : startStopStateRuleList) {
                    List<StateRule> stateRuleList = startStopStateRule.getStateRulesList()
                            .stream().sorted(Comparator.comparing(StateRule::getStartTimeSite)).collect(Collectors.toList());
                    Map<Integer,Integer> timeMap = stateRuleList.stream().
                            collect(Collectors.toMap(StateRule::getStartTimeSite,StateRule::getEndTimeSite));
                    List<Integer> startList = stateRuleList.stream().map(StateRule::getStartTimeSite).
                            collect(Collectors.toList());
                    Map<Integer,Integer> newTimeMap = getNewTimeMap(startList,timeMap);
                    if (MapUtils.isNotEmpty(newTimeMap)) {
                        for (Integer startTime : newTimeMap.keySet()) {
                            StateRule.Builder stopRuleBuilder = StateRule.newBuilder();
                            stopRuleBuilder.setStartTimeSite(startTime);
                            stopRuleBuilder.setEndTimeSite(newTimeMap.get(startTime));
                            stopRuleBuilder.setState("paused");
                            stateRuleList.add(stopRuleBuilder.build());
                        }
                        //获取暂停时间段后再度排序
                        stateRuleList = stateRuleList.stream().sorted(Comparator.comparing(StateRule::getStartTimeSite)).collect(Collectors.toList());
                    }

                    for (StateRule stateRule : stateRuleList) {
                        StartStopRuleVo startStopRuleVo = new StartStopRuleVo();
                        startStopRuleVo.setSiteDate(startStopStateRule.getSiteDate());
                        startStopRuleVo.setStartTimeSite(stateRule.getStartTimeSite());
                        startStopRuleVo.setEndTimeSite(stateRule.getEndTimeSite());
                        startStopRuleVo.setState(stateRule.getState());
                        startStopRuleVoList.add(startStopRuleVo);
                    }
                }
                String json = JSONUtil.objectToJson(startStopRuleVoList);
                template.setRule(json);
            } else if ("PORTFOLIO".equals(request.getItemType())) {
                if (PbUtil.isPortfolioHour(request.getChildrenItemType())) {
                    dealPortfolioHour(request.getPortfolioHourRuleListList(), request.getChildrenItemType(), request.getReturnValue(), template);
                } else {
                    List<PortfolioRule> portfolioRuleList = request.getPortfolioRuleListList();
                    List<PortfolioRuleVo> portfolioRuleVoList = Lists.newArrayListWithExpectedSize(portfolioRuleList.size());
                    for (PortfolioRule portfolioRule : portfolioRuleList) {
                        for (Portfolio portfolio : portfolioRule.getPortfoliosList()) {
                            PortfolioRuleVo portfolioRuleVo = new PortfolioRuleVo();
                            if (portfolioRule.hasSiteDate()) {
                                portfolioRuleVo.setSiteDate(portfolioRule.getSiteDate());
                            }
                            if (portfolioRule.hasStartDate()) {
                                portfolioRuleVo.setStartDate(portfolioRule.getStartDate());
                            }
                            if (portfolioRule.hasEndDate()) {
                                portfolioRuleVo.setEndDate(portfolioRule.getEndDate());
                            }
                            if (portfolio.hasAmount()) {
                                portfolioRuleVo.setAmount(BigDecimal.valueOf(portfolio.getAmount()));
                            }
                            if (portfolio.hasPolicy()) {
                                portfolioRuleVo.setPolicy(portfolio.getPolicy());
                            }
                            portfolioRuleVoList.add(portfolioRuleVo);
                        }
                    }
                    if ("MONTH_DAY".equals(request.getType())) {
                        portfolioRuleVoList = portfolioRuleVoList.stream().sorted(Comparator.comparing(PortfolioRuleVo::getStartDate)).collect(Collectors.toList());
                    } else {
                        portfolioRuleVoList = portfolioRuleVoList.stream().sorted(Comparator.comparing(PortfolioRuleVo::getSiteDate)).collect(Collectors.toList());
                    }
                    String json = JSONUtil.objectToJson(portfolioRuleVoList);
                    template.setRule(json);
                    OriginValueVo valueVo = new OriginValueVo();
                    valueVo.setAmount(BigDecimal.valueOf(request.getReturnValue().getAmount()));
                    valueVo.setPolicy(request.getReturnValue().getPolicy());
                    template.setReturnValue(JSONUtil.objectToJson(valueVo));
                }
            }
            template.setLastUpdateAt(LocalDateTime.now());
            template.setUpdateUid(request.getUpdateUid());
            template.setUpdateName(request.getUpdateName());
            template.setTemplateName(request.getTemplateName());
            template.setItemType(request.getItemType());
            Result<Long> result = strategyTemplateService.updateTemplate(request.getPuid(), template, request.getLoginIp(), request.getTraceId());
            if (result.getCode() == Result.ERROR) {
                responseBuilder.setMsg(result.getMsg());
                responseBuilder.setCode(Int32Value.of(result.getCode()));
            } else {
                responseBuilder.setMsg("模板更新成功");
                responseBuilder.setData(result.getData().toString());
                responseBuilder.setCode(Int32Value.of(Result.SUCCESS));
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     * 时间截取
     *
     * @param timeMap
     * @return
     */
    private Map<Integer,Integer> getNewTimeMap(List<Integer> startTimeList,Map<Integer,Integer> timeMap){
        log.info("模板设置时间 request {}",timeMap);
        int statTime = 0;
        int endTime = 24;
        Map<Integer,Integer> newTimeMap = Maps.newHashMap();
        for (int i = 0;i<startTimeList.size();i++) {
            if (startTimeList.size() == 1) {
                if (startTimeList.get(i) == statTime && timeMap.get(startTimeList.get(i)) != endTime) {
                    newTimeMap.put(timeMap.get(startTimeList.get(i)), endTime);
                } else if (startTimeList.get(i) != statTime && timeMap.get(startTimeList.get(i)) == endTime) {
                    newTimeMap.put(statTime, startTimeList.get(i));
                } else if (startTimeList.get(i) != statTime && timeMap.get(startTimeList.get(i)) != endTime) {
                    newTimeMap.put(statTime, startTimeList.get(i));
                    newTimeMap.put(timeMap.get(startTimeList.get(i)), endTime);
                }
            } else {
                if (i == 0) {
                    if (!startTimeList.get(i).equals(statTime)) {
                        newTimeMap.put(statTime, startTimeList.get(i));
                    }
                    if (!timeMap.get(startTimeList.get(i)).equals(startTimeList.get(i + 1))) {
                        newTimeMap.put(timeMap.get(startTimeList.get(i)), startTimeList.get(i + 1));
                    }
                } else if (i == startTimeList.size() - 1 && !timeMap.get(startTimeList.get(i)).equals(endTime)) {
                    newTimeMap.put(timeMap.get(startTimeList.get(i)), endTime);
                } else if (i != 0 && i != startTimeList.size() - 1) {
                    if (!timeMap.get(startTimeList.get(i)).equals(startTimeList.get(i + 1))) {
                        newTimeMap.put(timeMap.get(startTimeList.get(i)), startTimeList.get(i + 1));
                    }
                }
            }
        }
        return newTimeMap;
    }

    @Override
    public void delTemplate(AdStrategyTemplateDelRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("模板 delTemplate request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasTemplateId() || !request.hasPuid()) {
            builder.setMsg("请求参数错误");
            builder.setCode(Int32Value.of(Result.ERROR));
        } else {
            Result<String> result = strategyTemplateService.deleteTemplate(request.getPuid(), request.getTemplateId(), request.getUid(), request.getLoginIp(), request.getTraceId());
            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getTemplateById(AdStrategyTemplateEditRequest request, StreamObserver<AdStrategyTemplateResponse> responseObserver) {
        log.info("模板 getTemplateById request {}", request);
        AdStrategyTemplateResponse.Builder builder = AdStrategyTemplateResponse.newBuilder();
        if (!request.hasTemplateId() || !request.hasPuid()) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {
            Result<AdvertiseStrategyTemplate> result = strategyTemplateService.getTemplateById(request.getPuid(), request.getTemplateId(), request.getMarketplaceId());
            if (result.getCode() == Result.SUCCESS) {
                AdvertiseStrategyTemplate advertiseStrategyTemplate = result.getData();
                if (advertiseStrategyTemplate != null) {
                    AdStrategyTemplateRpc.Builder advertiseStrategyTemplateEdit = AdStrategyTemplateRpc.newBuilder();
                    List<ShopAuth> shops = shopAuthDao.getListByPuid(request.getPuid());
                    Map<Integer, ShopAuth> shopAuthMap = null;
                    if (CollectionUtils.isNotEmpty(shops)) {
                        shopAuthMap = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
                    }
                    advertiseStrategyTemplateEdit.setShopId(advertiseStrategyTemplate.getShopId());
                    if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(advertiseStrategyTemplate.getShopId())) {
                        advertiseStrategyTemplateEdit.setShopName(shopAuthMap.get(advertiseStrategyTemplate.getShopId()).getName());
                    }
                    advertiseStrategyTemplateEdit.setId(advertiseStrategyTemplate.getId());
                    advertiseStrategyTemplateEdit.setPuid(advertiseStrategyTemplate.getPuid());
                    advertiseStrategyTemplateEdit.setMarketplaceId(advertiseStrategyTemplate.getMarketplaceId());
                    advertiseStrategyTemplateEdit.setTemplateName(advertiseStrategyTemplate.getTemplateName());
                    advertiseStrategyTemplateEdit.setType(advertiseStrategyTemplate.getType());
                    advertiseStrategyTemplateEdit.setItemType(advertiseStrategyTemplate.getItemType());
                    advertiseStrategyTemplateEdit.setRule(advertiseStrategyTemplate.getRule());
                    if ("CAMPAIGN".equals(advertiseStrategyTemplate.getItemType())) {
                        List<CampaignRuleVo> campaignRuleVoList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(),CampaignRuleVo.class);
                        List<CampaignRule> campaignRuleList = new ArrayList<>(campaignRuleVoList.size());
                        Map<Integer,List<CampaignRuleVo>> map = campaignRuleVoList.stream().
                                collect(Collectors.groupingBy(CampaignRuleVo::getSiteDate));
                        for (Integer siteDate : map.keySet()) {
                            CampaignRule.Builder campaignRuleBuilder = CampaignRule.newBuilder();
                            campaignRuleBuilder.setSiteDate(siteDate);
                            for (CampaignRuleVo campaignRuleVo : map.get(siteDate)) {
                                Campaign.Builder campaign = Campaign.newBuilder();
                                campaign.setBudgetValue(campaignRuleVo.getBudgetValue().doubleValue());
                                campaign.setStartTimeSite(campaignRuleVo.getStartTimeSite());
                                campaign.setEndTimeSite(campaignRuleVo.getEndTimeSite());
                                campaignRuleBuilder.addCampaigns(campaign);
                            }
                            campaignRuleList.add(campaignRuleBuilder.build());
                        }
                        advertiseStrategyTemplateEdit.addAllCampaignRuleList(campaignRuleList);
                    } else if ("CAMPAIGN_PLACEMENT".equals(advertiseStrategyTemplate.getItemType())) {
                        List<CampaignPlacementRuleVo> campaignPlacementRuleVoList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(),CampaignPlacementRuleVo.class);
                        List<CampaignPlacementRule> campaignPlacementRuleList = new ArrayList<>(campaignPlacementRuleVoList.size());
                        Map<Integer,List<CampaignPlacementRuleVo>> map = campaignPlacementRuleVoList.stream().
                                collect(Collectors.groupingBy(CampaignPlacementRuleVo::getSiteDate));
                        for (Integer siteDate : map.keySet()) {
                            CampaignPlacementRule.Builder campaignPlacementRuleBuilder = CampaignPlacementRule.newBuilder();
                            campaignPlacementRuleBuilder.setSiteDate(siteDate);
                            for (CampaignPlacementRuleVo campaignPlacementRuleVo : map.get(siteDate)) {
                                CampaignPlacement.Builder campaignPlacement = CampaignPlacement.newBuilder();
                                campaignPlacement.setAdPlaceProductType(campaignPlacementRuleVo.getAdPlaceProductType());
                                campaignPlacement.setStartTimeSite(campaignPlacementRuleVo.getStartTimeSite());
                                campaignPlacement.setEndTimeSite(campaignPlacementRuleVo.getEndTimeSite());
                                campaignPlacement.setAdPlaceProductValue(campaignPlacementRuleVo.getAdPlaceProductValue().doubleValue());
                                campaignPlacement.setAdPlaceTopMaxValue(campaignPlacementRuleVo.getAdPlaceTopMaxValue().doubleValue());
                                campaignPlacement.setAdPlaceTopType(campaignPlacementRuleVo.getAdPlaceTopType());
                                campaignPlacement.setAdPlaceTopValue(campaignPlacementRuleVo.getAdPlaceTopValue().doubleValue());
                                if (campaignPlacementRuleVo.getAdOtherType() != null) {
                                    campaignPlacement.setAdOtherType(campaignPlacementRuleVo.getAdOtherType());
                                } else {
                                    campaignPlacement.setAdOtherType(4);
                                }
                                if (campaignPlacementRuleVo.getAdOtherValue() != null) {
                                    campaignPlacement.setAdOtherValue(campaignPlacementRuleVo.getAdOtherValue().doubleValue());
                                }else {
                                    campaignPlacement.setAdOtherValue(0);
                                }
                                campaignPlacementRuleBuilder.addCampaignPlacements(campaignPlacement);
                            }
                            campaignPlacementRuleList.add(campaignPlacementRuleBuilder.build());
                        }
                        advertiseStrategyTemplateEdit.addAllCampaignPlacementRuleList(campaignPlacementRuleList);
                    } else {
                        List<TargetRuleVo> targetRuleVoList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(),TargetRuleVo.class);
                        List<TargetRule> targetRuleList = new ArrayList<>(targetRuleVoList.size());
                        Map<Integer,List<TargetRuleVo>> map = targetRuleVoList.stream().
                                collect(Collectors.groupingBy(TargetRuleVo::getSiteDate));
                        for (Integer siteDate : map.keySet()) {
                            TargetRule.Builder targetRuleBuilder = TargetRule.newBuilder();
                            targetRuleBuilder.setSiteDate(siteDate);
                            for (TargetRuleVo targetRuleVo:map.get(siteDate)) {
                                Target.Builder target = Target.newBuilder();
                                target.setStartTimeSite(targetRuleVo.getStartTimeSite());
                                target.setEndTimeSite(targetRuleVo.getEndTimeSite());
                                target.setBiddingType(targetRuleVo.getBiddingType());
                                target.setBiddingValue(targetRuleVo.getBiddingValue().doubleValue());
                                if (targetRuleVo.getBiddingMaxValue() != null) {
                                    target.setBiddingMaxValue(targetRuleVo.getBiddingMaxValue().doubleValue());
                                }
                                targetRuleBuilder.addTargets(target);
                            }
                            targetRuleList.add(targetRuleBuilder.build());
                        }
                        advertiseStrategyTemplateEdit.addAllTargetRuleList(targetRuleList);
                    }
                    advertiseStrategyTemplateEdit.setVersion(advertiseStrategyTemplate.getVersion());
                    if (advertiseStrategyTemplate.getCreateUid() != null) {
                        advertiseStrategyTemplateEdit.setCreateUid(advertiseStrategyTemplate.getCreateUid());
                    }
                    if (advertiseStrategyTemplate.getUpdateUid() != null) {
                        advertiseStrategyTemplateEdit.setUpdateUid(advertiseStrategyTemplate.getUpdateUid());
                    }
                    if (StringUtils.isNotBlank(advertiseStrategyTemplate.getCreateName())) {
                        advertiseStrategyTemplateEdit.setCreateName(advertiseStrategyTemplate.getCreateName());
                    }
                    if (StringUtils.isNotBlank(advertiseStrategyTemplate.getUpdateName())){
                        advertiseStrategyTemplateEdit.setUpdateName(advertiseStrategyTemplate.getUpdateName());
                    }
                    advertiseStrategyTemplateEdit.setCreateAt(advertiseStrategyTemplate.getCreateAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    advertiseStrategyTemplateEdit.setLastUpdateAt(advertiseStrategyTemplate.getCreateAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    advertiseStrategyTemplateEdit.setMarketplaceName(advertiseStrategyTemplate.getMarketplaceName());
                    advertiseStrategyTemplateEdit.setStatus(advertiseStrategyTemplate.getStatus());
                    builder.setData(advertiseStrategyTemplateEdit);
                    builder.setCode(result.getCode());
                } else {
                    builder.setMsg(result.getMsg());
                    builder.setCode(result.getCode());
                }
            } else {
                builder.setMsg(result.getMsg());
                builder.setCode(result.getCode());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告组合 没有copy 都是利用addTemplate
     */
    @Override
    public void copyTemplate(AdStrategyTemplateCopyRequest request, StreamObserver<AdStrategyTemplateResponse> responseObserver) {
        log.info("模板 copyTemplate request {}", request);
        AdStrategyTemplateResponse.Builder responseBuilder = AdStrategyTemplateResponse.newBuilder();
        //参数校验
        if (!request.hasTemplateId() || !request.hasPuid()) {
            responseBuilder.setMsg("请求参数错误");
            responseBuilder.setCode(Result.ERROR);
        } else {
            Result<AdvertiseStrategyTemplate> result = strategyTemplateService.copyTemplate(request.getPuid(), request.getTemplateId());
            if (result.getCode() == Result.SUCCESS) {
                AdvertiseStrategyTemplate advertiseStrategyTemplate = result.getData();
                AdStrategyTemplateRpc.Builder advertiseStrategyTemplateCopy = AdStrategyTemplateRpc.newBuilder();
                advertiseStrategyTemplateCopy.setId(advertiseStrategyTemplate.getId());
                advertiseStrategyTemplateCopy.setPuid(advertiseStrategyTemplate.getPuid());
                List<ShopAuth> shops = shopAuthDao.getListByPuid(request.getPuid());
                Map<Integer, ShopAuth> shopAuthMap = null;
                if (CollectionUtils.isNotEmpty(shops)) {
                    shopAuthMap = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
                }
                advertiseStrategyTemplateCopy.setShopId(advertiseStrategyTemplate.getShopId());
                if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(advertiseStrategyTemplate.getShopId())) {
                    advertiseStrategyTemplateCopy.setShopName(shopAuthMap.get(advertiseStrategyTemplate.getShopId()).getName());
                }
                advertiseStrategyTemplateCopy.setMarketplaceId(advertiseStrategyTemplate.getMarketplaceId());
                advertiseStrategyTemplateCopy.setTemplateName(advertiseStrategyTemplate.getTemplateName());
                advertiseStrategyTemplateCopy.setType(advertiseStrategyTemplate.getType());
                advertiseStrategyTemplateCopy.setItemType(advertiseStrategyTemplate.getItemType());
                advertiseStrategyTemplateCopy.setRule(advertiseStrategyTemplate.getRule());
                if ("CAMPAIGN".equals(advertiseStrategyTemplate.getItemType())) {
                    List<CampaignRuleVo> campaignRuleVoList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(),CampaignRuleVo.class);
                    List<CampaignRule> campaignRuleList = new ArrayList<>(campaignRuleVoList.size());
                    Map<Integer,List<CampaignRuleVo>> map = campaignRuleVoList.stream().
                            collect(Collectors.groupingBy(CampaignRuleVo::getSiteDate));
                    for (Integer siteDate : map.keySet()) {
                        CampaignRule.Builder campaignRuleBuilder = CampaignRule.newBuilder();
                        campaignRuleBuilder.setSiteDate(siteDate);
                        for (CampaignRuleVo campaignRuleVo : map.get(siteDate)) {
                            Campaign.Builder campaign = Campaign.newBuilder();
                            campaign.setBudgetValue(campaignRuleVo.getBudgetValue().doubleValue());
                            campaign.setStartTimeSite(campaignRuleVo.getStartTimeSite());
                            campaign.setEndTimeSite(campaignRuleVo.getEndTimeSite());
                            if ("numerical".equals(advertiseStrategyTemplate.getChildrenItemType())) {
                                campaign.setBudgetType(campaignRuleVo.getBudgetType());
                            }
                            campaignRuleBuilder.addCampaigns(campaign);
                        }
                        campaignRuleList.add(campaignRuleBuilder.build());
                    }
                    advertiseStrategyTemplateCopy.addAllCampaignRuleList(campaignRuleList);
                } else if ("CAMPAIGN_PLACEMENT".equals(advertiseStrategyTemplate.getItemType())) {
                    List<CampaignPlacementRuleVo> campaignPlacementRuleVoList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(),CampaignPlacementRuleVo.class);
                    List<CampaignPlacementRule> campaignPlacementRuleList = new ArrayList<>(campaignPlacementRuleVoList.size());
                    Map<Integer,List<CampaignPlacementRuleVo>> map = campaignPlacementRuleVoList.stream().
                            collect(Collectors.groupingBy(CampaignPlacementRuleVo::getSiteDate));
                    for (Integer siteDate : map.keySet()) {
                        CampaignPlacementRule.Builder campaignPlacementRuleBuilder = CampaignPlacementRule.newBuilder();
                        campaignPlacementRuleBuilder.setSiteDate(siteDate);
                        for (CampaignPlacementRuleVo campaignPlacementRuleVo : map.get(siteDate)) {
                            CampaignPlacement.Builder campaignPlacement = CampaignPlacement.newBuilder();
                            campaignPlacement.setAdPlaceProductType(campaignPlacementRuleVo.getAdPlaceProductType());
                            campaignPlacement.setStartTimeSite(campaignPlacementRuleVo.getStartTimeSite());
                            campaignPlacement.setEndTimeSite(campaignPlacementRuleVo.getEndTimeSite());
                            campaignPlacement.setAdPlaceProductValue(campaignPlacementRuleVo.getAdPlaceProductValue().doubleValue());
                            campaignPlacement.setAdPlaceTopMaxValue(campaignPlacementRuleVo.getAdPlaceTopMaxValue().doubleValue());
                            campaignPlacement.setAdPlaceTopType(campaignPlacementRuleVo.getAdPlaceTopType());
                            campaignPlacement.setAdPlaceTopValue(campaignPlacementRuleVo.getAdPlaceTopValue().doubleValue());
                            if (campaignPlacementRuleVo.getAdOtherType() != null) {
                                campaignPlacement.setAdOtherType(campaignPlacementRuleVo.getAdOtherType());
                            } else {
                                campaignPlacement.setAdOtherType(4);
                            }
                            if (campaignPlacementRuleVo.getAdOtherValue() != null) {
                                campaignPlacement.setAdOtherValue(campaignPlacementRuleVo.getAdOtherValue().doubleValue());
                            }else {
                                campaignPlacement.setAdOtherValue(0);
                            }
                            campaignPlacementRuleBuilder.addCampaignPlacements(campaignPlacement);
                        }
                        campaignPlacementRuleList.add(campaignPlacementRuleBuilder.build());
                    }
                    advertiseStrategyTemplateCopy.addAllCampaignPlacementRuleList(campaignPlacementRuleList);
                } else if ("TARGET".equals(advertiseStrategyTemplate.getItemType())) {
                    List<TargetRuleVo> targetRuleVoList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(),TargetRuleVo.class);
                    List<TargetRule> targetRuleList = new ArrayList<>(targetRuleVoList.size());
                    Map<Integer,List<TargetRuleVo>> map = targetRuleVoList.stream().
                            collect(Collectors.groupingBy(TargetRuleVo::getSiteDate));
                    for (Integer siteDate : map.keySet()) {
                        TargetRule.Builder targetRuleBuilder = TargetRule.newBuilder();
                        targetRuleBuilder.setSiteDate(siteDate);
                        for (TargetRuleVo targetRuleVo:map.get(siteDate)) {
                            Target.Builder target = Target.newBuilder();
                            target.setStartTimeSite(targetRuleVo.getStartTimeSite());
                            target.setEndTimeSite(targetRuleVo.getEndTimeSite());
                            target.setBiddingType(targetRuleVo.getBiddingType());
                            target.setBiddingValue(targetRuleVo.getBiddingValue().doubleValue());
                            target.setBiddingMaxValue(targetRuleVo.getBiddingMaxValue().doubleValue());
                            targetRuleBuilder.addTargets(target);
                        }
                        targetRuleList.add(targetRuleBuilder.build());
                    }
                    advertiseStrategyTemplateCopy.addAllTargetRuleList(targetRuleList);
                } else if ("START_STOP".equals(advertiseStrategyTemplate.getItemType())) {
                    List<StartStopRuleVo> startStopRuleVoList = JSONUtil.
                            jsonToArray(advertiseStrategyTemplate.getRule(),StartStopRuleVo.class);
                    List<StartStopStateRule> startStopStateRuleList = Lists.newArrayList();
                    Map<Integer,List<StartStopRuleVo>> map = startStopRuleVoList.stream().
                            collect(Collectors.groupingBy(StartStopRuleVo::getSiteDate));
                    for (Integer siteDate : map.keySet()) {
                        StartStopStateRule.Builder startStopBuilder = StartStopStateRule.newBuilder();
                        startStopBuilder.setSiteDate(siteDate);
                        for (StartStopRuleVo startStopRuleVo:map.get(siteDate)) {
                            StateRule.Builder stateBuilder = StateRule.newBuilder();
                            stateBuilder.setStartTimeSite(startStopRuleVo.getStartTimeSite());
                            stateBuilder.setEndTimeSite(startStopRuleVo.getEndTimeSite());
                            stateBuilder.setState(startStopRuleVo.getState());
                            startStopBuilder.addStateRules(stateBuilder.build());
                        }
                        startStopStateRuleList.add(startStopBuilder.build());
                    }
                    advertiseStrategyTemplateCopy.addAllStartStopStateRuleList(startStopStateRuleList);
                }
                if (advertiseStrategyTemplate.getCreateUid() != null) {
                    advertiseStrategyTemplateCopy.setCreateUid(advertiseStrategyTemplate.getCreateUid());
                }
                if (advertiseStrategyTemplate.getUpdateUid() != null) {
                    advertiseStrategyTemplateCopy.setUpdateUid(advertiseStrategyTemplate.getUpdateUid());
                }
                if (StringUtils.isNotBlank(advertiseStrategyTemplate.getCreateName())) {
                    advertiseStrategyTemplateCopy.setCreateName(advertiseStrategyTemplate.getCreateName());
                }
                if (StringUtils.isNotBlank(advertiseStrategyTemplate.getUpdateName())){
                    advertiseStrategyTemplateCopy.setUpdateName(advertiseStrategyTemplate.getUpdateName());
                }
                advertiseStrategyTemplateCopy.setCreateAt(advertiseStrategyTemplate.getCreateAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                advertiseStrategyTemplateCopy.setLastUpdateAt(advertiseStrategyTemplate.getCreateAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                advertiseStrategyTemplateCopy.setMarketplaceName(advertiseStrategyTemplate.getMarketplaceName());
                advertiseStrategyTemplateCopy.setStatus(advertiseStrategyTemplate.getStatus());
                responseBuilder.setData(advertiseStrategyTemplateCopy);
                responseBuilder.setCode(result.getCode());
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     *  无需修改
     */
    @Override
    public void getList(AdStrategyTemplateRequest request, StreamObserver<AdStrategyTemplateListResponse> responseObserver) {
        log.info("模板 getHourList request {}", request);
        AdStrategyTemplateListResponse.Builder responseBuilder = AdStrategyTemplateListResponse.newBuilder();
        //参数校验
        if (!request.hasPuid()) {
            responseBuilder.setMsg("请求参数错误");
            responseBuilder.setCode(Result.ERROR);
        } else {
            Result<List<AdvertiseStrategyTemplate>> result = strategyTemplateService.getList(request.getPuid(), request.getTemplateName(),request.getItemType());
            List<AdStrategyTemplateList> adStrategyTemplateLists =
                    Lists.newArrayList();
            if (result.getCode() == Result.SUCCESS) {
                List<AdvertiseStrategyTemplate> list = result.getData();
                if (CollectionUtils.isNotEmpty(list)) {
                    for (AdvertiseStrategyTemplate advertiseStrategyTemplate : list) {
                        AdStrategyTemplateList.Builder builder = AdStrategyTemplateList.newBuilder();
                        builder.setId(advertiseStrategyTemplate.getId());
                        builder.setTemplateName(advertiseStrategyTemplate.getTemplateName());
                        adStrategyTemplateLists.add(builder.build());
                    }
                }
                responseBuilder.addAllData(adStrategyTemplateLists);
                responseBuilder.setCode(Result.SUCCESS);
            } else {
                responseBuilder.setMsg("模板列表查询异常");
                responseBuilder.setCode(Result.ERROR);
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }
//
//    @Override
//    public void transferTemplate(AdStrategyTemplateRequest request, StreamObserver<AdStrategyTemplateListResponse> responseObserver) {
//        log.info("模板 transferTemplate request {}", request);
//        AdStrategyTemplateListResponse.Builder responseBuilder = AdStrategyTemplateListResponse.newBuilder();
//        //参数校验
//        if (request.hasPuid() || request.hasShopId()) {
//            responseBuilder.setMsg("请求参数错误");
//            responseBuilder.setCode(Result.ERROR);
//        } else {
//            Result<List<AdvertiseStrategyTemplate>> result = strategyTemplateService.getHourList(request.getPuid(), request.getTemplateName(), request.getShopId());
//            if (result.getCode() == Result.SUCCESS) {
//                List<AdvertiseStrategyTemplate> list = result.getData();
//                if (CollectionUtils.isNotEmpty(list)) {
//                    List<AdStrategyTemplateList> strategyTemplateLists = new ArrayList<>(list.size());
//                    for (AdvertiseStrategyTemplate advertiseStrategyTemplate : list) {
//                        AdStrategyTemplateList.Builder builder = AdStrategyTemplateList.newBuilder();
//                        builder.setId(advertiseStrategyTemplate.getId());
//                        builder.setPuid(advertiseStrategyTemplate.getPuid());
//                        builder.setMarketplaceId(advertiseStrategyTemplate.getMarketplaceId());
//                        builder.setTemplateName(advertiseStrategyTemplate.getTemplateName());
//                        builder.setMarketplaceName(advertiseStrategyTemplate.getMarketplaceName());
//                        builder.setUpdateAt(advertiseStrategyTemplate.getLastUpdateAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
//                        if (AdItemType.CAMPAIGN.equals(advertiseStrategyTemplate.getItemType())) {
//                            List<CampaignRuleVo> campaignRuleVoList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(),CampaignRuleVo.class);
//                            List<CampaignRule> campaignRuleList = new ArrayList<>(campaignRuleVoList.size());
//                            for (CampaignRuleVo campaignRuleVo : campaignRuleVoList) {
//                                CampaignRule.Builder campaignRuleBuilder = CampaignRule.newBuilder();
//                                campaignRuleBuilder.setSiteDate(campaignRuleVo.getSiteDate());
//                                campaignRuleBuilder.setBudgetValue(campaignRuleVo.getBudgetValue().toString());
//                                campaignRuleBuilder.setStartTimeSite(campaignRuleVo.getStartTimeSite());
//                                campaignRuleBuilder.setEndTimeSite(campaignRuleVo.getEndTimeSite());
//                                campaignRuleList.add(campaignRuleBuilder.build());
//                            }
//                            builder.addAllCampaignRuleList(campaignRuleList);
//                        } else if (AdItemType.CAMPAIGN_PLACEMENT.equals(advertiseStrategyTemplate.getItemType())) {
//                            List<CampaignPlacementRuleVo> campaignPlacementRuleVoList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(),CampaignPlacementRuleVo.class);
//                            List<CampaignPlacementRule> campaignPlacementRuleList = new ArrayList<>(campaignPlacementRuleVoList.size());
//                            for (CampaignPlacementRuleVo campaignPlacementRuleVo : campaignPlacementRuleVoList) {
//                                CampaignPlacementRule.Builder campaignPlacementRuleBuilder = CampaignPlacementRule.newBuilder();
//                                campaignPlacementRuleBuilder.setSiteDate(campaignPlacementRuleVo.getSiteDate());
//                                campaignPlacementRuleBuilder.setAdPlaceProductType(campaignPlacementRuleVo.getAdPlaceProductType());
//                                campaignPlacementRuleBuilder.setStartTimeSite(campaignPlacementRuleVo.getStartTimeSite());
//                                campaignPlacementRuleBuilder.setEndTimeSite(campaignPlacementRuleVo.getEndTimeSite());
//                                campaignPlacementRuleBuilder.setAdPlaceProductValue(campaignPlacementRuleVo.getAdPlaceProductValue().toString());
//                                campaignPlacementRuleBuilder.setAdPlaceTopMaxValue(campaignPlacementRuleVo.getAdPlaceTopMaxValue().toString());
//                                campaignPlacementRuleBuilder.setAdPlaceTopType(campaignPlacementRuleVo.getAdPlaceTopType());
//                                campaignPlacementRuleBuilder.setAdPlaceTopValue(campaignPlacementRuleVo.getAdPlaceTopValue().toString());
//                                campaignPlacementRuleList.add(campaignPlacementRuleBuilder.build());
//                            }
//                            builder.addAllCampaignPlacementRuleList(campaignPlacementRuleList);
//                        } else {
//                            List<TargetRuleVo> targetRuleVoList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(),TargetRuleVo.class);
//                            List<TargetRule> targetRuleList = new ArrayList<>(targetRuleVoList.size());
//                            for (TargetRuleVo targetRuleVo : targetRuleVoList) {
//                                TargetRule.Builder targetRuleBuilder = TargetRule.newBuilder();
//                                targetRuleBuilder.setSiteDate(targetRuleVo.getSiteDate());
//                                targetRuleBuilder.setStartTimeSite(targetRuleVo.getStartTimeSite());
//                                targetRuleBuilder.setEndTimeSite(targetRuleVo.getEndTimeSite());
//                                targetRuleBuilder.setBiddingType(targetRuleVo.getBiddingType());
//                                targetRuleBuilder.setBiddingValue(targetRuleVo.getBiddingValue().toString());
//                                targetRuleBuilder.setBiddingMaxValue(targetRuleVo.getBiddingMaxValue().toString());
//                                targetRuleList.add(targetRuleBuilder.build());
//                            }
//                            builder.addAllTargetRuleList(targetRuleList);
//                        }
//                        strategyTemplateLists.add(builder.build());
//                    }
//                    responseBuilder.addAllData(strategyTemplateLists);
//                }
//                responseBuilder.setCode(Result.SUCCESS);
//            } else {
//                responseBuilder.setMsg("模板列表查询异常");
//                responseBuilder.setCode(Result.ERROR);
//            }
//        }
//        responseObserver.onNext(responseBuilder.build());
//        responseObserver.onCompleted();
//    }

    @Override
    public void templateExport(TemplateExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("模板导出 templateExport request {}", request);
        UrlResponse.Builder responseBuilder = UrlResponse.newBuilder();
        int count = 0;
        if (!request.hasPuid() || !request.hasItemType()) {
            responseBuilder.setMsg("请求参数错误");
            responseBuilder.setCode(Result.ERROR);
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        try {
            String fileName = "";
            if (ItemTypeEnum.CAMPAIGN.getItemType().equals(request.getItemType())) {
                fileName = "分时调预算模板";
            } else if (ItemTypeEnum.CAMPAIGN_PLACEMENT.getItemType().equals(request.getItemType())) {
                fileName = "分时调广告位模板";
            } else if (ItemTypeEnum.TARGET.getItemType().equals(request.getItemType())) {
                fileName = "分时调竞价模板";
            } else if (ItemTypeEnum.START_STOP.getItemType().equals(request.getItemType())) {
                fileName = "分时调启停模板";
            }

            AdvertiseStrategyTemplateRequest param = new AdvertiseStrategyTemplateRequest();
            param.setPageSize(Integer.MAX_VALUE);
            param.setPageNo(1);
            param.setSearchValue(request.getTemplateName());
            param.setItemType(request.getItemType());
            param.setChildrenItemType(request.getChildrenItemType());
            param.setShopIdList(request.getShopIdList());
            param.setStartStopItemType(request.getStartStopItemType());
            if (StringUtils.isNotBlank(request.getUpdateTimeStart())) {
                param.setUpdateTimeStart(DateUtil.strToDate(request.getUpdateTimeStart(), DateUtil.PATTERN));
            }
            if (StringUtils.isNotBlank(request.getUpdateTimeEnd())) {
                param.setUpdateTimeEnd(DateUtil.getDayMaxDate(DateUtil.strToDate(request.getUpdateTimeEnd(), DateUtil.PATTERN)));
            }
            if (StringUtils.isNotBlank(request.getUpdateUid())) {
                param.setUpdateUidList(Arrays.stream(request.getUpdateUid().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
            }
            if (StringUtils.isNotBlank(request.getCreateUid())) {
                param.setCreateUidList(Arrays.stream(request.getCreateUid().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
            }
            Page page = strategyTemplateService.excelExport(request.getPuid(),param);
            List<AdvertiseStrategyTemplate> advertiseStrategyTemplateList = page.getRows();

            if (CollectionUtils.isEmpty(advertiseStrategyTemplateList)) {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg("excel.export.none");
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }

            //查询所有门店
            List<ShopAuth> shopAuths = shopAuthDao.listScAndVcAllByPuid(request.getPuid());
            Map<Integer, ShopAuth> shopMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(shopAuths)) {
                shopMap = shopAuths.stream().filter(Objects::nonNull).collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
            }

            List<String> urls = Lists.newLinkedList();
            WriteHandlerBuild build = new WriteHandlerBuild();
            List<List<AdvertiseStrategyTemplate>> partition = Lists.partition(advertiseStrategyTemplateList, Constants.FILE_MAX_SIZE);

            //启停，需要区分受控对象类型
            if (ItemTypeEnum.START_STOP.getItemType().equals(request.getItemType())) {
                for (List<AdvertiseStrategyTemplate> advertiseStrategyTemplates : partition) {
                    List<AdStartStopTemplateExcel> templateExcelList = Lists.newArrayList();
                    for (AdvertiseStrategyTemplate advertiseStrategyTemplate : advertiseStrategyTemplates) {
                        AdStartStopTemplateExcel templateExcel = new AdStartStopTemplateExcel();
                        templateExcel.setTemplateName(advertiseStrategyTemplate.getTemplateName());
                        templateExcel.setUsageAmount(Optional.ofNullable(advertiseStrategyTemplate.getUsageAmount()).orElse(0));
                        templateExcel.setCreateName(advertiseStrategyTemplate.getCreateName());
                        templateExcel.setLastUpdateName(advertiseStrategyTemplate.getUpdateName());
                        if (advertiseStrategyTemplate.getLastUpdateAt() != null) {
                            templateExcel.setLastUpdateTime(advertiseStrategyTemplate.getLastUpdateAt().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
                        }
                        if (MapUtils.isNotEmpty(shopMap) && shopMap.containsKey(advertiseStrategyTemplate.getShopId())) {
                            templateExcel.setShopName(shopMap.get(advertiseStrategyTemplate.getShopId()).getName());
                        }
                        if (StartStopItemTypeEnum.CAMPAIGN.getValue().equals(advertiseStrategyTemplate.getStartStopItemType())) {
                            templateExcel.setStartStopItemType(StartStopItemTypeEnum.CAMPAIGN.getDesc());
                        } else if (StartStopItemTypeEnum.PRODUCT.getValue().equals(advertiseStrategyTemplate.getStartStopItemType())) {
                            templateExcel.setStartStopItemType(StartStopItemTypeEnum.PRODUCT.getDesc());
                        } else {
                            templateExcel.setStartStopItemType("-");
                        }
                        templateExcel.setRule(buildStartStopRuleString(advertiseStrategyTemplate));
                        templateExcelList.add(templateExcel);
                    }

                    if (CollectionUtils.isNotEmpty(templateExcelList)) {
                        //进行对数据的存入
                        String downloadUrl = excelService.easyExcelHandlerExport(request.getPuid(), templateExcelList,  fileName+ "(" + count++ + ")", AdStartStopTemplateExcel.class, build);
                        urls.add(downloadUrl);
                    }

                    responseBuilder.setCode(Result.SUCCESS);
                    responseBuilder.setMsg("process.msg.sync.success");
                    responseBuilder.addAllUrls(urls);
                }
            } else {
                //其他，统一处理

                boolean isTarget = ItemTypeEnum.TARGET.getItemType().equals(request.getItemType());
                for (List<AdvertiseStrategyTemplate> advertiseStrategyTemplates : partition) {

                    //投放，需要查询组受控的受控对象数量
                    Map<Long, Integer> groupCountMap = new HashMap<>();
                    if (isTarget) {
                        List<Long> templateIds = advertiseStrategyTemplates.stream().map(AdvertiseStrategyTemplate::getId).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(templateIds)) {
                            List<TemplateStatusCountVo> countVoList = advertiseStrategyAdGroupDao.getTemplateUsageAmount(request.getPuid(), templateIds, request.getItemType());
                            if (CollectionUtils.isNotEmpty(countVoList)) {
                                groupCountMap = countVoList.stream().collect(Collectors.toMap(TemplateStatusCountVo::getTemplateId, TemplateStatusCountVo::getStatusCount));
                            }
                        }
                    }

                    List<AdTemplateExcel> templateExcelList = Lists.newArrayList();
                    for (AdvertiseStrategyTemplate advertiseStrategyTemplate : advertiseStrategyTemplates) {
                        AdTemplateExcel templateExcel = new AdTemplateExcel();
                        templateExcel.setTemplateName(advertiseStrategyTemplate.getTemplateName());

                        //设置组受控的受控对象数量
                        if (isTarget) {
                            Integer groupCount = groupCountMap.get(advertiseStrategyTemplate.getId());
                            if (Objects.nonNull(groupCount)) {
                                advertiseStrategyTemplate.setUsageAmount(groupCount);
                            }
                        }

                        templateExcel.setUsageAmount(Optional.ofNullable(advertiseStrategyTemplate.getUsageAmount()).orElse(0));
                        templateExcel.setCreateName(advertiseStrategyTemplate.getCreateName());
                        templateExcel.setLastUpdateName(advertiseStrategyTemplate.getUpdateName());
                        if (advertiseStrategyTemplate.getLastUpdateAt() != null) {
                            templateExcel.setLastUpdateTime(advertiseStrategyTemplate.getLastUpdateAt().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
                        }
                        if (MapUtils.isNotEmpty(shopMap) && shopMap.containsKey(advertiseStrategyTemplate.getShopId())) {
                            templateExcel.setShopName(shopMap.get(advertiseStrategyTemplate.getShopId()).getName());
                        }

                        if (ItemTypeEnum.CAMPAIGN.getItemType().equals(request.getItemType())) {
                            templateExcel.setRule(buildCampaignRuleString(advertiseStrategyTemplate));
                        } else if (ItemTypeEnum.CAMPAIGN_PLACEMENT.getItemType().equals(request.getItemType())) {
                            templateExcel.setRule(buildCampaignPlacementRuleString(advertiseStrategyTemplate));
                        } else if (ItemTypeEnum.TARGET.getItemType().equals(request.getItemType())) {
                            templateExcel.setRule(buildTargetRuleString(advertiseStrategyTemplate));
                        }
                        templateExcelList.add(templateExcel);
                    }

                    if (CollectionUtils.isNotEmpty(templateExcelList)) {
                        //进行对数据的存入
                        String downloadUrl = excelService.easyExcelHandlerExport(request.getPuid(), templateExcelList,  fileName + "(" + count++ + ")", AdTemplateExcel.class, build);
                        urls.add(downloadUrl);
                    }

                    responseBuilder.setCode(Result.SUCCESS);
                    responseBuilder.setMsg("process.msg.sync.success");
                    responseBuilder.addAllUrls(urls);
                }
            }

        } catch (Exception e) {
            responseBuilder.setMsg("process.msg.sync.fail");
            responseBuilder.setCode(Result.ERROR);
            log.error("puid={} 模板导出异常:{}",request.getPuid(),e);
        }

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }


    private String buildStartStopRuleString(AdvertiseStrategyTemplate template) {
        List<StartStopRuleVo> startStopRuleVoList = JSONUtil.jsonToArray(template.getRule(),StartStopRuleVo.class);
        StringBuilder rule = new StringBuilder();
        if (template.getType().equals("DAILY")) {
            rule.append("每日调整:");
            for (StartStopRuleVo startStopRuleVo : startStopRuleVoList) {
                rule.append(startStopRuleVo.getStartTimeSite()).append(":00").append("~");
                rule.append(startStopRuleVo.getEndTimeSite()).append(":00");
                if ("enabled".equals(startStopRuleVo.getState())) {
                    rule.append(" 开启;");
                } else {
                    rule.append(" 暂停;");
                }
            }
        } else {
            rule.append("每周调整:");
            Map<Integer,List<StartStopRuleVo>> map = startStopRuleVoList.
                    stream().collect(Collectors.groupingBy(StartStopRuleVo::getSiteDate));
            for (Integer day : map.keySet()) {
                rule.append(com.meiyunji.sponsored.service.
                        cpc.util.Constants.getDateMap().get(day));
                for (StartStopRuleVo startStopRuleVo : map.get(day)) {
                    rule.append(startStopRuleVo.getStartTimeSite()).append(":00").append("~");
                    rule.append(startStopRuleVo.getEndTimeSite()).append(":00");
                    if ("enabled".equals(startStopRuleVo.getState())) {
                        rule.append(" 开启;");
                    } else {
                        rule.append(" 暂停;");
                    }
                }
                rule.append("\n");
            }
        }
        return rule.toString();
    }


    private String buildCampaignRuleString(AdvertiseStrategyTemplate template) {
        List<CampaignRuleVo> campaignRuleVoList = JSONUtil.jsonToArray(template.getRule(), CampaignRuleVo.class);
        StringBuilder rule = new StringBuilder();
        if (template.getChildrenItemType().equals("proportion")) {
            if (template.getType().equals("DAILY")) {
                rule.append("每日调整:");
                for (CampaignRuleVo campaignRuleVo : campaignRuleVoList) {
                    rule.append(campaignRuleVo.getStartTimeSite()).append(":00").append("~");
                    rule.append(campaignRuleVo.getEndTimeSite()).append(":00");
                    rule.append("原预算").append(campaignRuleVo.getBudgetValue()).append("%").append(";");
                }
            } else {
                rule.append("每周调整:");
                Map<Integer, List<CampaignRuleVo>> map = campaignRuleVoList.
                        stream().collect(Collectors.groupingBy(CampaignRuleVo::getSiteDate));
                for (Integer day : map.keySet()) {
                    rule.append(com.meiyunji.sponsored.service.
                            cpc.util.Constants.getDateMap().get(day));
                    for (CampaignRuleVo campaignRuleVo : map.get(day)) {
                        rule.append(campaignRuleVo.getStartTimeSite()).append(":00").append("~");
                        rule.append(campaignRuleVo.getEndTimeSite()).append(":00");
                        rule.append("原预算").append(campaignRuleVo.getBudgetValue()).append("%").append(";");
                    }
                    rule.append("\n");
                }
            }
        } else {
            if (template.getType().equals("DAILY")) {
                rule.append("每日调整:");
                for (CampaignRuleVo campaignRuleVo : campaignRuleVoList) {
                    rule.append(campaignRuleVo.getStartTimeSite()).append(":00").append("~");
                    rule.append(campaignRuleVo.getEndTimeSite()).append(":00");
                    rule.append("预算值调整为").append(campaignRuleVo.getBudgetValue()).append(";");
                }
            } else {
                rule.append("每周调整:");
                Map<Integer, List<CampaignRuleVo>> map = campaignRuleVoList.
                        stream().collect(Collectors.groupingBy(CampaignRuleVo::getSiteDate));
                for (Integer day : map.keySet()) {
                    rule.append(com.meiyunji.sponsored.service.
                            cpc.util.Constants.getDateMap().get(day));
                    for (CampaignRuleVo campaignRuleVo : map.get(day)) {
                        rule.append(campaignRuleVo.getStartTimeSite()).append(":00").append("~");
                        rule.append(campaignRuleVo.getEndTimeSite()).append(":00");
                        rule.append("预算值调整为").append(campaignRuleVo.getBudgetValue()).append(";");
                    }
                    rule.append("\n");
                }
            }
        }
        return rule.toString();
    }

    private String buildTargetRuleString(AdvertiseStrategyTemplate template) {
        List<TargetRuleVo> targetRuleVoList = JSONUtil.jsonToArray(template.getRule(), TargetRuleVo.class);
        StringBuilder rule = new StringBuilder();
        if (template.getType().equals("DAILY")) {
            rule.append("每日调整:");
            for (TargetRuleVo targetRuleVo : targetRuleVoList) {
                rule.append(targetRuleVo.getStartTimeSite()).append(":00").append("~");
                rule.append(targetRuleVo.getEndTimeSite()).append(":00");
                if (targetRuleVo.getBiddingType() == 0 || targetRuleVo.getBiddingType() == 1) {
                    rule.append("原始竞价增加").append(targetRuleVo.getBiddingValue()).append("%");
                    if (targetRuleVo.getBiddingMaxValue() != null) {
                        rule.append("且不大于").append(targetRuleVo.getBiddingMaxValue());
                    }
                    rule.append(";");
                }
                if (targetRuleVo.getBiddingType() == 2 || targetRuleVo.getBiddingType() == 3) {
                    rule.append("原始竞价减少").append(targetRuleVo.getBiddingValue()).append("%");
                    if (targetRuleVo.getBiddingMinValue() != null) {
                        rule.append("且不小于").append(targetRuleVo.getBiddingMinValue());
                    }
                    rule.append(";");
                }
                if (targetRuleVo.getBiddingType() == 4) {
                    rule.append("自定义新竞价").append(targetRuleVo.getBiddingValue()).append("%").append(";");
                }
            }
        } else {
            rule.append("每周调整:");
            Map<Integer,List<TargetRuleVo>> map = targetRuleVoList.
                    stream().collect(Collectors.groupingBy(TargetRuleVo::getSiteDate));
            for (Integer day : map.keySet()) {
                rule.append(com.meiyunji.sponsored.service.
                        cpc.util.Constants.getDateMap().get(day));
                for (TargetRuleVo targetRuleVo:map.get(day)) {
                    rule.append(targetRuleVo.getStartTimeSite()).append(":00").append("~");
                    rule.append(targetRuleVo.getEndTimeSite()).append(":00");
                    if (targetRuleVo.getBiddingType() == 0 || targetRuleVo.getBiddingType() == 1) {
                        rule.append("原始竞价增加").append(targetRuleVo.getBiddingValue()).append("%");
                        if (targetRuleVo.getBiddingMaxValue() != null) {
                            rule.append("且不大于").append(targetRuleVo.getBiddingMaxValue());
                        }
                        rule.append(";");
                    }
                    if (targetRuleVo.getBiddingType() == 2 || targetRuleVo.getBiddingType() == 3) {
                        rule.append("原始竞价减少").append(targetRuleVo.getBiddingValue()).append("%");
                        if (targetRuleVo.getBiddingMinValue() != null) {
                            rule.append("且不小于").append(targetRuleVo.getBiddingMinValue());
                        }
                        rule.append(";");
                    }
                    if (targetRuleVo.getBiddingType() == 4) {
                        rule.append("自定义新竞价").append(targetRuleVo.getBiddingValue()).append("%").append(";");
                    }
                }
                rule.append("\n");
            }
        }
        return rule.toString();
    }


    private String buildCampaignPlacementRuleString(AdvertiseStrategyTemplate template) {
        List<CampaignPlacementRuleVo> campaignPlacementRuleVoList = JSONUtil.jsonToArray(template.getRule(),CampaignPlacementRuleVo.class);
        StringBuilder rule = new StringBuilder();
        if (template.getType().equals("DAILY")) {
            rule.append("每日调整:");
            for (CampaignPlacementRuleVo campaignPlacementRuleVo : campaignPlacementRuleVoList) {
                rule.append(campaignPlacementRuleVo.getStartTimeSite()).append(":00").append("~");
                rule.append(campaignPlacementRuleVo.getEndTimeSite()).append(":00");
                if (campaignPlacementRuleVo.getAdPlaceTopType() == 0) {
                    rule.append("基于顶部（首页）竞价上浮").append(campaignPlacementRuleVo.getAdPlaceTopValue()).append("%").
                            append("且调整后最大值不大于").append(campaignPlacementRuleVo.getAdPlaceTopMaxValue()).append("%").append(";");
                }
                if (campaignPlacementRuleVo.getAdPlaceTopType() == 1) {
                    rule.append("基于顶部（首页）竞价上浮绝对值").append(campaignPlacementRuleVo.getAdPlaceTopValue()).append("%").
                            append("且调整后最大值不大于").append(campaignPlacementRuleVo.getAdPlaceTopMaxValue()).append("%").append(";");
                }
                if (campaignPlacementRuleVo.getAdPlaceTopType() == 2) {
                    rule.append("基于顶部（首页）竞价下浮").append(campaignPlacementRuleVo.getAdPlaceTopValue()).append("%").
                            append("且调整后最小值不小于").append(campaignPlacementRuleVo.getAdPlaceTopMinValue()).append("%").append(";");
                }
                if (campaignPlacementRuleVo.getAdPlaceTopType() == 3) {
                    rule.append("基于顶部（首页）竞价下浮绝对值").append(campaignPlacementRuleVo.getAdPlaceTopValue()).append("%").
                            append("且调整后最大值不大于").append(campaignPlacementRuleVo.getAdPlaceTopMinValue()).append("%").append(";");
                }
                if (campaignPlacementRuleVo.getAdPlaceTopType() == 4) {
                    rule.append("基于顶部（首页）竞价自定义数值").append(campaignPlacementRuleVo.getAdPlaceTopValue()).append("%").append(";");
                }
                if (campaignPlacementRuleVo.getAdPlaceProductType() == 0) {
                    rule.append("基于产品页面竞价上浮").append(campaignPlacementRuleVo.getAdPlaceProductValue()).append("%").
                            append("且调整后最大值不大于").append(campaignPlacementRuleVo.getAdPlaceProductMaxValue()).append("%").append(";");
                }
                if (campaignPlacementRuleVo.getAdPlaceProductType() == 1) {
                    rule.append("基于产品页面竞价上浮绝对值").append(campaignPlacementRuleVo.getAdPlaceProductValue()).append("%").
                            append("且调整后最大值不大于").append(campaignPlacementRuleVo.getAdPlaceProductMaxValue()).append("%").append(";");
                }
                if (campaignPlacementRuleVo.getAdPlaceProductType() == 2) {
                    rule.append("基于产品页面竞价竞价下浮").append(campaignPlacementRuleVo.getAdPlaceProductValue()).append("%").
                            append("且调整后最小值不小于").append(campaignPlacementRuleVo.getAdPlaceProductMinValue()).append("%").append(";");
                }
                if (campaignPlacementRuleVo.getAdPlaceProductType() == 3) {
                    rule.append("基于产品页面竞价下浮绝对值").append(campaignPlacementRuleVo.getAdPlaceProductValue()).append("%").
                            append("且调整后最大值不大于").append(campaignPlacementRuleVo.getAdPlaceProductMinValue()).append("%").append(";");
                }
                if (campaignPlacementRuleVo.getAdPlaceProductType() == 4) {
                    rule.append("基于产品页面竞价自定义数值").append(campaignPlacementRuleVo.getAdPlaceProductValue()).append("%").append(";");
                }
                if (StringUtils.isNotBlank(campaignPlacementRuleVo.getStrategy())) {
                    String strategy = null;
                    if ("legacyForSales".equalsIgnoreCase(campaignPlacementRuleVo.getStrategy())) {
                        strategy = "动态竞价-只降低";
                    } else if ("autoForSales".equalsIgnoreCase(campaignPlacementRuleVo.getStrategy())) {
                        strategy = "动态竞价-提高和降低";
                    } else if ("manual".equalsIgnoreCase(campaignPlacementRuleVo.getStrategy())) {
                        strategy = "固定竞价";
                    } else if ("ruleBased".equalsIgnoreCase(campaignPlacementRuleVo.getStrategy())) {
                        strategy = "基于规则的竞价";
                    }
                    rule.append("基于竞价策略调整: ").append(strategy);
                }
            }
        } else {
            rule.append("每周调整:");
            Map<Integer,List<CampaignPlacementRuleVo>> map = campaignPlacementRuleVoList.
                    stream().collect(Collectors.groupingBy(CampaignPlacementRuleVo::getSiteDate));
            for (Integer day : map.keySet()) {
                rule.append(com.meiyunji.sponsored.service.
                        cpc.util.Constants.getDateMap().get(day));
                for (CampaignPlacementRuleVo campaignPlacementRuleVo:map.get(day)) {
                    rule.append(campaignPlacementRuleVo.getStartTimeSite()).append(":00").append("~");
                    rule.append(campaignPlacementRuleVo.getEndTimeSite()).append(":00");
                    if (campaignPlacementRuleVo.getAdPlaceTopType() == 0) {
                        rule.append("基于顶部（首页）竞价上浮").append(campaignPlacementRuleVo.getAdPlaceTopValue()).append("%").
                                append("且调整后最大值不大于").append(campaignPlacementRuleVo.getAdPlaceTopMaxValue()).append("%").append(";");
                    }
                    if (campaignPlacementRuleVo.getAdPlaceTopType() == 1) {
                        rule.append("基于顶部（首页）竞价上浮绝对值").append(campaignPlacementRuleVo.getAdPlaceTopValue()).append("%").
                                append("且调整后最大值不大于").append(campaignPlacementRuleVo.getAdPlaceTopMaxValue()).append("%").append(";");
                    }
                    if (campaignPlacementRuleVo.getAdPlaceTopType() == 2) {
                        rule.append("基于顶部（首页）竞价下浮").append(campaignPlacementRuleVo.getAdPlaceTopValue()).append("%").
                                append("且调整后最小值不小于").append(campaignPlacementRuleVo.getAdPlaceTopMinValue()).append("%").append(";");
                    }
                    if (campaignPlacementRuleVo.getAdPlaceTopType() == 3) {
                        rule.append("基于顶部（首页）竞价下浮绝对值").append(campaignPlacementRuleVo.getAdPlaceTopValue()).append("%").
                                append("且调整后最大值不大于").append(campaignPlacementRuleVo.getAdPlaceTopMinValue()).append("%").append(";");
                    }
                    if (campaignPlacementRuleVo.getAdPlaceTopType() == 4) {
                        rule.append("基于顶部（首页）竞价自定义数值").append(campaignPlacementRuleVo.getAdPlaceTopValue()).append("%").append(";");
                    }
                    if (campaignPlacementRuleVo.getAdPlaceProductType() == 0) {
                        rule.append("基于产品页面竞价上浮").append(campaignPlacementRuleVo.getAdPlaceProductValue()).append("%").
                                append("且调整后最大值不大于").append(campaignPlacementRuleVo.getAdPlaceProductMaxValue()).append("%").append(";");
                    }
                    if (campaignPlacementRuleVo.getAdPlaceProductType() == 1) {
                        rule.append("基于产品页面竞价上浮绝对值").append(campaignPlacementRuleVo.getAdPlaceProductValue()).append("%").
                                append("且调整后最大值不大于").append(campaignPlacementRuleVo.getAdPlaceProductMaxValue()).append("%").append(";");
                    }
                    if (campaignPlacementRuleVo.getAdPlaceProductType() == 2) {
                        rule.append("基于产品页面竞价竞价下浮").append(campaignPlacementRuleVo.getAdPlaceProductValue()).append("%").
                                append("且调整后最小值不小于").append(campaignPlacementRuleVo.getAdPlaceProductMinValue()).append("%").append(";");
                    }
                    if (campaignPlacementRuleVo.getAdPlaceProductType() == 3) {
                        rule.append("基于产品页面竞价下浮绝对值").append(campaignPlacementRuleVo.getAdPlaceProductValue()).append("%").
                                append("且调整后最大值不大于").append(campaignPlacementRuleVo.getAdPlaceProductMinValue()).append("%").append(";");
                    }
                    if (campaignPlacementRuleVo.getAdPlaceProductType() == 4) {
                        rule.append("基于产品页面竞价自定义数值").append(campaignPlacementRuleVo.getAdPlaceProductValue()).append("%").append(";");
                    }
                }
                rule.append("\n");
            }
        }

        return rule.toString();
    }

    @Override
    public void updateTemplateStatus(AdStrategyUpdateTemplateStatusRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("分时策略模板 updateTemplateStatus request {}", request);
        int puid = request.getPuid();
        List<Long> templateIdList = request.getTemplateIdList();
        String status = request.getStatus();

        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (CollectionUtils.isEmpty(templateIdList) || !StringUtils.equalsAny(status, AdStrategyEnableStatusEnum.ENABLED.getCode(), AdStrategyEnableStatusEnum.DISABLED.getCode())) {
            log.info("分时策略模板 updateTemplateStatus templateIdList empty or status error");
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误，请刷新页面重试");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        //实际是否需要处理，没有需要处理的数据也返回成功
        List<AdvertiseStrategyTemplate> templateList = getValidUpdateTemplateList(puid, templateIdList, status);
        if (CollectionUtils.isEmpty(templateList)) {
            log.info("分时策略模板 updateTemplateStatus 修改状态和实际状态一致，没有需要处理的模板");
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setMsg("");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        strategyTemplateService.updateTemplateStatus(puid, templateList, status, request.getUpdateUid(), false);
        builder.setCode(Int32Value.of(Result.SUCCESS));
        builder.setMsg("");
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    private List<AdvertiseStrategyTemplate> getValidUpdateTemplateList(int puid, List<Long> templateIdList, String status) {
        //查询模板，开启则查关闭，关闭则查开启
        String queryStatus;
        if (AutoRuleEnableStatusEnum.ENABLED.getCode().equals(status)) {
            queryStatus = AutoRuleEnableStatusEnum.DISABLED.getCode();
        } else {
            queryStatus = AutoRuleEnableStatusEnum.ENABLED.getCode();
        }
        List<AdvertiseStrategyTemplate> queryTemplateList = advertiseStrategyTemplateDao.getListByLongIdList(puid, templateIdList);
        List<AdvertiseStrategyTemplate> templateList = queryTemplateList.stream().filter(x -> x.getStatus().equals(queryStatus)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(templateList)) {
            log.info("分时策略模板 updateTemplateStatus status, template empty templateId {}", templateIdList);
            return null;
        }

        return templateList;
    }


}