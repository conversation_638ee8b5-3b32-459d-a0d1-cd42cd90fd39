package com.meiyunji.sponsored.api.newDashboard;

import com.google.api.client.util.Lists;
import com.google.common.collect.Range;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdCampaignOrGroupOrPortfolioRequest;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdCampaignOrGroupOrPortfolioResponse;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdCampaignOrGroupOrPortfolioResponseVo;
import com.meiyunji.sponsored.rpc.newDashboard.RpcDashboardAdCampaignOrGroupOrPortfolioServiceGrpc;
import com.meiyunji.sponsored.rpc.newDashboard.vo.UrlResponse;
import com.meiyunji.sponsored.service.newDashboard.enums.*;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdCampaignService;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdGroupService;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdPortfolioService;
import com.meiyunji.sponsored.service.newDashboard.util.DateTimeUtil;
import com.meiyunji.sponsored.service.newDashboard.util.YoyDateUtil;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardAdTypeReqVo;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardCampaignOrGroupOrPortfolioReqVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: ys
 * @date: 2024/4/9 14:31
 * @describe:
 */
@GRpcService
@Slf4j
public class CampaignOrGroupOrPortfolioRpcService
        extends RpcDashboardAdCampaignOrGroupOrPortfolioServiceGrpc.RpcDashboardAdCampaignOrGroupOrPortfolioServiceImplBase{

    private static final Range<Integer> LIMIT_RANGE = Range.closed(10, 50);

    @Autowired
    private IDashboardAdCampaignService dashboardAdCampaignService;

    @Autowired
    private IDashboardAdPortfolioService dashboardAdPortfolioService;

    @Autowired
    private IDashboardAdGroupService dashboardAdGroupService;

    @Override
    public void queryAdCampaignOrGroupOrPortfolioCharts(DashboardAdCampaignOrGroupOrPortfolioRequest request, StreamObserver<DashboardAdCampaignOrGroupOrPortfolioResponse> responseObserver) {
        //根据queryField调用不同层级的service
        log.info("dashboard query ad campaign/group/portfolio charts, request data: {}", request);
        StopWatch sw = new StopWatch();
        sw.start();
        DashboardAdCampaignOrGroupOrPortfolioResponse.Builder builder = DashboardAdCampaignOrGroupOrPortfolioResponse.newBuilder();
        DashboardAdCampaignOrGroupOrPortfolioResponseVo.Builder voBuilder = DashboardAdCampaignOrGroupOrPortfolioResponseVo.newBuilder();
        DashboardAdCampaignOrGroupOrPortfolioResponseVo.Page pageResult = DashboardAdCampaignOrGroupOrPortfolioResponseVo.Page.newBuilder().build();
        DashboardCampaignOrGroupOrPortfolioReqVo reqVo = processParam(request, false);
        if (Objects.isNull(reqVo)) {
            log.error("dashboard query ad campaign/group/portfolio charts, check param error");
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        } else {
            if (DashboardQueryFieldEnum.CAMPAIGN_QUERY_TYPE.getCode() == request.getQueryField()) {
                DashboardAdCampaignOrGroupOrPortfolioResponseVo.Page topList = dashboardAdCampaignService.queryCampaignCharts(reqVo);
                if (Objects.nonNull(topList)) {
                    pageResult = topList;
                }
            }
            if (DashboardQueryFieldEnum.GROUP_QUERY_TYPE.getCode() == request.getQueryField()) {
                DashboardAdCampaignOrGroupOrPortfolioResponseVo.Page topList = dashboardAdGroupService.queryGroupCharts(reqVo);
                if (Objects.nonNull(topList)) {
                    pageResult = topList;
                }
            }
            if (DashboardQueryFieldEnum.PORTFOLIO_TYPE.getCode() == request.getQueryField()) {
                DashboardAdCampaignOrGroupOrPortfolioResponseVo.Page topList = dashboardAdPortfolioService.queryPortfolioCharts(reqVo);
                if (Objects.nonNull(topList)) {
                    pageResult = topList;
                }
            }
            voBuilder.setPage(pageResult);
            builder.setData(voBuilder.build());
            builder.setCode(Result.SUCCESS);

            sw.stop();
            log.info("dashboard query ad campaign/group/portfolio charts, puid: {}, 耗时: {}秒", request.getPuid(), sw.getTotalTimeSeconds());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void exportAdCampaignOrGroupOrPortfolioCharts(DashboardAdCampaignOrGroupOrPortfolioRequest request, StreamObserver<UrlResponse> responseObserver) {
        //根据queryField调用不同层级的service
        log.info("dashboard export ad campaign/group/portfolio charts, request data: {}", request);
        StopWatch sw = new StopWatch();
        sw.start();
        UrlResponse.Builder builder = UrlResponse.newBuilder();
        try {
            DashboardCampaignOrGroupOrPortfolioReqVo reqVo = processParam(request, true);
            if (Objects.isNull(reqVo)) {
                log.error("dashboard export ad campaign/group/portfolio charts, check param error");
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            } else {
                List<String> urlList = Lists.newArrayList();
                if (DashboardQueryFieldEnum.CAMPAIGN_QUERY_TYPE.getCode() == request.getQueryField()) {
                    urlList = dashboardAdCampaignService.exportCampaignCharts(reqVo);
                }
                if (DashboardQueryFieldEnum.GROUP_QUERY_TYPE.getCode() == request.getQueryField()) {
                    urlList = dashboardAdGroupService.exportGroupCharts(reqVo);
                }
                if (DashboardQueryFieldEnum.PORTFOLIO_TYPE.getCode() == request.getQueryField()) {
                    urlList = dashboardAdPortfolioService.exportPortfolioCharts(reqVo);
                }
                if (CollectionUtils.isNotEmpty(urlList)) {
                    builder.addAllUrls(urlList);
                    builder.setCode(Int32Value.of(Result.SUCCESS));
                } else {
                    builder.setCode(Int32Value.of(Result.ERROR));
                    builder.setMsg("dashboard export ad campaign/group/portfolio fail");
                }
                sw.stop();
                log.info("dashboard export ad campaign/group/portfolio charts, puid: {}, 耗时: {}秒", request.getPuid(), sw.getTotalTimeSeconds());
            }
        } catch (IllegalStateException e) {
            builder.setCode(Int32Value.of(Result.ERROR));
            log.error("dashboard export campaign/group/portfolio charts, puid: {}", request.getPuid(), e);
            builder.setMsg("dashboard export ad campaign/group/portfolio fail");
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private DashboardCampaignOrGroupOrPortfolioReqVo processParam(DashboardAdCampaignOrGroupOrPortfolioRequest req, boolean export) {
        //校验值是否合法
        if (!DashboardCurrencyEnum.currencySet.contains(req.getCurrency())) {
            log.error("dashboard query ad campaign/group/portfolio charts, req param error, currency is null:{}", req.getCurrency());
            return null;
        }
        if (!export && !LIMIT_RANGE.contains(req.getLimit())) {
            log.error("dashboard query ad campaign/group/portfolio charts, req param error, limit is null:{}", req.getLimit());
            return null;
        }
        if (!DashboardOrderByRateEnum.rateMap.containsKey(req.getOrderByField())) {
            log.error("dashboard query ad campaign/group/portfolio charts, req param error, orderField is null:{}", req.getOrderByField());
            return null;
        }
        if (!DashboardOrderByEnum.orderByMap.containsKey(req.getOrderBy())) {
            log.error("dashboard query ad campaign/group/portfolio charts, req param error, orderBy is null:{}", req.getOrderBy());
            return null;
        }
        if (Objects.isNull(DashboardQueryFieldEnum.getDashboardCampaignQueryFieldEnumByCode(req.getQueryField()))) {
            log.error("dashboard query ad campaign/group/portfolio charts, req param error, queryField is null:{}", req.getQueryField());
            return null;
        }
        //同比期
        List<String> yoyDate = YoyDateUtil.getYoyDate(req.getStartDate(), req.getEndDate());
        DashboardCampaignOrGroupOrPortfolioReqVo vo = DashboardCampaignOrGroupOrPortfolioReqVo.builder()
                .percent(req.getPercent())
                .yoy(req.getYoy())
                .mom(req.getMom())
                .queryField(req.getQueryField())
                .build();
        if (CollectionUtils.isEmpty(yoyDate)) {
            // 开始时间故意写得比结束时间晚,令后续查出来的同比数据为空
            vo.setYoyOverLimit(true);
            vo.setYoyStartDate(DateUtil.toFormatDate(req.getStartDate()));
            vo.setYoyEndDate(DateUtil.dateToStrWithTime(DateUtil.addDay(DateUtil.strToDate(req.getStartDate(), DateUtil.PATTERN_YYYYMMDD), -1), DateUtil.PATTERN));
        } else {
            Optional.ofNullable(yoyDate.get(0)).map(DateUtil::toFormatDate).ifPresent(vo::setYoyStartDate);
            Optional.ofNullable(yoyDate.get(1)).map(DateUtil::toFormatDate).ifPresent(vo::setYoyEndDate);
        }
        BeanUtils.copyProperties(req, vo, ParamCopyUtil.checkPropertiesNullOrEmptySuper(req));
        Optional.of(req.getStartDate()).map(DateUtil::toFormatDate).ifPresent(vo::setStartDate);
        Optional.of(req.getEndDate()).map(DateUtil::toFormatDate).ifPresent(vo::setEndDate);
        Optional.of(req.getMomStartDate()).map(DateUtil::toFormatDate).ifPresent(vo::setMomStartDate);
        Optional.of(req.getMomEndDate()).map(DateUtil::toFormatDate).ifPresent(vo::setMomEndDate);
        Optional.of(req.getMarketplaceIdList()).filter(CollectionUtils::isNotEmpty).ifPresent(vo::setMarketplaceIdList);
        Optional.of(req.getLimit()).ifPresent(vo::setLimit);
        Optional.of(req.getShopIdList()).filter(CollectionUtils::isNotEmpty).ifPresent(vo::setShopIdList);
        vo.setPageNo(Optional.of(req.getPageNo()).orElse(0));
        vo.setPageSize(Optional.of(req.getPageSize()).orElse(10));
        DateTimeUtil.resetTimeRange(vo, DashboardModuleEnum.CAMPAIGN);
        Optional.of(req.getPortfolioIdsList()).filter(CollectionUtils::isNotEmpty).ifPresent(vo::setPortfolioIds);
        Optional.of(req.getCampaignIdsList()).filter(CollectionUtils::isNotEmpty).ifPresent(vo::setCampaignIds);
        return vo;
    }
}
