package com.meiyunji.sponsored.api.sb;

import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.adCommon.AsyncAddTargetingRequest;
import com.meiyunji.sponsored.rpc.sb.neTarget.*;
import com.meiyunji.sponsored.rpc.vo.AsyncAddTargetingRpcVo;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetObjectTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskConstant;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskMatchTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskTypeEnum;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetTaskDto;
import com.meiyunji.sponsored.service.cpc.po.TargetTypeEnum;
import com.meiyunji.sponsored.service.cpc.service2.IAdTargetTaskService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbNeTargetService;
import com.meiyunji.sponsored.service.cpc.vo.AddSbNeTargetingVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@GRpcService
@Slf4j
public class SbNeTargetRpcService extends RPCCpcSbNeTargetServiceGrpc.RPCCpcSbNeTargetServiceImplBase {

    @Autowired
    private ICpcSbNeTargetService cpcSbNeTargetService;
    @Autowired
    private IAdTargetTaskService adTargetTaskService;

    @Override
    public void create(CreateRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        List<SbNeTargetsVo> neTargetsVosList = request.getNeTargetsVosList();
        if (!request.hasShopId() || StringUtils.isBlank(request.getCampaignId()) || StringUtils.isBlank(request.getGroupId()) || CollectionUtils.isEmpty(neTargetsVosList)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            AddSbNeTargetingVo vo = new AddSbNeTargetingVo();
            vo.setShopId(request.getShopId().getValue());
            vo.setPuid(request.getPuid().getValue());
            vo.setUid(request.getUid().getValue());
            vo.setCampaignId(request.getCampaignId());
            vo.setGroupId(request.getGroupId());
            vo.setIp(request.getIp());

            List<com.meiyunji.sponsored.service.cpc.vo.SbNeTargetsVo> voList = new ArrayList<>(neTargetsVosList.size());
            com.meiyunji.sponsored.service.cpc.vo.SbNeTargetsVo sbNeTargetsVo;
            for (SbNeTargetsVo targetsVo : neTargetsVosList) {
                sbNeTargetsVo = new com.meiyunji.sponsored.service.cpc.vo.SbNeTargetsVo();
                if (StringUtils.isNotBlank(targetsVo.getTargetId())) {
                    sbNeTargetsVo.setTargetId(targetsVo.getTargetId());
                }
                if (StringUtils.isNotBlank(targetsVo.getErrMsg())) {
                    sbNeTargetsVo.setErrMsg(targetsVo.getErrMsg());
                }
                if (StringUtils.isNotBlank(targetsVo.getType())) {
                    sbNeTargetsVo.setType(targetsVo.getType());
                }
                if (StringUtils.isNotBlank(targetsVo.getAsin())) {
                    sbNeTargetsVo.setAsin(targetsVo.getAsin());
                }
                if (StringUtils.isNotBlank(targetsVo.getTitle())) {
                    sbNeTargetsVo.setTitle(targetsVo.getTitle());
                }
                if (StringUtils.isNotBlank(targetsVo.getImgUrl())) {
                    sbNeTargetsVo.setImgUrl(targetsVo.getImgUrl());
                }
                if (StringUtils.isNotBlank(targetsVo.getBrandName())) {
                    sbNeTargetsVo.setBrandName(targetsVo.getBrandName());
                }
                if (StringUtils.isNotBlank(targetsVo.getBrandId())) {
                    sbNeTargetsVo.setBrandId(targetsVo.getBrandId());
                }

                voList.add(sbNeTargetsVo);
            }
            vo.setNeTargetsVos(voList);

            Result result = cpcSbNeTargetService.create(vo);
            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void asyncCreate(AsyncAddTargetingRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sb-async-targeting-创建否定投放 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        String error = verifyAsyncNeParam(request);
        if (StringUtils.isNotBlank(error)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(error);
        } else {
            AdTargetTaskDto adTargetTaskDto = new AdTargetTaskDto();
            adTargetTaskDto.setPuid(request.getPuid().getValue());
            adTargetTaskDto.setShopId(request.getShopId().getValue());
            adTargetTaskDto.setUid(request.getUid().getValue());
            adTargetTaskDto.setType(AdTargetTaskTypeEnum.SB_NEGATIVE_TARGETS.getCode());
            adTargetTaskDto.setLoginIp(request.getLoginIp());
            adTargetTaskDto.setTargetingType(request.getTargetingType());
            adTargetTaskDto.setTargetPageType(request.getTargetingPageType().getValue());
            adTargetTaskDto.setSourceAdCampaignId(request.getSourceAdCampaignId());
            adTargetTaskDto.setSourceShopId(request.getSourceShopId().getValue());
            List<AdTargetTaskDto.AdTargetTaskDetailDto> detailList = request.getTargetingList().stream().filter(Objects::nonNull).map(item -> {
                AdTargetTaskDto.AdTargetTaskDetailDto detail = new AdTargetTaskDto.AdTargetTaskDetailDto();
                if (item.hasSourceShopId()) {
                    detail.setSourceShopId(item.getSourceShopId().getValue());
                }  else {
                    detail.setSourceShopId(request.getSourceShopId().getValue());
                }
                detail.setAdCampaignId(item.getAdCampaignId());
                detail.setAdGroupId(item.getAdGroupId());
                detail.setMatchType(item.getExpressionType());
                if (StringUtils.isNotBlank(item.getTargetId())) {
                    detail.setTargetId(item.getTargetId());
                } else {
                    detail.setTargetObject(item.getAsin());
                    detail.setTargetObjectDesc(item.getTitle());
                    detail.setImgUrl(item.getImgUrl());
                }
                detail.setTargetObjectType(AdTargetObjectTypeEnum.getCodeByTargetType(item.getType()));
                return detail;
            }).collect(Collectors.toList());
            adTargetTaskDto.setTaskDetails(detailList);
            long taskId = adTargetTaskService.recordTargetTask(adTargetTaskDto);
            adTargetTaskService.executeTask(taskId);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private String verifyAsyncNeParam(AsyncAddTargetingRequest request) {
        if (!request.hasShopId() || !request.hasPuid() || !request.hasTargetingPageType()
                || CollectionUtils.isEmpty(request.getTargetingList()) || request.getTargetingList().size() > AdTargetTaskConstant.MAX_TARGET_SIZE) {
            return "请求参数错误";
        }
        List<AsyncAddTargetingRpcVo> list = request.getTargetingList();
        // 如果第一个包含targetId,那么列表中所有的对象都需要包含targetId
        int targetIdNum = 0;
        for (AsyncAddTargetingRpcVo each : list) {
            boolean haveTargetId = StringUtils.isNotBlank(each.getTargetId());
            if (haveTargetId) {
                targetIdNum++;
            }
            if (!AdTargetTaskMatchTypeEnum.SB_NE_TARGET_SUPPORT_TYPES.contains(each.getExpressionType())) {
                return "请求参数错误,存在不支持的筛选条件";
            }
            if (!AdTargetObjectTypeEnum.SB_NE_TARGET_SUPPORT_TYPES.contains(each.getType())) {
                return "请求参数错误,存在不支持的投放类型";
            }
            if (TargetTypeEnum.brand.name().equals(each.getType()) && !haveTargetId) {
                return "请求参数错误";
            }
            if (TargetTypeEnum.asin.name().equals(each.getType()) && !haveTargetId && StringUtils.isBlank(each.getAsin())) {
                return "请求参数错误";
            }
        }
        if (targetIdNum > 0 && targetIdNum < list.size() || (!request.hasSourceShopId() && !request.getTargetingList().get(0).hasSourceShopId()) || StringUtils.isBlank(request.getTargetingType())) {
            return "请求参数错误";
        }
        return StringUtils.EMPTY;
    }

    @Override
    public void archive(ArchiveRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasShopId() || StringUtils.isBlank(request.getTargetId())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            Result result = cpcSbNeTargetService.archive(request.getPuid().getValue(), request.getShopId().getValue(),
                    request.getUid().getValue(), request.getTargetId(), request.getIp());
            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }


    /**
     * sb--否定产品投放-批量归档
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void batchArchive(BatchArchiveSbNeTargetingRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sb--否定产品投放-批量归档 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasShopId() || !request.hasIdList() || !request.hasPuid() || !request.hasUid()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
        } else {
            List<String> idList = Arrays.stream(request.getIdList().split(",")).collect(Collectors.toList());
            Result result = cpcSbNeTargetService.batchArchiveNeTargeting(request.getPuid().getValue(), request.getShopId().getValue(),
                    request.getUid().getValue(), idList, request.getIp());

            builder.setCode(Int32Value.newBuilder().setValue(result.getCode()).build());
            if (result.getData() != null) {
                builder.setData(JSONUtil.objectToJson(result.getData()));
            }
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void batchAddNeTargeting(CreateRequest request, StreamObserver<BatchSbNetargetingResponse> responseObserver) {
        BatchSbNetargetingResponse.Builder builder = BatchSbNetargetingResponse.newBuilder();

        List<SbNeTargetsVo> neTargetsVosList = request.getNeTargetsVosList();
        if (!request.hasShopId() || CollectionUtils.isEmpty(neTargetsVosList)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            AddSbNeTargetingVo vo = new AddSbNeTargetingVo();
            vo.setShopId(request.getShopId().getValue());
            vo.setPuid(request.getPuid().getValue());
            vo.setUid(request.getUid().getValue());

            List<com.meiyunji.sponsored.service.cpc.vo.SbNeTargetsVo> voList = new ArrayList<>(neTargetsVosList.size());
            com.meiyunji.sponsored.service.cpc.vo.SbNeTargetsVo sbNeTargetsVo;
            for (SbNeTargetsVo targetsVo : neTargetsVosList) {
                sbNeTargetsVo = new com.meiyunji.sponsored.service.cpc.vo.SbNeTargetsVo();
                sbNeTargetsVo.setCampaignId(targetsVo.getCampaignId());
                sbNeTargetsVo.setGroupId(targetsVo.getGroupId());
                sbNeTargetsVo.setIndex(targetsVo.getIndex());
                if (StringUtils.isNotBlank(targetsVo.getTargetId())) {
                    sbNeTargetsVo.setTargetId(targetsVo.getTargetId());
                }
                if (StringUtils.isNotBlank(targetsVo.getErrMsg())) {
                    sbNeTargetsVo.setErrMsg(targetsVo.getErrMsg());
                }
                if (StringUtils.isNotBlank(targetsVo.getType())) {
                    sbNeTargetsVo.setType(targetsVo.getType());
                }
                if (StringUtils.isNotBlank(targetsVo.getAsin())) {
                    sbNeTargetsVo.setAsin(targetsVo.getAsin());
                }
                if (StringUtils.isNotBlank(targetsVo.getTitle())) {
                    sbNeTargetsVo.setTitle(targetsVo.getTitle());
                }
                if (StringUtils.isNotBlank(targetsVo.getImgUrl())) {
                    sbNeTargetsVo.setImgUrl(targetsVo.getImgUrl());
                }
                if (StringUtils.isNotBlank(targetsVo.getBrandName())) {
                    sbNeTargetsVo.setBrandName(targetsVo.getBrandName());
                }
                if (StringUtils.isNotBlank(targetsVo.getBrandId())) {
                    sbNeTargetsVo.setBrandId(targetsVo.getBrandId());
                }

                voList.add(sbNeTargetsVo);
            }
            vo.setNeTargetsVos(voList);

            Result<BatchSbDataResponse> result = cpcSbNeTargetService.batchAddNeTargeting(vo);
            // 错误信息返回：code = error返回给result.msg; code = success返回给data里的failmsg
            builder.setCode(Int32Value.of(result.getCode()));
            if (result.error()) {
                builder.setMsg(result.getMsg());
            }
            if (result.getData() != null) {
                builder.setData(result.getData());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
