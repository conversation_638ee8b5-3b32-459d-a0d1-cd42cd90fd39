package com.meiyunji.sponsored.api.query;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.amazon.advertising.mode.keywords.BidRecommendationKeyword;
import com.amazon.advertising.mode.keywords.SuggestedBid;
import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.mode.targeting.TargetingBidRecommendation;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.DoubleValue;
import com.google.protobuf.Int32Value;
import com.meiyunji.sellfox.aadras.types.enumeration.AddNegativeTargetType;
import com.meiyunji.sellfox.aadras.types.enumeration.RuleIndexType;
import com.meiyunji.sellfox.aadras.types.enumeration.RuleOperatorType;
import com.meiyunji.sellfox.aadras.types.enumeration.RuleStatisticalModeType;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.enums.ZoneNameEnum;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.grpc.common.AdHourReportResponsePb;
import com.meiyunji.sponsored.rpc.query.*;
import com.meiyunji.sponsored.rpc.vo.AdAdvancedFilterData;
import com.meiyunji.sponsored.rpc.vo.ReportRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.constants.AdManagePageExportTaskTypeEnum;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeyword;
import com.meiyunji.sponsored.service.cpc.service.*;
import com.meiyunji.sponsored.service.cpc.service2.IAdManagePageExportTaskService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.enums.AdvertisingQueryKeywordExportFieldEnum;
import com.meiyunji.sponsored.service.enums.AdvertisingQueryTargetExportFieldEnum;
import com.meiyunji.sponsored.service.enums.SbMatchValueEnum;
import com.meiyunji.sponsored.service.enums.SpKeywordGroupValueEnum;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.util.ExportStringUtil;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.KeywordViewHourParam;
import com.meiyunji.sponsored.service.reportHour.utils.ReportChartUtil;
import com.meiyunji.sponsored.service.reportHour.vo.AdQueryKeywordAndTargetVo;
import com.meiyunji.sponsored.service.util.PbUtil;
import com.meiyunji.sponsored.service.util.ZoneUtil;
import com.meiyunji.sponsored.util.ProtoBufUtil;
import io.grpc.stub.ServerCallStreamObserver;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2021/11/1 10:49
 * @describe: 广告搜索词
 */
@GRpcService
@Slf4j
public class AdQueryRpcService extends RPCAdQueryServiceGrpc.RPCAdQueryServiceImplBase {
    @Autowired
    private ICpcQueryKeywordReportService cpcQueryKeywordReportService;
    @Autowired
    private ICpcQueryTargetingReportService cpcQueryTargetingReportService;
    @Autowired
    private ICpcService cpcService;
    @Autowired
    private ICpcSbQueryKeywordReportService cpcSbQueryKeywordReportService;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private ICpcQueryReportDetailService cpcQueryReportDetailService;
    @Resource
    private CpcShopDataService cpcShopDataService;
    @Resource
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;
    @Autowired
    private IAdManagePageExportTaskService adManagePageExportTaskService;

    /**
     * 获取站点与北京时区差
     * @param request
     * @param responseObserver
     */
    @Override
    public void getZoneDiff(ZoneDiffRequest request, StreamObserver<ZoneDiffResponse> responseObserver) {
        log.info("ad-query-获取站点与北京时区差 request: {}",request);
        ZoneDiffResponse.Builder builder = ZoneDiffResponse.newBuilder();
        if (StringUtils.isBlank(request.getMarketplaceId())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            ZoneDiff zoneDiff = getZoneDiff(request.getMarketplaceId());
            Map<String,String> data = Maps.newHashMapWithExpectedSize(2);
            data.put("diff", zoneDiff.getDiff());
            data.put("name",zoneDiff.getName());
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.putAllData(data);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 批量获取站点与北京时区差
     */
    public void getZoneDiffList(ZoneDiffRequest request, StreamObserver<ZoneDiffListResponse> responseObserver) {
        ZoneDiffListResponse.Builder builder = ZoneDiffListResponse.newBuilder();
        if(CollectionUtil.isEmpty(request.getMarketplaceIdListList())){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
        List<ZoneDiff> zoneDiffs = new ArrayList<>();
        for (String marketPlaceId : request.getMarketplaceIdListList()) {
            ZoneDiff zoneDiff = getZoneDiff(marketPlaceId);
            zoneDiffs.add(zoneDiff);
        }
        builder.addAllData(zoneDiffs);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 获取站点与北京时区差
     */
    private ZoneDiff getZoneDiff(String marketPlaceId) {
        ZoneDiff.Builder builder = ZoneDiff.newBuilder();
        MarketTimezoneAndCurrencyEnum marketTimezone = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(marketPlaceId);
        Double diff = null;
        String name = null;
        if(marketTimezone != null){
            diff = DoubleUtil.divide(Double.parseDouble(ZonedDateTime.now(ZoneId.of(marketTimezone.getTimezone())).getOffset().getTotalSeconds() + ""), 60*60D);
            name = ZoneNameEnum.fromType(marketTimezone.getTimezone()).getName();
        }
        if (diff == null || name == null){
            ZoneId zoneId = ZoneUtil.getZoneIdByAmzSite(marketPlaceId);
            diff = DoubleUtil.divide(Double.parseDouble(ZonedDateTime.now(zoneId).getOffset().getTotalSeconds() + ""), 60*60D);
            name = ZoneUtil.getZoneName(marketPlaceId);
        }
        builder.setDiff(diff.toString());
        builder.setName(name);
        return builder.build();
    }

    /**
     * 搜索词(querywords)列表
     * @param request
     * @param responseObserver
     */
    @Override
    public void queryWords(QueryWordsRequest request, StreamObserver<QueryWordsResponse> responseObserver) {
        log.info("ad-query-搜索词(querywords)列表 request: {}", request);
        QueryWordsResponse.Builder builder = QueryWordsResponse.newBuilder();

        CpcQueryWordDto dto = new CpcQueryWordDto();
        dto.setPuid(request.getPuid().getValue());
        dto.setShopId(request.getShopId().getValue());
        dto.setUid(request.getUid().getValue());
        dto.setLoginIp(request.getLoginIp());
        dto.setMarketplaceId(request.getMarketplaceId());
        dto.setMatchType(request.getMatchType());
        dto.setStart(request.getStart());
        dto.setEnd(request.getEnd());
        dto.setSearchType(request.getSearchType());
        dto.setSearchField(request.getSearchField());
        dto.setSearchValue(request.getSearchValue());
        dto.setOrderField(request.getOrderField());
        dto.setOrderValue(request.getOrderValue());
        dto.setOrderType(request.getOrderType());
        dto.setState(request.getState());
        dto.setStatus(request.getStatus());
        dto.setCampaignId(request.getCampaignId());
        dto.setGroupId(request.getGroupId());
        dto.setCampaignIds(request.getCampaignIdsList());
        dto.setUseAdvanced(request.getUseAdvanced().getValue());
        dto.setImpressionsMin(request.hasImpressionsMin() ? request.getImpressionsMin().getValue(): null);
        dto.setImpressionsMax(request.hasImpressionsMax() ?request.getImpressionsMax().getValue(): null);
        dto.setClicksMin(request.hasClicksMin() ? request.getClicksMin().getValue(): null);
        dto.setClicksMax(request.hasClicksMax() ? request.getClicksMax().getValue(): null);
        dto.setClickRateMin(request.hasClickRateMin() ? BigDecimal.valueOf(request.getClickRateMin().getValue()): null);
        dto.setClickRateMax(request.hasClickRateMax() ? BigDecimal.valueOf(request.getClickRateMax().getValue()): null);
        dto.setCostMin(request.hasCostMin() ? BigDecimal.valueOf(request.getCostMin().getValue()): null);
        dto.setCostMax(request.hasCostMax() ? BigDecimal.valueOf(request.getCostMax().getValue()): null);
        dto.setCpcMin(request.hasCpaMin() ? BigDecimal.valueOf(request.getCpcMin().getValue()): null);
        dto.setCpcMax(request.hasCpaMax() ? BigDecimal.valueOf(request.getCpcMax().getValue()): null);
        dto.setOrderNumMin(request.hasOrderNumMin() ? request.getOrderNumMin().getValue(): null);
        dto.setOrderNumMax(request.hasOrderNumMax() ? request.getOrderNumMax().getValue(): null);
        dto.setSalesConversionRateMin(request.hasSalesConversionRateMin() ? BigDecimal.valueOf(request.getSalesConversionRateMin().getValue()): null);
        dto.setSalesConversionRateMax(request.hasSalesConversionRateMax() ? BigDecimal.valueOf(request.getSalesConversionRateMax().getValue()): null);
        dto.setCpaMin(request.hasCpaMin() ? BigDecimal.valueOf(request.getCpaMin().getValue()): null);
        dto.setCpaMax(request.hasCpaMax() ? BigDecimal.valueOf(request.getCpaMax().getValue()): null);
        dto.setTotalSalesMin(request.hasTotalSalesMin() ? BigDecimal.valueOf(request.getTotalSalesMin().getValue()): null);
        dto.setTotalSalesMax(request.hasTotalSalesMax() ? BigDecimal.valueOf(request.getTotalSalesMax().getValue()): null);
        dto.setAcosMin(request.hasAcosMin() ? BigDecimal.valueOf(request.getAcosMin().getValue()): null);
        dto.setAcosMax(request.hasAcosMax() ? BigDecimal.valueOf(request.getAcosMax().getValue()) : null);


        //做参数校验
        if(!request.hasPuid() || !request.hasShopId() || checkQueryWordDto(dto)){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            Page page = new Page(request.hasPageNo() ? request.getPageNo().getValue() : 1,request.hasPageSize() ? request.getPageSize().getValue() : 20);
            page = cpcQueryKeywordReportService.pageList(dto.getPuid(),dto,page);
            //获取汇总数据
            ReportVo sum = null;
            if(page.getRows() != null && page.getRows().size()>0){
                sum = cpcQueryKeywordReportService.sumReport(dto.getPuid(),dto);
            }
            //处理返回数据
            builder.setCode(Int32Value.of(Result.SUCCESS));

            QueryPage.Builder pageBuilder = QueryPage.newBuilder();
            pageBuilder.setPageNo(Int32Value.of(page.getPageNo()));
            pageBuilder.setPageSize(Int32Value.of(page.getPageSize()));
            pageBuilder.setTotalPage(Int32Value.of(page.getTotalPage()));
            pageBuilder.setTotalSize(Int32Value.of(page.getTotalSize()));

            //处理分页中list
            QueryWordsResponse.QueryWordsResponseData.Builder dataBuilder = QueryWordsResponse.QueryWordsResponseData.newBuilder();

            List<ReportVo> rows = page.getRows();
            if (CollectionUtils.isNotEmpty(rows)) {
                List<ReportRpcVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                    return convertVoToRpcMessage(item).build();
                }).collect(Collectors.toList());
                pageBuilder.addAllRows(rpcVos);
            }
            dataBuilder.setPage(pageBuilder.build());

            if (sum!=null) {
                ReportRpcVo rpcVo = convertVoToRpcMessage(sum).build();
                dataBuilder.setSum(rpcVo);
            }
            builder.setData(dataBuilder.build());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
    /**
     * vo转message
     */
    private ReportRpcVo.Builder convertVoToRpcMessage(ReportVo reportVo) {
        ReportRpcVo.Builder reportBuilder = ReportRpcVo.newBuilder();

        if (reportVo!=null) {
            if (reportVo.getShopId() != null) {
                reportBuilder.setShopId(Int32Value.of(reportVo.getShopId()));
            }
            if (reportVo.getCountDate() != null) {
                reportBuilder.setCountDate(reportVo.getCountDate());
            }
            reportBuilder.setCpc(DoubleValue.of(Optional.ofNullable(reportVo.getCpc()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setCost(DoubleValue.of(Optional.ofNullable(reportVo.getCost()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setSales(DoubleValue.of(Optional.ofNullable(reportVo.getSales()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setAcos(DoubleValue.of(Optional.ofNullable(reportVo.getAcos()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setRoas(DoubleValue.of(Optional.ofNullable(reportVo.getRoas()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setAcots(DoubleValue.of(Optional.ofNullable(reportVo.getAcots()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setAsots(DoubleValue.of(Optional.ofNullable(reportVo.getAsots()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setImpressions(Int32Value.of(Optional.ofNullable(reportVo.getImpressions()).orElse(0)));
            reportBuilder.setClicks(Int32Value.of(Optional.ofNullable(reportVo.getClicks()).orElse(0)));
            reportBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(reportVo.getOrderNum()).orElse(0)));
            reportBuilder.setSaleNum(Int32Value.of(Optional.ofNullable(reportVo.getSaleNum()).orElse(0)));
            reportBuilder.setClickRate(DoubleValue.of(Optional.ofNullable(reportVo.getClickRate()).orElse(0.0)));
            reportBuilder.setSalesConversionRate(DoubleValue.of(Optional.ofNullable(reportVo.getSalesConversionRate()).orElse(0.0)));
            reportBuilder.setAdConversionRate(DoubleValue.of(Optional.ofNullable(reportVo.getAdConversionRate()).orElse(0.0)));
            reportBuilder.setCpa(DoubleValue.of(Optional.ofNullable(reportVo.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setNaturalSales(DoubleValue.of(Optional.ofNullable(reportVo.getNaturalSales()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setNaturalClicks(Optional.ofNullable(reportVo.getNaturalClicks()).orElse("0"));
            reportBuilder.setNaturalOrderNum(Int32Value.of(Optional.ofNullable(reportVo.getNaturalOrderNum()).orElse(0)));
            reportBuilder.setAdClickRatio(DoubleValue.of(Optional.ofNullable(reportVo.getAdClickRatio()).orElse(0.0)));
            reportBuilder.setAdConversionRate(DoubleValue.of(Optional.ofNullable(reportVo.getAdConversionRate()).orElse(0.0)));
            // 广告花费占比
            reportBuilder.setAdCostPercentage(StringUtils.isNotBlank(reportVo.getAdCostPercentage()) ? reportVo.getAdCostPercentage() : "0.00");
            // 广告销售额占比
            reportBuilder.setAdSalePercentage(StringUtils.isNotBlank(reportVo.getAdSalePercentage()) ? reportVo.getAdSalePercentage() : "0.00");
            // 广告订单量占比
            reportBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(reportVo.getAdOrderNumPercentage()) ? reportVo.getAdOrderNumPercentage() : "0.00");
            // 广告销量占比
            reportBuilder.setOrderNumPercentage(StringUtils.isNotBlank(reportVo.getOrderNumPercentage()) ? reportVo.getOrderNumPercentage() : "0.00");

            if (reportVo.getCampaignId() != null) {
                reportBuilder.setCampaignId(reportVo.getCampaignId());
            }
            if (reportVo.getCampaignName() != null) {
                reportBuilder.setCampaignName(reportVo.getCampaignName());
            }
            if (reportVo.getAdGroupId() != null) {
                reportBuilder.setAdGroupId(reportVo.getAdGroupId());
            }
            if (reportVo.getAdGroupName() != null) {
                reportBuilder.setAdGroupName(reportVo.getAdGroupName());
            }
            if (reportVo.getKeywordText() != null) {
                reportBuilder.setKeywordText(reportVo.getKeywordText());
            }
            if (reportVo.getMatchType() != null) {
                reportBuilder.setMatchType(reportVo.getMatchType());
            }
            if (reportVo.getKeywordId() != null) {
                reportBuilder.setKeywordId(reportVo.getKeywordId());
            }
            if (reportVo.getSku() != null) {
                reportBuilder.setSku(reportVo.getSku());
            }
            if (reportVo.getAsin() != null) {
                reportBuilder.setAsin(reportVo.getAsin());
            }
            if (reportVo.getParentAsin() != null) {
                reportBuilder.setParentAsin(reportVo.getParentAsin());
            }
            if (reportVo.getTitle() != null) {
                reportBuilder.setTitle(reportVo.getTitle());
            }
            if (reportVo.getMainImage() != null) {
                reportBuilder.setMainImage(reportVo.getMainImage());
            }
            if (reportVo.getQuery() != null) {
                reportBuilder.setQuery(reportVo.getQuery());
            }
            if (reportVo.getQueryCn() != null) {
                reportBuilder.setQueryCn(reportVo.getQueryCn());
            }
            if (reportVo.getNegaType() != null) {
                reportBuilder.setNegaType(reportVo.getNegaType());
            }
            if (reportVo.getTargetingType() != null) {
                reportBuilder.setTargetingType(reportVo.getTargetingType());
            }
            if (reportVo.getTargetingExpression() != null) {
                reportBuilder.setTargetingExpression(reportVo.getTargetingExpression());
            }
            if (reportVo.getTargetId() != null) {
                reportBuilder.setTargetId(reportVo.getTargetId());
            }
            if (reportVo.getAdId() != null) {
                reportBuilder.setAdId(reportVo.getAdId());
            }
            if (reportVo.getTargetingText() != null) {
                reportBuilder.setTargetingText(reportVo.getTargetingText());
            }
            if (reportVo.getSpCampaignType() != null) {
                reportBuilder.setSpCampaignType(reportVo.getSpCampaignType());
            }
            if (reportVo.getSpGroupType() != null) {
                reportBuilder.setSpGroupType(reportVo.getSpGroupType());
            }
            if (reportVo.getSpTargetType() != null) {
                reportBuilder.setSpTargetType(reportVo.getSpTargetType());
            }
            if (reportVo.getPortfolioName() != null) {
                reportBuilder.setPortfolioName(reportVo.getPortfolioName());
            }
            /**
             * TODO 广告报告重构
             * 本广告产品销售额
             */
            reportBuilder.setAdSales(DoubleValue.of(Optional.ofNullable(reportVo.getAdSales()).orElse(BigDecimal.ZERO).doubleValue()));
            //本广告产品订单量
            reportBuilder.setAdSaleNum(Int32Value.of(Optional.ofNullable(reportVo.getAdSaleNum()).orElse(0)));
            //本广告产品销量
            reportBuilder.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(reportVo.getAdSelfSaleNum()).orElse(0)));
            //其他产品广告订单量
            reportBuilder.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(reportVo.getAdOtherOrderNum()).orElse(0)));
            //其他产品广告销售额
            reportBuilder.setAdOtherSales(DoubleValue.of(Optional.ofNullable(reportVo.getAdOtherSales()).orElse(BigDecimal.ZERO).doubleValue()));
            //其他产品广告销量
            reportBuilder.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(reportVo.getAdOtherSaleNum()).orElse(0)));
            //“品牌新买家”订单量
            if (reportVo.getOrdersNewToBrand14d() != null) {
                reportBuilder.setOrdersNewToBrandFTD(Int32Value.of(reportVo.getOrdersNewToBrand14d()));
            }
            //“品牌新买家”销售额
            if (reportVo.getSalesNewToBrand14d() != null) {
                reportBuilder.setSalesNewToBrandFTD(DoubleValue.of(reportVo.getSalesNewToBrand14d().doubleValue()));
            }
            //搜索词展示量排名
            if (reportVo.getImpressionRank() != null) {
                reportBuilder.setImpressionRank(Int32Value.of(reportVo.getImpressionRank()));
            }
            //搜索词展示份额
            if (reportVo.getImpressionShare() != null) {
                reportBuilder.setImpressionShare(DoubleValue.of(reportVo.getImpressionShare().doubleValue()));
            }
            //“品牌新买家”订单百分比
            if (reportVo.getOrderRateNewToBrand14d() != null) {
                reportBuilder.setOrderRateNewToBrandFTD(DoubleValue.of(reportVo.getOrderRateNewToBrand14d()));
            }
            //“品牌新买家”销售额百分比
            if (reportVo.getSalesRateNewToBrand14d() != null) {
                reportBuilder.setSalesRateNewToBrandFTD(DoubleValue.of(reportVo.getSalesRateNewToBrand14d()));
            }
            //“品牌新买家”订单转化率
            if (reportVo.getOrdersNewToBrandPercentage14d() != null) {
                reportBuilder.setOrdersNewToBrandPercentageFTD(DoubleValue.of(reportVo.getOrdersNewToBrandPercentage14d()));
            }

            reportBuilder.setVideo5SecondViews(Optional.ofNullable(reportVo.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
            reportBuilder.setVideo5SecondViewRate(Optional.ofNullable(reportVo.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
            reportBuilder.setVideoFirstQuartileViews(Optional.ofNullable(reportVo.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
            reportBuilder.setVideoMidpointViews(Optional.ofNullable(reportVo.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
            reportBuilder.setVideoThirdQuartileViews(Optional.ofNullable(reportVo.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
            reportBuilder.setVideoCompleteViews(Optional.ofNullable(reportVo.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
            reportBuilder.setVideoUnmutes(Optional.ofNullable(reportVo.getVideoUnmutes()).map(String::valueOf).orElse("0"));
            reportBuilder.setViewImpressions(Optional.ofNullable(reportVo.getViewImpressions()).map(String::valueOf).orElse("0"));
            reportBuilder.setViewabilityRate(Optional.ofNullable(reportVo.getViewabilityRate()).map(String::valueOf).orElse("0"));
            reportBuilder.setViewClickThroughRate(Optional.ofNullable(reportVo.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
            reportBuilder.setAdvertisingUnitPrice(Optional.ofNullable(reportVo.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
            reportBuilder.setSearchFrequencyRank(reportVo.getSearchFrequencyRank());
            reportBuilder.setWeekRatio(Optional.ofNullable(reportVo.getWeekRatio()).map(String::valueOf).orElse("0"));
        }

        return reportBuilder;
    }

    /**
     * 搜索词(querywords)列表导出
     * @param request
     * @param responseObserver
     */
    @Override
    public void queryWordsExcel(QueryWordsExcelRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("ad-query-搜索词(querywords)列表导出 request: {}", request);
        QueryWordsExcelResponse.Builder builder = QueryWordsExcelResponse.newBuilder();
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        CpcQueryWordDto dto = new CpcQueryWordDto();
        dto.setPuid(request.getPuid().getValue());
        dto.setShopId(request.getShopId().getValue());
        dto.setUid(request.getUid().getValue());
        dto.setLoginIp(request.getLoginIp());
        dto.setMarketplaceId(request.getMarketplaceId());
        dto.setMatchType(request.getMatchType());
        dto.setStart(request.getStart());
        dto.setEnd(request.getEnd());
        dto.setSearchType(request.getSearchType());
        dto.setSearchField(request.getSearchField());
        dto.setSearchValue(request.getSearchValue());
        dto.setOrderField(request.getOrderField());
        dto.setOrderValue(request.getOrderType()); //兼容
        dto.setOrderType(request.getOrderType());
        dto.setState(request.getState());
        dto.setStatus(request.getStatus());
        dto.setCampaignId(request.getCampaignId());
        dto.setGroupId(request.getGroupId());
        dto.setCampaignIds(request.getCampaignIdsList());
        dto.setUseAdvanced(request.getUseAdvanced().getValue());
        dto.setPortfolioId(request.getPortfolioId());
        dto.setType(request.getType());
        dto.setWordRoot(request.getWordRoot());
        dto.setServingStatus(request.getServingStatus());
        dto.setUuid(request.getUuid());
        dto.setExcludList(request.getExcludList());
        AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
        if (advancedFilter != null) {  //高级筛选
            dto.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            dto.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);
            dto.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            dto.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            dto.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            dto.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            dto.setClickRateMin(advancedFilter.hasClickRateMin() ?  MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMin()),BigDecimal.valueOf(100)) : null);
            dto.setClickRateMax(advancedFilter.hasClickRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMax()),BigDecimal.valueOf(100)) : null);
            dto.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            dto.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            dto.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            dto.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            dto.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            dto.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            dto.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            dto.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            dto.setAcosMin(advancedFilter.hasAcosMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMin()),BigDecimal.valueOf(100)) : null);
            dto.setAcosMax(advancedFilter.hasAcosMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMax()),BigDecimal.valueOf(100)) : null);
            dto.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            dto.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            dto.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()),BigDecimal.valueOf(100)) : null);
            dto.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()),BigDecimal.valueOf(100)): null);
            dto.setAcotsMin(advancedFilter.hasAcotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMin()),BigDecimal.valueOf(100)) :null);
            dto.setAcotsMax(advancedFilter.hasAcotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMax()),BigDecimal.valueOf(100)) : null);
            dto.setAsotsMin(advancedFilter.hasAsotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMin()),BigDecimal.valueOf(100)) :null);
            dto.setAsotsMax(advancedFilter.hasAsotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMax()),BigDecimal.valueOf(100)) : null);

            dto.setVideo5SecondViewsMin(advancedFilter.hasVideo5SecondViewsMin() ? advancedFilter.getVideo5SecondViewsMin() : null);
            dto.setVideo5SecondViewsMax(advancedFilter.hasVideo5SecondViewsMax() ? advancedFilter.getVideo5SecondViewsMax() : null);
            dto.setVideoCompleteViewsMin(advancedFilter.hasVideoCompleteViewsMin() ? advancedFilter.getVideoCompleteViewsMin() : null);
            dto.setVideoCompleteViewsMax(advancedFilter.hasVideoCompleteViewsMax() ? advancedFilter.getVideoCompleteViewsMax() : null);
            dto.setViewabilityRateMin(advancedFilter.hasViewabilityRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMin())) : null);
            dto.setViewabilityRateMax(advancedFilter.hasViewabilityRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMax())) : null);
            dto.setViewClickThroughRateMin(advancedFilter.hasViewClickThroughRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMin())) : null);
            dto.setViewClickThroughRateMax(advancedFilter.hasViewClickThroughRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMax())) : null);
            dto.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            dto.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            dto.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            dto.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            dto.setSearchFrequencyRankMin(advancedFilter.hasSearchFrequencyRankMin() ? advancedFilter.getSearchFrequencyRankMin() : null);
            dto.setSearchFrequencyRankMax(advancedFilter.hasSearchFrequencyRankMax() ? advancedFilter.getSearchFrequencyRankMax() : null);
            dto.setWeekRatioMin(advancedFilter.hasWeekRatioMin() ? new BigDecimal(String.valueOf(advancedFilter.getWeekRatioMin())) : null);
            dto.setWeekRatioMax(advancedFilter.hasWeekRatioMax() ? new BigDecimal(String.valueOf(advancedFilter.getWeekRatioMax())) : null);
        }

        if (request.hasFilterTargetType()) {
            dto.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasFilterMatchType()) {
            dto.setFilterMatchType(request.getFilterMatchType());
        }
        if (request.hasFilterAddProperty()) {
            dto.setFilterAddProperty(request.getFilterAddProperty());
        }

        if (CollectionUtils.isNotEmpty(request.getQueryWordTagTypeList())) {
            dto.setQueryWordTagTypeList(request.getQueryWordTagTypeList());
        }
        if (request.hasExportSortField()) {
            dto.setExportSortField(request.getExportSortField());
        }
        if (request.hasFreezeNum()) {
            dto.setFreezeNum(request.getFreezeNum());
        }
        if (CollectionUtils.isNotEmpty(request.getAdStrategyTypeListList())) {
            dto.setAdStrategyTypeList(request.getAdStrategyTypeListList());
        }

        dto.setAdminUser(BooleanUtils.toBoolean(MDC.get(Constants.ADMIN_USER)));

        //做参数校验
        String err = checkQueryWordExportDto(dto);
        if (StringUtils.isNotBlank(err)) {
            throw new SponsoredBizException(err);
        }

        Long id = adManagePageExportTaskService.saveExportTask(dto.getPuid(), dto.getUid(), dto.getShopId(),
            AdManagePageExportTaskTypeEnum.QUERY_KEYWORD, dto.getStart(), dto.getEnd(), dto);
        if (id == null) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("新建任务异常，请联系管理员");
        } else {
            urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
        }

        responseObserver.onNext(urlBuilder.build());
        responseObserver.onCompleted();
    }

    private WriteHandlerBuild setWriteHandlerBuild() {
        WriteHandlerBuild build = new WriteHandlerBuild();
        build = build.rate();
        //需要进行格式化的表头
        List<String> formatHeads = new ArrayList<>();
        formatHeads.add("impressions");
        formatHeads.add("clicks");
        build.integerFormat(formatHeads);
        return build;
    }
    /**
     * 处理excel导出vo
     * @param obj
     * @return
     */
    private ReportExcelVo getExcelVo(ReportVo obj,String currency) {
        ReportExcelVo excelVo = new ReportExcelVo();
        excelVo.setCountDate(obj.getCountDate());
        excelVo.setQuery(obj.getQuery());
        excelVo.setQueryCn(obj.getQueryCn());
        if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(obj.getKeywordText()) || Constants.keywords_related_to_your_brand.equalsIgnoreCase(obj.getKeywordText())) {
            excelVo.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
        } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(obj.getKeywordText()) || Constants.keywords_related_to_your_landing_pages.equalsIgnoreCase(obj.getKeywordText())) {
            excelVo.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
        } else {
            excelVo.setKeywordText(obj.getKeywordText());
        }


        SpKeywordGroupValueEnum keywordGroupValueEnumByTextCn = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByText(excelVo.getKeywordText());
        if (keywordGroupValueEnumByTextCn != null) {
            excelVo.setKeywordText(keywordGroupValueEnumByTextCn.getTextCn());
        }

        if(StringUtils.isNotEmpty(obj.getMatchType())){
            if(Constants.PHRASE.equalsIgnoreCase(obj.getMatchType())){
                excelVo.setMatchType("词组匹配");
            }else if(Constants.EXACT.equalsIgnoreCase(obj.getMatchType())){
                excelVo.setMatchType("精确匹配");
            }else if(Constants.BROAD.equalsIgnoreCase(obj.getMatchType())){
                excelVo.setMatchType("广泛匹配");
            } else if (Constants.TARGETING_EXPRESSION_CLOSE.equalsIgnoreCase(obj.getMatchType())) {
                excelVo.setMatchType("紧密匹配");
            } else if (Constants.TARGETING_EXPRESSION_LOOSE.equalsIgnoreCase(obj.getMatchType())) {
                excelVo.setMatchType("宽泛匹配");
            } else if (SbMatchValueEnum.THEME.getMatchType().equalsIgnoreCase(obj.getMatchType())) {
                excelVo.setMatchType("主题");
            } else {
                excelVo.setMatchType(obj.getMatchType());
            }
        }
        if(StringUtils.isNotEmpty(obj.getTargetingExpression())){
            if(Constants.TARGETING_EXPRESSION_SUBSTITUTES.equalsIgnoreCase(obj.getTargetingExpression())){
                excelVo.setTargetingExpression("同类商品");
            }else if(Constants.TARGETING_EXPRESSION_CLOSE.equalsIgnoreCase(obj.getTargetingExpression())){
                excelVo.setTargetingExpression("紧密匹配");
            }else if(Constants.TARGETING_EXPRESSION_LOOSE.equalsIgnoreCase(obj.getTargetingExpression())){
                excelVo.setTargetingExpression("宽泛匹配");
            }else if(Constants.TARGETING_EXPRESSION_COMPLEMENTS.equalsIgnoreCase(obj.getTargetingExpression())){
                excelVo.setTargetingExpression("关联商品");
            }else{
                excelVo.setTargetingExpression(obj.getTargetingExpression());
            }
        }
        excelVo.setAdGroupName(obj.getAdGroupName());
        excelVo.setCampaignName(obj.getCampaignName());
        if(StringUtils.isNotEmpty(obj.getTargetingType())){
            excelVo.setTargetingType(Constants.AUTO.equals(obj.getTargetingType()) ? "自动":"手动");
        }
        excelVo.setPortfolioName(obj.getPortfolioName());
        excelVo.setImpressions(obj.getImpressions());
        excelVo.setClicks(obj.getClicks());
        excelVo.setRoas(obj.getRoas() == null ? "-" : obj.getRoas().toString());
        excelVo.setClickRate(obj.getClickRate() == null ? "-" : obj.getClickRate()+"%");
        excelVo.setCost(obj.getCost()!=null?currency+obj.getCost():"-");
        excelVo.setCpc(obj.getCpc()!=null?currency+obj.getCpc():"-");
        excelVo.setOrderNum(obj.getSaleNum());
        excelVo.setSalesConversionRate(obj.getSalesConversionRate() == null ? "-" : obj.getSalesConversionRate()+"%");
        excelVo.setCpa(obj.getCpa()!=null?currency+obj.getCpa():"-");
        excelVo.setSales(obj.getSales()!=null?currency+obj.getSales():"-");
        excelVo.setAcos(obj.getAcos() == null ? "-" : obj.getAcos()+"%");
        excelVo.setAcos(obj.getAcos() == null ? "-" : obj.getAcos()+"%");
        excelVo.setAcots(obj.getAcots() == null ? "-" : obj.getAcots()+"%");
        excelVo.setAsots(obj.getAsots() == null ? "-" : obj.getAsots()+"%");
        excelVo.setRoas(obj.getRoas() == null ? "-" : obj.getRoas().toString());
        /**
         * TODO 广告报告重构
         * 本广告产品销售额
         */
        excelVo.setAdSales(obj.getAdSales()!=null?currency+obj.getAdSales():"-");
        //本广告产品订单量
        excelVo.setAdSaleNum(obj.getAdSaleNum());
        //广告销量
        excelVo.setSaleNum(obj.getOrderNum());
        //本广告产品销量
        excelVo.setAdSelfSaleNum(obj.getAdSelfSaleNum());
        //其他产品广告订单量
        excelVo.setAdOtherOrderNum(obj.getAdOtherOrderNum());
        //其他产品广告销售额
        excelVo.setAdOtherSales(obj.getAdOtherSales()!=null?currency+obj.getAdOtherSales():"-");
        //其他产品广告销量
        excelVo.setAdOtherSaleNum(obj.getAdOtherSaleNum());
        //“品牌新买家”订单量
        excelVo.setOrdersNewToBrand14d(obj.getOrdersNewToBrand14d());
        //“品牌新买家”销售额
        excelVo.setSalesNewToBrand14d(obj.getSalesNewToBrand14d()!=null?currency+obj.getSalesNewToBrand14d():"-");
        //搜索词展示量排名
        excelVo.setImpressionRank(obj.getImpressionRank());
        //搜索词展示份额
        excelVo.setImpressionShare(obj.getImpressionShare() == null ? "-" : obj.getImpressionShare()+"%");
        //“品牌新买家”订单百分比
        excelVo.setOrderRateNewToBrand14d(obj.getOrderRateNewToBrand14d() == null ? "-" : obj.getOrderRateNewToBrand14d()+"%");
        //“品牌新买家”销售额百分比
        excelVo.setSalesRateNewToBrand14d(obj.getSalesRateNewToBrand14d() == null ? "-" : obj.getSalesRateNewToBrand14d()+"%");
        //“品牌新买家”订单转化率
        excelVo.setOrdersNewToBrandPercentage14d(obj.getOrdersNewToBrandPercentage14d() == null ? "-" : obj.getOrdersNewToBrandPercentage14d()+"%");
        // 广告花费占比
        excelVo.setAdCostPercentage(StringUtils.isNotBlank(obj.getAdCostPercentage()) ? obj.getAdCostPercentage()+"%" : "0.00%");
        // 广告销售额占比
        excelVo.setAdSalePercentage(StringUtils.isNotBlank(obj.getAdSalePercentage()) ? obj.getAdSalePercentage()+"%" : "0.00%");
        // 广告订单量占比
        excelVo.setAdOrderNumPercentage(StringUtils.isNotBlank(obj.getAdOrderNumPercentage()) ? obj.getAdOrderNumPercentage()+"%" : "0.00%");
        // 广告销量占比
        excelVo.setOrderNumPercentage(StringUtils.isNotBlank(obj.getOrderNumPercentage()) ? obj.getOrderNumPercentage()+"%" : "0.00%");

        excelVo.setVideo5SecondViews(Optional.ofNullable(obj.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
        excelVo.setVideo5SecondViewRate(Optional.ofNullable(obj.getVideo5SecondViewRate()).map(String::valueOf).map(s->s.concat("%")).orElse("0.00%"));
        excelVo.setVideoFirstQuartileViews(Optional.ofNullable(obj.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
        excelVo.setVideoMidpointViews(Optional.ofNullable(obj.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
        excelVo.setVideoThirdQuartileViews(Optional.ofNullable(obj.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
        excelVo.setVideoCompleteViews(Optional.ofNullable(obj.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
        excelVo.setVideoUnmutes(Optional.ofNullable(obj.getVideoUnmutes()).map(String::valueOf).orElse("0"));
        excelVo.setViewImpressions(Optional.ofNullable(obj.getViewImpressions()).map(String::valueOf).orElse("0"));
        excelVo.setViewabilityRate(Optional.ofNullable(obj.getViewabilityRate()).map(String::valueOf).map(s->s.concat("%")).orElse("0.00%"));
        excelVo.setViewClickThroughRate(Optional.ofNullable(obj.getViewClickThroughRate()).map(String::valueOf).map(s->s.concat("%")).orElse("0.00%"));
        excelVo.setAdvertisingUnitPrice(Optional.ofNullable(obj.getAdvertisingUnitPrice()).map(String::valueOf).map(e -> currency + e).orElse("0"));
        if (obj.getSearchFrequencyRank() > 0) {
            excelVo.setSearchFrequencyRank(String.valueOf(obj.getSearchFrequencyRank()));
            excelVo.setWeekRatio(ExportStringUtil.modifyFormat(obj.getWeekRatio().toPlainString()));
        } else {
            excelVo.setSearchFrequencyRank("-");
            excelVo.setWeekRatio("-");
        }

        return excelVo;
    }
    /**
     * 搜索词(关键词)详情
     * @param request
     * @param responseObserver
     */
    @Override
    public void queryWordDetail(QueryWordDetailRequest request, StreamObserver<QueryWordDetailResponse> responseObserver) {
        log.info("ad-query-搜索(关键词)词详情 request: {}",request);
        QueryWordDetailResponse.Builder builder = QueryWordDetailResponse.newBuilder();

        CpcQueryWordDetailDto dto = new CpcQueryWordDetailDto();
        dto.setPuid(request.getPuid().getValue());
        dto.setUid(request.getUid().getValue());
        dto.setLoginIp(request.getLoginIp());
        dto.setShopId(request.getShopId().getValue());
        dto.setMarketplaceId(request.getMarketplaceId());
        dto.setQuery(request.getQuery());
        dto.setStart(request.getStart());
        dto.setEnd(request.getEnd());
        dto.setDateType(request.getDateType());
        dto.setOrderField(request.getOrderField());
        dto.setOrderValue(request.getOrderValue());
        dto.setType(request.getType());
        String id = "";
        if (StringUtils.isNotBlank(request.getKeywordId())) {
            id = request.getKeywordId();
        } else if (StringUtils.isNotBlank(request.getTargetId())) {
            id = request.getTargetId();
        }
        dto.setKeywordId(id);
        dto.setTargetId(id);

        if(!request.hasPuid() || !request.hasShopId() || checkQueryWordDetailDto(dto) || StringUtils.isBlank(id)){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {

            Page page = new Page(request.hasPageNo() ? request.getPageNo().getValue() : 1,request.hasPageSize() ? request.getPageSize().getValue() : 20);
            if("sb".equalsIgnoreCase(dto.getType())){
                page = cpcSbQueryKeywordReportService.detailList(dto.getPuid(),dto,page);
            } else {
                page = cpcQueryKeywordReportService.detailList(dto.getPuid(),dto,page);
            }
            //获取汇总数剧
            ReportVo sum = null;
            if(page.getRows() != null && page.getRows().size()>0){
                if("sb".equalsIgnoreCase(dto.getType())){
                    sum = cpcSbQueryKeywordReportService.sumDetailReport(dto.getPuid(),dto);
                } else {
                    sum = cpcQueryKeywordReportService.sumDetailReport(dto.getPuid(),dto);
                    sum.setSaleNum(sum.getOrderNum());
                    Double salesConversionRate = sum.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(sum.getSaleNum()) * 100, Double.valueOf(sum.getClicks()), 2);
                    sum.setSalesConversionRate(salesConversionRate);

                }

            }

            //处理返回数据
            builder.setCode(Int32Value.of(Result.SUCCESS));

            QueryPage.Builder pageBuilder = QueryPage.newBuilder();
            pageBuilder.setPageNo(Int32Value.of(page.getPageNo()));
            pageBuilder.setPageSize(Int32Value.of(page.getPageSize()));
            pageBuilder.setTotalPage(Int32Value.of(page.getTotalPage()));
            pageBuilder.setTotalSize(Int32Value.of(page.getTotalSize()));

            //处理分页中list
            QueryWordDetailResponse.QueryWordDetailResponseData.Builder dataBuilder = QueryWordDetailResponse.QueryWordDetailResponseData.newBuilder();

            List<ReportVo> rows = page.getRows();
            if (CollectionUtils.isNotEmpty(rows)) {
                List<ReportRpcVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                    return convertVoToRpcMessage(item).build();
                }).collect(Collectors.toList());
                pageBuilder.addAllRows(rpcVos);
            }
            dataBuilder.setPage(pageBuilder.build());

            if (sum!=null) {
                ReportRpcVo rpcVo = convertVoToRpcMessage(sum).build();
                dataBuilder.setSum(rpcVo);
            }
            builder.setData(dataBuilder.build());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    private boolean checkQueryWordDetailDto(CpcQueryWordDetailDto dto) {
        //做参数校验
        if(!CheckParamUtil.checkRequired(dto,false,"shopId,marketplaceId,dateType,start,end,query")){
            return true;
        }
        dto.setStart(DateUtil.dateToStrWithFormat(DateUtil.strToDate(dto.getStart(),"yyyy-MM-dd"),"yyyyMMdd"));
        dto.setEnd(DateUtil.dateToStrWithFormat(DateUtil.strToDate(dto.getEnd(),"yyyy-MM-dd"),"yyyyMMdd"));
        if (StringUtils.isNotBlank(dto.getStartDateCompare())) {
            dto.setStartDateCompare(DateUtil.dateToStrWithFormat(DateUtil.strToDate(dto.getStartDateCompare(), "yyyy-MM-dd"), "yyyyMMdd"));
        }
        if (StringUtils.isNotBlank(dto.getEndDateCompare())) {
            dto.setEndDateCompare(DateUtil.dateToStrWithFormat(DateUtil.strToDate(dto.getEndDateCompare(), "yyyy-MM-dd"), "yyyyMMdd"));
        }
        return false;
    }


    /**
     * 搜索词(关键词)详情导出
     * @param request
     * @param responseObserver
     */
    @Override
    public void queryWordDetailExcel(QueryWordDetailExcleRequest request, StreamObserver<QueryWordDetailExcelResponse> responseObserver) {
        log.info("ad-query-搜索词(关键词)详情 request: {}",request);
        QueryWordDetailExcelResponse.Builder builder = QueryWordDetailExcelResponse.newBuilder();
        CpcQueryWordDetailDto dto = new CpcQueryWordDetailDto();
        dto.setPuid(request.getPuid().getValue());
        dto.setUid(request.getUid().getValue());
        dto.setLoginIp(request.getLoginIp());
        dto.setShopId(request.getShopId().getValue());
        dto.setMarketplaceId(request.getMarketplaceId());
        dto.setKeywordId(request.getKeywordId());
        dto.setTargetId(request.getTargetId());
        dto.setQuery(request.getQuery());
        dto.setStart(request.getStart());
        dto.setEnd(request.getEnd());
        dto.setDateType(request.getDateType());
        dto.setOrderField(request.getOrderField());
        dto.setOrderValue(request.getOrderValue());
        dto.setType(request.getType());

        if(checkQueryWordDetailDto(dto)){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            Page pageTemp = new Page();
            pageTemp.setPageNo(1);
            pageTemp.setPageSize(Integer.MAX_VALUE);
            Page page;
            if("sb".equalsIgnoreCase(dto.getType())){
                page = cpcSbQueryKeywordReportService.detailList(request.getPuid().getValue(), dto, pageTemp);
            } else {
                page = cpcQueryKeywordReportService.detailList(request.getPuid().getValue(), dto, pageTemp);
            }



            List<ReportVo> rows = page.getRows();

            builder.setCode(Int32Value.of(Result.SUCCESS));
            //处理list
            if (CollectionUtils.isNotEmpty(rows)) {
                List<ReportRpcVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                    return convertVoToRpcMessage(item).build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 搜索词(关键词)chart图数据
     * @param request
     * @param responseObserver
     */
    @Override
    public void queryWordDetailChart(QueryWordDetailChartRequest request, StreamObserver<QueryWordDetailChartResponse> responseObserver) {
        log.info("ad-query-搜索词(关键词)chart图数据 request: {}",request);
        QueryWordDetailChartResponse.Builder builder = QueryWordDetailChartResponse.newBuilder();

        CpcQueryWordDetailDto dto = new CpcQueryWordDetailDto();
        dto.setPuid(request.getPuid().getValue());
        dto.setUid(request.getUid().getValue());
        dto.setLoginIp(request.getLoginIp());
        dto.setShopId(request.getShopId().getValue());
        dto.setMarketplaceId(request.getMarketplaceId());
        dto.setKeywordId(request.getKeywordId());
        dto.setQuery(request.getQuery());
        dto.setStart(request.getStart());
        dto.setEnd(request.getEnd());
        dto.setDateType(request.getDateType());
        dto.setOrderField(request.getOrderField());
        dto.setOrderValue(request.getOrderValue());
        dto.setType(request.getType());

        //做参数校验
        if(checkQueryWordDetailDto(dto)){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            List<ReportVo> list;
            if("sb".equalsIgnoreCase(dto.getType())){
                list = cpcSbQueryKeywordReportService.detailListChart(dto.getPuid(),dto);
            } else {
                list = cpcQueryKeywordReportService.detailListChart(dto.getPuid(),dto);
            }


            builder.setCode(Int32Value.of(Result.SUCCESS));

            if (CollectionUtils.isNotEmpty(list)) {
                List<ReportRpcVo> rpcVos = list.stream().filter(Objects::nonNull).map(item -> {
                    return convertVoToRpcMessage(item).build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 搜索词(投放)列表
     * @param request
     * @param responseObserver
     */
    @Override
    public void queryTarget(QueryTargetRequest request, StreamObserver<QueryTargetResponse> responseObserver) {
        log.info("ad-query-搜索词(投放)列表 request: {}",request);
        QueryTargetResponse.Builder builder = QueryTargetResponse.newBuilder();

        CpcQueryWordDto dto = new CpcQueryWordDto();
        dto.setPuid(request.getPuid().getValue());
        dto.setShopId(request.getShopId().getValue());
        dto.setUid(request.getUid().getValue());
        dto.setLoginIp(request.getLoginIp());
        dto.setMarketplaceId(request.getMarketplaceId());
        dto.setMatchType(request.getMatchType());
        dto.setStart(request.getStart());
        dto.setEnd(request.getEnd());
        dto.setSearchType(request.getSearchType());
        dto.setSearchField(request.getSearchField());
        dto.setSearchValue(request.getSearchValue());
        dto.setOrderField(request.getOrderField());
        dto.setOrderValue(request.getOrderValue());
        dto.setOrderType(request.getOrderType());
        dto.setState(request.getState());
        dto.setStatus(request.getStatus());
        dto.setCampaignId(request.getCampaignId());
        dto.setGroupId(request.getGroupId());
        dto.setCampaignIds(request.getCampaignIdsList());
        dto.setUseAdvanced(request.getUseAdvanced().getValue());
        dto.setImpressionsMin(request.hasImpressionsMin() ? request.getImpressionsMin().getValue(): null);
        dto.setImpressionsMax(request.hasImpressionsMax() ?request.getImpressionsMax().getValue(): null);
        dto.setClicksMin(request.hasClicksMin() ? request.getClicksMin().getValue(): null);
        dto.setClicksMax(request.hasClicksMax() ? request.getClicksMax().getValue(): null);
        dto.setClickRateMin(request.hasClickRateMin() ? BigDecimal.valueOf(request.getClickRateMin().getValue()): null);
        dto.setClickRateMax(request.hasClickRateMax() ? BigDecimal.valueOf(request.getClickRateMax().getValue()): null);
        dto.setCostMin(request.hasCostMin() ? BigDecimal.valueOf(request.getCostMin().getValue()): null);
        dto.setCostMax(request.hasCostMax() ? BigDecimal.valueOf(request.getCostMax().getValue()): null);
        dto.setCpcMin(request.hasCpaMin() ? BigDecimal.valueOf(request.getCpcMin().getValue()): null);
        dto.setCpcMax(request.hasCpaMax() ? BigDecimal.valueOf(request.getCpcMax().getValue()): null);
        dto.setOrderNumMin(request.hasOrderNumMin() ? request.getOrderNumMin().getValue(): null);
        dto.setOrderNumMax(request.hasOrderNumMax() ? request.getOrderNumMax().getValue(): null);
        dto.setSalesConversionRateMin(request.hasSalesConversionRateMin() ? BigDecimal.valueOf(request.getSalesConversionRateMin().getValue()): null);
        dto.setSalesConversionRateMax(request.hasSalesConversionRateMax() ? BigDecimal.valueOf(request.getSalesConversionRateMax().getValue()): null);
        dto.setCpaMin(request.hasCpaMin() ? BigDecimal.valueOf(request.getCpaMin().getValue()): null);
        dto.setCpaMax(request.hasCpaMax() ? BigDecimal.valueOf(request.getCpaMax().getValue()): null);
        dto.setTotalSalesMin(request.hasTotalSalesMin() ? BigDecimal.valueOf(request.getTotalSalesMin().getValue()): null);
        dto.setTotalSalesMax(request.hasTotalSalesMax() ? BigDecimal.valueOf(request.getTotalSalesMax().getValue()): null);
        dto.setAcosMin(request.hasAcosMin() ? BigDecimal.valueOf(request.getAcosMin().getValue()): null);
        dto.setAcosMax(request.hasAcosMax() ? BigDecimal.valueOf(request.getAcosMax().getValue()) : null);

        //做参数校验
        if(!request.hasPuid() || !request.hasShopId() || checkQueryWordDto(dto)){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            Page page = new Page(request.hasPageNo() ? request.getPageNo().getValue() : 1,request.hasPageSize() ? request.getPageSize().getValue() : 20);
            page = cpcQueryTargetingReportService.pageList(dto.getPuid(),dto,page);
            //获取汇总数据
            ReportVo sum = null;
            if(page.getRows() != null && page.getRows().size()>0){
                sum = cpcQueryTargetingReportService.sumReport(dto.getPuid(),dto, false, null);
            }

            //处理返回数据
            builder.setCode(Int32Value.of(Result.SUCCESS));

            QueryPage.Builder pageBuilder = QueryPage.newBuilder();
            pageBuilder.setPageNo(Int32Value.of(page.getPageNo()));
            pageBuilder.setPageSize(Int32Value.of(page.getPageSize()));
            pageBuilder.setTotalPage(Int32Value.of(page.getTotalPage()));
            pageBuilder.setTotalSize(Int32Value.of(page.getTotalSize()));

            //处理分页中list
            QueryTargetResponse.QueryTargetResponseData.Builder dataBuilder = QueryTargetResponse.QueryTargetResponseData.newBuilder();

            List<ReportVo> rows = page.getRows();
            if (CollectionUtils.isNotEmpty(rows)) {
                List<ReportRpcVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                    return convertVoToRpcMessage(item).build();
                }).collect(Collectors.toList());
                pageBuilder.addAllRows(rpcVos);
            }
            dataBuilder.setPage(pageBuilder.build());

            if (sum!=null) {
                ReportRpcVo rpcVo = convertVoToRpcMessage(sum).build();
                dataBuilder.setSum(rpcVo);
            }
            builder.setData(dataBuilder.build());

        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }


    /**
     * 搜索词(投放)列表导出
     * @param request
     * @param responseObserver
     */
    @Override
    public void queryTargetExcel(QueryTargetExcelRequest request, StreamObserver<UrlResponse> responseObserver) {

        log.info("ad-query-搜索词(投放)列表导出 request: {}",request);
        QueryTargetExcelResponse.Builder builder = QueryTargetExcelResponse.newBuilder();
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        List<String> feilds = Arrays.asList("orderNum", "orderNumPercentage", "salesConversionRate", "query", "targetingExpression", "adGroupName", "campaignName", "targetingType"
                , "impressions", "clicks", "clickRate", "cost", "adCostPercentage", "cpc", "cpa", "sales", "adSalePercentage", "acos", "acots", "roas", "asots", "adOrderNumPercentage");
        List<String> urls = Lists.newLinkedList();
        CpcQueryWordDto dto = new CpcQueryWordDto();
        dto.setPuid(request.getPuid().getValue());
        dto.setShopId(request.getShopId().getValue());
        dto.setUid(request.getUid().getValue());
        dto.setLoginIp(request.getLoginIp());
        dto.setMarketplaceId(request.getMarketplaceId());
        dto.setMatchType(request.getMatchType());
        dto.setStart(request.getStart());
        dto.setEnd(request.getEnd());
        dto.setSearchType(request.getSearchType());
        dto.setSearchField(request.getSearchField());
        dto.setSearchValue(request.getSearchValue());
        dto.setOrderField(request.getOrderField());
        dto.setOrderValue(request.getOrderType());
        dto.setOrderType(request.getOrderType());
        dto.setState(request.getState());
        dto.setStatus(request.getStatus());
        dto.setCampaignId(request.getCampaignId());
        dto.setGroupId(request.getGroupId());
        dto.setCampaignIds(request.getCampaignIdsList());
        dto.setPortfolioId(request.getPortfolioId());
        dto.setServingStatus(request.getServingStatus());
        dto.setUuid(request.getUuid());

        AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
        if (advancedFilter != null) {  //高级筛选
            dto.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            dto.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);
            dto.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            dto.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            dto.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            dto.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            dto.setClickRateMin(advancedFilter.hasClickRateMin() ?  MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMin()),BigDecimal.valueOf(100)) : null);
            dto.setClickRateMax(advancedFilter.hasClickRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMax()),BigDecimal.valueOf(100)) : null);
            dto.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            dto.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            dto.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            dto.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            dto.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            dto.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            dto.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            dto.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            dto.setAcosMin(advancedFilter.hasAcosMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMin()),BigDecimal.valueOf(100)) : null);
            dto.setAcosMax(advancedFilter.hasAcosMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMax()),BigDecimal.valueOf(100)) : null);
            dto.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            dto.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            dto.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()),BigDecimal.valueOf(100)) : null);
            dto.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()),BigDecimal.valueOf(100)): null);
            dto.setAcotsMin(advancedFilter.hasAcotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMin()),BigDecimal.valueOf(100)) :null);
            dto.setAcotsMax(advancedFilter.hasAcotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMax()),BigDecimal.valueOf(100)) : null);
            dto.setAsotsMin(advancedFilter.hasAsotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMin()),BigDecimal.valueOf(100)) :null);
            dto.setAsotsMax(advancedFilter.hasAsotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMax()),BigDecimal.valueOf(100)) : null);
            dto.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            dto.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);

        }
        if (request.hasFilterTargetType()) {
            dto.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasFilterAddProperty()) {
            dto.setFilterAddProperty(request.getFilterAddProperty());
        }
        if (CollectionUtils.isNotEmpty(request.getQueryWordTagTypeList())) {
            dto.setQueryWordTagTypeList(request.getQueryWordTagTypeList());
        }
        if (request.hasExportSortField()) {
            dto.setExportSortField(request.getExportSortField());
        }
        if (request.hasFreezeNum()) {
            dto.setFreezeNum(request.getFreezeNum());
        }
        if (CollectionUtils.isNotEmpty(request.getAdStrategyTypeListList())) {
            dto.setAdStrategyTypeList(request.getAdStrategyTypeListList());
        }
        //做参数校验
        String err = checkQueryTargetExportDto(dto);
        if (StringUtils.isNotBlank(err)) {
            throw new SponsoredBizException(err);
        }

        dto.setAdminUser(BooleanUtils.toBoolean(MDC.get(Constants.ADMIN_USER)));

        Long id = adManagePageExportTaskService.saveExportTask(dto.getPuid(), dto.getUid(), dto.getShopId(),
            AdManagePageExportTaskTypeEnum.QUERY_ASIN, dto.getStart(), dto.getEnd(), dto);
        if (id == null) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("新建任务异常，请联系管理员");
        } else {
            urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
        }

        responseObserver.onNext(urlBuilder.build());
        responseObserver.onCompleted();
    }


    /**
     * 搜索词(投放)详情
     * @return
     */
    @Override
    public void queryTargetDetail(QueryTargetDetailRequest request, StreamObserver<QueryTargetDetailResponse> responseObserver) {
        log.info("ad-query-搜索词(投放)详情 request: {}",request);
        QueryTargetDetailResponse.Builder builder = QueryTargetDetailResponse.newBuilder();

        //做参数校验
        CpcQueryWordDetailDto dto = new CpcQueryWordDetailDto();
        dto.setPuid(request.getPuid().getValue());
        dto.setUid(request.getUid().getValue());
        dto.setLoginIp(request.getLoginIp());
        dto.setShopId(request.getShopId().getValue());
        dto.setMarketplaceId(request.getMarketplaceId());
        dto.setKeywordId(request.getKeywordId());
        dto.setTargetId(request.getTargetId());
        dto.setQuery(request.getQuery());
        dto.setStart(request.getStart());
        dto.setEnd(request.getEnd());
        dto.setDateType(request.getDateType());
        dto.setOrderField(request.getOrderField());
        dto.setOrderValue(request.getOrderValue());

        if(!request.hasPuid() || !request.hasShopId() || checkQueryWordDetailDto(dto)){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            Page page = new Page(request.hasPageNo() ? request.getPageNo().getValue() : 1,request.hasPageSize() ? request.getPageSize().getValue() : 20);
            page = cpcQueryTargetingReportService.detailList(dto.getPuid(),dto,page);
            //获取汇总数剧
            ReportVo sum = null;
            if(page.getRows() != null && page.getRows().size()>0){
                sum = cpcQueryTargetingReportService.sumDetailReport(dto.getPuid(),dto);
                sum.setSaleNum(sum.getOrderNum());
                Double salesConversionRate = sum.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(sum.getSaleNum()) * 100, Double.valueOf(sum.getClicks()), 2);
                sum.setSalesConversionRate(salesConversionRate);
            }
            //处理返回数据
            builder.setCode(Int32Value.of(Result.SUCCESS));

            QueryPage.Builder pageBuilder = QueryPage.newBuilder();
            pageBuilder.setPageNo(Int32Value.of(page.getPageNo()));
            pageBuilder.setPageSize(Int32Value.of(page.getPageSize()));
            pageBuilder.setTotalPage(Int32Value.of(page.getTotalPage()));
            pageBuilder.setTotalSize(Int32Value.of(page.getTotalSize()));

            //处理分页中list
            QueryTargetDetailResponse.QueryTargetDetailResponseData.Builder dataBuilder = QueryTargetDetailResponse.QueryTargetDetailResponseData.newBuilder();

            List<ReportVo> rows = page.getRows();
            if (CollectionUtils.isNotEmpty(rows)) {
                List<ReportRpcVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                    return convertVoToRpcMessage(item).build();
                }).collect(Collectors.toList());
                pageBuilder.addAllRows(rpcVos);
            }
            dataBuilder.setPage(pageBuilder.build());

            if (sum!=null) {
                ReportRpcVo rpcVo = convertVoToRpcMessage(sum).build();
                dataBuilder.setSum(rpcVo);
            }
            builder.setData(dataBuilder.build());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    /**
     * 搜索词(投放)详情导出
     * @param request
     * @param responseObserver
     */
    @Override
    public void queryTargetDetailExcel(QueryTargetDetailExcelRequest request, StreamObserver<QueryTargetDetailExcelResponse> responseObserver) {
        log.info("ad-query-搜索词(投放)详情导出 request: {}",request);
        QueryTargetDetailExcelResponse.Builder builder = QueryTargetDetailExcelResponse.newBuilder();

        //做参数校验
        CpcQueryWordDetailDto dto = new CpcQueryWordDetailDto();
        dto.setPuid(request.getPuid().getValue());
        dto.setUid(request.getUid().getValue());
        dto.setLoginIp(request.getLoginIp());
        dto.setShopId(request.getShopId().getValue());
        dto.setMarketplaceId(request.getMarketplaceId());
        dto.setKeywordId(request.getKeywordId());
        dto.setTargetId(request.getTargetId());
        dto.setQuery(request.getQuery());
        dto.setStart(request.getStart());
        dto.setEnd(request.getEnd());
        dto.setDateType(request.getDateType());
        dto.setOrderField(request.getOrderField());
        dto.setOrderValue(request.getOrderValue());

        if(!request.hasPuid() || !request.hasShopId() || checkQueryWordDetailDto(dto)){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            Page pageTemp = new Page();
            pageTemp.setPageNo(1);
            pageTemp.setPageSize(Integer.MAX_VALUE);
            Page page = cpcQueryTargetingReportService.detailList(request.getPuid().getValue(), dto, pageTemp);

            //处理返回数据
            builder.setCode(Int32Value.of(Result.SUCCESS));

            List<ReportVo> rows = page.getRows();
            if (CollectionUtils.isNotEmpty(rows)) {
                List<ReportRpcVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                    return convertVoToRpcMessage(item).build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 搜索词投放详情图表
     * @param request
     * @param responseObserver
     */
    @Override
    public void queryTargetDetailChart(QueryTargetDetailChartRequest request, StreamObserver<QueryTargetDetailChartResponse> responseObserver) {
        log.info("ad-query-搜索词(投放)chart图数据 request: {}",request);
        QueryTargetDetailChartResponse.Builder builder = QueryTargetDetailChartResponse.newBuilder();

        CpcQueryWordDetailDto dto = new CpcQueryWordDetailDto();
        dto.setPuid(request.getPuid().getValue());
        dto.setUid(request.getUid().getValue());
        dto.setLoginIp(request.getLoginIp());
        dto.setShopId(request.getShopId().getValue());
        dto.setMarketplaceId(request.getMarketplaceId());
        dto.setTargetId(request.getTargetId());
        dto.setQuery(request.getQuery());
        dto.setStart(request.getStart());
        dto.setEnd(request.getEnd());
        dto.setDateType(request.getDateType());
        dto.setOrderField(request.getOrderField());
        dto.setOrderValue(request.getOrderValue());
        //做参数校验
        if(checkQueryWordDetailDto(dto)){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            List<ReportVo> list = cpcQueryTargetingReportService.detailListChart(dto.getPuid(),dto);
            //处理返回数据
            builder.setCode(Int32Value.of(Result.SUCCESS));

            if (CollectionUtils.isNotEmpty(list)) {
                List<ReportRpcVo> rpcVos = list.stream().filter(Objects::nonNull).map(item -> {
                    return convertVoToRpcMessage(item).build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    /**
     * 添加搜索词到关键词（获取当前关键词信息）
     * @return
     */
    @Override
    public void getKeywords(GetKeywordsRequest request, StreamObserver<GetKeywordsResponse> responseObserver) {
        log.info("ad-query-添加搜索词到关键词（获取当前关键词信息） request: {}",request);
        GetKeywordsResponse.Builder builder = GetKeywordsResponse.newBuilder();

        //做参数校验
        List<QueryVo> list = JSONUtil.jsonToArray(request.getQuerys(),QueryVo.class);
        if(StringUtils.isEmpty(request.getQuerys()) || list == null || list.size() ==0){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        for(QueryVo vo : list){
            if(!CheckParamUtil.checkRequired(vo,false,"itemValue")){
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }
        }
        List<QueryVo> resultList = cpcService.listKeywords(request.getPuid().getValue(),list);
        //处理list
        builder.setCode(Int32Value.of(Result.SUCCESS));
        if (CollectionUtils.isNotEmpty(resultList)) {
            List<QueryRpcVo> rpcVos = resultList.stream().filter(Objects::nonNull).map(item -> {
                QueryRpcVo.Builder voBuilder = QueryRpcVo.newBuilder();
                if (item.getIndex() != null) {
                    voBuilder.setIndex(Int32Value.of(item.getIndex()));
                }
                if (item.getItemId() != null) {
                    voBuilder.setItemId(item.getItemId());
                }
                if (item.getCampaignId() != null) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (item.getAdGroupId() != null) {
                    voBuilder.setAdGroupId(item.getAdGroupId());
                }
                if (item.getItemValue() != null) {
                    voBuilder.setItemValue(item.getItemValue());
                }
                if (item.getType() != null) {
                    voBuilder.setType(item.getType());
                }
                if (item.getBid() != null) {
                    voBuilder.setBid(DoubleValue.of(item.getBid()));
                }
                return voBuilder.build();
            }).collect(Collectors.toList());
            builder.addAllData(rpcVos);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();



    }

    /**
     * 添加搜索词到关键词
     * @param request
     * @param responseObserver
     */
    @Override
    public void createKeywords(KeywordsRequest request, StreamObserver<KeywordsResponse> responseObserver) {
        log.info("ad-query-添加搜索词到关键词 request: {}",request.getAllFields());
        KeywordsResponse.Builder builder = KeywordsResponse.newBuilder();

        SubmitQueryDto dto = new SubmitQueryDto();
        dto.setShopId(request.getShopId().getValue());
        dto.setPuid(request.getPuid().getValue());
        dto.setUid(request.getUid().getValue());
        dto.setLoginIp(request.getLoginIp());
        dto.setMarketplaceId(request.getMarketplaceId());
        dto.setType(request.getType());
        //处理list
        List<QueryRpcVo> querysList = request.getQuerysList();

        //包含特殊字符的关键词抛出错误提示，不包含的往下执行。
        StringBuilder errsStr = new StringBuilder();
        //获取添加失败的下标
        List<Int32Value> errIndexList = Lists.newArrayListWithExpectedSize(2);
        List<QueryVo> vos = Lists.newArrayList();
        querysList.forEach(item -> {
            QueryVo queryVo = new QueryVo();
            queryVo.setCampaignId(item.getCampaignId());
            queryVo.setIndex(item.getIndex().getValue());
            queryVo.setItemId(item.getItemId());
            queryVo.setAdGroupId(item.getAdGroupId());
            queryVo.setItemValue(item.getItemValue());
            queryVo.setType(item.getType());
            queryVo.setBid(item.getBid().getValue());
            String vaildResult = SpecSymbolUtils.validateSymbol(item.getItemValue());
            if (StringUtils.isNotBlank(vaildResult)) {
                errsStr.append(vaildResult);
                errIndexList.add(Int32Value.of(item.getIndex().getValue()));
            } else {
                vos.add(queryVo);
            }
        });

        dto.setQuerys(vos);
        //做参数校验
        if (!request.hasPuid() || !request.hasShopId() || checkSubmitQueryDto(dto)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            if (StringUtils.isNotBlank(errsStr)) {
                builder.setMsg(errsStr.toString());
            } else {
                builder.setMsg("请求参数错误");
            }
        } else {
            Result<Map<String, Object>> res = cpcService.createKeywords(dto.getPuid(), dto.getUid(), dto.getLoginIp(), dto);
            //处理返回结果
            builder.setCode(Int32Value.of(res.getCode()));
            // 错误信息返回：code = error返回给result.msg; code = success返回给data里的failmsg
            if (!res.success()) {
                builder.setMsg(res.getMsg());
            }
            Map<String, Object> data = res.getData();
            if (MapUtils.isNotEmpty(data)) {
                KeywordsResponse.keywordReturnData.Builder voBuilder = KeywordsResponse.keywordReturnData.newBuilder();
                List<Integer> success = (List<Integer>)data.get("success");
                List<Integer> fail = (List<Integer>)data.get("fail");
                String failMsg = (String) data.get("failMsg");
                if (StringUtils.isNotBlank(failMsg)) {
                    errsStr.append(failMsg);
                }
                if (CollectionUtils.isNotEmpty(success)) {
                    List<Int32Value> successNum = success.stream().filter(Objects::nonNull).map(Int32Value::of).collect(Collectors.toList());
                    voBuilder.addAllSuccess(successNum);
                }
                //前端处理需要传失败以及成功的数据。
                //注意: builder返回-1错误码前端统一捕获异常处理。
                if (CollectionUtils.isNotEmpty(errIndexList)) {
                    voBuilder.addAllFail(errIndexList);
                }
                if (CollectionUtils.isNotEmpty(fail)) {
                    List<Int32Value> failNum = fail.stream().filter(Objects::nonNull).map(Int32Value::of).collect(Collectors.toList());
                    voBuilder.addAllFail(failNum);
                }
                //处理返回结果
                //首先判断添加的关键词是否包含特殊字符，有返回错误信息。
                //部分成功部分失败-》需要回显失败消息传给voBuilder.failMsg。
                if (StringUtils.isNotBlank(errsStr)) {
                    voBuilder.setFailMsg(errsStr.toString());
                }
                builder.setData(voBuilder.build());
            }

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    @Override
    public void getTargets(GetTargetsRequest request, StreamObserver<GetTargetsResponse> responseObserver) {
        log.info("ad-query-获取投放 request: {}",request);
        GetTargetsResponse.Builder builder = GetTargetsResponse.newBuilder();
        //做参数校验
        if(!request.hasPuid() || StringUtils.isEmpty(request.getQuerys())){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        List<QueryVo> list = JSONUtil.jsonToArray(request.getQuerys(),QueryVo.class);
        if(list == null || list.size() ==0){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        for(QueryVo vo : list){
            if(!CheckParamUtil.checkRequired(vo,false,"itemId,itemValue")){
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }
        }
        List<QueryVo> resultList = cpcService.listTargets(request.getPuid().getValue(),list);

        //处理list
        builder.setCode(Int32Value.of(Result.SUCCESS));
        if (CollectionUtils.isNotEmpty(resultList)) {
            List<QueryRpcVo> rpcVos = resultList.stream().filter(Objects::nonNull).map(item -> {
                QueryRpcVo.Builder voBuilder = QueryRpcVo.newBuilder();
                if (item.getIndex() != null) {
                    voBuilder.setIndex(Int32Value.of(item.getIndex()));
                }
                if (item.getItemId() != null) {
                    voBuilder.setItemId(item.getItemId());
                }
                if (item.getCampaignId() != null) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (item.getAdGroupId() != null) {
                    voBuilder.setAdGroupId(item.getAdGroupId());
                }
                if (item.getItemValue() != null) {
                    voBuilder.setItemValue(item.getItemValue());
                }
                if (item.getType() != null) {
                    voBuilder.setType(item.getType());
                }
                if (item.getBid() != null) {
                    voBuilder.setBid(DoubleValue.of(item.getBid()));
                }
                return voBuilder.build();
            }).collect(Collectors.toList());
            builder.addAllData(rpcVos);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 添加asin到商品投放
     * @param request
     * @param responseObserver
     */
    @Override
    public void createTargets(TargetsRequest request, StreamObserver<TargetsResponse> responseObserver) {
        log.info("ad-query-添加asin到商品投放 request: {}",request);
        TargetsResponse.Builder builder = TargetsResponse.newBuilder();

        SubmitQueryDto dto = new SubmitQueryDto();
        dto.setShopId(request.getShopId().getValue());
        dto.setPuid(request.getPuid().getValue());
        dto.setUid(request.getUid().getValue());
        dto.setLoginIp(request.getLoginIp());
        dto.setMarketplaceId(request.getMarketplaceId());
        dto.setType(request.getType());
        //处理list
        List<QueryRpcVo> querysList = request.getQuerysList();
        List<QueryVo> vos = querysList.stream().filter(Objects::nonNull).map(item -> {
            QueryVo queryVo = new QueryVo();
            queryVo.setCampaignId(item.getCampaignId());
            queryVo.setIndex(item.getIndex().getValue());
            queryVo.setItemId(item.getItemId());
            queryVo.setAdGroupId(item.getAdGroupId());
            queryVo.setItemValue(item.getItemValue());
            queryVo.setType(item.getType());
            queryVo.setBid(item.getBid().getValue());
            queryVo.setExpressionType(item.getExpressionType());
            return queryVo;
        }).collect(Collectors.toList());
        dto.setQuerys(vos);

        //做参数校验
        if(!request.hasPuid() || !request.hasShopId() || checkSubmitQueryDto(dto)){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            Result<Map<String, Object>> res = cpcService.createTargets(dto.getPuid(), dto.getUid(), dto.getLoginIp(), dto);
            //处理返回结果
            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
            Map<String, Object> data = res.getData();
            if (MapUtils.isNotEmpty(data)) {
                TargetsResponse.TargetsReturnData.Builder voBuilder = TargetsResponse.TargetsReturnData.newBuilder();
                List<Integer> success = (List<Integer>) data.get("success");
                List<Integer> fail = (List<Integer>) data.get("fail");
                String errMsg = (String) data.get("failMsg");
                if (CollectionUtils.isNotEmpty(success)) {
                    List<Int32Value> successNum = success.stream().filter(Objects::nonNull).map(Int32Value::of).collect(Collectors.toList());
                    voBuilder.addAllSuccess(successNum);
                }
                if (CollectionUtils.isNotEmpty(fail)) {
                    List<Int32Value> failNum = fail.stream().filter(Objects::nonNull).map(Int32Value::of).collect(Collectors.toList());
                    voBuilder.addAllFail(failNum);
                }
                if (StringUtils.isNotBlank(errMsg)) {
                    voBuilder.setFailMsg(errMsg);
                }
                builder.setData(voBuilder.build());
            }

        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 获取建议竞价
     * @param request
     * @param responseObserver
     */
    @Override
    public void getSuggestBidByKeyword(GetSuggestBidByKeywordRequest request, StreamObserver<GetSuggestBidByKeywordResponse> responseObserver) {
        log.info("ad-query-获取建议竞价 request: {}",request);
        GetSuggestBidByKeywordResponse.Builder builder = GetSuggestBidByKeywordResponse.newBuilder();

        if(!request.hasPuid() || !request.hasShopId() || StringUtils.isBlank(request.getAdGroupId())|| StringUtils.isBlank(request.getKeywords())){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        //解析keywords
        List<AmazonAdKeyword> keywordlist = JSONUtil.jsonToArray(request.getKeywords(),AmazonAdKeyword.class);
        if(keywordlist == null || keywordlist.isEmpty()){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        Result<List<BidRecommendationKeyword>> res = cpcService.getKeywordSuggestBid(request.getPuid().getValue(), request.getShopId().getValue(), request.getMarketplaceId(), request.getAdGroupId(), keywordlist);

        //处理返回结果
        builder.setCode(Int32Value.of(res.getCode()));
        if (StringUtils.isNotBlank(res.getMsg())) {
            builder.setMsg(res.getMsg());
        }
        if (res.success() && CollectionUtils.isNotEmpty(res.getData())) {
            List<GetSuggestBidByKeywordResponse.BidRecommendationKeyword> rpcVos = res.getData().stream().filter(Objects::nonNull).map(item -> {
                GetSuggestBidByKeywordResponse.BidRecommendationKeyword.Builder voBuilder = GetSuggestBidByKeywordResponse.BidRecommendationKeyword.newBuilder();
                if (item.getCode() != null) {
                    voBuilder.setCode(item.getCode());
                }
                if (item.getKeyword() != null) {
                    voBuilder.setKeyword(item.getKeyword());
                }
                if (item.getMatchType() != null) {
                    voBuilder.setMatchType(item.getMatchType());
                }
                if (item.getSuggestedBid() != null) {

                    GetSuggestBidByKeywordResponse.BidRecommendationKeyword.SuggestedBid.Builder suggestedBidBuilder = GetSuggestBidByKeywordResponse.BidRecommendationKeyword.SuggestedBid.newBuilder();

                    if (item.getSuggestedBid().getRangeStart() != null) {
                        suggestedBidBuilder.setRangeStart(DoubleValue.of(item.getSuggestedBid().getRangeStart()));
                    }
                    if (item.getSuggestedBid().getRangeEnd() != null) {
                        suggestedBidBuilder.setRangeEnd(DoubleValue.of(item.getSuggestedBid().getRangeEnd()));
                    }
                    if (item.getSuggestedBid().getSuggested() != null) {
                        suggestedBidBuilder.setSuggested(DoubleValue.of(item.getSuggestedBid().getSuggested()));
                    }

                    voBuilder.setSuggestedBid(suggestedBidBuilder.build());
                }
                return voBuilder.build();
            }).collect(Collectors.toList());

            builder.addAllData(rpcVos);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    private boolean checkSubmitQueryDto(SubmitQueryDto dto) {
        if(!CheckParamUtil.checkRequired(dto,false,"shopId,marketplaceId,querys,type")){
            return true;
        }
        List<QueryVo> list = dto.getQuerys();
        if(list.isEmpty()){
            return true;
        }
        String requiredField = Constants.BIDDABLE.equals(dto.getType()) || Constants.TARGETING_TYPE_ASIN.equals(dto.getType())
                ?"adGroupId,campaignId,itemValue,type,bid,index":"adGroupId,campaignId,itemValue,type,index";
        for(QueryVo vo : list){
            if(!CheckParamUtil.checkRequired(vo,false,requiredField)){
                return true;
            }
        }
        return false;
    }


    /**
     * 获取搜索词建议竞价
     */
    @Override
    public void getQuerySuggestBid(GetQuerySuggestBidRequest request, StreamObserver<GetQuerySuggestBidResponse> responseObserver) {
        log.info("ad-query-获取搜索词建议竞价 request: {}",request);
        GetQuerySuggestBidResponse.Builder builder = GetQuerySuggestBidResponse.newBuilder();

        if(!request.hasPuid() || !request.hasShopId() || StringUtils.isBlank(request.getAdGroupId())|| StringUtils.isBlank(request.getQuery()) ||  StringUtils.isBlank(request.getType())){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            Result<List<TargetingBidRecommendation>> res = cpcService.getQuerySuggestBid(request.getPuid().getValue(), request.getShopId().getValue(), request.getAdGroupId(), request.getQuery(), request.getType());
            //处理返回结果
            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
            if (res.success() && CollectionUtils.isNotEmpty(res.getData())) {
                List<GetQuerySuggestBidResponse.TargetingBidRecommendation> rpcVos = res.getData().stream().map(item -> {

                    GetQuerySuggestBidResponse.TargetingBidRecommendation.Builder voBuilder = GetQuerySuggestBidResponse.TargetingBidRecommendation.newBuilder();
                    voBuilder.setCode(item.getCode());
                    //处理list
                    List<Expression> expressions = item.getExpression();
                    if (CollectionUtils.isNotEmpty(expressions)) {
                        List<GetQuerySuggestBidResponse.TargetingBidRecommendation.Expression> expressionRpcVos = expressions.stream().filter(Objects::nonNull).map(innerItem -> {
                            GetQuerySuggestBidResponse.TargetingBidRecommendation.Expression.Builder expressionBuilder = GetQuerySuggestBidResponse.TargetingBidRecommendation.Expression.newBuilder();
                            if (innerItem.getType() != null) {
                                expressionBuilder.setType(innerItem.getType());
                            }
                            if (innerItem.getValue() != null) {
                                expressionBuilder.setValue(innerItem.getValue());
                            }
                            return expressionBuilder.build();
                        }).collect(Collectors.toList());
                        voBuilder.addAllExpression(expressionRpcVos);
                    }

                    SuggestedBid suggestedBid = item.getSuggestedBid();
                    if (suggestedBid!=null) {
                        GetQuerySuggestBidResponse.TargetingBidRecommendation.SuggestedBid.Builder suggestedBidBuilder = GetQuerySuggestBidResponse.TargetingBidRecommendation.SuggestedBid.newBuilder();
                        if (suggestedBid.getRangeStart() != null) {
                            suggestedBidBuilder.setRangeStart(DoubleValue.of(suggestedBid.getRangeStart()));
                        }
                        if (suggestedBid.getRangeEnd() != null) {
                            suggestedBidBuilder.setRangeEnd(DoubleValue.of(suggestedBid.getRangeEnd()));
                        }
                        if (suggestedBid.getSuggested() != null) {
                            suggestedBidBuilder.setSuggested(DoubleValue.of(suggestedBid.getSuggested()));
                        }
                        voBuilder.setSuggestedBid(suggestedBidBuilder.build());
                    }



                    return voBuilder.build();
                }).collect(Collectors.toList());

                builder.addAllData(rpcVos);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    /**
     * 获取名称
     * @param request
     * @param responseObserver
     */
    @Override
    public void getNames(QueryGetNamesRequest request, StreamObserver<QueryGetNamesResponse> responseObserver) {
        log.info("ad-query-获取名称 request: {}",request);
        QueryGetNamesResponse.Builder builder = QueryGetNamesResponse.newBuilder();

        GetNamesDto dto = new GetNamesDto();
        dto.setPuid(request.getPuid().getValue());
        dto.setShopId(request.getShopId().getValue());
        dto.setMarketplaceId(request.getMarketplaceId());
        dto.setItemType(request.getItemType());
        dto.setCampaignId(request.getCampaignId());
        dto.setTargetingType(request.getTargetingType());
        dto.setAdGroupType(request.getAdGroupType());
        dto.setNegativeKeyword(request.getNegativeKeyword().getValue());
        dto.setNegativeProduct(request.getNegativeProduct().getValue());
        dto.setType(request.getType());

        //检查参数
        if(!CheckParamUtil.checkRequired(dto,false,"shopId,marketplaceId,itemType")){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            Map<String, String> map = cpcService.getNames(dto.getPuid(), dto);

            //处理返回结果
            builder.setCode(Int32Value.of(Result.SUCCESS));
            if (MapUtils.isNotEmpty(map)) {
                builder.putAllData(map);
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告活动和广告组
     * @param request
     * @param responseObserver
     */
    @Override
    public void campAndGroup(GetCampAndGroupRequest request, StreamObserver<GetCampAndGroupResponse> responseObserver) {
        log.info("ad-query-广告活动和广告组 request: {}",request);
        GetCampAndGroupResponse.Builder builder = GetCampAndGroupResponse.newBuilder();
        //检查参数
        if(!request.hasShopId() || !request.hasPuid() || StringUtils.isBlank(request.getMarketplaceId())){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            List<CampAndGroupVo> campAndGroups = cpcService.getCampAndGroup(request.getPuid().getValue(), request.getShopId().getValue(), request.getMarketplaceId());
            builder.setCode(Int32Value.of(Result.SUCCESS));
            if (CollectionUtils.isNotEmpty(campAndGroups)) {
                List<GetCampAndGroupResponse.CamAndGroupData> rpcVos = campAndGroups.stream().filter(Objects::nonNull).map(item -> {
                    GetCampAndGroupResponse.CamAndGroupData.Builder voBuilder = GetCampAndGroupResponse.CamAndGroupData.newBuilder();
                    if (item.getCampaignName() != null) {
                        voBuilder.setCampaignName(item.getCampaignName());
                    }
                    if (item.getCampaignId() != null) {
                        voBuilder.setCampaignId(item.getCampaignId());
                    }
                    if (item.getState() != null) {
                        voBuilder.setState(item.getState());
                    }
                    if (item.getType() != null) {
                        voBuilder.setType(item.getType());
                    }
                    if (item.getTargetingType() != null) {
                        voBuilder.setTargetingType(item.getTargetingType());
                    }
                    if (item.getAdGroupMap() != null) {
                        voBuilder.putAllAdGroupMap(item.getAdGroupMap());
                    }
                    return voBuilder.build();
                }).collect(Collectors.toList());

                builder.addAllData(rpcVos);
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private boolean checkQueryWordDto(CpcQueryWordDto dto) {
        if (!CheckParamUtil.checkRequired(dto, false, "shopId,marketplaceId,start,end")) {
            return true;
        }
        dto.setStart(DateUtil.dateToStrWithFormat(DateUtil.strToDate(dto.getStart(), "yyyy-MM-dd"), "yyyyMMdd"));
        dto.setEnd(DateUtil.dateToStrWithFormat(DateUtil.strToDate(dto.getEnd(), "yyyy-MM-dd"), "yyyyMMdd"));
        return false;
    }

    private String checkQueryWordExportDto(CpcQueryWordDto dto) {
        if (!CheckParamUtil.checkRequired(dto, false, "shopId,marketplaceId,start,end")) {
            return "请求参数错误";
        }

        dto.setStart(DateUtil.dateToStrWithFormat(DateUtil.strToDate(dto.getStart(), "yyyy-MM-dd"), "yyyyMMdd"));
        dto.setEnd(DateUtil.dateToStrWithFormat(DateUtil.strToDate(dto.getEnd(), "yyyy-MM-dd"), "yyyyMMdd"));

        if (StringUtils.isNotBlank(dto.getExportSortField())) {
            //排序字段校验
            try {
                String[] split = dto.getExportSortField().split(",");
                for (String s : split) {
                    if (!AdvertisingQueryKeywordExportFieldEnum.getPoParamKeyList().contains(s)) {
                        return "请求参数错误" + s;
                    }
                }
            } catch (Exception e) {
                return "请求参数错误" + dto.getExportSortField();
            }
        }
        return null;
    }

    private String checkQueryTargetExportDto(CpcQueryWordDto dto) {
        if (!CheckParamUtil.checkRequired(dto, false, "shopId,marketplaceId,start,end")) {
            return "请求参数错误";
        }

        dto.setStart(DateUtil.dateToStrWithFormat(DateUtil.strToDate(dto.getStart(), "yyyy-MM-dd"), "yyyyMMdd"));
        dto.setEnd(DateUtil.dateToStrWithFormat(DateUtil.strToDate(dto.getEnd(), "yyyy-MM-dd"), "yyyyMMdd"));

        if (StringUtils.isNotBlank(dto.getExportSortField())) {
            //排序字段校验
            try {
                String[] split = dto.getExportSortField().split(",");
                for (String s : split) {
                    if (!AdvertisingQueryTargetExportFieldEnum.getPoParamKeyList().contains(s)) {
                        return "请求参数错误" + s;
                    }
                }
            } catch (Exception e) {
                return "请求参数错误" + dto.getExportSortField();
            }
        }
        return null;
    }

    private Double calculationRateDouble(Double value1, Double value2) {
        return value2 == 0 ? 0 : DoubleUtil.divide(value1 * 100, value2, 2);
    }

    /**
     * 自动化规则获取搜索词数据
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAutoRuleQueryDataList(AutoRuleQueryDateListRequest request, StreamObserver<AutoRuleQueryDateListResponse> responseObserver) {
        log.info("ad-query-广告活动和广告组 request: {}",request);
        AutoRuleQueryDateListResponse.Builder builder = AutoRuleQueryDateListResponse.newBuilder();
        //检查参数
        if(!request.hasShopId() || !request.hasPuid() || StringUtils.isBlank(request.getMarketplaceId())){
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            AutoRuleQueryWordDto dto = new AutoRuleQueryWordDto() ;
            dto.setQuerys(request.getQuerysList());
            dto.setCampaignId(request.getCampaignId());
            dto.setGroupId(request.getGroupId());
            dto.setEnd(request.getEnd().replace("-",""));
            dto.setPuid(request.getPuid().getValue());
            dto.setShopId(request.getShopId().getValue());
            dto.setMarketplaceId(request.getMarketplaceId());
            dto.setStart(request.getStart().replace("-",""));
            List<AutoRuleQueryWordRuleParam> ruleQueryWordRuleParams = request.getRuleParamsList().stream().map(e -> {
                AutoRuleQueryWordRuleParam ruleParam = new AutoRuleQueryWordRuleParam();
                ruleParam.setRuleIndex(RuleIndexType.valueOf(e.getRuleIndex()));
                ruleParam.setDay(e.getDay());
                ruleParam.setComparator(RuleOperatorType.valueOf(e.getComparator()));
                ruleParam.setValue(e.getValue());
                ruleParam.setAfterValue(e.getAfterValue());
                ruleParam.setRuleStatisticalModeType(RuleStatisticalModeType.valueOf(e.getRuleStatisticalModeType()));
                return ruleParam;
            }).collect(Collectors.toList());
            dto.setRuleParams(ruleQueryWordRuleParams);
            dto.setType(request.getType());
            if (StringUtils.isNotBlank(request.getTargetType())) {
                if (AddNegativeTargetType.keyword.name().equals(request.getTargetType())) {
                    dto.setTargetType(AddNegativeTargetType.keyword);
                } else {
                    dto.setTargetType(AddNegativeTargetType.targeting);
                }
            }

            if (StringUtils.isNotBlank(request.getQueryId())) {
                dto.setQueryId(request.getQueryId());
            }

            List<AutoRuleQueryReportDataRpcVo> autoRuleDataList = cpcQueryKeywordReportService.getAutoRuleDataList(request.getPuid().getValue(), request.getShopId().getValue(), dto);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            if(CollectionUtils.isNotEmpty(autoRuleDataList)){
                builder.addAllData(autoRuleDataList);
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }



    /**
     * 搜索词(投放)详情
     * @return
     */
    @Override
    public void searchTermsDetail(SearchTermsDetailRequest request, StreamObserver<AdHourReportResponsePb.AdHourReportResponse> responseObserver) {
        log.info("ad-query-搜索词(投放)详情 request: {}",request);
        AdHourReportResponsePb.AdHourReportResponse.Builder builder = AdHourReportResponsePb.AdHourReportResponse.newBuilder();
        builder.setCode(Result.SUCCESS);

        //做参数校验

        CpcQueryWordDetailDto dto = new CpcQueryWordDetailDto();
        dto.setPuid(request.getPuid().getValue());
        dto.setUid(request.getUid().getValue());
        dto.setLoginIp(request.getLoginIp());
        dto.setShopId(request.getShopId().getValue());
        dto.setMarketplaceId(request.getMarketplaceId());
        dto.setKeywordId(request.getKeywordId());
        dto.setTargetId(request.getTargetId());
        dto.setQuery(request.getQuery());
        dto.setStart(request.getStart());
        dto.setEnd(request.getEnd());
        dto.setDateType(request.getDateType());
        dto.setOrderField(request.getOrderField());
        dto.setOrderValue(request.getOrderValue());
        dto.setIsTarget(request.getIsTargetType());
        dto.setType(request.getType());
        dto.setIsCompare(request.getIsCompare());
        dto.setStartDateCompare(request.getStartDateCompare());
        dto.setEndDateCompare(request.getEndDateCompare());

        AdHourReportResponsePb.AdHourReportResponse.AdHour.Builder reportBuilder =
                AdHourReportResponsePb.AdHourReportResponse.AdHour.newBuilder();

        List<AdQueryKeywordAndTargetVo> list = new ArrayList<>();
        List<AdQueryKeywordAndTargetVo> compares = new ArrayList<>();
        if (!request.hasPuid() || !request.hasShopId() || checkQueryWordDetailDto(dto)
                || (request.hasIsCompare() && request.getIsCompare() == 1 && (!request.hasStartDateCompare() || !request.hasEndDateCompare()))) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {

            if (KeywordViewHourParam.DateModel.DAILY.name().equalsIgnoreCase(dto.getDateType())) {
                list = cpcQueryReportDetailService.getDailyList(dto.getPuid(), dto.getType(), dto, compares);
            } else if (KeywordViewHourParam.DateModel.WEEKLY.name().equalsIgnoreCase(dto.getDateType())) {
                list = cpcQueryReportDetailService.getWeeklyList(dto.getPuid(), dto.getType(), dto, compares);
            } else if (KeywordViewHourParam.DateModel.MONTHLY.name().equalsIgnoreCase(dto.getDateType())) {
                list = cpcQueryReportDetailService.getMonthlyList(dto.getPuid(), dto.getType(), dto, compares);
            }

            if (list == null) {
                list = new ArrayList<>();
            }

            if (CollectionUtils.isNotEmpty(list)) {
                boolean isSorted = StringUtils.isNotBlank(dto.getOrderField()) &&
                        Constants.isADOrderField(dto.getOrderField(), AdQueryKeywordAndTargetVo.class);
                if (isSorted) {
                    PageUtil.sortedByOrderField(list, dto.getOrderField(), dto.getOrderValue());
                }
            }
            //取店铺销售额
            BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(dto.getShopId(), dto.getStart().replace("-",""), dto.getEnd().replace("-",""));
            reportBuilder.addAllList(list.stream().filter(Objects::nonNull).map(e -> PbUtil.toQueryReportPb(e, shopSalesByDate)).collect(Collectors.toList()));
            AdQueryKeywordAndTargetVo summaryVO = summaryQeuryVo(list, shopSalesByDate);
            if (dto.getIsCompare() == 1) {
                AdQueryKeywordAndTargetVo compareVO = summaryQeuryVo(compares, shopSalesByDate);
                summaryVO.compareDataSet(compareVO);
            }
            reportBuilder.setSummary(PbUtil.toQueryReportPb(summaryVO, shopSalesByDate));
            reportBuilder.addAllChart(ReportChartUtil.getQueryKeywordChartData(list, false));

            if (dto.getIsCompare() == 1) {
                List<AdQueryKeywordAndTargetVo> compareHourVos = list.stream().map(item -> {
                    AdQueryKeywordAndTargetVo vo = new AdQueryKeywordAndTargetVo();
                    vo.setLabel(item.getLabel());
                    vo.setClicks(item.getClicksCompare());
                    vo.setImpressions(item.getImpressionsCompare());
                    vo.setAdSale(item.getAdSaleCompare());
                    vo.setAdCost(item.getAdCostCompare());
                    vo.setAdOrderNum(item.getAdOrderNumCompare());
                    vo.setAdSaleNum(item.getAdSaleNumCompare());
                    vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                    vo.setAcos(item.getAcosCompare());
                    vo.setCtr(item.getCtrCompare());
                    vo.setCvr(item.getCvrCompare());
                    vo.setRoas(item.getRoasCompare());
                    vo.setCpa(item.getCpaCompare());
                    return vo;
                }).collect(Collectors.toList());
                reportBuilder.addAllChart(ReportChartUtil.getQueryKeywordChartData(compareHourVos, true));
            }

            builder.setCode(Result.SUCCESS);
            builder.setData(reportBuilder.build());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    private AdQueryKeywordAndTargetVo summaryQeuryVo(List<AdQueryKeywordAndTargetVo> list, BigDecimal shopSalesByDate) {
        AdQueryKeywordAndTargetVo vo = new AdQueryKeywordAndTargetVo();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        for (AdQueryKeywordAndTargetVo ad : list) {
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
            vo.setAdCostCompare(MathUtil.add(vo.getAdCostCompare(), ad.getAdCostCompare()));
            vo.setClicksCompare(MathUtil.add(vo.getClicksCompare(), ad.getClicksCompare()));
            vo.setImpressionsCompare(MathUtil.add(vo.getImpressionsCompare(), ad.getImpressionsCompare()));
            vo.setAdSaleNumCompare(MathUtil.add(vo.getAdSaleNumCompare(), ad.getAdSaleNumCompare()));
            vo.setAdSaleCompare(MathUtil.add(vo.getAdSaleCompare(), ad.getAdSaleCompare()));
            vo.setAdOrderNumCompare(MathUtil.add(vo.getAdOrderNumCompare(), ad.getAdOrderNumCompare()));

            vo.setAdCostPercentage(MathUtil.add(vo.getAdCostPercentage(), ad.getAdCostPercentage()));
            vo.setAdSalePercentage(MathUtil.add(vo.getAdSalePercentage(), ad.getAdSalePercentage()));
            vo.setAdOrderNumPercentage(MathUtil.add(vo.getAdOrderNumPercentage(), ad.getAdOrderNumPercentage()));
            vo.setOrderNumPercentage(MathUtil.add(vo.getOrderNumPercentage(), ad.getOrderNumPercentage()));
            vo.setViewableImpressions(MathUtil.add(vo.getViewableImpressions(),ad.getViewableImpressions()));
            vo.setOrdersNewToBrand(MathUtil.add(vo.getOrdersNewToBrand(),ad.getOrdersNewToBrand()));
            vo.setUnitsOrderedNewToBrand(MathUtil.add(vo.getUnitsOrderedNewToBrand(),ad.getUnitsOrderedNewToBrand()));
            vo.setSalesNewToBrand(MathUtil.add(vo.getSalesNewToBrand(),ad.getSalesNewToBrand()));
        }
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));

        vo.setAdCostPerClickCompare(MathUtil.divideByZero(vo.getAdCostCompare(), BigDecimal.valueOf(vo.getClicksCompare())));
        vo.setCpaCompare(vo.getAdOrderNumCompare() == 0 ? BigDecimal.ZERO : vo.getAdCostCompare().divide(BigDecimal.valueOf(vo.getAdOrderNumCompare()), 4, RoundingMode.HALF_UP));
        vo.setAcosCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                vo.getAdCostCompare().multiply(new BigDecimal("100")).divide(vo.getAdSaleCompare(), 4, RoundingMode.HALF_UP));
        vo.setRoasCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                vo.getAdSaleCompare().divide(vo.getAdCostCompare(), 4, RoundingMode.HALF_UP));
        vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicksCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressionsCompare())));
        vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNumCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicksCompare())));

        vo.setVcpm(MathUtil.divideByThousand(vo.getAdCost(), vo.getViewableImpressions()));
        vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
        vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getAdOrderNum() - vo.getSelfAdOrderNum())));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)),BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)),BigDecimal.valueOf(vo.getAdSaleNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));
        if (vo.getAdCostPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdCostPercentage()) == 0) {
            vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdCostPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getAdSalePercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdSalePercentage()) == 0) {
            vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdSalePercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getAdOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdOrderNumPercentage()) == 0) {
            vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdOrderNumPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getOrderNumPercentage()) == 0) {
            vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setOrderNumPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        vo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate));
        vo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate));
        vo.calculateCompareRate();//为各个对比率进行设值
        return vo;
    }


//    @Override
//    public void searchTermsDetailExclude(SearchTermsDetailRequest request, StreamObserver<com.meiyunji.sponsored.rpc.export.UrlResponse> responseObserver) {
//        log.info("request: {}", request);
//        try {
//            com.meiyunji.sponsored.rpc.export.UrlResponse.Builder builder = com.meiyunji.sponsored.rpc.export.UrlResponse.newBuilder();
//            //检查参数
//            if (!request.hasPuid() || !request.hasShopId() || !request.hasEnd() || !request.hasStart()) {
//                builder.setCode(Int32Value.of(Result.ERROR));
//                builder.setMsg("请求参数错误");
//            } else {
//                int count = 0;
//                String downloadUrl;
//                List<String> urls = Lists.newLinkedList();
//                CpcQueryWordDetailDto dto = new CpcQueryWordDetailDto();
//                dto.setPuid(request.getPuid().getValue());
//                dto.setUid(request.getUid().getValue());
//                dto.setLoginIp(request.getLoginIp());
//                dto.setShopId(request.getShopId().getValue());
//                dto.setMarketplaceId(request.getMarketplaceId());
//                dto.setKeywordId(request.getKeywordId());
//                dto.setTargetId(request.getTargetId());
//                dto.setQuery(request.getQuery());
//                dto.setStart(request.getStart());
//                dto.setEnd(request.getEnd());
//                dto.setDateType(request.getDateType());
//                dto.setOrderField(request.getOrderField());
//                dto.setOrderValue(request.getOrderValue());
//                dto.setIsTarget(request.getIsTargetType());
//                dto.setType(request.getType());
//                List<AdKeywordAndTargetHourVo> voList = new ArrayList<>();
//
//                if (KeywordViewHourParam.DateModel.DAILY.name().equalsIgnoreCase(dto.getDateType())) {
//                    list = cpcQueryReportDetailService.getDailyList(dto.getPuid(), dto.getType(), dto);
//                } else if (KeywordViewHourParam.DateModel.WEEKLY.name().equalsIgnoreCase(dto.getDateType())) {
//                    list = cpcQueryReportDetailService.getDailyList(dto.getPuid(), dto.getType(), dto);
//                } else if (KeywordViewHourParam.DateModel.MONTHLY.name().equalsIgnoreCase(dto.getDateType())) {
//                    list = cpcQueryReportDetailService.getDailyList(dto.getPuid(), dto.getType(), dto);
//                }
//
//                ShopAuth auth = shopAuthDao.getById(param.getShopId());
//                List<String> voExcludeFileds = new ArrayList<>();
//                if (!Integer.valueOf(1).equals(param.getIsCompare()) || request.getDateModel() != ReportDateModelPb.ReportDateModel.HOURLY) {
//                    voExcludeFileds = Lists.newArrayList("costCompare", "costCompareRate", "impressionsCompare", "costCompare", "impressionsCompareRate",
//                            "clicksCompareRate", "adOrderNumCompare", "adOrderNumCompareRate", "adSalesCompare",
//                            "adSalesCompareRate", "adSaleNumCompare", "adSaleNumCompareRate", "clicksCompare");
//
//                }
//                //SB不需要销量字段
//                if ("SB".equalsIgnoreCase(request.getType())) {
//                    voExcludeFileds.addAll(Lists.newArrayList("vcpm"));
//                }
//                //SD
//                if ("SD".equalsIgnoreCase(request.getType())) {
//
//                }
//                if (request.getDateModel() != ReportDateModelPb.ReportDateModel.HOURLY) {
//                    voExcludeFileds.addAll(Lists.newArrayList("acots", "asots", "viewableImpressions", "vrt", "vctr", "ordersNewToBrand",
//                            "unitsOrderedNewToBrand", "salesNewToBrand", "advertisingUnitPrice", "advertisingProductUnitPrice", "advertisingOtherProductUnitPrice",
//                            "ordersNewToBrandPercentage", "unitsOrderedNewToBrandPercentage", "salesNewToBrandPercentage",
//                            "adCostPercentage", "adSalePercentage", "adOrderNumPercentage", "orderNumPercentage"));
//                }
//                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
//                List<AdKeywordTargetHourWithCompareExportVo> datas = new ArrayList<>(24);
//                for (AdKeywordAndTargetHourVo vo : voList) {
//                    AdKeywordTargetHourWithCompareExportVo exVo = new AdKeywordTargetHourWithCompareExportVo(currencyCode, vo);
//                    datas.add(exVo);
//                }
//
//                if (request.getDateModel() != ReportDateModelPb.ReportDateModel.HOURLY) {
//                    Field filed = AdKeywordTargetHourWithCompareExportVo.class.getDeclaredField("label");
//                    filed.setAccessible(true);
//                    ExcelProperty annotation = filed.getAnnotation(ExcelProperty.class);
//                    InvocationHandler invocationHandler = Proxy.getInvocationHandler(annotation);
//                    Field memberValues = invocationHandler.getClass().getDeclaredField("memberValues");
//                    memberValues.setAccessible(true);
//                    Map<String, Object> map = (Map<String, Object>) memberValues.get(invocationHandler);
//                    String[] valueArr = {"日期"};
//                    map.put("value", valueArr);
//                }
//
//                //excel币种表头渲染
//                WriteHandlerBuild build = new WriteHandlerBuild().rate();
//                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
//                        request.getFileName() + "(" + count++ + ")", AdKeywordTargetHourWithCompareExportVo.class,
//                        build.currencyNew(AdKeywordTargetHourWithCompareExportVo.class), voExcludeFileds);
//                urls.add(downloadUrl);
//                builder.addAllUrls(urls);
//                builder.setCode(Int32Value.of(Result.SUCCESS));
//            }
//            responseObserver.onNext(builder.build());
//        } catch (Exception e) {
//            log.error("", e);
//            responseObserver.onError(e);
//        }
//        responseObserver.onCompleted();
//    }

    private void queryWordPageParamDateFormat(CpcQueryWordDto param) {
        //日期转换格式
        param.setStart(DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getStart(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        param.setEnd(DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getEnd(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        param.setCompareStartDate(StringUtils.isEmpty(param.getCompareStartDate()) ? null : DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getCompareStartDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        param.setCompareEndDate(StringUtils.isEmpty(param.getCompareEndDate()) ? null : DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getCompareEndDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
    }

    @Override
    public void allSearchTermsDetail(SearchTermsDetailRequest request, StreamObserver<AdHourReportResponsePb.AdHourReportResponse> responseObserver) {
        log.info("allSearchTermsDetail 搜索词详情 request:{}", ProtoBufUtil.toJson(request));
        AdHourReportResponsePb.AdHourReportResponse.Builder builder = AdHourReportResponsePb.AdHourReportResponse.newBuilder();
        builder.setCode(Result.SUCCESS);

        CpcQueryWordDetailDto dto = new CpcQueryWordDetailDto();
        dto.setPuid(request.getPuid().getValue());
        dto.setUid(request.getUid().getValue());
        dto.setLoginIp(request.getLoginIp());
        dto.setMarketplaceId(request.getMarketplaceId());
        dto.setQuery(request.getQuery());
        dto.setStart(request.getStart());
        dto.setEnd(request.getEnd());
        dto.setDateType(request.getDateType());
        dto.setOrderField(request.getOrderField());
        dto.setOrderValue(request.getOrderValue());
        dto.setShopIds(request.getShopIdsList());
        dto.setSearchTermIds(request.getSearchTermIdsList());
        dto.setIsCompare(request.getIsCompare());
        dto.setStartDateCompare(request.getStartDateCompare());
        dto.setEndDateCompare(request.getEndDateCompare());

        AdHourReportResponsePb.AdHourReportResponse.AdHour.Builder reportBuilder =
                AdHourReportResponsePb.AdHourReportResponse.AdHour.newBuilder();

        List<AdQueryKeywordAndTargetVo> list = new ArrayList<>();
        List<AdQueryKeywordAndTargetVo> compares = new ArrayList<>();
        if (KeywordViewHourParam.DateModel.DAILY.name().equalsIgnoreCase(dto.getDateType())) {
            list = cpcQueryReportDetailService.allSearchTermDailyList(dto.getPuid(), dto, compares);
        } else if (KeywordViewHourParam.DateModel.WEEKLY.name().equalsIgnoreCase(dto.getDateType())) {
            list = cpcQueryReportDetailService.allSearchTermWeeklyList(dto.getPuid(), dto, compares);
        } else if (KeywordViewHourParam.DateModel.MONTHLY.name().equalsIgnoreCase(dto.getDateType())) {
            list = cpcQueryReportDetailService.allSearchTermMonthlyList(dto.getPuid(), dto, compares);
        }
        if (list == null) {
            list = new ArrayList<>();
        }
        if (CollectionUtils.isNotEmpty(list)) {
            boolean isSorted = StringUtils.isNotBlank(dto.getOrderField()) &&
                    Constants.isADOrderField(dto.getOrderField(), AdQueryKeywordAndTargetVo.class);
            if (isSorted) {
                PageUtil.sortedByOrderField(list, dto.getOrderField(), dto.getOrderValue());
            }
        }

        reportBuilder.addAllList(list.stream().filter(Objects::nonNull).map(e -> PbUtil.toQueryReportPb(e, null)).collect(Collectors.toList()));
        AdQueryKeywordAndTargetVo summaryVO = summaryQeuryVo(list, null);
        if (dto.getIsCompare() == 1) {
            AdQueryKeywordAndTargetVo compareVO = summaryQeuryVo(compares, null);
            summaryVO.compareDataSet(compareVO);
        }
        reportBuilder.setSummary(PbUtil.toQueryReportPb(summaryVO, null));
        reportBuilder.addAllChart(ReportChartUtil.getQueryKeywordChartData(list, false));

        if (dto.getIsCompare() == 1) {
            List<AdQueryKeywordAndTargetVo> compareHourVos = list.stream().map(item -> {
                AdQueryKeywordAndTargetVo vo = new AdQueryKeywordAndTargetVo();
                vo.setLabel(item.getLabel());
                vo.setClicks(item.getClicksCompare());
                vo.setImpressions(item.getImpressionsCompare());
                vo.setAdSale(item.getAdSaleCompare());
                vo.setAdCost(item.getAdCostCompare());
                vo.setAdOrderNum(item.getAdOrderNumCompare());
                vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                vo.setAcos(item.getAcosCompare());
                vo.setCtr(item.getCtrCompare());
                vo.setCvr(item.getCvrCompare());
                vo.setRoas(item.getRoasCompare());
                vo.setCpa(item.getCpaCompare());
                return vo;
            }).collect(Collectors.toList());
            reportBuilder.addAllChart(ReportChartUtil.getQueryKeywordChartData(compareHourVos, true));
        }

        builder.setCode(Result.SUCCESS);
        builder.setData(reportBuilder.build());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }
}
