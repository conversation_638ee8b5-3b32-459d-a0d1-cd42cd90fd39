package com.meiyunji.sponsored.api.attribution;

import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.PageUtil;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.rpc.attribution.*;
import com.meiyunji.sponsored.rpc.vo.CommonResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.attribution.dao.IAmazonAdAttributionCampaignSequenceDao;
import com.meiyunji.sponsored.service.attribution.dao.IAmazonAdAttributionCreativeIdSequenceDao;
import com.meiyunji.sponsored.service.attribution.dao.IAmazonAdAttributionGroupIdSequenceDao;
import com.meiyunji.sponsored.service.attribution.entity.*;
import com.meiyunji.sponsored.service.attribution.service.IAmazonAdAttributionReportService;
import com.meiyunji.sponsored.service.attribution.service.impl.AmazonAdAttributionPublisherService;
import com.meiyunji.sponsored.service.attribution.service.impl.AmazonAdAttributionService;
import com.meiyunji.sponsored.service.attribution.service.impl.AmazonAdAttributionTagService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2022/3/14 15:09
 * @describe:
 */
@GRpcService
@Slf4j
public class AttributionRpcService extends RPCAdAttributionServiceGrpc.RPCAdAttributionServiceImplBase {

    private final AmazonAdAttributionPublisherService publisherService;
    private final AmazonAdAttributionService amazonAdAttributionService;
    private final IAmazonAdProfileDao amazonAdProfileDao;
    private final IScVcShopAuthDao shopAuthDao;
    private final AmazonAdAttributionTagService amazonAdAttributionTagService;
    private final IAmazonAdAttributionCampaignSequenceDao campaignSequenceDao;
    private final IAmazonAdAttributionGroupIdSequenceDao groupIdSequenceDao;
    private final IAmazonAdAttributionCreativeIdSequenceDao creativeIdSequenceDao;
    private final IAmazonAdAttributionReportService amazonAdAttributionReportService;
    private final RedisService redisService;


    public AttributionRpcService(AmazonAdAttributionPublisherService publisherService,
                                 AmazonAdAttributionService amazonAdAttributionService,
                                 IAmazonAdProfileDao amazonAdProfileDao, IScVcShopAuthDao shopAuthDao,
                                 AmazonAdAttributionTagService amazonAdAttributionTagService,
                                 IAmazonAdAttributionCampaignSequenceDao campaignSequenceDao,
                                 IAmazonAdAttributionGroupIdSequenceDao groupIdSequenceDao,
                                 IAmazonAdAttributionCreativeIdSequenceDao creativeIdSequenceDao,
                                 IAmazonAdAttributionReportService amazonAdAttributionReportService, RedisService redisService) {
        this.publisherService = publisherService;
        this.amazonAdAttributionService = amazonAdAttributionService;
        this.amazonAdProfileDao = amazonAdProfileDao;
        this.shopAuthDao = shopAuthDao;
        this.amazonAdAttributionTagService = amazonAdAttributionTagService;
        this.campaignSequenceDao = campaignSequenceDao;
        this.groupIdSequenceDao = groupIdSequenceDao;
        this.creativeIdSequenceDao = creativeIdSequenceDao;
        this.amazonAdAttributionReportService = amazonAdAttributionReportService;
        this.redisService = redisService;
    }

    @Override
    public void checkStatus(CheckStatusRequest request, StreamObserver<CheckStatusResponse> responseObserver) {
        CheckStatusResponse.Builder builder = CheckStatusResponse.newBuilder();
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(request.getShopId());
        if (shopAuth == null) {
            builder.setCode(Result.ERROR);
            builder.setMsg("店铺不存在");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        AmazonAdProfile profile = amazonAdAttributionService.getAttributionAdvertiserInfo(shopAuth.getPuid(), shopAuth.getId());
        builder.setCode(Result.SUCCESS);
        builder.setData(profile != null);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getPublishers(PublisherRequest request, StreamObserver<PublisherResponse> responseObserver) {
        PublisherResponse.Builder builder = PublisherResponse.newBuilder();
        List<AmazonAdAttributionPublisher> publishers = publisherService.getPublishers(request.getName(), false);
        if (CollectionUtils.isEmpty(publishers)) {
            builder.setCode(Result.ERROR);
            builder.setMsg("暂无可用渠道");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        List<PublisherResponse.Publisher> publisherList = publishers.stream().map(item -> {
            PublisherResponse.Publisher.Builder publisherBuilder = PublisherResponse.Publisher.newBuilder();
            publisherBuilder.setPublisherId(item.getPublisherId());
            publisherBuilder.setPublisherName(item.getPublisherName());
            publisherBuilder.setMarcoEnable(item.getMacroEnable() == 1);
            return publisherBuilder.build();
        }).collect(Collectors.toList());
        builder.addAllData(publisherList);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void productAdd(AttributionProductAddRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("productAdd request: {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        int shopId = request.getShopId();
        String publisherId = request.getPublisherId();
        String name = request.getName();
        String remark = request.getRemark();
        String userName = request.getCreateUserName();
        Integer createId = request.getCreateId();


        List<AttributionProductAddRequest.Product> productsList = request.getProductsList().stream().
                distinct().collect(Collectors.toList());

        List<String> distinctByPublishIdAsinList = request.getProductsList().stream().
                map(item -> publisherId.concat("_").concat(item.getAsin())).collect(Collectors.toList());
        if (distinctByPublishIdAsinList.size() != productsList.size()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("不能添加重复ASIN");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        try {
            distinctByPublishIdAsinList.forEach(o -> {
                Boolean lock = redisService.lock(RedisConstant.SEllFOX_ATTRIBUTION_PUBLISH_ASIN_LOCK.concat(String.valueOf(shopId))
                        .concat(":").concat(o), "1", 20, TimeUnit.SECONDS);
                if (!lock) {
                    builder.setCode(Int32Value.of(Result.ERROR));
                    builder.setMsg("操作过于频繁,请稍后再试");
                    responseObserver.onNext(builder.build());
                    responseObserver.onCompleted();
                    return;
                }
            });
            ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
            if (shopAuth == null) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("店铺不存在");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }
            AmazonAdAttributionPublisher publisher = publisherService.getPublisherById(publisherId);
            if (publisher == null) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("渠道不存在");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }

            AmazonAdProfile profile = amazonAdProfileDao.getProfile(shopAuth.getPuid(), shopId);
            if (profile == null || profile.getAttributionStatus() == null || profile.getAttributionStatus() == 0) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("未开通Amazon attribution功能");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }

            //校验asin是否添加过
            List<String> asins = request.getProductsList().stream().map(AttributionProductAddRequest.Product::getAsin).collect(Collectors.toList());
            List<AmazonAdAttribution> amazonAdAttributions = amazonAdAttributionService.listByAsin(shopAuth.getPuid(), shopId, profile.getAttributionAdvertiserId(), publisherId, asins);
            if (CollectionUtils.isNotEmpty(amazonAdAttributions)) {
                String asinStr = amazonAdAttributions.stream().map(AmazonAdAttribution::getAsin).collect(Collectors.joining(","));
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg(String.format("ASIN %s在该渠道下的推广链接已添加", asinStr));
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }

            AmazonAdAttributionTag tag = amazonAdAttributionTagService.getTag(shopAuth, profile.getProfileId(), publisher.getPublisherId(), profile.getAttributionAdvertiserId()
                    , publisher.getMacroEnable() == 1);
            if (tag == null) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("获取amazon广告标签接口异常，请稍后重试");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }
            AmazonAdAttributionCampaignSequence campaignSequence =
                    new AmazonAdAttributionCampaignSequence();
            campaignSequence.setPuid(shopAuth.getPuid());
            try {
                campaignSequenceDao.save(campaignSequence);
            } catch (Exception exception) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("系统异常，请稍后重试");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                exception.printStackTrace();
                return;
            }

            AmazonAdAttributionGroupIdSequence groupIdSequence =
                    new AmazonAdAttributionGroupIdSequence();
            groupIdSequence.setPuid(shopAuth.getPuid());
            try {
                groupIdSequenceDao.save(groupIdSequence);
            } catch (Exception exception) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("系统异常，请稍后重试");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }

            AmazonAdAttributionCreativeIdSequence creativeIdSequence =
                    new AmazonAdAttributionCreativeIdSequence();
            creativeIdSequence.setPuid(shopAuth.getPuid());
            try {
                creativeIdSequenceDao.save(creativeIdSequence);
            } catch (Exception exception) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("系统异常，请稍后重试");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }

            String advertiserId = profile.getAttributionAdvertiserId();
            String advertiserName = StringUtils.isNotBlank(profile.getAttributionAdvertiserName()) ?
                    profile.getAttributionAdvertiserName() : StringUtils.EMPTY;
            LocalDateTime now = LocalDateTime.now();

            List<AmazonAdAttribution> attributions = Lists.newArrayListWithExpectedSize(productsList.size());
            for (int i = 0; i < productsList.size(); i++) {
                AttributionProductAddRequest.Product product = productsList.get(i);
                String campaignId = String.valueOf(campaignSequence.getId()).concat("-").concat(String.valueOf(shopId))
                        .concat("-").concat(String.valueOf(i));
                String groupId = String.valueOf(groupIdSequence.getId()).concat("-").concat(String.valueOf(shopId))
                        .concat("-").concat(String.valueOf(i));
                String creativeId = String.valueOf(creativeIdSequence.getId()).concat("-")
                        .concat(String.valueOf(shopId)).concat("-").concat(String.valueOf(i));
                String baseUrl = "https://" + AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getDomain().concat("/dp/").concat(product.getAsin());
                String sponsoredUrl = generateSponsoredUrl(baseUrl, publisher.getMacroEnable(),
                        tag.getTag(), campaignId, groupId, creativeId);

                attributions.add(AmazonAdAttribution.builder().shopId(shopId).advertiserId(advertiserId)
                        .advertiserName(advertiserName).name(name)
                        .publisherId(publisherId).publisherName(publisher.getPublisherName()).asin(product.getAsin())
                        .msku(product.getMsku()).imgUrl(product.getImgUrl())
                        .createId(createId).createUserName(userName).remark(remark).createTime(now).updateTime(now)
                        .updateId(createId).type(AmazonAdAttribution.AttributionTypeEnum.product.name())
                        .macroEnable(publisher.getMacroEnable()).marketplaceId(shopAuth.getMarketplaceId())
                        .puid(shopAuth.getPuid()).sponsoredUrl(sponsoredUrl)
                        .campaignId(campaignId)
                        .adGroupId(groupId)
                        .productName(product.getProductName())
                        .creativeId(creativeId).build());
            }
            amazonAdAttributionService.batchInsert(shopAuth.getPuid(), attributions, profile);
        } finally {
            productsList.stream().map(item -> publisherId.concat("_").concat(item.getAsin())).forEach(o -> {
                redisService.releaseLock(RedisConstant.SEllFOX_ATTRIBUTION_PUBLISH_ASIN_LOCK.concat(String.valueOf(shopId))
                        .concat(":").concat(o));
            });
        }

        builder.setCode(Int32Value.of(Result.SUCCESS));
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private String generateSponsoredUrl(String baseUrl, Integer macroEnable, String tag, String campaignId, String groupId, String creativeId) {
        if (macroEnable == 0) {
            return baseUrl.concat(tag).replace("{insertCampaign}", campaignId).replace("{insertAdGroupId}", groupId)
                    .replace("{insertCreativeId}", creativeId);
        } else {
            return baseUrl.concat(tag).replace("{campaignid}", campaignId).replace("{adgroupid}", groupId)
                    .replace("{targetid}", creativeId);
        }
    }

    @Override
    public void topAdd(AttributionTopAddRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        int shopId = request.getShopId();
        String publisherId = request.getPublisherId();
        String name = request.getName();
        String remark = request.getTopUrl();
        String userName = request.getCreateUserName();
        Integer createId = request.getCreateId();
        String topUrl = request.getTopUrl();
        String lockKey = RedisConstant.SEllFOX_ATTRIBUTION_PUBLISH_TOP_URL_LOCK.concat(String.valueOf(shopId))
                .concat(":").concat(publisherId)
                .concat(":").concat(topUrl);

        try {
            Boolean lock = redisService.lock(lockKey, "1", 20, TimeUnit.SECONDS);
            if (!lock) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("操作过于频繁,请稍后再试");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }

            ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
            if (shopAuth == null) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("店铺不存在");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }
            AmazonAdAttributionPublisher publisher = publisherService.getPublisherById(publisherId);
            if (publisher == null) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("渠道不存在");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }

            AmazonAdProfile profile = amazonAdProfileDao.getProfile(shopAuth.getPuid(), shopId);
            if (profile == null || profile.getAttributionStatus() == null || profile.getAttributionStatus() == 0) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("未开通Amazon attribution功能");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }
            //检查是否重复添加过
            List<AmazonAdAttribution> existTopUrl = amazonAdAttributionService.getTopUrl(shopAuth.getPuid(), shopId, profile.getAttributionAdvertiserId(), publisherId, topUrl);
            if (CollectionUtils.isNotEmpty(existTopUrl)) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("同渠道不可重复添加店铺推广");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }

            AmazonAdAttributionTag tag = amazonAdAttributionTagService.getTag(shopAuth, profile.getProfileId(), publisher.getPublisherId(), profile.getAttributionAdvertiserId()
                    , publisher.getMacroEnable() == 1);
            if (tag == null) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("广告标签获取异常，请稍后重试");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }

            //生成campaignId groupId creativeId
            AmazonAdAttributionCampaignSequence campaignSequence =
                    new AmazonAdAttributionCampaignSequence();
            campaignSequence.setPuid(shopAuth.getPuid());
            try {
                campaignSequenceDao.save(campaignSequence);
            } catch (Exception exception) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("系统异常，请稍后重试");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }

            AmazonAdAttributionGroupIdSequence groupIdSequence =
                    new AmazonAdAttributionGroupIdSequence();
            groupIdSequence.setPuid(shopAuth.getPuid());
            try {
                groupIdSequenceDao.save(groupIdSequence);
            } catch (Exception exception) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("系统异常，请稍后重试");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }

            AmazonAdAttributionCreativeIdSequence creativeIdSequence =
                    new AmazonAdAttributionCreativeIdSequence();
            creativeIdSequence.setPuid(shopAuth.getPuid());
            try {
                creativeIdSequenceDao.save(creativeIdSequence);
            } catch (Exception exception) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("系统异常，请稍后重试");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }

            String advertiserId = profile.getAttributionAdvertiserId();
            String advertiserName = StringUtils.isNotBlank(profile.getAttributionAdvertiserName()) ? profile.getAttributionAdvertiserName()
                    : StringUtils.EMPTY;
            LocalDateTime now = LocalDateTime.now();
            String campaignId = String.valueOf(campaignSequence.getId()).concat("-").concat(String.valueOf(shopId))
                    .concat("-").concat("0");
            String groupId = String.valueOf(groupIdSequence.getId()).concat("-").concat(String.valueOf(shopId))
                    .concat("-").concat("0");
            String creativeId = String.valueOf(creativeIdSequence.getId()).concat("-").concat(String.valueOf(shopId))
                    .concat("-").concat("0");
            String sponsoredUrl = generateSponsoredUrl(topUrl, publisher.getMacroEnable(), tag.getTag(), campaignId, groupId, creativeId);
            amazonAdAttributionService.batchInsert(shopAuth.getPuid(), Collections.singletonList(AmazonAdAttribution.builder().shopId(shopId).advertiserId(advertiserId)
                    .advertiserName(advertiserName).name(name)
                    .publisherId(publisherId).publisherName(publisher.getPublisherName())
                    .createId(createId).createUserName(userName).remark(remark).createTime(now).updateTime(now)
                    .updateId(createId).type(AmazonAdAttribution.AttributionTypeEnum.top.name())
                    .macroEnable(publisher.getMacroEnable()).marketplaceId(shopAuth.getMarketplaceId())
                    .puid(shopAuth.getPuid()).sponsoredUrl(sponsoredUrl)
                    .campaignId(campaignId)
                    .adGroupId(groupId)
                    .creativeId(creativeId).topUrl(topUrl).build()), profile);

            builder.setCode(Int32Value.of(Result.SUCCESS));
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();

        } finally {
            redisService.releaseLock(lockKey);
        }
    }

    @Override
    public void list(AttributionListRequest request, StreamObserver<AttributionListResponse> responseObserver) {

        AttributionListResponse.Builder builder = AttributionListResponse.newBuilder();

        AmazonAdAttribution.SearchTypeEnum searchType = UCommonUtil.getByCode(request.getSearchType(), AmazonAdAttribution.SearchTypeEnum.class);
        AmazonAdAttribution.OrderByEnum orderBy = UCommonUtil.getByCode(request.getOrderBy(), AmazonAdAttribution.OrderByEnum.class);
        AmazonAdAttribution.OrderTypeEnum orderType = UCommonUtil.getByCode(request.getOrderType(), AmazonAdAttribution.OrderTypeEnum.class);

        if (orderBy == null || orderType == null) {
            //无指标数据排序
            listNonOrderBy(request, builder, searchType);
        } else {
            listOrderByReportMetric(request, builder, searchType, orderBy, orderType);
        }

        builder.setCode(Result.SUCCESS);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private void listOrderByReportMetric(
            AttributionListRequest request, AttributionListResponse.Builder builder,
            AmazonAdAttribution.SearchTypeEnum searchType, AmazonAdAttribution.OrderByEnum orderBy, AmazonAdAttribution.OrderTypeEnum orderType) {
        List<AmazonAdAttribution> attributionList = amazonAdAttributionService.list(request.getPuid(), request.getShopIdList(),
                searchType, request.getSearchValue(), request.getPublisherId());
        if (CollectionUtils.isNotEmpty(attributionList)) {
            List<String> creativeIds = attributionList.stream().map(AmazonAdAttribution::getCreativeId).collect(Collectors.toList());
            List<AmazonAdAttributionReport> reports = amazonAdAttributionReportService.countByCreativeIds(request.getPuid(),
                    creativeIds, request.getStartDate(), request.getEndDate());
            Map<String, AmazonAdAttributionReport> reportMap = null;
            if (CollectionUtils.isNotEmpty(reports)) {
                reportMap = reports.stream().collect(Collectors.toMap(AmazonAdAttributionReport::getCreativeId, Function.identity()));
            }

            List<Integer> shopIds = attributionList.stream().map(AmazonAdAttribution::getShopId).distinct().collect(Collectors.toList());
            List<ShopAuth> shops = shopAuthDao.getScAndVcByIds(shopIds);
            Map<Integer, ShopAuth> shopAuthMap = null;
            if (CollectionUtils.isNotEmpty(shops)) {
                shopAuthMap = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
            }

            AttributionListResponse.Data.Page.Builder pageBuilder = AttributionListResponse.Data.Page.newBuilder();
            List<AttributionListResponse.Data.Page.AttributionRow> rows = Lists.newArrayListWithExpectedSize(attributionList.size());

            //汇总数据
            long sumClick = 0L;
            long sumViews = 0L;
            long sumTotalViews = 0L;
            long sumPurchases = 0L;
            long sumTotalPurchases = 0L;
            long sumSold = 0L;
            long sumTotalSold = 0L;
            BigDecimal sumSales = BigDecimal.ZERO;
            BigDecimal sumTotalSales = BigDecimal.ZERO;
            long sumAddToCartNum = 0L;
            long sumTotalAddToCartNum = 0L;

            for (AmazonAdAttribution item : attributionList) {
                long view = MapUtils.isEmpty(reportMap) ? 0L :
                        (!reportMap.containsKey(item.getCreativeId()) ? 0L : reportMap.get(item.getCreativeId()).getViews());
                long totalView = MapUtils.isEmpty(reportMap) ? 0L :
                        (!reportMap.containsKey(item.getCreativeId()) ? 0L : reportMap.get(item.getCreativeId()).getTotalViews());
                long click = MapUtils.isEmpty(reportMap) ? 0L :
                        (!reportMap.containsKey(item.getCreativeId()) ? 0L : reportMap.get(item.getCreativeId()).getClick());
                long addCartNum = MapUtils.isEmpty(reportMap) ? 0L :
                        (!reportMap.containsKey(item.getCreativeId()) ? 0L : reportMap.get(item.getCreativeId()).getAddToCartNum());
                long totalAddCartNum = MapUtils.isEmpty(reportMap) ? 0L :
                        (!reportMap.containsKey(item.getCreativeId()) ? 0L : reportMap.get(item.getCreativeId()).getTotalAddToCartNum());
                long sold = MapUtils.isEmpty(reportMap) ? 0L :
                        (!reportMap.containsKey(item.getCreativeId()) ? 0L : reportMap.get(item.getCreativeId()).getSold());
                long totalSold = MapUtils.isEmpty(reportMap) ? 0L :
                        (!reportMap.containsKey(item.getCreativeId()) ? 0L : reportMap.get(item.getCreativeId()).getTotalSold());
                long purchases = MapUtils.isEmpty(reportMap) ? 0L :
                        (!reportMap.containsKey(item.getCreativeId()) ? 0L : reportMap.get(item.getCreativeId()).getPurchases());
                long totalPurchases = MapUtils.isEmpty(reportMap) ? 0L :
                        (!reportMap.containsKey(item.getCreativeId()) ? 0L : reportMap.get(item.getCreativeId()).getTotalPurchases());
                BigDecimal sales = MapUtils.isEmpty(reportMap) ? BigDecimal.ZERO :
                        (!reportMap.containsKey(item.getCreativeId()) ? BigDecimal.ZERO : reportMap.get(item.getCreativeId()).getSales());
                BigDecimal totalSales = MapUtils.isEmpty(reportMap) ? BigDecimal.ZERO :
                        (!reportMap.containsKey(item.getCreativeId()) ? BigDecimal.ZERO : reportMap.get(item.getCreativeId()).getTotalSales());
                //汇总，累加数据
                sumClick = sumClick + click;
                sumViews = sumViews + view;
                sumTotalViews = sumTotalViews + totalView;
                sumPurchases = sumPurchases + purchases;
                sumTotalPurchases = sumTotalPurchases + totalPurchases;
                sumSold = sumSold + sold;
                sumTotalSold = sumTotalSold + totalSold;
                sumSales = sumSales.add(sales);
                sumTotalSales = sumTotalSales.add(totalSales);
                sumAddToCartNum = sumAddToCartNum + addCartNum;
                sumTotalAddToCartNum = sumTotalAddToCartNum + totalAddCartNum;

                String shopName = "";
                String country = "";
                String marketplaceId = "";
                if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(item.getShopId())) {
                    shopName = shopAuthMap.get(item.getShopId()).getName();
                    marketplaceId = shopAuthMap.get(item.getShopId()).getMarketplaceId();
                    AmznEndpoint amznEndpoint = AmznEndpoint.getByMarketplaceId(marketplaceId);
                    if (amznEndpoint != null) {
                        country = amznEndpoint.getMarketplaceCN();
                    }

                }

                rows.add(AttributionListResponse.Data.Page.AttributionRow.newBuilder()
                        .setName(item.getName()).setImgUrl(Optional.ofNullable(item.getImgUrl()).orElse(StringUtils.EMPTY))
                        .setAsin(Optional.ofNullable(item.getAsin()).orElse(StringUtils.EMPTY))
                        .setMsku(Optional.ofNullable(item.getMsku()).orElse(StringUtils.EMPTY))
                        .setType(item.getType()).setPublisherId(item.getPublisherId()).setPublisherName(item.getPublisherName())
                        .setSponsoredUrl(item.getSponsoredUrl())
                        .setViews(view).setTotalView(totalView).setClick(click)
                        .setAddToCartNum(addCartNum).setTotalAddToCartNum(totalAddCartNum).setSold(sold).setTotalSold(totalSold)
                        .setPurchases(purchases).setTotalPurchases(totalPurchases).setSales(sales.doubleValue()).setTotalSales(totalSales.doubleValue())
                        .setCreateUserName(item.getCreateUserName()).setShopId(item.getShopId())
                        .setCreateTime(item.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATE_TIME)))
                        .setShopName(shopName).setRemark(Optional.ofNullable(item.getRemark()).orElse(StringUtils.EMPTY))
                        .setCountry(country).setMarketplaceId(marketplaceId).setId(item.getId())
                        .setCreativeId(item.getCreativeId())
                        .build());
            }
            //排序
            List<AttributionListResponse.Data.Page.AttributionRow> sortList = PageUtil.sort(rows, orderBy.getColumn(), orderType.getType());
            Page<AttributionListResponse.Data.Page.AttributionRow> page = new Page<>();
            page.setPageSize(request.getPageSize());
            page.setPageNo(request.getPageNo());

            Page<AttributionListResponse.Data.Page.AttributionRow> rowPage = PageUtil.getPage(page, sortList);
            pageBuilder.setPageNo(rowPage.getPageNo());
            pageBuilder.setPageSize(rowPage.getPageSize());
            pageBuilder.setTotalSize(rowPage.getTotalSize());
            pageBuilder.setTotalPage(rowPage.getTotalPage());
            pageBuilder.addAllRows(rowPage.getRows());

            //汇总的数据
            AttributionListResponse.Data.Summary.Builder sumBuilder = AttributionListResponse.Data.Summary.newBuilder();
            sumBuilder.setClick(sumClick);
            sumBuilder.setViews(sumViews);
            sumBuilder.setTotalView(sumTotalViews);
            sumBuilder.setAddToCartNum(sumAddToCartNum);
            sumBuilder.setTotalAddToCartNum(sumTotalAddToCartNum);
            sumBuilder.setSold(sumSold);
            sumBuilder.setTotalSold(sumTotalSold);
            sumBuilder.setSales(sumSales.doubleValue());
            sumBuilder.setTotalSales(sumTotalSales.doubleValue());
            sumBuilder.setPurchases(sumPurchases);
            sumBuilder.setTotalPurchases(sumTotalPurchases);

            AttributionListResponse.Data.Builder dataBuilder = AttributionListResponse.Data.newBuilder();
            dataBuilder.setPage(pageBuilder);
            dataBuilder.setSummary(sumBuilder.build());

            builder.setData(dataBuilder.build());
        }
    }

    private void listNonOrderBy(AttributionListRequest request, AttributionListResponse.Builder builder,
                                AmazonAdAttribution.SearchTypeEnum searchType) {
        Page<AmazonAdAttribution> pageList = amazonAdAttributionService.page(request.getPuid(), request.getShopIdList(), request.getPageNo(),
                request.getPageSize(), searchType, request.getSearchValue(), request.getPublisherId());
        List<AmazonAdAttribution> attributionList = pageList.getRows();
        AttributionListResponse.Data.Builder dataBuilder = AttributionListResponse.Data.newBuilder();
        List<AttributionListResponse.Data.Page.AttributionRow> rows = Lists.newArrayListWithExpectedSize(attributionList.size());

        if (CollectionUtils.isNotEmpty(attributionList)) {
            List<String> creativeIds = attributionList.stream().map(AmazonAdAttribution::getCreativeId).distinct().collect(Collectors.toList());
            List<AmazonAdAttributionReport> reports = amazonAdAttributionReportService.countByCreativeIds(
                    request.getPuid(), creativeIds, request.getStartDate(), request.getEndDate());
            Map<String, AmazonAdAttributionReport> reportMap = null;
            if (CollectionUtils.isNotEmpty(reports)) {
                reportMap = reports.stream().collect(Collectors.toMap(AmazonAdAttributionReport::getCreativeId, Function.identity()));
            }
            List<Integer> shopIds = attributionList.stream().map(AmazonAdAttribution::getShopId).distinct().collect(Collectors.toList());
            List<ShopAuth> shops = shopAuthDao.getScAndVcByIds(shopIds);
            Map<Integer, ShopAuth> shopAuthMap = null;
            if (CollectionUtils.isNotEmpty(shops)) {
                shopAuthMap = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
            }
            for (AmazonAdAttribution item : attributionList) {
                long view = MapUtils.isEmpty(reportMap) ? 0L :
                        (!reportMap.containsKey(item.getCreativeId()) ? 0L : reportMap.get(item.getCreativeId()).getViews());
                long totalView = MapUtils.isEmpty(reportMap) ? 0L :
                        (!reportMap.containsKey(item.getCreativeId()) ? 0L : reportMap.get(item.getCreativeId()).getTotalViews());
                long click = MapUtils.isEmpty(reportMap) ? 0L :
                        (!reportMap.containsKey(item.getCreativeId()) ? 0L : reportMap.get(item.getCreativeId()).getClick());
                long addCartNum = MapUtils.isEmpty(reportMap) ? 0L :
                        (!reportMap.containsKey(item.getCreativeId()) ? 0L : reportMap.get(item.getCreativeId()).getAddToCartNum());
                long totalAddCartNum = MapUtils.isEmpty(reportMap) ? 0L :
                        (!reportMap.containsKey(item.getCreativeId()) ? 0L : reportMap.get(item.getCreativeId()).getTotalAddToCartNum());
                long sold = MapUtils.isEmpty(reportMap) ? 0L :
                        (!reportMap.containsKey(item.getCreativeId()) ? 0L : reportMap.get(item.getCreativeId()).getSold());
                long totalSold = MapUtils.isEmpty(reportMap) ? 0L :
                        (!reportMap.containsKey(item.getCreativeId()) ? 0L : reportMap.get(item.getCreativeId()).getTotalSold());
                long purchases = MapUtils.isEmpty(reportMap) ? 0L :
                        (!reportMap.containsKey(item.getCreativeId()) ? 0L : reportMap.get(item.getCreativeId()).getPurchases());
                long totalPurchases = MapUtils.isEmpty(reportMap) ? 0L :
                        (!reportMap.containsKey(item.getCreativeId()) ? 0L : reportMap.get(item.getCreativeId()).getTotalPurchases());
                BigDecimal sales = MapUtils.isEmpty(reportMap) ? BigDecimal.ZERO :
                        (!reportMap.containsKey(item.getCreativeId()) ? BigDecimal.ZERO : reportMap.get(item.getCreativeId()).getSales());
                BigDecimal totalSales = MapUtils.isEmpty(reportMap) ? BigDecimal.ZERO :
                        (!reportMap.containsKey(item.getCreativeId()) ? BigDecimal.ZERO : reportMap.get(item.getCreativeId()).getTotalSales());

                String shopName = "";
                String country = "";
                String marketplaceId = "";
                if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(item.getShopId())) {
                    shopName = shopAuthMap.get(item.getShopId()).getName();
                    marketplaceId = shopAuthMap.get(item.getShopId()).getMarketplaceId();
                    AmznEndpoint amznEndpoint = AmznEndpoint.getByMarketplaceId(marketplaceId);
                    if (amznEndpoint != null) {
                        country = amznEndpoint.getMarketplaceCN();
                    }
                }
                rows.add(AttributionListResponse.Data.Page.AttributionRow.newBuilder()
                        .setName(item.getName()).setImgUrl(Optional.ofNullable(item.getImgUrl()).orElse(StringUtils.EMPTY))
                        .setAsin(Optional.ofNullable(item.getAsin()).orElse(StringUtils.EMPTY))
                        .setMsku(Optional.ofNullable(item.getMsku()).orElse(StringUtils.EMPTY))
                        .setType(item.getType()).setPublisherId(item.getPublisherId()).setPublisherName(item.getPublisherName())
                        .setSponsoredUrl(item.getSponsoredUrl())
                        .setViews(view).setTotalView(totalView).setClick(click)
                        .setAddToCartNum(addCartNum).setTotalAddToCartNum(totalAddCartNum).setSold(sold).setTotalSold(totalSold)
                        .setPurchases(purchases).setTotalPurchases(totalPurchases).setSales(sales.doubleValue()).setTotalSales(totalSales.doubleValue())
                        .setCreateUserName(item.getCreateUserName())
                        .setShopId(item.getShopId())
                        .setShopName(shopName)
                        .setCountry(country).setRemark(Optional.ofNullable(item.getRemark()).orElse(StringUtils.EMPTY))
                        .setCreativeId(item.getCreativeId()).setMarketplaceId(marketplaceId)
                        .setCreateTime(item.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATE_TIME)))
                        .build());
            }

            //查询所有creativeId
            List<String> allCreativeIds = amazonAdAttributionService.listAllCreativeIds(request.getPuid(), request.getShopIdList(), searchType,
                    request.getSearchValue(), request.getPublisherId());

            //查询汇总数据
            AmazonAdAttributionReport sumReport = null;
            if (CollectionUtils.isNotEmpty(allCreativeIds)) {
                sumReport = amazonAdAttributionReportService.summaryByCreativeIds(
                        request.getPuid(), allCreativeIds, request.getStartDate(), request.getEndDate());
            }

            AttributionListResponse.Data.Summary.Builder summaryBuilder = AttributionListResponse.Data.Summary.newBuilder();
            if (sumReport != null) {
                summaryBuilder.setClick(sumReport.getClick());
                summaryBuilder.setViews(sumReport.getViews());
                summaryBuilder.setTotalView(sumReport.getTotalViews());
                summaryBuilder.setPurchases(sumReport.getPurchases());
                summaryBuilder.setTotalPurchases(sumReport.getTotalPurchases());
                summaryBuilder.setAddToCartNum(sumReport.getAddToCartNum());
                summaryBuilder.setTotalAddToCartNum(sumReport.getTotalAddToCartNum());
                summaryBuilder.setSold(sumReport.getSold());
                summaryBuilder.setTotalSold(sumReport.getTotalSold());
                summaryBuilder.setSales(sumReport.getSales().doubleValue());
                summaryBuilder.setTotalSales(sumReport.getTotalSales().doubleValue());
                dataBuilder.setSummary(summaryBuilder.build());
            }

        }
        AttributionListResponse.Data.Page.Builder pageBuilder = AttributionListResponse.Data.Page.newBuilder();
        pageBuilder.setPageNo(pageList.getPageNo());
        pageBuilder.setPageSize(pageList.getPageSize());
        pageBuilder.setTotalPage(pageList.getTotalPage());
        pageBuilder.setTotalSize(pageList.getTotalSize());
        pageBuilder.addAllRows(rows);
        dataBuilder.setPage(pageBuilder.build());
        builder.setData(dataBuilder.build());

    }

    @Override
    public void detail(AttributionDetailRequest request, StreamObserver<AttributionDetailResponse> responseObserver) {
        AttributionDetailResponse.Builder builder = AttributionDetailResponse.newBuilder();
        AmazonAdAttribution adAttribution = amazonAdAttributionService.getByCreativeId(request.getPuid(), request.getCreativeId());
        if (adAttribution == null) {
            builder.setCode(Result.ERROR);
            builder.setMsg("数据不存在");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        List<AmazonAdAttributionReport> reports = amazonAdAttributionReportService.countByDate(request.getPuid(),
                request.getCreativeId(), request.getStartDate(), request.getEndDate());

        LocalDate start = LocalDate.parse(request.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        LocalDate end = LocalDate.parse(request.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        long days = ChronoUnit.DAYS.between(start, end) + 1;
        LocalDate lastStartTime = start.minusDays(days);
        LocalDate lastEndTime = end.minusDays(days);
        //上周期报告
        List<AmazonAdAttributionReport> lastReports = amazonAdAttributionReportService.countByDate(request.getPuid(),
                request.getCreativeId(), lastStartTime.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)),
                lastEndTime.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));

        AttributionDetailResponse.Detail.Builder detailBuilder = convertListToChart(reports, lastReports);
        detailBuilder.setAsin(Optional.ofNullable(adAttribution.getAsin()).orElse(StringUtils.EMPTY));
        detailBuilder.setImgUrl(Optional.ofNullable(adAttribution.getImgUrl()).orElse(StringUtils.EMPTY));
        detailBuilder.setMsku(Optional.ofNullable(adAttribution.getMsku()).orElse(StringUtils.EMPTY));
        detailBuilder.setProductName(Optional.ofNullable(adAttribution.getProductName()).orElse(StringUtils.EMPTY));
        detailBuilder.setShopId(adAttribution.getShopId());
        detailBuilder.setPuid(adAttribution.getPuid());
        builder.setData(detailBuilder.build());
        builder.setCode(Result.SUCCESS);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getSkusByShopId(GetSkusByShopIdRequest request, StreamObserver<GetSkusByShopIdResponse> responseObserver) {
        GetSkusByShopIdResponse.Builder builder = GetSkusByShopIdResponse.newBuilder();
        List<AmazonAdAttribution> list = amazonAdAttributionService.getSkusByShopId(request.getShopId());
        if (CollectionUtils.isNotEmpty(list)) {
            builder.addAllMsku(list.stream().filter(item -> StringUtils.isNotBlank(item.getMsku()))
                    .map(AmazonAdAttribution::getMsku).collect(Collectors.toList()));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAttributionAvailableShopsByPuid(GetAttributionAvailableShopsByPuidReqeust request,
                                                   StreamObserver<GetAttributionAvailableShopsByPuidResponse> responseObserver) {
        GetAttributionAvailableShopsByPuidResponse.Builder builder = GetAttributionAvailableShopsByPuidResponse.newBuilder();
        List<AmazonAdProfile> profiles = amazonAdAttributionService.getAttributionAvailableShopsByPuid(request.getPuid(),request.getShopIdList());
        List<Integer> shopIds = profiles.stream().map(AmazonAdProfile::getShopId).collect(Collectors.toList());
        List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(request.getPuid(), shopIds);
        Map<Integer, ShopAuth> shopAuthMap = shopAuths.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
        List<GetAttributionAvailableShopsByPuidResponse.Shop> shops = Lists.newArrayListWithExpectedSize(profiles.size());
        for (AmazonAdProfile profile : profiles) {
            String country = "";
            String shopName = "";
            if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(profile.getShopId())) {
                String marketplaceId = Optional.ofNullable(shopAuthMap.get(profile.getShopId()).getMarketplaceId()).orElse(StringUtils.EMPTY);
                AmznEndpoint amznEndpoint = AmznEndpoint.getByMarketplaceId(marketplaceId);
                if (amznEndpoint != null) {
                    country = amznEndpoint.getMarketplaceCN();
                }
                shopName = Optional.ofNullable(shopAuthMap.get(profile.getShopId()).getName()).orElse(StringUtils.EMPTY);
            }
            shops.add(GetAttributionAvailableShopsByPuidResponse.Shop.newBuilder().setShopId(profile.getShopId())
                    .setShopName(shopName).setCountry(country).build());
        }
        builder.addAllData(shops);
        builder.setCode(Result.SUCCESS);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private AttributionDetailResponse.Detail.Builder convertListToChart(List<AmazonAdAttributionReport> reports,
                                                                        List<AmazonAdAttributionReport> lastReports) {
        //汇总数据
        long click = 0L;
        long views = 0L;
        long totalViews = 0L;
        long sold = 0L;
        long totalSold = 0L;
        long addCartNum = 0L;
        long totalAddCartNum = 0L;
        long purchases = 0L;
        long totalPurchase = 0L;
        BigDecimal sales = BigDecimal.ZERO;
        BigDecimal totalSales = BigDecimal.ZERO;

        long lastClick = 0L;
        long lastViews = 0L;
        long lastTotalViews = 0L;
        long lastSold = 0L;
        long lastTotalSold = 0L;
        long lastAddCartNum = 0L;
        long lastTotalAddCartNum = 0L;
        long lastPurchases = 0L;
        long lastTotalPurchase = 0L;
        BigDecimal lastSales = BigDecimal.ZERO;
        BigDecimal lastTotalSales = BigDecimal.ZERO;

        List<AttributionDetailResponse.Detail.AttributionReportChart.Record> clickRecordList = Lists.newArrayListWithExpectedSize(reports.size());
        List<AttributionDetailResponse.Detail.AttributionReportChart.Record> viewsRecordList = Lists.newArrayListWithExpectedSize(reports.size());
        List<AttributionDetailResponse.Detail.AttributionReportChart.Record> totalViewsRecordList = Lists.newArrayListWithExpectedSize(reports.size());
        List<AttributionDetailResponse.Detail.AttributionReportChart.Record> salesRecordList = Lists.newArrayListWithExpectedSize(reports.size());
        List<AttributionDetailResponse.Detail.AttributionReportChart.Record> totalSalesRecordList = Lists.newArrayListWithExpectedSize(reports.size());
        List<AttributionDetailResponse.Detail.AttributionReportChart.Record> addCartNumRecordList = Lists.newArrayListWithExpectedSize(reports.size());
        List<AttributionDetailResponse.Detail.AttributionReportChart.Record> totalAddCartNumRecordList = Lists.newArrayListWithExpectedSize(reports.size());
        List<AttributionDetailResponse.Detail.AttributionReportChart.Record> soldRecordList = Lists.newArrayListWithExpectedSize(reports.size());
        List<AttributionDetailResponse.Detail.AttributionReportChart.Record> totalSoldRecordList = Lists.newArrayListWithExpectedSize(reports.size());
        List<AttributionDetailResponse.Detail.AttributionReportChart.Record> purchasesRecordList = Lists.newArrayListWithExpectedSize(reports.size());
        List<AttributionDetailResponse.Detail.AttributionReportChart.Record> totalPurchasesRecordList = Lists.newArrayListWithExpectedSize(reports.size());
        //请求接口获取asin销量排名
        List<AttributionDetailResponse.Detail.AttributionReportChart.Record> asinSalesRecordList = Lists.newArrayListWithExpectedSize(reports.size());

        for (AmazonAdAttributionReport report : lastReports) {
            lastClick = lastClick + Optional.ofNullable(report.getClick()).orElse(0L);
            lastViews = lastViews + Optional.ofNullable(report.getViews()).orElse(0L);
            lastTotalViews = lastTotalViews + Optional.ofNullable(report.getTotalViews()).orElse(0L);
            lastSold = lastSold + Optional.ofNullable(report.getSold()).orElse(0L);
            lastTotalSold = lastTotalSold + Optional.ofNullable(report.getTotalSold()).orElse(0L);
            lastAddCartNum = lastAddCartNum + Optional.ofNullable(report.getAddToCartNum()).orElse(0L);
            lastTotalAddCartNum = lastTotalAddCartNum + Optional.ofNullable(report.getTotalAddToCartNum()).orElse(0L);
            lastPurchases = lastPurchases + Optional.ofNullable(report.getPurchases()).orElse(0L);
            lastTotalPurchase = lastTotalPurchase + Optional.ofNullable(report.getTotalPurchases()).orElse(0L);
            lastSales = lastSales.add(Optional.ofNullable(report.getSales()).orElse(BigDecimal.ZERO));
            lastTotalSales = lastTotalSales.add(Optional.ofNullable(report.getTotalSales()).orElse(BigDecimal.ZERO));
        }

        for (AmazonAdAttributionReport report : reports) {
            click = click + Optional.ofNullable(report.getClick()).orElse(0L);
            views = views + Optional.ofNullable(report.getViews()).orElse(0L);
            totalViews = totalViews + Optional.ofNullable(report.getTotalViews()).orElse(0L);
            sold = sold + Optional.ofNullable(report.getSold()).orElse(0L);
            totalSold = totalSold + Optional.ofNullable(report.getTotalSold()).orElse(0L);
            addCartNum = addCartNum + Optional.ofNullable(report.getAddToCartNum()).orElse(0L);
            totalAddCartNum = totalAddCartNum + Optional.ofNullable(report.getTotalAddToCartNum()).orElse(0L);
            purchases = purchases + Optional.ofNullable(report.getPurchases()).orElse(0L);
            totalPurchase = totalPurchase + Optional.ofNullable(report.getTotalPurchases()).orElse(0L);
            sales = sales.add(Optional.ofNullable(report.getSales()).orElse(BigDecimal.ZERO));
            totalSales = totalSales.add(Optional.ofNullable(report.getTotalSales()).orElse(BigDecimal.ZERO));

            clickRecordList.add(AttributionDetailResponse.Detail.AttributionReportChart.Record.newBuilder()
                    .setDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(report.getCountDate(), DateUtil.PATTERN_YYYYMMDD),
                            DateUtil.PATTERN)).setValue(String.valueOf(report.getClick())).build());

            viewsRecordList.add(AttributionDetailResponse.Detail.AttributionReportChart.Record.newBuilder()
                    .setDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(report.getCountDate(), DateUtil.PATTERN_YYYYMMDD),
                            DateUtil.PATTERN)).setValue(String.valueOf(report.getViews())).build());

            totalViewsRecordList.add(AttributionDetailResponse.Detail.AttributionReportChart.Record.newBuilder()
                    .setDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(report.getCountDate(), DateUtil.PATTERN_YYYYMMDD),
                            DateUtil.PATTERN)).setValue(String.valueOf(report.getTotalViews())).build());

            salesRecordList.add(AttributionDetailResponse.Detail.AttributionReportChart.Record.newBuilder()
                    .setDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(report.getCountDate(), DateUtil.PATTERN_YYYYMMDD),
                            DateUtil.PATTERN)).setValue(String.valueOf(report.getSales())).build());

            totalSalesRecordList.add(AttributionDetailResponse.Detail.AttributionReportChart.Record.newBuilder()
                    .setDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(report.getCountDate(), DateUtil.PATTERN_YYYYMMDD),
                            DateUtil.PATTERN)).setValue(String.valueOf(report.getTotalSales())).build());

            addCartNumRecordList.add(AttributionDetailResponse.Detail.AttributionReportChart.Record.newBuilder()
                    .setDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(report.getCountDate(), DateUtil.PATTERN_YYYYMMDD),
                            DateUtil.PATTERN)).setValue(String.valueOf(report.getAddToCartNum())).build());

            totalAddCartNumRecordList.add(AttributionDetailResponse.Detail.AttributionReportChart.Record.newBuilder()
                    .setDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(report.getCountDate(), DateUtil.PATTERN_YYYYMMDD),
                            DateUtil.PATTERN)).setValue(String.valueOf(report.getTotalAddToCartNum())).build());

            soldRecordList.add(AttributionDetailResponse.Detail.AttributionReportChart.Record.newBuilder()
                    .setDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(report.getCountDate(), DateUtil.PATTERN_YYYYMMDD),
                            DateUtil.PATTERN)).setValue(String.valueOf(report.getSold())).build());

            totalSoldRecordList.add(AttributionDetailResponse.Detail.AttributionReportChart.Record.newBuilder()
                    .setDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(report.getCountDate(), DateUtil.PATTERN_YYYYMMDD),
                            DateUtil.PATTERN)).setValue(String.valueOf(report.getTotalSold())).build());

            purchasesRecordList.add(AttributionDetailResponse.Detail.AttributionReportChart.Record.newBuilder()
                    .setDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(report.getCountDate(), DateUtil.PATTERN_YYYYMMDD),
                            DateUtil.PATTERN)).setValue(String.valueOf(report.getPurchases())).build());

            totalPurchasesRecordList.add(AttributionDetailResponse.Detail.AttributionReportChart.Record.newBuilder()
                    .setDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(report.getCountDate(), DateUtil.PATTERN_YYYYMMDD),
                            DateUtil.PATTERN)).setValue(String.valueOf(report.getTotalPurchases())).build());

        }

        AttributionDetailResponse.Detail.Builder detailBuilder = AttributionDetailResponse.Detail.newBuilder();
        AttributionDetailResponse.Detail.AttributionReportChart.Builder clickBuilder = AttributionDetailResponse.Detail.AttributionReportChart.newBuilder();
        clickBuilder.setDescription("URL点击量");
        clickBuilder.setName("click");
        clickBuilder.addAllRecords(clickRecordList);
        detailBuilder.addDayChart(clickBuilder.build());

        AttributionDetailResponse.Detail.AttributionReportChart.Builder viewBuilder = AttributionDetailResponse.Detail.AttributionReportChart.newBuilder();
        viewBuilder.setDescription("URL曝光量");
        viewBuilder.setName("views");
        viewBuilder.addAllRecords(viewsRecordList);
        detailBuilder.addDayChart(viewBuilder.build());

        AttributionDetailResponse.Detail.AttributionReportChart.Builder totalViewsBuilder = AttributionDetailResponse.Detail.AttributionReportChart.newBuilder();
        totalViewsBuilder.setDescription("总URL曝光量");
        totalViewsBuilder.setName("totalViews");
        totalViewsBuilder.addAllRecords(totalViewsRecordList);
        detailBuilder.addDayChart(totalViewsBuilder.build());

        AttributionDetailResponse.Detail.AttributionReportChart.Builder soldBuilder = AttributionDetailResponse.Detail.AttributionReportChart.newBuilder();
        soldBuilder.setDescription("销量");
        soldBuilder.setName("sold");
        soldBuilder.addAllRecords(soldRecordList);
        detailBuilder.addDayChart(soldBuilder.build());

        AttributionDetailResponse.Detail.AttributionReportChart.Builder totalSoldBuilder = AttributionDetailResponse.Detail.AttributionReportChart.newBuilder();
        totalSoldBuilder.setDescription("总销量");
        totalSoldBuilder.setName("totalSold");
        totalSoldBuilder.addAllRecords(totalSoldRecordList);
        detailBuilder.addDayChart(totalSoldBuilder.build());

        AttributionDetailResponse.Detail.AttributionReportChart.Builder salesBuilder = AttributionDetailResponse.Detail.AttributionReportChart.newBuilder();
        salesBuilder.setDescription("销售额");
        salesBuilder.setName("sales");
        salesBuilder.addAllRecords(salesRecordList);
        detailBuilder.addDayChart(salesBuilder.build());

        AttributionDetailResponse.Detail.AttributionReportChart.Builder totalSalesBuilder = AttributionDetailResponse.Detail.AttributionReportChart.newBuilder();
        totalSalesBuilder.setDescription("总销售额");
        totalSalesBuilder.setName("totalSales");
        totalSalesBuilder.addAllRecords(totalSalesRecordList);
        detailBuilder.addDayChart(totalSalesBuilder.build());

        AttributionDetailResponse.Detail.AttributionReportChart.Builder addCartNumBuilder = AttributionDetailResponse.Detail.AttributionReportChart.newBuilder();
        addCartNumBuilder.setDescription("添加到购物车");
        addCartNumBuilder.setName("addCartNum");
        addCartNumBuilder.addAllRecords(addCartNumRecordList);
        detailBuilder.addDayChart(addCartNumBuilder.build());

        AttributionDetailResponse.Detail.AttributionReportChart.Builder totalAddCartNumBuilder = AttributionDetailResponse.Detail.AttributionReportChart.newBuilder();
        totalAddCartNumBuilder.setDescription("总添加到购物车");
        totalAddCartNumBuilder.setName("totalAddCartNum");
        totalAddCartNumBuilder.addAllRecords(totalAddCartNumRecordList);
        detailBuilder.addDayChart(totalAddCartNumBuilder.build());

        AttributionDetailResponse.Detail.AttributionReportChart.Builder purchasesBuilder = AttributionDetailResponse.Detail.AttributionReportChart.newBuilder();
        purchasesBuilder.setDescription("购买次数");
        purchasesBuilder.setName("purchases");
        purchasesBuilder.addAllRecords(purchasesRecordList);
        detailBuilder.addDayChart(purchasesBuilder.build());

        AttributionDetailResponse.Detail.AttributionReportChart.Builder totalPurchasesBuilder = AttributionDetailResponse.Detail.AttributionReportChart.newBuilder();
        totalPurchasesBuilder.setDescription("总购买次数");
        totalPurchasesBuilder.setName("totalPurchases");
        totalPurchasesBuilder.addAllRecords(totalPurchasesRecordList);
        detailBuilder.addDayChart(totalPurchasesBuilder.build());


        //对比数据
        List<AttributionDetailResponse.Detail.Comparison> comparisonList = new ArrayList<>();

        AttributionDetailResponse.Detail.Comparison.Builder clickComparisonBuilder = AttributionDetailResponse.Detail.Comparison.newBuilder();
        clickComparisonBuilder.setName("click");
        clickComparisonBuilder.setDescription("URL点击量");
        clickComparisonBuilder.setLastIssue(String.valueOf(lastClick));
        clickComparisonBuilder.setThisIssue(String.valueOf(click));
        clickComparisonBuilder.setGrowthRate(String.valueOf(lastClick == 0 ? 0 : BigDecimal.valueOf(click - lastClick).divide(BigDecimal.valueOf(lastClick), 4,
                RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).doubleValue()));
        comparisonList.add(clickComparisonBuilder.build());

        AttributionDetailResponse.Detail.Comparison.Builder viewsComparisonBuilder = AttributionDetailResponse.Detail.Comparison.newBuilder();
        viewsComparisonBuilder.setName("views");
        viewsComparisonBuilder.setDescription("URL曝光量");
        viewsComparisonBuilder.setLastIssue(String.valueOf(lastViews));
        viewsComparisonBuilder.setThisIssue(String.valueOf(views));
        viewsComparisonBuilder.setGrowthRate(String.valueOf(lastViews == 0 ? 0 : BigDecimal.valueOf(views - lastViews).divide(BigDecimal.valueOf(lastViews), 4,
                RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).doubleValue()));
        comparisonList.add(viewsComparisonBuilder.build());

        AttributionDetailResponse.Detail.Comparison.Builder totalViewsComparisonBuilder = AttributionDetailResponse.Detail.Comparison.newBuilder();
        totalViewsComparisonBuilder.setName("totalViews");
        totalViewsComparisonBuilder.setDescription("总URL曝光量");
        totalViewsComparisonBuilder.setLastIssue(String.valueOf(lastTotalViews));
        totalViewsComparisonBuilder.setThisIssue(String.valueOf(totalViews));
        totalViewsComparisonBuilder.setGrowthRate(String.valueOf(lastTotalViews == 0 ? 0 :
                BigDecimal.valueOf(totalViews - lastTotalViews).divide(BigDecimal.valueOf(lastTotalViews), 4,
                RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).doubleValue()));
        comparisonList.add(totalViewsComparisonBuilder.build());

        AttributionDetailResponse.Detail.Comparison.Builder purchasesComparisonBuilder = AttributionDetailResponse.Detail.Comparison.newBuilder();
        purchasesComparisonBuilder.setName("purchases");
        purchasesComparisonBuilder.setDescription("购买次数");
        purchasesComparisonBuilder.setLastIssue(String.valueOf(lastPurchases));
        purchasesComparisonBuilder.setThisIssue(String.valueOf(purchases));
        purchasesComparisonBuilder.setGrowthRate(String.valueOf(lastPurchases == 0 ? 0 : BigDecimal.valueOf(purchases - lastPurchases)
                .divide(BigDecimal.valueOf(lastPurchases), 4,
                RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).doubleValue()));
        comparisonList.add(purchasesComparisonBuilder.build());


        AttributionDetailResponse.Detail.Comparison.Builder totalPurchasesComparisonBuilder = AttributionDetailResponse.Detail.Comparison.newBuilder();
        totalPurchasesComparisonBuilder.setName("totalPurchases");
        totalPurchasesComparisonBuilder.setDescription("总购买次数");
        totalPurchasesComparisonBuilder.setLastIssue(String.valueOf(lastTotalPurchase));
        totalPurchasesComparisonBuilder.setThisIssue(String.valueOf(totalPurchase));
        totalPurchasesComparisonBuilder.setGrowthRate(String.valueOf(lastTotalPurchase == 0 ? 0 :
                BigDecimal.valueOf(totalPurchase - lastTotalPurchase).divide(BigDecimal.valueOf(lastTotalPurchase), 4,
                RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).doubleValue()));
        comparisonList.add(totalPurchasesComparisonBuilder.build());

        AttributionDetailResponse.Detail.Comparison.Builder addCartNumComparisonBuilder = AttributionDetailResponse.Detail.Comparison.newBuilder();
        addCartNumComparisonBuilder.setName("addToCartNum");
        addCartNumComparisonBuilder.setDescription("添加到购物车");
        addCartNumComparisonBuilder.setLastIssue(String.valueOf(lastAddCartNum));
        addCartNumComparisonBuilder.setThisIssue(String.valueOf(addCartNum));
        addCartNumComparisonBuilder.setGrowthRate(String.valueOf(lastAddCartNum == 0 ? 0 :
                BigDecimal.valueOf(addCartNum - lastAddCartNum).divide(BigDecimal.valueOf(lastAddCartNum), 4,
                RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).doubleValue()));
        comparisonList.add(addCartNumComparisonBuilder.build());

        AttributionDetailResponse.Detail.Comparison.Builder totalAddCartNumComparisonBuilder = AttributionDetailResponse.Detail.Comparison.newBuilder();
        totalAddCartNumComparisonBuilder.setName("totalAddToCartNum");
        totalAddCartNumComparisonBuilder.setDescription("总添加到购物车");
        totalAddCartNumComparisonBuilder.setLastIssue(String.valueOf(lastTotalAddCartNum));
        totalAddCartNumComparisonBuilder.setThisIssue(String.valueOf(totalAddCartNum));
        totalAddCartNumComparisonBuilder.setGrowthRate(String.valueOf(lastTotalAddCartNum == 0 ? 0 :
                BigDecimal.valueOf(totalAddCartNum - lastTotalAddCartNum).divide(BigDecimal.valueOf(lastTotalAddCartNum), 4,
                RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).doubleValue()));
        comparisonList.add(totalAddCartNumComparisonBuilder.build());

        AttributionDetailResponse.Detail.Comparison.Builder salesComparisonBuilder = AttributionDetailResponse.Detail.Comparison.newBuilder();
        salesComparisonBuilder.setName("sales");
        salesComparisonBuilder.setDescription("销售额");
        salesComparisonBuilder.setLastIssue(String.valueOf(lastSales));
        salesComparisonBuilder.setThisIssue(String.valueOf(sales));
        salesComparisonBuilder.setGrowthRate(String.valueOf(lastSales.compareTo(BigDecimal.ZERO) == 0 ?
                0 : (sales.subtract(lastSales)).divide(lastSales, 4,
                RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).doubleValue()));
        comparisonList.add(salesComparisonBuilder.build());

        AttributionDetailResponse.Detail.Comparison.Builder totalsalesComparisonBuilder = AttributionDetailResponse.Detail.Comparison.newBuilder();
        totalsalesComparisonBuilder.setName("totalSales");
        totalsalesComparisonBuilder.setDescription("总销售额");
        totalsalesComparisonBuilder.setLastIssue(String.valueOf(lastTotalSales));
        totalsalesComparisonBuilder.setThisIssue(String.valueOf(totalSales));
        totalsalesComparisonBuilder.setGrowthRate(String.valueOf(lastTotalSales.compareTo(BigDecimal.ZERO) == 0 ? 0 :
                (totalSales.subtract(lastTotalSales)).divide(lastTotalSales, 4,
                RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).doubleValue()));
        comparisonList.add(totalsalesComparisonBuilder.build());

        AttributionDetailResponse.Detail.Comparison.Builder soldComparisonBuilder = AttributionDetailResponse.Detail.Comparison.newBuilder();
        soldComparisonBuilder.setName("sold");
        soldComparisonBuilder.setDescription("销量");
        soldComparisonBuilder.setLastIssue(String.valueOf(lastSold));
        soldComparisonBuilder.setThisIssue(String.valueOf(sold));
        soldComparisonBuilder.setGrowthRate(String.valueOf(lastSold == 0 ? 0 : BigDecimal.valueOf(sold - lastSold).divide(BigDecimal.valueOf(lastSold), 4,
                RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).doubleValue()));
        comparisonList.add(soldComparisonBuilder.build());

        AttributionDetailResponse.Detail.Comparison.Builder totalSoldComparisonBuilder = AttributionDetailResponse.Detail.Comparison.newBuilder();
        totalSoldComparisonBuilder.setName("totalSold");
        totalSoldComparisonBuilder.setDescription("总销量");
        totalSoldComparisonBuilder.setLastIssue(String.valueOf(lastTotalSold));
        totalSoldComparisonBuilder.setThisIssue(String.valueOf(totalSold));
        totalSoldComparisonBuilder.setGrowthRate(String.valueOf(lastTotalSold == 0 ? 0 : BigDecimal.valueOf(totalSold - lastTotalSold)
                .divide(BigDecimal.valueOf(lastTotalSold), 4,
                RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).doubleValue()));
        comparisonList.add(totalSoldComparisonBuilder.build());
        detailBuilder.addAllComparison(comparisonList);
        return detailBuilder;
    }

}
