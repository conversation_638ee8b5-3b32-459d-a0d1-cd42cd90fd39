package com.meiyunji.sponsored.api.common;

import com.google.common.collect.Lists;
import com.google.protobuf.BoolValue;
import com.google.protobuf.Int32Value;
import com.google.protobuf.ProtocolStringList;
import com.meiyunji.sponsored.common.annotation.RateLimit;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.grpc.common.*;
import com.meiyunji.sponsored.rpc.adCommon.*;
import com.meiyunji.sponsored.rpc.vo.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.adCampaign.entity.AdSyncRecordEntity;
import com.meiyunji.sponsored.service.adCampaign.enums.AdSyncRecord;
import com.meiyunji.sponsored.service.adCampaign.service.IAdSyncRecord;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.constants.AdManagePageExportTaskTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.manager.CpcSpTargetingManager;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportAdvanceFiltersDto;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportFilterDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolio;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.*;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.service2.sd.ICpcSdAdProductService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcNeKeywordsService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcProductService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcSpCampaignService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcTargetingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.log.enums.OperationLogActionEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogFromEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogTargetEnum;
import com.meiyunji.sponsored.service.log.qo.OperationLogQo;
import com.meiyunji.sponsored.service.reportHour.service.IAmazonAdCampaignHourReportService;
import com.meiyunji.sponsored.service.reportHour.service.IAmazonAdPlacementHourReportService;
import com.meiyunji.sponsored.service.reportHour.utils.ReportChartUtil;
import com.meiyunji.sponsored.service.reportHour.vo.AdCampaignWeekDayVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdGroupWeekDayVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdPlacementWeekDayVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdProductWeekDayVo;
import com.meiyunji.sponsored.service.sync.service.ServersStatusSyncService;
import com.meiyunji.sponsored.service.util.PbUtil;
import com.meiyunji.sponsored.util.ProtoBufUtil;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

/**
 * @author: wade
 * @date: 2021/10/23 10:34
 * @describe:
 */
@GRpcService
@Slf4j
public class AdCommonRpcService extends RPCAdCommonServiceGrpc.RPCAdCommonServiceImplBase {

    @Autowired
    private ICpcCommonService cpcCommonService;

    @Autowired
    private ICpcCampaignService cpcCampaignService;

    @Autowired
    private IAmazonAdCampaignAllService amazonAdCampaignAllService;

    @Autowired
    private IWxCpcCommonService wxCpcCommonService;
    @Autowired
    private IAmazonAdCampaignHourReportService amazonAdCampaignHourReportService;


    @Autowired
    private IAdSyncRecord iAdSyncRecord;

    @Autowired
    private ICpcCampaignService iCpcCampaignService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IAmazonAdPlacementHourReportService amazonAdPlacementHourReportService;

    @Autowired
    private StringRedisService stringRedisService;

    @Autowired
    private CpcShopDataService cpcShopDataService;

    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAdTranslateService adTranslateService;

    @Resource
    private ServersStatusSyncService serversStatusSyncService;
    @Autowired
    private IAdManagePageExportTaskService adManagePageExportTaskService;

    @Autowired
    private ICpcProductService cpcProductService;
    @Autowired
    private ICpcSdAdProductService cpcSdAdProductService;
    @Autowired
    private IAmazonAdGroupDao spAdGroupDao;
    @Autowired
    private IAmazonSdAdGroupDao sdAdGroupDao;
    @Autowired
    private DynamicRefreshConfiguration configuration;
    @Autowired
    private ICpcSpCampaignService spCampaignService;
    @Autowired
    private ICpcNeKeywordsService groupNeKeywordsService;

    @Autowired
    private ICpcTargetingService cpcTargetingService;
    @Autowired
    private CpcPageIdsHandler cpcPageIdsHandler;
    @Resource
    private IScVcShopAuthDao scVcShopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private CpcSpTargetingManager cpcSpTargetingManager;

    /**
     * 查询广告活动和广告组信息
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllTypeCampAndGroup(AllTypeCampAndGroupRequest request, StreamObserver<AllTypeCampAndGroupResponse> responseObserver) {
        log.info("查询广告活动和广告组信息 request {}", request);
        AllTypeCampAndGroupResponse.Builder builder = AllTypeCampAndGroupResponse.newBuilder();
        //检查参数
        String err = checkParams(request);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(err);
        } else {
            List<AllTypeCampAndGroupResponse.CampAndGroupVo> list = cpcCommonService.getAllTypeCampAndGroup(request.getPuid().getValue(), request.getShopId().getValue(),
                    request.getType(), request.getCampaignType(), request.getGroupType(), request.getModular());
            builder.addAllData(list);
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }

        responseObserver.onNext(builder.build());

        responseObserver.onCompleted();

    }

    private String checkParams(AllTypeCampAndGroupRequest request) {
        if (StringUtils.isNotBlank(request.getType())) {
            if (!Arrays.asList(Constants.SP, Constants.SD, Constants.SB).contains(request.getType())) {
                return "请求参数错误";
            }
        } else if (StringUtils.isNotBlank(request.getCampaignType())) {
            if (StringUtils.isBlank(TargetingEnum.getTargetingValue(request.getCampaignType()))) {
                return "请求参数错误";
            }
        } else if (StringUtils.isNotBlank(request.getGroupType())) {
            GroupTypeEnum groupTypeEnum = UCommonUtil.getByCode(request.getGroupType(), GroupTypeEnum.class);
            if (groupTypeEnum == null) {
                return "请求参数错误";
            }
        } else if (StringUtils.isNotBlank(request.getModular())) {
            AdModularEnum adModularEnum = UCommonUtil.getByCode(request.getModular(), AdModularEnum.class);
            if (adModularEnum == null) {
                return "请求参数错误";
            }
        }
        return "";
    }

    private String checkParams(AllTypeCampaignAndGroupPageRequest request) {
        if (StringUtils.isNotBlank(request.getType())) {
            if (!Arrays.asList(Constants.SP, Constants.SD, Constants.SB).contains(request.getType())) {
                return "请求参数错误";
            }
        } else if (StringUtils.isNotBlank(request.getCampaignType())) {
            if (StringUtils.isBlank(TargetingEnum.getTargetingValue(request.getCampaignType()))) {
                return "请求参数错误";
            }
        } else if (StringUtils.isNotBlank(request.getGroupType())) {
            GroupTypeEnum groupTypeEnum = UCommonUtil.getByCode(request.getGroupType(), GroupTypeEnum.class);
            if (groupTypeEnum == null) {
                return "请求参数错误";
            }
        } else if (StringUtils.isNotBlank(request.getModular())) {
            AdModularEnum adModularEnum = UCommonUtil.getByCode(request.getModular(), AdModularEnum.class);
            if (adModularEnum == null) {
                return "请求参数错误";
            }
        }
        return "";
    }

    private String checkParams(AllTypeCampAndGroupPageRequest request) {
        if (StringUtils.isNotBlank(request.getType())) {
            if (!Arrays.asList(Constants.SP, Constants.SD, Constants.SB).contains(request.getType())) {
                return "请求参数错误";
            }
        } else if (StringUtils.isNotBlank(request.getCampaignType())) {
            if (StringUtils.isBlank(TargetingEnum.getTargetingValue(request.getCampaignType()))) {
                return "请求参数错误";
            }
        } else if (StringUtils.isNotBlank(request.getGroupType())) {
            GroupTypeEnum groupTypeEnum = UCommonUtil.getByCode(request.getGroupType(), GroupTypeEnum.class);
            if (groupTypeEnum == null) {
                return "请求参数错误";
            }
        } else if (StringUtils.isNotBlank(request.getModular())) {
            AdModularEnum adModularEnum = UCommonUtil.getByCode(request.getModular(), AdModularEnum.class);
            if (adModularEnum == null) {
                return "请求参数错误";
            }
        } else if (StringUtils.isNotBlank(request.getItemType())) {

            return "请求参数错误";

        }

        return "";
    }

    private String checkParamsNew(AllTypeCampAndGroupPageRequest request) {
        if (StringUtils.isNotBlank(request.getType()) && !Arrays.asList(Constants.SP, Constants.SD, Constants.SB).contains(request.getType())) {
            return "请求参数错误";
        }
        if (StringUtils.isNotBlank(request.getCampaignType()) && StringUtils.isBlank(TargetingEnum.getTargetingValue(request.getCampaignType()))) {
            return "请求参数错误";
        }
        if (StringUtils.isNotBlank(request.getGroupType())) {
            GroupTypeEnum groupTypeEnum = UCommonUtil.getByCode(request.getGroupType(), GroupTypeEnum.class);
            if (groupTypeEnum == null) {
                return "请求参数错误";
            }
        }
        if (StringUtils.isNotBlank(request.getModular())) {
            AdModularEnum adModularEnum = UCommonUtil.getByCode(request.getModular(), AdModularEnum.class);
            if (adModularEnum == null) {
                return "请求参数错误";
            }
        }
        if (StringUtils.isBlank(request.getItemType())) {
            return "请求参数错误";
        }
        if (StringUtils.isNotBlank(request.getOrderByField())) {
            AdOrderByFieldEnum adOrderByFieldEnum = UCommonUtil.getByCode(request.getOrderByField(), AdOrderByFieldEnum.class);
            if (adOrderByFieldEnum == null) {
                return "请求参数错误";
            }
        }
        if (StringUtils.isNotBlank(request.getOrderBy())) {
            if (!OrderTypeEnum.typeSet().contains(request.getOrderBy())) {
                return "请求参数错误";
            }
        }
        if (StringUtils.isNotBlank(request.getOrderByField()) && StringUtils.isBlank(request.getOrderBy()) ||
                StringUtils.isBlank(request.getOrderByField()) && StringUtils.isNotBlank(request.getOrderBy())) {
            return "请求参数错误";
        }
        if (StringUtils.isNotBlank(request.getExcludeState())) {
            if (StringUtils.isBlank(StateEnum.getStateValue(request.getExcludeState()))) {
                return "请求参数错误";
            }
        }

        return "";
    }


    /**
     * 查询所有已购买Asin具体信息
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllAsinData(AllAsinDataRequest request, StreamObserver<AllAsinDataResponse> responseObserver) {
        log.info("查询所有已购买Asin信息 request {}", request);
        long t = Instant.now().toEpochMilli();
        AllAsinDataResponse.Builder builder = AllAsinDataResponse.newBuilder();
        AdAsinPageParam param = new AdAsinPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid());
        param.setShopId(request.getShopId());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());

        if (request.hasAsin()) {
            param.setAsinList(Arrays.stream(request.getAsin().split(",")).map(String::valueOf).distinct().collect(Collectors.toList()));
        }
        if (request.hasPortfolioId()) {
            param.setPortfolioId(request.getPortfolioId());
        }
        if (request.hasCampaignId()) {
            param.setCampaignId(request.getCampaignId());
        }
        if (request.hasGroupId()) {
            param.setAdGroupId(request.getGroupId());
        }
        if (request.hasStartDate()) {
            param.setStartDate(request.getStartDate());
        }
        if (request.hasEndDate()) {
            param.setEndDate(request.getEndDate());
        }
        if (request.hasSearchField()) {
            param.setSearchField(request.getSearchField());
        }
        if (request.hasSearchValue()) {
            param.setSearchValue(request.getSearchValue());
        }
        if (request.hasOrderField()) {
            param.setOrderField(request.getOrderField());
        }
        if (request.hasOrderType()) {
            param.setOrderType(request.getOrderType());
        }
        if (request.hasParentAsin()) {
            param.setParentAsin(request.getParentAsin());
        }

        // 参数校验
        String err = checkAsinPageParam(param);
        if (StringUtils.isNotBlank(err)) {
            builder.setCode(Result.ERROR);
            builder.setMsg(err);
        } else {
            AllAsinDataResponse.AsinHomeVo asinHomeVo = cpcCommonService.getAllAsinData(request.getPuid(), param);
            builder.setData(asinHomeVo);
            builder.setCode(Result.SUCCESS);
            builder.setMsg("");
        }
        log.info("广告管理--已购买Asin接口调用花费时间 {} ,puid: {} ,shopId: {}", (Instant.now().toEpochMilli() - t), request.getPuid(), request.getShopId());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    @Override
    public void getAdGroupKeywordRank(AdGroupKeywordRankRequest request, StreamObserver<AdGroupKeywordRankResponse> responseObserver) {
        try {
            log.info("getAdGroupKeywordRank request {}", request);
            responseObserver.onNext(cpcCommonService.getAdGroupKeywordRank(request.getPuid().getValue(), AdGroupKeywordRankParam.get(request)));
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("getAdGroupKeywordRank = ", e);
            responseObserver.onError(e);
        }
    }


    @Override
    public void getAllAsinAggregateData(AllAsinDataRequest request, StreamObserver<AllAsinAggregateDataResponse> responseObserver) {
        log.info("查询所有已购买Asin汇总信息 request {}", request);
        long t = Instant.now().toEpochMilli();
        AllAsinAggregateDataResponse.Builder builder = AllAsinAggregateDataResponse.newBuilder();
        AdAsinPageParam param = new AdAsinPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid());
        param.setShopId(request.getShopId());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());

        if (request.hasAsin()) {
            param.setAsinList(Arrays.stream(request.getAsin().split(",")).map(String::valueOf).distinct().collect(Collectors.toList()));
        }
        if (request.hasPortfolioId()) {
            param.setPortfolioId(request.getPortfolioId());
        }
        if (request.hasCampaignId()) {
            param.setCampaignId(request.getCampaignId());
        }
        if (request.hasGroupId()) {
            param.setAdGroupId(request.getGroupId());
        }
        if (request.hasStartDate()) {
            param.setStartDate(request.getStartDate());
        }
        if (request.hasEndDate()) {
            param.setEndDate(request.getEndDate());
        }
        if (request.hasSearchField()) {
            param.setSearchField(request.getSearchField());
        }
        if (request.hasSearchValue()) {
            param.setSearchValue(request.getSearchValue());
        }

        // 参数校验
        String err = checkAsinPageParam(param);
        if (StringUtils.isNotBlank(err)) {
            builder.setCode(Result.ERROR);
            builder.setMsg(err);
        } else {
            AllAsinAggregateDataResponse.AsinHomeVo asinHomeVo = cpcCommonService.getAllAsinAggregateData(request.getPuid(), param);
            builder.setData(asinHomeVo);
            builder.setCode(Result.SUCCESS);
            builder.setMsg("");
        }
        log.info("广告管理--已购买Asin汇总接口调用花费时间 {} ,puid: {} ,shopId: {}", (Instant.now().toEpochMilli() - t), request.getPuid(), request.getShopId());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    /**
     * 查询所有已购买Asin关联信息
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllAssociationAsinData(AllAsinDataRequest request, StreamObserver<AllAssociationAsinDataResponse> responseObserver) {
        log.info("查询已购买Asin关联信息 request {}", request);
        long t = Instant.now().toEpochMilli();
        AllAssociationAsinDataResponse.Builder builder = AllAssociationAsinDataResponse.newBuilder();
        AdAsinPageParam param = new AdAsinPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid());
        param.setShopId(request.getShopId());

        if (request.hasAsin()) {
            param.setAsin(request.getAsin());
        }
        if (request.hasStartDate()) {
            param.setStartDate(request.getStartDate());
        }
        if (request.hasEndDate()) {
            param.setEndDate(request.getEndDate());
        }
        if (request.hasCampaignId()) {
            param.setCampaignId(request.getCampaignId());
        }
        if (request.hasGroupId()) {
            param.setAdGroupId(request.getGroupId());
        }

        // 参数校验
        String err = checkAsinPageParam(param);
        if (StringUtils.isNotBlank(err)) {
            builder.setCode(Result.ERROR);
            builder.setMsg(err);
        } else {
            AllAssociationAsinDataResponse.AsinHomeVo asinHomeVo = cpcCommonService.getAllAssociationAsinData(request.getPuid(), param);
            builder.setData(asinHomeVo);
            builder.setCode(Result.SUCCESS);
            builder.setMsg("");
        }
        log.info("广告管理--已购买Asin关联分析接口调用花费时间 {} ,puid: {} ,shopId: {}", (Instant.now().toEpochMilli() - t), request.getPuid(), request.getShopId());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 检验分页查询的参数
     *
     * @param param
     * @return
     */
    private String checkAsinPageParam(AdAsinPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }

        return null;
    }


    /**
     * 查询所有广告组合信息
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllPortfolioDataList(AllPortfolioDataListRequest request, StreamObserver<AllPortfolioDataListResponse> responseObserver) {
        log.info("查询所有广告组合信息 request {}", request);
        long t = Instant.now().toEpochMilli();
        AllPortfolioDataListResponse.Builder builder = AllPortfolioDataListResponse.newBuilder();
        PortfolioPageParam param = new PortfolioPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid());
        param.setShopId(request.getShopId());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setUid(request.getUid());
        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(param.getCompareStartDate().replace("-", ""));
        param.setCompareEndDate(param.getCompareEndDate().replace("-", ""));

        if (request.hasIsHidden()) {
            param.setIsHidden(request.getIsHidden());
        }
        if (request.hasStatus()) {
            param.setStatus(request.getStatus());
        }
        if (request.hasStartDate()) {
            param.setStartDate(request.getStartDate());
        }
        if (request.hasEndDate()) {
            param.setEndDate(request.getEndDate());
        }
        if (request.hasSearchType()) {
            param.setSearchType(request.getSearchType());
        }
        // 参数校验
        String err = checkPortfolioPageParam(param);
        if (StringUtils.isNotBlank(err)) {
            builder.setCode(Result.ERROR);
            builder.setMsg(err);
        } else {
            AllPortfolioDataListResponse.PortfolioHomeVo portfolioHomeVo = cpcCommonService.getAllPortfolioDataList(param.getPuid(), param);

            builder.setData(portfolioHomeVo);
            builder.setCode(Result.SUCCESS);
            builder.setMsg("");
        }
        log.info("广告管理--广告组合接口调用花费时间 {} ,puid: {} ,shopId: {}", (Instant.now().toEpochMilli() - t), request.getPuid(), request.getShopId());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAllPortfolioAggregateDataList(AllPortfolioDataListRequest request, StreamObserver<AllPortfolioAggregateDataListResponse> responseObserver) {
        log.info("查询所有广告组合信息 request {}", request);
        long t = Instant.now().toEpochMilli();
        AllPortfolioAggregateDataListResponse.Builder builder = AllPortfolioAggregateDataListResponse.newBuilder();
        PortfolioPageParam param = new PortfolioPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid());
        param.setShopId(request.getShopId());
        param.setUid(request.getUid());
        param.setSearchDataType(request.getSearchDataType().getValue());

        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(param.getCompareStartDate().replace("-", ""));
        param.setCompareEndDate(param.getCompareEndDate().replace("-", ""));

        // 参数校验
        String err = checkPortfolioPageParam(param);
        if (StringUtils.isNotBlank(err)) {
            builder.setCode(Result.ERROR);
            builder.setMsg(err);
        } else {
            AllPortfolioAggregateDataListResponse.PortfolioHomeVo portfolioHomeVo = cpcCommonService.getAllPortfolioAggregateDataList(param.getPuid(), param);
            builder.setData(portfolioHomeVo);
            builder.setCode(Result.SUCCESS);
            builder.setMsg("");
        }
        log.info("广告管理--广告组合数据汇总接口调用花费时间 {} ,puid: {},shopId: {}", (Instant.now().toEpochMilli() - t), request.getPuid(), request.getShopId());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 检验分页查询的参数
     *
     * @param param
     * @return
     */
    private String checkPortfolioPageParam(PortfolioPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }
        if (param.getIsHidden() == null) {
            param.setIsHidden(false);
        }
        if (param.getSearchType() == null) {
            param.setSearchType("exact");
        }
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }
        if (StringUtils.isNotBlank(param.getOrderField()) && !Constants.isADOrderField(param.getOrderField(), PortfolioPageVo.class)) {
            return "请求参数错误";
        }

        return null;
    }


    /**
     * 查询所有类型广告活动
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllCampaignData(AllCampaignDataRequest request, StreamObserver<AllCampaignDataResponse> responseObserver) {
        log.info("查询所有类型广告活动 {}", request);
        long t = Instant.now().toEpochMilli();
        AllCampaignDataResponse.Builder builder = AllCampaignDataResponse.newBuilder();
        CampaignPageParam campaignPageParam = new CampaignPageParam();
        BeanUtils.copyProperties(request, campaignPageParam);
        campaignPageParam.setPageNo(request.getPageNo().getValue());
        campaignPageParam.setPageSize(request.getPageSize().getValue());
        fillCampaignPageParam(request, campaignPageParam, true);
        if (request.hasCostType()) {
            campaignPageParam.setCostType(request.getCostType());
        }
        if (CollectionUtils.isNotEmpty(request.getAdTagIdsList())) {
            campaignPageParam.setAdTagIdList(request.getAdTagIdsList());
        }
        campaignPageParam.setOnlyCount(request.getOnlyCount());
        //做参数校验
        String err = checkCampaignPageParam(campaignPageParam);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            AllCampaignDataResponse.CampaignHomeVo homeVo = cpcCommonService.getAllCampaignData(campaignPageParam.getPuid(), campaignPageParam);
            serversStatusSyncService.sendCampaign(request.getPuid().getValue(), request.getShopId().getValue(), homeVo);
            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        log.info("广告管理--广告活动接口调用花费时间 {} ,puid: {},shopId: {}", (Instant.now().toEpochMilli() - t), request.getPuid(), request.getShopId());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告活动-汇总数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllCampaignAggregateData(AllCampaignDataRequest request, StreamObserver<AllCampaignAggregateDataResponse> responseObserver) {
        log.info("查询所有类型广告活动 {}", request);
        long t = Instant.now().toEpochMilli();
        AllCampaignAggregateDataResponse.Builder builder = AllCampaignAggregateDataResponse.newBuilder();
        CampaignPageParam campaignPageParam = new CampaignPageParam();
        BeanUtils.copyProperties(request, campaignPageParam);
        campaignPageParam.setSearchDataType(request.getSearchDataType().getValue());
        fillCampaignPageParam(request, campaignPageParam, false);

        //做参数校验
        String err = checkCampaignPageParam(campaignPageParam);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            AllCampaignAggregateDataResponse.CampaignHomeVo homeVo = cpcCommonService.getAllCampaignAggregateData(campaignPageParam.getPuid(), campaignPageParam);
            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        log.info("广告管理--广告活动数据汇总接口调用花费时间 {} ,puid: {},shopId: {}", (Instant.now().toEpochMilli() - t), request.getPuid(), request.getShopId());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private void fillCampaignPageParam(AllCampaignDataRequest request, CampaignPageParam campaignPageParam, boolean isList) {
        campaignPageParam.setPuid(request.getPuid().getValue());
        campaignPageParam.setShopId(request.getShopId().getValue());
        campaignPageParam.setUid(request.getUid().getValue());
        campaignPageParam.setIsAdmin(request.getIsAdmin());
        //环比数据传参
        campaignPageParam.setIsCompare(request.getIsCompare());
        campaignPageParam.setCompareStartDate(request.getCompareStartDate().replace("-", ""));
        campaignPageParam.setCompareEndDate(request.getCompareEndDate().replace("-", ""));

        //根据活动id查询具体的活动
        if (request.hasCampaignId()) {
            campaignPageParam.setCampaignId(request.getCampaignId());
        }

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            campaignPageParam.setServingStatus(request.getServingStatus());
        }

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            campaignPageParam.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            campaignPageParam.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            campaignPageParam.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            campaignPageParam.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            campaignPageParam.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            campaignPageParam.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            campaignPageParam.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            campaignPageParam.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            campaignPageParam.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            campaignPageParam.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            campaignPageParam.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            campaignPageParam.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            campaignPageParam.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            campaignPageParam.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            campaignPageParam.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            if (isList) {
                campaignPageParam.setAdCostPercentageMin(advancedFilter.hasAdCostPercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdCostPercentageMin()) : null);
                campaignPageParam.setAdCostPercentageMax(advancedFilter.hasAdCostPercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdCostPercentageMax()) : null);
                campaignPageParam.setAdSalePercentageMin(advancedFilter.hasAdSalePercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdSalePercentageMin()) : null);
                campaignPageParam.setAdSalePercentageMax(advancedFilter.hasAdSalePercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdSalePercentageMax()) : null);
                campaignPageParam.setAdOrderNumPercentageMin(advancedFilter.hasAdOrderNumPercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdOrderNumPercentageMin()) : null);
                campaignPageParam.setAdOrderNumPercentageMax(advancedFilter.hasAdOrderNumPercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdOrderNumPercentageMax()) : null);
                campaignPageParam.setOrderNumPercentageMin(advancedFilter.hasOrderNumPercentageMin() ? BigDecimal.valueOf(advancedFilter.getOrderNumPercentageMin()) : null);
                campaignPageParam.setOrderNumPercentageMax(advancedFilter.hasOrderNumPercentageMax() ? BigDecimal.valueOf(advancedFilter.getOrderNumPercentageMax()) : null);

                campaignPageParam.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
                campaignPageParam.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
                campaignPageParam.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
                campaignPageParam.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
                campaignPageParam.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
                campaignPageParam.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
                campaignPageParam.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
                campaignPageParam.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
                campaignPageParam.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
                campaignPageParam.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
                campaignPageParam.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
                campaignPageParam.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);
                campaignPageParam.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
                campaignPageParam.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);
                campaignPageParam.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()) : null);
                campaignPageParam.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()) : null);

            } else {
                campaignPageParam.setClickRateMin(advancedFilter.hasClickRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMin()), BigDecimal.valueOf(100)) : null);
                campaignPageParam.setClickRateMax(advancedFilter.hasClickRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMax()), BigDecimal.valueOf(100)) : null);
                campaignPageParam.setAcosMin(advancedFilter.hasAcosMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMin()), BigDecimal.valueOf(100)) : null);
                campaignPageParam.setAcosMax(advancedFilter.hasAcosMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMax()), BigDecimal.valueOf(100)) : null);
                campaignPageParam.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()), BigDecimal.valueOf(100)) : null);
                campaignPageParam.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()), BigDecimal.valueOf(100)) : null);
                campaignPageParam.setAcotsMin(advancedFilter.hasAcotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMin()), BigDecimal.valueOf(100)) : null);
                campaignPageParam.setAcotsMax(advancedFilter.hasAcotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMax()), BigDecimal.valueOf(100)) : null);
                campaignPageParam.setAsotsMin(advancedFilter.hasAsotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMin()), BigDecimal.valueOf(100)) : null);
                campaignPageParam.setAsotsMax(advancedFilter.hasAsotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMax()), BigDecimal.valueOf(100)) : null);
                campaignPageParam.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()), BigDecimal.valueOf(100), 6) : null);
                campaignPageParam.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()), BigDecimal.valueOf(100), 6) : null);
                campaignPageParam.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()), BigDecimal.valueOf(100), 6) : null);
                campaignPageParam.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()), BigDecimal.valueOf(100), 6) : null);
                campaignPageParam.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()), BigDecimal.valueOf(100), 6) : null);
                campaignPageParam.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()), BigDecimal.valueOf(100), 6) : null);
            }

            /********************高级搜索新增查询字段**********************/
            campaignPageParam.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            campaignPageParam.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            campaignPageParam.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            campaignPageParam.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            campaignPageParam.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            campaignPageParam.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            campaignPageParam.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            campaignPageParam.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            campaignPageParam.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            campaignPageParam.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);

            campaignPageParam.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            campaignPageParam.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);


            campaignPageParam.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            campaignPageParam.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);


            campaignPageParam.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            campaignPageParam.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);


            campaignPageParam.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            campaignPageParam.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);

            campaignPageParam.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            campaignPageParam.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);
            campaignPageParam.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            campaignPageParam.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);

            campaignPageParam.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            campaignPageParam.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);


            //广告销量
            campaignPageParam.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            campaignPageParam.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);


            campaignPageParam.setAddToCartMin(advancedFilter.hasAddToCartMin() ? advancedFilter.getAddToCartMin() : null);
            campaignPageParam.setAddToCartMax(advancedFilter.hasAddToCartMax() ? advancedFilter.getAddToCartMax() : null);
            campaignPageParam.setVideo5SecondViewsMin(advancedFilter.hasVideo5SecondViewsMin() ? advancedFilter.getVideo5SecondViewsMin() : null);
            campaignPageParam.setVideo5SecondViewsMax(advancedFilter.hasVideo5SecondViewsMax() ? advancedFilter.getVideo5SecondViewsMax() : null);
            campaignPageParam.setVideoCompleteViewsMin(advancedFilter.hasVideoCompleteViewsMin() ? advancedFilter.getVideoCompleteViewsMin() : null);
            campaignPageParam.setVideoCompleteViewsMax(advancedFilter.hasVideoCompleteViewsMax() ? advancedFilter.getVideoCompleteViewsMax() : null);
            campaignPageParam.setViewabilityRateMin(advancedFilter.hasViewabilityRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMin())) : null);
            campaignPageParam.setViewabilityRateMax(advancedFilter.hasViewabilityRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMax())) : null);
            campaignPageParam.setViewClickThroughRateMin(advancedFilter.hasViewClickThroughRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMin())) : null);
            campaignPageParam.setViewClickThroughRateMax(advancedFilter.hasViewClickThroughRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMax())) : null);
            campaignPageParam.setBrandedSearchesMin(advancedFilter.hasBrandedSearchesMin() ? advancedFilter.getBrandedSearchesMin() : null);
            campaignPageParam.setBrandedSearchesMax(advancedFilter.hasBrandedSearchesMax() ? advancedFilter.getBrandedSearchesMax() : null);
            campaignPageParam.setCumulativeReachMin(advancedFilter.hasCumulativeReachMin() ? advancedFilter.getCumulativeReachMin() : null);
            campaignPageParam.setCumulativeReachMax(advancedFilter.hasCumulativeReachMax() ? advancedFilter.getCumulativeReachMax() : null);
            campaignPageParam.setImpressionsFrequencyAverageMin(advancedFilter.hasImpressionsFrequencyAverageMin() ? new BigDecimal(String.valueOf(advancedFilter.getImpressionsFrequencyAverageMin())) : null);
            campaignPageParam.setImpressionsFrequencyAverageMax(advancedFilter.hasImpressionsFrequencyAverageMax() ? new BigDecimal(String.valueOf(advancedFilter.getImpressionsFrequencyAverageMax())) : null);
            campaignPageParam.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            campaignPageParam.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            // 搜索结果首页首位IS
            campaignPageParam.setTopImpressionShareMin(advancedFilter.hasTopImpressionShareMin() ? new BigDecimal(String.valueOf(advancedFilter.getTopImpressionShareMin())) : null);
            campaignPageParam.setTopImpressionShareMax(advancedFilter.hasTopImpressionShareMax() ? new BigDecimal(String.valueOf(advancedFilter.getTopImpressionShareMax())) : null);
        }

        if (request.hasFilterTargetType()) {
            campaignPageParam.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasDailyBudgetMin()) {
            campaignPageParam.setDailyBudgetMin(BigDecimal.valueOf(request.getDailyBudgetMin()));
        }
        if (request.hasDailyBudgetMax()) {
            campaignPageParam.setDailyBudgetMax(BigDecimal.valueOf(request.getDailyBudgetMax()));
        }
        if (request.hasFilterStartDate()) {
            campaignPageParam.setFilterStartDate(request.getFilterStartDate());
        }
        if (request.hasFilterEndDate()) {
            campaignPageParam.setFilterEndDate(request.getFilterEndDate());
        }
        if (request.hasProductType()) {
            campaignPageParam.setProductType(request.getProductType());
        }
        if (request.hasProductValue()) {
            campaignPageParam.setProductValue(request.getProductValue());
        }
        if (CollectionUtils.isNotEmpty(request.getAdTagIdsList())) {
            campaignPageParam.setAdTagIdList(request.getAdTagIdsList());
        }
        if (CollectionUtils.isNotEmpty(request.getAdStrategyTypeListList())) {
            campaignPageParam.setAdStrategyTypeList(request.getAdStrategyTypeListList());
        }
    }

    // 校验分页查询的参数
    private String checkCampaignPageParam(CampaignPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }
        //策略类型校验
        if (StringUtils.isNotBlank(param.getStrategyType())) {
            if (StringUtils.isBlank(StrategyEnum.getStrategyValue(param.getStrategyType()))) {
                return "请求参数错误";
            }
        }
        //预算状态校验
        if (StringUtils.isNotBlank(param.getBudgetState())) {
            CampaignPageParam.BudgetStateEnum budgetStateEnum = UCommonUtil.getByCode(param.getBudgetState(), CampaignPageParam.BudgetStateEnum.class);
            if (budgetStateEnum == null) {
                return "请求参数错误";
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField())) {
            CampaignPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(param.getSearchField(), CampaignPageParam.SearchFieldEnum.class);
            if (searchFieldEnum == null) {
                return "请求参数错误";
            }
        }

        if (StringUtils.isNotBlank(param.getOrderField())) {
            if (!Constants.isADOrderField(param.getOrderField(), CampaignPageVo.class)) {
                return "请求参数错误";
            }
        }
        return null;
    }

    /**
     * 查询所有类型广告位
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllPlacementData(AllPlacementDataRequest request, StreamObserver<AllPlacementDataResponse> responseObserver) {
        log.info("查询所有类型广告位 {}", request);
        AllPlacementDataResponse.Builder builder = AllPlacementDataResponse.newBuilder();

        PlacementPageParam param = new PlacementPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        param.setQuerySublist(request.getQuerySublist());
        if (CollectionUtils.isNotEmpty(request.getCampaignIdsList())) {
            param.setCampaignIds(request.getCampaignIdsList());
        }
        // 操作状态、服务状态
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }
        if (request.hasStatus()) {
            param.setState(request.getStatus());
        }
        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate().replace("-", ""));
        param.setCompareEndDate(request.getCompareEndDate().replace("-", ""));
        param.setOnlyCount(request.getOnlyCount());
        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);

            /********************高级搜索新增查询字段**********************/
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);

            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);


            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);


            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);


            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);

            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);

            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);

            param.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            param.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);

            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);

            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);

            param.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()) : null);

            //广告销量
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
        }

        //做参数校验
        String err = checkPlacementPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            AllPlacementDataResponse.AdPlacementHomeVo homeVo = cpcCommonService.getAllPlacementData(param.getPuid(), param);

            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getAllPlacementAggregateData(AllPlacementDataRequest request, StreamObserver<AllPlacementAggregateDataResponse> responseObserver) {
        log.info("查询所有类型广告位 {}", request);
        AllPlacementAggregateDataResponse.Builder builder = AllPlacementAggregateDataResponse.newBuilder();

        PlacementPageParam param = new PlacementPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        param.setSearchDataType(request.getSearchDataType().getValue());
        if (CollectionUtils.isNotEmpty(request.getCampaignIdsList())) {
            param.setCampaignIds(request.getCampaignIdsList());
        }
        if (request.hasStatus()) {
            param.setState(request.getStatus());
        }
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }
        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate().replace("-", ""));
        param.setCompareEndDate(request.getCompareEndDate().replace("-", ""));


        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMin()), BigDecimal.valueOf(100)) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMax()), BigDecimal.valueOf(100)) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMin()), BigDecimal.valueOf(100)) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMax()), BigDecimal.valueOf(100)) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()), BigDecimal.valueOf(100)) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()), BigDecimal.valueOf(100)) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMin()), BigDecimal.valueOf(100)) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMax()), BigDecimal.valueOf(100)) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMin()), BigDecimal.valueOf(100)) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMax()), BigDecimal.valueOf(100)) : null);

            /********************高级搜索新增查询字段**********************/
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);

            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);


            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);


            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);


            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);

            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);

            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()), BigDecimal.valueOf(100), 6) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()), BigDecimal.valueOf(100), 6) : null);

            param.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            param.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);

            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);

            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()), BigDecimal.valueOf(100), 6) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()), BigDecimal.valueOf(100), 6) : null);

            param.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()), BigDecimal.valueOf(100), 6) : null);
            param.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()), BigDecimal.valueOf(100), 6) : null);

            //广告销量
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
        }


        //做参数校验
        String err = checkPlacementPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            AllPlacementAggregateDataResponse.AdPlacementHomeVo homeVo = cpcCommonService.getAllPlacementAggregateData(param.getPuid(), param);

            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }

    }

    private String checkPlacementPageParam(PlacementPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }

        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }

        if (StringUtils.isNotBlank(param.getPredicate())) {
            if (StringUtils.isBlank(CpcPlaceNameEnum.getPredicateValue(param.getPredicate()))) {
                return "请求参数错误";
            }
        }
        if (StringUtils.isNotBlank(param.getStrategyType())) {
            if (StringUtils.isBlank(StrategyEnum.getStrategyValue(param.getStrategyType()))) {
                return "请求参数错误";
            }
        }
        if (StringUtils.isNotBlank(param.getOrderField()) && !Constants.isADperformanceOrderField(param.getOrderField())) {
            return "请求参数错误";
        }
        if (StringUtils.isNotBlank(param.getPredicate())) {
            AmazonAdvertisePredicateEnum predicateEnum = UCommonUtil.getByCode(param.getPredicate(), AmazonAdvertisePredicateEnum.class);
            if (predicateEnum == null) {
                return "请求参数错误";
            }
        }

        return null;
    }


    /**
     * 查询所有类型广告组信息
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllGroupData(AllGroupDataRequest request, StreamObserver<AllGroupDataResponse> responseObserver) {
        log.info("查询所有类型广告组信息 request {}", request);
        AllGroupDataResponse.Builder builder = AllGroupDataResponse.newBuilder();
        // grpc转param
        GroupPageParam param = grpcToParam(request);
        //做参数校验
        String err = checkGroupPageParam(param);
        if (StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(err);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            AllGroupDataResponse.GroupHomeVo homeVo = cpcCommonService.getAllGroupData(param.getPuid(), param);
            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            serversStatusSyncService.sendGroup(request.getPuid().getValue(), request.getShopId().getValue(), homeVo);
        }
    }

    private static GroupPageParam grpcToParam(AllGroupDataRequest request) {
        GroupPageParam param = new GroupPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        //搜索值
        //对批量查询做处理
        if (StringUtils.isNotBlank(request.getSearchValue()) && request.getSearchValue().contains("%±%")) {
            param.setSearchValueList(StringUtil.splitStr(request.getSearchValue().trim(), StringUtil.SPECIAL_COMMA));
            param.setSearchValue(null);
        }
        if (param.getSearchValue() != null && !SearchTypeEnum.EXACT.getValue().equalsIgnoreCase(param.getSearchType())) {
            String searchValue = param.getSearchValue();
            if (searchValue.contains("\\")) {
                searchValue = searchValue.replace("\\", "\\\\");
            }
            if (searchValue.contains("%")) {
                searchValue = searchValue.replace("%", "\\%");
            }
            // 将替换后的字符串重新设置回 param 对象
            param.setSearchValue(searchValue);
        }
        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate().replace("-", ""));
        param.setCompareEndDate(request.getCompareEndDate().replace("-", ""));
        param.setOnlyCount(request.getOnlyCount());
        if (!request.hasShopId() || param.getDxmCampaignId() != null) {
            param.setDxmCampaignId(request.getDxmCampaignId().getValue());
        }
        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }
        if (CollectionUtils.isNotEmpty(request.getAdTagIdsList())) {
            param.setAdTagIdList(request.getAdTagIdsList());
        }
        if (CollectionUtils.isNotEmpty(request.getAdStrategyTypeListList())) {
            param.setAdStrategyTypeList(request.getAdStrategyTypeListList());
        }
        // 仅显示正在投放
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }
        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }
        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setAdCostPercentageMin(advancedFilter.hasAdCostPercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdCostPercentageMin()) : null);
            param.setAdCostPercentageMax(advancedFilter.hasAdCostPercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdCostPercentageMax()) : null);
            param.setAdSalePercentageMin(advancedFilter.hasAdSalePercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdSalePercentageMin()) : null);
            param.setAdSalePercentageMax(advancedFilter.hasAdSalePercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdSalePercentageMax()) : null);
            param.setAdOrderNumPercentageMin(advancedFilter.hasAdOrderNumPercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdOrderNumPercentageMin()) : null);
            param.setAdOrderNumPercentageMax(advancedFilter.hasAdOrderNumPercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdOrderNumPercentageMax()) : null);
            param.setOrderNumPercentageMin(advancedFilter.hasOrderNumPercentageMin() ? BigDecimal.valueOf(advancedFilter.getOrderNumPercentageMin()) : null);
            param.setOrderNumPercentageMax(advancedFilter.hasOrderNumPercentageMax() ? BigDecimal.valueOf(advancedFilter.getOrderNumPercentageMax()) : null);
            //新增高级搜索筛选字段指标查询
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);

            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);


            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);


            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);


            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);

            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);

            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);

            param.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            param.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);

            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);

            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);

            param.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()) : null);

            //广告销量
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);

            param.setAddToCartMin(advancedFilter.hasAddToCartMin() ? advancedFilter.getAddToCartMin() : null);
            param.setAddToCartMax(advancedFilter.hasAddToCartMax() ? advancedFilter.getAddToCartMax() : null);
            param.setVideo5SecondViewsMin(advancedFilter.hasVideo5SecondViewsMin() ? advancedFilter.getVideo5SecondViewsMin() : null);
            param.setVideo5SecondViewsMax(advancedFilter.hasVideo5SecondViewsMax() ? advancedFilter.getVideo5SecondViewsMax() : null);
            param.setVideoCompleteViewsMin(advancedFilter.hasVideoCompleteViewsMin() ? advancedFilter.getVideoCompleteViewsMin() : null);
            param.setVideoCompleteViewsMax(advancedFilter.hasVideoCompleteViewsMax() ? advancedFilter.getVideoCompleteViewsMax() : null);
            param.setViewabilityRateMin(advancedFilter.hasViewabilityRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMin())) : null);
            param.setViewabilityRateMax(advancedFilter.hasViewabilityRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMax())) : null);
            param.setViewClickThroughRateMin(advancedFilter.hasViewClickThroughRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMin())) : null);
            param.setViewClickThroughRateMax(advancedFilter.hasViewClickThroughRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMax())) : null);
            param.setBrandedSearchesMin(advancedFilter.hasBrandedSearchesMin() ? advancedFilter.getBrandedSearchesMin() : null);
            param.setBrandedSearchesMax(advancedFilter.hasBrandedSearchesMax() ? advancedFilter.getBrandedSearchesMax() : null);
            param.setCumulativeReachMin(advancedFilter.hasCumulativeReachMin() ? advancedFilter.getCumulativeReachMin() : null);
            param.setCumulativeReachMax(advancedFilter.hasCumulativeReachMax() ? advancedFilter.getCumulativeReachMax() : null);
            param.setImpressionsFrequencyAverageMin(advancedFilter.hasImpressionsFrequencyAverageMin() ? new BigDecimal(String.valueOf(advancedFilter.getImpressionsFrequencyAverageMin())) : null);
            param.setImpressionsFrequencyAverageMax(advancedFilter.hasImpressionsFrequencyAverageMax() ? new BigDecimal(String.valueOf(advancedFilter.getImpressionsFrequencyAverageMax())) : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
        }

        if (request.hasFilterTargetType()) {
            param.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasBidMin()) {
            param.setBidMin(BigDecimal.valueOf(request.getBidMin()));
        }
        if (request.hasBidMax()) {
            param.setBidMax(BigDecimal.valueOf(request.getBidMax()));
        }
        //通过广告组Id查询具体广告组
        if (request.hasGroupId()) {
            param.setGroupId(request.getGroupId());
        }
        return param;
    }

    @Override
    public void getAllGroupAggregateData(AllGroupDataRequest request, StreamObserver<AllGroupAggregateDataResponse> responseObserver) {
        log.info("查询所有类型广告组信息 request {}", request);
        AllGroupAggregateDataResponse.Builder builder = AllGroupAggregateDataResponse.newBuilder();
        // grpc转param
        GroupPageParam param = grpcToParam(request);
        //做参数校验
        String err = checkGroupPageParam(param);
        if (StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(err);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            AllGroupAggregateDataResponse.GroupHomeVo homeVo = cpcCommonService.getAllGroupAggregateData(param.getPuid(), param);

            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }


    // 校验分页查询的参数
    private String checkGroupPageParam(GroupPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }

        if (StringUtils.isNotBlank(param.getSearchField())) {
            GroupPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(param.getSearchField(), GroupPageParam.SearchFieldEnum.class);
            if (searchFieldEnum == null) {
                return "请求参数错误";
            }
        }

        if (StringUtils.isNotBlank(param.getOrderField()) && !Constants.isADOrderField(param.getOrderField(), GroupPageVo.class)) {
            return "请求参数错误";
        }
        if (StringUtils.isNotBlank(param.getType())) {
            if (!Arrays.asList(Constants.SP, Constants.SD, Constants.SB).contains(param.getType())) {
                return "请求参数错误";
            }
        }
        if (StringUtils.isNotBlank(param.getOrderField()) && !Constants.isADOrderField(param.getOrderField(), GroupPageVo.class)) {
            return "请求参数错误";
        }
        param.setIsCompare(param.getIsCompare() != null && param.getIsCompare() && StringUtil.isNotEmpty(param.getCompareStartDate()) && StringUtil.isNotEmpty(param.getCompareEndDate()));


        return null;
    }

    /**
     * 查询所有类型广告产品
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllProductData(AllProductDataRequest request, StreamObserver<AllProductDataResponse> responseObserver) {
        log.info("查询所有类型广告产品 request {}", request);
        AllProductDataResponse.Builder builder = AllProductDataResponse.newBuilder();
        // 做参数校验
        AdProductPageParam param = buildAdProductPageParam(request);
        String err = checkAdProductPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        AllProductDataResponse.AdProductHomeVo HomeVo = cpcCommonService.getAllProductData(param.getPuid(), param);
        builder.setData(HomeVo);
        builder.setCode(Int32Value.of(Result.SUCCESS));
        builder.setMsg("success");
        serversStatusSyncService.sendProduct(request.getPuid().getValue(), request.getShopId().getValue(), HomeVo);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 构建AdProductPageParam
     *
     * @param request request
     * @return AdProductPageParam
     */
    private AdProductPageParam buildAdProductPageParam(AllProductDataRequest request) {
        AdProductPageParam param = new AdProductPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        param.setOnlyCount(request.getOnlyCount());
        //环比数据传参 兼容对比传值true 但是对比时间不传的场景
        param.setIsCompare(request.getIsCompare() &&  StringUtils.isNotBlank(request.getCompareStartDate()) && StringUtils.isNotBlank(request.getCompareEndDate()));
        param.setCompareStartDate(request.getCompareStartDate().replace("-", ""));
        param.setCompareEndDate(request.getCompareEndDate().replace("-", ""));

        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }

        if (CollectionUtils.isNotEmpty(request.getAdTagIdsList())) {
            param.setAdTagIdList(request.getAdTagIdsList());
        }

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);

            /********************高级搜索新增查询字段**********************/
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);

            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);

            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);

            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);

            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);

            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);

            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);

            param.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            param.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);

            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);

            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);

            param.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()) : null);

            //广告销量
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);

            param.setAddToCartMin(advancedFilter.hasAddToCartMin() ? advancedFilter.getAddToCartMin() : null);
            param.setAddToCartMax(advancedFilter.hasAddToCartMax() ? advancedFilter.getAddToCartMax() : null);
            param.setVideoCompleteViewsMin(advancedFilter.hasVideoCompleteViewsMin() ? advancedFilter.getVideoCompleteViewsMin() : null);
            param.setVideoCompleteViewsMax(advancedFilter.hasVideoCompleteViewsMax() ? advancedFilter.getVideoCompleteViewsMax() : null);
            param.setViewabilityRateMin(advancedFilter.hasViewabilityRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMin())) : null);
            param.setViewabilityRateMax(advancedFilter.hasViewabilityRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMax())) : null);
            param.setViewClickThroughRateMin(advancedFilter.hasViewClickThroughRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMin())) : null);
            param.setViewClickThroughRateMax(advancedFilter.hasViewClickThroughRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMax())) : null);
            param.setBrandedSearchesMin(advancedFilter.hasBrandedSearchesMin() ? advancedFilter.getBrandedSearchesMin() : null);
            param.setBrandedSearchesMax(advancedFilter.hasBrandedSearchesMax() ? advancedFilter.getBrandedSearchesMax() : null);
            param.setCumulativeReachMin(advancedFilter.hasCumulativeReachMin() ? advancedFilter.getCumulativeReachMin() : null);
            param.setCumulativeReachMax(advancedFilter.hasCumulativeReachMax() ? advancedFilter.getCumulativeReachMax() : null);
            param.setImpressionsFrequencyAverageMin(advancedFilter.hasImpressionsFrequencyAverageMin() ? new BigDecimal(String.valueOf(advancedFilter.getImpressionsFrequencyAverageMin())) : null);
            param.setImpressionsFrequencyAverageMax(advancedFilter.hasImpressionsFrequencyAverageMax() ? new BigDecimal(String.valueOf(advancedFilter.getImpressionsFrequencyAverageMax())) : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
        }
        return param;
    }

    @RateLimit(key = "#request.puid")
    @Override
    public void getAllProductAggregateData(AllProductDataRequest request, StreamObserver<AllProductAggregateDataResponse> responseObserver) {
        log.info("查询所有类型广告产品 request {}", request);
        AllProductAggregateDataResponse.Builder builder = AllProductAggregateDataResponse.newBuilder();
        // 做参数校验
        AdProductPageParam param = new AdProductPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        param.setSearchDataType(request.getSearchDataType().getValue());

        //环比数据传参
        param.setIsCompare(request.getIsCompare() &&  StringUtil.isNotEmpty(request.getCompareStartDate()) && StringUtil.isNotEmpty(request.getCompareEndDate()));
        param.setCompareStartDate(request.getCompareStartDate().replace("-", ""));
        param.setCompareEndDate(request.getCompareEndDate().replace("-", ""));

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }

        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }

        if (CollectionUtils.isNotEmpty(request.getAdTagIdsList())) {
            param.setAdTagIdList(request.getAdTagIdsList());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMin()), BigDecimal.valueOf(100)) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMax()), BigDecimal.valueOf(100)) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMin()), BigDecimal.valueOf(100)) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMax()), BigDecimal.valueOf(100)) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()), BigDecimal.valueOf(100)) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()), BigDecimal.valueOf(100)) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMin()), BigDecimal.valueOf(100)) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMax()), BigDecimal.valueOf(100)) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMin()), BigDecimal.valueOf(100)) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMax()), BigDecimal.valueOf(100)) : null);

            /********************高级搜索新增查询字段**********************/
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);

            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);


            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);


            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);


            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);

            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);

            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()), BigDecimal.valueOf(100), 6) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()), BigDecimal.valueOf(100), 6) : null);

            param.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            param.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);

            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);

            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()), BigDecimal.valueOf(100), 6) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()), BigDecimal.valueOf(100), 6) : null);

            param.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()), BigDecimal.valueOf(100), 6) : null);
            param.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()), BigDecimal.valueOf(100), 6) : null);

            //广告销量
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);

            param.setAddToCartMin(advancedFilter.hasAddToCartMin() ? advancedFilter.getAddToCartMin() : null);
            param.setAddToCartMax(advancedFilter.hasAddToCartMax() ? advancedFilter.getAddToCartMax() : null);
            param.setVideoCompleteViewsMin(advancedFilter.hasVideoCompleteViewsMin() ? advancedFilter.getVideoCompleteViewsMin() : null);
            param.setVideoCompleteViewsMax(advancedFilter.hasVideoCompleteViewsMax() ? advancedFilter.getVideoCompleteViewsMax() : null);
            param.setViewabilityRateMin(advancedFilter.hasViewabilityRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMin())) : null);
            param.setViewabilityRateMax(advancedFilter.hasViewabilityRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMax())) : null);
            param.setViewClickThroughRateMin(advancedFilter.hasViewClickThroughRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMin())) : null);
            param.setViewClickThroughRateMax(advancedFilter.hasViewClickThroughRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMax())) : null);
            param.setBrandedSearchesMin(advancedFilter.hasBrandedSearchesMin() ? advancedFilter.getBrandedSearchesMin() : null);
            param.setBrandedSearchesMax(advancedFilter.hasBrandedSearchesMax() ? advancedFilter.getBrandedSearchesMax() : null);
            param.setCumulativeReachMin(advancedFilter.hasCumulativeReachMin() ? advancedFilter.getCumulativeReachMin() : null);
            param.setCumulativeReachMax(advancedFilter.hasCumulativeReachMax() ? advancedFilter.getCumulativeReachMax() : null);
            param.setImpressionsFrequencyAverageMin(advancedFilter.hasImpressionsFrequencyAverageMin() ? new BigDecimal(String.valueOf(advancedFilter.getImpressionsFrequencyAverageMin())) : null);
            param.setImpressionsFrequencyAverageMax(advancedFilter.hasImpressionsFrequencyAverageMax() ? new BigDecimal(String.valueOf(advancedFilter.getImpressionsFrequencyAverageMax())) : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);

        }


        String err = checkAdProductPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");

        } else {
            AllProductAggregateDataResponse.AdProductHomeVo HomeVo = cpcCommonService.getAllProductAggregateData(param.getPuid(), param);

            builder.setData(HomeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setMsg("success");
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    private String checkAdProductPageParam(AdProductPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }
        if (StringUtils.isNotBlank(param.getOrderField()) && !Constants.isADperformanceOrderField(param.getOrderField())) {
            return "请求参数错误";
        }
        if (StringUtils.isNotBlank(param.getType())) {
            if (!Arrays.asList(Constants.SP, Constants.SD).contains(param.getType())) {
                return "请求参数错误";
            }
        }

        return null;
    }


    /**
     * 查询所有搜索词(投放产生)
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllQueryTargetData(AllQueryTargetDataRequest request, StreamObserver<AllQueryTargetDataResponse> responseObserver) {
        log.info("查询所有搜索词(投放产生) request {}", request);
        AllQueryTargetDataResponse.Builder builder = AllQueryTargetDataResponse.newBuilder();
        // grpc转dto
        CpcQueryWordDto dto = grpcToDto(request);
        //做参数校验
        if (!request.hasShopId() || checkQueryWordDto(dto)) {
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setMsg("请求参数错误");
        } else {
            Page page = new Page(request.getPageNo().getValue(), request.getPageSize().getValue());

            //兼容前端
            dto.setState(dto.getStatus());
            //兼容一下前端传参问题
            dto.setOrderValue(dto.getOrderType());
            AllQueryTargetDataResponse.AdQueryTargetingHomeVo homeVo = cpcCommonService.getAllQueryTargetData(dto.getPuid(), dto, page);

            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private static CpcQueryWordDto grpcToDto(AllQueryTargetDataRequest request) {
        CpcQueryWordDto dto = new CpcQueryWordDto();
        BeanUtils.copyProperties(request, dto);
        //环比数据传参
        dto.setIsCompare(request.getIsCompare());
        dto.setCompareStartDate(request.getCompareStartDate().replace("-", ""));
        dto.setCompareEndDate(request.getCompareEndDate().replace("-", ""));

        dto.setPuid(request.getPuid().getValue());
        dto.setShopId(request.getShopId().getValue());
        dto.setOnlyCount(request.getOnlyCount());
        AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
        if (advancedFilter != null) {  //高级筛选
            dto.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            dto.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            dto.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            dto.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            dto.setClickRateMin(advancedFilter.hasClickRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMin()), BigDecimal.valueOf(100)) : null);
            dto.setClickRateMax(advancedFilter.hasClickRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMax()), BigDecimal.valueOf(100)) : null);
            dto.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            dto.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            dto.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            dto.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            dto.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            dto.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            dto.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            dto.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            dto.setAcosMin(advancedFilter.hasAcosMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMin()), BigDecimal.valueOf(100)) : null);
            dto.setAcosMax(advancedFilter.hasAcosMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMax()), BigDecimal.valueOf(100)) : null);
            dto.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            dto.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            dto.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()), BigDecimal.valueOf(100)) : null);
            dto.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()), BigDecimal.valueOf(100)) : null);
            dto.setAcotsMin(advancedFilter.hasAcotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMin()), BigDecimal.valueOf(100)) : null);
            dto.setAcotsMax(advancedFilter.hasAcotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMax()), BigDecimal.valueOf(100)) : null);
            dto.setAsotsMin(advancedFilter.hasAsotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMin()), BigDecimal.valueOf(100)) : null);
            dto.setAsotsMax(advancedFilter.hasAsotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMax()), BigDecimal.valueOf(100)) : null);
            /********************高级搜索新增查询字段**********************/
            dto.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            dto.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            dto.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            dto.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            dto.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            dto.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            dto.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            dto.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            dto.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            dto.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);

            dto.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            dto.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);


            dto.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            dto.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);


            dto.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            dto.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);


            dto.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            dto.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);

            dto.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            dto.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);

            dto.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            dto.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);

            dto.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            dto.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);

            dto.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            dto.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);

            dto.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            dto.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);

            dto.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()) : null);
            dto.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()) : null);

            dto.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            dto.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);

            dto.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            dto.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);

        }

        if (request.hasFilterTargetType()) {
            dto.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasFilterAddProperty()) {
            dto.setFilterAddProperty(request.getFilterAddProperty());
        }
        if (CollectionUtils.isNotEmpty(request.getQueryWordTagTypeList())) {
            dto.setQueryWordTagTypeList(request.getQueryWordTagTypeList());
        }
        if (CollectionUtils.isNotEmpty(request.getAdStrategyTypeListList())) {
            dto.setAdStrategyTypeList(request.getAdStrategyTypeListList());
        }
        return dto;
    }


    @Override
    public void getAllQueryTargetAggregateData(AllQueryTargetDataRequest request, StreamObserver<AllQueryTargetAggregateDataResponse> responseObserver) {
        log.info("查询所有搜索词(投放产生) request {}", request);
        try {
            AllQueryTargetAggregateDataResponse.Builder builder = AllQueryTargetAggregateDataResponse.newBuilder();
            // grpc转dto
            CpcQueryWordDto dto = grpcToDto(request);
            //做参数校验
            if (!request.hasShopId() || checkQueryWordDto(dto)) {
                builder.setCode(Int32Value.of(Result.SUCCESS));
                builder.setMsg("请求参数错误");
            } else {

                //兼容前端
                dto.setState(dto.getStatus());
                //兼容一下前端传参问题
                dto.setOrderValue(dto.getOrderType());
                AllQueryTargetAggregateDataResponse.AdQueryTargetingHomeVo homeVo = cpcCommonService.getAllQueryTargetAggregateData(dto.getPuid(), dto);

                builder.setData(homeVo);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }


    private boolean checkQueryWordDto(CpcQueryWordDto dto) {
        if (!CheckParamUtil.checkRequired(dto, false, "shopId,marketplaceId,start,end")) {
            return true;
        }
        dto.setStart(DateUtil.dateToStrWithFormat(DateUtil.strToDate(dto.getStart(), "yyyy-MM-dd"), "yyyyMMdd"));
        dto.setEnd(DateUtil.dateToStrWithFormat(DateUtil.strToDate(dto.getEnd(), "yyyy-MM-dd"), "yyyyMMdd"));

        return false;
    }

    /**
     * 查询所有搜索词(关键词产生)
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllQueryWordData(AllQueryWordDataRequest request, StreamObserver<AllQueryWordDataResponse> responseObserver) {
        try {
            log.info("查询所有搜索词(关键词产生) request {}", request);
            AllQueryWordDataResponse.Builder builder = AllQueryWordDataResponse.newBuilder();
            // grpc转dto
            CpcQueryWordDto dto = grpcToDto(request);
            //做参数校验
            if (!request.hasShopId() || checkQueryWordDto(dto)) {
                builder.setCode(Int32Value.of(Result.SUCCESS));
                builder.setMsg("请求参数错误");
            } else {
                Page page = new Page(request.getPageNo().getValue(), request.getPageSize().getValue());
                dto.setUseAdvanced(true);
                //兼容前端
                dto.setState(dto.getStatus());
                //兼容一下前端传参问题
                dto.setOrderValue(dto.getOrderType());
                AllQueryWordDataResponse.AdQueryWordsHomeVo homeVo = cpcCommonService.getAllQueryWordData(dto.getPuid(), dto, page);
                builder.setData(homeVo);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    private static CpcQueryWordDto grpcToDto(AllQueryWordDataRequest request) {
        CpcQueryWordDto dto = new CpcQueryWordDto();
        BeanUtils.copyProperties(request, dto);
        dto.setPuid(request.getPuid().getValue());
        dto.setShopId(request.getShopId().getValue());
        dto.setType(request.getType());

        //环比数据传参
        dto.setIsCompare(request.getIsCompare());
        dto.setCompareStartDate(request.getCompareStartDate().replace("-", ""));
        dto.setCompareEndDate(request.getCompareEndDate().replace("-", ""));
        dto.setOnlyCount(request.getOnlyCount());
        AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
        if (advancedFilter != null) {  //高级筛选
            dto.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            dto.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            dto.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            dto.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            dto.setClickRateMin(advancedFilter.hasClickRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMin()), BigDecimal.valueOf(100)) : null);
            dto.setClickRateMax(advancedFilter.hasClickRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMax()), BigDecimal.valueOf(100)) : null);
            dto.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            dto.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            dto.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            dto.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            dto.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            dto.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            dto.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            dto.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            dto.setAcosMin(advancedFilter.hasAcosMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMin()), BigDecimal.valueOf(100)) : null);
            dto.setAcosMax(advancedFilter.hasAcosMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMax()), BigDecimal.valueOf(100)) : null);
            dto.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            dto.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            dto.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()), BigDecimal.valueOf(100)) : null);
            dto.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()), BigDecimal.valueOf(100)) : null);
            dto.setAcotsMin(advancedFilter.hasAcotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMin()), BigDecimal.valueOf(100)) : null);
            dto.setAcotsMax(advancedFilter.hasAcotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMax()), BigDecimal.valueOf(100)) : null);
            dto.setAsotsMin(advancedFilter.hasAsotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMin()), BigDecimal.valueOf(100)) : null);
            dto.setAsotsMax(advancedFilter.hasAsotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMax()), BigDecimal.valueOf(100)) : null);

            /********************高级搜索新增查询字段**********************/
            dto.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            dto.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            dto.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            dto.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            dto.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            dto.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            dto.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            dto.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            dto.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            dto.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);

            dto.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            dto.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);


            dto.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            dto.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);


            dto.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            dto.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);


            dto.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            dto.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);

            dto.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            dto.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);

            dto.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()), BigDecimal.valueOf(100), 6) : null);
            dto.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()), BigDecimal.valueOf(100), 6) : null);

            dto.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            dto.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);

            dto.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            dto.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);

            dto.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()), BigDecimal.valueOf(100), 6) : null);
            dto.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()), BigDecimal.valueOf(100), 6) : null);

            dto.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()), BigDecimal.valueOf(100), 6) : null);
            dto.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()), BigDecimal.valueOf(100), 6) : null);

            //广告销量
            dto.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            dto.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);

            //品牌买家订单转化率
            dto.setBrandNewBuyerOrderConversionRateMin(advancedFilter.hasBrandNewBuyerOrderConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getBrandNewBuyerOrderConversionRateMin()) : null);
            dto.setBrandNewBuyerOrderConversionRateMax(advancedFilter.hasBrandNewBuyerOrderConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getBrandNewBuyerOrderConversionRateMax()) : null);

            dto.setVideo5SecondViewsMin(advancedFilter.hasVideo5SecondViewsMin() ? advancedFilter.getVideo5SecondViewsMin() : null);
            dto.setVideo5SecondViewsMax(advancedFilter.hasVideo5SecondViewsMax() ? advancedFilter.getVideo5SecondViewsMax() : null);
            dto.setVideoCompleteViewsMin(advancedFilter.hasVideoCompleteViewsMin() ? advancedFilter.getVideoCompleteViewsMin() : null);
            dto.setVideoCompleteViewsMax(advancedFilter.hasVideoCompleteViewsMax() ? advancedFilter.getVideoCompleteViewsMax() : null);
            dto.setViewabilityRateMin(advancedFilter.hasViewabilityRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMin())) : null);
            dto.setViewabilityRateMax(advancedFilter.hasViewabilityRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMax())) : null);
            dto.setViewClickThroughRateMin(advancedFilter.hasViewClickThroughRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMin())) : null);
            dto.setViewClickThroughRateMax(advancedFilter.hasViewClickThroughRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMax())) : null);
            dto.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            dto.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            dto.setSearchFrequencyRankMin(advancedFilter.hasSearchFrequencyRankMin() ? advancedFilter.getSearchFrequencyRankMin() : null);
            dto.setSearchFrequencyRankMax(advancedFilter.hasSearchFrequencyRankMax() ? advancedFilter.getSearchFrequencyRankMax() : null);
            dto.setWeekRatioMin(advancedFilter.hasWeekRatioMin() ? new BigDecimal(String.valueOf(advancedFilter.getWeekRatioMin())) : null);
            dto.setWeekRatioMax(advancedFilter.hasWeekRatioMax() ? new BigDecimal(String.valueOf(advancedFilter.getWeekRatioMax())) : null);

        }

        if (request.hasFilterTargetType()) {
            dto.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasFilterMatchType()) {
            dto.setFilterMatchType(request.getFilterMatchType());
        }
        if (request.hasFilterAddProperty()) {
            dto.setFilterAddProperty(request.getFilterAddProperty());
        }

        if (CollectionUtils.isNotEmpty(request.getQueryWordTagTypeList())) {
            dto.setQueryWordTagTypeList(request.getQueryWordTagTypeList());
        }

        if (CollectionUtils.isNotEmpty(request.getAdStrategyTypeListList())) {
            dto.setAdStrategyTypeList(request.getAdStrategyTypeListList());
        }
        return dto;
    }

    @Override
    public void getAllQueryWordAggregateData(AllQueryWordDataRequest request, StreamObserver<AllQueryWordAggregateDataResponse> responseObserver) {
        log.info("查询所有搜索词(关键词产生) request {}", request);
        AllQueryWordAggregateDataResponse.Builder builder = AllQueryWordAggregateDataResponse.newBuilder();
        CpcQueryWordDto dto = grpcToDto(request);
        //做参数校验
        if (!request.hasShopId() || checkQueryWordDto(dto)) {
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setMsg("请求参数错误");
        } else {
            dto.setUseAdvanced(true);
            //兼容前端
            dto.setState(dto.getStatus());
            //兼容一下前端传参问题
            dto.setOrderValue(dto.getOrderType());
            AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo homeVo = cpcCommonService.getAllQueryWordAggregateData(dto.getPuid(), dto);
            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 查询所有类型否定关键词(活动)
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getCampaignNeKewordData(CampaignNeKewordDataRequest request, StreamObserver<CampaignNeKewordDataResponse> responseObserver) {
        log.info("查询SP活动层级否定关键词，request:{}", request);

        //1,校验基础参数
        checkRequestForBasicParam(request);

        //2,获取数据
        Page<CampaignNeKeywordsPageRpcVo> voPage;
        if (configuration.getContainsReport()) {//包含报告数据
            //校验分析数据筛选参数
            checkRequestForAnalysis(request);
            CampaignNeKeywordsPageParam param = reqVo2DtoV2(request);
            voPage = spCampaignService.neKeywordsPageListV2(param, false);
        } else {//不包含报告数据
            CampaignNeKeywordsPageParam param = reqVo2Dto(request);
            voPage = cpcCommonService.neKeywordsPageList(param.getPuid(), param);
        }

        //3,构建返回参数返回给前端
        responseObserver.onNext(buildResp(voPage));
        responseObserver.onCompleted();
    }

    /**
     * 校验基础请求参数
     * @param request
     */
    private void checkRequestForBasicParam(CampaignNeKewordDataRequest request) {
        if (!request.hasPuid() ) {
            throw new SponsoredBizException("缺少puid");
        }
        if (!request.hasShopId()) {
            throw new SponsoredBizException("缺少shopId");
        }
    }

    /**
     * 构建resp
     * @param voPage
     * @return
     */
    private CampaignNeKewordDataResponse buildResp(Page<CampaignNeKeywordsPageRpcVo> voPage) {
        CampaignNeKewordDataResponse.Page.Builder pageBuilder = CampaignNeKewordDataResponse.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        if (voPage.getRows() != null) {
            pageBuilder.addAllRows(voPage.getRows());
        }

        CampaignNeKewordDataResponse.Builder respBuilder = CampaignNeKewordDataResponse.newBuilder();
        respBuilder.setData(pageBuilder);
        respBuilder.setCode(Int32Value.of(Result.SUCCESS));
        return respBuilder.build();
    }

    /**
     * 校验分析数据筛选参数
     * @param request request
     */
    private void checkRequestForAnalysis(CampaignNeKewordDataRequest request) {
        if (!request.hasFilter()) {
            throw new SponsoredBizException("缺少报告数据过滤条件");
        }

        NeTargetReportFilter filter = request.getFilter();

        if (filter.hasOrderField() && !NeTargetReportParamsMappingEnum.FRONT_PARAM_LIST.contains(filter.getOrderField())) {
            throw new SponsoredBizException("请求参数错误: orderField");
        }
        if (filter.hasOrderType() && !"desc".equalsIgnoreCase(filter.getOrderType()) && !"asc".equalsIgnoreCase(filter.getOrderType())) {
            throw new SponsoredBizException("请求参数错误: orderType");
        }
        if (filter.hasDataFrom() && filter.getDataFrom() != 1 && filter.getDataFrom() != 2) {
            throw new SponsoredBizException("请求参数错误: dataFrom");
        }
        if (StringUtils.isAnyBlank(filter.getReportStartDate(), filter.getReportEndDate())) {
            if (StringUtils.isNotBlank(filter.getReportDateType()) && !NeTargetReportBeforeAfterDateEnum.reportDateTypeSet.contains(filter.getReportDateType())) {
                throw new SponsoredBizException("请求参数错误: reportDate");
            }
        }
    }

    private CampaignNeKeywordsPageParam reqVo2DtoV2(CampaignNeKewordDataRequest request){
        CampaignNeKeywordsPageParam param = new CampaignNeKeywordsPageParam();
        BeanUtils.copyProperties(request, param);
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        param.setPuid(request.getPuid().getValue());

        if (!request.hasPageNo()) {
            param.setPageNo(1);
        }
        if (!request.hasPageSize()) {
            param.setPageSize(20);
        }
        if (request.hasState()) {
            param.setState(request.getState());
        }
        if (request.hasStatus()) {
            param.setStatus(request.getStatus());
        }
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }

        //分析数据相关筛选参数
        NeTargetReportFilter filter = request.getFilter();
        NeTargetReportFilterDto neTargetReportFilterDto = new NeTargetReportFilterDto();
        if (filter.hasOrderField()) {
            neTargetReportFilterDto.setOrderField(filter.getOrderField());
        }
        if (filter.hasOrderType()) {
            neTargetReportFilterDto.setOrderType(filter.getOrderType());
        }
        if (filter.hasDoAdvancedFilter()) {
            neTargetReportFilterDto.setDoAdvancedFilter(filter.getDoAdvancedFilter());
        }
        if (filter.hasReportStartDate()) {
            neTargetReportFilterDto.setReportStartDate(filter.getReportStartDate());
        }
        if (filter.hasReportEndDate()) {
            neTargetReportFilterDto.setReportEndDate(filter.getReportEndDate());
        }
        if (filter.hasReportDateType()) {
            neTargetReportFilterDto.setReportDateType(filter.getReportDateType());
        }
        if (filter.hasDataFrom()) {
            neTargetReportFilterDto.setDataFrom(filter.getDataFrom());
        }
        if (filter.hasOnlyShowImpressions()) {
            neTargetReportFilterDto.setOnlyShowImpressions(filter.getOnlyShowImpressions());
        }
        if (filter.hasAdvanceFilterParams()) {
            NeTargetReportAdvanceFilters advanceFilterParams = filter.getAdvanceFilterParams();
            NeTargetReportAdvanceFiltersDto neTargetReportAdvanceFiltersDto = new NeTargetReportAdvanceFiltersDto();
            if (advanceFilterParams.hasAcosMin()) {
                neTargetReportAdvanceFiltersDto.setAcosMin(BigDecimal.valueOf(advanceFilterParams.getAcosMin()));
            }
            if (advanceFilterParams.hasAcosMax()) {
                neTargetReportAdvanceFiltersDto.setAcosMax(BigDecimal.valueOf(advanceFilterParams.getAcosMax()));
            }
            if (advanceFilterParams.hasAdOrderNumMin()) {
                neTargetReportAdvanceFiltersDto.setAdOrderNumMin(advanceFilterParams.getAdOrderNumMin());
            }
            if (advanceFilterParams.hasAdOrderNumMax()) {
                neTargetReportAdvanceFiltersDto.setAdOrderNumMax(advanceFilterParams.getAdOrderNumMax());
            }
            if (advanceFilterParams.hasClicksMin()) {
                neTargetReportAdvanceFiltersDto.setClicksMin(advanceFilterParams.getClicksMin());
            }
            if (advanceFilterParams.hasClicksMax()) {
                neTargetReportAdvanceFiltersDto.setClicksMax(advanceFilterParams.getClicksMax());
            }
            if (advanceFilterParams.hasImpressionsMin()) {
                neTargetReportAdvanceFiltersDto.setImpressionsMin(advanceFilterParams.getImpressionsMin());
            }
            if (advanceFilterParams.hasImpressionsMax()) {
                neTargetReportAdvanceFiltersDto.setImpressionsMax(advanceFilterParams.getImpressionsMax());
            }
            if (advanceFilterParams.hasAdCostMin()) {
                neTargetReportAdvanceFiltersDto.setAdCostMin(BigDecimal.valueOf(advanceFilterParams.getAdCostMin()));
            }
            if (advanceFilterParams.hasAdCostMax()) {
                neTargetReportAdvanceFiltersDto.setAdCostMax(BigDecimal.valueOf(advanceFilterParams.getAdCostMax()));
            }
            if (advanceFilterParams.hasAdSaleMin()) {
                neTargetReportAdvanceFiltersDto.setAdSaleMin(BigDecimal.valueOf(advanceFilterParams.getAdSaleMin()));
            }
            if (advanceFilterParams.hasAdSaleMax()) {
                neTargetReportAdvanceFiltersDto.setAdSaleMax(BigDecimal.valueOf(advanceFilterParams.getAdSaleMax()));
            }
            if (advanceFilterParams.hasCtrMin()) {
                neTargetReportAdvanceFiltersDto.setCtrMin(BigDecimal.valueOf(advanceFilterParams.getCtrMin()));
            }
            if (advanceFilterParams.hasCtrMax()) {
                neTargetReportAdvanceFiltersDto.setCtrMax(BigDecimal.valueOf(advanceFilterParams.getCtrMax()));
            }
            if (advanceFilterParams.hasCvrMin()) {
                neTargetReportAdvanceFiltersDto.setCvrMin(BigDecimal.valueOf(advanceFilterParams.getCvrMin()));
            }
            if (advanceFilterParams.hasCvrMax()) {
                neTargetReportAdvanceFiltersDto.setCvrMax(BigDecimal.valueOf(advanceFilterParams.getCvrMax()));
            }
            if (advanceFilterParams.hasRoasMin()) {
                neTargetReportAdvanceFiltersDto.setRoasMin(BigDecimal.valueOf(advanceFilterParams.getRoasMin()));
            }
            if (advanceFilterParams.hasRoasMax()) {
                neTargetReportAdvanceFiltersDto.setRoasMax(BigDecimal.valueOf(advanceFilterParams.getRoasMax()));
            }
            if (advanceFilterParams.hasCpcMin()) {
                neTargetReportAdvanceFiltersDto.setCpcMin(BigDecimal.valueOf(advanceFilterParams.getCpcMin()));
            }
            if (advanceFilterParams.hasCpcMax()) {
                neTargetReportAdvanceFiltersDto.setCpcMax(BigDecimal.valueOf(advanceFilterParams.getCpcMax()));
            }
            if (advanceFilterParams.hasCpaMin()) {
                neTargetReportAdvanceFiltersDto.setCpaMin(BigDecimal.valueOf(advanceFilterParams.getCpaMin()));
            }
            if (advanceFilterParams.hasCpaMax()) {
                neTargetReportAdvanceFiltersDto.setCpaMax(BigDecimal.valueOf(advanceFilterParams.getCpaMax()));
            }
            neTargetReportFilterDto.setAdvanceFilterParams(neTargetReportAdvanceFiltersDto);
        }
        param.setNeTargetReportFilterDto(neTargetReportFilterDto);

        return param;
    }

    private CampaignNeKeywordsPageParam reqVo2Dto(CampaignNeKewordDataRequest request){
        CampaignNeKeywordsPageParam param = new CampaignNeKeywordsPageParam();
        BeanUtils.copyProperties(request, param);
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        param.setPuid(request.getPuid().getValue());

        if (!request.hasPageNo()) {
            param.setPageNo(1);
        }
        if (!request.hasPageSize()) {
            param.setPageSize(20);
        }
        if (request.hasState()) {
            param.setState(request.getState());
        }
        if (request.hasStatus()) {
            param.setStatus(request.getStatus());
        }
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }
        return param;
    }

    // 校验分页查询的参数
    private String checkCamNeKeywordsPageParam(CampaignNeKeywordsPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }

        return null;
    }


    /**
     * 查询所有类型否定投放(活动)
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getCampaignNeTargetingData(CampaignNeTargetingDataRequest request, StreamObserver<CampaignNeTargetingDataResponse> responseObserver) {
        log.info("查询SP活动层级否定投放，request:{}", request);

        //1,校验基础请求参数
        checkRequestForBasicParam(request);

        //2,获取数据
        Page<CampaignNeTargetingSpRpcVo> voPage;
        if (configuration.getContainsReport()) {//包含报告数据
            //校验分析数据筛选参数
            checkAnalysisRequest(request);
            CampaignNeTargetingSpParam param = reqVo2DtoV2(request);
            voPage = spCampaignService.neTargetingPageListV2(param);
        } else {//不包含报告数据
            CampaignNeTargetingSpParam param = reqVo2Dto(request);
            voPage = cpcCommonService.neTargetingPageList(param);
        }

        //3,构建返回参数返回给前端
        responseObserver.onNext(buildRespForCampaignNeTargeting(voPage));
        responseObserver.onCompleted();
    }

    /**
     * 构建resp
     * @param voPage
     * @return
     */
    private CampaignNeTargetingDataResponse buildRespForCampaignNeTargeting(Page<CampaignNeTargetingSpRpcVo> voPage) {
        CampaignNeTargetingDataResponse.Page.Builder pageBuilder = CampaignNeTargetingDataResponse.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        if (voPage.getRows() != null) {
            pageBuilder.addAllRows(voPage.getRows());
        }

        CampaignNeTargetingDataResponse.Builder respBuilder = CampaignNeTargetingDataResponse.newBuilder();
        respBuilder.setData(pageBuilder.build());
        respBuilder.setCode(Int32Value.of(Result.SUCCESS));
        return respBuilder.build();
    }

    private void checkRequestForBasicParam(CampaignNeTargetingDataRequest request) {
        if (!request.hasShopId() || StringUtils.isNotBlank(request.getStatus()) && StateEnum.getStateValue(request.getStatus()) == null) {
            throw new SponsoredBizException("请求参数错误");
        }
    }

    /**
     * 校验基础请求参数
     * @param request
     */
    private void checkAnalysisRequest(CampaignNeTargetingDataRequest request) {
        if (!request.hasFilter()) {
            throw new SponsoredBizException("缺少报告数据过滤条件");
        }
        NeTargetReportFilter filter = request.getFilter();
        if (filter.hasOrderField() && !NeTargetReportParamsMappingEnum.FRONT_PARAM_LIST.contains(filter.getOrderField())) {
            throw new SponsoredBizException("请求参数错误: orderField");
        }
        if (filter.hasOrderType() && !"desc".equalsIgnoreCase(filter.getOrderType()) && !"asc".equalsIgnoreCase(filter.getOrderType())) {
            throw new SponsoredBizException("请求参数错误: orderType");
        }
        if (filter.hasDataFrom() && filter.getDataFrom() != 1 && filter.getDataFrom() != 2) {
            throw new SponsoredBizException("请求参数错误: dataFrom");
        }
        if (StringUtils.isAnyBlank(filter.getReportStartDate(), filter.getReportEndDate())) {
            if (StringUtils.isNotBlank(filter.getReportDateType()) && !NeTargetReportBeforeAfterDateEnum.reportDateTypeSet.contains(filter.getReportDateType())) {
                throw new SponsoredBizException("请求参数错误: reportDate");
            }
        }
    }

    private CampaignNeTargetingSpParam reqVo2DtoV2(CampaignNeTargetingDataRequest request) {
        CampaignNeTargetingSpParam param = new CampaignNeTargetingSpParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        if (request.hasStatus()) {
            param.setStatus(request.getStatus());
        }
        if (request.hasState()) {
            param.setState(request.getState());
        }
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }
        if (request.hasDesc()) {
            param.setDesc(request.getDesc().getValue());
        }

        //分析数据筛选条件
        NeTargetReportFilter filter = request.getFilter();
        NeTargetReportFilterDto neTargetReportFilterDto = new NeTargetReportFilterDto();
        if (filter.hasOrderField()) {
            neTargetReportFilterDto.setOrderField(filter.getOrderField());
        }
        if (filter.hasOrderType()) {
            neTargetReportFilterDto.setOrderType(filter.getOrderType());
        }
        if (filter.hasDoAdvancedFilter()) {
            neTargetReportFilterDto.setDoAdvancedFilter(filter.getDoAdvancedFilter());
        }
        if (filter.hasReportStartDate()) {
            neTargetReportFilterDto.setReportStartDate(filter.getReportStartDate());
        }
        if (filter.hasReportEndDate()) {
            neTargetReportFilterDto.setReportEndDate(filter.getReportEndDate());
        }
        if (filter.hasReportDateType()) {
            neTargetReportFilterDto.setReportDateType(filter.getReportDateType());
        }
        if (filter.hasDataFrom()) {
            neTargetReportFilterDto.setDataFrom(filter.getDataFrom());
        }
        if (filter.hasOnlyShowImpressions()) {
            neTargetReportFilterDto.setOnlyShowImpressions(filter.getOnlyShowImpressions());
        }
        if (filter.hasAdvanceFilterParams()) {
            NeTargetReportAdvanceFilters advanceFilterParams = filter.getAdvanceFilterParams();
            NeTargetReportAdvanceFiltersDto neTargetReportAdvanceFiltersDto = new NeTargetReportAdvanceFiltersDto();
            if (advanceFilterParams.hasAcosMin()) {
                neTargetReportAdvanceFiltersDto.setAcosMin(BigDecimal.valueOf(advanceFilterParams.getAcosMin()));
            }
            if (advanceFilterParams.hasAcosMax()) {
                neTargetReportAdvanceFiltersDto.setAcosMax(BigDecimal.valueOf(advanceFilterParams.getAcosMax()));
            }
            if (advanceFilterParams.hasAdOrderNumMin()) {
                neTargetReportAdvanceFiltersDto.setAdOrderNumMin(advanceFilterParams.getAdOrderNumMin());
            }
            if (advanceFilterParams.hasAdOrderNumMax()) {
                neTargetReportAdvanceFiltersDto.setAdOrderNumMax(advanceFilterParams.getAdOrderNumMax());
            }
            if (advanceFilterParams.hasClicksMin()) {
                neTargetReportAdvanceFiltersDto.setClicksMin(advanceFilterParams.getClicksMin());
            }
            if (advanceFilterParams.hasClicksMax()) {
                neTargetReportAdvanceFiltersDto.setClicksMax(advanceFilterParams.getClicksMax());
            }
            if (advanceFilterParams.hasImpressionsMin()) {
                neTargetReportAdvanceFiltersDto.setImpressionsMin(advanceFilterParams.getImpressionsMin());
            }
            if (advanceFilterParams.hasImpressionsMax()) {
                neTargetReportAdvanceFiltersDto.setImpressionsMax(advanceFilterParams.getImpressionsMax());
            }
            if (advanceFilterParams.hasAdCostMin()) {
                neTargetReportAdvanceFiltersDto.setAdCostMin(BigDecimal.valueOf(advanceFilterParams.getAdCostMin()));
            }
            if (advanceFilterParams.hasAdCostMax()) {
                neTargetReportAdvanceFiltersDto.setAdCostMax(BigDecimal.valueOf(advanceFilterParams.getAdCostMax()));
            }
            if (advanceFilterParams.hasAdSaleMin()) {
                neTargetReportAdvanceFiltersDto.setAdSaleMin(BigDecimal.valueOf(advanceFilterParams.getAdSaleMin()));
            }
            if (advanceFilterParams.hasAdSaleMax()) {
                neTargetReportAdvanceFiltersDto.setAdSaleMax(BigDecimal.valueOf(advanceFilterParams.getAdSaleMax()));
            }
            if (advanceFilterParams.hasCtrMin()) {
                neTargetReportAdvanceFiltersDto.setCtrMin(BigDecimal.valueOf(advanceFilterParams.getCtrMin()));
            }
            if (advanceFilterParams.hasCtrMax()) {
                neTargetReportAdvanceFiltersDto.setCtrMax(BigDecimal.valueOf(advanceFilterParams.getCtrMax()));
            }
            if (advanceFilterParams.hasCvrMin()) {
                neTargetReportAdvanceFiltersDto.setCvrMin(BigDecimal.valueOf(advanceFilterParams.getCvrMin()));
            }
            if (advanceFilterParams.hasCvrMax()) {
                neTargetReportAdvanceFiltersDto.setCvrMax(BigDecimal.valueOf(advanceFilterParams.getCvrMax()));
            }
            if (advanceFilterParams.hasRoasMin()) {
                neTargetReportAdvanceFiltersDto.setRoasMin(BigDecimal.valueOf(advanceFilterParams.getRoasMin()));
            }
            if (advanceFilterParams.hasRoasMax()) {
                neTargetReportAdvanceFiltersDto.setRoasMax(BigDecimal.valueOf(advanceFilterParams.getRoasMax()));
            }
            if (advanceFilterParams.hasCpcMin()) {
                neTargetReportAdvanceFiltersDto.setCpcMin(BigDecimal.valueOf(advanceFilterParams.getCpcMin()));
            }
            if (advanceFilterParams.hasCpcMax()) {
                neTargetReportAdvanceFiltersDto.setCpcMax(BigDecimal.valueOf(advanceFilterParams.getCpcMax()));
            }
            if (advanceFilterParams.hasCpaMin()) {
                neTargetReportAdvanceFiltersDto.setCpaMin(BigDecimal.valueOf(advanceFilterParams.getCpaMin()));
            }
            if (advanceFilterParams.hasCpaMax()) {
                neTargetReportAdvanceFiltersDto.setCpaMax(BigDecimal.valueOf(advanceFilterParams.getCpaMax()));
            }
            neTargetReportFilterDto.setAdvanceFilterParams(neTargetReportAdvanceFiltersDto);
        }
        param.setNeTargetReportFilterDto(neTargetReportFilterDto);
        return param;
    }

    private CampaignNeTargetingSpParam reqVo2Dto(CampaignNeTargetingDataRequest request) {
        CampaignNeTargetingSpParam param = new CampaignNeTargetingSpParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        if (request.hasStatus()) {
            param.setStatus(request.getStatus());
        }
        if (request.hasState()) {
            param.setState(request.getState());
        }
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }
        if (request.hasDesc()) {
            param.setDesc(request.getDesc().getValue());
        }
        return param;
    }

    /**
     * 自动化规则全部查询广告组合
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void queryPortfolio(QueryPortfolioRequest request, StreamObserver<QueryPortfolioResponse> responseObserver) {
        log.info("自动化规则全部查询广告组合{}:", request);
        QueryPortfolioResponse.Builder response = QueryPortfolioResponse.newBuilder();
        Result<List<AmazonAdPortfolio>> result = cpcCommonService.queryPortfolio(request.getPuid(), request.getShopId(),
                request.getMarketplaceId(), request.getPortfolioName(), request.getPageSize(), request.getPageNo());
        response.setCode(result.getCode());
        if (result.getCode() == Result.SUCCESS) {
            if (CollectionUtils.isNotEmpty(result.getData())) {
                result.getData().forEach(e -> {
                    QueryPortfolio.Builder builder = QueryPortfolio.newBuilder();
                    builder.setPortfolioId(e.getPortfolioId());
                    builder.setPortfolioName(e.getName());
                    builder.setIsHidden(1 == e.getIsHidden());
                    response.addData(builder.build());
                });
            }
        }
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();

    }

    /**
     * 自动化规则下拉查询广告活动
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void queryCampaign(QueryCampaignRequest request, StreamObserver<QueryCampaignResponse> responseObserver) {
        log.info("自动化规则全部查询广告活动:{}", request);
        QueryCampaignResponse.Builder response = QueryCampaignResponse.newBuilder();
        Result<Page<AmazonAdCampaignAll>> result = cpcCommonService.queryCampaign(request.getPuid(),
                request.getShopId(),
                request.getAdTypeList(),
                request.getPortfolioIdList(),
                request.getPageSize(),
                request.getPageNo(),
                request.getCampaignName(),
                request.getQueryType(),
                request.getTargetingType(),
                request.getCampaignId(),
                request.getStatusList());
        response.setCode(result.getCode());
        if (result.getCode() == Result.SUCCESS) {
            QueryCampaignPage.Builder pageBuilder = QueryCampaignPage.newBuilder();
            pageBuilder.setPageNo(request.getPageNo());
            pageBuilder.setPageSize(request.getPageSize());
            if (CollectionUtils.isNotEmpty(result.getData().getRows())) {
                List<QueryCampaign> queryCampaignList = new ArrayList<>();
                pageBuilder.setTotalPage(result.getData().getTotalPage());
                pageBuilder.setTotalSize(result.getData().getTotalSize());
                result.getData().getRows().forEach(e -> {
                    QueryCampaign.Builder builder = QueryCampaign.newBuilder();
                    builder.setCampaignId(e.getCampaignId());
                    builder.setCampaignName(e.getName());
                    builder.setAdType(e.getType());
                    builder.setState(e.getState());
                    queryCampaignList.add(builder.build());
                });
                pageBuilder.addAllRows(queryCampaignList);
            }
            response.setData(pageBuilder.build());
        }
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();

    }

    /**
     * 自动化规则下拉查询广告组
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void queryGroup(QueryGroupRequest request, StreamObserver<QueryGroupResponse> responseObserver) {
        log.info("自动化规则全部查询广告组:{}", request);
        QueryGroupResponse.Builder response = QueryGroupResponse.newBuilder();
        Result<Page<AmazonAdGroup>> result = cpcCommonService.queryGroup(request.getPuid(),
                request.getShopId(),
                request.getAdTypeList(),
                request.getCampaignIdList(),
                request.getPageSize(),
                request.getPageNo(),
                request.getGroupName(),
                request.getQueryType(),
                request.getTargetingTypesList(),
                request.getStatusList());
        response.setCode(result.getCode());
        if (result.getCode() == Result.SUCCESS) {
            QueryGroupPage.Builder pageBuilder = QueryGroupPage.newBuilder();
            pageBuilder.setPageNo(request.getPageNo());
            pageBuilder.setPageSize(request.getPageSize());
            if (CollectionUtils.isNotEmpty(result.getData().getRows())) {
                List<QueryGroup> queryGroupList = new ArrayList<>();
                pageBuilder.setTotalPage(result.getData().getTotalPage());
                pageBuilder.setTotalSize(result.getData().getTotalSize());
                result.getData().getRows().forEach(e -> {
                    QueryGroup.Builder builder = QueryGroup.newBuilder();
                    builder.setGroupId(e.getAdGroupId());
                    builder.setGroupName(e.getName());
                    builder.setAdType(e.getAdType());
                    builder.setState(e.getState());
                    queryGroupList.add(builder.build());
                });
                pageBuilder.addAllRows(queryGroupList);
            }
            response.setData(pageBuilder.build());
        }
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }


    /**
     * 所有类型否定关键词
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllNeKeywordData(AllNeKeywordDataRequest request, StreamObserver<AllNeKeywordDataResponse> responseObserver) {
        log.info("查询sp，sb类型否定关键词，request:{}", request);

        //1,校验基础参数
        checkBasicParam(request);
        NeKeywordsPageParam param = builderNeKeywordsPageParam(request);

        //2,获取数据
        Page<NeKeywordsPageRpcVo> voPage;
        if (configuration.getContainsReport()) {//包含报告数据
            //校验分析数据筛选参数
            checkAnalysisRequest(request);
            fillAnalysisToParam(request, param);
            voPage = groupNeKeywordsService.allNekeywordPageListV2(param, false);
        } else {//不包含报告数据
            voPage = cpcCommonService.allNekeywordPageList(param);
        }

        //3,构建返回参数返回给前端
        responseObserver.onNext(buildRespForNeKeyword(voPage));
        responseObserver.onCompleted();
    }

    /**
     * 构建返回对象
     * @param voPage
     * @return
     */
    private AllNeKeywordDataResponse buildRespForNeKeyword(Page<NeKeywordsPageRpcVo> voPage) {
        AllNeKeywordDataResponse.Page.Builder pageBuilder = AllNeKeywordDataResponse.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        if (voPage.getRows() != null) {
            pageBuilder.addAllRows(voPage.getRows());
        }

        AllNeKeywordDataResponse.Builder respBuilder = AllNeKeywordDataResponse.newBuilder();
        respBuilder.setData(pageBuilder.build());
        respBuilder.setCode(Int32Value.of(Result.SUCCESS));
        return respBuilder.build();
    }

    /**
     * 校验基础请求参数
     * @param request
     */
    private void checkBasicParam(AllNeKeywordDataRequest request){
        if (!request.hasShopId() || !request.hasPuid()) {
            throw new SponsoredBizException("缺少puid 或 shopId");
        }
        if (request.hasType()) {
            String type = request.getType();
            if (!Constants.SP.equalsIgnoreCase(type) && !Constants.SB.equalsIgnoreCase(type)) {
                throw new SponsoredBizException("广告类型错误");
            }
        }
    }

    /**
     * 构建查询参数
     * @param request
     * @return
     */
    private NeKeywordsPageParam builderNeKeywordsPageParam(AllNeKeywordDataRequest request) {
        NeKeywordsPageParam param = new NeKeywordsPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());

        if (StringUtils.isNotBlank(request.getSearchType())) {
            param.setSearchType(request.getSearchType());
        }
        if (request.hasUid()) {
            param.setUid(request.getUid().getValue());
        }
        if (StringUtils.isNotBlank(request.getState())) {
            param.setState(request.getState());
        }
        if (StringUtils.isNotBlank(request.getStatus())) {
            param.setStatus(request.getStatus());
        }
        if (StringUtils.isNotBlank(request.getServingStatus())) {
            param.setServingStatus(request.getServingStatus());
        }
        if (!request.hasPageNo()) {
            param.setPageNo(1);
        } else {
            param.setPageNo(request.getPageNo().getValue());
        }
        if (!request.hasPageSize()) {
            param.setPageSize(10);
        } else {
            param.setPageSize(request.getPageSize().getValue());
        }
        return param;
    }

    /**
     * 填充分析数据筛选参数
     * @param request
     * @param param
     */
    private void fillAnalysisToParam(AllNeKeywordDataRequest request, NeKeywordsPageParam param){
        //报告数据相关查询
        NeTargetReportFilter filter = request.getFilter();
        NeTargetReportFilterDto neTargetReportFilterDto = new NeTargetReportFilterDto();
        if (filter.hasOrderField()) {
            neTargetReportFilterDto.setOrderField(filter.getOrderField());
        }
        if (filter.hasOrderType()) {
            neTargetReportFilterDto.setOrderType(filter.getOrderType());
        }
        if (filter.hasDoAdvancedFilter()) {
            neTargetReportFilterDto.setDoAdvancedFilter(filter.getDoAdvancedFilter());
        }
        if (filter.hasReportStartDate()) {
            neTargetReportFilterDto.setReportStartDate(filter.getReportStartDate());
        }
        if (filter.hasReportEndDate()) {
            neTargetReportFilterDto.setReportEndDate(filter.getReportEndDate());
        }
        if (filter.hasReportDateType()) {
            neTargetReportFilterDto.setReportDateType(filter.getReportDateType());
        }
        if (filter.hasDataFrom()) {
            neTargetReportFilterDto.setDataFrom(filter.getDataFrom());
        }
        if (filter.hasOnlyShowImpressions()) {
            neTargetReportFilterDto.setOnlyShowImpressions(filter.getOnlyShowImpressions());
        }
        if (filter.hasAdvanceFilterParams()) {
            NeTargetReportAdvanceFilters advanceFilterParams = filter.getAdvanceFilterParams();
            NeTargetReportAdvanceFiltersDto neTargetReportAdvanceFiltersDto = new NeTargetReportAdvanceFiltersDto();
            if (advanceFilterParams.hasAcosMin()) {
                neTargetReportAdvanceFiltersDto.setAcosMin(BigDecimal.valueOf(advanceFilterParams.getAcosMin()));
            }
            if (advanceFilterParams.hasAcosMax()) {
                neTargetReportAdvanceFiltersDto.setAcosMax(BigDecimal.valueOf(advanceFilterParams.getAcosMax()));
            }
            if (advanceFilterParams.hasAdOrderNumMin()) {
                neTargetReportAdvanceFiltersDto.setAdOrderNumMin(advanceFilterParams.getAdOrderNumMin());
            }
            if (advanceFilterParams.hasAdOrderNumMax()) {
                neTargetReportAdvanceFiltersDto.setAdOrderNumMax(advanceFilterParams.getAdOrderNumMax());
            }
            if (advanceFilterParams.hasClicksMin()) {
                neTargetReportAdvanceFiltersDto.setClicksMin(advanceFilterParams.getClicksMin());
            }
            if (advanceFilterParams.hasClicksMax()) {
                neTargetReportAdvanceFiltersDto.setClicksMax(advanceFilterParams.getClicksMax());
            }
            if (advanceFilterParams.hasImpressionsMin()) {
                neTargetReportAdvanceFiltersDto.setImpressionsMin(advanceFilterParams.getImpressionsMin());
            }
            if (advanceFilterParams.hasImpressionsMax()) {
                neTargetReportAdvanceFiltersDto.setImpressionsMax(advanceFilterParams.getImpressionsMax());
            }
            if (advanceFilterParams.hasAdCostMin()) {
                neTargetReportAdvanceFiltersDto.setAdCostMin(BigDecimal.valueOf(advanceFilterParams.getAdCostMin()));
            }
            if (advanceFilterParams.hasAdCostMax()) {
                neTargetReportAdvanceFiltersDto.setAdCostMax(BigDecimal.valueOf(advanceFilterParams.getAdCostMax()));
            }
            if (advanceFilterParams.hasAdSaleMin()) {
                neTargetReportAdvanceFiltersDto.setAdSaleMin(BigDecimal.valueOf(advanceFilterParams.getAdSaleMin()));
            }
            if (advanceFilterParams.hasAdSaleMax()) {
                neTargetReportAdvanceFiltersDto.setAdSaleMax(BigDecimal.valueOf(advanceFilterParams.getAdSaleMax()));
            }
            if (advanceFilterParams.hasCtrMin()) {
                neTargetReportAdvanceFiltersDto.setCtrMin(BigDecimal.valueOf(advanceFilterParams.getCtrMin()));
            }
            if (advanceFilterParams.hasCtrMax()) {
                neTargetReportAdvanceFiltersDto.setCtrMax(BigDecimal.valueOf(advanceFilterParams.getCtrMax()));
            }
            if (advanceFilterParams.hasCvrMin()) {
                neTargetReportAdvanceFiltersDto.setCvrMin(BigDecimal.valueOf(advanceFilterParams.getCvrMin()));
            }
            if (advanceFilterParams.hasCvrMax()) {
                neTargetReportAdvanceFiltersDto.setCvrMax(BigDecimal.valueOf(advanceFilterParams.getCvrMax()));
            }
            if (advanceFilterParams.hasRoasMin()) {
                neTargetReportAdvanceFiltersDto.setRoasMin(BigDecimal.valueOf(advanceFilterParams.getRoasMin()));
            }
            if (advanceFilterParams.hasRoasMax()) {
                neTargetReportAdvanceFiltersDto.setRoasMax(BigDecimal.valueOf(advanceFilterParams.getRoasMax()));
            }
            if (advanceFilterParams.hasCpcMin()) {
                neTargetReportAdvanceFiltersDto.setCpcMin(BigDecimal.valueOf(advanceFilterParams.getCpcMin()));
            }
            if (advanceFilterParams.hasCpcMax()) {
                neTargetReportAdvanceFiltersDto.setCpcMax(BigDecimal.valueOf(advanceFilterParams.getCpcMax()));
            }
            if (advanceFilterParams.hasCpaMin()) {
                neTargetReportAdvanceFiltersDto.setCpaMin(BigDecimal.valueOf(advanceFilterParams.getCpaMin()));
            }
            if (advanceFilterParams.hasCpaMax()) {
                neTargetReportAdvanceFiltersDto.setCpaMax(BigDecimal.valueOf(advanceFilterParams.getCpaMax()));
            }
            neTargetReportFilterDto.setAdvanceFilterParams(neTargetReportAdvanceFiltersDto);
        }
        param.setNeTargetReportFilterDto(neTargetReportFilterDto);
    }

    /**
     * 校验分析数据筛选参数
     * @param request
     */
    private void checkAnalysisRequest(AllNeKeywordDataRequest request) {
        if (!request.hasFilter()) {
            throw new SponsoredBizException("缺少报告数据过滤条件");
        }
        NeTargetReportFilter filter = request.getFilter();
        if (filter.hasOrderField() && !NeTargetReportParamsMappingEnum.FRONT_PARAM_LIST.contains(filter.getOrderField())) {
            throw new SponsoredBizException("请求参数错误: orderField");
        }
        if (filter.hasOrderType() && !"desc".equalsIgnoreCase(filter.getOrderType()) && !"asc".equalsIgnoreCase(filter.getOrderType())) {
            throw new SponsoredBizException("请求参数错误: orderType");
        }
        if (filter.hasDataFrom() && filter.getDataFrom() != 1 && filter.getDataFrom() != 2) {
            throw new SponsoredBizException("请求参数错误: dataFrom");
        }
        if (StringUtils.isAnyBlank(filter.getReportStartDate(), filter.getReportEndDate())) {
            if (StringUtils.isNotBlank(filter.getReportDateType()) && !NeTargetReportBeforeAfterDateEnum.reportDateTypeSet.contains(filter.getReportDateType())) {
                throw new SponsoredBizException("请求参数错误: reportDate");
            }
        }
    }


    // 校验分页查询的参数
    private String checkNeKeywordsPageParam(NeKeywordsPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(10);
        }
        if (StringUtils.isNotBlank(param.getType())) {
            if (!(param.getType().equalsIgnoreCase(Constants.SB) || param.getType().equalsIgnoreCase(Constants.SP))) {
                return "请求参数错误";
            }
        }

        return null;
    }

    /**
     * 所有类型否定投放
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllNeTargetingData(AllNeTargetingDataRequest request, StreamObserver<AllNeTargetingDataResponse> responseObserver) {
        log.info("查询所有类型否定投放，request:{}", request);

        //1,校验基础参数
        checkBasicParam(request);
        NeTargetingPageParam param = buildReqParam(request);

        //2,获取数据
        Page<NeTargetingPageRpcVo> voPage;
        if (configuration.getContainsReport()) {//包含报告数据
            //校验分析数据筛选参数
            checkRequest(request);
            fillAnalysisFilter(request, param);
            voPage = cpcTargetingService.getAllNeTargetingPageListV2(param);
        } else {//不包含报告数据
            voPage = cpcCommonService.getAllNeTargeting(param);
        }

        //3,构建返回参数返回给前端
        responseObserver.onNext(buildRespForNeTargeting(voPage));
        responseObserver.onCompleted();
    }

    private AllNeTargetingDataResponse buildRespForNeTargeting(Page<NeTargetingPageRpcVo> voPage) {
        AllNeTargetingDataResponse.Page.Builder pageBuilder = AllNeTargetingDataResponse.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        if (voPage.getRows() != null) {
            pageBuilder.addAllRows(voPage.getRows());
        }

        AllNeTargetingDataResponse.Builder respBuilder = AllNeTargetingDataResponse.newBuilder();
        respBuilder.setData(pageBuilder.build());
        respBuilder.setCode(Int32Value.of(Result.SUCCESS));
        return respBuilder.build();
    }

    /**
     * 校验基础请求参数
     * @param request
     */
    private void checkBasicParam(AllNeTargetingDataRequest request){
        if (!request.hasShopId() || !request.hasPuid()) {
            throw new SponsoredBizException("缺少puid 或 shopId");
        }
        if (request.hasType()) {
            String type = request.getType();
            if (!Constants.SP.equalsIgnoreCase(type) && !Constants.SB.equalsIgnoreCase(type) && !Constants.SD.equalsIgnoreCase(type)) {
                throw new SponsoredBizException("广告类型错误");
            }
        }
    }

    private NeTargetingPageParam buildReqParam(AllNeTargetingDataRequest request) {
        NeTargetingPageParam param = new NeTargetingPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        if (!request.hasPageNo()) {
            param.setPageNo(1);
        } else {
            param.setPageNo(request.getPageNo().getValue());
        }
        if (!request.hasPageSize()) {
            param.setPageSize(20);
        } else {
            param.setPageSize(request.getPageSize().getValue());
        }
        if (request.hasState()) {
            param.setState(request.getState());
        }
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }
        if (request.hasStatus()) {
            param.setStatus(request.getStatus());
        }
        return param;
    }

    private void fillAnalysisFilter(AllNeTargetingDataRequest request, NeTargetingPageParam param){
        //报告数据相关查询
        NeTargetReportFilter filter = request.getFilter();
        NeTargetReportFilterDto neTargetReportFilterDto = new NeTargetReportFilterDto();
        if (filter.hasOrderField()) {
            neTargetReportFilterDto.setOrderField(filter.getOrderField());
        }
        if (filter.hasOrderType()) {
            neTargetReportFilterDto.setOrderType(filter.getOrderType());
        }
        if (filter.hasReportStartDate()) {
            neTargetReportFilterDto.setReportStartDate(filter.getReportStartDate());
        }
        if (filter.hasDoAdvancedFilter()) {
            neTargetReportFilterDto.setDoAdvancedFilter(filter.getDoAdvancedFilter());
        }
        if (filter.hasReportEndDate()) {
            neTargetReportFilterDto.setReportEndDate(filter.getReportEndDate());
        }
        if (filter.hasReportDateType()) {
            neTargetReportFilterDto.setReportDateType(filter.getReportDateType());
        }
        if (filter.hasDataFrom()) {
            neTargetReportFilterDto.setDataFrom(filter.getDataFrom());
        }
        if (filter.hasOnlyShowImpressions()) {
            neTargetReportFilterDto.setOnlyShowImpressions(filter.getOnlyShowImpressions());
        }
        Optional.of(filter.getOnlyShowImpressions()).ifPresent(neTargetReportFilterDto::setOnlyShowImpressions);
        if (filter.hasAdvanceFilterParams()) {
            NeTargetReportAdvanceFilters advanceFilterParams = filter.getAdvanceFilterParams();
            NeTargetReportAdvanceFiltersDto neTargetReportAdvanceFiltersDto = new NeTargetReportAdvanceFiltersDto();
            if (advanceFilterParams.hasAcosMin()) {
                neTargetReportAdvanceFiltersDto.setAcosMin(BigDecimal.valueOf(advanceFilterParams.getAcosMin()));
            }
            if (advanceFilterParams.hasAcosMax()) {
                neTargetReportAdvanceFiltersDto.setAcosMax(BigDecimal.valueOf(advanceFilterParams.getAcosMax()));
            }
            if (advanceFilterParams.hasAdOrderNumMin()) {
                neTargetReportAdvanceFiltersDto.setAdOrderNumMin(advanceFilterParams.getAdOrderNumMin());
            }
            if (advanceFilterParams.hasAdOrderNumMax()) {
                neTargetReportAdvanceFiltersDto.setAdOrderNumMax(advanceFilterParams.getAdOrderNumMax());
            }
            if (advanceFilterParams.hasClicksMin()) {
                neTargetReportAdvanceFiltersDto.setClicksMin(advanceFilterParams.getClicksMin());
            }
            if (advanceFilterParams.hasClicksMax()) {
                neTargetReportAdvanceFiltersDto.setClicksMax(advanceFilterParams.getClicksMax());
            }
            if (advanceFilterParams.hasImpressionsMin()) {
                neTargetReportAdvanceFiltersDto.setImpressionsMin(advanceFilterParams.getImpressionsMin());
            }
            if (advanceFilterParams.hasImpressionsMax()) {
                neTargetReportAdvanceFiltersDto.setImpressionsMax(advanceFilterParams.getImpressionsMax());
            }
            if (advanceFilterParams.hasAdCostMin()) {
                neTargetReportAdvanceFiltersDto.setAdCostMin(BigDecimal.valueOf(advanceFilterParams.getAdCostMin()));
            }
            if (advanceFilterParams.hasAdCostMax()) {
                neTargetReportAdvanceFiltersDto.setAdCostMax(BigDecimal.valueOf(advanceFilterParams.getAdCostMax()));
            }
            if (advanceFilterParams.hasAdSaleMin()) {
                neTargetReportAdvanceFiltersDto.setAdSaleMin(BigDecimal.valueOf(advanceFilterParams.getAdSaleMin()));
            }
            if (advanceFilterParams.hasAdSaleMax()) {
                neTargetReportAdvanceFiltersDto.setAdSaleMax(BigDecimal.valueOf(advanceFilterParams.getAdSaleMax()));
            }
            if (advanceFilterParams.hasCtrMin()) {
                neTargetReportAdvanceFiltersDto.setCtrMin(BigDecimal.valueOf(advanceFilterParams.getCtrMin()));
            }
            if (advanceFilterParams.hasCtrMax()) {
                neTargetReportAdvanceFiltersDto.setCtrMax(BigDecimal.valueOf(advanceFilterParams.getCtrMax()));
            }
            if (advanceFilterParams.hasCvrMin()) {
                neTargetReportAdvanceFiltersDto.setCvrMin(BigDecimal.valueOf(advanceFilterParams.getCvrMin()));
            }
            if (advanceFilterParams.hasCvrMax()) {
                neTargetReportAdvanceFiltersDto.setCvrMax(BigDecimal.valueOf(advanceFilterParams.getCvrMax()));
            }
            if (advanceFilterParams.hasRoasMin()) {
                neTargetReportAdvanceFiltersDto.setRoasMin(BigDecimal.valueOf(advanceFilterParams.getRoasMin()));
            }
            if (advanceFilterParams.hasRoasMax()) {
                neTargetReportAdvanceFiltersDto.setRoasMax(BigDecimal.valueOf(advanceFilterParams.getRoasMax()));
            }
            if (advanceFilterParams.hasCpcMin()) {
                neTargetReportAdvanceFiltersDto.setCpcMin(BigDecimal.valueOf(advanceFilterParams.getCpcMin()));
            }
            if (advanceFilterParams.hasCpcMax()) {
                neTargetReportAdvanceFiltersDto.setCpcMax(BigDecimal.valueOf(advanceFilterParams.getCpcMax()));
            }
            if (advanceFilterParams.hasCpaMin()) {
                neTargetReportAdvanceFiltersDto.setCpaMin(BigDecimal.valueOf(advanceFilterParams.getCpaMin()));
            }
            if (advanceFilterParams.hasCpaMax()) {
                neTargetReportAdvanceFiltersDto.setCpaMax(BigDecimal.valueOf(advanceFilterParams.getCpaMax()));
            }
            neTargetReportFilterDto.setAdvanceFilterParams(neTargetReportAdvanceFiltersDto);
        }
        param.setNeTargetReportFilterDto(neTargetReportFilterDto);
    }
    private void checkRequest(AllNeTargetingDataRequest request) {
        if (!request.hasFilter()) {
            throw new SponsoredBizException("缺少报告数据过滤条件");
        }
        NeTargetReportFilter filter = request.getFilter();
        if (filter.hasOrderField() && !NeTargetReportParamsMappingEnum.FRONT_PARAM_LIST.contains(filter.getOrderField())) {
            throw new SponsoredBizException("请求参数错误: orderField");
        }
        if (filter.hasOrderType() && !"desc".equalsIgnoreCase(filter.getOrderType()) && !"asc".equalsIgnoreCase(filter.getOrderType())) {
            throw new SponsoredBizException("请求参数错误: orderType");
        }
        if (filter.hasDataFrom() && filter.getDataFrom() != 1 && filter.getDataFrom() != 2) {
            throw new SponsoredBizException("请求参数错误: dataFrom");
        }
        if (StringUtils.isAnyBlank(filter.getReportStartDate(), filter.getReportEndDate())) {
            if (StringUtils.isNotBlank(filter.getReportDateType()) && !NeTargetReportBeforeAfterDateEnum.reportDateTypeSet.contains(filter.getReportDateType())) {
                throw new SponsoredBizException("请求参数错误: reportDate");
            }
        }
    }


    private String checkNeTargetingPageParam(NeTargetingPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }
        if (StringUtils.isNotBlank(param.getType())) {
            List<String> allType = Arrays.asList(Constants.SP, Constants.SD, Constants.SB);
            if (!(allType.contains(param.getType()))) {
                return "请求参数错误";
            }
        }

        return null;
    }

    /**
     * 所有类型关键词
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllKeyWordData(AllKeyWordDataRequest request, StreamObserver<AllKeyWordDataResponse> responseObserver) {
        log.info("所有类型关键词", request);
        AllKeyWordDataResponse.Builder builder = AllKeyWordDataResponse.newBuilder();
        KeywordsPageParam param = new KeywordsPageParam();
        BeanUtils.copyProperties(request, param);
        //参数填充
        fillAllKeyWordParam(request, param);
        //做参数校验
        String err = checkKeywordsPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            try {
                AllKeyWordDataResponse.AdkeywordHomeRpcVo homeVO = cpcCommonService.getAllKeyWordData(param.getPuid(), param);
                builder.setData(homeVO);
                builder.setCode(Int32Value.of(Result.SUCCESS));
                if (!CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(param.getType())) {
                    serversStatusSyncService.sendKeyword(request.getPuid().getValue(), request.getShopId().getValue(), homeVO);
                }
            } catch (ServiceException e) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg(e.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 关键词列表查询参数填充
     */
    private void fillAllKeyWordParam(AllKeyWordDataRequest request, KeywordsPageParam param) {
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        if (StringUtils.isNotBlank(request.getSearchType())) {
            param.setSearchType(param.getSearchType());
        }

        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate().replace("-", ""));
        param.setCompareEndDate(request.getCompareEndDate().replace("-", ""));

        param.setOnlyCount(request.getOnlyCount());
        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }

        if (CollectionUtils.isNotEmpty(request.getAdTagIdsList())) {
            param.setAdTagIdList(request.getAdTagIdsList());
        }

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }


        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setAdCostPercentageMin(advancedFilter.hasAdCostPercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdCostPercentageMin()) : null);
            param.setAdCostPercentageMax(advancedFilter.hasAdCostPercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdCostPercentageMax()) : null);
            param.setAdSalePercentageMin(advancedFilter.hasAdSalePercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdSalePercentageMin()) : null);
            param.setAdSalePercentageMax(advancedFilter.hasAdSalePercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdSalePercentageMax()) : null);
            param.setAdOrderNumPercentageMin(advancedFilter.hasAdOrderNumPercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdOrderNumPercentageMin()) : null);
            param.setAdOrderNumPercentageMax(advancedFilter.hasAdOrderNumPercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdOrderNumPercentageMax()) : null);
            param.setOrderNumPercentageMin(advancedFilter.hasOrderNumPercentageMin() ? BigDecimal.valueOf(advancedFilter.getOrderNumPercentageMin()) : null);
            param.setOrderNumPercentageMax(advancedFilter.hasOrderNumPercentageMax() ? BigDecimal.valueOf(advancedFilter.getOrderNumPercentageMax()) : null);
            /********************关键词投放新增高级筛选新增查询指标*****************************/
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);

            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);


            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);


            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);


            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);

            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);

            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);

            param.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            param.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);

            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);

            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);

            param.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()) : null);

            //广告销量
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);

            //品牌新买家订单转化率
            param.setBrandNewBuyerOrderConversionRateMin(advancedFilter.hasBrandNewBuyerOrderConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getBrandNewBuyerOrderConversionRateMin()) : null);
            param.setBrandNewBuyerOrderConversionRateMax(advancedFilter.hasBrandNewBuyerOrderConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getBrandNewBuyerOrderConversionRateMax()) : null);

            param.setVideo5SecondViewsMin(advancedFilter.hasVideo5SecondViewsMin() ? advancedFilter.getVideo5SecondViewsMin() : null);
            param.setVideo5SecondViewsMax(advancedFilter.hasVideo5SecondViewsMax() ? advancedFilter.getVideo5SecondViewsMax() : null);
            param.setVideoCompleteViewsMin(advancedFilter.hasVideoCompleteViewsMin() ? advancedFilter.getVideoCompleteViewsMin() : null);
            param.setVideoCompleteViewsMax(advancedFilter.hasVideoCompleteViewsMax() ? advancedFilter.getVideoCompleteViewsMax() : null);
            param.setViewabilityRateMin(advancedFilter.hasViewabilityRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMin())) : null);
            param.setViewabilityRateMax(advancedFilter.hasViewabilityRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMax())) : null);
            param.setViewClickThroughRateMin(advancedFilter.hasViewClickThroughRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMin())) : null);
            param.setViewClickThroughRateMax(advancedFilter.hasViewClickThroughRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMax())) : null);
            param.setBrandedSearchesMin(advancedFilter.hasBrandedSearchesMin() ? advancedFilter.getBrandedSearchesMin() : null);
            param.setBrandedSearchesMax(advancedFilter.hasBrandedSearchesMax() ? advancedFilter.getBrandedSearchesMax() : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            // 搜索结果首页首位IS
            param.setTopImpressionShareMin(advancedFilter.hasTopImpressionShareMin() ? new BigDecimal(String.valueOf(advancedFilter.getTopImpressionShareMin())) : null);
            param.setTopImpressionShareMax(advancedFilter.hasTopImpressionShareMax() ? new BigDecimal(String.valueOf(advancedFilter.getTopImpressionShareMax())) : null);
            param.setSearchFrequencyRankMin(advancedFilter.hasSearchFrequencyRankMin() ? advancedFilter.getSearchFrequencyRankMin() : null);
            param.setSearchFrequencyRankMax(advancedFilter.hasSearchFrequencyRankMax() ? advancedFilter.getSearchFrequencyRankMax() : null);
            param.setWeekRatioMin(advancedFilter.hasWeekRatioMin() ? new BigDecimal(String.valueOf(advancedFilter.getWeekRatioMin())) : null);
            param.setWeekRatioMax(advancedFilter.hasWeekRatioMax() ? new BigDecimal(String.valueOf(advancedFilter.getWeekRatioMax())) : null);
        }

        if (request.hasFilterTargetType()) {
            param.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasBidMin()) {
            param.setBidMin(BigDecimal.valueOf(request.getBidMin()));
        }
        if (request.hasBidMax()) {
            param.setBidMax(BigDecimal.valueOf(request.getBidMax()));
        }
        if (CollectionUtils.isNotEmpty(request.getAdStrategyTypeListList())) {
            param.setAdStrategyTypeList(request.getAdStrategyTypeListList());
        }
        if (CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(param.getType()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN.equals(param.getSearchValue())) {
                param.setSearchValue(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND);
            } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN.equals(param.getSearchValue())) {
                param.setSearchValue(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES);
            }
        }

        if (CampaignTypeEnum.sp.getCampaignType().equalsIgnoreCase(param.getType()) && StringUtils.isNotBlank(param.getSearchValue())) {
            SpKeywordGroupValueEnum keywordGroupValueEnumByTextCn = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByTextCn(param.getSearchValue());
            if (keywordGroupValueEnumByTextCn != null) {
                param.setSearchValue(keywordGroupValueEnumByTextCn.getKeywordText());
            }

        }
    }

    @RateLimit(key = "#request.puid")
    @Override
    public void getAllKeyWordAggregateData(AllKeyWordDataRequest request, StreamObserver<AllKeyWordAggregateDataResponse> responseObserver) {
        log.info("所有类型关键词:{}", request);
        AllKeyWordAggregateDataResponse.Builder builder = AllKeyWordAggregateDataResponse.newBuilder();
        KeywordsPageParam param = new KeywordsPageParam();
        BeanUtils.copyProperties(request, param);
        //参数填充
        fillAllKeyWordParam(request, param);
        //做参数校验
        String err = checkKeywordsPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            try {
                AllKeyWordAggregateDataResponse.AdkeywordHomeRpcVo homeVO = cpcCommonService.getAllKeyWordAggregateData(param.getPuid(), param);
                builder.setData(homeVO);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            } catch (ServiceException e) {
                builder.setMsg(e.getMsg());
                builder.setCode(Int32Value.of(Result.ERROR));
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    // 校验分页查询的参数
    private String checkKeywordsPageParam(KeywordsPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }
        //匹配方式校验
        if (StringUtils.isNotBlank(param.getMatchType())) {
            if (CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(param.getType())) {
                if (StringUtils.isBlank(SbMatchValueEnum.getMatchValue(param.getMatchType()))) {
                    return "请求参数错误";
                }
            } else {
                if (StringUtils.isBlank(MatchValueEnum.getMatchValue(param.getMatchType()))) {
                    return "请求参数错误";
                }
            }

        }
        //搜索字段校验
        if (StringUtils.isNotBlank(param.getSearchField())) {
            KeywordsPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(param.getSearchField(), KeywordsPageParam.SearchFieldEnum.class);
            if (searchFieldEnum == null) {
                return "请求参数错误";
            }
        }
        if (StringUtils.isNotBlank(param.getOrderField())) {
            if (!Constants.isADperformanceOrderField(param.getOrderField())) {
                return "请求参数错误";
            }
        }
        if (StringUtils.isNotBlank(param.getType())) {
            if (!Arrays.asList(Constants.SP, Constants.SB).contains(param.getType())) {
                return "请求参数错误";
            }
        }

        return null;
    }

    /**
     * 所有类型投放
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllTargetData(AllTargetDataRequest request, StreamObserver<AllTargetDataResponse> responseObserver) {
        log.info("所有类型投放", request);
        //做参数校验
        AllTargetDataResponse.Builder builder = AllTargetDataResponse.newBuilder();
        TargetingPageParam param = new TargetingPageParam();
        BeanUtils.copyProperties(request, param);
        //参数填充
        fillAllTargetParam(request, param);
        String err = checkTargetingPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            AllTargetDataResponse.AdTargetingHomeRpcVo homeVo = cpcCommonService.getAllTargetData(param.getPuid(), param);
            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            if (!CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(param.getType())) {
                serversStatusSyncService.sendTarget(request.getPuid().getValue(), request.getShopId().getValue(), homeVo);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 商品投放列表参数填充
     */
    private void fillAllTargetParam(AllTargetDataRequest request, TargetingPageParam param) {
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        param.setChosenTargetType(request.getChosenTargetType());
        param.setSelectType(request.getSelectType());

        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate().replace("-", ""));
        param.setCompareEndDate(request.getCompareEndDate().replace("-", ""));
        param.setOnlyCount(request.getOnlyCount());
        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }

        if (CollectionUtils.isNotEmpty(request.getAdTagIdsList())) {
            param.setAdTagIdList(request.getAdTagIdsList());
        }

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setAdCostPercentageMin(advancedFilter.hasAdCostPercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdCostPercentageMin()) : null);
            param.setAdCostPercentageMax(advancedFilter.hasAdCostPercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdCostPercentageMax()) : null);
            param.setAdSalePercentageMin(advancedFilter.hasAdSalePercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdSalePercentageMin()) : null);
            param.setAdSalePercentageMax(advancedFilter.hasAdSalePercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdSalePercentageMax()) : null);
            param.setAdOrderNumPercentageMin(advancedFilter.hasAdOrderNumPercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdOrderNumPercentageMin()) : null);
            param.setAdOrderNumPercentageMax(advancedFilter.hasAdOrderNumPercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdOrderNumPercentageMax()) : null);
            param.setOrderNumPercentageMin(advancedFilter.hasOrderNumPercentageMin() ? BigDecimal.valueOf(advancedFilter.getOrderNumPercentageMin()) : null);
            param.setOrderNumPercentageMax(advancedFilter.hasOrderNumPercentageMax() ? BigDecimal.valueOf(advancedFilter.getOrderNumPercentageMax()) : null);

            /******************************广告管理高级筛选新增查询指标*******************************/
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);

            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);


            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);


            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);


            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);

            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);

            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);

            param.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            param.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);

            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);

            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);

            param.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()) : null);

            //广告销量
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);

            //品牌买家订单转化率
            param.setBrandNewBuyerOrderConversionRateMin(advancedFilter.hasBrandNewBuyerOrderConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getBrandNewBuyerOrderConversionRateMin()) : null);
            param.setBrandNewBuyerOrderConversionRateMax(advancedFilter.hasBrandNewBuyerOrderConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getBrandNewBuyerOrderConversionRateMax()) : null);

            param.setAddToCartMin(advancedFilter.hasAddToCartMin() ? advancedFilter.getAddToCartMin() : null);
            param.setAddToCartMax(advancedFilter.hasAddToCartMax() ? advancedFilter.getAddToCartMax() : null);
            param.setVideo5SecondViewsMin(advancedFilter.hasVideo5SecondViewsMin() ? advancedFilter.getVideo5SecondViewsMin() : null);
            param.setVideo5SecondViewsMax(advancedFilter.hasVideo5SecondViewsMax() ? advancedFilter.getVideo5SecondViewsMax() : null);
            param.setVideoCompleteViewsMin(advancedFilter.hasVideoCompleteViewsMin() ? advancedFilter.getVideoCompleteViewsMin() : null);
            param.setVideoCompleteViewsMax(advancedFilter.hasVideoCompleteViewsMax() ? advancedFilter.getVideoCompleteViewsMax() : null);
            param.setViewabilityRateMin(advancedFilter.hasViewabilityRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMin())) : null);
            param.setViewabilityRateMax(advancedFilter.hasViewabilityRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMax())) : null);
            param.setViewClickThroughRateMin(advancedFilter.hasViewClickThroughRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMin())) : null);
            param.setViewClickThroughRateMax(advancedFilter.hasViewClickThroughRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMax())) : null);
            param.setBrandedSearchesMin(advancedFilter.hasBrandedSearchesMin() ? advancedFilter.getBrandedSearchesMin() : null);
            param.setBrandedSearchesMax(advancedFilter.hasBrandedSearchesMax() ? advancedFilter.getBrandedSearchesMax() : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            // 搜索结果首页首位IS
            param.setTopImpressionShareMin(advancedFilter.hasTopImpressionShareMin() ? new BigDecimal(String.valueOf(advancedFilter.getTopImpressionShareMin())) : null);
            param.setTopImpressionShareMax(advancedFilter.hasTopImpressionShareMax() ? new BigDecimal(String.valueOf(advancedFilter.getTopImpressionShareMax())) : null);


        }

        if (request.hasFilterTargetType()) {
            param.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasBidMin()) {
            param.setBidMin(BigDecimal.valueOf(request.getBidMin()));
        }
        if (request.hasBidMax()) {
            param.setBidMax(BigDecimal.valueOf(request.getBidMax()));
        }
        if(CollectionUtils.isNotEmpty(request.getAdStrategyTypeListList())){
            param.setAdStrategyTypeList(request.getAdStrategyTypeListList());
        }
    }


    @RateLimit(key = "#request.puid")
    @Override
    public void getAllTargetAggregateData(AllTargetDataRequest request, StreamObserver<AllTargetAggregateDataResponse> responseObserver) {
        log.info("所有类型投放:{}", request);
        //做参数校验
        AllTargetAggregateDataResponse.Builder builder = AllTargetAggregateDataResponse.newBuilder();

        TargetingPageParam param = new TargetingPageParam();
        BeanUtils.copyProperties(request, param);
        // 参数填充
        fillAllTargetParam(request, param);
        String err = checkTargetingPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");

        } else {
            AllTargetAggregateDataResponse.AdTargetingHomeRpcVo homeVo = cpcCommonService.getAllTargetAggregateData(param.getPuid(), param);
            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    private String checkTargetingPageParam(TargetingPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }

        if (StringUtils.isNotBlank(param.getOrderField())) {
            if (!Constants.isADperformanceOrderField(param.getOrderField())) {
                return "请求参数错误";
            }
            // 商品投放不支持按aba排名以及周变化率进行排序
            if ("searchFrequencyRank".equals(param.getOrderField()) || "weekRatio".equals(param.getOrderField())) {
                param.setOrderField(null);
            }
        }

        return null;
    }


    /**
     * 所有类型同步广告活动信息
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void syncCampaigns(SyncCampaignsRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("所有类型同步广告活动信息 = {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasPuid() || !request.hasShopId() || CollectionUtils.isEmpty(request.getCampaignIdList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            Result<String> result = cpcCampaignService.syncBasicInfo(request.getPuid().getValue(), request.getShopId().getValue(), request.getCampaignIdList());
            if (result.getCode() == Result.SUCCESS) {
                builder.setCode(Int32Value.of(Result.SUCCESS));
                builder.setMsg(result.getMsg());
                builder.setData(result.getData());
            } else {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    @Async
    public void syncAmazonAdByShopId(SyncAmazonAdByShopIdRequest request, StreamObserver<SyncAmazonAdByShopIdResponse> responseObserver) {
        SyncAmazonAdByShopIdResponse.Builder builder = SyncAmazonAdByShopIdResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        }
        String cacheKey = String.format(RedisConstant.SELLFOX_SYNC_AMAZON_AD_SHOP_FORMAT, request.getPuid().getValue(), request.getShopId().getValue());
        try {
            String value = redisService.getString(cacheKey);
            if (StringUtils.isNotBlank(value)) {
                ProcessMsg pm = new ProcessMsg(1, 0, "同步成功");
                stringRedisService.set(request.getUuid(), pm);
                builder.setMsg("每小时只能同步一次");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }
        } catch (Exception e) {
            log.error("syncAmazonAdByShopId redis get value fail");
        }

        // 控制同步周期，限制1小时内同步一次
        AdSyncRecordEntity adSyncRecordEntity = iAdSyncRecord.getLastOne(request.getPuid().getValue(), request.getShopId().getValue(), AdSyncRecord.AdTypeEnum.SHOP.getType(), AdSyncRecord.TriggerChannelEnum.USER_TRIGGER.getChannel());
        if (adSyncRecordEntity != null && System.currentTimeMillis() / 1000 - adSyncRecordEntity.getStartSyncTime() < 3600) {
            ProcessMsg pm = new ProcessMsg(1, 0, "同步成功");
            stringRedisService.set(request.getUuid(), pm);
            builder.setMsg("每小时只能同步一次");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        try {
            redisService.set(cacheKey, System.currentTimeMillis() / 1000, 3600);
        } catch (Exception e) {
            log.error("syncAmazonAdByShopId redis set value fail");
        }
        iCpcCampaignService.syncAmazonAdByShopId(request.getPuid().getValue(), request.getShopId().getValue(), request.getUuid());
        builder.setCode(Int32Value.of(Result.SUCCESS));
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    /**
     * 查询超预算数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getCampaignBeyondBudget(GetCampaignBeyondBudgetRequest request, StreamObserver<CampaignBeyondBudgetResponse> responseObserver) {
        log.info("查询超预算图标数据 request {}", request);
        CampaignBeyondBudgetResponse.Builder builder = CampaignBeyondBudgetResponse.newBuilder();
        //检查参数
        if (StringUtils.isNotBlank(request.getCampaignType())) {
            if (!Arrays.asList(Constants.SP, Constants.SB).contains(request.getCampaignType())) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            }
        }


        if (!request.hasShopId() || !request.hasPuid() || !request.hasCampaignId() || StringUtils.isBlank(request.getCampaignType())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {

            AdCampaignChangeHistoryParam param = new AdCampaignChangeHistoryParam();
            param.setPuid(request.getPuid().getValue());
            param.setType(request.getCampaignType());
            param.setCampaignId(request.getCampaignId());
            param.setShopId(request.getShopId().getValue());
            if (StringUtils.isBlank(request.getStartDate()) || StringUtils.isBlank(request.getEndDate())) {
                param.setStartDate(LocalDateTime.now().minusDays(7).withHour(0).withMinute(0).withSecond(0).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATE_TIME)));
                param.setEndDate(LocalDateTime.now().withHour(23).withMinute(59).withSecond(59).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATE_TIME)));
            } else {
                param.setStartDate(request.getStartDate());
                param.setEndDate(request.getEndDate());
            }
            List<CampaignBeyondBudgetvo> campaignBeyondBudget = cpcCommonService.getCampaignBeyondBudget(param);
            CampaignBeyondBudgetResponse.CampaignBeyondBudgetHomevo.Builder builder1 = CampaignBeyondBudgetResponse.CampaignBeyondBudgetHomevo.newBuilder();
            if (campaignBeyondBudget != null) {
                builder1.addAllVos(campaignBeyondBudget);
            }
            builder.setData(builder1.build());
            builder.setCode(Int32Value.of(Result.SUCCESS));


        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    /**
     * 查询超预算数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getCampaignBeyondBudgetHour(GetCampaignBeyondBudgetRequest request, StreamObserver<CampaignBeyondBudgetHourResponse> responseObserver) {
        log.info("查询超预算小时数据 request {}", request);
        CampaignBeyondBudgetHourResponse.Builder builder = CampaignBeyondBudgetHourResponse.newBuilder();
        //检查参数
        if (StringUtils.isNotBlank(request.getCampaignType())) {
            if (!Arrays.asList(Constants.SP, Constants.SB).contains(request.getCampaignType())) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            }
        }


        if (!request.hasShopId() || !request.hasPuid() || !request.hasCampaignId() || StringUtils.isBlank(request.getCampaignType())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {

            AdCampaignChangeHistoryParam param = new AdCampaignChangeHistoryParam();
            param.setPuid(request.getPuid().getValue());
            param.setType(request.getCampaignType());
            param.setCampaignId(request.getCampaignId());
            param.setShopId(request.getShopId().getValue());
            if (StringUtils.isBlank(request.getStartDate()) || StringUtils.isBlank(request.getEndDate())) {
                param.setStartDate(LocalDateTime.now().minusDays(7).withHour(0).withMinute(0).withSecond(0).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATE_TIME)));
                param.setEndDate(LocalDateTime.now().withHour(23).withMinute(59).withSecond(59).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATE_TIME)));
            } else {
                param.setStartDate(request.getStartDate());
                param.setEndDate(request.getEndDate());
            }
            List<CampaignBeyondBudgetHourVo> campaignBeyondBudget = cpcCommonService.getCampaignBeyondBudgetHour(param);
            CampaignBeyondBudgetHourResponse.CampaignBeyondBudgetHomeVo.Builder builder1 = CampaignBeyondBudgetHourResponse.CampaignBeyondBudgetHomeVo.newBuilder();
            List<String> asins = cpcCommonService.getAsinByCampaignId(request.getPuid().getValue(), request.getShopId().getValue(), request.getCampaignId(), request.getCampaignType());
            if (campaignBeyondBudget != null) {
                builder1.addAllVos(campaignBeyondBudget);
            }
            if (asins != null) {
                asins = asins.stream().filter(Objects::nonNull).collect(Collectors.toList());
                builder1.addAllAsins(asins);
            }
            builder.setData(builder1.build());
            builder.setCode(Int32Value.of(Result.SUCCESS));


        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 查询超预算数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getCampaignBeyondBudgetPage(GetCampaignBeyondBudgetRequest request, StreamObserver<CampaignBeyondBudgetPageResponse> responseObserver) {
        log.info("查询超预算分页数据 request {}", request);
        CampaignBeyondBudgetPageResponse.Builder builder = CampaignBeyondBudgetPageResponse.newBuilder();
        //检查参数
        if (StringUtils.isNotBlank(request.getCampaignType())) {
            if (!Arrays.asList(Constants.SP, Constants.SB).contains(request.getCampaignType())) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }
        }


        if (!request.hasShopId() || !request.hasPuid() || !request.hasCampaignId() || StringUtils.isBlank(request.getCampaignType())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {

            AdCampaignChangeHistoryParam param = new AdCampaignChangeHistoryParam();
            param.setPuid(request.getPuid().getValue());
            param.setType(request.getCampaignType());
            param.setCampaignId(request.getCampaignId());
            param.setShopId(request.getShopId().getValue());
            if (request.hasPageNo()) {
                param.setPageNo(request.getPageNo().getValue());
            }
            if (request.hasPageSize()) {
                param.setPageSize(request.getPageSize().getValue());
            }
            if (StringUtils.isBlank(request.getStartDate()) || StringUtils.isBlank(request.getEndDate())) {
                param.setStartDate(LocalDateTime.now().minusDays(7).withHour(0).withMinute(0).withSecond(0).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATE_TIME)));
                param.setEndDate(LocalDateTime.now().withHour(23).withMinute(59).withSecond(59).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATE_TIME)));
            } else {
                param.setStartDate(request.getStartDate());
                param.setEndDate(request.getEndDate());
            }
            CampaignBeyondBudgetPageResponse campaignBeyondBudgetPage = cpcCommonService.getCampaignBeyondBudgetPage(param);
            responseObserver.onNext(campaignBeyondBudgetPage);
            responseObserver.onCompleted();
            return;

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 查询超预算数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAsinsByCampaignId(GetAsinByCampaignIdRequest request, StreamObserver<GetAsinByCampaignIdResponse> responseObserver) {
        log.info("查询广告活动asin数据 request {}", request);
        GetAsinByCampaignIdResponse.Builder builder = GetAsinByCampaignIdResponse.newBuilder();
        //检查参数
        if (StringUtils.isNotBlank(request.getCampaignType())) {
            if (!Arrays.asList(Constants.SP, Constants.SB, Constants.SD).contains(request.getCampaignType())) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }
        }


        if (!request.hasShopId() || !request.hasPuid() || !request.hasCampaignId() || StringUtils.isBlank(request.getCampaignType())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            List<String> asins = cpcCommonService.getAsinByCampaignId(request.getPuid().getValue(), request.getShopId().getValue(), request.getCampaignId(), request.getCampaignType());
            List<String> keywords = cpcCommonService.getKeywordByCampaignId(request.getPuid().getValue(), request.getShopId().getValue(), request.getCampaignId(), request.getCampaignType());
            GetAsinByCampaignIdResponse.AsinsByCampaignId.Builder b = GetAsinByCampaignIdResponse.AsinsByCampaignId.newBuilder();
            if (asins != null) {
                asins = asins.stream().filter(Objects::nonNull).collect(Collectors.toList());
                b.addAllAsins(asins);
            }
            if (keywords != null) {
                keywords = keywords.stream().filter(Objects::nonNull).collect(Collectors.toList());
                b.addAllKeywords(keywords);
            }

            builder.setData(b.build());
            builder.setCode(Int32Value.of(Result.SUCCESS));

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    /**
     * 查询广告活动和广告组信息
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllTypeCampAndGroupPage(AllTypeCampAndGroupPageRequest request, StreamObserver<AllTypeCampAndGroupPageResponse> responseObserver) {
        log.info("查询广告活动和广告组信息 request {}", request);
        AllTypeCampAndGroupPageResponse.Builder builder = AllTypeCampAndGroupPageResponse.newBuilder();
        //检查参数
        if (StringUtils.isBlank(request.getType())) {
            AllTypeCampAndGroupPageResponse.Page.Builder pageBuilder = AllTypeCampAndGroupPageResponse.Page.newBuilder();
            pageBuilder.setPageNo(Int32Value.of(1));
            pageBuilder.setPageSize(Int32Value.of(20));
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setData(pageBuilder.build());
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        String err = checkParams(request);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(err);
        } else {

            Integer pageNo = 1;
            Integer pageSize = 20;
            if (request.hasPageNo()) {
                pageNo = request.getPageNo();
            }
            if (request.hasPageSize()) {
                pageSize = request.getPageSize();
            }
            CampaignAndGroupPageListParam param = CampaignAndGroupPageListParam.builder()
                    .pageSize(pageSize)
                    .pageNo(pageNo)
                    .shopId(request.getShopId().getValue())
                    .puid(request.getPuid().getValue())
                    .itemType(request.getItemType())
                    .modular(request.getModular())
                    .type(request.getType()).build();
            if (StringUtils.isNotBlank(request.getGroupIds())) {
                param.setGroupIds(request.getGroupIds());
            }
            if (StringUtils.isNotBlank(request.getCampaignIds())) {
                param.setCampaignIds(request.getCampaignIds());
            }
            if (StringUtils.isNotBlank(request.getSearchValue())) {
                param.setSearchValue(request.getSearchValue());
            }
            if (StringUtils.isNotBlank(request.getPortfolioId())) {
                param.setPortfolioId(request.getPortfolioId());
            }
            Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> voPage = cpcCommonService.getAllTypeCampAndGroupPage(param);
            AllTypeCampAndGroupPageResponse.Page.Builder pageBuilder = AllTypeCampAndGroupPageResponse.Page.newBuilder();
            pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
            pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
            pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
            pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
            if (voPage.getRows() != null) {
                pageBuilder.addAllRows(voPage.getRows());
            }
            builder.setData(pageBuilder);
            builder.setCode(Int32Value.of(Result.SUCCESS));

        }

        responseObserver.onNext(builder.build());

        responseObserver.onCompleted();

    }

    @Override
    public void getAllSortedTypeCampAndGroupPage(AllTypeCampAndGroupPageRequest request, StreamObserver<AllTypeCampAndGroupPageResponse> responseObserver) {
        log.info("查询排序后的广告活动和广告组信息 request {}", request);
        AllTypeCampAndGroupPageResponse.Builder builder = AllTypeCampAndGroupPageResponse.newBuilder();
        //检查参数
        if (StringUtils.isBlank(request.getType())) {
            AllTypeCampAndGroupPageResponse.Page.Builder pageBuilder = AllTypeCampAndGroupPageResponse.Page.newBuilder();
            pageBuilder.setPageNo(Int32Value.of(1));
            pageBuilder.setPageSize(Int32Value.of(20));
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setData(pageBuilder.build());
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        String err = checkParamsNew(request);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(err);
        } else {

            Integer pageNo = 1;
            Integer pageSize = 20;
            if (request.hasPageNo()) {
                pageNo = request.getPageNo();
            }
            if (request.hasPageSize()) {
                pageSize = request.getPageSize();
            }
            CampaignAndGroupPageListParam param = CampaignAndGroupPageListParam.builder()
                    .pageSize(pageSize)
                    .pageNo(pageNo)
                    .shopId(request.getShopId().getValue())
                    .puid(request.getPuid().getValue())
                    .itemType(request.getItemType())
                    .modular(request.getModular())
                    .type(request.getType())
                    .orderBy(request.getOrderBy())
                    .orderByField(request.getOrderByField())
                    .excludeState(request.getExcludeState())
                    .targetingType(request.getTargetingType())
                    .build();
            if (StringUtils.isNotBlank(request.getGroupIds())) {
                param.setGroupIds(request.getGroupIds());
            }
            if (StringUtils.isNotBlank(request.getCampaignIds())) {
                param.setCampaignIds(request.getCampaignIds());
            }
            if (StringUtils.isNotBlank(request.getSearchValue())) {
                param.setSearchValue(request.getSearchValue());
            }
            if (StringUtils.isNotBlank(request.getPortfolioId())) {
                param.setPortfolioId(request.getPortfolioId());
            }
            if (request.hasQuerySb2Campaign()) {
                param.setIsMultiAdGroupsEnabled(request.getQuerySb2Campaign());
            }
            Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> voPage = cpcCommonService.getAllSortedTypeCampAndGroupPage(param);
            AllTypeCampAndGroupPageResponse.Page.Builder pageBuilder = AllTypeCampAndGroupPageResponse.Page.newBuilder();
            pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
            pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
            pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
            pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
            if (voPage.getRows() != null) {
                pageBuilder.addAllRows(voPage.getRows());
            }
            builder.setData(pageBuilder);
            builder.setCode(Int32Value.of(Result.SUCCESS));

        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAllSortedTypeCampAndGroupPageNew(AllTypeCampAndGroupPageRequest request, StreamObserver<AllTypeCampAndGroupPageResponse> responseObserver) {
        log.info("查询排序后的广告活动和广告组信息 request {}", request);
        AllTypeCampAndGroupPageResponse.Builder builder = AllTypeCampAndGroupPageResponse.newBuilder();
        //检查参数
        if (StringUtils.isBlank(request.getType())) {
            AllTypeCampAndGroupPageResponse.Page.Builder pageBuilder = AllTypeCampAndGroupPageResponse.Page.newBuilder();
            pageBuilder.setPageNo(Int32Value.of(1));
            pageBuilder.setPageSize(Int32Value.of(20));
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setData(pageBuilder.build());
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        String err = checkParamsNew(request);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(err);
        } else {

            Integer pageNo = 1;
            Integer pageSize = 20;
            if (request.hasPageNo()) {
                pageNo = request.getPageNo();
            }
            if (request.hasPageSize()) {
                pageSize = request.getPageSize();
            }
            CampaignAndGroupPageListParam param = CampaignAndGroupPageListParam.builder()
                    .pageSize(pageSize)
                    .pageNo(pageNo)
                    .shopId(request.getShopId().getValue())
                    .puid(request.getPuid().getValue())
                    .itemType(request.getItemType())
                    .modular(request.getModular())
                    .type(request.getType())
                    .orderBy(request.getOrderBy())
                    .orderByField(request.getOrderByField())
                    .excludeState(request.getExcludeState())
                    .targetingType(request.getTargetingType())
                    .build();
            if (StringUtils.isNotBlank(request.getGroupIds())) {
                param.setGroupIds(request.getGroupIds());
            }
            if (StringUtils.isNotBlank(request.getCampaignIds())) {
                param.setCampaignIds(request.getCampaignIds());
            }
            if (StringUtils.isNotBlank(request.getSearchValue())) {
                param.setSearchValue(request.getSearchValue());
            }
            if (StringUtils.isNotBlank(request.getPortfolioId())) {
                param.setPortfolioId(request.getPortfolioId());
            }
            if (request.hasQuerySb2Campaign()) {
                param.setIsMultiAdGroupsEnabled(request.getQuerySb2Campaign());
            }
            Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> voPage = cpcCommonService.getAllSortedTypeCampAndGroupPageNew(param);
            AllTypeCampAndGroupPageResponse.Page.Builder pageBuilder = AllTypeCampAndGroupPageResponse.Page.newBuilder();
            pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
            pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
            pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
            pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
            if (voPage.getRows() != null) {
                pageBuilder.addAllRows(voPage.getRows());
            }
            builder.setData(pageBuilder);
            builder.setCode(Int32Value.of(Result.SUCCESS));

        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 多店铺查询广告活动和广告组信息
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllTypeCampaignAndGroupPage(AllTypeCampaignAndGroupPageRequest request, StreamObserver<AllTypeCampaignAndGroupPageResponse> responseObserver) {
        log.info("多店铺查询广告活动和广告组信息 request {}", request);
        AllTypeCampaignAndGroupPageResponse.Builder builder = AllTypeCampaignAndGroupPageResponse.newBuilder();
        //检查参数
        if (StringUtils.isBlank(request.getType())) {
            AllTypeCampaignAndGroupPageResponse.Page.Builder pageBuilder = AllTypeCampaignAndGroupPageResponse.Page.newBuilder();
            pageBuilder.setPageNo(Int32Value.of(1));
            pageBuilder.setPageSize(Int32Value.of(20));
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setData(pageBuilder.build());
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        String err = checkParams(request);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(err);
        } else {

            Integer pageNo = 1;
            Integer pageSize = 20;
            if (request.hasPageNo()) {
                pageNo = request.getPageNo();
            }
            if (request.hasPageSize()) {
                pageSize = request.getPageSize();
            }
            CampaignAndGroupPageParam param = CampaignAndGroupPageParam.builder()
                    .pageSize(pageSize)
                    .pageNo(pageNo)
                    .shopId(request.getShopId())
                    .puid(request.getPuid().getValue())
                    .itemType(request.getItemType())
                    .modular(request.getModular())
                    .type(request.getType()).build();
            if (StringUtils.isNotBlank(request.getGroupIds())) {
                param.setGroupIds(request.getGroupIds());
            }
            if (StringUtils.isNotBlank(request.getCampaignIds())) {
                param.setCampaignIds(request.getCampaignIds());
            }
            if (StringUtils.isNotBlank(request.getSearchValue())) {
                param.setSearchValue(request.getSearchValue());
            }
            if (StringUtils.isNotBlank(request.getPortfolioId())) {
                param.setPortfolioId(request.getPortfolioId());
            }
            Page<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> voPage = cpcCommonService.getAllTypeCampaignAndGroupPage(param);
            AllTypeCampaignAndGroupPageResponse.Page.Builder pageBuilder = AllTypeCampaignAndGroupPageResponse.Page.newBuilder();
            pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
            pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
            pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
            pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
            if (voPage.getRows() != null) {
                pageBuilder.addAllRows(voPage.getRows());
            }
            builder.setData(pageBuilder);
            builder.setCode(Int32Value.of(Result.SUCCESS));

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void operationLogPageList(OperationLogRequst request, StreamObserver<OperationLogPageResponse> responseObserver) {
        log.info("查询广告日志列表 request {}", request);
        OperationLogPageResponse.Builder builder = OperationLogPageResponse.newBuilder();
        OperationLogQo dto = new OperationLogQo();
        BeanUtils.copyProperties(request, dto);
        int puid = request.getPuid().getValue();
        int shopId = request.getShopId().getValue();
        dto.setPuid(puid);
        dto.setShopId(shopId);
        dto.setPageNo(request.getPageNo().getValue());
        dto.setPageSize(request.getPageSize().getValue());

        if (request.hasPortfolioId()) {
            dto.setPortfolioId(request.getPortfolioId());
        }
        if (request.hasTemplateId()) {
            dto.setTemplateId(request.getTemplateId().getValue());
        }

        if (request.hasProductSearchQueryType()) {
            dto.setProductSearchQueryType(request.getProductSearchQueryType());
        }
        if (request.hasProductSearchType()) {
            dto.setProductSearchType(request.getProductSearchType());
        }
        if (request.hasProductSearchValue()) {
            dto.setProductSearchValue(request.getProductSearchValue());
        }

        if (StringUtils.isNotBlank(request.getPortfolioIds())) {
            List<String> portfolioIdList = Arrays.stream(request.getPortfolioIds().split(",")).collect(Collectors.toList());
            dto.setPortfolioIdList(portfolioIdList);
            List<String> campaignIdList;
            if (StringUtils.isNotBlank(request.getAdType())) {
                campaignIdList = amazonAdCampaignAllDao.getCampaignIdsAllByPortfolioId(puid, shopId, portfolioIdList,
                        Collections.singletonList(request.getAdType()));
            } else {
                campaignIdList = amazonAdCampaignAllDao.getCampaignIdListByPortfolioIdNoType(puid, shopId, portfolioIdList);
            }
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                dto.getPortfolioCampaignIds().addAll(campaignIdList);
            }
            if (CollectionUtils.isNotEmpty(dto.getPortfolioIdList())) {
                dto.getPortfolioIdList().removeIf("-1"::equals);
            }
        }
        if (CollectionUtils.isNotEmpty(request.getOperationObjectsList())) {
            dto.setOperationObjectList(Lists.newArrayList(request.getOperationObjectsList()));
        }
        dto.setUserIds(request.getUserIdList());

        //做参数校验
        if (!request.hasShopId() || checkOperationLongDto(dto)) {
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setMsg("请求参数错误");
        } else {

            OperationLogPageResponse.Page page = cpcCommonService.operationLogPageList(dto);

            builder.setData(page);
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void operationLogListExport(OperationLogRequst request, StreamObserver<CommonResponse> responseObserver) {
        log.info("导出广告日志列表 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        OperationLogQo dto = new OperationLogQo();
        BeanUtils.copyProperties(request, dto);
        int puid = request.getPuid().getValue();
        int shopId = request.getShopId().getValue();
        dto.setPuid(puid);
        dto.setUid(request.getUid().getValue());
        dto.setShopId(shopId);

        if (request.hasPortfolioId()) {
            dto.setPortfolioId(request.getPortfolioId());
        }
        if (request.hasTemplateId()) {
            dto.setTemplateId(request.getTemplateId().getValue());
        }
        if (StringUtils.isNotBlank(request.getUuid())) {
            dto.setUuid(request.getUuid());
        }

        if (StringUtils.isNotBlank(request.getPortfolioIds())) {
            List<String> portfolioIdList = Arrays.stream(request.getPortfolioIds().split(",")).collect(Collectors.toList());
            dto.setPortfolioIdList(portfolioIdList);
            List<String> campaignIdList;
            if (StringUtils.isNotBlank(request.getAdType())) {
                campaignIdList = amazonAdCampaignAllDao.getCampaignIdsAllByPortfolioId(puid, shopId, portfolioIdList,
                        Collections.singletonList(request.getAdType()));
            } else {
                campaignIdList = amazonAdCampaignAllDao.getCampaignIdListByPortfolioIdNoType(puid, shopId, portfolioIdList);
            }
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                dto.getPortfolioCampaignIds().addAll(campaignIdList);
            }
            if (CollectionUtils.isNotEmpty(dto.getPortfolioIdList())) {
                dto.getPortfolioIdList().removeIf("-1"::equals);
            }
        }
        if (CollectionUtils.isNotEmpty(request.getOperationObjectsList())) {
            dto.setOperationObjectList(Lists.newArrayList(request.getOperationObjectsList()));
        }
        dto.setUserIds(request.getUserIdList());

        //做参数校验
        if (!request.hasShopId() || checkOperationLongDto(dto)) {
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setMsg("请求参数错误");
        } else {
            AdManagePageExportTaskTypeEnum taskTypeEnum = null;
            if (OperationLogFromEnum.AMAZON.getOperationType().equals(request.getFrom())) {
                taskTypeEnum = AdManagePageExportTaskTypeEnum.AMAZON_LOG;
            } else if (OperationLogFromEnum.SELLFOX.getOperationType().equals(request.getFrom())) {
                taskTypeEnum = AdManagePageExportTaskTypeEnum.SELLFOX_LOG;
            }

            if (taskTypeEnum == null) {
                builder.setCode(Int32Value.of(Result.SUCCESS));
                builder.setMsg("请求参数错误");
            } else {
                dto.setPageNo(1);
                dto.setPageSize(Constants.ES_EXPORT_MAX_SIZE);
                //插入任务
                Long id = adManagePageExportTaskService.saveExportTask(dto.getPuid(), dto.getUid(), dto.getShopId(), taskTypeEnum,
                        DateUtil.dateStringFormat(dto.getStart(), DateUtil.PATTERN, DateUtil.PATTERN_YYYYMMDD),
                        DateUtil.dateStringFormat(dto.getEnd(), DateUtil.PATTERN, DateUtil.PATTERN_YYYYMMDD), dto);
                if (id == null) {
                    builder.setCode(Int32Value.of(Result.ERROR));
                    builder.setMsg("新建任务异常，请联系管理员");
                } else {
                    builder.setCode(Int32Value.of(Result.SUCCESS));
                }
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void operationLogDayList(OperationLogRequst request, StreamObserver<OperationLogDayListResponse> responseObserver) {
        log.info("查询广告日志天维度汇总 request {}", request);
        OperationLogDayListResponse.Builder builder = OperationLogDayListResponse.newBuilder();
        OperationLogQo dto = new OperationLogQo();
        BeanUtils.copyProperties(request, dto);
        dto.setPuid(request.getPuid().getValue());
        dto.setShopId(request.getShopId().getValue());

        if (request.hasTemplateId()) {
            dto.setTemplateId(request.getTemplateId().getValue());
        }


        //做参数校验
        if (!request.hasShopId() || checkOperationLongDto(dto)) {
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setMsg("请求参数错误");
        } else {

            RowData rowData = cpcCommonService.operationLogDayList(dto);
            builder.setData(rowData);
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAllCampaignOrGroupAggregateAndOperationData(AllCampaignOrGroupAggregateAndOperationRequest request, StreamObserver<AllCampaignOrGroupAggregateDataAndOperationResponse> responseObserver) {
        log.info("查询所有类型广告组信息 request {}", request);
        AllCampaignOrGroupAggregateDataAndOperationResponse.Builder builder = AllCampaignOrGroupAggregateDataAndOperationResponse.newBuilder();
        CampaignOrGroupParam param = new CampaignOrGroupParam();
        BeanUtils.copyProperties(request, param);
        int puid = request.getPuid().getValue();
        int shopId = request.getShopId().getValue();
        param.setPuid(puid);
        param.setShopId(shopId);
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        param.setSearchDataType(request.getSearchDataType().getValue());
        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate().replace("-", ""));
        param.setCompareEndDate(request.getCompareEndDate().replace("-", ""));


        if (!request.hasShopId() || param.getDxmCampaignId() != null) {
            param.setDxmCampaignId(request.getDxmCampaignId().getValue());
        }

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMin()), BigDecimal.valueOf(100)) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMax()), BigDecimal.valueOf(100)) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMin()), BigDecimal.valueOf(100)) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMax()), BigDecimal.valueOf(100)) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()), BigDecimal.valueOf(100)) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()), BigDecimal.valueOf(100)) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMin()), BigDecimal.valueOf(100)) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMax()), BigDecimal.valueOf(100)) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMin()), BigDecimal.valueOf(100)) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMax()), BigDecimal.valueOf(100)) : null);
        }

        if (request.hasFilterTargetType()) {
            param.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasBidMin()) {
            param.setBidMin(BigDecimal.valueOf(request.getBidMin()));
        }
        if (request.hasBidMax()) {
            param.setBidMax(BigDecimal.valueOf(request.getBidMax()));
        }

        if (request.hasTargetType()) {
            param.setTargetType(request.getTargetType());
        }
        if (request.hasAdjustmentRange()) {
            param.setAdjustmentRange(request.getAdjustmentRange());
        }
        if (request.hasTemplateId()) {
            param.setTemplateId(request.getTemplateId().getValue());
        }

        if (StringUtils.isNotBlank(request.getPortfolioIds())) {
            List<String> portfolioIdList = Arrays.stream(request.getPortfolioIds().split(",")).collect(Collectors.toList());
            param.setPortfolioIdList(portfolioIdList);
            List<String> campaignIdList;
            if (StringUtils.isNotBlank(request.getAdType())) {
                campaignIdList = amazonAdCampaignAllDao.getCampaignIdsAllByPortfolioId(puid, shopId, portfolioIdList,
                        Collections.singletonList(request.getAdType()));
            } else {
                campaignIdList = amazonAdCampaignAllDao.getCampaignIdListByPortfolioIdNoType(puid, shopId, portfolioIdList);
            }
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                param.getPortfolioCampaignIds().addAll(campaignIdList);
            }
            if (CollectionUtils.isNotEmpty(param.getPortfolioIdList())) {
                param.getPortfolioIdList().removeIf("-1"::equals);
            }
        }

        if (CollectionUtils.isNotEmpty(request.getOperationObjectsList())) {
            param.setOperationObjectList(Lists.newArrayList(request.getOperationObjectsList()));
        }
        param.setUserIds(request.getUserIdList());

        //做参数校验
        String err = checkGroupOrCampaignParam(param);
        if (StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(err);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            AllCampaignOrGroupAggregateDataAndOperationResponse.CampaignOrGroup allGroupOrCampaignAggregateData = cpcCommonService.getAllGroupOrCampaignAggregateData(param.getPuid(), param);

            builder.setData(allGroupOrCampaignAggregateData);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    // 校验分页查询的参数
    private String checkGroupOrCampaignParam(CampaignOrGroupParam param) {
        if (param == null || param.getShopId() == null || param.getPuid() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEnd(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
            param.setStart(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
        } else {
            param.setEnd(param.getEndDate());
            param.setStart(param.getStartDate());
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }


        if (param.getOperationObject() != null) {
            String operationLogTargetEnumValue = OperationLogTargetEnum.getOperationLogTargetEnumValue(param.getOperationObject());
            if (StringUtils.isNotBlank(operationLogTargetEnumValue)) {
                param.setOperationObject(operationLogTargetEnumValue);
            }
        }
        if (CollectionUtils.isNotEmpty(param.getOperationObjectList())) {
            List<String> operationObjectList = param.getOperationObjectList().stream().map(i -> {
                String operationLogTargetEnumValue = OperationLogTargetEnum.getOperationLogTargetEnumValue(i);
                return StringUtils.isNotBlank(operationLogTargetEnumValue) ? operationLogTargetEnumValue : i;
            }).collect(Collectors.toList());
            param.setOperationObjectList(operationObjectList);
        }
        if (param.getOperationType() != null) {
            String operationValue = OperationLogActionEnum.getOperationValue(param.getOperationType().toLowerCase());
            if (StringUtils.isNotBlank(operationValue)) {
                param.setOperationType(operationValue);
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField())) {
            GroupPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(param.getSearchField(), GroupPageParam.SearchFieldEnum.class);
            if (searchFieldEnum == null) {
                return "请求参数错误";
            }
        }

        if (StringUtils.isNotBlank(param.getOrderField()) && !Constants.isADperformanceOrderField(param.getOrderField())) {
            return "请求参数错误";
        }
        if (StringUtils.isNotBlank(param.getOrderField()) && !Constants.isADperformanceOrderField(param.getOrderField())) {
            return "请求参数错误";
        }

        return null;
    }

    private boolean checkOperationLongDto(OperationLogQo dto) {
        if (!CheckParamUtil.checkRequired(dto, false, "puid,shopId,start,end")) {
            return true;
        }
        if (dto.getOperationObject() != null) {
            String operationLogTargetEnumValue = OperationLogTargetEnum.getOperationLogTargetEnumValue(dto.getOperationObject());
            if (StringUtils.isNotBlank(operationLogTargetEnumValue)) {
                dto.setOperationObject(operationLogTargetEnumValue);
            }
        }
        if (CollectionUtils.isNotEmpty(dto.getOperationObjectList())) {
            List<String> operationObjectList = dto.getOperationObjectList().stream().map(i -> {
                String operationLogTargetEnumValue = OperationLogTargetEnum.getOperationLogTargetEnumValue(i);
                return StringUtils.isNotBlank(operationLogTargetEnumValue) ? operationLogTargetEnumValue : i;
            }).collect(Collectors.toList());
            dto.setOperationObjectList(operationObjectList);
        }
        if (dto.getOperationType() != null) {
            String operationValue = OperationLogActionEnum.getOperationValue(dto.getOperationType().toLowerCase());
            if (StringUtils.isNotBlank(operationValue)) {
                dto.setOperationType(operationValue);
            }
        }
        return false;
    }

    /**
     * 批量更新
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void batchUpdateCampaign(BatchUpdateCampaignRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-更新活动 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        List<BatchUpdateCampaignVo> vosList = request.getVosList();
        if (!request.hasType() || CollectionUtils.isEmpty(vosList) || !request.hasPuid() || !request.hasUid() || !request.hasShopId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            Integer puid = request.getPuid().getValue();
            Integer shopId = request.getShopId().getValue();
            Integer uid = request.getUid().getValue();
            String ip = request.getLoginIp();
            String type = request.getType();
            List<BatchCampaignVo> updateList = Lists.newArrayListWithCapacity(vosList.size());
            for (BatchUpdateCampaignVo vo : vosList) {
                BatchCampaignVo.BatchCampaignVoBuilder campaignVoBuilder = BatchCampaignVo.builder();
                campaignVoBuilder.puid(puid);
                campaignVoBuilder.shopId(shopId);
                campaignVoBuilder.uid(uid);

                if (StringUtils.isNotBlank(ip)) {
                    campaignVoBuilder.loginIp(ip);
                }
                if (vo.hasDailyBudget()) {
                    campaignVoBuilder.dailyBudget(vo.getDailyBudget().getValue());
                }

                BatchUpdateCampaignVo.CampaignState state = vo.getState();
                if (state != null) {
                    CpcStatusEnum statusEnum = CpcStatusEnum.valueOf(state.getValueDescriptor().getName());
                    if (statusEnum != null) {
                        campaignVoBuilder.state(statusEnum.name());
                    }
                }

                if (vo.hasPlacementTop()) {
                    campaignVoBuilder.placementTop(vo.getPlacementTop().getValue());
                }
                if (vo.hasPlacementProductPage()) {
                    campaignVoBuilder.placementProductPage(vo.getPlacementProductPage().getValue());
                }
                if (vo.hasStrategy()) {
                    campaignVoBuilder.strategy(vo.getStrategy());
                }
                if (vo.hasDxmCampaignId()) {
                    campaignVoBuilder.dxmCampaignId(vo.getDxmCampaignId().getValue());
                }

                if (vo.hasCampaignId()) {
                    campaignVoBuilder.campaignId(vo.getCampaignId());
                }
                if (vo.hasType()) {
                    campaignVoBuilder.type(vo.getType());
                }

                BatchCampaignVo campaignvo = campaignVoBuilder.build();
                updateList.add(campaignvo);
            }
            //处理业务返回结果
            Result result = null;
            try {
                result = amazonAdCampaignAllService.batchUpdate(updateList, puid, uid, shopId, type);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            builder.setCode(Int32Value.newBuilder().setValue(result.getCode()).build());
            if (result.getData() != null) {
                builder.setData(JSONUtil.objectToJson(result.getData()));
            }
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 测试用，无
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void syncShop(SyncCampaignsRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("所有类型同步广告活动信息", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            boolean res = cpcCampaignService.syncShop(request.getPuid().getValue(), request.getShopId().getValue(), request.getType());
            if (res) {
                builder.setCode(Int32Value.of(Result.SUCCESS));
            } else {
                builder.setCode(Int32Value.of(Result.ERROR));
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 获取广告组下的产品asin
     */
    @Override
    public void getAsinByAdGroupId(GetAsinByAdGroupIdRequest request, StreamObserver<GetAsinByAdGroupIdResponse> responseObserver) {
        log.info("查询广告组asin数据 request {}", request);
        GetAsinByAdGroupIdResponse.Builder builder = GetAsinByAdGroupIdResponse.newBuilder();
        //检查参数
        if (!request.hasShopId() || !request.hasPuid() || !request.hasCampaignId() || !request.hasAdGroupId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            String asin = cpcCommonService.getAsinByAdGroupId(request.getPuid().getValue(), request.getShopId().getValue(), request.getCampaignId(), request.getAdGroupId());
            GetAsinByAdGroupIdResponse.AsinsByAdGroupId.Builder asinBuilder = GetAsinByAdGroupIdResponse.AsinsByAdGroupId.newBuilder();
            if (StringUtils.isNotBlank(asin)) {
                asinBuilder.setAsin(asin);
            }

            builder.setData(asinBuilder.build());
            builder.setCode(Int32Value.of(Result.SUCCESS));

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    /**
     * 广告活动小时级数据
     */
    @Deprecated
    @Override
    public void getCampaignHourReport(GetCampaignHourReportRequest request, StreamObserver<GetCampaignHourReportResponse> responseObserver) {
        log.info("广告活动小时级数据 request {}", request);
        GetCampaignHourReportResponse.Builder builder = GetCampaignHourReportResponse.newBuilder();
        //检查参数
        if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()
                || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");

        } else {
            CampaignHourParam param = new CampaignHourParam();
            BeanUtils.copyProperties(request, param);
            param.setCampaignId(request.getCampaignId());
            param.setWeeks(request.getWeeks());
            param.setPuid(request.getPuid().getValue());
            param.setShopId(request.getShopId().getValue());
            param.setStartDate(request.getStartDateStr());
            param.setEndDate(request.getEndDateStr());
            param.setEndDateCompare(request.getEndDateCompareStr());
            param.setStartDateCompare(request.getStartDateCompareStr());
            param.setOrderField(request.getOrderField());
            param.setOrderType(request.getOrderType());
            if (request.hasIsCompare()) {
                param.setIsCompare(request.getIsCompare().getValue());
            }


            param.setPuid(request.getPuid().getValue());
            param.setShopId(request.getShopId().getValue());
            param.setPageNo(request.getPageNo().getValue());
            param.setPageSize(request.getPageSize().getValue());
            param.setUid(request.getUid().getValue());

            // 仅显示正在投放
            if (request.hasServingStatus()) {
                param.setServingStatus(request.getServingStatus());
            }

            if (request.hasUseAdvanced()) {  //是否开启高级搜索
                param.setUseAdvanced(request.getUseAdvanced());
            }

            if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
                AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
                param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
                param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
                param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
                param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
                param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
                param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
                param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
                param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
                param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
                param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
                param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
                param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
                param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
                param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
                param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
                param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
                param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
                param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
                param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
                param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
                param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
                param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
                param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
                param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            }

            if (request.hasFilterTargetType()) {
                param.setFilterTargetType(request.getFilterTargetType());
            }
            if (request.hasDailyBudgetMin()) {
                param.setDailyBudgetMin(BigDecimal.valueOf(request.getDailyBudgetMin()));
            }
            if (request.hasDailyBudgetMax()) {
                param.setDailyBudgetMax(BigDecimal.valueOf(request.getDailyBudgetMax()));
            }
            if (request.hasFilterStartDate()) {
                param.setFilterStartDate(request.getFilterStartDate());
            }
            if (request.hasFilterEndDate()) {
                param.setFilterEndDate(request.getFilterEndDate());
            }
            if (request.hasCostType()) {
                param.setCostType(request.getCostType());
            }
//            if (request.hasAsin()) {
//                param.setAsin(request.getAsin());
//            }

            //根据活动id查询具体的活动
            if (request.hasCampaignId()) {
                param.setCampaignId(request.getCampaignId());
            }


            GetCampaignHourReportResponse.CampaignHour campaignHourReport =
                    cpcCommonService.getCampaignHourReport(request.getPuid().getValue(), param, request.getDateModel());
            if (campaignHourReport != null) {
                builder.setData(campaignHourReport);
            }
            builder.setCode(Int32Value.of(Result.SUCCESS));

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告活动预算小时级数据
     */
    @Override
    public void getCampaignBudgetHourReport(GetCampaignBudgetHourReportRequest request, StreamObserver<GetCampaignBudgetHourReportResponse> responseObserver) {
        log.info("广告活动小时级数据 request {}", request);
        GetCampaignBudgetHourReportResponse.Builder builder = GetCampaignBudgetHourReportResponse.newBuilder();
        //检查参数
        if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()
                || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");

        } else {
            CampaignHourParam param = new CampaignHourParam();
            BeanUtils.copyProperties(request, param);
            param.setCampaignId(request.getCampaignId());
            param.setWeeks(request.getWeeks());
            param.setPuid(request.getPuid().getValue());
            param.setShopId(request.getShopId().getValue());
            param.setStartDate(request.getStartDateStr());
            param.setEndDate(request.getEndDateStr());
            param.setEndDateCompare(request.getEndDateCompareStr());
            param.setStartDateCompare(request.getStartDateCompareStr());
            param.setOrderField(request.getOrderField());
            param.setOrderType(request.getOrderType());
            if (request.hasFindType()) {
                param.setFindType(request.getFindType());
            }
            if (request.hasFindValue()) {
                param.setFindValue(request.getFindValue());
            }
            if (request.hasIsCompare()) {
                param.setIsCompare(request.getIsCompare().getValue());
            }


            param.setPuid(request.getPuid().getValue());
            param.setShopId(request.getShopId().getValue());
            param.setPageNo(request.getPageNo().getValue());
            param.setPageSize(request.getPageSize().getValue());
            param.setUid(request.getUid().getValue());

            // 仅显示正在投放
            if (request.hasServingStatus()) {
                param.setServingStatus(request.getServingStatus());
            }

            if (request.hasUseAdvanced()) {  //是否开启高级搜索
                param.setUseAdvanced(request.getUseAdvanced());
            }

            if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
                AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
                param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
                param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
                param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
                param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
                param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
                param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
                param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
                param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
                param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
                param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
                param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
                param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
                param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
                param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
                param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
                param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
                param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
                param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
                param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
                param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
                param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
                param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
                param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
                param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            }

            if (request.hasFilterTargetType()) {
                param.setFilterTargetType(request.getFilterTargetType());
            }
            if (request.hasDailyBudgetMin()) {
                param.setDailyBudgetMin(BigDecimal.valueOf(request.getDailyBudgetMin()));
            }
            if (request.hasDailyBudgetMax()) {
                param.setDailyBudgetMax(BigDecimal.valueOf(request.getDailyBudgetMax()));
            }
            if (request.hasFilterStartDate()) {
                param.setFilterStartDate(request.getFilterStartDate());
            }
            if (request.hasFilterEndDate()) {
                param.setFilterEndDate(request.getFilterEndDate());
            }
            if (request.hasCostType()) {
                param.setCostType(request.getCostType());
            }

            //根据活动id查询具体的活动
            if (request.hasCampaignId()) {
                param.setCampaignId(request.getCampaignId());
            }


            GetCampaignBudgetHourReportResponse.CampaignHour campaignHourReport =
                    cpcCommonService.getCampaignBudgetHourReport(request.getPuid().getValue(), param, request.getDateModel());
            if (campaignHourReport != null) {
                builder.setData(campaignHourReport);
            }
            builder.setCode(Int32Value.of(Result.SUCCESS));

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告位周叠加数据
     */
    @Override
    public void getPlacementWeekReport(GetPlacementWeekReportRequestPb.GetPlacementWeekReportRequest request, StreamObserver<GetPlacementWeekReportResponsePb.GetPlacementWeekReportResponse> responseObserver) {
        try {
            GetPlacementWeekReportResponsePb.GetPlacementWeekReportResponse.Builder builder =
                    GetPlacementWeekReportResponsePb.GetPlacementWeekReportResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                PlacementHourParam param = new PlacementHourParam();
                param.setCampaignId(request.getCampaignId());
                param.setPredicate(request.getPredicate());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setOrderField(request.getOrderField());
                param.setOrderType(request.getOrderType());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDateCompare(request.getStartDateCompareStr());
                param.setEndDateCompare(request.getEndDateCompareStr());
                List<AdPlacementWeekDayVo> list =
                        amazonAdPlacementHourReportService.getWeeklySuperpositionList(param.getPuid(), param);
                if (CollectionUtils.isNotEmpty(list)) {
                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdPlacementWeekDayVo.class);
                    if (isSorted) {
                        PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
                    }

                    GetPlacementWeekReportResponsePb.GetPlacementWeekReportResponse.PlacementWeek.Builder reportBuilder =
                            GetPlacementWeekReportResponsePb.GetPlacementWeekReportResponse.PlacementWeek.newBuilder();
                    reportBuilder.addAllList(list.stream().filter(Objects::nonNull)
                            .map(PbUtil::toPlacementPb).collect(Collectors.toList()));
                    reportBuilder.setSummary(PbUtil.toPlacementPb(summaryPlacementOfWeekVo(list)));
                    reportBuilder.addAllChart(ReportChartUtil.getPlacementWeekChartData(list, false));
                    builder.setCode(Int32Value.of(Result.SUCCESS));
                    builder.setData(reportBuilder.build());
                }
            }

            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("puid:{},shopId:{},campaignId:{}，广告位{}，获取广告位周叠加数据异常",
                    request.getPuid().getValue(), request.getShopId().getValue(), request.getCampaignId(), request.getPredicate(), e);
            responseObserver.onError(e);
        }
    }


    /**
     * 广告活动周叠加数据
     */
    @Override
    public void getCampaignWeekReport(GetCampaignWeekReportRequestPb.GetCampaignWeekReportRequest request, StreamObserver<GetCampaignWeekReportResponsePb.GetCampaignWeekReportResponse> responseObserver) {
        try {
            GetCampaignWeekReportResponsePb.GetCampaignWeekReportResponse.Builder builder =
                    GetCampaignWeekReportResponsePb.GetCampaignWeekReportResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                CampaignHourParam param = new CampaignHourParam();
                BeanUtils.copyProperties(request, param);
                param.setCampaignId(request.getCampaignId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setOrderField(request.getOrderField());
                param.setOrderType(request.getOrderType());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setPageNo(request.getPageNo().getValue());
                param.setPageSize(request.getPageSize().getValue());
                param.setUid(request.getUid().getValue());
                List<AdCampaignWeekDayVo> list =
                        amazonAdCampaignHourReportService.getWeeklySuperpositionList(param.getPuid(), param);
                BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(request.getPuid().getValue(), param.getStartDate().replace("-", ""), param.getEndDate().replace("-", ""));
                if (CollectionUtils.isNotEmpty(list)) {
                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdProductWeekDayVo.class);
                    if (isSorted) {
                        PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
                    }

                    GetCampaignWeekReportResponsePb.GetCampaignWeekReportResponse.CampaignWeek.Builder reportBuilder =
                            GetCampaignWeekReportResponsePb.GetCampaignWeekReportResponse.CampaignWeek.newBuilder();
                    reportBuilder.addAllList(list.stream().filter(Objects::nonNull)
                            .map(key -> PbUtil.toCampaignPb(key, shopSalesByDate)).collect(Collectors.toList()));
                    reportBuilder.setSummary(PbUtil.toCampaignPb(summaryCampaignOfWeekVo(list), shopSalesByDate));
                    reportBuilder.addAllChart(ReportChartUtil.getCampaignWeekChartData(list, false));
                    builder.setCode(Int32Value.of(Result.SUCCESS));
                    builder.setData(reportBuilder.build());
                }
            }

            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    public static AdCampaignWeekDayVo summaryCampaignOfWeekVo(List<AdCampaignWeekDayVo> list) {
        AdCampaignWeekDayVo vo = new AdCampaignWeekDayVo();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        for (AdCampaignWeekDayVo ad : list) {
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
            vo.setAdCostPercentage(MathUtil.add(vo.getAdCostPercentage(), ad.getAdCostPercentage()));
            vo.setAdSalePercentage(MathUtil.add(vo.getAdSalePercentage(), ad.getAdSalePercentage()));
            vo.setAdOrderNumPercentage(MathUtil.add(vo.getAdOrderNumPercentage(), ad.getAdOrderNumPercentage()));
            vo.setOrderNumPercentage(MathUtil.add(vo.getOrderNumPercentage(), ad.getOrderNumPercentage()));

            vo.setViewableImpressions(MathUtil.add(vo.getViewableImpressions(), ad.getViewableImpressions()));
            vo.setOrdersNewToBrand(MathUtil.add(vo.getOrdersNewToBrand(), ad.getOrdersNewToBrand()));
            vo.setUnitsOrderedNewToBrand(MathUtil.add(vo.getUnitsOrderedNewToBrand(), ad.getUnitsOrderedNewToBrand()));
            vo.setSalesNewToBrand(MathUtil.add(vo.getSalesNewToBrand(), ad.getSalesNewToBrand()));
        }
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        if (vo.getAdCostPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdCostPercentage()) == 0) {
            vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdCostPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getAdSalePercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdSalePercentage()) == 0) {
            vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdSalePercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getAdOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdOrderNumPercentage()) == 0) {
            vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdOrderNumPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getOrderNumPercentage()) == 0) {
            vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setOrderNumPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));
        vo.setVcpm(MathUtil.divideByThousand(vo.getAdCost(), vo.getImpressions()));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
        vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getOtherAdOrderNum())));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));
        return vo;
    }

    @Override
    public void getAllAdsAggregateData(AllAdsDataRequest request, StreamObserver<AllAdsAggregateDataResponse> responseObserver) {
        log.info("查询所有类型广告产品 request {}", request);
        AllAdsAggregateDataResponse.Builder builder = AllAdsAggregateDataResponse.newBuilder();
        // 做参数校验
        AdAdsPageParam param = new AdAdsPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());

        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(param.getCompareStartDate().replace("-", ""));
        param.setCompareEndDate(param.getCompareEndDate().replace("-", ""));

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }

        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMin()), BigDecimal.valueOf(100)) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMax()), BigDecimal.valueOf(100)) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMin()), BigDecimal.valueOf(100)) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMax()), BigDecimal.valueOf(100)) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()), BigDecimal.valueOf(100)) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()), BigDecimal.valueOf(100)) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMin()), BigDecimal.valueOf(100)) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMax()), BigDecimal.valueOf(100)) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMin()), BigDecimal.valueOf(100)) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMax()), BigDecimal.valueOf(100)) : null);
        }


        String err = checkAdAdsPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");

        } else {
            AllAdsAggregateDataResponse.AdCreateHomeVo HomeVo = cpcCommonService.getAllAdsAggregateData(param.getPuid(), param);

            builder.setData(HomeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setMsg("success");
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 查询所有类型广告产品
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllAdsData(AllAdsDataRequest request, StreamObserver<AllAdsDataResponse> responseObserver) {
        log.info("查询所有类型广告产品 request {}", request);
        AllAdsDataResponse.Builder builder = AllAdsDataResponse.newBuilder();
        // 做参数校验
        AdAdsPageParam param = new AdAdsPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());

        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(param.getCompareStartDate().replace("-", ""));
        param.setCompareEndDate(param.getCompareEndDate().replace("-", ""));

        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
        }


        String err = checkAdAdsPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");

        } else {
            AllAdsDataResponse.AdCreateHomeVo HomeVo = cpcCommonService.getAllAdsData(param.getPuid(), param);

            builder.setData(HomeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setMsg("success");
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sp关键词投放
     * 更新关键词实时排名
     * (投放,搜索词)
     */
    @Override
    public void updateKeywordRank(KeywordTargetRankRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp关键词投放-更新关键词实时排名 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasKeywordId() || !request.hasAdvRank()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            Result result = cpcCommonService.updateKeywordRank(request.getPuid(), request.getShopId(), request.getKeywordId()
                    , request.getAdvRank(), request.getUid());
            builder.setCode(Int32Value.of(result.getCode()));
            if (result.getData() != null) {
                builder.setData(JSONUtil.objectToJson(result.getData()));
            }
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告位小时级数据
     */
    @Override
    public void getPlacementHourReport(GetPlacementHourReportRequestPb.GetPlacementHourReportRequest request, StreamObserver<GetPlacementHourReportResponsePb.GetPlacementHourReportResponse> responseObserver) {
        log.info("广告位小时级数据 request {}", request);
        GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.Builder builder = GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.newBuilder();
        //检查参数
        if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()
                || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");

        } else {
            PlacementHourParam param = new PlacementHourParam();
            param.setCampaignId(request.getCampaignId());
            param.setPredicate(request.getPredicate());
            param.setWeeks(request.getWeeks());
            param.setPuid(request.getPuid().getValue());
            param.setShopId(request.getShopId().getValue());
            param.setStartDate(request.getStartDateStr());
            param.setEndDate(request.getEndDateStr());
            param.setEndDateCompare(request.getEndDateCompareStr());
            param.setStartDateCompare(request.getStartDateCompareStr());
            param.setOrderField(request.getOrderField());
            param.setOrderType(request.getOrderType());
            param.setIsCompare(request.getIsCompare().getValue());
            param.setCampaignSite(request.getCampaignSite());
            //根据活动id查询具体的活动
            if (request.hasCampaignId()) {
                param.setCampaignId(request.getCampaignId());
            }
            //产品透视分析产品维度
            if (request.hasFindType()) {
                param.setFindType(request.getFindType());
            }
            if (request.hasFindValue()) {
                param.setFindValue(request.getFindValue());
            }

            GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour placementHourReport =
                    cpcCommonService.getPlacementHourReport(request.getPuid().getValue(), param, request.getDateModel());
            if (placementHourReport != null) {
                builder.setData(placementHourReport);
            }
            builder.setCode(Int32Value.of(Result.SUCCESS));

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private String checkAdAdsPageParam(AdAdsPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }
        if (StringUtils.isNotBlank(param.getOrderField()) && !Constants.isADperformanceOrderField(param.getOrderField())) {
            return "请求参数错误";
        }
        if (StringUtils.isNotBlank(param.getType())) {
            if (!Arrays.asList(Constants.SB).contains(param.getType())) {
                return "请求参数错误";
            }
        }

        return null;
    }

    public static AdPlacementWeekDayVo summaryPlacementOfWeekVo(List<AdPlacementWeekDayVo> list) {
        AdPlacementWeekDayVo vo = new AdPlacementWeekDayVo();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        for (AdPlacementWeekDayVo ad : list) {
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setAdCostPercentage(MathUtil.add(vo.getAdCostPercentage(), ad.getAdCostPercentage()));
            vo.setAdSalePercentage(MathUtil.add(vo.getAdSalePercentage(), ad.getAdSalePercentage()));
            vo.setAdOrderNumPercentage(MathUtil.add(vo.getAdOrderNumPercentage(), ad.getAdOrderNumPercentage()));
            vo.setOrderNumPercentage(MathUtil.add(vo.getOrderNumPercentage(), ad.getOrderNumPercentage()));
        }
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        if (vo.getAdCostPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdCostPercentage()) == 0) {
            vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdCostPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getAdSalePercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdSalePercentage()) == 0) {
            vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdSalePercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getAdOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdOrderNumPercentage()) == 0) {
            vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdOrderNumPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getOrderNumPercentage()) == 0) {
            vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setOrderNumPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        return vo;
    }

    public static AdGroupWeekDayVo summaryGroupOfWeekVo(List<AdGroupWeekDayVo> list) {
        AdGroupWeekDayVo vo = new AdGroupWeekDayVo();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        for (AdGroupWeekDayVo ad : list) {
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setAdCostPercentage(MathUtil.add(vo.getAdCostPercentage(), ad.getAdCostPercentage()));
            vo.setAdSalePercentage(MathUtil.add(vo.getAdSalePercentage(), ad.getAdSalePercentage()));
            vo.setAdOrderNumPercentage(MathUtil.add(vo.getAdOrderNumPercentage(), ad.getAdOrderNumPercentage()));
            vo.setOrderNumPercentage(MathUtil.add(vo.getOrderNumPercentage(), ad.getOrderNumPercentage()));
        }
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        if (vo.getAdCostPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdCostPercentage()) == 0) {
            vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdCostPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getAdSalePercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdSalePercentage()) == 0) {
            vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdSalePercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getAdOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdOrderNumPercentage()) == 0) {
            vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdOrderNumPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getOrderNumPercentage()) == 0) {
            vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setOrderNumPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        return vo;
    }

    @Override
    public void translate(TranslateRequest request, StreamObserver<TranslateResponse> responseStreamObserver) {
        log.info("翻译 request {}", request);
        TranslateResponse.Builder builder = TranslateResponse.newBuilder();
        //检查参数
        if (!request.hasPuid() || !request.hasShopId() || !request.hasMarketplaceId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        }
        String keywords = request.getKeywords();
        List<String> keywordList = Arrays.stream(keywords.split(",")).distinct().collect(Collectors.toList());
        int keywordSize = CollectionUtils.size(keywordList);
        // 该接口目前支持最多200个keyword翻译,亚马逊上限目前是1000个,如果后续有需求,可以提高数量上限
        if (keywordSize == 0 || keywordSize > 200) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        }
        Map<String, String> ressultMap = adTranslateService.translate(request.getPuid(), request.getShopId(), request.getMarketplaceId(), keywordList);
        builder.putAllData(ressultMap);
        responseStreamObserver.onNext(builder.build());
        responseStreamObserver.onCompleted();
    }

    @Override
    public void multilingualTranslate(multilingualTranslateRequest request, StreamObserver<TranslateResponse> responseStreamObserver) {
        log.info("翻译 request {}", request);
        TranslateResponse.Builder builder = TranslateResponse.newBuilder();
        //检查参数
        if (!request.hasPuid() || !request.hasShopId() || !request.hasSourceMarketplaceId() || !request.hasTargetMarketplaceId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        }
        String keywords = request.getKeywords();
        List<String> keywordList = Arrays.stream(keywords.split(",")).distinct().collect(Collectors.toList());
        int keywordSize = CollectionUtils.size(keywordList);
        // 亚马逊上限目前是1000个,如果后续有需求,可以提高数量上限
        if (keywordSize == 0 || keywordSize > 1000) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        }
        ShopAuth shopAuth = scVcShopAuthDao.getScAndVcByIdAndPuid(request.getShopId(), request.getPuid());
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(request.getPuid(), request.getShopId());
        Map<String, String> resultMap = cpcSpTargetingManager.translateKeywordsByMarketplaceId(request.getSourceMarketplaceId(), request.getTargetMarketplaceId(), keywordList, shopAuth, amazonAdProfile);
        builder.putAllData(resultMap);
        responseStreamObserver.onNext(builder.build());
        responseStreamObserver.onCompleted();
    }

    //*****************************************************微信端接口开始**********************************************/

    /**
     * 查询所有搜索词(投放产生)
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getWxAllQueryTargetData(AllQueryTargetDataRequest request, StreamObserver<AllQueryTargetDataResponse> responseObserver) {
        log.info("wx端-查询所有搜索词(投放产生) request {}", request);
        AllQueryTargetDataResponse.Builder builder = AllQueryTargetDataResponse.newBuilder();
        CpcQueryWordDto dto = new CpcQueryWordDto();
        BeanUtils.copyProperties(request, dto);
        dto.setPuid(request.getPuid().getValue());
        dto.setShopId(request.getShopId().getValue());

        AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
        if (advancedFilter != null) {
            dto.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            dto.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            dto.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            dto.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            dto.setClickRateMin(advancedFilter.hasClickRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMin()), BigDecimal.valueOf(100)) : null);
            dto.setClickRateMax(advancedFilter.hasClickRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMax()), BigDecimal.valueOf(100)) : null);
            dto.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            dto.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            dto.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            dto.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            dto.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            dto.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            dto.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            dto.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            dto.setAcosMin(advancedFilter.hasAcosMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMin()), BigDecimal.valueOf(100)) : null);
            dto.setAcosMax(advancedFilter.hasAcosMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMax()), BigDecimal.valueOf(100)) : null);
            dto.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            dto.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            dto.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()), BigDecimal.valueOf(100)) : null);
            dto.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()), BigDecimal.valueOf(100)) : null);
            dto.setAcotsMin(advancedFilter.hasAcotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMin()), BigDecimal.valueOf(100)) : null);
            dto.setAcotsMax(advancedFilter.hasAcotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMax()), BigDecimal.valueOf(100)) : null);
            dto.setAsotsMin(advancedFilter.hasAsotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMin()), BigDecimal.valueOf(100)) : null);
            dto.setAsotsMax(advancedFilter.hasAsotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMax()), BigDecimal.valueOf(100)) : null);
        }

        if (request.hasFilterTargetType()) {
            dto.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasFilterAddProperty()) {
            dto.setFilterAddProperty(request.getFilterAddProperty());
        }

        //做参数校验
        if (!request.hasShopId() || checkQueryWordDto(dto)) {
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setMsg("请求参数错误");
        } else {
            Page page = new Page(request.getPageNo().getValue(), request.getPageSize().getValue());

            //兼容前端
            dto.setState(dto.getStatus());
            //兼容一下前端传参问题
            dto.setOrderValue(dto.getOrderType());
            AllQueryTargetDataResponse.AdQueryTargetingHomeVo homeVo = wxCpcCommonService.getAllQueryTargetData(dto.getPuid(), dto, page);

            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    @Override
    public void getWxAllQueryTargetAggregateData(AllQueryTargetDataRequest request, StreamObserver<AllQueryTargetAggregateDataResponse> responseObserver) {
        log.info("wx端-查询所有搜索词(投放产生) request {}", request);
        AllQueryTargetAggregateDataResponse.Builder builder = AllQueryTargetAggregateDataResponse.newBuilder();
        CpcQueryWordDto dto = new CpcQueryWordDto();
        BeanUtils.copyProperties(request, dto);
        dto.setPuid(request.getPuid().getValue());
        dto.setShopId(request.getShopId().getValue());

        AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
        if (advancedFilter != null) {
            dto.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            dto.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            dto.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            dto.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            dto.setClickRateMin(advancedFilter.hasClickRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMin()), BigDecimal.valueOf(100)) : null);
            dto.setClickRateMax(advancedFilter.hasClickRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMax()), BigDecimal.valueOf(100)) : null);
            dto.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            dto.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            dto.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            dto.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            dto.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            dto.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            dto.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            dto.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            dto.setAcosMin(advancedFilter.hasAcosMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMin()), BigDecimal.valueOf(100)) : null);
            dto.setAcosMax(advancedFilter.hasAcosMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMax()), BigDecimal.valueOf(100)) : null);
            dto.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            dto.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            dto.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()), BigDecimal.valueOf(100)) : null);
            dto.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()), BigDecimal.valueOf(100)) : null);
            dto.setAcotsMin(advancedFilter.hasAcotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMin()), BigDecimal.valueOf(100)) : null);
            dto.setAcotsMax(advancedFilter.hasAcotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMax()), BigDecimal.valueOf(100)) : null);
            dto.setAsotsMin(advancedFilter.hasAsotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMin()), BigDecimal.valueOf(100)) : null);
            dto.setAsotsMax(advancedFilter.hasAsotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMax()), BigDecimal.valueOf(100)) : null);
        }

        if (request.hasFilterTargetType()) {
            dto.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasFilterAddProperty()) {
            dto.setFilterAddProperty(request.getFilterAddProperty());
        }
        if (request.hasTargetId()) {
            dto.setTargetId(request.getTargetId());
        }
        if (request.hasKeywordId()) {
            dto.setKeywordId(request.getKeywordId());
        }

        //做参数校验
        if (!request.hasShopId() || checkQueryWordDto(dto)) {
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setMsg("请求参数错误");
        } else {

            //兼容前端
            dto.setState(dto.getStatus());
            //兼容一下前端传参问题
            dto.setOrderValue(dto.getOrderType());
            AllQueryTargetAggregateDataResponse.AdQueryTargetingHomeVo homeVo = wxCpcCommonService.getAllQueryTargetAggregateData(dto.getPuid(), dto);

            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    /**
     * 查询所有搜索词(关键词产生)
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getWxAllQueryWordData(AllQueryWordDataRequest request, StreamObserver<AllQueryWordDataResponse> responseObserver) {
        log.info("wx端-查询所有搜索词(关键词产生) request {}", request);
        AllQueryWordDataResponse.Builder builder = AllQueryWordDataResponse.newBuilder();

        CpcQueryWordDto dto = new CpcQueryWordDto();
        BeanUtils.copyProperties(request, dto);
        dto.setPuid(request.getPuid().getValue());
        dto.setShopId(request.getShopId().getValue());
        dto.setType(request.getType());

        AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
        if (advancedFilter != null) {
            dto.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            dto.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            dto.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            dto.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            dto.setClickRateMin(advancedFilter.hasClickRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMin()), BigDecimal.valueOf(100)) : null);
            dto.setClickRateMax(advancedFilter.hasClickRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMax()), BigDecimal.valueOf(100)) : null);
            dto.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            dto.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            dto.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            dto.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            dto.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            dto.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            dto.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            dto.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            dto.setAcosMin(advancedFilter.hasAcosMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMin()), BigDecimal.valueOf(100)) : null);
            dto.setAcosMax(advancedFilter.hasAcosMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMax()), BigDecimal.valueOf(100)) : null);
            dto.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            dto.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            dto.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()), BigDecimal.valueOf(100)) : null);
            dto.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()), BigDecimal.valueOf(100)) : null);
            dto.setAcotsMin(advancedFilter.hasAcotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMin()), BigDecimal.valueOf(100)) : null);
            dto.setAcotsMax(advancedFilter.hasAcotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMax()), BigDecimal.valueOf(100)) : null);
            dto.setAsotsMin(advancedFilter.hasAsotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMin()), BigDecimal.valueOf(100)) : null);
            dto.setAsotsMax(advancedFilter.hasAsotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMax()), BigDecimal.valueOf(100)) : null);
        }

        if (request.hasFilterTargetType()) {
            dto.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasFilterMatchType()) {
            dto.setFilterMatchType(request.getFilterMatchType());
        }
        if (request.hasFilterAddProperty()) {
            dto.setFilterAddProperty(request.getFilterAddProperty());
        }

        //做参数校验
        if (!request.hasShopId() || checkQueryWordDto(dto)) {
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setMsg("请求参数错误");
        } else {
            Page page = new Page(request.getPageNo().getValue(), request.getPageSize().getValue());
            dto.setUseAdvanced(true);
            //兼容前端
            dto.setState(dto.getStatus());
            //兼容一下前端传参问题
            dto.setOrderValue(dto.getOrderType());
            AllQueryWordDataResponse.AdQueryWordsHomeVo homeVo = wxCpcCommonService.getAllQueryWordData(dto.getPuid(), dto, page);
            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    @Override
    public void getWxAllQueryWordAggregateData(AllQueryWordDataRequest request, StreamObserver<AllQueryWordAggregateDataResponse> responseObserver) {
        log.info("wx端-查询所有搜索词(关键词产生) request {}", request);
        AllQueryWordAggregateDataResponse.Builder builder = AllQueryWordAggregateDataResponse.newBuilder();

        CpcQueryWordDto dto = new CpcQueryWordDto();
        BeanUtils.copyProperties(request, dto);
        dto.setPuid(request.getPuid().getValue());
        dto.setShopId(request.getShopId().getValue());
        dto.setType(request.getType());

        AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
        if (advancedFilter != null) {
            dto.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            dto.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            dto.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            dto.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            dto.setClickRateMin(advancedFilter.hasClickRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMin()), BigDecimal.valueOf(100)) : null);
            dto.setClickRateMax(advancedFilter.hasClickRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMax()), BigDecimal.valueOf(100)) : null);
            dto.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            dto.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            dto.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            dto.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            dto.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            dto.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            dto.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            dto.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            dto.setAcosMin(advancedFilter.hasAcosMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMin()), BigDecimal.valueOf(100)) : null);
            dto.setAcosMax(advancedFilter.hasAcosMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMax()), BigDecimal.valueOf(100)) : null);
            dto.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            dto.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            dto.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()), BigDecimal.valueOf(100)) : null);
            dto.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()), BigDecimal.valueOf(100)) : null);
            dto.setAcotsMin(advancedFilter.hasAcotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMin()), BigDecimal.valueOf(100)) : null);
            dto.setAcotsMax(advancedFilter.hasAcotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMax()), BigDecimal.valueOf(100)) : null);
            dto.setAsotsMin(advancedFilter.hasAsotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMin()), BigDecimal.valueOf(100)) : null);
            dto.setAsotsMax(advancedFilter.hasAsotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMax()), BigDecimal.valueOf(100)) : null);
        }

        if (request.hasFilterTargetType()) {
            dto.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasFilterMatchType()) {
            dto.setFilterMatchType(request.getFilterMatchType());
        }
        if (request.hasFilterAddProperty()) {
            dto.setFilterAddProperty(request.getFilterAddProperty());
        }
        if (request.hasTargetId()) {
            dto.setTargetId(request.getTargetId());
        }
        if (request.hasKeywordId()) {
            dto.setKeywordId(request.getKeywordId());
        }

        //做参数校验
        if (!request.hasShopId() || checkQueryWordDto(dto)) {
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setMsg("请求参数错误");
        } else {
            dto.setUseAdvanced(true);
            //兼容前端
            dto.setState(dto.getStatus());
            //兼容一下前端传参问题
            dto.setOrderValue(dto.getOrderType());
            AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo homeVo = wxCpcCommonService.getAllQueryWordAggregateData(dto.getPuid(), dto);
            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    /**
     * 查询广告活动和广告组信息(不分页)
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getWxAllTypeCampAndGroup(AllTypeCampAndGroupPageRequest request, StreamObserver<AllTypeCampAndGroupPageResponse> responseObserver) {
        log.info("查询广告活动和广告组信息 request {}", request);
        AllTypeCampAndGroupPageResponse.Builder builder = AllTypeCampAndGroupPageResponse.newBuilder();
        //检查参数
        String err = checkParams(request);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(err);
        } else {
            CampaignAndGroupPageListParam param = CampaignAndGroupPageListParam.builder()
                    .shopId(request.getShopId().getValue())
                    .puid(request.getPuid().getValue())
                    .itemType(request.getItemType())
                    .modular(request.getModular())
                    .type(request.getType()).build();
            if (StringUtils.isNotBlank(request.getCampaignIds())) {
                param.setCampaignIds(request.getCampaignIds());
            }
            if (StringUtils.isNotBlank(request.getSearchValue())) {
                param.setSearchValue(request.getSearchValue());
            }
            Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> voPage = wxCpcCommonService.getAllTypeCampAndGroup(param);
            AllTypeCampAndGroupPageResponse.Page.Builder pageBuilder = AllTypeCampAndGroupPageResponse.Page.newBuilder();
            pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
            if (voPage.getRows() != null) {
                pageBuilder.addAllRows(voPage.getRows());
            }
            builder.setData(pageBuilder);
            builder.setCode(Int32Value.of(Result.SUCCESS));

        }

        responseObserver.onNext(builder.build());

        responseObserver.onCompleted();

    }


    @Override
    public void getAllWxGroupAggregateData(AllGroupDataRequest request, StreamObserver<AllGroupAggregateDataResponse> responseObserver) {
        log.info("查询所有类型广告组信息 wx request {}", request);
        AllGroupAggregateDataResponse.Builder builder = AllGroupAggregateDataResponse.newBuilder();
        GroupPageParam param = new GroupPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        if (!request.hasShopId() || param.getDxmCampaignId() != null) {
            param.setDxmCampaignId(request.getDxmCampaignId().getValue());
        }

        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }

        if (request.hasGroupId()) {
            param.setGroupId(request.getGroupId());
        }

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMin()), BigDecimal.valueOf(100)) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMax()), BigDecimal.valueOf(100)) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMin()), BigDecimal.valueOf(100)) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMax()), BigDecimal.valueOf(100)) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()), BigDecimal.valueOf(100)) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()), BigDecimal.valueOf(100)) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMin()), BigDecimal.valueOf(100)) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMax()), BigDecimal.valueOf(100)) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMin()), BigDecimal.valueOf(100)) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMax()), BigDecimal.valueOf(100)) : null);
        }

        if (request.hasFilterTargetType()) {
            param.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasBidMin()) {
            param.setBidMin(BigDecimal.valueOf(request.getBidMin()));
        }
        if (request.hasBidMax()) {
            param.setBidMax(BigDecimal.valueOf(request.getBidMax()));
        }

        //做参数校验
        String err = checkGroupPageParam(param);
        if (StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(err);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            AllGroupAggregateDataResponse.GroupHomeVo homeVo = cpcCommonService.getAllWxGroupAggregateData(param.getPuid(), param);

            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    /**
     * 广告活动-汇总数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllWxCampaignAggregateData(AllCampaignDataRequest request, StreamObserver<AllCampaignAggregateDataResponse> responseObserver) {
        log.info("查询所有类型广告活动 wx {}", request);
        long t = Instant.now().toEpochMilli();
        AllCampaignAggregateDataResponse.Builder builder = AllCampaignAggregateDataResponse.newBuilder();
        CampaignPageParam campaignPageParam = new CampaignPageParam();
        BeanUtils.copyProperties(request, campaignPageParam);
        campaignPageParam.setPuid(request.getPuid().getValue());
        campaignPageParam.setShopId(request.getShopId().getValue());
        campaignPageParam.setUid(request.getUid().getValue());

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            campaignPageParam.setServingStatus(request.getServingStatus());
        }

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            campaignPageParam.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            campaignPageParam.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            campaignPageParam.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            campaignPageParam.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            campaignPageParam.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            campaignPageParam.setClickRateMin(advancedFilter.hasClickRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMin()), BigDecimal.valueOf(100)) : null);
            campaignPageParam.setClickRateMax(advancedFilter.hasClickRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMax()), BigDecimal.valueOf(100)) : null);
            campaignPageParam.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            campaignPageParam.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            campaignPageParam.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            campaignPageParam.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            campaignPageParam.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            campaignPageParam.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            campaignPageParam.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            campaignPageParam.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            campaignPageParam.setAcosMin(advancedFilter.hasAcosMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMin()), BigDecimal.valueOf(100)) : null);
            campaignPageParam.setAcosMax(advancedFilter.hasAcosMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMax()), BigDecimal.valueOf(100)) : null);
            campaignPageParam.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            campaignPageParam.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            campaignPageParam.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()), BigDecimal.valueOf(100)) : null);
            campaignPageParam.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()), BigDecimal.valueOf(100)) : null);
            campaignPageParam.setAcotsMin(advancedFilter.hasAcotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMin()), BigDecimal.valueOf(100)) : null);
            campaignPageParam.setAcotsMax(advancedFilter.hasAcotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMax()), BigDecimal.valueOf(100)) : null);
            campaignPageParam.setAsotsMin(advancedFilter.hasAsotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMin()), BigDecimal.valueOf(100)) : null);
            campaignPageParam.setAsotsMax(advancedFilter.hasAsotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMax()), BigDecimal.valueOf(100)) : null);
        }

        if (request.hasFilterTargetType()) {
            campaignPageParam.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasDailyBudgetMin()) {
            campaignPageParam.setDailyBudgetMin(BigDecimal.valueOf(request.getDailyBudgetMin()));
        }
        if (request.hasDailyBudgetMax()) {
            campaignPageParam.setDailyBudgetMax(BigDecimal.valueOf(request.getDailyBudgetMax()));
        }
        if (request.hasFilterStartDate()) {
            campaignPageParam.setFilterStartDate(request.getFilterStartDate());
        }
        if (request.hasFilterEndDate()) {
            campaignPageParam.setFilterEndDate(request.getFilterEndDate());
        }

        //做参数校验
        String err = checkCampaignPageParam(campaignPageParam);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            AllCampaignAggregateDataResponse.CampaignHomeVo homeVo = cpcCommonService.getAllWxCampaignAggregateData(campaignPageParam.getPuid(), campaignPageParam);
            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        log.info("广告管理 wx--广告活动数据汇总接口调用花费时间 {} ,puid: {},shopId: {}", (Instant.now().toEpochMilli() - t), request.getPuid(), request.getShopId());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    /**
     * 查询所有类型广告活动
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllWxCampaignData(AllCampaignDataRequest request, StreamObserver<AllCampaignDataResponse> responseObserver) {
        log.info("查询所有类型广告活动 wx {}", request);
        long t = Instant.now().toEpochMilli();
        AllCampaignDataResponse.Builder builder = AllCampaignDataResponse.newBuilder();
        CampaignPageParam campaignPageParam = new CampaignPageParam();
        BeanUtils.copyProperties(request, campaignPageParam);
        campaignPageParam.setPuid(request.getPuid().getValue());
        campaignPageParam.setShopId(request.getShopId().getValue());
        campaignPageParam.setPageNo(request.getPageNo().getValue());
        campaignPageParam.setPageSize(request.getPageSize().getValue());
        campaignPageParam.setUid(request.getUid().getValue());

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            campaignPageParam.setServingStatus(request.getServingStatus());
        }

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            campaignPageParam.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            campaignPageParam.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            campaignPageParam.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            campaignPageParam.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            campaignPageParam.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            campaignPageParam.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            campaignPageParam.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            campaignPageParam.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            campaignPageParam.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            campaignPageParam.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            campaignPageParam.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            campaignPageParam.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            campaignPageParam.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            campaignPageParam.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            campaignPageParam.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            campaignPageParam.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            campaignPageParam.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            campaignPageParam.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            campaignPageParam.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            campaignPageParam.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            campaignPageParam.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
            campaignPageParam.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
            campaignPageParam.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            campaignPageParam.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
            campaignPageParam.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            campaignPageParam.setAdCostPercentageMin(advancedFilter.hasAdCostPercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdCostPercentageMin()) : null);
            campaignPageParam.setAdCostPercentageMax(advancedFilter.hasAdCostPercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdCostPercentageMax()) : null);
            campaignPageParam.setAdSalePercentageMin(advancedFilter.hasAdSalePercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdSalePercentageMin()) : null);
            campaignPageParam.setAdSalePercentageMax(advancedFilter.hasAdSalePercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdSalePercentageMax()) : null);
            campaignPageParam.setAdOrderNumPercentageMin(advancedFilter.hasAdOrderNumPercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdOrderNumPercentageMin()) : null);
            campaignPageParam.setAdOrderNumPercentageMax(advancedFilter.hasAdOrderNumPercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdOrderNumPercentageMax()) : null);
            campaignPageParam.setOrderNumPercentageMin(advancedFilter.hasOrderNumPercentageMin() ? BigDecimal.valueOf(advancedFilter.getOrderNumPercentageMin()) : null);
            campaignPageParam.setOrderNumPercentageMax(advancedFilter.hasOrderNumPercentageMax() ? BigDecimal.valueOf(advancedFilter.getOrderNumPercentageMax()) : null);
        }

        if (request.hasFilterTargetType()) {
            campaignPageParam.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasDailyBudgetMin()) {
            campaignPageParam.setDailyBudgetMin(BigDecimal.valueOf(request.getDailyBudgetMin()));
        }
        if (request.hasDailyBudgetMax()) {
            campaignPageParam.setDailyBudgetMax(BigDecimal.valueOf(request.getDailyBudgetMax()));
        }
        if (request.hasFilterStartDate()) {
            campaignPageParam.setFilterStartDate(request.getFilterStartDate());
        }
        if (request.hasFilterEndDate()) {
            campaignPageParam.setFilterEndDate(request.getFilterEndDate());
        }
        if (request.hasCostType()) {
            campaignPageParam.setCostType(request.getCostType());
        }

        //根据活动id查询具体的活动
        if (request.hasCampaignId()) {
            campaignPageParam.setCampaignId(request.getCampaignId());
        }
        //做参数校验
        String err = checkCampaignPageParam(campaignPageParam);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            AllCampaignDataResponse.CampaignHomeVo homeVo = cpcCommonService.getAllWxCampaignData(campaignPageParam.getPuid(), campaignPageParam);
            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            serversStatusSyncService.sendCampaign(request.getPuid().getValue(), request.getShopId().getValue(), homeVo);
        }
        log.info("广告管理 wx--广告活动接口调用花费时间 {} ,puid: {},shopId: {}", (Instant.now().toEpochMilli() - t), request.getPuid(), request.getShopId());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    /**
     * 查询所有类型广告组信息
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllWxGroupData(AllGroupDataRequest request, StreamObserver<AllGroupDataResponse> responseObserver) {
        log.info("查询所有类型广告组信息 wx request {}", request);
        AllGroupDataResponse.Builder builder = AllGroupDataResponse.newBuilder();
        GroupPageParam param = new GroupPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        if (!request.hasShopId() || param.getDxmCampaignId() != null) {
            param.setDxmCampaignId(request.getDxmCampaignId().getValue());
        }
        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setAdCostPercentageMin(advancedFilter.hasAdCostPercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdCostPercentageMin()) : null);
            param.setAdCostPercentageMax(advancedFilter.hasAdCostPercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdCostPercentageMax()) : null);
            param.setAdSalePercentageMin(advancedFilter.hasAdSalePercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdSalePercentageMin()) : null);
            param.setAdSalePercentageMax(advancedFilter.hasAdSalePercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdSalePercentageMax()) : null);
            param.setAdOrderNumPercentageMin(advancedFilter.hasAdOrderNumPercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdOrderNumPercentageMin()) : null);
            param.setAdOrderNumPercentageMax(advancedFilter.hasAdOrderNumPercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdOrderNumPercentageMax()) : null);
            param.setOrderNumPercentageMin(advancedFilter.hasOrderNumPercentageMin() ? BigDecimal.valueOf(advancedFilter.getOrderNumPercentageMin()) : null);
            param.setOrderNumPercentageMax(advancedFilter.hasOrderNumPercentageMax() ? BigDecimal.valueOf(advancedFilter.getOrderNumPercentageMax()) : null);
        }

        if (request.hasFilterTargetType()) {
            param.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasBidMin()) {
            param.setBidMin(BigDecimal.valueOf(request.getBidMin()));
        }
        if (request.hasBidMax()) {
            param.setBidMax(BigDecimal.valueOf(request.getBidMax()));
        }
        //通过广告组Id查询具体广告组
        if (request.hasGroupId()) {
            param.setGroupId(request.getGroupId());
        }

        //做参数校验
        String err = checkGroupPageParam(param);
        if (StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(err);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            AllGroupDataResponse.GroupHomeVo homeVo = cpcCommonService.getAllWxGroupData(param.getPuid(), param);
            serversStatusSyncService.sendGroup(request.getPuid().getValue(), request.getShopId().getValue(), homeVo);
            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    /**
     * 所有类型关键词
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllWxKeyWordData(AllKeyWordDataRequest request, StreamObserver<AllKeyWordDataResponse> responseObserver) {
        log.info("所有类型关键词 wx ", request);
        AllKeyWordDataResponse.Builder builder = AllKeyWordDataResponse.newBuilder();

        KeywordsPageParam param = new KeywordsPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        if (StringUtils.isNotBlank(request.getSearchType())) {
            param.setSearchType(param.getSearchType());
        }

        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate().replace("-", ""));
        param.setCompareEndDate(request.getCompareEndDate().replace("-", ""));


        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setAdCostPercentageMin(advancedFilter.hasAdCostPercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdCostPercentageMin()) : null);
            param.setAdCostPercentageMax(advancedFilter.hasAdCostPercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdCostPercentageMax()) : null);
            param.setAdSalePercentageMin(advancedFilter.hasAdSalePercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdSalePercentageMin()) : null);
            param.setAdSalePercentageMax(advancedFilter.hasAdSalePercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdSalePercentageMax()) : null);
            param.setAdOrderNumPercentageMin(advancedFilter.hasAdOrderNumPercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdOrderNumPercentageMin()) : null);
            param.setAdOrderNumPercentageMax(advancedFilter.hasAdOrderNumPercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdOrderNumPercentageMax()) : null);
            param.setOrderNumPercentageMin(advancedFilter.hasOrderNumPercentageMin() ? BigDecimal.valueOf(advancedFilter.getOrderNumPercentageMin()) : null);
            param.setOrderNumPercentageMax(advancedFilter.hasOrderNumPercentageMax() ? BigDecimal.valueOf(advancedFilter.getOrderNumPercentageMax()) : null);
            /********************关键词投放新增高级筛选新增查询指标*****************************/
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);

            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);


            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);


            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);


            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);

            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);

            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);

            param.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            param.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);

            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);

            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);

            param.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()) : null);

            //广告销量
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);

            //品牌新买家订单转化率
            param.setBrandNewBuyerOrderConversionRateMin(advancedFilter.hasBrandNewBuyerOrderConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getBrandNewBuyerOrderConversionRateMin()) : null);
            param.setBrandNewBuyerOrderConversionRateMax(advancedFilter.hasBrandNewBuyerOrderConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getBrandNewBuyerOrderConversionRateMax()) : null);


        }

        if (request.hasFilterTargetType()) {
            param.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasBidMin()) {
            param.setBidMin(BigDecimal.valueOf(request.getBidMin()));
        }
        if (request.hasBidMax()) {
            param.setBidMax(BigDecimal.valueOf(request.getBidMax()));
        }


        //做参数校验
        String err = checkKeywordsPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {

            if (CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(param.getType()) && StringUtils.isNotBlank(param.getSearchValue())) {
                if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN.equals(param.getSearchValue())) {
                    param.setSearchValue(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND);
                } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN.equals(param.getSearchValue())) {
                    param.setSearchValue(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES);
                }
            }

            if (CampaignTypeEnum.sp.getCampaignType().equalsIgnoreCase(param.getType()) && StringUtils.isNotBlank(param.getSearchValue())) {
                SpKeywordGroupValueEnum keywordGroupValueEnumByTextCn = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByTextCn(param.getSearchValue());
                if (keywordGroupValueEnumByTextCn != null) {
                    param.setSearchValue(keywordGroupValueEnumByTextCn.getKeywordText());
                }

            }

            AllKeyWordDataResponse.AdkeywordHomeRpcVo homeVO = cpcCommonService.getAllWxKeyWordData(param.getPuid(), param);
            builder.setData(homeVO);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            if (!CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(param.getType())) {
                serversStatusSyncService.sendKeyword(request.getPuid().getValue(), request.getShopId().getValue(), homeVO);
            }

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    /**
     * 所有类型投放
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllWxTargetData(AllTargetDataRequest request, StreamObserver<AllTargetDataResponse> responseObserver) {
        log.info("所有类型投放 wx ", request);
        //做参数校验
        AllTargetDataResponse.Builder builder = AllTargetDataResponse.newBuilder();

        TargetingPageParam param = new TargetingPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        param.setChosenTargetType(request.getChosenTargetType());
        param.setSelectType(request.getSelectType());

        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate().replace("-", ""));
        param.setCompareEndDate(request.getCompareEndDate().replace("-", ""));

        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setAdCostPercentageMin(advancedFilter.hasAdCostPercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdCostPercentageMin()) : null);
            param.setAdCostPercentageMax(advancedFilter.hasAdCostPercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdCostPercentageMax()) : null);
            param.setAdSalePercentageMin(advancedFilter.hasAdSalePercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdSalePercentageMin()) : null);
            param.setAdSalePercentageMax(advancedFilter.hasAdSalePercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdSalePercentageMax()) : null);
            param.setAdOrderNumPercentageMin(advancedFilter.hasAdOrderNumPercentageMin() ? BigDecimal.valueOf(advancedFilter.getAdOrderNumPercentageMin()) : null);
            param.setAdOrderNumPercentageMax(advancedFilter.hasAdOrderNumPercentageMax() ? BigDecimal.valueOf(advancedFilter.getAdOrderNumPercentageMax()) : null);
            param.setOrderNumPercentageMin(advancedFilter.hasOrderNumPercentageMin() ? BigDecimal.valueOf(advancedFilter.getOrderNumPercentageMin()) : null);
            param.setOrderNumPercentageMax(advancedFilter.hasOrderNumPercentageMax() ? BigDecimal.valueOf(advancedFilter.getOrderNumPercentageMax()) : null);

            /******************************广告管理高级筛选新增查询指标*******************************/
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);

            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);


            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);


            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);


            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);

            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);

            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);

            param.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            param.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);

            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);

            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);

            param.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()) : null);

            //广告销量
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);

            //品牌买家订单转化率
            param.setBrandNewBuyerOrderConversionRateMin(advancedFilter.hasBrandNewBuyerOrderConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getBrandNewBuyerOrderConversionRateMin()) : null);
            param.setBrandNewBuyerOrderConversionRateMax(advancedFilter.hasBrandNewBuyerOrderConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getBrandNewBuyerOrderConversionRateMax()) : null);
        }

        if (request.hasFilterTargetType()) {
            param.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasBidMin()) {
            param.setBidMin(BigDecimal.valueOf(request.getBidMin()));
        }
        if (request.hasBidMax()) {
            param.setBidMax(BigDecimal.valueOf(request.getBidMax()));
        }

        String err = checkTargetingPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");

        } else {
            AllTargetDataResponse.AdTargetingHomeRpcVo homeVo = cpcCommonService.getAllWxTargetData(param.getPuid(), param);
            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            if (!CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(param.getType())) {
                serversStatusSyncService.sendTarget(request.getPuid().getValue(), request.getShopId().getValue(), homeVo);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAllWxKeyWordAggregateData(AllKeyWordDataRequest request, StreamObserver<AllKeyWordAggregateDataResponse> responseObserver) {
        log.info("所有类型关键词", request);
        AllKeyWordAggregateDataResponse.Builder builder = AllKeyWordAggregateDataResponse.newBuilder();

        KeywordsPageParam param = new KeywordsPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());

        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMin()), BigDecimal.valueOf(100)) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMax()), BigDecimal.valueOf(100)) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMin()), BigDecimal.valueOf(100)) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMax()), BigDecimal.valueOf(100)) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()), BigDecimal.valueOf(100)) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()), BigDecimal.valueOf(100)) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMin()), BigDecimal.valueOf(100)) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMax()), BigDecimal.valueOf(100)) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMin()), BigDecimal.valueOf(100)) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMax()), BigDecimal.valueOf(100)) : null);
        }

        if (request.hasFilterTargetType()) {
            param.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasBidMin()) {
            param.setBidMin(BigDecimal.valueOf(request.getBidMin()));
        }
        if (request.hasBidMax()) {
            param.setBidMax(BigDecimal.valueOf(request.getBidMax()));
        }


        //做参数校验
        String err = checkKeywordsPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {

            AllKeyWordAggregateDataResponse.AdkeywordHomeRpcVo homeVO = cpcCommonService.getAllWxKeyWordAggregateData(param.getPuid(), param);
            builder.setData(homeVO);
            builder.setCode(Int32Value.of(Result.SUCCESS));

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    @Override
    public void getAllWxTargetAggregateData(AllTargetDataRequest request, StreamObserver<AllTargetAggregateDataResponse> responseObserver) {
        log.info("所有类型投放:{}", request);
        //做参数校验
        AllTargetAggregateDataResponse.Builder builder = AllTargetAggregateDataResponse.newBuilder();

        TargetingPageParam param = new TargetingPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setChosenTargetType(request.getChosenTargetType());

        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMin()), BigDecimal.valueOf(100)) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getClickRateMax()), BigDecimal.valueOf(100)) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMin()), BigDecimal.valueOf(100)) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcosMax()), BigDecimal.valueOf(100)) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()), BigDecimal.valueOf(100)) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()), BigDecimal.valueOf(100)) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMin()), BigDecimal.valueOf(100)) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAcotsMax()), BigDecimal.valueOf(100)) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMin()), BigDecimal.valueOf(100)) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? MathUtil.divide(BigDecimal.valueOf(advancedFilter.getAsotsMax()), BigDecimal.valueOf(100)) : null);
        }

        if (request.hasFilterTargetType()) {
            param.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasBidMin()) {
            param.setBidMin(BigDecimal.valueOf(request.getBidMin()));
        }
        if (request.hasBidMax()) {
            param.setBidMax(BigDecimal.valueOf(request.getBidMax()));
        }

        String err = checkTargetingPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");

        } else {
            AllTargetAggregateDataResponse.AdTargetingHomeRpcVo homeVo = cpcCommonService.getAllWxTargetAggregateData(param.getPuid(), param);
            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    @Override
    public void getWxAllPlacementData(AllPlacementDataRequest request, StreamObserver<AllPlacementDataResponse> responseObserver) {
        log.info("wx端-查询所有类型广告位 {}", request);
        AllPlacementDataResponse.Builder builder = AllPlacementDataResponse.newBuilder();

        PlacementPageParam param = new PlacementPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());

        // wx端暂时无高级筛选,先把这部分消掉
        param.setUseAdvanced(false);

        //做参数校验
        String err = checkPlacementPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            AllPlacementDataResponse.AdPlacementHomeVo homeVo = wxCpcCommonService.getAllPlacementData(param.getPuid(), param);

            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getWxAllPlacementAggregateData(AllPlacementDataRequest request, StreamObserver<AllPlacementAggregateDataResponse> responseObserver) {
        log.info("wx端-查询所有类型广告位 {}", request);
        AllPlacementAggregateDataResponse.Builder builder = AllPlacementAggregateDataResponse.newBuilder();

        PlacementPageParam param = new PlacementPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());

        // wx端暂时无高级筛选,先把这部分消掉
        param.setUseAdvanced(false);

        //做参数校验
        String err = checkPlacementPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            AllPlacementAggregateDataResponse.AdPlacementHomeVo homeVo = wxCpcCommonService.getAllPlacementAggregateData(param.getPuid(), param);

            builder.setData(homeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }


    @Override
    public void getWxAllProductData(AllProductDataRequest request, StreamObserver<AllProductDataResponse> responseObserver) {
        log.info("wx端-查询所有类型广告产品 request {}", request);
        AllProductDataResponse.Builder builder = AllProductDataResponse.newBuilder();
        // 做参数校验
        AdProductPageParam param = new AdProductPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());

        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }

        // wx端暂时无高级筛选,先把这部分消掉
        param.setUseAdvanced(false);

        String err = checkAdProductPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");

        } else {
            AllProductDataResponse.AdProductHomeVo HomeVo = wxCpcCommonService.getAllProductData(param.getPuid(), param);
            serversStatusSyncService.sendProduct(request.getPuid().getValue(), request.getShopId().getValue(), HomeVo);
            builder.setData(HomeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setMsg("success");
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    @Override
    public void getWxAllProductAggregateData(AllProductDataRequest request, StreamObserver<AllProductAggregateDataResponse> responseObserver) {
        log.info("wx端-查询所有类型广告产品 request {}", request);
        AllProductAggregateDataResponse.Builder builder = AllProductAggregateDataResponse.newBuilder();
        // 做参数校验
        AdProductPageParam param = new AdProductPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());

        // wx端暂时无高级筛选,先把这部分消掉
        param.setUseAdvanced(false);

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }

        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }

        String err = checkAdProductPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");

        } else {
            AllProductAggregateDataResponse.AdProductHomeVo HomeVo = wxCpcCommonService.getAllProductAggregateData(param.getPuid(), param);

            builder.setData(HomeVo);
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setMsg("success");
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getWxCampaignNeKeywordData(CampaignNeKewordDataRequest request, StreamObserver<CampaignNeKewordDataResponse> responseObserver) {
        log.info("wx端-查询所有类型否定关键词(活动)", request);
        CampaignNeKewordDataResponse.Builder builder = CampaignNeKewordDataResponse.newBuilder();
        //做参数校验
        CampaignNeKeywordsPageParam param = new CampaignNeKeywordsPageParam();
        BeanUtils.copyProperties(request, param);
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        param.setPuid(request.getPuid().getValue());

        String err = checkCamNeKeywordsPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(err);

        } else {
            Page<CampaignNeKeywordsPageRpcVo> voPage = wxCpcCommonService.getCampaignNeKeywordsPageList(param.getPuid(), param);
            CampaignNeKewordDataResponse.Page.Builder pageBuilder = CampaignNeKewordDataResponse.Page.newBuilder();
            pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
            pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
            pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
            pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
            if (voPage.getRows() != null) {
                pageBuilder.addAllRows(voPage.getRows());
            }
            builder.setData(pageBuilder);
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getWxAllNeKeywordData(AllNeKeywordDataRequest request, StreamObserver<AllNeKeywordDataResponse> responseObserver) {
        log.info("wx端-查询所有类型否定关键词(组)", request);
        AllNeKeywordDataResponse.Builder builder = AllNeKeywordDataResponse.newBuilder();
        //做参数校验
        NeKeywordsPageParam param = new NeKeywordsPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        if (request.hasUid()) {
            param.setUid(request.getUid().getValue());
        }
        String err = checkNeKeywordsPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(err);

        } else {
            Page<NeKeywordsPageRpcVo> voPage = wxCpcCommonService.getGroupNeKeywordPageList(param);
            AllNeKeywordDataResponse.Page.Builder pageBuilder = AllNeKeywordDataResponse.Page.newBuilder();
            pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
            pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
            pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
            pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
            if (voPage.getRows() != null) {
                pageBuilder.addAllRows(voPage.getRows());
            }

            builder.setData(pageBuilder.build());
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    @Override
    public void getWxCampaignNeTargetingData(CampaignNeTargetingDataRequest request, StreamObserver<CampaignNeTargetingDataResponse> responseObserver) {
        log.info("wx端-查询所有类型否定投放(活动)", request);
        CampaignNeTargetingDataResponse.Builder builder = CampaignNeTargetingDataResponse.newBuilder();

        if (!request.hasShopId() || StringUtils.isNotBlank(request.getStatus()) && StateEnum.getStateValue(request.getStatus()) == null) {
            builder.setMsg("请求参数错误");
            builder.setCode(Int32Value.of(Result.ERROR));
        } else {
            CampaignNeTargetingSpParam param = new CampaignNeTargetingSpParam();
            BeanUtils.copyProperties(request, param);
            param.setPuid(request.getPuid().getValue());
            param.setShopId(request.getShopId().getValue());
            param.setPageNo(request.getPageNo().getValue());
            param.setPageSize(request.getPageSize().getValue());
            if (request.hasDesc()) {
                param.setDesc(request.getDesc().getValue());
            }
            Page<CampaignNeTargetingSpRpcVo> voPage = wxCpcCommonService.getCampaignNeTargetingPageList(param);
            CampaignNeTargetingDataResponse.Page.Builder pageBuilder = CampaignNeTargetingDataResponse.Page.newBuilder();
            pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
            pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
            pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
            pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
            if (voPage.getRows() != null) {
                pageBuilder.addAllRows(voPage.getRows());
            }
            //设置data
            builder.setData(pageBuilder.build());
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getWxAllNeTargetingData(AllNeTargetingDataRequest request, StreamObserver<AllNeTargetingDataResponse> responseObserver) {
        log.info("wx端-所有类型否定投放", request);

        AllNeTargetingDataResponse.Builder builder = AllNeTargetingDataResponse.newBuilder();
        //做参数校验
        NeTargetingPageParam param = new NeTargetingPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        String err = checkNeTargetingPageParam(param);
        if (!request.hasShopId() || StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");

        } else {
            Page<NeTargetingPageRpcVo> voPage = wxCpcCommonService.getGroupNeTargetingPageList(param);

            AllNeTargetingDataResponse.Page.Builder pageBuilder = AllNeTargetingDataResponse.Page.newBuilder();
            pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
            pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
            pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
            pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
            if (voPage.getRows() != null) {
                pageBuilder.addAllRows(voPage.getRows());
            }

            builder.setData(pageBuilder.build());
            builder.setCode(Int32Value.of(Result.SUCCESS));

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 独立页查询广告组类型
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void queryGroupType(QueryGroupTypeRequest request, StreamObserver<QueryGroupTypeResponse> responseObserver) {
        log.info("自动化规则全部查询广告组合:{}", request);
        QueryGroupTypeResponse.Builder response = QueryGroupTypeResponse.newBuilder();
        Result<List<String>> result = cpcCommonService.queryGroupType(request.getPuid(), request.getShopId(), request.getCampaignId());
        response.setCode(result.getCode());
        response.setMsg(result.getMsg());
        if (result.getCode() == Result.SUCCESS) {
            if (CollectionUtils.isNotEmpty(result.getData())) {
                response.addAllData(result.getData());
            }
        }
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }

    @Override
    public void queryGroupTargetType(QueryGroupTargetTypeRequest request, StreamObserver<QueryGroupTargetTypeResponse> responseObserver) {
        QueryGroupTargetTypeResponse.Builder response = QueryGroupTargetTypeResponse.newBuilder();
        CampaignAndGroupTargetTypeInfo.Builder campaignAndGroupTargetInfoResp = CampaignAndGroupTargetTypeInfo.newBuilder();
        Result<CampaignAndGroupTargetTypeVo> result = cpcCommonService.queryCampaignAndGroupType(request.getPuid(),
                request.getShopId(), request.getCampaignId(), request.getGroupId());
        response.setCode(result.getCode());
        response.setMsg(result.getMsg());
        if (result.getCode() == Result.SUCCESS) {
            if (Objects.nonNull(result.getData())) {
                Optional.ofNullable(result.getData().getCampaignTargetType()).ifPresent(campaignAndGroupTargetInfoResp::setCampaignTargetType);
                if (CollectionUtils.isNotEmpty(result.getData().getGroupTargetTypeVo())) {
                    List<GroupTargetTypeInfo> typeInfoList = result.getData().getGroupTargetTypeVo().stream().map(t -> {
                        GroupTargetTypeInfo.Builder info = GroupTargetTypeInfo.newBuilder();
                        Optional.ofNullable(t.getGroupId()).ifPresent(info::setGroupId);
                        Optional.ofNullable(t.getGroupTargetType()).ifPresent(info::setTargetType);
                        return info.build();
                    }).collect(Collectors.toList());
                    campaignAndGroupTargetInfoResp.addAllGroupTargetTypeVo(typeInfoList);
                }
            }
        }
        response.setData(campaignAndGroupTargetInfoResp.build());
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }

    //*****************************************************微信端接口结束**********************************************/

    @Override
    public void productAdd(ProductAddRequest request, StreamObserver<ProductAddResponse> responseObserver) {
        log.info("添加广告产品 request {}", request);
        AddAdProductVo addAdProductVo = new AddAdProductVo();
        int shopId = request.getShopId();
        int puid = request.getPuid();
        String groupId = request.getGroupId();
        addAdProductVo.setShopId(shopId);
        addAdProductVo.setPuid(puid);
        addAdProductVo.setUid(request.getUid());
        addAdProductVo.setGroupId(groupId);
        addAdProductVo.setLoginIp(request.getIp());

        List<ProductVo> products = request.getMskuList().stream().map(msku -> {
            ProductVo vo = new ProductVo();
            vo.setAsin(request.getAsin());
            vo.setSku(msku);
            return vo;
        }).collect(Collectors.toList());
        addAdProductVo.setProducts(products);
        List<ProductAddResponse.AddFailInfo> failInfoList = Lists.newArrayList();
        addAdProductVo.setFailInfoList(failInfoList);

        if (spAdGroupDao.exist(puid, shopId, groupId)) {
            cpcProductService.addProduct(addAdProductVo, request.getIp());
        }
        if (sdAdGroupDao.exist(puid, shopId, groupId)) {
            cpcSdAdProductService.addProduct(addAdProductVo);
        }
        ProductAddResponse.Builder response = ProductAddResponse.newBuilder();
        ProductAddResponse.ResponseData.Builder data = ProductAddResponse.ResponseData.newBuilder();
        if (CollectionUtils.isNotEmpty(failInfoList)) {
            // 去重
            Map<String, ProductAddResponse.AddFailInfo> failInfoMap = failInfoList.stream().collect(Collectors.toMap(ProductAddResponse.AddFailInfo::getMsku, v -> v, (v1, v2) -> v1));
            data.addAllFailInfo(failInfoMap.values().stream().collect(Collectors.toList()));
        }
        response.setCode(Result.SUCCESS);
        response.setData(data.build());
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }

    private CpcQueryWordDto getParamDto(AllSearchTermDataRequest request) {
        CpcQueryWordDto dto = new CpcQueryWordDto();
        dto.setPuid(request.getPuid());
        dto.setMarketplaceId(request.getMarketplaceId());
        dto.setShopIdList(request.getShopIdsList());
        if (CollectionUtils.isNotEmpty(request.getPortfolioIdsList())) {
            dto.setPortfolioIds(request.getPortfolioIdsList());
        }
        if (CollectionUtils.isNotEmpty(request.getCampaignIdsList())) {
            dto.setCampaignIds(request.getCampaignIdsList());
        }
        dto.setGroupIdList(request.getAdGroupIdsList());
        dto.setSearchTermType(request.getSearchTermType());
        dto.setStart(request.getStart());
        dto.setEnd(request.getEnd());
        dto.setUuid(request.getUuid());

        //环比数据传参
        dto.setIsCompare(request.getIsCompare());
        dto.setCompareStartDate(request.getCompareStartDate());
        dto.setCompareEndDate(request.getCompareEndDate());

        if (StringUtils.isNotBlank(request.getQuery())) {
            dto.setQuery(request.getQuery());
        }
        if (CollectionUtils.isNotEmpty(request.getSpMatchTypesList())) {
            dto.setSpMatchTypes(request.getSpMatchTypesList());
        }
        if (CollectionUtils.isNotEmpty(request.getSbMatchTypesList())) {
            dto.setSbMatchTypes(request.getSbMatchTypesList());
        }
        dto.setQueryWordTargetType(request.getQueryWordTargetType());
        dto.setWordRoot(request.getWordRoot());
        dto.setSearchField(request.getSearchField());
        dto.setSearchType(request.getSearchType());
        dto.setSearchValue(request.getSearchValue());
        dto.setOrderField(request.getOrderField());
        dto.setOrderType(request.getOrderType());

        dto.setUseAdvanced(request.getUseAdvanced());
        if (request.getUseAdvanced()) {
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            dto.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            dto.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            dto.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? MathUtil.divide(new BigDecimal(advancedFilter.getSalesConversionRateMin()), new BigDecimal(100)) : null);
            dto.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? MathUtil.divide(new BigDecimal(advancedFilter.getSalesConversionRateMax()), new BigDecimal(100)) : null);
            dto.setSearchFrequencyRankMin(advancedFilter.hasSearchFrequencyRankMin() ? advancedFilter.getSearchFrequencyRankMin() : null);
            dto.setSearchFrequencyRankMax(advancedFilter.hasSearchFrequencyRankMax() ? advancedFilter.getSearchFrequencyRankMax() : null);
            dto.setCostMin(advancedFilter.hasCostMin() ? new BigDecimal(advancedFilter.getCostMin()) : null);
            dto.setCostMax(advancedFilter.hasCostMax() ? new BigDecimal(advancedFilter.getCostMax()) : null);
            dto.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            dto.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            dto.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            dto.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            dto.setCpcMin(advancedFilter.hasCpcMin() ? new BigDecimal(advancedFilter.getCpcMin()) : null);
            dto.setCpcMax(advancedFilter.hasCpcMax() ? new BigDecimal(advancedFilter.getCpcMax()) : null);
            dto.setCpaMin(advancedFilter.hasCpaMin() ? new BigDecimal(advancedFilter.getCpaMin()) : null);
            dto.setCpaMax(advancedFilter.hasCpaMax() ? new BigDecimal(advancedFilter.getCpaMax()) : null);
            dto.setAcosMin(advancedFilter.hasAcosMin() ? MathUtil.divide(new BigDecimal(advancedFilter.getAcosMin()), new BigDecimal(100)) : null);
            dto.setAcosMax(advancedFilter.hasAcosMax() ? MathUtil.divide(new BigDecimal(advancedFilter.getAcosMax()), new BigDecimal(100)) : null);
            dto.setRoasMin(advancedFilter.hasRoasMin() ? new BigDecimal(advancedFilter.getRoasMin()) : null);
            dto.setRoasMax(advancedFilter.hasRoasMax() ? new BigDecimal(advancedFilter.getRoasMax()) : null);
            dto.setSalesMin(advancedFilter.hasSalesMin() ? new BigDecimal(advancedFilter.getSalesMin()) : null);
            dto.setSalesMax(advancedFilter.hasSalesMax() ? new BigDecimal(advancedFilter.getSalesMax()) : null);
            dto.setAdSalesMin(advancedFilter.hasAdSalesMin() ? new BigDecimal(advancedFilter.getAdSalesMin()) : null);
            dto.setAdSalesMax(advancedFilter.hasAdSalesMax() ? new BigDecimal(advancedFilter.getAdSalesMax()) : null);
            dto.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? new BigDecimal(advancedFilter.getAdOtherSalesMin()) : null);
            dto.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? new BigDecimal(advancedFilter.getAdOtherSalesMax()) : null);
        }
        if (request.hasOnlyCount()) {
            dto.setOnlyCount(request.getOnlyCount());
        }
        return dto;
    }

    @Override
    public void allSearchTermList(AllSearchTermDataRequest request, StreamObserver<AllSearchTermDataResponse> responseObserver) {
        log.info("allSearchTermList request={}", ProtoBufUtil.toJsonStr(request));
        CpcQueryWordDto dto = getParamDto(request);
        Page page = new Page(request.getPageNo(), request.getPageSize());
        AllSearchTermDataResponse.AllSearchTermData data = cpcCommonService.allSearchTermList(request.getPuid(), dto, page);
        AllSearchTermDataResponse.Builder builder = AllSearchTermDataResponse.newBuilder();
        builder.setCode(Result.SUCCESS);
        builder.setMsg("success");
        builder.setData(data);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void allSearchTermAggregateData(AllSearchTermDataRequest request, StreamObserver<AllQueryWordAggregateDataResponse> responseObserver) {
        CpcQueryWordDto dto = getParamDto(request);

        AllQueryWordAggregateDataResponse.Builder builder = AllQueryWordAggregateDataResponse.newBuilder();
        AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo aggregateData = cpcCommonService.allSearchTermAggregateData(request.getPuid(), dto);
        builder.setData(aggregateData);
        builder.setMsg("success");
        builder.setCode(Int32Value.of(Result.SUCCESS));
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void searchTermSourceTargetDetail(SearchTermSourceTargetDetailRequest request, StreamObserver<SearchTermSourceTargetDetailResponse> responseObserver) {
        log.info("查询搜索词来源投放 request={}", ProtoBufUtil.toJsonStr(request));
        CpcQueryWordDto dto = new CpcQueryWordDto();
        dto.setPuid(request.getPuid());
        dto.setMarketplaceId(request.getMarketplaceId());
        dto.setSearchTermIds(request.getSearchTermIdsList());
        dto.setQuery(request.getQuery());
        dto.setShopIdList(request.getShopIdsList());
        dto.setStart(request.getStart());
        dto.setEnd(request.getEnd());

        SearchTermSourceTargetDetailResponse.ResponseData.Builder dataBuilder = SearchTermSourceTargetDetailResponse.ResponseData.newBuilder();
        dataBuilder.addAllRows(cpcCommonService.searchTermSourceTargetDetail(request.getPuid(), dto));
        SearchTermSourceTargetDetailResponse.Builder builder = SearchTermSourceTargetDetailResponse.newBuilder();
        builder.setCode(Result.SUCCESS);
        builder.setMsg("success");
        builder.setData(dataBuilder);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void allSearchTermExport(AllSearchTermDataRequest request, StreamObserver<BoolValue> responseObserver) {
        CpcQueryWordDto dto = getParamDto(request);
        dto.setUid(request.getUid());
        Long id = adManagePageExportTaskService.saveExportTask(dto.getPuid(), dto.getUid(), 0,
                AdManagePageExportTaskTypeEnum.ALL_SEARCH_TERM, dto.getStart(), dto.getEnd(), dto);
        responseObserver.onNext(BoolValue.newBuilder().setValue(Objects.nonNull(id)).build());
        responseObserver.onCompleted();
    }

    @Override
    public void setTemporaryData(SearchTermTemporaryDataRequest request, StreamObserver<SearchTermTemporaryDataResponse> responseObserver) {
        log.info("搜索词数据暂存缓存数据接口 puid={}", request.getPuid());
        SearchTermTemporaryDataResponse.Builder response = SearchTermTemporaryDataResponse.newBuilder();
        try {
            cpcPageIdsHandler.setTemporaryJsonData(request.getPuid(), request.getJsonData(), request.getPageSign());
            response.setCode(Result.SUCCESS);
            response.setMsg("success");
        } catch (Exception e) {
            log.error("出单搜索词暂存缓存数据失败，失败原因：{}", e);
            response.setCode(Result.ERROR);
            response.setMsg("error");
        }
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getTemporaryData(SearchTermTemporaryDataRequest request, StreamObserver<SearchTermTemporaryDataResponse> responseObserver) {
        log.info("搜索词数据获取缓存数据接口 puid={}", request.getPuid());
        SearchTermTemporaryDataResponse.Builder response = SearchTermTemporaryDataResponse.newBuilder();
        try {
            String jsonData = cpcPageIdsHandler.getTemporaryJsonData(request.getPageSign(), "");
            response.setCode(Result.SUCCESS);

            if (StringUtils.isNotBlank(jsonData)) {
                response.setMsg("success");
                byte[] jsonDataBytes = Base64.getDecoder().decode(jsonData);
                ByteArrayInputStream bis = new ByteArrayInputStream(jsonDataBytes);
                GZIPInputStream gzis = new GZIPInputStream(bis);
                StringBuilder output = new StringBuilder();
                byte[] buffer = new byte[1024];
                int len;
                while ((len = gzis.read(buffer)) > 0) {
                    output.append(new String(buffer, 0, len));
                }
                gzis.close();
                response.setData(output.toString());
            } else {
                response.setMsg("暂无缓存数据");
            }
        } catch (Exception e) {
            log.error("出单搜索词获取缓存数据失败，失败原因：{}", e);
            response.setCode(Result.ERROR);
            response.setMsg("error");
        }
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }
}

