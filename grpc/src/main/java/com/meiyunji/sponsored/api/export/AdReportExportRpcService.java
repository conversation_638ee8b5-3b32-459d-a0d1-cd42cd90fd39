package com.meiyunji.sponsored.api.export;

import com.alibaba.fastjson.JSON;
import com.amazon.advertising.mode.PredicateEnum;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.rpc.report.export.CommonReportExportRequest;
import com.meiyunji.sponsored.rpc.report.export.RPCAdReportExportServiceGrpc;
import com.meiyunji.sponsored.rpc.report.export.UrlResponse;
import com.meiyunji.sponsored.service.export.service.AdReportExportService;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author: wade
 * @date: 2021/11/2 9:39
 * @describe: 报告导出
 */
@GRpcService
@Slf4j
public class AdReportExportRpcService extends RPCAdReportExportServiceGrpc.RPCAdReportExportServiceImplBase {
    @Autowired
    AdReportExportService adReportExportService;

    /**
     * sp广告活动报告导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void campaignSpReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.campaignSpReportExport(request, responseObserver);
    }

    /**
     * sp广告位报告导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void spaceSpReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.spaceSpReportExport(request, responseObserver, null);
    }

    @Override
    public void amazonBusinessReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.spaceSpReportExport(request, responseObserver, PredicateEnum.SITEAMAZONBUSINESS.value());
    }

    /**
     * sp广告产品报告导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void productSpReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.productSpReportExport(request, responseObserver);
    }

    /**
     * sp广告组报告导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void groupSpReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.groupSpReportExport(request, responseObserver);
    }

    /**
     * sp广告投放报告导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void targetingSpReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.targetingSpReportExport(request, responseObserver);
    }

    /**
     * sp搜索词报告导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void searchTermSpExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.searchTermSpExport(request, responseObserver);
    }

    /**
     * sp已购买商品报告导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void purchasedItemExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.purchasedItemExport(request, responseObserver);
    }

    /**
     * 广告总流量与无效流量报告导出，（目前未启用）
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void grossAndInvalidTrafficAllSpReport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        UrlResponse.Builder builder = UrlResponse.newBuilder();
        try {
            adReportExportService.grossAndInvalidTrafficAllSpReport(request);
        } catch (Exception e) {
            log.error("create flowReport task error, ", e);
            builder.setMsg("create flowReport task error");
            builder.setCode(Int32Value.of(Result.ERROR));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sb广告活动报告导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void campaignSbReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.campaignSbReportExport(request, responseObserver);
    }

    /**
     * sb搜索词报告导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void searchTermSbExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.searchTermSbExport(request, responseObserver);
    }

    /**
     * sb投放报告导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void targetingSbReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.targetingSbReportExport(request, responseObserver);
    }

    /**
     * sb广告位报告导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void spaceSbReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.spaceSbReportExport(request, responseObserver);
    }

    /**
     * sd广告活动报告导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void campaignSdReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.campaignSdReportExport(request, responseObserver);
    }

    /**
     * sd广告组报告导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void groupSdReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.groupSdReportExport(request, responseObserver);
    }

    /**
     * sd广告产品报告导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void productSdReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.productSdReportExport(request, responseObserver);
    }

    /**
     * sd投放报告导出，前端用的是targetReportListSdReportExport，但是openapi可能还在用这个
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void targetingSdReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.targetingSdReportExport(request, responseObserver);
    }

    /**
     * sd匹配的目标报告导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void campaignMatchedTargetSdReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.campaignMatchedTargetSdReportExport(request, responseObserver);
    }

    /**
     * sd已购买商品报告导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void sdAsinReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.sdAsinReportExport(request, responseObserver);
    }

    /**
     * sd投放报告数据导出：包含商品投放和受众投放
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void targetReportListSdReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        adReportExportService.targetReportListSdReportExport(request, responseObserver);
    }

}
