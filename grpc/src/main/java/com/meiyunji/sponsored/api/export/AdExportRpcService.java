package com.meiyunji.sponsored.api.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sellfox.ams.api.service.AdReportHourlyAMSApiServiceGrpc;
import com.meiyunji.sellfox.ams.api.service.AdReportWeeklyAMSApiServiceGrpc;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.grpc.common.*;
import com.meiyunji.sponsored.grpc.entry.ReportDateModelPb;
import com.meiyunji.sponsored.rpc.adAggregateHour.AdPageBasicData;
import com.meiyunji.sponsored.rpc.adCommon.NeTargetReportAdvanceFilters;
import com.meiyunji.sponsored.rpc.adCommon.NeTargetReportFilter;
import com.meiyunji.sponsored.rpc.adCommon.OperationLogVo;
import com.meiyunji.sponsored.rpc.asins.PageListAsinsRequest;
import com.meiyunji.sponsored.rpc.export.*;
import com.meiyunji.sponsored.rpc.vo.AdAdvancedFilterData;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.autoRule.service.IAdvertiseAutoRuleExecuteRecordService;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.constants.AdManagePageExportTaskTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.BrandMessageConstants;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportAdvanceFiltersDto;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportFilterDto;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.ICpcQueryReportDetailService;
import com.meiyunji.sponsored.service.cpc.service2.*;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbAdsService;
import com.meiyunji.sponsored.service.cpc.service2.sp.*;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.service.AdReportExportService;
import com.meiyunji.sponsored.service.log.dao.IAdManageOperationLogDao;
import com.meiyunji.sponsored.service.log.enums.OperationLogActionEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogTargetEnum;
import com.meiyunji.sponsored.service.log.qo.OperationLogQo;
import com.meiyunji.sponsored.service.log.vo.AdManageLogVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.KeywordViewHourParam;
import com.meiyunji.sponsored.service.reportHour.service.*;
import com.meiyunji.sponsored.service.reportHour.vo.*;
import com.meiyunji.sponsored.service.vo.AdsExportVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2021/10/25 21:30
 * @describe: 导出
 */
@GRpcService
@Slf4j
public class AdExportRpcService extends RPCAdExportServiceGrpc.RPCAdExportServiceImplBase {

    @Autowired
    private ICpcCampaignService cpcCampaignService;
    @Autowired
    private ICpcProductService cpcProductService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private ICpcAdGroupService cpcAdGroupService;
    @Autowired
    private ICpcTargetingService cpcTargetingService;
    @Autowired
    private ICpcNeKeywordsService cpcNeKeywordsService;
    @Autowired
    private ICpcSpCampaignService cpcSpCampaignService;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private IAdManageOperationLogDao manageOperationLogDao;
    @Autowired
    private ICpcPortfolioService cpcPortfolioService;
    @Autowired
    private ICpcKeywordsLibService cpcKeywordsLibService;
    @Autowired
    private IAmazonAdCampaignHourReportService amazonAdCampaignHourReportService;
    @Autowired
    private ICpcSbAdsService cpcSbAdsService;
    @Autowired
    private IAmazonAdKeywordHourReportService amazonAdKeywordHourReportService;
    @Autowired
    private IAmazonAdTargetHourReportService amazonAdTargetHourReportService;
    @Autowired
    private IAmazonAdProductHourReportService amazonAdProductHourReportService;
    @Autowired
    private IAmazonAdInvoiceSummaryDao amazonAdInvoiceSummaryDao;
    @Autowired
    private IAmazonAdPlacementHourReportService amazonAdPlacementHourReportService;
    @Autowired
    private ICpcCommonService cpcCommonService;

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;


    @Autowired
    private IAdHourlyReportAnalysisService adHourlyReportAnalysisService;

    @Autowired
    private IAdWeeklyReportAnalysisService adWeeklyReportAnalysisService;

    @Qualifier("adHourlyFeedBlockingStub")
    @Autowired
    private AdReportHourlyAMSApiServiceGrpc.AdReportHourlyAMSApiServiceBlockingStub adHourlyFeedBlockingStub;

    @Qualifier("adReportWeeklyAMSApiServiceBlockingStub")
    @Autowired
    private AdReportWeeklyAMSApiServiceGrpc.AdReportWeeklyAMSApiServiceBlockingStub adReportWeeklyAMSApiServiceBlockingStub;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdFeedReportService amazonAdFeedReportService;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Autowired
    private IAmazonMarketingStreamDataDao amazonMarketingStreamDataDao;

    @Autowired
    private ICpcQueryReportDetailService cpcQueryReportDetailService;
    @Autowired
    private IAdManagePageExportTaskService adManagePageExportTaskService;
    @Autowired
    private ICpcRepeatTargetingService cpcRepeatTargetingService;

    @Resource
    private IAdvertiseAutoRuleExecuteRecordService advertiseAutoRuleExecuteRecordService;
    @Autowired
    private DynamicRefreshConfiguration configuration;

    @Autowired
    private ICpcAsinsLibService cpcAsinsLibService;

    @Autowired
    private AdReportExportService adReportExportService;

    @Override
    public void adManageExportTaskPage(AdManageExportTaskPageRequest request, StreamObserver<AdManageExportTaskPageResponse> responseObserver) {
        AdManageExportTaskPageResponse.Builder builder = AdManageExportTaskPageResponse.newBuilder();
        AdManageExportTaskPageResponse.Page.Builder voPage = AdManageExportTaskPageResponse.Page.newBuilder();
        if (!request.hasPuid()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误，请联系管理员");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        Page<AdManageExportTaskPageVo> page = adManagePageExportTaskService.getPageList(request.getPuid().getValue(), request.getShopId().getValue(),
                request.getPageNo(), request.getPageSize(), request.getType(), request.getUid().getValue());
        voPage.setPageNo(Int32Value.of(page.getPageNo()));
        voPage.setPageSize(Int32Value.of(page.getPageSize()));
        voPage.setTotalPage(Int32Value.of(page.getTotalPage()));
        voPage.setTotalSize(Int32Value.of(page.getTotalSize()));
        List<AdManageExportTaskPageResponse.AdManageExportTaskVo> voList = new ArrayList<>();
        page.getRows().forEach(e -> {
            AdManageExportTaskPageResponse.AdManageExportTaskVo.Builder vo = AdManageExportTaskPageResponse.AdManageExportTaskVo.newBuilder();
            vo.setCreateTime(e.getCreateTime());
            vo.setUserName(e.getUserName());
            vo.setPage(e.getPage());
            vo.setStartDate(e.getStartDate());
            vo.setEndDate(e.getEndDate());
            vo.setProgressBar(e.getProgressBar());
            if (CollectionUtils.isNotEmpty(e.getExportUrlList())) {
                vo.addAllExportUrl(e.getExportUrlList());
            }
            voList.add(vo.build());
        });
        voPage.addAllRows(voList);
        builder.setCode(Int32Value.of(Result.SUCCESS));
        builder.setData(voPage.build());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void adPortfolioData(AdPortfolioDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("广告组合导出 request {}", request);
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        PortfolioPageParam param = new PortfolioPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setUid(request.getUid().getValue());
        int count = 0;
        // 返回下载的url
        List<String> urls = Lists.newLinkedList();
        //做参数校验  与查询列表时,校验保持一致
        String err = checkPortfolioPageParam(param);
        if (StringUtils.isNotBlank(err)) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg(err);
        } else {
            //插入任务到kafka中
            Long id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(),
                    AdManagePageExportTaskTypeEnum.PORTFOLIO, param.getStartDate(), param.getEndDate(), param);
            if (id == null) {
                urlBuilder.setCode(Int32Value.of(Result.ERROR));
                urlBuilder.setMsg("新建任务异常，请联系管理员");
            } else {
                urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
            }
        }
        responseObserver.onNext(urlBuilder.build());
        responseObserver.onCompleted();
    }

    // 广告组合页面参数校验
    private String checkPortfolioPageParam(PortfolioPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }
        if (param.getIsHidden() == null) {
            param.setIsHidden(true);
        }
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }
        if (StringUtils.isNotBlank(param.getOrderField()) && !Constants.isADperformanceOrderField(param.getOrderField())) {
            return "请求参数错误";
        }
        return null;
    }

    /**
     * 广告活动导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void campaignData(CampaignDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("广告活动导出 request {}", request);
        UrlResponse.Builder urlBulider = UrlResponse.newBuilder();
        CampaignPageParam param = new CampaignPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setUid(request.getUid().getValue());
        param.setUuid(request.getUuid());
        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }
        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);

            param.setAddToCartMin(advancedFilter.hasAddToCartMin() ? advancedFilter.getAddToCartMin() : null);
            param.setAddToCartMax(advancedFilter.hasAddToCartMax() ? advancedFilter.getAddToCartMax() : null);
            param.setVideo5SecondViewsMin(advancedFilter.hasVideo5SecondViewsMin() ? advancedFilter.getVideo5SecondViewsMin() : null);
            param.setVideo5SecondViewsMax(advancedFilter.hasVideo5SecondViewsMax() ? advancedFilter.getVideo5SecondViewsMax() : null);
            param.setVideoCompleteViewsMin(advancedFilter.hasVideoCompleteViewsMin() ? advancedFilter.getVideoCompleteViewsMin() : null);
            param.setVideoCompleteViewsMax(advancedFilter.hasVideoCompleteViewsMax() ? advancedFilter.getVideoCompleteViewsMax() : null);
            param.setViewabilityRateMin(advancedFilter.hasViewabilityRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMin())) : null);
            param.setViewabilityRateMax(advancedFilter.hasViewabilityRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMax())) : null);
            param.setViewClickThroughRateMin(advancedFilter.hasViewClickThroughRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMin())) : null);
            param.setViewClickThroughRateMax(advancedFilter.hasViewClickThroughRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMax())) : null);
            param.setBrandedSearchesMin(advancedFilter.hasBrandedSearchesMin() ? advancedFilter.getBrandedSearchesMin() : null);
            param.setBrandedSearchesMax(advancedFilter.hasBrandedSearchesMax() ? advancedFilter.getBrandedSearchesMax() : null);
            param.setCumulativeReachMin(advancedFilter.hasCumulativeReachMin() ? advancedFilter.getCumulativeReachMin() : null);
            param.setCumulativeReachMax(advancedFilter.hasCumulativeReachMax() ? advancedFilter.getCumulativeReachMax() : null);
            param.setImpressionsFrequencyAverageMin(advancedFilter.hasImpressionsFrequencyAverageMin() ? new BigDecimal(String.valueOf(advancedFilter.getImpressionsFrequencyAverageMin())) : null);
            param.setImpressionsFrequencyAverageMax(advancedFilter.hasImpressionsFrequencyAverageMax() ? new BigDecimal(String.valueOf(advancedFilter.getImpressionsFrequencyAverageMax())) : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            // 搜索结果首页首位IS
            param.setTopImpressionShareMin(advancedFilter.hasTopImpressionShareMin() ? new BigDecimal(String.valueOf(advancedFilter.getTopImpressionShareMin())) : null);
            param.setTopImpressionShareMax(advancedFilter.hasTopImpressionShareMax() ? new BigDecimal(String.valueOf(advancedFilter.getTopImpressionShareMax())) : null);
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
        }
        if (request.hasFilterTargetType()) {
            param.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasDailyBudgetMin()) {
            param.setDailyBudgetMin(BigDecimal.valueOf(request.getDailyBudgetMin()));
        }
        if (request.hasDailyBudgetMax()) {
            param.setDailyBudgetMax(BigDecimal.valueOf(request.getDailyBudgetMax()));
        }
        if (request.hasFilterStartDate()) {
            param.setFilterStartDate(request.getFilterStartDate());
        }
        if (request.hasFilterEndDate()) {
            param.setFilterEndDate(request.getFilterEndDate());
        }
        if (request.hasProductType()) {
            param.setProductType(request.getProductType());
        }
        if (request.hasProductValue()) {
            param.setProductValue(request.getProductValue());
        }
        if (request.hasExportSortField()) {
            param.setExportSortField(request.getExportSortField());
        }
        if (request.hasFreezeNum()) {
            param.setFreezeNum(request.getFreezeNum());
        }
        if (CollectionUtils.isNotEmpty(request.getAdTagIdsList())) {
            param.setAdTagIdList(request.getAdTagIdsList());
        }
        if (CollectionUtils.isNotEmpty(request.getAdStrategyTypeListList())) {
            param.setAdStrategyTypeList(request.getAdStrategyTypeListList());
        }
        //做参数校验  与查询列表时,校验保持一致
        String err = checkCampaignPageParam(param);
        if (StringUtils.isNotBlank(err)) {
            urlBulider.setCode(Int32Value.of(Result.ERROR));
            urlBulider.setMsg(err);
        } else {
            //插入任务
            Long id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(),
                AdManagePageExportTaskTypeEnum.CAMPAIGN, param.getStartDate(), param.getEndDate(), param);
            if (id == null) {
                urlBulider.setCode(Int32Value.of(Result.ERROR));
                urlBulider.setMsg("新建任务异常，请联系管理员");
            } else {
                urlBulider.setCode(Int32Value.of(Result.SUCCESS));
            }
        }
        responseObserver.onNext(urlBulider.build());
        responseObserver.onCompleted();
    }

    /**
     * 保留两位小数，四舍五入
     */
    public static String getAdCostPerClick(String obj) {
        if (StringUtils.isNotBlank(obj)) {
            return BigDecimal.valueOf(Double.parseDouble(obj)).setScale(2, RoundingMode.HALF_UP).toString();
        } else {
            return "0.00";
        }
    }

    private String getDateState(String endDate) {
        if (endDate != null) {
            return endDate;
        } else {
            return "无结束日期";
        }
    }

    private String getStartDateState(String budgetStartDate) {
        if (budgetStartDate != null) {
            return budgetStartDate;
        } else {
            return "无开始日期";
        }
    }

    /**
     * 字符串数字取整+%
     */
    private String getPlacementProductPage(String placement) {
        if (StringUtils.isNotBlank(placement)) {
            double placementNum = Double.parseDouble(placement);
            int placementNumType = (int) placementNum;
            return placementNumType + "%";
        } else {
            return "0%";
        }
    }

    /**
     * 字符串数字取整+%
     */
    private String getPlacementRestOfSearchPage(String placement) {
        if (StringUtils.isNotBlank(placement)) {
            double placementNum = Double.parseDouble(placement);
            int placementNumType = (int) placementNum;
            return placementNumType + "%";
        } else {
            return "0%";
        }
    }

    /**
     * 保留两位小数+%
     */
    public static String modifyFormat(String obj) {
        if (StringUtils.isNotBlank(obj)) {
            BigDecimal bd = new BigDecimal(obj);
            DecimalFormat df = new DecimalFormat("#.00");
            //大于0 小于1
            if (bd.compareTo(BigDecimal.ZERO) > 0 && bd.compareTo(new BigDecimal(1)) < 0) {
                return "0" + df.format(bd).toString() + "%";
            } else if (bd.compareTo(BigDecimal.ZERO) == 0) {
                return "0.00%";
            } else {
                return df.format(bd).toString() + "%";
            }
        } else {
            return null;
        }
    }

    /**
     * 保留两位小数+% 默认返回0
     */
    public static String modifyFormatDefaultZero(String obj) {
        if (StringUtils.isNotBlank(obj)) {
            return modifyFormat(obj);
        } else {
            return modifyFormat("0.00");
        }
    }

    /**
     * 保留两位小数
     */
    public static String formatToNumber(String obj) {
        if (StringUtils.isNotBlank(obj)) {
            BigDecimal bd = new BigDecimal(obj);
            DecimalFormat df = new DecimalFormat("#.00");
            if (bd.compareTo(BigDecimal.ZERO) > 0 && bd.compareTo(new BigDecimal(1)) < 0) {
                return "0" + df.format(bd);
            } else if (bd.compareTo(BigDecimal.ZERO) == 0) {
                return "0.00";
            } else {
                return df.format(bd);
            }
        } else {
            return "0.00";
        }
    }

    private String checkCampaignPageParam(CampaignPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(param.getStartDate()) || org.apache.commons.lang3.StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }
        //策略类型校验
        if (StringUtils.isNotBlank(param.getStrategyType()) && !"none".equalsIgnoreCase(param.getStrategyType())) {
            if (org.apache.commons.lang3.StringUtils.isBlank(StrategyEnum.getStrategyValue(param.getStrategyType()))) {
                return "请求参数错误";
            }
        }
        //预算状态校验
        if (StringUtils.isNotBlank(param.getBudgetState())) {
            CampaignPageParam.BudgetStateEnum budgetStateEnum = UCommonUtil.getByCode(param.getBudgetState(), CampaignPageParam.BudgetStateEnum.class);
            if (budgetStateEnum == null) {
                return "请求参数错误";
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField())) {
            CampaignPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(param.getSearchField(), CampaignPageParam.SearchFieldEnum.class);
            if (searchFieldEnum == null) {
                return "请求参数错误";
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField())) {
            CampaignPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(param.getSearchField(), CampaignPageParam.SearchFieldEnum.class);
            if (searchFieldEnum == null) {
                return "请求参数错误";
            }
        }
        if (StringUtils.isNotBlank(param.getOrderField())) {
            if (!Constants.isADOrderField(param.getOrderField(), CampaignPageVo.class)) {
                return "请求参数错误";
            }
        }

        if (StringUtils.isNotBlank(param.getExportSortField())) {
            //排序字段校验
            try {
                String[] split = param.getExportSortField().split(",");
                for (String s : split) {
                    if (!AdvertisingCampaignExportFieldEnum.getPoParamKeyList().contains(s)) {
                        return "请求参数错误" + s;
                    }
                }
            } catch (Exception e) {
                return "请求参数错误" + param.getExportSortField();
            }
        }

        return null;
    }

    /**
     * 广告组导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void adGroupData(AdGroupDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("广告组导出 request {}", request);
        AdGroupDataResponse.Builder builder = AdGroupDataResponse.newBuilder();
        UrlResponse.Builder urlBuiler = UrlResponse.newBuilder();
        // grpc转param
        GroupPageParam param = grpcToParam(request);
        //做参数校验  与查询列表时,校验保持一致
        String err = checkGroupPageParam(param);
        if (StringUtils.isNotBlank(err)) {
            urlBuiler.setCode(Int32Value.of(Result.ERROR));
            urlBuiler.setMsg(err);
        } else {
            Long id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(),
                    AdManagePageExportTaskTypeEnum.GROUP, param.getStartDate(), param.getEndDate(), param);
            if (id == null) {
                urlBuiler.setCode(Int32Value.of(Result.ERROR));
                urlBuiler.setMsg("新建任务异常，请联系管理员");
            } else {
                urlBuiler.setCode(Int32Value.of(Result.SUCCESS));
            }
        }
        responseObserver.onNext(urlBuiler.build());
        responseObserver.onCompleted();
    }

    private static GroupPageParam grpcToParam(AdGroupDataRequest request) {
        GroupPageParam param = new GroupPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setUid(request.getUid().getValue());
        param.setUuid(request.getUuid());
        param.setCurrency(request.getCurrency());
        List<String> urls = Lists.newLinkedList();
        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }
        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setAddToCartMin(advancedFilter.hasAddToCartMin() ? advancedFilter.getAddToCartMin() : null);
            param.setAddToCartMax(advancedFilter.hasAddToCartMax() ? advancedFilter.getAddToCartMax() : null);
            param.setVideo5SecondViewsMin(advancedFilter.hasVideo5SecondViewsMin() ? advancedFilter.getVideo5SecondViewsMin() : null);
            param.setVideo5SecondViewsMax(advancedFilter.hasVideo5SecondViewsMax() ? advancedFilter.getVideo5SecondViewsMax() : null);
            param.setVideoCompleteViewsMin(advancedFilter.hasVideoCompleteViewsMin() ? advancedFilter.getVideoCompleteViewsMin() : null);
            param.setVideoCompleteViewsMax(advancedFilter.hasVideoCompleteViewsMax() ? advancedFilter.getVideoCompleteViewsMax() : null);
            param.setViewabilityRateMin(advancedFilter.hasViewabilityRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMin())) : null);
            param.setViewabilityRateMax(advancedFilter.hasViewabilityRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMax())) : null);
            param.setViewClickThroughRateMin(advancedFilter.hasViewClickThroughRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMin())) : null);
            param.setViewClickThroughRateMax(advancedFilter.hasViewClickThroughRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMax())) : null);
            param.setBrandedSearchesMin(advancedFilter.hasBrandedSearchesMin() ? advancedFilter.getBrandedSearchesMin() : null);
            param.setBrandedSearchesMax(advancedFilter.hasBrandedSearchesMax() ? advancedFilter.getBrandedSearchesMax() : null);
            param.setCumulativeReachMin(advancedFilter.hasCumulativeReachMin() ? advancedFilter.getCumulativeReachMin() : null);
            param.setCumulativeReachMax(advancedFilter.hasCumulativeReachMax() ? advancedFilter.getCumulativeReachMax() : null);
            param.setImpressionsFrequencyAverageMin(advancedFilter.hasImpressionsFrequencyAverageMin() ? new BigDecimal(String.valueOf(advancedFilter.getImpressionsFrequencyAverageMin())) : null);
            param.setImpressionsFrequencyAverageMax(advancedFilter.hasImpressionsFrequencyAverageMax() ? new BigDecimal(String.valueOf(advancedFilter.getImpressionsFrequencyAverageMax())) : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
        }
        if (request.hasFilterTargetType()) {
            param.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasBidMin()) {
            param.setBidMin(BigDecimal.valueOf(request.getBidMin()));
        }
        if (request.hasBidMax()) {
            param.setBidMax(BigDecimal.valueOf(request.getBidMax()));
        }
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }
        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }
        if(CollectionUtils.isNotEmpty(request.getAdStrategyTypeListList())){
            param.setAdStrategyTypeList(request.getAdStrategyTypeListList());
        }
        return param;
    }

    private String checkGroupPageParam(GroupPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (StringUtils.isNotBlank(param.getType())) {
            if (!Arrays.asList(Constants.SP, Constants.SD, Constants.SB).contains(param.getType())) {
                return "请求参数错误";
            }
        }

        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(param.getStartDate()) || org.apache.commons.lang3.StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }

        if (StringUtils.isNotBlank(param.getSearchField())) {
            GroupPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(param.getSearchField(), GroupPageParam.SearchFieldEnum.class);
            if (searchFieldEnum == null) {
                return "请求参数错误";
            }
        }

        if (StringUtils.isNotBlank(param.getOrderField())) {
            if (!Constants.isADOrderField(param.getOrderField(), GroupPageVo.class)) {
                return "请求参数错误";
            }
        }
        return null;
    }

    /**
     * 产品导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void adProductData(AdProductDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("产品导出 request {}", request);
        AdProductDataResponse.Builder builder = AdProductDataResponse.newBuilder();
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        AdProductPageParam param = new AdProductPageParam();
        List<String> urls = Lists.newLinkedList();
        BeanUtils.copyProperties(request, param);
        param.setUid(request.getUid().getValue());
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);

            param.setAddToCartMin(advancedFilter.hasAddToCartMin() ? advancedFilter.getAddToCartMin() : null);
            param.setAddToCartMax(advancedFilter.hasAddToCartMax() ? advancedFilter.getAddToCartMax() : null);
            param.setVideoCompleteViewsMin(advancedFilter.hasVideoCompleteViewsMin() ? advancedFilter.getVideoCompleteViewsMin() : null);
            param.setVideoCompleteViewsMax(advancedFilter.hasVideoCompleteViewsMax() ? advancedFilter.getVideoCompleteViewsMax() : null);
            param.setViewabilityRateMin(advancedFilter.hasViewabilityRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMin())) : null);
            param.setViewabilityRateMax(advancedFilter.hasViewabilityRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMax())) : null);
            param.setViewClickThroughRateMin(advancedFilter.hasViewClickThroughRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMin())) : null);
            param.setViewClickThroughRateMax(advancedFilter.hasViewClickThroughRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMax())) : null);
            param.setBrandedSearchesMin(advancedFilter.hasBrandedSearchesMin() ? advancedFilter.getBrandedSearchesMin() : null);
            param.setBrandedSearchesMax(advancedFilter.hasBrandedSearchesMax() ? advancedFilter.getBrandedSearchesMax() : null);
            param.setCumulativeReachMin(advancedFilter.hasCumulativeReachMin() ? advancedFilter.getCumulativeReachMin() : null);
            param.setCumulativeReachMax(advancedFilter.hasCumulativeReachMax() ? advancedFilter.getCumulativeReachMax() : null);
            param.setImpressionsFrequencyAverageMin(advancedFilter.hasImpressionsFrequencyAverageMin() ? new BigDecimal(String.valueOf(advancedFilter.getImpressionsFrequencyAverageMin())) : null);
            param.setImpressionsFrequencyAverageMax(advancedFilter.hasImpressionsFrequencyAverageMax() ? new BigDecimal(String.valueOf(advancedFilter.getImpressionsFrequencyAverageMax())) : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
        }

        String err = checkAdProductParams(param);
        if (StringUtils.isNotBlank(err)) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg(err);
        } else {
            //插入任务
            Long id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(),
                    AdManagePageExportTaskTypeEnum.PRODUCT, param.getStartDate(), param.getEndDate(), param);
            if (id == null) {
                urlBuilder.setCode(Int32Value.of(Result.ERROR));
                urlBuilder.setMsg("新建任务异常，请联系管理员");
            } else {
                urlBuilder.addAllUrls(urls);
                urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
            }
        }
        responseObserver.onNext(urlBuilder.build());
        responseObserver.onCompleted();
    }
    private String checkAdProductParams(AdProductPageParam param) {
        //做参数校验  与查询列表时,校验保持一致
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(param.getStartDate()) || org.apache.commons.lang3.StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }

        if (StringUtils.isNotBlank(param.getType())) {
            if (!Arrays.asList(Constants.SP, Constants.SD).contains(param.getType())) {
                return "请求参数错误";
            }
        }
        return "";
    }

    /**
     * 广告位导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void adPlacementData(AdPlacementDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("广告位导出 request {}", request);
        AdPlacementDataResponse.Builder builder = AdPlacementDataResponse.newBuilder();
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        PlacementPageParam param = new PlacementPageParam();
        BeanUtils.copyProperties(request, param);
        param.setCampaignIds(request.getCampaignIdsList());
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setUid(request.getUid().getValue());
        param.setUuid(request.getUuid());
        param.setCurrency(request.getCurrency());
        param.setIcon(request.getIcon());
        if (request.hasState()) {
            param.setState(request.getState());
        }
        if (request.hasStatus()) {
            param.setStatus(request.getStatus());
        }
        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }
        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
        }
        String err = checkAdPlacementParams(param);
        if (StringUtils.isNotBlank(err)) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg(err);
        } else {
            Long id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(),
                    AdManagePageExportTaskTypeEnum.PLACEMENT, param.getStartDate(), param.getEndDate(), param);
            if (id == null) {
                urlBuilder.setCode(Int32Value.of(Result.ERROR));
                urlBuilder.setMsg("新建任务异常，请联系管理员");
            } else {
                urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
            }
        }
        responseObserver.onNext(urlBuilder.build());
        responseObserver.onCompleted();
    }

    private String checkAdPlacementParams(PlacementPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }

        if (org.apache.commons.lang3.StringUtils.isBlank(param.getStartDate()) || org.apache.commons.lang3.StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }
        if (StringUtils.isNotBlank(param.getPredicate())) {
            if (StringUtils.isBlank(CpcPlaceNameEnum.getPredicateValue(param.getPredicate()))) {
                return "请求参数错误";
            }
        }
        if (StringUtils.isNotBlank(param.getStrategyType())) {
            if (StringUtils.isBlank(StrategyEnum.getStrategyValue(param.getStrategyType()))) {
                return "请求参数错误";
            }
        }
        return "";
    }

    /**
     * 投放导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void targetingData(TargetingDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("投放导出 request {}", request);
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        // grpcToParam
        TargetingPageParam param = grpcToParam(request);
        //做参数校验
        String err = checkTargetingPageParam(param);
        if (StringUtils.isNotBlank(err)) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg(err);
        } else {
            //插入任务
            Long id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(),
                    AdManagePageExportTaskTypeEnum.TARGET, param.getStartDate(), param.getEndDate(), param);
            if (id == null) {
                urlBuilder.setCode(Int32Value.of(Result.ERROR));
                urlBuilder.setMsg("新建任务异常，请联系管理员");
            } else {
                urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
            }
        }
        responseObserver.onNext(urlBuilder.build());
        responseObserver.onCompleted();
    }

    private static TargetingPageParam grpcToParam(TargetingDataRequest request) {
        TargetingPageParam param = new TargetingPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setUid(request.getUid().getValue());
        param.setUuid(request.getUuid());
        param.setCurrency(request.getCurrency());
        param.setIcon(request.getIcon());
        param.setChosenTargetType(request.getChosenTargetType());
        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }
        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);
            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);
            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);
            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);
            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);
            param.setBrandNewBuyerOrderConversionRateMin(advancedFilter.hasBrandNewBuyerOrderConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getBrandNewBuyerOrderConversionRateMin()) : null);
            param.setBrandNewBuyerOrderConversionRateMax(advancedFilter.hasBrandNewBuyerOrderConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getBrandNewBuyerOrderConversionRateMax()) : null);
            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);
            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);
            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);
            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setAddToCartMin(advancedFilter.hasAddToCartMin() ? advancedFilter.getAddToCartMin() : null);
            param.setAddToCartMax(advancedFilter.hasAddToCartMax() ? advancedFilter.getAddToCartMax() : null);
            param.setVideo5SecondViewsMin(advancedFilter.hasVideo5SecondViewsMin() ? advancedFilter.getVideo5SecondViewsMin() : null);
            param.setVideo5SecondViewsMax(advancedFilter.hasVideo5SecondViewsMax() ? advancedFilter.getVideo5SecondViewsMax() : null);
            param.setVideoCompleteViewsMin(advancedFilter.hasVideoCompleteViewsMin() ? advancedFilter.getVideoCompleteViewsMin() : null);
            param.setVideoCompleteViewsMax(advancedFilter.hasVideoCompleteViewsMax() ? advancedFilter.getVideoCompleteViewsMax() : null);
            param.setViewabilityRateMin(advancedFilter.hasViewabilityRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMin())) : null);
            param.setViewabilityRateMax(advancedFilter.hasViewabilityRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMax())) : null);
            param.setViewClickThroughRateMin(advancedFilter.hasViewClickThroughRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMin())) : null);
            param.setViewClickThroughRateMax(advancedFilter.hasViewClickThroughRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMax())) : null);
            param.setBrandedSearchesMin(advancedFilter.hasBrandedSearchesMin() ? advancedFilter.getBrandedSearchesMin() : null);
            param.setBrandedSearchesMax(advancedFilter.hasBrandedSearchesMax() ? advancedFilter.getBrandedSearchesMax() : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            // 搜索结果首页首位IS
            param.setTopImpressionShareMin(advancedFilter.hasTopImpressionShareMin() ? new BigDecimal(String.valueOf(advancedFilter.getTopImpressionShareMin())) : null);
            param.setTopImpressionShareMax(advancedFilter.hasTopImpressionShareMax() ? new BigDecimal(String.valueOf(advancedFilter.getTopImpressionShareMax())) : null);
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);

        }

        if (request.hasFilterTargetType()) {
            param.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasBidMin()) {
            param.setBidMin(BigDecimal.valueOf(request.getBidMin()));
        }
        if (request.hasBidMax()) {
            param.setBidMax(BigDecimal.valueOf(request.getBidMax()));
        }
        if (CollectionUtils.isNotEmpty(request.getAdStrategyTypeListList())) {
            param.setAdStrategyTypeList(request.getAdStrategyTypeListList());
        }
        return param;
    }

    /**
     * 关键词导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void keywordData(KeywordDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("关键词导出 request {}", request);
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        KeywordsPageParam param = grpcToParam(request);
        //做参数校验
        String err = checkKeywordsPageParam(param);
        if (StringUtils.isNotBlank(err)) {
            throw new SponsoredBizException(err);
        } else {
            //插入任务
            Long id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(),
                    AdManagePageExportTaskTypeEnum.KEYWORD, param.getStartDate(), param.getEndDate(), param);
            if (id == null) {
                urlBuilder.setCode(Int32Value.of(Result.ERROR));
                urlBuilder.setMsg("新建任务异常，请联系管理员");
            } else {
                urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
            }
        }
        responseObserver.onNext(urlBuilder.build());
        responseObserver.onCompleted();
    }

    private static KeywordsPageParam grpcToParam(KeywordDataRequest request) {
        KeywordsPageParam param = new KeywordsPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setUid(request.getUid().getValue());
        param.setUuid(request.getUuid());
        param.setCurrency(request.getCurrency());
        param.setIcon(request.getIcon());

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);
            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);
            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);
            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);
            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);
            param.setBrandNewBuyerOrderConversionRateMin(advancedFilter.hasBrandNewBuyerOrderConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getBrandNewBuyerOrderConversionRateMin()) : null);
            param.setBrandNewBuyerOrderConversionRateMax(advancedFilter.hasBrandNewBuyerOrderConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getBrandNewBuyerOrderConversionRateMax()) : null);
            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);
            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);
            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);
            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);

            param.setVideo5SecondViewsMin(advancedFilter.hasVideo5SecondViewsMin() ? advancedFilter.getVideo5SecondViewsMin() : null);
            param.setVideo5SecondViewsMax(advancedFilter.hasVideo5SecondViewsMax() ? advancedFilter.getVideo5SecondViewsMax() : null);
            param.setVideoCompleteViewsMin(advancedFilter.hasVideoCompleteViewsMin() ? advancedFilter.getVideoCompleteViewsMin() : null);
            param.setVideoCompleteViewsMax(advancedFilter.hasVideoCompleteViewsMax() ? advancedFilter.getVideoCompleteViewsMax() : null);
            param.setViewabilityRateMin(advancedFilter.hasViewabilityRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMin())) : null);
            param.setViewabilityRateMax(advancedFilter.hasViewabilityRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewabilityRateMax())) : null);
            param.setViewClickThroughRateMin(advancedFilter.hasViewClickThroughRateMin() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMin())) : null);
            param.setViewClickThroughRateMax(advancedFilter.hasViewClickThroughRateMax() ? new BigDecimal(String.valueOf(advancedFilter.getViewClickThroughRateMax())) : null);
            param.setBrandedSearchesMin(advancedFilter.hasBrandedSearchesMin() ? advancedFilter.getBrandedSearchesMin() : null);
            param.setBrandedSearchesMax(advancedFilter.hasBrandedSearchesMax() ? advancedFilter.getBrandedSearchesMax() : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            // 搜索结果首页首位IS
            param.setTopImpressionShareMin(advancedFilter.hasTopImpressionShareMin() ? new BigDecimal(String.valueOf(advancedFilter.getTopImpressionShareMin())) : null);
            param.setTopImpressionShareMax(advancedFilter.hasTopImpressionShareMax() ? new BigDecimal(String.valueOf(advancedFilter.getTopImpressionShareMax())) : null);
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setSearchFrequencyRankMin(advancedFilter.hasSearchFrequencyRankMin() ? advancedFilter.getSearchFrequencyRankMin() : null);
            param.setSearchFrequencyRankMax(advancedFilter.hasSearchFrequencyRankMax() ? advancedFilter.getSearchFrequencyRankMax() : null);
            param.setWeekRatioMin(advancedFilter.hasWeekRatioMin() ? new BigDecimal(String.valueOf(advancedFilter.getWeekRatioMin())) : null);
            param.setWeekRatioMax(advancedFilter.hasWeekRatioMax() ? new BigDecimal(String.valueOf(advancedFilter.getWeekRatioMax())) : null);
        }

        if (request.hasFilterTargetType()) {
            param.setFilterTargetType(request.getFilterTargetType());
        }
        if (request.hasBidMin()) {
            param.setBidMin(BigDecimal.valueOf(request.getBidMin()));
        }
        if (request.hasBidMax()) {
            param.setBidMax(BigDecimal.valueOf(request.getBidMax()));
        }
        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }
        if (CollectionUtils.isNotEmpty(request.getAdStrategyTypeListList())) {
            param.setAdStrategyTypeList(request.getAdStrategyTypeListList());
        }
        return param;
    }

    /**
     * 拼接竞价范围
     */
    private static String getSuggestBidScope(String rangeStart, String rangeEnd, String icon) {
        if (rangeStart != null && rangeEnd != null && icon != null) {
            return icon + rangeStart + "~" + icon + rangeEnd;
        } else {
            return null;
        }
    }

    /**
     * 保留原数据，无数据为空
     */
    private static String getSuggest(String obj, String icon) {
        if (obj != null) {
            return icon + obj;
        } else {
            return " ";
        }
    }

    // 校验分页查询的参数
    private String checkKeywordsPageParam(KeywordsPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(param.getStartDate()) || org.apache.commons.lang3.StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }

        //匹配方式校验
        if (StringUtils.isNotBlank(param.getMatchType())) {
            if (org.apache.commons.lang3.StringUtils.isBlank(SbMatchValueEnum.getMatchValue(param.getMatchType()))) {
                return "请求参数错误";
            }
        }
        //搜索字段校验
        if (StringUtils.isNotBlank(param.getSearchField())) {
            KeywordsPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(param.getSearchField(), KeywordsPageParam.SearchFieldEnum.class);
            if (searchFieldEnum == null) {
                return "请求参数错误";
            }
        }
        if (StringUtils.isNotBlank(param.getOrderField())) {
            if (!Constants.isADperformanceOrderField(param.getOrderField())) {
                return "请求参数错误";
            }
        }
        if (StringUtils.isNotBlank(param.getType())) {
            if (!Arrays.asList(Constants.SP, Constants.SD, Constants.SB).contains(param.getType())) {
                return "请求参数错误";
            }
        }
        if (StringUtils.isNotBlank(param.getExportSortField())) {
            //排序字段校验
            try {
                String[] split = param.getExportSortField().split(",");
                for (String s : split) {
                    if (!AdvertisingKeywordExportFieldEnum.getPoParamKeyList().contains(s)) {
                        return "请求参数错误:" + s;
                    }
                }
            } catch (Exception e) {
                return "请求参数错误:" + param.getExportSortField();
            }
        }

        return null;
    }


    private String checkTargetingPageParam(TargetingPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(param.getStartDate()) || org.apache.commons.lang3.StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }

        if (StringUtils.isNotBlank(param.getExportSortField())) {
            //排序字段校验
            try {
                String[] split = param.getExportSortField().split(",");
                for (String s : split) {
                    if (!AdvertisingTargetExportFieldEnum.getPoParamKeyList().contains(s)) {
                        return "请求参数错误" + s;
                    }
                }
            } catch (Exception e) {
                return "请求参数错误" + param.getExportSortField();
            }
        }

        return null;
    }

    /**
     * 否定关键词导出
     * @param request
     * @param responseObserver
     */
    @Override
    public void neKeywordData(NeKeywordDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("否定关键词导出 request {}", request);
        //1,构建请求param
        NeKeywordsPageParam param = buildReqParam(request);

        //2,保存导出任务
        Long id;
        if (configuration.getContainsReport()) {//包含报告数据
            //报告数据相关查询
            param.setNeTargetReportFilterDto(vo2Dto(request));
            if (StringUtils.isAnyBlank(request.getStartDate(), request.getEndDate())) {//否前否后30天
                NeTargetReportBeforeAfterDateEnum dateEnum = NeTargetReportBeforeAfterDateEnum.reportDateTypeMap.get(param.getNeTargetReportFilterDto().getReportDateType());
                id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(), AdManagePageExportTaskTypeEnum.NE_KEYWORD, dateEnum.getStartDate(), dateEnum.getEndDate(), param);
            } else {//自定义时间
                id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(), AdManagePageExportTaskTypeEnum.NE_KEYWORD, request.getStartDate(), request.getEndDate(), param);
            }
        } else {//不包含报告数据
            id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(), AdManagePageExportTaskTypeEnum.NE_KEYWORD, null, null, param);
        }

        //4,返回
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        if (id == null) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("新建任务异常，请联系管理员");
        } else {
            urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(urlBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     * 构建请求参数
     * @param request
     * @return
     */
    private NeKeywordsPageParam buildReqParam(NeKeywordDataRequest request) {
        NeKeywordsPageParam param = new NeKeywordsPageParam();
        BeanUtils.copyProperties(request, param);
        param.setUid(request.getUid().getValue());
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageSize(Constants.EXPORT_MAX_SIZE);
        param.setPageNo(1);
        return param;
    }

    private NeTargetReportFilterDto vo2Dto(NeKeywordDataRequest request){
        if (!request.hasFilter()) {
            return null;
        }
        //报告数据相关查询
        NeTargetReportFilter filter = request.getFilter();
        NeTargetReportFilterDto neTargetReportFilterDto = new NeTargetReportFilterDto();
        if (filter.hasOrderField()) {
            neTargetReportFilterDto.setOrderField(filter.getOrderField());
        }
        if (filter.hasOrderType()) {
            neTargetReportFilterDto.setOrderType(filter.getOrderType());
        }
        if (filter.hasDoAdvancedFilter()) {
            neTargetReportFilterDto.setDoAdvancedFilter(filter.getDoAdvancedFilter());
        }
        if (filter.hasReportStartDate()) {
            neTargetReportFilterDto.setReportStartDate(filter.getReportStartDate());
        }
        if (filter.hasReportEndDate()) {
            neTargetReportFilterDto.setReportEndDate(filter.getReportEndDate());
        }
        if (filter.hasReportDateType()) {
            neTargetReportFilterDto.setReportDateType(filter.getReportDateType());
        }
        if (filter.hasDataFrom()) {
            neTargetReportFilterDto.setDataFrom(filter.getDataFrom());
        }
        if (filter.hasOnlyShowImpressions()) {
            neTargetReportFilterDto.setOnlyShowImpressions(filter.getOnlyShowImpressions());
        }
        if (filter.hasAdvanceFilterParams()) {
            NeTargetReportAdvanceFilters advanceFilterParams = filter.getAdvanceFilterParams();
            NeTargetReportAdvanceFiltersDto neTargetReportAdvanceFiltersDto = new NeTargetReportAdvanceFiltersDto();
            if (advanceFilterParams.hasAcosMin()) {
                neTargetReportAdvanceFiltersDto.setAcosMin(BigDecimal.valueOf(advanceFilterParams.getAcosMin()));
            }
            if (advanceFilterParams.hasAcosMax()) {
                neTargetReportAdvanceFiltersDto.setAcosMax(BigDecimal.valueOf(advanceFilterParams.getAcosMax()));
            }
            if (advanceFilterParams.hasAdOrderNumMin()) {
                neTargetReportAdvanceFiltersDto.setAdOrderNumMin(advanceFilterParams.getAdOrderNumMin());
            }
            if (advanceFilterParams.hasAdOrderNumMax()) {
                neTargetReportAdvanceFiltersDto.setAdOrderNumMax(advanceFilterParams.getAdOrderNumMax());
            }
            if (advanceFilterParams.hasClicksMin()) {
                neTargetReportAdvanceFiltersDto.setClicksMin(advanceFilterParams.getClicksMin());
            }
            if (advanceFilterParams.hasClicksMax()) {
                neTargetReportAdvanceFiltersDto.setClicksMax(advanceFilterParams.getClicksMax());
            }
            if (advanceFilterParams.hasImpressionsMin()) {
                neTargetReportAdvanceFiltersDto.setImpressionsMin(advanceFilterParams.getImpressionsMin());
            }
            if (advanceFilterParams.hasImpressionsMax()) {
                neTargetReportAdvanceFiltersDto.setImpressionsMax(advanceFilterParams.getImpressionsMax());
            }
            if (advanceFilterParams.hasAdCostMin()) {
                neTargetReportAdvanceFiltersDto.setAdCostMin(BigDecimal.valueOf(advanceFilterParams.getAdCostMin()));
            }
            if (advanceFilterParams.hasAdCostMax()) {
                neTargetReportAdvanceFiltersDto.setAdCostMax(BigDecimal.valueOf(advanceFilterParams.getAdCostMax()));
            }
            if (advanceFilterParams.hasAdSaleMin()) {
                neTargetReportAdvanceFiltersDto.setAdSaleMin(BigDecimal.valueOf(advanceFilterParams.getAdSaleMin()));
            }
            if (advanceFilterParams.hasAdSaleMax()) {
                neTargetReportAdvanceFiltersDto.setAdSaleMax(BigDecimal.valueOf(advanceFilterParams.getAdSaleMax()));
            }
            if (advanceFilterParams.hasCtrMin()) {
                neTargetReportAdvanceFiltersDto.setCtrMin(BigDecimal.valueOf(advanceFilterParams.getCtrMin()));
            }
            if (advanceFilterParams.hasCtrMax()) {
                neTargetReportAdvanceFiltersDto.setCtrMax(BigDecimal.valueOf(advanceFilterParams.getCtrMax()));
            }
            if (advanceFilterParams.hasCvrMin()) {
                neTargetReportAdvanceFiltersDto.setCvrMin(BigDecimal.valueOf(advanceFilterParams.getCvrMin()));
            }
            if (advanceFilterParams.hasCvrMax()) {
                neTargetReportAdvanceFiltersDto.setCvrMax(BigDecimal.valueOf(advanceFilterParams.getCvrMax()));
            }
            if (advanceFilterParams.hasRoasMin()) {
                neTargetReportAdvanceFiltersDto.setRoasMin(BigDecimal.valueOf(advanceFilterParams.getRoasMin()));
            }
            if (advanceFilterParams.hasRoasMax()) {
                neTargetReportAdvanceFiltersDto.setRoasMax(BigDecimal.valueOf(advanceFilterParams.getRoasMax()));
            }
            if (advanceFilterParams.hasCpcMin()) {
                neTargetReportAdvanceFiltersDto.setCpcMin(BigDecimal.valueOf(advanceFilterParams.getCpcMin()));
            }
            if (advanceFilterParams.hasCpcMax()) {
                neTargetReportAdvanceFiltersDto.setCpcMax(BigDecimal.valueOf(advanceFilterParams.getCpcMax()));
            }
            if (advanceFilterParams.hasCpaMin()) {
                neTargetReportAdvanceFiltersDto.setCpaMin(BigDecimal.valueOf(advanceFilterParams.getCpaMin()));
            }
            if (advanceFilterParams.hasCpaMax()) {
                neTargetReportAdvanceFiltersDto.setCpaMax(BigDecimal.valueOf(advanceFilterParams.getCpaMax()));
            }
            neTargetReportFilterDto.setAdvanceFilterParams(neTargetReportAdvanceFiltersDto);
        }
        return neTargetReportFilterDto;
    }


    /**
     * 否定投放导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void neTargetingData(NeTargetingDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("否定投放导出 request {}", request);

        //1,校验请求参数
        checkNeTargetingRequest(request);

        //2,构建请求param
        NeTargetingPageParam param = buildReqParam(request);

        //3,保存导出任务
        Long id;
        if (configuration.getContainsReport()) {//包含报告数据
            //报告数据相关查询
            param.setNeTargetReportFilterDto(vo2Dto(request));
            if (StringUtils.isAnyBlank(request.getStartDate(), request.getEndDate())) {//否前否后30天
                NeTargetReportBeforeAfterDateEnum dateEnum = NeTargetReportBeforeAfterDateEnum.reportDateTypeMap.get(param.getNeTargetReportFilterDto().getReportDateType());
                id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(), AdManagePageExportTaskTypeEnum.NE_TARGET, dateEnum.getStartDate(), dateEnum.getEndDate(), param);
            } else {//自定义时间
                id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(), AdManagePageExportTaskTypeEnum.NE_TARGET, request.getStartDate(), request.getStartDate(), param);
            }
        } else {//不包含报告数据
            id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(), AdManagePageExportTaskTypeEnum.NE_TARGET, null, null, param);
        }

        //4,返回
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        if (id == null) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("新建任务异常，请联系管理员");
        } else {
            urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(urlBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     * 构建请求param
     * @param request request
     * @return
     */
    private NeTargetingPageParam buildReqParam(NeTargetingDataRequest request) {
        NeTargetingPageParam param = new NeTargetingPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageSize(Constants.EXPORT_MAX_SIZE);
        param.setPageNo(1);
        param.setUid(request.getUid().getValue());
        param.setState(request.getState());
        return param;
    }

    private NeTargetReportFilterDto vo2Dto(NeTargetingDataRequest request){
        if (!request.hasFilter()) {
            return null;
        }
        //报告数据相关查询
        NeTargetReportFilter filter = request.getFilter();
        NeTargetReportFilterDto neTargetReportFilterDto = new NeTargetReportFilterDto();
        if (filter.hasOrderField()) {
            neTargetReportFilterDto.setOrderField(filter.getOrderField());
        }
        if (filter.hasOrderType()) {
            neTargetReportFilterDto.setOrderType(filter.getOrderType());
        }
        if (filter.hasDoAdvancedFilter()) {
            neTargetReportFilterDto.setDoAdvancedFilter(filter.getDoAdvancedFilter());
        }
        if (filter.hasReportStartDate()) {
            neTargetReportFilterDto.setReportStartDate(filter.getReportStartDate());
        }
        if (filter.hasReportDateType()) {
            neTargetReportFilterDto.setReportDateType(filter.getReportDateType());
        }
        if (filter.hasReportEndDate()) {
            neTargetReportFilterDto.setReportEndDate(filter.getReportEndDate());
        }
        if (filter.hasDataFrom()) {
            neTargetReportFilterDto.setDataFrom(filter.getDataFrom());
        }
        if (filter.hasOnlyShowImpressions()) {
            neTargetReportFilterDto.setOnlyShowImpressions(filter.getOnlyShowImpressions());
        }
        if (filter.hasAdvanceFilterParams()) {
            NeTargetReportAdvanceFilters advanceFilterParams = filter.getAdvanceFilterParams();
            NeTargetReportAdvanceFiltersDto neTargetReportAdvanceFiltersDto = new NeTargetReportAdvanceFiltersDto();
            if (advanceFilterParams.hasAcosMin()) {
                neTargetReportAdvanceFiltersDto.setAcosMin(BigDecimal.valueOf(advanceFilterParams.getAcosMin()));
            }
            if (advanceFilterParams.hasAcosMax()) {
                neTargetReportAdvanceFiltersDto.setAcosMax(BigDecimal.valueOf(advanceFilterParams.getAcosMax()));
            }
            if (advanceFilterParams.hasAdOrderNumMin()) {
                neTargetReportAdvanceFiltersDto.setAdOrderNumMin(advanceFilterParams.getAdOrderNumMin());
            }
            if (advanceFilterParams.hasAdOrderNumMax()) {
                neTargetReportAdvanceFiltersDto.setAdOrderNumMax(advanceFilterParams.getAdOrderNumMax());
            }
            if (advanceFilterParams.hasClicksMin()) {
                neTargetReportAdvanceFiltersDto.setClicksMin(advanceFilterParams.getClicksMin());
            }
            if (advanceFilterParams.hasClicksMax()) {
                neTargetReportAdvanceFiltersDto.setClicksMax(advanceFilterParams.getClicksMax());
            }
            if (advanceFilterParams.hasImpressionsMin()) {
                neTargetReportAdvanceFiltersDto.setImpressionsMin(advanceFilterParams.getImpressionsMin());
            }
            if (advanceFilterParams.hasImpressionsMax()) {
                neTargetReportAdvanceFiltersDto.setImpressionsMax(advanceFilterParams.getImpressionsMax());
            }
            if (advanceFilterParams.hasAdCostMin()) {
                neTargetReportAdvanceFiltersDto.setAdCostMin(BigDecimal.valueOf(advanceFilterParams.getAdCostMin()));
            }
            if (advanceFilterParams.hasAdCostMax()) {
                neTargetReportAdvanceFiltersDto.setAdCostMax(BigDecimal.valueOf(advanceFilterParams.getAdCostMax()));
            }
            if (advanceFilterParams.hasAdSaleMin()) {
                neTargetReportAdvanceFiltersDto.setAdSaleMin(BigDecimal.valueOf(advanceFilterParams.getAdSaleMin()));
            }
            if (advanceFilterParams.hasAdSaleMax()) {
                neTargetReportAdvanceFiltersDto.setAdSaleMax(BigDecimal.valueOf(advanceFilterParams.getAdSaleMax()));
            }
            if (advanceFilterParams.hasCtrMin()) {
                neTargetReportAdvanceFiltersDto.setCtrMin(BigDecimal.valueOf(advanceFilterParams.getCtrMin()));
            }
            if (advanceFilterParams.hasCtrMax()) {
                neTargetReportAdvanceFiltersDto.setCtrMax(BigDecimal.valueOf(advanceFilterParams.getCtrMax()));
            }
            if (advanceFilterParams.hasCvrMin()) {
                neTargetReportAdvanceFiltersDto.setCvrMin(BigDecimal.valueOf(advanceFilterParams.getCvrMin()));
            }
            if (advanceFilterParams.hasCvrMax()) {
                neTargetReportAdvanceFiltersDto.setCvrMax(BigDecimal.valueOf(advanceFilterParams.getCvrMax()));
            }
            if (advanceFilterParams.hasRoasMin()) {
                neTargetReportAdvanceFiltersDto.setRoasMin(BigDecimal.valueOf(advanceFilterParams.getRoasMin()));
            }
            if (advanceFilterParams.hasRoasMax()) {
                neTargetReportAdvanceFiltersDto.setRoasMax(BigDecimal.valueOf(advanceFilterParams.getRoasMax()));
            }
            if (advanceFilterParams.hasCpcMin()) {
                neTargetReportAdvanceFiltersDto.setCpcMin(BigDecimal.valueOf(advanceFilterParams.getCpcMin()));
            }
            if (advanceFilterParams.hasCpcMax()) {
                neTargetReportAdvanceFiltersDto.setCpcMax(BigDecimal.valueOf(advanceFilterParams.getCpcMax()));
            }
            if (advanceFilterParams.hasCpaMin()) {
                neTargetReportAdvanceFiltersDto.setCpaMin(BigDecimal.valueOf(advanceFilterParams.getCpaMin()));
            }
            if (advanceFilterParams.hasCpaMax()) {
                neTargetReportAdvanceFiltersDto.setCpaMax(BigDecimal.valueOf(advanceFilterParams.getCpaMax()));
            }
            neTargetReportFilterDto.setAdvanceFilterParams(neTargetReportAdvanceFiltersDto);
        }
        return neTargetReportFilterDto;
    }

    /**
     * 校验请求参数
     * @param request
     */
    private void checkNeTargetingRequest(NeTargetingDataRequest request) {
        if (!request.hasPuid()) {
            throw new SponsoredBizException("缺少puid");
        }
        if (!request.hasShopId()) {
            throw new SponsoredBizException("缺少shopId");
        }
        if (StringUtils.isNotBlank(request.getType())) {
            if (!Arrays.asList(Constants.SP, Constants.SD, Constants.SB).contains(request.getType())) {
                throw new SponsoredBizException("请求参数错误: type");
            }
        }
    }

    /**
     * 活动否定关键词导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void campaignNeKeywordData(CampaignNeKeywordDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("活动否定关键词导出 request {}", request);

        //1,构建请求dto
        CampaignNeKeywordsPageParam param = buildReqParam(request);

        //2,保存导出任务
        Long id;
        if (configuration.getContainsReport()) {//包含报告数据
            //设置分析数据筛选参数
            param.setNeTargetReportFilterDto(vo2Dto(request));
            if (StringUtils.isAnyBlank(request.getStartDate(), request.getEndDate())) {//否前否后30天
                NeTargetReportBeforeAfterDateEnum dateEnum = NeTargetReportBeforeAfterDateEnum.reportDateTypeMap.get(param.getNeTargetReportFilterDto().getReportDateType());
                id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(), AdManagePageExportTaskTypeEnum.CAMPAIGN_NE_KEYWORD, dateEnum.getStartDate(), dateEnum.getEndDate(), param);
            } else {//自定义时间
                id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(), AdManagePageExportTaskTypeEnum.CAMPAIGN_NE_KEYWORD, request.getStartDate(), request.getEndDate(), param);
            }
        } else {//不包含报告数据
            id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(), AdManagePageExportTaskTypeEnum.CAMPAIGN_NE_KEYWORD, null, null, param);
        }

        //3,返回
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        if (id == null) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("新建任务异常，请联系管理员");
        } else {
            urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(urlBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     * 构建请求参数
     * @param request request
     * @return CampaignNeKeywordsPageParam
     */
    private CampaignNeKeywordsPageParam buildReqParam(CampaignNeKeywordDataRequest request) {
        CampaignNeKeywordsPageParam param = new CampaignNeKeywordsPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageSize(Constants.EXPORT_MAX_SIZE);
        param.setPageNo(1);
        param.setUid(request.getUid().getValue());
        param.setUuid(request.getUuid());
        return param;
    }

    private NeTargetReportFilterDto vo2Dto(CampaignNeKeywordDataRequest request){
        if (!request.hasFilter()) {
            return null;
        }
        //报告数据相关查询
        NeTargetReportFilter filter = request.getFilter();
        NeTargetReportFilterDto neTargetReportFilterDto = new NeTargetReportFilterDto();
        if (filter.hasOrderField()) {
            neTargetReportFilterDto.setOrderField(filter.getOrderField());
        }
        if (filter.hasOrderType()) {
            neTargetReportFilterDto.setOrderType(filter.getOrderType());
        }
        if (filter.hasDoAdvancedFilter()) {
            neTargetReportFilterDto.setDoAdvancedFilter(filter.getDoAdvancedFilter());
        }
        if (filter.hasReportStartDate()) {
            neTargetReportFilterDto.setReportStartDate(filter.getReportStartDate());
        }
        if (filter.hasReportEndDate()) {
            neTargetReportFilterDto.setReportEndDate(filter.getReportEndDate());
        }
        if (filter.hasReportDateType()) {
            neTargetReportFilterDto.setReportDateType(filter.getReportDateType());
        }
        if (filter.hasDataFrom()) {
            neTargetReportFilterDto.setDataFrom(filter.getDataFrom());
        }
        if (filter.hasOnlyShowImpressions()) {
            neTargetReportFilterDto.setOnlyShowImpressions(filter.getOnlyShowImpressions());
        }
        if (filter.hasAdvanceFilterParams()) {
            NeTargetReportAdvanceFilters advanceFilterParams = filter.getAdvanceFilterParams();
            NeTargetReportAdvanceFiltersDto neTargetReportAdvanceFiltersDto = new NeTargetReportAdvanceFiltersDto();
            if (advanceFilterParams.hasAcosMin()) {
                neTargetReportAdvanceFiltersDto.setAcosMin(BigDecimal.valueOf(advanceFilterParams.getAcosMin()));
            }
            if (advanceFilterParams.hasAcosMax()) {
                neTargetReportAdvanceFiltersDto.setAcosMax(BigDecimal.valueOf(advanceFilterParams.getAcosMax()));
            }
            if (advanceFilterParams.hasAdOrderNumMin()) {
                neTargetReportAdvanceFiltersDto.setAdOrderNumMin(advanceFilterParams.getAdOrderNumMin());
            }
            if (advanceFilterParams.hasAdOrderNumMax()) {
                neTargetReportAdvanceFiltersDto.setAdOrderNumMax(advanceFilterParams.getAdOrderNumMax());
            }
            if (advanceFilterParams.hasClicksMin()) {
                neTargetReportAdvanceFiltersDto.setClicksMin(advanceFilterParams.getClicksMin());
            }
            if (advanceFilterParams.hasClicksMax()) {
                neTargetReportAdvanceFiltersDto.setClicksMax(advanceFilterParams.getClicksMax());
            }
            if (advanceFilterParams.hasImpressionsMin()) {
                neTargetReportAdvanceFiltersDto.setImpressionsMin(advanceFilterParams.getImpressionsMin());
            }
            if (advanceFilterParams.hasImpressionsMax()) {
                neTargetReportAdvanceFiltersDto.setImpressionsMax(advanceFilterParams.getImpressionsMax());
            }
            if (advanceFilterParams.hasAdCostMin()) {
                neTargetReportAdvanceFiltersDto.setAdCostMin(BigDecimal.valueOf(advanceFilterParams.getAdCostMin()));
            }
            if (advanceFilterParams.hasAdCostMax()) {
                neTargetReportAdvanceFiltersDto.setAdCostMax(BigDecimal.valueOf(advanceFilterParams.getAdCostMax()));
            }
            if (advanceFilterParams.hasAdSaleMin()) {
                neTargetReportAdvanceFiltersDto.setAdSaleMin(BigDecimal.valueOf(advanceFilterParams.getAdSaleMin()));
            }
            if (advanceFilterParams.hasAdSaleMax()) {
                neTargetReportAdvanceFiltersDto.setAdSaleMax(BigDecimal.valueOf(advanceFilterParams.getAdSaleMax()));
            }
            if (advanceFilterParams.hasCtrMin()) {
                neTargetReportAdvanceFiltersDto.setCtrMin(BigDecimal.valueOf(advanceFilterParams.getCtrMin()));
            }
            if (advanceFilterParams.hasCtrMax()) {
                neTargetReportAdvanceFiltersDto.setCtrMax(BigDecimal.valueOf(advanceFilterParams.getCtrMax()));
            }
            if (advanceFilterParams.hasCvrMin()) {
                neTargetReportAdvanceFiltersDto.setCvrMin(BigDecimal.valueOf(advanceFilterParams.getCvrMin()));
            }
            if (advanceFilterParams.hasCvrMax()) {
                neTargetReportAdvanceFiltersDto.setCvrMax(BigDecimal.valueOf(advanceFilterParams.getCvrMax()));
            }
            if (advanceFilterParams.hasRoasMin()) {
                neTargetReportAdvanceFiltersDto.setRoasMin(BigDecimal.valueOf(advanceFilterParams.getRoasMin()));
            }
            if (advanceFilterParams.hasRoasMax()) {
                neTargetReportAdvanceFiltersDto.setRoasMax(BigDecimal.valueOf(advanceFilterParams.getRoasMax()));
            }
            if (advanceFilterParams.hasCpcMin()) {
                neTargetReportAdvanceFiltersDto.setCpcMin(BigDecimal.valueOf(advanceFilterParams.getCpcMin()));
            }
            if (advanceFilterParams.hasCpcMax()) {
                neTargetReportAdvanceFiltersDto.setCpcMax(BigDecimal.valueOf(advanceFilterParams.getCpcMax()));
            }
            if (advanceFilterParams.hasCpaMin()) {
                neTargetReportAdvanceFiltersDto.setCpaMin(BigDecimal.valueOf(advanceFilterParams.getCpaMin()));
            }
            if (advanceFilterParams.hasCpaMax()) {
                neTargetReportAdvanceFiltersDto.setCpaMax(BigDecimal.valueOf(advanceFilterParams.getCpaMax()));
            }
            neTargetReportFilterDto.setAdvanceFilterParams(neTargetReportAdvanceFiltersDto);
        }
        return neTargetReportFilterDto;
    }

    /**
     * 活动否定投放导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void campaignNeTargetData(CampaignNeTargetDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("活动否定投放导出 request {}", request);
        //1,构建请求dto
        CampaignNeTargetingSpParam param = buildReqParam(request);

        //2,保存导出任务
        Long id;
        if (configuration.getContainsReport()) {//包含报告数据
            //报告数据相关查询
            param.setNeTargetReportFilterDto(vo2Dto(request));
            if (StringUtils.isAnyBlank(request.getStartDate(), request.getEndDate())) {//否前否后30天
                NeTargetReportBeforeAfterDateEnum dateEnum = NeTargetReportBeforeAfterDateEnum.reportDateTypeMap.get(param.getNeTargetReportFilterDto().getReportDateType());
                id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(), AdManagePageExportTaskTypeEnum.CAMPAIGN_NE_TARGET, dateEnum.getStartDate(), dateEnum.getEndDate(), param);
            } else {//自定义时间
                id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(), AdManagePageExportTaskTypeEnum.CAMPAIGN_NE_TARGET, request.getFilter().getReportStartDate(), request.getFilter().getReportEndDate(), param);
            }
        } else {//不包含报告数据
            id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), param.getShopId(), AdManagePageExportTaskTypeEnum.CAMPAIGN_NE_TARGET, null, null, param);
        }

        //3,返回
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        if (id == null) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("新建任务异常，请联系管理员");
        } else {
            urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(urlBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     * 构建请求参数
     * @param request request
     * @return CampaignNeTargetingSpParam
     */
    private CampaignNeTargetingSpParam buildReqParam(CampaignNeTargetDataRequest request) {
        CampaignNeTargetingSpParam param = new CampaignNeTargetingSpParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageSize(Constants.EXPORT_MAX_SIZE);
        param.setPageNo(1);
        param.setUid(request.getUid().getValue());
        param.setUuid(request.getUuid());
        return param;
    }

    private NeTargetReportFilterDto vo2Dto(CampaignNeTargetDataRequest request){
        if (!request.hasFilter()) {
            return null;
        }
        //报告数据相关查询
        NeTargetReportFilter filter = request.getFilter();
        NeTargetReportFilterDto neTargetReportFilterDto = new NeTargetReportFilterDto();
        if (filter.hasOrderField()) {
            neTargetReportFilterDto.setOrderField(filter.getOrderField());
        }
        if (filter.hasOrderType()) {
            neTargetReportFilterDto.setOrderType(filter.getOrderType());
        }
        if (filter.hasDoAdvancedFilter()) {
            neTargetReportFilterDto.setDoAdvancedFilter(filter.getDoAdvancedFilter());
        }
        if (filter.hasReportStartDate()) {
            neTargetReportFilterDto.setReportStartDate(filter.getReportStartDate());
        }
        if (filter.hasReportEndDate()) {
            neTargetReportFilterDto.setReportEndDate(filter.getReportEndDate());
        }
        if (filter.hasReportDateType()) {
            neTargetReportFilterDto.setReportDateType(filter.getReportDateType());
        }
        if (filter.hasDataFrom()) {
            neTargetReportFilterDto.setDataFrom(filter.getDataFrom());
        }
        if (filter.hasOnlyShowImpressions()) {
            neTargetReportFilterDto.setOnlyShowImpressions(filter.getOnlyShowImpressions());
        }
        if (filter.hasAdvanceFilterParams()) {
            NeTargetReportAdvanceFilters advanceFilterParams = filter.getAdvanceFilterParams();
            NeTargetReportAdvanceFiltersDto neTargetReportAdvanceFiltersDto = new NeTargetReportAdvanceFiltersDto();
            if (advanceFilterParams.hasAcosMin()) {
                neTargetReportAdvanceFiltersDto.setAcosMin(BigDecimal.valueOf(advanceFilterParams.getAcosMin()));
            }
            if (advanceFilterParams.hasAcosMax()) {
                neTargetReportAdvanceFiltersDto.setAcosMax(BigDecimal.valueOf(advanceFilterParams.getAcosMax()));
            }
            if (advanceFilterParams.hasAdOrderNumMin()) {
                neTargetReportAdvanceFiltersDto.setAdOrderNumMin(advanceFilterParams.getAdOrderNumMin());
            }
            if (advanceFilterParams.hasAdOrderNumMax()) {
                neTargetReportAdvanceFiltersDto.setAdOrderNumMax(advanceFilterParams.getAdOrderNumMax());
            }
            if (advanceFilterParams.hasClicksMin()) {
                neTargetReportAdvanceFiltersDto.setClicksMin(advanceFilterParams.getClicksMin());
            }
            if (advanceFilterParams.hasClicksMax()) {
                neTargetReportAdvanceFiltersDto.setClicksMax(advanceFilterParams.getClicksMax());
            }
            if (advanceFilterParams.hasImpressionsMin()) {
                neTargetReportAdvanceFiltersDto.setImpressionsMin(advanceFilterParams.getImpressionsMin());
            }
            if (advanceFilterParams.hasImpressionsMax()) {
                neTargetReportAdvanceFiltersDto.setImpressionsMax(advanceFilterParams.getImpressionsMax());
            }
            if (advanceFilterParams.hasAdCostMin()) {
                neTargetReportAdvanceFiltersDto.setAdCostMin(BigDecimal.valueOf(advanceFilterParams.getAdCostMin()));
            }
            if (advanceFilterParams.hasAdCostMax()) {
                neTargetReportAdvanceFiltersDto.setAdCostMax(BigDecimal.valueOf(advanceFilterParams.getAdCostMax()));
            }
            if (advanceFilterParams.hasAdSaleMin()) {
                neTargetReportAdvanceFiltersDto.setAdSaleMin(BigDecimal.valueOf(advanceFilterParams.getAdSaleMin()));
            }
            if (advanceFilterParams.hasAdSaleMax()) {
                neTargetReportAdvanceFiltersDto.setAdSaleMax(BigDecimal.valueOf(advanceFilterParams.getAdSaleMax()));
            }
            if (advanceFilterParams.hasCtrMin()) {
                neTargetReportAdvanceFiltersDto.setCtrMin(BigDecimal.valueOf(advanceFilterParams.getCtrMin()));
            }
            if (advanceFilterParams.hasCtrMax()) {
                neTargetReportAdvanceFiltersDto.setCtrMax(BigDecimal.valueOf(advanceFilterParams.getCtrMax()));
            }
            if (advanceFilterParams.hasCvrMin()) {
                neTargetReportAdvanceFiltersDto.setCvrMin(BigDecimal.valueOf(advanceFilterParams.getCvrMin()));
            }
            if (advanceFilterParams.hasCvrMax()) {
                neTargetReportAdvanceFiltersDto.setCvrMax(BigDecimal.valueOf(advanceFilterParams.getCvrMax()));
            }
            if (advanceFilterParams.hasRoasMin()) {
                neTargetReportAdvanceFiltersDto.setRoasMin(BigDecimal.valueOf(advanceFilterParams.getRoasMin()));
            }
            if (advanceFilterParams.hasRoasMax()) {
                neTargetReportAdvanceFiltersDto.setRoasMax(BigDecimal.valueOf(advanceFilterParams.getRoasMax()));
            }
            if (advanceFilterParams.hasCpcMin()) {
                neTargetReportAdvanceFiltersDto.setCpcMin(BigDecimal.valueOf(advanceFilterParams.getCpcMin()));
            }
            if (advanceFilterParams.hasCpcMax()) {
                neTargetReportAdvanceFiltersDto.setCpcMax(BigDecimal.valueOf(advanceFilterParams.getCpcMax()));
            }
            if (advanceFilterParams.hasCpaMin()) {
                neTargetReportAdvanceFiltersDto.setCpaMin(BigDecimal.valueOf(advanceFilterParams.getCpaMin()));
            }
            if (advanceFilterParams.hasCpaMax()) {
                neTargetReportAdvanceFiltersDto.setCpaMax(BigDecimal.valueOf(advanceFilterParams.getCpaMax()));
            }
            neTargetReportFilterDto.setAdvanceFilterParams(neTargetReportAdvanceFiltersDto);
        }
        return neTargetReportFilterDto;
    }

    @Override
    public void operationLogPageData(OperationLogRequst request, StreamObserver<UrlResponse> responseObserver) {
        log.info("查询广告日志列表 request {}", request);
        String downloadUrl = null;
        int count = 0;
        List<String> urls = Lists.newLinkedList();
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        OperationLogQo dto = new OperationLogQo();
        BeanUtils.copyProperties(request, dto);

        List<AdManageLogVo> list = manageOperationLogDao.getAllAdLogs(dto);

        if (CollectionUtils.isEmpty(list)) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("excel.export.none");
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        if (CollectionUtils.isNotEmpty(list)) {
            try {
                //获取数据详情
                WriteHandlerBuild build = new WriteHandlerBuild().rate().currencyNew(OperationLogVo.class);
                //不需要导出的字段。
                List<String> excludeFileds = Lists.newArrayList("id", "shopId", "marketplaceId", "campaignId", "adGroupId", "adType", "uid", "ip", "module", "subModule",
                        "target", "targetId", "from", "changeType", "campaignTargetingType", "adGroupType", "operationDate", "operationDay", "siteOperationDay", "operationContent",
                        "previousValue", "newValue", "campaignName", "groupName");
                List<List<AdManageLogVo>> partition = Lists.partition(list, Constants.FILE_MAX_SIZE);//集合分片
                for (List<AdManageLogVo> partionList : partition) {
                    if (partionList.size() > 0) {
                        downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), partionList, request.getFileName() + "(" + count++ + ")", AdManageLogVo.class, build, excludeFileds);
                        urls.add(downloadUrl);
                    }
                }
                urlBuilder.addAllUrls(urls);
                urlBuilder.setCode(Int32Value.of(Result.SUCCESS));

            } catch (Exception e) {
                log.error("operationLog error :{},puid{},shopId{}", e, request.getPuid(), request.getShopId());
                urlBuilder.setCode(Int32Value.of(Result.ERROR));
                urlBuilder.setMsg("process.msg.sync.fail");
            } finally {
                responseObserver.onNext(urlBuilder.build());
                responseObserver.onCompleted();
            }
        } else {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("excel.export.none");
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
        }
    }

    /**
     * 数据聚合（asin,搜索词）导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void aggregationTargetData(TargetReportDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("广告聚合数据导出 request {}", request);
        UrlResponse.Builder urlBulider = UrlResponse.newBuilder();

        List<TargetReportDataRequest.ChildsRpcVo> childsRpcVoList = request.getChildsRpcVoList();
        if (!request.hasPuid() || org.apache.commons.lang.StringUtils.isBlank(request.getStartDate())
                || org.apache.commons.lang.StringUtils.isBlank(request.getEndDate())) {
            urlBulider.setMsg("请求参数错误");
            urlBulider.setCode(Int32Value.of(Result.ERROR));
            responseObserver.onNext(urlBulider.build());
            responseObserver.onCompleted();
            return;
        }
        if (CollectionUtils.isEmpty(childsRpcVoList)) {
            urlBulider.setMsg("请求参数错误");
            urlBulider.setCode(Int32Value.of(Result.ERROR));
            responseObserver.onNext(urlBulider.build());
            responseObserver.onCompleted();
            return;
        }

        List<Integer> shopIdList = childsRpcVoList.stream()
                .map(TargetReportDataRequest.ChildsRpcVo::getShopId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<Integer, String> shopNameMap = shopAuthDao.listAllByIds(request.getPuid(), shopIdList).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ShopAuth::getId, ShopAuth::getName, (e1, e2) -> e1));

        List<TargetReportSearchVo> childVoList = childsRpcVoList.stream().filter(Objects::nonNull).map(item -> {
            TargetReportSearchVo searchVo = new TargetReportSearchVo();
            searchVo.setPuid(request.getPuid());
            searchVo.setAsin(item.getAsin());
            searchVo.setShopId(item.getShopId());
            if (item.hasMarketplaceId()) {
                searchVo.setMarketplaceId(item.getMarketplaceId());
            }
            searchVo.setType(request.getType());
            searchVo.setStartDate(request.getStartDate());
            searchVo.setEndDate(request.getEndDate());
            searchVo.setOrderField(request.getOrderField());
            searchVo.setOrderValue(request.getOrderValue());
            searchVo.setStartDate(searchVo.getStartDate().replace("-", ""));
            searchVo.setEndDate(searchVo.getEndDate().replace("-", ""));
            Date start = DateUtil.strToDate(request.getStartDate(), "yyyy-MM-dd");
            Date end = DateUtil.strToDate(request.getEndDate(), "yyyy-MM-dd");
            searchVo.setStart(start);
            searchVo.setEnd(end);
            if (request.hasAsinType()) {
                searchVo.setAsinType(request.getAsinType());
            }
            if (request.hasShowType()) {
                searchVo.setShowType(request.getShowType());
            }
            return searchVo;
        }).collect(Collectors.toList());

        List<String> excludeFileds = new ArrayList<>();
        if (org.apache.commons.lang.StringUtils.isBlank(childVoList.get(0).getShowType()) || "asin".equals(childVoList.get(0).getShowType())) {
            excludeFileds = Lists.newArrayList("parentAsin");
        }
        if ("parentAsin".equals(childVoList.get(0).getShowType())) {
            excludeFileds = Lists.newArrayList("asin");
        }
        adReportExportService.aggregationTargetDataExport(excludeFileds, request, childVoList, shopNameMap);
        urlBulider.setCode(Int32Value.of(Result.SUCCESS));
        responseObserver.onNext(urlBulider.build());
        responseObserver.onCompleted();
    }

    /**
     * 关键词库导出excel
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void keywordLibListData(KeywordLibDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("广告关键词库列表导出 request {}", request);
        UrlResponse.Builder urlBulider = UrlResponse.newBuilder();
        List<KeywordLibsListExcelVo> voList = Lists.newArrayList();
        String fileName = "关键词库列表数据导出";

        // 返回下载的url
        List<String> urls = Lists.newLinkedList();

        if (!request.hasPuid() || !request.hasUid() || !request.hasTo() || !request.hasStartDate()) {
            urlBulider.setMsg("请求参数错误");
            urlBulider.setCode(Int32Value.of(Result.ERROR));
            responseObserver.onNext(urlBulider.build());
            responseObserver.onCompleted();
            return;
        }

        KeywordLibsPageParam param = new KeywordLibsPageParam();
        if (request.hasPuid()) {
            param.setPuid(request.getPuid());
        }
        if (request.hasUid()) {
            param.setUid(request.getUid());
        }
        if (CollectionUtils.isNotEmpty(request.getAdTagIdsList())) {
            param.setAdTagIds(request.getAdTagIdsList());
        }
        if (request.hasKeywordText()) {
            param.setKeywordText(request.getKeywordText());
        }
        if (request.hasSource()) {
            param.setSource(request.getSource());
        }
        //广告数据时间
        if (request.hasStartDate()) {
            param.setStartDate(request.getStartDate());
        }
        if (request.hasEndDate()) {
            param.setEndDate(request.getEndDate());
        }
        //关键词添加时间筛选
        if (request.hasAddStartDate()) {
            param.setAddStartDate(request.getAddStartDate());
        }
        if (request.hasAddEndDate()) {
            param.setAddEndDate(request.getAddEndDate());
        }
        //来源站点筛选
        if (!request.getCountryList().isEmpty()) {
            param.setCountry(request.getCountryList());
        }
        //来源店铺筛选
        if (!request.getShopListList().isEmpty()) {
            param.setShopIdList(request.getShopListList());
        }
        if (request.hasState()) {
            param.setState(request.getState());
        }
        if (request.hasOrderField()) {
            param.setOrderField(request.getOrderField());
        }
        if (request.hasOrderType()) {
            param.setOrderType(request.getOrderType());
        }
        if (request.hasSearchType()) {
            param.setSearchType(request.getSearchType());
        }
        if (request.hasAsin() && request.hasMarketplaceId()) {
            param.setAsin(request.getAsin());
            param.setMarketplaceId(request.getMarketplaceId());
        }
        if (CollectionUtils.isNotEmpty(request.getUidListList())) {
            param.setUidList(request.getUidListList());
        }

        if (request.getSearchUid() > 0) {
            param.setSearchUid(request.getSearchUid());
        }
        //新增币种
        if (request.hasTo()) {
            param.setTo(request.getTo());
        }
        if (request.hasSite()) {
            param.setSite(request.getSite());
        }
        if (request.hasRemark()) {
            param.setRemark(request.getRemark());
        }
        if (request.hasRemarkSearchType()) {
            param.setRemarkSearchType(request.getRemarkSearchType());
        }
        Result<List<KeywordLibsVo>> result = cpcKeywordsLibService.exportList(param);

        voList = ListUtils.emptyIfNull(result.getData()).stream().filter(Objects::nonNull)
                .map(item -> {
                    KeywordLibsListExcelVo excelVo = new KeywordLibsListExcelVo();
                    BeanUtils.copyProperties(item, excelVo);
                    //对特殊值进行加符号处理
                    excelVo.setClickRate(Optional.ofNullable(item.getClickRate()).orElse(BigDecimal.ZERO).toPlainString().concat("%"));
                    excelVo.setSalesConversionRate(Optional.ofNullable(item.getSalesConversionRate()).orElse(BigDecimal.ZERO).toPlainString().concat("%"));
                    excelVo.setAcos(Optional.ofNullable(item.getAcos()).orElse(BigDecimal.ZERO).toPlainString().concat("%"));
                    excelVo.setRoas(Optional.ofNullable(item.getRoas()).orElse(BigDecimal.ZERO).toPlainString());
                    excelVo.setTotalSales(CurrencyUnitEnum.getByCurrency(param.getTo()).getUnit() + Optional.ofNullable(item.getTotalSales()).orElse(BigDecimal.ZERO).toPlainString());
                    excelVo.setCost(CurrencyUnitEnum.getByCurrency(param.getTo()).getUnit() + Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO).toPlainString());
                    excelVo.setCpc(CurrencyUnitEnum.getByCurrency(param.getTo()).getUnit() + Optional.ofNullable(item.getCpc()).orElse(BigDecimal.ZERO).toPlainString());
                    excelVo.setCpa(CurrencyUnitEnum.getByCurrency(param.getTo()).getUnit() + Optional.ofNullable(item.getCpa()).orElse(BigDecimal.ZERO).toPlainString());
                    excelVo.setDisplaySearchFrequencyRank(item.getSearchFrequencyRank() > 0 ? String.valueOf(item.getSearchFrequencyRank()) : "-");
                    //                    excelVo.setWeekRatio(Optional.ofNullable(item.getWeekRatio()).orElse(BigDecimal.ZERO).toPlainString().concat("%"));
                    if (CollectionUtils.isNotEmpty(item.getAdTags())) {
                        List<String> tagName = item.getAdTags().stream()
                                .filter(Objects::nonNull)
                                .map(AdKeywordLibTag::getName)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(tagName)) {
                            excelVo.setAdTagName(String.join(StringUtil.SPLIT_COMMA, tagName));
                        }
                    }
                    if (CollectionUtils.isNotEmpty(item.getAsinTags())) {
                        String asinTagName = item.getAsinTags().stream().map(e -> e.getAsin() + "(" + e.getMarketplaceCN() + ")").collect(Collectors.joining(StringUtil.SPLIT_COMMA));
                        excelVo.setAsinTagName(asinTagName);
                    }
                    if(CollectionUtils.isNotEmpty(item.getItemVos())) {
                        excelVo.setAddUser(item.getItemVos().stream().map(KeywordLibsVo.KeywordLibsItemVo::getName).collect(Collectors.joining(",")));
                    }
                    return excelVo;
                }).collect(Collectors.toList());
        // 导出数据为空时
        if (CollectionUtils.isEmpty(voList)) {
            urlBulider.setCode(Int32Value.of(Result.ERROR));
            urlBulider.setMsg("excel.export.none");
            responseObserver.onNext(urlBulider.build());
            responseObserver.onCompleted();
            return;
        }

        // 执行导出处理逻辑
        try {
            WriteHandlerBuild build = new WriteHandlerBuild().rate().currencyNew(KeywordLibsListExcelVo.class);
            // 列表总数不超过5000条，不需要分片
            if (voList.size() > 0) {
                if (StringUtils.isBlank(param.getAsin()) && StringUtils.isBlank(param.getMarketplaceId())) {
                    urls.add(excelService.easyExcelHandlerExport(request.getPuid(), voList, fileName, KeywordLibsListExcelVo.class, build));
                } else {
                    List<String> voExcludeFileds = new ArrayList<>();
                    voExcludeFileds.add("asinTagName");
                    urls.add(excelService.easyExcelHandlerExport(request.getPuid(), voList, fileName, KeywordLibsListExcelVo.class, build, voExcludeFileds));
                }
            }
            urlBulider.addAllUrls(urls);
            urlBulider.setCode(Int32Value.of(Result.SUCCESS));
        } catch (Exception e) {
            urlBulider.setCode(Int32Value.of(Result.ERROR));
            log.error("广告关键词库列表 导出 error:{},puid{}", e, request.getPuid());
            urlBulider.setMsg("process.msg.sync.fail");
        } finally {
            responseObserver.onNext(urlBulider.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void asinLibListData(PageListAsinsRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("ASIN库列表导出 request {}", request);
        UrlResponse.Builder urlBulider = UrlResponse.newBuilder();
        List<AsinLibsListExcelVo> voList;
        String fileName = "ASIN库列表数据导出";

        // 返回下载的url
        List<String> urls = Lists.newLinkedList();

        if (!request.hasPuid() || !request.hasUid() || !request.hasTo() || !request.hasStartDate()) {
            urlBulider.setMsg("请求参数错误");
            urlBulider.setCode(Int32Value.of(Result.ERROR));
            responseObserver.onNext(urlBulider.build());
            responseObserver.onCompleted();
            return;
        }
        Result<List<AsinLibsVo>> result = cpcAsinsLibService.exportList(request);

        voList = ListUtils.emptyIfNull(result.getData()).stream().filter(Objects::nonNull)
                .map(item -> {
                    AsinLibsListExcelVo excelVo = new AsinLibsListExcelVo();
                    BeanUtils.copyProperties(item, excelVo);
                    //对特殊值进行加符号处理
                    excelVo.setAsinText(item.getAsin());
                    excelVo.setClickRate(Optional.ofNullable(item.getClickRate()).orElse(BigDecimal.ZERO).toPlainString().concat("%"));
                    excelVo.setSalesConversionRate(Optional.ofNullable(item.getSalesConversionRate()).orElse(BigDecimal.ZERO).toPlainString().concat("%"));
                    excelVo.setAcos(Optional.ofNullable(item.getAcos()).orElse(BigDecimal.ZERO).toPlainString().concat("%"));
                    excelVo.setRoas(Optional.ofNullable(item.getRoas()).orElse(BigDecimal.ZERO).toPlainString());
                    excelVo.setTotalSales(CurrencyUnitEnum.getByCurrency(request.getTo()).getUnit() + Optional.ofNullable(item.getTotalSales()).orElse(BigDecimal.ZERO).toPlainString());
                    excelVo.setCost(CurrencyUnitEnum.getByCurrency(request.getTo()).getUnit() + Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO).toPlainString());
                    excelVo.setCpc(CurrencyUnitEnum.getByCurrency(request.getTo()).getUnit() + Optional.ofNullable(item.getCpc()).orElse(BigDecimal.ZERO).toPlainString());
                    excelVo.setCpa(CurrencyUnitEnum.getByCurrency(request.getTo()).getUnit() + Optional.ofNullable(item.getCpa()).orElse(BigDecimal.ZERO).toPlainString());
                    excelVo.setAsinMonitor(Optional.ofNullable(item.getCompetitiveMonitor()).orElse(0));
                    if (CollectionUtils.isNotEmpty(item.getAdTags())) {
                        List<String> tagName = item.getAdTags().stream()
                                .filter(Objects::nonNull)
                                .map(AdKeywordLibTag::getName)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(tagName)) {
                            excelVo.setAdTagName(String.join(StringUtil.SPLIT_COMMA, tagName));
                        }
                    }
                    if (CollectionUtils.isNotEmpty(item.getAsinTags())) {
                        String asinTagName = item.getAsinTags().stream().map(e -> e.getAsin() + "(" + e.getMarketplaceCN() + ")").collect(Collectors.joining(StringUtil.SPLIT_COMMA));
                        excelVo.setAsinTagName(asinTagName);
                    }
                    if(CollectionUtils.isNotEmpty(item.getItemVos())) {
                        excelVo.setAddUser(item.getItemVos().stream().map(AsinLibsVo.AsinLibsItemVo::getName).collect(Collectors.joining(",")));
                    }
                    return excelVo;
                }).collect(Collectors.toList());
        // 导出数据为空时
        if (CollectionUtils.isEmpty(voList)) {
            urlBulider.setCode(Int32Value.of(Result.ERROR));
            urlBulider.setMsg("excel.export.none");
            responseObserver.onNext(urlBulider.build());
            responseObserver.onCompleted();
            return;
        }

        // 执行导出处理逻辑
        try {
            WriteHandlerBuild build = new WriteHandlerBuild().rate().currencyNew(AsinLibsListExcelVo.class);
            // 列表总数不超过5000条，不需要分片
            if (voList.size() > 0) {
                if (StringUtils.isBlank(request.getAsin()) && StringUtils.isBlank(request.getMarketplaceId())) {
                    urls.add(excelService.easyExcelHandlerExport(request.getPuid(), voList, fileName, AsinLibsListExcelVo.class, build));
                } else {
                    List<String> voExcludeFileds = new ArrayList<>();
                    voExcludeFileds.add("asinTagName");
                    urls.add(excelService.easyExcelHandlerExport(request.getPuid(), voList, fileName, AsinLibsListExcelVo.class, build, voExcludeFileds));
                }
            }
            urlBulider.addAllUrls(urls);
            urlBulider.setCode(Int32Value.of(Result.SUCCESS));
        } catch (Exception e) {
            urlBulider.setCode(Int32Value.of(Result.ERROR));
            log.error("广告ASIN库列表 导出 error:{},puid{}", e, request.getPuid());
            urlBulider.setMsg("process.msg.sync.fail");
        } finally {
            responseObserver.onNext(urlBulider.build());
            responseObserver.onCompleted();
        }
    }

    /**
     * 广告重复投放列表导出
     * @param request
     * @param responseObserver
     */
    @Override
    public void targetingDetailList(RepeatTargetingDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("广告重复投放列表导出 request {}", request);
        UrlResponse.Builder urlBulider = UrlResponse.newBuilder();
        List<RepeatTargetingListExcelVo> voList = Lists.newArrayList();
        String fileName = "重复投放列表数据导出";
        if (!request.hasPuid() || !request.hasStartDate()) {
            urlBulider.setMsg("请求参数错误");
            urlBulider.setCode(Int32Value.of(Result.ERROR));
            responseObserver.onNext(urlBulider.build());
            responseObserver.onCompleted();
            return;
        }

        // 返回下载的url
        List<String> urls = Lists.newLinkedList();

        RepeatTargetingDetailPageVo detailPageVo = new RepeatTargetingDetailPageVo();
        detailPageVo.setPuid(request.getPuid().getValue());
        detailPageVo.setUid(request.getUid().getValue());
        detailPageVo.setUuid(request.getUuid());
        //将参数填充抽成一个方法
        fillVoFromRequest(request, detailPageVo);
        //插入任务
        Long id = adManagePageExportTaskService.saveExportTask(detailPageVo.getPuid(), detailPageVo.getUid(), 0,
                AdManagePageExportTaskTypeEnum.REPEAT_TARGETING, detailPageVo.getStartDate(), detailPageVo.getEndDate(), detailPageVo);
        if (id == null) {
            urlBulider.setCode(Int32Value.of(Result.ERROR));
            urlBulider.setMsg("新建任务异常，请联系管理员");
        } else {
            urlBulider.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(urlBulider.build());
        responseObserver.onCompleted();

        //导出方法执行
//        Result<List<RepeatTargetingDetailVo>> result = cpcRepeatTargetingService.exportList(detailPageVo);
//        voList = ListUtils.emptyIfNull(result.getData()).stream().filter(Objects::nonNull)
//                .map(item -> {
//                    RepeatTargetingListExcelVo excelVo = new RepeatTargetingListExcelVo();
//                    BeanUtils.copyProperties(item, excelVo);
//                    //对特殊值进行加符号处理以及状态映射文字
//                    //keywordText字段无需查询，该投放详情下所有的投放关键词均一样
//                    if (Constants.EXACT.equalsIgnoreCase(item.getMatchType())) {
//                        excelVo.setMatchType("精确匹配");
//                    } else if (Constants.PHRASE.equalsIgnoreCase(item.getMatchType())) {
//                        excelVo.setMatchType("词组匹配");
//                    } else if (Constants.BROAD.equalsIgnoreCase(item.getMatchType())) {
//                        excelVo.setMatchType("广泛匹配");
//                    }
//                    excelVo.setKeywordText(request.getKeywordText());
//                    excelVo.setState(Optional.ofNullable(AllAdStateEnum.getStateValue(item.getState())).orElse("-"));
//                    excelVo.setStatus(Optional.ofNullable(AmazonAdKeyword.servingStatusEnum.getServingStatusName(item.getStatus())).orElse("-"));
//                    excelVo.setClickRate(Optional.ofNullable(item.getClickRate().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString().concat("%"));
//                    excelVo.setSalesConversionRate(Optional.ofNullable(item.getSalesConversionRate().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString().concat("%"));
//                    excelVo.setAcos(Optional.ofNullable(item.getAcos().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString().concat("%"));
//                    excelVo.setRoas(Optional.ofNullable(item.getRoas().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
//                    excelVo.setTotalSales(UserCurrencyType.getIconByMarketplaceId(detailPageVo.getMarketplaceId()) + Optional.ofNullable(item.getTotalSales().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
//                    excelVo.setCost(UserCurrencyType.getIconByMarketplaceId(detailPageVo.getMarketplaceId()) + Optional.ofNullable(item.getCost().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
//                    excelVo.setCpc(UserCurrencyType.getIconByMarketplaceId(detailPageVo.getMarketplaceId()) + Optional.ofNullable(item.getCpc().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
//                    excelVo.setCpa(UserCurrencyType.getIconByMarketplaceId(detailPageVo.getMarketplaceId()) + Optional.ofNullable(item.getCpa().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
//                    excelVo.setDisplaySearchFrequencyRank(Optional.ofNullable(item.getSearchFrequencyRank()).orElse(0) == 0 ? "-" : Optional.of(item.getSearchFrequencyRank()).toString());
//                    if (CollectionUtils.isNotEmpty(item.getAdTags())) {
//                        List<String> tagName = item.getAdTags().stream()
//                                .filter(Objects::nonNull)
//                                .map(AdTag::getName)
//                                .filter(StringUtils::isNotBlank)
//                                .collect(Collectors.toList());
//                        if (CollectionUtils.isNotEmpty(tagName)) {
//                            excelVo.setAdTagName(String.join(StringUtil.SPLIT_COMMA, tagName));
//                        }
//                    }
//                    return excelVo;
//                }).collect(Collectors.toList());
//        //导出数据为空时
//        if (CollectionUtils.isEmpty(voList)) {
//            urlBulider.setCode(Int32Value.of(Result.ERROR));
//            urlBulider.setMsg("excel.export.none");
//            responseObserver.onNext(urlBulider.build());
//            responseObserver.onCompleted();
//            return;
//        }
//        //执行导出逻辑
//        try {
//            WriteHandlerBuild build = new WriteHandlerBuild().rate().currencyNew(RepeatTargetingListExcelVo.class);
//            if (!voList.isEmpty()) {
//                urls.add(excelService.easyExcelHandlerExport(request.getPuid().getValue(), voList, fileName, RepeatTargetingListExcelVo.class, build));
//            }
//            urlBulider.addAllUrls(urls);
//        } catch (Exception e) {
//            urlBulider.setCode(Int32Value.of(Result.ERROR));
//            log.error("广告重复投放列表 导出 error:{},puid{}", e, request.getPuid());
//            urlBulider.setMsg("process.msg.sync.fail");
//        } finally {
//            responseObserver.onNext(urlBulider.build());
//            responseObserver.onCompleted();
//        }
    }

    private static void fillVoFromRequest(RepeatTargetingDataRequest request, RepeatTargetingDetailPageVo detailPageVo) {
        //站点
        if (request.hasMarketplaceId()) {
            detailPageVo.setMarketplaceId(request.getMarketplaceId());
        }
        //店铺
        if (CollectionUtils.isNotEmpty(request.getShopListList())) {
            List<Integer> shopList = request.getShopListList().stream().map(Integer::valueOf).collect(Collectors.toList());
            detailPageVo.setShopIdList(shopList);
        }
        //开始时间
        if (request.hasStartDate()) {
            detailPageVo.setStartDate(request.getStartDate());
        }
        if (request.hasEndDate()) {
            detailPageVo.setEndDate(request.getEndDate());
        }
        //是否查询环比数据
        detailPageVo.setIsCompare(request.getIsCompare());
        //对比开始时间
        if (request.hasCompareStartDate()) {
            detailPageVo.setCompareStartDate(request.getCompareStartDate());
        }
        //对比结束时间
        if (request.hasCompareEndDate()) {
            detailPageVo.setCompareEndDate(request.getCompareEndDate());
        }
        //标签
        if (CollectionUtils.isNotEmpty(request.getAdTagsList())) {
            detailPageVo.setAdTagIds(request.getAdTagsList());
        }
        //关键词
        if (request.hasKeywordText()) {
            detailPageVo.setKeywordText(request.getKeywordText());
        }
        //匹配类型
        if (request.hasMatchType()) {
            detailPageVo.setDetailMatchType(request.getMatchType());
        }
        //左侧筛选
        //词组数量筛选
        if (CollectionUtils.isNotEmpty(request.getKeywordSizeList())) {
            List<Integer> keywordSizeList = request.getKeywordSizeList().stream().map(Integer::valueOf).collect(Collectors.toList());
            detailPageVo.setKeywordSize(keywordSizeList);
        }
        //ASIN筛选框
        if (StringUtils.isNotBlank(request.getProductValue())) {
            List<String> valueList = Arrays.stream(request.getProductValue().split("%±%")).collect(Collectors.toList());
            detailPageVo.setProductValue(valueList);
        }
        //ASIN搜索类型-模糊与精准匹配查询
        if (StringUtils.isNotBlank(request.getProductType())) {
            detailPageVo.setProductType(request.getProductType());
        }
        //列表页筛选
        //广告组合
        if (CollectionUtils.isNotEmpty(request.getPortfolioIdList())) {
            detailPageVo.setPortfolioList(request.getPortfolioIdList());
        }
        //广告活动
        if (CollectionUtils.isNotEmpty(request.getCampaignIdList())) {
            detailPageVo.setCampaignIdList(request.getCampaignIdList());
        }
        //广告组
        if (CollectionUtils.isNotEmpty(request.getAdGroupIdList())) {
            detailPageVo.setAdGroupIdList(request.getAdGroupIdList());
        }
        //广告层级筛选
        if (StringUtils.isNotBlank(request.getType())) {
            detailPageVo.setAdType(request.getType());
        }
        //投放运行状态
        if (CollectionUtils.isNotEmpty(request.getStateList())) {
            detailPageVo.setStatus(request.getStateList());
        }
        //投放服务状态
        if (CollectionUtils.isNotEmpty(request.getStatusList())) {
            detailPageVo.setServingStatus(request.getStatusList());
        }
        //高级筛选
        if (request.hasUseAdvanced()) {
            detailPageVo.setUseAdvanced(request.getUseAdvanced());
        }
        if (request.hasUseAdvanced() && request.getUseAdvanced()) {
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            //曝光量最大值和最小值
            detailPageVo.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            detailPageVo.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            //点击量最大值和最小值
            detailPageVo.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            detailPageVo.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            //广告花费最大值和最小值
            detailPageVo.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            detailPageVo.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            //cpc最大值和最小值
            detailPageVo.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            detailPageVo.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            //cpa最大值和最小值
            detailPageVo.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            detailPageVo.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            //广告订单量最大值和最小值
            detailPageVo.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            detailPageVo.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            //广告销售额最大值和最小值
            detailPageVo.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            detailPageVo.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            //roas最大值和最小值
            detailPageVo.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            detailPageVo.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            //点击率最大值和最小值
            detailPageVo.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            detailPageVo.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            //acos最大值和最小值
            detailPageVo.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            detailPageVo.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            //广告订单转化率最大值和最小值
            detailPageVo.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            detailPageVo.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
        }
        //是否过滤已否定
        detailPageVo.setIsFilterNeTargeting(request.getIsFilterNeTargeting());
        //排序字段
        if (StringUtils.isNotBlank(request.getOrderField())) {
            detailPageVo.setOrderField(request.getOrderField());
        }
        //排序值
        if (StringUtils.isNotBlank(request.getOrderType())) {
            detailPageVo.setOrderType(request.getOrderType());
        }
        detailPageVo.setPageNo(request.getPageNo().getValue());
        detailPageVo.setPageSize(request.getPageSize().getValue());
        detailPageVo.setPageSign(request.getPageSign());
    }

    /**
     * 广告活动小时级数据导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void campaignHourData(GetCampaignHourReportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("广告活动小时数据导出 request {}", request);
        NeTargetingDataResponse.Builder builder = NeTargetingDataResponse.newBuilder();
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()
                || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        List<String> urls = Lists.newLinkedList();
        CampaignHourParam param = new CampaignHourParam();
        param.setCampaignId(request.getCampaignId());
        param.setWeeks(request.getWeeks());
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setStartDate(request.getStartDateStr());
        param.setEndDate(request.getEndDateStr());
        param.setEndDateCompare(request.getEndDateCompareStr());
        param.setStartDateCompare(request.getStartDateCompareStr());
        if (request.hasIsCompare()) {
            param.setIsCompare(request.getIsCompare().getValue());
        }
        param.setFindType(request.getFindType());
        param.setFindValue(request.getFindValue());
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getShopId(), param.getStartDate().replaceAll("-", ""), param.getEndDate().replaceAll("-", ""));
        List<AdCampaignHourVo> voList = new ArrayList<>();
        String downloadUrl;
        boolean bool = false;
        if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY) {
            voList = amazonAdCampaignHourReportService.getList(request.getPuid().getValue(), param);
            bool = true;
        } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.DAILY) {
            voList = amazonAdCampaignHourReportService.getDayCompareList(request.getPuid().getValue(), param);
        } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.WEEKLY) {
            voList = amazonAdCampaignHourReportService.getWeekCompareList(request.getPuid().getValue(), param);
        } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.MONTHLY) {
            voList = amazonAdCampaignHourReportService.getMonthCompareList(request.getPuid().getValue(), param);
        }

        if (CollectionUtils.isEmpty(voList)) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("process.msg.sync.fail");
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllDao.getByCampaignId(request.getPuid().getValue(), param.getShopId(), param.getCampaignId());
        String exportType = amazonAdCampaignAll != null ? amazonAdCampaignAll.getType() : Constants.SP;
        ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
        List<String> voExcludeFileds = new ArrayList<>();
        if (!Integer.valueOf(1).equals(param.getIsCompare())) {
            voExcludeFileds = Lists.newArrayList("costCompare", "costCompareRate",
                    "impressionsCompare", "impressionsCompareRate",
                    "clicksCompare", "clicksCompareRate",
                    "adOrderNumCompare", "adOrderNumCompareRate",
                    "adSalesCompare", "adSalesCompareRate",
                    "adSaleNumCompare", "adSaleNumCompareRate",
                    "cpcCompare", "cpcCompareRate",
                    "clickRateCompare", "clickRateCompareRate",
                    "salesConversionRateCompare", "salesConversionRateCompareRate",
                    "acosCompare", "acosCompareRate",
                    "roasCompare", "roasCompareRate");
        }
        String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
        List<AdCampaignHourExportVo> datas = new ArrayList<>(24);
        List<AdSpCampaignHourExportVo> spDatas = new ArrayList<>(24);
        // 暂时对sp做特殊处理
        for (AdCampaignHourVo vo : voList) {
            vo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate));
            vo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate));
            if (Constants.SP.equalsIgnoreCase(exportType)) {
                spDatas.add(new AdSpCampaignHourExportVo(currencyCode, vo));
                continue;
            }
            datas.add(new AdCampaignHourExportVo(currencyCode, vo));
        }
        // 代表小时级的导出对象
        if (bool) {
            if (Constants.SD.equalsIgnoreCase(exportType) || Constants.SB.equalsIgnoreCase(exportType)) {
                voExcludeFileds.add("adSelfSaleNum");
                voExcludeFileds.add("adOtherSaleNum");
            }
            if (Constants.SB.equalsIgnoreCase(exportType)) {
                voExcludeFileds.add("vcpm");
            }

            if (Constants.SD.equalsIgnoreCase(exportType) && Constants.SD_REPORT_VCPM.equalsIgnoreCase(amazonAdCampaignAll.getCostType())) {
                voExcludeFileds.add("selfAdOrderNum");
                voExcludeFileds.add("otherAdOrderNum");
                voExcludeFileds.add("advertisingProductUnitPrice");
                voExcludeFileds.add("advertisingOtherProductUnitPrice");
            }
        } else {
            voExcludeFileds.addAll(Lists.newArrayList("acots", "asots", "unitsOrderedNewToBrandPercentage", "unitsOrderedNewToBrand", "salesNewToBrandPercentage", "salesNewToBrand", "ordersNewToBrandPercentage", "ordersNewToBrand",
                    "advertisingOtherProductUnitPrice", "advertisingProductUnitPrice", "advertisingUnitPrice", "vcpm", "vctr", "vrt", "viewableImpressions", "adCostPercentage", "adOrderNumPercentage", "adSalePercentage", "orderNumPercentage"));
        }

        try {
            WriteHandlerBuild build = new WriteHandlerBuild().rate();
            if (Constants.SP.equalsIgnoreCase(exportType)) {
                //excel币种表头渲染
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), spDatas, request.getFileName() + "(0)", AdSpCampaignHourExportVo.class, build.currencyNew(AdSpCampaignHourExportVo.class), voExcludeFileds);
            } else {
                //excel币种表头渲染
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas, request.getFileName() + "(0)", AdCampaignHourExportVo.class, build.currencyNew(AdCampaignHourExportVo.class), voExcludeFileds);
            }
            urls.add(downloadUrl);
            urlBuilder.addAllUrls(urls);
            urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
        } catch (Exception e) {
            log.error("NeKeywords err(手动商品否定投放):{},puid{},shopId{}", e, request.getPuid(), request.getShopId());
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("process.msg.sync.fail");
        } finally {
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
        }

    }

    @Override
    public void campaignWeekData(GetCampaignWeekReportRequestPb.GetCampaignWeekReportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()
                    || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr()))) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();
                CampaignHourParam param = new CampaignHourParam();
                param.setCampaignId(request.getCampaignId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setIsCompare(request.getIsCompare().getValue());
                param.setStartDateCompare(request.getStartDateCompareStr());
                param.setEndDateCompare(request.getEndDateCompareStr());
                AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllDao.getByCampaignId(request.getPuid().getValue(), request.getShopId().getValue(), request.getCampaignId());
                boolean noSpType = amazonAdCampaignAll != null && (Constants.SB.equalsIgnoreCase(amazonAdCampaignAll.getType()) || Constants.SD.equalsIgnoreCase(amazonAdCampaignAll.getType()));
                String costType = amazonAdCampaignAll != null && StringUtils.isNotBlank(amazonAdCampaignAll.getCostType()) ? amazonAdCampaignAll.getCostType() : Constants.SD_REPORT_CPC;
                ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdCampaignWeekExportVo> datas = new ArrayList<>();
                List<AdSpCampaignWeekExportVo> spDatas = new ArrayList<>();
                WriteHandlerBuild build = new WriteHandlerBuild().rate();

                BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(auth.getId(), request.getStartDateStr().replace("-", ""), request.getEndDateStr().replace("-", ""));
                if (noSpType) {
                    AdPageBasicData adPageBasicData = AdPageBasicData.newBuilder()
                            .setStartDate(request.getStartDateStr())
                            .setEndDate(request.getEndDateStr())
                            .setPuid(request.getPuid())
                            .setShopId(request.getShopId())
                            .setIsCompare(request.getIsCompare())
                            .setStartDateCompare(StringUtils.defaultString(request.getStartDateCompareStr()))
                            .setEndDateCompare(StringUtils.defaultString(request.getEndDateCompareStr()))
                            .build();
                    AdHourReportRequest adHourReportRequest = AdHourReportRequest.newBuilder().setCampaignId(request.getCampaignId()).setPageBasic(adPageBasicData).build();
                    List<AdReportWeeklyDayVO> adReportWeeklyDayVOS = amazonAdFeedReportService.listAdReportWeeklyDayVOByAdHourReportRequest(auth, adHourReportRequest, amazonAdCampaignAll.getType(), costType);
                    if (CollectionUtils.isNotEmpty(adReportWeeklyDayVOS)) {
                        adReportWeeklyDayVOS.forEach(key -> {
                            if (CollectionUtils.isNotEmpty(key.getDetails())) {
                                List<AdReportHourlyVO> adReportHourlyVOS1 = key.getDetails();
                                for (AdReportHourlyVO adReportHourlyVO : adReportHourlyVOS1) {
                                    adReportHourlyVO.setWeekDay(key.getWeekDay());
                                    adReportHourlyVO.setAcots(MathUtil.divideIntegerByOneHundred(adReportHourlyVO.getAdCost(), shopSalesByDate));
                                    adReportHourlyVO.setAsots(MathUtil.divideIntegerByOneHundred(adReportHourlyVO.getAdSale(), shopSalesByDate));
                                    datas.add(new AdCampaignWeekExportVo(currencyCode, adReportHourlyVO));
                                }
                            }
                        });
                    }
                    List<String> excludeFields = Lists.newArrayList("adSelfSaleNum", "adOtherSaleNum");
                    if (Constants.SB.equalsIgnoreCase(amazonAdCampaignAll.getType())) {
                        excludeFields.add("vcpm");
                    }
                    if (request.getIsCompare().getValue() != 1) {
                        excludeFields.addAll(Lists.newArrayList("costCompare", "costCompareRate",
                                "impressionsCompare", "impressionsCompareRate",
                                "clicksCompare", "clicksCompareRate",
                                "adOrderNumCompare", "adOrderNumCompareRate",
                                "adSalesCompare", "adSalesCompareRate",
                                "adSaleNumCompare", "adSaleNumCompareRate",
                                "cpcCompare", "cpcCompareRate",
                                "clickRateCompare", "clickRateCompareRate",
                                "salesConversionRateCompare", "salesConversionRateCompareRate",
                                "acosCompare", "acosCompareRate",
                                "roasCompare", "roasCompareRate"));
                    }
                    downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                            request.getFileName() + "(0)", AdCampaignWeekExportVo.class,
                            build.currencyNew(AdCampaignWeekExportVo.class), excludeFields);
                } else {
                    List<AdCampaignWeekDayVo> campaignWeekDayVoList = amazonAdCampaignHourReportService.getWeeklySuperpositionList(request.getPuid().getValue(), param);
                    if (CollectionUtils.isNotEmpty(campaignWeekDayVoList)) {
                        campaignWeekDayVoList.forEach(e -> {
                            if (CollectionUtils.isNotEmpty(e.getDetails())) {
                                List<AdCampaignHourVo> adCampaignHourVos = e.getDetails();
                                for (AdCampaignHourVo adReportHourlyVO : adCampaignHourVos) {
                                    spDatas.add(new AdSpCampaignWeekExportVo(currencyCode, adReportHourlyVO));
                                }
                            }
                        });
                    }
                    List<String> excludeFields = Lists.newArrayList();
                    if (request.getIsCompare().getValue() != 1) {
                        excludeFields.addAll(Lists.newArrayList("costCompare", "costCompareRate",
                                "impressionsCompare", "impressionsCompareRate",
                                "clicksCompare", "clicksCompareRate",
                                "adOrderNumCompare", "adOrderNumCompareRate",
                                "adSalesCompare", "adSalesCompareRate",
                                "adSaleNumCompare", "adSaleNumCompareRate",
                                "cpcCompare", "cpcCompareRate",
                                "clickRateCompare", "clickRateCompareRate",
                                "salesConversionRateCompare", "salesConversionRateCompareRate",
                                "acosCompare", "acosCompareRate",
                                "roasCompare", "roasCompareRate"));
                    }
                    downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), spDatas,
                            request.getFileName() + "(0)", AdSpCampaignWeekExportVo.class,
                            build.currencyNew(AdSpCampaignWeekExportVo.class), excludeFields);
                }
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    /**
     * 导出周叠加数据
     *
     * @param request          request
     * @param responseObserver responseObserver
     */
    @Override
    public void exportCommonWeekData(HourlyWeeklySuperpositionCommonReq request, StreamObserver<UrlResponse> responseObserver) {
        UrlResponse.Builder builder = UrlResponse.newBuilder();
        log.info("exportCommonWeekData, request:{}", request);
        if (!isValidRequest(request)) {
            setErrorResponse(builder, "Request param error");
            log.error("exportCommonWeekData, Request param error, request:{}", request);
        } else {
            try {
                List<String> urls = new ArrayList<>();
                String downloadUrl = adWeeklyReportAnalysisService.exportCommonWeekData(request);
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            } catch (Exception e) {
                setErrorResponse(builder, "exportCommonWeekData error");
                log.error("exportCommonWeekData error, request:{}", request, e);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 多店铺投放导出周叠加数据
     */
    @Override
    public void exportTargetWeekData(HourlyWeeklySuperpositionTargetReq request, StreamObserver<UrlResponse> responseObserver) {
        UrlResponse.Builder builder = UrlResponse.newBuilder();
        log.info("exportCommonWeekData, request:{}", request);
        if (!isValidRequest(request)) {
            setErrorResponse(builder, "Request param error");
            log.error("exportCommonWeekData, Request param error, request:{}", request);
        } else {
            try {
                List<String> urls = new ArrayList<>();
                String downloadUrl = adWeeklyReportAnalysisService.exportTargetWeekData(request);
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            } catch (Exception e) {
                String msg = "getCommonWeeklyReport error";
                if(e instanceof SponsoredBizException){
                    msg = e.getMessage();
                }
                setErrorResponse(builder, msg);
                log.error("exportCommonWeekData error, request:{}", request, e);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private boolean isValidRequest(HourlyWeeklySuperpositionCommonReq req) {
        return req.hasPuid() && req.hasShopId() && req.hasType() && req.hasEndDate() && req.hasStartDate() && req.hasLevelType()
                && req.hasWeeks() && (req.getAggregated() || req.hasCommonId());
    }
    private boolean isValidRequest(HourlyWeeklySuperpositionTargetReq req) {
        return req.hasPuid() && CollectionUtils.isNotEmpty(req.getShopIdListList()) && req.hasType() && req.hasEndDate() && req.hasStartDate() && req.hasLevelType()
                && req.hasWeeks() && (req.getAggregated() || req.hasCommonId());
    }

    private void setErrorResponse(UrlResponse.Builder builder, String errorMsg) {
        builder.setCode(Int32Value.of(Result.ERROR));
        builder.setMsg(errorMsg);
    }

    /**
     * 广告位小时级数据导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void exportPlacementHourData(GetPlacementHourReportRequestPb.GetPlacementHourReportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("广告位小时数据导出 request {}", request);
        NeTargetingDataResponse.Builder builder = NeTargetingDataResponse.newBuilder();
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr() || !request.hasPredicate()
                || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        int count = 0;
        String downloadUrl = null;
        List<String> urls = Lists.newLinkedList();
        PlacementHourParam param = new PlacementHourParam();
        param.setCampaignId(request.getCampaignId());
        param.setPredicate(request.getPredicate());
        param.setCampaignSite(request.getCampaignSite());
        param.setWeeks(request.getWeeks());
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setStartDate(request.getStartDateStr());
        param.setEndDate(request.getEndDateStr());
        param.setEndDateCompare(request.getEndDateCompareStr());
        param.setStartDateCompare(request.getStartDateCompareStr());
        if (request.hasIsCompare()) {
            param.setIsCompare(request.getIsCompare().getValue());
        }
        param.setFindType(request.getFindType());
        param.setFindValue(request.getFindValue());

        List<AdPlacementHourVo> voList = new ArrayList<>();
        if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY) {
            voList = amazonAdPlacementHourReportService.getList(request.getPuid().getValue(), param);
        } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.DAILY) {
            voList = amazonAdPlacementHourReportService.getDayList(request.getPuid().getValue(), param, null);
        } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.WEEKLY) {
            voList = amazonAdPlacementHourReportService.getWeekList(request.getPuid().getValue(), param, null);
        } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.MONTHLY) {
            voList = amazonAdPlacementHourReportService.getMonthList(request.getPuid().getValue(), param, null);
        }

        if (CollectionUtils.isEmpty(voList)) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("process.msg.sync.none");
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
        List<String> voExcludeFileds = new ArrayList<>();
        if (!Integer.valueOf(1).equals(param.getIsCompare())) {
            voExcludeFileds = Lists.newArrayList("costCompare", "costCompareRate", "impressionsCompare", "impressionsCompareRate",
                    "clicksCompare", "clicksCompareRate", "cpaCompare", "cpaCompareRate", "cpcCompare", "cpcCompareRate", "ctrCompare",
                    "ctrCompareRate", "cvrCompare", "cvrCompareRate", "acosCompare", "acosCompareRate", "roasCompare", "roasCompareRate",
                    "adOrderNumCompare", "adOrderNumCompareRate", "selfAdOrderNumCompare", "selfAdOrderNumCompareRate", "otherAdOrderNumCompare",
                    "otherAdOrderNumCompareRate", "adSalesCompare", "adSalesCompareRate", "adSelfSalesCompare", "adSelfSalesCompareRate",
                    "adOtherSalesCompare", "adOtherSalesCompareRate", "adSaleNumCompare", "adSaleNumCompareRate", "adSelfSaleNumCompare",
                    "adSelfSaleNumCompareRate", "adOtherSaleNumCompare", "adOtherSaleNumCompareRate");
        }
        String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
        List<AdPlacementHourExportVo> datas = new ArrayList<>(24);
        for (AdPlacementHourVo vo : voList) {
            AdPlacementHourExportVo exVo = new AdPlacementHourExportVo(currencyCode, vo);
            datas.add(exVo);
        }
        try {
            //excel币种表头渲染
            WriteHandlerBuild build = new WriteHandlerBuild().rate();
            downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas, request.getFileName() + "(" + count++ + ")", AdPlacementHourExportVo.class, build.currencyNew(AdPlacementHourExportVo.class), voExcludeFileds);
            urls.add(downloadUrl);
            urlBuilder.addAllUrls(urls);
            urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
        } catch (Exception e) {
            log.error("puid{},shopId{}广告位小时数据导出异常:{}", request.getPuid(), request.getShopId(), e);
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("process.msg.sync.fail");
        } finally {
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
        }
    }

    /**
     * 广告位周叠加数据导出
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void exportPlacementWeekData(GetPlacementWeekReportRequestPb.GetPlacementWeekReportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr() || !request.hasCampaignId() || !request.hasPredicate()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();

                PlacementHourParam param = new PlacementHourParam();
                param.setCampaignId(request.getCampaignId());
                param.setPredicate(request.getPredicate());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                List<AdPlacementHourVo> voList = new ArrayList<>();
                List<AdPlacementWeekDayVo> placementWeekDayVoList = amazonAdPlacementHourReportService.getWeeklySuperpositionList(request.getPuid().getValue(), param);
                if (CollectionUtils.isNotEmpty(placementWeekDayVoList)) {
                    placementWeekDayVoList.forEach(e -> {
                        voList.addAll(e.getDetails());
                    });
                }
                ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdPlacementWeekExportVo> datas = new ArrayList<>();
                for (AdPlacementHourVo vo : voList) {
                    AdPlacementWeekExportVo exVo = new AdPlacementWeekExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdPlacementWeekExportVo.class,
                        build.currencyNew(AdPlacementWeekExportVo.class));
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("puid{},shopId{}广告位周叠加数据导出:{}", request.getPuid(), request.getShopId(), e);
            responseObserver.onError(e);
        }
    }


    @Override
    public void exportGroupHourData(GetGroupHourReportRequestPb.GetGroupHourReportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("广告组小时数据导出 request {}", request);
        NeTargetingDataResponse.Builder builder = NeTargetingDataResponse.newBuilder();
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr() || !request.hasAdGroupId()
                || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
            return;
        }


        String campaignType = StringUtils.isNotBlank(request.getType()) ? request.getType() : Constants.SP;
        String costType = Constants.SD_REPORT_CPC;
        if (Constants.SD.equalsIgnoreCase(campaignType) && StringUtils.isNotBlank(request.getAdGroupId())) {
            List<String> adCampaignIdsByGroupIds = amazonSdAdGroupDao.getAdCampaignIdsByGroupIds(request.getPuid().getValue(), request.getShopId().getValue(), null, Lists.newArrayList(request.getAdGroupId()));
            if (CollectionUtils.isNotEmpty(adCampaignIdsByGroupIds) && StringUtils.isNotBlank(adCampaignIdsByGroupIds.get(0))) {
                AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllDao.getByCampaignId(request.getPuid().getValue(), request.getShopId().getValue(), adCampaignIdsByGroupIds.get(0));
                if (amazonAdCampaignAll != null && StringUtils.isNotBlank(amazonAdCampaignAll.getCostType())) {
                    costType = amazonAdCampaignAll.getCostType();
                }
            }
        }

        int count = 0;
        String downloadUrl;
        List<String> urls = Lists.newLinkedList();
        AdHourReportRequest.Builder param = AdHourReportRequest.newBuilder();
        AdPageBasicData.Builder pageBasicInfo = AdPageBasicData.newBuilder();
        param.setCampaignId(request.getCampaignId());
        param.setGroupId(request.getAdGroupId());
        param.setWeeks(request.getWeeks());
        pageBasicInfo.setPuid(request.getPuid());
        pageBasicInfo.setShopId(request.getShopId());
        pageBasicInfo.setStartDate(request.getStartDateStr());
        pageBasicInfo.setEndDate(request.getEndDateStr());
        pageBasicInfo.setEndDateCompare(request.getEndDateCompareStr());
        pageBasicInfo.setStartDateCompare(request.getStartDateCompareStr());
        pageBasicInfo.setType(Optional.ofNullable(request.getType()).filter(StringUtils::isNotBlank).orElse(Constants.SP));
        if (request.hasIsCompare()) {
            pageBasicInfo.setIsCompare(request.getIsCompare());
        }
        AdPageBasicData pageBasic = pageBasicInfo.build();
        param.setPageBasic(pageBasic);
        AdHourReportRequest requestParam = param.build();
        ShopAuth auth = shopAuthDao.getScAndVcById(request.getShopId().getValue());
        List<AdReportHourlyVO> voList = new ArrayList<>();
        ReportDateModelPb.ReportDateModel dateModel = Optional.of(request.getDateModel()).orElse(ReportDateModelPb.ReportDateModel.HOURLY);
        Supplier<List<AdReportHourlyVO>> reportListSup = () -> cpcAdGroupService.getAdGroupDailyReport(request.getPuid().getValue(), requestParam, false);
        Supplier<List<AdReportHourlyVO>> reportListCompareSup = null;
        Int32Value isCompare = param.getPageBasic().getIsCompare();
        boolean booleanCompare = isCompare.getValue() == 1;
        if (booleanCompare) {
            reportListCompareSup = () -> cpcAdGroupService.getAdGroupDailyReport(request.getPuid().getValue(), requestParam, true);
        }
        if (dateModel == ReportDateModelPb.ReportDateModel.HOURLY) {
            voList = adHourlyReportAnalysisService.getHourReportListFromDoris(auth, requestParam, x -> amazonMarketingStreamDataDao.groupStatisticsByHour(x), campaignType, costType);
        } else if (dateModel == ReportDateModelPb.ReportDateModel.DAILY) {
            if (booleanCompare && reportListCompareSup != null) {
                voList = adHourlyReportAnalysisService.getDayReportList(requestParam, reportListSup.get(), reportListCompareSup.get());
            } else {
                voList = adHourlyReportAnalysisService.getDayReportList(reportListSup.get());
            }
        } else if (dateModel == ReportDateModelPb.ReportDateModel.WEEKLY) {
            if (booleanCompare && reportListCompareSup != null) {
                voList = adHourlyReportAnalysisService.getWeekReportList(requestParam, reportListSup.get(), reportListCompareSup.get());
            } else {
                voList = adHourlyReportAnalysisService.getWeekReportList(requestParam, reportListSup.get());
            }
        } else if (dateModel == ReportDateModelPb.ReportDateModel.MONTHLY) {
            if (booleanCompare && reportListCompareSup != null) {
                voList = adHourlyReportAnalysisService.getMonthReportList(requestParam, reportListSup.get(), reportListCompareSup.get());
            } else {
                voList = adHourlyReportAnalysisService.getMonthReportList(reportListSup.get());
            }
        }



        if (CollectionUtils.isEmpty(voList)) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("process.msg.sync.none");
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        List<String> voExcludeFileds = new ArrayList<>();
        if (!Integer.valueOf(1).equals(pageBasic.getIsCompare().getValue())) {
            voExcludeFileds = Lists.newArrayList("costCompare", "costCompareRate", "impressionsCompare", "impressionsCompareRate",
                    "clicksCompare", "clicksCompareRate", "cpaCompare", "cpaCompareRate", "cpcCompare", "cpcCompareRate", "ctrCompare",
                    "ctrCompareRate", "cvrCompare", "cvrCompareRate", "acosCompare", "acosCompareRate", "roasCompare", "roasCompareRate",
                    "adOrderNumCompare", "adOrderNumCompareRate", "selfAdOrderNumCompare", "selfAdOrderNumCompareRate", "otherAdOrderNumCompare",
                    "otherAdOrderNumCompareRate", "adSalesCompare", "adSalesCompareRate", "adSelfSalesCompare", "adSelfSalesCompareRate",
                    "adOtherSalesCompare", "adOtherSalesCompareRate", "adSaleNumCompare", "adSaleNumCompareRate", "adSelfSaleNumCompare",
                    "adSelfSaleNumCompareRate", "adOtherSaleNumCompare", "adOtherSaleNumCompareRate", "acotsCompare", "acotsCompareRate",
                    "asotsCompare", "asotsCompareRate");
        }

        if (Constants.SD.equalsIgnoreCase(campaignType) && Constants.SD_REPORT_CPC.equalsIgnoreCase(costType)) {
            //去掉
            voExcludeFileds.addAll(Lists.newArrayList("vctr", "vCtr", "vcpm", "vrt", "viewableImpressions"
            ));
        }

        if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY) {
            if (Constants.SP.equalsIgnoreCase(request.getType())) {
                voExcludeFileds.addAll(Lists.newArrayList(
                        "advertisingOtherProductUnitPrice", "ordersNewToBrandPercentage", "unitsOrderedNewToBrandPercentage",
                        "salesNewToBrandPercentage", "adCostPercentage", "acots", "acotsCompare", "acotsCompareRate",
                        "asots", "asotsCompare", "asotsCompareRate", "adSalePercentage", "adOrderNumPercentage", "orderNumPercentage"
                ));
                //去掉
                voExcludeFileds.addAll(Lists.newArrayList("salesNewToBrandPercentage", "unitsOrderedNewToBrandPercentage", "ordersNewToBrandPercentage",
                        "advertisingOtherProductUnitPrice", "advertisingProductUnitPrice", "advertisingUnitPrice", "salesNewToBrand",
                        "unitsOrderedNewToBrand", "ordersNewToBrand", "vctr", "vCtr", "vcpm", "vrt", "viewableImpressions"
                ));
            } else {
                if (Constants.SB.equalsIgnoreCase(request.getType()) || Constants.SD.equalsIgnoreCase(request.getType())) {
                    voExcludeFileds.addAll(Lists.newArrayList(
                            "adSelfSaleNum", "adSelfSaleNumCompareRate", "adOtherSaleNum",
                            "adOtherSaleNumCompare", "adOtherSaleNumCompareRate"
                    ));
                }
                if (Constants.SB.equalsIgnoreCase(request.getType())) {
                    voExcludeFileds.add("vcpm");
                }

            }
        } else {
            voExcludeFileds.addAll(Lists.newArrayList(
                    "advertisingOtherProductUnitPrice", "ordersNewToBrandPercentage", "unitsOrderedNewToBrandPercentage",
                    "salesNewToBrandPercentage", "adCostPercentage", "acots", "acotsCompare", "acotsCompareRate",
                    "asots", "asotsCompare", "asotsCompareRate", "adSalePercentage", "adOrderNumPercentage", "orderNumPercentage"
            ));
            //去掉
            voExcludeFileds.addAll(Lists.newArrayList("salesNewToBrandPercentage", "unitsOrderedNewToBrandPercentage", "ordersNewToBrandPercentage",
                    "advertisingOtherProductUnitPrice", "advertisingProductUnitPrice", "advertisingUnitPrice", "salesNewToBrand",
                    "unitsOrderedNewToBrand", "ordersNewToBrand", "vctr", "vCtr", "vcpm", "vrt", "viewableImpressions"
            ));
        }
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(auth.getId(), request.getStartDateStr().replace("-", ""), request.getEndDateStr().replace("-", ""));
        String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
        List<AdReportHourlyExportVo> datas = new ArrayList<>(24);
        for (AdReportHourlyVO vo : voList) {
            if (Constants.SB.equalsIgnoreCase(request.getType()) || Constants.SD.equalsIgnoreCase(request.getType())) {
                vo.setAdOtherSaleNum(0);
                vo.setAdOtherSaleNumCompare(0);
                vo.setAdOtherSaleNumCompareRate(BigDecimal.ZERO);
            }
            vo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate));
            vo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate));
            AdReportHourlyExportVo exVo = new AdReportHourlyExportVo(currencyCode, vo);
            datas.add(exVo);
        }
        try {
            //excel币种表头渲染
            WriteHandlerBuild build = new WriteHandlerBuild().rate();
            downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas, request.getFileName() + "(" + count++ + ")", AdReportHourlyExportVo.class, build.currencyNew(AdReportHourlyExportVo.class), voExcludeFileds);
            urls.add(downloadUrl);
            urlBuilder.addAllUrls(urls);
            urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
        } catch (Exception e) {
            log.error("puid{}, shopId{}广告组小时数据导出异常:{}", request.getPuid(), request.getShopId(), e);
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("process.msg.sync.fail");
        } finally {
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void exportGroupWeekData(GetGroupWeekReportRequestPb.GetGroupWeekReportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr() || !request.hasAdGroupId()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();

                AdHourReportRequest.Builder paramBuilder = AdHourReportRequest.newBuilder();
                AdPageBasicData.Builder pageBasicInfo = AdPageBasicData.newBuilder();
                paramBuilder.setCampaignId(request.getCampaignId());
                paramBuilder.setGroupId(request.getAdGroupId());
                pageBasicInfo.setPuid(Int32Value.of(request.getPuid().getValue()));
                pageBasicInfo.setShopId(Int32Value.of(request.getShopId().getValue()));
                pageBasicInfo.setStartDate(request.getStartDateStr());
                pageBasicInfo.setEndDate(request.getEndDateStr());
                pageBasicInfo.setType(Optional.of(request.getType()).orElse(Constants.SP));
                paramBuilder.setPageBasic(pageBasicInfo);
                List<AdReportWeeklyDayVO> weeklyList = adWeeklyReportAnalysisService.getSuperpositionList(paramBuilder.build(), x -> adReportWeeklyAMSApiServiceBlockingStub.getAdGroupReportWeekly(x));
                ShopAuth auth = shopAuthDao.getScAndVcById(pageBasicInfo.getShopId().getValue());
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdReportHourlyVO> voList = Lists.newArrayList();
                List<AdReportWeekExportVo> datas = new ArrayList<>();
                //处理所有天数据
                if (CollectionUtils.isNotEmpty(weeklyList)) {
                    weeklyList.forEach(e -> {
                        voList.addAll(e.getDetails());
                    });
                }
                for (AdReportHourlyVO vo : voList) {
                    AdReportWeekExportVo exVo = new AdReportWeekExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdReportWeekExportVo.class,
                        build.currencyNew(AdReportWeekExportVo.class));
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("puid{},shopId{}广告位周叠加数据导出:{}", request.getPuid(), request.getShopId(), e);
            responseObserver.onError(e);
        }
    }

    @Override
    public void keywordHourData(GetKeywordHourReportRequestPb.GetKeywordHourReportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()
                    || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr()))) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();

                KeywordHourParam param = new KeywordHourParam();
                param.setKeywordId(request.getKeywordId());
                param.setWeeks(request.getWeeks());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setEndDateCompare(request.getEndDateCompareStr());
                param.setStartDateCompare(request.getStartDateCompareStr());
                if (request.hasIsCompare()) {
                    param.setIsCompare(request.getIsCompare().getValue());
                }
                param.setType(request.getType());
                param.setFindType(request.getFindType());
                param.setFindValue(request.getFindValue());
                List<AdKeywordAndTargetHourVo> voList = new ArrayList<>();

                if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY) {
                    if ("SP".equalsIgnoreCase(request.getType())) {
                        voList = amazonAdKeywordHourReportService.getHourList(param.getPuid(), param);
                    } else {
                        voList = amazonAdKeywordHourReportService.getSbHourList(param.getPuid(), param);
                    }
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.DAILY) {
                    voList = amazonAdKeywordHourReportService.getDailyList(param.getPuid(), request.getAdType(), param, null);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.WEEKLY) {
                    voList = amazonAdKeywordHourReportService.getWeeklyList(param.getPuid(), request.getAdType(), param, null);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.MONTHLY) {
                    voList = amazonAdKeywordHourReportService.getMonthlyList(param.getPuid(), request.getAdType(), param, null);
                }

                ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
                List<String> voExcludeFileds = new ArrayList<>();
                if (!Integer.valueOf(1).equals(param.getIsCompare())) {
                    voExcludeFileds = Lists.newArrayList("costCompare", "costCompareRate", "impressionsCompare", "costCompare", "impressionsCompareRate",
                            "clicksCompareRate", "adOrderNumCompare", "adOrderNumCompareRate", "adSalesCompare",
                            "adSalesCompareRate", "adSaleNumCompare", "adSaleNumCompareRate", "clicksCompare");
                    voExcludeFileds.addAll(Lists.newArrayList("cpcCompare", "cpcCompareRate",
                            "clickRateCompare", "clickRateCompareRate",
                            "salesConversionRateCompare", "salesConversionRateCompareRate",
                            "acosCompare", "acosCompareRate",
                            "roasCompare", "roasCompareRate"));
                }
                //SB不需要销量字段
                if ("SB".equalsIgnoreCase(request.getType())) {
                    voExcludeFileds.addAll(Lists.newArrayList("vcpm"));
                }
                //SD
                if ("SD".equalsIgnoreCase(request.getType())) {

                }
                if (request.getDateModel() != ReportDateModelPb.ReportDateModel.HOURLY) {
                    voExcludeFileds.addAll(Lists.newArrayList("acots", "asots", "viewableImpressions", "vrt", "vctr", "ordersNewToBrand",
                            "unitsOrderedNewToBrand", "salesNewToBrand", "advertisingUnitPrice", "advertisingProductUnitPrice", "advertisingOtherProductUnitPrice",
                            "ordersNewToBrandPercentage", "unitsOrderedNewToBrandPercentage", "salesNewToBrandPercentage",
                            "adCostPercentage", "adSalePercentage", "adOrderNumPercentage", "orderNumPercentage"));
                }
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdKeywordTargetHourWithCompareExportVo> datas = new ArrayList<>(24);
                for (AdKeywordAndTargetHourVo vo : voList) {
                    AdKeywordTargetHourWithCompareExportVo exVo = new AdKeywordTargetHourWithCompareExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                if (request.getDateModel() != ReportDateModelPb.ReportDateModel.HOURLY) {
                    Field filed = AdKeywordTargetHourWithCompareExportVo.class.getDeclaredField("label");
                    filed.setAccessible(true);
                    ExcelProperty annotation = filed.getAnnotation(ExcelProperty.class);
                    InvocationHandler invocationHandler = Proxy.getInvocationHandler(annotation);
                    Field memberValues = invocationHandler.getClass().getDeclaredField("memberValues");
                    memberValues.setAccessible(true);
                    Map<String, Object> map = (Map<String, Object>) memberValues.get(invocationHandler);
                    String[] valueArr = {"日期"};
                    map.put("value", valueArr);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdKeywordTargetHourWithCompareExportVo.class,
                        build.currencyNew(AdKeywordTargetHourWithCompareExportVo.class), voExcludeFileds);
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    @Override
    public void keywordHourOfAdData(GetKeywordHourReportOfAdRequestPb.GetKeywordHourReportOfAdRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();

                KeywordHourParam param = new KeywordHourParam();
                param.setKeywordId(request.getKeywordId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setFindType(request.getFindType());
                param.setFindValue(request.getFindValue());
                List<AdKeywordAndTargetHourVo> voList =
                        amazonAdKeywordHourReportService.getDetailListOfAd(request.getPuid().getValue(), param);
                ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdKeywordTargetHourOfAdExportVo> datas = new ArrayList<>(24);
                for (AdKeywordAndTargetHourVo vo : voList) {
                    AdKeywordTargetHourOfAdExportVo exVo = new AdKeywordTargetHourOfAdExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdKeywordTargetHourOfAdExportVo.class,
                        build.currencyNew(AdKeywordTargetHourOfAdExportVo.class));
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    @Override
    public void keywordHourOfPlacementData(GetKeywordHourReportOfPlacementRequestPb.GetKeywordHourReportOfPlacementRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();

                KeywordHourParam param = new KeywordHourParam();
                param.setKeywordId(request.getKeywordId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setFindType(request.getFindType());
                param.setFindValue(request.getFindValue());
                List<AdKeywordAndTargetHourVo> voList =
                        amazonAdKeywordHourReportService.getDetailListOfPlacement(request.getPuid().getValue(), param);
                ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdKeywordTargetTargetHourOfPlacementExportVo> datas = new ArrayList<>(24);
                for (AdKeywordAndTargetHourVo vo : voList) {
                    AdKeywordTargetTargetHourOfPlacementExportVo exVo =
                            new AdKeywordTargetTargetHourOfPlacementExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdKeywordTargetTargetHourOfPlacementExportVo.class,
                        build.currencyNew(AdKeywordTargetTargetHourOfPlacementExportVo.class));
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    @Override
    public void targetHourData(GetTargetHourReportRequestPb.GetTargetHourReportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();

                TargetHourParam param = new TargetHourParam();
                param.setTargetId(request.getTargetId());
                param.setWeeks(request.getWeeks());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setEndDateCompare(request.getEndDateCompareStr());
                param.setStartDateCompare(request.getStartDateCompareStr());
                if (request.hasIsCompare()) {
                    param.setIsCompare(request.getIsCompare().getValue());
                }
                param.setType(request.getType());
                param.setFindType(request.getFindType());
                param.setFindValue(request.getFindValue());
                List<AdKeywordAndTargetHourVo> voList = new ArrayList<>();
                if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY) {
                    if ("SP".equalsIgnoreCase(request.getType())) {
                        voList = amazonAdTargetHourReportService.getList(request.getPuid().getValue(), param);
                    } else {
                        voList = amazonAdTargetHourReportService.getSbAndSdHourList(request.getPuid().getValue(), param);
                    }
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.DAILY) {
                    voList = amazonAdTargetHourReportService.getDailyList(param.getPuid(), request.getAdType(), param, null);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.WEEKLY) {
                    voList = amazonAdTargetHourReportService.getWeeklyList(param.getPuid(), request.getAdType(), param, null);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.MONTHLY) {
                    voList = amazonAdTargetHourReportService.getMonthlyList(param.getPuid(), request.getAdType(), param, null);
                }

                ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
                List<String> voExcludeFileds = new ArrayList<>();
                if (!Integer.valueOf(1).equals(param.getIsCompare())) {
                    voExcludeFileds = Lists.newArrayList("costCompare", "costCompareRate", "impressionsCompare", "costCompare", "impressionsCompareRate",
                            "clicksCompareRate", "adOrderNumCompare", "adOrderNumCompareRate", "adSalesCompare",
                            "adSalesCompareRate", "adSaleNumCompare", "adSaleNumCompareRate", "clicksCompare",
                            "cpcCompare", "cpcCompareRate", "clickRateCompare", "clickRateCompareRate",
                            "salesConversionRateCompare", "salesConversionRateCompareRate", "acosCompare", "acosCompareRate",
                            "roasCompare", "roasCompareRate");

                }
                //SB不需要销量字段
                if ("SB".equalsIgnoreCase(request.getType())) {
                    voExcludeFileds.addAll(Lists.newArrayList("vcpm"));
                }
                //SB不需要销量字段
                if ("SD".equalsIgnoreCase(request.getType())) {
                    AmazonSdAdTargeting amazonSdAdTargeting = amazonSdAdTargetingDao.getbyTargetId(param.getPuid(), param.getShopId(), param.getTargetId());
                    AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(param.getPuid(), param.getShopId(), amazonSdAdTargeting.getAdGroupId());
                    AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllDao.getByCampaignId(param.getPuid(), param.getShopId(), amazonSdAdGroup.getCampaignId());
                    if (Constants.SD_REPORT_CPC.equalsIgnoreCase(amazonAdCampaignAll.getCostType())) {
                        voExcludeFileds.addAll(Lists.newArrayList("vcpm", "viewableImpressions", "vrt", "vctr"));
                    }
                }
                if (request.getDateModel() != ReportDateModelPb.ReportDateModel.HOURLY) {
                    voExcludeFileds.addAll(Lists.newArrayList("acots", "asots", "vcpm", "viewableImpressions", "vrt", "vctr", "ordersNewToBrand",
                            "unitsOrderedNewToBrand", "salesNewToBrand", "advertisingUnitPrice", "advertisingProductUnitPrice", "advertisingOtherProductUnitPrice",
                            "ordersNewToBrandPercentage", "unitsOrderedNewToBrandPercentage", "salesNewToBrandPercentage",
                            "adCostPercentage", "adSalePercentage", "adOrderNumPercentage", "orderNumPercentage"));
                } else {
                    voExcludeFileds.addAll(Lists.newArrayList("adSelfSaleNum", "adOtherSaleNum"));
                }
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdKeywordTargetHourWithCompareExportVo> datas = new ArrayList<>(24);
                for (AdKeywordAndTargetHourVo vo : voList) {
                    AdKeywordTargetHourWithCompareExportVo exVo = new AdKeywordTargetHourWithCompareExportVo(currencyCode, vo);
                    datas.add(exVo);
                }
                //修改表头
                if (request.getDateModel() != ReportDateModelPb.ReportDateModel.HOURLY) {
                    Field filed = AdKeywordTargetHourWithCompareExportVo.class.getDeclaredField("label");
                    filed.setAccessible(true);
                    ExcelProperty annotation = filed.getAnnotation(ExcelProperty.class);
                    InvocationHandler invocationHandler = Proxy.getInvocationHandler(annotation);
                    Field memberValues = invocationHandler.getClass().getDeclaredField("memberValues");
                    memberValues.setAccessible(true);
                    Map<String, Object> map = (Map<String, Object>) memberValues.get(invocationHandler);
                    String[] valueArr = {"日期"};
                    map.put("value", valueArr);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdKeywordTargetHourWithCompareExportVo.class,
                        build.currencyNew(AdKeywordTargetHourWithCompareExportVo.class), voExcludeFileds);
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    @Override
    public void targetHourOfAdData(GetTargetHourReportOfAdRequestPb.GetTargetHourReportOfAdRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();

                TargetHourParam param = new TargetHourParam();
                param.setTargetId(request.getTargetId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                List<AdKeywordAndTargetHourVo> voList =
                        amazonAdTargetHourReportService.getDetailListOfAd(request.getPuid().getValue(), param);
                ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdKeywordTargetHourOfAdExportVo> datas = new ArrayList<>(24);
                for (AdKeywordAndTargetHourVo vo : voList) {
                    AdKeywordTargetHourOfAdExportVo exVo = new AdKeywordTargetHourOfAdExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdKeywordTargetHourOfAdExportVo.class,
                        build.currencyNew(AdKeywordTargetHourOfAdExportVo.class));
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    @Override
    public void targetHourOfPlacementData(GetTargetHourReportOfPlacementRequestPb.GetTargetHourReportOfPlacementRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();

                TargetHourParam param = new TargetHourParam();
                param.setTargetId(request.getTargetId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                List<AdKeywordAndTargetHourVo> voList =
                        amazonAdTargetHourReportService.getDetailListOfPlacement(request.getPuid().getValue(), param);
                ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdKeywordTargetTargetHourOfPlacementExportVo> datas = new ArrayList<>(24);
                for (AdKeywordAndTargetHourVo vo : voList) {
                    AdKeywordTargetTargetHourOfPlacementExportVo exVo = new AdKeywordTargetTargetHourOfPlacementExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdKeywordTargetTargetHourOfPlacementExportVo.class,
                        build.currencyNew(AdKeywordTargetTargetHourOfPlacementExportVo.class));
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    @Override
    public void adsData(AdsDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("ads导出 request {}", request);
        String downloadUrl;
        int count = 0;
        List<String> urls = Lists.newLinkedList();
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        AdAdsPageParam param = new AdAdsPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
        }

        if (request.hasFilterTargetType()) {
            param.setFilterTargetType(request.getFilterTargetType());
        }

        if (request.hasAdTagId()) {
            param.setAdTagId(request.getAdTagId().getValue());
        }

        //做参数校验
        String err = checkAdAdsPageParam(param);

        if (StringUtils.isNotBlank(err)) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg(err);
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
            return;
        } else {
            ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
            String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
            // 取店铺销售额
            BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getShopId(), param.getStartDate(), param.getEndDate());
            if (shopSalesByDate == null) {
                shopSalesByDate = BigDecimal.ZERO;
            }
            param.setShopSales(shopSalesByDate);

            List<AdAdsPageVo> voList = cpcSbAdsService.getSbAdsVoList(auth, request.getPuid().getValue(), param, null, true);

            if (CollectionUtils.isEmpty(voList)) {
                urlBuilder.setCode(Int32Value.of(Result.ERROR));
                urlBuilder.setMsg("excel.export.none");
                responseObserver.onNext(urlBuilder.build());
                responseObserver.onCompleted();
                return;
            }

            try {

                //页面渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                //集合分片
                List<List<AdAdsPageVo>> partition = Lists.partition(voList, Constants.FILE_MAX_SIZE);
                for (List<AdAdsPageVo> list1 : partition) {
                    List<AdsExportVo> adsExportVos = new LinkedList<>();
                    for (AdAdsPageVo adAdsPageVo : list1) {
                        AdsExportVo ktVo = new AdsExportVo();
                        ktVo.setImpressions(adAdsPageVo.getImpressions() == null ? 0 : adAdsPageVo.getImpressions());
                        ktVo.setClicks(adAdsPageVo.getClicks() == null ? 0 : adAdsPageVo.getClicks());
                        ktVo.setAdOrderNum(adAdsPageVo.getAdOrderNum() == null ? 0 : adAdsPageVo.getAdOrderNum());
                        // 广告组合
                        ktVo.setPortfolioName(adAdsPageVo.getPortfolioName());
                        //所属广告活动
                        ktVo.setAdvertisingActivities(adAdsPageVo.getCampaignName());
                        //所属广告组
                        ktVo.setAdvertisingGroup(adAdsPageVo.getAdGroupName());
                        //状态
                        ktVo.setState(AllAdStateEnum.getStateValue(adAdsPageVo.getState()));
                        ktVo.setServingStatusName(adAdsPageVo.getServingStatusName());
                        ktVo.setName(adAdsPageVo.getName());
                        //点击率
                        ktVo.setCtr(modifyFormatDefaultZero(adAdsPageVo.getCtr()));
                        //订单转化率
                        ktVo.setCvr(modifyFormatDefaultZero(adAdsPageVo.getCvr()));
                        //ACoS
                        ktVo.setAcos(modifyFormatDefaultZero(adAdsPageVo.getAcos()));
                        //ACoTS
                        ktVo.setAcots(modifyFormatDefaultZero(adAdsPageVo.getAcots()));
                        ktVo.setRoas(StringUtils.isBlank(adAdsPageVo.getRoas()) ? "0.00" : adAdsPageVo.getRoas());
                        //ASoTS
                        ktVo.setAsots(modifyFormatDefaultZero(adAdsPageVo.getAsots()));
                        //广告花费
                        ktVo.setAdCost(currencyCode + formatToNumber(adAdsPageVo.getAdCost()));
                        //平均点击费用(特殊处理)
                        ktVo.setAdCostPerClick(currencyCode + getAdCostPerClick(adAdsPageVo.getAdCostPerClick()));
                        //广告销售额
                        ktVo.setAdSale(currencyCode + getAdCostPerClick(adAdsPageVo.getAdSale()));

                        ktVo.setCpa(currencyCode + formatToNumber(adAdsPageVo.getCpa()));
                        ktVo.setAdSaleNum(adAdsPageVo.getAdSaleNum() == null ? 0 : adAdsPageVo.getAdSaleNum());
                        ktVo.setAdOtherOrderNum(adAdsPageVo.getAdOtherOrderNum() == null ? 0 : adAdsPageVo.getAdOtherOrderNum());
                        ktVo.setAdSales(currencyCode + formatToNumber(adAdsPageVo.getAdSales()));
                        ktVo.setAdOtherSales(currencyCode + formatToNumber(adAdsPageVo.getAdOtherSales()));
                        ktVo.setOrderNum(adAdsPageVo.getOrderNum() == null ? 0 : adAdsPageVo.getOrderNum());
                        ktVo.setAdSelfSaleNum(adAdsPageVo.getAdSelfSaleNum() == null ? 0 : adAdsPageVo.getAdSelfSaleNum());
                        ktVo.setAdOtherSaleNum(adAdsPageVo.getAdOtherSaleNum() == null ? 0 : adAdsPageVo.getAdOtherSaleNum());
                        ktVo.setOrdersNewToBrandFTD(adAdsPageVo.getOrdersNewToBrandFTD() == null ? 0 : adAdsPageVo.getOrdersNewToBrandFTD());
                        ktVo.setOrderRateNewToBrandFTD(modifyFormatDefaultZero(adAdsPageVo.getOrderRateNewToBrandFTD()));
                        ktVo.setSalesNewToBrandFTD(currencyCode + formatToNumber(StringUtils.isBlank(adAdsPageVo.getSalesNewToBrandFTD()) ? "0.00" : adAdsPageVo.getSalesNewToBrandFTD()));
                        ktVo.setSalesRateNewToBrandFTD(modifyFormatDefaultZero(adAdsPageVo.getSalesRateNewToBrandFTD()));
                        ktVo.setOrdersNewToBrandPercentageFTD(modifyFormatDefaultZero(adAdsPageVo.getOrdersNewToBrandPercentageFTD()));
                        adsExportVos.add(ktVo);
                    }
                    if (adsExportVos.size() > 0) {
                        downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), adsExportVos, request.getFileName() + "(" + count++ + ")", AdsExportVo.class, build);
                        urls.add(downloadUrl);
                    }
                }
                urlBuilder.addAllUrls(urls);
                urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
            } catch (Exception e) {
                log.error("sb ads 导出异常 err():{},puid{},shopId{}", e, request.getPuid(), request.getShopId());
                urlBuilder.setCode(Int32Value.of(Result.ERROR));
                urlBuilder.setMsg("process.msg.sync.fail");
            } finally {
                responseObserver.onNext(urlBuilder.build());
                responseObserver.onCompleted();
            }
        }
    }

    @Override
    public void productHourData(GetProductHourReportRequestPb.GetProductHourReportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()
                    || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 &&
                    (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr()))) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();

                ProductHourParam param = new ProductHourParam();
                param.setAdId(request.getAdId());
                param.setWeeks(request.getWeeks());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setEndDateCompare(request.getEndDateCompareStr());
                param.setStartDateCompare(request.getStartDateCompareStr());
                if (request.hasIsCompare()) {
                    param.setIsCompare(request.getIsCompare().getValue());
                }
                List<AdProductHourVo> voList = new ArrayList<>();

                if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY && "SP".equalsIgnoreCase(request.getAdType())) {
                    voList = amazonAdProductHourReportService.getHourList(request.getPuid().getValue(), param);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.DAILY) {
                    voList = amazonAdProductHourReportService.getDailyList(param.getPuid(), request.getAdType(), param, null);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.WEEKLY) {
                    voList = amazonAdProductHourReportService.getWeeklyList(param.getPuid(), request.getAdType(), param, null);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.MONTHLY) {
                    voList = amazonAdProductHourReportService.getMonthlyList(param.getPuid(), request.getAdType(), param, null);
                }

                ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
                List<String> voExcludeFileds = new ArrayList<>();
                if (!Integer.valueOf(1).equals(param.getIsCompare())) {
                    voExcludeFileds = Lists.newArrayList("costCompare", "costCompareRate",
                            "impressionsCompare", "impressionsCompareRate",
                            "clicksCompare", "clicksCompareRate",
                            "adOrderNumCompare", "adOrderNumCompareRate",
                            "adSalesCompare", "adSalesCompareRate",
                            "adSaleNumCompare", "adSaleNumCompareRate",
                            "cpcCompare", "cpcCompareRate",
                            "clickRateCompare", "clickRateCompareRate",
                            "salesConversionRateCompare", "salesConversionRateCompareRate",
                            "acosCompare", "acosCompareRate",
                            "roasCompare", "roasCompareRate"
                    );
                }
                //SB不需要销量字段
                if ("SB".equalsIgnoreCase(request.getAdType())) {
                    voExcludeFileds.addAll(Lists.newArrayList("adSaleNum", "adSelfSaleNum", "adOtherSaleNum"));
                }
                //SB不需要销量字段
                if ("SD".equalsIgnoreCase(request.getAdType())) {
                    voExcludeFileds.addAll(Lists.newArrayList("adSelfSaleNum", "adOtherSaleNum"));
                }
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdProductHourWithCompareExportVo> datas = new ArrayList<>(24);
                for (AdProductHourVo vo : voList) {
                    AdProductHourWithCompareExportVo exVo = new AdProductHourWithCompareExportVo(currencyCode, vo);
                    datas.add(exVo);
                }
                //修改表头
                if (request.getDateModel() != ReportDateModelPb.ReportDateModel.HOURLY) {
                    Field filed = AdProductHourWithCompareExportVo.class.getDeclaredField("label");
                    filed.setAccessible(true);
                    ExcelProperty annotation = filed.getAnnotation(ExcelProperty.class);
                    InvocationHandler invocationHandler = Proxy.getInvocationHandler(annotation);
                    Field memberValues = invocationHandler.getClass().getDeclaredField("memberValues");
                    memberValues.setAccessible(true);
                    Map<String, Object> map = (Map<String, Object>) memberValues.get(invocationHandler);
                    String[] valueArr = {"日期"};
                    map.put("value", valueArr);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdProductHourWithCompareExportVo.class,
                        build.currencyNew(AdProductHourWithCompareExportVo.class), voExcludeFileds);
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    @Override
    public void productWeekData(GetProductWeekReportRequestPb.GetProductWeekReportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()
                    || (request.hasIsCompare() && request.getIsCompare() == 1 && (!request.hasStartDateCompare() || !request.hasEndDateCompare()))) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();

                ProductHourParam param = new ProductHourParam();
                param.setAdId(request.getAdId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setIsCompare(request.getIsCompare());
                param.setEndDateCompare(request.getEndDateCompare());
                param.setStartDateCompare(request.getStartDateCompare());

                List<AdProductHourVo> voList = new ArrayList<>();
                List<AdProductWeekDayVo> productWeekDayVoList = amazonAdProductHourReportService.getWeeklySuperpositionList(request.getPuid().getValue(), param);
                if (CollectionUtils.isNotEmpty(productWeekDayVoList)) {
                    productWeekDayVoList.forEach(e -> {
                        voList.addAll(e.getDetails());
                    });
                }
                ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdProductWeekExportVo> datas = new ArrayList<>();
                for (AdProductHourVo vo : voList) {
                    AdProductWeekExportVo exVo = new AdProductWeekExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                List<String> voExcludeFileds = new ArrayList<>();
                if (!Integer.valueOf(1).equals(param.getIsCompare())) {
                    voExcludeFileds = Lists.newArrayList("costCompare", "costCompareRate",
                            "impressionsCompare", "impressionsCompareRate",
                            "clicksCompare", "clicksCompareRate",
                            "adOrderNumCompare", "adOrderNumCompareRate",
                            "adSalesCompare", "adSalesCompareRate",
                            "adSaleNumCompare", "adSaleNumCompareRate",
                            "cpcCompare", "cpcCompareRate",
                            "clickRateCompare", "clickRateCompareRate",
                            "salesConversionRateCompare", "salesConversionRateCompareRate",
                            "acosCompare", "acosCompareRate",
                            "roasCompare", "roasCompareRate"
                    );
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdProductWeekExportVo.class,
                        build.currencyNewWithParent(AdProductWeekExportVo.class), voExcludeFileds);
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    @Override
    public void productHourOfKeywordData(AdProductHourReportApiServiceOuterClass.GetProductHourReportOfKeywordRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();

                ProductHourParam param = new ProductHourParam();
                param.setAdId(request.getAdId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                List<AdProductHourVo> voList = new ArrayList<>();
                List<AdProductHourOfKeywordVo> adProductHourOfKeywordVos = amazonAdProductHourReportService.getListOfAd(request.getPuid().getValue(), param);
                if (CollectionUtils.isNotEmpty(adProductHourOfKeywordVos)) {
                    adProductHourOfKeywordVos.forEach(e -> {
                        dealExtraParameter(e.getDetails());
                        voList.addAll(e.getDetails());
                    });
                }
                ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdProductHourOfKeywordExportVo> datas = new ArrayList<>();
                for (AdProductHourVo vo : voList) {
                    AdProductHourOfKeywordExportVo exVo = new AdProductHourOfKeywordExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdProductHourOfKeywordExportVo.class,
                        build.currencyNew(AdProductHourOfKeywordExportVo.class));
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    /**
     * 处理额外参数，目前只需要对 keywordText 进行处理
     *
     * @param voList
     */
    private void dealExtraParameter(List<AdProductHourVo> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        for (AdProductHourVo vo : voList) {
            if (StringUtils.isNotBlank(vo.getKeywordText())) {
                switch (vo.getKeywordText()) {
                    case "close-match":
                        vo.setKeywordText("自动投放：紧密匹配");
                        break;
                    case "loose-match":
                        vo.setKeywordText("自动投放：宽泛匹配");
                        break;
                    case "substitutes":
                        vo.setKeywordText("自动投放：同类商品");
                        break;
                    case "complements":
                        vo.setKeywordText("自动投放：关联商品");
                        break;
                }
            }
        }
    }

    @Override
    public void productHourOfPlacementData(GetProductHourReportOfPlacementRequestPb.GetProductHourReportOfPlacementRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();

                ProductHourParam param = new ProductHourParam();
                param.setAdId(request.getAdId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                List<AdProductHourVo> voList = new ArrayList<>();
                List<AdProductHourOfPlacementVo> adProductHourOfPlacementVos = amazonAdProductHourReportService.getListOfPlacement(request.getPuid().getValue(), param);
                if (CollectionUtils.isNotEmpty(adProductHourOfPlacementVos)) {
                    adProductHourOfPlacementVos.forEach(e -> {
                        voList.addAll(e.getDetails());
                    });
                }
                ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdProductHourOfPlacementExportVo> datas = new ArrayList<>(24);
                for (AdProductHourVo vo : voList) {
                    AdProductHourOfPlacementExportVo exVo = new AdProductHourOfPlacementExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdProductHourOfPlacementExportVo.class,
                        build.currencyNew(AdProductHourOfPlacementExportVo.class));
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    private String checkAdAdsPageParam(AdAdsPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }
        if (StringUtils.isNotBlank(param.getOrderField()) && !Constants.isADperformanceOrderField(param.getOrderField())) {
            return "请求参数错误";
        }
        if (StringUtils.isNotBlank(param.getType())) {
            if (!Arrays.asList(Constants.SB).contains(param.getType())) {
                return "请求参数错误";
            }
        }

        return null;
    }

    private void filterSumMetricData(List<AdProductHourVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        adMetricDto.setSumCost(voList.stream().filter(item -> item != null && item.getAdCost() != null).map(item -> item.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdSale(voList.stream().filter(item -> item != null && item.getAdSale() != null).map(item -> item.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdProductHourVo::getAdOrderNum).sum()));
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdProductHourVo::getAdSaleNum).sum()));
    }

    // 填充指标占比数据
    private void filterMetricData(List<AdProductHourVo> voList, AdMetricDto adMetricDto) {
        for (AdProductHourVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                continue;
            }
            computeMetricData(adMetricDto, vo);
        }
    }

    private void computeMetricData(AdMetricDto adMetricDto, AdProductHourVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdCost().toString(), adMetricDto.getSumCost().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSale().toString(), adMetricDto.getSumAdSale().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getAdSaleNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSaleNum().toString(), adMetricDto.getSumOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }
    }


    @Override
    public void invoiceData(InvoicePageRequest request, StreamObserver<UrlResponse> responseObserver) {

        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        log.info("请求广告发票列表页参数：{}", request);
        if (request.getShopIdsCount() < 1 || !request.hasPuid() || StringUtils.isBlank(request.getStartDate()) || StringUtils.isBlank(request.getEndDate())) {
            urlBuilder.setMsg("请求参数错误");
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
            return;
        } else {
            InvoicePageParam param = new InvoicePageParam();
            param.setPageNo(request.getPageNum());
            param.setPageSize(500);
            param.setDateType(request.getDateType());
            if (StringUtils.isNotBlank(request.getStartDate())) {
                param.setStartDate(request.getStartDate().replace("-", ""));

            }
            if (StringUtils.isNotBlank(request.getEndDate())) {
                param.setEndDate(request.getEndDate().replace("-", ""));
            }
            param.setOrderField(request.getOrderField());
            param.setOrderType(request.getOrderType());
            param.setPuid(request.getPuid());
            param.setShopIds(request.getShopIdsList());
            param.setPaymentMethod(request.getPaymentMethodList());
            param.setMarketplaceIds(request.getMarketplaceIdsList());
            param.setStatus(request.getStatusList());
            param.setSearchValue(request.getSearchValue());
            param.setSearchType(request.getSearchType());

            int count = 0;
            int width = 0;
            String downloadUrl = "";
            int num = 500;//五百条数据一处理
            param.setPageNo(1);
            param.setPageSize(1);
            urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
            Page<AmazonAdInvoiceSummary> pageList = amazonAdInvoiceSummaryDao.getPageList(param.getPuid(), param, false);
            if (pageList == null || pageList.getTotalSize() == 0) {
                //没有符合条件的数据
                urlBuilder.setCode(Int32Value.of(Result.ERROR));
                urlBuilder.setMsg("excel.export.none");
                responseObserver.onNext(urlBuilder.build());
                responseObserver.onCompleted();
                return;
            }
            int pageSize = pageList.getTotalSize() % num == 0 ? pageList.getTotalSize() / num : pageList.getTotalSize() / num + 1;
            //封装自己的vo对象
            List<String> urls = Lists.newLinkedList();
            List<AmazonAdInvoiceSummaryExportVo> allExportVos = Lists.newArrayList();

            Map<Integer, ShopAuth> shopAuthMap = new HashMap<>();

            try {
                for (int i = 0; i < pageSize; ) {
                    if (allExportVos.size() < Constants.FILE_MAX_SIZE) {
                        //小于最大导出条数 就继续添加
                        //循环遍历进行execl导入
                        param.setPageNo(++i);
                        param.setPageSize(num);
                        pageList = amazonAdInvoiceSummaryDao.getPageList(param.getPuid(), param, false);

                        List<AmazonAdInvoiceSummary> rows = pageList.getRows();
                        if (CollectionUtils.isNotEmpty(rows)) {
                            List<AmazonAdInvoiceSummaryExportVo> collect = rows.stream().map(e -> {
                                ShopAuth shopAuth = shopAuthMap.get(e.getShopId());
                                if (shopAuth == null) {
                                    shopAuth = shopAuthDao.getScAndVcByIdAndPuid(e.getShopId(), request.getPuid());
                                    if (shopAuth != null) {
                                        shopAuthMap.put(e.getShopId(), shopAuth);
                                    }
                                }
                                return convertInvoice(e, shopAuth);
                            }).collect(Collectors.toList());
                            allExportVos.addAll(collect);
                        } else {
                            return;
                        }


                    } else {
                        //进行对数据的存入
                        WriteHandlerBuild build = new WriteHandlerBuild().rate();
                        downloadUrl = excelService.easyExcelHandlerExport(request.getPuid(), allExportVos,
                                request.getFileName() + "(" + count++ + ")", AmazonAdInvoiceSummaryExportVo.class,
                                build.currencyNew(AmazonAdInvoiceSummaryExportVo.class));
                        urls.add(downloadUrl);

                        allExportVos.clear();
                        urls.add(downloadUrl);
                    }
                }


                if (allExportVos.size() > 0) {

                    WriteHandlerBuild build = new WriteHandlerBuild().rate();
                    downloadUrl = excelService.easyExcelHandlerExport(request.getPuid(), allExportVos,
                            request.getFileName() + "(" + count++ + ")", AmazonAdInvoiceSummaryExportVo.class,
                            build.currencyNew(AmazonAdInvoiceSummaryExportVo.class));
                    urls.add(downloadUrl);
                }
                urlBuilder.addAllUrls(urls);
                urlBuilder.setCode(Int32Value.of(Result.SUCCESS));

            } catch (Exception e) {
                log.error("广告发票导出 导出异常 err():{},puid{},shopId{}", e, request.getPuid());
                urlBuilder.setCode(Int32Value.of(Result.ERROR));
                urlBuilder.setMsg("process.msg.sync.fail");
            } finally {
                responseObserver.onNext(urlBuilder.build());
                responseObserver.onCompleted();
            }

        }
    }


    private AmazonAdInvoiceSummaryExportVo convertInvoice(AmazonAdInvoiceSummary invoiceSummary, ShopAuth shopAuth) {
        AmazonAdInvoiceSummaryExportVo rowData = new AmazonAdInvoiceSummaryExportVo();


        if (StringUtils.isNotBlank(invoiceSummary.getInvoiceDate())) {
            rowData.setInvoiceDate(DateUtil.toFormatDate(invoiceSummary.getInvoiceDate()));
        }
        rowData.setInvoiceId(invoiceSummary.getInvoiceId());
        if (invoiceSummary.getPaymentMethod() != null) {
            InvoicePaymentMethodEnum invoicePaymentMethodEnum = InvoicePaymentMethodEnum.fromValue(invoiceSummary.getPaymentMethod());
            rowData.setPaymentMethod(invoicePaymentMethodEnum == null ? invoiceSummary.getPaymentMethod() : invoicePaymentMethodEnum.getDesc());
        }
        if (invoiceSummary.getMarketplaceId() != null) {
            AmznEndpoint byMarketplaceId = AmznEndpoint.getByMarketplaceId(invoiceSummary.getMarketplaceId());
            if (byMarketplaceId != null) {
                rowData.setMarketplaceName(byMarketplaceId.getMarketplaceCN());
            }
        }

        rowData.setInvoiceAmount(invoiceSummary.getCurrencyCode() + (invoiceSummary.getInvoiceAmount() == null ? "0.00" : invoiceSummary.getInvoiceAmount().setScale(2, RoundingMode.HALF_UP).toString()));
        if (shopAuth != null && shopAuth.getName() != null) {
            rowData.setShopName(shopAuth.getName());
        }
        if (invoiceSummary.getStatus() != null) {
            InvoiceStatusEnum invoiceStatusEnum = InvoiceStatusEnum.fromValue(invoiceSummary.getStatus());
            rowData.setStatus(invoiceStatusEnum == null ? invoiceSummary.getStatus() : invoiceStatusEnum.getDesc());
        }
        rowData.setInvoiceCycleDate(DateUtil.toFormatDate(invoiceSummary.getFromDate()) + "至" + DateUtil.toFormatDate(invoiceSummary.getToDate()));

        return rowData;
    }

    /**
     * 填充 品牌细节 默认信息
     */
    private void fillDefaultBrandMessage(TargetingDataResponse.TargetingPageVo.Builder voBuilder, TargetingPageVo item, boolean fillDefault) {
        if (TargetTypeEnum.category.name().equalsIgnoreCase(item.getType())) {
            String brandName = StringUtils.isNotBlank(item.getBrandName()) ? item.getBrandName() : fillDefault ? BrandMessageConstants.DEFAULT_BRAND_NAME : null;
            String commodityPriceRange = StringUtils.isNotBlank(item.getCommodityPriceRange()) ? item.getCommodityPriceRange() : fillDefault ? BrandMessageConstants.DEFAULT_COMMODITY_PRICE_RANGE : null;
            String rating = StringUtils.isNotBlank(item.getRating()) ? item.getRating() : fillDefault ? BrandMessageConstants.DEFAULT_RATING : null;
            String distribution = StringUtils.isNotBlank(item.getDistribution()) ? item.getDistribution() : fillDefault ? BrandMessageConstants.DEFAULT_DISTRIBUTION : null;

            if (StringUtils.isNotBlank(brandName)) {
                voBuilder.setBrandName(brandName);
            }
            if (StringUtils.isNotBlank(commodityPriceRange)) {
                voBuilder.setCommodityPriceRange(commodityPriceRange);
            }
            if (StringUtils.isNotBlank(rating)) {
                voBuilder.setRating(rating);
            }
            if (StringUtils.isNotBlank(distribution)) {
                voBuilder.setDistribution(distribution);
            }
        }
        if (StringUtils.isNotBlank(item.getLookback())) {
            voBuilder.setLookback(item.getLookback());
        }
    }

    @Override
    public void searchTermsDetailsData(SearchTermsDetailRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            log.info("ad-query-搜索词(投放)详情 request: {}", request);
            builder.setCode(Int32Value.of(Result.SUCCESS));

            //做参数校验

            CpcQueryWordDetailDto dto = new CpcQueryWordDetailDto();
            dto.setPuid(request.getPuid().getValue());
            dto.setUid(request.getUid().getValue());
            dto.setLoginIp(request.getLoginIp());
            dto.setShopId(request.getShopId().getValue());
            dto.setMarketplaceId(request.getMarketplaceId());
            dto.setKeywordId(request.getKeywordId());
            dto.setTargetId(request.getTargetId());
            dto.setQuery(request.getQuery());
            dto.setStart(request.getStart());
            dto.setEnd(request.getEnd());
            dto.setDateType(request.getDateType());
            dto.setOrderField(request.getOrderField());
            dto.setOrderValue(request.getOrderValue());
            dto.setIsTarget(request.getIsTargetType());
            dto.setType(request.getType());
            dto.setIsCompare(request.getIsCompare());
            dto.setStartDateCompare(request.getStartDateCompare());
            dto.setEndDateCompare(request.getEndDateCompare());

            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || checkQueryWordDetailDto(dto) || StringUtils.isBlank(dto.getKeywordId())
                    || (request.hasIsCompare() && request.getIsCompare() == 1 && (!request.hasStartDateCompare() || !request.hasEndDateCompare()))) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();

                List<AdQueryKeywordAndTargetVo> voList = new ArrayList<>();
                if (KeywordViewHourParam.DateModel.DAILY.name().equalsIgnoreCase(dto.getDateType())) {
                    voList = cpcQueryReportDetailService.getDailyList(dto.getPuid(), dto.getType(), dto, null);
                } else if (KeywordViewHourParam.DateModel.WEEKLY.name().equalsIgnoreCase(dto.getDateType())) {
                    voList = cpcQueryReportDetailService.getWeeklyList(dto.getPuid(), dto.getType(), dto, null);
                } else if (KeywordViewHourParam.DateModel.MONTHLY.name().equalsIgnoreCase(dto.getDateType())) {
                    voList = cpcQueryReportDetailService.getMonthlyList(dto.getPuid(), dto.getType(), dto, null);
                }

                if (voList == null) {
                    voList = new ArrayList<>();
                }

                if (CollectionUtils.isNotEmpty(voList)) {
                    boolean isSorted = StringUtils.isNotBlank(dto.getOrderField()) &&
                            Constants.isADOrderField(dto.getOrderField(), AdQueryKeywordAndTargetVo.class);
                    if (isSorted) {
                        PageUtil.sortedByOrderField(voList, dto.getOrderField(), dto.getOrderValue());
                    }
                }

                ShopAuth auth = shopAuthDao.getScAndVcById(dto.getShopId());
                List<String> voExcludeFields = Lists.newArrayList("acots", "asots", "vcpm", "viewableImpressions", "vrt", "vctr", "ordersNewToBrand",
                        "unitsOrderedNewToBrand", "salesNewToBrand", "advertisingUnitPrice", "advertisingProductUnitPrice", "advertisingOtherProductUnitPrice",
                        "ordersNewToBrandPercentage", "unitsOrderedNewToBrandPercentage", "salesNewToBrandPercentage",
                        "adCostPercentage", "adSalePercentage", "adOrderNumPercentage", "orderNumPercentage", "selfAdOrderNum",
                        "otherAdOrderNum", "adSelfSales", "adOtherSales", "adSelfSaleNum", "adOtherSales", "adOtherSaleNum");

                if (Constants.SB.equalsIgnoreCase(dto.getType())) {
                    // sb 没有广告销量字段
                    voExcludeFields.addAll(Lists.newArrayList("adSaleNum", "adSaleNumCompare", "adSaleNumCompareRate"));
                }

                if (dto.getIsCompare() != 1) {
                    voExcludeFields.addAll(Lists.newArrayList("costCompare", "costCompareRate",
                            "impressionsCompare", "impressionsCompareRate",
                            "clicksCompare", "clicksCompareRate",
                            "adOrderNumCompare", "adOrderNumCompareRate",
                            "adSalesCompare", "adSalesCompareRate",
                            "adSaleNumCompare", "adSaleNumCompareRate",
                            "cpaCompare", "cpaCompareRate",
                            "cpcCompare", "cpcCompareRate",
                            "clickRateCompare", "clickRateCompareRate",
                            "salesConversionRateCompare", "salesConversionRateCompareRate",
                            "acosCompare", "acosCompareRate",
                            "roasCompare", "roasCompareRate"));
                }

                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdQueryKeywordTargetWithCompareExportVo> datas = new ArrayList<>(24);
                for (AdQueryKeywordAndTargetVo vo : voList) {
                    AdQueryKeywordTargetWithCompareExportVo exVo = new AdQueryKeywordTargetWithCompareExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                Field filed = AdQueryKeywordTargetWithCompareExportVo.class.getDeclaredField("label");
                filed.setAccessible(true);
                ExcelProperty annotation = filed.getAnnotation(ExcelProperty.class);
                InvocationHandler invocationHandler = Proxy.getInvocationHandler(annotation);
                Field memberValues = invocationHandler.getClass().getDeclaredField("memberValues");
                memberValues.setAccessible(true);
                Map<String, Object> map = (Map<String, Object>) memberValues.get(invocationHandler);
                String[] valueArr = {"日期"};
                map.put("value", valueArr);
                String fileName = auth.getName() + "_用户搜索词详情数据" + "_" + dto.getStart() + "_" + dto.getEnd();

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        fileName + "(" + count++ + ")", AdQueryKeywordTargetWithCompareExportVo.class,
                        build.currencyNew(AdQueryKeywordTargetWithCompareExportVo.class), voExcludeFields);
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    private boolean checkQueryWordDetailDto(CpcQueryWordDetailDto dto) {
        //做参数校验
        if (!CheckParamUtil.checkRequired(dto, false, "shopId,marketplaceId,dateType,start,end,query")) {
            return true;
        }
        dto.setStart(DateUtil.dateToStrWithFormat(DateUtil.strToDate(dto.getStart(), "yyyy-MM-dd"), "yyyyMMdd"));
        dto.setEnd(DateUtil.dateToStrWithFormat(DateUtil.strToDate(dto.getEnd(), "yyyy-MM-dd"), "yyyyMMdd"));
        if (StringUtils.isNotBlank(dto.getStartDateCompare())) {
            dto.setStartDateCompare(DateUtil.dateToStrWithFormat(DateUtil.strToDate(dto.getStartDateCompare(), "yyyy-MM-dd"), "yyyyMMdd"));
        }
        if (StringUtils.isNotBlank(dto.getEndDateCompare())) {
            dto.setEndDateCompare(DateUtil.dateToStrWithFormat(DateUtil.strToDate(dto.getEndDateCompare(), "yyyy-MM-dd"), "yyyyMMdd"));
        }
        return false;
    }


    @Override
    public void exportGrabRankingSnapshot(GrabRankingSnapshotRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasRecordId() || !request.hasUuid()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                advertiseAutoRuleExecuteRecordService.grabRankingSnapshotExport(request.getPuid(), request.getShopId(), request.getRecordId(), request.getIsDownloadImage(), request.getUuid());
                //builder.addUrls(url);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }


}

