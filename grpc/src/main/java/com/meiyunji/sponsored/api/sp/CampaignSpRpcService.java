package com.meiyunji.sponsored.api.sp;

import com.alibaba.fastjson.JSON;
import com.amazon.advertising.budget.entity.BudgetRecommendationsResponse;
import com.amazon.advertising.budget.entity.BudgetRecommendationsSuccessResult;
import com.amazon.advertising.budget.entity.BudgetRuleRecommendation;
import com.amazon.advertising.budget.entity.SevenDaysMissedOpportunities;
import com.amazon.advertising.mode.PredicateEnum;
import com.amazon.advertising.mode.StrategyEnum;
import com.amazon.advertising.mode.TargetingTypeEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.BoolValue;
import com.google.protobuf.DoubleValue;
import com.google.protobuf.Int32Value;
import com.google.protobuf.Int64Value;
import com.meiyunji.sponsored.common.base.Constants;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.adCommon.CommonShowAdPerformanceRequest;
import com.meiyunji.sponsored.rpc.adCommon.CommonShowAdPerformanceResponse;
import com.meiyunji.sponsored.rpc.sp.campaign.NeKeywordsVo;
import com.meiyunji.sponsored.rpc.sp.campaign.*;
import com.meiyunji.sponsored.rpc.vo.AdPerformanceRpcVo;
import com.meiyunji.sponsored.rpc.vo.CommonResponse;
import com.meiyunji.sponsored.rpc.vo.CpcCommPageRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dto.sp.CreateSpTargetDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaign;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdSyncService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcSpCampaignService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.SpCampaignService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.SpCopyAdsService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.SpGroupService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.SpTargetService;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.missBudget.entity.AmazonAdMissBudget;
import com.meiyunji.sponsored.service.missBudget.service.AmazonAdMissBudgetService;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTopBudgetTemplateDao;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTopBudgetTemplate;
import com.meiyunji.sponsored.service.util.Constant;
import com.meiyunji.sponsored.service.util.WxNotificationUtil;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2021/10/21 10:14
 * @describe: sp 广告活动rpc接口
 */
@GRpcService
@Slf4j
public class CampaignSpRpcService extends RPCSpCampaignServiceGrpc.RPCSpCampaignServiceImplBase {

    public static final String CREATE_SP_ADS_WX_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=552c63a0-074f-4a8c-ae4e-09c9cd38eeb7";

    @Autowired
    private ICpcSpCampaignService cpcSpCampaignService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private AdvertiseStrategyTopBudgetTemplateDao advertiseStrategyTopBudgetTemplateDao;
    @Autowired
    private AmazonAdMissBudgetService amazonAdMissBudgetService;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private SpCampaignService spCampaignService;
    @Autowired
    private SpGroupService spGroupService;
    @Autowired
    private SpTargetService spTargetService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private ICpcAdSyncService cpcAdSyncService;
    @Autowired
    private SpCopyAdsService copyAdsService;

    @Override
    public void showAdPerformanceVo(CommonShowAdPerformanceRequest request, StreamObserver<CommonShowAdPerformanceResponse> responseObserver) {
        CommonShowAdPerformanceResponse.Builder builder = CommonShowAdPerformanceResponse.newBuilder();
        //做参数校验
        if (!request.hasShopId()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");

        } else {
            AdPerformanceParam param = new AdPerformanceParam();
            param.setShopId(request.getShopId());
            param.setPuid(request.getPuid());
            param.setCampaignId(request.getCampaignId());
            param.setGroupId(request.getGroupId());
            param.setKeywordId(request.getKeywordId());
            param.setTargetId(request.getTargetId());
            param.setCpcProductId(request.getCpcProductId());
            param.setQuery(request.getQuery());
            param.setPlacement(request.getPlacement());
            param.setStartDate(request.getStartDate());
            param.setEndDate(request.getEndDate());

            if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
                param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
                param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            } else {
                param.setStartDate(param.getStartDate().replace("-", ""));
                param.setEndDate(param.getEndDate().replace("-", ""));
            }
            //处理业务返回结果
            Result<AdPerformanceVo> res = cpcSpCampaignService.showCampaignPerformance(request.getPuid(), param);
            builder.setCode(res.getCode());
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                //处理data
                AdPerformanceVo data = res.getData();
                if (data != null) {
                    AdPerformanceRpcVo.Builder voBuilder = AdPerformanceRpcVo.newBuilder();
                    if (data.getShopId() != null) {
                        voBuilder.setShopId(data.getShopId());
                    }
                    if (data.getCampaignId() != null) {
                        voBuilder.setCampaignId(data.getCampaignId());
                    }
                    if (data.getGroupId() != null) {
                        voBuilder.setGroupId(data.getGroupId());
                    }
                    if (data.getKeywordId() != null) {
                        voBuilder.setKeywordId(data.getKeywordId());
                    }
                    if (data.getTargetId() != null) {
                        voBuilder.setTargetId(data.getTargetId());
                    }
                    if (data.getAdId() != null) {
                        voBuilder.setAdId(data.getAdId());
                    }
                    if (data.getQuery() != null) {
                        voBuilder.setQuery(data.getQuery());
                    }
                    if (data.getPlacement() != null) {
                        voBuilder.setPlacement(data.getPlacement());
                    }
                    Map<String, CpcCommPageRpcVo> rpcVoMap = Maps.newHashMap();

                    if (MapUtils.isNotEmpty(data.getMap())) {
                        for (Map.Entry<String, CpcCommPageVo> entry : data.getMap().entrySet()) {
                            CpcCommPageVo vo = entry.getValue();
                            //vo转message
                            CpcCommPageRpcVo.Builder cpcRpcVo = CpcCommPageRpcVo.newBuilder();
                            cpcRpcVo.setImpressions(Optional.ofNullable(vo.getImpressions()).orElse(0));
                            cpcRpcVo.setClicks(Optional.ofNullable(vo.getClicks()).orElse(0));
                            cpcRpcVo.setCtr(StringUtils.isNotBlank(vo.getCtr()) ? vo.getCtr() : "0");
                            cpcRpcVo.setCvr(StringUtils.isNotBlank(vo.getCvr()) ? vo.getCvr() : "0");
                            cpcRpcVo.setAcos(StringUtils.isNotBlank(vo.getAcos()) ? vo.getAcos() : "0");
                            cpcRpcVo.setRoas(StringUtils.isNotBlank(vo.getRoas()) ? vo.getRoas() : "0");
                            cpcRpcVo.setAcots(StringUtils.isNotBlank(vo.getAcots()) ? vo.getAcots() : "0");
                            cpcRpcVo.setAsots(StringUtils.isNotBlank(vo.getAsots()) ? vo.getAsots() : "0");
                            cpcRpcVo.setAdOrderNum(Optional.ofNullable(vo.getAdOrderNum()).orElse(0));
                            cpcRpcVo.setAdCost(StringUtils.isNotBlank(vo.getAdCost()) ? vo.getAdCost() : "0");
                            cpcRpcVo.setAdCostPerClick(StringUtils.isNotBlank(vo.getAdCostPerClick()) ? vo.getAdCostPerClick() : "0");
                            cpcRpcVo.setAdSale(StringUtils.isNotBlank(vo.getAdSale()) ? vo.getAdSale() : "0");

                            rpcVoMap.put(entry.getKey(), cpcRpcVo.build());
                        }

                        voBuilder.putAllMap(rpcVoMap);
                    }
                    builder.setData(voBuilder.build());
                }
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告位表现
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void showAdPlacementPerformance(CommonShowAdPerformanceRequest request, StreamObserver<CommonShowAdPerformanceResponse> responseObserver) {
        CommonShowAdPerformanceResponse.Builder builder = CommonShowAdPerformanceResponse.newBuilder();
        //做参数校验
        if (!request.hasShopId()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");

        } else {
            AdPerformanceParam param = new AdPerformanceParam();
            param.setShopId(request.getShopId());
            param.setPuid(request.getPuid());
            param.setCampaignId(request.getCampaignId());
            param.setGroupId(request.getGroupId());
            param.setKeywordId(request.getKeywordId());
            param.setTargetId(request.getTargetId());
            param.setCpcProductId(request.getCpcProductId());
            param.setQuery(request.getQuery());
            param.setPlacement(request.getPlacement());
            param.setStartDate(request.getStartDate());
            param.setEndDate(request.getEndDate());

            if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
                param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
                param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            } else {
                param.setStartDate(param.getStartDate().replace("-", ""));
                param.setEndDate(param.getEndDate().replace("-", ""));
            }
            //处理业务返回结果
            Result<AdPerformanceVo> res = cpcSpCampaignService.showPlacementPerformance(request.getPuid(), param);
            builder.setCode(res.getCode());
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                //处理data
                AdPerformanceVo data = res.getData();
                if (data != null) {
                    AdPerformanceRpcVo.Builder voBuilder = AdPerformanceRpcVo.newBuilder();
                    if (data.getShopId() != null) {
                        voBuilder.setShopId(data.getShopId());
                    }
                    if (data.getCampaignId() != null) {
                        voBuilder.setCampaignId(data.getCampaignId());
                    }
                    if (data.getGroupId() != null) {
                        voBuilder.setGroupId(data.getGroupId());
                    }
                    if (data.getKeywordId() != null) {
                        voBuilder.setKeywordId(data.getKeywordId());
                    }
                    if (data.getTargetId() != null) {
                        voBuilder.setTargetId(data.getTargetId());
                    }
                    if (data.getAdId() != null) {
                        voBuilder.setAdId(data.getAdId());
                    }
                    if (data.getQuery() != null) {
                        voBuilder.setQuery(data.getQuery());
                    }
                    if (data.getPlacement() != null) {
                        voBuilder.setPlacement(data.getPlacement());
                    }
                    Map<String, CpcCommPageRpcVo> rpcVoMap = Maps.newHashMap();

                    if (MapUtils.isNotEmpty(data.getMap())) {
                        for (Map.Entry<String, CpcCommPageVo> entry : data.getMap().entrySet()) {
                            CpcCommPageVo vo = entry.getValue();
                            //vo转message
                            CpcCommPageRpcVo.Builder cpcRpcVo = CpcCommPageRpcVo.newBuilder();
                            cpcRpcVo.setImpressions(Optional.ofNullable(vo.getImpressions()).orElse(0));
                            cpcRpcVo.setClicks(Optional.ofNullable(vo.getClicks()).orElse(0));
                            cpcRpcVo.setCtr(StringUtils.isNotBlank(vo.getCtr()) ? vo.getCtr() : "0");
                            cpcRpcVo.setCvr(StringUtils.isNotBlank(vo.getCvr()) ? vo.getCvr() : "0");
                            cpcRpcVo.setAcos(StringUtils.isNotBlank(vo.getAcos()) ? vo.getAcos() : "0");
                            cpcRpcVo.setRoas(StringUtils.isNotBlank(vo.getRoas()) ? vo.getRoas() : "0");
                            cpcRpcVo.setAcots(StringUtils.isNotBlank(vo.getAcots()) ? vo.getAcots() : "0");
                            cpcRpcVo.setAdOrderNum(Optional.ofNullable(vo.getAdOrderNum()).orElse(0));
                            cpcRpcVo.setAdCost(StringUtils.isNotBlank(vo.getAdCost()) ? vo.getAdCost() : "0");
                            cpcRpcVo.setAdCostPerClick(StringUtils.isNotBlank(vo.getAdCostPerClick()) ? vo.getAdCostPerClick() : "0");
                            cpcRpcVo.setAdSale(StringUtils.isNotBlank(vo.getAdSale()) ? vo.getAdSale() : "0");

                            rpcVoMap.put(entry.getKey(), cpcRpcVo.build());
                        }

                        voBuilder.putAllMap(rpcVoMap);
                    }
                    builder.setData(voBuilder.build());
                }
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 创建活动
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void createCampaign(CreateSpCampaignRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-创建活动 request {}", request);
        CampaignVo.CampaignVoBuilder campaignVoBuilder = CampaignVo.builder();
        if (request.hasPuid()) {
            campaignVoBuilder.puid(request.getPuid().getValue());
        }
        if (request.hasShopId()) {
            campaignVoBuilder.shopId(request.getShopId().getValue());
        }
        if (request.hasDailyBudget()) {
            campaignVoBuilder.dailyBudget(request.getDailyBudget().getValue());
        }
        if (request.hasDailyBudget()) {
            campaignVoBuilder.dailyBudget(request.getDailyBudget().getValue());
        }
        if (request.hasPlacementTop()) {
            campaignVoBuilder.placementTop(request.getPlacementTop().getValue());
        }
        if (request.hasPlacementProductPage()) {
            campaignVoBuilder.placementProductPage(request.getPlacementProductPage().getValue());
        }
        if (request.hasPlacementRestOfSearch()) {
            campaignVoBuilder.placementRestOfSearch(request.getPlacementRestOfSearch().getValue());
        }
        campaignVoBuilder.endDateStr(request.hasEndDateStr() ? request.getEndDateStr() : null);
        if (request.hasPortfolioId() && StringUtils.isNotBlank(request.getPortfolioId())) {
            if (Constant.NON_PORTFOLIO_ID.equals(request.getPortfolioId())) {
                campaignVoBuilder.portfolioId("");
            } else {
                campaignVoBuilder.portfolioId(request.getPortfolioId());
            }
        }
        campaignVoBuilder.startDateStr(request.getStartDateStr());
        campaignVoBuilder.loginIp(request.getLoginIp());
        campaignVoBuilder.name(request.getName());
        campaignVoBuilder.uid(request.getUid().getValue());
        campaignVoBuilder.strategy(request.getStrategy());
        campaignVoBuilder.targetingType(request.getTargetingType());
        CampaignVo vo = campaignVoBuilder.build();
        //做参数校验
        String err = checkCreateCampaignParams(vo, "create");

        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasUid()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else if (StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(err);
        } else {
            //处理业务返回结果
            Result<String> result = cpcSpCampaignService.createCampaign(vo);

            /**
             * TODO 广告活动增加日志
             * 操作类型：新增广告活动
             * 逻辑：调用新增广告日志方法，传空对象作为旧值
             * start
             */
            //获取配置信息
            AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(vo.getPuid(), vo.getShopId());
            AmazonAdCampaignAll amazonAdCampaign = cpcSpCampaignService.convertVoToCreatePo(vo, amazonAdProfile);
            List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
            AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(null, amazonAdCampaign);
            adManageOperationLog.setIp(request.getLoginIp());
            if (OperationLogResultEnum.SUCCESS.getResultValue().equals(result.getCode())) {
                adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                adManageOperationLog.setCampaignId(result.getData() == null ? null : result.getData());
            } else {
                adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                adManageOperationLog.setResultInfo(result.getMsg());
            }
            adManageOperationLogs.add(adManageOperationLog);
            adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
            //end

            builder.setCode(Int32Value.newBuilder().setValue(result.getCode()).build());
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
            builder.setData(String.valueOf(result.getData()));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void createSpCampaign(CreateSpCampaignReq request, StreamObserver<CreateSpCampaignResp> responseObserver) {
        log.info("create sp campaign, request {}", JSON.toJSONString(request.getAllFields()));
        CreateSpCampaignResp.Builder builder = CreateSpCampaignResp.newBuilder();
        // 参数校验
        if (!request.hasPuid() || !request.hasShopId() || !request.hasSpCampaignInfo() || !request.hasSpGroupInfo() || !request.hasSpTargetInfo()) {
            sendErrResp(builder, "请求参数错误", responseObserver);
            return;
        }
        // 校验配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(request.getPuid(), request.getShopId());
        if (amazonAdProfile == null) {
            sendErrResp(builder, "没有站点对应的配置信息", responseObserver);
            return;
        }
        // 校验店铺
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId(), request.getPuid());
        if (shop == null) {
            sendErrResp(builder, "店铺不存在", responseObserver);
            return;
        }

        //到这一步说明请求就已经成功
        builder.setCode(Result.SUCCESS);

        CreateSpCampaignInfo.Builder createSpCampaignInfoBuilder = CreateSpCampaignInfo.newBuilder();
        // 创建广告活动
        String campaignId = createCampaign(request, createSpCampaignInfoBuilder, shop, amazonAdProfile);
        if (StringUtils.isBlank(campaignId)) {
            builder.setData(createSpCampaignInfoBuilder.build());
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            printResultToWx(request, builder, true, false, null, null);
            return;
        }
        // 创建广告组
        AmazonAdGroup group = createGroup(request, createSpCampaignInfoBuilder, shop, campaignId, amazonAdProfile);
        if (group == null) {
            builder.setData(createSpCampaignInfoBuilder.build());
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            printResultToWx(request, builder, false, true, campaignId, null);
            return;
        }
        //1,构建请求参数
        CreateSpTargetDto spTargetDto = buildCreateSpTargetDto(request, createSpCampaignInfoBuilder, group);
        if (spTargetDto == null) {
            builder.setData(createSpCampaignInfoBuilder.build());
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        // 多线程创建投放和否投
        createTargetAndNeTarget(spTargetDto, createSpCampaignInfoBuilder, shop, amazonAdProfile, group);

        //整个广告创建完成之后再同步一下活动状态
        try {
            cpcAdSyncService.syncSpCampaignState(shop, campaignId);
        } catch (Exception e) {
            log.error("createCampaign, sync sp campaign state error:", e);
        }
        builder.setData(createSpCampaignInfoBuilder.build());
        //异步打印企业微信监控
        printResultToWx(request, builder, false, false, campaignId, group.getAdGroupId());

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 复制广告
     */
    @Override
    public void copySpAds(CopySpAdsReq request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        log.info("copy sp campaign, request {}", request);
        // 校验配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(request.getPuid(), request.getShopId());
        if (amazonAdProfile == null) {
            throw new SponsoredBizException("没有源站点对应的配置信息！");
        }
        AmazonAdProfile targetAmazonAdProfile = amazonAdProfileDao.getProfile(request.getPuid(), request.getTargetShopId());
        if (targetAmazonAdProfile == null) {
            throw new SponsoredBizException("没有目标站点对应的配置信息！");
        }
        // 校验源店铺
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId(), request.getPuid());
        if (shop == null) {
            throw new SponsoredBizException("源店铺不存在！");
        }
        // 校验目标店铺
        ShopAuth targetShop = shopAuthDao.getScAndVcByIdAndPuid(request.getTargetShopId(), request.getPuid());
        if (targetShop == null) {
            throw new SponsoredBizException("目标店铺不存在！");
        }
        // 业务参数校验:1,校验广告活动名称和广告组名称不重复。2,校验广告活动id。3，校验广告组数量和单个广告组下广告产品数量。
        copyAdsService.checkParam(request);
        // 同步创建广告活动
        String campaignId = copyAdsService.createCopyCampaign(request, targetShop, targetAmazonAdProfile);
        // 异步复制广告组及以下层级，广告组的默认竞价和投放的默认竞价如果超出范围，则自动更新为范围
        copyAdsService.copySpAds(request, request.getCampaignId(), campaignId, targetAmazonAdProfile, shop, targetShop);
        builder.setCode(Int32Value.of(Result.SUCCESS));
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private void printResultToWx(CreateSpCampaignReq request, CreateSpCampaignResp.Builder builder, boolean campaignCreatedFail, boolean groupCreatedFail, String campaignId, String groupId) {

        CompletableFuture.runAsync(() -> {
            CampaignVo campaignVo = JSON.parseObject(request.getSpCampaignInfo(), CampaignVo.class);
            SPadGroupVo groupVo = JSON.parseObject(request.getSpGroupInfo(), SPadGroupVo.class);
            //广告活动或者广告组创建失败告警通知
            CreateSpCampaignInfo data = builder.getData();
            if (campaignCreatedFail || groupCreatedFail) {
                if (campaignCreatedFail) {
                    String errMsg = data.getCampaignResp().getCampaignErrMsg();
                    String msg = String.format("新版创建广告：创建广告活动失败，活动名称：%s，失败原因：%s，puid：%d，shopId：%d", campaignVo.getName(), errMsg, request.getPuid(), request.getShopId());
                    WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, msg);
                }
                if (groupCreatedFail) {
                    String errMsg = data.getGroupResp().getGroupErrMsg();
                    String msg = String.format("新版创建广告：创建广告组失败，广告组名称：%s，失败原因：%s，puid：%d，shopId：%d，活动id：%s", groupVo.getName(), errMsg, request.getPuid(), request.getShopId(), campaignId);
                    WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, msg);
                }
                return;
            }

            //投放失败告警
            TargetResp targetResp = data.getTargetResp();
            if (targetResp.getCode() == Result.ERROR) {
                String msg = String.format("新版创建广告：创建投放失败，失败原因：%s，puid：%d，shopId：%d，活动id：%s，广告组id：%s", targetResp.getTargetErrMsg(), request.getPuid(), request.getShopId(), campaignId, groupId);
                WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, msg);
            } else {
                List<TargetInfoResp> targetInfoRespList = targetResp.getTargetListList();
                String targetType;
                if (Constants.AUTO_TARGET.equalsIgnoreCase(targetResp.getTargetType())) {
                    targetType = "自动投放";
                } else if (Constants.KEYWORD_TARGET.equalsIgnoreCase(targetResp.getTargetType())) {
                    targetType = "关键词投放";
                } else if (Constants.PRODUCT_TARGET.equalsIgnoreCase(targetResp.getTargetType())) {
                    targetType = "商品投放";
                } else {
                    targetType = "投放";
                }

                if (CollectionUtils.isNotEmpty(targetInfoRespList)) {
                    boolean print = false;
                    StringBuilder stringBuilder = new StringBuilder();
                    String msg = String.format("新版创建广告：创建%s失败，puid：%d，shopId：%d，活动id：%s，广告组id：%s \n index------失败原因", targetType, request.getPuid(), request.getShopId(), campaignId, groupId);
                    stringBuilder.append(msg);
                    for (int i = 0; i < targetInfoRespList.size(); i++) {
                        TargetInfoResp targetInfoResp = targetInfoRespList.get(i);
                        if (targetInfoResp != null && targetInfoResp.getCode() != ResultUtil.SUCCESS) {
                            print = true;
                            stringBuilder.append("\n").append(targetInfoResp.getIndex()).append("------").append(targetInfoResp.getErrMsg());
                        }
                    }
                    if (print) {
                        WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, stringBuilder.toString());
                    }
                }
            }
            //广告组层级否定投放关键词失败告警
            NeKeywordResp neKeywordResp = data.getNeKeywordResp();
            if (neKeywordResp.getCode() == Result.ERROR) {
                String msg = String.format("新版创建广告：创建广告组层级否定关键词失败，失败原因：%s，puid：%d，shopId：%d，活动id：%s，广告组id：%s", neKeywordResp.getNeKeywordErrMsg(), request.getPuid(), request.getShopId(), campaignId, groupId);
                WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, msg);
            } else {
                List<NeKeywordInfoResp> neKeywordInfoRespList = neKeywordResp.getNeKeywordsList();
                if (CollectionUtils.isNotEmpty(neKeywordInfoRespList)) {
                    boolean print = false;
                    StringBuilder stringBuilder = new StringBuilder();
                    String msg = String.format("新版创建广告：创建广告组层级否定关键词失败，puid：%d，shopId：%d，活动id：%s，广告组id：%s \n index------失败原因", request.getPuid(), request.getShopId(), campaignId, groupId);
                    stringBuilder.append(msg);
                    for (int i = 0; i < neKeywordInfoRespList.size(); i++) {
                        NeKeywordInfoResp neKeywordInfoResp = neKeywordInfoRespList.get(i);
                        if (neKeywordInfoResp != null && neKeywordInfoResp.getCode() != ResultUtil.SUCCESS) {
                            print = true;
                            stringBuilder.append("\n").append(neKeywordInfoResp.getIndex()).append("------").append(neKeywordInfoResp.getErrMsg());
                        }
                    }
                    if (print) {
                        WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, stringBuilder.toString());
                    }
                }
            }

            //广告组层级否定商品失败告警
            NeTargetResp neTargetResp = data.getNeTargetResp();
            if (neTargetResp.getCode() == Result.ERROR) {
                String msg = String.format("新版创建广告：创建广告组层级否定关键词失败，失败原因：%s，puid：%d，shopId：%d，活动id：%s，广告组id：%s", neTargetResp.getNeTargetErrMsg(), request.getPuid(), request.getShopId(), campaignId, groupId);
                WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, msg);
            } else {
                List<NeTargetInfoResp> neTargetInfoRespList = neTargetResp.getNeTargetsList();
                if (CollectionUtils.isNotEmpty(neTargetInfoRespList)) {
                    boolean print = false;
                    StringBuilder stringBuilder = new StringBuilder();
                    String msg = String.format("新版创建广告：创建广告组层级否定商品失败，puid：%d，shopId：%d，活动id：%s，广告组id：%s \n index------失败原因", request.getPuid(), request.getShopId(), campaignId, groupId);
                    stringBuilder.append(msg);
                    for (int i = 0; i < neTargetInfoRespList.size(); i++) {
                        NeTargetInfoResp neTargetInfoResp = neTargetInfoRespList.get(i);
                        if (neTargetInfoResp != null && neTargetInfoResp.getCode() != ResultUtil.SUCCESS) {
                            print = true;
                            stringBuilder.append("\n").append(neTargetInfoResp.getIndex()).append("------").append(neTargetInfoResp.getErrMsg());
                        }
                    }
                    if (print) {
                        WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, stringBuilder.toString());
                    }
                }
            }

        }, ThreadPoolUtil.getPrintWxLogExecutor()).exceptionally((e) -> null);
    }

    /**
     * 创建广告活动
     *
     * @param request                     request
     * @param createSpCampaignInfoBuilder createSpCampaignInfoBuilder
     * @param shop                        shop
     * @param amazonAdProfile             amazonAdProfile
     * @return 成功返回campaignId，失败返回null
     */
    private String createCampaign(CreateSpCampaignReq request, CreateSpCampaignInfo.Builder createSpCampaignInfoBuilder, ShopAuth shop, AmazonAdProfile amazonAdProfile) {
        //1，构建活动创建vo
        CampaignVo campaignVo;
        try {
            campaignVo = JSON.parseObject(request.getSpCampaignInfo(), CampaignVo.class);
        } catch (Exception e) {
            log.error("广告活动格式错误, string:{}----------e-----------", request.getSpCampaignInfo());
            CampaignResp.Builder spCampaign = CampaignResp.newBuilder();
            spCampaign.setCode(Result.ERROR);
            spCampaign.setCampaignErrMsg("广告活动格式错误");
            createSpCampaignInfoBuilder.setCampaignResp(spCampaign.build());
            return null;
        }
        campaignVo.setUid(request.getUid());
        campaignVo.setPuid(request.getPuid());
        campaignVo.setShopId(request.getShopId());
        campaignVo.setLoginIp(request.getLoginIp());

        //2，创建活动
        if (StringUtils.isNotBlank(campaignVo.getCampaignId())) {
            CampaignResp.Builder spCampaign = CampaignResp.newBuilder();
            spCampaign.setCode(Result.SUCCESS);
            spCampaign.setCampaignId(campaignVo.getCampaignId());
            createSpCampaignInfoBuilder.setCampaignResp(spCampaign);
            return campaignVo.getCampaignId();
        }
        CampaignResp spCampaign = spCampaignService.createSpCampaign(campaignVo, shop, amazonAdProfile);
        createSpCampaignInfoBuilder.setCampaignResp(spCampaign);
        if (StringUtils.isBlank(spCampaign.getCampaignId())) {
            return null;
        }
        return spCampaign.getCampaignId();
    }

    /**
     * 创建广告组
     *
     * @param request                     request
     * @param createSpCampaignInfoBuilder createSpCampaignInfoBuilder
     * @param shop                        shop
     * @param campaignId                  campaignId
     * @return 成功返回AmazonAdGroup，失败返回null
     */
    private AmazonAdGroup createGroup(CreateSpCampaignReq request, CreateSpCampaignInfo.Builder createSpCampaignInfoBuilder, ShopAuth shop, String campaignId, AmazonAdProfile amazonAdProfile) {
        //1，构建请求参数
        SPadGroupVo groupVo;
        try {
            groupVo = JSON.parseObject(request.getSpGroupInfo(), SPadGroupVo.class);
        } catch (Exception e) {
            log.error("广告组格式错误, string:{}----------e-----------", request.getSpCampaignInfo());
            GroupResp.Builder spGroup = GroupResp.newBuilder();
            spGroup.setCode(Result.ERROR);
            spGroup.setGroupErrMsg("广告组格式错误");
            createSpCampaignInfoBuilder.setGroupResp(spGroup.build());
            return null;
        }
        groupVo.setUid(request.getUid());
        groupVo.setPuid(request.getPuid());
        groupVo.setShopId(request.getShopId());
        groupVo.setLoginIp(request.getLoginIp());
        groupVo.setCampaignId(campaignId);

        //2，创建广告组
        String groupId = groupVo.getGroupId();
        if (StringUtils.isBlank(groupId)) {
            GroupResp spGroup = spGroupService.createSpGroup(groupVo, shop, amazonAdProfile);
            createSpCampaignInfoBuilder.setGroupResp(spGroup);
            if (StringUtils.isBlank(spGroup.getAdGroupId())) {
                return null;
            }
            groupId = spGroup.getAdGroupId();
        }

        AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByAdGroupId(shop.getPuid(), shop.getId(), groupId);
        if (Objects.isNull(amazonAdGroup)) {
            GroupResp.Builder spGroup = GroupResp.newBuilder();
            spGroup.setCode(Result.ERROR);
            spGroup.setGroupErrMsg("广告组不存在");
            createSpCampaignInfoBuilder.setGroupResp(spGroup);
            return null;
        }
        GroupResp.Builder spGroup = GroupResp.newBuilder();
        spGroup.setCode(Result.SUCCESS);
        spGroup.setAdGroupId(groupId);
        createSpCampaignInfoBuilder.setGroupResp(spGroup);
        return amazonAdGroup;
    }

    /**
     * 创建投放和否定投放
     *
     * @param createSpCampaignInfoBuilder createSpCampaignInfoBuilder
     * @param shop                        shop
     * @param amazonAdProfile             amazonAdProfile
     * @param group                       group
     */
    private void createTargetAndNeTarget(CreateSpTargetDto spTargetDto, CreateSpCampaignInfo.Builder createSpCampaignInfoBuilder, ShopAuth shop, AmazonAdProfile amazonAdProfile, AmazonAdGroup group) {
        //多线程创建投放、否定关键词投放、否定商品投放
        List<CompletableFuture<Void>> futureList = new ArrayList<>(3);
        //创建投放
        CompletableFuture<Void> spTargetCompletableFuture = CompletableFuture.runAsync(() -> {
            TargetResp spTarget = spTargetService.createSpTarget(amazonAdProfile, spTargetDto, shop, group);
            createSpCampaignInfoBuilder.setTargetResp(spTarget);
        }, ThreadPoolUtil.getCreateAdForTargetExecutor()).exceptionally((e) -> {
            log.error("创建投放 error", e);
            return null;
        });
        futureList.add(spTargetCompletableFuture);

        //创建否定关键词投放
        CompletableFuture<Void> spNeKeywordCompletableFuture = CompletableFuture.runAsync(() -> {
            NeKeywordResp spNeKeyword = spTargetService.createSpNeKeyword(spTargetDto, shop, group);
            createSpCampaignInfoBuilder.setNeKeywordResp(spNeKeyword);
        }, ThreadPoolUtil.getCreateAdForTargetExecutor()).exceptionally((e) -> {
            log.error("创建否定关键词投放 error", e);
            return null;
        });
        futureList.add(spNeKeywordCompletableFuture);

        //创建否定产品投放
        CompletableFuture<Void> spNeTargetCompletableFuture = CompletableFuture.runAsync(() -> {
            NeTargetResp spNeTarget = spTargetService.createSpNeProductTarget(spTargetDto, shop, group);
            createSpCampaignInfoBuilder.setNeTargetResp(spNeTarget);
        }, ThreadPoolUtil.getCreateAdForTargetExecutor()).exceptionally((e) -> {
            log.error("创建否定产品投放 error", e);
            return null;
        });
        futureList.add(spNeTargetCompletableFuture);

        CompletableFuture<Void> allOf = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
        try {
            // 等待所有 CompletableFuture 完成
            allOf.join();
        } catch (CompletionException e) {
            log.error("Error occurred while create CompletableFuture target", e);
        }
    }

    /**
     * 构建 CreateSpTargetDto
     *
     * @param request                     request
     * @param createSpCampaignInfoBuilder createSpCampaignInfoBuilder
     * @param group                       group
     * @return 成功返回CreateSpTargetDto，失败返回null
     */
    private CreateSpTargetDto buildCreateSpTargetDto(CreateSpCampaignReq request, CreateSpCampaignInfo.Builder createSpCampaignInfoBuilder, AmazonAdGroup group) {
        CreateSpTargetDto spTargetDto;
        try {
            spTargetDto = JSON.parseObject(request.getSpTargetInfo(), CreateSpTargetDto.class);
        } catch (Exception e) {
            log.error("广告投放格式错误, string:{}----------e-----------", request.getSpCampaignInfo(), e);
            TargetResp.Builder target = TargetResp.newBuilder();
            target.setCode(Result.ERROR);
            target.setTargetErrMsg("广告投放格式错误");
            createSpCampaignInfoBuilder.setTargetResp(target.build());

            NeKeywordResp.Builder neKeyWord = NeKeywordResp.newBuilder();
            neKeyWord.setCode(Result.ERROR);
            neKeyWord.setNeKeywordErrMsg("广告投放格式错误");
            createSpCampaignInfoBuilder.setNeKeywordResp(neKeyWord.build());

            NeTargetResp.Builder neTarget = NeTargetResp.newBuilder();
            neTarget.setCode(Result.ERROR);
            neTarget.setNeTargetErrMsg("广告投放格式错误");
            createSpCampaignInfoBuilder.setNeTargetResp(neTarget.build());
            return null;
        }
        spTargetDto.setUid(request.getUid());
        spTargetDto.setPuid(request.getPuid());
        spTargetDto.setShopId(request.getShopId());
        spTargetDto.setLoginIp(request.getLoginIp());
        spTargetDto.setGroupId(group.getAdGroupId());
        return spTargetDto;
    }

    private void sendErrResp(CreateSpCampaignResp.Builder builder, String msg, StreamObserver<CreateSpCampaignResp> responseObserver) {
        builder.setCode(Result.ERROR);
        builder.setMsg(msg);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 更新活动
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateCampaign(UpdateCampaignRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-更新活动 request {}", request);
        CampaignVo.CampaignVoBuilder campaignVoBuilder = CampaignVo.builder();
        if (request.hasPuid()) {
            campaignVoBuilder.puid(request.getPuid().getValue());
        }
        if (request.hasShopId()) {
            campaignVoBuilder.shopId(request.getShopId().getValue());
        }
        if (request.hasDailyBudget()) {
            campaignVoBuilder.dailyBudget(request.getDailyBudget().getValue());
        }
        if (request.hasDailyBudget()) {
            campaignVoBuilder.dailyBudget(request.getDailyBudget().getValue());
        }
        if (request.hasPlacementTop()) {
            campaignVoBuilder.placementTop(request.getPlacementTop().getValue());
        }
        if (request.hasPlacementProductPage()) {
            campaignVoBuilder.placementProductPage(request.getPlacementProductPage().getValue());
        }
        if (request.hasPlacementRestOfSearch()) {
            campaignVoBuilder.placementRestOfSearch(request.getPlacementRestOfSearch().getValue());
        }
        if (request.hasPlacementSiteAmazonBusiness()) {
            campaignVoBuilder.placementSiteAmazonBusiness(request.getPlacementSiteAmazonBusiness().getValue());
        }
        campaignVoBuilder.endDateStr(request.hasEndDateStr() ? request.getEndDateStr() : null);
        campaignVoBuilder.startDateStr(request.getStartDateStr());
        if (request.hasPortfolioId() && StringUtils.isNotBlank(request.getPortfolioId())) {
            if (Constant.NON_PORTFOLIO_ID.equals(request.getPortfolioId())) {
                campaignVoBuilder.portfolioId("");
            } else {
                campaignVoBuilder.portfolioId(request.getPortfolioId());
            }
        }
        campaignVoBuilder.loginIp(request.getLoginIp());
        campaignVoBuilder.name(request.getName());
        campaignVoBuilder.uid(request.getUid().getValue());
        campaignVoBuilder.strategy(request.getStrategy());
        campaignVoBuilder.dxmCampaignId(request.getDxmCampaignId().getValue());
        CampaignVo vo = campaignVoBuilder.build();
        //做参数校验
        String err = checkCreateCampaignParams(vo, "update");

        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasDxmCampaignId() || !request.hasPuid() || !request.hasUid()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");

        } else if (StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(err);
        } else {
            //处理业务返回结果
            Result result = cpcSpCampaignService.updateCampaign(vo);
            builder.setCode(Int32Value.newBuilder().setValue(result.getCode()).build());
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 批量更新
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void batchUpdateCampaign(BatchUpdateCampaignRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-更新活动 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        List<BatchUpdateCampaignVo> vosList = request.getVosList();
        if (!request.hasType() || CollectionUtils.isEmpty(vosList) || !request.hasPuid() || !request.hasUid() || !request.hasShopId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            Integer puid = request.getPuid().getValue();
            Integer shopId = request.getShopId().getValue();
            Integer uid = request.getUid().getValue();
            String ip = request.getLoginIp();
            String type = request.getType();
            List<BatchCampaignVo> updateList = Lists.newArrayListWithCapacity(vosList.size());
            for (BatchUpdateCampaignVo vo : vosList) {
                BatchCampaignVo.BatchCampaignVoBuilder campaignVoBuilder = BatchCampaignVo.builder();
                campaignVoBuilder.puid(puid);
                campaignVoBuilder.shopId(shopId);
                campaignVoBuilder.uid(uid);
                if (StringUtils.isNotBlank(ip)) {
                    campaignVoBuilder.loginIp(ip);
                }
                if (vo.hasDailyBudget()) {
                    campaignVoBuilder.dailyBudget(vo.getDailyBudget().getValue());
                }
                if (vo.hasType()) {
                    campaignVoBuilder.type(vo.getType());
                }
                BatchUpdateCampaignVo.CampaignState state = vo.getState();
                if (state != null) {
                    CpcStatusEnum statusEnum = CpcStatusEnum.valueOf(state.getValueDescriptor().getName());
                    if (statusEnum != null) {
                        campaignVoBuilder.state(statusEnum.name());
                    }
                }

                if (vo.hasPlacementTop()) {
                    campaignVoBuilder.placementTop(vo.getPlacementTop().getValue());
                }
                if (vo.hasPlacementProductPage()) {
                    campaignVoBuilder.placementProductPage(vo.getPlacementProductPage().getValue());
                }
                if (vo.hasPlacementRestOfSearch()) {
                    campaignVoBuilder.placementRestOfSearch(vo.getPlacementRestOfSearch().getValue());
                }
                if (vo.hasPlacementSiteAmazonBusiness()) {
                    campaignVoBuilder.placementSiteAmazonBusiness(vo.getPlacementSiteAmazonBusiness().getValue());
                }
                if (vo.hasStrategy()) {
                    campaignVoBuilder.strategy(vo.getStrategy());
                }
                if (vo.hasDxmCampaignId()) {
                    campaignVoBuilder.dxmCampaignId(vo.getDxmCampaignId().getValue());
                }

                if (vo.hasCampaignId()) {
                    campaignVoBuilder.campaignId(vo.getCampaignId());
                }

                BatchCampaignVo campaignvo = campaignVoBuilder.build();
                updateList.add(campaignvo);
            }
            //处理业务返回结果
            Result result = cpcSpCampaignService.batchUpdateCampaign(updateList, puid, uid, shopId, type);
            builder.setCode(Int32Value.newBuilder().setValue(result.getCode()).build());
            if (result.getData() != null) {
                builder.setData(JSONUtil.objectToJson(result.getData()));
            }
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 批量更新
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void batchUpdateMultiShopCampaign(BatchUpdateCampaignRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-更新活动 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        List<BatchUpdateCampaignVo> vosList = request.getVosList();
        boolean b = vosList.stream().allMatch(BatchUpdateCampaignVo::hasShopId);
        if (!request.hasType() || CollectionUtils.isEmpty(vosList) || !request.hasPuid() || !request.hasUid() || !b) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            Integer puid = request.getPuid().getValue();
            Integer uid = request.getUid().getValue();
            String ip = request.getLoginIp();
            String type = request.getType();
            List<BatchCampaignVo> updateList = Lists.newArrayListWithCapacity(vosList.size());
            for (BatchUpdateCampaignVo vo : vosList) {
                BatchCampaignVo.BatchCampaignVoBuilder campaignVoBuilder = BatchCampaignVo.builder();
                campaignVoBuilder.puid(puid);
                if (vo.hasShopId()) {
                    campaignVoBuilder.shopId(vo.getShopId().getValue());
                }
                campaignVoBuilder.uid(uid);
                if (StringUtils.isNotBlank(ip)) {
                    campaignVoBuilder.loginIp(ip);
                }
                if (vo.hasDailyBudget()) {
                    campaignVoBuilder.dailyBudget(vo.getDailyBudget().getValue());
                }
                if (vo.hasType()) {
                    campaignVoBuilder.type(vo.getType());
                }
                BatchUpdateCampaignVo.CampaignState state = vo.getState();
                if (state != null) {
                    CpcStatusEnum statusEnum = CpcStatusEnum.valueOf(state.getValueDescriptor().getName());
                    if (statusEnum != null) {
                        campaignVoBuilder.state(statusEnum.name());
                    }
                }

                if (vo.hasPlacementTop()) {
                    campaignVoBuilder.placementTop(vo.getPlacementTop().getValue());
                }
                if (vo.hasPlacementProductPage()) {
                    campaignVoBuilder.placementProductPage(vo.getPlacementProductPage().getValue());
                }
                if (vo.hasPlacementRestOfSearch()) {
                    campaignVoBuilder.placementRestOfSearch(vo.getPlacementRestOfSearch().getValue());
                }
                if (vo.hasPlacementSiteAmazonBusiness()) {
                    campaignVoBuilder.placementSiteAmazonBusiness(vo.getPlacementSiteAmazonBusiness().getValue());
                }
                if (vo.hasStrategy()) {
                    campaignVoBuilder.strategy(vo.getStrategy());
                }
                if (vo.hasDxmCampaignId()) {
                    campaignVoBuilder.dxmCampaignId(vo.getDxmCampaignId().getValue());
                }

                if (vo.hasCampaignId()) {
                    campaignVoBuilder.campaignId(vo.getCampaignId());
                }

                BatchCampaignVo campaignvo = campaignVoBuilder.build();
                updateList.add(campaignvo);
            }
            //处理业务返回结果
            Result result = cpcSpCampaignService.batchUpdateMultiShopCampaign(updateList, puid, uid, type);
            builder.setCode(Int32Value.newBuilder().setValue(result.getCode()).build());
            if (result.getData() != null) {
                builder.setData(JSONUtil.objectToJson(result.getData()));
            }
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 更新活动状态
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateState(UpdateCampaignStateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-更新活动状态 request {}", request);
        UpdateCampaignStateRequest.CampaignState state = request.getState();

        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasId() || !request.hasPuid() || !request.hasUid() || state == null) {
            builder.setMsg("请求参数错误");
            builder.setCode(Int32Value.newBuilder().setValue(Result.ERROR).build());
        } else {
            CpcStatusEnum statusEnum;
            statusEnum = CpcStatusEnum.valueOf(state.getValueDescriptor().getName());
            //处理业务返回结果
            Result result = cpcSpCampaignService.updateStatus(request.getPuid().getValue(), request.getUid().getValue(), request.getLoginIp(), request.getId().getValue(), statusEnum);
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
            builder.setCode(Int32Value.newBuilder().setValue(result.getCode()).build());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 修改广告名称
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateName(UpdateCampaignNameRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-修改广告名称 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasId() || !request.hasPuid() || !request.hasUid() || StringUtils.isBlank(request.getName())) {
            builder.setCode(Int32Value.newBuilder().setValue(Result.ERROR).build());
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result res = cpcSpCampaignService.updateName(request.getPuid().getValue(), request.getUid().getValue(), request.getLoginIp(), request.getId().getValue(), request.getName());
            builder.setCode(Int32Value.newBuilder().setValue(res.getCode()).build());
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告活动-改预算
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateBudget(UpdateBudgetRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-广告活动-改预算 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasId() || !request.hasPuid() || !request.hasUid() || request.hasBudget() && request.getBudget().getValue() < 0) {
            builder.setCode(Int32Value.newBuilder().setValue(Result.ERROR).build());
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result result = cpcSpCampaignService.updateBudget(request.getPuid().getValue(), request.getUid().getValue(), request.getLoginIp(), request.getId().getValue(), request.getBudget().getValue());
            builder.setCode(Int32Value.newBuilder().setValue(result.getCode()).build());
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告活动-改广告位调整竞价（增加新广告位修改这个接口）
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updatePlacementBid(UpdatePlacementBidRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-广告活动-改广告位调整竞价-列表页头部 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        PredicateEnum predicateEnum = PredicateEnum.fromValue(request.getPlacement());
        if (!request.hasId() || !request.hasPuid() || !request.hasUid() || !request.hasPercentage() || request.getPercentage().getValue() < 0
                || predicateEnum == null) {
            builder.setCode(Int32Value.newBuilder().setValue(Result.ERROR).build());
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result result = cpcSpCampaignService.updatePlacemet(request.getPuid().getValue(), request.getUid().getValue(), request.getLoginIp(),
                    request.getId().getValue(), predicateEnum, request.getPercentage().getValue());

            builder.setCode(Int32Value.newBuilder().setValue(result.getCode()).build());
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告活动-改广告位调整竞价-产品详情页
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updatePlacemetProductPage(UpdatePlacemetProductPageRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-广告活动-改广告位调整竞价-产品详情页 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasId() || !request.hasPuid() || !request.hasUid() || request.hasPercentage() && request.getPercentage().getValue() < 0) {
            builder.setCode(Int32Value.newBuilder().setValue(Result.ERROR).build());
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result result = cpcSpCampaignService.updatePlacemet(request.getPuid().getValue(), request.getUid().getValue(), request.getLoginIp(), request.getId().getValue(), PredicateEnum.PLACEMENTPRODUCTPAGE, request.getPercentage().getValue());
            builder.setCode(Int32Value.newBuilder().setValue(result.getCode()).build());
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告活动-改广告位调整竞价-列表页头部
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updatePlacemetTop(UpdatePlacemetTopReqeust request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-广告活动-改广告位调整竞价-列表页头部 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasId() || !request.hasPuid() || !request.hasUid() || !request.hasPercentage() || request.getPercentage().getValue() < 0) {
            builder.setCode(Int32Value.newBuilder().setValue(Result.ERROR).build());
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result result = cpcSpCampaignService.updatePlacemet(request.getPuid().getValue(), request.getUid().getValue(), request.getLoginIp(),
                request.getId().getValue(), PredicateEnum.PLACEMENTTOP, request.getPercentage().getValue());

            builder.setCode(Int32Value.newBuilder().setValue(result.getCode()).build());
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告活动-改广告位调整竞价-其他位置
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updatePlacementRestOfSearch(UpdatePlacementRestOfSearchReqeust request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-广告活动-改广告位调整竞价-其他位置 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasId() || !request.hasPuid() || !request.hasUid() || !request.hasPercentage() || request.getPercentage().getValue() < 0) {
            builder.setCode(Int32Value.newBuilder().setValue(Result.ERROR).build());
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result result = cpcSpCampaignService.updatePlacemet(request.getPuid().getValue(), request.getUid().getValue(), request.getLoginIp(),
                request.getId().getValue(), PredicateEnum.PLACEMENTRESTOFSEARCH, request.getPercentage().getValue());

            builder.setCode(Int32Value.newBuilder().setValue(result.getCode()).build());
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 更新开始时间
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateStartDate(UpdateStartDateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-更新开始时间 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasId() || !request.hasPuid() || !request.hasUid() || StringUtils.isBlank(request.getStartDate())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result result = cpcSpCampaignService.updateStartDate(request.getPuid().getValue(), request.getUid().getValue(),
                request.getLoginIp(), request.getId().getValue(), request.getStartDate());
            builder.setCode(Int32Value.of(result.getCode()));
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 更新结束时间
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateEnddate(UpdateEnddateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-更新结束时间 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        boolean b = !request.hasEndDate();
        if (!request.hasId() || !request.hasPuid() || !request.hasUid() || b) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result result = cpcSpCampaignService.updateEndDate(request.getPuid().getValue(), request.getUid().getValue(),
                request.getLoginIp(), request.getId().getValue(), request.getEndDate());
            builder.setCode(Int32Value.of(result.getCode()));
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告活动-修改广告位调整价
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updatePlacementAdjustment(UpdatePlacementAdjustmentRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-修改广告位调整价 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasId() || !request.hasPuid() || !request.hasUid()
            || StringUtils.isBlank(request.getPlacement())
            || StringUtils.isBlank(request.getValue())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result result = cpcSpCampaignService.updatePlacementAdjustment(request.getPuid().getValue(), request.getUid().getValue()
                , request.getLoginIp(), request.getId().getValue(), request.getPlacement(), request.getValue());
            builder.setCode(Int32Value.of(result.getCode()));
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告活动-修改广告位调整价
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void batchUpdatePlacementAdjustment(BatchUpdatePlacementAdjustmentRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-修改广告位批量调整价 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasUid() || !request.hasLoginIp()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result result = cpcSpCampaignService.batchUpdatePlacementAdjustment(request.getPuid().getValue(), request.getUid().getValue()
                , request.getLoginIp(), request.getUpdatePlacementAdjustmentsList());
            if (result.getData() != null) {
                builder.setData(JSONUtil.objectToJson(result.getData()));
            }
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 归档
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void archive(ArchiveRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-归档 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasUid() || !request.hasId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result result = cpcSpCampaignService.archive(request.getPuid().getValue(), request.getUid().getValue(), request.getLoginIp(),
                request.getId().getValue());
            builder.setCode(Int32Value.of(result.getCode()));
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 获取顶级预算
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getTopBudget(GetTopBudgetRequest request, StreamObserver<GetTopBudgetResponse> responseObserver) {
        log.info("sp-campaign-获取顶级预算 request {}", request);
        GetTopBudgetResponse.Builder builder = GetTopBudgetResponse.newBuilder();
        Double dailyBudget = null;
        boolean openTopBudget = false;
        if (!request.hasPuid() || !request.hasShopId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            AmazonAdProfile profile = amazonAdProfileDao.getProfile(request.getPuid().getValue(), request.getShopId().getValue());
            if (profile != null) {
                dailyBudget = profile.getDailyBudget();
                if (dailyBudget != null && dailyBudget < Constants.TOP_BUDGET) {
                    openTopBudget = true;
                } else {
                    dailyBudget = null;
                }
            }
            BudgetRpcVo.Builder voBuilder = BudgetRpcVo.newBuilder().setOpenTopBudget(BoolValue.of(openTopBudget));
            if (dailyBudget != null) {
                voBuilder.setTopBudget(DoubleValue.of(dailyBudget));
            }
            //查询当前应用状态
            AdvertiseStrategyTopBudgetTemplate advertiseStrategyTopBudgetTemplate =
                advertiseStrategyTopBudgetTemplateDao.selectById(request.getPuid().getValue(), request.getShopId().getValue());
            if (advertiseStrategyTopBudgetTemplate != null) {
                voBuilder.setStatus(advertiseStrategyTopBudgetTemplate.getStatus());
            }

            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setData(voBuilder.build());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告活动-顶级预算
     */
    @Override
    public void topBudget(TopBudgetRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-广告活动-顶级预算 request {}", request);
        double topBudget = 9.99999999E8;  //顶级预算
        double budget = 0.0;

        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasUseTopBudget() && request.hasBudget()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            budget = request.getBudget().getValue();
            if (!request.getUseTopBudget().getValue()) {
                budget = topBudget;
            }
            //处理业务返回结果
            Result result = cpcSpCampaignService.topBudget(request.getPuid().getValue(), request.getShopId().getValue(), budget);
            builder.setCode(Int32Value.of(result.getCode()));
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告活动-增加备注记录
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void addNote(AddNoteRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-广告活动-增加备注记录 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasUid() || request.hasId() || StringUtils.isBlank(request.getNote())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result result = cpcSpCampaignService.addNote(request.getPuid().getValue(), request.getUid().getValue(),
                request.getLoginIp(), request.getShopId().getValue(), request.getId().getValue(), request.getNote());
            builder.setCode(Int32Value.of(result.getCode()));
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 添加广告活动下的否定关键词
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void addNeKeywords(AddNeKeywordsRequest request, StreamObserver<NeKeywordResponse> responseObserver) {
        log.info("sp-campaign-添加广告活动下的否定关键词 request {}", request);
        NeKeywordResponse.Builder builder = NeKeywordResponse.newBuilder();

        if (!request.hasPuid() || !request.hasUid() || StringUtils.isBlank(request.getCampaignId()) || CollectionUtils.isEmpty(request.getNeKeywordsList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            com.meiyunji.sponsored.service.cpc.vo.AddCampaignNeKeywordsVo kvo = new com.meiyunji.sponsored.service.cpc.vo.AddCampaignNeKeywordsVo();
            kvo.setPuid(request.getPuid().getValue());
            kvo.setShopId(request.getShopId().getValue());
            kvo.setCampaignId(request.getCampaignId());
            kvo.setUid(request.getUid().getValue());
            kvo.setLoginIp(request.getLoginIp());
            //处理集合
            List<NeKeywordsVo> neKeywordsList = request.getNeKeywordsList();
            List<com.meiyunji.sponsored.service.cpc.vo.NeKeywordsVo> kList = neKeywordsList.stream().map(item -> {
                com.meiyunji.sponsored.service.cpc.vo.NeKeywordsVo vo = new com.meiyunji.sponsored.service.cpc.vo.NeKeywordsVo();
                vo.setKeywordText(StringUtil.replaceSpecialSymbol(item.getKeywordText()));
                vo.setMatchType(item.getMatchType());
                return vo;
            }).collect(Collectors.toList());
            kvo.setNeKeywords(kList);
            //处理业务返回结果
            Result<List<NeKeywordResponse.Data>> result = cpcSpCampaignService.createNeKeywords(kvo, request.getLoginIp());
            builder.setCode(Int32Value.of(result.getCode()));
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
            if (result.getData() != null && result.getCode() == 0) {
                builder.addAllData(result.getData());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告活动-否定关键词-归档
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void archiveNeKeywords(ArchiveNeKeywordsRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-广告活动-否定关键词-归档 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasId() || !request.hasPuid() || !request.hasUid()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result result = cpcSpCampaignService.archiveNeKeywords(request.getPuid().getValue(), request.getUid().getValue()
                , request.getLoginIp(), request.getId().getValue());
            builder.setCode(Int32Value.of(result.getCode()));
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告活动-否定关键词-批量归档
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void batchArchiveNeKeywords(BatchArchiveSpNeKeywordsRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-广告活动-否定关键词-批量归档 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasShopId() || !request.hasIdList() || !request.hasPuid() || !request.hasUid()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
        } else {
            List<Long> idList = Arrays.stream(request.getIdList().split(",")).map(Long::valueOf).collect(Collectors.toList());
            Result result = cpcSpCampaignService.batchArchiveNeKeywords(request.getPuid().getValue(), request.getShopId().getValue(),
                request.getUid().getValue(), request.getLoginIp(), idList);

            builder.setCode(Int32Value.newBuilder().setValue(result.getCode()).build());
            if (result.getData() != null) {
                builder.setData(JSONUtil.objectToJson(result.getData()));
            }
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告活动-否定商品投放-创建
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void createNetargeting(CreateNetargetingRequest request, StreamObserver<CreateNetargetingResponse> responseObserver) {
        log.info("sp-campaign-广告活动-否定商品投放-创建 request {}", request);
        CreateNetargetingResponse.Builder builder = CreateNetargetingResponse.newBuilder();
        if (!request.hasShopId() || !request.hasPuid() || !request.hasUid() || StringUtils.isBlank(request.getCampaignId()) || CollectionUtils.isEmpty(request.getAsinListList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            List<asinVo> asinListList = request.getAsinListList();
            List<NeTargetingVo> neTargetingVoList = asinListList.stream().filter(Objects::nonNull).map(item -> {
                NeTargetingVo neTargetingVo = new NeTargetingVo();
                BeanUtils.copyProperties(item, neTargetingVo);
                return neTargetingVo;
            }).collect(Collectors.toList());
            //处理业务返回结果
            Result<List<CampaignNeTargetingSpAddReturnVo>> result = cpcSpCampaignService.createNetargeting(CampaignNeTargetingSpAddParam.builder()
                .puid(request.getPuid().getValue())
                .shopId(request.getShopId().getValue())
                .uid(request.getUid().getValue())
                .loginIp(request.getLoginIp())
                .campaignId(request.getCampaignId())
                .asinList(neTargetingVoList)
                .build());
            if (result.error()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg(result.getMsg());
            } else {
                List<CampaignNeTargetingSpAddReturnVo> tVos = result.getData();

                builder.setCode(Int32Value.of(Result.SUCCESS));
                if (CollectionUtils.isNotEmpty(tVos)) {
                    List<CampaignNeTargetingSpAddReturnRpcVo> rpcVos = tVos.stream().filter(Objects::nonNull).map(item -> {
                        CampaignNeTargetingSpAddReturnRpcVo.Builder voBuilder = CampaignNeTargetingSpAddReturnRpcVo.newBuilder();
                        if (item.getCampaignId() != null) {
                            voBuilder.setCampaignId(item.getCampaignId());
                        }
                        if (item.getTargetId() != null) {
                            voBuilder.setTargetId(item.getTargetId());
                        }
                        if (item.getAsin() != null) {
                            voBuilder.setAsin(item.getAsin());
                        }
                        if (item.getTitle() != null) {
                            voBuilder.setTitle(item.getTitle());
                        }
                        if (item.getImgUrl() != null) {
                            voBuilder.setImgUrl(item.getImgUrl());
                        }
                        if (item.getState() != null) {
                            voBuilder.setState(item.getState());
                        }
                        if (item.getFailReason() != null) {
                            voBuilder.setFailReason(item.getFailReason());
                        }
                        return voBuilder.build();

                    }).collect(Collectors.toList());
                    builder.addAllData(rpcVos);
                }
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告活动-否定商品投放-归档
     */
    @Override
    public void archiveNetargeting(ArchiveNetargetingRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-广告活动-否定商品投放-归档 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasId() || !request.hasPuid() || !request.hasShopId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Map<Boolean, String> res = cpcSpCampaignService.archiveNetargeting(request.getShopId().getValue(), request.getUid().getValue(), request.getId().getValue(), request.getLoginIp());
            String falseStr = res.get(Boolean.FALSE);
            String trueStr = res.get(Boolean.TRUE);
            builder.setCode(Int32Value.of(trueStr != null ? Result.SUCCESS : Result.ERROR));
            if (falseStr != null) {
                builder.setMsg(falseStr);
            }

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告活动-否定商品投放-批量归档
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void batchArchiveNeTargeting(BatchArchiveNeTargetingRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-campaign-广告活动-否定商品投放-批量归档 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasShopId() || !request.hasIdList() || !request.hasPuid() || !request.hasUid()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
        } else {
            List<Long> idList = Arrays.stream(request.getIdList().split(",")).map(Long::valueOf).collect(Collectors.toList());
            Result result = cpcSpCampaignService.batchArchiveNetargeting(request.getPuid().getValue(), request.getShopId().getValue(),
                request.getUid().getValue(), request.getLoginIp(), idList);

            builder.setCode(Int32Value.newBuilder().setValue(result.getCode()).build());
            if (result.getData() != null) {
                builder.setData(JSONUtil.objectToJson(result.getData()));
            }
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 应用搜索词时，根据适配类型获取可支持的广告活动
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void listForQueryWord(ListForQueryWordRequest request, StreamObserver<ListForQueryWordResponse> responseObserver) {
        log.info("sp-campaign-应用搜索词时，根据适配类型获取可支持的广告活动 request {}", request);
        ListForQueryWordResponse.Builder builder = ListForQueryWordResponse.newBuilder();
        if (StringUtils.isBlank(request.getType()) || !request.hasPuid() || !request.hasShopId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result<List<CampaignNameVo>> res = cpcSpCampaignService.listForQueryWord(request.getPuid().getValue(), request.getShopId().getValue(), request.getType());
            builder.setCode(Int32Value.of(res.getCode()));
            builder.setMsg(res.getMsg());
            //处理list
            if (res.success()) {
                List<CampaignNameVo> data = res.getData();
                List<CampaignNameRpcVo> rpcVos = data.stream().filter(Objects::nonNull).map(item -> {
                    CampaignNameRpcVo.Builder voBuilder = CampaignNameRpcVo.newBuilder();
                    if (item.getId() != null) {
                        voBuilder.setId(Int64Value.of(item.getId()));
                    }
                    if (item.getCampaignId() != null) {
                        voBuilder.setCampaignId(item.getCampaignId());
                    }
                    if (item.getName() != null) {
                        voBuilder.setName(item.getName());
                    }
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }

        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 手动同步广告活动
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void syncCampaign(SyncCampaignRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (request.getShopId() == null || StringUtils.isBlank(request.getMarketplaceId()) ||
            StringUtils.isBlank(request.getCampaignIds())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            List<String> campaignIdList = StringUtil.stringToList(request.getCampaignIds(), StringUtil.SPLIT_COMMA);
            Result result = cpcSpCampaignService.syncCampaign(request.getPuid().getValue(), request.getShopId().getValue(),
                request.getMarketplaceId(), campaignIdList);
            builder.setCode(Int32Value.of(result.getCode()));
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private String checkCreateCampaignParams(CampaignVo vo, String OperateType) {
        if (vo == null) {
            return "请求参数错误";
        }
        if ("create".equals(OperateType) && TargetingTypeEnum.fromValue(vo.getTargetingType()) == null) {
            return "请求参数错误";
        }
        if ("update".equals(OperateType) && vo.getDxmCampaignId() == null) {
            return "请求参数错误";
        }
        if (vo.getShopId() == null || StringUtils.isBlank(vo.getName()) || StringUtils.isBlank(vo.getStartDateStr())
            || vo.getDailyBudget() == null) {
            return "请求参数错误";
        }

        if (StrategyEnum.fromValue(vo.getStrategy()) == null
            && !"ruleBased".equalsIgnoreCase(vo.getStrategy())) {
            return "请求参数错误";
        }

        if (vo.getName().length() > AmazonAdCampaign.nameLimit) {
            return MessageFormat.format("活动名称不能超过{0}个字符", AmazonAdCampaign.nameLimit);
        }

        return null;
    }

    @Override
    public void getBudgetRecommendation(BudgetRecommendationRequest request, StreamObserver<BudgetRecommendationResponse> responseObserver) {
        log.info("获取亚马逊错过的预算建议信息 request {}", request);
        BudgetRecommendationResponse.Builder builder = BudgetRecommendationResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasCampaignIds()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            List<String> campaignIdList = StringUtil.splitStr(request.getCampaignIds(), StringUtil.SPLIT_COMMA);
            Result result = cpcSpCampaignService.getBudgetRecommendation(request.getPuid(), request.getShopId(), campaignIdList);
            if (result.success()) {
                List<BudgetRecommendationDetails> detailsList = Lists.newArrayList();
                BudgetRecommendationsResponse budgetRecommendationsResponse = (BudgetRecommendationsResponse)result.getData();
                if (budgetRecommendationsResponse != null && CollectionUtils.isNotEmpty(budgetRecommendationsResponse.getSuccessResults())) {
                    // 接口调用成功同步数据
                    Map<String, AmazonAdMissBudget> missBudgetMap = amazonAdMissBudgetService.listByCampaignIds(request.getPuid(), request.getShopId(), campaignIdList).stream()
                        .filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdMissBudget::getCampaignId, Function.identity()));
                    List<AmazonAdMissBudget> updateList = Lists.newArrayList();
                    List<AmazonAdMissBudget> newList = Lists.newArrayList();
                    AmazonAdMissBudget budget;
                    for (BudgetRecommendationsSuccessResult succ : budgetRecommendationsResponse.getSuccessResults()) {
                        // 更新
                        if (missBudgetMap != null && missBudgetMap.get(succ.getCampaignId()) != null) {
                            budget = missBudgetMap.get(succ.getCampaignId());
                            budget.setUpdateId(request.getUid());
                            updateList.add(budget);
                        } else {
                            // 新增
                            budget = new AmazonAdMissBudget();
                            budget.setCreateId(request.getUid());
                            newList.add(budget);
                        }
                        budget.setPuid(request.getPuid());
                        budget.setShopId(request.getShopId());
                        budget.setCampaignId(succ.getCampaignId());

                        BudgetRecommendationDetails.Builder succBuilder = BudgetRecommendationDetails.newBuilder();
                        succBuilder.setCampaignId(succ.getCampaignId());
                        succBuilder.setIndex(succ.getIndex());
                        if (succ.getSuggestedBudget() != null) {
                            succBuilder.setSuggestedBudget(succ.getSuggestedBudget());
                            budget.setSuggestedBudget(BigDecimal.valueOf(succ.getSuggestedBudget()));
                        }
                        if (succ.getSevenDaysMissedOpportunities() != null) {
                            SevenDaysMissedOpportunities opportunities = succ.getSevenDaysMissedOpportunities();
                            if (opportunities.getEstimatedMissedSalesLower() != null) {
                                succBuilder.setEstimatedMissedSalesLower(opportunities.getEstimatedMissedSalesLower());
                                budget.setEstimatedMissedSalesLower(BigDecimal.valueOf(opportunities.getEstimatedMissedSalesLower()));
                            }
                            if (opportunities.getEstimatedMissedSalesUpper() != null) {
                                succBuilder.setEstimatedMissedSalesUpper(opportunities.getEstimatedMissedSalesUpper());
                                budget.setEstimatedMissedSalesUpper(BigDecimal.valueOf(opportunities.getEstimatedMissedSalesUpper()));
                            }
                            if (opportunities.getEstimatedMissedImpressionsLower() != null) {
                                succBuilder.setEstimatedMissedImpressionsLower(opportunities.getEstimatedMissedImpressionsLower());
                                budget.setEstimatedMissedImpressionsLower(opportunities.getEstimatedMissedImpressionsLower());
                            }
                            if (opportunities.getEstimatedMissedImpressionsUpper() != null) {
                                succBuilder.setEstimatedMissedImpressionsUpper(opportunities.getEstimatedMissedImpressionsUpper());
                                budget.setEstimatedMissedImpressionsUpper(opportunities.getEstimatedMissedImpressionsUpper());
                            }
                            if (opportunities.getEstimatedMissedClicksLower() != null) {
                                succBuilder.setEstimatedMissedClicksLower(opportunities.getEstimatedMissedClicksLower());
                                budget.setEstimatedMissedClicksLower(opportunities.getEstimatedMissedClicksLower());
                            }
                            if (opportunities.getEstimatedMissedClicksUpper() != null) {
                                succBuilder.setEstimatedMissedClicksUpper(opportunities.getEstimatedMissedClicksUpper());
                                budget.setEstimatedMissedClicksUpper(opportunities.getEstimatedMissedClicksUpper());
                            }
                            if (opportunities.getPercentTimeInBudget() != null) {
                                succBuilder.setPercentTimeInBudget(opportunities.getPercentTimeInBudget());
                                budget.setPercentTimeInBudget(BigDecimal.valueOf(opportunities.getPercentTimeInBudget()));
                            }
                            if (StringUtils.isNotBlank(opportunities.getStartDate())) {
                                succBuilder.setStartDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(opportunities.getStartDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
                                budget.setStartDate(opportunities.getStartDate());
                            }
                            if (StringUtils.isNotBlank(opportunities.getEndDate())) {
                                succBuilder.setEndDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(opportunities.getEndDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
                                budget.setEndDate(opportunities.getEndDate());
                            }
                        }
                        if (succ.getBudgetRuleRecommendation() != null) {
                            BudgetRuleRecommendation ruleRecommendation = succ.getBudgetRuleRecommendation();
                            if (StringUtils.isNotBlank(ruleRecommendation.getRuleId())) {
                                succBuilder.setRuleId(ruleRecommendation.getRuleId());
                                budget.setRuleId(ruleRecommendation.getRuleId());
                            }
                            if (StringUtils.isNotBlank(ruleRecommendation.getRuleName())) {
                                succBuilder.setRuleName(ruleRecommendation.getRuleName());
                                budget.setRuleName(ruleRecommendation.getRuleName());
                            }
                            if (ruleRecommendation.getSuggestedBudgetIncreasePercent() != null) {
                                succBuilder.setSuggestedBudgetIncreasePercent(ruleRecommendation.getSuggestedBudgetIncreasePercent());
                                budget.setSuggestedBudgetIncreasePercent(BigDecimal.valueOf(ruleRecommendation.getSuggestedBudgetIncreasePercent()));
                            }
                        }
                        detailsList.add(succBuilder.build());
                    }
                    // 同步数据
                    try {
                        amazonAdMissBudgetService.batchAdd(request.getPuid(), newList);
                        amazonAdMissBudgetService.batchUpdate(request.getPuid(), updateList);
                    } catch (Exception e) {
                        log.error("同步失败 失败原因:{}", e);
                    }
                }
                if (budgetRecommendationsResponse != null && CollectionUtils.isNotEmpty(budgetRecommendationsResponse.getErrorResults())) {
                    budgetRecommendationsResponse.getErrorResults().stream().filter(e -> e != null && e.getCampaignId() != null && e.getIndex() != null).forEach(fail -> {
                        BudgetRecommendationDetails.Builder failBuilder = BudgetRecommendationDetails.newBuilder();
                        failBuilder.setCampaignId(fail.getCampaignId());
                        failBuilder.setIndex(fail.getIndex());
                        if (fail.getError() != null) {
                            failBuilder.setCode(fail.getError().getCode());
                            failBuilder.setDetails(fail.getError().getDetails());
                        }
                        detailsList.add(failBuilder.build());
                    });
                }
                builder.addAllData(detailsList);
            }
            builder.setCode(result.getCode());
            builder.setMsg(result.getMsg());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getMultiShopBudgetRecommendation(MultiShopBudgetRecommendationRequest request, StreamObserver<BudgetRecommendationResponse> responseObserver) {
        log.info("获取亚马逊错过的预算建议信息 request {}", request);
        BudgetRecommendationResponse.Builder builder = BudgetRecommendationResponse.newBuilder();
        if (!request.hasPuid() || !request.hasCampaignIds()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            List<String> campaignIdList = StringUtil.splitStr(request.getCampaignIds(), StringUtil.SPLIT_COMMA);
            //通过广告活动id取店铺id
            Map<Integer, List<String>> shopCampaignIdsMap = amazonAdCampaignAllDao.listByShopIdAndCampaignIds(request.getPuid(), request.getShopIdList(), campaignIdList)
                .stream().collect(Collectors.groupingBy(AmazonAdCampaignAll::getShopId, Collectors.mapping(AmazonAdCampaignAll::getCampaignId, Collectors.toList())));
            List<BudgetRecommendationDetails> detailsList = Lists.newArrayList();
            //按店铺获取建议预算
            List<CompletableFuture<List<BudgetRecommendationDetails>>> futures = new ArrayList<>();
            ThreadPoolExecutor multiShopBudgetRecommendationPool = ThreadPoolUtil.getMultiShopBudgetRecommendationPool();
            try {
                shopCampaignIdsMap.forEach((k, v) -> {
                    futures.add(CompletableFuture.supplyAsync(() -> this.getBudgetRecommendationDetailsList(request.getPuid(), request.getUid(), k, v),
                        multiShopBudgetRecommendationPool));
                });
                CompletableFuture<Void> all = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                CompletableFuture<List<List<BudgetRecommendationDetails>>> results = all.thenApply(v -> futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
                results.get().stream().filter(Objects::nonNull).forEach(detailsList::addAll);
            } catch (Exception e) {
                log.error("getMultiShopBudgetRecommendation error", e);
                throw new BizServiceException("查询建议预算异常，请联系管理员");
            }
            builder.addAllData(detailsList);
            builder.setCode(Result.SUCCESS);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 批量添加多个广告活动的否定关键词投放
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void batchAddNeKeywords(AddNeKeywordsRequest request, StreamObserver<BatchNeKeywordResponse> responseObserver) {
        log.info("批量添加多个广告活动的否定关键词投放 request {}", request);
        BatchNeKeywordResponse.Builder builder = BatchNeKeywordResponse.newBuilder();
        com.meiyunji.sponsored.service.cpc.vo.AddCampaignNeKeywordsVo kvo = new com.meiyunji.sponsored.service.cpc.vo.AddCampaignNeKeywordsVo();
        kvo.setPuid(request.getPuid().getValue());
        kvo.setShopId(request.getShopId().getValue());
        kvo.setUid(request.getUid().getValue());
        kvo.setLoginIp(request.getLoginIp());
        //处理集合
        List<NeKeywordsVo> neKeywordsList = request.getNeKeywordsList();
        List<com.meiyunji.sponsored.service.cpc.vo.NeKeywordsVo> kList = Lists.newArrayList();
        neKeywordsList.stream().forEach(item -> {
            com.meiyunji.sponsored.service.cpc.vo.NeKeywordsVo vo = new com.meiyunji.sponsored.service.cpc.vo.NeKeywordsVo();
            vo.setIndex(item.getIndex());
            vo.setKeywordText(item.getKeywordText());
            vo.setMatchType(item.getMatchType());
            vo.setCampaignId(item.getCampaignId());
            kList.add(vo);
        });
        kvo.setNeKeywords(kList);

        if (checkNeKeywordVo(kvo)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result<BatchDataResponse> result = cpcSpCampaignService.batchAddNeKeywords(kvo, request.getLoginIp());
            // 错误信息返回：code = error返回给result.msg; code = success返回给data里的failmsg
            builder.setCode(Int32Value.of(result.getCode()));
            if (result.error()) {
                builder.setMsg(result.getMsg());
            }
            if (result.getData() != null) {
                builder.setData(result.getData());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 批量添加多个广告活动的否定商品投放
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void batchAddNeTargets(CreateNetargetingRequest request, StreamObserver<BatchNetargetingResponse> responseObserver) {
        log.info("批量添加多个广告活动的否定商品投放 request {}", request);
        BatchNetargetingResponse.Builder builder = BatchNetargetingResponse.newBuilder();
        if (!request.hasShopId() || !request.hasPuid() || !request.hasUid() || CollectionUtils.isEmpty(request.getAsinListList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            List<asinVo> asinListList = request.getAsinListList();
            List<NeTargetingVo> neTargetingVoList = asinListList.stream().filter(Objects::nonNull).map(item -> {
                NeTargetingVo neTargetingVo = new NeTargetingVo();
                BeanUtils.copyProperties(item, neTargetingVo);
                return neTargetingVo;
            }).collect(Collectors.toList());
            //处理业务返回结果
            Result<BatchDataResponse> result = cpcSpCampaignService.batchAddNeTargets(CampaignNeTargetingSpAddParam.builder()
                .puid(request.getPuid().getValue())
                .shopId(request.getShopId().getValue())
                .uid(request.getUid().getValue())
                .loginIp(request.getLoginIp())
                .asinList(neTargetingVoList)
                .build());
            // 错误信息返回：code = error返回给result.msg; code = success返回给data里的failmsg
            builder.setCode(Int32Value.of(result.getCode()));
            if (result.error()) {
                builder.setMsg(result.getMsg());
            }
            if (result.getData() != null) {
                builder.setData(result.getData());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private boolean checkNeKeywordVo(AddCampaignNeKeywordsVo keywordsVo) {
        if (!CheckParamUtil.checkRequired(keywordsVo, false, "puid, uid, shopId")) {
            return true;
        }
        List<com.meiyunji.sponsored.service.cpc.vo.NeKeywordsVo> list = keywordsVo.getNeKeywords();
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }
        for (com.meiyunji.sponsored.service.cpc.vo.NeKeywordsVo vo : list) {
            if (!CheckParamUtil.checkRequired(vo, false, "index, campaignId, keywordText, matchType")) {
                return true;
            }
        }
        return false;
    }

    /**
     * 单店铺获取建议预算
     */
    private List<BudgetRecommendationDetails> getBudgetRecommendationDetailsList(Integer puid, Integer uid, Integer shopId, List<String> campaignIds) {
        List<BudgetRecommendationDetails> detailsList = Lists.newArrayList();
        Result result = cpcSpCampaignService.getBudgetRecommendation(puid, shopId, campaignIds);
        if (result.success()) {
            BudgetRecommendationsResponse budgetRecommendationsResponse = (BudgetRecommendationsResponse)result.getData();
            if (budgetRecommendationsResponse != null && CollectionUtils.isNotEmpty(budgetRecommendationsResponse.getSuccessResults())) {
                // 接口调用成功同步数据
                Map<String, AmazonAdMissBudget> missBudgetMap = amazonAdMissBudgetService.listByCampaignIds(puid, shopId, campaignIds).stream()
                    .filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdMissBudget::getCampaignId, Function.identity()));
                List<AmazonAdMissBudget> updateList = Lists.newArrayList();
                List<AmazonAdMissBudget> newList = Lists.newArrayList();
                AmazonAdMissBudget budget;
                for (BudgetRecommendationsSuccessResult succ : budgetRecommendationsResponse.getSuccessResults()) {
                    // 更新
                    if (missBudgetMap != null && missBudgetMap.get(succ.getCampaignId()) != null) {
                        budget = missBudgetMap.get(succ.getCampaignId());
                        budget.setUpdateId(uid);
                        updateList.add(budget);
                    } else {
                        // 新增
                        budget = new AmazonAdMissBudget();
                        budget.setCreateId(uid);
                        newList.add(budget);
                    }
                    budget.setPuid(puid);
                    budget.setShopId(shopId);
                    budget.setCampaignId(succ.getCampaignId());

                    BudgetRecommendationDetails.Builder succBuilder = BudgetRecommendationDetails.newBuilder();
                    succBuilder.setCampaignId(succ.getCampaignId());
                    succBuilder.setIndex(succ.getIndex());
                    if (succ.getSuggestedBudget() != null) {
                        succBuilder.setSuggestedBudget(succ.getSuggestedBudget());
                        budget.setSuggestedBudget(BigDecimal.valueOf(succ.getSuggestedBudget()));
                    }
                    if (succ.getSevenDaysMissedOpportunities() != null) {
                        SevenDaysMissedOpportunities opportunities = succ.getSevenDaysMissedOpportunities();
                        if (opportunities.getEstimatedMissedSalesLower() != null) {
                            succBuilder.setEstimatedMissedSalesLower(opportunities.getEstimatedMissedSalesLower());
                            budget.setEstimatedMissedSalesLower(BigDecimal.valueOf(opportunities.getEstimatedMissedSalesLower()));
                        }
                        if (opportunities.getEstimatedMissedSalesUpper() != null) {
                            succBuilder.setEstimatedMissedSalesUpper(opportunities.getEstimatedMissedSalesUpper());
                            budget.setEstimatedMissedSalesUpper(BigDecimal.valueOf(opportunities.getEstimatedMissedSalesUpper()));
                        }
                        if (opportunities.getEstimatedMissedImpressionsLower() != null) {
                            succBuilder.setEstimatedMissedImpressionsLower(opportunities.getEstimatedMissedImpressionsLower());
                            budget.setEstimatedMissedImpressionsLower(opportunities.getEstimatedMissedImpressionsLower());
                        }
                        if (opportunities.getEstimatedMissedImpressionsUpper() != null) {
                            succBuilder.setEstimatedMissedImpressionsUpper(opportunities.getEstimatedMissedImpressionsUpper());
                            budget.setEstimatedMissedImpressionsUpper(opportunities.getEstimatedMissedImpressionsUpper());
                        }
                        if (opportunities.getEstimatedMissedClicksLower() != null) {
                            succBuilder.setEstimatedMissedClicksLower(opportunities.getEstimatedMissedClicksLower());
                            budget.setEstimatedMissedClicksLower(opportunities.getEstimatedMissedClicksLower());
                        }
                        if (opportunities.getEstimatedMissedClicksUpper() != null) {
                            succBuilder.setEstimatedMissedClicksUpper(opportunities.getEstimatedMissedClicksUpper());
                            budget.setEstimatedMissedClicksUpper(opportunities.getEstimatedMissedClicksUpper());
                        }
                        if (opportunities.getPercentTimeInBudget() != null) {
                            succBuilder.setPercentTimeInBudget(opportunities.getPercentTimeInBudget());
                            budget.setPercentTimeInBudget(BigDecimal.valueOf(opportunities.getPercentTimeInBudget()));
                        }
                        if (StringUtils.isNotBlank(opportunities.getStartDate())) {
                            succBuilder.setStartDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(opportunities.getStartDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
                            budget.setStartDate(opportunities.getStartDate());
                        }
                        if (StringUtils.isNotBlank(opportunities.getEndDate())) {
                            succBuilder.setEndDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(opportunities.getEndDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
                            budget.setEndDate(opportunities.getEndDate());
                        }
                    }
                    if (succ.getBudgetRuleRecommendation() != null) {
                        BudgetRuleRecommendation ruleRecommendation = succ.getBudgetRuleRecommendation();
                        if (StringUtils.isNotBlank(ruleRecommendation.getRuleId())) {
                            succBuilder.setRuleId(ruleRecommendation.getRuleId());
                            budget.setRuleId(ruleRecommendation.getRuleId());
                        }
                        if (StringUtils.isNotBlank(ruleRecommendation.getRuleName())) {
                            succBuilder.setRuleName(ruleRecommendation.getRuleName());
                            budget.setRuleName(ruleRecommendation.getRuleName());
                        }
                        if (ruleRecommendation.getSuggestedBudgetIncreasePercent() != null) {
                            succBuilder.setSuggestedBudgetIncreasePercent(ruleRecommendation.getSuggestedBudgetIncreasePercent());
                            budget.setSuggestedBudgetIncreasePercent(BigDecimal.valueOf(ruleRecommendation.getSuggestedBudgetIncreasePercent()));
                        }
                    }
                    detailsList.add(succBuilder.build());
                }
                // 同步数据
                try {
                    amazonAdMissBudgetService.batchAdd(puid, newList);
                    amazonAdMissBudgetService.batchUpdate(puid, updateList);
                } catch (Exception e) {
                    log.error("同步失败 失败原因:{}", e);
                }
            }
            if (budgetRecommendationsResponse != null && CollectionUtils.isNotEmpty(budgetRecommendationsResponse.getErrorResults())) {
                budgetRecommendationsResponse.getErrorResults().stream().filter(e -> e != null && e.getCampaignId() != null && e.getIndex() != null).forEach(fail -> {
                    BudgetRecommendationDetails.Builder failBuilder = BudgetRecommendationDetails.newBuilder();
                    failBuilder.setCampaignId(fail.getCampaignId());
                    failBuilder.setIndex(fail.getIndex());
                    if (fail.getError() != null) {
                        failBuilder.setCode(fail.getError().getCode());
                        failBuilder.setDetails(fail.getError().getDetails());
                    }
                    detailsList.add(failBuilder.build());
                });
            }
        }
        return detailsList;
    }
}
