package com.meiyunji.sponsored.api.sd;

import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.rpc.sd.audiences.AudiencesFilterListResponse;
import com.meiyunji.sponsored.rpc.sd.audiences.AudiencesListRequest;
import com.meiyunji.sponsored.rpc.sd.audiences.AudiencesListResponse;
import com.meiyunji.sponsored.rpc.sd.audiences.RPCSdAudiencesServiceGrpc;
import com.meiyunji.sponsored.rpc.sd.targeting.SuggestSdAudiencesResponse;
import com.meiyunji.sponsored.rpc.vo.AudiencesFilterListRpcVo;
import com.meiyunji.sponsored.rpc.vo.AudiencesListRpcVo;
import com.meiyunji.sponsored.rpc.vo.SuggestAudiencesRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.service2.sd.ICpcSdAudiencesService;
import com.meiyunji.sponsored.service.cpc.vo.SuggestAudiencesVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2024/8/6 15:34
 * @describe:
 */
@GRpcService
@Slf4j
public class SdAudienceRpcService extends RPCSdAudiencesServiceGrpc.RPCSdAudiencesServiceImplBase {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private ICpcSdAudiencesService cpcSdAudiencesService;

    @Override
    public void getTaxonomyList(AudiencesListRequest request, StreamObserver<AudiencesListResponse> responseObserver) {
        log.info("sd-suggestAudiencesNew 建议受众 request {}", request);
        AudiencesListResponse.Builder builder = AudiencesListResponse.newBuilder();
        if (request.getShopId() == 0 || request.getPuid() == 0) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId(), request.getPuid());
            if (shop == null) {
                throw new ServiceException("没有CPC授权");
            }
            AudiencesListRpcVo res = cpcSdAudiencesService.getAudienceTaxonomyList(request.getPuid(),
                    shop, request.getCategoryPathList());
            builder.setCode(Int32Value.of(Result.SUCCESS));
            if (CollectionUtils.isEmpty(res.getCategoryPathList()) && CollectionUtils.isEmpty(res.getCategoriesList())) {
                builder.setData(AudiencesListRpcVo.newBuilder().build());
            } else {
                builder.setData(res);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAudiencesFilterList(AudiencesListRequest request, StreamObserver<AudiencesFilterListResponse> responseObserver) {
        log.info("sd-getAudiencesFilterList 受众详细信息 request {}", request);
        AudiencesFilterListResponse.Builder builder = AudiencesFilterListResponse.newBuilder();
        if (request.getShopId() == 0 || request.getPuid() == 0) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId(), request.getPuid());
            if (shop == null) {
                throw new ServiceException("没有CPC授权");
            }
            List<AudiencesFilterListRpcVo> res = cpcSdAudiencesService.getAudienceFilterList(request.getPuid(), shop,
                    request.getCategoryPathList(), request.getAudienceName());
            builder.setCode(Int32Value.of(Result.SUCCESS));
            if (CollectionUtils.isEmpty(res)) {
                builder.addAllData(Collections.emptyList());
            } else {
                builder.addAllData(res);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
