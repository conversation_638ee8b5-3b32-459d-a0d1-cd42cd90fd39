package com.meiyunji.sponsored.api.sd;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.adCommon.*;
import com.meiyunji.sponsored.rpc.sd.campaign.TargetInfoResponse;
import com.meiyunji.sponsored.rpc.sd.campaign.TargetResponse;
import com.meiyunji.sponsored.rpc.sd.targeting.*;
import com.meiyunji.sponsored.rpc.vo.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetObjectTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskConstant;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskMatchTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetTaskDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.TargetTypeEnum;
import com.meiyunji.sponsored.service.cpc.service2.IAdTargetTaskService;
import com.meiyunji.sponsored.service.cpc.service2.sd.ICpcSdTargetingService;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.enums.StateEnum;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2021/10/28 18:49
 * @describe: sd投放rpc接口
 */
@GRpcService
@Slf4j
public class TargetingSdRpcService extends RPCSdTargetingServiceGrpc.RPCSdTargetingServiceImplBase {

    @Autowired
    private ICpcSdTargetingService cpcSdTargetingService;
    @Autowired
    private IAdTargetTaskService adTargetTaskService;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;

    @Override
    public void showAdPerformanceVo(CommonShowAdPerformanceRequest request, StreamObserver<CommonShowAdPerformanceResponse> responseObserver) {

        CommonShowAdPerformanceResponse.Builder builder = CommonShowAdPerformanceResponse.newBuilder();
        //做参数校验
        if (!request.hasShopId()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");

        } else {
            AdPerformanceParam param = new AdPerformanceParam();
            param.setShopId(request.getShopId());
            param.setPuid(request.getPuid());
            param.setCampaignId(request.getCampaignId());
            param.setGroupId(request.getGroupId());
            param.setKeywordId(request.getKeywordId());
            param.setTargetId(request.getTargetId());
            param.setCpcProductId(request.getCpcProductId());
            param.setQuery(request.getQuery());
            param.setPlacement(request.getPlacement());
            param.setStartDate(request.getStartDate());
            param.setEndDate(request.getEndDate());

            if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
                param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
                param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            } else {
                param.setStartDate(param.getStartDate().replace("-", ""));
                param.setEndDate(param.getEndDate().replace("-", ""));
            }
            //处理业务返回结果
            Result<AdPerformanceVo> res = cpcSdTargetingService.showTargetPerformance(request.getPuid(), param);
            builder.setCode(res.getCode());
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                //处理data
                AdPerformanceVo data = res.getData();
                if (data != null) {
                    AdPerformanceRpcVo.Builder voBuilder = AdPerformanceRpcVo.newBuilder();
                    if (data.getShopId() != null) {
                        voBuilder.setShopId(data.getShopId());
                    }
                    if (data.getCampaignId() != null) {
                        voBuilder.setCampaignId(data.getCampaignId());
                    }
                    if (data.getGroupId() != null) {
                        voBuilder.setGroupId(data.getGroupId());
                    }
                    if (data.getKeywordId() != null) {
                        voBuilder.setKeywordId(data.getKeywordId());
                    }
                    if (data.getTargetId() != null) {
                        voBuilder.setTargetId(data.getTargetId());
                    }
                    if (data.getAdId() != null) {
                        voBuilder.setAdId(data.getAdId());
                    }
                    if (data.getQuery() != null) {
                        voBuilder.setQuery(data.getQuery());
                    }
                    if (data.getPlacement() != null) {
                        voBuilder.setPlacement(data.getPlacement());
                    }
                    Map<String, CpcCommPageRpcVo> rpcVoMap = Maps.newHashMap();

                    if (MapUtils.isNotEmpty(data.getMap())) {
                        for (Map.Entry<String, CpcCommPageVo> entry : data.getMap().entrySet()) {
                            CpcCommPageVo vo = entry.getValue();
                            //vo转message
                            CpcCommPageRpcVo.Builder cpcRpcVo = CpcCommPageRpcVo.newBuilder();
                            cpcRpcVo.setImpressions(Optional.ofNullable(vo.getImpressions()).orElse(0));
                            cpcRpcVo.setClicks(Optional.ofNullable(vo.getClicks()).orElse(0));
                            cpcRpcVo.setCtr(StringUtils.isNotBlank(vo.getCtr()) ? vo.getCtr() : "0");
                            cpcRpcVo.setCvr(StringUtils.isNotBlank(vo.getCvr()) ? vo.getCvr() : "0");
                            cpcRpcVo.setAcos(StringUtils.isNotBlank(vo.getAcos()) ? vo.getAcos() : "0");
                            cpcRpcVo.setRoas(StringUtils.isNotBlank(vo.getRoas()) ? vo.getRoas() : "0");
                            cpcRpcVo.setAcots(StringUtils.isNotBlank(vo.getAcots()) ? vo.getAcots() : "0");
                            cpcRpcVo.setAdOrderNum(Optional.ofNullable(vo.getAdOrderNum()).orElse(0));
                            cpcRpcVo.setAdCost(StringUtils.isNotBlank(vo.getAdCost()) ? vo.getAdCost() : "0");
                            cpcRpcVo.setAdCostPerClick(StringUtils.isNotBlank(vo.getAdCostPerClick()) ? vo.getAdCostPerClick() : "0");
                            cpcRpcVo.setAdSale(StringUtils.isNotBlank(vo.getAdSale()) ? vo.getAdSale() : "0");

                            rpcVoMap.put(entry.getKey(), cpcRpcVo.build());
                        }

                        voBuilder.putAllMap(rpcVoMap);
                    }
                    builder.setData(voBuilder.build());
                }
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 创建投放
     * @param request
     * @param responseObserver
     */
    @Override
    public void create(CreateSdTargetingRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sd-targeting创建 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (StringUtils.isBlank(request.getGroupId())
                || !request.hasPuid() || !request.hasShopId() || !request.hasUid()
                || CollectionUtils.isEmpty(request.getTargetingList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            AddSdTargetingVo vo = new AddSdTargetingVo();
            vo.setGroupId(request.getGroupId());
            vo.setPuid(request.getPuid().getValue());
            vo.setUid(request.getUid().getValue());
            vo.setLoginIp(request.getLoginIp());
            vo.setShopId(request.getShopId().getValue());
            //处理集合
            List<SdTargetingVo> vos = request.getTargetingList().stream().filter(Objects::nonNull).map(item -> {
                SdTargetingVo tvo = new SdTargetingVo();
                BeanUtils.copyProperties(item, tvo);
                if (item.hasPrimeShippingEligible()) {
                    tvo.setPrimeShippingEligible(item.getPrimeShippingEligible().getValue());
                }
                if (item.hasMinReviewRating()) {
                    tvo.setMinReviewRating(item.getMinReviewRating().getValue());
                }
                if (item.hasMaxReviewRating()) {
                    tvo.setMaxReviewRating(item.getMaxReviewRating().getValue());
                }
                return tvo;
            }).collect(Collectors.toList());

            vo.setTargetings(vos);

            //处理业务返回结果
            Result res = cpcSdTargetingService.createTargeting(vo);
            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void createNew(CreateSdTargetingRequest request, StreamObserver<CreateSdTargetingResponse> responseObserver) {
        log.info("sd-targeting-new 创建 request {}", request);
        CreateSdTargetingResponse.Builder builder = CreateSdTargetingResponse.newBuilder();

        if (StringUtils.isBlank(request.getGroupId())
                || !request.hasPuid() || !request.hasShopId() || !request.hasUid()
                || CollectionUtils.isEmpty(request.getTargetingList())) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            AddSdTargetingVo vo = new AddSdTargetingVo();
            vo.setGroupId(request.getGroupId());
            vo.setPuid(request.getPuid().getValue());
            vo.setUid(request.getUid().getValue());
            vo.setLoginIp(request.getLoginIp());
            vo.setShopId(request.getShopId().getValue());
            //处理集合
            List<SdTargetingVo> vos = request.getTargetingList().stream().filter(Objects::nonNull).map(item -> {
                SdTargetingVo tvo = new SdTargetingVo();
                BeanUtils.copyProperties(item, tvo);
                if (item.hasPrimeShippingEligible()) {
                    tvo.setPrimeShippingEligible(item.getPrimeShippingEligible().getValue());
                }
                if (item.hasMinReviewRating()) {
                    tvo.setMinReviewRating(item.getMinReviewRating().getValue());
                }
                if (item.hasMaxReviewRating()) {
                    tvo.setMaxReviewRating(item.getMaxReviewRating().getValue());
                }
                return tvo;
            }).collect(Collectors.toList());

            vo.setTargetings(vos);
            TargetResponse.Builder targetBuilder = TargetResponse.newBuilder();

            if (CollectionUtils.isNotEmpty(vo.getTargetings())) {
                try {
                    //处理业务返回结果
                    NewCreateResultResultVo<SBCommonErrorVo> res = cpcSdTargetingService.
                            createTargetingNew(vo, null, null, null, vo.getUid(), vo.getLoginIp());
                    if (Objects.nonNull(res) && CollectionUtils.isNotEmpty(res.getTargetingList())) {
                        List<TargetInfoResponse> targetingList = res.getTargetingList().stream().map(k -> {
                            TargetInfoResponse.Builder t = TargetInfoResponse.newBuilder();
                            Optional.ofNullable(k.getTargetId()).ifPresent(t::setTargetId);
                            Optional.ofNullable(k.getTargetText()).ifPresent(t::setTargetText);
                            Optional.ofNullable(k.getMatchType()).ifPresent(t::setMatchType);
                            return t.build();
                        }).collect(Collectors.toList());
                        targetBuilder.addAllTargetList(targetingList);
                    }
                    if (Objects.nonNull(res) && CollectionUtils.isNotEmpty(res.getErrInfoList())) {
                        targetBuilder.setTargetErrMsg(JSONObject.toJSONString(res.getErrInfoList()));
                    }
                } catch (Exception e) {
                    targetBuilder.setTargetErrMsg(SDCommonErrorVo.getErrorListByRaw(e.getMessage()));
                    builder.setData(targetBuilder.build());
                    builder.setCode(Result.ERROR);
                    builder.setMsg("请求参数错误");
                    responseObserver.onNext(builder.build());
                    responseObserver.onCompleted();
                    return;
                }
                builder.setData(targetBuilder.build());
                builder.setCode(Result.SUCCESS);
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
            }
        }

    }

    @Override
    public void asyncCreate(AsyncAddTargetingRequest request, StreamObserver<com.meiyunji.sponsored.rpc.vo.CommonResponse> responseObserver) {
        log.info("sd-async-targeting-创建产品投放 request {}", request);
        com.meiyunji.sponsored.rpc.vo.CommonResponse.Builder builder = com.meiyunji.sponsored.rpc.vo.CommonResponse.newBuilder();
        String error = verifyAsyncParam(request);
        if (StringUtils.isNotBlank(error)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(error);
        } else {
            AdTargetTaskDto adTargetTaskDto = new AdTargetTaskDto();
            adTargetTaskDto.setPuid(request.getPuid().getValue());
            adTargetTaskDto.setShopId(request.getShopId().getValue());
            adTargetTaskDto.setUid(request.getUid().getValue());
            adTargetTaskDto.setType(AdTargetTaskTypeEnum.SD_TARGETS.getCode());
            adTargetTaskDto.setLoginIp(request.getLoginIp());
            adTargetTaskDto.setTargetingType(request.getTargetingType());
            adTargetTaskDto.setTargetPageType(request.getTargetingPageType().getValue());
            adTargetTaskDto.setSourceAdCampaignId(request.getSourceAdCampaignId());
            adTargetTaskDto.setSourceShopId(request.getSourceShopId().getValue());
            List<AdTargetTaskDto.AdTargetTaskDetailDto> detailList = request.getTargetingList().stream().filter(Objects::nonNull).map(item -> {
                AdTargetTaskDto.AdTargetTaskDetailDto detail = new AdTargetTaskDto.AdTargetTaskDetailDto();
                if (item.hasSourceShopId()) {
                    detail.setSourceShopId(item.getSourceShopId().getValue());
                }  else {
                    detail.setSourceShopId(request.getSourceShopId().getValue());
                }
                detail.setAdCampaignId(item.getAdCampaignId());
                detail.setAdGroupId(item.getAdGroupId());
                detail.setMatchType(item.getExpressionType());
                detail.setRangeEnd(StringUtils.isBlank(item.getRangeEnd()) ? null : new BigDecimal(item.getRangeEnd()));
                detail.setRangeStart(StringUtils.isBlank(item.getRangeStart()) ? null : new BigDecimal(item.getRangeStart()));
                detail.setSuggested(StringUtils.isBlank(item.getSuggested()) ? null : new BigDecimal(item.getSuggested()));
                detail.setBid(new BigDecimal(item.getBid()));
                if (StringUtils.isNotBlank(item.getTargetId())) {
                    detail.setTargetId(item.getTargetId());
                } else {
                    detail.setTargetObject(item.getAsin());
                    detail.setTargetObjectDesc(item.getTitle());
                    detail.setImgUrl(item.getImgUrl());
                }
                detail.setTargetObjectType(AdTargetObjectTypeEnum.getCodeByTargetType(item.getType()));
                return detail;
            }).collect(Collectors.toList());
            adTargetTaskDto.setTaskDetails(detailList);
            long taskId = adTargetTaskService.recordTargetTask(adTargetTaskDto);
            adTargetTaskService.executeTask(taskId);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private String verifyAsyncParam(AsyncAddTargetingRequest request) {
        if (!request.hasShopId() || !request.hasPuid() || !request.hasTargetingPageType()
                || CollectionUtils.isEmpty(request.getTargetingList()) || request.getTargetingList().size() > AdTargetTaskConstant.MAX_TARGET_SIZE) {
            return "请求参数错误";
        }
        List<AsyncAddTargetingRpcVo> list = request.getTargetingList();
        // 如果第一个包含targetId,那么列表中所有的对象都需要包含targetId
        int targetIdNum = 0;
        for (AsyncAddTargetingRpcVo each : list) {
            boolean haveTargetId = StringUtils.isNotBlank(each.getTargetId());
            if (haveTargetId) {
                targetIdNum++;
            }
            if (!AdTargetTaskMatchTypeEnum.SD_TARGET_SUPPORT_TYPES.contains(each.getExpressionType())) {
                return "请求参数错误,存在不支持的筛选条件";
            }
            if (!AdTargetObjectTypeEnum.TARGET_SUPPORT_TYPES.contains(each.getType())) {
                return "请求参数错误,存在不支持的投放类型";
            }
            if (TargetTypeEnum.category.name().equals(each.getType()) && !haveTargetId) {
                return "请求参数错误";
            }
            if (TargetTypeEnum.asin.name().equals(each.getType()) && !haveTargetId && StringUtils.isBlank(each.getAsin())) {
                return "请求参数错误";
            }
        }
        if (targetIdNum > 0 && targetIdNum < list.size() || (!request.hasSourceShopId() && !request.getTargetingList().get(0).hasSourceShopId()) || StringUtils.isBlank(request.getTargetingType())) {
            return "请求参数错误";
        }
        return StringUtils.EMPTY;
    }

    /**
     * 更新状态
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void update(UpdateSdTargetingRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sd-targeting更新状态 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (StringUtils.isBlank(request.getTargetId())
                || !request.hasPuid() || !request.hasShopId() || !request.hasUid()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else if (StringUtils.isNotBlank(request.getState()) && StringUtils.isBlank(StateEnum.getStateValue(request.getState()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            UpdateSdTargetingVo vo = new UpdateSdTargetingVo();
            vo.setPuid(request.getPuid().getValue());
            vo.setShopId(request.getShopId().getValue());
            vo.setUid(request.getUid().getValue());
            vo.setTargetId(request.getTargetId());
            vo.setIp(request.getLoginIp());
            if (StringUtils.isNotBlank(request.getBid())) {
                vo.setBid(request.getBid());
            }
            if (StringUtils.isNotBlank(request.getState())) {
                vo.setState(request.getState());
            }
            //处理业务返回结果
            Result res = cpcSdTargetingService.update(vo);
            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 归档
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void archive(ArchiveSdTargetingRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sd-targeting归档 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (StringUtils.isBlank(request.getTargetId())
                || !request.hasPuid() || !request.hasShopId() || !request.hasUid()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        }  else {
            Result res = cpcSdTargetingService.archive(request.getPuid().getValue(), request.getShopId().getValue(), request.getUid().getValue(), request.getTargetId(), request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    /**
     * 建议类型
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void suggestCategory(SuggestSdCategoryRequest request, StreamObserver<SuggestSdCategoryResponse> responseObserver) {
        log.info("sd-targeting建议类型 request {}", request);
        SuggestSdCategoryResponse.Builder builder = SuggestSdCategoryResponse.newBuilder();
        if (StringUtils.isBlank(request.getGroupId())
                || !request.hasShopId() || !request.hasPuid()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            Result<List<SuggestCategoryVo>> res = cpcSdTargetingService.suggestCategory(request.getPuid().getValue(), request.getShopId().getValue(), request.getGroupId());
            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                //处理list
                List<SuggestCategoryVo> data = res.getData();
                List<SuggestCategoryRpcVo> rpcVos = data.stream().filter(Objects::nonNull).map(item -> {
                    SuggestCategoryRpcVo.Builder voBuilder = SuggestCategoryRpcVo.newBuilder();
                    if (item.getId() != null) {
                        voBuilder.setId(item.getId());
                    }
                    if (item.getName() != null) {
                        voBuilder.setName(item.getName());
                    }
                    if (item.getPath() != null) {
                        voBuilder.setPath(item.getPath());
                    }
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void suggestCategoryNew(SuggestSdCategoryRequestNew request, StreamObserver<SuggestSdCategoryResponse> responseObserver) {
        log.debug("sd-suggestCategoryNew 建议类目 request {}", request);
        SuggestSdCategoryResponse.Builder builder = SuggestSdCategoryResponse.newBuilder();
        if (CollectionUtils.isEmpty(request.getAsinsList())
                || !request.hasShopId() || !request.hasPuid()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId().getValue(), request.getPuid().getValue());
            if (shop == null) {
                throw new ServiceException("没有CPC授权");
            }
            Result<List<SuggestCategoryVo>> res = cpcSdTargetingService.suggestCategoryNew(request.getPuid().getValue(), shop, request.getAsinsList(), "");
            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                //处理list
                List<SuggestCategoryVo> data = res.getData();
                List<SuggestCategoryRpcVo> rpcVos = data.stream().filter(Objects::nonNull).map(item -> {
                    SuggestCategoryRpcVo.Builder voBuilder = SuggestCategoryRpcVo.newBuilder();
                    if (item.getId() != null) {
                        voBuilder.setId(item.getId());
                    }
                    if (item.getName() != null) {
                        voBuilder.setName(item.getName());
                    }
                    if (item.getPath() != null) {
                        voBuilder.setPath(item.getPath());
                    }
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void suggestProductNew(SuggestSdCategoryRequestNew request, StreamObserver<SuggestSdProductResponse> responseObserver) {
        log.debug("sd-suggestProductNew 建议产品 request {}", request);
        SuggestSdProductResponse.Builder builder = SuggestSdProductResponse.newBuilder();
        if (CollectionUtils.isEmpty(request.getAsinsList())
                || !request.hasShopId() || !request.hasPuid()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId().getValue(), request.getPuid().getValue());
            if (shop == null) {
                throw new ServiceException("没有CPC授权");
            }
            Result<List<SuggestProductVo>> res = cpcSdTargetingService.suggestProductNew(request.getPuid().getValue(), shop, request.getAsinsList());
            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                //处理list
                List<SuggestProductVo> data = res.getData();
                List<SuggestProductRpcVo> rpcVos = data.stream().filter(Objects::nonNull).map(item -> {
                    SuggestProductRpcVo.Builder voBuilder = SuggestProductRpcVo.newBuilder();
                    Optional.ofNullable(item.getAsin()).ifPresent(voBuilder::setAsin);
                    Optional.ofNullable(item.getTitle()).ifPresent(voBuilder::setTitle);
                    Optional.ofNullable(item.getImgUrl()).ifPresent(voBuilder::setImgUrl);
                    Optional.ofNullable(item.getAdvertisedAsins()).filter(CollectionUtils::isNotEmpty).ifPresent(voBuilder::addAllAdvertisedAsins);
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
    }

    @Override
    public void suggestAudiencesNew(SuggestSdAudiencesRequestNew request, StreamObserver<SuggestSdAudiencesResponse> responseObserver) {
        log.debug("sd-suggestAudiencesNew 建议受众 request {}", request);
        SuggestSdAudiencesResponse.Builder builder = SuggestSdAudiencesResponse.newBuilder();
        if (CollectionUtils.isEmpty(request.getAsinsList()) || !request.hasShopId() || !request.hasPuid()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId().getValue(), request.getPuid().getValue());
            if (shop == null) {
                throw new ServiceException("没有CPC授权");
            }
            Result<List<SuggestAudiencesVo>> res = cpcSdTargetingService.suggestAudiences(request.getPuid().getValue(), shop, request.getAsinsList());
            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                //处理list
                List<SuggestAudiencesVo> data = res.getData();
                List<SuggestAudiencesRpcVo> rpcVos = data.stream().filter(Objects::nonNull).map(item -> {
                    SuggestAudiencesRpcVo.Builder voBuilder = SuggestAudiencesRpcVo.newBuilder();
                    Optional.ofNullable(item.getCategory()).ifPresent(voBuilder::setCategory);
                    Optional.ofNullable(item.getAudiences()).filter(CollectionUtils::isNotEmpty).map(audienceInfo ->
                            audienceInfo.stream().map(a -> {
                                SuggestAudiencesRpcVo.SDAudienceRecommendation.Builder audience = SuggestAudiencesRpcVo.SDAudienceRecommendation.newBuilder();
                                audience.setAudienceId(a.getAudienceId());
                                audience.setName(a.getName());
                                audience.setRank(a.getRank());
                                return audience.build();
                            }).collect(Collectors.toList())).ifPresent(voBuilder::addAllAudiences);
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 建议竞价
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getSuggestedBid(SuggestedSdBidRequest request, StreamObserver<SuggestedSdBidResponse> responseObserver) {
        log.info("sd-targeting建议竞价 request {}", request);
        SuggestedSdBidResponse.Builder builder = SuggestedSdBidResponse.newBuilder();
        if (StringUtils.isBlank(request.getTargetIds())
                || !request.hasShopId() || !request.hasPuid()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            Result<List<SdSuggestedTargetVo>> res = cpcSdTargetingService.getSuggestedBid(request.getPuid().getValue(), request.getShopId().getValue(), Lists.newArrayList(request.getTargetIds().split(",")));
            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
            //处理集合
            if (res.success()) {
                List<SdSuggestedTargetRpcVo> rpcVos = res.getData().stream().filter(Objects::nonNull).map(item -> {
                    SdSuggestedTargetRpcVo.Builder voBuilder = SdSuggestedTargetRpcVo.newBuilder();
                    if (item.getTargetId() != null) {
                        voBuilder.setTargetId(item.getTargetId());
                    }
                    if (item.getSuggested() != null) {
                        voBuilder.setSuggested(item.getSuggested());
                    }
                    if (item.getRangeEnd() != null) {
                        voBuilder.setRangeEnd(item.getRangeEnd());
                    }
                    if (item.getRangeStart() != null) {
                        voBuilder.setRangeStart(item.getRangeStart());
                    }
                    if (CollectionUtils.isNotEmpty(item.getExpression())) {
                        List<SdSuggestedTargetRpcVo.ExpressionNested> expression = item.getExpression().stream().map(innerItem -> {
                            SdSuggestedTargetRpcVo.ExpressionNested.Builder innerBuilder = SdSuggestedTargetRpcVo.ExpressionNested.newBuilder();
                            innerBuilder.setType(innerItem.getType());
                            innerBuilder.setValue(String.valueOf(innerItem.getValue()));
                            return innerBuilder.build();
                        }).collect(Collectors.toList());
                        voBuilder.addAllExpression(expression);
                    }
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getSuggestedBidNew(SuggestedSdBidNewRequest request, StreamObserver<SuggestedSdBidNewResponse> responseObserver) {
        log.debug("sd-targeting建议竞价 request {}", request);
        SuggestedSdBidNewResponse.Builder builder = SuggestedSdBidNewResponse.newBuilder();
        if (!request.hasShopId() || !request.hasPuid() || CollectionUtils.isEmpty(request.getAsinsList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId().getValue(), request.getPuid().getValue());
            if (Objects.isNull(shopAuth)) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("店铺为授权");
            }
            AmazonAdProfile profile = amazonAdProfileDao.getProfile(request.getPuid().getValue(), request.getShopId().getValue());
            if (profile == null) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("没有站点对应的配置信息");
            }
            List<SdTargetingVo> vos = request.getTargetingList().stream().filter(Objects::nonNull).map(item -> {
                SdTargetingVo tvo = new SdTargetingVo();
                BeanUtils.copyProperties(item, tvo);
                if (item.hasPrimeShippingEligible()) {
                    tvo.setPrimeShippingEligible(item.getPrimeShippingEligible().getValue());
                }
                if (item.hasMinReviewRating()) {
                    tvo.setMinReviewRating(item.getMinReviewRating().getValue());
                }
                if (item.hasMaxReviewRating()) {
                    tvo.setMaxReviewRating(item.getMaxReviewRating().getValue());
                }
                return tvo;
            }).collect(Collectors.toList());
            Result<List<SdSuggestedTargetVo>> res = cpcSdTargetingService.getSuggestedBidNew(request.getAsinsList(),
                    shopAuth, vos, profile, request.getBidOptimization(), request.getCreativeType());
            //拼装返回结果
            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
            //处理集合
            if (res.success()) {
                List<SdSuggestedTargetNewRpcVo> rpcVos = res.getData().stream().filter(Objects::nonNull).map(item -> {
                    SdSuggestedTargetNewRpcVo.Builder voBuilder = SdSuggestedTargetNewRpcVo.newBuilder();
                    List<SdTargetingRpcVo> requestList = request.getTargetingList();
                    if (CollectionUtils.isNotEmpty(requestList) && Objects.nonNull(requestList.get(item.getIndex()))) {
                        SdTargetingRpcVo reqVo = requestList.get(item.getIndex());
                        Optional.of(reqVo.getType()).filter(StringUtils::isNotEmpty).ifPresent(voBuilder::setType);
                        Optional.of(reqVo.getPredicate()).filter(StringUtils::isNotEmpty).ifPresent(voBuilder::setPredicate);
                    }
                    if (item.getTargetId() != null) {
                        voBuilder.setTargetId(item.getTargetId());
                    }
                    if (item.getTargetText() != null) {
                        voBuilder.setTargetText(item.getTargetText());
                    }
                    if (item.getIndex() != null) {
                        voBuilder.setIndex(item.getIndex());
                    }
                    if (item.getSuggested() != null) {
                        voBuilder.setSuggested(item.getSuggested());
                    }
                    if (item.getRangeEnd() != null) {
                        voBuilder.setRangeEnd(item.getRangeEnd());
                    }
                    if (item.getRangeStart() != null) {
                        voBuilder.setRangeStart(item.getRangeStart());
                    }
                    if (CollectionUtils.isNotEmpty(item.getExpression())) {
                        List<SdSuggestedTargetNewRpcVo.ExpressionNested> expression = item.getExpression().stream().map(innerItem -> {
                            SdSuggestedTargetNewRpcVo.ExpressionNested.Builder innerBuilder = SdSuggestedTargetNewRpcVo.ExpressionNested.newBuilder();
                            innerBuilder.setType(innerItem.getType());
                            innerBuilder.setValue(String.valueOf(innerItem.getValue()));
                            return innerBuilder.build();
                        }).collect(Collectors.toList());
                        voBuilder.addAllExpression(expression);
                    }
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void batchGetSuggestedBid(BatchTargetSuggestedBidRequest request, StreamObserver<SuggestedSdBidResponse> responseObserver) {
        log.info("sd-targeting建议竞价 request {}", request);
        SuggestedSdBidResponse.Builder builder = SuggestedSdBidResponse.newBuilder();

        if (!request.hasPuid() || (!request.hasSourceShopId() && !request.getTargetDetailList().get(0).hasSourceShopId()) || !request.hasShopId() || CollectionUtils.isEmpty(request.getTargetDetailList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            List<TargetSuggestedBidVo> requestDetails = request.getTargetDetailList();
            List<TargetSuggestedBidDetail> details = new ArrayList<>();
            for (int i = 0; i < requestDetails.size(); i++) {
                TargetSuggestedBidDetail targetSuggestedBidDetail = new TargetSuggestedBidDetail();
                BeanUtils.copyProperties(requestDetails.get(i), targetSuggestedBidDetail);
                targetSuggestedBidDetail.setIndex(i);
                targetSuggestedBidDetail.setSourceShopId(requestDetails.get(i).getSourceShopId().getValue());
                details.add(targetSuggestedBidDetail);
            }
            //兼容单店铺
            List<Integer> sourceShopIds = new ArrayList<>();
            if (details.get(0).getSourceShopId() != null && details.get(0).getSourceShopId() != 0) {
                sourceShopIds.addAll(details.stream().map(TargetSuggestedBidDetail::getSourceShopId).filter(e -> e != null && !e.equals(0)).distinct().collect(Collectors.toList()));
            } else {
                sourceShopIds.add(request.getSourceShopId().getValue());
            }
            Result<List<SdSuggestedTargetVo>> res = cpcSdTargetingService.batchGetSuggestedBid(
                    request.getPuid().getValue(), request.getShopId().getValue(), sourceShopIds, details, request.getTargetingType());
            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
            //处理集合
            if (res.success()) {
                List<SdSuggestedTargetRpcVo> rpcVos = res.getData().stream().filter(Objects::nonNull).map(item -> {
                    SdSuggestedTargetRpcVo.Builder voBuilder = SdSuggestedTargetRpcVo.newBuilder();
                    if (item.getTargetId() != null) {
                        voBuilder.setTargetId(item.getTargetId());
                    }
                    if (item.getSuggested() != null) {
                        voBuilder.setSuggested(item.getSuggested());
                    }
                    if (item.getRangeEnd() != null) {
                        voBuilder.setRangeEnd(item.getRangeEnd());
                    }
                    if (item.getRangeStart() != null) {
                        voBuilder.setRangeStart(item.getRangeStart());
                    }
                    if (CollectionUtils.isNotEmpty(item.getExpression())) {
                        List<SdSuggestedTargetRpcVo.ExpressionNested> expression = item.getExpression().stream().map(innerItem -> {
                            SdSuggestedTargetRpcVo.ExpressionNested.Builder innerBuilder = SdSuggestedTargetRpcVo.ExpressionNested.newBuilder();
                            innerBuilder.setType(innerItem.getType());
                            innerBuilder.setValue(String.valueOf(innerItem.getValue()));
                            return innerBuilder.build();
                        }).collect(Collectors.toList());
                        voBuilder.addAllExpression(expression);
                    }
                    voBuilder.setIndex(item.getIndex());
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 文本获取建议竞价
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getSuggestedBidByText(SuggestedSdBidByTextRequest request, StreamObserver<SuggestedSdBidByTextResponse> responseObserver) {
        log.info("sd-targeting文本获取建议竞价 request {}", request);
        SuggestedSdBidByTextResponse.Builder builder = SuggestedSdBidByTextResponse.newBuilder();

        if (StringUtils.isBlank(request.getGroupId())
                || !request.hasShopId() || !request.hasPuid()
                || CollectionUtils.isEmpty(request.getTargetingList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            AddSdTargetingVo vo = new AddSdTargetingVo();
            vo.setPuid(request.getPuid().getValue());
            vo.setShopId(request.getShopId().getValue());
            vo.setLoginIp(request.getLoginIp());
            vo.setUid(request.getUid().getValue());
            vo.setGroupId(request.getGroupId());

            List<SdTargetingRpcVo> targetingList = request.getTargetingList();
            List<SdTargetingVo> tVos = targetingList.stream().filter(Objects::nonNull).map(item -> {
                SdTargetingVo targetingVo = new SdTargetingVo();
                BeanUtils.copyProperties(item,targetingVo);
                targetingVo.setMinReviewRating(item.getMinReviewRating().getValue());
                targetingVo.setMaxReviewRating(item.getMaxReviewRating().getValue());
                targetingVo.setPrimeShippingEligible(item.hasPrimeShippingEligible() ? item.getPrimeShippingEligible().getValue() : null);
                targetingVo.setBrand(item.getBrand());
                return targetingVo;
            }).collect(Collectors.toList());

            vo.setTargetings(tVos);

            //处理业务返回结果
            Result<List<SdSuggestedTargetVo>> res = cpcSdTargetingService.getSuggestedBidByText(vo);
            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
            //处理list
            if (res.success()) {
                List<SdSuggestedTargetRpcVo> rpcVos = res.getData().stream().filter(Objects::nonNull).map(item -> {
                    SdSuggestedTargetRpcVo.Builder voBuilder = SdSuggestedTargetRpcVo.newBuilder();
                    if (item.getTargetId() != null) {
                        voBuilder.setTargetId(item.getTargetId());
                    }
                    if (item.getSuggested() != null) {
                        voBuilder.setSuggested(item.getSuggested());
                    }
                    if (item.getRangeEnd() != null) {
                        voBuilder.setRangeEnd(item.getRangeEnd());
                    }
                    if (item.getRangeStart() != null) {
                        voBuilder.setRangeStart(item.getRangeStart());
                    }
                    if (CollectionUtils.isNotEmpty(item.getExpression())) {
                        List<SdSuggestedTargetRpcVo.ExpressionNested> expression = item.getExpression().stream().map(innerItem -> {
                            SdSuggestedTargetRpcVo.ExpressionNested.Builder innerBuilder = SdSuggestedTargetRpcVo.ExpressionNested.newBuilder();
                            innerBuilder.setType(innerItem.getType());
                            innerBuilder.setValue(String.valueOf(innerItem.getValue()));
                            return innerBuilder.build();
                        }).collect(Collectors.toList());
                        voBuilder.addAllExpression(expression);
                    }
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 更新竞价
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateBatch(UpdateBatchSdTargetRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sd-targeting-更新投放竞价 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasPuid() || request.getVosCount() < 1 || !request.hasShopId() || !request.hasType()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            List<UpdateBatchTargetVo> collect = request.getVosList().stream().map(re -> {
                UpdateBatchTargetVo vo = new UpdateBatchTargetVo();
                if (re.hasId()) {
                    vo.setId(Long.valueOf(re.getId().getValue()));
                }
                vo.setPuid(request.getPuid().getValue());
                vo.setUid(request.getUid().getValue());
                vo.setShopId(request.getShopId().getValue());
                vo.setState(re.getState());
                if (re.hasBid()) {
                    vo.setBid(re.getBid().getValue());
                }
                return vo;
            }).collect(Collectors.toList());

            Result res = cpcSdTargetingService.updateBatch(collect, request.getType(), request.getIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getData() != null) {
                builder.setData(JSONUtil.objectToJson(res.getData()));
            }
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }


}
