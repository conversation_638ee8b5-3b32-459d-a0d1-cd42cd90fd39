package com.meiyunji.sponsored.api.autorule;

import com.amazon.advertising.mode.Adjustment;
import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.google.api.client.util.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.enums.AmazonAd;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.autorule.status.*;
import com.meiyunji.sponsored.rpc.vo.CommonResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDao;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleTemplateDao;
import com.meiyunji.sponsored.service.autoRule.enums.AdvertiseRuleQueryType;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleTypeEnum;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleTemplate;
import com.meiyunji.sponsored.service.autoRule.service.AdvertiseAutoRuleStatusService;
import com.meiyunji.sponsored.service.autoRule.util.AutoRuleJsonToGrpcUtil;
import com.meiyunji.sponsored.service.autoRule.vo.*;
import com.meiyunji.sponsored.service.autoRuleTask.api.strategy.AutoRuleTaskAllApi;
import com.meiyunji.sponsored.service.autoRuleTask.dao.AutoRuleTaskDao;
import com.meiyunji.sponsored.service.autoRuleTask.enums.ChildrenItemType;
import com.meiyunji.sponsored.service.autoRuleTask.vo.UpdateAutoRuleResponseVo;
import com.meiyunji.sponsored.service.config.IndexStrategyConfig;
import com.meiyunji.sponsored.service.cpc.constants.BrandMessageConstants;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AutoRuleAdGroup;
import com.meiyunji.sponsored.service.cpc.vo.AutoRuleQueryKeyword;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.log.enums.MatchTypeEnum;
import com.meiyunji.sponsored.service.strategy.vo.OriginValueVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@GRpcService
@Slf4j
public class AutoRuleStatusRpcService extends RPCAutoRuleStatusServiceGrpc.RPCAutoRuleStatusServiceImplBase {
    @Autowired
    private IAdvertiseAutoRuleTemplateDao advertiseAutoRuleTemplateDao;
    @Autowired
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;
    @Autowired
    private AdvertiseAutoRuleStatusService advertiseAutoRuleStatusService;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private AutoRuleTaskDao autoRuleTaskDao;
    @Resource
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Autowired
    private IndexStrategyConfig strategyConfig;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private AutoRuleJsonToGrpcUtil autoRuleJsonToGrpcUtil;
    @Autowired
    private AutoRuleTaskAllApi autoRuleTaskAllApi;
    @Autowired
    private IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao;


    @Override
    public void pageCampaignList(CampaignPageRequest request, StreamObserver<CampaignPageResponse> responseObserver) {
        log.info("查询广告活动数据 pageCampaignList request:{}", request);
        CampaignPageResponse.Builder responseBuilder = CampaignPageResponse.newBuilder();
        if (!request.hasPuid() || !request.hasPageNo() || !request.hasPageSize()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            AdCampaignAutoRuleParam param = new AdCampaignAutoRuleParam();
            param.setPuid(request.getPuid());
            param.setShopId(request.getShopId());
            param.setMarketplaceId(request.getMarketplaceId());
            param.setCampaignName(request.getCampaignName());
            param.setPortfolioId(request.getPortfolioIdList());
            param.setTemplateId(request.getTemplateId());
            param.setPageNo(request.getPageNo());
            param.setPageSize(request.getPageSize());
            param.setServingStatusList(request.getServingStatusList());
            param.setOperationType(request.getOperationType());
            //兼容前端传值问题
            if (CollectionUtils.isEmpty(request.getStateList())) {
                param.setStateList(Arrays.asList(StateEnum.enabled.getStateType(), StateEnum.paused.getStateType()));
            } else {
                param.setStateList(request.getStateList());
            }
            param.setTraceId(request.getTraceId());
            if (request.hasHasSimilarRule()) {
                param.setHasSimilarRule(request.getHasSimilarRule());
            }
            if (CollectionUtils.isNotEmpty(request.getAdTypeList())) {
                param.setAdTypeList(new ArrayList<>(request.getAdTypeList()));
            }

            AdvertiseAutoRuleTemplate advertiseAutoRuleTemplate = advertiseAutoRuleTemplateDao.selectByPrimaryKey(param.getPuid(),param.getTemplateId());
            if (advertiseAutoRuleTemplate == null) {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg("当前模板已经被删除");
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }

            //无需判断前端是否有传OperationType
            /*param.getOperationType() != null && */
            if (StringUtils.isNotBlank(advertiseAutoRuleTemplate.getPerformOperation())) {
                List<PerformOperationJson> performOperationJsonList = JSONUtil.jsonToArray(advertiseAutoRuleTemplate.getPerformOperation(), PerformOperationJson.class);
                if (CollectionUtils.isNotEmpty(performOperationJsonList)) {
                    PerformOperationJson performOperationJson = performOperationJsonList.get(0);
                    if (AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction()) != null) {
                        param.setOperationType(AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction()));
                    }
                }
            }

            //规则指标
            if (StringUtils.isNotBlank(advertiseAutoRuleTemplate.getRule())) {
                List<AutoRuleJson> ruleJsonList = JSONUtil.jsonToArray(advertiseAutoRuleTemplate.getRule(), AutoRuleJson.class);
                if (CollectionUtils.isNotEmpty(ruleJsonList)) {
                    param.setRuleIndexList(ruleJsonList.stream().map(x -> x.getRuleIndex()).collect(Collectors.toList()));
                }
            }

            //校验执行动作与规则指标
            //为空则查所有
            if (CollectionUtils.isEmpty(param.getAdTypeList())) {
                param.setAdTypeList(new ArrayList<>(CampaignTypeEnum.campaignTypeList));
            }

            //如果是修改广告位或竞价策略，那么只支持sp
            if (Objects.nonNull(param.getOperationType()) &&
                    (param.getOperationType() == AutoRuleOperationTypeEnum.editBidStrategy.getOperationType()
                            || param.getOperationType() == AutoRuleOperationTypeEnum.editCampaignPlacement.getOperationType())) {
                //移除sb,sd
                if (CollectionUtils.isNotEmpty(param.getAdTypeList())) {
                    param.getAdTypeList().remove(CampaignTypeEnum.sb.getCampaignType());
                    param.getAdTypeList().remove(CampaignTypeEnum.sd.getCampaignType());
                }
            }

            //如果是广告销量，那么不支持sb，还是要查出来展示，不过滤掉
            /*if (CollectionUtils.isNotEmpty(param.getRuleIndexList()) && param.getRuleIndexList().contains(AutoRuleIndexEnum.orderNum.getCode())) {
                //移除sb
                if (CollectionUtils.isNotEmpty(param.getAdTypeList())) {
                    param.getAdTypeList().remove(CampaignTypeEnum.sb.getCampaignType());
                }
            }*/

            Result<Page<AmazonAdCampaignAll>> result = advertiseAutoRuleStatusService.pageCampaignList(param);
            if (result.getCode() == Result.SUCCESS) {
                Page<AmazonAdCampaignAll> voPage = result.getData();
                CampaignPage.Builder campaignPage = CampaignPage.newBuilder();
                campaignPage.setPageNo(voPage.getPageNo());
                campaignPage.setPageSize(voPage.getPageSize());
                campaignPage.setTotalPage(voPage.getTotalPage());
                campaignPage.setTotalSize(voPage.getTotalSize());
                List<AmazonAdCampaignAll> amazonAdCampaignAllList = voPage.getRows();
                if (CollectionUtils.isNotEmpty(amazonAdCampaignAllList)) {
                    List<CampaignRpc> campaignRpcList = new ArrayList<>(amazonAdCampaignAllList.size());
                    //获取所有的店铺名称
                    List<Integer> shopIds = amazonAdCampaignAllList.stream().
                            map(AmazonAdCampaignAll::getShopId).distinct().collect(Collectors.toList());
                    List<ShopAuth> shops = shopAuthDao.getScAndVcByIds(shopIds);
                    List<String> itemIds = advertiseAutoRuleStatusDao.getLisBySimilarRuleItemIdList(param.getPuid(), param.getShopId(), param.getTemplateId(), param.getOperationType());
                    Map<Integer, ShopAuth> shopAuthMap = null;
                    if (CollectionUtils.isNotEmpty(shops)) {
                        shopAuthMap = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
                    }
                    //获取广告组合
                    Map<String, AmazonAdPortfolio> portfolioMap = null;
                    List<String> portfolioIds = amazonAdCampaignAllList.stream().
                            map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(portfolioIds)) {
                        portfolioMap = portfolioDao.getPortfolioList(request.getPuid(), request.getShopId(), portfolioIds).stream()
                                .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                    }
                    for (AmazonAdCampaignAll amazonAdCampaignAll : amazonAdCampaignAllList) {
                        CampaignRpc.Builder builder = CampaignRpc.newBuilder();
                        builder.setId(amazonAdCampaignAll.getId());
                        builder.setPuid(amazonAdCampaignAll.getPuid());
                        builder.setShopId(amazonAdCampaignAll.getShopId());
                        builder.setMarketplaceId(amazonAdCampaignAll.getMarketplaceId());
                        if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(amazonAdCampaignAll.getShopId())) {
                            builder.setShopName(shopAuthMap.get(amazonAdCampaignAll.getShopId()).getName());
                            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(amazonAdCampaignAll.getMarketplaceId());
                            if ( null != m ) {
                                builder.setCurrency(m.getCurrencyCode());
                            }
                        }
                        if (StringUtils.isNotBlank(amazonAdCampaignAll.getServingStatus())) {
                            builder.setServingStatus(amazonAdCampaignAll.getServingStatus());
                            AmazonAdCampaignAll.servingStatusEnum byCode = UCommonUtil.getByCode(amazonAdCampaignAll.getServingStatus(), AmazonAdCampaignAll.servingStatusEnum.class);
                            if(byCode != null){
                                builder.setServingStatusName(byCode.getName());
                                builder.setServingStatusDec(byCode.getDescription());
                            }else {
                                builder.setServingStatusDec(amazonAdCampaignAll.getServingStatus());
                                builder.setServingStatusName(amazonAdCampaignAll.getServingStatus());
                            }
                        }
                        if (CollectionUtils.isNotEmpty(itemIds) && itemIds.contains(amazonAdCampaignAll.getCampaignId())) {
                            builder.setHasSimilarRule(1);
                        } else {
                            builder.setHasSimilarRule(0);
                        }
                        builder.setCampaignId(amazonAdCampaignAll.getCampaignId());
                        builder.setItemId(amazonAdCampaignAll.getCampaignId());
                        builder.setCampaignName(amazonAdCampaignAll.getName());
                        builder.setItemName(amazonAdCampaignAll.getName());
                        builder.setAdType(amazonAdCampaignAll.getType());
                        builder.setBudgetValue(amazonAdCampaignAll.getBudget().doubleValue());
                        if (amazonAdCampaignAll.getBudget() != null) {
                            builder.setBudgetValue(amazonAdCampaignAll.getBudget().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        } else {
                            builder.setBudgetValue(0.00);
                        }
                        Optional.ofNullable(amazonAdCampaignAll.getBudgetType()).filter(StringUtils::isNotEmpty).ifPresent(builder::setBudgetType);
                        if (StringUtils.isNotBlank(amazonAdCampaignAll.getPortfolioId())) {
                            builder.setPortfolioId(amazonAdCampaignAll.getPortfolioId());
                            //查询广告组合名称
                            if (MapUtils.isNotEmpty(portfolioMap) && portfolioMap.containsKey(amazonAdCampaignAll.getPortfolioId())) {
                                builder.setPortfolioName(portfolioMap.get(amazonAdCampaignAll.getPortfolioId()).getName());
                            }
                        }
                        builder.setState(amazonAdCampaignAll.getState().toLowerCase());
                        campaignRpcList.add(builder.build());
                    }
                    campaignPage.addAllRows(campaignRpcList);
                }
                responseBuilder.setCode(result.getCode());
                responseBuilder.setData(campaignPage.build());
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void pageAdKeywordList(AdKeywordPageRequest request, StreamObserver<AdKeywordResponse> responseObserver) {
        log.info("自动化规则查询关键词数据 pageAdKeywordList request:{}", request);
        AdKeywordResponse.Builder responseBuilder = AdKeywordResponse.newBuilder();
        AdKeywordTargetAutoRuleParam param = new AdKeywordTargetAutoRuleParam();
        param.setPuid(request.getPuid());
        param.setShopId(request.getShopId());
        param.setMarketplaceId(request.getMarketplaceId());
        param.setTemplateId(request.getTemplateId());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        //兼容前端传值问题
        if (StringUtils.isBlank(request.getState())) {
            param.setStateList(Arrays.asList(StateEnum.enabled.getStateType(), StateEnum.paused.getStateType()));
        } else {
            param.setStateList(Arrays.asList(request.getState()));
        }
        param.setServingStatusList(request.getServingStatusList());
        param.setTargetType(request.getTargetType());
        param.setMatchType(request.getMatchType());
        param.setCampaignIdList(request.getCampaignIdList());
        param.setSearchValue(request.getKeywordText());
        param.setGroupIdList(request.getAdGroupIdList());
        param.setPortfolioIds(request.getPortfolioIdList());
        param.setTraceId(request.getTraceId());
        param.setOperationType(request.getOperationType());
        if (request.hasHasSimilarRule()) {
            param.setHasSimilarRule(request.getHasSimilarRule());
        }

        //模板删除
        AdvertiseAutoRuleTemplate advertiseAutoRuleTemplate = advertiseAutoRuleTemplateDao.selectByPrimaryKey(param.getPuid(),param.getTemplateId());
        if (advertiseAutoRuleTemplate == null) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("当前模板已经被删除");
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        //无需判断前端是否有传OperationType
        /*param.getOperationType() != null && */
        if (StringUtils.isNotBlank(advertiseAutoRuleTemplate.getPerformOperation())) {
            List<PerformOperationJson> performOperationJsonList = JSONUtil.jsonToArray(advertiseAutoRuleTemplate.getPerformOperation(), PerformOperationJson.class);
            if (CollectionUtils.isNotEmpty(performOperationJsonList)) {
                PerformOperationJson performOperationJson = performOperationJsonList.get(0);
                if (AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction()) != null) {
                    param.setOperationType(AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction()));
                }
            }
        }

        //规则指标
        if (StringUtils.isNotBlank(advertiseAutoRuleTemplate.getRule())) {
            List<AutoRuleJson> ruleJsonList = JSONUtil.jsonToArray(advertiseAutoRuleTemplate.getRule(), AutoRuleJson.class);
            if (CollectionUtils.isNotEmpty(ruleJsonList)) {
                param.setRuleIndexList(ruleJsonList.stream().map(x -> x.getRuleIndex()).collect(Collectors.toList()));
            }
        }

        //校验执行动作与规则指标
        //为空则查sp
        if (StringUtils.isBlank(request.getAdType())) {
            param.setAdType(CampaignTypeEnum.sp.getCampaignType());
        } else {
            param.setAdType(request.getAdType());
        }

        //查询数据
        Result<Page<AdKeywordTargetAutoRuleVo>> result = advertiseAutoRuleStatusService.pageKeywordTargetList(param);

        //不成功
        if (result.getCode() != Result.SUCCESS) {
            responseBuilder.setCode(result.getCode());
            responseBuilder.setMsg(result.getMsg());
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        Page<AdKeywordTargetAutoRuleVo> adKeywordTargetAutoRuleVoPage = result.getData();
        AdKeywordPage.Builder pageBuilder = AdKeywordPage.newBuilder();
        pageBuilder.setPageNo(adKeywordTargetAutoRuleVoPage.getPageNo());
        pageBuilder.setPageSize(adKeywordTargetAutoRuleVoPage.getPageSize());
        pageBuilder.setTotalPage(adKeywordTargetAutoRuleVoPage.getTotalPage());
        pageBuilder.setTotalSize(adKeywordTargetAutoRuleVoPage.getTotalSize());

        //没数据直接返回
        if (CollectionUtils.isEmpty(adKeywordTargetAutoRuleVoPage.getRows())) {
            responseBuilder.setCode(result.getCode());
            responseBuilder.setData(pageBuilder.build());
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        //处理查询结果
        List<AdKeywordTargetAutoRuleVo> adKeywordTargetAutoRuleVoList = adKeywordTargetAutoRuleVoPage.getRows();
        List<AdKeywordPageRpc> list = Lists.newArrayList();

        //收集数据
        Set<Integer> shopIds = new HashSet<>();
        Set<String> campaignIds = new HashSet<>();
        Set<String> spGroupIds = new HashSet<>();
        Set<String> sbGroupIds = new HashSet<>();
        Set<String> sdGroupIds = new HashSet<>();
        adKeywordTargetAutoRuleVoList.forEach(x -> {
            shopIds.add(x.getShopId());
            campaignIds.add(x.getCampaignId());
            if (CampaignTypeEnum.sp.getCampaignType().equals(x.getAdType())) {
                spGroupIds.add(x.getAdGroupId());
            } else if (CampaignTypeEnum.sb.getCampaignType().equals(x.getAdType())) {
                sbGroupIds.add(x.getAdGroupId());
            } else {
                sdGroupIds.add(x.getAdGroupId());
            }
        });


        //获取所有的店铺名称
        List<ShopAuth> shops = shopAuthDao.getScAndVcByIds(new ArrayList<>(shopIds));
        List<String> itemIds = advertiseAutoRuleStatusDao.getLisBySimilarRuleItemIdList(param.getPuid(), param.getShopId(), param.getTemplateId(), param.getOperationType());
        Map<Integer, ShopAuth> shopAuthMap = null;
        Map<String, AmazonAdCampaignAll> campaignMap = null;
        Map<String, AmazonAdGroup> amazonSpAdGroupMap = null;
        Map<String, AmazonSbAdGroup> amazonSbAdGroupMap = null;
        Map<String, AmazonSdAdGroup> amazonSdAdGroupMap = null;
        Map<String, AmazonAdPortfolio> amazonAdPortfolioMap  = null;
        if (CollectionUtils.isNotEmpty(shops)) {
            shopAuthMap = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
        }
        //批量获取广告活动信息
        List<AmazonAdCampaignAll> campaignAlls = amazonAdCampaignAllDao.listByCampaignIdNoType(param.getPuid(), param.getShopId(), new ArrayList<>(campaignIds));
        if (CollectionUtils.isNotEmpty(campaignAlls)) {
            List<String> portfolioIds = campaignAlls.stream().filter(Objects::nonNull).map(AmazonAdCampaignAll::getPortfolioId).distinct().collect(Collectors.toList());
            List<AmazonAdPortfolio> amazonAdPortfolios = portfolioDao.getPortfolioList(param.getPuid(), param.getShopId(), portfolioIds);
            if (CollectionUtils.isNotEmpty(amazonAdPortfolios)) {
                amazonAdPortfolioMap = amazonAdPortfolios.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
            }
            campaignMap = campaignAlls.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
        }

        //批量获取广告组信息
        if (CollectionUtils.isNotEmpty(spGroupIds)) {
            List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.getAdGroupByIds(param.getPuid(), param.getShopId(), null, new ArrayList<>(spGroupIds));
            if (CollectionUtils.isNotEmpty(amazonAdGroups)) {
                amazonSpAdGroupMap = amazonAdGroups.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e));
            }
        }

        if (CollectionUtils.isNotEmpty(sbGroupIds)) {
            List<AmazonSbAdGroup> amazonAdGroups = amazonSbAdGroupDao.getAdGroupByIds(param.getPuid(), param.getShopId(), new ArrayList<>(sbGroupIds));
            if (CollectionUtils.isNotEmpty(amazonAdGroups)) {
                amazonSbAdGroupMap = amazonAdGroups.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, e -> e));
            }
        }

        if (CollectionUtils.isNotEmpty(sdGroupIds)) {
            List<AmazonSdAdGroup> amazonAdGroups = amazonSdAdGroupDao.getByGroupIds(param.getPuid(), param.getShopId(), new ArrayList<>(sdGroupIds));
            if (CollectionUtils.isNotEmpty(amazonAdGroups)) {
                amazonSdAdGroupMap = amazonAdGroups.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, e -> e));
            }
        }

        //遍历填充
        for (AdKeywordTargetAutoRuleVo e : adKeywordTargetAutoRuleVoList) {
            AdKeywordPageRpc.Builder builder = AdKeywordPageRpc.newBuilder();
            builder.setId(e.getId());
            builder.setKeywordId(e.getKeywordId());
            builder.setItemId(e.getKeywordId());
            builder.setKeywordText(e.getKeywordText());
            builder.setItemName(e.getKeywordText());
            builder.setAdGroupId(e.getAdGroupId());
            builder.setAdType(e.getAdType());
            builder.setCampaignId(e.getCampaignId());
            builder.setShopId(e.getShopId());
            builder.setPuid(e.getPuid());
            builder.setState(e.getState().toLowerCase());
            builder.setMarketplaceId(e.getMarketplaceId());
            if (StringUtils.isNotBlank(e.getServingStatus())) {
                builder.setServingStatus(e.getServingStatus());
            }
            if (StringUtils.isNotBlank(e.getServingStatusName())) {
                builder.setServingStatusName(e.getServingStatusName());
            }
            if (StringUtils.isNotBlank(e.getServingStatusDec())) {
                builder.setServingStatusDec(e.getServingStatusDec());
            }
            if (e.getBiddingValue() != null) {
                builder.setBiddingValue(e.getBiddingValue());
            }

            //店铺名称
            if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(e.getShopId())) {
                builder.setShopName(shopAuthMap.get(e.getShopId()).getName());
                MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(e.getMarketplaceId());
                if ( null != m ) {
                    builder.setCurrency(m.getCurrencyCode());
                }
            }

            //活动名称和组合名称
            if (MapUtils.isNotEmpty(campaignMap)) {
                AmazonAdCampaignAll campaign = campaignMap.get(e.getCampaignId());
                if (Objects.nonNull(campaign)) {
                    builder.setCampaignName(campaign.getName());
                    if (MapUtils.isNotEmpty(amazonAdPortfolioMap) && StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        AmazonAdPortfolio portfolio = amazonAdPortfolioMap.get(campaign.getPortfolioId());
                        if (Objects.nonNull(portfolio)) {
                            builder.setPortfolioId(campaign.getPortfolioId());
                            builder.setPortfolioName(portfolio.getName());
                        }
                    }
                    Optional.ofNullable(campaign.getCostType()).filter(StringUtils::isNotEmpty).ifPresent(builder::setCostType);
                    Optional.ofNullable(campaign.getAdGoal()).filter(StringUtils::isNotEmpty)
                            .map(SBCampaignGoalEnum::getSBCampaignGoalEnumByType)
                            .map(SBCampaignGoalEnum::getCode)
                            .map(String::valueOf)
                            .ifPresent(builder::setGoal);
                }
            }

            //广告组名称和竞价
            if (CampaignTypeEnum.sp.getCampaignType().equals(e.getAdType())) {
                if (MapUtils.isNotEmpty(amazonSpAdGroupMap)) {
                    AmazonAdGroup group = amazonSpAdGroupMap.get(e.getAdGroupId());
                    if (Objects.nonNull(group)) {
                        builder.setAdGroupName(group.getName());
                        if (e.getBiddingValue() != null) {
                            builder.setBiddingValue(e.getBiddingValue());
                        } else {
                            if (group.getDefaultBid() != null) {
                                builder.setBiddingValue(group.getDefaultBid());
                            } else {
                                builder.setBiddingValue(0.00);
                            }
                        }
                    }
                }
            } else if (CampaignTypeEnum.sb.getCampaignType().equals(e.getAdType())) {
                if (MapUtils.isNotEmpty(amazonSbAdGroupMap)) {
                    AmazonSbAdGroup group = amazonSbAdGroupMap.get(e.getAdGroupId());
                    if (Objects.nonNull(group)) {
                        builder.setAdGroupName(group.getName());
                        if (e.getBiddingValue() != null) {
                            builder.setBiddingValue(e.getBiddingValue());
                        } else {
                            builder.setBiddingValue(0.00);
                        }
                        Optional.ofNullable(group.getAdFormat()).filter(StringUtils::isNotEmpty).ifPresent(builder::setAdFormat);
                    }
                }
            } else {
                if (MapUtils.isNotEmpty(amazonSdAdGroupMap)) {
                    AmazonSdAdGroup group = amazonSdAdGroupMap.get(e.getAdGroupId());
                    if (Objects.nonNull(group)) {
                        builder.setAdGroupName(group.getName());
                        if (e.getBiddingValue() != null) {
                            builder.setBiddingValue(e.getBiddingValue());
                        } else {
                            if (group.getDefaultBid() != null) {
                                builder.setBiddingValue(group.getDefaultBid().doubleValue());
                            } else {
                                builder.setBiddingValue(0.00);
                            }
                        }
                    }
                }
            }

            //匹配类型
            if (StringUtils.isNotBlank(e.getMatchType())) {
                builder.setMatchType(e.getMatchType());
                builder.setMatchName(MatchTypeEnum.getMatchValue(e.getMatchType()));
            }

            //相似规则
            if (CollectionUtils.isNotEmpty(itemIds) && itemIds.contains(e.getKeywordId())) {
                builder.setHasSimilarRule(1);
            } else {
                builder.setHasSimilarRule(0);
            }

            //投放类型
            if (StringUtils.isNotBlank(e.getTargetType())) {
                builder.setTargetType(e.getTargetType());
            }

            //产品标题图片，类目，品牌，受众等信息
            fillBrandInfo4KeywordList(builder, e);

            list.add(builder.build());
        }

        pageBuilder.addAllRows(list);
        responseBuilder.setCode(result.getCode());
        responseBuilder.setData(pageBuilder.build());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    private void fillBrandInfo4KeywordList(AdKeywordPageRpc.Builder builder, AdKeywordTargetAutoRuleVo e) {
        if (StringUtils.isNotBlank(e.getDetailTargetType())) {
            builder.setDetailTargetType(e.getDetailTargetType());
        }

        if (StringUtils.isNotBlank(e.getResolvedExpression())) {
            builder.setResolvedExpression(e.getResolvedExpression());
        }

        if (StringUtils.isNotBlank(e.getType())) {
            builder.setType(e.getType());
        }

        if (StringUtils.isNotBlank(e.getAsin())) {
            builder.setAsin(e.getAsin());
        }

        if (StringUtils.isNotBlank(e.getTitle())) {
            builder.setTitle(e.getTitle());
        }

        //sd内容投放，给空字符串
        if (SdTargetTypeNewEnum.CONTENT_CATEGORY.getCode().equals(e.getType())) {
            builder.setTitle("");
        }

        if (StringUtils.isNotBlank(e.getImgUrl())) {
            builder.setImgUrl(e.getImgUrl());
        }

        if (StringUtils.isNotBlank(e.getCategory())) {
            builder.setCategory(e.getCategory());
        }

        if (StringUtils.isNotBlank(e.getBrandName())) {
            builder.setBrandName(e.getBrandName());
        }

        if (StringUtils.isNotBlank(e.getCommodityPriceRange())) {
            builder.setCommodityPriceRange(e.getCommodityPriceRange());
        }

        if (StringUtils.isNotBlank(e.getRating())) {
            builder.setRating(e.getRating());
        }

        if (StringUtils.isNotBlank(e.getDistribution())) {
            builder.setDistribution(e.getDistribution());
        }

        if (StringUtils.isNotBlank(e.getLookback())) {
            builder.setLookback(e.getLookback());
        }
    }


    @Override
    public void pageAdGroupList(AdGroupPageRequest request, StreamObserver<AdGroupPageResponse> responseObserver) {
        log.info("查询广告组数据 pageAdGroupList request:{}", request);
        AdGroupPageResponse.Builder responseBuilder = AdGroupPageResponse.newBuilder();
        if (!request.hasPuid() || !request.hasPageNo() || !request.hasPageSize()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            AdGroupAutoRuleParam param = new AdGroupAutoRuleParam();
            param.setPuid(request.getPuid());
            param.setShopId(request.getShopId());
            param.setMarketplaceId(request.getMarketplaceId());
            param.setAdGroupName(request.getAdGroupName());
            param.setPortfolioIds(request.getPortfolioIdList());
            param.setCampaignIdList(request.getCampaignIdList());
            param.setTemplateId(request.getTemplateId());
            param.setPageNo(request.getPageNo());
            param.setPageSize(request.getPageSize());
            if (CollectionUtils.isNotEmpty(request.getAdTypeList())) {
                param.setAdTypes(new ArrayList<>(request.getAdTypeList()));
            }
            param.setOperationType(request.getOperationType());
            param.setServingStatusList(request.getServingStatusList());
            //兼容前端传值问题
            if (CollectionUtils.isEmpty(request.getStateList())) {
                param.setStateList(Arrays.asList(StateEnum.enabled.getStateType(), StateEnum.paused.getStateType()));
            } else {
                param.setStateList(request.getStateList());
            }

            param.setTraceId(request.getTraceId());
            if (request.hasHasSimilarRule()) {
                param.setHasSimilarRule(request.getHasSimilarRule());
            }
            AdvertiseAutoRuleTemplate advertiseAutoRuleTemplate = advertiseAutoRuleTemplateDao.selectByPrimaryKey(param.getPuid(),param.getTemplateId());
            if (advertiseAutoRuleTemplate == null) {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg("当前模板已经被删除");
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }

            //无需判断前端是否有传OperationType
            /*param.getOperationType() != null &&*/
            if (StringUtils.isNotBlank(advertiseAutoRuleTemplate.getPerformOperation())) {
                List<PerformOperationJson> performOperationJsonList = JSONUtil.jsonToArray(advertiseAutoRuleTemplate.getPerformOperation(), PerformOperationJson.class);
                if (CollectionUtils.isNotEmpty(performOperationJsonList)) {
                    PerformOperationJson performOperationJson = performOperationJsonList.get(0);
                    if (AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction()) != null) {
                        param.setOperationType(AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction()));
                    }
                    param.setBidType(performOperationJson.getBidType());
                    param.setItemType(advertiseAutoRuleTemplate.getItemType());
                    param.setAppointAdGroupType(performOperationJson.getAppointAdGroupType());
                }
            }

            //规则指标
            if (StringUtils.isNotBlank(advertiseAutoRuleTemplate.getRule())) {
                List<AutoRuleJson> ruleJsonList = JSONUtil.jsonToArray(advertiseAutoRuleTemplate.getRule(), AutoRuleJson.class);
                if (CollectionUtils.isNotEmpty(ruleJsonList)) {
                    param.setRuleIndexList(ruleJsonList.stream().map(x -> x.getRuleIndex()).collect(Collectors.toList()));
                }
            }

            //校验执行动作与规则指标
            //为空则查所有
            if (CollectionUtils.isEmpty(param.getAdTypes())) {
                param.setAdTypes(new ArrayList<>(CampaignTypeEnum.campaignTypeList));
            }

            //搜索词只有sp和sb
            if (AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName().equals(advertiseAutoRuleTemplate.getItemType())) {
                param.getAdTypes().remove(CampaignTypeEnum.sd.getCampaignType());
            }

            //如果是提高或降低默认竞价，那么不支持sb
            if (Objects.nonNull(param.getOperationType()) &&
                    (param.getOperationType() == AutoRuleOperationTypeEnum.defaultBidAdd.getOperationType()
                            || param.getOperationType() == AutoRuleOperationTypeEnum.defaultBidReduce.getOperationType())) {
                //移除sb
                if (CollectionUtils.isNotEmpty(param.getAdTypes())) {
                    param.getAdTypes().remove(CampaignTypeEnum.sb.getCampaignType());
                }
            }

            //如果是广告销量，那么不支持sb，还是要查出来展示，不过滤掉
            /*if (CollectionUtils.isNotEmpty(param.getRuleIndexList()) && param.getRuleIndexList().contains(AutoRuleIndexEnum.orderNum.getCode())) {
                //移除sb
                if (CollectionUtils.isNotEmpty(param.getAdTypes())) {
                    param.getAdTypes().remove(CampaignTypeEnum.sb.getCampaignType());
                }
            }*/

            Result<Page<AutoRuleAdGroup>> result = advertiseAutoRuleStatusService.pageAdGroupList(param);
            if (result.getCode() == Result.SUCCESS) {
                Page<AutoRuleAdGroup> voPage = result.getData();
                AdGroupPage.Builder adGroupPage = AdGroupPage.newBuilder();
                adGroupPage.setPageNo(voPage.getPageNo());
                adGroupPage.setPageSize(voPage.getPageSize());
                adGroupPage.setTotalPage(voPage.getTotalPage());
                adGroupPage.setTotalSize(voPage.getTotalSize());
                List<AutoRuleAdGroup> autoRuleAdGroupList = voPage.getRows();
                if (CollectionUtils.isNotEmpty(autoRuleAdGroupList)) {
                    List<AdGroupPageRpc> adGroupPageRpcList = new ArrayList<>(autoRuleAdGroupList.size());
                    //获取所有的店铺名称
                    List<Integer> shopIds = autoRuleAdGroupList.stream().
                            map(AutoRuleAdGroup::getShopId).distinct().collect(Collectors.toList());
                    List<ShopAuth> shops = shopAuthDao.getScAndVcByIds(shopIds);
                    List<String> itemIds = advertiseAutoRuleStatusDao.getLisBySimilarRuleItemIdList(param.getPuid(), param.getShopId(), param.getTemplateId(), param.getOperationType());
                    Map<Integer, ShopAuth> shopAuthMap = null;
                    if (CollectionUtils.isNotEmpty(shops)) {
                        shopAuthMap = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
                    }
                    //获取广告活动
                    Map<String,AmazonAdCampaignAll> amazonAdCampaignAllMap = null;
                    //获取广告组合
                    Map<String, AmazonAdPortfolio> portfolioMap = null;
                    List<String> campaignIds = autoRuleAdGroupList.stream().
                            map(AutoRuleAdGroup::getCampaignId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(campaignIds)) {
                        List<AmazonAdCampaignAll> amazonAdCampaignAllList = amazonAdCampaignAllDao.listByCampaignIdNoType(request.getPuid(),request.getShopId(),campaignIds);
                        if (CollectionUtils.isNotEmpty(amazonAdCampaignAllList)) {
                            amazonAdCampaignAllMap = amazonAdCampaignAllList.stream()
                                    .collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                        }
                        List<String> portfolioIds = amazonAdCampaignAllDao.getPortfolioListByCampaignIds(request.getPuid(),request.getShopId(),campaignIds);
                        if (CollectionUtils.isNotEmpty(portfolioIds)) {
                            portfolioMap = portfolioDao.getPortfolioList(request.getPuid(), request.getShopId(), portfolioIds).stream()
                                    .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                        }
                    }
                    for (AutoRuleAdGroup autoRuleAdGroup : autoRuleAdGroupList) {
                        AdGroupPageRpc.Builder builder = AdGroupPageRpc.newBuilder();
                        builder.setId(autoRuleAdGroup.getId());
                        builder.setPuid(autoRuleAdGroup.getPuid());
                        builder.setShopId(autoRuleAdGroup.getShopId());
                        builder.setMarketplaceId(autoRuleAdGroup.getMarketplaceId());
                        if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(autoRuleAdGroup.getShopId())) {
                            builder.setShopName(shopAuthMap.get(autoRuleAdGroup.getShopId()).getName());
                            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(autoRuleAdGroup.getMarketplaceId());
                            if ( null != m ) {
                                builder.setCurrency(m.getCurrencyCode());
                            }
                        }
                        builder.setAdGroupId(autoRuleAdGroup.getAdGroupId());
                        builder.setItemId(autoRuleAdGroup.getAdGroupId());
                        builder.setAdGroupName(autoRuleAdGroup.getName());
                        builder.setCampaignId(autoRuleAdGroup.getCampaignId());
                        builder.setItemName(autoRuleAdGroup.getName());
                        builder.setAdType(autoRuleAdGroup.getAdType());
                        if (MapUtils.isNotEmpty(amazonAdCampaignAllMap) && amazonAdCampaignAllMap.containsKey(autoRuleAdGroup.getCampaignId())) {
                            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(autoRuleAdGroup.getCampaignId());
                            builder.setCampaignName(amazonAdCampaignAll.getName());
                            if (MapUtils.isNotEmpty(portfolioMap) && StringUtils.isNotBlank(amazonAdCampaignAll.getPortfolioId())
                                    && portfolioMap.containsKey(amazonAdCampaignAll.getPortfolioId())) {
                                builder.setPortfolioId(amazonAdCampaignAll.getPortfolioId());
                                builder.setPortfolioName(portfolioMap.get(amazonAdCampaignAll.getPortfolioId()).getName());
                            }
                            Optional.ofNullable(amazonAdCampaignAll.getCostType()).filter(StringUtils::isNotEmpty).ifPresent(builder::setCostType);
                            Optional.ofNullable(amazonAdCampaignAll.getAdGoal()).filter(StringUtils::isNotEmpty).ifPresent(builder::setGoal);
                        }
                        if (CollectionUtils.isNotEmpty(itemIds) && itemIds.contains(autoRuleAdGroup.getAdGroupId())) {
                            builder.setHasSimilarRule(1);
                        } else {
                            builder.setHasSimilarRule(0);
                        }
                        if (StringUtils.isBlank(builder.getCampaignName())) {
                            builder.setCampaignName("");
                        }
                        if (StringUtils.isBlank(builder.getPortfolioName())) {
                            builder.setPortfolioName("");
                        }
                        if (StringUtils.isNotBlank(autoRuleAdGroup.getServingStatus())) {
                            builder.setServingStatus(autoRuleAdGroup.getServingStatus());
                        }
                        if (StringUtils.isNotBlank(autoRuleAdGroup.getServingStatusName())) {
                            builder.setServingStatusName(autoRuleAdGroup.getServingStatusName());
                        }
                        if (StringUtils.isNotBlank(autoRuleAdGroup.getServingStatusDec())) {
                            builder.setServingStatusDec(autoRuleAdGroup.getServingStatusDec());
                        }
                        if (autoRuleAdGroup.getDefaultBid() != null) {
                            builder.setDefaultBiddingValue(autoRuleAdGroup.getDefaultBid().toString());
                        }
                        builder.setState(autoRuleAdGroup.getState());
                        Optional.ofNullable(autoRuleAdGroup.getAdFormat()).filter(StringUtils::isNotEmpty).ifPresent(builder::setAdFormat);
                        adGroupPageRpcList.add(builder.build());
                    }
                    adGroupPage.addAllRows(adGroupPageRpcList);
                }
                responseBuilder.setCode(result.getCode());
                responseBuilder.setData(adGroupPage.build());
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void pageAdQueryKeywordList(AdQueryKeywordRequest request, StreamObserver<AdQueryKeywordResponse> responseObserver) {
        log.info("查询搜索词数据 pageQueryKeywordList request:{}", request);
        AdQueryKeywordResponse.Builder responseBuilder = AdQueryKeywordResponse.newBuilder();
        if (!request.hasPuid() || !request.hasPageNo() || !request.hasPageSize()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        AdQueryKeywordAutoRuleParam param = new AdQueryKeywordAutoRuleParam();
        param.setPuid(request.getPuid());
        param.setShopId(request.getShopId());
        param.setPortfolioIds(request.getPortfolioIdList());
        param.setCampaignIdList(request.getCampaignIdList());
        param.setAdGroupIdList(request.getAdGroupIdList());
        param.setTemplateId(request.getTemplateId());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setTraceId(request.getTraceId());
        param.setOperationType(Integer.valueOf(request.getOperationType()));
        param.setMarketplaceId(request.getMarketplaceId());

        //通过广告类型和搜索词类型确定最终搜索的广告类型
        List<String> adTypeList = Arrays.asList(CampaignTypeEnum.sp.getCampaignType(), CampaignTypeEnum.sb.getCampaignType());
        if (CollectionUtils.isNotEmpty(request.getAdTypeList())) {
            adTypeList = request.getAdTypeList();
        }
        //没有sd
        adTypeList.remove(CampaignTypeEnum.sd.getCampaignType());

        //搜索词类型
        param.setQueryType(AmazonAd.AdQueryTypeEnum.getQueryType(request.getSearchField()));
        String queryAdType = AmazonAd.AdQueryTypeEnum.SB_QUERY.getQueryType().equals(param.getQueryType()) ? AmazonAd.AdCommonEnum.SB.getAdName() : AmazonAd.AdCommonEnum.SP.getAdName();

        if (!adTypeList.contains(queryAdType)) {
            AdQueryKeywordPage.Builder adQueryKeywordPage = AdQueryKeywordPage.newBuilder();
            adQueryKeywordPage.setPageNo(request.getPageNo());
            adQueryKeywordPage.setPageSize(request.getPageSize());
            responseBuilder.setCode(Result.SUCCESS);
            responseBuilder.setData(adQueryKeywordPage);
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        //设置广告类型
        param.setAdType(queryAdType);

        if (CollectionUtils.isNotEmpty(request.getServingStatusList())) {
            param.setServingStatusList(request.getServingStatusList());
        }

        if (StringUtils.isNotBlank(request.getSearchValue())) {
            param.setSearchValue(SqlStringUtil.dealLikeSql(request.getSearchValue()));
        }

        if (StringUtils.isNotBlank(request.getState())) {
            param.setState(request.getState());
        }

        if (StringUtils.isNotBlank(request.getHasSimilarRule())) {
            param.setHasSimilarRule(request.getHasSimilarRule());
        }

        if (StringUtils.isNotBlank(request.getMatchType())) {
            param.setMatchType(request.getMatchType());
        }

        Result<Page<AutoRuleQueryKeyword>> result = advertiseAutoRuleStatusService.pageAdQueryKeywordList(param);

        if (result.getCode() != Result.SUCCESS) {
            responseBuilder.setCode(result.getCode());
            responseBuilder.setMsg(result.getMsg());
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        Page<AutoRuleQueryKeyword> voPage = result.getData();
        AdQueryKeywordPage.Builder adQueryKeywordPage = AdQueryKeywordPage.newBuilder();
        adQueryKeywordPage.setPageNo(voPage.getPageNo());
        adQueryKeywordPage.setPageSize(voPage.getPageSize());
        adQueryKeywordPage.setTotalPage(voPage.getTotalPage());
        adQueryKeywordPage.setTotalSize(voPage.getTotalSize());
        List<AutoRuleQueryKeyword> autoRuleAdQueryKeywordList = voPage.getRows();
        if (CollectionUtils.isNotEmpty(autoRuleAdQueryKeywordList)) {
            List<AdQueryKeywordRpc> adQueryKeywordPageRpcList = new ArrayList<>(autoRuleAdQueryKeywordList.size());
            //获取所有的店铺名称
            List<Integer> shopIds = autoRuleAdQueryKeywordList.stream().
                    map(AutoRuleQueryKeyword::getShopId).distinct().collect(Collectors.toList());
            List<ShopAuth> shops = shopAuthDao.getScAndVcByIds(shopIds);
            List<String> itemIds = advertiseAutoRuleStatusDao.getLisBySimilarRuleItemIdList(param.getPuid(), param.getShopId(), param.getTemplateId(), param.getOperationType());
            Map<Integer, ShopAuth> shopAuthMap = null;
            if (CollectionUtils.isNotEmpty(shops)) {
                shopAuthMap = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
            }
            //获取广告活动
            Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = null;
            //获取广告组合
            Map<String, AmazonAdPortfolio> portfolioMap = null;
            //获取广告组
            Map<String, String> amazonAdGroupMap = null;
            //用于获取相似规则是否存在
            Map<String, AdvertiseAutoRuleStatus> advertiseAutoRuleStatusMap = null;
            List<String> campaignIds = autoRuleAdQueryKeywordList.stream().
                    map(AutoRuleQueryKeyword::getCampaignId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                List<AmazonAdCampaignAll> amazonAdCampaignAllList = amazonAdCampaignAllDao.listByCampaignIdNoType(request.getPuid(), request.getShopId(), campaignIds);
                if (CollectionUtils.isNotEmpty(amazonAdCampaignAllList)) {
                    amazonAdCampaignAllMap = amazonAdCampaignAllList.stream()
                            .collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
                List<String> portfolioIds = amazonAdCampaignAllDao.getPortfolioListByCampaignIds(request.getPuid(),request.getShopId(),campaignIds);
                if (CollectionUtils.isNotEmpty(portfolioIds)) {
                    portfolioMap = portfolioDao.getPortfolioList(request.getPuid(), request.getShopId(), portfolioIds).stream()
                            .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                }
            }

            List<String> adGroupIds = autoRuleAdQueryKeywordList.stream().
                    map(AutoRuleQueryKeyword::getAdGroupId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(adGroupIds)) {
                if (AmazonAd.AdQueryTypeEnum.SB_QUERY.getQueryType().equals(param.getQueryType())) {
                    List<AmazonSbAdGroup> amazonSbAdGroupList = amazonSbAdGroupDao.getAdGroupByIds(request.getPuid(), request.getShopId(), adGroupIds);
                    if (CollectionUtils.isNotEmpty(amazonSbAdGroupList)) {
                        amazonAdGroupMap = amazonSbAdGroupList.stream()
                                .collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, AmazonSbAdGroup::getName));
                    }
                } else {
                    List<AmazonAdGroup> amazonAdGroupList = amazonAdGroupDao.getAdGroupByIds(request.getPuid(), request.getShopId(), adGroupIds);
                    if (CollectionUtils.isNotEmpty(amazonAdGroupList)) {
                        amazonAdGroupMap = amazonAdGroupList.stream()
                                .collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, AmazonAdGroup::getName));
                    }
                }
            }
            List<String> queryIds = autoRuleAdQueryKeywordList.stream().
                    map(AutoRuleQueryKeyword::getQueryId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(queryIds)) {
                try {
                    List<AdvertiseAutoRuleStatus> advertiseAutoRuleStatusList = advertiseAutoRuleStatusDao.getSimilarRuleListByQueryIds(param, queryIds);
                    if (CollectionUtils.isNotEmpty(advertiseAutoRuleStatusList)) {
                        advertiseAutoRuleStatusMap = advertiseAutoRuleStatusList.stream()
                                .collect(Collectors.toMap(AdvertiseAutoRuleStatus::getItemId, e -> e, (v1, v2) -> v1));
                    }
                } catch (Exception e) {
                    log.error("query advertiseAutoRuleStatusList error puid: {}", param.getPuid());
                    throw new RuntimeException(e);
                }
            }
            for (AutoRuleQueryKeyword autoRuleQueryKeyword : autoRuleAdQueryKeywordList) {
                AdQueryKeywordRpc.Builder builder = AdQueryKeywordRpc.newBuilder();
                builder.setQueryId(autoRuleQueryKeyword.getQueryId());
                builder.setSearchQuery(autoRuleQueryKeyword.getQuery());
                if (StringUtils.isNotBlank(autoRuleQueryKeyword.getKeywordText())) {
                    if (AmazonAd.AdQueryTypeEnum.SP_TARGETING.getQueryType().equals(param.getQueryType())) {
//                        AmazonAd.MatchTypeEnum matchType = AmazonAd.MatchTypeEnum.getMatchTypeEnum(autoRuleQueryKeyword.getKeywordText().toUpperCase());
//                        if (matchType == null) {
//                            builder.setKeywordText(autoRuleQueryKeyword.getKeywordText());
//                        } else {
//                            builder.setKeywordText(matchType.getMatchName());
//                        }
                        builder.setMatchType(autoRuleQueryKeyword.getMatchType());
                        builder.setMatchName(AmazonAd.MatchTypeEnum.getMatchName(autoRuleQueryKeyword.getMatchType().toUpperCase()));
                        builder.setKeywordText(autoRuleQueryKeyword.getKeywordText());
                        //填充类目默认信息
                        fillDefaultBrandMessage(builder, autoRuleQueryKeyword, true);
                        //填充商品类目信息
                        if (StringUtils.isNotBlank(autoRuleQueryKeyword.getCategory())) {
                            builder.setCategory(autoRuleQueryKeyword.getCategory());
                        }
                        if (StringUtils.isNotBlank(autoRuleQueryKeyword.getBrandName())) {
                            builder.setBrandName(autoRuleQueryKeyword.getBrandName());
                        }
                        if (StringUtils.isNotBlank(autoRuleQueryKeyword.getCommodityPriceRange())) {
                            builder.setCommodityPriceRange(autoRuleQueryKeyword.getCommodityPriceRange());
                        }
                        if (StringUtils.isNotBlank(autoRuleQueryKeyword.getCategoryRating())) {
                            builder.setCategoryRating(autoRuleQueryKeyword.getCategoryRating());
                        }
                        if (StringUtils.isNotBlank(autoRuleQueryKeyword.getDistribution())) {
                            builder.setDistribution(autoRuleQueryKeyword.getDistribution());
                        }
                        if (StringUtils.isNotBlank(autoRuleQueryKeyword.getLookBack())) {
                            builder.setLookBack(autoRuleQueryKeyword.getLookBack());
                        }
                        if (StringUtils.isNotBlank(autoRuleQueryKeyword.getTargetTitle())) {
                            builder.setTargetTitle(autoRuleQueryKeyword.getTargetTitle());
                        }
                        if (StringUtils.isNotBlank(autoRuleQueryKeyword.getImgUrl())) {
                            builder.setImageUrl(autoRuleQueryKeyword.getImgUrl());
                        }
                    } else {
                        builder.setKeywordText(autoRuleQueryKeyword.getKeywordText());
                    }
                }
                builder.setQueryType(autoRuleQueryKeyword.getQueryType());
                builder.setId(autoRuleQueryKeyword.getId());
                builder.setPuid(autoRuleQueryKeyword.getPuid());
                builder.setShopId(autoRuleQueryKeyword.getShopId());
                builder.setMarketplaceId(autoRuleQueryKeyword.getMarketplaceId());

                if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(autoRuleQueryKeyword.getShopId())) {
                    builder.setShopName(shopAuthMap.get(autoRuleQueryKeyword.getShopId()).getName());
                    MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(autoRuleQueryKeyword.getMarketplaceId());
                    if ( null != m ) {
                        builder.setCurrency(m.getCurrencyCode());
                    }
                }
                if (CollectionUtils.isNotEmpty(itemIds) && itemIds.contains(autoRuleQueryKeyword.getQueryId())) {
                    builder.setHasSimilarRule(1);
                } else {
                    builder.setHasSimilarRule(0);
                }
                if (StringUtils.isNotBlank(autoRuleQueryKeyword.getMatchType()) && !AmazonAd.AdQueryTypeEnum.SP_TARGETING.getQueryType().equals(param.getQueryType())) {
                    builder.setMatchType(autoRuleQueryKeyword.getMatchType());
                    builder.setMatchName(AmazonAd.MatchTypeEnum.getMatchName(autoRuleQueryKeyword.getMatchType().toUpperCase()));
                }
                if (autoRuleQueryKeyword.getServingStatus() != null) {
                    builder.setServingStatus(autoRuleQueryKeyword.getServingStatus());
                    builder.setServingStatusName(AmazonAdTargeting.servingStatusEnum.getServingStatusName(autoRuleQueryKeyword.getServingStatus()));
                    builder.setServingStatusDec(AmazonAdTargeting.servingStatusEnum.getServingStatusDesc(autoRuleQueryKeyword.getServingStatus()));
                }
                builder.setItemId(autoRuleQueryKeyword.getQueryId());
                builder.setItemName(autoRuleQueryKeyword.getQuery());
                builder.setAdGroupId(autoRuleQueryKeyword.getAdGroupId());
                if (amazonAdGroupMap != null && amazonAdGroupMap.containsKey(autoRuleQueryKeyword.getAdGroupId())) {
                    builder.setAdGroupName(String.valueOf(amazonAdGroupMap.get(autoRuleQueryKeyword.getAdGroupId())));
                } else {
                    builder.setAdGroupName("");
                }
                builder.setCampaignId(autoRuleQueryKeyword.getCampaignId());
                if (amazonAdCampaignAllMap != null && amazonAdCampaignAllMap.containsKey(autoRuleQueryKeyword.getCampaignId())) {
                    builder.setCampaignName(amazonAdCampaignAllMap.get(autoRuleQueryKeyword.getCampaignId()).getName());
                } else {
                    builder.setCampaignName("");
                }
                builder.setAdType(autoRuleQueryKeyword.getAdType());
                builder.setState(autoRuleQueryKeyword.getState());
                if (MapUtils.isNotEmpty(amazonAdCampaignAllMap) && amazonAdCampaignAllMap.containsKey(autoRuleQueryKeyword.getCampaignId())) {
                    AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(autoRuleQueryKeyword.getCampaignId());
                    builder.setCampaignName(amazonAdCampaignAll.getName());
                    if (MapUtils.isNotEmpty(portfolioMap) && StringUtils.isNotBlank(amazonAdCampaignAll.getPortfolioId())
                            && portfolioMap.containsKey(amazonAdCampaignAll.getPortfolioId())) {
                        builder.setPortfolioId(amazonAdCampaignAll.getPortfolioId());
                        builder.setPortfolioName(portfolioMap.get(amazonAdCampaignAll.getPortfolioId()).getName());
                    }
                }
                if (StringUtils.isBlank(builder.getPortfolioName())) {
                    builder.setPortfolioName("");
                }
                adQueryKeywordPageRpcList.add(builder.build());
            }
            adQueryKeywordPage.addAllRows(adQueryKeywordPageRpcList);
        }
        responseBuilder.setCode(result.getCode());
        responseBuilder.setData(adQueryKeywordPage);
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     * 填充 品牌细节 默认信息
     */
    private static void fillDefaultBrandMessage(AdQueryKeywordRpc.Builder voBuilder, AutoRuleQueryKeyword item, boolean fillDefault) {
        if (TargetTypeEnum.category.name().equalsIgnoreCase(item.getMatchType())) {
            String brandName = org.apache.commons.lang3.StringUtils.isNotBlank(item.getBrandName()) ? item.getBrandName() : fillDefault ? BrandMessageConstants.DEFAULT_BRAND_NAME : null;
            String commodityPriceRange = org.apache.commons.lang3.StringUtils.isNotBlank(item.getCommodityPriceRange()) ? item.getCommodityPriceRange() : fillDefault ? BrandMessageConstants.DEFAULT_COMMODITY_PRICE_RANGE : null;
            String rating = org.apache.commons.lang3.StringUtils.isNotBlank(item.getCategoryRating()) ? item.getCategoryRating() : fillDefault ? BrandMessageConstants.DEFAULT_RATING : null;
            String distribution = org.apache.commons.lang3.StringUtils.isNotBlank(item.getDistribution()) ? item.getDistribution() : fillDefault ? BrandMessageConstants.DEFAULT_DISTRIBUTION : null;

            if (org.apache.commons.lang3.StringUtils.isNotBlank(brandName)) {
                voBuilder.setBrandName(brandName);
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(commodityPriceRange)) {
                voBuilder.setCommodityPriceRange(commodityPriceRange);
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(rating)) {
                voBuilder.setCategoryRating(rating);
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(distribution)) {
                voBuilder.setDistribution(distribution);
            }
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getLookBack())) {
            voBuilder.setLookBack(item.getLookBack());
        }
    }


    @Override
    public void getAdKeywordCardList(AdKeywordPageRequest request, StreamObserver<AdKeywordResponse> responseObserver) {
        log.info("自动化规则查询关键词数据 pageAdKeywordCardList request:{}", request);
        AdKeywordResponse.Builder responseBuilder = AdKeywordResponse.newBuilder();
        AdKeywordCardAutoRuleParam param = new AdKeywordCardAutoRuleParam();
        param.setPuid(request.getPuid());
        param.setShopId(request.getShopId());
        param.setMarketplaceId(request.getMarketplaceId());
        param.setTemplateId(request.getTemplateId());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setSkuList(request.getSkuList());
        param.setServingStatusList(request.getServingStatusList());
        param.setPortfolioIdList(request.getPortfolioIdList());
        param.setStrategyType(request.getStrategyType());
        //兼容前端传值问题
        if (StringUtils.isBlank(request.getState())) {
            param.setState("all");
        } else {
            param.setState(request.getState());
        }
        param.setTraceId(request.getTraceId());
        param.setMatchType(request.getMatchType());
        param.setCampaignIdList(request.getCampaignIdList());
        param.setKeywordText(request.getKeywordText());
        param.setGroupIdList(request.getAdGroupIdList());
        Result<Page<AmazonAdKeyword>> result = advertiseAutoRuleStatusService.getAdKeywordCardList(param);
        if (result.getCode() == Result.SUCCESS) {
            Page<AmazonAdKeyword> amazonAdKeywordPage = result.getData();
            AdKeywordPage.Builder pageBuilder = AdKeywordPage.newBuilder();
            pageBuilder.setPageNo(request.getPageNo());
            pageBuilder.setPageSize(request.getPageSize());
            if (result.getData() != null && CollectionUtils.isNotEmpty(amazonAdKeywordPage.getRows())) {
                pageBuilder.setTotalPage(amazonAdKeywordPage.getTotalPage());
                pageBuilder.setTotalSize(amazonAdKeywordPage.getTotalSize());
                List<AmazonAdKeyword> amazonAdKeywords = amazonAdKeywordPage.getRows();
                List<AdKeywordPageRpc> list = Lists.newArrayList();
                //获取所有的店铺名称
                List<Integer> shopIds = amazonAdKeywords.stream().
                        map(AmazonAdKeyword::getShopId).distinct().collect(Collectors.toList());
                List<ShopAuth> shops = shopAuthDao.getScAndVcByIds(shopIds);
                Map<Integer, ShopAuth> shopAuthMap = null;
                Map<String, AmazonAdCampaignAll> campaignMap = null;
                Map<String, AmazonAdGroup> amazonAdGroupMap = null;
                Map<String, AmazonAdPortfolio> portfolioMap = null;
                if (CollectionUtils.isNotEmpty(shops)) {
                    shopAuthMap = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
                }
                //提取活动id
                List<String> campaignIds = amazonAdKeywords.stream().
                        map(AmazonAdKeyword::getCampaignId).collect(Collectors.toList());
                //提取广告组id
                List<String> groupIds = amazonAdKeywords.stream().
                        map(AmazonAdKeyword::getAdGroupId).collect(Collectors.toList());
                //批量获取广告活动信息
                List<AmazonAdCampaignAll> campaignAlls = amazonAdCampaignAllDao.
                        listByCampaignIdNoType(param.getPuid(),param.getShopId(),campaignIds);
                if (CollectionUtils.isNotEmpty(campaignAlls)) {
                    List<String> portfolioIds = campaignAlls.stream().filter(Objects::nonNull).
                            map(AmazonAdCampaignAll::getPortfolioId).distinct().collect(Collectors.toList());
                    List<AmazonAdPortfolio> amazonAdPortfolios = portfolioDao.getPortfolioList(param.getPuid(),param.getShopId(),portfolioIds);
                    if (CollectionUtils.isNotEmpty(amazonAdPortfolios)) {
                        portfolioMap = amazonAdPortfolios.stream().filter(Objects::nonNull).
                                collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                    }
                    campaignMap = campaignAlls.stream().filter(Objects::nonNull).
                            collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
                //批量获取广告组信息
                List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.getAdGroupByIds
                        (param.getPuid(),param.getShopId(),null,groupIds);
                if (CollectionUtils.isNotEmpty(amazonAdGroups)) {
                    amazonAdGroupMap = amazonAdGroups.stream().filter(Objects::nonNull).
                            collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e));
                }
                Map<Integer, ShopAuth> finalShopAuthMap = shopAuthMap;
                Map<String, AmazonAdCampaignAll> finalCampaignMap = campaignMap;
                Map<String, AmazonAdGroup> finalAmazonAdGroupMap = amazonAdGroupMap;
                Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;
                amazonAdKeywords.forEach(e->{
                    AdKeywordPageRpc.Builder builder = AdKeywordPageRpc.newBuilder();
                    builder.setId(e.getId());
                    builder.setKeywordId(e.getKeywordId());
                    builder.setItemId(e.getKeywordId());
                    builder.setKeywordText(e.getKeywordText());
                    builder.setItemName(e.getKeywordText());
                    builder.setAdGroupId(e.getAdGroupId());
                    builder.setAdType("sp");
                    builder.setCampaignId(e.getCampaignId());
                    builder.setShopId(e.getShopId());
                    builder.setPuid(e.getPuid());
                    builder.setState(e.getState().toLowerCase());
                    builder.setMarketplaceId(e.getMarketplaceId());
                    if (StringUtils.isNotBlank(e.getServingStatus())) {
                        builder.setServingStatus(e.getServingStatus());
                        AmazonAdKeyword.servingStatusEnum byCode = UCommonUtil.getByCode(e.getServingStatus(), AmazonAdKeyword.servingStatusEnum.class);
                        if(byCode != null){
                            builder.setServingStatusName(byCode.getName());
                        }else {
                            builder.setServingStatusName(e.getServingStatus());
                        }
                    }
                    if (MapUtils.isNotEmpty(finalShopAuthMap) && finalShopAuthMap.containsKey(e.getShopId())) {
                        builder.setShopName(finalShopAuthMap.get(e.getShopId()).getName());
                        MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(e.getMarketplaceId());
                        if ( null != m ) {
                            builder.setCurrency(m.getCurrencyCode());
                        }
                    }
                    if (MapUtils.isNotEmpty(finalCampaignMap) && finalCampaignMap.containsKey(e.getCampaignId())) {
                        builder.setCampaignName(finalCampaignMap.get(e.getCampaignId()).getName());
                        builder.setBidStrategy(finalCampaignMap.get(e.getCampaignId()).getStrategy());
                        //竞价比例无论什么情况都返回给前端，前端根据是否开启竞价回调进行展示
                        String adjustments = finalCampaignMap.get(e.getCampaignId()).getAdjustments();
                        List<Adjustment> array = JSONUtil.jsonToArray(adjustments, Adjustment.class);
                        if (CollectionUtils.isEmpty(array)) {
                            builder.setPlacementTopBidRatio(BigDecimal.ZERO.toPlainString());
                        } else {
                            array.forEach(item -> {
                                if ("placementTop".equals(item.getPredicate())) {
                                    builder.setPlacementTopBidRatio(item.getPercentage().toString());
                                }
                            });
                        }
                        if (MapUtils.isNotEmpty(finalPortfolioMap) && finalPortfolioMap.containsKey(finalCampaignMap.get(e.getCampaignId()).getPortfolioId())) {
                            builder.setPortfolioId(finalCampaignMap.get(e.getCampaignId()).getPortfolioId());
                            builder.setPortfolioName(finalPortfolioMap.get(finalCampaignMap.get(e.getCampaignId()).getPortfolioId()).getName());
                        }
                        Optional.ofNullable(finalCampaignMap.get(e.getCampaignId()).getCostType()).filter(StringUtils::isNotEmpty).ifPresent(builder::setCostType);
                        Optional.ofNullable(finalCampaignMap.get(e.getCampaignId()).getAdGoal()).filter(StringUtils::isNotEmpty)
                                .map(SBCampaignGoalEnum::getSBCampaignGoalEnumByType)
                                .map(SBCampaignGoalEnum::getCode)
                                .map(String::valueOf)
                                .ifPresent(builder::setCostType);
                    }
                    if (MapUtils.isNotEmpty(finalAmazonAdGroupMap) && finalAmazonAdGroupMap.containsKey(e.getAdGroupId())) {
                        builder.setAdGroupName(finalAmazonAdGroupMap.get(e.getAdGroupId()).getName());
                        if (e.getBid() != null) {
                            builder.setBiddingValue(e.getBid());
                        } else {
                            if (finalAmazonAdGroupMap.get(e.getAdGroupId()).getDefaultBid() != null) {
                                builder.setBiddingValue(finalAmazonAdGroupMap.get(e.getAdGroupId()).getDefaultBid());
                            } else {
                                builder.setBiddingValue(0.00);
                            }
                        }
                        Optional.ofNullable(finalAmazonAdGroupMap.get(e.getAdGroupId()).getAdFormat()).filter(StringUtils::isNotEmpty).ifPresent(builder::setAdFormat);

                    }
                    if (StringUtils.isNotBlank(e.getMatchType())) {
                        builder.setMatchType(e.getMatchType());
                        builder.setMatchName(MatchTypeEnum.getMatchValue(e.getMatchType()));
                    }
                    list.add(builder.build());
                });
                pageBuilder.addAllRows(list);
            }
            responseBuilder.setCode(result.getCode());
            responseBuilder.setData(pageBuilder.build());
        } else {
            responseBuilder.setCode(result.getCode());
            responseBuilder.setMsg(result.getMsg());
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void pageAdControlledObject(AdPageControlledObjectRequest request, StreamObserver<AdPageControlledObjectResponse> responseObserver) {
        log.info("自动化规则受控对象查询 pageAdControlledObject request:{}", request);
        AdPageControlledObjectResponse.Builder responseBuilder = AdPageControlledObjectResponse.newBuilder();

        //请求参数
        AutoRuleObjectParam param = new AutoRuleObjectParam();
        param.setPuid(request.getPuid());
        param.setShopId(request.getShopId());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setItemType(request.getItemType());
        param.setTemplateId(request.getTemplateId());
        param.setPortfolioIds(request.getPortfolioIdList());
        param.setCampaignIds(request.getCampaignIdList());
        param.setGroupIds(request.getGroupIdList());
        param.setSearchValue(request.getSearchValue());
        param.setTargetType(request.getSearchField());
        param.setMatchType(request.getMatchType());
        param.setState(request.getState());
        param.setAdTypeList(request.getAdTypeList());
        param.setTraceId(request.getTraceId());
        param.setServingStatusList(request.getServingStatusList());
        if (StringUtils.isNotBlank(request.getStrategyType())) {
            param.setStrategyType(request.getStrategyType());
        }

        Result<Page<AdvertiseAutoRuleStatus>> result = null;
        //广告活动
        if (AutoRuleItemTypeEnum.CAMPAIGN.getName().equals(request.getItemType())) {
            result = advertiseAutoRuleStatusService.pageAdControlledCampaign(param);
        }

        //广告组
        if (AutoRuleItemTypeEnum.AD_GROUP.getName().equals(request.getItemType())) {
            result = advertiseAutoRuleStatusService.pageAdControlledGroup(param);
        }

        //投放
        if (AutoRuleItemTypeEnum.KEYWORD.getName().equals(request.getItemType()) || AutoRuleItemTypeEnum.TARGET.getName().equals(request.getItemType())) {
            //组受控投放，同广告组方法
            if (ChildrenItemType.CHILDREN_TARGET_GROUP.name().equals(request.getChildrenItemType())) {
                result = advertiseAutoRuleStatusService.pageAdControlledGroup(param);
            } else {
                //投放受控
                result = advertiseAutoRuleStatusService.pageAdControlledTarget(param);
            }
        }

        //搜索词
        if (AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName().equals(request.getItemType())) {
            //搜索词受控
            if (ChildrenItemType.CHILDREN_SEARCH_QUERY.name().equals(request.getChildrenItemType())) {
                result = advertiseAutoRuleStatusService.pageAdControlledSearchQuery(param);
            } else {
                //组受控搜索词，同广告组方法
                result = advertiseAutoRuleStatusService.pageAdControlledGroup(param);
            }
        }

        //抢排名
        if (AutoRuleItemTypeEnum.KEYWORD_TARGET.getName().equals(request.getItemType())) {
            result = advertiseAutoRuleStatusService.pageAdControlledKeywordCard(param);
        }

        //处理结果
        if (Result.SUCCESS == result.getCode()) {
            ControlledObjectPage.Builder pageBuilder = ControlledObjectPage.newBuilder();
            Page<AdvertiseAutoRuleStatus> advertiseAutoRuleStatusPage = result.getData();
            pageBuilder.setPageNo(advertiseAutoRuleStatusPage.getPageNo());
            pageBuilder.setPageSize(advertiseAutoRuleStatusPage.getPageSize());
            pageBuilder.setTotalPage(advertiseAutoRuleStatusPage.getTotalPage());
            pageBuilder.setTotalSize(advertiseAutoRuleStatusPage.getTotalSize());
            if (CollectionUtils.isNotEmpty(advertiseAutoRuleStatusPage.getRows())) {
                List<AdvertiseAutoRuleStatus> list = advertiseAutoRuleStatusPage.getRows();
                List<ControlledObjectRpc> controlledObjectRpcs = Lists.newArrayList();
                list.forEach(e -> {
                    ControlledObjectRpc.Builder builder = ControlledObjectRpc.newBuilder();
                    builder.setStatusId(e.getId());
                    builder.setPuid(e.getPuid());
                    builder.setShopId(e.getShopId());
                    builder.setMarketplaceId(e.getMarketplaceId());
                    builder.setItemId(e.getItemId());
                    builder.setAdType(e.getAdType());
                    builder.setBudgetValue(Optional.ofNullable(e.getBudgetValue()).orElse(BigDecimal.valueOf(0.00)).doubleValue());
                    builder.setBiddingValue(Optional.ofNullable(e.getBiddingValue()).orElse(BigDecimal.valueOf(0.00)).doubleValue());
                    builder.setDefaultBiddingValue(Optional.ofNullable(e.getDefaultBid()).orElse(BigDecimal.valueOf(0.00)).doubleValue());
                    if (StringUtils.isNotBlank(e.getCurrency())) {
                        builder.setCurrency(e.getCurrency());
                    }
                    if (StringUtils.isNotBlank(e.getState())) {
                        builder.setState(e.getState().toLowerCase());
                    }
                    if (e.getTemplateId() != null) {
                        builder.setTemplateId(e.getTemplateId());
                    }
                    if (e.getVersion() != null) {
                        builder.setVersion(e.getVersion());
                    }
                    if (StringUtils.isNotBlank(e.getUpdateStatus())) {
                        builder.setUpdateStatus(e.getUpdateStatus());
                    }
                    if (StringUtils.isNotBlank(e.getItemName())) {
                        builder.setItemName(e.getItemName());
                    }
                    if (StringUtils.isNotBlank(e.getItemType())) {
                        builder.setItemType(e.getItemType());
                    }
                    if (StringUtils.isNotBlank(e.getServingStatus())) {
                        builder.setServingStatus(e.getServingStatus());
                    }
                    if (StringUtils.isNotBlank(e.getServingStatusName())) {
                        builder.setServingStatusName(e.getServingStatusName());
                    }
                    if (StringUtils.isNotBlank(e.getServingStatusDec())) {
                        builder.setServingStatusDec(e.getServingStatusDec());
                    }
                    if (StringUtils.isNotBlank(e.getCampaignId())) {
                        builder.setCampaignId(e.getCampaignId());
                    }
                    if (StringUtils.isNotBlank(e.getCampaignName())) {
                        builder.setCampaignName(e.getCampaignName());
                    } else {
                        builder.setCampaignName("");
                    }
                    if (StringUtils.isNotBlank(e.getAdGroupId())) {
                        builder.setAdGroupId(e.getAdGroupId());
                    }
                    if (StringUtils.isNotBlank(e.getAdGroupName())) {
                        builder.setAdGroupName(e.getAdGroupName());
                    }
                    if (StringUtils.isNotBlank(e.getPortfolioId())) {
                        builder.setPortfolioId(e.getPortfolioId());
                    }
                    if (Objects.nonNull(e.getPortfolioIsHidden())) {
                        builder.setIsHidden(e.getPortfolioIsHidden());
                    }
                    if (StringUtils.isNotBlank(e.getPortfolioName())) {
                        builder.setPortfolioName(e.getPortfolioName());
                    } else {
                        builder.setPortfolioName("");
                    }
                    if (StringUtils.isNotBlank(e.getQuery())) {
                        builder.setSearchQuery(e.getQuery());
                    }
                    if (StringUtils.isNotBlank(e.getQueryId())) {
                        builder.setQueryId(e.getQueryId());
                    }
                    if (StringUtils.isNotBlank(e.getMatchType())) {
                        builder.setMatchType(e.getMatchType());
                    }
                    if (StringUtils.isNotBlank(e.getMatchName())) {
                        builder.setMatchName(e.getMatchName());
                    }
                    if (StringUtils.isNotBlank(e.getKeywordText())) {
                        builder.setKeywordText(e.getKeywordText());
                    }
                    builder.setStatus(e.getStatus());

                    if (StringUtils.isNotBlank(e.getTargetType())) {
                        builder.setTargetType(e.getTargetType());
                    }

                    if (StringUtils.isNotBlank(e.getStrategyType())) {
                        builder.setBidStrategy(e.getStrategyType());
                    }
                    if (Objects.nonNull(e.getPlacementTopBidRatio())) {
                        builder.setPlacementTopBidRatio(e.getPlacementTopBidRatio());
                    }

                    //产品标题图片，类目，品牌，受众等信息
                    fillBrandInfo4ControlledObject(builder, e);

                    Optional.ofNullable(e.getCostType()).filter(StringUtils::isNotEmpty).ifPresent(builder::setCostType);
                    Optional.ofNullable(e.getAdGoal()).filter(StringUtils::isNotEmpty).ifPresent(builder::setGoal);
                    Optional.ofNullable(e.getAdFormat()).filter(StringUtils::isNotEmpty).ifPresent(builder::setAdFormat);
                    Optional.ofNullable(e.getBudgetType()).filter(StringUtils::isNotEmpty).ifPresent(builder::setBudgetType);

                    controlledObjectRpcs.add(builder.build());
                });
                pageBuilder.addAllRows(controlledObjectRpcs);
            }
            responseBuilder.setData(pageBuilder);
            responseBuilder.setCode(result.getCode());
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    private void fillBrandInfo4ControlledObject(ControlledObjectRpc.Builder builder, AdvertiseAutoRuleStatus e) {
        if (StringUtils.isNotBlank(e.getDetailTargetType())) {
            builder.setDetailTargetType(e.getDetailTargetType());
        }

        if (StringUtils.isNotBlank(e.getType())) {
            builder.setType(e.getType());
        }

        if (StringUtils.isNotBlank(e.getAsin())) {
            builder.setAsin(e.getAsin());
        }

        if (StringUtils.isNotBlank(e.getTitle())) {
            builder.setTitle(e.getTitle());
        }

        //sd内容投放，给空字符串
        if (SdTargetTypeNewEnum.CONTENT_CATEGORY.getCode().equals(e.getType())) {
            builder.setTitle("");
        }

        if (StringUtils.isNotBlank(e.getImgUrl())) {
            builder.setImgUrl(e.getImgUrl());
        }

        if (StringUtils.isNotBlank(e.getCategory())) {
            builder.setCategory(e.getCategory());
        }

        if (StringUtils.isNotBlank(e.getBrandName())) {
            builder.setBrandName(e.getBrandName());
        }

        if (StringUtils.isNotBlank(e.getCommodityPriceRange())) {
            builder.setCommodityPriceRange(e.getCommodityPriceRange());
        }

        if (StringUtils.isNotBlank(e.getRating())) {
            builder.setRating(e.getRating());
        }

        if (StringUtils.isNotBlank(e.getDistribution())) {
            builder.setDistribution(e.getDistribution());
        }

        if (StringUtils.isNotBlank(e.getLookback())) {
            builder.setLookback(e.getLookback());
        }
    }

    @Override
    public void submitAutoRule(AdAutoRuleStatusAddRequest request, StreamObserver<AdAutoRuleStatusAddResponse> responseObserver) {
        log.info("自动化规则提交 submitAutoRule request:{}", request);
        AdAutoRuleStatusAddResponse.Builder responseBuilder = AdAutoRuleStatusAddResponse.newBuilder();

        //空数据
        if (CollectionUtils.isEmpty(request.getStatusRpcList())) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("无受控对象数据提交");
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        //模板状态
        AdvertiseAutoRuleTemplate vo = advertiseAutoRuleTemplateDao.selectByPrimaryKey(request.getPuid(), request.getTemplateId());
        if (vo == null) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("当前模板已删除");
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        if (CollectionUtils.isEmpty(request.getAuthedShopIdList()) || !request.getAuthedShopIdList().contains(vo.getShopId())) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("未授权");
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        //数量拦截
        int submitSize = request.getStatusRpcList().size();
        if ("keywordCard".equals(vo.getRuleType())) {
            Integer size = advertiseAutoRuleStatusDao.queryItemIdByTemplateId(request.getPuid());
            Integer keywordCardMaxSize = strategyConfig.getKeywordCardMaxSize(request.getPuid());
            if (submitSize + size > keywordCardMaxSize) {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg("受控对象已添加" + keywordCardMaxSize + "，无法提交");
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
        } else {
            Integer statusSize = advertiseAutoRuleStatusDao.getAutoRuleStatusSize(request.getPuid(), request.getTemplateId());
            if ((submitSize + statusSize) > strategyConfig.getStatusSize()) {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg(String.format(Constants.AUTO_STATUS_RULE_TITLE, vo.getTemplateName(), statusSize, strategyConfig.getStatusSize()));
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
        }

        //收集提交的adType和targetType
        Set<String> adTypeSet = new HashSet<>();
        Set<String> targetTypeSet = new HashSet<>();
        for (AdAutoRuleStatusRpc statusRpc : request.getStatusRpcList()) {
            if (StringUtils.isNotBlank(statusRpc.getAdType())) {
                adTypeSet.add(statusRpc.getAdType().toUpperCase(Locale.ROOT));
            }
            if (StringUtils.isNotBlank(statusRpc.getTargetType())) {
                targetTypeSet.add(statusRpc.getTargetType());
            }
        }

        //检查规则指标与广告类型
        List<String> ruleIndexList = new ArrayList<>();
        if (StringUtils.isNotBlank(vo.getRule())) {
            List<AutoRuleJson> ruleJsonList = JSONUtil.jsonToArray(vo.getRule(), AutoRuleJson.class);
            if (CollectionUtils.isNotEmpty(ruleJsonList)) {
                ruleIndexList = ruleJsonList.stream().map(x -> x.getRuleIndex()).collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isNotEmpty(ruleIndexList) && CollectionUtils.isNotEmpty(adTypeSet)) {
            CheckRuleIndexAndActionStatusVo checkedIndex = advertiseAutoRuleStatusService.checkRuleIndexBase(ruleIndexList, adTypeSet);
            if (!checkedIndex.getCheckStatus()) {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg(checkedIndex.getCheckMsg());
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
        }

        //检查执行动作与广告类型
        List<PerformOperationJson> performOperationJsonList = JSONUtil.jsonToArray(vo.getPerformOperation(), PerformOperationJson.class);
        if (CollectionUtils.isNotEmpty(performOperationJsonList)) {
            String ruleAction = performOperationJsonList.get(0).getRuleAction();
            AutoRuleItemTypeEnum itemTypeEnum = AutoRuleItemTypeEnum.map.get(vo.getItemType());
            CheckRuleIndexAndActionStatusVo checkVo = advertiseAutoRuleStatusService.checkOperationBase(itemTypeEnum, adTypeSet, targetTypeSet, ruleAction);
            if (!checkVo.getCheckStatus()) {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg(checkVo.getCheckMsg());
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
        }


        //封装数据
        List<SubmitAutoRuleVo> submitAutoRuleVos = Lists.newArrayList();
        List<AdAutoRuleStatusRpc> list = request.getStatusRpcList();
        list.forEach(e -> {
            SubmitAutoRuleVo submitAutoRuleVo = new SubmitAutoRuleVo();
            submitAutoRuleVo.setShopId(e.getShopId());
            submitAutoRuleVo.setAdGroupId(e.getAdGroupId());
            submitAutoRuleVo.setCampaignId(e.getCampaignId());
            submitAutoRuleVo.setItemId(e.getItemId());
            submitAutoRuleVo.setStatus(e.getStatus());
            submitAutoRuleVo.setAdType(e.getAdType().toUpperCase(Locale.ROOT));
            if (StringUtils.isNotBlank(e.getAdGroupId())) {
                submitAutoRuleVo.setAdGroupId(e.getAdGroupId());
            }
            if (StringUtils.isNotBlank(e.getTargetType())) {
                submitAutoRuleVo.setTargetType(e.getTargetType());
            }
            if (StringUtils.isNotBlank(request.getChildrenItemType())) {
                submitAutoRuleVo.setChildrenItemType(request.getChildrenItemType());
            }
            if (StringUtils.isNotBlank(e.getQueryType())) {
                submitAutoRuleVo.setQueryType(e.getQueryType());
            }
            if (StringUtils.isNotBlank(e.getItemName())) {
                submitAutoRuleVo.setItemName(e.getItemName());
            }
            //原始竞价、预算、默认竞价值
            if (e.hasBiddingValue()) {
                if (MathUtil.isZero(e.getBiddingValue())) {
                    responseBuilder.setCode(Result.ERROR);
                    responseBuilder.setMsg("原始竞价值不能为0");
                    responseObserver.onNext(responseBuilder.build());
                    responseObserver.onCompleted();
                    return;
                }
                submitAutoRuleVo.setBiddingValue(new BigDecimal(e.getBiddingValue()));
            }
            if (e.hasBudgetValue()) {
                if (MathUtil.isZero(e.getBudgetValue())) {
                    responseBuilder.setCode(Result.ERROR);
                    responseBuilder.setMsg("原始预算值不能为0");
                    responseObserver.onNext(responseBuilder.build());
                    responseObserver.onCompleted();
                    return;
                }
                submitAutoRuleVo.setBudgetValue(new BigDecimal(e.getBudgetValue()));
            }
            if (e.hasDefaultBiddingValue()) {
                if (MathUtil.isZero(e.getDefaultBiddingValue())) {
                    responseBuilder.setCode(Result.ERROR);
                    responseBuilder.setMsg("原始默认竞价值不能为0");
                    responseObserver.onNext(responseBuilder.build());
                    responseObserver.onCompleted();
                    return;
                }
                submitAutoRuleVo.setDefaultBiddingValue(new BigDecimal(e.getDefaultBiddingValue()));
            }
            submitAutoRuleVo.setMarketplaceId(e.getMarketplaceId());
            submitAutoRuleVos.add(submitAutoRuleVo);
        });


        Result<List<AddAutoRuleVo>> result = advertiseAutoRuleStatusService.submitAutoRule(request.getPuid(), submitAutoRuleVos, request.getTemplateId(), vo, request.getUpdateId(), request.getTraceId());
        responseBuilder.setCode(result.getCode());
        if (StringUtils.isNotBlank(result.getMsg())) {
            responseBuilder.setMsg(result.getMsg());
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }


    @Override
    public void submitAutoRuleStatus(AdAutoRuleStatusAddRequest request, StreamObserver<AdAutoRuleAddResponse> responseObserver) {
        log.info("自动化规则提交 submitAutoRule request:{}", request);
        AdAutoRuleAddResponse.Builder responseBuilder = AdAutoRuleAddResponse.newBuilder();
        StatusAddResponse.Builder statusBuilder = StatusAddResponse.newBuilder();
        //空数据
        if (CollectionUtils.isEmpty(request.getStatusRpcList())) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("无受控对象数据提交");
            responseBuilder.setData(statusBuilder.build());
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        //模板状态
        AdvertiseAutoRuleTemplate vo = advertiseAutoRuleTemplateDao.selectByPrimaryKey(request.getPuid(), request.getTemplateId());
        if (vo == null) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("当前模板已删除");
            responseBuilder.setData(statusBuilder.build());
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        if (CollectionUtils.isEmpty(request.getAuthedShopIdList()) || !request.getAuthedShopIdList().contains(vo.getShopId())) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("未授权");
            responseBuilder.setData(statusBuilder.build());
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        //数量拦截
        int submitSize = request.getStatusRpcList().size();
        if ("keywordCard".equals(vo.getRuleType())) {
            Integer size = advertiseAutoRuleStatusDao.queryItemIdByTemplateId(request.getPuid());
            Integer keywordCardMaxSize = strategyConfig.getKeywordCardMaxSize(request.getPuid());
            if (submitSize + size > keywordCardMaxSize) {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg("受控对象已添加" + keywordCardMaxSize + "，无法提交");
                responseBuilder.setData(statusBuilder.build());
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
            //弹窗校验，返回广告活动下投放的数量 - 只针对抢排名
            if (Boolean.TRUE.equals(request.getIsCheck()) && AutoRuleTypeEnum.keywordCard.getValue().equalsIgnoreCase(vo.getRuleType())) {
                //以广告活动Id为键，投放Id为值
                Map<String, List<String>> campaignMap = new HashMap<>();
                List<CampaignTargetPair> campaignTargetPairsList = request.getCampaignTargetPairsList();
                campaignMap = StreamUtil.groupingBy(campaignTargetPairsList, CampaignTargetPair::getCampaignId, CampaignTargetPair::getTargetId);
                /**
                 * 是否开启了以下任一操作：
                 * 提高搜索结果顶部(首页)竞价比例、
                 * 降低搜索结果顶部(首页)竞价比例、
                 * 回调搜索结果顶部(首页)竞价比例，
                 */
                boolean openAdd = false;
                boolean openReduce = false;
                boolean openCallback = false;
                if (vo.getAdDataOperate() != null) {
                    AutoPriceOperateJson autoPriceOperateJson = JSONUtil.jsonToObject(vo.getAdDataOperate(), AutoPriceOperateJson.class);
                    if (autoPriceOperateJson != null && autoPriceOperateJson.getPlacementTopBidRatio() != null) {
                        openAdd = true;
                    }
                }
                if (vo.getAutoPriceOperate() != null) {
                    AutoPriceOperateJson autoPriceOperateJson = JSONUtil.jsonToObject(vo.getAutoPriceOperate(), AutoPriceOperateJson.class);
                    if (autoPriceOperateJson != null && autoPriceOperateJson.getPlacementTopBidRatio() != null) {
                        openReduce = true;
                    }
                }
                if (vo.getBiddingCallbackOperate() != null) {
                    BiddingCallbackOperateJson biddingCallbackOperateJson = JSONUtil.jsonToObject(vo.getBiddingCallbackOperate(), BiddingCallbackOperateJson.class);
                    if (biddingCallbackOperateJson!= null && biddingCallbackOperateJson.getPlacementTopBidRatio() != null) {
                        openCallback = true;
                    }
                }
                if (!(openAdd || openReduce || openCallback)) {
                    responseBuilder.setCode(Result.SUCCESS);
                    responseBuilder.setData(statusBuilder.build());
                    responseObserver.onNext(responseBuilder.build());
                    responseObserver.onCompleted();
                    return;
                }

                if (Objects.isNull(campaignMap)) {
                    responseBuilder.setCode(Result.SUCCESS);
                    responseBuilder.setData(statusBuilder.build());
                    responseObserver.onNext(responseBuilder.build());
                    responseObserver.onCompleted();
                    return;
                }
                ArrayList<String> campaignIds = new ArrayList<>(campaignMap.keySet());
                List<AutoRuleCampaignVo> campaignKeywordNumList = amazonAdKeywordShardingDao.getCountByCampaignId(request.getPuid(), request.getShopId(), campaignIds);
                Map<String, AutoRuleCampaignVo> campaignKeywordNumMap = StreamUtil.toMap(campaignKeywordNumList, AutoRuleCampaignVo::getCampaignId);
                //查询跳转所需的参数
                List<AutoRuleCampaignVo> campaigns = amazonAdCampaignAllDao.getCampaignDataByCampaignId(request.getPuid(), request.getShopId(), campaignIds);
                Map<String, AutoRuleCampaignVo> campaignInfoMap = StreamUtil.toMap(campaigns, AutoRuleCampaignVo::getCampaignId);
                //计算广告活动下投放的数量
                //返回跳转需要的参数
                List<CampaignTargetResponse> campaignTargetResponseList = Lists.newArrayList();
                for (String campaignId : campaignIds) {
                    if (campaignKeywordNumMap.containsKey(campaignId) && campaignInfoMap.containsKey(campaignId)) {
                        AutoRuleCampaignVo camKeywordNum = campaignKeywordNumMap.get(campaignId);
                        AutoRuleCampaignVo campaignInfo = campaignInfoMap.get(campaignId);
                        CampaignTargetResponse.Builder builder = CampaignTargetResponse.newBuilder();

                        builder.setTargetNum(camKeywordNum.getTargetNum());
                        builder.setId(campaignInfo.getId());
                        builder.setPuid(campaignInfo.getPuid());
                        builder.setShopId(campaignInfo.getShopId());
                        builder.setMarketplaceId(campaignInfo.getMarketplaceId());
                        builder.setCampaignId(campaignInfo.getCampaignId());
                        builder.setCampaignName(campaignInfo.getCampaignName());
                        builder.setType(campaignInfo.getType());
                        builder.setState(campaignInfo.getState());

                        campaignTargetResponseList.add(builder.build());
                    }
                }
                statusBuilder.addAllRows(campaignTargetResponseList);

                responseBuilder.setData(statusBuilder);
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
        } else {
            Integer statusSize = advertiseAutoRuleStatusDao.getAutoRuleStatusSize(request.getPuid(), request.getTemplateId());
            if ((submitSize + statusSize) > strategyConfig.getStatusSize()) {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg(String.format(Constants.AUTO_STATUS_RULE_TITLE, vo.getTemplateName(), statusSize, strategyConfig.getStatusSize()));
                responseBuilder.setData(statusBuilder.build());
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
        }

        //收集提交的adType和targetType
        Set<String> adTypeSet = new HashSet<>();
        Set<String> targetTypeSet = new HashSet<>();
        for (AdAutoRuleStatusRpc statusRpc : request.getStatusRpcList()) {
            if (StringUtils.isNotBlank(statusRpc.getAdType())) {
                adTypeSet.add(statusRpc.getAdType());
            }
            if (StringUtils.isNotBlank(statusRpc.getTargetType())) {
                targetTypeSet.add(statusRpc.getTargetType());
            }
        }

        //检查规则指标与广告类型
        List<String> ruleIndexList = new ArrayList<>();
        if (StringUtils.isNotBlank(vo.getRule())) {
            List<AutoRuleJson> ruleJsonList = JSONUtil.jsonToArray(vo.getRule(), AutoRuleJson.class);
            if (CollectionUtils.isNotEmpty(ruleJsonList)) {
                ruleIndexList = ruleJsonList.stream().map(x -> x.getRuleIndex()).collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isNotEmpty(ruleIndexList) && CollectionUtils.isNotEmpty(adTypeSet)) {
            CheckRuleIndexAndActionStatusVo checkedIndex = advertiseAutoRuleStatusService.checkRuleIndexBase(ruleIndexList, adTypeSet);
            if (!checkedIndex.getCheckStatus()) {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg(checkedIndex.getCheckMsg());
                responseBuilder.setData(statusBuilder.build());
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
        }

        //检查执行动作与广告类型
        List<PerformOperationJson> performOperationJsonList = JSONUtil.jsonToArray(vo.getPerformOperation(), PerformOperationJson.class);
        if (CollectionUtils.isNotEmpty(performOperationJsonList)) {
            String ruleAction = performOperationJsonList.get(0).getRuleAction();
            AutoRuleItemTypeEnum itemTypeEnum = AutoRuleItemTypeEnum.map.get(vo.getItemType());
            CheckRuleIndexAndActionStatusVo checkVo = advertiseAutoRuleStatusService.checkOperationBase(itemTypeEnum, adTypeSet, targetTypeSet, ruleAction);
            if (!checkVo.getCheckStatus()) {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg(checkVo.getCheckMsg());
                responseBuilder.setData(statusBuilder.build());
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
        }


        //封装数据
        List<SubmitAutoRuleVo> submitAutoRuleVos = Lists.newArrayList();
        List<AdAutoRuleStatusRpc> list = request.getStatusRpcList();
        list.forEach(e -> {
            SubmitAutoRuleVo submitAutoRuleVo = new SubmitAutoRuleVo();
            submitAutoRuleVo.setShopId(e.getShopId());
            submitAutoRuleVo.setAdGroupId(e.getAdGroupId());
            submitAutoRuleVo.setCampaignId(e.getCampaignId());
            if (StringUtils.isEmpty(e.getItemId())) {
                throw new SponsoredBizException("受控对象id不能为空");
            }
            submitAutoRuleVo.setItemId(e.getItemId());
            submitAutoRuleVo.setStatus(e.getStatus());
            submitAutoRuleVo.setAdType(e.getAdType().toUpperCase(Locale.ROOT));
            if (StringUtils.isNotBlank(e.getAdGroupId())) {
                submitAutoRuleVo.setAdGroupId(e.getAdGroupId());
            }
            if (StringUtils.isNotBlank(e.getTargetType())) {
                submitAutoRuleVo.setTargetType(e.getTargetType());
            }
            if (StringUtils.isNotBlank(request.getChildrenItemType())) {
                submitAutoRuleVo.setChildrenItemType(request.getChildrenItemType());
            }
            if (StringUtils.isNotBlank(e.getQueryType())) {
                submitAutoRuleVo.setQueryType(e.getQueryType());
            }
            if (StringUtils.isNotBlank(e.getItemName())) {
                submitAutoRuleVo.setItemName(e.getItemName());
            }
            //原始竞价、预算、默认竞价值、原始竞价比例
            if (e.hasBiddingValue()) {
                if (MathUtil.isZero(e.getBiddingValue())) {
                    responseBuilder.setCode(Result.ERROR);
                    responseBuilder.setMsg("原始竞价值不能为0");
                    responseBuilder.setData(statusBuilder.build());
                    responseObserver.onNext(responseBuilder.build());
                    responseObserver.onCompleted();
                    return;
                }
                submitAutoRuleVo.setBiddingValue(new BigDecimal(e.getBiddingValue()));
            }
            if (e.hasBudgetValue()) {
                if (MathUtil.isZero(e.getBudgetValue())) {
                    responseBuilder.setCode(Result.ERROR);
                    responseBuilder.setMsg("原始预算值不能为0");
                    responseBuilder.setData(statusBuilder.build());
                    responseObserver.onNext(responseBuilder.build());
                    responseObserver.onCompleted();
                    return;
                }
                submitAutoRuleVo.setBudgetValue(new BigDecimal(e.getBudgetValue()));
            }
            if (e.hasDefaultBiddingValue()) {
                if (MathUtil.isZero(e.getDefaultBiddingValue())) {
                    responseBuilder.setCode(Result.ERROR);
                    responseBuilder.setMsg("原始默认竞价值不能为0");
                    responseBuilder.setData(statusBuilder.build());
                    responseObserver.onNext(responseBuilder.build());
                    responseObserver.onCompleted();
                    return;
                }
                submitAutoRuleVo.setDefaultBiddingValue(new BigDecimal(e.getDefaultBiddingValue()));
            }
            if (e.hasPlacementTopBidRatio()) {
                submitAutoRuleVo.setPlacementTopBidRatio(e.getPlacementTopBidRatio());
            }
            submitAutoRuleVo.setMarketplaceId(e.getMarketplaceId());
            submitAutoRuleVos.add(submitAutoRuleVo);
        });


        Result<List<AddAutoRuleVo>> result = advertiseAutoRuleStatusService.submitAutoRule(request.getPuid(), submitAutoRuleVos, request.getTemplateId(), vo, request.getUpdateId(), request.getTraceId());
        responseBuilder.setCode(result.getCode());
        if (StringUtils.isNotBlank(result.getMsg())) {
            responseBuilder.setMsg(result.getMsg());
        }
        responseBuilder.setData(statusBuilder.build());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateAutoRule(AdAutoRuleStatusUpdateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("自动化规则受控对象更新 updateAutoRule request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        List<UpdateAutoRuleVo> updateAutoRuleVos = Lists.newArrayList();
        List<Long> statusIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(request.getStatusRpcsList())) {
            Integer count = autoRuleTaskDao.queryCountByTemplateId(request.getPuid(), request.getTemplateId());
            if (count > 0) {
                responseBuilder.setCode(Int32Value.of(Result.ERROR));
                responseBuilder.setMsg("批量任务未完成，请稍后提交");
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
            request.getStatusRpcsList().forEach(e->{
                UpdateAutoRuleVo updateAutoRuleVo = new UpdateAutoRuleVo();
                updateAutoRuleVo.setStatusId(e.getStatusId());
                updateAutoRuleVo.setItemId(e.getItemId());
                updateAutoRuleVo.setStatus(e.getStatus());
                if (e.hasBiddingValue()) {
                    if ("0".equals(e.getBiddingValue()) || "0.0".equals(e.getBiddingValue()) || "0.00".equals(e.getBiddingValue())) {
                        responseBuilder.setCode(Int32Value.of(Result.ERROR));
                        responseBuilder.setMsg("原始竞价值不能为0");
                        responseObserver.onNext(responseBuilder.build());
                        responseObserver.onCompleted();
                        return;
                    }
                    updateAutoRuleVo.setBiddingValue(new BigDecimal(e.getBiddingValue()));
                    updateAutoRuleVo.setPlacementTopBidRatio(e.getPlacementTopBidRatio());
                }
                updateAutoRuleVos.add(updateAutoRuleVo);
                statusIdList.add(e.getStatusId());
            });
        }
        AdvertiseAutoRuleTemplate advertiseAutoRuleTemplate = advertiseAutoRuleTemplateDao.selectByPrimaryKey(request.getPuid(),request.getTemplateId());
        // 校验规则、执行动作
        checkRuleOperation(request, statusIdList, advertiseAutoRuleTemplate);
        // 更新受控对象
        List<UpdateAutoRuleResponseVo> responseVoList = autoRuleTaskAllApi.updateAutoRule(request.getPuid(),request.getUpdateId(),
                request.getTemplateId(), updateAutoRuleVos, advertiseAutoRuleTemplate, request.getTraceId());
        if(CollectionUtils.isNotEmpty(responseVoList)){
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg(responseVoList.get(0).getMsg());
        }else{
            responseBuilder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     * 校验规则、执行动作
     */
    private void checkRuleOperation(AdAutoRuleStatusUpdateRequest request, List<Long> statusIdList, AdvertiseAutoRuleTemplate advertiseAutoRuleTemplate) {
        List<AdvertiseAutoRuleStatus> statusList = advertiseAutoRuleStatusDao.getByStatusIds(request.getPuid(), statusIdList);
        //收集提交的adType和targetType
        Set<String> adTypeSet = new HashSet<>();
        Set<String> targetTypeSet = new HashSet<>();
        for (AdvertiseAutoRuleStatus statusRpc : statusList) {
            if (StringUtils.isNotBlank(statusRpc.getAdType())) {
                adTypeSet.add(statusRpc.getAdType());
            }
            if (StringUtils.isNotBlank(statusRpc.getTargetType())) {
                targetTypeSet.add(statusRpc.getTargetType());
            }
        }
        //检查规则指标与广告类型
        List<String> ruleIndexList = new ArrayList<>();
        if (StringUtils.isNotBlank(advertiseAutoRuleTemplate.getRule())) {
            List<AutoRuleJson> ruleJsonList = JSONUtil.jsonToArray(advertiseAutoRuleTemplate.getRule(), AutoRuleJson.class);
            if (CollectionUtils.isNotEmpty(ruleJsonList)) {
                ruleIndexList = ruleJsonList.stream().map(x -> x.getRuleIndex()).collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isNotEmpty(ruleIndexList) && CollectionUtils.isNotEmpty(adTypeSet)) {
            CheckRuleIndexAndActionStatusVo checkedIndex = advertiseAutoRuleStatusService.checkRuleIndexBase(ruleIndexList, adTypeSet);
            if (!checkedIndex.getCheckStatus()) {
                throw new SponsoredBizException(checkedIndex.getCheckMsg());
            }
        }
        //检查执行动作与广告类型
        List<PerformOperationJson> performOperationJsonList = JSONUtil.jsonToArray(advertiseAutoRuleTemplate.getPerformOperation(), PerformOperationJson.class);
        if (CollectionUtils.isNotEmpty(performOperationJsonList)) {
            String ruleAction = performOperationJsonList.get(0).getRuleAction();
            AutoRuleItemTypeEnum itemTypeEnum = AutoRuleItemTypeEnum.map.get(advertiseAutoRuleTemplate.getItemType());
            CheckRuleIndexAndActionStatusVo checkVo = advertiseAutoRuleStatusService.checkOperationBase(itemTypeEnum, adTypeSet, targetTypeSet, ruleAction);
            if (!checkVo.getCheckStatus()) {
                throw new SponsoredBizException(checkVo.getCheckMsg());
            }
        }
        // 3.执行动作与投放类型校验
        if (CollectionUtils.isNotEmpty(performOperationJsonList)) {
            for (AdvertiseAutoRuleStatus status : statusList) {
                String ruleAction = performOperationJsonList.get(0).getRuleAction();
                AutoRuleItemTypeEnum itemTypeEnum = AutoRuleItemTypeEnum.map.get(advertiseAutoRuleTemplate.getItemType());
                CheckRuleIndexAndActionStatusVo checkResult = advertiseAutoRuleStatusService.checkTargetType(status.getPuid(), status.getItemId(), status.getShopId(), itemTypeEnum, status.getAdType(), status.getTargetType(), ruleAction);
                if (!checkResult.getCheckStatus()) {
                    throw new SponsoredBizException(checkResult.getCheckMsg());
                }
            }
        }
    }

    @Override
    public void updateAutoRuleBid(AdAutoRuleStatusUpdateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("关键词卡位修改竞价值 updateAutoRule request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        List<UpdateAutoRuleVo> updateAutoRuleVos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(request.getStatusRpcsList())) {
            Integer count = autoRuleTaskDao.queryCountByTemplateId(request.getPuid(), request.getTemplateId());
            if (count > 0) {
                responseBuilder.setCode(Int32Value.of(Result.ERROR));
                responseBuilder.setMsg("批量任务未完成，请稍后提交");
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
            request.getStatusRpcsList().forEach(e->{
                UpdateAutoRuleVo updateAutoRuleVo = new UpdateAutoRuleVo();
                updateAutoRuleVo.setStatusId(e.getStatusId());
                updateAutoRuleVo.setItemId(e.getItemId());
                updateAutoRuleVo.setStatus(e.getStatus());
                updateAutoRuleVo.setBiddingValue(new BigDecimal(e.getBiddingValue()));
                updateAutoRuleVos.add(updateAutoRuleVo);
            });
        }
        Result<String> result = advertiseAutoRuleStatusService.updateAutoRuleBid(request.getPuid(),request.getUpdateId(),
                request.getTemplateId(), updateAutoRuleVos, request.getTraceId());
        responseBuilder.setCode(Int32Value.of(result.getCode()));
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateAutoRulePlacementTopBidRatio(AdAutoRuleStatusUpdateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("关键词卡位修改竞价比例 updateAutoRulePlacementTopBidRatio request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        List<UpdateAutoRuleVo> updateAutoRuleVos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(request.getStatusRpcsList())) {
            Integer count = autoRuleTaskDao.queryCountByTemplateId(request.getPuid(), request.getTemplateId());
            if (count > 0) {
                responseBuilder.setCode(Int32Value.of(Result.ERROR));
                responseBuilder.setMsg("批量任务未完成，请稍后提交");
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
            request.getStatusRpcsList().forEach(e->{
                UpdateAutoRuleVo updateAutoRuleVo = new UpdateAutoRuleVo();
                updateAutoRuleVo.setStatusId(e.getStatusId());
                updateAutoRuleVo.setItemId(e.getItemId());
                updateAutoRuleVo.setStatus(e.getStatus());
                updateAutoRuleVo.setPlacementTopBidRatio(e.getPlacementTopBidRatio());
                updateAutoRuleVos.add(updateAutoRuleVo);
            });
        }
        Result<String> result = advertiseAutoRuleStatusService.updateAutoRuleBid(request.getPuid(),request.getUpdateId(),
                request.getTemplateId(), updateAutoRuleVos, request.getTraceId());
        responseBuilder.setCode(Int32Value.of(result.getCode()));
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateAutoRuleOriginalValue(AdAutoRuleStatusUpdateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("自定义规则修改原始值 updateAutoRuleOriginalValue request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        List<UpdateAutoRuleVo> updateAutoRuleVos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(request.getStatusRpcsList())) {
            Integer count = autoRuleTaskDao.queryCountByTemplateId(request.getPuid(), request.getTemplateId());
            if (count > 0) {
                responseBuilder.setCode(Int32Value.of(Result.ERROR));
                responseBuilder.setMsg("批量任务未完成，请稍后提交");
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
            request.getStatusRpcsList().forEach(e->{
                UpdateAutoRuleVo updateAutoRuleVo = new UpdateAutoRuleVo();
                updateAutoRuleVo.setStatusId(e.getStatusId());
                updateAutoRuleVo.setItemId(e.getItemId());
                updateAutoRuleVo.setStatus(e.getStatus());
                if (e.hasBiddingValue()) {
                    if (MathUtil.isZero(e.getBiddingValue())) {
                        responseBuilder.setCode(Int32Value.of(Result.ERROR));
                        responseBuilder.setMsg("原始竞价值不能为0");
                        responseObserver.onNext(responseBuilder.build());
                        responseObserver.onCompleted();
                        return;
                    }
                    updateAutoRuleVo.setBiddingValue(new BigDecimal(e.getBiddingValue()));
                }
                if (e.hasBudgetValue()) {
                    if (MathUtil.isZero(e.getBudgetValue())) {
                        responseBuilder.setCode(Int32Value.of(Result.ERROR));
                        responseBuilder.setMsg("原始预算值不能为0");
                        responseObserver.onNext(responseBuilder.build());
                        responseObserver.onCompleted();
                        return;
                    }
                    updateAutoRuleVo.setBudgetValue(new BigDecimal(e.getBudgetValue()));
                }
                if (e.hasDefaultBiddingValue()) {
                    if (MathUtil.isZero(e.getDefaultBiddingValue())) {
                        responseBuilder.setCode(Int32Value.of(Result.ERROR));
                        responseBuilder.setMsg("原始默认竞价值不能为0");
                        responseObserver.onNext(responseBuilder.build());
                        responseObserver.onCompleted();
                        return;
                    }
                    updateAutoRuleVo.setDefaultBiddingValue(new BigDecimal(e.getDefaultBiddingValue()));
                }
                updateAutoRuleVos.add(updateAutoRuleVo);
            });
        }
        Result<String> result = advertiseAutoRuleStatusService.updateAutoRuleBid(request.getPuid(),request.getUpdateId(),
                request.getTemplateId(), updateAutoRuleVos, request.getTraceId());
        responseBuilder.setCode(Int32Value.of(result.getCode()));
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    @Deprecated
    public void updateAllAutoRule(AdAutoRuleStatusAllUpdateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("自动化规则受控对象全部更新 updateAllAutoRule request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        List<UpdateAutoRuleVo> updateAutoRuleVoList = Lists.newArrayList();
        AdvertiseAutoRuleTemplate advertiseAutoRuleTemplate = advertiseAutoRuleTemplateDao.
                selectByPrimaryKey(request.getPuid(),request.getTemplateId());
        if (advertiseAutoRuleTemplate != null) {
            AllUpdateAutoRuleParam param = AllUpdateAutoRuleParam.builder()
                    .puid(request.getPuid()).shopId(request.getShopId()).marketplaceId(request.getMarketplaceId())
                    .templateId(request.getTemplateId()).itemType(request.getItemType())
                    .adGroupIds(request.getAdGroupIdsList()).campaignIds(request.getCampaignIdsList())
                    .portfolioIds(request.getPortfolioIdsList()).searchValue(request.getSearchValue())
                    .matchType(request.getMatchType()).state(request.getState()).build();
            List<AdvertiseAutoRuleStatus> advertiseAutoRuleStatusList = advertiseAutoRuleStatusDao.getListByPuidAndShopIds(param);
            List<AdvertiseAutoRuleStatus> list = Lists.newArrayList();
            if ("CAMPAIGN".equals(param.getItemType())) {
                List<AmazonAdCampaignAll> amazonAdCampaignAllList = amazonAdCampaignAllDao.getCampaignsByPortfolioIdList(param);
                if (CollectionUtils.isNotEmpty(amazonAdCampaignAllList)) {
                List<String> campaignIds = amazonAdCampaignAllList.stream().map(AmazonAdCampaignAll::getCampaignId).distinct().collect(Collectors.toList());
                    for (int i = 0;i < advertiseAutoRuleStatusList.size();i++) {
                        if (campaignIds.contains(advertiseAutoRuleStatusList.get(i).getItemId())) {
                            list.add(advertiseAutoRuleStatusList.get(i));
                        }
                    }
                }
            } else if ("KEYWORD".equals(param.getItemType())) {
                param.setState(null);
                List<AmazonAdKeyword> amazonAdKeywords = amazonAdKeywordDaoRoutingService.listByCampaignOrGroupOrName(param);
                if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                    List<String> kewordIds = amazonAdKeywords.stream().map(AmazonAdKeyword::getKeywordId).distinct().collect(Collectors.toList());
                    for (int i = 0;i < advertiseAutoRuleStatusList.size();i++) {
                        if (kewordIds.contains(advertiseAutoRuleStatusList.get(i).getItemId())) {
                            list.add(advertiseAutoRuleStatusList.get(i));
                        }
                    }
                }
            } else if ("GROUP_SEARCH_QUERY".equals(param.getItemType())) {
                if (CollectionUtils.isEmpty(param.getCampaignIds())) {
                    if (CollectionUtils.isEmpty(param.getPortfolioIds())) {
                        List<String> campaignIdList= amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdList(param.getPuid(),param.getShopId(),param.getPortfolioIds());
                        if (CollectionUtils.isNotEmpty(campaignIdList)) {
                            param.setCampaignIds(campaignIdList);
                        } else {
                            responseBuilder.setCode(Int32Value.of(Result.SUCCESS));
                            responseBuilder.setMsg("无数据更新");
                            responseObserver.onNext(responseBuilder.build());
                            responseObserver.onCompleted();
                            return;
                        }

                    }
                }
                List<AmazonAdGroup> amazonAdGroupList = Lists.newArrayList();
                List<AmazonSbAdGroup> amazonSbAdGroupList = Lists.newArrayList();
                if (request.getAdTypeList().contains("sp")) {
                    amazonAdGroupList = amazonAdGroupDao.queryGroupList(param);
                }
                if (request.getAdTypeList().contains("sb")) {
                    amazonSbAdGroupList = amazonSbAdGroupDao.queryGroupList(param);
                }
                if (CollectionUtils.isNotEmpty(amazonAdGroupList)) {
                    List<String> adGroupIds = amazonAdGroupList.stream().map(AmazonAdGroup::getAdGroupId).distinct().collect(Collectors.toList());
                    for (int i = 0;i < advertiseAutoRuleStatusList.size();i++) {
                        if (adGroupIds.contains(advertiseAutoRuleStatusList.get(i).getItemId())) {
                            list.add(advertiseAutoRuleStatusList.get(i));
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(amazonSbAdGroupList)) {
                    List<String> adSbGroupIds = amazonSbAdGroupList.stream().map(AmazonSbAdGroup::getAdGroupId).distinct().collect(Collectors.toList());
                    for (int i = 0;i < advertiseAutoRuleStatusList.size();i++) {
                        if (adSbGroupIds.contains(advertiseAutoRuleStatusList.get(i).getItemId())) {
                            list.add(advertiseAutoRuleStatusList.get(i));
                        }
                    }
                }
            } else if ("KEYWORD_TARGET".equals(param.getItemType())) {
                param.setState(null);
                List<AmazonAdKeyword> amazonAdKeywords = amazonAdKeywordDaoRoutingService.listByCampaignOrGroupOrName(param);
                if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                    List<String> kewordIds = amazonAdKeywords.stream().map(AmazonAdKeyword::getKeywordId).distinct().collect(Collectors.toList());
                    for (int i = 0;i < advertiseAutoRuleStatusList.size();i++) {
                        if (kewordIds.contains(advertiseAutoRuleStatusList.get(i).getItemId())) {
                            list.add(advertiseAutoRuleStatusList.get(i));
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(list)) {
                for (AdvertiseAutoRuleStatus advertiseAutoRuleStatus : list) {
                    UpdateAutoRuleVo updateAutoRuleVo = new UpdateAutoRuleVo();
                    updateAutoRuleVo.setStatusId(advertiseAutoRuleStatus.getId());
                    updateAutoRuleVo.setStatus(advertiseAutoRuleStatus.getStatus());
                    updateAutoRuleVo.setItemId(advertiseAutoRuleStatus.getItemId());
                    OriginValueVo originValueVo = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getOriginValue(),OriginValueVo.class);
                    if (originValueVo != null) {
                        if (originValueVo.getBiddingValue().equals(BigDecimal.ZERO)) {
                            responseBuilder.setCode(Int32Value.of(Result.ERROR));
                            responseBuilder.setMsg("原始竞价值不能为0");
                            responseObserver.onNext(responseBuilder.build());
                            responseObserver.onCompleted();
                            return;
                        }
                    }
                    updateAutoRuleVo.setBiddingValue(originValueVo.getBiddingValue());
                    updateAutoRuleVoList.add(updateAutoRuleVo);
                }
            }
        }
        Result<String> result = advertiseAutoRuleStatusService.updateAutoRule(request.getPuid(),
                request.getTemplateId(), updateAutoRuleVoList, advertiseAutoRuleTemplate, request.getTraceId());
        responseBuilder.setCode(Int32Value.of(result.getCode()));
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateAutoRuleStatus(UpdateAutoRuleStatusRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("自动化规则状态更新 updateAutoRuleStatus request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        AdvertiseAutoRuleStatus autoRuleStatus = advertiseAutoRuleStatusDao.getByStatusId(request.getPuid(), request.getStatusId());
        if (autoRuleStatus == null) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("当前受控对象已被删除");
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        if (CollectionUtils.isEmpty(request.getAuthedShopIdList()) || !request.getAuthedShopIdList().contains(autoRuleStatus.getShopId())) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("未授权");
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        Integer count = autoRuleTaskDao.queryCountByTemplateId(request.getPuid(), autoRuleStatus.getTemplateId());
        if (count > 0) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("批量任务未完成，请稍后提交");
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        Result result = advertiseAutoRuleStatusService.updateAutoRuleStatus(request.getPuid(), request.getStatusId(), request.getStatus(), request.getUpdateId(), request.getTraceId(), autoRuleStatus);
        responseBuilder.setCode(Int32Value.of(result.getCode()));
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void removeAutoRule(AdAutoRuleStatusRemoveRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("自动化规则受控对象删除 removeAutoRule request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        if (CollectionUtils.isEmpty(request.getAuthedShopIdList())) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("未授权");
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        List<RemoveAutoRuleVo> removeAutoRuleVoList = Lists.newArrayList();
        for (Long statusId : request.getStatusIdList()) {
            RemoveAutoRuleVo removeAutoRuleVo = new RemoveAutoRuleVo();
            removeAutoRuleVo.setStatusId(statusId);
            removeAutoRuleVoList.add(removeAutoRuleVo);
        }
        Result result = advertiseAutoRuleStatusService.removeAutoRule(request.getPuid(), removeAutoRuleVoList, request.getTraceId(), request.getAuthedShopIdList());
        responseBuilder.setCode(Int32Value.of(result.getCode()));
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAdProductList(GetAdProductRequest request, StreamObserver<GetAdProductResponse> responseObserver) {
        log.info("关键卡位目标商品查询 getAdProductList request:{}", request);
        GetAdProductResponse.Builder responseBuilder = GetAdProductResponse.newBuilder();
        AdProductAutoRuleParam param = new AdProductAutoRuleParam();
        param.setSearchValue(request.getSearchValue());
        param.setPuid(request.getPuid());
        param.setShopId(request.getShopId());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        Result<Page<AmazonAdProduct>> result = advertiseAutoRuleStatusService.getAdProductList(param);
        if (Result.SUCCESS == result.getCode()) {
            GetAdProductPage.Builder pageBuilder = GetAdProductPage.newBuilder();
            pageBuilder.setPageNo(result.getData().getPageNo());
            pageBuilder.setPageSize(result.getData().getPageSize());
            pageBuilder.setTotalSize(result.getData().getTotalSize());
            pageBuilder.setTotalPage(result.getData().getTotalPage());
            if (CollectionUtils.isNotEmpty(result.getData().getRows())) {
                List<AmazonAdProduct> amazonAdProductList = result.getData().getRows();
                if (CollectionUtils.isNotEmpty(amazonAdProductList)) {
                    amazonAdProductList.forEach(e -> {
                        GetAdProductPageRpc.Builder builder = GetAdProductPageRpc.newBuilder();
                        if (StringUtils.isNotBlank(e.getAsin())) {
                            builder.setAsin(e.getAsin());
                        }
                        if (StringUtils.isNotBlank(e.getSku())) {
                            builder.setSku(e.getSku());
                        }
                        if (StringUtils.isNotBlank(e.getImageUrl())) {
                            builder.setImageUrl(e.getImageUrl());
                        }
                        if (StringUtils.isNotBlank(e.getTitle())) {
                            builder.setTitle(e.getTitle());
                        }
                        pageBuilder.addRows(builder.build());
                    });
                }
            }
            responseBuilder.setData(pageBuilder.build());
        }
        responseBuilder.setCode(result.getCode());
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void pageSimilarRuleList(SimilarRulePageRequest request, StreamObserver<SimilarRulePageResponse> responseObserver) {
        log.info("自定义规则相似规则查询 pageSimilarRuleList request:{}", request);
        SimilarRulePageParam param = new SimilarRulePageParam();
        param.setPuid(request.getPuid());
        param.setId(request.getId());
        param.setItemId(request.getItemId());
        param.setItemType(request.getItemType());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        //获取相似规则列表页
        Result<Page<SimilarRulePageVo>> result = advertiseAutoRuleStatusService.pageSimilarRuleList(param);

        SimilarRulePageResponse.Builder responseBuilder = SimilarRulePageResponse.newBuilder();
        if (Result.SUCCESS == result.getCode() && result.getData() != null) {
            SimilarRulePage.Builder similarRulePage = SimilarRulePage.newBuilder();
            Page<SimilarRulePageVo> similarRuleVoPage = result.getData();
            List<SimilarRulePageVo> similarRulePageVoList = similarRuleVoPage.getRows();
            if (CollectionUtils.isNotEmpty(similarRulePageVoList)) {
                List<SimilarRule> similarRuleList = new ArrayList<>();
                SimilarRule.Builder similarRule = null;
                for (SimilarRulePageVo vo : similarRulePageVoList) {
                    similarRule = SimilarRule.newBuilder();
                    if (vo.getId() != null) {
                        similarRule.setId(vo.getId());
                    }
                    if (StringUtils.isNotBlank(vo.getName())) {
                        similarRule.setName(vo.getName());
                    }
                    similarRuleList.add(similarRule.build());
                }
                similarRulePage.addAllRows(similarRuleList);
                similarRulePage.setPageNo(similarRuleVoPage.getPageNo());
                similarRulePage.setPageSize(similarRuleVoPage.getPageSize());
                similarRulePage.setTotalPage(similarRuleVoPage.getTotalPage());
                similarRulePage.setTotalSize(similarRuleVoPage.getTotalSize());
            }
            responseBuilder.setData(similarRulePage);
        }

        responseBuilder.setCode(result.getCode());
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void pageAllSimilarRuleList(AllSimilarRulePageRequest request, StreamObserver<AllSimilarRulePageResponse> responseObserver) {
        log.info("自定义规则相似规则查询 AllSimilarRulePageRequest request:{}", request);
        SimilarRulePageParam param = new SimilarRulePageParam();
        //获取相似规则列表页
        Result<Page<SimilarRuleVo>> result = advertiseAutoRuleStatusService.pageAllSimilarRuleList(request.getPuid(), request.getShopId(), request.getTemplateId(),
                request.getItemIdList(), request.getExcludedStatusIdList(), request.getPageNo(), request.getPageSize());

        AllSimilarRulePageResponse.Builder responseBuilder = AllSimilarRulePageResponse.newBuilder();
        if (Result.SUCCESS == result.getCode() && result.getData() != null) {
            AllSimilarRulePage.Builder similarRulePage = AllSimilarRulePage.newBuilder();
            Page<SimilarRuleVo> similarRuleVoPage = result.getData();
            List<SimilarRuleVo> similarRuleVoList = similarRuleVoPage.getRows();
            if (CollectionUtils.isNotEmpty(similarRuleVoList)) {
                List<AllSimilarAllRule> similarRuleList = new ArrayList<>();
                for (SimilarRuleVo vo : similarRuleVoList) {
                    AllSimilarAllRule.Builder allSimilarAllRule = AllSimilarAllRule.newBuilder();
                    if (StringUtils.isNotBlank(vo.getItemId())) {
                        allSimilarAllRule.setItemId(vo.getItemId());
                    } else {
                        allSimilarAllRule.setItemId("");
                    }
                    if (StringUtils.isNotBlank(vo.getItemName())) {
                        allSimilarAllRule.setItemName(vo.getItemName());
                    } else {
                        allSimilarAllRule.setItemName("");
                    }
                    if (CollectionUtils.isNotEmpty(vo.getTemplateRuleVoList())) {
                        vo.getTemplateRuleVoList().forEach(e->{
                            TemplateRule.Builder builder = TemplateRule.newBuilder();
                            builder.setTemplateId(e.getTemplateId());
                            builder.setTemplateName(e.getTemplateName());
                            allSimilarAllRule.addTemplateRuleList(builder.build());
                        });
                    }
                    similarRuleList.add(allSimilarAllRule.build());
                }
                similarRulePage.addAllRows(similarRuleList);
                similarRulePage.setPageNo(similarRuleVoPage.getPageNo());
                similarRulePage.setPageSize(similarRuleVoPage.getPageSize());
                similarRulePage.setTotalPage(similarRuleVoPage.getTotalPage());
                similarRulePage.setTotalSize(similarRuleVoPage.getTotalSize());
            }
            responseBuilder.setData(similarRulePage);
        }

        responseBuilder.setCode(result.getCode());
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void queryGoalAdGroupCpc(QueryGoalAdGroupCpcRequest request, StreamObserver<QueryGoalAdGroupCpcResponse> responseObserver) {
        log.info("自动化规则广告组cpc指标查询 queryGoalAdGroupCpc request:{}", request);
        QueryGoalAdGroupCpcResponse.Builder responseBuilder = QueryGoalAdGroupCpcResponse.newBuilder();
        Result<String> result = advertiseAutoRuleStatusService.queryGoalAdGroupCpc(request.getPuid(),
                request.getShopId(), request.getMarketplaceId(),request.getAdType(),request.getAdGroupId());
        responseBuilder.setCode(result.getCode());
        responseBuilder.setData(result.getData());
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void checkOperation(CheckOperationRequest request, StreamObserver<CheckOperationResponse> responseObserver) {
        log.info("自动化规则检查执行动作与广告类型是否匹配, templateId:{}, ruleAction:{}", request.getTemplateId(), request.getRuleAction());
        CheckOperationResponse.Builder responseBuilder = CheckOperationResponse.newBuilder();
        Result<CheckOperationVo> result = advertiseAutoRuleStatusService.checkOperation4Grpc(request.getPuid(), request.getShopIdList(), request.getTemplateId(), request.getRuleAction());
        responseBuilder.setCode(result.getCode());
        responseBuilder.setData(result.getData());
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void pageAutoRuleStatus(PageAutoRuleStatusRequest request, StreamObserver<PageAutoRuleStatusResponse> responseObserver) {
        // grpc转请求参数
        PageAutoRuleStatusParam param = cn.hutool.json.JSONUtil.toBean(request.getParam(), PageAutoRuleStatusParam.class);
        param.setPuid(request.getPuid());
        param.setItemTypeList(AdvertiseRuleQueryType.map.get(param.getItemType()));
        // 分页获取数据
        Page<AdAutoRuleStatusVo> page = advertiseAutoRuleStatusService.pageAutoRuleStatus(param);
        // 构建响应参数
        PageAutoRuleStatusResponse response = buildResponse(page,param);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void getStatusByStatusId(GetStatusRequest request, StreamObserver<AutoRuleStatusResponse> responseObserver) {
        // 根据主键id获取受控对象记录
        AdAutoRuleStatusVo vo =  advertiseAutoRuleStatusService.getStatusByStatusId(request.getPuid(),request.getStatusId());
        // 构建响应参数
        AutoRuleStatusResp.Builder respBuilder = buildVoResponse(vo, new HashMap<>());
        AutoRuleStatusResponse.Builder builder = AutoRuleStatusResponse.newBuilder();
        builder.setCode(Result.SUCCESS);
        builder.setData(respBuilder.build());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void editAutoRuleStatus(EditAutoRuleStatusRequest request, StreamObserver<CommonResponse> responseObserver) {
        // grpc转请求参数
        EditAutoRuleStatusParam param = cn.hutool.json.JSONUtil.toBean(request.getParam(), EditAutoRuleStatusParam.class);
        param.setPuid(request.getPuid());
        param.setUid(request.getUid());
        log.info("编辑受控对象 param {}", JSONUtil.objectToJson(param));
        Boolean b = advertiseAutoRuleStatusService.editAutoRuleStatus(param);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        builder.setCode(Int32Value.of(Result.SUCCESS));
        builder.setData(b.toString());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void checkItemTemplate(CheckItemTemplateRequest request, StreamObserver<CheckItemTemplateResponse> responseObserver)  {
        // grpc转请求参数
        CheckItemTemplateParam param = cn.hutool.json.JSONUtil.toBean(request.getParam(), CheckItemTemplateParam.class);
        param.setPuid(request.getPuid());
        // 校验受控对象是否合法
        List<CheckItemTemplateVo> voList = advertiseAutoRuleStatusService.checkItemTemplate(param);
        // 构建grpc响应参数
        List<CheckItemTemplateResp> respList = new ArrayList<>();
        for (CheckItemTemplateVo vo : voList) {
            CheckItemTemplateResp.Builder respBuilder = CheckItemTemplateResp.newBuilder();
            respBuilder.setItemId(vo.getItemId());
            respBuilder.setItemName(vo.getItemName());
            respBuilder.addAllErrorMsgList(vo.getErrorMsgList());
            respList.add(respBuilder.build());
        }
        CheckItemTemplateResponse.Builder builder = CheckItemTemplateResponse.newBuilder();
        builder.setCode(Result.SUCCESS);
        builder.addAllData(respList);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 构建响应参数
     */
    private PageAutoRuleStatusResponse buildResponse(Page<AdAutoRuleStatusVo> page, PageAutoRuleStatusParam param) {
        PageAutoRuleStatusResponse.Builder builder = PageAutoRuleStatusResponse.newBuilder();
        PageAutoRuleStatus.Builder pageBuilder = PageAutoRuleStatus.newBuilder();
        pageBuilder.setPageNo(page.getPageNo());
        pageBuilder.setPageSize(page.getPageSize());
        pageBuilder.setTotalPage(page.getTotalPage());
        pageBuilder.setTotalSize(page.getTotalSize());
        List<AutoRuleStatusResp> respList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(page.getRows())){
            // 前置获取广告组名称
            Map<String, String> groupNameMap = getGroupNameMap(page, param);
            for (AdAutoRuleStatusVo row : page.getRows()) {
                AutoRuleStatusResp.Builder respBuilder = buildVoResponse(row, groupNameMap);
                respList.add(respBuilder.build());
            }
        }
        pageBuilder.addAllRows(respList);
        builder.setCode(Result.SUCCESS);
        builder.setData(pageBuilder.build());
        return builder.build();
    }

    /**
     * 构建响应vo
     */
    private AutoRuleStatusResp.Builder buildVoResponse(AdAutoRuleStatusVo row, Map<String, String> groupNameMap) {
        AutoRuleStatusResp.Builder respBuilder = AutoRuleStatusResp.newBuilder();
        BeanUtils.copyProperties(row, respBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(row));
        if(CollectionUtils.isNotEmpty(row.getPushMessageTypeStr())){
            respBuilder.addAllPushMessageTypeStr(row.getPushMessageTypeStr());
        }
        if (StringUtils.isNotBlank(row.getCallbackState())) {
            respBuilder.setCallbackState(Boolean.parseBoolean(row.getCallbackState()));
        }
        if(StringUtils.isNotEmpty(row.getSku())){
            // 获取产品信息
            respBuilder.addAllProductList(autoRuleJsonToGrpcUtil.buildSku(row.getSku(),row.getPuid(),row.getShopId()));
        }
        if (StringUtils.isNotEmpty(row.getTimeRule())) {
            // 构建时间规则信息
            respBuilder.addAllTimeRules(AutoRuleJsonToGrpcUtil.buildTimeRules(row.getTimeRule()));
        }
        if (StringUtils.isNotEmpty(row.getRule())) {
            // 构建自动化规则-规则信息
            respBuilder.addAllRuleList(AutoRuleJsonToGrpcUtil.buildRule(row.getRule(), row.getRuleType()));
        }
        if (StringUtils.isNotEmpty(row.getPerformOperation())) {
            // 构建自动化规则执行操作信息
            respBuilder.addAllPerformOperationList(AutoRuleJsonToGrpcUtil.buildPerformOperation(row.getPerformOperation(), row.getItemType(), groupNameMap));
        }
        if (StringUtils.isNotEmpty(row.getCallbackOperate())) {
            // 构建自动化规则回调操作信息
            respBuilder.setCallbackOperate(AutoRuleJsonToGrpcUtil.buildCallbackOperate(row.getCallbackOperate()));
        }
        if (StringUtils.isNotEmpty(row.getDesiredPosition())) {
            // 构建抢排名期望位置
            respBuilder.setDesiredPosition(AutoRuleJsonToGrpcUtil.buildDesiredPosition(row.getDesiredPosition()));
        }
        if (StringUtils.isNotEmpty(row.getAdDataRule())) {
            // 构建抢排名自动加价规则列表信息
            respBuilder.addAllAdDataRules(AutoRuleJsonToGrpcUtil.buildAdDataRules(row.getAdDataRule(),row.getRuleType()));
        }
        if (StringUtils.isNotEmpty(row.getAdDataOperate())) {
            // 构建抢排名自动加价执行操作信息
            respBuilder.setAdDataOperate(AutoRuleJsonToGrpcUtil.buildAdDataOperate(row.getAdDataOperate()));
        }
        if (StringUtils.isNotEmpty(row.getAutoPriceRule())) {
            // 构建抢排名自动降价规则信息
            respBuilder.setAutoPriceRule(AutoRuleJsonToGrpcUtil.buildAutoPriceRule(row.getAutoPriceRule()));
        }
        if (StringUtils.isNotEmpty(row.getAutoPriceOperate())) {
            // 构建抢排名自动降价执行操作信息
            respBuilder.setAutoPriceOperate(AutoRuleJsonToGrpcUtil.buildAutoPriceOperate(row.getAutoPriceOperate()));
        }
        if (StringUtils.isNotEmpty(row.getBiddingCallbackOperate())) {
            // 构建抢排名竞价回调操作信息
            respBuilder.setBiddingCallbackOperate(AutoRuleJsonToGrpcUtil.buildBiddingCallbackOperate(row.getBiddingCallbackOperate()));
        }
        return respBuilder;
    }

    /**
     * 获取广告组名称
     */
    private Map<String, String>  getGroupNameMap(Page<AdAutoRuleStatusVo> page, PageAutoRuleStatusParam param) {
        Set<String> adGroupIdSet = new HashSet<>();
        for (AdAutoRuleStatusVo row : page.getRows()) {
            if(StringUtils.isEmpty(row.getPerformOperation())){
                continue;
            }
            List<PerformOperationJson> performOperationJsonList = JSONUtil.jsonToArray(row.getPerformOperation(), PerformOperationJson.class);
            if(CollectionUtils.isEmpty(performOperationJsonList)){
                continue;
            }
            for (PerformOperationJson operationJson : performOperationJsonList) {
                if(StringUtils.isBlank(operationJson.getAdGroupId())){
                    continue;
                }
                adGroupIdSet.add(operationJson.getAdGroupId());
            }
        }
        Map<String, String> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(adGroupIdSet)) {
            //查询广告组
            List<AmazonAdGroup> spGroupList = amazonAdGroupDao.listByGroupIdsAndShopIdList(param.getPuid(), new ArrayList<>(param.getShopId()), new ArrayList<>(adGroupIdSet));
            List<AmazonSbAdGroup> sbGroupList = amazonSbAdGroupDao.getListByShopIdsAndGroupIds(param.getPuid(), new ArrayList<>(param.getShopId()), new ArrayList<>(adGroupIdSet));
            spGroupList.forEach(x -> map.put(x.getAdGroupId(), x.getName()));
            sbGroupList.forEach(x -> map.put(x.getAdGroupId(), x.getName()));
        }
        return map;
    }

}
