package com.meiyunji.sponsored.api.multiple;

import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.api.common.AdCommonRpcService;
import com.meiyunji.sponsored.api.export.AdExportRpcService;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.multiple.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.cpc.constants.AdManagePageExportTaskTypeEnum;
import com.meiyunji.sponsored.service.cpc.service2.IAdManagePageExportTaskService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.PortfolioPageParam;
import com.meiyunji.sponsored.service.enums.ShopTypeEnum;
import com.meiyunji.sponsored.service.multiple.common.utils.MultipleUtils;
import com.meiyunji.sponsored.service.multiple.portfolio.sevice.IMultiplePortfolioService;
import com.meiyunji.sponsored.service.multiple.portfolio.vo.MultiplePortfolioPageVo;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTemplateDao;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatus;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.strategyTask.enums.ItemType;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@GRpcService
@Slf4j
@RequiredArgsConstructor
public class MultiplePortfolioRpcService extends RPCMultiplePortfolioServiceGrpc.RPCMultiplePortfolioServiceImplBase {

    private final IMultiplePortfolioService multiplePortfolioService;
    private final IScVcShopAuthDao scVcShopAuthDao;
    private final AdvertiseStrategyStatusDao advertiseStrategyStatusDao;
    private final AdvertiseStrategyTemplateDao advertiseStrategyTemplateDao;
    private final IAdManagePageExportTaskService adManagePageExportTaskService;
    private final IVcShopAuthDao vcShopAuthDao;

    /**
     * 多店铺广告活动组合列表页接口
     * 旧支持单店铺接口： {@link AdCommonRpcService#getAllPortfolioDataList}
     */
    @Override
    public void getAllPortfolioData(GetPortfolioDataRequest request, StreamObserver<GetPortfolioDataResponse> responseObserver) {
        log.info("getAllPortfolioData = {}", request);
        PortfolioPageParam param = buildRequest(request);
        String message = check(param);
        GetPortfolioDataResponse.Builder builder = GetPortfolioDataResponse.newBuilder();
        if (StringUtils.isNotBlank(message)) {
            builder.setCode(Result.ERROR);
            builder.setMsg(message);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        builder.setCode(Result.SUCCESS);
        Page<MultiplePortfolioPageVo> portfolioData = multiplePortfolioService.getAllPortfolioData(param);
        GetPortfolioDataResponse.PortfolioHomeVo.Builder pageBuilder = GetPortfolioDataResponse.PortfolioHomeVo.newBuilder();
        GetPortfolioDataResponse.PortfolioHomeVo.Page.Builder page = GetPortfolioDataResponse.PortfolioHomeVo.Page.newBuilder();
        page.setPageNo(portfolioData.getPageNo());
        page.setPageSize(portfolioData.getPageSize());
        page.setTotalPage(portfolioData.getTotalPage());
        page.setTotalSize(portfolioData.getTotalSize());
        List<ShopAuth> shopAuths = scVcShopAuthDao.listAllByIds(param.getPuid(), request.getShopIdList());
        final boolean isCurrency = MultipleUtils.changeRate(shopAuths);
        Map<String, String> advertiseStrategyStatusMap = new HashMap<>(4);
        if (CollectionUtils.isNotEmpty(portfolioData.getRows())) {
            // TODO 这里可以考虑连表一次性出来 后面再说 这里是写到后面 发现少了模板名称 才在这里处理 后续再处理
            List<AdvertiseStrategyStatus> advertiseStrategyStatusList = advertiseStrategyStatusDao.getByShopIdAndItemIds(param.getPuid(), param.getShopIdList(), ItemType.PORTFOLIO.name(), param.getPortfolioIdList(), null, null);
            if (CollectionUtils.isNotEmpty(advertiseStrategyStatusList)) {
                Map<Long, String> nameMap = StreamUtil.toMap(advertiseStrategyTemplateDao.getListByTemplateId(param.getPuid(), advertiseStrategyStatusList.stream().map(AdvertiseStrategyStatus::getTemplateId).distinct().collect(Collectors.toList())), AdvertiseStrategyTemplate::getId, AdvertiseStrategyTemplate::getTemplateName);
                for (AdvertiseStrategyStatus advertiseStrategyStatus : advertiseStrategyStatusList) {
                    advertiseStrategyStatusMap.put(advertiseStrategyStatus.getPortfolioId(), nameMap.getOrDefault(advertiseStrategyStatus.getTemplateId(), ""));
                }
            }
        }
        Boolean isVc = shopAuths.stream().anyMatch(e -> ShopTypeEnum.VC.getCode().equals(e.getType()));
        Map<Integer, Boolean> shopTypeMap = shopAuths.stream().collect(Collectors.toMap(ShopAuth::getId, e -> ShopTypeEnum.VC.getCode().equals(e.getType())));
        page.addAllRows(portfolioData.getRows().stream().map(key -> build(param, key, isCurrency, advertiseStrategyStatusMap.get(key.getPortfolioId()), shopTypeMap.get(key.getShopId()))).collect(Collectors.toList()));
        pageBuilder.setPage(page);
        builder.setData(pageBuilder);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 多店铺广告活动组合汇总接口
     * 旧支持单店铺接口： {@link AdCommonRpcService#getAllPortfolioAggregateDataList}
     */
    @Override
    public void getAllPortfolioAggregateData(GetPortfolioDataRequest request, StreamObserver<GetAllPortfolioAggregateDataResponse> responseObserver) {
        log.info("getAllPortfolioAggregateData = {}", request);
        PortfolioPageParam param = buildRequest(request);
        String message = check(param);
        GetAllPortfolioAggregateDataResponse.Builder builder = GetAllPortfolioAggregateDataResponse.newBuilder();
        if (StringUtils.isNotBlank(message)) {
            builder.setCode(Result.ERROR);
            builder.setMsg(message);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        builder.setCode(Result.SUCCESS);
        MultiplePortfolioPageVo multiplePortfolioPageVo = multiplePortfolioService.getAllPortfolioAggregateData(param);
        GetAllPortfolioAggregateDataResponse.PortfolioHomeVo.Builder homeVo = GetAllPortfolioAggregateDataResponse.PortfolioHomeVo.newBuilder();
        AdNewAggregateDataRpcVo.Builder aggregateDataRpc = AdNewAggregateDataRpcVo.newBuilder();
        if (CollectionUtils.isNotEmpty(multiplePortfolioPageVo.getDayPerformanceVos())) {
            homeVo.addAllDay(multiplePortfolioPageVo.getDayPerformanceVos());
        }
        if (CollectionUtils.isNotEmpty(multiplePortfolioPageVo.getWeekPerformanceVos())) {
            homeVo.addAllWeek(multiplePortfolioPageVo.getWeekPerformanceVos());
        }
        if (CollectionUtils.isNotEmpty(multiplePortfolioPageVo.getMonthPerformanceVos())) {
            homeVo.addAllMonth(multiplePortfolioPageVo.getMonthPerformanceVos());
        }
        boolean isVc = false;
        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
            List<VcShopAuth> listByIdList = vcShopAuthDao.getListByIdList(param.getShopIdList());
            if (CollectionUtils.isNotEmpty(listByIdList)) {
                isVc = true;
            }
        }

        build(aggregateDataRpc, multiplePortfolioPageVo, param, isVc);
        homeVo.setAggregateDataVo(aggregateDataRpc);
        builder.setData(homeVo);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 多店铺广告活动组合导出接口
     * 旧支持单店铺接口： {@link AdExportRpcService#adPortfolioData}
     */
    @Override
    public void exportPortfolioData(GetPortfolioDataRequest request, StreamObserver<ExportPortfolioDataResponse> responseObserver) {
        log.info("exportPortfolioData = {}", request);
        PortfolioPageParam param = buildRequest(request);
        String message = check(param);
        ExportPortfolioDataResponse.Builder builder = ExportPortfolioDataResponse.newBuilder();
        if (StringUtils.isNotBlank(message)) {
            builder.setCode(Result.ERROR);
            builder.setMsg(message);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        param.setUuid(request.getPageSign());
        param.setIsAdminUser(BooleanUtils.toBoolean(MDC.get(Constants.ADMIN_USER)));
        //插入任务
        Long id = adManagePageExportTaskService.saveExportTask(request.getPuid(), request.getUid(), 0,
                AdManagePageExportTaskTypeEnum.MULTIPLE_PORTFOLIO, param.getStartDate().replaceAll("-", ""), param.getEndDate().replaceAll("-", ""), param);
        if (id == null) {
            builder.setCode(-1);
            builder.setMsg("新建任务异常，请联系管理员!");
        } else {
            builder.setCode(0);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private void build(AdNewAggregateDataRpcVo.Builder voBuilder, MultiplePortfolioPageVo multiplePortfolioPageVo, PortfolioPageParam param, boolean isVc) {
        voBuilder.setSumCampaignNumber(StringUtil.getSafeValue(multiplePortfolioPageVo.getCampaignNumber()));
        voBuilder.setImpressions(StringUtil.getSafeLongValue(multiplePortfolioPageVo.getImpressions()));
        voBuilder.setClicks(StringUtil.getSafeLongValue(multiplePortfolioPageVo.getClicks()));
        voBuilder.setCtr(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCtr()));
        voBuilder.setCvr(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCvr()));
        voBuilder.setAcos(StringUtil.getSafePlainString(multiplePortfolioPageVo.getAcos()));
        voBuilder.setAcots(StringUtil.getSafePlainString(multiplePortfolioPageVo.getAcots()));
        voBuilder.setAsots(StringUtil.getSafePlainString(multiplePortfolioPageVo.getAsots()));
        voBuilder.setAdOrderNum(StringUtil.getSafeLongValue(multiplePortfolioPageVo.getAdOrderNum()));
        voBuilder.setAdCost(StringUtil.getSafePlainString(multiplePortfolioPageVo.getAdCost()));
        voBuilder.setAdCostPerClick(StringUtil.getSafePlainString(multiplePortfolioPageVo.getAdCostPerClick()));
        voBuilder.setAdSale(StringUtil.getSafePlainString(multiplePortfolioPageVo.getAdSale()));
        voBuilder.setRoas(StringUtil.getSafePlainString(multiplePortfolioPageVo.getRoas()));
        voBuilder.setCurrency(multiplePortfolioPageVo.getCurrency());
        //每笔订单花费
        voBuilder.setCpa(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCpa()));
        //广告销量
        voBuilder.setAdSaleNum(StringUtil.getSafeLongValue(multiplePortfolioPageVo.getAdSaleNum()));
        // 花费占比
        voBuilder.setAdCostPercentage("100.00");
        // 销售额占比
        voBuilder.setAdSalePercentage("100.00");
        // 订单量占比
        voBuilder.setAdOrderNumPercentage("100.00");
        // 销量占比
        voBuilder.setAdSaleNumPercentage("100.00");
        // 广告笔单价 广告销售额÷广告订单量×100%
        voBuilder.setAdvertisingUnitPrice(StringUtil.getSafePlainString(multiplePortfolioPageVo.getAdvertisingUnitPrice()));
        if (param.getIsCompare() != null && param.getIsCompare()) {
            voBuilder.setCompareImpressions(StringUtil.getSafeLongValue(multiplePortfolioPageVo.getCompareImpressions()));
            //曝光环比值
            voBuilder.setCompareImpressionsRate(StringUtil.getSafeValueSplit(multiplePortfolioPageVo.getCompareRateImpressions()));

            voBuilder.setCompareClicks(StringUtil.getSafeLongValue(multiplePortfolioPageVo.getCompareClicks()));
            //点击量环比值
            voBuilder.setCompareClicksRate(StringUtil.getSafeValueSplit(multiplePortfolioPageVo.getCompareRateClicks()));

            voBuilder.setCompareCtr(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareCtr()));
            //ctr环比值
            voBuilder.setCompareCtrRate(StringUtil.getSafeValueSplit(multiplePortfolioPageVo.getCompareRateCtr()));

            voBuilder.setCompareCvr(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareCvr()));
            //cvr环比值
            voBuilder.setCompareCvrRate(StringUtil.getSafeValueSplit(multiplePortfolioPageVo.getCompareRateCvr()));

            voBuilder.setCompareAcos(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareAcos()));
            //Acos环比值
            voBuilder.setCompareAcosRate(StringUtil.getSafeValueSplit(multiplePortfolioPageVo.getCompareRateAcos()));

            voBuilder.setCompareAcots(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareAcots()));
            //Acots环比值
            voBuilder.setCompareAcotsRate(StringUtil.getSafeValueSplit(multiplePortfolioPageVo.getCompareRateAcots()));

            voBuilder.setCompareAsots(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareAsots()));
            //Asots环比值
            voBuilder.setCompareAsotsRate(StringUtil.getSafeValueSplit(multiplePortfolioPageVo.getCompareRateAsots()));

            voBuilder.setCompareAdOrderNum(StringUtil.getSafeLongValue(multiplePortfolioPageVo.getCompareAdOrderNum()));
            //AdOrderNum环比值
            voBuilder.setCompareAdOrderNumRate(StringUtil.getSafeValueSplit(multiplePortfolioPageVo.getCompareRateAdOrderNum()));

            voBuilder.setCompareAdCost(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareAdCost()));
            //AdCost环比值
            voBuilder.setCompareAdCostRate(StringUtil.getSafeValueSplit(multiplePortfolioPageVo.getCompareRateAdCost()));

            voBuilder.setCompareAdCostPerClick(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareAdCostPerClick()));
            //AdCostPerClick环比值
            voBuilder.setCompareAdCostPerClickRate(StringUtil.getSafeValueSplit(multiplePortfolioPageVo.getCompareRateAdCostPerClick()));

            voBuilder.setCompareAdSale(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareAdSale()));
            //AdSale环比值
            voBuilder.setCompareAdSaleRate(StringUtil.getSafeValueSplit(multiplePortfolioPageVo.getCompareRateAdSale()));

            voBuilder.setCompareRoas(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareRoas()));
            //Roas环比值
            voBuilder.setCompareRoasRate(StringUtil.getSafeValueSplit(multiplePortfolioPageVo.getCompareRateRoas()));

            voBuilder.setCompareCpa(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareCpa()));
            //Cpa环比值
            voBuilder.setCompareCpaRate(StringUtil.getSafeValueSplit(multiplePortfolioPageVo.getCompareRateCpa()));

            voBuilder.setCompareAdSaleNum(StringUtil.getSafeLongValue(multiplePortfolioPageVo.getCompareAdSaleNum()));
            //OrderNum比值
            voBuilder.setCompareAdSaleNumRate(StringUtil.getSafeValueSplit(multiplePortfolioPageVo.getCompareRateAdSaleNum()));
            // 广告笔单价 环比
            voBuilder.setCompareAdvertisingUnitPrice(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareAdvertisingUnitPrice()));
            voBuilder.setCompareAdvertisingUnitPriceRate(StringUtil.getSafeValueSplit(multiplePortfolioPageVo.getCompareRateAdvertisingUnitPrice()));
        }
        if (isVc) {
            voBuilder.setAcots("-");
            voBuilder.setCompareAcotsRate("-");
            voBuilder.setCompareAcots("-");
            voBuilder.setAsots("-");
            voBuilder.setCompareAsotsRate("-");
            voBuilder.setCompareAsots("-");
        }
    }

    private GetPortfolioDataResponse.PortfolioHomeVo.Page.PortfolioPageVo build(PortfolioPageParam param, MultiplePortfolioPageVo multiplePortfolioPageVo, Boolean currency, String advertiseStrategyStatus, boolean isVc) {
        GetPortfolioDataResponse.PortfolioHomeVo.Page.PortfolioPageVo.Builder voBuilder = GetPortfolioDataResponse.PortfolioHomeVo.Page.PortfolioPageVo.newBuilder();
        voBuilder.setId(multiplePortfolioPageVo.getId());
        voBuilder.setShopId(multiplePortfolioPageVo.getShopId());
        voBuilder.setPortfolioId(multiplePortfolioPageVo.getPortfolioId());
        voBuilder.setIsHidden(multiplePortfolioPageVo.getIsHidden() == 1);
        voBuilder.setShopName(multiplePortfolioPageVo.getShopName());
        voBuilder.setSiteName(multiplePortfolioPageVo.getSiteName());
        voBuilder.setCurrency(AmznEndpoint.getByMarketplaceId(multiplePortfolioPageVo.getMarketplaceId()).getCurrencyCode().name());
        if (StringUtils.isNotBlank(multiplePortfolioPageVo.getMarketplaceId())) {
            voBuilder.setMarketplaceId(multiplePortfolioPageVo.getMarketplaceId());
        }
        voBuilder.setPortfolioName(StringUtils.isNotBlank(multiplePortfolioPageVo.getName()) ? multiplePortfolioPageVo.getName() : "广告组合未命名");
        voBuilder.setState("enabled");
        if (StringUtils.isNotBlank(multiplePortfolioPageVo.getServingStatus())) {
            voBuilder.setServingStatus(multiplePortfolioPageVo.getServingStatus());
        }
        if (StringUtils.isNotBlank(multiplePortfolioPageVo.getServingStatusDec())) {
            voBuilder.setServingStatusDec(multiplePortfolioPageVo.getServingStatusDec());
        }
        if (StringUtils.isNotBlank(multiplePortfolioPageVo.getServingStatusName())) {
            voBuilder.setServingStatusName(multiplePortfolioPageVo.getServingStatusName());
        }
        if (StringUtils.isNotBlank(advertiseStrategyStatus)) {
            voBuilder.setTemplateName(advertiseStrategyStatus);
        }
        voBuilder.setPolicy(StringUtils.isNotBlank(multiplePortfolioPageVo.getPolicy()) ? multiplePortfolioPageVo.getPolicy() : "noBudget");
        voBuilder.setAmount(StringUtil.getSafePlainString(multiplePortfolioPageVo.getAmount()));
        voBuilder.setBudgetStartDate(StringUtil.getSafeValue(multiplePortfolioPageVo.getBudgetStartDate(), "-"));
        voBuilder.setBudgetEndDate(StringUtil.getSafeValue(multiplePortfolioPageVo.getBudgetEndDate(), "-"));
        voBuilder.setCampaignNumber(StringUtil.getSafeValue(multiplePortfolioPageVo.getCampaignNumber()));
        voBuilder.setImpressions(StringUtil.getSafeLongValue(multiplePortfolioPageVo.getImpressions()));
        voBuilder.setClicks(StringUtil.getSafeLongValue(multiplePortfolioPageVo.getClicks()));
        voBuilder.setCtr(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCtr()));
        voBuilder.setCvr(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCvr()));
        voBuilder.setAcos(StringUtil.getSafePlainString(multiplePortfolioPageVo.getAcos()));
        voBuilder.setAcots(StringUtil.getSafePlainString(multiplePortfolioPageVo.getAcots()));
        voBuilder.setAsots(StringUtil.getSafePlainString(multiplePortfolioPageVo.getAsots()));
        voBuilder.setAdOrderNum(StringUtil.getSafeLongValue(multiplePortfolioPageVo.getAdOrderNum()));
        voBuilder.setAdCost(StringUtil.getSafePlainString(multiplePortfolioPageVo.getAdCost()));
        voBuilder.setAdCostPerClick(StringUtil.getSafePlainString(multiplePortfolioPageVo.getAdCostPerClick()));
        voBuilder.setAdSale(StringUtil.getSafePlainString(multiplePortfolioPageVo.getAdSale()));
        voBuilder.setRoas(StringUtil.getSafePlainString(multiplePortfolioPageVo.getRoas()));
        voBuilder.setIsAmountPricing(multiplePortfolioPageVo.getIsAmountPricing());
        voBuilder.setPricingAmountState(multiplePortfolioPageVo.getPricingAmountState());
        //每笔订单花费
        voBuilder.setCpa(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCpa()));
        //广告销量
        voBuilder.setAdSaleNum(StringUtil.getSafeLongValue(multiplePortfolioPageVo.getAdSaleNum()));
        // 花费占比
        voBuilder.setAdCostPercentage(currency ? "-" : StringUtil.getSafePlainString(multiplePortfolioPageVo.getAdCostPercentage()));
        // 销售额占比
        voBuilder.setAdSalePercentage(currency ?"-" : StringUtil.getSafePlainString(multiplePortfolioPageVo.getAdSalePercentage()));
        // 订单量占比
        voBuilder.setAdOrderNumPercentage(StringUtil.getSafePlainString(multiplePortfolioPageVo.getAdOrderNumPercentage()));
        // 销量占比
        voBuilder.setAdSaleNumPercentage(StringUtil.getSafePlainString(multiplePortfolioPageVo.getAdSaleNumPercentage()));
        // 广告笔单价 广告销售额÷广告订单量×100%
        voBuilder.setAdvertisingUnitPrice(StringUtil.getSafePlainString(multiplePortfolioPageVo.getAdvertisingUnitPrice()));
        if (param.getIsCompare() != null && param.getIsCompare()) {
            voBuilder.setCompareImpressions(StringUtil.getSafeLongValue(multiplePortfolioPageVo.getCompareImpressions()));
            //曝光环比值
            voBuilder.setCompareImpressionsRate(multiplePortfolioPageVo.getCompareRateImpressions());

            voBuilder.setCompareClicks(StringUtil.getSafeLongValue(multiplePortfolioPageVo.getCompareClicks()));
            //点击量环比值
            voBuilder.setCompareClicksRate(multiplePortfolioPageVo.getCompareRateClicks());

            voBuilder.setCompareCtr(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareCtr()));
            //ctr环比值
            voBuilder.setCompareCtrRate(multiplePortfolioPageVo.getCompareRateCtr());

            voBuilder.setCompareCvr(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareCvr()));
            //cvr环比值
            voBuilder.setCompareCvrRate(multiplePortfolioPageVo.getCompareRateCvr());

            voBuilder.setCompareAcos(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareAcos()));
            //Acos环比值
            voBuilder.setCompareAcosRate(multiplePortfolioPageVo.getCompareRateAcos());

            voBuilder.setCompareAcots(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareAcots()));
            //Acots环比值
            voBuilder.setCompareAcotsRate(multiplePortfolioPageVo.getCompareRateAcots());

            voBuilder.setCompareAsots(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareAsots()));
            //Asots环比值
            voBuilder.setCompareAsotsRate(multiplePortfolioPageVo.getCompareRateAsots());

            voBuilder.setCompareAdOrderNum(StringUtil.getSafeLongValue(multiplePortfolioPageVo.getCompareAdOrderNum()));
            //AdOrderNum环比值
            voBuilder.setCompareAdOrderNumRate(multiplePortfolioPageVo.getCompareRateAdOrderNum());

            voBuilder.setCompareAdCost(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareAdCost()));
            //AdCost环比值
            voBuilder.setCompareAdCostRate(multiplePortfolioPageVo.getCompareRateAdCost());

            voBuilder.setCompareAdCostPerClick(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareAdCostPerClick()));
            //AdCostPerClick环比值
            voBuilder.setCompareAdCostPerClickRate(multiplePortfolioPageVo.getCompareRateAdCostPerClick());

            voBuilder.setCompareAdSale(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareAdSale()));
            //AdSale环比值
            voBuilder.setCompareAdSaleRate(multiplePortfolioPageVo.getCompareRateAdSale());

            voBuilder.setCompareRoas(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareRoas()));
            //Roas环比值
            voBuilder.setCompareRoasRate(multiplePortfolioPageVo.getCompareRateRoas());

            voBuilder.setCompareCpa(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareCpa()));
            //Cpa环比值
            voBuilder.setCompareCpaRate(multiplePortfolioPageVo.getCompareRateCpa());

            voBuilder.setCompareAdSaleNum(StringUtil.getSafeLongValue(multiplePortfolioPageVo.getCompareAdSaleNum()));
            //OrderNum比值
            voBuilder.setCompareAdSaleNumRate(multiplePortfolioPageVo.getCompareRateAdSaleNum());
            // 广告笔单价 环比
            voBuilder.setCompareAdvertisingUnitPrice(StringUtil.getSafePlainString(multiplePortfolioPageVo.getCompareAdvertisingUnitPrice()));
            voBuilder.setCompareAdvertisingUnitPriceRate(multiplePortfolioPageVo.getCompareRateAdvertisingUnitPrice());
        }


        if (isVc) {
            voBuilder.setAcots("-");
            voBuilder.setCompareAcotsRate("-");
            voBuilder.setCompareAcots("-");
            voBuilder.setAsots("-");
            voBuilder.setCompareAsotsRate("-");
            voBuilder.setCompareAsots("-");
        }
        return voBuilder.build();
    }

    private PortfolioPageParam buildRequest(GetPortfolioDataRequest request) {
        PortfolioPageParam param = new PortfolioPageParam();
        BeanUtils.copyProperties(request, param);
        param.setPuid(request.getPuid());
        param.setShopIdList(request.getShopIdList());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setUid(request.getUid());
        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate());
        param.setCompareEndDate(request.getCompareEndDate());
        if (request.hasIsHidden()) {
            param.setIsHidden(request.getIsHidden());
        }
        if (request.hasStartDate()) {
            param.setStartDate(request.getStartDate());
        }
        if (request.hasEndDate()) {
            param.setEndDate(request.getEndDate());
        }
        if (StringUtils.isNotBlank(request.getPortfolioId())) {
            param.setPortfolioIdList(StringUtil.splitStr(request.getPortfolioId()));
        }
        //对批量查询做处理
        if (StringUtils.isNotBlank(request.getSearchValue()) && request.getSearchValue().contains("%±%")) {
            param.setSearchValueList(StringUtil.splitStr(request.getSearchValue().trim(), StringUtil.SPECIAL_COMMA));
            param.setSearchValue(null);
        }
        if (param.getSearchValue() != null && !SearchTypeEnum.EXACT.getValue().equalsIgnoreCase(param.getSearchType())) {
            String searchValue = param.getSearchValue();
            if (searchValue.contains("\\")) {
                searchValue = searchValue.replace("\\", "\\\\");
            }
            if (searchValue.contains("%")) {
                searchValue = searchValue.replace("%", "\\%");
            }
            // 将替换后的字符串重新设置回 param 对象
            param.setSearchValue(searchValue);
        }
        param.setIsAdminUser(Boolean.parseBoolean(MDC.get(Constants.ADMIN_USER)));

        return param;
    }

    private String check(PortfolioPageParam param) {
        if (param == null || CollectionUtils.isEmpty(param.getShopIdList())) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(20);
        }
        if (param.getIsHidden() == null) {
            param.setIsHidden(false);
        }
        if (param.getSearchType() == null) {
            param.setSearchType("exact");
        }
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
        } else {
            param.setStartDate(param.getStartDate());
            param.setEndDate(param.getEndDate());
        }
        return null;
    }

}
