package com.meiyunji.sponsored.api.batchCreate;

import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.RpcTaskTerminationBatchSpServiceGrpc;
import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.TaskTerminationRequest;
import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.TaskTerminationResponse;
import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchTaskDao;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateTaskStatusEnum;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchTask;
import com.meiyunji.sponsored.service.batchCreate.service.IAsinViewBatchSpService;
import com.meiyunji.sponsored.service.batchCreate.service.IKeywordAndTargetingBatchSpService;
import com.meiyunji.sponsored.service.batchCreate.service.INeKeywordAndTargetingService;
import com.meiyunji.sponsored.service.batchCreate.service.impl.CampaignBatchSpService;
import com.meiyunji.sponsored.service.batchCreate.service.impl.GroupBatchSpService;
import com.meiyunji.sponsored.service.batchCreate.task.BatchTaskStatusUpdateTask;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * @author: ys
 * @date: 2023/11/29 11:29
 * @describe:
 */

@GRpcService
@Slf4j
public class TaskTerminationRpcService  extends RpcTaskTerminationBatchSpServiceGrpc.RpcTaskTerminationBatchSpServiceImplBase {

    @Autowired
    private IAmazonAdBatchTaskDao amazonAdBatchTaskDao;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private CampaignBatchSpService campaignBatchSpService;

    @Autowired
    private GroupBatchSpService groupBatchSpService;

    @Autowired
    private IAsinViewBatchSpService asinViewBatchSpService;

    @Autowired
    private IKeywordAndTargetingBatchSpService keywordAndTargetingBatchSpService;

    @Autowired
    private INeKeywordAndTargetingService neKeywordAndTargetingService;

    private static final String TERMINATE_DISTRIBUTE_LOCK = "terminate_batch_sp_task:";

    @Autowired
    private BatchTaskStatusUpdateTask batchTaskStatusUpdateTask;

    private ExecutorService threadPool = ThreadPoolUtil.getTerminateBatchSpSyncPool();


    @Override
    public void terminate(TaskTerminationRequest request, StreamObserver<TaskTerminationResponse> responseObserver) {
        TaskTerminationResponse.Builder builder = TaskTerminationResponse.newBuilder();
        TaskTerminationResponse.TaskTerminationResponseVo.Builder responseBuilder = TaskTerminationResponse
                .TaskTerminationResponseVo.newBuilder();
        //根据taskId对任务进行终止，先查询对应taskId的任务
        //考虑多线程并发，增加分布式锁，同一时间只能有一个线程操作该task进行终止，不能重复终止
        //只有在进行中的顶层batch_task才能被终止，即status=0进行中的任务才能被终止
        //将batch_task状态设置为:3 终止中，更新stopTime字段
        //需要更新广告活动，广告组，投放，否定投放层级，将所有状态都设置为4，同时只有处于0中的状态才能被更新
        //当其中某个任务状态更新失败时，合适的处理是全局回滚，目前暂不处理
        Integer puid = request.getPuid();
        Integer shopId = request.getShopId();
        Long taskId = request.getTaskId();

        if (Objects.isNull(taskId)) {
            //返回response
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        //更新顶层任务状态为4，加分布式锁，返回顶层任务状态被并发修改，从而导致下层子任务被多次终止
        //由于task不会跨店铺，所以分布式锁的key为:puid+taskId
        RLock lock = redissonClient.getLock(TERMINATE_DISTRIBUTE_LOCK + puid + ":" + taskId);
        boolean b;
        boolean update = false;
        try {
            b = lock.tryLock(0, 5, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.info("task termination tryLock err,puid：{}, taskId:{}", puid, taskId);
            return ;
        }
        if (!b) {
            log.info("task termination in progress,puid：{}, taskId:{}", puid, taskId);
            return ;
        }
        //update top task status to 3
        try {
            List<AmazonAdBatchTask> result =  amazonAdBatchTaskDao
                    .listByCondition(puid, new ConditionBuilder.Builder().equalTo("id", taskId).build());
            AmazonAdBatchTask task = null;
            //只有顶层task状态为提交中或提交完成有失败的状态可以进行终止
            if (CollectionUtils.isEmpty(result) || (task = result.get(0)) == null
                    || SpBatchCreateTaskStatusEnum.FINISH_SUCCESS == SpBatchCreateTaskStatusEnum
                    .getSpBatchCreateTaskStatusEnumByCode(task.getStatus())||
                    SpBatchCreateTaskStatusEnum.TERMINATING == SpBatchCreateTaskStatusEnum
                    .getSpBatchCreateTaskStatusEnumByCode(task.getStatus())) {
                //顶层task需要添加一个终止中的状态，终止中的任务不允许再次终止
                log.error("batch task not allow to terminate,puid：{}, taskId:{},  status:{}", puid, taskId, task.getStatus());
                responseBuilder.setResult(false);
                builder.setData(responseBuilder.build());
                builder.setCode(Result.ERROR);
                builder.setMsg("该任务状态不允许终止");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }
            int updateResult = amazonAdBatchTaskDao
                    .terminateTask(puid, taskId, SpBatchCreateTaskStatusEnum.TERMINATING.getCode(),
                            Collections.singletonList(SpBatchCreateTaskStatusEnum.DOING.getCode()));
            if (updateResult == 1) {
                update = true;
            }
        } finally {
            lock.unlock();
        }

        //async
        //只有成功更新顶层任务状态的线程才可以继续终止其子任务
        if (update) {
            threadPool.execute(() -> {
                //各层级之间的终止不相互影响，也可以不严格按照广告活动，广告组，投放，否定投放的顺序来执行
                try {
                    //终止广告活动创建
                    campaignBatchSpService.terminateCampaignsByTaskId(puid, taskId);
                } catch (Exception e) {
                    log.error("terminate campaign task level failed, puid:{}, taskId:{}, e:{}", puid, taskId, e.getMessage());
                }
                try {
                    //终止广告组创建
                    groupBatchSpService.terminateGroupTask(puid, taskId);
                } catch (Exception e) {
                    log.error("terminate group task level failed, puid:{}, taskId:{}, e:{}", puid, taskId, e.getMessage());
                }
                try {
                    //终止广告产品创建
                    asinViewBatchSpService.terminateProducts(puid, shopId, taskId);
                } catch (Exception e) {
                    log.error("terminate asin task level failed, puid:{}, taskId:{}, e:{}", puid, taskId, e.getMessage());
                }
                try {
                    //以task为维度的终止，但是需要判断按广告组去判断
                    //终止投放创建
                    keywordAndTargetingBatchSpService.terminateKeywordAndTargetingTask(puid, shopId, taskId);
                } catch (Exception e) {
                    log.error("terminate keyword task level failed, puid:{}, taskId:{}, e:{}", puid, taskId, e.getMessage());
                }
                try {
                    //终止否定投放创建
                    neKeywordAndTargetingService.terminateNeKeywordAndKeywordTask(puid, shopId, taskId);
                } catch (Exception e) {
                    log.error("terminate ne keyword task level failed, puid:{}, taskId:{}, e:{}", puid, taskId, e.getMessage());
                }

                //最后检查各子层级后更新顶层task状态
                batchTaskStatusUpdateTask.update(puid, shopId, taskId);
            });
            responseBuilder.setResult(true);
            builder.setData(responseBuilder.build());
            builder.setCode(Result.SUCCESS);
        } else {
            responseBuilder.setResult(false);
            builder.setData(responseBuilder.build());
            builder.setCode(Result.ERROR);
            builder.setMsg("任务已提交完成，无法终止");
            log.info("terminate batch task failed, puid：{}, shopId:{}, taskId:{}", puid, shopId, taskId);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
