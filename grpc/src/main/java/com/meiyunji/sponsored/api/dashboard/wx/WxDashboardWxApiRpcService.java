package com.meiyunji.sponsored.api.dashboard.wx;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.api.dashboard.utils.DashboardHourlyChartUtil;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.grpc.dashboard.wx.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.dashboard.dto.*;
import com.meiyunji.sponsored.service.dashboard.service.DashBoardService;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;

import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@GRpcService
@Slf4j
public class WxDashboardWxApiRpcService extends WxDashBoardApiServiceGrpc.WxDashBoardApiServiceImplBase {

    private final DashBoardService dashBoardService;
    private final IScVcShopAuthDao shopAuthDao;
    private final IAmazonAdCampaignAllDao amazonAdCampaignAllDao;

    public WxDashboardWxApiRpcService(DashBoardService dashBoardService, IScVcShopAuthDao shopAuthDao,
                                      IAmazonAdCampaignAllDao amazonAdCampaignAllDao) {
        this.dashBoardService = dashBoardService;
        this.shopAuthDao = shopAuthDao;
        this.amazonAdCampaignAllDao = amazonAdCampaignAllDao;
    }

    /**
     * 全部广告活动
     * 本期以及上期(效果，转化，展示)数据
     * @param request
     * @param responseObserver
     */
    @Override
    public void wxAllCampIndicatorData(WxAllCampIndicatorDataRequest request, StreamObserver<WxAllCampIndicatorDataResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            int puid = request.getPuid();
            List<Integer> shopIdList = request.getShopIdList();
            String currency = request.getCurrency();
            String adType = request.getAdType();

            LocalDate startDate = LocalDate.parse(request.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDate endDate = LocalDate.parse(request.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);

            AdAllCampDashboradDataDto adAllCampDashboradDataDto = dashBoardService.getWxAllCampIndicatorData(
                    puid, shopIdList, adType, currency, startDate, endDate);

            WxAllCampIndicatorDataResponse.Builder builder = WxAllCampIndicatorDataResponse.newBuilder();

            WxAllCampIndicatorDataResponse.Data.Builder dataBuilder = WxAllCampIndicatorDataResponse.Data.newBuilder();
            dataBuilder.setImpressions(adAllCampDashboradDataDto.getImpressions());
            dataBuilder.setClicks(adAllCampDashboradDataDto.getClicks());
            dataBuilder.setClickRate(adAllCampDashboradDataDto.getClickRate().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setCpc(adAllCampDashboradDataDto.getCpc().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setSalesConversionRate(adAllCampDashboradDataDto.getSalesConversionRate().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setCost(adAllCampDashboradDataDto.getCost().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setAdOrder(adAllCampDashboradDataDto.getAdOrder());
            dataBuilder.setAdSales(adAllCampDashboradDataDto.getAdSales().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setAcos(adAllCampDashboradDataDto.getAcos().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setRoas(adAllCampDashboradDataDto.getRoas().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setSpc(adAllCampDashboradDataDto.getClicks() == 0 ? 0.00 : adAllCampDashboradDataDto.getSpc());

            dataBuilder.setPerImpressions(adAllCampDashboradDataDto.getPerImpressions());
            dataBuilder.setPerClicks(adAllCampDashboradDataDto.getPerClicks());
            dataBuilder.setPerClickRate(adAllCampDashboradDataDto.getPerClickRate().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setPerCpc(adAllCampDashboradDataDto.getPerCpc().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setPerSalesConversionRate(adAllCampDashboradDataDto.getPerSalesConversionRate().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setPerCost(adAllCampDashboradDataDto.getPerCost().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setPerAdOrder(adAllCampDashboradDataDto.getPerAdOrder());
            dataBuilder.setPerAdSales(adAllCampDashboradDataDto.getPerAdSales().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setPerAcos(adAllCampDashboradDataDto.getPerAcos().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setPerRoas(adAllCampDashboradDataDto.getPerRoas().setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataBuilder.setPerSpc(adAllCampDashboradDataDto.getPerClicks() == 0 ? 0.00 : adAllCampDashboradDataDto.getPerSpc());

            builder.setCode(Result.SUCCESS);
            builder.setData(dataBuilder.build());
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("error: ", e);
            responseObserver.onError(e);
        }

    }

    @Override
    public void getWxCampaignTopTen(WxAllCampIndicatorDataRequest request, StreamObserver<WxAllCampTopTenResponse> responseObserver) {
        log.info("request:{}", request);
        try {
            int puid = request.getPuid();
            List<Integer> shopIdList = request.getShopIdList();
            String adType = request.getAdType();
            String orderBy = request.getOrderBy();
            String currency = request.getCurrency();

            LocalDate startDate = LocalDate.parse(request.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDate endDate = LocalDate.parse(request.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
            List<WxCampaignTopTenDto> wxCampaignTopTenDtos = dashBoardService.getWxCampaignTopTen(puid, shopIdList, adType, currency, orderBy, startDate, endDate);
            WxAllCampTopTenResponse.Builder builder = WxAllCampTopTenResponse.newBuilder();
            builder.addAllData(wxCampaignTopTenDtos.stream().map(item -> {
                WxAllCampTopTenResponse.Data.Builder dataBuilder = WxAllCampTopTenResponse.Data.newBuilder();
                dataBuilder.setMarketplaceId(item.getMarketplaceId());
                dataBuilder.setShopId(item.getShopId());
                if (StringUtils.isNotBlank(item.getShopName())) {
                    dataBuilder.setShopName(item.getShopName());
                }
                dataBuilder.setCampaignId(item.getCampaignId());
                if (StringUtils.isNotBlank(item.getCampaignName())) {
                    dataBuilder.setCampaignName(item.getCampaignName());
                }
                dataBuilder.setAdType(item.getAdType());
                dataBuilder.setCost(item.getCost().doubleValue());
                dataBuilder.setAdOrder(item.getAdOrder());
                dataBuilder.setAdSales(item.getAdSales().doubleValue());
                dataBuilder.setAcos(item.getAcos().doubleValue());
                dataBuilder.setSpc(item.getSpc().doubleValue());
                dataBuilder.setCpc(item.getCpc().doubleValue());
                dataBuilder.setSalesConversionRate(item.getSalesConversionRate().doubleValue());
                dataBuilder.setImpressions(item.getImpressions());
                dataBuilder.setClick(item.getClicks());
                dataBuilder.setClickRate(item.getClickRate().doubleValue());
                return dataBuilder.build();
            }).collect(Collectors.toList()));
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("error: ", e);
            responseObserver.onError(e);
        }
    }

    /**
     * sp实时播报
     * 本期以及上期(效果，转化，展示)数据
     * @param request
     * @param responseObserver
     */
    @Override
    public void wxRealTimeHourlyData(WxRealTimeHourlyDataRequest request, StreamObserver<WxRealTimeHourlyDataResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            int puid = request.getPuid();
            List<Integer> shopIdList = request.getShopIdList();
            int hour = request.getHour();
            String currency = request.getCurrency();

            //小时级数据,目前只有SP类型的数据
            DashboardHourReportDto dto = dashBoardService.realTimeHourlyData(puid, shopIdList, hour, currency, Lists.newArrayList());

            WxRealTimeHourlyDataResponse.Builder builder = WxRealTimeHourlyDataResponse.newBuilder();
            WxRealTimeHourlyDataResponse.Data.Builder dataBuilder =
                    WxRealTimeHourlyDataResponse.Data.newBuilder();
            dataBuilder.setClicks(dto.getClicks());
            dataBuilder.setImpressions(dto.getImpressions());
            dataBuilder.setClickRate(dto.getClickRate());
            dataBuilder.setCpc(dto.getCpc());
            dataBuilder.setSalesConversionRate(dto.getSalesConversionRate());
            dataBuilder.setCost(dto.getCost());
            dataBuilder.setAdOrder(dto.getAdOrder());
            dataBuilder.setAdSales(dto.getAdSales());
            dataBuilder.setAcos(dto.getAcos());
            dataBuilder.setSpc(dto.getClicks() == 0 ? 0.00 : dto.getSpc()); // 广告销量额/广告点击量
            //小于1分钟显示一分钟, 无数显示20分钟前
            long minutes = Duration.between(LocalDateTime.now(ZoneId.of("UTC")), LocalDateTime.parse(dto.getLastUpdateAt(),
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).toMinutes();
            dataBuilder.setLastUpdateAt(Math.max(1, Math.min(20, Math.abs(minutes))) + "分钟前");

            builder.setCode(Result.SUCCESS);
            builder.setData(dataBuilder.build());

            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("error: ", e);
            responseObserver.onError(e);
        }
    }

    public void getWxHourlyCampaignTopFive(WxRealTimeHourlyDataRequest request, StreamObserver<WxHourlyTopFiveResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            int puid = request.getPuid();
            List<Integer> shopIdList = request.getShopIdList();
            int hour = request.getHour();
            String currency = request.getCurrency();
            String orderBy = request.getOrderBy();

            //小时级数据,目前只有SP类型的数据
            List<WxCampaignTopTenDto> dtos = dashBoardService.getHourlyDataIndicator(puid, shopIdList, hour, currency, orderBy);
            // 获取店铺名称
            List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(puid, shopIdList);
            Map<String, String> shopNameMap = shopAuths.stream().filter(Objects::nonNull)
                    .collect(Collectors.toMap(item -> item.getSellingPartnerId().concat("#" + item.getMarketplaceId())
                            , ShopAuth::getName));
            // 获取广告活动名称
            Map<String, String> campaignNameMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(dtos)) {
                Map<Integer, String> shopIdParseMarketplaceIdMap = shopAuths.stream().filter(Objects::nonNull)
                        .collect(Collectors.toMap(ShopAuth::getId,
                                item -> item.getSellingPartnerId().concat("#" + item.getMarketplaceId()), (a, b) -> a));
                List<String> campaignIds = dtos.stream().filter(item -> StringUtils.isNotBlank(item.getCampaignId()))
                        .map(WxCampaignTopTenDto::getCampaignId).collect(Collectors.toList());
                campaignNameMap = amazonAdCampaignAllDao.getListByShopIdsAndCampaignIds(puid, shopIdList, campaignIds, Constants.SP)
                        .stream().filter(Objects::nonNull)
                        .collect(Collectors.toMap(item ->
                                        shopIdParseMarketplaceIdMap.get(item.getShopId()).concat("#" + item.getCampaignId()),
                                AmazonAdCampaignAll::getName, (a, b) -> a));
            }

            List<WxDashboardChart> dataRpcVos = DashboardHourlyChartUtil.getPerformanceChart(dtos, hour, campaignNameMap, shopNameMap);
            WxHourlyTopFiveResponse.Builder builder = WxHourlyTopFiveResponse.newBuilder();
            builder.setCode(Result.SUCCESS);
            builder.addAllData(dataRpcVos);

            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("error: ", e);
            responseObserver.onError(e);
        }
    }
}
