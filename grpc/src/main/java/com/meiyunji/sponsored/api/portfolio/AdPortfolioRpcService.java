package com.meiyunji.sponsored.api.portfolio;

import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.rpc.portfolio.*;
import com.meiyunji.sponsored.rpc.portfolio.ErrorMsgVo;
import com.meiyunji.sponsored.service.adProductRight.service.IAdProductRightService;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolio;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdPortfolioService;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @describe: 广告组合
 */
@GRpcService
@Slf4j
public class AdPortfolioRpcService extends RPCAdPortfolioServiceGrpc.RPCAdPortfolioServiceImplBase {

    @Autowired
    private IAmazonAdPortfolioService portfolioService;
    @Autowired
    private IAdProductRightService adProductRightService;

    /**
     * 左框显示广告组合列表(通过名字查询)
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllPortfolioData(AllPortfolioDataRequest request, StreamObserver<AllPortfolioDataResponse> responseObserver) {
        log.info("显示所有广告组合名字 request {}", request);
        AllPortfolioDataResponse.Builder builder = AllPortfolioDataResponse.newBuilder();
            PortfolioPageParam param = new PortfolioPageParam();
            param.setPuid(request.getPuid());
            param.setShopId(request.getShopId());
            param.setUid(request.getUid());
            if (request.hasIsHidden()) {
                param.setIsHidden(request.getIsHidden());
            }
            if (request.hasStatus()) {
                param.setStatus(request.getStatus());
            }
            if (request.hasSearchField()) {
                param.setSearchField(request.getSearchField());
            }
            if (request.hasSearchType()) {
                param.setSearchType(request.getSearchType());
            }
            if (request.hasSearchValue()) {
                param.setSearchValue(request.getSearchValue());
            }
            
            // 参数校验
            String err = checkPortfolioPageParam(param);
            if (StringUtils.isNotBlank(err)) {
                builder.setCode(Result.ERROR);
                builder.setMsg(err);
            } else {
                List<AmazonAdPortfolio> portfolioList = portfolioService.getPortfolioNameAndIdList(request.getPuid(), param);
                if (CollectionUtils.isNotEmpty(portfolioList)) {
                    List<AmazonAdPortfolioVo> rpcVos = portfolioList.stream().filter(Objects::nonNull).map(item -> {
                        AmazonAdPortfolioVo.Builder voBuilder = AmazonAdPortfolioVo.newBuilder();
                        if (item.getPortfolioId() != null) {
                            voBuilder.setPortfolioId(item.getPortfolioId());
                        }
                        if (item.getName() != null) {
                            voBuilder.setPortfolioName(item.getName());
                        }
                        return voBuilder.build();
                    }).collect(Collectors.toList());
                    builder.addAllData(rpcVos);
                }
                builder.setCode(Result.SUCCESS);
                builder.setMsg("");
            }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 通过店铺id查询广告组合列表
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllPortfolioDataByMultipleShopid(MultipleShopPortfolioDataRequest request, StreamObserver<AllPortfolioDataResponse> responseObserver) {
        log.info("显示所有广告组合名字 request {}", request);
        AllPortfolioDataResponse.Builder builder = AllPortfolioDataResponse.newBuilder();
        PortfolioListParam param = new PortfolioListParam();
        param.setPuid(request.getPuid());
        param.setShopId(request.getShopId());
        param.setUid(request.getUid());
        if (request.hasIsHidden()) {
            param.setIsHidden(request.getIsHidden());
        }
        if (request.hasStatus()) {
            param.setStatus(request.getStatus());
        }
        if (request.hasSearchField()) {
            param.setSearchField(request.getSearchField());
        }
        if (request.hasSearchType()) {
            param.setSearchType(request.getSearchType());
        }
        if (request.hasSearchValue()) {
            param.setSearchValue(request.getSearchValue());
        }

        // 参数校验
        String err = checkPortfolioPageParam(param);
        if (StringUtils.isNotBlank(err)) {
            builder.setCode(Result.ERROR);
            builder.setMsg(err);
        } else {
            List<AmazonAdPortfolio> portfolioList = portfolioService.getPortfolioNameAndIdList(request.getPuid(), param);
            if (CollectionUtils.isNotEmpty(portfolioList)) {
                Set<Integer> shopIds = new HashSet<>();
                Set<String> portfolioIdSet = new HashSet<>();
                boolean isProductRight = false;
                if (request.hasIsProductRight() && request.getIsProductRight()) {
                    List<String> collect = portfolioList.stream().map(e -> {
                        shopIds.add(e.getShopId());
                        return e.getPortfolioId();
                    }).distinct().collect(Collectors.toList());
                    Pair<Boolean, List<String>> productRightPortfolioIdsFromGrpc = adProductRightService.getProductRightPortfolioIdsFromGrpc(param.getPuid(), Lists.newArrayList(shopIds), collect);
                    if (productRightPortfolioIdsFromGrpc.getKey() && CollectionUtils.isNotEmpty(productRightPortfolioIdsFromGrpc.getValue())) {
                        portfolioIdSet = productRightPortfolioIdsFromGrpc.getValue().stream().collect(Collectors.toSet());
                    }
                    isProductRight = productRightPortfolioIdsFromGrpc.getKey();
                }

                Set<String> finalPortfolioIdSet = portfolioIdSet;
                boolean finalIsProductRight = isProductRight;
                List<AmazonAdPortfolioVo> rpcVos = portfolioList.stream().filter(Objects::nonNull).filter(e-> {
                    if (!finalIsProductRight) {
                        return true;
                    } else {
                        return finalPortfolioIdSet.contains(e.getPortfolioId());
                    }
                }).map(item -> {
                    AmazonAdPortfolioVo.Builder voBuilder = AmazonAdPortfolioVo.newBuilder();
                    if (item.getPortfolioId() != null) {
                        voBuilder.setPortfolioId(item.getPortfolioId());
                    }
                    if (item.getName() != null) {
                        voBuilder.setPortfolioName(item.getName());
                    }
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
            builder.setCode(Result.SUCCESS);
            builder.setMsg("");
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private String checkPortfolioPageParam(PortfolioPageParam param) {
        if (param == null || param.getPuid() == null || param.getShopId() == null || param.getUid() == null ) {
            return "请求参数错误";
        }
        return null;
    }

    private String checkPortfolioPageParam(PortfolioListParam param) {
        if (param == null || param.getPuid() == null || param.getShopId() == null || param.getUid() == null ) {
            return "请求参数错误";
        }
        return null;
    }

    @Override
    public void getTypeCampaignData(TypeCampaignDataRequest request, StreamObserver<TypeCampaignDataResponse> responseObserver) {
        TypeCampaignDataResponse.Builder builder = TypeCampaignDataResponse.newBuilder();
        if (!request.hasShopId() || !request.hasPuid() || StringUtils.isBlank(request.getType()) || StringUtils.isBlank(request.getPortfolioId())) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {
            List<AdCampaignVo> voList = portfolioService.getListCampaignVoByType(request.getPuid(), request.getShopId(),
                    request.getType(), request.getPortfolioId());
            if (CollectionUtils.isNotEmpty(voList)) {
                List<AmazonAdCampaignVo> rpcVos = voList.stream().filter(Objects::nonNull).map(item -> {
                    AmazonAdCampaignVo.Builder voBuilder = AmazonAdCampaignVo.newBuilder();
                    if (item.getCampaignId() != null) {
                        voBuilder.setCampaignId(item.getCampaignId());
                    }
                    if (item.getName() != null) {
                        voBuilder.setName(item.getName());
                    }
                    if (item.getState() != null) {
                        voBuilder.setState(item.getState());
                    }
                    if (item.getId() != null) {
                        voBuilder.setId(Math.toIntExact(item.getId()));
                    }
                    if (item.getCampaignTargetingType() != null) {
                        voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                    }
                    if (item.getTargetType() != null) {
                        voBuilder.setTargetType(item.getTargetType());
                    }
                    if (item.getDailyBudget() != null) {
                        voBuilder.setDailyBudget(item.getDailyBudget());
                    }
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
            builder.setCode(Result.SUCCESS);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 更新广告组合隐藏状态
     * @param request
     * @param responseObserver
     */
    @Override
    public void updatePortfolioHiddenState(UpdatePortfolioHiddenStateRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasShopId() || !request.hasPuid() || !request.hasUid()
                || StringUtils.isBlank(request.getPortfolioIds()) || !request.hasIsHidden()) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {
            Result<String> result = portfolioService.updatePortfolioHiddenState(request.getPuid(), request.getUid(), request.getShopId(),
                    request.getPortfolioIds(), request.getIsHidden());
            if (result.success()) {
                builder.setCode(Result.SUCCESS);
                builder.setMsg("success");
            } else {
                builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
                builder.setCode(Result.ERROR);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 创建广告组合
     * @param request
     * @param responseObserver
     */
    @Override
    public void createPortfolioData(CreatePortfolioDataRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasShopId() || !request.hasPuid() || !request.hasUid() || StringUtils.isBlank(request.getName())) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {
            Result<String> result = portfolioService.createPortfolioData(request.getPuid(), request.getUid(),
                    request.getShopId(), request.getName(), request.getIp());
            if (result.success()) {
                builder.setData(result.getData());
                builder.setCode(Result.SUCCESS);
                builder.setMsg("success");
            } else {
                builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
                builder.setCode(Result.ERROR);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 更新广告组合(主要更新预算上限类型、预算金额及相关预算日期)
     * @param request
     * @param responseObserver
     */
    @Override
    public void updatePortFolioData(UpdatePortFolioDataRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("编辑广告组合信息 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasShopId() || !request.hasPuid() || !request.hasUid() || StringUtils.isBlank(request.getPortfolioName()) ||  StringUtils.isBlank(request.getPortfolioId()) || StringUtils.isBlank(request.getPolicy())) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {
            PortfolioEditParam param = new PortfolioEditParam();
            BeanUtils.copyProperties(request,param);
            param.setPuid(request.getPuid());
            param.setShopId(request.getShopId());
            param.setUid(request.getUid());
            param.setPortfolioName(request.getPortfolioName());
            param.setPortfolioId(request.getPortfolioId());
            param.setIp(request.getIp());
            if (request.hasAmount()) {
                param.setAmount(Double.valueOf(request.getAmount()));
            }
            if (StringUtils.isNotBlank(param.getBudgetStartDate())) {
                param.setBudgetStartDate(param.getBudgetStartDate().replace("-", ""));
            }
            if (StringUtils.isNotBlank(param.getBudgetEndDate())) {
                param.setBudgetEndDate(param.getBudgetEndDate().replace("-", ""));
            }

            // 参数校验
            String err = checkPortfolioEditParam(param, request);
            if (StringUtils.isNotBlank(err)) {
                builder.setCode(Result.ERROR);
                builder.setMsg(err);
            } else {
                Result<String> result = portfolioService.updatePortFolioData(param);
                if (result.success()) {
                    builder.setData(result.getData());
                    builder.setCode(Result.SUCCESS);
                    builder.setMsg("success");
                } else {
                    builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
                    builder.setCode(Result.ERROR);
                }
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 更新广告组合状态 （用户可更改为暂停paused、归档archived、开启enabled）
     * @param request
     * @param responseObserver
     */
    public void updateState(UpdatePortfolioStateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("广告组合-更新广告组合状态 request {}", request);
        UpdatePortfolioStateRequest.PortfolioState state = request.getState();

        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasUid() || !request.hasShopId() || state == null || request.getPortfolioId() == null) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {
            CpcStatusEnum statusEnum;
            statusEnum = CpcStatusEnum.valueOf(state.getValueDescriptor().getName());

            Result result = portfolioService.updateState(request.getPuid().getValue(), request.getUid().getValue(), request.getShopId().getValue(), request.getPortfolioId(), statusEnum);
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
            builder.setCode(result.getCode());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 检查编辑广告组合的参数
     * @param param
     * @return
     */
    private  String checkPortfolioEditParam(PortfolioEditParam param, UpdatePortFolioDataRequest request){
        if (StringUtils.isNotBlank(param.getPolicy())) {
            PortfolioEditParam.PortfolioPolicyEnum policyEnum= UCommonUtil.getByCode(param.getPolicy(), PortfolioEditParam.PortfolioPolicyEnum.class);
            if (policyEnum == null) {
                return "请求参数错误";
            }
        }
        if ("dateRange".equals(param.getPolicy())) {
            param.setPolicy(request.getPolicy());
            if (param.getAmount() == null) {
                return "请求参数错误";
            }
            if (StringUtils.isBlank(param.getBudgetStartDate())) {
                return "请求参数错误";
            }
        } else if ("MonthlyRecurring".equals(param.getPolicy())) {
            param.setPolicy(StringUtils.uncapitalize(param.getPolicy()));
            if (param.getAmount() == null) {
                return "请求参数错误";
            }
        }
        return null;
    }

    @Override
    public void removePortFolioData(RemovePortFolioDataRequest request, StreamObserver<RemovePortFolioDataResponse> responseObserver) {
        RemovePortFolioDataResponse.Builder builder = RemovePortFolioDataResponse.newBuilder();
        if (!request.hasShopId() || !request.hasPuid() || !request.hasUid() ||  StringUtils.isBlank(request.getCampaignIds())) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {
            Result<List<PortfolioErrorMsgVo>> result = portfolioService.removePortFolioData(request.getPuid(), request.getUid(), request.getShopId(), request.getType(),
                    request.getCampaignIds(), request.getLoginIp());
            if (result.success()) {
                List<PortfolioErrorMsgVo> resultData = result.getData();
                if (CollectionUtils.isNotEmpty(resultData)) {
                    List<ErrorMsgVo> rpcList = new ArrayList<>(resultData.size());
                    for (PortfolioErrorMsgVo msgVo : resultData) {
                        ErrorMsgVo.Builder builder1 = ErrorMsgVo.newBuilder();
                        if (msgVo.getCampaignId() != null) {
                            builder1.setCampaignId(msgVo.getCampaignId());
                        }
                        if (msgVo.getName() != null) {
                            builder1.setName(msgVo.getName());
                        }
                        if (msgVo.getErrMsg() != null) {
                            builder1.setErrMsg(msgVo.getErrMsg());
                        }
                        rpcList.add(builder1.build());
                    }
                    builder.addAllData(rpcList);
                }

                builder.setCode(Result.SUCCESS);
            } else {
                builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
                builder.setCode(Result.ERROR);
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void movePortFolioData(MovePortFolioDataRequest request, StreamObserver<MovePortFolioDataResponse> responseObserver) {
        MovePortFolioDataResponse.Builder builder = MovePortFolioDataResponse.newBuilder();
        if (!request.hasShopId() || !request.hasPuid() || !request.hasUid()
                ||  StringUtils.isBlank(request.getCampaignIds()) || StringUtils.isBlank(request.getPortfolioId())) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {
            Result<List<PortfolioErrorMsgVo>> result = portfolioService.movePortFolioData(request.getPuid(), request.getUid(), request.getShopId(), request.getType(),
                    request.getCampaignIds(), request.getPortfolioId(), request.getLoginIp());
            if (result.success()) {
                List<PortfolioErrorMsgVo> resultData = result.getData();
                if (CollectionUtils.isNotEmpty(resultData)) {
                    List<ErrorMsgVo> rpcList = new ArrayList<>(resultData.size());
                    for (PortfolioErrorMsgVo msgVo : resultData) {
                        ErrorMsgVo.Builder builder1 = ErrorMsgVo.newBuilder();
                        if (msgVo.getCampaignId() != null) {
                            builder1.setCampaignId(msgVo.getCampaignId());
                        }
                        if (msgVo.getName() != null) {
                            builder1.setName(msgVo.getName());
                        }
                        if (msgVo.getErrMsg() != null) {
                            builder1.setErrMsg(msgVo.getErrMsg());
                        }
                        rpcList.add(builder1.build());
                    }
                    builder.addAllData(rpcList);
                }

                builder.setCode(Result.SUCCESS);
            } else {
                builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
                builder.setCode(Result.ERROR);
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 左框显示广告组合列表(通过名字查询分时调价专属)
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void allPortfolioDataList(AllPortfolioDataListsRequest request, StreamObserver<AllPortfolioDataResponse> responseObserver) {
        log.info("显示所有广告组合名字 request {}", request);
        AllPortfolioDataResponse.Builder builder = AllPortfolioDataResponse.newBuilder();
        PortfolioPageParam param = new PortfolioPageParam();
        param.setPuid(request.getPuid());
        param.setShopIdList(request.getShopIdList());
        param.setUid(request.getUid());
        if (request.hasIsHidden()) {
            param.setIsHidden(request.getIsHidden());
        }
        if (request.hasStatus()) {
            param.setStatus(request.getStatus());
        }
        if (request.hasSearchField()) {
            param.setSearchField(request.getSearchField());
        }
        if (request.hasSearchType()) {
            param.setSearchType(request.getSearchType());
        }
        if (request.hasSearchValue()) {
            param.setSearchValue(request.getSearchValue());
        }

        // 参数校验
        String err = null;
        if (request == null || param.getPuid() == null || param.getUid() == null ) {
            err =  "请求参数错误";
        }
        if (StringUtils.isNotBlank(err)) {
            builder.setCode(Result.ERROR);
            builder.setMsg(err);
        } else {
            List<AmazonAdPortfolio> portfolioList = portfolioService.getPortfolioNameAndIdLists(request.getPuid(), param);
            if (CollectionUtils.isNotEmpty(portfolioList)) {
                List<AmazonAdPortfolioVo> rpcVos = portfolioList.stream().filter(Objects::nonNull).map(item -> {
                    AmazonAdPortfolioVo.Builder voBuilder = AmazonAdPortfolioVo.newBuilder();
                    if (item.getPortfolioId() != null) {
                        voBuilder.setPortfolioId(item.getPortfolioId());
                    }
                    if (item.getName() != null) {
                        voBuilder.setPortfolioName(item.getName());
                    }
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
            builder.setCode(Result.SUCCESS);
            builder.setMsg("");
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 手动自定义排序
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updatePortFolioRank(UpdatePortFolioRankRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("广告更新自定义排序 request {}", request);

        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasUid() || !request.hasShopId() || !request.hasNewRank()  || !request.hasOldRank() || StringUtils.isBlank(request.getPortfolioId())) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {


            Result result = portfolioService.updateRank(request.getPuid().getValue(), request.getUid().getValue(), request.getShopId().getValue(), request.getPortfolioId(),request.getNewRank(),request.getOldRank());
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
            builder.setCode(result.getCode());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
