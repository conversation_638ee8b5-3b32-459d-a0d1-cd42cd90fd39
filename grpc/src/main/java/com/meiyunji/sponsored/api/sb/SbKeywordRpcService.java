package com.meiyunji.sponsored.api.sb;

import com.amazon.advertising.sb.entity.keywordRecommendation.SBKeywordSuggestion;
import com.google.protobuf.Int32Value;
import com.google.protobuf.ProtocolStringList;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.keywords.AsyncAddKeywordsRequest;
import com.meiyunji.sponsored.rpc.keywords.BatchSuggestedBidRequest;
import com.meiyunji.sponsored.rpc.keywords.KeywordSuggestedBidVo;
import com.meiyunji.sponsored.rpc.sb.campaign.Themes;
import com.meiyunji.sponsored.rpc.sb.keyword.KeywordsVo;
import com.meiyunji.sponsored.rpc.sb.keyword.SuggestedKeywordVo;
import com.meiyunji.sponsored.rpc.sb.keyword.ThemesVo;
import com.meiyunji.sponsored.rpc.sb.keyword.*;
import com.meiyunji.sponsored.rpc.vo.AsyncAddKeywordsRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.common.qo.WordTranslateQo;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetObjectTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskConstant;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskMatchTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetTaskDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.IAdTargetTaskService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbKeywordServiceImpl;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.enums.SBAdFormatEnum;
import com.meiyunji.sponsored.service.enums.SBCampaignCostTypeEnum;
import com.meiyunji.sponsored.service.enums.SBCampaignGoalEnum;
import com.meiyunji.sponsored.service.enums.SBThemesEnum;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@GRpcService
@Slf4j
public class SbKeywordRpcService extends RPCCpcSbKeywordServiceGrpc.RPCCpcSbKeywordServiceImplBase {

    @Autowired
    private CpcSbKeywordServiceImpl cpcSbKeywordService;
    @Autowired
    private IAdTargetTaskService adTargetTaskService;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;

    @Override
    public void update(UpdateRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasId() || !request.hasShopId()
                || (StringUtils.isBlank(request.getState()) && !request.hasBid())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
        } else {
            String state = null;
            if (StringUtils.isNotBlank(request.getState())) {
                state = request.getState();
            }
            Double bid = null;
            if (request.hasBid()) {
                bid = request.getBid().getValue();
            }
            Result result = cpcSbKeywordService.update(request.getPuid().getValue(), request.getShopId().getValue(),
                    request.getUid().getValue(), request.getId().getValue(), state, bid, request.getIp());
            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void archive(ArchiveRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasId() || !request.hasShopId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
        } else {
            Result result = cpcSbKeywordService.archive(request.getPuid().getValue(), request.getShopId().getValue(),
                    request.getUid().getValue(), request.getId().getValue(), request.getIp());
            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void suggestKeyword(SuggestKeywordRequest request, StreamObserver<SuggestKeywordResponse> responseObserver) {
        SuggestKeywordResponse.Builder builder = SuggestKeywordResponse.newBuilder();
        if (!request.hasShopId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
        } else {
            Result<List<SBKeywordSuggestion>> result = cpcSbKeywordService.getKeywordRecommendationList(request.getPuid().getValue(), request.getShopId().getValue(),
                    request.getAsinListList(), request.getUrl());
            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            List<SBKeywordSuggestion> suggestionList = result.getData();

            if (CollectionUtils.isNotEmpty(suggestionList)) {
                List<com.meiyunji.sponsored.rpc.sb.keyword.SBKeywordSuggestion> builderList = new ArrayList<>(suggestionList.size());
                for (SBKeywordSuggestion suggestion : suggestionList) {
                    com.meiyunji.sponsored.rpc.sb.keyword.SBKeywordSuggestion.Builder suggestionBuilder = com.meiyunji.sponsored.rpc.sb.keyword.SBKeywordSuggestion.newBuilder();
                    if (suggestion.getMatchType() != null) {
                        suggestionBuilder.setMatchType(suggestion.getMatchType());
                    }
                    if (suggestion.getTranslation() != null) {
                        suggestionBuilder.setTranslation(suggestion.getTranslation());
                    }
                    if (suggestion.getRecommendationId() != null) {
                        suggestionBuilder.setRecommendationId(suggestion.getRecommendationId());
                    }
                    if (suggestion.getType() != null) {
                        suggestionBuilder.setType(suggestion.getType());
                    }
                    if (suggestion.getValue() != null) {
                        suggestionBuilder.setValue(suggestion.getValue());
                    }
                    builderList.add(suggestionBuilder.build());
                }
                builder.addAllData(builderList);
            }


        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    @Override
    public void batchGetSuggestedBid(BatchSuggestedBidRequest request, StreamObserver<SuggestedBidResponse> responseObserver) {
        log.info("sb-keyword-建议竞价 request {}", request);
        SuggestedBidResponse.Builder builder = SuggestedBidResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || CollectionUtils.isEmpty(request.getKeywordDetailList()) || request.getKeywordDetailList().size() > 3000) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            List<KeywordSuggestedBidVo> requestDetails = request.getKeywordDetailList();
            List<KeywordSuggestedBidDetail> details = new ArrayList<>();
            for (int i = 0; i < requestDetails.size(); i++) {
                KeywordSuggestedBidDetail keywordSuggestedBidDetail = new KeywordSuggestedBidDetail();
                BeanUtils.copyProperties(requestDetails.get(i), keywordSuggestedBidDetail);
                keywordSuggestedBidDetail.setIndex(i);
                details.add(keywordSuggestedBidDetail);
            }
            Result<List<com.meiyunji.sponsored.service.cpc.vo.SuggestedKeywordVo>> result = cpcSbKeywordService.batchGetSuggestedBid(request.getPuid().getValue(), request.getShopId().getValue(),
                    details);

            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");

            List<com.meiyunji.sponsored.service.cpc.vo.SuggestedKeywordVo> data = result.getData();

            if (CollectionUtils.isNotEmpty(data)) {
                List<SuggestedKeywordVo> voList = new ArrayList<>();
                for (com.meiyunji.sponsored.service.cpc.vo.SuggestedKeywordVo vo : data) {
                    SuggestedKeywordVo.Builder builder1 = SuggestedKeywordVo.newBuilder();
                    if (vo.getKeywordId() != null) {
                        builder1.setKeywordId(vo.getKeywordId());
                    }
                    if (vo.getKeywordText() != null) {
                        builder1.setKeywordText(vo.getKeywordText());
                        //SB主题广告转换为中文返回
                        if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(vo.getKeywordText())) {
                            builder1.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
                        } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(vo.getKeywordText())) {
                            builder1.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
                        }
                    }
                    if (vo.getMatchType() != null) {
                        builder1.setMatchType(vo.getMatchType());
                    }
                    if (vo.getSuggested() != null) {
                        builder1.setSuggested(vo.getSuggested());
                    }
                    if (vo.getRangeStart() != null) {
                        builder1.setRangeStart(vo.getRangeStart());
                    }
                    if (vo.getRangeEnd() != null) {
                        builder1.setRangeEnd(vo.getRangeEnd());
                    }
                    builder1.setIndex(vo.getIndex());
                    voList.add(builder1.build());
                }
                builder.addAllData(voList);
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void suggestKeywordNew(SuggestKeywordRequest request, StreamObserver<SuggestKeywordPageResponse> responseObserver) {
        SuggestKeywordPageResponse.Builder builder = SuggestKeywordPageResponse.newBuilder();
        SBKeywordSuggestionPage.Builder pageBuilder = SBKeywordSuggestionPage.newBuilder();
        if (!request.hasShopId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
        } else {
            Integer showExact = request.getShowExact();
            Integer showBroad = request.getShowBroad();
            Integer showPhrase = request.getShowPhrase();
            Integer adGoal = request.getAdGoal();
            Integer adFormat = request.getAdFormat();
            Integer pageNo = request.getPageNo();
            Integer pageSize = request.getPageSize();
            String searchVal = request.getSearchVal();
            Result<SuggestedKeywordPageVo> result = cpcSbKeywordService.getKeywordRecommendationListNew(request.getPuid().getValue(), request.getShopId().getValue(),
                    request.getAsinListList(), request.getUrl(), adGoal, adFormat, showExact, showBroad, showPhrase, searchVal, pageNo, pageSize, request.getCreativeAsinsList(), request.getCampaignId(), request.getGroupId());
            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            SuggestedKeywordPageVo suggestionResult = result.getData();
            List<SuggestedKeywordPageVo.SbKeywordSuggestion> suggestionList = new ArrayList<>();
            if (Objects.nonNull(suggestionResult) && CollectionUtils.isNotEmpty(suggestionResult.getKeywordList())) {
                suggestionList = suggestionResult.getKeywordList();
            }

            if (CollectionUtils.isNotEmpty(suggestionList)) {
                List<com.meiyunji.sponsored.rpc.sb.keyword.SBKeywordSuggestionPage.KeywordSuggestionList> builderList =  new ArrayList<>(suggestionList.size());
                for (SuggestedKeywordPageVo.SbKeywordSuggestion suggestion : suggestionList) {
                    com.meiyunji.sponsored.rpc.sb.keyword.SBKeywordSuggestionPage.KeywordSuggestionList.Builder suggestionBuilder =  com.meiyunji.sponsored.rpc.sb.keyword.SBKeywordSuggestionPage.KeywordSuggestionList.newBuilder();
                    if (suggestion.getMatchType() != null) {
                        suggestionBuilder.setMatchType(suggestion.getMatchType());
                    }
                    if (suggestion.getTranslation() != null) {
                        suggestionBuilder.setTranslation(suggestion.getTranslation());
                    }
                    if (suggestion.getRecommendationId() != null) {
                        suggestionBuilder.setRecommendationId(suggestion.getRecommendationId());
                    }
                    if (suggestion.getType() != null) {
                        suggestionBuilder.setType(suggestion.getType());
                    }
                    if (suggestion.getValue() != null) {
                        suggestionBuilder.setKeywordText(suggestion.getValue());
                    }
                    if (suggestion.getValueCn() != null) {
                        suggestionBuilder.setKeywordTextCn(suggestion.getValueCn());
                    }
                    builderList.add(suggestionBuilder.build());
                }
                pageBuilder.addAllKeywordList(builderList);
            }
            if (Objects.nonNull(suggestionResult)) {
                Optional.ofNullable(suggestionResult.getTotalSize()).ifPresent(pageBuilder::setTotalSize);
                Optional.ofNullable(suggestionResult.getTotalPage()).ifPresent(pageBuilder::setTotalPage);
            }
            Optional.of(pageNo).ifPresent(pageBuilder::setPageNo);
            Optional.of(pageSize).ifPresent(pageBuilder::setPageSize);
            builder.setData(pageBuilder.build());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getSuggestedBidByText(SuggestedBidTextRequest request, StreamObserver<SuggestedBidTextResponse> responseObserver) {
        SuggestedBidTextResponse.Builder builder = SuggestedBidTextResponse.newBuilder();
        List<KeywordsVo> keywordsList = request.getKeywordsList();

        if (!request.hasShopId() || CollectionUtils.isEmpty(keywordsList) || StringUtils.isBlank(request.getGroupId())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
        } else {

            List<com.meiyunji.sponsored.service.cpc.vo.KeywordsVo> list = new ArrayList<>(keywordsList.size());
            com.meiyunji.sponsored.service.cpc.vo.KeywordsVo keywordsVo;
            for (KeywordsVo vo : keywordsList) {
                keywordsVo = new com.meiyunji.sponsored.service.cpc.vo.KeywordsVo();
                if (StringUtils.isNotBlank(vo.getKeywordText())) {
                    keywordsVo.setKeywordText(vo.getKeywordText());
                }
                if (StringUtils.isNotBlank(vo.getMatchType())) {
                    keywordsVo.setMatchType(vo.getMatchType());
                }
                if (StringUtils.isNotBlank(vo.getBid())) {
                    keywordsVo.setBid(vo.getBid());
                }
                if (StringUtils.isNotBlank(vo.getSuggested())) {
                    keywordsVo.setSuggested(vo.getSuggested());
                }
                if (StringUtils.isNotBlank(vo.getRangeStart())) {
                    keywordsVo.setRangeStart(vo.getRangeStart());
                }
                if (StringUtils.isNotBlank(vo.getRangeEnd())) {
                    keywordsVo.setRangeEnd(vo.getRangeEnd());
                }

                list.add(keywordsVo);
            }

            Result result = cpcSbKeywordService.getKeywordBids(request.getPuid().getValue(), request.getShopId().getValue(),
                    request.getCampaignId(), request.getGroupId(), list);
            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");

            List<com.meiyunji.sponsored.service.cpc.vo.KeywordsVo> data = (List<com.meiyunji.sponsored.service.cpc.vo.KeywordsVo>) result.getData();

            if (CollectionUtils.isNotEmpty(data)) {
                List<KeywordsVo> voList = new ArrayList<>();
                for (com.meiyunji.sponsored.service.cpc.vo.KeywordsVo vo : data) {
                    KeywordsVo.Builder builder1 = KeywordsVo.newBuilder();
                    if (vo.getKeywordText() != null) {
                        builder1.setKeywordText(vo.getKeywordText());
                    }
                    if (vo.getMatchType() != null) {
                        builder1.setMatchType(vo.getMatchType());
                    }
                    if (vo.getBid() != null) {
                        builder1.setBid(vo.getBid());
                    }
                    if (vo.getSuggested() != null) {
                        builder1.setSuggested(vo.getSuggested());
                    }
                    if (vo.getRangeStart() != null) {
                        builder1.setRangeStart(vo.getRangeStart());
                    }
                    if (vo.getRangeEnd() != null) {
                        builder1.setRangeEnd(vo.getRangeEnd());
                    }
                    voList.add(builder1.build());
                }
                builder.addAllKeywords(voList);
            }


        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    @Override
    public void getSuggestedBidByTextNew(SuggestedBidTextRequest request, StreamObserver<SuggestedBidTextNewResponse> responseObserver) {
        SuggestedBidTextNewResponse.Builder builder = SuggestedBidTextNewResponse.newBuilder();
        SuggestedBidTextNewResponse.SuggestedKeywordAndThemesNew.Builder keywordAndThemeBuilder = SuggestedBidTextNewResponse.SuggestedKeywordAndThemesNew.newBuilder();
        List<KeywordsVo> keywordsList = request.getKeywordsList();
        List<ThemesVo> themesList = request.getThemesVoListList();
        String adFormat = Optional.of(request.getAdFormat()).map(Integer::valueOf)
                .map(SBAdFormatEnum::getSBAdFormatEnumByCode).map(SBAdFormatEnum::getValue).orElse("");
        SBCampaignGoalEnum goalEn = SBCampaignGoalEnum.getSBCampaignGoalEnumByCode(request.getGoal());
        String costType = null;
        if (Objects.nonNull(goalEn)) {
            if (SBCampaignGoalEnum.PAGE_VISIT == goalEn) {
                costType = SBCampaignCostTypeEnum.CPC.getCode();
            }
            if (SBCampaignGoalEnum.BRAND_IMPRESSION_SHARE == goalEn) {
                costType = SBCampaignCostTypeEnum.VCPM.getCode();
            }
        }
        if (CollectionUtils.isNotEmpty(themesList) && themesList.stream().anyMatch(t -> !SBThemesEnum.getSBThemesEnumMap().containsKey(t.getThemeType()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
        }
        if (!request.hasShopId() || CollectionUtils.isEmpty(keywordsList)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
        } else {
            List<com.meiyunji.sponsored.service.cpc.vo.KeywordsVo> list = new ArrayList<>(keywordsList.size());
            com.meiyunji.sponsored.service.cpc.vo.KeywordsVo keywordsVo;
            for (KeywordsVo vo : keywordsList) {
                keywordsVo = new com.meiyunji.sponsored.service.cpc.vo.KeywordsVo();
                if (StringUtils.isNotBlank(vo.getKeywordText())) {
                    keywordsVo.setKeywordText(vo.getKeywordText());
                }
                if (StringUtils.isNotBlank(vo.getMatchType())) {
                    keywordsVo.setMatchType(vo.getMatchType().toLowerCase());
                }
                list.add(keywordsVo);
            }

            List<com.meiyunji.sponsored.service.cpc.vo.ThemesVo> themes = new ArrayList<>();
            for (ThemesVo vo : themesList) {
                if (SBThemesEnum.getSBThemesEnumMap().containsKey(vo.getThemeType())) {
                    com.meiyunji.sponsored.service.cpc.vo.ThemesVo themesVo = new com.meiyunji.sponsored.service.cpc.vo.ThemesVo();
                    themesVo.setThemeType(SBThemesEnum.getSBThemesEnumMap().get(vo.getThemeType()).getValue());
                    themes.add(themesVo);
                }
            }

            Result result = cpcSbKeywordService.getKeywordBidsNew(request.getPuid().getValue(),
                    request.getShopId().getValue(), adFormat, list, Optional.ofNullable(goalEn).map(SBCampaignGoalEnum::getType).orElse(null), costType);
            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");

            List<com.meiyunji.sponsored.service.cpc.vo.KeywordsVo> data = (List<com.meiyunji.sponsored.service.cpc.vo.KeywordsVo>) result.getData();

            if (CollectionUtils.isNotEmpty(data)) {
                List<KeywordsVo> voList = new ArrayList<>();
                for (com.meiyunji.sponsored.service.cpc.vo.KeywordsVo vo : data) {
                    KeywordsVo.Builder builder1 = KeywordsVo.newBuilder();
                    if (vo.getKeywordText() != null) {
                        builder1.setKeywordText(vo.getKeywordText());
                    }
                    if (vo.getMatchType() != null) {
                        builder1.setMatchType(vo.getMatchType());
                    }
                    if (vo.getBid() != null) {
                        builder1.setBid(vo.getBid());
                    }
                    if (vo.getSuggested() != null) {
                        builder1.setSuggested(vo.getSuggested());
                    }
                    if (vo.getRangeStart() != null) {
                        builder1.setRangeStart(vo.getRangeStart());
                    }
                    if (vo.getRangeEnd() != null) {
                        builder1.setRangeEnd(vo.getRangeEnd());
                    }
                    voList.add(builder1.build());
                }
                keywordAndThemeBuilder.addAllKeywords(voList);
            }

            if (CollectionUtils.isNotEmpty(themesList)) {
                Result<List<ThemeBidVo>> themesResult = cpcSbKeywordService.getThemesBidsNew(request.getPuid().getValue(),
                        request.getShopId().getValue(), adFormat, themes, Optional.ofNullable(goalEn).map(SBCampaignGoalEnum::getType).orElse(null), costType);

                if (Objects.nonNull(themesResult) && CollectionUtils.isNotEmpty(themesResult.getData())) {
                    List<ThemesResponseVo> voList = new ArrayList<>();
                    for (ThemeBidVo vo : themesResult.getData()) {
                        ThemesResponseVo.Builder builder2 = ThemesResponseVo.newBuilder();
                        Optional.ofNullable(vo.getThemesType()).filter(StringUtils::isNotEmpty)
                                .map(SBThemesEnum::getSBThemesEnumByVal).map(SBThemesEnum::getCode).map(String::valueOf).ifPresent(builder2::setThemeType);
                        Optional.ofNullable(vo.getSuggested()).filter(StringUtils::isNotEmpty).ifPresent(builder2::setSuggested);
                        Optional.ofNullable(vo.getRangeStart()).filter(StringUtils::isNotEmpty).ifPresent(builder2::setRangeStart);
                        Optional.ofNullable(vo.getRangeEnd()).filter(StringUtils::isNotEmpty).ifPresent(builder2::setRangeEnd);
                        voList.add(builder2.build());
                    }
                    keywordAndThemeBuilder.addAllThemes(voList);
                }
            }
        }
        builder.setData(keywordAndThemeBuilder);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getSuggestedBid(SuggestedBidRequest request, StreamObserver<SuggestedBidResponse> responseObserver) {
        SuggestedBidResponse.Builder builder = SuggestedBidResponse.newBuilder();
        ProtocolStringList keywordIdsList = request.getKeywordIdsList();
        if (!request.hasShopId() || CollectionUtils.isEmpty(keywordIdsList)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            List<String> keywordList = new ArrayList<>(keywordIdsList.size());
            keywordList.addAll(keywordIdsList);
            Result<List<com.meiyunji.sponsored.service.cpc.vo.SuggestedKeywordVo>> result = cpcSbKeywordService.getSuggestedBid(request.getPuid().getValue(), request.getShopId().getValue(),
                    keywordList);

            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");

            List<com.meiyunji.sponsored.service.cpc.vo.SuggestedKeywordVo> data = result.getData();

            if (CollectionUtils.isNotEmpty(data)) {
                List<SuggestedKeywordVo> voList = new ArrayList<>();
                for (com.meiyunji.sponsored.service.cpc.vo.SuggestedKeywordVo vo : data) {
                    SuggestedKeywordVo.Builder builder1= SuggestedKeywordVo.newBuilder();
                    if (vo.getKeywordId() != null) {
                        builder1.setKeywordId(vo.getKeywordId());
                    }
                    if (vo.getKeywordText() != null) {
                        builder1.setKeywordText(vo.getKeywordText());
                        //SB主题广告转换为中文返回
                        if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(vo.getKeywordText())) {
                            builder1.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
                        } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(vo.getKeywordText())) {
                            builder1.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
                        }
                    }
                    if (vo.getMatchType() != null) {
                        builder1.setMatchType(vo.getMatchType());
                    }
                    if (vo.getSuggested() != null) {
                        builder1.setSuggested(vo.getSuggested());
                    }
                    if (vo.getRangeStart() != null) {
                        builder1.setRangeStart(vo.getRangeStart());
                    }
                    if (vo.getRangeEnd() != null) {
                        builder1.setRangeEnd(vo.getRangeEnd());
                    }
                    voList.add(builder1.build());
                }
                builder.addAllData(voList);
            }

            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();

        }
    }

    @Override
    public void addSuggestKeyword(AddSuggestKeywordRequest request, StreamObserver<AddSuggestKeywordResponse> responseObserver) {
        AddSuggestKeywordResponse.Builder builder = AddSuggestKeywordResponse.newBuilder();

        if (!request.hasShopId() || StringUtils.isBlank(request.getCampaignId()) && StringUtils.isBlank(request.getGroupId())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");

        } else {

            Result<List<SBKeywordSuggestion>> result = cpcSbKeywordService.addSuggestKeyword(request.getPuid().getValue(), request.getShopId().getValue(),
                    request.getCampaignId(),request.getGroupId());

            List<SBKeywordSuggestion> data = result.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                List<com.meiyunji.sponsored.rpc.sb.keyword.SBKeywordSuggestion> list = new ArrayList<>(data.size());
                for (SBKeywordSuggestion suggestion : data) {
                    com.meiyunji.sponsored.rpc.sb.keyword.SBKeywordSuggestion.Builder builder1 = com.meiyunji.sponsored.rpc.sb.keyword.SBKeywordSuggestion.newBuilder();
                    if (suggestion.getMatchType() != null) {
                        builder1.setMatchType(suggestion.getMatchType());
                    }
                    if (suggestion.getTranslation() != null) {
                        builder1.setTranslation(suggestion.getTranslation());
                    }
                    if (suggestion.getRecommendationId() != null) {
                        builder1.setRecommendationId(suggestion.getRecommendationId());
                    }
                    if (suggestion.getType() != null) {
                        builder1.setType(suggestion.getType());
                    }
                    if (suggestion.getValue() != null) {
                        builder1.setValue(suggestion.getValue());
                    }
                    list.add(builder1.build());
                }
                builder.addAllData(list);
            }

            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void createKeywords(CreateKeywordsRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        List<KeywordsVo> keywordsList = request.getKeywordsList();

        if (!request.hasShopId() || StringUtils.isBlank(request.getCampaignId())
                || CollectionUtils.isEmpty(keywordsList)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");

        } else {
            AddSbKeywordsVo vo = new AddSbKeywordsVo();
            vo.setPuid(request.getPuid().getValue());
            vo.setUid(request.getUid().getValue());
            vo.setShopId(request.getShopId().getValue());
            vo.setCampaignId(request.getCampaignId());
            vo.setGroupId(request.getGroupId());
            vo.setIp(request.getIp());
            List<com.meiyunji.sponsored.service.cpc.vo.KeywordsVo> voList = new ArrayList<>(keywordsList.size());
            com.meiyunji.sponsored.service.cpc.vo.KeywordsVo keywordsVo1;
            for (KeywordsVo keywordsVo : keywordsList) {
                keywordsVo1 = new com.meiyunji.sponsored.service.cpc.vo.KeywordsVo();
                if (StringUtils.isNotBlank(keywordsVo.getKeywordText())) {
                    keywordsVo1.setKeywordText(StringUtil.replaceSpecialSymbol(keywordsVo.getKeywordText()));
                }
                if (StringUtils.isNotBlank(keywordsVo.getMatchType())) {
                    keywordsVo1.setMatchType(keywordsVo.getMatchType());
                }
                if (StringUtils.isNotBlank(keywordsVo.getBid())) {
                    keywordsVo1.setBid(keywordsVo.getBid());
                }
                if (StringUtils.isNotBlank(keywordsVo.getSuggested())) {
                    keywordsVo1.setSuggested(keywordsVo.getSuggested());
                }
                if (StringUtils.isNotBlank(keywordsVo.getRangeStart())) {
                    keywordsVo1.setRangeStart(keywordsVo.getRangeStart());
                }
                if (StringUtils.isNotBlank(keywordsVo.getRangeEnd())) {
                    keywordsVo1.setRangeEnd(keywordsVo.getRangeEnd());
                }
                voList.add(keywordsVo1);
            }
            vo.setKeywords(voList);

            Result result = cpcSbKeywordService.createKeywords(vo);
            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");


        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void asyncCreateKeywords(AsyncAddKeywordsRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sb-keyword-异步添加关键词 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        String error = verifyAsyncParam(request);
        if (StringUtils.isNotBlank(error)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(error);
        } else {
            AdTargetTaskDto adTargetTaskDto = new AdTargetTaskDto();
            adTargetTaskDto.setPuid(request.getPuid().getValue());
            adTargetTaskDto.setShopId(request.getShopId().getValue());
            adTargetTaskDto.setUid(request.getUid().getValue());
            adTargetTaskDto.setType(AdTargetTaskTypeEnum.SB_KEYWORDS.getCode());
            adTargetTaskDto.setLoginIp(request.getLoginIp());
            adTargetTaskDto.setTargetPageType(request.getTargetingPageType().getValue());
            adTargetTaskDto.setSourceAdCampaignId(request.getSourceAdCampaignId());
            adTargetTaskDto.setSourceShopId(request.getSourceShopId());
            List<AdTargetTaskDto.AdTargetTaskDetailDto> detailList = request.getKeywordList().stream().filter(Objects::nonNull).map(item -> {
                AdTargetTaskDto.AdTargetTaskDetailDto detail = new AdTargetTaskDto.AdTargetTaskDetailDto();
                detail.setAdCampaignId(item.getAdCampaignId());
                detail.setAdGroupId(item.getAdGroupId());
                detail.setMatchType(item.getMatchType());
                detail.setRangeEnd(StringUtils.isBlank(item.getRangeEnd()) ? null : new BigDecimal(item.getRangeEnd()));
                detail.setRangeStart(StringUtils.isBlank(item.getRangeStart()) ? null : new BigDecimal(item.getRangeStart()));
                detail.setSuggested(StringUtils.isBlank(item.getSuggested()) ? null : new BigDecimal(item.getSuggested()));
                detail.setBid(new BigDecimal(item.getBid()));
                detail.setTargetObject(item.getKeywordText());
                detail.setTargetObjectDesc(item.getKeywordCn());
                detail.setTargetObjectType(AdTargetObjectTypeEnum.KEYWORD.getCode());
                return detail;
            }).collect(Collectors.toList());
            adTargetTaskDto.setTaskDetails(detailList);
            long taskId = adTargetTaskService.recordTargetTask(adTargetTaskDto);
            adTargetTaskService.executeTask(taskId);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private String verifyAsyncParam(AsyncAddKeywordsRequest request) {
        if (!request.hasShopId() || !request.hasPuid() || !request.hasTargetingPageType()
                || CollectionUtils.isEmpty(request.getKeywordList()) || request.getKeywordList().size() > AdTargetTaskConstant.MAX_TARGET_SIZE) {
            return "请求参数错误";
        }
        List<AsyncAddKeywordsRpcVo> keywordList = request.getKeywordList();
        for (AsyncAddKeywordsRpcVo keyword : keywordList) {
            if (!AdTargetTaskMatchTypeEnum.SB_KEYWORD_SUPPORT_TYPES.contains(keyword.getMatchType())) {
                return "包含未支持的匹配类型";
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 添加搜索词到关键词
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void createCampaignIdsKeywords(CreateCampaignIdsKeywordsRequest request, StreamObserver<KeywordsResponse> responseObserver) {
        KeywordsResponse.Builder builder = KeywordsResponse.newBuilder();
        List<CampaignIdsKeywordsVo> keywordsList = request.getKeywordsList();

        if (!request.hasShopId()
                || CollectionUtils.isEmpty(keywordsList)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");

        } else {
            AddSbCampaignIdsKeywordsVo vo = new AddSbCampaignIdsKeywordsVo();
            vo.setPuid(request.getPuid().getValue());
            vo.setUid(request.getUid().getValue());
            vo.setShopId(request.getShopId().getValue());
            List<com.meiyunji.sponsored.service.cpc.vo.CampaignIdKeywordsVo> voList = new ArrayList<>(keywordsList.size());
            com.meiyunji.sponsored.service.cpc.vo.CampaignIdKeywordsVo keywordsVo1;
            for (CampaignIdsKeywordsVo keywordsVo : keywordsList) {
                keywordsVo1 = new com.meiyunji.sponsored.service.cpc.vo.CampaignIdKeywordsVo();
                if (StringUtils.isNotBlank(keywordsVo.getKeywordText())) {
                    keywordsVo1.setKeywordText(keywordsVo.getKeywordText());
                }
                if (StringUtils.isNotBlank(keywordsVo.getMatchType())) {
                    keywordsVo1.setMatchType(keywordsVo.getMatchType());
                }
                if (StringUtils.isNotBlank(keywordsVo.getBid())) {
                    keywordsVo1.setBid(keywordsVo.getBid());
                }
                if (StringUtils.isNotBlank(keywordsVo.getSuggested())) {
                    keywordsVo1.setSuggested(keywordsVo.getSuggested());
                }
                if (StringUtils.isNotBlank(keywordsVo.getRangeStart())) {
                    keywordsVo1.setRangeStart(keywordsVo.getRangeStart());
                }
                if (StringUtils.isNotBlank(keywordsVo.getRangeEnd())) {
                    keywordsVo1.setRangeEnd(keywordsVo.getRangeEnd());
                }
                if (StringUtils.isNotBlank(keywordsVo.getCampaignId())) {
                    keywordsVo1.setCampaignId(keywordsVo.getCampaignId());
                }
                if (StringUtils.isNotBlank(keywordsVo.getGroupId())) {
                    keywordsVo1.setGroupId(keywordsVo.getGroupId());
                }
                if (keywordsVo.hasIndex()) {
                    keywordsVo1.setIndex(keywordsVo.getIndex().getValue());
                }
                voList.add(keywordsVo1);
            }
            vo.setKeywords(voList);

            Result<Map<String, List<Integer>>> res = cpcSbKeywordService.createCampaignIdsKeywords(vo);
            //处理返回结果
            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
            Map<String, List<Integer>> data = res.getData();
            if (MapUtils.isNotEmpty(data)) {
                KeywordsResponse.keywordReturnData.Builder voBuilder = KeywordsResponse.keywordReturnData.newBuilder();
                List<Integer> success = data.get("success");
                List<Integer> fail = data.get("fail");
                if (CollectionUtils.isNotEmpty(success)) {
                    List<Int32Value> successNum = success.stream().filter(Objects::nonNull).map(Int32Value::of).collect(Collectors.toList());
                    voBuilder.addAllSuccess(successNum);
                }
                if (CollectionUtils.isNotEmpty(fail)) {
                    List<Int32Value> failNum = fail.stream().filter(Objects::nonNull).map(Int32Value::of).collect(Collectors.toList());
                    voBuilder.addAllFail(failNum);
                }
                builder.setData(voBuilder.build());
            }
            builder.setCode(Int32Value.of(res.getCode()));
            builder.setMsg(res.getMsg() != null ? res.getMsg() : "");


        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateBatch(UpdateBatchRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasShopId() || !request.hasPuid() || !request.hasType() || !request.hasUid() || request.getVosCount() < 1) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
        } else {
            List<UpdateBatchVo> vosList = request.getVosList();
            List<SbUpdateKeywordsVo> collect = vosList.stream().map(e -> {
                SbUpdateKeywordsVo sbUpdateKeywordsVo = new SbUpdateKeywordsVo();
                sbUpdateKeywordsVo.setPuid(request.getPuid().getValue());
                sbUpdateKeywordsVo.setShopId(request.getShopId().getValue());
                sbUpdateKeywordsVo.setUid(request.getUid().getValue());
                if (e.hasId()) {
                    sbUpdateKeywordsVo.setId(e.getId().getValue());
                }
                if (e.hasState()) {
                    sbUpdateKeywordsVo.setState(e.getState());
                }
                if (e.hasBid()) {
                    sbUpdateKeywordsVo.setBid(e.getBid().getValue());
                }
                return sbUpdateKeywordsVo;
            }).collect(Collectors.toList());

            Result result = cpcSbKeywordService.updateBatch(request.getPuid().getValue(), request.getShopId().getValue(),
                    request.getUid().getValue(), collect, request.getType(), request.getIp());
            builder.setCode(Int32Value.of(result.getCode()));
            if (result.getData() != null) {
                builder.setData(JSONUtil.objectToJson(result.getData()));
            }
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void createThemes(CreateThemesRequest request, StreamObserver<ThemesResponse> responseObserver) {
        ThemesResponse.Builder responseInfo = ThemesResponse.newBuilder();
        ThemesResponse.ThemesReturnData.Builder dataBuilder = ThemesResponse.ThemesReturnData.newBuilder();
        List<Themes> themesList = request.getThemesList();

        if (!request.hasShopId()
                || CollectionUtils.isEmpty(themesList)) {
            responseInfo.setCode(Result.ERROR);
            responseInfo.setMsg("参数有误");
        } else {
            ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId(), request.getPuid());
            if (shop == null) {
                dataBuilder.setCommonErrMsg("没有CPC授权");
                responseInfo.setData(dataBuilder.build());
                responseObserver.onNext(responseInfo.build());
                responseObserver.onCompleted();
            }
            AmazonAdProfile profile = amazonAdProfileDao.getProfile(request.getPuid(), request.getShopId());
            if (profile == null) {
                dataBuilder.setCommonErrMsg("没有站点对应的配置信息");
                responseInfo.setData(dataBuilder.build());
                responseObserver.onNext(responseInfo.build());
                responseObserver.onCompleted();
            }
            AddSbThemesVo vo = new AddSbThemesVo();
            vo.setPuid(request.getPuid());
            vo.setShopId(request.getShopId());
            List<com.meiyunji.sponsored.service.cpc.vo.ThemesVo> voList = new ArrayList<>(themesList.size());
            for (Themes theme : themesList) {
                com.meiyunji.sponsored.service.cpc.vo.ThemesVo tVo = new com.meiyunji.sponsored.service.cpc.vo.ThemesVo();
                Optional.of(theme.getThemeType()).map(SBThemesEnum::getSBThemesEnumByCode).map(SBThemesEnum::getValue).ifPresent(tVo::setThemeType);
                tVo.setBid(theme.getBid());
                voList.add(tVo);
            }
            vo.setThemes(voList);

            NewCreateResultResultVo<SBCommonErrorVo> result = cpcSbKeywordService.createThemes(vo, shop, profile);
            //处理返回结果
            if (CollectionUtils.isNotEmpty(result.getThemesList())) {
                List<ThemesInfo> list = result.getThemesList().stream().map(k -> {
                    ThemesInfo.Builder t = ThemesInfo.newBuilder();
                    t.setThemesId(k.getThemesId());
                    t.setThemesText(k.getThemesText());
                    return t.build();
                }).collect(Collectors.toList());
                dataBuilder.addAllThemeList(list);
                responseInfo.setData(dataBuilder.build());
            }
        }
        responseObserver.onNext(responseInfo.build());
        responseObserver.onCompleted();
    }
}
