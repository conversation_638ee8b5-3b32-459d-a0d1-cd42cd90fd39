package com.meiyunji.sponsored.api.sd;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdProductDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProduct;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdProduct;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.rpc.adCommon.CommonShowAdPerformanceRequest;
import com.meiyunji.sponsored.rpc.adCommon.CommonShowAdPerformanceResponse;
import com.meiyunji.sponsored.rpc.sd.product.*;
import com.meiyunji.sponsored.rpc.vo.AdPerformanceRpcVo;
import com.meiyunji.sponsored.rpc.vo.CommonResponse;
import com.meiyunji.sponsored.rpc.vo.CpcCommPageRpcVo;
import com.meiyunji.sponsored.rpc.vo.ProductRpcVo;
import com.meiyunji.sponsored.service.enums.StateEnum;
import com.meiyunji.sponsored.service.cpc.service2.sd.ICpcSdAdProductService;
import com.meiyunji.sponsored.service.cpc.vo.*;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2021/10/28 18:49
 * @describe: sd 广告产品rpc接口
 */
@GRpcService
@Slf4j
public class ProductSdRpcService extends RPCSdProductServiceGrpc.RPCSdProductServiceImplBase {


    @Autowired
    private ICpcSdAdProductService cpcSdAdProductService;

    @Autowired
    private IAmazonSdAdProductDao amazonSdAdProductDao;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;

    @Override
    public void showAdPerformanceVo(CommonShowAdPerformanceRequest request, StreamObserver<CommonShowAdPerformanceResponse> responseObserver) {
        CommonShowAdPerformanceResponse.Builder builder = CommonShowAdPerformanceResponse.newBuilder();
        //做参数校验
        if (!request.hasShopId()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");

        } else {
            AdPerformanceParam param = new AdPerformanceParam();
            param.setShopId(request.getShopId());
            param.setPuid(request.getPuid());
            param.setCampaignId(request.getCampaignId());
            param.setGroupId(request.getGroupId());
            param.setKeywordId(request.getKeywordId());
            param.setTargetId(request.getTargetId());
            param.setCpcProductId(request.getCpcProductId());
            param.setQuery(request.getQuery());
            param.setPlacement(request.getPlacement());
            param.setStartDate(request.getStartDate());
            param.setEndDate(request.getEndDate());

            if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
                param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
                param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            } else {
                param.setStartDate(param.getStartDate().replace("-", ""));
                param.setEndDate(param.getEndDate().replace("-", ""));
            }
            //处理业务返回结果
            Result<AdPerformanceVo> res = cpcSdAdProductService.showAdPerformance(request.getPuid(), param);
            builder.setCode(res.getCode());
            if (res.getMsg()!=null) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                //处理data
                AdPerformanceVo data = res.getData();
                if (data!=null) {
                    AdPerformanceRpcVo.Builder voBuilder = AdPerformanceRpcVo.newBuilder();
                    if (data.getShopId()!=null) {
                        voBuilder.setShopId(data.getShopId());
                    }
                    if (data.getCampaignId()!=null) {
                        voBuilder.setCampaignId(data.getCampaignId());
                    }
                    if (data.getGroupId()!=null) {
                        voBuilder.setGroupId(data.getGroupId());
                    }
                    if (data.getKeywordId()!=null) {
                        voBuilder.setKeywordId(data.getKeywordId());
                    }
                    if (data.getTargetId()!=null) {
                        voBuilder.setTargetId(data.getTargetId());
                    }
                    if (data.getAdId()!=null) {
                        voBuilder.setAdId(data.getAdId());
                    }
                    if (data.getQuery()!=null) {
                        voBuilder.setQuery(data.getQuery());
                    }
                    if (data.getPlacement()!=null) {
                        voBuilder.setPlacement(data.getPlacement());
                    }
                    Map<String, CpcCommPageRpcVo> rpcVoMap = Maps.newHashMap();

                    if (MapUtils.isNotEmpty(data.getMap())) {
                        for (Map.Entry<String, CpcCommPageVo> entry : data.getMap().entrySet()) {
                            CpcCommPageVo vo = entry.getValue();
                            //vo转message
                            CpcCommPageRpcVo.Builder cpcRpcVo = CpcCommPageRpcVo.newBuilder();
                            cpcRpcVo.setImpressions(Optional.ofNullable(vo.getImpressions()).orElse(0));
                            cpcRpcVo.setClicks(Optional.ofNullable(vo.getClicks()).orElse(0));
                            cpcRpcVo.setCtr(StringUtils.isNotBlank(vo.getCtr()) ? vo.getCtr(): "0");
                            cpcRpcVo.setCvr(StringUtils.isNotBlank(vo.getCvr()) ? vo.getCvr(): "0");
                            cpcRpcVo.setAcos(StringUtils.isNotBlank(vo.getAcos()) ? vo.getAcos(): "0");
                            cpcRpcVo.setRoas(StringUtils.isNotBlank(vo.getRoas()) ? vo.getRoas(): "0");
                            cpcRpcVo.setAcots(StringUtils.isNotBlank(vo.getAcots()) ? vo.getAcots(): "0");
                            cpcRpcVo.setAdOrderNum(Optional.ofNullable(vo.getAdOrderNum()).orElse(0));
                            cpcRpcVo.setAdCost(StringUtils.isNotBlank(vo.getAdCost()) ? vo.getAdCost(): "0");
                            cpcRpcVo.setAdCostPerClick(StringUtils.isNotBlank(vo.getAdCostPerClick()) ? vo.getAdCostPerClick(): "0");
                            cpcRpcVo.setAdSale(StringUtils.isNotBlank(vo.getAdSale()) ? vo.getAdSale(): "0");

                            rpcVoMap.put(entry.getKey(),cpcRpcVo.build());
                        }

                        voBuilder.putAllMap(rpcVoMap);
                    }
                    builder.setData(voBuilder.build());
                }
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getExistProduct(GetExistSdProductRequest request, StreamObserver<GetExistSdProductResponse> responseObserver) {
        log.info("sd-product查询广告组存在产品推广 request {}", request);
        GetExistSdProductResponse.Builder builder = GetExistSdProductResponse.newBuilder();

        List<String> list = amazonSdAdProductDao.listSkus(request.getPuid(), request.getShopId(), request.getAdGroupId(), Lists.newArrayList(CpcStatusEnum.paused.name(), CpcStatusEnum.enabled.name()));
        builder.setCode(Result.SUCCESS);
        builder.addAllData(list);

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 创建广告产品
     * @param request
     * @param responseObserver
     */
    @Override
    public void create(CreateSdProductRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sd-product创建 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasPuid() || !request.hasShopId() || !request.hasUid()
                || CollectionUtils.isEmpty(request.getProductList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else  {
            AddAdProductVo vo = new AddAdProductVo();
            vo.setPuid(request.getPuid().getValue());
            vo.setShopId(request.getShopId().getValue());
            vo.setUid(request.getUid().getValue());
            vo.setGroupId(request.getGroupId());
            vo.setLoginIp(request.getLoginIp());
            //处理list
            List<ProductRpcVo> productList = request.getProductList();
            if (CollectionUtils.isNotEmpty(productList)) {
                List<ProductVo> vos = productList.stream().filter(Objects::nonNull).map(item -> {
                    ProductVo productVo = new ProductVo();
                    BeanUtils.copyProperties(item, productVo);
                    return productVo;
                }).collect(Collectors.toList());
                vo.setProducts(vos);
            }

            Result res = cpcSdAdProductService.addProduct(vo);

            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    @Override
    public void createNew(CreateSdProductNewRequest request, StreamObserver<ProductResponse> responseObserver) {
        log.debug("新版SD广告产品创建 request {}", request);
        ProductResponse.Builder builder = ProductResponse.newBuilder();

        if (!request.hasPuid() || !request.hasShopId() || !request.hasUid()
                || CollectionUtils.isEmpty(request.getProductList())) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId().getValue(), request.getPuid().getValue());
        if (Objects.isNull(shop)) {
            builder.setCode(Result.ERROR);
            builder.setMsg("没有CPC授权");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(request.getPuid().getValue(), request.getShopId().getValue());
        if (Objects.isNull(profile)) {
            builder.setCode(Result.ERROR);
            builder.setMsg("没有站点对应的配置信息");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }

        Result<List<AmazonSdAdProduct>> res = cpcSdAdProductService.addProductNew(shop, profile,
                request.getCampaignId(), request.getGroupId(), request);

        if (Objects.nonNull(res)) {
            builder.setMsg(res.getMsg());
            builder.setCode(res.getCode());
            List<AmazonSdAdProduct> productList = res.getData();
            List<ProductResponse.ProductVOResponse> responseList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(productList)) {
                for (AmazonSdAdProduct product : productList) {
                    ProductResponse.ProductVOResponse.Builder voBuilder = ProductResponse.ProductVOResponse.newBuilder();
                    voBuilder.setAdId(product.getAdId());
                    responseList.add(voBuilder.build());
                }
            }
            builder.addAllData(responseList);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 更新状态
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateState(UpdateSdProductStateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sd-product更新状态 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        String stateValue = StateEnum.getStateValue(request.getState());
        if (!request.hasPuid() || !request.hasShopId() || !request.hasUid() || StringUtils.isBlank(stateValue)
                || StringUtils.isBlank(request.getProductId())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else  {
            Result res = cpcSdAdProductService.updateState(request.getPuid().getValue(), request.getShopId().getValue(), request.getUid().getValue(), request.getProductId(), request.getState());
            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    /**
     * 归档
     * @param request
     * @param responseObserver
     */
    @Override
    public void archive(ArchiveSdProductRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sd-product归档 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasPuid() || !request.hasShopId() || !request.hasUid()
                || StringUtils.isBlank(request.getProductId())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            Result res = cpcSdAdProductService.archive(request.getPuid().getValue(), request.getShopId().getValue(), request.getUid().getValue(), request.getProductId(), request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    /**
     * 更新状态
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateBatchState(UpdateBatchSdProductStateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sd-product更新状态 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if ( !request.hasPuid() || !request.hasUid()  || !request.hasShopId() || request.getVosCount() < 1) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else  {
            List<AmazonSdAdProduct> amazonAdProducts = request.getVosList().stream().map(vo -> {
                AmazonSdAdProduct amazonAdProduct = new AmazonSdAdProduct();
                amazonAdProduct.setPuid(request.getPuid().getValue());
                amazonAdProduct.setUpdateId(request.getUid().getValue());
                amazonAdProduct.setShopId(request.getShopId().getValue());
                if (vo.hasId()) {
                    amazonAdProduct.setId(vo.getId().getValue());
                }
                amazonAdProduct.setState(vo.getState());
                return amazonAdProduct;
            }).collect(Collectors.toList());
            Result res = cpcSdAdProductService.updateBatchState(amazonAdProducts, request.getIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if(res.getData() != null ){
                builder.setData(JSONUtil.objectToJson(res.getData()));
            }
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    @Override
    public void getProductAsinByGroupId(GetProductAsinByGroupRequest request, StreamObserver<GetProductAsinByGroupResponse> responseObserver) {
        log.debug("根据广告组Id获取其下所有广告产品中的Asin request {}", request);
        GetProductAsinByGroupResponse.Builder builder = GetProductAsinByGroupResponse.newBuilder();

        if (request.getPuid() == 0 || request.getShopId() == 0 || StringUtils.isEmpty(request.getGroupId())) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId(), request.getPuid());
        if (Objects.isNull(shop)) {
            builder.setCode(Result.ERROR);
            builder.setMsg("没有CPC授权");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(request.getPuid(), request.getShopId());
        if (Objects.isNull(profile)) {
            builder.setCode(Result.ERROR);
            builder.setMsg("没有站点对应的配置信息");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }

        List<String> asinList = cpcSdAdProductService.getProductAsinByGroupId(shop, profile,
                request.getUid(), request.getGroupId(), request.getLoginIp());

        builder.setCode(Result.SUCCESS);
        if (CollectionUtils.isNotEmpty(asinList)) {
            GetProductAsinByGroupResponse.ProductAsinInfo.Builder asinInfoBuilder = GetProductAsinByGroupResponse.ProductAsinInfo.newBuilder();
            asinInfoBuilder.addAllAsin(asinList);
            builder.setData(asinInfoBuilder.build());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
