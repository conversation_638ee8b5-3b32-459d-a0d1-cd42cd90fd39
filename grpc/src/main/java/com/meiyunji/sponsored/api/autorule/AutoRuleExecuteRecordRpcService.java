package com.meiyunji.sponsored.api.autorule;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.grpc.autorule.common.HandleExecuteRecordMessageRequestPb;
import com.meiyunji.sponsored.grpc.autorule.common.HandleExecuteRecordMessageResponsePb;
import com.meiyunji.sponsored.grpc.autorule.common.UpdateExecuteRecordMessageStateRequestPb;
import com.meiyunji.sponsored.grpc.autorule.common.UpdateExecuteRecordMessageStateResponsePb;
import com.meiyunji.sponsored.rpc.autorule.executeRecord.*;
import com.meiyunji.sponsored.rpc.autorule.executeRecord.PerformOperation;
import com.meiyunji.sponsored.rpc.autorule.executeRecord.Rule;
import com.meiyunji.sponsored.rpc.autorule.template.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleExecuteRecordDao;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleTemplateDao;
import com.meiyunji.sponsored.service.autoRule.enums.AfterOperationRankEnum;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleRecordStatus;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleTypeEnum;
import com.meiyunji.sponsored.service.autoRule.enums.OperationEnum;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleExecuteRecord;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleTemplate;
import com.meiyunji.sponsored.service.autoRule.service.IAdvertiseAutoRuleExecuteRecordService;
import com.meiyunji.sponsored.service.autoRule.util.AutoRuleExecuteRecordUtils;
import com.meiyunji.sponsored.service.autoRule.util.AutoRuleJsonToGrpcUtil;
import com.meiyunji.sponsored.service.autoRule.vo.*;
import com.meiyunji.sponsored.service.autoRuleTask.enums.TaskItemType;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdGroupDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProductMetadata;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdGroup;
import com.meiyunji.sponsored.service.cpc.po.PostcodeData;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdProductMetadataService;
import com.meiyunji.sponsored.service.enums.RuleAdjustTypeEnum;
import com.meiyunji.sponsored.service.grabRankings.service.GrabRankingsPostcodeService;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.ShopByPuidDto;
import com.meiyunji.sponsored.service.sellfoxApi.IProductApi;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import com.meiyunji.sponsored.service.vo.ProductAdReportVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@GRpcService
@Slf4j
public class AutoRuleExecuteRecordRpcService extends RPCAutoRuleExecuteRecordServiceGrpc.RPCAutoRuleExecuteRecordServiceImplBase {
    @Autowired
    private IAdvertiseAutoRuleExecuteRecordDao advertiseAutoRuleExecuteRecordDao;
    @Autowired
    private IAdvertiseAutoRuleTemplateDao advertiseAutoRuleTemplateDao;
    @Autowired
    private IAdvertiseAutoRuleExecuteRecordService advertiseAutoRuleExecuteRecordService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProductMetadataService amazonAdProductMetadataService;
    @Autowired
    private IProductApi productDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Resource
    private GrabRankingsPostcodeService grabRankingsPostcodeService;

    //排名结果达标率
    private static final BigDecimal TARGET_RATE = new BigDecimal("0.75");

    @Override
    public void pageList(AutoRuleExecuteRecordPageRequest request, StreamObserver<AutoRuleExecuteRecordPageResponse> responseObserver) {
        log.info("分页查询执行记录 pageList request:{}", request);
        AutoRuleExecuteRecordPageResponse.Builder responseBuilder = AutoRuleExecuteRecordPageResponse.newBuilder();
        AutoRuleExecuteRecordPage.Builder pageBuilder = AutoRuleExecuteRecordPage.newBuilder();
        List<Integer> allValidShopIds = request.getShopIdsList();
        ExecuteRecordPageParam param = new ExecuteRecordPageParam();
        param.setPuid(request.getPuid());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setShopIdList(allValidShopIds);

        //查询待确认记录数
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        int toBeConfirmedCount = Optional.ofNullable(advertiseAutoRuleExecuteRecordDao.queryToBeConfirmedCount(request.getPuid(), allValidShopIds)).orElse(0);
        stopWatch.stop();
        log.info("queryToBeConfirmedCount time:{}", stopWatch.getTotalTimeSeconds());

        //计算code
        List<String> codeList = computeCode(request);
        if (CollectionUtils.isEmpty(codeList)) {
            buildBlankResp(toBeConfirmedCount, request.getPageNo(), request.getPageSize(), responseBuilder, pageBuilder);
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        } else {
            param.setCodes(codeList);
        }
        if (StringUtils.isNotBlank(request.getStartDate())) {
            param.setStartDate(LocalDate.parse(request.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
        if (StringUtils.isNotBlank(request.getEndDate())) {
            param.setEndDate(LocalDate.parse(request.getEndDate(),DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(1));
        }
        if (CollectionUtils.isNotEmpty(request.getRuleTypesList())) {
            param.setRuleTypes(request.getRuleTypesList());
        }
        if (StringUtils.isNotBlank(request.getItemSearchValue())) {
            param.setItemSearchValue(request.getItemSearchValue());
        }
        if (StringUtils.isNotBlank(request.getTemplateSearchValue())) {
            param.setTemplateSearchValue(request.getTemplateSearchValue());
        }
        if (CollectionUtils.isNotEmpty(request.getAdTypeList())) {
            List<String> collect = request.getAdTypeList().stream().map(x -> x.toUpperCase(Locale.ROOT)).collect(Collectors.toList());
            param.setAdTypeList(collect);
        }

        //广告组
        if (CollectionUtils.isNotEmpty(request.getAdGroupIdsList())) {
            param.setAdGroupIdList(request.getAdGroupIdsList());
        } else {
            if (CollectionUtils.isNotEmpty(request.getCampaignIdsList())) {
                param.setCampaignIdList(request.getCampaignIdsList());
            } else {
                if (CollectionUtils.isNotEmpty(request.getPortfolioIdsList())) {
                    List<String> campaignIdList = amazonAdCampaignAllDao.getCampaignIdByShopAndPortfolio(request.getPuid(), allValidShopIds, request.getPortfolioIdsList());
                    if (CollectionUtils.isNotEmpty(campaignIdList)) {
                        param.setCampaignIdList(campaignIdList);
                    } else {
                        buildBlankResp(toBeConfirmedCount, request.getPageNo(), request.getPageSize(), responseBuilder, pageBuilder);
                        responseObserver.onNext(responseBuilder.build());
                        responseObserver.onCompleted();
                        return;
                    }
                }
            }
        }

        //itemType,dao层通过AutoRuleRecordItemType处理为条件
        if (CollectionUtils.isNotEmpty(request.getItemTypesList())) {
            param.setItemTypeList(request.getItemTypesList());
        }

        //查询数据库
        Result<Page<AdvertiseAutoRuleExecuteRecord>> result = advertiseAutoRuleExecuteRecordService.pageList(param);
        responseBuilder.setCode(result.getCode());
        if (result.getCode() != Result.SUCCESS) {
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        //组装grpc响应数据
        Page<AdvertiseAutoRuleExecuteRecord> page = result.getData();
        pageBuilder.setPageNo(page.getPageNo());
        pageBuilder.setPageSize(page.getPageSize());
        pageBuilder.setTotalPage(page.getTotalPage());
        pageBuilder.setTotalSize(page.getTotalSize());
        pageBuilder.setToBeConfirmedCount(toBeConfirmedCount);

        if (CollectionUtils.isEmpty(page.getRows())) {
            responseBuilder.setData(pageBuilder);
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        Map<String, Set<String>> postcodeMap = new ConcurrentHashMap<>();

        List<AutoRuleExecuteRecordRpc.Builder> list = Lists.newArrayListWithCapacity(page.getRows().size());
        //获取所有的店铺名称
        List<Integer> shopIds = page.getRows().stream().peek(e-> {
                    if (AutoRuleTypeEnum.keywordCard.getValue().equalsIgnoreCase(e.getRuleType())) {
                        if (StringUtils.isNotBlank(e.getPostalCodeSettings())) {
                            Set<String> orDefault = postcodeMap.getOrDefault(e.getMarketplaceId(), new HashSet<>());
                            orDefault.addAll(StringUtil.splitStr(e.getPostalCodeSettings()));
                            postcodeMap.put(e.getMarketplaceId(), orDefault);
                        }
                    }
                }).
                map(AdvertiseAutoRuleExecuteRecord::getShopId).distinct().collect(Collectors.toList());
        List<Long> templateIds = page.getRows().stream().
                map(AdvertiseAutoRuleExecuteRecord::getTemplateId).distinct().collect(Collectors.toList());
        List<ShopAuth> shops = shopAuthDao.getScAndVcByIds(shopIds);
        List<AdvertiseAutoRuleTemplate> advertiseAutoRuleTemplateList = advertiseAutoRuleTemplateDao.listByIdList(request.getPuid(),templateIds);
        Map<Integer, ShopAuth> shopAuthMap = null;
        if (CollectionUtils.isNotEmpty(shops)) {
            shopAuthMap = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
        }
        Map<Long, AdvertiseAutoRuleTemplate> advertiseAutoRuleTemplateMap = null;
        if (CollectionUtils.isNotEmpty(advertiseAutoRuleTemplateList)) {
            advertiseAutoRuleTemplateMap = advertiseAutoRuleTemplateList.stream().collect(Collectors.toMap(AdvertiseAutoRuleTemplate::getId, Function.identity()));
        }
        Map<Integer, ShopAuth> finalShopAuthMap = shopAuthMap;
        Map<Long, AdvertiseAutoRuleTemplate> finalAdvertiseAutoRuleTemplateMap = advertiseAutoRuleTemplateMap;


        //记录adGroupId
        Set<String> adGroupIdSet = new HashSet<>();
        //记录shopId
        Set<Integer> shopIdSet = new HashSet<>();
        //记录记录和performOperation映射
        Map<Long, List<PerformOperation.Builder>> performOperationBuilderMap = new HashMap<>();
        Map<String, PostcodeData> postcodeByCode = new HashMap<>();
        if (MapUtils.isNotEmpty(postcodeMap)) {
            postcodeByCode = grabRankingsPostcodeService.getPostcodeByCode(postcodeMap);
        }
        Map<String, PostcodeData> finalPostcodeByCode = postcodeByCode;
        page.getRows().forEach(e -> {
            AutoRuleExecuteRecordRpc.Builder builder = AutoRuleExecuteRecordRpc.newBuilder();
            builder.setId(e.getId());
            builder.setPuid(e.getPuid());
            builder.setShopId(e.getShopId());
            if (MapUtils.isNotEmpty(finalShopAuthMap) && finalShopAuthMap.containsKey(e.getShopId())) {
                builder.setShopName(finalShopAuthMap.get(e.getShopId()).getName());
            }
            builder.setMarketplaceId(e.getMarketplaceId());
            builder.setAdType(e.getAdType());
            builder.setCode(e.getCode());
            builder.setRecordId(e.getRecordId());
            builder.setHasSimilarRule(e.getHasSimilarRule());
            if (e.getContinuousCount() != null) {
                builder.setContinuousCount(e.getContinuousCount());
            }
            if (StringUtils.isNotBlank(e.getUnadjustedReason())) {
                builder.setUnadjustedReason(e.getUnadjustedReason());
            }
            builder.setCountResult(Optional.ofNullable(e.getCountResult()).orElse(0));
            builder.setOvertopCount(Optional.ofNullable(e.getOvertopCount()).orElse(0));
            if (StringUtils.isNotBlank(e.getAfterOperationRank())) {
                builder.setAfterOperationRank(e.getAfterOperationRank());
            }
            if (StringUtils.isNotBlank(e.getOperation())) {
                builder.setOperation(e.getOperation());
            }
            // 操作动作为不调整时计算排名结果
            if (OperationEnum.UNADJUSTED.getValue().equals(builder.getOperation()) && builder.getCountResult() != 0) {
                BigDecimal targetNum = MathUtil.multiply(BigDecimal.valueOf(builder.getCountResult()), TARGET_RATE);
                if (targetNum.compareTo(BigDecimal.valueOf(builder.getOvertopCount())) <= 0) {
                    builder.setAfterOperationRank(AfterOperationRankEnum.OVERTOP.getValue());
                } else {
                    builder.setAfterOperationRank(AfterOperationRankEnum.LOWER_THEN.getValue());
                }
            }

            builder.setCampaignId(StringUtils.isNotBlank(e.getCampaignId()) ? e.getCampaignId() : "");
            builder.setAdGroupId(StringUtils.isNotBlank(e.getAdGroupId()) ? e.getAdGroupId() : "");
            builder.setCampaignName(StringUtils.isNotBlank(e.getCampaignName()) ? e.getCampaignName() : "");
            builder.setAdGroupName(StringUtils.isNotBlank(e.getAdGroupName()) ? e.getAdGroupName() : "");
            builder.setPortfolioId(StringUtils.isNotBlank(e.getPortfolioId()) ? e.getPortfolioId() : "");
            builder.setPortfolioName(StringUtils.isNotBlank(e.getPortfolioName()) ? e.getPortfolioName() : "");

            if ("KEYWORD_TARGET".equals(e.getItemType())) {
                if ("restoreBid".equals(e.getOperation()) || "restorePlacementTopBidRatio".equals(e.getOperation())) {
                    if (StringUtils.isNotBlank(e.getItemStatus())) {
                        builder.setItemStatus(e.getItemStatus());
                    } else {
                        builder.setItemStatus("NORMAL");
                    }
                }
                if (StringUtils.isNotBlank(e.getSku())) {
                    Map<String, List<ProductAdReportVo>> asinImageMap = null;
                    Map<String, List<AmazonAdProductMetadata>> metadataMap = null;
                    List<String> skus = StringUtil.stringToList(e.getSku(),",");
                    Map<String, List<ProductAdReportVo>> hashMap = new HashMap<>();
                    Map<String, List<AmazonAdProductMetadata>> map = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(skus)) {
                        List<AmazonAdProductMetadata> amazonAdProductMetadataList = amazonAdProductMetadataService.getAsinBySkus(e.getPuid(),e.getShopId(),skus,null);
                        if (CollectionUtils.isNotEmpty(amazonAdProductMetadataList)) {
                            map.putAll(amazonAdProductMetadataList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AmazonAdProductMetadata::getSku)));
                            metadataMap = map;
                        }
                        // 兼容老代码逻辑
                        List<List<String>> partition = com.google.common.collect.Lists.partition(skus, 100);
                        for (List<String> batchSkus : partition) {
                            List<ProductAdReportVo> asinBySkus = productDao.getAsinBySkus(e.getPuid(), e.getShopId(), "", batchSkus);
                            if (CollectionUtils.isNotEmpty(asinBySkus)) {
                                hashMap.putAll(asinBySkus.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(ProductAdReportVo::getSku)));
                            }
                        }
                        asinImageMap = hashMap;
                    }
                    Map<String, List<AmazonAdProductMetadata>> finalMetadataMap = metadataMap;
                    Map<String, List<ProductAdReportVo>> finalAsinImageMap = asinImageMap;
                    skus.forEach(sk->{
                        ProductRpc.Builder productBuilder = ProductRpc.newBuilder();
                        if (MapUtils.isNotEmpty(finalMetadataMap) && finalMetadataMap.containsKey(sk)) {
                            if (StringUtils.isNotBlank(finalMetadataMap.get(sk).get(0).getImageUrl())) {
                                productBuilder.setImageUrl(finalMetadataMap.get(sk).get(0).getImageUrl());
                            }
                        } else if (MapUtils.isNotEmpty(finalAsinImageMap) && finalAsinImageMap.containsKey(sk)) {
                            if (StringUtils.isNotBlank(finalAsinImageMap.get(sk).get(0).getMainImage())) {
                                productBuilder.setImageUrl(finalAsinImageMap.get(sk).get(0).getMainImage());
                            }
                        }
                        productBuilder.setSku(sk);
                        builder.addProductList(productBuilder.build());
                    });
                }
                if (StringUtils.isNotBlank(e.getAsin())) {
                    builder.setAsin(e.getAsin());
                }
                if (StringUtils.isNotBlank(e.getPostalCodeSettings())){
                    builder.setPostalCodeSettings(e.getPostalCodeSettings());
                }
                if (StringUtils.isNotBlank(e.getCheckFrequency())) {
                    builder.setCheckFrequency(e.getCheckFrequency());
                }
                if (MapUtils.isNotEmpty(finalAdvertiseAutoRuleTemplateMap) && finalAdvertiseAutoRuleTemplateMap.containsKey(e.getTemplateId())) {
                    AdvertiseAutoRuleTemplate template = finalAdvertiseAutoRuleTemplateMap.get(e.getTemplateId());
                    if ("wx".equals(template.getPushMessageType())) {
                        builder.setMessageReminder("排名高于预期微信消息通知");
                    } else if ("instation".equals(template.getPushMessageType())) {
                        builder.setMessageReminder("排名高于预期站内消息通知");
                    } else if ("all".equals(template.getPushMessageType())) {
                        builder.setMessageReminder("排名高于预期站内微信消息通知");
                    } else {
                        builder.setMessageReminder("不通知");
                    }
                } else {
                    builder.setMessageReminder("不通知");
                }
                if (StringUtils.isNotBlank(e.getTimeType())) {
                    builder.setTimeType(e.getTimeType());
                }
                if (StringUtils.isNotBlank(e.getTimeRule())) {
                    Map<Integer,List<TimeRuleJson>> timeRuleMap = JSONUtil.jsonToArray(e.getTimeRule(),TimeRuleJson.class).stream().
                            collect(Collectors.groupingBy(TimeRuleJson::getSiteDate));
                    if (MapUtils.isNotEmpty(timeRuleMap)) {
                        timeRuleMap.forEach((k, v) -> {
                            TimeRule.Builder timeRuleBuilder = TimeRule.newBuilder();
                            timeRuleBuilder.setSiteDate(k);
                            v.forEach(t -> {
                                Time.Builder timeBuilder = Time.newBuilder();
                                timeBuilder.setStartTimeSite(t.getStartTimeSite());
                                timeBuilder.setEndTimeSite(t.getEndTimeSite());
                                timeRuleBuilder.addTimes(timeBuilder.build());
                            });
                            builder.addTimeRules(timeRuleBuilder.build());
                        });
                    }
                }
                if (StringUtils.isNotBlank(e.getDesiredPosition())) {
                    DesiredPositionJson desiredPositionJson = JSONUtil.jsonToObject(e.getDesiredPosition(),DesiredPositionJson.class);
                    if (desiredPositionJson != null) {
                        DesiredPosition.Builder desBuilder = DesiredPosition.newBuilder();
                        desBuilder.setPage(desiredPositionJson.getPage());
                        desBuilder.setFrom(desiredPositionJson.getFrom());
                        desBuilder.setTo(desiredPositionJson.getTo());
                        builder.setDesiredPosition(desBuilder.build());
                    }
                }
                if (StringUtils.isNotBlank(e.getAdDataRule())) {
                    List<AdDataRuleJson> adDataRuleJsons = JSONUtil.jsonToArray(e.getAdDataRule(),AdDataRuleJson.class);
                    if (CollectionUtils.isNotEmpty(adDataRuleJsons)) {
                        adDataRuleJsons.forEach(a->{
                            AdDataRule.Builder adDataRuleBuilder = AdDataRule.newBuilder();
                            adDataRuleBuilder.setDay(a.getDay().toString());
                            if (a.getExcludeDay() != null && !a.getExcludeDay().equals(0)) {
                                adDataRuleBuilder.setDay(a.getDay().toString() + "-" + a.getExcludeDay().toString());
                            }

                            adDataRuleBuilder.setRuleIndex(a.getRuleIndex());
                            adDataRuleBuilder.setRuleValue(a.getRuleValue());
                            adDataRuleBuilder.setRuleOperatorType(a.getRuleOperator());
                            adDataRuleBuilder.setRuleStatisticalModeType(a.getRuleStatisticalModeType());
                            builder.addAdDataRules(adDataRuleBuilder.build());
                        });
                    }
                }
                if (StringUtils.isNotBlank(e.getAdDataOperate())) {
                    AdDataOperateJson adDataOperateJson = JSONUtil.jsonToObject(e.getAdDataOperate(),AdDataOperateJson.class);
                    if (adDataOperateJson != null) {
//                        AdDataOperate.Builder adDataOperateBuilder = AdDataOperate.newBuilder();
//                        adDataOperateBuilder.setRuleAction(adDataOperateJson.getRuleAction());
//                        adDataOperateBuilder.setRuleAdjust(adDataOperateJson.getRuleAdjust());
//                        adDataOperateBuilder.setAdjustValue(adDataOperateJson.getAdJustValue());
//                        adDataOperateBuilder.setLimitValue(adDataOperateJson.getLimitValue());
//                        if (Objects.nonNull(adDataOperateJson.getBid())) {
//                            BidOperate.Builder bidBuilder = BidOperate.newBuilder();
//                            bidBuilder.setRuleAction(adDataOperateJson.getBid().getRuleAction());
//                            bidBuilder.setRuleAdjust(adDataOperateJson.getBid().getRuleAdjust());
//                            bidBuilder.setAdjustValue(adDataOperateJson.getBid().getAdJustValue());
//                            bidBuilder.setLimitValue(adDataOperateJson.getBid().getLimitValue());
//                            adDataOperateBuilder.setBid(bidBuilder.build());
//
//                        }
//                        if (Objects.nonNull(adDataOperateJson.getPlacementTopBidRatio())) {
//                            BidOperate.Builder bidBuilder = BidOperate.newBuilder();
//                            bidBuilder.setRuleAction(adDataOperateJson.getPlacementTopBidRatio().getRuleAction());
//                            bidBuilder.setRuleAdjust(adDataOperateJson.getPlacementTopBidRatio().getRuleAdjust());
//                            bidBuilder.setAdjustValue(adDataOperateJson.getPlacementTopBidRatio().getAdJustValue());
//                            bidBuilder.setLimitValue(adDataOperateJson.getPlacementTopBidRatio().getLimitValue());
//                            adDataOperateBuilder.setBid(bidBuilder.build());
//                        }
//                        builder.setAdDataOperate(adDataOperateBuilder.build());
                        builder.setAdDataOperate(AutoRuleJsonToGrpcUtil.buildAdDataOperate(e.getAdDataOperate()));
                    }
                }
                if (StringUtils.isNotBlank(e.getAutoPriceRule())) {
                    AutoPriceRuleJson autoPriceRuleJson = JSONUtil.jsonToObject(e.getAutoPriceRule(),AutoPriceRuleJson.class);
                    if (autoPriceRuleJson != null) {
                        AutoPriceRule.Builder autoPriceRuleBuilder = AutoPriceRule.newBuilder();
                        autoPriceRuleBuilder.setContinuousFrequency(autoPriceRuleJson.getContinuousFrequency());
                        autoPriceRuleBuilder.setPage(autoPriceRuleJson.getPage());
                        autoPriceRuleBuilder.setRank(autoPriceRuleJson.getRank());
                        builder.setAutoPriceRule(autoPriceRuleBuilder.build());
                    }
                }
                if (StringUtils.isNotBlank(e.getAutoPriceOperate())) {
                    AutoPriceOperateJson autoPriceOperateJson = JSONUtil.jsonToObject(e.getAutoPriceOperate(),AutoPriceOperateJson.class);
                    if (autoPriceOperateJson != null) {
//                        AutoPriceOperate.Builder apoBuilder = AutoPriceOperate.newBuilder();
//                        apoBuilder.setRuleAction(autoPriceOperateJson.getRuleAction());
//                        apoBuilder.setRuleAdjust(autoPriceOperateJson.getRuleAdjust());
//                        apoBuilder.setAdjustValue(autoPriceOperateJson.getAdJustValue());
//                        apoBuilder.setLimitValue(autoPriceOperateJson.getLimitValue());
//                        if (Objects.nonNull(autoPriceOperateJson.getBid())) {
//                            BidOperate.Builder bidBuilder = BidOperate.newBuilder();
//                            bidBuilder.setRuleAction(autoPriceOperateJson.getBid().getRuleAction());
//                            bidBuilder.setRuleAdjust(autoPriceOperateJson.getBid().getRuleAdjust());
//                            bidBuilder.setAdjustValue(autoPriceOperateJson.getBid().getAdJustValue());
//                            bidBuilder.setLimitValue(autoPriceOperateJson.getBid().getLimitValue());
//                            apoBuilder.setBid(bidBuilder.build());
//
//                        }
//                        if (Objects.nonNull(autoPriceOperateJson.getPlacementTopBidRatio())) {
//                            BidOperate.Builder bidBuilder = BidOperate.newBuilder();
//                            bidBuilder.setRuleAction(autoPriceOperateJson.getPlacementTopBidRatio().getRuleAction());
//                            bidBuilder.setRuleAdjust(autoPriceOperateJson.getPlacementTopBidRatio().getRuleAdjust());
//                            bidBuilder.setAdjustValue(autoPriceOperateJson.getPlacementTopBidRatio().getAdJustValue());
//                            bidBuilder.setLimitValue(autoPriceOperateJson.getPlacementTopBidRatio().getLimitValue());
//                            apoBuilder.setBid(bidBuilder.build());
//                        }
//                        builder.setAutoPriceOperate(apoBuilder.build());
                        builder.setAutoPriceOperate(AutoRuleJsonToGrpcUtil.buildAutoPriceOperate(e.getAutoPriceOperate()));
                    }
                }
                if (StringUtils.isNotBlank(e.getBiddingCallbackOperate())) {
                    BiddingCallbackOperateJson biddingCallbackOperateJson = JSONUtil.jsonToObject(e.getBiddingCallbackOperate(),
                            BiddingCallbackOperateJson.class);
                    if (biddingCallbackOperateJson != null) {
//                        BiddingCallbackOperate.Builder backBuilder = BiddingCallbackOperate.newBuilder();
//                        backBuilder.setAdjustType(biddingCallbackOperateJson.getAdjustType());
//                        backBuilder.setAdjustValue(biddingCallbackOperateJson.getAdJustValue());
//                        if (biddingCallbackOperateJson.getLimitValue() != null) {
//                            backBuilder.setLimitValue(biddingCallbackOperateJson.getLimitValue());
//                        }
//                        if (Objects.nonNull(biddingCallbackOperateJson.getBid())) {
//                            BidOperate.Builder bidBuilder = BidOperate.newBuilder();
//                            bidBuilder.setAdjustType(biddingCallbackOperateJson.getBid().getRuleAction());
//                            bidBuilder.setAdjustValue(biddingCallbackOperateJson.getBid().getAdJustValue());
//                            if (biddingCallbackOperateJson.getBid().getLimitValue() != null) {
//                                bidBuilder.setLimitValue(biddingCallbackOperateJson.getBid().getLimitValue());
//                            }
//                            backBuilder.setBid(bidBuilder.build());
//                        }
//                        if (Objects.nonNull(biddingCallbackOperateJson.getPlacementTopBidRatio())) {
//                            BidOperate.Builder bidBuilder = BidOperate.newBuilder();
//                            bidBuilder.setAdjustType(biddingCallbackOperateJson.getPlacementTopBidRatio().getRuleAction());
//                            bidBuilder.setAdjustValue(biddingCallbackOperateJson.getPlacementTopBidRatio().getAdJustValue());
//                            if (biddingCallbackOperateJson.getPlacementTopBidRatio().getLimitValue() != null) {
//                                bidBuilder.setLimitValue(biddingCallbackOperateJson.getPlacementTopBidRatio().getLimitValue());
//                            }
//                            backBuilder.setBid(bidBuilder.build());
//                        }
//                        builder.setBiddingCallbackOperate(backBuilder.build());
                        builder.setBiddingCallbackOperate(AutoRuleJsonToGrpcUtil.buildBiddingCallbackOperate(e.getBiddingCallbackOperate()));
                    }
                }
                if (StringUtils.isNotBlank(e.getAdDataDetail())) {
                    AdDataDetailJson adDataDetailJson = JSONUtil.jsonToObject(e.getAdDataDetail(),AdDataDetailJson.class);
                    DataDetail.Builder dataBuilder = DataDetail.newBuilder();
                    if (StringUtils.isNotBlank(adDataDetailJson.getOriginalValue())) {
                        dataBuilder.setOriginalValue(adDataDetailJson.getOriginalValue());
                    }
                    if (StringUtils.isNotBlank(adDataDetailJson.getExecuteValue())) {
                        dataBuilder.setExecuteValue(adDataDetailJson.getExecuteValue());
                    }
                    if (StringUtils.isNotBlank(adDataDetailJson.getAdjustType())) {
                        dataBuilder.setAdjustType(adDataDetailJson.getAdjustType());
                    }
                    if (StringUtils.isNotBlank(adDataDetailJson.getAdjustValue())) {
                        dataBuilder.setAdjustValue(adDataDetailJson.getAdjustValue());
                    }
                    if (StringUtils.isNotBlank(adDataDetailJson.getOperatorType())) {
                        dataBuilder.setOperatorType(adDataDetailJson.getOperatorType());
                    }
                    if (CollectionUtils.isNotEmpty(adDataDetailJson.getRuleIndexList())) {
                        adDataDetailJson.getRuleIndexList().forEach(i -> {
                            DataDetail.RuleIndexRpc.Builder rpcBuilder = DataDetail.RuleIndexRpc.newBuilder();
                            if (StringUtils.isNotBlank(i.getRuleIndex())) {
                                rpcBuilder.setRuleIndex(i.getRuleIndex());
                            }
                            if (StringUtils.isNotBlank(i.getRuleIndexValue())) {
                                rpcBuilder.setRuleIndexValue(i.getRuleIndexValue());
                            }
                            if (StringUtils.isNotBlank(i.getRuleStatisticalModeType())) {
                                rpcBuilder.setRuleStatisticalModeType(i.getRuleStatisticalModeType());
                            }

                            if (i.getDay() != null && !i.getDay().equals(0)) {
                                rpcBuilder.setDay(i.getDay().toString());
                            }
                            if (i.getExcludeDay() != null && !i.getExcludeDay().equals(0)) {
                                rpcBuilder.setDay(i.getDay().toString() + "-" + i.getExcludeDay().toString());
                            }
                            dataBuilder.addRuleIndexList(rpcBuilder.build());
                        });
                    }
                    builder.setAdDataDetails(dataBuilder.build());
                }
                if (StringUtils.isNotBlank(e.getAdRankDataDetail())) {
                    List<AdRankDataDetailJson> adRankDataDetailJsonList = JSONUtil.jsonToArray(e.getAdRankDataDetail(),AdRankDataDetailJson.class);
                    adRankDataDetailJsonList.forEach(adRankDataDetailJson -> {
                        AdRankDataDetail.Builder rankBuilder = AdRankDataDetail.newBuilder();
                        if (StringUtils.isNotBlank(adRankDataDetailJson.getCrawlTimeAt())) {
                            rankBuilder.setCrawlTimeAt(adRankDataDetailJson.getCrawlTimeAt());
                        }
                        if (adRankDataDetailJson.getRank() != null) {
                            rankBuilder.setRank(adRankDataDetailJson.getRank());
                        }
                        if (adRankDataDetailJson.getPage() != null) {
                            rankBuilder.setPage(adRankDataDetailJson.getPage());
                        }
                        if (adRankDataDetailJson.getIp() != null) {
                            rankBuilder.setIp(adRankDataDetailJson.getIp());
                        }
                        if (StringUtils.isNotBlank(adRankDataDetailJson.getTerminal())) {
                            rankBuilder.setTerminal(adRankDataDetailJson.getTerminal());
                        }
                        if (StringUtils.isNotBlank(adRankDataDetailJson.getPostalCodeSettings())) {
                            rankBuilder.setPostalCodeSettings(adRankDataDetailJson.getPostalCodeSettings());
                        }

                        if (AutoRuleTypeEnum.keywordCard.getValue().equalsIgnoreCase(e.getRuleType()) && StringUtils.isNotBlank(e.getPostalCodeSettings())) {
                            List<String> strings = StringUtil.splitStr(e.getPostalCodeSettings());
                            Map<String, String> postcode= new HashMap<>();
                            strings.forEach(item -> {
                                PostcodeData postcodeData = finalPostcodeByCode.get(grabRankingsPostcodeService.getMarketplaceIdAndPostcodeKey(e.getMarketplaceId(), item));
                                if (postcodeData != null) {
                                    postcode.put(item, grabRankingsPostcodeService.getFrontEndLabel(postcodeData));
                                }
                            });

                        }

                        PostcodeData postcodeData = finalPostcodeByCode.get(grabRankingsPostcodeService.getMarketplaceIdAndPostcodeKey(e.getMarketplaceId(), adRankDataDetailJson.getPostalCodeSettings()));
                        if (postcodeData != null) {
                            rankBuilder.setLabel(grabRankingsPostcodeService.getFrontEndLabel(postcodeData));
                        }

                        builder.addAdRankDataDetails(rankBuilder.build());
                    });
                }
                if (StringUtils.isNotBlank(e.getCheckFrequency())) {
                    builder.setCheckFrequency(e.getCheckFrequency());
                }
                if (e.getStartDate() != null) {
                    builder.setStartDate(e.getStartDate().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
                }
                if (e.getEndDate() != null) {
                    builder.setEndDate(e.getEndDate().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
                }
            } else {
                List<AutoRuleJson> ruleJsonList = JSONUtil.jsonToArray(e.getRule(), AutoRuleJson.class);
                ruleJsonList.forEach(r -> {
                    Rule.Builder ruleBuilder = Rule.newBuilder();
                    ruleBuilder.setDay(r.getDay().toString());
                    if (!"customRule".equals(e.getRuleType())) {
                        if (r.getExcludeDay() != null && !r.getExcludeDay().equals(0)) {
                            ruleBuilder.setDay(r.getDay().toString() + "-" + r.getExcludeDay().toString());
                        }
                    } else {
                        if (r.getExcludeDay() != null && r.getExcludeDay() != 0) {
                            ruleBuilder.setExcludeDay(r.getExcludeDay().toString());
                        } else {
                            ruleBuilder.setExcludeDay("");
                        }
                    }

                    ruleBuilder.setRuleIndex(r.getRuleIndex());
                    ruleBuilder.setRuleOperatorType(r.getRuleOperator());
                    ruleBuilder.setRuleStatisticalModeType(r.getRuleStatisticalModeType());
                    ruleBuilder.setRuleValue(r.getRuleValue());
                    if (StringUtils.isNotEmpty(r.getAfterRuleValue())) {
                        ruleBuilder.setAfterRuleValue(r.getAfterRuleValue());
                    }
                    if (StringUtils.isNotBlank(r.getExcludeCheckStatus())) {
                        ruleBuilder.setExcludeCheckStatus(r.getExcludeCheckStatus());
                    }
                    builder.addRuleList(ruleBuilder.build());
                });

                List<PerformOperationJson> performOperationJsonList = JSONUtil.jsonToArray(e.getPerformOperation(), PerformOperationJson.class);
                List<PerformOperation.Builder> performOperationList = new ArrayList<>(performOperationJsonList.size());
                performOperationJsonList.forEach(p -> {
                    PerformOperation.Builder performOperationBuilder = PerformOperation.newBuilder();
                    performOperationBuilder.setRuleAction(p.getRuleAction());
                    if (StringUtils.isNotBlank(p.getRuleAdjust())) {
                        //aadras返回的是大写入库，此处需要转换
                        if (StringUtils.equalsAny(p.getRuleAdjust(), RuleAdjustTypeEnum.legacyForSales.getDesc(), RuleAdjustTypeEnum.autoForSales.getDesc(), RuleAdjustTypeEnum.manual.getDesc())) {
                            performOperationBuilder.setRuleAdjust(RuleAdjustTypeEnum.descMap.getOrDefault(p.getRuleAdjust(), p.getRuleAdjust()));
                        } else {
                            performOperationBuilder.setRuleAdjust(p.getRuleAdjust());
                        }
                    }
                    if (StringUtils.isNotBlank(p.getAdJustValue())) {
                        performOperationBuilder.setAdjustValue(p.getAdJustValue());
                    }
                    if (StringUtils.isNotBlank(p.getLimitValue())) {
                        performOperationBuilder.setLimitValue(p.getLimitValue());
                    }
                    if (StringUtils.isNotBlank(p.getNegativeType())) {
                        performOperationBuilder.setNegativeType(p.getNegativeType());
                    }
                    if (StringUtils.isNotBlank(p.getBaseValueType())) {
                        performOperationBuilder.setBaseValueType(p.getBaseValueType());
                    } else {
                        performOperationBuilder.setBaseValueType("");
                    }

                    if (StringUtils.isNotBlank(p.getAdPlaceProductValue())) {
                        performOperationBuilder.setAdPlaceProductValue(p.getAdPlaceProductValue());
                    }
                    if (StringUtils.isNotBlank(p.getAdPlaceTopValue())) {
                        performOperationBuilder.setAdPlaceTopValue(p.getAdPlaceTopValue());
                    }
                    if (StringUtils.isNotBlank(p.getAdOtherValue())) {
                        performOperationBuilder.setAdOtherValue(p.getAdOtherValue());
                    }

                    if (StringUtils.isNotBlank(p.getCampaignId())) {
                        performOperationBuilder.setCampaignId(p.getCampaignId());
                    } else {
                        performOperationBuilder.setCampaignId("");
                    }

                    performOperationBuilder.setAdGroupName("");
                    if (StringUtils.isNotBlank(p.getAdGroupId())) {
                        performOperationBuilder.setAdGroupId(p.getAdGroupId());
                        if ((TaskItemType.GROUP_SEARCH_QUERY.getDesc().equals(e.getItemType())
                                || TaskItemType.SEARCH_QUERY.getDesc().equals(e.getItemType()))
                                && "addTarget".equals(p.getRuleAction())) {
                            if (p.getAdGroupId().equals(e.getAdGroupId())) {
                                performOperationBuilder.setAdGroupName("搜索词所在广告组");
                            } else {
                                adGroupIdSet.add(p.getAdGroupId());
                                shopIdSet.add(e.getShopId());
                            }
                        }
                    } else {
                        performOperationBuilder.setAdGroupId("");
                        if ((TaskItemType.GROUP_SEARCH_QUERY.getDesc().equals(e.getItemType())
                                || TaskItemType.SEARCH_QUERY.getDesc().equals(e.getItemType()))
                                && "addTarget".equals(p.getRuleAction())) {
                            performOperationBuilder.setAdGroupName("搜索词所在广告组");
                        }
                    }
                    performOperationList.add(performOperationBuilder);

                });
                performOperationBuilderMap.put(e.getId(), performOperationList);

                DataDetailJson dataDetailJson = JSONUtil.jsonToObject(e.getDataDetail(), DataDetailJson.class);
                DataDetail.Builder dataBuilder = DataDetail.newBuilder();
                if (StringUtils.isNotBlank(dataDetailJson.getOriginalValue())) {
                    dataBuilder.setOriginalValue(dataDetailJson.getOriginalValue());
                }
                if (StringUtils.isNotBlank(dataDetailJson.getExecuteValue())) {
                    dataBuilder.setExecuteValue(dataDetailJson.getExecuteValue());
                }
                dataDetailJson.getRuleIndexList().forEach(i -> {
                    DataDetail.RuleIndexRpc.Builder rpcBuilder = DataDetail.RuleIndexRpc.newBuilder();
                    if (StringUtils.isNotBlank(i.getRuleIndex())) {
                        rpcBuilder.setRuleIndex(i.getRuleIndex());
                    }
                    if (StringUtils.isNotBlank(i.getRuleIndexValue())) {
                        rpcBuilder.setRuleIndexValue(i.getRuleIndexValue());
                    }
                    if (i.getDay() != null && !i.getDay().equals(0)) {
                        rpcBuilder.setDay(i.getDay().toString());
                    }
                    if (i.getExcludeDay() != null && !i.getExcludeDay().equals(0)) {
                        rpcBuilder.setDay(i.getDay().toString() + "-" + i.getExcludeDay().toString());
                    }
                    dataBuilder.addRuleIndexList(rpcBuilder.build());
                });
                PerformOperationJson performOperationJson = performOperationJsonList.get(0);
                if (StringUtils.isNotBlank(performOperationJson.getAdJustValue())) {
                    dataBuilder.setAdjustValue(performOperationJson.getAdJustValue());
                }
                if ("stateClose".equals(performOperationJson.getRuleAction())) {
                    dataBuilder.setAdjustType("state");
                    dataBuilder.setOperatorType("close");
                } else if ("budgetAdd".equals(performOperationJson.getRuleAction())) {
                    dataBuilder.setAdjustType("budget");
                    dataBuilder.setOperatorType("add");
                } else if ("budgetReduce".equals(performOperationJson.getRuleAction())) {
                    dataBuilder.setAdjustType("budget");
                    dataBuilder.setOperatorType("reduce");
                } else if ("bidAdd".equals(performOperationJson.getRuleAction())) {
                    dataBuilder.setAdjustType("bid");
                    dataBuilder.setOperatorType("add");
                } else if ("bidReduce".equals(performOperationJson.getRuleAction())) {
                    dataBuilder.setAdjustType("bid");
                    dataBuilder.setOperatorType("reduce");
                } else if ("addNotTarget".equals(performOperationJson.getRuleAction())) {
                    if ("SEARCH_QUERY".equals(e.getItemOperateType())) {
                        dataBuilder.setAdjustType("searchQuery");
                    } else if ("TARGET".equals(e.getItemOperateType())) {
                        dataBuilder.setAdjustType("target");
                    }
                    dataBuilder.setOperatorType("addNotTarget");
                } else if ("addTarget".equals(performOperationJson.getRuleAction())) {
                    if ("SEARCH_QUERY".equals(e.getItemOperateType())) {
                        dataBuilder.setAdjustType("searchQuery");
                    } else if ("TARGET".equals(e.getItemOperateType())) {
                        dataBuilder.setAdjustType("target");
                    }
                    dataBuilder.setOperatorType("addTarget");
                } else if ("defaultBidAdd".equals(performOperationJson.getRuleAction())) {
                    dataBuilder.setAdjustType("defaultBid");
                    dataBuilder.setOperatorType("add");
                } else if ("defaultBidReduce".equals(performOperationJson.getRuleAction())) {
                    dataBuilder.setAdjustType("defaultBid");
                    dataBuilder.setOperatorType("reduce");
                } else if ("editBidStrategy".equals(performOperationJson.getRuleAction())) {
                    dataBuilder.setAdjustType("bidStrategy");
                    dataBuilder.setOperatorType("edit");
                } else if ("editCampaignPlacement".equals(performOperationJson.getRuleAction())) {
                    dataBuilder.setAdjustType("campaignPlacement");
                    dataBuilder.setOperatorType("edit");
                }
                if ("fixed".equals(performOperationJson.getRuleAdjust())) {
                    dataBuilder.setActionType("fixed");
                } else if ("percentage".equals(performOperationJson.getRuleAdjust())) {
                    dataBuilder.setActionType("percentage");
                } else if ("inputValue".equals(performOperationJson.getRuleAdjust())) {
                    dataBuilder.setActionType("inputValue");
                } else if ("legacyForSales".equals(performOperationJson.getRuleAdjust())) {
                    dataBuilder.setActionType("legacyForSales");
                } else if ("autoForSales".equals(performOperationJson.getRuleAdjust())) {
                    dataBuilder.setActionType("autoForSales");
                } else if ("manual".equals(performOperationJson.getRuleAdjust())) {
                    dataBuilder.setActionType("manual");
                }
                builder.setDataDetail(dataBuilder.build());
            }

            if ("customRule".equals(e.getRuleType())) {
                AutoRuleExecuteRecordUtils.convertAutoRuleJson(builder,e);
                AutoRuleExecuteRecordUtils.convertDataDetailJson(builder,e);
            }
            if (StringUtils.isBlank(e.getSetRelation())) {
                builder.setSetRelation("all");
            } else {
                builder.setSetRelation(e.getSetRelation());
            }
            builder.setTemplateId(e.getTemplateId());
            builder.setTaskId(e.getTaskId());
            builder.setTemplateName(e.getTemplateName());
            builder.setItemName(e.getItemName());
            builder.setItemId(e.getItemId());
            builder.setItemType(e.getItemType());
            if (StringUtils.isNotBlank(e.getItemOperateId())) {
                builder.setItemOperateId(e.getItemOperateId());
            }
            if (StringUtils.isNotBlank(e.getItemOperateName())) {
                builder.setItemOperateName(e.getItemOperateName());
            }
            if (StringUtils.isNotBlank(e.getItemOperateType())) {
                builder.setItemOperateType(e.getItemOperateType());
            }
            builder.setRuleType(e.getRuleType());
            builder.setProfileId(e.getProfileId());
            builder.setExecuteType(e.getExecuteType());
            if (StringUtils.isNotBlank(e.getStateErrMsg())) {
                builder.setErrMsg(e.getStateErrMsg());
            }
            builder.setCreateAt(e.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            builder.setLastUpdateAt(e.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            if (AutoRuleRecordStatus.unconfirmedTab.equals(request.getCodes())) {
                builder.setExecuteAt(e.getTriggerAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                builder.setExecuteSiteAt(LocalDateTimeUtil.convertChinaToSiteTime(e.getTriggerAt(), e.getMarketplaceId()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            } else if (AutoRuleRecordStatus.allTab.equals(request.getCodes())) {
                if (e.getExecuteAt() != null) {
                    builder.setExecuteAt(e.getExecuteAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    builder.setExecuteSiteAt(LocalDateTimeUtil.convertChinaToSiteTime(e.getExecuteAt(), e.getMarketplaceId()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                } else {
                    builder.setExecuteAt(e.getTriggerAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    builder.setExecuteSiteAt(LocalDateTimeUtil.convertChinaToSiteTime(e.getTriggerAt(), e.getMarketplaceId()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                }
            }
            list.add(builder);
        });

        //广告组名称，这里只有搜索词，只需要查sp和sb即可
        Map<String, String> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(adGroupIdSet)) {
            //查询广告组
            List<AmazonAdGroup> spGroupList = amazonAdGroupDao.listByGroupIdsAndShopIdList(request.getPuid(), new ArrayList<>(shopIdSet), new ArrayList<>(adGroupIdSet));
            List<AmazonSbAdGroup> sbGroupList = amazonSbAdGroupDao.getListByShopIdsAndGroupIds(request.getPuid(), new ArrayList<>(shopIdSet), new ArrayList<>(adGroupIdSet));
            spGroupList.forEach(x -> map.put(x.getAdGroupId(), x.getName()));
            sbGroupList.forEach(x -> map.put(x.getAdGroupId(), x.getName()));
        }

        //设置广告组
        list.forEach(e -> {
            List<PerformOperation.Builder> builderList = performOperationBuilderMap.get(e.getId());
            if (CollectionUtils.isNotEmpty(builderList)) {
                List<PerformOperation> collect = builderList.stream().map(x -> {
                    if (StringUtils.isNotBlank(x.getAdGroupId()) && StringUtils.isBlank(x.getAdGroupName())) {
                        String name = map.get(x.getAdGroupId());
                        if (StringUtils.isNotBlank(name)) {
                            x.setAdGroupName(name);
                        }
                    }
                    return x.build();
                }).collect(Collectors.toList());
                e.addAllPerformOperationList(collect);
            }
        });

        pageBuilder.addAllRows(list.stream().map(AutoRuleExecuteRecordRpc.Builder::build).collect(Collectors.toList()));
        responseBuilder.setData(pageBuilder);

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void submitExecuteRecord(SubmitExecuteRecordRequest request, StreamObserver<SubmitExecuteRecordResponse> responseObserver) {
        log.info("自动化规则执行记录, 人工确认操作 pageList request:{}", request);
        SubmitExecuteRecordResponse.Builder responseBuilder = SubmitExecuteRecordResponse.newBuilder();
        List<SubmitExecuteRecordParam> params = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(request.getSubmitExecuteRecordRpcsList())) {
            request.getSubmitExecuteRecordRpcsList().forEach(e -> {
                SubmitExecuteRecordParam param = new SubmitExecuteRecordParam();
                param.setRecordId(e.getRecordId());
                param.setItemType(e.getItemType());
                params.add(param);
            });
        }

        //立即执行
        Result<List<AddAutoRuleExecuteRecordVo>> result = advertiseAutoRuleExecuteRecordService.submitExecuteRecord(request.getPuid(), params, request.getCode(), request.getTraceId());
        responseBuilder.setCode(result.getCode());
        if (StringUtils.isNotBlank(result.getMsg())) {
            responseBuilder.setMsg(result.getMsg());
        }

        if (CollectionUtils.isNotEmpty(result.getData())) {
            ExecuteRecordAddResponse.Builder builder = ExecuteRecordAddResponse.newBuilder();
            if (params.size() == 1) {
                responseBuilder.setCode(-1);
                responseBuilder.setMsg(result.getData().get(0).getMsg());
            }
            builder.setErrorMsg(result.getMsg());
            result.getData().forEach(e -> {
                ItemMsg.Builder newBuilder = ItemMsg.newBuilder();
                newBuilder.setItemId(e.getItemId());
                newBuilder.setItemName(e.getItemName());
                newBuilder.setItemMsg(e.getMsg());
                builder.addItemMsgList(newBuilder.build());
            });
            responseBuilder.setData(builder.build());
        }

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void handleExecuteRecordMessage(HandleExecuteRecordMessageRequestPb.HandleExecuteRecordMessageRequest request, StreamObserver<HandleExecuteRecordMessageResponsePb.HandleExecuteRecordMessageResponse> responseObserver) {
        log.info("查询当前需要推送自动规则记录 handleExecuteRecordMessage request:{}", request);
        HandleExecuteRecordMessageResponsePb.HandleExecuteRecordMessageResponse.Builder responseBuilder = HandleExecuteRecordMessageResponsePb.HandleExecuteRecordMessageResponse.newBuilder();
        Result<List<ExecuteRecordMessageVo>> result = advertiseAutoRuleExecuteRecordService.queryPushMessage(request.getIndex(), request.getTotal());
        if (result.getCode() == Result.SUCCESS) {
            List<HandleExecuteRecordMessageResponsePb.HandleExecuteRecordMessageResponse.ExecuteRecordMessage> list = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(result.getData())) {
                result.getData().forEach(e -> {
                    HandleExecuteRecordMessageResponsePb.HandleExecuteRecordMessageResponse.ExecuteRecordMessage.Builder builder =
                            HandleExecuteRecordMessageResponsePb.HandleExecuteRecordMessageResponse.ExecuteRecordMessage.newBuilder();
                    builder.setPuid(e.getPuid());
                    builder.setShopId(e.getShopId());
                    builder.setItemType(e.getItemType());
                    builder.setPushMessageType(e.getPushMessageType());
                    builder.setRunTime(e.getRunTime());
                    builder.setTemplateId(e.getTemplateId());
                    builder.setTemplateName(e.getTemplateName());
                    builder.setExecuteRecordCount(e.getExecuteRecordCount());
                    if (e.getCreateId() != null) {
                        builder.setCreateId(e.getCreateId());
                    }
                    builder.setExecuteType(e.getExecuteType());
                    list.add(builder.build());
                });
            }
            responseBuilder.addAllData(list);
        }
        responseBuilder.setCode(result.getCode());
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateExecuteRecordMessageState(UpdateExecuteRecordMessageStateRequestPb.UpdateExecuteRecordMessageStateRequest request, StreamObserver<UpdateExecuteRecordMessageStateResponsePb.UpdateExecuteRecordMessageStateResponse> responseObserver) {
        log.info("修改执行记录消息推送状态 updateExecuteRecordMessageState request:{}", request);
        UpdateExecuteRecordMessageStateResponsePb.UpdateExecuteRecordMessageStateResponse.Builder responseBuilder = UpdateExecuteRecordMessageStateResponsePb.UpdateExecuteRecordMessageStateResponse.newBuilder();
        if (!request.hasTemplateId() || StringUtils.isBlank(request.getPushState())|| !request.hasPuid()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            Result<String> result = advertiseAutoRuleExecuteRecordService.updatePushMessage(request.getPuid().getValue(),request.getTemplateId().getValue(),request.getPushState(),request.getPushMessageType());
            responseBuilder.setCode(result.getCode());
            responseBuilder.setMsg(result.getMsg());
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     * 计算codes，因前端处理复杂，所以拆成3个字段codes、state、showCanRecord，这里统一处理成codes
     * @param request
     * @return
     */
    private List<String> computeCode(AutoRuleExecuteRecordPageRequest request) {
        //codes
        Set<String> codeSet = new HashSet<>();
        //state
        Set<String> stateSet = new HashSet<>();
        //showCanRecord
        Set<String> showCanRecordSet = new HashSet<>();

        //前端传值，为了兼容历史情况
        //全部和待确认操作tab切换使用codes字段：选择全部tab：传SUCCESS,FAILURE，选择待确认操作tab：传IN_PROGRESS,UNCONFIRMED,IGNORE
        //操作状态使用state字段：全部tab下的操作状态下拉：成功SUCCESS，失败FAILURE，待确认操作tab下的操作状态下拉：执行中IN_PROGRESS，待确认UNCONFIRMED，已忽略IGNORE
        //展示可操作记录：勾选true，不勾选false，即可操作只查UNCONFIRMED的数据

        //如果是待确认
        if (AutoRuleRecordStatus.unconfirmedTab.equals(request.getCodes())) {
            //codeSet加入IN_PROGRESS,UNCONFIRMED,IGNORE
            codeSet.addAll(AutoRuleRecordStatus.unconfirmedSet);
            //用户没有选择操作状态
            if (StringUtils.isBlank(request.getState()) || !AutoRuleRecordStatus.unconfirmedSet.contains(request.getState())) {
                //stateSet加入IN_PROGRESS,UNCONFIRMED,IGNORE
                stateSet.addAll(AutoRuleRecordStatus.unconfirmedSet);
            } else {
                //stateSet只加入用户选择的
                stateSet.add(request.getState());
            }
            //用户勾选展示可操作记录
            if (request.getShowCanRecord()) {
                //showCanRecordSet只加入待确认的
                showCanRecordSet.add(AutoRuleRecordStatus.UNCONFIRMED.getStatus());
            } else {
                //showCanRecordSet加入IN_PROGRESS,UNCONFIRMED,IGNORE
                showCanRecordSet.addAll(AutoRuleRecordStatus.unconfirmedSet);
            }
        } else {
            //如果是全部，codeSet加入SUCCESS, FAILURE
            codeSet.addAll(AutoRuleRecordStatus.allSet);
            //showCanRecordSet没用，所以也加入SUCCESS, FAILURE
            showCanRecordSet.addAll(AutoRuleRecordStatus.allSet);
            //用户没有选择操作状态
            if (StringUtils.isBlank(request.getState()) || !AutoRuleRecordStatus.allSet.contains(request.getState())) {
                //stateSet加入SUCCESS, FAILURE
                stateSet.addAll(AutoRuleRecordStatus.allSet);
            } else {
                //stateSet只加入用户选择的
                stateSet.add(request.getState());
            }
        }

        //三个集合求交集即可
        codeSet.retainAll(stateSet);
        codeSet.retainAll(showCanRecordSet);

        return new ArrayList<>(codeSet);
    }

    private void buildBlankResp(int toBeConfirmedCount, int pageNo, int pageSize,
                                AutoRuleExecuteRecordPageResponse.Builder responseBuilder,
                                AutoRuleExecuteRecordPage.Builder pageBuilder) {
        pageBuilder.setPageNo(pageNo);
        pageBuilder.setPageSize(pageSize);
        pageBuilder.setTotalPage(0);
        pageBuilder.setTotalSize(0);
        pageBuilder.setToBeConfirmedCount(toBeConfirmedCount);
        responseBuilder.setCode(Result.SUCCESS);
        responseBuilder.setData(pageBuilder);
    }

    @Override
    public void getGrabRankingSnapshot(GetGrabRankingSnapshotRequest request, StreamObserver<GetGrabRankingSnapshotResponse> responseObserver) {
        GetGrabRankingSnapshotResponse.Builder responseBuilder = GetGrabRankingSnapshotResponse.newBuilder();
        if (!request.hasPuid() || !request.hasRecordId() || !request.hasShopId()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            GetGrabRankingSnapshotData grabRankingSnapshot = advertiseAutoRuleExecuteRecordService.getGrabRankingSnapshot(request.getPuid(), request.getShopId(), request.getRecordId());
            responseBuilder.setCode(Result.SUCCESS);
            if (grabRankingSnapshot != null) {
                responseBuilder.setData(grabRankingSnapshot);
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }



}
