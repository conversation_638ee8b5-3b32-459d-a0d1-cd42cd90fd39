package com.meiyunji.sponsored.api.sb;

import com.amazon.advertising.sb.entity.brands.BrandResult;
import com.amazon.advertising.sb.entity.storeInfo.StoresResult;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.rpc.sb.store.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dto.SbGroupInfoDto;
import com.meiyunji.sponsored.service.cpc.manager.SbCreativeManager;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbStoreService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbStoreServiceImpl;
import com.meiyunji.sponsored.service.cpc.vo.SbStoreInfoVo;
import com.meiyunji.sponsored.service.cpc.vo.ThemesVo;
import com.meiyunji.sponsored.service.cpc.vo.assets.ImageAssetVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@GRpcService
@Slf4j
public class SbStoreRpcService extends RPCCpcSbStoreServiceGrpc.RPCCpcSbStoreServiceImplBase {

    @Autowired
    private CpcSbStoreServiceImpl cpcSbStoreService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private CpcSbStoreService sbStoreService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private SbCreativeManager sbCreativeManager;


    @Override
    public void getStoreInfo(StoreInfoRequest request, StreamObserver<StoreInfoResponse> responseObserver) {
        StoreInfoResponse.Builder builder = StoreInfoResponse.newBuilder();
        if (!request.hasShopId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            Result<SbStoreInfoVo> result = cpcSbStoreService.getStoreInfo(request.getPuid().getValue(), request.getShopId().getValue());
            SbStoreInfoVo data = result.getData();
            if (data != null) {
                List<BrandResult> brandList = data.getBrandList();
                if (CollectionUtils.isNotEmpty(brandList)) {
                    List<com.meiyunji.sponsored.rpc.sb.store.BrandResult> cpcList = new ArrayList<>(brandList.size());
                    for (BrandResult vo : brandList) {
                        com.meiyunji.sponsored.rpc.sb.store.BrandResult.Builder builder2 = com.meiyunji.sponsored.rpc.sb.store.BrandResult.newBuilder();
                        if (StringUtils.isNotBlank(vo.getBrandId())) {
                            builder2.setBrandId(vo.getBrandId());
                        }
                        if (StringUtils.isNotBlank(vo.getBrandEntityId())) {
                            builder2.setBrandEntityId(vo.getBrandEntityId());
                        }
                        if (StringUtils.isNotBlank(vo.getBrandRegistryName())) {
                            builder2.setBrandRegistryName(vo.getBrandRegistryName());
                        }
                        cpcList.add(builder2.build());
                    }
                    builder.addAllBrandList(cpcList);
                }

                Map<String, StoresResult> storesInfoMap = data.getStoresInfoMap();
                if (storesInfoMap != null) {
                    Map<String, com.meiyunji.sponsored.rpc.sb.store.StoresResult> map = new LinkedHashMap<>();
                    for (Map.Entry<String, StoresResult> entry : storesInfoMap.entrySet()) {
                        com.meiyunji.sponsored.rpc.sb.store.StoresResult.Builder builder3 = com.meiyunji.sponsored.rpc.sb.store.StoresResult.newBuilder();
                        String key = entry.getKey();
                        StoresResult storeResult = entry.getValue();
                        if (StringUtils.isNotBlank(storeResult.getBrandEntityId())) {
                            builder3.setEntityId(storeResult.getEntityId());
                        }
                        if (StringUtils.isNotBlank(storeResult.getStoreName())) {
                            builder3.setStoreName(storeResult.getStoreName());
                        }
                        if (StringUtils.isNotBlank(storeResult.getBrandEntityId())) {
                            builder3.setBrandEntityId(storeResult.getBrandEntityId());
                        }

                        List<com.amazon.advertising.sb.entity.storeInfo.StorePageInfo> pageInfoList = storeResult.getStorePageInfo();
                        if (CollectionUtils.isNotEmpty(pageInfoList)) {
                            List<StorePageInfo> cpcPageInfoList = new ArrayList<>(pageInfoList.size());
                            for (com.amazon.advertising.sb.entity.storeInfo.StorePageInfo info : pageInfoList) {
                                StorePageInfo.Builder builder4 = StorePageInfo.newBuilder();
                                if (StringUtils.isNotBlank(info.getStorePageId())) {
                                    builder4.setStorePageId(info.getStorePageId());
                                }
                                if (StringUtils.isNotBlank(info.getStorePageUrl())) {
                                    builder4.setStorePageUrl(info.getStorePageUrl());
                                }
                                if (StringUtils.isNotBlank(info.getStorePageName())) {
                                    builder4.setStorePageName(info.getStorePageName());
                                }

                                cpcPageInfoList.add(builder4.build());
                            }
                            builder3.addAllStorePageInfo(cpcPageInfoList);
                        }

                        map.put(key, builder3.build());
                    }
                    builder.putAllStoresInfoMap(map);
                }
            }

            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }

    }

    @Override
    public void getBrand(BrandRequest request, StreamObserver<BrandResponse> responseObserver) {
        BrandResponse.Builder builder = BrandResponse.newBuilder();
        if (!request.hasShopId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }  else {
            Result<List<BrandResult>> result = cpcSbStoreService.getBrand(request.getPuid().getValue(), request.getShopId().getValue());
            List<BrandResult> data = result.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                List<com.meiyunji.sponsored.rpc.sb.store.BrandResult> cpcList = new ArrayList<>(data.size());
                for (BrandResult vo : data) {
                    com.meiyunji.sponsored.rpc.sb.store.BrandResult.Builder builder2 = com.meiyunji.sponsored.rpc.sb.store.BrandResult.newBuilder();
                    if (StringUtils.isNotBlank(vo.getBrandId())) {
                        builder2.setBrandId(vo.getBrandId());
                    }
                    if (StringUtils.isNotBlank(vo.getBrandEntityId())) {
                        builder2.setBrandEntityId(vo.getBrandEntityId());
                    }
                    if (StringUtils.isNotBlank(vo.getBrandRegistryName())) {
                        builder2.setBrandRegistryName(vo.getBrandRegistryName());
                    }
                    cpcList.add(builder2.build());
                }
                builder.addAllData(cpcList);
            }

            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getStoreImage(StoreImageRequest request, StreamObserver<StoreImageResponse> responseObserver) {
        StoreImageResponse.Builder builder = StoreImageResponse.newBuilder();
        if (!request.hasShopId() || StringUtils.isBlank(request.getBrandEntityId())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {

            Result<List<ImageAssetVo>> result = cpcSbStoreService.getStoreImage(request.getPuid().getValue(),
                    request.getShopId().getValue(), request.getBrandEntityId(), request.getFilterType());

            List<ImageAssetVo> data = result.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                List<com.meiyunji.sponsored.rpc.sb.store.StoreAssetResult> rpcList = new ArrayList<>(data.size());
                for (ImageAssetVo vo : data) {
                    com.meiyunji.sponsored.rpc.sb.store.StoreAssetResult.Builder builder2 = com.meiyunji.sponsored.rpc.sb.store.StoreAssetResult.newBuilder();
                    if (vo.getAssetId() != null) {
                        builder2.setAssetId(vo.getAssetId());
                    }
                    if (StringUtils.isNotEmpty(vo.getVersionId())) {
                        builder2.setAssetId(builder2.getAssetId().concat(":").concat(vo.getVersionId()));
                    }
                    if (vo.getUrl() != null) {
                        builder2.setUrl(vo.getUrl());
                    }
                    if (vo.getMediaType() != null) {
                        builder2.setMediaType(vo.getMediaType());
                    }
                    if (vo.getName() != null) {
                        builder2.setName(vo.getName());
                    }
                    if (vo.getHeight() != null) {
                        builder2.setHeight(Int32Value.of(vo.getHeight()));
                    }
                    if (vo.getWidth() != null) {
                        builder2.setWidth(Int32Value.of(vo.getWidth()));
                    }
                    rpcList.add(builder2.build());
                }
                builder.addAllData(rpcList);
            }

            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getCreativeInfo(GetCreativeInfoRequest request, StreamObserver<GetCreativeInfoResponse> responseObserver) {
        GetCreativeInfoResponse.Builder builder = GetCreativeInfoResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasAdId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        int puid = request.getPuid();
        int shopId = request.getShopId();

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("没有CPC授权");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("没有站点对应的配置信息");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        Result<CreativeInfoResult> result = sbCreativeManager.getAdCreativeInfo(shop, profile, request.getAdId());

        builder.setCode(Int32Value.of(result.getCode()));
        builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
        builder.setData(result.getData() == null ? CreativeInfoResult.newBuilder().build() : result.getData());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getSbGroupInfo(GetSbGroupInfoRequest request, StreamObserver<GetSbGroupInfoResponse> responseObserver) {
        log.info("getSbGroupInfo, req:{}", request);
        GetSbGroupInfoResponse.Builder builder = GetSbGroupInfoResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasCampaignId() || !request.hasGroupId()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        int puid = request.getPuid();
        int shopId = request.getShopId();

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            builder.setCode(Result.ERROR);
            builder.setMsg("没有CPC授权");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            builder.setCode(Result.ERROR);
            builder.setMsg("没有站点对应的配置信息");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        Result<SbGroupInfoDto> result = cpcSbStoreService.getSbGroupInfo(shop, profile, request.getCampaignId(), request.getGroupId());
        SbGroupInfoDto data = result.getData();
        if (Objects.nonNull(data)) {
            GetSbGroupInfo.Builder getSbGroupInfoBuilder = GetSbGroupInfo.newBuilder();
            if (Objects.nonNull(data.getAdGroupName())) {
                getSbGroupInfoBuilder.setAdGroupName(data.getAdGroupName());
            }
            if (Objects.nonNull(data.getAdGroupName())) {
                getSbGroupInfoBuilder.setCampaignId(data.getCampaignId());
            }
            if (Objects.nonNull(data.getCampaignDailyBudget())) {
                getSbGroupInfoBuilder.setCampaignDailyBudget(data.getCampaignDailyBudget());
            }
            if (Objects.nonNull(data.getAdFormat())) {
                getSbGroupInfoBuilder.setAdFormat(data.getAdFormat());
            }
            if (Objects.nonNull(data.getSbAdGroupType())) {
                getSbGroupInfoBuilder.setSbAdGroupType(data.getSbAdGroupType());
            }
            if (Objects.nonNull(data.getSbvLandingPageType())) {
                getSbGroupInfoBuilder.setSbvLandingPageType(data.getSbvLandingPageType());
            }
            if (CollectionUtils.isNotEmpty(data.getThemesVo())) {
                List<Themes> themesList = new ArrayList<>();
                for (ThemesVo themesVo: data.getThemesVo()) {
                    if (Objects.nonNull(themesVo)) {
                        Themes.Builder themes = Themes.newBuilder();
                        if (Objects.nonNull(themesVo.getThemeType())) {
                            themes.setThemeType(themesVo.getThemeType());
                        }
                        if (Objects.nonNull(themesVo.getBid())) {
                            themes.setBid(themesVo.getBid());
                        }
                        themesList.add(themes.build());
                    }
                }
                getSbGroupInfoBuilder.addAllThemesVo(themesList);
            }
            builder.setData(getSbGroupInfoBuilder.build());
        }

        builder.setCode(result.getCode());
        builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 查询品牌旗舰店店铺信息，所有店铺-所有子页面-子页面下第一个ASIN
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void queryBrandStoreList(QueryBrandStoreListRequest request, StreamObserver<BrandStoresListResponse> responseObserver) {
        log.info("queryBrandStoreList, req:{}", request);
        BrandStoresListResponse.Builder respBuilder = BrandStoresListResponse.newBuilder();

        //校验权限
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId(), request.getPuid());
        if (shop == null) {
            respBuilder.setCode(Result.ERROR);
            respBuilder.setMsg("没有CPC授权");
            responseObserver.onNext(respBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(request.getPuid(), request.getShopId());
        if (profile == null) {
            respBuilder.setCode(Result.ERROR);
            respBuilder.setMsg("没有站点对应的配置信息");
            responseObserver.onNext(respBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        Result<List<com.meiyunji.sponsored.rpc.sb.store.StoresResult>> listResult = sbStoreService.queryBrandStoreList(shop, profile, request);

        if (listResult.error()) {
            respBuilder.setCode(Result.ERROR);
            respBuilder.setMsg(listResult.getMsg());
            responseObserver.onNext(respBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        respBuilder.setCode(Result.SUCCESS);
        respBuilder.addAllData(listResult.getData());
        responseObserver.onNext(respBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     * 通过店铺子页面url分页查询asin列表，asin基础信息从t_product表查询，合格状态调用亚马逊接口获取
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAsinsByPages(GetAsinInfoByPageReq request, StreamObserver<GetAsinInfoByPageResp> responseObserver) {
        log.info("getAsinsByPages req:{}", request);
        GetAsinInfoByPageResp.Builder respBuilder = GetAsinInfoByPageResp.newBuilder();

        //校验权限
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId(), request.getPuid());
        if (shop == null) {
            respBuilder.setCode(Result.ERROR);
            respBuilder.setMsg("没有CPC授权");
            responseObserver.onNext(respBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(request.getPuid(), request.getShopId());
        if (profile == null) {
            respBuilder.setCode(Result.ERROR);
            respBuilder.setMsg("没有站点对应的配置信息");
            responseObserver.onNext(respBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        Result<PageSbAsinInfos> listResult = sbStoreService.getAsinsByPages(shop, profile, request);

        if (listResult.error()) {
            respBuilder.setCode(Result.ERROR);
            respBuilder.setMsg(listResult.getMsg());
            responseObserver.onNext(respBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        respBuilder.setCode(Result.SUCCESS);
        respBuilder.setData(listResult.getData());
        responseObserver.onNext(respBuilder.build());
        responseObserver.onCompleted();

    }





}
