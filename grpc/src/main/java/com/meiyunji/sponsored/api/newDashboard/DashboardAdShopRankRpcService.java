package com.meiyunji.sponsored.api.newDashboard;

import com.google.common.collect.Range;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.rpc.newDashboard.*;
import com.meiyunji.sponsored.rpc.newDashboard.vo.UrlResponse;
import com.meiyunji.sponsored.service.newDashboard.enums.*;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdShopRankService;
import com.meiyunji.sponsored.service.newDashboard.util.DateTimeUtil;
import com.meiyunji.sponsored.service.newDashboard.util.YoyDateUtil;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardShopRankReqVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-04-22  16:20
 */
@GRpcService
@Slf4j
public class DashboardAdShopRankRpcService extends RpcDashboardAdShopRankServiceGrpc.RpcDashboardAdShopRankServiceImplBase{

    private static final Range<Integer> LIMIT_RANGE = Range.closed(10, 50);

    @Autowired
    private IDashboardAdShopRankService dashboardAdShopRankServiceImpl;

    @Override
    public void queryAdShopRank(DashboardAdShopRankRequest request, StreamObserver<DashboardAdShopRankResponse> responseObserver) {
        log.info("dashboard query shop rank, request data: {}", request);
        StopWatch sw = new StopWatch();
        sw.start();
        DashboardAdShopRankResponse.Builder builder = DashboardAdShopRankResponse.newBuilder();
        DashboardShopRankReqVo reqVo = processParam(request, false);
        if (Objects.isNull(reqVo)) {
            log.error("dashboard query shop rank, check param error");
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
            return;
        } else {
            DashboardAdShopRankResponseVo vo = dashboardAdShopRankServiceImpl.queryShopRank(reqVo);
            builder.setData(vo);
            builder.setCode(Result.SUCCESS);

            sw.stop();
            log.info("dashboard query shop rank, puid: {}, 耗时: {}秒", request.getPuid(), sw.getTotalTimeSeconds());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void exportAdShopRank(DashboardAdShopRankRequest request, StreamObserver<UrlResponse> responseObserver) {
        //根据queryField调用不同层级的service
        log.info("dashboard export shop rank, request data: {}", request);
        StopWatch sw = new StopWatch();
        sw.start();
        UrlResponse.Builder builder = UrlResponse.newBuilder();
        try {
            DashboardShopRankReqVo reqVo = processParam(request, true);
            if (Objects.isNull(reqVo)) {
                log.error("dashboard export shop rank, check param error");
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            } else {
                List<String> urlList = dashboardAdShopRankServiceImpl.exportShopRank(reqVo);
                if (CollectionUtils.isNotEmpty(urlList)) {
                    builder.addAllUrls(urlList);
                    builder.setCode(Int32Value.of(Result.SUCCESS));
                } else {
                    builder.setCode(Int32Value.of(Result.ERROR));
                    builder.setMsg("dashboard export shop rank fail");
                }
                sw.stop();
                log.info("dashboard export shop rank, puid: {}, 耗时: {}秒", request.getPuid(), sw.getTotalTimeSeconds());
            }
        } catch (IllegalStateException e) {
            builder.setCode(Int32Value.of(Result.ERROR));
            log.error("dashboard export shop rank, puid: {}", request.getPuid(), e);
            builder.setMsg("dashboard export shop rank fail");
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private DashboardShopRankReqVo processParam(DashboardAdShopRankRequest req, boolean export) {
        //校验值是否合法
        if (!DashboardCurrencyEnum.currencySet.contains(req.getCurrency())) {
            log.error("dashboard query shop rank, req param error, currency is null:{}", req.getCurrency());
            return null;
        }
        if (!export && !LIMIT_RANGE.contains(req.getLimit())) {
            log.error("dashboard query shop rank, req param error, limit is null:{}", req.getLimit());
            return null;
        }
        if (!DashboardOrderByRateEnum.rateMap.containsKey(req.getOrderByField())) {
            log.error("dashboard query shop rank, req param error, orderField is null:{}", req.getOrderByField());
            return null;
        }
        if (!DashboardOrderByEnum.orderByMap.containsKey(req.getOrderBy())) {
            log.error("dashboard query shop rank, req param error, orderBy is null:{}", req.getOrderBy());
            return null;
        }
        if (Objects.isNull(DashboardRankQueryFieldEnum.getDashboardRankQueryFieldEnumByCode(req.getQueryField()))) {
            log.error("dashboard query shop rank, req param error, queryField is null:{}", req.getQueryField());
            return null;
        }
        //同比期
        List<String> yoyDate = YoyDateUtil.getYoyDate(req.getStartDate(), req.getEndDate());
        DashboardShopRankReqVo vo = DashboardShopRankReqVo.builder()
                .percent(req.getPercent())
                .yoy(req.getYoy())
                .mom(req.getMom())
                .queryField(req.getQueryField())
                .limit(req.getLimit()).build();
        if (CollectionUtils.isEmpty(yoyDate)) {
            // 开始时间故意写得比结束时间晚,令后续查出来的同比数据为空
            vo.setYoyOverLimit(true);
            vo.setYoyStartDate(DateUtil.toFormatDate(req.getStartDate()));
            vo.setYoyEndDate(DateUtil.dateToStrWithTime(DateUtil.addDay(DateUtil.strToDate(req.getStartDate(), DateUtil.PATTERN_YYYYMMDD), -1), DateUtil.PATTERN));
        } else {
//            vo.setYoyStartDate(yoyDate.get(0));
//            vo.setYoyEndDate(yoyDate.get(1));
            Optional.ofNullable(yoyDate.get(0)).map(DateUtil::toFormatDate).ifPresent(vo::setYoyStartDate);
            Optional.ofNullable(yoyDate.get(1)).map(DateUtil::toFormatDate).ifPresent(vo::setYoyEndDate);
        }
        BeanUtils.copyProperties(req, vo, ParamCopyUtil.checkPropertiesNullOrEmptySuper(req));
        Optional.of(req.getStartDate()).map(DateUtil::toFormatDate).ifPresent(vo::setStartDate);
        Optional.of(req.getEndDate()).map(DateUtil::toFormatDate).ifPresent(vo::setEndDate);
        Optional.of(req.getMomStartDate()).map(DateUtil::toFormatDate).ifPresent(vo::setMomStartDate);
        Optional.of(req.getMomEndDate()).map(DateUtil::toFormatDate).ifPresent(vo::setMomEndDate);
        Optional.of(req.getMarketplaceIdList()).filter(CollectionUtils::isNotEmpty).ifPresent(vo::setMarketplaceIdList);
        Optional.of(req.getShopIdList()).filter(CollectionUtils::isNotEmpty).ifPresent(vo::setShopIdList);
        DateTimeUtil.resetTimeRange(vo, DashboardModuleEnum.SHOP_RANK);
        Optional.of(req.getPortfolioIdsList()).filter(CollectionUtils::isNotEmpty).ifPresent(vo::setPortfolioIds);
        Optional.of(req.getCampaignIdsList()).filter(CollectionUtils::isNotEmpty).ifPresent(vo::setCampaignIds);
        return vo;
    }
}
