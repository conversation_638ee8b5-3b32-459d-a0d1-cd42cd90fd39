package com.meiyunji.sponsored.api.common;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.commonMultiShop.*;
import com.meiyunji.sponsored.rpc.commonMultiShop.ErrorMsgVo;
import com.meiyunji.sponsored.rpc.vo.CommonResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.batchCreate.enums.AdStructureLevelEnum;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.SyncBasicDto;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdPortfolioService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcProductService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.service2.ICpcCampaignService;
import com.meiyunji.sponsored.service.cpc.service2.ICpcPortfolioService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdGroupService;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdPortfolio;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdProduct;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonSbAds;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.enums.AllAdStateEnum;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.multiple.common.service.IMultipleCommonService;
import com.meiyunji.sponsored.service.multiple.common.vo.ChangeRateParam;
import com.meiyunji.sponsored.service.multiple.common.vo.ChangeRateVo;
import com.meiyunji.sponsored.service.util.PbUtil;
import com.meiyunji.sponsored.util.ProtoBufUtil;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2024-05-14  16:05
 */

@GRpcService
@Slf4j
public class AdCommonMultiShopRpcService extends RPCAdCommonMultiShopServiceGrpc.RPCAdCommonMultiShopServiceImplBase {

    @Autowired
    private ICpcPortfolioService cpcPortfolioService;

    @Autowired
    private ICpcCampaignService cpcCampaignService;

    @Autowired
    private ICpcAdGroupService cpcAdGroupService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdCampaignAllDorisDao amazonAdCampaignAllDorisDao;
    @Autowired
    private ICpcProductService cpcProductService;
    @Autowired
    private IAmazonAdGroupDao spGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao sbGroupDao;
    @Autowired
    private IOdsProductDao odsProductDao;
    @Resource
    private IMultipleCommonService multipleCommonService;
    @Resource
    private IAmazonAdPortfolioService amazonAdPortfolioService;


    private final List<String> stateList = Arrays.asList(AllAdStateEnum.enabled.getStateType(),
            AllAdStateEnum.paused.getStateType(),
            AllAdStateEnum.archived.getStateType());

    private final List<String> adTypeList = Arrays.asList(CampaignTypeEnum.sp.getCampaignType(),
            CampaignTypeEnum.sb.getCampaignType(),
            CampaignTypeEnum.sd.getCampaignType());

    private final Set<String> marketplace_List = Arrays.stream(AmznEndpoint.values())
            .map(AmznEndpoint::getMarketplaceId)
            .collect(Collectors.toSet());


    @Override
    public void getPortfolioList(CommonMultiShopPortfolioRequest request, StreamObserver<CommonMultiShopPortfolioResponse> responseObserver) {
        log.info("跨店铺查询所有广告组合 request {}", request);
        CommonMultiShopPortfolioResponse.Builder builder = CommonMultiShopPortfolioResponse.newBuilder();
        MultiShopPortfolioListParam param = new MultiShopPortfolioListParam();
        // 店铺状态校验
        List<ShopAuth> shopAuthRes = shopAuthDao.listValidShopByIds(request.getPuid(), request.getShopIdList());
        if (CollectionUtils.isEmpty(shopAuthRes)) {
            throw new SponsoredBizException("店铺未授权");
        }
        param.setShopIdList(StreamUtil.toListDistinct(shopAuthRes, ShopAuth::getId));
        param.setPuid(request.getPuid());
        if (StringUtils.isNotBlank(request.getSearchValue())) {
            if (request.getSearchValue().contains("%±%")) {
                List<String> valueList = Arrays.stream(request.getSearchValue().split("%±%")).collect(Collectors.toList());
                param.setSearchValueList(valueList);
            } else {
                param.setSearchValue(request.getSearchValue());
            }
        }
        if (StringUtils.isNotBlank(request.getSearchType())) {
            param.setSearchType(request.getSearchType());
        }
        if (StringUtils.isNotBlank(request.getPosition())) {
            param.setPosition(request.getPosition());
        }
        if (CollectionUtils.isNotEmpty(request.getMarketplaceIdList())) {
            param.setMarketplaceId(request.getMarketplaceIdList());
        }

        if (CollectionUtils.isNotEmpty(request.getPortfolioIdList())) {
            param.setPortfolioIdList(request.getPortfolioIdList());
        }

        if (CollectionUtils.isNotEmpty(request.getMarketplaceIdList())) {
            List<String> collect = request.getMarketplaceIdList().stream().filter(marketplace_List::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                param.setMarketplaceId(collect);
            }
        }

        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());

        CommonMultiShopPortfolioPage.Builder pageBuilder = CommonMultiShopPortfolioPage.newBuilder();
        pageBuilder.setPageNo(param.getPageNo());
        pageBuilder.setPageSize(param.getPageSize());
        if (request.hasIsProductRight() && request.getIsProductRight()) {
            Page<OdsAmazonAdPortfolio> page = cpcPortfolioService.getMultiShopPortfolioListDoris(param);
            pageBuilder.setTotalPage(page.getTotalPage());
            pageBuilder.setTotalSize(page.getTotalSize());
            if (CollectionUtils.isNotEmpty(page.getRows())) {
                List<Integer> shopList = page.getRows().stream().map(OdsAmazonAdPortfolio::getShopId).distinct().collect(Collectors.toList());
                Map<Integer, ShopAuth> shopAuths = shopAuthDao.listAllByIds(param.getPuid(), shopList).stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));

                List<CommonMultiShopPortfolio> rpcVos = page.getRows().stream().filter(Objects::nonNull).map(item -> {
                    CommonMultiShopPortfolio.Builder voBuilder = CommonMultiShopPortfolio.newBuilder();
                    voBuilder.setShopId(item.getShopId());
                    voBuilder.setPortfolioId(item.getPortfolioId());
                    voBuilder.setPortfolioName(item.getName());
                    voBuilder.setState(item.getState());
                    voBuilder.setIsHidden(item.getIsHidden());
                    ShopAuth shopAuth = shopAuths.get(item.getShopId());
                    if (shopAuth != null) {
                        voBuilder.setShopName(StringUtil.toStringSafe(shopAuth.getName()));
                        voBuilder.setSiteName(AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getMarketplaceCN());
                        voBuilder.setMarketplaceId(shopAuth.getMarketplaceId());
                    }
                    return voBuilder.build();
                }).collect(Collectors.toList());
                pageBuilder.addAllRows(rpcVos);
            }
        } else {
            Page<AmazonAdPortfolio> page = cpcPortfolioService.getMultiShopPortfolioList(param);

            pageBuilder.setTotalPage(page.getTotalPage());
            pageBuilder.setTotalSize(page.getTotalSize());
            if (CollectionUtils.isNotEmpty(page.getRows())) {
                List<Integer> shopList = page.getRows().stream().map(AmazonAdPortfolio::getShopId).distinct().collect(Collectors.toList());
                Map<Integer, ShopAuth> shopAuths = shopAuthDao.listAllByIds(param.getPuid(), shopList).stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));

                List<CommonMultiShopPortfolio> rpcVos = page.getRows().stream().filter(Objects::nonNull).map(item -> {
                    CommonMultiShopPortfolio.Builder voBuilder = CommonMultiShopPortfolio.newBuilder();
                    voBuilder.setShopId(item.getShopId());
                    voBuilder.setPortfolioId(item.getPortfolioId());
                    voBuilder.setPortfolioName(item.getName());
                    voBuilder.setState(item.getState());
                    voBuilder.setIsHidden(item.getIsHidden());
                    ShopAuth shopAuth = shopAuths.get(item.getShopId());
                    if (shopAuth != null) {
                        voBuilder.setShopName(StringUtil.toStringSafe(shopAuth.getName()));
                        voBuilder.setSiteName(AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getMarketplaceCN());
                        voBuilder.setMarketplaceId(shopAuth.getMarketplaceId());
                    }
                    return voBuilder.build();
                }).collect(Collectors.toList());
                pageBuilder.addAllRows(rpcVos);
            }
        }


        builder.setCode(Result.SUCCESS);
        builder.setMsg("");
        builder.setData(pageBuilder);

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getCampaignList(CommonMultiShopCampaignRequest request, StreamObserver<CommonMultiShopCampaignResponse> responseObserver) {
        log.info("跨店铺查询所有广告活动 request {}", request);
        CommonMultiShopCampaignResponse.Builder builder = CommonMultiShopCampaignResponse.newBuilder();
        MultiShopCampaignListParam param = new MultiShopCampaignListParam();
        param.setPuid(request.getPuid());
        param.setShopIdList(request.getShopIdList());
        if (CollectionUtils.isNotEmpty(request.getPortfolioIdList())) {
            param.setPortfolioIdList(request.getPortfolioIdList());
        }
        if (StringUtils.isNotBlank(request.getSearchValue())) {
            if (request.getSearchValue().contains("%±%")) {
                List<String> valueList = Arrays.stream(request.getSearchValue().split("%±%")).collect(Collectors.toList());
                param.setSearchValueList(valueList);
            } else {
                param.setSearchValue(request.getSearchValue());
            }
        }
        if (StringUtils.isNotBlank(request.getSearchType())) {
            param.setSearchType(request.getSearchType());
        }
        if (CollectionUtils.isNotEmpty(request.getStateList())) {
            List<String> collect = request.getStateList().stream().filter(x -> stateList.contains(x)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                param.setStateList(collect);
            }
        }
        if (CollectionUtils.isNotEmpty(request.getAdTypeList())) {
            List<String> collect = request.getAdTypeList().stream().filter(x -> adTypeList.contains(x)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                param.setAdTypeList(collect);
            }
        }
        if (CollectionUtils.isNotEmpty(request.getMarketplaceIdList())) {
            List<String> collect = request.getMarketplaceIdList().stream().filter(marketplace_List::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                param.setMarketplaceId(collect);
            }
        }

        if (CollectionUtils.isNotEmpty(request.getCampaignIdList())) {
            param.setCampaignIdList(request.getCampaignIdList());
        }
        if (StringUtils.isNotBlank(request.getAdTargetType())) {
            param.setAdTargetType(request.getAdTargetType());
        }
        param.setNeedOrder(Boolean.TRUE.equals(request.getNeedOrder()));
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        Page<AmazonAdCampaignAll> page;
        if(request.hasIsProductRight() && request.getIsProductRight()) {
            page = cpcCampaignService.getMultiShopCampaignListDoris(param);
        } else {
            page = cpcCampaignService.getMultiShopCampaignList(param);
        }

        CommonMultiShopCampaignPage.Builder pageBuilder = CommonMultiShopCampaignPage.newBuilder();
        pageBuilder.setPageNo(param.getPageNo());
        pageBuilder.setPageSize(param.getPageSize());
        pageBuilder.setTotalPage(page.getTotalPage());
        pageBuilder.setTotalSize(page.getTotalSize());
        if (CollectionUtils.isNotEmpty(page.getRows())) {
            List<Integer> shopList = page.getRows().stream().map(AmazonAdCampaignAll::getShopId).distinct().collect(Collectors.toList());
            Map<Integer, ShopAuth> shopAuths = shopAuthDao.listAllByIds(param.getPuid(), shopList).stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
            List<String> portfolioIds = page.getRows().stream().map(AmazonAdCampaignAll::getPortfolioId).filter(Objects::nonNull).collect(Collectors.toList());
            Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                portfolioMap = portfolioDao.listByShopId(request.getPuid(), shopList, portfolioIds).stream()
                        .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, Function.identity()));
            }

            Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;
            List<CommonMultiShopCampaign> rpcVos = page.getRows().stream().filter(Objects::nonNull).map(item -> {
                CommonMultiShopCampaign.Builder voBuilder = CommonMultiShopCampaign.newBuilder();
                voBuilder.setShopId(item.getShopId());
                voBuilder.setCampaignId(item.getCampaignId());
                voBuilder.setCampaignName(item.getName());
                voBuilder.setState(item.getState());
                voBuilder.setAdType(item.getType());

                ShopAuth shopAuth = shopAuths.get(item.getShopId());
                if (shopAuth != null) {
                    voBuilder.setShopName(StringUtil.toStringSafe(shopAuth.getName()));
                    voBuilder.setSiteName(AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getMarketplaceCN());
                    voBuilder.setMarketplaceId(shopAuth.getMarketplaceId());
                }

                if (StringUtils.isNotBlank(item.getPortfolioId()) && finalPortfolioMap.containsKey(item.getPortfolioId())) {
                    AmazonAdPortfolio amazonAdPortfolio = finalPortfolioMap.get(item.getPortfolioId());
                    voBuilder.setPortfolioName(StringUtil.toStringSafe(amazonAdPortfolio.getName()));
                    voBuilder.setPortfolioId(StringUtil.toStringSafe(amazonAdPortfolio.getPortfolioId()));
                } else {
                    voBuilder.setPortfolioName("-");
                    voBuilder.setPortfolioId("-");
                }
                return voBuilder.build();
            }).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }
        builder.setCode(Result.SUCCESS);
        builder.setMsg("");
        builder.setData(pageBuilder);

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getGroupList(CommonMultiShopGroupRequest request, StreamObserver<CommonMultiShopGroupResponse> responseObserver) {
        log.info("跨店铺查询所有广告组 request {}", request);
        CommonMultiShopGroupResponse.Builder builder = CommonMultiShopGroupResponse.newBuilder();
        MultiShopGroupListParam param = new MultiShopGroupListParam();
        param.setPuid(request.getPuid());
        param.setShopIdList(request.getShopIdList());
        if (CollectionUtils.isNotEmpty(request.getPortfolioIdList())) {
            param.setPortfolioIdList(request.getPortfolioIdList());
        }
        if (CollectionUtils.isNotEmpty(request.getCampaignIdList())) {
            param.setCampaignIdList(request.getCampaignIdList());
        }
        if (StringUtils.isNotBlank(request.getSearchValue())) {
            if (request.getSearchValue().contains("%±%")) {
                List<String> valueList = Arrays.stream(request.getSearchValue().split("%±%")).collect(Collectors.toList());
                param.setSearchValueList(valueList);
            } else {
                param.setSearchValue(request.getSearchValue());
            }

        }
        if (StringUtils.isNotBlank(request.getSearchType())) {
            param.setSearchType(request.getSearchType());
        }
        if (CollectionUtils.isNotEmpty(request.getStateList())) {
            List<String> collect = request.getStateList().stream().filter(x -> stateList.contains(x)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                param.setStateList(collect);
            }
        }
        if (CollectionUtils.isNotEmpty(request.getAdTypeList())) {
            List<String> collect = request.getAdTypeList().stream().filter(x -> adTypeList.contains(x)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                param.setAdTypeList(collect);
            }
        }

        if (CollectionUtils.isNotEmpty(request.getMarketplaceIdList())) {
            List<String> collect = request.getMarketplaceIdList().stream().filter(marketplace_List::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                param.setMarketplaceId(collect);
            }
        }

        if (CollectionUtils.isNotEmpty(request.getGroupIdList())) {
            param.setGroupIdList(request.getGroupIdList());
        }
        if(StringUtils.isNotBlank(request.getAdGroupType())){
            param.setAdGroupType(request.getAdGroupType());
        }

        if (StringUtils.isNotBlank(request.getGroupType())) {
            param.setGroupType(request.getGroupType());
        }

        param.setNeedOrder(Boolean.TRUE.equals(request.getNeedOrder()));
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        Page<MultiShopGroupListVo> page ;
        if(request.hasIsProductRight() && request.getIsProductRight()) {
            page = cpcAdGroupService.getMultiShopGroupListDoris(param);
        } else {
            page = cpcAdGroupService.getMultiShopGroupList(param);
        }
        CommonMultiShopGroupPage.Builder pageBuilder = CommonMultiShopGroupPage.newBuilder();
        pageBuilder.setPageNo(param.getPageNo());
        pageBuilder.setPageSize(param.getPageSize());
        pageBuilder.setTotalPage(page.getTotalPage());
        pageBuilder.setTotalSize(page.getTotalSize());
        if (CollectionUtils.isNotEmpty(page.getRows())) {

            List<Integer> shopList = page.getRows().stream().map(MultiShopGroupListVo::getShopId).distinct().collect(Collectors.toList());
            Map<Integer, ShopAuth> shopAuths = shopAuthDao.listAllByIds(param.getPuid(), shopList).stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
            List<String> campaignList = page.getRows().stream().map(MultiShopGroupListVo::getCampaignId).distinct().collect(Collectors.toList());
            Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = amazonAdCampaignAllDao.listByShopIdAndCampaignIds(request.getPuid(), shopList, campaignList).stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity()));
            List<String> portfolioIds = amazonAdCampaignAllMap.values().stream().map(AmazonAdCampaignAll::getPortfolioId).filter(Objects::nonNull).collect(Collectors.toList());
            Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                portfolioMap = portfolioDao.listByShopId(request.getPuid(), shopList, portfolioIds).stream()
                        .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, Function.identity()));
            }
            Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;
            List<CommonMultiShopGroup> rpcVos = page.getRows().stream().filter(Objects::nonNull).map(item -> {
                CommonMultiShopGroup.Builder voBuilder = CommonMultiShopGroup.newBuilder();
                voBuilder.setShopId(item.getShopId());
                voBuilder.setCampaignId(item.getCampaignId());
                voBuilder.setGroupId(item.getAdGroupId());
                voBuilder.setGroupName(item.getName());
                voBuilder.setState(item.getState());
                voBuilder.setAdType(item.getAdType());

                ShopAuth shopAuth = shopAuths.get(item.getShopId());
                if (shopAuth != null) {
                    voBuilder.setShopName(StringUtil.toStringSafe(shopAuth.getName()));
                    voBuilder.setSiteName(AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getMarketplaceCN());
                    voBuilder.setMarketplaceId(shopAuth.getMarketplaceId());
                }
                AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(item.getCampaignId());
                if (amazonAdCampaignAll != null) {
                    voBuilder.setCampaignName(StringUtil.toStringSafe(amazonAdCampaignAll.getName()));
                    if (StringUtils.isNotBlank(amazonAdCampaignAll.getPortfolioId()) && finalPortfolioMap.containsKey(amazonAdCampaignAll.getPortfolioId())) {
                        AmazonAdPortfolio amazonAdPortfolio = finalPortfolioMap.get(amazonAdCampaignAll.getPortfolioId());
                        voBuilder.setPortfolioName(StringUtil.toStringSafe(amazonAdPortfolio.getName()));
                        voBuilder.setPortfolioId(StringUtil.toStringSafe(amazonAdPortfolio.getPortfolioId()));
                    } else {
                        voBuilder.setPortfolioName("-");
                        voBuilder.setPortfolioId("-");
                    }
                }
                return voBuilder.build();
            }).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }
        builder.setCode(Result.SUCCESS);
        builder.setMsg("");
        builder.setData(pageBuilder);

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getCommonInfo(CommonInfoRequest request, StreamObserver<CommonInfoResponse> responseObserver) {
        log.info("查询广告基本信息 request {}", request);
        Set<Integer> rightShopSet = CollectionUtils.isEmpty(request.getRightShopIdList()) ?
                Sets.newHashSet() : Sets.newHashSet(request.getRightShopIdList());

        CommonInfoResponse.Builder builder = CommonInfoResponse.newBuilder();
        List<CommonInfoData> dataList = Lists.newArrayList();

        if (AdStructureLevelEnum.LEVEL_PORTFOLIO.getCode().equals(request.getType())) {
            dataList = getPortfolioInfo(request, rightShopSet);
        }
        if (AdStructureLevelEnum.LEVEL_CAMPAIGN.getCode().equals(request.getType())) {
            dataList = getCampaignInfo(request, rightShopSet);
        }
        if (AdStructureLevelEnum.LEVEL_GROUP.getCode().equals(request.getType())) {
            dataList = getGroupInfo(request, rightShopSet);
        }

        builder.setCode(Result.SUCCESS);
        builder.setMsg("");
        builder.addAllData(dataList);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private List<CommonInfoData> getPortfolioInfo(CommonInfoRequest request, Set<Integer> rightShopSet) {
        int puid = request.getPuid();

        List<PortfolioListParam> paramList = request.getShopVPairList().stream().map(item -> {
            PortfolioListParam param = new PortfolioListParam();
            param.setShopId(String.valueOf(item.getShopId()));
            param.setPortfolioId(item.getV());
            return param;
        }).collect(Collectors.toList());

        List<AmazonAdPortfolio> portfolioList = portfolioDao.getByShopPortfolioPair(puid, paramList);
        if (CollectionUtils.isEmpty(portfolioList)) {
            return Collections.emptyList();
        }

        List<Integer> shopList = portfolioList.stream().map(AmazonAdPortfolio::getShopId).distinct().collect(Collectors.toList());
        Map<Integer, ShopAuth> shopAuths = shopAuthDao.listAllByIds(puid, shopList).stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));

        return portfolioList.stream().filter(Objects::nonNull).map(item -> {
            CommonInfoData.Builder voBuilder = CommonInfoData.newBuilder();
            voBuilder.setShopId(item.getShopId());
            voBuilder.setPortfolioId(item.getPortfolioId());
            voBuilder.setPortfolioName(item.getName());
            voBuilder.setState(item.getState());
            voBuilder.setIsHidden(item.getIsHidden());
            ShopAuth shopAuth = shopAuths.get(item.getShopId());
            if (shopAuth != null) {
                voBuilder.setShopName(StringUtil.toStringSafe(shopAuth.getName()));
                voBuilder.setSiteName(AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getMarketplaceCN());
                voBuilder.setMarketplaceId(shopAuth.getMarketplaceId());
            }
            voBuilder.setHasAuth(rightShopSet.contains(item.getShopId()));
            return voBuilder.build();
        }).collect(Collectors.toList());
    }

    private List<CommonInfoData> getCampaignInfo(CommonInfoRequest request, Set<Integer> rightShopSet) {
        int puid = request.getPuid();
        List<MultiShopCampaignListParam> paramList = request.getShopVPairList().stream().map(item -> {
            MultiShopCampaignListParam param = new MultiShopCampaignListParam();
            param.setShopId(item.getShopId());
            param.setCampaignId(item.getV());
            return param;
        }).collect(Collectors.toList());

        List<AmazonAdCampaignAll> campaignList = amazonAdCampaignAllDao.getByShopCampaignPair(puid, paramList);
        if (CollectionUtils.isEmpty(campaignList)) {
            return Collections.emptyList();
        }

        List<Integer> shopList = campaignList.stream().map(AmazonAdCampaignAll::getShopId).distinct().collect(Collectors.toList());
        Map<Integer, ShopAuth> shopAuths = shopAuthDao.listAllByIds(puid, shopList).stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
        List<String> portfolioIds = campaignList.stream().map(AmazonAdCampaignAll::getPortfolioId).filter(Objects::nonNull).collect(Collectors.toList());

        Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            portfolioMap = portfolioDao.listByShopId(request.getPuid(), shopList, portfolioIds).stream()
                    .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, Function.identity()));
        }

        Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;
        return campaignList.stream().filter(Objects::nonNull).map(item -> {
            CommonInfoData.Builder voBuilder = CommonInfoData.newBuilder();
            voBuilder.setShopId(item.getShopId());
            voBuilder.setCampaignId(item.getCampaignId());
            voBuilder.setCampaignName(item.getName());
            voBuilder.setState(item.getState());
            voBuilder.setAdType(item.getType());

            ShopAuth shopAuth = shopAuths.get(item.getShopId());
            if (shopAuth != null) {
                voBuilder.setShopName(StringUtil.toStringSafe(shopAuth.getName()));
                voBuilder.setSiteName(AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getMarketplaceCN());
                voBuilder.setMarketplaceId(shopAuth.getMarketplaceId());
            }

            if (StringUtils.isNotBlank(item.getPortfolioId()) && finalPortfolioMap.containsKey(item.getPortfolioId())) {
                AmazonAdPortfolio amazonAdPortfolio = finalPortfolioMap.get(item.getPortfolioId());
                voBuilder.setPortfolioName(StringUtil.toStringSafe(amazonAdPortfolio.getName()));
                voBuilder.setPortfolioId(StringUtil.toStringSafe(amazonAdPortfolio.getPortfolioId()));
            } else {
                voBuilder.setPortfolioName("-");
                voBuilder.setPortfolioId("-");
            }
            voBuilder.setHasAuth(rightShopSet.contains(item.getShopId()));
            return voBuilder.build();
        }).collect(Collectors.toList());
    }

    private List<CommonInfoData> getGroupInfo(CommonInfoRequest request, Set<Integer> rightShopSet) {
        int puid = request.getPuid();
        List<MultiShopGroupListParam> paramList = request.getShopVPairList().stream().map(item -> {
            MultiShopGroupListParam param = new MultiShopGroupListParam();
            param.setShopId(item.getShopId());
            param.setAdGroupId(item.getV());
            return param;
        }).collect(Collectors.toList());

        List<MultiShopGroupListVo> adGroupList = cpcAdGroupService.getByShopGroupIdPair(puid, paramList);

        List<Integer> shopList = adGroupList.stream().map(MultiShopGroupListVo::getShopId).distinct().collect(Collectors.toList());
        Map<Integer, ShopAuth> shopAuths = shopAuthDao.listAllByIds(puid, shopList).stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
        List<String> campaignList = adGroupList.stream().map(MultiShopGroupListVo::getCampaignId).distinct().collect(Collectors.toList());
        Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = amazonAdCampaignAllDao.listByShopIdAndCampaignIds(puid, shopList, campaignList).stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity()));
        List<String> portfolioIds = amazonAdCampaignAllMap.values().stream().map(AmazonAdCampaignAll::getPortfolioId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            portfolioMap = portfolioDao.listByShopId(puid, shopList, portfolioIds).stream()
                    .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, Function.identity()));
        }
        Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;

        return adGroupList.stream().filter(Objects::nonNull).map(item -> {
            CommonInfoData.Builder voBuilder = CommonInfoData.newBuilder();
            voBuilder.setShopId(item.getShopId());
            voBuilder.setCampaignId(item.getCampaignId());
            voBuilder.setGroupId(item.getAdGroupId());
            voBuilder.setGroupName(item.getName());
            voBuilder.setState(item.getState());
            voBuilder.setAdType(item.getAdType());

            ShopAuth shopAuth = shopAuths.get(item.getShopId());
            if (shopAuth != null) {
                voBuilder.setShopName(StringUtil.toStringSafe(shopAuth.getName()));
                voBuilder.setSiteName(AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getMarketplaceCN());
                voBuilder.setMarketplaceId(shopAuth.getMarketplaceId());
            }
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(item.getCampaignId());
            if (amazonAdCampaignAll != null) {
                voBuilder.setCampaignName(StringUtil.toStringSafe(amazonAdCampaignAll.getName()));
                if (StringUtils.isNotBlank(amazonAdCampaignAll.getPortfolioId()) && finalPortfolioMap.containsKey(amazonAdCampaignAll.getPortfolioId())) {
                    AmazonAdPortfolio amazonAdPortfolio = finalPortfolioMap.get(amazonAdCampaignAll.getPortfolioId());
                    voBuilder.setPortfolioName(StringUtil.toStringSafe(amazonAdPortfolio.getName()));
                    voBuilder.setPortfolioId(StringUtil.toStringSafe(amazonAdPortfolio.getPortfolioId()));
                } else {
                    voBuilder.setPortfolioName("-");
                    voBuilder.setPortfolioId("-");
                }
            }
            voBuilder.setHasAuth(rightShopSet.contains(item.getShopId()));
            return voBuilder.build();
        }).collect(Collectors.toList());
    }

    @Override
    public void getMostSaleNumMarketplace(MostSaleNumMarketplaceRequest request, StreamObserver<MostSaleNumMarketplaceResponse> responseObserver) {
        int puid = request.getPuid();
        List<Integer> shopIdList = request.getShopIdList();

        String endDate = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now());
        String startDate = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now().minusDays(29));
        AmazonAdCampaignAll result = amazonAdCampaignAllDorisDao.getMostSaleNumMarketplaceId(puid, shopIdList, startDate, endDate);

        MostSaleNumMarketplaceResponse.Builder builder = MostSaleNumMarketplaceResponse.newBuilder();
        builder.setCode(Result.SUCCESS);
        builder.setMsg("");
        MostSaleNumMarketplaceResponse.ResponseData.Builder dataBuilder = MostSaleNumMarketplaceResponse.ResponseData.newBuilder();
        if (Objects.nonNull(result) && StringUtils.isNotBlank(result.getMarketplaceId())) {
            dataBuilder.setMarketplaceId(result.getMarketplaceId());
        }
        builder.setData(dataBuilder.build());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getGroupsByProduct(ProductGroupRequest request, StreamObserver<ProductGroupResponse> responseObserver) {
        log.info("getGroupsByProduct request:{}", ProtoBufUtil.toJsonStr(request));
        MultiShopGroupByProductParam param = new MultiShopGroupByProductParam();
        int puid = request.getPuid();
        param.setPuid(puid);
        param.setMarketplaceId(request.getMarketplaceId());
        param.setShopIds(request.getShopIdsList());
        param.setSearchType(request.getSearchType());
        param.setSearchValue(request.getSearchValueList());
        param.setContainType(request.getContainType());
        if (CollectionUtils.isNotEmpty(request.getStateList())) {
            param.setStates(request.getStateList().stream().filter(x -> stateList.contains(x)).collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(request.getClickPercent())) {
            param.setClickPercent(request.getClickPercent());
        }
        if (StringUtils.isBlank(request.getStartDate()) || StringUtils.isBlank(request.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
        } else {
            param.setStartDate(request.getStartDate());
            param.setEndDate(request.getEndDate());
        }

        List<ProductGroupResponse.ProductGroupData> dataList = Lists.newArrayList();
        List<ProductGroupResponse.ProductGroupData> spDataList = buildSpProductGroupData(param);
        if (CollectionUtils.isNotEmpty(spDataList)) {
            spDataList.sort(Comparator.comparing(ProductGroupResponse.ProductGroupData::getCampaignId).thenComparing(ProductGroupResponse.ProductGroupData::getAdGroupId));
            dataList.addAll(spDataList);
        }
        List<ProductGroupResponse.ProductGroupData> sbDataList = buildSbProductGroupData(param);
        if (CollectionUtils.isNotEmpty(sbDataList)) {
            sbDataList.sort(Comparator.comparing(ProductGroupResponse.ProductGroupData::getCampaignId).thenComparing(ProductGroupResponse.ProductGroupData::getAdGroupId));
            dataList.addAll(sbDataList);
        }
        ProductGroupResponse.Builder builder = ProductGroupResponse.newBuilder();
        builder.addAllGroups(dataList);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private List<ProductGroupResponse.ProductGroupData> buildSpProductGroupData(MultiShopGroupByProductParam param) {
        Integer puid = param.getPuid();
        List<OdsAmazonAdProduct> productGroup = cpcProductService.getSpProductGroup(param);
        if (CollectionUtils.isEmpty(productGroup)) {
            return Collections.emptyList();
        }
        // 广告组点击量数据
        Map<String, Integer> groupClickMap = cpcAdGroupService.getGroupClickData(param, productGroup)
                .stream().filter(i -> Objects.nonNull(i) && StringUtils.isNotBlank(i.getAdGroupId()))
                .collect(Collectors.toMap(AmazonAdGroupDorisAllReport::getAdGroupId, AmazonAdGroupDorisAllReport::getClicks, (v1, v2) -> v2));
        // 广告产品数据
        List<AdProductPageVo> productClickData = cpcProductService.getSpProductClickData(param, productGroup);
        if (CollectionUtils.isEmpty(productClickData)) {
            return Collections.emptyList();
        }

        List<Integer> shopIds = productClickData.stream().map(AdProductPageVo::getShopId).distinct().collect(Collectors.toList());
        List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(puid, shopIds);
        Map<Integer, ShopAuth> shopAuthMap = shopAuths.stream().collect(Collectors.toMap(ShopAuth::getId, v -> v, (v1, v2) -> v2));

        List<String> adGroupIds = productClickData.stream().map(AdProductPageVo::getAdGroupId).distinct().collect(Collectors.toList());
        List<AmazonAdGroup> adGroups = spGroupDao.listByGroupIdsAndShopIdList(puid, shopIds, adGroupIds);
        Map<String, AmazonAdGroup> spGroupMap = adGroups.stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity(), (e1, e2) -> e2));

        List<String> campaignIds = adGroups.stream().map(AmazonAdGroup::getCampaignId).distinct().collect(Collectors.toList());
        List<AmazonAdCampaignAll> campaignList = amazonAdCampaignAllDao.getByCampaignIdsAndShopIdList(puid, shopIds, param.getMarketplaceId(), campaignIds, Constants.SP);
        Map<String, AmazonAdCampaignAll> campaignMap = campaignList.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));

        BigDecimal clickPercent = StringUtils.isNotBlank(param.getClickPercent()) ? (new BigDecimal(param.getClickPercent())).divide(BigDecimal.valueOf(100)) : BigDecimal.ZERO;

        return productClickData.stream().map(item -> {
            Integer groupClick = groupClickMap.getOrDefault(item.getAdGroupId(), 0);
            BigDecimal productClickPer = MathUtil.divideByZero(new BigDecimal(item.getClicks()), new BigDecimal(groupClick), 4);
            if (clickPercent.compareTo(BigDecimal.ZERO) > 0) {
                if (productClickPer.compareTo(clickPercent) < 0) {
                    return null;
                }
            }

            ProductGroupResponse.ProductGroupData.Builder dataBuilder = ProductGroupResponse.ProductGroupData.newBuilder();
            dataBuilder.setType(Constants.SP);
            dataBuilder.setShopId(item.getShopId());
            if (shopAuthMap.containsKey(item.getShopId())) {
                dataBuilder.setShopName(shopAuthMap.get(item.getShopId()).getName());
            }
            dataBuilder.setAdGroupId(item.getAdGroupId());
            if (spGroupMap.containsKey(item.getAdGroupId())) {
                AmazonAdGroup amazonAdGroup = spGroupMap.get(item.getAdGroupId());
                dataBuilder.setAdGroupName(amazonAdGroup.getName());
                if (campaignMap.containsKey(amazonAdGroup.getCampaignId())) {
                    AmazonAdCampaignAll campaign = campaignMap.get(amazonAdGroup.getCampaignId());
                    dataBuilder.setCampaignId(campaign.getCampaignId());
                    dataBuilder.setCampaignName(campaign.getName());
                    dataBuilder.setPortfolioId(StringUtils.isNotBlank(campaign.getPortfolioId()) ? campaign.getPortfolioId() : "");
                }
            }
            dataBuilder.setAsin(item.getAsin());
            dataBuilder.setSku(item.getSku());
            dataBuilder.setMsku(item.getSku());
            if (StringUtils.isNotBlank(item.getParentAsin())) {
                dataBuilder.setParentAsin(item.getParentAsin());
            }
            if (StringUtils.isNotBlank(item.getImgUrl())) {
                dataBuilder.setMainImg(item.getImgUrl());
            }
            if (StringUtils.isNotBlank(item.getTitle())) {
                dataBuilder.setTitle(item.getTitle());
            }
            if (productClickPer.compareTo(BigDecimal.ONE) > 0) {
                productClickPer = BigDecimal.ONE;
            }
            dataBuilder.setClickPercent(productClickPer.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
            return dataBuilder.build();
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<ProductGroupResponse.ProductGroupData> buildSbProductGroupData(MultiShopGroupByProductParam param) {
        List<OdsAmazonSbAds> sbProductGroup = cpcProductService.getSbProductGroup(param);
        if (CollectionUtils.isEmpty(sbProductGroup)) {
            return Collections.emptyList();
        }
        Integer puid = param.getPuid();

        List<Integer> shopIds = sbProductGroup.stream().map(OdsAmazonSbAds::getShopId).distinct().collect(Collectors.toList());
        List<String> asinLit = sbProductGroup.stream().map(OdsAmazonSbAds::getAsinList).flatMap(List::stream).distinct().collect(Collectors.toList());

        List<OdsProduct> productList = odsProductDao.listByAsins(puid, param.getMarketplaceId(), shopIds, asinLit);
        Map<String, List<OdsProduct>> productMap = StreamUtil.groupingBy(productList, k -> k.getShopId() + "-" + k.getAsin());

        List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(puid, shopIds);
        Map<Integer, ShopAuth> shopAuthMap = shopAuths.stream().collect(Collectors.toMap(ShopAuth::getId, v -> v, (v1, v2) -> v2));

        List<String> adGroupIds = sbProductGroup.stream().map(OdsAmazonSbAds::getAdGroupId).distinct().collect(Collectors.toList());
        List<AmazonSbAdGroup> adGroups = sbGroupDao.getListByShopIdsAndGroupIds(puid, shopIds, adGroupIds);
        Map<String, AmazonSbAdGroup> groupMap = adGroups.stream().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, Function.identity(), (e1, e2) -> e2));

        List<String> campaignIds = adGroups.stream().map(AmazonSbAdGroup::getCampaignId).distinct().collect(Collectors.toList());
        List<AmazonAdCampaignAll> campaignList = amazonAdCampaignAllDao.getByCampaignIdsAndShopIdList(puid, shopIds, param.getMarketplaceId(), campaignIds, Constants.SB);
        Map<String, AmazonAdCampaignAll> campaignMap = campaignList.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));

        return sbProductGroup.stream().map(item -> {
            Integer shopId = item.getShopId();
            return item.getAsinList().stream().map(asin -> {
                ProductGroupResponse.ProductGroupData.Builder dataBuilder = ProductGroupResponse.ProductGroupData.newBuilder();
                dataBuilder.setType(Constants.SB);
                dataBuilder.setShopId(shopId);
                if (shopAuthMap.containsKey(item.getShopId())) {
                    dataBuilder.setShopName(shopAuthMap.get(item.getShopId()).getName());
                }
                dataBuilder.setAdGroupId(item.getAdGroupId());
                if (groupMap.containsKey(item.getAdGroupId())) {
                    AmazonSbAdGroup amazonAdGroup = groupMap.get(item.getAdGroupId());
                    dataBuilder.setAdGroupName(amazonAdGroup.getName());
                    if (campaignMap.containsKey(amazonAdGroup.getCampaignId())) {
                        AmazonAdCampaignAll campaign = campaignMap.get(amazonAdGroup.getCampaignId());
                        dataBuilder.setCampaignId(campaign.getCampaignId());
                        dataBuilder.setCampaignName(campaign.getName());
                        dataBuilder.setPortfolioId(StringUtils.isNotBlank(campaign.getPortfolioId()) ? campaign.getPortfolioId() : "");
                    }
                }
                dataBuilder.setAsin(asin);
                List<OdsProduct> asinList = productMap.get(shopId + "-" + asin);
                if (CollectionUtils.isNotEmpty(asinList)) {
                    OdsProduct asinInfo = asinList.stream()
                            .filter(i -> StringUtils.isNotBlank(i.getMainImage()) && StringUtils.isNotBlank(i.getTitle()))
                            .findFirst()
                            .orElseGet(() -> asinList.stream().filter(i -> StringUtils.isNotBlank(i.getTitle())).findFirst().orElse(null));
                    if (Objects.nonNull(asinInfo)) {
                        dataBuilder.setTitle(asinInfo.getTitle());
                        dataBuilder.setMainImg(asinInfo.getMainImage());
                    }
                    List<String> skuList = asinList.stream().map(OdsProduct::getSku).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                    dataBuilder.addAllSkuList(skuList);
                    dataBuilder.addAllMskuList(skuList);
                    dataBuilder.addAllParentAsinList(asinList.stream().map(OdsProduct::getParentAsin).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
                }
                return dataBuilder.build();
            }).collect(Collectors.toList());
        }).flatMap(List::stream).collect(Collectors.toList());
    }

    @Override
    public void changeRate(ChangeRateRequest request, StreamObserver<ChangeRateResponse> responseObserver) {
        List<ChangeRateParam> paramList = cn.hutool.json.JSONUtil.toList(request.getRequest(), ChangeRateParam.class);
        List<ChangeRateVo> rateVoList = multipleCommonService.changeRate(request.getPuid(), paramList);
        List<ChangeRateResponse.ChangeRate> rateList = new ArrayList<>();
        ChangeRateResponse.Builder builder = ChangeRateResponse.newBuilder();
        for (ChangeRateVo rateVo : rateVoList) {
            ChangeRateResponse.ChangeRate.Builder rateBuilder = ChangeRateResponse.ChangeRate.newBuilder();
            rateBuilder.setFrom(rateVo.getFrom());
            rateBuilder.setTo(rateVo.getTo());
            rateBuilder.setRate(rateVo.getRate().doubleValue());
            rateBuilder.setOriginalAmount(rateVo.getOriginalAmount().doubleValue());
            rateBuilder.setTargetAmount(rateVo.getTargetAmount().doubleValue());
            rateList.add(rateBuilder.build());
        }
        builder.setCode(Result.SUCCESS);
        builder.addAllData(rateList);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private SyncBasicDto build(SyncAdBasicInfoData syncAdBasicInfoData) {
        SyncBasicDto syncBasicDto = new SyncBasicDto();
        syncBasicDto.setId(syncAdBasicInfoData.getId());
        syncBasicDto.setSyncType(syncAdBasicInfoData.getSyncType());
        syncBasicDto.setShopId(syncAdBasicInfoData.getShopId());
        syncBasicDto.setType(syncAdBasicInfoData.getType());
        syncBasicDto.setPortfolioId(syncAdBasicInfoData.getPortfolioId());
        return syncBasicDto;
    }

    @Override
    public void syncAdBasicInfo(SyncAdBasicInfoRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("syncAdBasicInfo = {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        Result<String> result = cpcCampaignService.syncMultipleBasicInfo(request.getPuid(), request.getListList().stream().map(this::build).collect(Collectors.toList()));
        if (result.getCode() == Result.SUCCESS) {
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setMsg(result.getMsg());
            builder.setData(result.getData());
        } else {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(result.getMsg());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void removePortFolio(SyncAdBasicInfoRequest request, StreamObserver<RemovePortFolioDataResponse> responseObserver) {
        log.info("removePortFolio = {}", request);
        RemovePortFolioDataResponse.Builder builder = RemovePortFolioDataResponse.newBuilder();
        Result<List<PortfolioErrorMsgVo>> result = amazonAdPortfolioService.removeMultiplePortFolioData(request.getPuid(), request.getUid(), request.getLoginIp(), request.getListList().stream().map(this::build).collect(Collectors.toList()));
        if (result.success()) {
            List<PortfolioErrorMsgVo> resultData = result.getData();
            if (CollectionUtils.isNotEmpty(resultData)) {
                List<ErrorMsgVo> rpcList = new ArrayList<>(resultData.size());
                for (PortfolioErrorMsgVo msgVo : resultData) {
                    ErrorMsgVo.Builder builder1 = ErrorMsgVo.newBuilder();
                    if (msgVo.getCampaignId() != null) {
                        builder1.setCampaignId(msgVo.getCampaignId());
                    }
                    if (msgVo.getName() != null) {
                        builder1.setName(msgVo.getName());
                    }
                    if (msgVo.getErrMsg() != null) {
                        builder1.setErrMsg(msgVo.getErrMsg());
                    }
                    rpcList.add(builder1.build());
                }
                builder.addAllData(rpcList);
            }

            builder.setCode(Result.SUCCESS);
        } else {
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            builder.setCode(Result.ERROR);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void movePortFolio(SyncAdBasicInfoRequest request, StreamObserver<MovePortFolioDataResponse> responseObserver) {
        log.info("removePortFolio = {}", request);
        MovePortFolioDataResponse.Builder builder = MovePortFolioDataResponse.newBuilder();
        Result<List<PortfolioErrorMsgVo>> result = amazonAdPortfolioService.moveMultiplePortFolioData(request.getPuid(), request.getUid(), request.getLoginIp(), request.getListList().stream().map(this::build).collect(Collectors.toList()));
        if (result.success()) {
            List<PortfolioErrorMsgVo> resultData = result.getData();
            if (CollectionUtils.isNotEmpty(resultData)) {
                List<ErrorMsgVo> rpcList = new ArrayList<>(resultData.size());
                for (PortfolioErrorMsgVo msgVo : resultData) {
                    ErrorMsgVo.Builder builder1 = ErrorMsgVo.newBuilder();
                    if (msgVo.getCampaignId() != null) {
                        builder1.setCampaignId(msgVo.getCampaignId());
                    }
                    if (msgVo.getName() != null) {
                        builder1.setName(msgVo.getName());
                    }
                    if (msgVo.getErrMsg() != null) {
                        builder1.setErrMsg(msgVo.getErrMsg());
                    }
                    rpcList.add(builder1.build());
                }
                builder.addAllData(rpcList);
            }

            builder.setCode(Result.SUCCESS);
        } else {
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            builder.setCode(Result.ERROR);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    @Override
    public void updatePortfolioHiddenState(SyncAdBasicInfoRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("updatePortfolioHiddenState = {}", request);
        List<SyncAdBasicInfoData> list = request.getListList();
        Map<Integer, List<SyncAdBasicInfoData>> listMap = list.stream().collect(Collectors.groupingBy(SyncAdBasicInfoData::getShopId));
        listMap.forEach((key, value) -> amazonAdPortfolioService.updatePortfolioHiddenState(request.getPuid(), request.getUid(), key, value.stream().map(SyncAdBasicInfoData::getPortfolioId).collect(Collectors.joining(",")), request.getIsHidden()));
        responseObserver.onNext(PbUtil.buildSuccess());
        responseObserver.onCompleted();
    }
}
