package com.meiyunji.sponsored.api.export;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.AssertUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.PageUtil;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.grpc.common.*;
import com.meiyunji.sponsored.grpc.entry.ReportDateModelPb;
import com.meiyunji.sponsored.rpc.adAggregateHour.AdPageBasicData;
import com.meiyunji.sponsored.rpc.export.*;
import com.meiyunji.sponsored.service.account.bo.ShopAuthBo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.adTagSystem.service.impl.AdManageTagRelationService;
import com.meiyunji.sponsored.service.config.nacos.AdManageLimitConfig;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.doris.dao.IDwsSaleProfitShopDayDao;
import com.meiyunji.sponsored.service.enums.CurrencyUnitEnum;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.multiple.common.utils.MultipleUtils;
import com.meiyunji.sponsored.service.reportHour.service.*;
import com.meiyunji.sponsored.service.reportHour.vo.*;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2023/10/25 20:48
 * @describe:
 */

@GRpcService
@Slf4j
public class AdAggregateExportRpcService extends AggregateExportServiceGrpc.AggregateExportServiceImplBase {
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private IAmazonAdCampaignHourReportService amazonAdCampaignHourReportService;
    @Resource
    private CpcPageIdsHandler cpcPageIdsHandler;

    @Resource
    IAmazonAdPlacementHourReportService amazonAdPlacementHourReportService;

    @Resource
    IAmazonAdGroupHourReportService amazonAdGroupHourReportService;

    @Resource
    private IAmazonAdProductHourReportService amazonAdProductHourReportService;

    @Resource
    private IAmazonAdTargetHourReportService amazonAdTargetHourReportService;

    @Resource
    private IAmazonAdKeywordHourReportService amazonAdKeywordHourReportService;

    @Autowired
    private CpcShopDataService cpcShopDataService;

    @Resource
    private IDwsSaleProfitShopDayDao dwsSaleProfitShopDayDao;

    @Autowired
    private AdManageTagRelationService adManageTagRelationService;

    @Resource
    private AdManageLimitConfig adManageLimitConfig;

    @Override
    public void campaignAggregateData(GetCampaignAggregateExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("cpc campaign hour report data export request:{}", request);
        NeTargetingDataResponse.Builder builder = NeTargetingDataResponse.newBuilder();
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        if (!request.hasPuid() || (!request.hasShopId() && CollectionUtils.isEmpty(request.getShopIdListList())) || !request.hasEndDateStr() || !request.hasStartDateStr()
                || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        // grpc参数转param
        CampaignAggregateHourParamVO param = grpcToParam(request);
        // 店铺状态校验
        List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(param.getPuid(), param.getShopIdList());
        if (CollectionUtils.isEmpty(shopAuths)) {
            throw new SponsoredBizException("店铺未授权");
        }
         // 获取数据
        List<AdCampaignHourVo> voList = getAdCampaignHourVos(request, param, shopAuths);
        if (CollectionUtils.isEmpty(voList)) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("excel.export.none");
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        try {
            // 生成导出url
            String downloadUrl = getDownloadUrl(request, param, shopAuths, voList);
            List<String> urls = Lists.newLinkedList();
            urls.add(downloadUrl);
            urlBuilder.addAllUrls(urls);
            urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
        } catch (Exception e) {
            log.error("NeKeywords err(手动商品否定投放):{},puid{},shopId{}", e, request.getPuid(), request.getShopId());
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("process.msg.sync.fail");
        } finally {
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
        }
    }

    /**
     * 生成导出url
     */
    private String getDownloadUrl(GetCampaignAggregateExportRequest request, CampaignAggregateHourParamVO param, List<ShopAuth> shopAuths, List<AdCampaignHourVo> voList) {
        // 取店铺销售额
        BigDecimal shopSalesByDate = dwsSaleProfitShopDayDao.sumShopSaleByDateRange(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate(), MultipleUtils.changeRate(shopAuths));
        List<String> voExcludeFileds = new ArrayList<>();
        if (!Integer.valueOf(1).equals(param.getIsCompare())) {
            voExcludeFileds = Lists.newArrayList("costCompare", "costCompareRate",
                    "impressionsCompare", "impressionsCompareRate",
                    "clicksCompare", "clicksCompareRate",
                    "adOrderNumCompare", "adOrderNumCompareRate",
                    "adSalesCompare", "adSalesCompareRate",
                    "adSaleNumCompare", "adSaleNumCompareRate",
                    "cpcCompare", "cpcCompareRate",
                    "clickRateCompare", "clickRateCompareRate",
                    "salesConversionRateCompare", "salesConversionRateCompareRate",
                    "acosCompare", "acosCompareRate",
                    "roasCompare", "roasCompareRate");
        }
        String spType = StringUtils.isNotBlank(request.getType()) ? request.getType() : Constants.SB;
        String currencyCode = MultipleUtils.getCurrency(shopAuths);
        List<AdCampaignHourExportVo> datas = new ArrayList<>(24);
        List<AdSpCampaignHourExportVo> spDatas = new ArrayList<>(24);
        for (AdCampaignHourVo vo : voList) {
            vo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate));
            vo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate));
            if (Constants.SP.equalsIgnoreCase(spType)) {
                spDatas.add(new AdSpCampaignHourExportVo(currencyCode, vo));
                continue;
            }
            datas.add(new AdCampaignHourExportVo(currencyCode, vo));
        }
        // 文件名称
        String fileName = MultipleUtils.getExportShopName(shopAuths) + "_广告活动小时数据" + "_" + param.getStartDate() + "_" + param.getEndDate();
        //excel币种表头渲染
        WriteHandlerBuild build = new WriteHandlerBuild().rate();
        if (Constants.SP.equalsIgnoreCase(spType)) {
            return excelService.easyExcelHandlerExport(request.getPuid().getValue(), spDatas, fileName, AdSpCampaignHourExportVo.class, build.currencyNew(AdSpCampaignHourExportVo.class), voExcludeFileds);
        } else {
            if (request.getDateModel() != ReportDateModelPb.ReportDateModel.HOURLY) {
                voExcludeFileds.addAll(Lists.newArrayList("acots", "asots", "unitsOrderedNewToBrandPercentage", "unitsOrderedNewToBrand", "salesNewToBrandPercentage", "salesNewToBrand", "ordersNewToBrandPercentage", "ordersNewToBrand",
                        "advertisingOtherProductUnitPrice", "advertisingProductUnitPrice", "advertisingUnitPrice", "vcpm", "vctr", "vrt", "viewableImpressions", "adCostPercentage", "adOrderNumPercentage", "adSalePercentage", "orderNumPercentage"));
            }
            return excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas, fileName, AdCampaignHourExportVo.class, build.currencyNew(AdCampaignHourExportVo.class), voExcludeFileds);
        }
    }

    /**
     * 获取数据
     */
    private List<AdCampaignHourVo> getAdCampaignHourVos(GetCampaignAggregateExportRequest request, CampaignAggregateHourParamVO param, List<ShopAuth> shopAuths) {
        List<AdCampaignHourVo> voList = new ArrayList<>();
        CampaignHourParam paramOld = new CampaignHourParam();
        BeanUtils.copyProperties(param, paramOld);
        List<String> temporaryIds = cpcPageIdsHandler.getCampaignIdsTemporary(request.getPuid().getValue(),
                request.getPageSign(), "", param.getShopId(), new CampaignAggregateHourParamVO[]{param});
        if (CollectionUtils.isNotEmpty(temporaryIds)) {
            // 多店鋪,超过限制广告活动数量返回，防止doris cpu过高
            if((shopAuths.size() > 1 && temporaryIds.size() >= adManageLimitConfig.getHourLimit()) || shopAuths.size() > 40){
                if(shopAuths.size() > 40){
                    throw new SponsoredBizException("当前店铺数量超过40个，请减少后重新查询!");
                }else{
                    throw new SponsoredBizException("当前所选数据量过大，请减少店铺查询!");
                }
            }
            if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY) {
                voList = amazonAdCampaignHourReportService.getAggregateHourList(shopAuths, temporaryIds, param);
            } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.DAILY) {
                voList = amazonAdCampaignHourReportService.getAggregateDayList(request.getPuid().getValue(), temporaryIds, paramOld, null);
            } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.WEEKLY) {
                voList = amazonAdCampaignHourReportService.getAggregateWeekList(request.getPuid().getValue(), temporaryIds, paramOld, null);
            } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.MONTHLY) {
                voList = amazonAdCampaignHourReportService.getAggregateMonthList(request.getPuid().getValue(), temporaryIds, paramOld, null);
            }
        }
        return voList;
    }

    /**
     * grpc参数转param
     */
    private static CampaignAggregateHourParamVO grpcToParam(GetCampaignAggregateExportRequest request) {
        CampaignAggregateHourParamVO param = new CampaignAggregateHourParamVO();
        param.setWeeks(request.getWeeks());
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setStartDate(request.getStartDateStr());
        param.setEndDate(request.getEndDateStr());
        param.setEndDateCompare(request.getEndDateCompareStr());
        param.setStartDateCompare(request.getStartDateCompareStr());
        param.setShopIdList(request.getShopIdListList());
        if (request.hasIsCompare()) {
            param.setIsCompare(request.getIsCompare().getValue());
        }
        // 兼容旧单店铺逻辑
        if(CollectionUtils.isEmpty(param.getShopIdList()) && param.getShopId() != null){
            param.setShopIdList(CollectionUtil.newArrayList(param.getShopId()));
        }
        return param;
    }

    @Override
    public void campaignData(GetCampaignExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("cpc campaign hour report data export request:{}", request);
        List<ShopAuth> shopAuth = shopAuthDao.getScAndVcByIds(request.getShopIdsList());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        NeTargetingDataResponse.Builder builder = NeTargetingDataResponse.newBuilder();
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        if (!request.hasPuid() || CollectionUtils.isEmpty(request.getShopIdsList()) || !request.hasEndDateStr() || !request.hasStartDateStr()
                || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompare() || !request.hasEndDateCompare()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        int count = 0;
        String downloadUrl;
        List<String> urls = Lists.newLinkedList();
        CampaignAggregateHourMultiShopParamVO param = new CampaignAggregateHourMultiShopParamVO();
        param.setWeeks(request.getWeeks());
        param.setPuid(request.getPuid().getValue());
        param.setShopIds(request.getShopIdsList());
        param.setStartDate(request.getStartDateStr());
        param.setEndDate(request.getEndDateStr());
        param.setEndDateCompare(request.getEndDateCompare());
        param.setStartDateCompare(request.getStartDateCompare());
        param.setCurrency(request.getCurrency());
        if (request.hasIsCompare()) {
            param.setIsCompare(request.getIsCompare().getValue());
        }
        if (CollectionUtils.isNotEmpty(request.getMarketplaceIdsList())) {
            param.setMarketplaceIds(request.getMarketplaceIdsList());
        }
        if (StringUtils.isNotBlank(request.getPageSign())) {
            param.setPageSign(request.getPageSign());
        }
        if (StringUtils.isNotBlank(request.getTagId())) {
            param.setTagId(request.getTagId());
        }
        if (StringUtils.isNotBlank(request.getGroupId())) {
            param.setGroupId(request.getGroupId());
        }
        if (StringUtils.isNotBlank(request.getOrderField())) {
            param.setOrderField(request.getOrderField());
        }
        if (StringUtils.isNotBlank(request.getOrderType())) {
            param.setOrderType(request.getOrderType());
        }
        //站点过滤
        if (CollectionUtils.isNotEmpty(param.getMarketplaceIds())) {
            List<ShopAuthBo> shopAuthList = shopAuthDao.getShopAuthBoByMarketPlaceAndIds(param.getPuid(), param.getShopIds(), param.getMarketplaceIds());
            if (CollectionUtils.isEmpty(shopAuthList)) {
                param.setShopIds(shopAuthList.stream().map(ShopAuthBo::getId).collect(Collectors.toList()));
            }
        }
        List<AdCampaignHourVo> voList = new ArrayList<>();
        List<String> temporaryIds = new ArrayList<>();
        List<String> relationIds = new ArrayList<>();
        //层级类型：0-广告活动
        int type = 0;
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getPuid(), param.getShopIds(), param.getStartDate().replace("-", ""), param.getEndDate().replace("-", ""));
        //标签组或汇总导出
        if (CollectionUtils.isNotEmpty(param.getShopIds())) {
            if (StringUtils.isNotBlank(request.getPageSign())) {
                //标签组或汇总的导出
                temporaryIds = cpcPageIdsHandler.getTemporaryAggregateIds(param.getPageSign(), request.getGroupId());
                relationIds = adManageTagRelationService.getRelationIdByTagIds(param.getPuid(), temporaryIds, type, param.getShopIds());
            } else {
                //单个标签的导出
                relationIds = adManageTagRelationService.getRelationIdByTagId(param.getPuid(), param.getTagId(), type, param.getShopIds());
            }
        }

        boolean bool = true;
        CampaignHourParamMultiShop paramOld = new CampaignHourParamMultiShop();
        param.setMultipleIdComputation(true);
        BeanUtils.copyProperties(param, paramOld);
        if(CollectionUtils.isNotEmpty(relationIds)){
            if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY) {
                voList = amazonAdCampaignHourReportService.getAggregateHourListMultiShop(shopAuth, relationIds, param);
                bool = false;
            } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.DAILY) {
                voList = amazonAdCampaignHourReportService.getAggregateDayListMultiShop(param.getPuid(), relationIds, paramOld, shopAuth, null);
            } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.WEEKLY) {
                voList = amazonAdCampaignHourReportService.getAggregateWeekListMultiShop(param.getPuid(), relationIds, paramOld, shopAuth, null);
            } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.MONTHLY) {
                voList = amazonAdCampaignHourReportService.getAggregateMonthListMultiShop(param.getPuid(), relationIds, paramOld, shopAuth, null);
            }
        }

        if (CollectionUtils.isEmpty(voList)) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("excel.export.none");
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        List<String> voExcludeFileds = new ArrayList<>();
        if (!Integer.valueOf(1).equals(param.getIsCompare())) {
            voExcludeFileds = Lists.newArrayList("costCompare", "costCompareRate", "impressionsCompare", "costCompare", "impressionsCompareRate",
                    "clicksCompareRate", "adOrderNumCompare", "adOrderNumCompareRate", "adSalesCompare",
                    "adSalesCompareRate", "adSaleNumCompare", "adSaleNumCompareRate", "clicksCompare",
                    "cpcCompare", "cpcCompareRate", "clickRateCompare", "clickRateCompareRate",
                    "salesConversionRateCompare", "salesConversionRateCompareRate", "acosCompare", "acosCompareRate",
                    "roasCompare", "roasCompareRate");
        }
        String spType = StringUtils.isNotBlank(request.getType()) ? request.getType() : Constants.SP;
        String currencyCode = "";
        if (StringUtils.isNotBlank(param.getCurrency())) {
            currencyCode = CurrencyUnitEnum.getByCurrency(param.getCurrency()).getUnit();
        }
        List<AdCampaignHourExportVo> datas = new ArrayList<>(24);
        List<AdSpCampaignHourExportVo> spDatas = new ArrayList<>(24);
        for (AdCampaignHourVo vo : voList) {
            vo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate));
            vo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate));
            if (Constants.SP.equalsIgnoreCase(spType)) {
                spDatas.add(new AdSpCampaignHourExportVo(currencyCode, vo));
                continue;
            }
            datas.add(new AdCampaignHourExportVo(currencyCode, vo));
        }
        boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                Constants.isADOrderField(param.getOrderField(), AdCampaignHourExportVo.class);
        if (isSorted) {
            PageUtil.sortedByOrderField(spDatas, param.getOrderField(), param.getOrderType());
        }
        try {
            //excel币种表头渲染
            WriteHandlerBuild build = new WriteHandlerBuild().rate();
            if (Constants.SP.equalsIgnoreCase(spType)) {
                voExcludeFileds.addAll(Lists.newArrayList("acots", "asots"));
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), spDatas, request.getFileName() + "(" + count++ + ")", AdSpCampaignHourExportVo.class, build.currencyNew(AdSpCampaignHourExportVo.class), voExcludeFileds);
            } else {
                if (bool) {
                    voExcludeFileds.addAll(Lists.newArrayList("acots","asots","unitsOrderedNewToBrandPercentage", "unitsOrderedNewToBrand", "salesNewToBrandPercentage", "salesNewToBrand", "ordersNewToBrandPercentage", "ordersNewToBrand",
                            "advertisingOtherProductUnitPrice","advertisingProductUnitPrice","advertisingUnitPrice","vcpm","vctr","vrt", "viewableImpressions", "adCostPercentage", "adOrderNumPercentage", "adSalePercentage", "orderNumPercentage"));
                }
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas, request.getFileName() + "(" + count++ + ")", AdCampaignHourExportVo.class, build.currencyNew(AdCampaignHourExportVo.class), voExcludeFileds);
            }
            urls.add(downloadUrl);
            urlBuilder.addAllUrls(urls);
            urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
        } catch (Exception e) {
            log.error("export err(标签系统小时维度导出异常):{},puid{},shopId{}", e, request.getPuid(), request.getShopIdsList());
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("process.msg.sync.fail");
        } finally {
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void campaignWeekData(GetCampaignExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("cpc campaign weekly hour export request:{}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || CollectionUtils.isEmpty(request.getShopIdsList()) || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();
                CampaignHourParamMultiShop param = new CampaignHourParamMultiShop();
                param.setPuid(request.getPuid().getValue());
                param.setShopIds(request.getShopIdsList());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                Optional.of(request.getPageSign()).ifPresent(param::setPageSign);
                param.setCurrency(request.getCurrency());
                if (StringUtils.isNotBlank(request.getTagId())) {
                    param.setTagId(request.getTagId());
                }
                if (StringUtils.isNotBlank(request.getGroupId())) {
                    param.setGroupId(request.getGroupId());
                }
                if (CollectionUtils.isNotEmpty(request.getMarketplaceIdsList())) {
                    param.setMarketplaceIds(request.getMarketplaceIdsList());
                }
                if (StringUtils.isNotBlank(request.getOrderField())) {
                    param.setOrderField(request.getOrderField());
                }
                if (StringUtils.isNotBlank(request.getOrderType())) {
                    param.setOrderType(request.getOrderType());
                }
                if (request.hasIsCompare()) {
                    param.setIsCompare(request.getIsCompare().getValue());
                    if (StringUtils.isNotBlank(request.getStartDateCompare())) {
                        param.setStartDateCompare(request.getStartDateCompare());
                    }
                    if (StringUtils.isNotBlank(request.getEndDateCompare())) {
                        param.setEndDateCompare(request.getEndDateCompare());
                    }
                }
                //站点过滤
                if (CollectionUtils.isNotEmpty(param.getMarketplaceIds())) {
                    List<ShopAuthBo> shopAuthList = shopAuthDao.getShopAuthBoByMarketPlaceAndIds(param.getPuid(), param.getShopIds(), param.getMarketplaceIds());
                    if (CollectionUtils.isEmpty(shopAuthList)) {
                        param.setShopIds(shopAuthList.stream().map(ShopAuthBo::getId).collect(Collectors.toList()));
                    }
                }
                List<AdCampaignHourVo> voList = new ArrayList<>();
                //获取广告活动Id
                List<String> relationIds = new ArrayList<>();
                List<String> temporaryIds = new ArrayList<>();
                //层级类型：0-广告活动
                int type = 0;
                if (CollectionUtils.isNotEmpty(param.getShopIds())) {
                    if (StringUtils.isNotBlank(param.getPageSign())) {
                        //标签组或汇总的导出
                        temporaryIds = cpcPageIdsHandler.getTemporaryAggregateIds(param.getPageSign(), request.getGroupId());
                        relationIds = adManageTagRelationService.getRelationIdByTagIds(param.getPuid(), temporaryIds, type, param.getShopIds());
                    } else {
                        //单个标签的导出
                        relationIds = adManageTagRelationService.getRelationIdByTagId(param.getPuid(), param.getTagId(), type, param.getShopIds());
                    }
                }
                param.setMultipleIdComputation(true);
                List<AdCampaignWeekDayVo> campaignWeekDayVoList = amazonAdCampaignHourReportService.
                        getAggregateWeeklySuperpositionListMultiShop(request.getPuid().getValue(), relationIds, param);
                if (CollectionUtils.isNotEmpty(campaignWeekDayVoList)) {
                    campaignWeekDayVoList.forEach(e-> voList.addAll(e.getDetails()));
                }

                BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getPuid(), param.getShopIds(), param.getStartDate().replace("-", ""), param.getEndDate().replace("-", ""));
                String currencyCode = "";
                if (StringUtils.isNotBlank(param.getCurrency())) {
                    currencyCode = CurrencyUnitEnum.getByCurrency(param.getCurrency()).getUnit();
                }
                List<AdCampaignWeekExportVo> datas = new ArrayList<>();
                for (AdCampaignHourVo vo : voList) {
                    vo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate));
                    vo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate));
                    AdCampaignWeekExportVo exVo = new AdCampaignWeekExportVo(currencyCode, vo);
                    datas.add(exVo);
                }
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                        Constants.isADOrderField(param.getOrderField(), AdCampaignWeekExportVo.class);
                if (isSorted) {
                    PageUtil.sortedByOrderField(datas, param.getOrderField(), param.getOrderType());
                }
                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                List<String> voExcludeFileds = new ArrayList<>(Lists.newArrayList("acots", "asots", "vrt", "vctr",  "adCostPercentage", "viewableImpressions", "vcpm",
                        "adSalePercentage", "adOrderNumPercentage", "orderNumPercentage", "ordersNewToBrand", "unitsOrderedNewToBrand", "salesNewToBrand", "advertisingUnitPrice",
                        "advertisingProductUnitPrice", "advertisingOtherProductUnitPrice", "ordersNewToBrandPercentage", "unitsOrderedNewToBrandPercentage", "salesNewToBrandPercentage"));
                if (!Integer.valueOf(1).equals(param.getIsCompare())) {
                    voExcludeFileds.addAll(Lists.newArrayList("costCompare", "costCompareRate", "impressionsCompare", "impressionsCompareRate",
                            "clicksCompare", "clicksCompareRate", "cpcCompare", "cpcCompareRate",
                            "clickRateCompare", "clickRateCompareRate", "salesConversionRateCompare", "salesConversionRateCompareRate",
                            "acosCompare", "acosCompareRate", "roasCompare", "roasCompareRate",
                            "adOrderNumCompare", "adOrderNumCompareRate", "adSalesCompare", "adSalesCompareRate",
                            "adSaleNumCompare", "adSaleNumCompareRate"));
                }
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(0)", AdCampaignWeekExportVo.class,
                        build.currencyNew(AdCampaignWeekExportVo.class), voExcludeFileds);
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    @Override
    public void campaignAggregateWeekData(GetCampaignWeekReportRequestPb.GetCampaignWeekReportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("cpc campaign weekly hour export request:{}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || (!request.hasShopId() && CollectionUtils.isEmpty(request.getShopIdListList())) || !request.hasEndDateStr() || !request.hasStartDateStr()
                    || (!request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr()))) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                CampaignHourParam param = grpcToParam(request);
                // 店铺状态校验
                List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(param.getPuid(), param.getShopIdList());
                if (CollectionUtils.isEmpty(shopAuths)) {
                    throw new SponsoredBizException("店铺未授权");
                }
                // 取店铺销售额
                BigDecimal shopSalesByDate = dwsSaleProfitShopDayDao.sumShopSaleByDateRange(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate(), MultipleUtils.changeRate(shopAuths));
                List<AdCampaignWeekDayVo> campaignWeekDayVoList = amazonAdCampaignHourReportService.getAggregateWeeklySuperpositionList(shopAuths,request.getPuid().getValue(),param);
                List<AdCampaignHourVo> voList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(campaignWeekDayVoList)) {
                    campaignWeekDayVoList.forEach(e-> voList.addAll(e.getDetails()));
                }
                String currencyCode = MultipleUtils.getCurrency(shopAuths);
                List<AdCampaignWeekExportVo> datas = new ArrayList<>();
                for (AdCampaignHourVo vo : voList) {
                    vo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate));
                    vo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate));
                    AdCampaignWeekExportVo exVo = new AdCampaignWeekExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                List<String> excludeFields = Lists.newArrayList();
                if (!Integer.valueOf(1).equals(param.getIsCompare())) {
                    excludeFields.addAll(Lists.newArrayList("costCompare", "costCompareRate",
                            "impressionsCompare", "impressionsCompareRate",
                            "clicksCompare", "clicksCompareRate",
                            "cpcCompare", "cpcCompareRate",
                            "clickRateCompare", "clickRateCompareRate",
                            "salesConversionRateCompare", "salesConversionRateCompareRate",
                            "acosCompare", "acosCompareRate",
                            "roasCompare", "roasCompareRate",
                            "adOrderNumCompare", "adOrderNumCompareRate",
                            "adSalesCompare", "adSalesCompareRate",
                            "adSaleNumCompare", "adSaleNumCompareRate"));
                }

                // 文件名称
                String fileName = MultipleUtils.getExportShopName(shopAuths) + "_广告活动小时数据" + "_" + param.getStartDate() + "_" + param.getEndDate();
                // excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                String downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas, fileName, AdCampaignWeekExportVo.class, build.currencyNew(AdCampaignWeekExportVo.class), excludeFields);
                List<String> urls = Lists.newLinkedList();
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    private static CampaignHourParam grpcToParam(GetCampaignWeekReportRequestPb.GetCampaignWeekReportRequest request) {
        CampaignHourParam param = new CampaignHourParam();
        param.setCampaignId(request.getCampaignId());
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setStartDate(request.getStartDateStr());
        param.setEndDate(request.getEndDateStr());
        param.setShopIdList(request.getShopIdListList());
        param.setIsCompare(request.getIsCompare().getValue());
        param.setStartDateCompare(request.getStartDateCompareStr());
        param.setEndDateCompare(request.getEndDateCompareStr());
        Optional.of(request.getPageSign()).ifPresent(param::setPageSign);
        // 兼容旧单店铺逻辑
        if(CollectionUtils.isEmpty(param.getShopIdList()) && param.getShopId() != null){
            param.setShopIdList(CollectionUtil.newArrayList(param.getShopId()));
        }
        return param;
    }

    @Override
    public void placementAggregateData(GetPlacementHourReportRequestPb.GetPlacementHourReportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("cpc placement hour export request {}", request);
        NeTargetingDataResponse.Builder builder = NeTargetingDataResponse.newBuilder();
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(request.getShopId().getValue());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr() || !request.hasPredicate()
                || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("request param error");
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        int count = 0;
        String downloadUrl;
        List<String> urls = Lists.newLinkedList();
        PlacementAggregateHourVo param = new PlacementAggregateHourVo();
        AdPageBasicData pageBasicBuilder = AdPageBasicData.newBuilder()
                .setPuid(request.getPuid())
                .setShopId(request.getShopId())
                .setStartDate(request.getStartDateStr())
                .setEndDate(request.getEndDateStr())
                .setEndDateCompare(request.getEndDateCompareStr())
                .setStartDateCompare(request.getStartDateCompareStr())
                .setPageSign(request.getPageSign())
                .setIsCompare(request.getIsCompare())
                .build();
        param.setPredicate(request.getPredicate());
        param.setWeeks(request.getWeeks());
        param.setAdPageBasicData(pageBasicBuilder);
        param.setCampaignSite(request.getCampaignSite());
        int puid = Optional.of(request.getPuid()).map(Int32Value::getValue).orElse(0);
        String pageSign = Optional.of(request.getPageSign()).orElse("");
        List<AdPlacementHourVo> voList = new ArrayList<>();
        //获取暂存的idList
        List<String> idList = cpcPageIdsHandler.getCampaignIdsTemporary(puid, pageSign,
                "", pageBasicBuilder.getShopId().getValue(), new Object[]{param, pageBasicBuilder});
        if(CollectionUtils.isNotEmpty(idList)){
            if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY) {
                voList = amazonAdPlacementHourReportService.getAggregateHourList(shopAuth, puid, idList, param);
            } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.DAILY) {
                voList = amazonAdPlacementHourReportService.getAggregateHourDayList(puid, idList, param, null);
            } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.WEEKLY) {
                voList = amazonAdPlacementHourReportService.getAggregateHourWeekList(puid, idList, param, null);
            } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.MONTHLY) {
                voList = amazonAdPlacementHourReportService.getAggregateHourMonthList(puid, idList, param, null);
            }
        }


        if (CollectionUtils.isEmpty(voList)) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("process.msg.sync.none");
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        List<String> voExcludeFileds = new ArrayList<>();
        if (!Integer.valueOf(1).equals(pageBasicBuilder.getIsCompare().getValue())) {
            voExcludeFileds = Lists.newArrayList("costCompare", "costCompareRate", "impressionsCompare", "impressionsCompareRate",
                    "clicksCompare", "clicksCompareRate", "cpaCompare", "cpaCompareRate", "cpcCompare", "cpcCompareRate", "ctrCompare",
                    "ctrCompareRate", "cvrCompare", "cvrCompareRate", "acosCompare", "acosCompareRate", "roasCompare", "roasCompareRate",
                    "adOrderNumCompare", "adOrderNumCompareRate", "selfAdOrderNumCompare", "selfAdOrderNumCompareRate", "otherAdOrderNumCompare",
                    "otherAdOrderNumCompareRate", "adSalesCompare", "adSalesCompareRate", "adSelfSalesCompare", "adSelfSalesCompareRate",
                    "adOtherSalesCompare", "adOtherSalesCompareRate", "adSaleNumCompare", "adSaleNumCompareRate", "adSelfSaleNumCompare",
                    "adSelfSaleNumCompareRate", "adOtherSaleNumCompare", "adOtherSaleNumCompareRate");
        }
        String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode();
        List<AdPlacementHourExportVo> datas = new ArrayList<>(24);
        for (AdPlacementHourVo vo : voList) {
            AdPlacementHourExportVo exVo = new AdPlacementHourExportVo(currencyCode, vo);
            datas.add(exVo);
        }
        try {
            //excel币种表头渲染
            WriteHandlerBuild build = new WriteHandlerBuild().rate();
            downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas, request.getFileName() + "(" + count++ + ")", AdPlacementHourExportVo.class, build.currencyNew(AdPlacementHourExportVo.class), voExcludeFileds);
            urls.add(downloadUrl);
            urlBuilder.addAllUrls(urls);
            urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
        } catch (Exception e) {
            log.error("puid:{},shopId:{} placement aggregate data export error:{}", request.getPuid(), request.getShopId(), e);
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("process.msg.sync.fail");
        } finally {
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void placementAggregateWeekData(GetPlacementWeekReportRequestPb.GetPlacementWeekReportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr() || !request.hasCampaignId() || !request.hasPredicate()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();

                PlacementHourParam param = new PlacementHourParam();
                param.setCampaignId(request.getCampaignId());
                param.setPredicate(request.getPredicate());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                Optional.of(request.getPageSign()).ifPresent(param::setPageSign);
                List<AdPlacementHourVo> voList = new ArrayList<>();
                List<AdPlacementWeekDayVo> placementWeekDayVoList = amazonAdPlacementHourReportService.
                        getPlacementWeeklySuperpositionList(request.getPuid().getValue(), param);
                if (CollectionUtils.isNotEmpty(placementWeekDayVoList)) {
                    placementWeekDayVoList.forEach(e->{
                        voList.addAll(e.getDetails());
                    });
                }
                ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdPlacementWeekExportVo> datas = new ArrayList<>();
                for (AdPlacementHourVo vo : voList) {
                    AdPlacementWeekExportVo exVo = new AdPlacementWeekExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdPlacementWeekExportVo.class,
                        build.currencyNew(AdPlacementWeekExportVo.class));
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("puid:{},shopId:{} placement aggregate week data export error:{}", request.getPuid(), request.getShopId(), e);
            responseObserver.onError(e);
        }
    }


    @Override
    public void productWeekAggregateData(GetProductWeekReportAggregateDataRequestPb.GetProductWeekReportAggregateDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()
                    || (request.hasIsCompare() && request.getIsCompare() == 1 && (!request.hasStartDateCompare() || !request.hasEndDateCompare()))) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();

                ProductHourParam param = new ProductHourParam();
                param.setAdId(request.getAdId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                List<String> temporaryIds = cpcPageIdsHandler.getCampaignIdsTemporary(request.getPuid().getValue(),
                        request.getPageSign(), "", param.getShopId(), new ProductHourParam[]{param});
                param.setAdIds(temporaryIds);
                param.setIsCompare(request.getIsCompare());
                param.setStartDateCompare(request.getStartDateCompare());
                param.setEndDateCompare(request.getEndDateCompare());

                List<AdProductHourVo> voList = new ArrayList<>();
                List<AdProductWeekDayVo> productWeekDayVoList = amazonAdProductHourReportService.getWeeklySuperpositionListAll(request.getPuid().getValue(),param);
                if (CollectionUtils.isNotEmpty(productWeekDayVoList)) {
                    productWeekDayVoList.forEach(e->{
                        voList.addAll(e.getDetails());
                    });
                }
                ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdProductWeekExportVo> datas = new ArrayList<>();
                for (AdProductHourVo vo : voList) {
                    AdProductWeekExportVo exVo = new AdProductWeekExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                List<String> voExcludeFileds = new ArrayList<>();
                if (!Integer.valueOf(1).equals(param.getIsCompare())) {
                    voExcludeFileds = Lists.newArrayList("costCompare", "costCompareRate",
                            "impressionsCompare", "impressionsCompareRate",
                            "clicksCompare", "clicksCompareRate",
                            "adOrderNumCompare", "adOrderNumCompareRate",
                            "adSalesCompare", "adSalesCompareRate",
                            "adSaleNumCompare", "adSaleNumCompareRate",
                            "cpcCompare", "cpcCompareRate",
                            "clickRateCompare", "clickRateCompareRate",
                            "salesConversionRateCompare", "salesConversionRateCompareRate",
                            "acosCompare", "acosCompareRate",
                            "roasCompare", "roasCompareRate"
                    );
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdProductWeekExportVo.class,
                        build.currencyNew(AdProductWeekExportVo.class), voExcludeFileds);
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }


    @Override
    public void productHourAggregateData(GetProductHourReportAggregateDataRequestPb.GetProductHourReportAggregateDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()
                    || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr()))) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                List<AdProductHourVo> voList = new ArrayList<>();
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();

                ProductHourParam param = new ProductHourParam();
                param.setAdId(request.getAdId());
                param.setWeeks(request.getWeeks());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                List<String> temporaryIds = cpcPageIdsHandler.getCampaignIdsTemporary(request.getPuid().getValue(),
                        request.getPageSign(), "", param.getShopId(), new ProductHourParam[]{param});
                param.setAdIds(temporaryIds);
                param.setEndDateCompare(request.getEndDateCompareStr());
                param.setStartDateCompare(request.getStartDateCompareStr());
                if (request.hasIsCompare()) {
                    param.setIsCompare(request.getIsCompare().getValue());
                }

                if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY && "SP".equalsIgnoreCase(request.getAdType())) {
                    voList = amazonAdProductHourReportService.getHourListAll(request.getPuid().getValue(), param);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.DAILY) {
                    voList = amazonAdProductHourReportService.getDailyListAll(param.getPuid(), request.getAdType(), param, null);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.WEEKLY) {
                    voList = amazonAdProductHourReportService.getWeeklyListAll(param.getPuid(), request.getAdType(), param, null);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.MONTHLY) {
                    voList = amazonAdProductHourReportService.getMonthlyListAll(param.getPuid(), request.getAdType(), param, null);
                }

                ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
                List<String> voExcludeFileds = new ArrayList<>();
                if (!Integer.valueOf(1).equals(param.getIsCompare())) {
                    voExcludeFileds = Lists.newArrayList(
                            "costCompare", "costCompareRate",
                            "impressionsCompare", "impressionsCompareRate",
                            "clicksCompare", "clicksCompareRate",
                            "adOrderNumCompare", "adOrderNumCompareRate",
                            "adSalesCompare", "adSalesCompareRate",
                            "adSaleNumCompare", "adSaleNumCompareRate",
                            "cpcCompare", "cpcCompareRate",
                            "clickRateCompare", "clickRateCompareRate",
                            "salesConversionRateCompare", "salesConversionRateCompareRate",
                            "acosCompare", "acosCompareRate",
                            "roasCompare", "roasCompareRate"
                    );
                }
                //SB不需要销量字段
                if ("SB".equalsIgnoreCase(request.getAdType())) {
                    voExcludeFileds.addAll(Lists.newArrayList("adSaleNum", "adSelfSaleNum", "adOtherSaleNum"));
                }
                //SB不需要销量字段
                if ("SD".equalsIgnoreCase(request.getAdType())) {
                    voExcludeFileds.addAll(Lists.newArrayList("adSelfSaleNum", "adOtherSaleNum"));
                }
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdProductHourWithCompareExportVo> datas = new ArrayList<>(24);
                for (AdProductHourVo vo : voList) {
                    AdProductHourWithCompareExportVo exVo = new AdProductHourWithCompareExportVo(currencyCode, vo);
                    datas.add(exVo);
                }
                //修改表头
                if (request.getDateModel() != ReportDateModelPb.ReportDateModel.HOURLY) {
                    Field filed = AdProductHourWithCompareExportVo.class.getDeclaredField("label");
                    filed.setAccessible(true);
                    ExcelProperty annotation = filed.getAnnotation(ExcelProperty.class);
                    InvocationHandler invocationHandler = Proxy.getInvocationHandler(annotation);
                    Field memberValues = invocationHandler.getClass().getDeclaredField("memberValues");
                    memberValues.setAccessible(true);
                    Map<String, Object> map = (Map<String, Object>) memberValues.get(invocationHandler);
                    String[] valueArr = {"日期"};
                    map.put("value", valueArr);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdProductHourWithCompareExportVo.class,
                        build.currencyNew(AdProductHourWithCompareExportVo.class), voExcludeFileds);
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }


    @Override
    public void productHourOfPlacementAggregateData(GetProductHourReportOfPlacementAggregateDataRequestPb.GetProductHourReportOfPlacementAggregateDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr() || StringUtils.isBlank(request.getPageSign())) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();

                ProductHourParam param = new ProductHourParam();
                param.setAdId(request.getAdId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                List<String> temporaryIds = cpcPageIdsHandler.getCampaignIdsTemporary(request.getPuid().getValue(),
                        request.getPageSign(), "", param.getShopId(), new ProductHourParam[]{param});
                param.setAdIds(temporaryIds);
                List<AdProductHourVo> voList = new ArrayList<>();
                List<AdProductHourOfPlacementVo> adProductHourOfPlacementVos = amazonAdProductHourReportService.getListOfPlacementAll(request.getPuid().getValue(), param);
                if (CollectionUtils.isNotEmpty(adProductHourOfPlacementVos)) {
                    adProductHourOfPlacementVos.forEach(e->{
                        voList.addAll(e.getDetails());
                    });
                }
                ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdProductHourOfPlacementExportVo> datas = new ArrayList<>(24);
                for (AdProductHourVo vo : voList) {
                    AdProductHourOfPlacementExportVo exVo = new AdProductHourOfPlacementExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdProductHourOfPlacementExportVo.class,
                        build.currencyNew(AdProductHourOfPlacementExportVo.class));
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }


    @Override
    public void targetHourAggregateData(GetTargetHourReportAggregateDataRequestPb.GetTargetHourReportAggregateDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || (!request.hasShopId() && CollectionUtils.isEmpty(request.getShopIdListList()))
                    || !request.hasEndDateStr() || !request.hasStartDateStr() || !request.hasPageSign()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();
                TargetHourParam param = new TargetHourParam();
                param.setTargetId(request.getTargetId());
                param.setWeeks(request.getWeeks());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setShopIdList(request.getShopIdListList());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setEndDateCompare(request.getEndDateCompareStr());
                param.setStartDateCompare(request.getStartDateCompareStr());
                List<String> temporaryIds = cpcPageIdsHandler.getCampaignIdsTemporary(request.getPuid().getValue(),
                        request.getPageSign(), "", param.getShopId(), new TargetHourParam[]{param});
                param.setTargetIds(temporaryIds);
                if (request.hasIsCompare()) {
                    param.setIsCompare(request.getIsCompare().getValue());
                }
                List<AdKeywordAndTargetHourVo> voList = new ArrayList<>();

                // 兼容旧单店铺逻辑
                if(CollectionUtils.isEmpty(param.getShopIdList()) && param.getShopId() != null){
                    param.setShopIdList(CollectionUtil.newArrayList(param.getShopId()));
                }
                // 店铺状态校验
                List<ShopAuth> shopAuths = shopAuthDao.listValidShopByIds(param.getPuid(), param.getShopIdList());
                if (CollectionUtils.isEmpty(shopAuths)) {
                    builder.setCode(Int32Value.of(Result.ERROR));
                    builder.setMsg("no shop found");
                    responseObserver.onNext(builder.build());
                    responseObserver.onCompleted();
                    return;
                }
                param.setShopIdList(StreamUtil.toListDistinct(shopAuths, ShopAuth::getId));
                // 多店鋪,超过限制投放数量返回，防止doris cpu过高
                if((shopAuths.size() > 1 && temporaryIds.size() >= adManageLimitConfig.getTargetHourLimit()) || shopAuths.size() > 40){
                    builder.setCode(Int32Value.of(Result.ERROR));
                    if(shopAuths.size() > 40){
                        builder.setMsg("当前店铺数量超过40个，请减少后重新查询!");
                    }else{
                        builder.setMsg("当前所选数据量过大，请减少店铺查询!");
                    }
                    responseObserver.onNext(builder.build());
                    responseObserver.onCompleted();
                    return;
                }
                if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY) {
                    if ("SP".equalsIgnoreCase(request.getType())) {
                        voList = amazonAdTargetHourReportService.getAllList(param.getPuid(), shopAuths, param);
                    } else if ("SB".equalsIgnoreCase(request.getType())) {
                        voList = amazonAdTargetHourReportService.getSbHourAllList(param.getPuid(), shopAuths, param);
                    } else if ("SD".equalsIgnoreCase(request.getType())) {
                        voList = amazonAdTargetHourReportService.getSdHourAllList(param.getPuid(), shopAuths, param);
                    }
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.DAILY) {
                    voList = amazonAdTargetHourReportService.getDailyAllList(param.getPuid(), shopAuths, request.getAdType(), param, null);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.WEEKLY) {
                    voList = amazonAdTargetHourReportService.getWeeklyAllList(param.getPuid(), shopAuths, request.getAdType(), param, null);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.MONTHLY) {
                    voList = amazonAdTargetHourReportService.getMonthlyAllList(param.getPuid(), shopAuths, request.getAdType(), param, null);
                }
                //默认按日期升序
                PageUtil.sortedByOrderField(voList, "date", null);

                List<String> voExcludeFileds = new ArrayList<>();
                if (!Integer.valueOf(1).equals(param.getIsCompare())) {
                    voExcludeFileds = Lists.newArrayList("costCompare", "costCompareRate", "impressionsCompare", "costCompare", "impressionsCompareRate",
                            "clicksCompareRate", "adOrderNumCompare", "adOrderNumCompareRate", "adSalesCompare",
                            "adSalesCompareRate", "adSaleNumCompare", "adSaleNumCompareRate", "clicksCompare",
                            "cpcCompare", "cpcCompareRate", "clickRateCompare", "clickRateCompareRate",
                            "salesConversionRateCompare", "salesConversionRateCompareRate", "acosCompare", "acosCompareRate",
                            "roasCompare", "roasCompareRate");

                }
                // SP过滤字段
                if ("SP".equalsIgnoreCase(request.getType())) {
                    voExcludeFileds.addAll(Lists.newArrayList( "adCostPercentage","acots","asots","adOrderNumPercentage","adSalePercentage","orderNumPercentage","viewableImpressions","vrt",
                            "vcpm","vctr","ordersNewToBrand","unitsOrderedNewToBrand","salesNewToBrand","advertisingUnitPrice","advertisingProductUnitPrice","advertisingOtherProductUnitPrice","" +
                                    "ordersNewToBrandPercentage","unitsOrderedNewToBrandPercentage","salesNewToBrandPercentage"));
                }
                //SB不需要销量字段
                if ("SB".equalsIgnoreCase(request.getType())) {
                    voExcludeFileds.addAll(Lists.newArrayList( "vcpm","adSelfSaleNum","adOtherSaleNum"));
                }
                //SB不需要销量字段
                if ("SD".equalsIgnoreCase(request.getType())) {
                    voExcludeFileds.addAll(Lists.newArrayList( "adSelfSaleNum","adOtherSaleNum"));
                }
                if (request.getDateModel() != ReportDateModelPb.ReportDateModel.HOURLY) {
                    voExcludeFileds.addAll(Lists.newArrayList("acots","asots", "vcpm","viewableImpressions","vrt","vctr","ordersNewToBrand",
                            "unitsOrderedNewToBrand","salesNewToBrand","advertisingUnitPrice","advertisingProductUnitPrice","advertisingOtherProductUnitPrice",
                            "ordersNewToBrandPercentage","unitsOrderedNewToBrandPercentage","salesNewToBrandPercentage",
                            "adCostPercentage","adSalePercentage","adOrderNumPercentage","orderNumPercentage"));
                }
                String currencyCode = MultipleUtils.getCurrency(shopAuths);
                List<AdKeywordTargetHourWithCompareExportVo> datas = new ArrayList<>(24);
                for (AdKeywordAndTargetHourVo vo : voList) {
                    AdKeywordTargetHourWithCompareExportVo exVo = new AdKeywordTargetHourWithCompareExportVo(currencyCode, vo);
                    datas.add(exVo);
                }
                //修改表头
                if (request.getDateModel() != ReportDateModelPb.ReportDateModel.HOURLY) {
                    Field filed = AdKeywordTargetHourWithCompareExportVo.class.getDeclaredField("label");
                    filed.setAccessible(true);
                    ExcelProperty annotation = filed.getAnnotation(ExcelProperty.class);
                    InvocationHandler invocationHandler = Proxy.getInvocationHandler(annotation);
                    Field memberValues = invocationHandler.getClass().getDeclaredField("memberValues");
                    memberValues.setAccessible(true);
                    Map<String, Object> map = (Map<String, Object>) memberValues.get(invocationHandler);
                    String[] valueArr = {"日期"};
                    map.put("value", valueArr);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdKeywordTargetHourWithCompareExportVo.class,
                        build.currencyNew(AdKeywordTargetHourWithCompareExportVo.class), voExcludeFileds);
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }


    @Override
    public void targetHourOfPlacementAggregateData(GetTargetHourReportOfPlacementAggregateDataRequestPb.GetTargetHourReportOfPlacementAggregateDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || (!request.hasShopId() && CollectionUtils.isEmpty(request.getShopIdListList())) || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();
                TargetHourParam param = new TargetHourParam();
                param.setTargetId(request.getTargetId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setShopIdList(request.getShopIdListList());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                List<String> temporaryIds = cpcPageIdsHandler.getCampaignIdsTemporary(request.getPuid().getValue(),
                        request.getPageSign(), "",  param.getShopId(), new TargetHourParam[]{param});
                param.setTargetIds(temporaryIds);
                // 店铺状态校验
                List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(param.getPuid(), param.getShopIdList());
                if (CollectionUtils.isEmpty(shopAuths)) {
                    throw new SponsoredBizException("no shop found");
                }
                List<AdKeywordAndTargetHourVo> voList =
                        amazonAdTargetHourReportService.getDetailListOfPlacementAll(request.getPuid().getValue(), shopAuths, param);
                String currencyCode = MultipleUtils.getCurrency(shopAuths);
                List<AdKeywordTargetTargetHourOfPlacementExportVo> datas = new ArrayList<>(24);
                for (AdKeywordAndTargetHourVo vo : voList) {
                    AdKeywordTargetTargetHourOfPlacementExportVo exVo = new AdKeywordTargetTargetHourOfPlacementExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdKeywordTargetTargetHourOfPlacementExportVo.class,
                        build.currencyNew(AdKeywordTargetTargetHourOfPlacementExportVo.class));
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }


    @Override
    public void keywordHourAggregateData(GetKeywordHourReportAggregateDataRequestPb.GetKeywordHourReportAggregateDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || (!request.hasShopId() && CollectionUtils.isEmpty(request.getShopIdListList())) || !request.hasEndDateStr() || !request.hasStartDateStr()
                    || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr()))) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();
                KeywordHourParam param = new KeywordHourParam();
                param.setKeywordId(request.getKeywordId());
                param.setWeeks(request.getWeeks());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setShopIdList(request.getShopIdListList());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setEndDateCompare(request.getEndDateCompareStr());
                param.setStartDateCompare(request.getStartDateCompareStr());
                List<String> temporaryIds = cpcPageIdsHandler.getCampaignIdsTemporary(request.getPuid().getValue(),
                        request.getPageSign(), "", param.getShopId(), new KeywordHourParam[]{param});
                param.setKeywordIds(temporaryIds);
                if (request.hasIsCompare()) {
                    param.setIsCompare(request.getIsCompare().getValue());
                }
                param.setType(request.getType());
                List<AdKeywordAndTargetHourVo> voList = new ArrayList<>();

                // 兼容旧单店铺逻辑
                if(CollectionUtils.isEmpty(param.getShopIdList()) && param.getShopId() != null){
                    param.setShopIdList(CollectionUtil.newArrayList(param.getShopId()));
                }
                // 店铺状态校验
                List<ShopAuth> shopAuths = shopAuthDao.listValidShopByIds(param.getPuid(), param.getShopIdList());
                if (CollectionUtils.isEmpty(shopAuths)) {
                    builder.setCode(Int32Value.of(Result.ERROR));
                    builder.setMsg("no shop found");
                    responseObserver.onNext(builder.build());
                    responseObserver.onCompleted();
                    return;
                }
                param.setShopIdList(StreamUtil.toListDistinct(shopAuths, ShopAuth::getId));
                // 多店鋪,超过限制投放数量返回，防止doris cpu过高
                if((shopAuths.size() > 1 && temporaryIds.size() >= adManageLimitConfig.getTargetHourLimit()) || shopAuths.size() > 40){
                    builder.setCode(Int32Value.of(Result.ERROR));
                    if(shopAuths.size() > 40){
                        builder.setMsg("当前店铺数量超过40个，请减少后重新查询!");
                    }else{
                        builder.setMsg("当前所选数据量过大，请减少店铺查询!");
                    }
                    responseObserver.onNext(builder.build());
                    responseObserver.onCompleted();
                    return;
                }
                if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY) {
                    if ("SP".equalsIgnoreCase(request.getAdType())) {
                        voList = amazonAdKeywordHourReportService.getHourListAll(param.getPuid(), shopAuths, param);
                    } else {
                        voList = amazonAdKeywordHourReportService.getSbHourListAll(param.getPuid(), shopAuths, param);
                    }
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.DAILY) {
                    voList = amazonAdKeywordHourReportService.getDailyListAll(param.getPuid(), shopAuths, request.getAdType(), param, null);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.WEEKLY) {
                    voList = amazonAdKeywordHourReportService.getWeeklyAllList(param.getPuid(), shopAuths, request.getAdType(), param, null);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.MONTHLY) {
                    voList = amazonAdKeywordHourReportService.getMonthlyAllList(param.getPuid(), shopAuths, request.getAdType(), param, null);
                }
                //默认按日期升序
                PageUtil.sortedByOrderField(voList, "date", null);
                List<String> voExcludeFileds = new ArrayList<>();
                if (!Integer.valueOf(1).equals(param.getIsCompare())) {
                    voExcludeFileds = Lists.newArrayList("costCompare", "costCompareRate", "impressionsCompare", "costCompare", "impressionsCompareRate",
                            "clicksCompareRate", "adOrderNumCompare", "adOrderNumCompareRate", "adSalesCompare",
                            "adSalesCompareRate", "adSaleNumCompare", "adSaleNumCompareRate", "clicksCompare");
                    voExcludeFileds.addAll(Lists.newArrayList("cpcCompare", "cpcCompareRate",
                            "clickRateCompare", "clickRateCompareRate",
                            "salesConversionRateCompare", "salesConversionRateCompareRate",
                            "acosCompare", "acosCompareRate",
                            "roasCompare", "roasCompareRate"));
                }
                //SP不需要销量字段
                if ("SP".equalsIgnoreCase(request.getType())) {
                    voExcludeFileds.addAll(Lists.newArrayList( "adCostPercentage","acots","asots","adOrderNumPercentage","adSalePercentage","orderNumPercentage","viewableImpressions","vrt",
                            "vcpm","vctr","ordersNewToBrand","unitsOrderedNewToBrand","salesNewToBrand","advertisingUnitPrice","advertisingProductUnitPrice","advertisingOtherProductUnitPrice","" +
                                    "ordersNewToBrandPercentage","unitsOrderedNewToBrandPercentage","salesNewToBrandPercentage"));
                }
                //SB不需要销量字段
                if ("SB".equalsIgnoreCase(request.getType())) {
                    voExcludeFileds.addAll(Lists.newArrayList( "vcpm"));
                }
                //SD
                if ("SD".equalsIgnoreCase(request.getType())) {

                }
                if (request.getDateModel() != ReportDateModelPb.ReportDateModel.HOURLY) {
                    voExcludeFileds.addAll(Lists.newArrayList("acots","asots","viewableImpressions","vrt","vctr","ordersNewToBrand",
                            "unitsOrderedNewToBrand","salesNewToBrand","advertisingUnitPrice","advertisingProductUnitPrice","advertisingOtherProductUnitPrice",
                            "ordersNewToBrandPercentage","unitsOrderedNewToBrandPercentage","salesNewToBrandPercentage",
                            "adCostPercentage","adSalePercentage","adOrderNumPercentage","orderNumPercentage"));
                }
                String currencyCode = MultipleUtils.getCurrency(shopAuths);
                List<AdKeywordTargetHourWithCompareExportVo> datas = new ArrayList<>(24);
                for (AdKeywordAndTargetHourVo vo : voList) {
                    AdKeywordTargetHourWithCompareExportVo exVo = new AdKeywordTargetHourWithCompareExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                if (request.getDateModel() != ReportDateModelPb.ReportDateModel.HOURLY) {
                    Field filed = AdKeywordTargetHourWithCompareExportVo.class.getDeclaredField("label");
                    filed.setAccessible(true);
                    ExcelProperty annotation = filed.getAnnotation(ExcelProperty.class);
                    InvocationHandler invocationHandler = Proxy.getInvocationHandler(annotation);
                    Field memberValues = invocationHandler.getClass().getDeclaredField("memberValues");
                    memberValues.setAccessible(true);
                    Map<String, Object> map = (Map<String, Object>) memberValues.get(invocationHandler);
                    String[] valueArr = {"日期"};
                    map.put("value", valueArr);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdKeywordTargetHourWithCompareExportVo.class,
                        build.currencyNew(AdKeywordTargetHourWithCompareExportVo.class), voExcludeFileds);
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }


    @Override
    public void keywordHourOfPlacementAggregateData(GetKeywordHourReportOfPlacementAggregateDataRequestPb.GetKeywordHourReportOfPlacementAggregateDataRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || (!request.hasShopId() && CollectionUtils.isEmpty(request.getShopIdListList())) || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();
                KeywordHourParam param = new KeywordHourParam();
                param.setKeywordId(request.getKeywordId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setShopIdList(request.getShopIdListList());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                List<String> temporaryIds = cpcPageIdsHandler.getCampaignIdsTemporary(request.getPuid().getValue(),
                        request.getPageSign(), "", param.getShopId(), new KeywordHourParam[]{param});
                param.setKeywordIds(temporaryIds);
                // 店铺状态校验
                List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(param.getPuid(), param.getShopIdList());
                if (CollectionUtils.isEmpty(shopAuths)) {
                    throw new SponsoredBizException("no shop found");
                }
                List<AdKeywordAndTargetHourVo> voList =
                        amazonAdKeywordHourReportService.getDetailListOfPlacementAll(request.getPuid().getValue(), shopAuths, param);
                String currencyCode = MultipleUtils.getCurrency(shopAuths);
                List<AdKeywordTargetTargetHourOfPlacementExportVo> datas = new ArrayList<>(24);
                for (AdKeywordAndTargetHourVo vo : voList) {
                    AdKeywordTargetTargetHourOfPlacementExportVo exVo =
                            new AdKeywordTargetTargetHourOfPlacementExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdKeywordTargetTargetHourOfPlacementExportVo.class,
                        build.currencyNew(AdKeywordTargetTargetHourOfPlacementExportVo.class));
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    @Override
    public void groupAggregateData(GetGroupHourReportRequestPb.GetGroupHourReportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("cpc placement hour export request {}", request);
        NeTargetingDataResponse.Builder builder = NeTargetingDataResponse.newBuilder();
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(request.getShopId().getValue());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr() || !request.hasAdGroupId()
                || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("request param error");
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        int count = 0;
        String downloadUrl;
        List<String> urls = Lists.newLinkedList();
        GroupAggregateHourVo param = new GroupAggregateHourVo();
        AdPageBasicData pageBasicBuilder = AdPageBasicData.newBuilder()
                .setPuid(request.getPuid())
                .setShopId(request.getShopId())
                .setStartDate(request.getStartDateStr())
                .setEndDate(request.getEndDateStr())
                .setEndDateCompare(request.getEndDateCompareStr())
                .setStartDateCompare(request.getStartDateCompareStr())
                .setPageSign(request.getPageSign())
                .setIsCompare(request.getIsCompare())
                .setType(request.getType())
                .build();
        param.setAdGroupId(request.getAdGroupId());
        param.setWeeks(request.getWeeks());
        param.setAdPageBasicData(pageBasicBuilder);
        int puid = Optional.of(request.getPuid()).map(Int32Value::getValue).orElse(0);
        String pageSign = Optional.of(request.getPageSign()).orElse("");
        List<AdGroupHourVo> voList = new ArrayList<>();
        //获取暂存的idList
        List<String> idList = cpcPageIdsHandler.getCampaignIdsTemporary(puid, pageSign,
                "", pageBasicBuilder.getShopId().getValue(), new Object[]{param, pageBasicBuilder});
        if(CollectionUtils.isNotEmpty(idList)){
            if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY) {
                voList = amazonAdGroupHourReportService.getAggregateHourList(shopAuth, puid, idList, param);
            } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.DAILY) {
                voList = amazonAdGroupHourReportService.getAggregateHourDayList(puid, idList, param, null);
            } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.WEEKLY) {
                voList = amazonAdGroupHourReportService.getAggregateHourWeekList(puid, idList, param, null);
            } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.MONTHLY) {
                voList = amazonAdGroupHourReportService.getAggregateHourMonthList(puid, idList, param, null);
            }
        }

        if (CollectionUtils.isEmpty(voList)) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("process.msg.sync.none");
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        List<String> voExcludeFileds = new ArrayList<>();
        if (!Integer.valueOf(1).equals(pageBasicBuilder.getIsCompare().getValue())) {
            voExcludeFileds = Lists.newArrayList("costCompare", "costCompareRate", "impressionsCompare", "impressionsCompareRate",
                    "clicksCompare", "clicksCompareRate", "cpaCompare", "cpaCompareRate", "cpcCompare", "cpcCompareRate", "ctrCompare",
                    "ctrCompareRate", "cvrCompare", "cvrCompareRate", "acosCompare", "acosCompareRate", "roasCompare", "roasCompareRate",
                    "adOrderNumCompare", "adOrderNumCompareRate", "selfAdOrderNumCompare", "selfAdOrderNumCompareRate", "otherAdOrderNumCompare",
                    "otherAdOrderNumCompareRate", "adSalesCompare", "adSalesCompareRate", "adSelfSalesCompare", "adSelfSalesCompareRate",
                    "adOtherSalesCompare", "adOtherSalesCompareRate", "adSaleNumCompare", "adSaleNumCompareRate", "adSelfSaleNumCompare",
                    "adSelfSaleNumCompareRate", "adOtherSaleNumCompare", "adOtherSaleNumCompareRate", "acotsCompare", "acotsCompareRate",
                    "asotsCompare", "asotsCompareRate");
        }

        if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY) {
            if (Constants.SP.equalsIgnoreCase(request.getType())) {
                voExcludeFileds.addAll(Lists.newArrayList(
                        "advertisingOtherProductUnitPrice", "ordersNewToBrandPercentage", "unitsOrderedNewToBrandPercentage",
                        "salesNewToBrandPercentage", "adCostPercentage", "acots", "acotsCompare", "acotsCompareRate",
                        "asots", "asotsCompare", "asotsCompareRate", "adSalePercentage", "adOrderNumPercentage", "orderNumPercentage"
                ));
                //去掉
                voExcludeFileds.addAll(Lists.newArrayList("salesNewToBrandPercentage","unitsOrderedNewToBrandPercentage","ordersNewToBrandPercentage",
                        "advertisingOtherProductUnitPrice","advertisingProductUnitPrice","advertisingUnitPrice","salesNewToBrand",
                        "unitsOrderedNewToBrand","ordersNewToBrand","vctr","vCtr","vcpm","vrt","viewableImpressions"
                ));
            } else {
                if (Constants.SB.equalsIgnoreCase(request.getType()) || Constants.SD.equalsIgnoreCase(request.getType())) {
                    voExcludeFileds.addAll(Lists.newArrayList(
                            "adSelfSaleNum", "adSelfSaleNumCompareRate", "adOtherSaleNum",
                            "adOtherSaleNumCompare", "adOtherSaleNumCompareRate"
                    ));
                }
                if (Constants.SB.equalsIgnoreCase(request.getType())) {
                    voExcludeFileds.add("vcpm");
                }

            }
        } else {
            voExcludeFileds.addAll(Lists.newArrayList(
                    "advertisingOtherProductUnitPrice", "ordersNewToBrandPercentage", "unitsOrderedNewToBrandPercentage",
                    "salesNewToBrandPercentage", "adCostPercentage", "acots", "acotsCompare", "acotsCompareRate",
                    "asots", "asotsCompare", "asotsCompareRate", "adSalePercentage", "adOrderNumPercentage", "orderNumPercentage"
            ));
            //去掉
            voExcludeFileds.addAll(Lists.newArrayList("salesNewToBrandPercentage","unitsOrderedNewToBrandPercentage","ordersNewToBrandPercentage",
                    "advertisingOtherProductUnitPrice","advertisingProductUnitPrice","advertisingUnitPrice","salesNewToBrand",
                    "unitsOrderedNewToBrand","ordersNewToBrand","vctr","vCtr","vcpm","vrt","viewableImpressions"
            ));
        }


        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(shopAuth.getId(), request.getStartDateStr().replace("-",""), request.getEndDateStr().replace("-",""));
        String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode();
        List<AdReportHourlyExportVo> datas = new ArrayList<>(24);
        for (AdGroupHourVo vo : voList) {
            if (Constants.SB.equalsIgnoreCase(request.getType()) || Constants.SD.equalsIgnoreCase(request.getType())) {
                vo.setAdOtherSaleNum(0);
                vo.setAdOtherSaleNumCompare(0);
                vo.setAdOtherSaleNumCompareRate(BigDecimal.ZERO);
            }
            vo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate));
            vo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate));
            AdReportHourlyExportVo exVo = new AdReportHourlyExportVo(currencyCode, vo);
            datas.add(exVo);
        }
        try {
            //excel币种表头渲染
            WriteHandlerBuild build = new WriteHandlerBuild().rate();
            downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas, request.getFileName() + "(" + count++ + ")", AdReportHourlyExportVo.class, build.currencyNew(AdReportHourlyExportVo.class), voExcludeFileds);
            urls.add(downloadUrl);
            urlBuilder.addAllUrls(urls);
            urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
        } catch (Exception e) {
            log.error("puid:{},shopId:{} group aggregate data export error:{}", request.getPuid(), request.getShopId(), e);
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("process.msg.sync.fail");
        } finally {
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void groupAggregateWeekData(GetGroupWeekReportRequestPb.GetGroupWeekReportRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            UrlResponse.Builder builder = UrlResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr() || !request.hasCampaignId() || !request.hasAdGroupId()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                int count = 0;
                String downloadUrl;
                List<String> urls = Lists.newLinkedList();

                GroupHourParam param = new GroupHourParam();
                param.setCampaignId(request.getCampaignId());
                param.setAdGroupId(request.getAdGroupId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                Optional.of(request.getPageSign()).ifPresent(param::setPageSign);
                List<AdGroupHourVo> voList = new ArrayList<>();
                List<AdGroupWeekDayVo> groupWeekDayVoList = amazonAdGroupHourReportService.
                        getGroupWeeklySuperpositionList(request.getPuid().getValue(), param);
                if (CollectionUtils.isNotEmpty(groupWeekDayVoList)) {
                    groupWeekDayVoList.forEach(e->{
                        voList.addAll(e.getDetails());
                    });
                }
                ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode();
                List<AdReportWeekExportVo> datas = new ArrayList<>();
                for (AdGroupHourVo vo : voList) {
                    AdReportWeekExportVo exVo = new AdReportWeekExportVo(currencyCode, vo);
                    datas.add(exVo);
                }

                //excel币种表头渲染
                WriteHandlerBuild build = new WriteHandlerBuild().rate();
                downloadUrl = excelService.easyExcelHandlerExport(request.getPuid().getValue(), datas,
                        request.getFileName() + "(" + count++ + ")", AdReportWeekExportVo.class,
                        build.currencyNew(AdReportWeekExportVo.class));
                urls.add(downloadUrl);
                builder.addAllUrls(urls);
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("puid:{},shopId:{} group aggregate week data export error:{}", request.getPuid(), request.getShopId(), e);
            responseObserver.onError(e);
        }
    }
}
