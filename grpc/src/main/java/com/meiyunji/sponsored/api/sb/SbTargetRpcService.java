package com.meiyunji.sponsored.api.sb;

import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.sb.entity.targetingRecommendation.CategoryRecommendationResults;
import com.google.protobuf.BoolValue;
import com.google.protobuf.Int32Value;
import com.google.protobuf.Int64Value;
import com.google.protobuf.ProtocolStringList;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.adCommon.AsyncAddTargetingRequest;
import com.meiyunji.sponsored.rpc.adCommon.BatchTargetSuggestedBidRequest;
import com.meiyunji.sponsored.rpc.adCommon.TargetSuggestedBidVo;
import com.meiyunji.sponsored.rpc.sb.target.*;
import com.meiyunji.sponsored.rpc.sb.target.SuggestCategoryBrandVo;
import com.meiyunji.sponsored.rpc.sb.target.SuggestedTargetVo;
import com.meiyunji.sponsored.rpc.sb.target.TargetingVo;
import com.meiyunji.sponsored.rpc.vo.AsyncAddTargetingRpcVo;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetObjectTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskConstant;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskMatchTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskTypeEnum;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetTaskDto;
import com.meiyunji.sponsored.service.cpc.po.TargetTypeEnum;
import com.meiyunji.sponsored.service.cpc.service2.IAdTargetTaskService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbTargetServiceImpl;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.enums.SBAdFormatEnum;
import com.meiyunji.sponsored.service.enums.SBCampaignCostTypeEnum;
import com.meiyunji.sponsored.service.enums.SBCampaignGoalEnum;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@GRpcService
@Slf4j
public class SbTargetRpcService extends RPCCpcSbTargetServiceGrpc.RPCCpcSbTargetServiceImplBase {

    @Autowired
    private CpcSbTargetServiceImpl cpcSbTargetService;
    @Autowired
    private IAdTargetTaskService adTargetTaskService;

    @Override
    public void createTargeting(CreateTargetRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        List<TargetingVo> targetsList = request.getTargetsList();
        if (!request.hasShopId() || StringUtils.isBlank(request.getCampaignId()) || CollectionUtils.isEmpty(targetsList)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            AddSbTargetingVo addSbTargetingVo = new AddSbTargetingVo();
            addSbTargetingVo.setPuid(request.getPuid().getValue());
            addSbTargetingVo.setShopId(request.getShopId().getValue());
            addSbTargetingVo.setUid(request.getUid().getValue());
            addSbTargetingVo.setCampaignId(request.getCampaignId());
            addSbTargetingVo.setGroupId(request.getGroupId());
            addSbTargetingVo.setIp(request.getIp());
            if (StringUtils.isNotBlank(request.getAdFormat())) {
                addSbTargetingVo.setAdFormat(request.getAdFormat());
            }

            List<com.meiyunji.sponsored.service.cpc.vo.TargetingVo> voList = new ArrayList<>(targetsList.size());
            com.meiyunji.sponsored.service.cpc.vo.TargetingVo targetingVo;
            for (TargetingVo rpcVo : targetsList) {
                targetingVo = new com.meiyunji.sponsored.service.cpc.vo.TargetingVo();
                if (StringUtils.isNotBlank(rpcVo.getType())) {
                    targetingVo.setType(rpcVo.getType());
                }
                if (StringUtils.isNotBlank(rpcVo.getAsin())) {
                    targetingVo.setAsin(rpcVo.getAsin());
                }
                if (StringUtils.isNotBlank(rpcVo.getTitle())) {
                    targetingVo.setTitle(rpcVo.getTitle());
                }
                if (StringUtils.isNotBlank(rpcVo.getImgUrl())) {
                    targetingVo.setImgUrl(rpcVo.getImgUrl());
                }
                if (StringUtils.isNotBlank(rpcVo.getCategoryId())) {
                    targetingVo.setCategoryId(rpcVo.getCategoryId());
                }
                if (StringUtils.isNotBlank(rpcVo.getCategory())) {
                    targetingVo.setCategory(rpcVo.getCategory());
                }
                if (StringUtils.isNotBlank(rpcVo.getBid())) {
                    targetingVo.setBid(rpcVo.getBid());
                }
                if (StringUtils.isNotBlank(rpcVo.getBrand())) {
                    targetingVo.setBrand(rpcVo.getBrand());
                }
                if (StringUtils.isNotBlank(rpcVo.getMinPrice())) {
                    targetingVo.setMinPrice(rpcVo.getMinPrice());
                }
                if (StringUtils.isNotBlank(rpcVo.getMaxPrice())) {
                    targetingVo.setMaxPrice(rpcVo.getMaxPrice());
                }
                if (rpcVo.hasMinReviewRating()) {
                    targetingVo.setMinReviewRating(rpcVo.getMinReviewRating().getValue());
                }
                if (rpcVo.hasMaxReviewRating()) {
                    targetingVo.setMaxReviewRating(rpcVo.getMaxReviewRating().getValue());
                }
                if (rpcVo.hasPrimeShippingEligible()) {
                    targetingVo.setPrimeShippingEligible(rpcVo.getPrimeShippingEligible().getValue());
                }
                if (StringUtils.isNotBlank(rpcVo.getSuggested())) {
                    targetingVo.setSuggested(rpcVo.getSuggested());
                }
                if (StringUtils.isNotBlank(rpcVo.getRangeStart())) {
                    targetingVo.setRangeStart(rpcVo.getRangeStart());
                }
                if (StringUtils.isNotBlank(rpcVo.getRangeEnd())) {
                    targetingVo.setRangeEnd(rpcVo.getRangeEnd());
                }
                if (StringUtils.isNotBlank(rpcVo.getCategoryName())) {
                    targetingVo.setCategoryName(rpcVo.getCategoryName());
                }
                if (StringUtils.isNotBlank(rpcVo.getBrandName())) {
                    targetingVo.setBrandName(rpcVo.getBrandName());
                }
                voList.add(targetingVo);
            }

            addSbTargetingVo.setTargetings(voList);

            Result result = cpcSbTargetService.createTargeting(addSbTargetingVo);
            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void asyncCreateTargeting(AsyncAddTargetingRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sb-async-targeting-创建产品投放 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        String error = verifyAsyncParam(request);
        if (StringUtils.isNotBlank(error)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(error);
        } else {
            AdTargetTaskDto adTargetTaskDto = new AdTargetTaskDto();
            adTargetTaskDto.setPuid(request.getPuid().getValue());
            adTargetTaskDto.setShopId(request.getShopId().getValue());
            adTargetTaskDto.setUid(request.getUid().getValue());
            adTargetTaskDto.setType(AdTargetTaskTypeEnum.SB_TARGETS.getCode());
            adTargetTaskDto.setLoginIp(request.getLoginIp());
            adTargetTaskDto.setTargetingType(request.getTargetingType());
            adTargetTaskDto.setTargetPageType(request.getTargetingPageType().getValue());
            adTargetTaskDto.setSourceAdCampaignId(request.getSourceAdCampaignId());
            adTargetTaskDto.setSourceShopId(request.getSourceShopId().getValue());
            List<AdTargetTaskDto.AdTargetTaskDetailDto> detailList = request.getTargetingList().stream().filter(Objects::nonNull).map(item -> {
                AdTargetTaskDto.AdTargetTaskDetailDto detail = new AdTargetTaskDto.AdTargetTaskDetailDto();
                if (item.hasSourceShopId()) {
                    detail.setSourceShopId(item.getSourceShopId().getValue());
                }  else {
                    detail.setSourceShopId(request.getSourceShopId().getValue());
                }
                detail.setAdCampaignId(item.getAdCampaignId());
                detail.setAdGroupId(item.getAdGroupId());
                detail.setMatchType(item.getExpressionType());
                detail.setRangeEnd(StringUtils.isBlank(item.getRangeEnd()) ? null : new BigDecimal(item.getRangeEnd()));
                detail.setRangeStart(StringUtils.isBlank(item.getRangeStart()) ? null : new BigDecimal(item.getRangeStart()));
                detail.setSuggested(StringUtils.isBlank(item.getSuggested()) ? null : new BigDecimal(item.getSuggested()));
                detail.setBid(new BigDecimal(item.getBid()));
                if (StringUtils.isNotBlank(item.getTargetId())) {
                    detail.setTargetId(item.getTargetId());
                } else {
                    detail.setTargetObject(item.getAsin());
                    detail.setTargetObjectDesc(item.getTitle());
                    detail.setImgUrl(item.getImgUrl());
                }
                detail.setTargetObjectType(AdTargetObjectTypeEnum.getCodeByTargetType(item.getType()));
                return detail;
            }).collect(Collectors.toList());
            adTargetTaskDto.setTaskDetails(detailList);
            long taskId = adTargetTaskService.recordTargetTask(adTargetTaskDto);
            adTargetTaskService.executeTask(taskId);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private String verifyAsyncParam(AsyncAddTargetingRequest request) {
        if (!request.hasShopId() || !request.hasPuid() || !request.hasTargetingPageType()
                || CollectionUtils.isEmpty(request.getTargetingList()) || request.getTargetingList().size() > AdTargetTaskConstant.MAX_TARGET_SIZE) {
            return "请求参数错误";
        }
        List<AsyncAddTargetingRpcVo> list = request.getTargetingList();
        // 如果第一个包含targetId,那么列表中所有的对象都需要包含targetId
        int targetIdNum = 0;
        for (AsyncAddTargetingRpcVo each : list) {
            boolean haveTargetId = StringUtils.isNotBlank(each.getTargetId());
            if (haveTargetId) {
                targetIdNum++;
            }
            if (!AdTargetTaskMatchTypeEnum.SB_TARGET_SUPPORT_TYPES.contains(each.getExpressionType())) {
                return "请求参数错误,存在不支持的筛选条件";
            }
            if (!AdTargetObjectTypeEnum.TARGET_SUPPORT_TYPES.contains(each.getType())) {
                return "请求参数错误,存在不支持的投放类型";
            }
            if (TargetTypeEnum.category.name().equals(each.getType()) && !haveTargetId) {
                return "请求参数错误";
            }
            if (TargetTypeEnum.asin.name().equals(each.getType()) && !haveTargetId && StringUtils.isBlank(each.getAsin())) {
                return "请求参数错误";
            }
        }
        if (targetIdNum > 0 && targetIdNum < list.size() || (!request.hasSourceShopId() && !request.getTargetingList().get(0).hasSourceShopId()) || StringUtils.isBlank(request.getTargetingType())) {
            return "请求参数错误";
        }
        return StringUtils.EMPTY;
    }

    @Override
    public void update(UpdateRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasShopId() || StringUtils.isBlank(request.getTargetId()) ||
                (StringUtils.isBlank(request.getState()) && !request.hasBid())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            UpdateSdTargetingVo vo = new UpdateSdTargetingVo();
            vo.setShopId(request.getShopId().getValue());
            vo.setPuid(request.getPuid().getValue());
            vo.setUid(request.getUid().getValue());
            vo.setTargetId(request.getTargetId());
            vo.setIp(request.getIp());
            if (StringUtils.isNotBlank(request.getBid())) {
                vo.setBid(request.getBid());
            }
            if (StringUtils.isNotBlank(request.getState())) {
                vo.setState(request.getState());
            }
            Result result = cpcSbTargetService.update(vo);
            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }

    }

    @Override
    public void archive(ArchiveRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasShopId() || StringUtils.isBlank(request.getTargetId())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            Result result = cpcSbTargetService.archive(request.getPuid().getValue(), request.getShopId().getValue(),
                    request.getUid().getValue(), request.getTargetId(), request.getIp());
            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }


    }

    @Override
    public void suggestCategory(SuggestCategoryRequest request, StreamObserver<CategoryRecommendationResultsResponse> responseObserver) {
        CategoryRecommendationResultsResponse.Builder builder = CategoryRecommendationResultsResponse.newBuilder();
        ProtocolStringList asinListList = request.getAsinListList();
        if (!request.hasShopId() || CollectionUtils.isEmpty(asinListList)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            List<String> asinList = new ArrayList<>(asinListList.size());
            for (String s : asinListList) {
                asinList.add(s);
            }
            Result<List<CategoryRecommendationResults>> result = cpcSbTargetService.getTargetsCategoryList(request.getPuid().getValue(),
                    request.getShopId().getValue(), asinList);

            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");

            List<CategoryRecommendationResults> data = result.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                List<com.meiyunji.sponsored.rpc.sb.target.CategoryRecommendationResults> rpcList = new ArrayList<>(data.size());
                for (CategoryRecommendationResults vo : data) {
                    com.meiyunji.sponsored.rpc.sb.target.CategoryRecommendationResults.Builder builder2 = com.meiyunji.sponsored.rpc.sb.target.CategoryRecommendationResults.newBuilder();
                    if (vo.getId() != null) {
                        builder2.setId(Int64Value.of(vo.getId()));
                    }
                    if (vo.getName() != null) {
                        builder2.setName(vo.getName());
                    }
                    if (vo.getIsTargetable() != null) {
                        builder2.setIsTargetable(BoolValue.of(vo.getIsTargetable()));
                    }
                    if (vo.getPath() != null) {
                        builder2.setPath(vo.getPath());
                    }
                    rpcList.add(builder2.build());
                }
                builder.addAllData(rpcList);
            }

            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }

    }

    @Override
    public void suggestCategoryNew(SuggestCategoryPageRequest request, StreamObserver<CategoryRecommendationResultsPageResponse> responseObserver) {
        CategoryRecommendationResultsPageResponse.Builder builder = CategoryRecommendationResultsPageResponse.newBuilder();
        ProtocolStringList asinListList = request.getAsinListList();
        if (!request.hasShopId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            Result<TargetRecommendsPageVo> result = cpcSbTargetService.getTargetsCategoryListNew(request.getPuid().getValue(),
                    request.getShopId().getValue(), request.getAsinListList(), request.getPageNo(), request.getPageSize(), request.getCampaignId(), request.getGroupId());

            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");

            TargetRecommendsPageVo data = result.getData();
            com.meiyunji.sponsored.rpc.sb.target.CategoryRecommendationPageResults.Builder categoryRecommendationResult = com.meiyunji.sponsored.rpc.sb.target.CategoryRecommendationPageResults.newBuilder();
            List<com.meiyunji.sponsored.rpc.sb.target.CategoryRecommendationPageResults.CategoryRecommendWithTranslate> rpcList = new ArrayList<>();
            if (Objects.nonNull(data) && CollectionUtils.isNotEmpty(data.getCategoryList())) {
                for (CategoryRecommendationResults vo : data.getCategoryList()) {
                    com.meiyunji.sponsored.rpc.sb.target.CategoryRecommendationPageResults.CategoryRecommendWithTranslate
                            .Builder builder2 = com.meiyunji.sponsored.rpc.sb.target.CategoryRecommendationPageResults.CategoryRecommendWithTranslate.newBuilder();
                    if (vo.getId() != null) {
                        builder2.setId(Int64Value.of(vo.getId()));
                    }
                    if (vo.getName() != null) {
                        builder2.setName(vo.getName());
                    }
                    if (vo.getIsTargetable() != null) {
                        builder2.setIsTargetable(BoolValue.of(vo.getIsTargetable()));
                    }
                    if (vo.getPath() != null) {
                        builder2.setPath(vo.getPath());
                    }
                    Optional.ofNullable(vo.getTranslatedName()).ifPresent(builder2::setTranslatedName);
                    Optional.ofNullable(vo.getTranslatedCategoryPath()).ifPresent(builder2::setTranslatedPath);
                    rpcList.add(builder2.build());
                }
            }
            categoryRecommendationResult.addAllCategoryList(rpcList);
            Optional.ofNullable(data.getTotalSize()).ifPresent(categoryRecommendationResult::setTotalSize);
            Optional.ofNullable(data.getTotalPage()).ifPresent(categoryRecommendationResult::setTotalPage);
            Optional.of(request.getPageNo()).ifPresent(categoryRecommendationResult::setPageNo);
            Optional.of(request.getPageSize()).ifPresent(categoryRecommendationResult::setPageSize);
            builder.setData(categoryRecommendationResult.build());
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void addTargets(AddTargetsRequest request, StreamObserver<CategoryRecommendationResultsResponse> responseObserver) {
        CategoryRecommendationResultsResponse.Builder builder = CategoryRecommendationResultsResponse.newBuilder();
        if (!request.hasShopId() || StringUtils.isBlank(request.getCampaignId()) || StringUtils.isBlank(request.getGroupId())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            Result<List<CategoryRecommendationResults>> result = cpcSbTargetService.addTargets(request.getPuid().getValue(),
                    request.getShopId().getValue(), request.getCampaignId(), request.getGroupId());

            List<CategoryRecommendationResults> data = result.getData();
            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");

            if (CollectionUtils.isNotEmpty(data)) {
                List<com.meiyunji.sponsored.rpc.sb.target.CategoryRecommendationResults> rpcList = new ArrayList<>(data.size());
                for (CategoryRecommendationResults vo : data) {
                    com.meiyunji.sponsored.rpc.sb.target.CategoryRecommendationResults.Builder builder2 = com.meiyunji.sponsored.rpc.sb.target.CategoryRecommendationResults.newBuilder();
                    if (vo.getId() != null) {
                        builder2.setId(Int64Value.of(vo.getId()));
                    }
                    if (vo.getName() != null) {
                        builder2.setName(vo.getName());
                    }
                    if (vo.getIsTargetable() != null) {
                        builder2.setIsTargetable(BoolValue.of(vo.getIsTargetable()));
                    }
                    if (vo.getPath() != null) {
                        builder2.setPath(vo.getPath());
                    }
                    rpcList.add(builder2.build());
                }
                builder.addAllData(rpcList);
            }

            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }

    }

    @Override
    public void suggestCategoryBrand(SuggestCategoryBrandRequest request, StreamObserver<SuggestCategoryBrandResponse> responseObserver) {
        SuggestCategoryBrandResponse.Builder builder = SuggestCategoryBrandResponse.newBuilder();
        if (!request.hasShopId() || StringUtils.isBlank(request.getCategoryId())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            Result<List<com.meiyunji.sponsored.service.cpc.vo.SuggestCategoryBrandVo>> result = cpcSbTargetService.suggestBrand(request.getPuid().getValue(), request.getShopId().getValue(),
                    request.getCategoryId(), null);

            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");

            List<com.meiyunji.sponsored.service.cpc.vo.SuggestCategoryBrandVo> data = result.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                List<SuggestCategoryBrandVo> rpcList = new ArrayList<>(data.size());
                for (com.meiyunji.sponsored.service.cpc.vo.SuggestCategoryBrandVo vo : data) {
                    SuggestCategoryBrandVo.Builder builder2 = SuggestCategoryBrandVo.newBuilder();
                    if (vo.getId() != null) {
                        builder2.setId(vo.getId());
                    }
                    if (vo.getName() != null) {
                        builder2.setName(vo.getName());
                    }
                    rpcList.add(builder2.build());
                }
                builder.addAllData(rpcList);
            }

            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }


    }

    @Override
    public void suggestKeywordBrand(SuggestKeywordBrandRequest request, StreamObserver<SuggestCategoryBrandResponse> responseObserver) {
        SuggestCategoryBrandResponse.Builder builder = SuggestCategoryBrandResponse.newBuilder();
        if (!request.hasShopId() || StringUtils.isBlank(request.getKeyword())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            Result<List<com.meiyunji.sponsored.service.cpc.vo.SuggestCategoryBrandVo>> result = cpcSbTargetService.suggestBrand(request.getPuid().getValue(), request.getShopId().getValue(),
                    null, request.getKeyword());

            List<com.meiyunji.sponsored.service.cpc.vo.SuggestCategoryBrandVo> data = result.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                List<SuggestCategoryBrandVo> rpcList = new ArrayList<>(data.size());
                for (com.meiyunji.sponsored.service.cpc.vo.SuggestCategoryBrandVo vo : data) {
                    SuggestCategoryBrandVo.Builder builder2 = SuggestCategoryBrandVo.newBuilder();
                    if (vo.getId() != null) {
                        builder2.setId(vo.getId());
                    }
                    if (vo.getName() != null) {
                        builder2.setName(vo.getName());
                    }
                    rpcList.add(builder2.build());
                }
                builder.addAllData(rpcList);
            }

            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void suggestAsin(SuggestAsinRequest request, StreamObserver<SuggestAsinResponse> responseObserver) {
        SuggestAsinResponse.Builder builder = SuggestAsinResponse.newBuilder();
        ProtocolStringList asinListList = request.getAsinListList();
        if (!request.hasShopId() || CollectionUtils.isEmpty(asinListList)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            List<String> asinList = new ArrayList<>(asinListList.size());
            for (String s : asinListList) {
                asinList.add(s);
            }
            Result<List<com.amazon.advertising.sb.entity.targetingRecommendation.RecommendedProducts>> result = cpcSbTargetService.getSuggestAsin(request.getPuid().getValue(), request.getShopId().getValue(),
                    request.getAsinListList());

            List<com.amazon.advertising.sb.entity.targetingRecommendation.RecommendedProducts> data = result.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                List<RecommendedProducts> rpcList = new ArrayList<>(data.size());
                for (com.amazon.advertising.sb.entity.targetingRecommendation.RecommendedProducts vo : data) {
                    RecommendedProducts.Builder builder2 = RecommendedProducts.newBuilder();
                    if (vo.getRecommendedTargetAsin() != null) {
                        builder2.setRecommendedTargetAsin(vo.getRecommendedTargetAsin());
                    }
                    rpcList.add(builder2.build());
                }
                builder.addAllData(rpcList);
            }

            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();

        }

    }

    @Override
    public void getSuggestedBidByText(SuggestedBidByTextRequest request, StreamObserver<SuggestedBidByTextResponse> responseObserver) {
        SuggestedBidByTextResponse.Builder builder = SuggestedBidByTextResponse.newBuilder();
        List<TargetingVo> targetsList = request.getTargetsList();
        if (!request.hasShopId() || StringUtils.isBlank(request.getAdFormat()) || CollectionUtils.isEmpty(targetsList)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            List<com.meiyunji.sponsored.service.cpc.vo.TargetingVo> voList = new ArrayList<>(targetsList.size());
            com.meiyunji.sponsored.service.cpc.vo.TargetingVo targetingVo;
            for (TargetingVo vo : targetsList) {
                targetingVo = new com.meiyunji.sponsored.service.cpc.vo.TargetingVo();
                if (StringUtils.isNotBlank(vo.getType())) {
                    targetingVo.setType(vo.getType());
                }
                if (StringUtils.isNotBlank(vo.getAsin())) {
                    targetingVo.setAsin(vo.getAsin());
                }
                if (StringUtils.isNotBlank(vo.getTitle())) {
                    targetingVo.setTitle(vo.getTitle());
                }
                if (StringUtils.isNotBlank(vo.getImgUrl())) {
                    targetingVo.setImgUrl(vo.getImgUrl());
                }
                if (StringUtils.isNotBlank(vo.getCategoryId())) {
                    targetingVo.setCategoryId(vo.getCategoryId());
                }
                if (StringUtils.isNotBlank(vo.getCategory())) {
                    targetingVo.setCategory(vo.getCategory());
                }
                if (StringUtils.isNotBlank(vo.getBid())) {
                    targetingVo.setBid(vo.getBid());
                }
                if (StringUtils.isNotBlank(vo.getBrand())) {
                    targetingVo.setBrand(vo.getBrand());
                }
                    targetingVo.setMinPrice(vo.getMinPrice());
                    targetingVo.setMaxPrice(vo.getMaxPrice());
                if (vo.hasMinReviewRating()) {
                    targetingVo.setMinReviewRating(vo.getMinReviewRating().getValue());
                }
                if (vo.hasMaxReviewRating()) {
                    targetingVo.setMaxReviewRating(vo.getMaxReviewRating().getValue());
                }
                if (vo.hasPrimeShippingEligible()) {
                    targetingVo.setPrimeShippingEligible(vo.getPrimeShippingEligible().getValue());
                }
                if (StringUtils.isNotBlank(vo.getSuggested())) {
                    targetingVo.setSuggested(vo.getSuggested());
                }
                if (StringUtils.isNotBlank(vo.getRangeStart())) {
                    targetingVo.setRangeStart(vo.getRangeStart());
                }
                if (StringUtils.isNotBlank(vo.getRangeEnd())) {
                    targetingVo.setRangeEnd(vo.getRangeEnd());
                }
                voList.add(targetingVo);
            }

            Result<List<com.meiyunji.sponsored.service.cpc.vo.SuggestedTargetVo>> result = cpcSbTargetService.getTargetBids(request.getPuid().getValue(), request.getShopId().getValue(),
                    request.getCampaignId(), request.getAdFormat(), voList);

            List<com.meiyunji.sponsored.service.cpc.vo.SuggestedTargetVo> data = result.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                List<SuggestedTargetVo> cpcList = new ArrayList<>(data.size());
                for (com.meiyunji.sponsored.service.cpc.vo.SuggestedTargetVo vo : data) {
                    SuggestedTargetVo.Builder builder2 = SuggestedTargetVo.newBuilder();
                    if (vo.getTargetId() != null) {
                        builder2.setTargetId(vo.getTargetId());
                    }
                    if (vo.getSuggested() != null) {
                        builder2.setSuggested(vo.getSuggested());
                    }
                    if (vo.getRangeStart() != null) {
                        builder2.setRangeStart(vo.getRangeStart());
                    }
                    if (vo.getRangeEnd() != null) {
                        builder2.setRangeEnd(vo.getRangeEnd());
                    }

                    List<Expression> expression = vo.getExpression();
                    if (CollectionUtils.isNotEmpty(expression)) {
                        List<com.meiyunji.sponsored.rpc.sb.target.Expression> cpcExList = new ArrayList<>(expression.size());
                        for (Expression e : expression) {
                            com.meiyunji.sponsored.rpc.sb.target.Expression.Builder builder3 = com.meiyunji.sponsored.rpc.sb.target.Expression.newBuilder();
                            if (e.getType() != null) {
                                builder3.setType(e.getType());
                            }
                            if (e.getValue() != null) {
                                builder3.setValue(e.getValue());
                            }
                            cpcExList.add(builder3.build());
                        }
                        builder2.addAllExpression(cpcExList);
                    }
                    cpcList.add(builder2.build());
                }
                builder.addAllData(cpcList);
            }


            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getSuggestedBidByTextNew(SuggestedBidByTextRequest request, StreamObserver<SuggestedBidByTextResponse> responseObserver) {
        SuggestedBidByTextResponse.Builder builder = SuggestedBidByTextResponse.newBuilder();
        List<TargetingVo> targetsList = request.getTargetsList();
        String adFormat = Optional.of(request.getAdFormat()).filter(StringUtils::isNotEmpty).map(Integer::valueOf)
                .map(SBAdFormatEnum::getSBAdFormatEnumByCode).map(SBAdFormatEnum::getValue).orElse(null);
        SBCampaignGoalEnum goalEn = SBCampaignGoalEnum.getSBCampaignGoalEnumByCode(request.getGoal());
        String costType = null;
        if (Objects.nonNull(goalEn)) {
            if (SBCampaignGoalEnum.PAGE_VISIT == goalEn) {
                costType = SBCampaignCostTypeEnum.CPC.getCode();
            }
            if (SBCampaignGoalEnum.BRAND_IMPRESSION_SHARE == goalEn) {
                costType = SBCampaignCostTypeEnum.VCPM.getCode();
            }
        }
        if (!request.hasShopId()  || CollectionUtils.isEmpty(targetsList)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            List<com.meiyunji.sponsored.service.cpc.vo.TargetingVo> voList = new ArrayList<>(targetsList.size());
            com.meiyunji.sponsored.service.cpc.vo.TargetingVo targetingVo;
            for (TargetingVo vo : targetsList) {
                targetingVo = new com.meiyunji.sponsored.service.cpc.vo.TargetingVo();
                if (StringUtils.isNotBlank(vo.getType())) {
                    targetingVo.setType(vo.getType());
                }
                if (StringUtils.isNotBlank(vo.getAsin())) {
                    targetingVo.setAsin(vo.getAsin());
                }
                if (StringUtils.isNotBlank(vo.getTitle())) {
                    targetingVo.setTitle(vo.getTitle());
                }
                if (StringUtils.isNotBlank(vo.getImgUrl())) {
                    targetingVo.setImgUrl(vo.getImgUrl());
                }
                if (StringUtils.isNotBlank(vo.getCategoryId())) {
                    targetingVo.setCategoryId(vo.getCategoryId());
                }
                if (StringUtils.isNotBlank(vo.getCategory())) {
                    targetingVo.setCategory(vo.getCategory());
                }
                if (StringUtils.isNotBlank(vo.getBid())) {
                    targetingVo.setBid(vo.getBid());
                }
                if (StringUtils.isNotBlank(vo.getBrand())) {
                    targetingVo.setBrand(vo.getBrand());
                }
                targetingVo.setMinPrice(vo.getMinPrice());
                targetingVo.setMaxPrice(vo.getMaxPrice());
                if (vo.hasMinReviewRating()) {
                    targetingVo.setMinReviewRating(vo.getMinReviewRating().getValue());
                }
                if (vo.hasMaxReviewRating()) {
                    targetingVo.setMaxReviewRating(vo.getMaxReviewRating().getValue());
                }
                if (vo.hasPrimeShippingEligible()) {
                    targetingVo.setPrimeShippingEligible(vo.getPrimeShippingEligible().getValue());
                }
                if (StringUtils.isNotBlank(vo.getSuggested())) {
                    targetingVo.setSuggested(vo.getSuggested());
                }
                if (StringUtils.isNotBlank(vo.getRangeStart())) {
                    targetingVo.setRangeStart(vo.getRangeStart());
                }
                if (StringUtils.isNotBlank(vo.getRangeEnd())) {
                    targetingVo.setRangeEnd(vo.getRangeEnd());
                }
                voList.add(targetingVo);
            }

            Result<List<com.meiyunji.sponsored.service.cpc.vo.SuggestedTargetVo>> result = cpcSbTargetService.getTargetBidsNew(request.getPuid().getValue(), request.getShopId().getValue(),
                    adFormat, voList, Optional.ofNullable(goalEn).map(SBCampaignGoalEnum::getType).orElse(null), costType);

            List<com.meiyunji.sponsored.service.cpc.vo.SuggestedTargetVo> data = result.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                List<SuggestedTargetVo> cpcList = new ArrayList<>(data.size());
                for (com.meiyunji.sponsored.service.cpc.vo.SuggestedTargetVo vo : data) {
                    SuggestedTargetVo.Builder builder2 = SuggestedTargetVo.newBuilder();
                    Optional.ofNullable(vo.getIndex()).ifPresent(builder2::setIndex);
                    if (vo.getTargetId() != null) {
                        builder2.setTargetId(vo.getTargetId());
                    }
                    if (vo.getSuggested() != null) {
                        builder2.setSuggested(vo.getSuggested());
                    }
                    if (vo.getRangeStart() != null) {
                        builder2.setRangeStart(vo.getRangeStart());
                    }
                    if (vo.getRangeEnd() != null) {
                        builder2.setRangeEnd(vo.getRangeEnd());
                    }
                    List<Expression> expression = vo.getExpression();
                    if (CollectionUtils.isNotEmpty(expression)) {
                        List<com.meiyunji.sponsored.rpc.sb.target.Expression> cpcExList = new ArrayList<>(expression.size());
                        for (Expression e : expression) {
                            com.meiyunji.sponsored.rpc.sb.target.Expression.Builder builder3 = com.meiyunji.sponsored.rpc.sb.target.Expression.newBuilder();
                            if (e.getType() != null) {
                                builder3.setType(e.getType());
                            }
                            if (e.getValue() != null) {
                                builder3.setValue(e.getValue());
                            }
                            cpcExList.add(builder3.build());
                        }
                        builder2.addAllExpression(cpcExList);
                    }
                    cpcList.add(builder2.build());
                }
                builder.addAllData(cpcList);
            }


            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getSuggestedBid(SuggestedBidRequest request, StreamObserver<SuggestedBidResponse> responseObserver) {
        SuggestedBidResponse.Builder builder = SuggestedBidResponse.newBuilder();

        ProtocolStringList targetIdListList = request.getTargetIdListList();
        if (!request.hasShopId() || CollectionUtils.isEmpty(targetIdListList)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            List<String> targetList = new ArrayList<>(targetIdListList.size());
            for (String s : targetIdListList) {
                targetList.add(s);
            }
            Result<List<com.meiyunji.sponsored.service.cpc.vo.SuggestedTargetVo>> result = cpcSbTargetService.getSuggestedBid(request.getPuid().getValue(),
                    request.getShopId().getValue(), targetList);

            List<com.meiyunji.sponsored.service.cpc.vo.SuggestedTargetVo> data = result.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                List<SuggestedTargetVo> cpcList = new ArrayList<>(data.size());
                for (com.meiyunji.sponsored.service.cpc.vo.SuggestedTargetVo vo : data) {
                    SuggestedTargetVo.Builder builder2 = SuggestedTargetVo.newBuilder();
                    if (vo.getTargetId() != null) {
                        builder2.setTargetId(vo.getTargetId());
                    }
                    if (vo.getSuggested() != null) {
                        builder2.setSuggested(vo.getSuggested());
                    }
                    if (vo.getRangeStart() != null) {
                        builder2.setRangeStart(vo.getRangeStart());
                    }
                    if (vo.getRangeEnd() != null) {
                        builder2.setRangeEnd(vo.getRangeEnd());
                    }

                    List<Expression> expression = vo.getExpression();
                    if (CollectionUtils.isNotEmpty(expression)) {
                        List<com.meiyunji.sponsored.rpc.sb.target.Expression> cpcExList = new ArrayList<>(expression.size());
                        for (Expression e : expression) {
                            com.meiyunji.sponsored.rpc.sb.target.Expression.Builder builder3 = com.meiyunji.sponsored.rpc.sb.target.Expression.newBuilder();
                            if (e.getType() != null) {
                                builder3.setType(e.getType());
                            }
                            if (e.getValue() != null) {
                                builder3.setValue(e.getValue());
                            }
                            cpcExList.add(builder3.build());
                        }
                        builder2.addAllExpression(cpcExList);
                    }
                    cpcList.add(builder2.build());
                }
                builder.addAllData(cpcList);
            }


            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void batchGetSuggestedBid(BatchTargetSuggestedBidRequest request, StreamObserver<SuggestedBidResponse> responseObserver) {
        SuggestedBidResponse.Builder builder = SuggestedBidResponse.newBuilder();

        if (!request.hasPuid() || (!request.hasSourceShopId() && !request.getTargetDetailList().get(0).hasSourceShopId()) || !request.hasShopId() || CollectionUtils.isEmpty(request.getTargetDetailList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            List<TargetSuggestedBidVo> requestDetails = request.getTargetDetailList();
            List<TargetSuggestedBidDetail> details = new ArrayList<>();
            for (int i = 0; i < requestDetails.size(); i++) {
                TargetSuggestedBidDetail targetSuggestedBidDetail = new TargetSuggestedBidDetail();
                BeanUtils.copyProperties(requestDetails.get(i), targetSuggestedBidDetail);
                targetSuggestedBidDetail.setIndex(i);
                targetSuggestedBidDetail.setSourceShopId(requestDetails.get(i).getSourceShopId().getValue());
                details.add(targetSuggestedBidDetail);
            }
            //兼容单店铺
            List<Integer> sourceShopIds = new ArrayList<>();
            if (details.get(0).getSourceShopId() != null && details.get(0).getSourceShopId() != 0) {
                sourceShopIds.addAll(details.stream().map(TargetSuggestedBidDetail::getSourceShopId).filter(e -> e != null && !e.equals(0)).distinct().collect(Collectors.toList()));
            } else {
                sourceShopIds.add(request.getSourceShopId().getValue());
            }
            Result<List<com.meiyunji.sponsored.service.cpc.vo.SuggestedTargetVo>> result = cpcSbTargetService.batchGetSuggestedBid(
                    request.getPuid().getValue(), request.getShopId().getValue(), sourceShopIds, details, request.getTargetingType());

            List<com.meiyunji.sponsored.service.cpc.vo.SuggestedTargetVo> data = result.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                List<SuggestedTargetVo> cpcList = new ArrayList<>(data.size());
                for (com.meiyunji.sponsored.service.cpc.vo.SuggestedTargetVo vo : data) {
                    SuggestedTargetVo.Builder builder2 = SuggestedTargetVo.newBuilder();
                    if (vo.getTargetId() != null) {
                        builder2.setTargetId(vo.getTargetId());
                    }
                    if (vo.getSuggested() != null) {
                        builder2.setSuggested(vo.getSuggested());
                    }
                    if (vo.getRangeStart() != null) {
                        builder2.setRangeStart(vo.getRangeStart());
                    }
                    if (vo.getRangeEnd() != null) {
                        builder2.setRangeEnd(vo.getRangeEnd());
                    }

                    List<Expression> expression = vo.getExpression();
                    if (CollectionUtils.isNotEmpty(expression)) {
                        List<com.meiyunji.sponsored.rpc.sb.target.Expression> cpcExList = new ArrayList<>(expression.size());
                        for (Expression e : expression) {
                            com.meiyunji.sponsored.rpc.sb.target.Expression.Builder builder3 = com.meiyunji.sponsored.rpc.sb.target.Expression.newBuilder();
                            if (e.getType() != null) {
                                builder3.setType(e.getType());
                            }
                            if (e.getValue() != null) {
                                builder3.setValue(e.getValue());
                            }
                            cpcExList.add(builder3.build());
                        }
                        builder2.addAllExpression(cpcExList);
                    }
                    builder2.setIndex(vo.getIndex());
                    cpcList.add(builder2.build());
                }
                builder.addAllData(cpcList);
            }


            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    /**
     * 更新竞价
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateBatch(UpdateBatchSbTargetRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-targeting-更新投放竞价 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasPuid() || request.getVosCount() < 1 || !request.hasShopId() || !request.hasType()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            List<UpdateBatchTargetVo> collect = request.getVosList().stream().map(re -> {
                UpdateBatchTargetVo vo = new UpdateBatchTargetVo();
                if (re.hasId()) {
                    vo.setId(Long.valueOf(re.getId().getValue()));
                }
                vo.setPuid(request.getPuid().getValue());
                vo.setUid(request.getUid().getValue());
                vo.setShopId(request.getShopId().getValue());
                vo.setState(re.getState());
                if (re.hasBid()) {
                    vo.setBid(re.getBid().getValue());
                }
                return vo;
            }).collect(Collectors.toList());

            Result res = cpcSbTargetService.updateBatch(collect, request.getType(), request.getIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getData() != null) {
                builder.setData(JSONUtil.objectToJson(res.getData()));
            }
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }
}
