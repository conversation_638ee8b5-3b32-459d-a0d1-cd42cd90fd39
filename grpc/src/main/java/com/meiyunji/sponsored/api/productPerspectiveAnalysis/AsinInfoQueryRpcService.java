package com.meiyunji.sponsored.api.productPerspectiveAnalysis;

import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.asinInfoQuery.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.IAsinInfoQueryService;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AsinListReqVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.InitAsinInfoReqVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.InitAsinVo;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import com.meiyunji.sponsored.util.ProtoBufUtil;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-08-31  19:21
 */

@GRpcService
@Slf4j
public class AsinInfoQueryRpcService extends RpcAsinInfoQueryServiceGrpc.RpcAsinInfoQueryServiceImplBase{

    @Autowired
    private IAsinInfoQueryService asinInfoQueryService;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Override
    public void getInitAsinInfo(InitAsinInfoRequest request, StreamObserver<InitAsinInfoResponse> responseObserver) {
        getAsinInit(request, responseObserver, asinInfoQueryService::getInitAsinInfo);
    }

    @Override
    public void getInitAsinInfoBatchSpCreate(InitAsinInfoRequest request, StreamObserver<InitAsinInfoResponse> responseObserver) {
        getAsinInit(request, responseObserver, asinInfoQueryService::getBatchSpTaskInitAsinInfo);
    }

    private void getAsinInit(InitAsinInfoRequest request, StreamObserver<InitAsinInfoResponse> responseObserver,
                             Function<InitAsinInfoReqVo, InitAsinVo> func) {
        StopWatch sw = new StopWatch();
        sw.start();
        InitAsinInfoResponse.Builder builder = InitAsinInfoResponse.newBuilder();
        InitAsinInfoReqVo reqVo = new InitAsinInfoReqVo();
        reqVo.setPuid(request.getPuid());
        List<ShopAuth> shopList = shopAuthDao.getAuthShopByShopIdList(request.getPuid(), request.getShopIdList());
        if (CollectionUtils.isEmpty(shopList)) {
            builder.setCode(Result.SUCCESS);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        reqVo.setShopIdList(shopList.stream().map(ShopAuth::getId).collect(Collectors.toList()));
        if (StringUtils.isNotBlank(request.getMarketplaceId())) {
            reqVo.setMarketplaceId(request.getMarketplaceId());
        }

        InitAsinVo initAsinInfo = func.apply(reqVo);
        if (initAsinInfo == null) {
            log.info("init asin failed, asin not found");
            builder.setCode(Result.ERROR);
            builder.setMsg("初始化ASIN数据失败");
        } else {
            InitAsinInfoResponseVo.Builder asinBuilder = InitAsinInfoResponseVo.newBuilder();
            Optional.ofNullable(initAsinInfo.getMarketplaceId()).ifPresent(asinBuilder::setMarketplaceId);
            Optional.ofNullable(initAsinInfo.getShopId()).ifPresent(asinBuilder::addAllShopId);
            Optional.ofNullable(initAsinInfo.getAsin()).ifPresent(asinBuilder::setAsin);
            builder.setData(asinBuilder.build());
            builder.setCode(Result.SUCCESS);
        }

        sw.stop();
        log.info("get asin info, puid: {}, shopId: {}, marketplaceId: {}, 耗时: {}秒",
                reqVo.getPuid(),
                reqVo.getShopIdList(),
                reqVo.getMarketplaceId(),
                sw.getTotalTimeSeconds());

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAsinList(AsinListRequest request, StreamObserver<AsinListResponse> responseObserver) {
        StopWatch sw = new StopWatch();
        sw.start();
        AsinListResponse.Builder builder = AsinListResponse.newBuilder();
        AsinListReqVo reqVo = new AsinListReqVo();
        reqVo.setPageNo(request.getPageNo());
        reqVo.setPageSize(request.getPageSize());
        reqVo.setPuid(request.getPuid());
        reqVo.setMarketplaceId(request.getMarketplaceId());
        reqVo.setShopIdList(request.getShopIdList());
        LocalDateTime now = LocalDateTime.now();
        reqVo.setStart(LocalDateTimeUtil.formatTime(now.plusDays(-59), LocalDateTimeUtil.YYYYMMDD_DATE_FORMATE));
        reqVo.setEnd(LocalDateTimeUtil.formatTime(now, LocalDateTimeUtil.YYYYMMDD_DATE_FORMATE));
        if (StringUtils.isNotBlank(request.getSearchValue())) {
            reqVo.setSearchValue(request.getSearchValue());
        }
        if (StringUtils.isNotBlank(request.getSearchType())) {
            reqVo.setSearchType(request.getSearchType());
        }
        AsinListResponseVo asinList = asinInfoQueryService.getAsinList(reqVo);
        builder.setData(asinList);
        builder.setCode(Result.SUCCESS);
        sw.stop();
        log.info("商品透视分析查询asin列表, puid: {}, shopId: {}, marketplaceId: {}, asin: {}, 耗时: {}秒",
                reqVo.getPuid(),
                reqVo.getShopIdList(),
                reqVo.getMarketplaceId(),
                reqVo.getSearchValue(),
                sw.getTotalTimeSeconds());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAllAsinList(AllAsinListRequest request, StreamObserver<AllAsinListResponse> responseObserver) {
        StopWatch sw = new StopWatch();
        sw.start();
        AllAsinListResponse.Builder builder = AllAsinListResponse.newBuilder();
        AsinListReqVo reqVo = new AsinListReqVo();
        reqVo.setPageNo(request.getPageNo());
        reqVo.setPageSize(request.getPageSize());
        reqVo.setPuid(request.getPuid());
        reqVo.setMarketplaceId(request.getMarketplaceId());
        reqVo.setShopIdList(request.getShopIdList());
        LocalDateTime now = LocalDateTime.now();
        reqVo.setStart(LocalDateTimeUtil.formatTime(now.plusDays(-59), LocalDateTimeUtil.YMD_DATE_FORMAT));
        reqVo.setEnd(LocalDateTimeUtil.formatTime(now, LocalDateTimeUtil.YMD_DATE_FORMAT));
        if (StringUtils.isNotBlank(request.getSearchValue())) {
            reqVo.setSearchValue(request.getSearchValue());
        }
        if (StringUtils.isNotBlank(request.getSearchType())) {
            reqVo.setSearchType(request.getSearchType());
        }
        reqVo.setAdType(request.getAdType());
        reqVo.setSearchFlag(request.getSearchFlag());
        reqVo.setSearchList(request.getSearchList());
        if (StringUtils.isBlank(request.getAdType())) {
            log.info("get all asin list failed, adtype is empty");
            builder.setCode(Result.ERROR);
            builder.setMsg("查询产品列表失败");
        } else {
            AllAsinListResponseVo asinList = asinInfoQueryService.getAllAsinList(reqVo);
            builder.setData(asinList);
            builder.setCode(Result.SUCCESS);
        }
        sw.stop();
        log.info("商品透视分析查询asin列表, puid: {}, shopId: {}, marketplaceId: {}, asin: {}, 耗时: {}秒",
                reqVo.getPuid(),
                reqVo.getShopIdList(),
                reqVo.getMarketplaceId(),
                reqVo.getSearchValue(),
                sw.getTotalTimeSeconds());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAsinAllList(AsinListRequest request, StreamObserver<AsinListResponse> responseObserver) {
        log.info("getAsinAllList = {}", request);
        AsinListResponse.Builder builder = AsinListResponse.newBuilder();
        AsinListReqVo reqVo = new AsinListReqVo();
        reqVo.setPageNo(request.getPageNo());
        reqVo.setPageSize(request.getPageSize());
        reqVo.setPuid(request.getPuid());
        reqVo.setShopIdList(request.getShopIdList());
        if (StringUtils.isNotBlank(request.getSearchValue())) {
            reqVo.setSearchValue(request.getSearchValue());
        }
        if (StringUtils.isNotBlank(request.getSearchType())) {
            reqVo.setSearchType(request.getSearchType());
        }
        reqVo.setPortfolioId(request.getPortfolioId());
        reqVo.setCampaignId(request.getCampaignId());
        reqVo.setAdType(request.getAdType());
        reqVo.setGroupId(request.getGroupId());
        reqVo.setStatus(request.getStatus());
        reqVo.setServingStatus(request.getServingStatus());
        reqVo.setSearchFlag(request.getSearchFlag());
        if(CollectionUtils.isEmpty(reqVo.getShopIdList())){
            builder.setCode(Result.ERROR);
            builder.setMsg("您没有店铺访问权限，请联系管理员");
        }else{
            AsinListResponseVo asinList = asinInfoQueryService.getAsinAllList(reqVo);
            builder.setData(asinList);
            builder.setCode(Result.SUCCESS);
        }
        log.info("查询商品over");
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getBatchTaskAsinList(AsinListRequest request, StreamObserver<AsinListResponse> responseObserver) {
        StopWatch sw = new StopWatch();
        sw.start();
        AsinListResponse.Builder builder = AsinListResponse.newBuilder();
        AsinListReqVo reqVo = new AsinListReqVo();
        reqVo.setPageNo(request.getPageNo());
        reqVo.setPageSize(request.getPageSize());
        reqVo.setPuid(request.getPuid());
        reqVo.setMarketplaceId(request.getMarketplaceId());
        reqVo.setShopIdList(request.getShopIdList());
        LocalDateTime now = LocalDateTime.now();
        reqVo.setEnd(LocalDateTimeUtil.formatTime(now, LocalDateTimeUtil.YYYYMMDD_DATE_FORMATE));
        if (StringUtils.isNotBlank(request.getSearchValue())) {
            reqVo.setSearchValue(request.getSearchValue());
        }
        AsinListResponseVo asinList = asinInfoQueryService.getAsinList(reqVo);
        builder.setData(asinList);
        builder.setCode(Result.SUCCESS);
        sw.stop();
        log.info("商品透视分析查询asin列表, puid: {}, shopId: {}, marketplaceId: {}, asin: {}, 耗时: {}秒",
                reqVo.getPuid(),
                reqVo.getShopIdList(),
                reqVo.getMarketplaceId(),
                reqVo.getSearchValue(),
                sw.getTotalTimeSeconds());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAsinListBatchSpCreate(AsinListRequest request, StreamObserver<AsinListResponse> responseObserver) {
        getBatchTaskAsinList(request, responseObserver);
    }

    @Override
    public void getAdvertisedProducts(AsinListRequest request, StreamObserver<AsinListResponse> responseObserver) {
        log.info("getAdvertisedProducts, request={}", ProtoBufUtil.toJson(request));
        AsinListResponse.Builder builder = AsinListResponse.newBuilder();
        AsinListReqVo reqVo = new AsinListReqVo();
        reqVo.setPageNo(request.getPageNo());
        reqVo.setPageSize(request.getPageSize());
        reqVo.setPuid(request.getPuid());
        reqVo.setMarketplaceId(request.getMarketplaceId());
        reqVo.setShopIdList(request.getShopIdList());
        if (StringUtils.isNotBlank(request.getSearchValue())) {
            reqVo.setSearchValue(request.getSearchValue());
        }
        if (StringUtils.isNotBlank(request.getSearchType())) {
            reqVo.setSearchType(request.getSearchType());
        }
        if (StringUtils.isNotBlank(request.getSearchField())) {
            reqVo.setSearchField(request.getSearchField());
        }
        reqVo.setSearchFlag(request.getSearchFlag());
        reqVo.setOrderType(request.getOrderType());
        reqVo.setOnlineStatus(request.getOnlineStatus());
        AsinListResponseVo asinList = asinInfoQueryService.getAdProducts(reqVo);
        builder.setData(asinList);
        builder.setCode(Result.SUCCESS);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
