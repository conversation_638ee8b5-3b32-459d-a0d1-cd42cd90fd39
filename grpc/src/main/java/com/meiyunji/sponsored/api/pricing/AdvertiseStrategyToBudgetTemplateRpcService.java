package com.meiyunji.sponsored.api.pricing;

import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.pricing.to.budget.*;
import com.meiyunji.sponsored.rpc.vo.CommonResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdPortfolioDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTopBudgetTemplateDao;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTopBudgetTemplate;
import com.meiyunji.sponsored.service.strategy.service.IAdvertiseStrategyToBudgetTemplateService;
import com.meiyunji.sponsored.service.strategy.vo.*;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.shade.com.google.common.collect.Lists;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@GRpcService
@Slf4j
public class AdvertiseStrategyToBudgetTemplateRpcService extends RPCAdStrategyToBudgetServiceGrpc.RPCAdStrategyToBudgetServiceImplBase {

    @Autowired
    private IAdvertiseStrategyToBudgetTemplateService advertiseStrategyToBudgetTemplateService;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;

    @Autowired
    private AdvertiseStrategyTopBudgetTemplateDao advertiseStrategyTopBudgetTemplateDao;

    @Override
    public void pageToBudgetList(PageToBudgetRequest request, StreamObserver<PageToBudgetResponse> responseObserver) {
        log.info("分页查询顶级预算模板数据 pageToBudgetList request:{}", request);
        PageToBudgetResponse.Builder responseBuilder = PageToBudgetResponse.newBuilder();
        if (!request.hasPuid() || !request.hasPageNo() || !request.hasPageSize()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            PageTopBudgetParam param = new PageTopBudgetParam();
            param.setPuid(request.getPuid());
            param.setShopIds(request.getShopIdList());
            param.setPageSize(request.getPageSize());
            param.setPageNo(request.getPageNo());
            param.setTraceId(request.getTraceId());
            if (StringUtils.isNotBlank(request.getUpdateTimeStart())) {
                param.setUpdateTimeStart(DateUtil.strToDate(request.getUpdateTimeStart(), DateUtil.PATTERN));
            }
            if (StringUtils.isNotBlank(request.getUpdateTimeEnd())) {
                param.setUpdateTimeEnd(DateUtil.getDayMaxDate(DateUtil.strToDate(request.getUpdateTimeEnd(), DateUtil.PATTERN)));
            }
            if (StringUtils.isNotBlank(request.getUpdateUid())) {
                param.setUpdateUidList(Arrays.stream(request.getUpdateUid().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
            }
            if (StringUtils.isNotBlank(request.getCreateUid())) {
                param.setCreateUidList(Arrays.stream(request.getCreateUid().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
            }
            if (StringUtils.isNotBlank(request.getStatus())) {
                param.setStatus(request.getStatus());
            }
            Result<Page<AdvertiseStrategyTopBudgetTemplate>> result = advertiseStrategyToBudgetTemplateService.getPageList(param);
            ToBudgetPage.Builder pageBuilder = ToBudgetPage.newBuilder();
            pageBuilder.setPageNo(request.getPageNo());
            pageBuilder.setPageSize(request.getPageSize());
            if (result.getCode() == Result.SUCCESS) {
                Page<AdvertiseStrategyTopBudgetTemplate> page = result.getData();
                pageBuilder.setTotalSize(page.getTotalSize());
                pageBuilder.setTotalPage(page.getTotalPage());
                if (CollectionUtils.isNotEmpty(page.getRows())) {
                    List<ToBudgetRpc> toBudgetRpcList = Lists.newArrayListWithCapacity(page.getRows().size());
                    List<ShopAuth> shops = shopAuthDao.getListByPuid(request.getPuid());
                    Map<Integer, ShopAuth> shopAuthMap = null;
                    if (CollectionUtils.isNotEmpty(shops)) {
                        shopAuthMap = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
                    }
                    for (AdvertiseStrategyTopBudgetTemplate advertiseStrategyTopBudgetTemplate:page.getRows()) {
                        ToBudgetRpc.Builder builder = ToBudgetRpc.newBuilder();
                        builder.setPuid(advertiseStrategyTopBudgetTemplate.getPuid());
                        builder.setShopId(advertiseStrategyTopBudgetTemplate.getShopId());
                        builder.setId(advertiseStrategyTopBudgetTemplate.getId());
                        builder.setTaskId(advertiseStrategyTopBudgetTemplate.getTaskId());
                        builder.setProfileId(advertiseStrategyTopBudgetTemplate.getProfileId());
                        builder.setStatus(advertiseStrategyTopBudgetTemplate.getStatus());
                        builder.setMarketplaceId(advertiseStrategyTopBudgetTemplate.getMarketplaceId());
                        if (StringUtils.isNotBlank(advertiseStrategyTopBudgetTemplate.getMarketplaceName())) {
                            builder.setMarketplaceName(advertiseStrategyTopBudgetTemplate.getMarketplaceName());
                        }
                        if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(advertiseStrategyTopBudgetTemplate.getShopId())) {
                            builder.setShopName(shopAuthMap.get(advertiseStrategyTopBudgetTemplate.getShopId()).getName());
                        }
                        OriginToBudgetValueVo originToBudgetValueVo = JSONUtil.jsonToObject(advertiseStrategyTopBudgetTemplate.getOriginValue(),
                                OriginToBudgetValueVo.class);
                        builder.setOriginUseTopBudget(originToBudgetValueVo.getUseTopBudget());
                        if (originToBudgetValueVo.getUseTopBudget() && originToBudgetValueVo.getToBudgetValue() != null) {
                            builder.setOriginToBudgetValue(originToBudgetValueVo.getToBudgetValue().doubleValue());
                        }
                        OriginToBudgetValueVo reductionValueVo = JSONUtil.jsonToObject(advertiseStrategyTopBudgetTemplate.getReductionValue(),
                                OriginToBudgetValueVo.class);
                        builder.setReductionUseTopBudget(reductionValueVo.getUseTopBudget());
                        if (reductionValueVo.getUseTopBudget() && reductionValueVo.getToBudgetValue() != null) {
                            builder.setReductionToBudgetValue(reductionValueVo.getToBudgetValue().doubleValue());
                        }
                        builder.setType(advertiseStrategyTopBudgetTemplate.getType());
                        List<ToBudgetRuleVo> toBudgetRuleVoList = JSONUtil.
                                jsonToArray(advertiseStrategyTopBudgetTemplate.getRule(),ToBudgetRuleVo.class);
                        if (CollectionUtils.isNotEmpty(toBudgetRuleVoList)) {
                            Map<Integer,List<ToBudgetRuleVo>> map = toBudgetRuleVoList.stream().
                                    collect(Collectors.groupingBy(ToBudgetRuleVo::getSiteDate));
                            for (Integer day : map.keySet()) {
                                ToBudgetRule.Builder toBudgetRule = ToBudgetRule.newBuilder();
                                toBudgetRule.setSiteDate(day);
                                if (day == 0) {
                                    toBudgetRule.setSiteDateName("每日");
                                } else {
                                    toBudgetRule.setSiteDateName(com.meiyunji.sponsored.service.
                                            cpc.util.Constants.getDateMap().get(day));
                                }
                                for (ToBudgetRuleVo toBudgetRuleVo : map.get(day)) {
                                    ToBudget.Builder toBudget = ToBudget.newBuilder();
                                    toBudget.setStartTimeSite(toBudgetRuleVo.getStartTimeSite());
                                    toBudget.setEndTimeSite(toBudgetRuleVo.getEndTimeSite());
                                    if (toBudgetRuleVo.getToBudgetValue() != null) {
                                        toBudget.setToBudgetValue(toBudgetRuleVo.getToBudgetValue().doubleValue());
                                    }
                                    MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.
                                            getByMarketplaceId(advertiseStrategyTopBudgetTemplate.getMarketplaceId());
                                    if(null != m){
                                        toBudget.setSymbol(m.getCurrencyCode());
                                    }
                                    toBudget.setUseTopBudget(toBudgetRuleVo.getUseTopBudget());
                                    toBudgetRule.addToBudgets(toBudget.build());
                                }
                                builder.addToBudgetRules(toBudgetRule.build());
                            }
                        }
                        builder.setTaskId(advertiseStrategyTopBudgetTemplate.getTaskId());
                        builder.setCreateAt(advertiseStrategyTopBudgetTemplate.getCreateAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        builder.setLastUpdateAt(advertiseStrategyTopBudgetTemplate.getLastUpdateAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        builder.setCreateName(advertiseStrategyTopBudgetTemplate.getCreateName());
                        builder.setUpdateName(advertiseStrategyTopBudgetTemplate.getUpdateName());
                        toBudgetRpcList.add(builder.build());
                    }
                    pageBuilder.addAllRows(toBudgetRpcList);
                }
                responseBuilder.setData(pageBuilder.build());
            }
            responseBuilder.setCode(result.getCode());
            responseBuilder.setMsg(result.getMsg());
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void insertTemplate(InsertTemplateRequest request, StreamObserver<InsertTemplateResponse> responseObserver) {
        log.info("新增顶级预算模板 insertTemplate request:{}", request);
        InsertTemplateResponse.Builder responseBuilder = InsertTemplateResponse.newBuilder();
        AddTopBudgetVo addTopBudgetVo = new AddTopBudgetVo();
        addTopBudgetVo.setPuid(request.getPuid());
        addTopBudgetVo.setShopIdList(request.getShopIdList());
        addTopBudgetVo.setMarketplaceId(request.getMarketplaceId());
        addTopBudgetVo.setCreateUid(request.getCreateUid());
        addTopBudgetVo.setUpdateUid(request.getUpdateUid());
        addTopBudgetVo.setCreateName(request.getCreateName());
        addTopBudgetVo.setUpdateName(request.getUpdateName());
        addTopBudgetVo.setIsUse(request.getIsUse());
        addTopBudgetVo.setType(request.getType());
        addTopBudgetVo.setLoginIp(request.getLoginIp());
        OriginToBudgetValueVo originToBudgetValueVo = new OriginToBudgetValueVo();
        originToBudgetValueVo.setUseTopBudget(request.getOriginToBudgetValue().getUseTopBudget());
        if (request.getOriginToBudgetValue().hasToBudgetValue()) {
            originToBudgetValueVo.setToBudgetValue(BigDecimal.valueOf(request.getOriginToBudgetValue().getToBudgetValue()));
        }
        addTopBudgetVo.setOriginToBudgetValueVo(originToBudgetValueVo);
        List<ToBudgetRuleVo> toBudgetRuleVoList = Lists.newArrayList();
        for (ToBudgetRule toBudgetRule : request.getToBudgetRulesList()) {
            List<ToBudget> toBudgets = toBudgetRule.getToBudgetsList().
                    stream().sorted(Comparator.comparing(ToBudget::getStartTimeSite)).collect(Collectors.toList());
            for (ToBudget toBudget : toBudgets) {
                ToBudgetRuleVo toBudgetRuleVo = new ToBudgetRuleVo();
                toBudgetRuleVo.setSiteDate(toBudgetRule.getSiteDate());
                toBudgetRuleVo.setStartTimeSite(toBudget.getStartTimeSite());
                toBudgetRuleVo.setEndTimeSite(toBudget.getEndTimeSite());
                toBudgetRuleVo.setUseTopBudget(toBudget.getUseTopBudget());
                if (toBudget.hasToBudgetValue()) {
                    toBudgetRuleVo.setToBudgetValue(BigDecimal.valueOf(toBudget.getToBudgetValue()));
                }
                toBudgetRuleVoList.add(toBudgetRuleVo);
            }
        }
        addTopBudgetVo.setToBudgetRuleVoList(toBudgetRuleVoList);
        Result<List<ErrorToBudgetMsg>> result = advertiseStrategyToBudgetTemplateService.insertTemplate(addTopBudgetVo, request.getTraceId());
        responseBuilder.setCode(result.getCode());
        responseBuilder.setMsg(result.getMsg());
        if (result.getCode() == Result.SUCCESS) {
            TopBudgetMsg.Builder builder = TopBudgetMsg.newBuilder();
            builder.setErrorSize(result.getData().size());
            builder.setSuccessSize(request.getShopIdList().size() - result.getData().size());
            if (CollectionUtils.isNotEmpty(result.getData())) {
                result.getData().forEach(e->{
                    ErrorTopBudgetMsg.Builder errorBuilder = ErrorTopBudgetMsg.newBuilder();
                    errorBuilder.setShopId(e.getShopId());
                    errorBuilder.setShopName(e.getShopName());
                    errorBuilder.setErrorMsg(e.getErrorMsg());
                    builder.addErrorTopBudgets(errorBuilder.build());
                });
            }
            responseBuilder.setData(builder.build());
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();

    }

    @Override
    public void updateTemplate(UpdateTemplateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("修改顶级预算模板 updateTemplate request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        UpdateTopBudgetVo updateTopBudgetVo = new UpdateTopBudgetVo();
        updateTopBudgetVo.setPuid(request.getPuid());
        updateTopBudgetVo.setShopId(request.getShopId());
        updateTopBudgetVo.setMarketplaceId(request.getMarketplaceId());
        updateTopBudgetVo.setType(request.getType());
        updateTopBudgetVo.setUpdateUid(request.getUpdateUid());
        updateTopBudgetVo.setUpdateName(request.getUpdateName());
        updateTopBudgetVo.setTaskId(request.getTaskId());
        updateTopBudgetVo.setStatus(request.getStatus());
        updateTopBudgetVo.setLoginIp(request.getLoginIp());
        OriginToBudgetValueVo originToBudgetValueVo = new OriginToBudgetValueVo();
        originToBudgetValueVo.setUseTopBudget(request.getOriginToBudgetValue().getUseTopBudget());
        if (request.getOriginToBudgetValue().hasToBudgetValue()) {
            originToBudgetValueVo.setToBudgetValue(BigDecimal.valueOf(request.getOriginToBudgetValue().getToBudgetValue()));
        }
        updateTopBudgetVo.setOriginToBudgetValueVo(originToBudgetValueVo);
        List<ToBudgetRuleVo> toBudgetRuleVoList = Lists.newArrayList();
        for (ToBudgetRule toBudgetRule : request.getToBudgetRulesList()) {
            List<ToBudget> toBudgets = toBudgetRule.getToBudgetsList().
                    stream().sorted(Comparator.comparing(ToBudget::getStartTimeSite)).collect(Collectors.toList());
            for (ToBudget toBudget : toBudgets) {
                ToBudgetRuleVo toBudgetRuleVo = new ToBudgetRuleVo();
                toBudgetRuleVo.setSiteDate(toBudgetRule.getSiteDate());
                toBudgetRuleVo.setStartTimeSite(toBudget.getStartTimeSite());
                toBudgetRuleVo.setEndTimeSite(toBudget.getEndTimeSite());
                toBudgetRuleVo.setUseTopBudget(toBudget.getUseTopBudget());
                if (toBudget.hasToBudgetValue()) {
                    toBudgetRuleVo.setToBudgetValue(BigDecimal.valueOf(toBudget.getToBudgetValue()));
                }
                toBudgetRuleVoList.add(toBudgetRuleVo);
            }
        }
        updateTopBudgetVo.setToBudgetRuleVoList(toBudgetRuleVoList);
        Result<Integer> result = advertiseStrategyToBudgetTemplateService.updateTemplate(updateTopBudgetVo, request.getTraceId());
        responseBuilder.setCode(Int32Value.of(result.getCode()));
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateStatus(UpdateStatusRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("修改顶级预算状态 updateStatus request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        UpdateTopBudgetStatusVo updateTopBudgetStatusVo = new UpdateTopBudgetStatusVo();
        updateTopBudgetStatusVo.setPuid(request.getPuid());
        updateTopBudgetStatusVo.setShopId(request.getShopId());
        updateTopBudgetStatusVo.setMarketplaceId(request.getMarketplaceId());
        updateTopBudgetStatusVo.setStatus(request.getStatus());
        updateTopBudgetStatusVo.setTaskId(request.getTaskId());
        updateTopBudgetStatusVo.setId(request.getId());
        updateTopBudgetStatusVo.setLoginIp(request.getLoginIp());
        updateTopBudgetStatusVo.setUid(request.getUid());
        Result<Integer> result = advertiseStrategyToBudgetTemplateService.updateStatus(updateTopBudgetStatusVo, request.getTraceId());
        responseBuilder.setCode(Int32Value.of(result.getCode()));
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void deleteTemplate(DeleteTemplateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("删除顶级预算模板 deleteTemplate request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        DeleteTopBudgetVo deleteTopBudgetVo = new DeleteTopBudgetVo();
        deleteTopBudgetVo.setPuid(request.getPuid());
        deleteTopBudgetVo.setId(request.getId());
        deleteTopBudgetVo.setAuthedShopIdList(request.getAuthedShopIdList());
        deleteTopBudgetVo.setLoginIp(request.getLoginIp());
        deleteTopBudgetVo.setUid(request.getUid());
        Result<Integer> result = advertiseStrategyToBudgetTemplateService.deleteTemplate(deleteTopBudgetVo, request.getTraceId());
        responseBuilder.setCode(Int32Value.of(result.getCode()));
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void transferTemplate(TransferTemplateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("转移顶级预算模板 transferTemplate request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        TransferTopBudgetVo transferTopBudgetVo = new TransferTopBudgetVo();
        transferTopBudgetVo.setPuid(request.getPuid());
        if (request.hasId()) {
            transferTopBudgetVo.setId(request.getId());
        }
        transferTopBudgetVo.setTemplateId(request.getTemplateId());
        Result<Integer> result = advertiseStrategyToBudgetTemplateService.transferTemplate(transferTopBudgetVo, request.getTraceId());
        responseBuilder.setCode(Int32Value.of(result.getCode()));
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAdmPageList(PageAdmPageRequest request, StreamObserver<PageAdmPageResponse> responseObserver) {
        log.info("管理页列表查询 PageAdmPageResponse request:{}", request);
        PageAdmPageResponse.Builder responseBuilder = PageAdmPageResponse.newBuilder();
        PageTopBudgetParam param = new PageTopBudgetParam();
        String admRule = null;
        param.setPuid(request.getPuid());
//        param.setShopIds(request.getS());
//        param.setMarketplaceId(request.getMarketplaceId());
        param.setTemplateName(request.getTemplateName());
        param.setPageSize(request.getPageSize());
        param.setPageNo(request.getPageNo());
        param.setTraceId(request.getTraceId());
        AdvertiseStrategyTopBudgetTemplate advertiseStrategyTopBudgetTemplate1 = advertiseStrategyTopBudgetTemplateDao.
                selectByStatus(request.getPuid(),request.getShopId());
        if (advertiseStrategyTopBudgetTemplate1 != null) {
            admRule = advertiseStrategyTopBudgetTemplate1.getRule();
            param.setId(advertiseStrategyTopBudgetTemplate1.getId());
        }
        Result<Page<AdvertiseStrategyTopBudgetTemplate>> result = advertiseStrategyToBudgetTemplateService.
                getAdmPageList(param);
        PageAdmPag.Builder pageBuilder = PageAdmPag.newBuilder();
        pageBuilder.setPageNo(request.getPageNo());
        pageBuilder.setPageSize(request.getPageSize());
        if (result.getCode() == Result.SUCCESS) {

            if (StringUtils.isNotBlank(admRule)) {
                pageBuilder.setId(advertiseStrategyTopBudgetTemplate1.getId());
                pageBuilder.setTaskId(advertiseStrategyTopBudgetTemplate1.getTaskId());
                pageBuilder.setShopId(advertiseStrategyTopBudgetTemplate1.getShopId());
                pageBuilder.setMarketplaceId(advertiseStrategyTopBudgetTemplate1.getMarketplaceId());
                pageBuilder.setStatus(advertiseStrategyTopBudgetTemplate1.getStatus());
                MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.
                        getByMarketplaceId(advertiseStrategyTopBudgetTemplate1.getMarketplaceId());
                if(null != m){
                    pageBuilder.setSymbol(m.getCurrencyCode());
                }
                List<ToBudgetRuleVo> toBudgetRuleVoList = JSONUtil.
                        jsonToArray(admRule,ToBudgetRuleVo.class);
                if (CollectionUtils.isNotEmpty(toBudgetRuleVoList)) {
                    Map<Integer,List<ToBudgetRuleVo>> map = toBudgetRuleVoList.stream().
                            collect(Collectors.groupingBy(ToBudgetRuleVo::getSiteDate));
                    for (Integer day : map.keySet()) {
                        ToBudgetRule.Builder toBudgetRule = ToBudgetRule.newBuilder();
                        toBudgetRule.setSiteDate(day);
                        if (day == 0) {
                            toBudgetRule.setSiteDateName("每日");
                        } else {
                            toBudgetRule.setSiteDateName(com.meiyunji.sponsored.service.
                                    cpc.util.Constants.getDateMap().get(day));
                        }
                        for (ToBudgetRuleVo toBudgetRuleVo : map.get(day)) {
                            ToBudget.Builder toBudget = ToBudget.newBuilder();
                            toBudget.setStartTimeSite(toBudgetRuleVo.getStartTimeSite());
                            toBudget.setEndTimeSite(toBudgetRuleVo.getEndTimeSite());
                            if (toBudgetRuleVo.getToBudgetValue() != null) {
                                toBudget.setToBudgetValue(toBudgetRuleVo.getToBudgetValue().doubleValue());
                            }
                            if(null != m){
                                toBudget.setSymbol(m.getCurrencyCode());
                            }
                            toBudget.setUseTopBudget(toBudgetRuleVo.getUseTopBudget());
                            toBudgetRule.addToBudgets(toBudget.build());
                        }
                        pageBuilder.addToBudgetRules(toBudgetRule.build());
                    }
                }
                OriginToBudgetValueVo originToBudgetValueVo = JSONUtil.jsonToObject(advertiseStrategyTopBudgetTemplate1.getOriginValue(),
                        OriginToBudgetValueVo.class);
                pageBuilder.setOriginUseTopBudget(originToBudgetValueVo.getUseTopBudget());
                if (originToBudgetValueVo.getUseTopBudget() && originToBudgetValueVo.getToBudgetValue() != null) {
                    pageBuilder.setOriginToBudgetValue(originToBudgetValueVo.getToBudgetValue().doubleValue());
                }
            }

            Page<AdvertiseStrategyTopBudgetTemplate> page = result.getData();
            List<PageAdmRpc> toBudgetRpcList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(page.getRows())) {
                pageBuilder.setTotalSize(page.getTotalSize());
                pageBuilder.setTotalPage(page.getTotalPage());
                List<ShopAuth> shops = shopAuthDao.getListByPuid(request.getPuid());
                Map<Integer, ShopAuth> shopAuthMap = null;
                if (CollectionUtils.isNotEmpty(shops)) {
                    shopAuthMap = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
                }
                for (AdvertiseStrategyTopBudgetTemplate advertiseStrategyTopBudgetTemplate:page.getRows()) {
                    PageAdmRpc.Builder builder = PageAdmRpc.newBuilder();
                    builder.setPuid(advertiseStrategyTopBudgetTemplate.getPuid());
                    builder.setShopId(advertiseStrategyTopBudgetTemplate.getShopId());
                    builder.setId(advertiseStrategyTopBudgetTemplate.getId());
                    builder.setTaskId(advertiseStrategyTopBudgetTemplate.getTaskId());
                    builder.setProfileId(advertiseStrategyTopBudgetTemplate.getProfileId());
                    builder.setStatus(advertiseStrategyTopBudgetTemplate.getStatus());
                    builder.setMarketplaceId(advertiseStrategyTopBudgetTemplate.getMarketplaceId());
                    if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(advertiseStrategyTopBudgetTemplate.getShopId())) {
                        builder.setShopName(shopAuthMap.get(advertiseStrategyTopBudgetTemplate.getShopId()).getName());
                    }
                    builder.setType(advertiseStrategyTopBudgetTemplate.getType());
                    List<ToBudgetRuleVo> toBudgetRuleVoList = JSONUtil.
                            jsonToArray(advertiseStrategyTopBudgetTemplate.getRule(),ToBudgetRuleVo.class);
                    if (CollectionUtils.isNotEmpty(toBudgetRuleVoList)) {
                        Map<Integer,List<ToBudgetRuleVo>> map = toBudgetRuleVoList.stream().
                                collect(Collectors.groupingBy(ToBudgetRuleVo::getSiteDate));
                        for (Integer day : map.keySet()) {
                            ToBudgetRule.Builder toBudgetRule = ToBudgetRule.newBuilder();
                            toBudgetRule.setSiteDate(day);
                            if (day == 0) {
                                toBudgetRule.setSiteDateName("每日");
                            } else {
                                toBudgetRule.setSiteDateName(com.meiyunji.sponsored.service.
                                        cpc.util.Constants.getDateMap().get(day));
                            }
                            for (ToBudgetRuleVo toBudgetRuleVo : map.get(day)) {
                                ToBudget.Builder toBudget = ToBudget.newBuilder();
                                toBudget.setStartTimeSite(toBudgetRuleVo.getStartTimeSite());
                                toBudget.setEndTimeSite(toBudgetRuleVo.getEndTimeSite());
                                if (toBudgetRuleVo.getToBudgetValue() != null) {
                                    toBudget.setToBudgetValue(toBudgetRuleVo.getToBudgetValue().doubleValue());
                                }
                                MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.
                                        getByMarketplaceId(advertiseStrategyTopBudgetTemplate.getMarketplaceId());
                                if(null != m){
                                    toBudget.setSymbol(m.getCurrencyCode());
                                }
                                toBudget.setUseTopBudget(toBudgetRuleVo.getUseTopBudget());
                                toBudgetRule.addToBudgets(toBudget.build());
                            }
                            builder.addToBudgetRules(toBudgetRule.build());
                        }
                    }
                    builder.setTaskId(advertiseStrategyTopBudgetTemplate.getTaskId());
                    builder.setCreateAt(advertiseStrategyTopBudgetTemplate.getCreateAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    builder.setLastUpdateAt(advertiseStrategyTopBudgetTemplate.getLastUpdateAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    builder.setCreateName(advertiseStrategyTopBudgetTemplate.getCreateName());
                    builder.setUpdateName(advertiseStrategyTopBudgetTemplate.getUpdateName());
                    toBudgetRpcList.add(builder.build());
                }
            }
            pageBuilder.addAllRows(toBudgetRpcList);
            responseBuilder.setData(pageBuilder.build());
        }
        responseBuilder.setCode(result.getCode());
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getSiteList(SiteRequest request, StreamObserver<SiteResponse> responseObserver) {
        log.info("分时顶级预算站点查询 SiteRequest request:{}", request);
        SiteResponse.Builder responseBuilder = SiteResponse.newBuilder();
        Result<List<MarketplaceVo>> result = advertiseStrategyToBudgetTemplateService.
                getSiteIdList(request.getPuid(), request.getUserId(), request.getTraceId());
        if (result.getCode() == Result.SUCCESS) {
            List<MarketplaceVo> marketplaceVoList = result.getData();
            if (CollectionUtils.isNotEmpty(marketplaceVoList)) {
                marketplaceVoList.forEach(e->{
                    Site.Builder builder = Site.newBuilder();
                    if (StringUtils.isNotBlank(e.getMarketplaceId()) && StringUtils.isNotBlank(e.getMarketplaceName())) {
                        builder.setMarketplaceId(e.getMarketplaceId());
                        builder.setMarketplaceName(e.getMarketplaceName());
                        responseBuilder.addData(builder);
                    }
                });
            }
        }
        responseBuilder.setCode(result.getCode());
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }


    @Override
    public void getShopList(ShopRequest request, StreamObserver<ShopResponse> responseObserver) {
        log.info("分时顶级预算店铺查询 ShopRequest request:{}", request);
        ShopResponse.Builder responseBuilder = ShopResponse.newBuilder();
        Result<List<ShopAuth>> result = advertiseStrategyToBudgetTemplateService.getShopIdList(request.getPuid(), request.getMarketplaceId(), request.getUserId(), request.getTraceId());
        if (result.getCode() == Result.SUCCESS) {
            List<ShopAuth> shopAuthList = result.getData();
            if (CollectionUtils.isNotEmpty(shopAuthList)) {
                shopAuthList.forEach(e->{
                    Shop.Builder builder = Shop.newBuilder();
                    builder.setShopId(e.getId());
                    builder.setShopName(e.getName());
                    responseBuilder.addData(builder.build());
                });
            }
        }
        responseBuilder.setCode(result.getCode());
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void batchUpdateStatus(BatchUpdateStatusRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("批量修改顶级预算状态 updateStatus request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        Result<Integer> result = advertiseStrategyToBudgetTemplateService.batchUpdateStatus(request.getPuid(), request.getStatus(), request.getIdList(), request.getAuthedShopIdList(),
                request.getUid(), request.getLoginIp());
        responseBuilder.setCode(Int32Value.of(result.getCode()));
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void batchDeleteTemplate(BatchDeleteTemplateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("批量删除顶级预算模板 deleteTemplate request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        if (CollectionUtils.isEmpty(request.getAuthedShopIdList())) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("未授权");
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        Result<Integer> result = advertiseStrategyToBudgetTemplateService.batchDeleteTemplate(request.getPuid(), request.getIdList(), request.getAuthedShopIdList(), request.getUid(), request.getLoginIp());
        responseBuilder.setCode(Int32Value.of(result.getCode()));
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }
}