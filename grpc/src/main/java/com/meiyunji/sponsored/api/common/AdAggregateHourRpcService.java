package com.meiyunji.sponsored.api.common;

import cn.hutool.core.collection.CollectionUtil;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.AssertUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.PageUtil;
import com.meiyunji.sponsored.grpc.common.*;
import com.meiyunji.sponsored.rpc.adAggregateHour.*;
import com.meiyunji.sponsored.rpc.adCommon.GetCampaignHourReportResponse;
import com.meiyunji.sponsored.service.account.bo.ShopAuthBo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.adTagSystem.service.impl.AdManageTagRelationService;
import com.meiyunji.sponsored.service.cpc.service2.ICpcCommonService;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.doris.dao.IDwsSaleProfitShopDayDao;
import com.meiyunji.sponsored.service.multiple.common.utils.MultipleUtils;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.CampaignAggregateHourParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.PlacementAggregateHourParam;
import com.meiyunji.sponsored.service.reportHour.service.IAmazonAdCampaignHourReportService;
import com.meiyunji.sponsored.service.reportHour.service.IAmazonAdGroupHourReportService;
import com.meiyunji.sponsored.service.reportHour.service.IAmazonAdPlacementHourReportService;
import com.meiyunji.sponsored.service.reportHour.utils.ReportChartUtil;
import com.meiyunji.sponsored.service.reportHour.vo.*;
import com.meiyunji.sponsored.service.util.PbUtil;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2023/10/20 10:00
 * @describe:
 */

@GRpcService
@Slf4j
public class AdAggregateHourRpcService extends AdAggregateHourReportApiServiceGrpc.AdAggregateHourReportApiServiceImplBase {

    @Resource
    private ICpcCommonService cpcCommonService;

    @Resource
    private IScVcShopAuthDao shopAuthDao;

    @Resource
    private CpcPageIdsHandler cpcPageIdsHandler;

    @Autowired
    private IAmazonAdCampaignHourReportService amazonAdCampaignHourReportService;

    @Resource
    private IAmazonAdPlacementHourReportService amazonAdPlacementHourReportService;

    @Autowired
    private IAmazonAdGroupHourReportService amazonAdGroupHourReportService;

    @Autowired
    private CpcShopDataService cpcShopDataService;

    @Resource
    private IDwsSaleProfitShopDayDao dwsSaleProfitShopDayDao;

    @Autowired
    private AdManageTagRelationService adManageTagRelationService;

    /**
     * 查询全部广告活动的汇总小时级数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAllCampaignAggregateHour(CampaignAggregateHourRequest request, StreamObserver<GetCampaignHourReportResponse> responseObserver) {
        log.info("aggregate ad campaign hour report request:{}", request);
        GetCampaignHourReportResponse.Builder builder = GetCampaignHourReportResponse.newBuilder();
        //检查参数
        if (!request.hasPuid() || (!request.hasShopId() && CollectionUtils.isEmpty(request.getShopIdListList())) || !request.hasEndDate() || !request.hasStartDate()
                || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompare() || !request.hasEndDateCompare()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("request param error");
        } else {
            CampaignAggregateHourParamVO param = fillParam(request);
            List<String> temporaryIds = cpcPageIdsHandler.getCampaignIdsTemporary(request.getPuid().getValue(), request.getPageSign(),
                    "", param.getShopId(), new CampaignAggregateHourParamVO[]{param});
            GetCampaignHourReportResponse.CampaignHour campaignHourReport = cpcCommonService.getAggregateCampaignHourReport(request.getPuid().getValue(), temporaryIds, param);
            if (campaignHourReport != null) {
                builder.setData(campaignHourReport);
            }
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAggregateDailyWeeklyOrMonthly(CampaignAggregateHourRequest request, StreamObserver<GetCampaignHourReportResponse> responseObserver) {
        log.info("aggregate ad placement daily weekly or monthly report request:{}", request);
        GetCampaignHourReportResponse.Builder builder = GetCampaignHourReportResponse.newBuilder();
        //检查参数
        if (!request.hasPuid() || (!request.hasShopId() && CollectionUtils.isEmpty(request.getShopIdListList())) || !request.hasEndDate() || !request.hasStartDate()
                || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompare() || !request.hasEndDateCompare()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("request param error");
        } else {
            CampaignAggregateHourParamVO param = fillParam(request);
            List<String> temporaryIds = cpcPageIdsHandler.getCampaignIdsTemporary(request.getPuid().getValue(),
                    request.getPageSign(), "", param.getShopId(), new CampaignAggregateHourParamVO[]{param});
            //返回请求结果
            GetCampaignHourReportResponse.CampaignHour campaignHourReport = cpcCommonService.
                    getAggregateDailyWeeklyAndMonthly(request.getPuid().getValue(), temporaryIds, param, request.getDateModel());
            if (campaignHourReport != null) {
                builder.setData(campaignHourReport);
            }
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAggregateDailyWeeklyOrMonthlyMultiShop(CampaignAggregateHourMultiShopRequest request, StreamObserver<GetCampaignHourReportResponse> responseObserver) {
        log.info("aggregate ad placement daily weekly or monthly report request:{}", request);
        List<ShopAuth> shopAuth = shopAuthDao.getScAndVcByIds(request.getShopIdList());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        GetCampaignHourReportResponse.Builder builder = GetCampaignHourReportResponse.newBuilder();
        //检查参数
        if (!request.hasPuid() || CollectionUtils.isEmpty(request.getShopIdList()) || !request.hasEndDate() || !request.hasStartDate()
                || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompare() || !request.hasEndDateCompare()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("request param error");
        } else {
            CampaignAggregateHourMultiShopParamVO param = fillParam(request);
            //站点过滤
            if (CollectionUtils.isNotEmpty(param.getMarketplaceIds())) {
                List<ShopAuthBo> shopAuthList = shopAuthDao.getShopAuthBoByMarketPlaceAndIds(param.getPuid(), param.getShopIds(), param.getMarketplaceIds());
                if (CollectionUtils.isEmpty(shopAuthList)) {
                    param.setShopIds(shopAuthList.stream().map(ShopAuthBo::getId).collect(Collectors.toList()));
                }
            }
            //根据标签组Id或标签Id去查询广告活动Id
            List<String> temporaryIds = new ArrayList<>();
            List<String> relationIds = new ArrayList<>();
            //层级类型：0-广告活动
            int type = 0;
            if (CollectionUtils.isNotEmpty(param.getShopIds())) {
                if (StringUtils.isNotBlank(request.getPageSign())) {
                    temporaryIds = cpcPageIdsHandler.getTemporaryAggregateIds(param.getPageSign(), request.getTagGroupId());
                    relationIds = adManageTagRelationService.getRelationIdByTagIds(param.getPuid(), temporaryIds, type, param.getShopIds());
                } else {
                    //单个标签的查询
                    relationIds = adManageTagRelationService.getRelationIdByTagId(param.getPuid(), param.getTagId(), type, param.getShopIds());
                }
            }
            //多个广告活动id重复计算报告数据
            param.setMultipleIdComputation(true);
            //返回请求结果
            GetCampaignHourReportResponse.CampaignHour campaignHourReport = cpcCommonService.
                    getAggregateDailyWeeklyAndMonthlyMultiShop(request.getPuid().getValue(), relationIds, param, request.getDateModel());
            if (campaignHourReport != null) {
                builder.setData(campaignHourReport);
            }
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAggregateWeeklySuperpositionMultiShop(CampaignAggregateHourMultiShopRequest request, StreamObserver<GetCampaignWeekReportResponsePb.GetCampaignWeekReportResponse> responseObserver) {
        log.info("aggregate ad placement weekly report request:{}", request);
        try {
            GetCampaignWeekReportResponsePb.GetCampaignWeekReportResponse.Builder builder =
                    GetCampaignWeekReportResponsePb.GetCampaignWeekReportResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || CollectionUtils.isEmpty(request.getShopIdList()) || !request.hasEndDate() || !request.hasStartDate()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("request param error");
            } else {
                CampaignHourParamMultiShop param = new CampaignHourParamMultiShop();
                BeanUtils.copyProperties(request, param);
                param.setPuid(request.getPuid().getValue());
                param.setShopIds(request.getShopIdList());
                param.setStartDate(request.getStartDate());
                param.setEndDate(request.getEndDate());
                if (StringUtils.isNotBlank(request.getOrderField())) {
                    param.setOrderField(request.getOrderField());
                }
                if (StringUtils.isNotBlank(request.getOrderType())) {
                    param.setOrderType(request.getOrderType());
                }
                param.setPuid(request.getPuid().getValue());
                param.setPageNo(request.getPageNo().getValue());
                param.setPageSize(request.getPageSize().getValue());
                param.setUid(request.getUid().getValue());
                param.setCurrency(request.getCurrency());
                param.setMarketplaceIds(request.getMarketplaceIdList());
                if (StringUtils.isNotBlank(request.getTagId())) {
                    param.setTagId(request.getTagId());
                }
                if (StringUtils.isNotBlank(request.getTagGroupId())) {
                    param.setGroupId(request.getTagGroupId());
                }
                if (request.hasIsCompare()) {
                    param.setIsCompare(request.getIsCompare().getValue());
                }
                //站点过滤
                if (CollectionUtils.isNotEmpty(param.getMarketplaceIds())) {
                    List<ShopAuthBo> shopAuthList = shopAuthDao.getShopAuthBoByMarketPlaceAndIds(param.getPuid(), param.getShopIds(), param.getMarketplaceIds());
                    if (CollectionUtils.isEmpty(shopAuthList)) {
                        param.setShopIds(shopAuthList.stream().map(ShopAuthBo::getId).collect(Collectors.toList()));
                    }
                }
                //根据标签组Id或标签Id去查询广告活动Id
                List<String> temporaryIds = new ArrayList<>();
                List<String> relationIds = new ArrayList<>();
                //层级类型：0-广告活动
                int type = 0;
                if (CollectionUtils.isNotEmpty(param.getShopIds())) {
                    if (StringUtils.isNotBlank(request.getPageSign())) {
                        temporaryIds = cpcPageIdsHandler.getTemporaryAggregateIds(param.getPageSign(), request.getTagGroupId());
                        relationIds = adManageTagRelationService.getRelationIdByTagIds(param.getPuid(), temporaryIds, type, param.getShopIds());
                    } else {
                        //存在标签Id则通过标签Id去进行查询
                        //单个标签的查询
                        relationIds = adManageTagRelationService.getRelationIdByTagId(param.getPuid(), param.getTagId(), type, param.getShopIds());
                    }
                }
                param.setMultipleIdComputation(true);
                //返回请求结果
                List<AdCampaignWeekDayVo> list =
                        amazonAdCampaignHourReportService.getAggregateWeeklySuperpositionListMultiShop(param.getPuid(), relationIds, param);

                BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getPuid(), param.getShopIds(), param.getStartDate().replace("-", ""), param.getEndDate().replace("-", ""));

                if (CollectionUtils.isNotEmpty(list)) {
                    for (AdCampaignWeekDayVo vo : list) {
                        vo.setAcots(shopSalesByDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(shopSalesByDate, 4, RoundingMode.HALF_UP));
                        vo.setAsots(shopSalesByDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().multiply(new BigDecimal("100")).divide(shopSalesByDate, 4, RoundingMode.HALF_UP));
                    }
                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdCampaignWeekDayVo.class);
                    if (isSorted) {
                        PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
                    }
                    GetCampaignWeekReportResponsePb.GetCampaignWeekReportResponse.CampaignWeek.Builder reportBuilder =
                            GetCampaignWeekReportResponsePb.GetCampaignWeekReportResponse.CampaignWeek.newBuilder();
                    reportBuilder.addAllList(list.stream().filter(Objects::nonNull)
                            .map(key -> PbUtil.toCampaignPb(key, shopSalesByDate)).collect(Collectors.toList()));
                    reportBuilder.setSummary(PbUtil.toCampaignPb(summaryAllCampaignOfWeekVo(list), shopSalesByDate));
                    reportBuilder.addAllChart(ReportChartUtil.getCampaignWeekChartData(list, false));
                    //对比数据,chart图数据
                    if (param.getIsCompare() != null && param.getIsCompare() == 1) {
                        List<AdCampaignWeekDayVo> compareList = list.stream().map(item-> {
                            AdCampaignWeekDayVo vo = new AdCampaignWeekDayVo();
                            vo.setWeekDay(item.getWeekDay());
                            vo.setClicks(item.getClicksCompare());
                            vo.setImpressions(item.getImpressionsCompare());
                            vo.setAdSale(item.getAdSaleCompare());
                            vo.setAdCost(item.getAdCostCompare());
                            vo.setAdOrderNum(item.getAdOrderNumCompare());
                            vo.setAdSaleNum(item.getAdSaleNumCompare());
                            vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                            vo.setAcos(item.getAcosCompare());
                            vo.setRoas(item.getRoasCompare());
                            vo.setCtr(item.getCtrCompare());
                            vo.setCvr(item.getCvrCompare());
                            return vo;
                        }).collect(Collectors.toList());
                        reportBuilder.addAllChart(ReportChartUtil.getCampaignWeekChartData(compareList, true));
                    }
                    builder.setCode(Int32Value.of(Result.SUCCESS));
                    builder.setData(reportBuilder.build());
                }
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    @Override
    public void getAggregateWeeklySuperposition(CampaignAggregateHourRequest request, StreamObserver<GetCampaignWeekReportResponsePb.GetCampaignWeekReportResponse> responseObserver) {
        log.info("aggregate ad placement weekly report request:{}", request);
        GetCampaignWeekReportResponsePb.GetCampaignWeekReportResponse.Builder builder =
                GetCampaignWeekReportResponsePb.GetCampaignWeekReportResponse.newBuilder();
        //检查参数
        if (!request.hasPuid() || (!request.hasShopId() && CollectionUtils.isEmpty(request.getShopIdListList())) || !request.hasEndDate() || !request.hasStartDate()
                || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompare() || !request.hasEndDateCompare()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("request param error");
        } else {
            // grpc参数转param
            CampaignHourParam param = grpcToParam(request);
            // 店铺状态校验
            List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(param.getPuid(), param.getShopIdList());
            if (CollectionUtils.isEmpty(shopAuths)) {
                throw new SponsoredBizException("店铺未授权");
            }
            // 取店铺销售额
            BigDecimal shopSalesByDate = dwsSaleProfitShopDayDao.sumShopSaleByDateRange(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate(), MultipleUtils.changeRate(shopAuths));
            List<AdCampaignWeekDayVo> list = amazonAdCampaignHourReportService.getAggregateWeeklySuperpositionList(shopAuths, param.getPuid(), param);
            if (CollectionUtils.isNotEmpty(list)) {
                for (AdCampaignWeekDayVo vo : list) {
                    vo.setAcots(shopSalesByDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(shopSalesByDate, 4, RoundingMode.HALF_UP));
                    vo.setAsots(shopSalesByDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().multiply(new BigDecimal("100")).divide(shopSalesByDate, 4, RoundingMode.HALF_UP));
                }
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                        Constants.isADOrderField(param.getOrderField(), AdCampaignWeekDayVo.class);
                if (isSorted) {
                    PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
                }
                GetCampaignWeekReportResponsePb.GetCampaignWeekReportResponse.CampaignWeek.Builder reportBuilder =
                        GetCampaignWeekReportResponsePb.GetCampaignWeekReportResponse.CampaignWeek.newBuilder();
                reportBuilder.addAllList(list.stream().filter(Objects::nonNull)
                        .map(key -> PbUtil.toCampaignPb(key, shopSalesByDate)).collect(Collectors.toList()));
                reportBuilder.setSummary(PbUtil.toCampaignPb(summaryAllCampaignOfWeekVo(list), shopSalesByDate));
                reportBuilder.addAllChart(ReportChartUtil.getCampaignWeekChartData(list, false));
                reportBuilder.setCurrency(MultipleUtils.getCurrency(shopAuths));
                if (Objects.nonNull(param.getIsCompare()) && param.getIsCompare() == 1) {
                    List<AdCampaignWeekDayVo> compareList = list.stream().map(item -> {
                        AdCampaignWeekDayVo vo = new AdCampaignWeekDayVo();
                        vo.setWeekDay(item.getWeekDay());
                        vo.setClicks(item.getClicksCompare());
                        vo.setImpressions(item.getImpressionsCompare());
                        vo.setAdCost(item.getAdCostCompare());
                        vo.setAdSale(item.getAdSaleCompare());
                        vo.setAdOrderNum(item.getAdOrderNumCompare());
                        vo.setAdSaleNum(item.getAdSaleNumCompare());
                        vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                        vo.setAcos(item.getAcosCompare());
                        vo.setRoas(item.getRoasCompare());
                        vo.setCtr(item.getCtrCompare());
                        vo.setCvr(item.getCvrCompare());
                        return vo;
                    }).collect(Collectors.toList());
                    reportBuilder.addAllChart(ReportChartUtil.getCampaignWeekChartData(compareList, true));
                }
                builder.setCode(Int32Value.of(Result.SUCCESS));
                builder.setData(reportBuilder.build());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * grpc参数转param
     */
    private static CampaignHourParam grpcToParam(CampaignAggregateHourRequest request) {
        CampaignHourParam param = new CampaignHourParam();
        BeanUtils.copyProperties(request, param);
        param.setCampaignId(request.getCampaignId());
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setStartDate(request.getStartDate());
        param.setEndDate(request.getEndDate());
        param.setOrderField(request.getOrderField());
        param.setOrderType(request.getOrderType());
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());
        param.setUid(request.getUid().getValue());
        param.setShopIdList(request.getShopIdListList());
        param.setIsCompare(request.getIsCompare().getValue());
        param.setStartDateCompare(request.getStartDateCompare());
        param.setEndDateCompare(request.getEndDateCompare());
        // 兼容旧单店铺逻辑
        if(CollectionUtils.isEmpty(param.getShopIdList()) && param.getShopId() != null){
            param.setShopIdList(CollectionUtil.newArrayList(param.getShopId()));
        }
        return param;
    }

    private static AdCampaignWeekDayVo summaryAllCampaignOfWeekVo(List<AdCampaignWeekDayVo> list) {
        AdCampaignWeekDayVo vo = new AdCampaignWeekDayVo();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        BigDecimal vcpmCost = BigDecimal.ZERO;
        Long vcpmImpressions = 0L;
        long totalImpressions = 0L;
        long totalClicks = 0L;
        BigDecimal totalAdSelfSale = BigDecimal.ZERO;
        BigDecimal totalAdSale = BigDecimal.ZERO;

        for (AdCampaignWeekDayVo ad : list) {
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
            vo.setAdCostPercentage(MathUtil.add(vo.getAdCostPercentage(), ad.getAdCostPercentage()));
            vo.setAdSalePercentage(MathUtil.add(vo.getAdSalePercentage(), ad.getAdSalePercentage()));
            vo.setAdOrderNumPercentage(MathUtil.add(vo.getAdOrderNumPercentage(), ad.getAdOrderNumPercentage()));
            vo.setOrderNumPercentage(MathUtil.add(vo.getOrderNumPercentage(), ad.getOrderNumPercentage()));
            vo.setViewableImpressions(MathUtil.add(vo.getViewableImpressions(), ad.getViewableImpressions()));
            vo.setOrdersNewToBrand(MathUtil.add(vo.getOrdersNewToBrand(), ad.getOrdersNewToBrand()));
            vo.setUnitsOrderedNewToBrand(MathUtil.add(vo.getUnitsOrderedNewToBrand(), ad.getUnitsOrderedNewToBrand()));
            vo.setSalesNewToBrand(MathUtil.add(vo.getSalesNewToBrand(), ad.getSalesNewToBrand()));
            if (ad.getVcpmCost() != null) {
                vcpmCost = vcpmCost.add(ad.getVcpmCost());
            }
            if (ad.getVcpmImpressions() != null) {
                vcpmImpressions += ad.getVcpmImpressions();
            }
            if (ad.getTotalClicks() != null) {
                totalClicks += ad.getTotalClicks();
            }
            if (ad.getTotalImpressions() != null) {
                totalImpressions += ad.getTotalImpressions();
            }

            if (ad.getTotalAdSale() != null) {
                totalAdSale = totalAdSale.add(ad.getTotalAdSale());
            }

            if (ad.getTotalAdSelfSale() != null) {
                totalAdSelfSale = totalAdSelfSale.add(ad.getTotalAdSelfSale());
            }
            vo.setAdCostCompare(MathUtil.add(vo.getAdCostCompare(), ad.getAdCostCompare()));
            vo.setClicksCompare(MathUtil.add(vo.getClicksCompare(), ad.getClicksCompare()));
            vo.setImpressionsCompare(MathUtil.add(vo.getImpressionsCompare(), ad.getImpressionsCompare()));
            vo.setAdSaleNumCompare(MathUtil.add(vo.getAdSaleNumCompare(), ad.getAdSaleNumCompare()));
            vo.setAdSaleCompare(MathUtil.add(vo.getAdSaleCompare(), ad.getAdSaleCompare()));
            vo.setAdOrderNumCompare(MathUtil.add(vo.getAdOrderNumCompare(), ad.getAdOrderNumCompare()));
        }


        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        if (vo.getAdCostPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdCostPercentage()) == 0) {
            vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setAdCostPercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getAdSalePercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdSalePercentage()) == 0) {
            vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setAdSalePercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getAdOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdOrderNumPercentage()) == 0) {
            vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setAdOrderNumPercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getOrderNumPercentage()) == 0) {
            vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setOrderNumPercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), totalImpressions));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(totalClicks, vo.getViewableImpressions()));
        vo.setVcpm(MathUtil.divideByThousand(vcpmCost, vcpmImpressions));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(totalAdSelfSale, BigDecimal.valueOf(vo.getSelfAdOrderNum())));
        vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(totalAdSale.subtract(totalAdSelfSale), BigDecimal.valueOf(vo.getOtherAdOrderNum())));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));

        vo.setAdCostPerClickCompare(MathUtil.divideByZero(vo.getAdCostCompare(), BigDecimal.valueOf(vo.getClicksCompare())));
        vo.setAcosCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCostCompare().multiply(new BigDecimal("100")).divide(vo.getAdSaleCompare(), 4, RoundingMode.HALF_UP));
        vo.setRoasCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSaleCompare().divide(vo.getAdCostCompare(), 4, RoundingMode.HALF_UP));
        vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicksCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressionsCompare())));
        vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNumCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicksCompare())));
        vo.afterPropertiesSet();//为各对比率属性设值
        return vo;
    }

    @Override
    public void getPlacementAggregateHour(PlacementAggregateRequest request,
                                          StreamObserver<GetPlacementHourReportResponsePb.GetPlacementHourReportResponse> responseObserver) {
        log.info("aggregate ad placement hourly report request:{}", request);
        GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.Builder builder = GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.newBuilder();
        AdPageBasicData pageBasicData = request.getPageBasic();
        Integer puid = Optional.of(pageBasicData.getPuid()).map(Int32Value::getValue).orElse(null);
        //检查参数
        if (!pageBasicData.hasPuid() || !pageBasicData.hasShopId() || !pageBasicData.hasEndDate() || !pageBasicData.hasStartDate()
                || (pageBasicData.hasIsCompare() && pageBasicData.getIsCompare().getValue() == 1 &&
                (!pageBasicData.hasStartDateCompare() || !pageBasicData.hasEndDateCompare()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("request param error");
        } else {
            //fill properties to vo
            PlacementAggregateHourVo param = new PlacementAggregateHourVo();
            param.setAdPageBasicData(pageBasicData);
            param.setCampaignId(request.getCampaignId());
            param.setPredicate(request.getPredicate());
            param.setWeeks(request.getWeeks());
            GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour placementHourReport =
                    cpcCommonService.getPlacementAggregateHourReport(puid, param);
            if (placementHourReport != null) {
                builder.setData(placementHourReport);
            }
            builder.setCode(Int32Value.of(Result.SUCCESS));

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getPlacementDailyWeeklyOrMonthly(PlacementAggregateRequest request, StreamObserver<GetPlacementHourReportResponsePb.GetPlacementHourReportResponse> responseObserver) {
        log.info("aggregate ad placement daily weekly or monthly request:{}", request);
        GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.Builder builder = GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.newBuilder();
        AdPageBasicData pageBasicData = request.getPageBasic();
        Integer puid = Optional.of(pageBasicData.getPuid()).map(Int32Value::getValue).orElse(null);
        //检查参数
        if (!pageBasicData.hasPuid() || !pageBasicData.hasShopId() || !pageBasicData.hasEndDate() || !pageBasicData.hasStartDate()
                || (pageBasicData.hasIsCompare() && pageBasicData.getIsCompare().getValue() == 1 &&
                (!pageBasicData.hasStartDateCompare() || !pageBasicData.hasEndDateCompare()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("request param error");
        } else {
            //fill properties to vo
            PlacementAggregateHourVo param = new PlacementAggregateHourVo();
            param.setAdPageBasicData(pageBasicData);
            param.setCampaignId(request.getCampaignId());
            param.setPredicate(request.getPredicate());
            param.setCampaignSite(request.getCampaignSite());
            param.setWeeks(request.getWeeks());
            GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour placementHourReport =
                    cpcCommonService.getPlacementDailyWeeklyAndMonthly(puid, param, pageBasicData.getDateModel());
            if (placementHourReport != null) {
                builder.setData(placementHourReport);
            }
            builder.setCode(Int32Value.of(Result.SUCCESS));

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getPlacementWeeklySuperposition(GetPlacementWeekReportRequestPb.GetPlacementWeekReportRequest request,
                                                StreamObserver<GetPlacementWeekReportResponsePb.GetPlacementWeekReportResponse> responseObserver) {
        log.info("aggregate ad placement weekly superposition request:{}", request);
        try {
            GetPlacementWeekReportResponsePb.GetPlacementWeekReportResponse.Builder builder =
                    GetPlacementWeekReportResponsePb.GetPlacementWeekReportResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("request param error");
            } else {
                PlacementHourParam param = PlacementHourParam.builder()
                        .Predicate(request.getPredicate())
                        .puid(request.getPuid().getValue())
                        .shopId(request.getShopId().getValue())
                        .startDate(request.getStartDateStr())
                        .endDate(request.getEndDateStr())
                        .orderField(request.getOrderField())
                        .orderType(request.getOrderType())
                        .startDateCompare(request.getStartDateCompareStr())
                        .endDateCompare(request.getEndDateCompareStr())
                        .pageSign(request.getPageSign())
                        .build();
                List<AdPlacementWeekDayVo> list =
                        amazonAdPlacementHourReportService.getPlacementWeeklySuperpositionList(param.getPuid(), param);
                GetPlacementWeekReportResponsePb.GetPlacementWeekReportResponse.PlacementWeek.Builder reportBuilder =
                        GetPlacementWeekReportResponsePb.GetPlacementWeekReportResponse.PlacementWeek.newBuilder();
                if (CollectionUtils.isNotEmpty(list)) {
                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdPlacementWeekDayVo.class);
                    if (isSorted) {
                        PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
                    }
                }
                reportBuilder.addAllList(list.stream().filter(Objects::nonNull)
                        .map(PbUtil::toPlacementPb).collect(Collectors.toList()));
                reportBuilder.setSummary(PbUtil.toPlacementPb(AdCommonRpcService.summaryPlacementOfWeekVo(list)));
                reportBuilder.addAllChart(ReportChartUtil.getPlacementWeekChartData(list, false));
                builder.setCode(Int32Value.of(Result.SUCCESS));
                builder.setData(reportBuilder.build());
            }

            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("puid:{},shopId:{},campaignId:{}，predicate:{}，get placement data error",
                    request.getPuid().getValue(), request.getShopId().getValue(), request.getCampaignId(), request.getPredicate(), e);
            responseObserver.onError(e);
        }
    }

    @Override
    public void getIdsTemporaryBySign(IdsTemporaryBySignRequest request, StreamObserver<IdsTemporaryBySignResponse> responseObserver) {
        log.info("aggregate get id list temporary by sign request:{}", request);
        IdsTemporaryBySignResponse.Builder builder = IdsTemporaryBySignResponse.newBuilder();
        String pageSign = request.getPageSign();
        //query temporary by page sign
        boolean dataReady = cpcPageIdsHandler.getTemporaryIdsReady(pageSign, "");
        IdsTemporaryBySignResponse.IdsTemporaryDataReady.Builder redayBuilder = IdsTemporaryBySignResponse.IdsTemporaryDataReady.newBuilder();
        redayBuilder.setDataReady(dataReady);
        builder.setData(redayBuilder.build());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getGroupAggregateHour(GroupAggregateRequest request, StreamObserver<GetGroupHourReportResponsePb.GetGroupHourReportResponse> responseObserver) {
        log.info("aggregate ad group hourly report request:{}", request);
        GetGroupHourReportResponsePb.GetGroupHourReportResponse.Builder builder = GetGroupHourReportResponsePb.GetGroupHourReportResponse.newBuilder();
        AdPageBasicData pageBasicData = request.getPageBasic();
        Integer puid = Optional.of(pageBasicData.getPuid()).map(Int32Value::getValue).orElse(null);
        //检查参数
        if (!pageBasicData.hasPuid() || !pageBasicData.hasShopId() || !pageBasicData.hasEndDate() || !pageBasicData.hasStartDate()
                || (pageBasicData.hasIsCompare() && pageBasicData.getIsCompare().getValue() == 1 &&
                (!pageBasicData.hasStartDateCompare() || !pageBasicData.hasEndDateCompare()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("request param error");
        } else {
            //fill properties to vo
            GroupAggregateHourVo param = new GroupAggregateHourVo();
            param.setAdPageBasicData(pageBasicData);
            param.setCampaignId(request.getCampaignId());
            param.setAdGroupId(request.getAdGroupId());
            param.setWeeks(request.getWeeks());
            GetGroupHourReportResponsePb.GetGroupHourReportResponse.GroupHour placementHourReport =
                    cpcCommonService.getGroupAggregateHourReport(puid, param);
            if (placementHourReport != null) {
                builder.setData(placementHourReport);
            }
            builder.setCode(Int32Value.of(Result.SUCCESS));

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getGroupDailyWeeklyOrMonthly(GroupAggregateRequest request, StreamObserver<GetGroupHourReportResponsePb.GetGroupHourReportResponse> responseObserver) {
        log.info("aggregate ad group daily weekly or monthly request:{}", request);
        GetGroupHourReportResponsePb.GetGroupHourReportResponse.Builder builder = GetGroupHourReportResponsePb.GetGroupHourReportResponse.newBuilder();
        AdPageBasicData pageBasicData = request.getPageBasic();
        Integer puid = Optional.of(pageBasicData.getPuid()).map(Int32Value::getValue).orElse(null);
        //检查参数
        if (!pageBasicData.hasPuid() || !pageBasicData.hasShopId() || !pageBasicData.hasEndDate() || !pageBasicData.hasStartDate()
                || (pageBasicData.hasIsCompare() && pageBasicData.getIsCompare().getValue() == 1 &&
                (!pageBasicData.hasStartDateCompare() || !pageBasicData.hasEndDateCompare()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("request param error");
        } else {
            //fill properties to vo
            GroupAggregateHourVo param = new GroupAggregateHourVo();
            param.setAdPageBasicData(pageBasicData);
            param.setCampaignId(request.getCampaignId());
            param.setAdGroupId(request.getAdGroupId());
            param.setWeeks(request.getWeeks());
            param.setType(pageBasicData.getType());
            GetGroupHourReportResponsePb.GetGroupHourReportResponse.GroupHour groupHourReport =
                    cpcCommonService.getGroupDailyWeeklyAndMonthly(puid, param, pageBasicData.getDateModel());
            if (groupHourReport != null) {
                builder.setData(groupHourReport);
            }
            builder.setCode(Int32Value.of(Result.SUCCESS));

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getGroupWeeklySuperposition(GetGroupWeekReportRequestPb.GetGroupWeekReportRequest request, StreamObserver<GetGroupWeekReportResponsePb.GetGroupWeekReportResponse> responseObserver) {
        log.info("aggregate ad placement weekly superposition request:{}", request);
        try {
            GetGroupWeekReportResponsePb.GetGroupWeekReportResponse.Builder builder =
                    GetGroupWeekReportResponsePb.GetGroupWeekReportResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("request param error");
            } else {
                GroupHourParam param = GroupHourParam.builder()
                        .adGroupId(request.getAdGroupId())
                        .puid(request.getPuid().getValue())
                        .shopId(request.getShopId().getValue())
                        .startDate(request.getStartDateStr())
                        .endDate(request.getEndDateStr())
                        .orderField(request.getOrderField())
                        .orderType(request.getOrderType())
                        .startDateCompare(request.getStartDateCompareStr())
                        .endDateCompare(request.getEndDateCompareStr())
                        .pageSign(request.getPageSign())
                        .type(request.getType())
                        .build();
                List<AdGroupWeekDayVo> list =
                        amazonAdGroupHourReportService.getGroupWeeklySuperpositionList(param.getPuid(), param);
                GetGroupWeekReportResponsePb.GetGroupWeekReportResponse.GroupWeek.Builder reportBuilder =
                        GetGroupWeekReportResponsePb.GetGroupWeekReportResponse.GroupWeek.newBuilder();
                if (CollectionUtils.isNotEmpty(list)) {
                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdGroupWeekDayVo.class);
                    if (isSorted) {
                        PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
                    }
                }
                reportBuilder.addAllList(list.stream().filter(Objects::nonNull)
                        .map(PbUtil::toGroupPb).collect(Collectors.toList()));
                reportBuilder.setSummary(PbUtil.toGroupPb(AdCommonRpcService.summaryGroupOfWeekVo(list)));
                reportBuilder.addAllChart(ReportChartUtil.getGroupWeekChartData(list, false));
                builder.setCode(Int32Value.of(Result.SUCCESS));
                builder.setData(reportBuilder.build());
            }

            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("puid:{},shopId:{},campaignId:{}，adGroupId:{}，get placement data error",
                    request.getPuid().getValue(), request.getShopId().getValue(), request.getCampaignId(), request.getAdGroupId(), e);
            responseObserver.onError(e);
        }
    }

    /**
     * 查询全部广告活动的汇总小时级数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAggregateCampaignHour(AggregateCampaignHourRequest request, StreamObserver<GetCampaignHourReportResponse> responseObserver) {
        log.info("aggregate ad campaign hour report request:{}", request);
        GetCampaignHourReportResponse.Builder builder = GetCampaignHourReportResponse.newBuilder();
        //检查参数
        if (!request.hasPuid() || CollectionUtils.isEmpty(request.getShopIdList()) || !request.hasEndDate() || !request.hasStartDate()
                || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 && (!request.hasStartDateCompare() || !request.hasEndDateCompare()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("request param error");
        } else {
            CampaignAggregateHourParam param = new CampaignAggregateHourParam();
            BeanUtils.copyProperties(request, param);
            param.setPuid(request.getPuid().getValue());
            param.setIsCompare(request.getIsCompare().getValue());
            param.setStartDateCompare(request.getStartDateCompare());
            param.setEndDateCompare(request.getEndDateCompare());
            param.setMarketplaceId(request.getMarketplaceId());
            GetCampaignHourReportResponse.CampaignHour campaignHourReport = cpcCommonService.getAggregateCampaignHourReport(request.getPuid().getValue(), param);
            if (campaignHourReport != null) {
                builder.setData(campaignHourReport);
            }
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAggregatePlacementHour(AggregatePlacementHourRequest request,
                                          StreamObserver<GetPlacementHourReportResponsePb.GetPlacementHourReportResponse> responseObserver) {
        log.info("aggregate ad placement hourly report request:{}", request);
        GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.Builder builder = GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.newBuilder();
        //检查参数
        if (!request.hasPuid() || CollectionUtils.isEmpty(request.getShopIdList()) || !request.hasEndDate() || !request.hasStartDate()
                || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 &&
                (!request.hasStartDateCompare() || !request.hasEndDateCompare()))) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("request param error");
        } else {
            Integer puid = Optional.of(request.getPuid()).map(Int32Value::getValue).orElse(null);
            //fill properties to vo
            PlacementAggregateHourParam param = new PlacementAggregateHourParam();
            BeanUtils.copyProperties(request, param);
            param.setPuid(request.getPuid().getValue());
            param.setIsCompare(request.getIsCompare().getValue());
            param.setMarketplaceId(request.getMarketplaceId());
            GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour placementHourReport =
                    cpcCommonService.getPlacementAggregateHourReport(puid, param);
            if (placementHourReport != null) {
                builder.setData(placementHourReport);
            }
            builder.setCode(Int32Value.of(Result.SUCCESS));

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private CampaignAggregateHourParamVO fillParam(CampaignAggregateHourRequest request) {
        CampaignAggregateHourParamVO param = new CampaignAggregateHourParamVO();
        BeanUtils.copyProperties(request, param);
        param.setCampaignId(request.getCampaignId());
        param.setWeeks(request.getWeeks());
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setStartDate(request.getStartDate());
        param.setEndDate(request.getEndDate());
        param.setEndDateCompare(request.getEndDateCompare());
        param.setStartDateCompare(request.getStartDateCompare());
        param.setOrderField(request.getOrderField());
        param.setOrderType(request.getOrderType());
        param.setShopIdList(request.getShopIdListList());
        if (request.hasIsCompare()) {
            param.setIsCompare(request.getIsCompare().getValue());
        }


        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            Optional.of(request).filter(CampaignAggregateHourRequest::hasImpressionsMin).
                    map(CampaignAggregateHourRequest::getImpressionsMin).ifPresent(v -> param.setImpressionsMin(v.getValue()));
            Optional.of(request).filter(CampaignAggregateHourRequest::hasImpressionsMax).
                    map(CampaignAggregateHourRequest::getImpressionsMax).ifPresent(v -> param.setImpressionsMax(v.getValue()));
            Optional.of(request).filter(CampaignAggregateHourRequest::hasClicksMin).
                    map(CampaignAggregateHourRequest::getClicksMin).ifPresent(v -> param.setClicksMin(v.getValue()));
            Optional.of(request).filter(CampaignAggregateHourRequest::hasClicksMax).
                    map(CampaignAggregateHourRequest::getClicksMax).ifPresent(v -> param.setClicksMax(v.getValue()));
            Optional.of(request).filter(CampaignAggregateHourRequest::hasClickRateMin).
                    map(CampaignAggregateHourRequest::getClickRateMin).ifPresent(param::setClickRateMin);
            Optional.of(request).filter(CampaignAggregateHourRequest::hasClickRateMax).
                    map(CampaignAggregateHourRequest::getClickRateMax).ifPresent(param::setClickRateMax);
            Optional.of(request).filter(CampaignAggregateHourRequest::hasCostMin).
                    map(CampaignAggregateHourRequest::getCostMin).ifPresent(param::setCostMin);
            Optional.of(request).filter(CampaignAggregateHourRequest::hasCostMax).
                    map(CampaignAggregateHourRequest::getCostMax).ifPresent(param::setCostMax);
            Optional.of(request).filter(CampaignAggregateHourRequest::hasCpcMin).
                    map(CampaignAggregateHourRequest::getCpcMin).ifPresent(param::setCpcMin);
            Optional.of(request).filter(CampaignAggregateHourRequest::hasCpcMax).
                    map(CampaignAggregateHourRequest::getCpcMax).ifPresent(param::setCpcMax);
            Optional.of(request).filter(CampaignAggregateHourRequest::hasOrderNumMin).
                    map(CampaignAggregateHourRequest::getOrderNumMin).ifPresent(v -> param.setOrderNumMin(v.getValue()));
            Optional.of(request).filter(CampaignAggregateHourRequest::hasOrderNumMax).
                    map(CampaignAggregateHourRequest::getOrderNumMax).ifPresent(v -> param.setOrderNumMax(v.getValue()));
            Optional.of(request).filter(CampaignAggregateHourRequest::hasSalesMin).
                    map(CampaignAggregateHourRequest::getSalesMin).ifPresent(param::setSalesMin);
            Optional.of(request).filter(CampaignAggregateHourRequest::hasSalesMax).
                    map(CampaignAggregateHourRequest::getSalesMax).ifPresent(param::setSalesMax);
            Optional.of(request).filter(CampaignAggregateHourRequest::hasAcosMin).
                    map(CampaignAggregateHourRequest::getAcosMin).ifPresent(param::setAcosMin);
            Optional.of(request).filter(CampaignAggregateHourRequest::hasAcosMax).
                    map(CampaignAggregateHourRequest::getAcosMax).ifPresent(param::setAcosMax);
            Optional.of(request).filter(CampaignAggregateHourRequest::hasRoasMin).
                    map(CampaignAggregateHourRequest::getRoasMin).ifPresent(param::setRoasMin);
            Optional.of(request).filter(CampaignAggregateHourRequest::hasRoasMax).
                    map(CampaignAggregateHourRequest::getRoasMax).ifPresent(param::setRoasMax);
            Optional.of(request).filter(CampaignAggregateHourRequest::hasSalesConversionRateMin).
                    map(CampaignAggregateHourRequest::getSalesConversionRateMin).ifPresent(param::setSalesConversionRateMin);
            Optional.of(request).filter(CampaignAggregateHourRequest::hasSalesConversionRateMax).
                    map(CampaignAggregateHourRequest::getSalesConversionRateMax).ifPresent(param::setSalesConversionRateMax);
            Optional.of(request).filter(CampaignAggregateHourRequest::hasAcotsMin).
                    map(CampaignAggregateHourRequest::getAcotsMin).ifPresent(param::setAcotsMin);
            Optional.of(request).filter(CampaignAggregateHourRequest::hasAcotsMax).
                    map(CampaignAggregateHourRequest::getAcotsMax).ifPresent(param::setAcotsMax);
            Optional.of(request).filter(CampaignAggregateHourRequest::hasAsotsMin).
                    map(CampaignAggregateHourRequest::getAsotsMin).ifPresent(param::setAsotsMin);
            Optional.of(request).filter(CampaignAggregateHourRequest::hasAsotsMax).
                    map(CampaignAggregateHourRequest::getAsotsMax).ifPresent(param::setAsotsMax);
        }

        Optional.of(request).filter(CampaignAggregateHourRequest::hasDailyBudgetMin).
                map(CampaignAggregateHourRequest::getDailyBudgetMin).ifPresent(param::setDailyBudgetMin);
        Optional.of(request).filter(CampaignAggregateHourRequest::hasDailyBudgetMax).
                map(CampaignAggregateHourRequest::getDailyBudgetMax).ifPresent(param::setDailyBudgetMax);
        Optional.of(request).filter(CampaignAggregateHourRequest::hasFilterStartDate).
                map(CampaignAggregateHourRequest::getFilterStartDate).ifPresent(param::setFilterStartDate);
        Optional.of(request).filter(CampaignAggregateHourRequest::hasFilterEndDate).
                map(CampaignAggregateHourRequest::getFilterEndDate).ifPresent(param::setFilterEndDate);
        Optional.of(request).filter(CampaignAggregateHourRequest::hasCostType).
                map(CampaignAggregateHourRequest::getCostType).ifPresent(param::setCostType);
        Optional.of(request).filter(CampaignAggregateHourRequest::hasCampaignId).
                map(CampaignAggregateHourRequest::getCampaignId).ifPresent(param::setCampaignId);
        return param;
    }

    private CampaignAggregateHourMultiShopParamVO fillParam(CampaignAggregateHourMultiShopRequest request) {
        CampaignAggregateHourMultiShopParamVO param = new CampaignAggregateHourMultiShopParamVO();
        BeanUtils.copyProperties(request, param);
        param.setCampaignId(request.getCampaignId());
        param.setWeeks(request.getWeeks());
        param.setPuid(request.getPuid().getValue());
        param.setShopIds(request.getShopIdList());
        param.setStartDate(request.getStartDate());
        param.setEndDate(request.getEndDate());
        param.setEndDateCompare(request.getEndDateCompare());
        param.setStartDateCompare(request.getStartDateCompare());
        param.setOrderField(request.getOrderField());
        param.setOrderType(request.getOrderType());
        param.setCurrency(request.getCurrency());
        param.setMarketplaceIds(request.getMarketplaceIdList());
        if (request.hasIsCompare()) {
            param.setIsCompare(request.getIsCompare().getValue());
        }


        param.setPuid(request.getPuid().getValue());
        param.setPageNo(request.getPageNo().getValue());
        param.setPageSize(request.getPageSize().getValue());

        // 仅显示正在投放
        if (request.hasServingStatus()) {
            param.setServingStatus(request.getServingStatus());
        }

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasImpressionsMin).
                    map(CampaignAggregateHourMultiShopRequest::getImpressionsMin).ifPresent(v -> param.setImpressionsMin(v.getValue()));
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasImpressionsMax).
                    map(CampaignAggregateHourMultiShopRequest::getImpressionsMax).ifPresent(v -> param.setImpressionsMax(v.getValue()));
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasClicksMin).
                    map(CampaignAggregateHourMultiShopRequest::getClicksMin).ifPresent(v -> param.setClicksMin(v.getValue()));
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasClicksMax).
                    map(CampaignAggregateHourMultiShopRequest::getClicksMax).ifPresent(v -> param.setClicksMax(v.getValue()));
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasClickRateMin).
                    map(CampaignAggregateHourMultiShopRequest::getClickRateMin).ifPresent(param::setClickRateMin);
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasClickRateMax).
                    map(CampaignAggregateHourMultiShopRequest::getClickRateMax).ifPresent(param::setClickRateMax);
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasCostMin).
                    map(CampaignAggregateHourMultiShopRequest::getCostMin).ifPresent(param::setCostMin);
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasCostMax).
                    map(CampaignAggregateHourMultiShopRequest::getCostMax).ifPresent(param::setCostMax);
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasCpcMin).
                    map(CampaignAggregateHourMultiShopRequest::getCpcMin).ifPresent(param::setCpcMin);
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasCpcMax).
                    map(CampaignAggregateHourMultiShopRequest::getCpcMax).ifPresent(param::setCpcMax);
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasOrderNumMin).
                    map(CampaignAggregateHourMultiShopRequest::getOrderNumMin).ifPresent(v -> param.setOrderNumMin(v.getValue()));
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasOrderNumMax).
                    map(CampaignAggregateHourMultiShopRequest::getOrderNumMax).ifPresent(v -> param.setOrderNumMax(v.getValue()));
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasSalesMin).
                    map(CampaignAggregateHourMultiShopRequest::getSalesMin).ifPresent(param::setSalesMin);
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasSalesMax).
                    map(CampaignAggregateHourMultiShopRequest::getSalesMax).ifPresent(param::setSalesMax);
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasAcosMin).
                    map(CampaignAggregateHourMultiShopRequest::getAcosMin).ifPresent(param::setAcosMin);
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasAcosMax).
                    map(CampaignAggregateHourMultiShopRequest::getAcosMax).ifPresent(param::setAcosMax);
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasRoasMin).
                    map(CampaignAggregateHourMultiShopRequest::getRoasMin).ifPresent(param::setRoasMin);
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasRoasMax).
                    map(CampaignAggregateHourMultiShopRequest::getRoasMax).ifPresent(param::setRoasMax);
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasSalesConversionRateMin).
                    map(CampaignAggregateHourMultiShopRequest::getSalesConversionRateMin).ifPresent(param::setSalesConversionRateMin);
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasSalesConversionRateMax).
                    map(CampaignAggregateHourMultiShopRequest::getSalesConversionRateMax).ifPresent(param::setSalesConversionRateMax);
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasAcotsMin).
                    map(CampaignAggregateHourMultiShopRequest::getAcotsMin).ifPresent(param::setAcotsMin);
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasAcotsMax).
                    map(CampaignAggregateHourMultiShopRequest::getAcotsMax).ifPresent(param::setAcotsMax);
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasAsotsMin).
                    map(CampaignAggregateHourMultiShopRequest::getAsotsMin).ifPresent(param::setAsotsMin);
            Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasAsotsMax).
                    map(CampaignAggregateHourMultiShopRequest::getAsotsMax).ifPresent(param::setAsotsMax);
        }

        Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasDailyBudgetMin).
                map(CampaignAggregateHourMultiShopRequest::getDailyBudgetMin).ifPresent(param::setDailyBudgetMin);
        Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasDailyBudgetMax).
                map(CampaignAggregateHourMultiShopRequest::getDailyBudgetMax).ifPresent(param::setDailyBudgetMax);
        Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasFilterStartDate).
                map(CampaignAggregateHourMultiShopRequest::getFilterStartDate).ifPresent(param::setFilterStartDate);
        Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasFilterEndDate).
                map(CampaignAggregateHourMultiShopRequest::getFilterEndDate).ifPresent(param::setFilterEndDate);
        Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasCostType).
                map(CampaignAggregateHourMultiShopRequest::getCostType).ifPresent(param::setCostType);
        Optional.of(request).filter(CampaignAggregateHourMultiShopRequest::hasCampaignId).
                map(CampaignAggregateHourMultiShopRequest::getCampaignId).ifPresent(param::setCampaignId);
        return param;
    }
}
