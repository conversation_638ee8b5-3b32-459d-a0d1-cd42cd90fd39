package com.meiyunji.sponsored.api.sp;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.Int32Value;
import com.google.protobuf.Int64Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProduct;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.rpc.adCommon.CommonShowAdPerformanceRequest;
import com.meiyunji.sponsored.rpc.adCommon.CommonShowAdPerformanceResponse;
import com.meiyunji.sponsored.rpc.sp.product.*;
import com.meiyunji.sponsored.rpc.vo.AdPerformanceRpcVo;
import com.meiyunji.sponsored.rpc.vo.CommonResponse;
import com.meiyunji.sponsored.rpc.vo.CpcCommPageRpcVo;
import com.meiyunji.sponsored.service.enums.StateEnum;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcProductService;
import com.meiyunji.sponsored.service.cpc.vo.*;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2021/10/27 19:07
 * @describe:
 */
@GRpcService
@Slf4j
public class ProductSpRpcService extends RPCSpProductServiceGrpc.RPCSpProductServiceImplBase {


    @Autowired
    private ICpcProductService cpcProductService;

    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;

    @Override
    public void showAdPerformanceVo(CommonShowAdPerformanceRequest request, StreamObserver<CommonShowAdPerformanceResponse> responseObserver) {
        CommonShowAdPerformanceResponse.Builder builder = CommonShowAdPerformanceResponse.newBuilder();
        //做参数校验
        if (!request.hasShopId()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");

        } else {
            AdPerformanceParam param = new AdPerformanceParam();
            param.setShopId(request.getShopId());
            param.setPuid(request.getPuid());
            param.setCampaignId(request.getCampaignId());
            param.setGroupId(request.getGroupId());
            param.setKeywordId(request.getKeywordId());
            param.setTargetId(request.getTargetId());
            param.setCpcProductId(request.getCpcProductId());
            param.setQuery(request.getQuery());
            param.setPlacement(request.getPlacement());
            param.setStartDate(request.getStartDate());
            param.setEndDate(request.getEndDate());

            if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
                param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
                param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            } else {
                param.setStartDate(param.getStartDate().replace("-", ""));
                param.setEndDate(param.getEndDate().replace("-", ""));
            }
            //处理业务返回结果
            Result<AdPerformanceVo> res = cpcProductService.showAdPerformance(request.getPuid(), param);
            builder.setCode(res.getCode());
            if (res.getMsg()!=null) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                //处理data
                AdPerformanceVo data = res.getData();
                if (data!=null) {
                    AdPerformanceRpcVo.Builder voBuilder = AdPerformanceRpcVo.newBuilder();
                    if (data.getShopId()!=null) {
                        voBuilder.setShopId(data.getShopId());
                    }
                    if (data.getCampaignId()!=null) {
                        voBuilder.setCampaignId(data.getCampaignId());
                    }
                    if (data.getGroupId()!=null) {
                        voBuilder.setGroupId(data.getGroupId());
                    }
                    if (data.getKeywordId()!=null) {
                        voBuilder.setKeywordId(data.getKeywordId());
                    }
                    if (data.getTargetId()!=null) {
                        voBuilder.setTargetId(data.getTargetId());
                    }
                    if (data.getAdId()!=null) {
                        voBuilder.setAdId(data.getAdId());
                    }
                    if (data.getQuery()!=null) {
                        voBuilder.setQuery(data.getQuery());
                    }
                    if (data.getPlacement()!=null) {
                        voBuilder.setPlacement(data.getPlacement());
                    }
                    Map<String, CpcCommPageRpcVo> rpcVoMap = Maps.newHashMap();

                    if (MapUtils.isNotEmpty(data.getMap())) {
                        for (Map.Entry<String, CpcCommPageVo> entry : data.getMap().entrySet()) {
                            CpcCommPageVo vo = entry.getValue();
                            //vo转message
                            CpcCommPageRpcVo.Builder cpcRpcVo = CpcCommPageRpcVo.newBuilder();
                            cpcRpcVo.setImpressions(Optional.ofNullable(vo.getImpressions()).orElse(0));
                            cpcRpcVo.setClicks(Optional.ofNullable(vo.getClicks()).orElse(0));
                            cpcRpcVo.setCtr(StringUtils.isNotBlank(vo.getCtr()) ? vo.getCtr(): "0");
                            cpcRpcVo.setCvr(StringUtils.isNotBlank(vo.getCvr()) ? vo.getCvr(): "0");
                            cpcRpcVo.setAcos(StringUtils.isNotBlank(vo.getAcos()) ? vo.getAcos(): "0");
                            cpcRpcVo.setRoas(StringUtils.isNotBlank(vo.getRoas()) ? vo.getRoas(): "0");
                            cpcRpcVo.setAcots(StringUtils.isNotBlank(vo.getAcots()) ? vo.getAcots(): "0");
                            cpcRpcVo.setAdOrderNum(Optional.ofNullable(vo.getAdOrderNum()).orElse(0));
                            cpcRpcVo.setAdCost(StringUtils.isNotBlank(vo.getAdCost()) ? vo.getAdCost(): "0");
                            cpcRpcVo.setAdCostPerClick(StringUtils.isNotBlank(vo.getAdCostPerClick()) ? vo.getAdCostPerClick(): "0");
                            cpcRpcVo.setAdSale(StringUtils.isNotBlank(vo.getAdSale()) ? vo.getAdSale(): "0");

                            rpcVoMap.put(entry.getKey(),cpcRpcVo.build());
                        }

                        voBuilder.putAllMap(rpcVoMap);
                    }
                    builder.setData(voBuilder.build());
                }
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getExistProduct(GetExistSpProductRequest request, StreamObserver<GetExistSpProductResponse> responseObserver) {
        log.info("sp-product查询广告组存在产品推广 request {}", request);
        GetExistSpProductResponse.Builder builder = GetExistSpProductResponse.newBuilder();

        List<String> list = amazonAdProductDao.listSkus(request.getPuid(), request.getShopId(), request.getAdGroupId(), Lists.newArrayList(CpcStatusEnum.paused.name(), CpcStatusEnum.enabled.name()));
        builder.setCode(Result.SUCCESS);
        builder.addAllData(list);

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    /**
     * 展示添加广告产品弹层
     */
    @Override
    public void showAdd(ShowSpAddProductRequest request, StreamObserver<ShowAddResponse> responseObserver) {
        log.info("sp-product-展示添加广告产品弹层数据 request {}", request);
        ShowAddResponse.Builder builder = ShowAddResponse.newBuilder();
        if (!request.hasShopId() || !request.hasPuid() || !request.hasDxmGroupId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result<List<AdProductPageVo>> res = cpcProductService.showAdd(request.getPuid().getValue(), request.getShopId().getValue(), request.getDxmGroupId().getValue());
            builder.setCode(Int32Value.of(res.getCode()));
           if (res.getMsg()!=null) {
                builder.setMsg(res.getMsg());
            }
            //处理list
            if (res.success()) {
                List<AdProductPageVo> data = res.getData();
                List<AdProductPageRpcVo> rpcVos = data.stream().filter(Objects::nonNull).map(item -> {
                    AdProductPageRpcVo.Builder voBuilder = AdProductPageRpcVo.newBuilder();
                    voBuilder.setType(item.getType());
                    voBuilder.setId(Int64Value.of(item.getId()));
                    voBuilder.setShopId(Int32Value.of(item.getShopId()));
                    voBuilder.setCampaignId(item.getCampaignId());
                    voBuilder.setCampaignName(item.getCampaignName());
                    voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                    voBuilder.setAdGroupId(item.getAdGroupId());
                    voBuilder.setAdGroupName(item.getAdGroupName());
                    voBuilder.setAdId(item.getAdId());
                    voBuilder.setState(item.getState());
                    voBuilder.setAsin(item.getAsin());
                    voBuilder.setSku(item.getSku());
                    voBuilder.setTitle(item.getTitle());
                    voBuilder.setImgUrl(item.getImgUrl());
                    voBuilder.setPrice(item.getPrice());
                    voBuilder.setDomain(item.getDomain());
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 添加广告产品-SP
     * @param request
     * @param responseObserver
     */
    @Override
    public void addProduct(AddSpProductRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-product-添加广告产品 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasPuid() || StringUtils.isBlank(request.getGroupId()) ||
                !request.hasUid() || CollectionUtils.isEmpty(request.getProductsList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            AddAdProductVo addAdProductVo = new AddAdProductVo();
            addAdProductVo.setGroupId(request.getGroupId());
            addAdProductVo.setPuid(request.getPuid().getValue());
            addAdProductVo.setShopId(request.getShopId().getValue());
            addAdProductVo.setUid(request.getUid().getValue());
            //处理集合
            List<productsRpc> productsList = request.getProductsList();
            List<ProductVo> voList = productsList.stream().filter(Objects::nonNull).map(item -> {
                ProductVo productVo = new ProductVo();
                productVo.setId(item.getId().getValue());
                productVo.setSku(item.getSku());
                productVo.setAsin(item.getAsin());
                productVo.setMainImage(item.getMainImage());
                productVo.setMarketplaceId(item.getMarketplaceId());
                productVo.setDomain(item.getDomain());
                return productVo;
            }).collect(Collectors.toList());
            addAdProductVo.setProducts(voList);
            //处理业务返回结果
            Result res = cpcProductService.addProduct(addAdProductVo, request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            builder.setData(res.getData() == null ? "" : String.valueOf(res.getData()));
           if (res.getMsg()!=null) {
                builder.setMsg(res.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void addProductV2(AddSpProductV2Request request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-product-添加广告产品v2 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasPuid() || CollectionUtils.isEmpty(request.getGroupIdList()) ||
                !request.hasUid() || CollectionUtils.isEmpty(request.getProductsList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            AddAdProductVo addAdProductVo = new AddAdProductVo();
            addAdProductVo.setPuid(request.getPuid().getValue());
            addAdProductVo.setShopId(request.getShopId().getValue());
            addAdProductVo.setUid(request.getUid().getValue());
            //处理集合
            List<productsRpc> productsList = request.getProductsList();
            List<ProductVo> voList = productsList.stream().filter(Objects::nonNull).map(item -> {
                ProductVo productVo = new ProductVo();
                productVo.setId(item.getId().getValue());
                productVo.setSku(item.getSku());
                productVo.setAsin(item.getAsin());
                productVo.setMainImage(item.getMainImage());
                productVo.setMarketplaceId(item.getMarketplaceId());
                productVo.setDomain(item.getDomain());
                return productVo;
            }).collect(Collectors.toList());
            addAdProductVo.setProducts(voList);

            List<CompletableFuture<Void>> futures = new ArrayList<>();
            List<AddAdProductVo> subAddAdProductVos = new ArrayList<>();
            for (String groupId : request.getGroupIdList()) {
                AddAdProductVo subAddAdProductVo = new AddAdProductVo();
                BeanUtils.copyProperties(addAdProductVo, subAddAdProductVo);
                subAddAdProductVo.setGroupId(groupId);
                subAddAdProductVo.setDisplayErrorMsgList(new ArrayList<>());
                subAddAdProductVos.add(subAddAdProductVo);
                futures.add(CompletableFuture.runAsync(() -> {
                    cpcProductService.addProduct(subAddAdProductVo, request.getLoginIp());
                }, ThreadPoolUtil.getMultiGroupAddProductPool()));
            }

            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            List<AddAdProductVo.DisplayErrorMsg> displayErrorMsgList = new ArrayList<>();
            subAddAdProductVos.stream().map(AddAdProductVo::getDisplayErrorMsgList).forEach(displayErrorMsgList::addAll);
            builder.setCode(Int32Value.of(0));
            if (CollectionUtils.isNotEmpty(displayErrorMsgList)) {
                builder.setData(JSONUtil.objectToJson2(displayErrorMsgList));
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 更新广告产品状态
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateState(UpdateSpProductStateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-product-更新广告产品状态 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        String stateValue = StateEnum.getStateValue(request.getState());
        if (!request.hasId() || !request.hasPuid() ||
                !request.hasUid() || StringUtils.isBlank(stateValue)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result res = cpcProductService.updateState(request.getPuid().getValue(), request.getUid().getValue(), request.getId().getValue(), request.getState(), request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
           if (res.getMsg()!=null) {
                builder.setMsg(res.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 归档
     * @param request
     * @param responseObserver
     */
    @Override
    public void archive(ArchiveSpProductRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-product-归档广告产品 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasId() || !request.hasPuid() ||
                !request.hasUid()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result res = cpcProductService.archive(request.getPuid().getValue(), request.getUid().getValue(), request.getId().getValue(), request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
           if (res.getMsg()!=null) {
                builder.setMsg(res.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 更新广告产品状态
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateBatch(UpdateBatchSpProductStateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-product-更新广告产品状态 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();


        if ( !request.hasPuid() || !request.hasUid()  || !request.hasShopId() || request.getVosCount() < 1) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            List<AmazonAdProduct> amazonAdProducts = request.getVosList().stream().map(vo -> {
                AmazonAdProduct amazonAdProduct = new AmazonAdProduct();
                amazonAdProduct.setPuid(request.getPuid().getValue());
                amazonAdProduct.setUpdateId(request.getUid().getValue());
                amazonAdProduct.setShopId(request.getShopId().getValue());
                if (vo.hasId()) {
                    amazonAdProduct.setId(vo.getId().getValue());
                }
                amazonAdProduct.setState(vo.getState());
                return amazonAdProduct;
            }).collect(Collectors.toList());

            Result res = cpcProductService.updateBatch(amazonAdProducts, request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if(res.getData() != null){
                builder.setData(JSONUtil.objectToJson(res.getData()));
            }
            if (res.getMsg()!=null) {
                builder.setMsg(res.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

}
