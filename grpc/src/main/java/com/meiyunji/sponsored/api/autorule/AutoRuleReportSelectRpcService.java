package com.meiyunji.sponsored.api.autorule;

import com.meiyunji.sponsored.rpc.autorule.autoRuleReportSelectServiceApi.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.AdReportData;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@GRpcService
@Slf4j
@RequiredArgsConstructor
public class AutoRuleReportSelectRpcService extends RPCAutoRuleReportSelectServiceApiGrpc.RPCAutoRuleReportSelectServiceApiImplBase {

    private final IScVcShopAuthDao scVcShopAuthDao;
    private final IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao;
    private final IAmazonAdGroupReportDao amazonAdGroupReportDao;
    private final IAmazonAdKeywordReportDao amazonAdKeywordReportDao;
    private final ICpcTargetingReportDao cpcTargetingReportDao;
    private final IAmazonAdSbTargetingReportDao amazonAdSbTargetingReportDao;
    private final IAmazonAdSdTargetingReportDao amazonAdSdTargetingReportDao;
    private final IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao;
    private final IAmazonSbAdKeywordDao amazonSbAdKeywordDao;

    private final IAmazonAdTargetingShardingDao amazonAdTargetingShardingDao;
    private final IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    private final IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    private final IAmazonAdSbGroupReportDao amazonAdSbGroupReportDao;
    private final IAmazonAdSdGroupReportDao amazonAdSdGroupReportDao;
    private final IAmazonAdSbKeywordReportDao amazonAdSbKeywordReportDao;

    @Override
    public void queryCampaignReportRpc(AutoRuleReportRequest request, StreamObserver<AutoRuleReportResponse> responseObserver) {
        log.info(" queryCampaignReportRpc = {}", request);
        AutoRuleReportResponse.Builder build = AutoRuleReportResponse.newBuilder();
        ShopAuth shopAuth = scVcShopAuthDao.getBySellerIdAndMarketplaceId(request.getSellerId(), request.getMarketplaceId());
        if (shopAuth == null) {
            log.error("授权店铺不存在!");
            build.setCode(1);
            responseObserver.onNext(build.build());
            responseObserver.onCompleted();
            return;
        }
        List<AdReportData> list = amazonAdCampaignAllReportDao.getAllReportByCampaignIdsAndDate(shopAuth.getPuid(), shopAuth.getId(), request.getStartDate(), request.getEndDate(), request.getItemIdList());
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> build.addData(buildAutoRuleReportItem(item)));
        }
        build.setCode(0);
        responseObserver.onNext(build.build());
        responseObserver.onCompleted();
    }

    private AutoRuleReportItem buildAutoRuleReportItem(AdReportData adReportData) {
        AutoRuleReportItem.Builder builder = AutoRuleReportItem.newBuilder()
                .setItemId(check(adReportData.getItemId()))
                .setOtherId(check(adReportData.getOtherId()))
                .setCost(check(adReportData.getCost()))
                .setImpressions(check(adReportData.getImpressions()))
                .setClicks(check(adReportData.getClicks()))
                .setAdOrderNum(check(adReportData.getAdOrderNum()))
                .setAdSelfOrderNum(check(adReportData.getAdSelfOrderNum()))
                .setAdSale(check(adReportData.getAdSale()))
                .setAdSelfSale(check(adReportData.getAdSelfSale()))
                .setAdSaleNum(check(adReportData.getAdSaleNum()))
                .setAdSelfSaleNum(check(adReportData.getAdSelfSaleNum()))
                .setType(check(adReportData.getType()))
                .setDate(check(adReportData.getCountDate()));
        return builder.build();
    }

    private int check(Integer num) {
        return num != null ? num : 0;
    }

    private String check(String str) {
        return str != null ? str : "";
    }

    private long check(Long num) {
        return num != null ? num : 0L;
    }

    private String check(BigDecimal num) {
        return num != null ? num.setScale(4, RoundingMode.HALF_UP).toString() : "0";
    }

    @Override
    public void queryGroupReportRpc(AutoRuleReportRequest request, StreamObserver<AutoRuleReportResponse> responseObserver) {
        log.info(" queryGroupReportRpc = {}", request);
        AutoRuleReportResponse.Builder build = AutoRuleReportResponse.newBuilder();
        ShopAuth shopAuth = scVcShopAuthDao.getBySellerIdAndMarketplaceId(request.getSellerId(), request.getMarketplaceId());
        if (shopAuth == null) {
            log.error("授权店铺不存在!");
            build.setCode(1);
            responseObserver.onNext(build.build());
            responseObserver.onCompleted();
            return;
        }
        List<AdReportData> list = new ArrayList<>();
        String type = StringUtils.isBlank(request.getType()) ? Constants.SP : request.getType();
        if (Constants.SP.equalsIgnoreCase(type)) {
            list = amazonAdGroupReportDao.getAllReportByGroupIdsAndDate(shopAuth.getPuid(), shopAuth.getId(), request.getStartDate(), request.getEndDate(), request.getItemIdList());
        } else if (Constants.SB.equalsIgnoreCase(type)) {
            list = amazonAdSbGroupReportDao.getAllReportByGroupIdsAndDate(shopAuth.getPuid(), shopAuth.getId(), request.getStartDate(), request.getEndDate(), request.getItemIdList());
        } else if (Constants.SD.equalsIgnoreCase(type)) {
            list = amazonAdSdGroupReportDao.getAllReportByGroupIdsAndDate(shopAuth.getPuid(), shopAuth.getId(), request.getStartDate(), request.getEndDate(), request.getItemIdList());
        }
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> build.addData(buildAutoRuleReportItem(item)));
        }
        build.setCode(0);
        responseObserver.onNext(build.build());
        responseObserver.onCompleted();
    }

    @Override
    public void queryKeywordReportRpc(AutoRuleReportRequest request, StreamObserver<AutoRuleReportResponse> responseObserver) {
        log.info(" queryKeywordReportRpc = {}", request);
        AutoRuleReportResponse.Builder build = AutoRuleReportResponse.newBuilder();
        ShopAuth shopAuth = scVcShopAuthDao.getBySellerIdAndMarketplaceId(request.getSellerId(), request.getMarketplaceId());
        if (shopAuth == null) {
            log.error("授权店铺不存在!");
            build.setCode(1);
            responseObserver.onNext(build.build());
            responseObserver.onCompleted();
            return;
        }
        // 重复代码 可以抽成 Func 方式 但算了
        List<AdReportData> list = new ArrayList<>();
        String type = StringUtils.isBlank(request.getType()) ? Constants.SP : request.getType();
        if (Constants.SP.equalsIgnoreCase(type)) {
            list = amazonAdKeywordReportDao.getAllReportByKeywordIdsAndDate(shopAuth.getPuid(), shopAuth.getId(), request.getStartDate(), request.getEndDate(), request.getItemIdList(), null);
        } else if (Constants.SB.equalsIgnoreCase(type)) {
            list = amazonAdSbKeywordReportDao.getAllReportByKeywordIdsAndDate(shopAuth.getPuid(), shopAuth.getId(), request.getStartDate(), request.getEndDate(), request.getItemIdList(), null);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> build.addData(buildAutoRuleReportItem(item)));
        }
        build.setCode(0);
        responseObserver.onNext(build.build());
        responseObserver.onCompleted();
    }

    @Override
    public void queryTargetingReportRpc(AutoRuleReportRequest request, StreamObserver<AutoRuleReportResponse> responseObserver) {
        log.info(" queryTargetingReportRpc = {}", request);
        AutoRuleReportResponse.Builder build = AutoRuleReportResponse.newBuilder();
        ShopAuth shopAuth = scVcShopAuthDao.getBySellerIdAndMarketplaceId(request.getSellerId(), request.getMarketplaceId());
        if (shopAuth == null) {
            log.error("授权店铺不存在!");
            build.setCode(1);
            responseObserver.onNext(build.build());
            responseObserver.onCompleted();
            return;
        }
        // 重复代码 可以抽成 Func 方式 但算了
        List<AdReportData> list = new ArrayList<>();
        String type = StringUtils.isBlank(request.getType()) ? Constants.SP : request.getType();
        if (Constants.SP.equalsIgnoreCase(type)) {
            list = cpcTargetingReportDao.getAllReportByTargetIdsAndDate(shopAuth.getPuid(), shopAuth.getId(), request.getStartDate(), request.getEndDate(), request.getItemIdList(), null);
        } else if (Constants.SB.equalsIgnoreCase(type)) {
            list = amazonAdSbTargetingReportDao.getAllReportByTargetIdsAndDate(shopAuth.getPuid(), shopAuth.getId(), request.getStartDate(), request.getEndDate(), request.getItemIdList(), null);
        } else if (Constants.SD.equalsIgnoreCase(type)) {
            list = amazonAdSdTargetingReportDao.getAllReportByTargetIdsAndDate(shopAuth.getPuid(), shopAuth.getId(), request.getStartDate(), request.getEndDate(), request.getItemIdList(), null);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> build.addData(buildAutoRuleReportItem(item)));
        }
        build.setCode(0);
        responseObserver.onNext(build.build());
        responseObserver.onCompleted();
    }

    @Override
    public void queryKeywordGroupReportRpc(AutoRuleReportRequest request, StreamObserver<AutoRuleReportResponse> responseObserver) {
        log.info(" queryKeywordGroupReportRpc = {}", request);
        AutoRuleReportResponse.Builder build = AutoRuleReportResponse.newBuilder();
        ShopAuth shopAuth = scVcShopAuthDao.getBySellerIdAndMarketplaceId(request.getSellerId(), request.getMarketplaceId());
        if (shopAuth == null) {
            log.error("授权店铺不存在!");
            build.setCode(1);
            responseObserver.onNext(build.build());
            responseObserver.onCompleted();
            return;
        }
        List<AdReportData> list = new ArrayList<>();
        String type = StringUtils.isBlank(request.getType()) ? Constants.SP : request.getType();
        if (Constants.SP.equalsIgnoreCase(type)) {
            list = amazonAdKeywordReportDao.getAllReportByKeywordIdsAndDate(shopAuth.getPuid(), shopAuth.getId(), request.getStartDate(), request.getEndDate(), null, request.getItemIdList());
            List<AmazonAdKeyword> amazonAdKeywords = amazonAdKeywordShardingDao.listByGroupIdListExcludeTheme(shopAuth.getPuid(), shopAuth.getId(), request.getItemIdList());
            if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                Map<String, List<String>> map = amazonAdKeywords.stream().collect(Collectors.groupingBy(AmazonAdKeyword::getAdGroupId, Collectors.mapping(AmazonAdKeyword::getKeywordId, Collectors.toList())));
                map.forEach((key, value) -> build.addGroupItem(AutoRuleReportGroupItem.newBuilder().setGroupId(key).addAllItemId(value)));
            }
        } else if (Constants.SB.equalsIgnoreCase(type)) {
            list = amazonAdSbKeywordReportDao.getAllReportByKeywordIdsAndDate(shopAuth.getPuid(), shopAuth.getId(), request.getStartDate(), request.getEndDate(), null, request.getItemIdList());
            List<AmazonSbAdKeyword> amazonAdKeywords = amazonSbAdKeywordDao.listByGroupIdList(shopAuth.getPuid(), shopAuth.getId(), request.getItemIdList());
            if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                Map<String, List<String>> map = amazonAdKeywords.stream().collect(Collectors.groupingBy(AmazonSbAdKeyword::getAdGroupId, Collectors.mapping(AmazonSbAdKeyword::getKeywordId, Collectors.toList())));
                map.forEach((key, value) -> build.addGroupItem(AutoRuleReportGroupItem.newBuilder().setGroupId(key).addAllItemId(value)));
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> build.addData(buildAutoRuleReportItem(item)));
        }
        build.setCode(0);
        responseObserver.onNext(build.build());
        responseObserver.onCompleted();
    }

    @Override
    public void queryTargetingGroupReportRpc(AutoRuleReportRequest request, StreamObserver<AutoRuleReportResponse> responseObserver) {
        log.info(" queryTargetingGroupReportRpc = {}", request);
        AutoRuleReportResponse.Builder build = AutoRuleReportResponse.newBuilder();
        ShopAuth shopAuth = scVcShopAuthDao.getBySellerIdAndMarketplaceId(request.getSellerId(), request.getMarketplaceId());
        if (shopAuth == null) {
            log.error("授权店铺不存在!");
            build.setCode(1);
            responseObserver.onNext(build.build());
            responseObserver.onCompleted();
            return;
        }
        List<AdReportData> list = new ArrayList<>();
        String type = StringUtils.isBlank(request.getType()) ? Constants.SP : request.getType();
        if (Constants.SP.equalsIgnoreCase(type)) {
            list = cpcTargetingReportDao.getAllReportByTargetIdsAndDate(shopAuth.getPuid(), shopAuth.getId(), request.getStartDate(), request.getEndDate(), null, request.getItemIdList());
            List<AmazonAdTargeting> amazonAdKeywords = amazonAdTargetingShardingDao.listByGroupIdList(shopAuth.getPuid(), shopAuth.getId(), request.getItemIdList());
            if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                Map<String, List<String>> map = amazonAdKeywords.stream().collect(Collectors.groupingBy(AmazonAdTargeting::getAdGroupId, Collectors.mapping(AmazonAdTargeting::getTargetId, Collectors.toList())));
                map.forEach((key, value) -> build.addGroupItem(AutoRuleReportGroupItem.newBuilder().setGroupId(key).addAllItemId(value)));
            }
        } else if (Constants.SB.equalsIgnoreCase(type)) {
            list = amazonAdSbTargetingReportDao.getAllReportByTargetIdsAndDate(shopAuth.getPuid(), shopAuth.getId(), request.getStartDate(), request.getEndDate(), null, request.getItemIdList());
            List<AmazonSbAdTargeting> amazonAdKeywords = amazonSbAdTargetingDao.listByGroupIdList(shopAuth.getPuid(), shopAuth.getId(), request.getItemIdList());
            if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                Map<String, List<String>> map = amazonAdKeywords.stream().collect(Collectors.groupingBy(AmazonSbAdTargeting::getAdGroupId, Collectors.mapping(AmazonSbAdTargeting::getTargetId, Collectors.toList())));
                map.forEach((key, value) -> build.addGroupItem(AutoRuleReportGroupItem.newBuilder().setGroupId(key).addAllItemId(value)));
            }
        } else if (Constants.SD.equalsIgnoreCase(type)) {
            list = amazonAdSdTargetingReportDao.getAllReportByTargetIdsAndDate(shopAuth.getPuid(), shopAuth.getId(), request.getStartDate(), request.getEndDate(), null, request.getItemIdList());
            List<AmazonSdAdTargeting> amazonAdKeywords = amazonSdAdTargetingDao.listByGroupIdList(shopAuth.getPuid(), shopAuth.getId(), request.getItemIdList());
            if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                Map<String, List<String>> map = amazonAdKeywords.stream().collect(Collectors.groupingBy(AmazonSdAdTargeting::getAdGroupId, Collectors.mapping(AmazonSdAdTargeting::getTargetId, Collectors.toList())));
                map.forEach((key, value) -> build.addGroupItem(AutoRuleReportGroupItem.newBuilder().setGroupId(key).addAllItemId(value)));
            }
        }
        // 广告组下 全部投放ID
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> build.addData(buildAutoRuleReportItem(item)));
        }
        build.setCode(0);
        responseObserver.onNext(build.build());
        responseObserver.onCompleted();
    }
}
