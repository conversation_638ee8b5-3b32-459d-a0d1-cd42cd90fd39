package com.meiyunji.sponsored.api.common;

import com.meiyunji.sellfox.ams.api.service.AdReportWeeklyAMSApiServiceGrpc;
import com.meiyunji.sellfox.ams.api.service.AdReportWeeklyRequestPb;
import com.meiyunji.sellfox.ams.api.service.AdReportWeeklyResponsePb;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.grpc.common.*;
import com.meiyunji.sponsored.rpc.adAggregateHour.AdPageBasicData;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonMarketingStreamDataDao;
import com.meiyunji.sponsored.service.cpc.dto.CampaignHourlyReportSelectDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonMarketingStreamData;
import com.meiyunji.sponsored.service.cpc.service2.IAdWeeklyReportAnalysisService;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.List;
import java.util.function.Function;

/**
 * @author: ys
 * @date: 2023/12/26 20:25
 * @describe:
 */
@GRpcService
@Slf4j
public class AdReportWeeklyRpcService extends AdReportWeeklyAnalysisApiServiceGrpc.AdReportWeeklyAnalysisApiServiceImplBase {

    @Autowired
    private IAdWeeklyReportAnalysisService adWeeklyReportAnalysisService;

    @Qualifier("adReportWeeklyAMSApiServiceBlockingStub")
    @Autowired
    private AdReportWeeklyAMSApiServiceGrpc.AdReportWeeklyAMSApiServiceBlockingStub adReportWeeklyAMSApiServiceBlockingStub;
    @Autowired
    private IAmazonMarketingStreamDataDao amazonMarketingStreamDataDao;

    @Override
    public void getCampaignWeeklyReport(AdHourReportRequest request, StreamObserver<AdWeekReportResponsePb.AdWeekReportResponse> responseObserver) {
        this.getAdReportWeeklyDataFromDoris(request, responseObserver, x -> amazonMarketingStreamDataDao.statisticsByWeek(x));
    }

    @Override
    public void getGroupWeeklyReport(AdHourReportRequest request, StreamObserver<AdWeekReportResponsePb.AdWeekReportResponse> responseObserver) {
        this.getAdReportWeeklyData(request, responseObserver, x -> adReportWeeklyAMSApiServiceBlockingStub.getAdGroupReportWeekly(x));
    }

    /**
     * 多店铺 商品投放、关键词投放、自动投放的周叠加数据
     * @param request
     * @param responseObserver
     */
    @Override
    public void getTargetWeeklyReport(HourlyWeeklySuperpositionTargetReq request, StreamObserver<AdWeekReportResponsePb.AdWeekReportResponse> responseObserver) {
        AdWeekReportResponsePb.AdWeekReportResponse.Builder builder = AdWeekReportResponsePb.AdWeekReportResponse.newBuilder();
        log.info("getTargetWeeklyReport, request:{}", request);
        if (!isTargetValidRequest(request) || (request.getIsCompare() == 1 && (!request.hasStartDateCompare() || !request.hasEndDateCompare()))) {
            setErrorResponse(builder, "Request param error");
            log.error("getTargetWeeklyReport, Request param error, request:{}" , request);
        } else {
            try {
                AdWeekReportResponsePb.AdWeekReportResponse.AdWeek weeklyReport =
                        adWeeklyReportAnalysisService.getTargetWeeklySuperpositionReport(request);
                if (weeklyReport != null) {
                    builder.setData(weeklyReport);
                }
                builder.setCode(Result.SUCCESS);
            } catch (Exception e) {
                String msg = "getCommonWeeklyReport error";
                if(e instanceof SponsoredBizException){
                    msg = e.getMessage();
                }
                setErrorResponse(builder, msg);
                log.error("getTargetWeeklyReport error, request:{}", request, e);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private boolean isTargetValidRequest(HourlyWeeklySuperpositionTargetReq req) {
        return req.hasPuid() && CollectionUtils.isNotEmpty(req.getShopIdListList()) && req.hasType() && req.hasEndDate() && req.hasStartDate() && req.hasLevelType()
                && req.hasWeeks() && (req.getAggregated() || req.hasCommonId());
    }

    /**
     * 通用查询广告活动、广告组合、投放的周叠加数据
     * @param request
     * @param responseObserver
     */
    @Override
    public void getCommonWeeklyReport(HourlyWeeklySuperpositionCommonReq request, StreamObserver<AdWeekReportResponsePb.AdWeekReportResponse> responseObserver) {
        AdWeekReportResponsePb.AdWeekReportResponse.Builder builder = AdWeekReportResponsePb.AdWeekReportResponse.newBuilder();
        log.info("getCommonWeeklyReport, request:{}", request);
        if (!isValidRequest(request) || (request.getIsCompare() == 1 && (!request.hasStartDateCompare() || !request.hasEndDateCompare()))) {
            setErrorResponse(builder, "Request param error");
            log.error("getCommonWeeklyReport, Request param error, request:{}" , request);
        } else {
            try {
                AdWeekReportResponsePb.AdWeekReportResponse.AdWeek weeklyReport =
                    adWeeklyReportAnalysisService.getCommonWeeklySuperpositionReport(request);
                if (weeklyReport != null) {
                    builder.setData(weeklyReport);
                }
                builder.setCode(Result.SUCCESS);
            } catch (Exception e) {
                setErrorResponse(builder, "getCommonWeeklyReport error");
                log.error("getCommonWeeklyReport error, request:{}", request, e);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private boolean isValidRequest(HourlyWeeklySuperpositionCommonReq req) {
        return req.hasPuid() && req.hasShopId() && req.hasType() && req.hasEndDate() && req.hasStartDate() && req.hasLevelType()
            && req.hasWeeks() && (req.getAggregated() || req.hasCommonId());
    }

    private void setErrorResponse(AdWeekReportResponsePb.AdWeekReportResponse.Builder builder, String errorMsg) {
        builder.setCode(Result.ERROR);
        builder.setMsg(errorMsg);
    }

    private void getAdReportWeeklyData(AdHourReportRequest request,
                                       StreamObserver<AdWeekReportResponsePb.AdWeekReportResponse> responseObserver,
                                       Function<AdReportWeeklyRequestPb.AdReportWeeklyRequest,
                                               AdReportWeeklyResponsePb.AdReportWeeklyResponse> func) {
        log.info("ad report weekly report analysis request:{}", request);
        AdWeekReportResponsePb.AdWeekReportResponse.Builder builder = AdWeekReportResponsePb.AdWeekReportResponse.newBuilder();
        AdPageBasicData pageBasicData = request.getPageBasic();
        //检查参数
        if (!paramCheck(pageBasicData)) {
            builder.setCode(Result.ERROR);
            builder.setMsg("request param error");
        } else {
            AdWeekReportResponsePb.AdWeekReportResponse.AdWeek weeklyReport =
                    adWeeklyReportAnalysisService.getSuperpositionReport(request, func);
            if (weeklyReport != null) {
                builder.setData(weeklyReport);
            }
            builder.setCode(Result.SUCCESS);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private void getAdReportWeeklyDataFromDoris(AdHourReportRequest request,
                                       StreamObserver<AdWeekReportResponsePb.AdWeekReportResponse> responseObserver,
                                       Function<CampaignHourlyReportSelectDto,
                                               List<AmazonMarketingStreamData>> func) {
        log.info("ad report weekly report analysis request:{}", request);
        AdWeekReportResponsePb.AdWeekReportResponse.Builder builder = AdWeekReportResponsePb.AdWeekReportResponse.newBuilder();
        AdPageBasicData pageBasicData = request.getPageBasic();
        //检查参数
        if (!paramCheck(pageBasicData)) {
            builder.setCode(Result.ERROR);
            builder.setMsg("request param error");
        } else {
            AdWeekReportResponsePb.AdWeekReportResponse.AdWeek weeklyReport =
                    adWeeklyReportAnalysisService.getSuperpositionReportFromDoris(request, func);
            if (weeklyReport != null) {
                builder.setData(weeklyReport);
            }
            builder.setCode(Result.SUCCESS);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private boolean paramCheck(AdPageBasicData pageBasicData) {
        if (!pageBasicData.hasPuid() || !pageBasicData.hasShopId() || !pageBasicData.hasEndDate() || !pageBasicData.hasStartDate()
                || (pageBasicData.hasIsCompare() && pageBasicData.getIsCompare().getValue() == 1 &&
                (!pageBasicData.hasStartDateCompare() || !pageBasicData.hasEndDateCompare()))) {
            return false;
        }
        return true;
    }

}
