package com.meiyunji.sponsored.api.newDashboard;

import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdFlowConversionRequest;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdFlowConversionResponse;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdFlowConversionResponseVo;
import com.meiyunji.sponsored.rpc.newDashboard.RpcDashboardAdFlowConversionServiceGrpc;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardCurrencyEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardModuleEnum;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdFlowConversionService;
import com.meiyunji.sponsored.service.newDashboard.util.DateTimeUtil;
import com.meiyunji.sponsored.service.newDashboard.util.YoyDateUtil;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardFlowConversionReqVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @create time :2024-06-25 14:59:15
 */
@GRpcService
@Slf4j
public class DashboardAdFlowConversionRpcService extends RpcDashboardAdFlowConversionServiceGrpc.RpcDashboardAdFlowConversionServiceImplBase {

    @Autowired
    private IDashboardAdFlowConversionService iDashboardAdFlowConversionService;
    @Override
    public void queryAdFlowConversion(DashboardAdFlowConversionRequest request, StreamObserver<DashboardAdFlowConversionResponse> responseObserver) {
        log.info("dashboard query flowconversion: {}",request);
        StopWatch sw = new StopWatch();
        sw.start();
        DashboardAdFlowConversionResponse.Builder builder = DashboardAdFlowConversionResponse.newBuilder();
        DashboardFlowConversionReqVo reqVo = processParam(request);
        if(Objects.isNull(reqVo)){
            log.error("dashboard flow conversion, check param error");
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        else{
            DashboardAdFlowConversionResponseVo vo = iDashboardAdFlowConversionService.queryFlowConversion(reqVo);
            builder.addData(vo);
            builder.setCode(Result.SUCCESS);
            builder.setMsg("请求成功");
            sw.stop();
            log.info("dashboard query flow conversion, puid: {}, 耗时: {}秒", request.getPuid(), sw.getTotalTimeSeconds());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();


    }
    private DashboardFlowConversionReqVo processParam(DashboardAdFlowConversionRequest request){
        if (!DashboardCurrencyEnum.currencySet.contains(request.getCurrency())) {
            log.error(" req param error, currency is null:{}", request.getCurrency());
            return null;
        }
        DashboardFlowConversionReqVo vo = DashboardFlowConversionReqVo.builder().build();
        //获得同比的开始与结束时间
        List<String> yoyDate = YoyDateUtil.getYoyDate(request.getStartDate(), request.getEndDate());
        if(CollectionUtils.isEmpty(yoyDate)){
            // 查询超过两年后，会抛出null 开始时间故意写得比结束时间晚,令后续查出来的同比数据为空
            vo.setYoyOverLimit(true);
            vo.setYoyStartDate(DateUtil.toFormatDate(request.getStartDate()));
            vo.setYoyEndDate(DateUtil.dateToStrWithTime(DateUtil.addDay(DateUtil.strToDate(request.getStartDate(), DateUtil.PATTERN_YYYYMMDD), -1), DateUtil.PATTERN));
        }
        else{
            Optional.ofNullable(yoyDate.get(0)).map(DateUtil::toFormatDate).ifPresent(vo::setYoyStartDate);
            Optional.ofNullable(yoyDate.get(1)).map(DateUtil::toFormatDate).ifPresent(vo::setYoyEndDate);
        }
        //copy data. direction: request->vo,shallow copy
        //if you want to realize the deep copy,you are recommended to utilize stream to create A new object
        BeanUtils.copyProperties(request,vo, ParamCopyUtil.checkPropertiesNullOrEmptySuper(request));
        Optional.of(request.getStartDate()).map(DateUtil::toFormatDate).ifPresent(vo::setStartDate);
        Optional.of(request.getEndDate()).map(DateUtil::toFormatDate).ifPresent(vo::setEndDate);
        Optional.of(request.getMomStartDate()).map(DateUtil::toFormatDate).ifPresent(vo::setMomStartDate);
        Optional.of(request.getMomEndDate()).map(DateUtil::toFormatDate).ifPresent(vo::setMomEndDate);
        Optional.of(request.getMarketplaceIdList()).filter(org.apache.commons.collections4.CollectionUtils::isNotEmpty).ifPresent(vo::setMarketplaceIdList);
        Optional.of(request.getShopIdList()).filter(org.apache.commons.collections4.CollectionUtils::isNotEmpty).ifPresent(vo::setShopIdList);
        DateTimeUtil.resetTimeRange(vo, DashboardModuleEnum.FLOW_CONVERSION);
        Optional.of(request.getPortfolioIdsList()).filter(org.apache.commons.collections4.CollectionUtils::isNotEmpty).ifPresent(vo::setPortfolioIds);
        Optional.of(request.getCampaignIdsList()).filter(org.apache.commons.collections4.CollectionUtils::isNotEmpty).ifPresent(vo::setCampaignIds);
        return vo;

    }
}
