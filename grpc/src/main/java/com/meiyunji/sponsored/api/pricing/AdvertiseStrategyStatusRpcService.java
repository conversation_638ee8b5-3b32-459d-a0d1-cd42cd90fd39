package com.meiyunji.sponsored.api.pricing;

import com.amazon.advertising.mode.Adjustment;
import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.google.protobuf.Int32Value;
import com.meiyunji.sellfox.aadas.types.enumeration.TaskTimeType;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.pricing.status.*;
import com.meiyunji.sponsored.rpc.pricing.status.Campaign;
import com.meiyunji.sponsored.rpc.pricing.status.CampaignPlacement;
import com.meiyunji.sponsored.rpc.pricing.status.CampaignPlacementRule;
import com.meiyunji.sponsored.rpc.pricing.status.CampaignRule;
import com.meiyunji.sponsored.rpc.pricing.status.Target;
import com.meiyunji.sponsored.rpc.pricing.status.TargetRule;
import com.meiyunji.sponsored.rpc.pricing.template.*;
import com.meiyunji.sponsored.rpc.vo.CommonResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.IndexStrategyConfig;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdPortfolioDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolio;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProductMetadata;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdProductMetadataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.PlacementPageParam;
import com.meiyunji.sponsored.service.enums.AutoTargetTypeEnum;
import com.meiyunji.sponsored.service.enums.SBCampaignGoalEnum;
import com.meiyunji.sponsored.service.enums.StateEnum;
import com.meiyunji.sponsored.service.enums.StrategyEnum;
import com.meiyunji.sponsored.service.product.service.ISyncAsinImageService;
import com.meiyunji.sponsored.service.sellfoxApi.IProductApi;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyAdGroupDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTemplateDao;
import com.meiyunji.sponsored.service.strategy.dao.impl.StrategyLimitConfigDao;
import com.meiyunji.sponsored.service.strategy.enums.AdItemType;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyAdGroup;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatus;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.strategy.po.StrategyLimitConfig;
import com.meiyunji.sponsored.service.strategy.service.AdStrategyStatusApiFactory;
import com.meiyunji.sponsored.service.strategy.service.IAdvertiseStrategyGroupTargetBidService;
import com.meiyunji.sponsored.service.strategy.service.IAdvertiseStrategyQueryService;
import com.meiyunji.sponsored.service.strategy.service.IAdvertiseStrategyStatusService;
import com.meiyunji.sponsored.service.strategy.vo.*;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyTaskDao;
import com.meiyunji.sponsored.service.taskGrpcApi.AadasApiFactory;
import com.meiyunji.sponsored.service.vo.ProductAdReportVo;
import com.meiyunji.sponsored.service.util.PbUtil;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.apache.pulsar.shade.com.google.common.collect.Lists;
import org.lognet.springboot.grpc.GRpcService;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.cpc.util.Constants.AD_GROUP_STRATEGY_MAX_COUNT_MSG;

@GRpcService
@Slf4j
public class AdvertiseStrategyStatusRpcService extends RPCAdStrategyStatusServiceGrpc.RPCAdStrategyStatusServiceImplBase {

    @Autowired
    private IAdvertiseStrategyStatusService advertiseStrategyStatusService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private ISlaveScVcShopAuthDao slaveShopAuthDao;
    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private AdvertiseStrategyTemplateDao advertiseStrategyTemplateDao;
    @Autowired
    private AdvertiseStrategyStatusDao advertiseStrategyStatusDao;
    @Autowired
    private AdStrategyStatusApiFactory adStrategyStatusApiFactory;
    @Autowired
    private IAdvertiseStrategyQueryService advertiseStrategyQueryService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private IAdvertiseStrategyGroupTargetBidService advertiseStrategyGroupTargetBidService;
    @Autowired
    private AdvertiseStrategyAdGroupDao advertiseStrategyAdGroupDao;
    @Autowired
    private IndexStrategyConfig indexStrategyConfig;
    @Autowired
    private AdvertiseStrategyTaskDao advertiseStrategyTaskDao;
    @Autowired
    private StrategyLimitConfigDao strategyLimitConfigDao;
    @Autowired
    private ISyncAsinImageService syncAsinImageService;
    @Autowired
    private IAmazonAdProductMetadataService amazonAdProductMetadataService;
    @Autowired
    private IProductApi productDao;
    @Autowired
    AadasApiFactory aadasApiFactory;

    @Override
    public void pageCampaignList(CampaignPageRequest request, StreamObserver<CampaignPageResponse> responseObserver) {
        log.info("查询广告活动数据 pageCampaignList request:{}", request);
        CampaignPageResponse.Builder responseBuilder = CampaignPageResponse.newBuilder();
        if (!request.hasPuid() || !request.hasPageNo() || !request.hasPageSize()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            AdCampaignStrategyParam param = new AdCampaignStrategyParam();
            param.setPuid(request.getPuid());
            param.setShopId(request.getShopId());
            param.setCampaignName(request.getCampaignName());
            param.setAdTypeList(request.getAdTypeList());
            param.setPortfolioIds(request.getPortfolioIdList());
            param.setPageNo(request.getPageNo());
            param.setPageSize(request.getPageSize());
            param.setServingStatusList(request.getServingStatusList());
            //兼容前端传值问题
            if (StringUtils.isBlank(request.getState())) {
                param.setState("all");
            } else {
                param.setState(request.getState());
            }
            param.setTraceId(request.getTraceId());
            Result<Page<AmazonAdCampaignAll>> result = null;
            if (StringUtils.isNotBlank(request.getItemType()) && "START_STOP".equals(request.getItemType())){
                result = advertiseStrategyStatusService.queryAdCampaignState(param);
            } else {
                result = advertiseStrategyStatusService.queryAdCampaign(param);
            }
            if (result.getCode() == Result.SUCCESS) {
                Page<AmazonAdCampaignAll> voPage = result.getData();
                CampaignPage.Builder campaignPage = CampaignPage.newBuilder();
                campaignPage.setPageNo(voPage.getPageNo());
                campaignPage.setPageSize(voPage.getPageSize());
                campaignPage.setTotalPage(voPage.getTotalPage());
                campaignPage.setTotalSize(voPage.getTotalSize());
                List<AmazonAdCampaignAll> amazonAdCampaignAllList = voPage.getRows();
                if (CollectionUtils.isNotEmpty(amazonAdCampaignAllList)) {
                    List<CampaignRpc> campaignRpcList = new ArrayList<>(amazonAdCampaignAllList.size());
                    //获取所有的店铺名称
                    List<Integer> shopIds = amazonAdCampaignAllList.stream().
                            map(AmazonAdCampaignAll::getShopId).distinct().collect(Collectors.toList());
                    List<ShopAuth> shops = shopAuthDao.getScAndVcByIds(shopIds);
                    Map<Integer, ShopAuth> shopAuthMap = null;
                    if (CollectionUtils.isNotEmpty(shops)) {
                        shopAuthMap = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
                    }
                    //获取广告组合
                    Map<String, AmazonAdPortfolio> portfolioMap = null;
                    List<String> portfolioIds = amazonAdCampaignAllList.stream().
                            map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(portfolioIds)) {
                        portfolioMap = portfolioDao.getPortfolioList(request.getPuid(), request.getShopId(), portfolioIds).stream()
                                .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                    }
                    for (AmazonAdCampaignAll amazonAdCampaignAll : amazonAdCampaignAllList) {
                        CampaignRpc.Builder builder = CampaignRpc.newBuilder();
                        builder.setId(amazonAdCampaignAll.getId());
                        builder.setPuid(amazonAdCampaignAll.getPuid());
                        builder.setShopId(amazonAdCampaignAll.getShopId());
                        builder.setMarketplaceId(amazonAdCampaignAll.getMarketplaceId());
                        if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(amazonAdCampaignAll.getShopId())) {
                            builder.setShopName(shopAuthMap.get(amazonAdCampaignAll.getShopId()).getName());
                            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(amazonAdCampaignAll.getMarketplaceId());
                            if ( null != m ) {
                                builder.setCurrency(m.getCurrencyCode());
                            }
                        }
                        builder.setCampaignId(amazonAdCampaignAll.getCampaignId());
                        builder.setCampaignName(amazonAdCampaignAll.getName());
                        builder.setAdType(amazonAdCampaignAll.getType());
                        builder.setBudgetValue(amazonAdCampaignAll.getBudget().doubleValue());
                        Optional.ofNullable(amazonAdCampaignAll.getBudgetType()).filter(StringUtils::isNotBlank)
                                .ifPresent(builder::setBudgetType);
                        if (StringUtils.isNotBlank(amazonAdCampaignAll.getServingStatus())) {
                            builder.setServingStatus(amazonAdCampaignAll.getServingStatus());
                            AmazonAdCampaignAll.servingStatusEnum byCode = UCommonUtil.getByCode(amazonAdCampaignAll.getServingStatus(), AmazonAdCampaignAll.servingStatusEnum.class);
                            if(byCode != null){
                                builder.setServingStatusName(byCode.getName());
                            }else {
                                builder.setServingStatusName(amazonAdCampaignAll.getServingStatus());
                            }
                        }
                        if (StringUtils.isNotBlank(amazonAdCampaignAll.getPortfolioId())) {
                            builder.setPortfolioId(amazonAdCampaignAll.getPortfolioId());
                            //查询广告组合名称
                            if (MapUtils.isNotEmpty(portfolioMap) && portfolioMap.containsKey(amazonAdCampaignAll.getPortfolioId())) {
                                builder.setPortfolioName(portfolioMap.get(amazonAdCampaignAll.getPortfolioId()).getName());
                            }
                        }
                        builder.setState(amazonAdCampaignAll.getState());
                        campaignRpcList.add(builder.build());
                    }
                    campaignPage.addAllRows(campaignRpcList);
                }
                responseBuilder.setCode(result.getCode());
                responseBuilder.setData(campaignPage.build());
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void pageProductList(ProductPageRequest request, StreamObserver<ProductPageResponse> responseObserver) {
        log.info("查询广告产品数据 pageProductList request:{}", request);
        ProductPageResponse.Builder responseBuilder = ProductPageResponse.newBuilder();
        if (!request.hasPuid() || !request.hasPageNo() || !request.hasPageSize()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        AdProductStrategyParam param = new AdProductStrategyParam();
        param.setPuid(request.getPuid());
        param.setShopId(request.getShopId());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());

        param.setPortfolioIdList(request.getPortfolioIdList());
        param.setAdType(request.getAdType());
        param.setCampaignIdList(request.getCampaignIdList());
        param.setAdGroupIdList(request.getAdGroupIdList());
        param.setItemType(request.getItemType());
        param.setProductType(request.getProductType());
        param.setProductValueList(request.getProductValueList());
        param.setTraceId(request.getTraceId());

        if (CollectionUtils.isNotEmpty(request.getStateList())) {
            param.setStateList(request.getStateList());
        } else {
            param.setStateList(Arrays.asList(StateEnum.enabled.getStateType(), StateEnum.paused.getStateType()));
        }
        param.setTraceId(request.getTraceId());
        Result<Page<AdProductStrategyVo>> result = null;
        if (StringUtils.isNotBlank(request.getItemType()) && "START_STOP".equals(request.getItemType())){
            result = advertiseStrategyStatusService.queryAdProduct(param);
        }

        if (Result.SUCCESS != result.getCode() || Objects.isNull(result.getData())) {
            responseBuilder.setCode(result.getCode());
            responseBuilder.setMsg(result.getMsg());
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        Page<AdProductStrategyVo> voPage = result.getData();

        ProductPage.Builder productPage = ProductPage.newBuilder();
        productPage.setPageNo(voPage.getPageNo());
        productPage.setPageSize(voPage.getPageSize());
        productPage.setTotalPage(voPage.getTotalPage());
        productPage.setTotalSize(voPage.getTotalSize());

        List<AdProductStrategyVo> productStrategyVoList = voPage.getRows();
        if (CollectionUtils.isNotEmpty(productStrategyVoList)) {
            List<ProductRpc> productRpcList = new ArrayList<>(productStrategyVoList.size());

            //收集数据
            Set<String> skuSet = new HashSet<>();
            Set<String> asinSet = new HashSet<>();
            Set<String> portfolioIdSet = new HashSet<>();
            productStrategyVoList.forEach(x -> {
                asinSet.add(x.getAsin());
                skuSet.add(x.getSku());
                if (StringUtils.isNotBlank(x.getPortfolioId())) {
                    portfolioIdSet.add(x.getPortfolioId());
                }
            });

            //获取店铺名称
            ShopAuth shop = shopAuthDao.getScAndVcById(request.getShopId());

            //获取广告组合
            Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(portfolioIdSet)) {
                List<AmazonAdPortfolio> portfolioList = portfolioDao.getPortfolioList(request.getPuid(), request.getShopId(), new ArrayList<>(portfolioIdSet));
                if (CollectionUtils.isNotEmpty(portfolioList)) {
                    portfolioMap = portfolioList.stream().collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                }
            }

            //收集asin图片和标题
            Map<String, List<AmazonAdProductMetadata>> skuMetaDataMap = new HashMap<>();
            Map<String, List<ProductAdReportVo>> skuProductMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(skuSet)  && Objects.nonNull(shop)) {
                List<String> skuList = new ArrayList<>(skuSet);
                List<AmazonAdProductMetadata> amazonAdProductMetadataList = amazonAdProductMetadataService.getAsinBySkus(shop.getPuid(), shop.getId(), skuList, new ArrayList<>(asinSet));
                if (CollectionUtils.isNotEmpty(amazonAdProductMetadataList)) {
                    skuMetaDataMap = amazonAdProductMetadataList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AmazonAdProductMetadata::getSku));
                }
                // 兼容老代码逻辑
                List<List<String>> partition = com.google.common.collect.Lists.partition(skuList, 100);
                for (List<String> batchSkus : partition) {
                    List<ProductAdReportVo> asinBySkus = productDao.getAsinBySkus(shop.getPuid(), shop.getId(), shop.getMarketplaceId(), batchSkus);
                    if (CollectionUtils.isNotEmpty(asinBySkus)) {
                        skuProductMap.putAll(asinBySkus.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(ProductAdReportVo::getSku)));
                    }
                }
            }

            for (AdProductStrategyVo productStrategyVo : productStrategyVoList) {
                ProductRpc.Builder builder = ProductRpc.newBuilder();

                builder.setPuid(productStrategyVo.getPuid());
                builder.setShopId(productStrategyVo.getShopId());
                builder.setMarketplaceId(productStrategyVo.getMarketplaceId());

                builder.setAdId(productStrategyVo.getAdId());
                builder.setAsin(productStrategyVo.getAsin());
                builder.setMsku(productStrategyVo.getSku());
                builder.setAdType(productStrategyVo.getAdType());

                builder.setPortfolioId("");
                builder.setPortfolioName("");
                if (StringUtils.isNotBlank(productStrategyVo.getPortfolioId())) {
                    AmazonAdPortfolio portfolio = portfolioMap.getOrDefault(productStrategyVo.getPortfolioId(), null);
                    if (Objects.nonNull(portfolio)) {
                        builder.setPortfolioId(portfolio.getPortfolioId());
                        builder.setPortfolioName(portfolio.getName());
                    }
                }

                builder.setCampaignId(productStrategyVo.getCampaignId());
                builder.setCampaignName(productStrategyVo.getCampaignName());
                builder.setCampaignState(productStrategyVo.getCampaignState());
                builder.setAdGroupId(productStrategyVo.getAdGroupId());
                builder.setAdgroupName(productStrategyVo.getAdgroupName());
                builder.setAdGroupState(productStrategyVo.getAdGroupState());
                builder.setProductState(productStrategyVo.getProductState());
                if (Objects.nonNull(shop)) {
                    builder.setShopName(shop.getName());
                }

                // asin标题图片
                List<AmazonAdProductMetadata> skuMetaDataList = skuMetaDataMap.get(productStrategyVo.getSku());
                if (CollectionUtils.isNotEmpty(skuMetaDataList)) {
                    AmazonAdProductMetadata metadata = skuMetaDataList.get(0);
                    if (StringUtils.isNotBlank(metadata.getTitle())) {
                        productStrategyVo.setTitle(metadata.getTitle());
                    }
                    if (StringUtils.isNotBlank(metadata.getImageUrl())) {
                        productStrategyVo.setImageUrl(metadata.getImageUrl());
                    }
                }

                if (StringUtils.isAnyBlank(productStrategyVo.getTitle(), productStrategyVo.getImageUrl())) {
                    List<ProductAdReportVo> skuProductList = skuProductMap.get(productStrategyVo.getSku());
                    if (CollectionUtils.isNotEmpty(skuProductList)) {
                        ProductAdReportVo product = skuProductList.get(0);
                        if (StringUtils.isNotBlank(product.getTitle())) {
                            productStrategyVo.setTitle(product.getTitle());
                        }
                        if (StringUtils.isNotBlank(product.getMainImage())) {
                            productStrategyVo.setImageUrl(product.getMainImage());
                        }
                    }
                }

                if (StringUtils.isNotBlank(productStrategyVo.getTitle())) {
                    builder.setTitle(productStrategyVo.getTitle());
                }
                if (StringUtils.isNotBlank(productStrategyVo.getImageUrl())) {
                    builder.setImageUrl(productStrategyVo.getImageUrl());
                }

                productRpcList.add(builder.build());
            }

            productPage.addAllRows(productRpcList);
        }

        responseBuilder.setCode(result.getCode());
        responseBuilder.setData(productPage.build());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void pageAdSpaceList(AdSpacePageRequest request, StreamObserver<AdSpaceResponse> responseObserver) {
        log.info("查询广告位数据 pageAdSpaceList request:{}", request);
        AdSpaceResponse.Builder responseBuilder = AdSpaceResponse.newBuilder();
        if (!request.hasPuid() || !request.hasPageNo() || !request.hasPageSize()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            AdSpaceStrategyParam param = new AdSpaceStrategyParam();
            param.setPuid(request.getPuid());
            param.setShopId(request.getShopId());
            param.setPortfolioIds(request.getPortfolioIdList());
            param.setCampaignName(request.getCampaignName());
            param.setPageNo(request.getPageNo());
            param.setPageSize(request.getPageSize());
            param.setServingStatusList(request.getServingStatusList());
            //兼容前端传值问题
            if (StringUtils.isBlank(request.getState())) {
                param.setState("all");
            } else {
                param.setState(request.getState());
            }
            param.setTraceId(request.getTraceId());
            Result<Page<AmazonAdCampaignAll>> result = advertiseStrategyStatusService.queryAdSpace(param);
            if (result.getCode() == Result.SUCCESS) {
                Page<AmazonAdCampaignAll> voPage = result.getData();
                AdSpacePage.Builder spacePage = AdSpacePage.newBuilder();
                spacePage.setPageNo(voPage.getPageNo());
                spacePage.setPageSize(voPage.getPageSize());
                spacePage.setTotalPage(voPage.getTotalPage());
                spacePage.setTotalSize(voPage.getTotalSize());
                List<AmazonAdCampaignAll> amazonAdCampaignAllList = voPage.getRows();
                if (CollectionUtils.isNotEmpty(amazonAdCampaignAllList)) {
                    List<AdSpacePageRpc> spacePageRpcList = new ArrayList<>(amazonAdCampaignAllList.size());
                    //获取所有的店铺名称
                    List<Integer> shopIds = amazonAdCampaignAllList.stream().
                            map(AmazonAdCampaignAll::getShopId).distinct().collect(Collectors.toList());
                    List<ShopAuth> shops = shopAuthDao.getScAndVcByIds(shopIds);
                    Map<Integer, ShopAuth> shopAuthMap = null;
                    if (CollectionUtils.isNotEmpty(shops)) {
                        shopAuthMap = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
                    }
                    //获取广告组合
                    Map<String, AmazonAdPortfolio> portfolioMap = null;
                    List<String> portfolioIds = amazonAdCampaignAllList.stream().
                            map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(portfolioIds)) {
                        portfolioMap = portfolioDao.getPortfolioList(request.getPuid(), request.getShopId(), portfolioIds).stream()
                                .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                    }
                    for (AmazonAdCampaignAll amazonAdCampaignAll : amazonAdCampaignAllList) {
                        AdSpacePageRpc.Builder builder = AdSpacePageRpc.newBuilder();
                        builder.setId(amazonAdCampaignAll.getId());
                        builder.setPuid(amazonAdCampaignAll.getPuid());
                        builder.setShopId(amazonAdCampaignAll.getShopId());
                        builder.setMarketplaceId(amazonAdCampaignAll.getMarketplaceId());
                        builder.setCampaignId(amazonAdCampaignAll.getCampaignId());
                        builder.setCampaignName(amazonAdCampaignAll.getName());
                        builder.setAdType(amazonAdCampaignAll.getType());
                        if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(amazonAdCampaignAll.getShopId())) {
                            builder.setShopName(shopAuthMap.get(amazonAdCampaignAll.getShopId()).getName());
                        }
                        if (StringUtils.isNotBlank(amazonAdCampaignAll.getPortfolioId())) {
                            builder.setPortfolioId(amazonAdCampaignAll.getPortfolioId());
                            if (MapUtils.isNotEmpty(portfolioMap) && portfolioMap.containsKey(amazonAdCampaignAll.getPortfolioId())) {
                                builder.setPortfolioName(portfolioMap.get(amazonAdCampaignAll.getPortfolioId()).getName());
                            }
                        }
                        List<Adjustment> adjustments = JSONUtil.jsonToArray(amazonAdCampaignAll.getAdjustments(),
                                Adjustment.class);
                        if (StringUtils.isNotBlank(amazonAdCampaignAll.getStrategy())) {
                            builder.setStrategy(amazonAdCampaignAll.getStrategy());
                            builder.setStrategyName(StrategyEnum.
                                    getStrategyValue(amazonAdCampaignAll.getStrategy()));
                        }
                        Double adPlaceProductValue = null;
                        Double adPlaceTopValue = null;
                        Double adOtherValue = null;
                        if (CollectionUtils.isNotEmpty(adjustments)) {
                            for (Adjustment adjustment : adjustments) {
                                if ("placementTop".equals(adjustment.getPredicate())) {
                                    builder.setAdPlaceTop(PlacementPageParam.
                                            placementPredicateEnum.getDescription(Constants.PLACEMENT_TOP)+":"+adjustment.getPercentage().intValue()+"%");
                                    adPlaceTopValue = adjustment.getPercentage();
                                    builder.setAdPlaceTopValue(adPlaceTopValue);
                                } else if ("placementProductPage".equals(adjustment.getPredicate())) {
                                    builder.setAdPlaceProduct(PlacementPageParam.
                                            placementPredicateEnum.getDescription(Constants.PLACEMENT_DETAIL_PAGE)+":"+adjustment.getPercentage().intValue()+"%");
                                    adPlaceProductValue = adjustment.getPercentage();
                                    builder.setAdPlaceProductValue(adPlaceProductValue);
                                } else if ("placementRestOfSearch".equals(adjustment.getPredicate())) {
                                    builder.setAdOther(PlacementPageParam.
                                            placementPredicateEnum.getDescription(Constants.PLACEMENT_OTHER)+":"+adjustment.getPercentage().intValue()+"%");
                                    adOtherValue = adjustment.getPercentage();
                                    builder.setAdOtherValue(adOtherValue);
                                }
                            }
                        }
                        if (adPlaceTopValue == null) {
                            builder.setAdPlaceTopValue(0);
                            builder.setAdPlaceTop(PlacementPageParam.
                                    placementPredicateEnum.getDescription(Constants.PLACEMENT_TOP)+":0%");
                        }
                        if (adPlaceProductValue == null) {
                            builder.setAdPlaceProductValue(0);
                            builder.setAdPlaceProduct(PlacementPageParam.
                                    placementPredicateEnum.getDescription(Constants.PLACEMENT_DETAIL_PAGE)+":0%");
                        }
                        if (adOtherValue == null) {
                            builder.setAdOtherValue(0);
                            builder.setAdOther(PlacementPageParam.
                                    placementPredicateEnum.getDescription(Constants.PLACEMENT_OTHER)+":0%");
                        }
                        builder.setState(amazonAdCampaignAll.getState());
                        if (StringUtils.isNotBlank(amazonAdCampaignAll.getServingStatus())) {
                            builder.setServingStatus(amazonAdCampaignAll.getServingStatus());
                            AmazonAdCampaignAll.servingStatusEnum byCode = UCommonUtil.getByCode(amazonAdCampaignAll.getServingStatus(), AmazonAdCampaignAll.servingStatusEnum.class);
                            if(byCode != null){
                                builder.setServingStatusName(byCode.getName());
                            }else {
                                builder.setServingStatusName(amazonAdCampaignAll.getServingStatus());
                            }
                        }
                        spacePageRpcList.add(builder.build());
                    }
                    spacePage.addAllRows(spacePageRpcList);
                }
                responseBuilder.setCode(result.getCode());
                responseBuilder.setData(spacePage.build());
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void pageAdTargetList(AdTargetPageRequest request, StreamObserver<AdTargetResponse> responseObserver) {
        log.info("查询投放数据 pageAdTargetList request:{}", request);
        AdTargetResponse.Builder responseBuilder = AdTargetResponse.newBuilder();
        if (!request.hasPuid() || !request.hasPageNo() || !request.hasPageSize() || !request.hasTargetType()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            AdTargetStrategyParam param = new AdTargetStrategyParam();
            param.setPuid(request.getPuid());
            param.setShopId(request.getShopId());
            param.setPageNo(request.getPageNo());
            param.setPageSize(request.getPageSize());
            param.setSearchValue(request.getSearchValue());
            param.setAdType(request.getAdType());
            //兼容前端传值问题
            if (StringUtils.isBlank(request.getState())) {
                param.setState("all");
            } else {
                param.setState(request.getState());
            }
            param.setServingStatusList(request.getServingStatusList());
            param.setPortfolioIdList(request.getPortfolioIdList());
            param.setTraceId(request.getTraceId());
            param.setGroupIdList(request.getAdGroupIdList());
            param.setCampaignIdList(request.getCampaignIdList());
            param.setTargetType(request.getTargetType());
            param.setMarketplaceId(request.getMarketplaceId());
            param.setMatchType(request.getMatchType());
            Result<Page<AdvertiseStrategyTargetVo>> result = advertiseStrategyStatusService.queryAdTarget(param);
            if (result.getCode() == Result.SUCCESS) {
                responseBuilder.setCode(result.getCode());
                Page<AdvertiseStrategyTargetVo> page = result.getData();
                AdTargetPage.Builder pageBuilder = AdTargetPage.newBuilder();
                pageBuilder.setPageNo(page.getPageNo());
                pageBuilder.setPageSize(page.getPageSize());
                pageBuilder.setTotalPage(page.getTotalPage());
                pageBuilder.setTotalSize(page.getTotalSize());
                List<AdvertiseStrategyTargetVo> advertiseStrategyTargetVos = page.getRows();
                if (CollectionUtils.isNotEmpty(advertiseStrategyTargetVos)) {
                    List<AdTargetPageRpc> list = Lists.newArrayListWithExpectedSize(advertiseStrategyTargetVos.size());
                    //获取所有的店铺名称
                    List<Integer> shopIds = advertiseStrategyTargetVos.stream().
                            map(AdvertiseStrategyTargetVo::getShopId).distinct().collect(Collectors.toList());
                    List<ShopAuth> shops = shopAuthDao.getScAndVcByIds(shopIds);
                    Map<Integer, ShopAuth> shopAuthMap = null;
                    if (CollectionUtils.isNotEmpty(shops)) {
                        shopAuthMap = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
                    }
                    for (AdvertiseStrategyTargetVo vo : advertiseStrategyTargetVos) {
                        AdTargetPageRpc.Builder builder = AdTargetPageRpc.newBuilder();
                        builder.setTargetId(vo.getTargetId());
                        builder.setPuid(vo.getPuid());
                        builder.setShopId(vo.getShopId());
                        builder.setMarketplaceId(vo.getMarketplaceId());
                        builder.setAdType(vo.getAdType());
                        if (StringUtils.isNotBlank(vo.getCampaignId())) {
                            builder.setCampaignId(vo.getCampaignId());
                        }
                        if (StringUtils.isNotBlank(vo.getGroupId())) {
                            builder.setAdGroupId(vo.getGroupId());
                        }
                        if (StringUtils.isNotBlank(vo.getCampaignName())) {
                            builder.setCampaignName(vo.getCampaignName());
                        }
                        if (StringUtils.isNotBlank(vo.getGroupName())) {
                            builder.setAdGroupName(vo.getGroupName());
                        }
                        if (StringUtils.isNotBlank(vo.getServingStatus())) {
                            builder.setServingStatus(vo.getServingStatus());
                        }
                        if (StringUtils.isNotBlank(vo.getServingStatusName())) {
                            builder.setServingStatusName(vo.getServingStatusName());
                        }
                        if (StringUtils.isNotBlank(vo.getPortfolioId())) {
                            builder.setPortfolioId(vo.getPortfolioId());
                        }
                        if (StringUtils.isNotBlank(vo.getPortfolioName())) {
                            builder.setPortfolioName(vo.getPortfolioName());
                        }
                        builder.setBiddingValue(vo.getBiddingValue().doubleValue());
                        if (StringUtils.isNotBlank(vo.getTargetingValue())) {
                            builder.setTargetValue(vo.getTargetingValue());
                        }
                        builder.setState(vo.getTargetingState());
                        builder.setTargetType(vo.getTargetType());
                        if (StringUtils.isNotBlank(vo.getAdTargetType())) {
                            builder.setAdTargetType(vo.getAdTargetType());
                        }
                        if (StringUtils.isNotBlank(vo.getMatchType())) {
                            builder.setMatchType(vo.getMatchType());
                        }
                        if (StringUtils.isNotBlank(vo.getMatchName())) {
                            builder.setMatchName(vo.getMatchName());
                        }
                        if (StringUtils.isNotBlank(vo.getDetailTargetText())) {
                            builder.setDetailTargetText(vo.getDetailTargetText());
                        }
                        if (StringUtils.isNotBlank(vo.getDetailType())) {
                            builder.setDetailType(vo.getDetailType());
                        }
                        if (StringUtils.isNotBlank(vo.getDetailTitle())) {
                            builder.setDetailTitle(vo.getDetailTitle());
                        }
                        if (StringUtils.isNotBlank(vo.getDetailTargetType())) {
                            builder.setDetailTargetType(vo.getDetailTargetType());
                        }
                        if (StringUtils.isNotBlank(vo.getDetailCategory())) {
                            builder.setDetailCategory(vo.getDetailCategory());
                        }
                        if (StringUtils.isNotBlank(vo.getDetailBrandName())) {
                            builder.setDetailBrandName(vo.getDetailBrandName());
                        }
                        if (StringUtils.isNotBlank(vo.getDetailCommodityPriceRange())) {
                            builder.setDetailCommodityPriceRange(vo.getDetailCommodityPriceRange());
                        }
                        if (StringUtils.isNotBlank(vo.getDetailRating())) {
                            builder.setDetailRating(vo.getDetailRating());
                        }
                        if (StringUtils.isNotBlank(vo.getDetailDistribution())) {
                            builder.setDetailDistribution(vo.getDetailDistribution());
                        }
                        if (StringUtils.isNotBlank(vo.getDetailLookback())) {
                            builder.setDetailLookback(vo.getDetailLookback());
                        }
                        if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(vo.getShopId())) {
                            builder.setShopName(shopAuthMap.get(vo.getShopId()).getName());
                            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(vo.getMarketplaceId());
                            if( null != m){
                                builder.setCurrency(m.getCurrencyCode());
                            }
                        }
                        Optional.ofNullable(vo.getCostType()).filter(StringUtils::isNotEmpty).ifPresent(builder::setCostType);
                        Optional.ofNullable(vo.getAdGoal()).filter(StringUtils::isNotEmpty).ifPresent(builder::setGoal);
                        Optional.ofNullable(vo.getAdFormat()).filter(StringUtils::isNotEmpty).ifPresent(builder::setAdFormat);
                        list.add(builder.build());
                    }
                    pageBuilder.addAllRows(list);
                    responseBuilder.setData(pageBuilder);
                }
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void pageAdPortfolioList(AdPortfolioPageRequest request, StreamObserver<AdPortfolioPageResponse> responseObserver) {
        log.info("查询广告组合数据 pageAdPortfolioList request:{}", request);
        AdPortfolioPageResponse.Builder responseBuilder = AdPortfolioPageResponse.newBuilder();
        AdPortfolioStrategyParam param = new AdPortfolioStrategyParam();
        param.setPuid(request.getPuid());
        param.setShopId(request.getShopId());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setPortfolioName(request.getSearchValue());
        param.setServingStatusList(request.getServingStatusList());
        param.setTraceId(request.getTraceId());
        Result<Page<AmazonAdPortfolio>> result = advertiseStrategyStatusService.queryAdPortfolio(param);
        responseBuilder.setCode(result.getCode());
        if (result.getCode() == Result.SUCCESS) {
            Page<AmazonAdPortfolio> page = result.getData();
            AdPortfolioPage.Builder pageBuilder = AdPortfolioPage.newBuilder();
            pageBuilder.setPageNo(page.getPageNo());
            pageBuilder.setPageSize(page.getPageSize());
            pageBuilder.setTotalPage(page.getTotalPage());
            pageBuilder.setTotalSize(page.getTotalSize());
            if (CollectionUtils.isNotEmpty(page.getRows())) {
                page.getRows().forEach(e->{
                    AdPortfolioPageRpc.Builder builder = AdPortfolioPageRpc.newBuilder();
                    builder.setPuid(e.getPuid());
                    builder.setShopId(e.getShopId());
                    builder.setMarketplaceId(e.getMarketplaceId());
                    builder.setPortfolioId(e.getPortfolioId());
                    builder.setPortfolioName(e.getName());
                    if (e.getAmount() != null) {
                        builder.setAmount(e.getAmount());
                    }
                    if (StringUtils.isNotBlank(e.getServingStatus())) {
                        builder.setServingStatus(e.getServingStatus());
                        AmazonAdPortfolio.servingStatusEnum byCode = UCommonUtil.getByCode(e.getServingStatus(), AmazonAdPortfolio.servingStatusEnum.class);
                        if(byCode != null){
                            builder.setServingStatusName(byCode.getName());
                        }else {
                            builder.setServingStatusName(e.getServingStatus());
                        }
                    }
                    builder.setPolicy(e.getPolicy());
                    if (StringUtils.isNotBlank(e.getBudgetStartDateStr())) {
                        builder.setBudgetStartDate(e.getBudgetStartDateStr());
                    }
                    if (StringUtils.isNotBlank(e.getBudgetEndDateStr())) {
                        builder.setBudgetEndDate(e.getBudgetEndDateStr());
                    }
                    builder.setState(e.getState());
                    pageBuilder.addRows(builder.build());
                });
            }
            responseBuilder.setData(pageBuilder.build());
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void pageAdControlledObject(AdPageControlledObjectRequest request, StreamObserver<AdPageControlledObjectResponse> responseObserver) {
        log.info("查询受控对象数据 pageAdControlledObject request:{}", request);
        AdPageControlledObjectResponse.Builder responseBuilder = AdPageControlledObjectResponse.newBuilder();
        if (!request.hasTemplateId() || !request.hasPuid()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        ControlledObjectParam param = new ControlledObjectParam();
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setPuid(request.getPuid());
        param.setShopId(request.getShopId());
        param.setItemType(request.getItemType());
        param.setAdTypeList(request.getAdTypesList());
        param.setCampaignIds(request.getCampaignIdList());
        param.setGroupIds(request.getGroupIdList());
        param.setTemplateId(request.getTemplateId());
        param.setSearchValue(request.getSearchValue());
        param.setSearchField(request.getSearchField());
        param.setStartStopItemType(request.getStartStopItemType());
        param.setPortfolioIds(request.getPortfolioIdList());
        param.setMatchType(request.getMatchType());
        param.setTraceId(request.getTraceId());
        param.setAdType(request.getAdType());
        param.setState(request.getState());
        param.setAdGroupScreen(request.getAdGroupScreen());
        param.setServingStatusList(request.getServingStatusList());
        Result<Page<AdvertiseStrategyStatus>> result = new Result<>();
        if (request.getItemType().equals("CAMPAIGN")) {
            result = advertiseStrategyStatusService.pageAdControlledCampaign(param);
        }
        if (request.getItemType().equals("CAMPAIGN_PLACEMENT")) {
            result = advertiseStrategyStatusService.pageAdControlledCampaignSpace(param);
        }
        if (request.getItemType().equals("TARGET")) {
            if ("TARGET".equals(request.getAddWayType())) {
                if (StringUtils.isEmpty(request.getTargetType()) || "all".equals(request.getTargetType())) {
                    List<String> targetTypeList = Lists.newArrayList("autoTarget", "keywordTarget", "productTarget", "audienceTarget");
                    param.setTargetTypeList(targetTypeList);
                } else {
                    List<String> targetTypeList = Lists.newArrayList(request.getTargetType());
                    param.setTargetTypeList(targetTypeList);
                }
                result = advertiseStrategyStatusService.pageAdControlledTarget(param);
            } else {
                result = advertiseStrategyGroupTargetBidService.pageAdControlledAdGroupTarget(param);
            }
        }
        String startStopItemType = "";
        if (request.getItemType().equals("START_STOP")) {
            result = adStrategyStatusApiFactory.getStrategyStatusService(request.getItemType()).pageAdControlled(param);
            List<AdvertiseStrategyStatus> existList = advertiseStrategyStatusDao.getListByTemplateIdLimit(request.getPuid(), request.getShopId(), request.getTemplateId(), 1);
            if (CollectionUtils.isNotEmpty(existList)) {
                startStopItemType = existList.get(0).getStartStopItemType();
            }
        }
        AdvertiseStrategyTemplate advertiseStrategyTemplate = advertiseStrategyTemplateDao.
                selectByPrimaryKey(request.getPuid(), request.getTemplateId());
        if (request.getItemType().equals(AdItemType.PORTFOLIO.name())) {
            result = adStrategyStatusApiFactory.getStrategyStatusService(PbUtil.isPortfolioHour(advertiseStrategyTemplate.getChildrenItemType()) ? Constants.PORTFOLIO_HOUR : request.getItemType()).pageAdControlled(param);
        }

        if (Result.SUCCESS != result.getCode()) {
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        ControlledObjectPage.Builder builder = ControlledObjectPage.newBuilder();
        Page<AdvertiseStrategyStatus> page = result.getData();
        builder.setPageNo(page.getPageNo());
        builder.setPageSize(page.getPageSize());
        builder.setTotalPage(page.getTotalPage());
        builder.setTotalSize(page.getTotalSize());
        builder.setStartStopItemType(startStopItemType);

        if (CollectionUtils.isEmpty(page.getRows())) {
            responseBuilder.setCode(result.getCode());
            responseBuilder.setData(builder.build());
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        //获取所有广告有效的店铺名称
        List<ShopAuth> shops = shopAuthDao.listAllValidAdShop(request.getPuid());
        Map<Integer, ShopAuth> shopAuthMap = null;
        if (CollectionUtils.isNotEmpty(shops)) {
            shopAuthMap = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
        }

        List<AdvertiseStrategyStatus> advertiseStrategyStatusList = page.getRows();
        List<ControlledObjectRpc> list = Lists.newArrayListWithExpectedSize(advertiseStrategyStatusList.size());
        if ("CAMPAIGN".equals(request.getItemType())) {
            List<CampaignRuleVo> campaignRuleVoList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(),CampaignRuleVo.class);
            List<CampaignRule> campaignRuleList = new ArrayList<>(campaignRuleVoList.size());
            Map<Integer,List<CampaignRuleVo>> map = campaignRuleVoList.stream().
                    collect(Collectors.groupingBy(CampaignRuleVo::getSiteDate));
            for (Integer siteDate : map.keySet()) {
                CampaignRule.Builder campaignRuleBuilder = CampaignRule.newBuilder();
                campaignRuleBuilder.setSiteDate(siteDate);
                for (CampaignRuleVo campaignRuleVo : map.get(siteDate)) {
                    Campaign.Builder campaign = Campaign.newBuilder();
                    campaign.setBudgetValue(campaignRuleVo.getBudgetValue().doubleValue());
                    campaign.setStartTimeSite(campaignRuleVo.getStartTimeSite());
                    campaign.setEndTimeSite(campaignRuleVo.getEndTimeSite());
                    if ("numerical".equals(advertiseStrategyTemplate.getChildrenItemType())) {
                        campaign.setBudgetType(campaignRuleVo.getBudgetType());
                        MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(advertiseStrategyTemplate.getMarketplaceId());
                        if (m != null) {
                            campaign.setSymbol(m.getCurrencyCode());
                        }
                    } else {
                        campaign.setSymbol("%");
                    }
                    campaignRuleBuilder.addCampaigns(campaign);
                }
                if (siteDate == 0) {
                    campaignRuleBuilder.setSiteDateName("每日");
                } else {
                    campaignRuleBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                            cpc.util.Constants.getDateMap().get(siteDate));
                }
                campaignRuleList.add(campaignRuleBuilder.build());
            }
            builder.addAllCampaignRuleList(campaignRuleList);
        } else if ("CAMPAIGN_PLACEMENT".equals(request.getItemType())) {
            List<CampaignPlacementRuleVo> campaignPlacementRuleVoList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(),CampaignPlacementRuleVo.class);
            List<CampaignPlacementRule> campaignPlacementRuleList = new ArrayList<>(campaignPlacementRuleVoList.size());
            Map<Integer,List<CampaignPlacementRuleVo>> map = campaignPlacementRuleVoList.stream().
                    collect(Collectors.groupingBy(CampaignPlacementRuleVo::getSiteDate));
            for (Integer siteDate : map.keySet()) {
                CampaignPlacementRule.Builder campaignPlacementRuleBuilder =CampaignPlacementRule.newBuilder();
                campaignPlacementRuleBuilder.setSiteDate(siteDate);
                for (CampaignPlacementRuleVo campaignPlacementRuleVo : map.get(siteDate)) {
                    CampaignPlacement.Builder campaignPlacement = CampaignPlacement.newBuilder();
                    campaignPlacement.setAdPlaceProductType(campaignPlacementRuleVo.getAdPlaceProductType());
                    campaignPlacement.setStartTimeSite(campaignPlacementRuleVo.getStartTimeSite());
                    campaignPlacement.setEndTimeSite(campaignPlacementRuleVo.getEndTimeSite());
                    campaignPlacement.setAdPlaceProductValue(campaignPlacementRuleVo.getAdPlaceProductValue().doubleValue());
                    if (campaignPlacementRuleVo.getAdPlaceTopMaxValue() != null) {
                        campaignPlacement.setAdPlaceTopMaxValue(campaignPlacementRuleVo.getAdPlaceTopMaxValue().doubleValue());
                    }
                    if (campaignPlacementRuleVo.getAdPlaceTopMinValue() != null) {
                        campaignPlacement.setAdPlaceTopMinValue(campaignPlacementRuleVo.getAdPlaceTopMinValue().doubleValue());
                    }
                    if (campaignPlacementRuleVo.getAdPlaceProductMaxValue() != null) {
                        campaignPlacement.setAdPlaceProductMaxValue(campaignPlacementRuleVo.getAdPlaceProductMaxValue().doubleValue());
                    }
                    if (campaignPlacementRuleVo.getAdPlaceProductMinValue() != null) {
                        campaignPlacement.setAdPlaceProductMinValue(campaignPlacementRuleVo.getAdPlaceProductMinValue().doubleValue());
                    }
                    campaignPlacement.setAdPlaceTopType(campaignPlacementRuleVo.getAdPlaceTopType());
                    campaignPlacement.setAdPlaceTopValue(campaignPlacementRuleVo.getAdPlaceTopValue().doubleValue());
                    if (StringUtils.isNotBlank(campaignPlacementRuleVo.getStrategy())) {
                        campaignPlacement.setStrategy(campaignPlacementRuleVo.getStrategy());
                    }
                    if (campaignPlacementRuleVo.getAdOtherType() != null) {
                        campaignPlacement.setAdOtherType(campaignPlacementRuleVo.getAdOtherType());
                    } else {
                        campaignPlacement.setAdOtherType(4);
                    }
                    if (campaignPlacementRuleVo.getAdOtherValue() != null) {
                        campaignPlacement.setAdOtherValue(campaignPlacementRuleVo.getAdOtherValue().doubleValue());
                    } else {
                        campaignPlacement.setAdOtherValue(0);
                    }
                    campaignPlacement.setSymbol("%");
                    campaignPlacementRuleBuilder.addCampaignPlacements(campaignPlacement);
                }
                if (siteDate == 0) {
                    campaignPlacementRuleBuilder.setSiteDateName("每日");
                } else {
                    campaignPlacementRuleBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                            cpc.util.Constants.getDateMap().get(siteDate));
                }
                campaignPlacementRuleList.add(campaignPlacementRuleBuilder.build());
            }
            builder.addAllCampaignPlacementRuleList(campaignPlacementRuleList);
        } else if ("TARGET".equals(request.getItemType())) {
            List<TargetRuleVo> targetRuleVoList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(),TargetRuleVo.class);
            List<TargetRule> targetRuleList = new ArrayList<>(targetRuleVoList.size());
            Map<Integer,List<TargetRuleVo>> map = targetRuleVoList.stream().
                    collect(Collectors.groupingBy(TargetRuleVo::getSiteDate));
            for (Integer siteDate : map.keySet()) {
                TargetRule.Builder targetRuleBuilder = TargetRule.newBuilder();
                targetRuleBuilder.setSiteDate(siteDate);
                for (TargetRuleVo targetRuleVo:map.get(siteDate)) {
                    Target.Builder target = Target.newBuilder();
                    target.setStartTimeSite(targetRuleVo.getStartTimeSite());
                    target.setEndTimeSite(targetRuleVo.getEndTimeSite());
                    target.setBiddingType(targetRuleVo.getBiddingType());
                    if (targetRuleVo.getBiddingType().equals(1) || targetRuleVo.getBiddingType().equals(3)) {
                        target.setBiddingValue(targetRuleVo.getBiddingValue().doubleValue());
                        if (targetRuleVo.getBiddingMaxValue() != null) {
                            target.setBiddingMaxValue(targetRuleVo.getBiddingMaxValue().doubleValue());
                        }
                        if (targetRuleVo.getBiddingMinValue() != null) {
                            target.setBiddingMinValue(targetRuleVo.getBiddingMinValue().doubleValue());
                        }
                    } else {
                        target.setBiddingValue(targetRuleVo.getBiddingValue().doubleValue());
                        if (targetRuleVo.getBiddingMaxValue() != null) {
                            target.setBiddingMaxValue(targetRuleVo.getBiddingMaxValue().doubleValue());
                        }
                        if (targetRuleVo.getBiddingMinValue() != null) {
                            target.setBiddingMinValue(targetRuleVo.getBiddingMinValue().doubleValue());
                        }
                    }
                    MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(advertiseStrategyTemplate.getMarketplaceId());
                    if (m != null) {
                        target.setSymbol(m.getCurrencyCode());
                    }
                    targetRuleBuilder.addTargets(target);
                }
                if (siteDate == 0) {
                    targetRuleBuilder.setSiteDateName("每日");
                } else {
                    targetRuleBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                            cpc.util.Constants.getDateMap().get(siteDate));
                }
                targetRuleList.add(targetRuleBuilder.build());
            }
            builder.addAllTargetRuleList(targetRuleList);
        } else if ("START_STOP".equals(request.getItemType())) {
            List<StartStopRuleVo> startStopRuleVoList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(), StartStopRuleVo.class);
            List<StartStopStateRule> startStopStateRuleList = Lists.newArrayList();
            Map<Integer,List<StartStopRuleVo>> map = startStopRuleVoList.stream().collect(Collectors.groupingBy(StartStopRuleVo::getSiteDate));
            for (Integer siteDate : map.keySet()) {
                StartStopStateRule.Builder startStopStateRuleBuilder = StartStopStateRule.newBuilder();
                startStopStateRuleBuilder.setSiteDate(siteDate);
                if (siteDate == 0) {
                    startStopStateRuleBuilder.setSiteDateName("每日");
                } else {
                    startStopStateRuleBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                            cpc.util.Constants.getDateMap().get(siteDate));
                }
                for (StartStopRuleVo startStopRuleVo : map.get(siteDate)) {
                    StateRule.Builder stateBuilder = StateRule.newBuilder();
                    stateBuilder.setStartTimeSite(startStopRuleVo.getStartTimeSite());
                    stateBuilder.setEndTimeSite(startStopRuleVo.getEndTimeSite());
                    stateBuilder.setState(startStopRuleVo.getState());
                    startStopStateRuleBuilder.addStateRules(stateBuilder);
                }
                startStopStateRuleList.add(startStopStateRuleBuilder.build());
            }
            builder.addAllStartStopStateRuleList(startStopStateRuleList);
        } else if ("PORTFOLIO".equals(request.getItemType())) {
            // 是否小时级
            if (PbUtil.isPortfolioHour(advertiseStrategyTemplate.getChildrenItemType())) {
                builder.addAllPortfolioHourRuleList(PbUtil.buildPortfolioHourRuleByJson(advertiseStrategyTemplate.getRule(), advertiseStrategyTemplate.getMarketplaceId()));
            } else {
                List<PortfolioRuleVo> portfolioRuleList = JSONUtil.jsonToArray(advertiseStrategyTemplate.getRule(), PortfolioRuleVo.class);
                List<PortfolioRule> portfolioRules = com.google.common.collect.Lists.newArrayList();
                Map<Integer,List<PortfolioRuleVo>> map = new LinkedHashMap<>();
                Map<Integer,List<PortfolioRuleVo>> listMap = new LinkedHashMap<>();
                if ("MONTH_DAY".equals(advertiseStrategyTemplate.getType())) {
                    portfolioRuleList.stream().collect(Collectors.groupingBy(e->
                                    Integer.valueOf(LocalDate.parse(e.getStartDate(),DateTimeFormatter.ofPattern(DateUtil.PATTERN)).
                                            format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD))))).entrySet().stream()
                            .sorted(Map.Entry.comparingByKey()).forEachOrdered(x->listMap.put(x.getKey(),x.getValue()));
                } else {
                    map = portfolioRuleList.stream().collect(Collectors.groupingBy(PortfolioRuleVo::getSiteDate));
                }
                if (MapUtils.isNotEmpty(map)) {
                    for (Integer siteDate : map.keySet()) {
                        PortfolioRule.Builder portfolioBuilder = PortfolioRule.newBuilder();
                        portfolioBuilder.setSiteDate(siteDate);
                        if (siteDate == 0) {
                            portfolioBuilder.setSiteDateName("每日");
                        } else {
                            portfolioBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                    cpc.util.Constants.getDateMap().get(siteDate));
                        }
                        for (PortfolioRuleVo portfolioRuleVo : map.get(siteDate)) {
                            Portfolio.Builder portfolio = Portfolio.newBuilder();
                            if (!"noBudget".equals(portfolioRuleVo.getAmount())) {
                                portfolio.setAmount(portfolioRuleVo.getAmount().doubleValue());
                            }
                            portfolio.setPolicy(portfolioRuleVo.getPolicy());
                            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(advertiseStrategyTemplate.getMarketplaceId());
                            if(null != m){
                                portfolio.setSymbol(m.getCurrencyCode());
                            }
                            portfolioBuilder.addPortfolios(portfolio.build());
                        }
                        portfolioRules.add(portfolioBuilder.build());
                    }
                }
                if (MapUtils.isNotEmpty(listMap)) {
                    for (Integer countDate : listMap.keySet()) {
                        PortfolioRule.Builder portfolioBuilder = PortfolioRule.newBuilder();
                        portfolioBuilder.setStartDate(listMap.get(countDate).get(0).getStartDate());
                        portfolioBuilder.setEndDate(listMap.get(countDate).get(0).getEndDate());
                        for (PortfolioRuleVo portfolioRuleVo : listMap.get(countDate)) {
                            Portfolio.Builder portfolio = Portfolio.newBuilder();
                            if (!"noBudget".equals(portfolioRuleVo.getAmount())) {
                                portfolio.setAmount(portfolioRuleVo.getAmount().doubleValue());
                            }
                            portfolio.setPolicy(portfolioRuleVo.getPolicy());
                            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(advertiseStrategyTemplate.getMarketplaceId());
                            if(null != m){
                                portfolio.setSymbol(m.getCurrencyCode());
                            }
                            portfolioBuilder.addPortfolios(portfolio.build());
                        }
                        portfolioRules.add(portfolioBuilder.build());
                    }
                }
                builder.addAllPortfolioRuleList(portfolioRules);
            }
        }

        for (AdvertiseStrategyStatus advertiseStrategyStatus : advertiseStrategyStatusList) {
            ControlledObjectRpc.Builder coBuilder = ControlledObjectRpc.newBuilder();
            coBuilder.setPuid(advertiseStrategyStatus.getPuid());
            coBuilder.setShopId(advertiseStrategyStatus.getShopId());
            coBuilder.setStatus(advertiseStrategyStatus.getStatus());
            coBuilder.setAdType(advertiseStrategyStatus.getAdType());
            coBuilder.setMarketplaceId(advertiseStrategyStatus.getMarketplaceId());
            if (advertiseStrategyStatus.getIsTargetItem() != null) {
                coBuilder.setIsTargetItem(advertiseStrategyStatus.getIsTargetItem());
            }
            if (advertiseStrategyTemplate.getVersion() > advertiseStrategyStatus.getVersion()) {
                coBuilder.setUpdateStatus("notUpdate");
                //未更新策略展示
                if ("CAMPAIGN".equals(advertiseStrategyStatus.getItemType())) {
                    List<CampaignRuleVo> campaignRuleVoList = JSONUtil.jsonToArray(advertiseStrategyStatus.getRule(),CampaignRuleVo.class);
                    List<CampaignRule> campaignRuleList = new ArrayList<>(campaignRuleVoList.size());
                    Map<Integer,List<CampaignRuleVo>> map = campaignRuleVoList.stream().
                            collect(Collectors.groupingBy(CampaignRuleVo::getSiteDate));
                    for (Integer siteDate : map.keySet()) {
                        CampaignRule.Builder campaignRuleBuilder = CampaignRule.newBuilder();
                        campaignRuleBuilder.setSiteDate(siteDate);
                        for (CampaignRuleVo campaignRuleVo : map.get(siteDate)) {
                            Campaign.Builder campaign = Campaign.newBuilder();
                            campaign.setBudgetValue(campaignRuleVo.getBudgetValue().doubleValue());
                            campaign.setStartTimeSite(campaignRuleVo.getStartTimeSite());
                            campaign.setEndTimeSite(campaignRuleVo.getEndTimeSite());
                            if ("numerical".equals(advertiseStrategyStatus.getChildrenItemType())) {
                                campaign.setBudgetType(campaignRuleVo.getBudgetType());
                                MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(advertiseStrategyStatus.getMarketplaceId());
                                if (m != null) {
                                    campaign.setSymbol(m.getCurrencyCode());
                                }
                            } else {
                                campaign.setSymbol("%");
                            }
                            campaignRuleBuilder.addCampaigns(campaign);
                        }
                        if (siteDate == 0) {
                            campaignRuleBuilder.setSiteDateName("每日");
                        } else {
                            campaignRuleBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                    cpc.util.Constants.getDateMap().get(siteDate));
                        }
                        campaignRuleList.add(campaignRuleBuilder.build());
                    }
                    coBuilder.addAllCampaignRuleList(campaignRuleList);
                } else if ("CAMPAIGN_PLACEMENT".equals(advertiseStrategyStatus.getItemType())) {
                    List<CampaignPlacementRuleVo> campaignPlacementRuleVoList = JSONUtil.jsonToArray(advertiseStrategyStatus.getRule(),CampaignPlacementRuleVo.class);
                    List<CampaignPlacementRule> campaignPlacementRuleList = new ArrayList<>(campaignPlacementRuleVoList.size());
                    Map<Integer,List<CampaignPlacementRuleVo>> map = campaignPlacementRuleVoList.stream().
                            collect(Collectors.groupingBy(CampaignPlacementRuleVo::getSiteDate));
                    for (Integer siteDate : map.keySet()) {
                        CampaignPlacementRule.Builder campaignPlacementRuleBuilder =CampaignPlacementRule.newBuilder();
                        campaignPlacementRuleBuilder.setSiteDate(siteDate);
                        for (CampaignPlacementRuleVo campaignPlacementRuleVo : map.get(siteDate)) {
                            CampaignPlacement.Builder campaignPlacement = CampaignPlacement.newBuilder();
                            campaignPlacement.setAdPlaceProductType(campaignPlacementRuleVo.getAdPlaceProductType());
                            campaignPlacement.setStartTimeSite(campaignPlacementRuleVo.getStartTimeSite());
                            campaignPlacement.setEndTimeSite(campaignPlacementRuleVo.getEndTimeSite());
                            campaignPlacement.setAdPlaceProductValue(campaignPlacementRuleVo.getAdPlaceProductValue().doubleValue());
                            if (campaignPlacementRuleVo.getAdPlaceTopMaxValue() != null) {
                                campaignPlacement.setAdPlaceTopMaxValue(campaignPlacementRuleVo.getAdPlaceTopMaxValue().doubleValue());
                            }
                            if (campaignPlacementRuleVo.getAdPlaceTopMinValue() != null) {
                                campaignPlacement.setAdPlaceTopMinValue(campaignPlacementRuleVo.getAdPlaceTopMinValue().doubleValue());
                            }
                            if (campaignPlacementRuleVo.getAdPlaceProductMaxValue() != null) {
                                campaignPlacement.setAdPlaceProductMaxValue(campaignPlacementRuleVo.getAdPlaceProductMaxValue().doubleValue());
                            }
                            if (campaignPlacementRuleVo.getAdPlaceProductMinValue() != null) {
                                campaignPlacement.setAdPlaceProductMinValue(campaignPlacementRuleVo.getAdPlaceProductMinValue().doubleValue());
                            }
                            if (campaignPlacementRuleVo.getAdOtherType() != null) {
                                campaignPlacement.setAdOtherType(campaignPlacementRuleVo.getAdOtherType());
                            } else {
                                campaignPlacement.setAdOtherType(4);
                            }
                            if (campaignPlacementRuleVo.getAdOtherValue() != null) {
                                campaignPlacement.setAdOtherValue(campaignPlacementRuleVo.getAdOtherValue().doubleValue());
                            } else {
                                campaignPlacement.setAdOtherValue(0);
                            }
                            campaignPlacement.setAdPlaceTopType(campaignPlacementRuleVo.getAdPlaceTopType());
                            campaignPlacement.setAdPlaceTopValue(campaignPlacementRuleVo.getAdPlaceTopValue().doubleValue());
                            if (StringUtils.isNotBlank(campaignPlacementRuleVo.getStrategy())) {
                                campaignPlacement.setStrategy(campaignPlacementRuleVo.getStrategy());
                            }
                            campaignPlacement.setSymbol("%");
                            campaignPlacementRuleBuilder.addCampaignPlacements(campaignPlacement);
                        }
                        if (siteDate == 0) {
                            campaignPlacementRuleBuilder.setSiteDateName("每日");
                        } else {
                            campaignPlacementRuleBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                    cpc.util.Constants.getDateMap().get(siteDate));
                        }
                        campaignPlacementRuleList.add(campaignPlacementRuleBuilder.build());
                    }
                    coBuilder.addAllCampaignPlacementRuleList(campaignPlacementRuleList);
                } else if ("TARGET".equals(advertiseStrategyStatus.getItemType())) {
                    List<TargetRuleVo> targetRuleVoList = JSONUtil.jsonToArray(advertiseStrategyStatus.getRule(),TargetRuleVo.class);
                    List<TargetRule> targetRuleList = new ArrayList<>(targetRuleVoList.size());
                    Map<Integer,List<TargetRuleVo>> map = targetRuleVoList.stream().
                            collect(Collectors.groupingBy(TargetRuleVo::getSiteDate));
                    for (Integer siteDate : map.keySet()) {
                        TargetRule.Builder targetRuleBuilder = TargetRule.newBuilder();
                        targetRuleBuilder.setSiteDate(siteDate);
                        for (TargetRuleVo targetRuleVo:map.get(siteDate)) {
                            Target.Builder target = Target.newBuilder();
                            target.setStartTimeSite(targetRuleVo.getStartTimeSite());
                            target.setEndTimeSite(targetRuleVo.getEndTimeSite());
                            target.setBiddingType(targetRuleVo.getBiddingType());
                            target.setBiddingValue(targetRuleVo.getBiddingValue().doubleValue());
                            if (targetRuleVo.getBiddingMaxValue() != null) {
                                target.setBiddingMaxValue(targetRuleVo.getBiddingMaxValue().doubleValue());
                            }
                            if (targetRuleVo.getBiddingMinValue() != null) {
                                target.setBiddingMinValue(targetRuleVo.getBiddingMinValue().doubleValue());
                            }
                            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(advertiseStrategyStatus.getMarketplaceId());
                            if (null != m) {
                                target.setSymbol(m.getCurrencyCode());
                            }
                            targetRuleBuilder.addTargets(target);
                        }
                        if (siteDate == 0) {
                            targetRuleBuilder.setSiteDateName("每日");
                        } else {
                            targetRuleBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                    cpc.util.Constants.getDateMap().get(siteDate));
                        }
                        targetRuleList.add(targetRuleBuilder.build());
                    }
                    coBuilder.addAllTargetRuleList(targetRuleList);
                } else if ("START_STOP".equals(advertiseStrategyStatus.getItemType())) {
                    List<StartStopRuleVo> startStopRuleVoList = JSONUtil.jsonToArray(advertiseStrategyStatus.getRule(), StartStopRuleVo.class);
                    List<StartStopStateRule> startStopStateRuleList = Lists.newArrayList();
                    Map<Integer,List<StartStopRuleVo>> map = startStopRuleVoList.stream().collect(Collectors.groupingBy(StartStopRuleVo::getSiteDate));
                    for (Integer siteDate : map.keySet()) {
                        StartStopStateRule.Builder startStopStateRuleBuilder = StartStopStateRule.newBuilder();
                        startStopStateRuleBuilder.setSiteDate(siteDate);
                        if (siteDate == 0) {
                            startStopStateRuleBuilder.setSiteDateName("每日");
                        } else {
                            startStopStateRuleBuilder.setSiteDateName(com.meiyunji.sponsored.service.cpc.util.Constants.getDateMap().get(siteDate));
                        }
                        for (StartStopRuleVo startStopRuleVo : map.get(siteDate)) {
                            StateRule.Builder stateBuilder = StateRule.newBuilder();
                            stateBuilder.setStartTimeSite(startStopRuleVo.getStartTimeSite());
                            stateBuilder.setEndTimeSite(startStopRuleVo.getEndTimeSite());
                            stateBuilder.setState(startStopRuleVo.getState());
                            startStopStateRuleBuilder.addStateRules(stateBuilder);
                        }
                        startStopStateRuleList.add(startStopStateRuleBuilder.build());
                    }
                    coBuilder.addAllStartStopStateRuleList(startStopStateRuleList);
                } else if ("PORTFOLIO".equals(advertiseStrategyStatus.getItemType())) {
                    if (PbUtil.isPortfolioHour(advertiseStrategyStatus.getChildrenItemType())) {
                        builder.addAllPortfolioHourRuleList(PbUtil.buildPortfolioHourRuleByJson(advertiseStrategyStatus.getRule(), advertiseStrategyStatus.getMarketplaceId()));
                    } else {
                        List<PortfolioRuleVo> portfolioRuleList = JSONUtil.jsonToArray(advertiseStrategyStatus.getRule(), PortfolioRuleVo.class);
                        List<PortfolioRule> portfolioRules = com.google.common.collect.Lists.newArrayList();
                        Map<Integer,List<PortfolioRuleVo>> map = new LinkedHashMap<>();
                        Map<Integer,List<PortfolioRuleVo>> listMap = new LinkedHashMap<>();
                        if ("MONTH_DAY".equals(advertiseStrategyStatus.getType())) {
                            portfolioRuleList.stream().collect(Collectors.groupingBy(e->
                                            Integer.valueOf(LocalDate.parse(e.getStartDate(),DateTimeFormatter.ofPattern(DateUtil.PATTERN)).
                                                    format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD))))).entrySet().stream()
                                    .sorted(Map.Entry.comparingByKey()).forEachOrdered(x->listMap.put(x.getKey(),x.getValue()));
                        } else {
                            map = portfolioRuleList.stream().collect(Collectors.groupingBy(PortfolioRuleVo::getSiteDate));
                        }
                        if (MapUtils.isNotEmpty(map)) {
                            for (Integer siteDate : map.keySet()) {
                                PortfolioRule.Builder portfolioBuilder = PortfolioRule.newBuilder();
                                portfolioBuilder.setSiteDate(siteDate);
                                if (siteDate == 0) {
                                    portfolioBuilder.setSiteDateName("每日");
                                } else {
                                    portfolioBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                            cpc.util.Constants.getDateMap().get(siteDate));
                                }
                                for (PortfolioRuleVo portfolioRuleVo : map.get(siteDate)) {
                                    Portfolio.Builder portfolio = Portfolio.newBuilder();
                                    if (!"noBudget".equals(portfolioRuleVo.getAmount())) {
                                        portfolio.setAmount(portfolioRuleVo.getAmount().doubleValue());
                                    }
                                    portfolio.setPolicy(portfolioRuleVo.getPolicy());
                                    MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(advertiseStrategyStatus.getMarketplaceId());
                                    if(null != m){
                                        portfolio.setSymbol(m.getCurrencyCode());
                                    }
                                    portfolioBuilder.addPortfolios(portfolio.build());
                                }
                                portfolioRules.add(portfolioBuilder.build());
                            }
                        }
                        if (MapUtils.isNotEmpty(listMap)) {
                            for (Integer countDate : listMap.keySet()) {
                                PortfolioRule.Builder portfolioBuilder = PortfolioRule.newBuilder();
                                portfolioBuilder.setStartDate(listMap.get(countDate).get(0).getStartDate());
                                portfolioBuilder.setEndDate(listMap.get(countDate).get(0).getEndDate());
                                for (PortfolioRuleVo portfolioRuleVo : listMap.get(countDate)) {
                                    Portfolio.Builder portfolio = Portfolio.newBuilder();
                                    if (!"noBudget".equals(portfolioRuleVo.getAmount())) {
                                        portfolio.setAmount(portfolioRuleVo.getAmount().doubleValue());
                                    }
                                    portfolio.setPolicy(portfolioRuleVo.getPolicy());
                                    MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(advertiseStrategyStatus.getMarketplaceId());
                                    if(null != m){
                                        portfolio.setSymbol(m.getCurrencyCode());
                                    }
                                    portfolioBuilder.addPortfolios(portfolio.build());
                                }
                                portfolioRules.add(portfolioBuilder.build());
                            }
                        }
                        builder.addAllPortfolioRuleList(portfolioRules);
                    }
                }
            } else {
                coBuilder.setUpdateStatus("update");
            }
            if (StringUtils.isNotBlank(advertiseStrategyStatus.getAdTargetType())) {
                coBuilder.setAdTargetType(advertiseStrategyStatus.getAdTargetType());
            }
            if (StringUtils.isNotBlank(advertiseStrategyStatus.getCampaignId())) {
                coBuilder.setCampaignId(advertiseStrategyStatus.getCampaignId());
            }
            if (StringUtils.isNotBlank(advertiseStrategyStatus.getCampaignName())) {
                coBuilder.setCampaignName(advertiseStrategyStatus.getCampaignName());
            }
            if (StringUtils.isNotBlank(advertiseStrategyStatus.getGroupName())) {
                coBuilder.setGroupName(advertiseStrategyStatus.getGroupName());
            }
            if (StringUtils.isNotBlank(advertiseStrategyStatus.getAdGroupId())) {
                coBuilder.setGroupId(advertiseStrategyStatus.getAdGroupId());
            }
            if (StringUtils.isNotBlank(advertiseStrategyStatus.getTargetName())) {
                String targetName = AutoTargetTypeEnum.getAutoTargetValue(advertiseStrategyStatus.getTargetName());
                if (StringUtils.isNotBlank(targetName)) {
                    coBuilder.setTargetName(targetName);
                } else {
                    coBuilder.setTargetName(advertiseStrategyStatus.getTargetName());
                }
            }
            if (StringUtils.isNotBlank(advertiseStrategyStatus.getTargetType())) {
                coBuilder.setTargetType(advertiseStrategyStatus.getTargetType());
            }
            if (StringUtils.isNotBlank(advertiseStrategyStatus.getPortfolioId())) {
                coBuilder.setPortfolioId(advertiseStrategyStatus.getPortfolioId());
            }
            if (StringUtils.isNotBlank(advertiseStrategyStatus.getPortfolioName())) {
                coBuilder.setPortfolioName(advertiseStrategyStatus.getPortfolioName());
            }
            if (StringUtils.isNotBlank(advertiseStrategyStatus.getMatchType())) {
                coBuilder.setMatchType(advertiseStrategyStatus.getMatchType());
            }
            if (StringUtils.isNotBlank(advertiseStrategyStatus.getMatchName())) {
                coBuilder.setMatchName(advertiseStrategyStatus.getMatchName());
            }
            coBuilder.setStatus(advertiseStrategyStatus.getStatus());
            coBuilder.setId(advertiseStrategyStatus.getId());
            coBuilder.setTaskId(advertiseStrategyStatus.getTaskId());
            Optional.ofNullable(advertiseStrategyStatus.getCostType()).filter(StringUtils::isNotEmpty).ifPresent(coBuilder::setCostType);
            Optional.ofNullable(advertiseStrategyStatus.getAdGoal()).filter(StringUtils::isNotEmpty)
                    .map(SBCampaignGoalEnum::getSBCampaignGoalEnumByType).map(SBCampaignGoalEnum::getCode).map(String::valueOf).ifPresent(coBuilder::setGoal);
            Optional.ofNullable(advertiseStrategyStatus.getAdFormat()).filter(StringUtils::isNotEmpty).ifPresent(coBuilder::setAdFormat);
            Optional.ofNullable(advertiseStrategyStatus.getBudgetType()).filter(StringUtils::isNotEmpty).ifPresent(coBuilder::setBudgetType);

            //SD详细
            convertSdTargetDetailInfo(advertiseStrategyStatus, coBuilder);

            if (request.getItemType().equals("CAMPAIGN")) {
                coBuilder.setBudgetValue(advertiseStrategyStatus.getBudgetValue().doubleValue());
            }
            if (request.getItemType().equals("CAMPAIGN_PLACEMENT")) {
                coBuilder.setAdPlaceTopValue(advertiseStrategyStatus.getAdPlaceTopValue().doubleValue());
                coBuilder.setAdPlaceProductValue(advertiseStrategyStatus.getAdPlaceProductValue().doubleValue());
                if (StringUtils.isNotBlank(advertiseStrategyStatus.getStrategy())) {
                    coBuilder.setStrategy(advertiseStrategyStatus.getStrategy());
                }
                if (advertiseStrategyStatus.getAdOtherValue() != null) {
                    coBuilder.setAdOtherValue(advertiseStrategyStatus.getAdOtherValue().doubleValue());
                } else {
                    coBuilder.setAdOtherValue(0);
                }
            }
            if ("TARGET".equals(request.getItemType())) {
                if (StringUtils.isNotBlank(advertiseStrategyStatus.getServingStatus())) {
                    coBuilder.setServingStatus(advertiseStrategyStatus.getServingStatus());
                } else {
                    coBuilder.setServingStatus("");
                }
                if (StringUtils.isNotBlank(advertiseStrategyStatus.getServingStatusName())) {
                    coBuilder.setServingStatusName(advertiseStrategyStatus.getServingStatusName());
                } else {
                    coBuilder.setServingStatusName("");
                }
                if (StringUtils.isNotBlank(advertiseStrategyStatus.getServingStatusDec())) {
                    coBuilder.setServingStatusDec(advertiseStrategyStatus.getServingStatusDec());
                } else {
                    coBuilder.setServingStatusDec("");
                }
                if (advertiseStrategyStatus.getBiddingValue() != null) {
                    coBuilder.setBiddingValue(advertiseStrategyStatus.getBiddingValue().doubleValue());
                }
                if (StringUtils.isNotBlank(advertiseStrategyStatus.getItemId())) {
                    coBuilder.setTargetId(advertiseStrategyStatus.getItemId());
                }
            }
            if (request.getItemType().equals("PORTFOLIO")) {
                if (StringUtils.isNotBlank(advertiseStrategyStatus.getServingStatus())) {
                    coBuilder.setServingStatus(advertiseStrategyStatus.getServingStatus());
                    AmazonAdPortfolio.servingStatusEnum byCode = UCommonUtil.getByCode(advertiseStrategyStatus.getServingStatus(), AmazonAdPortfolio.servingStatusEnum.class);
                    if(byCode != null){
                        coBuilder.setServingStatusName(byCode.getName());
                    }else {
                        coBuilder.setServingStatusName(advertiseStrategyStatus.getServingStatus());
                    }
                }
                if (advertiseStrategyStatus.getAmount() != null) {
                    coBuilder.setAmount(advertiseStrategyStatus.getAmount().toString());
                } else {
                    coBuilder.setAmount("-");
                }
                if (StringUtils.isNotBlank(advertiseStrategyStatus.getPolicy())) {
                    coBuilder.setPolicy(advertiseStrategyStatus.getPolicy());
                } else {
                    coBuilder.setPolicy("-");
                }
                if (StringUtils.isNotBlank(advertiseStrategyStatus.getBudgetStartDate())) {
                    coBuilder.setBudgetStartDate(advertiseStrategyStatus.getBudgetStartDate());
                } else {
                    coBuilder.setBudgetStartDate("-");
                }
                if (StringUtils.isNotBlank(advertiseStrategyStatus.getBudgetEndDate())) {
                    coBuilder.setBudgetEndDate(advertiseStrategyStatus.getBudgetEndDate());
                } else {
                    coBuilder.setBudgetEndDate("-");
                }
                if (StringUtils.isNotBlank(advertiseStrategyStatus.getChildrenItemType())) {
                    coBuilder.setChildrenItemType(advertiseStrategyStatus.getChildrenItemType());
                }
            }

            if (request.getItemType().equals("START_STOP")) {
                if (StringUtils.isNotBlank(advertiseStrategyStatus.getItemId())) {
                    coBuilder.setAdId(advertiseStrategyStatus.getItemId());
                }

                if (StringUtils.isNotBlank(advertiseStrategyStatus.getAsin())) {
                    coBuilder.setAsin(advertiseStrategyStatus.getAsin());
                }

                if (StringUtils.isNotBlank(advertiseStrategyStatus.getSku())) {
                    coBuilder.setMsku(advertiseStrategyStatus.getSku());
                }
            }

            if (StringUtils.isNotBlank(advertiseStrategyStatus.getState())) {
                coBuilder.setState(advertiseStrategyStatus.getState());
            }
            if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(advertiseStrategyStatus.getShopId())) {
                coBuilder.setShopName(shopAuthMap.get(advertiseStrategyStatus.getShopId()).getName());
            }
            list.add(coBuilder.build());
        }
        builder.addAllRows(list);

        responseBuilder.setCode(result.getCode());
        responseBuilder.setData(builder.build());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    private void convertSdTargetDetailInfo(AdvertiseStrategyStatus advertiseStrategyStatus, ControlledObjectRpc.Builder coBuilder) {
        if(advertiseStrategyStatus instanceof AdvertiseStrategySdTargetControlledVo) {
            AdvertiseStrategySdTargetControlledVo controlledVo = (AdvertiseStrategySdTargetControlledVo) advertiseStrategyStatus;
            if (StringUtils.isNotBlank(controlledVo.getDetailTargetText())) {
                coBuilder.setDetailTargetText(controlledVo.getDetailTargetText());
            }
            if (StringUtils.isNotBlank(controlledVo.getDetailType())) {
                coBuilder.setDetailType(controlledVo.getDetailType());
            }
            if (StringUtils.isNotBlank(controlledVo.getDetailTitle())) {
                coBuilder.setDetailTitle(controlledVo.getDetailTitle());
            }
            if (StringUtils.isNotBlank(controlledVo.getDetailTargetType())) {
                coBuilder.setDetailTargetType(controlledVo.getDetailTargetType());
            }
            if (StringUtils.isNotBlank(controlledVo.getDetailCategory())) {
                coBuilder.setDetailCategory(controlledVo.getDetailCategory());
            }
            if (StringUtils.isNotBlank(controlledVo.getDetailBrandName())) {
                coBuilder.setDetailBrandName(controlledVo.getDetailBrandName());
            }
            if (StringUtils.isNotBlank(controlledVo.getDetailCommodityPriceRange())) {
                coBuilder.setDetailCommodityPriceRange(controlledVo.getDetailCommodityPriceRange());
            }
            if (StringUtils.isNotBlank(controlledVo.getDetailRating())) {
                coBuilder.setDetailRating(controlledVo.getDetailRating());
            }
            if (StringUtils.isNotBlank(controlledVo.getDetailDistribution())) {
                coBuilder.setDetailDistribution(controlledVo.getDetailDistribution());
            }
            if (StringUtils.isNotBlank(controlledVo.getDetailLookback())) {
                coBuilder.setDetailLookback(controlledVo.getDetailLookback());
            }
        }
    }

    private int getAdGroupStrategyMaxSize(int puid) {
        try {
            StrategyLimitConfig strategyLimitConfig = strategyLimitConfigDao.queryStrategyConfigByPuid(puid);
            if (strategyLimitConfig == null || strategyLimitConfig.getGroupControlledObjectNumLimit() <= 0) {
                return indexStrategyConfig.getAdGroupStrategyMaxSize();
            }
            return strategyLimitConfig.getGroupControlledObjectNumLimit();
        } catch (Exception e) {
            log.error("getAdGroupStrategyMaxSize error", e);
        }
        return indexStrategyConfig.getAdGroupStrategyMaxSize();
    }

    @Override
    public void submitStrategy(AdStrategyStatusAddRequest request, StreamObserver<AdStrategyStatusAddResponse> responseObserver) {
        log.info("策略确认提交 submitStrategy request:{}", request);
        try {
            AdStrategyStatusAddResponse.Builder responseBuilder = AdStrategyStatusAddResponse.newBuilder();
            List<SubmitStrategyVo> paramList = Lists.newArrayList();
            List<AdStrategyStatusRpc> adStrategyStatusRpcList = request.getStatusRpcList();
            AdvertiseStrategyTemplate advertiseStrategyTemplate = advertiseStrategyTemplateDao.selectByPrimaryKey(request.getPuid(), request.getTemplateId());
            if (advertiseStrategyTemplate == null) {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg("当前模板已被删除");
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }

            if (!request.getItemType().equals(advertiseStrategyTemplate.getItemType())) {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg("模板受控对象类型和添加的受控对象不一致");
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }

            if ("AD_GROUP_TARGET".equals(request.getAddWayType())) {
                Integer count = advertiseStrategyAdGroupDao.getCount(request.getPuid(),  request.getTemplateId());
                int adGroupStrategyMaxSize = getAdGroupStrategyMaxSize(request.getPuid());
                if (adStrategyStatusRpcList.size() + count > adGroupStrategyMaxSize) {
                    responseBuilder.setCode(Result.ERROR);
                    responseBuilder.setMsg(String.format(AD_GROUP_STRATEGY_MAX_COUNT_MSG, adGroupStrategyMaxSize));
                    responseObserver.onNext(responseBuilder.build());
                    responseObserver.onCompleted();
                    return;
                }
            } else {
                Integer count = advertiseStrategyStatusDao.getCount(request.getPuid(), request.getTemplateId());
                if (count + adStrategyStatusRpcList.size() > indexStrategyConfig.getAdStrategyMaxSize()) {
                    responseBuilder.setCode(Result.ERROR);
                    responseBuilder.setMsg(String.format(Constants.AD_STRATEGY_MAX_COUNT_MSG, advertiseStrategyTemplate.getTemplateName(), count, indexStrategyConfig.getAdStrategyMaxSize()));
                    responseObserver.onNext(responseBuilder.build());
                    responseObserver.onCompleted();
                    return;
                }
            }
            if (CollectionUtils.isNotEmpty(adStrategyStatusRpcList)) {
                for (AdStrategyStatusRpc vo : adStrategyStatusRpcList) {
                    SubmitStrategyVo submitStrategyVo = new SubmitStrategyVo();
                    submitStrategyVo.setShopId(vo.getShopId());
                    submitStrategyVo.setMarketplaceId(vo.getMarketplaceId());
                    submitStrategyVo.setItemId(vo.getItemId());
                    submitStrategyVo.setAdType(vo.getAdType().toUpperCase(Locale.ROOT));
                    submitStrategyVo.setAdGroupId(vo.getAdGroupId());
                    submitStrategyVo.setCampaignId(vo.getCampaignId());
                    submitStrategyVo.setTargetType(vo.getTargetType());
                    submitStrategyVo.setTargetName(vo.getTargetName());
                    submitStrategyVo.setOriginBudgetValue(BigDecimal.valueOf(vo.getOriginBudgetValue()));
                    submitStrategyVo.setOriginStrategy(vo.getOriginStrategy());
                    submitStrategyVo.setOriginAdPlaceTopValue(BigDecimal.valueOf(vo.getOriginAdPlaceTopValue()));
                    submitStrategyVo.setOriginAdPlaceProductValue(BigDecimal.valueOf(vo.getOriginAdPlaceProductValue()));
                    submitStrategyVo.setOriginAdOtherValue(BigDecimal.valueOf(vo.getOriginAdOtherValue()));
                    submitStrategyVo.setOriginBiddingValue(BigDecimal.valueOf(vo.getOriginBiddingValue()));
                    submitStrategyVo.setOriginState(vo.getOriginState());
                    if (vo.hasDefaultBid()) {
                        submitStrategyVo.setDefaultBid(BigDecimal.valueOf(vo.getDefaultBid()));
                    }
                    if (vo.hasAmount()) {
                        submitStrategyVo.setAmount(BigDecimal.valueOf(vo.getAmount()));
                    }
                    submitStrategyVo.setPolicy(vo.getPolicy());
                    submitStrategyVo.setBudgetEndDate(vo.getBudgetEndDate());
                    submitStrategyVo.setBudgetStartDate(vo.getBudgetStartDate());
                    submitStrategyVo.setStatus(vo.getStatus());
                    submitStrategyVo.setStartStopItemType(vo.getStartStopItemType());
                    submitStrategyVo.setAdTargetType(vo.getAdTargetType());
                    submitStrategyVo.setTargetName(vo.getTargetName());
                    submitStrategyVo.setAsin(vo.getAsin());
                    submitStrategyVo.setSku(vo.getSku());
                    submitStrategyVo.setTargetName(vo.getTargetName());
                    paramList.add(submitStrategyVo);
                }
                Vector<AddStrategyVo> addStrategyVos = new Vector<>();
                if (paramList.size() > Constants.STRATEGY_PARTITION_SIZE) {
                    List<List<SubmitStrategyVo>> submitStrategyListPartition = Lists.partition(paramList, Constants.STRATEGY_PARTITION_SIZE);
                    ThreadPoolExecutor threadPoolExecutor = ThreadPoolUtil.getTimeSharingStrategyThreadPool();
                    CountDownLatch countDownLatch = new CountDownLatch(submitStrategyListPartition.size());
                    for (List<SubmitStrategyVo> submitStrategyVoList : submitStrategyListPartition) {
                        threadPoolExecutor.execute(() -> {
                            try {
                                AddStrategyVo addStrategyVo = null;
                                if (AdItemType.START_STOP.name().equals(request.getItemType()) || AdItemType.PORTFOLIO.name().equals(request.getItemType())) {
                                    String itemType = request.getItemType();
                                    if (AdItemType.PORTFOLIO.name().equals(request.getItemType()) && PbUtil.isPortfolioHour(advertiseStrategyTemplate.getChildrenItemType())) {
                                        itemType = Constants.PORTFOLIO_HOUR;
                                    }
                                    addStrategyVo = adStrategyStatusApiFactory.getStrategyStatusService(itemType).submitStrategy(request.getPuid(), submitStrategyVoList,
                                            request.getTemplateId(), request.getUpdateId(), request.getLoginIp(), request.getTraceId());
                                } else {
                                    if (request.hasAddWayType() && "AD_GROUP_TARGET".equals(request.getAddWayType())) {
                                        addStrategyVo = advertiseStrategyGroupTargetBidService.submitStrategy(request.getPuid(), submitStrategyVoList,
                                                request.getTemplateId(), request.getUpdateId(), request.getLoginIp(), request.getTraceId());
                                    } else {
                                        addStrategyVo = advertiseStrategyStatusService.submitStrategy(request.getPuid(), submitStrategyVoList,
                                                request.getTemplateId(), request.getUpdateId(), request.getLoginIp(), request.getTraceId());
                                    }
                                }
                                if (addStrategyVo != null) {
                                    addStrategyVos.add(addStrategyVo);
                                }
                            } catch (Exception e) {
                                log.error("submitStrategyThread request fail:{}, puid:{}, itemType:{}, traceId:{}", e.getMessage(), request.getPuid(), request.getItemType(), request.getTraceId());
                            } finally {
                                countDownLatch.countDown();
                            }
                        });
                    }
                    countDownLatch.await();
                } else {
                    AddStrategyVo addStrategyVo;
                    if (AdItemType.START_STOP.name().equals(request.getItemType()) || AdItemType.PORTFOLIO.name().equals(request.getItemType())) {
                        String itemType = request.getItemType();
                        if (AdItemType.PORTFOLIO.name().equals(request.getItemType()) && PbUtil.isPortfolioHour(advertiseStrategyTemplate.getChildrenItemType())) {
                            itemType = Constants.PORTFOLIO_HOUR;
                        }
                        addStrategyVo = adStrategyStatusApiFactory.getStrategyStatusService(itemType).submitStrategy(request.getPuid(), paramList,
                                request.getTemplateId(), request.getUpdateId(), request.getLoginIp(), request.getTraceId());
                    } else {
                        if (request.hasAddWayType() && request.getAddWayType().equals("AD_GROUP_TARGET")) {
                            addStrategyVo = advertiseStrategyGroupTargetBidService.submitStrategy(request.getPuid(), paramList,
                                    request.getTemplateId(), request.getUpdateId(), request.getLoginIp(), request.getTraceId());
                        } else {
                            addStrategyVo = advertiseStrategyStatusService.submitStrategy(request.getPuid(), paramList,
                                    request.getTemplateId(), request.getUpdateId(), request.getLoginIp(), request.getTraceId());
                        }
                    }
                    if (addStrategyVo != null) {
                        addStrategyVos.add(addStrategyVo);
                    }
                }
                if (CollectionUtils.isNotEmpty(addStrategyVos)) {
                    StatusAddResponse.Builder status = StatusAddResponse.newBuilder();
                    for (AddStrategyVo addStrategyVo : addStrategyVos) {
                        if (addStrategyVo != null) {
                            status.addAllItemIds(addStrategyVo.getItemIds());
                            if (StringUtils.isNotBlank(addStrategyVo.getMsg())) {
                                status.setMsg(addStrategyVo.getMsg());
                            }
                        }
                    }
                    responseBuilder.setCode(Result.SUCCESS);
                    responseBuilder.setData(status.build());
                } else {
                    responseBuilder.setCode(Result.ERROR);
                    responseBuilder.setMsg("无数据提交");
                }
                responseObserver.onNext(responseBuilder.build());
            }
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("traceId:{} puid:{} itemType:{} 策略提交异常: ", request.getTraceId(), request.getPuid(), request.getItemType(), e);
            responseObserver.onError(e);
        }
    }


    @Override
    public void updateStrategy(AdStrategyStatusUpdateRequest request,StreamObserver<AdStrategyStatusUpdateResponse> responseObserver) {
        log.info("策略更新 updateStrategy request:{}", request);
        AdStrategyStatusUpdateResponse.Builder responseBuilder = AdStrategyStatusUpdateResponse.newBuilder();
        boolean hasError = false;
        if (!request.hasPuid() || !request.hasTemplateId()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            List<UpdateStrategyVo> updateStrategyVoList = Lists.newArrayList();
            AdvertiseStrategyTemplate vo = advertiseStrategyTemplateDao.selectByPrimaryKey(request.getPuid(),request.getTemplateId());
            if (CollectionUtils.isNotEmpty(request.getStatusRpcList())) {
                for (AdStrategyStatusRpc adStrategyStatusRpc : request.getStatusRpcList()) {
                    UpdateStrategyVo updateStrategyVo = new UpdateStrategyVo();
                    updateStrategyVo.setStatusId(adStrategyStatusRpc.getStatusId());
                    updateStrategyVo.setStatus(adStrategyStatusRpc.getStatus());
                    if (adStrategyStatusRpc.hasOriginBudgetValue()) {
                        updateStrategyVo.setBudgetValue(BigDecimal.valueOf(adStrategyStatusRpc.getOriginBudgetValue()));
                    }
                    if (adStrategyStatusRpc.hasOriginAdPlaceTopValue()) {
                        updateStrategyVo.setAdPlaceTopValue(BigDecimal.valueOf(adStrategyStatusRpc.getOriginAdPlaceTopValue()));
                    }
                    if (adStrategyStatusRpc.hasOriginAdPlaceProductValue()) {
                        updateStrategyVo.setAdPlaceProductValue(BigDecimal.valueOf(adStrategyStatusRpc.getOriginAdPlaceProductValue()));
                    }
                    if (adStrategyStatusRpc.hasOriginStrategy()) {
                        updateStrategyVo.setStrategy(adStrategyStatusRpc.getOriginStrategy());
                    }
                    if (adStrategyStatusRpc.hasOriginBiddingValue()) {
                        updateStrategyVo.setBiddingValue(BigDecimal.valueOf(adStrategyStatusRpc.getOriginBiddingValue()));
                    }
                    if (adStrategyStatusRpc.hasOriginState()) {
                        updateStrategyVo.setState(adStrategyStatusRpc.getOriginState());
                    }
                    if (adStrategyStatusRpc.hasOriginAdOtherValue()) {
                        updateStrategyVo.setAdOtherValue(BigDecimal.valueOf(adStrategyStatusRpc.getOriginAdOtherValue()));
                    }
                    updateStrategyVoList.add(updateStrategyVo);
                }
            }
            Integer count = advertiseStrategyTaskDao.queryCountByTemplateId(request.getPuid(), request.getTemplateId());
            if (count > 0) {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg("批量任务未完成，请稍后提交");
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
            try {
                Vector<Result<List<UpdateStrategyResponseVo>>> resultVector = new Vector<>();
                if (updateStrategyVoList.size() > Constants.STRATEGY_PARTITION_SIZE) {
                    List<List<UpdateStrategyVo>> updateStrategyListPartition = null;
                    // 分时策略动态分片
                    if (updateStrategyVoList.size() == 50) {
                        updateStrategyListPartition = Lists.partition(updateStrategyVoList, 5);
                    } else {
                        updateStrategyListPartition = Lists.partition(updateStrategyVoList, Constants.STRATEGY_PARTITION_SIZE);
                    }
                    ThreadPoolExecutor threadPoolExecutor = ThreadPoolUtil.getTimeSharingStrategyThreadPool();
                    CountDownLatch countDownLatch = new CountDownLatch(updateStrategyListPartition.size());
                    for (List<UpdateStrategyVo> updateStrategyVos : updateStrategyListPartition) {
                        threadPoolExecutor.execute(() -> {
                            try {
                                Result<List<UpdateStrategyResponseVo>> result = null;
                                if (AdItemType.START_STOP.name().equals(vo.getItemType()) || AdItemType.PORTFOLIO.name().equals(vo.getItemType())) {
                                    String itemType = vo.getItemType();
                                    if (AdItemType.PORTFOLIO.name().equals(vo.getItemType()) && PbUtil.isPortfolioHour(vo.getChildrenItemType())) {
                                        itemType = Constants.PORTFOLIO_HOUR;
                                    }
                                    result = adStrategyStatusApiFactory.getStrategyStatusService(itemType).updateStrategy(request.getPuid(), request.getTemplateId(),
                                            updateStrategyVos, vo, request.getUpdateId(), request.getTraceId());
                                } else {
                                    result = advertiseStrategyStatusService.updateStrategy(request.getPuid(), request.getTemplateId(),
                                            updateStrategyVos, vo, request.getUpdateId(), request.getTraceId());
                                }
                                if (result != null) {
                                    resultVector.add(result);
                                }
                            } catch (Exception e) {
                                log.error("submitStrategyThread request fail: {}, puid: {}, itemType{} traceId{}", e.getMessage(), request.getPuid(), vo.getItemType(), request.getTraceId());
                            } finally {
                                countDownLatch.countDown();
                            }
                        });
                    }
                    countDownLatch.await();
                } else {
                    Result<List<UpdateStrategyResponseVo>> result = null;
                    if (AdItemType.START_STOP.name().equals(vo.getItemType()) || AdItemType.PORTFOLIO.name().equals(vo.getItemType())) {
                        String itemType = vo.getItemType();
                        if (AdItemType.PORTFOLIO.name().equals(vo.getItemType()) && PbUtil.isPortfolioHour(vo.getChildrenItemType())) {
                            itemType = Constants.PORTFOLIO_HOUR;
                        }
                        result = adStrategyStatusApiFactory.getStrategyStatusService(itemType).updateStrategy(request.getPuid(), request.getTemplateId(),
                                updateStrategyVoList, vo, request.getUpdateId(), request.getTraceId());
                    } else {
                        result = advertiseStrategyStatusService.updateStrategy(request.getPuid(), request.getTemplateId(),
                                updateStrategyVoList, vo, request.getUpdateId(), request.getTraceId());
                    }
                    if (result != null) {
                        resultVector.add(result);
                    }
                }
                if (CollectionUtils.isNotEmpty(resultVector)) {
                    List<UpdateStrategyResponseVo> updateStrategyResponseVoList = Lists.newArrayList();
                    resultVector.forEach(e->{
                        if (CollectionUtils.isNotEmpty(e.getData())) {
                            updateStrategyResponseVoList.addAll(e.getData());
                        }
                        if (Result.ERROR == e.getCode()) {
                            responseBuilder.setCode(Result.ERROR);
                            responseBuilder.setMsg(e.getMsg());
                        }
                    });
                    if (CollectionUtils.isNotEmpty(updateStrategyResponseVoList)) {
                        updateStrategyResponseVoList.forEach(e->{
                            UpdateResponse.Builder builder = UpdateResponse.newBuilder();
                            if (StringUtils.isNotBlank(e.getItemId())) {
                                builder.setItemId(e.getItemId());
                            }
                            if (StringUtils.isNotBlank(e.getItemName())) {
                                builder.setItemName(e.getItemName());
                            }
                            if (StringUtils.isNotBlank(e.getPortfolioId())) {
                                builder.setPortfolioId(e.getPortfolioId());
                            }
                            if (StringUtils.isNotBlank(e.getPortfolioName())) {
                                builder.setPortfolioName(e.getPortfolioName());
                            }
                            if (StringUtils.isNotBlank(e.getCampaignId())) {
                                builder.setCampaignId(e.getCampaignId());
                            }
                            if (StringUtils.isNotBlank(e.getCampaignName())) {
                                builder.setCampaignName(e.getCampaignName());
                            }
                            if (StringUtils.isNotBlank(e.getAdGroupId())) {
                                builder.setAdGroupId(e.getAdGroupId());
                            }
                            if (StringUtils.isNotBlank(e.getAdGroupName())) {
                                builder.setAdGroupName(e.getAdGroupName());
                            }
                            if (StringUtils.isNotBlank(e.getTargetId())) {
                                builder.setTargetId(e.getTargetId());
                            }
                            if (StringUtils.isNotBlank(e.getTargetName())) {
                                builder.setTargetName(e.getTargetName());
                            }
                            if (StringUtils.isNotBlank(e.getAdType())) {
                                builder.setAdType(e.getAdType());
                            }
                            if (e.getIsGrayPlacement() != null) {
                                builder.setIsGrayPlacement(e.getIsGrayPlacement());
                            } else {
                                builder.setIsGrayPlacement(true);
                            }
                            if (StringUtils.isNotBlank(e.getMsg())) {
                                builder.setMsg(e.getMsg());
                            }
                            if (e.getStatusId() != null) {
                                builder.setStatusId(e.getStatusId());
                            }
                            responseBuilder.addData(builder.build());
                        });
                    }
                } else {
                    responseBuilder.setCode(Result.ERROR);
                    responseBuilder.setMsg("分时策略更新失败");
                }
                responseObserver.onNext(responseBuilder.build());
            } catch (Exception e) {
                hasError = true;
                log.error("traceId:{} puid:{} 策略更新异常: ", request.getTraceId(), request.getPuid(), e);
                responseObserver.onError(e);
            }
        }
        if (!hasError) {
            responseObserver.onCompleted();
        }
    }

    @Override
    public void updateStrategyStatus (UpdateStrategyStatusRequest request,StreamObserver<CommonResponse> responseObserver){
        log.info("策略状态更新 updateStrategyStatus request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasStatusId() || !request.hasStatus()) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("请求参数错误");
        } else {
            String itemType = null;
            AdvertiseStrategyStatus strategyStatus = advertiseStrategyStatusDao.getByStatusId(request.getPuid(), request.getStatusId());
            AdvertiseStrategyAdGroup advertiseStrategyAdGroup = advertiseStrategyAdGroupDao.getByStatusId(request.getPuid(), request.getStatusId());
            if (strategyStatus == null && advertiseStrategyAdGroup == null) {
                responseBuilder.setCode(Int32Value.of(Result.ERROR));
                responseBuilder.setMsg("当前受控对象不存在");
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
            if (CollectionUtils.isEmpty(request.getAuthedShopIdList()) ||
                (strategyStatus != null && !request.getAuthedShopIdList().contains(strategyStatus.getShopId())) ||
                (advertiseStrategyAdGroup != null && !request.getAuthedShopIdList().contains(advertiseStrategyAdGroup.getShopId()))) {
                responseBuilder.setCode(Int32Value.of(Result.ERROR));
                responseBuilder.setMsg("未授权");
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
            if (strategyStatus != null) {
                itemType = strategyStatus.getItemType();
            } else {
                itemType = advertiseStrategyAdGroup.getItemType();
            }
            Result result = null;
            try {
                if (AdItemType.START_STOP.name().equals(itemType) || AdItemType.PORTFOLIO.name().equals(itemType)) {
                    if (AdItemType.PORTFOLIO.name().equals(itemType) && strategyStatus != null && PbUtil.isPortfolioHour(strategyStatus.getChildrenItemType())) {
                        itemType = Constants.PORTFOLIO_HOUR;
                    }
                    result = adStrategyStatusApiFactory.getStrategyStatusService(itemType).updateStrategyStatus(request.getPuid(),
                            request.getStatusId(), request.getStatus(), request.getUpdateId(), request.getLoginIp(), request.getTraceId());
                } else {
                    if ("AD_GROUP_TARGET".equals(request.getAddWayType())) {
                        result = advertiseStrategyGroupTargetBidService.updateStrategyStatus(request.getPuid(),
                                request.getStatusId(), request.getStatus(), request.getUpdateId(), request.getLoginIp(), request.getTraceId());
                    } else {
                        result = advertiseStrategyStatusService.updateStrategyStatus(request.getPuid(),
                                request.getStatusId(), request.getStatus(), request.getUpdateId(), request.getLoginIp(), request.getTraceId());
                    }
                }
            } catch (Exception e) {
                log.error("puid:{}, statusId:{} 分时调价修改状态异常:", request.getPuid(), request.getStatusId(), e);
            }
            responseBuilder.setCode(Int32Value.of(result.getCode()));
            responseBuilder.setMsg(result.getMsg());
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void removeStrategy (AdStrategyStatusRemoveRequest request,StreamObserver<CommonResponse> responseObserver){
        log.info("策略移除 removeStrategy request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        boolean hasError = false;
        if (!request.hasPuid() || CollectionUtils.isEmpty(request.getStrategyStatusList())) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("请求参数错误");
        } else {
            try {
                List<RemoveStrategyVo> removeStrategyVoList = Lists.newArrayList();
                for (StrategyStatus strategyStatus : request.getStrategyStatusList()) {
                    RemoveStrategyVo removeStrategyVo = new RemoveStrategyVo();
                    removeStrategyVo.setTaskId(strategyStatus.getTaskId());
                    removeStrategyVo.setTargetType(strategyStatus.getTargetType());
                    removeStrategyVo.setShopId(strategyStatus.getShopId());
                    removeStrategyVo.setItemType(strategyStatus.getItemType());
                    removeStrategyVo.setStatusId(strategyStatus.getStatusId());
                    removeStrategyVoList.add(removeStrategyVo);
                }
                Vector<Result<String>> resultVector = new Vector<>();
                AdvertiseStrategyStatus strategyStatus = advertiseStrategyStatusDao.getByStatusId(request.getPuid(), request.getStrategyStatusList().get(0).getStatusId());
                if(strategyStatus == null && (AdItemType.START_STOP.name().equals(request.getStrategyStatusList().get(0).getItemType())
                        || AdItemType.PORTFOLIO.name().equals(request.getStrategyStatusList().get(0).getItemType()))) {
                    responseBuilder.setCode(Int32Value.of(Result.ERROR));
                    responseBuilder.setMsg("当前策略已被删除");
                    responseObserver.onNext(responseBuilder.build());
                    responseObserver.onCompleted();
                    return;
                }
                if (removeStrategyVoList.size() > Constants.STRATEGY_PARTITION_SIZE) {
                    List<List<RemoveStrategyVo>> removeStrategyListPartition = Lists.partition(removeStrategyVoList, Constants.STRATEGY_PARTITION_SIZE);
                    ThreadPoolExecutor threadPoolExecutor = ThreadPoolUtil.getTimeSharingStrategyThreadPool();
                    CountDownLatch countDownLatch = new CountDownLatch(removeStrategyListPartition.size());
                    for (List<RemoveStrategyVo> removeStrategyVos : removeStrategyListPartition) {
                        threadPoolExecutor.execute(() -> {
                            try {
                                Result<String> result = null;
                                if (AdItemType.START_STOP.name().equals(request.getStrategyStatusList().get(0).getItemType()) || AdItemType.PORTFOLIO.name().equals(request.getStrategyStatusList().get(0).getItemType())) {
                                    String itemType = strategyStatus.getItemType();
                                    if (AdItemType.PORTFOLIO.name().equals(strategyStatus.getItemType()) && PbUtil.isPortfolioHour(strategyStatus.getChildrenItemType())) {
                                        itemType = Constants.PORTFOLIO_HOUR;
                                    }
                                    result = adStrategyStatusApiFactory.getStrategyStatusService(itemType).removeStrategy(request.getPuid(), removeStrategyVos, request.getUpdateId(), request.getLoginIp(), request.getTraceId());
                                } else {
                                    if ("AD_GROUP_TARGET".equals(request.getAddWayType())) {
                                        result = advertiseStrategyGroupTargetBidService.removeStrategy(request.getPuid(),
                                                removeStrategyVos, request.getUpdateId(), request.getLoginIp(), request.getTraceId());
                                    } else {
                                        result = advertiseStrategyStatusService.removeStrategy(request.getPuid(),
                                                removeStrategyVos, request.getUpdateId(), request.getLoginIp(), request.getTraceId());
                                    }
                                }
                                if (result != null) {
                                    resultVector.add(result);
                                }
                            } catch (Exception e) {
                                log.error("submitStrategyThread request fail:{}, puid:{}, itemType:{}, traceId:{}", e.getMessage(), request.getPuid(), request.getStrategyStatusList().get(0).getItemType(), request.getTraceId());
                            } finally {
                                countDownLatch.countDown();
                            }
                        });
                    }
                    countDownLatch.await();
                } else {
                    Result<String> result = null;
                    if (AdItemType.START_STOP.name().equals(request.getStrategyStatusList().get(0).getItemType()) || AdItemType.PORTFOLIO.name().equals(request.getStrategyStatusList().get(0).getItemType())) {
                        String itemType = strategyStatus.getItemType();
                        if (AdItemType.PORTFOLIO.name().equals(strategyStatus.getItemType()) && PbUtil.isPortfolioHour(strategyStatus.getChildrenItemType())) {
                            itemType = Constants.PORTFOLIO_HOUR;
                        }
                        result = adStrategyStatusApiFactory.getStrategyStatusService(itemType).removeStrategy(request.getPuid(), removeStrategyVoList, request.getUpdateId(), request.getLoginIp(), request.getTraceId());
                    } else {
                        if ("AD_GROUP_TARGET".equals(request.getAddWayType())) {
                            result = advertiseStrategyGroupTargetBidService.removeStrategy(request.getPuid(),
                                    removeStrategyVoList, request.getUpdateId(), request.getLoginIp(), request.getTraceId());
                        } else {
                            result = advertiseStrategyStatusService.removeStrategy(request.getPuid(),
                                    removeStrategyVoList, request.getUpdateId(), request.getLoginIp(), request.getTraceId());
                        }
                    }
                    if (result != null) {
                        resultVector.add(result);
                    }
                }
                if (CollectionUtils.isNotEmpty(resultVector)) {
                    resultVector.forEach(e->{
                        if (Result.ERROR == e.getCode()) {
                            responseBuilder.setCode(Int32Value.of(Result.ERROR));
                            responseBuilder.setMsg(e.getMsg());
                        }
                    });
                } else {
                    responseBuilder.setCode(Int32Value.of(Result.ERROR));
                    responseBuilder.setMsg("分时策略删除失败");
                }
                responseObserver.onNext(responseBuilder.build());
            } catch (Exception e) {
                hasError = true;
                log.error("traceId:{} puid:{} 策略删除异常: ", request.getTraceId(), request.getPuid(), e);
                responseObserver.onError(e);
            }
        }
        if (!hasError) {
            responseObserver.onCompleted();
        }
    }

    @Override
    public void transferStrategy(AdStrategyStatusTransferRequest request,StreamObserver<CommonResponse> responseObserver){
        log.info("策略转移 transferStrategy request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasTemplateId() || CollectionUtils.isEmpty(request.getStatusIdsList())) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("请求参数错误");
        } else {
            Result result = null;
            try {
                AdvertiseStrategyTemplate advertiseStrategyTemplate = advertiseStrategyTemplateDao.selectByPrimaryKey(request.getPuid(), request.getTemplateId());
                if (advertiseStrategyTemplate == null) {
                    responseBuilder.setCode(Int32Value.of(Result.ERROR));
                    responseBuilder.setMsg("当前模板已被删除");
                    responseObserver.onNext(responseBuilder.build());
                    responseObserver.onCompleted();
                    return;
                }
                if (CollectionUtils.isEmpty(request.getAuthedShopIdList()) || !request.getAuthedShopIdList().contains(advertiseStrategyTemplate.getShopId())) {
                    responseBuilder.setCode(Int32Value.of(Result.ERROR));
                    responseBuilder.setMsg("未授权");
                    responseObserver.onNext(responseBuilder.build());
                    responseObserver.onCompleted();
                    return;
                }
                Integer count = advertiseStrategyStatusDao.getCount(request.getPuid(), request.getTemplateId());
                if (count + request.getStatusIdsList().size() > indexStrategyConfig.getAdStrategyMaxSize()) {
                    responseBuilder.setCode(Int32Value.of(Result.ERROR));
                    responseBuilder.setMsg(String.format(Constants.AD_STRATEGY_MAX_COUNT_MSG, advertiseStrategyTemplate.getTemplateName(), count, indexStrategyConfig.getAdStrategyMaxSize()));
                    responseObserver.onNext(responseBuilder.build());
                    responseObserver.onCompleted();
                    return;
                }
                if ("START_STOP".equals(advertiseStrategyTemplate.getItemType()) || "PORTFOLIO".equals(advertiseStrategyTemplate.getItemType())) {
                    result = adStrategyStatusApiFactory.getStrategyStatusService(advertiseStrategyTemplate.getItemType()).transferStrategy(request.getPuid(), request.getTemplateId(),
                            request.getStatusIdsList(), request.getOperation(), request.getLoginIp(), request.getUid(), request.getTraceId());
                } else {
                    if ("AD_GROUP_TARGET".equals(request.getAddWayType())) {
                        result = advertiseStrategyGroupTargetBidService.transferStrategy(request.getPuid(), request.getUid(), request.getTemplateId(),
                                request.getStatusIdsList(), request.getOperation(), request.getLoginIp(), request.getTraceId());
                    } else {
                        result = advertiseStrategyStatusService.transferStrategy(request.getPuid(), request.getUid(), request.getTemplateId(),
                                request.getStatusIdsList(), request.getOperation(), request.getLoginIp(), request.getTraceId());
                    }
                }
            } catch (Exception e) {
                log.error("puid:{}, templateId:{} 分时调价修改状态异常:", request.getPuid(), request.getTemplateId(), e);
            }
            responseBuilder.setCode(Int32Value.of(result.getCode()));
            responseBuilder.setMsg(result.getMsg());
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void queryAdStartStopPreview(AdStartStopPreviewAddRequest request, StreamObserver<AdStartStopPreviewAddResponse> responseObserver) {
        log.info("查询分时调启停预览 AdStartStopPreviewAddRequest request:{}", request);
        AdStartStopPreviewAddResponse.Builder responseBuilder = AdStartStopPreviewAddResponse.newBuilder();
        if (!request.hasPuid() || !request.hasTemplateId()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            List<SubmitStrategyVo> submitStrategyVos = Lists.newArrayList();
            List<PreviewStrategy> paramList = request.getPreviewStrategyList();
            for (PreviewStrategy previewStrategy : paramList) {
                SubmitStrategyVo submitStrategyVo = new SubmitStrategyVo();
                submitStrategyVo.setAdType(previewStrategy.getAdType());
                submitStrategyVo.setItemId(previewStrategy.getItemId());
                submitStrategyVo.setItemName(previewStrategy.getCampaignName());
                submitStrategyVo.setOriginState(previewStrategy.getOriginState());
                submitStrategyVo.setAsin(previewStrategy.getAsin());
                submitStrategyVo.setSku(previewStrategy.getMsku());
                submitStrategyVos.add(submitStrategyVo);
            }
            Result<List<StartStopPreviewVo>> result = advertiseStrategyStatusService.
                    queryAdStartStopPreview(request.getPuid(), submitStrategyVos, request.getTemplateId(), request.getTraceId());
            if (result.getCode() == Result.SUCCESS) {
                List<StartStopPreviewVo> startStopPreviewVoList = result.getData();
                if (CollectionUtils.isNotEmpty(startStopPreviewVoList)) {
                    List<StartStopPreview> previewList = Lists.newArrayList();
                    for (StartStopPreviewVo vo : startStopPreviewVoList) {
                        StartStopPreview.Builder builder = StartStopPreview.newBuilder();
                        builder.setPuid(vo.getPuid());
                        builder.setCampaignId(vo.getCampaignId());
                        builder.setCampaignName(vo.getCampaignName());
                        builder.setOriginState(vo.getOriginState());
                        builder.setType(vo.getType());
                        builder.setAdType(vo.getAdType());
                        builder.setAsin(vo.getAsin());
                        builder.setMsku(vo.getMsku());
                        if (CollectionUtils.isNotEmpty(vo.getPeriodStartStopVoList())) {
                            Map<Integer,List<PeriodStartStopVo>> map = vo.getPeriodStartStopVoList().
                                    stream().collect(Collectors.groupingBy(PeriodStartStopVo::getDay));
                            List<PeriodStartStop> periodStartStopList = Lists.newArrayList();
                            for (Integer day : map.keySet()) {
                                PeriodStartStop.Builder pcBuilder = PeriodStartStop.newBuilder();
                                pcBuilder.setDay(day);
                                if (day == 0) {
                                    pcBuilder.setDayName("每天");
                                } else {
                                    pcBuilder.setDayName(com.meiyunji.sponsored.service.cpc.util.
                                            Constants.getDateMap().get(day));
                                }
                                for (PeriodStartStopVo periodStartStopVo:map.get(day)) {
                                    TimeStartStop.Builder time = TimeStartStop.newBuilder();
                                    time.setStart(periodStartStopVo.getStart());
                                    time.setEnd(periodStartStopVo.getEnd());
                                    time.setNewState(periodStartStopVo.getNewState());
                                    pcBuilder.addTimeStartStops(time.build());
                                }
                                periodStartStopList.add(pcBuilder.build());
                            }
                            builder.addAllPeriodStartStops(periodStartStopList);
                        }
                        previewList.add(builder.build());
                    }
                    responseBuilder.setCode(result.getCode());
                    responseBuilder.addAllData(previewList);
                }
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void queryAdCampaignPreview(AdCampaignPreviewAddRequest request, StreamObserver<AdCampaignPreviewAddResponse> responseObserver) {
        log.info("查询分时调预算预览 queryAdCampaignPreview request:{}", request);
        AdCampaignPreviewAddResponse.Builder responseBuilder = AdCampaignPreviewAddResponse.newBuilder();
        if (!request.hasPuid() || !request.hasTemplateId()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            List<SubmitStrategyVo> submitStrategyVos = Lists.newArrayList();
            List<PreviewStrategy> paramList = request.getPreviewStrategyList();
            for (PreviewStrategy previewStrategy : paramList) {
                SubmitStrategyVo submitStrategyVo = new SubmitStrategyVo();
                submitStrategyVo.setAdType(previewStrategy.getAdType());
                submitStrategyVo.setItemId(previewStrategy.getItemId());
                submitStrategyVo.setItemName(previewStrategy.getCampaignName());
                submitStrategyVo.setOriginBudgetValue(BigDecimal.valueOf(previewStrategy.getOriginBudgetValue()));
                submitStrategyVo.setCurrency(previewStrategy.getCurrency());
                submitStrategyVos.add(submitStrategyVo);
            }
            Result<List<CampaignPreviewVo>> result = advertiseStrategyStatusService.
                    queryAdCampaignPreview(request.getPuid(), submitStrategyVos, request.getTemplateId(), request.getTraceId());
            if (result.getCode() == Result.SUCCESS) {
                List<CampaignPreviewVo> campaignPreviewVoList = result.getData();
                if (CollectionUtils.isNotEmpty(campaignPreviewVoList)) {
                    List<CampaignPreview> previewList = Lists.newArrayList();
                    for (CampaignPreviewVo vo : campaignPreviewVoList) {
                        CampaignPreview.Builder builder = CampaignPreview.newBuilder();
                        builder.setPuid(vo.getPuid());
                        builder.setCampaignId(vo.getCampaignId());
                        builder.setCampaignName(vo.getCampaignName());
                        builder.setOriginalBudget(vo.getOriginalBudget());
                        builder.setType(vo.getType());
                        builder.setAdType(vo.getAdType());
                        builder.setCurrency(vo.getCurrency());
                        if (CollectionUtils.isNotEmpty(vo.getPeriodCampaignList())) {
                            Map<Integer,List<PeriodCampaignVo>> map = vo.getPeriodCampaignList().
                                    stream().collect(Collectors.groupingBy(PeriodCampaignVo::getDay));
                            List<PeriodCampaign> periodCampaignList = Lists.newArrayList();
                            for (Integer day : map.keySet()) {
                                PeriodCampaign.Builder pcBuilder = PeriodCampaign.newBuilder();
                                pcBuilder.setDay(day);
                                if (day == 0) {
                                    pcBuilder.setDayName("每天");
                                } else {
                                    pcBuilder.setDayName(com.meiyunji.sponsored.service.cpc.util.
                                            Constants.getDateMap().get(day));
                                }
                                for (PeriodCampaignVo periodCampaignVo:map.get(day)) {
                                    TimeCampaign.Builder time = TimeCampaign.newBuilder();
                                    time.setStart(periodCampaignVo.getStart());
                                    time.setEnd(periodCampaignVo.getEnd());
                                    time.setNewBudget(periodCampaignVo.getNewBudget());
                                    time.setCurrency(periodCampaignVo.getCurrency());
                                    pcBuilder.addTimeCampaigns(time.build());
                                }
                                periodCampaignList.add(pcBuilder.build());
                            }
                            builder.addAllPeriodCampaign(periodCampaignList);
                        }
                        previewList.add(builder.build());
                    }
                    responseBuilder.setCode(result.getCode());
                    responseBuilder.addAllData(previewList);
                }
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void queryAdCampaignSpacePreview(AdCampaignSpacePreviewAddRequest request, StreamObserver<AdCampaignSpacePreviewAddResponse> responseObserver) {
        log.info("查询分时调广告位预览 queryAdCampaignSpacePreview request:{}", request);
        AdCampaignSpacePreviewAddResponse.Builder responseBuilder = AdCampaignSpacePreviewAddResponse.newBuilder();
        if (!request.hasPuid() || !request.hasTemplateId()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            List<SubmitStrategyVo> submitStrategyVos = Lists.newArrayList();
            List<PreviewStrategy> paramList = request.getPreviewStrategyList();
            for (PreviewStrategy previewStrategy : paramList) {
                SubmitStrategyVo submitStrategyVo = new SubmitStrategyVo();
                submitStrategyVo.setAdType(previewStrategy.getAdType());
                submitStrategyVo.setItemId(previewStrategy.getItemId());
                submitStrategyVo.setItemName(previewStrategy.getCampaignName());
                submitStrategyVo.setOriginStrategy(previewStrategy.getOriginStrategy());
                submitStrategyVo.setOriginAdPlaceProductValue(BigDecimal.valueOf(previewStrategy.getOriginAdPlaceProductValue()));
                submitStrategyVo.setOriginAdPlaceTopValue(BigDecimal.valueOf(previewStrategy.getOriginAdPlaceTopValue()));
                submitStrategyVo.setOriginAdOtherValue(BigDecimal.valueOf(previewStrategy.getAdOtherValue()));
                submitStrategyVos.add(submitStrategyVo);
            }
            Result<List<CampaignSpacePreviewVo>> result = advertiseStrategyStatusService.
                    queryAdCampaignSpacePreview(request.getPuid(), submitStrategyVos, request.getTemplateId(), request.getTraceId());
            if (result.getCode() == Result.SUCCESS) {
                List<CampaignSpacePreviewVo> campaignSpacePreviewVoList = result.getData();
                if (CollectionUtils.isNotEmpty(campaignSpacePreviewVoList)) {
                    List<CampaignSpacePreview> previewList = Lists.newArrayListWithExpectedSize(campaignSpacePreviewVoList.size());
                    for (CampaignSpacePreviewVo vo : campaignSpacePreviewVoList) {
                        CampaignSpacePreview.Builder builder = CampaignSpacePreview.newBuilder();
                        List<PeriodCampaignSpace> periodCampaignSpaceList = Lists.newArrayList();
                        builder.setPuid(vo.getPuid());
                        builder.setCampaignId(vo.getCampaignId());
                        builder.setCampaignName(vo.getCampaignName());
                        builder.setOriginalPlaceProductValue(vo.getOriginalAdPlaceProductValue());
                        builder.setOriginalPlaceTopValue(vo.getOriginalAdPlaceTopValue());
                        builder.setOriginalStrategy(vo.getOriginalStrategy());
                        if (StringUtils.isNotBlank(vo.getOriginalAdOtherValue())) {
                            builder.setAdOtherValue(vo.getOriginalAdOtherValue());
                        } else {
                            builder.setAdOtherValue("0");
                        }
                        builder.setType(vo.getType());
                        builder.setAdType(vo.getAdType());
                        Map<Integer,List<PeriodCampaignSpaceVo>> map = vo.getPeriodCampaignSpaceVoList().
                                stream().collect(Collectors.groupingBy(PeriodCampaignSpaceVo::getDay));
                        for (Integer day : map.keySet()) {
                            PeriodCampaignSpace.Builder pcsBuilder = PeriodCampaignSpace.newBuilder();
                            pcsBuilder.setDay(day);
                            if (day == 0) {
                                pcsBuilder.setDayName("每天");
                            } else {
                                pcsBuilder.setDayName(com.meiyunji.sponsored.service.cpc.util.
                                        Constants.getDateMap().get(day));
                            }
                            for (PeriodCampaignSpaceVo periodCampaignSpaceVo : map.get(day)) {
                                TimeCampaignSpace.Builder time = TimeCampaignSpace.newBuilder();
                                time.setStart(periodCampaignSpaceVo.getStart());
                                time.setEnd(periodCampaignSpaceVo.getEnd());
                                if (periodCampaignSpaceVo.getNewAdPlaceProductValue() != null) {
                                    time.setNewPlaceProductValue(periodCampaignSpaceVo.getNewAdPlaceProductValue());
                                }
                                if (periodCampaignSpaceVo.getNewAdPlaceTopValue() != null) {
                                    time.setNewPlaceTopValue(periodCampaignSpaceVo.getNewAdPlaceTopValue());
                                }
                                if (periodCampaignSpaceVo.getNewStrategy() != null) {
                                    time.setNewStrategy(periodCampaignSpaceVo.getNewStrategy());
                                }
                                if (periodCampaignSpaceVo.getNewAdOtherValue() != null) {
                                    time.setNewAdOtherValue(periodCampaignSpaceVo.getNewAdOtherValue());
                                } else {
                                    time.setNewAdOtherValue("0");
                                }
                                pcsBuilder.addTimeCampaignSpace(time.build());
                            }
                            periodCampaignSpaceList.add(pcsBuilder.build());
                        }
                        builder.addAllPeriodCampaignSpaces(periodCampaignSpaceList);
                        previewList.add(builder.build());
                    }
                    responseBuilder.setCode(result.getCode());
                    responseBuilder.addAllData(previewList);
                }
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void queryAdTargetPreview(AdTargetPreviewAddRequest request, StreamObserver<AdTargetPreviewAddResponse> responseObserver) {
        log.info("查询分时调竞价预览 queryAdTargetPreview request:{}", request);
        AdTargetPreviewAddResponse.Builder responseBuilder = AdTargetPreviewAddResponse.newBuilder();
        if (!request.hasPuid() || !request.hasTemplateId()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            List<SubmitStrategyVo> submitStrategyVos = Lists.newArrayList();
            List<PreviewStrategy> paramList = request.getPreviewStrategyList();
            for (PreviewStrategy previewStrategy : paramList) {
                SubmitStrategyVo submitStrategyVo = new SubmitStrategyVo();
                submitStrategyVo.setAdType(previewStrategy.getAdType());
                submitStrategyVo.setItemId(previewStrategy.getItemId());
                submitStrategyVo.setItemName(previewStrategy.getTargetName());
                submitStrategyVo.setOriginBiddingValue(BigDecimal.valueOf(previewStrategy.getOriginBiddingValue()));
                submitStrategyVo.setCurrency(previewStrategy.getCurrency());
                submitStrategyVo.setAdGroupName(previewStrategy.getGroupName());
                submitStrategyVo.setCampaignName(previewStrategy.getCampaignName());
                submitStrategyVo.setMatchName(previewStrategy.getMatchName());
                submitStrategyVos.add(submitStrategyVo);
            }
            Result<List<TargetPreviewVo>> result = advertiseStrategyStatusService.
                    queryAdTargetPreview(request.getPuid(), submitStrategyVos, request.getTemplateId(), request.getTraceId());
            if (result.getCode() == Result.SUCCESS) {
                List<TargetPreviewVo> targetPreviewVoList = result.getData();
                if (CollectionUtils.isNotEmpty(targetPreviewVoList)) {
                    List<TargetPreview> list = Lists.newArrayListWithExpectedSize(targetPreviewVoList.size());
                    for (TargetPreviewVo vo : targetPreviewVoList) {
                        TargetPreview.Builder builder = TargetPreview.newBuilder();
                        List<PeriodTarget> periodTargetList = Lists.newArrayList();
                        builder.setPuid(vo.getPuid());
                        builder.setTargetId(vo.getItemId());
                        builder.setTargetName(vo.getItemName());
                        builder.setCampaignName(vo.getCampaignName());
                        builder.setOriginalBidValue(vo.getOriginalBiddingValue());
                        builder.setType(vo.getType());
                        builder.setCurrency(vo.getCurrency());
                        builder.setAdType(vo.getAdType());
                        if (StringUtils.isNotBlank(vo.getMatchName())) {
                            builder.setMatchName(vo.getMatchName());
                        }
                        if (StringUtils.isNotBlank(vo.getAdGroupName())) {
                            builder.setGroupName(vo.getAdGroupName());
                        }
                        Map<Integer,List<PeriodTargetVo>> map = vo.getPeriodTargetVoList().
                                stream().collect(Collectors.groupingBy(PeriodTargetVo::getDay));
                        for (Integer day : map.keySet()) {
                            PeriodTarget.Builder ptBuilder = PeriodTarget.newBuilder();
                            ptBuilder.setDay(day);
                            if (day == 0) {
                                ptBuilder.setDayName("每天");
                            } else {
                                ptBuilder.setDayName(com.meiyunji.sponsored.service.cpc.util.
                                        Constants.getDateMap().get(day));
                            }
                            for (PeriodTargetVo periodTargetVo:map.get(day)) {
                                TimeTarget.Builder time = TimeTarget.newBuilder();
                                time.setStart(periodTargetVo.getStart());
                                time.setEnd(periodTargetVo.getEnd());
                                time.setNewBidValue(periodTargetVo.getNewBiddingValue());
                                time.setCurrency(periodTargetVo.getCurrency());
                                ptBuilder.addTimeTargets(time.build());
                            }
                            periodTargetList.add(ptBuilder.build());
                        }
                        builder.addAllPeriodTarget(periodTargetList);
                        list.add(builder.build());
                    }
                    responseBuilder.setCode(result.getCode());
                    responseBuilder.addAllData(list);
                }
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void executeCampaignPreview(ExecutePreviewRequest request, StreamObserver<ExecuteCampaignPreviewResponse> responseObserver) {
        log.info("预算执行预览 executeCampaignPreview request:{}", request);
        ExecuteCampaignPreviewResponse.Builder responseBuilder = ExecuteCampaignPreviewResponse.newBuilder();
        if (!request.hasPuid() || !request.hasTemplateId()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            ExecuteParam param = new ExecuteParam();
            param.setPuid(request.getPuid());
            param.setTemplateId(request.getTemplateId());
            param.setPageNo(request.getPageNo());
            param.setPageSize(request.getPageSize());
            param.setAdTypeList(request.getAdTypeList());
            param.setItemType(request.getItemType());
            param.setCampaignIdList(request.getCampaignIdList());
            param.setTraceId(request.getTraceId());
            Result<Page<ExecuteCampaignVo>> result = advertiseStrategyStatusService.executeCampaignPreview(param);
            ExecuteCampaignPage.Builder pageBuilder = ExecuteCampaignPage.newBuilder();
            if (result.getCode() == Result.SUCCESS) {
                Page<ExecuteCampaignVo> voPage = result.getData();
                pageBuilder.setPageNo(voPage.getPageNo());
                pageBuilder.setPageSize(voPage.getPageSize());
                pageBuilder.setTotalPage(voPage.getTotalPage());
                pageBuilder.setTotalSize(voPage.getTotalSize());
                if (CollectionUtils.isNotEmpty(voPage.getRows())) {
                    List<ExecuteCampaignVo> executeCampaignVos = voPage.getRows();
                    List<ExecuteCampaignRpc> executeCampaignRpcList = Lists.newArrayListWithExpectedSize(executeCampaignVos.size());
                    for (ExecuteCampaignVo executeCampaignVo : executeCampaignVos) {
                        for (PeriodCampaignVo periodCampaignVo: executeCampaignVo.getPeriodCampaignVoList()) {
                            ExecuteCampaignRpc.Builder builder = ExecuteCampaignRpc.newBuilder();
                            builder.setPuid(executeCampaignVo.getPuid());
                            builder.setShopId(executeCampaignVo.getShopId());
                            builder.setType(executeCampaignVo.getType());
                            if (StringUtils.isNotBlank(executeCampaignVo.getCampaignName())) {
                                builder.setCampaignName(executeCampaignVo.getCampaignName());
                            }
                            if (StringUtils.isNotBlank(executeCampaignVo.getCampaignId())) {
                                builder.setCampaignId(executeCampaignVo.getCampaignId());
                            }
                            builder.setNewBudget(periodCampaignVo.getNewBudget());
                            builder.setBeijingDateTime(periodCampaignVo.getBeijingDateTime());
                            builder.setSiteDateTime(periodCampaignVo.getSiteDateTime());
                            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(executeCampaignVo.getMarketplaceId());
                            if( null != m){
                                builder.setCurrency(m.getCurrencyCode());
                            }
                            executeCampaignRpcList.add(builder.build());
                        }
                    }
                    pageBuilder.addAllRows(executeCampaignRpcList);
                }
                responseBuilder.setCode(result.getCode());
                responseBuilder.setData(pageBuilder.build());
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void executeCampaignSpacePreview(ExecutePreviewRequest request, StreamObserver<ExecuteCampaignSpacePreviewResponse> responseObserver) {
        log.info("广告位执行预览 executeCampaignSpacePreview request:{}", request);
        ExecuteCampaignSpacePreviewResponse.Builder responseBuilder = ExecuteCampaignSpacePreviewResponse.newBuilder();
        if (!request.hasPuid() || !request.hasTemplateId()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            ExecuteParam param = new ExecuteParam();
            param.setPuid(request.getPuid());
            param.setTemplateId(request.getTemplateId());
            param.setItemType("CAMPAIGN_PLACEMENT");
            param.setPageNo(request.getPageNo());
            param.setPageSize(request.getPageSize());
            param.setAdTypeList(request.getAdTypeList());
            param.setCampaignIdList(request.getCampaignIdList());
            param.setTraceId(request.getTraceId());
            Result<Page<ExecuteCampaignSpaceVo>> result = advertiseStrategyStatusService.executeCampaignSpacePreview(param);
            ExecuteCampaignSpacePage.Builder pageBuilder = ExecuteCampaignSpacePage.newBuilder();
            if (result.getCode() == Result.SUCCESS) {
                Page<ExecuteCampaignSpaceVo> voPage = result.getData();
                pageBuilder.setPageNo(voPage.getPageNo());
                pageBuilder.setPageSize(voPage.getPageSize());
                pageBuilder.setTotalPage(voPage.getTotalPage());
                pageBuilder.setTotalSize(voPage.getTotalSize());
                if (CollectionUtils.isNotEmpty(voPage.getRows())) {
                    List<ExecuteCampaignSpaceVo> executeCampaignSpaceVos = voPage.getRows();
                    List<ExecuteCampaignSpaceRpc> executeCampaignSpaceRpcList = Lists.newArrayListWithExpectedSize(executeCampaignSpaceVos.size());
                    for (ExecuteCampaignSpaceVo executeCampaignSpaceVo : executeCampaignSpaceVos) {
                        for (PeriodCampaignSpaceVo periodCampaignSpaceVo: executeCampaignSpaceVo.getPeriodCampaignSpaceVoList()) {
                            ExecuteCampaignSpaceRpc.Builder builder = ExecuteCampaignSpaceRpc.newBuilder();
                            builder.setPuid(executeCampaignSpaceVo.getPuid());
                            builder.setShopId(executeCampaignSpaceVo.getShopId());
                            builder.setType(executeCampaignSpaceVo.getType());
                            if (StringUtils.isNotBlank(executeCampaignSpaceVo.getCampaignName())) {
                                builder.setCampaignName(executeCampaignSpaceVo.getCampaignName());
                            }
                            if (StringUtils.isNotBlank(executeCampaignSpaceVo.getCampaignId())) {
                                builder.setCampaignId(executeCampaignSpaceVo.getCampaignId());
                            }
                            builder.setBeijingDateTime(periodCampaignSpaceVo.getBeijingDateTime());
                            builder.setSiteDateTime(periodCampaignSpaceVo.getSiteDateTime());
                            if (StringUtils.isNotBlank(periodCampaignSpaceVo.getNewStrategy())) {
                                builder.setNewStrategy(periodCampaignSpaceVo.getNewStrategy());
                            }
                            if (periodCampaignSpaceVo.getNewAdPlaceTopValue() != null) {
                                builder.setNewPlaceTopValue(periodCampaignSpaceVo.getNewAdPlaceTopValue());
                            }
                            if (periodCampaignSpaceVo.getNewAdPlaceProductValue() != null) {
                                builder.setNewPlaceProductValue(periodCampaignSpaceVo.getNewAdPlaceProductValue());
                            }
                            if (periodCampaignSpaceVo.getNewAdOtherValue() != null) {
                                builder.setNewAdOtherValue(periodCampaignSpaceVo.getNewAdOtherValue());
                            }
                            executeCampaignSpaceRpcList.add(builder.build());
                        }
                    }
                    pageBuilder.addAllRows(executeCampaignSpaceRpcList);
                }
                responseBuilder.setCode(result.getCode());
                responseBuilder.setData(pageBuilder.build());
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void executeTargetPreview(ExecutePreviewRequest request, StreamObserver<ExecuteTargetPreviewResponse> responseObserver) {
        log.info("竞价执行预览 executeCampaignPreview request:{}", request);
        ExecuteTargetPreviewResponse.Builder responseBuilder = ExecuteTargetPreviewResponse.newBuilder();
        if (!request.hasPuid() || !request.hasTemplateId()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            ExecuteParam param = new ExecuteParam();
            param.setPuid(request.getPuid());
            param.setTemplateId(request.getTemplateId());
            param.setItemType(request.getItemType());
            param.setPageNo(request.getPageNo());
            param.setPageSize(request.getPageSize());
            param.setCampaignIdList(request.getCampaignIdList());
            param.setAdGroupIdList(request.getAdGroupIdList());
            param.setMatchType(request.getMatchType());
            param.setTraceId(request.getTraceId());
            //兼容前端传值的问题
            if (CollectionUtils.isNotEmpty(request.getTargetTypeList())) {
                List<String> targetTypeList = Lists.newArrayList("autoTarget","keywordTarget"
                        ,"productTarget","audienceTarget");
                if (StringUtils.isBlank(request.getTargetTypeList().get(0))) {
                    param.setTargetTypeList(targetTypeList);
                } else {
                    if (request.getTargetTypeList().get(0).equals("all")) {
                        param.setTargetTypeList(targetTypeList);
                    } else {
                        param.setTargetTypeList(request.getTargetTypeList());
                    }
                }
            } else {
                param.setTargetTypeList(request.getTargetTypeList());
            }
            param.setTargetName(request.getTargetName());
            Result<List<ExecuteTargetVo>> result = advertiseStrategyStatusService.executeTargetPreview(param);
            ExecuteTargetPage.Builder pageBuilder = ExecuteTargetPage.newBuilder();
            if (result.getCode() == Result.SUCCESS) {
                List<ExecuteTargetVo> executeTargetVoList = result.getData();
                pageBuilder.setPageNo(request.getPageNo());
                pageBuilder.setPageSize(request.getPageSize());
                if (CollectionUtils.isNotEmpty(executeTargetVoList)) {
                    List<ExecuteTargetRpc> executeTargetRpcList = Lists.newArrayListWithExpectedSize(executeTargetVoList.size());
                    for (ExecuteTargetVo executeTargetVo : executeTargetVoList) {
                        for (PeriodTargetVo periodTargetVo: executeTargetVo.getPeriodTargetVoList()) {
                            ExecuteTargetRpc.Builder builder = ExecuteTargetRpc.newBuilder();
                            builder.setPuid(executeTargetVo.getPuid());
                            builder.setShopId(executeTargetVo.getShopId());
                            builder.setType(executeTargetVo.getType());
                            if (StringUtils.isNotBlank(executeTargetVo.getCampaignName())) {
                                builder.setCampaignName(executeTargetVo.getCampaignName());
                            }
                            if (StringUtils.isNotBlank(executeTargetVo.getCampaignId())) {
                                builder.setCampaignId(executeTargetVo.getCampaignId());
                            }
                            if (StringUtils.isNotBlank(executeTargetVo.getTargetName())) {
                                String targetName = AutoTargetTypeEnum.getAutoTargetValue(executeTargetVo.getTargetName());
                                if (StringUtils.isNotBlank(targetName)) {
                                    builder.setTargetName(targetName);
                                } else {
                                    builder.setTargetName(executeTargetVo.getTargetName());
                                }
                            }
                            if (StringUtils.isNotBlank(executeTargetVo.getAdGroupId())) {
                                builder.setAdGroupId(executeTargetVo.getAdGroupId());
                            }
                            if (StringUtils.isNotBlank(executeTargetVo.getAdGroupName())) {
                                builder.setAdGroupName(executeTargetVo.getAdGroupName());
                            }
                            if (StringUtils.isNotBlank(executeTargetVo.getTargetType())) {
                                builder.setTargetType(executeTargetVo.getTargetType());
                            }
                            if (StringUtils.isNotBlank(executeTargetVo.getMatchType())) {
                                builder.setMatchType(executeTargetVo.getMatchType());
                            }
                            if (StringUtils.isNotBlank(executeTargetVo.getMatchName())) {
                                builder.setMatchName(executeTargetVo.getMatchName());
                            }
                            builder.setBeijingDateTime(periodTargetVo.getBeijingDateTime());
                            builder.setSiteDateTime(periodTargetVo.getSiteDateTime());
                            builder.setNewBidValue(periodTargetVo.getNewBiddingValue());
                            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(executeTargetVo.getMarketplaceId());
                            if( null != m){
                                builder.setCurrency(m.getCurrencyCode());
                            }
                            executeTargetRpcList.add(builder.build());
                        }
                    }
                    //内存分页
                    if (CollectionUtils.isNotEmpty(executeTargetRpcList)) {
                        Page<ExecuteTargetRpc> voPage = new Page<>();
                        voPage.setPageNo(request.getPageNo());
                        voPage.setPageSize(request.getPageSize());
                        voPage = PageUtil.getPage(voPage,executeTargetRpcList);
                        pageBuilder.setTotalPage(voPage.getTotalPage());
                        pageBuilder.setTotalSize(voPage.getTotalSize());
                        executeTargetRpcList = voPage.getRows();
                    }
                    pageBuilder.addAllRows(executeTargetRpcList);
                }
                responseBuilder.setCode(result.getCode());
                responseBuilder.setData(pageBuilder.build());
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void independentPageQuery(IndependentPageQueryRequest request, StreamObserver<IndependentPageQueryResponse> responseObserver) {
        log.info("独立页分时调价查询 independentPageQuery request:{}", request);
        IndependentPageQueryResponse.Builder responseBuilder = IndependentPageQueryResponse.newBuilder();
        String itemType = request.getItemType();

        String marketplaceId = "";
        // 处理广告组合分时小时级数据
        if (AdItemType.PORTFOLIO.name().equals(itemType)) {
            AdvertiseStrategyStatus advertiseStrategyStatus = advertiseStrategyStatusDao
                    .getObjectByPuidAndShopId(request.getPuid(), request.getShopId(), request.getItemId(), AdItemType.PORTFOLIO.name());
            if (advertiseStrategyStatus != null) {
                if (PbUtil.isPortfolioHour(advertiseStrategyStatus.getChildrenItemType())) {
                    itemType = Constants.PORTFOLIO_HOUR;
                    marketplaceId = advertiseStrategyStatus.getMarketplaceId();
                }
            }
        }

        Result<IndependentCampaignStateVo> result = adStrategyStatusApiFactory.getStrategyStatusService(itemType).
                independentPageStrategy(request.getItemId(), request.getPuid(), request.getShopId(), request.getTraceId());
        if (result.getCode() == Result.SUCCESS) {
            responseBuilder.setCode(result.getCode());
            IndependentPageQuery.Builder builder = IndependentPageQuery.newBuilder();
            if (result.getData() != null) {
                IndependentCampaignStateVo independentCampaignStateVo = result.getData();
                builder.setId(independentCampaignStateVo.getId());
                builder.setPuid(independentCampaignStateVo.getPuid());
                builder.setShopId(independentCampaignStateVo.getShopId());
                builder.setMarketplaceId(independentCampaignStateVo.getMarketplaceId());
                builder.setStatus(independentCampaignStateVo.getStatus());
                builder.setTemplateId(independentCampaignStateVo.getTemplateId());
                builder.setTemplateName(independentCampaignStateVo.getTemplateName());
                builder.setUpdateStatus(independentCampaignStateVo.getUpdateStatus());
                if (StringUtils.isNotBlank(independentCampaignStateVo.getOriginState())) {
                    builder.setOriginState(independentCampaignStateVo.getOriginState());
                }
                builder.setTaskId(independentCampaignStateVo.getTaskId());
                builder.setType(independentCampaignStateVo.getType());
                if (StringUtils.isNotBlank(independentCampaignStateVo.getStartStopItemType())) {
                    builder.setStartStopType(independentCampaignStateVo.getStartStopItemType());
                }
                if (AdItemType.START_STOP.name().equals(independentCampaignStateVo.getItemType())) {
                    Map<Integer, List<StartStopRuleVo>> map = independentCampaignStateVo.getStartStopRuleVoList().stream().
                            collect(Collectors.groupingBy(StartStopRuleVo::getSiteDate));
                    for (Integer day : map.keySet()) {
                        StartStopStateRule.Builder rule = StartStopStateRule.newBuilder();
                        rule.setSiteDate(day);
                        if (day == 0) {
                            rule.setSiteDateName("每天");
                        } else {
                            rule.setSiteDateName(com.meiyunji.sponsored.service.
                                    cpc.util.Constants.getDateMap().get(day));
                        }
                        for (StartStopRuleVo startStopRuleVo : map.get(day)) {
                            StateRule.Builder stateRule = StateRule.newBuilder();
                            stateRule.setState(startStopRuleVo.getState());
                            stateRule.setStartTimeSite(startStopRuleVo.getStartTimeSite());
                            stateRule.setEndTimeSite(startStopRuleVo.getEndTimeSite());
                            rule.addStateRules(stateRule.build());
                        }
                        builder.addStartStopStateRuleList(rule.build());
                    }
                } else if (AdItemType.PORTFOLIO.name().equals(independentCampaignStateVo.getItemType())) {
                    if (Constants.PORTFOLIO_HOUR.equals(itemType)) {
                        builder.setReturnValue(PbUtil.buildPortfolioByJson(independentCampaignStateVo.getReturnValue()));
                        builder.setChildrenItemType(StringUtil.toStringSafe(independentCampaignStateVo.getChildrenItemType()));
                        builder.addAllPortfolioHourRuleList(PbUtil.buildPortfolioHourRuleByJson(JSONUtil.objectToJson(independentCampaignStateVo.getPortfolioHourRuleVoList()), marketplaceId));
                    } else {
                        OriginValueVo valueVo = JSONUtil.jsonToObject(independentCampaignStateVo.getReturnValue(),OriginValueVo.class);
                        Portfolio.Builder returnBuilder = Portfolio.newBuilder();
                        returnBuilder.setPolicy(valueVo.getPolicy());
                        if (!"noBudget".equals(valueVo.getPolicy())) {
                            returnBuilder.setAmount(valueVo.getAmount().doubleValue());
                        }
                        builder.setReturnValue(returnBuilder.build());
                        List<PortfolioRuleVo> portfolioRuleList = independentCampaignStateVo.getPortfolioRuleVoList();
                        List<PortfolioRule> portfolioRules = com.google.common.collect.Lists.newArrayList();
                        Map<Integer,List<PortfolioRuleVo>> map = new LinkedHashMap<>();
                        Map<Integer,List<PortfolioRuleVo>> listMap = new LinkedHashMap<>();
                        if ("MONTH_DAY".equals(independentCampaignStateVo.getType())) {
                            portfolioRuleList.stream().collect(Collectors.groupingBy(e->
                                            Integer.valueOf(LocalDate.parse(e.getStartDate(),DateTimeFormatter.ofPattern(DateUtil.PATTERN)).
                                                    format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD))))).entrySet().stream()
                                    .sorted(Map.Entry.comparingByKey()).forEachOrdered(x->listMap.put(x.getKey(),x.getValue()));
                        } else {
                            map = portfolioRuleList.stream().collect(Collectors.groupingBy(PortfolioRuleVo::getSiteDate));
                        }
                        if (MapUtils.isNotEmpty(map)) {
                            for (Integer siteDate : map.keySet()) {
                                PortfolioRule.Builder portfolioBuilder = PortfolioRule.newBuilder();
                                portfolioBuilder.setSiteDate(siteDate);
                                if (siteDate == 0) {
                                    portfolioBuilder.setSiteDateName("每日");
                                } else {
                                    portfolioBuilder.setSiteDateName(com.meiyunji.sponsored.service.
                                            cpc.util.Constants.getDateMap().get(siteDate));
                                }
                                for (PortfolioRuleVo portfolioRuleVo : map.get(siteDate)) {
                                    Portfolio.Builder portfolio = Portfolio.newBuilder();
                                    if (!"noBudget".equals(portfolioRuleVo.getAmount())) {
                                        portfolio.setAmount(portfolioRuleVo.getAmount().doubleValue());
                                    }
                                    portfolio.setPolicy(portfolioRuleVo.getPolicy());
                                    MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(independentCampaignStateVo.getMarketplaceId());
                                    if(null != m){
                                        portfolio.setSymbol(m.getCurrencyCode());
                                    }
                                    portfolioBuilder.addPortfolios(portfolio.build());
                                }
                                portfolioRules.add(portfolioBuilder.build());
                            }
                        }
                        if (MapUtils.isNotEmpty(listMap)) {
                            for (Integer countDate : listMap.keySet()) {
                                PortfolioRule.Builder portfolioBuilder = PortfolioRule.newBuilder();
                                portfolioBuilder.setStartDate(listMap.get(countDate).get(0).getStartDate());
                                portfolioBuilder.setEndDate(listMap.get(countDate).get(0).getEndDate());
                                for (PortfolioRuleVo portfolioRuleVo : listMap.get(countDate)) {
                                    Portfolio.Builder portfolio = Portfolio.newBuilder();
                                    if (!"noBudget".equals(portfolioRuleVo.getAmount())) {
                                        portfolio.setAmount(portfolioRuleVo.getAmount().doubleValue());
                                    }
                                    portfolio.setPolicy(portfolioRuleVo.getPolicy());
                                    MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(independentCampaignStateVo.getMarketplaceId());
                                    if(null != m){
                                        portfolio.setSymbol(m.getCurrencyCode());
                                    }
                                    portfolioBuilder.addPortfolios(portfolio.build());
                                }
                                portfolioRules.add(portfolioBuilder.build());
                            }
                        }
                        builder.addAllPortfolioRuleList(portfolioRules);
                    }
                }
                responseBuilder.setData(builder.build());
            }
        } else {
            responseBuilder.setCode(result.getCode());
            responseBuilder.setMsg(result.getMsg());
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void independentPageCampaign(IndependentPageCampaignRequest request, StreamObserver<IndependentPageCampaignResponse> responseObserver) {
        log.info("独立页分时预算查询 independentPageCampaign request:{}", request);
        IndependentPageCampaignResponse.Builder responseBuilder = IndependentPageCampaignResponse.newBuilder();
        if (!request.hasPuid() || !request.hasCampaignId() || !request.hasCampaignId()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            Result<IndependentCampaignVo> result = advertiseStrategyStatusService.
                    independentPageCampaign(request.getCampaignId(), request.getPuid(), request.getShopId(), request.getTraceId());
            if (result.getCode() == Result.SUCCESS) {
                responseBuilder.setCode(result.getCode());
                IndependentPageCampaign.Builder builder = IndependentPageCampaign.newBuilder();
                if (result.getData() != null) {
                    IndependentCampaignVo independentCampaignVo = result.getData();
                    builder.setId(independentCampaignVo.getId());
                    builder.setPuid(independentCampaignVo.getPuid());
                    builder.setShopId(independentCampaignVo.getShopId());
                    builder.setMarketplaceId(independentCampaignVo.getMarketplaceId());
                    builder.setStatus(independentCampaignVo.getStatus());
                    builder.setTemplateId(independentCampaignVo.getTemplateId());
                    builder.setTemplateName(independentCampaignVo.getTemplateName());
                    builder.setUpdateStatus(independentCampaignVo.getUpdateStatus());
                    builder.setOriginalBudget(independentCampaignVo.getOriginalBudget().doubleValue());
                    builder.setCurrency(independentCampaignVo.getCurrency());
                    builder.setTaskId(independentCampaignVo.getTaskId());
                    builder.setType(independentCampaignVo.getType());
                    builder.setChildrenItemType(independentCampaignVo.getChildrenItemType());
                    Map<Integer,List<CampaignRuleVo>> map = independentCampaignVo.getCampaignRuleVoList().stream().
                            collect(Collectors.groupingBy(CampaignRuleVo::getSiteDate));
                    for (Integer day : map.keySet()) {
                        CampaignRule.Builder rule = CampaignRule.newBuilder();
                        rule.setSiteDate(day);
                        if (day == 0) {
                            rule.setSiteDateName("每天");
                        } else {
                            rule.setSiteDateName(com.meiyunji.sponsored.service.
                                    cpc.util.Constants.getDateMap().get(day));
                        }
                        for (CampaignRuleVo campaignRuleVo : map.get(day)) {
                            Campaign.Builder campain = Campaign.newBuilder();
                            campain.setBudgetValue(campaignRuleVo.getBudgetValue().doubleValue());
                            campain.setStartTimeSite(campaignRuleVo.getStartTimeSite());
                            campain.setEndTimeSite(campaignRuleVo.getEndTimeSite());
                            if ("numerical".equals(independentCampaignVo.getChildrenItemType())) {
                                campain.setSymbol(independentCampaignVo.getCurrency());
                            } else {
                                campain.setSymbol("%");
                            }
                            if (campaignRuleVo.getBudgetType() != null) {
                                campain.setBudgetType(campaignRuleVo.getBudgetType());
                            }
                            rule.addCampaigns(campain.build());
                        }
                        builder.addCampaignRuleList(rule.build());
                    }
                    if (CollectionUtils.isNotEmpty(independentCampaignVo.getPeriodCampaignVoList())) {
                        List<PeriodCampaignVo> periodCampaignVoList = independentCampaignVo.getPeriodCampaignVoList();
                        List<ExecuteCampaignRpc> executeCampaignRpcList = Lists.newArrayList();
                        periodCampaignVoList.forEach(e->{
                            ExecuteCampaignRpc.Builder ecBuilder = ExecuteCampaignRpc.newBuilder();
                            ecBuilder.setBeijingDateTime(e.getBeijingDateTime());
                            ecBuilder.setSiteDateTime(e.getSiteDateTime());
                            if (StringUtils.isNotBlank(independentCampaignVo.getCurrency())) {
                                ecBuilder.setCurrency(independentCampaignVo.getCurrency());
                            }
                            ecBuilder.setNewBudget(e.getNewBudget());
                            executeCampaignRpcList.add(ecBuilder.build());
                        });
                        builder.addAllExecuteCampaign(executeCampaignRpcList);
                    }
                    responseBuilder.setData(builder.build());
                }
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void independentPageCampaignSpace(IndependentPageCampaignSpaceRequest request, StreamObserver<IndependentPageCampaignSpaceResponse> responseObserver) {
        log.info("独立页分时广告位查询 independentPageCampaign request:{}", request);
        IndependentPageCampaignSpaceResponse.Builder responseBuilder = IndependentPageCampaignSpaceResponse.newBuilder();
        if (!request.hasPuid() || !request.hasCampaignId() || !request.hasCampaignId()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            Result<IndependentCampaignSpaceVo> result = advertiseStrategyStatusService.
                    independentPageCampaignSpace(request.getCampaignId(), request.getPuid(), request.getShopId(), request.getTraceId());
            if (result.getCode() == Result.SUCCESS) {
                responseBuilder.setCode(result.getCode());
                IndependentPageCampaignSpace.Builder builder = IndependentPageCampaignSpace.newBuilder();
                if (result.getData() != null) {
                    IndependentCampaignSpaceVo independentCampaignSpaceVo = result.getData();
                    builder.setId(independentCampaignSpaceVo.getId());
                    builder.setPuid(independentCampaignSpaceVo.getPuid());
                    builder.setShopId(independentCampaignSpaceVo.getShopId());
                    builder.setMarketplaceId(independentCampaignSpaceVo.getMarketplaceId());
                    builder.setStatus(independentCampaignSpaceVo.getStatus());
                    builder.setTemplateId(independentCampaignSpaceVo.getTemplateId());
                    builder.setTemplateName(independentCampaignSpaceVo.getTemplateName());
                    builder.setUpdateStatus(independentCampaignSpaceVo.getUpdateStatus());
                    builder.setOriginalPlaceProductValue(independentCampaignSpaceVo.getOriginalPlaceProductValue().doubleValue());
                    builder.setOriginalPlaceTopValue(independentCampaignSpaceVo.getOriginalPlaceTopValue().doubleValue());
                    builder.setOriginalStrategy(independentCampaignSpaceVo.getOriginalStrategy());
                    if (independentCampaignSpaceVo.getOriginalAdOtherValue() != null) {
                        builder.setAdOtherValue(independentCampaignSpaceVo.getOriginalAdOtherValue().doubleValue());
                    } else {
                        builder.setAdOtherValue(0);
                    }
                    builder.setTaskId(independentCampaignSpaceVo.getTaskId());
                    builder.setType(independentCampaignSpaceVo.getType());
                    Map<Integer,List<CampaignPlacementRuleVo>> map = independentCampaignSpaceVo.getCampaignPlacementRuleList().
                            stream().
                            collect(Collectors.groupingBy(CampaignPlacementRuleVo::getSiteDate));
                    for (Integer day : map.keySet()) {
                        CampaignPlacementRule.Builder rule = CampaignPlacementRule.newBuilder();
                        rule.setSiteDate(day);
                        if (day == 0) {
                            rule.setSiteDateName("每天");
                        } else {
                            rule.setSiteDateName(com.meiyunji.sponsored.service.
                                    cpc.util.Constants.getDateMap().get(day));
                        }
                        for (CampaignPlacementRuleVo campaignPlacementRuleVo : map.get(day)) {
                            CampaignPlacement.Builder campainSpace = CampaignPlacement.newBuilder();
                            campainSpace.setStartTimeSite(campaignPlacementRuleVo.getStartTimeSite());
                            campainSpace.setEndTimeSite(campaignPlacementRuleVo.getEndTimeSite());
                            if (campaignPlacementRuleVo.getAdPlaceProductValue() != null) {
                                campainSpace.setAdPlaceProductValue(campaignPlacementRuleVo.getAdPlaceProductValue().doubleValue());
                            }
                            if (campaignPlacementRuleVo.getAdPlaceTopMaxValue() != null) {
                                campainSpace.setAdPlaceTopMaxValue(campaignPlacementRuleVo.getAdPlaceTopMaxValue().doubleValue());
                            }
                            if (campaignPlacementRuleVo.getAdPlaceTopType() != null) {
                                campainSpace.setAdPlaceTopType(campaignPlacementRuleVo.getAdPlaceTopType());
                            }
                            if (campaignPlacementRuleVo.getAdPlaceProductType() != null) {
                                campainSpace.setAdPlaceProductType(campaignPlacementRuleVo.getAdPlaceProductType());
                            }
                            if (campaignPlacementRuleVo.getAdPlaceTopValue() != null) {
                                campainSpace.setAdPlaceTopValue(campaignPlacementRuleVo.getAdPlaceTopValue().doubleValue());
                            }
                            if (campaignPlacementRuleVo.getAdPlaceTopMinValue() != null) {
                                campainSpace.setAdPlaceTopMinValue(campaignPlacementRuleVo.getAdPlaceTopMinValue().doubleValue());
                            }
                            if (campaignPlacementRuleVo.getAdPlaceProductMaxValue() != null) {
                                campainSpace.setAdPlaceProductMaxValue(campaignPlacementRuleVo.getAdPlaceProductMaxValue().doubleValue());
                            }
                            if (campaignPlacementRuleVo.getAdPlaceProductMinValue() != null) {
                                campainSpace.setAdPlaceProductMinValue(campaignPlacementRuleVo.getAdPlaceProductMinValue().doubleValue());
                            }
                            if (StringUtils.isNotBlank(campaignPlacementRuleVo.getStrategy())) {
                                campainSpace.setStrategy(campaignPlacementRuleVo.getStrategy());
                            }
                            if (campaignPlacementRuleVo.getAdOtherType() != null) {
                                campainSpace.setAdOtherType(campaignPlacementRuleVo.getAdOtherType());
                            } else {
                                campainSpace.setAdOtherType(4);
                            }
                            if (campaignPlacementRuleVo.getAdOtherValue() != null) {
                                campainSpace.setAdOtherValue(campaignPlacementRuleVo.getAdOtherValue().doubleValue());
                            } else {
                                campainSpace.setAdOtherValue(0);
                            }
                            campainSpace.setSymbol("%");
                            rule.addCampaignPlacements(campainSpace.build());
                        }
                        builder.addCampaignPlacementRuleList(rule.build());
                    }
                    if (CollectionUtils.isNotEmpty(independentCampaignSpaceVo.getPeriodCampaignSpaceVoList())) {
                        List<PeriodCampaignSpaceVo> periodCampaignSpaceVoList = independentCampaignSpaceVo.getPeriodCampaignSpaceVoList();
                        List<ExecuteCampaignSpaceRpc> executeCampaignSpaceRpcList = Lists.newArrayList();
                        periodCampaignSpaceVoList.forEach(e->{
                            ExecuteCampaignSpaceRpc.Builder ecsBuilder = ExecuteCampaignSpaceRpc.newBuilder();
                            ecsBuilder.setBeijingDateTime(e.getBeijingDateTime());
                            ecsBuilder.setSiteDateTime(e.getSiteDateTime());
                            if (StringUtils.isNotBlank(e.getNewStrategy())) {
                                ecsBuilder.setNewStrategy(e.getNewStrategy());
                            }
                            if (StringUtils.isNotBlank(e.getNewAdPlaceTopValue())) {
                                ecsBuilder.setNewPlaceTopValue(e.getNewAdPlaceTopValue());
                            }
                            if (StringUtils.isNotBlank(e.getNewAdPlaceProductValue())) {
                                ecsBuilder.setNewPlaceProductValue(e.getNewAdPlaceProductValue());
                            }
                            if (StringUtils.isNotBlank(e.getNewAdOtherValue())) {
                                ecsBuilder.setNewAdOtherValue(e.getNewAdOtherValue());
                            } else {
                                ecsBuilder.setNewAdOtherValue("0");
                            }
                            executeCampaignSpaceRpcList.add(ecsBuilder.build());
                        });
                        builder.addAllExecuteCampaignSpace(executeCampaignSpaceRpcList);
                    }
                    responseBuilder.setData(builder.build());
                }
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void independentPageTarget(IndependentPageTargetRequest request, StreamObserver<IndependentPageTargetResponse> responseObserver) {
        log.info("独立页分时竞价查询 independentPageTarget request:{}", request);
        IndependentPageTargetResponse.Builder responseBuilder = IndependentPageTargetResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasTargetId()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            Result<IndependentTargetVo> result = advertiseStrategyStatusService.
                    independentPageTarget(request.getTargetId(), request.getPuid(), request.getShopId(), request.getTraceId());
            if (result.getCode() == Result.SUCCESS) {
                responseBuilder.setCode(result.getCode());
                IndependentPageTarget.Builder builder = IndependentPageTarget.newBuilder();
                if (result.getData() != null) {
                    IndependentTargetVo independentTargetVo = result.getData();
                    builder.setId(independentTargetVo.getId());
                    builder.setPuid(independentTargetVo.getPuid());
                    builder.setShopId(independentTargetVo.getShopId());
                    builder.setMarketplaceId(independentTargetVo.getMarketplaceId());
                    builder.setStatus(independentTargetVo.getStatus());
                    builder.setTemplateId(independentTargetVo.getTemplateId());
                    builder.setTemplateName(independentTargetVo.getTemplateName());
                    builder.setUpdateStatus(independentTargetVo.getUpdateStatus());
                    builder.setOriginalBidValue(independentTargetVo.getOriginalBidValue().doubleValue());
                    builder.setCurrency(independentTargetVo.getCurrency());
                    builder.setTaskId(independentTargetVo.getTaskId());
                    builder.setType(independentTargetVo.getType());
                    if (StringUtils.isNotBlank(independentTargetVo.getAddWayType())) {
                        builder.setAddWayType(independentTargetVo.getAddWayType());
                    }
                    Map<Integer,List<TargetRuleVo>> map = independentTargetVo.getTargetRuleList().
                            stream().
                            collect(Collectors.groupingBy(TargetRuleVo::getSiteDate));
                    for (Integer day : map.keySet()) {
                        TargetRule.Builder rule = TargetRule.newBuilder();
                        rule.setSiteDate(day);
                        if (day == 0) {
                            rule.setSiteDateName("每天");
                        } else {
                            rule.setSiteDateName(com.meiyunji.sponsored.service.
                                    cpc.util.Constants.getDateMap().get(day));
                        }
                        for (TargetRuleVo targetRuleVo : map.get(day)) {
                            Target.Builder target = Target.newBuilder();
                            target.setBiddingType(targetRuleVo.getBiddingType());
                            target.setStartTimeSite(targetRuleVo.getStartTimeSite());
                            target.setEndTimeSite(targetRuleVo.getEndTimeSite());
                            if (targetRuleVo.getBiddingMaxValue() != null) {
                                target.setBiddingMaxValue(targetRuleVo.getBiddingMaxValue().doubleValue());
                            }
                            if (targetRuleVo.getBiddingMinValue() != null) {
                                target.setBiddingMinValue(targetRuleVo.getBiddingMinValue().doubleValue());
                            }
                            if (targetRuleVo.getBiddingValue() != null) {
                                target.setBiddingValue(targetRuleVo.getBiddingValue().doubleValue());
                            }
                            target.setSymbol(independentTargetVo.getCurrency());
                            rule.addTargets(target.build());
                        }
                        builder.addTargetRuleList(rule.build());
                    }
                    if (CollectionUtils.isNotEmpty(independentTargetVo.getPeriodTargetVoList())) {
                        List<PeriodTargetVo> periodTargetVoList = independentTargetVo.getPeriodTargetVoList();
                        List<ExecuteTargetRpc> executeTargetRpcList = Lists.newArrayList();
                        periodTargetVoList.forEach(e->{
                            ExecuteTargetRpc.Builder etBuilder = ExecuteTargetRpc.newBuilder();
                            etBuilder.setBeijingDateTime(e.getBeijingDateTime());
                            etBuilder.setSiteDateTime(e.getSiteDateTime());
                            etBuilder.setNewBidValue(e.getNewBiddingValue());
                            if (StringUtils.isNotBlank(independentTargetVo.getCurrency())) {
                                etBuilder.setCurrency(independentTargetVo.getCurrency());
                            }
                            executeTargetRpcList.add(etBuilder.build());
                        });
                        builder.addAllExecuteTarget(executeTargetRpcList);
                    }
                    responseBuilder.setData(builder.build());
                }
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void listStrategyPortfolio(StrategyPortfolioRequest request, StreamObserver<StrategyPortfolioResponse> responseObserver) {
        log.info("下拉框广告组合查询 listStrategyPortfolio request:{}", request);
        StrategyPortfolioResponse.Builder responseBuilder = StrategyPortfolioResponse.newBuilder();
        if (!request.hasPuid() || !request.hasTemplateId()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            Result<List<StrategyPortfolioVo>> result = advertiseStrategyStatusService.
                    listStrategyPortfolio(request.getTemplateId(), request.getPuid(), request.getShopIdList());
            List<StrategyPortfolio> portfolioList = Lists.newArrayList();
            if (result.getCode() == Result.SUCCESS) {
                List<StrategyPortfolioVo> voList = result.getData();
                if (CollectionUtils.isNotEmpty(voList)) {
                    for (StrategyPortfolioVo vo : voList) {
                        StrategyPortfolio.Builder builder = StrategyPortfolio.newBuilder();
                        builder.setPortfolioId(vo.getPortfolioId());
                        builder.setPortfolioName(vo.getPortfolioName());
                        portfolioList.add(builder.build());
                    }
                }
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
                responseBuilder.addAllData(portfolioList);
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void listStrategyCampaign(StrategyCampaignRequest request, StreamObserver<StrategyCampaignResponse> responseObserver) {
        log.info("下拉框活动查询 listStrategyCampaign request:{}", request);
        StrategyCampaignResponse.Builder responseBuilder = StrategyCampaignResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasTemplateId()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            Result<List<StrategyCampaignVo>> result = advertiseStrategyStatusService.
                    listStrategyCampaign(request.getTemplateId(), request.getPuid(), request.getShopId(), request.getTraceId(), request.getPortfolioIdList());
            List<StrategyCampaign> strategyCampaigns = Lists.newArrayList();
            if (result.getCode() == Result.SUCCESS) {
                List<StrategyCampaignVo> list = result.getData();
                if (CollectionUtils.isNotEmpty(list)) {
                    for (StrategyCampaignVo strategyCampaignVo : list) {
                        StrategyCampaign.Builder builder = StrategyCampaign.newBuilder();
                        builder.setAdType(strategyCampaignVo.getAdType());
                        builder.setCampaignId(strategyCampaignVo.getCampaignId());
                        builder.setCampaignName(strategyCampaignVo.getCampaignName());
                        strategyCampaigns.add(builder.build());
                    }
                }
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
                responseBuilder.addAllData(strategyCampaigns);
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void listStrategyGroup(StrategyGroupRequest request, StreamObserver<StrategyGroupResponse> responseObserver) {
        log.info("下拉框广告组查询 listStrategyCampaign request:{}", request);
        StrategyGroupResponse.Builder responseBuilder = StrategyGroupResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasTemplateId()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            Result<List<StrategyGroupVo>> result = advertiseStrategyStatusService.
                    listStrategyGroup(request.getPuid(), request.getShopId(), request.getCampaignIdList(), request.getTemplateId(), request.getTraceId());
            List<StrategyGroup> strategyGroupVos = Lists.newArrayList();
            if (result.getCode() == Result.SUCCESS) {
                List<StrategyGroupVo> list = result.getData();
                if (CollectionUtils.isNotEmpty(list)) {
                    for (StrategyGroupVo strategyGroupVo : list) {
                        StrategyGroup.Builder builder = StrategyGroup.newBuilder();
                        builder.setAdType(strategyGroupVo.getAdType());
                        builder.setAdGroupId(strategyGroupVo.getGroupId());
                        builder.setAdGroupName(strategyGroupVo.getGroupName());
                        strategyGroupVos.add(builder.build());
                    }
                }
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
                responseBuilder.addAllData(strategyGroupVos);
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void independentUpdate(IndependentUpdateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("独立活动策略修改 independentUpdateCampaign request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || CollectionUtils.isEmpty(request.getStartStopStateRuleListList())) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("请求参数错误");
        } else {
            List<StartStopStateRule> list = request.getStartStopStateRuleListList();
            List<StartStopStateRule> startStopStateRuleList = com.google.common.collect.Lists.newArrayList();
            startStopStateRuleList.addAll(list);
            if ("WEEKLY".equals(request.getType())) {
                List<Integer> dayList = startStopStateRuleList.stream().map(StartStopStateRule::getSiteDate).distinct().
                        collect(Collectors.toList());
                for (int i = 1;i < 8;i++) {
                    if (!dayList.contains(i)) {
                        StartStopStateRule.Builder startStopBuilder = StartStopStateRule.newBuilder();
                        startStopBuilder.setSiteDate(i);
                        StateRule.Builder stopRuleBuilder = StateRule.newBuilder();
                        stopRuleBuilder.setStartTimeSite(0);
                        stopRuleBuilder.setEndTimeSite(24);
                        stopRuleBuilder.setState("paused");
                        startStopBuilder.addStateRules(stopRuleBuilder.build());
                        startStopStateRuleList.add(startStopBuilder.build());
                    }
                }
            }
            startStopStateRuleList =  startStopStateRuleList.stream().sorted(Comparator.comparing(StartStopStateRule::getSiteDate)).collect(Collectors.toList());
            List<StartStopRuleVo> startStopRuleVoList = Lists.newArrayList();
            for (StartStopStateRule startStopStateRule : startStopStateRuleList) {
                List<StateRule> stateRuleList = startStopStateRule.getStateRulesList()
                        .stream().sorted(Comparator.comparing(StateRule::getStartTimeSite)).collect(Collectors.toList());
                Map<Integer,Integer> timeMap = stateRuleList.stream().
                        collect(Collectors.toMap(StateRule::getStartTimeSite,StateRule::getEndTimeSite));
                List<Integer> startList = stateRuleList.stream().map(StateRule::getStartTimeSite).
                        collect(Collectors.toList());
                Map<Integer,Integer> newTimeMap = getNewTimeMap(startList,timeMap);
                if (MapUtils.isNotEmpty(newTimeMap)) {
                    for (Integer startTime : newTimeMap.keySet()) {
                        StateRule.Builder stopRuleBuilder = StateRule.newBuilder();
                        stopRuleBuilder.setStartTimeSite(startTime);
                        stopRuleBuilder.setEndTimeSite(newTimeMap.get(startTime));
                        stopRuleBuilder.setState("paused");
                        stateRuleList.add(stopRuleBuilder.build());
                    }
                    //获取暂停时间段后再度排序
                    stateRuleList = stateRuleList.stream().sorted(Comparator.comparing(StateRule::getStartTimeSite)).collect(Collectors.toList());
                }
                for (StateRule stateRule : stateRuleList) {
                    StartStopRuleVo startStopRuleVo = new StartStopRuleVo();
                    startStopRuleVo.setSiteDate(startStopStateRule.getSiteDate());
                    startStopRuleVo.setStartTimeSite(stateRule.getStartTimeSite());
                    startStopRuleVo.setEndTimeSite(stateRule.getEndTimeSite());
                    startStopRuleVo.setState(stateRule.getState());
                    startStopRuleVoList.add(startStopRuleVo);
                }
            }
            Result<String> result = adStrategyStatusApiFactory.getStrategyStatusService(request.getItemType()).
                    independentUpdateStrategy(request.getStatusId(), request.getPuid(),request.getShopId(),
                            startStopRuleVoList,request.getType(), request.getUpdateId(), request.getLoginIp(), request.getTraceId());
            responseBuilder.setCode(Int32Value.of(result.getCode()));
            responseBuilder.setMsg(result.getMsg());
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     * 时间截取
     *
     * @param timeMap
     * @return
     */
    private Map<Integer,Integer> getNewTimeMap(List<Integer> startTimeList,Map<Integer,Integer> timeMap){
        log.info("模板设置时间 request {}",timeMap);
        int statTime = 0;
        int endTime = 24;
        Map<Integer,Integer> newTimeMap = Maps.newHashMap();
        for (int i=0;i<startTimeList.size();i++) {
            if (startTimeList.size() == 1) {
                if (startTimeList.get(i) == statTime && timeMap.get(startTimeList.get(i)) != endTime) {
                    newTimeMap.put(timeMap.get(startTimeList.get(i)), endTime);
                } else if (startTimeList.get(i) != statTime && timeMap.get(startTimeList.get(i)) == endTime) {
                    newTimeMap.put(statTime, startTimeList.get(i));
                } else if (startTimeList.get(i) != statTime && timeMap.get(startTimeList.get(i)) != endTime) {
                    newTimeMap.put(statTime, startTimeList.get(i));
                    newTimeMap.put(timeMap.get(startTimeList.get(i)), endTime);
                }
            } else {
                if (i == 0) {
                    if (!startTimeList.get(i).equals(statTime)) {
                        newTimeMap.put(statTime, startTimeList.get(i));
                    }
                    if (!timeMap.get(startTimeList.get(i)).equals(startTimeList.get(i + 1))) {
                        newTimeMap.put(timeMap.get(startTimeList.get(i)), startTimeList.get(i + 1));
                    }
                } else if (i == startTimeList.size() - 1 && !timeMap.get(startTimeList.get(i)).equals(endTime)) {
                    newTimeMap.put(timeMap.get(startTimeList.get(i)), endTime);
                } else if (i != 0 && i != startTimeList.size() - 1) {
                    if (!timeMap.get(startTimeList.get(i)).equals(startTimeList.get(i + 1))) {
                        newTimeMap.put(timeMap.get(startTimeList.get(i)), startTimeList.get(i + 1));
                    }
                }
            }
        }
        return newTimeMap;
    }

    @Override
    public void independentUpdateCampaign(independentUpdateCampaignRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("独立活动策略修改 independentUpdateCampaign request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || CollectionUtils.isEmpty(request.getCampaignRuleListList())) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("请求参数错误");
        } else {
            List<CampaignRule> campaignRuleList = request.getCampaignRuleListList();
            List<CampaignRuleVo> campaignRuleVoList = Lists.newArrayList();
            for (CampaignRule campaignRule : campaignRuleList) {
                for (Campaign campaign : campaignRule.getCampaignsList()) {
                    CampaignRuleVo campaignRuleVo = new CampaignRuleVo();
                    campaignRuleVo.setSiteDate(campaignRule.getSiteDate());
                    campaignRuleVo.setStartTimeSite(campaign.getStartTimeSite());
                    campaignRuleVo.setEndTimeSite(campaign.getEndTimeSite());
                    campaignRuleVo.setBudgetValue(BigDecimal.valueOf(campaign.getBudgetValue()));
                    if (campaign.hasBudgetType()) {
                        campaignRuleVo.setBudgetType(campaign.getBudgetType());
                    }
                    campaignRuleVoList.add(campaignRuleVo);
                }
            }
            Result<String> result = advertiseStrategyStatusService.independentUpdateCampaign(request.getStatusId(),
                    request.getPuid(), request.getShopId(), campaignRuleVoList, request.getType(),
                    request.getUpdateId(), request.getLoginIp(), request.getTraceId());
            responseBuilder.setCode(Int32Value.of(result.getCode()));
            responseBuilder.setMsg(result.getMsg());
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void independentUpdateCampaignSpace(independentUpdateCampaignSpaceRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("独立广告位策略修改 independentUpdateCampaignSpace request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || CollectionUtils.isEmpty(request.getCampaignPlacementRuleListList())) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("请求参数错误");
        } else {
            List<CampaignPlacementRule> campaignPlacementRuleList = request.getCampaignPlacementRuleListList();
            List<CampaignPlacementRuleVo> campaignPlacementRuleVoList = Lists.newArrayList();
            for (CampaignPlacementRule campaignPlacementRule : campaignPlacementRuleList) {
                for (CampaignPlacement campaignPlacement:campaignPlacementRule.getCampaignPlacementsList()) {
                    CampaignPlacementRuleVo campaignPlacementRuleVo = new CampaignPlacementRuleVo();
                    campaignPlacementRuleVo.setSiteDate(campaignPlacementRule.getSiteDate());
                    campaignPlacementRuleVo.setStartTimeSite(campaignPlacement.getStartTimeSite());
                    campaignPlacementRuleVo.setEndTimeSite(campaignPlacement.getEndTimeSite());
                    if (campaignPlacement.hasStrategy()) {
                        campaignPlacementRuleVo.setStrategy(campaignPlacement.getStrategy());
                    }
                    if (campaignPlacement.hasAdPlaceProductType()) {
                        campaignPlacementRuleVo.setAdPlaceProductType(campaignPlacement.getAdPlaceProductType());
                    }
                    if (campaignPlacement.hasAdPlaceProductValue()) {
                        campaignPlacementRuleVo.setAdPlaceProductValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceProductValue()));
                    }
                    if (campaignPlacement.hasAdPlaceTopType()) {
                        campaignPlacementRuleVo.setAdPlaceTopType(campaignPlacement.getAdPlaceTopType());
                    }
                    if (campaignPlacement.hasAdPlaceTopMaxValue()) {
                        campaignPlacementRuleVo.setAdPlaceTopMaxValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceTopMaxValue()));
                    }
                    if (campaignPlacement.hasAdPlaceTopMinValue()) {
                        campaignPlacementRuleVo.setAdPlaceTopMinValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceTopMinValue()));
                    }
                    if (campaignPlacement.hasAdPlaceProductMaxValue()) {
                        campaignPlacementRuleVo.setAdPlaceProductMaxValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceProductMaxValue()));
                    }
                    if (campaignPlacement.hasAdPlaceProductMinValue()) {
                        campaignPlacementRuleVo.setAdPlaceProductMinValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceProductMinValue()));
                    }
                    if (campaignPlacement.hasAdPlaceTopValue()) {
                        campaignPlacementRuleVo.setAdPlaceTopValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceTopValue()));
                    }
                    if (campaignPlacement.hasAdOtherType()) {
                        campaignPlacementRuleVo.setAdOtherType(campaignPlacement.getAdOtherType());
                    }
                    if (campaignPlacement.hasAdOtherValue()) {
                        campaignPlacementRuleVo.setAdOtherValue(BigDecimal.valueOf(campaignPlacement.getAdOtherValue()));
                    }
                    campaignPlacementRuleVoList.add(campaignPlacementRuleVo);
                }
            }
            Result<String> result = advertiseStrategyStatusService.independentUpdateCampaignSpace(request.getStatusId(),
                    request.getPuid(),request.getShopId(),campaignPlacementRuleVoList, request.getType(),
                    request.getUpdateId(), request.getLoginIp(), request.getTraceId());
            responseBuilder.setCode(Int32Value.of(result.getCode()));
            responseBuilder.setMsg(result.getMsg());
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void independentUpdateTarget(independentUpdateTargetRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("独立投放策略修改 independentUpdateCampaign request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || CollectionUtils.isEmpty(request.getTargetRuleListList())) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("请求参数错误");
        } else {
            List<TargetRule> targetRuleList = request.getTargetRuleListList();
            List<TargetRuleVo> targetRuleVoList = Lists.newArrayList();
            for (TargetRule targetRule : targetRuleList) {
                for (Target target : targetRule.getTargetsList()) {
                    TargetRuleVo targetRuleVo = new TargetRuleVo();
                    targetRuleVo.setSiteDate(targetRule.getSiteDate());
                    targetRuleVo.setStartTimeSite(target.getStartTimeSite());
                    targetRuleVo.setEndTimeSite(target.getEndTimeSite());
                    targetRuleVo.setBiddingType(target.getBiddingType());
                    targetRuleVo.setBiddingValue(BigDecimal.valueOf(target.getBiddingValue()));
                    if (target.hasBiddingMaxValue()) {
                        targetRuleVo.setBiddingMaxValue(BigDecimal.valueOf(target.getBiddingMaxValue()));
                    }
                    if (target.hasBiddingMinValue()) {
                        targetRuleVo.setBiddingMinValue(BigDecimal.valueOf(target.getBiddingMinValue()));
                    }
                    targetRuleVoList.add(targetRuleVo);
                }
            }
            Result<String> result = advertiseStrategyStatusService.independentUpdateTarget(request.getStatusId(),
                    request.getPuid(),request.getShopId(),targetRuleVoList,request.getType(),
                    request.getUpdateId(), request.getLoginIp(), request.getTraceId());
            responseBuilder.setCode(Int32Value.of(result.getCode()));
            responseBuilder.setMsg(result.getMsg());
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void independentUpdatePortfolio(IndependentUpdateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("独立广告组合策略修改 independentUpdatePortfolio request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || CollectionUtils.isEmpty(request.getPortfolioRuleListList())) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("请求参数错误");
        } else {
            List<PortfolioRule> list = request.getPortfolioRuleListList();
            List<PortfolioRuleVo> portfolioRuleVoList = Lists.newArrayList();
            OriginValueVo returnValue = new OriginValueVo();
            for (PortfolioRule portfolioRule : list) {
                for (Portfolio portfolio : portfolioRule.getPortfoliosList()) {
                    PortfolioRuleVo portfolioRuleVo = new PortfolioRuleVo();
                    if (portfolioRule.hasSiteDate()) {
                        portfolioRuleVo.setSiteDate(portfolioRule.getSiteDate());
                    }
                    if (StringUtils.isNotBlank(portfolioRule.getStartDate())) {
                        portfolioRuleVo.setStartDate(portfolioRule.getStartDate());
                    }
                    if (StringUtils.isNotBlank(portfolioRule.getEndDate())) {
                        portfolioRuleVo.setEndDate(portfolioRule.getEndDate());
                    }
                    if (StringUtils.isNotBlank(portfolio.getPolicy())) {
                        portfolioRuleVo.setPolicy(portfolio.getPolicy());
                    }
                    if (portfolio.hasAmount()) {
                        portfolioRuleVo.setAmount(BigDecimal.valueOf(portfolio.getAmount()));
                    }
                    portfolioRuleVoList.add(portfolioRuleVo);
                }
            }
            if ("MONTH_DAY".equals(request.getType())) {
                portfolioRuleVoList = portfolioRuleVoList.stream().sorted(Comparator.comparing(PortfolioRuleVo::getStartDate)).collect(Collectors.toList());
            } else {
                portfolioRuleVoList = portfolioRuleVoList.stream().sorted(Comparator.comparing(PortfolioRuleVo::getSiteDate)).collect(Collectors.toList());
            }
            if (request.getReturnValue().hasAmount()) {
                returnValue.setAmount(BigDecimal.valueOf(request.getReturnValue().getAmount()));
            }
            if (StringUtils.isNotBlank(request.getReturnValue().getPolicy())) {
                returnValue.setPolicy(request.getReturnValue().getPolicy());
            }
            Result<String> result = adStrategyStatusApiFactory.getStrategyStatusService(request.getItemType()).
                    independentUpdatePortfolioStrategy(request.getStatusId(), request.getPuid(),request.getShopId(),
                            JSONUtil.objectToJson(portfolioRuleVoList),request.getType(), request.getUpdateId(), request.getLoginIp(), returnValue, request.getTraceId(), null);
            responseBuilder.setCode(Int32Value.of(result.getCode()));
            responseBuilder.setMsg(result.getMsg());
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }


    @Override
    public void independentUpdatePortfolioHour(IndependentUpdateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("独立广告组合策略修改 independentUpdatePortfolio request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || CollectionUtils.isEmpty(request.getPortfolioHourRuleListList())) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("请求参数错误");
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        List<PortfolioHourRule> rules = request.getPortfolioHourRuleListList();
        OriginValueVo returnValue = new OriginValueVo();
        returnValue.setAmount(BigDecimal.valueOf(request.getReturnValue().getAmount()));
        if (StringUtils.isNotBlank(request.getReturnValue().getPolicy())) {
            returnValue.setPolicy(request.getReturnValue().getPolicy());
        }
        List<PortfolioHourRuleVo> portfolioRuleVoList = Lists.newArrayList();
        for (PortfolioHourRule portfolioHourRule : rules) {
            for (Portfolio portfolio : portfolioHourRule.getPortfoliosList()) {
                PortfolioHourRuleVo portfolioHourRuleVo = new PortfolioHourRuleVo();
                portfolioHourRuleVo.setSiteDate(portfolioHourRule.getSiteDate());
                portfolioHourRuleVo.setStartTimeSite(portfolio.getStartTimeSite());
                portfolioHourRuleVo.setEndTimeSite(portfolio.getEndTimeSite());
                portfolioHourRuleVo.setAmount(BigDecimal.valueOf(portfolio.getAmount()));
                portfolioHourRuleVo.setPolicy(portfolio.getPolicy());
                portfolioRuleVoList.add(portfolioHourRuleVo);
            }
        }
        Result<String> result = adStrategyStatusApiFactory.getStrategyStatusService(Constants.PORTFOLIO_HOUR).
                independentUpdatePortfolioStrategy(request.getStatusId(), request.getPuid(),request.getShopId(),
                        JSONUtil.objectToJson(portfolioRuleVoList),request.getType(), request.getUpdateId(), request.getLoginIp(), returnValue, request.getTraceId(), request.getChildrenItemType());
        responseBuilder.setCode(Int32Value.of(result.getCode()));
        responseBuilder.setMsg(result.getMsg());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void independentCreateCampaign(IndependentCreateCampaignRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("独立预算策略创建 independentCreateCampaign request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || CollectionUtils.isEmpty(request.getCampaignRuleList())) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("请求参数错误");
        } else {
            IndependentCreateCampaignVo campaignVo = new IndependentCreateCampaignVo();
            campaignVo.setPuid(request.getPuid());
            campaignVo.setShopId(request.getShopId());
            campaignVo.setMarketplaceId(request.getMarketplaceId());
            campaignVo.setType(request.getType());
            campaignVo.setCampaignId(request.getCampaignId());
            campaignVo.setChildrenItemType(request.getChildrenItemType());
            campaignVo.setCreateId(request.getCreateId());
            campaignVo.setCreateName(request.getCreateName());
            campaignVo.setTemplateName(request.getTemplateName());
            campaignVo.setUpdateId(request.getUpdateId());
            campaignVo.setUpdateName(request.getUpdateName());
            List<CampaignRuleVo> ruleVoList = Lists.newArrayList();
            for (CampaignRule campaignRule : request.getCampaignRuleList()) {
                for (Campaign campaign : campaignRule.getCampaignsList()) {
                    CampaignRuleVo campaignRuleVo = new CampaignRuleVo();
                    campaignRuleVo.setBudgetType(campaign.getBudgetType());
                    campaignRuleVo.setBudgetValue(BigDecimal.valueOf(campaign.getBudgetValue()));
                    campaignRuleVo.setSiteDate(campaignRule.getSiteDate());
                    campaignRuleVo.setStartTimeSite(campaign.getStartTimeSite());
                    campaignRuleVo.setEndTimeSite(campaign.getEndTimeSite());
                    ruleVoList.add(campaignRuleVo);
                }
            }
            campaignVo.setCampaignRuleVoList(ruleVoList);
            Result<String> result = advertiseStrategyStatusService.independentCreateCampaign(campaignVo);
            responseBuilder.setCode(Int32Value.of(result.getCode()));
            responseBuilder.setMsg(result.getMsg());
        }

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void independentCreateCampaignSpace(IndependentCreateCampaignSpaceRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("独立广告位策略创建 independentCreateCampaign request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || CollectionUtils.isEmpty(request.getCampaignPlacementRuleList())) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("请求参数错误");
        } else {
            IndependentCreateCampaignSpaceVo campaignSpaceVo = new IndependentCreateCampaignSpaceVo();
            campaignSpaceVo.setPuid(request.getPuid());
            campaignSpaceVo.setShopId(request.getShopId());
            campaignSpaceVo.setMarketplaceId(request.getMarketplaceId());
            campaignSpaceVo.setType(request.getType());
            campaignSpaceVo.setCampaignId(request.getCampaignId());
            campaignSpaceVo.setCreateId(request.getCreateId());
            campaignSpaceVo.setCreateName(request.getCreateName());
            campaignSpaceVo.setTemplateName(request.getTemplateName());
            campaignSpaceVo.setUpdateId(request.getUpdateId());
            campaignSpaceVo.setUpdateName(request.getUpdateName());
            List<CampaignPlacementRuleVo> ruleVoList = Lists.newArrayList();
            for (CampaignPlacementRule campaignPlacementRule : request.getCampaignPlacementRuleList()) {
                for (CampaignPlacement campaignPlacement : campaignPlacementRule.getCampaignPlacementsList()) {
                    CampaignPlacementRuleVo campaignPlacementRuleVo = new CampaignPlacementRuleVo();
                    campaignPlacementRuleVo.setSiteDate(campaignPlacementRule.getSiteDate());
                    campaignPlacementRuleVo.setStartTimeSite(campaignPlacement.getStartTimeSite());
                    campaignPlacementRuleVo.setEndTimeSite(campaignPlacement.getEndTimeSite());
                    campaignPlacementRuleVo.setStrategy(campaignPlacement.getStrategy());
                    campaignPlacementRuleVo.setAdPlaceTopValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceTopValue()));
                    campaignPlacementRuleVo.setAdPlaceProductValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceProductValue()));
                    campaignPlacementRuleVo.setAdPlaceProductMaxValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceProductMaxValue()));
                    campaignPlacementRuleVo.setAdPlaceTopMaxValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceTopMaxValue()));
                    campaignPlacementRuleVo.setAdPlaceProductMinValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceProductMaxValue()));
                    campaignPlacementRuleVo.setAdPlaceTopMinValue(BigDecimal.valueOf(campaignPlacement.getAdPlaceTopMinValue()));
                    campaignPlacementRuleVo.setAdPlaceProductType(campaignPlacement.getAdPlaceProductType());
                    campaignPlacementRuleVo.setAdPlaceTopType(campaignPlacement.getAdPlaceTopType());
                    ruleVoList.add(campaignPlacementRuleVo);
                }
            }
            campaignSpaceVo.setCampaignPlacementRuleVoList(ruleVoList);
            Result<String> result = advertiseStrategyStatusService.independentCreateCampaignSpace(campaignSpaceVo);
            responseBuilder.setCode(Int32Value.of(result.getCode()));
            responseBuilder.setMsg(result.getMsg());
        }

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void independentCreateTarget(IndependentCreateTargetRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("独立投放策略创建 independentCreateTarget request:{}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || CollectionUtils.isEmpty(request.getTargetRuleList())) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("请求参数错误");
        } else {
            IndependentCreateTargetVo targetVo = new IndependentCreateTargetVo();
            targetVo.setPuid(request.getPuid());
            targetVo.setShopId(request.getShopId());
            targetVo.setMarketplaceId(request.getMarketplaceId());
            targetVo.setType(request.getType());
            targetVo.setCampaignId(request.getCampaignId());
            targetVo.setAdGroupId(request.getAdGroupId());
            targetVo.setTargetId(request.getTargetId());
            targetVo.setTargetType(request.getTargetType());
            targetVo.setOriginBiddingValue(BigDecimal.valueOf(request.getOriginBiddingValue()));
            targetVo.setAdType(request.getAdType());
            targetVo.setCreateId(request.getCreateId());
            targetVo.setCreateName(request.getCreateName());
            targetVo.setTemplateName(request.getTemplateName());
            targetVo.setUpdateId(request.getUpdateId());
            targetVo.setUpdateName(request.getUpdateName());
            List<TargetRuleVo> ruleVoList = Lists.newArrayList();
            for (TargetRule targetRule : request.getTargetRuleList()) {
                for (Target target : targetRule.getTargetsList()) {
                    TargetRuleVo targetRuleVo = new TargetRuleVo();
                    targetRuleVo.setSiteDate(targetRule.getSiteDate());
                    targetRuleVo.setStartTimeSite(target.getStartTimeSite());
                    targetRuleVo.setEndTimeSite(target.getEndTimeSite());
                    targetRuleVo.setBiddingMaxValue(BigDecimal.valueOf(target.getBiddingMaxValue()));
                    targetRuleVo.setBiddingMinValue(BigDecimal.valueOf(target.getBiddingMinValue()));
                    targetRuleVo.setBiddingType(target.getBiddingType());
                    targetRuleVo.setBiddingValue(BigDecimal.valueOf(target.getBiddingValue()));
                    ruleVoList.add(targetRuleVo);
                }
            }
            targetVo.setTargetRuleVoList(ruleVoList);
            Result<String> result = advertiseStrategyStatusService.independentCreateTarget(targetVo);
            responseBuilder.setCode(Int32Value.of(result.getCode()));
            responseBuilder.setMsg(result.getMsg());
        }

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void adSubmitStrategy(AdStrategyStatusAddRequest request, StreamObserver<AdSubmitStrategyResponse> responseObserver) {
        log.info("策略确认提交 adSubmitStrategy request:{}", request);
        AdSubmitStrategyResponse.Builder responseBuilder = AdSubmitStrategyResponse.newBuilder();
        if (!request.hasTemplateId() || !request.hasPuid() || !request.hasItemType() || !request.hasUpdateId()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            List<SubmitStrategyVo> paramList = Lists.newArrayList();
            List<AdStrategyStatusRpc> adStrategyStatusRpcList = request.getStatusRpcList();
            AdvertiseStrategyTemplate advertiseStrategyTemplate = advertiseStrategyTemplateDao.selectByPrimaryKey(request.getPuid(), request.getTemplateId());
            if (advertiseStrategyTemplate == null) {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg("当前模板已被删除");
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
            Integer count = advertiseStrategyStatusDao.getCount(request.getPuid(), request.getTemplateId());
            if (count + adStrategyStatusRpcList.size() > indexStrategyConfig.getAdStrategyMaxSize()) {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg(String.format(Constants.AD_STRATEGY_MAX_COUNT_MSG, advertiseStrategyTemplate.getTemplateName(), count, indexStrategyConfig.getAdStrategyMaxSize()));
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
            if (CollectionUtils.isNotEmpty(adStrategyStatusRpcList)) {
                adStrategyStatusRpcList.forEach(e->{
                    SubmitStrategyVo submitStrategyVo = new SubmitStrategyVo();
                    submitStrategyVo.setShopId(e.getShopId());
                    submitStrategyVo.setMarketplaceId(e.getMarketplaceId());
                    submitStrategyVo.setItemId(e.getItemId());
                    submitStrategyVo.setAdType(e.getAdType().toUpperCase(Locale.ROOT));
                    submitStrategyVo.setAdGroupId(e.getAdGroupId());
                    submitStrategyVo.setCampaignId(e.getCampaignId());
                    submitStrategyVo.setTargetType(e.getTargetType());
                    submitStrategyVo.setTargetName(e.getTargetName());
                    submitStrategyVo.setOriginBudgetValue(BigDecimal.valueOf(e.getOriginBudgetValue()));
                    submitStrategyVo.setOriginStrategy(e.getOriginStrategy());
                    submitStrategyVo.setOriginAdPlaceTopValue(BigDecimal.valueOf(e.getOriginAdPlaceTopValue()));
                    submitStrategyVo.setOriginAdPlaceProductValue(BigDecimal.valueOf(e.getOriginAdPlaceProductValue()));
                    submitStrategyVo.setOriginAdPlaceTopValue(BigDecimal.valueOf(e.getOriginAdPlaceTopValue()));
                    submitStrategyVo.setOriginBiddingValue(BigDecimal.valueOf(e.getOriginBiddingValue()));
                    submitStrategyVo.setStatus(e.getStatus());
                    submitStrategyVo.setAdTargetType(e.getAdTargetType());
                    submitStrategyVo.setTargetName(e.getTargetName());
                    submitStrategyVo.setIsBudgetPricing(e.getIsBudgetPricing());
                    submitStrategyVo.setIsPricing(e.getIsPricing());
                    paramList.add(submitStrategyVo);
                });
            }
            Result<List<ErrorMsgVo>> result = adStrategyStatusApiFactory.
                    getStrategyStatusService(request.getItemType()).adSubmitStrategy(request.getPuid(),paramList,request.getTemplateId(),request.getUpdateId());
            if (result.getCode() == Result.SUCCESS) {
                ErrorData.Builder errorData = ErrorData.newBuilder();
                if (CollectionUtils.isNotEmpty(result.getData())) {
                    List<ErrorMsgVo> errorMsgVoList = result.getData();
                    errorData.setCountSize(request.getStatusRpcList().size());
                    errorData.setErrorSize(errorMsgVoList.size());
                    Map<String,List<ErrorMsgVo>> map = errorMsgVoList.stream().
                            collect(Collectors.groupingBy(ErrorMsgVo::getErrorMsg));
                    List<ErrorMsg> list = Lists.newArrayList();
                    map.forEach((k,v)->{
                        ErrorMsg.Builder builder = ErrorMsg.newBuilder();
                        builder.setPuid(request.getPuid());
                        builder.setErrorMsg(k);
                        v.forEach(e->{
                            ErrorStrategy.Builder esBuilder = ErrorStrategy.newBuilder();
                            esBuilder.setItemId(e.getItemId());
                            esBuilder.setItemName(e.getItemName());
                            builder.addErrorStrategy(esBuilder.build());
                        });
                        list.add(builder.build());
                    });
                    errorData.addAllData(list);
                } else {
                    errorData.setErrorSize(0);
                    errorData.setCountSize(request.getStatusRpcList().size());
                }
                responseBuilder.setData(errorData);
            }
            responseBuilder.setCode(result.getCode());
            responseBuilder.setMsg(result.getMsg());
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void executePreview(ExecutePreviewRequest request, StreamObserver<ExecutePreviewResponse> responseObserver) {
        log.info("执行预览 executePreview request:{}", request);
        ExecutePreviewResponse.Builder responseBuilder = ExecutePreviewResponse.newBuilder();
        if (!request.hasPuid() || !request.hasTemplateId()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            ExecuteRequestParam param = new ExecuteRequestParam();
            param.setPuid(request.getPuid());
            param.setShopId(request.getShopId());
            param.setTaskId(request.getTaskId());
            param.setTemplateId(request.getTemplateId());
            param.setItemType(request.getItemType());
            param.setTraceId(request.getTraceId());
            Result<List<ExecuteVo>> result = adStrategyStatusApiFactory.
                    getStrategyStatusService(request.getItemType()).executePreview(param);
            List<ExecutePreview> executePreviews = Lists.newArrayList();
            if (result.getCode() == Result.SUCCESS) {
                List<ExecuteVo> executeVoList = result.getData();
                if (CollectionUtils.isNotEmpty(executeVoList)) {
                    for (ExecuteVo executeVo : executeVoList) {
                        if ("CAMPAIGN".equals(request.getItemType())) {
                            executeVo.getPeriodCampaignVoList().forEach(e->{
                                ExecutePreview.Builder epBuilder = ExecutePreview.newBuilder();
                                epBuilder.setPuid(request.getPuid());
                                epBuilder.setShopId(request.getShopId());
                                epBuilder.setMarketplaceId(executeVo.getMarketplaceId());
                                epBuilder.setSiteDateTime(e.getSiteDateTime());
                                epBuilder.setBeijingDateTime(e.getBeijingDateTime());
                                epBuilder.setNewBudget(e.getNewBudget());
                                epBuilder.setType(executeVo.getType());
                                MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.
                                        getByMarketplaceId(executeVo.getMarketplaceId());
                                if( null != m){
                                    epBuilder.setCurrency(m.getCurrencyCode());
                                }
                                executePreviews.add(epBuilder.build());
                            });
                        } else if ("CAMPAIGN_PLACEMENT".equals(request.getItemType())) {
                            executeVo.getPeriodCampaignSpaceVoList().forEach(e->{
                                ExecutePreview.Builder epBuilder = ExecutePreview.newBuilder();
                                epBuilder.setPuid(request.getPuid());
                                epBuilder.setShopId(request.getShopId());
                                epBuilder.setMarketplaceId(executeVo.getMarketplaceId());
                                epBuilder.setSiteDateTime(e.getSiteDateTime());
                                epBuilder.setBeijingDateTime(e.getBeijingDateTime());
                                if (StringUtils.isNotBlank(e.getNewStrategy())) {
                                    epBuilder.setNewStrategy(e.getNewStrategy());
                                }
                                if (StringUtils.isNotBlank(e.getNewAdPlaceProductValue())) {
                                    epBuilder.setNewPlaceProductValue(e.getNewAdPlaceProductValue());
                                }
                                if (StringUtils.isNotBlank(e.getNewAdPlaceTopValue())) {
                                    epBuilder.setNewPlaceTopValue(e.getNewAdPlaceTopValue());
                                }
                                if (StringUtils.isNotBlank(e.getNewAdOtherValue())) {
                                    epBuilder.setNewAdOtherValue(e.getNewAdOtherValue());
                                } else {
                                    epBuilder.setNewAdOtherValue("0");
                                }
                                epBuilder.setType(executeVo.getType());
                                executePreviews.add(epBuilder.build());
                            });
                        } else {
                            executeVo.getPeriodTargetVoList().forEach(e->{
                                ExecutePreview.Builder epBuilder = ExecutePreview.newBuilder();
                                epBuilder.setPuid(request.getPuid());
                                epBuilder.setShopId(request.getShopId());
                                epBuilder.setMarketplaceId(executeVo.getMarketplaceId());
                                epBuilder.setSiteDateTime(e.getSiteDateTime());
                                epBuilder.setBeijingDateTime(e.getBeijingDateTime());
                                if (StringUtils.isNotBlank(e.getNewBiddingValue())) {
                                    epBuilder.setNewBidValue(e.getNewBiddingValue());
                                }
                                epBuilder.setType(executeVo.getType());
                                MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.
                                        getByMarketplaceId(executeVo.getMarketplaceId());
                                if( null != m){
                                    epBuilder.setCurrency(m.getCurrencyCode());
                                }
                                executePreviews.add(epBuilder.build());
                            });
                        }
                    }
                }
                responseBuilder.setCode(result.getCode());
                responseBuilder.addAllData(executePreviews);
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void queryCampaignRealTimeBudget(QueryCampaignRealTimeBudgetRequest request, StreamObserver<QueryCampaignRealTimeBudgetResponse> responseObserver) {
        log.info("queryCampaignRealTimeBudget request:{}", request);
        QueryCampaignRealTimeBudgetResponse.Builder responseBuilder = QueryCampaignRealTimeBudgetResponse.newBuilder();
        List<CampaignRealTimeBudgetParam> paramList = Lists.newArrayList();
        for (CampaignRealTimeBudget campaignRealTimeBudget : request.getCampaignRealTimeBudgetsList()) {
            CampaignRealTimeBudgetParam param = new CampaignRealTimeBudgetParam();
            param.setCampaignId(campaignRealTimeBudget.getCampaignId());
            param.setBudgetValue(BigDecimal.valueOf(campaignRealTimeBudget.getBudgetValue()));
            param.setAdType(campaignRealTimeBudget.getAdType());
            param.setStatus(campaignRealTimeBudget.getStatus());
            param.setCampaignName(campaignRealTimeBudget.getCampaignName());
            param.setStatusId(campaignRealTimeBudget.getStatusId());
            paramList.add(param);
        }
        Result<List<CampaignRealTimeBudgetResult>> result = advertiseStrategyQueryService.queryCampaignRealTimeBudget(request.getPuid(), request.getShopId(), paramList);
        if (ResultUtil.SUCCESS == result.getCode()) {
            if (CollectionUtils.isNotEmpty(result.getData())) {
                result.getData().forEach(e->{
                    QueryCampaignRealTimeBudget.Builder builder = QueryCampaignRealTimeBudget.newBuilder();
                    builder.setPuid(request.getPuid());
                    builder.setShopId(request.getShopId());
                    if (StringUtils.isNotBlank(e.getCampaignId())) {
                        builder.setCampaignId(e.getCampaignId());
                    }
                    if (StringUtils.isNotBlank(e.getCampaignName())) {
                        builder.setCampaignName(e.getCampaignName());
                    }
                    if (StringUtils.isNotBlank(e.getAdType())) {
                        builder.setAdType(e.getAdType());
                    }
                    if (e.getStatusId() != null) {
                        builder.setStatusId(e.getStatusId());
                    }
                    if (StringUtils.isNotBlank(e.getStatus())) {
                        builder.setStatus(e.getStatus());
                    }
                    if (StringUtils.isNotBlank(e.getErrorMsg())) {
                        builder.setErrorMsg(e.getErrorMsg());
                    }
                    if (StringUtils.isNotBlank(e.getOriginBudgetValue())) {
                        builder.setOriginBudgetValue(e.getOriginBudgetValue());
                    }
                    if (StringUtils.isNotBlank(e.getRealTimeBudgetValue())) {
                        builder.setRealTimeBudgetValue(e.getRealTimeBudgetValue());
                    }
                    responseBuilder.addData(builder.build());
                });
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void queryTargetRealTimeBid(QueryTargetRealTimeBidRequest request, StreamObserver<QueryTargetRealTimeBidResponse> responseObserver) {
        log.info("QueryTargetRealTimeBidResponse request:{}", request);
        QueryTargetRealTimeBidResponse.Builder responseBuilder = QueryTargetRealTimeBidResponse.newBuilder();
        List<TargetRealTimeBidParam> paramList = Lists.newArrayList();
        for (TargetRealTimeBid targetRealTimeBid : request.getQueryTargetRealTimeBidsList()) {
            TargetRealTimeBidParam param = new TargetRealTimeBidParam();
            param.setCampaignId(targetRealTimeBid.getCampaignId());
            param.setAdGroupId(targetRealTimeBid.getAdGroupId());
            param.setTargetId(targetRealTimeBid.getTargetId());
            param.setTargetType(targetRealTimeBid.getTargetType());
            param.setTargetName(targetRealTimeBid.getTargetName());
            param.setBiddingValue(BigDecimal.valueOf(targetRealTimeBid.getBiddingValue()));
            param.setAdType(targetRealTimeBid.getAdType());
            param.setStatus(targetRealTimeBid.getStatus());
            param.setStatusId(targetRealTimeBid.getStatusId());
            paramList.add(param);
        }
        Result<List<TargetRealTimeBidResult>> result = advertiseStrategyQueryService.queryTargetRealTimeBid(request.getPuid(), request.getShopId(), paramList);
        if (ResultUtil.SUCCESS == result.getCode()) {
            if (CollectionUtils.isNotEmpty(result.getData())) {
                result.getData().forEach(e->{
                    QueryTargetRealTimeBid.Builder builder = QueryTargetRealTimeBid.newBuilder();
                    builder.setPuid(request.getPuid());
                    builder.setShopId(request.getShopId());
                    if (StringUtils.isNotBlank(e.getTargetId())) {
                        builder.setTargetId(e.getTargetId());
                    }
                    if (StringUtils.isNotBlank(e.getTargetName())) {
                        builder.setTargetName(e.getTargetName());
                    }
                    if (StringUtils.isNotBlank(e.getAdType())) {
                        builder.setAdType(e.getAdType());
                    }
                    if (e.getStatusId() != null) {
                        builder.setStatusId(e.getStatusId());
                    }
                    if (StringUtils.isNotBlank(e.getStatus())) {
                        builder.setStatus(e.getStatus());
                    }
                    if (StringUtils.isNotBlank(e.getOriginBiddingValue())) {
                        builder.setOriginBiddingValue(e.getOriginBiddingValue());
                    }
                    if (StringUtils.isNotBlank(e.getRealTimeBiddingValue())) {
                        builder.setRealTimeBiddingValue(e.getRealTimeBiddingValue());
                    }
                    if (StringUtils.isNotBlank(e.getErrorMsg())) {
                        builder.setErrorMsg(e.getErrorMsg());
                    }
                    responseBuilder.addData(builder.build());
                });
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void queryAdStrategyGroup(QueryAdStrategyGroupRequest request, StreamObserver<QueryAdStrategyGroupResponse> responseObserver) {
        log.info("查询广告组数据 queryAdStrategyGroup request:{}", request);
        QueryAdStrategyGroupResponse.Builder responseBuilder = QueryAdStrategyGroupResponse.newBuilder();
        if (!request.hasPuid() || !request.hasPageNo() || !request.hasPageSize() || !request.hasAdType()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            AdStrategyGroupParam param = new AdStrategyGroupParam();
            param.setPuid(request.getPuid());
            param.setShopId(request.getShopId());
            param.setPageNo(request.getPageNo());
            param.setPageSize(request.getPageSize());
            param.setAdGroupName(request.getSearchValue());
            param.setAdType(request.getAdType());
            param.setState(request.getState());
            param.setServingStatusList(request.getServingStatusList());
            param.setPortfolioIdList(request.getPortfolioIdsList());
            param.setCampaignIdList(request.getCampaignIdsList());
            Result<Page<AdStrategyGroupVo>> result = advertiseStrategyGroupTargetBidService.queryAdStrategyGroup(param);
            if (result.getCode() == Result.SUCCESS) {
                responseBuilder.setCode(result.getCode());
                Page<AdStrategyGroupVo> page = result.getData();
                QueryAdStrategyGroupPage.Builder pageBuilder = QueryAdStrategyGroupPage.newBuilder();
                pageBuilder.setPageNo(page.getPageNo());
                pageBuilder.setPageSize(page.getPageSize());
                pageBuilder.setTotalPage(page.getTotalPage());
                pageBuilder.setTotalSize(page.getTotalSize());
                List<AdStrategyGroupVo> adStrategyGroupVoList = page.getRows();
                if (CollectionUtils.isNotEmpty(adStrategyGroupVoList)) {
                    List<QueryAdStrategyGroup> list = Lists.newArrayListWithExpectedSize(adStrategyGroupVoList.size());
                    //获取所有的店铺名称
                    List<Integer> shopIds = adStrategyGroupVoList.stream().
                            map(AdStrategyGroupVo::getShopId).distinct().collect(Collectors.toList());
                    List<ShopAuth> shops = shopAuthDao.getScAndVcByIds(shopIds);
                    Map<Integer, ShopAuth> shopAuthMap = null;
                    if (CollectionUtils.isNotEmpty(shops)) {
                        shopAuthMap = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
                    }
                    for (AdStrategyGroupVo vo : adStrategyGroupVoList) {
                        QueryAdStrategyGroup.Builder builder = QueryAdStrategyGroup.newBuilder();
                        builder.setPuid(vo.getPuid());
                        builder.setShopId(vo.getShopId());
                        builder.setMarketplaceId(vo.getMarketplaceId());
                        builder.setAdType(vo.getAdType());
                        if (StringUtils.isNotBlank(vo.getCampaignId())) {
                            builder.setCampaignId(vo.getCampaignId());
                        }
                        if (StringUtils.isNotBlank(vo.getAdGroupId())) {
                            builder.setAdGroupId(vo.getAdGroupId());
                        }
                        if (StringUtils.isNotBlank(vo.getCampaignName())) {
                            builder.setCampaignName(vo.getCampaignName());
                        }
                        if (StringUtils.isNotBlank(vo.getAdGroupName())) {
                            builder.setAdGroupName(vo.getAdGroupName());
                        }
                        if (StringUtils.isNotBlank(vo.getServingStatus())) {
                            builder.setServingStatus(vo.getServingStatus());
                        }
                        if (StringUtils.isNotBlank(vo.getServingStatusName())) {
                            builder.setServingStatusName(vo.getServingStatusName());
                        }
                        if (StringUtils.isNotBlank(vo.getServingStatusDec())) {
                            builder.setServingStatusDec(vo.getServingStatusDec());
                        }
                        if (StringUtils.isNotBlank(vo.getPortfolioId())) {
                            builder.setPortfolioId(vo.getPortfolioId());
                        }
                        if (StringUtils.isNotBlank(vo.getPortfolioName())) {
                            builder.setPortfolioName(vo.getPortfolioName());
                        }
                        if (StringUtils.isNotBlank(vo.getDefaultBid())) {
                            builder.setDefaultBid(vo.getDefaultBid());
                        }
                        builder.setState(vo.getState());
                        if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(vo.getShopId())) {
                            builder.setShopName(shopAuthMap.get(vo.getShopId()).getName());
                            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(vo.getMarketplaceId());
                            if( null != m){
                                builder.setCurrency(m.getCurrencyCode());
                            }
                        }
                        list.add(builder.build());
                    }
                    pageBuilder.addAllRows(list);
                    responseBuilder.setData(pageBuilder);
                }
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void checkItem(CheckItemRequest request, StreamObserver<CheckItemResponse> responseObserver) {
        log.info("分时策略提交校验 checkItem request:{}", request);
        CheckItemResponse.Builder responseBuilder = CheckItemResponse.newBuilder();
        if (!request.hasPuid() || !request.hasPageNo() || !request.hasPageSize()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            CheckItemParam param = new CheckItemParam();
            param.setPuid(request.getPuid());
            param.setShopId(request.getShopId());
            param.setPageNo(request.getPageNo());
            param.setPageSize(request.getPageSize());
            param.setItemType(request.getItemType());
            param.setAddWayType(request.getAddWayType());
            if (CollectionUtils.isNotEmpty(request.getAdStrategyStatusVoListList())) {
                List<ItemParam> paramList = Lists.newArrayList();
                for (CheckItem checkItem : request.getAdStrategyStatusVoListList()) {
                    ItemParam param1 = new ItemParam();
                    param1.setItemId(checkItem.getItemId());
                    param1.setTargetType(checkItem.getTargetType());
                    param1.setAdType(checkItem.getAdType());
                    paramList.add(param1);
                }
                param.setItemParams(paramList);
            }
            Result<Page<CheckItemResult>> result = advertiseStrategyStatusService.checkItem(param);
            if (result.getCode() == Result.SUCCESS) {
                responseBuilder.setCode(result.getCode());
                Page<CheckItemResult> page = result.getData();
                CheckItemPage.Builder pageBuilder = CheckItemPage.newBuilder();
                pageBuilder.setPageNo(page.getPageNo());
                pageBuilder.setPageSize(page.getPageSize());
                pageBuilder.setTotalPage(page.getTotalPage());
                pageBuilder.setTotalSize(page.getTotalSize());
                List<CheckItemResult> checkItemResultList = page.getRows();
                if (CollectionUtils.isNotEmpty(checkItemResultList)) {
                    List<CheckItemVo> list = Lists.newArrayListWithExpectedSize(checkItemResultList.size());
                    for (CheckItemResult vo : checkItemResultList) {
                        CheckItemVo.Builder builder = CheckItemVo.newBuilder();
                        if (StringUtils.isNotBlank(vo.getAdGroupId())) {
                            builder.setAdGroupId(vo.getAdGroupId());
                        }
                        if (StringUtils.isNotBlank(vo.getAdGroupName())) {
                            builder.setAdGroupName(vo.getAdGroupName());
                        }
                        if (StringUtils.isNotBlank(vo.getTargetId())) {
                            builder.setTargetId(vo.getTargetId());
                        }
                        if (StringUtils.isNotBlank(vo.getTargetName())) {
                            builder.setTargetName(vo.getTargetName());
                        }
                        if (vo.getTemplateId() != null) {
                            builder.setTemplateId(vo.getTemplateId());
                        }
                        if (StringUtils.isNotBlank(vo.getTemplateName())) {
                            builder.setTemplateName(vo.getTemplateName());
                        }
                        list.add(builder.build());
                    }
                    pageBuilder.addAllRows(list);
                    responseBuilder.setData(pageBuilder.build());
                }
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void selectItem(SelectItemRequest request, StreamObserver<SelectItemResponse> responseObserver) {
        log.info("分时策略提交校验 checkItem request:{}", request);
        SelectItemResponse.Builder responseBuilder = SelectItemResponse.newBuilder();
        ShopAuth shopAuth = slaveShopAuthDao.getByShopAuth(request.getSellerId(), request.getMarketplaceId());
        if (shopAuth == null) {
            responseBuilder.setCode(Result.SUCCESS);
            responseBuilder.setData(true);
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        Long taskId = request.hasTaskId() ? request.getTaskId() : null;
        AdvertiseStrategyStatus strategyStatus = advertiseStrategyStatusDao.getStatusByItemId(shopAuth.getPuid(), shopAuth.getId(), request.getItemId(), request.getItemType(), taskId);
        responseBuilder.setCode(Result.SUCCESS);
        if (strategyStatus == null) {
            responseBuilder.setData(false);
        } else {
            responseBuilder.setData(true);
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }


    @Override
    public void getStrategyRequest (GetStrategyRequestRequest request, StreamObserver<GetStrategyRequestResponse> responseObserver) {
        log.info("分时策略获取受控数据  request:{}", request);
        GetStrategyRequestResponse.Builder responseBuilder = GetStrategyRequestResponse.newBuilder();
        GetStrategyRequestResponse.GetStrategyRequestData.Builder dataBuilder = GetStrategyRequestResponse.GetStrategyRequestData.newBuilder();
        responseBuilder.setCode(Result.SUCCESS);
        GetStrategyRequestResponse.GetStrategyRequestData strategyRequest = null;
        try {
            strategyRequest = advertiseStrategyStatusService.getStrategyRequest(request);
        } catch (Exception e) {
            log.error("分时策略获取受控数据异常，request:{},", request, e);
        }
        if (strategyRequest != null) {
            responseBuilder.setData(strategyRequest);
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void selectTemplateId(BatchQueryTemplateIdRequest request, StreamObserver<BatchQueryTemplateIdResponse> responseObserver) {
        log.info("分时策略 selectTemplateId request:{}", request);
        BatchQueryTemplateIdResponse.Builder batchBuilder = BatchQueryTemplateIdResponse.newBuilder();

        //直接使用puid路由，taskId作为主键去查数据即可，和受控对象类型无关
        Map<Long, List<Long>> resultMap = new HashMap<>();
        request.getPuidTaskList().forEach(x -> {
            List<TaskTemplateIdVo> list = advertiseStrategyStatusDao.getTaskTemplateMapping(x.getPuid(), x.getTaskIdList());
            list.forEach(e -> resultMap.computeIfAbsent(e.getTemplateId(), k -> new ArrayList<>()).add(e.getTaskId()));
        });

        if (MapUtils.isNotEmpty(resultMap)) {
            List<BatchQueryTemplateIdResponse.QueryTemplateIdResponse> resultList = new ArrayList<>();
            resultMap.forEach((k, v) -> {
                BatchQueryTemplateIdResponse.QueryTemplateIdResponse.Builder builder = BatchQueryTemplateIdResponse.QueryTemplateIdResponse.newBuilder();
                builder.setTemplateId(k);
                builder.addAllTaskId(v);
                resultList.add(builder.build());
            });
            batchBuilder.addAllData(resultList);
        }

        batchBuilder.setCode(Result.SUCCESS);
        responseObserver.onNext(batchBuilder.build());
        responseObserver.onCompleted();
    }

}