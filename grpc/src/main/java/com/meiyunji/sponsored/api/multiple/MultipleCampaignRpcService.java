package com.meiyunji.sponsored.api.multiple;

import cn.hutool.json.JSONUtil;
import com.google.protobuf.Int64Value;
import com.meiyunji.sponsored.api.common.AdCommonRpcService;
import com.meiyunji.sponsored.api.export.AdExportRpcService;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.multiple.*;
import com.meiyunji.sponsored.rpc.vo.AdTagVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.cpc.vo.AdStrategyVo;
import com.meiyunji.sponsored.service.cpc.vo.CampaignPageParam;
import com.meiyunji.sponsored.service.enums.ShopTypeEnum;
import com.meiyunji.sponsored.service.multiple.campagin.service.IMultipleCampaignService;
import com.meiyunji.sponsored.service.multiple.campagin.vo.*;
import com.meiyunji.sponsored.service.multiple.campagin.vo.CampaignDataLog;
import com.meiyunji.sponsored.service.multiple.common.vo.CommonCompareReportRate;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

/**
 * 活动层级支持多店铺grpc接口
 *
 * @author: zzh
 * @create: 2024-11-21 10:05
 */
@GRpcService
@Slf4j
public class MultipleCampaignRpcService extends RPCMultipleCampaignServiceGrpc.RPCMultipleCampaignServiceImplBase {

    @Resource
    private IMultipleCampaignService multipleCampaignService;
    @Resource
    private IVcShopAuthDao vcShopAuthDao;

    /**
     * 多店铺广告活动列表页接口
     * 旧支持单店铺接口： {@link AdCommonRpcService#getAllCampaignData}
     */
    @Override
    public void getAllCampaignData(AllCampaignDataRequest request, StreamObserver<AllCampaignDataResponse> responseObserver) {
        log.info("广告活动列表页参数: {}", request.getParam());
        long t = Instant.now().toEpochMilli();
        // grpc请求参数转vo
        CampaignPageParam param = JSONUtil.toBean(request.getParam(), CampaignPageParam.class);
        param.setPuid(request.getPuid());
        param.setUid(request.getUid());
        param.setIsAdmin(request.getIsAdmin());
        log.info("广告活动列表页参数param: {}", JSONUtil.toJsonStr(param));
        // 获取分页列表
        Page<MultipleCampaignPageVo> page = multipleCampaignService.getAllCampaignData(param, false);
        // 构建grpc响应参数
        AllCampaignDataResponse response = buildResponse(page);
        log.info("广告管理--广告活动列表页接口调用花费时间 {}", (Instant.now().toEpochMilli() - t));
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    /**
     * 多店铺广告活动汇总接口
     * 旧支持单店铺接口： {@link AdCommonRpcService#getAllCampaignAggregateData}
     */
    @Override
    public void getAllCampaignAggregateData(AllCampaignDataRequest request, StreamObserver<AllCampaignAggregateDataResponse> responseObserver) {
        log.info("广告活动汇总参数: {}", request.getParam());
        long t = Instant.now().toEpochMilli();
        // grpc请求参数转vo
        CampaignPageParam param = JSONUtil.toBean(request.getParam(), CampaignPageParam.class);
        param.setPuid(request.getPuid());
        param.setUid(request.getUid());
        param.setIsAdmin(request.getIsAdmin());
        // 获取汇总数据
        MultipleCampaignAggregateVo aggregateData = multipleCampaignService.getAllCampaignAggregateData(param);
        // 构建grpc响应参数
        boolean isVc = false;
        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
            List<VcShopAuth> listByIdList = vcShopAuthDao.getListByIdList(param.getShopIdList());
            isVc = CollectionUtils.isNotEmpty(listByIdList);
        }

        AllCampaignAggregateDataResponse response = buildAggregateResponse(aggregateData, isVc);
        log.info("广告管理--广告活动汇总接口调用花费时间 {}", (Instant.now().toEpochMilli() - t));
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    /**
     * 多店铺广告活动导出接口
     * 旧支持单店铺接口： {@link AdExportRpcService#campaignData}
     */
    @Override
    public void exportCampaignDetails(AllCampaignDataRequest request, StreamObserver<ExportCampaignResponse> responseObserver){
        log.info("广告活动导出参数: {}", request.getParam());
        CampaignPageParam param = JSONUtil.toBean(request.getParam(), CampaignPageParam.class);
        param.setPuid(request.getPuid());
        param.setIsAdmin(request.getIsAdmin());
        param.setUid(request.getUid());
        param.setUuid(request.getUuid());
        Boolean success = multipleCampaignService.exportCampaignDetails(param);
        ExportCampaignResponse.Builder builder = ExportCampaignResponse.newBuilder();
        if(success){
            builder.setCode(Result.SUCCESS);
            builder.setData(request.getUuid());
        }else{
            builder.setCode(Result.ERROR);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 构建汇总grpc响应参数
     */
    private AllCampaignAggregateDataResponse buildAggregateResponse(MultipleCampaignAggregateVo aggregateData, boolean isVc) {
        AllCampaignAggregateDataResponse.Builder builder = AllCampaignAggregateDataResponse.newBuilder();
        builder.setCode(Result.SUCCESS);
        AllCampaignAggregateDataResponse.CampaignHomeVo.Builder voBuilder = AllCampaignAggregateDataResponse.CampaignHomeVo.newBuilder();
        voBuilder.setOverLimit(aggregateData.getOverLimit());
        if(aggregateData.getOverLimit()){
            builder.setData(voBuilder.build());
            return builder.build();
        }
        // 报告数据
        AdHomeAggregateDataRpcVo.Builder aggregateDataBuilder = AdHomeAggregateDataRpcVo.newBuilder();
        CommonCompareReportRate report = aggregateData.getReport();
        BeanUtils.copyProperties(report, aggregateDataBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(report));
        if(StringUtil.isNotEmpty(aggregateData.getDailyBudgetSum())){
            aggregateDataBuilder.setDailyBudget(aggregateData.getDailyBudgetSum());
        }
        aggregateDataBuilder.setCurrency(aggregateData.getCurrency());
        if (isVc) {
            aggregateDataBuilder.setAcots("-");
            aggregateDataBuilder.setCompareAcots("-");
            aggregateDataBuilder.setCompareAcotsRate("-");
            aggregateDataBuilder.setAsots("-");
            aggregateDataBuilder.setCompareAsots("-");
            aggregateDataBuilder.setCompareAsotsRate("-");
        }
        voBuilder.setAggregateDataVo(aggregateDataBuilder.build());
        // 日图表数据
        if (CollectionUtils.isNotEmpty(aggregateData.getDay())) {
            voBuilder.addAllDay(aggregateData.getDay());
        }
        // 周图表数据
        if (CollectionUtils.isNotEmpty(aggregateData.getWeek())) {
            voBuilder.addAllWeek(aggregateData.getWeek());
        }
        // 月图表数据
        if (CollectionUtils.isNotEmpty(aggregateData.getMonth())) {
            voBuilder.addAllMonth(aggregateData.getMonth());
        }
        builder.setData(voBuilder.build());
        return builder.build();
    }

    /**
     * 构建列表页grpc响应参数
     */
    private AllCampaignDataResponse buildResponse(Page<MultipleCampaignPageVo> page) {
        AllCampaignDataResponse.Builder builder = AllCampaignDataResponse.newBuilder();
        AllCampaignDataResponse.CampaignHomeVo.Builder homeBuilder = AllCampaignDataResponse.CampaignHomeVo.newBuilder();
        AllCampaignDataResponse.CampaignHomeVo.Page.Builder pageBuilder = AllCampaignDataResponse.CampaignHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(page.getPageNo());
        pageBuilder.setPageSize(page.getPageSize());
        pageBuilder.setTotalPage(page.getTotalPage());
        pageBuilder.setTotalSize(page.getTotalSize());
        List<AllCampaignDataResponse.CampaignHomeVo.Page.CampaignPageVo> voList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(page.getRows())) {
            for (MultipleCampaignPageVo row : page.getRows()) {
                AllCampaignDataResponse.CampaignHomeVo.Page.CampaignPageVo.Builder voBuilder = AllCampaignDataResponse.CampaignHomeVo.Page.CampaignPageVo.newBuilder();
                BeanUtils.copyProperties(row, voBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(row));
                // 对象赋值
                buildObject(row, voBuilder);
                voList.add(voBuilder.build());
            }
        }
        pageBuilder.addAllRows(voList);
        homeBuilder.setPage(pageBuilder.build());
        builder.setCode(Result.SUCCESS);
        builder.setData(homeBuilder.build());
        return builder.build();
    }

    /**
     * 标签赋值
     */
    private void buildObject(MultipleCampaignPageVo row, AllCampaignDataResponse.CampaignHomeVo.Page.CampaignPageVo.Builder voBuilder) {
        // 预算使用量信息
        if (row.getBudgetUsage() != null) {
            BudgetUsage budgetUsage = row.getBudgetUsage();
            AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.Builder budgetUsageBuilder = AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.newBuilder();
            BeanUtils.copyProperties(budgetUsage, budgetUsageBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(budgetUsage));
            voBuilder.setBudgetUsage(budgetUsageBuilder.build());
        }
        if (CollectionUtils.isNotEmpty(row.getBudgetUsageList())) {
            List<BudgetUsage> budgetUsageList = row.getBudgetUsageList();
            List<AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage> usageList = new ArrayList<>();
            for (BudgetUsage usage : budgetUsageList) {
                AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.Builder usageBuilder = AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.newBuilder();
                BeanUtils.copyProperties(usage, usageBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(usage));
                if(CollectionUtils.isNotEmpty(usage.getItem())){
                    List<AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.Item> itemList = new ArrayList<>();
                    for (BudgetUsage.Item item : usage.getItem()) {
                        AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.Item.Builder itemBuilder = AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.Item.newBuilder();
                        BeanUtils.copyProperties(item, itemBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(usage));
                        itemList.add(itemBuilder.build());
                    }
                    usageBuilder.addAllItem(itemList);
                }
                usageList.add(usageBuilder.build());
            }
            voBuilder.addAllBudgetUsages(usageList);
        }
        // 建议预算信息
        if (row.getMissBudget() != null) {
            MissBudgetRecommendation missBudget = row.getMissBudget();
            AllCampaignDataResponse.CampaignHomeVo.Page.MissBudgetRecommendation.Builder missBuilder = AllCampaignDataResponse.CampaignHomeVo.Page.MissBudgetRecommendation.newBuilder();
            BeanUtils.copyProperties(missBudget, missBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(missBudget));
            voBuilder.setMissBudget(missBuilder.build());
        }
        // 预算日志
        if (row.getBudgetLog() != null) {
            CampaignDataLog budgetLog = row.getBudgetLog();
            com.meiyunji.sponsored.rpc.multiple.CampaignDataLog.Builder budgeLogBuilder = com.meiyunji.sponsored.rpc.multiple.CampaignDataLog.newBuilder();
            BeanUtils.copyProperties(budgetLog, budgeLogBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(budgetLog));
            voBuilder.setBudgetLog(budgeLogBuilder.build());
        }
        // 广告位竞价搜索结果顶部（首页）日志
        if (row.getPlacementTopLog() != null) {
            CampaignDataLog topLog = row.getPlacementTopLog();
            com.meiyunji.sponsored.rpc.multiple.CampaignDataLog.Builder topLogBuilder = com.meiyunji.sponsored.rpc.multiple.CampaignDataLog.newBuilder();
            BeanUtils.copyProperties(topLog, topLogBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(topLog));
            voBuilder.setPlacementTopLog(topLogBuilder.build());
        }
        // 广告位竞价产品页面日志
        if (row.getPlacementProductPageLog() != null) {
            CampaignDataLog pageLog = row.getPlacementProductPageLog();
            com.meiyunji.sponsored.rpc.multiple.CampaignDataLog.Builder pageLogBuilder = com.meiyunji.sponsored.rpc.multiple.CampaignDataLog.newBuilder();
            BeanUtils.copyProperties(pageLog, pageLogBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(pageLog));
            voBuilder.setPlacementProductPageLog(pageLogBuilder.build());
        }
        // 广告位竞价产品页面日志
        if (row.getPlacementRestOfSearchLog() != null) {
            CampaignDataLog searchLog = row.getPlacementRestOfSearchLog();
            com.meiyunji.sponsored.rpc.multiple.CampaignDataLog.Builder pageLogBuilder = com.meiyunji.sponsored.rpc.multiple.CampaignDataLog.newBuilder();
            BeanUtils.copyProperties(searchLog, pageLogBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(searchLog));
            voBuilder.setPlacementRestOfSearchLog(pageLogBuilder.build());
        }
        // 广告位竞价企业购日志
        if (row.getPlacementSiteAmazonBusinessLog() != null) {
            CampaignDataLog searchLog = row.getPlacementSiteAmazonBusinessLog();
            com.meiyunji.sponsored.rpc.multiple.CampaignDataLog.Builder pageLogBuilder = com.meiyunji.sponsored.rpc.multiple.CampaignDataLog.newBuilder();
            BeanUtils.copyProperties(searchLog, pageLogBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(searchLog));
            voBuilder.setPlacementSiteAmazonBusinessLog(pageLogBuilder.build());
        }
        // 标签信息
        if(CollectionUtils.isNotEmpty(row.getAdTags())){
            List<AdTag> adTagList = row.getAdTags();
            List<AdTagVo> tagVoList = new ArrayList<>();
            for (AdTag adTag : adTagList) {
                AdTagVo.Builder adTagBuilder = AdTagVo.newBuilder();
                BeanUtils.copyProperties(adTag, adTagBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(adTag));
                adTagBuilder.setId(Int64Value.of(adTag.getId()));
                tagVoList.add(adTagBuilder.build());
            }
            voBuilder.addAllAdTags(tagVoList);
        }
        // 广告策略标签
        if(CollectionUtils.isNotEmpty(row.getStrategyList())){
            List<AllCampaignDataResponse.CampaignHomeVo.Page.AdStrategy> strategyList = new ArrayList<>();
            for (AdStrategyVo strategyVo : row.getStrategyList()) {
                AllCampaignDataResponse.CampaignHomeVo.Page.AdStrategy.Builder strategyBuilder = AllCampaignDataResponse.CampaignHomeVo.Page.AdStrategy.newBuilder();
                strategyBuilder.setAdStrategyType(strategyVo.getAdStrategyType());
                strategyBuilder.setStatus(strategyVo.getStatus());
                strategyList.add(strategyBuilder.build());
            }
            voBuilder.addAllAdStrategys(strategyList);
        }
    }
}
