package com.meiyunji.sponsored.api.common;

import cn.hutool.core.collection.CollectionUtil;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.grpc.common.*;
import com.meiyunji.sponsored.grpc.entry.AdReportChartRpcVoPb;
import com.meiyunji.sponsored.grpc.entry.AdReportDataRpcVoPb;
import com.meiyunji.sponsored.grpc.entry.ReportDateModelPb;
import com.meiyunji.sponsored.rpc.adAggregateHour.AggregateCampaignHourRequest;
import com.meiyunji.sponsored.rpc.adCommon.GetCampaignHourReportResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.nacos.AdManageLimitConfig;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.KeywordHourParam;
import com.meiyunji.sponsored.service.cpc.vo.TargetHourParam;
import com.meiyunji.sponsored.service.doris.dao.IDwsSaleProfitShopDayDao;
import com.meiyunji.sponsored.service.enums.SBCampaignCostTypeEnum;
import com.meiyunji.sponsored.service.log.dao.IAdManageOperationLogDao;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.qo.OperationLogQo;
import com.meiyunji.sponsored.service.multiple.common.utils.MultipleUtils;
import com.meiyunji.sponsored.service.multiple.common.utils.MultipleUtils;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AggregateIdsTemporary;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.CampaignAggregateHourParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.KeywordViewHourParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.TargetViewHourParam;
import com.meiyunji.sponsored.service.reportHour.service.IAmazonAdKeywordHourReportService;
import com.meiyunji.sponsored.service.reportHour.service.IAmazonAdTargetHourReportService;
import com.meiyunji.sponsored.service.reportHour.utils.ReportChartUtil;
import com.meiyunji.sponsored.service.reportHour.vo.AdKeywordAndTargetHourVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdKeywordTargetHourOfAdVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdKeywordTargetHourOfPlacementVo;
import com.meiyunji.sponsored.service.util.PbUtil;
import com.meiyunji.sponsored.util.ProtoBufUtil;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@GRpcService
@Slf4j
public class HourReportRpcService extends AdHourReportApiServiceGrpc.AdHourReportApiServiceImplBase {

    private final IAmazonAdKeywordHourReportService amazonAdKeywordHourReportService;
    private final IAmazonAdTargetHourReportService amazonAdTargetHourReportService;
    private final CpcPageIdsHandler cpcPageIdsHandler;
    private final CpcShopDataService cpcShopDataService;
    @Autowired
    private IAdManageOperationLogDao manageOperationLogDao;
    @Resource
    private IVcShopAuthDao vcShopAuthDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Resource
    private AdManageLimitConfig adManageLimitConfig;
    @Resource
    private IDwsSaleProfitShopDayDao dwsSaleProfitShopDayDao;

    public HourReportRpcService(
            IAmazonAdKeywordHourReportService amazonAdKeywordHourReportService,
            IAmazonAdTargetHourReportService amazonAdTargetHourReportService, CpcPageIdsHandler cpcPageIdsHandler,CpcShopDataService cpcShopDataService) {
        this.amazonAdKeywordHourReportService = amazonAdKeywordHourReportService;
        this.amazonAdTargetHourReportService = amazonAdTargetHourReportService;
        this.cpcPageIdsHandler = cpcPageIdsHandler;
        this.cpcShopDataService = cpcShopDataService;
    }

    /**
     * 查询关键词小时级数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getKeywordHourReport(
            GetKeywordHourReportRequestPb.GetKeywordHourReportRequest request,
            StreamObserver<GetKeywordHourReportResponsePb.GetKeywordHourReportResponse> responseObserver) {
        try {
            GetKeywordHourReportResponsePb.GetKeywordHourReportResponse.Builder builder =
                    GetKeywordHourReportResponsePb.GetKeywordHourReportResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()
                    || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 &&
                    (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr()))) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                VcShopAuth byIdAndPuid = vcShopAuthDao.getByIdAndPuid(request.getShopId().getValue(), request.getPuid().getValue());
                boolean isVc = byIdAndPuid != null;
                KeywordHourParam param = new KeywordHourParam();
                BeanUtils.copyProperties(request, param);
                param.setKeywordId(request.getKeywordId());
                param.setWeeks(request.getWeeks());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setEndDateCompare(request.getEndDateCompareStr());
                param.setStartDateCompare(request.getStartDateCompareStr());
                param.setOrderField(request.getOrderField());
                param.setOrderType(request.getOrderType());
                if (request.hasIsCompare()) {
                    param.setIsCompare(request.getIsCompare().getValue());
                }
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setPageNo(request.getPageNo().getValue());
                param.setPageSize(request.getPageSize().getValue());
                param.setUid(request.getUid().getValue());
                param.setFindType(request.getFindType());
                param.setFindValue(request.getFindValue());
                param.setType(request.getType());
                GetKeywordHourReportResponsePb.GetKeywordHourReportResponse.HourReport.Builder reportBuilder =
                        GetKeywordHourReportResponsePb.GetKeywordHourReportResponse.HourReport.newBuilder();
                List<AdKeywordAndTargetHourVo> list = new ArrayList<>();
                List<AdKeywordAndTargetHourVo> compares = new ArrayList<>();
                if (hasHourlyData(request)) {
                    if ("SP".equalsIgnoreCase(request.getType())) {
                        list = amazonAdKeywordHourReportService.getHourList(param.getPuid(), param);
                    } else {
                        list = amazonAdKeywordHourReportService.getSbHourList(param.getPuid(), param);
                    }
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.DAILY) {
                    list = amazonAdKeywordHourReportService.getDailyList(param.getPuid(), request.getType(), param, compares);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.WEEKLY) {
                    list = amazonAdKeywordHourReportService.getWeeklyList(param.getPuid(), request.getType(), param, compares);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.MONTHLY) {
                    list = amazonAdKeywordHourReportService.getMonthlyList(param.getPuid(), request.getType(), param, compares);
                }
                if (CollectionUtils.isNotEmpty(list)) {

                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdKeywordAndTargetHourVo.class);
                    if (isSorted) {
                        PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
                    }
                }
                //取店铺销售额
                BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getShopId(), param.getStartDate().replace("-",""), param.getEndDate().replace("-",""));
                reportBuilder.addAllList(list.stream().filter(Objects::nonNull).map(e -> PbUtil.toHourReportPb(e, shopSalesByDate, isVc)).collect(Collectors.toList()));
                AdKeywordAndTargetHourVo summaryVO = summaryTargetHourVo(list, shopSalesByDate);
                if (Integer.valueOf(1).equals(param.getIsCompare()) && !hasHourlyData(request)) {
                    AdKeywordAndTargetHourVo compareVO = summaryTargetHourVo(compares);
                    summaryVO.compareDataSet(compareVO);
                }
                reportBuilder.setSummary(PbUtil.toHourReportPb(summaryVO, shopSalesByDate, isVc));
                reportBuilder.addAllChart(ReportChartUtil.getHourChartData(list, false));
                //对比数据,chart图数据
                if (param.getIsCompare() != null && param.getIsCompare() == 1) {
                    List<AdKeywordAndTargetHourVo> compareHourVos = list.stream().map(item-> {
                        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                        vo.setLabel(item.getLabel());
                        vo.setClicks(item.getClicksCompare());
                        vo.setImpressions(item.getImpressionsCompare());
                        vo.setAdSale(item.getAdSaleCompare());
                        vo.setAdCost(item.getAdCostCompare());
                        vo.setAdOrderNum(item.getAdOrderNumCompare());
                        vo.setAdSaleNum(item.getAdSaleNumCompare());
                        vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                        vo.setAcos(item.getAcosCompare());
                        vo.setRoas(item.getRoasCompare());
                        vo.setCtr(item.getCtrCompare());
                        vo.setCvr(item.getCvrCompare());
                        return vo;
                    }).collect(Collectors.toList());
                    reportBuilder.addAllChart(ReportChartUtil.getHourChartData(compareHourVos, true));
                }

                builder.setCode(Int32Value.of(Result.SUCCESS));
                builder.setData(reportBuilder.build());
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    /**
     * 查询竞价关键词小时级数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getBidKeywordHourReport(
            GetBidKeywordHourReportRequestPb.GetBidKeywordHourReportRequest request,
            StreamObserver<GetBidKeywordHourReportResponsePb.GetBidKeywordHourReportResponse> responseObserver) {
        try {
            GetBidKeywordHourReportResponsePb.GetBidKeywordHourReportResponse.Builder builder =
                    GetBidKeywordHourReportResponsePb.GetBidKeywordHourReportResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                KeywordHourParam param = new KeywordHourParam();
                BeanUtils.copyProperties(request, param);
                param.setKeywordId(request.getKeywordId());
                param.setWeeks(request.getWeeks());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
//                param.setEndDateCompare(request.getEndDateCompareStr());
//                param.setStartDateCompare(request.getStartDateCompareStr());
                param.setOrderField(request.getOrderField());
                param.setOrderType(request.getOrderType());
//                if (request.hasIsCompare()) {
//                    param.setIsCompare(request.getIsCompare().getValue());
//                }
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setPageNo(request.getPageNo().getValue());
                param.setPageSize(request.getPageSize().getValue());
                param.setUid(request.getUid().getValue());
                if (request.hasFindType()) {
                    param.setFindType(request.getFindType());
                }
                if (request.hasFindValue()) {
                    param.setFindValue(request.getFindValue());
                }

                OperationLogQo operationLogQo = new OperationLogQo();
                operationLogQo.setPuid(request.getPuid().getValue());
                operationLogQo.setShopId(request.getShopId().getValue());
                operationLogQo.setSiteStart(request.getStartDateStr());
                operationLogQo.setSiteEnd(request.getEndDateStr());
                operationLogQo.setChangeType("BID_AMOUNT");
                operationLogQo.setAdType(request.getAdType().toLowerCase());
                operationLogQo.setFrom("amazon");
                operationLogQo.setTargetId(request.getKeywordId());
                List<AdManageOperationLog> adManageOperationLogList = manageOperationLogDao.getAllListAdLogs(operationLogQo);

                GetBidKeywordHourReportResponsePb.GetBidKeywordHourReportResponse.HourReport.Builder reportBuilder =
                        GetBidKeywordHourReportResponsePb.GetBidKeywordHourReportResponse.HourReport.newBuilder();
                List<AdKeywordAndTargetHourVo> list = new ArrayList<>();

//                if (hasHourlyData(request)) {
                list = amazonAdKeywordHourReportService.getBidHourList(param.getPuid(), param);
//                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.DAILY) {
//                    list = amazonAdKeywordHourReportService.getDailyList(param.getPuid(), request.getAdType(), param);
//                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.WEEKLY) {
//                    list = amazonAdKeywordHourReportService.getWeeklyList(param.getPuid(), request.getAdType(), param);
//                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.MONTHLY) {
//                    list = amazonAdKeywordHourReportService.getMonthlyList(param.getPuid(), request.getAdType(), param);
//                }

                List<AdReportChartRpcVoPb.AdReportChartRpcVo> bidChartData = null;

                if (null != adManageOperationLogList && adManageOperationLogList.size() > 0) {
                    bidChartData = ReportChartUtil.getBidChartData(adManageOperationLogList, request.getStartDateStr(), false);
                }


                if (CollectionUtils.isNotEmpty(list)) {
                    //填充数据
                    if(CollectionUtils.isNotEmpty(bidChartData) && CollectionUtils.isNotEmpty(bidChartData.get(0).getRecordsList()) ) {
                        List<AdReportChartRpcVoPb.ChartRecord> chartRecordList = bidChartData.get(0).getRecordsList().stream().filter(e-> StringUtils.isNotBlank(e.getValue()) && !e.getValue().equals("null")).sorted(Comparator.comparingLong(e -> Long.valueOf(e.getDate().substring(0, 16).replace("-", "").replace(":", "").replace(" ", "")))).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(chartRecordList)) {
                            Map<String, List<AdReportChartRpcVoPb.ChartRecord>> chartRecord = new LinkedHashMap();
                            for (AdReportChartRpcVoPb.ChartRecord record : chartRecordList) {
                                String key = record.getDate().substring(0, 13);
                                List<AdReportChartRpcVoPb.ChartRecord> records = chartRecord.getOrDefault(key, new ArrayList<AdReportChartRpcVoPb.ChartRecord>());
                                records.add(record);
                                chartRecord.put(key, records);
                            }
                            String marketplaceId = adManageOperationLogList.get(0).getMarketplaceId();
                            String currencyUnit = PbUtil.getCurrencyUnit(marketplaceId);
                            String bid = chartRecordList.get(0).getValue();
                            LocalDateTime startDateTime = LocalDateTime.of(LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE), LocalTime.MIN);
                            LocalDateTime endDateTime = LocalDateTime.of(LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE), LocalTime.MAX);


                            Map<String, AdKeywordAndTargetHourVo> stringAdKeywordAndTargetHourVoMap = list.stream().collect(Collectors.toMap(AdKeywordAndTargetHourVo::getLabel, Function.identity(), (e1, e2) -> e2));
                            while (startDateTime.compareTo(endDateTime) < 1) {
                                AdKeywordAndTargetHourVo adKeywordAndTargetHourVo = stringAdKeywordAndTargetHourVoMap.get(startDateTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME).replace("T", " ").substring(0, 13));
                                if (adKeywordAndTargetHourVo != null) {
                                    List<AdReportChartRpcVoPb.ChartRecord> chartRecords = chartRecord.get(adKeywordAndTargetHourVo.getLabel());
                                    if (CollectionUtils.isNotEmpty(chartRecords)) {
                                        if (chartRecords.size() > 1) {
                                            List<String> collect = chartRecords.stream().map(AdReportChartRpcVoPb.ChartRecord::getValue).collect(Collectors.toList());
                                            collect.add(bid);
                                            Map<String, List<String>> stringListMap = collect.stream().collect(Collectors.groupingBy(Function.identity()));
                                            if (stringListMap.size() == 1) {
                                                adKeywordAndTargetHourVo.setBidAdjust(currencyUnit + bid);
                                                adKeywordAndTargetHourVo.setBidAdjustMax(new BigDecimal(bid));
                                            } else if (stringListMap.size() == 2) {
                                                String max = Collections.max(collect);
                                                String min = Collections.min(collect);
                                                if (!bid.equals(collect.get(collect.size() - 2))) {
                                                    adKeywordAndTargetHourVo.setBidAdjust(currencyUnit + max + "~" + currencyUnit + min);
                                                    adKeywordAndTargetHourVo.setBidAdjustMax(new BigDecimal(collect.get(collect.size() - 2)));
                                                } else {
                                                    adKeywordAndTargetHourVo.setBidAdjust(currencyUnit + bid + "→" + currencyUnit + collect.get(collect.size() - 2));
                                                    adKeywordAndTargetHourVo.setBidAdjustMax(new BigDecimal(collect.get(collect.size() - 2)));
                                                }
                                            } else {
                                                String max = Collections.max(collect);
                                                adKeywordAndTargetHourVo.setBidAdjust(currencyUnit + Collections.min(collect) + "~" + currencyUnit + max);
                                                adKeywordAndTargetHourVo.setBidAdjustMax(new BigDecimal(max));
                                            }

                                            bid = chartRecords.get(chartRecords.size() - 1).getValue();
                                        } else {
                                            if (bid.equals(chartRecords.get(0).getValue())) {
                                                adKeywordAndTargetHourVo.setBidAdjust(currencyUnit + bid);
                                                adKeywordAndTargetHourVo.setBidAdjustMax(new BigDecimal(bid));
                                            } else {
                                                adKeywordAndTargetHourVo.setBidAdjust(currencyUnit + bid + "→" + currencyUnit + chartRecords.get(0).getValue());
                                                adKeywordAndTargetHourVo.setBidAdjustMax(new BigDecimal(chartRecords.get(0).getValue()));
                                                bid = chartRecords.get(0).getValue();
                                            }
                                        }
                                    } else {
                                        adKeywordAndTargetHourVo.setBidAdjust(currencyUnit + bid);
                                        adKeywordAndTargetHourVo.setBidAdjustMax(new BigDecimal(bid));
                                    }
                                } else {
                                    List<AdReportChartRpcVoPb.ChartRecord> chartRecords = chartRecord.get(startDateTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME).replace("T", " ").substring(0, 13));
                                    if (CollectionUtils.isNotEmpty(chartRecords)) {
                                        AdReportChartRpcVoPb.ChartRecord record = chartRecords.get(chartRecords.size() - 1);
                                        bid = record.getValue();
                                    }
                                }
                                startDateTime = startDateTime.plusHours(1);
                            }
                        }
                    }
                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdKeywordAndTargetHourVo.class);
                    if (isSorted) {
                        String orderField = param.getOrderField();
                        if ("date".equalsIgnoreCase(param.getOrderField())) {
                            orderField = "label";
                        } else if ("bidAdjust".equalsIgnoreCase(param.getOrderField())) {
                            orderField = "bidAdjustMax";
                        }
                        PageUtil.sortedByOrderField(list, orderField, param.getOrderType());
                    }
                }
                reportBuilder.addAllList(list.stream().filter(Objects::nonNull)
                        .map(PbUtil::toHourReportPb).collect(Collectors.toList()));
                reportBuilder.setSummary(PbUtil.toHourReportPb(summaryTargetHourVo(list)));
                List<AdReportChartRpcVoPb.AdReportChartRpcVo> adReportChartRpcVoList = ReportChartUtil.getHourChartData(list, false);

                if (bidChartData != null) {
                    adReportChartRpcVoList.addAll(bidChartData);
                }
                reportBuilder.addAllChart(adReportChartRpcVoList);
                //对比数据,chart图数据
//                if (param.getIsCompare() != null && param.getIsCompare() == 1) {
//                    List<AdKeywordAndTargetHourVo> compareHourVos = list.stream().map(item-> {
//                        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
//                        vo.setLabel(item.getLabel());
//                        vo.setClicks(item.getClicksCompare());
//                        vo.setImpressions(item.getImpressionsCompare());
//                        vo.setAdSale(item.getAdSaleCompare());
//                        vo.setAdCost(item.getAdCostCompare());
//                        vo.setAdOrderNum(item.getAdOrderNumCompare());
//                        return vo;
//                    }).collect(Collectors.toList());
//                    List<AdReportChartRpcVoPb.AdReportChartRpcVo> adReportChartRpcVoList1 = ReportChartUtil.getHourChartData(compareHourVos, true);
//                    adReportChartRpcVoList1.addAll(ReportChartUtil.getBidChartData(adManageOperationLogList, true));
//                    reportBuilder.addAllChart(adReportChartRpcVoList1);
//                }

                builder.setCode(Int32Value.of(Result.SUCCESS));
                builder.setData(reportBuilder.build());
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    private boolean hasHourlyData(GetKeywordHourReportRequestPb.GetKeywordHourReportRequest request) {
        return request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY;
    }

    /**
     * 查询广告产品维度关键词小时级数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getKeywordHourReportOfAd(
            GetKeywordHourReportOfAdRequestPb.GetKeywordHourReportOfAdRequest request,
            StreamObserver<GetKeywordHourReportOfAdResponsePb.GetKeywordHourReportOfAdResponse> responseObserver) {
        try {
            GetKeywordHourReportOfAdResponsePb.GetKeywordHourReportOfAdResponse.Builder builder =
                    GetKeywordHourReportOfAdResponsePb.GetKeywordHourReportOfAdResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                KeywordHourParam param = new KeywordHourParam();
                BeanUtils.copyProperties(request, param);
                param.setKeywordId(request.getKeywordId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setOrderField(request.getOrderField());
                param.setOrderType(request.getOrderType());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setPageNo(request.getPageNo().getValue());
                param.setPageSize(request.getPageSize().getValue());
                param.setUid(request.getUid().getValue());
                param.setFindType(request.getFindType());
                param.setFindValue(request.getFindValue());
                List<AdKeywordTargetHourOfAdVo> list =
                        amazonAdKeywordHourReportService.getListOfAd(param.getPuid(), param);
                if (CollectionUtils.isNotEmpty(list)) {
                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdKeywordAndTargetHourVo.class);
                    if (isSorted) {
                        PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
                    }

                    GetKeywordHourReportOfAdResponsePb.GetKeywordHourReportOfAdResponse.HourReport.Builder reportBuilder =
                            GetKeywordHourReportOfAdResponsePb.GetKeywordHourReportOfAdResponse.HourReport.newBuilder();
                    reportBuilder.addAllList(list.stream().filter(Objects::nonNull)
                            .map(PbUtil::toKeywordPb).collect(Collectors.toList()));
                    reportBuilder.setSummary(PbUtil.toKeywordPb(summaryKeywordOfAdVo(list)));
                    builder.setCode(Int32Value.of(Result.SUCCESS));
                    builder.setData(reportBuilder.build());
                }
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }


    /**
     * 查询广告位维度关键词小时级数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getKeywordHourReportOfPlacement(
            GetKeywordHourReportOfPlacementRequestPb.GetKeywordHourReportOfPlacementRequest request,
            StreamObserver<GetKeywordHourReportOfPlacementResponsePb.GetKeywordHourReportOfPlacementResponse> responseObserver) {
        try {
            GetKeywordHourReportOfPlacementResponsePb.GetKeywordHourReportOfPlacementResponse.Builder builder =
                    GetKeywordHourReportOfPlacementResponsePb.GetKeywordHourReportOfPlacementResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                KeywordHourParam param = new KeywordHourParam();
                BeanUtils.copyProperties(request, param);
                param.setKeywordId(request.getKeywordId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setOrderField(request.getOrderField());
                param.setOrderType(request.getOrderType());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setPageNo(request.getPageNo().getValue());
                param.setPageSize(request.getPageSize().getValue());
                param.setUid(request.getUid().getValue());
                param.setFindType(request.getFindType());
                param.setFindValue(request.getFindValue());
                List<AdKeywordTargetHourOfPlacementVo> list =
                        amazonAdKeywordHourReportService.getListOfPlacement(param.getPuid(), param);
                if (CollectionUtils.isNotEmpty(list)) {
                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdKeywordAndTargetHourVo.class);
                    if (isSorted) {
                        PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
                    } else {
                        list = list.stream().sorted(Comparator.comparingInt(e -> {
                            if ("产品页面".equalsIgnoreCase(e.getPlacement())) {
                                return 3;
                            } else if ("搜索结果顶部(首页)".equalsIgnoreCase(e.getPlacement())) {
                                return 1;
                            } else if ("搜索结果的其余位置".equalsIgnoreCase(e.getPlacement())) {
                                return 2;
                            } else {
                                return 4;
                            }
                        })).collect(Collectors.toList());
                    }

                    GetKeywordHourReportOfPlacementResponsePb.GetKeywordHourReportOfPlacementResponse.HourReport.Builder reportBuilder =
                            GetKeywordHourReportOfPlacementResponsePb.GetKeywordHourReportOfPlacementResponse.HourReport.newBuilder();
                    reportBuilder.addAllList(list.stream().filter(Objects::nonNull)
                            .map(PbUtil::toKeywordPb).collect(Collectors.toList()));
                    reportBuilder.setSummary(PbUtil.toKeywordPb(summaryKeywordOfPlacementVo(list)));
                    builder.setCode(Int32Value.of(Result.SUCCESS));
                    builder.setData(reportBuilder.build());
                }
            }

            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }


    /**
     * 查询投放小时级数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getTargetHourReport(
            GetTargetHourReportRequestPb.GetTargetHourReportRequest request,
            StreamObserver<GetTargetHourReportResponsePb.GetTargetHourReportResponse> responseObserver) {
        try {
            GetTargetHourReportResponsePb.GetTargetHourReportResponse.Builder builder =
                    GetTargetHourReportResponsePb.GetTargetHourReportResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()
                    || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 &&
                    (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr()))) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                VcShopAuth byIdAndPuid = vcShopAuthDao.getByIdAndPuid(request.getShopId().getValue(), request.getPuid().getValue());
                boolean isVc = byIdAndPuid != null;
                TargetHourParam param = new TargetHourParam();
                BeanUtils.copyProperties(request, param);
                param.setTargetId(request.getTargetId());
                param.setWeeks(request.getWeeks());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setEndDateCompare(request.getEndDateCompareStr());
                param.setStartDateCompare(request.getStartDateCompareStr());
                param.setOrderField(request.getOrderField());
                param.setOrderType(request.getOrderType());
                if (request.hasIsCompare()) {
                    param.setIsCompare(request.getIsCompare().getValue());
                }
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setPageNo(request.getPageNo().getValue());
                param.setPageSize(request.getPageSize().getValue());
                param.setUid(request.getUid().getValue());
                param.setFindType(request.getFindType());
                param.setFindValue(request.getFindValue());
                param.setType(request.getType());
                GetTargetHourReportResponsePb.GetTargetHourReportResponse.HourReport.Builder reportBuilder =
                        GetTargetHourReportResponsePb.GetTargetHourReportResponse.HourReport.newBuilder();

                List<AdKeywordAndTargetHourVo> list = new ArrayList<>();
                List<AdKeywordAndTargetHourVo> compares = new ArrayList<>();

                if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY) {
                    if ("SP".equalsIgnoreCase(request.getType())) {
                        list = amazonAdTargetHourReportService.getList(param.getPuid(), param);
                    } else {
                        list = amazonAdTargetHourReportService.getSbAndSdHourList(param.getPuid(), param);
                    }
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.DAILY) {
                    list = amazonAdTargetHourReportService.getDailyList(param.getPuid(), request.getType(), param, compares);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.WEEKLY) {
                    list = amazonAdTargetHourReportService.getWeeklyList(param.getPuid(), request.getType(), param, compares);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.MONTHLY) {
                    list = amazonAdTargetHourReportService.getMonthlyList(param.getPuid(), request.getType(), param, compares);
                }

                if (CollectionUtils.isNotEmpty(list)) {
                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdKeywordAndTargetHourVo.class);
                    if (isSorted) {
                        PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
                    }
                }
                //取店铺销售额
                BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getShopId(), param.getStartDate().replace("-",""), param.getEndDate().replace("-",""));
                reportBuilder.addAllList(list.stream().filter(Objects::nonNull).map(e -> {
                    AdReportDataRpcVoPb.AdReportDataRpcVo hourReportPb = PbUtil.toHourReportPb(e, shopSalesByDate, isVc);
                    if(!SBCampaignCostTypeEnum.VCPM.getCode().equals(e.getCostType())){
                        hourReportPb = hourReportPb.toBuilder().setVcpm("-").build();
                    }
                    return hourReportPb;
                }).collect(Collectors.toList()));
                AdKeywordAndTargetHourVo summaryVO = summaryTargetHourVo(list, shopSalesByDate);
                if (Integer.valueOf(1).equals(param.getIsCompare()) && request.getDateModel() != ReportDateModelPb.ReportDateModel.HOURLY) {
                    AdKeywordAndTargetHourVo compareVO = summaryTargetHourVo(compares);
                    summaryVO.compareDataSet(compareVO);
                }
                reportBuilder.setSummary(PbUtil.toHourReportPb(summaryVO, shopSalesByDate, isVc));
                reportBuilder.addAllChart(ReportChartUtil.getHourChartData(list, false));

                if (param.getIsCompare() != null && param.getIsCompare() == 1) {
                    List<AdKeywordAndTargetHourVo> compareHourVos = list.stream().map(item-> {
                        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                        vo.setLabel(item.getLabel());
                        vo.setClicks(item.getClicksCompare());
                        vo.setImpressions(item.getImpressionsCompare());
                        vo.setAdSale(item.getAdSaleCompare());
                        vo.setAdCost(item.getAdCostCompare());
                        vo.setAdOrderNum(item.getAdOrderNumCompare());
                        vo.setAdSaleNum(item.getAdSaleNumCompare());
                        vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                        vo.setAcos(item.getAcosCompare());
                        vo.setRoas(item.getRoasCompare());
                        vo.setCtr(item.getCtrCompare());
                        vo.setCvr(item.getCvrCompare());
                        return vo;
                    }).collect(Collectors.toList());
                    reportBuilder.addAllChart(ReportChartUtil.getHourChartData(compareHourVos, true));
                }

                builder.setCode(Int32Value.of(Result.SUCCESS));
                builder.setData(reportBuilder.build());
            }

            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    /**
     * 查询投放竞价小时级数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getBidTargetHourReport(
            GetBidTargetHourReportRequestPb.GetBidTargetHourReportRequest request,
            StreamObserver<GetBidTargetHourReportResponsePb.GetBidTargetHourReportResponse> responseObserver) {
        try {
            GetBidTargetHourReportResponsePb.GetBidTargetHourReportResponse.Builder builder =
                    GetBidTargetHourReportResponsePb.GetBidTargetHourReportResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                TargetHourParam param = new TargetHourParam();
                BeanUtils.copyProperties(request, param);
                param.setTargetId(request.getTargetId());
                param.setWeeks(request.getWeeks());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setOrderField(request.getOrderField());
                param.setOrderType(request.getOrderType());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setPageNo(request.getPageNo().getValue());
                param.setPageSize(request.getPageSize().getValue());
                param.setUid(request.getUid().getValue());
                if (request.hasFindType()) {
                    param.setFindType(request.getFindType());
                }
                if (request.hasFindValue()) {
                    param.setFindValue(request.getFindValue());
                }
                GetBidTargetHourReportResponsePb.GetBidTargetHourReportResponse.HourReport.Builder reportBuilder =
                        GetBidTargetHourReportResponsePb.GetBidTargetHourReportResponse.HourReport.newBuilder();
                List<AdKeywordAndTargetHourVo> list = amazonAdTargetHourReportService.getBidList(param.getPuid(), param);
                OperationLogQo operationLogQo = new OperationLogQo();
                operationLogQo.setPuid(request.getPuid().getValue());
                operationLogQo.setShopId(request.getShopId().getValue());
                operationLogQo.setSiteStart(request.getStartDateStr());
                operationLogQo.setSiteEnd(request.getEndDateStr());
                operationLogQo.setChangeType("BID_AMOUNT");
                operationLogQo.setAdType(request.getAdType().toLowerCase());
                operationLogQo.setFrom("amazon");
                operationLogQo.setTargetId(request.getTargetId());
                List<AdManageOperationLog> adManageOperationLogList = manageOperationLogDao.getAllListAdLogs(operationLogQo);

                List<AdReportChartRpcVoPb.AdReportChartRpcVo> bidChartData = null;

                if (null != adManageOperationLogList && adManageOperationLogList.size() > 0) {
                    bidChartData = ReportChartUtil.getBidChartData(adManageOperationLogList, request.getStartDateStr(), false);
                }

                if (CollectionUtils.isNotEmpty(list)) {
                    //填充数据
                    if (CollectionUtils.isNotEmpty(bidChartData) && CollectionUtils.isNotEmpty(bidChartData.get(0).getRecordsList())) {
                        List<AdReportChartRpcVoPb.ChartRecord> chartRecordList = bidChartData.get(0).getRecordsList().stream().filter(e-> StringUtils.isNotBlank(e.getValue()) && !e.getValue().equals("null")).sorted(Comparator.comparingLong(e -> Long.valueOf(e.getDate().substring(0, 16).replace("-", "").replace(":", "").replace(" ", "")))).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(chartRecordList)) {
                            Map<String, List<AdReportChartRpcVoPb.ChartRecord>> chartRecord = new LinkedHashMap();
                            for (AdReportChartRpcVoPb.ChartRecord record : chartRecordList) {
                                String key = record.getDate().substring(0, 13);
                                List<AdReportChartRpcVoPb.ChartRecord> records = chartRecord.getOrDefault(key, new ArrayList<AdReportChartRpcVoPb.ChartRecord>());
                                records.add(record);
                                chartRecord.put(key, records);
                            }
                            String currencyUnit = PbUtil.getCurrencyUnit(adManageOperationLogList.get(0).getMarketplaceId());
                            String bid = chartRecordList.get(0).getValue();
                            LocalDateTime startDateTime = LocalDateTime.of(LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE), LocalTime.MIN);
                            LocalDateTime endDateTime = LocalDateTime.of(LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE), LocalTime.MAX);


                            Map<String, AdKeywordAndTargetHourVo> stringAdKeywordAndTargetHourVoMap = list.stream().collect(Collectors.toMap(AdKeywordAndTargetHourVo::getLabel, Function.identity(), (e1, e2) -> e2));
                            while (startDateTime.compareTo(endDateTime) < 1) {
                                AdKeywordAndTargetHourVo adKeywordAndTargetHourVo = stringAdKeywordAndTargetHourVoMap.get(startDateTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME).replace("T", " ").substring(0, 13));
                                if (adKeywordAndTargetHourVo != null) {
                                    List<AdReportChartRpcVoPb.ChartRecord> chartRecords = chartRecord.get(adKeywordAndTargetHourVo.getLabel());
                                    if (CollectionUtils.isNotEmpty(chartRecords)) {
                                        if (chartRecords.size() > 1) {
                                            List<String> collect = chartRecords.stream().map(AdReportChartRpcVoPb.ChartRecord::getValue).collect(Collectors.toList());
                                            collect.add(bid);
                                            Map<String, List<String>> stringListMap = collect.stream().collect(Collectors.groupingBy(Function.identity()));
                                            if (stringListMap.size() == 1) {
                                                adKeywordAndTargetHourVo.setBidAdjust(currencyUnit + bid);
                                                adKeywordAndTargetHourVo.setBidAdjustMax(new BigDecimal(bid));
                                            } else if (stringListMap.size() == 2) {
                                                String max = Collections.max(collect);
                                                String min = Collections.min(collect);
                                                if (!bid.equals(collect.get(collect.size() - 2))) {
                                                    adKeywordAndTargetHourVo.setBidAdjust(currencyUnit + max + "~" + currencyUnit + min);
                                                    adKeywordAndTargetHourVo.setBidAdjustMax(new BigDecimal(collect.get(collect.size() - 2)));
                                                } else {
                                                    adKeywordAndTargetHourVo.setBidAdjust(currencyUnit + bid + "→" + currencyUnit + collect.get(collect.size() - 2));
                                                    adKeywordAndTargetHourVo.setBidAdjustMax(new BigDecimal(collect.get(collect.size() - 2)));
                                                }

                                            } else {
                                                String max = Collections.max(collect);
                                                adKeywordAndTargetHourVo.setBidAdjust(currencyUnit + Collections.min(collect) + "~" + currencyUnit + max);
                                                adKeywordAndTargetHourVo.setBidAdjustMax(new BigDecimal(max));
                                            }

                                            bid = chartRecords.get(chartRecords.size() - 1).getValue();
                                        } else {
                                            if (bid.equals(chartRecords.get(0).getValue())) {
                                                adKeywordAndTargetHourVo.setBidAdjust(currencyUnit + bid);
                                                adKeywordAndTargetHourVo.setBidAdjustMax(StringUtils.isBlank(bid) ? BigDecimal.ZERO : new BigDecimal(bid));
                                            } else {
                                                adKeywordAndTargetHourVo.setBidAdjust(currencyUnit + bid + "→" + currencyUnit + chartRecords.get(0).getValue());
                                                adKeywordAndTargetHourVo.setBidAdjustMax(new BigDecimal(chartRecords.get(0).getValue()));
                                                bid = chartRecords.get(0).getValue();
                                            }
                                        }
                                    } else {
                                        adKeywordAndTargetHourVo.setBidAdjust(currencyUnit + bid);
                                        adKeywordAndTargetHourVo.setBidAdjustMax(StringUtils.isBlank(bid) ? BigDecimal.ZERO : new BigDecimal(bid));
                                    }
                                } else {
                                    List<AdReportChartRpcVoPb.ChartRecord> chartRecords = chartRecord.get(startDateTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME).replace("T", " ").substring(0, 13));
                                    if (CollectionUtils.isNotEmpty(chartRecords)) {
                                        AdReportChartRpcVoPb.ChartRecord record = chartRecords.get(chartRecords.size() - 1);
                                        bid = record.getValue();
                                    }
                                }
                                startDateTime = startDateTime.plusHours(1);
                            }
                        }
                    }

                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdKeywordAndTargetHourVo.class);
                    if (isSorted) {
                        String orderField = param.getOrderField();
                        if ("date".equalsIgnoreCase(param.getOrderField())) {
                            orderField = "label";
                        } else if ("bidAdjust".equalsIgnoreCase(param.getOrderField())) {
                            orderField = "bidAdjustMax";
                        }

                        PageUtil.sortedByOrderField(list, orderField, param.getOrderType());
                    }
                }
                reportBuilder.addAllList(list.stream().filter(Objects::nonNull)
                        .map(PbUtil::toHourReportPb).collect(Collectors.toList()));
                reportBuilder.setSummary(PbUtil.toHourReportPb(summaryTargetHourVo(list)));
                List<AdReportChartRpcVoPb.AdReportChartRpcVo> adReportChartRpcVoList = ReportChartUtil.getHourChartData(list, false);
                if (CollectionUtils.isNotEmpty(bidChartData)) {
                    adReportChartRpcVoList.addAll(bidChartData);
                }
                reportBuilder.addAllChart(adReportChartRpcVoList);
                builder.setCode(Int32Value.of(Result.SUCCESS));
                builder.setData(reportBuilder.build());
            }

            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    /**
     * 查询广告产品维度投放小时级数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getTargetHourReportOfAd(
            GetTargetHourReportOfAdRequestPb.GetTargetHourReportOfAdRequest request,
            StreamObserver<GetTargetHourReportOfAdResponsePb.GetTargetHourReportOfAdResponse> responseObserver) {
        try {
            GetTargetHourReportOfAdResponsePb.GetTargetHourReportOfAdResponse.Builder builder =
                    GetTargetHourReportOfAdResponsePb.GetTargetHourReportOfAdResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                TargetHourParam param = new TargetHourParam();
                BeanUtils.copyProperties(request, param);
                param.setTargetId(request.getTargetId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setOrderField(request.getOrderField());
                param.setOrderType(request.getOrderType());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setPageNo(request.getPageNo().getValue());
                param.setPageSize(request.getPageSize().getValue());
                param.setUid(request.getUid().getValue());
                List<AdKeywordTargetHourOfAdVo> list = amazonAdTargetHourReportService.getListOfAd(param.getPuid(), param);
                if (CollectionUtils.isNotEmpty(list)) {
                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdKeywordAndTargetHourVo.class);
                    if (isSorted) {
                        PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
                    }

                    GetTargetHourReportOfAdResponsePb.GetTargetHourReportOfAdResponse.HourReport.Builder reportBuilder =
                            GetTargetHourReportOfAdResponsePb.GetTargetHourReportOfAdResponse.HourReport.newBuilder();
                    reportBuilder.addAllList(list.stream().filter(Objects::nonNull)
                            .map(PbUtil::toTargetPb).collect(Collectors.toList()));
                    reportBuilder.setSummary(PbUtil.toTargetPb(summaryTargetHourOfAdVo(list)));
                    builder.setCode(Int32Value.of(Result.SUCCESS));
                    builder.setData(reportBuilder.build());
                }
            }

            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    /**
     * 查询广告位维度投放小时级数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getTargetHourReportOfPlacement(
            GetTargetHourReportOfPlacementRequestPb.GetTargetHourReportOfPlacementRequest request,
            StreamObserver<GetTargetHourReportOfPlacementResponsePb.GetTargetHourReportOfPlacementResponse> responseObserver) {
        try {
            GetTargetHourReportOfPlacementResponsePb.GetTargetHourReportOfPlacementResponse.Builder builder =
                    GetTargetHourReportOfPlacementResponsePb.GetTargetHourReportOfPlacementResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                TargetHourParam param = new TargetHourParam();
                BeanUtils.copyProperties(request, param);
                param.setTargetId(request.getTargetId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setOrderField(request.getOrderField());
                param.setOrderType(request.getOrderType());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setPageNo(request.getPageNo().getValue());
                param.setPageSize(request.getPageSize().getValue());
                param.setUid(request.getUid().getValue());

                List<AdKeywordTargetHourOfPlacementVo> list = amazonAdTargetHourReportService.getListOfPlacement(param.getPuid(), param);
                if (CollectionUtils.isNotEmpty(list)) {
                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdKeywordAndTargetHourVo.class);
                    if (isSorted) {
                        PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
                    } else {
                        list = list.stream().sorted(Comparator.comparingInt(e -> {
                                    if ("产品页面".equalsIgnoreCase(e.getPlacement())) {
                                        return 3;
                                    } else if ("搜索结果顶部(首页)".equalsIgnoreCase(e.getPlacement())) {
                                        return 1;
                                    } else if ("搜索结果的其余位置".equalsIgnoreCase(e.getPlacement())) {
                                        return 2;
                                    } else {
                                        return 4;
                                    }
                                })).collect(Collectors.toList());
                    }
                    GetTargetHourReportOfPlacementResponsePb.GetTargetHourReportOfPlacementResponse.HourReport.Builder reportBuilder =
                            GetTargetHourReportOfPlacementResponsePb.GetTargetHourReportOfPlacementResponse.HourReport.newBuilder();
                    reportBuilder.addAllList(list.stream().filter(Objects::nonNull)
                            .map(PbUtil::toTargetPb).collect(Collectors.toList()));
                    reportBuilder.setSummary(PbUtil.toTargetPb(summaryTargetHourOfPlacementVo(list)));
                    builder.setCode(Int32Value.of(Result.SUCCESS));
                    builder.setData(reportBuilder.build());
                }
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }


    public AdKeywordTargetHourOfAdVo summaryKeywordOfAdVo(List<AdKeywordTargetHourOfAdVo> list) {
        AdKeywordTargetHourOfAdVo vo = new AdKeywordTargetHourOfAdVo();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        for (AdKeywordTargetHourOfAdVo ad : list) {
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
        }
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }


    public AdKeywordTargetHourOfPlacementVo summaryKeywordOfPlacementVo(List<AdKeywordTargetHourOfPlacementVo> list) {
        AdKeywordTargetHourOfPlacementVo vo = new AdKeywordTargetHourOfPlacementVo();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        for (AdKeywordTargetHourOfPlacementVo ad : list) {
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
        }
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }


    private AdKeywordTargetHourOfAdVo summaryTargetHourOfAdVo(List<AdKeywordTargetHourOfAdVo> list) {
        AdKeywordTargetHourOfAdVo vo = new AdKeywordTargetHourOfAdVo();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        for (AdKeywordTargetHourOfAdVo ad : list) {
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
        }
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }


    private AdKeywordTargetHourOfPlacementVo summaryTargetHourOfPlacementVo(List<AdKeywordTargetHourOfPlacementVo> list) {
        AdKeywordTargetHourOfPlacementVo vo = new AdKeywordTargetHourOfPlacementVo();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        for (AdKeywordTargetHourOfPlacementVo ad : list) {
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
        }
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }


    private AdKeywordAndTargetHourVo summaryTargetHourVo(List<AdKeywordAndTargetHourVo> list, boolean bool, BigDecimal shopSalesByDate) {
        if (bool) {
            return summaryTargetHourVo(list, shopSalesByDate);
        }
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        for (AdKeywordAndTargetHourVo ad : list) {
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
            vo.setAdCostCompare(MathUtil.add(vo.getAdCostCompare(), ad.getAdCostCompare()));
            vo.setClicksCompare(MathUtil.add(vo.getClicksCompare(), ad.getClicksCompare()));
            vo.setImpressionsCompare(MathUtil.add(vo.getImpressionsCompare(), ad.getImpressionsCompare()));
            vo.setAdSaleNumCompare(MathUtil.add(vo.getAdSaleNumCompare(), ad.getAdSaleNumCompare()));
            vo.setAdSaleCompare(MathUtil.add(vo.getAdSaleCompare(), ad.getAdSaleCompare()));
            vo.setAdOrderNumCompare(MathUtil.add(vo.getAdOrderNumCompare(), ad.getAdOrderNumCompare()));

            vo.setAdCostPercentage(MathUtil.add(vo.getAdCostPercentage(), ad.getAdCostPercentage()));
            vo.setAdSalePercentage(MathUtil.add(vo.getAdSalePercentage(), ad.getAdSalePercentage()));
            vo.setAdOrderNumPercentage(MathUtil.add(vo.getAdOrderNumPercentage(), ad.getAdOrderNumPercentage()));
            vo.setOrderNumPercentage(MathUtil.add(vo.getOrderNumPercentage(), ad.getOrderNumPercentage()));
            vo.setViewableImpressions(MathUtil.add(vo.getViewableImpressions(),ad.getViewableImpressions()));
            vo.setOrdersNewToBrand(MathUtil.add(vo.getOrdersNewToBrand(),ad.getOrdersNewToBrand()));
            vo.setUnitsOrderedNewToBrand(MathUtil.add(vo.getUnitsOrderedNewToBrand(),ad.getUnitsOrderedNewToBrand()));
            vo.setSalesNewToBrand(MathUtil.add(vo.getSalesNewToBrand(),ad.getSalesNewToBrand()));

            vo.setVcpmCost(MathUtil.add(vo.getVcpmCost(),ad.getVcpmCost()));
            vo.setVcpmImpressions(MathUtil.add(vo.getVcpmImpressions(),ad.getVcpmImpressions()));
            vo.setTotalClicks(MathUtil.add(vo.getTotalClicks(),ad.getTotalClicks()));
            vo.setTotalImpressions(MathUtil.add(vo.getTotalImpressions(),ad.getTotalImpressions()));
            vo.setTotalAdSale(MathUtil.add(vo.getTotalAdSale(),ad.getTotalAdSale()));
            vo.setTotalAdSelfSale(MathUtil.add(vo.getTotalAdSelfSale(),ad.getTotalAdSelfSale()));
        }
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));

        vo.setAdCostPerClickCompare(MathUtil.divideByZero(vo.getAdCostCompare(), BigDecimal.valueOf(vo.getClicksCompare())));
        vo.setAcosCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                vo.getAdCostCompare().multiply(new BigDecimal("100")).divide(vo.getAdSaleCompare(), 4, RoundingMode.HALF_UP));
        vo.setRoasCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                vo.getAdSaleCompare().divide(vo.getAdCostCompare(), 4, RoundingMode.HALF_UP));
        vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicksCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressionsCompare())));
        vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNumCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicksCompare())));

//        vo.setVcpm(MathUtil.divideByThousand(vo.getAdCost(), vo.getViewableImpressions()));
//        vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
//        vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));
//        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
//        vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
//        vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getAdOrderNum() - vo.getSelfAdOrderNum())));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)),BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)),BigDecimal.valueOf(vo.getAdSaleNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));

        vo.setVcpm(MathUtil.divideByThousand(vo.getVcpmCost(), vo.getVcpmImpressions()));
        vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getTotalImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getTotalClicks(), vo.getViewableImpressions()));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getTotalAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
        vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getTotalAdSale().subtract(vo.getTotalAdSelfSale()), BigDecimal.valueOf(vo.getOtherAdOrderNum())));


        if (vo.getAdCostPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdCostPercentage()) == 0) {
            vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setAdCostPercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getAdSalePercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdSalePercentage()) == 0) {
            vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setAdSalePercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getAdOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdOrderNumPercentage()) == 0) {
            vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setAdOrderNumPercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getOrderNumPercentage()) == 0) {
            vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setOrderNumPercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        vo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate));
        vo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate));
        vo.afterPropertiesSet();//为各个对比率进行设值
        return vo;
    }


    private AdKeywordAndTargetHourVo summaryTargetHourVo(List<AdKeywordAndTargetHourVo> list) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        for (AdKeywordAndTargetHourVo ad : list) {
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
            vo.setAdCostCompare(MathUtil.add(vo.getAdCostCompare(), ad.getAdCostCompare()));
            vo.setClicksCompare(MathUtil.add(vo.getClicksCompare(), ad.getClicksCompare()));
            vo.setImpressionsCompare(MathUtil.add(vo.getImpressionsCompare(), ad.getImpressionsCompare()));
            vo.setAdSaleNumCompare(MathUtil.add(vo.getAdSaleNumCompare(), ad.getAdSaleNumCompare()));
            vo.setAdSaleCompare(MathUtil.add(vo.getAdSaleCompare(), ad.getAdSaleCompare()));
            vo.setAdOrderNumCompare(MathUtil.add(vo.getAdOrderNumCompare(), ad.getAdOrderNumCompare()));

            vo.setAdCostPercentage(MathUtil.add(vo.getAdCostPercentage(), ad.getAdCostPercentage()));
            vo.setAdSalePercentage(MathUtil.add(vo.getAdSalePercentage(), ad.getAdSalePercentage()));
            vo.setAdOrderNumPercentage(MathUtil.add(vo.getAdOrderNumPercentage(), ad.getAdOrderNumPercentage()));
            vo.setOrderNumPercentage(MathUtil.add(vo.getOrderNumPercentage(), ad.getOrderNumPercentage()));
            vo.setViewableImpressions(MathUtil.add(vo.getViewableImpressions(),ad.getViewableImpressions()));
            vo.setOrdersNewToBrand(MathUtil.add(vo.getOrdersNewToBrand(),ad.getOrdersNewToBrand()));
            vo.setUnitsOrderedNewToBrand(MathUtil.add(vo.getUnitsOrderedNewToBrand(),ad.getUnitsOrderedNewToBrand()));
            vo.setSalesNewToBrand(MathUtil.add(vo.getSalesNewToBrand(),ad.getSalesNewToBrand()));
        }
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));

        vo.setVcpm(MathUtil.divideByThousand(vo.getAdCost(), vo.getViewableImpressions()));
        vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
        vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getAdOrderNum() - vo.getSelfAdOrderNum())));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)),BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)),BigDecimal.valueOf(vo.getAdSaleNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));
        if (vo.getAdCostPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdCostPercentage()) == 0) {
            vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setAdCostPercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getAdSalePercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdSalePercentage()) == 0) {
            vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setAdSalePercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getAdOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdOrderNumPercentage()) == 0) {
            vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setAdOrderNumPercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getOrderNumPercentage()) == 0) {
            vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setOrderNumPercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        vo.afterPropertiesSet();//为各个对比率进行设值
        return vo;
    }

    private AdKeywordAndTargetHourVo summaryTargetHourVo(List<AdKeywordAndTargetHourVo> list, BigDecimal shopSalesByDate) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        for (AdKeywordAndTargetHourVo ad : list) {
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
            vo.setAdCostCompare(MathUtil.add(vo.getAdCostCompare(), ad.getAdCostCompare()));
            vo.setClicksCompare(MathUtil.add(vo.getClicksCompare(), ad.getClicksCompare()));
            vo.setImpressionsCompare(MathUtil.add(vo.getImpressionsCompare(), ad.getImpressionsCompare()));
            vo.setAdSaleNumCompare(MathUtil.add(vo.getAdSaleNumCompare(), ad.getAdSaleNumCompare()));
            vo.setAdSaleCompare(MathUtil.add(vo.getAdSaleCompare(), ad.getAdSaleCompare()));
            vo.setAdOrderNumCompare(MathUtil.add(vo.getAdOrderNumCompare(), ad.getAdOrderNumCompare()));

            vo.setAdCostPercentage(MathUtil.add(vo.getAdCostPercentage(), ad.getAdCostPercentage()));
            vo.setAdSalePercentage(MathUtil.add(vo.getAdSalePercentage(), ad.getAdSalePercentage()));
            vo.setAdOrderNumPercentage(MathUtil.add(vo.getAdOrderNumPercentage(), ad.getAdOrderNumPercentage()));
            vo.setOrderNumPercentage(MathUtil.add(vo.getOrderNumPercentage(), ad.getOrderNumPercentage()));
            vo.setViewableImpressions(MathUtil.add(vo.getViewableImpressions(),ad.getViewableImpressions()));
            vo.setOrdersNewToBrand(MathUtil.add(vo.getOrdersNewToBrand(),ad.getOrdersNewToBrand()));
            vo.setUnitsOrderedNewToBrand(MathUtil.add(vo.getUnitsOrderedNewToBrand(),ad.getUnitsOrderedNewToBrand()));
            vo.setSalesNewToBrand(MathUtil.add(vo.getSalesNewToBrand(),ad.getSalesNewToBrand()));
        }
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));

        vo.setAdCostPerClickCompare(MathUtil.divideByZero(vo.getAdCostCompare(), BigDecimal.valueOf(vo.getClicksCompare())));
        vo.setAcosCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                vo.getAdCostCompare().multiply(new BigDecimal("100")).divide(vo.getAdSaleCompare(), 4, RoundingMode.HALF_UP));
        vo.setRoasCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                vo.getAdSaleCompare().divide(vo.getAdCostCompare(), 4, RoundingMode.HALF_UP));
        vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicksCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressionsCompare())));
        vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNumCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicksCompare())));

        vo.setVcpm(MathUtil.divideByThousand(vo.getAdCost(), vo.getViewableImpressions()));
        vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
        vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getAdOrderNum() - vo.getSelfAdOrderNum())));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)),BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)),BigDecimal.valueOf(vo.getAdSaleNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));
        if (vo.getAdCostPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdCostPercentage()) == 0) {
            vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdCostPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getAdSalePercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdSalePercentage()) == 0) {
            vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdSalePercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getAdOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdOrderNumPercentage()) == 0) {
            vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdOrderNumPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getOrderNumPercentage()) == 0) {
            vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setOrderNumPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        vo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate));
        vo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate));
        vo.calculateCompareRate();//为各个对比率进行设值
        return vo;
    }


    /**
     * 查询关键词小时级数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getKeywordHourReportAggregateData(
            GetKeywordHourReportAggregateDataRequestPb.GetKeywordHourReportAggregateDataRequest request,
            StreamObserver<GetKeywordHourReportAggregateDataResponsePb.GetKeywordHourReportAggregateDataResponse> responseObserver) {
        try {
            log.info("getKeywordHourReportAggregateData:",request);
            GetKeywordHourReportAggregateDataResponsePb.GetKeywordHourReportAggregateDataResponse.Builder builder =
                    GetKeywordHourReportAggregateDataResponsePb.GetKeywordHourReportAggregateDataResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || (!request.hasShopId() && CollectionUtils.isEmpty(request.getShopIdListList())) || !request.hasEndDateStr() || !request.hasStartDateStr()
                    || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 &&
                    (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr())) || !request.hasPageSign()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                VcShopAuth byIdAndPuid = vcShopAuthDao.getByIdAndPuid(request.getShopId().getValue(), request.getPuid().getValue());
                boolean isVc = byIdAndPuid != null;
                KeywordHourParam param = new KeywordHourParam();
                BeanUtils.copyProperties(request, param);
                param.setKeywordId(request.getKeywordId());
                param.setWeeks(request.getWeeks());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setShopIdList(request.getShopIdListList());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setEndDateCompare(request.getEndDateCompareStr());
                param.setStartDateCompare(request.getStartDateCompareStr());
                param.setOrderField(request.getOrderField());
                param.setOrderType(request.getOrderType());
                if (request.hasIsCompare()) {
                    param.setIsCompare(request.getIsCompare().getValue());
                }
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setPageNo(request.getPageNo().getValue());
                param.setPageSize(request.getPageSize().getValue());
                param.setUid(request.getUid().getValue());
                param.setType(request.getType());
                List<String> temporaryIds = cpcPageIdsHandler.getCampaignIdsTemporary(request.getPuid().getValue(),
                        request.getPageSign(), "", param.getShopId(), new KeywordHourParam[]{param});
                param.setKeywordIds(temporaryIds);
                // 兼容旧单店铺逻辑
                if(CollectionUtils.isEmpty(param.getShopIdList()) && param.getShopId() != null){
                    param.setShopIdList(CollectionUtil.newArrayList(param.getShopId()));
                }
                // 店铺状态校验
                List<ShopAuth> shopAuths = shopAuthDao.listValidShopByIds(param.getPuid(), param.getShopIdList());
                if (CollectionUtils.isEmpty(shopAuths)) {
                    builder.setCode(Int32Value.of(Result.ERROR));
                    builder.setMsg("no shop found!");
                    responseObserver.onNext(builder.build());
                    responseObserver.onCompleted();
                    return;
                }
                param.setShopIdList(StreamUtil.toListDistinct(shopAuths, ShopAuth::getId));
                // 多店鋪,超过限制投放数量返回，防止doris cpu过高
                if((shopAuths.size() > 1 && temporaryIds.size() >= adManageLimitConfig.getTargetHourLimit()) || shopAuths.size() > 40){
                    builder.setCode(Int32Value.of(Result.ERROR));
                    if(shopAuths.size() > 40){
                        builder.setMsg("当前店铺数量超过40个，请减少后重新查询!");
                    }else{
                        builder.setMsg("当前所选数据量过大，请减少店铺查询!");
                    }
                    responseObserver.onNext(builder.build());
                    responseObserver.onCompleted();
                    return;
                }
                GetKeywordHourReportAggregateDataResponsePb.GetKeywordHourReportAggregateDataResponse.HourReport.Builder reportBuilder =
                        GetKeywordHourReportAggregateDataResponsePb.GetKeywordHourReportAggregateDataResponse.HourReport.newBuilder();
                List<AdKeywordAndTargetHourVo> list = new ArrayList<>();
                List<AdKeywordAndTargetHourVo> compares = new ArrayList<>();
                if (hasHourlyData(request)) {
                    if ("SP".equalsIgnoreCase(request.getAdType())) {
                        list = amazonAdKeywordHourReportService.getHourListAll(param.getPuid(), shopAuths, param);
                    } else {
                        list = amazonAdKeywordHourReportService.getSbHourListAll(param.getPuid(), shopAuths, param);
                    }
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.DAILY) {
                    list = amazonAdKeywordHourReportService.getDailyListAll(param.getPuid(), shopAuths, request.getType(), param, compares);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.WEEKLY) {
                    list = amazonAdKeywordHourReportService.getWeeklyAllList(param.getPuid(), shopAuths, request.getType(), param, compares);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.MONTHLY) {
                    list = amazonAdKeywordHourReportService.getMonthlyAllList(param.getPuid(), shopAuths, request.getType(), param, compares);
                }
                if (CollectionUtils.isNotEmpty(list)) {

                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdKeywordAndTargetHourVo.class);
                    if (isSorted) {
                        PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
                    } else {
                        //默认按天升序
                        PageUtil.sortedByOrderField(list, "date", null);
                    }
                }
                //取店铺销售额
                BigDecimal shopSalesByDate = dwsSaleProfitShopDayDao.sumShopSaleByDateRange(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate(), MultipleUtils.changeRate(shopAuths));
                reportBuilder.addAllList(list.stream().filter(Objects::nonNull).map(e -> PbUtil.toHourReportPb(e, shopSalesByDate, isVc)).collect(Collectors.toList()));
                AdKeywordAndTargetHourVo summaryVO = summaryTargetHourVo(list, shopSalesByDate);
                if (Integer.valueOf(1).equals(param.getIsCompare()) && !hasHourlyData(request)) {
                    AdKeywordAndTargetHourVo compareVO = summaryTargetHourVo(compares);
                    summaryVO.compareDataSet(compareVO);
                }
                reportBuilder.setSummary(PbUtil.toHourReportPb(summaryVO, shopSalesByDate, isVc));
                reportBuilder.addAllChart(ReportChartUtil.getHourChartData(list, false));
                reportBuilder.setCurrency(MultipleUtils.getCurrency(shopAuths));
                //对比数据,chart图数据
                if (param.getIsCompare() != null && param.getIsCompare() == 1) {
                    List<AdKeywordAndTargetHourVo> compareHourVos = list.stream().map(item-> {
                        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                        vo.setLabel(item.getLabel());
                        vo.setClicks(item.getClicksCompare());
                        vo.setImpressions(item.getImpressionsCompare());
                        vo.setAdSale(item.getAdSaleCompare());
                        vo.setAdCost(item.getAdCostCompare());
                        vo.setAdOrderNum(item.getAdOrderNumCompare());
                        vo.setAdSaleNum(item.getAdSaleNumCompare());
                        vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                        vo.setAcos(item.getAcosCompare());
                        vo.setRoas(item.getRoasCompare());
                        vo.setCtr(item.getCtrCompare());
                        vo.setCvr(item.getCvrCompare());
                        return vo;
                    }).collect(Collectors.toList());
                    reportBuilder.addAllChart(ReportChartUtil.getHourChartData(compareHourVos, true));
                }

                builder.setCode(Int32Value.of(Result.SUCCESS));
                builder.setData(reportBuilder.build());
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }



    /**
     * 查询广告位维度关键词小时级数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getKeywordHourReportOfPlacementAggregateData(
            GetKeywordHourReportOfPlacementAggregateDataRequestPb.GetKeywordHourReportOfPlacementAggregateDataRequest request,
            StreamObserver<GetKeywordHourReportOfPlacementAggregateDataResponsePb.GetKeywordHourReportOfPlacementAggregateDataResponse> responseObserver) {
        try {
            GetKeywordHourReportOfPlacementAggregateDataResponsePb.GetKeywordHourReportOfPlacementAggregateDataResponse.Builder builder =
                    GetKeywordHourReportOfPlacementAggregateDataResponsePb.GetKeywordHourReportOfPlacementAggregateDataResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || (!request.hasShopId() && CollectionUtils.isEmpty(request.getShopIdListList())) || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                KeywordHourParam param = new KeywordHourParam();
                BeanUtils.copyProperties(request, param);
                param.setKeywordId(request.getKeywordId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setShopIdList(request.getShopIdListList());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setOrderField(request.getOrderField());
                param.setOrderType(request.getOrderType());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setPageNo(request.getPageNo().getValue());
                param.setPageSize(request.getPageSize().getValue());
                param.setUid(request.getUid().getValue());
                // 兼容旧单店铺逻辑
                if(CollectionUtils.isEmpty(param.getShopIdList()) && param.getShopId() != null){
                    param.setShopIdList(CollectionUtil.newArrayList(param.getShopId()));
                }
                // 店铺状态校验
                List<ShopAuth> shopAuths = shopAuthDao.listValidShopByIds(param.getPuid(), param.getShopIdList());
                if (CollectionUtils.isEmpty(shopAuths)) {
                    throw new SponsoredBizException("no shop found");
                }
                List<String> temporaryIds = cpcPageIdsHandler.getCampaignIdsTemporary(request.getPuid().getValue(),
                        request.getPageSign(), "", param.getShopId(), new KeywordHourParam[]{param});
                param.setKeywordIds(temporaryIds);
                List<AdKeywordTargetHourOfPlacementVo> list =
                        amazonAdKeywordHourReportService.getListOfPlacementAll(param.getPuid(), shopAuths, param);
                if (CollectionUtils.isNotEmpty(list)) {
                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdKeywordAndTargetHourVo.class);
                    if (isSorted) {
                        PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
                    } else {
                        list = list.stream().sorted(Comparator.comparingInt(e -> {
                            if ("产品页面".equalsIgnoreCase(e.getPlacement())) {
                                return 3;
                            } else if ("搜索结果顶部(首页)".equalsIgnoreCase(e.getPlacement())) {
                                return 1;
                            } else if ("搜索结果的其余位置".equalsIgnoreCase(e.getPlacement())) {
                                return 2;
                            } else {
                                return 4;
                            }
                        })).collect(Collectors.toList());
                    }

                    GetKeywordHourReportOfPlacementAggregateDataResponsePb.GetKeywordHourReportOfPlacementAggregateDataResponse.HourReport.Builder reportBuilder =
                            GetKeywordHourReportOfPlacementAggregateDataResponsePb.GetKeywordHourReportOfPlacementAggregateDataResponse.HourReport.newBuilder();
                    reportBuilder.addAllList(list.stream().filter(Objects::nonNull)
                            .map(PbUtil::toKeywordPb).collect(Collectors.toList()));
                    reportBuilder.setSummary(PbUtil.toKeywordPb(summaryKeywordOfPlacementVo(list)));
                    reportBuilder.setCurrency(MultipleUtils.getCurrency(shopAuths));
                    builder.setCode(Int32Value.of(Result.SUCCESS));
                    builder.setData(reportBuilder.build());
                }
            }

            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }


    private boolean hasHourlyData(GetKeywordHourReportAggregateDataRequestPb.GetKeywordHourReportAggregateDataRequest request) {
        return request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY;
    }


    /**
     * 查询投放小时级数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getTargetHourReportAggregateData(
            GetTargetHourReportAggregateDataRequestPb.GetTargetHourReportAggregateDataRequest request,
            StreamObserver<GetTargetHourReportAggregateDataResponsePb.GetTargetHourReportAggregateDataResponse> responseObserver) {
        try {
            GetTargetHourReportAggregateDataResponsePb.GetTargetHourReportAggregateDataResponse.Builder builder =
                    GetTargetHourReportAggregateDataResponsePb.GetTargetHourReportAggregateDataResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || (!request.hasShopId() && CollectionUtils.isEmpty(request.getShopIdListList())) || !request.hasEndDateStr() || !request.hasStartDateStr()
                    || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 &&
                    (!request.hasStartDateCompareStr() || !request.hasEndDateCompareStr()))) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                VcShopAuth byIdAndPuid = vcShopAuthDao.getByIdAndPuid(request.getShopId().getValue(), request.getPuid().getValue());
                boolean isVc = byIdAndPuid != null;
                TargetHourParam param = new TargetHourParam();
                BeanUtils.copyProperties(request, param);
                param.setTargetId(request.getTargetId());
                param.setWeeks(request.getWeeks());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setShopIdList(request.getShopIdListList());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setEndDateCompare(request.getEndDateCompareStr());
                param.setStartDateCompare(request.getStartDateCompareStr());
                param.setOrderField(request.getOrderField());
                param.setOrderType(request.getOrderType());
                if (request.hasIsCompare()) {
                    param.setIsCompare(request.getIsCompare().getValue());
                }
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setPageNo(request.getPageNo().getValue());
                param.setPageSize(request.getPageSize().getValue());
                param.setUid(request.getUid().getValue());
                List<String> temporaryIds = cpcPageIdsHandler.getCampaignIdsTemporary(request.getPuid().getValue(),
                        request.getPageSign(), "", param.getShopId(), new TargetHourParam[]{param});
                param.setTargetIds(temporaryIds);
                param.setType(request.getType());
                GetTargetHourReportAggregateDataResponsePb.GetTargetHourReportAggregateDataResponse.HourReport.Builder reportBuilder =
                        GetTargetHourReportAggregateDataResponsePb.GetTargetHourReportAggregateDataResponse.HourReport.newBuilder();

                List<AdKeywordAndTargetHourVo> list = new ArrayList<>();
                List<AdKeywordAndTargetHourVo> compares = new ArrayList<>();
                // 兼容旧单店铺逻辑
                if(CollectionUtils.isEmpty(param.getShopIdList()) && param.getShopId() != null){
                    param.setShopIdList(CollectionUtil.newArrayList(param.getShopId()));
                }
                // 店铺状态校验
                List<ShopAuth> shopAuths = shopAuthDao.listValidShopByIds(param.getPuid(), param.getShopIdList());
                if (CollectionUtils.isEmpty(shopAuths)) {
                    builder.setCode(Int32Value.of(Result.ERROR));
                    builder.setMsg("no shop found");
                    responseObserver.onNext(builder.build());
                    responseObserver.onCompleted();
                    return;
                }
                param.setShopIdList(StreamUtil.toListDistinct(shopAuths, ShopAuth::getId));
                // 多店鋪,超过限制投放数量返回，防止doris cpu过高
                if((shopAuths.size() > 1 && temporaryIds.size() >= adManageLimitConfig.getTargetHourLimit()) || shopAuths.size() > 40){
                    builder.setCode(Int32Value.of(Result.ERROR));
                    if(shopAuths.size() > 40){
                        builder.setMsg("当前店铺数量超过40个，请减少后重新查询!");
                    }else{
                        builder.setMsg("当前所选数据量过大，请减少店铺查询!");
                    }
                    responseObserver.onNext(builder.build());
                    responseObserver.onCompleted();
                    return;
                }
                if (hasHourlyData(request)) {
                    if ("SP".equalsIgnoreCase(request.getType())) {
                        list = amazonAdTargetHourReportService.getAllList(param.getPuid(), shopAuths, param);
                    } else if ("SB".equalsIgnoreCase(request.getType())) {
                        list = amazonAdTargetHourReportService.getSbHourAllList(param.getPuid(), shopAuths, param);
                    } else if ("SD".equalsIgnoreCase(request.getType())) {
                        list = amazonAdTargetHourReportService.getSdHourAllList(param.getPuid(), shopAuths, param);
                    }
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.DAILY) {
                    list = amazonAdTargetHourReportService.getDailyAllList(param.getPuid(), shopAuths, request.getType(), param, compares);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.WEEKLY) {
                    list = amazonAdTargetHourReportService.getWeeklyAllList(param.getPuid(), shopAuths, request.getType(), param, compares);
                } else if (request.getDateModel() == ReportDateModelPb.ReportDateModel.MONTHLY) {
                    list = amazonAdTargetHourReportService.getMonthlyAllList(param.getPuid(), shopAuths, request.getType(), param, compares);
                }
                BigDecimal shopSalesByDate = dwsSaleProfitShopDayDao.sumShopSaleByDateRange(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate(), MultipleUtils.changeRate(shopAuths));
                if (CollectionUtils.isNotEmpty(list)) {
                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdKeywordAndTargetHourVo.class);
                    if (isSorted) {
                        PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
                    } else {
                        //默认按天升序
                        PageUtil.sortedByOrderField(list, "date", null);
                    }
                }
                reportBuilder.setCurrency(MultipleUtils.getCurrency(shopAuths));
                //取店铺销售额
                reportBuilder.addAllList(list.stream().filter(Objects::nonNull).map(e -> PbUtil.toHourReportPb(e, shopSalesByDate, isVc)).collect(Collectors.toList()));
                AdKeywordAndTargetHourVo summaryVO = summaryTargetHourVo(list, shopSalesByDate);
                if (Integer.valueOf(1).equals(param.getIsCompare()) && !hasHourlyData(request)) {
                    AdKeywordAndTargetHourVo compareVO = summaryTargetHourVo(compares);
                    summaryVO.compareDataSet(compareVO);
                }
                reportBuilder.setSummary(PbUtil.toHourReportPb(summaryVO, shopSalesByDate, isVc));
                reportBuilder.addAllChart(ReportChartUtil.getHourChartData(list, false));

                if (param.getIsCompare() != null && param.getIsCompare() == 1) {
                    List<AdKeywordAndTargetHourVo> compareHourVos = list.stream().map(item-> {
                        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                        vo.setLabel(item.getLabel());
                        vo.setClicks(item.getClicksCompare());
                        vo.setImpressions(item.getImpressionsCompare());
                        vo.setAdSale(item.getAdSaleCompare());
                        vo.setAdCost(item.getAdCostCompare());
                        vo.setAdOrderNum(item.getAdOrderNumCompare());
                        vo.setAdSaleNum(item.getAdSaleNumCompare());
                        vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                        vo.setAcos(item.getAcosCompare());
                        vo.setRoas(item.getRoasCompare());
                        vo.setCtr(item.getCtrCompare());
                        vo.setCvr(item.getCvrCompare());
                        return vo;
                    }).collect(Collectors.toList());
                    reportBuilder.addAllChart(ReportChartUtil.getHourChartData(compareHourVos, true));
                }

                builder.setCode(Int32Value.of(Result.SUCCESS));
                builder.setData(reportBuilder.build());
            }

            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    private boolean hasHourlyData(GetTargetHourReportAggregateDataRequestPb.GetTargetHourReportAggregateDataRequest request) {
        return request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY;
    }

    /**
     * 查询广告位维度投放小时级数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getTargetHourReportOfPlacementAggregateData(
            GetTargetHourReportOfPlacementAggregateDataRequestPb.GetTargetHourReportOfPlacementAggregateDataRequest request,
            StreamObserver<GetTargetHourReportOfPlacementAggregateDataResponsePb.GetTargetHourReportOfPlacementAggregateDataResponse> responseObserver) {
        try {
            GetTargetHourReportOfPlacementAggregateDataResponsePb.GetTargetHourReportOfPlacementAggregateDataResponse.Builder builder =
                    GetTargetHourReportOfPlacementAggregateDataResponsePb.GetTargetHourReportOfPlacementAggregateDataResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || (!request.hasShopId() && CollectionUtils.isEmpty(request.getShopIdListList())) || !request.hasEndDateStr() || !request.hasStartDateStr()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                TargetHourParam param = new TargetHourParam();
                BeanUtils.copyProperties(request, param);
                param.setTargetId(request.getTargetId());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setShopIdList(request.getShopIdListList());
                param.setStartDate(request.getStartDateStr());
                param.setEndDate(request.getEndDateStr());
                param.setOrderField(request.getOrderField());
                param.setOrderType(request.getOrderType());
                param.setPuid(request.getPuid().getValue());
                param.setShopId(request.getShopId().getValue());
                param.setPageNo(request.getPageNo().getValue());
                param.setPageSize(request.getPageSize().getValue());
                param.setUid(request.getUid().getValue());
                // 兼容旧单店铺逻辑
                if(CollectionUtils.isEmpty(param.getShopIdList()) && param.getShopId() != null){
                    param.setShopIdList(CollectionUtil.newArrayList(param.getShopId()));
                }
                List<String> temporaryIds = cpcPageIdsHandler.getCampaignIdsTemporary(request.getPuid().getValue(),
                        request.getPageSign(), "", param.getShopId(), new TargetHourParam[]{param});
                param.setTargetIds(temporaryIds);
                // 店铺状态校验
                List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(param.getPuid(), param.getShopIdList());
                if (CollectionUtils.isEmpty(shopAuths)) {
                    throw new SponsoredBizException("no shop found");
                }
                List<AdKeywordTargetHourOfPlacementVo> list = amazonAdTargetHourReportService.getListOfPlacementAll(param.getPuid(), shopAuths, param);
                if (CollectionUtils.isNotEmpty(list)) {
                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdKeywordAndTargetHourVo.class);
                    if (isSorted) {
                        PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
                    } else {
                        list = list.stream().sorted(Comparator.comparingInt(e -> {
                            if ("产品页面".equalsIgnoreCase(e.getPlacement())) {
                                return 3;
                            } else if ("搜索结果顶部(首页)".equalsIgnoreCase(e.getPlacement())) {
                                return 1;
                            } else if ("搜索结果的其余位置".equalsIgnoreCase(e.getPlacement())) {
                                return 2;
                            } else {
                                return 4;
                            }
                        })).collect(Collectors.toList());
                    }

                    GetTargetHourReportOfPlacementAggregateDataResponsePb.GetTargetHourReportOfPlacementAggregateDataResponse.HourReport.Builder reportBuilder =
                            GetTargetHourReportOfPlacementAggregateDataResponsePb.GetTargetHourReportOfPlacementAggregateDataResponse.HourReport.newBuilder();
                    reportBuilder.addAllList(list.stream().filter(Objects::nonNull)
                            .map(PbUtil::toTargetPb).collect(Collectors.toList()));
                    reportBuilder.setSummary(PbUtil.toTargetPb(summaryTargetHourOfPlacementVo(list)));
                    reportBuilder.setCurrency(MultipleUtils.getCurrency(shopAuths));
                    builder.setCode(Int32Value.of(Result.SUCCESS));
                    builder.setData(reportBuilder.build());
                }
            }


            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    /**
     * 查询关键词小时级数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAggregateKeywordHourReportData(
            GetAggregateKeywordHourReportDataRequestPb.GetAggregateKeywordHourReportDataRequest request,
            StreamObserver<GetAggregateKeywordHourReportDataResponsePb.GetAggregateKeywordHourReportDataResponse> responseObserver) {
        try {
            GetAggregateKeywordHourReportDataResponsePb.GetAggregateKeywordHourReportDataResponse.Builder builder =
                    GetAggregateKeywordHourReportDataResponsePb.GetAggregateKeywordHourReportDataResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || CollectionUtils.isEmpty(request.getShopIdList()) || !request.hasEndDate() || !request.hasStartDate()
                    || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 &&
                    (!request.hasStartDateCompare() || !request.hasEndDateCompare())) || !request.hasPageSign()) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                List<VcShopAuth> byIdAndPuid = vcShopAuthDao.getListByIdList(request.getShopIdList());
                boolean isVc = CollectionUtils.isNotEmpty(byIdAndPuid);
                KeywordViewHourParam param = new KeywordViewHourParam();
                BeanUtils.copyProperties(request, param);
                param.setPuid(request.getPuid().getValue());
                param.setIsCompare(request.getIsCompare().getValue());
                param.setMarketplaceId(request.getMarketplaceId());

                AggregateIdsTemporary aggregateIdsTemporary = cpcPageIdsHandler.getAggregateIdsTemporary(request.getPageSign(), "");
                if (aggregateIdsTemporary != null) {
                    param.setKeywordIdList(aggregateIdsTemporary.getIdList());
                    param.setAdIdList(aggregateIdsTemporary.getAdIdList());
                }
                GetAggregateKeywordHourReportDataResponsePb.GetAggregateKeywordHourReportDataResponse.HourReport.Builder reportBuilder =
                        GetAggregateKeywordHourReportDataResponsePb.GetAggregateKeywordHourReportDataResponse.HourReport.newBuilder();
                List<AdKeywordAndTargetHourVo> list = new ArrayList<>();

                if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY) {
                    //新版支持类型多选，后续删除旧分支即可
//                    if (Constants.SP.equalsIgnoreCase(param.getAdType())) {
//                        list = amazonAdKeywordHourReportService.getViewHourListAll(param.getPuid(), param);
//                    } else {
                        list = amazonAdKeywordHourReportService.getViewHourListAllType(param.getPuid(), param);
//                    }
                }
                BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getPuid(), param.getShopIdList(), param.getStartDate().replace("-",""), param.getEndDate().replace("-",""));
                if (CollectionUtils.isNotEmpty(list)) {
                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdKeywordAndTargetHourVo.class);
                    if (isSorted) {
                        PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
                    }
                }
                reportBuilder.addAllList(list.stream().filter(Objects::nonNull)
                        .map(e -> PbUtil.toHourReportPb(e, shopSalesByDate, isVc)).collect(Collectors.toList()));
                reportBuilder.setSummary(PbUtil.toHourReportPb(summaryTargetHourVo(list, shopSalesByDate), shopSalesByDate, isVc));
                reportBuilder.addAllChart(ReportChartUtil.getHourChartData(list, false));
                //对比数据,chart图数据
                if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY
                        && param.getIsCompare() != null && param.getIsCompare() == 1) {
                    List<AdKeywordAndTargetHourVo> compareHourVos = list.stream().map(item-> {
                        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                        vo.setLabel(item.getLabel());
                        vo.setClicks(item.getClicksCompare());
                        vo.setImpressions(item.getImpressionsCompare());
                        vo.setAdSale(item.getAdSaleCompare());
                        vo.setAdCost(item.getAdCostCompare());
                        vo.setAdOrderNum(item.getAdOrderNumCompare());
                        vo.setAdSaleNum(item.getAdSaleNumCompare());
                        vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                        vo.setAcos(item.getAcosCompare());
                        vo.setRoas(item.getRoasCompare());
                        vo.setCtr(item.getCtrCompare());
                        vo.setCvr(item.getCvrCompare());
                        return vo;
                    }).collect(Collectors.toList());
                    reportBuilder.addAllChart(ReportChartUtil.getHourChartData(compareHourVos, true));
                }

                builder.setCode(Int32Value.of(Result.SUCCESS));
                builder.setData(reportBuilder.build());
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    /**
     * 查询投放小时级数据
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAggregateTargetHourReportData(
            GetAggregateTargetHourReportDataRequestPb.GetAggregateTargetHourReportDataRequest request,
            StreamObserver<GetAggregateTargetHourReportDataResponsePb.GetAggregateTargetHourReportDataResponse> responseObserver) {
        try {
            GetAggregateTargetHourReportDataResponsePb.GetAggregateTargetHourReportDataResponse.Builder builder =
                    GetAggregateTargetHourReportDataResponsePb.GetAggregateTargetHourReportDataResponse.newBuilder();
            //检查参数
            if (!request.hasPuid() || CollectionUtils.isEmpty(request.getShopIdList()) || !request.hasEndDate() || !request.hasStartDate()
                    || (request.hasIsCompare() && request.getIsCompare().getValue() == 1 &&
                    (!request.hasStartDateCompare() || !request.hasEndDateCompare()))) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                List<VcShopAuth> byIdAndPuid = vcShopAuthDao.getListByIdList(request.getShopIdList());
                boolean isVc = CollectionUtils.isNotEmpty(byIdAndPuid);
                TargetViewHourParam param = new TargetViewHourParam();
                BeanUtils.copyProperties(request, param);
                param.setPuid(request.getPuid().getValue());
                param.setIsCompare(request.getIsCompare().getValue());
                param.setMarketplaceId(request.getMarketplaceId());
                param.setIsAudienceTarget(request.getIsAudienceTarget());

                AggregateIdsTemporary aggregateIdsTemporary = cpcPageIdsHandler.getAggregateIdsTemporary(request.getPageSign(), "");
                if (aggregateIdsTemporary != null) {
                    param.setTargetIdList(aggregateIdsTemporary.getIdList());
                    param.setAdIdList(aggregateIdsTemporary.getAdIdList());
                }

                GetAggregateTargetHourReportDataResponsePb.GetAggregateTargetHourReportDataResponse.HourReport.Builder reportBuilder =
                        GetAggregateTargetHourReportDataResponsePb.GetAggregateTargetHourReportDataResponse.HourReport.newBuilder();
                List<AdKeywordAndTargetHourVo> list = new ArrayList<>();
                if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY) {
                    //新版支持类型多选，后续删除旧分支即可
//                    if (Constants.SP.equalsIgnoreCase(param.getAdType())) {
//                        list = amazonAdTargetHourReportService.getViewHourListAll(param.getPuid(), param);
//                    } else {
                        list = amazonAdTargetHourReportService.getViewHourListAllType(param.getPuid(), param);
//                    }
                }
                BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getPuid(), param.getShopIdList(), param.getStartDate().replace("-",""), param.getEndDate().replace("-",""));
                if (CollectionUtils.isNotEmpty(list)) {
                    boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                            Constants.isADOrderField(param.getOrderField(), AdKeywordAndTargetHourVo.class);
                    if (isSorted) {
                        PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
                    }
                }
                reportBuilder.addAllList(list.stream().filter(Objects::nonNull)
                        .map(e -> PbUtil.toHourReportPb(e, shopSalesByDate, isVc)).collect(Collectors.toList()));
                reportBuilder.setSummary(PbUtil.toHourReportPb(summaryTargetHourVo(list, shopSalesByDate), shopSalesByDate, isVc));
                reportBuilder.addAllChart(ReportChartUtil.getHourChartData(list, false));

                if (request.getDateModel() == ReportDateModelPb.ReportDateModel.HOURLY
                        && param.getIsCompare() != null && param.getIsCompare() == 1) {
                    List<AdKeywordAndTargetHourVo> compareHourVos = list.stream().map(item-> {
                        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                        vo.setLabel(item.getLabel());
                        vo.setClicks(item.getClicksCompare());
                        vo.setImpressions(item.getImpressionsCompare());
                        vo.setAdSale(item.getAdSaleCompare());
                        vo.setAdCost(item.getAdCostCompare());
                        vo.setAdOrderNum(item.getAdOrderNumCompare());
                        vo.setAdSaleNum(item.getAdSaleNumCompare());
                        vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                        vo.setAcos(item.getAcosCompare());
                        vo.setRoas(item.getRoasCompare());
                        vo.setCtr(item.getCtrCompare());
                        vo.setCvr(item.getCvrCompare());
                        return vo;
                    }).collect(Collectors.toList());
                    reportBuilder.addAllChart(ReportChartUtil.getHourChartData(compareHourVos, true));
                }

                builder.setCode(Int32Value.of(Result.SUCCESS));
                builder.setData(reportBuilder.build());
            }

            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

}
