package com.meiyunji.sponsored.api.product;

import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.rpc.product.ProductEligibilityRequest;
import com.meiyunji.sponsored.rpc.product.ProductEligibilityResponse;
import com.meiyunji.sponsored.rpc.product.RPCProductServiceGrpc;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dto.CpcProductDto;
import com.meiyunji.sponsored.service.cpc.dto.ProductStatusDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.ICpcProductApiService;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-07-09  09:45
 */
@Slf4j
@GRpcService
public class CpcProductRpcService extends RPCProductServiceGrpc.RPCProductServiceImplBase {
    @Autowired
    private ICpcProductApiService cpcProductApiService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;

    public void productEligibility(ProductEligibilityRequest request, StreamObserver<ProductEligibilityResponse> responseObserver) {
        ProductEligibilityResponse.Builder builder = ProductEligibilityResponse.newBuilder();
        ProductEligibilityResponse.ProductResult.Builder productResult = ProductEligibilityResponse.ProductResult.newBuilder();
        //做参数校验
        if (!request.hasPuid() || !request.hasShopId() || !request.hasAdType()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误，请联系管理员");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        if (CollectionUtils.isEmpty(request.getProductList())) {
            builder.setCode(Int32Value.of(Result.SUCCESS));
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        //拼装dto调api接口
        List<CpcProductDto> dtoList = request.getProductList().stream().map(e -> new CpcProductDto(e.getAsin(), e.getSku())).collect(Collectors.toList());
        List<ProductStatusDto> resultDtoList = cpcProductApiService.productEligibility(request.getPuid(), request.getShopId(),
                request.getAdType(), request.getLocale(), dtoList);
        if (CollectionUtils.isNotEmpty(resultDtoList)) {
            productResult.addAllProductStatus(resultDtoList.stream().map(e -> {
                ProductEligibilityResponse.ProductStatus.Builder productStatus = ProductEligibilityResponse.ProductStatus.newBuilder();
                productStatus.setAsin(e.getAsin());
                if (e.getSku() != null) {
                    productStatus.setSku(e.getSku());
                }
                return productStatus.build();
            }).collect(Collectors.toList()));
        }
        builder.setData(productResult.build());
        builder.setCode(Int32Value.of(Result.SUCCESS));
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 查询产品可用状态
     * @param request
     * @param responseObserver
     */
    @Override
    public void getProductEligibility(ProductEligibilityRequest request, StreamObserver<ProductEligibilityResponse> responseObserver) {
        ProductEligibilityResponse.Builder builder = ProductEligibilityResponse.newBuilder();
        ProductEligibilityResponse.ProductResult.Builder productResult = ProductEligibilityResponse.ProductResult.newBuilder();
        //做参数校验
        if (!request.hasPuid() || !request.hasShopId() || !request.hasAdType()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误，请联系管理员");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        if (CollectionUtils.isEmpty(request.getProductList())) {
            builder.setCode(Int32Value.of(Result.SUCCESS));
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId(), request.getPuid());
        if (shopAuth == null) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("店铺不存在，请联系管理员");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(request.getPuid(), request.getShopId());
        if (profile == null) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("店铺未授权，请联系管理员");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        //拼装dto调api接口
        List<OdsProduct> dtoList = request.getProductList().stream().map(e -> {
            OdsProduct odsProduct = new OdsProduct();
            odsProduct.setAsin(e.getAsin());
            odsProduct.setSku(e.getSku());
            return odsProduct;
        }).collect(Collectors.toList());
        List<ProductStatusDto> resultDtoList = cpcProductApiService.getProductEligibility(shopAuth, profile, request.getAdType(), request.getLocale(), dtoList);
        List<ProductEligibilityResponse.ProductStatus> productStatuses = new ArrayList<>();
        for (ProductStatusDto productStatusDto : resultDtoList) {
            ProductEligibilityResponse.ProductStatus.Builder productStatus = ProductEligibilityResponse.ProductStatus.newBuilder();
            if (productStatusDto.getSku() == null || productStatusDto.getAsin() == null) {
                continue;
            }
            productStatus.setAsin(productStatusDto.getAsin());
            productStatus.setSku(productStatusDto.getSku());
            productStatus.setStatus(productStatusDto.getStatus());
            productStatuses.add(productStatus.build());
        }
        productResult.addAllProductStatus(productStatuses);

        builder.setData(productResult.build());
        builder.setCode(Int32Value.of(Result.SUCCESS));
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
