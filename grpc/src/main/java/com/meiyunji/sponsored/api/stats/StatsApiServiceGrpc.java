package com.meiyunji.sponsored.api.stats;

import com.amazon.advertising.sb.mode.campaigm.Creative;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.ISlaveScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveShopAuthDao;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.vo.ReportParam;
import com.meiyunji.sponsored.rpc.statsApi.*;
import com.meiyunji.sponsored.service.doris.dao.*;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AdGroupAndAdIdDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.AdKeywordStreamDataParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.AsinStreamDataParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.IKeywordViewService;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2021/11/16 19:00
 * @describe: 数据服务接口API
 */
@GRpcService
@Slf4j
public class StatsApiServiceGrpc extends RPCStatsApiServiceGrpc.RPCStatsApiServiceImplBase {

    @Resource
    private IAmazonAdCampaignReportDao amazonAdCampaignReportDao;

    @Resource
    private IAmazonAdCampaignDao amazonAdCampaignDao;

    @Resource
    private IAmazonAdGroupDao amazonAdGroupDao;

    @Resource
    private IAmazonAdProductReportDao amazonAdProductReportDao;


    @Resource
    private IAmazonAdSbCampaignOldReportDao amazonAdSbCampaignReportDao;


    @Resource
    private IAmazonAdSdProductReportDao amazonAdSdProductReportDao;


    @Resource
    private IAmazonAdShopReportDao amazonAdShopReportDao;


    @Resource
    private IAmazonSbAdCampaignDao sbAdCampaignDao;

    @Resource
    private IAmazonSbAdsDao amazonSbAdsDao;

    @Resource
    private IAmazonAdSbAdsReportDao amazonAdSbAdsReportDao;

    @Resource
    private IKeywordViewService keywordViewService;
    @Resource
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;
    @Resource
    private IOdsAmazonAdTargetingSdDao odsAmazonAdTargetingSdDao;
    @Resource
    private IOdsAmazonAdProductSdDao odsAmazonAdProductSdDao;
    @Resource
    private IOdsAmazonAdProductDao odsAmazonAdProductDao;
    @Resource
    private IOdsAmazonAdTargetingDao odsAmazonAdTargetingDao;
    @Resource
    private IOdsAmazonAdKeywordDao odsAmazonAdKeywordDao;
    @Resource
    private ISlaveScVcShopAuthDao slaveShopAuthDao;


    @Override
    public void getSpCampaignReportSumByDate(GetSpCampaignReportSumByDateRequest request, StreamObserver<GetSpCampaignReportSumByDateResponse> responseObserver) {

        GetSpCampaignReportSumByDateResponse.Builder builder = GetSpCampaignReportSumByDateResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasMarketplaceId() || !request.hasStartDate() || !request.hasEndDate()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            List<AmazonAdCampaignAllReport> list = amazonAdCampaignReportDao.getSumByDate(request.getPuid(), request.getShopId(), request.getMarketplaceId(), request.getStartDate(), request.getEndDate());
            //po转message
            if (CollectionUtils.isNotEmpty(list)) {
                List<AmazonAdCampaignRpcReport> rpcVos = list.stream().filter(Objects::nonNull).map(item -> {
                    AmazonAdCampaignRpcReport.Builder voBuilder = AmazonAdCampaignRpcReport.newBuilder();

                    if (item.getPuid() != null) {
                        voBuilder.setPuid(item.getPuid());
                    }
                    if (item.getShopId() != null) {
                        voBuilder.setShopId(item.getShopId());
                    }
                    if (item.getMarketplaceId() != null) {
                        voBuilder.setMarketplaceId(item.getMarketplaceId());
                    }
                    if (item.getCampaignId() != null) {
                        voBuilder.setCampaignId(item.getCampaignId());
                    }
                    if (item.getPlacement() != null) {
                        voBuilder.setPlacement(item.getPlacement());
                    }
                    if (item.getCampaignName() != null) {
                        voBuilder.setName(item.getCampaignName());
                    }
                    if (item.getCampaignStatus() != null) {
                        voBuilder.setState(item.getCampaignStatus());
                    }
                    if (item.getCampaignBudget() != null) {
                        voBuilder.setDailyBudget(item.getCampaignBudget());
                    }
                    if (item.getCountDate() != null) {
                        voBuilder.setCountDate(item.getCountDate());
                    }
                    if (item.getCost() != null) {
                        voBuilder.setCost(item.getCost().doubleValue());
                    }
                    if (item.getTotalSales() != null) {
                        voBuilder.setTotalSales(item.getTotalSales().doubleValue());
                    }
                    if (item.getAdSales() != null) {
                        voBuilder.setAdSales(item.getAdSales().doubleValue());
                    }
                    if (item.getImpressions() != null) {
                        voBuilder.setImpressions(item.getImpressions());
                    }
                    if (item.getClicks() != null) {
                        voBuilder.setClicks(item.getClicks());
                    }
                    if (item.getOrderNum() != null) {
                        voBuilder.setSaleNum(item.getOrderNum());
                    }
                    if (item.getAdOrderNum() != null) {
                        voBuilder.setAdSaleNum(item.getAdOrderNum());
                    }
                    if (item.getSaleNum() != null) {
                        voBuilder.setOrderNum(item.getSaleNum());
                    }
                    if (item.getAdSaleNum() != null) {
                        voBuilder.setAdOrderNum(item.getAdSaleNum());
                    }
                    if (item.getTotalSaleNum() != null) {
                        voBuilder.setTotalSaleNum(item.getTotalSaleNum());
                    }
                    if (item.getCreateTime() != null) {
                        voBuilder.setCreateTime(DateUtil.dateToStrWithFormat(item.getCreateTime(), DateUtil.PATTERN_DATE_TIME));
                    }
                    if (item.getUpdateTime() != null) {
                        voBuilder.setUpdateTime(DateUtil.dateToStrWithFormat(item.getUpdateTime(), DateUtil.PATTERN_DATE_TIME));
                    }
                    return voBuilder.build();

                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
            builder.setCode(Result.SUCCESS);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getSpCampaignByCampaignId(GetSpCampaignByCampaignIdRequest request, StreamObserver<GetSpCampaignByCampaignIdResponse> responseObserver) {
        GetSpCampaignByCampaignIdResponse.Builder builder = GetSpCampaignByCampaignIdResponse.newBuilder();

        if (!request.hasPuid() || !request.hasShopId() || !request.hasCampaignId()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            AmazonAdCampaignAll campaign = amazonAdCampaignDao.getCampaignByCampaignId(request.getPuid(), request.getShopId(), request.getCampaignId());
            if (campaign != null) {
                //po转message
                AmazonAdSpCampaignRpcVo.Builder voBuilder = AmazonAdSpCampaignRpcVo.newBuilder();
                if (campaign.getCampaignId() != null) {
                    voBuilder.setCampaignId(campaign.getCampaignId());
                }
                if (campaign.getName() != null) {
                    voBuilder.setName(campaign.getName());
                }
                if (campaign.getCampaignType() != null) {
                    voBuilder.setCampaignType(campaign.getCampaignType());
                }
                if (campaign.getTargetingType() != null) {
                    voBuilder.setTargetingType(campaign.getTargetingType());
                }
                if (campaign.getId() != null) {
                    voBuilder.setId(campaign.getId());
                }
                builder.setData(voBuilder.build());
            }
            builder.setCode(Result.SUCCESS);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getSpGroupByAdGroupId(GetSpGroupByAdGroupIdRequest request, StreamObserver<GetSpGroupByAdGroupIdResponse> responseObserver) {
        GetSpGroupByAdGroupIdResponse.Builder builder = GetSpGroupByAdGroupIdResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasGroupId()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            AmazonAdGroup group = amazonAdGroupDao.getByAdGroupId(request.getPuid(), request.getShopId(), request.getGroupId());
            if (group != null) {
                //po转message
                AmazonSpAdGroupRpcVo.Builder voBuilder = AmazonSpAdGroupRpcVo.newBuilder();
                if (group.getAdGroupId() != null) {
                    voBuilder.setGroupId(group.getAdGroupId());
                }
                if (group.getName() != null) {
                    voBuilder.setName(group.getName());
                }
                if (group.getAdGroupType() != null) {
                    voBuilder.setGroupType(group.getAdGroupType());
                }
                if (group.getId() != null) {
                    voBuilder.setId(group.getId());
                }
                builder.setData(voBuilder.build());
            }
            builder.setCode(Result.SUCCESS);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getSpProductReportSumReport(GetSpProductReportSumReportRequest request, StreamObserver<GetSpProductReportSumReportResponse> responseObserver) {
        GetSpProductReportSumReportResponse.Builder builder = GetSpProductReportSumReportResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasMarketplaceId() || !request.hasEndDate() ||
                !request.hasStartDate() || !request.hasTabId() || !request.hasTabType()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            ReportParam param = new ReportParam();
            param.setEndDate(request.getEndDate());
            param.setStartDate(request.getStartDate());
            param.setTabId(request.getTabId());
            param.setTabType(request.getTabType());

            AmazonAdProductReport report = amazonAdProductReportDao.getSumReport(request.getPuid(), request.getShopId(), request.getMarketplaceId(), param);
            if (report != null) {
                //po转message
                AmazonAdProductReportRpcVo.Builder voBuilder = convertSpProductReportToMessage(report);
                builder.setData(voBuilder.build());
            }
            builder.setCode(Result.SUCCESS);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getListSpProductReportSumReport(GetListSpProductReportSumReportRequest request, StreamObserver<GetListSpProductReportSumReportResponse> responseObserver) {
        GetListSpProductReportSumReportResponse.Builder builder = GetListSpProductReportSumReportResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasMarketplaceId() || !request.hasEndDate() ||
                !request.hasStartDate() || !request.hasTabId() || !request.hasTabType()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            ReportParam param = new ReportParam();
            param.setEndDate(request.getEndDate());
            param.setStartDate(request.getStartDate());
            param.setTabId(request.getTabId());
            param.setTabType(request.getTabType());
            param.setTabIds(request.getTabId());

            List<AmazonAdProductReport> report = amazonAdProductReportDao.getSumReports(request.getPuid(), request.getShopId(), request.getMarketplaceId(), param);
            if (CollectionUtils.isNotEmpty(report)) {
                List<AmazonAdProductReportRpcVo> collect = report.stream().map(e->convertSpProductReportToMessage(e).build()).collect(Collectors.toList());

                builder.addAllData(collect);
            }
            builder.setCode(Result.SUCCESS);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getListSdProductReportSkuSumData(GetListSdProductReportSkuSumDataRequest request, StreamObserver<GetListSdProductReportSkuSumDataResponse> responseObserver) {

        GetListSdProductReportSkuSumDataResponse.Builder builder = GetListSdProductReportSkuSumDataResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasMarketplaceId() || !request.hasEndDate() ||
                !request.hasStartDate()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            List<AmazonAdSdProductReport> report = amazonAdSdProductReportDao.getListSkuSumData(request.getPuid(), request.getShopId(), request.getMarketplaceId(), request.getStartDate(), request.getEndDate(), request.hasMsku() ? StringUtil.stringToList(request.getMsku(),"%±%") : null);
            if (CollectionUtils.isNotEmpty(report)) {
                List<AmazonAdSdProductReportRpcVo> collect = report.stream().map(e -> convertSdProductReportToMessage(e).build()).collect(Collectors.toList());
                builder.addAllData(collect);
            }
            builder.setCode(Result.SUCCESS);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private AmazonAdProductReportRpcVo.Builder convertSpProductReportToMessage(AmazonAdProductReport report) {
        AmazonAdProductReportRpcVo.Builder voBuilder = AmazonAdProductReportRpcVo.newBuilder();
        if (report.getCountDate() != null) {
            voBuilder.setCountDate(report.getCountDate());
        }
        if (report.getCost() != null) {
            voBuilder.setCost(report.getCost().doubleValue());
        }
        if (report.getTotalSales() != null) {
            voBuilder.setTotalSales(report.getTotalSales().doubleValue());
        }
        if (report.getAdSales() != null) {
            voBuilder.setAdSales(report.getAdSales().doubleValue());
        }
        if (report.getImpressions() != null) {
            voBuilder.setImpressions(report.getImpressions());
        }
        if (report.getClicks() != null) {
            voBuilder.setClicks(report.getClicks());
        }
        if (report.getOrderNum() != null) {
            voBuilder.setOrderNum(report.getOrderNum());
        }
        if (report.getAdOrderNum() != null) {
            voBuilder.setAdOrderNum(report.getOrderNum());
        }
        if (report.getSaleNum() != null) {
            voBuilder.setSaleNum(report.getSaleNum());
        }
        if (report.getAdSaleNum() != null) {
            voBuilder.setAdSaleNum(report.getAdSaleNum());
        }
        if (report.getTotalSaleNum() != null) {
            voBuilder.setTotalSaleNum(report.getTotalSaleNum());
        }
        if (report.getSku() != null) {
            voBuilder.setSku(report.getSku());
        }
        if (report.getAsin() != null) {
            voBuilder.setAsin(report.getAsin());
        }
        if (report.getParentAsin() != null) {
            voBuilder.setParentAsin(report.getParentAsin());
        }
        if (report.getCpc() != null) {
            voBuilder.setCpc(report.getCpc().doubleValue());
        }
        if (report.getAcos() != null) {
            voBuilder.setAcos(report.getAcos().doubleValue());
        }
        if (report.getClickRate() != null) {
            voBuilder.setClickRate(report.getClickRate());
        }
        if (report.getSalesConversionRate() != null) {
            voBuilder.setSalesConversionRate(report.getSalesConversionRate());
        }
        return voBuilder;
    }

    @Override
    public void getAdSpProductReportsByUpdateTime(GetAdSpProductReportsByUpdateTimeRequest request, StreamObserver<GetAdSpProductReportsByUpdateTimeResponse> responseObserver) {
        GetAdSpProductReportsByUpdateTimeResponse.Builder builder = GetAdSpProductReportsByUpdateTimeResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasMarketplaceId() || !request.hasEndDate() ||
                !request.hasStartDate()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            List<AmazonAdProductReport> reports = amazonAdProductReportDao.listByCondition(request.getPuid(), new ConditionBuilder.Builder()
                        .equalTo("puid", request.getPuid())
                        .equalTo("shop_id", request.getShopId())
                        .equalTo("marketplace_id", request.getMarketplaceId())
                        .greaterThanOrEqualTo("update_time", request.getStartDate())
                        .lessThanOrEqualTo("update_time", request.getEndDate())
                        .build());

            if (CollectionUtils.isNotEmpty(reports)) {
                //po转message
                List<AmazonAdProductReportRpcVo> rpcVos = reports.stream().filter(Objects::nonNull).map(item -> {
                    AmazonAdProductReportRpcVo.Builder voBuilder = convertSpProductReportToMessage(item);
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
            builder.setCode(Result.SUCCESS);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAdSpProductReportsByCountDate(GetAdSpProductReportsByCountDateRequest request, StreamObserver<GetAdSpProductReportsByCountDateResponse> responseObserver) {
        GetAdSpProductReportsByCountDateResponse.Builder builder = GetAdSpProductReportsByCountDateResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasMarketplaceId() || !request.hasEndDate() ||
                !request.hasStartDate()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            List<AmazonAdProductReport> reports = amazonAdProductReportDao.listByCondition(request.getPuid(), new ConditionBuilder.Builder()
                        .equalTo("puid", request.getPuid())
                        .equalTo("shop_id", request.getShopId())
                        .equalTo("marketplace_id", request.getMarketplaceId())
                        .greaterThanOrEqualTo("count_date", request.getStartDate())
                        .lessThanOrEqualTo("count_date", request.getEndDate())
                        .build()
                );

            if (CollectionUtils.isNotEmpty(reports)) {
                List<AmazonAdProductReportRpcVo> rpcVos = reports.stream().filter(Objects::nonNull).map(item -> {
                    AmazonAdProductReportRpcVo.Builder voBuilder = convertSpProductReportToMessage(item);
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
            builder.setCode(Result.SUCCESS);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getSbCampaignReportSumByDate(GetSbCampaignReportSumByDateRequest request, StreamObserver<GetSbCampaignReportSumByDateResponse> responseObserver) {
        GetSbCampaignReportSumByDateResponse.Builder builder = GetSbCampaignReportSumByDateResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasMarketplaceId() || !request.hasEndDate() ||
                !request.hasStartDate()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            AmazonAdSbCampaignReport report = amazonAdSbCampaignReportDao.getSumReportByDate(request.getPuid(), request.getShopId(), request.getMarketplaceId(), request.getStartDate(), request.getEndDate());
            if (report != null) {
                //po转message
                AmazonAdSbCampaignReportRpcVo.Builder voBuilder = AmazonAdSbCampaignReportRpcVo.newBuilder();
                if (report.getImpressions() != null) {
                    voBuilder.setImpressions(report.getImpressions());
                }
                if (report.getClicks() != null) {
                    voBuilder.setClicks(report.getClicks());
                }
                if (report.getCost() != null) {
                    voBuilder.setCost(report.getCost().doubleValue());
                }
                if (report.getSales14d() != null) {
                    voBuilder.setSales14D(report.getSales14d().doubleValue());
                }
                if (report.getConversions14d() != null) {
                    voBuilder.setConversions14D(report.getConversions14d());
                }
                if (report.getConversions14dSameSKU() != null) {
                    voBuilder.setConversions14DSameSKU(report.getConversions14dSameSKU());
                }
                if (report.getUnitsSold14d() != null) {
                    voBuilder.setUnitsSold14D(report.getUnitsSold14d());
                }
                builder.setData(voBuilder.build());
            }
            builder.setCode(Result.SUCCESS);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getSdProductReportSkuSumData(GetSdProductReportSkuSumDataRequest request, StreamObserver<GetSdProductReportSkuSumDataResponse> responseObserver) {
        GetSdProductReportSkuSumDataResponse.Builder builder = GetSdProductReportSkuSumDataResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasMarketplaceId() || !request.hasEndDate() ||
                !request.hasStartDate()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            AmazonAdSdProductReport report = amazonAdSdProductReportDao.getSkuSumData(request.getPuid(), request.getShopId(), request.getMarketplaceId(), request.getStartDate(), request.getEndDate(), request.hasMsku() ? request.getMsku() : null);
            if (report != null) {
                AmazonAdSdProductReportRpcVo.Builder voBuilder = convertSdProductReportToMessage(report);
                builder.setData(voBuilder.build());
            }
            builder.setCode(Result.SUCCESS);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private AmazonAdSdProductReportRpcVo.Builder convertSdProductReportToMessage(AmazonAdSdProductReport report) {
        AmazonAdSdProductReportRpcVo.Builder voBuilder = AmazonAdSdProductReportRpcVo.newBuilder();
        if (report.getImpressions() != null) {
            voBuilder.setImpressions(report.getImpressions());
        }
        if (report.getClicks() != null) {
            voBuilder.setClicks(report.getClicks());
        }
        if (report.getCost() != null) {
            voBuilder.setCost(report.getCost().doubleValue());
        }
        if (report.getSales14d() != null) {
            voBuilder.setSales14D(report.getSales14d().doubleValue());
        }
        if (report.getSales14d() != null) {
            voBuilder.setSales14D(report.getSales14d().doubleValue());
        }
        if (report.getConversions14d() != null) {
            voBuilder.setConversions14D(report.getConversions14d());
        }
        if (report.getConversions14dSameSKU() != null) {
            voBuilder.setConversions14DSameSKU(report.getConversions14dSameSKU());
        }
        if (report.getUnitsOrdered14d() != null) {
            voBuilder.setUnitsSold14D(report.getUnitsOrdered14d());
        }
        if (report.getSku() != null) {
            voBuilder.setSku(report.getSku());
        }
        if (report.getAsin() != null) {
            voBuilder.setAsin(report.getAsin());
        }
        if (report.getParentAsin() != null) {
            voBuilder.setParentAsin(report.getParentAsin());
        }
        if (report.getCountDate() != null) {
            voBuilder.setCountDate(report.getCountDate());
        }
        return voBuilder;
    }

    @Override
    public void getAdSdProductReportsByUpdateTime(GetAdSdProductReportsByUpdateTimeRequest request, StreamObserver<GetAdSdProductReportsByUpdateTimeResponse> responseObserver) {
        GetAdSdProductReportsByUpdateTimeResponse.Builder builder = GetAdSdProductReportsByUpdateTimeResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasMarketplaceId() || !request.hasEndDate() ||
                !request.hasStartDate()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            ConditionBuilder build = new ConditionBuilder.Builder()
                    .equalTo("puid", request.getPuid())
                    .equalTo("shop_id", request.getShopId())
                    .equalTo("marketplace_id", request.getMarketplaceId())
                    .greaterThanOrEqualTo("update_time", request.getStartDate())
                    .lessThanOrEqualTo("update_time", request.getEndDate())
                    .build();
            List<AmazonAdSdProductReport> reports = amazonAdSdProductReportDao.listByCondition(request.getPuid(), build);
            if (CollectionUtils.isNotEmpty(reports)) {
                //po转message
                List<AmazonAdSdProductReportRpcVo> rpcVos = reports.stream().filter(Objects::nonNull).map(item -> {
                    AmazonAdSdProductReportRpcVo.Builder voBuilder = convertSdProductReportToMessage(item);
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
            builder.setCode(Result.SUCCESS);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAdSdProductReportsByCountDate(GetAdSdProductReportsByCountDateRequest request, StreamObserver<GetAdSdProductReportsByCountDateResponse> responseObserver) {
        GetAdSdProductReportsByCountDateResponse.Builder builder = GetAdSdProductReportsByCountDateResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasMarketplaceId() || !request.hasEndDate() ||
                !request.hasStartDate()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            ConditionBuilder build = new ConditionBuilder.Builder()
                    .equalTo("puid", request.getPuid())
                    .equalTo("shop_id", request.getShopId())
                    .equalTo("marketplace_id", request.getMarketplaceId())
                    .greaterThanOrEqualTo("count_date", request.getStartDate())
                    .lessThanOrEqualTo("count_date", request.getEndDate())
                    .build();
            List<AmazonAdSdProductReport> reports = amazonAdSdProductReportDao.listByCondition(request.getPuid(), build);
            if (CollectionUtils.isNotEmpty(reports)) {
                //po转message
                List<AmazonAdSdProductReportRpcVo> rpcVos = reports.stream().filter(Objects::nonNull).map(item -> {
                    AmazonAdSdProductReportRpcVo.Builder voBuilder = convertSdProductReportToMessage(item);
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
            builder.setCode(Result.SUCCESS);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getShopSumReport(GetShopSumReportRequest request, StreamObserver<GetShopSumReportResponse> responseObserver) {
        GetShopSumReportResponse.Builder builder = GetShopSumReportResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasMarketplaceId() || !request.hasEndDate() ||
                !request.hasStartDate()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            AmazonAdShopReport report = amazonAdShopReportDao.getSumReport(request.getPuid(), request.getShopId(), request.getMarketplaceId(), request.getStartDate(), request.getEndDate());
            if (report != null) {
                //po转message
                AmazonAdShopReportRpcVo.Builder voBuilder = convertShopReportToMessage(report);
                builder.setData(voBuilder.build());
            }
            builder.setCode(Result.SUCCESS);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private AmazonAdShopReportRpcVo.Builder convertShopReportToMessage(AmazonAdShopReport report) {
        AmazonAdShopReportRpcVo.Builder voBuilder = AmazonAdShopReportRpcVo.newBuilder();
        if (report.getId() != null) {
            voBuilder.setId(report.getId());
        }
        if (report.getPuid() != null) {
            voBuilder.setPuid(report.getPuid());
        }
        if (report.getShopId() != null) {
            voBuilder.setShopId(report.getShopId());
        }
        if (report.getMarketplaceId() != null) {
            voBuilder.setMarketplaceId(report.getMarketplaceId());
        }
        if (report.getNaturalSales() != null) {
            voBuilder.setNaturalSales(report.getNaturalSales().doubleValue());
        }
        if (report.getNaturalClicks() != null) {
            voBuilder.setNaturalClicks(report.getNaturalClicks());
        }
        if (report.getNaturalOrderNum() != null) {
            voBuilder.setNaturalOrderNum(report.getNaturalOrderNum());
        }
        if (report.getCountDate() != null) {
            voBuilder.setCountDate(report.getCountDate());
        }
        if (report.getCost() != null) {
            voBuilder.setCost(report.getCost().doubleValue());
        }
        if (report.getTotalSales() != null) {
            voBuilder.setTotalSales(report.getTotalSales().doubleValue());
        }
        if (report.getAdSales() != null) {
            voBuilder.setAdSales(report.getAdSales().doubleValue());
        }
        if (report.getImpressions() != null) {
            voBuilder.setImpressions(report.getImpressions());
        }
        if (report.getClicks() != null) {
            voBuilder.setClicks(report.getClicks());
        }
        if (report.getOrderNum() != null) {
            voBuilder.setOrderNum(report.getOrderNum());
        }
        if (report.getAdOrderNum() != null) {
            voBuilder.setAdOrderNum(report.getAdOrderNum());
        }
        if (report.getSaleNum() != null) {
            voBuilder.setSaleNum(report.getSaleNum());
        }
        if (report.getTotalSaleNum() != null) {
            voBuilder.setTotalSaleNum(report.getTotalSaleNum());
        }
        return voBuilder;
    }

    @Override
    public void getIndexCpcData(GetIndexCpcDataRequest request, StreamObserver<GetIndexCpcDataResponse> responseObserver) {
        GetIndexCpcDataResponse.Builder builder = GetIndexCpcDataResponse.newBuilder();
        if (!request.hasPuid() || !request.hasStartStr() || !request.hasEndStr() || !request.hasGroupBy() ||
                !request.hasCurrency() || CollectionUtils.isEmpty(request.getShopIdList())) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            List<AmazonAdShopReport> list = amazonAdShopReportDao.getIndexCpcData(request.getPuid(), request.getShopIdList(), request.getStartStr(), request.getEndStr(), request.getGroupBy(), request.getCurrency());
            if (CollectionUtils.isNotEmpty(list)) {
                //po转message
                List<AmazonAdShopReportRpcVo> rpcVos = list.stream().filter(Objects::nonNull).map(item -> {
                    AmazonAdShopReportRpcVo.Builder voBuilder = convertShopReportToMessage(item);
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
            builder.setCode(Result.SUCCESS);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 此方法涉及查询视频和商品集，查询时间范围只会查近期，故而直接切到查ads报告表数据
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAdSbReportsByUpdateTime(GetAdSbReportsByUpdateTimeRequest request, StreamObserver<GetAdSbReportsByUpdateTimeResponse> responseObserver) {
        GetAdSbReportsByUpdateTimeResponse.Builder builder = GetAdSbReportsByUpdateTimeResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasEndDate() || !request.hasStartDate()
                || StringUtils.isBlank(request.getType())) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            ConditionBuilder build = new ConditionBuilder.Builder()
                    .equalTo("puid", request.getPuid())
                    .equalTo("shop_id", request.getShopId())
                    .equalTo("ad_format", request.getType())
                    .greaterThanOrEqualTo("update_time", request.getStartDate())
                    .lessThanOrEqualTo("update_time", request.getEndDate())
                    .build();

            List<AmazonAdSbAdsReport> reportList = null;
            reportList = amazonAdSbAdsReportDao.listByCondition(request.getPuid(), build);
            if (CollectionUtils.isNotEmpty(reportList)) {
                Map<String, String> asinMap = new HashMap<>();

                List<AmazonAdSbReportRpcVo> rpcVos = new ArrayList<>(reportList.size());
                for (AmazonAdSbAdsReport report : reportList) {
                    AmazonAdSbReportRpcVo.Builder voBuilder = AmazonAdSbReportRpcVo.newBuilder();

                    String asin = null;
                    if (asinMap.containsKey(report.getQueryId())) {
                        asin = asinMap.get(report.getQueryId());
                    } else {
                        String asins = amazonSbAdsDao.getSbCreativeByQueryId(report.getPuid(), report.getShopId(), report.getQueryId());
                        if (StringUtils.isNotBlank(asins)) {
                            List<String> list = StringUtil.splitStr(asins, ",");
                            if (CollectionUtils.isNotEmpty(list)) {

                                asin = list.get(0);

                                if (StringUtils.isNotBlank(asin)) {
                                    asinMap.put(report.getQueryId(), asin);
                                }
                            }
                        }
                    }

                    if (StringUtils.isNotBlank(asin)) {
                        voBuilder.setAsin(asin);
                    }

                    if (report.getCountDate() != null) {
                        voBuilder.setCountDate(report.getCountDate());
                    }

                    rpcVos.add(voBuilder.build());
                }
                builder.addAllData(rpcVos);

            }
            builder.setCode(Result.SUCCESS);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getSbProductCollectionReportSumByDate(GetSbProductCollectionReportSumByDateRequest request, StreamObserver<GetSbProductCollectionReportSumByDateResponse> responseObserver) {
        GetSbProductCollectionReportSumByDateResponse.Builder builder = GetSbProductCollectionReportSumByDateResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || StringUtils.isBlank(request.getType()) || !request.hasEndDate() ||
                !request.hasStartDate()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            if(isOld(request.getStartDate())){
                getSbProductCollectionReportSumByDateByCampaign(request,builder);
            } else {
                getSbProductCollectionReportSumByDateByAds(request,builder);
            }
            builder.setCode(Result.SUCCESS);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getListSbProductCollectionReportSumByDate(GetListSbProductCollectionReportSumByDateRequest request, StreamObserver<GetListSbProductCollectionReportSumByDateResponse> responseObserver) {
        GetListSbProductCollectionReportSumByDateResponse.Builder builder = GetListSbProductCollectionReportSumByDateResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || StringUtils.isBlank(request.getType()) || !request.hasEndDate() ||
                !request.hasStartDate()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            if(isOld(request.getStartDate())){
                getListSbProductCollectionReportSumByDateByCampaign(request,builder);
            } else {
                getListSbProductCollectionReportSumByDateByAds(request,builder);
            }
            builder.setCode(Result.SUCCESS);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getSbVideoReportSumByAsinAndDate(GetSbVideoReportSumRequest request, StreamObserver<GetSbVideoReportSumResponse> responseObserver) {
        GetSbVideoReportSumResponse.Builder builder = GetSbVideoReportSumResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || StringUtils.isBlank(request.getType()) || !request.hasEndDate() ||
                !request.hasStartDate() || CollectionUtils.isEmpty(request.getAsinsList())) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            List<AmazonAdSbVideoReportRpcVo> sbVideoReport;
            if(isOld(request.getStartDate())){
                sbVideoReport = getSbVideoReportSumByAsinAndDateByCampaign(request);
            } else {
                sbVideoReport = getSbVideoReportSumByAsinAndDateByAds(request);
            }

            if(CollectionUtils.isNotEmpty(sbVideoReport)){
                builder.addAllData(sbVideoReport);
            }
            builder.setCode(Result.SUCCESS);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    private void getSbProductCollectionReportSumByDateByCampaign(GetSbProductCollectionReportSumByDateRequest request,GetSbProductCollectionReportSumByDateResponse.Builder builder){
        AmazonAdSbCampaignReport report = amazonAdSbCampaignReportDao.getSumReportByDateAndType(request.getPuid(), request.getShopId(), request.getType(),
                    request.getStartDate(), request.getEndDate());
        if (report != null) {
            //po转message
            AmazonAdSbProductCollectionReportRpcVo.Builder voBuilder = AmazonAdSbProductCollectionReportRpcVo.newBuilder();
            if (report.getImpressions() != null) {
                voBuilder.setImpressions(report.getImpressions());
            }
            if (report.getClicks() != null) {
                voBuilder.setClicks(report.getClicks());
            }
            if (report.getCost() != null) {
                voBuilder.setCost(report.getCost().doubleValue());
            }
            if (report.getSales14d() != null) {
                voBuilder.setSales14D(report.getSales14d().doubleValue());
            }
            if (report.getConversions14d() != null) {
                voBuilder.setConversions14D(report.getConversions14d());
            }
            if (report.getConversions14dSameSKU() != null) {
                voBuilder.setConversions14DSameSKU(report.getConversions14dSameSKU());
            }
            if (report.getUnitsSold14d() != null) {
                voBuilder.setUnitsSold14D(report.getUnitsSold14d());
            }
            builder.setData(voBuilder.build());
        }
    }

    private void getSbProductCollectionReportSumByDateByAds(GetSbProductCollectionReportSumByDateRequest request,GetSbProductCollectionReportSumByDateResponse.Builder builder){
        AmazonAdSbAdsReport report = null;

        report = amazonAdSbAdsReportDao.getSumReportByDateAndType(request.getPuid(), request.getShopId(), request.getType(),
                    request.getStartDate(), request.getEndDate());

        if (report != null) {
            //po转message
            AmazonAdSbProductCollectionReportRpcVo.Builder voBuilder = AmazonAdSbProductCollectionReportRpcVo.newBuilder();
            if (report.getImpressions() != null) {
                voBuilder.setImpressions(report.getImpressions());
            }
            if (report.getClicks() != null) {
                voBuilder.setClicks(report.getClicks());
            }
            if (report.getCost() != null) {
                voBuilder.setCost(report.getCost().doubleValue());
            }
            if (report.getSales14d() != null) {
                voBuilder.setSales14D(report.getSales14d().doubleValue());
            }
            if (report.getConversions14d() != null) {
                voBuilder.setConversions14D(report.getConversions14d());
            }
            if (report.getConversions14dSameSKU() != null) {
                voBuilder.setConversions14DSameSKU(report.getConversions14dSameSKU());
            }
            if (report.getUnitsSold14d() != null) {
                voBuilder.setUnitsSold14D(report.getUnitsSold14d());
            }
            builder.setData(voBuilder.build());
        }
    }

    private List<AmazonAdSbVideoReportRpcVo> getSbVideoReportSumByAsinAndDateByCampaign(GetSbVideoReportSumRequest request){
        List<AmazonAdSbCampaignReport> reportList = amazonAdSbCampaignReportDao.getSbVideoReportSumByDateAndType(request.getPuid(), request.getShopId(), request.getType(),
                    request.getStartDate(), request.getEndDate());

        boolean isAll = false;  //是否返回全部asin
        List<AmazonAdSbVideoReportRpcVo> rpcList = new ArrayList<>();
        List<String> asinList = request.getAsinsList();

        if (asinList.size() == 1 && "".equals(asinList.get(0))) {  // 有且只有一个,且是空串
            isAll = true;
        }

        //处理数据
        if (CollectionUtils.isNotEmpty(reportList)) {
            Map<String, String> asinMap = new HashMap<>();
            // 组装asin数据
            for (AmazonAdSbCampaignReport report : reportList) {
                String asin = "";
                if (asinMap.containsKey(report.getCampaignId())) {
                    asin = asinMap.get(report.getCampaignId());
                } else {
                    String creative = sbAdCampaignDao.getCreativeByCampaignId(request.getPuid(), request.getShopId(), report.getCampaignId());
                    if (StringUtils.isNotBlank(creative)) {
                        Creative creativeVo = JSONUtil.jsonToObject(creative, Creative.class);
                        if (creativeVo != null && CollectionUtils.isNotEmpty(creativeVo.getAsins())) {
                            List<String> asins = creativeVo.getAsins();
                            asin = asins.get(0);
                            if (StringUtils.isNotBlank(asin)) {
                                asinMap.put(report.getCampaignId(), asin);
                            }
                        }
                    }
                }
                report.setAsin(asin);
            }

            //同asin 的聚合
            Map<String, List<AmazonAdSbCampaignReport>> reportMap = reportList.stream().filter(report -> StringUtils.isNotBlank(report.getAsin())).collect(Collectors.groupingBy(AmazonAdSbCampaignReport::getAsin));;
            List<AmazonAdSbCampaignReport> newReportList = new ArrayList<>();
            AmazonAdSbCampaignReport report;
            for (Map.Entry<String, List<AmazonAdSbCampaignReport>> entry : reportMap.entrySet()) {
                report = new AmazonAdSbCampaignReport();
                report.setAsin(entry.getKey());

                List<AmazonAdSbCampaignReport> list = entry.getValue();
                int clicks = 0;
                int impressions = 0;
                BigDecimal cost = BigDecimal.ZERO;
                BigDecimal sales14d = BigDecimal.ZERO;
                int conversions14d = 0;
                int unitsSold14d = 0;
                for (AmazonAdSbCampaignReport vo : list) {
                    clicks = clicks + (vo.getClicks() != null ? vo.getClicks() : 0);
                    impressions = impressions + (vo.getImpressions() != null ? vo.getImpressions() : 0);
                    conversions14d = conversions14d + (vo.getConversions14d() != null ? vo.getConversions14d() : 0);
                    cost = cost.add(vo.getCost() != null ? vo.getCost() : BigDecimal.ZERO);
                    sales14d = sales14d.add(vo.getSales14d() != null ? vo.getSales14d() : BigDecimal.ZERO);
                    unitsSold14d = unitsSold14d + (vo.getUnitsSold14d() != null ? vo.getUnitsSold14d() : 0);
                }
                report.setImpressions(impressions);
                report.setClicks(clicks);
                report.setCost(cost);
                report.setSales14d(sales14d);
                report.setConversions14d(conversions14d);
                report.setUnitsSold14d(unitsSold14d);
                newReportList.add(report);
            }


            for (AmazonAdSbCampaignReport newReport : newReportList) {
                if (newReport.getAsin() == null) {
                    continue;
                }
                if (!isAll) {
                    if (!asinList.contains(newReport.getAsin())) {
                        continue;
                    }
                }
                AmazonAdSbVideoReportRpcVo.Builder voBuilder = AmazonAdSbVideoReportRpcVo.newBuilder();
                if (newReport.getImpressions() != null) {
                    voBuilder.setImpressions(newReport.getImpressions());
                }
                if (newReport.getClicks() != null) {
                    voBuilder.setClicks(newReport.getClicks());
                }
                if (newReport.getCost() != null) {
                    voBuilder.setCost(newReport.getCost().doubleValue());
                }
                if (newReport.getSales14d() != null) {
                    voBuilder.setSales14D(newReport.getSales14d().doubleValue());
                }
                if (newReport.getConversions14d() != null) {
                    voBuilder.setConversions14D(newReport.getConversions14d());
                }
                if (newReport.getAsin() != null) {
                    voBuilder.setAsin(newReport.getAsin());
                }
                if (newReport.getUnitsSold14d() != null) {
                    voBuilder.setUnitsSold14D(newReport.getUnitsSold14d());
                }
                rpcList.add(voBuilder.build());
            }
        }
        return rpcList;

    }


    private List<AmazonAdSbVideoReportRpcVo> getSbVideoReportSumByAsinAndDateByAds(GetSbVideoReportSumRequest request){
        List<AmazonAdSbAdsReport> reportList = null;
        reportList = amazonAdSbAdsReportDao.getSbVideoReportSumByDateAndType(request.getPuid(), request.getShopId(), request.getType(),
                    request.getStartDate(), request.getEndDate());

        //是否返回全部asin
        boolean isAll = false;
        List<AmazonAdSbVideoReportRpcVo> rpcList = new ArrayList<>();
        List<String> asinList = request.getAsinsList();
        // 有且只有一个,且是空串
        if (asinList.size() == 1 && "".equals(asinList.get(0))) {
            isAll = true;
        }

        //处理数据
        if (CollectionUtils.isNotEmpty(reportList)) {
            Map<String, String> asinMap = new HashMap<>();
            // 组装asin数据
            for (AmazonAdSbAdsReport report : reportList) {
                String asin = "";
                if (asinMap.containsKey(report.getQueryId())) {
                    asin = asinMap.get(report.getQueryId());
                } else {
                    String asins = amazonSbAdsDao.getSbCreativeByQueryId(request.getPuid(), request.getShopId(), report.getQueryId());
                    if (StringUtils.isNotBlank(asins)) {
                        if (dynamicRefreshConfiguration.checkSbvData(request.getPuid())) {
                            asin = asins;
                            asinMap.put(report.getQueryId(), asins);
                        } else {
                            List<String> list = StringUtil.splitStr(asins, ",");
                            if (CollectionUtils.isNotEmpty(list)) {
                                asin = list.get(0);
                                if (StringUtils.isNotBlank(asin)) {
                                    asinMap.put(report.getQueryId(), asin);
                                }
                            }
                        }
                    }
                }
                report.setAsin(asin);
            }

            //同asin 的聚合
            Map<String, List<AmazonAdSbAdsReport>> reportMap = reportList.stream().filter(report -> StringUtils.isNotBlank(report.getAsin())).collect(Collectors.groupingBy(AmazonAdSbAdsReport::getAsin));;
            List<AmazonAdSbAdsReport> newReportList = new ArrayList<>();
            AmazonAdSbAdsReport report;
            for (Map.Entry<String, List<AmazonAdSbAdsReport>> entry : reportMap.entrySet()) {
                report = new AmazonAdSbAdsReport();
                report.setAsin(entry.getKey());

                List<AmazonAdSbAdsReport> list = entry.getValue();
                int clicks = 0;
                int impressions = 0;
                BigDecimal cost = BigDecimal.ZERO;
                BigDecimal sales14d = BigDecimal.ZERO;
                int conversions14d = 0;
                int unitsSold14d = 0;
                for (AmazonAdSbAdsReport vo : list) {
                    clicks = clicks + (vo.getClicks() != null ? vo.getClicks() : 0);
                    impressions = impressions + (vo.getImpressions() != null ? vo.getImpressions() : 0);
                    conversions14d = conversions14d + (vo.getConversions14d() != null ? vo.getConversions14d() : 0);
                    cost = cost.add(vo.getCost() != null ? vo.getCost() : BigDecimal.ZERO);
                    sales14d = sales14d.add(vo.getSales14d() != null ? vo.getSales14d() : BigDecimal.ZERO);
                    unitsSold14d = unitsSold14d + (vo.getUnitsSold14d() != null ? vo.getUnitsSold14d() : 0);
                }
                report.setImpressions(impressions);
                report.setClicks(clicks);
                report.setCost(cost);
                report.setSales14d(sales14d);
                report.setConversions14d(conversions14d);
                report.setUnitsSold14d(unitsSold14d);

                newReportList.add(report);
            }


            for (AmazonAdSbAdsReport newReport : newReportList) {
                if (newReport.getAsin() == null) {
                    continue;
                }
                if (!isAll) {
                    if (!asinList.contains(newReport.getAsin())) {
                        continue;
                    }
                }
                AmazonAdSbVideoReportRpcVo.Builder voBuilder = AmazonAdSbVideoReportRpcVo.newBuilder();
                if (newReport.getImpressions() != null) {
                    voBuilder.setImpressions(newReport.getImpressions());
                }
                if (newReport.getClicks() != null) {
                    voBuilder.setClicks(newReport.getClicks());
                }
                if (newReport.getCost() != null) {
                    voBuilder.setCost(newReport.getCost().doubleValue());
                }
                if (newReport.getSales14d() != null) {
                    voBuilder.setSales14D(newReport.getSales14d().doubleValue());
                }
                if (newReport.getConversions14d() != null) {
                    voBuilder.setConversions14D(newReport.getConversions14d());
                }
                if (newReport.getAsin() != null) {
                    voBuilder.setAsin(newReport.getAsin());
                }
                if (newReport.getUnitsSold14d() != null) {
                    voBuilder.setUnitsSold14D(newReport.getUnitsSold14d());
                }
                rpcList.add(voBuilder.build());
            }
        }
        return rpcList;

    }


    private void getListSbProductCollectionReportSumByDateByCampaign(GetListSbProductCollectionReportSumByDateRequest request,GetListSbProductCollectionReportSumByDateResponse.Builder builder){
        List<AmazonAdSbCampaignReport> reports = amazonAdSbCampaignReportDao.getSumCountDateReportByDateAndType(request.getPuid(), request.getShopId(), request.getType(),
                    request.getStartDate(), request.getEndDate());
        if (CollectionUtils.isNotEmpty(reports)) {
            //po转message
            for (AmazonAdSbCampaignReport report : reports){
                AmazonAdSbProductCollectionReportRpcVo.Builder voBuilder = AmazonAdSbProductCollectionReportRpcVo.newBuilder();
                if (report.getImpressions() != null) {
                    voBuilder.setImpressions(report.getImpressions());
                }
                if (report.getClicks() != null) {
                    voBuilder.setClicks(report.getClicks());
                }
                if (report.getCost() != null) {
                    voBuilder.setCost(report.getCost().doubleValue());
                }
                if (report.getSales14d() != null) {
                    voBuilder.setSales14D(report.getSales14d().doubleValue());
                }
                if (report.getConversions14d() != null) {
                    voBuilder.setConversions14D(report.getConversions14d());
                }
                if (report.getConversions14dSameSKU() != null) {
                    voBuilder.setConversions14DSameSKU(report.getConversions14dSameSKU());
                }
                if (report.getUnitsSold14d() != null) {
                    voBuilder.setUnitsSold14D(report.getUnitsSold14d());
                }
                if(report.getCountDate() != null){
                    voBuilder.setCountDate(report.getCountDate());
                }
                builder.addData(voBuilder.build());
            }

        }
    }


    private void getListSbProductCollectionReportSumByDateByAds(GetListSbProductCollectionReportSumByDateRequest request,GetListSbProductCollectionReportSumByDateResponse.Builder builder){
        List<AmazonAdSbAdsReport> reports = null;
        reports = amazonAdSbAdsReportDao.getSumCountDateReportByDateAndType(request.getPuid(), request.getShopId(), request.getType(),
                request.getStartDate(), request.getEndDate());
        if (CollectionUtils.isNotEmpty(reports)) {
            //po转message
            for (AmazonAdSbAdsReport report : reports){
                AmazonAdSbProductCollectionReportRpcVo.Builder voBuilder = AmazonAdSbProductCollectionReportRpcVo.newBuilder();
                if (report.getImpressions() != null) {
                    voBuilder.setImpressions(report.getImpressions());
                }
                if (report.getClicks() != null) {
                    voBuilder.setClicks(report.getClicks());
                }
                if (report.getCost() != null) {
                    voBuilder.setCost(report.getCost().doubleValue());
                }
                if (report.getSales14d() != null) {
                    voBuilder.setSales14D(report.getSales14d().doubleValue());
                }
                if (report.getConversions14d() != null) {
                    voBuilder.setConversions14D(report.getConversions14d());
                }
                if (report.getConversions14dSameSKU() != null) {
                    voBuilder.setConversions14DSameSKU(report.getConversions14dSameSKU());
                }
                if (report.getUnitsSold14d() != null) {
                    voBuilder.setUnitsSold14D(report.getUnitsSold14d());
                }
                if(report.getCountDate() != null){
                    voBuilder.setCountDate(report.getCountDate());
                }
                builder.addData(voBuilder.build());
            }
        }
    }

    private boolean isOld(String date) {
        if (StringUtils.isNotBlank(date)) {

            Date date2 = DateUtil.strToDate("20221201", DateUtil.PATTERN_YYYYMMDD);
            date = date.replace("-", "");
            date = date.replace("/", "");
            Date date1 = DateUtil.strToDate(date, DateUtil.PATTERN_YYYYMMDD);
            return DateUtil.compareDate(date2, date1) == 1;
        } else {
            return false;
        }
    }

    private boolean isSbOld(String date) {
        if (StringUtils.isNotBlank(date)) {
            Date date2 = DateUtil.strToDate("20230101", DateUtil.PATTERN_YYYYMMDD);
            date = date.replace("-", "");
            date = date.replace("/", "");
            Date date1 = DateUtil.strToDate(date, DateUtil.PATTERN_YYYYMMDD);
            return DateUtil.compareDate(date2, date1) == 1;
        } else {
            return false;
        }
    }



    private List<AmazonAdSbProductCollectionAsinReportRpcVo> getSbProductCollectionReportSumByAsinAndDateByAds(GetSbProductCollectionAsinReportSumByDateRequest request){
        List<AmazonAdSbAdsReport> reportList = null;
        reportList = amazonAdSbAdsReportDao.getSbVideoReportSumByDateAndType(request.getPuid(), request.getShopId(), request.getType(),
                request.getStartDate(), request.getEndDate());

        List<AmazonAdSbProductCollectionAsinReportRpcVo> rpcList = new ArrayList<>();


        //处理数据
        if (CollectionUtils.isNotEmpty(reportList)) {
            Map<String, String> asinMap = new HashMap<>();
            // 组装asin数据
            for (AmazonAdSbAdsReport report : reportList) {
                String asin = "";
                if (asinMap.containsKey(report.getQueryId())) {
                    asin = asinMap.get(report.getQueryId());
                } else {
                    String asins = amazonSbAdsDao.getSbCreativeByQueryId(request.getPuid(), request.getShopId(), report.getQueryId());
                    if (StringUtils.isNotBlank(asins)) {
                        asinMap.put(report.getQueryId(), asins);
                        asin = asins;
                    }
                }
                report.setAsin(asin);
            }



            for (AmazonAdSbAdsReport newReport : reportList) {

                AmazonAdSbProductCollectionAsinReportRpcVo.Builder voBuilder = AmazonAdSbProductCollectionAsinReportRpcVo.newBuilder();
                if (newReport.getImpressions() != null) {
                    voBuilder.setImpressions(newReport.getImpressions());
                }
                if (newReport.getClicks() != null) {
                    voBuilder.setClicks(newReport.getClicks());
                }
                if (newReport.getCost() != null) {
                    voBuilder.setCost(newReport.getCost().doubleValue());
                }
                if (newReport.getSales14d() != null) {
                    voBuilder.setSales14D(newReport.getSales14d().doubleValue());
                }
                if (newReport.getConversions14d() != null) {
                    voBuilder.setConversions14D(newReport.getConversions14d());
                }
                if (newReport.getAsin() != null) {
                    voBuilder.setAsin(newReport.getAsin());
                }
                if (newReport.getUnitsSold14d() != null) {
                    voBuilder.setUnitsSold14D(newReport.getUnitsSold14d());
                }
                rpcList.add(voBuilder.build());
            }
        }
        return rpcList;

    }


    private AmazonAdSbStoreSpotlightReportRpcVo getSbStoreSpotlightReportSumByDateByAds(GetSbStoreSpotlightReportSumByDateRequest request){
        AmazonAdSbAdsReport report = null;

        report = amazonAdSbAdsReportDao.getSumReportByDateAndType(request.getPuid(), request.getShopId(), request.getType(),
                request.getStartDate(), request.getEndDate());

        if (report != null) {
            //po转message
            AmazonAdSbStoreSpotlightReportRpcVo.Builder voBuilder = AmazonAdSbStoreSpotlightReportRpcVo.newBuilder();
            if (report.getImpressions() != null) {
                voBuilder.setImpressions(report.getImpressions());
            }
            if (report.getClicks() != null) {
                voBuilder.setClicks(report.getClicks());
            }
            if (report.getCost() != null) {
                voBuilder.setCost(report.getCost().doubleValue());
            }
            if (report.getSales14d() != null) {
                voBuilder.setSales14D(report.getSales14d().doubleValue());
            }
            if (report.getConversions14d() != null) {
                voBuilder.setConversions14D(report.getConversions14d());
            }
            if (report.getConversions14dSameSKU() != null) {
                voBuilder.setConversions14DSameSKU(report.getConversions14dSameSKU());
            }
            if (report.getUnitsSold14d() != null) {
                voBuilder.setUnitsSold14D(report.getUnitsSold14d());
            }
            return voBuilder.build();
        }
        return null;
        
    }

    private List<AmazonAdSbProductCollectionAsinReportRpcVo> getSbProductCollectionReportSumByDateByAds(GetSbProductCollectionAsinReportSumByDateRequest request){
        AmazonAdSbAdsReport report = null;

        report = amazonAdSbAdsReportDao.getSumReportByDateAndType(request.getPuid(), request.getShopId(), request.getType(),
                request.getStartDate(), request.getEndDate());

        if (report != null) {
            //po转message
            AmazonAdSbProductCollectionAsinReportRpcVo.Builder voBuilder = AmazonAdSbProductCollectionAsinReportRpcVo.newBuilder();
            if (report.getImpressions() != null) {
                voBuilder.setImpressions(report.getImpressions());
            }
            if (report.getClicks() != null) {
                voBuilder.setClicks(report.getClicks());
            }
            if (report.getCost() != null) {
                voBuilder.setCost(report.getCost().doubleValue());
            }
            if (report.getSales14d() != null) {
                voBuilder.setSales14D(report.getSales14d().doubleValue());
            }
            if (report.getConversions14d() != null) {
                voBuilder.setConversions14D(report.getConversions14d());
            }
            if (report.getConversions14dSameSKU() != null) {
                voBuilder.setConversions14DSameSKU(report.getConversions14dSameSKU());
            }
            if (report.getUnitsSold14d() != null) {
                voBuilder.setUnitsSold14D(report.getUnitsSold14d());
            }

            return Lists.newArrayList(voBuilder.build());
        }
        return null;
    }


    @Override
    public void getSbProductCollectionAsinReportSumByDate(GetSbProductCollectionAsinReportSumByDateRequest request, StreamObserver<GetSbProductCollectionAsinReportSumByDateResponse> responseObserver) {
        GetSbProductCollectionAsinReportSumByDateResponse.Builder builder = GetSbProductCollectionAsinReportSumByDateResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || StringUtils.isBlank(request.getType()) || !request.hasEndDate() ||
                !request.hasStartDate()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            List<AmazonAdSbProductCollectionAsinReportRpcVo> sbReport;

            if(isSbOld(request.getStartDate())){
                sbReport= getSbProductCollectionReportSumByDateByAds(request);
            } else {
                sbReport = getSbProductCollectionReportSumByAsinAndDateByAds(request);
            }

            if(CollectionUtils.isNotEmpty(sbReport)){
                builder.addAllData(sbReport);
            }
            builder.setCode(Result.SUCCESS);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getSbStoreSpotlightReportSumByDate(GetSbStoreSpotlightReportSumByDateRequest request, StreamObserver<GetSbStoreSpotlightReportSumByDateResponse> responseObserver) {
        GetSbStoreSpotlightReportSumByDateResponse.Builder builder = GetSbStoreSpotlightReportSumByDateResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || StringUtils.isBlank(request.getType()) || !request.hasEndDate() ||
                !request.hasStartDate()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            AmazonAdSbStoreSpotlightReportRpcVo sbStoreSpotlightReportSumByDateByAds = null ;
            if(!isSbOld(request.getStartDate())){
                sbStoreSpotlightReportSumByDateByAds = getSbStoreSpotlightReportSumByDateByAds(request);
            }

            if(sbStoreSpotlightReportSumByDateByAds != null){
                builder.setData(sbStoreSpotlightReportSumByDateByAds);
            }
            builder.setCode(Result.SUCCESS);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 按keywordId和adId批量查询stream明细数据
     * @param request
     * @param responseObserver
     */
    @Override
    public void queryAdStreamByKeywordIdsAndAdIds(QueryAdStreamByKeywordIdsAndAdIdsRequest request, StreamObserver<QueryAdStreamByKeywordIdsAndAdIdsResponse> responseObserver) {
        try {
            AdKeywordStreamDataParam param = new AdKeywordStreamDataParam();
            param.setSellerIds(request.getSellerIdList());
            param.setMarketplaceId(request.getMarketplaceId());
            param.setKeywordIds(request.getKeywordIdList());
            param.setAdIds(request.getAdIdList());
            param.setStartDate(request.getStartDate());
            param.setEndDate(request.getEndDate());
            param.setType(request.getType());
            param.setGroupByDate(request.getOrderByDate());

            QueryAdStreamByKeywordIdsAndAdIdsResponse.Builder builder = QueryAdStreamByKeywordIdsAndAdIdsResponse.newBuilder();

            if (checkParam(param)) {
                List<AmazonMarketingStreamData> list = keywordViewService.queryAdStreamByKeywordIdsAndAdIds(param);
                List<MarketingStreamData> streamDataList = list.stream().map(item -> {
                    MarketingStreamData.Builder dataBuilder = MarketingStreamData.newBuilder();
                    if (item.getDate() != null) {
                        dataBuilder.setNowDate(DateUtil.format(item.getDate()));
                    }
                    dataBuilder.setAdIdListStr(item.getAdIdListStr());
                    dataBuilder.setKeywordId(item.getKeywordId());
                    dataBuilder.setCost(item.getCost());
                    dataBuilder.setClicks(item.getClicks());
                    dataBuilder.setImpressions(item.getImpressions());
                    dataBuilder.setAttributedConversions7D(item.getAttributedConversions7d());
                    dataBuilder.setAttributedConversions7DSameSku(item.getAttributedConversions7dSameSku());
                    dataBuilder.setAttributedSales7D(item.getAttributedSales7d().doubleValue());
                    dataBuilder.setAttributedSales7DSameSku(item.getAttributedSales7dSameSku().doubleValue());
                    dataBuilder.setAttributedUnitsOrdered7D(item.getAttributedUnitsOrdered7d());
                    dataBuilder.setAttributedUnitsOrdered7DSameSku(item.getAttributedUnitsOrdered7dSameSku());
                    return dataBuilder.build();
                }).collect(Collectors.toList());

                builder.addAllStreamData(streamDataList);
            }


            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("QueryAdStreamByKeywordIdsAndAdIds with an error.", e);
            responseObserver.onError(e);
        }
    }

    /**
     * 按keywordId和adId批量查询stream明细数据（小时维度统计）
     * @param request
     * @param responseObserver
     */
    @Override
    public void queryAdStreamByAsinGroupByHour(QueryAdStreamByAsinRequest request, StreamObserver<QueryAdStreamByKeywordIdsAndAdIdsResponse> responseObserver) {
        log.info("queryAdStreamByAsinGroupByHour request: {}", request);
        QueryAdStreamByKeywordIdsAndAdIdsResponse.Builder builder = QueryAdStreamByKeywordIdsAndAdIdsResponse.newBuilder();

        if (CollectionUtils.isEmpty(request.getMarketplaceIdList()) || CollectionUtils.isEmpty(request.getShopIdList())
                && CollectionUtils.isEmpty(request.getSellerIdList()) && CollectionUtils.isEmpty(request.getAsinList())
                && StringUtils.isBlank(request.getStartDate()) && StringUtils.isBlank(request.getEndDate())) {
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        try {
            List<String> allAdId = new ArrayList<>();

            //查询sp keywordId和targetId / adId
            List<AdGroupAndAdIdDto> spAdList = odsAmazonAdProductDao.getAdIdAndAdGroupIdByAsin(request.getPuid(),
                    request.getShopIdList(), request.getAsinList(), request.getMskuList(), request.getSpCampaignId(), request.getSpGroupId());

            List<String> spAdIdList = spAdList.stream().map(AdGroupAndAdIdDto::getAdId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(spAdIdList)) {
                allAdId.addAll(spAdIdList);
            }

            log.info("allAdId {}", allAdId);
            if (CollectionUtils.isEmpty(allAdId)) {
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }

            //过滤出有广告的sellerId,避免查询范围过大
            List<Integer> allShopIdList = spAdList.stream().map(AdGroupAndAdIdDto::getShopId).distinct().collect(Collectors.toList());
            List<String> sellerIds = slaveShopAuthDao.getSellerIdByShopIds(allShopIdList);


            AdKeywordStreamDataParam param = new AdKeywordStreamDataParam();
            param.setSellerIds(sellerIds);
            param.setMarketplaceIdList(request.getMarketplaceIdList());
            param.setAdIds(allAdId);
            param.setStartDate(request.getStartDate());
            param.setEndDate(request.getEndDate());
            param.setCurrency(request.getCurrency());
            param.setPuid(request.getPuid());
            param.setShopIdList(request.getShopIdList());
            param.setAsinList(request.getAsinList());
            param.setCampaignId(request.getSpCampaignId());
            param.setGroupId(request.getSpGroupId());

            List<AmazonMarketingStreamData> list = keywordViewService.queryAdStreamByKeywordIdsAndAdIdsGroupByHour(param);
            List<MarketingStreamData> streamDataList = list.stream().map(item -> {
                MarketingStreamData.Builder dataBuilder = MarketingStreamData.newBuilder();
                dataBuilder.setCost(item.getCost());
                dataBuilder.setClicks(item.getClicks());
                dataBuilder.setImpressions(item.getImpressions());
                dataBuilder.setAttributedConversions7D(item.getAttributedConversions7d());
                dataBuilder.setAttributedConversions7DSameSku(item.getAttributedConversions7dSameSku());
                dataBuilder.setAttributedSales7D(item.getAttributedSales7d().doubleValue());
                dataBuilder.setAttributedSales7DSameSku(item.getAttributedSales7dSameSku().doubleValue());
                dataBuilder.setAttributedUnitsOrdered7D(item.getAttributedUnitsOrdered7d());
                dataBuilder.setAttributedUnitsOrdered7DSameSku(item.getAttributedUnitsOrdered7dSameSku());
                String[] arr = item.getTime().split(":");
                dataBuilder.setHour(Integer.parseInt(arr[0].trim()));
                return dataBuilder.build();
            }).collect(Collectors.toList());

            builder.addAllStreamData(streamDataList);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("queryAdStreamByAsinGroupByHour with an error.", e);
            responseObserver.onError(e);
        }
    }

    /**
     * 按keywordId和adId批量查询stream汇总数据
     * @param request
     * @param responseObserver
     */
    @Override
    public void sumAdStreamByKeywordIdsAndAdIds(SumAdStreamByKeywordIdsAndAdIdsRequest request, StreamObserver<SumAdStreamByKeywordIdsAndAdIdsResponse> responseObserver) {
        try {
            AdKeywordStreamDataParam param = new AdKeywordStreamDataParam();
            param.setSellerIds(request.getSellerIdList());
            param.setMarketplaceId(request.getMarketplaceId());
            param.setKeywordIds(request.getKeywordIdList());
            param.setAdIds(request.getAdIdList());
            param.setStartDate(request.getStartDate());
            param.setEndDate(request.getEndDate());
            param.setType(request.getType());

            SumAdStreamByKeywordIdsAndAdIdsResponse.Builder builder = SumAdStreamByKeywordIdsAndAdIdsResponse.newBuilder();

            if (checkParam(param)) {
                MarketingStreamData.Builder dataBuilder = MarketingStreamData.newBuilder();
                AmazonMarketingStreamData item = keywordViewService.sumAdStreamByKeywordIdsAndAdIds(param);
                dataBuilder.setCost(item.getCost() == null ? 0 : item.getCost());
                dataBuilder.setClicks(item.getClicks() == null ? 0 : item.getClicks());
                dataBuilder.setImpressions(item.getImpressions() == null? 0 : item.getImpressions());
                dataBuilder.setAttributedConversions7D(item.getAttributedConversions7d() == null? 0 : item.getAttributedConversions7d());
                dataBuilder.setAttributedConversions7DSameSku(item.getAttributedConversions7dSameSku() == null? 0 : item.getAttributedConversions7dSameSku());
                dataBuilder.setAttributedSales7D(item.getAttributedSales7d() == null ? 0 : item.getAttributedSales7d().doubleValue());
                dataBuilder.setAttributedSales7DSameSku(item.getAttributedSales7dSameSku() == null ? 0 : item.getAttributedSales7dSameSku().doubleValue());
                dataBuilder.setAttributedUnitsOrdered7D(item.getAttributedUnitsOrdered7d() == null ? 0 : item.getAttributedUnitsOrdered7d());
                dataBuilder.setAttributedUnitsOrdered7DSameSku(item.getAttributedUnitsOrdered7dSameSku() == null ? 0 : item.getAttributedUnitsOrdered7dSameSku());
                builder.setStreamData(dataBuilder.build());
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("SumAdStreamByKeywordIdsAndAdIds with an error.", e);
            responseObserver.onError(e);
        }
    }

    private boolean checkParam(AdKeywordStreamDataParam param) {
        return CollectionUtils.isNotEmpty(param.getKeywordIds()) && CollectionUtils.isNotEmpty(param.getAdIds())
                && CollectionUtils.isNotEmpty(param.getSellerIds()) && StringUtils.isNotBlank(param.getMarketplaceId())
                && StringUtils.isNotBlank(param.getStartDate()) && StringUtils.isNotBlank(param.getEndDate());
    }



}
