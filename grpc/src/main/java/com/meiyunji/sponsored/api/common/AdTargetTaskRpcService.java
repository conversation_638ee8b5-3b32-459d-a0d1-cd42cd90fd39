package com.meiyunji.sponsored.api.common;

import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.grpc.adTargetTask.AdTargetTaskOuterClass;
import com.meiyunji.sponsored.grpc.adTargetTask.AdTargetTaskServiceGrpc;
import com.meiyunji.sponsored.rpc.vo.CommonResponse;
import com.meiyunji.sponsored.service.cpc.service2.IAdTargetTaskService;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-05-08 20:20
 */
@GRpcService
@Slf4j
public class AdTargetTaskRpcService extends AdTargetTaskServiceGrpc.AdTargetTaskServiceImplBase {

    @Autowired
    private IAdTargetTaskService adTargetTaskService;
    private static final int MAX_PAGE_SIZE = 200;

    @Override
    public void list(AdTargetTaskOuterClass.ListAdTargetTaskRequest request,
                     StreamObserver<AdTargetTaskOuterClass.ListAdTargetTaskResponse> responseObserver) {
        AdTargetTaskOuterClass.ListAdTargetTaskResponse.Builder builder = AdTargetTaskOuterClass.ListAdTargetTaskResponse.newBuilder();
        if (!request.hasPuid() || request.getPageSize() <= 0 || !request.hasTargetPageType()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            int pageSize = Math.min(request.getPageSize(), MAX_PAGE_SIZE);
            AdTargetTaskOuterClass.AdTargetTaskPage adTargetTaskPage =
                    adTargetTaskService.getAdTargetTaskPage(request.getPuid(), request.getShopId(), request.getPageNo(), pageSize, request.getTargetPageType(), request.getSourceAdCampaignId(),request.getUid());
            builder.setCode(Result.SUCCESS);
            builder.setData(adTargetTaskPage);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void failureDetailList(AdTargetTaskOuterClass.ListAdTargetTaskDetailRequest request,
                                  StreamObserver<AdTargetTaskOuterClass.ListAdTargetTaskDetailResponse> responseObserver) {
        AdTargetTaskOuterClass.ListAdTargetTaskDetailResponse.Builder builder = AdTargetTaskOuterClass.ListAdTargetTaskDetailResponse.newBuilder();
        if (!request.hasPuid() || !request.hasTaskId() || request.getPageSize() <= 0) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            int pageSize = Math.min(request.getPageSize(), MAX_PAGE_SIZE);
            AdTargetTaskOuterClass.AdTargetTaskDetailPage adTargetTaskDetailPage = adTargetTaskService
                    .getFailAdTargetTaskDetailPage(request.getTaskId(), request.getPuid(), request.getPageNo(), pageSize);
            builder.setCode(Result.SUCCESS);
            builder.setData(adTargetTaskDetailPage);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void targetObjectDetailList(AdTargetTaskOuterClass.ListTargetObjectDetailRequest request, StreamObserver<AdTargetTaskOuterClass.ListTargetObjectDetailResponse> responseObserver) {
        AdTargetTaskOuterClass.ListTargetObjectDetailResponse.Builder builder = AdTargetTaskOuterClass.ListTargetObjectDetailResponse.newBuilder();
        if (!request.hasPuid() || !request.hasTaskId() || request.getPageSize() <= 0) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            int pageSize = Math.min(request.getPageSize(), MAX_PAGE_SIZE);
            AdTargetTaskOuterClass.TargetObjectDetailPage keywordDetailPage = adTargetTaskService
                    .getTargetObjectDetailPage(request.getTaskId(), request.getPuid(), request.getPageNo(), pageSize);
            builder.setCode(Result.SUCCESS);
            builder.setData(keywordDetailPage);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void retry(AdTargetTaskOuterClass.AdTargetTaskRetryRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasTaskId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            String result = adTargetTaskService.retry(request.getPuid(), request.getTaskId());
            if (StringUtils.isNotBlank(result)) {
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg(result);
            } else {
                builder.setCode(Int32Value.of(Result.SUCCESS));
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
