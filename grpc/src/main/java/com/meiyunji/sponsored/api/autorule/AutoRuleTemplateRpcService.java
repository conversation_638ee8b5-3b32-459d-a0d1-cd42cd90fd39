package com.meiyunji.sponsored.api.autorule;

import com.google.api.client.util.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.sellfox.ams.api.service.QueryFeedStartRequestPb;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.autorule.template.*;
import com.meiyunji.sponsored.rpc.vo.CommonResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDao;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleTemplateDao;
import com.meiyunji.sponsored.service.autoRule.enums.*;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleTemplate;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleTemplateReportComplete;
import com.meiyunji.sponsored.service.autoRule.po.ChildrenItemTypeVo;
import com.meiyunji.sponsored.service.autoRule.service.AdvertiseAutoRuleStatusService;
import com.meiyunji.sponsored.service.autoRule.service.AdvertiseAutoRuleTemplateReportCompleteService;
import com.meiyunji.sponsored.service.autoRule.service.AdvertiseAutoRuleTemplateService;
import com.meiyunji.sponsored.service.autoRule.util.AutoRuleJsonToGrpcUtil;
import com.meiyunji.sponsored.service.autoRule.util.ReportTriggerAutoRuleHelper;
import com.meiyunji.sponsored.service.autoRule.vo.*;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.config.IndexStrategyConfig;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdGroupDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdGroup;
import com.meiyunji.sponsored.service.cpc.po.PostcodeData;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.enums.AutoRuleOperationTypeEnum;
import com.meiyunji.sponsored.service.grabRankings.service.GrabRankingsPostcodeService;
import com.meiyunji.sponsored.service.grabRankings.vo.PostcodeResult;
import com.meiyunji.sponsored.service.strategy.dao.impl.StrategyLimitConfigDao;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.strategy.po.StrategyLimitConfig;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import com.meiyunji.sponsored.service.vo.ProductAdReportVo;
import io.grpc.stub.ServerCalls;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @describe: 广告自动化规则模板
 */
@GRpcService
@Slf4j
public class AutoRuleTemplateRpcService extends RPCAutoRuleTemplateServiceGrpc.RPCAutoRuleTemplateServiceImplBase {
    @Autowired
    private IAdvertiseAutoRuleTemplateDao advertiseAutoRuleTemplateDao;
    @Autowired
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;
    @Autowired
    private AdvertiseAutoRuleTemplateService advertiseAutoRuleTemplateService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IndexStrategyConfig strategyConfig;
    @Autowired
    private ReportTriggerAutoRuleHelper reportTriggerAutoruleHelper;
    @Autowired
    private AdvertiseAutoRuleTemplateReportCompleteService advertiseAutoRuleTemplateReportCompleteService;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private AdvertiseAutoRuleStatusService advertiseAutoRuleStatusService;
    @Autowired
    private GrabRankingsPostcodeService grabRankingsPostcodeService;
    @Resource
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Autowired
    private StrategyLimitConfigDao strategyLimitConfigDao;
    @Autowired
    private AutoRuleJsonToGrpcUtil autoRuleJsonToGrpcUtil;

    private static final BigDecimal ONE_HUNDRED = new BigDecimal(100);
    /**
     * grpc转param
     */

    @Override
    public void pageList(AutoRuleTemplatePageParamRequest request, StreamObserver<AutoRuleTemplatePageListResponse> responseObserver) {
        log.info("自动化规则模板 pageList request {}", request);
        // grpc转param
        AdvertiseAutoRuleTemplateRequest param = grpcToParam(request);
        // 分页获取参数
        Page<AdvertiseAutoRuleTemplate> page = advertiseAutoRuleTemplateService.pageList(request.getPuid(), param);
        // 构建grpc响应参数
        AutoRuleTemplatePageListResponse response = buildResponse(request, page);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    /**
     * 构建grpc响应参数
     */
    private AutoRuleTemplatePageListResponse buildResponse(AutoRuleTemplatePageParamRequest request, Page<AdvertiseAutoRuleTemplate> page) {
        AutoRuleTemplatePageListResponse.Builder responseBuilder = AutoRuleTemplatePageListResponse.newBuilder();
        AutoRuleTemplatePage.Builder pageBuilder = AutoRuleTemplatePage.newBuilder();
        pageBuilder.setPageNo(page.getPageNo());
        pageBuilder.setPageSize(page.getPageSize());
        pageBuilder.setTotalPage(page.getTotalPage());
        pageBuilder.setTotalSize(page.getTotalSize());
        List<AutoRuleTemplateRpcVo> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(page.getRows())){
            // 广告组名称Map
            Map<String, String> groupNameMap = getGroupNameMap(page, request);
            List<Long> templateIdList = page.getRows().stream().map(AdvertiseAutoRuleTemplate::getId).collect(Collectors.toList());
            // 店铺信息Map
            List<ShopAuth> shops = shopAuthDao.getListByPuid(request.getPuid());
            Map<Integer, ShopAuth> shopAuthMap  = shops.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
            // 组类型Map
            List<ChildrenItemTypeVo> childrenItemTypeVoList = advertiseAutoRuleStatusDao.selectChildrenItemType(request.getPuid(), templateIdList);
            Map<Long, List<ChildrenItemTypeVo>> childrenItemTypeMap = childrenItemTypeVoList.stream().collect(Collectors.groupingBy(ChildrenItemTypeVo::getTemplateId));
            for (AdvertiseAutoRuleTemplate e : page.getRows()) {
                ShopAuth shopAuth = shopAuthMap.get(e.getShopId());
                String childrenItemType = null;
                List<ChildrenItemTypeVo> childrenItemTypeList = childrenItemTypeMap.get(e.getId());
                if (CollectionUtils.isNotEmpty(childrenItemTypeList)) {
                    childrenItemType = childrenItemTypeList.get(0).getChildrenItemType();
                }
                AutoRuleTemplateRpcVo.Builder builder = AutoRuleTemplateRpcVo.newBuilder();
                // 构建基础信息
                buildTemplateBase(e, builder, childrenItemType, shopAuth);
                // 构建json信息
                buildTemplateJson(e, builder, groupNameMap);
                list.add(builder.build());
            }
        }
        pageBuilder.addAllRows(list);
        responseBuilder.setData(pageBuilder.build());
        responseBuilder.setCode(Result.SUCCESS);
        return responseBuilder.build();
    }

    /**
     * 构建单个模版响应参数
     */
    private void buildTemplateBase(AdvertiseAutoRuleTemplate e, AutoRuleTemplateRpcVo.Builder builder, String childrenItemType, ShopAuth shopAuth) {
        builder.setTemplateId(e.getId());
        builder.setPuid(e.getPuid());
        builder.setShopId(e.getShopId());
        builder.setMarketplaceId(e.getMarketplaceId());
        builder.setAdType(e.getAdType());
        builder.setStatus(e.getStatus());
        builder.setTemplateName(e.getTemplateName());
        builder.setRuleType(e.getRuleType());
        builder.setItemType(e.getItemType());
        builder.setCreateUid(e.getCreateUid());
        builder.setUpdateUid(e.getUpdateUid());
        builder.setCreateName(e.getCreateName());
        builder.setUpdateName(e.getUpdateName());
        builder.setVersion(e.getVersion());
        builder.setReportCompletePush(e.getReportCompletePush());
        builder.setCreateAt(e.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        builder.setLastUpdateAt(e.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        builder.setOptimizedTimes(Optional.ofNullable(e.getOptimizedTimes()).orElse(0));
        builder.setItemCount(Optional.ofNullable(e.getItemCount()).orElse(0));
        if (StringUtils.isNotEmpty(childrenItemType)) {
            builder.setChildrenItemType(childrenItemType);
        }
        if (shopAuth != null) {
            builder.setShopName(shopAuth.getName());
        }
        if (StringUtils.isNotBlank(e.getAsin())) {
            builder.setAsin(e.getAsin());
        }
        if (StringUtils.isNotBlank(e.getSetRelation())) {
            builder.setSetRelation(e.getSetRelation());
        }
        if (StringUtils.isNotBlank(e.getExecuteTimeSpaceValue())) {
            builder.setExecuteTimeSpaceValue(e.getExecuteTimeSpaceValue());
        }
        if (StringUtils.isNotBlank(e.getExecuteTimeSpaceUnit())) {
            builder.setExecuteTimeSpaceUnit(e.getExecuteTimeSpaceUnit());
        }
        if (StringUtils.isNotBlank(e.getMessageReminderType())) {
            builder.setMessageReminderType(e.getMessageReminderType());
        }
        if (StringUtils.isNotBlank(e.getCallbackState())) {
            builder.setCallbackState(Boolean.parseBoolean(e.getCallbackState()));
        }
        if (e.getState() == null || !e.getState().equals(0)) {
            builder.setTaskState(1);
        } else {
            builder.setTaskState(0);
        }
        if (StringUtils.isNotBlank(e.getChooseTimeType())) {
            builder.setChooseTimeType(e.getChooseTimeType());
        }
        if (StringUtils.isNotBlank(e.getTimeType())) {
            builder.setTimeType(e.getTimeType());
        }
        if (StringUtils.isNotBlank(e.getCheckFrequency())) {
            builder.setCheckFrequency(e.getCheckFrequency());
        }
        if (StringUtils.isNotBlank(e.getPostalCodeSettings())) {
            builder.setPostalCodeSettings(e.getPostalCodeSettings());
        }
        if (e.getStartDate() != null) {
            builder.setStartDate(e.getStartDate().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
        }
        if (e.getEndDate() != null) {
            builder.setEndDate(e.getEndDate().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
        }
        if (PushMessageTypeEnum.ALL.getCode().equals(e.getPushMessageType())) {
            builder.addPushMessageTypeStr(PushMessageTypeEnum.WX.getCode());
            builder.addPushMessageTypeStr(PushMessageTypeEnum.INSTATION.getCode());
        } else if (PushMessageTypeEnum.WX.getCode().equals(e.getPushMessageType()) || (PushMessageTypeEnum.INSTATION.getCode().equals(e.getPushMessageType()))) {
            builder.addPushMessageTypeStr(e.getPushMessageType());
        }
        if (StringUtils.isNotBlank(e.getMarketplaceName())) {
            builder.setMarketplaceName(e.getMarketplaceName());
        }
        if (StringUtils.isNotBlank(e.getExecuteType())) {
            builder.setExecuteType(e.getExecuteType());
        }
        if (StringUtils.isNotBlank(e.getSku())) {
            // 构建sku信息
            builder.addAllProductList(autoRuleJsonToGrpcUtil.buildSku(e.getSku(),e.getPuid(),e.getShopId()));
        }
    }

    /**
     * 构建json转grpc信息
     */
    private static void buildTemplateJson(AdvertiseAutoRuleTemplate e, AutoRuleTemplateRpcVo.Builder builder, Map<String, String> groupNameMap) {
        if (StringUtils.isNotBlank(e.getTimeRule())) {
            // 构建时间规则信息
            builder.addAllTimeRules(AutoRuleJsonToGrpcUtil.buildTimeRules(e.getTimeRule()));
        }
        if (StringUtils.isNotBlank(e.getDesiredPosition())) {
            // 构建抢排名期望位置
            builder.setDesiredPosition(AutoRuleJsonToGrpcUtil.buildDesiredPosition(e.getDesiredPosition()));
        }
        if (StringUtils.isNotBlank(e.getAdDataRule())) {
            // 构建抢排名规则列表信息
            builder.addAllAdDataRules(AutoRuleJsonToGrpcUtil.buildAdDataRules(e.getAdDataRule(), e.getRuleType()));
        }
        if (StringUtils.isNotBlank(e.getAdDataOperate())) {
            // 构建抢排名自动加价执行操作信息
            builder.setAdDataOperate(AutoRuleJsonToGrpcUtil.buildAdDataOperate(e.getAdDataOperate()));
        }
        if (StringUtils.isNotBlank(e.getAutoPriceRule())) {
            // 构建抢排名自动降价规则信息
            builder.setAutoPriceRule(AutoRuleJsonToGrpcUtil.buildAutoPriceRule(e.getAutoPriceRule()));
        }
        if (StringUtils.isNotBlank(e.getAutoPriceOperate())) {
            // 构建抢排名自动降价执行操作信息
            builder.setAutoPriceOperate(AutoRuleJsonToGrpcUtil.buildAutoPriceOperate(e.getAutoPriceOperate()));
        }
        if (StringUtils.isNotBlank(e.getBiddingCallbackOperate())) {
            // 构建抢排名竞价回调操作信息
            builder.setBiddingCallbackOperate(AutoRuleJsonToGrpcUtil.buildBiddingCallbackOperate(e.getBiddingCallbackOperate()));
        }
        if (StringUtils.isNotBlank(e.getCallbackOperate())) {
            // 构建自动化规则回调操作信息
            builder.setCallbackOperate(AutoRuleJsonToGrpcUtil.buildCallbackOperate(e.getCallbackOperate()));
        }
        if (StringUtils.isNotBlank(e.getRule())) {
            // 构建自动化规则-规则信息
            builder.addAllRuleList(AutoRuleJsonToGrpcUtil.buildRule(e.getRule(), e.getRuleType()));
        }
        if (StringUtils.isNotBlank(e.getPerformOperation())) {
            // 构建自动化规则-执行操作信息
            List<PerformOperation> operationList = AutoRuleJsonToGrpcUtil.buildPerformOperation(e.getPerformOperation(), e.getItemType(), groupNameMap);
            builder.addAllPerformOperationList(operationList);
            if(CollectionUtils.isNotEmpty(operationList)){
                builder.setOperationType(AutoRuleOperationTypeEnum.getOperationType(operationList.get(0).getRuleAction()));
            }
        }
    }

    /**
     * 获取广告组名称
     */
    private Map<String, String> getGroupNameMap(Page<AdvertiseAutoRuleTemplate> page, AutoRuleTemplatePageParamRequest param) {
        Set<String> adGroupIdSet = new HashSet<>();
        for (AdvertiseAutoRuleTemplate row : page.getRows()) {
            if(StringUtils.isEmpty(row.getPerformOperation())){
                continue;
            }
            List<PerformOperationJson> performOperationJsonList = JSONUtil.jsonToArray(row.getPerformOperation(), PerformOperationJson.class);
            if(CollectionUtils.isEmpty(performOperationJsonList)){
                continue;
            }
            for (PerformOperationJson operationJson : performOperationJsonList) {
                if(StringUtils.isBlank(operationJson.getAdGroupId())){
                    continue;
                }
                adGroupIdSet.add(operationJson.getAdGroupId());
            }
        }
        Map<String, String> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(adGroupIdSet)) {
            //查询广告组
            List<AmazonAdGroup> spGroupList = amazonAdGroupDao.listByGroupIdsAndShopIdList(param.getPuid(), param.getShopIdList(), new ArrayList<>(adGroupIdSet));
            List<AmazonSbAdGroup> sbGroupList = amazonSbAdGroupDao.getListByShopIdsAndGroupIds(param.getPuid(), param.getShopIdList(), new ArrayList<>(adGroupIdSet));
            spGroupList.forEach(x -> map.put(x.getAdGroupId(), x.getName()));
            sbGroupList.forEach(x -> map.put(x.getAdGroupId(), x.getName()));
        }
        return map;
    }

    /**
     * grpc转param
     */
    private static AdvertiseAutoRuleTemplateRequest grpcToParam(AutoRuleTemplatePageParamRequest request) {
        AdvertiseAutoRuleTemplateRequest param = new AdvertiseAutoRuleTemplateRequest();
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setShopIdList(request.getShopIdList());
        param.setTemplateName(request.getTemplateName());
        if (StringUtils.isNotBlank(request.getRuleType())) {
            param.setRuleType(StringUtil.stringToList(request.getRuleType(),","));
        }
        param.setTraceId(request.getTraceId());
        if (StringUtils.isNotBlank(request.getItemType())) {
            param.setItemType(Arrays.stream(StringUtils.split(request.getItemType(), ","))
                    .flatMap(x -> AdvertiseRuleQueryType.map.get(x).stream()).collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(request.getUpdateTimeStart())) {
            param.setUpdateTimeStart(DateUtil.strToDate(request.getUpdateTimeStart(), DateUtil.PATTERN));
        }
        if (StringUtils.isNotBlank(request.getUpdateTimeEnd())) {
            param.setUpdateTimeEnd(DateUtil.getDayMaxDate(DateUtil.strToDate(request.getUpdateTimeEnd(), DateUtil.PATTERN)));
        }
        if (StringUtils.isNotBlank(request.getUpdateUid())) {
            param.setUpdateUidList(Arrays.stream(request.getUpdateUid().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(request.getCreateUid())) {
            param.setCreateUidList(Arrays.stream(request.getCreateUid().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(request.getItemId())) {
            param.setItemId(request.getItemId());
        }
        param.setFilterGroup(request.getFilterGroup());
        return param;
    }

    private int getAutoRuleTemplateWithoutKeywordCardMaxSize(int puid) {
        try {
            StrategyLimitConfig strategyLimitConfig = strategyLimitConfigDao.queryCustomRuleConfigByPuid(puid);
            if (strategyLimitConfig == null || strategyLimitConfig.getGroupTemplateNumLimit() <= 0) {
                return strategyConfig.getTemplateSize();
            }
            return strategyLimitConfig.getGroupTemplateNumLimit();
        } catch (Exception e) {
            log.error("getAutoRuleTemplateWithoutKeywordCardMaxSize error", e);
        }
        return strategyConfig.getTemplateSize();
    }

    @Override
    public void addTemplate(AutoRuleTemplateAddRequest request, StreamObserver<AutoRuleTemplateAddListResponse> responseObserver) {
        log.info("自动化规则模板addTemplate request {}", request);
        AutoRuleTemplateAddListResponse.Builder responseBuilder = AutoRuleTemplateAddListResponse.newBuilder();
        //参数校验
        Integer templateSize = advertiseAutoRuleTemplateDao.getTemplateSize(request.getPuid());
        if (!"keywordCard".equals(request.getRuleType())) {
            if (templateSize >= getAutoRuleTemplateWithoutKeywordCardMaxSize(request.getPuid())) {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg(Constants.AUTO_TEMPLATE_RULE_TITLE);
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
        }
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(request.getShopId());
        if (shopAuth == null) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("当前店铺无任何信息");
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        //如果是抢排名进行邮编可用校验
        if (AutoRuleTypeEnum.keywordCard.getValue().equals(request.getRuleType())) {
            if (Boolean.TRUE.equals(dynamicRefreshConfiguration.getKeywordCardCheckPostcode())) {
                List<String> postCodes = StringUtil.splitStr(request.getPostalCodeSettings());
                Map<String, PostcodeResult> stringPostcodeResultMap = grabRankingsPostcodeService.queryPostcode(request.getMarketplaceId(), postCodes);
                List<String> errorCode = postCodes.stream().filter(e -> !stringPostcodeResultMap.containsKey(e)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(errorCode)) {
                    responseBuilder.setCode(Result.ERROR);
                    responseBuilder.setMsg("邮编不合法：" + StringUtil.joinString(errorCode));
                    responseObserver.onNext(responseBuilder.build());
                    responseObserver.onCompleted();
                    return;
                }
            }
        }


        int day = 0;
        if (!reportTriggerAutoruleHelper.isGray(request.getPuid())) {
            String sellerId = shopAuth.getSellingPartnerId();
            QueryFeedStartRequestPb.QueryFeedStartRequest.Builder builder = QueryFeedStartRequestPb.QueryFeedStartRequest.newBuilder();
            builder.setSellerId(sellerId);
            builder.setMarketplaceId(shopAuth.getMarketplaceId());
            String startDate = null;
            if (!"keywordCard".equalsIgnoreCase(request.getRuleType())) {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg("当前店铺没有订阅小时级数据信息");
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            } else {
                startDate = LocalDate.now().minusDays(30).format(DateTimeFormatter.ISO_LOCAL_DATE);
            }

            int toDay = (int) LocalDateTimeUtil.convertChinaToSiteTime(LocalDateTime.now(),
                    shopAuth.getMarketplaceId()).toLocalDate().toEpochDay();
            int feedStartDate = (int) LocalDate.parse(startDate).toEpochDay();
            day = (toDay-feedStartDate) +1 ;
        }

        //拼接插入参数
        AdvertiseAutoRuleTemplate template = new AdvertiseAutoRuleTemplate();
        template.setPuid(request.getPuid());
        template.setShopId(request.getShopId());
        template.setMarketplaceId(request.getMarketplaceId());
        if (StringUtils.isNotBlank(request.getSetRelation())) {
            template.setSetRelation(request.getSetRelation());
        }

        if (AutoRuleTypeEnum.keywordCard.getValue().equals(request.getRuleType())) {
            template.setExecuteTimeSpaceValue(null);
            template.setExecuteTimeSpaceUnit(null);
        }

        if (AutoRuleTypeEnum.customRule.getValue().equals(request.getRuleType())) {
            template.setExecuteTimeSpaceValue(request.getExecuteTimeSpaceValue());
            template.setExecuteTimeSpaceUnit(request.getExecuteTimeSpaceUnit());
        }

        if (StringUtils.equalsAny(request.getRuleType(),
                AutoRuleTypeEnum.campaignOverBudgetRaiseBudget.getValue(),
                AutoRuleTypeEnum.campaignAcosRaiseBudget.getValue(),
                AutoRuleTypeEnum.searchQueryAutoUpdateBidding.getValue(),
                AutoRuleTypeEnum.keywordAcosLowerPrice.getValue(),
                AutoRuleTypeEnum.keywordAcosRaisePrice.getValue())) {

            if (!reportTriggerAutoruleHelper.isGray(request.getPuid())) {
                template.setExecuteTimeSpaceValue(null);
                template.setExecuteTimeSpaceUnit(null);
            } else {
                if (StringUtils.isNotBlank(request.getExecuteTimeSpaceValue()) && StringUtils.isNotBlank(request.getExecuteTimeSpaceUnit())) {
                    template.setExecuteTimeSpaceValue(request.getExecuteTimeSpaceValue());
                    template.setExecuteTimeSpaceUnit(request.getExecuteTimeSpaceUnit());
                } else {
                    template.setExecuteTimeSpaceValue("12");
                    template.setExecuteTimeSpaceUnit(AutoRuleExecuteTimeSpaceUnitEnum.HOUR.getValue());
                }
            }
        }


        if (StringUtils.isNotBlank(request.getMessageReminderType())) {
            template.setMessageReminderType(request.getMessageReminderType());
        }
        if (StringUtils.isNotBlank(request.getAsin())) {
            template.setAsin(request.getAsin());
        }
        if (StringUtils.isNotBlank(request.getSku())) {
            template.setSku(request.getSku());
        }
        if (StringUtils.isNotBlank(request.getTimeType())) {
            template.setTimeType(request.getTimeType());
        }
        if (CollectionUtils.isNotEmpty(request.getTimeRulesList())) {
            List<TimeRuleJson> timeRuleJsons = Lists.newArrayList();
            request.getTimeRulesList().forEach(e -> {
                e.getTimesList().forEach(t -> {
                    TimeRuleJson timeRuleJson = new TimeRuleJson();
                    timeRuleJson.setSiteDate(e.getSiteDate());
                    timeRuleJson.setStartTimeSite(t.getStartTimeSite());
                    timeRuleJson.setEndTimeSite(t.getEndTimeSite());
                    timeRuleJsons.add(timeRuleJson);
                });
            });
            template.setTimeRule(JSONUtil.objectToJson(timeRuleJsons));
        }
        if (StringUtils.isNotBlank(request.getChooseTimeType())) {
            template.setChooseTimeType(request.getChooseTimeType());
        }
        if ("all".equals(request.getChooseTimeType())) {
            List<TimeRuleJson> timeRuleJsons = Lists.newArrayList();
            for (int i = 1;i<=7;i++) {
                TimeRuleJson timeRuleJson = new TimeRuleJson();
                timeRuleJson.setSiteDate(i);
                timeRuleJson.setStartTimeSite(0);
                timeRuleJson.setEndTimeSite(24);
                timeRuleJsons.add(timeRuleJson);
            }
            template.setTimeRule(JSONUtil.objectToJson(timeRuleJsons));
        }
        if (request.hasDesiredPosition()) {
            DesiredPositionJson desiredPositionJson = new DesiredPositionJson();
            desiredPositionJson.setFrom(request.getDesiredPosition().getFrom());
            desiredPositionJson.setPage(request.getDesiredPosition().getPage());
            desiredPositionJson.setTo(request.getDesiredPosition().getTo());
            template.setDesiredPosition(JSONUtil.objectToJson(desiredPositionJson));
        }
        if (CollectionUtils.isNotEmpty(request.getAdDataRulesList())) {
            List<AdDataRuleJson> adDataRuleJsons = Lists.newArrayList();
            request.getAdDataRulesList().forEach(e->{
                AdDataRuleJson adDataRuleJson = new AdDataRuleJson();
                adDataRuleJson.setDay(Integer.valueOf(e.getDay()));
                adDataRuleJson.setExcludeDay(e.getExcludeDay());
                adDataRuleJson.setRuleIndex(e.getRuleIndex());
                adDataRuleJson.setRuleValue(e.getRuleValue());
                adDataRuleJson.setRuleOperator(e.getRuleOperatorType());
                adDataRuleJson.setRuleStatisticalModeType(e.getRuleStatisticalModeType());
                adDataRuleJsons.add(adDataRuleJson);
            });
            template.setAdDataRule(JSONUtil.objectToJson(adDataRuleJsons));
        }

        // 新增支持自动加价调整竞价比例
        if (request.hasAdDataOperate()) {
            AdDataOperateJson adDataOperateJson = new AdDataOperateJson();
            if (request.getAdDataOperate().hasRuleAction()) {
                adDataOperateJson.setRuleAction(request.getAdDataOperate().getRuleAction());
                adDataOperateJson.setRuleAdjust(request.getAdDataOperate().getRuleAdjust());
                adDataOperateJson.setAdJustValue(request.getAdDataOperate().getAdjustValue());
                adDataOperateJson.setLimitValue(request.getAdDataOperate().getLimitValue());
            }
            //竞价
            if(request.getAdDataOperate().hasBid()) {
                BidDataOperate bid = new BidDataOperate();
                bid.setRuleAdjust(request.getAdDataOperate().getBid().getRuleAdjust());
                bid.setRuleAction(request.getAdDataOperate().getBid().getRuleAction());
                bid.setAdJustValue(request.getAdDataOperate().getBid().getAdjustValue());
                bid.setLimitValue(request.getAdDataOperate().getBid().getLimitValue());
                adDataOperateJson.setBid(bid);
            }
            //顶部竞价比例
            if (request.getAdDataOperate().hasPlacementTopBid()) {
                BidDataOperate bidDataOperate = new BidDataOperate();
                bidDataOperate.setRuleAdjust(request.getAdDataOperate().getPlacementTopBid().getRuleAdjust());
                bidDataOperate.setRuleAction(request.getAdDataOperate().getPlacementTopBid().getRuleAction());
                bidDataOperate.setAdJustValue(request.getAdDataOperate().getPlacementTopBid().getAdjustValue());
                bidDataOperate.setLimitValue(request.getAdDataOperate().getPlacementTopBid().getLimitValue());
                adDataOperateJson.setPlacementTopBidRatio(bidDataOperate);
            }
            template.setAdDataOperate(JSONUtil.objectToJson(adDataOperateJson));
        }
        if (request.hasAutoPriceRule()) {
            AutoPriceRuleJson autoPriceRuleJson = new AutoPriceRuleJson();
            autoPriceRuleJson.setPage(request.getAutoPriceRule().getPage());
            autoPriceRuleJson.setRank(request.getAutoPriceRule().getRank());
            autoPriceRuleJson.setContinuousFrequency(request.getAutoPriceRule().getContinuousFrequency());
            template.setAutoPriceRule(JSONUtil.objectToJson(autoPriceRuleJson));
        }
        // 新增支持自动降价调整竞价比例
        if (request.hasAutoPriceOperate()) {
            AutoPriceOperateJson autoPriceOperateJson = new AutoPriceOperateJson();
            if (request.getAutoPriceOperate().hasRuleAction()) {
                autoPriceOperateJson.setRuleAction(request.getAutoPriceOperate().getRuleAction());
                autoPriceOperateJson.setRuleAdjust(request.getAutoPriceOperate().getRuleAdjust());
                autoPriceOperateJson.setAdJustValue(request.getAutoPriceOperate().getAdjustValue());
                autoPriceOperateJson.setLimitValue(request.getAutoPriceOperate().getLimitValue());
            }
            //竞价
            if(request.getAutoPriceOperate().hasBid()) {
                BidDataOperate bid = new BidDataOperate();
                bid.setRuleAdjust(request.getAutoPriceOperate().getBid().getRuleAdjust());
                bid.setRuleAction(request.getAutoPriceOperate().getBid().getRuleAction());
                bid.setAdJustValue(request.getAutoPriceOperate().getBid().getAdjustValue());
                bid.setLimitValue(request.getAutoPriceOperate().getBid().getLimitValue());
                autoPriceOperateJson.setBid(bid);
            }
            //顶部竞价比例
            if (request.getAutoPriceOperate().hasPlacementTopBid()) {
                BidDataOperate bidDataOperate = new BidDataOperate();
                bidDataOperate.setRuleAdjust(request.getAutoPriceOperate().getPlacementTopBid().getRuleAdjust());
                bidDataOperate.setRuleAction(request.getAutoPriceOperate().getPlacementTopBid().getRuleAction());
                bidDataOperate.setAdJustValue(request.getAutoPriceOperate().getPlacementTopBid().getAdjustValue());
                bidDataOperate.setLimitValue(request.getAutoPriceOperate().getPlacementTopBid().getLimitValue());
                autoPriceOperateJson.setPlacementTopBidRatio(bidDataOperate);
            }
            template.setAutoPriceOperate(JSONUtil.objectToJson(autoPriceOperateJson));
        }
        // 新增支持回调竞价比例
        if (request.hasBiddingCallbackOperate()) {
            BiddingCallbackOperateJson biddingCallbackOperateJson = new BiddingCallbackOperateJson();
            if (request.getBiddingCallbackOperate().hasAdjustType()) {
                biddingCallbackOperateJson.setAdjustType(request.getBiddingCallbackOperate().getAdjustType());
                biddingCallbackOperateJson.setAdJustValue(request.getBiddingCallbackOperate().getAdjustValue());
                if (StringUtils.isNotBlank(request.getBiddingCallbackOperate().getLimitValue())) {
                    biddingCallbackOperateJson.setLimitValue(request.getBiddingCallbackOperate().getLimitValue());
                }
            }
            //竞价
            if (request.getBiddingCallbackOperate().hasBid()) {
                BidDataOperate bid = new BidDataOperate();
                bid.setAdjustType(request.getBiddingCallbackOperate().getBid().getAdjustType());
                bid.setAdJustValue(request.getBiddingCallbackOperate().getBid().getAdjustValue());
                if (StringUtils.isNotBlank(request.getBiddingCallbackOperate().getBid().getLimitValue())) {
                    bid.setLimitValue(request.getBiddingCallbackOperate().getBid().getLimitValue());
                }
                biddingCallbackOperateJson.setBid(bid);
            }
            //顶部竞价比例
            if (request.getBiddingCallbackOperate().hasPlacementTopBid()) {
                BidDataOperate bidDataOperate = new BidDataOperate();
                bidDataOperate.setAdjustType(request.getBiddingCallbackOperate().getPlacementTopBid().getAdjustType());
                bidDataOperate.setAdJustValue(request.getBiddingCallbackOperate().getPlacementTopBid().getAdjustValue());
                if (StringUtils.isNotBlank(request.getBiddingCallbackOperate().getPlacementTopBid().getLimitValue())) {
                    bidDataOperate.setLimitValue(request.getBiddingCallbackOperate().getPlacementTopBid().getLimitValue());
                }
                biddingCallbackOperateJson.setPlacementTopBidRatio(bidDataOperate);
            }
            template.setBiddingCallbackOperate(JSONUtil.objectToJson(biddingCallbackOperateJson));
        }
        if (request.hasCallbackState()) {
            template.setCallbackState(String.valueOf(request.getCallbackState()));
        }
        if (request.hasCallbackOperate()) {
            CallbackOperateJson callbackOperateJson = new CallbackOperateJson();
            if (!request.getCallbackState()) {
                callbackOperateJson.setAdjustType("adjustFixed");
            } else {
                callbackOperateJson.setAdjustType(request.getCallbackOperate().getAdjustType());
            }
            callbackOperateJson.setAdJustValue(request.getCallbackOperate().getAdjustValue());
            if (StringUtils.isNotBlank(request.getCallbackOperate().getLimitValue())) {
                callbackOperateJson.setLimitValue(request.getCallbackOperate().getLimitValue());
            }
            template.setCallbackOperate(JSONUtil.objectToJson(callbackOperateJson));
        }
        if (StringUtils.isNotBlank(request.getCheckFrequency())) {
            template.setCheckFrequency(request.getCheckFrequency());
        }
        if (StringUtils.isNotBlank(request.getPostalCodeSettings())) {
            template.setPostalCodeSettings(request.getPostalCodeSettings());
        }
        if (request.hasStartDate()) {
            template.setStartDate(LocalDate.parse(request.getStartDate(),DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
        }
        if (request.hasEndDate()) {
            template.setEndDate(LocalDate.parse(request.getEndDate(),DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
        }
        template.setTemplateName(request.getTemplateName());
        template.setRuleType(request.getRuleType());
        template.setAdType(request.getAdType());
        template.setItemType(request.getItemType());
        template.setExecuteType(request.getExecuteType());
        template.setCreateUid(request.getCreateUid());
        template.setUpdateUid(request.getUpdateUid());
        template.setCreateName(request.getCreateName());
        template.setUpdateName(request.getUpdateName());
        template.setStatus(AutoRuleEnableStatusEnum.ENABLED.getCode());
        if (StringUtils.isNotBlank(request.getPushMessageType())) {
            template.setPushMessageType(request.getPushMessageType());
        } else {
            template.setPushMessageType("none");
        }
        template.setVersion(1);
        List<AutoRuleJson> autoRuleJsonList = Lists.newArrayList();
        List<PerformOperationJson> performOperationJsonList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(request.getRuleListList())) {
            for(Rule e : request.getRuleListList()) {
                if (!reportTriggerAutoruleHelper.isGray(request.getPuid())) {
                    if (Integer.parseInt(e.getDay()) > day) {
                        responseBuilder.setCode(Result.ERROR);
                        responseBuilder.setMsg("店铺:" + shopAuth.getName() + "订阅订阅实时数据不满" + e.getDay() + ",请选择较小的时间范围");
                        responseObserver.onNext(responseBuilder.build());
                        responseObserver.onCompleted();
                        return;
                    }
                }

                AutoRuleJson autoRuleJson = new AutoRuleJson();
                autoRuleJson.setDay(Integer.valueOf(e.getDay()));
                if (StringUtils.isNotBlank(e.getExcludeDay())) {
                    autoRuleJson.setExcludeDay(Integer.valueOf(e.getExcludeDay()));
                }
                autoRuleJson.setRuleIndex(e.getRuleIndex());
                autoRuleJson.setRuleOperator(e.getRuleOperatorType());
                autoRuleJson.setRuleValue(e.getRuleValue());
                autoRuleJson.setExcludeCheckStatus(e.getExcludeCheckStatus());
                autoRuleJson.setAfterRuleValue(e.getAfterRuleValue());
                autoRuleJson.setRuleStatisticalModeType(e.getRuleStatisticalModeType());
                autoRuleJsonList.add(autoRuleJson);
            }
        }
        if (CollectionUtils.isNotEmpty(request.getPerformOperationListList())) {
            request.getPerformOperationListList().forEach(e -> {
                PerformOperationJson performOperationJson = new PerformOperationJson();
                performOperationJson.setRuleAction(e.getRuleAction());
                performOperationJson.setRuleAdjust(e.getRuleAdjust());
                performOperationJson.setAdJustValue(e.getAdjustValue());
                performOperationJson.setLimitValue(e.getLimitValue());
                if (CollectionUtils.isNotEmpty(e.getExpressionTypesList())) {
                    performOperationJson.setExpressionType(StringUtils.join(e.getExpressionTypesList(), ","));
                }
                if (StringUtils.isNotBlank(e.getNegativeType())) {
                    performOperationJson.setNegativeType(e.getNegativeType());
                }
                if (StringUtils.isNotBlank(e.getBaseValueType())) {
                    performOperationJson.setBaseValueType(e.getBaseValueType());
                }
                if (StringUtils.isNotBlank(e.getAdPlaceTopValue())) {
                    performOperationJson.setAdPlaceTopValue(e.getAdPlaceTopValue());
                }
                if (StringUtils.isNotBlank(e.getAdPlaceProductValue())) {
                    performOperationJson.setAdPlaceProductValue(e.getAdPlaceProductValue());
                }
                if (StringUtils.isNotBlank(e.getAdOtherValue())) {
                    performOperationJson.setAdOtherValue(e.getAdOtherValue());
                }
                if (StringUtils.isNotBlank(e.getAppointAdGroupType())) {
                    performOperationJson.setAppointAdGroupType(e.getAppointAdGroupType());
                }
                if (StringUtils.isNotBlank(e.getPortfolioId())) {
                    performOperationJson.setPortfolioId(e.getPortfolioId());
                }
                if (StringUtils.isNotBlank(e.getCampaignId())) {
                    performOperationJson.setCampaignId(e.getCampaignId());
                }
                if (StringUtils.isNotBlank(e.getAdGroupId())) {
                    performOperationJson.setAdGroupId(e.getAdGroupId());
                }
                if (StringUtils.isNotBlank(e.getBidType())) {
                    performOperationJson.setBidType(e.getBidType());
                }
                if (StringUtils.isNotBlank(e.getBidValue())) {
                    performOperationJson.setBidValue(e.getBidValue());
                }
                if (StringUtils.isNotBlank(e.getMatchType())) {
                    performOperationJson.setMatchType(e.getMatchType());
                }
                performOperationJsonList.add(performOperationJson);
            });
        }
        template.setPerformOperation(JSONUtil.objectToJson(performOperationJsonList));
        template.setRule(JSONUtil.objectToJson(autoRuleJsonList));

        //维护报告初始化和推送
        if (request.hasReportCompletePush() && Objects.nonNull(request.getReportCompletePush())) {
            template.setReportCompletePush(request.getReportCompletePush().getValue());
        } else {
            template.setReportCompletePush(AutoRuleReportCompleteEnum.PUSH_NOT_REQUIRED.getValue());
        }

        //维护下报告拉取完记录表
        advertiseAutoRuleTemplateReportCompleteService.insertOrUpdate(new AdvertiseAutoRuleTemplateReportComplete(request.getPuid(), request.getShopId(), AutoRuleReportCompleteEnum.NOT_COMPLETE.getValue()));

        Result<AddTemplateVo> result = advertiseAutoRuleTemplateService.insertTemplate(request.getPuid(), template, request.getTraceId());
        if (result.getCode() == Result.SUCCESS) {
            if (result.getData() != null) {
                AutoRuleTemplateRpc.Builder dataBuilder = AutoRuleTemplateRpc.newBuilder();
                dataBuilder.setTemplateId(result.getData().getTemplateId());
                if (result.getData().getOperationType() != null) {
                    dataBuilder.setOperationType(result.getData().getOperationType());
                }
                responseBuilder.setData(dataBuilder.build());
            }
        }
        responseBuilder.setMsg(result.getMsg());
        responseBuilder.setCode(result.getCode());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }


    @Override
    public void updateTemplate(AutoRuleTemplateUpdateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("自动化规则模板updateTemplate request {}", request);
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        //参数校验
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(request.getShopId());
        if (shopAuth == null) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("当前店铺无任何信息");
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        AdvertiseAutoRuleTemplate existTemplate = advertiseAutoRuleTemplateDao.selectByPrimaryKey(request.getPuid(), request.getId());
        if (Objects.isNull(existTemplate)) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("模板不存在");
            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();
            return;
        }

        //检查执行动作与广告类型
        if (CollectionUtils.isNotEmpty(request.getPerformOperationListList())) {
            String ruleAction = request.getPerformOperationListList().get(0).getRuleAction();
            CheckRuleIndexAndActionStatusVo checkVo = advertiseAutoRuleStatusService.checkOperation4Template(existTemplate, ruleAction);
            if (!checkVo.getCheckStatus()) {
                responseBuilder.setCode(Int32Value.of(Result.ERROR));
                responseBuilder.setMsg(checkVo.getCheckMsg());
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            }
        }

        int day = 0;
        if (!reportTriggerAutoruleHelper.isGray(request.getPuid())) {
            String sellerId = shopAuth.getSellingPartnerId();
            QueryFeedStartRequestPb.QueryFeedStartRequest.Builder builder = QueryFeedStartRequestPb.QueryFeedStartRequest.newBuilder();
            builder.setSellerId(sellerId);
            builder.setMarketplaceId(shopAuth.getMarketplaceId());
            String startDate = null;
            if (!"keywordCard".equalsIgnoreCase(request.getRuleType())) {
                responseBuilder.setCode(Int32Value.of(Result.ERROR));
                responseBuilder.setMsg("当前店铺没有订阅小时级数据信息");
                responseObserver.onNext(responseBuilder.build());
                responseObserver.onCompleted();
                return;
            } else {
                startDate = LocalDate.now().minusDays(30).format(DateTimeFormatter.ISO_LOCAL_DATE);
            }

            int toDay = (int) LocalDateTimeUtil.convertChinaToSiteTime(LocalDateTime.now(),
                    shopAuth.getMarketplaceId()).toLocalDate().toEpochDay();
            int feedStartDate = (int) LocalDate.parse(startDate).toEpochDay();
            day = (toDay-feedStartDate) + 1 ;
        }


        AdvertiseAutoRuleTemplate template = new AdvertiseAutoRuleTemplate();
        template.setId(request.getId());
        template.setPuid(request.getPuid());
        template.setShopId(request.getShopId());
        if (StringUtils.isNotBlank(request.getSetRelation())) {
            template.setSetRelation(request.getSetRelation());
        }
        if (StringUtils.isNotBlank(request.getExecuteTimeSpaceValue())) {
            template.setExecuteTimeSpaceValue(request.getExecuteTimeSpaceValue());
        }
        if (StringUtils.isNotBlank(request.getExecuteTimeSpaceUnit())) {
            template.setExecuteTimeSpaceUnit(request.getExecuteTimeSpaceUnit());
        }
        if (StringUtils.isNotBlank(request.getMessageReminderType())) {
            template.setMessageReminderType(request.getMessageReminderType());
        }
        if (StringUtils.isNotBlank(request.getTimeType())) {
            template.setTimeType(request.getTimeType());
        }
        if (CollectionUtils.isNotEmpty(request.getTimeRulesList())) {
            List<TimeRuleJson> timeRuleJsons = Lists.newArrayList();
            request.getTimeRulesList().forEach(e -> {
                e.getTimesList().forEach(t -> {
                    TimeRuleJson timeRuleJson = new TimeRuleJson();
                    timeRuleJson.setSiteDate(e.getSiteDate());
                    timeRuleJson.setStartTimeSite(t.getStartTimeSite());
                    timeRuleJson.setEndTimeSite(t.getEndTimeSite());
                    timeRuleJsons.add(timeRuleJson);
                });
            });
            template.setTimeRule(JSONUtil.objectToJson(timeRuleJsons));
        }
        if (StringUtils.isNotBlank(request.getChooseTimeType())) {
            template.setChooseTimeType(request.getChooseTimeType());
        }
        if ("all".equals(request.getChooseTimeType())) {
            List<TimeRuleJson> timeRuleJsons = Lists.newArrayList();
            for (int i = 1;i<=7;i++) {
                TimeRuleJson timeRuleJson = new TimeRuleJson();
                timeRuleJson.setSiteDate(i);
                timeRuleJson.setStartTimeSite(0);
                timeRuleJson.setEndTimeSite(24);
                timeRuleJsons.add(timeRuleJson);
            }
            template.setTimeRule(JSONUtil.objectToJson(timeRuleJsons));
        }
        if (request.hasDesiredPosition()) {
            DesiredPositionJson desiredPositionJson = new DesiredPositionJson();
            desiredPositionJson.setFrom(request.getDesiredPosition().getFrom());
            desiredPositionJson.setPage(request.getDesiredPosition().getPage());
            desiredPositionJson.setTo(request.getDesiredPosition().getTo());
            template.setDesiredPosition(JSONUtil.objectToJson(desiredPositionJson));
        }
        if (CollectionUtils.isNotEmpty(request.getAdDataRulesList())) {
            List<AdDataRuleJson> adDataRuleJsons = Lists.newArrayList();
            request.getAdDataRulesList().forEach(e->{
                AdDataRuleJson adDataRuleJson = new AdDataRuleJson();
                adDataRuleJson.setDay(Integer.valueOf(e.getDay()));
                adDataRuleJson.setExcludeDay(e.getExcludeDay());
                adDataRuleJson.setRuleIndex(e.getRuleIndex());
                adDataRuleJson.setRuleValue(e.getRuleValue());
                adDataRuleJson.setRuleOperator(e.getRuleOperatorType());
                adDataRuleJson.setRuleStatisticalModeType(e.getRuleStatisticalModeType());
                adDataRuleJsons.add(adDataRuleJson);
            });
            template.setAdDataRule(JSONUtil.objectToJson(adDataRuleJsons));
        }

        if (request.hasAdDataOperate()) {
            AdDataOperateJson adDataOperateJson = new AdDataOperateJson();
            if (request.getAdDataOperate().hasRuleAction()) {
                adDataOperateJson.setRuleAction(request.getAdDataOperate().getRuleAction());
                adDataOperateJson.setRuleAdjust(request.getAdDataOperate().getRuleAdjust());
                adDataOperateJson.setAdJustValue(request.getAdDataOperate().getAdjustValue());
                adDataOperateJson.setLimitValue(request.getAdDataOperate().getLimitValue());
            }
            //修改竞价
            if(request.getAdDataOperate().hasBid()) {
                BidDataOperate bid = new BidDataOperate();
                bid.setRuleAdjust(request.getAdDataOperate().getBid().getRuleAdjust());
                bid.setRuleAction(request.getAdDataOperate().getBid().getRuleAction());
                bid.setAdJustValue(request.getAdDataOperate().getBid().getAdjustValue());
                bid.setLimitValue(request.getAdDataOperate().getBid().getLimitValue());
                adDataOperateJson.setBid(bid);
            }
            //修改顶部竞价比例
            if (request.getAdDataOperate().hasPlacementTopBid()) {
                BidDataOperate bidDataOperate = new BidDataOperate();
                bidDataOperate.setRuleAdjust(request.getAdDataOperate().getPlacementTopBid().getRuleAdjust());
                bidDataOperate.setRuleAction(request.getAdDataOperate().getPlacementTopBid().getRuleAction());
                bidDataOperate.setAdJustValue(request.getAdDataOperate().getPlacementTopBid().getAdjustValue());
                bidDataOperate.setLimitValue(request.getAdDataOperate().getPlacementTopBid().getLimitValue());
                adDataOperateJson.setPlacementTopBidRatio(bidDataOperate);
            }
            template.setAdDataOperate(JSONUtil.objectToJson(adDataOperateJson));
        }
        if (request.hasAutoPriceRule()) {
            AutoPriceRuleJson autoPriceRuleJson = new AutoPriceRuleJson();
            autoPriceRuleJson.setPage(request.getAutoPriceRule().getPage());
            autoPriceRuleJson.setRank(request.getAutoPriceRule().getRank());
            autoPriceRuleJson.setContinuousFrequency(request.getAutoPriceRule().getContinuousFrequency());
            template.setAutoPriceRule(JSONUtil.objectToJson(autoPriceRuleJson));
        }
        if (request.hasAutoPriceOperate()) {
            AutoPriceOperateJson autoPriceOperateJson = new AutoPriceOperateJson();
            if (request.getAutoPriceOperate().hasRuleAction()) {
                autoPriceOperateJson.setRuleAction(request.getAutoPriceOperate().getRuleAction());
                autoPriceOperateJson.setRuleAdjust(request.getAutoPriceOperate().getRuleAdjust());
                autoPriceOperateJson.setAdJustValue(request.getAutoPriceOperate().getAdjustValue());
                autoPriceOperateJson.setLimitValue(request.getAutoPriceOperate().getLimitValue());
            }
            //修改竞价
            if (request.getAutoPriceOperate().hasBid()) {
                BidDataOperate bid = new BidDataOperate();
                bid.setRuleAdjust(request.getAutoPriceOperate().getBid().getRuleAdjust());
                bid.setRuleAction(request.getAutoPriceOperate().getBid().getRuleAction());
                bid.setAdJustValue(request.getAutoPriceOperate().getBid().getAdjustValue());
                bid.setLimitValue(request.getAutoPriceOperate().getBid().getLimitValue());
                autoPriceOperateJson.setBid(bid);
            }
            //修改顶部竞价比例
            if (request.getAutoPriceOperate().hasPlacementTopBid()) {
                BidDataOperate bidDataOperate = new BidDataOperate();
                bidDataOperate.setRuleAdjust(request.getAutoPriceOperate().getPlacementTopBid().getRuleAdjust());
                bidDataOperate.setRuleAction(request.getAutoPriceOperate().getPlacementTopBid().getRuleAction());
                bidDataOperate.setAdJustValue(request.getAutoPriceOperate().getPlacementTopBid().getAdjustValue());
                bidDataOperate.setLimitValue(request.getAutoPriceOperate().getPlacementTopBid().getLimitValue());
                autoPriceOperateJson.setPlacementTopBidRatio(bidDataOperate);
            }
            template.setAutoPriceOperate(JSONUtil.objectToJson(autoPriceOperateJson));
        }
        if (request.hasBiddingCallbackOperate()) {
            BiddingCallbackOperateJson biddingCallbackOperateJson = new BiddingCallbackOperateJson();
            if (request.getBiddingCallbackOperate().hasAdjustType()) {
                biddingCallbackOperateJson.setAdjustType(request.getBiddingCallbackOperate().getAdjustType());
                biddingCallbackOperateJson.setAdJustValue(request.getBiddingCallbackOperate().getAdjustValue());
                if (StringUtils.isNotBlank(request.getBiddingCallbackOperate().getLimitValue())) {
                    biddingCallbackOperateJson.setLimitValue(request.getBiddingCallbackOperate().getLimitValue());
                }
            }
            //修改竞价
            if (request.getBiddingCallbackOperate().hasBid()) {
                BidDataOperate bid = new BidDataOperate();
                bid.setAdjustType(request.getBiddingCallbackOperate().getBid().getAdjustType());
                bid.setAdJustValue(request.getBiddingCallbackOperate().getBid().getAdjustValue());
                if (StringUtils.isNotBlank(request.getBiddingCallbackOperate().getBid().getLimitValue())) {
                    bid.setLimitValue(request.getBiddingCallbackOperate().getBid().getLimitValue());
                }
                biddingCallbackOperateJson.setBid(bid);
            }
            //修改竞价比例
            if (request.getBiddingCallbackOperate().hasPlacementTopBid()) {
                BidDataOperate bidDataOperate = new BidDataOperate();
                bidDataOperate.setAdjustType(request.getBiddingCallbackOperate().getPlacementTopBid().getAdjustType());
                bidDataOperate.setAdJustValue(request.getBiddingCallbackOperate().getPlacementTopBid().getAdjustValue());
                if (StringUtils.isNotBlank(request.getBiddingCallbackOperate().getPlacementTopBid().getLimitValue())) {
                    bidDataOperate.setLimitValue(request.getBiddingCallbackOperate().getPlacementTopBid().getLimitValue());
                }
                biddingCallbackOperateJson.setPlacementTopBidRatio(bidDataOperate);
            }

            template.setBiddingCallbackOperate(JSONUtil.objectToJson(biddingCallbackOperateJson));
        }
        if (request.hasCallbackState()) {
            template.setCallbackState(String.valueOf(request.getCallbackState()));
        }
        if (request.hasCallbackOperate()) {
            CallbackOperateJson callbackOperateJson = new CallbackOperateJson();
            if (!request.getCallbackState()) {
                callbackOperateJson.setAdjustType("adjustFixed");
            } else {
                callbackOperateJson.setAdjustType(request.getCallbackOperate().getAdjustType());
            }
            callbackOperateJson.setAdJustValue(request.getCallbackOperate().getAdjustValue());
            if (StringUtils.isNotBlank(request.getCallbackOperate().getLimitValue())) {
                callbackOperateJson.setLimitValue(request.getCallbackOperate().getLimitValue());
            }
            template.setCallbackOperate(JSONUtil.objectToJson(callbackOperateJson));
        }
        if (StringUtils.isNotBlank(request.getCheckFrequency())) {
            template.setCheckFrequency(request.getCheckFrequency());
        }
        if (StringUtils.isNotBlank(request.getPostalCodeSettings())) {
            template.setPostalCodeSettings(request.getPostalCodeSettings());
        }
        if (request.hasStartDate()) {
            template.setStartDate(LocalDate.parse(request.getStartDate(),DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
        }
        if (request.hasEndDate()) {
            template.setEndDate(LocalDate.parse(request.getEndDate(),DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
        }

        template.setId(request.getId());
        template.setRuleType(request.getRuleType());
        if (StringUtils.isNotBlank(request.getExecuteType())) {
            template.setExecuteType(request.getExecuteType());
        }
        template.setUpdateTime(LocalDateTime.now());
        template.setUpdateUid(request.getUpdateUid());
        template.setUpdateName(request.getUpdateName());
        template.setTemplateName(request.getTemplateName());
        if (StringUtils.isNotBlank(request.getPushMessageType())) {
            template.setPushMessageType(request.getPushMessageType());
        } else {
            template.setPushMessageType("none");
        }
        List<AutoRuleJson> autoRuleJsonList = Lists.newArrayList();
        List<PerformOperationJson> performOperationJsonList = Lists.newArrayList();
        for (Rule e : request.getRuleListList()) {
            if (!reportTriggerAutoruleHelper.isGray(request.getPuid())) {
                if (Integer.parseInt(e.getDay()) > day) {
                    responseBuilder.setCode(Int32Value.of(Result.ERROR));
                    responseBuilder.setMsg("店铺:"+shopAuth.getName()+"订阅订阅实时数据不满"+e.getDay()+",请选择较小的时间范围");
                    responseObserver.onNext(responseBuilder.build());
                    responseObserver.onCompleted();
                    return;
                }
            }
            AutoRuleJson autoRuleJson = new AutoRuleJson();
            autoRuleJson.setDay(Integer.valueOf(e.getDay()));
            if (StringUtils.isNotBlank(e.getExcludeDay())) {
                autoRuleJson.setExcludeDay(Integer.valueOf(e.getExcludeDay()));
            }
            autoRuleJson.setRuleIndex(e.getRuleIndex());
            autoRuleJson.setRuleOperator(e.getRuleOperatorType());
            autoRuleJson.setRuleValue(e.getRuleValue());
            autoRuleJson.setRuleStatisticalModeType(e.getRuleStatisticalModeType());
            autoRuleJson.setExcludeCheckStatus(e.getExcludeCheckStatus());
            autoRuleJson.setAfterRuleValue(e.getAfterRuleValue());
            autoRuleJsonList.add(autoRuleJson);
        }
        request.getPerformOperationListList().forEach(e -> {
            PerformOperationJson performOperationJson = new PerformOperationJson();
            performOperationJson.setRuleAction(e.getRuleAction());
            performOperationJson.setRuleAdjust(e.getRuleAdjust());
            performOperationJson.setAdJustValue(e.getAdjustValue());
            performOperationJson.setLimitValue(e.getLimitValue());
            if (CollectionUtils.isNotEmpty(e.getExpressionTypesList())) {
                performOperationJson.setExpressionType(StringUtils.join(e.getExpressionTypesList(), ","));
            }
            if (StringUtils.isNotBlank(e.getNegativeType())) {
                performOperationJson.setNegativeType(e.getNegativeType());
            }
            if (StringUtils.isNotBlank(e.getBaseValueType())) {
                performOperationJson.setBaseValueType(e.getBaseValueType());
            }
            if (StringUtils.isNotBlank(e.getAdPlaceTopValue())) {
                performOperationJson.setAdPlaceTopValue(e.getAdPlaceTopValue());
            }
            if (StringUtils.isNotBlank(e.getAdPlaceProductValue())) {
                performOperationJson.setAdPlaceProductValue(e.getAdPlaceProductValue());
            }
            if (StringUtils.isNotBlank(e.getAdOtherValue())) {
                performOperationJson.setAdOtherValue(e.getAdOtherValue());
            }
            if (StringUtils.isNotBlank(e.getAppointAdGroupType())) {
                performOperationJson.setAppointAdGroupType(e.getAppointAdGroupType());
            }
            if (StringUtils.isNotBlank(e.getPortfolioId())) {
                performOperationJson.setPortfolioId(e.getPortfolioId());
            }
            if (StringUtils.isNotBlank(e.getCampaignId())) {
                performOperationJson.setCampaignId(e.getCampaignId());
            }
            if (StringUtils.isNotBlank(e.getAdGroupId())) {
                performOperationJson.setAdGroupId(e.getAdGroupId());
            }
            if (StringUtils.isNotBlank(e.getBidType())) {
                performOperationJson.setBidType(e.getBidType());
            }
            if (StringUtils.isNotBlank(e.getBidValue())) {
                performOperationJson.setBidValue(e.getBidValue());
            }
            if (StringUtils.isNotBlank(e.getMatchType())) {
                performOperationJson.setMatchType(e.getMatchType());
            }
            performOperationJsonList.add(performOperationJson);
        });
        template.setRule(JSONUtil.objectToJson(autoRuleJsonList));
        template.setPerformOperation(JSONUtil.objectToJson(performOperationJsonList));

        //维护报告初始化和推送
        if (request.hasReportCompletePush() && Objects.nonNull(request.getReportCompletePush())) {
            template.setReportCompletePush(request.getReportCompletePush().getValue());
        } else {
            template.setReportCompletePush(AutoRuleReportCompleteEnum.PUSH_NOT_REQUIRED.getValue());
        }

        Result<Long> result = advertiseAutoRuleTemplateService.updateTemplate(request.getPuid(), template, request.getTraceId());
        if (result.getCode() == Result.ERROR) {
            responseBuilder.setMsg(result.getMsg());
            responseBuilder.setCode(Int32Value.of(result.getCode()));
        } else {
            responseBuilder.setMsg("模板更新成功");
            responseBuilder.setData(result.getData().toString());
            responseBuilder.setCode(Int32Value.of(Result.SUCCESS));
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void delTemplate(AutoRuleTemplateDelRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("模板 delTemplate request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasTemplateId() || !request.hasPuid()) {
            builder.setMsg("请求参数错误");
            builder.setCode(Int32Value.of(Result.ERROR));
        } else {
            Result<String> result = advertiseAutoRuleTemplateService.deleteTemplate(request.getPuid(), request.getTemplateId(), request.getTraceId());
            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getTemplate(AutoGetTemplateRequest request, StreamObserver<AutoGetTemplateResponse> responseObserver) {
        log.info("自动化规则模板 getTemplate request {}", request);
        AutoGetTemplateResponse.Builder responseBuilder = AutoGetTemplateResponse.newBuilder();
        AutoRuleTemplateRpcVo.Builder builder = AutoRuleTemplateRpcVo.newBuilder();
        // 获取模板信息
        AdvertiseAutoRuleTemplate template = advertiseAutoRuleTemplateService.getTemplate(request.getPuid(), request.getTemplateId(),request.getTraceId());
        if(template == null){
            throw new SponsoredBizException("此模板不存在或已被删除！");
        }
        // 获取组受控对象类型
        List<AdvertiseAutoRuleStatus> advertiseAutoRuleStatusList = advertiseAutoRuleStatusDao.selectChildrenItemType(template.getPuid(), template.getShopId(), template.getId());
        String childrenItemType = null;
        if(CollectionUtils.isNotEmpty(advertiseAutoRuleStatusList)){
            childrenItemType = advertiseAutoRuleStatusList.get(0).getChildrenItemType();
        }
        // 获取店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(template.getShopId());
        // 构建店铺基础信息 -- 与列表页公用方法
        buildTemplateBase(template,builder,childrenItemType,shopAuth);
        // 构建店铺json信息 -- 与列表页公用方法
        buildTemplateJson(template,builder,new HashMap<>());
        // 构建店铺额外信息 -- 列表页没有的字段
        buildTemplateExtra(request, template, builder);
        responseBuilder.setData(builder.build());
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     * 构建店铺额外信息
     */
    private void buildTemplateExtra(AutoGetTemplateRequest request, AdvertiseAutoRuleTemplate template, AutoRuleTemplateRpcVo.Builder builder) {
        AdvertiseAutoRuleTemplateReportComplete reportComplete = advertiseAutoRuleTemplateReportCompleteService.queryReportComplete(request.getPuid(), template.getShopId());
        if (Objects.isNull(reportComplete)) {
            builder.setReportComplete(0);
        } else {
            builder.setReportComplete(reportComplete.getReportComplete());
        }
        if (AutoRuleTypeEnum.keywordCard.getValue().equalsIgnoreCase(template.getRuleType())) {
            String postalCodeSettings = template.getPostalCodeSettings();
            if(StringUtils.isNotBlank(postalCodeSettings)) {
                Map<String, PostcodeData> postcodeByCode = grabRankingsPostcodeService.getPostcodeByCode(template.getMarketplaceId(), StringUtil.splitStr(postalCodeSettings, ","));
                if (MapUtils.isNotEmpty(postcodeByCode)) {
                    postcodeByCode.forEach((k, v)-> {
                        builder.putLabel(k, grabRankingsPostcodeService.getFrontEndLabel(v));
                    });
                }
            }
        }
    }

    @Override
    public void updateTemplateStatus(AutoRuleUpdateTemplateStatusRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("自动化规则模板 updateTemplateStatus request {}", request);
        int puid = request.getPuid();
        List<Long> templateIdList = request.getTemplateIdList();
        String status = request.getStatus();

        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (CollectionUtils.isEmpty(templateIdList) || !StringUtils.equalsAny(status, AutoRuleEnableStatusEnum.ENABLED.getCode(), AutoRuleEnableStatusEnum.DISABLED.getCode())) {
            log.info("自动化规则模板 updateTemplateStatus templateIdList empty or status error");
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误，请刷新页面重试");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        //实际是否需要处理，没有需要处理的数据也返回成功
        List<AdvertiseAutoRuleTemplate> templateList = getValidUpdateTemplateList(puid, templateIdList, status);
        if (CollectionUtils.isEmpty(templateList)) {
            log.info("自动化规则模板 updateTemplateStatus 修改状态和实际状态一致，没有需要处理的模板");
            builder.setCode(Int32Value.of(Result.SUCCESS));
            builder.setMsg("");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        advertiseAutoRuleTemplateService.updateTemplateStatus(puid, templateList, status, request.getUpdateUid());
        builder.setCode(Int32Value.of(Result.SUCCESS));
        builder.setMsg("");
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private List<AdvertiseAutoRuleTemplate> getValidUpdateTemplateList(int puid, List<Long> templateIdList, String status) {
        //查询模板，开启则查关闭，关闭则查开启
        String queryStatus;
        if (AutoRuleEnableStatusEnum.ENABLED.getCode().equals(status)) {
            queryStatus = AutoRuleEnableStatusEnum.DISABLED.getCode();
        } else {
            queryStatus = AutoRuleEnableStatusEnum.ENABLED.getCode();
        }
        List<AdvertiseAutoRuleTemplate> queryTemplateList = advertiseAutoRuleTemplateDao.getListByLongIdList(puid, templateIdList);
        List<AdvertiseAutoRuleTemplate> templateList = queryTemplateList.stream().filter(x -> x.getStatus().equals(queryStatus)).collect(Collectors.toList());
        return templateList;
    }

}
