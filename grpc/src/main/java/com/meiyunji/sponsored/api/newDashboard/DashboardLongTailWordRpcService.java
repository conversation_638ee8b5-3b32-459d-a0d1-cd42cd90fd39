package com.meiyunji.sponsored.api.newDashboard;

import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.rpc.newDashboard.*;
import com.meiyunji.sponsored.rpc.newDashboard.vo.UrlResponse;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardLongTailWordDataDto;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardLongTailWordService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardBaseReqVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 长尾词面板接口
 * @author: zzh
 * @date: 2025-03-14  10:41
 */
@GRpcService
@Slf4j
public class DashboardLongTailWordRpcService extends RpcDashboardLongTailWordServiceGrpc.RpcDashboardLongTailWordServiceImplBase {

    @Autowired
    private IDashboardLongTailWordService dashboardLongTailWordService;

    @Override
    public void queryLongTailWordData(DashboardLongTailWordRequest request, StreamObserver<DashboardLongTailWordResponse> responseObserver) {
        log.info("dashboard query long tail word data, process request data: {}", request);
        long begin = System.currentTimeMillis();
        DashboardBaseReqVo reqVo = processParam(request);
        List<DashboardLongTailWordDataDto> dataList = dashboardLongTailWordService.queryLongTailWordData(reqVo);
        DashboardLongTailWordResponse.Builder builder = DashboardLongTailWordResponse.newBuilder();
        // 构建响应参数
        List<LongTailWordVo> voList = buildVoList(dataList);
        builder.addAllData(voList);
        builder.setCode(Result.SUCCESS);
        log.info("dashboard query long tail word data,, puid: {}, 耗时: {}", request.getPuid(), System.currentTimeMillis()-begin);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void exportLongTailWordData(DashboardLongTailWordRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("dashboard export long tail word data, request data: {}", request);
        long begin = System.currentTimeMillis();
        UrlResponse.Builder builder = UrlResponse.newBuilder();
        DashboardBaseReqVo reqVo = processParam(request);
        List<String> urlList = dashboardLongTailWordService.exportLongTailWordData(reqVo);
        if (CollectionUtils.isNotEmpty(urlList)) {
            builder.addAllUrls(urlList);
            builder.setCode(Int32Value.of(Result.SUCCESS));
        } else {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("process.msg.sync.fail");
        }
        log.info("dashboard export long tail word data, puid: {}, 耗时: {}", request.getPuid(), System.currentTimeMillis()-begin);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 处理参数
     * @param request
     * @return
     */
    private DashboardBaseReqVo processParam(DashboardLongTailWordRequest request) {
        //将grpc请求对象转换未查询实体类
        DashboardBaseReqVo reqVo = new DashboardBaseReqVo();
        BeanUtils.copyProperties(request, reqVo);
        reqVo.setPuid(reqVo.getPuid());
        reqVo.setMarketplaceIdList(reqVo.getMarketplaceIdList());
        reqVo.setShopIdList(reqVo.getShopIdList());
        Optional.of(request.getPortfolioIdsList()).filter(CollectionUtils::isNotEmpty).ifPresent(reqVo::setPortfolioIds);
        Optional.of(request.getCampaignIdsList()).filter(CollectionUtils::isNotEmpty).ifPresent(reqVo::setCampaignIds);
        Optional.of(request.getStartDate()).map(DateUtil::toFormatDate).ifPresent(reqVo::setStartDate);
        Optional.of(request.getEndDate()).map(DateUtil::toFormatDate).ifPresent(reqVo::setEndDate);
        if(CollectionUtils.isEmpty(reqVo.getShopIdList())){
            throw new SponsoredBizException("没有可见权限的店铺信息");
        }
        return reqVo;
    }

    private static List<LongTailWordVo> buildVoList(List<DashboardLongTailWordDataDto> dataList) {
        List<LongTailWordVo> voList = new ArrayList<>();
        for (DashboardLongTailWordDataDto dataDto : dataList) {
            LongTailWordVo.Builder wordBuilder = LongTailWordVo.newBuilder();
            buildVo(dataDto, wordBuilder);
            List<LongTailWordVo> childrenList = new ArrayList<>();
            for (DashboardLongTailWordDataDto children: dataDto.getChildren()) {
                LongTailWordVo.Builder childrenBuilder = LongTailWordVo.newBuilder();
                buildVo(children, childrenBuilder);
                childrenList.add(childrenBuilder.build());
            }
            wordBuilder.addAllChild(childrenList);
            voList.add(wordBuilder.build());
        }
        return voList;
    }

    private static void buildVo(DashboardLongTailWordDataDto dataDto, LongTailWordVo.Builder builder){
        BeanUtils.copyProperties(dataDto, builder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(dataDto));
        builder.setCost(CalculateUtil.formatDecimal(dataDto.getCost()));
        builder.setTotalSales(CalculateUtil.formatDecimal(dataDto.getTotalSales()));
        builder.setImpressions(String.valueOf(dataDto.getImpressions()));
        builder.setClicks(String.valueOf(dataDto.getClicks()));
        builder.setOrderNum(String.valueOf(dataDto.getOrderNum()));
        builder.setSaleNum(String.valueOf(dataDto.getSaleNum()));
        builder.setAcos(CalculateUtil.formatPercentNoPercent(dataDto.getAcos()));
        builder.setRoas(CalculateUtil.formatDecimal(dataDto.getRoas()));
        builder.setClickRate(CalculateUtil.formatPercentNoPercent(dataDto.getClickRate()));
        builder.setConversionRate(CalculateUtil.formatPercentNoPercent(dataDto.getConversionRate()));
        builder.setCpc(CalculateUtil.formatDecimal(dataDto.getCpc()));
        builder.setCpa(CalculateUtil.formatDecimal(dataDto.getCpa()));
    }
}
