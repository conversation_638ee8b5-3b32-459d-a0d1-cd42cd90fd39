package com.meiyunji.sponsored.api.sp;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.adCommon.*;
import com.meiyunji.sponsored.rpc.sp.targeting.*;
import com.meiyunji.sponsored.rpc.vo.*;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetObjectTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskConstant;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskMatchTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskTypeEnum;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetTaskDto;
import com.meiyunji.sponsored.service.cpc.po.TargetTypeEnum;
import com.meiyunji.sponsored.service.cpc.qo.TargetSuggestBidBatchQo;
import com.meiyunji.sponsored.service.cpc.service2.IAdTargetTaskService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbTargetService;
import com.meiyunji.sponsored.service.cpc.service2.sd.ICpcSdTargetingService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcTargetingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.enums.StateEnum;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2021/10/27 19:07
 * @describe:
 */
@GRpcService
@Slf4j
public class TargetingSpRpcService extends RPCSpTargetingServiceGrpc.RPCSpTargetingServiceImplBase {

    @Autowired
    private ICpcTargetingService cpcTargetingService;
    @Autowired
    private IAdTargetTaskService adTargetTaskService;
    @Autowired
    private ICpcSbTargetService cpcSbTargetService;
    @Autowired
    private ICpcSdTargetingService cpcSdTargetingService;

    @Override
    public void showAdPerformanceVo(CommonShowAdPerformanceRequest request, StreamObserver<CommonShowAdPerformanceResponse> responseObserver) {
        CommonShowAdPerformanceResponse.Builder builder = CommonShowAdPerformanceResponse.newBuilder();
        //做参数校验
        if (!request.hasShopId()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");

        } else {
            AdPerformanceParam param = new AdPerformanceParam();
            param.setShopId(request.getShopId());
            param.setPuid(request.getPuid());
            param.setCampaignId(request.getCampaignId());
            param.setGroupId(request.getGroupId());
            param.setKeywordId(request.getKeywordId());
            param.setTargetId(request.getTargetId());
            param.setCpcProductId(request.getCpcProductId());
            param.setQuery(request.getQuery());
            param.setPlacement(request.getPlacement());
            param.setStartDate(request.getStartDate());
            param.setEndDate(request.getEndDate());

            if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
                param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
                param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            } else {
                param.setStartDate(param.getStartDate().replace("-", ""));
                param.setEndDate(param.getEndDate().replace("-", ""));
            }
            //处理业务返回结果
            Result<AdPerformanceVo> res = cpcTargetingService.showTargetPerformance(request.getPuid(), param);
            builder.setCode(res.getCode());
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                //处理data
                AdPerformanceVo data = res.getData();
                if (data != null) {
                    AdPerformanceRpcVo.Builder voBuilder = AdPerformanceRpcVo.newBuilder();
                    if (data.getShopId() != null) {
                        voBuilder.setShopId(data.getShopId());
                    }
                    if (data.getCampaignId() != null) {
                        voBuilder.setCampaignId(data.getCampaignId());
                    }
                    if (data.getGroupId() != null) {
                        voBuilder.setGroupId(data.getGroupId());
                    }
                    if (data.getKeywordId() != null) {
                        voBuilder.setKeywordId(data.getKeywordId());
                    }
                    if (data.getTargetId() != null) {
                        voBuilder.setTargetId(data.getTargetId());
                    }
                    if (data.getAdId() != null) {
                        voBuilder.setAdId(data.getAdId());
                    }
                    if (data.getQuery() != null) {
                        voBuilder.setQuery(data.getQuery());
                    }
                    if (data.getPlacement() != null) {
                        voBuilder.setPlacement(data.getPlacement());
                    }
                    Map<String, CpcCommPageRpcVo> rpcVoMap = Maps.newHashMap();

                    if (MapUtils.isNotEmpty(data.getMap())) {
                        for (Map.Entry<String, CpcCommPageVo> entry : data.getMap().entrySet()) {
                            CpcCommPageVo vo = entry.getValue();
                            //vo转message
                            CpcCommPageRpcVo.Builder cpcRpcVo = CpcCommPageRpcVo.newBuilder();
                            cpcRpcVo.setImpressions(Optional.ofNullable(vo.getImpressions()).orElse(0));
                            cpcRpcVo.setClicks(Optional.ofNullable(vo.getClicks()).orElse(0));
                            cpcRpcVo.setCtr(StringUtils.isNotBlank(vo.getCtr()) ? vo.getCtr() : "0");
                            cpcRpcVo.setCvr(StringUtils.isNotBlank(vo.getCvr()) ? vo.getCvr() : "0");
                            cpcRpcVo.setRoas(StringUtils.isNotBlank(vo.getRoas()) ? vo.getRoas() : "0");
                            cpcRpcVo.setAcos(StringUtils.isNotBlank(vo.getAcos()) ? vo.getAcos() : "0");
                            cpcRpcVo.setAcots(StringUtils.isNotBlank(vo.getAcots()) ? vo.getAcots() : "0");
                            cpcRpcVo.setAdOrderNum(Optional.ofNullable(vo.getAdOrderNum()).orElse(0));
                            cpcRpcVo.setAdCost(StringUtils.isNotBlank(vo.getAdCost()) ? vo.getAdCost() : "0");
                            cpcRpcVo.setAdCostPerClick(StringUtils.isNotBlank(vo.getAdCostPerClick()) ? vo.getAdCostPerClick() : "0");
                            cpcRpcVo.setAdSale(StringUtils.isNotBlank(vo.getAdSale()) ? vo.getAdSale() : "0");

                            rpcVoMap.put(entry.getKey(), cpcRpcVo.build());
                        }

                        voBuilder.putAllMap(rpcVoMap);
                    }
                    builder.setData(voBuilder.build());
                }
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 创建自动投放
     */
    @Override
    public void createAutoTargeting(CreateSpAutoRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-targeting-创建自动投放 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasShopId() || !request.hasPuid()
                || StringUtils.isBlank(request.getGroupId())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            CreateAutoTargetingVo vo = new CreateAutoTargetingVo();
            vo.setPuid(request.getPuid().getValue());
            vo.setShopId(request.getShopId().getValue());
            vo.setUid(request.getUid().getValue());
            vo.setGroupId(request.getGroupId());
            vo.setQueryHighRelMatchesState(request.hasQueryHighRelMatchesState() ? request.getQueryHighRelMatchesState().getValue() : null);
            vo.setQueryHighRelMatchesBid(request.getQueryHighRelMatchesBid());
            vo.setQueryBroadRelMatchesState(request.hasQueryBroadRelMatchesState() ? request.getQueryBroadRelMatchesState().getValue() : null);
            vo.setQueryBroadRelMatchesBid(request.getQueryBroadRelMatchesBid());
            vo.setAsinSubstituteRelatedState(request.hasAsinSubstituteRelatedState() ? request.getAsinSubstituteRelatedState().getValue() : null);
            vo.setAsinSubstituteRelatedBid(request.getAsinSubstituteRelatedBid());
            vo.setAsinAccessoryRelatedState(request.hasAsinAccessoryRelatedState() ? request.getAsinAccessoryRelatedState().getValue() : null);
            vo.setAsinAccessoryRelatedBid(request.getAsinAccessoryRelatedBid());
            //处理list
            List<NeKeywordsRpcVo> neKeywordList = request.getNeKeywordList();
            List<NeKeywordsVo> nkVos = neKeywordList.stream().filter(Objects::nonNull).map(item -> {
                NeKeywordsVo keywordsVo = new NeKeywordsVo();
                keywordsVo.setKeywordText(item.getKeywordText());
                keywordsVo.setMatchType(item.getMatchType());
                return keywordsVo;
            }).collect(Collectors.toList());

            List<NeTargetingRpcVo> neTargetingList = request.getNeTargetingList();
            List<NeTargetingVo> ntVos = neTargetingList.stream().filter(Objects::nonNull).map(item -> {
                NeTargetingVo neTargetingVo = new NeTargetingVo();
                neTargetingVo.setAsin(item.getAsin());
                neTargetingVo.setTitle(item.getTitle());
                neTargetingVo.setImgUrl(item.getImgUrl());
                // 自动投放类型广告活动只允许添加否定产品，品牌手动允许
                neTargetingVo.setType(TargetTypeEnum.asin.name());
                return neTargetingVo;
            }).collect(Collectors.toList());


            vo.setNeKeywords(nkVos);
            vo.setNeTargetings(ntVos);
            //处理业务,返回结果
            Result res = cpcTargetingService.createAutoTargeting(vo, request.getLoginIp());

            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 创建手动投放
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void createManualTargeting(CreateSpManualRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-targeting-创建手动投放 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasShopId() || !request.hasPuid()
                || StringUtils.isBlank(request.getGroupId())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            CreateManualTargetingVo vo = new CreateManualTargetingVo();
            vo.setGroupId(request.getGroupId());
            vo.setPuid(request.getPuid().getValue());
            vo.setShopId(request.getShopId().getValue());
            vo.setUid(request.getUid().getValue());
            //处理list
            List<KeywordsRpcVo> keywordList = request.getKeywordList();
            List<KeywordsVo> kVos = keywordList.stream().filter(Objects::nonNull).map(item -> {
                KeywordsVo keyVo = new KeywordsVo();
                keyVo.setMatchType(item.getMatchType());
                keyVo.setRangeEnd(item.getRangeEnd());
                keyVo.setRangeStart(item.getRangeStart());
                keyVo.setSuggested(item.getSuggested());
                keyVo.setBid(item.getBid());
                keyVo.setKeywordText(item.getKeywordText());
                return keyVo;
            }).collect(Collectors.toList());


            List<TargetingRpcVo> targetingList = request.getTargetingList();
            List<TargetingVo> tVos = targetingList.stream().filter(Objects::nonNull).map(item -> {
                TargetingVo targetingVo = new TargetingVo();
                targetingVo.setType(item.getType());
                targetingVo.setAsin(item.getAsin());
                targetingVo.setTitle(item.getTitle());
                targetingVo.setImgUrl(item.getImgUrl());
                targetingVo.setCategoryId(item.getCategoryId());
                targetingVo.setCategory(item.getCategory());
                targetingVo.setBid(item.getBid());
                targetingVo.setBrand(item.getBrand());
                targetingVo.setMinPrice(item.getMinPrice());
                targetingVo.setMaxPrice(item.getMaxPrice());
                if (item.hasMinReviewRating()) {
                    targetingVo.setMinReviewRating(item.getMinReviewRating().getValue());
                }
                if (item.hasMaxReviewRating()) {
                    targetingVo.setMaxReviewRating(item.getMaxReviewRating().getValue());
                }
                targetingVo.setPrimeShippingEligible(item.hasPrimeShippingEligible() ? item.getPrimeShippingEligible().getValue() : null);
                targetingVo.setSuggested(item.getSuggested());
                targetingVo.setRangeStart(item.getRangeStart());
                targetingVo.setRangeEnd(item.getRangeEnd());
                targetingVo.setExpressionType(item.getExpressionType());
                return targetingVo;
            }).collect(Collectors.toList());

            List<NeKeywordsRpcVo> neKeywordList = request.getNeKeywordList();
            List<NeKeywordsVo> nkVos = neKeywordList.stream().filter(Objects::nonNull).map(item -> {
                NeKeywordsVo neKeywordsVo = new NeKeywordsVo();
                neKeywordsVo.setKeywordText(item.getKeywordText());
                neKeywordsVo.setMatchType(item.getMatchType());
                return neKeywordsVo;
            }).collect(Collectors.toList());

            List<NeTargetingRpcVo> neTargetingList = request.getNeTargetingList();
            List<NeTargetingVo> ntVos = neTargetingList.stream().filter(Objects::nonNull).map(item -> {
                NeTargetingVo neTargetingVo = new NeTargetingVo();
                neTargetingVo.setAsin(item.getAsin());
                neTargetingVo.setTitle(item.getTitle());
                neTargetingVo.setImgUrl(item.getImgUrl());
                neTargetingVo.setBrandId(item.getBrandId());
                neTargetingVo.setBrandName(item.getBrandName());
                neTargetingVo.setType(item.getType());
                return neTargetingVo;
            }).collect(Collectors.toList());

            vo.setNeKeywords(nkVos);
            vo.setNeTargetings(ntVos);
            vo.setKeywords(kVos);
            vo.setTargetings(tVos);
            //处理业务返回结果
            Result res = cpcTargetingService.createManualTargeting(vo, request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    /**
     * 创建产品投放
     */
    @Override
    public void createTargeting(CreateSpTargetingRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-targeting-创建产品投放 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (StringUtils.isBlank(request.getGroupId())
                || !request.hasPuid() || !request.hasShopId()
                || CollectionUtils.isEmpty(request.getTargetingList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            AddTargetingVo vo = new AddTargetingVo();
            vo.setPuid(request.getPuid().getValue());
            vo.setShopId(request.getShopId().getValue());
            vo.setUid(request.getUid().getValue());
            vo.setGroupId(request.getGroupId());
            //处理list
            List<TargetingRpcVo> targetingList = request.getTargetingList();
            List<TargetingVo> tVos = targetingList.stream().filter(Objects::nonNull).map(item -> {
                TargetingVo targetingVo = new TargetingVo();
                targetingVo.setType(item.getType());
                targetingVo.setAsin(item.getAsin());
                targetingVo.setTitle(item.getTitle());
                targetingVo.setImgUrl(item.getImgUrl());
                targetingVo.setCategoryId(item.getCategoryId());
                targetingVo.setCategory(item.getCategory());
                targetingVo.setBid(item.getBid());
                targetingVo.setBrand(item.getBrand());
                targetingVo.setMinPrice(item.getMinPrice());
                targetingVo.setMaxPrice(item.getMaxPrice());
                if (item.hasMinReviewRating()) {
                    targetingVo.setMinReviewRating(item.getMinReviewRating().getValue());
                }
                if (item.hasMaxReviewRating()) {
                    targetingVo.setMaxReviewRating(item.getMaxReviewRating().getValue());
                }
                targetingVo.setPrimeShippingEligible(item.hasPrimeShippingEligible() ? item.getPrimeShippingEligible().getValue() : null);
                targetingVo.setSuggested(item.getSuggested());
                targetingVo.setRangeStart(item.getRangeStart());
                targetingVo.setRangeEnd(item.getRangeEnd());
                targetingVo.setExpressionType(item.getExpressionType());
                return targetingVo;
            }).collect(Collectors.toList());
            vo.setTargetings(tVos);
            //处理业务返回结果
            Result res = cpcTargetingService.createTargeting(vo, request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void asyncCreateTargeting(AsyncAddTargetingRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-async-targeting-创建产品投放 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        String error = verifyAsyncParam(request);
        if (StringUtils.isNotBlank(error)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(error);
        } else {
            AdTargetTaskDto adTargetTaskDto = new AdTargetTaskDto();
            adTargetTaskDto.setPuid(request.getPuid().getValue());
            adTargetTaskDto.setShopId(request.getShopId().getValue());
            adTargetTaskDto.setUid(request.getUid().getValue());
            adTargetTaskDto.setType(AdTargetTaskTypeEnum.SP_TARGETS.getCode());
            adTargetTaskDto.setLoginIp(request.getLoginIp());
            adTargetTaskDto.setTargetingType(request.getTargetingType());
            adTargetTaskDto.setTargetPageType(request.getTargetingPageType().getValue());
            adTargetTaskDto.setSourceAdCampaignId(request.getSourceAdCampaignId());
            adTargetTaskDto.setSourceShopId(request.getSourceShopId().getValue());
            List<AdTargetTaskDto.AdTargetTaskDetailDto> detailList = request.getTargetingList().stream().filter(Objects::nonNull).map(item -> {
                AdTargetTaskDto.AdTargetTaskDetailDto detail = new AdTargetTaskDto.AdTargetTaskDetailDto();
                if (item.hasSourceShopId()) {
                    detail.setSourceShopId(item.getSourceShopId().getValue());
                }  else {
                    detail.setSourceShopId(request.getSourceShopId().getValue());
                }
                detail.setAdCampaignId(item.getAdCampaignId());
                detail.setAdGroupId(item.getAdGroupId());
                detail.setMatchType(item.getExpressionType());
                detail.setRangeEnd(StringUtils.isBlank(item.getRangeEnd()) ? BigDecimal.ZERO : new BigDecimal(item.getRangeEnd()));
                detail.setRangeStart(StringUtils.isBlank(item.getRangeStart()) ? BigDecimal.ZERO : new BigDecimal(item.getRangeStart()));
                detail.setSuggested(StringUtils.isBlank(item.getSuggested()) ? BigDecimal.ZERO : new BigDecimal(item.getSuggested()));
                detail.setBid(new BigDecimal(item.getBid()));
                if (StringUtils.isNotBlank(item.getTargetId())) {
                    detail.setTargetId(item.getTargetId());
                } else {
                    detail.setTargetObject(item.getAsin());
                    detail.setTargetObjectDesc(item.getTitle());
                    detail.setImgUrl(item.getImgUrl());
                }
                detail.setTargetObjectType(AdTargetObjectTypeEnum.getCodeByTargetType(item.getType()));
                return detail;
            }).collect(Collectors.toList());
            adTargetTaskDto.setTaskDetails(detailList);
            long taskId = adTargetTaskService.recordTargetTask(adTargetTaskDto);
            adTargetTaskService.executeTask(taskId);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private String verifyAsyncParam(AsyncAddTargetingRequest request) {
        if (!request.hasShopId() || !request.hasPuid() || !request.hasTargetingPageType()
                || CollectionUtils.isEmpty(request.getTargetingList()) || request.getTargetingList().size() > AdTargetTaskConstant.MAX_TARGET_SIZE) {
            return "请求参数错误";
        }
        List<AsyncAddTargetingRpcVo> list = request.getTargetingList();
        // 如果第一个包含targetId,那么列表中所有的对象都需要包含targetId
        int targetIdNum = 0;
        for (AsyncAddTargetingRpcVo each : list) {
            boolean haveTargetId = StringUtils.isNotBlank(each.getTargetId());
            if (haveTargetId) {
                targetIdNum++;
            }
            if (!AdTargetTaskMatchTypeEnum.SP_TARGET_SUPPORT_TYPES.contains(each.getExpressionType())) {
                return "请求参数错误,存在不支持的筛选条件";
            }
            if (!AdTargetObjectTypeEnum.TARGET_SUPPORT_TYPES.contains(each.getType())) {
                return "请求参数错误,存在不支持的投放类型";
            }
            if (TargetTypeEnum.category.name().equals(each.getType()) && !haveTargetId) {
                return "请求参数错误";
            }
            if (TargetTypeEnum.asin.name().equals(each.getType()) && !haveTargetId && StringUtils.isBlank(each.getAsin())) {
                return "请求参数错误";
            }
        }
        if (targetIdNum > 0 && targetIdNum < list.size() || (!request.hasSourceShopId() && !request.getTargetingList().get(0).hasSourceShopId()) || StringUtils.isBlank(request.getTargetingType())) {
            return "请求参数错误";
        }
        return StringUtils.EMPTY;
    }

    /**
     * 更新投放状态
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateState(UpdateSpTargetStateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-targeting-更新投放状态 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        String stateValue = StateEnum.getStateValue(request.getState());
        if (!request.hasPuid() || !request.hasId() || StringUtils.isBlank(request.getState()) || StringUtils.isBlank(stateValue)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result res = cpcTargetingService.updateState(request.getPuid().getValue(), request.getUid().getValue(), request.getId().getValue(), request.getState(), request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    /**
     * 更新竞价
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateBid(UpdateSpTargetBidRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-targeting-更新投放竞价 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasPuid() || !request.hasId() || !request.hasBid() || request.getBid().getValue() <= 0) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result res = cpcTargetingService.updateBid(request.getPuid().getValue(), request.getUid().getValue(), request.getId().getValue(), request.getBid().getValue(), request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    /**
     * 创建否定投放
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void createNeTargeting(CreateSpNeTargetingRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-targeting-创建否定投放 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (StringUtils.isBlank(request.getGroupId())
                || !request.hasShopId()
                || CollectionUtils.isEmpty(request.getNeTargetingList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            AddNeTargetingVo addNeTargetingVo = new AddNeTargetingVo();
            addNeTargetingVo.setPuid(request.getPuid().getValue());
            addNeTargetingVo.setUid(request.getUid().getValue());
            addNeTargetingVo.setShopId(request.getShopId().getValue());
            addNeTargetingVo.setGroupId(request.getGroupId());
            //处理list
            List<NeTargetingRpcVo> neTargetingList = request.getNeTargetingList();
            List<NeTargetingVo> ntVos = neTargetingList.stream().filter(Objects::nonNull).map(item -> {
                NeTargetingVo neTargetingVo = new NeTargetingVo();
                neTargetingVo.setAsin(item.getAsin());
                neTargetingVo.setTitle(item.getTitle());
                neTargetingVo.setImgUrl(item.getImgUrl());
                neTargetingVo.setBrandId(item.getBrandId());
                neTargetingVo.setBrandName(item.getBrandName());
                neTargetingVo.setType(item.getType());
                return neTargetingVo;
            }).collect(Collectors.toList());
            addNeTargetingVo.setNeTargetings(ntVos);
            //处理业务返回结果
            Result res = cpcTargetingService.createNeTargeting(addNeTargetingVo, request.getLoginIp());

            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void asyncCreateNeTargeting(AsyncAddTargetingRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-async-targeting-创建否定投放 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        String error = verifyAsyncNeParam(request);
        if (StringUtils.isNotBlank(error)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(error);
        } else {
            AdTargetTaskDto adTargetTaskDto = new AdTargetTaskDto();
            adTargetTaskDto.setPuid(request.getPuid().getValue());
            adTargetTaskDto.setShopId(request.getShopId().getValue());
            adTargetTaskDto.setUid(request.getUid().getValue());
            if (request.getType() != 0 && Objects.nonNull(AdTargetTaskTypeEnum.enumMap.get(request.getType()))) {
                adTargetTaskDto.setType(AdTargetTaskTypeEnum.enumMap.get(request.getType()).getCode());
            } else {
                adTargetTaskDto.setType(AdTargetTaskTypeEnum.SP_NEGATIVE_TARGETS.getCode());
            }
            adTargetTaskDto.setLoginIp(request.getLoginIp());
            adTargetTaskDto.setTargetingType(request.getTargetingType());
            adTargetTaskDto.setTargetPageType(request.getTargetingPageType().getValue());
            adTargetTaskDto.setSourceAdCampaignId(request.getSourceAdCampaignId());
            adTargetTaskDto.setSourceShopId(request.getSourceShopId().getValue());
            List<AdTargetTaskDto.AdTargetTaskDetailDto> detailList = request.getTargetingList().stream().filter(Objects::nonNull).map(item -> {
                AdTargetTaskDto.AdTargetTaskDetailDto detail = new AdTargetTaskDto.AdTargetTaskDetailDto();
                if (item.hasSourceShopId()) {
                    detail.setSourceShopId(item.getSourceShopId().getValue());
                }  else {
                    detail.setSourceShopId(request.getSourceShopId().getValue());
                }
                detail.setAdCampaignId(item.getAdCampaignId());
                detail.setAdGroupId(item.getAdGroupId());
                detail.setMatchType(item.getExpressionType());
                if (StringUtils.isNotBlank(item.getTargetId())) {
                    detail.setTargetId(item.getTargetId());
                } else {
                    detail.setTargetObject(item.getAsin());
                    detail.setTargetObjectDesc(item.getTitle());
                    detail.setImgUrl(item.getImgUrl());
                }
                detail.setTargetObjectType(AdTargetObjectTypeEnum.getCodeByTargetType(item.getType()));
                return detail;
            }).collect(Collectors.toList());
            adTargetTaskDto.setTaskDetails(detailList);
            long taskId = adTargetTaskService.recordTargetTask(adTargetTaskDto);
            adTargetTaskService.executeTask(taskId);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private String verifyAsyncNeParam(AsyncAddTargetingRequest request) {
        if (!request.hasShopId() || !request.hasPuid() || !request.hasTargetingPageType()
                || CollectionUtils.isEmpty(request.getTargetingList()) || request.getTargetingList().size() > AdTargetTaskConstant.MAX_TARGET_SIZE) {
            return "请求参数错误";
        }
        List<AsyncAddTargetingRpcVo> list = request.getTargetingList();
        // 如果第一个包含targetId,那么列表中所有的对象都需要包含targetId
        int targetIdNum = 0;
        for (AsyncAddTargetingRpcVo each : list) {
            boolean haveTargetId = StringUtils.isNotBlank(each.getTargetId());
            if (haveTargetId) {
                targetIdNum++;
            }
            if (!AdTargetTaskMatchTypeEnum.SP_NE_TARGET_SUPPORT_TYPES.contains(each.getExpressionType())) {
                return "请求参数错误,存在不支持的筛选条件";
            }
            if (!AdTargetObjectTypeEnum.SP_NE_TARGET_SUPPORT_TYPES.contains(each.getType())) {
                return "请求参数错误,存在不支持的投放类型";
            }
            if (TargetTypeEnum.brand.name().equals(each.getType()) && !haveTargetId) {
                return "请求参数错误";
            }
            if (TargetTypeEnum.asin.name().equals(each.getType()) && !haveTargetId && StringUtils.isBlank(each.getAsin())) {
                return "请求参数错误";
            }
        }
        if (targetIdNum > 0 && targetIdNum < list.size() || (!request.hasSourceShopId() && !request.getTargetingList().get(0).hasSourceShopId()) || StringUtils.isBlank(request.getTargetingType())) {
            return "请求参数错误";
        }
        return StringUtils.EMPTY;
    }

    /**
     * 更新否定投放状态
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateNeTargetingState(UpdateSpNeTargetingStateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-targeting-更新否定投放状态 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        String stateValue = StateEnum.getStateValue(request.getState());
        if (!request.hasPuid() || !request.hasId() || StringUtils.isBlank(stateValue)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result res = cpcTargetingService.updateNeTargetingState(request.getPuid().getValue(), request.getUid().getValue(), request.getId().getValue(), request.getState());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    /**
     * 投放-归档
     */
    @Override
    public void archive(ArchiveSpTargetRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-targeting-投放-归档 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasPuid() || !request.hasUid() || !request.hasId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result res = cpcTargetingService.archive(request.getPuid().getValue(), request.getUid().getValue(), request.getId().getValue(), request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 否定投放-归档
     */
    @Override
    public void archiveNeTargeting(ArchiveSpNeTargetingRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-targeting-否定投放-归档 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasPuid() || !request.hasUid() || !request.hasId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result res = cpcTargetingService.archiveNeTargeting(request.getPuid().getValue(), request.getUid().getValue(), request.getId().getValue(), request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    /**
     * Sp否定投放-批量归档（组）
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void batchArchiveNeTargeting(BatchArchiveSpNeTargetingRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-targeting-否定投放-批量归档(组) request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasShopId() || !request.hasIdList() || !request.hasPuid() || !request.hasUid() || !request.hasLoginIp()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
        } else {
            List<Long> idList = Arrays.stream(request.getIdList().split(",")).map(Long::parseLong).collect(Collectors.toList());
            Result result = cpcTargetingService.batchArchiveNeTargeting(request.getPuid().getValue(), request.getShopId().getValue(),
                    request.getUid().getValue(), request.getLoginIp(), idList);

            builder.setCode(Int32Value.newBuilder().setValue(result.getCode()).build());
            if (result.getData() != null) {
                builder.setData(JSONUtil.objectToJson(result.getData()));
            }
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 获取建议分类
     */
    @Override
    public void suggestCategory(SpTargetSuggestCategoryRequest request, StreamObserver<SpTargetSuggestCategoryResponse> responseObserver) {
        log.info("sp-targeting-获取建议分类 request {}", request);
        SpTargetSuggestCategoryResponse.Builder builder = SpTargetSuggestCategoryResponse.newBuilder();

        if (!request.hasPuid() || !request.hasShopId() || StringUtils.isBlank(request.getGroupId())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理返回结果
            Result<List<SuggestCategoryVo>> res = cpcTargetingService.suggestCategory(request.getPuid().getValue(), request.getShopId().getValue(), request.getGroupId());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
            //处理list
            if (res.success()) {
                List<SuggestCategoryVo> data = res.getData();
                List<SuggestCategoryRpcVo> rpcVos = data.stream().filter(Objects::nonNull).map(item -> {
                    SuggestCategoryRpcVo.Builder voBuilder = SuggestCategoryRpcVo.newBuilder();
                    if (item.getId() != null) {
                        voBuilder.setId(item.getId());
                    }
                    if (item.getName() != null) {
                        voBuilder.setName(item.getName());
                    }
                    if (item.getPath() != null) {
                        voBuilder.setPath(item.getPath());
                    }
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    /**
     * 获取建议分类
     */
    @Override
    public void suggestCategoryByAsin(SpTargetSuggestCategoryByAsinRequest request, StreamObserver<SpTargetSuggestCategoryResponse> responseObserver) {
        log.info("sp-targeting-asin-获取建议分类 request {}", request);
        SpTargetSuggestCategoryResponse.Builder builder = SpTargetSuggestCategoryResponse.newBuilder();

        if (!request.hasPuid() || !request.hasShopId() || CollectionUtils.isEmpty(request.getAsinList()) || request.getAsinList().size() > 1000) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理返回结果
            Result<List<SuggestCategoryVo>> res = cpcTargetingService.suggestCategoryByAsin(request.getPuid().getValue(), request.getShopId().getValue(), request.getAsinList());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
            //处理list
            if (res.success()) {
                List<SuggestCategoryVo> data = res.getData();
                List<SuggestCategoryRpcVo> rpcVos = data.stream().filter(Objects::nonNull).map(item -> {
                    SuggestCategoryRpcVo.Builder voBuilder = SuggestCategoryRpcVo.newBuilder();
                    if (item.getId() != null) {
                        voBuilder.setId(item.getId());
                    }
                    if (item.getName() != null) {
                        voBuilder.setName(item.getName());
                    }
                    if (item.getPath() != null) {
                        voBuilder.setPath(item.getPath());
                    }
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    /**
     * 根据分类id取建议分类
     */
    @Override
    public void suggestCategoryBrand(SpTargetSuggestCategoryBrandRequest request, StreamObserver<SpTargetSuggestCategoryBrandResponse> responseObserver) {
        log.info("sp-targeting-根据分类id取建议分类 request {}", request);
        SpTargetSuggestCategoryBrandResponse.Builder builder = SpTargetSuggestCategoryBrandResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId()
                || StringUtils.isBlank(request.getCategoryId())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result<List<SuggestCategoryBrandVo>> res = cpcTargetingService.suggestCategoryBrand(request.getPuid().getValue(), request.getShopId().getValue(), request.getCategoryId());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                List<SuggestCategoryBrandVo> data = res.getData();
                List<SuggestCategoryBrandRpcVo> rpcVos = data.stream().filter(Objects::nonNull).map(item -> {
                    SuggestCategoryBrandRpcVo.Builder voBuilder = SuggestCategoryBrandRpcVo.newBuilder();
                    voBuilder.setId(item.getId());
                    voBuilder.setName(item.getName());
                    return voBuilder.build();
                }).collect(Collectors.toList());

                builder.addAllData(rpcVos);
            }

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 根据投放文本获取建议竞价
     */
    @Override
    public void getSuggestedBidByText(SpTargetSuggestedBidByTextRequest request, StreamObserver<SpTargetSuggestedBidByTextResponse> responseObserver) {
        log.info("sp-targeting-根据投放文本获取建议竞价 request {}", request);
        SpTargetSuggestedBidByTextResponse.Builder builder = SpTargetSuggestedBidByTextResponse.newBuilder();

        if (StringUtils.isBlank(request.getGroupId())
                || !request.hasShopId() || !request.hasPuid()
                || CollectionUtils.isEmpty(request.getTargetingList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            AddTargetingVo vo = new AddTargetingVo();
            vo.setPuid(request.getPuid().getValue());
            vo.setShopId(request.getShopId().getValue());
            vo.setUid(request.getUid().getValue());
            vo.setGroupId(request.getGroupId());
            //处理list
            List<TargetingRpcVo> targetingList = request.getTargetingList();
            List<TargetingVo> tVos = targetingList.stream().filter(Objects::nonNull).map(item -> {
                TargetingVo targetingVo = new TargetingVo();
                targetingVo.setType(item.getType());
                targetingVo.setAsin(item.getAsin());
                targetingVo.setTitle(item.getTitle());
                targetingVo.setImgUrl(item.getImgUrl());
                targetingVo.setCategoryId(item.getCategoryId());
                targetingVo.setCategory(item.getCategory());
                targetingVo.setBid(item.getBid());
                targetingVo.setBrand(item.getBrand());
                targetingVo.setMinPrice(item.getMinPrice());
                targetingVo.setMaxPrice(item.getMaxPrice());
                targetingVo.setMinReviewRating(item.getMinReviewRating().getValue());
                targetingVo.setMaxReviewRating(item.getMaxReviewRating().getValue());
                targetingVo.setPrimeShippingEligible(item.hasPrimeShippingEligible() ? item.getPrimeShippingEligible().getValue() : null);
                targetingVo.setSuggested(item.getSuggested());
                targetingVo.setRangeStart(item.getRangeStart());
                targetingVo.setRangeEnd(item.getRangeEnd());
                targetingVo.setExpressionType(item.getExpressionType());
                return targetingVo;
            }).collect(Collectors.toList());
            vo.setTargetings(tVos);

            //处理业务返回结果
            Result<List<SuggestedTargetVo>> res = cpcTargetingService.getSuggestedBidByText(request.getPuid().getValue(), vo);
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                //处理list
                List<SuggestedTargetVo> data = res.getData();
                List<SuggestedTargetRpcVo> rpcVos = data.stream().filter(Objects::nonNull).map(item -> {
                    SuggestedTargetRpcVo.Builder voBuilder = SuggestedTargetRpcVo.newBuilder();
                    if (item.getTargetId() != null) {
                        voBuilder.setTargetId(item.getTargetId());
                    }
                    if (item.getSuggested() != null) {
                        voBuilder.setSuggested(item.getSuggested());
                    }
                    if (item.getRangeEnd() != null) {
                        voBuilder.setRangeEnd(item.getRangeEnd());
                    }
                    if (item.getRangeStart() != null) {
                        voBuilder.setRangeStart(item.getRangeStart());
                    }
                    if (CollectionUtils.isNotEmpty(item.getExpression())) {
                        List<SuggestedTargetRpcVo.Expression> expression = item.getExpression().stream().map(innerItem -> {
                            SuggestedTargetRpcVo.Expression.Builder innerBuilder = SuggestedTargetRpcVo.Expression.newBuilder();
                            if (innerItem.getType() != null) {
                                innerBuilder.setType(innerItem.getType());
                            }
                            if (innerItem.getValue() != null) {
                                innerBuilder.setValue(innerItem.getValue());
                            }
                            return innerBuilder.build();
                        }).collect(Collectors.toList());
                        voBuilder.addAllExtension(expression);
                    }

                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 获取投放的建议竞价
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getSuggestedBid(SpTargetSuggestedBidRequest request, StreamObserver<SpTargetSuggestedBidResponse> responseObserver) {
        log.info("sp-targeting-获取投放的建议竞价 request {}", request);
        SpTargetSuggestedBidResponse.Builder builder = SpTargetSuggestedBidResponse.newBuilder();

        if (!request.hasPuid() || !request.hasShopId() || StringUtils.isBlank(request.getTargetIds())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result<List<SuggestedTargetVo>> res = cpcTargetingService.getSuggestedBid(request.getPuid().getValue(), request.getShopId().getValue(), Lists.newArrayList(request.getTargetIds().split(",")));
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }

            if (res.success()) {
                //处理list
                List<SuggestedTargetVo> data = res.getData();
                List<SuggestedTargetRpcVo> rpcVos = data.stream().filter(Objects::nonNull).map(item -> {
                    SuggestedTargetRpcVo.Builder voBuilder = SuggestedTargetRpcVo.newBuilder();
                    if (item.getTargetId() != null) {
                        voBuilder.setTargetId(item.getTargetId());
                    }
                    if (item.getSuggested() != null) {
                        voBuilder.setSuggested(item.getSuggested());
                    }
                    if (item.getRangeEnd() != null) {
                        voBuilder.setRangeEnd(item.getRangeEnd());
                    }
                    if (item.getRangeStart() != null) {
                        voBuilder.setRangeStart(item.getRangeStart());
                    }
                    if (item.getEstimatedImpressionUpper() != null) {
                        voBuilder.setEstimatedImpressionUpper(item.getEstimatedImpressionUpper());
                    }
                    if (item.getEstimatedImpressionLower() != null) {
                        voBuilder.setEstimatedImpressionLower(item.getEstimatedImpressionLower());
                    }
                    if (CollectionUtils.isNotEmpty(item.getExpression())) {
                        List<SuggestedTargetRpcVo.Expression> expression = item.getExpression().stream().map(innerItem -> {
                            SuggestedTargetRpcVo.Expression.Builder innerBuilder = SuggestedTargetRpcVo.Expression.newBuilder();
                            if (innerItem.getType() != null) {
                                innerBuilder.setType(innerItem.getType());
                            }
                            if (innerItem.getValue() != null) {
                                innerBuilder.setValue(innerItem.getValue());
                            }
                            return innerBuilder.build();
                        }).collect(Collectors.toList());
                        voBuilder.addAllExtension(expression);
                    }

                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }


        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void batchGetSuggestedBid(BatchTargetSuggestedBidRequest request, StreamObserver<SpTargetSuggestedBidResponse> responseObserver) {
        log.info("sp-targeting-获取投放的建议竞价 request {}", request);
        SpTargetSuggestedBidResponse.Builder builder = SpTargetSuggestedBidResponse.newBuilder();

        if (!request.hasPuid() || !request.hasShopId() || (!request.hasSourceShopId() && !request.getTargetDetailList().get(0).hasSourceShopId()) || CollectionUtils.isEmpty(request.getTargetDetailList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            List<TargetSuggestedBidVo> requestDetails = request.getTargetDetailList();
            List<TargetSuggestedBidDetail> details = new ArrayList<>();
            for (int i = 0; i < requestDetails.size(); i++) {
                TargetSuggestedBidDetail targetSuggestedBidDetail = new TargetSuggestedBidDetail();
                BeanUtils.copyProperties(requestDetails.get(i), targetSuggestedBidDetail);
                targetSuggestedBidDetail.setIndex(i);
                targetSuggestedBidDetail.setSourceShopId(requestDetails.get(i).getSourceShopId().getValue());
                details.add(targetSuggestedBidDetail);
            }
            //兼容单店铺
            List<Integer> sourceShopIds = new ArrayList<>();
            if (details.get(0).getSourceShopId() != null && details.get(0).getSourceShopId() != 0) {
                sourceShopIds.addAll(details.stream().map(TargetSuggestedBidDetail::getSourceShopId).filter(e -> e != null && !e.equals(0)).distinct().collect(Collectors.toList()));
            } else {
                sourceShopIds.add(request.getSourceShopId().getValue());
            }
            Result<List<SuggestedTargetVo>> res = cpcTargetingService.batchGetSuggestedBid(
                    request.getPuid().getValue(), request.getShopId().getValue(), sourceShopIds, details, request.getTargetingType());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }

            if (res.success()) {
                //处理list
                List<SuggestedTargetVo> data = res.getData();
                List<SuggestedTargetRpcVo> rpcVos = data.stream().filter(Objects::nonNull).map(item -> {
                    SuggestedTargetRpcVo.Builder voBuilder = SuggestedTargetRpcVo.newBuilder();
                    if (item.getTargetId() != null) {
                        voBuilder.setTargetId(item.getTargetId());
                    }
                    if (item.getSuggested() != null) {
                        voBuilder.setSuggested(item.getSuggested());
                    }
                    if (item.getRangeEnd() != null) {
                        voBuilder.setRangeEnd(item.getRangeEnd());
                    }
                    if (item.getRangeStart() != null) {
                        voBuilder.setRangeStart(item.getRangeStart());
                    }
                    if (CollectionUtils.isNotEmpty(item.getExpression())) {
                        List<SuggestedTargetRpcVo.Expression> expression = item.getExpression().stream().map(innerItem -> {
                            SuggestedTargetRpcVo.Expression.Builder innerBuilder = SuggestedTargetRpcVo.Expression.newBuilder();
                            if (innerItem.getType() != null) {
                                innerBuilder.setType(innerItem.getType());
                            }
                            if (innerItem.getValue() != null) {
                                innerBuilder.setValue(innerItem.getValue());
                            }
                            return innerBuilder.build();
                        }).collect(Collectors.toList());
                        voBuilder.addAllExtension(expression);
                    }
                    voBuilder.setIndex(item.getIndex());
                    return voBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllData(rpcVos);
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 获取投放的建议竞价(多店铺)
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void getSuggestedBidMultiShop(SpTargetSuggestedBidMultiShopRequest request, StreamObserver<SpTargetSuggestedBidResponse> responseObserver) {
        log.info("sp-targeting-获取投放的建议竞价 request {}", request);
        SpTargetSuggestedBidResponse.Builder builder = SpTargetSuggestedBidResponse.newBuilder();

        if (!request.hasPuid() || request.getTargetCount() <= 0
                || !request.getTargetList().stream().allMatch(SpTargetSuggestedBidMultiShop::hasTargetId)
                || !request.getTargetList().stream().allMatch(SpTargetSuggestedBidMultiShop::hasShopId)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            String type = request.getTargetList().get(0).getType();
            List<TargetSuggestBidBatchQo> targetList = request.getTargetList().stream().map(k -> {
                TargetSuggestBidBatchQo suggestedBid = new TargetSuggestBidBatchQo();
                suggestedBid.setShopId(k.getShopId().getValue());
                suggestedBid.setTargetId(k.getTargetId());
                return suggestedBid;
            }).collect(Collectors.toList());
            //处理业务返回结果
            Result<List<SuggestedTargetVo>> res;
            if (Constants.SD.equalsIgnoreCase(type)) {
                Result<List<SdSuggestedTargetVo>> sdRes = cpcSdTargetingService.getSuggestedBidMultiShop(request.getPuid().getValue(), targetList);
                builder.setCode(Int32Value.of(sdRes.getCode()));
                if (sdRes.getMsg() != null) {
                    builder.setMsg(sdRes.getMsg());
                }
                if (sdRes.success()) {
                    List<SuggestedTargetRpcVo> rpcVos = this.buildSdSuggestedTargetRpcVo(sdRes);
                    builder.addAllData(rpcVos);
                }
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            } else if (Constants.SB.equalsIgnoreCase(type)) {
                res = cpcSbTargetService.getSuggestedBidMultiShop(request.getPuid().getValue(), targetList);
            } else {
                res = cpcTargetingService.getSuggestedBidMultiShop(request.getPuid().getValue(), targetList);
            }
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                List<SuggestedTargetRpcVo> rpcVos = this.buildSpSbSuggestedTargetRpcVo(res);
                builder.addAllData(rpcVos);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 更新竞价
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateBatch(UpdateBatchSpTargetRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-targeting-更新投放竞价 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasPuid() || !request.hasUid() || request.getVosCount() < 1 || !request.hasShopId() || !request.hasType()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            List<UpdateBatchTargetVo> collect = request.getVosList().stream().map(re -> {
                UpdateBatchTargetVo vo = new UpdateBatchTargetVo();
                if (re.hasId()) {
                    vo.setId(re.getId().getValue());
                }
                vo.setPuid(request.getPuid().getValue());
                vo.setUid(request.getUid().getValue());
                vo.setShopId(request.getShopId().getValue());
                vo.setState(re.getState());
                if (re.hasBid()) {
                    vo.setBid(re.getBid().getValue());
                }
                return vo;
            }).collect(Collectors.toList());

            Result res = cpcTargetingService.updateBatch(collect, request.getType(), request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getData() != null) {
                builder.setData(JSONUtil.objectToJson(res.getData()));
            }
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    /**
     * 更新竞价(多店铺)
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateBatchMultiShop(UpdateBatchMultiShopSpTargetRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-targeting-更新投放竞价 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasPuid() || !request.hasUid() || request.getVosCount() < 1 || !request.hasType() || !request.getVosList().stream().allMatch(e -> e.hasShopId())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            List<UpdateBatchTargetVo> collect = request.getVosList().stream().map(re -> {
                UpdateBatchTargetVo vo = new UpdateBatchTargetVo();
                if (re.hasId()) {
                    vo.setId(re.getId().getValue());
                }
                vo.setPuid(request.getPuid().getValue());
                vo.setUid(request.getUid().getValue());
                vo.setShopId(re.getShopId().getValue());
                vo.setState(re.getState());
                if (re.hasBid()) {
                    vo.setBid(re.getBid().getValue());
                }
                return vo;
            }).collect(Collectors.toList());

            String adType = request.getVosList().get(0).getAdType();
            Result res;
            if (Constants.SD.equalsIgnoreCase(adType)) {
                res = cpcSdTargetingService.updateBatchMultiShop(collect, request.getType(), request.getLoginIp());
            } else if (Constants.SB.equalsIgnoreCase(adType)) {
                res = cpcSbTargetService.updateBatchMultiShop(collect, request.getType(), request.getLoginIp());
            } else {
                res = cpcTargetingService.updateBatchMultiShop(collect, request.getType(), request.getLoginIp());
            }
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getData() != null) {
                builder.setData(JSONUtil.objectToJson(res.getData()));
            }
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    /**
     * 根据品牌关键词获取品牌信息
     */
    @Override
    public void getBrandByKeyword(getBrandByKeywordRequest request, StreamObserver<SpTargetSuggestCategoryBrandResponse> responseObserver) {
        log.info("sp-targeting-根据品牌关键词获取品牌信息 request {}", request);
        SpTargetSuggestCategoryBrandResponse.Builder builder = SpTargetSuggestCategoryBrandResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId()
                || StringUtils.isBlank(request.getKeyword())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result<List<SuggestCategoryBrandVo>> res = cpcTargetingService.getBrandByKeyword(request.getPuid().getValue(), request.getShopId().getValue(), request.getKeyword());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg() != null) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                List<SuggestCategoryBrandVo> data = res.getData();
                List<SuggestCategoryBrandRpcVo> rpcVos = data.stream().filter(Objects::nonNull).map(item -> {
                    SuggestCategoryBrandRpcVo.Builder voBuilder = SuggestCategoryBrandRpcVo.newBuilder();
                    voBuilder.setId(item.getId());
                    voBuilder.setName(item.getName());
                    return voBuilder.build();
                }).collect(Collectors.toList());

                builder.addAllData(rpcVos);
            }

        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 批量创建否定投放
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void batchAddNeTargeting(CreateSpNeTargetingRequest request, StreamObserver<BatchSpNetargetingResponse> responseObserver) {
        log.info("sp 批量创建否定投放 request {}", request);
        BatchSpNetargetingResponse.Builder builder = BatchSpNetargetingResponse.newBuilder();

        if (!request.hasShopId() || CollectionUtils.isEmpty(request.getNeTargetingList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            AddNeTargetingVo addNeTargetingVo = new AddNeTargetingVo();
            addNeTargetingVo.setPuid(request.getPuid().getValue());
            addNeTargetingVo.setUid(request.getUid().getValue());
            addNeTargetingVo.setShopId(request.getShopId().getValue());
            //处理list
            List<NeTargetingRpcVo> neTargetingList = request.getNeTargetingList();
            List<NeTargetingVo> ntVos = neTargetingList.stream().filter(Objects::nonNull).map(item -> {
                NeTargetingVo neTargetingVo = new NeTargetingVo();
                neTargetingVo.setGroupId(item.getGroupId());
                neTargetingVo.setIndex(item.getIndex());
                neTargetingVo.setAsin(item.getAsin());
                neTargetingVo.setTitle(item.getTitle());
                neTargetingVo.setImgUrl(item.getImgUrl());
                neTargetingVo.setBrandId(item.getBrandId());
                neTargetingVo.setBrandName(item.getBrandName());
                neTargetingVo.setType(item.getType());
                return neTargetingVo;
            }).collect(Collectors.toList());
            addNeTargetingVo.setNeTargetings(ntVos);
            //处理业务返回结果
            Result<BatchSpDataResponse> res = cpcTargetingService.batchAddNeTargeting(addNeTargetingVo, request.getLoginIp());
            // 错误信息返回：code = error返回给result.msg; code = success返回给data里的failmsg
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.error()) {
                builder.setMsg(res.getMsg());
            }
            if (res.getData() != null) {
                builder.setData(res.getData());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();


    }


    @Override
    public void getTargetList(GetTargetListRequest request, StreamObserver<GetTargetListResponse> responseObserver) {
        GetTargetListResponse.Builder builder = GetTargetListResponse.newBuilder();
        if (!request.hasPuid() || request.getShopIdCount() < 1 || request.getTargetIdsCount() < 1) {
            builder.setCode(Int32Value.of(-1));
            responseObserver.onNext(builder.build());
            return;
        }
        List<TargetVo> listTarget = cpcTargetingService.getListTarget(request.getPuid().getValue(), request.getShopIdList().stream().map(Int32Value::getValue).collect(Collectors.toList()), request.getTargetIdsList());

        builder.setCode(Int32Value.of(0));
        if (CollectionUtils.isEmpty(listTarget)) {
            builder.addAllData(listTarget);
        }
        responseObserver.onNext(builder.build());
    }

    private List<SuggestedTargetRpcVo> buildSpSbSuggestedTargetRpcVo(Result<List<SuggestedTargetVo>> res) {
        //处理list
        List<SuggestedTargetVo> data = res.getData();
        List<SuggestedTargetRpcVo> rpcVos = data.stream().filter(Objects::nonNull).map(item -> {
            SuggestedTargetRpcVo.Builder voBuilder = SuggestedTargetRpcVo.newBuilder();
            if (item.getTargetId() != null) {
                voBuilder.setTargetId(item.getTargetId());
            }
            if (item.getSuggested() != null) {
                voBuilder.setSuggested(item.getSuggested());
            }
            if (item.getRangeEnd() != null) {
                voBuilder.setRangeEnd(item.getRangeEnd());
            }
            if (item.getRangeStart() != null) {
                voBuilder.setRangeStart(item.getRangeStart());
            }
            if (item.getEstimatedImpressionUpper() != null) {
                voBuilder.setEstimatedImpressionUpper(item.getEstimatedImpressionUpper());
            }
            if (item.getEstimatedImpressionLower() != null) {
                voBuilder.setEstimatedImpressionLower(item.getEstimatedImpressionLower());
            }
            if (CollectionUtils.isNotEmpty(item.getExpression())) {
                List<SuggestedTargetRpcVo.Expression> expression = item.getExpression().stream().map(innerItem -> {
                    SuggestedTargetRpcVo.Expression.Builder innerBuilder = SuggestedTargetRpcVo.Expression.newBuilder();
                    if (innerItem.getType() != null) {
                        innerBuilder.setType(innerItem.getType());
                    }
                    if (innerItem.getValue() != null) {
                        innerBuilder.setValue(innerItem.getValue());
                    }
                    return innerBuilder.build();
                }).collect(Collectors.toList());
                voBuilder.addAllExtension(expression);
            }

            return voBuilder.build();
        }).collect(Collectors.toList());
        return rpcVos;
    }

    private List<SuggestedTargetRpcVo> buildSdSuggestedTargetRpcVo(Result<List<SdSuggestedTargetVo>> sdRes) {
        //处理list
        List<SdSuggestedTargetVo> data = sdRes.getData();
        List<SuggestedTargetRpcVo> rpcVos = data.stream().filter(Objects::nonNull).map(item -> {
            SuggestedTargetRpcVo.Builder voBuilder = SuggestedTargetRpcVo.newBuilder();
            if (item.getTargetId() != null) {
                voBuilder.setTargetId(item.getTargetId());
            }
            if (item.getSuggested() != null) {
                voBuilder.setSuggested(item.getSuggested());
            }
            if (item.getRangeEnd() != null) {
                voBuilder.setRangeEnd(item.getRangeEnd());
            }
            if (item.getRangeStart() != null) {
                voBuilder.setRangeStart(item.getRangeStart());
            }
            if (CollectionUtils.isNotEmpty(item.getExpression())) {
                List<SuggestedTargetRpcVo.Expression> expression = item.getExpression().stream().map(innerItem -> {
                    SuggestedTargetRpcVo.Expression.Builder innerBuilder = SuggestedTargetRpcVo.Expression.newBuilder();
                    if (innerItem.getType() != null) {
                        innerBuilder.setType(innerItem.getType());
                    }
                    if (innerItem.getValue() != null) {
                        innerBuilder.setValue(String.valueOf(innerItem.getValue()));
                    }
                    return innerBuilder.build();
                }).collect(Collectors.toList());
                voBuilder.addAllExtension(expression);
            }

            return voBuilder.build();
        }).collect(Collectors.toList());
        return rpcVos;
    }
}
