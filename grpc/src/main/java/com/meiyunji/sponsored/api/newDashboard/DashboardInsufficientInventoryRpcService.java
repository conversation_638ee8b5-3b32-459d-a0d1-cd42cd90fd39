package com.meiyunji.sponsored.api.newDashboard;

import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.newDashboard.*;
import com.meiyunji.sponsored.rpc.newDashboard.vo.UrlResponse;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardInsufficientInventoryDataDto;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardInsufficientInventoryService;
import com.meiyunji.sponsored.service.newDashboard.util.PageUtils;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardInsufficientInventoryReqVo;
import com.meiyunji.sponsored.service.sellfoxApi.vo.FbaInTransitApiVO;
import com.meiyunji.sponsored.service.sellfoxApi.vo.FbaStockApiVO;
import com.meiyunji.sponsored.service.sellfoxApi.vo.OutOfStockDateAfterArrivalVO;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * 库存不足接口
 * @author: zzh
 * @date: 2025-03-14  10:41
 */
@GRpcService
@Slf4j
public class DashboardInsufficientInventoryRpcService extends RpcDashboardInsufficientInventoryServiceGrpc.RpcDashboardInsufficientInventoryServiceImplBase {

    @Autowired
    private IDashboardInsufficientInventoryService dashboardInsufficientInventoryService;

    @Override
    public void queryInsufficientInventoryData(DashboardInsufficientInventoryRequest request, StreamObserver<DashboardInsufficientInventoryResponse> responseObserver) {
        log.info("dashboard query insufficient inventory data, process request data: {}", request);
        long begin = System.currentTimeMillis();
        DashboardInsufficientInventoryReqVo reqVo = processParam(request);
        List<DashboardInsufficientInventoryDataDto> dataList = dashboardInsufficientInventoryService.queryInsufficientInventoryData(reqVo);
        DashboardInsufficientInventoryResponse.Builder builder = DashboardInsufficientInventoryResponse.newBuilder();
        // 构建响应参数
        InsufficientInventoryVo buildVoList = buildVoList(dataList, reqVo);
        builder.setData(buildVoList);
        builder.setCode(Result.SUCCESS);
        log.info("dashboard query insufficient inventory data, puid: {}, 耗时: {}", request.getPuid(), System.currentTimeMillis()-begin);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void exportInsufficientInventoryData(DashboardInsufficientInventoryRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("dashboard export insufficient inventory data, request data: {}", request);
        long begin = System.currentTimeMillis();
        UrlResponse.Builder builder = UrlResponse.newBuilder();
        DashboardInsufficientInventoryReqVo reqVo = processParam(request);
        List<String> urlList = dashboardInsufficientInventoryService.exportInsufficientInventoryData(reqVo);
        if (CollectionUtils.isNotEmpty(urlList)) {
            builder.addAllUrls(urlList);
            builder.setCode(Int32Value.of(Result.SUCCESS));
        } else {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("process.msg.sync.fail");
        }
        log.info("dashboard export insufficient inventory data, puid: {}, 耗时: {}", request.getPuid(), System.currentTimeMillis()-begin);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 处理参数
     * @param request
     * @return
     */
    private DashboardInsufficientInventoryReqVo processParam(DashboardInsufficientInventoryRequest request) {
        //将grpc请求对象转换未查询实体类
        DashboardInsufficientInventoryReqVo reqVo = new DashboardInsufficientInventoryReqVo();
        BeanUtils.copyProperties(request, reqVo);
        reqVo.setPuid(reqVo.getPuid());
        reqVo.setMarketplaceIdList(reqVo.getMarketplaceIdList());
        reqVo.setShopIdList(reqVo.getShopIdList());
        if(CollectionUtils.isEmpty(reqVo.getShopIdList())){
            throw new SponsoredBizException("没有可见权限的店铺信息");
        }
        return reqVo;
    }

    private static InsufficientInventoryVo buildVoList(List<DashboardInsufficientInventoryDataDto> dataList, DashboardInsufficientInventoryReqVo reqVo) {
        InsufficientInventoryVo.Builder voBuilder = InsufficientInventoryVo.newBuilder();
        List<InsufficientInventoryVo.Page.InsufficientInventoryPageVo> voList = new ArrayList<>();
        for (DashboardInsufficientInventoryDataDto inventoryDataDto : dataList) {
            InsufficientInventoryVo.Page.InsufficientInventoryPageVo.Builder inventoryBuilder = InsufficientInventoryVo.Page.InsufficientInventoryPageVo.newBuilder();
            BeanUtils.copyProperties(inventoryDataDto, inventoryBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(inventoryDataDto));
            if(inventoryDataDto.getDailySale() != null){
                inventoryBuilder.setDailySale(inventoryDataDto.getDailySale().doubleValue());
            }
            if(StringUtil.isNotEmpty(inventoryDataDto.getOutOfStockAfterArrival())){
                inventoryBuilder.setOutOfStockAfterArrival(inventoryDataDto.getOutOfStockAfterArrival()+" ("+inventoryDataDto.getBetweenDay()+"天)");
            }
            if(CollectionUtils.isNotEmpty(inventoryDataDto.getMsku())){
                inventoryBuilder.addAllMsku(inventoryDataDto.getMsku());
            }
            if(CollectionUtils.isNotEmpty(inventoryDataDto.getOutOfStockDateAfterArrivalDetail())){
                List<OutOfStockDateAfterArrival> arrivalList = new ArrayList<>();
                for (OutOfStockDateAfterArrivalVO afterArrivalVO : inventoryDataDto.getOutOfStockDateAfterArrivalDetail()) {
                    OutOfStockDateAfterArrival.Builder arrivalBuilder = OutOfStockDateAfterArrival.newBuilder();
                    BeanUtils.copyProperties(afterArrivalVO, arrivalBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(afterArrivalVO));
                    arrivalList.add(arrivalBuilder.build());
                }
                inventoryBuilder.addAllOutOfStockDateAfterArrivalDetail(arrivalList);
            }
            if(CollectionUtils.isNotEmpty(inventoryDataDto.getFbaStockDetail())){
                List<FbaStock> arrivalList = new ArrayList<>();
                for (FbaStockApiVO fbaStock : inventoryDataDto.getFbaStockDetail()) {
                    FbaStock.Builder stockBuilder = FbaStock.newBuilder();
                    BeanUtils.copyProperties(fbaStock, stockBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(fbaStock));
                    if(fbaStock.getShopVO() != null){
                        Shop.Builder shopBuilder = Shop.newBuilder();
                        BeanUtils.copyProperties(fbaStock.getShopVO(), shopBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(fbaStock.getShopVO()));
                        stockBuilder.setShopVO(shopBuilder.build());
                    }
                    arrivalList.add(stockBuilder.build());
                }
                inventoryBuilder.addAllFbaStockDetail(arrivalList);
            }
            if(CollectionUtils.isNotEmpty(inventoryDataDto.getFbaInTransitDetail())){
                List<FbaInTransit> transitList = new ArrayList<>();
                for (FbaInTransitApiVO transit : inventoryDataDto.getFbaInTransitDetail()) {
                    FbaInTransit.Builder transitBuilder = FbaInTransit.newBuilder();
                    BeanUtils.copyProperties(transit, transitBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(transit));
                    if(transit.getShopVO() != null){
                        Shop.Builder shopBuilder = Shop.newBuilder();
                        BeanUtils.copyProperties(transit.getShopVO(), shopBuilder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(transit.getShopVO()));
                        transitBuilder.setShopVO(shopBuilder.build());
                    }
                    if(StringUtil.isNotEmpty(transit.getExpectArrivalDate())){
                        transitBuilder.setExpectArrivalDate(transit.getExpectArrivalDate()+" ("+transit.getBetweenArrivalTime()+")");
                    }
                    transitList.add(transitBuilder.build());
                }
                inventoryBuilder.addAllFbaInTransitDetail(transitList);
            }
            voList.add(inventoryBuilder.build());
        }
        InsufficientInventoryVo.Page insufficientInventoryPageInfo = PageUtils.getInsufficientInventoryPageInfo(voList, reqVo.getPageSize(), reqVo.getPageNo());
        voBuilder.setPage(insufficientInventoryPageInfo);
        return voBuilder.build();
    }
}
