package com.meiyunji.sponsored.api.budget;

import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.budget.analysis.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.budgetUsage.IAmazonAdBudgetAnalysisService;
import com.meiyunji.sponsored.service.budgetUsage.dto.BudgetAnalysisBudgetChartDto;
import com.meiyunji.sponsored.service.budgetUsage.dto.BudgetAnalysisBudgetChartQueryDto;
import com.meiyunji.sponsored.service.budgetUsage.dto.BudgetAnalysisPageDto;
import com.meiyunji.sponsored.service.budgetUsage.dto.BudgetAnalysisQueryRequest;
import com.meiyunji.sponsored.service.cpc.constants.AdManagePageExportTaskTypeEnum;
import com.meiyunji.sponsored.service.cpc.service2.IAdManagePageExportTaskService;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@GRpcService
@Slf4j
@RequiredArgsConstructor
public class BudgetAnalysisRpcService extends RPCBudgetAnalysisServiceGrpc.RPCBudgetAnalysisServiceImplBase {

    private final IAmazonAdBudgetAnalysisService amazonAdBudgetAnalysisService;

    private final IAdManagePageExportTaskService adManagePageExportTaskService;

    private final IScVcShopAuthDao shopAuthDao;

    @Override
    public void pageList(BudgetAnalysisQueryGrpcRequest request, StreamObserver<BudgetAnalysisQueryGrpcResponse> responseObserver) {
        log.info(" page list = {}", request);
        Page<BudgetAnalysisPageDto> analysisPageDtoPage = amazonAdBudgetAnalysisService.listPage(build(request));
        BudgetAnalysisQueryGrpcResponse.Builder builder = BudgetAnalysisQueryGrpcResponse.newBuilder();
        builder.setCode(Result.SUCCESS);
        BudgetAnalysisQueryGrpcPage.Builder pageBuilder = BudgetAnalysisQueryGrpcPage.newBuilder();
        pageBuilder.setPageNo(analysisPageDtoPage.getPageNo());
        pageBuilder.setPageSize(analysisPageDtoPage.getPageSize());
        pageBuilder.setTotalPage(analysisPageDtoPage.getTotalPage());
        pageBuilder.setTotalSize(analysisPageDtoPage.getTotalSize());
        pageBuilder.addAllRows(analysisPageDtoPage.getRows().stream().map(this::build).collect(Collectors.toList()));
        builder.setData(pageBuilder);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getBudgetTypeChart(GetBudgetTypeChartGrpcRequest request, StreamObserver<GetBudgetTypeChartGrpcResponse> responseObserver) {
        log.info(" getBudgetTypeChart = {}", request);
        //过滤掉未授权的店铺
        List<Integer> shopIds = shopAuthDao.getAuthShopByShopIdList(request.getPuid(), request.getShopIdListList()).stream().map(ShopAuth::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shopIds)) {
            shopIds.add(-1);
        }
        BudgetAnalysisBudgetChartDto budgetAnalysisBudgetChartDto = amazonAdBudgetAnalysisService.budgetAnalysisTotalBudget(build(request, shopIds));
        GetBudgetTypeChartGrpcResponse.Builder builder = GetBudgetTypeChartGrpcResponse.newBuilder();
        builder.setCode(Result.SUCCESS);
        builder.setData(build(budgetAnalysisBudgetChartDto));
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void exportBudgetAnalysis(ExportBudgetAnalysisGrpcRequest request, StreamObserver<ExportBudgetAnalysisGrpcResponse> responseObserver) {
        log.info("exportBudgetAnalysis = {}", request);
        BudgetAnalysisQueryRequest budgetAnalysisQueryRequest = JSONUtil.jsonToObject(request.getJson(), BudgetAnalysisQueryRequest.class);
        ExportBudgetAnalysisGrpcResponse.Builder builder = ExportBudgetAnalysisGrpcResponse.newBuilder();
        if (budgetAnalysisQueryRequest == null) {
            builder.setCode(-1);
            builder.setMsg("请求参数错误!");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        if (budgetAnalysisQueryRequest.getSiteToday() != null && budgetAnalysisQueryRequest.getSiteToday()) {
            String nowDate = LocalDate.now().toString();
            budgetAnalysisQueryRequest.setStartDate(nowDate);
            budgetAnalysisQueryRequest.setEndDate(nowDate);
        }
        //插入任务
        Long id = adManagePageExportTaskService.saveExportTask(request.getPuid(), request.getUid(), request.getShopId(),
                AdManagePageExportTaskTypeEnum.BUDGET_ANALYSIS, budgetAnalysisQueryRequest.getStartDate().replaceAll("-", ""), budgetAnalysisQueryRequest.getEndDate().replaceAll("-", ""), budgetAnalysisQueryRequest);
        if (id == null) {
            builder.setCode(-1);
            builder.setMsg("新建任务异常，请联系管理员!");
        } else {
            builder.setCode(0);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private BudgetAnalysisBudgetChartQueryDto build(GetBudgetTypeChartGrpcRequest request, List<Integer> shopIds) {
        BudgetAnalysisBudgetChartQueryDto budgetAnalysisBudgetChartQueryDto = new BudgetAnalysisBudgetChartQueryDto();
        budgetAnalysisBudgetChartQueryDto.setPuid(request.getPuid());
        budgetAnalysisBudgetChartQueryDto.setShopIdList(shopIds);
        budgetAnalysisBudgetChartQueryDto.setMarketplaceIdList(request.getMarketplaceIdListList());
        budgetAnalysisBudgetChartQueryDto.setStatus(request.getStatus());
        return budgetAnalysisBudgetChartQueryDto;
    }

    private GetBudgetTypeChartGrpcData build(BudgetAnalysisBudgetChartDto res) {
        GetBudgetTypeChartGrpcData.Builder builder = GetBudgetTypeChartGrpcData.newBuilder();
        builder.setCurrencyCode(StringUtil.getSafeValue(res.getCurrencyCode()));
        builder.setTotalBudget(StringUtil.getSafeValue(res.getTotalBudget()).toString());
        for (BudgetAnalysisBudgetChartDto.BudgetAnalysisBudgetChartItemDto itemDto : res.getItemDtoList()) {
            GetBudgetTypeChartGrpcItemData.Builder builder1 = GetBudgetTypeChartGrpcItemData.newBuilder();
            builder1.setBudget(StringUtil.getSafeValue(itemDto.getBudget()));
            builder1.setType(StringUtil.getSafeValue(itemDto.getType()));
            if (res.getTotalBudget().compareTo(BigDecimal.ZERO) == 0) {
                builder1.setPercent("0.00");
            } else {
                builder1.setPercent(new BigDecimal(itemDto.getBudget()).multiply(BigDecimal.valueOf(100L)).divide(res.getTotalBudget(), 2, RoundingMode.HALF_UP).toString());
            }
            builder.addItem(builder1);
        }
        return builder.build();
    }

    private BudgetAnalysisQueryGrpcRecord build(BudgetAnalysisPageDto budgetAnalysisPageDto) {
        BudgetAnalysisQueryGrpcRecord.Builder builder = BudgetAnalysisQueryGrpcRecord.newBuilder();
        builder.setName(StringUtil.getSafeValue(budgetAnalysisPageDto.getName()));
        builder.setState(StringUtil.getSafeValue(budgetAnalysisPageDto.getState()));
        builder.setStateName(StringUtil.getSafeValue(budgetAnalysisPageDto.getStateName()));
        builder.setShopId(StringUtil.getSafeValue(budgetAnalysisPageDto.getShopId()));
        builder.setShopName(StringUtil.getSafeValue(budgetAnalysisPageDto.getShopName()));
        builder.setMarketplaceTime(LocalDateTimeUtil.getHourTime(LocalDateTime.now(Marketplace.fromId(budgetAnalysisPageDto.getMarketplaceId()).getTimeZone().toZoneId())));
        builder.setMarketplaceId(StringUtil.getSafeValue(budgetAnalysisPageDto.getMarketplaceId()));
        builder.setSiteName(StringUtil.getSafeValue(budgetAnalysisPageDto.getSiteName()));
        builder.setCampaignId(StringUtil.getSafeValue(budgetAnalysisPageDto.getCampaignId()));
        builder.setPortfolioId(budgetAnalysisPageDto.getPortfolioId() != null ? budgetAnalysisPageDto.getPortfolioId() : "-");
        builder.setPortfolioName(budgetAnalysisPageDto.getPortfolioName() != null ? budgetAnalysisPageDto.getPortfolioName() : "-");
        builder.setType(StringUtil.getSafeValue(budgetAnalysisPageDto.getType()));
        builder.setBudget(StringUtil.getSafeValue(budgetAnalysisPageDto.getBudget()).toString());
        builder.setDate(StringUtil.getSafeValue(budgetAnalysisPageDto.getDate()));
        builder.setBudgetRemaining(StringUtil.getSafeValue(budgetAnalysisPageDto.getBudgetRemaining()).toString());
        builder.setMissBudgetRecommendation(build(budgetAnalysisPageDto.getMissBudgetRecommendation()));
        builder.setBudgetUsagePercentage(StringUtil.getSafeValue(budgetAnalysisPageDto.getBudgetUsagePercentage()).toString());
        builder.setDailyBudget(StringUtil.getSafeValue(budgetAnalysisPageDto.getDailyBudget()).toString());
        if (CollectionUtils.isNotEmpty(budgetAnalysisPageDto.getBudgetRemainingChart())) {
            builder.addAllBudgetRemainingChart(budgetAnalysisPageDto.getBudgetRemainingChart().stream().map(this::build).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(budgetAnalysisPageDto.getOverBudgetTime())) {
            builder.addAllOverBudgetTime(budgetAnalysisPageDto.getOverBudgetTime().stream().map(this::build).collect(Collectors.toList()));
        }
        if (budgetAnalysisPageDto.getSuggestedBudget() != null) {
            builder.setSuggestedBudget(budgetAnalysisPageDto.getSuggestedBudget().toString());
        }

        if (budgetAnalysisPageDto.getId() != null) {
            builder.setId(budgetAnalysisPageDto.getId());
        }
        if (budgetAnalysisPageDto.getIsUpdateBudget() != null) {
            builder.setIsUpdateBudget(budgetAnalysisPageDto.getIsUpdateBudget());
        }
        if (budgetAnalysisPageDto.getBudgetLog()!= null) {
            BudgetAdOperationLogGrpc.Builder log = BudgetAdOperationLogGrpc.newBuilder();
            log.setCount(StringUtil.getSafeValue(budgetAnalysisPageDto.getBudgetLog().getCount()));
            if (StringUtils.isNotBlank(budgetAnalysisPageDto.getBudgetLog().getNewValue())){
                log.setNewValue(budgetAnalysisPageDto.getBudgetLog().getNewValue());
            }
            if (StringUtils.isNotBlank(budgetAnalysisPageDto.getBudgetLog().getPreviousValue())){
                log.setPreviousValue(budgetAnalysisPageDto.getBudgetLog().getPreviousValue());
            }
            if (StringUtils.isNotBlank(budgetAnalysisPageDto.getBudgetLog().getSiteOperationTime())){
                log.setSiteOperationTime(budgetAnalysisPageDto.getBudgetLog().getSiteOperationTime());
            }
            builder.setBudgetLog(log.build());
        }

        if (StringUtils.isNotBlank(budgetAnalysisPageDto.getPlacementProductPage())) {
            builder.setPlacementProductPage(budgetAnalysisPageDto.getPlacementProductPage());
        }

        if (StringUtils.isNotBlank(budgetAnalysisPageDto.getPlacementTop())) {
            builder.setPlacementTop(budgetAnalysisPageDto.getPlacementTop());
        }

        if (StringUtils.isNotBlank(budgetAnalysisPageDto.getPlacementRestOfSearch())) {
            builder.setPlacementRestOfSearch(budgetAnalysisPageDto.getPlacementRestOfSearch());
        }

        if (StringUtils.isNotBlank(budgetAnalysisPageDto.getStrategy())) {
            builder.setStrategy(budgetAnalysisPageDto.getStrategy());
        }
        builder.setServingStatus(StringUtil.getSafeValue(budgetAnalysisPageDto.getServingStatus()));
        builder.setServingStatusDec(StringUtil.getSafeValue(budgetAnalysisPageDto.getServingStatusDec()));
        builder.setServingStatusName(StringUtil.getSafeValue(budgetAnalysisPageDto.getServingStatusName()));
        builder.setBudgetAdjustmentNum(StringUtil.getSafeValue(budgetAnalysisPageDto.getBudgetAdjustmentNum()).toString());
        builder.setCampaignTargetingType(StringUtil.getSafeValue(budgetAnalysisPageDto.getCampaignTargetingType()));
        builder.setTargetingType(StringUtil.getSafeValue(budgetAnalysisPageDto.getTargetingType()));
        builder.setBudgetTime(StringUtil.getSafeValue(budgetAnalysisPageDto.getBudgetTime()));
        builder.setBudgetAdjustmentTime(StringUtil.getSafeValue(budgetAnalysisPageDto.getBudgetAdjustmentTime()));
        builder.setImpressions(budgetAnalysisPageDto.getImpressions().toString());
        builder.setClicks(budgetAnalysisPageDto.getClicks().toString());
        builder.setAdOrderNum(budgetAnalysisPageDto.getAdOrderNum().toString());
        builder.setCtr(budgetAnalysisPageDto.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        builder.setCvr(budgetAnalysisPageDto.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        builder.setOrderNum(budgetAnalysisPageDto.getOrderNum().toString());
        builder.setAdCost(budgetAnalysisPageDto.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        builder.setAdSale(budgetAnalysisPageDto.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        builder.setAcos(budgetAnalysisPageDto.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        builder.setRoas(budgetAnalysisPageDto.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        builder.setAdCostPerClick(budgetAnalysisPageDto.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        builder.setSpent(budgetAnalysisPageDto.getSpent().toString());
        builder.setEstimateClicks(budgetAnalysisPageDto.getEstimateClicks().toString());
        builder.setCpa(budgetAnalysisPageDto.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        builder.setAdvertisingUnitPrice(budgetAnalysisPageDto.getAdvertisingUnitPrice().setScale(2, RoundingMode.HALF_UP).toString());
        return builder.build();
    }


    private BudgetRemainingChart build(BudgetAnalysisPageDto.BudgetRemainingChart budgetRemainingChart) {
        return BudgetRemainingChart.newBuilder()
                .setCurrentBudget(StringUtil.getSafeValue(budgetRemainingChart.getCurrentBudget()))
                .setPercent(StringUtil.getSafeValue(budgetRemainingChart.getPercent()))
                .setUpdateAt(StringUtil.getSafeValue(budgetRemainingChart.getUpdateAt())).build();
    }

    private OverBudgetTime build(List<String> list) {
        return OverBudgetTime.newBuilder()
                .setStartDate(StringUtil.getSafeValue(list.get(0)))
                .setEndDate(StringUtil.getSafeValue(list.get(1))).build();
    }

    private MissBudgetRecommendationGrpcRes build(BudgetAnalysisPageDto.MissBudgetRecommendation succ) {
        MissBudgetRecommendationGrpcRes.Builder succBuilder = MissBudgetRecommendationGrpcRes.newBuilder();
        if (succ == null) {
            return succBuilder.build();
        }
        if (succ.getEstimatedMissedSalesLower() != null) {
            succBuilder.setEstimatedMissedSalesLower(succ.getEstimatedMissedSalesLower());
        }
        if (succ.getEstimatedMissedSalesUpper() != null) {
            succBuilder.setEstimatedMissedSalesUpper(succ.getEstimatedMissedSalesUpper());
        }
        if (succ.getEstimatedMissedImpressionsLower() != null) {
            succBuilder.setEstimatedMissedImpressionsLower(succ.getEstimatedMissedImpressionsLower());
        }
        if (succ.getEstimatedMissedImpressionsUpper() != null) {
            succBuilder.setEstimatedMissedImpressionsUpper(succ.getEstimatedMissedImpressionsUpper());
        }
        if (succ.getEstimatedMissedClicksLower() != null) {
            succBuilder.setEstimatedMissedClicksLower(succ.getEstimatedMissedClicksLower());
        }
        if (succ.getEstimatedMissedClicksUpper() != null) {
            succBuilder.setEstimatedMissedClicksUpper(succ.getEstimatedMissedClicksUpper());
        }
        if (succ.getPercentTimeInBudget() != null) {
            succBuilder.setPercentTimeInBudget(succ.getPercentTimeInBudget());
        }
        if (StringUtils.isNotBlank(succ.getStartDate())) {
            succBuilder.setStartDate(succ.getStartDate());
        }
        if (StringUtils.isNotBlank(succ.getEndDate())) {
            succBuilder.setEndDate(succ.getEndDate());
        }
        if (StringUtils.isNotBlank(succ.getRuleId())) {
            succBuilder.setRuleId(succ.getRuleId());
        }
        if (StringUtils.isNotBlank(succ.getRuleName())) {
            succBuilder.setRuleName(succ.getRuleName());
        }
        if (succ.getSuggestedBudgetIncreasePercent() != null) {
            succBuilder.setSuggestedBudgetIncreasePercent(succ.getSuggestedBudgetIncreasePercent());
        }
        return succBuilder.build();
    }

    private BudgetAnalysisQueryRequest build(BudgetAnalysisQueryGrpcRequest request) {
        BudgetAnalysisQueryRequest budgetAnalysisQueryRequest = new BudgetAnalysisQueryRequest();
        budgetAnalysisQueryRequest.setPageNo(request.getPageNo());
        budgetAnalysisQueryRequest.setPageSize(request.getPageSize());
        budgetAnalysisQueryRequest.setShopIdList(request.getShopIdListList());
        budgetAnalysisQueryRequest.setPuid(request.getPuid());
        budgetAnalysisQueryRequest.setOrderField(request.getOrderField());
        budgetAnalysisQueryRequest.setOrderType(request.getOrderType());
        budgetAnalysisQueryRequest.setStatus(request.getStatus());
        budgetAnalysisQueryRequest.setSeverStatus(request.getSeverStatus());
        budgetAnalysisQueryRequest.setStartDate(request.getStartDate());
        budgetAnalysisQueryRequest.setEndDate(request.getEndDate());
        budgetAnalysisQueryRequest.setType(request.getType());
        budgetAnalysisQueryRequest.setPortfolioIds(request.getPortfolioIds());
        budgetAnalysisQueryRequest.setCampaignIds(request.getCampaignIds());
        budgetAnalysisQueryRequest.setSiteToday(request.getSiteToday());
        budgetAnalysisQueryRequest.setUseAdvanced(request.getUseAdvanced());
        budgetAnalysisQueryRequest.setBudgetActiveTime(request.getBudgetActiveTime());
        budgetAnalysisQueryRequest.setProductType(request.getProductType());
        budgetAnalysisQueryRequest.setProductValue(request.getProductValue());
        budgetAnalysisQueryRequest.setOnlyCount(request.getOnlyCount());
        budgetAnalysisQueryRequest.setOutBudget(request.getOutBudget());
        budgetAnalysisQueryRequest.setMarketplaceIdList(request.getMarketplaceIdListList());
        if (request.getUseAdvanced()) {
            if (StringUtils.isNotBlank(request.getImpressionsMin())) {
                budgetAnalysisQueryRequest.setImpressionsMin(Integer.parseInt(request.getImpressionsMin()));
            }
            if (StringUtils.isNotBlank(request.getImpressionsMax())) {
                budgetAnalysisQueryRequest.setImpressionsMax(Integer.parseInt(request.getImpressionsMax()));
            }

            if (StringUtils.isNotBlank(request.getClicksMin())) {
                budgetAnalysisQueryRequest.setClicksMin(Integer.parseInt(request.getClicksMin()));
            }
            if (StringUtils.isNotBlank(request.getClicksMax())) {
                budgetAnalysisQueryRequest.setClicksMax(Integer.parseInt(request.getClicksMax()));
            }

            if (StringUtils.isNotBlank(request.getClickRateMin())) {
                budgetAnalysisQueryRequest.setClickRateMin(new BigDecimal(request.getClickRateMin()));
            }
            if (StringUtils.isNotBlank(request.getClickRateMax())) {
                budgetAnalysisQueryRequest.setClickRateMax(new BigDecimal(request.getClickRateMax()));
            }

            if (StringUtils.isNotBlank(request.getCostMin())) {
                budgetAnalysisQueryRequest.setCostMin(new BigDecimal(request.getCostMin()));
            }
            if (StringUtils.isNotBlank(request.getCostMax())) {
                budgetAnalysisQueryRequest.setCostMax(new BigDecimal(request.getCostMax()));
            }

            if (StringUtils.isNotBlank(request.getCpcMin())) {
                budgetAnalysisQueryRequest.setCpcMin(new BigDecimal(request.getCpcMin()));
            }
            if (StringUtils.isNotBlank(request.getCpcMax())) {
                budgetAnalysisQueryRequest.setCpcMax(new BigDecimal(request.getCpcMax()));
            }

            if (StringUtils.isNotBlank(request.getOrderNumMin())) {
                budgetAnalysisQueryRequest.setOrderNumMin(Integer.parseInt(request.getOrderNumMin()));
            }
            if (StringUtils.isNotBlank(request.getOrderNumMax())) {
                budgetAnalysisQueryRequest.setOrderNumMax(Integer.parseInt(request.getOrderNumMax()));
            }

            if (StringUtils.isNotBlank(request.getSalesMin())) {
                budgetAnalysisQueryRequest.setSalesMin(new BigDecimal(request.getSalesMin()));
            }
            if (StringUtils.isNotBlank(request.getSalesMax())) {
                budgetAnalysisQueryRequest.setSalesMax(new BigDecimal(request.getSalesMax()));
            }
            if (StringUtils.isNotBlank(request.getSalesConversionRateMin())) {
                budgetAnalysisQueryRequest.setSalesConversionRateMin(new BigDecimal(request.getSalesConversionRateMin()));
            }
            if (StringUtils.isNotBlank(request.getSalesConversionRateMax())) {
                budgetAnalysisQueryRequest.setSalesConversionRateMax(new BigDecimal(request.getSalesConversionRateMax()));
            }
            if (StringUtils.isNotBlank(request.getAcosMin())) {
                budgetAnalysisQueryRequest.setAcosMin(new BigDecimal(request.getAcosMin()));
            }
            if (StringUtils.isNotBlank(request.getAcosMax())) {
                budgetAnalysisQueryRequest.setAcosMax(new BigDecimal(request.getAcosMax()));
            }
            if (StringUtils.isNotBlank(request.getRoasMin())) {
                budgetAnalysisQueryRequest.setRoasMin(new BigDecimal(request.getRoasMin()));
            }
            if (StringUtils.isNotBlank(request.getRoasMax())) {
                budgetAnalysisQueryRequest.setRoasMax(new BigDecimal(request.getRoasMax()));
            }
            if (StringUtils.isNotBlank(request.getBudgetMin())) {
                budgetAnalysisQueryRequest.setBudgetMin(new BigDecimal(request.getBudgetMin()));
            }
            if (StringUtils.isNotBlank(request.getBudgetMax())) {
                budgetAnalysisQueryRequest.setBudgetMax(new BigDecimal(request.getBudgetMax()));
            }
            if (StringUtils.isNotBlank(request.getAdSalesTotalMin())) {
                budgetAnalysisQueryRequest.setAdSalesTotalMin(Integer.parseInt(request.getAdSalesTotalMin()));
            }
            if (StringUtils.isNotBlank(request.getAdSalesTotalMax())) {
                budgetAnalysisQueryRequest.setAdSalesTotalMax(Integer.parseInt(request.getAdSalesTotalMax()));
            }
            if (StringUtils.isNotBlank(request.getCpaMin())) {
                budgetAnalysisQueryRequest.setCpaMin(new BigDecimal(request.getCpaMin()));
            }
            if (StringUtils.isNotBlank(request.getCpaMax())) {
                budgetAnalysisQueryRequest.setCpaMax(new BigDecimal(request.getCpaMax()));
            }
            if (StringUtils.isNotBlank(request.getAdvertisingUnitPriceMin())) {
                budgetAnalysisQueryRequest.setAdvertisingUnitPriceMin(new BigDecimal(request.getAdvertisingUnitPriceMin()));
            }
            if (StringUtils.isNotBlank(request.getAdvertisingUnitPriceMax())) {
                budgetAnalysisQueryRequest.setAdvertisingUnitPriceMax(new BigDecimal(request.getAdvertisingUnitPriceMax()));
            }
            if (StringUtils.isNotBlank(request.getAdCostPerClickMin())) {
                budgetAnalysisQueryRequest.setAdCostPerClickMin(new BigDecimal(request.getAdCostPerClickMin()));
            }
            if (StringUtils.isNotBlank(request.getAdCostPerClickMax())) {
                budgetAnalysisQueryRequest.setAdCostPerClickMax(new BigDecimal(request.getAdCostPerClickMax()));
            }
            if (StringUtils.isNotBlank(request.getBudgetAdjustmentNumMin())) {
                budgetAnalysisQueryRequest.setBudgetAdjustmentNumMin(Integer.parseInt(request.getBudgetAdjustmentNumMin()));
            }
            if (StringUtils.isNotBlank(request.getBudgetAdjustmentNumMax())) {
                budgetAnalysisQueryRequest.setBudgetAdjustmentNumMax(Integer.parseInt(request.getBudgetAdjustmentNumMax()));
            }
            if (StringUtils.isNotBlank(request.getSpentMin())) {
                budgetAnalysisQueryRequest.setSpentMin(new BigDecimal(request.getSpentMin()));
            }
            if (StringUtils.isNotBlank(request.getSpentMax())) {
                budgetAnalysisQueryRequest.setSpentMax(new BigDecimal(request.getSpentMax()));
            }
            if (StringUtils.isNotBlank(request.getEstimateClicksMin())) {
                budgetAnalysisQueryRequest.setEstimateClicksMin(new BigDecimal(request.getEstimateClicksMin()));
            }
            if (StringUtils.isNotBlank(request.getEstimateClicksMax())) {
                budgetAnalysisQueryRequest.setEstimateClicksMax(new BigDecimal(request.getEstimateClicksMax()));
            }
            if (StringUtils.isNotBlank(request.getBudgetSurplusMin())) {
                budgetAnalysisQueryRequest.setBudgetSurplusMin(new BigDecimal(request.getBudgetSurplusMin()));
            }
            if (StringUtils.isNotBlank(request.getBudgetSurplusMax())) {
                budgetAnalysisQueryRequest.setBudgetSurplusMax(new BigDecimal(request.getBudgetSurplusMax()));
            }
            if (StringUtils.isNotBlank(request.getResidualBudgetMin())) {
                budgetAnalysisQueryRequest.setResidualBudgetMin(new BigDecimal(request.getResidualBudgetMin()));
            }
            if (StringUtils.isNotBlank(request.getResidualBudgetMax())) {
                budgetAnalysisQueryRequest.setResidualBudgetMax(new BigDecimal(request.getResidualBudgetMax()));
            }
        }
        return budgetAnalysisQueryRequest;
    }
}
