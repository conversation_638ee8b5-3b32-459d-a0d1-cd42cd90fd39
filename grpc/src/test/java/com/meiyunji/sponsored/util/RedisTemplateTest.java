package com.meiyunji.sponsored.util;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.meiyunji.sponsored.service.autoRule.dao.IAdAutoRuleTemplateDisabledDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import redis.clients.jedis.JedisPoolConfig;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

@SpringBootTest
@Slf4j
public class RedisTemplateTest {

    public static void main(String[] args) {
        RedisTemplate redisTemplate = getRedisTemplate();
        Set<String> strings = scanKeys(redisTemplate, "AUTO_RULE_DISABLED_TEMPLATE_KEY*");
        redisTemplate.delete("AUTO_RULE_DISABLED_TEMPLATE:100");
        strings.forEach(System.out::println);
    }

    @Autowired
    private IAdAutoRuleTemplateDisabledDao adAutoRuleTemplateDisabledDao;

    @Test
    public void test1() {
        int puid = 1009;
        Long templateId = 557535L;
        boolean result = false;
        String key = String.format("AUTO_RULE_DISABLED_TEMPLATE:%s", puid);
        RedisTemplate redisTemplate2 = getRedisTemplate();
        Boolean keyExists = redisTemplate2.hasKey(key);
        if (keyExists != null && keyExists) {
            result = redisTemplate2.opsForSet().isMember(key, templateId);
        } else {
            List<Long> templateIdList = adAutoRuleTemplateDisabledDao.distinctTemplateId(puid);
            //加入缓存
            if (CollectionUtils.isEmpty(templateIdList)) {
                templateIdList = new ArrayList<>();
                templateIdList.add(-1L);
            } else {
                result = templateIdList.contains(templateId);
            }
            //使用Lua脚本保证添加缓存和设置过期时间的原子性
            String script = "redis.call('SADD', KEYS[1], unpack(ARGV)) " +
                    "redis.call('EXPIRE', KEYS[1], 1800) " +
                    "return 1";
            redisTemplate2.execute(new DefaultRedisScript<>(script, Long.class), Collections.singletonList(key), templateIdList.toArray());
        }

        System.out.printf("result:%s\n", result);
    }


    public static Set<String> scanKeys(RedisTemplate redisTemplate, String pattern) {
        Set<String> keys = new HashSet<>();
        try (Cursor<byte[]> cursor = redisTemplate.getConnectionFactory().getConnection()
                .scan(ScanOptions.scanOptions().match(pattern).count(100).build())) {
            while (cursor.hasNext()) {
                keys.add(new String(cursor.next(), StandardCharsets.UTF_8));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return keys;
    }


    private static RedisTemplate getRedisTemplate() {
        // 创建 JedisPoolConfig 并进行配置
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        // 设置最大连接数
        poolConfig.setMaxTotal(100);
        // 设置最大空闲连接数
        poolConfig.setMaxIdle(10);
        // 设置最小空闲连接数
        poolConfig.setMinIdle(5);
        // 设置连接超时时间
        poolConfig.setMaxWaitMillis(3000);
        // 配置 Redis 连接信息
        JedisConnectionFactory redisConfig = new JedisConnectionFactory(poolConfig);
        redisConfig.setHostName("redis-dev.meiyunji.net");
        redisConfig.setPort(6379);
        redisConfig.setDatabase(0);
        redisConfig.setPassword("dxm!123@321#");
        redisConfig.setTimeout(3000);
        RedisSerializer<Object> serializer = redisSerializer();
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisConfig);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(serializer);
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(serializer);
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    private static RedisSerializer<Object> redisSerializer() {
        //创建JSON序列化器
        Jackson2JsonRedisSerializer<Object> serializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        //必须设置，否则无法将JSON转化为对象，会转化成Map类型
        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance,ObjectMapper.DefaultTyping.NON_FINAL);
        serializer.setObjectMapper(objectMapper);
        return serializer;
    }
}
