package com.meiyunji.sponsored.api.common;

import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.cpc.dao.IAdPlacementAmazonBusinessReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.cpc.po.AdPlacementAmazonBusinessReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2025-04-17 09:52
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class AmazonBusinessTest {

    @Resource
    private IAdPlacementAmazonBusinessReportDao adPlacementAmazonBusinessReportDao;
    @Resource
    private IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao;

    @Test
    public void testInsert() {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", 100)
                .notEqualTo("placement", "all");
        List<AmazonAdCampaignAllReport> amazonAdCampaignAllReports = amazonAdCampaignAllReportDao.listByCondition(100, conditionBuilder.build());
        List<AdPlacementAmazonBusinessReport> adPlacementAmazonBusinessReports = adPlacementAmazonBusinessReportDao.listByCondition(100, conditionBuilder.build());
        for (AmazonAdCampaignAllReport amazonAdCampaignAllReport : amazonAdCampaignAllReports) {
            AdPlacementAmazonBusinessReport adPlacementAmazonBusinessReport = new AdPlacementAmazonBusinessReport();
            BeanUtils.copyProperties(amazonAdCampaignAllReport, adPlacementAmazonBusinessReport);
            adPlacementAmazonBusinessReports.add(adPlacementAmazonBusinessReport);
        }
        adPlacementAmazonBusinessReportDao.insertOrUpdateList(100, adPlacementAmazonBusinessReports);
    }
}
