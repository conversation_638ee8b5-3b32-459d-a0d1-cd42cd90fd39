package com.meiyunji.sponsored.ApplicationTest;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.cron.CronjobService;
import com.meiyunji.sponsored.cron.service.ISyncAmazonAdDataService;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.adCampaign.enums.AdSyncRecord;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchConstants;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbAdsApiService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdTargetingApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdSyncService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcAdGroupApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCampaignApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcNeKeywordsApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcTargetingApiService;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.GZipUtils;
import com.meiyunji.sponsored.service.localization.dao.IAmazonKeywordLocalizationScheduleDao;
import com.meiyunji.sponsored.service.localization.po.AmazonKeywordLocalizationSchedule;
import com.meiyunji.sponsored.service.localization.service.KeywordLocalizationScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.MurmurHash3;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2022/3/17 14:03
 * @describe:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TaskApplicationTest {

    @Autowired
    private KeywordLocalizationScheduleService keywordLocalizationScheduleService;

    @Autowired
    private IAmazonKeywordLocalizationScheduleDao amazonKeywordLocalizationScheduleDao;

    @Autowired
    private CosBucketClient dataBucketClient;

    @Autowired
    private IShopAuthDao shopAuthDao;
    @Autowired
    private CpcSbAdsApiService sbAdsApiService;
    @Autowired
    private ICpcAdSyncService cpcAdSyncService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IUserDao userDao;
    @Autowired
    private CronjobService cronjobService;
    @Autowired
    private ICpcAdSyncService iCpcAdSyncService;

    @Autowired
    private CpcNeKeywordsApiService cpcNeKeywordsApiService;

    @Autowired
    private CpcCampaignApiService cpcCampaignApiService;

    @Autowired
    private CpcApiHelper cpcApiHelper;

    @Autowired
    private ISyncAmazonAdDataService iSyncAmazonAdDataService;

    @Autowired
    private CpcAdGroupApiService cpcAdGroupApiService;
    @Autowired
    private CpcSdTargetingApiService cpcSdTargetingApiService;
    @Autowired
    private CpcTargetingApiService cpcTargetingApiService;
    @Test
    public void publisherSync() throws InterruptedException {
        cronjobService.cpcSbSync(0,1,100,4636, null,null);
        Thread.sleep(10000000000L);
    }

    @Test
    public void syncAttributionReport() throws IOException {
        cronjobService.deleteMonitor();
    }
    @Test
    public void localization() {
        while(true) {
            Integer index = 0;
            Integer total = 2;
            List<AmazonKeywordLocalizationSchedule> needsSyncSchedules =
                    amazonKeywordLocalizationScheduleDao.getNeedsSyncSchedules();

            List<AmazonKeywordLocalizationSchedule> shardSchedules = needsSyncSchedules.stream()
                    .filter(item -> Math.abs(MurmurHash3.hash32(item.getId())
                            % total) == index).collect(Collectors.toList());
            keywordLocalizationScheduleService.execute(shardSchedules);
        }
    }

    @Test
    public void download() {
        try {
            byte[] objectToBytes = dataBucketClient.getObjectToBytes("NorthAmerica/A3EKSF9KVNWOZ7/USA/reportV3/sp_ad_product/2025-04-04_2025-04-07.json.gz");
            byte[] bytes = GZipUtils.decompressData(objectToBytes);

            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(bytes);
            // 1. 创建工作簿和表
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("数据");

            // 2. 提取表头
            List<String> headers = new ArrayList<>();
            if (root.isArray() && root.size() > 0) {
                root.get(0).fieldNames().forEachRemaining(headers::add);
            }

            // 3. 写入表头
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < headers.size(); i++) {
                headerRow.createCell(i).setCellValue(headers.get(i));
            }

            // 4. 写入数据行
            int rowNum = 1;
            for (JsonNode node : root) {
                Row row = sheet.createRow(rowNum++);
                for (int i = 0; i < headers.size(); i++) {
                    row.createCell(i).setCellValue(node.get(headers.get(i)).asText());
                }
            }

            // 5. 保存 Excel 文件
            try (FileOutputStream fos = new FileOutputStream("output.xlsx")) {
                workbook.write(fos);
            }
            System.out.println(1);
        } catch (IOException ioException) {
            ioException.printStackTrace();
        }
    }

    @Test
    public void aggregationKeywordsData() {
        List<Integer> puidList = amazonAdProfileDao.getAllPuid();
        for (Integer puid : puidList) {
            cpcAdSyncService.aggregationKeywordsData(puid);
        }
    }

    @Test
    public void syncCampaignNeKeyword() {
        List<ShopAuth> shopAuths = shopAuthDao.getAllValidAdShopByLimit(100, 4636, 0, 100);
        iCpcAdSyncService.syncSbByShop(shopAuths.get(0),null,"3");
    }

    @Test
    public void syncCampaign() {
        List<ShopAuth> shopAuthList = shopAuthDao.getAllValidAdShopByLimit(100, 4636, 0, 100);
        for (ShopAuth shopAuth : shopAuthList) {
            cpcCampaignApiService.syncCampaigns(shopAuth, "135775188839540", false);
        }
    }

    @Test
    public void syncShopSp() throws InterruptedException {
        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getAllShopPoolExecutor();
        int start = 0;
        int shopLimit = 1000;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = shopAuthDao.getAllValidAdShopByLimit(null, null, start, shopLimit);
            int size = shopAuths.size();
            for(ShopAuth shopAuth : shopAuths) {
                threadExecutor.execute(() -> {
                    try {
                        iSyncAmazonAdDataService.syncAmazonSp(shopAuth, AdSyncRecord.TriggerChannelEnum.SCHEDULE_TRIGGER.getChannel());
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
        ThreadPoolUtil.waitingFinish(threadExecutor);
    }

    @Test
    public void syncShopSb() throws InterruptedException {
        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getAllShopPoolExecutor();
        int start = 0;
        int shopLimit = 1000;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = shopAuthDao.getAllValidAdShopByLimit(null, null, start, shopLimit);
            int size = shopAuths.size();
            for(ShopAuth shopAuth : shopAuths) {
                threadExecutor.execute(() -> {
                    try {
                        iSyncAmazonAdDataService.syncAmazonSb(shopAuth, AdSyncRecord.TriggerChannelEnum.SCHEDULE_TRIGGER.getChannel());
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
        ThreadPoolUtil.waitingFinish(threadExecutor);
    }

    @Test
    public void syncShopSd() throws InterruptedException {
        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getAllShopPoolExecutor();
        int start = 0;
        int shopLimit = 1000;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = shopAuthDao.getAllValidAdShopByLimit(null, null, start, shopLimit);
            int size = shopAuths.size();
            for(ShopAuth shopAuth : shopAuths) {
                threadExecutor.execute(() -> {
                    try {
                        iSyncAmazonAdDataService.syncAmazonSd(shopAuth, AdSyncRecord.TriggerChannelEnum.SCHEDULE_TRIGGER.getChannel());
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
        ThreadPoolUtil.waitingFinish(threadExecutor);
    }

    @Test
    public void exponentialBackOff() {
        int i = 0;
        while (i < 10) {
            int sec = AmazonAdUtils.exponentialBackOffSec(i);
            i++;
        }
    }

    @Test
    public void detectAdType() throws InterruptedException{
        cronjobService.detectAdType(0,1,1000);
    }

    @Test
    public void syncAdGroup() {
        List<ShopAuth> shopAuthList = shopAuthDao.getAllValidAdShopByLimit(100, 4636, 0, 100);
        for (ShopAuth shopAuth : shopAuthList) {
//            cpcCampaignApiService.syncCampaigns(shopAuth, null, false);
//            cpcAdGroupApiService.syncAdGroups(shopAuth, null, null);
            cpcTargetingApiService.syncTargetings(shopAuth, null, null, null);
        }
    }

    @Test
    public void sdTargeting() {
        List<ShopAuth> shopAuthList = shopAuthDao.getAllValidAdShopByLimit(100, 4636, 0, 100);
        for (ShopAuth shopAuth : shopAuthList) {
            cpcSdTargetingApiService.syncTargetings(shopAuth, null);
        }
    }
}
