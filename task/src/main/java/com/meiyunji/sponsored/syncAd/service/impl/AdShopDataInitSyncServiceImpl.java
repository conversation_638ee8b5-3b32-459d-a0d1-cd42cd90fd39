package com.meiyunji.sponsored.syncAd.service.impl;

import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdSyncService;
import com.meiyunji.sponsored.service.enums.ShopAdStatusEnum;
import com.meiyunji.sponsored.service.post.service.IPostService;
import com.meiyunji.sponsored.service.syncAd.cache.TaskSyncCache;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataInitRecordStateEnum;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncAdTypeEnum;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncConstant;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncTaskTypeEnum;
import com.meiyunji.sponsored.service.syncAd.po.AmazonAdShopDataInitRecord;
import com.meiyunji.sponsored.service.syncAd.po.AmazonAdShopDataInitTask;
import com.meiyunji.sponsored.service.syncAd.service.IAmazonAdShopDataInitRecordService;
import com.meiyunji.sponsored.service.syncAd.service.IAmazonAdShopDataInitTaskService;
import com.meiyunji.sponsored.service.syncAd.task.init.AdShopDataSyncTask;
import com.meiyunji.sponsored.service.util.WxNotificationUtil;
import com.meiyunji.sponsored.syncAd.service.IAdShopDataInitSyncService;
import com.meiyunji.sponsored.util.TaskThreadPoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2024-01-29  19:19
 */

@Service
@Slf4j
public class AdShopDataInitSyncServiceImpl implements IAdShopDataInitSyncService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;

    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;

    @Autowired
    private IAmazonAdShopDataInitRecordService amazonAdShopDataInitRecordService;

    @Autowired
    private IAmazonAdShopDataInitTaskService amazonAdShopDataInitTaskService;

    @Autowired
    private Map<String, AdShopDataSyncTask> syncTaskBeanMap;

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Autowired
    private ICpcAdSyncService cpcAdSyncService;

    @Resource
    private IPostService postService;

    private final Map<ShopDataSyncAdTypeEnum, ThreadPoolExecutor> threadPoolExecutorMap = new HashMap<ShopDataSyncAdTypeEnum, ThreadPoolExecutor>() {{
        put(ShopDataSyncAdTypeEnum.sp, TaskThreadPoolUtil.getShopInitSpDataPool());
        put(ShopDataSyncAdTypeEnum.sb, TaskThreadPoolUtil.getShopInitSbDataPool());
        put(ShopDataSyncAdTypeEnum.sd, TaskThreadPoolUtil.getShopInitSdDataPool());
    }};

    private List<Byte> groupLevelTaskTypeList = ShopDataSyncTaskTypeEnum.groupLevelTask.stream().map(ShopDataSyncTaskTypeEnum::getType).collect(Collectors.toList());

    /**
     * 授权初始化数据同步-触发
     *
     * @param shopIdList
     */
    @Override
    public void initSync(List<Integer> shopIdList) {
        //过滤未进行广告授权的店铺
        List<ShopAuth> shopAuthList = shopAuthDao.getScAndVcShopListByShopIdsAndAdStatus(shopIdList, null);
        if (CollectionUtils.isEmpty(shopAuthList)) {
            log.warn("ad shop data init start, All shops are not authorized shopIds:{}", shopIdList);
            return;
        }
        //同步所有已进行广告授权店铺广告数据
        for (ShopAuth shop : shopAuthList) {
            //过滤未授权广告记录
            AmazonAdProfile profile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
            if (Objects.isNull(profile)) {
                log.warn("ad shop data init start, profile is null, puid:{}, shopId:{}", shop.getPuid(), shop.getId());
                continue;
            }
            //过滤规定时间内已同步过数据的店铺
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllDao.getByLimitTime(shop.getPuid(), shop.getId(), dynamicRefreshConfiguration.getTimeLimitForShopSyncSeconds());
            if (Objects.nonNull(amazonAdCampaignAll)) {
                log.info("ad shop data init start, it has been synchronized in the last {} seconds, puid:{}, shopId:{}",dynamicRefreshConfiguration.getTimeLimitForShopSyncSeconds(), shop.getPuid(), shop.getId());
                continue;
            }
            //开始同步
            initSingleShopAdData(shop, profile);
        }

    }

    /**
     * 同步单个店铺数据
     *
     * @param shop
     */
    void initSingleShopAdData(ShopAuth shop, AmazonAdProfile profile) {
        Integer shopId = shop.getId();
        log.info("single ad shop data init: start, puid:{}, shopId:{}", shop.getPuid(), shopId);
        //判断是否走新逻辑
        boolean flag = false;
        if (dynamicRefreshConfiguration.isSyncAdInitEnable()) {
            //判断测试
            if (CollectionUtils.isNotEmpty(dynamicRefreshConfiguration.getSyncAdInitGrayPuidSet()) && dynamicRefreshConfiguration.getSyncAdInitGrayPuidSet().contains(shop.getPuid())) {
                flag = true;
            } else {
                if (dynamicRefreshConfiguration.getSyncAdInitGrayPercent() == 100) {
                    flag = true;
                } else if (dynamicRefreshConfiguration.getSyncAdInitGrayPercent() > 0) {
                    if ((shopId % 100) < dynamicRefreshConfiguration.getSyncAdInitGrayPercent()) {
                        flag = true;
                    }
                }
            }
        }
        if (flag) {
            //同步中、同步成功和同步失败-成功率高的数据进行拦截
            AmazonAdShopDataInitRecord shopDataInitRecord = amazonAdShopDataInitRecordService.getRecordByShopId(shopId);
            if (Objects.nonNull(shopDataInitRecord) && !ShopDataInitRecordStateEnum.FAIL_LOW_SUCCESS.getState().equals(shopDataInitRecord.getState())) {
                log.info("single ad shop data init: have been synced, puid:{}, shopId:{}", shop.getPuid(), shopId);
                return;
            }
            //发送通知
            sendSyncStartMessage(shop.getPuid(), shopId);

            //保存店铺初始化记录
            amazonAdShopDataInitRecordService.saveInitRecord(shop, ShopDataInitRecordStateEnum.SYNCING, null);

            //保存店铺级别任务
            amazonAdShopDataInitTaskService.saveAllInitTask(shop);

            //提交任务
            for (ShopDataSyncAdTypeEnum adTypeEnum : ShopDataSyncAdTypeEnum.values()) {
                CompletableFuture.runAsync(() -> doShopDataInitSync(shop, profile, adTypeEnum), TaskThreadPoolUtil.getDoShopDataInit()).exceptionally(e -> {
                    log.error("single ad shop data init: execute error, adType: {}, puid: {}, shopId: {} ", adTypeEnum.getAdType(), shop.getPuid(), shopId, e);
                    return null;
                });
            }
        } else {
            //发送通知
            sendSyncStartMessage(shop.getPuid(), shopId);

            //原来的逻辑
            CompletableFuture.runAsync(() -> cpcAdSyncService.syncSpByShop(shop, null, null), TaskThreadPoolUtil.getShopInitSpDataPool()).exceptionally(e -> {
                log.error("shop data init sync old logic error, sp, puid: {}, shopId: {}", shop.getPuid(), shopId, e);
                return null;
            });
            CompletableFuture.runAsync(() -> cpcAdSyncService.syncSbByShop(shop, null, null), TaskThreadPoolUtil.getShopInitSbDataPool()).exceptionally(e -> {
                log.error("shop data init sync old logic error, sb, puid: {}, shopId: {}", shop.getPuid(), shopId, e);
                return null;
            });
            CompletableFuture.runAsync(() -> cpcAdSyncService.syncSdByShop(shop, null, null), TaskThreadPoolUtil.getShopInitSdDataPool()).exceptionally(e -> {
                log.error("shop data init sync old logic error, sd, puid: {}, shopId: {}", shop.getPuid(), shopId, e);
                return null;
            });
        }
//        CompletableFuture.runAsync(() -> {
//            postService.syncUserPostProfiles(0, 1, Collections.singletonList(shop.getPuid()));
//            postService.syncUserPosts(0, 1, Collections.singletonList(shop.getPuid()));
//            postService.syncPostReportAllData(0, 1, shop.getPuid(), shopId, null);
//        }, TaskThreadPoolUtil.getShopInitPostDataPool()).exceptionally(e -> {
//            log.error("shop post data init sync old logic error, puid: {}, shopId: {}", shop.getPuid(), shopId, e);
//            return null;
//        });
    }

    /**
     * 授权初始化数据同步-执行
     *
     * @param shop       店铺
     * @param profile
     * @param adTypeEnum
     */
    @Override
    public void doShopDataInitSync(ShopAuth shop, AmazonAdProfile profile, ShopDataSyncAdTypeEnum adTypeEnum) {
        int shopId = shop.getId();
        //准备sp，sb，sd数据
        String adType = adTypeEnum.getAdType();
        ThreadPoolExecutor pool = threadPoolExecutorMap.get(adTypeEnum);
        List<ShopDataSyncTaskTypeEnum> shopLevelTaskList;

        if (ShopDataSyncAdTypeEnum.sp == adTypeEnum) {
            shopLevelTaskList = ShopDataSyncTaskTypeEnum.spShopLevelTask;
        } else if (ShopDataSyncAdTypeEnum.sb == adTypeEnum) {
            shopLevelTaskList = ShopDataSyncTaskTypeEnum.sbShopLevelTask;
        } else {
            shopLevelTaskList = ShopDataSyncTaskTypeEnum.sdShopLevelTask;
        }

        //店铺级任务
        Map<ShopDataSyncTaskTypeEnum, CompletableFuture<Void>> futureMap = new HashMap<>();
        for (ShopDataSyncTaskTypeEnum taskType : shopLevelTaskList) {
            String cacheValue = String.format(ShopDataSyncConstant.DATA_SYNC_CACHE, shop.getId(), adTypeEnum.getAdType(), taskType.getType(), 0);
            TaskSyncCache.put(cacheValue);
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> syncTaskBeanMap.get(adType + taskType.getCode()).sync(shop, profile, null), pool);
            futureMap.put(taskType, future);
        }

        //等待组任务完成，一个店铺较多的是5、6w个组，也就10来页就能拿完
        CompletableFuture<Void> future = futureMap.get(ShopDataSyncTaskTypeEnum.GROUP);
        try {
            future.get();
            log.info("shop data init sync get group future success, adtype: {}, puid: {}, shopId: {} ", adType, shop.getPuid(), shopId);
        } catch (Exception e) {
            log.error("shop data init sync get group future error, adtype: {}, puid: {}, shopId: {} ", adType, shop.getPuid(), shopId, e);
        }

        //如果组任务超过一定数量，则交给组重试定时任务去执行，定时任务分片跑
        int count = amazonAdShopDataInitTaskService.countGroupLevelTask(shop.getPuid(), shopId, Collections.singletonList(adType), groupLevelTaskTypeList);
        log.info("shop data init sync trigger group count, adtype: {}, puid: {}, shopId: {}, count: {}", adType, shop.getPuid(), shopId, count);
        if (count >= dynamicRefreshConfiguration.getWaitScheduleHandleCount()) {
            return;
        }

        //组任务，从数据库查询task并触发同步
        int limit = 1000;
        Long maxId = 0L;
        List<Byte> taskTypeList = ShopDataSyncTaskTypeEnum.groupLevelTask.stream().map(ShopDataSyncTaskTypeEnum::getType).collect(Collectors.toList());
        while (true) {
            //查询待执行的任务
            List<AmazonAdShopDataInitTask> taskList = amazonAdShopDataInitTaskService.queryRetryTaskByTaskTypeWithLimit(shop.getPuid(), shopId, Collections.singletonList(adType), taskTypeList, maxId, limit);

            //执行任务
            for (AmazonAdShopDataInitTask task : taskList) {
                //缓冲，不要往线程池添加太多任务
                if (pool.getQueue().size() >= ShopDataSyncConstant.threadpoolTaskQueueWaitLimit) {
                    continue;
                }
                //提交任务
                String cacheValue = String.format(ShopDataSyncConstant.DATA_SYNC_CACHE, task.getShopId(), task.getAdType(), task.getTaskType(), task.getAdGroupId());
                TaskSyncCache.put(cacheValue);
                CompletableFuture.runAsync(() -> {
                    ShopDataSyncTaskTypeEnum taskTypeEnum = ShopDataSyncTaskTypeEnum.typeMap.get(task.getTaskType());
                    syncTaskBeanMap.get(adType + taskTypeEnum.getCode()).sync(shop, profile, task);
                }, pool);
            }

            if (CollectionUtils.isEmpty(taskList) || taskList.size() < limit) {
                break;
            }
            //下一页
            maxId = taskList.get(taskList.size() - 1).getId();
        }
    }

    /**
     * 授权初始化数据同步-重试店铺级任务
     *
     * @param index
     * @param total
     * @param shopIdList
     */
    @Override
    public void doShopDataInitSyncRetry4ShopLevel(int index, int total, List<Integer> shopIdList) {

        //查询所有店铺级任务
        int limit = 1000;
        long maxId = 0;

        while (true) {
            List<AmazonAdShopDataInitRecord> recordList = amazonAdShopDataInitRecordService.getRecordByState(shopIdList, limit, maxId);
            recordList.forEach(x -> {
                if (x.getShopId() % total == index) {
                    doShopDataInitSyncRetry4ShopLevelOneShop(x);
                }
            });
            if (CollectionUtils.isEmpty(recordList) || recordList.size() < limit) {
                break;
            }
            maxId = recordList.get(recordList.size() - 1).getId();
        }

    }

    @Override
    public void shopDataInitSyncRetry4GroupLevel(int index, int total, List<Integer> shopIdList, List<String> groupIdList) {

        //查询组任务
        int limit = 1000;
        Long maxId = 0L;

        while (true) {
            //查询组级任务
            List<AmazonAdShopDataInitTask> allRetryTaskList = amazonAdShopDataInitTaskService.queryRetryGroupLevelTaskLimit(shopIdList, groupIdList, ShopDataSyncAdTypeEnum.adTypeList, groupLevelTaskTypeList, maxId, limit);
            if (CollectionUtils.isEmpty(allRetryTaskList)) {
                log.info("shopDataInitSyncRetry4GroupLevel, maxId:{}, limit:{}, is empty", maxId, limit);
                return;
            }
            log.info("shopDataInitSyncRetry4GroupLevel, all task size:{}", allRetryTaskList.size());

            //当前分片处理的数据
            List<AmazonAdShopDataInitTask> handleTaskList = new ArrayList<>();
            Set<Integer> handleShopIdSet = new HashSet<>();
            allRetryTaskList.forEach(x -> {
                if (Math.abs(x.getAdGroupId().hashCode() % total) == index) {
                    handleTaskList.add(x);
                    handleShopIdSet.add(x.getShopId());
                }
            });

            log.info("shopDataInitSyncRetry4GroupLevel, shard task size:{}", handleTaskList.size());

            if (CollectionUtils.isNotEmpty(handleTaskList)) {
                //批量查询出shop和profile
                List<Integer> handleShopIdList = new ArrayList<>(handleShopIdSet);
                List<ShopAuth> shopAuthList = shopAuthDao.getScAndVcByIds(handleShopIdList);
                List<AmazonAdProfile> profileList = amazonAdProfileDao.getProfilesByShopIds(handleShopIdList);
                //转map
                Map<Integer, List<ShopAuth>> shopAuthMap = shopAuthList.stream().collect(Collectors.groupingBy(ShopAuth::getId));
                Map<Integer, List<AmazonAdProfile>> profileMap = profileList.stream().collect(Collectors.groupingBy(AmazonAdProfile::getShopId));

                //提交线程池重试
                for (AmazonAdShopDataInitTask task : handleTaskList) {
                    //取得数据
                    ShopAuth shop = shopAuthMap.get(task.getShopId()).stream().findFirst().orElse(null);
                    AmazonAdProfile profile = profileMap.get(task.getShopId()).stream().findFirst().orElse(null);
                    if (Objects.isNull(shop) || Objects.isNull(profile)) {
                        log.error("shopDataInitSyncRetry4GroupLevel, shop or profile is null, puid:{}, shopId:{}", task.getPuid(), task.getShopId());
                        amazonAdShopDataInitTaskService.deleteByShopId(task.getPuid(), task.getShopId());
                        continue;
                    }
                    String cacheValue = String.format(ShopDataSyncConstant.DATA_SYNC_CACHE, task.getShopId(), task.getAdType(), task.getTaskType(), task.getAdGroupId());
                    if (TaskSyncCache.contains(cacheValue)) {
                        log.info("shopDataInitSyncRetry4GroupLevel, syncing:{}", cacheValue);
                        continue;
                    }
                    //对应的线程池
                    ThreadPoolExecutor pool = threadPoolExecutorMap.get(ShopDataSyncAdTypeEnum.adTypeMap.get(task.getAdType()));
                    //缓冲，不要往线程池添加太多任务
                    if (pool.getQueue().size() >= ShopDataSyncConstant.threadpoolTaskQueueWaitLimit) {
                        log.warn("shopDataInitSyncRetry4GroupLevel, the number of tasks reached the maximum:{}", cacheValue);
                        continue;
                    }
                    ShopDataSyncTaskTypeEnum taskTypeEnum = ShopDataSyncTaskTypeEnum.typeMap.get(task.getTaskType());
                    //提交任务
                    TaskSyncCache.put(cacheValue);
                    CompletableFuture.runAsync(() -> syncTaskBeanMap.get(task.getAdType() + taskTypeEnum.getCode()).sync(shop, profile, task), pool);
                }
            }

            if (allRetryTaskList.size() < limit) {
                return;
            }

            //下一页
            maxId = allRetryTaskList.get(allRetryTaskList.size() - 1).getId();
        }
    }

    @Override
    public int deleteShopDataInitHistoryData(List<Integer> shopIdList, int deleteTimeLimitSecond, int onceDelMaxCount) {
        return amazonAdShopDataInitTaskService.deleteHistoryTasks(shopIdList, deleteTimeLimitSecond, onceDelMaxCount);
    }

    /**
     * 授权初始化数据同步-重试某个店铺的店铺级任务
     *
     * @param record
     */
    private void doShopDataInitSyncRetry4ShopLevelOneShop(AmazonAdShopDataInitRecord record) {
        //查询shop
        ShopAuth shop = shopAuthDao.getScAndVcById(record.getShopId());
        if (Objects.isNull(shop)) {
            log.error("shopDataInitSyncRetry4GroupLevel, shop or profile is null, puid:{}, shopId:{}", record.getPuid(), record.getShopId());
            amazonAdShopDataInitRecordService.deleteByShopId(record.getPuid(), record.getShopId());
            amazonAdShopDataInitTaskService.deleteByShopId(record.getPuid(), record.getShopId());
            return;
        }
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (ShopAdStatusEnum.UNAUTH.getName().equals(shop.getAdStatus()) || Objects.isNull(profile)) {
            log.error("shopDataInitSyncRetry4GroupLevel, shop or profile is null, puid:{}, shopId:{}", record.getPuid(), record.getShopId());
            amazonAdShopDataInitRecordService.deleteByShopId(record.getPuid(), record.getShopId());
            amazonAdShopDataInitTaskService.deleteByShopId(record.getPuid(), record.getShopId());
            return;
        }

        //查询同步中的店铺级任务-8个
        List<AmazonAdShopDataInitTask> retryShopLevelTaskList = amazonAdShopDataInitTaskService.queryRetryTaskByTaskTypeWithLimit(record.getPuid(), record.getShopId(), ShopDataSyncAdTypeEnum.adTypeList, ShopDataSyncTaskTypeEnum.shopLevelTaskType, 0L, 1000);

        //店铺级任务同步完成，检查组级任务并更新店铺同步记录状态
        if (CollectionUtils.isEmpty(retryShopLevelTaskList)) {
            amazonAdShopDataInitRecordService.checkAndUpdateShopRecordStatus(record);
            return;
        }

        //还未同步完成的店铺级任务交给线程池重试
        for (AmazonAdShopDataInitTask task : retryShopLevelTaskList) {
            String cacheValue = String.format(ShopDataSyncConstant.DATA_SYNC_CACHE, task.getShopId(), task.getAdType(), task.getTaskType(), 0);
            if (TaskSyncCache.contains(cacheValue)) {
                log.info("shopDataInitSyncRetry4ShopLevel, syncing:{}", cacheValue);
                continue;
            }
            ThreadPoolExecutor pool = threadPoolExecutorMap.get(ShopDataSyncAdTypeEnum.adTypeMap.get(task.getAdType()));
            //缓冲，不要往线程池添加太多任务
            if (pool.getQueue().size() >= ShopDataSyncConstant.threadpoolTaskQueueWaitLimit) {
                log.warn("shopDataInitSyncRetry4ShopLevel, the number of tasks reached the maximum:{}", cacheValue);
                continue;
            }
            //异步执行店铺级任务
            TaskSyncCache.put(cacheValue);
            ShopDataSyncTaskTypeEnum taskTypeEnum = ShopDataSyncTaskTypeEnum.typeMap.get(task.getTaskType());
            CompletableFuture.runAsync(() -> syncTaskBeanMap.get(task.getAdType() + taskTypeEnum.getCode()).sync(shop, profile, task), pool);
        }

    }

    private void sendSyncStartMessage(Integer puid, Integer shopId) {
        //发送告警
        try {
            String content = "店铺授权初始化基础数据预警\n" + String.format("ad shop data init sync start, puid: %s, shopId: %s", puid, shopId); ;
            WxNotificationUtil.sendContent(ShopDataSyncConstant.warningUrl, content);
        } catch (Exception e) {
            log.error("send ad shop data init sync start msg error", e);
        }
    }

}
