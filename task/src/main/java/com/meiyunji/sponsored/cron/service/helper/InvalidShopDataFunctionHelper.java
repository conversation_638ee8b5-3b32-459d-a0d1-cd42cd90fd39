package com.meiyunji.sponsored.cron.service.helper;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleExecuteRecordDao;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDao;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDeleteDao;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleTemplateDao;
import com.meiyunji.sponsored.service.autoRuleTask.dao.AutoRuleTaskDao;
import com.meiyunji.sponsored.service.autoRuleTask.dao.AutoRuleTaskRecordDao;
import com.meiyunji.sponsored.service.batchCreate.dao.*;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.function.ThFunction;
import com.meiyunji.sponsored.service.strategy.dao.*;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyRealTimeBidDao;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyTaskDao;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyTaskRecordDao;
import com.meiyunji.sponsored.service.wordFrequency.dao.*;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiFunction;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2025-04-30  10:52
 */

@Component
public class InvalidShopDataFunctionHelper {

    //单表
    @Autowired
    private IAmazonAdPortfolioDao amazonAdPortfolioDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;
    @Autowired
    private IAmazonSdAdProductDao amazonSdAdProductDao;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Autowired
    private AdvertiseStrategyScheduleDao advertiseStrategyScheduleDao;
    @Autowired
    private AdvertiseStrategyStatusDao advertiseStrategyStatusDao;
    @Autowired
    private AdvertiseStrategyTemplateDao advertiseStrategyTemplateDao;
    @Autowired
    private AdvertiseStrategyStatusDeleteDao advertiseStrategyStatusDeleteDao;
    @Autowired
    private AdvertiseStrategyTopBudgetTemplateDao advertiseStrategyTopBudgetTemplateDao;
    @Autowired
    private AdvertiseStrategyTopBudgetScheduleDao advertiseStrategyTopBudgetScheduleDao;
    @Autowired
    private IAdvertiseAutoRuleExecuteRecordDao advertiseAutoRuleExecuteRecordDao;
    @Autowired
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;
    @Autowired
    private IAdvertiseAutoRuleTemplateDao advertiseAutoRuleTemplateDao;
    @Autowired
    private IAdvertiseAutoRuleStatusDeleteDao advertiseAutoRuleStatusDeleteDao;
    @Autowired
    private IAmazonAdOperationLogDao amazonAdOperationLogDao;
    @Autowired
    private IAmazonAdBatchBaseInfoDao amazonAdBatchBaseInfoDao;
    @Autowired
    private IAmazonAdBatchCampaignDao amazonAdBatchCampaignDao;
    @Autowired
    private IAmazonAdBatchGroupDao amazonAdBatchGroupDao;
    @Autowired
    private IAmazonAdBatchKeywordDao amazonAdBatchKeywordDao;
    @Autowired
    private IAmazonAdBatchNekeywordDao amazonAdBatchNekeywordDao;
    @Autowired
    private IAmazonAdBatchNetargetingDao amazonAdBatchNetargetingDao;
    @Autowired
    private IAmazonAdBatchProductDao amazonAdBatchProductDao;
    @Autowired
    private IAmazonAdBatchTargetingDao amazonAdBatchTargetingDao;
    @Autowired
    private IAmazonAdBatchTaskDao amazonAdBatchTaskDao;
    @Autowired
    private ICpcSbQueryKeywordReportDao cpcSbQueryKeywordReportDao;
    @Autowired
    private IAmazonAdSdGroupReportDao amazonAdSdGroupReportDao;
    @Autowired
    private IAmazonAdSbGroupReportDao amazonAdSbGroupReportDao;
    @Autowired
    private IAmazonAdSdProductReportDao amazonAdSdProductReportDao;
    @Autowired
    private IAmazonAdSbTargetingReportDao amazonAdSbTargetingReportDao;
    @Autowired
    private IAmazonAdSdTargetingReportDao amazonAdSdTargetingReportDao;
    @Autowired
    private AutoRuleTaskDao autoRuleTaskDao;
    @Autowired
    private AutoRuleTaskRecordDao autoRuleTaskRecordDao;
    @Autowired
    private AdvertiseStrategyAdGroupDao advertiseStrategyAdGroupDao;
    @Autowired
    private AdvertiseStrategyRealTimeBidDao advertiseStrategyRealTimeBidDao;
    @Autowired
    private AdvertiseStrategyTaskDao advertiseStrategyTaskDao;
    @Autowired
    private AdvertiseStrategyTaskRecordDao advertiseStrategyTaskRecordDao;

    //分表
    @Autowired
    private IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao;
    @Autowired
    private IAmazonAdGroupReportDao amazonAdGroupReportDao;
    @Autowired
    private IAmazonAdProductReportDao amazonAdProductReportDao;
    @Autowired
    private IAmazonAdKeywordReportDao amazonAdKeywordReportDao;
    @Autowired
    private IAmazonAdSbKeywordReportDao amazonAdSbKeywordReportDao;
    @Autowired
    private ICpcTargetingReportDao cpcTargetingReportDao;
    @Autowired
    private ICpcQueryKeywordReportDao cpcQueryKeywordReportDao;
    @Autowired
    private ICpcQueryTargetingReportDao cpcQueryTargetingReportDao;
    @Autowired
    private IWordRootKeywordSbDao wordRootKeywordSbDao;
    @Autowired
    private IWordRootKeywordSpDao wordRootKeywordSpDao;
    @Autowired
    private IWordRootTargetingSpDao wordRootTargetingSpDao;
    @Autowired
    private IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao;
    @Autowired
    private IAmazonAdNeKeywordDao amazonAdNeKeywordDao;
    @Autowired
    private IAmazonAdTargetingShardingDao amazonAdTargetingShardingDao;
    @Autowired
    private IAmazonAdNeTargetingDao amazonAdNeTargetingDao;
    @Autowired
    private IAmazonAdWordRootKeywordSbDao amazonAdWordRootKeywordSbDao;
    @Autowired
    private IAmazonAdWordRootKeywordSpDao amazonAdWordRootKeywordSpDao;
    @Autowired
    private IWordRootQueryDao wordRootQueryDao;

    //doris需要删除的表
    private List<String> dorisDeleteInvalidShopDataTableNameList = Lists.newArrayList(
            "dim_t_amazon_ad_campaign_ne_keyword_before_after_report",
            "dim_t_amazon_ad_campaign_ne_keyword_report",
            "dim_t_amazon_ad_campaign_ne_targeting_before_after_report",
            "dim_t_amazon_ad_campaign_ne_targeting_report",
            "dim_t_amazon_ad_ne_keyword_before_after_report",
            "dim_t_amazon_ad_ne_keyword_report",
            "dim_t_amazon_ad_ne_targeting_before_after_report",
            "dim_t_amazon_ad_ne_targeting_report",
            "dim_t_amazon_ad_sb_ne_keyword_before_after_report",
            "dim_t_amazon_ad_sb_ne_keyword_report",
            "dim_t_amazon_ad_sb_ne_targeting_before_after_report",
            "dim_t_amazon_ad_sb_ne_targeting_report",
            "dim_t_amazon_ad_sd_ne_targeting_before_after_report",
            "dim_t_amazon_ad_sd_ne_targeting_report",
            "dim_t_cpc_query_campaign_ne_keyword_before_after_report",
            "dim_t_cpc_query_campaign_ne_keyword_report",
            "dim_t_cpc_query_campaign_ne_targeting_before_after_report",
            "dim_t_cpc_query_campaign_ne_targeting_report",
            "dim_t_cpc_query_ne_keyword_before_after_report",
            "dim_t_cpc_query_ne_keyword_report",
            "dim_t_cpc_query_ne_targeting_before_after_report",
            "dim_t_cpc_query_ne_targeting_report",
            "dim_t_cpc_sb_query_ne_keyword_before_after_report",
            "dim_t_cpc_sb_query_ne_keyword_report",
            "dws_sale_profit_shop_day",
            "ods_ad_manage_operation_log_amazon",
            "ods_ad_manage_operation_log_auto",
            "ods_ad_manage_operation_log_other",
            "ods_ad_manage_operation_log_sellfox",
            "ods_amc_campaign_info",
            "ods_amc_query_targeting_attributed_report",
            "ods_amc_query_targeting_report",
            "ods_t_amazon_ad_asin_report",
            "ods_t_amazon_ad_asin_report_keyword",
            "ods_t_amazon_ad_campaign_all",
            "ods_t_amazon_ad_campaign_all_report",
            "ods_t_amazon_ad_campaign_nekeywords",
            "ods_t_amazon_ad_campaign_netargeting_sp",
            "ods_t_amazon_ad_campaign_placement_report",
            "ods_t_amazon_ad_group",
            "ods_t_amazon_ad_group_report",
            "ods_t_amazon_ad_group_sb",
            "ods_t_amazon_ad_group_sd",
            "ods_t_amazon_ad_keyword",
            "ods_t_amazon_ad_keyword_report",
            "ods_t_amazon_ad_keyword_sb",
            "ods_t_amazon_ad_ne_keyword",
            "ods_t_amazon_ad_ne_targeting",
            "ods_t_amazon_ad_nekeyword_sb",
            "ods_t_amazon_ad_netargeting_sb",
            "ods_t_amazon_ad_netargeting_sd",
            "ods_t_amazon_ad_portfolio",
            "ods_t_amazon_ad_product",
            "ods_t_amazon_ad_product_report",
            "ods_t_amazon_ad_product_sd",
            "ods_t_amazon_ad_sb_ads_report",
            "ods_t_amazon_ad_sb_group_report",
            "ods_t_amazon_ad_sb_keyword_report",
            "ods_t_amazon_ad_sb_targeting_report",
            "ods_t_amazon_ad_sd_asin_report",
            "ods_t_amazon_ad_sd_group_report",
            "ods_t_amazon_ad_sd_product_report",
            "ods_t_amazon_ad_sd_targeting_report",
            "ods_t_amazon_ad_sp_sd_product_report",
            "ods_t_amazon_ad_targeting",
            "ods_t_amazon_ad_targeting_sb",
            "ods_t_amazon_ad_targeting_sd",
            "ods_t_amazon_ad_word_root_keyword_sb",
            "ods_t_amazon_ad_word_root_keyword_sp",
            "ods_t_amazon_sb_ads",
            "ods_t_amazon_word_root_query",
            "ods_t_cpc_query_keyword_report",
            "ods_t_cpc_query_targeting_report",
            "ods_t_cpc_sb_query_keyword_report",
            "ods_t_cpc_targeting_report",
            "ods_t_amazon_ad_budget_usage",
            "ods_t_amazon_ad_budget_usage_day"
    );


    public List<ThFunction<Integer, Integer, Integer, Integer>> getAllDeleteFunction4Mysql() {
        List<ThFunction<Integer, Integer, Integer, Integer>> list = new ArrayList<>();
        //单表
        list.add(amazonAdPortfolioDao::deleteByPuidAndShopId);
        list.add(amazonAdCampaignAllDao::deleteByPuidAndShopId);
        list.add(amazonAdGroupDao::deleteByPuidAndShopId);
        list.add(amazonSbAdGroupDao::deleteByPuidAndShopId);
        list.add(amazonSdAdGroupDao::deleteByPuidAndShopId);
        list.add(amazonAdProductDao::deleteByPuidAndShopId);
        list.add(amazonSdAdProductDao::deleteByPuidAndShopId);
        list.add(amazonSbAdKeywordDao::deleteByPuidAndShopId);
        list.add(amazonSbAdTargetingDao::deleteByPuidAndShopId);
        list.add(amazonSdAdTargetingDao::deleteByPuidAndShopId);
        list.add(advertiseStrategyScheduleDao::deleteByPuidAndShopId);
        list.add(advertiseStrategyStatusDao::deleteByPuidAndShopId);
        list.add(advertiseStrategyTemplateDao::deleteByPuidAndShopId);
        list.add(advertiseStrategyStatusDeleteDao::deleteByPuidAndShopId);
        list.add(advertiseStrategyTopBudgetTemplateDao::deleteByPuidAndShopId);
        list.add(advertiseStrategyTopBudgetScheduleDao::deleteByPuidAndShopId);
        list.add(advertiseAutoRuleExecuteRecordDao::deleteByPuidAndShopId);
        list.add(advertiseAutoRuleStatusDao::deleteByPuidAndShopId);
        list.add(advertiseAutoRuleTemplateDao::deleteByPuidAndShopId);
        list.add(advertiseAutoRuleStatusDeleteDao::deleteByPuidAndShopId);
        list.add(amazonAdOperationLogDao::deleteByPuidAndShopId);
        list.add(amazonAdBatchBaseInfoDao::deleteByPuidAndShopId);
        list.add(amazonAdBatchCampaignDao::deleteByPuidAndShopId);
        list.add(amazonAdBatchGroupDao::deleteByPuidAndShopId);
        list.add(amazonAdBatchKeywordDao::deleteByPuidAndShopId);
        list.add(amazonAdBatchNekeywordDao::deleteByPuidAndShopId);
        list.add(amazonAdBatchNetargetingDao::deleteByPuidAndShopId);
        list.add(amazonAdBatchProductDao::deleteByPuidAndShopId);
        list.add(amazonAdBatchTargetingDao::deleteByPuidAndShopId);
        list.add(amazonAdBatchTaskDao::deleteByPuidAndShopId);
        list.add(cpcSbQueryKeywordReportDao::deleteByPuidAndShopId);
        list.add(amazonAdSdGroupReportDao::deleteByPuidAndShopId);
        list.add(amazonAdSbGroupReportDao::deleteByPuidAndShopId);
        list.add(amazonAdSdProductReportDao::deleteByPuidAndShopId);
        list.add(amazonAdSbTargetingReportDao::deleteByPuidAndShopId);
        list.add(amazonAdSdTargetingReportDao::deleteByPuidAndShopId);
        list.add(autoRuleTaskDao::deleteByPuidAndShopId);
        list.add(autoRuleTaskRecordDao::deleteByPuidAndShopId);
        list.add(advertiseStrategyAdGroupDao::deleteByPuidAndShopId);
        list.add(advertiseStrategyRealTimeBidDao::deleteByPuidAndShopId);
        list.add(advertiseStrategyTaskDao::deleteByPuidAndShopId);
        list.add(advertiseStrategyTaskRecordDao::deleteByPuidAndShopId);

        //分表
        list.add(amazonAdCampaignAllReportDao::deleteByPuidAndShopId);
        list.add(amazonAdGroupReportDao::deleteByPuidAndShopId);
        list.add(amazonAdProductReportDao::deleteByPuidAndShopId);
        list.add(amazonAdKeywordReportDao::deleteByPuidAndShopId);
        list.add(amazonAdSbKeywordReportDao::deleteByPuidAndShopId);
        list.add(cpcTargetingReportDao::deleteByPuidAndShopId);
        list.add(cpcQueryKeywordReportDao::deleteByPuidAndShopId);
        list.add(cpcQueryTargetingReportDao::deleteByPuidAndShopId);
        //这3张表需要删除，故不清理否则会报错
        /*list.add(wordRootKeywordSbDao::deleteByPuidAndShopId);
        list.add(wordRootKeywordSpDao::deleteByPuidAndShopId);
        list.add(wordRootTargetingSpDao::deleteByPuidAndShopId);*/
        list.add(amazonAdKeywordShardingDao::deleteByPuidAndShopId);
        list.add(amazonAdNeKeywordDao::deleteByPuidAndShopId);
        list.add(amazonAdTargetingShardingDao::deleteByPuidAndShopId);
        list.add(amazonAdNeTargetingDao::deleteByPuidAndShopId);
        list.add(amazonAdWordRootKeywordSbDao::deleteByPuidAndShopId);
        list.add(amazonAdWordRootKeywordSpDao::deleteByPuidAndShopId);
        list.add(wordRootQueryDao::deleteByPuidAndShopId);

        return list;
    }


    public List<Pair<String, BiFunction<Integer, Integer, Integer>>> getAllStatisticsFunction() {
        List<Pair<String, BiFunction<Integer, Integer, Integer>>> functionList = new ArrayList<>();
        functionList.add(Pair.of("t_amazon_ad_portfolio", amazonAdPortfolioDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_campaign_all", amazonAdCampaignAllDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_group", amazonAdGroupDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_group_sb", amazonSbAdGroupDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_group_sd", amazonSdAdGroupDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_product", amazonAdProductDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_product_sd", amazonSdAdProductDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_keyword_sb", amazonSbAdKeywordDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_targeting_sb", amazonSbAdTargetingDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_targeting_sd", amazonSdAdTargetingDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_advertise_strategy_schedule", advertiseStrategyScheduleDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_advertise_strategy_status", advertiseStrategyStatusDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_advertise_strategy_template", advertiseStrategyTemplateDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_advertise_strategy_status_delete", advertiseStrategyStatusDeleteDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_advertise_strategy_top_budget_template", advertiseStrategyTopBudgetTemplateDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_advertise_strategy_top_budget_schedule", advertiseStrategyTopBudgetScheduleDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_ad_auto_rule_execute_record", advertiseAutoRuleExecuteRecordDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_ad_auto_rule_status", advertiseAutoRuleStatusDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_ad_auto_rule_template", advertiseAutoRuleTemplateDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_ad_auto_rule_status_delete", advertiseAutoRuleStatusDeleteDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_operation_log", amazonAdOperationLogDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_batch_baseinfo", amazonAdBatchBaseInfoDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_batch_campaign", amazonAdBatchCampaignDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_batch_group", amazonAdBatchGroupDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_batch_keyword", amazonAdBatchKeywordDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_batch_nekeyword", amazonAdBatchNekeywordDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_batch_netargeting", amazonAdBatchNetargetingDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_batch_product", amazonAdBatchProductDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_batch_targeting", amazonAdBatchTargetingDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_batch_task", amazonAdBatchTaskDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_cpc_sb_query_keyword_report", cpcSbQueryKeywordReportDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_sd_group_report", amazonAdSdGroupReportDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_sb_group_report", amazonAdSbGroupReportDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_sd_product_report", amazonAdSdProductReportDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_sb_targeting_report", amazonAdSbTargetingReportDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_sd_targeting_report", amazonAdSdTargetingReportDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_campaign_all_report", amazonAdCampaignAllReportDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_group_report", amazonAdGroupReportDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_product_report", amazonAdProductReportDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_keyword_report", amazonAdKeywordReportDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_ad_sb_keyword_report", amazonAdSbKeywordReportDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_cpc_targeting_report", cpcTargetingReportDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_cpc_query_keyword_report", cpcQueryKeywordReportDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_cpc_query_targeting_report", cpcQueryTargetingReportDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_word_root_keyword_sb", wordRootKeywordSbDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_word_root_keyword_sp", wordRootKeywordSpDao::getCountByPuidAndShopId));
        functionList.add(Pair.of("t_amazon_word_root_targeting_sp", wordRootTargetingSpDao::getCountByPuidAndShopId));

        return functionList;
    }

    public List<String> getAllDorisDeleteTableNameList() {
        return dorisDeleteInvalidShopDataTableNameList;
    }

}
