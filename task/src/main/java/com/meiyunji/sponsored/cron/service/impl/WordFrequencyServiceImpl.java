package com.meiyunji.sponsored.cron.service.impl;

import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.cron.service.IWordFrequencyService;
import com.meiyunji.sponsored.service.account.dao.ISlaveScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.constants.TaskExecutionRecordTaskType;
import com.meiyunji.sponsored.service.cpc.dao.ITaskExecutionRecordDao;
import com.meiyunji.sponsored.service.cpc.po.TaskExecutionRecord;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.wordFrequency.service.*;
import com.meiyunji.sponsored.service.wordFrequency.service.helper.WordRootCalculateServiceHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: zhulukun
 * @email: <EMAIL>
 * @date: 2023-11-15  20:51
 */
@Service
@Slf4j
public class WordFrequencyServiceImpl implements IWordFrequencyService {

    @Autowired
    private ISlaveScVcShopAuthDao slaveShopAuthDao;

    @Autowired
    private ITaskExecutionRecordDao taskExecutionRecordDao;

    @Autowired
    private IWordRootSpService iWordRootKeywordSpService;

    @Autowired
    private IWordRootSbService iWordRootKeywordSbService;

    @Autowired
    private IWordRootTargetingSpService iWordRootTargetingSpService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    private List<ShopAuth> taskListByTimeAndType(List<ShopAuth> shopAuths, String taskType, int shardIndex, int totalShard, Integer calculateAll) {
        log.info("taskListByTimeAndType taskType: {}, calculateAll: {}, shopAuths: {}", taskType, calculateAll, shopAuths.size());
        List<Integer> shopIdList;
        //1000->250
        List<ShopAuth> shopAuthsShard = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
        log.info("taskListByTimeAndType taskType: {}, calculateAll: {}, shopAuthsShard: {}", taskType, calculateAll, shopAuthsShard.size());
        //获得商铺ID
        List<Integer> shopIds = shopAuthsShard.stream().map(ShopAuth::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shopIds)) {
            return new ArrayList<>();
        }
        if (calculateAll != null && calculateAll == 1) {
            //再根据当前的定时任务类型，从mysql中选择出相应的店铺记录
            List<TaskExecutionRecord> spQueryHandlerRecords = taskExecutionRecordDao.getTaskExecutionRecordByTaskType(shopIds, taskType);
            shopIdList = spQueryHandlerRecords.stream().map(TaskExecutionRecord::getShopId).collect(Collectors.toList());
            log.info("taskListByTimeAndType taskType: {}, shopIdList: {}", taskType, shopIdList.size());
            List<ShopAuth> returnShopIds = shopAuthsShard.stream().filter(s -> !shopIdList.contains(s.getId())).collect(Collectors.toList());
            log.info("taskListByTimeAndType taskType: {}, returnShopIds: {}", taskType, returnShopIds.size());
            return returnShopIds;
        } else {
            return shopAuthsShard;
        }
    }

    @Override
    public void wordFrequencySpQueryHandler(int shardIndex, int totalShard, Integer puid, Integer shopId, Integer calculateAll) {
        int start = 0;
        int shopLimit = 1000;
        String taskType = TaskExecutionRecordTaskType.SPQUERY.getType();
        List<ShopAuth> shopAuths;
        while (true) {
            List<ShopAuth> shopList = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, shopLimit);
            int size = shopList.size();
            //需要进行定时任务的店铺
            shopAuths = this.taskListByTimeAndType(shopList, taskType, shardIndex, totalShard, calculateAll);
            for (ShopAuth shopAuth : shopAuths) {
                try {
                    List<String> openWordRootMarketplaceList = WordRootCalculateServiceHelper.openWordRootMarketplaceList();
                    if (!openWordRootMarketplaceList.contains(shopAuth.getMarketplaceId())) {
                        log.info("wordFrequencySpQueryHandler close wordRootService puid: {}, shopId: {}, marketplace: {}", shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplace());
                        continue;
                    }
                    log.info("wordFrequencySpQueryHandler puid: {}, shopId: {}", shopAuth.getPuid(), shopAuth.getId());
                    if (calculateAll != null && calculateAll == 1) {
                        int page = 1;
                        while (true) {
                            try {
                                boolean result = iWordRootKeywordSpService.wordFrequencyQueryHandler(shopAuth, page);
                                if (result) {
                                    page++;
                                } else {
                                    //记录完成计算的shopId
                                    taskExecutionRecordDao.insertOrUpdateTaskExecutionRecordByTaskType(new TaskExecutionRecord(shopAuth.getPuid(), shopAuth.getId(), taskType));
                                    break;
                                }
                            } catch (Exception e) {
                                log.error("", e);
                            }

                        }
                    } else {
                        iWordRootKeywordSpService.wordFrequencyQueryHandler(shopAuth, null);
                    }
                } catch (Exception e) {
                    log.error("", e);
                }
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
    }

    @Override
    public void wordFrequencySbQueryHandler(int shardIndex, int totalShard, Integer puid, Integer shopId, Integer calculateAll) {
        int start = 0;
        int shopLimit = 1000;
        String taskType = TaskExecutionRecordTaskType.SBQUERY.getType();
        List<ShopAuth> shopAuths;
        while (true) {
            List<ShopAuth> shopList = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, shopLimit);
            int size = shopList.size();
            //需要进行定时任务的店铺
            shopAuths = this.taskListByTimeAndType(shopList, taskType, shardIndex, totalShard, calculateAll);
            for (ShopAuth shopAuth : shopAuths) {
                try {
                    List<String> openWordRootMarketplaceList = WordRootCalculateServiceHelper.openWordRootMarketplaceList();
                    if (!openWordRootMarketplaceList.contains(shopAuth.getMarketplaceId())) {
                        log.info("wordFrequencySbKeywordHandler close wordRootService puid: {}, shopId: {}, marketplace: {}", shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplace());
                        continue;
                    }
                    log.info("wordFrequencySbKeywordHandler puid: {}, shopId: {}", shopAuth.getPuid(), shopAuth.getId());
                    if (calculateAll != null && calculateAll == 1) {
                        int page = 1;
                        while (true) {
                            try {
                                boolean result = iWordRootKeywordSbService.wordFrequencyQueryHandler(shopAuth, page);
                                if (result) {
                                    page++;
                                } else {
                                    //记录完成计算的shopId
                                    taskExecutionRecordDao.insertOrUpdateTaskExecutionRecordByTaskType(new TaskExecutionRecord(shopAuth.getPuid(), shopAuth.getId(), taskType));
                                    break;
                                }
                            } catch (Exception e) {
                                log.error("", e);
                            }

                        }
                    } else {
                        iWordRootKeywordSbService.wordFrequencyQueryHandler(shopAuth, null);
                    }
                } catch (Exception e) {
                    log.error("", e);
                }
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
    }

    @Override
    public void wordFrequencySpTargetingHandler(int shardIndex, int totalShard, Integer puid, Integer shopId, Integer calculateAll) {
        int start = 0;
        int shopLimit = 1000;
        String taskType = TaskExecutionRecordTaskType.SPTARGETING.getType();
        List<ShopAuth> shopAuths;
        while (true) {
            List<ShopAuth> shopList = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, shopLimit);
            int size = shopList.size();
            //需要进行定时任务的店铺
            shopAuths = this.taskListByTimeAndType(shopList, taskType, shardIndex, totalShard, calculateAll);
            for (ShopAuth shopAuth : shopAuths) {
                try {
                    List<String> openWordRootMarketplaceList = WordRootCalculateServiceHelper.openWordRootMarketplaceList();
                    if (!openWordRootMarketplaceList.contains(shopAuth.getMarketplaceId())) {
                        log.info("wordFrequencySpTargetingHandler close wordRootService puid: {}, shopId: {}, marketplace: {}", shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplace());
                        continue;
                    }
                    log.info("wordFrequencySpTargetingHandler puid: {}, shopId: {}", shopAuth.getPuid(), shopAuth.getId());
                    if (calculateAll != null && calculateAll == 1) {
                        int page = 1;
                        while (true) {
                            try {
                                boolean result = iWordRootTargetingSpService.wordFrequencyHandler(shopAuth, page);
                                if (result) {
                                    page++;
                                } else {
                                    //记录完成计算的shopId
                                    taskExecutionRecordDao.insertOrUpdateTaskExecutionRecordByTaskType(new TaskExecutionRecord(shopAuth.getPuid(), shopAuth.getId(), taskType));
                                    break;
                                }
                            } catch (Exception e) {
                                log.error("", e);
                            }

                        }
                    } else {
                        iWordRootTargetingSpService.wordFrequencyHandler(shopAuth, null);
                    }
                } catch (Exception e) {
                    log.error("", e);
                }
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
    }

    @Override
    public void wordFrequencySpKeywordHandler(int shardIndex, int totalShard, Integer puid, Integer shopId, Integer calculateAll, Integer desc) {
        int start = 0;
        int shopLimit = 1000;
        String taskType = TaskExecutionRecordTaskType.SPKEYWORD.getType();
        List<ShopAuth> shopAuths;
        while (true) {
            List<ShopAuth> shopList = new ArrayList<>();
            if (desc != null && desc == 1) {
                shopList = slaveShopAuthDao.getAllValidAdShopByLimitOrderByIdDesc(puid, shopId, start, shopLimit);
            } else {
                shopList = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, shopLimit);
            }
            int size = shopList.size();
            //需要进行定时任务的店铺
            shopAuths = this.taskListByTimeAndType(shopList, taskType, shardIndex, totalShard, calculateAll);
            for (ShopAuth shopAuth : shopAuths) {
                try {
                    List<String> openWordRootMarketplaceList = WordRootCalculateServiceHelper.openWordRootMarketplaceList();
                    if (!openWordRootMarketplaceList.contains(shopAuth.getMarketplaceId())) {
                        log.info("wordFrequencySpKeywordHandler close wordRootService calculateAll: {}, puid: {}, shopId: {}, marketplaceId: {}", calculateAll, shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId());
                        continue;
                    }
                    log.info("wordFrequencySpKeywordHandler calculateAll: {}, puid: {}, shopId: {}", calculateAll, shopAuth.getPuid(), shopAuth.getId());
                    if (calculateAll != null && calculateAll == 1) {
                        Long id = Long.MAX_VALUE;
                        Object storeId = redisService.get(Constants.SP_KEYWORD_WORD_FREQUENCY_PAGE + shopAuth.getId());
                        if (storeId != null) {
                            id = (Long) storeId;
                        }
                        while (true) {
                            try {
                                Long nextId = iWordRootKeywordSpService.wordFrequencyKeywordIdHandler(shopAuth, id);
                                if (nextId != null) {
                                    id = nextId;
                                    redisService.set(Constants.SP_KEYWORD_WORD_FREQUENCY_PAGE + shopAuth.getId(), nextId, 60 * 60 * 48L);
                                } else {
                                    //记录完成计算的shopId
                                    taskExecutionRecordDao.insertOrUpdateTaskExecutionRecordByTaskType(new TaskExecutionRecord(shopAuth.getPuid(), shopAuth.getId(), taskType));
                                    redisService.del(Constants.SP_KEYWORD_WORD_FREQUENCY_PAGE + shopAuth.getId());
                                    break;
                                }
                            } catch (Exception e) {
                                log.error("", e);
                            }
                        }
                    } else {
                        iWordRootKeywordSpService.wordFrequencyKeywordIdHandler(shopAuth, null);
                    }
                } catch (Exception e) {
                    log.error("wordFrequencySpKeywordHandler error", e);
                }
            }
            if (size < shopLimit) {
                log.info("wordFrequencySpKeywordHandler break, shardIndex: {}, totalShard: {}", shardIndex, totalShard);
                break;
            }
            start += size;
        }
    }

    @Override
    public void wordFrequencySbKeywordHandler(int shardIndex, int totalShard, Integer puid, Integer shopId, Integer calculateAll) {
        int start = 0;
        int shopLimit = 1000;
        String taskType = TaskExecutionRecordTaskType.SBKEYWORD.getType();
        List<ShopAuth> shopAuths;
        while (true) {
            List<ShopAuth> shopList = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, shopLimit);
            int size = shopList.size();
            //需要进行定时任务的店铺
            shopAuths = this.taskListByTimeAndType(shopList, taskType, shardIndex, totalShard, calculateAll);
            for (ShopAuth shopAuth : shopAuths) {
                try {
                    List<String> openWordRootMarketplaceList = WordRootCalculateServiceHelper.openWordRootMarketplaceList();
                    if (!openWordRootMarketplaceList.contains(shopAuth.getMarketplaceId())) {
                        log.info("wordFrequencySbKeywordHandler close wordRootService calculateAll: {}, puid: {}, shopId: {}, marketplace: {}", calculateAll, shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplace());
                        continue;
                    }
                    log.info("wordFrequencySbKeywordHandler calculateAll: {}, puid: {}, shopId: {}", calculateAll, shopAuth.getPuid(), shopAuth.getId());
                    if (calculateAll != null && calculateAll == 1) {
                        int page = 1;
                        while (true) {
                            try {
                                boolean result = iWordRootKeywordSbService.wordFrequencyKeywordHandler(shopAuth, page);
                                if (result) {
                                    page++;
                                } else {
                                    //记录完成计算的shopId
                                    taskExecutionRecordDao.insertOrUpdateTaskExecutionRecordByTaskType(new TaskExecutionRecord(shopAuth.getPuid(), shopAuth.getId(), taskType));
                                    break;
                                }
                            } catch (Exception e) {
                                log.error("", e);
                            }

                        }
                    } else {
                        iWordRootKeywordSbService.wordFrequencyKeywordHandler(shopAuth, null);
                    }
                } catch (Exception e) {
                    log.error("", e);
                }
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
    }

    @Override
    public void wordFrequencySpQueryNewHandler(int shardIndex, int totalShard, Integer puid, Integer shopId, Integer calculateAll) {
        int start = 0;
        int shopLimit = 1000;
        String taskType = TaskExecutionRecordTaskType.SPQUERY.getType();
        List<ShopAuth> shopAuths;
        while (true) {
            List<ShopAuth> shopList = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, shopLimit);
            int size = shopList.size();
            //需要进行定时任务的店铺
            if (puid != null) {
                shopAuths = this.taskListByTimeAndType(shopList, taskType, shardIndex, totalShard, null);
            } else {
                shopAuths = this.taskListByTimeAndType(shopList, taskType, shardIndex, totalShard, calculateAll);
            }
            for (ShopAuth shopAuth : shopAuths) {
                try {
                    List<String> openWordRootMarketplaceList = WordRootCalculateServiceHelper.openWordRootMarketplaceList();
                    if (!openWordRootMarketplaceList.contains(shopAuth.getMarketplaceId())) {
                        log.info("wordFrequencySpQueryNewHandler close wordRootService puid: {}, shopId: {}, marketplace: {}", shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplace());
                        continue;
                    }
                    log.info("wordFrequencySpQueryNewHandler calculateAll: {}, puid: {}, shopId: {}", calculateAll, shopAuth.getPuid(), shopAuth.getId());
                    if (calculateAll != null && calculateAll == 1) {
                        long l1 = System.currentTimeMillis();
                        int page = 1;
                        Object storeId = redisService.get(Constants.SP_KEYWORD_QUERY_KEYWORD_WORD_FREQUENCY_PAGE + shopAuth.getId());
                        if (storeId != null) {
                            page = (int) storeId;
                        }
                        while (true) {
                            boolean result = iWordRootKeywordSpService.wordFrequencyQueryNewHandler(shopAuth, page);
                            if (result) {
                                page++;
                                redisService.set(Constants.SP_KEYWORD_QUERY_KEYWORD_WORD_FREQUENCY_PAGE + shopAuth.getId(), page, 60 * 60 * 48L);
                            } else {
                                //记录完成计算的shopId
                                taskExecutionRecordDao.insertOrUpdateTaskExecutionRecordByTaskType(new TaskExecutionRecord(shopAuth.getPuid(), shopAuth.getId(), taskType));
                                redisService.del(Constants.SP_KEYWORD_QUERY_KEYWORD_WORD_FREQUENCY_PAGE + shopAuth.getId());
                                break;
                            }
                        }
                        log.info("wordFrequencySpQueryNewHandler:puid: {}, shopId: {}, time: {}", shopAuth.getPuid(), shopAuth.getId(), System.currentTimeMillis() - l1);
                    } else {
                        iWordRootKeywordSpService.wordFrequencyQueryNewHandler(shopAuth, null);
                    }
                } catch (Exception e) {
                    log.error("wordFrequencySpQueryNewHandler error", e);
                }
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
    }

    @Override
    public void wordFrequencySbQueryNewHandler(int shardIndex, int totalShard, Integer puid, Integer shopId, Integer calculateAll) {
        int start = 0;
        int shopLimit = 1000;
        String taskType = TaskExecutionRecordTaskType.SBQUERY.getType();
        List<ShopAuth> shopAuths;
        while (true) {
            List<ShopAuth> shopList = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, shopLimit);
            int size = shopList.size();
            //需要进行定时任务的店铺
            if (puid != null) {
                shopAuths = this.taskListByTimeAndType(shopList, taskType, shardIndex, totalShard, null);
            } else {
                shopAuths = this.taskListByTimeAndType(shopList, taskType, shardIndex, totalShard, calculateAll);
            }
            for (ShopAuth shopAuth : shopAuths) {
                try {
                    List<String> openWordRootMarketplaceList = WordRootCalculateServiceHelper.openWordRootMarketplaceList();
                    if (!openWordRootMarketplaceList.contains(shopAuth.getMarketplaceId())) {
                        log.info("wordFrequencySbQueryNewHandler close wordRootService puid: {}, shopId: {}, marketplace: {}", shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplace());
                        continue;
                    }
                    log.info("wordFrequencySbQueryNewHandler calculateAll: {}, puid: {}, shopId: {}", calculateAll, shopAuth.getPuid(), shopAuth.getId());
                    if (calculateAll != null && calculateAll == 1) {
                        long l1 = System.currentTimeMillis();
                        int page = 1;
                        Object storeId = redisService.get(Constants.SB_KEYWORD_QUERY_KEYWORD_WORD_FREQUENCY_PAGE + shopAuth.getId());
                        if (storeId != null) {
                            page = (int) storeId;
                        }
                        while (true) {
                            boolean result = iWordRootKeywordSbService.wordFrequencyQueryNewHandler(shopAuth, page);
                            if (result) {
                                page++;
                                redisService.set(Constants.SB_KEYWORD_QUERY_KEYWORD_WORD_FREQUENCY_PAGE + shopAuth.getId(), page, 60 * 60 * 48L);
                            } else {
                                //记录完成计算的shopId
                                taskExecutionRecordDao.insertOrUpdateTaskExecutionRecordByTaskType(new TaskExecutionRecord(shopAuth.getPuid(), shopAuth.getId(), taskType));
                                redisService.del(Constants.SB_KEYWORD_QUERY_KEYWORD_WORD_FREQUENCY_PAGE + shopAuth.getId());
                                break;
                            }
                        }
                        log.info("wordFrequencySbQueryNewHandler:puid: {}, shopId: {}, time: {}", shopAuth.getPuid(), shopAuth.getId(), System.currentTimeMillis() - l1);
                    } else {
                        iWordRootKeywordSbService.wordFrequencyQueryNewHandler(shopAuth, null);
                    }
                } catch (Exception e) {
                    log.error("wordFrequencySbQueryNewHandler error", e);
                }
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
    }

    @Override
    public void wordFrequencySpTargetingNewHandler(int shardIndex, int totalShard, Integer puid, Integer shopId, Integer calculateAll) {
        int start = 0;
        int shopLimit = 1000;
        String taskType = TaskExecutionRecordTaskType.SPTARGETING.getType();
        List<ShopAuth> shopAuths;
        while (true) {
            List<ShopAuth> shopList = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, shopLimit);
            int size = shopList.size();
            //需要进行定时任务的店铺
            if (puid != null) {
                shopAuths = this.taskListByTimeAndType(shopList, taskType, shardIndex, totalShard, null);
            } else {
                shopAuths = this.taskListByTimeAndType(shopList, taskType, shardIndex, totalShard, calculateAll);
            }
            for (ShopAuth shopAuth : shopAuths) {
                try {
                    List<String> openWordRootMarketplaceList = WordRootCalculateServiceHelper.openWordRootMarketplaceList();
                    if (!openWordRootMarketplaceList.contains(shopAuth.getMarketplaceId())) {
                        log.info("wordFrequencySpTargetingNewHandler close wordRootService puid: {}, shopId: {}, marketplace: {}", shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplace());
                        continue;
                    }
                    log.info("wordFrequencySpTargetingNewHandler calculateAll: {}, puid: {}, shopId: {}", calculateAll, shopAuth.getPuid(), shopAuth.getId());
                    if (calculateAll != null && calculateAll == 1) {
                        long l1 = System.currentTimeMillis();
                        int page = 1;
                        Object storeId = redisService.get(Constants.SP_KEYWORD_QUERY_TARGET_WORD_FREQUENCY_PAGE + shopAuth.getId());
                        if (storeId != null) {
                            page = (int) storeId;
                        }
                        while (true) {
                            boolean result = iWordRootTargetingSpService.wordFrequencyQueryHandler(shopAuth, page);
                            if (result) {
                                page++;
                                redisService.set(Constants.SP_KEYWORD_QUERY_TARGET_WORD_FREQUENCY_PAGE + shopAuth.getId(), page, 60 * 60 * 48L);
                            } else {
                                //记录完成计算的shopId
                                taskExecutionRecordDao.insertOrUpdateTaskExecutionRecordByTaskType(new TaskExecutionRecord(shopAuth.getPuid(), shopAuth.getId(), taskType));
                                redisService.del(Constants.SP_KEYWORD_QUERY_TARGET_WORD_FREQUENCY_PAGE + shopAuth.getId());
                                break;
                            }
                        }
                        log.info("wordFrequencySpTargetingNewHandler:puid: {}, shopId: {}, time: {}", shopAuth.getPuid(), shopAuth.getId(), System.currentTimeMillis() - l1);
                    } else {
                        iWordRootTargetingSpService.wordFrequencyQueryHandler(shopAuth, null);
                    }
                } catch (Exception e) {
                    log.error("wordFrequencySpTargetingNewHandler error", e);
                }
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
    }

    @Override
    public void wordFrequencySpKeywordNewHandler(int shardIndex, int totalShard, Integer puid, Integer shopId, Integer calculateAll, Integer desc) {
        int start = 0;
        int shopLimit = 1000;
        String taskType = TaskExecutionRecordTaskType.SPKEYWORD.getType();
        List<ShopAuth> shopAuths;
        while (true) {
            List<ShopAuth> shopList = new ArrayList<>();
            if (desc != null && desc == 1) {
                shopList = slaveShopAuthDao.getAllValidAdShopByLimitOrderByIdDesc(puid, shopId, start, shopLimit);
            } else {
                shopList = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, shopLimit);
            }
            int size = shopList.size();
            //需要进行定时任务的店铺
            if (puid != null) {
                shopAuths = this.taskListByTimeAndType(shopList, taskType, shardIndex, totalShard, null);
            } else {
                shopAuths = this.taskListByTimeAndType(shopList, taskType, shardIndex, totalShard, calculateAll);
            }
            for (ShopAuth shopAuth : shopAuths) {
                try {
                    //黑名单直接返回
                    if (CollectionUtils.isNotEmpty(dynamicRefreshConfiguration.getKeywordWordRootBlackPuids()) && dynamicRefreshConfiguration.getKeywordWordRootBlackPuids().contains(shopAuth.getPuid().toString())) {
                        log.info("wordFrequencySpKeywordNewHandler blackPuids wordRootService calculateAll: {}, puid: {}", calculateAll, shopAuth.getPuid());
                        continue;
                    }
                    List<String> openWordRootMarketplaceList = WordRootCalculateServiceHelper.openWordRootMarketplaceList();
                    if (!openWordRootMarketplaceList.contains(shopAuth.getMarketplaceId())) {
                        log.info("wordFrequencySpKeywordNewHandler close wordRootService calculateAll: {}, puid: {}, shopId: {}, marketplaceId: {}", calculateAll, shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId());
                        continue;
                    }
                    log.info("wordFrequencySpKeywordNewHandler calculateAll: {}, puid: {}, shopId: {}", calculateAll, shopAuth.getPuid(), shopAuth.getId());
                    if (calculateAll != null && calculateAll == 1) {
                        long l1 = System.currentTimeMillis();
                        int page = 1;
                        Object storeId = redisService.get(Constants.SP_KEYWORD_WORD_FREQUENCY_PAGE + shopAuth.getId());
                        if (storeId != null) {
                            page = (int) storeId;
                        }
                        while (true) {
                            boolean result = iWordRootKeywordSpService.wordFrequencyKeywordHandler(shopAuth, page);
                            if (result) {
                                page++;
                                redisService.set(Constants.SP_KEYWORD_WORD_FREQUENCY_PAGE + shopAuth.getId(), page, 60 * 60 * 48L);
                            } else {
                                //记录完成计算的shopId
                                taskExecutionRecordDao.insertOrUpdateTaskExecutionRecordByTaskType(new TaskExecutionRecord(shopAuth.getPuid(), shopAuth.getId(), taskType));
                                redisService.del(Constants.SP_KEYWORD_WORD_FREQUENCY_PAGE + shopAuth.getId());
                                break;
                            }
                        }
                        log.info("wordFrequencySpKeywordNewHandler:puid: {}, shopId: {}, time: {}", shopAuth.getPuid(), shopAuth.getId(), System.currentTimeMillis() - l1);
                    } else {
                        iWordRootKeywordSpService.wordFrequencyKeywordHandler(shopAuth, null);
                    }
                } catch (Exception e) {
                    log.error("wordFrequencySpKeywordNewHandler error", e);
                }
            }
            if (size < shopLimit) {
                log.info("wordFrequencySpKeywordNewHandler break, shardIndex: {}, totalShard: {}", shardIndex, totalShard);
                break;
            }
            start += size;
        }
    }

    @Override
    public void wordFrequencySbKeywordNewHandler(int shardIndex, int totalShard, Integer puid, Integer shopId, Integer calculateAll) {
        int start = 0;
        int shopLimit = 1000;
        String taskType = TaskExecutionRecordTaskType.SBKEYWORD.getType();
        List<ShopAuth> shopAuths;
        while (true) {
            List<ShopAuth> shopList = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, shopLimit);
            int size = shopList.size();
            //需要进行定时任务的店铺
            if (puid != null) {
                shopAuths = this.taskListByTimeAndType(shopList, taskType, shardIndex, totalShard, null);
            } else {
                shopAuths = this.taskListByTimeAndType(shopList, taskType, shardIndex, totalShard, calculateAll);
            }
            for (ShopAuth shopAuth : shopAuths) {
                try {
                    //黑名单直接返回
                    if (CollectionUtils.isNotEmpty(dynamicRefreshConfiguration.getKeywordWordRootBlackPuids()) && dynamicRefreshConfiguration.getKeywordWordRootBlackPuids().contains(shopAuth.getPuid().toString())) {
                        log.info("wordFrequencySpKeywordNewHandler blackPuids wordRootService calculateAll: {}, puid: {}", calculateAll, shopAuth.getPuid());
                        continue;
                    }
                    List<String> openWordRootMarketplaceList = WordRootCalculateServiceHelper.openWordRootMarketplaceList();
                    if (!openWordRootMarketplaceList.contains(shopAuth.getMarketplaceId())) {
                        log.info("wordFrequencySbKeywordNewHandler close wordRootService calculateAll: {}, puid: {}, shopId: {}, marketplace: {}", calculateAll, shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplace());
                        continue;
                    }
                    log.info("wordFrequencySbKeywordNewHandler calculateAll: {}, puid: {}, shopId: {}", calculateAll, shopAuth.getPuid(), shopAuth.getId());
                    if (calculateAll != null && calculateAll == 1) {
                        long l1 = System.currentTimeMillis();
                        int page = 1;
                        Object storeId = redisService.get(Constants.SB_KEYWORD_WORD_FREQUENCY_PAGE + shopAuth.getId());
                        if (storeId != null) {
                            page = (int) storeId;
                        }
                        while (true) {
                            boolean result = iWordRootKeywordSbService.wordFrequencyKeywordNewHandler(shopAuth, page);
                            if (result) {
                                page++;
                                redisService.set(Constants.SB_KEYWORD_WORD_FREQUENCY_PAGE + shopAuth.getId(), page, 60 * 60 * 48L);
                            } else {
                                //记录完成计算的shopId
                                taskExecutionRecordDao.insertOrUpdateTaskExecutionRecordByTaskType(new TaskExecutionRecord(shopAuth.getPuid(), shopAuth.getId(), taskType));
                                redisService.del(Constants.SB_KEYWORD_WORD_FREQUENCY_PAGE + shopAuth.getId());
                                break;
                            }
                        }
                        log.info("wordFrequencySbKeywordNewHandler:puid: {}, shopId: {}, time: {}", shopAuth.getPuid(), shopAuth.getId(), System.currentTimeMillis() - l1);
                    } else {
                        iWordRootKeywordSbService.wordFrequencyKeywordNewHandler(shopAuth, null);
                    }
                } catch (Exception e) {
                    log.error("wordFrequencySbKeywordNewHandler error", e);
                }
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
    }

    @Override
    public void wordRootTranslatorsQuerySpKeywordHandler(int shardIndex, int totalShard, Integer puid, Integer shopId) {
        int start = 0;
        int shopLimit = 1000;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, shopLimit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shopAuth : shopAuths) {
                log.info("wordRootTranslatorsSpKeywordHandler puid: {}, shopId: {}", shopAuth.getPuid(), shopAuth.getId());
                String startTime = DateUtil.getDayByDaysAgo(180, DateUtil.PATTERN_DATE_TIME);
                String endTime = DateUtil.getDayByDaysAgo(0, DateUtil.PATTERN_DATE_TIME);
                try {
                    iWordRootKeywordSpService.queryWordRootTranslators(shopAuth.getPuid(), shopAuth, startTime, endTime);
                } catch (Exception e) {
                    log.error(String.format("wordRootTranslatorsSpKeywordHandler error, puid: %d, shopId: %d", shopAuth.getPuid(), shopAuth.getId()), e);
                }
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
    }

    @Override
    public void wordRootTranslationQueryHandler(int shardIndex, int totalShard, Integer puid, Integer shopId) {
        int start = 0;
        int shopLimit = 1000;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, shopLimit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shopAuth : shopAuths) {
                log.info("wordRootTranslationQueryHandler puid: {}, shopId: {}", shopAuth.getPuid(), shopAuth.getId());
                String startTime = DateUtil.getDayByDaysAgo(2, DateUtil.PATTERN_YYYYMMDD);
                String endTime = DateUtil.getDayByDaysAgo(0, DateUtil.PATTERN_YYYYMMDD);
                try {
                    iWordRootKeywordSpService.queryWordRootTranslation(shopAuth.getPuid(), shopAuth, startTime, endTime);
                } catch (Exception e) {
                    log.error(String.format("wordRootTranslationQueryHandler error, puid: %d, shopId: %d", shopAuth.getPuid(), shopAuth.getId()), e);
                }
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
    }

    @Override
    public void wordRootTranslatorsQuerySbKeywordHandler(int shardIndex, int totalShard, Integer puid, Integer shopId) {
        int start = 0;
        int shopLimit = 1000;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, shopLimit);
            int size = shopAuths.size();
            for (ShopAuth shopAuth : shopAuths) {
                log.info("wordRootTranslatorsSbKeywordHandler puid: {}, shopId: {}", shopAuth.getPuid(), shopAuth.getId());
                String startTime = DateUtil.getDayByDaysAgo(180, DateUtil.PATTERN_DATE_TIME);
                String endTime = DateUtil.getDayByDaysAgo(0, DateUtil.PATTERN_DATE_TIME);
                try {
                    iWordRootKeywordSbService.queryWordRootTranslators(shopAuth.getPuid(), shopAuth, startTime, endTime);
                } catch (Exception e) {
                    log.error(String.format("wordRootTranslatorsSbKeywordHandler error, puid: %d, shopId: %d", shopAuth.getPuid(), shopAuth.getId()), e);
                }
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
    }

    @Override
    public void wordRootTranslatorsQuerySpTargetingHandler(int shardIndex, int totalShard, Integer puid, Integer shopId) {
        int start = 0;
        int shopLimit = 1000;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, shopLimit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shopAuth : shopAuths) {
                log.info("wordRootTranslatorsSpTargetingHandler puid: {}, shopId: {}", shopAuth.getPuid(), shopAuth.getId());
                String startTime = DateUtil.getDayByDaysAgo(180, DateUtil.PATTERN_DATE_TIME);
                String endTime = DateUtil.getDayByDaysAgo(0, DateUtil.PATTERN_DATE_TIME);
                try {
                    iWordRootTargetingSpService.wordRootTranslators(shopAuth.getPuid(), shopAuth, startTime, endTime);
                } catch (Exception e) {
                    log.error(String.format("wordRootTranslatorsSpTargetingHandler error, puid: %d, shopId: %d", shopAuth.getPuid(), shopAuth.getId()), e);
                }
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
    }

    @Override
    public void keywordSpWordRootTranslatorsHandler(int shardIndex, int totalShard, Integer puid, Integer shopId) {
        int start = 0;
        int shopLimit = 1000;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, shopLimit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shopAuth : shopAuths) {
                log.info("wordRootTranslatorsKeywordSpHandler puid: {}, shopId: {}", shopAuth.getPuid(), shopAuth.getId());
                String startTime = DateUtil.getDayByDaysAgo(180, DateUtil.PATTERN_DATE_TIME);
                String endTime = DateUtil.getDayByDaysAgo(0, DateUtil.PATTERN_DATE_TIME);
                try {
                    iWordRootKeywordSpService.keywordWordRootTranslators(shopAuth.getPuid(), shopAuth, startTime, endTime);
                } catch (Exception e) {
                    log.error(String.format("wordRootTranslatorsKeywordSpHandler error, puid: %d, shopId: %d", shopAuth.getPuid(), shopAuth.getId()), e);
                }
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
    }

    @Override
    public void keywordSbWordRootTranslatorsHandler(int shardIndex, int totalShard, Integer puid, Integer shopId) {
        int start = 0;
        int shopLimit = 1000;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, shopLimit);
            int size = shopAuths.size();
            for (ShopAuth shopAuth : shopAuths) {
                log.info("wordRootTranslatorsKeywordSbHandler puid: {}, shopId: {}", shopAuth.getPuid(), shopAuth.getId());
                String startTime = DateUtil.getDayByDaysAgo(180, DateUtil.PATTERN_DATE_TIME);
                String endTime = DateUtil.getDayByDaysAgo(0, DateUtil.PATTERN_DATE_TIME);
                try {
                    iWordRootKeywordSbService.keywordWordRootTranslators(shopAuth.getPuid(), shopAuth, startTime, endTime);
                } catch (Exception e) {
                    log.error(String.format("wordRootTranslatorsKeywordSbHandler error, puid: %d, shopId: %d", shopAuth.getPuid(), shopAuth.getId()), e);
                }
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
    }
}
