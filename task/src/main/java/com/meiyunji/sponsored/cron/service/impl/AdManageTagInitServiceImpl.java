package com.meiyunji.sponsored.cron.service.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.cron.service.IAdManageTagInitService;
import com.meiyunji.sponsored.service.account.dao.ISlaveScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveUserDao;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.adTagSystem.dao.IAdManageTagDao;
import com.meiyunji.sponsored.service.adTagSystem.dao.IAdManageTagGroupDao;
import com.meiyunji.sponsored.service.adTagSystem.dao.IAdManageTagGroupUserDao;
import com.meiyunji.sponsored.service.adTagSystem.dao.IAdManageTagRelationDao;
import com.meiyunji.sponsored.service.adTagSystem.dao.impl.AdManageTagDaoGroupImpl;
import com.meiyunji.sponsored.service.adTagSystem.enums.AdManageTagTypeEnum;
import com.meiyunji.sponsored.service.adTagSystem.enums.AdTagPermissionTypeEnum;
import com.meiyunji.sponsored.service.adTagSystem.po.AdManageTag;
import com.meiyunji.sponsored.service.adTagSystem.po.AdManageTagGroup;
import com.meiyunji.sponsored.service.adTagSystem.po.AdManageTagGroupUser;
import com.meiyunji.sponsored.service.adTagSystem.po.AdManageTagRelation;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.constants.TaskExecutionRecordTaskType;
import com.meiyunji.sponsored.service.cpc.dao.IAdMarkupTagDao;
import com.meiyunji.sponsored.service.cpc.dao.IAdTagDao;
import com.meiyunji.sponsored.service.cpc.dao.ITaskExecutionRecordDao;
import com.meiyunji.sponsored.service.cpc.po.AdMarkupTag;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.cpc.po.TaskExecutionRecord;
import com.meiyunji.sponsored.service.cpc.vo.AdTagVo;
import com.meiyunji.sponsored.service.doris.po.OdsAdManageTag;
import com.meiyunji.sponsored.service.doris.po.OdsAdManageTagRelation;
import com.meiyunji.sponsored.service.doris.service.impl.DorisServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-11-05  19:43
 */
@Service
@Slf4j
public class AdManageTagInitServiceImpl implements IAdManageTagInitService {
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;
    @Autowired
    private IAdTagDao adTagDao;
    @Autowired
    private IAdMarkupTagDao adMarkupTagDao;
    @Autowired
    private IAdManageTagDao adManageTagDao;
    @Autowired
    private IAdManageTagGroupDao adManageTagGroupDao;
    @Autowired
    private IAdManageTagGroupUserDao adManageTagGroupUserDao;
    @Autowired
    private IAdManageTagRelationDao adManageTagRelationDao;
    @Autowired
    private ISlaveScVcShopAuthDao slaveShopAuthDao;
    @Autowired
    private ITaskExecutionRecordDao taskExecutionRecordDao;
    @Autowired
    private IUserDao userDao;
    @Autowired
    private ISlaveUserDao slaveUserDao;
    @Autowired
    private DorisServiceImpl dorisService;

    @Override
    public void initCampaignAdManageTagHandler(int shardIndex, int totalShard, Integer puid) {
        //指定puid跑不校验直接跑
        if (puid != null) {
            log.info(String.format("initCampaignAdManageTagHandler by puid, puid: %d", puid));
            try {
                if (Math.abs(puid.hashCode() % totalShard) == shardIndex) {
                    this.initCampaignAdManageTag(puid);
                }
            } catch (Exception e) {
                log.error(String.format("initCampaignAdManageTagHandler by puid error, puid: %d", puid), e);
            }
            return;
        }
        if (dynamicRefreshConfiguration.getAdManageTagInitAllBool()) {
            log.info("initCampaignAdManageTagHandler by all puid");
            //查询所有puid
            int puidLimit = 1000;
            int puidStartIndex = 0;
            int index = 1;
            Integer curPuid = 0;
            //获取已经跑完的puid列表
            List<Integer> succPuidList = taskExecutionRecordDao.getTaskExecutionRecordByTaskType(TaskExecutionRecordTaskType.ADMANEGETAGINIT.getType());
            try {
                while (true) {
                    List<Integer> puidList = slaveShopAuthDao.getAllValidAdShopPuidByLimit(puidStartIndex, puidLimit);
                    log.info(String.format("initCampaignAdManageTagHandler by all puid, puidList.size: %d", puidList.size()));
                    //处理一个puid
                    for (int i = 0; i < puidList.size(); i++) {
                        curPuid = puidList.get(i);
                        log.info(String.format("initCampaignAdManageTagHandler by all puid, puid: %d, index: %d", curPuid, index++));
                        if (Math.abs(curPuid.hashCode() % totalShard) != shardIndex) {
                            log.info(String.format("initCampaignAdManageTagHandler by all puid, puid: %d shard continue", curPuid));
                            continue;
                        }
                        if (succPuidList.contains(curPuid)) {
                            log.info(String.format("initCampaignAdManageTagHandler by all puid, puid: %d continue", curPuid));
                            continue;
                        }
                        this.initCampaignAdManageTag(curPuid);
                        taskExecutionRecordDao.insertOrUpdateTaskExecutionRecordByTaskType(new TaskExecutionRecord(curPuid, 0, TaskExecutionRecordTaskType.ADMANEGETAGINIT.getType()));
                    }
                    if (CollectionUtils.isEmpty(puidList) || puidList.size() < puidLimit) {
                        break;
                    }
                    puidStartIndex += puidLimit;
                }
            } catch (Exception e) {
                log.error(String.format("initCampaignAdManageTagHandler by all puid error, puid: %d", curPuid), e);
            }
        } else if (CollectionUtils.isNotEmpty(dynamicRefreshConfiguration.getAdManageTagInitWhitePuidSet())) {
            log.info(String.format("initCampaignAdManageTagHandler by whild puid, whilePuidSet.size:%d", dynamicRefreshConfiguration.getAdManageTagInitWhitePuidSet().size()));
            //获取已经跑完的puid列表
            List<Integer> succPuidList = taskExecutionRecordDao.getTaskExecutionRecordByTaskType(TaskExecutionRecordTaskType.ADMANEGETAGINIT.getType());
            int index = 1;
            for (String curPuid : dynamicRefreshConfiguration.getAdManageTagInitWhitePuidSet()) {
                try {
                    Integer cPuid = Integer.valueOf(curPuid);
                    log.info(String.format("initCampaignAdManageTagHandler by whild puid, puid: %d, index: %d", cPuid, index++));
                    if (Math.abs(cPuid.hashCode() % totalShard) != shardIndex) {
                        log.info(String.format("initCampaignAdManageTagHandler by all puid, puid: %d shard continue", cPuid));
                        continue;
                    }
                    if (succPuidList.contains(cPuid)) {
                        log.info(String.format("initCampaignAdManageTagHandler by whild puid, puid: %d continue", cPuid));
                        continue;
                    }
                    this.initCampaignAdManageTag(cPuid);
                    taskExecutionRecordDao.insertOrUpdateTaskExecutionRecordByTaskType(new TaskExecutionRecord(cPuid, 0, TaskExecutionRecordTaskType.ADMANEGETAGINIT.getType()));
                } catch (Exception e) {
                    log.error(String.format("initCampaignAdManageTagHandler by whild puid error, puid: %s", curPuid), e);
                }
            }
        }
    }

    @Override
    public void initNewUser() {
        try {
            //获取已经跑完的任务时间
            TaskExecutionRecord taskExecutionRecord = taskExecutionRecordDao.getOneByTaskType(TaskExecutionRecordTaskType.INIT_NEW_USER_TAG.getType());
            boolean isNullTaskExecutionRecord = false;
            Date currentDate = new Date();
            if (taskExecutionRecord == null) {
                taskExecutionRecord = new TaskExecutionRecord();
                taskExecutionRecord.setPuid(0);
                taskExecutionRecord.setShopId(0);
                taskExecutionRecord.setTaskType(TaskExecutionRecordTaskType.INIT_NEW_USER_TAG.getType());
                isNullTaskExecutionRecord = true;
            }
            //拿记录表的同步时间作为已跑完用户的创建时间来增量同步
            Date userCreateTime = taskExecutionRecord.getSyncTime() != null ? taskExecutionRecord.getSyncTime() : DateUtil.addMonth(new Date(), -2);
            List<User> userList = slaveUserDao.listNoDeleteUserByCreateTime(userCreateTime);
            for (User user : userList) {
                List<Long> groupIds = adManageTagGroupDao.listGroupIdByAllPermissionType(user.getPuid(), AdManageTagTypeEnum.CAMPAIGN.getCode());
                if (CollectionUtils.isEmpty(groupIds)) {
                    continue;
                }
                List<AdManageTagGroupUser> groupUserList = groupIds.stream().map(i -> {
                    AdManageTagGroupUser tagGroupUser = new AdManageTagGroupUser();
                    tagGroupUser.setPuid(user.getPuid());
                    tagGroupUser.setType(AdManageTagTypeEnum.CAMPAIGN.getCode());
                    tagGroupUser.setGroupId(i);
                    tagGroupUser.setUserId(user.getId());
                    return tagGroupUser;
                }).collect(Collectors.toList());
                adManageTagGroupUserDao.batchInsert(user.getPuid(), groupUserList);
            }
            //第一次全量同步，增加同步记录
            if (isNullTaskExecutionRecord) {
                taskExecutionRecord.setSyncTime(currentDate);
                taskExecutionRecordDao.insertOrUpdateTaskExecutionRecordByTaskType(taskExecutionRecord);
                return;
            }
            //后续增量同步，记录当前时间或者最后一个用户创建时间
            if (CollectionUtils.isNotEmpty(userList)) {
                currentDate = userList.get(userList.size() - 1).getCreateTime();
            }
            taskExecutionRecordDao.updateSyncTimeByTaskType(TaskExecutionRecordTaskType.INIT_NEW_USER_TAG.getType(), currentDate);
        } catch (Exception e) {
            log.error("initNewUser error", e);
        }
    }

    private void initCampaignAdManageTag(Integer puid) throws Exception {
        //查询旧标签表是否有标签
        int count = adTagDao.countByPuidType(puid, AdManageTagTypeEnum.CAMPAIGN.getType());
        if (count == 0) {
            return;
        }
        //插入默认标签组
        AdManageTagGroup defaultTagGroup = adManageTagGroupDao.getByPuidTypeName(puid, AdManageTagTypeEnum.CAMPAIGN.getCode(), AdManageTagDaoGroupImpl.DEFALUT_TAG_GROUP_NAME);
        Long groupId = null;
        if (defaultTagGroup == null) {
            AdManageTagGroup adManageTagGroup = new AdManageTagGroup();
            adManageTagGroup.setPuid(puid);
            adManageTagGroup.setType(AdManageTagTypeEnum.CAMPAIGN.getCode());
            adManageTagGroup.setName(AdManageTagDaoGroupImpl.DEFALUT_TAG_GROUP_NAME);
            adManageTagGroup.setPermissionType(AdTagPermissionTypeEnum.ALL.getCode());
            adManageTagGroup.setCreateId(puid);
            adManageTagGroup.setUpdateId(puid);
            groupId = adManageTagGroupDao.save(puid, adManageTagGroup);
        } else {
            groupId = defaultTagGroup.getId();
        }
        //插入标签组权限
        List<Integer> uidList = userDao.listNoDeleteUser(puid).stream().map(User::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(uidList)) {
            adManageTagGroupUserDao.batchInsert(puid, this.convertAdManageTagGroupUser(puid, groupId, uidList, AdManageTagTypeEnum.CAMPAIGN.getCode()));
        }
        //获取旧标签数据
        List<AdTag> adTags = adTagDao.listAllByPuidAndType(puid, AdManageTagTypeEnum.CAMPAIGN.getType());
        if (CollectionUtils.isNotEmpty(adTags)) {
            adManageTagDao.batchInsertOrUpdate(puid, this.convertAdTagToAdManageTag(groupId, adTags));
            List<AdManageTag> adManageTagList = adManageTagDao.getListByGroupId(puid, AdManageTagTypeEnum.CAMPAIGN.getCode(), groupId);
            List<OdsAdManageTag> odsAdManageTagList = new ArrayList<>();
            adManageTagList.forEach(e -> {
                OdsAdManageTag odsAdManageTag = new OdsAdManageTag();
                BeanUtils.copyProperties(e, odsAdManageTag);
                odsAdManageTagList.add(odsAdManageTag);
            });
            dorisService.saveDoris(odsAdManageTagList);
        }
        //获取旧标签关联表数据
        List<AdMarkupTag> adMarkupTags = adMarkupTagDao.listAllByPuidAndType(puid, AdManageTagTypeEnum.CAMPAIGN.getType());
        if (CollectionUtils.isNotEmpty(adMarkupTags)) {
            //获取新标签id
            Map<String, Long> tagNameIdMap = adManageTagDao.getListByGroupId(puid, AdManageTagTypeEnum.CAMPAIGN.getCode(), groupId)
                    .stream().collect(Collectors.toMap(AdManageTag::getName, AdManageTag::getId));
            Map<Long, Long> idMap = adTags.stream().collect(Collectors.toMap(AdTag::getId, e -> tagNameIdMap.get(e.getName())));
            List<AdManageTagRelation> insertList = this.convertAdMarkupTagToAdManageTagRelation(groupId, adMarkupTags, idMap, AdManageTagTypeEnum.CAMPAIGN.getCode());
            if (CollectionUtils.isNotEmpty(insertList)) {
                adManageTagRelationDao.makeAdTag(puid, AdManageTagTypeEnum.CAMPAIGN.getCode(), insertList);
                List<AdManageTagRelation> adManageTagRelationList = adManageTagRelationDao.listByGroupId(puid, AdManageTagTypeEnum.CAMPAIGN.getCode(), groupId);
                List<OdsAdManageTagRelation> odsAdManageTagRelationList = new ArrayList<>();
                adManageTagRelationList.forEach(e -> {
                    OdsAdManageTagRelation odsAdManageTagRelation = new OdsAdManageTagRelation();
                    BeanUtils.copyProperties(e, odsAdManageTagRelation);
                    odsAdManageTagRelationList.add(odsAdManageTagRelation);
                });
                dorisService.saveDoris(odsAdManageTagRelationList);
            }
        }
    }

    private List<AdManageTagGroupUser> convertAdManageTagGroupUser(Integer puid, Long groupId, List<Integer> uidList, Integer type) {
        List<AdManageTagGroupUser> adManageTagGroupUsers = new ArrayList<>();
        for (Integer uid : uidList) {
            AdManageTagGroupUser adManageTagGroupUser = new AdManageTagGroupUser();
            adManageTagGroupUser.setPuid(puid);
            adManageTagGroupUser.setType(type);
            adManageTagGroupUser.setGroupId(groupId);
            adManageTagGroupUser.setUserId(uid);
            adManageTagGroupUsers.add(adManageTagGroupUser);
        }
        return adManageTagGroupUsers;
    }

    private List<AdManageTagRelation> convertAdMarkupTagToAdManageTagRelation(Long groupId, List<AdMarkupTag> adMarkupTags, Map<Long, Long> idMap, Integer type) {
        List<AdManageTagRelation> adManageTagRelationList = new ArrayList<>();
        for (AdMarkupTag adMarkupTag : adMarkupTags) {
            if (idMap.containsKey(adMarkupTag.getTagId())) {
                AdManageTagRelation adManageTagRelation = new AdManageTagRelation();
                adManageTagRelation.setPuid(adMarkupTag.getPuid());
                adManageTagRelation.setShopId(adMarkupTag.getShopId());
                adManageTagRelation.setType(type);
                adManageTagRelation.setTagId(idMap.get(adMarkupTag.getTagId()));
                adManageTagRelation.setRelationId(adMarkupTag.getRelationId());
                adManageTagRelation.setGroupId(groupId);
                adManageTagRelation.setDelFlag(0L);
                adManageTagRelation.setCreateId(adMarkupTag.getCreateId());
                adManageTagRelation.setUpdateId(adMarkupTag.getUpdateId());
                adManageTagRelationList.add(adManageTagRelation);
            }
        }
        return adManageTagRelationList;
    }

    private List<AdManageTag> convertAdTagToAdManageTag(Long groupId, List<AdTag> adTags) {
        List<AdManageTag> adManageTagList = new ArrayList<>();
        int i = 1;
        for (AdTag adTag: adTags) {
            AdManageTag adManageTag = new AdManageTag();
            adManageTag.setPuid(adTag.getPuid());
            adManageTag.setType(AdManageTagTypeEnum.getCodeByType(adTag.getType()));
            adManageTag.setGroupId(groupId);
            adManageTag.setName(adTag.getName());
            adManageTag.setColor(adTag.getColor());
            adManageTag.setPermissionType(AdTagPermissionTypeEnum.ALL.getCode());
            adManageTag.setSort(i++);
            adManageTag.setDelFlag(0);
            adManageTag.setCreateId(adTag.getCreateId());
            adManageTag.setUpdateId(adTag.getUpdateId());
            adManageTagList.add(adManageTag);
        }
        return adManageTagList;
    }
}
