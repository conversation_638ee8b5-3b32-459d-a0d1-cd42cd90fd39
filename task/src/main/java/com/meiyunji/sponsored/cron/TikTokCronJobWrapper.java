package com.meiyunji.sponsored.cron;

import com.meiyunji.sponsored.service.multiPlatform.tiktok.service.TikTokSyncService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class TikTokCronJobWrapper {

    // tk同步定时任务

    @Autowired
    private TikTokSyncService tiktokSyncService;

    @XxlJob(value = "tiktokGmvMaxCampaignSync")
    public ReturnT<String> tiktokGmvMaxCampaignSync(String params) {
        log.info("TikTokCronJobWrapper.tiktokGmvMaxCampaignSync start");
        try {
            tiktokSyncService.syncGmvMaxCampaignData(params);
            log.info("TikTokCronJobWrapper.tiktokGmvMaxCampaignSync end");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("TikTokCronJobWrapper.tiktokGmvMaxCampaignSync error", e);
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "tiktokGmvMaxCampaignReportSync")
    public ReturnT<String> tiktokGmvMaxCampaignReportSync(String params) {
        log.info("TikTokCronJobWrapper.tiktokGmvMaxCampaignReportSync start");
        try {
            tiktokSyncService.syncGmvMaxCampaignReportData(params);
            log.info("TikTokCronJobWrapper.tiktokGmvMaxCampaignReportSync end");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("TikTokCronJobWrapper.tiktokGmvMaxCampaignReportSync error", e);
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "tiktokGmvMaxErrorRecoveryJob")
    public ReturnT<String> tiktokGmvMaxErrorRecoveryJob(String params) {
        log.info("TikTokCronJobWrapper.tiktokGmvMaxErrorRecoveryJob start");
        try {
            tiktokSyncService.tiktokGmvMaxErrorRecoveryJob(params);
            log.info("TikTokCronJobWrapper.tiktokGmvMaxErrorRecoveryJob end");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("TikTokCronJobWrapper.tiktokGmvMaxErrorRecoveryJob error", e);
            return ReturnT.FAIL;
        }
    }

    // todo chenheng 删除重试任务


}
