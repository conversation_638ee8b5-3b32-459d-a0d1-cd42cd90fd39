package com.meiyunji.sponsored.cron;

import com.alibaba.fastjson.JSONObject;
import com.amazon.advertising.attribution.AttributionAdvertisers;
import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.amazon.advertising.mode.productAd.ProductMetadata;
import com.amazon.advertising.productAd.ListProductMetadataAdsResponse;
import com.amazon.advertising.productAd.ProductAdClient;
import com.amazon.advertising.sb.mode.campaigm.CampaignV4;
import com.amazon.advertising.spV3.campaign.entity.CampaignExtendEntityV3;
import com.amazonaws.util.json.Jackson;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sellfox.aadas.api.enumeration.AmazonAdvertiseTypePb;
import com.meiyunji.sellfox.aadas.api.enumeration.MarketplacePb;
import com.meiyunji.sellfox.aadas.api.enumeration.RegionPb;
import com.meiyunji.sellfox.aadas.api.service.*;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.base.TFunction;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.cron.service.*;
import com.meiyunji.sponsored.cron.service.impl.KeywordLibServiceImpl;
import com.meiyunji.sponsored.cron.service.impl.SyncAmazonSpCampaignReportCurrencyTask;
import com.meiyunji.sponsored.kafka.producer.AdSyncAsinInfoKafkaProducer;
import com.meiyunji.sponsored.service.account.dao.*;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.adCampaign.enums.AdSyncRecord;
import com.meiyunji.sponsored.service.aggregationReport.dao.IAmazonAggregationReportScheduleDao;
import com.meiyunji.sponsored.service.aggregationReport.po.AmazonAggregationReportSchedule;
import com.meiyunji.sponsored.service.aggregationReport.service.AggregationProductReportScheduleService;
import com.meiyunji.sponsored.service.attribution.api.AttributionApiClient;
import com.meiyunji.sponsored.service.attribution.service.IAmazonAdAttributionPublisherService;
import com.meiyunji.sponsored.service.attribution.service.IAmazonAdAttributionReportService;
import com.meiyunji.sponsored.service.audiences.service.AmazonAudiencesApiService;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleExecuteRecordDao;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDao;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDeleteDao;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleTemplateDao;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleTemplate;
import com.meiyunji.sponsored.service.autoRule.vo.TimeRuleJson;
import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchTaskDao;
import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchTaskSupportDao;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchConstants;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchTask;
import com.meiyunji.sponsored.service.batchCreate.service.IBatchCreateRetryService;
import com.meiyunji.sponsored.service.budgetUsage.IAmazonAdBudgetUsageDayService;
import com.meiyunji.sponsored.service.cache.UserPlanTypeCacheService;
import com.meiyunji.sponsored.service.category.service.AmazonTargetCategoryApiService;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdProfileBo;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.AdSyncAsinInfoMessage;
import com.meiyunji.sponsored.service.cpc.dto.ProductReportParentAsinUpdateDto;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdInvoiceService;
import com.meiyunji.sponsored.service.cpc.service2.ICpcHistoryApiService;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbCampaignApiService;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdCampaignApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdSyncService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCampaignApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCampaignNeTargetingApiService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcUpdateAdAuthHelper;
import com.meiyunji.sponsored.service.cpc.vo.GroupPageParam;
import com.meiyunji.sponsored.service.dbcompare.dto.ScanUniqueIndexDto;
import com.meiyunji.sponsored.service.dbcompare.service.IDbCompareMonitorService;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.LocaleEnum;
import com.meiyunji.sponsored.service.enums.ShopTypeEnum;
import com.meiyunji.sponsored.service.enums.StateEnum;
import com.meiyunji.sponsored.service.index.service.BudgetIndexService;
import com.meiyunji.sponsored.service.kafka.ReportKafkaProducer;
import com.meiyunji.sponsored.service.localization.dao.IAmazonKeywordLocalizationScheduleDao;
import com.meiyunji.sponsored.service.localization.po.AmazonKeywordLocalizationSchedule;
import com.meiyunji.sponsored.service.localization.service.KeywordLocalizationScheduleService;
import com.meiyunji.sponsored.service.monitor.service.IAmazonAdListMonitorService;
import com.meiyunji.sponsored.service.post.service.IPostService;
import com.meiyunji.sponsored.service.product.dao.IAsinInfoDao;
import com.meiyunji.sponsored.service.product.util.Constant;
import com.meiyunji.sponsored.service.reportDiffMonitor.service.ReportDateDiffMonitorService;
import com.meiyunji.sponsored.service.reportDiffMonitor.service.ReportLevelDiffMonitorService;
import com.meiyunji.sponsored.service.reportImport.dao.ICpcReportsImportTaskScheduleDao;
import com.meiyunji.sponsored.service.reportImport.entity.CpcReportsImportTaskSchedule;
import com.meiyunji.sponsored.service.reportImport.message.AdReportImportMessage;
import com.meiyunji.sponsored.service.reportImport2.dao.IAmazonAdReportsImportTaskScheduleDao;
import com.meiyunji.sponsored.service.reportImport2.entity.AmazonAdReportsImportTaskSchedule;
import com.meiyunji.sponsored.service.reportImport2.vo.AmazonAdReportImportMessage;
import com.meiyunji.sponsored.service.sellfoxApi.IProductApi;
import com.meiyunji.sponsored.service.strategy.dao.impl.StrategyLimitConfigDao;
import com.meiyunji.sponsored.service.stream.enums.StreamConstants;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamRedisCountService;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamTaskRetryService;
import com.meiyunji.sponsored.service.sysMonitor.service.DorisDataStatisticsService;
import com.meiyunji.sponsored.service.system.po.UserSyncTypeEnum;
import com.meiyunji.sponsored.service.system.service.IUserSyncTimeService;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import com.meiyunji.sponsored.service.util.OkHttpClientUtil;
import com.meiyunji.sponsored.service.util.ThreadPoolMonitorUtil;
import com.meiyunji.sponsored.service.vo.ProductAdReportVo;
import com.meiyunji.sponsored.syncAd.service.IAdShopDataInitSyncService;
import com.meiyunji.sponsored.util.TaskThreadPoolUtil;
import com.xxl.job.core.log.XxlJobLogger;
import com.xxl.job.core.util.ShardingUtil;
import io.grpc.ManagedChannel;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.digest.MurmurHash3;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.pulsar.client.api.Producer;
import org.apache.pulsar.client.api.PulsarClientException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.meiyunji.sellfox.aadas.api.service.GetProfileResponsePb.GetProfileResponse;

@Service
@Slf4j
public class CronjobService {

    @Resource
    private IShopAuthDao shopAuthDao;

    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;

    @Autowired
    private ISlaveScVcShopAuthDao slaveShopAuthDao;


    @Autowired
    private ISlaveAmazonAdProfileDao slaveAmazonAdProfileDao;

    @Autowired
    private ICpcAdSyncService cpcAdSyncService;

    @Autowired
    private IUserSyncTimeService syncTimeService;

    @Autowired
    private CpcCampaignApiService cpcCampaignApiService;

    @Autowired
    private CpcSdCampaignApiService cpcSdCampaignApiService;

    @Autowired
    private CpcSbCampaignApiService cpcSbCampaignApiService;

    @Autowired
    private ICpcHistoryApiService cpcHistoryApiService;

    @Autowired
    private CpcCampaignNeTargetingApiService campaignNeTargetingApiService;

    @Autowired
    private IAmazonAdInvoiceService invoiceService;

    @Autowired
    private IAmazonAdSdProductReportDao sdProductReportDao;

    @Autowired
    private IProductApi productDao;

    @Autowired
    private IAmazonAdReportQueueDao queueDao;

    @Autowired
    private StrategyLimitConfigDao strategyLimitConfigDao;

    @Autowired
    private IAmazonAdAttributionPublisherService amazonAdAttributionPublisherService;

    @Autowired
    private IAmazonAdAttributionReportService amazonAdAttributionReportService;
    @Autowired
    private AttributionApiClient attributionApiClient;
    @Autowired
    private IAmazonAdProductReportDao amazonAdProductReportDao;
    @Autowired
    private ReportKafkaProducer adProductReportProducer;
    @Autowired
    private ICpcQueryKeywordReportDao cpcQueryKeywordReportDao;
    @Autowired
    private ReportKafkaProducer queryKeywordReportProducer;
    @Autowired
    private ICpcQueryTargetingReportDao cpcQueryTargetingReportDao;
    @Autowired
    private ICpcSbQueryKeywordReportDao cpcSbQueryKeywordReportDao;
    @Autowired
    private ReportKafkaProducer queryTargetingReportProducer;
    @Autowired
    @Qualifier("taskManagedChannel")
    private ManagedChannel taskManagedChannel;
    @Autowired
    private RedisService redisService;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAmazonAdProductMetadataDao amazonAdProductMetadataDao;
    private static final Random random = new Random();
    @Autowired
    private KeywordLocalizationScheduleService keywordLocalizationScheduleService;
    @Autowired
    private AggregationProductReportScheduleService aggregationProductReportScheduleService;
    @Autowired
    private IAmazonKeywordLocalizationScheduleDao amazonKeywordLocalizationScheduleDao;
    @Autowired
    private IAmazonAggregationReportScheduleDao amazonAggregationReportScheduleDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private AmazonTargetCategoryApiService amazonTargetCategoryApiService;
    @Autowired
    private ICpcReportsImportTaskScheduleDao cpcReportsImportTaskScheduleDao;

    @Autowired
    private IDbCompareMonitorService dbCompareMonitorService;

    @Resource(name = "sfAdReportImportProducer")
    private Producer<byte[]> sfAdReportImportProducer;

    @Resource(name = "shardingJdbcMap")
    private Map<String, JdbcTemplate> jdbcTemplateMap;

    @Resource(name = "vipJdbcTemplateMap")
    private Map<String, JdbcTemplate> vipJdbcTemplateMap;

    @Autowired
    private IAdvertiseAutoRuleTemplateDao advertiseAutoRuleTemplateDao;

    @Autowired
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;

    @Autowired
    private IAdvertiseAutoRuleStatusDeleteDao advertiseAutoRuleStatusDeleteDao;

    @Autowired
    private IAdvertiseAutoRuleExecuteRecordDao advertiseAutoRuleExecuteRecordDao;

    @Autowired
    private ISyncAmazonAdDataService iSyncAmazonAdDataService;

    @Autowired
    private IAmazonAdListMonitorService adListMonitorService;

    @Autowired
    private IAmazonAdInvoiceSyncStateDao amazonAdInvoiceSyncStateDao;

    @Autowired
    private CpcUpdateAdAuthHelper cpcUpdateAdAuthHelper;

    @Autowired
    private IAmazonAdReportsImportTaskScheduleDao amazonAdReportsImportTaskScheduleDao;

    @Resource(name = "amazonAdReportImportProducer")
    Producer<byte[]> amazonAdReportImportProducer;

    @Resource
    private CpcPageIdsHandler cpcPageIdsHandler;

    @Resource
    private IAmazonAdBatchTaskSupportDao amazonAdBatchTaskSupportDao;

    @Resource
    private IBatchCreateRetryService batchCreateRetryService;

    @Resource
    private IAmazonAdBatchTaskDao amazonAdBatchTaskDao;

    @Autowired
    private IDeleteHotTableDataService deleteHotTableDataService;

    @Autowired
    private IWordRootQueryDirtyDataDelService wordRootQueryDirtyDataDelService;

    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;

    @Autowired
    private IQueryWordStatisticsService queryWordStatisticsService;

    @Autowired
    private IDeleteAdAmazonOperationLog deleteAdAmazonOperationLog;

    @Autowired
    private IDeleteInvalidShopDataService deleteInvalidShopDataService;

    @Autowired
    public AmazonAudiencesApiService amazonAudiencesApiService;

    @Autowired
    private IInitPerspectiveHotAsinService initPerspectiveHotAsinService;

    @Autowired
    private IAdShopDataInitSyncService shopDataInitSyncService;

    @Resource
    private IAmazonManagementStreamTaskRetryService amazonManagementStreamTaskRetryService;

    @Autowired
    private IAmazonManagementStreamRedisCountService amazonManagementStreamRedisCountService;

    @Autowired
    private IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao;

    @Autowired
    private IAdManageOperationLogTask adManageOperationLogTask;

    @Autowired
    private StringRedisService stringRedisService;

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Autowired
    private BudgetIndexService budgetIndexService;
    @Autowired
    private ReportDateDiffMonitorService reportDateDiffMonitorService;
    @Autowired
    private ReportLevelDiffMonitorService reportLevelDiffMonitorService;

    @Autowired
    private IAdTargetTaskScheduleService adTargetTaskScheduleService;
    @Autowired
    private KeywordLibServiceImpl keywordLibService;

    @Autowired
    private IDeleteColdTableDataService deleteColdTableDataService;

    @Autowired
    private IAdManagePageExportTaskDelService adManagePageExportTaskDelService;
    @Autowired
    private IRepeatTargetingSyncKeywordSizeService repeatTargetingSyncKeywordSizeService;

    @Autowired
    private IAmazonAdBudgetUsageDayService amazonAdBudgetUsageDayService;

    @Autowired
    private SyncAmazonSpCampaignReportCurrencyTask syncAmazonSpCampaignReportCurrencyTask;
    @Resource
    private IAsinInfoDao asinInfoDao;
    @Resource
    private AdSyncAsinInfoKafkaProducer adSyncAsinInfoKafkaProducer;

    @Autowired
    private IPostService postsService;

    @Autowired
    private UserPlanTypeCacheService userPlanTypeCacheService;

    @Resource
    private IVcShopAuthDao vcShopAuthDao;

    @Autowired
    private DorisDataStatisticsService dorisDataStatisticsService;

    private final String url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=005bb35b-28b5-4580-b576-ce8941b6148f";
    private final String contextTitle = "mysql_name:t_shop_auth, 店铺token为空数据警告<font color=\"warning\">%s例</font>, 请相关同事注意。\n";
    private final String successListContext = "> 处理成功shopId:<font color=\"comment\">%s</font>, 处理成功条数:<font color=\"comment\">%s</font>";
    private final String failListContext = "> 处理失败shopId:<font color=\"comment\">%s</font>, 处理失败条数:<font color=\"comment\">%s</font>";
    private final String contextList = "> puid:<font color=\"comment\">%s</font>, shopId:<font color=\"comment\">%s</font>";
    private final String listTitle = "店铺token失效数据列";

    public void cpcAmazonAds(int shardIndex, int totalShard) throws InterruptedException {
        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getAllShopPoolExecutor();
        int start = 0;
        int shopLimit = 1000;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(null, null, start, shopLimit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shopAuth : shopAuths) {
                ThreadPoolUtil.waiting(threadExecutor);
                threadExecutor.execute(() -> {
                    try {
                        iSyncAmazonAdDataService.syncAmazonShop(shopAuth, AdSyncRecord.TriggerChannelEnum.SCHEDULE_TRIGGER.getChannel());
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
        ThreadPoolUtil.waitingFinish(threadExecutor);
    }

    public void cpcAmazonSp(int shardIndex, int totalShard) throws InterruptedException {
        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getAllShopPoolExecutor();
        int start = 0;
        int shopLimit = 1000;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(null, null, start, shopLimit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shopAuth : shopAuths) {
                ThreadPoolUtil.waiting(threadExecutor);
                threadExecutor.execute(() -> {
                    try {
                        iSyncAmazonAdDataService.syncAmazonSp(shopAuth, AdSyncRecord.TriggerChannelEnum.SCHEDULE_TRIGGER.getChannel());
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
        ThreadPoolUtil.waitingFinish(threadExecutor);
    }


    /**
     * 不开多线程 慢慢更新就可以
     * 线上
     */
    public void updateSbAdTargetType() {
        jdbcTemplateMap.forEach((k, value) -> updateSbAdTargetTypeById(value));
        vipJdbcTemplateMap.forEach((key, value) -> updateSbAdTargetTypeById(value));
    }

    private void updateSbAdTargetTypeById(JdbcTemplate jdbcTemplate) {
        List<Long> list = jdbcTemplate.queryForList("select id from t_amazon_ad_campaign_all where `type` = 'sb' AND ad_target_type = 'manual' limit 1", Long.class);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Long id = list.get(0);
        if (id == null) {
            log.info("无数据");
            return;
        }
        while (true) {
            List<Long> ids = jdbcTemplate.queryForList(" select id from t_amazon_ad_campaign_all where id >= " + id + " AND `type` = 'sb' AND ad_target_type = 'manual' limit 200", Long.class);
            if (CollectionUtils.isEmpty(ids)) {
                log.info("无id数据");
                return;
            }
            jdbcTemplate.update("UPDATE t_amazon_ad_campaign_all SET ad_target_type = 'sb_manual' WHERE id IN (" + ids.stream().map(Object::toString).collect(Collectors.joining(",")) + ") and `type` = 'sb' AND ad_target_type = 'manual' ");
            log.info("更新成功!");
            id = Collections.max(ids);
        }
    }

    public void cpcAmazonSb(int shardIndex, int totalShard) throws InterruptedException {
        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getAllShopPoolExecutor();
        int start = 0;
        int shopLimit = 1000;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(null, null, start, shopLimit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shopAuth : shopAuths) {
                ThreadPoolUtil.waiting(threadExecutor);
                threadExecutor.execute(() -> {
                    try {
                        iSyncAmazonAdDataService.syncAmazonSb(shopAuth, AdSyncRecord.TriggerChannelEnum.SCHEDULE_TRIGGER.getChannel());
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
        ThreadPoolUtil.waitingFinish(threadExecutor);
    }

    public void cpcAmazonSd(int shardIndex, int totalShard) throws InterruptedException {
        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getAllShopPoolExecutor();
        int start = 0;
        int shopLimit = 1000;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(null, null, start, shopLimit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shopAuth : shopAuths) {
                ThreadPoolUtil.waiting(threadExecutor);
                threadExecutor.execute(() -> {
                    try {
                        iSyncAmazonAdDataService.syncAmazonSd(shopAuth, AdSyncRecord.TriggerChannelEnum.SCHEDULE_TRIGGER.getChannel());
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
            if (size < shopLimit) {
                break;
            }
            start += size;
        }
        ThreadPoolUtil.waitingFinish(threadExecutor);
    }

    /**
     * 检测用户广告类型状态
     *
     * @param shardIndex
     * @param totalShard
     */
    public void detectAdType(int shardIndex, int totalShard, Integer limit) {
        long startTime = System.currentTimeMillis();
        List<ShopAuth> shopAuths;
        List<AmazonAdProfile> amazonAdProfiles;
        amazonAdProfiles = slaveAmazonAdProfileDao.getDataProfile(limit);
        amazonAdProfiles = amazonAdProfiles.stream().filter(s -> Math.abs(s.getShopId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(amazonAdProfiles)) {
            return;
        }
        List<Integer> collect = amazonAdProfiles.stream().map(AmazonAdProfile::getShopId).collect(Collectors.toList());
        shopAuths = slaveShopAuthDao.getScAndVcByIds(collect);
        Map<Integer, ShopAuth> shopAuthMap = shopAuths.stream().collect(Collectors.toMap(s -> s.getId(), Function.identity(), (s1, s2) -> s1));
        for (AmazonAdProfile amazonAdProfile : amazonAdProfiles) {
            boolean flag = true;
            try {
                ShopAuth shopAuth = shopAuthMap.get(amazonAdProfile.getShopId());
                if (shopAuth == null) {
                    continue;
                }
                try {
                    List<CampaignExtendEntityV3> spCampaignList = cpcAdSyncService.checkAdTypeSyncSpCampaign(shopAuth);
                    Boolean sp = null;
                    Boolean isSp = null;
                    if (spCampaignList == null) {
                        sp = false;
                        isSp = false;
                    }
                    if (spCampaignList != null && spCampaignList.size() == 0) {
                        sp = false;
                        isSp = true;
                    }
                    if (spCampaignList != null && spCampaignList.size() > 0) {
                        sp = true;
                        isSp = true;
                    }
                    if (sp != null && !sp) {
                        log.info("sp广告授权,该告类型没有活动shopId:{}", shopAuth.getId());
                    }
                    List<CampaignV4> sbCampaignList = cpcAdSyncService.checkAdTypeSyncSbCampaign(shopAuth);
                    Boolean sb = null;
                    Boolean isSb = null;
                    if (sbCampaignList == null) {
                        sb = false;
                        isSb = false;
                    }
                    if (sbCampaignList != null && sbCampaignList.size() == 0) {
                        sb = false;
                        isSb = true;
                    }
                    if (sbCampaignList != null && sbCampaignList.size() > 0) {
                        sb = true;
                        isSb = true;
                    }
                    if (sb != null && !sb) {
                        log.info("sb广告授权,该告类型没有活动shopId:{}", shopAuth.getId());
                    }
                    List<com.amazon.advertising.sd.mode.Campaign> sdCampaignList = cpcAdSyncService.checkAdTypeSyncSdCampaign(shopAuth);
                    Boolean sd = null;
                    Boolean isSd = null;
                    if (sdCampaignList == null) {
                        sd = false;
                        isSd = false;
                    }
                    if (sdCampaignList != null && sdCampaignList.size() == 0) {
                        sd = false;
                        isSd = true;
                    }
                    if (sdCampaignList != null && sdCampaignList.size() > 0) {
                        sd = true;
                        isSd = true;
                    }
                    if (sd != null && !sd) {
                        log.info("sd广告授权,该广告类型没有活动shopId:{}", shopAuth.getId());
                    }
                    amazonAdProfileDao.updateAdType(shopAuth.getPuid(), shopAuth.getId(), sp, sb, sd);
                    int normalDate = random.nextInt(20);
                    String redisKey = String.format(RedisConstant.SELLFOX_AD_AUTH_COUNT, shopAuth.getPuid(), shopAuth.getId());

                    if ((isSp || isSb || isSd)) {
                        LocalDateTime localDateTime = LocalDateTime.now().plusHours(12).plusMinutes(normalDate);
                        amazonAdProfileDao.updateAdAuthDate(shopAuth.getPuid(), shopAuth.getId(), localDateTime);
                        flag = false;
                        redisService.del(redisKey);
                        if (StringUtils.isBlank(shopAuth.getAdRefreshToken())) {
                            cpcUpdateAdAuthHelper.updateShopAdToken(shopAuth, amazonAdProfile);
                        }
                        if ("expire".equals(shopAuth.getAdStatus())) {
                            shopAuthDao.updateAdStatus(shopAuth.getPuid(), shopAuth.getId(), "auth");
                        }
                    }
                    if (!isSp && !isSb && !isSd) {
                        Long callCount = redisService.incr(redisKey, 1L);
                        LocalDateTime localDateTime = LocalDateTime.now().plusHours(1).plusMinutes(normalDate);
                        amazonAdProfileDao.updateAdAuthDate(shopAuth.getPuid(), shopAuth.getId(), localDateTime);
                        flag = false;
                        if (callCount >= 3) {
                            shopAuthDao.updateAdStatus(shopAuth.getPuid(), shopAuth.getId(), "expire");
                            redisService.del(redisKey);
                        }
                    }
                } catch (Exception e) {
                    log.error("puid={}  shopId={}  detectAdType:{}", shopAuth.getPuid(), shopAuth.getId(), e);
                }
            } finally {
                //如果接口报错更新实际
                if (flag) {
                    int normalDate = random.nextInt(20);
                    LocalDateTime localDateTime = LocalDateTime.now().plusHours(6).plusMinutes(normalDate);
                    amazonAdProfileDao.updateAdAuthDate(amazonAdProfile.getPuid(), amazonAdProfile.getShopId(), localDateTime);
                }
            }

        }
        log.info("同步检测用户广告类型状态 花费时间 {}", System.currentTimeMillis() - startTime);
    }

    /**
     * 检测用户广告类型状态
     *
     * @param shardIndex
     * @param totalShard
     */
    public void detectAdTypeV2(int shardIndex, int totalShard, Integer limit) {
        long startTime = System.currentTimeMillis();
        List<ShopAuth> shopAuths;
        List<AmazonAdProfile> amazonAdProfiles;
        amazonAdProfiles = slaveAmazonAdProfileDao.getDataProfile(limit);
        amazonAdProfiles = amazonAdProfiles.stream().filter(s -> Math.abs(s.getShopId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(amazonAdProfiles)) {
            return;
        }
        List<Integer> collect = amazonAdProfiles.stream().map(AmazonAdProfile::getShopId).collect(Collectors.toList());
        shopAuths = slaveShopAuthDao.getScAndVcByIds(collect);
        Map<Integer, ShopAuth> shopAuthMap = shopAuths.stream().collect(Collectors.toMap(s -> s.getId(), Function.identity(), (s1, s2) -> s1));
        for (AmazonAdProfile amazonAdProfile : amazonAdProfiles) {
            boolean flag = true;
            try {
                ShopAuth shopAuth = shopAuthMap.get(amazonAdProfile.getShopId());
                if (shopAuth == null) {
                    continue;
                }
                try {
                    AadasApiGrpc.AadasApiBlockingStub stub = AadasApiGrpc.newBlockingStub(taskManagedChannel);
                    GetProfileRequestPb.GetProfileRequest profileRequest = GetProfileRequestPb.GetProfileRequest.newBuilder()
                            .setSellerId(shopAuth.getSellingPartnerId())
                            .setMarketplace(MarketplacePb.Marketplace.valueOf(Marketplace.fromId(shopAuth.getMarketplaceId()).name())).build();
                    GetProfileResponse profile = stub.getProfile(profileRequest);
                    if (profile == null || !profile.hasProfile()) {
                        log.info("puid:{},shopId:{},detect failure profile not fund", shopAuth.getPuid(), shopAuth.getId());
                        continue;
                    }
                    if (!profile.getProfile().getIsEnabled()) {
                        log.info("puid:{},shopId:{},detect failure profile is not enabled", shopAuth.getPuid(), shopAuth.getId());
                        continue;
                    }
                    List<AmazonAdvertiseTypePb.AmazonAdvertiseType> activeTypesList = profile.getProfile().getActiveTypesList();

                    boolean sp = activeTypesList.contains(AmazonAdvertiseTypePb.AmazonAdvertiseType.SP);
                    boolean sb = activeTypesList.contains(AmazonAdvertiseTypePb.AmazonAdvertiseType.SB);
                    boolean sd = activeTypesList.contains(AmazonAdvertiseTypePb.AmazonAdvertiseType.SD);

                    amazonAdProfileDao.updateAdType(shopAuth.getPuid(), shopAuth.getId(), sp, sb, sd);
                    int normalDate = random.nextInt(20);
                    String redisKey = String.format(RedisConstant.SELLFOX_AD_AUTH_COUNT, shopAuth.getPuid(), shopAuth.getId());

                    if (profile.getProfile().getSyncActiveTypeErrorTimes() == 0) {
                        LocalDateTime localDateTime = LocalDateTime.now().plusHours(6).plusMinutes(normalDate);
                        amazonAdProfileDao.updateAdAuthDate(shopAuth.getPuid(), shopAuth.getId(), localDateTime);
                        flag = false;
                        redisService.del(redisKey);
                        if ("auth".equalsIgnoreCase(shopAuth.getAdStatus()) && StringUtils.isBlank(shopAuth.getAdRefreshToken())) {
                            cpcUpdateAdAuthHelper.updateShopAdToken(shopAuth, amazonAdProfile);
                        }
                        if ("expire".equals(shopAuth.getAdStatus())) {
                            if (ShopTypeEnum.VC.getCode().equals(shopAuth.getType())) {
                                vcShopAuthDao.updateAdStatus(shopAuth.getPuid(), shopAuth.getId(), "auth");
                            } else {
                                shopAuthDao.updateAdStatus(shopAuth.getPuid(), shopAuth.getId(), "auth");
                            }
                        }
                    }
                    if ("auth".equalsIgnoreCase(shopAuth.getAdStatus())) {
                        if (profile.getProfile().getSyncActiveTypeErrorTimes() > dynamicRefreshConfiguration.getShopAuthErrorCountMax()) {
                            Long callCount = redisService.incr(redisKey, 1L);
                            LocalDateTime localDateTime = LocalDateTime.now().plusMinutes(20).plusMinutes(normalDate);
                            amazonAdProfileDao.updateAdAuthDate(shopAuth.getPuid(), shopAuth.getId(), localDateTime);
                            flag = false;
                            if (callCount >= 3) {
                                if (ShopTypeEnum.VC.getCode().equals(shopAuth.getType())) {
                                    vcShopAuthDao.updateAdStatus(shopAuth.getPuid(), shopAuth.getId(), "expire");
                                } else {
                                    shopAuthDao.updateAdStatus(shopAuth.getPuid(), shopAuth.getId(), "expire");
                                }
                                redisService.del(redisKey);
                            }
                        }
                    }

                } catch (Exception e) {
                    log.error("puid={}  shopId={}  detectAdType:{}", shopAuth.getPuid(), shopAuth.getId(), e);
                }
            } finally {
                //如果接口报错更新实际
                if (flag) {
                    int normalDate = random.nextInt(20);
                    LocalDateTime localDateTime = LocalDateTime.now().plusHours(6).plusMinutes(normalDate);
                    amazonAdProfileDao.updateAdAuthDate(amazonAdProfile.getPuid(), amazonAdProfile.getShopId(), localDateTime);
                }
            }

        }
        log.info("同步检测用户广告类型状态 花费时间 {}", System.currentTimeMillis() - startTime);
    }

    public void detectActiveAdType(Integer hours) {
        long startTime = System.currentTimeMillis();
        List<AmazonAdProfile> profiles = slaveAmazonAdProfileDao.getByCreateTimeRange(LocalDateTime.now().minusHours(hours), LocalDateTime.now());
        //查询所有对应的店铺
        if (CollectionUtils.isNotEmpty(profiles)) {
            List<Integer> shopIds = profiles.stream().map(AmazonAdProfile::getShopId).distinct().collect(Collectors.toList());
            Map<Integer, AmazonAdProfile> profileMap = profiles.stream().collect(Collectors.toMap(AmazonAdProfile::getShopId, Function.identity(), (e1, e2) -> e2));
            if (CollectionUtils.isNotEmpty(shopIds)) {
                List<ShopAuth> shopAuths = slaveShopAuthDao.getScAndVcByIds(shopIds);
                for (ShopAuth shopAuth : shopAuths) {
                    try {
                        List<CampaignExtendEntityV3> spCampaignampaignList = cpcAdSyncService.detectAdTypeSyncSpCampaign(shopAuth);
                        Boolean sp = !CollectionUtils.isEmpty(spCampaignampaignList);
                        if (sp != null && !sp) {
                            log.info("sp广告授权,该告类型没有活动shopId:{}", shopAuth.getId());
                        }
                        List<CampaignV4> sbCampaignampaignList = cpcAdSyncService.detectAdTypeSyncSbCampaign(shopAuth);
                        Boolean sb = !CollectionUtils.isEmpty(sbCampaignampaignList);
                        if (sb != null && !sb) {
                            log.info("sb广告授权,该告类型没有活动shopId:{}", shopAuth.getId());
                        }
                        List<com.amazon.advertising.sd.mode.Campaign> sdCampaignList = cpcAdSyncService.detectAdTypeSyncSdCampaign(shopAuth);
                        Boolean sd = !CollectionUtils.isEmpty(sdCampaignList);
                        if (sd != null && !sd) {
                            log.info("sd广告授权,该广告类型没有活动shopId:{}", shopAuth.getId());
                        }
                        AmazonAdProfile amazonAdProfile = profileMap.get(shopAuth.getId());
                        sp = Integer.valueOf(1).equals(amazonAdProfile.getIsSp()) ? true : sp;
                        sb = Integer.valueOf(1).equals(amazonAdProfile.getIsSb()) ? true : sb;
                        sd = Integer.valueOf(1).equals(amazonAdProfile.getIsSd()) ? true : sd;
                        amazonAdProfileDao.updateAdType(shopAuth.getPuid(), shopAuth.getId(), sp, sb, sd);
                    } catch (Exception e) {
                        log.error("detectAdType:", e);
                    }
                }
            }
        }
        log.info("同步检测用户广告类型状态 花费时间 {}", System.currentTimeMillis() - startTime);
    }

    public void syncByShopProfileAndPortfolio(int shardIndex, int totalShard, Integer puid, Integer shopId) {
        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getCpcSpType3SyncPool();
        int start = 0;
        int limit = 100;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, limit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shop : shopAuths) {
                if (userPlanTypeCacheService.puidExpire(shop.getPuid())) {
                    log.warn("同步profile和portfolio数据, shopId: {}, puid: {}, 状态为-99, 停止同步", shop.getId(), shop.getPuid());
                    continue;
                }
                ThreadPoolUtil.waiting(threadExecutor);
                threadExecutor.execute(() -> {
                    try {
                        //先获取到配置信息
                        AmazonAdProfile amazonAdProfile = slaveAmazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
                        if (amazonAdProfile != null && (amazonAdProfile.getIsSp() == 1 || amazonAdProfile.getIsSb() == 1 || amazonAdProfile.getIsSd() == 1)) {
                            cpcAdSyncService.syncByShopProfileAndPortfolio(shop);
                        }
                    } catch (Exception e) {
                        log.error("cpcSync:", e);
                    }
                });
            }

            if (size < limit) {
                break;
            }

            start += size;
        }

        ThreadPoolUtil.waitingFinish(threadExecutor);
    }

    public void syncAmazonAdBudgetUsageData(int shardIndex, int totalShard, Integer puid, Integer shopId, TFunction<Integer, ShopAuth> function) {
        int start = 0;
        int limit = 100;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, limit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shop : shopAuths) {
                try {
                    // 是否开启多线同步 暂时可忽略
                    //先获取到配置信息
                    AmazonAdProfile amazonAdProfile = slaveAmazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
                    if (amazonAdProfile != null) {
                        function.apply(shop.getPuid(), shop);
                    }
                } catch (Exception e) {
                    log.error("syncAmazonAdBudgetUsageData:", e);
                }
            }
            if (size < limit) {
                break;
            }
            start += size;
        }
    }

    public void syncAmazonAdBudgetUsageByDay(int shardIndex, int totalShard, Integer puid, Integer shopId, LocalDate startDate, LocalDate endDate) {
        final long day = ChronoUnit.DAYS.between(startDate, endDate);
        int start = 0;
        int limit = 100;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, limit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shop : shopAuths) {
                try {
                    // 是否开启多线同步 暂时可忽略
                    AmazonAdProfile amazonAdProfile = slaveAmazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
                    if (amazonAdProfile != null) {
                        amazonAdBudgetUsageDayService.syncAmazonAdBudgetUsageByDay(shop.getPuid(), shop, startDate, day);
                    }
                } catch (Exception e) {
                    log.error("syncAmazonAdBudgetUsageData:", e);
                }
            }
            if (size < limit) {
                break;
            }
            start += size;
        }
    }

    public void syncAmazonSpCampaignReportCurrency(int shardIndex, int totalShard, Integer puid, Integer shopId, LocalDate startDate, LocalDate endDate) {
        final long day = ChronoUnit.DAYS.between(startDate, endDate);
        int start = 0;
        int limit = 100;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, limit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shop : shopAuths) {
                try {
                    // 是否开启多线同步 暂时可忽略
                    AmazonAdProfile amazonAdProfile = slaveAmazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
                    if (amazonAdProfile != null) {
                        syncAmazonSpCampaignReportCurrencyTask.dealReportCurrencyCode(shop.getPuid(), shop, startDate, day);
                    }
                } catch (Exception e) {
                    log.error("syncAmazonAdBudgetUsageData:", e);
                }
            }
            if (size < limit) {
                break;
            }
            start += size;
        }
    }

    public void cpcSdSync(int shardIndex, int totalShard, Integer puid, Integer shopId, String campaignId, String type) {

        ThreadPoolExecutor threadExecutor;
        if ("1".equals(type)) {
            threadExecutor = ThreadPoolUtil.getCpcSdType1SyncPool();
        } else if ("2".equals(type)) {
            threadExecutor = ThreadPoolUtil.getCpcSdType2SyncPool();
        } else if ("3".equals(type)) {
            threadExecutor = ThreadPoolUtil.getCpcSdType3SyncPool();
        } else if ("4".equals(type)) {
            threadExecutor = ThreadPoolUtil.getCpcSdType4SyncPool();
        } else if ("5".equals(type)) {
            threadExecutor = ThreadPoolUtil.getCpcSdType5SyncPool();
        } else {
            threadExecutor = ThreadPoolUtil.getCpcSdSyncPool();
        }
        int start = 0;
        int limit = 100;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, limit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shop : shopAuths) {
                if (userPlanTypeCacheService.puidExpire(shop.getPuid())) {
                    log.warn("同步sd数据, shopId: {}, puid: {}, 状态为-99, 停止同步", shop.getId(), shop.getPuid());
                    continue;
                }
                ThreadPoolUtil.waiting(threadExecutor);
                threadExecutor.execute(() -> {
                    try {
                        //先获取到配置信息
                        AmazonAdProfile amazonAdProfile = slaveAmazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
                        if (amazonAdProfile != null && amazonAdProfile.getIsSd() == 1) {
                            cpcAdSyncService.syncSdByShop(shop, campaignId, type);
                            syncTimeService.saveOrUpdate(shop.getPuid(), shop.getId(), shop.getMarketplaceId(), UserSyncTypeEnum.AD_SD_MANAGE);
                        }
                    } catch (Exception e) {
                        log.error("cpcSdSync:", e);
                    }
                });
            }

            if (size < limit) {
                break;
            }

            start += size;
        }

        ThreadPoolUtil.waitingFinish(threadExecutor);
    }

    public void cpcSbSync(int shardIndex, int totalShard, Integer puid, Integer shopId, String campaignId, String type) {

        ThreadPoolExecutor threadExecutor;
        if ("1".equals(type)) {
            threadExecutor = ThreadPoolUtil.getCpcSbType1SyncPool();
        } else if ("2".equals(type)) {
            threadExecutor = ThreadPoolUtil.getCpcSbType2SyncPool();
        } else if ("3".equals(type)) {
            threadExecutor = ThreadPoolUtil.getCpcSbType3SyncPool();
        } else {
            threadExecutor = ThreadPoolUtil.getCpcSbSyncPool();
        }

        int start = 0;
        int limit = 100;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, limit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shopAuth : shopAuths) {
                if (userPlanTypeCacheService.puidExpire(shopAuth.getPuid())) {
                    log.warn("同步sb数据, shopId: {}, puid: {}, 状态为-99, 停止同步", shopAuth.getId(), shopAuth.getPuid());
                    continue;
                }
                ThreadPoolUtil.waiting(threadExecutor);
                threadExecutor.execute(() -> {
                    try {
                        //先获取到配置信息
                        AmazonAdProfile amazonAdProfile = slaveAmazonAdProfileDao.getProfile(shopAuth.getPuid(), shopAuth.getId());
                        if (amazonAdProfile != null && amazonAdProfile.getIsSb() == 1) {
                            cpcAdSyncService.syncSbByShop(shopAuth, campaignId, type);
                            syncTimeService.saveOrUpdate(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId(), UserSyncTypeEnum.AD_SB_MANAGE);
                        }
                    } catch (Exception e) {
                        log.error("syncSbAd:", e);
                    }
                });
            }

            if (size < limit) {
                break;
            }

            start += size;
        }
        ThreadPoolUtil.waitingFinish(threadExecutor);
    }

    private Date convertDateToSite(Date date, String marketplaceId) {
        if (date == null) {
            return null;
        }
        MarketTimezoneAndCurrencyEnum cpcTime = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(marketplaceId);
        if (cpcTime == null) {
            return null;
        }
        ZoneId targetZone = ZoneId.of(cpcTime.getTimezone());
        LocalDateTime localDateTime = LocalDateTimeUtil.convertDateToLDT(date);
        localDateTime = LocalDateTimeUtil.getZoneTime(localDateTime, ZoneId.systemDefault(), targetZone);
        return LocalDateTimeUtil.convertLDTToDate(localDateTime);
    }

    // 按时区生成今天的任务
    private Integer setDateList(Date siteDate, String type, List<String> dateList) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(siteDate);                         //放入Date类型数据
        int hours = calendar.get(Calendar.HOUR_OF_DAY);  //时（24小时制）

        Integer priority = null;
        switch (type) {
            case "0":   //站点执行时间
                //同步昨天的, 每天执行 0、6、12、18点
                if (hours == 0 || hours == 6 || hours == 12 || hours == 18) {
                    dateList.add(DateUtil.dateToStrWithFormat(DateUtil.addDay(siteDate, -1), "yyyyMMdd"));
                }
                priority = 40;
                break;
            case "1": //站点执行时间
                //同步最近2-3天的, 每天执行 0、6、12、18点
                if (hours == 0 || hours == 6 || hours == 12 || hours == 18) {
                    for (int i = 2; i <= 3; i++) {
                        dateList.add(DateUtil.dateToStrWithFormat(DateUtil.addDay(siteDate, -i), "yyyyMMdd"));
                    }
                }
                priority = 30;
                break;
            case "2": //站点执行时间
                //同步最近4-8天的, 每天执行 1、18点
                if (hours == 1 || hours == 18) {
                    for (int i = 4; i <= 8; i++) {
                        dateList.add(DateUtil.dateToStrWithFormat(DateUtil.addDay(siteDate, -i), "yyyyMMdd"));
                    }
                }
                priority = 20;
                break;
            case "3": //站点执行时间
                //同步最近9-15天的, 每两天执行一次 3点
                if (hours == 3) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Date oldDate;
                    try {
                        oldDate = sdf.parse("2021-08-30");
                    } catch (ParseException e) {
                        return null;
                    }

                    int day = DateUtil.daysBetween(oldDate, siteDate);//站点时间跟指定日期比较,计算天数差值
                    if (day >= 2 && day % 2 == 0) {
                        for (int i = 9; i <= 15; i++) {
                            dateList.add(DateUtil.dateToStrWithFormat(DateUtil.addDay(siteDate, -i), "yyyyMMdd"));
                        }
                    }
                }
                priority = 10;
                break;
        }

        return priority;
    }

    /**
     * 同步sp,sb,sd广告活动信息(分片)
     *
     * @param index
     * @param total
     */
    public void syncCampaignInfo(int index, int total, Integer puid, Integer shopId, String campaignId) {
        long start = System.currentTimeMillis();
        //取出所有门店来跑数据
        List<ShopAuth> shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, 0, Integer.MAX_VALUE);
        shopAuths = shopAuths.stream().filter(Objects::nonNull).filter(s -> Math.abs(s.getId().hashCode() % total) == index).collect(Collectors.toList());

        CountDownLatch latch = new CountDownLatch(shopAuths.size());
        shopAuths.stream().forEach(shop -> {
            long t1 = System.currentTimeMillis();
            ThreadPoolUtil.getBatchAdManagePool().execute(() -> {
                try {
                    log.info("puid={} shopId={} 开始同步广告信息活动", shop.getPuid(), shop.getId());
                    cpcCampaignApiService.syncCampaigns(shop, campaignId, false);
                    log.info("puid={} shopId={} sp广告活动信息同步完成 花费时间 {}", shop.getPuid(), shop.getId(), System.currentTimeMillis() - t1);
                    cpcSdCampaignApiService.syncCampaigns(shop, campaignId, false);
                    log.info("puid={} shopId={} sb广告活动信息同步完成  花费时间 {}", shop.getPuid(), shop.getId(), System.currentTimeMillis() - t1);
                    cpcSbCampaignApiService.syncCampaignsV4(shop, campaignId, false);
                    log.info("puid={} shopId={} sd广告活动信息同步完成  花费时间 {}", shop.getPuid(), shop.getId(), System.currentTimeMillis() - t1);

                } catch (Exception e) {
                    log.info("puid={} shopId={} 同步广告信息活动异常 {}", shop.getPuid(), shop.getId(), e);
                } finally {
                    latch.countDown();
                }
            });
        });
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("同步广告活动信息 latch await error {}", e);
        }

        log.info("同步广告活动信息总耗时:{} ", System.currentTimeMillis() - start);
    }

    /**
     * 再次同步超预算广告日志
     *
     * @param index
     * @param total
     */
    public void syncOutOfBudgetHistoryAgain(int index, int total) {
        long start = System.currentTimeMillis();
        //取出所有门店来跑数据
        List<ShopAuth> shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(null, null, 0, Integer.MAX_VALUE);
        shopAuths = shopAuths.stream().filter(Objects::nonNull).filter(s -> Math.abs(s.getId().hashCode() % total) == index).collect(Collectors.toList());

        CountDownLatch latch = new CountDownLatch(shopAuths.size());
        shopAuths.stream().forEach(shop -> {
            long t1 = System.currentTimeMillis();
            ThreadPoolUtil.getBatchAdManagePool().execute(() -> {
                try {
                    cpcHistoryApiService.syncOutOfBudgetCampainsAgain(shop);
                } catch (Exception e) {
                    log.info("puid={} shopId={} 再次同步超预算广告日志异常{}", shop.getPuid(), shop.getId(), e);
                } finally {
                    latch.countDown();
                }
            });
        });
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("再次同步超预算广告日志异常 latch await error {}", e);
        }

        log.info("再次同步超预算广告日志异常总耗时:{} ", System.currentTimeMillis() - start);
    }

    /**
     * 分片同步投放产生的搜索词报告里是asin图片、标题
     * 从平台同步下来没有这些信息，需要单独同步
     *
     * @param shardIndex:
     * @param totalShard:
     */
    public void shardSyncQueryTargetingReportAsinImage(int shardIndex, int totalShard) {
        int start = 0;
        int limit = 100;
        // 默认三天
        String startDate = LocalDate.now().minusDays(3).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String endDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(null, null, start, limit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shopAuth : shopAuths) {
                try {
                    cpcAdSyncService.syncQueryTargetingReportAsinImage(shopAuth, startDate, endDate, null, 2000);
                } catch (Exception e) {
                    log.error("shardSyncQueryTargetingReportAsinImage:", e);
                }
            }

            if (size < limit) {
                break;
            }

            start += size;
        }
    }

    /**
     * 同步投放产生的搜索词报告里是asin图片、标题
     * 从平台同步下来没有这些信息，需要单独同步
     *
     * @param shopId:
     */
    public void syncQueryTargetingReportAsinImage(Integer puid, Integer shopId, String startDate, String endDate, String campaignId, long blockingTime) {
        if (StringUtils.isEmpty(startDate) || StringUtils.isBlank(endDate)) {
            return;
        }

        int start = 0;
        int limit = 100;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, limit);
            for (ShopAuth shopAuth : shopAuths) {
                try {
                    cpcAdSyncService.syncQueryTargetingReportAsinImage(shopAuth, startDate, endDate, campaignId, blockingTime);
                } catch (Exception e) {
                    log.error("syncQueryTargetingReportAsinImage:", e);
                }
            }

            if (shopAuths.size() < limit) {
                break;
            }

            start += shopAuths.size();
        }
    }

    /**
     * 分片同步asin报告下的产品图片
     * 从平台同步下来没有这些信息，需要单独同步
     *
     * @param shardIndex:
     * @param totalShard:
     */
    public void shardSyncAsinReportImage(int shardIndex, int totalShard, String params) {
        int start = 0;
        int limit = 100;

        // 默认三天
        String startDate = LocalDate.now().minusDays(3).format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        String startDateStr = LocalDate.now().minusDays(3).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        Integer puid = null;
        Integer shopId = null;
        String campaignId = null;
        if (org.apache.commons.lang3.StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                startDate = jsonObject.getString("startDate");
                startDateStr = jsonObject.getString("startDateStr");
                campaignId = jsonObject.getString("campaignId");
            }
        }

        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, limit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shopAuth : shopAuths) {
                try {
                    cpcAdSyncService.syncAsinReportAsinImage(shopAuth, startDate, campaignId);
                    cpcAdSyncService.syncAsinReportOtherAsinImage(shopAuth, startDate, campaignId);
                    cpcAdSyncService.syncAsinReportKeywordAsinImage(shopAuth, startDate, campaignId);
                    cpcAdSyncService.syncAsinReportKeywordOtherAsinImage(shopAuth, startDate, campaignId);
                    campaignNeTargetingApiService.syncCampaignNeTargetingAsinInfoByShop(shopAuth, startDateStr, campaignId);
                } catch (Exception e) {
                    log.error("shardSyncAsinReportImage:", e);
                }
            }

            if (size < limit) {
                break;
            }

            start += size;
        }
    }

    /**
     * 同步投放下的产品信息：图片、标题
     * 从平台同步下来的target没有这些信息，需要单独同步
     *
     * @param shopId:
     */
    public void syncTargetAsinInfo(ShardingUtil.ShardingVO shardingVO, Integer puid, Integer shopId) {
        int limit = 100;
        int start = 0;

        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, limit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % shardingVO.getTotal()) == shardingVO.getIndex()).collect(Collectors.toList());
            for (ShopAuth shopAuth : shopAuths) {
                try {

                    try {
                        cpcAdSyncService.syncTargetAsinInfo(shopAuth);
                    } catch (Exception e) {
                        log.error(String.format("同步sp投放图片报错,puid:%d,shopId:%d", shopAuth.getPuid(), shopAuth.getId()), e);
                    }
                    try {
                        cpcAdSyncService.syncSdTargetingAsinInfo(shopAuth, 0);
                    } catch (Exception e) {
                        log.error(String.format("同步sd投放图片报错,puid:%d,shopId:%d", shopAuth.getPuid(), shopAuth.getId()), e);
                    }
                    try {
                        cpcAdSyncService.syncSbTargetingAsinInfo(shopAuth, 0);
                    } catch (Exception e) {
                        log.error(String.format("同步sb投放图片报错,puid:%d,shopId:%d", shopAuth.getPuid(), shopAuth.getId()), e);
                    }

                } catch (Exception e) {
                    log.error("shardSyncAsinReportImage:", e);
                }
            }

            if (size < limit) {
                break;
            }

            start += size;
        }

    }

    public void syncInvoice(Integer puid, Integer shopId, String invoiceId, String updateMonth, int index, int total) {

        int start = 0;
        int limit = 100;
        List<ShopAuth> shopAuths;
        ThreadPoolExecutor syncInvoicePool = ThreadPoolUtil.getSyncInvoicePool();
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, limit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(Objects::nonNull).filter(s -> Math.abs(s.getId().hashCode() % total) == index).collect(Collectors.toList());
            for (ShopAuth shopAuth : shopAuths) {
                ThreadPoolUtil.waiting(syncInvoicePool);
                syncInvoicePool.execute(() -> {
                    try {
                        if (StringUtils.isNotBlank(invoiceId)) {
                            invoiceService.syncInvoicesDetails(shopAuth, invoiceId);
                        } else {
                            invoiceService.syncInvoiceList(shopAuth, updateMonth);
                        }
                    } catch (Exception e) {
                        log.error("syncInvoice:", e);
                    }
                });
            }

            if (size < limit) {
                break;
            }

            start += size;
        }
        ThreadPoolUtil.waitingFinish(syncInvoicePool);
    }

    // sp同步在线产品数据
    public void syncSpAdParentAsin(Integer puid, Integer shopId, String sku, Integer days) {
        if (days == null) {
            days = 10;  //默认只同步十天前数据
        }
        XxlJobLogger.log(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " days: %d", days));
        log.info(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " days: %d", days));

        Date date = DateUtil.addDay(new Date(), -days);
        List<AmazonAdProfile> profiles = new ArrayList<>();
        if (puid != null && shopId != null) {
            profiles.add(slaveAmazonAdProfileDao.getProfile(puid, shopId));
        } else {
            profiles = slaveAmazonAdProfileDao.listAll();
        }
        XxlJobLogger.log(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " profiles size: %d", profiles.size()));
        log.info(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " profiles size: %d", profiles.size()));
        List<String> skuList;
        if (CollectionUtils.isNotEmpty(profiles)) {
            int i = 1;
            for (AmazonAdProfile profile : profiles) {
                long updateSize = 0;
                XxlJobLogger.log(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " sync puid/shopId %d/%d ,profile index: %d/%d", profile.getPuid(), profile.getShopId(), i, profiles.size()));
                log.info(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " sync puid/shopId %d/%d ,profile index: %d/%d", profile.getPuid(), profile.getShopId(), i++, profiles.size()));
                List<AmazonAdProductReport> list = amazonAdProductReportDao
                        .getProductListByParentAsinIsNull(profile.getPuid(), profile.getShopId(), profile.getMarketplaceId(), date); // 需要修改父asin的数据
                XxlJobLogger.log(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " wait sync list size: %d ", list.size()));
                log.info(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " wait sync list size: %d ", list.size()));

                List<ProductAdReportVo> voList;  // 在线产品数据
                if (CollectionUtils.isNotEmpty(list)) {
                    List<List<AmazonAdProductReport>> partitionList = Lists.partition(list, dynamicRefreshConfiguration.getParentAsinSyncPartition());
                    for (List<AmazonAdProductReport> reportList : partitionList) {
                        skuList = new ArrayList<>();
                        for (AmazonAdProductReport productReport : reportList) {
                            if (StringUtils.isNotBlank(sku)) {
                                skuList.add(sku);
                                break;
                            }
                            if (!skuList.contains(productReport.getSku())) {
                                skuList.add(productReport.getSku());
                            }
                        }
                        voList = productDao.getListProductBySkuList(profile.getPuid(), profile.getShopId(), StringUtils.join(skuList, ","));

                        if (CollectionUtils.isNotEmpty(voList)) {  // 入库
                            Map<String, ProductAdReportVo> voMap = new HashMap<>();
                            for (ProductAdReportVo vo : voList) {
                                if (!voMap.containsKey(vo.getSku())) {
                                    voMap.put(vo.getSku(), vo);
                                }
                            }

                            List<AmazonAdProductReport> newList = new ArrayList<>();

                            for (AmazonAdProductReport productReport2 : reportList) {
                                if (voMap.containsKey(productReport2.getSku())) {   ////去产品获取parentAsin的信息
                                    ProductAdReportVo productAdReportVo = voMap.get(productReport2.getSku());
                                    if (productAdReportVo != null) {
                                        if (Constant.PRODUCT_SINGLE.equals(productAdReportVo.getIsVariation())) {
                                            productReport2.setParentAsin(productAdReportVo.getAsin());
                                        } else {
                                            productReport2.setParentAsin(productAdReportVo.getParentAsin());
                                        }
                                        newList.add(productReport2);
                                    }
                                }
                            }

                            //批量更新
                            if (CollectionUtils.isNotEmpty(newList)) {
                                updateSize = updateSize + newList.size();
                                //批量更新数据(分200条一批更新)
                                List<List<AmazonAdProductReport>> lists = Lists.partition(newList, 200);

                                for (List<AmazonAdProductReport> subList : lists) {
                                    try {
                                        amazonAdProductReportDao.batchUpdateParentAsin(profile.getPuid(), profile.getShopId(), subList);
                                    } catch (Exception e) {
                                        XxlJobLogger.log(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " error");
                                        log.error(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " error", e);
                                    }
                                }
                            }
                        }
                    }
                }
                XxlJobLogger.log(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " update size: %d", updateSize));
                log.info(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " update size: %d", updateSize));
            }
        }

    }

    // sd同步在线产品数据
    public void syncSdAdParentAsin(Integer puid, Integer shopId, String sku, Integer days) {

        if (days == null) {
            days = 10;  //默认只同步十天前数据
        }
        Date date = DateUtil.addDay(new Date(), -days);

        List<AmazonAdSdProductReport> productList = sdProductReportDao.getListByParentAsinIsNull(puid, shopId, sku, date);  //找出需要跑数据的店铺
        List<String> skuList;
        if (CollectionUtils.isNotEmpty(productList)) {
            for (AmazonAdSdProductReport product : productList) {
                List<AmazonAdSdProductReport> list = sdProductReportDao.getProductListByParentAsinIsNull(product.getPuid(), product.getShopId(), date); // 需要修改父asin的数据

                List<ProductAdReportVo> voList;  // 在线产品数据
                if (CollectionUtils.isNotEmpty(list)) {
                    skuList = new ArrayList<>();
                    for (AmazonAdSdProductReport productReport : list) {
                        if (StringUtils.isNotBlank(sku)) {
                            skuList.add(sku);
                            break;
                        }
                        if (!skuList.contains(productReport.getSku())) {
                            skuList.add(productReport.getSku());
                        }
                    }
                    voList = productDao.getListProductBySkuList(product.getPuid(), product.getShopId(), StringUtils.join(skuList, ","));

                    if (CollectionUtils.isNotEmpty(voList)) {  // 入库
                        Map<String, ProductAdReportVo> voMap = new HashMap<>();
                        for (ProductAdReportVo vo : voList) {
                            if (!voMap.containsKey(vo.getSku())) {
                                voMap.put(vo.getSku(), vo);
                            }
                        }

                        List<AmazonAdSdProductReport> newList = new ArrayList<>();

                        for (AmazonAdSdProductReport productReport2 : list) {
                            if (voMap.containsKey(productReport2.getSku())) {   ////去产品获取parentAsin的信息
                                ProductAdReportVo productAdReportVo = voMap.get(productReport2.getSku());
                                if (productAdReportVo != null) {
                                    if (Constant.PRODUCT_SINGLE.equals(productAdReportVo.getIsVariation())) {
                                        productReport2.setParentAsin(productAdReportVo.getAsin());
                                    } else {
                                        productReport2.setParentAsin(productAdReportVo.getParentAsin());
                                    }
                                    newList.add(productReport2);
                                }
                            }
                        }

                        //批量更新
                        if (CollectionUtils.isNotEmpty(newList)) {
                            //批量更新数据(分200条一批更新)
                            List<List<AmazonAdSdProductReport>> lists = Lists.partition(newList, 200);

                            for (List<AmazonAdSdProductReport> subList : lists) {
                                sdProductReportDao.batchUpdateParentAsin(product.getPuid(), product.getShopId(), subList);
                            }
                        }
                    }
                }
            }
        }

    }

    // sp同步在线产品数据
    public void syncSpAdParentAsinInit(int shardIndex, int totalShard, Integer puid, Integer shopId, Integer days) {
        if (days == null) {
            days = 30;  //默认只同步三十天前数据
        }
        XxlJobLogger.log(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " days: %d", days));
        log.info(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " days: %d", days));
        Date date = DateUtil.addDay(new Date(), -days);
        String dateStr = DateUtil.dateToStrWithTime(date, DateUtil.PATTERN_YYYYMMDD);

        List<AmazonAdProfileBo> profiles = new ArrayList<>();
        if (puid != null && shopId != null) {
            profiles.add(slaveAmazonAdProfileDao.listAmazonAdProfileBoByShopId(puid, shopId));
        } else {
            profiles = slaveAmazonAdProfileDao.listAmazonAdProfileBo();
        }
        XxlJobLogger.log(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " total profiles size: %d", profiles.size()));
        log.info(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " total profiles size: %d", profiles.size()));

        //分片
        profiles = profiles.stream().filter(s -> Math.abs(s.getShopId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());

        XxlJobLogger.log(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " shard profiles size: %d", profiles.size()));
        log.info(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " shard profiles size: %d", profiles.size()));
        Set<String> skuSet;
        if (CollectionUtils.isNotEmpty(profiles)) {
            AmazonAdProfileBo profile;
            for (int i = 0; i < profiles.size(); i++) {
                profile = profiles.get(i);
                XxlJobLogger.log(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " sync puid/shopId %d/%d ,profile index: %d/%d", profile.getPuid(), profile.getShopId(), (i + 1), profiles.size()));
                log.info(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " sync puid/shopId %d/%d ,profile index: %d/%d", profile.getPuid(), profile.getShopId(), (i + 1), profiles.size()));

                long updateSize = 0;
                long start = 0;
                long shopLimit = 500;
                while (true) {
                    // 需要修改父asin的数据
                    List<String> reportSkus = amazonAdProductReportDao.getSkuPageByDate(profile.getPuid(), profile.getShopId(), profile.getMarketplaceId(), start, shopLimit, dateStr);
                    XxlJobLogger.log(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " wait sync reportSkus size: %d ", reportSkus.size()));
                    log.info(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " wait sync reportSkus size: %d ", reportSkus.size()));
                    int size = reportSkus.size();

                    if (CollectionUtils.isNotEmpty(reportSkus)) {
                        // 在线产品数据
                        List<ProductAdReportVo> voList;
                        List<List<String>> partitionReportSkus = Lists.partition(reportSkus, dynamicRefreshConfiguration.getParentAsinSyncPartition());
                        for (List<String> skus : partitionReportSkus) {
                            skuSet = new HashSet<>(skus);
                            //获取在线产品信息
                            voList = productDao.getListProductBySkuList(profile.getPuid(), profile.getShopId(), StringUtils.join(skuSet, ","));
                            // 入库
                            if (CollectionUtils.isNotEmpty(voList)) {
                                Map<String, ProductAdReportVo> voMap = voList.stream().collect(Collectors.toMap(ProductAdReportVo::getSku, Function.identity(), (e1, e2) -> e1));
                                List<ProductReportParentAsinUpdateDto> updateList = new ArrayList<>();
                                ProductReportParentAsinUpdateDto updateDto;
                                for (String sku : skus) {
                                    //去产品获取parentAsin的信息
                                    if (voMap.containsKey(sku)) {
                                        ProductAdReportVo productAdReportVo = voMap.get(sku);
                                        if (productAdReportVo != null) {
                                            updateDto = new ProductReportParentAsinUpdateDto();
                                            if (Constant.PRODUCT_SINGLE.equals(productAdReportVo.getIsVariation())) {
                                                updateDto.setParentAsin(productAdReportVo.getAsin());
                                            } else {
                                                updateDto.setParentAsin(productAdReportVo.getParentAsin());
                                            }
                                            updateDto.setSku(sku);
                                            updateList.add(updateDto);
                                        }
                                    }
                                }

                                //批量更新
                                if (CollectionUtils.isNotEmpty(updateList)) {
                                    updateSize = updateSize + updateList.size();
                                    //批量更新数据
                                    try {
                                        amazonAdProductReportDao.batchUpdateParentAsin(profile.getPuid(), profile.getShopId(), profile.getMarketplaceId(), updateList, dateStr);
                                    } catch (Exception e) {
                                        XxlJobLogger.log(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " error:" + e.getMessage());
                                        log.error(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " error", e);
                                    }
                                }
                            }
                        }
                    }
                    if (size < shopLimit) {
                        break;
                    }
                    start += size;
                }
                XxlJobLogger.log(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " update size: %d", updateSize));
                log.info(String.format(Constants.SYNC_SP_AD_PARENT_ASIN_INIT + " update size: %d", updateSize));
            }
        }
    }

    //******************************************************************* 测试脚本
    public void detectAdTypeTest(Integer puid, Integer shopId) {
        if (puid == null && shopId == null) {
            log.info("参数有误");
            return;
        }

        long startTime = System.currentTimeMillis();
        int start = 0;
        int limit = 200;
        List<ShopAuth> shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, limit);

        for (ShopAuth shopAuth : shopAuths) {
            try {
                List<CampaignExtendEntityV3> spCampaignList = cpcAdSyncService.detectAdTypeSyncSpCampaign(shopAuth);
                Boolean sp = !CollectionUtils.isEmpty(spCampaignList);
                if (sp != null && !sp) {
                    log.info("sp广告授权,该告类型没有活动shopId:{}", shopAuth.getId());
                }
                List<CampaignV4> sbCampaignList = cpcAdSyncService.detectAdTypeSyncSbCampaign(shopAuth);
                Boolean sb = !CollectionUtils.isEmpty(sbCampaignList);
                if (sb != null && !sb) {
                    log.info("sb广告授权,该告类型没有活动shopId:{}", shopAuth.getId());
                }
                List<com.amazon.advertising.sd.mode.Campaign> sdCampaignList = cpcAdSyncService.detectAdTypeSyncSdCampaign(shopAuth);
                Boolean sd = !CollectionUtils.isEmpty(sdCampaignList);
                if (sd != null && !sd) {
                    log.info("sd广告授权,该广告类型没有活动shopId:{}", shopAuth.getId());
                }
                amazonAdProfileDao.updateAdType(shopAuth.getPuid(), shopAuth.getId(), sp, sb, sd);
            } catch (Exception e) {
                log.error("detectAdType:", e);
            }
        }
        log.info("同步检测用户广告类型状态 花费时间 {}", System.currentTimeMillis() - startTime);
    }

    public Result<String> statisticsExecResultCount(String params) {

        if (StringUtils.isNotBlank(params)) {
            queueDao.updateSpQueueData();
            queueDao.updateSbQueueData();
            queueDao.updateSdQueueData();
        } else {
            int count = queueDao.statisticsExecResultCount();
            if (count > 0) {
                return ResultUtil.returnErr("队列执行异常,待执行数量:" + count);
            }
        }

        return ResultUtil.success();
    }

    public void syncAttributionPublishers() {
        amazonAdAttributionPublisherService.syncAttributionPublisher();
    }

    public void syncAttributionReport(Integer puid, Integer shopId, String start, String end) {
        amazonAdAttributionReportService.syncAttributionReport(puid, shopId, start, end);
    }

    public Result<String> syncAttributionStatus(Integer puid, Integer shopId) {
        List<AmazonAdProfile> profileList = slaveAmazonAdProfileDao.getNeedSyncAttribution(puid, shopId);
        if (CollectionUtils.isEmpty(profileList)) {
            return ResultUtil.success();
        }

        List<AmazonAdProfile> list = new ArrayList<>();
        for (AmazonAdProfile profile : profileList) {
            if (userPlanTypeCacheService.puidExpire(profile.getPuid())) {
                log.warn("同步attribution开通状态数据, shopId: {}, puid: {}, 状态为-99, 停止同步", profile.getShopId(), profile.getPuid());
                continue;
            }
            list.add(profile);
        }

        if (CollectionUtils.isEmpty(list)) {
            return ResultUtil.success();
        }
        List<Integer> shopIds = list.stream().map(AmazonAdProfile::getShopId).collect(Collectors.toList());
        List<ShopAuth> shopAuths = slaveShopAuthDao.getScAndVcByIds(shopIds);
        Map<Integer, ShopAuth> shopAuthMap = shopAuths.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
        for (AmazonAdProfile profile : list) {
            if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(profile.getShopId())) {
                //请求接口检测
                AttributionAdvertisers attributionAdvertisers = attributionApiClient.listAdvertisers(shopAuthMap.get(profile.getShopId()),
                        profile.getProfileId(), profile.getMarketplaceId());
                AmazonAdProfile updateProfile = new AmazonAdProfile();
                updateProfile.setId(profile.getId());
                if (attributionAdvertisers != null && CollectionUtils.isNotEmpty(attributionAdvertisers.getAdvertisers())) {
                    AttributionAdvertisers.Advertisers advertiser = attributionAdvertisers.getAdvertisers().get(0);
                    updateProfile.setAttributionStatus(1);
                    updateProfile.setAttributionAdvertiserId(advertiser.getAdvertiserId());
                    updateProfile.setAttributionAdvertiserName(advertiser.getAdvertiserName());
                    updateProfile.setAttributionStatusNextSyncTime(LocalDateTime.now().plusDays(1));
                    amazonAdProfileDao.updateById(updateProfile);
                } else {
                    updateProfile.setAttributionStatus(0);
                    updateProfile.setAttributionStatusNextSyncTime(LocalDateTime.now().plusDays(1));
                    amazonAdProfileDao.updateById(updateProfile);
                }
            }
        }
        return ResultUtil.success();
    }

    public Result<String> syncAttributionStatus() {
        syncAttributionStatus(null, null);
        return ResultUtil.success();
    }

    /**
     * 同步广告活动历史变更
     *
     * @param index
     * @param total
     */
    public void syncChangeHistory(int index, int total, String params) {
        long start = System.currentTimeMillis();
        Long startTime = null;
        Long end = null;
        Integer puid = null;
        Integer shopId = null;
        List<String> campaignIds = null;
        String type = null;
        if (org.apache.commons.lang3.StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                startTime = jsonObject.getLong("start");
                end = jsonObject.getLong("end");
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                String s = jsonObject.getString("campaignIds");
                type = jsonObject.getString("type");
                if (org.apache.commons.lang3.StringUtils.isNotBlank(s)) {
                    campaignIds = StringUtil.splitStr(s, ",");
                }

            }
        }
        //取出所有门店来跑数据
        List<ShopAuth> shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(null, shopId, 0, Integer.MAX_VALUE);
        shopAuths = shopAuths.stream().filter(Objects::nonNull).filter(s -> Math.abs(s.getId().hashCode() % total) == index).collect(Collectors.toList());

        CountDownLatch latch = new CountDownLatch(shopAuths.size());
        Long finalStartTime = startTime;
        Long finalEnd = end;
        List<String> finalCampaignIds = campaignIds;
        String finalType = type;
        shopAuths.stream().forEach(shop -> {
            long t1 = System.currentTimeMillis();
            ThreadPoolUtil.getBatchAdManagePool().execute(() -> {
                try {
                    cpcHistoryApiService.syncCampainsBeyondBugeting(shop, finalStartTime, finalEnd, finalCampaignIds, finalType);
                } catch (Exception e) {
                    log.info("puid={} shopId={} 同步广告活动变更历史异常{}", shop.getPuid(), shop.getId(), e);
                } finally {
                    latch.countDown();
                }
            });
        });
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("同步广告活动变更历史异常 latch await error {}", e);
        }

        log.info("同步广告活动变更历史总耗时:{} ", System.currentTimeMillis() - start);
    }

    /**
     * 切换报告同步 停止旧版 切换至新版
     *
     * @param puid
     * @param shopId
     */
    public String togglesReportSync(Integer puid, Integer shopId, Boolean syncHistory) {
        //停止旧报告同步
        if (puid == null) {
            return "";
        }
        if (syncHistory == null) {
            syncHistory = false;
        }
        List<Integer> failShops = new ArrayList<>();
        if (shopId == null) {
            List<AmazonAdProfile> profiles = slaveAmazonAdProfileDao.getList(puid);
            if (CollectionUtils.isEmpty(profiles)) {
                return "";
            }
            List<Integer> shopIds = profiles.stream().map(AmazonAdProfile::getShopId).collect(Collectors.toList());
            List<ShopAuth> shopAuths = slaveShopAuthDao.listAllByIds(puid, shopIds);
            if (CollectionUtils.isEmpty(shopAuths)) {
                return "";
            }
            List<Integer> validShops = shopAuths.stream().map(ShopAuth::getId).collect(Collectors.toList());
            Map<Integer, ShopAuth> shopAuthMap = shopAuths.stream().filter(Objects::nonNull).collect(Collectors.
                    toMap(ShopAuth::getId, Function.identity(), (e1, e2) -> e1));
            for (AmazonAdProfile profile : profiles.stream().filter(item -> validShops.contains(item.getShopId())).collect(Collectors.toList())) {
                try {
                    if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(profile.getShopId())) {
                        changeReportSync(shopAuthMap, profile, syncHistory);
                        updateReportSyncStatus(profile);
                    }
                } catch (Exception e) {
                    failShops.add(profile.getShopId());
                    log.error("切换报告同步 停止旧版 切换至新版 puid: {} shopId: {} 异常", profile.getPuid(), profile.getShopId(), e);
                }
            }
        } else {
            AmazonAdProfile profile = slaveAmazonAdProfileDao.getProfile(puid, shopId);
            if (profile != null) {
                ShopAuth shopAuth = slaveShopAuthDao.getScAndVcById(profile.getShopId());
                try {
                    if (shopAuth != null) {
                        changeReportSync(ImmutableMap.of(profile.getShopId(), shopAuth), profile, syncHistory);
                        updateReportSyncStatus(profile);
                    }
                } catch (Exception e) {
                    failShops.add(profile.getShopId());
                    log.error("切换报告同步 停止旧版 切换至新版 puid: {} shopId: {} 异常", profile.getPuid(), profile.getShopId(), e);
                }
            }
        }
        return failShops.isEmpty() ? "" : StringUtils.join(failShops, ",");
    }

    public void autoTogglesReportSync() {
        List<AmazonAdProfile> profiles = slaveAmazonAdProfileDao.getNeedStopSyncProfile();
        if (CollectionUtils.isEmpty(profiles)) {
            return;
        }
        List<Integer> shopIds = profiles.stream().map(AmazonAdProfile::getShopId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(shopIds)) {
            List<ShopAuth> shopAuths = slaveShopAuthDao.getScAndVcByIds(shopIds);
            if (CollectionUtils.isNotEmpty(shopAuths)) {
                List<Integer> validShops = shopAuths.stream().map(ShopAuth::getId).collect(Collectors.toList());
                Map<Integer, ShopAuth> shopAuthMap = shopAuths.stream().filter(Objects::nonNull).collect(Collectors.
                        toMap(ShopAuth::getId, Function.identity(), (e1, e2) -> e1));
                for (AmazonAdProfile profile : profiles.stream().filter(item -> validShops.contains(item.getShopId())).collect(Collectors.toList())) {
                    try {
                        changeReportSync(shopAuthMap, profile, true);
                        updateReportSyncStatus(profile);
                    } catch (Exception e) {
                        log.error("切换报告同步 停止旧版 切换至新版 puid: {} shopId: {} 异常", profile.getPuid(), profile.getShopId(), e);
                    }
                }
            }
        }
    }

    /**
     * 切换快照同步 停止旧版 切换至新版
     *
     * @param puid
     * @param shopId
     */
    public String togglesSnapshotSync(Integer puid, Integer shopId) {
        //停止旧报告同步
        if (puid == null) {
            return "";
        }
        List<Integer> failShops = new ArrayList<>();
        if (shopId == null) {
            List<AmazonAdProfile> profiles = slaveAmazonAdProfileDao.getList(puid);
            if (CollectionUtils.isEmpty(profiles)) {
                return "";
            }
            List<Integer> shopIds = profiles.stream().map(AmazonAdProfile::getShopId).collect(Collectors.toList());
            List<ShopAuth> shopAuths = slaveShopAuthDao.listAllByIds(puid, shopIds);
            if (CollectionUtils.isEmpty(shopAuths)) {
                return "";
            }
            List<Integer> validShops = shopAuths.stream().map(ShopAuth::getId).collect(Collectors.toList());
            Map<Integer, ShopAuth> shopAuthMap = shopAuths.stream().filter(Objects::nonNull).collect(Collectors.
                    toMap(ShopAuth::getId, Function.identity(), (e1, e2) -> e1));
            for (AmazonAdProfile profile : profiles.stream().filter(item -> validShops.contains(item.getShopId())).collect(Collectors.toList())) {
                try {
                    changeSnapshotSync(shopAuthMap, profile);
                    updateSnapshotSyncStatus(profile);
                } catch (Exception e) {
                    failShops.add(profile.getShopId());
                    log.error("切换快照同步 停止旧版 切换至新版 puid: {} shopId: {} 异常", profile.getPuid(), profile.getShopId(), e);
                }
            }
        } else {
            AmazonAdProfile profile = slaveAmazonAdProfileDao.getProfile(puid, shopId);
            if (profile == null) {
                return "";
            }
            ShopAuth shopAuth = slaveShopAuthDao.getScAndVcById(profile.getShopId());
            if (shopAuth == null) {
                return "";
            }
            try {
                changeSnapshotSync(ImmutableMap.of(profile.getShopId(), shopAuth), profile);
                updateSnapshotSyncStatus(profile);
            } catch (Exception e) {
                failShops.add(profile.getShopId());
                log.error("切换快照同步 停止旧版 切换至新版 puid: {} shopId: {} 异常", profile.getPuid(), profile.getShopId(), e);
            }
        }
        return failShops.isEmpty() ? "" : StringUtils.join(failShops, ",");
    }

    private void updateReportSyncStatus(AmazonAdProfile profile) {
        AmazonAdProfile updateProfile = new AmazonAdProfile();
        updateProfile.setId(profile.getId());
        updateProfile.setReportSyncStatus(AmazonAdProfile.ReportSyncStatusEnum.STOP.name());
        amazonAdProfileDao.updateById(updateProfile);
    }

    private void updateSnapshotSyncStatus(AmazonAdProfile profile) {
        AmazonAdProfile updateProfile = new AmazonAdProfile();
        updateProfile.setId(profile.getId());
        updateProfile.setSnapshotSyncStatus(AmazonAdProfile.ReportSyncStatusEnum.STOP.name());
        amazonAdProfileDao.updateById(updateProfile);
    }

    private void changeReportSync(Map<Integer, ShopAuth> shopAuthMap, AmazonAdProfile profile, Boolean syncHistory) {
        AadasApiGrpc.AadasApiBlockingStub stub = AadasApiGrpc.newBlockingStub(taskManagedChannel);
        AddSellerForSyncReportRequestPb.AddSellerForSyncReportRequest.Builder builder =
                AddSellerForSyncReportRequestPb.AddSellerForSyncReportRequest.newBuilder();
        builder.setSellerId(profile.getAccountId());
        builder.setIdentifier(profile.getPuid());
        builder.setRefreshToken(shopAuthMap.get(profile.getShopId()).getAdRefreshToken());
        Marketplace marketplace = Marketplace.fromId(profile.getMarketplaceId());
        RegionPb.Region region = RegionPb.Region.valueOf(marketplace.getRegion().name());
        builder.setRegion(region);
        MarketplacePb.Marketplace marketplaceValue = MarketplacePb.Marketplace.valueOf(marketplace.name());
        Map<Integer, MarketplacePb.Marketplace> marketplaces = new HashMap<>();
        marketplaces.put(profile.getShopId(), marketplaceValue);
        builder.putAllMarketplaces(marketplaces);
        builder.setIsSyncReportHistory(syncHistory);
        stub.addSellerForSyncReport(builder.build());
    }

    private void changeSnapshotSync(Map<Integer, ShopAuth> shopAuthMap, AmazonAdProfile profile) {
        if (MapUtils.isEmpty(shopAuthMap) || !shopAuthMap.containsKey(profile.getShopId())) {
            return;
        }
        AadasApiGrpc.AadasApiBlockingStub stub = AadasApiGrpc.newBlockingStub(taskManagedChannel);
        AddSellerForSyncSnapshotRequestPb.AddSellerForSyncSnapshotRequest.Builder builder =
                AddSellerForSyncSnapshotRequestPb.AddSellerForSyncSnapshotRequest.newBuilder();
        builder.setSellerId(profile.getAccountId());
        builder.setIdentifier(profile.getPuid());
        Marketplace marketplace = Marketplace.fromId(profile.getMarketplaceId());
        RegionPb.Region region = RegionPb.Region.valueOf(marketplace.getRegion().name());
        builder.setRegion(region);
        MarketplacePb.Marketplace marketplaceValue = MarketplacePb.Marketplace.valueOf(marketplace.name());
        Map<Integer, MarketplacePb.Marketplace> marketplaces = new HashMap<>();
        marketplaces.put(profile.getShopId(), marketplaceValue);
        builder.putAllMarketplaces(marketplaces);
        stub.addSellerForSyncSnapshot(builder.build());
    }

    public Result<String> notSuccessfulExecResultCount(String params) {
        int value = 100000;
        if (params != null) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                value = jsonObject.getInteger("value");

            }
        }
        int count = queueDao.getNotSuccessfulQueueNum();
        if (count > value) {
            return ResultUtil.returnErr("队列执行异常,待执行数量:" + count);
        }

        return ResultUtil.success();
    }

    public Result<String> sendReportDataToStats(Integer puid, Integer shopId, String start, String end) {
        if (StringUtils.isBlank(start) || StringUtils.isBlank(end)) {
            return ResultUtil.returnErr("参数异常");
        }
        if (puid != null && shopId != null) {
            sendReportToKafka(puid, shopId, start, end);
        } else {
            List<AmazonAdProfile> validAdProfile = slaveAmazonAdProfileDao.getValidSpAdProfile();
            for (AmazonAdProfile amazonAdProfile : validAdProfile) {
                sendReportToKafka(amazonAdProfile.getPuid(), amazonAdProfile.getShopId(), start, end);
            }
        }
        return ResultUtil.success("发送成功");
    }

    private void sendReportToKafka(Integer puid, Integer shopId, String start, String end) {
        ShopAuth byIdAndPuid = slaveShopAuthDao.getVcAndScByIdAndPuid(shopId, puid);
        List<AmazonAdProductReport> listReportByDate = amazonAdProductReportDao.getListReportByDate(puid, shopId, byIdAndPuid.getMarketplaceId(), start, end);
        for (AmazonAdProductReport report : listReportByDate) {
            adProductReportProducer.send(report.getShopId(), report);
        }
        List<CpcQueryKeywordReport> reportKeywordByDate = cpcQueryKeywordReportDao.getReportKeywordByDate(puid, shopId, start, end);
        for (CpcQueryKeywordReport report : reportKeywordByDate) {
            queryKeywordReportProducer.send(report.getShopId(), report);
        }
        List<CpcQueryTargetingReport> reportTargetByDate = cpcQueryTargetingReportDao.getReportTargetByDate(puid, shopId, start, end);
        for (CpcQueryTargetingReport report : reportTargetByDate) {
            queryTargetingReportProducer.send(report.getShopId(), report);
        }
    }

    /**
     * 同步广告活动历史变更
     *
     * @param index
     * @param total
     */
    public void handleNonTypeChangeHistoryLog(int index, int total, String params) {
        long startTime = System.currentTimeMillis();
        int start = 0;
        int limit = 100;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(null, null, start, limit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % total) == index).collect(Collectors.toList());
            for (ShopAuth shopAuth : shopAuths) {
                try {
                    cpcHistoryApiService.handleNonTypeChangeLog(shopAuth);
                } catch (Exception e) {
                    log.error("同步广告活动未知type变更历史:", e);
                }
            }

            if (size < limit) {
                break;
            }

            start += size;
        }

        log.info("同步广告活动未知type变更历史总耗时:{} ", System.currentTimeMillis() - startTime);
    }

    /**
     * 同步广告活动历史变更
     *
     * @param index
     * @param total
     */
    public void syncAllChangeHistoryLog(int index, int total, String params) {

        long start = System.currentTimeMillis();
        Long startTime = null;
        Long end = null;
        Integer puid = null;
        Integer shopId = null;
        List<String> campaignIds = null;
        String type = null;
        if (org.apache.commons.lang3.StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                startTime = jsonObject.getLong("start");
                end = jsonObject.getLong("end");
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                String s = jsonObject.getString("campaignIds");
                type = jsonObject.getString("type");
                if (org.apache.commons.lang3.StringUtils.isNotBlank(s)) {
                    campaignIds = StringUtil.splitStr(s, ",");
                }

            }
        }

        int startIndex = 0;
        int limit = 50;
        List<ShopAuth> shopAuths;

        while (true) {
            //取出所有门店来跑数据
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, startIndex, limit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(Objects::nonNull).filter(s -> Math.abs(s.getId().hashCode() % total) == index).collect(Collectors.toList());

            CountDownLatch latch = new CountDownLatch(shopAuths.size());
            Long finalStartTime = startTime;
            Long finalEnd = end;
            List<String> finalCampaignIds = campaignIds;
            for (ShopAuth shop : shopAuths) {
                if (userPlanTypeCacheService.puidExpire(shop.getPuid())) {
                    log.warn("同步history数据, shopId: {}, puid: {}, 状态为-99, 停止同步", shop.getId(), shop.getPuid());
                    latch.countDown();
                    continue;
                }
                ThreadPoolUtil.getSyncHistoryPool().execute(() -> {
                    try {
                        cpcHistoryApiService.syncAllChangeLog(shop, finalStartTime, finalEnd, finalCampaignIds);
                    } catch (Exception e) {
                        log.error("puid={} shopId={} 同步广告活动变更历史异常{}", shop.getPuid(), shop.getId(), e);
                    } finally {
                        latch.countDown();
                    }
                });
            }
            try {
                latch.await();
            } catch (InterruptedException e) {
                log.error("同步广告活动变更历史异常 latch await error {}", e);
            }

            if (size < limit) {
                break;
            }

            startIndex += size;
        }

    }

    /**
     * 拉取广告商产品元数据
     *
     * @param shardIndex 当前第i个实例
     * @param totalShard 总共有多少个实例
     * @param param
     */
    public void pullProductMetadataTask(int shardIndex, int totalShard, String param) {
        Integer limit = null;
        if (StringUtils.isNotBlank(param)) {
            limit = Integer.valueOf(param);
        }
        // 获取所有的广告商信息
        List<AmazonAdProfile> amazonAdProfileList = slaveAmazonAdProfileDao.queryAllProfile(limit);
        // 分片
        amazonAdProfileList = amazonAdProfileList.stream().filter(s -> Math.abs(s.getShopId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getProductMetadataPool();
        for (AmazonAdProfile amazonAdProfile : amazonAdProfileList) {
            if (userPlanTypeCacheService.puidExpire(amazonAdProfile.getPuid())) {
                log.warn("同步广告商产品元数据, shopId: {}, puid: {}, 状态为-99, 停止同步", amazonAdProfile.getShopId(), amazonAdProfile.getPuid());
                continue;
            }
            ShopAuth shopAuth = slaveShopAuthDao.getVcAndScByIdAndPuid(amazonAdProfile.getShopId(), amazonAdProfile.getPuid());
            if (shopAuth == null) {
                continue;
            }
            ThreadPoolUtil.waiting(threadExecutor);
            threadExecutor.execute(() -> {
                try {
                    productMetadataMethod(amazonAdProfile, shopAuth);
                } catch (Exception e) {
                    log.error("puid={} shopId={} 拉取广告商产品数据异常{}", amazonAdProfile.getPuid(), amazonAdProfile.getShopId(), e);
                }
            });
        }
        ThreadPoolUtil.waitingFinish(threadExecutor);
        log.info("========================任务结束==========================");
    }

    public void productMetadataMethod(AmazonAdProfile amazonAdProfile, ShopAuth shopAuth) {
        // 定义起始页码
        Integer pageIndex = 0;
        List<ProductMetadata> productMetadataList = Lists.newArrayList();
        while (true) {
            ProductAdClient productAdClient = ProductAdClient.getInstance();
            ListProductMetadataAdsResponse response;
            int retryCount = 0;
            do {
                String token = shopAuthService.getAdToken(shopAuth);
                //如果response为空就在请求一次
                response = productAdClient.listProductMetadataAdsResponse(
                        token, amazonAdProfile.getProfileId(), shopAuth.getMarketplaceId(),
                        null, 300, pageIndex, true);
                if (response != null) {
                    if (response.getStatusCode() == 401) {  //授权过期
                        //刷新token
                        shopAuthService.refreshCpcAuth(shopAuth);
                        retryCount++;
                    } else if (CollectionUtils.isNotEmpty(response.getProductMetadataList())) {
                        retryCount = 3;
                    } else {
                        break;
                    }
                } else {
                    break;
                }
            } while (retryCount < 3);

            if (response == null || CollectionUtils.isEmpty(response.getProductMetadataList())) {
                break;
            } else {
                productMetadataList.addAll(response.getProductMetadataList());
                if (response.getProductMetadataList().size() < 300) {
                    break;
                }
            }
            pageIndex++;
        }
        if (CollectionUtils.isNotEmpty(productMetadataList)) {
            Map<String, AmazonAdProductMetadata> map = Maps.newHashMap();
            // 数据转化
            List<AmazonAdProductMetadata> dataList = dataConvert(amazonAdProfile, productMetadataList, map);
            // 数据落表存储
            createdProductMetadataVoList(amazonAdProfile, dataList);
        }
        amazonAdProfileDao.updateNextTime(LocalDateTime.now().plusDays(1), amazonAdProfile.getId());
    }

    public List<AmazonAdProductMetadata> dataConvert(AmazonAdProfile amazonAdProfile, List<ProductMetadata> productMetadataList, Map<String, AmazonAdProductMetadata> map) {
        List<AmazonAdProductMetadata> amazonAdProductMetadataList =
                Lists.newArrayListWithExpectedSize(productMetadataList.size());
        // 数据组装
        if (CollectionUtils.isNotEmpty(productMetadataList)) {
            for (ProductMetadata productMetadata : productMetadataList) {
                if (StringUtils.isNotBlank(productMetadata.getAsin()) && StringUtils.isNotBlank(productMetadata.getSku())) {
                    AmazonAdProductMetadata amazonAdProductMetadata = new AmazonAdProductMetadata();
                    amazonAdProductMetadata.setPuid(amazonAdProfile.getPuid());
                    amazonAdProductMetadata.setShopId(amazonAdProfile.getShopId());
                    amazonAdProductMetadata.setMarketplaceId(amazonAdProfile.getMarketplaceId());
                    amazonAdProductMetadata.setProfileId(amazonAdProfile.getProfileId());
                    amazonAdProductMetadata.setSku(productMetadata.getSku());
                    amazonAdProductMetadata.setAsin(productMetadata.getAsin());
                    if (StringUtils.isNotBlank(productMetadata.getEligibilityStatus())) {
                        if (productMetadata.getEligibilityStatus().length() >= 200) {
                            amazonAdProductMetadata.setEligibilityStatus(productMetadata.getEligibilityStatus().substring(0, 200));
                        } else {
                            amazonAdProductMetadata.setEligibilityStatus(productMetadata.getEligibilityStatus());
                        }
                    }
                    if (StringUtils.isNotBlank(productMetadata.getAvailability())) {
                        if (productMetadata.getAvailability().length() >= 200) {
                            amazonAdProductMetadata.setAvailability(productMetadata.getAvailability().substring(0, 200));
                        } else {
                            amazonAdProductMetadata.setAvailability(productMetadata.getAvailability());
                        }
                    }
                    if (productMetadata.getBasisPrice() != null) {
                        amazonAdProductMetadata.setBasisPriceAmount(productMetadata.getBasisPrice().getBasisPriceAmount());
                        amazonAdProductMetadata.setBasisPriceCurrency(productMetadata.getBasisPrice().getBasisPriceCurrency());
                    }
                    if (productMetadata.getPriceToPay() != null) {
                        amazonAdProductMetadata.setPriceToPayAmount(productMetadata.getPriceToPay().getPriceToPayAmount());
                        amazonAdProductMetadata.setPriceToPayCurrency(productMetadata.getPriceToPay().getPriceToPayCurrency());
                    }
                    if (CollectionUtils.isNotEmpty(productMetadata.getIneligibilityCodes())) {
                        if (productMetadata.getIneligibilityCodes().toString().length() >= 200) {
                            amazonAdProductMetadata.setIneligibilityCodes(productMetadata.getIneligibilityCodes().toString().substring(0, 200));
                        } else {
                            amazonAdProductMetadata.setIneligibilityCodes(productMetadata.getIneligibilityCodes().toString());
                        }
                    }
                    if (CollectionUtils.isNotEmpty(productMetadata.getVariationList())) {
                        if (productMetadata.getVariationList().toString().length() >= 200) {
                            amazonAdProductMetadata.setVariationList(productMetadata.getVariationList().toString().substring(0, 200));
                        } else {
                            amazonAdProductMetadata.setVariationList(productMetadata.getVariationList().toString());
                        }
                    }
                    if (CollectionUtils.isNotEmpty(productMetadata.getIneligibilityReasons())) {
                        if (productMetadata.getIneligibilityReasons().toString().length() >= 200) {
                            amazonAdProductMetadata.setIneligibilityReasons(productMetadata.getIneligibilityReasons().toString().substring(0, 200));
                        } else {
                            amazonAdProductMetadata.setIneligibilityReasons(productMetadata.getIneligibilityReasons().toString());
                        }
                    }
                    if (StringUtils.isNotBlank(productMetadata.getBrand())) {
                        if (productMetadata.getBrand().length() >= 100) {
                            amazonAdProductMetadata.setBrand(productMetadata.getBrand().substring(0, 100));
                        } else {
                            amazonAdProductMetadata.setBrand(productMetadata.getBrand());
                        }
                    }
                    if (StringUtils.isNotBlank(productMetadata.getBestSellerRank())) {
                        if (productMetadata.getBestSellerRank().length() >= 200) {
                            amazonAdProductMetadata.setBestSellerRank(productMetadata.getBestSellerRank().substring(0, 200));
                        } else {
                            amazonAdProductMetadata.setBestSellerRank(productMetadata.getBestSellerRank());
                        }
                    }
                    if (StringUtils.isNotBlank(productMetadata.getCategory())) {
                        if (productMetadata.getCategory().length() >= 8) {
                            amazonAdProductMetadata.setCategory(productMetadata.getCategory().substring(0, 8));
                        } else {
                            amazonAdProductMetadata.setCategory(productMetadata.getCategory());
                        }
                    }
                    if (StringUtils.isNotBlank(productMetadata.getCreatedDate())) {
                        if (productMetadata.getCreatedDate().length() >= 200) {
                            amazonAdProductMetadata.setCreatedDate(productMetadata.getCreatedDate().substring(0, 200));
                        } else {
                            amazonAdProductMetadata.setCreatedDate(productMetadata.getCreatedDate());
                        }
                    }
                    if (StringUtils.isNotBlank(productMetadata.getImageUrl())) {
                        if (productMetadata.getImageUrl().length() >= 200) {
                            amazonAdProductMetadata.setImageUrl(productMetadata.getImageUrl().substring(0, 200));
                        } else {
                            amazonAdProductMetadata.setImageUrl(productMetadata.getImageUrl());
                        }
                    }
                    if (StringUtils.isNotBlank(productMetadata.getTitle())) {
                        if (productMetadata.getTitle().length() >= 500) {
                            amazonAdProductMetadata.setTitle(productMetadata.getTitle().substring(0, 500));
                        } else {
                            amazonAdProductMetadata.setTitle(productMetadata.getTitle());
                        }
                    }
                    amazonAdProductMetadataList.add(amazonAdProductMetadata);
                }
            }
        }
        return amazonAdProductMetadataList;
    }

    private void createdProductMetadataVoList(AmazonAdProfile amazonAdProfile, List<AmazonAdProductMetadata> amazonAdProductMetadataList) {
        if (CollectionUtils.isNotEmpty(amazonAdProductMetadataList)) {
            amazonAdProductMetadataDao.insertList(amazonAdProfile.getPuid(), amazonAdProductMetadataList);
        }
    }

    /**
     * 修复sb广告活动数据投放类型错误问题；
     */
    public void repairSbTargetType(String params) {
        long startTime = System.currentTimeMillis();
        int start = 0;
        int limit = 100;
        Integer puid = null;
        Integer shopId = null;
        List<String> campaignIds = new ArrayList<>();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {

                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                String s = jsonObject.getString("campaignIds");
                if (org.apache.commons.lang3.StringUtils.isNotBlank(s)) {
                    campaignIds = StringUtil.splitStr(s, ",");
                }

            }
        }
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, limit);
            int size = shopAuths.size();
            for (ShopAuth shopAuth : shopAuths) {
                try {
                    cpcAdSyncService.repairSbCampaingTargetType(shopAuth, campaignIds);
                } catch (Exception e) {
                    log.error("修复sb广告活动投放类型:", e);
                }
            }

            if (size < limit) {
                break;
            }

            start += size;
        }

        log.info("同步广告活动未知type变更历史总耗时:{} ", System.currentTimeMillis() - startTime);
    }

    /**
     * 搜索词本地化任务
     *
     * @param index
     * @param total
     */
    public void keywordLocalizationSchedule(Integer index, Integer total) {
        List<AmazonKeywordLocalizationSchedule> needsSyncSchedules =
                amazonKeywordLocalizationScheduleDao.getNeedsSyncSchedules();

        List<AmazonKeywordLocalizationSchedule> shardSchedules = needsSyncSchedules.stream()
                .filter(item -> Math.abs(MurmurHash3.hash32(item.getId())
                        % total) == index).collect(Collectors.toList());
        keywordLocalizationScheduleService.execute(shardSchedules);
    }

    /**
     * 同步所有sd广告创意
     */
    public void cpcSdCreativeSync(int shardIndex, int totalShard, Integer puid, Integer shopId) {
        int start = 0;
        int limit = 100;
        List<ShopAuth> shopAuths;

        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, limit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shopAuth : shopAuths) {
                if (userPlanTypeCacheService.puidExpire(shopAuth.getPuid())) {
                    log.warn("同步sd创意数据, shopId: {}, puid: {}, 状态为-99, 停止同步", shopAuth.getId(), shopAuth.getPuid());
                    continue;
                }
                try {
                    //先获取到配置信息
                    AmazonAdProfile amazonAdProfile = slaveAmazonAdProfileDao.getProfile(shopAuth.getPuid(), shopAuth.getId());
                    if (amazonAdProfile != null) {
                        //TODO 如果creativeId为空，以店铺维度同步所有广告组下的创意数据。
                        int gLimit = 50;
                        GroupPageParam param = new GroupPageParam();
                        param.setPuid(shopAuth.getPuid());
                        param.setShopId(shopAuth.getId());
                        //查询所有广告组id
                        List<AmazonSdAdGroup> sdAdGroups = amazonSdAdGroupDao.getList(shopAuth.getPuid(), param);
                        List<List<AmazonSdAdGroup>> sdAdGroupList = Lists.partition(sdAdGroups, gLimit);
                        for (List<AmazonSdAdGroup> sdGroups : sdAdGroupList) {
                            List<String> sdAdGroupIds = sdGroups.stream().map(AmazonSdAdGroup::getAdGroupId).collect(Collectors.toList());
                            cpcAdSyncService.syncSdCreativeByShop(shopAuth, StringUtil.joinString(sdAdGroupIds, StringUtil.SPLIT_COMMA), null);
                        }
                        syncTimeService.saveOrUpdate(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId(), UserSyncTypeEnum.AD_SD_CREATIVE);
                    }
                } catch (Exception e) {
                    log.error("syncSbAd:", e);
                }
            }

            if (size < limit) {
                break;
            }

            start += size;
        }
    }

    public void cpcSync(int shardIndex, int totalShard, Integer puid, Integer shopId, String campaignId, String type) {

        ThreadPoolExecutor threadExecutor;
        if ("1".equals(type)) {
            threadExecutor = ThreadPoolUtil.getCpcSpType1SyncPool();
        } else if ("2".equals(type)) {
            threadExecutor = ThreadPoolUtil.getCpcSpType2SyncPool();
        } else if ("3".equals(type)) {
            threadExecutor = ThreadPoolUtil.getCpcSpType3SyncPool();
        } else if ("4".equals(type)) {
            threadExecutor = ThreadPoolUtil.getCpcSpType4SyncPool();
        } else {
            threadExecutor = ThreadPoolUtil.getCpcSpSyncPool();
        }

        int start = 0;
        int limit = 100;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, limit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shop : shopAuths) {
                if (userPlanTypeCacheService.puidExpire(shop.getPuid())) {
                    log.warn("同步sp数据, shopId: {}, puid: {}, 状态为-99, 停止同步", shop.getId(), shop.getPuid());
                    continue;
                }
                ThreadPoolUtil.waiting(threadExecutor);
                threadExecutor.execute(() -> {
                    try {
                        //先获取到配置信息
                        AmazonAdProfile amazonAdProfile = slaveAmazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
                        if (amazonAdProfile != null && amazonAdProfile.getIsSp() == 1) {
                            cpcAdSyncService.syncSpByShop(shop, campaignId, type);
                            syncTimeService.saveOrUpdate(shop.getPuid(), shop.getId(), shop.getMarketplaceId(), UserSyncTypeEnum.AD_SP_MANAGE);
                        }
                    } catch (Exception e) {
                        log.error("cpcSync:", e);
                    }
                });
            }

            if (size < limit) {
                break;
            }

            start += size;
        }

        ThreadPoolUtil.waitingFinish(threadExecutor);
    }

    public void ShardingSphereTest() {
        //        for (int puid = 100; puid < 300; puid++) {
        //            String index = shardingMapInstance.getShardTable(Byte.toUnsignedInt(
        //                    DigestUtils.md5Digest(String.valueOf(puid).getBytes(StandardCharsets.US_ASCII))[0]));
        //            int dbIndex = getDbIndex(puid);
        //            log.info("=======================db-index {} table-index {} =======================", dbIndex, index);
        //            amazonAdProductDao.getByCondition(puid, new ConditionBuilder.Builder()
        //                    .equalTo("puid", puid).limit(1).build());
        //            amazonAdProductReportDao.getByCondition(puid, new ConditionBuilder.Builder()
        //                    .equalTo("puid", puid).limit(1).build());
        //            amazonAdKeywordDao.getByCondition(puid, new ConditionBuilder.Builder()
        //                    .equalTo("puid", puid).limit(1).build());
        //            amazonAdKeywordReportDao.getByCondition(puid, new ConditionBuilder.Builder()
        //                    .equalTo("puid", puid).limit(1).build());
        //            amazonAdProductAggregationReportDao.getByCondition(puid, new ConditionBuilder.Builder()
        //                    .equalTo("puid", puid).limit(1).build());
        //            cpcQueryTargetingReportDao.getByCondition(puid, new ConditionBuilder.Builder()
        //                    .equalTo("puid", puid).limit(1).build());
        //            cpcQueryKeywordReportDao.getByCondition(puid, new ConditionBuilder.Builder()
        //                    .equalTo("puid", puid).limit(1).build());
        //            log.info("======================= !!!!!! shard test end !!!!!!  =======================");
        //
        //        }
    }

    public int getDbIndex(int puid) {
        assert (puid > 0);

        if (vipJdbcTemplateMap != null) {
            JdbcTemplate vip = vipJdbcTemplateMap.get("vip" + puid);
            if (vip != null) {
                return puid;
            }
        }

        byte[] digest = MD5Util.getMD5Digest("" + puid);
        int byte1 = digest[digest.length - 2] & 0xff;
        int byte2 = digest[digest.length - 1] & 0xff;
        int index = (byte1 << 8) + byte2;
        int total = jdbcTemplateMap.size();
        index = index % total;
        return index;
    }

    /**
     * 聚合关键词数据
     */
    public void aggregationKeywordsData(int shardIndex, int totalShard, Integer puid) {
        if (puid != null) {
            cpcAdSyncService.aggregationKeywordsData(puid);
        } else {
            List<Integer> puidList = slaveAmazonAdProfileDao.getAllPuid();
            for (Integer pid : puidList) {
                cpcAdSyncService.aggregationKeywordsData(pid);
            }
        }
    }

    /**
     * 产品聚合报告同步父asin任务
     *
     * @param index
     * @param total
     */
    public void productAggregationReportSchedule(Integer index, Integer total, Integer limit, Integer puid, Integer shopId) {
        List<AmazonAggregationReportSchedule> shardSchedules = Lists.newArrayList();
        if (puid != null && shopId != null) {
            AmazonAggregationReportSchedule schedule = amazonAggregationReportScheduleDao.getByShopId(puid, shopId);
            shardSchedules.add(schedule);
        } else {
            List<AmazonAggregationReportSchedule> needsSyncSchedules = Lists.newArrayList();
            needsSyncSchedules = amazonAggregationReportScheduleDao.getNeedsSyncSchedules();
            shardSchedules = needsSyncSchedules.stream()
                    .filter(item -> Math.abs(MurmurHash3.hash32(item.getId())
                            % total) == index).collect(Collectors.toList());
        }

        aggregationProductReportScheduleService.execute(shardSchedules, limit);
    }

    /**
     * 产品聚合报告同步任务
     * 无父asin报告数据重试策略同步
     *
     * @param index
     * @param total
     */
    public void noneParentAsinAggregationReportSchedule(Integer index, Integer total, Integer limit, Integer syncDay, Integer puid, Integer shopId) {
        List<AmazonAggregationReportSchedule> shardSchedules = Lists.newArrayList();
        if (puid != null && shopId != null) {
            AmazonAggregationReportSchedule schedule = amazonAggregationReportScheduleDao.getByShopId(puid, shopId);
            shardSchedules.add(schedule);
        } else {
            List<AmazonAggregationReportSchedule> needsSyncSchedules = Lists.newArrayList();
            needsSyncSchedules = amazonAggregationReportScheduleDao.getNeedsNoneSyncSchedules();
            shardSchedules = needsSyncSchedules.stream()
                    .filter(item -> Math.abs(MurmurHash3.hash32(item.getId())
                            % total) == index).collect(Collectors.toList());
        }

        aggregationProductReportScheduleService.noneParentAsinExecute(shardSchedules, limit, syncDay);
    }

    public void syncInvoiceDetails(Integer puid, Integer shopId, String updateMonth, Integer detailsLimit, int index, int total) {

        int start = 0;
        int limit = 100;

        List<ShopAuth> shopAuths;
        ThreadPoolExecutor syncInvoicePool = ThreadPoolUtil.getSyncInvoiceDetailsPool();
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, limit);
            int size = shopAuths.size();
            shopAuths = shopAuths.stream().filter(Objects::nonNull).filter(s -> Math.abs(s.getId().hashCode() % total) == index).collect(Collectors.toList());

            for (ShopAuth shopAuth : shopAuths) {
                if (userPlanTypeCacheService.puidExpire(shopAuth.getPuid())) {
                    log.warn("同步信用卡账单详情数据, shopId: {}, puid: {}, 状态为-99, 停止同步", shopAuth.getId(), shopAuth.getPuid());
                    continue;
                }
                ThreadPoolUtil.waiting(syncInvoicePool);
                syncInvoicePool.execute(() -> {
                    try {
                        invoiceService.syncShopInvoiceDetails(shopAuth, updateMonth, detailsLimit);
                    } catch (Exception e) {
                        log.error("syncInvoice:", e);
                    }
                });
            }

            if (size < limit) {
                break;
            }

            start += size;
        }
        ThreadPoolUtil.waitingFinish(syncInvoicePool);
    }

    public void syncCategoriesByShopId(String marketplaceId, int index, int total) {
        List<String> marketplaceIds = Lists.newArrayList();
        Result result = ResultUtil.success();
        if (StringUtils.isNotBlank(marketplaceId)) {
            marketplaceIds = StringUtil.splitStr(marketplaceId, StringUtil.SPLIT_COMMA);
        } else {
            marketplaceIds = LocaleEnum.getAllMarketplaceIds();
        }
        // 分组marketplaceId。一个marketplaceId只会取一个有配置id的店铺去同步分类
        // 亚马逊类目数据不区分店铺，marketplaceId同步的类目数据以及这个marketplaceId下的所有店铺共用。
        // -- start
        Long t2 = System.currentTimeMillis();
        for (String marketId : marketplaceIds) {
            Long t1 = System.currentTimeMillis();
            List<ShopAuth> shopAuths = slaveShopAuthDao.getShopAuthByMarketplaceId(marketId);
            log.info("marketplaceId查询有效店铺共耗时：{}", System.currentTimeMillis() - t1);
            if (shopAuths.isEmpty()) {
                continue;
            }
            for (ShopAuth shop : shopAuths) {
                AmazonAdProfile amazonAdProfile = slaveAmazonAdProfileDao.getProfileByMarketId(shop.getPuid(), shop.getId(), shop.getMarketplaceId());
                if (amazonAdProfile != null && StringUtils.isNotBlank(amazonAdProfile.getProfileId())) {
                    try {
                        result = amazonTargetCategoryApiService.syncCategories(amazonAdProfile.getShopId(), amazonAdProfile.getProfileId(), amazonAdProfile.getMarketplaceId());
                    } catch (Exception e) {
                        log.error("sync target category error:", e);
                    }
                    // 只要有一个店铺同步到类目数据就跳出当前循环
                    if (result.success()) {
                        break;
                    }
                }
            }
        }
        log.info("分组marketplaceId,每个marketplaceId只获取一个有效店铺的profileId同步类目,共耗时：{}", System.currentTimeMillis() - t2);
        // -- end
    }

    /**
     * lx报告导入
     */
    public void processAdLxReportImportTask() throws PulsarClientException {
        List<CpcReportsImportTaskSchedule> schedules = cpcReportsImportTaskScheduleDao.listWaitTimeOutReportTask();
        for (CpcReportsImportTaskSchedule schedule : schedules) {
            AdReportImportMessage message = new AdReportImportMessage();
            message.setPuid(schedule.getPuid());
            message.setScheduleId(schedule.getId());
            message.setTaskId(schedule.getTaskId());
            message.setReportType(schedule.getReportType());
            message.setFileId(schedule.getFileId());
            sfAdReportImportProducer.send(Jackson.toJsonString(message).getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 自动化规则刷新模板和受控对象数据
     */
    public void refreshAutoData() {
        List<AmazonAdProfile> amazonAdProfiles = slaveAmazonAdProfileDao.getAllAmazonAdProfile();
        List<TimeRuleJson> timeRuleJsonList = Lists.newArrayList();
        for (int i = 1; i <= 7; i++) {
            TimeRuleJson timeRuleJson = new TimeRuleJson();
            timeRuleJson.setSiteDate(i);
            timeRuleJson.setStartTimeSite(0);
            timeRuleJson.setEndTimeSite(24);
            timeRuleJsonList.add(timeRuleJson);
        }
        amazonAdProfiles.forEach(e -> {
            List<AdvertiseAutoRuleTemplate> templateList = advertiseAutoRuleTemplateDao.getList(e.getPuid(), e.getShopId());
            templateList.forEach(t -> {
                //刷新模板数据
                advertiseAutoRuleTemplateDao.updateAutoRuleTemplate(t.getPuid(), t.getId(), JSONUtil.objectToJson(timeRuleJsonList),
                        LocalDateTimeUtil.convertChinaToSiteTime(t.getCreateTime(), t.getMarketplaceId()).toLocalDate());
                //刷新受控对象的数据
                advertiseAutoRuleStatusDao.updateAutoRuleStatus(t.getPuid(), t.getId(), JSONUtil.objectToJson(timeRuleJsonList),
                        LocalDateTimeUtil.convertChinaToSiteTime(t.getCreateTime(), t.getMarketplaceId()).toLocalDate());
            });
        });
    }

    /**
     * 检测数据
     */
    public void monitorCheck() throws IOException {
        adListMonitorService.compareMonitorData();
    }

    /**
     * 自动化规则刷新模板和受控对象数据
     */
    public void refreshAutoDataTo() {
        List<Integer> puidList = slaveAmazonAdProfileDao.getAllPuid();
        puidList.forEach(puid -> {
            advertiseAutoRuleTemplateDao.updateAutoRuleTemplate(puid);
            advertiseAutoRuleStatusDao.updateAutoRuleStatus(puid);
            advertiseAutoRuleStatusDeleteDao.updateAutoRuleStatus(puid);
            advertiseAutoRuleExecuteRecordDao.updateAutoRuleRecord(puid);
        });
    }

    /**
     * 删除监控数据
     */
    public void deleteMonitor() throws IOException {
        adListMonitorService.deleteMonitorData();
    }

    public void syncInvoiceList(Integer puid, Integer shopId, int limit, int index, int total) {
        List<AmazonAdInvoiceSyncState> amazonAdInvoiceSyncStates;
        ThreadPoolExecutor syncInvoicePool = ThreadPoolUtil.getSyncInvoicePool();
        amazonAdInvoiceSyncStates = amazonAdInvoiceSyncStateDao.getNeedSyncList(puid, shopId, limit);
        if (CollectionUtils.isEmpty(amazonAdInvoiceSyncStates)) {
            return;
        }
        amazonAdInvoiceSyncStates = amazonAdInvoiceSyncStates.stream().filter(Objects::nonNull).filter(s -> Math.abs(s.getId().hashCode() % total) == index).collect(Collectors.toList());
        List<Integer> shopIds = amazonAdInvoiceSyncStates.stream().map(AmazonAdInvoiceSyncState::getShopId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shopIds)) {
            return;
        }
        List<ShopAuth> shopAuths = slaveShopAuthDao.getScAndVcByIds(shopIds);
        Map<Integer, ShopAuth> shopAuthMap = shopAuths.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
        for (AmazonAdInvoiceSyncState amazonAdInvoiceSyncState : amazonAdInvoiceSyncStates) {
            ThreadPoolUtil.waiting(syncInvoicePool);
            ShopAuth shopAuth = shopAuthMap.get(amazonAdInvoiceSyncState.getShopId());
            if (shopAuth == null) {
                amazonAdInvoiceSyncStateDao.deleteById(amazonAdInvoiceSyncState.getId());
                log.error("删除调度任务，puid：{}， shopId:{}", amazonAdInvoiceSyncState.getPuid(), amazonAdInvoiceSyncState.getShopId());
                continue;
            }
            if (userPlanTypeCacheService.puidExpire(amazonAdInvoiceSyncState.getPuid())) {
                amazonAdInvoiceSyncStateDao.updateNextSyncTime(LocalDateTime.now().plusHours(8).plusSeconds(random.nextInt(180)), amazonAdInvoiceSyncState.getId());
                log.warn("同步广告发票列表数据, shopId: {}, puid: {}, 状态为-99, 停止同步", amazonAdInvoiceSyncState.getShopId(), amazonAdInvoiceSyncState.getPuid());
                continue;
            }
            syncInvoicePool.execute(() -> {
                try {
                    invoiceService.syncInvoiceList(shopAuth, amazonAdInvoiceSyncState);
                } catch (Exception e) {
                    try {
                        amazonAdInvoiceSyncStateDao.updateNextSyncTime(LocalDateTime.now().plusHours(6).plusSeconds(random.nextInt(180)), amazonAdInvoiceSyncState.getId());
                    } catch (Exception e1) {
                        log.error("syncInvoice 更新状态异常:", e1);
                    }

                    log.error("syncInvoice:", e);
                }
            });
        }
        ThreadPoolUtil.waitingFinish(syncInvoicePool);
    }

    public void initSyncInvoice(Integer puid, Integer shopId, Integer hour) {
        if (hour == null) {
            hour = -6;
        }
        LocalDateTime start = LocalDateTime.now().plusHours(hour);
        LocalDateTime end = LocalDateTime.now();
        List<AmazonAdProfile> profiles;
        int limit = 500;
        int startIndex = 0;
        if (puid != null || shopId != null) {
            profiles = slaveAmazonAdProfileDao.getListByPuidOrShopId(puid, shopId);
            saveAmazonAdInvoiceSyncState(profiles);
        } else {

            while (true) {
                profiles = slaveAmazonAdProfileDao.getByCreateTimeRange(start, end, startIndex, limit);
                int count = profiles.size();
                saveAmazonAdInvoiceSyncState(profiles);
                if (count < limit) {
                    return;
                }
                startIndex += limit;
            }
        }

    }

    private void saveAmazonAdInvoiceSyncState(List<AmazonAdProfile> profiles) {
        if (CollectionUtils.isEmpty(profiles)) {
            return;
        }
        List<Integer> shopIds = profiles.stream().map(AmazonAdProfile::getShopId).collect(Collectors.toList());

        List<Integer> shopIdsByIds = amazonAdInvoiceSyncStateDao.getShopIdsByIds(shopIds);
        Set<Integer> shopIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(shopIdsByIds)) {
            shopIdSet = new HashSet<>(shopIdsByIds);
        }
        LocalDate initDate = LocalDate.now().minusYears(1).withMonth(1).withDayOfMonth(1);
        List<AmazonAdInvoiceSyncState> saveList = new ArrayList<>(shopIds.size());
        for (AmazonAdProfile amazonAdProfile : profiles) {
            if (!shopIdSet.contains(amazonAdProfile.getShopId())) {
                AmazonAdInvoiceSyncState build = AmazonAdInvoiceSyncState.builder()
                        .puid(amazonAdProfile.getPuid())
                        .shopId(amazonAdProfile.getShopId())
                        .marketplaceId(amazonAdProfile.getMarketplaceId())
                        .nextToken("")
                        .isInitialize(Boolean.FALSE)
                        .initDate(initDate)
                        .nextSyncTime(LocalDateTime.now().plusMinutes(random.nextInt(30)))
                        .build();
                saveList.add(build);
            }
        }
        if (CollectionUtils.isNotEmpty(saveList)) {
            amazonAdInvoiceSyncStateDao.insert(saveList);
        }
    }

    /**
     * puid处理queryId
     *
     * @param puid  指定puid
     * @param index
     * @param total
     */
    public void handleReportQueryId(Integer puid, int index, int total) {
        if (puid != null) {
            handleReportByPuid(puid);
        } else {
            List<Integer> puidList;
            int limit = 500;
            int startIndex = 0;
            ThreadPoolExecutor executor = new ThreadPoolExecutor(16, 16, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
            //遍历广告profile表获取所有puid进行处理
            while (true) {
                puidList = slaveAmazonAdProfileDao.getAllPuidByLimit(startIndex, limit);
                int count = puidList.size();
                puidList = puidList.stream().filter(s -> s % total == index).collect(Collectors.toList());
                puidList.forEach(x -> executor.execute(() -> handleReportByPuid(x)));
                if (count < limit) {
                    executor.shutdown();
                    return;
                }
                startIndex += limit;
            }
        }
    }

    private void handleReportByPuid(Integer puid) {
        int start = 0;
        int limit = 2000;
        try {
            while (true) {
                List<Long> idList = cpcSbQueryKeywordReportDao.getAllIdByPuidLimit(puid, start, limit);
                int size = idList.size();
                if (size == 0) {
                    break;
                }
                cpcSbQueryKeywordReportDao.updateQueryId(puid, idList.get(0), idList.get(idList.size() - 1));
                if (size < limit) {
                    break;
                }
            }
            while (true) {
                List<Long> idList = cpcQueryKeywordReportDao.getAllIdByPuidLimit(puid, start, limit);
                int size = idList.size();
                if (size == 0) {
                    break;
                }
                cpcQueryKeywordReportDao.updateQueryId(puid, idList.get(0), idList.get(idList.size() - 1));
                if (size < limit) {
                    break;
                }
            }
            while (true) {
                List<Long> idList = cpcQueryTargetingReportDao.getAllIdByPuidLimit(puid, start, limit);
                int size = idList.size();
                if (size == 0) {
                    break;
                }
                cpcQueryTargetingReportDao.updateQueryId(puid, idList.get(0), idList.get(idList.size() - 1));
                if (size < limit) {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("handle queryId error, puid {}", puid, e);
        }
    }

    public void doDbCompare(List<String> paramDsBeanList) {
        dbCompareMonitorService.doDbCompare(paramDsBeanList);
    }

    public void repairShopAdToken() throws IOException {
        List<ShopAuth> shopAuths = shopAuthDao.getShopAuthByAuthAndTokenIsNull();
        AadasApiGrpc.AadasApiBlockingStub stub = AadasApiGrpc.newBlockingStub(taskManagedChannel);

        if (CollectionUtils.isNotEmpty(shopAuths)) {
            List<Integer> successList = new ArrayList<>();
            List<Integer> failList = new ArrayList<>();
            for (ShopAuth shopAuth : shopAuths) {
                try {
                    GetAccessTokenRequestPb.GetAccessTokenRequest.Builder builder = GetAccessTokenRequestPb.GetAccessTokenRequest.newBuilder();
                    builder.setSellerId(shopAuth.getSellingPartnerId());
                    builder.setRegion(RegionPb.Region.valueOf(Marketplace.fromId(shopAuth.getMarketplaceId()).getRegion().name()));
                    GetAccessTokenResponsePb.GetAccessTokenResponse accessToken = stub.getAccessToken(builder.build());
                    if (accessToken != null && StringUtils.isNotBlank(accessToken.getAccessToken()) && StringUtils.isNotBlank(accessToken.getRefreshToken())) {
                        log.info("Processing store token is empty , puid : {} ,shopId: {}, state :{} ", shopAuth.getPuid(), shopAuth.getId(), shopAuth.getAdStatus());
                        AmazonAdProfile profile = slaveAmazonAdProfileDao.getProfile(shopAuth.getPuid(), shopAuth.getId());
                        shopAuthDao.updateAdAccessTokenAndRefreshToken(shopAuth.getId(), accessToken.getAccessToken(), accessToken.getRefreshToken(), profile.getCreateTime());
                        successList.add(shopAuth.getId());
                        continue;
                    }
                    failList.add(shopAuth.getId());
                } catch (Exception e) {
                    failList.add(shopAuth.getId());
                    log.error("Processing store token is empty error,puid :{} ,shopId:{} :", shopAuth.getPuid(), shopAuth.getId(), e);
                }
            }
            OkHttpClient client = OkHttpClientUtil.getClient();
            Map<String, Object> param = new HashMap<>(2);
            param.put("msgtype", "markdown");
            Map<String, Object> textMap = new HashMap<>(2);
            StringBuilder context = new StringBuilder();
            context.append(String.format(contextTitle, shopAuths.size()));
            context.append("\n").append(String.format(successListContext, StringUtil.joinInt(successList), successList.size())).append("\n");
            context.append("\n").append(String.format(failListContext, StringUtil.joinInt(failList), failList.size())).append("\n");
            context.append("\n").append(String.format(listTitle, StringUtil.joinInt(failList), failList.size())).append("\n");
            Map<Integer, List<Integer>> shopMap = shopAuths.stream().collect(Collectors.groupingBy(ShopAuth::getPuid, Collectors.mapping(ShopAuth::getId, Collectors.toList())));
            shopMap.forEach((k, v) -> {
                if (CollectionUtils.isNotEmpty(v)) {
                    Integer puid = k == null ? 0 : k;
                    String ids = org.apache.commons.lang3.StringUtils.join(v, ",");
                    context.append(String.format(contextList, puid, ids)).append("\n");
                }
            });
            textMap.put("content", context.toString());
            param.put("markdown", textMap);
            RequestBody body = RequestBody.create(okhttp3.MediaType.parse(MediaType.APPLICATION_JSON), JSONUtil.objectToJson(param));
            Call call = client.newCall(new Request.Builder().url(url).post(body).build());
            Response execute = call.execute();
            log.info("发送预警通知结果：{}", execute.body());
        }

    }

    public void doScanUniqueIndex(List<ScanUniqueIndexDto> collect) {
        dbCompareMonitorService.doScanUniqueIndex(collect);
    }

    /**
     * lx新版数据搬家导入
     */
    public void processAdAmazonLxReportImportTask() throws PulsarClientException {
        List<AmazonAdReportsImportTaskSchedule> schedules = amazonAdReportsImportTaskScheduleDao.listWaitTimeOutReportTask();
        for (AmazonAdReportsImportTaskSchedule schedule : schedules) {
            AmazonAdReportImportMessage message = new AmazonAdReportImportMessage();
            message.setPuid(schedule.getPuid());
            message.setShopId(schedule.getShopId());
            message.setScheduleId(schedule.getId());
            message.setCountDate(schedule.getCountDate());
            message.setTaskId(schedule.getTaskId());
            message.setReportType(schedule.getReportType());
            message.setAdType(schedule.getAdType());
            message.setFileId(schedule.getFileId());
            amazonAdReportImportProducer.send(Jackson.toJsonString(message).getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 删除id表数据
     */
    public void deleteAggregateIdsTemporary() throws IOException {
        cpcPageIdsHandler.deleteAggregateIdsTemporary();
    }

    public void deleteHotTableData(int index, int total, String deleteBefore, Integer deleteByLimit) {
        deleteHotTableDataService.deleteHotTableData(index, total, deleteBefore, deleteByLimit);
    }

    public void batchCreateRetry(int index, int total, Integer puid, Integer shopId, Long taskId, String retryType) {
        List<Integer> puids = amazonAdBatchTaskSupportDao.getPuidsByUpdateTime(puid, LocalDateTime.now().minusHours(24));
        puids = puids.stream().filter(s -> Math.abs(s.hashCode() % total) == index).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(puids)) {
            log.info("batch Create " + retryType + " Retry puid is empty");
        }

        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getBatchCreateRetryPool();
        for (Integer pid : puids) {

            int start = 0;
            int limit = 100;
            int size;
            while (true) {

                List<ShopAuth> shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(pid, shopId, start, limit);
                size = shopAuths.size();

                for (ShopAuth shop : shopAuths) {
                    List<AmazonAdBatchTask> needRetryList = amazonAdBatchTaskDao.getNeedRetryListByPuidAndshopId(pid, shop.getId(), taskId);
                    for (AmazonAdBatchTask amazonAdBatchTask : needRetryList) {
                        ThreadPoolUtil.waiting(threadExecutor);
                        threadExecutor.execute(() -> {
                            try {
                                executeRetry(pid, shop.getId(), amazonAdBatchTask.getId(), retryType);
                            } catch (Exception e) {
                                log.error("batch create retry " + retryType + " error puid:{}, shopId:{} :", puid, shop.getId(), e);
                            }
                        });
                    }
                }
                if (size < limit) {
                    break;
                }

                start += size;
                ThreadPoolUtil.waitingFinish(threadExecutor);
            }

        }
    }

    private void executeRetry(Integer puid, Integer shopId, Long taskId, String retryKey) {
        switch (retryKey) {
            case SpBatchConstants.RETRY_CAMPAIGN_KEY:
                batchCreateRetryService.retryCampaign(puid, shopId, taskId);
                break;
            case SpBatchConstants.RETRY_GROUP_KEY:
                batchCreateRetryService.retryGroup(puid, shopId, taskId);
                break;
            case SpBatchConstants.RETRY_PRODUCT_KEY:
                batchCreateRetryService.retryProduct(puid, shopId, taskId);
                break;
            case SpBatchConstants.RETRY_ALL_TARGETING_KEY:
                batchCreateRetryService.retryAllTargeting(puid, shopId, taskId);
                break;
            default:
                throw new IllegalStateException("Unexpected value: " + retryKey);
        }

    }

    public void batchCreateAutoTargetRetry(int index, int total, Integer puid, Integer shopId, Long taskId) {
        List<Integer> puids = amazonAdBatchTaskSupportDao.getPuidsByUpdateTime(puid, LocalDateTime.now().minusHours(24));
        puids = puids.stream().filter(s -> Math.abs(s.hashCode() % total) == index).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(puids)) {
            log.info("batch Create auto target Retry puid is empty");
        }

        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getBatchCreateRetryPool();
        for (Integer pid : puids) {

            int start = 0;
            int limit = 100;
            int size;
            while (true) {

                List<ShopAuth> shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(pid, shopId, start, limit);
                size = shopAuths.size();

                for (ShopAuth shop : shopAuths) {

                    ThreadPoolUtil.waiting(threadExecutor);
                    threadExecutor.execute(() -> {
                        try {
                            batchCreateRetryService.retryAutoTarget(pid, shop.getId(), taskId);
                        } catch (Exception e) {
                            log.error("batch create retry  auto target error puid:{}, shopId:{} :", puid, shop.getId(), e);
                        }
                    });

                }
                if (size < limit) {
                    break;
                }

                start += size;
                ThreadPoolUtil.waitingFinish(threadExecutor);
            }

        }
    }

    public void retryManagementStreamTask(int index, int total, Integer puid, Integer shopId, String type) throws InterruptedException {
        List<Integer> shopIds = amazonManagementStreamTaskRetryService.getNeedRetryShopId(5000, puid, shopId, type);
        shopIds = shopIds.stream().filter(s -> Math.abs(s.hashCode() % total) == index).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shopIds)) {
            log.info("batch Create auto target Retry puid is empty");
            return;
        }

        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getManagementStreamMessagePool();
        if (StreamConstants.SB_TARGET_QUERY_TYPE.equals(type)) {
            threadExecutor = ThreadPoolUtil.getManagementStreamSbTargetMessagePool();
        }
        List<ShopAuth> shopAuths = slaveShopAuthDao.getScAndVcByIds(shopIds);
        if (CollectionUtils.isEmpty(shopAuths)) {
            return;
        }
        CountDownLatch latch = new CountDownLatch(shopAuths.size());
        for (ShopAuth shop : shopAuths) {

            threadExecutor.execute(() -> {
                try {
                    amazonManagementStreamTaskRetryService.retryTask(shop, type);
                } catch (Exception e) {
                    log.error("retry  manage stream error  shopId:{} :", shop.getId(), e);
                } finally {
                    latch.countDown();
                }
            });

        }
        latch.await();
    }

    public void sendStreamWarn() {
        amazonManagementStreamRedisCountService.sendAmazonManagementStreamWarn();
    }

    public void cpcSbTargetSync(int shardIndex, int totalShard, Integer puid, Integer shopId, String states, String type) {

        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getCpcSbSyncPool();

        int start = 0;
        int limit = 100;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, limit);
            int size = shopAuths.size();
            //shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shopAuth : shopAuths) {
                try {
                    //先获取到配置信息
                    AmazonAdProfile amazonAdProfile = slaveAmazonAdProfileDao.getProfile(shopAuth.getPuid(), shopAuth.getId());
                    if (amazonAdProfile != null && amazonAdProfile.getIsSb() == 1) {
                        List<String> campaignIdsByStates = amazonAdCampaignAllDao.getCampaignIdsByStates(puid, shopAuth.getId(), Lists.newArrayList(StateEnum.paused.getStateType(), StateEnum.enabled.getStateType(), StateEnum.archived.getStateType()), CampaignTypeEnum.sb.getCampaignType());
                        campaignIdsByStates = campaignIdsByStates.stream().filter(s -> Math.abs(s.hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
                        for (String cid : campaignIdsByStates) {
                            ThreadPoolUtil.waiting(threadExecutor);
                            threadExecutor.execute(() -> {
                                try {
                                    cpcAdSyncService.syncSbByShop(shopAuth, cid, "4");
                                } catch (Exception e) {
                                    log.error("syncSbAd:", e);
                                }

                            });
                        }

                    }
                } catch (Exception e) {
                    log.error("syncSbAd:", e);
                }
            }
            if (size < limit) {
                break;
            }

            start += size;
        }
        ThreadPoolUtil.waitingFinish(threadExecutor);
    }

    public void cpcSpTargetSync(int shardIndex, int totalShard, Integer puid, Integer shopId, String states, String type) {

        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getCpcSpSyncPool();

        int start = 0;
        int limit = 100;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = slaveShopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, limit);
            int size = shopAuths.size();
            //shopAuths = shopAuths.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            for (ShopAuth shopAuth : shopAuths) {
                try {
                    //先获取到配置信息
                    AmazonAdProfile amazonAdProfile = slaveAmazonAdProfileDao.getProfile(shopAuth.getPuid(), shopAuth.getId());
                    if (amazonAdProfile != null && amazonAdProfile.getIsSb() == 1) {
                        List<String> campaignIdsByStates = amazonAdCampaignAllDao.getCampaignIdsByStates(puid, shopAuth.getId(), Lists.newArrayList(StateEnum.paused.getStateType(), StateEnum.enabled.getStateType()), CampaignTypeEnum.sp.getCampaignType());
                        campaignIdsByStates = campaignIdsByStates.stream().filter(s -> Math.abs(s.hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
                        List<List<String>> partition = Lists.partition(campaignIdsByStates, 1);
                        for (List<String> cid : partition) {
                            ThreadPoolUtil.waiting(threadExecutor);
                            threadExecutor.execute(() -> {
                                try {
                                    cpcAdSyncService.syncSpByShop(shopAuth, StringUtil.joinString(cid, ","), type);
                                } catch (Exception e) {
                                    log.error("syncSbAd:", e);
                                }

                            });
                        }

                    }
                } catch (Exception e) {
                    log.error("syncSpAd:", e);
                }
            }
            if (size < limit) {
                break;
            }

            start += size;
        }
        ThreadPoolUtil.waitingFinish(threadExecutor);
    }

    public void statisticsQueryWord(Integer count) throws IOException {
        List<String> urls = queryWordStatisticsService.statisticsQueryWord(count);
        //发送企业微信结果
        OkHttpClient client = OkHttpClientUtil.getClient();
        Map<String, Object> param = new HashMap<>(2);
        param.put("msgtype", "markdown");
        Map<String, Object> textMap = new HashMap<>(2);
        StringBuilder context = new StringBuilder();
        for (String exportUrl : urls) {
            context.append("\n").append(String.format("statistics query word downloadUrl: %s", exportUrl)).append("\n");
        }
        textMap.put("content", context);
        param.put("markdown", textMap);
        RequestBody body = RequestBody.create(okhttp3.MediaType.parse(MediaType.APPLICATION_JSON), JSONUtil.objectToJson(param));
        Call call = client.newCall(new Request.Builder().url(url).post(body).build());
        Response execute = call.execute();
        log.info("发送预警通知结果：{}", execute.body());
    }

    public void deleteAdAmazonOperationLog(Integer limit) {
        deleteAdAmazonOperationLog.deleteAdAmazonOperationLog(limit);
    }

    /**
     * 删除无效店铺数据
     *
     * @param puid
     * @param shopId
     */
    public void deleteInvalidShopDataService(Integer puid, Integer shopId, Integer totalLimit) {
        deleteInvalidShopDataService.deleteInvalidShopData(puid, shopId, totalLimit);
    }

    /**
     * 删除迁移用户旧数据
     *
     * @param puid
     * @param beanId
     */
    public void deleteMigrateOldData(Integer puid, String beanId, Integer totalLimit) {
        deleteInvalidShopDataService.deleteMigrateOldData(puid, beanId, totalLimit);
    }

    /**
     * 统计无效店铺数据
     */
    public void statisticsInvalidShopData() {
        deleteInvalidShopDataService.statisticsInvalidShopData();
    }

    /**
     * 同步 audience
     */
    public Result<?> syncAudiences(int index, int total) {
        Result<?> result = ResultUtil.success();
        List<String> marketplaceIds = LocaleEnum.getAllMarketplaceIds();
        // 分组marketplaceId。一个marketplaceId只会取一个有配置id的店铺去同步分类
        // 亚马逊类目数据不区分店铺，marketplaceId同步的类目数据以及这个marketplaceId下的所有店铺共用。
        // -- start
        long t2 = System.currentTimeMillis();
        out:
        for (String marketId : marketplaceIds) {
            long t1 = System.currentTimeMillis();
            List<ShopAuth> shopAuths = slaveShopAuthDao.getShopAuthByMarketplaceId(marketId);
            log.info("marketplaceId查询有效店铺共耗时：{}", System.currentTimeMillis() - t1);
            if (shopAuths.isEmpty()) {
                continue;
            }
            for (ShopAuth shop : shopAuths) {
                AmazonAdProfile amazonAdProfile = slaveAmazonAdProfileDao.getProfileByMarketId(shop.getPuid(), shop.getId(), shop.getMarketplaceId());
                if (amazonAdProfile != null && StringUtils.isNotBlank(amazonAdProfile.getProfileId())) {
                    try {
                        result = amazonAudiencesApiService.syncAudiences(amazonAdProfile.getShopId(), amazonAdProfile.getProfileId(), amazonAdProfile.getMarketplaceId());
                    } catch (Exception e) {
                        log.error("sync target audience error:", e);
                        result = ResultUtil.error();
                    }
                    // 只要有一个店铺同步到受众数据就跳出当前循环
                    if (result.success()) {
                        break out;
                    }
                }
            }
        }
        log.info("分组marketplaceId,每个marketplaceId只获取一个有效店铺的profileId同步类目,共耗时：{}", System.currentTimeMillis() - t2);
        return result;
    }

    /**
     * 监控表大小
     *
     * @param rows 输出超过该行数的表
     */
    public void dbTableSizeMonitor(int rows) {
        dbCompareMonitorService.dbTableSizeMonitor(rows);
    }

    public void initPerspectiveHotAsin(int index, int total, List<Integer> puidList, Integer partition) {
        initPerspectiveHotAsinService.initHotAsin(index, total, puidList, partition);
    }

    public void shopDataInitSyncRetry4ShopLevel(int index, int total, List<Integer> shopIdList) {
        shopDataInitSyncService.doShopDataInitSyncRetry4ShopLevel(index, total, shopIdList);
    }

    public void shopDataInitSyncRetry4GroupLevel(int index, int total, List<Integer> shopIdList, List<String> groupIdList) {
        shopDataInitSyncService.shopDataInitSyncRetry4GroupLevel(index, total, shopIdList, groupIdList);
    }

    public int deleteShopDataInitHistoryData(List<Integer> shopIdList, int deleteTimeLimitSecond, int onceDelMaxCount) {
        return shopDataInitSyncService.deleteShopDataInitHistoryData(shopIdList, deleteTimeLimitSecond, onceDelMaxCount);
    }

    public void checkCost(Map<String, Object> params) throws IOException {
        String title = "<font color=\"warning\">店铺花费预警</font>\n\n";
        List<Integer> puids = Lists.newArrayList(3, 6);
        Object puids1 = params.get("puids");
        if (puids1 != null) {
            puids = StringUtil.stringToIntegerList(String.valueOf(puids1), ",");
        }

        for (Integer puid : puids) {
            List<ShopAuth> allValidAdShopByLimit = slaveShopAuthDao.getAllValidAdShopByLimit(puid, null, 0, 100);
            if (CollectionUtils.isEmpty(allValidAdShopByLimit)) {
                continue;
            }
            for (ShopAuth shopAuth : allValidAdShopByLimit) {
                StringBuilder message = new StringBuilder();
                MarketTimezoneAndCurrencyEnum marketTimezone = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(shopAuth.getMarketplaceId());
                LocalDate date = LocalDate.now(TimeZone.getTimeZone(marketTimezone.getTimezone()).toZoneId());
                List<AmazonAdCampaignAllReport> shopCost = amazonAdCampaignAllReportDao.getShopCost(puid, Lists.newArrayList(shopAuth.getId()), date.format(DateTimeFormatter.BASIC_ISO_DATE), null);
                if (CollectionUtils.isEmpty(shopCost) || shopCost.get(0).getCost() == null) {
                    continue;
                }
                Object limit = params.get(shopAuth.getId().toString());
                BigDecimal limitCost = limit == null ? BigDecimal.valueOf(5) : BigDecimal.valueOf((int) limit);

                if (limitCost.compareTo(shopCost.get(0).getCost()) > 0) {
                    continue;
                }

                message.append("puid：<font color=\"warning\">" + puid + "</font>" +
                        "，店铺名称：<font color=\"warning\">" + shopAuth.getName() + "</font>" +
                        "，花费金额：<font color=\"warning\">" + shopCost.get(0).getCost().setScale(2, RoundingMode.HALF_UP) + marketTimezone.getCurrencyCode() + "</font>" +
                        "，日期：<font color=\"warning\">" + date.format(DateTimeFormatter.BASIC_ISO_DATE) + "</font>\n\n");
                List<AmazonAdCampaignAllReport> campaignAllReportList = amazonAdCampaignAllReportDao.getShopCampaignOrderByCostLimit(puid, Lists.newArrayList(shopAuth.getId()), date.format(DateTimeFormatter.BASIC_ISO_DATE), null);
                message.append("<font color=\"warning\">花费排名前三活动</font>\n");
                for (AmazonAdCampaignAllReport amazonAdCampaignAllReport : campaignAllReportList) {
                    message.append("活动名称：<font color=\"warning\">" + amazonAdCampaignAllReport.getCampaignName() + "</font>" +
                            "，活动类型：<font color=\"warning\">" + amazonAdCampaignAllReport.getType() + "</font>" +
                            "，花费金额：<font color=\"warning\">" + amazonAdCampaignAllReport.getCost().setScale(2, RoundingMode.HALF_UP) + marketTimezone.getCurrencyCode() + "</font>\n");
                }

                //发送企业微信结果
                OkHttpClient client = OkHttpClientUtil.getClient();
                Map<String, Object> param = new HashMap<>(2);
                param.put("msgtype", "markdown");
                Map<String, Object> textMap = new HashMap<>(2);

                textMap.put("content", title + message);
                param.put("markdown", textMap);
                RequestBody body = RequestBody.create(okhttp3.MediaType.parse(MediaType.APPLICATION_JSON), JSONUtil.objectToJson(param));
                Call call = client.newCall(new Request.Builder().url((String) params.get("wxUrl")).post(body).build());
                Response execute = call.execute();
                log.info("发送预警通知结果：{}", execute.body());

            }
        }

    }

    public void dealStrategyExpiredConfig() {
        int update = strategyLimitConfigDao.updateExpiredStatus();
        log.info("已更新过期配置数{}条", update);
    }

    public boolean manualSyncOperationLog(int batchSize) {
        return adManageOperationLogTask.manualSyncOperationLog(batchSize);
    }

    public boolean clearHistoryOperationLog(int retainDays, int limit, int index, int total) {
        return adManageOperationLogTask.clearHistoryOperationLog(retainDays, limit, index, total);
    }

    /**
     * redis 初始化预警指标
     */
    public void initBudgetCampaignIndex() {
        budgetIndexService.initBudgetCampaignIndex();
    }

    public void threadpoolMonitor() {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b3640ea2-dba8-4c37-a5b8-52257d5ffeb9";
        ThreadPoolMonitorUtil.threadpoolMonitor(Arrays.asList(ThreadPoolUtil.class, TaskThreadPoolUtil.class), url, dynamicRefreshConfiguration.getThreadpoolMonitorWarningSize());
    }

    public void reportLevelDiffMonitor() {
        reportLevelDiffMonitorService.dealReportLevelDiffMonitor();
    }

    public void createDateDiffTaskJob() {
        reportDateDiffMonitorService.createFirstReportDateDiffMonitor();
    }

    public void reportDateDiffMonitorJob() {
        reportDateDiffMonitorService.dealEndReportDateDiffMonitor();
    }


    public void removeExpiredTargetTaskRecords() {
        adTargetTaskScheduleService.removeExpiredRecords();
    }

    public void removeOrRestartAbnormalRecords() {
        adTargetTaskScheduleService.removeOrRestartAbnormalRecords();
    }

    public void deleteWordRootQueryDirtyData(int index, int total) {
        wordRootQueryDirtyDataDelService.dirtyDataDel(index, total);
    }


    public void deleteColdTableData(int index, int total, String deleteBefore, Integer deleteByLimit) {
        deleteColdTableDataService.deleteColdTableData(index, total, deleteBefore, deleteByLimit);
    }

    public void deleteAdManagePageExportTask(Integer deleteBefore) {
        adManagePageExportTaskDelService.deleteHistoryTask(deleteBefore);
    }

    public void initUserKeywordLibTag(String params) {
        keywordLibService.initUserKeywordLibTag(params);
    }

    public void initCommonKeywordLibTag(String params) {
        keywordLibService.initCommonKeywordLibTag(params);
    }

    public void handlerInitKeywordSize(Integer puid, int index, int total) {
        repeatTargetingSyncKeywordSizeService.initKeywordSize(puid, index, total);
    }

    public void syncKeywordJoiningRank(String params) {
        keywordLibService.syncKeywordJoiningRank(params);
    }

    public void initKeywordLibUserTag(String params) {
        keywordLibService.initKeywordLibUserTag(params);
    }

    public void initKeywordLibCommonTag(String params) {
        keywordLibService.initKeywordLibCommonTag(params);
    }


    public void syncAsinInfo(String params) {
        AmznEndpoint[] values = AmznEndpoint.values();
        for (AmznEndpoint amznEndpoint : values) {
            String marketplaceId = amznEndpoint.getMarketplaceId();
            List<String> needSyncAsin = asinInfoDao.getNeedSyncAsin(marketplaceId, null, 10000);
            if (CollectionUtils.isEmpty(needSyncAsin)) {
                continue;
            }
            List<List<String>> partition = Lists.partition(needSyncAsin, 1000);
            for (List<String> asins : partition) {
                AdSyncAsinInfoMessage adSyncAsinInfoMessage = new AdSyncAsinInfoMessage();
                adSyncAsinInfoMessage.setMarketplaceId(marketplaceId);
                adSyncAsinInfoMessage.setAsins(asins);
                try {
                    adSyncAsinInfoKafkaProducer.send(adSyncAsinInfoMessage);
                    asinInfoDao.updateIsNeedSync(marketplaceId, asins);
                } catch (Exception e) {
                    log.error("发送同步asin信息消息失败：", e);
                }
            }
        }
    }


    public void syncUserPostProfiles(Integer index, Integer total, String params) {
        Map<String, String> parseQueryParams = XxlJobParamsUtil.parseQueryParams(params);
        String puidsStr = parseQueryParams.get("puid");
        List<Integer> puids = new ArrayList<>();
        if (StringUtils.isNotBlank(puidsStr)) {
            puids = Arrays.stream(StringUtils.split(puidsStr, ","))
                    .map(NumberUtils::toInt).filter(each -> each > 0).collect(Collectors.toList());
        }
        postsService.syncUserPostProfiles(index, total, puids);
    }

    public void exePricingStatusFix() {
        deleteInvalidShopDataService.exePricingStatusFix();
    }

    public void syncUserPosts(Integer index, Integer total, String params) {
        Map<String, String> parseQueryParams = XxlJobParamsUtil.parseQueryParams(params);
        String puidsStr = parseQueryParams.get("puid");
        List<Integer> puids = new ArrayList<>();
        if (StringUtils.isNotBlank(puidsStr)) {
            puids = Arrays.stream(StringUtils.split(puidsStr, ","))
                    .map(NumberUtils::toInt).filter(each -> each > 0).collect(Collectors.toList());
        }
        postsService.syncUserPosts(index, total, puids);
    }

    public void syncPostReportIncrementData(Integer index, Integer total, String params) {
        Map<String, String> queryParams = XxlJobParamsUtil.parseQueryParams(params);
        Integer puid = XxlJobParamsUtil.getIntParam(queryParams, "puid", null);
        Integer shopId = XxlJobParamsUtil.getIntParam(queryParams, "shopId", null);
        String postProfileId = XxlJobParamsUtil.getStringParam(queryParams, "postProfileId", null);
        postsService.syncPostReportIncrementData(index, total, puid, shopId, postProfileId);
    }

    public void syncPostReportAllData(Integer index, Integer total, String params) {
        Map<String, String> queryParams = XxlJobParamsUtil.parseQueryParams(params);
        Integer puid = XxlJobParamsUtil.getIntParam(queryParams, "puid", null);
        Integer shopId = XxlJobParamsUtil.getIntParam(queryParams, "shopId", null);
        String postProfileId = XxlJobParamsUtil.getStringParam(queryParams, "postProfileId", null);
        postsService.syncPostReportAllData(index, total, puid, shopId, postProfileId);
    }

    /**
     * doris数据统计
     */
    public void statDorisData() {
        dorisDataStatisticsService.statDorisData();
    }
}