package com.meiyunji.sponsored.cron;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.cron.dto.NginxCgiMonitorReportDto;
import com.meiyunji.sponsored.cron.service.*;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchConstants;
import com.meiyunji.sponsored.service.budgetUsage.IAmazonAdBudgetUsageDayService;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.dbcompare.dto.ScanUniqueIndexDto;
import com.meiyunji.sponsored.service.index.service.BudgetIndexService;
import com.meiyunji.sponsored.service.product.po.AsinImage;
import com.meiyunji.sponsored.service.product.service.ISyncAsinImageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import com.xxl.job.core.util.ShardingUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.client.api.PulsarClientException;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CronjobWrapper {

    @Autowired
    @Lazy
    private CronjobService cronjobService;

    @Autowired
    private ISyncAsinImageService syncAsinImageService;

    @Autowired
    private IWordFrequencyService iWordFrequencyService;

    @Autowired
    private ICpcStrategyService cpcStrategyService;

    @Autowired
    private ICpcAutoRuleService cpcAutoRuleService;

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Autowired
    private ICpcAutoRuleExecuteRecordService autoRuleExecuteRecordService;

    @Autowired
    private NginxCgiMonitorService nginxCgiMonitorService;
    @Autowired
    private IRepeatTargetingSyncKeywordSizeService repeatTargetingSyncKeywordSize;
    @Autowired
    private IAmazonAdBudgetUsageDayService amazonAdBudgetUsageDayService;
    @Autowired
    private IAdManageTagInitService adManageTagInitService;


    @Autowired
    private ISdTargetingDataService sdTargetingDataService;

    @XxlJob(value = "cpcAmazonAds")
    public ReturnT<String> cpcAmazonAds(String params) throws InterruptedException {
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.cpcAmazonAds(shardingVO.getIndex(), shardingVO.getTotal());
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "cpcAmazonSp")
    public ReturnT<String> cpcAmazonSp(String params) throws InterruptedException {
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.cpcAmazonSp(shardingVO.getIndex(), shardingVO.getTotal());
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "cpcAmazonSb")
    public ReturnT<String> cpcAmazonSb(String params) throws InterruptedException {
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.cpcAmazonSb(shardingVO.getIndex(), shardingVO.getTotal());
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "updateSbAdTargetType")
    public ReturnT<String> updateSbAdTargetType(String params) throws InterruptedException {
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.updateSbAdTargetType();
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "cpcAmazonSd")
    public ReturnT<String> cpcAmazonSd(String params) throws InterruptedException {
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.cpcAmazonSd(shardingVO.getIndex(), shardingVO.getTotal());
        return ReturnT.SUCCESS;
    }


    /**
     * 检测用户广告类型状态
     */
    @XxlJob(value = "detectAdType")
    public ReturnT<String> detectAdType(String params) {
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        Integer limit = null;
        if (StringUtils.isNotBlank(params)) {
            limit = Integer.valueOf(params);
        }
        cronjobService.detectAdTypeV2(shardingVO.getIndex(), shardingVO.getTotal(), limit);
        return ReturnT.SUCCESS;
    }

    /**
     * 检测用户广告类型状态
     */
    @XxlJob(value = "detectAdTypeOld")
    public ReturnT<String> detectAdTypeOld(String params) {
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        Integer limit = null;
        if (StringUtils.isNotBlank(params)) {
            limit = Integer.valueOf(params);
        }
        cronjobService.detectAdType(shardingVO.getIndex(), shardingVO.getTotal(), limit);
        return ReturnT.SUCCESS;
    }

    /**
     * 检测近2小时内授权用户的广告类型状态
     */
    @XxlJob(value = "detectActiveAdType")
    public ReturnT<String> detectActiveAdType(String params) {
        cronjobService.detectActiveAdType(Integer.valueOf(params));
        return ReturnT.SUCCESS;
    }


    /**
     * 定时同步广告活动
     */
    @XxlJob(value = "cpcSbSync")
    public ReturnT<String> cpcSbSync(String params) throws InterruptedException {
        Integer puid = null;
        Integer shopId = null;
        String campaignId = null;
        String type = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                campaignId = jsonObject.getString("campaignId");
                type = jsonObject.getString("type");
            }
        }

        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.cpcSbSync(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, campaignId, type);
        return ReturnT.SUCCESS;
    }

    /**
     * 定时同步广告活动
     */
    @XxlJob(value = "cpcSdSync")
    public ReturnT<String> cpcSdSync(String params) throws InterruptedException {
        Integer puid = null;
        Integer shopId = null;
        String campaignId = null;
        String type = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                campaignId = jsonObject.getString("campaignId");
                type = jsonObject.getString("type");
            }
        }

        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.cpcSdSync(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, campaignId, type);
        return ReturnT.SUCCESS;
    }

    /**
     * 同步sp,sb,sd广告活动信息(分片)
     */
    @XxlJob("syncCampaignInfo")
    public ReturnT<String> syncCampaignInfo(String params) {
        Integer puid = null;
        Integer shopId = null;
        String campaignId = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                campaignId = jsonObject.getString("campaignId");
            }
        }

        XxlJobLogger.log("start");
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        XxlJobLogger.log("sharding info index:{}, total:{}", shardingVO.getIndex(), shardingVO.getTotal());
        try {
            cronjobService.syncCampaignInfo(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, campaignId);
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        } finally {
            XxlJobLogger.log("end");
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 再次同步超预算活动日志
     */
    @XxlJob("syncOutOfBudgetHistoryAgain")
    public ReturnT<String> syncOutOfBudgetHistoryAgain(String params) {
        XxlJobLogger.log("start");
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        XxlJobLogger.log("sharding info index:{}, total:{}", shardingVO.getIndex(), shardingVO.getTotal());
        try {
            cronjobService.syncOutOfBudgetHistoryAgain(shardingVO.getIndex(), shardingVO.getTotal());
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        } finally {
            XxlJobLogger.log("end");
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 分片同步投放产生的搜索词报告里是asin图片、标题
     */
    @XxlJob(value = "shardSyncQueryTargetingReportAsinImage")
    public ReturnT<String> shardSyncQueryTargetingReportAsinImage(String params) {

        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.shardSyncQueryTargetingReportAsinImage(shardingVO.getIndex(), shardingVO.getTotal());
        return ReturnT.SUCCESS;
    }

    /**
     * 分片同步asin报告下的产品图片
     */
    @XxlJob(value = "syncAsinReportImage")
    public ReturnT<String> syncAsinReportImage(String params) {
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.shardSyncAsinReportImage(shardingVO.getIndex(), shardingVO.getTotal(), params);
        return ReturnT.SUCCESS;
    }


    /**
     * 定时同步广告活动
     */
    @XxlJob(value = "cpcSync")
    public ReturnT<String> cpcSync(String params) throws InterruptedException {
        Integer puid = null;
        Integer shopId = null;
        String campaignId = null;
        String type = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                campaignId = jsonObject.getString("campaignId");
                type = jsonObject.getString("type");
            }
        }

        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.cpcSync(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, campaignId, type);
        return ReturnT.SUCCESS;
    }


    /**
     * 定时同步投放下的产品信息：图片、标题
     * 从平台同步下来的target没有这些信息，需要单独同步
     */
    @XxlJob(value = "syncTargetAsinInfo")
    public ReturnT<String> syncTargetAsinInfo(String params) {
        Integer puid = null;
        Integer shopId = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
            }
        }
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.syncTargetAsinInfo(shardingVO, puid, shopId);
        return ReturnT.SUCCESS;
    }


    /**
     * 同步指定asin图片
     *
     * @param params
     * @return
     */
    @XxlJob(value = "syncAsinImage")
    public ReturnT<String> syncAsinImage(String params) {
        XxlJobLogger.log("start sync asin image with params：{}", params);
        try {
            List<AsinImage> list = Lists.newArrayList();
            int limit = 0;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                String asinsParams = jsonObject.getString("asins");
                if (StringUtils.isNotBlank(asinsParams)) {
                    List<String> asins = Arrays.asList(asinsParams.split(";")).stream().filter(e -> StringUtils.isNotBlank(e)).distinct().collect(Collectors.toList());
                    asins.forEach(e -> {
                        String[] arr = e.split("-");
                        if (arr.length == 2 && StringUtils.isNotBlank(arr[0]) && StringUtils.isNotBlank(arr[1])) {
                            AsinImage asinImage = new AsinImage();
                            asinImage.setAsin(arr[0]);
                            asinImage.setMarketplaceId(arr[1]);
                        }
                    });
                }
                Integer limitParams = jsonObject.getInteger("limit");
                if (limitParams != null) {
                    limit = limitParams;
                }

            }
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            syncAsinImageService.syncAsinImage(list, shardingVO.getIndex(), shardingVO.getTotal(), limit);
        } catch (Exception e) {
            XxlJobLogger.log(" sync sync asin image with error：{}", e);
            return ReturnT.FAIL;
        } finally {
            XxlJobLogger.log("sync sync asin image end");
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 同步广告信用卡支付费用数据
     * updateMonth  202111
     */
    @XxlJob(value = "syncInvoice")
    public ReturnT<String> syncInvoice(String params) {
        Integer puid = null;
        Integer shopId = null;
        String invoiceId = null;
        String updateMonth = null;
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                invoiceId = jsonObject.getString("invoiceId");
                updateMonth = jsonObject.getString("updateMonth");
            }
        }

        cronjobService.syncInvoice(puid, shopId, invoiceId, updateMonth, shardingVO.getIndex(), shardingVO.getTotal());
        return ReturnT.SUCCESS;
    }


    /**
     * 同步广告sp父asin数据
     * updateMonth  202111
     */
    @XxlJob(value = "syncSpAdParentAsin")
    public ReturnT<String> syncSpAdParentAsin(String params) {
        Integer puid = null;
        Integer shopId = null;
        String sku = null;
        Integer days = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                sku = jsonObject.getString("sku");
                days = jsonObject.getInteger("days");
            }
        }
        try {
            cronjobService.syncSpAdParentAsin(puid, shopId, sku, days);
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 同步广告sd父asin数据
     * updateMonth  202111
     */
    @XxlJob(value = "syncSdAdParentAsin")
    public ReturnT<String> syncSdAdParentAsin(String params) {
        Integer puid = null;
        Integer shopId = null;
        String sku = null;
        Integer days = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                sku = jsonObject.getString("sku");
                days = jsonObject.getInteger("days");
            }
        }
        cronjobService.syncSdAdParentAsin(puid, shopId, sku, days);
        return ReturnT.SUCCESS;
    }

    /**
     * 同步sp广告产品近三十天父asin数据
     */
    @XxlJob(value = "syncSpAdParentAsinInit")
    public ReturnT<String> syncSpAdParentAsinInit(String params) {
        Integer puid = null;
        Integer shopId = null;
        Integer days = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                days = jsonObject.getInteger("days");
            }
        }
        try {
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            cronjobService.syncSpAdParentAsinInit(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, days);
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    // ***************************** 下面是手动执行脚本
    /**
     * 检测用户广告类型状态
     */
    @XxlJob(value = "detectAdTypeTest")
    public ReturnT<String> detectAdTypeTest(String params) {
        Integer puid = null;
        Integer shopId = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
            }
        }
        if (puid == null && shopId == null) {
            XxlJobLogger.log("puid and shopId is null");
            return ReturnT.FAIL;
        }

        cronjobService.detectAdTypeTest(puid, shopId);
        return ReturnT.SUCCESS;
    }

    /**
     * 每小时统计队列第二步执行是否异常
     */
    @XxlJob(value = "statisticsExecResultCount")
    public ReturnT<String> statisticsExecResultCount(String params) {

        Result<String> result = cronjobService.statisticsExecResultCount(params);
        if (result.error()) {
            XxlJobLogger.log(result.getMsg());
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 同步attribution广告渠道
     */
    @XxlJob(value = "syncAttributionPublishers")
    public ReturnT<String> syncAttributionPublishers(String params) {
        cronjobService.syncAttributionPublishers();
        return ReturnT.SUCCESS;
    }

    /**
     * 同步profile开通attribution状态
     */
    @XxlJob(value = "syncAttributionStatus")
    public ReturnT<String> syncAttributionStatus(String params) {
        Integer puid = null;
        Integer shopId = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
            }
            if (puid != null && shopId != null) {
                Result<String> result = cronjobService.syncAttributionStatus(puid, shopId);
                if (result.error()) {
                    XxlJobLogger.log(result.getMsg());
                    return ReturnT.FAIL;
                }
            }
        } else {
            cronjobService.syncAttributionStatus();
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 同步attribution报告
     */
    @XxlJob(value = "syncAttributionReport")
    public ReturnT<String> syncAttributionReport(String params) {
        Integer puid = null;
        Integer shopId = null;
        String start = "";
        String end = "";
        //默认同步15天执行
        if (StringUtils.isBlank(params)) {
            LocalDate now = LocalDate.now();
            end = now.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
            start = now.minusDays(15).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
            cronjobService.syncAttributionReport(null, null, start, end);
        } else {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                start = jsonObject.getString("start");
                end = jsonObject.getString("end");
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                cronjobService.syncAttributionReport(puid, shopId, start, end);
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 同步广告活动变更事件数据
     *
     * @param params
     * @return
     */
    @XxlJob("syncChangeHistory")
    public ReturnT<String> syncChangeHistory(String params) {
        XxlJobLogger.log("start");
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        XxlJobLogger.log("sharding info index:{}, total:{}", shardingVO.getIndex(), shardingVO.getTotal());
        try {
            cronjobService.syncChangeHistory(shardingVO.getIndex(), shardingVO.getTotal(), params);
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        } finally {
            XxlJobLogger.log("end");

        }
        return ReturnT.SUCCESS;
    }

    /**
     * 同步广告活动变更事件数据
     *
     * @param params
     * @return
     */
    @XxlJob("togglesReportSync")
    public ReturnT<String> togglesReportSync(String params) {
        Integer puid = null;
        Integer shopId = null;
        Boolean syncHistory = null;
        if (StringUtils.isNotBlank(params)) {
            JSONArray array = JSONObject.parseArray(params);
            for (int i = 0; i < array.size(); i++) {
                JSONObject jsonObject = array.getJSONObject(i);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    syncHistory = jsonObject.getBoolean("syncHistory");
                    if (puid != null) {
                        String shopIds = cronjobService.togglesReportSync(puid, shopId, syncHistory);
                        XxlJobLogger.log("失败的店铺ID:{}", shopIds);
                    }
                }
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 同步广告活动变更事件数据
     *
     * @param params
     * @return
     */
    @XxlJob("autoTogglesReportSync")
    public ReturnT<String> autoTogglesReportSync(String params) {
        cronjobService.autoTogglesReportSync();
        return ReturnT.SUCCESS;
    }


    /**
     * 切换新版快照同步脚本
     * 切换广告同步
     *
     * @param params
     * @return
     */
    @XxlJob("togglesSnapshotSync")
    public ReturnT<String> togglesSnapshotSync(String params) {
        Integer puid = null;
        Integer shopId = null;
        if (StringUtils.isNotBlank(params)) {
            JSONArray array = JSONObject.parseArray(params);
            for (int i = 0; i < array.size(); i++) {
                JSONObject jsonObject = array.getJSONObject(i);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    if (puid != null) {
                        String shopIds = cronjobService.togglesSnapshotSync(puid, shopId);
                        XxlJobLogger.log("失败的店铺ID:{}", shopIds);
                    }
                }
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 统计报告同步队列未完成的数据是否异常
     */
    @XxlJob(value = "checkNotSuccessfulExecResultCount")
    public ReturnT<String> checkNotSuccessfulExecResultCount(String params) {

        Result<String> result = cronjobService.notSuccessfulExecResultCount(params);
        if (result.error()) {
            XxlJobLogger.log(result.getMsg());
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 推送广告数据kafka(asin看板数据)
     */
    @XxlJob(value = "sendReportDataToStats")
    public ReturnT<String> sendReportDataToStats(String params) {
        Integer puid = null;
        Integer shopId = null;
        String start = "";
        String end = "";
        JSONObject jsonObject = JSONObject.parseObject(params);
        if (jsonObject != null) {
            start = jsonObject.getString("start");
            end = jsonObject.getString("end");
            puid = jsonObject.getInteger("puid");
            shopId = jsonObject.getInteger("shopId");
            Result<String> result = cronjobService.sendReportDataToStats(puid, shopId, start, end);
            if (result.error()) {
                XxlJobLogger.log(result.getMsg());
                return ReturnT.FAIL;
            }
        }
        return ReturnT.SUCCESS;
    }


    @XxlJob("syncAllChangeHistoryLog")
    public ReturnT<String> syncAllChangeHistoryLog(String params) {
        XxlJobLogger.log("start");
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        XxlJobLogger.log("sharding info index:{}, total:{}", shardingVO.getIndex(), shardingVO.getTotal());
        try {
            cronjobService.syncAllChangeHistoryLog(shardingVO.getIndex(), shardingVO.getTotal(), params);
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        } finally {
            XxlJobLogger.log("end");

        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("handleNonTypeChangeHistoryLog")
    public ReturnT<String> handleNonTypeChangeHistoryLog(String params) {
        XxlJobLogger.log("start");
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        XxlJobLogger.log("sharding info index:{}, total:{}", shardingVO.getIndex(), shardingVO.getTotal());
        try {
            cronjobService.handleNonTypeChangeHistoryLog(shardingVO.getIndex(), shardingVO.getTotal(), params);
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        } finally {
            XxlJobLogger.log("end");

        }
        return ReturnT.SUCCESS;
    }

    /**
     * 广告产品元数据接口测试
     *
     * @return
     */
    @XxlJob(value = "pullProductMetadata")
    public ReturnT<String> pullProductMetadata(String params) {
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.pullProductMetadataTask(shardingVO.getIndex(), shardingVO.getTotal(), params);
        return ReturnT.SUCCESS;
    }

    /**
     * 同步关键词翻译
     *
     * @param params
     * @return
     */
    @XxlJob(value = "keywordLocalizationSchedule")
    public ReturnT<String> keywordLocalizationSchedule(String params) {
        XxlJobLogger.log("start");
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        XxlJobLogger.log("sharding info index:{}, total:{}", shardingVO.getIndex(), shardingVO.getTotal());
        try {
            cronjobService.keywordLocalizationSchedule(shardingVO.getIndex(), shardingVO.getTotal());
        } catch (Exception e) {
            log.error("keywordLocalizationSchedule", e);
            XxlJobLogger.log(e.getMessage());
            return ReturnT.FAIL;
        } finally {
            XxlJobLogger.log("end");
        }
        return ReturnT.SUCCESS;
    }


    @XxlJob("repairSbTargetType")
    public ReturnT<String> repairSbTargetType(String params) {

        try {
            cronjobService.repairSbTargetType(params);
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        } finally {
            XxlJobLogger.log("end");

        }
        return ReturnT.SUCCESS;
    }

    /**
     * 同步sd广告创意数据
     */
    @XxlJob(value = "cpcSdCreativeSync")
    public ReturnT<String> cpcSdCreativeSync(String params) throws InterruptedException {
        Integer puid = null;
        Integer shopId = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
            }
        }
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.cpcSdCreativeSync(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId);
        return ReturnT.SUCCESS;
    }

    /**
     *
     */
    @XxlJob(value = "syncAsinTitle")
    public ReturnT<String> syncAsinTitle(String params) {
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        syncAsinImageService.syncAsinTitle(params, shardingVO.getIndex(), shardingVO.getTotal());
        return ReturnT.SUCCESS;
    }

    /**
     *
     */
    @XxlJob(value = "syncAsinInvalid")
    public ReturnT<String> syncAsinInvalid(String params) {
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        syncAsinImageService.syncAsinInvalid(shardingVO.getIndex(), shardingVO.getTotal());
        return ReturnT.SUCCESS;
    }


    /**
     * 定时同步广告组合和顶级预算
     */
    @XxlJob(value = "cpcSyncProfileAndPortfolio")
    public ReturnT<String> syncByShopProfileAndPortfolio(String params) {
        Integer puid = null;
        Integer shopId = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
            }
        }

        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.syncByShopProfileAndPortfolio(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId);
        return ReturnT.SUCCESS;
    }


    /**
     * 同步今日预算分析数据
     */
    @XxlJob(value = "syncAmazonAdBudgetUsageToday")
    public ReturnT<String> syncAmazonAdBudgetUsageToday(String params) {
        Integer puid = null;
        Integer shopId = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
            }
        }

        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.syncAmazonAdBudgetUsageData(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, amazonAdBudgetUsageDayService::syncAmazonAdBudgetUsageToday);
        return ReturnT.SUCCESS;
    }

    /**
     * 同步昨天预算分析数据
     */
    @XxlJob(value = "syncAmazonAdBudgetUsageYesterday")
    public ReturnT<String> syncAmazonAdBudgetUsageYesterday(String params) {
        Integer puid = null;
        Integer shopId = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
            }
        }

        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.syncAmazonAdBudgetUsageData(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, amazonAdBudgetUsageDayService::syncAmazonAdBudgetUsageYesterday);
        return ReturnT.SUCCESS;
    }

    /**
     * 一次性任务 用于修复数据
     */
    @XxlJob(value = "syncAmazonAdBudgetUsageByDay")
    public ReturnT<String> syncAmazonAdBudgetUsageByDay(String params) {
        Integer puid = null;
        Integer shopId = null;
        String startDate = null;
        String endDate = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                startDate = jsonObject.getString("startDate");
                endDate = jsonObject.getString("endDate");
            }
        }
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            return ReturnT.FAIL;
        }

        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.syncAmazonAdBudgetUsageByDay(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, LocalDate.parse(startDate), LocalDate.parse(endDate));
        return ReturnT.SUCCESS;
    }

    /**
     * 一次性任务 用于修复报告币种数据
     */
    @XxlJob(value = "syncAmazonSpCampaignReportCurrency")
    public ReturnT<String> syncAmazonSpCampaignReportCurrency(String params) {
        Integer puid = null;
        Integer shopId = null;
        String startDate = null;
        String endDate = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                startDate = jsonObject.getString("startDate");
                endDate = jsonObject.getString("endDate");
            }
        }
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            return ReturnT.FAIL;
        }
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.syncAmazonSpCampaignReportCurrency(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, LocalDate.parse(startDate), LocalDate.parse(endDate));
        return ReturnT.SUCCESS;
    }


    /**
     * ShardingSphereTest
     */
    @XxlJob(value = "ShardingSphereTest")
    public ReturnT<String> ShardingSphereTest(String params) {
        cronjobService.ShardingSphereTest();
        return ReturnT.SUCCESS;
    }


    /**
     * TODO 聚合关键词库数据
     * 由于涉及权限以puid维度聚合数据，并存到表里
     */
    @XxlJob(value = "aggregationKeywordsData")
    public ReturnT<String> aggregationKeywordsData(String params) {
        Integer puid = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
            }
        }
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.aggregationKeywordsData(shardingVO.getIndex(), shardingVO.getTotal(), puid);
        return ReturnT.SUCCESS;
    }

    /**
     * 同步聚合产品报告数据（父asin字段同步）
     *
     * @param params
     * @return
     */
    @XxlJob(value = "productAggregationReportSchedule")
    public ReturnT<String> productAggregationReportSchedule(String params) {
        XxlJobLogger.log("start");
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Integer limit = null;
        Integer puid = null;
        Integer shopId = null;
        if (jsonObject != null) {
            limit = jsonObject.getInteger("limit");
            puid = jsonObject.getInteger("puid");
            shopId = jsonObject.getInteger("shopId");
        }
        XxlJobLogger.log("sharding info index:{}, total:{}", shardingVO.getIndex(), shardingVO.getTotal());
        try {
            cronjobService.productAggregationReportSchedule(shardingVO.getIndex(), shardingVO.getTotal(), limit, puid, shopId);
        } catch (Exception e) {
            log.error("productAggregationReportSchedule", e);
            XxlJobLogger.log(e.getMessage());
            return ReturnT.FAIL;
        } finally {
            XxlJobLogger.log("end");
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 同步聚合产品报告数据
     * 无父asin字段的重试策略同步数据
     *
     * @param params
     * @return
     */
    @XxlJob(value = "noneParentAsinAggregationReportSchedule")
    public ReturnT<String> noneParentAsinAggregationReportSchedule(String params) {
        XxlJobLogger.log("start");
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Integer limit = null;
        Integer puid = null;
        Integer shopId = null;
        if (jsonObject != null) {
            limit = jsonObject.getInteger("limit");
            puid = jsonObject.getInteger("puid");
            shopId = jsonObject.getInteger("shopId");
        }
        Integer syncDay = jsonObject.getInteger("day");
        XxlJobLogger.log("sharding info index:{}, total:{}", shardingVO.getIndex(), shardingVO.getTotal());
        try {
            cronjobService.noneParentAsinAggregationReportSchedule(shardingVO.getIndex(), shardingVO.getTotal(), limit, syncDay, puid, shopId);
        } catch (Exception e) {
            log.error("noneParentAsinAggregationReportSchedule", e);
            XxlJobLogger.log(e.getMessage());
            return ReturnT.FAIL;
        } finally {
            XxlJobLogger.log("end");
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 同步广告信用卡支付费用详情数据
     * updateMonth  202111
     */
    @XxlJob(value = "syncInvoiceDetails")
    public ReturnT<String> syncInvoiceDetails(String params) {
        Integer puid = null;
        Integer shopId = null;
        Integer limit = null;
        String updateMonth = null;
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                limit = jsonObject.getInteger("limit");
                updateMonth = jsonObject.getString("updateMonth");
            }
        }

        cronjobService.syncInvoiceDetails(puid, shopId, updateMonth, limit, shardingVO.getIndex(), shardingVO.getTotal());
        return ReturnT.SUCCESS;
    }

    /**
     * 同步分类/类目数据
     * 类目以marketplaceId维度去同步
     */
    @XxlJob(value = "syncCategoriesByShop")
    public ReturnT<String> syncCategoriesByShop(String params) {
        String marketplaceId = null;
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            marketplaceId = jsonObject.getString("marketplaceId");
        }
        cronjobService.syncCategoriesByShopId(marketplaceId, shardingVO.getIndex(), shardingVO.getTotal());
        return ReturnT.SUCCESS;
    }

    /**
     * 处理领星报告(超过10分钟处理异常的)
     */
    @XxlJob(value = "processAdLxReportImportTask")
    public ReturnT<String> processAdLxReportImportTask(String params) {
        try {
            cronjobService.processAdLxReportImportTask();
        } catch (PulsarClientException e) {
            log.error("error: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 自动化规则刷新模板历史数据
     */
    @XxlJob(value = "refreshAutoData")
    public ReturnT<String> refreshAutoData(String params) {
        cronjobService.refreshAutoData();
        return ReturnT.SUCCESS;
    }

    /**
     * 自动化规则刷新模板历史数据
     */
    @XxlJob(value = "refreshAutoDataTo")
    public ReturnT<String> refreshAutoDataTo(String params) {
        cronjobService.refreshAutoDataTo();
        return ReturnT.SUCCESS;
    }

    /**
     * 监控数据校验
     */
    @XxlJob(value = "monitorCheck")
    public ReturnT<String> monitorCheck(String params) {
        try {
            cronjobService.monitorCheck();
        } catch (Exception e) {
            log.error("检测监控数据错误: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 删除监控数据
     */
    @XxlJob(value = "deleteMonitor")
    public ReturnT<String> deleteMonitor(String params) {
        try {
            cronjobService.deleteMonitor();
        } catch (Exception e) {
            log.error("删除监控数据: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 同步广告信用卡支付费用
     */
    @XxlJob(value = "syncInvoiceList")
    public ReturnT<String> syncInvoiceList(String params) {
        Integer puid = null;
        Integer shopId = null;
        int limit = 1000;
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                limit = jsonObject.getIntValue("limit");
            }
        }

        cronjobService.syncInvoiceList(puid, shopId, limit, shardingVO.getIndex(), shardingVO.getTotal());
        return ReturnT.SUCCESS;
    }


    /**
     * 初始化广告同步发票状态
     */
    @XxlJob(value = "initSyncInvoice")
    public ReturnT<String> initSyncInvoice(String params) {
        Integer puid = null;
        Integer shopId = null;
        Integer hour = null;
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                hour = jsonObject.getInteger("hour");
            }
        }
        cronjobService.initSyncInvoice(puid, shopId, hour);
        return ReturnT.SUCCESS;
    }


    /**
     * 处理t_cpc_sb_query_keyword_report，t_cpc_query_keyword_report，t_cpc_query_targeting_report表的query_id
     *
     * @param params
     * @return
     */
    @XxlJob(value = "handleReportQueryId")
    public ReturnT<String> handleReportQueryId(String params) {
        try {
            Integer puid = null;
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                }
            }
            cronjobService.handleReportQueryId(puid, shardingVO.getIndex(), shardingVO.getTotal());
        } catch (Exception e) {
            log.error("handle report queryId error: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 数据库比对
     */
    @XxlJob(value = "doDbCompare")
    public ReturnT<String> doDbCompare(String params) {
        List<String> list = Collections.emptyList();
        if (StringUtils.isNotBlank(params)) {
            JSONArray jsonArray = JSONObject.parseArray(params);
            if (!CollectionUtils.isEmpty(jsonArray)) {
                list = jsonArray.toJavaList(String.class);
            }
        }
        cronjobService.doDbCompare(list);
        return ReturnT.SUCCESS;
    }

    /**
     * 修复店铺广告token为空的情况
     */
    @XxlJob(value = "repairShopAdToken")
    public ReturnT<String> repairShopAdToken(String params) {
        try {
            cronjobService.repairShopAdToken();
        } catch (Exception e) {
            log.error("handle store token is empty error: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 扫描25个分库的某张逻辑表，输出指定的索引(列名逗号分隔)是否存在以及是否是唯一索引
     * param为指定的json参数tableName为表名，shard为是否分表0否1是，indexColumnName为索引字段使用逗号分隔，例如：
     * [{
     * "tableName": "t_amazon_ad_product_aggregation_report",
     * "shard": 1,
     * "indexColumnName": "puid,shop_id,ad_id,count_date,type"
     * }]
     * 以上我们可以输出该表(json数组支持多个)在25个分库下的所有分表的这个索引是否存在，存在的话是唯一索引还是普通索引
     */
    @XxlJob(value = "doScanUniqueIndex")
    public ReturnT<String> doScanUniqueIndex(String params) {
        List<ScanUniqueIndexDto> list = Collections.emptyList();
        if (StringUtils.isNotBlank(params)) {
            JSONArray jsonArray = JSONObject.parseArray(params);
            if (!CollectionUtils.isEmpty(jsonArray)) {
                list = jsonArray.toJavaList(ScanUniqueIndexDto.class);
                //参数校验
                List<ScanUniqueIndexDto> collect = list.stream()
                        .filter(x -> !StringUtils.isAnyBlank(x.getTableName(), x.getIndexColumnName()))
                        .filter(x -> (0 == x.getShard() || 1 == x.getShard())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)) {
                    cronjobService.doScanUniqueIndex(collect);
                }
            }
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 处理领星报告(超过10分钟处理异常的)
     */
    @XxlJob(value = "processAdAmazonLxReportImportTask")
    public ReturnT<String> processAdAmazonLxReportImportTask(String params) {
        try {
            cronjobService.processAdAmazonLxReportImportTask();
        } catch (PulsarClientException e) {
            log.error("error: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 删除监控数据
     */
    @XxlJob(value = "deleteAggregateIdsTemporary")
    public ReturnT<String> deleteAggregateIdsTemporary(String params) {
        try {
            cronjobService.deleteAggregateIdsTemporary();
        } catch (Exception e) {
            log.error("删除id表数据异常: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "wordFrequencySpQueryHandler")
    public ReturnT<String> wordFrequencySpQueryHandler(String params) throws InterruptedException {
        try {
            Integer puid = null;
            Integer shopId = null;
            Integer calculateAll = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    calculateAll = jsonObject.getInteger("calculateAll");
                }
            }
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            iWordFrequencyService.wordFrequencySpQueryHandler(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, calculateAll);
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "wordFrequencySpKeywordHandler")
    public ReturnT<String> wordFrequencySpKeywordHandler(String params) throws InterruptedException {
        try {
            Integer puid = null;
            Integer shopId = null;
            Integer calculateAll = null;
            Integer desc = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    calculateAll = jsonObject.getInteger("calculateAll");
                    desc = jsonObject.getInteger("desc");
                }
            }
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            iWordFrequencyService.wordFrequencySpKeywordHandler(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, calculateAll, desc);
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "wordFrequencySpTargetingHandler")
    public ReturnT<String> wordFrequencySpTargetingHandler(String params) throws InterruptedException {
        try {
            Integer puid = null;
            Integer shopId = null;
            Integer calculateAll = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    calculateAll = jsonObject.getInteger("calculateAll");
                }
            }
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            iWordFrequencyService.wordFrequencySpTargetingHandler(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, calculateAll);
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "wordFrequencySbQueryHandler")
    public ReturnT<String> wordFrequencySbQueryHandler(String params) throws InterruptedException {
        try {
            Integer puid = null;
            Integer shopId = null;
            Integer calculateAll = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    calculateAll = jsonObject.getInteger("calculateAll");
                }
            }
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            iWordFrequencyService.wordFrequencySbQueryHandler(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, calculateAll);
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "wordFrequencySbKeywordHandler")
    public ReturnT<String> wordFrequencySbKeywordHandler(String params) throws InterruptedException {
        try {
            Integer puid = null;
            Integer shopId = null;
            Integer calculateAll = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    calculateAll = jsonObject.getInteger("calculateAll");
                }
            }
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            iWordFrequencyService.wordFrequencySbKeywordHandler(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, calculateAll);
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "wordFrequencySpQueryNewHandler")
    public ReturnT<String> wordFrequencySpQueryNewHandler(String params) throws InterruptedException {
        try {
            Integer puid = null;
            Integer shopId = null;
            Integer calculateAll = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    calculateAll = jsonObject.getInteger("calculateAll");
                }
            }
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            iWordFrequencyService.wordFrequencySpQueryNewHandler(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, calculateAll);
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "wordFrequencySpKeywordNewHandler")
    public ReturnT<String> wordFrequencySpKeywordNewHandler(String params) throws InterruptedException {
        try {
            Integer puid = null;
            Integer shopId = null;
            Integer calculateAll = null;
            Integer desc = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    calculateAll = jsonObject.getInteger("calculateAll");
                    desc = jsonObject.getInteger("desc");
                }
            }
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            iWordFrequencyService.wordFrequencySpKeywordNewHandler(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, calculateAll, desc);
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "wordFrequencySpTargetingNewHandler")
    public ReturnT<String> wordFrequencySpTargetingNewHandler(String params) throws InterruptedException {
        try {
            Integer puid = null;
            Integer shopId = null;
            Integer calculateAll = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    calculateAll = jsonObject.getInteger("calculateAll");
                }
            }
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            iWordFrequencyService.wordFrequencySpTargetingNewHandler(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, calculateAll);
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "wordFrequencySbQueryNewHandler")
    public ReturnT<String> wordFrequencySbQueryNewHandler(String params) throws InterruptedException {
        try {
            Integer puid = null;
            Integer shopId = null;
            Integer calculateAll = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    calculateAll = jsonObject.getInteger("calculateAll");
                }
            }
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            iWordFrequencyService.wordFrequencySbQueryNewHandler(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, calculateAll);
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "wordFrequencySbKeywordNewHandler")
    public ReturnT<String> wordFrequencySbKeywordNewHandler(String params) throws InterruptedException {
        try {
            Integer puid = null;
            Integer shopId = null;
            Integer calculateAll = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    calculateAll = jsonObject.getInteger("calculateAll");
                }
            }
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            iWordFrequencyService.wordFrequencySbKeywordNewHandler(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, calculateAll);
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "wordRootTranslatorsSpKeywordHandler")
    public ReturnT<String> wordRootTranslatorsSpKeywordHandler(String params) throws InterruptedException {
        Integer puid = null;
        Integer shopId = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
            }
        }
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        iWordFrequencyService.wordRootTranslatorsQuerySpKeywordHandler(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId);
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "wordRootTranslationQueryHandler")
    public ReturnT<String> wordRootTranslationQueryHandler(String params) throws InterruptedException {
        Integer puid = null;
        Integer shopId = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
            }
        }
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        iWordFrequencyService.wordRootTranslationQueryHandler(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId);
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "wordRootTranslatorsSbKeywordHandler")
    public ReturnT<String> wordRootTranslatorsSbKeywordHandler(String params) throws InterruptedException {
        Integer puid = null;
        Integer shopId = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
            }
        }
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        iWordFrequencyService.wordRootTranslatorsQuerySbKeywordHandler(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId);
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "wordRootTranslatorsSpTargetingHandler")
    public ReturnT<String> wordRootTranslatorsSpTargetingHandler(String params) throws InterruptedException {
        Integer puid = null;
        Integer shopId = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
            }
        }
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        iWordFrequencyService.wordRootTranslatorsQuerySpTargetingHandler(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId);
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "keywordSpWordRootTranslatorsHandler")
    public ReturnT<String> keywordSpWordRootTranslatorsHandler(String params) throws InterruptedException {
        Integer puid = null;
        Integer shopId = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
            }
        }
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        iWordFrequencyService.keywordSpWordRootTranslatorsHandler(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId);
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "keywordSbWordRootTranslatorsHandler")
    public ReturnT<String> keywordSbWordRootTranslatorsHandler(String params) throws InterruptedException {
        Integer puid = null;
        Integer shopId = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
            }
        }
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        iWordFrequencyService.keywordSbWordRootTranslatorsHandler(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId);
        return ReturnT.SUCCESS;
    }


    /**
     * 清理热表数据
     */
    @XxlJob(value = "deleteHotTableData")
    public ReturnT<String> deleteHotTableData(String params) {

        String deleteBefore = null;
        Integer deleteByLimit = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                deleteBefore = jsonObject.getString("deleteBefore");
                deleteByLimit = jsonObject.getInteger("deleteByLimit");
            }
        }
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();

        try {
            cronjobService.deleteHotTableData(shardingVO.getIndex(), shardingVO.getTotal(), deleteBefore, deleteByLimit);
        } catch (Exception e) {
            log.error("清理hot表数据异常: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 广告活动重试
     */
    @XxlJob(value = "batchCreateRetryByCampaign")
    public ReturnT<String> batchCreateRetryByCampaign(String params) {
        try {
            Integer puid = null;
            Integer shopId = null;
            Long taskId = null;
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    taskId = jsonObject.getLong("taskId");
                }
            }
            cronjobService.batchCreateRetry(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, taskId, SpBatchConstants.RETRY_CAMPAIGN_KEY);
        } catch (Exception e) {
            log.error("batchCreateRetryByCampaign error: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 广告组重试
     */
    @XxlJob(value = "batchCreateRetryByGroup")
    public ReturnT<String> batchCreateRetryByGroup(String params) {
        try {
            Integer puid = null;
            Integer shopId = null;
            Long taskId = null;
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    taskId = jsonObject.getLong("taskId");
                }
            }
            cronjobService.batchCreateRetry(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, taskId, SpBatchConstants.RETRY_GROUP_KEY);
        } catch (Exception e) {
            log.error("batchCreateRetryByGroup error: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 广告产品重试
     */
    @XxlJob(value = "batchCreateRetryByProduct")
    public ReturnT<String> batchCreateRetryByProduct(String params) {
        try {
            Integer puid = null;
            Integer shopId = null;
            Long taskId = null;
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    taskId = jsonObject.getLong("taskId");
                }
            }
            cronjobService.batchCreateRetry(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, taskId, SpBatchConstants.RETRY_PRODUCT_KEY);
        } catch (Exception e) {
            log.error("batchCreateRetryByProduct error: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 广告投放重试（关键词投放，商品投放，否定关键词投放，否定商品投放）
     */
    @XxlJob(value = "batchCreateRetryByTarget")
    public ReturnT<String> batchCreateRetryByTarget(String params) {
        try {
            Integer puid = null;
            Integer shopId = null;
            Long taskId = null;
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    taskId = jsonObject.getLong("taskId");
                }
            }
            cronjobService.batchCreateRetry(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, taskId, SpBatchConstants.RETRY_ALL_TARGETING_KEY);
        } catch (Exception e) {
            log.error("batchCreateRetryByTarget error: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 批量创建自动投放重试
     */
    @XxlJob(value = "batchCreateRetryByAutoTarget")
    public ReturnT<String> batchCreateRetryByAutoTarget(String params) {
        try {
            Integer puid = null;
            Integer shopId = null;
            Long taskId = null;
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    taskId = jsonObject.getLong("taskId");
                }
            }
            cronjobService.batchCreateAutoTargetRetry(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, taskId);
        } catch (Exception e) {
            log.error("batchCreateRetryByTarget error: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


    /**
     * stream retry task
     */
    @XxlJob(value = "retryManagementStreamTask")
    public ReturnT<String> retryManagementStreamTask(String params) {
        try {
            Integer puid = null;
            Integer shopId = null;
            String type = null;
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    type = jsonObject.getString("type");
                }
            }
            cronjobService.retryManagementStreamTask(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, type);
        } catch (Exception e) {
            log.error("batchCreateRetryByTarget error: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 亚马逊stream监控
     */
    @XxlJob(value = "sendAmazonManagementStreamWarn")
    public ReturnT<String> sendAmazonManagementStreamWarn(String param) {
        try {
            cronjobService.sendStreamWarn();
        } catch (Exception e) {
            log.error("retryManagementStreamTask = {}", e.getMessage());
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 定时同步广告活动
     */
    @XxlJob(value = "cpcSbTargetSync")
    public ReturnT<String> cpcSbTargetSync(String params) throws InterruptedException {
        Integer puid = null;
        Integer shopId = null;
        String campaignId = null;
        String type = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                campaignId = jsonObject.getString("campaignId");
                type = jsonObject.getString("type");
            }
        }

        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.cpcSbTargetSync(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, campaignId, type);
        return ReturnT.SUCCESS;
    }


    /**
     * 定时同步广告活动
     */
    @XxlJob(value = "cpcSpTargetSync")
    public ReturnT<String> cpcSpTargetSync(String params) throws InterruptedException {
        Integer puid = null;
        Integer shopId = null;
        String campaignId = null;
        String type = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                puid = jsonObject.getInteger("puid");
                shopId = jsonObject.getInteger("shopId");
                campaignId = jsonObject.getString("campaignId");
                type = jsonObject.getString("type");
            }
        }

        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.cpcSpTargetSync(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, campaignId, type);
        return ReturnT.SUCCESS;
    }

    /**
     * 统计搜索词大于5000的puid
     */
    @XxlJob(value = "statisticsQueryWord")
    public ReturnT<String> statisticsQueryWord(String params) throws IOException {
        Integer count = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                count = jsonObject.getInteger("count");
            }
        }
        cronjobService.statisticsQueryWord(count);
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "initBudgetCampaignIndex")
    public ReturnT<String> initBudgetCampaignIndex(String params) throws IOException {
        log.info(" begin initBudgetCampaignIndex");
        cronjobService.initBudgetCampaignIndex();
        return ReturnT.SUCCESS;
    }

    /**
     * 清理指定日期之前的亚马逊日志
     */
    @XxlJob(value = "deleteAdAmazonOperationLog")
    public ReturnT<String> deleteAdAmazonOperationLog(String params) {
        try {
            Integer limit = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    limit = jsonObject.getInteger("limit");
                }
            }
            cronjobService.deleteAdAmazonOperationLog(limit);
        } catch (Exception e) {
            log.error("清理指定日期之前的亚马逊日志: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 删除无效店铺数据
     */
    @XxlJob(value = "deleteInvalidShopDataService")
    public ReturnT<String> deleteInvalidShopDataService(String params) {
        try {
            Integer puid = null;
            Integer shopId = null;
            Integer totalLimit = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    totalLimit = jsonObject.getInteger("totalLimit");
                }
            }
            cronjobService.deleteInvalidShopDataService(puid, shopId, totalLimit);
        } catch (Exception e) {
            log.error("删除无效店铺数据: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 删除无效店铺数据
     */
    @XxlJob(value = "deleteMigrateOldData")
    public ReturnT<String> deleteMigrateOldData(String params) {
        try {
            Integer puid = null;
            String beanId = null;
            Integer totalLimit = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    beanId = jsonObject.getString("beanId");
                    totalLimit = jsonObject.getInteger("totalLimit");
                }
            }
            cronjobService.deleteMigrateOldData(puid, beanId, totalLimit);
        } catch (Exception e) {
            log.error("删除无效店铺数据: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 统计无效店铺数据
     */
    @XxlJob(value = "statisticsInvalidShopData")
    public ReturnT<String> statisticsInvalidShopData(String params) {
        try {
            cronjobService.statisticsInvalidShopData();
        } catch (Exception e) {
            log.error("统计无效店铺数据: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 同步受众列表
     */
    @XxlJob(value = "syncAudiences")
    public ReturnT<String> syncAudiences(String params) {
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        Result<?> result = cronjobService.syncAudiences(shardingVO.getIndex(), shardingVO.getTotal());
        return result.success() ? ReturnT.SUCCESS : ReturnT.FAIL;
    }

    /**
     * 数据库表大小监控
     */
    @XxlJob(value = "dbTableSizeMonitor")
    public ReturnT<String> dbTableSizeMonitor(String params) {
        int rows = 5000000;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                rows = jsonObject.getInteger("rows");
            }
        }
        cronjobService.dbTableSizeMonitor(rows);
        return ReturnT.SUCCESS;
    }

    /**
     * 商品透视分析初始化数据到热表中
     *
     * @param params
     * @return
     */
    @XxlJob(value = "initPerspectiveHotAsin")
    public ReturnT<String> initPerspectiveHotAsin(String params) {
        List<Integer> puidList = new ArrayList<>();
        Integer partition = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                partition = jsonObject.getInteger("partition");
                JSONArray jsonArray = jsonObject.getJSONArray("puid");
                if (!CollectionUtils.isEmpty(jsonArray)) {
                    jsonArray.forEach(x -> puidList.add(Integer.parseInt(x.toString())));
                }
            }
        }

        if (Objects.isNull(partition)) {
            partition = 10;
        }

        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        cronjobService.initPerspectiveHotAsin(shardingVO.getIndex(), shardingVO.getTotal(), puidList, partition);

        return ReturnT.SUCCESS;
    }


    /**
     * 店铺授权数据初始化同步重试店铺级任务
     *
     * @param params
     * @return
     */
    @XxlJob(value = "shopDataInitSyncRetry4ShopLevel")
    public ReturnT<String> shopDataInitSyncRetry4ShopLevel(String params) {
        try {
            log.info("shopDataInitSyncRetry4ShopLevel start");
            List<Integer> shopIdList = new ArrayList<>();
            if (StringUtils.isNotBlank(params)) {
                JSONArray jsonArray = JSONObject.parseArray(params);
                if (!CollectionUtils.isEmpty(jsonArray)) {
                    jsonArray.forEach(x -> shopIdList.add((Integer) x));
                }
            }

            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            cronjobService.shopDataInitSyncRetry4ShopLevel(shardingVO.getIndex(), shardingVO.getTotal(), shopIdList);
        } catch (Exception e) {
            log.error("shopDataInitSyncRetry4ShopLevel error", e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;

    }

    /**
     * 店铺授权数据初始化同步重试组级任务
     *
     * @param params
     * @return
     */
    @XxlJob(value = "shopDataInitSyncRetry4GroupLevel")
    public ReturnT<String> shopDataInitSyncRetry4GroupLevel(String params) {
        try {
            log.info("shopDataInitSyncRetry4GroupLevel start");
            List<Integer> shopIdList = new ArrayList<>();
            List<String> groupIdList = new ArrayList<>();
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    JSONArray shopIdArray = jsonObject.getJSONArray("shopId");
                    JSONArray groupIdArray = jsonObject.getJSONArray("groupId");
                    if (!CollectionUtils.isEmpty(shopIdArray)) {
                        shopIdArray.forEach(x -> shopIdList.add((Integer) x));
                    }
                    if (!CollectionUtils.isEmpty(groupIdArray)) {
                        groupIdArray.forEach(x -> groupIdList.add(x.toString()));
                    }
                }
            }

            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            cronjobService.shopDataInitSyncRetry4GroupLevel(shardingVO.getIndex(), shardingVO.getTotal(), shopIdList, groupIdList);
        } catch (Exception e) {
            log.error("shopDataInitSyncRetry4GroupLevel error", e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }


    /**
     * 店铺授权数据初始化历史数据清理
     *
     * @param params
     * @return
     */
    @XxlJob(value = "deleteShopDataInitHistoryData")
    public ReturnT<String> deleteShopDataInitHistoryData(String params) {
        List<Integer> shopIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(params)) {
            JSONArray jsonArray = JSONObject.parseArray(params);
            if (!CollectionUtils.isEmpty(jsonArray)) {
                jsonArray.forEach(x -> shopIdList.add((Integer) x));
            }
        }
        int deletedCount = cronjobService.deleteShopDataInitHistoryData(shopIdList, dynamicRefreshConfiguration.getDeleteTimeLimitSecond(), dynamicRefreshConfiguration.getOnceDelMaxCount());
        log.info("deleted {} historic tasks", deletedCount);

        return ReturnT.SUCCESS;
    }


    @XxlJob(value = "cpcStrategyRetry")
    public ReturnT<String> cpcStrategyRetry(String params) {
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        try {
            Integer puid = null;
            Integer shopId = null;
            Long templateId = null;
            Long taskId = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    templateId = jsonObject.getLong("templateId");
                    taskId = jsonObject.getLong("taskId");
                }
            }
            cpcStrategyService.cpcStrategyRetry(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, templateId, taskId);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("cpcStrategyRetry: ", e);
            return ReturnT.FAIL;
        }

    }

    @XxlJob(value = "deleteStrategyTask")
    public ReturnT<String> deleteStrategyTask(String params) {
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        try {
            Integer puid = null;
            Integer shopId = null;
            Long templateId = null;
            Long taskId = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    templateId = jsonObject.getLong("templateId");
                    taskId = jsonObject.getLong("taskId");
                }
            }
            cpcStrategyService.deleteStrategyTask(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, templateId, taskId);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("cpcStrategyRetry: ", e);
            return ReturnT.FAIL;
        }

    }

    @XxlJob(value = "cpcAdGroupStrategyRetry")
    public ReturnT<String> cpcAdGroupStrategyRetry(String params) {
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        try {
            String puid = null;
            String shopId = null;
            String adGroupId = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getString("puid");
                    shopId = jsonObject.getString("shopId");
                    adGroupId = jsonObject.getString("adGroupId");
                }
            }
            cpcStrategyService.cpcAdGroupStrategyRetry(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, adGroupId);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("cpcAdGroupStrategyRetry: ", e);
            return ReturnT.FAIL;
        }

    }

    @XxlJob(value = "checkCost")
    public ReturnT<String> checkCost(String params) {
        try {
            String puid = null;
            String shopId = null;
            String adGroupId = null;
            Map<String, Object> stringObjectMap = JSONUtil.jsonToObjects(params, new TypeReference<Map<String, Object>>() {
            });
            cronjobService.checkCost(stringObjectMap);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("checkCost: ", e);
            return ReturnT.FAIL;
        }

    }

    @XxlJob(value = "manualSyncOperationLog")
    public ReturnT<String> manualSyncOperationLog(String params) {
        try {
            int batchSize = 0;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    batchSize = Optional.ofNullable(jsonObject.getInteger("batchSize")).orElse(0);
                }
            }
            return cronjobService.manualSyncOperationLog(batchSize) ? ReturnT.SUCCESS : ReturnT.FAIL;
        } catch (Exception e) {
            log.error("manualSyncOperationLog: ", e);
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "clearHistoryOperationLog")
    public ReturnT<String> clearHistoryOperationLog(String params) {
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        try {
            int retainDays = 0;
            int limit = 0;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    retainDays = Optional.ofNullable(jsonObject.getInteger("retainDays")).orElse(0);
                    limit = Optional.ofNullable(jsonObject.getInteger("limit")).orElse(0);
                }
            }
            return cronjobService.clearHistoryOperationLog(retainDays, limit, shardingVO.getIndex(), shardingVO.getTotal())
                    ? ReturnT.SUCCESS : ReturnT.FAIL;
        } catch (Exception e) {
            log.error("clearHistoryOperationLog: ", e);
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "cpcAutoRuleRetry")
    public ReturnT<String> cpcAutoRuleRetry(String params) {
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        try {
            Integer puid = null;
            Integer shopId = null;
            Long templateId = null;
            Long taskId = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    templateId = jsonObject.getLong("templateId");
                    taskId = jsonObject.getLong("taskId");
                }
            }
            cpcAutoRuleService.cpcAutoRuleRetry(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, templateId, taskId);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("cpcAutoRuleRetry: ", e);
            return ReturnT.FAIL;
        }

    }

    /**
     * 填充自动化规则执行记录历史数据的广告组合id、活动id、组id等数据
     * @param params
     * @return
     */
    @XxlJob(value = "fixAutoRuleExecuteRecordData")
    public ReturnT<String> fixAutoRuleExecuteRecordData(String params) {
        log.info("handle auto rule blank record error start");
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        try {
            Integer puid = null;
            Integer shopId = null;
            String db = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                    shopId = jsonObject.getInteger("shopId");
                    db = jsonObject.getString("db");
                    if (Objects.isNull(puid) && Objects.nonNull(shopId)) {
                        log.error("handle auto rule blank record puid is null but shopId is not null, {}", shopId);
                        return ReturnT.FAIL;
                    }
                }
            }

            autoRuleExecuteRecordService.fixAutoRuleExecuteRecordData(shardingVO.getIndex(), shardingVO.getTotal(), puid, shopId, db);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("handle auto rule blank record excute error: ", e);
            return ReturnT.FAIL;
        }

    }


    /**
     * 线程池监控
     */
    @XxlJob(value = "threadpoolMonitor")
    public ReturnT<String> threadpoolMonitor(String params) {
        try {
            cronjobService.threadpoolMonitor();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("threadpoolMonitor execute error: ", e);
            return ReturnT.FAIL;
        }
    }
    /**
     * 报告层级差异监控
     */
    @XxlJob(value = "reportLevelDiffMonitorJob")
    public ReturnT<String> reportLevelDiffMonitorJob(String params) {
        try {
            cronjobService.reportLevelDiffMonitor();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("reportLevelDiffMonitorJob execute error: ", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 报告日期变化监控任务生成器
     * 对于唯一（广告类型、层级类型、创建时间）生成唯一一条记录，并记录各指标情况，和开始时间和下次采集时间。
     */
    @XxlJob(value = "createDateDiffTaskJob")
    public ReturnT<String> createDateDiffTaskJob(String params) {
        try {
            cronjobService.createDateDiffTaskJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("createDateDiffTaskJob execute error: ", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 报告日期变化监控
     * 每天不间断执行，搜索未执行完成且结束执行时间小于当前时间的记录，进行二次采集，得到最终结果并更新。
     */
    @XxlJob(value = "reportDateDiffMonitorJob")
    public ReturnT<String> reportDateDiffMonitorJob(String params) {
        try {
            cronjobService.reportDateDiffMonitorJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("reportDateDiffMonitorJob execute error: ", e);
            return ReturnT.FAIL;
        }
    }

    @Autowired
    private BudgetIndexService budgetIndexService;

    @XxlJob(value = "sendBudgetCampaignWarning")
    public ReturnT<String> sendBudgetCampaignWarning(String params) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(params);

            budgetIndexService.sendBudgetCampaignWarning(
                    jsonObject.getInteger("puid"),
                    jsonObject.getInteger("shopId"),
                    jsonObject.getString("campaignId"),
                    jsonObject.getString("budgetUsagePercentage"));
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("sendBudgetCampaignWarning excute error: ", e);
            return ReturnT.FAIL;
        }
    }


    @XxlJob(value = "removeExpiredTargetTaskRecords")
    public ReturnT<String> removeExpiredTargetTaskRecords(String params) throws InterruptedException {
        cronjobService.removeExpiredTargetTaskRecords();
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "removeOrRestartAbnormalTargetTaskRecords")
    public ReturnT<String> removeOrRestartAbnormalRecords(String params) throws InterruptedException {
        cronjobService.removeOrRestartAbnormalRecords();
        return ReturnT.SUCCESS;
    }

    /**
     * 清理搜索词词根脏数据
     */
    @XxlJob(value = "deleteWordRootQueryDirtyData")
    public ReturnT<String> deleteWordRootQueryDirtyData(String params) {
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        try {
            cronjobService.deleteWordRootQueryDirtyData(shardingVO.getIndex(), shardingVO.getTotal());
        } catch (Exception e) {
            log.error("清理搜索词词根脏数据异常: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 接口测速时间段对比监控
     *
     * @param params NginxCgiMonitorReportDto.json
     * @return
     */
    @XxlJob(value = "nginxCgiMonitorReport")
    public ReturnT<String> nginxCgiMonitorReport(String params) {
        NginxCgiMonitorReportDto reportDto = JSON.parseObject(params, NginxCgiMonitorReportDto.class);
        nginxCgiMonitorService.generateReport(reportDto);
        return ReturnT.SUCCESS;
    }


    /**
     * 清理热表数据
     */
    @XxlJob(value = "deleteColdTableData")
    public ReturnT<String> deleteColdTableData(String params) {

        String deleteBefore = null;
        Integer deleteByLimit = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                deleteBefore = jsonObject.getString("deleteBefore");
                deleteByLimit = jsonObject.getInteger("deleteByLimit");
            }
        }
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();

        try {
            cronjobService.deleteColdTableData(shardingVO.getIndex(), shardingVO.getTotal(), deleteBefore, deleteByLimit);
        } catch (Exception e) {
            log.error("清理冷表数据异常: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 删除历史导出任务
     */
    @XxlJob(value = "deleteAdManagePageExportTask")
    public ReturnT<String> deleteAdManagePageExportTask(String params) {
        Integer deleteBefore = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                deleteBefore = jsonObject.getInteger("deleteBefore");
            }
        }
        try {
            cronjobService.deleteAdManagePageExportTask(deleteBefore);
        } catch (Exception e) {
            log.error("deleteAdManagePageExportTask error: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 初始化用户关键词库标签
     * @param params
     * @return
     * @throws InterruptedException
     */
    @XxlJob(value = "initUserKeywordLibTag")
    public ReturnT<String> initUserKeywordLibTag(String params) throws InterruptedException {
        cronjobService.initUserKeywordLibTag(params);
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "initCommonKeywordLibTag")
    public ReturnT<String> initCommonKeywordLibTag(String params) throws InterruptedException {
        cronjobService.initCommonKeywordLibTag(params);
        return ReturnT.SUCCESS;
    }

    /**
     * 处理分时策略过期状态
     *
     * @param params params
     * @return
     */
    @XxlJob(value = "dealStrategyExpiredConfig")
    public ReturnT<String> dealStrategyExpiredConfig(String params) {
        log.info("dealStrategyExpiredConfig开始执行");
        cronjobService.dealStrategyExpiredConfig();
        log.info("dealStrategyExpiredConfig结束执行");
        return ReturnT.SUCCESS;
    }

    /**
     * 处理SP、SB关键词投放表词组数量统计 ods_t_amazon_ad_keyword ods_t_amazon_ad_sb_keyword t_amazon_ad_keyword t_amazon_ad_sb_keyword
     * @param params
     * @return
     * @throws InterruptedException
     */
    @XxlJob(value = "initKeywordSize")
    public ReturnT<String> initKeywordSize(String params) {
        try {
            Integer puid = null;
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                }
            }
            cronjobService.handlerInitKeywordSize(puid, shardingVO.getIndex(), shardingVO.getTotal());
        } catch (Exception e) {
            log.error("handle report queryId error: ", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 同步关键词库历史数据的加入时排名（多站点）
     * @param params
     * @return
     * @throws InterruptedException
     */
    @XxlJob(value = "syncKeywordJoiningRank")
    public ReturnT<String> syncKeywordJoiningRank(String params) throws InterruptedException {
        cronjobService.syncKeywordJoiningRank(params);
        return ReturnT.SUCCESS;
    }


    /**
     * 自动化规则报告数据是否拉取完成，历史数据导入
     *
     * @param params
     * @return
     */
    @XxlJob(value = "fixSdTargetingMerge")
    public ReturnT<String> fixSdTargetingMerge(String params) {
        log.info("fixSdTargetingMerge start");
        try {
            List<Integer> puidList = new ArrayList<>();
            if (StringUtils.isNotBlank(params)) {
                puidList = Arrays.stream(params.split(",")).map(Integer::parseInt).distinct().collect(Collectors.toList());
            }
            sdTargetingDataService.fixSdTargetingMerge(puidList);
            log.info("fixSdTargetingMerge end");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("fixSdTargetingMerge error: ", e);
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "initCampaignAdManageTagHandler")
    public ReturnT<String> initCampaignAdManageTagHandler(String params) throws InterruptedException {
        try {
            Integer puid = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = JSONObject.parseObject(params);
                if (jsonObject != null) {
                    puid = jsonObject.getInteger("puid");
                }
            }
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            adManageTagInitService.initCampaignAdManageTagHandler(shardingVO.getIndex(), shardingVO.getTotal(), puid);
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "initCampaignAdManageTagNewUserHandler")
    public ReturnT<String> initCampaignAdManageTagNewUserHandler(String params) throws InterruptedException {
        try {
            adManageTagInitService.initNewUser();
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 初始化用户关键词库标签-修正之前受到服务器时区影响而导致的时间偏移问题
     * @param params
     * @return
     * @throws InterruptedException
     */
    @XxlJob(value = "initKeywordLibUserTag")
    public ReturnT<String> initKeywordLibUserTag(String params) throws InterruptedException {
        cronjobService.initKeywordLibUserTag(params);
        return ReturnT.SUCCESS;
    }

    /**
     * 初始化关键词库公共标签-修正之前受到服务器时区影响而导致的时间偏移问题
     * @param params
     * @return
     * @throws InterruptedException
     */
    @XxlJob(value = "initKeywordLibCommonTag")
    public ReturnT<String> initKeywordLibCommonTag(String params) throws InterruptedException {
        cronjobService.initKeywordLibCommonTag(params);
        return ReturnT.SUCCESS;
    }

    /**
     * 处理asin 详情任务
     * @param params
     * @return
     * @throws InterruptedException
     */
    @XxlJob(value = "syncAsinInfo")
    public ReturnT<String> syncAsinInfo(String params) throws InterruptedException {
        cronjobService.syncAsinInfo(params);
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "syncUserPostProfiles")
    public ReturnT<String> syncUserPostProfiles(String params) throws InterruptedException {
        try {
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            cronjobService.syncUserPostProfiles(shardingVO.getIndex(), shardingVO.getTotal(), params);
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


    @XxlJob(value = "pricingStatusFix")
    public ReturnT<String> pricingStatusFix(String params) throws InterruptedException {
        try {
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            cronjobService.exePricingStatusFix();
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "datePrint")
    public ReturnT<String> datePrint(String params) throws InterruptedException {
        try {
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            DateTime dateTime = DateTime.now();
            LocalDateTime localDateTime = LocalDateTime.now();
            DateTimeZone dateTimeZone = DateTimeZone.getDefault();
            String userTimeZone = System.getProperty("user.timezone");
            TimeZone timeZone = TimeZone.getDefault();
            DateTimeZone dtz = DateTimeZone.getProvider().getZone(timeZone.getID());
            XxlJobLogger.log("dateTime:{}, localDateTime:{} zoneId:{} userTimeZone:{} dateTimeZone:{} timeZone:{}, dtz:{}",
                    dateTime, localDateTime, ZoneId.systemDefault(), userTimeZone, dateTimeZone, timeZone, dtz);
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "syncUserPosts")
    public ReturnT<String> syncUserPosts(String params) throws InterruptedException {
        try {
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            cronjobService.syncUserPosts(shardingVO.getIndex(), shardingVO.getTotal(), params);
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "syncPostReportIncrementData")
    public ReturnT<String> syncPostReportIncrementData(String params) throws InterruptedException {
        try {
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            cronjobService.syncPostReportIncrementData(shardingVO.getIndex(), shardingVO.getTotal(), params);
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "syncPostReportAllData")
    public ReturnT<String> syncPostReportAllData(String params) throws InterruptedException {
        try {
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            cronjobService.syncPostReportAllData(shardingVO.getIndex(), shardingVO.getTotal(), params);
        } catch (Exception e) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * doris数据变化监控
     * @param params
     * @return
     * @throws InterruptedException
     */
    @XxlJob(value = "statDorisData")
    public ReturnT<String> statDorisData(String params) throws InterruptedException {
        try {
            cronjobService.statDorisData();
        } catch (Exception e) {
            log.error("statDorisData error", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}