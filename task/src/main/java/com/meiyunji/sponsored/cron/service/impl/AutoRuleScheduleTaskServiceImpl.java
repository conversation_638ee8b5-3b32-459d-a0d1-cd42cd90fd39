package com.meiyunji.sponsored.cron.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadras.api.enumeration.AdvertiseRuleTaskTypePb;
import com.meiyunji.sellfox.aadras.api.service.AadrasApiGrpc;
import com.meiyunji.sellfox.aadras.api.service.ReportTriggerAutoRuleUpdateExecuteTimeSpaceRequestPb;
import com.meiyunji.sellfox.aadras.types.enumeration.AutoRuleScheduleTriggerRuleTaskItemType;
import com.meiyunji.sellfox.aadras.types.enumeration.Marketplace;
import com.meiyunji.sellfox.aadras.types.message.task.AutoRuleScheduleTriggerTaskMessage;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.cron.dto.UpdateSearchQueryToAadrasParam;
import com.meiyunji.sponsored.cron.service.IAutoRuleScheduleTaskService;
import com.meiyunji.sponsored.cron.service.helper.AutoRuleScheduleMonitorHelper;
import com.meiyunji.sponsored.service.aadrasGrpcApi.AadrasApiFactory;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.autoRule.dao.AdvertiseAutoRuleTemplateReportCompleteDao;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDao;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleTemplateDao;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleConstants;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleMonitorPrefixEnum;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleReportCompleteEnum;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleTemplateReportComplete;
import com.meiyunji.sponsored.service.autoRule.service.AdvertiseAutoRuleTemplateReportCompleteService;
import com.meiyunji.sponsored.service.autoRule.service.AdvertiseAutoRuleTemplateService;
import com.meiyunji.sponsored.service.autoRule.util.ReportTriggerAutoRuleHelper;
import com.meiyunji.sponsored.service.autoRule.vo.*;
import com.meiyunji.sponsored.service.autoRuleTask.enums.ChildrenItemType;
import com.meiyunji.sponsored.service.cache.dao.UserPlanTypeDao;
import com.meiyunji.sponsored.service.cache.enums.PayPackTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dao.impl.AmazonSbAdKeywordDaoImpl;
import com.meiyunji.sponsored.service.cpc.dao.impl.AmazonSbAdTargetingDaoImpl;
import com.meiyunji.sponsored.service.cpc.dao.impl.AmazonSdAdTargetingDaoImpl;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.common.base.Constants;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.strategy.enums.AdType;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTopBudgetTemplate;
import com.meiyunji.sponsored.service.strategy.service.IAdvertiseStrategyTemplateService;
import com.meiyunji.sponsored.service.strategy.service.IAdvertiseStrategyToBudgetTemplateService;
import com.meiyunji.sponsored.service.taskGrpcApi.ProfilesApi;
import com.meiyunji.sponsored.service.vo.DisabledTemplateDto;
import com.meiyunji.sponsored.service.vo.ShopAuthSellerInfoVo;
import io.grpc.ManagedChannel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.pulsar.client.api.Producer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 自动化规则定时任务执行服务实现类
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2024-06-17  10:45
 */

@Service
@Slf4j
public class AutoRuleScheduleTaskServiceImpl implements IAutoRuleScheduleTaskService {

    @Resource(name = "shardingJdbcMap")
    private Map<String, JdbcTemplate> jdbcTemplateMap;

    //所有的vip数据库
    @Resource(name = "vipJdbcTemplateMap")
    private Map<String, JdbcTemplate> vipJdbcTemplateMap;

    @Autowired
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;

    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Autowired
    private ReportTriggerAutoRuleHelper reportTriggerAutoRuleHelper;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;

    @Autowired
    private AdvertiseAutoRuleTemplateReportCompleteDao advertiseAutoRuleTemplateReportCompleteDao;

    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;

    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;

    @Autowired
    private Producer<byte[]> autoRuleScheduleTaskSpCampaignGroupTargetProducer;

    @Autowired
    private Producer<byte[]>  autoRuleScheduleTaskSpGroupKeywordProducer;

    @Autowired
    private Producer<byte[]>  autoRuleScheduleTaskSpKeywordProducer;

    @Autowired
    private Producer<byte[]> autoRuleScheduleTaskSbProducer;

    @Autowired
    private Producer<byte[]> autoRuleScheduleTaskSdProducer;

    @Autowired
    private AdvertiseAutoRuleTemplateReportCompleteService advertiseAutoRuleTemplateReportCompleteService;

    @Autowired
    private IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao;

    @Autowired
    private IAmazonAdTargetingShardingDao amazonAdTargetingShardingDao;

    @Autowired
    private AutoRuleScheduleMonitorHelper monitorHelper;

    @Autowired
    private IAdvertiseAutoRuleTemplateDao advertiseAutoRuleTemplateDao;

    @Autowired
    private ManagedChannel aadrasApiManagedChannel;

    @Autowired
    private AadrasApiFactory aadrasApiFactory;

    @Autowired
    private ISlaveAmazonAdProfileDao slaveAmazonAdProfileDao;

    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;

    @Autowired
    private AmazonSbAdTargetingDaoImpl amazonSbAdTargetingDao;

    @Autowired
    private AmazonSbAdKeywordDaoImpl amazonSbAdKeywordDao;

    @Autowired
    private AmazonSdAdTargetingDaoImpl amazonSdAdTargetingDao;

    @Autowired
    private UserPlanTypeDao userPlanTypeDao;

    @Autowired
    private AdvertiseAutoRuleTemplateService advertiseAutoRuleTemplateService;

    @Autowired
    private IAdvertiseStrategyTemplateService strategyTemplateService;

    @Autowired
    private IAdvertiseStrategyToBudgetTemplateService advertiseStrategyToBudgetTemplateService;

    @Autowired
    private ProfilesApi profilesApi;

    private List<JdbcTemplateVo> jdbcTemplateVoList = new ArrayList<>();

    //启动时加载，随机数据源顺序
    @PostConstruct
    public void initJdbcTemplateList() {
        List<String> keyList1 = new ArrayList<>(jdbcTemplateMap.keySet());
        Collections.sort(keyList1);
        keyList1.forEach(x -> jdbcTemplateVoList.add(new JdbcTemplateVo(x, jdbcTemplateMap.get(x))));

        List<String> keyList2 = new ArrayList<>(vipJdbcTemplateMap.keySet());
        Collections.sort(keyList2);
        keyList2.forEach(x -> jdbcTemplateVoList.add(new JdbcTemplateVo(x, vipJdbcTemplateMap.get(x))));
    }


    @Override
    public void reportCompleteDataImport() {
        List<JdbcTemplate> jdbcTemplateList = new ArrayList<>(jdbcTemplateMap.size() + vipJdbcTemplateMap.size());
        jdbcTemplateList.addAll(jdbcTemplateMap.values());
        jdbcTemplateList.addAll(vipJdbcTemplateMap.values());

        //遍历导入
        jdbcTemplateList.forEach(x -> {
            String sql = "select distinct puid, shop_id shopId from t_ad_auto_rule_template order by puid, shop_id";
            List<AutoRuleScheduleShopDto> list = x.query(sql, new BeanPropertyRowMapper<>(AutoRuleScheduleShopDto.class));
            if (CollectionUtils.isNotEmpty(list)) {
                List<AdvertiseAutoRuleTemplateReportComplete> collect = list.stream().map(shopDto -> new AdvertiseAutoRuleTemplateReportComplete(shopDto.getPuid(), shopDto.getShopId(), AutoRuleReportCompleteEnum.NOT_COMPLETE.getValue())).collect(Collectors.toList());
                List<List<AdvertiseAutoRuleTemplateReportComplete>> partitionList = Lists.partition(collect, 500);
                partitionList.forEach(p -> advertiseAutoRuleTemplateReportCompleteDao.insertOrUpdateList(p));
            }
        });
    }

    @Override
    public void updateSearchQueryToAadras(UpdateSearchQueryToAadrasParam param) {
        List<Long> allUpdateTaskIdList = new ArrayList<>();
        if (Objects.nonNull(param)) {
            List<AdvertiseAutoRuleStatus> list = advertiseAutoRuleStatusDao.queryUpdateSearchQueryToAadras(param.getPuid(), param.getTaskIdList());
            if (CollectionUtils.isNotEmpty(list)) {

                Set<Integer> shopIdSet = new HashSet<>();
                list.forEach(x -> shopIdSet.add(x.getShopId()));
                List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(param.getPuid(), new ArrayList<>(shopIdSet));
                Map<Integer, ShopAuth> shopMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(shopAuthList)) {
                    shopMap = shopAuthList.stream().filter(Objects::nonNull).collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
                }

                for (AdvertiseAutoRuleStatus status : list) {
                    try {
                        ShopAuth shopAuth = shopMap.get(status.getShopId());
                        if (Objects.nonNull(shopAuth)) {
                            updateSearchQueryToAadras(status, shopAuth);
                            allUpdateTaskIdList.add(status.getTaskId());
                        }
                    } catch (Exception e) {
                        log.error("updateSearchQueryToAadras error, taskId: {}", status.getTaskId(), e);
                    }
                }
            }
        } else {
            List<JdbcTemplate> jdbcTemplateList = new ArrayList<>(jdbcTemplateMap.size() + vipJdbcTemplateMap.size());
            jdbcTemplateList.addAll(jdbcTemplateMap.values());
            jdbcTemplateList.addAll(vipJdbcTemplateMap.values());
            //遍历处理
            jdbcTemplateList.forEach(x -> {
                String sql = "select a.* from t_ad_auto_rule_status a inner join t_ad_auto_rule_template b " +
                        "on a.puid = b.puid and a.shop_id = b.shop_id and a.template_id=b.id " +
                        "where a.rule_type ='searchQueryAutoUpdateBidding' " +
                        "and a.version = b.version " +
                        "and a.version > 1 " +
                        "and a.status = 'ENABLED' " +
                        "and DATE_FORMAT(a.update_time, '%Y-%m-%d %H:%i:%s') > DATE_FORMAT(a.create_time, '%Y-%m-%d %H:%i:%s') " +
                        "and a.create_time between '2024-07-24 22:30:00' and '2024-08-01 21:00:00' " +
                        "and a.update_time <= '2024-08-01 21:00:00'";
                List<AdvertiseAutoRuleStatus> list = x.query(sql, new BeanPropertyRowMapper<>(AdvertiseAutoRuleStatus.class));
                if (CollectionUtils.isNotEmpty(list)) {

                    Set<Integer> shopIdSet = new HashSet<>();
                    list.forEach(l -> shopIdSet.add(l.getShopId()));
                    List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(param.getPuid(), new ArrayList<>(shopIdSet));
                    Map<Integer, ShopAuth> shopMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(shopAuthList)) {
                        shopMap = shopAuthList.stream().filter(Objects::nonNull).collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
                    }

                    for (AdvertiseAutoRuleStatus status : list) {
                        try {
                            ShopAuth shopAuth = shopMap.get(status.getShopId());
                            if (Objects.nonNull(shopAuth)) {
                                updateSearchQueryToAadras(status, shopAuth);
                                allUpdateTaskIdList.add(status.getTaskId());
                            }
                        } catch (Exception e) {
                            log.error("updateSearchQueryToAadras error, taskId: {}", status.getTaskId(), e);
                        }
                    }
                }
            });
        }
        log.info("updateSearchQueryToAadras, total size:{}, taskIdList:{}", allUpdateTaskIdList.size(), allUpdateTaskIdList);
    }

    private void updateSearchQueryToAadras(AdvertiseAutoRuleStatus status, ShopAuth shopAuth) throws Exception {
        if ("SP".equals(status.getAdType())) {
            AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByAdGroupId(status.getPuid(), status.getShopId(),
                    status.getMarketplaceId(), status.getItemId());
            if (amazonAdGroup != null) {
                status.setTargetAdType("SP");
                status.setGroupType(amazonAdGroup.getAdGroupType());
                status.setTarGroupType(amazonAdGroup.getAdGroupType());
                status.setCampaignId(amazonAdGroup.getCampaignId());
                status.setAdGroupId(status.getQueryAdGroupId());
                status.setDefaultBid(BigDecimal.valueOf(amazonAdGroup.getDefaultBid()));
            }
        } else if ("SB".equals(status.getAdType())) {
            AmazonSbAdGroup amazonSbAdGroup = amazonSbAdGroupDao.getByGroupId(status.getPuid(), status.getShopId(), status.getItemId());
            if (amazonSbAdGroup != null) {
                status.setTargetAdType("SB");
                status.setGroupType(amazonSbAdGroup.getAdGroupType());
                status.setTarGroupType(amazonSbAdGroup.getAdGroupType());
                status.setCampaignId(amazonSbAdGroup.getCampaignId());
                status.setAdGroupId(status.getQueryAdGroupId());
                status.setDefaultBid(amazonSbAdGroup.getBid());
            }
        }

        if (StringUtils.isNotBlank(status.getPerformOperation())) {
            PerformOperationJson performOperationJson = JSONUtil.jsonToArray(status.getPerformOperation(), PerformOperationJson.class).get(0);
            if ("addTarget".equals(performOperationJson.getRuleAction()) && "1".equals(performOperationJson.getAppointAdGroupType())) {
                AmazonAdGroup queryAmazonAdGroup = amazonAdGroupDao.getByAdGroupId(status.getPuid(), status.getShopId(),
                        status.getMarketplaceId(), performOperationJson.getAdGroupId());
                if (queryAmazonAdGroup != null) {
                    status.setTargetAdType("SP");
                    status.setTarGroupType(queryAmazonAdGroup.getAdGroupType());
                    status.setDefaultBid(BigDecimal.valueOf(queryAmazonAdGroup.getDefaultBid()));
                }
                AmazonSbAdGroup queryAmazonSbAdGroup = amazonSbAdGroupDao.getByGroupId(status.getPuid(), status.getShopId(), performOperationJson.getAdGroupId());
                if (queryAmazonSbAdGroup != null) {
                    status.setTargetAdType("SB");
                    status.setTarGroupType(queryAmazonSbAdGroup.getAdGroupType());
                    status.setDefaultBid(queryAmazonSbAdGroup.getBid());
                }
            }
        }

        //推送至计算服务更新
        aadrasApiFactory.getAadrasApi(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.
                valueOf(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.GROUP_SEARCH_QUERY.name())).setAutoRuleTask(status.getTaskId(), status, shopAuth);
    }

    @Override
    public void checkReportComplete() {
        advertiseAutoRuleTemplateReportCompleteService.checkReportComplete();
    }

    @Override
    public void executeAutoRuleTask(int index, int total, List<AutoRuleItemTypeEnum> itemTypeEnumList, List<String> itemIdList, String adType) {

        List<String> itemTypeList = itemTypeEnumList.stream().map(AutoRuleItemTypeEnum::getName).collect(Collectors.toList());

        String puidStr = AdType.SP.name().equals(adType) ? dynamicRefreshNacosConfiguration.getReportTriggerAutoruleGrayPuid() : dynamicRefreshNacosConfiguration.getReportTriggerAutoruleSbSdGrayPuid();
        Integer percent = AdType.SP.name().equals(adType) ? dynamicRefreshNacosConfiguration.getReportTriggerAutoruleGrayPercent() : dynamicRefreshNacosConfiguration.getReportTriggerAutoruleSbSdGrayPercent();
        String scheduleNo = monitorHelper.getScheduleNo();

        //如果没有灰度
        if (AutoRuleConstants.GRAY_NONE.equals(puidStr) && AutoRuleConstants.GRAY_ZERO.equals(percent)) {
            log.error("executeAutoRuleTask, no gray data， scheduleNo: {}", scheduleNo);
            return;
        }

        //只有puid灰度，则可以只查puid对应的数据
        if (!StringUtils.equalsAny(puidStr, AutoRuleConstants.GRAY_ALL, AutoRuleConstants.GRAY_NONE) && StringUtils.isNotBlank(puidStr) && AutoRuleConstants.GRAY_ZERO.equals(percent)) {
            log.info("executeAutoRuleTask, gray puid: {}, scheduleNo: {}", puidStr, scheduleNo);

            List<Integer> puidList = Arrays.stream(puidStr.split(",")).map(Integer::parseInt).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(puidList)) {
                log.error("executeAutoRuleTask, no gray data, scheduleNo: {}", scheduleNo);
                return;
            }

            for (Integer puid : puidList) {
                //查询店铺id
                try {
                    //分片执行
                    if (puid % total == index) {
                        List<AutoRuleScheduleShopDto> shopList = advertiseAutoRuleStatusDao.getShopId4AutoRuleScheduleTask(puid, itemTypeList, itemIdList, adType);
                        if (CollectionUtils.isNotEmpty(shopList)) {
                            log.info("executeAutoRuleTask, itemType:{}, grayPuid: {}, shopId:{}, scheduleNo: {}", itemTypeList, puid, shopList, scheduleNo);
                            processAutoRule4ShopList(shopList, itemTypeList, false, null, itemIdList, adType);
                        }
                    }
                } catch (Exception e) {
                    log.error("executeAutoRuleTask error, grayPuid: {}, scheduleNo: {}", puid, scheduleNo, e);
                }
            }
            return ;
        }

        //需要遍历数据库
        List<JdbcTemplateVo> jdbcTemplateList = getJdbcTemplateList(index, total);
        jdbcTemplateList.forEach(jdbcTemplateVo -> {
            try {
                //查询出店铺和shop
                String sql = "select distinct puid, shop_id shopId " +
                        " from t_ad_auto_rule_template " +
                        " where status = 'ENABLED'";
                JdbcTemplate jdbcTemplate = jdbcTemplateVo.getJdbcTemplate();
                List<AutoRuleScheduleShopDto> shopList = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(AutoRuleScheduleShopDto.class));
                if (CollectionUtils.isNotEmpty(shopList)) {
                    log.info("executeAutoRuleTask, itemType:{}, shopId:{}, scheduleNo: {}", itemTypeList, shopList, scheduleNo);
                    processAutoRule4ShopList(shopList, itemTypeList, true, jdbcTemplateVo, null, adType);
                }
            } catch (Exception e) {
                log.error("executeAutoRuleTask error, error jdbctemplate key: {}, scheduleNo: {}", jdbcTemplateVo.getKey(), scheduleNo, e);
            }
        });
    }

    @Override
    public void updateGrayPuidExecuteTimeSpace(List<Integer> puidList) {

        if (puidList == null) {
            //更新全部
            List<JdbcTemplate> jdbcTemplateList = new ArrayList<>(jdbcTemplateMap.size() + vipJdbcTemplateMap.size());
            jdbcTemplateList.addAll(jdbcTemplateMap.values());
            jdbcTemplateList.addAll(vipJdbcTemplateMap.values());
            //遍历处理
            jdbcTemplateList.forEach(x -> updateGrayPuidExecuteTimeSpaceByJdbcTemplate(x));
            log.info("updateGrayPuidExecuteTimeSpace update all db success");
        } else {
            //指定puid更新
            puidList.forEach(x -> updateGrayPuidExecuteTimeSpaceByPuid(x));
            log.info("updateGrayPuidExecuteTimeSpace update all puid success");
        }

        //调用aadras接口处理aadras库
        try {
            AadrasApiGrpc.AadrasApiBlockingStub stub = AadrasApiGrpc.newBlockingStub(aadrasApiManagedChannel);
            ReportTriggerAutoRuleUpdateExecuteTimeSpaceRequestPb.ReportTriggerAutoRuleUpdateExecuteTimeSpaceRequest.Builder builder = ReportTriggerAutoRuleUpdateExecuteTimeSpaceRequestPb.ReportTriggerAutoRuleUpdateExecuteTimeSpaceRequest.newBuilder();
            if (puidList == null) {
                builder.setAll(true);
            } else {
                builder.addAllPuid(puidList);
            }
            stub.reportTriggerAutoRuleUpdateExecuteTimeSpace(builder.build());
            log.info("updateGrayPuidExecuteTimeSpace call aadras grpc success");
        } catch (Exception e) {
            log.error("updateGrayPuidExecuteTimeSpace call aadras grpc error", e);
        }
    }

    @Override
    public void updateGrayPuidExecuteTimeSpacePlus(Map<String, String[]>  executeTimeSpaceMap, List<Integer> puidList) {
        if (puidList == null) {
            //更新全部
            List<JdbcTemplate> jdbcTemplateList = new ArrayList<>(jdbcTemplateMap.size() + vipJdbcTemplateMap.size());
            jdbcTemplateList.addAll(jdbcTemplateMap.values());
            jdbcTemplateList.addAll(vipJdbcTemplateMap.values());
            //遍历处理
            jdbcTemplateList.forEach(x -> updateGrayPuidExecuteTimeSpacePlusByJdbcTemplate(executeTimeSpaceMap, x));
            log.info("updateGrayPuidExecuteTimeSpacePlus update all db success");
        } else {
            //指定puid更新
            puidList.forEach(x -> updateGrayPuidExecuteTimeSpacePlusByPuid(executeTimeSpaceMap, x));
            log.info("updateGrayPuidExecuteTimeSpacePlus update all puid success");
        }

        //调用aadras接口处理aadras库
        try {
            AadrasApiGrpc.AadrasApiBlockingStub stub = AadrasApiGrpc.newBlockingStub(aadrasApiManagedChannel);
            ReportTriggerAutoRuleUpdateExecuteTimeSpaceRequestPb.ReportTriggerAutoRuleUpdateExecuteTimeSpacePlusRequest.Builder builder = ReportTriggerAutoRuleUpdateExecuteTimeSpaceRequestPb.ReportTriggerAutoRuleUpdateExecuteTimeSpacePlusRequest.newBuilder();
            List<String> list = new ArrayList<>(6);
            executeTimeSpaceMap.forEach((k, v) -> {
                list.add(k + "-" + v[0] + "-" + v[1]);
            });
            builder.addAllTimeSpace(list);
            if (puidList == null) {
                builder.setAll(true);
            } else {
                builder.addAllPuid(puidList);
            }
            stub.reportTriggerAutoRuleUpdateExecuteTimeSpacePlus(builder.build());
            log.info("updateGrayPuidExecuteTimeSpacePlus call aadras grpc success");
        } catch (Exception e) {
            log.error("updateGrayPuidExecuteTimeSpacePlus call aadras grpc error", e);
        }
    }

    @Override
    public void updateAutoRuleState(int index, int total, List<AutoRuleItemTypeEnum> itemTypeEnumList, List<Integer> puids) {
        List<String> itemTypeList = itemTypeEnumList.stream().map(AutoRuleItemTypeEnum::getName).collect(Collectors.toList());
        List<Integer> puidList;
        int limit = 1000;
        int startIndex = 0;
        if (CollectionUtils.isNotEmpty(puids)) {
            for(Integer puid : puids) {
                updateAutoRuleAllState(puid, itemTypeList);
                log.info("updateAutoRuleAllState update success puid: {}", puid);
            }
        } else {
            while (true) {
                puidList = slaveAmazonAdProfileDao.getAllPuidByLimit(startIndex, limit);
                int count = puidList.size();
                //xxl-job采用分片广播策略，对puid取模分到不同的服务器上进行执行
//            puidList.stream()
//                    .filter(x -> x % total == index)
//                    .forEach(s -> updateAutoRuleAllState(s, itemTypeList, itemIdList));
                for (int puid : puidList) {
                    if (puid % total == index) {
                        updateAutoRuleAllState(puid, itemTypeList);
                    }
                    log.info("updateAutoRuleAllState update success puid: {}", puid);
                }
                if (count < limit) {
                    return;
                }
                startIndex += limit;
            }
        }
    }

    @Override
    public void expirePuidStopStrategy(int index, int total, List<Integer> puids) {
        //查询-99的puid
        List<Integer> expirePuidList = userPlanTypeDao.selectPuidByPlanType(PayPackTypeEnum.EXPIRE, puids);

        for (Integer puid : expirePuidList) {
            if (puid % total == index) {
                log.info("expirePuidStopStrategy process start puid: {}", puid);
                //停自动化和分时
                advertiseAutoRuleTemplateService.disableAllTemplateStatus(puid, null);
                strategyTemplateService.disableAllTemplateStatus(puid, null, true);
                //顶级预算
                advertiseStrategyToBudgetTemplateService.disableAllTemplateStatus(puid, null);
                log.info("expirePuidStopStrategy process success puid: {}", puid);
            }
        }

    }

    @Override
    public void rebuildAndRetry(int index, int total) {
        //处理每个数据库的禁用模板
        List<JdbcTemplateVo> jdbcTemplateList = getJdbcTemplateList(index, total);
        jdbcTemplateList.forEach(x -> {
            //自动化
            retryAutoRule(x);
            //分时
            retryStrategy(x);
            //顶级预算
            retryTopBudget(x);
        });
    }

    private void updateAutoRuleAllState(Integer puid, List<String> itemTypeList) {

        String itemType = StringUtils.join(itemTypeList, "','");

        List<AutoRuleScheduleShopDto> shopList = advertiseAutoRuleStatusDao.getShopIdByItemType(puid, itemType);

        if (CollectionUtils.isEmpty(shopList)) {
            return;
        }

        Set<Integer> shopIdSet = new HashSet<>();

        for (AutoRuleScheduleShopDto next : shopList) {
            shopIdSet.add(next.getShopId());
        }

        //查询店铺并转map
        List<ShopAuthSellerInfoVo> sellerInfoList = shopAuthDao.getSellerInfoByIdList(new ArrayList<>(shopIdSet));
        if (CollectionUtils.isEmpty(sellerInfoList)) {
            log.error("executeAutoRuleTask query seller info is empty, shopIdSet: {},", shopIdSet);
            return;
        }
        Map<Integer, ShopAuthSellerInfoVo> sellerInfoMap = sellerInfoList.stream().collect(Collectors.toMap(ShopAuthSellerInfoVo::getId, Function.identity(), (a, b) -> a));

        //查
        for (AutoRuleScheduleShopDto shopDto : shopList) {
            try {
                //店铺校验
                ShopAuthSellerInfoVo sellerInfoVo = sellerInfoMap.get(shopDto.getShopId());
                if (Objects.isNull(sellerInfoVo)) {
                    log.info("executeAutoRuleTask, no shop info or shop unauth, shopInfo: {}, scheduleNo: {}", shopDto);
                    continue;
                }

                Long startIndex = 0L;
                int limit = 1000;
                while (true) {
                    List<AdvertiseAutoRuleStatus> list = advertiseAutoRuleStatusDao.getList4AutoRuleScheduleTask(shopDto.getPuid(), shopDto.getShopId(), itemTypeList, startIndex, limit);
                    if (CollectionUtils.isEmpty(list)) {
                        break;
                    }
                    //处理
                    processAutoRuleStatus(sellerInfoVo, list);
                    //翻页完
                    if (list.size() < limit) {
                        break;
                    }
                    startIndex = list.get(list.size() - 1).getId();
                }
            } catch (Exception e) {
                log.error("executeAutoRuleTask error, shopInfo: {}, error: {}", shopDto, e);
            }
        }
    }

    /**
     * 处理当前页的受控对象
     * @param sellerInfoVo 店铺信息
     * @param list 当前页受控对象数据
     */
    private void processAutoRuleStatus(ShopAuthSellerInfoVo sellerInfoVo, List<AdvertiseAutoRuleStatus> list) {
        //收集不同类型的受控对象
        Map<AutoRuleItemTypeEnum, List<AdvertiseAutoRuleStatus>> typeStatusMap = new HashMap<>();

        //数据按照受控对象类型分类
        for (AdvertiseAutoRuleStatus dto : list) {

            //数据分类处理
            AutoRuleItemTypeEnum itemTypeEnum = AutoRuleItemTypeEnum.map.get(dto.getItemType());
            if (Objects.isNull(itemTypeEnum)) {
                continue;
            }

            List<AdvertiseAutoRuleStatus> itemTypeDataList = typeStatusMap.get(itemTypeEnum);

            if (Objects.isNull(itemTypeDataList)) {
                itemTypeDataList = new ArrayList<>();
                itemTypeDataList.add(dto);
                typeStatusMap.put(itemTypeEnum, itemTypeDataList);
            } else {
                itemTypeDataList.add(dto);
            }
        }
        if (MapUtils.isEmpty(typeStatusMap)) {
            return;
        }

        //处理各种受控对象
        typeStatusMap.forEach((k, v) -> {
            Map<String, List<AdvertiseAutoRuleStatus>> map = v.stream()
                    .collect(Collectors.groupingBy(AdvertiseAutoRuleStatus::getAdType));
            List<AdvertiseAutoRuleStatus> spValue = map.get("SP");
            List<AdvertiseAutoRuleStatus> sbValue = map.get("SB");
            List<AdvertiseAutoRuleStatus> sdValue = map.get("SD");
            switch (k) {
                //活动
                case CAMPAIGN:
                    if (CollectionUtils.isNotEmpty(v)) {
                        checkCampaignArchived(v, sellerInfoVo);
                    }
                    break;
                //广告组
                case AD_GROUP:
                    //SP类型的广告组
                    if (CollectionUtils.isNotEmpty(spValue)) {
                        checkSpGroupArchived(spValue, sellerInfoVo);
                    }
                    //SB类型的广告组
                    if (CollectionUtils.isNotEmpty(sbValue)) {
                        checkSbGroupArchived(sbValue, sellerInfoVo);
                    }
                    //SD类型的广告组
                    if (CollectionUtils.isNotEmpty(sdValue)) {
                        checkSdGroupArchived(sdValue, sellerInfoVo);
                    }
                    break;
                //投放
                case TARGET:
                    //注意：投放类型为TARGET时，item_id不一定为keyword_id或target_id
                    // 需要判断他的children_item_type：
                    // 如果children_item_type为CHILDREN_TARGET_GROUP，则为组受控，对应的item_id为group_id；否则对应的item_id为keyword_id或target_id
                    //SP类型的投放
                    if (CollectionUtils.isNotEmpty(spValue)) {
                        List<AdvertiseAutoRuleStatus> spGroupValue = new ArrayList<>();
                        List<AdvertiseAutoRuleStatus> spTargetValue = new ArrayList<>();
                        for (AdvertiseAutoRuleStatus value : spValue) {
                            if (AutoRuleChildrenItemTypeEnum.CHILDREN_TARGET_GROUP.name().equals(value.getChildrenItemType())) {
                                spGroupValue.add(value);
                            } else {
                                spTargetValue.add(value);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(spGroupValue)) {
                            checkSpGroupArchived(spGroupValue, sellerInfoVo);
                        }
                        if (CollectionUtils.isNotEmpty(spTargetValue)) {
                            checkSpTargetArchived(spTargetValue, sellerInfoVo);
                        }
                    }
                    //SB类型的投放
                    if (CollectionUtils.isNotEmpty(sbValue)) {
                        List<AdvertiseAutoRuleStatus> sbGroupValue = new ArrayList<>();
                        List<AdvertiseAutoRuleStatus> sbTargetValue = new ArrayList<>();
                        for (AdvertiseAutoRuleStatus value : sbValue) {
                            if (AutoRuleChildrenItemTypeEnum.CHILDREN_TARGET_GROUP.name().equals(value.getChildrenItemType())) {
                                sbGroupValue.add(value);
                            } else {
                                sbTargetValue.add(value);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(sbGroupValue)) {
                            checkSbGroupArchived(sbGroupValue, sellerInfoVo);
                        }
                        if (CollectionUtils.isNotEmpty(sbTargetValue)) {
                            checkSbTargetArchived(sbTargetValue, sellerInfoVo);
                        }
                    }
                    //SD类型的投放
                    if (CollectionUtils.isNotEmpty(sdValue)) {
                        List<AdvertiseAutoRuleStatus> sdGroupValue = new ArrayList<>();
                        List<AdvertiseAutoRuleStatus> sdTargetValue = new ArrayList<>();
                        for (AdvertiseAutoRuleStatus value : sdValue) {
                            if (AutoRuleChildrenItemTypeEnum.CHILDREN_TARGET_GROUP.name().equals(value.getChildrenItemType())) {
                                sdGroupValue.add(value);
                            } else {
                                sdTargetValue.add(value);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(sdGroupValue)) {
                            checkSdGroupArchived(sdGroupValue, sellerInfoVo);
                        }
                        if (CollectionUtils.isNotEmpty(sdTargetValue)) {
                            checkSdTargetArchived(sdTargetValue, sellerInfoVo);
                        }
                    }
                    break;
                default:
                    break;
            }
        });
    }

    private void checkSdTargetArchived(List<AdvertiseAutoRuleStatus> statusList, ShopAuthSellerInfoVo sellerInfoVo) {
        Set<String> targetItemIdSet = new HashSet<>();
        statusList.forEach(x -> targetItemIdSet.add(x.getItemId()));
        List<String> targetItemIdList = new ArrayList<>(targetItemIdSet);

        //查询对应投放的状态
        //商品投放
        List<AmazonSdAdTargeting> targetingList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(targetItemIdList)) {
            targetingList = amazonSdAdTargetingDao.getArchivedTargetingByAdTargetIds(sellerInfoVo.getPuid(), sellerInfoVo.getId(), targetItemIdList);
        }

        //收集需要更新的数据
        List<String> archivedItemIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(targetingList)) {
            archivedItemIdList = targetingList.stream().map(AmazonSdAdTargeting::getTargetId).collect(Collectors.toList());
        }

        //更新
        if (CollectionUtils.isNotEmpty(archivedItemIdList)) {
            advertiseAutoRuleStatusDao.batchUpdateArchivedState(sellerInfoVo.getPuid(), sellerInfoVo.getId(), AutoRuleItemTypeEnum.TARGET.getName(), archivedItemIdList);
        }
    }

    private void checkSbTargetArchived(List<AdvertiseAutoRuleStatus> statusList, ShopAuthSellerInfoVo sellerInfoVo) {
        Set<String> targetItemIdSet = new HashSet<>();
        Set<String> keywordItemIdSet = new HashSet<>();
        statusList.forEach(x -> {
            if (Constants.KEYWORD_TARGET.equals(x.getTargetType())) {
                keywordItemIdSet.add(x.getItemId());
            } else {
                targetItemIdSet.add(x.getItemId());
            }
        });
        List<String> targetItemIdList = new ArrayList<>(targetItemIdSet);
        List<String> keywordItemIdList = new ArrayList<>(keywordItemIdSet);

        //查询对应投放的状态
        //商品投放
        List<AmazonSbAdTargeting> targetingList = new ArrayList<>();
        List<AmazonSbAdKeyword> keywordList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(targetItemIdList)) {
            targetingList = amazonSbAdTargetingDao.getArchivedTargetingByAdTargetIds(sellerInfoVo.getPuid(), sellerInfoVo.getId(), targetItemIdList);
        }

        //关键词投放
        if (CollectionUtils.isNotEmpty(keywordItemIdList)) {
            keywordList = amazonSbAdKeywordDao.getArchivedKeywordByKeywordIds(sellerInfoVo.getPuid(), sellerInfoVo.getId(), keywordItemIdList);
        }

        //收集需要更新的数据
        List<String> archivedItemIdList = new ArrayList<>();
        List<String> targetId = new ArrayList<>();
        List<String> keywordId = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(targetingList)) {
            targetId = targetingList.stream().map(AmazonSbAdTargeting::getTargetId).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(keywordList)) {
            keywordId = keywordList.stream().map(AmazonSbAdKeyword::getKeywordId).collect(Collectors.toList());
        }
        archivedItemIdList.addAll(targetId);
        archivedItemIdList.addAll(keywordId);

        //更新
        if (CollectionUtils.isNotEmpty(archivedItemIdList)) {
            advertiseAutoRuleStatusDao.batchUpdateArchivedState(sellerInfoVo.getPuid(), sellerInfoVo.getId(), AutoRuleItemTypeEnum.TARGET.getName(), archivedItemIdList);
        }
    }

    private void checkSpTargetArchived(List<AdvertiseAutoRuleStatus> statusList, ShopAuthSellerInfoVo sellerInfoVo) {
        Set<String> targetItemIdSet = new HashSet<>();
        Set<String> keywordItemIdSet = new HashSet<>();
        statusList.forEach(x -> {
            if (Constants.KEYWORD_TARGET.equals(x.getTargetType())) {
                keywordItemIdSet.add(x.getItemId());
            } else {
                targetItemIdSet.add(x.getItemId());
            }
        });
        List<String> targetItemIdList = new ArrayList<>(targetItemIdSet);
        List<String> keywordItemIdList = new ArrayList<>(keywordItemIdSet);

        //查询对应投放的状态
        //商品投放
        List<AmazonAdTargeting> targetingList = new ArrayList<>();
        List<AmazonAdKeyword> keywordList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(targetItemIdList)) {
            targetingList = amazonAdTargetingShardingDao.getArchivedTargetingByAdTargetIds(sellerInfoVo.getPuid(), sellerInfoVo.getId(), targetItemIdList);
        }

        //关键词投放
        if (CollectionUtils.isNotEmpty(keywordItemIdList)) {
            keywordList = amazonAdKeywordShardingDao.getArchivedKeywordByKeywordIds(sellerInfoVo.getPuid(), sellerInfoVo.getId(), keywordItemIdList);
        }

        //收集需要更新的数据
        List<String> archivedItemIdList = new ArrayList<>();
        List<String> targetId = new ArrayList<>();
        List<String> keywordId = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(targetingList)) {
            targetId = targetingList.stream().map(AmazonAdTargeting::getTargetId).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(keywordList)) {
            keywordId = keywordList.stream().map(AmazonAdKeyword::getKeywordId).collect(Collectors.toList());
        }
        archivedItemIdList.addAll(targetId);
        archivedItemIdList.addAll(keywordId);

        //更新
        if (CollectionUtils.isNotEmpty(archivedItemIdList)) {
            advertiseAutoRuleStatusDao.batchUpdateArchivedState(sellerInfoVo.getPuid(), sellerInfoVo.getId(), AutoRuleItemTypeEnum.TARGET.getName(), archivedItemIdList);
        }
    }

    /**
     * 检查sd的广告组是否归档并处理
     * @param statusList
     * @param sellerInfoVo
     */
    private void checkSdGroupArchived(List<AdvertiseAutoRuleStatus> statusList, ShopAuthSellerInfoVo sellerInfoVo) {
        Set<String> itemIdSet = new HashSet<>();
        statusList.forEach(x -> itemIdSet.add(x.getItemId()));
        List<String> itemIdList = new ArrayList<>(itemIdSet);
        List<AmazonSdAdGroup> adGroupList = amazonSdAdGroupDao.getAdGroupByIds(sellerInfoVo.getPuid(), sellerInfoVo.getId(), itemIdList);

        Set<String> archivedAdGroupIdSet = new HashSet<>();
        adGroupList.forEach(x -> {
            if (AllAdStateEnum.archived.getStateType().equals(x.getState())) {
                //归档的组
                archivedAdGroupIdSet.add(x.getAdGroupId());
            }
        });

        //移除归档数据
        Iterator<AdvertiseAutoRuleStatus> iterator = statusList.iterator();

        //收集需要更新的数据
        List<String> archivedItemIdList = new ArrayList<>();

        while (iterator.hasNext()) {
            //组归档或所属活动归档
            AdvertiseAutoRuleStatus next = iterator.next();
            if (archivedAdGroupIdSet.contains(next.getItemId())) {
                archivedItemIdList.add(next.getItemId());
                iterator.remove();
            }
        }

        //更新
        if (CollectionUtils.isNotEmpty(archivedItemIdList)) {
            //组受控投放
            if (AutoRuleChildrenItemTypeEnum.CHILDREN_TARGET_GROUP.name().equals(statusList.get(0).getChildrenItemType())) {
                advertiseAutoRuleStatusDao.batchUpdateArchivedState(sellerInfoVo.getPuid(), sellerInfoVo.getId(), AutoRuleItemTypeEnum.TARGET.getName(), archivedItemIdList);
            } else {
                advertiseAutoRuleStatusDao.batchUpdateArchivedState(sellerInfoVo.getPuid(), sellerInfoVo.getId(), AutoRuleItemTypeEnum.AD_GROUP.getName(), archivedItemIdList);
            }
        }
    }

    /**
     * 检查sb的广告组是否归档并处理
     * @param statusList
     * @param sellerInfoVo
     */
    private void checkSbGroupArchived(List<AdvertiseAutoRuleStatus> statusList, ShopAuthSellerInfoVo sellerInfoVo) {
        Set<String> itemIdSet = new HashSet<>();
        statusList.forEach(x -> itemIdSet.add(x.getItemId()));
        List<String> itemIdList = new ArrayList<>(itemIdSet);
        List<AmazonSbAdGroup> adGroupList = amazonSbAdGroupDao.getAdGroupByIds(sellerInfoVo.getPuid(), sellerInfoVo.getId(), itemIdList);

        Set<String> archivedAdGroupIdSet = new HashSet<>();
        adGroupList.forEach(x -> {
            if (AllAdStateEnum.archived.getStateType().equals(x.getState())) {
                //归档的组
                archivedAdGroupIdSet.add(x.getAdGroupId());
            }
        });

        //移除归档数据
        Iterator<AdvertiseAutoRuleStatus> iterator = statusList.iterator();

        //收集需要更新的数据
        List<String> archivedItemIdList = new ArrayList<>();

        while (iterator.hasNext()) {
            //组归档或所属活动归档
            AdvertiseAutoRuleStatus next = iterator.next();
            if (archivedAdGroupIdSet.contains(next.getItemId())) {
                archivedItemIdList.add(next.getItemId());
                iterator.remove();
            }
        }

        //更新
        if (CollectionUtils.isNotEmpty(archivedItemIdList)) {
            //组受控投放
            if (AutoRuleChildrenItemTypeEnum.CHILDREN_TARGET_GROUP.name().equals(statusList.get(0).getChildrenItemType())) {
                advertiseAutoRuleStatusDao.batchUpdateArchivedState(sellerInfoVo.getPuid(), sellerInfoVo.getId(), AutoRuleItemTypeEnum.TARGET.getName(), archivedItemIdList);
            } else {
                advertiseAutoRuleStatusDao.batchUpdateArchivedState(sellerInfoVo.getPuid(), sellerInfoVo.getId(), AutoRuleItemTypeEnum.AD_GROUP.getName(), archivedItemIdList);
            }
        }
    }

    /**
     * 检查sp的广告组是否归档并处理
     * @param statusList
     * @param sellerInfoVo
     */
    private void checkSpGroupArchived(List<AdvertiseAutoRuleStatus> statusList, ShopAuthSellerInfoVo sellerInfoVo) {
        Set<String> itemIdSet = new HashSet<>();
        statusList.forEach(x -> itemIdSet.add(x.getItemId()));
        List<String> itemIdList = new ArrayList<>(itemIdSet);
        List<AmazonAdGroup> adGroupList = amazonAdGroupDao.getAdGroupByIds(sellerInfoVo.getPuid(), sellerInfoVo.getId(), itemIdList);

        Set<String> archivedAdGroupIdSet = new HashSet<>();
        adGroupList.forEach(x -> {
            if (AllAdStateEnum.archived.getStateType().equals(x.getState())) {
                //归档的组
                archivedAdGroupIdSet.add(x.getAdGroupId());
            }
        });

        //移除归档数据
        Iterator<AdvertiseAutoRuleStatus> iterator = statusList.iterator();

        //收集需要更新的数据
        List<String> archivedItemIdList = new ArrayList<>();

        while (iterator.hasNext()) {
            //组归档或所属活动归档
            AdvertiseAutoRuleStatus next = iterator.next();
            if (archivedAdGroupIdSet.contains(next.getItemId())) {
                archivedItemIdList.add(next.getItemId());
                iterator.remove();
            }
        }

        //更新
        if (CollectionUtils.isNotEmpty(archivedItemIdList)) {
            //组受控投放
            if (AutoRuleChildrenItemTypeEnum.CHILDREN_TARGET_GROUP.name().equals(statusList.get(0).getChildrenItemType())) {
                advertiseAutoRuleStatusDao.batchUpdateArchivedState(sellerInfoVo.getPuid(), sellerInfoVo.getId(), AutoRuleItemTypeEnum.TARGET.getName(), archivedItemIdList);
            } else {
                advertiseAutoRuleStatusDao.batchUpdateArchivedState(sellerInfoVo.getPuid(), sellerInfoVo.getId(), AutoRuleItemTypeEnum.AD_GROUP.getName(), archivedItemIdList);
            }
        }
    }


    /**
     * 处理所有店铺的的受控对象
     * @param shopList
     */
    private void processAutoRule4ShopList(List<AutoRuleScheduleShopDto> shopList, List<String> itemTypeList, boolean processGray, JdbcTemplateVo jdbcTemplateVo, List<String> itemIdList, String adType) {

        if (CollectionUtils.isEmpty(shopList)) {
            return;
        }

        String scheduleNo = monitorHelper.getScheduleNo();

        Set<Integer> puidSet = new HashSet<>();
        Set<Integer> shopIdSet = new HashSet<>();

        //灰度和数据分库路由校验
        Iterator<AutoRuleScheduleShopDto> iterator1 = shopList.iterator();
        while (iterator1.hasNext()) {
            AutoRuleScheduleShopDto next = iterator1.next();
            //灰度
            if (processGray) {
                //不满足灰度
                if (!reportTriggerAutoRuleHelper.isGray(next.getPuid(), adType)) {
                    log.error("executeAutoRuleTask not gray, puid: {}, scheduleNo: {}", next.getPuid(), scheduleNo);
                    iterator1.remove();
                    continue;
                }
            }
            //数据分库准确性校验
            if (Objects.nonNull(jdbcTemplateVo) && !Objects.equals(jdbcTemplateVo.getJdbcTemplate(), advertiseAutoRuleStatusDao.getJdbcTemplate(next.getPuid()))) {
                log.error("executeAutoRuleTask not correct db, puid: {}, jdbctemplate key: {}, scheduleNo: {}", next.getPuid(), jdbcTemplateVo.getKey(), scheduleNo);
                iterator1.remove();
                continue;
            }
            puidSet.add(next.getPuid());
            shopIdSet.add(next.getShopId());
        }

        if (CollectionUtils.isEmpty(shopList)) {
            log.error("executeAutoRuleTask filter gray shopIdList is empty, scheduleNo: {}", scheduleNo);
            return;
        }

        //查询报告拉取完成
        List<AdvertiseAutoRuleTemplateReportComplete> reportCompleteList = advertiseAutoRuleTemplateReportCompleteDao.getByPuidAndShopIdList(new ArrayList<>(puidSet), new ArrayList<>(shopIdSet));
        Set<Integer> completeShopIdSet = reportCompleteList.stream().filter(x -> AutoRuleReportCompleteEnum.COMPLETE.getValue() == x.getReportComplete()).map(AdvertiseAutoRuleTemplateReportComplete::getShopId).collect(Collectors.toSet());

        //移除未拉取完成的
        Set<Integer> execShopIdSet = new HashSet<>();
        Iterator<AutoRuleScheduleShopDto> iterator2 = shopList.iterator();
        while (iterator2.hasNext()) {
            AutoRuleScheduleShopDto next = iterator2.next();
            //报告是否拉取完成
            if (!completeShopIdSet.contains(next.getShopId())) {
                log.error("executeAutoRuleTask report not complete, puid: {}, shopId: {}, scheduleNo: {}", next.getPuid(), next.getShopId(), scheduleNo);
                iterator2.remove();
                continue;
            }
            execShopIdSet.add(next.getShopId());
        }

        if (CollectionUtils.isEmpty(shopList)) {
            log.error("executeAutoRuleTask filter report complete shopIdList is empty, scheduleNo: {}", scheduleNo);
            return;
        }

        //查询店铺并转map
        List<ShopAuthSellerInfoVo> sellerInfoList = shopAuthDao.getSellerInfoByIdList(new ArrayList<>(execShopIdSet));
        if (CollectionUtils.isEmpty(sellerInfoList)) {
            log.error("executeAutoRuleTask query seller info is empty, shopIdSet: {}, scheduleNo: {}", execShopIdSet, scheduleNo);
            return;
        }
        Map<Integer, ShopAuthSellerInfoVo> sellerInfoMap = sellerInfoList.stream().collect(Collectors.toMap(ShopAuthSellerInfoVo::getId, Function.identity(), (a, b) -> a));


        //查询
        for (AutoRuleScheduleShopDto shopDto : shopList) {
            try {
                //店铺校验
                ShopAuthSellerInfoVo sellerInfoVo = sellerInfoMap.get(shopDto.getShopId());
                if (Objects.isNull(sellerInfoVo)) {
                    log.info("executeAutoRuleTask, no shop info or shop unauth, shopInfo: {}, scheduleNo: {}", shopDto, scheduleNo);
                    continue;
                }

                Marketplace marketplace = Marketplace.fromId(sellerInfoVo.getMarketplaceId());
                if (Objects.isNull(marketplace)) {
                    log.info("executeAutoRuleTask, no marketplace info, shopInfo: {}, scheduleNo: {}", shopDto, scheduleNo);
                    continue;
                }

                String startIndex = "0";
                int limit = 1000;
                while (true) {
                    //开始和结束时间
                    LocalDate localDate = LocalDateTime.now(marketplace.getTimeZone().toZoneId()).toLocalDate();
                    List<AutoRuleScheduleTaskDto> list = advertiseAutoRuleStatusDao.getList4AutoRuleScheduleTask(shopDto.getPuid(), shopDto.getShopId(), itemTypeList, itemIdList, localDate, startIndex, limit, adType);
                    if (CollectionUtils.isEmpty(list)) {
                        break;
                    }
                    //处理
                    processAutoRuleStatusList(sellerInfoVo, list, adType);
                    //翻页完
                    if (list.size() < limit) {
                        break;
                    }
                    startIndex = list.get(list.size() - 1).getItemId();
                }
            } catch (Exception e) {
                log.error("executeAutoRuleTask error, shopInfo: {}, scheduleNo: {}", shopDto, scheduleNo, e);
            }
        }
    }


    /**
     * 处理当前页的受控对象
     * @param sellerInfoVo 店铺信息
     * @param list 当前页受控对象数据
     */
    private void processAutoRuleStatusList(ShopAuthSellerInfoVo sellerInfoVo, List<AutoRuleScheduleTaskDto> list, String adType) {

        //收集不同类型的受控对象
        Map<AutoRuleItemTypeEnum, List<AutoRuleScheduleTaskDto>> typeStatusMap = new HashMap<>();

        //数据按照受控对象类型分类
        for (AutoRuleScheduleTaskDto dto : list) {

            dto.setTaskIdList(Arrays.stream(dto.getTaskId().split(",")).map(Long::parseLong).collect(Collectors.toList()));

            //数据分类处理
            AutoRuleItemTypeEnum itemTypeEnum = AutoRuleItemTypeEnum.map.get(dto.getItemType());
            if (Objects.isNull(itemTypeEnum)) {
                continue;
            }

            //投放数据正确性
            if (AutoRuleItemTypeEnum.TARGET.getName().equals(dto.getItemType())) {
                if (!AutoRuleTargetTypeEnum.set.contains(dto.getTargetType())) {
                    continue;
                }
            }

            List<AutoRuleScheduleTaskDto> itemTypeDataList = typeStatusMap.get(itemTypeEnum);
            if (Objects.isNull(itemTypeDataList)) {
                itemTypeDataList = new ArrayList<>();
                itemTypeDataList.add(dto);
                typeStatusMap.put(itemTypeEnum, itemTypeDataList);
            } else {
                itemTypeDataList.add(dto);
            }
        }

        if (MapUtils.isEmpty(typeStatusMap)) {
            return;
        }

        //处理各种受控对象
        //根据数据统计，我们把单个搜索词、组受控搜索词、其余(活动、组、单个投放、组受控投放)各投递到单独一个队列
        typeStatusMap.forEach((k, v) -> {
            switch (k) {
                //活动
                case CAMPAIGN:
                    processAndSendCampaign(v, sellerInfoVo, adType);
                    break;
                //广告组
                case AD_GROUP:
                    processAndSendGroup(v, sellerInfoVo, adType);
                    break;
                //投放
                case TARGET:
                    processAndSendTarget(v, sellerInfoVo, adType);
                    break;
                //搜索词使用报告触发，非定时触发
                default:
                    break;
            }
        });

    }

    /**
     * 处理广告活动受控对象
     * @param statusList
     * @param sellerInfoVo
     */
    private void processAndSendCampaign(List<AutoRuleScheduleTaskDto> statusList, ShopAuthSellerInfoVo sellerInfoVo, String adType) {
        //记录监控
        monitorHelper.addScanCount(AutoRuleScheduleTriggerRuleTaskItemType.CAMPAIGN, statusList.size());

        //检查归档
//        checkCampaignArchived(statusList, sellerInfoVo);

        if (CollectionUtils.isEmpty(statusList)) {
            return;
        }

        //组装和发送数据到aadras
        List<AutoRuleScheduleTriggerTaskMessage> messageList = buildMessage(AutoRuleScheduleTriggerRuleTaskItemType.CAMPAIGN, sellerInfoVo, statusList, dynamicRefreshNacosConfiguration.getReportTriggerAutorulePartitionSize(), adType);

        LocalDateTime nextExecuteTime = computeNextExecuteTime(sellerInfoVo.getMarketplaceId(), 30);

        //发送队列
        int itemCount = 0;
        int taskCount = 0;
        for (AutoRuleScheduleTriggerTaskMessage x : messageList) {
            boolean sendSuccess = true;
            try {
                if (AdType.SP.name().equals(adType)) {
                    autoRuleScheduleTaskSpCampaignGroupTargetProducer.send(JSON.toJSONString(x).getBytes(StandardCharsets.UTF_8));
                } else if (AdType.SB.name().equals(adType)) {
                    autoRuleScheduleTaskSbProducer.send(JSON.toJSONString(x).getBytes(StandardCharsets.UTF_8));
                } else if (AdType.SD.name().equals(adType)) {
                    autoRuleScheduleTaskSdProducer.send(JSON.toJSONString(x).getBytes(StandardCharsets.UTF_8));
                }
            } catch (Exception e) {
                sendSuccess = false;
                log.warn("executeAutoRuleTask, send campaign data to aadras failed, scheduleNo: {}, shopId: {}, itemSize: {}", monitorHelper.getScheduleNo(), sellerInfoVo.getId(), x.getItemIdList().size(), e);
            }
            if (sendSuccess) {
                log.info("executeAutoRuleTask, send campaign data to aadras success, scheduleNo: {}, shopId: {}, itemSize: {}", monitorHelper.getScheduleNo(), sellerInfoVo.getId(), x.getItemIdList().size());
                itemCount += x.getItemIdList().size();
                taskCount += x.getTaskIdList().size();
            }
            //更新下次执行时间，避免执行阻塞重复发队列
            Lists.partition(x.getTaskIdList(), 500).forEach(t -> advertiseAutoRuleStatusDao.batchAddNextExecuteTime(sellerInfoVo.getPuid(), sellerInfoVo.getId(), t, nextExecuteTime));
        }

        //统计数量
        monitorHelper.addItemAndTaskCount(AutoRuleScheduleTriggerRuleTaskItemType.CAMPAIGN, itemCount, taskCount);
    }


    /**
     * 处理广告组受控对象
     * @param statusList
     * @param sellerInfoVo
     */
    private void processAndSendGroup(List<AutoRuleScheduleTaskDto> statusList, ShopAuthSellerInfoVo sellerInfoVo, String adType) {

        //记录监控
        monitorHelper.addScanCount(AutoRuleScheduleTriggerRuleTaskItemType.AD_GROUP, statusList.size());

        //检查归档
//        checkGroupArchived(AutoRuleItemTypeEnum.AD_GROUP, statusList, sellerInfoVo);

        if (CollectionUtils.isEmpty(statusList)) {
            return;
        }

        //组装和发送数据到aadras
        List<AutoRuleScheduleTriggerTaskMessage> messageList = buildMessage(AutoRuleScheduleTriggerRuleTaskItemType.AD_GROUP, sellerInfoVo, statusList, dynamicRefreshNacosConfiguration.getReportTriggerAutorulePartitionSize(), adType);

        LocalDateTime nextExecuteTime = computeNextExecuteTime(sellerInfoVo.getMarketplaceId(), 60);
        //发送队列
        int itemCount = 0;
        int taskCount = 0;
        for (AutoRuleScheduleTriggerTaskMessage x : messageList) {
            boolean sendSuccess = true;
            try {
                if (AdType.SP.name().equals(adType)) {
                    autoRuleScheduleTaskSpCampaignGroupTargetProducer.send(JSON.toJSONString(x).getBytes(StandardCharsets.UTF_8));
                } else if (AdType.SB.name().equals(adType)) {
                    autoRuleScheduleTaskSbProducer.send(JSON.toJSONString(x).getBytes(StandardCharsets.UTF_8));
                } else if (AdType.SD.name().equals(adType)) {
                    autoRuleScheduleTaskSdProducer.send(JSON.toJSONString(x).getBytes(StandardCharsets.UTF_8));
                }
            } catch (Exception e) {
                sendSuccess = false;
                log.warn("executeAutoRuleTask, send adgroup data to aadras failed, scheduleNo: {}, shopId: {}, itemSize: {}", monitorHelper.getScheduleNo(), sellerInfoVo.getId(), x.getItemIdList().size(), e);
            }
            if (sendSuccess) {
                log.info("executeAutoRuleTask, send adgroup data to aadras success, scheduleNo: {}, shopId: {}, itemSize: {}", monitorHelper.getScheduleNo(), sellerInfoVo.getId(), x.getItemIdList().size());
                itemCount += x.getItemIdList().size();
                taskCount += x.getTaskIdList().size();
            }
            //更新下次执行时间，避免执行阻塞重复发队列
            Lists.partition(x.getTaskIdList(), 500).forEach(t -> advertiseAutoRuleStatusDao.batchAddNextExecuteTime(sellerInfoVo.getPuid(), sellerInfoVo.getId(), t, nextExecuteTime));
        }

        //统计数量
        monitorHelper.addItemAndTaskCount(AutoRuleScheduleTriggerRuleTaskItemType.AD_GROUP, itemCount, taskCount);
    }

    /**
     * 处理投放受控对象
     * @param statusList
     * @param sellerInfoVo
     */
    private void processAndSendTarget(List<AutoRuleScheduleTaskDto> statusList, ShopAuthSellerInfoVo sellerInfoVo, String adType) {
        //需要分成4份数据：单个投放、组受控投放，单个关键词、组受控关键词
        Map<AutoRuleScheduleTriggerRuleTaskItemType, List<AutoRuleScheduleTaskDto>> collectMap = statusList.stream().collect(Collectors.groupingBy(x -> {
            if (ChildrenItemType.CHILDREN_TARGET_GROUP.name().equals(x.getChildrenItemType())) {
                //组
                return AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(x.getTargetType()) ?
                        AutoRuleScheduleTriggerRuleTaskItemType.KEYWORD_GROUP : AutoRuleScheduleTriggerRuleTaskItemType.TARGET_GROUP;
            } else {
                //单个
                return AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(x.getTargetType()) ?
                        AutoRuleScheduleTriggerRuleTaskItemType.KEYWORD : AutoRuleScheduleTriggerRuleTaskItemType.TARGET;
            }
        }));

        collectMap.forEach((k, v) -> {
            switch (k) {
                case KEYWORD:
                case TARGET:
                    processAndSendSingleTarget(k, v, sellerInfoVo, adType);
                    break;
                case KEYWORD_GROUP:
                case TARGET_GROUP:
                    processAndSendGroupTarget(k, v, sellerInfoVo, adType);
                    break;
                default:
                    break;
            }
        });
    }

    /**
     * 处理单个投放(关键词)的受控对象
     * @param taskItemType
     * @param statusList
     * @param sellerInfoVo
     */
    private void processAndSendSingleTarget(AutoRuleScheduleTriggerRuleTaskItemType taskItemType, List<AutoRuleScheduleTaskDto> statusList, ShopAuthSellerInfoVo sellerInfoVo, String adType) {

        //记录监控
        monitorHelper.addScanCount(taskItemType, statusList.size());

        //检查归档
//        checkTargetArchived(taskItemType, statusList, sellerInfoVo);

        if (CollectionUtils.isEmpty(statusList)) {
            return;
        }

        String scheduleNo = monitorHelper.getScheduleNo();

        //组装和发送数据到aadras
        List<AutoRuleScheduleTriggerTaskMessage> messageList = buildMessage(taskItemType, sellerInfoVo, statusList, dynamicRefreshNacosConfiguration.getReportTriggerAutorulePartitionSize(), adType);

        LocalDateTime nextExecuteTime = computeNextExecuteTime(sellerInfoVo.getMarketplaceId(), 60);

        //发送队列
        int itemCount = 0;
        int taskCount = 0;
        if (AutoRuleScheduleTriggerRuleTaskItemType.TARGET == taskItemType) {
            for (AutoRuleScheduleTriggerTaskMessage x : messageList) {
                boolean sendSuccess = true;
                try {
                    if (AdType.SP.name().equals(adType)) {
                        autoRuleScheduleTaskSpCampaignGroupTargetProducer.send(JSON.toJSONString(x).getBytes(StandardCharsets.UTF_8));
                    } else if (AdType.SB.name().equals(adType)) {
                        autoRuleScheduleTaskSbProducer.send(JSON.toJSONString(x).getBytes(StandardCharsets.UTF_8));
                    } else if (AdType.SD.name().equals(adType)) {
                        autoRuleScheduleTaskSdProducer.send(JSON.toJSONString(x).getBytes(StandardCharsets.UTF_8));
                    }
                } catch (Exception e) {
                    sendSuccess = false;
                    log.warn("executeAutoRuleTask, send target data to aadras failed, scheduleNo: {}, shopId: {}, itemSize: {}", scheduleNo, sellerInfoVo.getId(), x.getItemIdList().size(), e);
                }
                if (sendSuccess) {
                    log.info("executeAutoRuleTask, send target data to aadras success, scheduleNo: {}, shopId: {}, itemSize: {}", scheduleNo, sellerInfoVo.getId(), x.getItemIdList().size());
                    itemCount += x.getItemIdList().size();
                    taskCount += x.getTaskIdList().size();
                }
                //更新下次执行时间，避免执行阻塞重复发队列
                Lists.partition(x.getTaskIdList(), 500).forEach(t -> advertiseAutoRuleStatusDao.batchAddNextExecuteTime(sellerInfoVo.getPuid(), sellerInfoVo.getId(), t, nextExecuteTime));
            }
        } else {
            for (AutoRuleScheduleTriggerTaskMessage x : messageList) {
                boolean sendSuccess = true;
                try {
                    if (AdType.SP.name().equals(adType)) {
                        autoRuleScheduleTaskSpKeywordProducer.send(JSON.toJSONString(x).getBytes(StandardCharsets.UTF_8));
                    } else if (AdType.SB.name().equals(adType)) {
                        autoRuleScheduleTaskSbProducer.send(JSON.toJSONString(x).getBytes(StandardCharsets.UTF_8));
                    }
                } catch (Exception e) {
                    sendSuccess = false;
                    log.warn("executeAutoRuleTask, send keyword data to aadras failed, scheduleNo: {}, shopId: {}, itemSize: {}", scheduleNo, sellerInfoVo.getId(), x.getItemIdList().size(), e);
                }
                if (sendSuccess) {
                    log.info("executeAutoRuleTask, send keyword data to aadras success, scheduleNo: {}, shopId: {}, itemSize: {}", scheduleNo, sellerInfoVo.getId(), x.getItemIdList().size());
                    itemCount += x.getItemIdList().size();
                    taskCount += x.getTaskIdList().size();
                }
                //更新下次执行时间，避免执行阻塞重复发队列
                Lists.partition(x.getTaskIdList(), 500).forEach(t -> advertiseAutoRuleStatusDao.batchAddNextExecuteTime(sellerInfoVo.getPuid(), sellerInfoVo.getId(), t, nextExecuteTime));
            }
        }

        //统计数量
        monitorHelper.addItemAndTaskCount(taskItemType, itemCount, taskCount);
    }

    private void processAndSendGroupTarget(AutoRuleScheduleTriggerRuleTaskItemType taskItemType, List<AutoRuleScheduleTaskDto> statusList, ShopAuthSellerInfoVo sellerInfoVo, String adType) {

        //记录监控
        monitorHelper.addScanCount(taskItemType, statusList.size());

        //检查归档
//        checkGroupArchived(AutoRuleItemTypeEnum.TARGET, statusList, sellerInfoVo);

        if (CollectionUtils.isEmpty(statusList)) {
            return;
        }

        String scheduleNo = monitorHelper.getScheduleNo();

        //组装和发送数据到aadras
        int partitionSize = AutoRuleScheduleTriggerRuleTaskItemType.TARGET_GROUP == taskItemType ? dynamicRefreshNacosConfiguration.getReportTriggerAutoruleGroupTargetPartitionSize() : dynamicRefreshNacosConfiguration.getReportTriggerAutoruleGroupKeywordPartitionSize();
        List<AutoRuleScheduleTriggerTaskMessage> messageList = buildMessage(taskItemType, sellerInfoVo, statusList, partitionSize, adType);

        LocalDateTime nextExecuteTime = computeNextExecuteTime(sellerInfoVo.getMarketplaceId(), 60);

        //发送队列
        int itemCount = 0;
        int taskCount = 0;
        if (AutoRuleScheduleTriggerRuleTaskItemType.TARGET_GROUP == taskItemType) {
            for (AutoRuleScheduleTriggerTaskMessage x : messageList) {
                boolean sendSuccess = true;
                try {
                    if (AdType.SP.name().equals(adType)) {
                        autoRuleScheduleTaskSpCampaignGroupTargetProducer.send(JSON.toJSONString(x).getBytes(StandardCharsets.UTF_8));
                    } else if (AdType.SB.name().equals(adType)) {
                        autoRuleScheduleTaskSbProducer.send(JSON.toJSONString(x).getBytes(StandardCharsets.UTF_8));
                    } else if (AdType.SD.name().equals(adType)) {
                        autoRuleScheduleTaskSdProducer.send(JSON.toJSONString(x).getBytes(StandardCharsets.UTF_8));
                    }
                } catch (Exception e) {
                    sendSuccess = false;
                    log.warn("executeAutoRuleTask, send group target data to aadras failed, scheduleNo: {}, shopId: {}, itemSize: {}", scheduleNo, sellerInfoVo.getId(), x.getItemIdList().size(), e);
                }

                if (sendSuccess) {
                    log.info("executeAutoRuleTask, send group target data to aadras success, scheduleNo: {}, shopId: {}, itemSize: {}", scheduleNo, sellerInfoVo.getId(), x.getItemIdList().size());
                    itemCount += x.getItemIdList().size();
                    taskCount += x.getTaskIdList().size();
                }

                //更新下次执行时间，避免执行阻塞重复发队列
                Lists.partition(x.getTaskIdList(), 500).forEach(t -> advertiseAutoRuleStatusDao.batchAddNextExecuteTime(sellerInfoVo.getPuid(), sellerInfoVo.getId(), t, nextExecuteTime));
            }
        } else {
            for (AutoRuleScheduleTriggerTaskMessage x : messageList) {
                boolean sendSuccess = true;
                try {
                    if (AdType.SP.name().equals(adType)) {
                        autoRuleScheduleTaskSpGroupKeywordProducer.send(JSON.toJSONString(x).getBytes(StandardCharsets.UTF_8));
                    } else if (AdType.SB.name().equals(adType)) {
                        autoRuleScheduleTaskSbProducer.send(JSON.toJSONString(x).getBytes(StandardCharsets.UTF_8));
                    }
                } catch (Exception e) {
                    sendSuccess = false;
                    log.warn("executeAutoRuleTask, send group keyword data to aadras failed, scheduleNo: {}, shopId: {}, itemSize: {}", scheduleNo, sellerInfoVo.getId(), x.getItemIdList().size(), e);
                }

                if (sendSuccess) {
                    log.info("executeAutoRuleTask, send group keyword data to aadras success, scheduleNo: {}, shopId: {}, itemSize: {}", scheduleNo, sellerInfoVo.getId(), x.getItemIdList().size());
                    itemCount += x.getItemIdList().size();
                    taskCount += x.getTaskIdList().size();
                }

                //更新下次执行时间，避免执行阻塞重复发队列
                Lists.partition(x.getTaskIdList(), 500).forEach(t -> advertiseAutoRuleStatusDao.batchAddNextExecuteTime(sellerInfoVo.getPuid(), sellerInfoVo.getId(), t, nextExecuteTime));
            }
        }

        //统计数量
        monitorHelper.addItemAndTaskCount(taskItemType, itemCount, taskCount);
    }

    /**
     * 检查活动是否归档并处理
     * @param statusList
     * @param sellerInfoVo
     */
    private void checkCampaignArchived(List<AdvertiseAutoRuleStatus> statusList, ShopAuthSellerInfoVo sellerInfoVo) {

        Set<String> itemIdSet = new HashSet<>();
        statusList.forEach(x -> itemIdSet.add(x.getItemId()));
        List<String> itemIdList = new ArrayList<>(itemIdSet);

        List<String> archivedCampaignIdList = amazonAdCampaignAllDao.queryArchivedByCampaignIdList(sellerInfoVo.getPuid(), sellerInfoVo.getId(), itemIdList);

        if (CollectionUtils.isEmpty(archivedCampaignIdList)) {
            return;
        }

        //移除归档数据
        Set<String> archivedCampaignIdSet = new HashSet<>(archivedCampaignIdList);
        Iterator<AdvertiseAutoRuleStatus> iterator = statusList.iterator();
        while (iterator.hasNext()) {
            if (archivedCampaignIdSet.contains(iterator.next().getItemId())) {
                iterator.remove();
            }
        }
        advertiseAutoRuleStatusDao.batchUpdateArchivedState(sellerInfoVo.getPuid(), sellerInfoVo.getId(), AutoRuleItemTypeEnum.CAMPAIGN.getName(), archivedCampaignIdList);
    }

    /**
     * 构建发送给aadras的消息
     * @param dtoList
     * @return
     */
    private List<AutoRuleScheduleTriggerTaskMessage> buildMessage(AutoRuleScheduleTriggerRuleTaskItemType taskItemType,
                                                                  ShopAuthSellerInfoVo sellerInfoVo,
                                                                  List<AutoRuleScheduleTaskDto> dtoList,
                                                                  int partitionSize,
                                                                  String adType) {
        List<List<AutoRuleScheduleTaskDto>> partition = Lists.partition(dtoList, partitionSize);
        return partition.stream().map(x -> {
            AutoRuleScheduleTriggerTaskMessage message = new AutoRuleScheduleTriggerTaskMessage();
            message.setPuid(sellerInfoVo.getPuid());
            message.setShopId(sellerInfoVo.getId());
            message.setItemType(taskItemType);
            message.setItemIdList(x.stream().map(AutoRuleScheduleTaskDto::getItemId).collect(Collectors.toList()));
            message.setTaskIdList(x.stream().flatMap(t -> t.getTaskIdList().stream()).collect(Collectors.toList()));
            message.setSellerId(sellerInfoVo.getSellerId());
            message.setMarketplaceId(sellerInfoVo.getMarketplaceId());
            //批次信息，需要清理掉hash tag，因为hash tag中含{}和:会导致消费反序列化json字符串时报错
            String scheduleNo = monitorHelper.getScheduleNo();
            String hashTagPrefix;
            if (AdType.SP.name().equals(adType)) {
                if (AutoRuleScheduleTriggerRuleTaskItemType.CAMPAIGN == taskItemType) {
                    hashTagPrefix = AutoRuleMonitorPrefixEnum.CAMPAIGN.getHashTagPrefix();
                } else {
                    hashTagPrefix = AutoRuleMonitorPrefixEnum.GROUP_TARGET.getHashTagPrefix();
                }
            } else if (AdType.SB.name().equals(adType)) {
                hashTagPrefix = AutoRuleMonitorPrefixEnum.SB.getHashTagPrefix();
            } else {
                hashTagPrefix = AutoRuleMonitorPrefixEnum.SD.getHashTagPrefix();
            }
            if (StringUtils.isNotBlank(scheduleNo) && scheduleNo.length() > hashTagPrefix.length()) {
                message.setScheduleNo(scheduleNo.substring(hashTagPrefix.length()));
            }
            return message;
        }).collect(Collectors.toList());
    }

    private LocalDateTime computeNextExecuteTime(String marketplaceId, int minutes) {

        //间隔后的站点时间
        LocalDateTime nowTime = LocalDateTime.now(Marketplace.fromId(marketplaceId).getTimeZone().toZoneId());
        LocalDateTime nextExecuteSiteTime = nowTime.plusMinutes(minutes);

        //转成北京时间
        ZonedDateTime zonedDateTime = nextExecuteSiteTime.atZone(Marketplace.fromId(marketplaceId).getTimeZone().toZoneId());
        ZoneId beijingZone = ZoneId.of("Asia/Shanghai");
        ZonedDateTime beijingDateTime = zonedDateTime.withZoneSameInstant(beijingZone);
        return beijingDateTime.toLocalDateTime();
    }

    /**
     * 全库更新执行间隔
     * @param jdbcTemplate
     */
    private void updateGrayPuidExecuteTimeSpaceByJdbcTemplate(JdbcTemplate jdbcTemplate) {
        jdbcTemplate.update("update t_ad_auto_rule_template set execute_time_space_value=12, execute_time_space_unit='hour', update_time=update_time where (execute_time_space_value is null or execute_time_space_unit is null)");
        //查询数据
        String sql = "select distinct puid, shop_id shopId, marketplace_id marketplaceId from t_ad_auto_rule_status where (execute_time_space_value is null or execute_time_space_unit is null)";
        List<AutoRuleMarketplaceDto> marketplaceDtoList = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(AutoRuleMarketplaceDto.class));
        if (CollectionUtils.isEmpty(marketplaceDtoList)) {
            return;
        }

        Map<String, List<Integer>> map = marketplaceDtoList.stream()
                .filter(x -> ObjectUtils.allNotNull(x.getPuid(), x.getShopId(), x.getMarketplaceId()))
                .collect(Collectors.groupingBy(dto -> dto.getMarketplaceId() + "," + dto.getPuid(),
                        Collectors.mapping(AutoRuleMarketplaceDto::getShopId, Collectors.toList())));
        //计算下一次执行时间并更新
        map.forEach((k, v) -> {
            String[] split = k.split(",");
            String marketplaceId = split[0];
            Integer puid = Integer.parseInt(split[1]);
            advertiseAutoRuleStatusDao.updateDefaultExecuteTimeSpace(puid, v, completeGrayNextExecuteTime(marketplaceId));
        });
    }


    /**
     * 更新指定puid的执行间隔
     * @param puid
     */
    private void updateGrayPuidExecuteTimeSpaceByPuid(Integer puid) {

        //更新模板
        advertiseAutoRuleTemplateDao.updateDefaultExecuteTimeSpace(puid);

        //更新受控对象
        List<AutoRuleMarketplaceDto> marketplaceDtoList = advertiseAutoRuleStatusDao.selectDistinctMarket(puid);

        if (CollectionUtils.isEmpty(marketplaceDtoList)) {
            return;
        }

        Map<String, List<Integer>> map = marketplaceDtoList.stream()
                .filter(x -> ObjectUtils.allNotNull(x.getPuid(), x.getShopId(), x.getMarketplaceId()))
                .collect(Collectors.groupingBy(AutoRuleMarketplaceDto::getMarketplaceId,
                        Collectors.mapping(AutoRuleMarketplaceDto::getShopId, Collectors.toList())));
        //计算下一次执行时间并更新
        map.forEach((k, v) -> advertiseAutoRuleStatusDao.updateDefaultExecuteTimeSpace(puid, v, completeGrayNextExecuteTime(k)));

        log.info("updateGrayPuidExecuteTimeSpaceByPuid update success, puid:{}", puid);
    }

    private static Date completeGrayNextExecuteTime(String marketplaceId) {
        //取得时区枚举
        MarketplaceTimeZoneEnum zoneEnum = MarketplaceTimeZoneEnum.map.get(marketplaceId);
        if (Objects.isNull(zoneEnum)) {
            return new Date();
        }
        //当前北京时间
        LocalDateTime localDateTime = LocalDateTime.now();
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.of("Asia/Shanghai"));
        //转成站点时间
        ZonedDateTime marketDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of(zoneEnum.getZone_id()));

        int hour = marketDateTime.getHour();
        ZonedDateTime nextExecuteZoneDateTime = null;
        if (hour < 11) {
            nextExecuteZoneDateTime = marketDateTime.withHour(11).withMinute(0).withSecond(0);
        }

        if (hour >= 11 && hour < 23) {
            nextExecuteZoneDateTime = marketDateTime.withHour(23).withMinute(0).withSecond(0);
        }

        if (hour >= 23) {
            nextExecuteZoneDateTime = marketDateTime.plusDays(1).withHour(11).withMinute(0).withSecond(0);
        }

        //转成北京时间
        ZonedDateTime beijingDateTime = nextExecuteZoneDateTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
        Date date = Date.from(beijingDateTime.toInstant());
        //随机一下
        int minutes = new Random().nextInt(50);
        Date randomDate = DateUtils.addMinutes(date, minutes);
        log.info("站点id: {}, 当前北京时间: {}, 当前站点时间: {}, 站点下次执行时间: {}, 站点下次执行时间对应北京时间: {}, 随机后时间: {}",
                marketplaceId, localDateTime, marketDateTime, nextExecuteZoneDateTime, beijingDateTime, randomDate);
        return randomDate;
    }


    /**
     * 全库更新执行间隔
     * @param jdbcTemplate
     */
    private void updateGrayPuidExecuteTimeSpacePlusByJdbcTemplate(Map<String, String[]> executeTimeSpaceMap, JdbcTemplate jdbcTemplate) {
        //更新模板
        executeTimeSpaceMap.forEach((k, v) -> {
            String updateSql = "update t_ad_auto_rule_template set execute_time_space_value= ? , execute_time_space_unit= ? where rule_type = ? and (execute_time_space_value is null or execute_time_space_unit is null)";
            List<Object> argList = new ArrayList<>();
            argList.add(v[0]);
            argList.add(v[1]);
            argList.add(k);
            jdbcTemplate.update(updateSql, argList.toArray());
        });

        //查询数据
        String sql = "select distinct puid, shop_id shopId, marketplace_id marketplaceId from t_ad_auto_rule_status where (execute_time_space_value is null or execute_time_space_unit is null)";
        List<AutoRuleMarketplaceDto> marketplaceDtoList = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(AutoRuleMarketplaceDto.class));
        if (CollectionUtils.isEmpty(marketplaceDtoList)) {
            return;
        }

        Map<String, List<Integer>> map = marketplaceDtoList.stream()
                .filter(x -> ObjectUtils.allNotNull(x.getPuid(), x.getShopId(), x.getMarketplaceId()))
                .collect(Collectors.groupingBy(dto -> dto.getMarketplaceId() + "," + dto.getPuid(),
                        Collectors.mapping(AutoRuleMarketplaceDto::getShopId, Collectors.toList())));
        //计算下一次执行时间并更新
        map.forEach((k, v) -> {
            String[] split = k.split(",");
            String marketplaceId = split[0];
            Integer puid = Integer.parseInt(split[1]);
            executeTimeSpaceMap.forEach((ruleType, timeSpace) -> {
                advertiseAutoRuleStatusDao.updateDefaultExecuteTimeSpace(puid, v, ruleType, timeSpace[0], timeSpace[1], completeGrayNextExecuteTime(marketplaceId));
            });
        });
    }


    /**
     * 更新指定puid的执行间隔
     * @param puid
     */
    private void updateGrayPuidExecuteTimeSpacePlusByPuid(Map<String, String[]>  executeTimeSpaceMap, Integer puid) {

        //更新模板
        executeTimeSpaceMap.forEach((ruleType, timeSpace) -> {
            advertiseAutoRuleTemplateDao.updateDefaultExecuteTimeSpace(puid, ruleType, timeSpace[0], timeSpace[1]);
        });


        //更新受控对象
        List<AutoRuleMarketplaceDto> marketplaceDtoList = advertiseAutoRuleStatusDao.selectDistinctMarket(puid);

        if (CollectionUtils.isEmpty(marketplaceDtoList)) {
            return;
        }

        Map<String, List<Integer>> map = marketplaceDtoList.stream()
                .filter(x -> ObjectUtils.allNotNull(x.getPuid(), x.getShopId(), x.getMarketplaceId()))
                .collect(Collectors.groupingBy(AutoRuleMarketplaceDto::getMarketplaceId,
                        Collectors.mapping(AutoRuleMarketplaceDto::getShopId, Collectors.toList())));
        //计算下一次执行时间并更新
        map.forEach((k, v) -> {
            executeTimeSpaceMap.forEach((ruleType, timeSpace) -> {
                advertiseAutoRuleStatusDao.updateDefaultExecuteTimeSpace(puid, v, ruleType, timeSpace[0], timeSpace[1], completeGrayNextExecuteTime(k));
            });
        });

        log.info("updateGrayPuidExecuteTimeSpacePlusByPuid update success, puid:{}", puid);
    }

    private List<JdbcTemplateVo> getJdbcTemplateList(int index, int total) {
        List<JdbcTemplateVo> result = new ArrayList<>();
        for (int i = 0; i < jdbcTemplateVoList.size(); i++) {
            if (i % total == index) {
                result.add(jdbcTemplateVoList.get(i));
            }
        }
        return result;
    }

    private void retryAutoRule(JdbcTemplateVo vo) {
        JdbcTemplate jdbcTemplate = vo.getJdbcTemplate();
        String sql = "select puid, shop_id shopId, id templateId from t_ad_auto_rule_template where status='DISABLED'";
        List<DisabledTemplateDto> templateDtoList = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(DisabledTemplateDto.class));
        if (CollectionUtils.isEmpty(templateDtoList)) {
            return;
        }

        List<DisabledTemplateDto> collect = templateDtoList.stream().filter(x -> Objects.equals(jdbcTemplate, advertiseAutoRuleStatusDao.getJdbcTemplate(x.getPuid())))
                .collect(Collectors.toList());

        Map<Integer, List<DisabledTemplateDto>> map = collect.stream().collect(Collectors.groupingBy(DisabledTemplateDto::getPuid));

        map.forEach((k, v) -> advertiseAutoRuleTemplateService.retryDisableTemplate(k, v));
    }

    private void retryStrategy(JdbcTemplateVo vo) {
        JdbcTemplate jdbcTemplate = vo.getJdbcTemplate();
        String sql = "select id,puid, shop_id, item_type,children_item_type from t_advertise_strategy_template where status='DISABLED'";
        List<AdvertiseStrategyTemplate> templateDtoList = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(AdvertiseStrategyTemplate.class));
        if (CollectionUtils.isEmpty(templateDtoList)) {
            return;
        }

        List<AdvertiseStrategyTemplate> collect = templateDtoList.stream().filter(x -> Objects.equals(jdbcTemplate, advertiseAutoRuleStatusDao.getJdbcTemplate(x.getPuid())))
                .collect(Collectors.toList());

        Map<Integer, List<AdvertiseStrategyTemplate>> map = collect.stream().collect(Collectors.groupingBy(AdvertiseStrategyTemplate::getPuid));

        map.forEach((k, v) -> strategyTemplateService.retryDisableTemplate(k, v));
    }

    private void retryTopBudget(JdbcTemplateVo x) {
        JdbcTemplate jdbcTemplate = x.getJdbcTemplate();
        String sql = "select * from t_advertise_strategy_top_budget_template where status='DISABLED'";
        List<AdvertiseStrategyTopBudgetTemplate> templateList = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(AdvertiseStrategyTopBudgetTemplate.class));
        if (CollectionUtils.isEmpty(templateList)) {
            return;
        }

        for (AdvertiseStrategyTopBudgetTemplate template : templateList) {
            if (!Objects.equals(jdbcTemplate, advertiseAutoRuleStatusDao.getJdbcTemplate(template.getPuid()))) {
                continue;
            }
            //数据到任务调度服务删除数据
            profilesApi.removeTopBudgetTask(template.getPuid(), template.getShopId(), template.getTaskId(), false);
        }
    }

}
