package com.meiyunji.sponsored.cron.service.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.cron.service.IDeleteInvalidShopDataService;
import com.meiyunji.sponsored.cron.service.helper.InvalidShopDataFunctionHelper;
import com.meiyunji.sponsored.cron.service.helper.WxNotificationHelper;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDeleteDao;
import com.meiyunji.sponsored.service.account.po.ShopAuthDelete;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleExecuteRecordDao;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDao;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDeleteDao;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleTemplateDao;
import com.meiyunji.sponsored.service.autoRuleTask.dao.AutoRuleTaskDao;
import com.meiyunji.sponsored.service.autoRuleTask.dao.AutoRuleTaskRecordDao;
import com.meiyunji.sponsored.service.batchCreate.dao.*;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.constants.TaskExecutionRecordTaskType;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.TaskExecutionRecord;
import com.meiyunji.sponsored.service.function.ThFunction;
import com.meiyunji.sponsored.service.strategy.dao.*;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyRealTimeBidDao;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyTaskDao;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyTaskRecordDao;
import com.meiyunji.sponsored.service.util.ShardTableSuffixUtil;
import com.meiyunji.sponsored.service.wordFrequency.dao.IWordRootKeywordSbDao;
import com.meiyunji.sponsored.service.wordFrequency.dao.IWordRootKeywordSpDao;
import com.meiyunji.sponsored.service.wordFrequency.dao.IWordRootTargetingSpDao;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-01-08  19:07
 */
@Service
@Slf4j
public class DeleteInvalidShopDataServiceImpl implements IDeleteInvalidShopDataService {
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthDeleteDao shopAuthDeleteDao;

    @Autowired
    private ResourceLoader resourceLoader;

    @Autowired
    private InvalidShopDataFunctionHelper invalidShopDataFunctionHelper;


    //所有的共用数据库
    @Resource(name = "shardingJdbcMap")
    private Map<String, JdbcTemplate> jdbcTemplateMap;
    @Resource(name = "vipJdbcTemplateMap")
    private Map<String, JdbcTemplate> vipJdbcTemplateMap;

    //环境
    @Value(value = "${spring.profiles.active}")
    private String active;

    @Autowired
    private ITaskExecutionRecordDao taskExecutionRecordDao;

    @Resource(name = "jdbcTemplateFeedDorisDb")
    private JdbcTemplate jdbcTemplateFeedDorisDb;

    //分表逻辑表名
    public static List<String> shardTableList = Lists.newArrayList(
            "t_amazon_ad_campaign_all_report", "t_amazon_ad_group_report", "t_amazon_ad_product_report",
            "t_amazon_ad_keyword_report", "t_amazon_ad_sb_keyword_report", "t_cpc_targeting_report",
            "t_cpc_query_keyword_report", "t_cpc_query_targeting_report", "t_amazon_word_root_keyword_sb",
            "t_amazon_word_root_keyword_sp", "t_amazon_word_root_targeting_sp", "t_amazon_ad_keyword",
            "t_amazon_ad_ne_keyword", "t_amazon_ad_ne_targeting", "t_amazon_ad_targeting"

    );

    //单表表名
    public static List<String> singleTableList = Lists.newArrayList(
            "t_amazon_ad_portfolio", "t_amazon_ad_campaign_all", "t_amazon_ad_group", "t_amazon_ad_group_sb",
            "t_amazon_ad_group_sd", "t_amazon_ad_product", "t_amazon_ad_product_sd",
            "t_amazon_ad_keyword_sb", "t_amazon_ad_targeting_sb", "t_amazon_ad_targeting_sd",
            "t_advertise_strategy_schedule", "t_advertise_strategy_status", "t_advertise_strategy_template",
            "t_advertise_strategy_status_delete", "t_advertise_strategy_top_budget_template", "t_advertise_strategy_top_budget_schedule",
            "t_ad_auto_rule_execute_record", "t_ad_auto_rule_status", "t_ad_auto_rule_template", "t_ad_auto_rule_status_delete",
            "t_amazon_ad_operation_log", "t_amazon_ad_batch_baseinfo", "t_amazon_ad_batch_campaign", "t_amazon_ad_batch_group",
            "t_amazon_ad_batch_keyword", "t_amazon_ad_batch_nekeyword", "t_amazon_ad_batch_netargeting",
            "t_amazon_ad_batch_product", "t_amazon_ad_batch_targeting", "t_amazon_ad_batch_task",
            "t_cpc_sb_query_keyword_report", "t_amazon_ad_sd_group_report", "t_amazon_ad_sb_group_report",
            "t_amazon_ad_sd_product_report", "t_amazon_ad_sb_targeting_report", "t_amazon_ad_sd_targeting_report"
    );

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    private static final Integer SELECT_DELETE_SHOP_LIMIT = 1000;

    private static final String DELETE_INVALID_SHOP_DATA_LOG = "delete invalid shop data:";

    private static final String STATISTICS_INVALID_SHOP_DATA_LOG = "statistics invalid shop data:";

    private static final String DELETE_MIGRATE_OLD_DATA_LOG = "delete migrate old data:";

    private static final String DELETE_DORIS_SQL ="delete from %s where puid = ? and shop_id = ? ";

    @Override
    public void deleteInvalidShopData(Integer puid, Integer shopId, Integer totalLimit) {
        StopWatch sw = new StopWatch(DELETE_INVALID_SHOP_DATA_LOG);
        XxlJobLogger.log(DELETE_INVALID_SHOP_DATA_LOG + "start");
        if (dynamicRefreshConfiguration.getInvalidShopDeleteLimit() == null || dynamicRefreshConfiguration.getInvalidShopDeleteWaitTime() == null) {
            XxlJobLogger.log(DELETE_INVALID_SHOP_DATA_LOG + "limit or wait time is null");
            log.info(DELETE_INVALID_SHOP_DATA_LOG + "limit or wait time is null");
            return;
        }
        if (totalLimit != null && totalLimit.equals(0)) {
            XxlJobLogger.log(DELETE_INVALID_SHOP_DATA_LOG + "limit equals zero");
            log.info(DELETE_INVALID_SHOP_DATA_LOG + "limit equals zero");
            return;
        }
        if (!dynamicRefreshConfiguration.getInvalidShopDeleteEnable()) {
            XxlJobLogger.log(DELETE_INVALID_SHOP_DATA_LOG + "delete enable is false");
            log.info(DELETE_INVALID_SHOP_DATA_LOG + "delete enable is false");
            return;
        }

        Integer count = shopAuthDeleteDao.getCount();
        XxlJobLogger.log(String.format(DELETE_INVALID_SHOP_DATA_LOG + "shop total num:%d", count));
        log.info(String.format(DELETE_INVALID_SHOP_DATA_LOG + "shop total num:%d", count));

        int start = 0;
        List<ShopAuthDelete> shopAuthDeleteList = new ArrayList<>();
        while (true) {
            if (!dynamicRefreshConfiguration.getInvalidShopDeleteEnable()) {
                XxlJobLogger.log(String.format(DELETE_INVALID_SHOP_DATA_LOG + "delete enable is false"));
                log.info(String.format(DELETE_INVALID_SHOP_DATA_LOG + "delete enable is false"));
                return;
            }
            //单用户验证
            if (puid != null && shopId != null) {
                ShopAuthDelete shopAuthDelete = shopAuthDeleteDao.getById(shopId);
                if (!shopAuthDelete.getPuid().equals(puid)) {
                    XxlJobLogger.log(String.format(DELETE_INVALID_SHOP_DATA_LOG + "puid and shopId mismatch"));
                    log.info(String.format(DELETE_INVALID_SHOP_DATA_LOG + "puid and shopId mismatch"));
                    break;
                }
                shopAuthDeleteList.add(shopAuthDelete);
            } else {
                shopAuthDeleteList = shopAuthDeleteDao.getAllShopByLimit(start, SELECT_DELETE_SHOP_LIMIT);
            }
            int size = shopAuthDeleteList.size();
            //删除数据
            this.deleteShopDataHandler(shopAuthDeleteList, totalLimit, sw, start);
            if (size < SELECT_DELETE_SHOP_LIMIT) {
                break;
            }
            start += size;
        }
        XxlJobLogger.log(DELETE_INVALID_SHOP_DATA_LOG + "end, total time: " + sw.getTotalTimeSeconds());
        log.info(DELETE_INVALID_SHOP_DATA_LOG + "end, total time: " + sw.getTotalTimeSeconds());
        XxlJobLogger.log(sw.prettyPrint());
    }

    @Override
    public void deleteMigrateOldData(Integer puid, String beanId, Integer totalLimit) {
        XxlJobLogger.log(DELETE_MIGRATE_OLD_DATA_LOG + "start");
        log.info(DELETE_MIGRATE_OLD_DATA_LOG + "start");
        if (!dynamicRefreshConfiguration.getMigrateShopDeleteEnable()) {
            XxlJobLogger.log(DELETE_MIGRATE_OLD_DATA_LOG + "delete enable is false");
            log.info(DELETE_MIGRATE_OLD_DATA_LOG + "delete enable is false");
            return;
        }
        if (puid == null || beanId == null) {
            XxlJobLogger.log(DELETE_MIGRATE_OLD_DATA_LOG + "puid or beanId is null");
            log.info(DELETE_MIGRATE_OLD_DATA_LOG + "puid or beanId is null");
            return;
        }
        if (totalLimit != null && totalLimit.equals(0)) {
            XxlJobLogger.log(DELETE_MIGRATE_OLD_DATA_LOG + "limit equals zero");
            log.info(DELETE_MIGRATE_OLD_DATA_LOG + "limit equals zero");
            return;
        }
        if (jdbcTemplateMap.isEmpty()) {
            XxlJobLogger.log(DELETE_MIGRATE_OLD_DATA_LOG + "jdbc template map is empty");
            log.info(DELETE_MIGRATE_OLD_DATA_LOG + "jdbc template map is empty");
            return;
        }
        JdbcTemplate jdbcTemplate = null;
        if (jdbcTemplateMap.containsKey(beanId)) {
            jdbcTemplate = jdbcTemplateMap.get(beanId);
        }
        if (jdbcTemplate == null) {
            XxlJobLogger.log(DELETE_MIGRATE_OLD_DATA_LOG + "jdbc template is null");
            log.info(DELETE_MIGRATE_OLD_DATA_LOG + "jdbc template is null");
            return;
        }

        //分表后缀集合
        List<String> suffixList = ShardTableSuffixUtil.getShardSuffixList(active);

        //获取所有的表名
        List<String> tableNameList = new ArrayList<>();
        shardTableList.forEach(x -> suffixList.forEach(y -> tableNameList.add(x + y)));
        tableNameList.addAll(singleTableList);

        String sql = "delete from %s where puid = %d limit %d";
        int count;

        for (String tableName: tableNameList) {
            count = 0;
            while (true) {
                if (!dynamicRefreshConfiguration.getMigrateShopDeleteEnable()) {
                    XxlJobLogger.log(String.format(DELETE_MIGRATE_OLD_DATA_LOG + "delete enable is false"));
                    log.info(String.format(DELETE_MIGRATE_OLD_DATA_LOG + "delete enable is false"));
                    return;
                }
                try {
                    count = jdbcTemplate.update(String.format(sql, tableName, puid, dynamicRefreshConfiguration.getMigrateShopDeleteLimit()));
                    XxlJobLogger.log(String.format(DELETE_MIGRATE_OLD_DATA_LOG + "puid %d delete %s count %d", puid, tableName, count));
                    log.info(String.format(DELETE_MIGRATE_OLD_DATA_LOG + "puid %d delete %s count %d", puid, tableName, count));
                    if (totalLimit != null) {
                        totalLimit = totalLimit - count;
                        if (totalLimit <= 0) {
                            return;
                        }
                    }
                    Thread.sleep(dynamicRefreshConfiguration.getMigrateShopDeleteWaitTime());
                } catch (Exception e) {
                    XxlJobLogger.log(String.format(DELETE_MIGRATE_OLD_DATA_LOG + "sql exception, puid: %d, tableName: %s, errMsg: %s", puid, tableName, e.getMessage()));
                    log.error(String.format(DELETE_MIGRATE_OLD_DATA_LOG + "sql exception, puid: %d, tableName: %s", puid, tableName), e);
                }
                if (count < dynamicRefreshConfiguration.getMigrateShopDeleteLimit()) {
                    break;
                }
            }
        }
    }

    @Override
    public void statisticsInvalidShopData() {
        XxlJobLogger.log(STATISTICS_INVALID_SHOP_DATA_LOG + "start");

        Integer count = shopAuthDeleteDao.getCount();
        XxlJobLogger.log(String.format(STATISTICS_INVALID_SHOP_DATA_LOG + "shop total num:%d", count));

        int start = 0;
        int index;
        ShopAuthDelete shop;
        List<ShopAuthDelete> shopAuthDeleteList;
        Map<String, Long> countMap = new LinkedHashMap<>();
        while (true) {
            shopAuthDeleteList = shopAuthDeleteDao.getAllShopByLimit(start, SELECT_DELETE_SHOP_LIMIT);
            int size = shopAuthDeleteList.size();
            if (CollectionUtils.isNotEmpty(shopAuthDeleteList)) {
                List<Integer> deleteShopIdList = shopAuthDeleteList.stream().map(ShopAuthDelete::getId).collect(Collectors.toList());
                //获取还存在的店铺，过滤掉已存在的店铺
                List<Integer> existShopIdList = shopAuthDao.IdListByShopIds(deleteShopIdList);
                if (CollectionUtils.isNotEmpty(existShopIdList)) {
                    XxlJobLogger.log(String.format(STATISTICS_INVALID_SHOP_DATA_LOG + "shop auth table exist ids:%s", StringUtil.joinInt(existShopIdList)));
                    shopAuthDeleteList = shopAuthDeleteList.stream().filter(e -> !existShopIdList.contains(e.getId())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(shopAuthDeleteList)) {
                        XxlJobLogger.log(STATISTICS_INVALID_SHOP_DATA_LOG + "all shop exist");
                        return;
                    }
                }
            }
            //统计数据
            for (int i = 0; i < shopAuthDeleteList.size(); i++) {
                shop = shopAuthDeleteList.get(i);
                //打印日志
                index = start + i + 1;
                XxlJobLogger.log(String.format(STATISTICS_INVALID_SHOP_DATA_LOG + "execute sql, index: %d, puid: %d, shopId: %d", index, shop.getPuid(), shop.getId()));
                //统计数据
                this.statisticsInvalidShopDataHandler(shop.getPuid(), shop.getId(), countMap);
            }
            if (size < SELECT_DELETE_SHOP_LIMIT) {
                break;
            }
            start += size;
        }

        Long sumCount = 0L;
        for (Map.Entry<String, Long> entry : countMap.entrySet()) {
            sumCount = sumCount + entry.getValue();
            XxlJobLogger.log(String.format(STATISTICS_INVALID_SHOP_DATA_LOG + "table:%s, count:%d", entry.getKey(), entry.getValue()));
        }
        XxlJobLogger.log(String.format(STATISTICS_INVALID_SHOP_DATA_LOG + "sumCount:%d", sumCount));
        WxNotificationHelper.sendContent(String.format(STATISTICS_INVALID_SHOP_DATA_LOG + "sumCount:%d", sumCount));
    }

    private void statisticsInvalidShopDataHandler(Integer puid, Integer shopId, Map<String, Long> countMap) {
        List<Pair<String, BiFunction<Integer, Integer, Integer>>> functionList = invalidShopDataFunctionHelper.getAllStatisticsFunction();
        for (Pair<String, BiFunction<Integer, Integer, Integer>> functionPair : functionList) {
            countMap.put(functionPair.getLeft(), countMap.getOrDefault(functionPair.getLeft(), 0L) + functionPair.getRight().apply(puid, shopId));
        }
    }

    private void deleteShopDataHandler(List<ShopAuthDelete> shopAuthDeleteList, Integer totalLimit, StopWatch sw, Integer start) {
        if (CollectionUtils.isEmpty(shopAuthDeleteList)) {
            XxlJobLogger.log(DELETE_INVALID_SHOP_DATA_LOG + "shopAuthDeleteList is empty");
            log.info(DELETE_INVALID_SHOP_DATA_LOG + "shopAuthDeleteList is empty");
            return;
        }
        List<Integer> deleteShopIdList = shopAuthDeleteList.stream().map(ShopAuthDelete::getId).collect(Collectors.toList());
        //获取还存在的店铺，过滤掉已存在的店铺
        List<Integer> existShopIdList = shopAuthDao.IdListByShopIds(deleteShopIdList);
        if (CollectionUtils.isNotEmpty(existShopIdList)) {
            XxlJobLogger.log(String.format(DELETE_INVALID_SHOP_DATA_LOG + "shop auth table exist ids:%s", StringUtil.joinInt(existShopIdList)));
            log.info(String.format(DELETE_INVALID_SHOP_DATA_LOG + "shop auth table exist ids:%s", StringUtil.joinInt(existShopIdList)));
            shopAuthDeleteList = shopAuthDeleteList.stream().filter(e -> !existShopIdList.contains(e.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(shopAuthDeleteList)) {
                XxlJobLogger.log(DELETE_INVALID_SHOP_DATA_LOG + "all shop exist");
                log.info(DELETE_INVALID_SHOP_DATA_LOG + "all shop exist");
                return;
            }
        }
        //过滤掉已经跑过的店铺
        if (dynamicRefreshConfiguration.getInvalidShopDeleteFilterEnable()) {
            List<Integer> succShopIdList = taskExecutionRecordDao.getByTaskType(TaskExecutionRecordTaskType.DELETE_INVALID_SHOP_DATA.getType())
                    .stream().map(TaskExecutionRecord::getShopId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(succShopIdList)) {
                shopAuthDeleteList = shopAuthDeleteList.stream().filter(e -> !succShopIdList.contains(e.getId())).collect(Collectors.toList());
            }
        }

        XxlJobLogger.log(String.format(DELETE_INVALID_SHOP_DATA_LOG + "execute sql, shop size: %d", shopAuthDeleteList.size()));
        log.info(String.format(DELETE_INVALID_SHOP_DATA_LOG + "execute sql, shop size: %d", shopAuthDeleteList.size()));
        int index;

        for (int i = 0; i < shopAuthDeleteList.size(); i++) {
            ShopAuthDelete shop = shopAuthDeleteList.get(i);
            index = start + i + 1;
            XxlJobLogger.log(String.format(DELETE_INVALID_SHOP_DATA_LOG + "execute sql, index: %d, puid: %d, shopId: %d", index, shop.getPuid(), shop.getId()));
            log.info(String.format(DELETE_INVALID_SHOP_DATA_LOG + "execute sql, index: %d, puid: %d, shopId: %d", index, shop.getPuid(), shop.getId()));
            sw.start("puid:" + shop.getPuid() + ",shopId:" + shop.getId());
            try {
                int count;
                while (true) {
                    count = 0;
                    if (!dynamicRefreshConfiguration.getInvalidShopDeleteEnable()) {
                        XxlJobLogger.log(String.format(DELETE_INVALID_SHOP_DATA_LOG + "delete enable is false"));
                        log.info(String.format(DELETE_INVALID_SHOP_DATA_LOG + "delete enable is false"));
                        return;
                    }

                    //获取所有mysql删除函数
                    List<ThFunction<Integer, Integer, Integer, Integer>> functionList = invalidShopDataFunctionHelper.getAllDeleteFunction4Mysql();
                    //执行mysql删除
                    for (ThFunction<Integer, Integer, Integer, Integer> function : functionList) {
                        count += function.apply(shop.getPuid(), shop.getId(), dynamicRefreshConfiguration.getInvalidShopDeleteLimit());
                    }

                    //执行doris删除
                    List<String> dorisDeleteTableNameList = invalidShopDataFunctionHelper.getAllDorisDeleteTableNameList();
                    jdbcTemplateFeedDorisDb.update("set delete_without_partition = true");
                    dorisDeleteTableNameList.forEach(tableName -> {
                        jdbcTemplateFeedDorisDb.update(String.format(DELETE_DORIS_SQL, tableName), shop.getPuid(), shop.getId());
                    });

                    XxlJobLogger.log(String.format(DELETE_INVALID_SHOP_DATA_LOG + "delete puid: %d, shopId: %d, count: %d", shop.getPuid(), shop.getId(), count));
                    log.error(String.format(DELETE_INVALID_SHOP_DATA_LOG + "delete puid: %d, shopId: %d, count: %d", shop.getPuid(), shop.getId(), count));
                    if (totalLimit != null) {
                        totalLimit = totalLimit - count;
                        if (totalLimit <= 0) {
                            return;
                        }
                    }
                    Thread.sleep(dynamicRefreshConfiguration.getInvalidShopDeleteWaitTime());
                    //无数据删除，跳出循环
                    if (count == 0) {
                        //记录已跑完的店铺
                        taskExecutionRecordDao.insertOrUpdateTaskExecutionRecordByTaskType(new TaskExecutionRecord(shop.getPuid(), shop.getId(), TaskExecutionRecordTaskType.DELETE_INVALID_SHOP_DATA.getType()));
                        break;
                    }
                }
            } catch (Exception e) {
                XxlJobLogger.log(String.format(DELETE_INVALID_SHOP_DATA_LOG + "exception, puid: %d, shopId: %d, errMsg: %s", shop.getPuid(), shop.getId(), e.getMessage()));
                log.error(String.format(DELETE_INVALID_SHOP_DATA_LOG + "exception, puid: %d, shopId: %d", shop.getPuid(), shop.getId()), e);
            }
            sw.stop();
        }
    }


    private void executeSqlFile(JdbcTemplate jdbcTemplate, String filePath) {
        StringBuilder sqlScript = new StringBuilder();
        org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:" + filePath);
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(resource.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                sqlScript.append(line).append("\n");
            }
            String[] sqlStatements = sqlScript.toString().split(";");
            for (String sql : sqlStatements) {
                if (!sql.trim().isEmpty()) {
                    jdbcTemplate.execute(sql);
                }
            }
        } catch (Exception e) {
            log.error("execute sql error", e);
        }
    }


    /**
     * 执行定价状态修复的SQL脚本
     * 此方法旨在修复可能存在的定价状态问题，通过执行特定的SQL脚本来更新数据库中的相关记录
     */
    @Override
    public void exePricingStatusFix() {
        // 遍历所有JdbcTemplate实例，执行定价状态修复的SQL脚本
        for (JdbcTemplate  jdbcTemplate: jdbcTemplateMap.values()) {
            executeSqlFile(jdbcTemplate, "pricingStatusFix.sql");
        }
        // 遍历所有VIP相关的JdbcTemplate实例，执行相同的SQL脚本进行状态修复
        for (JdbcTemplate  jdbcTemplate: vipJdbcTemplateMap.values()) {
            executeSqlFile(jdbcTemplate, "pricingStatusFix.sql");
        }
    }
}
