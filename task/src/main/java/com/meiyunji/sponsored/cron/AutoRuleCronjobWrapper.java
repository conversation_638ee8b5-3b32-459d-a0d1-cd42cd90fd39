package com.meiyunji.sponsored.cron;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.cron.dto.UpdateSearchQueryToAadrasParam;
import com.meiyunji.sponsored.cron.service.IAutoRuleScheduleTaskService;
import com.meiyunji.sponsored.cron.service.helper.AutoRuleScheduleMonitorHelper;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleExecuteTimeSpaceUnitEnum;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleMonitorPrefixEnum;
import com.meiyunji.sponsored.service.enums.AdTypeEnum;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleTypeEnum;
import com.meiyunji.sponsored.service.enums.AutoRuleItemTypeEnum;
import com.meiyunji.sponsored.service.strategy.enums.AdType;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.util.ShardingUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;


/**
 * 自动化规则执行定时任务类
 *
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2024-06-17  10:43
 */
@Component
@Slf4j
public class AutoRuleCronjobWrapper {

    @Autowired
    private IAutoRuleScheduleTaskService autoRuleTaskService;

    @Autowired
    private AutoRuleScheduleMonitorHelper monitorHelper;

    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    /**
     * 自动化规则报告数据是否拉取完成，历史数据导入
     *
     * @param params
     * @return
     */
    @XxlJob(value = "reportCompleteDataImport")
    public ReturnT<String> reportCompleteDataImport(String params) {
        log.info("reportCompleteDataImport start");
        try {
            autoRuleTaskService.reportCompleteDataImport();
            log.info("reportCompleteDataImport end");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("reportCompleteDataImport error: ", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 查询自动化规则报告数据是否完成
     *
     * @param params
     * @return
     */
    @XxlJob(value = "checkReportComplete")
    public ReturnT<String> checkReportComplete(String params) {
        log.info("queryReportComplete start");
        try {
            autoRuleTaskService.checkReportComplete();
            log.info("queryReportComplete end");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("queryReportComplete error: ", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 执行自动化规则定时任务
     *
     * @param params
     * @return
     */
    @XxlJob(value = "executeAutoRuleCampaignTask")
    public ReturnT<String> executeAutoRuleCampaignTask(String params) {
        log.info("executeAutoRuleCampaignTask start");
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        if (shardingVO.getIndex() == 0) {
            Map<String, String> objectMap = monitorHelper.getLastMonitorInfo(AutoRuleMonitorPrefixEnum.CAMPAIGN);
            if (MapUtils.isNotEmpty(objectMap)) {
                log.info("last executeAutoRuleCampaignTask info: {}", JSON.toJSONString(objectMap));
                monitorHelper.sendWarningMessage(objectMap);
            }
        }
        try {
            List<String> itemIdList = new ArrayList<>();
            if (StringUtils.isNotBlank(params)) {
                JSONArray array = JSONObject.parseArray(params);
                if (CollectionUtils.isNotEmpty(array)) {
                    itemIdList = array.toJavaList(String.class);
                }
            }
            StopWatch sw = new StopWatch();
            sw.start();
            monitorHelper.initScheduleNo(AutoRuleMonitorPrefixEnum.CAMPAIGN);
            log.info("executeAutoRuleCampaignTask start, scheduleNo: {}", monitorHelper.getScheduleNo());
            autoRuleTaskService.executeAutoRuleTask(shardingVO.getIndex(), shardingVO.getTotal(), Arrays.asList(AutoRuleItemTypeEnum.CAMPAIGN), itemIdList, AdType.SP.name());
            monitorHelper.setScanEndTime();
            sw.stop();
            log.info("executeAutoRuleCampaignTask end, cost: {} seconds, scheduleNo: {}, monitor: {}",
                    sw.getTotalTimeSeconds(),
                    monitorHelper.getScheduleNo(),
                    monitorHelper.getCurMonitorInfo());
            monitorHelper.cleanScheduleNo();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("executeAutoRuleCampaignTask error: ", e);
            monitorHelper.cleanScheduleNo();
            return ReturnT.FAIL;
        }
    }


    /**
     * 执行自动化规则定时任务
     *
     * @param params
     * @return
     */
    @XxlJob(value = "executeAutoRuleGroupAndTargetTask")
    public ReturnT<String> executeAutoRuleGroupAndTargetTask(String params) {
        log.info("executeAutoRuleGroupAndTargetTask start");
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        if (shardingVO.getIndex() == 0) {
            Map<String, String> objectMap = monitorHelper.getLastMonitorInfo(AutoRuleMonitorPrefixEnum.GROUP_TARGET);
            if (MapUtils.isNotEmpty(objectMap)) {
                log.info("last executeAutoRuleGroupAndTargetTask info: {}", JSON.toJSONString(objectMap));
                monitorHelper.sendWarningMessage(objectMap);
            }
        }
        try {
            List<String> itemIdList = new ArrayList<>();
            if (StringUtils.isNotBlank(params)) {
                JSONArray array = JSONObject.parseArray(params);
                if (CollectionUtils.isNotEmpty(array)) {
                    itemIdList = array.toJavaList(String.class);
                }
            }
            StopWatch sw = new StopWatch();
            sw.start();
            monitorHelper.initScheduleNo(AutoRuleMonitorPrefixEnum.GROUP_TARGET);
            log.info("executeAutoRuleGroupAndTargetTask start, scheduleNo: {}", monitorHelper.getScheduleNo());
            autoRuleTaskService.executeAutoRuleTask(shardingVO.getIndex(), shardingVO.getTotal(), Arrays.asList(AutoRuleItemTypeEnum.AD_GROUP, AutoRuleItemTypeEnum.TARGET), itemIdList, AdType.SP.name());
            monitorHelper.setScanEndTime();
            sw.stop();
            log.info("executeAutoRuleGroupAndTargetTask end, cost: {} seconds, scheduleNo: {}, monitor: {}",
                    sw.getTotalTimeSeconds(),
                    monitorHelper.getScheduleNo(),
                    monitorHelper.getCurMonitorInfo());
            monitorHelper.cleanScheduleNo();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("executeAutoRuleGroupAndTargetTask error: ", e);
            monitorHelper.cleanScheduleNo();
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "executeSbAutoRuleTask")
    public ReturnT<String> executeSbAutoRuleTask(String params) {
        log.info("executeSbAutoRuleTask start");
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        if (shardingVO.getIndex() == 0) {
            Map<String, String> objectMap = monitorHelper.getLastMonitorInfo(AutoRuleMonitorPrefixEnum.SB);
            if (MapUtils.isNotEmpty(objectMap)) {
                log.info("last executeSbAutoRuleTask info: {}", JSON.toJSONString(objectMap));
                if (dynamicRefreshNacosConfiguration.getReportTriggerAutoRuleWarning()) {
                    monitorHelper.sendWarningMessage(objectMap);
                }
            }
        }
        try {
            List<String> itemIdList = new ArrayList<>();
            if (StringUtils.isNotBlank(params)) {
                JSONArray array = JSONObject.parseArray(params);
                if (CollectionUtils.isNotEmpty(array)) {
                    itemIdList = array.toJavaList(String.class);
                }
            }
            StopWatch sw = new StopWatch();
            sw.start();
            monitorHelper.initScheduleNo(AutoRuleMonitorPrefixEnum.SB);
            log.info("executeSbAutoRuleTask start, scheduleNo: {}", monitorHelper.getScheduleNo());
            autoRuleTaskService.executeAutoRuleTask(shardingVO.getIndex(), shardingVO.getTotal(),
                    Arrays.asList(AutoRuleItemTypeEnum.CAMPAIGN, AutoRuleItemTypeEnum.AD_GROUP, AutoRuleItemTypeEnum.TARGET), itemIdList, AdType.SB.name());
            monitorHelper.setScanEndTime();
            sw.stop();
            log.info("executeSbAutoRuleTask end, cost: {} seconds, scheduleNo: {}, monitor: {}",
                    sw.getTotalTimeSeconds(),
                    monitorHelper.getScheduleNo(),
                    monitorHelper.getCurMonitorInfo());
            monitorHelper.cleanScheduleNo();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("executeSbAutoRuleTask error: ", e);
            monitorHelper.cleanScheduleNo();
            return ReturnT.FAIL;
        }
    }

    @XxlJob(value = "executeSdAutoRuleTask")
    public ReturnT<String> executeSdAutoRuleTask(String params) {
        log.info("executeSdAutoRuleTask start");
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        if (shardingVO.getIndex() == 0) {
            Map<String, String> objectMap = monitorHelper.getLastMonitorInfo(AutoRuleMonitorPrefixEnum.SD);
            if (MapUtils.isNotEmpty(objectMap)) {
                log.info("last executeSdAutoRuleTask info: {}", JSON.toJSONString(objectMap));
                if (dynamicRefreshNacosConfiguration.getReportTriggerAutoRuleWarning()) {
                    monitorHelper.sendWarningMessage(objectMap);
                }
            }
        }
        try {
            List<String> itemIdList = new ArrayList<>();
            if (StringUtils.isNotBlank(params)) {
                JSONArray array = JSONObject.parseArray(params);
                if (CollectionUtils.isNotEmpty(array)) {
                    itemIdList = array.toJavaList(String.class);
                }
            }
            StopWatch sw = new StopWatch();
            sw.start();
            monitorHelper.initScheduleNo(AutoRuleMonitorPrefixEnum.SD);
            log.info("executeSdAutoRuleTask start, scheduleNo: {}", monitorHelper.getScheduleNo());
            autoRuleTaskService.executeAutoRuleTask(shardingVO.getIndex(), shardingVO.getTotal(),
                    Arrays.asList(AutoRuleItemTypeEnum.CAMPAIGN, AutoRuleItemTypeEnum.AD_GROUP, AutoRuleItemTypeEnum.TARGET), itemIdList, AdType.SD.name());
            monitorHelper.setScanEndTime();
            sw.stop();
            log.info("executeSdAutoRuleTask end, cost: {} seconds, scheduleNo: {}, monitor: {}",
                    sw.getTotalTimeSeconds(),
                    monitorHelper.getScheduleNo(),
                    monitorHelper.getCurMonitorInfo());
            monitorHelper.cleanScheduleNo();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("executeSdAutoRuleTask error: ", e);
            monitorHelper.cleanScheduleNo();
            return ReturnT.FAIL;
        }
    }

    /**
     * 处理搜索词预设实际更新失败的受控对象更新到aadras
     *
     * @param params
     * @return
     */
    @XxlJob(value = "updateSearchQueryToAadras")
    public ReturnT<String> updateSearchQueryToAadras(String params) {
        log.info("updateSearchQueryToAadras start");
        try {
            UpdateSearchQueryToAadrasParam param = null;
            if (StringUtils.isNotBlank(params)) {
                JSONObject json = JSONObject.parseObject(params);
                Integer puid = json.getInteger("puid");
                JSONArray taskIdArr = json.getJSONArray("taskIdList");
                List<Long> taskIdList = taskIdArr.toJavaList(Long.class);
                if (Objects.isNull(puid) || CollectionUtils.isEmpty(taskIdList)) {
                    log.error("updateSearchQueryToAadras param error");
                    return ReturnT.FAIL;
                }
                param = new UpdateSearchQueryToAadrasParam();
                param.setPuid(puid);
                param.setTaskIdList(taskIdList);
            }

            autoRuleTaskService.updateSearchQueryToAadras(param);
            log.info("updateSearchQueryToAadras end");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("updateSearchQueryToAadras error: ", e);
            return ReturnT.FAIL;
        }
    }


    /**
     * 定时触发自动化规则处理灰度puid默认执行时间间隔数据
     *
     * @param params
     * @return
     */
    @XxlJob(value = "updateGrayPuidExecuteTimeSpace")
    public ReturnT<String> updateGrayPuidExecuteTimeSpace(String params) {
        log.info("updateGrayPuidExecuteTimeSpace start");
        try {

            if (StringUtils.isBlank(params)) {
                log.error("updateGrayPuidExecuteTimeSpace, no params");
                return ReturnT.FAIL;
            }

            if ("ALL".equals(params)) {
                autoRuleTaskService.updateGrayPuidExecuteTimeSpace(null);
                log.info("updateGrayPuidExecuteTimeSpace by all end");
                return ReturnT.SUCCESS;
            }

            JSONArray array = JSONObject.parseArray(params);
            if (CollectionUtils.isEmpty(array)) {
                log.error("updateGrayPuidExecuteTimeSpace, empty array");
                return ReturnT.FAIL;
            }

            List<Integer> puidList = new ArrayList<>();

            array.forEach(x -> puidList.add(Integer.parseInt(x.toString())));

            if (CollectionUtils.isEmpty(puidList)) {
                log.error("updateGrayPuidExecuteTimeSpace, empty list");
                return ReturnT.FAIL;
            }

            autoRuleTaskService.updateGrayPuidExecuteTimeSpace(puidList);
            log.info("updateGrayPuidExecuteTimeSpace by puidList end");
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            log.error("updateGrayPuidExecuteTimeSpace error: ", e);
            return ReturnT.FAIL;
        }
    }



    /**
     * 定时触发自动化规则处理灰度puid默认执行时间间隔数据，可设置各规则的默认执行时间和单位
     *
     * @param params
     * @return
     */
    @XxlJob(value = "updateGrayPuidExecuteTimeSpacePlus")
    public ReturnT<String> updateGrayPuidExecuteTimeSpacePlus(String params) {
        log.info("updateGrayPuidExecuteTimeSpacePlus start");
        try {

            if (StringUtils.isBlank(params)) {
                log.error("updateGrayPuidExecuteTimeSpacePlus, no params");
                return ReturnT.FAIL;
            }

            JSONObject jsonObject = JSONObject.parseObject(params);

            String keywordAcosLowerPrice = jsonObject.getString(AutoRuleTypeEnum.keywordAcosLowerPrice.getValue());
            String searchQueryAutoUpdateBidding = jsonObject.getString(AutoRuleTypeEnum.searchQueryAutoUpdateBidding.getValue());
            String keywordAcosRaisePrice = jsonObject.getString(AutoRuleTypeEnum.keywordAcosRaisePrice.getValue());
            String campaignAcosRaiseBudget = jsonObject.getString(AutoRuleTypeEnum.campaignAcosRaiseBudget.getValue());
            String campaignOverBudgetRaiseBudget = jsonObject.getString(AutoRuleTypeEnum.campaignOverBudgetRaiseBudget.getValue());
            String puid = jsonObject.getString("puid");

            if (StringUtils.isAnyBlank(keywordAcosLowerPrice, searchQueryAutoUpdateBidding, keywordAcosRaisePrice, campaignAcosRaiseBudget, campaignOverBudgetRaiseBudget, puid)) {
                log.error("updateGrayPuidExecuteTimeSpacePlus, params error");
                return ReturnT.FAIL;
            }

            //处理ExecuteTimeSpace数据
            Map<String, String[]>  executeTimeSpaceMap = new HashMap<>(10);
            executeTimeSpaceMap.put(AutoRuleTypeEnum.keywordAcosLowerPrice.getValue(), keywordAcosLowerPrice.split("-"));
            executeTimeSpaceMap.put(AutoRuleTypeEnum.searchQueryAutoUpdateBidding.getValue(), searchQueryAutoUpdateBidding.split("-"));
            executeTimeSpaceMap.put(AutoRuleTypeEnum.keywordAcosRaisePrice.getValue(), keywordAcosRaisePrice.split("-"));
            executeTimeSpaceMap.put(AutoRuleTypeEnum.campaignAcosRaiseBudget.getValue(), campaignAcosRaiseBudget.split("-"));
            executeTimeSpaceMap.put(AutoRuleTypeEnum.campaignOverBudgetRaiseBudget.getValue(), campaignOverBudgetRaiseBudget.split("-"));

            //数据校验
            for (Map.Entry<String, String[]> entry : executeTimeSpaceMap.entrySet()) {
                if (entry.getValue() == null || entry.getValue().length != 2) {
                    log.error("updateGrayPuidExecuteTimeSpacePlus, params error");
                    return ReturnT.FAIL;
                }

                if (Integer.parseInt(entry.getValue()[0]) <= 0 || Integer.parseInt(entry.getValue()[0]) > 60) {
                    log.error("updateGrayPuidExecuteTimeSpacePlus, params error");
                    return ReturnT.FAIL;
                }

                if (!AutoRuleExecuteTimeSpaceUnitEnum.set.contains(entry.getValue()[1])) {
                    log.error("updateGrayPuidExecuteTimeSpacePlus, params error");
                    return ReturnT.FAIL;
                }
            }


            if ("ALL".equals(puid)) {
                autoRuleTaskService.updateGrayPuidExecuteTimeSpacePlus(executeTimeSpaceMap, null);
                log.info("updateGrayPuidExecuteTimeSpace by all end");
                return ReturnT.SUCCESS;
            }

            String[] puidArr = puid.split(",");
            if (puidArr == null || puidArr.length == 0) {
                log.error("updateGrayPuidExecuteTimeSpacePlus, empty puid array");
                return ReturnT.FAIL;
            }

            List<Integer> puidList = new ArrayList<>();

            for (String x : puidArr) {
                puidList.add(Integer.parseInt(x.toString()));
            }

            if (CollectionUtils.isEmpty(puidList)) {
                log.error("updateGrayPuidExecuteTimeSpacePlus, empty puid list");
                return ReturnT.FAIL;
            }

            autoRuleTaskService.updateGrayPuidExecuteTimeSpacePlus(executeTimeSpaceMap, puidList);
            log.info("updateGrayPuidExecuteTimeSpacePlus by puidList end");
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            log.error("updateGrayPuidExecuteTimeSpacePlus error: ", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 执行自动化规则定时任务-更新状态信息
     *
     * @param params
     * @return
     */
    @XxlJob(value = "updateAutoRuleCampaignAndGroupAndTargetStateTask")
    public ReturnT<String> updateAutoRuleCampaignAndGroupAndTargetStateTask(String params) {
        log.info("updateAutoRuleCampaignAndGroupAndTargetStateTask start");
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        try {
            List<Integer> puidList = new ArrayList<>();
            if (StringUtils.isNotBlank(params)) {
                JSONArray array = JSONObject.parseArray(params);
                if (CollectionUtils.isNotEmpty(array)) {
                    puidList = array.toJavaList(Integer.class);
                }
            }
            StopWatch sw = new StopWatch();
            sw.start();
            autoRuleTaskService.updateAutoRuleState(shardingVO.getIndex(), shardingVO.getTotal(), Arrays.asList(AutoRuleItemTypeEnum.CAMPAIGN, AutoRuleItemTypeEnum.AD_GROUP, AutoRuleItemTypeEnum.TARGET), puidList);
            sw.stop();
            log.info("updateAutoRuleCampaignAndGroupAndTargetStateTask success,use time:{}", sw.getTotalTimeSeconds());
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("updateAutoRuleCampaignAndGroupAndTargetStateTask error: ", e);
            return ReturnT.FAIL;
        }
    }


    /**
     * -99用户停止自动化规则和分时策略
     * 1、-99用户处理，每小时处理一次
     * 2、缓存重建：删除店铺、禁用等缓存定时兜底，删除的模板等处理：这个每4小时处理一次，复用同一个定时
     * @param params
     * @return
     */
    @XxlJob(value = "expirePuidStopStrategyAndRetryJob")
    public ReturnT<String> expirePuidStopStrategyAndRetryJob(String params) {
        log.info("expirePuidStopStrategyAndRetryJob start");
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        List<Integer> puids = new ArrayList<>();
        if (StringUtils.isNotBlank(params)) {
            puids = StringUtil.splitInt(params, ",");
        }
        try {

            //-99用户处理
            autoRuleTaskService.expirePuidStopStrategy(shardingVO.getIndex(), shardingVO.getTotal(), puids);
            log.info("expirePuidStopStrategyAndRetryJob process -99 puid finish");

            //重建一下缓存和兜底处理
            autoRuleTaskService.rebuildAndRetry(shardingVO.getIndex(), shardingVO.getTotal());

            log.info("expirePuidStopStrategyAndRetryJob success");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("expirePuidStopStrategyAndRetryJob error: ", e);
            return ReturnT.FAIL;
        }
    }

}