package com.meiyunji.sponsored.cron.service.impl;

import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.cron.service.IInitPerspectiveHotAsinService;
import com.meiyunji.sponsored.service.account.dao.ISlaveScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveShopAuthDao;
import com.meiyunji.sponsored.service.hotdata.HotDataTypeEnum;
import com.meiyunji.sponsored.service.hotdata.dao.IAmazonShopHotDataDao;
import com.meiyunji.sponsored.service.hotdata.po.AmazonShopHotData;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AsinOrderNumDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.ShopByPuidDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.IAsinInfoQueryService;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.InitAsinInfoReqVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2024-01-19  13:41
 */

@Service
@Slf4j
public class InitPerspectiveHotAsinServiceImpl implements IInitPerspectiveHotAsinService {

    @Autowired
    private ISlaveScVcShopAuthDao slaveShopAuthDao;

    @Autowired
    private IAsinInfoQueryService asinInfoQueryService;

    @Autowired
    private IAmazonShopHotDataDao amazonShopHotDataDao;

    @Override
    public void initHotAsin(int index, int total, List<Integer> specPuidList, Integer partition) {
        StopWatch sw = new StopWatch();
        sw.start();

        ThreadPoolExecutor executor = new ThreadPoolExecutor(6, 6, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

        if (CollectionUtils.isNotEmpty(specPuidList)) {
            specPuidList.forEach(curPuid -> {
                if (curPuid % total == index) {
                    executor.execute(() -> initByAllShopForPuid(curPuid, partition));
                }
            });
        } else {
            //查询所有puid
            int puidLimit = 1000;
            int puidStartIndex = 0;

            while (true) {
                List<Integer> puidList = slaveShopAuthDao.getAllValidAdShopPuidByLimit(puidStartIndex, puidLimit);
                //处理一个puid
                puidList.forEach(curPuid -> {
                    if (curPuid % total == index) {
                        executor.execute(() -> initByAllShopForPuid(curPuid, partition));
                    }
                });
                if (CollectionUtils.isEmpty(puidList) || puidList.size() < puidLimit) {
                    break;
                }
                puidStartIndex += puidLimit;
            }

            ThreadPoolUtil.waitingFinish(executor);
        }
        executor.shutdown();
        sw.stop();
        log.info("商品透视分析asin初始化定时计算总耗时: {}", sw.getTotalTimeSeconds());
    }

    /**
     * 处理当前puid下的初始化数据
     * @param puid
     * @param partition
     */
    private void initByAllShopForPuid(Integer puid, Integer partition) {

        try {
            //查询所有非未授权的店铺
            List<ShopByPuidDto> dtoList = slaveShopAuthDao.getAllValidAdShopByPuid(puid);

            if (CollectionUtils.isEmpty(dtoList)) {
                return;
            }

            LocalDateTime now = LocalDateTime.now();
            InitAsinInfoReqVo reqVo = new InitAsinInfoReqVo();
            reqVo.setPuid(puid);

            List<Integer> shopIdList = new ArrayList<>(dtoList.size());
            Set<String> marketplaceIdSet = new HashSet<>();
            dtoList.forEach(x -> {
                shopIdList.add(x.getShopId());
                marketplaceIdSet.add(x.getMarketplaceId());
            });
            reqVo.setShopIdList(shopIdList);
            reqVo.setMarketplaceIdList(new ArrayList<>(marketplaceIdSet));

            List<AsinOrderNumDto> shopInitAsinList = asinInfoQueryService.getOrderNumAsinForShopList(reqVo, now, null, partition);

            if (CollectionUtils.isNotEmpty(shopInitAsinList)) {
                List<AmazonShopHotData> hotDataList = new ArrayList<>(shopInitAsinList.size());
                shopInitAsinList.forEach(x -> {
                    AmazonShopHotData hotData = new AmazonShopHotData();
                    hotData.setPuid(puid);
                    hotData.setMarketplaceId(x.getMarketplaceId());
                    hotData.setShopId(x.getShopId());
                    hotData.setDataType(HotDataTypeEnum.PERSPECTIVE_INIT_ASIN.getType());
                    hotData.setOrderNum(x.getAdOrderNum());
                    hotData.setDataValue(x.getAsin());
                    hotDataList.add(hotData);
                });
                amazonShopHotDataDao.insertOrUpdate(hotDataList);
            }
        } catch (Exception e) {
            log.error("商品透视分析asin初始化定时任务计算异常, puid {}", puid, e);
        }
    }

}
