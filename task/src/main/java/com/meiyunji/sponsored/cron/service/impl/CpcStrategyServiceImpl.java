package com.meiyunji.sponsored.cron.service.impl;

import com.google.api.client.util.Lists;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.cron.service.ICpcStrategyService;
import com.meiyunji.sponsored.cron.service.helper.CpcStrategyHelper;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.IndexStrategyConfig;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.kafka.service.AdvertiseStrategyProcessTaskMessageService;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyAdGroupDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusDeleteDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTemplateDao;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyAdGroup;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatus;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.strategy.service.helper.AdvertiseStrategyGroupTargetBidHelper;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyTaskDao;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyTaskRecordDao;
import com.meiyunji.sponsored.service.strategyTask.dao.IAmazonAdStrategyTaskSupportDao;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyTask;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyTaskRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-20  09:37
 */
@Service
@Slf4j
public class CpcStrategyServiceImpl implements ICpcStrategyService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private AdvertiseStrategyTaskDao advertiseStrategyTaskDao;
    @Autowired
    private AdvertiseStrategyTaskRecordDao advertiseStrategyTaskRecordDao;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private IAmazonAdStrategyTaskSupportDao amazonAdStrategyTaskSupportDao;
    @Autowired
    private AdvertiseStrategyAdGroupDao advertiseStrategyAdGroupDao;
    @Autowired
    private IndexStrategyConfig indexStrategyConfig;
    @Autowired
    private AdvertiseStrategyStatusDao advertiseStrategyStatusDao;
    @Autowired
    private AdvertiseStrategyStatusDeleteDao advertiseStrategyStatusDeleteDao;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private AdvertiseStrategyTemplateDao advertiseStrategyTemplateDao;
    @Autowired
    private CpcStrategyHelper cpcStrategyHelper;
    @Autowired
    private AdvertiseStrategyProcessTaskMessageService advertiseStrategyProcessTaskMessageService;
    @Autowired
    private ISlaveScVcShopAuthDao slaveShopAuthDao;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;

    @Override
    public void cpcStrategyRetry(Integer shardIndex, Integer totalShard, Integer puid, Integer shopId, Long templateId, Long taskId) {
        List<Integer> puids = amazonAdStrategyTaskSupportDao.getPuidsByUpdateTime(puid, LocalDate.now().minusDays(3));
        //改成按for puid
        if (CollectionUtils.isNotEmpty(puids)) {
            List<ShopAuth> shopAuthList = slaveShopAuthDao.getAdAuthShopByShopIdList(puids);
            if (CollectionUtils.isEmpty(shopAuthList)) {
                return;
            }
            ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getCpcStrategyRetryPoolExecutor();
            shopAuthList = shopAuthList.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(shopAuthList)) {
                return;
            }
            CountDownLatch countDownLatch = new CountDownLatch(puids.size());
            for (ShopAuth shopAuth : shopAuthList) {
                threadExecutor.execute(() -> {
                    try {
                        List<AdvertiseStrategyTask> taskList = advertiseStrategyTaskDao.queryListByTemplateIdList(shopAuth.getPuid(), shopAuth.getId(), templateId, taskId);
                        if (CollectionUtils.isEmpty(taskList)) {
                            return;
                        }
                        for (AdvertiseStrategyTask task : taskList) {
                            String lockKey = String.format(RedisConstant.SPONSORED_AD_STRATEGY_PROCESS_TASK_KEY, task.getOldTemplateId());
                            RLock lock = redissonClient.getLock(lockKey);
                            boolean b = lock.tryLock();
                            if (!b) {
                                log.info("当前模板任务执行中, puid:{}, shopId:{}, templateId:{},", shopAuth.getPuid(), shopAuth.getId(), task.getOldTemplateId());
                                return;
                            }
                            try {
                                Long id = 0L;
                                while (true) {
                                    //指针分页取法
                                    List<AdvertiseStrategyTaskRecord> recordList = advertiseStrategyTaskRecordDao.getListByTaskId(task.getPuid(), task.getShopId(), id, task.getId());
                                    if (CollectionUtils.isEmpty(recordList)) {
                                        break;
                                    }
                                    id = recordList.get(recordList.size() - 1).getId();
                                    switch (task.getTaskAction()) {
                                        case 0:
                                            advertiseStrategyProcessTaskMessageService.updateTask(task, recordList, true);
                                            break;
                                        case 1:
                                            advertiseStrategyProcessTaskMessageService.updateTask(task, recordList, true);
                                            break;
                                        case 2:
                                            advertiseStrategyProcessTaskMessageService.deleteTask(task, recordList, true);
                                            break;
                                        case 3:
                                            advertiseStrategyProcessTaskMessageService.deleteTask(task, recordList, true);
                                            break;
                                        case 4:
                                            advertiseStrategyProcessTaskMessageService.transferTask(task, recordList, true);
                                            break;
                                        case 5:
                                            advertiseStrategyProcessTaskMessageService.transferTask(task, recordList, true);
                                            break;
                                        case 6:
                                            advertiseStrategyProcessTaskMessageService.updateTask(task, recordList, true);
                                            break;
                                        case 7:
                                            advertiseStrategyProcessTaskMessageService.updateTask(task, recordList, true);
                                            break;
                                        case 8:
                                            advertiseStrategyProcessTaskMessageService.updateTask(task, recordList, true);
                                            break;
                                        case 9:
                                            advertiseStrategyProcessTaskMessageService.updateTask(task, recordList, true);
                                            break;
                                        case 10:
                                            advertiseStrategyProcessTaskMessageService.updateTask(task, recordList, true);
                                            break;
                                        case 11:
                                            advertiseStrategyProcessTaskMessageService.queryTargetRealTimeBid(task, recordList, true);
                                            break;
                                        case 12:
                                            advertiseStrategyProcessTaskMessageService.updateState(task, recordList, true, "DISABLED");
                                            break;
                                        case 13:
                                            advertiseStrategyProcessTaskMessageService.updateState(task, recordList, true, "ENABLED");
                                            break;
                                        case 14:
                                            advertiseStrategyProcessTaskMessageService.updateState(task, recordList, true, "DISABLED");
                                            break;
                                        case 15:
                                            advertiseStrategyProcessTaskMessageService.updateState(task, recordList, true, "ENABLED");
                                            break;
                                        default:
                                            break;
                                    }
                                }
                                int count = advertiseStrategyTaskRecordDao.queryCount(task.getPuid(), task.getShopId(), task.getId(), null);
                                if (count <= 0) {
                                    task.setState(1);
                                } else {
                                    int errorCount = advertiseStrategyTaskRecordDao.queryCount(task.getPuid(), task.getShopId(), task.getId(), -1);
                                    int sucessCount = advertiseStrategyTaskRecordDao.queryCount(task.getPuid(), task.getShopId(), task.getId(), 1);
                                    if (errorCount > 0) {
                                        task.setState(-1);
                                    } else if (sucessCount >= task.getCount()) {
                                        task.setState(1);
                                    }
                                }
                                advertiseStrategyTaskDao.updateStateByPrimaryKey(task.getPuid(), task);
                            } catch (Exception e) {
                                log.error("分时策略重试任务处理失败，puid:{}, shopId:{} taskId:{} 错误信息：", shopAuth.getPuid(), shopAuth.getId(), task.getId(),e);
                            } finally {
                                lock.unlock();
                            }
                        }
                    } catch (Exception e) {
                        log.error("多线程处理失败，puid:{}, shopId:{} 错误信息：", shopAuth.getPuid(), shopAuth.getId(),e);
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    public void deleteStrategyTask(Integer shardIndex, Integer totalShard, Integer puid, Integer shopId, Long templateId, Long taskId) {
        List<Integer> puids = amazonAdStrategyTaskSupportDao.getPuidsByUpdateTime(puid, LocalDate.now().minusDays(7));
        if (CollectionUtils.isNotEmpty(puids)) {
            List<ShopAuth> shopAuthList = shopAuthDao.getAdAuthShopByShopIdList(puids);
            if (CollectionUtils.isEmpty(shopAuthList)) {
                return;
            }
            ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getCpcStrategyRetryPoolExecutor();
            shopAuthList = shopAuthList.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(shopAuthList)) {
                return;
            }
            CountDownLatch countDownLatch = new CountDownLatch(shopAuthList.size());
            for (ShopAuth shopAuth : shopAuthList) {
                threadExecutor.execute(() -> {
                    try {
//                        List<AdvertiseStrategyAdGroup> advertiseStrategyAdGroupList = advertiseStrategyAdGroupDao.getListByAdGroupId();
                    } catch (Exception e) {
                        log.error("多线程处理失败，puid:{}, shopId:{} 错误信息：", shopAuth.getPuid(), shopAuth.getId(),e);
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                log.error("deleteStrategyTask:", e);
            }
        }
    }

    @Override
    public void cpcAdGroupStrategyRetry(Integer shardIndex, Integer totalShard, String puid, String shopId, String adGroupId) {
        List<Integer> puids = Lists.newArrayList();
        if (StringUtils.isNotBlank(puid)) {
            puids = StringUtil.stringToIntegerList(puid, ",");
        }
        List<Integer> shopIdList = Lists.newArrayList();
        if (StringUtils.isNotBlank(shopId)) {
            shopIdList = StringUtil.stringToIntegerList(shopId, ",");
        }
        List<ShopAuth> shopAuthList = shopAuthDao.getAdAuthShopByShopId(puids,shopIdList);
        if (CollectionUtils.isEmpty(shopAuthList)) {
            return;
        }
        List<String> adGroupIdList = Lists.newArrayList();
        if (StringUtils.isNotBlank(adGroupId)) {
            adGroupIdList = StringUtil.stringToList(adGroupId, ",");
        }
        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getCpcStrategyRetryPoolExecutor();
        shopAuthList = shopAuthList.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
        CountDownLatch countDownLatch = new CountDownLatch(shopAuthList.size());
        for (ShopAuth shopAuth : shopAuthList) {
            List<String> finalAdGroupIdList = adGroupIdList;
            threadExecutor.execute(() -> {
                try {
                    List<AdvertiseStrategyAdGroup> advertiseStrategyAdGroupList = advertiseStrategyAdGroupDao.getAdGroupIds(shopAuth.getPuid(), shopAuth.getId(), finalAdGroupIdList);
                    if (CollectionUtils.isNotEmpty(advertiseStrategyAdGroupList)) {
                        for (AdvertiseStrategyAdGroup advertiseStrategyAdGroup : advertiseStrategyAdGroupList) {
                            List<String> itemIds = Lists.newArrayList();
                            List<String> itemIdList = advertiseStrategyStatusDao.getItemIdList(shopAuth.getPuid(), shopAuth.getId(), advertiseStrategyAdGroup.getAdGroupId());
                            List<String> deleteItemIdList = advertiseStrategyStatusDeleteDao.getItemIdList(shopAuth.getPuid(), shopAuth.getId(), advertiseStrategyAdGroup.getAdGroupId());
                            if (CollectionUtils.isNotEmpty(itemIdList)) {
                                itemIds.addAll(itemIdList);
                            }
                            if (CollectionUtils.isNotEmpty(deleteItemIdList)) {
                                itemIds.addAll(deleteItemIdList);
                            }
                            List<AmazonAdKeyword> amazonAdKeywordList = amazonAdKeywordDaoRoutingService.listByGroupIdAndItemIdList(shopAuth.getPuid(), shopAuth.getId(), advertiseStrategyAdGroup.getAdGroupId(), itemIds);
                            List<AmazonAdTargeting> amazonAdTargetingList = amazonAdTargetDaoRoutingService.listByGroupIdAndItemIdList(shopAuth.getPuid(), shopAuth.getId(), advertiseStrategyAdGroup.getAdGroupId(), itemIds);
                            List<AmazonSbAdKeyword> amazonSbAdKeywordList = amazonSbAdKeywordDao.listByGroupIdAndItemIdList(shopAuth.getPuid(), shopAuth.getId(), advertiseStrategyAdGroup.getAdGroupId(), itemIds);
                            List<AmazonSbAdTargeting> amazonSbAdTargetingList = amazonSbAdTargetingDao.listByGroupIdAndItemIdList(shopAuth.getPuid(), shopAuth.getId(), advertiseStrategyAdGroup.getAdGroupId(), itemIds);
                            List<AmazonSdAdTargeting> amazonSdAdTargetingList = amazonSdAdTargetingDao.listByGroupIdAndItemIdList(shopAuth.getPuid(), shopAuth.getId(), advertiseStrategyAdGroup.getAdGroupId(), itemIds);
                            if (CollectionUtils.isNotEmpty(amazonAdKeywordList)) {
                                cpcStrategyHelper.spKeywordSubmitStrategy(advertiseStrategyAdGroup, amazonAdKeywordList);
                            }
                            if (CollectionUtils.isNotEmpty(amazonAdTargetingList)) {
                                cpcStrategyHelper.spTargetSubmitStrategy(advertiseStrategyAdGroup, amazonAdTargetingList);
                            }
                            if (CollectionUtils.isNotEmpty(amazonSbAdKeywordList)) {
                                cpcStrategyHelper.sbKeywordSubmitStrategy(advertiseStrategyAdGroup, amazonSbAdKeywordList);
                            }
                            if (CollectionUtils.isNotEmpty(amazonSbAdTargetingList)) {
                                cpcStrategyHelper.sbTargetSubmitStrategy(advertiseStrategyAdGroup, amazonSbAdTargetingList);
                            }
                            if (CollectionUtils.isNotEmpty(amazonSdAdTargetingList)) {
                                cpcStrategyHelper.sdTargetSubmitStrategy(advertiseStrategyAdGroup, amazonSdAdTargetingList);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("多线程处理失败，puid:{}, shopId:{} 错误信息：", shopAuth.getPuid(), shopAuth.getId(),e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error("deleteStrategyTask:", e);
        }
    }

    public void processTask(List<Long> templateIdList, List<Long> taskIdList) {

    }
}
