package com.meiyunji.sponsored.cron.service.impl;

import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.cron.service.ICpcAutoRuleService;
import com.meiyunji.sponsored.cron.service.helper.CpcStrategyHelper;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.autoRuleTask.dao.AutoRuleTaskDao;
import com.meiyunji.sponsored.service.autoRuleTask.dao.AutoRuleTaskRecordDao;
import com.meiyunji.sponsored.service.autoRuleTask.po.AutoRuleTask;
import com.meiyunji.sponsored.service.autoRuleTask.po.AutoRuleTaskRecord;
import com.meiyunji.sponsored.service.config.IndexStrategyConfig;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.kafka.service.AdvertiseStrategyProcessTaskMessageService;
import com.meiyunji.sponsored.service.kafka.service.AutoRuleProcessTaskMessageService;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyAdGroupDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusDeleteDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTemplateDao;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyTaskDao;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyTaskRecordDao;
import com.meiyunji.sponsored.service.strategyTask.dao.IAmazonAdStrategyTaskSupportDao;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyTask;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyTaskRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-04-16  11:13
 */
@Service
@Slf4j
public class CpcAutoRuleServiceImpl implements ICpcAutoRuleService {
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private AutoRuleTaskDao autoRuleTaskDao;
    @Autowired
    private AutoRuleTaskRecordDao autoRuleTaskRecordDao;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private IAmazonAdStrategyTaskSupportDao amazonAdStrategyTaskSupportDao;
    @Autowired
    private AdvertiseStrategyAdGroupDao advertiseStrategyAdGroupDao;
    @Autowired
    private IndexStrategyConfig indexStrategyConfig;
    @Autowired
    private AdvertiseStrategyStatusDao advertiseStrategyStatusDao;
    @Autowired
    private AdvertiseStrategyStatusDeleteDao advertiseStrategyStatusDeleteDao;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private AdvertiseStrategyTemplateDao advertiseStrategyTemplateDao;
    @Autowired
    private CpcStrategyHelper cpcStrategyHelper;
    @Autowired
    private AutoRuleProcessTaskMessageService autoRuleProcessTaskMessageService;
    @Autowired
    private ISlaveScVcShopAuthDao slaveShopAuthDao;

    @Override
    public void cpcAutoRuleRetry(Integer shardIndex, Integer totalShard, Integer puid, Integer shopId, Long templateId, Long taskId) {
        List<Integer> puids = amazonAdStrategyTaskSupportDao.getPuidsByUpdateTime(puid, LocalDate.now().minusDays(3));
        //改成按for puid
        if (CollectionUtils.isNotEmpty(puids)) {
            List<ShopAuth> shopAuthList = slaveShopAuthDao.getAdAuthShopByShopIdList(puids);
            if (CollectionUtils.isEmpty(shopAuthList)) {
                return;
            }
            ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getCpcStrategyRetryPoolExecutor();
            shopAuthList = shopAuthList.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(shopAuthList)) {
                return;
            }
            CountDownLatch countDownLatch = new CountDownLatch(puids.size());
            for (ShopAuth shopAuth : shopAuthList) {
                threadExecutor.execute(() -> {
                    try {
                        List<AutoRuleTask> taskList = autoRuleTaskDao.queryListByTemplateIdList(shopAuth.getPuid(), shopAuth.getId(), templateId, taskId);
                        if (CollectionUtils.isEmpty(taskList)) {
                            return;
                        }
                        for (AutoRuleTask task : taskList) {
                            String lockKey = String.format(RedisConstant.SPONSORED_AUTO_RULE_PROCESS_TASK_KEY, task.getOldTemplateId());
                            RLock lock = redissonClient.getLock(lockKey);
                            boolean b = lock.tryLock();
                            if (!b) {
                                log.info("任务执行中, puid:{}, shopId:{}, taskId:{}, templateId:{},", task.getPuid(), task.getShopId(), task.getId(), task.getOldTemplateId());
                                return;
                            }
                            try {
                                Long id = 0L;
                                while (true) {
                                    //指针分页取法
                                    List<AutoRuleTaskRecord> recordList = autoRuleTaskRecordDao.getListByTaskId(task.getPuid(), task.getShopId(), id, task.getId());
                                    if (CollectionUtils.isEmpty(recordList)) {
                                        break;
                                    }
                                    id = recordList.get(recordList.size() - 1).getId();
                                    switch (task.getTaskAction()) {
                                        case 0:
                                            autoRuleProcessTaskMessageService.updateTask(task, recordList, true);
                                            break;
                                        case 1:
                                            autoRuleProcessTaskMessageService.updateTask(task, recordList, true);
                                            break;
                                        case 2:
                                            autoRuleProcessTaskMessageService.deleteTask(task, recordList, true);
                                            break;
                                        case 3:
                                            autoRuleProcessTaskMessageService.deleteTask(task, recordList, true);
                                            break;
                                        case 4:

                                            break;
                                        case 5:

                                            break;
                                        case 6:
                                            autoRuleProcessTaskMessageService.updateTask(task, recordList, true);
                                            break;
                                        case 7:
                                            autoRuleProcessTaskMessageService.updateAutoRuleOriginalValue(task, recordList, true);
                                            break;
                                        case 8:
                                            autoRuleProcessTaskMessageService.updateTask(task, recordList, true);
                                            break;
                                        case 9:
                                            autoRuleProcessTaskMessageService.updateAutoRuleOriginalValue(task, recordList, true);
                                            break;
                                        case 10:
                                            autoRuleProcessTaskMessageService.updateAutoRuleOriginalValue(task, recordList, true);
                                            break;
                                        case 11:

                                            break;
                                        case 12:
                                            autoRuleProcessTaskMessageService.updateState(task, recordList, true, "DISABLED");
                                            break;
                                        case 13:
                                            autoRuleProcessTaskMessageService.updateState(task, recordList, true, "ENABLED");
                                            break;
                                        case 14:
                                            autoRuleProcessTaskMessageService.updateState(task, recordList, true, "DISABLED");
                                            break;
                                        case 15:
                                            autoRuleProcessTaskMessageService.updateState(task, recordList, true, "ENABLED");
                                            break;
                                        default:
                                            break;
                                    }
                                }
                                int count = autoRuleTaskRecordDao.queryCount(task.getPuid(), task.getShopId(), task.getId(), null);
                                if (count <= 0) {
                                    task.setState(1);
                                } else {
                                    int errorCount = autoRuleTaskRecordDao.queryCount(task.getPuid(), task.getShopId(), task.getId(), -1);
                                    int sucessCount = autoRuleTaskRecordDao.queryCount(task.getPuid(), task.getShopId(), task.getId(), 1);
                                    if (errorCount > 0) {
                                        task.setState(-1);
                                    } else if (sucessCount >= task.getCount()) {
                                        task.setState(1);
                                    }
                                }
                                autoRuleTaskDao.updateStateByPrimaryKey(task.getPuid(), task);
                            } catch (Exception e) {
                                log.error("分时策略重试任务处理失败，puid:{}, shopId:{} taskId:{} 错误信息：", shopAuth.getPuid(), shopAuth.getId(), task.getId(),e);
                            } finally {
                                lock.unlock();
                            }
                        }
                    } catch (Exception e) {
                        log.error("多线程处理失败，puid:{}, shopId:{} 错误信息：", shopAuth.getPuid(), shopAuth.getId(),e);
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    public void deleteAutoRuleTask(Integer shardIndex, Integer totalShard, Integer puid, Integer shopId, Long templateId, Long taskId) {
        List<Integer> puids = amazonAdStrategyTaskSupportDao.getPuidsByUpdateTime(puid, LocalDate.now().minusDays(7));
        if (CollectionUtils.isNotEmpty(puids)) {
            List<ShopAuth> shopAuthList = shopAuthDao.getAdAuthShopByShopIdList(puids);
            if (CollectionUtils.isEmpty(shopAuthList)) {
                return;
            }
            ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getCpcStrategyRetryPoolExecutor();
            shopAuthList = shopAuthList.stream().filter(s -> Math.abs(s.getId().hashCode() % totalShard) == shardIndex).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(shopAuthList)) {
                return;
            }
            CountDownLatch countDownLatch = new CountDownLatch(shopAuthList.size());
            for (ShopAuth shopAuth : shopAuthList) {
                threadExecutor.execute(() -> {
                    try {
//                        List<AdvertiseStrategyAdGroup> advertiseStrategyAdGroupList = advertiseStrategyAdGroupDao.getListByAdGroupId();
                    } catch (Exception e) {
                        log.error("多线程处理失败，puid:{}, shopId:{} 错误信息：", shopAuth.getPuid(), shopAuth.getId(),e);
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                log.error("deleteStrategyTask:", e);
            }
        }
    }

}
