<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.meiyunji.amzup.sellfox-sponsored</groupId>
        <artifactId>parent</artifactId>
        <version>1.0.1969-SNAPSHOT</version>
    </parent>

    <groupId>com.meiyunji.amzup.sellfox-sponsored</groupId>
    <artifactId>task</artifactId>


    <dependencies>
        <dependency>
            <groupId>com.meiyunji.amzup.sellfox-sponsored</groupId>
            <artifactId>service</artifactId>
            <version>${project.version}</version>
            <type>jar</type>
        </dependency>

        <dependency>
            <groupId>io.github.lognet</groupId>
            <artifactId>grpc-spring-boot-starter</artifactId>
            <version>${grpc-spring-boot.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>${xxl-job.version}</version>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.4.5</version>
        </dependency>

        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-commons</artifactId>
        </dependency>

        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-log4j2</artifactId>
            <version>${sentry-log4j2.version}</version>
        </dependency>

    </dependencies>

    <build>
            <finalName>sellfox-sponsored-task</finalName>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                </plugin>

                <!--<plugin>
                        <groupId>com.spotify</groupId>
                        <artifactId>dockerfile-maven-plugin</artifactId>
                        <version>1.4.13</version>
                        <executions>
                                <execution>
                                        <id>default</id>
                                        <goals>
                                                <goal>build</goal>
                                        </goals>
                                </execution>
                        </executions>
                        <configuration>
                                <repository>sellfox-sponsored-task</repository>
                                <tag>${project.version}</tag>
                                <buildArgs>
                                        <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
                                </buildArgs>
                        </configuration>
                </plugin>-->

	</plugins>
    </build>

</project>
