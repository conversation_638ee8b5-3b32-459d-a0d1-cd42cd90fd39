package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.ImmutableMap;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.bo.SearchTermAggregateBO;
import com.meiyunji.sponsored.service.cpc.bo.SearchTermBO;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdQueryStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.po.CpcSbQueryKeywordReport;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.CpcQueryWordDetailDto;
import com.meiyunji.sponsored.service.doris.dao.IOdsCpcQueryKeywordReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsCpcQueryTargetingReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsCpcSbQueryKeywordReportDao;
import com.meiyunji.sponsored.service.doris.dao.helper.SearchQueryTagSqlHelper;
import com.meiyunji.sponsored.service.doris.po.OdsCpcQueryKeywordReport;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.enums.SpKeywordGroupValueEnum;
import com.meiyunji.sponsored.service.enums.TargetMatchTypeEnum;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdQueryWordDataDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdQueryWordDataPageDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdQueryWordMatrixTopDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardQueryWordTypeEnum;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardQueryWordReqVo;
import com.meiyunji.sponsored.service.util.Constant;
import com.meiyunji.sponsored.service.wordFrequency.qo.QueryWordTopQo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryKeywordReport;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryTargetingReport;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.cpc.vo.CpcQueryWordDto;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.enums.MatchValueEnum;
import com.meiyunji.sponsored.service.wordFrequency.enums.WordRoot;
import com.meiyunji.sponsored.service.wordFrequency.vo.WordRootTopVo;
import org.springframework.jdbc.core.RowMapper;
import com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.cpc.util.Constants.ASIN_REGEX;

/**
 * amazon查询关键词报告表(OdsCpcQueryKeywordReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:21
 */
@Repository
@Slf4j
public class OdsCpcQueryKeywordReportDaoImpl extends DorisBaseDaoImpl<OdsCpcQueryKeywordReport> implements IOdsCpcQueryKeywordReportDao {

    @Autowired
    private IOdsCpcQueryTargetingReportDao odsCpcQueryTargetingReportDao;
    @Autowired
    private IOdsCpcSbQueryKeywordReportDao odsCpcSbQueryKeywordReportDao;

    private static final List<String> GROUP_CONCAT_COLUMNS = Arrays.asList("cost", "totalSales", "impressions", "clicks", "orderNum", "saleNum");
    private static final List<String> AMOUNT_FIELDS = Arrays.asList("cost", "totalSales", "acos", "roas", "cpc", "cpa");
    private static final Map<String, String> FIELD_2_INNER_SELECT_COLUMN = ImmutableMap.of(
            "cost", "ifnull(sum(r.cost * c.rate), 0) cost",
            "totalSales", "ifnull(sum(total_sales * c.rate), 0) totalSales",
            "impressions", "ifnull(sum(impressions), 0) impressions",
            "clicks", "ifnull(sum(clicks), 0) clicks",
            "orderNum", "ifnull(sum(sale_num), 0) orderNum",
            "saleNum", "ifnull(sum(order_num), 0) saleNum");

    // sb搜索词没有广告销量
    private static final Map<String, String> FIELD_2_INNER_SB_SELECT_COLUMN = ImmutableMap.of(
            "cost", "ifnull(sum(r.cost * c.rate), 0) cost",
            "totalSales", "ifnull(sum(sales14d * c.rate), 0) totalSales",
            "impressions", "ifnull(sum(impressions), 0) impressions",
            "clicks", "ifnull(sum(clicks), 0) clicks",
            "orderNum", "ifnull(sum(conversions14d), 0) orderNum",
            "saleNum", "0 saleNum");

    private static final Map<String, String> ALL_SEARCH_TERM_ORDER_FIELD_MAP = ImmutableMap.<String, String>builder()
            .put("occurrenceNum", "occurrence_num")
            .put("orderNum", "sale_num")
            .put("salesConversionRate", "ifnull(sale_num/clicks,0)")
            .put("searchFrequencyRank", "search_frequency_rank")
            .put("cost", "cost")
            .put("impressions", "impressions")
            .put("clicks", "clicks")
            .put("cpc", "ifnull(cost/clicks,0)")
            .put("cpa", "ifnull(cost/sale_num,0)")
            .put("acos", "ifnull(cost/total_sales,0)")
            .put("roas", "ifnull(total_sales/cost,0)")
            .put("sales", "total_sales")
            .put("adSales", "ad_sales")
            .put("adOtherSales", "ad_other_sales")
            .build();

    @Override
    public List<DashboardAdQueryWordDataDto> queryAdQueryWordCharts(Integer puid,
                                                                    List<String> marketplaceIdList,
                                                                    List<Integer> shopIdList,
                                                                    String currency,
                                                                    String startDate,
                                                                    String endDate,
                                                                    String orderByField,
                                                                    String orderBy,
                                                                    Integer limit, List<String> campaignIds,
                                                                    List<String> portfolioIds, Boolean isSiteToday,
                                                                    Boolean noZero) {

        List<String> siteToday = null;
        if (Boolean.TRUE.equals(isSiteToday)) {
            siteToday = CalculateAdDataUtil.getSiteToday(marketplaceIdList);
        }
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(orderByField);
        // 聚合sb用户搜索词和sp用户搜索词的查询结果
        String groupConcatColumns = getGroupConcatColumn(orderByField);
        String outerSelectColumns = getOuterSelectColumn(orderByField);
        String innerSelectColumns = getInnerSelectColumn(orderByField);
        String innerSBSelectColumns = getSBInnerSelectColumn(orderByField);
        groupConcatColumns = groupConcatColumns + ",targetNum";
        // 排序字段是否和金额相关
        boolean isOrderByFieldAboutAmount = AMOUNT_FIELDS.contains(orderByField);
        List<DashboardAdQueryWordDataDto> firstQueryResult = queryAdQueryWordTopSimpleInfos(puid, marketplaceIdList, shopIdList,
                currency, startDate, endDate, orderByField, orderBy,
                limit, groupConcatColumns, outerSelectColumns,
                innerSelectColumns, innerSBSelectColumns,
                isOrderByFieldAboutAmount, true, Collections.emptyList(), siteToday,
                isSiteToday, portfolioIds, campaignIds, noZero, dataField);

        List<String> firstGroupConcatColumnList = Arrays.stream(groupConcatColumns.split(",")).collect(Collectors.toList());
        List<String> secondGroupConcatColumnList = GROUP_CONCAT_COLUMNS.stream().filter(each -> !firstGroupConcatColumnList.contains(each)).collect(Collectors.toList());
        String secondGroupConcatColumns = String.join(",", secondGroupConcatColumnList);
        String secondOuterSelectColumns = getSecondOuterSelectColumn(secondGroupConcatColumnList);
        String secondInnerSelectColumns = getSecondInnerSelectColumn(secondGroupConcatColumnList);
        String secondInnerSBSelectColumns = getSecondInnerSBSelectColumn(secondGroupConcatColumnList);

        secondGroupConcatColumns = secondGroupConcatColumns + ",targetNum";

        List<String> queryWords = firstQueryResult.stream().map(DashboardAdQueryWordDataDto::getQueryWord).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(queryWords)) {
            return Collections.emptyList();
        }
        List<DashboardAdQueryWordDataDto> secondQueryResult = queryAdQueryWordTopSimpleInfos(puid, marketplaceIdList, shopIdList, currency, startDate, endDate,
                orderByField, orderBy, limit, secondGroupConcatColumns, secondOuterSelectColumns, secondInnerSelectColumns,
                secondInnerSBSelectColumns, isOrderByFieldAboutAmount, false, queryWords, siteToday,
                isSiteToday, portfolioIds, campaignIds, noZero, dataField);
        Map<String, DashboardAdQueryWordDataDto> queryWord2Data = secondQueryResult.stream().collect(Collectors.toMap(DashboardAdQueryWordDataDto::getQueryWord, Function.identity(), (oldVal, newVal) -> newVal));

        for (DashboardAdQueryWordDataDto firstQuery : firstQueryResult) {
            DashboardAdQueryWordDataDto secondQuery = queryWord2Data.get(firstQuery.getQueryWord());
            String firstQueryDetail = firstQuery.getDetailData();
            String secondQueryDetail = secondQuery.getDetailData();
            // 聚合两次查询的detail数据, 并且列按GROUP_CONCAT_COLUMNS的顺序返回
            String[] firstQueryDetailArr = firstQueryDetail.split(";");
            String[] secondQueryDetailArr = secondQueryDetail.split(";");
            Map<String, String[]> secondQueryMatchType2Detail = Arrays.stream(secondQueryDetailArr).map(each -> each.split(","))
                    .collect(Collectors.toMap(each -> each[0], Function.identity(), (oldVal, newVal) -> newVal));
            StringBuilder detailBuilder = new StringBuilder();
            secondGroupConcatColumnList.add("targetNum");
            for (String firstQueryDetailStr : firstQueryDetailArr) {
                String[] subFirstQueryDetailArr = firstQueryDetailStr.split(",");
                String[] subSecondQueryDetailArr = secondQueryMatchType2Detail.get(subFirstQueryDetailArr[0]);
                Map<String, String> field2Data = new HashMap<>();
                for (int i = 1; i < subFirstQueryDetailArr.length; i++) {
                    field2Data.put(firstGroupConcatColumnList.get(i - 1), subFirstQueryDetailArr[i]);
                }
                for (int i = 1; i < subSecondQueryDetailArr.length; i++) {
                    field2Data.put(secondGroupConcatColumnList.get(i - 1), subSecondQueryDetailArr[i]);
                }
                detailBuilder.append(subFirstQueryDetailArr[0]).append(",");
                for (String column : GROUP_CONCAT_COLUMNS) {
                    detailBuilder.append(field2Data.get(column)).append(",");
                }
                String s = subFirstQueryDetailArr[subFirstQueryDetailArr.length - 1];
                detailBuilder.append(s);
                detailBuilder.append(";");
            }
            firstQuery.setDetailData(detailBuilder.toString());
            unionData(firstQuery, secondQuery);
        }
        log.info("query-data: " + JSONUtil.objectToJson(firstQueryResult));
        return firstQueryResult;
    }

    private void unionData(DashboardAdQueryWordDataDto d1, DashboardAdQueryWordDataDto d2) {
        d1.setCost(Optional.ofNullable(d1.getCost()).orElse(d2.getCost()));
        d1.setTotalSales(Optional.ofNullable(d1.getTotalSales()).orElse(d2.getTotalSales()));
        d1.setImpressions(Optional.ofNullable(d1.getImpressions()).orElse(d2.getImpressions()));
        d1.setClicks(Optional.ofNullable(d1.getClicks()).orElse(d2.getClicks()));
        d1.setOrderNum(Optional.ofNullable(d1.getOrderNum()).orElse(d2.getOrderNum()));
        d1.setSaleNum(Optional.ofNullable(d1.getSaleNum()).orElse(d2.getSaleNum()));
        CalculateAdDataUtil.calAdCalData(d1);
    }

    private String getOuterSelectColumn(String orderByField) {
        switch (orderByField) {
            case "cost":
            case "totalSales":
                return "SUM(cost) cost, SUM(totalSales) totalSales";
            case "acos":
                return "SUM(cost) cost, SUM(totalSales) totalSales, ifnull(sum(cost) / sum(totalSales), 0) `acos`";
            case "roas":
                return "SUM(cost) cost, SUM(totalSales) totalSales, ifnull(sum(totalSales) / sum(cost), 0) roas";
            case "clickRate":
                return "SUM(clicks) clicks, SUM(impressions) impressions, ifnull(sum(clicks) / sum(impressions), 0) clickRate";
            case "conversionRate":
                return "SUM(orderNum) orderNum, SUM(clicks) clicks, ifnull(sum(orderNum) / sum(clicks), 0) conversionRate";
            case "cpc":
                return "SUM(cost) cost, SUM(totalSales) totalSales, SUM(clicks) clicks, ifnull(sum(cost) / sum(clicks), 0) cpc";
            case "cpa":
                return "SUM(cost) cost, SUM(totalSales) totalSales, SUM(orderNum) orderNum, ifnull(sum(cost) / sum(orderNum), 0) cpa";
            default:
                return "SUM(" + orderByField + ") " + orderByField;
        }
    }

    private String getSecondOuterSelectColumn(List<String> secondGroupConcatColumns) {
        return secondGroupConcatColumns.stream().map(each -> "SUM(" + each + ") " + each).collect(Collectors.joining(","));
    }

    private String getGroupConcatColumn(String orderByField) {
        switch (orderByField) {
            case "cost":
            case "totalSales":
            case "acos":
            case "roas":
                return "cost,totalSales";
            case "cpc":
                return "cost,totalSales,clicks";
            case "cpa":
                return "cost,totalSales,orderNum";
            case "impressions":
                return "impressions";
            case "clicks":
                return "clicks";
            case "orderNum":
                return "orderNum";
            case "saleNum":
                return "saleNum";
            case "clickRate":
                return "clicks,impressions";
            case "conversionRate":
                return "orderNum,clicks";
            default:
                return null;
        }
    }

    private List<DashboardAdQueryWordDataDto> queryAdQueryWordTopSimpleInfos(Integer puid,
                                                                             List<String> marketplaceIdList,
                                                                             List<Integer> shopIdList,
                                                                             String currency,
                                                                             String startDate,
                                                                             String endDate,
                                                                             String orderByField,
                                                                             String orderBy,
                                                                             Integer limit,
                                                                             String groupConcatColumns,
                                                                             String outerSelectColumns,
                                                                             String innerSelectColumns,
                                                                             String innerSBSelectColumns,
                                                                             boolean isOrderByFieldAboutAmount,
                                                                             boolean firstQuery,
                                                                             List<String> queryWords, List<String> siteToday,
                                                                             Boolean isSiteToday, List<String> portfolioIds,
                                                                             List<String> campaignIds, Boolean noZero,
                                                                             DashboardDataFieldEnum dashboardDataFieldEnum) {
        // 聚合sb用户搜索词和sp用户搜索词的查询结果
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("select queryWord, sum(targetNum) targetNum, any(marketplaceId) marketplaceId, ");
        sql.append(outerSelectColumns);
        sql.append(",GROUP_CONCAT(CONCAT_WS(',', matchType, ");

        sql.append(groupConcatColumns);
        sql.append("), ';') as detailData from (");

        String queryWordSpQuerySql = buildQueryAdQueryWordTopSimpleInfosSql(puid, marketplaceIdList, shopIdList, currency,
                startDate, endDate, argsList, innerSelectColumns,
                isOrderByFieldAboutAmount, firstQuery, queryWords, siteToday,
                isSiteToday, portfolioIds, campaignIds, noZero,dashboardDataFieldEnum);
        String queryWordSpTargetingQuerySql = odsCpcQueryTargetingReportDao.buildQueryAdQueryWordTopSimpleInfosSql(puid, marketplaceIdList, shopIdList, currency,
                startDate, endDate, argsList, innerSelectColumns, isOrderByFieldAboutAmount,
                firstQuery, queryWords, siteToday,
                isSiteToday, portfolioIds, campaignIds, noZero,dashboardDataFieldEnum);
        String queryWordSbQuerySql = odsCpcSbQueryKeywordReportDao.buildQueryAdQueryWordTopSimpleInfosSql(puid, marketplaceIdList, shopIdList, currency, startDate,
                endDate, argsList, innerSBSelectColumns,
                isOrderByFieldAboutAmount, firstQuery, queryWords, siteToday,
                isSiteToday, portfolioIds, campaignIds, noZero,dashboardDataFieldEnum);
        String unionAllSql = String.join("union all ", queryWordSpQuerySql, queryWordSpTargetingQuerySql, queryWordSbQuerySql);
        sql.append(unionAllSql);
        sql.append(") as t ");
        sql.append(" group by queryWord ");

        if (firstQuery) {
            if (Boolean.TRUE.equals(noZero) && !DashboardDataFieldEnum.noZeroFieldSet.contains(orderByField)) {
                sql.append(" having " + orderByField +" <> 0 ");
            }
            sql.append(" order by ").append(orderByField).append(" ").append(orderBy);
            sql.append(" , queryWord desc");
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        log.info(sql.toString());

        return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(DashboardAdQueryWordDataDto.class), argsList.toArray());
    }

    private String buildQueryAdQueryWordTopSimpleInfosSql(Integer puid,
                                                          List<String> marketplaceIdList,
                                                          List<Integer> shopIdList,
                                                          String currency,
                                                          String startDate,
                                                          String endDate,
                                                          List<Object> argsList,
                                                          String innerSelectColumns,
                                                          boolean isOrderByFieldAboutAmount,
                                                          boolean firstQuery,
                                                          List<String> queryWords, List<String> siteToday, Boolean isSiteToday,
                                                          List<String> portfolioIds, List<String> campaignIds,
                                                          Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum) {
        boolean isWhere = false;
        StringBuilder sql = new StringBuilder();
        sql.append("select query queryWord, match_type matchType, any(r.marketplace_id) marketplaceId, count(distinct keyword_id) targetNum, ");
        sql.append(innerSelectColumns);
        sql.append(" from ").append(getJdbcHelper().getTable()).append(" r ");
        if (firstQuery && isOrderByFieldAboutAmount || !firstQuery && !isOrderByFieldAboutAmount) {
            sql.append("join (select m.marketplace_id,c.month,c.rate from dim_currency_rate c join dim_marketplace_info m on ");
            sql.append("c.`from` = m.currency and c.puid=? and `to`=? and month>=? and month <=? ");
            argsList.add(puid);
            argsList.add(currency);
            argsList.add(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
            argsList.add(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));

            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append("and m.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
            }

            sql.append(") c on r.marketplace_id = c.marketplace_id and r.count_month = c.month and r.puid = ? and r.count_day>=? and r.count_day<=? ");
            argsList.add(puid);
            argsList.add(startDate);
            argsList.add(endDate);

            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append("and r.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sql.append(SqlStringUtil.dealBitMapDorisInList("r.shop_id", shopIdList, argsList));
            }
        } else {
            isWhere = true;
            sql.append(" where r.puid = ? and r.count_day >=? and r.count_day <= ? ");
            argsList.add(puid);
            argsList.add(startDate);
            argsList.add(endDate);

            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append("and r.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sql.append(SqlStringUtil.dealBitMapDorisInList("r.shop_id", shopIdList, argsList));
            }
        }



        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sql.append(SqlStringUtil.dealDorisInList("concat_ws('|', r.marketplace_id, r.count_day)", siteToday, argsList));
            sql.append(" and r.count_day >= ? and r.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sql.append(" and r.count_day >= ? and r.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealDorisInList("r.campaign_id", campaignIds, argsList));
        }


        if (CollectionUtils.isNotEmpty(queryWords) && !firstQuery) {
            sql.append(SqlStringUtil.dealInList("query", queryWords, argsList));
        }

        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (isWhere) {
                sql.append(" and ");
            } else {
                sql.append(" where ");
                isWhere = true;
            }
            sql.append(" r.campaign_id in ( ");
            sql.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = 'sp' ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sql.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sql.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sql.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sql.append(" ) ");
        }
        if (Boolean.TRUE.equals(noZero) && DashboardDataFieldEnum.noZeroFieldSet.contains(dashboardDataFieldEnum.getCode())) {
            if (isWhere) {
                sql.append(" and ");
            } else {
                sql.append(" where ");
                isWhere = true;
            }
            sql.append(" r." + getColumn(dashboardDataFieldEnum.getCode()) +" <> 0 ");
        }

        sql.append("group by queryWord, matchType ");

        return sql.toString();
    }

    private String getInnerSelectColumn(String orderByField) {
        switch (orderByField) {
            case "cost":
            case "totalSales":
            case "acos":
            case "roas":
                return "ifnull(sum(r.cost * c.rate), 0) cost, ifnull(sum(total_sales * c.rate), 0) totalSales";
            case "cpc":
                return "ifnull(sum(r.cost * c.rate), 0) cost, ifnull(sum(total_sales * c.rate), 0) totalSales, ifnull(sum(clicks), 0) clicks";
            case "cpa":
                return "ifnull(sum(r.cost * c.rate), 0) cost, ifnull(sum(total_sales * c.rate), 0) totalSales, ifnull(sum(sale_num), 0) orderNum";
            case "impressions":
                return "ifnull(sum(impressions), 0) impressions";
            case "clicks":
                return "ifnull(sum(clicks), 0) clicks";
            case "orderNum":
                return "ifnull(sum(sale_num), 0) orderNum";
            case "saleNum":
                return "ifnull(sum(order_num), 0) saleNum";
            case "clickRate":
                return "ifnull(sum(clicks), 0) clicks, ifnull(sum(impressions), 0) impressions";
            case "conversionRate":
                return "ifnull(sum(sale_num), 0) orderNum, ifnull(sum(clicks), 0) clicks";
            default:
                return null;
        }
    }

    private String getSBInnerSelectColumn(String orderByField) {
        switch (orderByField) {
            case "cost":
            case "totalSales":
            case "acos":
            case "roas":
                return "ifnull(sum(r.cost * c.rate), 0) cost, ifnull(sum(sales14d * c.rate), 0) totalSales";
            case "cpc":
                return "ifnull(sum(r.cost * c.rate), 0) cost, ifnull(sum(sales14d * c.rate), 0) totalSales, ifnull(sum(clicks), 0) clicks";
            case "cpa":
                return "ifnull(sum(r.cost * c.rate), 0) cost, ifnull(sum(sales14d * c.rate), 0) totalSales, ifnull(sum(conversions14d), 0) orderNum";
            case "impressions":
                return "ifnull(sum(impressions), 0) impressions";
            case "clicks":
                return "ifnull(sum(clicks), 0) clicks";
            case "orderNum":
                return "ifnull(sum(conversions14d), 0) orderNum";
            case "saleNum":
                return "0 saleNum";
            case "clickRate":
                return "ifnull(sum(clicks), 0) clicks, ifnull(sum(impressions), 0) impressions";
            case "conversionRate":
                return "ifnull(sum(conversions14d), 0) orderNum, ifnull(sum(clicks), 0) clicks";
            default:
                return null;
        }
    }

    private String getSecondInnerSelectColumn(List<String> secondGroupConcatColumns) {
        return secondGroupConcatColumns.stream().map(FIELD_2_INNER_SELECT_COLUMN::get).collect(Collectors.joining(","));
    }

    private String getSecondInnerSBSelectColumn(List<String> secondGroupConcatColumns) {
        return secondGroupConcatColumns.stream().map(FIELD_2_INNER_SB_SELECT_COLUMN::get).collect(Collectors.joining(","));
    }

    @Override
    public List<DashboardAdQueryWordMatrixTopDto> queryMatrixInfo(Integer puid, List<String> marketplaceIdList,
                                                                  List<Integer> shopIdList, String currency,
                                                                  String startDate, String endDate,
                                                                  DashboardDataFieldEnum dataField, List<String> keywordIdList,
                                                                  Integer limit, DashboardOrderByEnum orderBy,
                                                                  DashboardQueryWordTypeEnum queryWordType, List<String> siteToday, Boolean isSiteToday,
                                                                  List<String> portfolioIds, List<String> campaignIds,
                                                                  Boolean noZero) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
//            sb.append(" SELECT 'SP' as campaignType, report.query_id query_id, report.query query, ");
//            sb.append(" report.shop_id shopId, report.marketplace_id marketplaceId, 'keyword' as `type`, ");
//            sb.append(" report.campaign_id campaignId, report.ad_group_id adGroupId, ");
//            sb.append(" report.campaign_name campaignName, report.ad_group_name adGroupName, report.match_type matchType, '' as targetingText, ");
            sb.append(" SELECT 'sp' as campaignType, 'keyword' as type, report.query_id query_id, ");
            sb.append(" ifnull(sum(cost * c.rate), 0) cost, ifnull(sum(total_sales * c.rate), 0) totalSales, ");
            sb.append(" ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ");
            sb.append(" ifnull(sum(sale_num), 0) orderNum, ");
            sb.append(" ifnull(sum(order_num), 0) saleNum, ");
            sb.append(" ifnull(ROUND(ifnull(sum(clicks)/ sum(impressions), 0), 4), 0) clickRate, ");//点击率
            sb.append(" ifnull(ROUND(ifnull(sum(sale_num)/ sum(clicks), 0), 4), 0) conversionRate, ");//转化率
            sb.append(" ifnull(ROUND(ifnull(sum(cost * c.rate), 0)/ ifnull(sum(total_sales * c.rate), 0), 4), 0) acos, ");
            sb.append(" ifnull(ROUND(ifnull(sum(total_sales * c.rate), 0)/ ifnull(sum(cost * c.rate), 0), 4), 0) roas, ");
            sb.append(" ifnull(ROUND(ifnull(sum(cost * c.rate)/ sum(clicks), 0), 4), 0) cpc, ");//cpc
            sb.append(" ifnull(ROUND(ifnull(sum(cost * c.rate)/ sum(sale_num), 0), 4), 0) cpa ");//cpa
            sb.append(" from ").append(getJdbcHelper().getTable()).append("  report ");
            sb.append(" join (select * from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency and puid = ? and `to` = ? ");
            sb.append(" and c.month >= ").append(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(" and c.month <= ").append(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(") c");
            sb.append(" on c.marketplace_id = report.marketplace_id and report.count_month = c.month ");
            sb.append(" and report.puid = ? ");
            argsList.add(puid);
            argsList.add(currency);
            argsList.add(puid);
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append("and report.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append("and report.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
            }
            if (CollectionUtils.isNotEmpty(keywordIdList)) {
                sb.append("and report.keyword_id in ('").append(StringUtils.join(keywordIdList, "','")).append("') ");
            }


        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', report.marketplace_id, report.count_day)", siteToday, argsList));
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("report.campaign_id", campaignIds, argsList));
        }
        if (Boolean.TRUE.equals(noZero)) {
            sb.append(" and " + dataField.getCode() + " <> 0 ");
        }


        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" where report.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = 'sp' ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }
//            sb.append(" group by report.keyword_id, report.query, report.shop_id, report.marketplace_id, report.campaign_id, report.ad_group_id, report.campaign_name, report.ad_group_name, report.match_type");
        sb.append(" group by report.query_id ");
        sb.append(" ORDER BY ").append(dataField.getCode());
        if (StringUtils.isNotEmpty(orderBy.getCode())) {
            sb.append(" ").append(orderBy.getCode());
        }
        if (Objects.nonNull(limit) && limit != 0) {
            sb.append(" LIMIT ").append(limit);
        }
        return getJdbcTemplate().query(sb.toString(), new BeanPropertyRowMapper<>(DashboardAdQueryWordMatrixTopDto.class), argsList.toArray());
    }

    @Override
    public List<OdsCpcQueryKeywordReport> getByQueryIdList(Integer puid, List<Integer> shopIdList, Collection<String> queryIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT query_id, query, shop_id, marketplace_id, campaign_id, ad_group_id, keyword_id, ");
        sb.append(" match_type ");
        sb.append(" from ").append(getJdbcHelper().getTable());
        sb.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(queryIdList)) {
            sb.append("and query_id in ('").append(StringUtils.join(queryIdList, "','")).append("') ");
        }
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(OdsCpcQueryKeywordReport.class), argsList.toArray());
    }

//    todo:第一版查询搜索词图表实现方式,如果新版实现效果更好就删除
//    @Override
//    public List<DashboardAdQueryWordDataDto> queryAdQueryWordCharts(Integer puid, List<String> marketplaceIdList, List<Integer> shopIdList, String currency, String startDate, String endDate, String orderByField, String orderBy, Integer limit) {
//        // 聚合sb用户搜索词和sp用户搜索词的查询结果
//        List<Object> argsList = new ArrayList<>();
//        StringBuilder sql = new StringBuilder();
//        sql.append("select queryWord, SUM(cost) cost, SUM(totalSales) totalSales, SUM(impressions) impressions, ");
//        sql.append("SUM(clicks) clicks, SUM(orderNum) orderNum, SUM(saleNum) saleNum, ifnull(sum(cost) / sum(totalSales), 0) `acos`, ifnull(sum(totalSales) / sum(cost), 0) roas, ");
//        sql.append("ifnull(sum(clicks) / sum(impressions), 0) clickRate, ifnull(sum(orderNum) / sum(clicks), 0) conversionRate, ifnull(sum(cost) / sum(clicks), 0) cpc, ifnull(sum(cost) / sum(orderNum), 0) cpa, ");
//        sql.append("GROUP_CONCAT(CONCAT_WS(',', matchType, cost, totalSales, impressions, clicks, orderNum, saleNum), ';') as detailData from (");
//        String queryWordSpQuerySql = buildQueryAdQueryWordChartsSql(puid, marketplaceIdList, shopIdList, currency, startDate, endDate, argsList);
//        String queryWordSpTargetingQuerySql = odsCpcQueryTargetingReportDao.buildQueryAdQueryWordChartsSql(puid, marketplaceIdList, shopIdList, currency, startDate, endDate, argsList);
//        String queryWordSbQuerySql = odsCpcSbQueryKeywordReportDao.buildQueryAdQueryWordChartsSql(puid, marketplaceIdList, shopIdList, currency, startDate, endDate, argsList);
//        String unionAllSql = String.join("union all ", queryWordSpQuerySql, queryWordSpTargetingQuerySql, queryWordSbQuerySql);
//        sql.append(unionAllSql);
//        sql.append(") as t group by queryWord order by ");
//        sql.append(orderByField).append(" ").append(orderBy);
//        sql.append(" limit ? ");
//        argsList.add(limit);
//        log.info(sql.toString());
//        return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(DashboardAdQueryWordDataDto.class), argsList.toArray());
//    }
//
//    private String buildQueryAdQueryWordChartsSql(Integer puid, List<String> marketplaceIdList, List<Integer> shopIdList, String currency, String startDate, String endDate, List<Object> argsList) {
//        StringBuilder sql = new StringBuilder();
//        sql.append("select query queryWord, match_type matchType, ifnull(sum(r.cost * c.rate), 0) cost, ifnull(sum(total_sales * c.rate), 0) totalSales, ");
//        sql.append("ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ");
//        sql.append("ifnull(sum(sale_num), 0) orderNum, ifnull(sum(order_num), 0) saleNum ");
//        sql.append("from ").append(getJdbcHelper().getTable()).append(" r ");
//        sql.append("join (select m.marketplace_id,c.month,c.rate,c.puid from dim_currency_rate c join dim_marketplace_info m on ");
//        sql.append("c.`from` = m.currency and c.puid=? and `to`=? and month>=? and month<=? ");
//        argsList.add(puid);
//        argsList.add(currency);
//        argsList.add(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
//        argsList.add(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
//
//        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
//            sql.append("and m.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
//        }
//
//        sql.append(") c on r.puid = ? and r.puid = c.puid and r.count_month = c.month and c.marketplace_id = r.marketplace_id and r.count_day >= ? and r.count_day <= ? ");
//        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
//            sql.append("and r.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
//        }
//        if (CollectionUtils.isNotEmpty(shopIdList)) {
//            sql.append("and r.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
//        }
//        sql.append("group by queryWord, matchType ");
//
//        argsList.add(puid);
//        argsList.add(startDate);
//        argsList.add(endDate);
//        return sql.toString();
//    }

    @Override
    public Page pageManageList(Integer puid, CpcQueryWordDto dto, Page page) {
        List<Object> argList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT query_id, p.`marketplace_id`,`query`,`query_cn`,type, keyword_id,target_id,")
                .append(" keyword_text,match_type,targeting_expression,targeting_type,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("impressions,clicks,cost,sale_num,ad_order_num,")
                .append("total_sales,`ad_sales`,`ad_sale_num`, ")
                .append("order_num ");
        if (dto.isQueryJoinSearchTermsRank()) {
            sql.append(",ifnull(search_frequency_rank, 2147483647) search_frequency_rank, ifnull(round(week_ratio*100,2), -2147483648) week_ratio ");
        }
        sql.append(" FROM ( ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        //keyword搜索词表
        StringBuilder selectKeywordSql = new StringBuilder("SELECT query_id, ANY(kr.`marketplace_id`) marketplace_id, ANY(`query`) query, ANY(`query_cn`) query_cn,")
                .append(" 'keyword' as type, ANY(keyword_id) keyword_id, '' as target_id, ANY(keyword_text) keyword_text, ANY(match_type) match_type, '' as targeting_expression, '' as targeting_type,")
                .append(" ANY(ad_group_id) ad_group_id, ANY(ad_group_name) ad_group_name, ANY(campaign_id) campaign_id, ANY(campaign_name) campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ");
        selectKeywordSql.append(this.getJdbcHelper().getTable()).append(" kr ");
        selectKeywordSql.append(this.getWhereSqlCountByKeyword(puid, dto, argList));
        //target搜索词表
        StringBuilder selectTargetSql = new StringBuilder("SELECT query_id, ANY(tr.`marketplace_id`) marketplace_id, ANY(`query`) query, ANY(`query_cn`) query_cn,")
                .append(" 'target' as type, '' as keyword_id, ANY(target_id) target_id, '' as keyword_text, '' as match_type, ANY(targeting_expression) targeting_expression, ANY(targeting_type) targeting_type,")
                .append(" ANY(ad_group_id) ad_group_id, ANY(ad_group_name) ad_group_name, ANY(campaign_id) campaign_id, ANY(campaign_name) campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ");
        selectTargetSql.append(" ods_t_cpc_query_targeting_report tr ");
        selectTargetSql.append(this.getWhereSqlCountByTarget(puid, dto, argList));
        //两个表UNION ALL
        sql.append(selectKeywordSql);
        sql.append(" UNION ALL ");
        sql.append(selectTargetSql);
        sql.append(" ) p ");

        List<Object> countArgList = new ArrayList<>(argList);
        StringBuilder abaRankSql = new StringBuilder();
        boolean isFilterAbaRank = false;
        if (dto.isQueryJoinSearchTermsRank()) {
            isFilterAbaRank = dto.getUseAdvanced() && (
                    dto.getSearchFrequencyRankMin() != null || dto.getSearchFrequencyRankMax() != null ||
                            dto.getWeekRatioMin() != null || dto.getWeekRatioMax() != null);
            abaRankSql.append(" left join ods_t_week_search_terms_analysis a on lower(query) = a.search_term and p.marketplace_id = a.marketplace_id and a.start_date=? and a.marketplace_id=? ");
            argList.add(dto.getLastWeekSearchTermsRankDate());
            argList.add(dto.getMarketplaceId());

            if (isFilterAbaRank) {
                countArgList.add(dto.getLastWeekSearchTermsRankDate());
                countArgList.add(dto.getMarketplaceId());
                if (dto.getSearchFrequencyRankMin() != null) {
                    abaRankSql.append(" and ifnull(search_frequency_rank, 2147483647) >= ? ");
                    argList.add(dto.getSearchFrequencyRankMin());
                    countArgList.add(dto.getSearchFrequencyRankMin());
                }
                if (dto.getSearchFrequencyRankMax() != null) {
                    abaRankSql.append(" and ifnull(search_frequency_rank, 2147483647) <= ? ");
                    argList.add(dto.getSearchFrequencyRankMax());
                    countArgList.add(dto.getSearchFrequencyRankMax());
                }
                if (dto.getWeekRatioMin() != null) {
                    abaRankSql.append(" and ifnull(round(week_ratio*100,2), -2147483648) >= ? ");
                    argList.add(dto.getWeekRatioMin());
                    countArgList.add(dto.getWeekRatioMin());
                }
                if (dto.getWeekRatioMax() != null) {
                    abaRankSql.append(" and ifnull(round(week_ratio*100,2), -2147483648) <= ? ");
                    if (dto.getWeekRatioMin() == null) {
                        abaRankSql.append(" and ifnull(round(week_ratio*100,2), -2147483648) > -2147483648 ");
                    }
                    argList.add(dto.getWeekRatioMax());
                    countArgList.add(dto.getWeekRatioMax());
                }
                abaRankSql.append(" where 1=1 ");
                abaRankSql.append(" and search_frequency_rank is not null ");
                if (dto.getSearchFrequencyRankMin() != null) {
                    abaRankSql.append(" and search_frequency_rank >= ? ");
                    argList.add(dto.getSearchFrequencyRankMin());
                    countArgList.add(dto.getSearchFrequencyRankMin());
                }
                if (dto.getSearchFrequencyRankMax() != null) {
                    abaRankSql.append(" and search_frequency_rank <= ? ");
                    argList.add(dto.getSearchFrequencyRankMax());
                    countArgList.add(dto.getSearchFrequencyRankMax());
                }
                if (dto.getWeekRatioMin() != null) {
                    abaRankSql.append(" and round(week_ratio*100,2) >= ? ");
                    argList.add(dto.getWeekRatioMin());
                    countArgList.add(dto.getWeekRatioMin());
                }
                if (dto.getWeekRatioMax() != null) {
                    abaRankSql.append(" and round(week_ratio*100,2) <= ? ");
                    argList.add(dto.getWeekRatioMax());
                    countArgList.add(dto.getWeekRatioMax());
                }
            }
        }

        sql.append(abaRankSql);

        if (StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())) {
            String orderField = ReportService.getOrderFieldIgnoreCountDate(dto.getOrderField(), false, dto.isQueryJoinSearchTermsRank());
            if (StringUtils.isNotBlank(orderField)) {
                sql.append(" order by ").append(orderField);
                if ("desc".equals(dto.getOrderValue())) {
                    sql.append(" desc");
                }
                sql.append(" , query desc, keyword_id desc, target_id desc ");
            } else {
                sql.append(" order by query desc, keyword_id desc, target_id desc ");
            }
        } else {
            sql.append(" order by query desc, keyword_id desc, target_id desc ");
        }

        countSql.append(selectKeywordSql);
        countSql.append(" UNION ALL ");
        countSql.append(selectTargetSql);
        countSql.append(" ) p ");
        if (isFilterAbaRank) {
            countSql.append(abaRankSql);
        }

        return getPageByMapper(page.getPageNo(), page.getPageSize(), countSql.toString(), countArgList.toArray(), sql.toString(), argList.toArray(), new RowMapper<AdQueryOptionVo>() {
            @Override
            public AdQueryOptionVo mapRow(ResultSet res, int i) throws SQLException {
                AdQueryOptionVo.AdQueryOptionVoBuilder optionVoBuilder = AdQueryOptionVo.builder()
                        .queryId(res.getString("query_id"))
                        .marketplaceId(res.getString("marketplace_id"))
                        .query(res.getString("query"))
                        .queryCn(res.getString("query_cn"))
                        .type(res.getString("type"))
                        .keywordId(res.getString("keyword_id"))
                        .targetId(res.getString("target_id"))
                        .keywordText(res.getString("keyword_text"))
                        .matchType(res.getString("match_type"))
                        .targetingExpression(res.getString("targeting_expression"))
                        .targetingType(res.getString("targeting_type"))
                        .adGroupId(res.getString("ad_group_id"))
                        .adGroupName(res.getString("ad_group_name"))
                        .campaignId(res.getString("campaign_id"))
                        .campaignName(res.getString("campaign_name"))
                        .impressions(res.getInt("impressions"))
                        .clicks(res.getInt("clicks"))
                        .cost(res.getBigDecimal("cost") != null ? res.getBigDecimal("cost") : BigDecimal.ZERO)
                        .saleNum(res.getInt("sale_num"))
                        /**
                         * TODO 广告报告重构
                         * 本广告产品销量
                         */
                        .adOrderNum(res.getInt("ad_order_num"))
                        //广告销售额
                        .totalSales(res.getBigDecimal("total_sales") != null ? res.getBigDecimal("total_sales") : BigDecimal.ZERO)
                        //本广告产品订单量
                        .adSaleNum(Optional.ofNullable(res.getInt("ad_sale_num")).orElse(0))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(res.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        //广告销量
                        .salesNum(Optional.ofNullable(res.getInt("order_num")).orElse(0));
                if (dto.isQueryJoinSearchTermsRank()) {
                    optionVoBuilder.searchFrequencyRank(res.getInt("search_frequency_rank"))
                            .weekRatio(res.getBigDecimal("week_ratio"));
                }
                AdQueryOptionVo optionVo = optionVoBuilder.build();

                return optionVo;
            }
        });
    }


    @Override
    public int pageManageCountAll(Integer puid, CpcQueryWordDto dto) {
        List<Object> argList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT p.`marketplace_id`,`query`,`query_cn`,type, keyword_id,target_id,")
                .append(" keyword_text,match_type,targeting_expression,targeting_type,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("impressions,clicks,cost,sale_num,ad_order_num,")
                .append("total_sales,`ad_sales`,`ad_sale_num`, ")
                .append("order_num ");
        if (dto.isQueryJoinSearchTermsRank()) {
            sql.append(",ifnull(search_frequency_rank, 2147483647) search_frequency_rank, ifnull(round(week_ratio*100,2), -2147483648) week_ratio ");
        }
        sql.append(" FROM ( ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        //keyword搜索词表
        StringBuilder selectKeywordSql = new StringBuilder("SELECT ANY(kr.`marketplace_id`) marketplace_id, ANY(`query`) query, ANY(`query_cn`) query_cn,")
                .append(" 'keyword' as type, ANY(keyword_id) keyword_id, '' as target_id, ANY(keyword_text) keyword_text, ANY(match_type) match_type, '' as targeting_expression, '' as targeting_type,")
                .append(" ANY(ad_group_id) ad_group_id, ANY(ad_group_name) ad_group_name, ANY(campaign_id) campaign_id, ANY(campaign_name) campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ");
        selectKeywordSql.append(this.getJdbcHelper().getTable()).append(" kr ");
        selectKeywordSql.append(this.getWhereSqlCountByKeyword(puid, dto, argList));
        //target搜索词表
        StringBuilder selectTargetSql = new StringBuilder("SELECT ANY(tr.`marketplace_id`) marketplace_id, ANY(`query`) query, ANY(`query_cn`) query_cn,")
                .append(" 'target' as type, '' as keyword_id, ANY(target_id) target_id, '' as keyword_text, '' as match_type, ANY(targeting_expression) targeting_expression, ANY(targeting_type) targeting_type,")
                .append(" ANY(ad_group_id) ad_group_id, ANY(ad_group_name) ad_group_name, ANY(campaign_id) campaign_id, ANY(campaign_name) campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ");
        selectTargetSql.append(" ods_t_cpc_query_targeting_report tr ");
        selectTargetSql.append(this.getWhereSqlCountByTarget(puid, dto, argList));
        //两个表UNION ALL
        sql.append(selectKeywordSql);
        sql.append(" UNION ALL ");
        sql.append(selectTargetSql);
        sql.append(" ) p ");
        List<Object> countArgList = new ArrayList<>(argList);
        StringBuilder abaRankSql = new StringBuilder();
        boolean isFilterAbaRank = false;
        if (dto.isQueryJoinSearchTermsRank()) {
            isFilterAbaRank = dto.getUseAdvanced() && (
                    dto.getSearchFrequencyRankMin() != null || dto.getSearchFrequencyRankMax() != null ||
                            dto.getWeekRatioMin() != null || dto.getWeekRatioMax() != null);
            abaRankSql.append(" left join ods_t_week_search_terms_analysis a on lower(query) = a.search_term and p.marketplace_id = a.marketplace_id and a.start_date=? and a.marketplace_id=? ");
            argList.add(dto.getLastWeekSearchTermsRankDate());
            argList.add(dto.getMarketplaceId());

            if (isFilterAbaRank) {
                countArgList.add(dto.getLastWeekSearchTermsRankDate());
                countArgList.add(dto.getMarketplaceId());
                if (dto.getSearchFrequencyRankMin() != null) {
                    abaRankSql.append(" and ifnull(search_frequency_rank, 2147483647) >= ? ");
                    argList.add(dto.getSearchFrequencyRankMin());
                    countArgList.add(dto.getSearchFrequencyRankMin());
                }
                if (dto.getSearchFrequencyRankMax() != null) {
                    abaRankSql.append(" and ifnull(search_frequency_rank, 2147483647) <= ? ");
                    argList.add(dto.getSearchFrequencyRankMax());
                    countArgList.add(dto.getSearchFrequencyRankMax());
                }
                if (dto.getWeekRatioMin() != null) {
                    abaRankSql.append(" and ifnull(round(week_ratio*100,2), -2147483648) >= ? ");
                    argList.add(dto.getWeekRatioMin());
                    countArgList.add(dto.getWeekRatioMin());
                }
                if (dto.getWeekRatioMax() != null) {
                    abaRankSql.append(" and ifnull(round(week_ratio*100,2), -2147483648) <= ? ");
                    if (dto.getWeekRatioMin() == null) {
                        abaRankSql.append(" and ifnull(round(week_ratio*100,2), -2147483648) > -2147483648 ");
                    }
                    argList.add(dto.getWeekRatioMax());
                    countArgList.add(dto.getWeekRatioMax());
                }
                abaRankSql.append(" where 1=1 ");
                abaRankSql.append(" and search_frequency_rank is not null ");
                if (dto.getSearchFrequencyRankMin() != null) {
                    abaRankSql.append(" and search_frequency_rank >= ? ");
                    argList.add(dto.getSearchFrequencyRankMin());
                    countArgList.add(dto.getSearchFrequencyRankMin());
                }
                if (dto.getSearchFrequencyRankMax() != null) {
                    abaRankSql.append(" and search_frequency_rank <= ? ");
                    argList.add(dto.getSearchFrequencyRankMax());
                    countArgList.add(dto.getSearchFrequencyRankMax());
                }
                if (dto.getWeekRatioMin() != null) {
                    abaRankSql.append(" and round(week_ratio*100,2) >= ? ");
                    argList.add(dto.getWeekRatioMin());
                    countArgList.add(dto.getWeekRatioMin());
                }
                if (dto.getWeekRatioMax() != null) {
                    abaRankSql.append(" and round(week_ratio*100,2) <= ? ");
                    argList.add(dto.getWeekRatioMax());
                    countArgList.add(dto.getWeekRatioMax());
                }
            }
        }

        sql.append(abaRankSql);

        if (StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())) {
            String orderField = ReportService.getOrderField(dto.getOrderField(), false, dto.isQueryJoinSearchTermsRank());
            if (StringUtils.isNotBlank(orderField)) {
                sql.append(" order by ").append(orderField);
                if ("desc".equals(dto.getOrderValue())) {
                    sql.append(" desc");
                }
                sql.append(" , query desc, keyword_id desc, target_id desc ");
            } else {
                sql.append(" order by query desc, keyword_id desc, target_id desc ");
            }
        } else {
            sql.append(" order by query desc, keyword_id desc, target_id desc ");
        }

        countSql.append(selectKeywordSql);
        countSql.append(" UNION ALL ");
        countSql.append(selectTargetSql);
        countSql.append(" ) p ");
        if (isFilterAbaRank) {
            countSql.append(abaRankSql);
        }
        return countPageResult(puid, countSql.toString(), countArgList.toArray());
    }

    @Override
    public AdMetricDto getSumAdMetricDto(Integer puid, CpcQueryWordDto dto) {
        List<Object> argList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT SUM(cost) cost,SUM(sale_num) sale_num,SUM(total_sales) total_sales,SUM(order_num) order_num FROM ( ");
        boolean filterAbaRank = dto.isQueryJoinSearchTermsRank() && dto.getUseAdvanced() && (
                dto.getSearchFrequencyRankMin() != null || dto.getSearchFrequencyRankMax() != null ||
                        dto.getWeekRatioMin() != null || dto.getWeekRatioMax() != null);
        //keyword搜索词
        StringBuilder selectKeywordSql = new StringBuilder("SELECT SUM(cost) cost,SUM(sale_num) sale_num, SUM(total_sales) total_sales, SUM(order_num) order_num ");
        if (filterAbaRank) {
            selectKeywordSql.append(",any(query) query,any(marketplace_id) marketplace_id ");
        }
        selectKeywordSql.append(" FROM ").append(this.getJdbcHelper().getTable());
        selectKeywordSql.append(this.getWhereSqlCountByKeyword(puid, dto, argList));
        //target搜索词
        StringBuilder selectTargetSql = new StringBuilder("SELECT SUM(cost) cost,SUM(sale_num) sale_num, SUM(total_sales) total_sales, SUM(order_num) order_num ");
        if (filterAbaRank) {
            selectTargetSql.append(",any(query) query,any(marketplace_id) marketplace_id ");
        }
        selectTargetSql.append(" FROM ").append(" ods_t_cpc_query_targeting_report ");
        selectTargetSql.append(this.getWhereSqlCountByTarget(puid, dto, argList));
        //两个表UNION ALL
        sql.append(selectKeywordSql);
        sql.append(" UNION ALL ");
        sql.append(selectTargetSql);
        sql.append(" ) p");
        if (filterAbaRank) {
            sql.append(" left join ods_t_week_search_terms_analysis a on lower(query) = a.search_term and p.marketplace_id = a.marketplace_id and a.start_date=? and a.marketplace_id=? ");
            argList.add(dto.getLastWeekSearchTermsRankDate());
            argList.add(dto.getMarketplaceId());

            if (dto.getSearchFrequencyRankMin() != null) {
                sql.append(" and ifnull(search_frequency_rank, 2147483647) >= ? ");
                argList.add(dto.getSearchFrequencyRankMin());
            }
            if (dto.getSearchFrequencyRankMax() != null) {
                sql.append(" and ifnull(search_frequency_rank, 2147483647) <= ? ");
                argList.add(dto.getSearchFrequencyRankMax());
            }
            if (dto.getWeekRatioMin() != null) {
                sql.append(" and ifnull(round(week_ratio*100,2),-2147483648) >= ? ");
                argList.add(dto.getWeekRatioMin());
            }
            if (dto.getWeekRatioMax() != null) {
                sql.append(" and ifnull(round(week_ratio*100,2),-2147483648) <= ? ");
                if (dto.getWeekRatioMin() == null) {
                    sql.append(" and ifnull(round(week_ratio*100,2),-2147483648) > -2147483648 ");
                }
                argList.add(dto.getWeekRatioMax());
            }
            sql.append(" where 1=1 ");
            sql.append(" and search_frequency_rank is not null ");
            if (dto.getSearchFrequencyRankMin() != null) {
                sql.append(" and search_frequency_rank >= ?");
                argList.add(dto.getSearchFrequencyRankMin());
            }
            if (dto.getSearchFrequencyRankMax() != null) {
                sql.append(" and search_frequency_rank <= ?");
                argList.add(dto.getSearchFrequencyRankMax());
            }
            if (dto.getWeekRatioMin() != null) {
                sql.append(" and round(week_ratio*100,2) >= ?");
                argList.add(dto.getWeekRatioMin());
            }
            if (dto.getWeekRatioMax() != null) {
                sql.append(" and round(week_ratio*100,2) <= ?");
                argList.add(dto.getWeekRatioMax());
            }
        }

        List<AdMetricDto> adMetricDtoList = getJdbcTemplate().query(sql.toString(), new RowMapper<AdMetricDto>() {
            @Override
            public AdMetricDto mapRow(ResultSet re, int i) throws SQLException {
                AdMetricDto dto = AdMetricDto.builder()
                        .sumCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .sumAdSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("sale_num")).orElse(BigDecimal.ZERO))
                        .sumOrderNum(Optional.ofNullable(re.getBigDecimal("order_num")).orElse(BigDecimal.ZERO))
                        .build();
                return dto;
            }
        }, argList.toArray());
        return adMetricDtoList.size() > 0 ? adMetricDtoList.get(0) : null;
    }

    @Override
    public List<AdHomePerformancedto> getQueryKeywordReportAggregate(Integer puid, CpcQueryWordDto dto, boolean isGroupByDate, boolean selCompareData) {
        List<Object> argsList = Lists.newArrayList();
        //keyword表
        StringBuilder keywordSql = new StringBuilder("select ");
        if (isGroupByDate) {
            keywordSql.append(" count_day, ");
        }
        keywordSql.append(" SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,")
                .append("SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales, SUM(ad_sales) `ad_sales`,")
                .append("SUM(ad_sale_num)  `ad_sale_num`,SUM(order_num) sales_num ");
        if (dto.isQueryJoinSearchTermsRank()) {
            keywordSql.append(" ,query, marketplace_id ");
        }
        keywordSql.append(" FROM ").append(this.getJdbcHelper().getTable());
        keywordSql.append(" where puid = ? and shop_id = ? and count_day >= ? and count_day <= ? and query_id in ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        if (selCompareData) {
            argsList.add(dto.getCompareStartDate());
            argsList.add(dto.getCompareEndDate());
        } else {
            argsList.add(dto.getStart());
            argsList.add(dto.getEnd());
        }
        //子查询
        keywordSql.append("(select query_id queryId")
                .append(" FROM ").append(this.getJdbcHelper().getTable()).append(" kr ")
                .append(this.getWhereSqlCountByKeyword(puid, dto, argsList))
                .append(")");
        if (isGroupByDate) {
            keywordSql.append(" group by count_day ");
        }
        if (dto.isQueryJoinSearchTermsRank()) {
            if (isGroupByDate) {
                keywordSql.append(" ,query, marketplace_id ");
            } else {
                keywordSql.append(" group by query, marketplace_id ");
            }
        }

        //target表
        StringBuilder targetSql = new StringBuilder("select ");
        if (isGroupByDate) {
            targetSql.append(" count_day, ");
        }
        targetSql.append(" SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,")
                .append("SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales, SUM(ad_sales) `ad_sales`,")
                .append("SUM(ad_sale_num)  `ad_sale_num`,SUM(order_num) sales_num ");
        if (dto.isQueryJoinSearchTermsRank()) {
            targetSql.append(" ,query, marketplace_id ");
        }
        targetSql.append(" FROM ods_t_cpc_query_targeting_report ");
        targetSql.append(" where puid = ? and shop_id = ? and count_day >= ? and count_day <= ? and query_id in ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        if (selCompareData) {
            argsList.add(dto.getCompareStartDate());
            argsList.add(dto.getCompareEndDate());
        } else {
            argsList.add(dto.getStart());
            argsList.add(dto.getEnd());
        }
        //子查询
        targetSql.append("(select query_id queryId")
                .append(" FROM ods_t_cpc_query_targeting_report tr ")
                .append(this.getWhereSqlCountByTarget(puid, dto, argsList))
                .append(")");
        if (isGroupByDate) {
            targetSql.append(" group by count_day ");
        }
        if (dto.isQueryJoinSearchTermsRank()) {
            if (isGroupByDate) {
                targetSql.append(" ,query, marketplace_id ");
            } else {
                targetSql.append(" group by query, marketplace_id ");
            }
        }

        StringBuilder sql = new StringBuilder(" select ");
        if (isGroupByDate) {
            sql.append(" count_day, ");
        }
        sql.append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num, SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales, SUM(ad_sales) `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`,SUM(sales_num) sales_num from ")
                .append("(")
                .append(keywordSql)
                .append(" UNION ALL ")
                .append(targetSql)
                .append(") c");

        if (dto.isQueryJoinSearchTermsRank()) {
            sql.append(" left join ods_t_week_search_terms_analysis a on lower(query) = a.search_term and c.marketplace_id = a.marketplace_id and a.start_date=? and a.marketplace_id=? ");
            argsList.add(dto.getLastWeekSearchTermsRankDate());
            argsList.add(dto.getMarketplaceId());

            if (dto.getSearchFrequencyRankMin() != null) {
                sql.append(" and ifnull(search_frequency_rank, 2147483647) >= ? ");
                argsList.add(dto.getSearchFrequencyRankMin());
            }
            if (dto.getSearchFrequencyRankMax() != null) {
                sql.append(" and ifnull(search_frequency_rank, 2147483647) <= ? ");
                argsList.add(dto.getSearchFrequencyRankMax());
            }
            if (dto.getWeekRatioMin() != null) {
                sql.append(" and ifnull(round(week_ratio*100,2),-2147483648) >= ? ");
                argsList.add(dto.getWeekRatioMin());
            }
            if (dto.getWeekRatioMax() != null) {
                sql.append(" and ifnull(round(week_ratio*100,2),-2147483648) <= ? ");
                if (dto.getWeekRatioMin() == null) {
                    sql.append(" and ifnull(round(week_ratio*100,2),-2147483648) > -2147483648 ");
                }
                argsList.add(dto.getWeekRatioMax());
            }
            sql.append(" where 1=1 ");
            sql.append(" and search_frequency_rank is not null ");
            if (dto.getSearchFrequencyRankMin() != null) {
                sql.append(" and search_frequency_rank >= ?");
                argsList.add(dto.getSearchFrequencyRankMin());
            }
            if (dto.getSearchFrequencyRankMax() != null) {
                sql.append(" and search_frequency_rank <= ?");
                argsList.add(dto.getSearchFrequencyRankMax());
            }
            if (dto.getWeekRatioMin() != null) {
                sql.append(" and round(week_ratio*100,2) >= ?");
                argsList.add(dto.getWeekRatioMin());
            }
            if (dto.getWeekRatioMax() != null) {
                sql.append(" and round(week_ratio*100,2) <= ?");
                argsList.add(dto.getWeekRatioMax());
            }
        }

        if (isGroupByDate) {
            sql.append(" group by count_day ");
        }

        return getJdbcTemplate().query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
            @Override
            public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                return AdHomePerformancedto.builder()
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        //本广告产品订单量
                        .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        //广告销量
                        .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                        //本广告产品销量
                        .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                        //时间
                        .countDate(!isGroupByDate ? "" : DateUtil.dateToStrWithFormat(DateUtil.strToDate(re.getString("count_day"), DateUtil.PATTERN), DateUtil.PATTERN_YYYYMMDD))
                        .build();
            }
        }, argsList.toArray());
    }

    @Override
    public List<WordRootTopVo> getWordRootToplist(QueryWordTopQo dto) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select word_root, count(distinct query_id) count ")
                .append(" from ods_t_amazon_word_root_query ");
        sqlSb.append(" where puid = ? and shop_id = ? and count_day >= ? and count_day <= ? ");
        argsList.add(dto.getPuid());
        argsList.add(dto.getShopId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        //搜索词来源类型，只取sp的
        sqlSb.append(" and query_type in (?, ?) ");
        argsList.add(WordRoot.QueryType.SP_QUERY.getType());
        argsList.add(WordRoot.QueryType.SP_TARGETING.getType());
        if (StringUtils.isNotBlank(dto.getWordFrequencyType())) {
            WordRoot.WordFrequencyType wordFrequencyType = WordRoot.WordFrequencyType.getWordFrequencyType(Integer.valueOf(dto.getWordFrequencyType()));
            if (wordFrequencyType != null) {
                sqlSb.append(" and word_frequency_type = ? ");
                argsList.add(dto.getWordFrequencyType());
            }
        }

        //查询queryId
        StringBuilder queryIdSql = new StringBuilder("SELECT distinct `query_id` queryId FROM ( ");
        StringBuilder selectKeywordSql = new StringBuilder("SELECT ANY(`marketplace_id`) marketplace_id, ANY(`query_id`) query_id, ANY(`query`) query, ANY(`query_cn`) query_cn,'keyword' as type, ANY(keyword_id) keyword_id, '' as target_id, ANY(keyword_text) keyword_text, ANY(match_type) match_type,'' as targeting_expression,'' as targeting_type, ANY(ad_group_id) ad_group_id, ANY(ad_group_name) ad_group_name, ANY(campaign_id) campaign_id, ANY(campaign_name) campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ").append(this.getJdbcHelper().getTable());
        selectKeywordSql.append(this.getWhereSqlCountByKeyword(dto.getPuid(), dto, argsList));
        StringBuilder selectTargetSql = new StringBuilder("SELECT ANY(`marketplace_id`) marketplace_id, ANY(`query_id`) query_id, ANY(`query`) query, ANY(`query_cn`) query_cn,'target' as type,'' as keyword_id,  ANY(target_id) target_id,  '' as keyword_text,'' as match_type, ANY(targeting_expression) targeting_expression, ANY(targeting_type) targeting_type, ANY(ad_group_id) ad_group_id, ANY(ad_group_name) ad_group_name, ANY(campaign_id) campaign_id, ANY(campaign_name) campaign_name, ")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ods_t_cpc_query_targeting_report ");
        selectTargetSql.append(this.getWhereSqlCountByTarget(dto.getPuid(), dto, argsList));
        queryIdSql.append(selectKeywordSql);
        queryIdSql.append(" UNION ALL ");
        queryIdSql.append(selectTargetSql);
        queryIdSql.append(" ) c ");
        //aba排名参数联表
        if (dto.isQueryJoinSearchTermsRank()) {
            queryIdSql.append(" left join ods_t_week_search_terms_analysis a on lower(query) = a.search_term and c.marketplace_id = a.marketplace_id and a.start_date=? and a.marketplace_id=? ");
            argsList.add(dto.getLastWeekSearchTermsRankDate());
            argsList.add(dto.getMarketplaceId());

            if (dto.getSearchFrequencyRankMin() != null) {
                queryIdSql.append(" and ifnull(search_frequency_rank, 2147483647) >= ? ");
                argsList.add(dto.getSearchFrequencyRankMin());
            }
            if (dto.getSearchFrequencyRankMax() != null) {
                queryIdSql.append(" and ifnull(search_frequency_rank, 2147483647) <= ? ");
                argsList.add(dto.getSearchFrequencyRankMax());
            }
            if (dto.getWeekRatioMin() != null) {
                queryIdSql.append(" and ifnull(round(week_ratio*100,2),-2147483648) >= ? ");
                argsList.add(dto.getWeekRatioMin());
            }
            if (dto.getWeekRatioMax() != null) {
                queryIdSql.append(" and ifnull(round(week_ratio*100,2),-2147483648) <= ? ");
                if (dto.getWeekRatioMin() == null) {
                    queryIdSql.append(" and ifnull(round(week_ratio*100,2),-2147483648) > -2147483648 ");
                }
                argsList.add(dto.getWeekRatioMax());
            }
            queryIdSql.append(" where 1=1 ");
            queryIdSql.append(" and search_frequency_rank is not null ");
            if (dto.getSearchFrequencyRankMin() != null) {
                queryIdSql.append(" and search_frequency_rank >= ?");
                argsList.add(dto.getSearchFrequencyRankMin());
            }
            if (dto.getSearchFrequencyRankMax() != null) {
                queryIdSql.append(" and search_frequency_rank <= ?");
                argsList.add(dto.getSearchFrequencyRankMax());
            }
            if (dto.getWeekRatioMin() != null) {
                queryIdSql.append(" and round(week_ratio*100,2) >= ?");
                argsList.add(dto.getWeekRatioMin());
            }
            if (dto.getWeekRatioMax() != null) {
                queryIdSql.append(" and round(week_ratio*100,2) <= ?");
                argsList.add(dto.getWeekRatioMax());
            }
        }

        sqlSb.append(" and query_id in (").append(queryIdSql).append(") ");
        sqlSb.append(" group by word_root order by count desc ");
        if (dto.getTop() != null) {
            sqlSb.append(" limit ").append(dto.getTop().toString());
        }
        return getJdbcTemplate().query(sqlSb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordRootTopVo.class));
    }

    /**
     * 搜索词页面查询关键词where拼接
     */
    private String getWhereSqlCountByKeyword(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" where puid = ? and shop_id = ? and count_day >= ? and count_day <= ? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        whereSql.append(" and query not REGEXP '" + ASIN_REGEX + "' ");
        /**
         *   根据匹配类型（matchType）查对应表的数据
         *   matchType in ('close-match','loose-match')紧密匹配，宽泛匹配 不查询该表数据
         *   matchType in ('broad','phrase','exact')'广泛匹配','词组匹配','精准匹配' 查询该表数据
         *   matchTypes：符合查询该表数据的条件('广泛匹配','词组匹配','精准匹配')数组
         *   MatchValueEnum值是('广泛匹配','词组匹配','精准匹配')的枚举
         */
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(), StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        //不带匹配类型条件查询不走下面逻辑
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isNotBlank(MatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            //如果匹配条件为(紧密匹配，宽泛匹配),则不查询数据
            //把matchTypes作为条件查不出数据
            if (CollectionUtils.isEmpty(matchTypes)) {
                whereSql.append(" group by query_id having 1=0 ");
                return whereSql.toString();
            }
        }
        if (CollectionUtils.isNotEmpty(matchTypes)) {
            whereSql.append(SqlStringUtil.dealInList("lower(match_type)", matchTypes, argsList));
        }
        if (StringUtils.isNotBlank(dto.getCampaignId())) {
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", list, argsList));

        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(dto.getGroupId())) {
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", list, argsList));
        }
        // 广告策略筛选
        if(CollectionUtils.isNotEmpty(dto.getAdStrategyTypeList())){
            String sql = AdQueryStrategyTypeEnum.getSql(dto.getAdStrategyTypeList(), dto.getAutoRuleIds(),dto.getAutoRuleGroupIds(), argsList, "query_id","ad_group_id");
            if(StringUtils.isNotEmpty(sql)){
                if(!dto.getAdStrategyTypeList().contains(AdQueryStrategyTypeEnum.NONE.getCode())){
                    whereSql.append(sql);
                    whereSql.append(" and lower(match_type) != 'theme' ");
                }else{
                    whereSql.append(" and ( ");
                    // 去掉第一个and
                    sql = sql.replaceFirst("and", "");
                    whereSql.append(sql);
                    whereSql.append(" or lower(match_type) = 'theme' )");
                }
            }
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryKeywordReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                //将搜索词转小写和数据库中函数小写匹配
                field = "lower(" + field + ")";
                dto.setSearchValue(dto.getSearchValue().toLowerCase());
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    whereSql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    if (dto.getListSearchValue().size() > 1) {
                        whereSql.append(SqlStringUtil.dealInList(field, dto.getListSearchValue(), argsList));
                    } else {
                        whereSql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getSearchValue().trim());
                    }
                }
            }
        }
        //标签
        if (CollectionUtils.isNotEmpty(dto.getQueryWordTagTypeList())) {
            if (dto.getQueryWordTagTypeList().contains(Constants.QUERY_NOT_TARGET)) {

                if (dto.getQueryWordTagTypeList().size() > 1) {
                    List<String> allSearchField = CpcQueryWordDto.QueryWordTagTypeEnum.getAllSearchField();
                    allSearchField.removeAll(dto.getQueryWordTagTypeList());
                    if (CollectionUtils.isNotEmpty(allSearchField)) {
                        whereSql.append(" AND concat_ws(',', ad_group_id, query) not in ")
                                .append(" ( ")
                                .append(SearchQueryTagSqlHelper.getKeywordSearchQueryTagInSql(dto.getPuid(), dto, argsList, allSearchField))
                                .append(" ) ");
                    }
                } else {
                    whereSql.append(" and concat_ws(',', ad_group_id, query) not in ")
                            .append(" ( ")
                            .append(SearchQueryTagSqlHelper.getKeywordSearchQueryTagInSql(dto.getPuid(), dto, argsList, CpcQueryWordDto.QueryWordTagTypeEnum.getAllSearchField()))
                            .append(" ) ");
                }
            } else {
                whereSql.append(" and concat_ws(',', ad_group_id, query) in ")
                        .append(" ( ")
                        .append(SearchQueryTagSqlHelper.getKeywordSearchQueryTagInSql(puid, dto, argsList, dto.getQueryWordTagTypeList()))
                        .append(" ) ");
            }
        }
        //词根
        if (StringUtils.isNotBlank(dto.getWordRoot())) {
            whereSql.append(" and query_id in ( ")
                    .append(this.getWordRootQueryIdSqlByQuery(puid, dto, argsList, WordRoot.QueryType.SP_QUERY.getType()))
                    .append(" ) ");
        }
        whereSql.append(" group by query_id  ");
        whereSql.append(this.getHavingSql(dto, argsList));

        return whereSql.toString();
    }

    /**
     * 搜索词页面查询投放where拼接
     */
    private String getWhereSqlCountByTarget(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" where puid = ? and shop_id = ? and count_day >= ? and count_day <= ? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        whereSql.append(" and query not REGEXP '" + ASIN_REGEX + "' ");
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(), StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        /**
         *   根据匹配类型（matchType）查对应表的数据
         *   matchType in ('close-match','loose-match')'广泛匹配','词组匹配','精准匹配' 不查询该表数据
         *   matchType in ('broad','phrase','exact')'紧密匹配'，'宽泛匹配'查询该表数据
         *   matchTypes：符合查询该表数据的条件('紧密匹配'，'宽泛匹配')数组
         *   MatchValueEnum值是('广泛匹配','词组匹配','精准匹配')的枚举
         */
        //不带匹配类型条件查询不走下面逻辑
        // start
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                matchType = matchType.replace("=", "");
                if (StringUtils.isNotBlank(TargetMatchTypeEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            if (CollectionUtils.isEmpty(matchTypes)) {
                whereSql.append(" group by query_id having 1=0 ");
                return whereSql.toString();
            } else {
                whereSql.append(SqlStringUtil.dealInList("lower(match_type)", matchTypes, argsList));
            }
        }
        //end
        if (StringUtils.isNotBlank(dto.getCampaignId())) {
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(dto.getGroupId())) {
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", list, argsList));
        }
        // 广告策略筛选
        if(CollectionUtils.isNotEmpty(dto.getAdStrategyTypeList())){
            String sql = AdQueryStrategyTypeEnum.getSql(dto.getAdStrategyTypeList(), dto.getAutoRuleIds(),dto.getAutoRuleGroupIds(), argsList, "query_id","ad_group_id");
            if(StringUtils.isNotEmpty(sql)){
                if(!dto.getAdStrategyTypeList().contains(AdQueryStrategyTypeEnum.NONE.getCode())){
                    whereSql.append(sql);
                    whereSql.append(" and lower(match_type) != 'theme' ");
                }else{
                    whereSql.append(" and ( ");
                    // 去掉第一个and
                    sql = sql.replaceFirst("and", "");
                    whereSql.append(sql);
                    whereSql.append(" or lower(match_type) = 'theme' )");
                }
            }
        }
        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryTargetingReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                //将搜索词转小写和数据库中函数小写匹配
                field = "lower(" + field + ")";
                dto.setSearchValue(dto.getSearchValue().toLowerCase());
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    whereSql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    if (dto.getListSearchValue().size() > 1) {
                        whereSql.append(SqlStringUtil.dealInList(field, dto.getListSearchValue(), argsList));
                    } else {
                        whereSql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getSearchValue().trim());
                    }
                }
            } else if ("keywordText".equalsIgnoreCase(dto.getSearchField())) {
                if (!dto.getSearchValue().equalsIgnoreCase("自动投放组")) {
                    whereSql.append(" and targeting_text = ? ");  // 需要查不出这个表的数据
                    argsList.add(dto.getSearchValue());
                }
            }
        }
        //标签
        if (CollectionUtils.isNotEmpty(dto.getQueryWordTagTypeList())) {

            if (dto.getQueryWordTagTypeList().contains(Constants.QUERY_NOT_TARGET)) {


                if (dto.getQueryWordTagTypeList().size() > 1) {
                    List<String> allSearchField = CpcQueryWordDto.QueryWordTagTypeEnum.getAllSearchField();
                    allSearchField.removeAll(dto.getQueryWordTagTypeList());
                    if (CollectionUtils.isNotEmpty(allSearchField)) {
                        whereSql.append(" AND concat_ws(',', ad_group_id, query) not in ")
                                .append(" ( ")
                                .append(SearchQueryTagSqlHelper.getKeywordSearchQueryTagInSql(dto.getPuid(), dto, argsList, allSearchField))
                                .append(" ) ");
                    }
                } else {
                    whereSql.append(" and concat_ws(',', ad_group_id, query) not in ")
                            .append(" ( ")
                            .append(SearchQueryTagSqlHelper.getKeywordSearchQueryTagInSql(dto.getPuid(), dto, argsList, CpcQueryWordDto.QueryWordTagTypeEnum.getAllSearchField()))
                            .append(" ) ");
                }
            } else {

                whereSql.append(" and concat_ws(',', ad_group_id, query) in ")
                        .append(" ( ")
                        .append(SearchQueryTagSqlHelper.getKeywordSearchQueryTagInSql(puid, dto, argsList, dto.getQueryWordTagTypeList()))
                        .append(" ) ");
            }
        }
        //词根
        if (StringUtils.isNotBlank(dto.getWordRoot())) {
            whereSql.append(" and query_id in ( ")
                    .append(this.getWordRootQueryIdSqlByQuery(puid, dto, argsList, WordRoot.QueryType.SP_TARGETING.getType()))
                    .append(" ) ");
        }
        whereSql.append(" group by query_id ");
        whereSql.append(this.getHavingSql(dto, argsList));
        return whereSql.toString();
    }

    private String getHavingSql(CpcQueryWordDto dto, List<Object> argsList) {
        if (!dto.getUseAdvanced()) {
            return "";
        }
        StringBuilder havingSql = new StringBuilder();
        BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);
        havingSql.append(" having 1=1 ");
        //展示量
        if (dto.getImpressionsMin() != null) {
            havingSql.append(" and SUM(impressions) >= ?");
            argsList.add(dto.getImpressionsMin());
        }
        if (dto.getImpressionsMax() != null) {
            havingSql.append(" and SUM(impressions) <= ?");
            argsList.add(dto.getImpressionsMax());
        }
        //点击量
        if (dto.getClicksMin() != null) {
            havingSql.append(" and SUM(clicks) >= ?");
            argsList.add(dto.getClicksMin());
        }
        if (dto.getClicksMax() != null) {
            havingSql.append(" and SUM(clicks) <= ?");
            argsList.add(dto.getClicksMax());
        }
        //点击率（clicks/impressions）
        if (dto.getClickRateMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(clicks)/SUM(impressions),0),4) >= ?");
            argsList.add(dto.getClickRateMin());
        }
        if (dto.getClickRateMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(clicks)/SUM(impressions),0),4) <= ?");
            argsList.add(dto.getClickRateMax());
        }
        //花费
        if (dto.getCostMin() != null) {
            havingSql.append(" and SUM(cost) >= ?");
            argsList.add(dto.getCostMin());
        }
        if (dto.getCostMax() != null) {
            havingSql.append(" and SUM(cost) <= ?");
            argsList.add(dto.getCostMax());
        }
        //cpc  平均点击费用
        if (dto.getCpcMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(clicks),0),2) >= ?");
            argsList.add(dto.getCpcMin());
        }
        if (dto.getCpcMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(clicks),0),2) <= ?");
            argsList.add(dto.getCpcMax());
        }
        //广告订单量
        if (dto.getOrderNumMin() != null) {
            havingSql.append(" and SUM(sale_num) >= ?");
            argsList.add(dto.getOrderNumMin());
        }
        if (dto.getOrderNumMax() != null) {
            havingSql.append(" and SUM(sale_num) <= ?");
            argsList.add(dto.getOrderNumMax());
        }
        //广告销售额
        if (dto.getSalesMin() != null) {
            havingSql.append(" and SUM(total_sales) >= ?");
            argsList.add(dto.getSalesMin());
        }
        if (dto.getSalesMax() != null) {
            havingSql.append(" and SUM(total_sales) <= ?");
            argsList.add(dto.getSalesMax());
        }
        //订单转化率
        if (dto.getSalesConversionRateMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(sale_num)/SUM(clicks),0),4) >= ?");
            argsList.add(dto.getSalesConversionRateMin());
        }
        if (dto.getSalesConversionRateMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(sale_num)/SUM(clicks),0),4) <= ?");
            argsList.add(dto.getSalesConversionRateMax());
        }
        //acos
        if (dto.getAcosMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(total_sales),0),4) >= ?");
            argsList.add(dto.getAcosMin());
        }
        if (dto.getAcosMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(total_sales),0),4) <= ?");
            argsList.add(dto.getAcosMax());
        }
        // roas
        if (dto.getRoasMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(total_sales)/SUM(cost),0),2) >= ?");
            argsList.add(dto.getRoasMin());
        }
        // roas
        if (dto.getRoasMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(total_sales)/SUM(cost),0),2) <= ?");
            argsList.add(dto.getRoasMax());
        }
        // acots  需要乘以店铺销售额
        if (dto.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                havingSql.append(" and ROUND((ifnull(SUM(cost),0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(dto.getAcotsMin());
            } else {
                havingSql.append(" and 0 >= ? ");
                argsList.add(dto.getAcotsMin());
            }
        }
        // acots  需要乘以店铺销售额
        if (dto.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                havingSql.append(" and ROUND((ifnull(SUM(cost),0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(dto.getAcotsMax());
            } else {
                havingSql.append(" and 0 <= ? ");
                argsList.add(dto.getAcotsMin());
            }
        }
        // asots 需要乘以店铺销售额
        if (dto.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                havingSql.append(" and ROUND((ifnull(SUM(total_sales),0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(dto.getAsotsMin());
            } else {
                havingSql.append(" and 0 >= ? ");
                argsList.add(dto.getAcotsMin());
            }
        }
        // asots  需要乘以店铺销售额
        if (dto.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                havingSql.append(" and ROUND((ifnull(SUM(total_sales),0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(dto.getAsotsMax());
            } else {
                havingSql.append(" and 0 <= ? ");
                argsList.add(dto.getAcotsMin());
            }
        }
        //CPA
        if (dto.getCpaMin() != null) {
            havingSql.append(" and ROUND(ROUND(ifnull(SUM(cost)/SUM(sale_num),0), 4), 2) >= ?");
            argsList.add(dto.getCpaMin());
        }
        if (dto.getCpaMax() != null) {
            havingSql.append(" and ROUND(ROUND(ifnull(SUM(cost)/SUM(sale_num),0), 4), 2) <= ?");
            argsList.add(dto.getCpaMax());
        }

        //本广告产品订单量
        if (dto.getAdSaleNumMin() != null) {
            havingSql.append(" and ifnull(SUM(ad_sale_num), 0) >= ?");
            argsList.add(dto.getAdSaleNumMin());
        }
        if (dto.getAdSaleNumMax() != null) {
            havingSql.append(" and ifnull(SUM(ad_sale_num), 0) <= ?");
            argsList.add(dto.getAdSaleNumMax());
        }
        //其他产品广告订单量
        if (dto.getAdOtherOrderNumMin() != null) {
            havingSql.append(" and ifnull(SUM(sale_num) - SUM(ad_sale_num), 0) >= ?");
            argsList.add(dto.getAdOtherOrderNumMin());
        }
        if (dto.getAdOtherOrderNumMax() != null) {
            havingSql.append(" and ifnull(SUM(sale_num) - SUM(ad_sale_num), 0) <= ?");
            argsList.add(dto.getAdOtherOrderNumMax());
        }
        //本广告产品销售额
        if (dto.getAdSalesMin() != null) {
            havingSql.append(" and ifnull(SUM(ad_sales), 0) >= ?");
            argsList.add(dto.getAdSalesMin());
        }
        if (dto.getAdSalesMax() != null) {
            havingSql.append(" and ifnull(SUM(ad_sales), 0) <= ?");
            argsList.add(dto.getAdSalesMax());
        }
        //其他产品广告销售额
        if (dto.getAdOtherSalesMin() != null) {
            havingSql.append(" and ifnull(SUM(total_sales) - SUM(ad_sales), 0) >= ?");
            argsList.add(dto.getAdOtherSalesMin());
        }
        if (dto.getAdOtherSalesMax() != null) {
            havingSql.append(" and ifnull(SUM(total_sales) - SUM(ad_sales), 0) <= ?");
            argsList.add(dto.getAdOtherSalesMax());
        }
        //广告销量
        if (dto.getAdSalesTotalMin() != null) {
            havingSql.append(" and ifnull(SUM(order_num), 0) >= ?");
            argsList.add(dto.getAdSalesTotalMin());
        }
        if (dto.getAdSalesTotalMax() != null) {
            havingSql.append(" and ifnull(SUM(order_num), 0) <= ?");
            argsList.add(dto.getAdSalesTotalMax());
        }
        //本广告产品销量
        if (dto.getAdSelfSaleNumMin() != null) {
            havingSql.append(" and ifnull(SUM(ad_order_num), 0) >= ?");
            argsList.add(dto.getAdSelfSaleNumMin());
        }
        if (dto.getAdSelfSaleNumMax() != null) {
            havingSql.append(" and ifnull(SUM(ad_order_num), 0) <= ?");
            argsList.add(dto.getAdSelfSaleNumMax());
        }
        //其他产品广告销量
        if (dto.getAdOtherSaleNumMin() != null) {
            havingSql.append(" and ifnull(SUM(order_num) - SUM(ad_order_num), 0) >= ?");
            argsList.add(dto.getAdOtherSaleNumMin());
        }
        if (dto.getAdOtherSaleNumMax() != null) {
            havingSql.append(" and ifnull(SUM(order_num) - SUM(ad_order_num), 0) <= ?");
            argsList.add(dto.getAdOtherSaleNumMax());
        }

        // 广告笔单价(广告销售额÷广告订单量×100%)
        if (dto.getAdvertisingUnitPriceMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(total_sales)/SUM(sale_num), 0), 2) >= ?");
            argsList.add(dto.getAdvertisingUnitPriceMin());
        }
        if (dto.getAdvertisingUnitPriceMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(total_sales)/SUM(sale_num), 0), 2) <= ?");
            argsList.add(dto.getAdvertisingUnitPriceMax());
        }

        return havingSql.toString();
    }

    private String getWordRootQueryIdSqlByQuery(int puid, CpcQueryWordDto dto, List<Object> argsList, Integer wordRootQueryType) {
        StringBuilder sb = new StringBuilder("select distinct query_id from ods_t_amazon_word_root_query ");
        sb.append(" where puid = ? and count_day >= ? and count_day <= ? ");
        argsList.add(puid);
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());

        List<Integer> shopIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dto.getShopIdList())){
            shopIds.addAll(dto.getShopIdList());
        }
        if (Objects.nonNull(dto.getShopId())){
            shopIds.add(dto.getShopId());
        }
        sb.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));

        sb.append(" and query_type = ? ");
        argsList.add(wordRootQueryType);

        //词根
        if (StringUtils.isNotBlank(dto.getWordRoot())) {
            sb.append(" and word_root = ? ");
            argsList.add(dto.getWordRoot());
        }
        return sb.toString();
    }

    private String getWordRootQuerySqlByQuery(int puid, CpcQueryWordDto dto, List<Object> argsList, Integer wordRootQueryType) {
        StringBuilder sb = new StringBuilder("select distinct query from ods_t_amazon_word_root_query ");
        sb.append(" where puid=? and marketplace_id=? and count_day >= ? and count_day <= ? ");
        argsList.add(puid);
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());

        List<Integer> shopIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dto.getShopIdList())){
            shopIds.addAll(dto.getShopIdList());
        }
        if (Objects.nonNull(dto.getShopId())){
            shopIds.add(dto.getShopId());
        }
        sb.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));

        sb.append(" and query_type = ? ");
        argsList.add(wordRootQueryType);

        //词根
        if (StringUtils.isNotBlank(dto.getWordRoot())) {
            sb.append(" and word_root = ? ");
            argsList.add(dto.getWordRoot());
        }
        return sb.toString();
    }


    /**
     * 用户排除为0 的字段处理
     * @param orderByField
     * @return
     */
    private String getColumn(String orderByField) {

        switch (orderByField) {
            case "totalSales":
                return "total_sales";
            case "orderNum":
                return "sale_num";
            case "saleNum":
                return "order_num";
            default:
                return orderByField ;
        }
    }




    @Override
    public Page<DashboardAdQueryWordDataPageDto> queryAdQueryWordPage(Integer puid, DashboardQueryWordReqVo vo) {
        // 聚合sb用户搜索词和sp用户搜索词的查询结果
        DashboardDataFieldEnum dashboardDataFieldEnum = DashboardDataFieldEnum.fieldMap.get(vo.getOrderByField());
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(vo.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(vo.getMarketplaceIdList());
        }


        String queryWordSpQuerySql = buildQueryAdQueryWordPageSql(puid, vo.getMarketplaceIdList(), vo.getShopIdList(),
                vo.getStartDate(), vo.getEndDate(), argsList,
                vo.getQueryWord(), siteToday, vo.getSiteToday(), vo.getPortfolioIds(), vo.getCampaignIds(), vo.getNoZero(),dashboardDataFieldEnum, vo.getMatchType());
        String queryWordSpTargetingQuerySql = odsCpcQueryTargetingReportDao.buildQueryAdQueryWordPageSql(puid, vo.getMarketplaceIdList(), vo.getShopIdList(),
                vo.getStartDate(), vo.getEndDate(), argsList,
                vo.getQueryWord(), siteToday, vo.getSiteToday(), vo.getPortfolioIds(), vo.getCampaignIds(), vo.getNoZero(),dashboardDataFieldEnum, vo.getMatchType());
        String queryWordSbQuerySql = odsCpcSbQueryKeywordReportDao.buildQueryAdQueryWordPageSql(puid, vo.getMarketplaceIdList(), vo.getShopIdList(),
                vo.getStartDate(), vo.getEndDate(), argsList,
                vo.getQueryWord(), siteToday, vo.getSiteToday(), vo.getPortfolioIds(), vo.getCampaignIds(), vo.getNoZero(),dashboardDataFieldEnum, vo.getMatchType());
        String unionAllSql = String.join(" union all ", queryWordSpQuerySql, queryWordSpTargetingQuerySql, queryWordSbQuerySql);

        String countSql = " select count(*)  from ( " + unionAllSql + " ) r";
        String selectSql = " select * from ( " + unionAllSql + " ) r order by keywordId ";
        Object[] arg = argsList.toArray();
        return getPageResultByClass(vo.getPageNo(), vo.getPageSize(), countSql, arg, selectSql, arg, DashboardAdQueryWordDataPageDto.class);
    }


    private String buildQueryAdQueryWordPageSql(Integer puid,
                                                List<String> marketplaceIdList,
                                                List<Integer> shopIdList,
                                                String startDate,
                                                String endDate,
                                                List<Object> argsList,
                                                String queryWord, List<String> siteToday, Boolean isSiteToday,
                                                List<String> portfolioIds, List<String> campaignIds,
                                                Boolean noZero,
                                                DashboardDataFieldEnum dashboardDataFieldEnum, String matchType) {

        StringBuilder sql = new StringBuilder();
        sql.append("select 'sp' adType, 'keyword' type, any(shop_id) shopId, any(keyword_id) keywordId, any(campaign_id) campaignId, any(ad_group_id) adGroupId, any(match_type) matchType, any(keyword_text) keywordText ");
        sql.append(" from ").append(getJdbcHelper().getTable());
        sql.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sql.append("and marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("shop_id", shopIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sql.append(SqlStringUtil.dealDorisInList("concat_ws('|', marketplace_id, count_day)", siteToday, argsList));
            sql.append(" and count_day >= ? and count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sql.append(" and count_day >= ? and count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealDorisInList("campaign_id", campaignIds, argsList));
        }

        sql.append(" and  query = ? ");

        argsList.add(queryWord);
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sql.append(" and ");
            sql.append(" campaign_id in ( ");
            sql.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = 'sp' ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sql.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sql.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append(" )  ");
                }
            } else {
                sql.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sql.append(" ) ");
        }
        if (StringUtils.isNotBlank(matchType)) {
            sql.append(" and LOWER(match_type) = ? ");
            argsList.add(matchType.toLowerCase());
        }

        if (Boolean.TRUE.equals(noZero) && DashboardDataFieldEnum.noZeroFieldSet.contains(dashboardDataFieldEnum.getCode())) {
            sql.append(" and ");
            sql.append(" " + getColumn(dashboardDataFieldEnum.getCode()) + " <> 0 ");
        }
        sql.append(" group by keyword_id ");
        return sql.toString();
    }

    @Override
    public Page allSearchTermPageList(Integer puid, CpcQueryWordDto dto, Page page) {
        List<Object> args = new ArrayList<>();

        List<String> sqlList = Lists.newArrayList();
        getSpKeywordSearchTermSql(puid, dto, args, sqlList);
        getSpTargetSearchTermSql(puid, dto, args, sqlList);
        getSbSearchTermSql(puid, dto, args, sqlList);
        if (CollectionUtils.isEmpty(sqlList)) {
            return page;
        }

        String allSearchTermSql = "SELECT" +
                " query , any(`marketplace_id`) marketplace_id, any(query_cn) query_cn, any(main_image) main_image, ANY(targeting_expression) targeting_expression," +
                " GROUP_CONCAT(distinct adType,',') adType, GROUP_CONCAT(idStr,',') idStr," +
                " sum(CAST(occurrence_num AS INT)) occurrence_num, SUM(impressions) impressions, SUM(clicks) clicks, SUM(cost) cost," +
                " SUM(sale_num) sale_num, SUM(total_sales) total_sales, SUM(ad_sales) `ad_sales`, SUM(ad_other_sales) `ad_other_sales`" +
                " FROM ( " + StringUtils.join(sqlList, " union all ") + " ) p group by query " + this.getHavingSql(dto, args);

        StringBuilder sql = new StringBuilder("select ")
                .append(" t.query query, t.query_cn query_cn, t.marketplace_id marketplace_id, t.main_image main_image, t.targeting_expression,")
                .append(" t.adType adType, t.idStr,")
                .append(" t.occurrence_num occurrence_num, t.impressions impressions, t.clicks clicks, t.cost cost,")
                .append(" t.sale_num sale_num, t.total_sales total_sales, t.ad_sales ad_sales, t.ad_other_sales ad_other_sales");
        if (dto.isQueryJoinSearchTermsRank()) {
            sql.append(", ifnull(search_frequency_rank, 2147483647) search_frequency_rank ");
        }
        sql.append(" FROM ( ").append(allSearchTermSql).append(" ) t ");

        if (dto.isQueryJoinSearchTermsRank()) {
            boolean isFilterAbaRank = dto.getUseAdvanced() && (dto.getSearchFrequencyRankMin() != null || dto.getSearchFrequencyRankMax() != null);
            sql.append(" left join ods_t_week_search_terms_analysis a on lower(query) = a.search_term and t.marketplace_id = a.marketplace_id and a.start_date=? and a.marketplace_id=? ");
            args.add(dto.getLastWeekSearchTermsRankDate());
            args.add(dto.getMarketplaceId());

            if (isFilterAbaRank) {
                if (dto.getSearchFrequencyRankMin() != null) {
                    sql.append(" and ifnull(search_frequency_rank, 2147483647) >= ? ");
                    args.add(dto.getSearchFrequencyRankMin());
                }
                if (dto.getSearchFrequencyRankMax() != null) {
                    sql.append(" and ifnull(search_frequency_rank, 2147483647) <= ? ");
                    args.add(dto.getSearchFrequencyRankMax());
                }
                sql.append(" where search_frequency_rank is not null ");
                if (dto.getSearchFrequencyRankMin() != null) {
                    sql.append(" and search_frequency_rank >= ? ");
                    args.add(dto.getSearchFrequencyRankMin());
                }
                if (dto.getSearchFrequencyRankMax() != null) {
                    sql.append(" and search_frequency_rank <= ? ");
                    args.add(dto.getSearchFrequencyRankMax());
                }
            }
        }

        String countSql = "select count(*) from ( " + sql + ") c ";

        if (StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderType())) {
            String orderField = ALL_SEARCH_TERM_ORDER_FIELD_MAP.get(dto.getOrderField());
            if (StringUtils.equalsIgnoreCase("search_frequency_rank", orderField) && !dto.isQueryJoinSearchTermsRank()) {
                orderField = "";
            }
            if (StringUtils.isNotBlank(orderField)) {
                sql.append(" order by ").append(orderField);
                if ("desc".equals(dto.getOrderType())) {
                    sql.append(" desc");
                }
            } else {
                sql.append(" order by occurrence_num desc, sale_num desc ");
            }
        } else {
            sql.append(" order by occurrence_num desc, sale_num desc ");
        }

        log.info(" chTest allSearchTermPageList sql {}", SqlStringUtil.exactSql(sql.toString(), args));

        return getPageByMapper(page.getPageNo(), page.getPageSize(), countSql, args.toArray(),
                sql.toString(), args.toArray(), (rs, rowNum) -> {
                    SearchTermBO.SearchTermBOBuilder builder = SearchTermBO.builder()
                            .marketplaceId(rs.getString("marketplace_id"))
                            .adType(rs.getString("adType"))
                            .query(rs.getString("query"))
                            .queryCn(rs.getString("query_cn"))
                            .idStr(rs.getString("idStr"))
                            .mainImage(rs.getString("main_image"))
                            .targetingExpression(rs.getString("targeting_expression"))
                            .occurrenceNum(rs.getInt("occurrence_num"))
                            .impressions(rs.getInt("impressions"))
                            .clicks(rs.getInt("clicks"))
                            .cost(rs.getBigDecimal("cost") != null ? rs.getBigDecimal("cost") : BigDecimal.ZERO)
                            .saleNum(rs.getInt("sale_num"))
                            .totalSales(rs.getBigDecimal("total_sales") != null ? rs.getBigDecimal("total_sales") : BigDecimal.ZERO)
                            .adSales(Optional.ofNullable(rs.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            .adOtherSales(Optional.ofNullable(rs.getBigDecimal("ad_other_sales")).orElse(BigDecimal.ZERO));
                    if (dto.isQueryJoinSearchTermsRank()) {
                        builder.searchFrequencyRank(rs.getInt("search_frequency_rank"));
                    }
                    return builder.build();
                });
    }

    @Override
    public List<SearchTermBO> searchTermSourceTargetDetail(CpcQueryWordDto dto) {
        List<Object> args = Lists.newArrayList();

        StringBuilder spKeywordSql = new StringBuilder("select ")
                .append(" keyword_id search_id, any(query) query, any(query_cn) query_cn, 'sp' adType, 'keyword' type,")
                .append(" any(match_type) match_type, '' targeting_expression, any(keyword_text) keyword_text, '' as targeting_type,")
                .append(" any(ad_group_id) ad_group_id, any(campaign_id) campaign_id,")
                .append(" SUM(impressions) impressions, SUM(clicks) clicks, SUM(cost) cost, SUM(sale_num) sale_num,")
                .append(" SUM(total_sales) total_sales, SUM(ad_sales) `ad_sales`, SUM(total_sales) - SUM(ad_sales) ad_other_sales")
                .append(" from ods_t_cpc_query_keyword_report ")
                .append(" where puid=? and marketplace_id=? and lower(query)=? and count_day >= ? and count_day <= ? ");
        args.add(dto.getPuid());
        args.add(dto.getMarketplaceId());
        args.add(dto.getQuery().toLowerCase());
        args.add(dto.getStart());
        args.add(dto.getEnd());
        spKeywordSql.append(SqlStringUtil.dealInList("keyword_id", dto.getSearchTermIds(), args))
                .append(SqlStringUtil.dealInList("shop_id", dto.getShopIdList(), args))
                .append(" group by keyword_id ");

        StringBuilder spTargetSql = new StringBuilder("select ")
                .append(" target_id search_id, any(query) query, any(query_cn) query_cn, 'sp' adType, 'target' type,")
                .append(" any(match_type) match_type, any(targeting_expression) targeting_expression, '' keyword_text, any(targeting_type) targeting_type,")
                .append(" any(ad_group_id) ad_group_id, any(campaign_id) campaign_id,")
                .append(" SUM(impressions) impressions, SUM(clicks) clicks, SUM(cost) cost, SUM(sale_num) sale_num,")
                .append(" SUM(total_sales) total_sales, SUM(ad_sales) `ad_sales`, SUM(total_sales) - SUM(ad_sales) ad_other_sales")
                .append(" from ods_t_cpc_query_targeting_report ")
                .append(" where puid=? and marketplace_id=? and lower(query)=? and count_day >= ? and count_day <= ? ");
        args.add(dto.getPuid());
        args.add(dto.getMarketplaceId());
        args.add(dto.getQuery().toLowerCase());
        args.add(dto.getStart());
        args.add(dto.getEnd());
        spTargetSql.append(SqlStringUtil.dealInList("target_id", dto.getSearchTermIds(), args))
                .append(SqlStringUtil.dealInList("shop_id", dto.getShopIdList(), args))
                .append(" group by target_id ");

        StringBuilder sbKeywordSql = new StringBuilder("select ")
                .append(" keyword_id search_id, any(query) query, any(query_cn) query_cn, 'sb' adType, 'keyword' type,")
                .append(" any(match_type) match_type, '' targeting_expression, any(keyword_text) keyword_text, '' as targeting_type,")
                .append(" any(ad_group_id) ad_group_id, any(campaign_id) campaign_id,")
                .append(" sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`cost`) cost, sum(`conversions14d`) sale_num,")
                .append(" sum(`sales14d`) total_sales, 0 ad_sales, 0 ad_other_sales")
                .append(" from ods_t_cpc_sb_query_keyword_report ")
                .append(" where puid=? and marketplace_id=? and lower(query)=? and count_day >= ? and count_day <= ? ");
        args.add(dto.getPuid());
        args.add(dto.getMarketplaceId());
        args.add(dto.getQuery().toLowerCase());
        args.add(dto.getStart());
        args.add(dto.getEnd());
        sbKeywordSql.append(SqlStringUtil.dealInList("keyword_id", dto.getSearchTermIds(), args))
                .append(SqlStringUtil.dealInList("shop_id", dto.getShopIdList(), args))
                .append(" group by keyword_id ");

        StringBuilder sql = new StringBuilder("select ")
                .append(" t.search_id search_id, t.query query, t.query_cn query_cn, t.adType adType, t.type type,")
                .append(" t.match_type match_type, t.targeting_expression targeting_expression, t.keyword_text keyword_text,")
                .append(" t.targeting_type targeting_type, t.ad_group_id ad_group_id, t.campaign_id campaign_id,")
                .append(" t.impressions impressions, t.clicks clicks, t.cost cost, t.sale_num sale_num,")
                .append(" t.total_sales total_sales, t.ad_sales ad_sales, t.ad_other_sales ad_other_sales")
                .append(" from (")
                .append(String.join(" union all ", spKeywordSql.toString(), spTargetSql.toString(), sbKeywordSql.toString()))
                .append(") t");

        log.info("searchTermSourceTargetDetail sql {}", SqlStringUtil.exactSql(sql.toString(), args));

        return getJdbcTemplate().query(sql.toString(), (rs, i) -> SearchTermBO.builder()
                        .query(rs.getString("query"))
                        .queryCn(rs.getString("query_cn"))
                        .adType(rs.getString("adType"))
                        .type(rs.getString("type"))
                        .matchType(rs.getString("match_type"))
                        .targetingExpression(rs.getString("targeting_expression"))
                        .keywordText(rs.getString("keyword_text"))
                        .targetingType(rs.getString("targeting_type"))
                        .adGroupId(rs.getString("ad_group_id"))
                        .campaignId(rs.getString("campaign_id"))
                        .impressions(rs.getInt("impressions"))
                        .clicks(rs.getInt("clicks"))
                        .cost(rs.getBigDecimal("cost") != null ? rs.getBigDecimal("cost") : BigDecimal.ZERO)
                        .saleNum(rs.getInt("sale_num"))
                        .totalSales(rs.getBigDecimal("total_sales") != null ? rs.getBigDecimal("total_sales") : BigDecimal.ZERO)
                        .adSales(Optional.ofNullable(rs.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        .adOtherSales(Optional.ofNullable(rs.getBigDecimal("ad_other_sales")).orElse(BigDecimal.ZERO))
                        .searchId(rs.getString("search_id"))
                        .build(),
                args.toArray());
    }

    private void getSpKeywordSearchTermSql(Integer puid, CpcQueryWordDto dto, List<Object> args, List<String> sqlList) {
        //keyword搜索词表
        if (StringUtils.equalsIgnoreCase(dto.getSearchField(), CpcQueryWordDto.SearchFieldEnum.TARGETING_EXPRESSION.getValue())
                && StringUtils.isNotBlank(dto.getSearchValue())) {
            return;
        }
        // 选择了类型查询，但是没有选sp的
        if (CollectionUtils.isNotEmpty(dto.getSpMatchTypes()) || CollectionUtils.isNotEmpty(dto.getSbMatchTypes())) {
            if (CollectionUtils.isEmpty(dto.getSpMatchTypes())) {
                return;
            }
        }
        List<String> matchTypeList = dto.getSpMatchTypes();
        List<String> matchTypes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isNotBlank(MatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            if (CollectionUtils.isEmpty(matchTypes)) {
                return;
            }
        }

        StringBuilder sql = new StringBuilder("SELECT")
                .append(" query, 'keyword' as type, 'sp' as adType, GROUP_CONCAT(distinct keyword_id,',') idStr,")
                .append(" ANY(`query_cn`) query_cn, ANY(kr.`marketplace_id`) marketplace_id,")
                .append(" count(distinct keyword_id) occurrence_num, '' main_image, '' targeting_expression,")
                .append(" SUM(impressions) impressions, SUM(clicks) clicks, SUM(cost) cost, SUM(sale_num) sale_num,")
                .append(" SUM(total_sales) total_sales, SUM(ad_sales) `ad_sales`, SUM(total_sales) - SUM(ad_sales) ad_other_sales")
                .append(" FROM ");
        sql.append(this.getJdbcHelper().getTable()).append(" kr ");
        sql.append(" where puid=? and marketplace_id=? and count_day>=? and count_day<=?");
        args.add(dto.getPuid());
        args.add(dto.getMarketplaceId());
        args.add(dto.getStart());
        args.add(dto.getEnd());
        if (StringUtils.isNotBlank(dto.getQuery())) {
            sql.append(" and query = ? ");
            args.add(dto.getQuery());
        }
        sql.append(SqlStringUtil.dealInList("shop_id", dto.getShopIdList(), args));
        if (StringUtils.equalsIgnoreCase(dto.getSearchTermType(), Constants.SEARCH_TERM_TYPE_KEYWORD)) {
            sql.append("  and query not REGEXP '" + ASIN_REGEX + "'  ");
        }
        if (StringUtils.equalsIgnoreCase(dto.getSearchTermType(), Constants.SEARCH_TERM_TYPE_ASIN)) {
            sql.append("  and query REGEXP '" + ASIN_REGEX + "'  ");
        }
        if (CollectionUtils.isNotEmpty(matchTypes)) {
            sql.append(SqlStringUtil.dealInList("lower(match_type)", matchTypes, args));
        }
        sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", dto.getGroupIdList(), args));
        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String searchValue = dto.getSearchValue();
            List<String> searchValueList = StringUtil.stringToList(searchValue, StringUtil.SPECIAL_COMMA);
            SpKeywordGroupValueEnum keywordGroupValueEnumByTextCn = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByTextCn(dto.getSearchValue());
            if (keywordGroupValueEnumByTextCn != null) {
                searchValue = keywordGroupValueEnumByTextCn.getKeywordText();
            }
            String field = SqlStringUtil.getSqlField(CpcQueryKeywordReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                field = "lower(" + field + ")";
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    sql.append(" and ").append(field).append(" like ?");
                    args.add("%" + SqlStringUtil.dealLikeSql(searchValue) + "%");
                } else {//默认精确
                    if (searchValueList.size() > 1) {
                        sql.append(SqlStringUtil.dealInList(field, searchValueList, args));
                    } else {
                        sql.append(" and ").append(field).append(" = ?");
                        args.add(searchValueList.get(0));
                    }
                }
            }
        }
        // 仅展示未投放 or 仅展示已投放
        if (StringUtils.equalsAny(dto.getQueryWordTargetType(), Constants.QUERY_TARGET, Constants.QUERY_NOT_TARGET)) {
            if (StringUtils.isNotBlank(dto.getSearchTermType())) {
                sql.append(" and concat_ws(',', ad_group_id, query) ");
                if (StringUtils.equals(dto.getQueryWordTargetType(), Constants.QUERY_NOT_TARGET)) {
                    sql.append("not");
                }
                sql.append(" in ( ");
                if (StringUtils.equalsIgnoreCase(dto.getSearchTermType(), Constants.SEARCH_TERM_TYPE_KEYWORD)) {
                    sql.append(SearchQueryTagSqlHelper.getKeywordSearchQueryTagInSql(dto.getPuid(), dto, args, CpcQueryWordDto.QueryWordTagTypeEnum.getKeywordSearchField()));
                } else if (StringUtils.equalsIgnoreCase(dto.getSearchTermType(), Constants.SEARCH_TERM_TYPE_ASIN)) {
                    sql.append(SearchQueryTagSqlHelper.getTargetSearchQueryTagInSql(dto.getPuid(), dto, args, CpcQueryWordDto.QueryWordTagTypeEnum.getTargetSearchField()));
                }
                sql.append(" ) ");
            } else {
                StringBuilder keywordSearchQueryTagSql = new StringBuilder(" concat_ws(',', ad_group_id, query) ");
                if (StringUtils.equals(dto.getQueryWordTargetType(), Constants.QUERY_NOT_TARGET)) {
                    keywordSearchQueryTagSql.append("not");
                }
                keywordSearchQueryTagSql.append(" in (")
                        .append(SearchQueryTagSqlHelper.getKeywordSearchQueryTagInSql(dto.getPuid(), dto, args, CpcQueryWordDto.QueryWordTagTypeEnum.getKeywordSearchField()))
                        .append(" ) ");

                StringBuilder targetSearchQueryTagSql = new StringBuilder(" concat_ws(',', ad_group_id, query) ");
                if (StringUtils.equals(dto.getQueryWordTargetType(), Constants.QUERY_NOT_TARGET)) {
                    targetSearchQueryTagSql.append("not");
                }
                targetSearchQueryTagSql.append(" in (")
                        .append(SearchQueryTagSqlHelper.getTargetSearchQueryTagInSql(dto.getPuid(), dto, args, CpcQueryWordDto.QueryWordTagTypeEnum.getTargetSearchField()))
                        .append(" ) ");

                sql.append(" and (");
                sql.append(keywordSearchQueryTagSql);
                if (StringUtils.equals(dto.getQueryWordTargetType(), Constants.QUERY_NOT_TARGET)) {
                    sql.append(" and ");
                }else {
                    sql.append(" or ");
                }
                sql.append(targetSearchQueryTagSql);
                sql.append(")");
            }
        }
        //词根
        if (StringUtils.isNotBlank(dto.getWordRoot())) {
            sql.append(" and query in ( ")
                    .append(this.getWordRootQuerySqlByQuery(puid, dto, args, WordRoot.QueryType.SP_QUERY.getType()))
                    .append(" ) ");
        }
        sql.append(" group by query ");
        sqlList.add(sql.toString());
    }

    private void getSpTargetSearchTermSql(Integer puid, CpcQueryWordDto dto, List<Object> args, List<String> sqlList) {
        //target搜索词表
        // 选择了类型查询，但是没有选sp的
        if (CollectionUtils.isNotEmpty(dto.getSpMatchTypes()) || CollectionUtils.isNotEmpty(dto.getSbMatchTypes())) {
            if (CollectionUtils.isEmpty(dto.getSpMatchTypes())) {
                return;
            }
        }
        List<String> matchTypeList = dto.getSpMatchTypes();
        List<String> matchTypes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                matchType = matchType.replace("=", "");
                if (StringUtils.isNotBlank(TargetMatchTypeEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            if (CollectionUtils.isEmpty(matchTypes)) {
                return;
            }
        }

        StringBuilder sql = new StringBuilder("SELECT")
                .append(" query, 'target' as type, 'sp' as adType, GROUP_CONCAT(distinct target_id,',') idStr,")
                .append(" ANY(`query_cn`) query_cn, ANY(tr.`marketplace_id`) marketplace_id,")
                .append(" count(distinct target_id) occurrence_num, ANY(main_image) main_image, ANY(targeting_expression) targeting_expression,")
                .append(" SUM(impressions) impressions, SUM(clicks) clicks, SUM(cost) cost, SUM(sale_num) sale_num,")
                .append(" SUM(total_sales) total_sales, SUM(ad_sales) `ad_sales`, SUM(total_sales) - SUM(ad_sales) ad_other_sales")
                .append(" FROM ods_t_cpc_query_targeting_report tr ")
                .append(" where puid=? and marketplace_id=? and count_day>=? and count_day<=?");
        args.add(dto.getPuid());
        args.add(dto.getMarketplaceId());
        args.add(dto.getStart());
        args.add(dto.getEnd());
        if (StringUtils.isNotBlank(dto.getQuery())) {
            sql.append(" and query = ? ");
            args.add(dto.getQuery());
        }
        sql.append(SqlStringUtil.dealInList("shop_id", dto.getShopIdList(), args));
        if (StringUtils.equalsIgnoreCase(dto.getSearchTermType(), Constants.SEARCH_TERM_TYPE_KEYWORD)) {
            sql.append("  and query not REGEXP '" + ASIN_REGEX + "'  ");
        }
        if (StringUtils.equalsIgnoreCase(dto.getSearchTermType(), Constants.SEARCH_TERM_TYPE_ASIN)) {
            sql.append("  and query REGEXP '" + ASIN_REGEX + "'  ");
        }

        if (CollectionUtils.isNotEmpty(matchTypes)) {
            sql.append(SqlStringUtil.dealInList("lower(match_type)", matchTypes, args));
        }

        sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", dto.getGroupIdList(), args));

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String searchValue = dto.getSearchValue();
            List<String> searchValueList = StringUtil.stringToList(searchValue, StringUtil.SPECIAL_COMMA);
            SpKeywordGroupValueEnum keywordGroupValueEnumByTextCn = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByTextCn(dto.getSearchValue());
            if (keywordGroupValueEnumByTextCn != null) {
                searchValue = keywordGroupValueEnumByTextCn.getKeywordText();
            }
            String field = SqlStringUtil.getSqlField(CpcQueryTargetingReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                //将搜索词转小写和数据库中函数小写匹配
                field = "lower(" + field + ")";
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    sql.append(" and ").append(field).append(" like ?");
                    args.add("%" + SqlStringUtil.dealLikeSql(searchValue) + "%");
                } else {//默认精确
                    if (searchValueList.size() > 1) {
                        sql.append(SqlStringUtil.dealInList(field, searchValueList, args));
                    } else {
                        sql.append(" and ").append(field).append(" = ?");
                        args.add(searchValueList.get(0));
                    }
                }
            } else if ("keywordText".equalsIgnoreCase(dto.getSearchField())) {
                if (!searchValue.equalsIgnoreCase("自动投放组")) {
                    sql.append(" and targeting_text = ? ");  // 需要查不出这个表的数据
                    args.add(searchValue);
                }
            }
        }
        // 仅展示未投放 or 仅展示已投放
        if (StringUtils.equalsAny(dto.getQueryWordTargetType(), Constants.QUERY_TARGET, Constants.QUERY_NOT_TARGET)) {
            if (StringUtils.isNotBlank(dto.getSearchTermType())) {
                sql.append(" and concat_ws(',', ad_group_id, query) ");
                if (StringUtils.equals(dto.getQueryWordTargetType(), Constants.QUERY_NOT_TARGET)) {
                    sql.append("not");
                }
                sql.append(" in ( ");
                if (StringUtils.equalsIgnoreCase(dto.getSearchTermType(), Constants.SEARCH_TERM_TYPE_KEYWORD)) {
                    sql.append(SearchQueryTagSqlHelper.getKeywordSearchQueryTagInSql(dto.getPuid(), dto, args, CpcQueryWordDto.QueryWordTagTypeEnum.getKeywordSearchField()));
                } else if (StringUtils.equalsIgnoreCase(dto.getSearchTermType(), Constants.SEARCH_TERM_TYPE_ASIN)) {
                    sql.append(SearchQueryTagSqlHelper.getTargetSearchQueryTagInSql(dto.getPuid(), dto, args, CpcQueryWordDto.QueryWordTagTypeEnum.getTargetSearchField()));
                }
                sql.append(" ) ");
            } else {
                StringBuilder keywordSearchQueryTagSql = new StringBuilder(" concat_ws(',', ad_group_id, query) ");
                if (StringUtils.equals(dto.getQueryWordTargetType(), Constants.QUERY_NOT_TARGET)) {
                    keywordSearchQueryTagSql.append("not");
                }
                keywordSearchQueryTagSql.append(" in (")
                        .append(SearchQueryTagSqlHelper.getKeywordSearchQueryTagInSql(dto.getPuid(), dto, args, CpcQueryWordDto.QueryWordTagTypeEnum.getKeywordSearchField()))
                        .append(" ) ");

                StringBuilder targetSearchQueryTagSql = new StringBuilder(" concat_ws(',', ad_group_id, query) ");
                if (StringUtils.equals(dto.getQueryWordTargetType(), Constants.QUERY_NOT_TARGET)) {
                    targetSearchQueryTagSql.append("not");
                }
                targetSearchQueryTagSql.append(" in (")
                        .append(SearchQueryTagSqlHelper.getTargetSearchQueryTagInSql(dto.getPuid(), dto, args, CpcQueryWordDto.QueryWordTagTypeEnum.getTargetSearchField()))
                        .append(" ) ");

                sql.append(" and (");
                sql.append(keywordSearchQueryTagSql);
                if (StringUtils.equals(dto.getQueryWordTargetType(), Constants.QUERY_NOT_TARGET)) {
                    sql.append(" and ");
                }else {
                    sql.append(" or ");
                }
                sql.append(targetSearchQueryTagSql);
                sql.append(")");
            }
        }
        //词根
        if (StringUtils.isNotBlank(dto.getWordRoot())) {
            sql.append(" and query in ( ")
                    .append(this.getWordRootQuerySqlByQuery(puid, dto, args, WordRoot.QueryType.SP_TARGETING.getType()))
                    .append(" ) ");
        }
        sql.append(" group by query ");
        sqlList.add(sql.toString());
    }

    private void getSbSearchTermSql(Integer puid, CpcQueryWordDto dto, List<Object> args, List<String> sqlList) {
        if (StringUtils.equalsIgnoreCase(dto.getSearchField(), CpcQueryWordDto.SearchFieldEnum.TARGETING_EXPRESSION.getValue())
                && StringUtils.isNotBlank(dto.getSearchValue())) {
            return;
        }
        // sb只有搜索词，没有asin 不能 REGEXP asin
        if (StringUtils.equalsIgnoreCase(dto.getSearchTermType(), Constants.SEARCH_TERM_TYPE_ASIN)) {
            return;
        }
        // 选择了类型查询，但是没有选sp的
        if (CollectionUtils.isNotEmpty(dto.getSpMatchTypes()) || CollectionUtils.isNotEmpty(dto.getSbMatchTypes())) {
            if (CollectionUtils.isEmpty(dto.getSbMatchTypes())) {
                return;
            }
        }
        List<String> matchTypeList = dto.getSbMatchTypes();
        List<String> matchTypes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isNotBlank(MatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            if (CollectionUtils.isEmpty(matchTypes)) {
                return;
            }
        }

        StringBuilder sql = new StringBuilder("SELECT")
                .append(" query, 'keyword' as type, 'sb' as adType, GROUP_CONCAT(distinct keyword_id,',') idStr,")
                .append(" ANY(`query_cn`) query_cn, ANY(`marketplace_id`) marketplace_id,")
                .append(" count(distinct keyword_id) occurrence_num, '' main_image, '' targeting_expression,")
                .append(" sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`cost`) cost, sum(`conversions14d`) sale_num,")
                .append(" sum(`sales14d`) total_sales, 0 ad_sales, 0 ad_other_sales")
                .append(" FROM ods_t_cpc_sb_query_keyword_report t ")
                .append(" where puid=? and marketplace_id=? and count_day>=? and count_day<=? ");
        args.add(puid);
        args.add(dto.getMarketplaceId());
        args.add(dto.getStart());
        args.add(dto.getEnd());
        if (StringUtils.isNotBlank(dto.getQuery())) {
            sql.append(" and query = ? ");
            args.add(dto.getQuery());
        }
        sql.append(SqlStringUtil.dealInList("shop_id", dto.getShopIdList(), args));
        if (CollectionUtils.isNotEmpty(matchTypes)) {
            sql.append(SqlStringUtil.dealInList("lower(match_type)", matchTypes, args));
        }
        sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", dto.getGroupIdList(), args));

        // 仅展示未投放 or 仅展示已投放
        if (StringUtils.equalsAny(dto.getQueryWordTargetType(), Constants.QUERY_TARGET, Constants.QUERY_NOT_TARGET)) {
            sql.append(" and concat_ws(',', ad_group_id, query) ");
            if (StringUtils.equals(dto.getQueryWordTargetType(), Constants.QUERY_NOT_TARGET)) {
                sql.append("not");
            }
            sql.append(" in ( ")
                    .append(SearchQueryTagSqlHelper.getSbKeywordSearchQueryTagInSql(dto.getPuid(), dto, args, CpcQueryWordDto.QueryWordTagTypeEnum.getKeywordSearchField()))
                    .append(" ) ");
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String searchValue = dto.getSearchValue();
            if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN.equals(searchValue)) {
                searchValue = Constants.keywords_related_to_your_brand;
            } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN.equals(searchValue)) {
                searchValue = Constants.keywords_related_to_your_landing_pages;
            }
            List<String> searchValueList = StringUtil.stringToList(searchValue, StringUtil.SPECIAL_COMMA);
            String field = SqlStringUtil.getSqlField(CpcSbQueryKeywordReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                //将搜索词转小写和数据库中函数小写匹配
                field = "lower(" + field + ")";
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    sql.append(" and ").append(field).append(" like ?");
                    args.add("%" + SqlStringUtil.dealLikeSql(searchValue) + "%");
                } else {//默认精确
                    if (searchValueList.size() > 1) {
                        sql.append(SqlStringUtil.dealInList(field, searchValueList, args));
                    } else {
                        sql.append(" and ").append(field).append(" = ?");
                        args.add(searchValueList.get(0));
                    }
                }
            }
        }

        if (StringUtils.isNotBlank(dto.getWordRoot())) {
            sql.append(" and query in ( ")
                    .append(this.getWordRootQuerySqlByQuery(puid, dto, args, WordRoot.QueryType.SB_QUERY.getType()))
                    .append(" ) ");
        }
        sql.append(" group by query ");

        sqlList.add(sql.toString());
    }

    @Override
    public SearchTermAggregateBO allSearchTermAggregate(Integer puid, CpcQueryWordDto dto, boolean isGroupByDate) {
        List<Object> args = new ArrayList<>();

        List<String> sqlList = Lists.newArrayList();
        getSpKeywordSearchTermSql(puid, dto, args, sqlList);
        getSpTargetSearchTermSql(puid, dto, args, sqlList);
        getSbSearchTermSql(puid, dto, args, sqlList);
        if (CollectionUtils.isEmpty(sqlList)) {
            return null;
        }

        StringBuilder allSearchTermSql = new StringBuilder("SELECT")
                .append(" query , any(`marketplace_id`) marketplace_id, any(query_cn) query_cn,")
                .append(" SUM(impressions) impressions, SUM(clicks) clicks, SUM(cost) cost,")
                .append(" SUM(sale_num) sale_num, SUM(total_sales) total_sales, SUM(ad_sales) `ad_sales`, SUM(ad_other_sales) `ad_other_sales`")
                .append(" FROM ( ").append(StringUtils.join(sqlList, " union all ")).append(" ) p group by query ")
                .append(this.getHavingSql(dto, args));

        StringBuilder sql = new StringBuilder("select ");
        sql.append(" count(*) size_sum,")
                .append(" sum(t.sale_num) sale_num_sum,")
                .append(" sum(t.cost) cost_sum,")
                .append(" sum(t.impressions) impressions_sum,")
                .append(" sum(t.clicks) clicks_sum,")
                .append(" sum(t.total_sales) total_sales_sum,")
                .append(" sum(t.ad_sales) ad_sales_sum,")
                .append(" sum(t.ad_other_sales) ad_other_sales_sum");

        sql.append(" FROM ( ").append(allSearchTermSql).append(" ) t ");

        if (dto.isQueryJoinSearchTermsRank()) {
            boolean isFilterAbaRank = dto.getUseAdvanced() && (dto.getSearchFrequencyRankMin() != null || dto.getSearchFrequencyRankMax() != null);
            sql.append(" left join ods_t_week_search_terms_analysis a on lower(query) = a.search_term and t.marketplace_id = a.marketplace_id and a.start_date=? and a.marketplace_id=? ");
            args.add(dto.getLastWeekSearchTermsRankDate());
            args.add(dto.getMarketplaceId());

            if (isFilterAbaRank) {
                if (dto.getSearchFrequencyRankMin() != null) {
                    sql.append(" and ifnull(search_frequency_rank, 2147483647) >= ? ");
                    args.add(dto.getSearchFrequencyRankMin());
                }
                if (dto.getSearchFrequencyRankMax() != null) {
                    sql.append(" and ifnull(search_frequency_rank, 2147483647) <= ? ");
                    args.add(dto.getSearchFrequencyRankMax());
                }
                sql.append(" where search_frequency_rank is not null ");
                if (dto.getSearchFrequencyRankMin() != null) {
                    sql.append(" and search_frequency_rank >= ? ");
                    args.add(dto.getSearchFrequencyRankMin());
                }
                if (dto.getSearchFrequencyRankMax() != null) {
                    sql.append(" and search_frequency_rank <= ? ");
                    args.add(dto.getSearchFrequencyRankMax());
                }
            }
        }
        return getJdbcTemplate().queryForObject(sql.toString(), (rs, rowNum) -> SearchTermAggregateBO.builder()
                .totalSize(rs.getInt("size_sum"))
                .saleNum(rs.getInt("sale_num_sum"))
                .cost(rs.getBigDecimal("cost_sum"))
                .impressions(rs.getInt("impressions_sum"))
                .clicks(rs.getInt("clicks_sum"))
                .totalSales(rs.getBigDecimal("total_sales_sum"))
                .adSales(rs.getBigDecimal("ad_sales_sum"))
                .adOtherSales(rs.getBigDecimal("ad_other_sales_sum"))
                .build(),
                args.toArray());

    }


    @Override
    public List<String> queryListAllSearchTermAggregateData(Integer puid, CpcQueryWordDto dto) {
        List<Object> args = new ArrayList<>();

        List<String> sqlList = Lists.newArrayList();
        getSpKeywordSearchTermSql(puid, dto, args, sqlList);
        getSpTargetSearchTermSql(puid, dto, args, sqlList);
        getSbSearchTermSql(puid, dto, args, sqlList);
        if (CollectionUtils.isEmpty(sqlList)) {
            return null;
        }

        StringBuilder allSearchTermSql = new StringBuilder("SELECT")
                .append(" query, any(`marketplace_id`) marketplace_id, any(query_cn) query_cn,")
                .append(" SUM(impressions) impressions, SUM(clicks) clicks, SUM(cost) cost,")
                .append(" SUM(sale_num) sale_num, SUM(total_sales) total_sales, SUM(ad_sales) `ad_sales`, SUM(ad_other_sales) `ad_other_sales`")
                .append(" FROM ( ").append(StringUtils.join(sqlList, " union all ")).append(" ) p group by query ")
                .append(this.getHavingSql(dto, args));

        StringBuilder sql = new StringBuilder("select query FROM ( ").append(allSearchTermSql).append(" ) t ");

        if (dto.isQueryJoinSearchTermsRank()) {
            boolean isFilterAbaRank = dto.getUseAdvanced() && (dto.getSearchFrequencyRankMin() != null || dto.getSearchFrequencyRankMax() != null);
            sql.append(" left join ods_t_week_search_terms_analysis a on lower(query) = a.search_term and t.marketplace_id = a.marketplace_id and a.start_date=? and a.marketplace_id=? ");
            args.add(dto.getLastWeekSearchTermsRankDate());
            args.add(dto.getMarketplaceId());

            if (isFilterAbaRank) {
                if (dto.getSearchFrequencyRankMin() != null) {
                    sql.append(" and ifnull(search_frequency_rank, 2147483647) >= ? ");
                    args.add(dto.getSearchFrequencyRankMin());
                }
                if (dto.getSearchFrequencyRankMax() != null) {
                    sql.append(" and ifnull(search_frequency_rank, 2147483647) <= ? ");
                    args.add(dto.getSearchFrequencyRankMax());
                }
                sql.append(" where search_frequency_rank is not null ");
                if (dto.getSearchFrequencyRankMin() != null) {
                    sql.append(" and search_frequency_rank >= ? ");
                    args.add(dto.getSearchFrequencyRankMin());
                }
                if (dto.getSearchFrequencyRankMax() != null) {
                    sql.append(" and search_frequency_rank <= ? ");
                    args.add(dto.getSearchFrequencyRankMax());
                }
            }
        }
        return getJdbcTemplate().queryForList(sql.toString(), String.class, args.toArray());
    }

    @Override
    public List<SearchTermAggregateBO> allSearchTermAggregateDataByQueryList(Integer puid, CpcQueryWordDto dto, String startDate, String endDate,
                                                                                    List<String> queryList) {
        if (CollectionUtils.isEmpty(queryList)) {
            return new ArrayList<>();
        }
        List<Object> args = new ArrayList<>();
        StringJoiner sj = new StringJoiner(" union all ");
        List<List<String>> queryPartitionList = Lists.partition(queryList, 8000);
        StringBuilder patitionSql;
        for (List<String> querys : queryPartitionList) {
            patitionSql = new StringBuilder("SELECT ")
                    .append(" IFNULL(SUM(impressions), 0) impressions, IFNULL(SUM(clicks), 0) clicks, IFNULL(SUM(cost), 0) cost, IFNULL(SUM(sale_num), 0) sale_num,")
                    .append(" IFNULL(SUM(total_sales), 0) total_sales, IFNULL(SUM(ad_sales), 0) `ad_sales`, IFNULL(SUM(total_sales) - SUM(ad_sales), 0) ad_other_sales")
                    .append(" from ").append(this.getJdbcHelper().getTable());
            patitionSql.append(" where puid=? and marketplace_id=? and count_day>=? and count_day<=?");
            args.add(puid);
            args.add(dto.getMarketplaceId());
            args.add(startDate);
            args.add(endDate);
            patitionSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", dto.getGroupIdList(), args));
            patitionSql.append(SqlStringUtil.dealInList("query", querys, args));
            sj.add(patitionSql.toString());
        }
        return getJdbcTemplate().query(sj.toString(), new BeanPropertyRowMapper<>(SearchTermAggregateBO.class), args.toArray());
    }

    @Override
    public List<SearchTermBO> getDetailList(Integer puid, CpcQueryWordDetailDto dto) {

        List<Object> args = Lists.newArrayList();

        StringBuilder spKeywordSql = new StringBuilder(" select ");
        spKeywordSql.append(" count_day, impressions, clicks, cost, sale_num, order_num, total_sales ")
                .append(" from ods_t_cpc_query_keyword_report ")
                .append(" where puid=? and marketplace_id=? and count_day>=? and count_day<=? and `query`=? ");
        args.add(puid);
        args.add(dto.getMarketplaceId());
        args.add(dto.getStart());
        args.add(dto.getEnd());
        args.add(dto.getQuery());
        spKeywordSql.append(SqlStringUtil.dealInList("shop_id", dto.getShopIds(), args));
        spKeywordSql.append(SqlStringUtil.dealInList("keyword_id", dto.getSearchTermIds(), args));

        StringBuilder spTargetSql = new StringBuilder(" select ");
        spTargetSql.append(" count_day, impressions, clicks, cost, sale_num, order_num, total_sales ")
                .append(" from ods_t_cpc_query_targeting_report ")
                .append(" where puid=? and marketplace_id=? and count_day>=? and count_day<=? and `query`=? ");
        args.add(puid);
        args.add(dto.getMarketplaceId());
        args.add(dto.getStart());
        args.add(dto.getEnd());
        args.add(dto.getQuery());
        spTargetSql.append(SqlStringUtil.dealInList("shop_id", dto.getShopIds(), args));
        spTargetSql.append(SqlStringUtil.dealInList("target_id", dto.getSearchTermIds(), args));

        StringBuilder sbKeywordSql = new StringBuilder(" select ");
        sbKeywordSql.append(" count_day, impressions, clicks, cost, conversions14d sale_num, 0 order_num, sales14d total_sales ")
                .append(" from ods_t_cpc_sb_query_keyword_report ")
                .append(" where puid=? and marketplace_id=? and count_day>=? and count_day<=? and `query`=? ");
        args.add(puid);
        args.add(dto.getMarketplaceId());
        args.add(dto.getStart());
        args.add(dto.getEnd());
        args.add(dto.getQuery());
        sbKeywordSql.append(SqlStringUtil.dealInList("shop_id", dto.getShopIds(), args));
        sbKeywordSql.append(SqlStringUtil.dealInList("keyword_id", dto.getSearchTermIds(), args));

        List<String> sqlList = Lists.newArrayList(spKeywordSql.toString(), spTargetSql.toString(), sbKeywordSql.toString());

        StringBuilder sql = new StringBuilder(" select ");
        sql.append(" count_day, sum(impressions) impressions, sum(clicks) clicks, sum(cost) cost, sum(sale_num) sale_num, sum(order_num) order_num, sum(total_sales) total_sales ")
                .append(" from ( ").append(StringUtils.join(sqlList, " union all ")).append(" ) t ")
                .append(" group by count_day order by count_day");

        log.info(" getDetailList sql {}", SqlStringUtil.exactSql(sql.toString(), args));

        return getJdbcTemplate().query(sql.toString(), (rs, i) -> SearchTermBO.builder()
                        .countDay(rs.getString("count_day"))
                        .impressions(rs.getInt("impressions"))
                        .clicks(rs.getInt("clicks"))
                        .cost(rs.getBigDecimal("cost") != null ? rs.getBigDecimal("cost") : BigDecimal.ZERO)
                        .saleNum(rs.getInt("sale_num"))
                        .orderNum(rs.getInt("order_num"))
                        .totalSales(rs.getBigDecimal("total_sales") != null ? rs.getBigDecimal("total_sales") : BigDecimal.ZERO)
                        .build(),
                args.toArray());
    }

    @Override
    public int listCountSearchTermPageList(Integer puid, CpcQueryWordDto dto, Page page) {
        List<Object> args = new ArrayList<>();

        List<String> sqlList = Lists.newArrayList();
        getSpKeywordSearchTermSql(puid, dto, args, sqlList);
        getSpTargetSearchTermSql(puid, dto, args, sqlList);
        getSbSearchTermSql(puid, dto, args, sqlList);
        if (CollectionUtils.isEmpty(sqlList)) {
            return 0;
        }

        StringBuilder allSearchTermSql = new StringBuilder("SELECT")
                .append(" query , any(`marketplace_id`) marketplace_id, any(query_cn) query_cn, any(main_image) main_image, ANY(targeting_expression) targeting_expression,")
                .append(" GROUP_CONCAT(distinct adType,',') adType, GROUP_CONCAT(idStr,',') idStr,")
                .append(" sum(CAST(occurrence_num AS INT)) occurrence_num, SUM(impressions) impressions, SUM(clicks) clicks, SUM(cost) cost,")
                .append(" SUM(sale_num) sale_num, SUM(total_sales) total_sales, SUM(ad_sales) `ad_sales`, SUM(ad_other_sales) `ad_other_sales`")
                .append(" FROM ( ").append(StringUtils.join(sqlList, " union all ")).append(" ) p group by query ")
                .append(this.getHavingSql(dto, args));

        StringBuilder sql = new StringBuilder("select ")
                .append(" t.query query, t.query_cn query_cn, t.marketplace_id marketplace_id, t.main_image main_image, t.targeting_expression,")
                .append(" t.adType adType, t.idStr,")
                .append(" t.occurrence_num occurrence_num, t.impressions impressions, t.clicks clicks, t.cost cost,")
                .append(" t.sale_num sale_num, t.total_sales total_sales, t.ad_sales ad_sales, t.ad_other_sales ad_other_sales");
        if (dto.isQueryJoinSearchTermsRank()) {
            sql.append(", ifnull(search_frequency_rank, 2147483647) search_frequency_rank ");
        }
        sql.append(" FROM ( ").append(allSearchTermSql).append(" ) t ");

        if (dto.isQueryJoinSearchTermsRank()) {
            boolean isFilterAbaRank = dto.getUseAdvanced() && (dto.getSearchFrequencyRankMin() != null || dto.getSearchFrequencyRankMax() != null);
            sql.append(" left join ods_t_week_search_terms_analysis a on lower(query) = a.search_term and t.marketplace_id = a.marketplace_id and a.start_date=? and a.marketplace_id=? ");
            args.add(dto.getLastWeekSearchTermsRankDate());
            args.add(dto.getMarketplaceId());

            if (isFilterAbaRank) {
                if (dto.getSearchFrequencyRankMin() != null) {
                    sql.append(" and ifnull(search_frequency_rank, 2147483647) >= ? ");
                    args.add(dto.getSearchFrequencyRankMin());
                }
                if (dto.getSearchFrequencyRankMax() != null) {
                    sql.append(" and ifnull(search_frequency_rank, 2147483647) <= ? ");
                    args.add(dto.getSearchFrequencyRankMax());
                }
                sql.append(" where search_frequency_rank is not null ");
                if (dto.getSearchFrequencyRankMin() != null) {
                    sql.append(" and search_frequency_rank >= ? ");
                    args.add(dto.getSearchFrequencyRankMin());
                }
                if (dto.getSearchFrequencyRankMax() != null) {
                    sql.append(" and search_frequency_rank <= ? ");
                    args.add(dto.getSearchFrequencyRankMax());
                }
            }
        }

        String countSql = "select count(*) from ( " + sql + ") c ";
        return countPageResult(puid, countSql.toString(), args.toArray());
    }
}

