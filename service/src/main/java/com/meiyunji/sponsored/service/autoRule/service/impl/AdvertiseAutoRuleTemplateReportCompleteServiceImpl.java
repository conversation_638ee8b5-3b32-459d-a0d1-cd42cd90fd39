package com.meiyunji.sponsored.service.autoRule.service.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.api.enumeration.MarketplacePb;
import com.meiyunji.sellfox.aadas.api.service.AadasApiGrpc;
import com.meiyunji.sellfox.aadas.api.service.GetSellerReportInitSuccessRequestPb;
import com.meiyunji.sellfox.aadas.api.service.GetSellerReportInitSuccessResponsePb;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.autoRule.dao.AdvertiseAutoRuleTemplateReportCompleteDao;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleReportCompleteEnum;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleTemplateReportComplete;
import com.meiyunji.sponsored.service.autoRule.service.AdvertiseAutoRuleTemplateReportCompleteService;
import com.meiyunji.sponsored.service.autoRule.vo.ReportCompletePushUidVo;
import com.meiyunji.sponsored.service.autoRule.vo.ReportCompletePushVo;
import io.grpc.ManagedChannel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2024-06-12  14:21
 */

@Service
@Slf4j
public class AdvertiseAutoRuleTemplateReportCompleteServiceImpl implements AdvertiseAutoRuleTemplateReportCompleteService {

    @Autowired
    private AdvertiseAutoRuleTemplateReportCompleteDao advertiseAutoRuleTemplateReportCompleteDao;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    @Qualifier("taskManagedChannel")
    private ManagedChannel taskManagedChannel;

    @Resource(name = "shardingJdbcMap")
    private Map<String, JdbcTemplate> jdbcTemplateMap;

    //所有的vip数据库
    @Resource(name = "vipJdbcTemplateMap")
    private Map<String, JdbcTemplate> vipJdbcTemplateMap;

    @Override
    public void insertOrUpdate(AdvertiseAutoRuleTemplateReportComplete advertiseAutoRuleTemplateReportComplete) {
        advertiseAutoRuleTemplateReportCompleteDao.insertOrUpdateList(Arrays.asList(advertiseAutoRuleTemplateReportComplete));
    }

    @Override
    public AdvertiseAutoRuleTemplateReportComplete queryReportComplete(int puid, Integer shopId) {
        return advertiseAutoRuleTemplateReportCompleteDao.getByPuidAndShopId(puid, shopId);
    }

    @Override
    public boolean queryReportComplete(int puid, List<Integer> shopIdList) {
        //查询数据库
        List<AdvertiseAutoRuleTemplateReportComplete> list = advertiseAutoRuleTemplateReportCompleteDao.getByPuidAndShopIdList(Arrays.asList(puid), shopIdList);

        //需要调接口查询的shopId
        List<Integer> queryShopIdList;
        //数据库存在的未完成的
        Set<Integer> dbExistNotCompleteSet = new HashSet<>();
        //数据库所有的
        Map<Integer, AdvertiseAutoRuleTemplateReportComplete> dbAllMap = new HashMap<>();

        //收集数据
        if (CollectionUtils.isEmpty(list)) {
            queryShopIdList = shopIdList;
        } else {
            Set<Integer> set = new HashSet<>();
            list.forEach(x -> {
                dbAllMap.put(x.getShopId(), x);
                if (AutoRuleReportCompleteEnum.NOT_COMPLETE.getValue() == x.getReportComplete()) {
                    dbExistNotCompleteSet.add(x.getShopId());
                }
            });

            if (CollectionUtils.isNotEmpty(dbExistNotCompleteSet)) {
                set.addAll(dbExistNotCompleteSet);
            }
            if (list.size() < shopIdList.size()) {
                shopIdList.forEach(x -> {
                    if (!dbAllMap.containsKey(x)) {
                        set.add(x);
                    }
                });
            }
            queryShopIdList = new ArrayList<>(set);
        }

        //没有待同步的
        if (CollectionUtils.isEmpty(queryShopIdList)) {
            return true;
        }

        List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, queryShopIdList);
        if (CollectionUtils.isEmpty(shopAuthList)) {
            return false;
        }

        //调用接口查询
        Map<Integer, Integer> resultMap = queryReportCompleteAadasApi(shopAuthList);

        //整理出需要更新或者插入到数据库的
        List<AdvertiseAutoRuleTemplateReportComplete> addList = new ArrayList<>();
        List<AdvertiseAutoRuleTemplateReportComplete> updateList = new ArrayList<>();

        queryShopIdList.forEach(x -> {
            if (resultMap.containsKey(x)) {
                //结果包含
                //数据库不存在则插入，存在则判断结果完成且数据库未完成则更新，未完成则不处理
                Integer reportComplete = resultMap.get(x);
                if (!dbAllMap.containsKey(x)) {
                    addList.add(new AdvertiseAutoRuleTemplateReportComplete(puid, x, reportComplete));
                } else {
                    if (AutoRuleReportCompleteEnum.COMPLETE.getValue() == reportComplete && AutoRuleReportCompleteEnum.NOT_COMPLETE.getValue() == dbAllMap.get(x).getReportComplete()) {
                        updateList.add(new AdvertiseAutoRuleTemplateReportComplete(puid, x, reportComplete));
                    }
                }
            } else {
                //结果不包含
                //数据库不存在则插入，存在则不处理
                if (!dbAllMap.containsKey(x)) {
                    addList.add(new AdvertiseAutoRuleTemplateReportComplete(puid, x, AutoRuleReportCompleteEnum.NOT_COMPLETE.getValue()));
                }
            }
        });

        if (CollectionUtils.isNotEmpty(addList)) {
            advertiseAutoRuleTemplateReportCompleteDao.insertOrUpdateList(addList);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            advertiseAutoRuleTemplateReportCompleteDao.updateCompleteList(updateList);
        }

        //返回结果
        return MapUtils.isNotEmpty(resultMap) && resultMap.keySet().containsAll(shopIdList) && !resultMap.containsValue(0);
    }

    /**
     * 查询报告是否拉取完，查询结果map key为shopId，value为是否拉取完，1是0否AutoRuleReportCompleteEnum
     * @param shopAuthList
     * @return
     */
    @Override
    public Map<Integer, Integer> queryReportCompleteAadasApi(List<ShopAuth> shopAuthList) {
        //查询aadas
        Map<String, ShopAuth> shopAuthMap = new HashMap<>(shopAuthList.size()  * 2);
        AadasApiGrpc.AadasApiBlockingStub aadasApiBlockingStub = AadasApiGrpc.newBlockingStub(taskManagedChannel);
        GetSellerReportInitSuccessRequestPb.GetSellerReportInitSuccessRequest.Builder request = GetSellerReportInitSuccessRequestPb.GetSellerReportInitSuccessRequest.newBuilder();
        List<GetSellerReportInitSuccessRequestPb.GetSellerReportInitSuccessItemRequest> itemList = new ArrayList<>(shopAuthList.size());
        shopAuthList.forEach(x -> {
            GetSellerReportInitSuccessRequestPb.GetSellerReportInitSuccessItemRequest.Builder builder = GetSellerReportInitSuccessRequestPb.GetSellerReportInitSuccessItemRequest.newBuilder();
            builder.setSellerId(x.getSellingPartnerId());
            builder.setMarketplaceId(MarketplacePb.Marketplace.valueOf(Marketplace.fromId(x.getMarketplaceId()).name()));
            shopAuthMap.put(x.getSellingPartnerId() + x.getMarketplaceId(), x);
            itemList.add(builder.build());
        });
        request.addAllItem(itemList);
        GetSellerReportInitSuccessResponsePb.GetSellerReportInitSuccessResponse response = aadasApiBlockingStub.getSellerReportInitSuccessInfo(request.build());

        //查询结果转成shopId为key，是否同步完为value的map
        List<GetSellerReportInitSuccessResponsePb.GetSellerReportInitSuccessItemResponse> resultList = response.getResultList();
        Map<Integer, Integer> resultMap = new HashMap<>();
        resultList.forEach(x -> {
            String key = x.getSellerId() + Marketplace.valueOf(x.getMarketplaceId().name()).getId();
            ShopAuth shopAuth = shopAuthMap.get(key);
            if (Objects.nonNull(shopAuth)) {
                resultMap.put(shopAuth.getId(), x.getSuccess() ? AutoRuleReportCompleteEnum.COMPLETE.getValue() : AutoRuleReportCompleteEnum.NOT_COMPLETE.getValue());
            }
        });

        return resultMap;
    }


    @Override
    public List<ReportCompletePushVo> queryRequiredPush(int shardIndex, int shardTotal) {
        //所有数据源任务，确保顺序一致
        List<JdbcTemplate> jdbcTemplateTaskList = new ArrayList<>(jdbcTemplateMap.size() + vipJdbcTemplateMap.size());
        List<String> keyList = new ArrayList<>(jdbcTemplateMap.keySet());
        List<String> vipKeyList = new ArrayList<>(vipJdbcTemplateMap.keySet());
        Collections.sort(keyList);
        Collections.sort(vipKeyList);
        keyList.forEach(x -> jdbcTemplateTaskList.add(jdbcTemplateMap.get(x)));
        vipKeyList.forEach(x -> jdbcTemplateTaskList.add(vipJdbcTemplateMap.get(x)));

        //按照主项目执行器分片，识别当前分片的数据源任务
        List<JdbcTemplate> jdbcTemplateList = new ArrayList<>(jdbcTemplateTaskList.size() / shardTotal + 1);
        for (int i = 0; i < jdbcTemplateTaskList.size(); i++) {
            if (i % shardTotal == shardIndex) {
                jdbcTemplateList.add(jdbcTemplateTaskList.get(i));
            }
        }

        if (CollectionUtils.isEmpty(jdbcTemplateList)) {
            return null;
        }

        //查询需要推送的数据
        List<ReportCompletePushUidVo> uidList = new ArrayList<>();
        jdbcTemplateList.forEach(jdbcTemplate -> {
            List<ReportCompletePushUidVo> curUidList = jdbcTemplate.query("select distinct shop_id shopId, create_uid uid from t_ad_auto_rule_template where report_complete_push = 1", new BeanPropertyRowMapper<>(ReportCompletePushUidVo.class));
            if (CollectionUtils.isNotEmpty(curUidList)) {
                uidList.addAll(curUidList);
            }
        });

        //无数据
        if (CollectionUtils.isEmpty(uidList)) {
            return null;
        }

        //转换map
        Map<Integer, Set<Integer>> uidMap = uidList.stream().collect(Collectors.groupingBy(ReportCompletePushUidVo::getShopId,
                        Collectors.mapping(ReportCompletePushUidVo::getUid, Collectors.toSet())));

        //查询报告拉取完信息
        List<AdvertiseAutoRuleTemplateReportComplete> completeList = advertiseAutoRuleTemplateReportCompleteDao.getCompleteByShopIdList(new ArrayList<>(uidMap.keySet()));
        Set<Integer> completeSet = completeList.stream().map(AdvertiseAutoRuleTemplateReportComplete::getShopId).collect(Collectors.toSet());

        //剔除没有完成的店铺
        Iterator<Map.Entry<Integer, Set<Integer>>> iterator = uidMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Integer, Set<Integer>> entry = iterator.next();
            if (!completeSet.contains(entry.getKey())) {
                iterator.remove();
            }
        }

        //无数据
        if (MapUtils.isEmpty(uidMap)) {
            return null;
        }

        //查询店铺信息
        List<ShopAuth> shopAuthList = shopAuthDao.getScAndVcByIds((new ArrayList<>(uidMap.keySet())));
        Map<Integer, ShopAuth> shopAuthMap = shopAuthList.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity(), (a, b) -> a));

        //整理返回数据
        List<ReportCompletePushVo> voList = new ArrayList<>(uidMap.size());
        uidMap.forEach((k, v) -> {
            if (shopAuthMap.containsKey(k)) {
                ReportCompletePushVo vo = new ReportCompletePushVo();
                vo.setShopId(k);
                vo.setUidList(new ArrayList<>(v));
                ShopAuth shopAuth = shopAuthMap.get(k);
                vo.setShopName(shopAuth.getName());
                vo.setMarketplaceId(shopAuth.getMarketplaceId());
                vo.setPuid(shopAuth.getPuid());
                voList.add(vo);
            }
        });
        return voList;
    }

    @Override
    public void checkReportComplete() {

        long startIndex = 0;
        int limit = 500;

        while (true) {
            List<AdvertiseAutoRuleTemplateReportComplete> list = advertiseAutoRuleTemplateReportCompleteDao.selectNotComplete(startIndex, limit);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }

            //处理当前页
            processCheckReportComplete(list);

            //翻页完
            if (list.size() < limit) {
                break;
            }
            startIndex = list.get(list.size() - 1).getId();
        }
    }

    private void processCheckReportComplete(List<AdvertiseAutoRuleTemplateReportComplete> list) {

        //所有店铺id，查询授权店铺信息
        List<Integer> allShopIdList = list.stream().map(x -> x.getShopId()).collect(Collectors.toList());
        List<ShopAuth> shopAuthList = shopAuthDao.getAdAuthShopByShopId(null, allShopIdList);

        if (CollectionUtils.isEmpty(shopAuthList)) {
            return;
        }

        Map<Integer, ShopAuth> shopAuthMap = new HashMap<>();
        shopAuthList.forEach(x -> shopAuthMap.put(x.getId(), x));

        //需要处理的数据
        List<AdvertiseAutoRuleTemplateReportComplete> processList = list.stream().filter(x -> shopAuthMap.containsKey(x.getShopId())).collect(Collectors.toList());

        //未完成map
        Map<Integer, AdvertiseAutoRuleTemplateReportComplete> notCompleteMap = new HashMap<>();
        processList.forEach(x -> notCompleteMap.put(x.getShopId(), x));

        //最终需要更新到数据库的
        List<AdvertiseAutoRuleTemplateReportComplete> updateList = new ArrayList<>();

        //分片处理
        List<List<AdvertiseAutoRuleTemplateReportComplete>> partitionList = Lists.partition(processList, 20);
        for (List<AdvertiseAutoRuleTemplateReportComplete> partition : partitionList) {
            List<ShopAuth> queryAuthList = new ArrayList<>(partition.size());
            partition.forEach(x -> {
                ShopAuth shopAuth = shopAuthMap.get(x.getShopId());
                if (Objects.nonNull(shopAuth)) {
                    queryAuthList.add(shopAuth);
                }
            });

            if (CollectionUtils.isEmpty(queryAuthList)) {
                continue;
            }

            //需要调用aadas接口查询
            Map<Integer, Integer> resultMap = queryReportCompleteAadasApi(queryAuthList);
            notCompleteMap.forEach((k, v) -> {
                //结果包含且完成
                if (resultMap.containsKey(k) && AutoRuleReportCompleteEnum.COMPLETE.getValue() == resultMap.get(k)) {
                    //待更新数据库完成的
                    v.setReportComplete(AutoRuleReportCompleteEnum.COMPLETE.getValue());
                    updateList.add(v);
                }
            });
        }

        //更新数据库
        if (CollectionUtils.isNotEmpty(updateList)) {
            advertiseAutoRuleTemplateReportCompleteDao.updateCompleteList(updateList);
        }

    }

}
