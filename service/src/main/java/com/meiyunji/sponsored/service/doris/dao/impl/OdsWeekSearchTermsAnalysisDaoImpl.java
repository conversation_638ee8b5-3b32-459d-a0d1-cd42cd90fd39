package com.meiyunji.sponsored.service.doris.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.amc.dto.QueryAbaRankDto;
import com.meiyunji.sponsored.service.amc.dto.QueryWordAdSpaceReportDto;
import com.meiyunji.sponsored.service.doris.dao.IOdsWeekSearchTermsAnalysisDao;
import com.meiyunji.sponsored.service.doris.po.OdsWeekSearchTermsAnalysis;
import com.meiyunji.sponsored.service.searchTermsAnalysis.dto.MarketplaceStartDateDto;
import com.meiyunji.sponsored.service.searchTermsAnalysis.qo.SearchTermsAnalysisTrendQo;
import com.meiyunji.sponsored.service.searchTermsAnalysis.vo.MuiltMarketplaceRankVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-08-05 20:19
 */
@Repository
public class OdsWeekSearchTermsAnalysisDaoImpl extends DorisBaseDaoImpl<OdsWeekSearchTermsAnalysis> implements IOdsWeekSearchTermsAnalysisDao {
    @Override
    public OdsWeekSearchTermsAnalysis getLatestDate(String marketplaceId) {
        StringBuilder sql = new StringBuilder("select MAX(start_date) start_date from ");
        sql.append(this.getJdbcHelper().getTable());
        sql.append(" where marketplace_id = ? and start_date >= DATE_SUB(now(), INTERVAL 4 WEEK)");
        List<Object> argsList = new ArrayList<>();
        argsList.add(marketplaceId);
        List<OdsWeekSearchTermsAnalysis> results = getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(OdsWeekSearchTermsAnalysis.class));
        return CollectionUtils.isEmpty(results) ? null : results.get(0);
    }

    @Override
    public List<OdsWeekSearchTermsAnalysis> getLatestDateList(List<String> marketplaceIdList) {
        if (CollectionUtils.isEmpty(marketplaceIdList)) {
            return Collections.emptyList();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select marketplace_id, MAX(start_date) AS start_date from ");
        sql.append(this.getJdbcHelper().getTable());
        sql.append(" where start_date >= DATE_SUB(now(), INTERVAL 4 WEEK) ");
        sql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIdList, argsList));
        sql.append(" GROUP BY marketplace_id ");
        return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(OdsWeekSearchTermsAnalysis.class), argsList.toArray());
    }

    @Override
    public List<OdsWeekSearchTermsAnalysis> queryRanks(List<String> searchTerms, String marketplaceId, String startDate) {
        if (CollectionUtils.isEmpty(searchTerms)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select search_term , search_frequency_rank , round(week_ratio*100,2) week_ratio from ");
        sql.append(this.getJdbcHelper().getTable());
        sql.append(" where start_date=? and marketplace_id=? ");
        argsList.add(startDate);
        argsList.add(marketplaceId);
        sql.append(SqlStringUtil.dealInList("search_term", searchTerms, argsList));
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(OdsWeekSearchTermsAnalysis.class));
    }

    @Override
    public List<OdsWeekSearchTermsAnalysis> queryRanks(List<String> searchTerms, List<String> marketplaceIdList, List<String> startDateList, List<String> marketplaceStartDateList) {
        if (CollectionUtils.isEmpty(searchTerms)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select marketplace_id, search_term , search_frequency_rank , round(week_ratio*100,2) week_ratio from ");
        sql.append(this.getJdbcHelper().getTable());
        sql.append(" where 1=1 ");
        sql.append(SqlStringUtil.dealInList("start_date", startDateList, argsList));
        sql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIdList, argsList));
        sql.append(SqlStringUtil.dealDorisInList("concat_ws('|', marketplace_id, start_date)", marketplaceStartDateList, argsList));
        sql.append(SqlStringUtil.dealInList("search_term", searchTerms, argsList));
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(OdsWeekSearchTermsAnalysis.class));
    }

    @Override
    public List<OdsWeekSearchTermsAnalysis> queryRanks(List<QueryAbaRankDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList<>();
        }

        List<Object> argsList = new ArrayList<>();

        StringBuilder sql = new StringBuilder();
        // 为了处理多个Dto条件，我们构建多个union al子句
        boolean firstCondition = true;

        for (QueryAbaRankDto dto : dtoList) {
            if (!firstCondition) {
                sql.append(" union all ");
            }
            firstCondition = false;

            sql.append("select search_term, search_frequency_rank,marketplace_id,start_date from ");
            sql.append(this.getJdbcHelper().getTable());
            sql.append(" where start_date = ? and marketplace_id = ? ");
            argsList.add(dto.getStartDate());
            argsList.add(dto.getMarketplaceId());
            sql.append(SqlStringUtil.dealInList("search_term", new ArrayList<>(dto.getSearchTerms()), argsList));
        }
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(OdsWeekSearchTermsAnalysis.class));
    }


    @Override
    public List<OdsWeekSearchTermsAnalysis> getTrend(SearchTermsAnalysisTrendQo qo, String minDay) {
        if (CollectionUtils.isEmpty(qo.getSearchTerms())) {
            return new ArrayList<>();
        }
        List<String> searchTerms = qo.getSearchTerms();
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("select start_date ,end_date ,marketplace_id ,department_name ,search_term ,search_frequency_rank from ");
        sql.append(this.getJdbcHelper().getTable());
        sql.append(" where marketplace_id = ? and end_date <= ? and start_date >= ? ");
        argsList.add(qo.getMarketplaceId());
        argsList.add(qo.getEndDate());
        argsList.add(minDay);
        if (CollectionUtils.isNotEmpty(searchTerms)) {
            sql.append(SqlStringUtil.dealInList("search_term", searchTerms, argsList));
        }
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(OdsWeekSearchTermsAnalysis.class));
    }

    @Override
    public List<OdsWeekSearchTermsAnalysis> getLatestDateAndMarketplaceId(List<String> keywordTexts) {
        //取出所有近四周内有数据站点和最新开始时间
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select MAX(start_date) start_date,marketplace_id, search_term from ");
        sql.append(this.getJdbcHelper().getTable());
        sql.append(" where start_date >= DATE_SUB(now(), INTERVAL 4 WEEK) ");
        if (CollectionUtils.isNotEmpty(keywordTexts)) {
            sql.append(SqlStringUtil.dealInList("search_term", keywordTexts, argsList));
        }
        sql.append("group by marketplace_id, search_term ");
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(OdsWeekSearchTermsAnalysis.class));
    }

    @Override
    public List<MuiltMarketplaceRankVo> getRank(List<MarketplaceStartDateDto> muiltMarketplaceRankList, List<String> keywordTexts) {
        //根据上面取出的开始时间和站点筛出该关键词所有的站点的排名
        if (CollectionUtils.isEmpty(keywordTexts)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select search_term , search_frequency_rank , marketplace_id from ");
        sql.append(this.getJdbcHelper().getTable());
        sql.append(" where ");
        List<String> marketplaceIdList = muiltMarketplaceRankList.stream().map(MarketplaceStartDateDto::getMarketplaceId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sql.append(SqlStringUtil.dealInListNotAnd("marketplace_id", marketplaceIdList, argsList));
        }
        List<String> startDateList = muiltMarketplaceRankList.stream().map(MarketplaceStartDateDto::getStartDate).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(startDateList)) {
            sql.append(SqlStringUtil.dealInList("start_date", startDateList, argsList));
        }
        if (CollectionUtils.isNotEmpty(muiltMarketplaceRankList)) {
            sql.append(SqlStringUtil.dealMultiInList(Arrays.asList("marketplace_id", "start_date", "search_term"), muiltMarketplaceRankList, argsList, Arrays.asList(MarketplaceStartDateDto::getMarketplaceId, MarketplaceStartDateDto::getStartDate, MarketplaceStartDateDto::getKeywordText)));
        }
        sql.append(SqlStringUtil.dealInList("lower(search_term)", keywordTexts, argsList));
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(MuiltMarketplaceRankVo.class));
    }

    @Override
    public List<OdsWeekSearchTermsAnalysis> taskGetLatestDateAndMarketplaceId(String createTime, List<String> keywords) {
        //取出所有近四周内有数据站点和最新开始时间
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select if(? = max(start_date), min(start_date), if(? < min(start_date), '2020-01-01', max(start_date)) ) as start_date, marketplace_id, search_term from ");
        argsList.add(createTime);
        argsList.add(createTime);
        sql.append(this.getJdbcHelper().getTable());
        sql.append(" where start_date >= DATE_SUB(?, INTERVAL 4 WEEK) ");
        argsList.add(createTime);
        if (CollectionUtils.isNotEmpty(keywords)) {
            List<String> lowerCaseKeywords = keywords.stream()
                    .map(String::toLowerCase) // 使用map操作将每个字符串转换为小写
                    .collect(Collectors.toList()); // 收集转换后的字符串到一个新的List中
            sql.append(SqlStringUtil.dealInList("lower(search_term)", lowerCaseKeywords, argsList));
        }
        sql.append("group by marketplace_id, search_term ");
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(OdsWeekSearchTermsAnalysis.class));
    }

}
