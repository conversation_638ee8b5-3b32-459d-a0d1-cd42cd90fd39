package com.meiyunji.sponsored.service.doris.dao.impl;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.LogicType;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportDataDto;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportFilterDto;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.KeywordsPageParam;
import com.meiyunji.sponsored.service.cpc.vo.NeKeywordsPageParam;
import com.meiyunji.sponsored.service.cpc.vo.RepeatTargetingDetailPageVo;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdNeKeywordDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdNeKeyword;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.NeTargetReportParamsMappingEnum;
import com.meiyunji.sponsored.service.enums.NeTargetReportTableEnum;
import com.meiyunji.sponsored.service.enums.NetargetTypeTableEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * amazon广告关键词表(OdsAmazonAdNeKeyword)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:18
 */
@Repository
public class OdsAmazonAdNeKeywordDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdNeKeyword> implements IOdsAmazonAdNeKeywordDao {

    @Override
    public List<OdsAmazonAdNeKeyword> listKeyWordAndMatchTypeByGroupIdList(Integer puid, Integer shopId, List<String> groupIdList) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
            .equalTo("puid", puid)
            .equalTo("shop_id", shopId)
            .inStrList("ad_group_id", groupIdList.toArray(new String[] {}))
            .equalTo("type", Constants.NEGATIVE)
            .in("state", new Object[] {CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()})
            .build();
        String sql = "select keyword_text, match_type, ad_group_id from " + this.getJdbcHelper().getTable() + " where " + builder.getSql();
        return getJdbcTemplate().query(sql, builder.getValues(), new BeanPropertyRowMapper<>(OdsAmazonAdNeKeyword.class));
    }

    @Override
    public List<String> filterKeywordId(Integer puid, RepeatTargetingDetailPageVo detailPageVo) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select ad_group_id adGroupId from ods_t_amazon_ad_ne_keyword ");
        sql.append("where puid = ? and marketplace_id = ?");
        args.add(puid);
        args.add(detailPageVo.getMarketplaceId());
        sql.append(" and keyword_text = ? ");
        args.add(detailPageVo.getKeywordText());
        if (CollectionUtils.isNotEmpty(detailPageVo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", detailPageVo.getShopIdList(), args));
        }
        //广告组合和广告活动筛选
        if (CollectionUtils.isNotEmpty(detailPageVo.getCampaignIdList())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", detailPageVo.getCampaignIdList(), args));
        }
        //广告组筛选
        if (CollectionUtils.isNotEmpty(detailPageVo.getSpAdGroupId())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", detailPageVo.getSpAdGroupId(), args));
        }
        //投放运行状态
        if (CollectionUtils.isNotEmpty(detailPageVo.getStatus())) {
            sql.append(SqlStringUtil.dealInList("state", detailPageVo.getStatus(), args));
        }
        //投放服务状态
        if (CollectionUtils.isNotEmpty(detailPageVo.getServingStatus())) {
            sql.append(SqlStringUtil.dealInList("serving_status", detailPageVo.getServingStatus(), args));

        }
        return getJdbcTemplate().queryForList(sql.toString(), args.toArray(), String.class);
    }

    @Override
    public Integer getTotalSizeFromDoris(Integer puid, NeKeywordsPageParam param) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select count(*) from ods_t_amazon_ad_ne_keyword ");
        sql.append(" where puid = ? and shop_id = ? ");
        args.add(puid);
        args.add(param.getShopId());
        sql.append("and type = " + "'" + Constants.NEGATIVE + "'");
        //状态查询
        if (StringUtils.isNotBlank(param.getState())) {
            sql.append(" and state = ? ");
            args.add(param.getState());
        }
        //关键搜索
        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            KeywordsPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(param.getSearchField(), KeywordsPageParam.SearchFieldEnum.class);
            if (searchFieldEnum != null) {
                if ("blur".equals(param.getSearchType())) { //模糊搜索
                    sql.append(" and ").append("lower(" + searchFieldEnum.getColumn()).append(") like ? ");
                    args.add("%" + SqlStringUtil.dealLikeSql(param.getSearchValue().trim().toLowerCase()) + "%");
                } else {//默认精确
                    if (param.getListSearchValue().size() > 1) {
                        List<String> values = new ArrayList<>();
                        param.getListSearchValue().forEach(item -> values.add(SqlStringUtil.dealLikeSql(item.toLowerCase())));
                        sql.append(SqlStringUtil.dealInList("lower(" + searchFieldEnum.getColumn() + ")", values, args));
                    } else {
                        sql.append(" and ").append("lower(" + searchFieldEnum.getColumn() + ")").append(" =  ? ");
                        args.add(param.getSearchValue().trim().toLowerCase());
                    }
                }
            } else {
                logger.error("否定关键词搜索条件异常：puid:{},shopId:{},searchField:{}", puid, param.getShopId(), param.getSearchField());
            }
        }
        //广告活动ID搜索
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", Arrays.asList(StringUtil.splitStr(param.getCampaignId()).toArray(new String[] {})), args));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            // portfolioIds筛选掉-1的情况
            List<String> portfolioIds = new ArrayList<>();
            if (StringUtils.isNotBlank(param.getPortfolioId())) {
                portfolioIds = StringUtil.splitStr(param.getPortfolioId()).stream().distinct()
                    .filter(item -> !item.equals("-1")).collect(Collectors.toList());
            }
            StringBuilder campaignId = new StringBuilder(" and campaign_id in ( select campaign_id from ods_t_amazon_ad_campaign_all where 1=1 ");
            if (StringUtils.isNotBlank(param.getStatus())) {
                List<String> stateList = Arrays.asList(param.getStatus().split(","));
                if (stateList.size() > 0) {
                    campaignId.append(SqlStringUtil.dealInList("state", stateList, args));
                }
            }
            if (StringUtils.isNotBlank(param.getServingStatus())) {
                List<String> servingStatusList = Arrays.asList(param.getServingStatus().split(","));
                if (servingStatusList.size() > 0) {
                    List<String> statusList = getServingStatus(servingStatusList);
                    if (CollectionUtils.isNotEmpty(statusList)) {
                        campaignId.append(SqlStringUtil.dealInList("serving_status", statusList, args));
                    }
                }
            }
            campaignId.append(" and puid = ? and shop_id = ? ");
            args.add(puid);
            args.add(param.getShopId());
            // 选择‘未分类’：没有广告组合的广告活动
            if ("-1".equals(param.getPortfolioId())) {
                //                builder.isNull(LogicType.AND, "portfolio_id");
                campaignId.append(" and portfolio_id is null ");
            } else if (CollectionUtils.isNotEmpty(portfolioIds)) {
                if (param.getPortfolioId().contains("-1")) {
                    campaignId.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, args));
                    campaignId.append(" or portfolio_id is null ");
                } else {
                    campaignId.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, args));
                }
            }
            if (StringUtils.isNotBlank(param.getType())) {
                campaignId.append(SqlStringUtil.dealInList("type", StringUtil.splitStr(param.getType()), args));
            }
            sql.append(campaignId + " )");
        }
        //广告组ID搜索
        if (StringUtils.isNotBlank(param.getGroupId())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", Arrays.asList(StringUtil.splitStr(param.getGroupId()).toArray(new String[] {})), args));
        }
        //匹配类型搜索
        if (StringUtils.isNotBlank(param.getMatchType())) {
            sql.append(" and match_type = ? ");
            args.add(param.getMatchType());
        }
        return getTotalSize(getJdbcTemplate(), sql.toString(), args.toArray());
    }

    /**
     * 只查询基础数据，未筛选报告数据
     *
     * @param param
     * @return
     */
    @Override
    public Page<String> pageGroupNeKeywordWithoutReportFilter(NeKeywordsPageParam param) {
        //指定sp
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            List<Object> argsList = new ArrayList<>();
            StringBuilder baseSql = new StringBuilder();
            baseSql.append(" from ")
                .append(NeTargetReportTableEnum.SP_AD_GROUP_NEKEYWORD.getBasicTableName())
                .append(" where puid = ? and shop_id = ? and type = 'negative' ");
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            //广告活动筛选
            List<String> campaignIdList = StringUtil.stringToList(param.getCampaignId(), ",");
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                baseSql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
            }
            //广告组合筛选
            baseSql.append(" and campaign_id in ( ").append(getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sp.getCampaignType(), param.getStatus(), param.getServingStatus(), argsList)).append(") ");
            //广告组筛选
            List<String> adGroupList = StringUtil.stringToList(param.getGroupId(), ",");
            if (CollectionUtils.isNotEmpty(adGroupList)) {
                baseSql.append(SqlStringUtil.dealInList("ad_group_id", adGroupList, argsList));
            }
            //否定关键词状态筛选
            baseSql.append(SqlStringUtil.dealSingleField("state", param.getState(), argsList));
            //匹配方式筛选
            baseSql.append(SqlStringUtil.dealSingleField("match_type", param.getMatchType(), argsList));
            //搜索keyword_text
            if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
                KeywordsPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(param.getSearchField(), KeywordsPageParam.SearchFieldEnum.class);
                if (searchFieldEnum != null) {
                    if ("blur".equals(param.getSearchType())) { //模糊搜索
                        baseSql.append(" and ").append(searchFieldEnum.getColumn()).append(" like ? ");
                        argsList.add("%" + param.getSearchValue().trim() + "%");
                    } else {//默认精确
                        if (param.getListSearchValue().size() > 1) {
                            baseSql.append(SqlStringUtil.dealInList(searchFieldEnum.getColumn(), param.getListSearchValue(), argsList));
                        } else {
                            baseSql.append(SqlStringUtil.dealSingleField(searchFieldEnum.getColumn(), param.getSearchValue().trim(), argsList));
                        }
                    }
                } else {
                    logger.error("否定关键词搜索条件异常：puid:{},shopId:{},searchField:{}", param.getPuid(), param.getShopId(), param.getSearchField());
                }
            }

            StringBuilder countSql = new StringBuilder().append("select count(*) ").append(baseSql);
            StringBuilder selectSql = new StringBuilder().append("select keyword_id, create_time, creation_date ").append(baseSql).append(" order by ifnull(creation_date, create_time) desc");
            return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(), selectSql.toString(), argsList.toArray(), (res, i) -> res.getString("keyword_id"));
        }

        //指定sb
        if (Constants.SB.equalsIgnoreCase(param.getType())) {
            List<Object> argsList = new ArrayList<>();
            StringBuilder baseSql = new StringBuilder();
            baseSql.append(" from ")
                .append(NeTargetReportTableEnum.SB_AD_GROUP_NEKEYWORD.getBasicTableName())
                .append(" where puid = ? and shop_id = ? ");
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            //广告活动筛选
            List<String> campaignIdList = StringUtil.stringToList(param.getCampaignId(), ",");
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                baseSql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
            }
            //广告组合筛选
            baseSql.append(" and campaign_id in ( ").append(getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sb.getCampaignType(), param.getStatus(), param.getServingStatus(), argsList)).append(") ");
            //广告组筛选
            List<String> adGroupList = StringUtil.stringToList(param.getGroupId(), ",");
            if (CollectionUtils.isNotEmpty(adGroupList)) {
                baseSql.append(SqlStringUtil.dealInList("ad_group_id", adGroupList, argsList));
            }
            //否定关键词状态筛选
            baseSql.append(SqlStringUtil.dealSingleField("state", param.getState(), argsList));
            //匹配方式筛选
            baseSql.append(SqlStringUtil.dealSingleField("match_type", param.getMatchType(), argsList));
            //搜索keyword_text
            if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
                KeywordsPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(param.getSearchField(), KeywordsPageParam.SearchFieldEnum.class);
                if (searchFieldEnum != null) {
                    if ("blur".equals(param.getSearchType())) { //模糊搜索
                        baseSql.append(" and ").append(searchFieldEnum.getColumn()).append(" like ? ");
                        argsList.add("%" + param.getSearchValue().trim() + "%");
                    } else {//默认精确
                        if (param.getListSearchValue().size() > 1) {
                            baseSql.append(SqlStringUtil.dealInList(searchFieldEnum.getColumn(), param.getListSearchValue(), argsList));
                        } else {
                            baseSql.append(SqlStringUtil.dealSingleField(searchFieldEnum.getColumn(), param.getSearchValue().trim(), argsList));
                        }
                    }
                } else {
                    logger.error("否定关键词搜索条件异常：puid:{},shopId:{},searchField:{}", param.getPuid(), param.getShopId(), param.getSearchField());
                }
            }

            StringBuilder countSql = new StringBuilder().append("select count(*) ").append(baseSql);
            StringBuilder selectSql = new StringBuilder().append("select keyword_id ").append(baseSql).append(" order by ifnull(creation_date, create_time) desc");
            return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(), selectSql.toString(), argsList.toArray(), (res, i) -> res.getString("keyword_id"));
        }
        return new Page<>(param.getPageNo(), param.getPageSize(), 0, 0, Collections.EMPTY_LIST);
    }



    /**
     * 筛选广告组层级否定关键词前后30天报告数据
     * @param param param
     * @return Page<String> row = keywordId
     */
    @Override
    public Page<String> page30DaysGroupNeKeywordWithReportFilter(@NotNull NeKeywordsPageParam param) {
        NeTargetReportFilterDto filterDto = param.getNeTargetReportFilterDto();
        boolean queryWord = Objects.nonNull(filterDto.getDataFrom()) && filterDto.getDataFrom() == 2;
        boolean doAdvancedFilter = Boolean.TRUE.equals(filterDto.getDoAdvancedFilter());
        boolean order = StringUtils.isNotBlank(filterDto.getOrderField()) && StringUtils.isNotBlank(filterDto.getOrderType());
        NeTargetReportParamsMappingEnum orderFieldEnum = NeTargetReportParamsMappingEnum.getByFrontParam(filterDto.getOrderField());

        //指定sp
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            // Initialize DTO and arguments list
            List<Object> argsList = new ArrayList<>();
            StringBuilder baseSql = new StringBuilder();

            // Start building the SQL query
            baseSql.append(" from ")
                .append(NeTargetReportTableEnum.SP_AD_GROUP_NEKEYWORD.getBasicTableName())
                .append(" t left join (")
                .append(" SELECT keyword_id ")
                .append(SqlStringReportUtil.deal30SelectSqlForNeTarget(filterDto.getAdvanceFilterParams(), filterDto.getOnlyShowImpressions(), Constants.SP, filterDto.getDoAdvancedFilter(), order, orderFieldEnum))
                .append(" FROM ")
                .append(queryWord ? NeTargetReportTableEnum.SP_AD_GROUP_NEKEYWORD.getQueryReportTableNameFor30Days() : NeTargetReportTableEnum.SP_AD_GROUP_NEKEYWORD.getTargetReportTableNameFor30Days())
                .append(" WHERE puid = ? AND shop_id = ? and report_date_type = ? ");
            // Add parameters to argsList
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(filterDto.getReportDateType());

            baseSql//.append(doAdvancedFilter ?SqlStringReportUtil.deal30AdvancedFilterForNeTargetWithOnAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions(), Constants.SP): SqlStringReportUtil.dealOnlyShowImpressionsWithOnAnd(filterDto.getOnlyShowImpressions()))
                .append(") r ")
                .append("ON t.puid = ? AND t.shop_id = ? AND t.keyword_id = r.keyword_id ")
                .append("WHERE t.puid = ? AND t.shop_id = ? and t.type = 'negative' ");

            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());

            //广告活动筛选
            List<String> campaignIdList = StringUtil.stringToList(param.getCampaignId(), ",");
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                baseSql.append(SqlStringUtil.dealInList("t.campaign_id", campaignIdList, argsList));
            }
            //广告组合筛选
            baseSql.append(" and t.campaign_id in ( ").append(getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sp.getCampaignType(), param.getStatus(), param.getServingStatus(), argsList)).append(") ");
            //广告组筛选
            List<String> adGroupList = StringUtil.stringToList(param.getGroupId(), ",");
            if (CollectionUtils.isNotEmpty(adGroupList)) {
                baseSql.append(SqlStringUtil.dealInList("t.ad_group_id", adGroupList, argsList));
            }
            //否定关键词状态筛选
            baseSql.append(SqlStringUtil.dealSingleField("t.state", param.getState(), argsList));
            //匹配方式筛选
            baseSql.append(SqlStringUtil.dealSingleField("t.match_type", param.getMatchType(), argsList));
            //搜索keyword_text
            if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
                KeywordsPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(param.getSearchField(), KeywordsPageParam.SearchFieldEnum.class);
                if (searchFieldEnum != null) {
                    if ("blur".equals(param.getSearchType())) { //模糊搜索
                        baseSql.append(" and ").append("t.").append(searchFieldEnum.getColumn()).append(" like ? ");
                        argsList.add("%" + param.getSearchValue().trim() + "%");
                    } else {//默认精确
                        if (param.getListSearchValue().size() > 1) {
                            baseSql.append(SqlStringUtil.dealInList("t." + searchFieldEnum.getColumn(), param.getListSearchValue(), argsList));
                        } else {
                            baseSql.append(SqlStringUtil.dealSingleField("t." + searchFieldEnum.getColumn(), param.getSearchValue().trim(), argsList));
                        }
                    }
                } else {
                    logger.error("否定关键词搜索条件异常：puid:{},shopId:{},searchField:{}", param.getPuid(), param.getShopId(), param.getSearchField());
                }
            }
            baseSql.append(doAdvancedFilter ?SqlStringReportUtil.dealAdvancedFilterForNeTargetWithWhereAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions()): SqlStringReportUtil.dealOnlyShowImpressionsWithWhereAnd(filterDto.getOnlyShowImpressions()));

            // SQL to count the total rows
            StringBuilder countSql = new StringBuilder()
                .append("SELECT COUNT(*) ")
                .append(baseSql);

            // SQL to select keyword IDs, ordered by sum_acos and creation date
            StringBuilder selectSql = new StringBuilder()
                .append(" SELECT t.keyword_id, t.create_time, t.creation_date ")
                .append(baseSql)
                .append("ORDER BY ");
            if (order && Objects.nonNull(orderFieldEnum)) {
                selectSql.append("r.")
                    .append(orderFieldEnum.getPoSumParam())
                    .append(filterDto.getOrderType())
                    .append(",")
                ;
            }
            selectSql.append(" IFNULL(t.creation_date, t.create_time) DESC");

            // Execute query and return paged results
            return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(),
                selectSql.toString(), argsList.toArray(), (res, i) -> res.getString("keyword_id"));
        }
        //指定sb
        if (Constants.SB.equalsIgnoreCase(param.getType())) {
            // Initialize DTO and arguments list
            List<Object> argsList = new ArrayList<>();
            StringBuilder baseSql = new StringBuilder();

            // Start building the SQL query
            baseSql.append(" from ")
                .append(NeTargetReportTableEnum.SB_AD_GROUP_NEKEYWORD.getBasicTableName())
                .append(" t left join (")
                .append(" SELECT keyword_id ")
                .append(SqlStringReportUtil.deal30SelectSqlForNeTarget(filterDto.getAdvanceFilterParams(), filterDto.getOnlyShowImpressions(), Constants.SB, filterDto.getDoAdvancedFilter(), order, orderFieldEnum))
                .append(" FROM ")
                .append(queryWord ? NeTargetReportTableEnum.SB_AD_GROUP_NEKEYWORD.getQueryReportTableNameFor30Days() : NeTargetReportTableEnum.SB_AD_GROUP_NEKEYWORD.getTargetReportTableNameFor30Days())
                .append(" WHERE puid = ? AND shop_id = ? and report_date_type = ? ");
            // Add parameters to argsList
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(filterDto.getReportDateType());

            baseSql//.append(doAdvancedFilter ?SqlStringReportUtil.deal30AdvancedFilterForNeTargetWithOnAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions(), Constants.SB): SqlStringReportUtil.dealOnlyShowImpressionsWithOnAnd(filterDto.getOnlyShowImpressions()))
                .append(") r ")
                .append("ON t.puid = ? AND t.shop_id = ? AND t.keyword_id = r.keyword_id ")
                .append("WHERE t.puid = ? AND t.shop_id = ? ");

            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());

            //广告活动筛选
            List<String> campaignIdList = StringUtil.stringToList(param.getCampaignId(), ",");
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                baseSql.append(SqlStringUtil.dealInList("t.campaign_id", campaignIdList, argsList));
            }
            //广告组合筛选
            baseSql.append(" and t.campaign_id in ( ").append(getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sb.getCampaignType(), param.getStatus(), param.getServingStatus(), argsList)).append(") ");
            //广告组筛选
            List<String> adGroupList = StringUtil.stringToList(param.getGroupId(), ",");
            if (CollectionUtils.isNotEmpty(adGroupList)) {
                baseSql.append(SqlStringUtil.dealInList("t.ad_group_id", adGroupList, argsList));
            }
            //否定关键词状态筛选
            baseSql.append(SqlStringUtil.dealSingleField("t.state", param.getState(), argsList));
            //匹配方式筛选
            baseSql.append(SqlStringUtil.dealSingleField("t.match_type", param.getMatchType(), argsList));
            //搜索keyword_text
            if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
                KeywordsPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(param.getSearchField(), KeywordsPageParam.SearchFieldEnum.class);
                if (searchFieldEnum != null) {
                    if ("blur".equals(param.getSearchType())) { //模糊搜索
                        baseSql.append(" and ").append("t.").append(searchFieldEnum.getColumn()).append(" like ? ");
                        argsList.add("%" + param.getSearchValue().trim() + "%");
                    } else {//默认精确
                        if (param.getListSearchValue().size() > 1) {
                            baseSql.append(SqlStringUtil.dealInList("t." + searchFieldEnum.getColumn(), param.getListSearchValue(), argsList));
                        } else {
                            baseSql.append(SqlStringUtil.dealSingleField("t." + searchFieldEnum.getColumn(), param.getSearchValue().trim(), argsList));
                        }
                    }
                } else {
                    logger.error("否定关键词搜索条件异常：puid:{},shopId:{},searchField:{}", param.getPuid(), param.getShopId(), param.getSearchField());
                }
            }
            baseSql.append(doAdvancedFilter ? SqlStringReportUtil.dealAdvancedFilterForNeTargetWithWhereAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions()): SqlStringReportUtil.dealOnlyShowImpressionsWithWhereAnd(filterDto.getOnlyShowImpressions()));

            // SQL to count the total rows
            StringBuilder countSql = new StringBuilder()
                .append("SELECT COUNT(*) ")
                .append(baseSql);

            // SQL to select keyword IDs, ordered by sum_acos and creation date
            StringBuilder selectSql = new StringBuilder()
                .append("SELECT t.keyword_id ")
                .append(baseSql)
                .append("ORDER BY ");
            if (order && Objects.nonNull(orderFieldEnum)) {
                selectSql.append("r.")
                    .append(orderFieldEnum.getPoSumParam())
                    .append(filterDto.getOrderType())
                    .append(",")
                ;
            }
            selectSql.append(" IFNULL(t.creation_date, t.create_time) DESC");

            // Execute query and return paged results
            return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(),
                selectSql.toString(), argsList.toArray(), (res, i) -> res.getString("keyword_id"));
        }
        return new Page<>(param.getPageNo(), param.getPageSize(), 0, 0, Collections.EMPTY_LIST);
    }

    /**
     * 筛选广告组层级否定关键词自定义时间范围内报告数据
     * @param param param
     * @return Page<String> row = keywordId
     */
    @Override
    public Page<String> pageGroupNeKeywordWithReportFilter(@NotNull NeKeywordsPageParam param) {
        // Initialize DTO and arguments list
        NeTargetReportFilterDto filterDto = param.getNeTargetReportFilterDto();
        boolean queryWord = Objects.nonNull(filterDto.getDataFrom()) && filterDto.getDataFrom() == 2;
        boolean doAdvancedFilter = Boolean.TRUE.equals(filterDto.getDoAdvancedFilter());
        boolean order = StringUtils.isNotBlank(filterDto.getOrderField()) && StringUtils.isNotBlank(filterDto.getOrderType());
        NeTargetReportParamsMappingEnum orderFieldEnum = NeTargetReportParamsMappingEnum.getByFrontParam(filterDto.getOrderField());

        //未指定类型 或 指定sp
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            List<Object> argsList = new ArrayList<>();
            StringBuilder baseSql = new StringBuilder();

            // Start building the SQL query
            baseSql.append(" from ")
                .append(NeTargetReportTableEnum.SP_AD_GROUP_NEKEYWORD.getBasicTableName())
                .append(" t left join (")
                .append("  SELECT keyword_id ")
                .append(SqlStringReportUtil.dealSelectSqlForNeTarget(filterDto.getAdvanceFilterParams(), filterDto.getOnlyShowImpressions(), Constants.SP, filterDto.getDoAdvancedFilter(), order, orderFieldEnum))
                .append("  FROM ").append(queryWord ? NeTargetReportTableEnum.SP_AD_GROUP_NEKEYWORD.getQueryReportTableName() : NeTargetReportTableEnum.SP_AD_GROUP_NEKEYWORD.getReportTableName())
                .append("  WHERE puid = ? AND shop_id = ? and count_day >= ? and count_day <= ? ");
            // Add parameters to argsList
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(filterDto.getReportStartDate());
            argsList.add(filterDto.getReportEndDate());
            baseSql.append(" group by keyword_id HAVING 1=1 " );
            baseSql//.append(doAdvancedFilter ? SqlStringReportUtil.deal30AdvancedFilterForNeTargetWithHavingAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions()): SqlStringReportUtil.dealOnlyShowImpressionsWithHavingAnd(filterDto.getOnlyShowImpressions()))
                .append(") r ")
                .append("ON t.puid = ? AND t.shop_id = ? AND t.keyword_id = r.keyword_id ")
                .append(" WHERE t.puid = ? AND t.shop_id = ? and t.type = 'negative' ");

            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());

            //广告活动筛选
            List<String> campaignIdList = StringUtil.stringToList(param.getCampaignId(), ",");
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                baseSql.append(SqlStringUtil.dealInList("t.campaign_id", campaignIdList, argsList));
            }
            //广告组合筛选
            baseSql.append(" and t.campaign_id in ( ").append(getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sp.getCampaignType(), param.getStatus(), param.getServingStatus(), argsList)).append(") ");
            //广告组筛选
            List<String> adGroupList = StringUtil.stringToList(param.getGroupId(), ",");
            if (CollectionUtils.isNotEmpty(adGroupList)) {
                baseSql.append(SqlStringUtil.dealInList("t.ad_group_id", adGroupList, argsList));
            }
            //否定关键词状态筛选
            baseSql.append(SqlStringUtil.dealSingleField("t.state", param.getState(), argsList));
            //匹配方式筛选
            baseSql.append(SqlStringUtil.dealSingleField("t.match_type", param.getMatchType(), argsList));
            //搜索keyword_text
            if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
                KeywordsPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(param.getSearchField(), KeywordsPageParam.SearchFieldEnum.class);
                if (searchFieldEnum != null) {
                    if ("blur".equals(param.getSearchType())) { //模糊搜索
                        baseSql.append(" and ").append("t.").append(searchFieldEnum.getColumn()).append(" like ? ");
                        argsList.add("%" + param.getSearchValue().trim() + "%");
                    } else {//默认精确
                        if (param.getListSearchValue().size() > 1) {
                            baseSql.append(SqlStringUtil.dealInList("t." + searchFieldEnum.getColumn(), param.getListSearchValue(), argsList));
                        } else {
                            baseSql.append(SqlStringUtil.dealSingleField("t." + searchFieldEnum.getColumn(), param.getSearchValue().trim(), argsList));
                        }
                    }
                } else {
                    logger.error("否定关键词搜索条件异常：puid:{},shopId:{},searchField:{}", param.getPuid(), param.getShopId(), param.getSearchField());
                }
            }
            baseSql.append(doAdvancedFilter ? SqlStringReportUtil.dealAdvancedFilterForNeTargetWithWhereAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions()): SqlStringReportUtil.dealOnlyShowImpressionsWithWhereAnd(filterDto.getOnlyShowImpressions()));

            // SQL to count the total rows
            StringBuilder countSql = new StringBuilder()
                .append("SELECT COUNT(*) ")
                .append(baseSql);

            // SQL to select keyword IDs, ordered by sum_acos and creation date
            StringBuilder selectSql = new StringBuilder()
                .append("SELECT t.keyword_id, t.create_time, t.creation_date ")
                .append(baseSql)
                .append("ORDER BY ");
            if (order && Objects.nonNull(orderFieldEnum)) {
                selectSql.append("r.")
                    .append(orderFieldEnum.getPoSumParam())
                    .append(filterDto.getOrderType())
                    .append(",")
                ;
            }
            selectSql.append(" IFNULL(t.creation_date, t.create_time) DESC");

            // Execute query and return paged results
            return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(),
                selectSql.toString(), argsList.toArray(), (res, i) -> res.getString("keyword_id"));
        }

        //未指定类型 或 指定sp
        if (Constants.SB.equalsIgnoreCase(param.getType())) {
            List<Object> argsList = new ArrayList<>();
            StringBuilder baseSql = new StringBuilder();

            // Start building the SQL query
            baseSql.append(" from ")
                .append(NeTargetReportTableEnum.SB_AD_GROUP_NEKEYWORD.getBasicTableName())
                .append(" t left join (")
                .append("  SELECT keyword_id ")
                .append(SqlStringReportUtil.dealSelectSqlForNeTarget(filterDto.getAdvanceFilterParams(), filterDto.getOnlyShowImpressions(), Constants.SB, filterDto.getDoAdvancedFilter(), order, orderFieldEnum))
                .append("  FROM ").append(queryWord ? NeTargetReportTableEnum.SB_AD_GROUP_NEKEYWORD.getQueryReportTableName() : NeTargetReportTableEnum.SB_AD_GROUP_NEKEYWORD.getReportTableName())
                .append("  WHERE puid = ? AND shop_id = ? and count_day >= ? and count_day <= ? ");
            // Add parameters to argsList
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(filterDto.getReportStartDate());
            argsList.add(filterDto.getReportEndDate());
            baseSql.append(" group by keyword_id HAVING 1=1 " );
            baseSql//.append(doAdvancedFilter ? SqlStringReportUtil.deal30AdvancedFilterForNeTargetWithHavingAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions()): SqlStringReportUtil.dealOnlyShowImpressionsWithHavingAnd(filterDto.getOnlyShowImpressions()))
                .append(") r ")
                .append("ON t.puid = ? AND t.shop_id = ? AND t.keyword_id = r.keyword_id ")
                .append("WHERE t.puid = ? AND t.shop_id = ? ");

            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());

            //广告活动筛选
            List<String> campaignIdList = StringUtil.stringToList(param.getCampaignId(), ",");
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                baseSql.append(SqlStringUtil.dealInList("t.campaign_id", campaignIdList, argsList));
            }
            //广告组合筛选
            baseSql.append(" and t.campaign_id in ( ").append(getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sb.getCampaignType(), param.getStatus(), param.getServingStatus(), argsList)).append(") ");
            //广告组筛选
            List<String> adGroupList = StringUtil.stringToList(param.getGroupId(), ",");
            if (CollectionUtils.isNotEmpty(adGroupList)) {
                baseSql.append(SqlStringUtil.dealInList("t.ad_group_id", adGroupList, argsList));
            }
            //否定关键词状态筛选
            baseSql.append(SqlStringUtil.dealSingleField("t.state", param.getState(), argsList));
            //匹配方式筛选
            baseSql.append(SqlStringUtil.dealSingleField("t.match_type", param.getMatchType(), argsList));
            //搜索keyword_text
            if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
                KeywordsPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(param.getSearchField(), KeywordsPageParam.SearchFieldEnum.class);
                if (searchFieldEnum != null) {
                    if ("blur".equals(param.getSearchType())) { //模糊搜索
                        baseSql.append(" and ").append("t.").append(searchFieldEnum.getColumn()).append(" like ? ");
                        argsList.add("%" + param.getSearchValue().trim() + "%");
                    } else {//默认精确
                        if (param.getListSearchValue().size() > 1) {
                            baseSql.append(SqlStringUtil.dealInList("t." + searchFieldEnum.getColumn(), param.getListSearchValue(), argsList));
                        } else {
                            baseSql.append(SqlStringUtil.dealSingleField("t." + searchFieldEnum.getColumn(), param.getSearchValue().trim(), argsList));
                        }
                    }
                } else {
                    logger.error("否定关键词搜索条件异常：puid:{},shopId:{},searchField:{}", param.getPuid(), param.getShopId(), param.getSearchField());
                }
            }
            baseSql.append(doAdvancedFilter ? SqlStringReportUtil.dealAdvancedFilterForNeTargetWithWhereAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions()): SqlStringReportUtil.dealOnlyShowImpressionsWithWhereAnd(filterDto.getOnlyShowImpressions()));

            // SQL to count the total rows
            StringBuilder countSql = new StringBuilder()
                .append("SELECT COUNT(*) ")
                .append(baseSql);

            // SQL to select keyword IDs, ordered by sum_acos and creation date
            StringBuilder selectSql = new StringBuilder()
                .append("SELECT t.keyword_id, t.create_time, t.creation_date ")
                .append(baseSql)
                .append("ORDER BY ");
            if (order && Objects.nonNull(orderFieldEnum)) {
                selectSql.append("r.")
                    .append(orderFieldEnum.getPoSumParam())
                    .append(filterDto.getOrderType())
                    .append(",")
                ;
            }
            selectSql.append(" IFNULL(t.creation_date, t.create_time) DESC");

            // Execute query and return paged results
            return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(),
                selectSql.toString(), argsList.toArray(), (res, i) -> res.getString("keyword_id"));
        }

        return new Page<>(param.getPageNo(), param.getPageSize(), 0, 0, Collections.EMPTY_LIST);
    }

    @Override
    public List<NeTargetReportDataDto> get30ReportByKeyWordIds(int puid, int shopId, List<String> keywordIds, NeTargetReportFilterDto neTargetReportFilterDto, String type) {
        boolean target = neTargetReportFilterDto.getDataFrom() != 2;

        //未指定类型 或 指定sp
        if (Constants.SP.equalsIgnoreCase(type)) {
            StringBuilder sql = new StringBuilder();
            sql.append("select keyword_id target_id, cost, total_sales, impressions, clicks, sale_num ad_order_num ")
                .append(" from ")
                .append(target ? NeTargetReportTableEnum.SP_AD_GROUP_NEKEYWORD.getTargetReportTableNameFor30Days() : NeTargetReportTableEnum.SP_AD_GROUP_NEKEYWORD.getQueryReportTableNameFor30Days())
                .append(" where puid = ? and shop_id = ? and report_date_type = ? ");
            List<Object> argsList = new ArrayList<>();
            argsList.add(puid);
            argsList.add(shopId);
            argsList.add(neTargetReportFilterDto.getReportDateType());
            if (keywordIds.size() >= 10000) {
                sql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", keywordIds, argsList));
            } else {
                sql.append(SqlStringUtil.dealInList("keyword_id", keywordIds, argsList));
            }
            return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(NeTargetReportDataDto.class), argsList.toArray());
        }
        if (Constants.SB.equalsIgnoreCase(type)) {
            StringBuilder sql = new StringBuilder();
            sql.append("select keyword_id target_id, cost,sales14d total_sales, impressions, clicks, conversions14d ad_order_num ")
                .append(" from ")
                .append(target ? NeTargetReportTableEnum.SB_AD_GROUP_NEKEYWORD.getTargetReportTableNameFor30Days() : NeTargetReportTableEnum.SB_AD_GROUP_NEKEYWORD.getQueryReportTableNameFor30Days())
                .append(" where puid = ? and shop_id = ? and report_date_type = ? ");
            List<Object> argsList = new ArrayList<>();
            argsList.add(puid);
            argsList.add(shopId);
            argsList.add(neTargetReportFilterDto.getReportDateType());
            if (keywordIds.size() >= 10000) {
                sql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", keywordIds, argsList));
            } else {
                sql.append(SqlStringUtil.dealInList("keyword_id", keywordIds, argsList));
            }
            return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(NeTargetReportDataDto.class), argsList.toArray());
        }
        return Collections.emptyList();
    }

    @Override
    public List<NeTargetReportDataDto> getReportByKeyWordIds(int puid, int shopId, List<String> keywordIds, NeTargetReportFilterDto neTargetReportFilterDto, String type) {
        boolean target = neTargetReportFilterDto.getDataFrom() != 2;
        if (Constants.SP.equalsIgnoreCase(type)) {
            StringBuilder sql = new StringBuilder();
            sql.append("select keyword_id target_id "
                + ",sum(cost) cost "
                + ",sum(total_sales) total_sales "
                + ",sum(impressions) impressions "
                + ",sum(clicks) clicks "
                + ",sum(sale_num) ad_order_num ")
                .append(" from ")
                .append(target ? NeTargetReportTableEnum.SP_AD_GROUP_NEKEYWORD.getReportTableName() : NeTargetReportTableEnum.SP_AD_GROUP_NEKEYWORD.getQueryReportTableName());
            List<Object> argsList = new ArrayList<>();
            argsList.add(puid);
            argsList.add(shopId);
            argsList.add(neTargetReportFilterDto.getReportStartDate());
            argsList.add(neTargetReportFilterDto.getReportEndDate());
            sql.append(" where puid = ? and shop_id = ? and count_day between ? and ? ")
                .append(keywordIds.size() >= 10000 ? SqlStringUtil.dealBitMapDorisInList("keyword_id", keywordIds, argsList) : SqlStringUtil.dealInList("keyword_id", keywordIds, argsList))
                .append(" group by keyword_id");
            return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(NeTargetReportDataDto.class), argsList.toArray());
        }
        if (Constants.SB.equalsIgnoreCase(type)) {
            StringBuilder sql = new StringBuilder();
            sql.append("select keyword_id target_id "
                + ",sum(cost) cost "
                + ",sum(sales14d) total_sales "
                + ",sum(impressions) impressions "
                + ",sum(clicks) clicks "
                + ",sum(conversions14d) ad_order_num ")
                .append(" from ")
                .append(target ? NeTargetReportTableEnum.SB_AD_GROUP_NEKEYWORD.getReportTableName() : NeTargetReportTableEnum.SB_AD_GROUP_NEKEYWORD.getQueryReportTableName());
            List<Object> argsList = new ArrayList<>();
            argsList.add(puid);
            argsList.add(shopId);
            argsList.add(neTargetReportFilterDto.getReportStartDate());
            argsList.add(neTargetReportFilterDto.getReportEndDate());
            sql.append(" where puid = ? and shop_id = ? and count_day between ? and ? ")
                .append(keywordIds.size() >= 10000 ? SqlStringUtil.dealBitMapDorisInList("keyword_id", keywordIds, argsList) : SqlStringUtil.dealInList("keyword_id", keywordIds, argsList))
                .append(" group by keyword_id");
            return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(NeTargetReportDataDto.class), argsList.toArray());
        }
        return Collections.emptyList();

    }

    @Override
    public List<NeTargetReportDataDto> getNetargetAnalysisList(int puid, int shopId, String targetId, NetargetTypeTableEnum netargetTypeTableEnum, String startDate, String endDate) {
        StringBuilder sql = new StringBuilder();
        if (NetargetTypeTableEnum.SB_TYPE_TABLE_ENUM_LIST.contains(netargetTypeTableEnum) || NetargetTypeTableEnum.SD_TYPE_TABLE_ENUM_LIST.contains(netargetTypeTableEnum)) {
            sql.append("select cost,sales14d total_sales, impressions, clicks,conversions14d ad_order_num, count_day, count_month, count_date");
        } else {
            sql.append("select cost, total_sales, impressions, clicks, sale_num ad_order_num,count_day, count_month, count_date");
        }
        sql.append("," + netargetTypeTableEnum.getIdName() + " target_id");
        sql.append(" from " + netargetTypeTableEnum.getTableName())
            .append(" where puid = ? and shop_id = ? AND count_day >= ? AND count_day <= ? ")
            .append(" AND " + netargetTypeTableEnum.getIdName() + " = ? ")
            .append(" order by count_date ")
        ;
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(targetId);
        return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(NeTargetReportDataDto.class), argsList.toArray());

    }

    /**
     * 过滤服务状态
     *
     * @param servingStatusList
     * @return
     */
    private List<String> getServingStatus(List<String> servingStatusList) {
        List<String> statusList = new ArrayList<>();
        for (String servingStatusKey : servingStatusList) {
            List<String> servingStatusValueList = Constants.SERVER_STATUS_SELECT.get(servingStatusKey);
            if (servingStatusValueList != null) {
                statusList.addAll(servingStatusValueList);
            }
        }
        return statusList;
    }

    /**
     * 通过广告组合id查询活动id的sql
     *
     * @param puid
     * @param shopId
     * @param portfolioId
     * @param type
     * @param state
     * @param servingStatus
     * @return
     */
    private String getCampaignIdsByPortfolioIdSql(Integer puid, Integer shopId, String portfolioId, String type, String state, String servingStatus, List<Object> argsList) {
        // portfolioIds筛选掉-1的情况
        List<String> portfolioIds = new ArrayList<>();
        if (StringUtils.isNotBlank(portfolioId)) {
            portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                .filter(item -> !item.equals("-1")).collect(Collectors.toList());
        }
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        if (StringUtils.isNotBlank(state)) {
            List<String> stateList = Arrays.asList(state.split(","));
            if (stateList.size() > 0) {
                builder.in("state", stateList.toArray());
            }
        }
        if (StringUtils.isNotBlank(servingStatus)) {
            List<String> servingStatusList = Arrays.asList(servingStatus.split(","));
            if (servingStatusList.size() > 0) {
                List<String> statusList = getServingStatus(servingStatusList);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(statusList)) {
                    builder.in("serving_status", statusList.toArray());
                }
            }
        }
        builder.equalTo("puid", puid);
        builder.equalTo("shop_id", shopId);
        // 选择‘未分类’：没有广告组合的广告活动
        if ("-1".equals(portfolioId)) {
            builder.isNull(LogicType.AND, "portfolio_id");
        } else if (org.apache.commons.collections.CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioId.contains("-1")) {
                builder.and().leftBracket().in(LogicType.EPT, "portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
                builder.isNull(LogicType.OR, "portfolio_id").rightBracket();
            } else {
                builder.inStrList("portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
            }
        }
        if (StringUtils.isNotBlank(type)) {
            builder.in("type", StringUtil.splitStr(type).toArray());
            //            builder.equalTo("type", type);
        }
        argsList.addAll(Arrays.asList(builder.build().getValues()));
        return "select campaign_id from ods_t_amazon_ad_campaign_all where " + builder.build().getSql();
    }

}

