package com.meiyunji.sponsored.service.multiPlatform.shop.dao;



import com.meiyunji.sponsored.common.springjdbc.IBaseDao;
import com.meiyunji.sponsored.service.multiPlatform.shop.po.MultiPlatformShopAuth;

import java.time.LocalDateTime;
import java.util.List;

public interface IMultiPlatformShopAuthDao extends IBaseDao<MultiPlatformShopAuth> {

    List<MultiPlatformShopAuth> listByPuidAndPlatform(Integer puid, String platformType);

    MultiPlatformShopAuth listByIdAndPlatform(Integer puid, Integer id, String platformType);

    List<Integer> listAdAuthIdByPuidAndPlatform(Integer puid, List<Integer> idList, String platformType);

    MultiPlatformShopAuth getAdShopAuthByPlatform(Integer puid, Integer shopId, String platformType, List<Integer> adStatusList);

    List<Integer> listAdAuthIdByPuidAndPlatform(Integer puid, List<Integer> shopIdList, String platformType, List<Integer> adStatusList);

    List<MultiPlatformShopAuth> listByPuidAndPlatform(Integer puid, List<String> platformTypes);

    /**
     * 根据类型获取puid
     * @param platformType
     * @return Puid
     */
    List<Integer> listPuidByPlatform(String platformType);

    List<MultiPlatformShopAuth> listByAuthExpiredTime(Integer puid, String platformType, LocalDateTime dateTime);

    MultiPlatformShopAuth getPuidAndName(Integer puid, String name);

    int updateShopAuth(MultiPlatformShopAuth shopAuth);

    int copyToDeleteShop(int shopId);

    List<Integer> getAllShopId(Integer puid);

    Integer countExpiredShops(int puid);

    Integer countExpiringShops(int puid);

    List<MultiPlatformShopAuth> listByPuidAndShopIds(Integer puid, List<Integer> shopIdList);

    List<MultiPlatformShopAuth> batchSelectValidaList(Integer minId, List<Long> puidList, String code);

    /**
     * 通过店铺id获取店铺名称
     * @param puid
     * @param authShopIdList
     * @return
     */
    List<MultiPlatformShopAuth> listNameByIds(Integer puid, List<Integer> authShopIdList);

    /**
     * 根据店铺名称列表查询店铺信息 半托管，本土店
     * @param puid 商户信息
     * @param names 店铺名称
     * @return 多平台店铺信息
     */
    List<MultiPlatformShopAuth> listShopByPuidAndNames(Integer puid, List<String> names);


    MultiPlatformShopAuth getByPlatformShopId(Integer puid, String platformType, String platformShopId);


    List<MultiPlatformShopAuth> listByPlatformShopIds(Integer puid, String platformType, List<String> platformShopIds);

    /**
     * 查询半托管店铺 平台类型
     * @param puid
     * @return 多平台店铺信息
     */
    List<MultiPlatformShopAuth> checkPlatformTypeExist(int puid, List<String> platformTypes);

    /**
     * 获取IOSS税号
     * @param puid
     * @param shopId
     * @return
     */
    String getIossNumber(Integer puid, Integer shopId);

    /**
     * 获取无IOSS店铺id
     * @param puid
     * @param shopIdList
     * @return
     */
    List<Integer> getNoIossNumberIdList(Integer puid, List<Integer> shopIdList);


    /**
     * 根据平台类型和temuType类型查询店铺
     * @param puid
     * @param platformType
     * @param temuTypeList
     * @return
     */
    List<MultiPlatformShopAuth> getByPlatFormType(Integer puid, String platformType,  List<String> temuTypeList);

    List<Integer> getIdListByShopTypes(Integer puid, String platformType, List<Integer> shopIdList, List<String> platformShopTypes);

    /**
     * 获取多平台店铺所有puid
     * @return
     */
    List<Integer> getAllPuid();


    List<Integer> getAllPuidByPlatformTypeAndState(String platformType, Integer adState);

    List<MultiPlatformShopAuth> getAllListByPlatformTypeAndState(Integer puid, Integer shopId, String platformType, Integer adState, Integer start, Integer limit);

    int batchInsert(int puid, List<MultiPlatformShopAuth> shopAuths);

    int updateShopAdAuth(Integer puid, Integer shopId, Integer state, LocalDateTime adAuthTime);

    int updateShopAdAuth(Integer puid, Integer shopId, Integer state);

    int updateShopAdAuth(Integer puid, Integer shopId, Integer state, LocalDateTime adAuthTime, String adAccountName);

    List<MultiPlatformShopAuth> listAdAuthByPuidAndPlatform(Integer puid, List<Integer> idList, String platformType);
}
