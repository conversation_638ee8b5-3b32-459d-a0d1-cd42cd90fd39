package com.meiyunji.sponsored.service.multiple.targets.enums;

import lombok.Getter;

/**
 * 广告管理-投放类型枚举
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Getter
public enum TargetTableEnum {
    SP_KEYWORD("keyword_id", "ods_t_amazon_ad_keyword", "ods_t_amazon_ad_keyword_report", "ods_t_amazon_ad_group", "ods_t_amazon_ad_word_root_keyword_sp","sp关键词投放"),
    SB_KEYWORD("keyword_id", "ods_t_amazon_ad_keyword_sb","ods_t_amazon_ad_sb_keyword_report", "ods_t_amazon_ad_group_sb","ods_t_amazon_ad_word_root_keyword_sb","sb关键词投放"),
    SP_TARGET("target_id", "ods_t_amazon_ad_targeting", "ods_t_cpc_targeting_report","ods_t_amazon_ad_group", null,"sp商品投放+sp自动投放"),
    SB_TARGET("target_id", "ods_t_amazon_ad_targeting_sb", "ods_t_amazon_ad_sb_targeting_report", "ods_t_amazon_ad_group_sb",null,"sb商品投放"),
    SD_TARGET("target_id", "ods_t_amazon_ad_targeting_sd", "ods_t_amazon_ad_sd_targeting_report", "ods_t_amazon_ad_group_sd",null,"sd商品投放+sd受众投放"),
    ;

    // 主键id
    private String primaryId;
    // 投放基础数据表
    private String tableName;
    // 投放报告数据表
    private String reportTableName;
    // 广告组数据表
    private String groupTableName;
    // 关键词词根表
    private String wordRootTableName;
    // 投放层级标签枚举
    private String desc;

    TargetTableEnum(String primaryId, String tableName, String reportTableName, String groupTableName, String wordRootTableName, String desc) {
        this.primaryId = primaryId;
        this.tableName = tableName;
        this.reportTableName = reportTableName;
        this.groupTableName = groupTableName;
        this.wordRootTableName = wordRootTableName;
        this.desc = desc;
    }
}
