package com.meiyunji.sponsored.service.cpc.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadras.types.schedule.AdvertiseRuleTaskExecuteRecordV2Message;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.autoRule.vo.AdKeywordTargetAutoRuleParam;
import com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam;
import com.meiyunji.sponsored.service.cpc.bo.AdOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.AdTargetBo;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskMatchTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdGroupStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdTargetStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdTargetingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdTargeting;
import com.meiyunji.sponsored.service.cpc.po.SdTargetTypeEnum;
import com.meiyunji.sponsored.service.cpc.po.SdTargetTypeShowEnum;
import com.meiyunji.sponsored.service.cpc.qo.TargetSuggestBidBatchQo;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.pricing.ScheduleTaskFinishedVo;
import com.meiyunji.sponsored.service.enums.AutoRuleOperationTypeEnum;
import com.meiyunji.sponsored.service.enums.AutoRuleTargetTypeEnum;
import com.meiyunji.sponsored.service.enums.SDTargetingTargetTypeEnum;
import com.meiyunji.sponsored.service.export.dto.DownloadCenterSdTargetBaseDataBO;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.AudienceTargetViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.DiagnoseCountParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.TargetViewParam;
import com.meiyunji.sponsored.service.strategy.vo.AdTargetStrategyParam;
import com.meiyunji.sponsored.service.vo.PuidShopDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.SingleColumnRowMapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2021/7/6
 */
@Repository
public class AmazonSdAdTargetingDaoImpl extends BaseShardingDaoImpl<AmazonSdAdTargeting> implements IAmazonSdAdTargetingDao {

    private final int imgLimit = 1000;
    private final int titleLimit = 1000;

    @Override
    public void batchAdd(int puid, List<AmazonSdAdTargeting> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;
        for (AmazonSdAdTargeting po : list) {
            if (po.getPuid() == null || po.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(po.getPuid());
            arg.add(po.getShopId());
            arg.add(po.getMarketplaceId());
            arg.add(po.getProfileId());
            arg.add(po.getAdGroupId());
            arg.add(po.getTargetId());
            arg.add(po.getState());
            arg.add(po.getType());
            arg.add(po.getTargetText());
            arg.add(po.getBid());
            arg.add(po.getExpressionType());
            arg.add(po.getExpression());
            arg.add(po.getResolvedExpression());
            arg.add(po.getTitle() == null ? "" : (po.getTitle().length() > titleLimit ? po.getTitle().substring(0, titleLimit) : po.getTitle()));
            arg.add(po.getImgUrl() == null ? "" : (po.getImgUrl().length() > titleLimit ? po.getImgUrl().substring(0, imgLimit) : po.getImgUrl()));
            arg.add(po.getSuggested());
            arg.add(po.getRangeStart());
            arg.add(po.getRangeEnd());
            arg.add(po.getServingStatus());
            arg.add(po.getTargetType());
            arg.add(po.getCreationDate());
            arg.add(po.getLastUpdatedDate());
            arg.add(po.getCreateInAmzup());
            arg.add(po.getCreateId());
            arg.add(po.getUpdateId());
            arg.add(po.getTacticType());
            arg.add(po.getCampaignId());
            argList.add(arg.toArray());
        }

        String sql = "insert into t_amazon_ad_targeting_sd (`puid`,`shop_id`,`marketplace_id`,`profile_id`,`ad_group_id`,`target_id`," +
                "`state`,`type`,`target_text`,`bid`,`expression_type`,`expression`,`resolved_expression`,`title`,`img_url`,`suggested`," +
                "`range_start`,`range_end`,`serving_status`,`target_type`,`creation_date`,`last_updated_date`,`create_in_amzup`," +
                "`create_id`,`update_id`,`tactic_type`,`campaign_id`,`create_time`,`update_time`) values (?,?, ?,?, ?,?, ?,?, " +
                "?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?,?, ?, ?, now(3), now(3))";
        getJdbcTemplate(puid).batchUpdate(sql, argList);
    }

    @Override
    public void batchUpdate(int puid, List<AmazonSdAdTargeting> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;
        for (AmazonSdAdTargeting po : list) {
            if (po.getPuid() == null || po.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(po.getState());
            arg.add(po.getType());
            arg.add(po.getTacticType());
            arg.add(po.getTargetText());
            arg.add(po.getBid());
            arg.add(po.getExpressionType());
            arg.add(po.getExpression());
            arg.add(po.getResolvedExpression());
            arg.add(po.getSuggested());
            arg.add(po.getRangeStart());
            arg.add(po.getRangeEnd());
            arg.add(po.getServingStatus());
            arg.add(po.getTargetType());
            arg.add(po.getCreationDate());
            arg.add(po.getLastUpdatedDate());
            arg.add(po.getUpdateId());
            arg.add(po.getPuid());
            arg.add(po.getShopId());
            arg.add(po.getTargetId());
            argList.add(arg.toArray());
        }

        String sql = "update t_amazon_ad_targeting_sd set `state`=?,`type`=?,`tactic_type`=?,`target_text`=?,`bid`=?,`expression_type`=?," +
                "`expression`=?,`resolved_expression`=?,`suggested`=?,`range_start`=?,`range_end`=?," +
                "`serving_status`=?,`target_type`=?,`creation_date`=?,`last_updated_date`=?,`update_id`=? " +
                " where puid=? and shop_id=? and target_id=?";

        getJdbcTemplate(puid).batchUpdate(sql, argList);
    }

    @Override
    public void batchUpdateSuggestValue(int puid, List<AmazonSdAdTargeting> targetingList) {
        if (CollectionUtils.isEmpty(targetingList)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(targetingList.size());
        List<Object> arg;
        for (AmazonSdAdTargeting po : targetingList) {
            if (po.getPuid() == null || po.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(po.getSuggested());
            arg.add(po.getRangeStart());
            arg.add(po.getRangeEnd());
            arg.add(po.getPuid());
            arg.add(po.getShopId());
            arg.add(po.getTargetId());
            argList.add(arg.toArray());
        }

        String sql = "update t_amazon_ad_targeting_sd set `suggested`=?,`range_start`=?,`range_end`=?,update_time=now(3)" +
                " where puid=? and shop_id=? and target_id=?";

        getJdbcTemplate(puid).batchUpdate(sql, argList);
    }

    @Override
    public List<AmazonSdAdTargeting> listByTargetId(int puid, int shopId, List<String> targetIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .in("target_id", targetIds.toArray())
                .build();

        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonSdAdTargeting> listByShopIdsAndTargetIds(int puid, List<Integer> shopIdList, List<String> targetIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .in("shop_id", shopIdList.toArray())
                .in("target_id", targetIds.toArray())
                .build();

        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonSdAdTargeting> getByTargetSuggestBidBatchQo(int puid, List<TargetSuggestBidBatchQo> targetList) {
        StringBuilder sb = new StringBuilder("select * from " + getJdbcHelper().getTable() + " where puid = ? and (shop_id,target_id) in (");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        for (TargetSuggestBidBatchQo qo : targetList) {
            sb.append("(?,?),");
            argsList.add(qo.getShopId());
            argsList.add(qo.getTargetId());
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(")");
        return getJdbcTemplate(puid).query(sb.toString(), argsList.toArray(), getMapper());
    }

    @Override
    public List<AmazonSdAdTargeting> listByTargetId(int puid,  List<String> targetIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .in("target_id", targetIds.toArray())
                .build();

        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonSdAdTargeting> audienceByTargetId(int puid,  List<String> targetIds) {
        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_targeting_sd d left join t_amazon_ad_group_sd g on(d.ad_group_id = g.ad_group_id) ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where d.puid=? and g.tactic = 'audience'");
        argsList.add(puid);

        selectSql.append(whereSql);
        Object[] args = argsList.toArray();
        return getJdbcTemplate(puid).queryForList(selectSql.toString(),AmazonSdAdTargeting.class);
    }

    @Override
    public AmazonSdAdTargeting getbyTargetId(int puid, Integer shopId, String targetId) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .equalToWithoutCheck("target_id", targetId)
                .build();

        return getByCondition(puid, conditionBuilder);
    }

    @Override
    public Map<String, Integer> statCountByAdGroup(int puid, Integer shopId, List<String> status, List<String> adGroupIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .in("ad_group_id", adGroupIds.toArray())
                .in("state", status.toArray())
                .groupBy("ad_group_id").build();

        String sql = "select ad_group_id adGroupId, count(*) c from t_amazon_ad_targeting_sd where " + conditionBuilder.getSql();
        return getJdbcTemplate(puid).queryForList(sql, conditionBuilder.getValues())
                .stream().collect(Collectors.toMap(e -> e.get("adGroupId").toString(), e -> Integer.parseInt(e.get("c").toString())));
    }

    @Override
    public Page<AmazonSdAdTargeting> pageList(int puid, TargetingPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("ad_group_id", param.getGroupId());

        if (StringUtils.isNotBlank(param.getStatus())) {
            builder.equalTo("state", param.getStatus());
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            builder.like("target_text", param.getSearchValue());
        }

        String orderBySql = " order by create_time desc";

        return page(puid, param.getPageNo(), param.getPageSize(), orderBySql, builder.build());
    }

    @Override
    public List<AmazonSdAdTargeting> listByCondition(int puid, TargetingPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("ad_group_id", param.getGroupId());

        if (StringUtils.isNotBlank(param.getStatus())) {
            builder.equalTo("state", param.getStatus());
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            builder.like("target_text", param.getSearchValue());
        }

        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonSdAdTargeting> listNoAsinImage(Integer puid, Integer shopId, long offset, int limit) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .equalToWithoutCheck("type", SdTargetTypeEnum.asin.getValue())
                .appendSql(" and (img_url='' or `title`='')", true);

        builder.greaterThan("id", offset);
        builder.orderBy("create_time");
        builder.limit(limit);

        return listByCondition(puid, builder.build());
    }

    @Override
    public void batchSetAsinImage(Integer puid, List<AmazonSdAdTargeting> needUpdateList) {
        if (CollectionUtils.isNotEmpty(needUpdateList)) {
            String sql = "update t_amazon_ad_targeting_sd set img_url=?, title=?, update_time=now(3) where id=? and puid=?";
            List<Object[]> argsList = new ArrayList<>();
            List<Object> args;
            for (AmazonSdAdTargeting t : needUpdateList) {
                args = new ArrayList<>(4);
                args.add(t.getImgUrl() == null ? ""
                        : (t.getImgUrl().length() > imgLimit ? t.getImgUrl().substring(0, imgLimit) : t.getImgUrl()));
                args.add(t.getTitle() == null ? ""
                        : (t.getTitle().length() > titleLimit ? t.getTitle().substring(0, titleLimit) : t.getTitle()));
                args.add(t.getId());
                args.add(t.getPuid());
                argsList.add(args.toArray());
            }
            getJdbcTemplate(puid).batchUpdate(sql, argsList);
        }
    }

    @Override
    public Page getPageList(Integer puid, TargetingPageParam param, Page page) {
        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_targeting_sd ");
        StringBuilder countSql = new StringBuilder("select count(*)  FROM `t_amazon_ad_targeting_sd` ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=?  ");
        argsList.add(puid);

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> groupList = StringUtil.splitStr(param.getGroupId(), ",");
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupList, argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", param.getGroupIdList(), argsList));
        }

        //标签管理
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            whereSql.append(SqlStringUtil.dealInList("target_id", param.getTargetIds(), argsList));
        }

        // 广告策略筛选
        if(CollectionUtils.isNotEmpty(param.getAdStrategyTypeList())){
            String sql = AdTargetStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(),param.getAutoRuleGroupIds(), argsList, "target_id","ad_group_id");
            if(StringUtils.isNotEmpty(sql)){
                whereSql.append(sql);
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                whereSql.append(" and target_text = ? and type = 'asin' ");
                argsList.add(param.getSearchValue());
            } else if ("category".equalsIgnoreCase(param.getSearchField())) {
                whereSql.append(" and target_text like ? and type = 'category' ");
                argsList.add("%" + param.getSearchValue() + "%");
            } else {
                whereSql.append(" and target_text like ? ");
                argsList.add("%" + param.getSearchValue() + "%");
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, argsList));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {

            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ? ");
                argsList.add(AmazonSdAdTargeting.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }

        }

        if (StringUtils.isNotEmpty(param.getChosenTargetType())) {
            whereSql.append(" and tactic_type = ? ");
            argsList.add(param.getChosenTargetType());
        }

        //匹配新版SD投放融合
        List<String> tacticTargetList = Arrays.asList(SdTargetTypeShowEnum.VIEW_PURCHASE.getCode(),
                SdTargetTypeShowEnum.INMARKET_AUDIENCE.getCode(), SdTargetTypeShowEnum.AUDIENCE_CONTENT_CATEGORY.getCode());
        List<String> filterTargetTypeList;
        if (StringUtils.isNotBlank(param.getFilterTargetType()) && CollectionUtils.isNotEmpty(filterTargetTypeList = Arrays.asList(param.getFilterTargetType().split(",")))) {

            StringBuilder conditionBuilder = new StringBuilder();
            final boolean[] hasCondition = {false}; // 使用数组替代boolean变量

            // 添加条件的辅助方法
            Consumer<String> addCondition = condition -> {
                if (hasCondition[0]) {
                    conditionBuilder.append(" or ");
                }
                conditionBuilder.append(condition);
                hasCondition[0] = true;
            };

            if (tacticTargetList.parallelStream().anyMatch(filterTargetTypeList::contains)) {
                if (filterTargetTypeList.contains(SdTargetTypeShowEnum.VIEW_PURCHASE.getCode())) {
                    addCondition.accept("target_type in ('views', 'purchases') ");
                }
                if (filterTargetTypeList.contains(SdTargetTypeShowEnum.INMARKET_AUDIENCE.getCode())) {
                    addCondition.accept("type in ('In-market')  ");
                }
                if (filterTargetTypeList.contains(SdTargetTypeShowEnum.AUDIENCE_CONTENT_CATEGORY.getCode())){
                    //兴趣及生活方式受众(兴趣：Interest ; 内容分类：contentCategorySameAs ; 生活事件：Life event ; 生活方式：Lifestyle; 受众：audienceSameAs ; 受众： Audience)
                    addCondition.accept("type in ('Interest', 'contentCategorySameAs', 'Life event', 'Lifestyle', 'audienceSameAs', 'Audience') ");
                }
            } else {
                if (filterTargetTypeList.contains("asin")) {
                    addCondition.accept("type='asin' ");
                } else if (filterTargetTypeList.contains("category")) {
                    addCondition.accept("target_type = 'asinCategorySameAs'  ");
                } else if (filterTargetTypeList.contains("similarProduct")) {
                    //受众投放类型(亚马逊消费者：audience ; 购买再营销：purchases ; 再营销浏览定向：views ; 与推广商品相似：similarProduct)
                    addCondition.accept("target_type = 'similarProduct' ");
                }
            }

            // 只有存在有效条件时才添加到主SQL
            if (hasCondition[0]) {
                whereSql.append(" and (").append(conditionBuilder).append(")");
            }
        }

        selectSql.append(whereSql);
        countSql.append(whereSql);
        selectSql.append(" order by data_update_time desc, id desc ");

        Object[] args = argsList.toArray();
        return getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonSdAdTargeting.class);
    }

    @Override
    public List<AmazonSdAdTargeting> getAsinTargetViewList(Integer puid, TargetViewParam param) {
        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_targeting_sd ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = this.buildAsinTargetViewWhereSql(puid, param, argsList);
        selectSql.append(whereSql);
        selectSql.append(" order by data_update_time desc, id desc ");
        Object[] args = argsList.toArray();
        return getJdbcTemplate(puid).query(selectSql.toString(), args, new BeanPropertyRowMapper<>(AmazonSdAdTargeting.class));
    }

    @Override
    public List<String> getAsinTargetViewIdList(Integer puid, TargetViewParam param) {
        StringBuilder selectSql = new StringBuilder("select target_id from t_amazon_ad_targeting_sd ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = this.buildAsinTargetViewWhereSql(puid, param, argsList);
        if (CollectionUtils.isNotEmpty(param.getTargetIdList())) {
            whereSql.append(SqlStringUtil.dealInList("target_id", param.getTargetIdList(), argsList));
        }
        selectSql.append(whereSql);
        selectSql.append(" order by data_update_time desc, id desc ");
        Object[] args = argsList.toArray();
        try {
            return getJdbcTemplate(puid).queryForList(selectSql.toString(), args, String.class);
        } catch (Exception e) {
            logger.info("getAsinTargetIdListByTargetViewHourParam error", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<AmazonSdAdTargeting> getAudienceTargetViewList(Integer puid, AudienceTargetViewParam param, boolean isAggregate) {
        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_targeting_sd ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = this.buildAudienceTargetViewWhereSql(puid, param, isAggregate, argsList);
        selectSql.append(whereSql);
        selectSql.append(" order by data_update_time desc, id desc ");
        Object[] args = argsList.toArray();
        return getJdbcTemplate(puid).query(selectSql.toString(), args, new BeanPropertyRowMapper<>(AmazonSdAdTargeting.class));
    }

    @Override
    public List<String> getAudienceTargetViewIdList(Integer puid, AudienceTargetViewParam param) {
        StringBuilder selectSql = new StringBuilder("select target_id from t_amazon_ad_targeting_sd ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = this.buildAudienceTargetViewWhereSql(puid, param, false, argsList);
        if (CollectionUtils.isNotEmpty(param.getTargetIdList())) {
            whereSql.append(SqlStringUtil.dealInList("target_id", param.getTargetIdList(), argsList));
        }
        selectSql.append(whereSql);
        Object[] args = argsList.toArray();
        try {
            return getJdbcTemplate(puid).queryForList(selectSql.toString(), args, String.class);
        } catch (Exception e) {
            logger.info("getAudienceTargetViewList error", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<AmazonSdAdTargeting> getList(Integer puid, TargetingPageParam param) {

        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_targeting_sd ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=?  ");
        argsList.add(puid);

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> groupList = StringUtil.splitStr(param.getGroupId(), ",");
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupList, argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", param.getGroupIdList(), argsList));
        }

        //标签管理
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            whereSql.append(SqlStringUtil.dealInList("target_id", param.getTargetIds(), argsList));
        }
        // 广告策略筛选
        if(CollectionUtils.isNotEmpty(param.getAdStrategyTypeList())){
            String sql = AdTargetStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(),param.getAutoRuleGroupIds(), argsList, "target_id","ad_group_id");
            if(StringUtils.isNotEmpty(sql)){
                whereSql.append(sql);
            }
        }
        //匹配新版SD投放融合
        List<String> tacticTargetList = Arrays.asList(SdTargetTypeShowEnum.VIEW_PURCHASE.getCode(),
                SdTargetTypeShowEnum.INMARKET_AUDIENCE.getCode(), SdTargetTypeShowEnum.AUDIENCE_CONTENT_CATEGORY.getCode());
        List<String> filterTargetTypeList;
        if (StringUtils.isNotBlank(param.getFilterTargetType()) && CollectionUtils.isNotEmpty(filterTargetTypeList = Arrays.asList(param.getFilterTargetType().split(",")))) {
             List<String> conditionList = Lists.newArrayList();

            if (tacticTargetList.parallelStream().anyMatch(filterTargetTypeList::contains)) {
                if (filterTargetTypeList.contains(SdTargetTypeShowEnum.VIEW_PURCHASE.getCode())) {
                    conditionList.add("target_type in ('views', 'purchases')");
                }
                if (filterTargetTypeList.contains(SdTargetTypeShowEnum.INMARKET_AUDIENCE.getCode())) {
                    conditionList.add("type in ('In-market')");
                }
                if (filterTargetTypeList.contains(SdTargetTypeShowEnum.AUDIENCE_CONTENT_CATEGORY.getCode())){
                    //兴趣及生活方式受众(兴趣：Interest ; 内容分类：contentCategorySameAs ; 生活事件：Life event ; 生活方式：Lifestyle; 受众：audienceSameAs ; 受众： Audience)
                    conditionList.add("type in ('Interest', 'contentCategorySameAs', 'Life event', 'Lifestyle', 'audienceSameAs', 'Audience')");
                }
            } else {
                if (filterTargetTypeList.contains("asin")) {
                    conditionList.add("type='asin'");
                } else if (filterTargetTypeList.contains("category")) {
                    conditionList.add("target_type = 'asinCategorySameAs'");
                } else if (filterTargetTypeList.contains("similarProduct")) {
                    //受众投放类型(亚马逊消费者：audience ; 购买再营销：purchases ; 再营销浏览定向：views ; 与推广商品相似：similarProduct)
                    conditionList.add("target_type = 'similarProduct'");
                }
            }

            if (CollectionUtils.isNotEmpty(conditionList)) {
                whereSql.append(" and ( ").append(String.join(" or ", conditionList)).append(" ) ");
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                whereSql.append(" and target_text = ? and type = 'asin' ");
                argsList.add(param.getSearchValue());
            } else if ("category".equalsIgnoreCase(param.getSearchField())) {
                whereSql.append(" and target_text like ? and type = 'category' ");
                argsList.add("%" + param.getSearchValue() + "%");
            } else {
                whereSql.append(" and target_text like ? ");
                argsList.add("%" + param.getSearchValue() + "%");
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, argsList));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {

            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ? ");
                argsList.add(AmazonSdAdTargeting.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }
        }

        // 开启了高级搜索 竞价值
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {
            if (param.getBidMin() != null) {
                whereSql.append(" and bid >= ? ");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and bid <= ? ");
                argsList.add(param.getBidMax());
            }
        }

        if (StringUtils.isNotEmpty(param.getChosenTargetType())) {
            whereSql.append(" and tactic_type = ? ");
            argsList.add(param.getChosenTargetType());
        }

        selectSql.append(whereSql);
        selectSql.append(" order by data_update_time desc, id desc ");

        selectSql.append(" limit ? ");
        argsList.add(Constants.TOTALSIZELIMIT);

        return getJdbcTemplate(puid).query(selectSql.toString(),getRowMapper(),argsList.toArray());
    }

    @Override
    public List<AdTargetBo> getAdTargetBoList(Integer puid, TargetingPageParam param, Integer limit) {

        StringBuilder selectSql = new StringBuilder("select target_id id, bid orderField, ad_group_id groupId from ").append(this.getJdbcHelper().getTable());
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=?  ");
        argsList.add(puid);

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> groupList = StringUtil.splitStr(param.getGroupId(), ",");
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupList, argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", param.getGroupIdList(), argsList));
        }

        //标签管理
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            whereSql.append(SqlStringUtil.dealInList("target_id", param.getTargetIds(), argsList));
        }

        //匹配新版SD投放融合
        List<String> tacticTargetList = Arrays.asList(SdTargetTypeShowEnum.VIEW_PURCHASE.getCode(),
                SdTargetTypeShowEnum.INMARKET_AUDIENCE.getCode(), SdTargetTypeShowEnum.AUDIENCE_CONTENT_CATEGORY.getCode());
        List<String> filterTargetTypeList;
        if (StringUtils.isNotBlank(param.getFilterTargetType()) && CollectionUtils.isNotEmpty(filterTargetTypeList = Arrays.asList(param.getFilterTargetType().split(",")))) {

            StringBuilder conditionBuilder = new StringBuilder();
            final boolean[] hasCondition = {false}; // 使用数组替代boolean变量

            // 添加条件的辅助方法
            Consumer<String> addCondition = condition -> {
                if (hasCondition[0]) {
                    conditionBuilder.append(" or ");
                }
                conditionBuilder.append(condition);
                hasCondition[0] = true;
            };

            if (tacticTargetList.parallelStream().anyMatch(filterTargetTypeList::contains)) {
                if (filterTargetTypeList.contains(SdTargetTypeShowEnum.VIEW_PURCHASE.getCode())) {
                    addCondition.accept("target_type in ('views', 'purchases') ");
                }
                if (filterTargetTypeList.contains(SdTargetTypeShowEnum.INMARKET_AUDIENCE.getCode())) {
                    addCondition.accept("type in ('In-market')  ");
                }
                if (filterTargetTypeList.contains(SdTargetTypeShowEnum.AUDIENCE_CONTENT_CATEGORY.getCode())){
                    //兴趣及生活方式受众(兴趣：Interest ; 内容分类：contentCategorySameAs ; 生活事件：Life event ; 生活方式：Lifestyle; 受众：audienceSameAs ; 受众： Audience)
                    addCondition.accept("type in ('Interest', 'contentCategorySameAs', 'Life event', 'Lifestyle', 'audienceSameAs', 'Audience') ");
                }
            } else {
                if (filterTargetTypeList.contains("asin")) {
                    addCondition.accept("type='asin' ");
                } else if (filterTargetTypeList.contains("category")) {
                    addCondition.accept("target_type = 'asinCategorySameAs'  ");
                } else if (filterTargetTypeList.contains("similarProduct")) {
                    //受众投放类型(亚马逊消费者：audience ; 购买再营销：purchases ; 再营销浏览定向：views ; 与推广商品相似：similarProduct)
                    addCondition.accept("target_type = 'similarProduct' ");
                }
            }

            // 只有存在有效条件时才添加到主SQL
            if (hasCondition[0]) {
                whereSql.append(" and (").append(conditionBuilder).append(")");
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                whereSql.append(" and target_text = ? and type = 'asin' ");
                argsList.add(param.getSearchValue());
            } else if ("category".equalsIgnoreCase(param.getSearchField())) {
                whereSql.append(" and target_text like ? and type = 'category' ");
                argsList.add("%" + param.getSearchValue() + "%");
            } else {
                whereSql.append(" and target_text like ? ");
                argsList.add("%" + param.getSearchValue() + "%");
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, argsList));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {

            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ? ");
                argsList.add(AmazonSdAdTargeting.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }
        }

        // 开启了高级搜索 竞价值
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {
            if (param.getBidMin() != null) {
                whereSql.append(" and bid >= ? ");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and bid <= ? ");
                argsList.add(param.getBidMax());
            }
        }

        if (StringUtils.isNotEmpty(param.getChosenTargetType())) {
            whereSql.append(" and tactic_type = ? ");
            argsList.add(param.getChosenTargetType());
        }

        selectSql.append(whereSql);

        selectSql.append(" limit ? ");
        argsList.add(limit);

        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AdTargetBo.class));
    }

    @Override
    public void updateDataUpdateTime(Integer puid, Integer shopId, String targetId, LocalDate localDate) {
        String sql = "update t_amazon_ad_targeting_sd set data_update_time=?,update_time=now() where puid = ? and shop_id = ? and `target_id`=?";
        getJdbcTemplate(puid).update(sql,new Object[]{localDate,puid,shopId,targetId});
    }

    @Override
    public void updateList(Integer puid, List<AmazonSdAdTargeting> list, String type) {
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_targeting_sd` set update_id=?,update_time=now() ");
        if (Constants.CPC_SD_TARGET_BATCH_UPDATE_BID.equals(type)) {
            sql.append(",`bid` = ? ");
        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            sql.append(",`state`=? ");
        }
        sql.append(" where puid=? and shop_id=?  and target_id=? and id = ?");
        List<Object[]> batchArgs = Lists.newArrayList();
        List<Object> batchArg = new ArrayList<>();
        for (AmazonSdAdTargeting amazonAdTargeting : list) {
            batchArg.add(amazonAdTargeting.getUpdateId());
            if (Constants.CPC_SD_TARGET_BATCH_UPDATE_BID.equals(type)) {
                batchArg.add(amazonAdTargeting.getBid());
            } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
                batchArg.add(amazonAdTargeting.getState());

            }
            batchArg.add(amazonAdTargeting.getPuid());
            batchArg.add(amazonAdTargeting.getShopId());
            batchArg.add(amazonAdTargeting.getTargetId());
            batchArg.add(amazonAdTargeting.getId());
            batchArgs.add(batchArg.toArray());
            batchArg.clear();
        }
        getJdbcTemplate(puid).batchUpdate(sql.toString(), batchArgs);

    }

    @Override
    public List<String> getAsinsByGroupId(Integer puid, Integer shopId, List<String> groupIds) {
        StringBuilder sqlBuilder = new StringBuilder("select distinct(target_text) from t_amazon_ad_targeting_sd where puid = ? and shop_id = ? " );
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(shopId);

        sqlBuilder.append(SqlStringUtil.dealInList("ad_group_id",groupIds,args) );
        sqlBuilder.append(" and type = 'asin'  and state in('enabled','paused')");
        return  getJdbcTemplate(puid).queryForList(sqlBuilder.toString(),String.class, args.toArray());
    }

    @Override
    public List<String> getTargetIdsByTarget(Integer puid ,TargetingPageParam param){
        List<Object> tarArgList = Lists.newArrayList();
        StringBuilder targetSql = new StringBuilder(" select c.target_id from t_amazon_ad_targeting_sd c where c.puid = ? and c.shop_id = ? ");
        tarArgList.add(puid);
        tarArgList.add(param.getShopId());

        StringBuilder whereSql = new StringBuilder();

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> groupList = StringUtil.splitStr(param.getGroupId(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", groupList, tarArgList));
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", param.getGroupIdList(), tarArgList));
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, tarArgList));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and c.serving_status = ? ");
                tarArgList.add(AmazonSdAdTargeting.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("c.serving_status", list, tarArgList));
            }

        }

        //广告标签
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.target_id", param.getTargetIds(), tarArgList));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and c.target_text = ?  ");
            tarArgList.add(param.getSearchValue());
        }

        if (StringUtils.isNotBlank(param.getFilterTargetType())) {
            if ("asin".equalsIgnoreCase(param.getFilterTargetType())) {
                whereSql.append(" and c.type='asin' ");
            } else if ("category".equalsIgnoreCase(param.getFilterTargetType())) {
                whereSql.append(" and c.type='category'  ");
            } else {
                //受众投放类型(亚马逊消费者：audience ; 购买再营销：purchases ; 再营销浏览定向：views ; 与推广商品相似：similarProduct)
                whereSql.append(" and target_type = ? ");
                tarArgList.add(param.getFilterTargetType());
            }
        }

        // 开启了高级搜索 竞价值
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {
            if (param.getBidMin() != null) {
                whereSql.append(" and c.bid >= ? ");
                tarArgList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and c.bid <= ? ");
                tarArgList.add(param.getBidMax());
            }
        }

        targetSql.append(whereSql);
        List targetIds = getJdbcTemplate(puid).queryForList(targetSql.toString(),tarArgList.toArray(),String.class);
        return targetIds;
    }

    @Override
    public Integer statSumCountByAdGroup(int puid, Integer shopId, List<String> status, List<String> adGroupIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .in("ad_group_id", adGroupIds.toArray())
                .in("state", status.toArray())
                .build();

        String sql = "select  count(*) c from t_amazon_ad_targeting_sd where " + conditionBuilder.getSql();
        List<Integer> list = getJdbcTemplate(puid).queryForList(sql,Integer.class,conditionBuilder.getValues());
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : 0 ;
    }
    @Override
    public Integer statSumCountByAdGroupPage(int puid, Integer shopId, List<String> status, GroupPageParam param) {
        StringBuilder builder = new StringBuilder("select count(*) c from t_amazon_ad_targeting_sd where puid = ? and shop_id = ? ");
        List<Object> args = Lists.newArrayList(puid,shopId);
        builder.append(SqlStringUtil.dealInList("state" ,status,args));

        if (param.getCheckProductRightPair() != null && param.getCheckProductRightPair().getKey()) {
            if (CollectionUtils.isEmpty(param.getCheckProductRightPair().getValue())) {
                return 0;
            }
            builder.append(SqlStringUtil.dealInList("campaign_id", param.getCheckProductRightPair().getValue(), args));
        }

        builder.append(" and ad_group_id in ( ").append(getGroupPageSql(puid,param,args)).append(" )");
        List<Integer> list = getJdbcTemplate(puid).queryForList(builder.toString(), Integer.class,args.toArray());
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : 0 ;
    }

    @Override
    public Page<AmazonSdAdTargeting> queryAdSdCommodityTarget(AdTargetStrategyParam param) {
        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_targeting_sd t   " +
                " left join t_amazon_ad_group_sd g on(t.puid = g.puid and t.shop_id = g.shop_id and t.marketplace_id = g.marketplace_id and  t.ad_group_id = g.ad_group_id) ");
        StringBuilder countSql = new StringBuilder("SELECT count(t.id) FROM t_amazon_ad_targeting_sd t "
                +" left join t_amazon_ad_group_sd g on (t.puid = g.puid and t.shop_id = g.shop_id and t.marketplace_id = g.marketplace_id and t.ad_group_id = g.ad_group_id)");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where t.puid=? and t.tactic_type = 'productTarget' and t.is_pricing = 0 and g.state not in ('archived','pendingReview','rejected')");
        argsList.add(param.getPuid());

        if (param.getShopId() != null) {
            whereSql.append(" and t.shop_id = ?");
            argsList.add(param.getShopId());
        }
        if (StringUtils.isNotBlank(param.getState())) {//状态
            if (param.getState().equals("all")) {
                whereSql.append(" and t.state in ('enabled','paused')");
            } else {
                whereSql.append(" and t.state = ?");
                argsList.add(param.getState());
            }
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("t.ad_group_id", param.getGroupIdList(), argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告活动查询
            whereSql.append(SqlStringUtil.dealInList("g.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {  //服务状态查询
            whereSql.append(SqlStringUtil.dealInList("t.serving_status", param.getServingStatusList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            whereSql.append(" and t.target_text like ?  ");
            argsList.add("%"+param.getSearchValue()+"%");
        }

        selectSql.append(whereSql);
        countSql.append(whereSql);
        Object[] args = argsList.toArray();
        return getPageResult(param.getPuid(),param.getPageNo(),param.getPageSize(),
                countSql.toString(),argsList.toArray(),selectSql.toString(),argsList.toArray(), AmazonSdAdTargeting.class);
    }

    @Override
    public Page<AmazonSdAdTargeting> queryAdSdAudienceTarget(AdTargetStrategyParam param) {
        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_targeting_sd t left join t_amazon_ad_group_sd g on(t.puid = g.puid and t.shop_id = g.shop_id and t.marketplace_id = g.marketplace_id and t.ad_group_id = g.ad_group_id) ");
        StringBuilder countSql = new StringBuilder("SELECT count(t.id) FROM t_amazon_ad_targeting_sd t "
                +" left join t_amazon_ad_group_sd g on (t.puid = g.puid and t.shop_id = g.shop_id and t.marketplace_id = g.marketplace_id and t.ad_group_id = g.ad_group_id)");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where t.puid=? and t.tactic_type = 'audienceTarget' and t.is_pricing = 0 and g.state not in ('archived','pendingReview','rejected')");
        argsList.add(param.getPuid());
        if (param.getShopId() != null) {
            whereSql.append(" and t.shop_id = ?");
            argsList.add(param.getShopId());
        }
        if (StringUtils.isNotBlank(param.getState())) {//状态
            if (param.getState().equals("all")) {
                whereSql.append(" and t.state in ('enabled','paused')");
            } else {
                whereSql.append(" and t.state = ?");
                argsList.add(param.getState());
            }
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("t.ad_group_id", param.getGroupIdList(), argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告活动查询
            whereSql.append(SqlStringUtil.dealInList("g.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {  //服务状态查询
            whereSql.append(SqlStringUtil.dealInList("t.serving_status", param.getServingStatusList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            whereSql.append(" and t.target_text like ?  ");
            argsList.add("%"+param.getSearchValue()+"%");
        }

        selectSql.append(whereSql);
        countSql.append(whereSql);
        Object[] args = argsList.toArray();
        return getPageResult(param.getPuid(),param.getPageNo(),param.getPageSize(),
                countSql.toString(),argsList.toArray(),selectSql.toString(),argsList.toArray(), AmazonSdAdTargeting.class);
    }

    private String getGroupPageSql(Integer puid, GroupPageParam param,List<Object> args) {
        StringBuilder selectSql = new StringBuilder("select ad_group_id from t_amazon_ad_group_sd ");


        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        args.add(puid);

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            args.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            whereSql.append(" and campaign_id = ? ");
            args.add(param.getCampaignId());
        }
        if (StringUtils.isNotBlank(param.getMultiCampaignId())) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", StringUtil.splitStr(param.getMultiCampaignId()), args));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), args));
        }


        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getGroupId());
            if (groupIds.size() < 10000) {
                whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, args));
            } else {
                whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", groupIds, args));
            }
        }
        if (StringUtils.isNotBlank(param.getMultiGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getMultiGroupId());
            if (groupIds.size() < 10000) {
                whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, args));
            } else {
                whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", groupIds, args));
            }
        }
        if (CollectionUtils.isNotEmpty(param.getGroupIds())) {  // 广告标签
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", param.getGroupIds(), args));
        }

        // 广告策略筛选
        if(CollectionUtil.isNotEmpty(param.getAdStrategyTypeList())) {
            String subSql = AdGroupStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(), args, "ad_group_id", false);
            if(StringUtils.isNotEmpty(subSql)){
                whereSql.append(subSql);
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and name like ? ");
            args.add("%" + param.getSearchValue().trim() + "%");
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, args));
        }
        if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
            whereSql.append(" and serving_status = 'AD_GROUP_STATUS_ENABLED' ");
        }
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (param.getBidMin() != null) {   //默认竞价
                whereSql.append(" and default_bid >= ? ");
                args.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and default_bid <= ? ");
                args.add(param.getBidMax());
            }
        }
        selectSql.append(whereSql);
        return selectSql.toString();
    }

    @Override
    public List<String> getTargetIds(Integer puid, Integer shopId, String marketPlaceId, List<String> onlineTargetId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .equalTo("marketplace_id",marketPlaceId)
                .inStrList("target_id",onlineTargetId.toArray(new String[]{}))
                .build();
        return listDistinctFieldByCondition(puid,"target_id",builder,String.class);
    }

    @Override
    public List<String> getByTargetIdList(Integer puid, Integer shopId,
                                          List<String> campaignIds,List<String> groupIds,String state,String targetType, String matchType, List<String> targetIdList) {
        StringBuilder sql = new StringBuilder("SELECT d.target_id" +
                " FROM t_amazon_ad_targeting_sd  d left join t_amazon_ad_group_sd g on(d.puid = g.puid and d.shop_id = g.shop_id and d.ad_group_id = g.ad_group_id)");
        List<Object> args = new ArrayList<>();
        StringBuilder whereSql = new StringBuilder(" where d.puid = ? and d.shop_id = ? and JSON_EXTRACT(expression, '$[0].type') NOT IN ('views', 'audience', 'purchases')  ");
        args.add(puid);
        args.add(shopId);
        whereSql.append(SqlStringUtil.dealInList("target_id", targetIdList, args));
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(campaignIds)) { //广告活动查询
            whereSql.append(SqlStringUtil.dealInList("g.campaign_id", campaignIds, args));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(groupIds)) {  //广告组查询
            whereSql.append(SqlStringUtil.dealInList("d.ad_group_id", groupIds, args));

        }
        if (StringUtils.isNotBlank(state)) {//状态
            if (state.equals("all")) {
                whereSql.append(" and d.state in ('enabled','paused')");
            } else {
                whereSql.append(" and d.state = ?");
                args.add(state);
            }
        }

        if (StringUtils.isNotBlank(matchType)) {
            if ("asin".equalsIgnoreCase(matchType)) {
                whereSql.append(" and type='asin' ");
            } else if ("category".equalsIgnoreCase(matchType)) {
                whereSql.append(" and type='category'  ");
            } else {
                //商品投放类型(与推广商品相似：similarProduct)
                whereSql.append(" and target_type = ? ");
                args.add(matchType);
            }
        }

        sql.append(whereSql);
        return getJdbcTemplate(puid).queryForList(sql.toString(),args.toArray(),String.class);

    }

    @Override
    public List<String> queryAdSdAudienceTargetList(Integer puid, Integer shopId,
                                                    List<String> campaignIds,List<String> groupIds,String state,String targetType,String matchType, List<String> targetIdList) {
        StringBuilder selectSql = new StringBuilder("select d.target_id from t_amazon_ad_targeting_sd d left join t_amazon_ad_group_sd g on(d.puid=g.puid and d.shop_id = g.shop_id and d.ad_group_id = g.ad_group_id) ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where d.puid=? and d.shop_id=? and g.tactic = 'T00030'");
        argsList.add(puid);
        argsList.add(shopId);
        whereSql.append(SqlStringUtil.dealInList("target_id", targetIdList,argsList));
        if (StringUtils.isNotBlank(state)) {//状态
            if (state.equals("all")) {
                whereSql.append(" and d.state in ('enabled','paused')");
            } else {
                whereSql.append(" and d.state = ?");
                argsList.add(state);
            }
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) { //广告活动查询
            whereSql.append(SqlStringUtil.dealInList("g.campaign_id", campaignIds, argsList));
        }

        if (CollectionUtils.isNotEmpty(groupIds)) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("d.ad_group_id", groupIds, argsList));
        }

        if (StringUtils.isNotBlank(matchType)) {
            //受众投放类型(亚马逊消费者：audience ; 购买再营销：purchases ; 再营销浏览定向：views ;)
            whereSql.append(" and target_type = ? ");
            argsList.add(matchType);
        }

        selectSql.append(whereSql);
        Object[] args = argsList.toArray();
        return getJdbcTemplate(puid).queryForList(selectSql.toString(),args,String.class);
    }


    @Override
    public void updatePricing(Integer puid, Integer shopId, String targetId, Integer isPricing, Integer pricingState, int updateId) {
        String sql = "update t_amazon_ad_targeting_sd set is_pricing=?,pricing_state=?,update_id=?,update_time=now() where puid = ? and shop_id = ? and `target_id`=?";
        getJdbcTemplate(puid).update(sql,new Object[]{isPricing,pricingState,updateId,puid,shopId,targetId});
    }

    @Override
    public void strategyUpdate(int puid, int shopId, ScheduleTaskFinishedVo message) {
        String sql = "update `t_amazon_ad_targeting_sd` set bid=? where puid = ? and shop_id = ? and target_id=?";
        getJdbcTemplate(puid).update(sql, message.getModifiedValue(),message.getPuid(),message.getShopId(),message.getItemId());
    }

    @Override
    public List<AmazonSdAdTargeting> getByGroupIdsAndTargetTypes(Integer puid, List<String> groupIds, List<String> targetTypes) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inStrList("ad_group_id", groupIds.toArray(new String[0]))
                .inStrList("type", targetTypes.toArray(new String[0]))
                .build());
    }

    @Override
    public List<AmazonSdAdTargeting> getByGroupIdsAndTargetTypesNew(Integer puid, List<String> groupIds, List<String> targetTypes) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inStrList("ad_group_id", groupIds.toArray(new String[0]))
                .inStrList("type", targetTypes.toArray(new String[0]))
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()})
                .build());
    }

    @Override
    public List<AmazonSdAdTargeting> listByGroupIdsAndTargetText(Integer puid, List<String> groupIds, List<String> targetTexts) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inStrList("ad_group_id", groupIds.toArray(new String[0]))
                .inStrList("type", new String[] {"category","asin","similarProduct"})
                .inStrList("target_text", targetTexts.toArray(new String[0]))
                .build());
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public Page<SdTargetPageVo> getTargetPage(Integer puid, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlCountSb = new StringBuilder("select count(*) from (select target_id ");
        StringBuilder sqlSb = new StringBuilder("select ")
                .append(" id, puid, shop_id shopId, marketplace_id marketplaceId, ad_group_id adGroupId, target_id targetId,")
                .append(" state, type, target_text targetText, bid, expression_type expressionType, resolved_expression resolvedExpression, title,")
                .append(" img_url imgUrl, suggested, range_start rangeStart, range_end rangeEnd,serving_status servingStatus,")
                .append(" is_pricing isPricing, pricing_state pricingState, target_type targetType ");
        StringBuilder sb = new StringBuilder(" from ").append(this.getJdbcHelper().getTable());
        sb.append(this.getTargetPageWhereSql(puid, param, null, argsList));

        sqlSb.append(sb);
        sqlCountSb.append(sb).append(" ) c");

        if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType()) && SqlStringReportUtil.BID.equals(param.getOrderField())) {
            sqlSb.append(" order by bid ").append(OrderTypeEnum.desc.getType().equals(param.getOrderType()) ? "desc" : "").append(" ,id desc ");
        } else {
            sqlSb.append(" order by id desc ");
        }

        Object[] args = argsList.toArray();
        return getPageResultByClass(puid, param.getPageNo(), param.getPageSize(), sqlCountSb.toString(), args, sqlSb.toString(), args, SdTargetPageVo.class);
    }

    @Override
    public List<SdTargetPageVo> getTargetPageVoListByTargetIdList(Integer puid, TargetingPageParam param, List<String> targetIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ")
                .append(" id, puid, shop_id shopId, marketplace_id marketplaceId, ad_group_id adGroupId, target_id targetId,")
                .append(" state, type, target_text targetText, bid, expression_type expressionType, resolved_expression resolvedExpression, title,")
                .append(" img_url imgUrl, suggested, range_start rangeStart, range_end rangeEnd,serving_status servingStatus,")
                .append(" is_pricing isPricing, pricing_state pricingState, target_type targetType")
                .append(" from ").append(this.getJdbcHelper().getTable());
        sb.append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (CollectionUtils.isNotEmpty(targetIdList)) {
            sb.append(SqlStringUtil.dealInList("target_id", targetIdList, argsList))
                    .append(" order by field(target_id, ").append(StringUtil.joinString(targetIdList)).append(")");
        }
        return getJdbcTemplate(puid).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(SdTargetPageVo.class));
    }

    @Override
    public List<String> getTargetIdListByParam(Integer puid, TargetingPageParam param, List<String> targetIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select target_id from ").append(this.getJdbcHelper().getTable());
        sb.append(this.getTargetPageWhereSql(puid, param, targetIdList, argsList));
        return getJdbcTemplate(puid).queryForList(sb.toString(), argsList.toArray(), String.class);
    }

    @Override
    public List<AdOrderBo> getTargetIdAndOrderFieldList(Integer puid, TargetingPageParam param, List<String> targetIdList, String orderField) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select target_id id")
                .append(StringUtils.isNotBlank(orderField) ? "," + orderField + " orderField" : "")
                .append(" from ").append(this.getJdbcHelper().getTable());
        sb.append(this.getTargetPageWhereSql(puid, param, targetIdList, argsList));
        return getJdbcTemplate(puid).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AdOrderBo.class));
    }

    private String getTargetPageWhereSql(Integer puid, TargetingPageParam param, List<String> targetIdList, List<Object> argsList) {
        StringBuilder sb = new StringBuilder(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (CollectionUtils.isNotEmpty(targetIdList)) {
            sb.append(SqlStringUtil.dealInList("target_id", targetIdList, argsList));
        }
        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> groupList = StringUtil.splitStr(param.getGroupId(), ",");
            sb.append(SqlStringUtil.dealInList("ad_group_id", groupList, argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIdList())) { //广告组合查询
            sb.append(SqlStringUtil.dealInList("ad_group_id", param.getGroupIdList(), argsList));
        }

        //标签管理
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            sb.append(SqlStringUtil.dealInList("target_id", param.getTargetIds(), argsList));
        }

        //匹配新版SD投放融合
        List<String> tacticTargetList = Arrays.asList(SdTargetTypeShowEnum.VIEW_PURCHASE.getCode(),
                SdTargetTypeShowEnum.INMARKET_AUDIENCE.getCode(), SdTargetTypeShowEnum.AUDIENCE_CONTENT_CATEGORY.getCode());
        List<String> filterTargetTypeList;
        if (StringUtils.isNotBlank(param.getFilterTargetType()) && CollectionUtils.isNotEmpty(filterTargetTypeList = Arrays.asList(param.getFilterTargetType().split(",")))) {

            StringBuilder conditionBuilder = new StringBuilder();
            final boolean[] hasCondition = {false}; // 使用数组替代boolean变量

            // 添加条件的辅助方法
            Consumer<String> addCondition = condition -> {
                if (hasCondition[0]) {
                    conditionBuilder.append(" or ");
                }
                conditionBuilder.append(condition);
                hasCondition[0] = true;
            };

            if (tacticTargetList.parallelStream().anyMatch(filterTargetTypeList::contains)) {
                if (filterTargetTypeList.contains(SdTargetTypeShowEnum.VIEW_PURCHASE.getCode())) {
                    addCondition.accept("target_type in ('views', 'purchases') ");
                }
                if (filterTargetTypeList.contains(SdTargetTypeShowEnum.INMARKET_AUDIENCE.getCode())) {
                    addCondition.accept("type in ('In-market')  ");
                }
                if (filterTargetTypeList.contains(SdTargetTypeShowEnum.AUDIENCE_CONTENT_CATEGORY.getCode())){
                    //兴趣及生活方式受众(兴趣：Interest ; 内容分类：contentCategorySameAs ; 生活事件：Life event ; 生活方式：Lifestyle; 受众：audienceSameAs ; 受众： Audience)
                    addCondition.accept("type in ('Interest', 'contentCategorySameAs', 'Life event', 'Lifestyle', 'audienceSameAs', 'Audience') ");
                }
            } else {
                if (filterTargetTypeList.contains("asin")) {
                    addCondition.accept("type='asin' ");
                } else if (filterTargetTypeList.contains("category")) {
                    addCondition.accept("target_type = 'asinCategorySameAs'  ");
                } else if (filterTargetTypeList.contains("similarProduct")) {
                    //受众投放类型(亚马逊消费者：audience ; 购买再营销：purchases ; 再营销浏览定向：views ; 与推广商品相似：similarProduct)
                    addCondition.accept("target_type = 'similarProduct' ");
                }
            }

            // 只有存在有效条件时才添加到主SQL
            if (hasCondition[0]) {
                sb.append(" and (").append(conditionBuilder).append(")");
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                sb.append(" and target_text = ? and type = 'asin' ");
                argsList.add(param.getSearchValue());
            } else if ("category".equalsIgnoreCase(param.getSearchField())) {
                sb.append(" and target_text like ? and type = 'category' ");
                argsList.add("%" + param.getSearchValue() + "%");
            } else {
                sb.append(" and target_text like ? ");
                argsList.add("%" + param.getSearchValue() + "%");
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            sb.append(SqlStringUtil.dealInList("state", statusList, argsList));
        }

        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                sb.append(" and serving_status = ? ");
                argsList.add(AmazonSdAdTargeting.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                sb.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }
        }

        //竞价高级筛选
        if (param.getUseAdvanced()) {
            if (param.getBidMin() != null) {
                sb.append(" and bid >= ? ");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                sb.append(" and bid <= ? ");
                argsList.add(param.getBidMax());
            }
        }
        return sb.toString();
    }
    @Override
    public List<AmazonSdAdTargeting> listByGroupIdList(Integer puid, Integer shopId, List<String> groupIdList) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("ad_group_id", groupIdList.toArray(new String[]{}))
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()})
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonSdAdTargeting> listByGroupId(Integer puid, Integer shopId, String adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId)
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()})
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonSdAdTargeting> listByGroupIdAndItemIdList(Integer puid, Integer shopId, String adGroupId, List<String> itemIdList) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("shop_id", shopId);
        builder.equalTo("ad_group_id", adGroupId);
        if (CollectionUtils.isNotEmpty(itemIdList)) {
            builder.notIn("target_id", itemIdList.toArray());
        }
        builder.in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()});
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<String> getAdGroupIdsByTargetIds(int puid, List<String> targetIds) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.in("target_id", targetIds.toArray());
        return listDistinctFieldByCondition(puid, "ad_group_id",builder.build(),String.class);
    }

    @Override
    public List<AmazonSdAdTargeting> listByPuidAndShopIdLimit(Integer puid, Integer shopId, Long startIndex) {
        String sql = "select * from t_amazon_ad_targeting_sd where puid = ? and shop_id = ? and id > ? and (campaign_id is null or tactic_type is null or campaign_id='' and tactic_type='') order by id limit 2000";
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(startIndex);
        return getJdbcTemplate(puid).query(sql, getRowMapper(), argsList.toArray());
    }

    @Override
    public void updateTacticTypeAndCampaignId(int puid, List<AmazonSdAdTargeting> targetingList) {
        if (CollectionUtils.isEmpty(targetingList)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(targetingList.size());
        List<Object> arg;
        for (AmazonSdAdTargeting po : targetingList) {
            if (Objects.isNull(po.getCampaignId()) && Objects.isNull(po.getTacticType())) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(po.getCampaignId());
            arg.add(po.getTacticType());
            arg.add(po.getPuid());
            arg.add(po.getShopId());
            arg.add(po.getTargetId());
            argList.add(arg.toArray());
        }

        String sql = "update t_amazon_ad_targeting_sd set `campaign_id`=?, `tactic_type`=?, update_time=now(3)" +
                " where puid=? and shop_id=? and target_id=?";

        getJdbcTemplate(puid).batchUpdate(sql, argList);
    }

    @Override
    public List<PuidShopDto> distinctShopByPuid(Integer puid) {
        String sql = "select distinct puid, shop_id from t_amazon_ad_targeting_sd where puid = ?";

        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        return getJdbcTemplate(puid).query(sql, argsList.toArray(), new BeanPropertyRowMapper<>(PuidShopDto.class));
    }

    @Override
    public void autoUpdate(int puid, int shopId, AdvertiseRuleTaskExecuteRecordV2Message message) {
        StringBuilder updateSql = new StringBuilder("update ").append(this.getJdbcHelper().getTable());
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? and target_id = ?");
        List<Object> args = Lists.newArrayList();
        if ("restore".equals(message.getOperation().name())) {
            updateSql.append(" set bid=?");
            args.add(message.getRecoveryAdjustment().getExecuteValue());
        } else {
            if ("stateClose".equals(message.getPerformOperation().get(0).getRuleAction().name())) {
                updateSql.append(" set state=?");
                args.add(message.getPerformOperation().get(0).getExecuteValue());
            } else {
                updateSql.append(" set bid=?");
                args.add(message.getPerformOperation().get(0).getExecuteValue());
            }
        }
        updateSql.append(whereSql);
        args.add(message.getPuid());
        args.add(message.getShopId());
        args.add(message.getItemId());
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(message.getPuid()).update(updateSql.toString(),args.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public Page<AmazonSdAdTargeting> queryAutoRuleAdSdTarget(AdKeywordTargetAutoRuleParam param, List<String> itemIds, List<String> similarItemIdList, AutoRuleTargetTypeEnum targetTypeEnum) {
        StringBuilder selectSql = new StringBuilder("SELECT t.*, c.campaign_id campaignId FROM t_amazon_ad_targeting_sd t LEFT JOIN t_amazon_ad_group_sd g ON " +
                " t.puid=g.puid and t.shop_id = g.shop_id and t.ad_group_id = g.ad_group_id LEFT JOIN t_amazon_ad_campaign_all c ON " +
                " g.puid=c.puid and g.shop_id = c.shop_id and g.campaign_id = c.campaign_id ");
        StringBuilder countSql = new StringBuilder("select count(t.id) from t_amazon_ad_targeting_sd t LEFT JOIN t_amazon_ad_group_sd g ON " +
                " t.puid=g.puid and t.shop_id = g.shop_id and t.ad_group_id = g.ad_group_id LEFT JOIN t_amazon_ad_campaign_all c ON " +
                " g.puid=c.puid and g.shop_id = c.shop_id and g.campaign_id = c.campaign_id ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where t.puid=? ");
        argsList.add(param.getPuid());
        if (param.getShopId() != null) {
            whereSql.append(" and t.shop_id = ?");
            argsList.add(param.getShopId());
        }
        //过滤已存在的受控对象
        if (CollectionUtils.isNotEmpty(itemIds)) {
            whereSql.append(SqlStringUtil.dealNotInList("t.target_id", itemIds, argsList));
        }
        //相似规则处理
        if (param.getHasSimilarRule() != null) {
            if (param.getHasSimilarRule() == 0) {
                if (CollectionUtils.isNotEmpty(similarItemIdList)) {
                    whereSql.append(SqlStringUtil.dealNotInList("t.target_id", similarItemIdList, argsList));
                }
            } else if (param.getHasSimilarRule() == 1) {
                if (CollectionUtils.isNotEmpty(similarItemIdList)) {
                    whereSql.append(SqlStringUtil.dealInList("t.target_id", similarItemIdList, argsList));
                }
            }
        }
        //广告活动id
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }
        //服务状态
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            whereSql.append(SqlStringUtil.dealInList("t.serving_status", param.getServingStatusList(), argsList));
        }
        //广告组id
        if (CollectionUtils.isNotEmpty(param.getGroupIdList())) {
            whereSql.append(SqlStringUtil.dealInList("t.ad_group_id", param.getGroupIdList(), argsList));
        }
        //状态
        if (CollectionUtils.isNotEmpty(param.getStateList())) {
            whereSql.append(SqlStringUtil.dealInList("t.state", param.getStateList(), argsList));
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            whereSql.append(" and t.target_text like ? ");
            argsList.add("%" + param.getSearchValue().trim() + "%");
        }
        whereSql.append(" and c.state in ('enabled','paused') and g.state in ('enabled','paused') ");
        //商品投放
        if (AutoRuleTargetTypeEnum.productTarget == targetTypeEnum) {
            whereSql.append(" and REPLACE(JSON_EXTRACT(t.expression, '$[0].type'), '\"', '') NOT IN ('views', 'audience', 'purchases', 'contentCategorySameAs') ");
            //添加到否投，只查asin
            if (Objects.nonNull(param.getOperationType()) && AutoRuleOperationTypeEnum.addNotTarget.getOperationType() == param.getOperationType()) {
                whereSql.append(" and t.type ='asin' ");
            }
        //受众投放
        } else if (AutoRuleTargetTypeEnum.audienceTarget == targetTypeEnum) {
            whereSql.append(" and REPLACE(JSON_EXTRACT(t.expression, '$[0].type'), '\"', '') IN ('views', 'audience', 'purchases', 'contentCategorySameAs') ");
        }

        selectSql.append(whereSql);
        countSql.append(whereSql);

        return getPageByMapper(param.getPuid(), param.getPageNo(), param.getPageSize(),
                countSql.toString(), argsList.toArray(), selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AmazonSdAdTargeting.class));
    }

    @Override
    public List<AmazonSdAdTargeting> autoRuleTargetIdList(Integer puid, Integer shopId, List<String> campaignIds, List<String> groupIds, String state, String matchType, String targetType, String searchValue, List<String> sdTargetList, List<String> servingStatusList) {
        StringBuilder sql = new StringBuilder("SELECT t.* FROM t_amazon_ad_targeting_sd t ");

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append("LEFT JOIN t_amazon_ad_group_sd g ON t.puid=g.puid and t.shop_id = g.shop_id and t.ad_group_id = g.ad_group_id");
        }

        List<Object> args = new ArrayList<>();
        sql.append(" where t.puid = ? ");
        sql.append(" and t.shop_id = ? ");
        args.add(puid);
        args.add(shopId);
        if (CollectionUtils.isNotEmpty(sdTargetList)) {
            sql.append(SqlStringUtil.dealInList("t.target_id", sdTargetList, args));
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealInList("g.campaign_id", campaignIds, args));
        }
        if (CollectionUtils.isNotEmpty(groupIds)) {
            sql.append(SqlStringUtil.dealInList("t.ad_group_id", groupIds, args));
        }
        if (StringUtils.isNotBlank(state)) {
            sql.append(" and t.state = ? ");
            args.add(state);
        }
        if (StringUtils.isNotBlank(searchValue)) {
            sql.append(" and t.target_text like ? ");
            args.add("%" + searchValue + "%");
        }
        if (CollectionUtils.isNotEmpty(servingStatusList)) {
            sql.append(SqlStringUtil.dealInList("t.serving_status", servingStatusList, args));
        }

        if (AutoRuleTargetTypeEnum.productTarget.getTargetType().equals(targetType)) {
            sql.append(" and REPLACE(JSON_EXTRACT(t.expression, '$[0].type'), '\"', '') NOT IN ('views', 'audience', 'purchases', 'contentCategorySameAs') ");
        } else if (AutoRuleTargetTypeEnum.audienceTarget.getTargetType().equals(targetType)) {
            sql.append(" and REPLACE(JSON_EXTRACT(t.expression, '$[0].type'), '\"', '') IN ('views', 'audience', 'purchases', 'contentCategorySameAs') ");
        }
        return getJdbcTemplate(puid).query(sql.toString(), getRowMapper(), args.toArray());
    }

    @Override
    public List<String> queryAutoRuleTargetIdList(ProcessTaskParam param) {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.equalTo("puid", param.getPuid());
        conditionBuilder.equalTo("shop_id", param.getShopId());
        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) {
            conditionBuilder.in("campaign_id", param.getCampaignIds().toArray());
        }
        if (CollectionUtils.isNotEmpty(param.getGroupIds())) {
            conditionBuilder.in("ad_group_id", param.getGroupIds().toArray());
        }
        if (CollectionUtils.isNotEmpty(param.getItemIdList())) {
            conditionBuilder.in("target_id", param.getItemIdList().toArray());
        }
        if (StringUtils.isNotBlank(param.getState())) {
            conditionBuilder.equalTo("state", param.getState());
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            conditionBuilder.like("target_value", param.getSearchValue());
        }
        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            conditionBuilder.in("serving_status", param.getServingStatusList().toArray());
        }
        if (AutoRuleTargetTypeEnum.productTarget.getTargetType().equals(param.getTargetType())) {
            conditionBuilder.appendSql(" and REPLACE(JSON_EXTRACT(expression, '$[0].type'), '\"', '') NOT IN ('views', 'audience', 'purchases', 'contentCategorySameAs') ", true);
        } else if (AutoRuleTargetTypeEnum.audienceTarget.getTargetType().equals(param.getTargetType())) {
            conditionBuilder.appendSql(" and REPLACE(JSON_EXTRACT(expression, '$[0].type'), '\"', '') IN ('views', 'audience', 'purchases', 'contentCategorySameAs') ", true);
        }
        return listDistinctFieldByCondition(param.getPuid(), "target_id", conditionBuilder.build(), String.class);
    }

    @Override
    public List<String> getDiagnoseCountTargetId(DiagnoseCountParam param, boolean isAsinTarget) {
        StringBuilder selectSql = new StringBuilder("select target_id from " + getJdbcHelper().getTable());
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", param.getPuid())
                .in("shop_id", param.getShopIdList().toArray());
        //广告组ID查询
        if (CollectionUtils.isNotEmpty(param.getAdGroupIdList())) {
            builder.in("ad_group_id", param.getAdGroupIdList().toArray());
        }
        if (isAsinTarget) {
            builder.appendSql(" and REPLACE(JSON_EXTRACT(expression, '$[0].type'), '\"', '') NOT IN ('views', 'audience', 'purchases', 'contentCategorySameAs') ", true);
        } else {
            builder.appendSql(" and REPLACE(JSON_EXTRACT(expression, '$[0].type'), '\"', '') IN ('views', 'audience', 'purchases', 'contentCategorySameAs') ", true);
        }
        // 限制10万
        builder.limit(Constants.TOTALSIZELIMIT);
        ConditionBuilder build = builder.build();
        selectSql.append(" where ").append(build.getSql());
        return getJdbcTemplate(param.getPuid()).query(selectSql.toString(), new SingleColumnRowMapper<String>(), build.getValues());
    }

    @Override
    public List<String> listIdByTacticType(Integer puid, Integer shopId, List<String> adGroupId, List<String> tacticTypeList) {
        StringBuilder selectSql = new StringBuilder("select target_id from " + getJdbcHelper().getTable());
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId);
        //广告组ID查询
        if (CollectionUtils.isNotEmpty(adGroupId)) {
            builder.in("ad_group_id", adGroupId.toArray());
        }
        if (CollectionUtils.isNotEmpty(tacticTypeList)) {
            builder.in("tactic_type", tacticTypeList.toArray());
        }
        // 限制10万
        builder.limit(Constants.TOTALSIZELIMIT);
        ConditionBuilder build = builder.build();
        selectSql.append(" where ").append(build.getSql());
        return getJdbcTemplate(puid).query(selectSql.toString(), new SingleColumnRowMapper<String>(), build.getValues());    }

    @Override
    public List<AsinLibsDetailVo> getSdAsinDetailDataByTargetId(Integer puid, AsinLibsDetailParam param, List<String> sdTargetIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuffer selectSql = new StringBuffer("SELECT id, puid, target_id targetId, shop_id shopId, marketplace_id marketplaceId, type matchType, 'sd' type, " +
                "campaign_id campaignId, ad_group_id adGroupId, target_text targetingText, create_time createTime, bid, state");
        selectSql.append(" from ").append(getJdbcHelper().getTable()).append(" where ");
        selectSql.append(" puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
            selectSql.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCountry())) {
            selectSql.append(SqlStringUtil.dealInList("marketplace_id", param.getCountry(), argsList));
        }
        if (CollectionUtils.isNotEmpty(sdTargetIds)) {
            selectSql.append(SqlStringUtil.dealInList("target_id", sdTargetIds, argsList));
        }
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AsinLibsDetailVo.class));
    }

    @Override
    public List<AmazonSdAdTargeting> getArchivedTargetingByAdTargetIds(Integer puid, Integer shopId, List<String> targetIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("target_id", targetIds.toArray())
                .equalTo("state", "archived")
                .build();
        return listByCondition(puid,conditionBuilder);
    }

    @Override
    public List<DownloadCenterSdTargetBaseDataBO> queryBaseData4DownloadByTargetIdList(Integer puid, Integer shopId, List<String> targetIdList) {
        StringBuilder sql = new StringBuilder("select target_id id, target_text targetText, state from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? ");
        sql.append(" and shop_id = ? ");
        sql.append(" and target_id in ('").append(StringUtils.join(targetIdList, "','")).append("')");
        return getJdbcTemplate(puid).query(sql.toString(), new BeanPropertyRowMapper<>(DownloadCenterSdTargetBaseDataBO.class), puid, shopId);
    }

    private StringBuilder buildAsinTargetViewWhereSql(Integer puid, TargetViewParam param, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid=?  ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {  //店铺
            whereSql.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), argsList));
        }
        //广告组ID、广告活动、广告组合查询
        if (CollectionUtils.isNotEmpty(param.getAdGroupIdList())) {
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", param.getAdGroupIdList(), argsList));
        }
        //选中的列
        if (StringUtils.isNotBlank(param.getTargetText())) {
            whereSql.append(" and target_text = ? ");
            argsList.add(param.getTargetText());
        }
        whereSql.append(" and REPLACE(JSON_EXTRACT(expression, '$[0].type'), '\"', '') NOT IN ('views', 'audience', 'purchases', 'contentCategorySameAs') ");
        if (StringUtils.isNotBlank(param.getTargetType())) {
            List<String> targetTypeList = StringUtil.splitStr(param.getTargetType(), ",");
            whereSql.append(SqlStringUtil.dealInList("type", targetTypeList, argsList));
        }
        //asin、类目名称筛选
        if (StringUtils.isNotBlank(param.getQueryValue()) && StringUtils.isNotBlank(param.getQueryType())
                && StringUtils.isNotBlank(param.getQueryField()) && (Constants.TARGETING_TYPE_ASIN.equals(param.getQueryField()) || Constants.TARGETING_TYPE_CATEGORY.equals(param.getQueryField()))) {
            if (Constants.TARGETING_TYPE_ASIN.equalsIgnoreCase(param.getQueryField())) {
                whereSql.append(" and type = 'asin' ");
            } else {
                whereSql.append(" and type = 'category' ");
            }
            if (SearchTypeEnum.EXACT.getValue().equals(param.getQueryType())) {
                if (param.getQueryValue().contains(StringUtil.SPECIAL_COMMA)) {
                    whereSql.append(SqlStringUtil.dealInList("target_text",
                            StringUtil.splitStr(param.getQueryValue(), StringUtil.SPECIAL_COMMA), argsList));
                } else {
                    whereSql.append(" and target_text = ? ");
                    argsList.add(param.getQueryValue().trim());
                }
            } else {
                whereSql.append(" and target_text like ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(param.getQueryValue().trim()) + "%");
            }
        }
        //状态
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, argsList));
        }
        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ? ");
                argsList.add(AmazonSdAdTargeting.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }
        }
        //筛选状态
        if (StringUtils.isNotBlank(param.getSelectType())) {
            List<String> selectTypeList = StringUtil.splitStr(param.getSelectType(), ",");
            if (selectTypeList.contains(AdTargetTaskMatchTypeEnum.ASIN_EXPANDED_FROM.getCode())) {
                whereSql.append(" and 1=0 ");
            }
        }
        return whereSql;
    };

    private StringBuilder buildAudienceTargetViewWhereSql(Integer puid, AudienceTargetViewParam param, boolean isAggregate, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid=?  ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {  //店铺
            whereSql.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), argsList));
        }
        //广告组ID、广告活动、广告组合查询
        if (CollectionUtils.isNotEmpty(param.getAdGroupIdList())) {
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", param.getAdGroupIdList(), argsList));
        }
        if (isAggregate) {
            //汇总接口用
            if (StringUtils.isNotBlank(param.getTargetType())) {
                List<String> targetTypeList = StringUtil.splitStr(param.getTargetType(), ",");
                whereSql.append(SqlStringUtil.dealInList("target_type", targetTypeList, argsList));
            }
        } else {
            //列表接口用，选中的列
            if (StringUtils.isNotBlank(param.getTargetType()) && StringUtils.isNotBlank(param.getTargetText())) {
                whereSql.append(" and target_type = ? ");
                argsList.add(param.getTargetType());
                if (SDTargetingTargetTypeEnum.AUDIENCE.getType().equals(param.getTargetType())) {
                    whereSql.append(" and type = ? ");
                    argsList.add(param.getTargetText());
                } else {
                    whereSql.append(" and target_text = ? ");
                    argsList.add(param.getTargetText());
                }
            }
        }

        whereSql.append(" and REPLACE(JSON_EXTRACT(expression, '$[0].type'), '\"', '') IN ('views', 'audience', 'purchases', 'contentCategorySameAs') ");
        //受众分类筛选
        if (StringUtils.isNotBlank(param.getQueryValue())) {
            whereSql.append(" and target_text like ? ");
            argsList.add("%" + param.getQueryValue().trim() + "%");
        }
        //状态
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, argsList));
        }
        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ? ");
                argsList.add(AmazonSdAdTargeting.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }
        }
        return whereSql;
    }
}