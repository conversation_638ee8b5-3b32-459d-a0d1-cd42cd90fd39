package com.meiyunji.sponsored.service.export.constants;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-07-17  18:47
 * 导出handler常量
 */
public class AdManagePageExportTaskConstant {
    public static final String KEYWORD = "keywordPageExportTaskHandler";
    public static final String TARGET = "targetPageExportTaskHandler";
    public static final String CAMPAIGN = "campaignPageExportTaskHandler";
    public static final String NE_TARGET = "neTargetPageExportTaskHandler";
    public static final String PLACEMENT = "placementPageExportTaskHandler";
    public static final String GROUP = "groupPageExportTaskHandler";
    public static final String PRODUCT = "productPageExportTaskHandler";
    public static final String PORTFOLIO = "portfolioPageExportTaskHandler";
    public static final String QUERY_KEYWORD = "queryKeywordPageExportTaskHandler";
    public static final String QUERY_ASIN = "queryAsinPageExportTaskHandler";
    public static final String CAMPAIGN_NE_KEYWORD = "campaignNeKeywordPageExportTaskHandler";
    public static final String CAMPAIGN_NE_TARGET = "campaignNeTargetPageExportTaskHandler";
    public static final String GROUP_NE_KEYWORD = "groupNeKeywordPageExportTaskHandler";
    public static final String GROUP_NE_TARGET = "groupNeTargetPageExportTaskHandler";
    public static final String AD_LOG = "adLogExportTaskHandler";
    public static final String NE_KEYWORD = "NeKeywordPageExportTaskHandler";
    public static final String ALL_SEARCH_TERM = "allSearchTermExportTaskHandler";
    public static final String REPEAT_TARGETING = "RepeatTargetingPageExportTaskHandler";
    public static final String BUDGET_ANALYSIS = "budgetAnalysisExportTaskHandler";
    public static final String CAMPAIGN_MULTIPLE = "campaignMultiplePageExportTaskHandler";

    public static final String MULTIPLE_PORTFOLIO = "multiplePortfolioPageExportTaskHandler";
    public static final String AMC_QUERY_WORD_AD_SPACE = "amcQueryWordAdSpaceExportTaskHandler";

    public static final String PERSPECTIVE_CAMPAIGN_VIEW = "perspectiveCampaignViewExportTaskHandler";
    public static final String PERSPECTIVE_KEYWORD_VIEW_AGGREGATE = "perspectiveKeywordAggregateViewExportTaskHandler";
    public static final String PERSPECTIVE_KEYWORD_VIEW = "perspectiveKeywordViewExportTaskHandler";
    public static final String PERSPECTIVE_TARGET_VIEW_AGGREGATE = "perspectiveTargetAggregateViewExportTaskHandler";
    public static final String PERSPECTIVE_TARGET_VIEW = "perspectiveTargetViewExportTaskHandler";
    public static final String PERSPECTIVE_AUTO_TARGET_VIEW = "perspectiveAutoTargetViewExportTaskHandler";
    public static final String PERSPECTIVE_PLACEMENT_VIEW_AGGREGATE = "perspectivePlacementAggregateViewExportTaskHandler";
    public static final String PERSPECTIVE_PLACEMENT_VIEW = "perspectivePlacementViewExportTaskHandler";
    public static final String PERSPECTIVE_AUDIENCE_TARGET_VIEW_AGGREGATE = "perspectiveAudienceTargetAggregateViewExportTaskHandler";
    public static final String PERSPECTIVE_AUDIENCE_TARGET_VIEW = "perspectiveAudienceTargetViewExportTaskHandler";
    public static final String PERSPECTIVE_SEARCH_TERMS_VIEW = "perspectiveSearchTermsViewExportTaskHandler";
    public static final String POST = "postExportTaskHandler";
    public static final String TARGET_MULTIPLE = "targetMultiplePageExportTaskHandler";
}
