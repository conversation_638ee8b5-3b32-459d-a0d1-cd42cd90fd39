package com.meiyunji.sponsored.service.cpc.service2.sp.impl;

import com.alibaba.fastjson.JSON;
import com.amazon.advertising.spV3.group.CreateSpGroupV3Response;
import com.amazon.advertising.spV3.group.GroupSpV3Client;
import com.amazon.advertising.spV3.group.entity.GroupEntityV3;
import com.amazon.advertising.spV3.group.entity.GroupSuccessResultV3;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.rpc.sp.campaign.GroupResp;
import com.meiyunji.sponsored.rpc.sp.campaign.ProductInfoResp;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dto.CpcProductDto;
import com.meiyunji.sponsored.service.cpc.dto.CreateGroupResultDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaign;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdGroupService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcProductService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AddAdProductVo;
import com.meiyunji.sponsored.service.cpc.vo.ProductVo;
import com.meiyunji.sponsored.service.cpc.vo.SPadGroupVo;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdGroup;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.WxNotificationUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.util.WxNotificationUtil.CREATE_SP_ADS_WX_URL;

/**
 * SpGroupService
 *
 * @Author: hejh
 * @Date: 2024/7/12 13:32
 */
@Service
@Slf4j
public class SpGroupService {
    @Autowired
    ICpcAdGroupService cpcAdGroupService;
    @Autowired
    private ICpcProductService cpcProductService;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Resource
    private IAdManageOperationLogService adManageOperationLogService;
    @Resource
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Resource
    private IDorisService dorisService;

    /**
     * 创建sp广告组
     *
     * @param vo SPadGroupVo
     * @return
     */
    public GroupResp createSpGroup(SPadGroupVo vo, ShopAuth shop, AmazonAdProfile amazonAdProfile) {
        GroupResp.Builder builder = GroupResp.newBuilder();
        //1，参数校验
        String err = checkCreateCampaignParams(vo);
        if (StringUtils.isNotBlank(err)) {
            builder.setCode(Result.ERROR);
            builder.setGroupErrMsg(err);
            return builder.build();
        }
        //2，创建广告组
        Result<String> result = cpcAdGroupService.createAdGroupWithAuthed(vo, shop);
        if (StringUtils.isNotBlank(result.getMsg())) {
            builder.setCode(Result.ERROR);
            builder.setGroupErrMsg(result.getMsg());
        } else {
            builder.setCode(Result.SUCCESS);
            String groupId = String.valueOf(result.getData());
            builder.setAdGroupId(groupId);

            //3,添加广告产品
            CompletableFuture.runAsync(() -> {
                List<ProductVo> products = vo.getProducts();
                if (CollectionUtils.isEmpty(products)) {
                    return;
                }
                List<CpcProductDto> cpcProductDtos = new ArrayList<>(products.size());
                for (ProductVo productVo : products) {
                    CpcProductDto cpcProductDto = new CpcProductDto();
                    cpcProductDto.setAsin(productVo.getAsin());
                    cpcProductDto.setSku(productVo.getSku());
                    cpcProductDtos.add(cpcProductDto);
                }
                Result<List<ProductInfoResp>> listResult = cpcProductService.addProductToGroup(shop, vo.getCampaignId(), groupId, cpcProductDtos, vo.getLoginIp(), vo.getUid(), amazonAdProfile);
                printWxAlarmFofProducts(listResult, vo.getCampaignId(), groupId, shop);
            }, ThreadPoolUtil.getCreateAdForSyncExecutor()).exceptionally(e -> {
                log.error("create group, add product error:", e);
                return null;
            });
        }
        return builder.build();
    }

    private void printWxAlarmFofProducts(Result<List<ProductInfoResp>> listResult, String campaignId, String groupId, ShopAuth shop) {
        if (listResult.error()) {
            String msg = String.format("新版创建广告：创建广告产品失败，失败原因：%s，puid：%d，shopId：%d，活动id：%s，广告组id：%s", listResult.getMsg(), shop.getPuid(), shop.getId(), campaignId, groupId);
            WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, msg);
            return;
        }
        List<ProductInfoResp> data = listResult.getData();
        if (CollectionUtils.isNotEmpty(data)) {
            boolean print = false;
            StringBuilder stringBuilder = new StringBuilder();
            String msg = String.format("新版创建广告：创建广告产品失败，puid：%d，shopId：%d，活动id：%s，广告组id：%s \nindex------失败原因", shop.getPuid(), shop.getId(), campaignId, groupId);
            stringBuilder.append(msg);
            for (ProductInfoResp productInfoResp : data) {
                if (productInfoResp != null && productInfoResp.getCode() != ResultUtil.SUCCESS) {
                    print = true;
                    stringBuilder.append("\n").append(productInfoResp.getIndex()).append("------").append(productInfoResp.getErrMsg());
                }
            }
            if (print) {
                WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, stringBuilder.toString());
            }
        }
    }

    private String checkCreateCampaignParams(SPadGroupVo vo) {
        if (vo == null) {
            return "请求参数为空";
        }
        if (StringUtils.isBlank(vo.getCampaignId())) {
            return "广告活动为空";
        }
        if (StringUtils.isBlank(vo.getName())) {
            return "广告组名称为空";
        }
        if (vo.getDefaultBid() == null) {
            return "默认竞价为空";
        }
        if (vo.getDefaultBid() <= 0) {
            return "默认竞价必须大于0";
        }
        if (CollectionUtils.isEmpty(vo.getProducts())) {
            return "推广的广告产品为空";
        }
        if (vo.getName().length() > AmazonAdGroup.nameLimit) {
            return MessageFormat.format("广告组名称不能超过{0}个字符", AmazonAdCampaign.nameLimit);
        }
        return null;
    }

    public void addProduct(SPadGroupVo vo, String groupId) {
        cpcProductService.addProduct(AddAdProductVo.builder()
                .products(vo.getProducts())
                .groupId(groupId)
                .puid(vo.getPuid())
                .shopId(vo.getShopId())
                .uid(vo.getUid())
                .build(), vo.getLoginIp());
    }

    /**
     * 批量创建Sp广告组
     */
    public List<CreateGroupResultDto> createBatchGroup(List<SPadGroupVo> groupVoList, ShopAuth shop) {
        if (CollectionUtils.isEmpty(groupVoList)) {
            return new ArrayList<>();
        }
        // 获取活动信息
        AmazonAdCampaignAll amazonAdCampaign = amazonAdCampaignDao.getCampaignByCampaignId(shop.getPuid(), shop.getId(), groupVoList.get(0).getCampaignId(), Constants.SP);
        // 封装数据库实体类
        List<AmazonAdGroup> adGroupList = convertVoToCreatePo(groupVoList, amazonAdCampaign);
        // 调用亚马逊api接口
        CreateSpGroupV3Response response = getCreateSpGroupV3Response(groupVoList, shop, adGroupList, amazonAdCampaign);
        if (response == null || response.getData() == null || response.getData().getAdGroups() == null) {
            log.warn("sp copy create, create group response none data, fail all, response: {}", JSON.toJSONString(response));
            return new ArrayList<>();
        }
        // 落库 返回值 原group
        return saveDbGroupAndLog(groupVoList, response, adGroupList);
    }

    /**
     * 调用亚马逊api接口
     */
    private CreateSpGroupV3Response getCreateSpGroupV3Response(List<SPadGroupVo> groupVoList, ShopAuth shop, List<AmazonAdGroup> adGroupList, AmazonAdCampaignAll amazonAdCampaign) {
        // 封装api接口请求实体类
        List<GroupEntityV3> groupEntityV3List = new ArrayList<>(groupVoList.size());
        adGroupList.forEach(x -> {
            GroupEntityV3 groupEntityV3 = cpcAdGroupService.makeAdGroupByAdGroupPo(x);
            groupEntityV3List.add(groupEntityV3);
        });
        // 调用亚马逊api接口请求
        CreateSpGroupV3Response response = GroupSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createGroups(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                amazonAdCampaign.getMarketplaceId(), groupEntityV3List, true);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = GroupSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createGroups(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                    amazonAdCampaign.getMarketplaceId(), groupEntityV3List, true);
        }
        return response;
    }

    private List<CreateGroupResultDto> saveDbGroupAndLog(List<SPadGroupVo> groupVoList, CreateSpGroupV3Response response, List<AmazonAdGroup> adGroupList) {
        // 封装参数
        List<CreateGroupResultDto> resultList = new ArrayList<>();
        List<AmazonAdGroup> successGroupList = new ArrayList<>();
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        List<GroupSuccessResultV3> successList = response.getData().getAdGroups().getSuccess();
        List<ErrorItemResultV3> errorList = response.getData().getAdGroups().getError();
        Date date = new Date();
        for (GroupSuccessResultV3 success : successList) {
            AmazonAdGroup adGroup = adGroupList.get(success.getIndex());
            adGroup.setAdGroupId(success.getAdGroupId());
            adGroup.setCreateTime(date);
            adGroup.setUpdateTime(date);
            successGroupList.add(adGroup);
            AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdGroupLog(null, adGroup);
            adManageOperationLog.setIp(groupVoList.get(0).getLoginIp());
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            adManageOperationLog.setAdGroupId(success.getAdGroupId());
            adManageOperationLogs.add(adManageOperationLog);
            CreateGroupResultDto resultDto = new CreateGroupResultDto();
            resultDto.setName(adGroup.getName());
            resultDto.setResult(Boolean.TRUE);
            resultDto.setGroupId(success.getAdGroupId());
            resultList.add(resultDto);
        }
        for (ErrorItemResultV3 error : errorList) {
            AmazonAdGroup adGroup = adGroupList.get(error.getIndex());
            String errorType = error.getErrors().get(0).getErrorType();
            String msg = errorType;
            try {
                msg = String.valueOf(error.getErrors().get(0).getErrorValue().get(errorType).get("message"));
                log.warn("sp batch create group error name:{} msg:{}", adGroup.getName(), msg);
            } catch (Exception e) {
                log.error("sp batch create group get error message exception", e);
            }
            AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdGroupLog(null, adGroup);
            adManageOperationLog.setIp(groupVoList.get(0).getLoginIp());
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(msg);
            adManageOperationLogs.add(adManageOperationLog);
            CreateGroupResultDto resultDto = new CreateGroupResultDto();
            resultDto.setName(adGroup.getName());
            resultDto.setResult(Boolean.FALSE);
            resultDto.setFailMsg(msg);
            resultList.add(resultDto);
        }
        if (CollectionUtils.isNotEmpty(successGroupList)) {
            // 批量保存adGroup数据
            amazonAdGroupDao.insertList4BatchCreate(groupVoList.get(0).getPuid(), successGroupList);
            // 写入doris
            saveDoris(successGroupList);
        }
        // 写入日志
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
        return resultList;
    }

    /**
     * routine load写入doris
     *
     * @param adGroupList
     */
    private void saveDoris(List<AmazonAdGroup> adGroupList) {
        try {
            Date date = new Date();
            List<OdsAmazonAdGroup> collect = adGroupList.stream().map(x -> {
                OdsAmazonAdGroup odsAmazonAdGroup = new OdsAmazonAdGroup();
                BeanUtils.copyProperties(x, odsAmazonAdGroup);
                odsAmazonAdGroup.setCreateTime(date);
                odsAmazonAdGroup.setUpdateTime(date);
                if (StringUtils.isNotBlank(odsAmazonAdGroup.getState())) {
                    odsAmazonAdGroup.setState(odsAmazonAdGroup.getState().toLowerCase());
                }
                return odsAmazonAdGroup;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sp group save doris error", e);
        }
    }

    // 创建活动时vo->po
    private List<AmazonAdGroup> convertVoToCreatePo(List<SPadGroupVo> groupVoList, AmazonAdCampaignAll amazonAdCampaign) {
        List<AmazonAdGroup> adGroupList = new ArrayList<>(groupVoList.size());
        for (SPadGroupVo vo : groupVoList) {
            AmazonAdGroup amazonAdGroup = new AmazonAdGroup();
            amazonAdGroup.setPuid(vo.getPuid());
            amazonAdGroup.setShopId(amazonAdCampaign.getShopId());
            amazonAdGroup.setMarketplaceId(amazonAdCampaign.getMarketplaceId());
            amazonAdGroup.setCampaignId(amazonAdCampaign.getCampaignId());
            amazonAdGroup.setProfileId(amazonAdCampaign.getProfileId());
            amazonAdGroup.setName(vo.getName().trim());
            amazonAdGroup.setDefaultBid(vo.getDefaultBid());
            amazonAdGroup.setAdGroupType(Constants.AUTO.equals(amazonAdCampaign.getTargetingType()) ? Constants.GROUP_TYPE_AUTO : null);
            amazonAdGroup.setState(vo.getState());
            amazonAdGroup.setCreateId(vo.getUid());
            adGroupList.add(amazonAdGroup);
        }
        return adGroupList;
    }

}
