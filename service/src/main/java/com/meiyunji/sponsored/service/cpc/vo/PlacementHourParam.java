package com.meiyunji.sponsored.service.cpc.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-07-19  14:27
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PlacementHourParam {

    @ApiModelProperty(value = "shopId",required = true)
    private Integer shopId;

    @ApiModelProperty("puid")
    private Integer puid;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("是否对比")
    private Integer isCompare;

    @ApiModelProperty("对比开始时间")
    private String startDateCompare;

    @ApiModelProperty("对比结束时间")
    private String endDateCompare;

    @ApiModelProperty("结束时间")
    private String weeks;

    @ApiModelProperty("广告活动")
    private String campaignId;

    @ApiModelProperty("广告位")
    private String Predicate;

    @ApiModelProperty("排序字段")
    private String orderField;

    @ApiModelProperty("排序类型")
    private String orderType;

    @ApiModelProperty("asin、msku、parentAsin（用于产品透视分析页面）")
    private String findType;
    private String findValue;

    @ApiModelProperty("页面请求唯一标识")
    private String pageSign;

    private String campaignSite;

    public enum CompareEnum {
        TRUE(1, "对比"),
        FALSE(2, "不对比");
        private Integer code;
        private String desc;

        CompareEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    @Override
    public String toString() {
        return "PlacementHourParam{" +
                "shopId=" + shopId +
                ", puid=" + puid +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                ", isCompare=" + isCompare +
                ", startDateCompare='" + startDateCompare + '\'' +
                ", endDateCompare='" + endDateCompare + '\'' +
                ", weeks='" + weeks + '\'' +
                ", campaignId='" + campaignId + '\'' +
                ", Predicate='" + Predicate + '\'' +
                ", orderField='" + orderField + '\'' +
                ", orderType='" + orderType + '\'' +
                ", pageSign='" + pageSign + '\'' +
                '}';
    }
}
