package com.meiyunji.sponsored.service.productPerspectiveAnalysis.service;

import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.asinInfoQuery.AllAsinListResponseVo;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.asinInfoQuery.AsinListResponseVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AsinOrderNumDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AsinListReqVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.BatchSpInitAsinVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.InitAsinInfoReqVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.InitAsinVo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: sun<PERSON><PERSON>
 * @email: sun<PERSON><PERSON>@dianxiaomi.com
 * @date: 2023-08-31  19:15
 */
public interface IAsinInfoQueryService {

    InitAsinVo getInitAsinInfo(InitAsinInfoReqVo reqVo);

    AsinListResponseVo getAsinList(AsinListReqVo reqVo);

    AllAsinListResponseVo getAllAsinList(AsinListReqVo reqVo);

    /**
     * 此版本 先不做跨店铺
     * @param reqVo
     * @return
     */
    AsinListResponseVo getAsinAllList(AsinListReqVo reqVo);

    /**
     * 出单搜索词使用，查询sp sb的推广产品，以推广产品进行聚合，并组装价格、星级和评分数
     * @param reqVo
     * @return
     */
    AsinListResponseVo getAdProducts(AsinListReqVo reqVo);

    InitAsinVo getBatchSpTaskInitAsinInfo(InitAsinInfoReqVo reqVo);

    /**
     * 查询某个puid下的透视分析初始化数据-最终的初始化asin
     * @param reqVo
     * @param now
     * @param days
     * @param partition
     * @return
     */
    InitAsinVo getTopOrderNumAsinLastSomeDays(InitAsinInfoReqVo reqVo, LocalDateTime now, Integer days, int partition);

    /**
     * 查询各店铺下的透视分析初始化数据-店铺维度的订单量最高asin
     * @param reqVo
     * @param now
     * @param days
     * @param partition
     * @return
     */
    List<AsinOrderNumDto> getOrderNumAsinForShopList(InitAsinInfoReqVo reqVo, LocalDateTime now, Integer days, int partition);
}
