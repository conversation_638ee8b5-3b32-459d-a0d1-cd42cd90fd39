package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProductReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSdProductReport;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by lm on 2021/5/14.
 *
 */
public interface IAmazonAdSdProductReportDao extends IBaseShardingDao<AmazonAdSdProductReport> {

    /**
     * 批量插入、更新
     * @param puid
     * @param list
     */
    void insertOrUpdateList(Integer puid, List<AmazonAdSdProductReport> list);

    void insertDorisList(Integer puid, List<AmazonAdSdProductReport> list);

    Page getPageList(Integer puid, SearchVo search, Page page);

    Page detailPageList(Integer puid, Integer shopId, String marketplaceId, ReportParam param , Page page);

    AmazonAdSdProductReport getSumReport(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, ReportParam param);

    List<AmazonAdSdProductReport> getChartList(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, ReportParam param);

    List<Map<String, Object>> getCampaignOrAdGroupNames(int puid, Integer shopId, String marketplaceId, String campaignId);

    /**
     * 推送cpc任务数据
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param startDate
     * @param endDate
     * @return
     */
    List<AmazonAdSdProductReport> getTaskSumReport(Integer puid, Integer shopId, String marketplaceId, String startDate, String endDate);

    List<AmazonAdSdProductReport> listSumReports(Integer puid, Integer shopId, String startDate, String endDate, List<String> adIds);

    List<AmazonAdSdProductReport> listReports(Integer puid, Integer shopId, String startDate, String endDate, String adId);

    AmazonAdSdProductReport getSkuSumData(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String msku);


    List<AmazonAdSdProductReport> getListSkuSumData(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, List<String> msku);

    List<AmazonAdSdProductReport> getListByDate(Integer puid, Integer shopId, String marketplaceId, String startTime, String endTime, String filed);

    List<AmazonAdSdProductReport> getListByParentAsinIsNull(Integer puid, Integer shopId, String sku, Date date);

    List<AmazonAdSdProductReport> getProductListByParentAsinIsNull(Integer puid, Integer shopId, Date date);

    void batchUpdateParentAsin(Integer puid, Integer shopId, List<AmazonAdSdProductReport> newList);

    List<AdHomePerformancedto> listSumReportByAdIds(Integer puid, Integer shopId, String startStr, String endStr, AdProductPageParam param, List<String> adIds);

    List<AdHomePerformancedto> listLatestReports(Integer puid, Integer shopId, List<String> adIds, boolean isAggregation);

    List<AdHomePerformancedto> getSdReportByDate(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, AdProductPageParam param);

    List<AdHomePerformancedto> getSdReportByDate(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, AdProductPageParam param, boolean isLatest);

    List<AdHomePerformancedto> getSdReportByAdIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> adIdList);

    /**
     *
     * @param puid
     * @param shopId
     * @param date  更新数据前的时间
     * @return
     */
    List<String> getProductListByUpdateTime(Integer puid, Integer shopId, Date date);

    /**
     * 同步报告汇总
     * @param puid
     * @param shopId
     * @return
     */
    List<AmazonAdSdProductReport> getListReportByDate(Integer puid, Integer shopId, String startDate, String endDate);

    /**
     * 获取已购买ASIN页面的产品数据
     * @param param
     * @return
     */
    List<AdAsinProductDto> getAsinPageSdProductList(AdAsinPageParam param, List<String> asinList);

    /**
     * 取已购买ASIN页面的汇总数据
     * @param param
     * @param asinList
     * @return
     */
    List<AdAsinAggregateDto> getAsinAggregateDto(AdAsinPageParam param, List<String> asinList);

    List<AmazonAdSdProductReport> getReportByAdId(Integer puid, Integer shopId, String startDate, String endDate, String marketplaceId, String adId);

    List<AmazonAdSdProductReport> getSales(Integer puid, Integer shopId, String startDate, String endDate,List<String> asins, List<String> campaignIds);

    List<AmazonAdSdProductReport> listReportsByAdIdsGroupByCountDate(int puid, Integer shopId, String marketplaceId, String startDate, String endDate, List<String> adIds);


    List<String> getAdIdsByProduct(int puid, int shopId, String marketplaceId, String start, List<String> adId);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

    List<InvoiceProductDto> getInvoiceProductList(Integer puid, Integer shopId, String marketplaceId, List<String> campaignId, String start, String end);

    LocalDateTime getInvoiceMaxUpdateTime(Integer puid, Integer shopId, String marketplaceId, List<String> campaignId, String start, String end);
}
