package com.meiyunji.sponsored.service.cpc.service2.sb.impl;

import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.amazon.advertising.sb.entity.bidRecommendation.BidRecommendationResult;
import com.amazon.advertising.sb.entity.bidRecommendation.RecommendedBid;
import com.amazon.advertising.sb.entity.bidRecommendation.SBTargetingExpressions;
import com.amazon.advertising.sb.entity.bidRecommendation.TargetsBidsSuccessResults;
import com.amazon.advertising.sb.entity.pageAisn.PageAsinsResult;
import com.amazon.advertising.sb.entity.targetingRecommendation.*;
import com.amazon.advertising.sb.mode.ads.AdLandingPage;
import com.amazon.advertising.sb.mode.targeting.Targeting;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadras.types.enumeration.SpV3ExpressionEnum;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.constants.TargetingTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.CommonAmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.qo.TargetSuggestBidBatchQo;
import com.meiyunji.sponsored.service.cpc.service.ICommonAmazonAdTargetingService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbGroupService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbTargetService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdTargetingSb;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.SBCreateErrorEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by lm on 2021/8/3.
 */
@Service
@Slf4j
public class CpcSbTargetServiceImpl implements ICpcSbTargetService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private CpcSbTargetCategoryAipService cpcSbTargetCategoryAipService;
    @Autowired
    private CpcSbPageAsinsApiService pageAsinsApiService;
    @Autowired
    private CpcSbBidApiService cpcSbBidApiService;
    @Autowired
    private IAmazonSbAdGroupDao groupDao;
    @Autowired
    private CpcSbTargetApiService cpcSbTargetApiService;
    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;

    @Autowired
    private IAmazonSbAdsDao amazonSbAdsDao;
    @Autowired
    private IDorisService dorisService;
    @Autowired
    private ICpcSbGroupService cpcSbGroupService;
    @Autowired
    private IAdManageOperationLogService adOperationLogService;
    @Autowired
    private ICommonAmazonAdTargetingService commonAmazonAdTargetingService;

    @Autowired
    private IAmazonAdCampaignAllDao campaignAllDao;

    private static final String MIN_PRICE = "0";
    private static final String MAX_PRICE = String.valueOf(Integer.MAX_VALUE);

    @Override
    public Result createTargeting(AddSbTargetingVo addTargetingVo) {
        Integer puid = addTargetingVo.getPuid();
        Integer shopId = addTargetingVo.getShopId();
        String campaignId = addTargetingVo.getCampaignId();
        String groupId = addTargetingVo.getGroupId();

        List<TargetingVo> targetings = addTargetingVo.getTargetings();
        // 校验参数
        String msg = checkAddTargetingVo(targetings);
        if (StringUtils.isNotBlank(msg)) {
            return ResultUtil.error(msg);
        }

        AmazonSbAdGroup adGroup = groupDao.getByGroupId(puid, shopId, groupId);
        if (adGroup == null) {
            return ResultUtil.returnErr("没有广告组信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        List<AmazonSbAdTargeting> amazonAdTargetList = convertAddTargetingVoToPO(addTargetingVo.getUid(), adGroup, targetings);

        Result result = cpcSbTargetApiService.create(shop, profile, amazonAdTargetList);
        logSbTargetCreate(amazonAdTargetList, addTargetingVo.getIp(), result);
        if (!result.success()) {
            return result;
        }

        List<AmazonSbAdTargeting> succList = amazonAdTargetList.stream().filter(e -> StringUtils.isNotBlank(e.getTargetId()))
                .collect(Collectors.toList());
        if (succList.size() > 0) {
            // 有可能已经添加过了
            List<String> existInDB = amazonSbAdTargetingDao.listByTargetId(addTargetingVo.getPuid(), addTargetingVo.getShopId(), succList.stream()
                    .map(AmazonSbAdTargeting::getTargetId).collect(Collectors.toList())).stream()
                    .map(AmazonSbAdTargeting::getTargetId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getTargetId())).collect(Collectors.toList());
            }

            // 入库
            try {
                amazonSbAdTargetingDao.batchAdd(addTargetingVo.getPuid(), succList);
            } catch (Exception e) {
                log.error("createSbTargeting:", e);
            }

            //创建成功, 需要在同步 获取投放状态
            cpcSbTargetApiService.syncTargets(shop, campaignId);
            //写入doris
            saveDoris(puid, shopId, succList.stream().map(AmazonSbAdTargeting::getTargetId).collect(Collectors.toList()));
            //同步广告组投放类型字段
            try {
                AmazonSbAdGroup sbAdGroup = groupDao.getByGroupId(puid, shopId, groupId);
                if (StringUtils.isBlank(sbAdGroup.getAdGroupType())) {
                    sbAdGroup.setAdGroupType("product");
                    groupDao.updateByIdAndPuid(puid, sbAdGroup);
                    cpcSbGroupService.saveDoris(null, Collections.singletonList(sbAdGroup));
                }
            } catch (Exception e) {
                log.error("updateGroupTargetType:", e);
            }
        }

        // 没有失败的就代表全部成功了
        List<AmazonSbAdTargeting> failList = amazonAdTargetList.stream().filter(e -> StringUtils.isBlank(e.getTargetId()))
                .collect(Collectors.toList());
        if (failList.size() == 0) {
            return ResultUtil.success();
        }

        StringBuilder errMsg = new StringBuilder("创建失败的投放原因：");
        failList.forEach((e) -> {
            if (StringUtils.isNotBlank(e.getErrMsg())) {
                errMsg.append(e.getErrMsg());
            }
        });

        return ResultUtil.returnErr(errMsg.toString());
    }

    private void logSbTargetCreate(List<AmazonSbAdTargeting> targetList, String ip, Result result) {
        try {
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(targetList)) {
                return;
            }
            List<AdManageOperationLog> operationLogs = Lists.newArrayList();
            for (AmazonSbAdTargeting targeting : targetList) {
                AdManageOperationLog targetLog = adOperationLogService.getSbTargetLog(null, targeting);
                targetLog.setIp(ip);
                if (StringUtils.isNotBlank(targeting.getTargetId())) {
                    targetLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    targetLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    if (StringUtils.isNotBlank(targeting.getErrMsg())) {
                        targetLog.setResultInfo(targeting.getErrMsg());
                    } else {
                        targetLog.setResultInfo(result.getMsg());
                    }
                }
                operationLogs.add(targetLog);
            }
            adOperationLogService.batchLogsMergeByAdGroup(operationLogs);
        } catch (Exception e) {
            log.error("SB商品投放创建日志异常", e);
        }
    }

    @Override
    public NewCreateResultResultVo<SBCommonErrorVo> createTargeting(AddSbTargetingVo addTargetingVo, ShopAuth shop, AmazonAdProfile profile) {
        Integer puid = addTargetingVo.getPuid();
        Integer shopId = addTargetingVo.getShopId();
        String campaignId = addTargetingVo.getCampaignId();
        String groupId = addTargetingVo.getGroupId();

        List<TargetingVo> targetings = addTargetingVo.getTargetings();
        // 校验参数
        String msg = checkAddTargetingVo(targetings);
        if (StringUtils.isNotBlank(msg)) {
            throw new ServiceException(msg);
        }

        AmazonSbAdGroup adGroup = groupDao.getByGroupId(puid, shopId, groupId);
        if (adGroup == null) {
            throw new ServiceException(SBCreateErrorEnum.GROUP_NOT_EXIST.getMsg());
        }

        List<AmazonSbAdTargeting> amazonAdTargetList = convertAddTargetingVoToPO(addTargetingVo.getUid(), adGroup, targetings);

        Result result = cpcSbTargetApiService.create(shop, profile, amazonAdTargetList);
        if (!result.success()) {
            NewCreateResultResultVo<SBCommonErrorVo> targetingResult = new NewCreateResultResultVo();
            targetingResult.setCampaignId(campaignId);
            targetingResult.setAdGroupId(groupId);
            return targetingResult;
        }

        List<AmazonSbAdTargeting> succList = amazonAdTargetList.stream().filter(e -> StringUtils.isNotBlank(e.getTargetId()))
                .collect(Collectors.toList());
        if (succList.size() > 0) {
            // 有可能已经添加过了
            List<String> existInDB = amazonSbAdTargetingDao.listByTargetId(addTargetingVo.getPuid(), addTargetingVo.getShopId(), succList.stream()
                            .map(AmazonSbAdTargeting::getTargetId).collect(Collectors.toList())).stream()
                    .map(AmazonSbAdTargeting::getTargetId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getTargetId())).collect(Collectors.toList());
            }

            // 入库
            try {
                amazonSbAdTargetingDao.batchAdd(addTargetingVo.getPuid(), succList);
            } catch (Exception e) {
                log.error("createSbTargeting:", e);
                throw new ServiceException(e.getMessage());
            }

          /*  ThreadPoolUtil.getTargetCreateSyncPool().execute(() -> {
                try {*/
                    //创建成功, 需要在同步 获取投放状态
                    cpcSbTargetApiService.syncTargets(shop, campaignId);
               /* } catch (Exception e) {
                    log.info("添加成功后同步商品投放异常", e);
                    throw new ServiceException(e.getMessage());
                }
            });*/

            //写入doris
            saveDoris(puid, shopId, succList.stream().map(AmazonSbAdTargeting::getTargetId).collect(Collectors.toList()));
            //同步广告组投放类型字段
            try {
                AmazonSbAdGroup sbAdGroup = groupDao.getByGroupId(puid, shopId, groupId);
                if (StringUtils.isBlank(sbAdGroup.getAdGroupType())) {
                    sbAdGroup.setAdGroupType("product");
                    groupDao.updateByIdAndPuid(puid, sbAdGroup);
                    cpcSbGroupService.saveDoris(null, Collections.singletonList(sbAdGroup));
                }
            } catch (Exception e) {
                log.error("updateGroupTargetType:", e);
                throw new ServiceException(e.getMessage());
            }
        }

        NewCreateResultResultVo<SBCommonErrorVo> targetingResult = new NewCreateResultResultVo();
        targetingResult.setCampaignId(campaignId);
        targetingResult.setAdGroupId(groupId);
        targetingResult.setTargetingList(amazonAdTargetList.stream().map(s -> {
                    NewCreateTargetResultVo vo = new NewCreateTargetResultVo();
                    Optional.ofNullable(s.getTargetId()).ifPresent(vo::setTargetId);
                    Optional.ofNullable(s.getTargetText()).ifPresent(vo::setTargetText);
                    Optional.ofNullable(s.getType()).ifPresent(vo::setMatchType);
                    return vo;
                })
                .collect(Collectors.toList()));
        // 没有失败的就代表全部成功了
        List<AmazonSbAdTargeting> failList = amazonAdTargetList.stream().filter(e -> StringUtils.isBlank(e.getTargetId()))
                .collect(Collectors.toList());
        if (failList.isEmpty()) {
            return targetingResult;
        }

        List<SBCommonErrorVo> errorList = new ArrayList<>();
        failList.forEach((e) -> {
            if (org.apache.commons.lang.StringUtils.isNotBlank(e.getErrMsg())) {
                errorList.add(SBCommonErrorVo.getErrorVo(e.getTargetText(), e.getErrMsg()));
            }
        });
        targetingResult.setErrInfoList(errorList);
        return targetingResult;
    }

    @Override
    public Result update(UpdateSdTargetingVo vo) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();

        AmazonSbAdTargeting target = amazonSbAdTargetingDao.getByTargetId(puid, shopId, vo.getTargetId());
        if (target == null) {
            return ResultUtil.returnErr("没有投放定位信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        AmazonSbAdTargeting oldTarget = new AmazonSbAdTargeting();
        BeanUtils.copyProperties(target, oldTarget);

        List<Targeting> targetingList = new ArrayList<>(1);
        Targeting t = new Targeting();
        if (StringUtils.isNotBlank(target.getCampaignId())) {
            t.setCampaignId(Long.valueOf(target.getCampaignId()));
        }
        if (StringUtils.isNotBlank(target.getAdGroupId())) {
            t.setAdGroupId(Long.valueOf(target.getAdGroupId()));
        }
        if (StringUtils.isNotBlank(target.getTargetId())) {
            t.setTargetId(Long.valueOf(target.getTargetId()));
        }

        if (StringUtils.isNotBlank(vo.getBid())) {
            t.setBid(Double.valueOf(vo.getBid()));
            target.setBid(new BigDecimal(vo.getBid()));
        }
        if (StringUtils.isNotBlank(vo.getState())) {  //修改状态
            t.setState(vo.getState());
            target.setState(vo.getState());
        }
        targetingList.add(t);

        Result result = cpcSbTargetApiService.update(shop, profile, targetingList);
        target.setUpdateId(vo.getUid());
        logSbTargetUpdate(oldTarget, target, vo.getIp(), result);

        if (result.success()) {
            amazonSbAdTargetingDao.updateByIdAndPuid(puid, target);
            saveDoris(Collections.singletonList(target), false, true);
            return ResultUtil.success();
        }

        return ResultUtil.error(result.getMsg());
    }

    @Override
    public Result archive(Integer puid, Integer shopId, Integer uid, String targetId, String ip) {

        AmazonSbAdTargeting target = amazonSbAdTargetingDao.getByTargetId(puid, shopId, targetId);
        if (target == null) {
            return ResultUtil.returnErr("没有投放定位信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Result result = cpcSbTargetApiService.archive(shop, profile, targetId);

        AmazonSbAdTargeting oldTarget = new AmazonSbAdTargeting();
        BeanUtils.copyProperties(target, oldTarget);
        target.setUpdateId(uid);
        target.setState(CpcStatusEnum.archived.name());
        logSbTargetUpdate(oldTarget, target, ip, result);

        if (result.success()) {
            amazonSbAdTargetingDao.updateByIdAndPuid(puid, target);
            saveDoris(Collections.singletonList(target), false, true);
            return ResultUtil.success();
        }

        return ResultUtil.error(result.getMsg());
    }

    private void logSbTargetUpdate(AmazonSbAdTargeting oldTargeting, AmazonSbAdTargeting targeting, String ip, Result result) {
        try {
            AdManageOperationLog operationLog = adOperationLogService.getSbTargetLog(oldTargeting, targeting);
            operationLog.setIp(ip);
            if (result.success()) {
                operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                operationLog.setResultInfo(result.getMsg());
            }
            adOperationLogService.printAdOperationLog(Lists.newArrayList(operationLog));
        } catch (Exception e) {
            log.error("SB商品投放更新日志异常", e);
        }
    }

    @Override
    public Result<List<CategoryRecommendationResults>> addTargets(Integer puid, Integer shopId, String campaignId, String groupId) {
        List<AmazonSbAds> adsList = amazonSbAdsDao.getAdGroupByIds(puid, shopId, Lists.newArrayList(groupId));
        if (adsList == null) {
            return ResultUtil.returnErr("没有ads信息");
        }
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Set<String> asinList = new HashSet<>();

        for (AmazonSbAds ads: adsList) {
            if ("productCollection".equalsIgnoreCase(ads.getAdFormat()) && StringUtils.isNotBlank(ads.getLandingPage())) {  //商品集的需要接口取最新asinlist
                AdLandingPage landingPage = JSONUtil.jsonToObject(ads.getLandingPage(), AdLandingPage.class);
                if (landingPage != null && StringUtils.isNotBlank(landingPage.getUrl())) {
                    Result<PageAsinsResult> result = pageAsinsApiService.getAsinList(shop, profile, landingPage.getUrl());
                    if (result.success()) {
                        PageAsinsResult resultData = result.getData();
                        if (resultData != null && CollectionUtils.isNotEmpty(resultData.getAsinList())) {
                            asinList.addAll(resultData.getAsinList());
                        }
                    }
                }
            } else {
                if (StringUtils.isNotBlank(ads.getAsins())) {
                    List<String> creativeAsin = StringUtil.splitStr(ads.getAsins());
                    if (CollectionUtils.isNotEmpty(creativeAsin)) {
                        asinList.addAll(creativeAsin);
                    }
                }
            }
        }



        if (CollectionUtils.isEmpty(asinList)) {
            return ResultUtil.returnErr("请手动同步该活动后,在操作");
        }

        List<CategoryRecommendationResults> categoryList = null;
        Result<TargetCategoryResult> resultResult = cpcSbTargetCategoryAipService.getTargetsCategoryList(shop, profile, Lists.newArrayList(asinList));
        if (resultResult.success()) {
            TargetCategoryResult data = resultResult.getData();
            categoryList = data.getCategoryRecommendationResults();
        }

        return ResultUtil.returnSucc(categoryList);
    }

    @Override
    public Result<List<CategoryRecommendationResults>> getTargetsCategoryList(Integer puid, Integer shopId, List<String> asinList) {

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        List<CategoryRecommendationResults> categoryList = null;
        Result<TargetCategoryResult> resultResult = cpcSbTargetCategoryAipService.getTargetsCategoryList(shop, profile, asinList);
        if (resultResult.success()) {
            TargetCategoryResult data = resultResult.getData();
           categoryList = data.getCategoryRecommendationResults();
        }
        return ResultUtil.returnSucc(categoryList);
    }

    @Override
    public Result<TargetRecommendsPageVo> getTargetsCategoryListNew(Integer puid,
                                                                    Integer shopId,
                                                                    List<String> asinList,
                                                                    Integer pageNo,
                                                                    Integer pageSize,
                                                                    String campaignId,
                                                                    String groupId) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Result<TargetCategoryResult> resultResult = null;
        if (StringUtils.isNotBlank(campaignId) && StringUtils.isNotBlank(groupId)) {
            resultResult = getSBTargetsCategorySuggestionResultByCampaignIdAndGroupId(shop, profile, campaignId, groupId);
        } else {
            if (CollectionUtils.isEmpty(asinList)) {
                return ResultUtil.returnErr("需要获取分类的Asin不能为空");
            }
            resultResult = cpcSbTargetCategoryAipService.getTargetsCategoryList(shop, profile, asinList);
        }

        List<CategoryRecommendationResults> categoryList = null;
        if (Objects.nonNull(resultResult) && resultResult.success() && Objects.nonNull(resultResult.getData())) {
            TargetCategoryResult data = resultResult.getData();
            categoryList = data.getCategoryRecommendationResults();
        }
        return ResultUtil.returnSucc(getRecommendTargetListPage(categoryList, pageSize, pageNo));
    }

    private Result<TargetCategoryResult> getSBTargetsCategorySuggestionResultByCampaignIdAndGroupId(ShopAuth shop, AmazonAdProfile profile, String campaignId, String groupId) {
        List<String> asinByGroup = amazonSbAdsDao.getAsinByGroup(shop.getPuid(), shop.getId(), campaignId, groupId);
        if (CollectionUtils.isEmpty(asinByGroup)) {
            return ResultUtil.success();
        }

        Set<String> asinList = new HashSet<>();
        for (String asin : asinByGroup){
            if(org.apache.commons.lang.StringUtils.isNotBlank(asin)){
                List<String> list = StringUtil.splitStr(asin);
                if(CollectionUtils.isNotEmpty(list)){
                    asinList.addAll(list);
                }
            }
        }

        if (CollectionUtils.isEmpty(asinList)) {
            return ResultUtil.returnSucc(null);
        }

        Result<TargetCategoryResult> suggestionResult = cpcSbTargetCategoryAipService.getTargetsCategoryList(shop, profile, Lists.newArrayList(asinList));
        if (suggestionResult.success()) {
            return ResultUtil.returnSucc(suggestionResult.getData());
        }
        return ResultUtil.returnSucc(null);
    }

    @Override
    public Result<List<SuggestCategoryBrandVo>> suggestBrand(int puid, Integer shopId, String categoryId, String keyword) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        List<SuggestCategoryBrandVo> voList = null;
        Result<TargetBrandResult> resultResult = cpcSbTargetCategoryAipService.getTargetsBrandList(shop, profile, categoryId, keyword);
        if (resultResult.success()) {
            TargetBrandResult data = resultResult.getData();
            List<BrandRecommendationResults> brandList = data.getBrandRecommendationResults();
            if (CollectionUtils.isNotEmpty(brandList)) {
                voList = new ArrayList<>(brandList.size());
                SuggestCategoryBrandVo vo;
                for (BrandRecommendationResults brand : brandList) {
                    vo = new SuggestCategoryBrandVo();
                    vo.setId(brand.getId().toString());
                    vo.setName(brand.getName());
                    voList.add(vo);
                }
            }
        }

        return ResultUtil.returnSucc(voList);
    }

    @Override
    public Result<List<RecommendedProducts>> getSuggestAsin(Integer puid, Integer shopId, List<String> asinList) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Result<List<RecommendedProducts>> result = cpcSbTargetCategoryAipService.getSuggestAsin(shop, profile, asinList);
        List<RecommendedProducts> productsList = null;
        if (result.success()) {
            productsList = result.getData();
        }
        return ResultUtil.returnSucc(productsList);
    }

    @Override
    public Result<List<SuggestedTargetVo>> getTargetBids(Integer puid, Integer shopId, String campaignId, String adFormat, List<TargetingVo> targetingVoList) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        List<SuggestedTargetVo> suggestedTargetVos = targetingVoList.stream().map(vo -> {
            SuggestedTargetVo suggestedBidVo = new SuggestedTargetVo();
            List<Expression> expressions = new ArrayList<>();
            suggestedBidVo.setExpression(expressions);
            Optional.ofNullable(vo.getCategoryId()).ifPresent(suggestedBidVo::setTargetId);
            Expression expression;
            if (TargetTypeEnum.asin.name().equals(vo.getType())) {
                expression = new Expression();
                expression.setType(ExpressionEnum.asinSameAs.value());
                expression.setValue(vo.getAsin());
                expressions.add(expression);
            } else {
                expression = new Expression();
                expression.setType(ExpressionEnum.asinCategorySameAs.value());
                expression.setValue(vo.getCategoryId());
                expressions.add(expression);

                if (StringUtils.isNotBlank(vo.getBrand())) {
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinBrandSameAs.value());
                    expression.setValue(vo.getBrand());
                    expressions.add(expression);
                }
                if(StringUtils.isBlank(vo.getMinPrice())) {
                    vo.setMinPrice(MIN_PRICE);
                }
                if(StringUtils.isBlank(vo.getMaxPrice())) {
                    vo.setMaxPrice(MAX_PRICE);
                }
                if (StringUtils.isNotBlank(vo.getMinPrice()) && StringUtils.isNotBlank(vo.getMaxPrice())) {
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinPriceBetween.value());
                    expression.setValue(vo.getMinPrice() + "-" + vo.getMaxPrice());
                    expressions.add(expression);
                }
                if (vo.getMinReviewRating() != null && vo.getMaxReviewRating() != null) {
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinReviewRatingBetween.value());
                    expression.setValue(vo.getMinReviewRating() + "-" + vo.getMaxReviewRating());
                    expressions.add(expression);
                }
//                if (vo.getPrimeShippingEligible() != null) {
//                    expression = new Expression();
//                    expression.setType(ExpressionEnum.asinIsPrimeShippingEligible.value());
//                    expression.setValue(vo.getPrimeShippingEligible().toString());
//                    expressions.add(expression);
//                }
            }
            return suggestedBidVo;
        }).collect(Collectors.toList());

        Long camId = null;
        if (StringUtils.isNotBlank(campaignId)) {
            camId = Long.valueOf(campaignId);
        }

        // 接口要求：每次指定的关键词数量不能超过100个
        List<List<SuggestedTargetVo>> lists = Lists.partition(suggestedTargetVos, 100);
        for (List<SuggestedTargetVo> subList : lists) {
            List<List<SBTargetingExpressions>> targetList = subList.stream().map(e -> {
                List<Expression> expression = e.getExpression();
                List<SBTargetingExpressions> list = new ArrayList<>(expression.size());
                SBTargetingExpressions sbTx;
                for (Expression ex : expression) {
                    sbTx = new SBTargetingExpressions();
                    sbTx.setType(ex.getType());
                    sbTx.setValue(ex.getValue());
                    list.add(sbTx);
                }
                return list;
            }).collect(Collectors.toList());

            Result<BidRecommendationResult> resultResult = cpcSbBidApiService.getKeywordAndTargetBids(shop, profile, camId,
                    adFormat, null, targetList, null);

            if (resultResult.success()) {
                BidRecommendationResult result = resultResult.getData();
                if (result == null) {
                    break;
                }
                List<TargetsBidsSuccessResults> successList = result.getTargetsBidsRecommendationSuccessResults();
                if (CollectionUtils.isEmpty(successList)) {
                    break;
                }
                for (TargetsBidsSuccessResults successResult : successList) {
                    Integer index = successResult.getTargetsIndex();
                    RecommendedBid recommendedBid = successResult.getRecommendedBid();
                    if (recommendedBid != null && index != null) {
                        SuggestedTargetVo suggestedTargetVo = subList.get(index);
                        if (recommendedBid.getRecommended() != null) {
                            suggestedTargetVo.setSuggested(recommendedBid.getRecommended().toString());
                        }
                        if (recommendedBid.getRangeStart() != null) {
                            suggestedTargetVo.setRangeStart(recommendedBid.getRangeStart().toString());
                        }
                        if (recommendedBid.getRangeEnd() != null) {
                            suggestedTargetVo.setRangeEnd(recommendedBid.getRangeEnd().toString());
                        }
                    }
                }

            }
        }
        return ResultUtil.returnSucc(suggestedTargetVos);
    }

    @Override
    public Result<List<SuggestedTargetVo>> getTargetBidsNew(Integer puid, Integer shopId,
                                                            String adFormat, List<TargetingVo> targetingVoList,
                                                            String goal, String costType) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        List<SuggestedTargetVo> suggestedTargetVos = targetingVoList.stream().map(vo -> {
            SuggestedTargetVo suggestedBidVo = new SuggestedTargetVo();
            List<Expression> expressions = new ArrayList<>();
            suggestedBidVo.setExpression(expressions);
            Optional.ofNullable(vo.getCategoryId()).ifPresent(suggestedBidVo::setTargetId);
            Expression expression;
            if (TargetTypeEnum.asin.name().equals(vo.getType())) {
                expression = new Expression();
                expression.setType(ExpressionEnum.asinSameAs.value());
                expression.setValue(vo.getAsin());
                expressions.add(expression);
            } else {
                expression = new Expression();
                expression.setType(ExpressionEnum.asinCategorySameAs.value());
                expression.setValue(vo.getCategoryId());
                expressions.add(expression);

                if (StringUtils.isNotBlank(vo.getBrand())) {
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinBrandSameAs.value());
                    expression.setValue(vo.getBrand());
                    expressions.add(expression);
                }
//                if(StringUtils.isBlank(vo.getMinPrice())) {
//                    vo.setMinPrice(MIN_PRICE);
//                }
//                if(StringUtils.isBlank(vo.getMaxPrice())) {
//                    vo.setMaxPrice(MAX_PRICE);
//                }
                if (StringUtils.isNotBlank(vo.getMinPrice()) && StringUtils.isNotBlank(vo.getMaxPrice())) {
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinPriceBetween.value());
                    expression.setValue(vo.getMinPrice() + "-" + vo.getMaxPrice());
                    expressions.add(expression);
                }
                if (vo.getMinReviewRating() != null && vo.getMaxReviewRating() != null) {
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinReviewRatingBetween.value());
                    expression.setValue(vo.getMinReviewRating() + "-" + vo.getMaxReviewRating());
                    expressions.add(expression);
                }
//                if (vo.getPrimeShippingEligible() != null) {
//                    expression = new Expression();
//                    expression.setType(ExpressionEnum.asinIsPrimeShippingEligible.value());
//                    expression.setValue(vo.getPrimeShippingEligible().toString());
//                    expressions.add(expression);
//                }
            }
            return suggestedBidVo;
        }).collect(Collectors.toList());

        // 接口要求：每次指定的关键词数量不能超过100个
        List<List<SuggestedTargetVo>> lists = Lists.partition(suggestedTargetVos, 100);
        for (List<SuggestedTargetVo> subList : lists) {
            List<List<SBTargetingExpressions>> targetList = subList.stream().map(e -> {
                List<Expression> expression = e.getExpression();
                List<SBTargetingExpressions> list = new ArrayList<>(expression.size());
                SBTargetingExpressions sbTx;
                for (Expression ex : expression) {
                    sbTx = new SBTargetingExpressions();
                    sbTx.setType(ex.getType());
                    sbTx.setValue(ex.getValue());
                    list.add(sbTx);
                }
                return list;
            }).collect(Collectors.toList());

            Result<BidRecommendationResult> resultResult = cpcSbBidApiService.getKeywordAndTargetBidsNew(shop, profile, null,
                    adFormat, costType, null, targetList, null, goal);

            if (resultResult.success()) {
                BidRecommendationResult result = resultResult.getData();
                if (result == null) {
                    break;
                }
                List<TargetsBidsSuccessResults> successList = result.getTargetsBidsRecommendationSuccessResults();
                if (CollectionUtils.isEmpty(successList)) {
                    break;
                }
                for (TargetsBidsSuccessResults successResult : successList) {
                    Integer index = successResult.getTargetsIndex();
                    RecommendedBid recommendedBid = successResult.getRecommendedBid();
                    if (recommendedBid != null && index != null) {
                        SuggestedTargetVo suggestedTargetVo = subList.get(index);
                        suggestedTargetVo.setIndex(index);
                        if (recommendedBid.getRecommended() != null) {
                            suggestedTargetVo.setSuggested(recommendedBid.getRecommended().toString());
                        }
                        if (recommendedBid.getRangeStart() != null) {
                            suggestedTargetVo.setRangeStart(recommendedBid.getRangeStart().toString());
                        }
                        if (recommendedBid.getRangeEnd() != null) {
                            suggestedTargetVo.setRangeEnd(recommendedBid.getRangeEnd().toString());
                        }
                    }
                }

            }
        }
        return ResultUtil.returnSucc(suggestedTargetVos);
    }

    @Override
    public Result<List<SuggestedTargetVo>> getSuggestedBid(int puid, Integer shopId, List<String> targetIds) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        List<AmazonSbAdTargeting> targetingList = amazonSbAdTargetingDao.listByTargetId(puid, shopId, targetIds);
        if (CollectionUtils.isEmpty(targetingList)) {
            return ResultUtil.returnErr("没有投放定位信息");
        }

        // 过滤出有效的, 没有有效的也不返回报错
        targetingList = targetingList.stream().filter(e -> CpcStatusEnum.validList().contains(e.getState())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(targetingList)) {
            return ResultUtil.success(new ArrayList<>());
        }

        Map<String, AmazonSbAdTargeting> targetingMap = targetingList.stream().collect(Collectors.toMap(AmazonSbAdTargeting::getTargetId, Function.identity()));

        // 根据campaign分组查询建议竞价
        Map<String, List<AmazonSbAdTargeting>> adTargetingMap = targetingList.stream().collect(Collectors.groupingBy(AmazonSbAdTargeting::getAdGroupId));
        List<String> adGroupIds = adTargetingMap.keySet().stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<AmazonSbAdGroup> adGroups = groupDao.getAdGroupByIds(puid, shopId, adGroupIds);
        if (CollectionUtils.isEmpty(adGroups)) {
            return ResultUtil.returnErr("没有活动信息");
        }

        List<String> campaignIds = adGroups.stream().map(AmazonSbAdGroup::getCampaignId).collect(Collectors.toList());
        Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = campaignAllDao.listByCampaignIds(puid, shopId, campaignIds).stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));

        ThreadPoolExecutor executor = ThreadPoolUtil.getBatchGetSuggestedBitPool();
        List<CompletableFuture<List<SuggestedTargetVo>>> completableFutures = new ArrayList<>();
        List<SuggestedTargetVo> suggestedTargetVoList = new ArrayList<>();
        adGroups.forEach(adGroup -> completableFutures.add(CompletableFuture.supplyAsync(() -> {
            Long camId = Optional.ofNullable(adGroup.getCampaignId()).filter(org.apache.commons.lang.StringUtils::isNotBlank).map(Long::valueOf).orElse(null);
            String adFormat = adGroup.getAdFormat();
            String goal = null;
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(adGroup.getCampaignId());
            if (amazonAdCampaignAll != null) {
                goal = amazonAdCampaignAll.getAdGoal();
                if ("VIDEO".equalsIgnoreCase(adFormat) && "BRAND_IMPRESSION_SHARE".equalsIgnoreCase(goal)) {
                    adFormat = null;
                }
            }
            List<SuggestedTargetVo> suggestedTargetVos = adTargetingMap.get(adGroup.getAdGroupId()).stream().map(vo -> {
                SuggestedTargetVo suggestedBidVo = new SuggestedTargetVo();
                suggestedBidVo.setTargetId(vo.getTargetId());
                suggestedBidVo.setExpression(JSONUtil.jsonToArray(vo.getExpression(), Expression.class));
                suggestedBidVo.setSuggested(Optional.ofNullable(vo.getSuggested()).map(o -> o.setScale(2, RoundingMode.HALF_UP).toString()).orElse(null));
                suggestedBidVo.setRangeStart(Optional.ofNullable(vo.getRangeStart()).map(o -> o.setScale(2, RoundingMode.HALF_UP).toString()).orElse(null));
                suggestedBidVo.setRangeEnd(Optional.ofNullable(vo.getRangeEnd()).map(o -> o.setScale(2, RoundingMode.HALF_UP).toString()).orElse(null));
                return suggestedBidVo;
            }).collect(Collectors.toList());

            // 接口要求：每次指定的关键词数量不能超过100个
            String finalGoal = goal;
            String finalAdFormat = adFormat;
            Lists.partition(suggestedTargetVos, 100).forEach(subList -> {
                List<List<SBTargetingExpressions>> targetList = subList.stream().map(e -> {
                    List<Expression> expression = e.getExpression();
                    List<SBTargetingExpressions> list = new ArrayList<>(expression.size());
                    SBTargetingExpressions sbTx;
                    for (Expression ex : expression) {
                        sbTx = new SBTargetingExpressions();
                        sbTx.setType(ex.getType());
                        sbTx.setValue(ex.getValue());
                        list.add(sbTx);
                    }
                    return list;
                }).collect(Collectors.toList());

                Result<BidRecommendationResult> resultResult = cpcSbBidApiService.getKeywordAndTargetBids(shop, profile, camId, finalAdFormat, null, targetList, finalGoal);
                BidRecommendationResult result;
                if (!resultResult.success() || (result = resultResult.getData()) == null) return;
                List<TargetsBidsSuccessResults> successList = result.getTargetsBidsRecommendationSuccessResults();
                if (CollectionUtils.isEmpty(successList)) return;

                successList.forEach(successResult -> {
                    Integer index = successResult.getTargetsIndex();
                    RecommendedBid recommendedBid = successResult.getRecommendedBid();
                    if (recommendedBid != null && index != null) {
                        SuggestedTargetVo suggestedTargetVo = subList.get(index);
                        suggestedTargetVo.setSuggested(Optional.ofNullable(recommendedBid.getRecommended()).map(String::valueOf).orElse(suggestedTargetVo.getSuggested()));
                        suggestedTargetVo.setRangeStart(Optional.ofNullable(recommendedBid.getRangeStart()).map(String::valueOf).orElse(suggestedTargetVo.getRangeStart()));
                        suggestedTargetVo.setRangeEnd(Optional.ofNullable(recommendedBid.getRangeEnd()).map(String::valueOf).orElse(suggestedTargetVo.getRangeEnd()));

                        AmazonSbAdTargeting amazonSbAdTargeting = targetingMap.get(suggestedTargetVo.getTargetId());
                        if (amazonSbAdTargeting == null) return;
                        amazonSbAdTargeting.setSuggested(Optional.ofNullable(recommendedBid.getRecommended()).map(String::valueOf).map(BigDecimal::new).orElse(amazonSbAdTargeting.getSuggested()));
                        amazonSbAdTargeting.setRangeStart(Optional.ofNullable(recommendedBid.getRangeStart()).map(String::valueOf).map(BigDecimal::new).orElse(amazonSbAdTargeting.getRangeStart()));
                        amazonSbAdTargeting.setRangeEnd(Optional.ofNullable(recommendedBid.getRangeEnd()).map(String::valueOf).map(BigDecimal::new).orElse(amazonSbAdTargeting.getRangeEnd()));
                    }
                });
            });
            return suggestedTargetVos;
        }, executor)));

        try {
            CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).get();
        } catch (InterruptedException | ExecutionException ignored) {
        }
        completableFutures.forEach(future -> {
            try {
                suggestedTargetVoList.addAll(future.get());
            } catch (InterruptedException | ExecutionException e) {
                log.info("sb targeting getSuggestedBid error, puid:{}, shopId:{}", puid, shop.getId(), e);
            }
        });

        // 批量更新建议竞价
        amazonSbAdTargetingDao.batchUpdateSuggestValue(puid, Lists.newArrayList(targetingMap.values()));
        return ResultUtil.returnSucc(suggestedTargetVoList);
    }

    @Override
    public Result<List<SuggestedTargetVo>> batchGetSuggestedBid(int puid, Integer shopId, List<Integer> sourceShopIds, List<TargetSuggestedBidDetail> details, String targetingType) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        List<String> targetIds = details.stream().map(TargetSuggestedBidDetail::getTargetId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, CommonAmazonAdTargeting> targetingMap = new HashMap<>();
        // 搜索词与词库不传targetId
        if (CollectionUtils.isNotEmpty(targetIds)) {
            List<CommonAmazonAdTargeting> targetingList = commonAmazonAdTargetingService.listByTargetIds(targetingType, puid, sourceShopIds, targetIds);
            if (CollectionUtils.isEmpty(targetingList)) {
                return ResultUtil.returnErr("对象不存在");
            }
            targetingMap.putAll(targetingList.stream().collect(
                    Collectors.toMap(CommonAmazonAdTargeting::getTargetId, Function.identity(), (newVal, oldVal) -> newVal)));
        }

        // 根据campaign分组查询建议竞价
        Map<String, List<TargetSuggestedBidDetail>> suggestedBidDetailMap = details.stream().collect(Collectors.groupingBy(TargetSuggestedBidDetail::getAdGroupId));
        List<String> adGroupIds = suggestedBidDetailMap.keySet().stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<AmazonSbAdGroup> adGroups = groupDao.getAdGroupByIds(puid, shopId, adGroupIds);
        if (CollectionUtils.isEmpty(adGroups)) {
            return ResultUtil.returnErr("没有活动信息");
        }

        List<String> campaignIds = adGroups.stream().map(AmazonSbAdGroup::getCampaignId).collect(Collectors.toList());
        Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = campaignAllDao.listByCampaignIds(puid, shopId, campaignIds).stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));


        ThreadPoolExecutor executor = ThreadPoolUtil.getBatchGetSuggestedBitPool();
        List<CompletableFuture<List<SuggestedTargetVo>>> completableFutures = new ArrayList<>();
        List<SuggestedTargetVo> suggestedTargetVoList = new ArrayList<>();
        adGroups.forEach(adGroup -> completableFutures.add(CompletableFuture.supplyAsync(() -> {
            Long camId = Optional.ofNullable(adGroup.getCampaignId()).filter(StringUtils::isNotBlank).map(Long::valueOf).orElse(null);
            String adFormat = adGroup.getAdFormat();
            String goal = null;
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(adGroup.getCampaignId());
            if (amazonAdCampaignAll != null) {
                goal = amazonAdCampaignAll.getAdGoal();
                if ("VIDEO".equalsIgnoreCase(adFormat) && "BRAND_IMPRESSION_SHARE".equalsIgnoreCase(goal)) {
                    adFormat = null;
                }
            }
            List<SuggestedTargetVo> suggestedTargetVos = suggestedBidDetailMap.get(adGroup.getAdGroupId()).stream().map(vo -> {
                SuggestedTargetVo suggestedBidVo = new SuggestedTargetVo();
                suggestedBidVo.setIndex(vo.getIndex());
                if (StringUtils.isNotBlank(vo.getTargetId())) {
                    String targetExpression = targetingMap.get(vo.getTargetId()).getExpression();
                    List<Expression> expressions = JSONUtil.jsonToArray(targetExpression, Expression.class);
                    if (CollectionUtils.isNotEmpty(expressions)) {
                        for (Expression expression : expressions) {
                            String expressionType = expression.getType();
                            if (TargetingTypeEnum.SP_TARGETING_CODE_LIST.contains(targetingType)) {
                                SpV3ExpressionEnum expressionByValueV3 = SpV3ExpressionEnum.getExpressionByValueV3(expressionType);
                                if (expressionByValueV3 != null) {
                                    expressionType = expressionByValueV3.getValue();
                                }
                            }
                            // 以用户选择的投放类型为准
                            if (ExpressionEnum.asinSameAs.value().equals(expressionType) || ExpressionEnum.asinExpandedFrom.value().equals(expressionType)) {
                                expressionType = vo.getExpressionType();
                            }
                            expression.setType(expressionType);
                        }
                    }
                    suggestedBidVo.setExpression(expressions);
                } else {
                    Expression expression = new Expression();
                    expression.setType(vo.getExpressionType());
                    expression.setValue(vo.getAsin());
                    suggestedBidVo.setExpression(Collections.singletonList(expression));
                }
                return suggestedBidVo;
            }).collect(Collectors.toList());

            // 接口要求：每次指定的关键词数量不能超过100个
            String finalGoal = goal;
            String finalAdFormat = adFormat;
            Lists.partition(suggestedTargetVos, 100).forEach(subList -> {
                List<List<SBTargetingExpressions>> targetList = subList.stream().map(e -> {
                    List<Expression> expression = e.getExpression();
                    List<SBTargetingExpressions> list = new ArrayList<>(expression.size());
                    SBTargetingExpressions sbTx;
                    for (Expression ex : expression) {
                        sbTx = new SBTargetingExpressions();
                        sbTx.setType(ex.getType());
                        sbTx.setValue(ex.getValue());
                        list.add(sbTx);
                    }
                    return list;
                }).collect(Collectors.toList());

                Result<BidRecommendationResult> resultResult = cpcSbBidApiService.getKeywordAndTargetBids(shop, profile, camId, finalAdFormat, null, targetList, finalGoal);
                BidRecommendationResult result;
                if (!resultResult.success() || (result = resultResult.getData()) == null) return;
                List<TargetsBidsSuccessResults> successList = result.getTargetsBidsRecommendationSuccessResults();
                if (CollectionUtils.isEmpty(successList)) return;

                successList.forEach(successResult -> {
                    Integer index = successResult.getTargetsIndex();
                    RecommendedBid recommendedBid = successResult.getRecommendedBid();
                    if (recommendedBid != null && index != null) {
                        SuggestedTargetVo suggestedTargetVo = subList.get(index);
                        suggestedTargetVo.setSuggested(Optional.ofNullable(recommendedBid.getRecommended()).map(String::valueOf).orElse(suggestedTargetVo.getSuggested()));
                        suggestedTargetVo.setRangeStart(Optional.ofNullable(recommendedBid.getRangeStart()).map(String::valueOf).orElse(suggestedTargetVo.getRangeStart()));
                        suggestedTargetVo.setRangeEnd(Optional.ofNullable(recommendedBid.getRangeEnd()).map(String::valueOf).orElse(suggestedTargetVo.getRangeEnd()));
                    }
                });
            });
            return suggestedTargetVos;
        }, executor)));

        try {
            CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).get();
        } catch (InterruptedException | ExecutionException ignored) {
        }
        completableFutures.forEach(future -> {
            try {
                suggestedTargetVoList.addAll(future.get());
            } catch (InterruptedException | ExecutionException e) {
                log.info("sb targeting getSuggestedBid error, puid:{}, shopId:{}", puid, shop.getId(), e);
            }
        });

        return ResultUtil.returnSucc(suggestedTargetVoList);
    }

    @Override
    public Result<List<SuggestedTargetVo>> getSuggestedBidMultiShop(int puid, List<TargetSuggestBidBatchQo> qoList) {
        List<Integer> shopIdList = qoList.stream().map(TargetSuggestBidBatchQo::getShopId).distinct().collect(Collectors.toList());
        List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, shopIdList);
        if (shopIdList.size() != shopAuthList.size()) {
            return ResultUtil.error("店铺不存在，请刷新页面重试");
        }
        Map<Integer, ShopAuth> shopAuthMap = shopAuthList.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
        List<Integer> shopIds = new ArrayList<>(shopAuthMap.keySet());
        List<AmazonAdProfile> profileList = amazonAdProfileDao.getByPuidAndShopIdList(puid, new ArrayList<>(shopIds));
        if (profileList.size() != shopAuthMap.size()) {
            return ResultUtil.returnErr("没有站点对应的配置信息，请刷新页面重试");
        }
        Map<Integer, AmazonAdProfile> profileMap = profileList.stream().collect(Collectors.toMap(AmazonAdProfile::getShopId, Function.identity()));

        List<AmazonSbAdTargeting> targetingList = amazonSbAdTargetingDao.getByTargetSuggestBidBatchQo(puid, qoList);
        if (CollectionUtils.isEmpty(targetingList)) {
            return ResultUtil.returnErr("没有投放定位信息");
        }

        // 过滤出有效的, 没有有效的也不返回报错
        targetingList = targetingList.stream().filter(e -> CpcStatusEnum.validList().contains(e.getState())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(targetingList)) {
            return ResultUtil.success(new ArrayList<>());
        }

        Map<String, AmazonSbAdTargeting> targetingMap = targetingList.stream().collect(Collectors.toMap(AmazonSbAdTargeting::getTargetId, Function.identity()));

        // 根据campaign分组查询建议竞价
        Map<String, List<AmazonSbAdTargeting>> adTargetingMap = targetingList.stream().collect(Collectors.groupingBy(AmazonSbAdTargeting::getAdGroupId));
        List<String> adGroupIds = adTargetingMap.keySet().stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<AmazonSbAdGroup> adGroups = groupDao.getListByShopIdsAndGroupIds(puid, shopIds, adGroupIds);
        if (CollectionUtils.isEmpty(adGroups)) {
            return ResultUtil.returnErr("没有活动信息");
        }

        List<String> campaignIds = adGroups.stream().map(AmazonSbAdGroup::getCampaignId).collect(Collectors.toList());
        Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = campaignAllDao.listByShopIdAndCampaignIds(puid, shopIds, campaignIds).stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));

        ThreadPoolExecutor executor = ThreadPoolUtil.getBatchGetSuggestedBitPool();
        List<CompletableFuture<List<SuggestedTargetVo>>> completableFutures = new ArrayList<>();
        List<SuggestedTargetVo> suggestedTargetVoList = new ArrayList<>();
        adGroups.forEach(adGroup -> completableFutures.add(CompletableFuture.supplyAsync(() -> {
            Long camId = Optional.ofNullable(adGroup.getCampaignId()).filter(org.apache.commons.lang.StringUtils::isNotBlank).map(Long::valueOf).orElse(null);
            String adFormat = adGroup.getAdFormat();
            String goal = null;
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(adGroup.getCampaignId());
            if (amazonAdCampaignAll != null) {
                goal = amazonAdCampaignAll.getAdGoal();
                if ("VIDEO".equalsIgnoreCase(adFormat) && "BRAND_IMPRESSION_SHARE".equalsIgnoreCase(goal)) {
                    adFormat = null;
                }
            }
            List<SuggestedTargetVo> suggestedTargetVos = adTargetingMap.get(adGroup.getAdGroupId()).stream().map(vo -> {
                SuggestedTargetVo suggestedBidVo = new SuggestedTargetVo();
                suggestedBidVo.setTargetId(vo.getTargetId());
                suggestedBidVo.setExpression(JSONUtil.jsonToArray(vo.getExpression(), Expression.class));
                suggestedBidVo.setSuggested(Optional.ofNullable(vo.getSuggested()).map(o -> o.setScale(2, RoundingMode.HALF_UP).toString()).orElse(null));
                suggestedBidVo.setRangeStart(Optional.ofNullable(vo.getRangeStart()).map(o -> o.setScale(2, RoundingMode.HALF_UP).toString()).orElse(null));
                suggestedBidVo.setRangeEnd(Optional.ofNullable(vo.getRangeEnd()).map(o -> o.setScale(2, RoundingMode.HALF_UP).toString()).orElse(null));
                return suggestedBidVo;
            }).collect(Collectors.toList());

            // 接口要求：每次指定的关键词数量不能超过100个
            String finalGoal = goal;
            String finalAdFormat = adFormat;
            Lists.partition(suggestedTargetVos, 100).forEach(subList -> {
                List<List<SBTargetingExpressions>> targetList = subList.stream().map(e -> {
                    List<Expression> expression = e.getExpression();
                    List<SBTargetingExpressions> list = new ArrayList<>(expression.size());
                    SBTargetingExpressions sbTx;
                    for (Expression ex : expression) {
                        sbTx = new SBTargetingExpressions();
                        sbTx.setType(ex.getType());
                        sbTx.setValue(ex.getValue());
                        list.add(sbTx);
                    }
                    return list;
                }).collect(Collectors.toList());

                Result<BidRecommendationResult> resultResult = cpcSbBidApiService.getKeywordAndTargetBids(shopAuthMap.get(adGroup.getShopId()), profileMap.get(adGroup.getShopId()), camId, finalAdFormat, null, targetList, finalGoal);
                BidRecommendationResult result;
                if (!resultResult.success() || (result = resultResult.getData()) == null) return;
                List<TargetsBidsSuccessResults> successList = result.getTargetsBidsRecommendationSuccessResults();
                if (CollectionUtils.isEmpty(successList)) return;

                successList.forEach(successResult -> {
                    Integer index = successResult.getTargetsIndex();
                    RecommendedBid recommendedBid = successResult.getRecommendedBid();
                    if (recommendedBid != null && index != null) {
                        SuggestedTargetVo suggestedTargetVo = subList.get(index);
                        suggestedTargetVo.setSuggested(Optional.ofNullable(recommendedBid.getRecommended()).map(String::valueOf).orElse(suggestedTargetVo.getSuggested()));
                        suggestedTargetVo.setRangeStart(Optional.ofNullable(recommendedBid.getRangeStart()).map(String::valueOf).orElse(suggestedTargetVo.getRangeStart()));
                        suggestedTargetVo.setRangeEnd(Optional.ofNullable(recommendedBid.getRangeEnd()).map(String::valueOf).orElse(suggestedTargetVo.getRangeEnd()));

                        AmazonSbAdTargeting amazonSbAdTargeting = targetingMap.get(suggestedTargetVo.getTargetId());
                        if (amazonSbAdTargeting == null) return;
                        amazonSbAdTargeting.setSuggested(Optional.ofNullable(recommendedBid.getRecommended()).map(String::valueOf).map(BigDecimal::new).orElse(amazonSbAdTargeting.getSuggested()));
                        amazonSbAdTargeting.setRangeStart(Optional.ofNullable(recommendedBid.getRangeStart()).map(String::valueOf).map(BigDecimal::new).orElse(amazonSbAdTargeting.getRangeStart()));
                        amazonSbAdTargeting.setRangeEnd(Optional.ofNullable(recommendedBid.getRangeEnd()).map(String::valueOf).map(BigDecimal::new).orElse(amazonSbAdTargeting.getRangeEnd()));
                    }
                });
            });
            return suggestedTargetVos;
        }, executor)));

        try {
            CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).get();
        } catch (InterruptedException | ExecutionException ignored) {
        }
        completableFutures.forEach(future -> {
            try {
                suggestedTargetVoList.addAll(future.get());
            } catch (InterruptedException | ExecutionException e) {
                log.info(String.format("sb targeting getSuggestedBidMultiShop error, puid:%s", puid), e);
            }
        });

        // 批量更新建议竞价
        amazonSbAdTargetingDao.batchUpdateSuggestValue(puid, Lists.newArrayList(targetingMap.values()));
        return ResultUtil.returnSucc(suggestedTargetVoList);
    }

    private String checkAddTargetingVo(List<TargetingVo> targetings) {
        if (CollectionUtils.isEmpty(targetings)) {
            return SBCreateErrorEnum.TARGETING_REQ_PARAM_IS_NULL.getMsg();
        }

        for (TargetingVo vo : targetings) {
            if (!SdTargetTypeEnum.asin.name().equals(vo.getType())
                    && !SdTargetTypeEnum.category.name().equals(vo.getType())) {
                return SBCreateErrorEnum.TARGETING_TYPE_IS_NULL.getMsg();
            }
            if ((TargetTypeEnum.asin.name().equals(vo.getType()) && StringUtils.isBlank(vo.getAsin()))
                    || (TargetTypeEnum.category.name().equals(vo.getType()) && StringUtils.isBlank(vo.getCategoryId()))) {
                return SBCreateErrorEnum.TARGETING_CONTENT_IS_NULL.getMsg();
            }
            if (StringUtils.isBlank(vo.getBid())) {
                return SBCreateErrorEnum.TARGETING_CONTENT_IS_NULL.getMsg();
            }
        }
        return null;
    }

    private List<AmazonSbAdTargeting> convertAddTargetingVoToPO(Integer uid, AmazonSbAdGroup adGroup, List<TargetingVo> targetings) {
        List<AmazonSbAdTargeting> amazonAdTargetList = new ArrayList<>(targetings.size());
        AmazonSbAdTargeting target;

        Expression expression;
        List<Expression> expressions;
        for (TargetingVo vo : targetings) {
            target = new AmazonSbAdTargeting();
            amazonAdTargetList.add(target);

            target.setPuid(adGroup.getPuid());
            target.setShopId(adGroup.getShopId());
            target.setMarketplaceId(adGroup.getMarketplaceId());
            target.setProfileId(adGroup.getProfileId());
            target.setAdGroupId(adGroup.getAdGroupId());
            target.setCampaignId(adGroup.getCampaignId());
            target.setType(vo.getType());
            target.setBid(new BigDecimal(vo.getBid()));
            target.setCreateId(uid);
            target.setCreateInAmzup(1);

            if (StringUtils.isNotBlank(vo.getSuggested())) {
                target.setSuggested(new BigDecimal(vo.getSuggested()));
            }
            if (StringUtils.isNotBlank(vo.getRangeStart())) {
                target.setRangeStart(new BigDecimal(vo.getRangeStart()));
            }
            if (StringUtils.isNotBlank(vo.getRangeEnd())) {
                target.setRangeEnd(new BigDecimal(vo.getRangeEnd()));
            }

            expressions = new ArrayList<>();
            if (SdTargetTypeEnum.asin.name().equals(vo.getType())) {
                expression = new Expression();
                expression.setType(ExpressionEnum.asinSameAs.value());
                expression.setValue(vo.getAsin());
                expressions.add(expression);

                target.setTargetText(vo.getAsin());
                target.setImgUrl(vo.getImgUrl());
                target.setTitle(vo.getTitle());
            } else {
                expression = new Expression();
                expression.setType(ExpressionEnum.asinCategorySameAs.value());
                expression.setValue(vo.getCategoryId());
                expressions.add(expression);
                target.setTargetText(vo.getCategoryName());
                target.setCategoryName(vo.getCategoryName());
                target.setBrandName(vo.getBrandName());
                target.setMinPrice(vo.getMinPrice());
                target.setMaxPrice(vo.getMaxPrice());
                target.setMinReviewRating(vo.getMinReviewRating());
                target.setMaxReviewRating(vo.getMaxReviewRating());

                if (StringUtils.isNotBlank(vo.getBrand())) {
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinBrandSameAs.value());
                    expression.setValue(vo.getBrand());
                    expressions.add(expression);
                }
                if (StringUtils.isNotBlank(vo.getMinPrice()) || StringUtils.isNotBlank(vo.getMaxPrice())) {
                    expression = new Expression();
                    if (StringUtils.isBlank(vo.getMinPrice())) {
                        expression.setType(ExpressionEnum.asinPriceLessThan.value());
                        expression.setValue(vo.getMaxPrice());
                        expressions.add(expression);
                    } else if (StringUtils.isBlank(vo.getMaxPrice())) {
                        expression.setType(ExpressionEnum.asinPriceGreaterThan.value());
                        expression.setValue(vo.getMinPrice());
                        expressions.add(expression);
                    } else {
                        expression.setType(ExpressionEnum.asinPriceBetween.value());
                        expression.setValue(vo.getMinPrice() + "-" + vo.getMaxPrice());
                        expressions.add(expression);
                    }
                }
                if (vo.getMinReviewRating() != null || vo.getMaxReviewRating() != null) {
                    expression = new Expression();
                    if (vo.getMinReviewRating() == null) {
                        expression.setType(ExpressionEnum.asinReviewRatingLessThan.value());
                        expression.setValue(String.valueOf(vo.getMaxReviewRating()));
                        expressions.add(expression);
                    } else if (vo.getMaxReviewRating() == null) {
                        expression.setType(ExpressionEnum.asinReviewRatingGreaterThan.value());
                        expression.setValue(String.valueOf(vo.getMinReviewRating()));
                        expressions.add(expression);
                    } else {
                        expression.setType(ExpressionEnum.asinReviewRatingBetween.value());
                        expression.setValue(vo.getMinReviewRating() + "-" + vo.getMaxReviewRating());
                        expressions.add(expression);
                    }
                }
                if (vo.getPrimeShippingEligible() != null) {
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinIsPrimeShippingEligible.value());
                    expression.setValue(vo.getPrimeShippingEligible().toString());
                    expressions.add(expression);
                }
            }
            target.setExpression(JSONUtil.objectToJson(expressions));

        }

        return amazonAdTargetList;
    }

    @Override
    public Result updateBatchMultiShop(List<UpdateBatchTargetVo> vos, String type, String loginIp) {
        Map<Integer, List<UpdateBatchTargetVo>> voMap = vos.stream().collect(Collectors.groupingBy(UpdateBatchTargetVo::getShopId));
        BatchResponseVo<UpdateBatchTargetVo, AmazonAdTargeting> batchData = new BatchResponseVo<>();
        Result<BatchResponseVo<UpdateBatchTargetVo, AmazonAdTargeting>> result;
        BatchResponseVo<UpdateBatchTargetVo, AmazonAdTargeting> data;
        for (Map.Entry<Integer, List<UpdateBatchTargetVo>> entry : voMap.entrySet()) {
            result = this.updateBatch(entry.getValue(), type, loginIp);
            if (result.error()) {
                return ResultUtil.error(result.getMsg());
            }

            data = result.getData();
            if (data != null) {
                batchData.addCountNum(data.getCountNum());
                batchData.addSuccessNum(data.getSuccessNum());
                batchData.addFailNum(data.getFailNum());
                batchData.addSuccessList(data.getSuccessList());
                batchData.addErrorList(data.getErrorList());
            }
        }
        return ResultUtil.success(batchData);
    }

    @Override
    public Result updateBatch(List<UpdateBatchTargetVo> vos,String type, String ip) {

        int puid = vos.get(0).getPuid();

        int uid = vos.get(0).getUid();
        int shopId = vos.get(0).getShopId();

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }


        List<UpdateBatchTargetVo> errorList = Lists.newArrayList();

        List<Long> ids = vos.stream().filter(e-> e.getId()!= null && e.getId()>0).map(e->Long.valueOf(e.getId().toString())).collect(Collectors.toList());
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(ids)){
            return ResultUtil.error("参数错误");
        }

        List<AmazonSbAdTargeting> listByIdList = amazonSbAdTargetingDao.getListByLongIdList(puid,ids);
        List<AmazonSbAdTargeting> updateList = Lists.newArrayList();
        Map<Long, AmazonSbAdTargeting> amazonAdTargetingMap = listByIdList.stream().collect(Collectors.toMap(AmazonSbAdTargeting::getId, e -> e));
        for (UpdateBatchTargetVo vo: vos) {
            checkBatchVo(vo, type);
            if(StringUtils.isNotBlank(vo.getFailReason())){
                errorList.add(vo);
                continue;
            }
            AmazonSbAdTargeting oldAmazonAdTargeting = amazonAdTargetingMap.get(vo.getId());
            if (oldAmazonAdTargeting == null) {
                vo.setFailReason("对象不存在");
                errorList.add(vo);
                continue;
            }
            AmazonSbAdTargeting amazonAdTargeting = new AmazonSbAdTargeting();
            BeanUtils.copyProperties(oldAmazonAdTargeting, amazonAdTargeting);
            convertVoToBatchUpdatePo(amazonAdTargeting, vo,type);
            updateList.add(amazonAdTargeting);
        }
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(updateList)){
            BatchResponseVo<UpdateBatchTargetVo, AmazonAdTargeting> data =  new  BatchResponseVo<> ();
            data.setErrorList(errorList);
            data.setCountNum(vos.size());
            data.setFailNum(errorList.size());
            data.setSuccessNum(0);
            return ResultUtil.success(data);
        }

        Result<BatchResponseVo<UpdateBatchTargetVo,AmazonSbAdTargeting>> result = cpcSbTargetApiService.update(shop,profile,updateList,type);

        List<AdManageOperationLog> targetsLogs = Lists.newArrayList();
        for (AmazonSbAdTargeting targeting : updateList) {
            AdManageOperationLog targetsLog = adOperationLogService.getSbTargetLog(amazonAdTargetingMap.get(targeting.getId()), targeting);
            targetsLog.setIp(ip);
            targetsLogs.add(targetsLog);
        }


        if (result.success()) {
            BatchResponseVo<UpdateBatchTargetVo, AmazonSbAdTargeting> data = result.getData();
            List<UpdateBatchTargetVo> keywordsError = data.getErrorList();
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(errorList)){
                keywordsError.addAll(errorList);
                data.setFailNum(data.getErrorList().size());
                data.setCountNum( (data.getErrorList() == null? 0 : data.getErrorList().size())+(data.getSuccessList() == null? 0 : data.getSuccessList().size())) ;
            }
            List<AmazonSbAdTargeting> amazonAdKeywordsSuccess = data.getSuccessList();
            Map<String, AmazonSbAdTargeting> successTargetsMap = amazonAdKeywordsSuccess.stream().collect(Collectors.toMap(AmazonSbAdTargeting::getTargetId, e -> e));
            Map<String, UpdateBatchTargetVo> errorTargetsMap = keywordsError.stream().collect(Collectors.toMap(UpdateBatchTargetVo::getTargetId, e -> e));
            for (AdManageOperationLog targetsLog : targetsLogs) {
                if (!StringUtil.isEmptyObject(successTargetsMap.get(targetsLog.getTargetId()))) {
                    targetsLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                }
                if (!StringUtil.isEmptyObject(errorTargetsMap.get(targetsLog.getTargetId()))) {
                    targetsLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    targetsLog.setResultInfo(errorTargetsMap.get(targetsLog.getTargetId()).getFailReason());
                }
            }

            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(amazonAdKeywordsSuccess)){
                amazonSbAdTargetingDao.updateList(puid, amazonAdKeywordsSuccess,type);
                saveDoris(puid, shopId, amazonAdKeywordsSuccess.stream().map(AmazonSbAdTargeting::getTargetId).collect(Collectors.toList()));
                //更新成功数据打日志
                log.info("用户批量更新成功，typ:{},updateId:{},puid :{},shopid:{},更新成功数据：{}",type,uid,puid,shopId, JSONUtil.objectToJson(amazonAdKeywordsSuccess));
                //前端不需要展示成功消息，减少消耗移除成功数据
                data.getSuccessList().clear();
            }

        } else {
            for (AdManageOperationLog targetsLog : targetsLogs) {
                targetsLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                targetsLog.setResultInfo(result.getMsg());
            }
        }
        adOperationLogService.batchLogsMergeByAdGroup(targetsLogs);
        return result;

    }

    /**
     * 根据更新类型进行数据校验
     *
     */
    private UpdateBatchTargetVo checkBatchVo(UpdateBatchTargetVo updateBatchTargetVo,String type) {

        if(updateBatchTargetVo.getId() == null || updateBatchTargetVo.getId() < 1 ){
            updateBatchTargetVo.setFailReason("参数错误");
            return updateBatchTargetVo;
        }

        if (Constants.CPC_SP_TARGET_BATCH_UPDATE_BID.equals(type)) {
            if(updateBatchTargetVo.getBid() == null || updateBatchTargetVo.getBid().isNaN() || updateBatchTargetVo.getBid() < 0.02){
                updateBatchTargetVo.setFailReason("竞价错误 ");
            }
            return updateBatchTargetVo;
        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            if(StringUtils.isEmpty(updateBatchTargetVo.getState())){
                updateBatchTargetVo.setFailReason("参数错误");
            }
            return updateBatchTargetVo;
        } else {
            updateBatchTargetVo.setFailReason("参数错误");
            return updateBatchTargetVo;
        }

    }

    // 更新广告组时vo->po
    private void convertVoToBatchUpdatePo(AmazonSbAdTargeting amazonAdTargeting, UpdateBatchTargetVo vo,String type) {
        if (Constants.CPC_SD_TARGET_BATCH_UPDATE_BID.equals(type)){
            amazonAdTargeting.setBid(BigDecimal.valueOf(vo.getBid()));
        }
        if(Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
            amazonAdTargeting.setState(vo.getState());
        }
        amazonAdTargeting.setUpdateId(vo.getUid());
    }

    private List<Object> buildUpLogMessage(Map<Long, AmazonSbAdTargeting> oldList,List<AmazonSbAdTargeting> newList,String type){
        List<Object> dataList = new ArrayList<>();
        StringBuilder builder = new StringBuilder();
        if(Constants.CPC_SD_TARGET_BATCH_UPDATE_BID.equals(type)){
            dataList.add("SB商品投放批量修改默认竞价");

        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
            dataList.add("SB商品投放批量修改状态");

        }
        newList.forEach(e ->{
            AmazonSbAdTargeting old = oldList.get(e.getId());

            if(Constants.CPC_SD_TARGET_BATCH_UPDATE_BID.equals(type)){

                builder.append("id:").append(e.getId());
                if(old != null){
                    builder.append(",旧值:").append(old.getBid());
                }
                builder.append(",新值:").append(e.getBid());
            } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
                builder.append("id:").append(e.getId());
                if(old != null){
                    builder.append(",旧值:").append(old.getState());
                }
                builder.append(",新值:").append(e.getState());
            }
            dataList.add(builder.toString());
            builder.delete(0,builder.length());
        });
        return dataList;
    }


    /**
     * 写入doris
     * @param targetingList
     */
    private void saveDoris(List<AmazonSbAdTargeting> targetingList, boolean create, boolean update) {
        try {
            List<OdsAmazonAdTargetingSb> collect = targetingList.stream().map(x -> {
                OdsAmazonAdTargetingSb odsAmazonAdTargetingSb = new OdsAmazonAdTargetingSb();
                BeanUtils.copyProperties(x, odsAmazonAdTargetingSb);
                if (create) {
                    odsAmazonAdTargetingSb.setCreateTime(new Date());
                }
                if (update) {
                    odsAmazonAdTargetingSb.setUpdateTime(new Date());
                }
                if (StringUtils.isNotBlank(odsAmazonAdTargetingSb.getState())) {
                    odsAmazonAdTargetingSb.setState(odsAmazonAdTargetingSb.getState().toLowerCase());
                }
                return odsAmazonAdTargetingSb;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sb targeting save doris error", e);
        }
    }


    /**
     * 写入doris
     * @param puid
     * @param shopId
     * @param targetIdList
     */
    @Override
    public void saveDoris(Integer puid, Integer shopId, List<String> targetIdList) {
        try {
            if (CollectionUtils.isEmpty(targetIdList)) {
                return;
            }
            List<AmazonSbAdTargeting> targetingList = amazonSbAdTargetingDao.listByTargetId(puid, shopId, targetIdList);
            saveDoris(targetingList, false, false);
        } catch (Exception e) {
            log.error("sb targeting save doris error", e);
        }
    }

    private TargetRecommendsPageVo getRecommendTargetListPage(List<CategoryRecommendationResults> dataList, Integer pageSize,
                                                               Integer pageNo) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(dataList)) {
            TargetRecommendsPageVo targetRecommendsPageVo = new TargetRecommendsPageVo();
            targetRecommendsPageVo.setTotalPage(0);
            targetRecommendsPageVo.setTotalSize(0);
            return targetRecommendsPageVo;
        }
        //总页数
        int totalSize = dataList.size();
        int totalPage = (totalSize - 1) / pageSize + 1;
        //如果当前页大于总页数,则显示最后一页
        pageNo = Math.min(pageNo, totalPage);
        //如果当前页小于0,则显示第一页
        pageNo = Math.max(pageNo, 1);
        //记录开始值
        int start = pageSize * (pageNo - 1);
        int end = start + pageSize;
        dataList = dataList.subList(start, Math.min(totalSize, end));
        return TargetRecommendsPageVo.builder()
                .categoryList(dataList)
                .totalSize(totalSize)
                .totalPage(totalPage)
                .build();
    }
}
