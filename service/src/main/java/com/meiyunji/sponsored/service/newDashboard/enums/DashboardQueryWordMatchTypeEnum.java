package com.meiyunji.sponsored.service.newDashboard.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-04-07 14:56
 */
@Getter
public enum DashboardQueryWordMatchTypeEnum {
    BOARD("BROAD", "broad", "广泛匹配", "MANUAL"),
    PHRASE("PHRASE", "phrase", "词组匹配", "MANUAL"),
    EXACT("EXACT", "exact", "精确匹配", "MANUAL"),
    CLOSE_MATCH("close-match", "close-match", "紧密匹配", "AUTO"),
    LOOSE_MATCH("loose-match", "loose-match", "宽泛匹配", "AUTO"),
    SUBSTITUTES("substitutes", "substitutes", "同类商品", "AUTO"),
    COMPLEMENTS("complements", "complements", "关联商品", "AUTO"),
    ASIN("asin", "asin", "精准", "MANUAL"),
    ASIN_SAME_AS("asinSameAs", "asinSameAs", "精准", "MANUAL"),
    ASIN_EXPANDED("asin-expanded", "asin-expanded", "拓展", "MANUAL"),
    CATEGORY("category", "category", "类目", "MANUAL"),
    THEME("THEME", "theme", "主题", "MANUAL"),
    ;

    private String code;
    private String msg;
    private String desc;
    private String targetType;

    DashboardQueryWordMatchTypeEnum(String code, String msg, String desc, String targetType) {
        this.code = code;
        this.msg = msg;
        this.desc = desc;
        this.targetType = targetType;
    }

    public static Map<String, DashboardQueryWordMatchTypeEnum> dataMap = Arrays.stream(DashboardQueryWordMatchTypeEnum.values())
            .collect(Collectors.toMap(DashboardQueryWordMatchTypeEnum::getCode, Function.identity(), (oldVal, newVal) -> newVal));

    public static Map<String, DashboardQueryWordMatchTypeEnum> upperCaseDataMap = Arrays.stream(DashboardQueryWordMatchTypeEnum.values())
            .collect(Collectors.toMap(e -> e.getCode().toUpperCase(), Function.identity(), (oldVal, newVal) -> newVal));
}
