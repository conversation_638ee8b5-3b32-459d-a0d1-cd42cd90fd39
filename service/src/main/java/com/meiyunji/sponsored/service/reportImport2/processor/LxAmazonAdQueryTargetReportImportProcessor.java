package com.meiyunji.sponsored.service.reportImport2.processor;

import com.alibaba.fastjson.JSONReader;
import com.amazon.advertising.spV3.targeting.entity.TargetExpression;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.enums.AmazonTargetingReportTypeEnum;
import com.meiyunji.sponsored.common.util.GZipUtils;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.MD5Util;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.enums.AsinMatchValueEnum;
import com.meiyunji.sponsored.service.enums.AutoTargetTypeEnum;
import com.meiyunji.sponsored.service.enums.QueryTargetingExpressionTransferEnum;
import com.meiyunji.sponsored.service.reportImport2.enums.AmazonLxReportType;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdQueryTargetReport;
import com.meiyunji.sponsored.service.reportImport2.vo.AmazonAdReportImportMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.cpc.util.Constants.ASIN_REGEX;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-11-28  14:13
 */
@Service
@Slf4j
public class LxAmazonAdQueryTargetReportImportProcessor extends AbstractAmazonAdLxReportImportProcessor<LxAmazonAdQueryTargetReport> {

    protected final IAmazonAdTargetingShardingDao amazonAdTargetingShardingDao;
    protected final ICpcQueryTargetingReportDao cpcQueryTargetingReportDao;

    protected LxAmazonAdQueryTargetReportImportProcessor(IAmazonAdCampaignAllDao amazonAdCampaignAllDao, IAmazonAdGroupDao amazonAdGroupDao, IAmazonSdAdGroupDao amazonSdAdGroupDao, IAmazonSbAdGroupDao amazonSbAdGroupDao, IScVcShopAuthDao shopAuthDao, CosBucketClient tempBucketClient,
                                                         IAmazonAdTargetingShardingDao amazonAdTargetingShardingDao, ICpcQueryTargetingReportDao cpcQueryTargetingReportDao) {
        super(amazonAdCampaignAllDao, amazonAdGroupDao, amazonSdAdGroupDao, amazonSbAdGroupDao, shopAuthDao, tempBucketClient);
        this.amazonAdTargetingShardingDao = amazonAdTargetingShardingDao;
        this.cpcQueryTargetingReportDao = cpcQueryTargetingReportDao;
    }


    @Override
    public void importReport(AmazonAdReportImportMessage importMessage) {
        //读取数据
        try (InputStreamReader inputStreamReader = new InputStreamReader(new ByteArrayInputStream(GZipUtils.release(tempBucketClient.getObjectToBytes(importMessage.getFileId().replaceFirst("/", "")))))) {
            ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(importMessage.getShopId(), importMessage.getPuid());
            JSONReader jsonReader = new JSONReader(inputStreamReader);
            jsonReader.startArray();
            List<LxAmazonAdQueryTargetReport> reports = Lists.newArrayListWithExpectedSize(500);
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                LxAmazonAdQueryTargetReport report = new LxAmazonAdQueryTargetReport();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.isValid()) {
                    reports.add(report);
                    if (reports.size() >= 500) {
                        this.dealReport(importMessage, reports, shopAuth);
                        reports = Lists.newArrayListWithExpectedSize(500);
                    }
                }
            }
            jsonReader.endArray();
            jsonReader.close();
            if (CollectionUtils.isNotEmpty(reports)) {
                this.dealReport(importMessage, reports, shopAuth);
            }
        } catch (Exception e) {
            log.error("import query keyword report error puid:{},shopId：{}，scheduleId :{} error:", importMessage.getPuid(), importMessage.getShopId(), importMessage.getScheduleId(), e);
        }
    }

    private void dealReport(AmazonAdReportImportMessage importMessage, List<LxAmazonAdQueryTargetReport> reports, ShopAuth shopAuth) {
        Integer puid = importMessage.getPuid();
        Integer shopId = importMessage.getShopId();
        String adType = importMessage.getAdType().toLowerCase();
        //获取广告活动详情
        List<String> campaignIds = reports.stream().map(LxAmazonAdQueryTargetReport::getCampaignId).collect(Collectors.toList());
        Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = amazonAdCampaignAllDao.getByCampaignIds(puid, shopId, shopAuth.getMarketplaceId(), campaignIds, adType)
                .stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));
        //获取广告组详情
        List<String> adGroupIds = reports.stream().map(LxAmazonAdQueryTargetReport::getAdGroupId).collect(Collectors.toList());
        Map<String, AmazonAdGroup> amazonAdGroupMap = amazonAdGroupDao.getListByAdGroupIds(puid, shopId, adGroupIds)
                .stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity(), (e1, e2) -> e2));
        //获取关键词详情
        List<String> targetIds = reports.stream().map(LxAmazonAdQueryTargetReport::getTargetId).collect(Collectors.toList());
        Map<String, AmazonAdTargeting> amazonAdTargetingMap = amazonAdTargetingShardingDao.getByAdTargetIds(puid, shopId, targetIds)
                .stream().collect(Collectors.toMap(AmazonAdTargeting::getTargetId, Function.identity(), (e1, e2) -> e2));


        List<CpcQueryTargetingReport> insertSpReport = new ArrayList<>();
        reports.forEach(e -> {
            if (StringUtils.isBlank(e.getQuery())) {
                log.error("pxq-report-import puid : {} shop_id : {} query 不存在", puid, shopId);
                return;
            }
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(e.getCampaignId());
            if (amazonAdCampaignAll == null) {
                log.error("pxq-report-import puid : {} shop_id : {} campaignId : {} 不存在", puid, shopId, e.getCampaignId());
                return;
            }
            AmazonAdGroup amazonAdGroup = amazonAdGroupMap.get(e.getAdGroupId());
            if (amazonAdGroup == null) {
                log.error("pxq-report-import puid : {} shop_id : {} sp adGroupId : {} 不存在", puid, shopId, e.getAdGroupId());
                return;
            }
            AmazonAdTargeting amazonAdTargeting = amazonAdTargetingMap.get(e.getTargetId());
            if (amazonAdTargeting == null) {
                log.error("pxq-report-import puid : {} shop_id : {} sp keywordId : {} 不存在", puid, shopId, e.getTargetId());
                return;
            }
            CpcQueryTargetingReport spReport = this.getSpReport(puid, shopAuth, e, amazonAdCampaignAll, amazonAdGroup, amazonAdTargeting, importMessage);
            insertSpReport.add(spReport);
        });
        if (CollectionUtils.isNotEmpty(insertSpReport)) {
            cpcQueryTargetingReportDao.insertList(puid, insertSpReport);
        }
    }

    private CpcQueryTargetingReport getSpReport(Integer puid, ShopAuth shopAuth, LxAmazonAdQueryTargetReport report, AmazonAdCampaignAll campaign, AmazonAdGroup group, AmazonAdTargeting target, AmazonAdReportImportMessage importMessage) {
        CpcQueryTargetingReport cpcQueryTargetReport = new CpcQueryTargetingReport();
        cpcQueryTargetReport.setPuid(puid);
        cpcQueryTargetReport.setShopId(shopAuth.getId());
        cpcQueryTargetReport.setMarketplaceId(shopAuth.getMarketplaceId());
        cpcQueryTargetReport.setCampaignId(campaign.getCampaignId());
        cpcQueryTargetReport.setAdGroupId(group.getAdGroupId());
        cpcQueryTargetReport.setTargetId(target.getTargetId());
        cpcQueryTargetReport.setCountDate(importMessage.getCountDate());
        cpcQueryTargetReport.setTitle(target.getTitle());
        if (AmazonLxReportType.queryTarget.name().equals(importMessage.getReportType())) {
            cpcQueryTargetReport.setTargetingType(AmazonTargetingReportTypeEnum.MANUAL.getTargetingType());
            if (StringUtils.isNotBlank(target.getResolvedExpression())) {
                List<TargetExpression> targetExpressions = JSONUtil.jsonToArray(target.getResolvedExpression(), TargetExpression.class);
                if (CollectionUtils.isNotEmpty(targetExpressions)) {
                    StringJoiner sj = new StringJoiner(" ");
                    targetExpressions.stream().filter(e -> e.getType() != null && e.getValue() != null).forEach(e -> {
                        QueryTargetingExpressionTransferEnum expressionEnum = QueryTargetingExpressionTransferEnum.fromType(e.getType());
                        if (expressionEnum != null) {
                            String value = expressionEnum.getHasQuotationMarks() ? ("\"" + e.getValue() + "\"") : e.getValue();
                            sj.add(expressionEnum.getValue() + value);
                        }
                    });
                    String expression = sj.toString();
                    cpcQueryTargetReport.setTargetingExpression(expression);
                    cpcQueryTargetReport.setTargetingText(expression);
                }
            }
        } else if (AmazonLxReportType.queryAutoTarget.name().equals(importMessage.getReportType())) {
            cpcQueryTargetReport.setTargetingType(AmazonTargetingReportTypeEnum.AUTO.getTargetingType());
            if (AutoTargetTypeEnum.queryHighRelMatches.getAutoTargetType().equals(target.getTargetingValue())) {
                cpcQueryTargetReport.setTargetingExpression(AsinMatchValueEnum.closematch.getMatchType());
                cpcQueryTargetReport.setTargetingText(AsinMatchValueEnum.closematch.getMatchType());
            } else if (AutoTargetTypeEnum.queryBroadRelMatches.getAutoTargetType().equals(target.getTargetingValue())) {
                cpcQueryTargetReport.setTargetingExpression(AsinMatchValueEnum.loosematch.getMatchType());
                cpcQueryTargetReport.setTargetingText(AsinMatchValueEnum.loosematch.getMatchType());
            } else if (AutoTargetTypeEnum.asinSubstituteRelated.getAutoTargetType().equals(target.getTargetingValue())) {
                cpcQueryTargetReport.setTargetingExpression(AsinMatchValueEnum.substitutes.getMatchType());
                cpcQueryTargetReport.setTargetingText(AsinMatchValueEnum.substitutes.getMatchType());
            } else if (AutoTargetTypeEnum.asinAccessoryRelated.getAutoTargetType().equals(target.getTargetingValue())) {
                cpcQueryTargetReport.setTargetingExpression(AsinMatchValueEnum.complements.getMatchType());
                cpcQueryTargetReport.setTargetingText(AsinMatchValueEnum.complements.getMatchType());
            }
        }
        cpcQueryTargetReport.setCampaignName(campaign.getName());
        cpcQueryTargetReport.setAdGroupName(group.getName());
        cpcQueryTargetReport.setQuery(report.getQuery().toLowerCase());
        cpcQueryTargetReport.setQueryId(MD5Util.getMD5(cpcQueryTargetReport.getTargetId() + cpcQueryTargetReport.getQuery()));
        cpcQueryTargetReport.setIsAsin(StringUtils.isNotBlank(cpcQueryTargetReport.getQuery()) && Pattern.compile(ASIN_REGEX).matcher(cpcQueryTargetReport.getQuery()).matches());
        //数据字段
        cpcQueryTargetReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
        cpcQueryTargetReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        cpcQueryTargetReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        cpcQueryTargetReport.setSaleNum(report.getOrders() != null ? report.getOrders() : 0);
        cpcQueryTargetReport.setAdSaleNum(report.getDirectOrders() != null ? report.getDirectOrders() : 0);
        cpcQueryTargetReport.setTotalSales(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        cpcQueryTargetReport.setAdSales(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);
        cpcQueryTargetReport.setOrderNum(report.getAdUnits() != null ? report.getAdUnits() : 0);
        cpcQueryTargetReport.setAdOrderNum(report.getDirectUnits() != null ? report.getDirectUnits() : 0);
        return cpcQueryTargetReport;
    }
}
