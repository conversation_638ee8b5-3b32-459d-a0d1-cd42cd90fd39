package com.meiyunji.sponsored.service.reportImport2.processor;

import com.alibaba.fastjson.JSONReader;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.util.GZipUtils;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.reportImport.model.LxCampaignPlacementReport;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdCampaignPlacementReport;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdProductReport;
import com.meiyunji.sponsored.service.reportImport2.vo.AmazonAdReportImportMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * lx活动报告导入处理器
 *
 * <AUTHOR>
 * @date 2023/05/26
 */
@Service
@Slf4j
public class LxAmazonAdCampaignPlacementReportImportProcessor extends AbstractAmazonAdLxReportImportProcessor<LxAmazonAdProductReport> {


    private final IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao;

    private final IAmazonAdCampaignPlacementReportDao amazonAdCampaignPlacementReportDao;

    /**
     * 搜索结果顶部(首页)
     */
    private String top = "TOP OF SEARCH ON-AMAZON";

    /**
     * 搜索结果的其余位置
     */
    private String other = "OTHER ON-AMAZON";

    /**
     * 商品页面
     */
    private String product = "DETAIL PAGE ON-AMAZON";


    private String search = "SEARCH ON-AMAZON";


    protected LxAmazonAdCampaignPlacementReportImportProcessor(IAmazonAdCampaignAllDao amazonAdCampaignAllDao, IAmazonAdGroupDao amazonAdGroupDao,
                                                               IAmazonSdAdGroupDao amazonSdAdGroupDao, IAmazonSbAdGroupDao amazonSbAdGroupDao,
                                                               IScVcShopAuthDao shopAuthDao, CosBucketClient tempBucketClient,
                                                               IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao,
                                                               IAmazonAdCampaignPlacementReportDao amazonAdCampaignPlacementReportDao) {
        super(amazonAdCampaignAllDao, amazonAdGroupDao, amazonSdAdGroupDao, amazonSbAdGroupDao, shopAuthDao, tempBucketClient);
        this.amazonAdCampaignAllReportDao = amazonAdCampaignAllReportDao;
        this.amazonAdCampaignPlacementReportDao = amazonAdCampaignPlacementReportDao;
    }


    @Override
    public void importReport(AmazonAdReportImportMessage importMessage) {

        //读取数据
        try (InputStreamReader inputStreamReader = new InputStreamReader(new ByteArrayInputStream(GZipUtils.release(tempBucketClient.getObjectToBytes(importMessage.getFileId().replaceFirst("/", "")))))) {
            ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(importMessage.getShopId(), importMessage.getPuid());
            JSONReader jsonReader = new JSONReader(inputStreamReader);
            jsonReader.startArray();
            List<LxAmazonAdCampaignPlacementReport> reports = Lists.newArrayListWithExpectedSize(500);
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                LxAmazonAdCampaignPlacementReport report = new LxAmazonAdCampaignPlacementReport();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.getImpressions() != 0) {
                    reports.add(report);
                    if (reports.size() >= 500) {
                        dealReport(importMessage, reports, shopAuth);
                        reports = Lists.newArrayListWithExpectedSize(500);
                    }
                }

            }
            jsonReader.endArray();
            jsonReader.close();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReport(importMessage, reports, shopAuth);
            }
        } catch (Exception e) {
            log.error("import campaign report error puid:{},shopId：{}，scheduleId :{} error:", importMessage.getPuid(), importMessage.getShopId(), importMessage.getScheduleId(), e);
        }

    }

    private void dealReport(AmazonAdReportImportMessage importMessage, List<LxAmazonAdCampaignPlacementReport> reports, ShopAuth shopAuth) {
        Integer puid = importMessage.getPuid();
        Integer shopId = importMessage.getShopId();

        String adType = importMessage.getAdType().toLowerCase();
        List<String> campaignIds = reports.stream().map(LxAmazonAdCampaignPlacementReport::getCampaignId).collect(Collectors.toList());
        List<AmazonAdCampaignAll> byCampaignIds = amazonAdCampaignAllDao.getByCampaignIds(puid, shopId, shopAuth.getMarketplaceId(), campaignIds, adType);
        Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = byCampaignIds.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));


        List<AmazonAdCampaignAllReport> spReports = new ArrayList<>();

        reports.forEach(e -> {
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(e.getCampaignId());
            if (amazonAdCampaignAll == null) {
                log.error("pxq-report-import puid : {} shop_id : {} campaignId : {} 不存在", puid, shopId, e.getCampaignId());
                return;
            }


            if (CampaignTypeEnum.sp.name().equalsIgnoreCase(importMessage.getAdType())) {
                AmazonAdCampaignAllReport spReport = buildSpAdProductReport(importMessage.getCountDate(), e, amazonAdCampaignAll, shopAuth);
                spReports.add(spReport);
            }
        });

        // 注释入库活动报告表，后续删除该代码
//        //持久数据到数据库
//        if (CollectionUtils.isNotEmpty(spReports)) {
//            amazonAdCampaignAllReportDao.insertOrUpdateList(puid, spReports);
//        }


    }


    /**
     * 构建sp广告组报告
     *
     * @param report 报告
     * @return {@link AmazonAdCampaignAllReport}
     */
    private AmazonAdCampaignAllReport buildSpAdProductReport(String countDate, LxAmazonAdCampaignPlacementReport report, AmazonAdCampaignAll campaignAll, ShopAuth shopAuth) {


        AmazonAdCampaignAllReport amazonAdCampaignReport = new AmazonAdCampaignAllReport();
        amazonAdCampaignReport.setPuid(shopAuth.getPuid());
        amazonAdCampaignReport.setShopId(shopAuth.getId());
        amazonAdCampaignReport.setMarketplaceId(shopAuth.getMarketplaceId());
        amazonAdCampaignReport.setCountDate(countDate);
        amazonAdCampaignReport.setCampaignId(campaignAll.getCampaignId());
        amazonAdCampaignReport.setType(CampaignTypeEnum.sp.getCampaignType());
        String placement;
        if (product.equalsIgnoreCase(report.getPlacement())) {
            placement = "Detail Page on-Amazon";
        } else if (top.equalsIgnoreCase(report.getPlacement())) {
            placement = "Top of Search on-Amazon";
        } else if (other.equalsIgnoreCase(report.getPlacement())) {
            placement = "Other on-Amazon";
        } else {
            placement = "Search on-Amazon";
        }

        amazonAdCampaignReport.setCampaignType(placement); //All表示汇总信息
        amazonAdCampaignReport.setPlacement(placement);
        amazonAdCampaignReport.setIsSummary(0);
        amazonAdCampaignReport.setCampaignName(report.getCampaignName());

        amazonAdCampaignReport.setSales7d(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        amazonAdCampaignReport.setSales7dSameSKU(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);

        amazonAdCampaignReport.setUnitsOrdered7d(report.getAdUnits() != null ? report.getAdUnits() : 0);
        amazonAdCampaignReport.setUnitsOrdered7dSameSKU(report.getDirectUnits() != null ? report.getDirectUnits() : 0);
        amazonAdCampaignReport.setConversions7d(report.getOrders() != null ? report.getOrders() : 0);
        amazonAdCampaignReport.setConversions7dSameSKU(report.getDirectOrders() != null ? report.getDirectOrders() : 0);

        amazonAdCampaignReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        amazonAdCampaignReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        amazonAdCampaignReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
        amazonAdCampaignReport.setViewImpressions(report.getViewImpressions() != null ? report.getViewImpressions() : 0);

        return amazonAdCampaignReport;
    }


}
