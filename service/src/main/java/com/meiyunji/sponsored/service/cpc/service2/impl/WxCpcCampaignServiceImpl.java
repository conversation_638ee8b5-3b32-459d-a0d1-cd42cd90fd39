package com.meiyunji.sponsored.service.cpc.service2.impl;

import com.amazon.advertising.mode.Adjustment;
import com.amazon.advertising.mode.PredicateEnum;
import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.google.protobuf.Int64Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.adCommon.*;
import com.meiyunji.sponsored.rpc.vo.CampaignNeKeywordsPageRpcVo;
import com.meiyunji.sponsored.rpc.vo.CampaignNeTargetingSpRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.impl.AdChartDataProcess;
import com.meiyunji.sponsored.service.cpc.service2.IWxCpcCampaignService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCommService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.CampaignNeKeywordsPageParam;
import com.meiyunji.sponsored.service.cpc.vo.CampaignNeTargetingSpParam;
import com.meiyunji.sponsored.service.cpc.vo.PlacementPageParam;
import com.meiyunji.sponsored.service.cpc.vo.PlacementPageVo;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.product.po.AsinImage;
import com.meiyunji.sponsored.service.product.service.ISyncAsinImageService;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 微信-广告活动相关业务接口实现
 * <AUTHOR> on 2023/02/06
 */
@Service
@Slf4j
public class WxCpcCampaignServiceImpl implements IWxCpcCampaignService {


    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private AdChartDataProcess adChartDataProcess;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private CpcCommService cpcCommService;
    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdCampaignPlacementReportDao amazonAdCampaignPlacementReportDao;
    @Autowired
    private IAmazonAdCampaignNeKeywordsDao amazonAdCampaignNeKeywordsDao;
    @Autowired
    private ISyncAsinImageService syncAsinImageService;
    @Autowired
    private IAmazonAdCampaignNetargetingSpDao campaignNetargetingSpDao;


    /**
     * 广告位首页数据(分页,拆线图,指标数据)
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public AllPlacementDataResponse.AdPlacementHomeVo getAllPlacementData(int puid, PlacementPageParam param) {

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        // 取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);

        //获取vo
        List<PlacementPageVo> placementVoList = getAllPlacementVoList(puid, param, shopAuth);


        Page<PlacementPageVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());

        //分页数 //要排序
        if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
            placementVoList = PageUtil.sort(placementVoList, param.getOrderField(), param.getOrderType());
        }

        PageUtil.getPage(voPage, placementVoList);
        //总数大于十万
        if (voPage.getTotalSize() > Constants.TOTALSIZELIMIT) {
            int totalPage = Constants.TOTALSIZELIMIT / voPage.getPageSize();
            voPage.setTotalPage(totalPage);
            voPage.setTotalSize(Constants.TOTALSIZELIMIT);
        }


        //处理分页
        AllPlacementDataResponse.AdPlacementHomeVo.Page.Builder pageBuilder = AllPlacementDataResponse.AdPlacementHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        List<PlacementPageVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            List<AllPlacementDataResponse.AdPlacementHomeVo.Page.PlacementPageVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                AllPlacementDataResponse.AdPlacementHomeVo.Page.PlacementPageVo.Builder voBuilder = AllPlacementDataResponse.AdPlacementHomeVo.Page.PlacementPageVo.newBuilder();
                voBuilder.setId(Int64Value.of(item.getId()));
                voBuilder.setCampaignId(item.getCampaignId());
                voBuilder.setType(item.getType());
                if (StringUtils.isNotBlank(item.getPredicate())) {
                    voBuilder.setPredicate(item.getPredicate());
                }
                if (StringUtils.isNotBlank(item.getPercentage())) {
                    voBuilder.setPercentage(item.getPercentage());
                }
                if (StringUtils.isNotBlank(item.getStrategy())) {
                    voBuilder.setStrategy(item.getStrategy());
                }
                if (StringUtils.isNotBlank(item.getCampaignId())) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (StringUtils.isNotBlank(item.getCampaignName())) {
                    voBuilder.setCampaignName(item.getCampaignName());
                }
                if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                    voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                }

                if (StringUtils.isNotBlank(item.getPortfolioId())) {
                    voBuilder.setPortfolioId(item.getPortfolioId());
                }

                if (StringUtils.isNotBlank(item.getPortfolioName())) {
                    voBuilder.setPortfolioName(item.getPortfolioName());
                }

                voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                voBuilder.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
                voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");

                return voBuilder.build();

            }).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }

        return AllPlacementDataResponse.AdPlacementHomeVo.newBuilder()
                .setPage(pageBuilder.build()).build();

    }

    /**
     * 广告位指标数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public AllPlacementAggregateDataResponse.AdPlacementHomeVo getAllPlacementAggregateData(Integer puid, PlacementPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        // 取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);


        boolean isNull = false;

        if (StringUtils.isNotBlank(param.getPortfolioId())) {
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sp.getCampaignType(), param.getState(), param.getServingStatus());
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                param.setCampaignIdList(campaignIds);
            } else {
                isNull = true;
            }
        }

        List<AdHomePerformancedto> list;
        List<AdHomePerformancedto> reportDayList;
        List<AdHomePerformancedto> compareList;
        if (isNull) {
            list = new ArrayList<>();
            compareList = new ArrayList<>();
            reportDayList = new ArrayList<>();
        } else {

            // 增加竞价策略条件过滤广告活动
            if (StringUtils.isNotBlank(param.getStrategyType())) {
                if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
                    List<String> campaignIds = amazonAdCampaignAllDao.getByCampaignIdsAndStrategy(puid, shopAuth.getId(), shopAuth.getMarketplaceId(),
                            param.getCampaignIdList(), CampaignTypeEnum.sp.getCampaignType(), param.getStrategyType(), param.getState(), param.getServingStatus());
                    param.setCampaignIdList(campaignIds);
                }
                if (StringUtils.isNotBlank(param.getCampaignId())) {
                    List<String> campaignId = amazonAdCampaignAllDao.getCampaignIdByStrategy(puid, shopAuth.getId(), shopAuth.getMarketplaceId(),
                            param.getCampaignId(), CampaignTypeEnum.sp.getCampaignType(), param.getStrategyType(), param.getState(), param.getServingStatus());
                    if (campaignId == null || campaignId.size() == 0) {
                        param.setCampaignId("-1");
                    }
                }
            }

            LocalDate startDate = LocalDate.parse(param.getStartDate(), DateTimeFormatter.BASIC_ISO_DATE);
            LocalDate endDate = LocalDate.parse(param.getEndDate(), DateTimeFormatter.BASIC_ISO_DATE);
            Period between = Period.between(startDate, endDate);
            String compareStartDate = startDate.minus(between).minusDays(1L).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
            String compareEndDate = startDate.minusDays(1L).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));

            PlacementPageParam compareParam = new PlacementPageParam();
            BeanUtils.copyProperties(param, compareParam);
            compareParam.setStartDate(compareStartDate);
            compareParam.setEndDate(compareEndDate);


            list = amazonAdCampaignPlacementReportDao.listAllPlacementReportsByDate(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param.getCampaignId(),
                    param.getPredicate(), param);
            compareList = amazonAdCampaignPlacementReportDao.listAllPlacementReportsByDate(puid, compareParam.getShopId(), compareStartDate, compareEndDate, compareParam.getCampaignId(),
                    compareParam.getPredicate(), compareParam);

            List<String> campaignsList = list.stream().map(AdHomePerformancedto::getCampaignId).collect(Collectors.toList());

            //每日汇总数据

            reportDayList = amazonAdCampaignPlacementReportDao.listAllPlacementReportsByCampaignIdList(puid, param.getShopId(), param.getStartDate(),
                    param.getEndDate(), param.getPredicate(), campaignsList);

        }

        //汇总数据
        AdHomeAggregateDataRpcVo placementAggregateDataVo = getPlacementAggregateDataVo(list, shopSalesByDate);
        AdHomeAggregateDataRpcVo compareAggregateDataVo = getPlacementAggregateDataVo(compareList, shopSalesByDate);

        //处理环比数据
        AdHomeAggregateDataRpcVo adHomeAggregateDataRpcVo = getPlacementAggregateDataChainVo(placementAggregateDataVo, compareAggregateDataVo);

        //货币类型
        String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();

        //chart图数据
        List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, shopSalesByDate);

        return AllPlacementAggregateDataResponse.AdPlacementHomeVo.newBuilder()
                .setAggregateDataVo(adHomeAggregateDataRpcVo)
                .addAllDay(dayPerformanceVos)
                .build();

    }

    /**
     * 每个活动固定广告位三条,查询出所有广告位,报告中存在数据则填充数据,否则为0
     *
     * @param puid
     * @param param
     * @param shopAuth
     * @return
     */
    @Override
    public List<PlacementPageVo> getAllPlacementVoList(int puid, PlacementPageParam param, ShopAuth shopAuth) {
        List<PlacementPageVo> placementPageVos = new ArrayList<>();

        if (StringUtils.isNotBlank(param.getPortfolioId())) {
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sp.getCampaignType(), param.getState(), param.getServingStatus());
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                // 查询符合操作状态运行状态的campaignId
                List<AmazonAdCampaignAll> amazonAdCampaignAllList = amazonAdCampaignAllDao.listCampaignIds(shopAuth.getPuid(), param.getShopId(), param.getState(), param.getServingStatus(), campaignIds);
                List<String> campaignIdList = new ArrayList<>();
                amazonAdCampaignAllList.stream().forEach((campaign) -> {
                    campaignIdList.add(campaign.getCampaignId());
                });
                param.setCampaignIdList(campaignIdList);
            } else {
                return placementPageVos;
            }
        }


        //先查询报告数据
        long placementTime = System.currentTimeMillis();
        List<AmazonAdCampaignAllReport> poList;
        poList = amazonAdCampaignPlacementReportDao.listSumPlacementReports(puid, param);

        log.info("==============================wx端-查询广告位数据花费时间 {} ==============================", System.currentTimeMillis() - placementTime);


        //日期报告数据 按活动ID分组
        Map<String, List<AmazonAdCampaignAllReport>> reportsMap = null;
        if (CollectionUtils.isNotEmpty(poList)) {
            reportsMap = poList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AmazonAdCampaignAllReport::getCampaignId));
        }

        //查询所有活动信息  传活动ID,则查具体的活动ID,否则查询所有活动
        List<AmazonAdCampaignAll> campaigns = Lists.newArrayList();
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
                if (param.getCampaignIdList().contains(param.getCampaignId())) {
                    AmazonAdCampaignAll campaign = amazonAdCampaignAllDao.getByCampaignIdAndStrategy(puid, shopAuth.getId(), shopAuth.getMarketplaceId(),
                            param.getCampaignId(), CampaignTypeEnum.sp.getCampaignType(), param.getStrategyType(), param.getState(), param.getServingStatus());
                    campaigns.add(campaign);
                }
            } else {
                AmazonAdCampaignAll campaign = amazonAdCampaignAllDao.getByCampaignIdAndStrategy(puid, shopAuth.getId(), shopAuth.getMarketplaceId(),
                        param.getCampaignId(), CampaignTypeEnum.sp.getCampaignType(), param.getStrategyType(), param.getState(), param.getServingStatus());
                campaigns.add(campaign);
            }
        } else {
            long placementTime2 = System.currentTimeMillis();
            List<AmazonAdCampaignAll> allCampaignsByShop = amazonAdCampaignAllDao.getAllCampaignsByShop(puid, shopAuth.getId(), param.getStrategyType(),
                    param.getCampaignIdList(), CampaignTypeEnum.sp.getCampaignType(), param.getState(), param.getServingStatus());
            log.info("==============================wx端-查询广告位数据花费时间2 {} ==============================", System.currentTimeMillis() - placementTime2);
            campaigns.addAll(allCampaignsByShop);
        }


        List<String> portfolioIds = campaigns.stream().filter(Objects::nonNull).map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());
        Map<String, AmazonAdPortfolio> portfolioMap = null;
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            portfolioMap = portfolioDao.getPortfolioList(puid, param.getShopId(), portfolioIds).stream()
                    .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
        }

        // 取店铺销售额
        ShopSaleDto finalShopSaleDto = new ShopSaleDto();
        BigDecimal shopSales = param.getShopSales();
        finalShopSaleDto.setSumRange(shopSales);

        log.info("==============================wx端-查询广告位数据花费时间3 {} ==============================", System.currentTimeMillis() - placementTime);
        Map<String, List<AmazonAdCampaignAllReport>> finalReportsMap = reportsMap;
        Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;
        campaigns.stream().filter(Objects::nonNull).forEach(item -> {
            Map<String, Adjustment> adjustmentMap = null;
            List<Adjustment> adjustments = JSONUtil.jsonToArray(item.getAdjustments(), Adjustment.class);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(adjustments)) {
                adjustmentMap = adjustments.stream().filter(Objects::nonNull).collect(Collectors.toMap(Adjustment::getPredicate, e -> e));
            }

            //处理位置搜索
            List<String> placements;
            if (StringUtils.isNotBlank(param.getPredicate())) {
                PlacementPageParam.placementPredicateEnum predicateEnum = UCommonUtil.getByCode(param.getPredicate(), PlacementPageParam.placementPredicateEnum.class);
                placements = Lists.newArrayList(predicateEnum.getContent());
            } else {
                //不传广告位置条件,则搜索所有位置的
                placements = Lists.newArrayList(Constants.PLACEMENT_TOP, Constants.PLACEMENT_OTHER, Constants.PLACEMENT_DETAIL_PAGE);
            }

            PlacementPageVo placementPageVo;

            Map<String, AmazonAdCampaignAllReport> placementMap = null;
            //活动对应报告数据
            if (MapUtils.isNotEmpty(finalReportsMap) && finalReportsMap.containsKey(item.getCampaignId())) {
                List<AmazonAdCampaignAllReport> reports = finalReportsMap.get(item.getCampaignId());
                //再按投放位置分组
                if (CollectionUtils.isNotEmpty(reports)) {
                    placementMap = reports.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAllReport::getCampaignType, e -> e));
                }
            }

            for (String placement : placements) {
                placementPageVo = new PlacementPageVo();
                placementPageVo.setType(Constants.SP);
                placementPageVo.setCampaignId(item.getCampaignId());
                placementPageVo.setStrategy(item.getStrategy());
                placementPageVo.setCampaignTargetingType(item.getTargetingType());
                placementPageVo.setCampaignName(item.getName());
                placementPageVo.setId(item.getId());
                placementPageVo.setPortfolioId(item.getPortfolioId());
                if (StringUtils.isNotBlank(item.getPortfolioId())) {
                    if (finalPortfolioMap != null && finalPortfolioMap.containsKey(item.getPortfolioId())) {
                        placementPageVo.setPortfolioName(finalPortfolioMap.get(item.getPortfolioId()).getName());
                    } else {
                        placementPageVo.setPortfolioName("广告组合待同步");
                    }
                } else {
                    placementPageVo.setPortfolioName("-");
                }

                if (Constants.PLACEMENT_TOP.equals(placement)) {
                    placementPageVo.setPredicate(PredicateEnum.PLACEMENTTOP.value());
                } else if (Constants.PLACEMENT_DETAIL_PAGE.equals(placement)) {
                    placementPageVo.setPredicate(PredicateEnum.PLACEMENTPRODUCTPAGE.value());
                } else if (Constants.PLACEMENT_OTHER.equals(placement)) {
                    placementPageVo.setPredicate(PredicateEnum.PLACEMENTRESTOFSEARCH.value());
                }

                if (adjustmentMap != null && adjustmentMap.containsKey(placementPageVo.getPredicate())) {
                    placementPageVo.setPercentage(String.valueOf(adjustmentMap.get(placementPageVo.getPredicate()).getPercentage()));
                }


                if (MapUtils.isNotEmpty(placementMap) && placementMap.containsKey(placement)) {
                    // 填充报告数据
                    cpcCommService.fillReportDataIntoPageVo(placementPageVo, placementMap.get(placement), finalShopSaleDto);
                }

                placementPageVos.add(placementPageVo);
            }

        });

        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {
            cpcCommService.filterPlacementAdVanceData(placementPageVos, param);
        }

        log.info("==============================wx端-查询广告位花费时间 {} ==============================", System.currentTimeMillis() - placementTime);
        return placementPageVos;
    }


    private AdHomeAggregateDataRpcVo getPlacementAggregateDataVo(List<AdHomePerformancedto> rows, BigDecimal shopSales) {

        //为前端渲染页面   集合为0时,也返回对象,不返回null
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(rows)) {
            return AdHomeAggregateDataRpcVo.newBuilder()
                    .setAcos("0")
                    .setAdCost("0")
                    .setAdCostPerClick("0")
                    .setAdOrderNum(Int32Value.of(0))
                    .setCvr("0")
                    .setCtr("0")
                    .setAdSale("0")
                    .setClicks(Int32Value.of(0))
                    .setImpressions(Int32Value.of(0))
                    .build();
        }

        //点击量
        int sumClicks = rows.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNum = rows.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressions = rows.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSale = rows.stream().filter(item -> item != null && item.getAdSale() != null).map(AdHomePerformancedto::getAdSale).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcost = rows.stream().filter(item -> item != null && item.getAdCost() != null).map(AdHomePerformancedto::getAdCost).reduce(BigDecimal.ZERO, BigDecimal::add);

        //ACoS
        BigDecimal sumAcos = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(sumAdSale, 4, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClick = sumClicks == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumClicks), 4, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCvr = sumClicks == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNum).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicks), 4, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtr = sumImpressions == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicks).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressions), 4, BigDecimal.ROUND_HALF_UP);

        return AdHomeAggregateDataRpcVo.newBuilder()
                .setAcos(sumAcos.stripTrailingZeros().toString())
                .setAdCost(sumAdcost.stripTrailingZeros().toString())
                .setAdCostPerClick(sumAdCostPerClick.stripTrailingZeros().toString())
                .setAdOrderNum(Int32Value.of(sumAdOrderNum))
                .setCvr(sumCvr.stripTrailingZeros().toString())
                .setCtr(sumCtr.stripTrailingZeros().toString())
                .setAdSale(sumAdSale.stripTrailingZeros().toString())
                .setClicks(Int32Value.of(sumClicks))
                .setImpressions(Int32Value.of(sumImpressions))
                .build();
    }



    @Override
    public Page<CampaignNeKeywordsPageRpcVo> getNeKeywordsPageList(Integer puid, CampaignNeKeywordsPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth==null) {
            AssertUtil.fail("店铺未授权");
        }

        if (StringUtils.isNotBlank(param.getPortfolioId())) {
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(),CampaignTypeEnum.sp.getCampaignType(), null, null);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(campaignIds)) {
                param.setCampaignIdList(campaignIds);
            } else {
                return new Page<>(param.getPageNo(), param.getPageSize());
            }
        }

        Page<AmazonAdCampaignNeKeywords> page = amazonAdCampaignNeKeywordsDao.pageList(puid, param);
        Page<CampaignNeKeywordsPageRpcVo> voPage = new Page<>();
        BeanUtils.copyProperties(page, voPage);

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(page.getRows())) {
            //查询活动ids
            List<String> spCampaignIds = page.getRows().stream().filter(Objects::nonNull).map(AmazonAdCampaignNeKeywords::getCampaignId).collect(Collectors.toList());

            //sp广告活动
            Map<String, AmazonAdCampaignAll> spCampaignMap = null;
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(spCampaignIds)) {
                List<AmazonAdCampaignAll> spAdCampaigns = amazonAdCampaignAllDao.getByCampaignIds(puid, param.getShopId(), shopAuth.getMarketplaceId(), spCampaignIds,CampaignTypeEnum.sp.getCampaignType());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(spAdCampaigns)) {
                    spCampaignMap = spAdCampaigns.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
            }

            Map<String, AmazonAdPortfolio> portfolioMap = null;
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(spCampaignIds)) {
                List<String> portfolioIds = amazonAdCampaignAllDao.getPortfolioListByCampaignIds(puid, param.getShopId(), spCampaignIds,CampaignTypeEnum.sp.getCampaignType());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(portfolioIds)) {
                    portfolioMap = portfolioDao.getPortfolioList(puid, param.getShopId(), portfolioIds).stream()
                            .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                }
            }


            List<CampaignNeKeywordsPageRpcVo> list = new ArrayList<>(page.getRows().size());
            voPage.setRows(list);

            for (AmazonAdCampaignNeKeywords neKeywords : page.getRows()) {
                CampaignNeKeywordsPageRpcVo.Builder builder = CampaignNeKeywordsPageRpcVo.newBuilder();

                builder.setId(Int64Value.of(neKeywords.getId()));
                builder.setShopId(Int32Value.of(neKeywords.getShopId()));
                builder.setCampaignId(neKeywords.getCampaignId());
                if (StringUtils.isNotBlank(neKeywords.getState())) {
                    builder.setState(neKeywords.getState());
                }
                if (StringUtils.isNotBlank(neKeywords.getMatchType())) {
                    builder.setMatchType(neKeywords.getMatchType());
                }
                if (StringUtils.isNotBlank(neKeywords.getKeywordText())) {
                    builder.setKeywordText(neKeywords.getKeywordText());
                }
                if (neKeywords.getCreateTime()!=null) {
                    builder.setCreateTime(DateUtil.dateToStrWithTime(neKeywords.getCreateTime(), DateUtil.PATTERN_DATE_TIME));
                }
                builder.setType(Constants.SP);
                //广告信息填充
                if (MapUtils.isNotEmpty(spCampaignMap) && spCampaignMap.containsKey(neKeywords.getCampaignId())) {
                    AmazonAdCampaignAll campaign = spCampaignMap.get(neKeywords.getCampaignId());
                    if (StringUtils.isNotBlank(campaign.getName())) {
                        builder.setCampaignName(campaign.getName());
                    }
                    if (StringUtils.isNotBlank(campaign.getTargetingType())) {
                        builder.setCampaignTargetingType(campaign.getTargetingType());
                    }
                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        AmazonAdPortfolio adPortfolio = portfolioDao.getByPortfolioId(puid, campaign.getShopId(), campaign.getPortfolioId());
                        builder.setPortfolioId(campaign.getPortfolioId());
                        if (portfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(campaign.getPortfolioId());
                            builder.setPortfolioName(amazonAdPortfolio.getName());
                            builder.setIsHidden(adPortfolio.getIsHidden());
                        } else {
                            builder.setPortfolioName("广告组合待同步");
                        }
                    } else {
                        builder.setPortfolioName("-");
                    }
                }
                list.add(builder.build());
            }
        }

        return voPage;
    }



    @Override
    public Page<CampaignNeTargetingSpRpcVo> getNeTargetingPageList(CampaignNeTargetingSpParam campaignNeTargetingSpParam) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(campaignNeTargetingSpParam.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺不存在");
        }

        if (StringUtils.isNotBlank(campaignNeTargetingSpParam.getPortfolioId())) {
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(shopAuth.getPuid(), campaignNeTargetingSpParam.getShopId(), campaignNeTargetingSpParam.getPortfolioId(),CampaignTypeEnum.sp.getCampaignType(), null, null);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(campaignIds)) {
                campaignNeTargetingSpParam.setCampaignIdList(campaignIds);
            } else {
                return new Page<>(campaignNeTargetingSpParam.getPageNo(), campaignNeTargetingSpParam.getPageSize());
            }
        }

        Page<AmazonAdCampaignNetargetingSp> page = campaignNetargetingSpDao.pageList(shopAuth.getPuid(), campaignNeTargetingSpParam);

        List<CampaignNeTargetingSpRpcVo> campaignNeTargetingSpVos = new ArrayList<>();
        List<AmazonAdCampaignNetargetingSp> rows = page.getRows();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(rows)) {

            List<String> spCampaignIds = rows.stream().filter(Objects::nonNull).map(AmazonAdCampaignNetargetingSp::getCampaignId).collect(Collectors.toList());

            Map<String, AmazonAdCampaignAll> spCampaignMap = null;
            //sp广告活动
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(spCampaignIds)) {
                List<AmazonAdCampaignAll> spAdCampaigns = amazonAdCampaignAllDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId(), spCampaignIds,CampaignTypeEnum.sp.getCampaignType());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(spAdCampaigns)) {
                    spCampaignMap = spAdCampaigns.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
            }

            Map<String, AmazonAdPortfolio> portfolioMap = null;
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(spCampaignIds)) {
                List<String> portfolioIds = amazonAdCampaignAllDao.getPortfolioListByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), spCampaignIds,CampaignTypeEnum.sp.getCampaignType());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(portfolioIds)) {
                    portfolioMap = portfolioDao.getPortfolioList(shopAuth.getPuid(), shopAuth.getId(), portfolioIds).stream()
                            .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                }
            }
            List<String> asins = rows.stream().filter(e -> StringUtils.isNotBlank(e.getTargetText()) && StringUtils.isBlank(e.getImgUrl())).map(e -> e.getTargetText().toUpperCase()).distinct().collect(Collectors.toList());
            Map<String, AsinImage> asinMap = new HashMap<>();
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(asins)){
                long t4 = Instant.now().toEpochMilli();
                List<AsinImage> listByAsins = syncAsinImageService.getListByAsinsNoSave(shopAuth.getPuid(),shopAuth.getMarketplaceId(),asins);
                log.info("获取图片花费时间 {}", Instant.now().toEpochMilli() - t4);
                asinMap = listByAsins.stream().filter(e->StringUtils.isNotBlank(e.getAsin())).collect(Collectors.toMap(e->e.getAsin().toUpperCase(),e1->e1,(e2,e3)->e3));
            }
            Map<String, AmazonAdCampaignAll> finalSpCampaignMap = spCampaignMap;
            Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;
            Map<String, AsinImage> finalAsinMap = asinMap;
            rows.forEach(e-> {
                if(StringUtils.isNotBlank(e.getTargetText()) && StringUtils.isBlank(e.getImgUrl())){
                    AsinImage asinImage = finalAsinMap.get(e.getTargetText().toUpperCase());
                    if(asinImage != null){
                        e.setImgUrl(asinImage.getImage());
                        e.setTitle(asinImage.getTitle());
                    }
                }
                CampaignNeTargetingSpRpcVo.Builder builder = CampaignNeTargetingSpRpcVo.newBuilder();
                if (MapUtils.isNotEmpty(finalSpCampaignMap) && finalSpCampaignMap.containsKey(e.getCampaignId())) {
                    AmazonAdCampaignAll campaign = finalSpCampaignMap.get(e.getCampaignId());
                    if (StringUtils.isNotBlank(campaign.getName())) {
                        builder.setCampaignName(campaign.getName());
                    }
                    if (StringUtils.isNotBlank(campaign.getTargetingType())) {
                        builder.setCampaignTargetingType(campaign.getTargetingType());
                    }
                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        AmazonAdPortfolio adPortfolio = portfolioDao.getByPortfolioId(campaign.getPuid(), campaign.getShopId(), campaign.getPortfolioId());
                        builder.setPortfolioId(campaign.getPortfolioId());
                        if (finalPortfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = finalPortfolioMap.get(campaign.getPortfolioId());
                            builder.setPortfolioName(amazonAdPortfolio.getName());
                            builder.setIsHidden(adPortfolio.getIsHidden());
                        } else {
                            builder.setPortfolioName("广告组合待同步");
                        }
                    } else {
                        builder.setPortfolioName("-");
                    }
                }

                if (StringUtils.isNotBlank(e.getTargetText())) {
                    builder.setAsin(e.getTargetText());
                }
                if (StringUtils.isNotBlank(e.getCampaignId())) {
                    builder.setCampaignId(e.getCampaignId());
                }
                if (StringUtils.isNotBlank(e.getImgUrl())) {
                    builder.setImgUrl(e.getImgUrl());
                }

                if (StringUtils.isNotBlank(e.getState())) {
                    builder.setState(e.getState());
                }
                if (StringUtils.isNotBlank(e.getTitle())) {
                    builder.setTitle(e.getTitle());
                }
                if (e.getId()!=null) {
                    builder.setId(Int64Value.of(e.getId()));
                }
                if (e.getCreateTime()!=null) {
                    builder.setCreateTime(DateUtil.dateToStrWithFormat(e.getCreateTime(), DateUtil.PATTERN_DATE_TIME));
                }
                builder.setType(Constants.SP);

                campaignNeTargetingSpVos.add(builder.build());
            });
        }
        Page<CampaignNeTargetingSpRpcVo> spVoPage = new Page<>();
        spVoPage.setPageNo(page.getPageNo());
        spVoPage.setPageSize(page.getPageSize());
        spVoPage.setTotalPage(page.getTotalPage());
        spVoPage.setTotalSize(page.getTotalSize());
        spVoPage.setRows(campaignNeTargetingSpVos);
        return spVoPage;
    }


    private AdHomeAggregateDataRpcVo getPlacementAggregateDataChainVo(AdHomeAggregateDataRpcVo aggregateDataVo,AdHomeAggregateDataRpcVo compareAggregateDataVo){
        AdHomeAggregateDataRpcVo.Builder builder = aggregateDataVo.toBuilder();
        if (compareAggregateDataVo == null) {
            return builder
                    .setAcosCompare("0")
                    .setClicksCompare(0)
                    .setCtrCompare("0")
                    .setCvrCompare("0")
                    .setImpressionsCompare(0)
                    .setAdCostCompare("0")
                    .setAdSaleCompare("0")
                    .setAdOrderNumCompare(0)
                    .setAdCostPerClickCompare("0")
                    .setAcosChain("0")
                    .setClicksChain("0")
                    .setCtrChain("0")
                    .setCvrChain("0")
                    .setImpressionsChain("0")
                    .setAdCostChain("0")
                    .setAdSaleChain("0")
                    .setAdOrderNumChain("0")
                    .setAdCostPerClickChain("0")
                    .build();
        }

        return builder
                .setAcosCompare(compareAggregateDataVo.getAcos())
                .setClicksCompare(compareAggregateDataVo.getClicks().getValue())
                .setCtrCompare(compareAggregateDataVo.getCtr())
                .setCvrCompare(compareAggregateDataVo.getCvr())
                .setImpressionsCompare(compareAggregateDataVo.getImpressions().getValue())
                .setAdCostCompare(compareAggregateDataVo.getAdCost())
                .setAdSaleCompare(compareAggregateDataVo.getAdSale())
                .setAdOrderNumCompare(compareAggregateDataVo.getAdOrderNum().getValue())
                .setAdCostPerClickCompare(compareAggregateDataVo.getAdCostPerClick())
                .setAcosChain(calculationChain(new BigDecimal(aggregateDataVo.getAcos()),new BigDecimal(compareAggregateDataVo.getAcos())))
                .setClicksChain(calculationChain(BigDecimal.valueOf(aggregateDataVo.getClicks().getValue()),BigDecimal.valueOf(compareAggregateDataVo.getClicks().getValue())))
                .setCtrChain(calculationChain(new BigDecimal(aggregateDataVo.getCtr()),new BigDecimal(compareAggregateDataVo.getCtr())))
                .setCvrChain(calculationChain(new BigDecimal(aggregateDataVo.getCvr()),new BigDecimal(compareAggregateDataVo.getCvr())))
                .setImpressionsChain(calculationChain(BigDecimal.valueOf(aggregateDataVo.getImpressions().getValue()),BigDecimal.valueOf(compareAggregateDataVo.getImpressions().getValue())))
                .setAdCostChain(calculationChain(new BigDecimal(aggregateDataVo.getAdCost()),new BigDecimal(compareAggregateDataVo.getAdCost())))
                .setAdSaleChain(calculationChain(new BigDecimal(aggregateDataVo.getAdSale()),new BigDecimal(compareAggregateDataVo.getAdSale())))
                .setAdOrderNumChain(calculationChain(BigDecimal.valueOf(aggregateDataVo.getAdOrderNum().getValue()),BigDecimal.valueOf(compareAggregateDataVo.getAdOrderNum().getValue())))
                .setAdCostPerClickChain(calculationChain(new BigDecimal(aggregateDataVo.getAdCostPerClick()),new BigDecimal(compareAggregateDataVo.getAdCostPerClick())))
                .build();
    }

    private String calculationChain(BigDecimal data,BigDecimal dataCompare) {
        if (dataCompare.compareTo(BigDecimal.ZERO) == 0 && data.compareTo(BigDecimal.ZERO) > 0) {
            return "100.0000";
        }
        return MathUtil.divideByZero(MathUtil.subtract(data, dataCompare).multiply(BigDecimal.valueOf(100)), dataCompare).toString();
    }

}