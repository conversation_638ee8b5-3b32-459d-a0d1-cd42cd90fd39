package com.meiyunji.sponsored.service.account.dao;

import com.meiyunji.amazon.sellerpartner.base.RegionEnum;
import com.meiyunji.sponsored.common.springjdbc.IBaseDao;
import com.meiyunji.sponsored.common.springjdbc.ISlaveBaseDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.ShopByPuidDto;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * ShopAuth
 *
 * <AUTHOR>
 */
public interface ISlaveShopAuthDao extends ISlaveBaseDao<ShopAuth> {

    List<String> getAllMarketplaceIdByPuid(int puid);

    List<Integer> getShopByMid(int puid, String marketplaceId);

    List<ShopAuth> getListByPuid(int puid);

    List<ShopAuth> getAllId();

    List<Integer> getAllShopId();

    List<Integer> getAllShopId(int puid);

    List<Integer> getAllAdShopId(int puid);

    List<String> getAllSellerId(Integer puid);

    List<ShopAuth> listBySellerId(String sellerId);

    List<Integer> getIdByPuidAndShop(Integer puid, Integer shopId);

    Integer getCountByShopIds(Integer puid, List<Integer> shopIds);

    /**
     * 根据 sellerPartnerId 和 marketplaceId 找到店铺
     * 理论上：同一个sellerPartnerId ，在同一个marketplaceId 下只会有一个店铺
     *
     * @param puid
     * @param sellerId
     * @param marketplaceId
     * @return
     */
    ShopAuth getBySellerIdAndMarketplaceId(Integer puid, String sellerId, String marketplaceId);

    /**
     * 批量获取店铺信息（有效的店铺）
     *
     * @param puid
     * @param shopIds
     * @return
     */
    List<ShopAuth> listValidByIds(Integer puid, List<Integer> shopIds);

    /**
     * 批量获取店铺信息
     *
     * @param puid
     * @param shopIds
     * @return
     */
    List<ShopAuth> listAllByIds(Integer puid, List<Integer> shopIds);

    /**
     * 获取所有有效的店铺
     *
     * @return
     */
    List<ShopAuth> listAllValidShop();

    /**
     * 获取所有有效的广告店铺
     */
    List<ShopAuth> listAllValidAdShop(Integer puid);

    ShopAuth getValidAdShopById(Integer puid, Integer shopId);

    /**
     * 根据sellerid, region取店铺信息
     *
     * @param puid             puid
     * @param sellingPartnerId amazon seller id
     * @param region           区域
     * @return List
     */
    List<ShopAuth> listByPuidSellerIdRegion(int puid, String sellingPartnerId, String region);


    List<ShopAuth> listByPuidSellerIdRegion(int puid, String sellingPartnerId, String region, List<Integer> shopIds);

    /**
     * 获取店铺信息
     *
     * @param puid
     * @param sellerId
     * @param marketplaceId
     * @return
     */
    ShopAuth getByMarketplaceId(int puid, String sellerId, String marketplaceId);

    /**
     * 分页取授权了广告的店铺ID
     *
     * @param puid   puid
     * @param shopId shopId
     * @param start  start
     * @param limit  limit
     * @return List
     */
    List<Integer> getAllValidAdShopIdByLimit(Integer puid, Integer shopId, int start, int limit);

    /**
     * 分页取有效的店铺
     *
     * @param puid   puid
     * @param shopId shopId
     * @param start  start
     * @param limit  limit
     * @return List
     */
    List<ShopAuth> getAllValidShopByLimit(Integer puid, Integer shopId, int start, int limit);

    /**
     * 根据时间分页取授权了广告的店铺(间隔3个小时)
     *
     * @param puid   puid
     * @param shopId shopId
     * @return List
     */
    List<ShopAuth> getAllValidAdShopByLimitDate(Integer puid, Integer shopId);

    /**
     * 分页取授权了广告的店铺
     *
     * @param puid   puid
     * @param shopId shopId
     * @param start  start
     * @param limit  limit
     * @return List
     */
    List<ShopAuth> getAllValidAdShopByLimit(Integer puid, Integer shopId, int start, int limit);

    /**
     * 分页取授权了广告的店铺 按id倒序
     */
    List<ShopAuth> getAllValidAdShopByLimitOrderByIdDesc(Integer puid, Integer shopId, int start, int limit);

    /**
     * 按照puid获取有广告授权或者过期的店铺数据
     * @param puid
     * @return
     */
    List<ShopByPuidDto> getAllValidAdShopByPuid(Integer puid);

    /**
     * 分页获取有广告授权或者过期的puid
     * @param start
     * @param limit
     * @return
     */
    List<Integer> getAllValidAdShopPuidByLimit(int start, int limit);

    List<ShopAuth> getAllValidAdShopByLimitOrderByAdAuthTime(Integer puid, Integer shopId, int start, int limit);

    /**
     * 分页取授权店铺用户PUID
     *
     * @param start start
     * @param limit limit
     * @return List
     */
    List<Integer> getAllPuidByLimit(int start, int limit);

    /**
     * 取店铺partner
     *
     * @param puid puid
     * @return List
     */
    List<ShopAuth> getAllPartner(Integer puid);

    /**
     * 根据用户id跟sellerid marketplace获取
     *
     * @param puid
     * @param sellId
     * @return
     */
    ShopAuth getByPuidAndSellerId(Integer puid, String sellId, String marketplaceId);

    List<ShopAuth> getByPuidAndSellerId(Integer puid, String sellerId);

    List<ShopAuth> getByPuidAndSellerIdAvailable(Integer puid, String sellerId);


    List<ShopAuth> listLimit(String marketplace, int maximumPoolSize);

    /**
     * 获得该区域下的所有的店铺的id
     *
     * @param puid
     * @param sellingPartnerId
     * @param region
     */
    List<ShopAuth> getBySellerIdAndRegion(Integer puid, String sellingPartnerId, String region);

    /**
     * 获取店铺的sellerId
     *
     * @param shopIdList
     * @return
     */
    List<String> getSellerIdByShopIds(List<Integer> shopIdList);

    /**
     * 根据大区取店铺数
     *
     * @param puid             puid
     * @param sellingPartnerId seller id
     * @param region           region
     * @return int
     */
    int countByPuidSellerIdRegion(int puid, String sellingPartnerId, String region);

    /**
     * 获取sellerApi正常的seller
     *
     * @return
     */
    List<String> getAuthNormalSellers();

    /**
     * 获取MWS正常,sellerApi不正常
     *
     * @return
     */
    List<String> getMWSSellers();

    /**
     * 通过店铺名称获取店铺信息
     *
     * @param puid
     * @param shopName
     * @return
     */
    ShopAuth getByName(Integer puid, String shopName);

    /**
     * @param puid puid
     * @return List
     */
    List<String> getSellerIdByPuid(Integer puid);

    /**
     * 获取所有店铺信息
     *
     * @param puid
     * @param sellerId
     * @return
     */
    List<ShopAuth> listByPuidAndSellerId(Integer puid, String sellerId);

    /**
     * 获取指定站点的店铺
     *
     * @param puid
     * @param mkList
     * @param shopList
     * @return
     */
    List<ShopAuth> listByPuidAndMarketplace(int puid, List<String> mkList, List<Integer> shopList);

    /**
     * 获取token
     * <p>
     * 根据大区分组
     * <p>
     * 获取大于timeSecond时长/秒 的数据
     *
     * @return
     */
    List<ShopAuth> getSellingToken(Integer puid, Integer shopId, long timeSecond);

    List<ShopAuth> getShopAuthGroupBySellerId(Integer puid, int syncType, int status);

    /**
     * 取用户下的所有店铺
     *
     * @param puids
     * @return
     */
    List<ShopAuth> listByPuids(List<Integer> puids);


    /**
     * 获取店铺信息
     *
     * @param puid
     * @param sellerId
     * @param marketplaceId
     * @return
     */
    List<ShopAuth> getByMarketplaceIds(int puid, String sellerId, String marketplaceId);

    /**
     * 获取店铺信息
     *
     * @param puid
     * @param marketplaceIds
     * @return
     */
    List<ShopAuth> getByMarketplaceIds(int puid, String[] marketplaceIds);

    /**
     * 随机获取已授权对应marketplaceId的店铺
     * @param marketplaceId
     */
    List<ShopAuth> getShopAuthByMarketplaceId(String marketplaceId);

    List<ShopAuth> getShopAuthByAuthAndTokenIsNull();

    /**
     * 根据店铺获取所有站点
     * @param shopIdList
     * @return
     */
    List<String> marketplaceIdListByShopIds(List<Integer> shopIdList);

    List<Integer> IdListByShopIds(List<Integer> shopIdList);

    /**
     * 根据店铺id获取出已授权的店铺
     * @param puid
     * @param shopIdList
     * @return
     */
    List<ShopAuth> getAuthShopByShopIdList(int puid, List<Integer> shopIdList);

    List<ShopAuth> getBySellerIdsAndMarketplaceIds(List<String> sellerIds, List<String> marketplaceIds);

    List<ShopAuth> getAdAuthShopByShopIdList(List<Integer> puids);

    ShopAuth getByShopAuth(String sellerId, String marketplaceId);

}