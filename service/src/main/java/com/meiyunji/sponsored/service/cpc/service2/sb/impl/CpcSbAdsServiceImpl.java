package com.meiyunji.sponsored.service.cpc.service2.sb.impl;

import com.alibaba.fastjson.JSONObject;
import com.amazon.advertising.assets.entity.assetsGet.AssetVersionList;
import com.amazon.advertising.assets.entity.assetsGet.GetAssetResult;
import com.amazon.advertising.sb.entity.ads.AdSuccessResponseItem;
import com.amazon.advertising.sb.entity.ads.AdsUpdate;
import com.amazon.advertising.sb.entity.ads.productCollection.SbAdsCreateResult;
import com.amazon.advertising.sb.entity.pageAisn.PageAsinsResult;
import com.amazon.advertising.sb.mode.ads.AdCreative;
import com.amazon.advertising.sb.mode.ads.AdLandingPage;
import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.google.protobuf.Int64Value;
import com.mchange.v1.db.sql.ResultSetUtils;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.adCommon.*;
import com.meiyunji.sponsored.rpc.sb.ads.*;
import com.meiyunji.sponsored.rpc.sb.campaign.*;
import com.meiyunji.sponsored.rpc.vo.AdTagVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.impl.AdChartDataProcess;
import com.meiyunji.sponsored.service.cpc.service2.assets.CpcAssetsApiService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbAdsService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCommService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.SbAdsCreativeInfoVo;
import com.meiyunji.sponsored.service.cpc.vo.SbLandingPageVo;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.reportHour.vo.AdCommonHourVo;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.common.inject.internal.Stopwatch;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.cpc.util.Constants.EXPORT_MAX_SIZE;

/**
 * Created by lm on 2021/8/3.
 */
@Service
@Slf4j
public class CpcSbAdsServiceImpl implements ICpcSbAdsService {
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;

    @Autowired
    private IAmazonSbAdGroupDao groupDao;

    @Autowired
    private CpcCommService cpcCommService;
    @Autowired
    private IAmazonSbAdsDao amazonSbAdsDao;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private IAmazonAdSbAdsReportDao amazonAdSbAdsReportDao;
    @Autowired
    private AdChartDataProcess adChartDataProcess;
    @Autowired
    private CpcSbAdsApiService cpcSbAdsApiService;
    @Autowired
    private CpcSbPageAsinsApiService pageAsinsApiService;
    @Autowired
    private IAdManageOperationLogService operationAdGroupLogService;

    @Autowired
    private IAmazonAdCampaignAllDao amazonSbAdCampaignDao;

    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;

    @Autowired
    private CpcAssetsApiService cpcAssetsApiService;


    @Override
    public Result create(SbAdVo vo) {
        Integer puid = vo.getPuid();
        Integer shopId = vo.getShopId();
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        Result sbAdresult = cpcSbAdsApiService.createSbAd(shop, profile, vo);

        if (!sbAdresult.success()) {
            return ResultUtil.returnErr(sbAdresult.getMsg());
        }
        return ResultUtil.success(sbAdresult.getData());
    }

    @Override
    public Result update(Integer puid, Integer shopId, Integer uid, String adId, String state, String name, String ip) {
        if (StringUtils.isBlank(adId)) {
            return ResultUtil.error("请求参数错误");
        }
        AmazonSbAds amazonSbAds = amazonSbAdsDao.getAdsByAdId(puid, shopId, adId);
        if (amazonSbAds == null) {
            return ResultUtil.error("没有广告创意数据");
        }

        String amazonSbAdsAdId = amazonSbAds.getAdId();
        if (StringUtils.isBlank(amazonSbAdsAdId)) {
            return ResultUtil.error("该广告创意不可修改");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        //sb ad归档只能调用单独删除接口
        if (StateEnum.archived.getStateType().equalsIgnoreCase(state)) {
            Result archiveRes = cpcSbAdsApiService.archive(shop, profile, Lists.newArrayList(adId));
            logSbAdsStateUpdate(amazonSbAds, state, name, uid, ip, archiveRes);
            return archiveRes;
        }


        List<AdsUpdate> updateList = new ArrayList<>(1);
        AdsUpdate update = new AdsUpdate();
        update.setAdId(amazonSbAdsAdId);
        if (StringUtils.isNotBlank(name)) {
            update.setName(name);
        }
        if (StringUtils.isNotBlank(state)) {
            update.setState(state.toUpperCase());
        }
        updateList.add(update);
        Result updateRes = cpcSbAdsApiService.update(shop, profile, updateList);
        logSbAdsStateUpdate(amazonSbAds, state, name, uid, ip, updateRes);
        return updateRes;

    }

    private void logSbAdsStateUpdate(AmazonSbAds oldSbAds, String state, String name, Integer uid, String ip, Result result) {
        try {
            AmazonSbAds sbAds = new AmazonSbAds();
            BeanUtils.copyProperties(oldSbAds, sbAds);
            if (StringUtils.isNotBlank(state)) {
                sbAds.setState(state);
            }
            if (StringUtils.isNotBlank(name)) {
                sbAds.setName(name);
            }
            sbAds.setUpdateId(uid);

            AdManageOperationLog sbAdsLog = operationAdGroupLogService.getSbAdsLog(oldSbAds, sbAds);
            sbAdsLog.setIp(ip);
            if (result.success()) {
                sbAdsLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                sbAdsLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                sbAdsLog.setResultInfo(result.getMsg());
            }
            operationAdGroupLogService.printAdOperationLog(Lists.newArrayList(sbAdsLog));
        } catch (Exception e) {
            log.error("sb 广告创意状态日志异常", e);
        }
    }

    @Override
    public Result updateCreative(SbAdCreativeVo vo) {
        Integer puid = vo.getPuid();
        Integer shopId = vo.getShopId();
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        Result sbAdresult = cpcSbAdsApiService.updateCreative(shop, profile, vo);

        if (!sbAdresult.success()) {
            ResultUtil.returnErr(sbAdresult.getMsg());
        }
        SbAdsCreateResult adResult = (SbAdsCreateResult) sbAdresult.getData();
        AdSuccessResponseItem successSbAdResult = adResult.getAd().getSuccess().get(0);
        String adId = successSbAdResult.getAdId();
        String errMsg = null;
        // 入库
        try {
            AmazonSbAds sbAds = amazonSbAdsDao.getAdsByAdId(puid, shopId, adId);
            AdCreative adCreative = new AdCreative();
            adCreative.setAsins(vo.getAsins());
            adCreative.setBrandName(vo.getBrandName());
            adCreative.setBrandLogoAssetID(vo.getBrandLogoAssetId());
            adCreative.setHeadline(vo.getHeadline());
            if (StringUtils.isNotBlank(vo.getCustomImageAssetId())) {
                adCreative.setCustomImageAssetId(vo.getCustomImageAssetId());
            }
            if (StringUtils.isNotBlank(vo.getBrandLogoUrl())) {
                sbAds.setBrandLogoUrl(vo.getBrandLogoUrl());
            }
            if (StringUtils.isNotBlank(vo.getCustomImageUrl())) {
                sbAds.setCustomImageUrl(vo.getCustomImageUrl());
            }
            sbAds.setCreative(JSONUtil.objectToJson(adCreative));
            sbAds.setCreateId(vo.getUid());
            amazonSbAdsDao.updateByIdAndPuid(puid, sbAds);
            cpcSbAdsApiService.saveDoris(null, Collections.singletonList(sbAds));
        } catch (Exception e) {
            log.error("update Sb Ad save fail:", e);
        }
        return ResultUtil.success(adId);
    }


    @Override
    public Result updateBatch(Integer puid, Integer shopId, Integer uid, List<SbUpdateAdsVo> vos, String ip) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(vos)) {
            return ResultUtil.error("请求参数错误");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }


        List<String> adIds = vos.stream().map(SbUpdateAdsVo::getAdId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adIds)) {
            return ResultUtil.returnErr("无可调整对象");
        }
        List<AmazonSbAds> amazonSbAds = amazonSbAdsDao.listByAdId(puid, shopId, adIds);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(amazonSbAds)) {
            return ResultUtil.error("未找到广告创意");
        }
        List<AmazonSbAds> updateList = Lists.newArrayList();
        List<AmazonSbAds> deleteList = Lists.newArrayList();
        List<SbUpdateAdsVo> errorList = Lists.newArrayList();
        Map<String, AmazonSbAds> amazonSbAdsMap = amazonSbAds.stream().collect(Collectors.toMap(AmazonSbAds::getAdId, e -> e));
        for (SbUpdateAdsVo vo : vos) {
            AmazonSbAds oldAmazonSbAds = amazonSbAdsMap.get(vo.getAdId());
            if (oldAmazonSbAds == null) {
                vo.setFailReason("对象不存在");
                errorList.add(vo);
                continue;
            }
            if (StringUtils.isBlank(oldAmazonSbAds.getAdId())) {
                vo.setFailReason("对象不可编辑");
                errorList.add(vo);
                continue;
            }
            AmazonSbAds updateAmazonSbAds = new AmazonSbAds();
            BeanUtils.copyProperties(oldAmazonSbAds, updateAmazonSbAds);
            updateAmazonSbAds.setState(vo.getState().toUpperCase());
            updateAmazonSbAds.setUpdateId(uid);
            if (StateEnum.archived.getStateType().equalsIgnoreCase(vo.getState())) {
                deleteList.add(updateAmazonSbAds);
            } else {
                updateList.add(updateAmazonSbAds);
            }

        }
        if (CollectionUtils.isEmpty(updateList) && CollectionUtils.isEmpty(deleteList)) {
            BatchResponseVo<SbUpdateAdsVo, AmazonSbAds> data = new BatchResponseVo<>();
            data.setErrorList(errorList);
            data.setSuccessNum(0);
            data.setCountNum(vos.size());
            data.setFailNum(errorList.size());
            return ResultUtil.success(data);
        }
        Result<BatchResponseVo<SbUpdateAdsVo, AmazonSbAds>> deleteResult = null;
        if (CollectionUtils.isNotEmpty(deleteList)) {
            List<String> collect = deleteList.stream().map(AmazonSbAds::getAdId).collect(Collectors.toList());
            deleteResult = cpcSbAdsApiService.deleteBatch(shop, profile, deleteList, collect);
        }
        Result<BatchResponseVo<SbUpdateAdsVo, AmazonSbAds>> updateResult = null;
        if (CollectionUtils.isNotEmpty(updateList)) {
            updateResult = cpcSbAdsApiService.update(shop, profile, updateList, Constants.CPC_BATCH_UPDATE_STATUS);
        }
        Result<BatchResponseVo<SbUpdateAdsVo, AmazonSbAds>> result = null;
        if (updateResult != null) {
            result = updateResult;
        }
        if (deleteResult != null) {
            if (result == null) {
                result = deleteResult;
            } else {
                if (result.getData() != null && deleteResult.getData() != null) {
                    result.getData().addSuccessNum(deleteResult.getData().getSuccessNum());
                    result.getData().addFailNum(deleteResult.getData().getFailNum());
                    result.getData().addErrorList(deleteResult.getData().getErrorList());
                    result.getData().addSuccessList(deleteResult.getData().getSuccessList());
                    result.getData().addSuccessNum(deleteResult.getData().getCountNum());
                } else if (result.getData() == null && deleteResult.getData() != null) {
                    result.setData(deleteResult.getData());
                }
            }
        }

        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayList();
        updateList.addAll(deleteList);
        for (AmazonSbAds sbAds : updateList) {
            AmazonSbAds oldSbAds = amazonSbAdsMap.get(sbAds.getAdId());
            AdManageOperationLog sbAdsLog = operationAdGroupLogService.getSbAdsLog(oldSbAds, sbAds);
            sbAdsLog.setIp(ip);
            adManageOperationLogs.add(sbAdsLog);
        }

        if (result.success()) {
            BatchResponseVo<SbUpdateAdsVo, AmazonSbAds> data = result.getData();
            List<SbUpdateAdsVo> keywordsError = data.getErrorList();
            if (CollectionUtils.isNotEmpty(errorList)) {
                keywordsError.addAll(errorList);
                data.setFailNum(data.getErrorList().size());
            }
            List<AmazonSbAds> amazonAdKeywordsSuccess = data.getSuccessList();

            Map<String, AmazonSbAds> successMap = amazonAdKeywordsSuccess.stream().collect(Collectors.toMap(AmazonSbAds::getAdId, e -> e));
            Map<String, SbUpdateAdsVo> errorMap = keywordsError.stream().collect(Collectors.toMap(SbUpdateAdsVo::getAdId, e -> e, (e1, e2) -> e1));
            for (AdManageOperationLog adManageOperationLog : adManageOperationLogs) {
                if (!StringUtil.isEmptyObject(successMap.get(adManageOperationLog.getTargetId()))) {
                    adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                }
                if (!StringUtil.isEmptyObject(errorMap.get(adManageOperationLog.getTargetId()))) {
                    adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    adManageOperationLog.setResultInfo(errorMap.get(adManageOperationLog.getTargetId()).getFailReason());
                }
            }

            if (CollectionUtils.isNotEmpty(amazonAdKeywordsSuccess)) {
                log.info("用户批量更新成功，typ:{},updateId:{},puid :{},shopid:{},更新成功数据：{}", Constants.CPC_BATCH_UPDATE_STATUS, uid, puid, shopId, JSONUtil.objectToJson(amazonAdKeywordsSuccess));
                data.getSuccessList().clear();
            }
        } else {
            for (AdManageOperationLog adManageOperationLog : adManageOperationLogs) {
                adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                adManageOperationLog.setResultInfo(result.getMsg());
            }
        }
        operationAdGroupLogService.batchLogsMergeByAdGroup(adManageOperationLogs);
        return result;
    }


    private List<Object> buildUpLogMessage(Map<String, AmazonSbAds> oldList, List<AmazonSbAds> newList, String type) {
        List<Object> dataList = new ArrayList<>();
        StringBuilder builder = new StringBuilder();
        if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            dataList.add("SB ADS 批量修改状态");
        }
        newList.forEach(e -> {
            AmazonSbAds old = oldList.get(e.getAdId());

            if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {

                builder.append("adId:").append(e.getAdId());
                if (old != null) {
                    builder.append(",旧值:").append(old.getState());
                }
                builder.append(",新值:").append(e.getState());
            }
            dataList.add(builder.toString());
            builder.delete(0, builder.length());
        });
        return dataList;
    }


    /**
     * 广告创意(sb)
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public AllAdsDataResponse.AdCreateHomeVo getAllAdsData(int puid, AdAdsPageParam param) {
        Page<AdAdsPageVo> voPage = new Page<>();
        voPage.setPageSize(param.getPageSize());
        voPage.setPageNo(param.getPageNo());
        //查询所有数据(sb sd)

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        // 取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);

        if (org.apache.commons.lang3.StringUtils.isBlank(param.getType())) {
            param.setType("sb");
        }

        //获取不同类型数据 sp、sd
        getSbAdsVoList(shopAuth, puid, param, voPage, false);


        //分页后,填充活动,广告组,asin信息
        fillinAdInfoForVo(shopAuth, voPage.getRows(), false);

        //处理分页
        AllAdsDataResponse.AdCreateHomeVo.Page.Builder pageBuilder = AllAdsDataResponse.AdCreateHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        List<AdAdsPageVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            Map<String, AdAdsPageVo> adAdsPageVoMap = null;
            if (param.getIsCompare()) {
                //对比时无须高级搜索条件
                param.setUseAdvanced(false);
                ShopSaleDto shopSaleDtoCompare = cpcShopDataService.getShopSaleData(param.getShopId(), param.getCompareStartDate(), param.getCompareEndDate());
                BigDecimal shopSaleDataCompare;
                if (shopSaleDtoCompare == null || shopSaleDtoCompare.getSumRange() == null) {  //店铺销售额
                    shopSaleDataCompare = BigDecimal.ZERO;
                } else {
                    shopSaleDataCompare = shopSaleDtoCompare.getSumRange();
                }
                param.setShopSales(shopSaleDataCompare);
                param.setStartDate(param.getCompareStartDate());
                param.setEndDate(param.getCompareEndDate());

                List<AdAdsPageVo> adsPageVoList = getAdsPageVoList(puid, param);
                adAdsPageVoMap = adsPageVoList.stream().collect(Collectors.toMap(AdAdsPageVo::getQueryId, Function.identity(), (a, b) -> a));

            }


            Map<String, AdAdsPageVo> finalAdAdsPageVoMap = adAdsPageVoMap;
            List<AllAdsDataResponse.AdCreateHomeVo.Page.AdCreatePageVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                AllAdsDataResponse.AdCreateHomeVo.Page.AdCreatePageVo.Builder voBuilder = AllAdsDataResponse.AdCreateHomeVo.Page.AdCreatePageVo.newBuilder();
                voBuilder.setType(item.getType());
                voBuilder.setId(Int64Value.of(item.getId()));
                voBuilder.setShopId(Int32Value.of(item.getShopId()));
                if (CollectionUtils.isNotEmpty(item.getAdTags())) {
                    item.getAdTags().forEach(e -> {
                        com.meiyunji.sponsored.rpc.vo.AdTagVo.Builder builder = com.meiyunji.sponsored.rpc.vo.AdTagVo.newBuilder();
                        AdTagVo tagVo = builder.setId(Int64Value.of(e.getId())).setColor(e.getColor()).setName(e.getName()).build();
                        voBuilder.addAdTags(tagVo);
                    });
                }


                if (item.getDxmAdGroupId() != null) {
                    voBuilder.setDxmAdGroupId(Int64Value.of(item.getDxmAdGroupId()));
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getCampaignId())) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getCampaignName())) {
                    voBuilder.setCampaignName(item.getCampaignName());
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                    voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getAdGroupId())) {
                    voBuilder.setAdGroupId(item.getAdGroupId());
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getAdGroupType())) {
                    voBuilder.setAdGroupType(item.getAdGroupType());
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getAdGroupName())) {
                    voBuilder.setAdGroupName(item.getAdGroupName());
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getAdId())) {
                    voBuilder.setAdId(item.getAdId());
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getState())) {
                    voBuilder.setState(item.getState());
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getAsin())) {
                    voBuilder.setAsin(item.getAsin());
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getSku())) {
                    voBuilder.setSku(item.getSku());
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getTitle())) {
                    voBuilder.setTitle(item.getTitle());
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getImgUrl())) {
                    if (item.getImgUrl().endsWith("S60_.jpg")) {
                        item.setImgUrl(item.getImgUrl().replace("S60_.jpg", "S600_.jpg"));
                    }
                    voBuilder.setImgUrl(item.getImgUrl());
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getPrice())) {
                    voBuilder.setPrice(item.getPrice());
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getDomain())) {
                    voBuilder.setDomain(item.getDomain());
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getPortfolioId())) {
                    voBuilder.setPortfolioId(item.getPortfolioId());
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getPortfolioName())) {
                    voBuilder.setPortfolioName(item.getPortfolioName());
                }
                if (item.getIsHidden() != null) {
                    voBuilder.setIsHidden(item.getIsHidden());
                }

                if (item.getServingStatus() != null) {
                    voBuilder.setServingStatus(item.getServingStatus());
                }
                if (item.getServingStatusDec() != null) {
                    voBuilder.setServingStatusDec(item.getServingStatusDec());
                }
                if (item.getServingStatusName() != null) {
                    voBuilder.setServingStatusName(item.getServingStatusName());
                }

                if (item.getQueryId() != null) {
                    voBuilder.setQueryId(item.getQueryId());
                }
                if (item.getName() != null) {
                    voBuilder.setName(item.getName());
                }
                // 创意品牌logo以及自定义图片回显地址
                if (StringUtils.isNotBlank(item.getBrandLogoUrl())) {
                    voBuilder.setBrandLogoUrl(item.getBrandLogoUrl());
                }
                if (StringUtils.isNotBlank(item.getCustomImageUrl())) {
                    voBuilder.setCustomImageUrl(item.getCustomImageUrl());
                }
                voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                voBuilder.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
                voBuilder.setAdCostPerClick(org.apache.commons.lang3.StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setCtr(org.apache.commons.lang3.StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(org.apache.commons.lang3.StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(org.apache.commons.lang3.StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setRoas(org.apache.commons.lang3.StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                voBuilder.setAdCost(org.apache.commons.lang3.StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAcots(org.apache.commons.lang3.StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                voBuilder.setAsots(org.apache.commons.lang3.StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                voBuilder.setAdSale(org.apache.commons.lang3.StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
                /**
                 * TODO 广告报告重构
                 * sd广告vcpm类型报告特殊字段。
                 */
                //可见展示次数(VCPM专用)
                voBuilder.setViewImpressions(Int32Value.of(Optional.ofNullable(item.getViewImpressions()).orElse(0)));
                //每笔订单花费
                voBuilder.setCpa(org.apache.commons.lang3.StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                //vcpm
                voBuilder.setVcpm(org.apache.commons.lang3.StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0");
                //本广告产品订单量
                voBuilder.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
                //其他产品广告订单量
                voBuilder.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
                //本广告产品销售额
                voBuilder.setAdSales(org.apache.commons.lang3.StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                //其他产品广告销售额
                voBuilder.setAdOtherSales(org.apache.commons.lang3.StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                //广告销量
                voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
                //本广告产品销量
                voBuilder.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
                //其他产品广告销量
                voBuilder.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));
                //“品牌新买家”订单量
                voBuilder.setOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0)));
                //“品牌新买家”订单百分比
                voBuilder.setOrderRateNewToBrandFTD(org.apache.commons.lang3.StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
                //“品牌新买家”销售额
                voBuilder.setSalesNewToBrandFTD(org.apache.commons.lang3.StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
                //“品牌新买家”销售额百分比
                voBuilder.setSalesRateNewToBrandFTD(org.apache.commons.lang3.StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
                //“品牌新买家”销量
                voBuilder.setUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getUnitsOrderedNewToBrandFTD()).orElse(0)));
                //“品牌新买家”销量百分比
                voBuilder.setUnitsOrderedRateNewToBrandFTD(org.apache.commons.lang3.StringUtils.isNotBlank(item.getUnitsOrderedRateNewToBrandFTD()) ? item.getUnitsOrderedRateNewToBrandFTD() : "0");
                // 花费占比
                voBuilder.setAdCostPercentage(org.apache.commons.lang3.StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                // 销售额占比
                voBuilder.setAdSalePercentage(org.apache.commons.lang3.StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                // 订单量占比
                voBuilder.setAdOrderNumPercentage(org.apache.commons.lang3.StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                // 销量占比
                voBuilder.setOrderNumPercentage(org.apache.commons.lang3.StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");
                //“品牌新买家”订单转化率
                voBuilder.setOrdersNewToBrandPercentageFTD(org.apache.commons.lang3.StringUtils.isNotBlank(item.getOrdersNewToBrandPercentageFTD()) ? item.getOrdersNewToBrandPercentageFTD() : "0");

                //环比数据填充
                if (MapUtils.isNotEmpty(finalAdAdsPageVoMap)) {
                    if (finalAdAdsPageVoMap.containsKey(item.getQueryId())) {
                        AdAdsPageVo compareItem = finalAdAdsPageVoMap.get(item.getQueryId());
                        voBuilder.setCompareImpressions(Int32Value.of(Optional.ofNullable(compareItem.getImpressions()).orElse(0)));
                        //曝光环比值
                        int impressionDiff = voBuilder.getImpressions().getValue() - voBuilder.getCompareImpressions().getValue();
                        voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions().getValue() == 0 ? "-" :
                                new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareViewImpressions(Int32Value.of(Optional.ofNullable(compareItem.getViewImpressions()).orElse(0)));
                        ;
                        //可见展示环比值
                        int viewImpressionDiff = voBuilder.getViewImpressions().getValue() - voBuilder.getCompareViewImpressions().getValue();
                        voBuilder.setCompareViewImpressionsRate(voBuilder.getCompareViewImpressions().getValue() == 0 ? "-" :
                                new BigDecimal(viewImpressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareViewImpressions().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareClicks(Int32Value.of(Optional.ofNullable(compareItem.getClicks()).orElse(0)));
                        //点击量环比值
                        int clicksDiff = voBuilder.getClicks().getValue() - voBuilder.getCompareClicks().getValue();
                        voBuilder.setCompareClicksRate(voBuilder.getCompareClicks().getValue() == 0 ? "-" :
                                new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCtr(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
                        //ctr环比值
                        BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
                        voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCvr(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
                        //cvr环比值
                        BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
                        voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcos(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
                        //Acos环比值
                        BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
                        voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcots(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
                        //Acots环比值
                        BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
                        voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAsots(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
                        //Asots环比值
                        BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
                        voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0)));
                        //AdOrderNum环比值
                        int adOrderNumDiff = voBuilder.getAdOrderNum().getValue() - voBuilder.getCompareAdOrderNum().getValue();
                        voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCost(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
                        //AdCost环比值
                        BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
                        voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPerClick(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
                        //AdCostPerClick环比值
                        BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
                        voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSale(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
                        //AdSale环比值
                        BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
                        voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareRoas(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
                        //Roas环比值
                        BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
                        voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCpa(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                        //Cpa环比值
                        BigDecimal CpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                        voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                CpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareVcpm(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getVcpm()) ? compareItem.getVcpm() : "0");
                        //Vcpm环比值
                        BigDecimal vcpmDiff = new BigDecimal(voBuilder.getVcpm()).subtract(new BigDecimal(voBuilder.getCompareVcpm()));
                        voBuilder.setCompareVcpmRate(new BigDecimal(voBuilder.getCompareVcpm()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                vcpmDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareVcpm()), 2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareAdSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0)));
                        //AdSaleNum比值
                        int adSaleNumDiff = voBuilder.getAdSaleNum().getValue() - voBuilder.getCompareAdSaleNum().getValue();
                        voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum().getValue() == 0 ? "-" :
                                new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0)));
                        //AdOtherOrderNum比值
                        int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum().getValue() - voBuilder.getCompareAdOtherOrderNum().getValue();
                        voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSales(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                        //AdSales环比值
                        BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                        voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareAdOtherSales(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                        //AdOtherSales环比值
                        BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                        voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getOrderNum()).orElse(0)));
                        //OrderNum比值
                        int orderNumDiff = voBuilder.getOrderNum().getValue() - voBuilder.getCompareOrderNum().getValue();
                        voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSelfSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0)));
                        //AdSelfSaleNum比值
                        int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum().getValue() - voBuilder.getCompareAdSelfSaleNum().getValue();
                        voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum().getValue() == 0 ? "-" :
                                new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0)));
                        //AdOtherSaleNum比值
                        int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum().getValue() - voBuilder.getCompareAdOtherSaleNum().getValue();
                        voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum().getValue() == 0 ? "-" :
                                new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getOrdersNewToBrandFTD()).orElse(0)));
                        //OrdersNewToBrandFTD比值
                        int ordersNewToBrandFTDDiff = voBuilder.getOrdersNewToBrandFTD().getValue() - voBuilder.getCompareOrdersNewToBrandFTD().getValue();
                        voBuilder.setCompareOrdersNewToBrandFTDRate(voBuilder.getCompareOrdersNewToBrandFTD().getValue() == 0 ? "-" :
                                new BigDecimal(ordersNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrdersNewToBrandFTD().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderRateNewToBrandFTD(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getOrderRateNewToBrandFTD()) ?
                                compareItem.getOrderRateNewToBrandFTD() : "0");
                        //OrderRateNewToBrandFTD环比值
                        BigDecimal orderRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getOrderRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()));
                        voBuilder.setCompareOrderRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                orderRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareSalesNewToBrandFTD(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getSalesNewToBrandFTD()) ?
                                compareItem.getSalesNewToBrandFTD() : "0");
                        //SalesNewToBrandFTD环比值
                        BigDecimal salesNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()));
                        voBuilder.setCompareSalesNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                salesNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareSalesRateNewToBrandFTD(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getSalesRateNewToBrandFTD()) ?
                                compareItem.getSalesRateNewToBrandFTD() : "0");
                        //SalesRateNewToBrandFTD环比值
                        BigDecimal salesRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()));
                        voBuilder.setCompareSalesRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                salesRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getUnitsOrderedNewToBrandFTD()).orElse(0)));
                        //UnitsOrderedNewToBrandFTD比值
                        int unitsOrderedNewToBrandFTDDiff = voBuilder.getUnitsOrderedNewToBrandFTD().getValue() - voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue();
                        voBuilder.setCompareUnitsOrderedNewToBrandFTDRate(voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue() == 0 ? "-" :
                                new BigDecimal(unitsOrderedNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareUnitsOrderedRateNewToBrandFTD(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getUnitsOrderedRateNewToBrandFTD()) ?
                                compareItem.getUnitsOrderedRateNewToBrandFTD() : "0");
                        //UnitsOrderedRateNewToBrandFTD环比值
                        BigDecimal UnitsOrderedRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getUnitsOrderedRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()));
                        voBuilder.setCompareUnitsOrderedRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                UnitsOrderedRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPercentage(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getAdCostPercentage()) ?
                                compareItem.getAdCostPercentage() : "0");
                        //AdCostPercentage环比值
                        BigDecimal adCostPercentageDiff = new BigDecimal(voBuilder.getAdCostPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdCostPercentage()));
                        voBuilder.setCompareAdCostPercentageRate(new BigDecimal(voBuilder.getCompareAdCostPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPercentage()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSalePercentage(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getAdSalePercentage()) ? compareItem.getAdSalePercentage() : "0");
                        //AdSalePercentage环比值
                        BigDecimal adSalePercentageDiff = new BigDecimal(voBuilder.getAdSalePercentage()).subtract(new BigDecimal(voBuilder.getCompareAdSalePercentage()));
                        voBuilder.setCompareAdSalePercentageRate(new BigDecimal(voBuilder.getCompareAdSalePercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSalePercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSalePercentage()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNumPercentage(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getAdOrderNumPercentage()) ? compareItem.getAdOrderNumPercentage() : "0");
                        //AdOrderNumPercentage环比值
                        BigDecimal adOrderNumPercentageDiff = new BigDecimal(voBuilder.getAdOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()));
                        voBuilder.setCompareAdOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adOrderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNumPercentage(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getOrderNumPercentage()) ? compareItem.getOrderNumPercentage() : "0");
                        //OrderNumPercentage环比值
                        BigDecimal orderNumPercentageDiff = new BigDecimal(voBuilder.getOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareOrderNumPercentage()));
                        voBuilder.setCompareOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                orderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());


                        //“品牌新买家”订单转化率
                        voBuilder.setCompareOrdersNewToBrandPercentageFTD(org.apache.commons.lang3.StringUtils.isNotBlank(compareItem.getOrdersNewToBrandPercentageFTD()) ? compareItem.getOrdersNewToBrandPercentageFTD() : "0");
                        //OrderNumPercentage环比值
                        BigDecimal ordersNewToBrandPercentageFTDDiff = new BigDecimal(voBuilder.getOrdersNewToBrandPercentageFTD()).subtract(new BigDecimal(voBuilder.getCompareOrdersNewToBrandPercentageFTD()));
                        voBuilder.setCompareOrdersNewToBrandPercentageFTDRate(new BigDecimal(voBuilder.getCompareOrdersNewToBrandPercentageFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                ordersNewToBrandPercentageFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrdersNewToBrandPercentageFTD()), 2, RoundingMode.HALF_UP).toString());

                    }
                }

                return voBuilder.build();

            }).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }

        return AllAdsDataResponse.AdCreateHomeVo.newBuilder()
                .setPage(pageBuilder.build())
                .build();

    }

    @Override
    public List<AdAdsPageVo> getSbAdsVoList(ShopAuth shopAuth, Integer puid, AdAdsPageParam param, Page<AdAdsPageVo> voPage, boolean isExport) {

        List<AdAdsPageVo> pageVoList;

        if (isExport) {
            pageVoList = getAdsPageVoList(puid, param);
            if (CollectionUtils.isEmpty(pageVoList)) {
                return pageVoList;
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getOrderField()) && org.apache.commons.lang3.StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = org.apache.commons.lang3.StringUtils.isNotBlank(param.getOrderField()) && Constants.isADperformanceOrderField(param.getOrderField());
                if (isSorted) {
                    pageVoList = PageUtil.sort(pageVoList, param.getOrderField(), param.getOrderType());
                }
            }
            if (CollectionUtils.isNotEmpty(pageVoList) && pageVoList.size() > EXPORT_MAX_SIZE) {  //限制60000条，需要优化
                pageVoList = pageVoList.subList(0, EXPORT_MAX_SIZE);
            }
            fillinAdInfoForVo(shopAuth, pageVoList, isExport);
            return pageVoList;
        }

        if ((org.apache.commons.lang3.StringUtils.isNotBlank(param.getOrderField()) && org.apache.commons.lang3.StringUtils.isNotBlank(param.getOrderType())) || param.getUseAdvanced()) {  //页面有排序或有高
            List<AdAdsPageVo> voList = getAdsPageVoList(puid, param);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getOrderField()) && org.apache.commons.lang3.StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = org.apache.commons.lang3.StringUtils.isNotBlank(param.getOrderField()) && Constants.isADperformanceOrderField(param.getOrderField());
                if (isSorted) {
                    voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
                }
            }
            //分页
            PageUtil.getPage(voPage, voList);
        } else {
            Page page = new Page(voPage.getPageNo(), voPage.getPageSize());
            page = getPageList(puid, param, page);

            if (page.getTotalSize() > Constants.TOTALSIZELIMIT) {  //总数大于十万
                int totalPage = Constants.TOTALSIZELIMIT / voPage.getPageSize();
                voPage.setTotalPage(totalPage);
                voPage.setTotalSize(Constants.TOTALSIZELIMIT);
            } else {
                voPage.setTotalPage(page.getTotalPage());
                voPage.setTotalSize(page.getTotalSize());
            }
            voPage.setRows(page.getRows());
        }


        return null;
    }

    private Page getPageList(Integer puid, AdAdsPageParam param, Page page) {
        List<AdAdsPageVo> voList = Lists.newArrayList();


        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sb.getCampaignType(), null, null);
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                param.setCampaignIdList(campaignIds);
            } else {
                return page;
            }
        }


        page = amazonSbAdsDao.getPageList(puid, param, page);

        List<AmazonSbAds> poList = page.getRows();
        if (CollectionUtils.isNotEmpty(poList)) {
            List<String> queryIds = poList.stream().map(AmazonSbAds::getQueryId).collect(Collectors.toList());

            List<AdHomePerformancedto> list = new ArrayList<>();
            AdMetricDto adMetricDto;

            list = amazonAdSbAdsReportDao.listSumReportByQueryId(puid, param.getShopId(), param.getStartDate(),
                    param.getEndDate(), param, queryIds);
            adMetricDto = amazonAdSbAdsReportDao.getSumAdMetric(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param);

            Map<String, AdHomePerformancedto> productReportMap = list.stream().filter(Objects::nonNull).collect(Collectors.toMap(AdHomePerformancedto::getQueryId, e -> e));

            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();  //最外层已经查了
            BigDecimal shopSales = param.getShopSales();
            shopSaleDto.setSumRange(shopSales);

            String domain = AmznEndpoint.getByMarketplaceId(poList.get(0).getMarketplaceId()).getDomain();
            AdAdsPageVo vo;
            page.setRows(voList);
            for (AmazonSbAds amazonSbAds : poList) {
                vo = new AdAdsPageVo();
                amazonSbAds.setServingStatus(amazonSbAds.getServingStatus());
                vo.setServingStatus(amazonSbAds.getServingStatus());
                vo.setServingStatusDec(amazonSbAds.getServingStatusDec());
                vo.setServingStatusName(amazonSbAds.getServingStatusName());
                vo.setDomain(domain);
                vo.setType(Constants.SB);
                convertDtoToPageVo(amazonSbAds, vo);
                filterAdMetricData(adMetricDto, vo);
                if (productReportMap.size() > 0) {
                    AdHomePerformancedto adHomePerformancedto = productReportMap.get(amazonSbAds.getQueryId());
                    if (adHomePerformancedto != null) {
                        // 填充报告数据
                        AmazonAdProductReport report = new AmazonAdProductReport();
                        report.setCountDate(adHomePerformancedto.getCountDate());
                        report.setImpressions(adHomePerformancedto.getImpressions());
                        report.setClicks(adHomePerformancedto.getClicks());
                        report.setTotalSales(adHomePerformancedto.getAdSale());   //兼容旧代码
                        /**
                         * TODO 广告报告重构
                         * 本广告产品销售额
                         */
                        report.setAdSales(adHomePerformancedto.getAdSales());   //兼容旧代码
                        report.setSaleNum(adHomePerformancedto.getAdOrderNum());
                        report.setCost(adHomePerformancedto.getAdCost());
                        //本广告产品订单量
                        report.setAdSaleNum(adHomePerformancedto.getAdSaleNum());
                        //广告销量
                        report.setOrderNum(adHomePerformancedto.getSalesNum());
                        //本广告产品销量
                        report.setAdOrderNum(adHomePerformancedto.getOrderNum());
                        report.setOrdersNewToBrand14d(adHomePerformancedto.getOrdersNewToBrand14d());
                        report.setSalesNewToBrand14d(adHomePerformancedto.getSalesNewToBrand14d());
                        cpcCommService.fillReportDataIntoPageVo(vo, report, shopSaleDto);
                    }
                }

                voList.add(vo);
            }
            // 获取汇总信息
            AdMetricDto adMetric = new AdMetricDto();
            filterSumMetricData(voList, adMetric);
            // 填充指标占比
            filterMetricData(voList, adMetric);
        }

        return page;

    }

    // 填充指标总和数据
    private void filterAdMetricData(AdMetricDto adMetricDto, AdAdsPageVo vo) {
        if (adMetricDto == null) {
            vo.setSumCost(BigDecimal.ZERO);
            vo.setSumAdSale(BigDecimal.ZERO);
            vo.setSumAdOrderNum(BigDecimal.ZERO);
            vo.setSumOrderNum(BigDecimal.ZERO);
            return;
        }
        vo.setSumCost(adMetricDto.getSumCost() == null ? BigDecimal.ZERO : adMetricDto.getSumCost());
        vo.setSumAdSale(adMetricDto.getSumAdSale() == null ? BigDecimal.ZERO : adMetricDto.getSumAdSale());
        vo.setSumAdOrderNum(adMetricDto.getSumAdOrderNum() == null ? BigDecimal.ZERO : adMetricDto.getSumAdOrderNum());
        vo.setSumOrderNum(adMetricDto.getSumOrderNum() == null ? BigDecimal.ZERO : adMetricDto.getSumOrderNum());
    }

    private void filterSumMetricData(List<AdAdsPageVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        adMetricDto.setSumCost(voList.stream().filter(item -> item != null && item.getAdCost() != null).map(item -> new BigDecimal(item.getAdCost())).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdSale(voList.stream().filter(item -> item != null && item.getAdSale() != null).map(item -> new BigDecimal(item.getAdSale())).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdAdsPageVo::getAdOrderNum).sum()));
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(AdAdsPageVo::getOrderNum).sum()));
    }

    // 填充指标占比数据
    private void filterMetricData(List<AdAdsPageVo> voList, AdMetricDto adMetricDto) {
        for (AdAdsPageVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage("0");
                vo.setAdSalePercentage("0");
                vo.setAdOrderNumPercentage("0");
                vo.setOrderNumPercentage("0");
                continue;
            }
            computeMetricData(adMetricDto, vo);
        }
    }

    private void computeMetricData(AdMetricDto adMetricDto, AdAdsPageVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(MathUtil.multiply(MathUtil.divide(vo.getAdCost(), adMetricDto.getSumCost().toString()), "100"));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(MathUtil.multiply(MathUtil.divide(vo.getAdSale(), adMetricDto.getSumAdSale().toString()), "100"));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100"));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getOrderNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(MathUtil.multiply(MathUtil.divide(vo.getOrderNum().toString(), adMetricDto.getSumOrderNum().toString()), "100"));
        }
    }

    private List<AdAdsPageVo> getAdsPageVoList(Integer puid, AdAdsPageParam param) {
        List<AdAdsPageVo> voList = Lists.newArrayList();


        //广告组合id不为空
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getPortfolioId())) {
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sb.getCampaignType(), null, null);
            // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                param.setCampaignIdList(campaignIds);
            } else {
                return voList;
            }
        }

        List<AmazonSbAds> poList = amazonSbAdsDao.getList(puid, param);
        if (CollectionUtils.isNotEmpty(poList)) {
            List<String> queryIds = poList.stream().map(AmazonSbAds::getQueryId).collect(Collectors.toList());
            List<AdHomePerformancedto> list = new ArrayList<>();
            if (queryIds.size() > 20000) {
                List<List<String>> lists = Lists.partition(queryIds, 20000);
                List<AdHomePerformancedto> dtoList;
                for (List<String> subList : lists) {

                    dtoList = amazonAdSbAdsReportDao.listSumReportByQueryId(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, subList);

                    list.addAll(dtoList);
                }

                queryIds = null;
            } else {

                list = amazonAdSbAdsReportDao.listSumReportByQueryId(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, queryIds);

            }

            Map<String, AdHomePerformancedto> productReportMap = list.stream().filter(Objects::nonNull).collect(Collectors.toMap(AdHomePerformancedto::getQueryId, e -> e));

            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();  //最外层已经查了
            BigDecimal shopSales = param.getShopSales();
            shopSaleDto.setSumRange(shopSales);

            String domain = AmznEndpoint.getByMarketplaceId(poList.get(0).getMarketplaceId()).getDomain();
            AdAdsPageVo vo;
            for (AmazonSbAds amazonSbAds : poList) {
                vo = new AdAdsPageVo();
                amazonSbAds.setServingStatus(amazonSbAds.getServingStatus());
                vo.setServingStatus(amazonSbAds.getServingStatus());
                vo.setServingStatusDec(amazonSbAds.getServingStatusDec());
                vo.setServingStatusName(amazonSbAds.getServingStatusName());
                vo.setDomain(domain);
                vo.setType(Constants.SB);
                convertDtoToPageVo(amazonSbAds, vo);

                if (productReportMap.size() > 0) {
                    AdHomePerformancedto adHomePerformancedto = productReportMap.get(amazonSbAds.getQueryId());
                    if (adHomePerformancedto != null) {
                        // 填充报告数据
                        AmazonAdProductReport report = new AmazonAdProductReport();
                        report.setCountDate(adHomePerformancedto.getCountDate());
                        report.setImpressions(adHomePerformancedto.getImpressions());
                        report.setClicks(adHomePerformancedto.getClicks());
                        report.setTotalSales(adHomePerformancedto.getAdSale());  //兼容旧代码
                        /**
                         * TODO 广告报告重构
                         * 本广告产品销售额
                         */
                        report.setAdSales(adHomePerformancedto.getAdSales());   //兼容旧代码
                        report.setSaleNum(adHomePerformancedto.getAdOrderNum());
                        //广告销量
                        report.setOrderNum(adHomePerformancedto.getSalesNum());
                        report.setCost(adHomePerformancedto.getAdCost());
                        //本广告产品订单量
                        report.setAdSaleNum(adHomePerformancedto.getAdSaleNum());
                        //“品牌新买家”订单量
                        report.setOrdersNewToBrand14d(adHomePerformancedto.getOrdersNewToBrand14d());
                        //“品牌新买家”销售额
                        report.setSalesNewToBrand14d(adHomePerformancedto.getSalesNewToBrand14d());
                        cpcCommService.fillReportDataIntoPageVo(vo, report, shopSaleDto);
                    }
                }
                voList.add(vo);
            }

            if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索,需要过滤
                cpcCommService.filterAdsAdVanceData(voList, param);
            }
        }
        // 获取汇总信息
        AdMetricDto adMetricDto = new AdMetricDto();
        filterSumMetricData(voList, adMetricDto);
        // 填充指标占比
        filterMetricData(voList, adMetricDto);
        return voList;
    }

    private void convertDtoToPageVo(AmazonSbAds amazonSbAds, AdAdsPageVo vo) {
        vo.setId(amazonSbAds.getId());
        vo.setShopId(amazonSbAds.getShopId());
        vo.setCampaignId(amazonSbAds.getCampaignId());
        vo.setAdGroupId(amazonSbAds.getAdGroupId());
        vo.setAdId(amazonSbAds.getAdId());
        vo.setState(amazonSbAds.getState());
        vo.setUpdateTime(amazonSbAds.getUpdateTime());
        vo.setQueryId(amazonSbAds.getQueryId());
        vo.setName(amazonSbAds.getName());
        if (StringUtils.isNotBlank(amazonSbAds.getBrandLogoUrl())) {
            vo.setBrandLogoUrl(amazonSbAds.getBrandLogoUrl());
        }
        if (StringUtils.isNotBlank(amazonSbAds.getCustomImageUrl())) {
            vo.setBrandLogoUrl(amazonSbAds.getCustomImageUrl());
        }
    }

    /**
     * 填充广告信息
     *
     * @param shopAuth 门店对象
     */
    private void fillinAdInfoForVo(ShopAuth shopAuth, List<AdAdsPageVo> rows, boolean isExport) {
        long startTime = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(rows)) {

            Map<String, AmazonAdCampaignAll> sbCampaignMap = null;
            Map<String, AmazonSbAdGroup> sbGroupMap = null;
            //分组获取广告活动和广告组IDS
            Map<String, Set<String>> allTypeCamMap = rows.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AdAdsPageVo::getType, Collectors.mapping(AdAdsPageVo::getCampaignId, Collectors.toSet())));
            Map<String, Set<String>> allTypeGroupMap = rows.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AdAdsPageVo::getType, Collectors.mapping(AdAdsPageVo::getAdGroupId, Collectors.toSet())));


            //sp广告活动
            if (MapUtils.isNotEmpty(allTypeCamMap) && allTypeCamMap.containsKey(Constants.SB) && CollectionUtils.isNotEmpty(allTypeCamMap.get(Constants.SB))) {
                List<String> sbCampaignIds = Arrays.asList(allTypeCamMap.get(Constants.SB).toArray(new String[]{}));
                List<AmazonAdCampaignAll> spAdCampaigns = amazonAdCampaignAllDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId(), sbCampaignIds, CampaignTypeEnum.sb.getCampaignType());
                if (CollectionUtils.isNotEmpty(spAdCampaigns)) {
                    sbCampaignMap = spAdCampaigns.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
            }
            log.info("============================== 查询所有广告产品未分页数据 step1 ,并转vo花费时间 {} ==============================", System.currentTimeMillis() - startTime);


            //sb广告组
            if (MapUtils.isNotEmpty(allTypeGroupMap) && allTypeGroupMap.containsKey(Constants.SB) && CollectionUtils.isNotEmpty(allTypeGroupMap.get(Constants.SB))) {
                List<String> spGroupIds = Arrays.asList(allTypeGroupMap.get(Constants.SB).toArray(new String[]{}));
                List<AmazonSbAdGroup> sbGroupList = groupDao.getAdGroupByIds(shopAuth.getPuid(), shopAuth.getId(), spGroupIds);
                if (CollectionUtils.isNotEmpty(sbGroupList)) {
                    sbGroupMap = sbGroupList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, e -> e));
                }
            }
            log.info("============================== 查询所有广告产品未分页数据 step3 ,并转vo花费时间 {} ==============================", System.currentTimeMillis() - startTime);


            Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>(); //广告组合信息

            Map<String, AmazonSbAdGroup> finalSbGroupMap = sbGroupMap;
            Map<String, AmazonAdCampaignAll> finalSbCampaignMap = sbCampaignMap;
            rows.stream().filter(Objects::nonNull).forEach(vo -> {


                if (Constants.SB.equalsIgnoreCase(vo.getType())) {
                    //广告信息填充
                    if (MapUtils.isNotEmpty(finalSbCampaignMap) && finalSbCampaignMap.containsKey(vo.getCampaignId())) {
                        AmazonAdCampaignAll campaign = finalSbCampaignMap.get(vo.getCampaignId());
                        vo.setCampaignName(campaign.getName());
                        vo.setCampaignTargetingType(campaign.getTargetingType());

                        if (org.apache.commons.lang3.StringUtils.isNotBlank(campaign.getPortfolioId())) {
                            vo.setPortfolioId(campaign.getPortfolioId());
                            if (portfolioMap.containsKey(campaign.getPortfolioId())) {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(campaign.getPortfolioId());
                                vo.setPortfolioName(amazonAdPortfolio.getName());
                                vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                            } else {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(campaign.getPuid(), campaign.getShopId(), campaign.getPortfolioId());
                                if (amazonAdPortfolio != null) {
                                    vo.setPortfolioName(amazonAdPortfolio.getName());
                                    portfolioMap.put(campaign.getPortfolioId(), amazonAdPortfolio);
                                    vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                                } else {
                                    vo.setPortfolioName("广告组合待同步");
                                }
                            }
                        } else {
                            vo.setPortfolioName("-");
                        }

                    }
                    //广告组名称填充
                    if (MapUtils.isNotEmpty(finalSbGroupMap) && finalSbGroupMap.containsKey(vo.getAdGroupId())) {
                        vo.setAdGroupName(finalSbGroupMap.get(vo.getAdGroupId()).getName());
                        vo.setAdGroupType(finalSbGroupMap.get(vo.getAdGroupId()).getAdGroupType());
                    }

                }


            });
        }
    }


    @Override
    public AllAdsAggregateDataResponse.AdCreateHomeVo getAllAdsAggregateData(int puid, AdAdsPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        // 取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getShopId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);

        //所有数据
        long start = System.currentTimeMillis();

        //获取不同类型数据 sp、sb
        List<AdHomePerformancedto> reportList;
        List<AdHomePerformancedto> compareReportList = new ArrayList<>();
        List<AdHomePerformancedto> reportDayList;  //每日汇总数据
        List<String> queryIdList;
        boolean isNull = false;  // 查询的数据为空


        if (!isNull) {
            //广告组合id不为空
            if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getPortfolioId())) {
                List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sb.getCampaignType(), null, null);
                // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                if (CollectionUtils.isNotEmpty(campaignIds)) {
                    param.setCampaignIdList(campaignIds);
                } else {
                    isNull = true;
                }
            }
        }
        if (isNull) {
            reportList = new ArrayList<>();
            reportDayList = new ArrayList<>();
        } else {

            reportList = amazonAdSbAdsReportDao.getSbReportByDate(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param);
            if (param.getIsCompare()) {
                compareReportList = amazonAdSbAdsReportDao.getSbReportByDate(puid, param.getShopId(), param.getCompareStartDate(), param.getCompareEndDate(), param);
            }

            queryIdList = reportList.stream().map(AdHomePerformancedto::getQueryId).collect(Collectors.toList());

            reportDayList = amazonAdSbAdsReportDao.getSbReportByQueryIdList(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), queryIdList);

        }

        //环比数据
        BigDecimal shopSalesByDateCompare = BigDecimal.ZERO;
        if (param.getIsCompare()) {
            shopSalesByDateCompare = cpcShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
            if (shopSalesByDateCompare == null) {
                shopSalesByDateCompare = BigDecimal.ZERO;
            }
        }

        //汇总指标数据
        AdHomeAggregateDataRpcVo aggregateDataVo = getAdsAggregateDataVo(reportList, compareReportList, shopSalesByDate, shopSalesByDateCompare);

        //查询货币类型
        String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();
        //获取chart数据
        List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, shopSalesByDate);
        List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, shopSalesByDate);
        List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, param.getStartDate(), param.getEndDate(), reportDayList, shopSalesByDate);

        return AllAdsAggregateDataResponse.AdCreateHomeVo.newBuilder()
                .setAggregateDataVo(aggregateDataVo)
                .addAllDay(dayPerformanceVos)
                .addAllMonth(monthPerformanceVos)
                .addAllWeek(weekPerformanceVos)
                .build();
    }


    /**
     * 汇总数据组装
     *
     * @param rows
     * @return
     */
    private AdHomeAggregateDataRpcVo getAdsAggregateDataVo(List<AdHomePerformancedto> rows, List<AdHomePerformancedto> rowsCompare, BigDecimal shopSales, BigDecimal shopSalesCompare) {

        //为前端渲染页面   集合为0时,也返回对象,不返回null
        if (CollectionUtils.isEmpty(rows)) {
            return AdHomeAggregateDataRpcVo.newBuilder()
                    .setAcos("0")
                    .setAsots("0")
                    .setAcots("0")
                    .setRoas("0")
                    .setAdCost("0")
                    .setAdCostPerClick("0")
                    .setAdOrderNum(Int32Value.of(0))
                    .setCvr("0")
                    .setCtr("0")
                    .setAdSale("0")
                    .setClicks(Int32Value.of(0))
                    .setImpressions(Int32Value.of(0))
                    .setCpa("0")
                    .setCpa("0")
                    .setAdSaleNum(Int32Value.of(0))
                    .setAdOtherOrderNum(Int32Value.of(0))
                    .setAdSales("0")
                    .setAdOtherSales("0")
                    .setOrderNum(Int32Value.of(0))
                    .setAdSelfSaleNum(Int32Value.of(0))
                    .setAdOtherSaleNum(Int32Value.of(0))
                    .setOrdersNewToBrandFTD(Int32Value.of(0))
                    .setOrderRateNewToBrandFTD("0")
                    .setSalesNewToBrandFTD("0")
                    .setSalesRateNewToBrandFTD("0")
                    .setOrdersNewToBrandPercentageFTD("0")
                    .setAdCostPercentage("0")
                    .setAdSalePercentage("0")
                    .setAdOrderNumPercentage("0")
                    .setOrderNumPercentage("0")
                    .build();
        }

        //点击量
        int sumClicks = rows.stream().filter(item -> item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNum = rows.stream().filter(item -> item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressions = rows.stream().filter(item -> item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSale = rows.stream().filter(item -> item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcost = rows.stream().filter(item -> item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);

        //ACoS
        BigDecimal sumAcos = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(sumAdSale, 2, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClick = sumClicks == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCVr = sumClicks == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNum).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtr = sumImpressions == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicks).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressions), 2, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roas = sumAdcost.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSale.divide(sumAdcost, 2, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(shopSales, 2, BigDecimal.ROUND_HALF_UP);//asots
        BigDecimal asots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSale.multiply(new BigDecimal("100")).divide(shopSales, 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal cpa = sumAdcost.compareTo(BigDecimal.ZERO) == 0 || BigDecimal.valueOf(sumAdOrderNum).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(sumAdcost, BigDecimal.valueOf(sumAdOrderNum));

        //本广告产品订单量
        int sumAdSaleNum = rows.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdHomePerformancedto::getAdSaleNum).sum();
        //本广告产品销售额
        BigDecimal sumAdSales = rows.stream().filter(item -> item != null && item.getAdSales() != null).map(e -> e.getAdSales()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //CPC,VCPM广告销量
        int sumSalesNum = rows.stream().filter(item -> item != null && item.getSalesNum() != null).mapToInt(AdHomePerformancedto::getSalesNum).sum();
        //“品牌新买家”订单量
        int sumOrdersNewToBrand14d = rows.stream().filter(item -> item != null && item.getOrdersNewToBrand14d() != null).mapToInt(AdHomePerformancedto::getOrdersNewToBrand14d).sum();
        //“品牌新买家”销售额
        BigDecimal sumSalesNewToBrand14d = rows.stream().filter(item -> item != null && item.getSalesNewToBrand14d() != null).map(e -> e.getSalesNewToBrand14d()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //每笔订单花费
        BigDecimal sumCpa = sumAdOrderNum == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumAdOrderNum), 2, BigDecimal.ROUND_HALF_UP);
        //其他产品广告订单量
        int sumAdOtherOrderNum = sumAdOrderNum - sumAdSaleNum;
        //其他产品广告销售额
        BigDecimal sumAdOtherSales = sumAdSale.subtract(sumAdSales);
        //本广告产品销量
        int sumOrderNum = rows.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(AdHomePerformancedto::getOrderNum).sum();
        //其他产品广告销量
        int sumAdOtherSaleNum = sumSalesNum - sumOrderNum;
        //“品牌新买家”订单百分比
        BigDecimal sumOrderRateNewToBrand14d = sumAdOrderNum == 0 ? BigDecimal.ZERO : new BigDecimal(sumOrdersNewToBrand14d).multiply(new BigDecimal("100")).divide(new BigDecimal(sumAdOrderNum), 2, BigDecimal.ROUND_HALF_UP);
        //“品牌新买家”销售额百分比
        BigDecimal sumSalesRateNewToBrand14d = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumSalesNewToBrand14d.multiply(new BigDecimal("100")).divide(sumAdSale, 2, BigDecimal.ROUND_HALF_UP);
        //“品牌新买家”订单转化率
        BigDecimal sumOrdersNewToBrandPercentage14d = sumClicks == 0 ? BigDecimal.ZERO : new BigDecimal(sumOrdersNewToBrand14d).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        String sumAdCostPercentage = sumAdcost.compareTo(BigDecimal.ZERO) == 0 ? "0" : "100.0000";
        String sumAdSalePercentage = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? "0" : "100.0000";
        String sumAdOrderNumPercentage = sumAdOrderNum == 0 ? "0" : "100.0000";
        String sumOrderNumPercentage = sumSalesNum == 0 ? "0" : "100.0000";

        //环比数据
        //点击量
        int sumClicksCompare = rowsCompare.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNumCompare = rowsCompare.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressionsCompare = rowsCompare.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSaleCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcostCompare = rowsCompare.stream().filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);

        //ACoS
        BigDecimal sumAcosCompare = sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcostCompare.multiply(new BigDecimal("100")).divide(sumAdSaleCompare, 2, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClickCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : sumAdcostCompare.divide(new BigDecimal(sumClicksCompare), 2, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCvrCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNumCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicksCompare), 2, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtrCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicksCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressionsCompare), 2, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roasCompare = sumAdcostCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSaleCompare.divide(sumAdcostCompare, 2, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acotsCompare = shopSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcostCompare.multiply(new BigDecimal("100")).divide(shopSalesCompare, 2, BigDecimal.ROUND_HALF_UP);
        //asots
        BigDecimal asotsCompare = shopSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSaleCompare.multiply(new BigDecimal("100")).divide(shopSalesCompare, 2, BigDecimal.ROUND_HALF_UP);

        AdHomeAggregateDataRpcVo.Builder builder = AdHomeAggregateDataRpcVo.newBuilder();
        builder.setAcos(sumAcos.stripTrailingZeros().toString());
        builder.setAcots(acots.stripTrailingZeros().toString());
        builder.setAsots(asots.stripTrailingZeros().toString());
        builder.setRoas(roas.stripTrailingZeros().toString());
        builder.setAdCost(sumAdcost.stripTrailingZeros().toString());
        builder.setAdCostPerClick(sumAdCostPerClick.stripTrailingZeros().toString());
        builder.setAdOrderNum(Int32Value.of(sumAdOrderNum));
        builder.setCvr(sumCVr.stripTrailingZeros().toString());
        builder.setCtr(sumCtr.stripTrailingZeros().toString());
        builder.setAdSale(sumAdSale.stripTrailingZeros().toString());
        builder.setClicks(Int32Value.of(sumClicks));
        builder.setImpressions(Int32Value.of(sumImpressions));
        builder.setCpa(cpa.stripTrailingZeros().toString());
        builder.setCpa(sumCpa.stripTrailingZeros().toString());
        builder.setAdSaleNum(Int32Value.of(sumAdSaleNum));
        builder.setAdOtherOrderNum(Int32Value.of(sumAdOtherOrderNum));
        builder.setAdSales(sumAdSales.stripTrailingZeros().toString());
        builder.setAdOtherSales(sumAdOtherSales.stripTrailingZeros().toString());
        builder.setOrderNum(Int32Value.of(sumSalesNum));
        builder.setAdSelfSaleNum(Int32Value.of(sumOrderNum));
        builder.setAdOtherSaleNum(Int32Value.of(sumAdOtherSaleNum));
        builder.setOrdersNewToBrandFTD(Int32Value.of(sumOrdersNewToBrand14d));
        builder.setOrderRateNewToBrandFTD(sumOrderRateNewToBrand14d.stripTrailingZeros().toString());
        builder.setSalesNewToBrandFTD(sumSalesNewToBrand14d.stripTrailingZeros().toString());
        builder.setSalesRateNewToBrandFTD(sumSalesRateNewToBrand14d.stripTrailingZeros().toString());
        builder.setOrdersNewToBrandPercentageFTD(sumOrdersNewToBrandPercentage14d.stripTrailingZeros().toString());
        builder.setAdCostPercentage(sumAdCostPercentage);
        builder.setAdSalePercentage(sumAdSalePercentage);
        builder.setAdOrderNumPercentage(sumAdOrderNumPercentage);
        builder.setOrderNumPercentage(sumOrderNumPercentage);


        //环比数据
        builder.setCompareAcos(sumAcosCompare.toPlainString());
        builder.setCompareRoas(roasCompare.toPlainString());
        builder.setCompareAdCost(sumAdcostCompare.toPlainString());
        builder.setCompareAdCostPerClick(sumAdCostPerClickCompare.toPlainString());
        builder.setCompareAdOrderNum(Int32Value.of(sumAdOrderNumCompare));
        builder.setCompareCvr(sumCvrCompare.toPlainString());
        builder.setCompareCtr(sumCtrCompare.toPlainString());
        builder.setCompareAdSale(sumAdSaleCompare.toPlainString());
        builder.setCompareClicks(Int32Value.of(sumClicksCompare));
        builder.setCompareImpressions(Int32Value.of(sumImpressionsCompare));
        builder.setCompareAcots(acotsCompare.toPlainString());
        builder.setCompareAsots(asotsCompare.toPlainString());
        //环比值
        builder.setCompareAcosRate(sumAcosCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAcos.subtract(sumAcosCompare))
                .multiply(new BigDecimal(100)).divide(sumAcosCompare, 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareRoasRate(roasCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (roas.subtract(roasCompare))
                .multiply(new BigDecimal(100)).divide(roasCompare, 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareAdCostRate(sumAdcostCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdcost.subtract(sumAdcostCompare))
                .multiply(new BigDecimal(100)).divide(sumAdcostCompare, 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareAdCostPerClickRate(sumAdCostPerClickCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdCostPerClick.subtract(sumAdCostPerClickCompare))
                .multiply(new BigDecimal(100)).divide(sumAdCostPerClickCompare, 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareAdOrderNumRate(sumAdOrderNumCompare == 0 ? "-" : new BigDecimal(sumAdOrderNum - sumAdOrderNumCompare)
                .multiply(new BigDecimal(100)).divide(new BigDecimal(sumAdOrderNumCompare), 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareCvrRate(sumCvrCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCVr.subtract(sumCvrCompare))
                .multiply(new BigDecimal(100)).divide(sumCvrCompare, 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareCtrRate(sumCtrCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCtr.subtract(sumCtrCompare))
                .multiply(new BigDecimal(100)).divide(sumCtrCompare, 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareAdSaleRate(sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdSale.subtract(sumAdSaleCompare))
                .multiply(new BigDecimal(100)).divide(sumAdSaleCompare, 2, RoundingMode.HALF_UP).toPlainString());


        builder.setCompareClicksRate(sumClicksCompare == 0 ? "-" : new BigDecimal(sumClicks - sumClicksCompare)
                .multiply(new BigDecimal(100)).divide(new BigDecimal(sumClicksCompare), 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareImpressionsRate(sumImpressionsCompare == 0 ? "-" : new BigDecimal(sumImpressions - sumImpressionsCompare)
                .multiply(new BigDecimal(100)).divide(new BigDecimal(sumImpressionsCompare), 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareAcotsRate(acotsCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (acots.subtract(acotsCompare))
                .multiply(new BigDecimal(100)).divide(acotsCompare, 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareAsotsRate(asotsCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (asots.subtract(asotsCompare))
                .multiply(new BigDecimal(100)).divide(asotsCompare, 2, RoundingMode.HALF_UP).toPlainString());
        return builder.build();
    }

    @Override
    public Result getAsinListByLandingPageUrl(int puid, int shopId, String adFormat, String landingPageStr) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        List<String> asinList = Lists.newArrayList();
        //todo 需要修改，增加SB创建着陆页类型对应的逻辑
        if ("productCollection".equalsIgnoreCase(adFormat) && org.apache.commons.lang3.StringUtils.isNotBlank(landingPageStr)) {  //商品集的需要接口取最新asinlist
            asinList.addAll(getAsinListByUrl(shop, profile, landingPageStr));

        }
        if ("storeSpotlight".equalsIgnoreCase(adFormat) && org.apache.commons.lang3.StringUtils.isNotBlank(landingPageStr)){
            //待使用
        }
        if (("video".equalsIgnoreCase(adFormat) || ("brandVideo").equalsIgnoreCase(adFormat)) && org.apache.commons.lang3.StringUtils.isNotBlank(landingPageStr)) {  //SBV的需要接口取最新asinlist
            asinList.addAll(getAsinListByUrl(shop, profile, landingPageStr));
        }
        return ResultUtil.returnSucc(asinList);
    }

    private List<String> getAsinListByUrl(ShopAuth shop, AmazonAdProfile profile, String landingPageStr) {
        AdLandingPage landingPage = JSONUtil.jsonToObject(landingPageStr, AdLandingPage.class);
        if (landingPage != null && StringUtils.isNotBlank(landingPage.getUrl())) {
            Result<PageAsinsResult> result = pageAsinsApiService.getAsinList(shop, profile, landingPage.getUrl());
            if (result.success()) {
                PageAsinsResult resultData = result.getData();
                if (resultData != null && CollectionUtils.isNotEmpty(resultData.getAsinList())) {
                    return resultData.getAsinList();
                }
            }
        } else if (landingPage != null && CollectionUtils.isNotEmpty(landingPage.getAsins())) {
            return landingPage.getAsins();
        }
        return Collections.emptyList();
    }

    @Override
    public NewCreateInfoResponse createStoreSpotlight(NewCreateCampaignRequest request) {
        return createAdFormat(request, SBAdFormatEnum.STORE_SPOTLIGHT.getCode());
    }

    @Override
    public NewCreateInfoResponse createSBV(NewCreateCampaignRequest request) {
        return createAdFormat(request, SBAdFormatEnum.VIDEO.getCode());
    }

    @Override
    public Result<SbAdsInfoVo> getAdsInfo(Integer puid ,Integer shopId, String adId) {
        //校验店铺权限
        //校验adId是否存在
        //获取adId对应的广告活动目标参数
        //从db中获取对应ads的信息
        if (Objects.isNull(puid) || Objects.isNull(shopId) || org.apache.commons.lang3.StringUtils.isEmpty(adId)) {
            return ResultUtil.returnErr("店铺Id或者广告创意id不能为空");
        }
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        AmazonSbAds adsInfo = amazonSbAdsDao.getAdsByAdId(puid, shopId, adId);
        if (Objects.isNull(adsInfo)) {
            return ResultUtil.returnErr("对应的广告创意不存在");
        }
        String campaignId = adsInfo.getCampaignId();
        if (StringUtils.isEmpty(campaignId)) {
            return ResultUtil.returnErr("adId对应的广告活动不存在");
        }
        AmazonAdCampaignAll campaignInfo = amazonSbAdCampaignDao.getByCampaignId(puid, shopId, shop.getMarketplaceId(), campaignId, "sb");
        if (Objects.isNull(campaignInfo)) {
            return ResultUtil.returnErr("adId对应的广告活动不存在");
        }
        SbAdsInfoVo.Builder adInfoBuilder = SbAdsInfoVo.newBuilder();
        Optional.of(campaignInfo).map(AmazonAdCampaignAll::getAdGoal).filter(StringUtils::isNotEmpty).ifPresent(adInfoBuilder::setAdGoal);
        Optional.ofNullable(adsInfo.getAdFormat()).filter(StringUtils::isNotEmpty).ifPresent(adInfoBuilder::setAdFormat);
        Optional.ofNullable(adsInfo.getName()).filter(StringUtils::isNotEmpty).ifPresent(adInfoBuilder::setAdsName);
        Optional.ofNullable(adsInfo.getServingStatus()).filter(StringUtils::isNotEmpty).ifPresent(adInfoBuilder::setServingStatus);

        //设置着陆页信息
        Optional.ofNullable(adsInfo.getLandingPage()).filter(StringUtils::isNotEmpty).map(landingStr -> {
            SbLandingPageVo landingVo;
            try {
                landingVo = JSONObject.parseObject(landingStr, SbLandingPageVo.class);
            } catch (Exception e) {
                log.error("sb landingPage parse error", e);
                return null;
            }
            LandingPage.Builder landingBuilder = LandingPage.newBuilder();
            Optional.ofNullable(landingVo.getAsins()).filter(CollectionUtils::isNotEmpty).ifPresent(landingBuilder::addAllAsins);
            Optional.ofNullable(landingVo.getPageType()).filter(StringUtils::isNotEmpty).ifPresent(landingBuilder::setPageType);
            Optional.ofNullable(landingVo.getUrl()).filter(StringUtils::isNotEmpty).ifPresent(landingBuilder::setUrl);
            return landingBuilder.build();
        }).ifPresent(adInfoBuilder::setLandingPage);

        //设置创意信息
        Optional.ofNullable(adsInfo.getCreative()).filter(StringUtils::isNotEmpty).map(creativeStr -> {
            SbAdsCreativeInfoVo creatives = null;
            try {
                creatives = JSONObject.parseObject(creativeStr, SbAdsCreativeInfoVo.class);
            } catch (Exception e) {
                log.error("sb create parse error", e);
                return null;
            }
            com.meiyunji.sponsored.rpc.sb.ads.SbAdsCreativeInfoVo.Builder creativesBuilder = com.meiyunji.sponsored.rpc.sb.ads.SbAdsCreativeInfoVo.newBuilder();
            BeanUtils.copyProperties(creatives, creativesBuilder, ParamCopyUtil.checkPropertiesNullOrEmpty(creatives));

            //设置创意asinList
            Optional.ofNullable(creatives.getAsins()).filter(CollectionUtils::isNotEmpty).ifPresent(creativesBuilder::addAllAsins);
            Optional.ofNullable(creatives.getBrandLogoAssetId()).filter(StringUtils::isNotEmpty).ifPresent(creativesBuilder::setBrandLogoAssetID);

            //设置创意用户自定义图片
            if (CollectionUtils.isNotEmpty(creatives.getCustomImages())) {
                List<CustomImages> cusBuilderList = new ArrayList<>();
                for (CustomImagesVo cVo : creatives.getCustomImages()) {
                    CustomImages.Builder cBuilder = CustomImages.newBuilder();
                    cBuilder.setAssetId(cVo.getAssetId());
                    cBuilder.setUrl(cVo.getUrl());
                    if (Objects.nonNull(cVo.getCrop())) {
                        CustomImages.Crop.Builder cropBuilder = CustomImages.Crop.newBuilder();
                        Optional.ofNullable(cVo.getCrop().getTop()).ifPresent(cropBuilder::setTop);
                        Optional.ofNullable(cVo.getCrop().getLeft()).ifPresent(cropBuilder::setLeft);
                        Optional.ofNullable(cVo.getCrop().getWidth()).ifPresent(cropBuilder::setWidth);
                        Optional.ofNullable(cVo.getCrop().getHeight()).ifPresent(cropBuilder::setHeight);
                        cBuilder.setCrop(cropBuilder.build());
                    }
                    cusBuilderList.add(cBuilder.build());
                }
                creativesBuilder.addAllCustomImages(cusBuilderList);
            }

            //设置创意子页面
            if (CollectionUtils.isNotEmpty(creatives.getSubpages())) {
                List<SbAdSubPage> subPageList = new ArrayList<>();
                for (SbAdSubpageVo subPageVo : creatives.getSubpages()) {
                    SbAdSubPage.Builder subBuilder = SbAdSubPage.newBuilder();
                    Optional.ofNullable(subPageVo.getPageTitle()).ifPresent(subBuilder::setPageTitle);
                    Optional.ofNullable(subPageVo.getAsin()).ifPresent(subBuilder::setAsin);
                    Optional.ofNullable(subPageVo.getUrl()).ifPresent(subBuilder::setUrl);
                    subPageList.add(subBuilder.build());
                }
                creativesBuilder.addAllSubpages(subPageList);
            }

            //设置品牌logo裁剪参数
            if (Objects.nonNull(creatives.getBrandLogoCrop())) {
                BrandLogoCrop.Builder brandLogoCropBuilder = BrandLogoCrop.newBuilder();
                Optional.ofNullable(creatives.getBrandLogoCrop().getTop()).ifPresent(brandLogoCropBuilder::setTop);
                Optional.ofNullable(creatives.getBrandLogoCrop().getLeft()).ifPresent(brandLogoCropBuilder::setLeft);
                Optional.ofNullable(creatives.getBrandLogoCrop().getWidth()).ifPresent(brandLogoCropBuilder::setWidth);
                Optional.ofNullable(creatives.getBrandLogoCrop().getHeight()).ifPresent(brandLogoCropBuilder::setHeight);
                creativesBuilder.setBrandLogoCrop(brandLogoCropBuilder.build());
            }

            if (Objects.nonNull(creatives.getCustomImageCrop())) {
                //设置自定义图片裁剪参数
                CustomImageCrop.Builder cusImageCropBuilder = CustomImageCrop.newBuilder();
                Optional.ofNullable(creatives.getCustomImageCrop().getTop()).ifPresent(cusImageCropBuilder::setTop);
                Optional.ofNullable(creatives.getCustomImageCrop().getLeft()).ifPresent(cusImageCropBuilder::setLeft);
                Optional.ofNullable(creatives.getCustomImageCrop().getWidth()).ifPresent(cusImageCropBuilder::setWidth);
                Optional.ofNullable(creatives.getCustomImageCrop().getHeight()).ifPresent(cusImageCropBuilder::setHeight);
                creativesBuilder.setCustomImageCrop(cusImageCropBuilder.build());
            }

            //设置原始视频素材id
            if (CollectionUtils.isNotEmpty(creatives.getOriginalVideoAssetIds())) {
                List<String> originalVideoAssetIds = new ArrayList<>();
                originalVideoAssetIds.addAll(creatives.getOriginalVideoAssetIds());
                creativesBuilder.addAllOriginalVideoAssetIds(originalVideoAssetIds);
            }

            //设置当前视频素材id
            if
            (CollectionUtils.isNotEmpty(creatives.getVideoAssetIds())) {
                List<String> videoAssetIds = new ArrayList<>();
                videoAssetIds.addAll(creatives.getVideoAssetIds());

                String videoAssetId = videoAssetIds.get(0);
                if (StringUtils.isNotEmpty(videoAssetId)) {
                    String[] assetIdAndeVersion = videoAssetId.split(":");
                    Result<GetAssetResult> assetInfo = cpcAssetsApiService.getAsset(shop, profile, assetIdAndeVersion[0], assetIdAndeVersion.length > 1 ? assetIdAndeVersion[1] : "");
                    Optional.ofNullable(assetInfo).map(Result::getData)
                            .map(GetAssetResult::getAssetVersionList)
                            .filter(CollectionUtils::isNotEmpty)
                            .map(l -> l.get(0)).map(AssetVersionList::getUrl).ifPresent(creativesBuilder::setVideoUrl);
                }

                creativesBuilder.addAllVideoAssetIds(videoAssetIds);
            }
            return creativesBuilder.build();
        }).ifPresent(adInfoBuilder::setCreative);
        return ResultUtil.returnSucc(adInfoBuilder.build());
    }

    @Override
    public Result<SbAdsInfoVo> editAdsInfo(EditSbAdsInfoRequest request) {
        int puid = request.getPuid();
        int shopId = request.getShopId();
        String adId = request.getAdId();
        if (puid == 0 || shopId == 0 || StringUtils.isEmpty(adId)) {
            return ResultUtil.returnErr("店铺Id或者广告创意id不能为空");
        }
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        AmazonSbAds adsInfo = amazonSbAdsDao.getAdsByAdId(puid, shopId, adId);
        if (Objects.isNull(adsInfo)) {
            return ResultUtil.returnErr("对应的广告创意不存在");
        }
        String adName = "";
        if (StringUtils.isNotEmpty(request.getAdsName()) && !adsInfo.getName().equals(request.getAdsName())) {
            adName = request.getAdsName();
        }
        String adGroupId = adsInfo.getAdGroupId();
        if (StringUtils.isEmpty(adGroupId)) {
            return ResultUtil.returnErr("adId对应的广告组不存在");
        }
        //这里需要去对应的广告组，因为ads直接关联的是广告组，而并不是广告活动
        AmazonSbAdGroup groupInfo = amazonSbAdGroupDao.getByGroupId(puid, shopId, adGroupId);
        if (Objects.isNull(groupInfo)) {
            return ResultUtil.returnErr("adId对应的广告组不存在");
        }
        String adFormat = groupInfo.getAdFormat();
        SBAdFormatEnum adFormatEnum = SBAdFormatEnum.getSBAdFormatEnumByVal(adFormat);
        //通过广告格式参数来判断入参是否正确
        if (Objects.isNull(adFormatEnum)) {
            return ResultUtil.returnErr("对应的广告格式不存在，无法修改");
        }
        String pageType = request.getLandingPage().getPageType();
        SBAdsLandingPageTypeEnum pageTypeEnum = SBAdsLandingPageTypeEnum.getSBAdsLandingPageTypeEnumByCode(Integer.parseInt(pageType));
        if (Objects.isNull(pageTypeEnum)) {
            log.error("landing page type error:{}", pageType);
            return ResultUtil.returnErr("着陆页类型错误");
        }
        if (Arrays.stream(pageTypeEnum.getAllowedAdFormat()).noneMatch(f -> f == adFormatEnum)) {
            log.error("landing page type error:{}", pageType);
            return ResultUtil.returnErr("着陆页类型错误");
        }
        if (SBAdsLandingPageTypeEnum.STORE == pageTypeEnum) {
            //如果pageType为"亚马逊旗舰店"时
            String checkMsg = checkStoreParam(adFormatEnum, request.getCreative());
            if (StringUtils.isNotEmpty(checkMsg)) {
                return ResultUtil.returnErr(checkMsg);
            }
        }
        if (SBAdsLandingPageTypeEnum.PRODUCT_LIST == pageTypeEnum) {
            //如果pageType为"新着陆页"时
            String checkMsg = checkProductListParam(adFormatEnum, request.getCreative());
            if (StringUtils.isNotEmpty(checkMsg)) {
                return ResultUtil.returnErr(checkMsg);
            }
        }
        if (SBAdsLandingPageTypeEnum.DETAIL_PAGE == pageTypeEnum) {
            //如果pageType为"商品详情页"时
            String checkMsg = checkDetailPageParam(adFormatEnum, request.getCreative());
            if (StringUtils.isNotEmpty(checkMsg)) {
                return ResultUtil.returnErr(checkMsg);
            }
        }
        SbAdsInfoVo.Builder result = SbAdsInfoVo.newBuilder();
        try {
            NewCreateResultResultVo updateResult = cpcSbAdsApiService.updateAdsCreativeNew(shop, profile, adFormatEnum, adName, request, adsInfo);
            if (CollectionUtils.isNotEmpty(updateResult.getErrInfoList())) {
                return ResultUtil.error(JSONObject.toJSONString(updateResult.getErrInfoList()));
            }
            Optional.ofNullable(updateResult.getAdId()).filter(StringUtils::isNotEmpty).ifPresent(result::setAdId);
            return ResultUtil.success(result.build());
        } catch (Exception e) {
            log.info("update creative error", e);
            return ResultUtil.error(JSONObject.toJSONString(e.getMessage()));
        }
    }

    private NewCreateInfoResponse createAdFormat(NewCreateCampaignRequest request, Integer adFormat) {
        NewCreateInfoResponse.Builder responseInfo = NewCreateInfoResponse.newBuilder();
        int shopId = request.getShopId();
        int puid = request.getPuid();
        responseInfo.setShopId(shopId);
        //校验店铺权限
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            responseInfo.setCommonErrMsg("没有CPC授权");
            return responseInfo.build();
        }
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            responseInfo.setCommonErrMsg("没有站点对应的配置信息");
            return responseInfo.build();
        }
        //check campaignId
        AmazonAdCampaignAll campaignInfo = amazonSbAdCampaignDao.getSbAsinByCampaignId(puid, shop.getId(), request.getCampaignId());
        if (Objects.isNull(campaignInfo)) {
            CampaignResponse.Builder campaignResBuilder = CampaignResponse.newBuilder();
            campaignResBuilder.setCampaignErrMsg("广告活动不存在");
            responseInfo.setCampaignResponse(campaignResBuilder.build());
            return responseInfo.build();
        }
        //check adGroupId
        AmazonSbAdGroup groupInfo = amazonSbAdGroupDao.getByGroupId(puid, shop.getId(), request.getAdGroupId());
        if (Objects.isNull(groupInfo)) {
            GroupResponse.Builder groupResBuilder = GroupResponse.newBuilder();
            groupResBuilder.setGroupErrMsg("广告组不存在");
            responseInfo.setGroupResponse(groupResBuilder.build());
            return responseInfo.build();
        }

        //创建广告格式
        AdsResponse.Builder adsBuilder = AdsResponse.newBuilder();
        try {
            SbAdVo sbAdVo = buildAdsVo(request);
            Optional.of(adFormat).map(SBAdFormatEnum::getSBAdFormatEnumByCode)
                    .map(SBAdFormatEnum::getValue).ifPresent(sbAdVo::setAdFormat);
            sbAdVo.setAdGroupId(request.getAdGroupId());
            NewCreateResultResultVo adsResult = cpcSbAdsApiService.createSbAdNew(shop, profile, sbAdVo);
            adsBuilder.setAdId(adsResult.getAdId());
            responseInfo.setAdsResponse(adsBuilder.build());
        } catch (Exception e) {
            adsBuilder.setAdErrMsg(e.getMessage());
            responseInfo.setAdsResponse(adsBuilder.build());
            return responseInfo.build();
        }
        return responseInfo.build();
    }

    private SbAdVo buildAdsVo(NewCreateCampaignRequest request) {
        SbAdVo sbAdVo = new SbAdVo();
        sbAdVo.setName(request.getAdsName());
        sbAdVo.setState(request.getAdsState());
        sbAdVo.setLandingPage(request.getLandingPageVo());
        sbAdVo.setCreative(request.getCreativeVo());
        return sbAdVo;
    }

    private String checkStoreParam(SBAdFormatEnum adFormat, com.meiyunji.sponsored.rpc.sb.ads.SbAdsCreativeInfoVo creativeInfo) {
        //着陆页为亚马逊旗舰店
        //需要建议品牌logo,品牌名称，标题，自定义图片
        Assert.notNull(adFormat, "对应广告格式不能为空");
        if (adFormat == SBAdFormatEnum.PRODUCT_COLLECTION && !CheckParamUtil.checkRequired(creativeInfo, false, "brandName, brandLogoAssetId, customImages, headline")) {
            log.error("creatives param is null:{}, adFormat:{}", creativeInfo, adFormat);
            return "广告创意参数不能为空";
        }
        if (adFormat == SBAdFormatEnum.PRODUCT_COLLECTION && CollectionUtils.isEmpty(creativeInfo.getCustomImagesList())) {
            log.error("creatives param custom image list is empty:{}, adFormat:{}", creativeInfo, adFormat);
            return "自定义图片不能为空";
        }
        if (adFormat == SBAdFormatEnum.STORE_SPOTLIGHT && !CheckParamUtil.checkRequired(creativeInfo, false, "brandName, brandLogoAssetId, headline")) {
            log.error("creatives param is null:{}, adFormat:{}", creativeInfo, adFormat);
            return "广告创意参数不能为空";
        }
        if (adFormat == SBAdFormatEnum.VIDEO && !CheckParamUtil.checkRequired(creativeInfo, false, "brandName, brandLogoAssetId, headline, videoAssetIds")) {
            log.error("creatives param is null:{}, adFormat:{}", creativeInfo, adFormat);
            return "广告创意参数不能为空";
        }
        return "";
    }

    private String checkProductListParam(SBAdFormatEnum adFormat, com.meiyunji.sponsored.rpc.sb.ads.SbAdsCreativeInfoVo creativeInfo) {
        //着陆页为亚马逊旗舰店
        //需要建议品牌logo,品牌名称，标题，自定义图片
        Assert.notNull(adFormat, "对应广告格式不能为空");
        if (adFormat == SBAdFormatEnum.PRODUCT_COLLECTION && !CheckParamUtil.checkRequired(creativeInfo, false, "brandName, brandLogoAssetId, customImages, headline")) {
            log.error("creatives param is null:{}, adFormat:{}", creativeInfo, adFormat);
            return "广告创意参数不能为空";
        }
        if (adFormat == SBAdFormatEnum.PRODUCT_COLLECTION && CollectionUtils.isEmpty(creativeInfo.getCustomImagesList())) {
            log.error("creatives param custom image list is empty:{}, adFormat:{}", creativeInfo, adFormat);
            return "自定义图片不能为空";
        }
        return "";
    }

    private String checkDetailPageParam(SBAdFormatEnum adFormat, com.meiyunji.sponsored.rpc.sb.ads.SbAdsCreativeInfoVo creativeInfo) {
        //着陆页为亚马逊旗舰店
        //需要建议品牌logo,品牌名称，标题，自定义图片
        Assert.notNull(adFormat, "对应广告格式不能为空");
        if (adFormat == SBAdFormatEnum.VIDEO && !CheckParamUtil.checkRequired(creativeInfo, false, "videoAssetIds")) {
            log.error("creatives param is null:{}, adFormat:{}", creativeInfo, adFormat);
            return "广告创意参数不能为空";
        }
        return "";
    }
}
