package com.meiyunji.sponsored.service.cpc.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.amazon.advertising.mode.Adjustment;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.CampaignStrategy;
import com.meiyunji.sellfox.aadas.types.schedule.CampaignAdjustment;
import com.meiyunji.sellfox.aadras.types.enumeration.AmazonAdvertiseType;
import com.meiyunji.sellfox.aadras.types.schedule.AdvertiseRuleTaskExecuteRecordV2Message;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.LogicType;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.adCampaign.enums.AdCampaignDefaultOrderEnum;
import com.meiyunji.sponsored.service.autoRule.vo.AdCampaignAutoRuleParam;
import com.meiyunji.sponsored.service.autoRule.vo.AllUpdateAutoRuleParam;
import com.meiyunji.sponsored.service.autoRule.vo.AutoRuleCampaignVo;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdCampaignStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dto.CampaignTypeDto;
import com.meiyunji.sponsored.service.cpc.vo.MultiShopCampaignListParam;
import com.meiyunji.sponsored.service.cpc.bo.AllCampaignOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdCampaignAllBo;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdCampaignTargetingTypeBO;
import com.meiyunji.sponsored.service.cpc.bo.FeedTargetCampaignDto;
import com.meiyunji.sponsored.service.autoRule.vo.CampaignIdWithAdTypeVo;
import com.meiyunji.sponsored.service.cpc.bo.*;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaign;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.pricing.ScheduleTaskFinishedVo;
import com.meiyunji.sponsored.service.enums.CampaignStrategyV3;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.export.dto.DownloadCenterCampaignBaseDataBO;
import com.meiyunji.sponsored.service.gps.vo.GpsCampaign;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.CampaignViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.DiagnoseCountParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.PlacementViewParam;
import com.meiyunji.sponsored.service.strategy.enums.StartStopItemTypeEnum;
import com.meiyunji.sponsored.service.strategy.vo.AdCampaignStrategyParam;
import com.meiyunji.sponsored.service.strategy.vo.AdSpaceStrategyParam;
import com.meiyunji.sponsored.service.strategy.vo.ControlledObjectParam;
import com.meiyunji.sponsored.service.strategyTask.vo.ProcessTaskParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.SingleColumnRowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * AmazonAdCampaign
 *
 * <AUTHOR>
 */
@Repository
public class AmazonAdCampaignAllDaoImpl extends BaseShardingDaoImpl<AmazonAdCampaignAll> implements IAmazonAdCampaignAllDao {
    @Autowired
    private RedisService redisService;

    @Override
    public void insertOnDuplicateKeyUpdateSp(Integer puid, List<AmazonAdCampaignAll> amazonAdCampaignList, boolean syncHistory) {
        long startTime = System.currentTimeMillis();
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_ad_campaign_all` (`puid`,`shop_id`,`campaign_id`,`profile_id`,")
                .append("`marketplace_id`,`type`,`portfolio_id`,`name`,`campaign_type`,`targeting_type`,`ad_target_type`,`state`,`budget`,`start_date`,`end_date`,")
                .append("`strategy`,`adjustments`,`serving_status`,`creation_date`,");
        if (syncHistory) {
            sql.append("`out_of_budget_time`,");
            sql.append("`sync_out_of_budget_time_state`,");
        }
        sql.append("`last_updated_date`,`create_in_amzup`,`create_time`,`update_time` ) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdCampaignAll amazonAdCampaign : amazonAdCampaignList) {
            sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?");
            if (syncHistory) {
                sql.append(",?");
                sql.append(",?");
            }
            sql.append(",now(),now()),");
            argsList.add(puid);
            argsList.add(amazonAdCampaign.getShopId());
            argsList.add(amazonAdCampaign.getCampaignId());
            argsList.add(amazonAdCampaign.getProfileId());
            argsList.add(amazonAdCampaign.getMarketplaceId());
            argsList.add(Constants.SP);
            argsList.add(amazonAdCampaign.getPortfolioId());
            argsList.add(amazonAdCampaign.getName());
            argsList.add(amazonAdCampaign.getCampaignType());
            argsList.add(amazonAdCampaign.getTargetingType());
            //sp ad_target_type 用TargetingType
            argsList.add(amazonAdCampaign.getTargetingType());
            argsList.add(amazonAdCampaign.getState());
            argsList.add(amazonAdCampaign.getBudget());
            argsList.add(amazonAdCampaign.getStartDate());
            argsList.add(amazonAdCampaign.getEndDate());
            argsList.add(amazonAdCampaign.getStrategy());
            argsList.add(amazonAdCampaign.getAdjustments());
            argsList.add(amazonAdCampaign.getServingStatus());
            argsList.add(amazonAdCampaign.getCreationDate());
            if (syncHistory) {
                argsList.add(amazonAdCampaign.getOutOfBudgetTime());
                argsList.add(amazonAdCampaign.getSyncOutOfBudgetTimeState());
            }
            argsList.add(amazonAdCampaign.getLastUpdatedDate());
            argsList.add(amazonAdCampaign.getCreateInAmzup());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `name`=values(name),`campaign_type`=values(campaign_type),`ad_target_type`=values(ad_target_type),")
                .append("`targeting_type`=values(targeting_type),`state`=values(state),`budget`=values(budget),`portfolio_id`=values(portfolio_id),")
                .append("`start_date`=values(start_date),`end_date`=values(end_date),`strategy`=values(strategy),`adjustments`=values(adjustments),")
                .append("`serving_status`=values(serving_status),`creation_date`=values(creation_date),");
        if (syncHistory) {
            sql.append("`out_of_budget_time`=values(out_of_budget_time),");
            sql.append("`sync_out_of_budget_time_state`=values(sync_out_of_budget_time_state),");
        }
        sql.append("`last_updated_date`=values(last_updated_date) ");
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
        logger.info("insertOnDuplicateKeyUpdateSpTime: {}", System.currentTimeMillis() - startTime);
    }


    @Override
    public void batchAddSd(int puid, List<AmazonAdCampaignAll> list, boolean syncHistory) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;
        for (AmazonAdCampaignAll campaign : list) {
            if (campaign.getPuid() == null || campaign.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(campaign.getPuid());
            arg.add(campaign.getShopId());
            arg.add(campaign.getMarketplaceId());
            arg.add(Constants.SD);
            arg.add(campaign.getCampaignId());
            arg.add(campaign.getProfileId());
            arg.add(campaign.getName());
            arg.add(campaign.getTactic());
            //SD target_type 使用 tactic；
            arg.add(campaign.getTactic());
            arg.add(campaign.getState());
            arg.add(campaign.getStartDate());
            arg.add(campaign.getEndDate());
            arg.add(campaign.getCostType());
            arg.add(campaign.getBudget());
            arg.add(campaign.getBudgetType());
            arg.add(campaign.getServingStatus());
            arg.add(campaign.getPortfolioId());
            if (syncHistory) {
                arg.add(campaign.getOutOfBudgetTime());
                arg.add(campaign.getSyncOutOfBudgetTimeState());
            }

            arg.add(campaign.getCreationDate());
            arg.add(campaign.getLastUpdatedDate());
            arg.add(campaign.getCreateInAmzup());
            arg.add(campaign.getCreateId());
            arg.add(campaign.getUpdateId());
            argList.add(arg.toArray());
        }

        StringBuilder sql = new StringBuilder("insert into t_amazon_ad_campaign_all (`puid`,`shop_id`,`marketplace_id`,`type`,`campaign_id`,`profile_id`,`name`,`tactic`,`ad_target_type`,");
        sql.append("`state`,`start_date`,`end_date`,`cost_type`,`budget`,`budget_type`,`serving_status`,`portfolio_id`,");
        if (syncHistory) {
            sql.append("`out_of_budget_time`,");
            sql.append("`sync_out_of_budget_time_state`,");
        }
        sql.append("`creation_date`,`last_updated_date`,`create_in_amzup`,`create_id`,`update_id`,`create_time`,`update_time`) values " +
                "(?,?, ?,?, ?,?, ?,?,?,?,?, ?,?, ?,?, ?,?, ?,?,?, ?,?, ");
        if (syncHistory) {
            sql.append(" ?,");
            sql.append(" ?,");
        }
        sql.append(" now(3), now(3))");

        getJdbcTemplate(puid).batchUpdate(sql.toString(), argList);
    }

    @Override
    public void batchUpdateSd(int puid, List<AmazonAdCampaignAll> list, boolean syncHistory) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;
        for (AmazonAdCampaignAll campaign : list) {
            if (campaign.getPuid() == null || campaign.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(campaign.getName());
            arg.add(campaign.getTactic());
            //SD target_type 使用 tactic；
            arg.add(campaign.getTactic());
            arg.add(campaign.getState());
            arg.add(campaign.getStartDate());
            arg.add(campaign.getEndDate());
            arg.add(campaign.getCostType());
            arg.add(campaign.getBudget());
            arg.add(campaign.getBudgetType());
            arg.add(campaign.getServingStatus());
            arg.add(campaign.getPortfolioId());
            if (syncHistory) {
                arg.add(campaign.getOutOfBudgetTime());
                arg.add(campaign.getSyncOutOfBudgetTimeState());
            }
            arg.add(campaign.getCreationDate());
            arg.add(campaign.getLastUpdatedDate());
            arg.add(campaign.getUpdateId());
            arg.add(campaign.getPuid());
            arg.add(campaign.getShopId());
            arg.add(Constants.SD);
            arg.add(campaign.getCampaignId());
            argList.add(arg.toArray());
        }
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_campaign_all` set `name` =?,`tactic` =?,`ad_target_type`=?,`state`=?,`start_date`=?,`end_date`=?," +
                "`cost_type`=?,`budget`=?,`budget_type`=?,`serving_status`=?,`portfolio_id`= ?,");
        if (syncHistory) {
            sql.append("`out_of_budget_time`=?,");
            sql.append("`sync_out_of_budget_time_state`=?,");
        }
        sql.append("`creation_date`=?,`last_updated_date`=?, update_id=? where `puid` =? and `shop_id`=? and `type` = ? and `campaign_id`=?");

        getJdbcTemplate(puid).batchUpdate(sql.toString(), argList);
    }


    @Override
    public void batchAddSb(int puid, List<AmazonAdCampaignAll> list, boolean syncHistory) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;

        for (AmazonAdCampaignAll campaign : list) {
            if (campaign.getPuid() == null || campaign.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(campaign.getPuid());
            arg.add(campaign.getShopId());
            arg.add(campaign.getMarketplaceId());
            arg.add(Constants.SB);
            arg.add(campaign.getProfileId());
            arg.add(campaign.getName());
            arg.add(campaign.getCampaignId());
            arg.add(campaign.getBudget());
            arg.add(campaign.getBudgetType());
            arg.add(campaign.getStartDate());
            arg.add(campaign.getEndDate());
            arg.add(campaign.getState());
            arg.add(campaign.getServingStatus());
            if (syncHistory) {
                arg.add(campaign.getOutOfBudgetTime());
                arg.add(campaign.getSyncOutOfBudgetTimeState());
            }
            arg.add(campaign.getBrandEntityId());
            arg.add(campaign.getPortfolioId());
            arg.add(campaign.getBidOptimization());
            arg.add(campaign.getBidMultiplier());
            arg.add(campaign.getCreative());
            arg.add(campaign.getLandingPage());
            arg.add(campaign.getCreateInAmzup());
            arg.add(campaign.getRuleBasedBudget());
            arg.add(campaign.getIsMultiAdGroupsEnabled());
            arg.add(campaign.getBidAdjustmentsByShopperSegment());
            arg.add(campaign.getBidAdjustmentsByPlacement());
            arg.add(campaign.getBidOptimizationStrategy());
            arg.add(campaign.getProductLocation());
            arg.add(campaign.getTags());
            arg.add(campaign.getCreateId());
            arg.add(campaign.getUpdateId());
            arg.add(campaign.getAdFormat());
            arg.add(StringUtils.isNotBlank(campaign.getAdFormat()) && Constants.SB_CAMPAIGN_MANUAL.equals(campaign.getAdFormat()) ? Constants.SB_CAMPAIGN_MANUAL_NEW : campaign.getAdFormat());
            arg.add(campaign.getAdGoal());
            arg.add(campaign.getCostType());
            argList.add(arg.toArray());
        }

        StringBuilder sql = new StringBuilder();
        sql.append("insert into t_amazon_ad_campaign_all (`puid`,`shop_id`,`marketplace_id`,`type`,`profile_id`,`name`,`campaign_id`,");
        sql.append("`budget`,`budget_type`,`start_date`,`end_date`,`state`,`serving_status`,");
        if (syncHistory) {
            sql.append("out_of_budget_time,");
            sql.append("sync_out_of_budget_time_state,");
        }
        sql.append("`brand_entity_id`,`portfolio_id`,`bid_optimization`,`bid_multiplier`,`creative`,`landing_page`,`create_in_amzup`,");
        sql.append("`rule_based_budget`,`is_multiAdGroups_enabled`,`bid_adjustments_by_shopper_segment`,`bid_adjustments_by_placement`,`bid_optimization_strategy`,`product_location`,`tags`,");
        sql.append("`create_id`,`update_id`,`ad_format`,`ad_target_type`,`ad_goal`,`cost_type`,`create_time`,`update_time`) values (?,?, ?,?, ?,?,?, ?,?, ?,?, ");
        if (syncHistory) {
            sql.append("?,");
            sql.append("?,");
        }
        sql.append("?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?,?,?,?,?, now(), now())");

        getJdbcTemplate(puid).batchUpdate(sql.toString(), argList);
    }

    @Override
    public void batchUpdateSb(int puid, List<AmazonAdCampaignAll> list, boolean syncHistory) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;

        for (AmazonAdCampaignAll campaign : list) {
            if (campaign.getPuid() == null || campaign.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(campaign.getName());
            arg.add(campaign.getBudget());
            arg.add(campaign.getBudgetType());
            arg.add(campaign.getStartDate());
            arg.add(campaign.getEndDate());
            arg.add(campaign.getState());
            arg.add(campaign.getServingStatus());
            if (syncHistory) {
                arg.add(campaign.getOutOfBudgetTime());
                arg.add(campaign.getSyncOutOfBudgetTimeState());
            }
            arg.add(campaign.getRuleBasedBudget());
            arg.add(campaign.getIsMultiAdGroupsEnabled());
            arg.add(campaign.getBidAdjustmentsByShopperSegment());
            arg.add(campaign.getBidAdjustmentsByPlacement());
            arg.add(campaign.getBidOptimizationStrategy());
            arg.add(campaign.getProductLocation());
            arg.add(campaign.getTags());
            arg.add(campaign.getPortfolioId());
            arg.add(campaign.getBidOptimization());
            arg.add(campaign.getBidMultiplier());
            arg.add(campaign.getBrandEntityId());
            arg.add(campaign.getAdGoal());
            arg.add(campaign.getUpdateId());
            arg.add(campaign.getPuid());
            arg.add(campaign.getShopId());
            arg.add(Constants.SB);
            arg.add(campaign.getCampaignId());
            argList.add(arg.toArray());
        }
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_campaign_all` set `name` =?,`budget` =?,`budget_type`=?,`start_date`=?,`end_date`=?,`state`=?,`serving_status`=?,");
        if (syncHistory) {
            sql.append("`out_of_budget_time`=?,");
            sql.append("`sync_out_of_budget_time_state`=?,");
        }
        sql.append("`rule_based_budget`=?,`is_multiAdGroups_enabled`=?,`bid_adjustments_by_shopper_segment`=?,`bid_adjustments_by_placement`=?,`bid_optimization_strategy`=?,`product_location`=?,`tags`=?,");
        sql.append("`portfolio_id`=?,`bid_optimization`=?,`bid_multiplier`=?, `brand_entity_id` = ?,`ad_goal` = ?, update_id=? ");
        sql.append(" where `puid` =? and `shop_id`=? and `type` = ? and `campaign_id`=?");
        getJdbcTemplate(puid).batchUpdate(sql.toString(), argList);
    }

    @Override
    public void batchAddSbV3(int puid, List<AmazonAdCampaignAll> list, boolean syncHistory) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;

        for (AmazonAdCampaignAll campaign : list) {
            if (campaign.getPuid() == null || campaign.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(campaign.getPuid());
            arg.add(campaign.getShopId());
            arg.add(campaign.getMarketplaceId());
            arg.add(Constants.SB);
            arg.add(campaign.getProfileId());
            arg.add(campaign.getName());
            arg.add(campaign.getCampaignId());
            arg.add(campaign.getBudget());
            arg.add(campaign.getBudgetType());
            arg.add(campaign.getStartDate());
            arg.add(campaign.getEndDate());
            arg.add(campaign.getAdFormat());
            //SB target_type 使用adFormat；
            arg.add(campaign.getAdFormat());
            arg.add(campaign.getState());
            arg.add(campaign.getServingStatus());
            if (syncHistory) {
                arg.add(campaign.getOutOfBudgetTime());
                arg.add(campaign.getSyncOutOfBudgetTimeState());
            }
            arg.add(campaign.getBrandEntityId());
            arg.add(campaign.getPortfolioId());
            arg.add(campaign.getBidOptimization());
            arg.add(campaign.getBidMultiplier());
            arg.add(campaign.getCreative());
            arg.add(campaign.getLandingPage());
            arg.add(campaign.getCreateInAmzup());
            arg.add(campaign.getCreateId());
            arg.add(campaign.getUpdateId());
            argList.add(arg.toArray());
        }

        StringBuilder sql = new StringBuilder();
        sql.append("insert into t_amazon_ad_campaign_all (`puid`,`shop_id`,`marketplace_id`,`type`,`profile_id`,`name`,`campaign_id`,");
        sql.append("`budget`,`budget_type`,`start_date`,`end_date`,`ad_format`,`ad_target_type`,`state`,`serving_status`,");
        if (syncHistory) {
            sql.append("out_of_budget_time,");
            sql.append("sync_out_of_budget_time_state,");
        }
        sql.append("`brand_entity_id`,`portfolio_id`,`bid_optimization`,`bid_multiplier`,`creative`,`landing_page`,`create_in_amzup`,`create_id`,");
        sql.append("`update_id`,`create_time`,`update_time`) values (?,?, ?,?, ?,?,?, ?,?, ?,?, ?,?, ");
        if (syncHistory) {
            sql.append("?,");
            sql.append("?,");
        }
        sql.append("?,?, ?,?, ?,?, ?,?, ?,?,?, now(), now())");

        getJdbcTemplate(puid).batchUpdate(sql.toString(), argList);
    }


    @Override
    public void batchUpdateSbV3(int puid, List<AmazonAdCampaignAll> list, boolean syncHistory) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;

        for (AmazonAdCampaignAll campaign : list) {
            if (campaign.getPuid() == null || campaign.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(campaign.getName());
            arg.add(campaign.getBudget());
            arg.add(campaign.getBudgetType());
            arg.add(campaign.getStartDate());
            arg.add(campaign.getEndDate());
            arg.add(campaign.getState());
            arg.add(campaign.getServingStatus());
            if (syncHistory) {
                arg.add(campaign.getOutOfBudgetTime());
                arg.add(campaign.getSyncOutOfBudgetTimeState());
            }
            arg.add(campaign.getPortfolioId());
            arg.add(campaign.getBidOptimization());
            arg.add(campaign.getBidMultiplier());
            arg.add(campaign.getCreative());
            arg.add(campaign.getLandingPage());
            arg.add(campaign.getBrandEntityId());
            arg.add(campaign.getUpdateId());
            arg.add(campaign.getPuid());
            arg.add(campaign.getShopId());
            arg.add(Constants.SB);
            arg.add(campaign.getCampaignId());
            argList.add(arg.toArray());
        }
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_campaign_all` set `name` =?,`budget` =?,`budget_type`=?,`start_date`=?,`end_date`=?,`state`=?,`serving_status`=?,");
        if (syncHistory) {
            sql.append("`out_of_budget_time`=?,");
            sql.append("`sync_out_of_budget_time_state`=?,");
        }
        sql.append("`portfolio_id`=?,`bid_optimization`=?,`bid_multiplier`=?,`creative`=?, `landing_page`=?, `brand_entity_id` = ? ,update_id=?, update_time=now() where `puid` =? and `shop_id`=? and `type` = ? and `campaign_id`=?");
        getJdbcTemplate(puid).batchUpdate(sql.toString(), argList);
    }



    @Override
    public List<AmazonAdCampaignAll> listByCampaignId(int puid, Integer shopId, List<String> campaignIds, String type) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .equalToWithoutCheck("type", type)
                .in("campaign_id", campaignIds.toArray())
                .build();
        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonAdCampaignAll> listByCampaignIdNoShopId(int puid, List<String> campaignIds, String type) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("type", type)
                .in("campaign_id", campaignIds.toArray())
                .build();
        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonAdCampaignAll> listByCampaignIdNoType(int puid,int shopId,List<String> campaignIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .in("campaign_id", campaignIds.toArray())
                .build();
        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonAdCampaignAll> listByCampaignIdNoType(int puid, int shopId, List<String> campaignIds, List<String> portfolioIds) {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.equalToWithoutCheck("puid", puid);
        conditionBuilder.equalToWithoutCheck("shop_id", shopId);
        conditionBuilder.in("campaign_id", campaignIds.toArray());
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            conditionBuilder.and();
            conditionBuilder.leftBracket();
            conditionBuilder.in(LogicType.EPT,"portfolio_id",portfolioIds.toArray());
            if (portfolioIds.contains("-1")) {
                conditionBuilder.or();
                conditionBuilder.isNull(LogicType.EPT,"portfolio_id");
            }
            conditionBuilder.rightBracket();
        }
        return listByCondition(puid, conditionBuilder.build());
    }

    @Override
    public List<AmazonAdCampaignAll> autoRuleCampaign(int puid, int shopId, String campaignName, List<String> portfolioIds, List<String> adTypeList, List<String> campaignIds, String state, List<String> servingStatus) {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.equalTo("puid", puid);
        conditionBuilder.equalTo("shop_id", shopId);
        conditionBuilder.in("campaign_id", campaignIds.toArray());
        conditionBuilder.in("type", adTypeList.toArray());
        if (StringUtils.isNotBlank(state)) {
            conditionBuilder.equalTo("state", state);
        }
        if (StringUtils.isNotBlank(campaignName)) {
            conditionBuilder.like("name", campaignName);
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            conditionBuilder.and();
            conditionBuilder.leftBracket();
            conditionBuilder.in(LogicType.EPT,"portfolio_id", portfolioIds.toArray());
            if (portfolioIds.contains("-1")) {
                conditionBuilder.or();
                conditionBuilder.isNull(LogicType.EPT, "portfolio_id");
            }
            conditionBuilder.rightBracket();
        }
        if (CollectionUtils.isNotEmpty(servingStatus)) {
            List<String> servings = servingStatus.stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(servings)) {
                conditionBuilder.in("serving_status", servings.toArray());
            }
        }
        return listByCondition(puid, conditionBuilder.build());
    }


    @Override
    public List<String> getSpCampaignIdsByTargetType(Integer puid, Integer shopId, String targetingType) {
        String sql = "select campaign_id from t_amazon_ad_campaign_all where puid=? and shop_id=? and type = 'sp' and targeting_type=?";
        return getJdbcTemplate(puid).queryForList(sql, new Object[]{puid, shopId, targetingType}, String.class);
    }

    @Override
    public List<String> getCampaignIdsAndShopIdsByPortfolioId(Integer puid, List<Integer> shopId, String portfolioId, String name) {
        // portfolioIds筛选掉-1的情况
        List<String> portfolioIds = new ArrayList<>();
        if (StringUtils.isNotBlank(portfolioId)) {
            portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                    .filter(item -> !item.equals("-1")).collect(Collectors.toList());
        }
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.inIntList("shop_id", shopId.toArray(new Integer[0]));
        // 选择‘未分类’：没有广告组合的广告活动
        if ("-1".equals(portfolioId)) {
            builder.isNull(LogicType.AND, "portfolio_id");
        } else if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioId.contains("-1")) {
                builder.and().leftBracket().in(LogicType.EPT, "portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
                builder.isNull(LogicType.OR, "portfolio_id").rightBracket();
            } else {
                builder.inStrList("portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
            }
        }
        if (StringUtils.isNotBlank(name)) {
            builder.like("name", name);
        }
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where " + builder.build().getSql());
        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, builder.build().getValues());
    }

    @Override
    public List<CampaignTypeDto> getCampaignIdsAndTypeByPortfolioId(Integer puid, List<Integer> shopIds, String portfolioId, String searchVal) {
        // portfolioIds筛选掉-1的情况
        List<String> portfolioIds = new ArrayList<>();
        if (StringUtils.isNotBlank(portfolioId)) {
            portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                    .filter(item -> !item.equals("-1")).collect(Collectors.toList());
        }
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.inIntList("shop_id", shopIds.toArray(new Integer[0]));
        // 选择‘未分类’：没有广告组合的广告活动
        if ("-1".equals(portfolioId)) {
            builder.isNull(LogicType.AND, "portfolio_id");
        } else if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioId.contains("-1")) {
                builder.and().leftBracket().in(LogicType.EPT, "portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
                builder.isNull(LogicType.OR, "portfolio_id").rightBracket();
            } else {
                builder.inStrList("portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
            }
        }
        if (StringUtils.isNotBlank(searchVal)) {
            builder.like("name", searchVal);
        }
        StringBuilder sql = new StringBuilder("select campaign_id, type from t_amazon_ad_campaign_all where " + builder.build().getSql());
        return getJdbcTemplate(puid).query(sql.toString(), new ObjectMapper<>(CampaignTypeDto.class), builder.build().getValues());
    }

    @Override
    public List<String> getCampaignIdListByPortfolioIdAndStrategyType(Integer puid, Integer shopId, List<String> campaignIdList, com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam param) {
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where puid= ? and shop_id= ?");
        List<Object> objects = Lists.newArrayList(puid, shopId);
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            sql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, objects));
        }
        if (StringUtils.isNotBlank(param.getStrategyType())) {
            sql.append(" and strategy = ? ");
            objects.add(param.getStrategyType());
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), objects.toArray(), String.class);
    }

    @Override
    public List<DownloadCenterCampaignBaseDataBO> queryBaseData4DownloadByCampaignIdList(Integer puid, Integer shopId, List<String> campaignIdList) {
        StringBuilder sql = new StringBuilder("select campaign_id id, targeting_type campaignTargetingType, start_date campaignStartDate, end_date campaignEndDate, state, portfolio_id portfolioId from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? ");
        sql.append(" and shop_id = ? ");
        sql.append(" and campaign_id in ('").append(StringUtils.join(campaignIdList, "','")).append("')");
        return getJdbcTemplate(puid).query(sql.toString(), new BeanPropertyRowMapper<>(DownloadCenterCampaignBaseDataBO.class), puid, shopId);
    }

    @Override
    public List<AutoRuleCampaignVo> getCampaignDataByCampaignId(int puid, int shopId, ArrayList<String> campaignIds) {
        StringBuilder sql = new StringBuilder("select id, puid, shop_id shopId, marketplace_id marketplaceId, campaign_id campaignId, type, name campaignName, state  from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? ");
        sql.append(" and shop_id = ? ");
        sql.append(" and campaign_id in ('").append(StringUtils.join(campaignIds, "','")).append("')");
        return getJdbcTemplate(puid).query(sql.toString(), new BeanPropertyRowMapper<>(AutoRuleCampaignVo.class), puid, shopId);
    }

    @Override
    public List<CampaignTypeDto> getCampaignIdsTypeByPortfolioId(Integer puid, List<Integer> shopId, String portfolioId, String campaignId, String type, String state, String servingStatus) {
        // portfolioIds筛选掉-1的情况
        List<String> portfolioIds = new ArrayList<>();
        if (StringUtils.isNotBlank(portfolioId)) {
            portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                    .filter(item -> !item.equals("-1")).collect(Collectors.toList());
        }
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.inIntList("shop_id", shopId.toArray(new Integer[0]));
//        builder.equalTo("shop_id", shopId);
        if (StringUtils.isNotBlank(campaignId)) {
            builder.in("campaign_id", StringUtil.splitStr(campaignId).toArray());
        }

        if (StringUtils.isNotBlank(type)) {
            builder.in("type", StringUtil.splitStr(type).toArray());
        }

        if (StringUtils.isNotBlank(state)) {
            List<String> stateList = Arrays.asList(state.split(","));
            if (stateList.size() > 0) {
                builder.in("state", stateList.toArray());
            }
        }

        if (StringUtils.isNotBlank(servingStatus)) {
            List<String> servingStatusList = Arrays.asList(servingStatus.split(","));
            if (servingStatusList.size() > 0) {
                List<String> statusList = getServingStatus(servingStatusList);
                if (CollectionUtils.isNotEmpty(statusList)) {
                    builder.in("serving_status", statusList.toArray());
                }
            }
        }
        // 选择‘未分类’：没有广告组合的广告活动
        if ("-1".equals(portfolioId)) {
            builder.isNull(LogicType.AND, "portfolio_id");
        } else if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioId.contains("-1")) {
                builder.and().leftBracket().in(LogicType.EPT, "portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
                builder.isNull(LogicType.OR, "portfolio_id").rightBracket();
            } else {
                builder.inStrList("portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
            }
        }
        StringBuilder sql = new StringBuilder("select campaign_id,type from t_amazon_ad_campaign_all where " + builder.build().getSql());
        return getJdbcTemplate(puid).query(sql.toString(), new ObjectMapper<>(CampaignTypeDto.class), builder.build().getValues());
    }

    /**
     * 过滤服务状态
     * @param servingStatusList
     * @return
     */
    private List<String> getServingStatus(List<String> servingStatusList) {
        List<String> statusList = new ArrayList<>();
        for (String servingStatusKey : servingStatusList) {
            List<String> servingStatusValueList = Constants.SERVER_STATUS_SELECT.get(servingStatusKey);
            if (servingStatusValueList != null) {
                statusList.addAll(servingStatusValueList);
            }
        }
        return statusList;
    }

    /**
     * type 为空查所有类型
     *
     * @param puid
     * @param shopId
     * @param portfolioId
     * @param type
     * @return
     */
    @Override
    public List<String> getCampaignIdsByPortfolioId(Integer puid, Integer shopId, String portfolioId, String type, String state, String servingStatus) {
        // portfolioIds筛选掉-1的情况
        List<String> portfolioIds = new ArrayList<>();
        if (StringUtils.isNotBlank(portfolioId)) {
            portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                    .filter(item -> !item.equals("-1")).collect(Collectors.toList());
        }
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        if (StringUtils.isNotBlank(state)) {
            List<String> stateList = Arrays.asList(state.split(","));
            if (stateList.size() > 0) {
                builder.in("state", stateList.toArray());
            }
        }
        if (StringUtils.isNotBlank(servingStatus)) {
            List<String> servingStatusList = Arrays.asList(servingStatus.split(","));
            if (servingStatusList.size() > 0) {
                List<String> statusList = getServingStatus(servingStatusList);
                if (CollectionUtils.isNotEmpty(statusList)) {
                    builder.in("serving_status", statusList.toArray());
                }
            }
        }
        builder.equalTo("puid", puid);
        builder.equalTo("shop_id", shopId);
        // 选择‘未分类’：没有广告组合的广告活动
        if ("-1".equals(portfolioId)) {
            builder.isNull(LogicType.AND, "portfolio_id");
        } else if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioId.contains("-1")) {
                builder.and().leftBracket().in(LogicType.EPT, "portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
                builder.isNull(LogicType.OR, "portfolio_id").rightBracket();
            } else {
                builder.inStrList("portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
            }
        }
        if (StringUtils.isNotBlank(type)) {
            builder.in("type", StringUtil.splitStr(type).toArray());
//            builder.equalTo("type", type);
        }
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where " + builder.build().getSql());

        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, builder.build().getValues());
    }

    @Override
    public List<String> getCampaignIdsByPortfolioIdAndShopIds(Integer puid, List<Integer> shopId, String portfolioId, String type, String state, String servingStatus) {
        // portfolioIds筛选掉-1的情况
        List<String> portfolioIds = new ArrayList<>();
        if (StringUtils.isNotBlank(portfolioId)) {
            portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                    .filter(item -> !item.equals("-1")).collect(Collectors.toList());
        }
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        if (StringUtils.isNotBlank(state)) {
            List<String> stateList = Arrays.asList(state.split(","));
            if (stateList.size() > 0) {
                builder.in("state", stateList.toArray());
            }
        }
        if (StringUtils.isNotBlank(servingStatus)) {
            List<String> servingStatusList = Arrays.asList(servingStatus.split(","));
            if (servingStatusList.size() > 0) {
                List<String> statusList = getServingStatus(servingStatusList);
                if (CollectionUtils.isNotEmpty(statusList)) {
                    builder.in("serving_status", statusList.toArray());
                }
            }
        }
        builder.equalTo("puid", puid);
        builder.in("shop_id", shopId.toArray());
        // 选择‘未分类’：没有广告组合的广告活动
        if ("-1".equals(portfolioId)) {
            builder.isNull(LogicType.AND, "portfolio_id");
        } else if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioId.contains("-1")) {
                builder.and().leftBracket().in(LogicType.EPT, "portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
                builder.isNull(LogicType.OR, "portfolio_id").rightBracket();
            } else {
                builder.inStrList("portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
            }
        }
        if (StringUtils.isNotBlank(type)) {
            builder.in("type", StringUtil.splitStr(type).toArray());
//            builder.equalTo("type", type);
        }
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where " + builder.build().getSql());

        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, builder.build().getValues());
    }

    @Override
    public List<String> getCampaignIdsByPortfolioId(Integer puid, Integer shopId, List<String> portfolioIds, List<String> types) {
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where puid=? and shop_id=? and state in ('enabled','paused')");
        List<Object> objects = Lists.newArrayList(puid, shopId);
        if (CollectionUtils.isNotEmpty(types)) {
            sql.append(SqlStringUtil.dealInList("type",types,objects));
        } else {
            sql.append(" and type in ('sp','sb','sd')");
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioIds.size() == 1 && portfolioIds.contains("-1")) {
                sql.append(" and portfolio_id is null ");
            } else if (portfolioIds.contains("-1")) {
                sql.append(" and ( ").append(SqlStringUtil.dealInListNotAnd("portfolio_id", portfolioIds, objects))
                        .append(" or portfolio_id is null ) ");
            } else {
                sql.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, objects));
            }
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), objects.toArray(), String.class);
    }

    @Override
    public List<String> getCampaignIdsAllByPortfolioId(Integer puid, Integer shopId, List<String> portfolioIds, List<String> types) {
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where puid=? and shop_id=? ");
        List<Object> objects = Lists.newArrayList(puid, shopId);
        if (CollectionUtils.isNotEmpty(types)) {
            sql.append(SqlStringUtil.dealInList("type",types,objects));
        } else {
            sql.append(" and type in ('sp','sb','sd')");
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioIds.size() == 1 && portfolioIds.contains("-1")) {
                sql.append(" and portfolio_id is null ");
            } else if (portfolioIds.contains("-1")) {
                sql.append(" and ( ").append(SqlStringUtil.dealInListNotAnd("portfolio_id", portfolioIds, objects))
                        .append(" or portfolio_id is null ) ");
            } else {
                sql.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, objects));
            }
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), objects.toArray(), String.class);
    }

    @Override
    public List<String> getCampaignIdByShopIdsPortfolioIds(Integer puid, List<Integer> shopIds, List<String> portfolioIds, List<String> types) {
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where puid= ? ");
        List<Object> objects = Lists.newArrayList(puid);

        if (CollectionUtils.isNotEmpty(types)) {
            sql.append(SqlStringUtil.dealInList("type", types, objects));
        } else {
            sql.append(" and type in ('sp','sd')");
        }

        if (CollectionUtils.isNotEmpty(shopIds)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, objects));
        }

        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioIds.size() == 1 && portfolioIds.contains("-1")) {
                sql.append(" and portfolio_id is null ");
            } else if (portfolioIds.contains("-1")) {
                sql.append(" and ( ").append(SqlStringUtil.dealInListNotAnd("portfolio_id", portfolioIds, objects))
                        .append(" or portfolio_id is null ) ");
            } else {
                sql.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, objects));
            }
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), objects.toArray(), String.class);
    }

    @Override
    public List<String> getCampaignIdByPortfolioIdCampaignId(Integer puid, String marketplaceId, List<Integer> shopIds, List<String> portfolioIds, List<String> campaignIds) {
        List<Object> args = Lists.newArrayList();

        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where puid=? and marketplace_id=? ");
        args.add(puid);
        args.add(marketplaceId);

        if (CollectionUtils.isNotEmpty(shopIds)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        }

        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioIds.size() == 1 && portfolioIds.contains("-1")) {
                sql.append(" and portfolio_id is null ");
            } else if (portfolioIds.contains("-1")) {
                sql.append(" and ( ").append(SqlStringUtil.dealInListNotAnd("portfolio_id", portfolioIds, args))
                        .append(" or portfolio_id is null ) ");
            } else {
                sql.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, args));
            }
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, args));
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), args.toArray(), String.class);
    }

    @Override
    public List<String> getCampaignIdListByPortfolioId(Integer puid, Integer shopId, List<String> portfolioIds, String type) {
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where puid= ? and shop_id= ? and type = ? ");
        List<Object> objects = Lists.newArrayList(puid, shopId, type);
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioIds.size() == 1 && portfolioIds.contains("-1")) {
                sql.append(" and portfolio_id is null ");
            } else if (portfolioIds.contains("-1")) {
                sql.append(" and ( ").append(SqlStringUtil.dealInListNotAnd("portfolio_id", portfolioIds, objects))
                        .append(" or portfolio_id is null ) ");
            } else {
                sql.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, objects));
            }
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), objects.toArray(), String.class);
    }

    @Override
    public List<String> getCampaignIdListByPortfolioIdNoType(Integer puid, Integer shopId, List<String> portfolioIds) {
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where puid= ? and shop_id= ?");
        List<Object> objects = Lists.newArrayList(puid, shopId);
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioIds.size() == 1 && portfolioIds.contains("-1")) {
                sql.append(" and portfolio_id is null ");
            } else if (portfolioIds.contains("-1")) {
                sql.append(" and ( ").append(SqlStringUtil.dealInListNotAnd("portfolio_id", portfolioIds, objects))
                        .append(" or portfolio_id is null ) ");
            } else {
                sql.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, objects));
            }
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), objects.toArray(), String.class);
    }

    @Override
    public List<String> getCampaignIdsByPortfolioIdList(Integer puid, Integer shopId, List<String> portfolioIds) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("shop_id", shopId);
        if(CollectionUtils.isNotEmpty(portfolioIds)){
            builder.and();
            builder.leftBracket();
            builder.in(LogicType.EPT,"portfolio_id",portfolioIds.toArray());
            if (portfolioIds.contains("-1")) {
                builder.or();
                builder.isNull(LogicType.EPT,"portfolio_id");
            }
            builder.rightBracket();
        }
        return listDistinctFieldByCondition(puid, "campaign_id", builder.build(), String.class);
    }

    @Override
    public List<String> getCampaignIdsByPortfolioIdList(Integer puid, List<Integer> shopIds, List<String> portfolioIds) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            List<String> list = shopIds.stream().map(String::valueOf).collect(Collectors.toList());
            builder.inStrList("shop_id", list.toArray(new String[list.size()]));
        }
        if(CollectionUtils.isNotEmpty(portfolioIds)){
            builder.and();
            builder.leftBracket();
            builder.in(LogicType.EPT,"portfolio_id",portfolioIds.toArray());
            if (portfolioIds.contains("-1")) {
                builder.or();
                builder.isNull(LogicType.EPT,"portfolio_id");
            }
            builder.rightBracket();
        }
        return listDistinctFieldByCondition(puid, "campaign_id", builder.build(), String.class);
    }


    /**
     * type 为空查所有类型
     *
     * @param puid
     * @param shopId
     * @param portfolioId
     * @param type
     * @return
     */
    @Override
    public List<String> getCampaignIdsByPortfolioId(Integer puid, Integer shopId, String portfolioId, String type,String campaignIds) {
        // portfolioIds筛选掉-1的情况
        List<String> portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                .filter(item -> !item.equals("-1")).collect(Collectors.toList());

        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("shop_id", shopId);
        if(StringUtils.isNotBlank(campaignIds)){
            List<String> list = StringUtil.splitStr(campaignIds);
            builder.inStrList("campaign_id",list.toArray(new String[]{}));
        }
        // 选择‘未分类’：没有广告组合的广告活动
        if ("-1".equals(portfolioId)) {
            builder.isNull(LogicType.AND, "portfolio_id");
        } else if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioId.indexOf("-1") >= 0) {
                builder.and().leftBracket().in(LogicType.EPT, "portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
                builder.isNull(LogicType.OR, "portfolio_id").rightBracket();
            } else {
                builder.inStrList("portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
            }
        }
        if (StringUtils.isNotBlank(type)) {
            builder.equalTo("type", type);
        }
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where " + builder.build().getSql());

        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, builder.build().getValues());
    }


    /**
     * 多站点获取
     */
    @Override
    public List<String> getCampaignIdsByPortfolioId(Integer puid, String shopId, String portfolioId, String type, String campaignIds) {
        // portfolioIds筛选掉-1的情况
        List<String> portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                .filter(item -> !item.equals("-1")).collect(Collectors.toList());

        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        if (StringUtils.isNotBlank(shopId)) {
            List<String> list = StringUtil.splitStr(shopId);
            builder.inStrList("shop_id", list.toArray(new String[]{}));
        }
        if (StringUtils.isNotBlank(campaignIds)) {
            List<String> list = StringUtil.splitStr(campaignIds);
            builder.inStrList("campaign_id", list.toArray(new String[]{}));
        }
        // 选择‘未分类’：没有广告组合的广告活动
        if ("-1".equals(portfolioId)) {
            builder.isNull(LogicType.AND, "portfolio_id");
        } else if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioId.indexOf("-1") >= 0) {
                builder.and().leftBracket().in(LogicType.EPT, "portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
                builder.isNull(LogicType.OR, "portfolio_id").rightBracket();
            } else {
                builder.inStrList("portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
            }
        }
        if (StringUtils.isNotBlank(type)) {
            builder.equalTo("type", type);
        }
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where " + builder.build().getSql());

        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, builder.build().getValues());
    }

    @Override
    public List<AmazonAdCampaignAll> getCampaignsByPortfolioIdList(Integer puid, Integer shopId, List<String> portfolioIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("portfolio_id", portfolioIds.toArray(new String[]{}))
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonAdCampaignAll> getCampaignsByPortfolioIdList(AllUpdateAutoRuleParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", param.getPuid());
        builder.equalTo("shop_id", param.getShopId());
        if (StringUtils.isNotBlank(param.getState())) {
            builder.equalTo("state",param.getState());
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            builder.like("name",param.getSearchValue());
        }
        if (CollectionUtils.isNotEmpty(param.getPortfolioIds())) {
            builder.inStrList("portfolio_id", param.getPortfolioIds().toArray(new String[]{}));
        }
        return listByCondition(param.getPuid(), builder.build());
    }

    @Override
    public List<AmazonAdCampaignAll> getCampaignsByPortfolioIdList(Integer puid, Integer shopId, List<String> portfolioIds, String campaignName) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("shop_id", shopId);
        if (StringUtils.isNotBlank(campaignName)) {
            builder.equalTo("name", campaignName);
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            builder.inStrList("portfolio_id", portfolioIds.toArray(new String[]{}));
        }
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdCampaignAll> getList(Integer puid, CampaignPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId());
        if (StringUtils.isNotBlank(param.getType())) {
            builder.in("type", StringUtil.splitStr(param.getType()).toArray());
        }

        if (StringUtils.isNotBlank(param.getPortfolioId()) && param.getPortfolioId().equals("-1")) {
            builder.isNull("portfolio_id");
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if ("name".equals(param.getSearchField())) {
                builder.like(param.getSearchField(), param.getSearchValue());
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            builder.inStrList("state", statusList.toArray(new String[]{}));
        }

        //服务状态筛选
        if(StringUtils.isNotBlank(param.getServingStatus())){
            List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
            List<String> servings = list.stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).flatMap(Collection::stream).collect(Collectors.toList());
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(servings)){
                builder.inStrList("serving_status",servings.toArray(new String[]{}));
            }
        }
        if (StringUtils.isNotBlank(param.getStrategyType())) {
            builder.equalTo("strategy", param.getStrategyType());
        }


        if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.budgetExceeded.getCode())) {
            builder.equalTo("serving_status", AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
        } else if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.underBudget.getCode())) {
            builder.notEqualTo("serving_status", AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {  //广告组合查询
            builder.inStrList("campaign_id", param.getCampaignIdList().toArray(new String[]{}));
        }

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告组合查询
            builder.equalTo("campaign_id", param.getCampaignId());
        }
        // 广告策略筛选
        if(CollectionUtil.isNotEmpty(param.getAdStrategyTypeList())) {
            List<Object> argsList = new ArrayList<>();
            String sql = AdCampaignStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(), argsList, "campaign_id");
            if(StringUtils.isNotEmpty(sql)){
                builder.custom(sql, argsList.toArray(), true);
            }
        }
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (StringUtils.isNotBlank(param.getFilterTargetType())) {  //活动类型  auto自动投放,manual手动投放
                List<String> typeList = StringUtil.splitStr(param.getFilterTargetType(), ",");
                if (CollectionUtils.isNotEmpty(param.getCreativeIds())) {
                    List<Object> argsList = new ArrayList<>();
                    StringBuilder whereSql = new StringBuilder();
                    whereSql.append(" and (");
                    whereSql.append(SqlStringUtil.dealInListNotAnd("ad_target_type", typeList, argsList));
                    whereSql.append(" or ");
                    whereSql.append(SqlStringUtil.dealInListNotAnd("campaign_id", param.getCreativeIds(), argsList));
                    whereSql.append(" ) ");
                    builder.custom(whereSql.toString(), argsList.toArray(), true);
                } else {
                    builder.inStrList("ad_target_type", typeList.toArray(new String[]{}));
                }

            }
            if (param.getDailyBudgetMin() != null) {   //每日预算
                builder.greaterThanOrEqualTo("budget", param.getDailyBudgetMin());
            }
            if (StringUtils.isNotBlank(param.getCostType())) {
                builder.equalTo("cost_type", param.getCostType());
            }
            if (param.getDailyBudgetMax() != null) {
                builder.lessThanOrEqualTo("budget", param.getDailyBudgetMax());
            }
            if (StringUtils.isNotBlank(param.getFilterStartDate())) {  // 日期
                builder.greaterThanOrEqualTo("start_date", param.getFilterStartDate());
            }
            if (StringUtils.isNotBlank(param.getFilterEndDate())) {
                builder.lessThanOrEqualTo("end_date", param.getFilterEndDate());
            }
        }

        builder.orderByDesc("data_update_time", "id");

        builder.limit(Constants.TOTALSIZELIMIT);  // 限制10万

        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdCampaignAll> getCampaignViewList(Integer puid, CampaignViewParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", param.getShopIdList().toArray())
                .equalTo("type", param.getType());
        //通过广告产品的活动id过滤
        if (CollectionUtils.isNotEmpty(param.getAdCamgaignIdList())) {
            builder.in("campaign_id", param.getAdCamgaignIdList().toArray());
        }

        //运行状态
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            builder.inStrList("state", statusList.toArray(new String[]{}));
        }

        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
            List<String> servings = list.stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).flatMap(Collection::stream).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(servings)) {
                builder.inStrList("serving_status", servings.toArray(new String[]{}));
            }
        }

        //竞价策略
        if (StringUtils.isNotBlank(param.getStrategyType())) {
            List<String> strategyTypeList = StringUtil.splitStr(param.getStrategyType(), ",");
            builder.inStrList("strategy", strategyTypeList.toArray(new String[]{}));
        }


        if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.budgetExceeded.getCode())) {
            builder.equalTo("serving_status", AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
        } else if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.underBudget.getCode())) {
            builder.notEqualTo("serving_status", AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
        }

        //广告活动名称
        if (StringUtils.isNotBlank(param.getQueryValue())) {
            if (SearchTypeEnum.EXACT.getValue().equals(param.getQueryType())) {
                if (param.getQueryValue().contains(StringUtil.SPECIAL_COMMA)) {
                    builder.in("name", param.getQueryValue().split(StringUtil.SPECIAL_COMMA));
                } else {
                    builder.equalTo("name", param.getQueryValue().trim());
                }
            } else {
                builder.like("name", param.getQueryValue().trim());
            }
        }

        builder.orderByDesc("data_update_time", "id");

        builder.limit(Constants.TOTALSIZELIMIT);  // 限制10万

        return listByCondition(puid, builder.build());
    }

    @Override
    public Page<AmazonAdCampaignAll> getList(Integer puid, Integer shopId, List<String> types, List<String> portfiolioIds,Integer pageSize,Integer pageNo,String campaignName,String targetingType,String campaignId, List<String> statusList) {
        StringBuilder sql = new StringBuilder("SELECT * FROM t_amazon_ad_campaign_all ");
        StringBuilder countSql = new StringBuilder("SELECT count(id) FROM `t_amazon_ad_campaign_all`");
        List<Object> args = new ArrayList<>();
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? ");
        args.add(puid);
        args.add(shopId);
        if (CollectionUtils.isNotEmpty(types)) {
            whereSql.append(SqlStringUtil.dealInList("type", types, args));
        } else {
            whereSql.append(" and type in ('sp','sb', 'sd') ");
        }
        if (CollectionUtils.isNotEmpty(statusList)) {
            whereSql.append(SqlStringUtil.dealInList("state", statusList, args));
        }
        if (StringUtils.isNotBlank(campaignId)) {
            whereSql.append(" and campaign_id = ?");
            args.add(campaignId);
        }
        if (CollectionUtils.isNotEmpty(portfiolioIds)) {
            whereSql.append(" and ( ");
            whereSql.append(" portfolio_id in ");
            whereSql.append(" ( "+SqlStringUtil.toInSqlStrs(portfiolioIds)+" ) ");
            if (portfiolioIds.contains("-1")) {
                whereSql.append("or portfolio_id is null ");
            }
            whereSql.append(" ) ");
        }
        if (StringUtils.isNotBlank(campaignName)) {
            whereSql.append(" and name like ? ");
            args.add("%"+campaignName+"%");
        }
        //搜索词添加到投放，指定广告组（不查询自动投放）
        if ("manual".equals(targetingType)) {
            whereSql.append(" and type in ('sp','sb') and (targeting_type != 'auto' or targeting_type is null )");
        }
        sql.append(whereSql);
        sql.append(" ORDER BY CASE state WHEN 'enabled' THEN 1 WHEN 'paused' THEN 2 WHEN null THEN 3 WHEN 'archived' THEN 4 ELSE 5 END ");
        countSql.append(whereSql);
        return this.getPageResult(puid,pageNo,pageSize,
                countSql.toString(),args.toArray(),sql.toString(),args.toArray(),AmazonAdCampaignAll.class);
    }

    @Override
    public List<AmazonAdCampaignAll> getAllCampaignViewList(Integer puid, CampaignViewParam param) {
        List<String> typeList = StringUtil.splitStr(param.getType());
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", param.getShopIdList().toArray())
                .in("type", typeList.toArray());
        //通过广告产品的活动id过滤
        if (CollectionUtils.isNotEmpty(param.getAdCamgaignIdList())) {
            builder.in("campaign_id", param.getAdCamgaignIdList().toArray());
        }

        //运行状态
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            builder.inStrList("state", statusList.toArray(new String[]{}));
        }

        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
            List<String> servings = list.stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).flatMap(Collection::stream).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(servings)) {
                builder.inStrList("serving_status", servings.toArray(new String[]{}));
            }
        }

        //竞价策略
        if (StringUtils.isNotBlank(param.getStrategyType())) {
            List<String> strategyTypeList = StringUtil.splitStr(param.getStrategyType(), ",");
            builder.inStrList("strategy", strategyTypeList.toArray(new String[]{}));
        }


        if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.budgetExceeded.getCode())) {
            builder.equalTo("serving_status", AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
        } else if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.underBudget.getCode())) {
            builder.notEqualTo("serving_status", AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
        }

        //广告活动名称
        if (StringUtils.isNotBlank(param.getQueryValue())) {
            if (SearchTypeEnum.EXACT.getValue().equals(param.getQueryType())) {
                if (param.getQueryValue().contains(StringUtil.SPECIAL_COMMA)) {
                    builder.in("name", param.getQueryValue().split(StringUtil.SPECIAL_COMMA));
                } else {
                    builder.equalTo("name", param.getQueryValue().trim());
                }
            } else {
                builder.like("name", param.getQueryValue().trim());
            }
        }

        builder.orderByDesc("data_update_time", "id");

        builder.limit(Constants.TOTALSIZELIMIT);  // 限制10万

        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdCampaignAll> listCampaignIds(int puid, int shopId, String state, String servingStatus, List<String> campaignIdList) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId);
        if (StringUtils.isNotBlank(state)) {
            List<String> stateList = Arrays.asList(state.split(","));
            if (stateList.size() > 0) {
                builder.in("state", stateList.toArray());
            }
        }
        if (StringUtils.isNotBlank(servingStatus)) {
            List<String> servingStatusList = Arrays.asList(servingStatus.split(","));
            if (servingStatusList.size() > 0) {
                List<String> statusList = getServingStatus(servingStatusList);
                if (CollectionUtils.isNotEmpty(statusList)) {
                    builder.in("serving_status", statusList.toArray());
                }
            }

        }
        if (campaignIdList != null && campaignIdList.size() > 0) {
            builder.in("campaign_id", campaignIdList.toArray());
        }
        builder.limit(Constants.TOTALSIZELIMIT);  // 限制10万
        List<String> fieldsList = new ArrayList<>();
        fieldsList.add("campaign_id");
        return listByConditionWithFields(puid, fieldsList, builder.build());
    }

    @Override
    public Page getPageList(Integer puid, CampaignPageParam param, Page page) {

        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_campaign_all ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( select id FROM `t_amazon_ad_campaign_all` ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), argsList));
        }
        //通过param.campaignId查询具体的广告活动
        if (StringUtils.isNotEmpty(param.getCampaignId())) {
            whereSql.append(" and campaign_id = ? ");
            argsList.add(param.getCampaignId());
        }

        if (StringUtils.isNotBlank(param.getType())) {
//            whereSql.append(" and type = ? ");
//            argsList.add(param.getType());
            whereSql.append(SqlStringUtil.dealInList("type",  StringUtil.splitStr(param.getType()), argsList));
        }

        if (StringUtils.isNotBlank(param.getPortfolioId()) && param.getPortfolioId().equals("-1")) {
            whereSql.append(" and  portfolio_id is null ");
        }


        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and name like ? ");
            argsList.add("%" + param.getSearchValue().trim() + "%");
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, argsList));
        }

        //服务状态筛选
        if(StringUtils.isNotBlank(param.getServingStatus())){
            List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
            List<String> servings = list.stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).flatMap(Collection::stream).collect(Collectors.toList());
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(servings)){
                whereSql.append(SqlStringUtil.dealInList("serving_status",servings,argsList));
            }
        }

        if (StringUtils.isNotBlank(param.getStrategyType())) {  //竞价策略
            whereSql.append(" and strategy = ? ");
            argsList.add(param.getStrategyType());
        }

        //预算状态
        if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.budgetExceeded.getCode())) {
            whereSql.append(" and serving_status in (?,?) ");
            argsList.add(AmazonAdCampaignAll.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
            argsList.add(AmazonAdCampaignAll.servingStatusEnum.outOfBudget.getCode());
        } else if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.underBudget.getCode())) {
            whereSql.append(" and serving_status not in (?,?) ");
            argsList.add(AmazonAdCampaignAll.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
            argsList.add(AmazonAdCampaignAll.servingStatusEnum.outOfBudget.getCode());
        }

        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");
        selectSql.append(" order by data_update_time desc, id desc ");

        Object[] args = argsList.toArray();
        return getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdCampaignAll.class);

    }

    @Override
    public List<String> getCampaignIdsByCampaign(Integer puid, CampaignPageParam param) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder(" select c.campaign_id from t_amazon_ad_campaign_all c ");
        StringBuilder whereSql = new StringBuilder(" where c.puid = ? and c.shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and c.name like ? ");
            argsList.add("%" + param.getSearchValue().trim() + "%");
        }
        if (StringUtils.isNotBlank(param.getType())) {
            whereSql.append(SqlStringUtil.dealInList("c.type", StringUtil.splitStr(param.getType()), argsList));
        }
        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }
        // 仅显示正在投放
        if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
            whereSql.append(" and c.serving_status in (?,?)");
            argsList.add(AmazonAdCampaignAll.servingStatusEnum.CAMPAIGN_STATUS_ENABLED.getCode());
            argsList.add(AmazonAdCampaignAll.servingStatusEnum.running.getCode());
        }

        if (StringUtils.isNotBlank(param.getPortfolioId()) && param.getPortfolioId().equals("-1")) {
            whereSql.append(" and c.portfolio_id is null ");
        }

        if (StringUtils.isNotBlank(param.getStrategyType())) {
            whereSql.append(" and c.strategy = ?");
            argsList.add(param.getStrategyType());
        }
        if (StringUtils.isNotBlank(param.getCostType())) {
            whereSql.append(" and c.cost_type = ? ");
            argsList.add(param.getCostType());
        }
        //预算状态
        if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.budgetExceeded.getCode())) {
            whereSql.append(" and c.serving_status in (?,?) ");
            argsList.add(AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
            argsList.add(AmazonAdCampaignAll.servingStatusEnum.outOfBudget.getCode());
        } else if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.underBudget.getCode())) {
            whereSql.append(" and c.serving_status not in (?,?) ");
            argsList.add(AmazonAdCampaignAll.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
            argsList.add(AmazonAdCampaignAll.servingStatusEnum.outOfBudget.getCode());
        }
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (StringUtils.isNotBlank(param.getFilterTargetType())) {  //活动类型  auto自动投放,manual手动投放
                List<String> typeList = StringUtil.splitStr(param.getFilterTargetType(), ",");
                whereSql.append(SqlStringUtil.dealInList("c.ad_target_type", typeList, argsList));
            }
            if (param.getDailyBudgetMin() != null) {   //每日预算
                whereSql.append(" and c.budget >= ? ");
                argsList.add(param.getDailyBudgetMin());
            }
            if (StringUtils.isNotBlank(param.getCostType())) {
                whereSql.append(" and c.cost_type = ? ");
                argsList.add(param.getCostType());
            }
            if (param.getDailyBudgetMax() != null) {
                whereSql.append(" and c.budget <= ? ");
                argsList.add(param.getDailyBudgetMax());
            }
            if (StringUtils.isNotBlank(param.getFilterStartDate())) {  // 日期
                whereSql.append(" and c.start_date >= ? ");
                argsList.add(param.getFilterStartDate());
            }
            if (StringUtils.isNotBlank(param.getFilterEndDate())) {
                whereSql.append(" and c.end_date <= ? ");
                argsList.add(param.getFilterEndDate());
            }
        }
        sql.append(whereSql);
        List<String> campaignIds = getJdbcTemplate(puid).queryForList(sql.toString(), argsList.toArray(), String.class);
        return campaignIds;
    }

    /**
     * type null 查所有
     *
     * @param puid
     * @param shopId
     * @param campaignId
     * @param type
     * @return
     */
    @Override
    public AmazonAdCampaignAll getCampaignByCampaignId(int puid, Integer shopId, String campaignId, String type) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("type", type)
                .build();
        return getByCondition(puid, builder);
    }


    /**
     * type null 查所有
     *
     * @param puid
     * @param shopId
     * @param campaignId
     * @param type
     * @return
     */
    @Override
    public List<AmazonAdCampaignAll> getByCampaignIds(Integer puid, Integer shopId, String marketplaceId, List<String> campaignId, String type) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("campaign_id", campaignId.toArray(new String[]{}));
        if (StringUtils.isNotBlank(type)) {
            builder.equalTo("type", type);
        }
        return listByCondition(puid, builder.build());
    }


    /**
     * type null 查所有
     *
     * @param puid
     * @param shopId
     * @param campaignId
     * @return
     */
    @Override
    public List<AmazonAdCampaignAll> listByCampaignIds(Integer puid, Integer shopId,  List<String> campaignId){
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("campaign_id", campaignId.toArray(new String[]{}))
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonAdCampaignAll> listByShopIdAndCampaignIds(Integer puid, List<Integer> shopId, List<String> campaignId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopId.toArray())
                .inStrList("campaign_id", campaignId.toArray(new String[]{}))
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonAdCampaignAll> getByCampaignIdsAndShopIdList(Integer puid, List<Integer> shopIdList, String marketplaceId, List<String> campaignId, String type) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopIdList.toArray())
                .inStrList("campaign_id", campaignId.toArray(new String[]{}))
                .equalTo("type", type)
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonAdCampaignAll> getByCampaignIdsAndShopIdList(Integer puid, List<Integer> shopIdList, String marketplaceId, List<String> campaignId, List<String> typeList) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopIdList.toArray())
                .inStrList("campaign_id", campaignId.toArray(new String[]{}))
                .in("type", typeList.toArray())
                .build();
        return listByCondition(puid, builder);
    }

    /**
     * type null 查所有
     *
     * @param puid
     * @param shopId
     * @param campaignId
     * @param type
     * @return
     */
    @Override
    public List<String> getPortfolioListByCampaignIds(Integer puid, Integer shopId, List<String> campaignId, String type) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("type", type)
                .isNotNull("portfolio_id")
                .inStrList("campaign_id", campaignId.toArray(new String[]{}))
                .groupBy("portfolio_id")
                .build();
        return listDistinctFieldByCondition(puid, "portfolio_id", builder, String.class);
    }

    /**
     * type null 查所有
     *
     * @param puid
     * @param shopId
     * @param campaignId
     * @return
     */
    @Override
    public List<String> getPortfolioListByCampaignIds(Integer puid, Integer shopId, List<String> campaignId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .isNotNull("portfolio_id")
                .inStrList("campaign_id", campaignId.toArray(new String[]{}))
                .groupBy("portfolio_id")
                .build();
        return listDistinctFieldByCondition(puid, "portfolio_id", builder, String.class);
    }

    /**
     * type null 查所有
     *
     * @param puid
     * @param shopId
     * @param marketPlaceId
     * @param campaignId
     * @param type
     * @return
     */
    @Override
    public AmazonAdCampaignAll getByCampaignId(int puid, Integer shopId, String marketPlaceId, String campaignId, String type) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("marketplace_id", marketPlaceId)
                .equalTo("type", type)
                .build();

        return getByCondition(puid, builder);
    }

    @Override
    public AmazonAdCampaignAll getByCampaignName(int puid, Integer shopId, String campaignName, String type) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("name", campaignName)
                .equalTo("type", type)
                .notEqualTo("state", CpcStatusEnum.archived.name())
                .limit(1)
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public AmazonAdCampaignAll getByCampaignIdAndStrategy(int puid, Integer shopId, String marketPlaceId, String campaignId, String type, String strategy, String state, String servingStatus) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("marketplace_id", marketPlaceId)
                .equalTo("type", type)
                .equalTo("strategy", strategy);
        if (StringUtils.isNotBlank(state)) {
            List<String> stateList = Arrays.asList(state.split(","));
            if (stateList.size() > 0) {
                builder.in("state", stateList.toArray());
            }
        }
        if (StringUtils.isNotBlank(servingStatus)) {
            List<String> servingStatusList = Arrays.asList(servingStatus.split(","));
            if (servingStatusList.size() > 0) {
                List<String> statusList = getServingStatus(servingStatusList);
                if (CollectionUtils.isNotEmpty(statusList)) {
                    builder.in("serving_status", statusList.toArray());
                }
            }
        }
        // todo heqiwen 做权限过滤

        return getByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdCampaignAll> getByCampaignIdAndStrategy(int puid, Integer shopId, String marketPlaceId, List<String> campaignIds, String type, String strategy, String state, String servingStatus) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("campaign_id", campaignIds.toArray())
                .equalTo("marketplace_id", marketPlaceId)
                .equalTo("type", type)
                .equalTo("strategy", strategy);
        if (StringUtils.isNotBlank(state)) {
            List<String> stateList = Arrays.asList(state.split(","));
            if (stateList.size() > 0) {
                builder.in("state", stateList.toArray());
            }
        }
        if (StringUtils.isNotBlank(servingStatus)) {
            List<String> servingStatusList = Arrays.asList(servingStatus.split(","));
            if (servingStatusList.size() > 0) {
                List<String> statusList = getServingStatus(servingStatusList);
                if (CollectionUtils.isNotEmpty(statusList)) {
                    builder.in("serving_status", statusList.toArray());
                }
            }
        }
        // todo heqiwen 做权限过滤
//        builder.in("campaign_id", );
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<String> getByCampaignIdsAndStrategy(int puid, Integer shopId, String marketPlaceId, List<String> campaignIds, String type, String strategy, String state, String servingStatus) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketPlaceId)
                .inStrList("campaign_id", campaignIds.toArray(new String[]{}))
                .equalTo("type", type)
                .equalTo("strategy", strategy);
        if (StringUtils.isNotBlank(state)) {
            List<String> stateList = Arrays.asList(state.split(","));
            if (stateList.size() > 0) {
                builder.in("state", stateList.toArray());
            }
        }
        if (StringUtils.isNotBlank(servingStatus)) {
            List<String> servingStatusList = Arrays.asList(servingStatus.split(","));
            if (servingStatusList.size() > 0) {
                List<String> statusList = getServingStatus(servingStatusList);
                if (CollectionUtils.isNotEmpty(statusList)) {
                    builder.in("serving_status", statusList.toArray());
                }
            }
        }
        return listDistinctFieldByCondition(puid, "campaign_id", builder.build(), String.class);
    }

    @Override
    public List<String> getCampaignIdByStrategy(int puid, Integer shopId, String marketPlaceId, String campaignId, String type, String strategy, String state, String servingStatus) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketPlaceId)
                .equalTo("campaign_id", campaignId)
                .equalTo("type", type)
                .equalTo("strategy", strategy)
                .build();
        return listDistinctFieldByCondition(puid, "campaign_id", builder, String.class);
    }

    @Override
    public List<String> getCampaignIdByStrategy(int puid, Integer shopId, String marketPlaceId, List<String> campaignIds, String type, String strategy, String state, String servingStatus) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketPlaceId)
                .equalTo("campaign_id", campaignIds.toArray(new String[]{}))
                .equalTo("type", type)
                .equalTo("strategy", strategy)
                .build();
        return listDistinctFieldByCondition(puid, "campaign_id", builder, String.class);
    }

    @Override
    public AmazonAdCampaignAll getSbAsinByCampaignId(int puid, Integer shopId, String campaignId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("type", Constants.SB)
                .equalTo("state", CpcStatusEnum.enabled.name())
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public AmazonAdCampaignAll getSdAsinByCampaignId(int puid, Integer shopId, String campaignId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("type", Constants.SD)
                .in("state", new String[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()})
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public List<AmazonAdCampaignAll> getAllStateSbAsinByCampaignId(int puid, Integer shopId, String campaignId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
            .equalTo("puid", puid)
            .equalTo("shop_id", shopId)
            .equalTo("campaign_id", campaignId)
            .equalTo("type", Constants.SB)
            .build();
        return listByCondition(puid, builder);
    }


    @Override
    public boolean exist(Integer puid, Integer shopId, String name, String type) {
        String sql = "select count(*) from t_amazon_ad_campaign_all where puid = ? and shop_id = ? and `name`=? and `type` = ? and `state` in ('enabled','paused')";
        return getJdbcTemplate(puid).queryForObject(sql, Boolean.class, puid, shopId, name, type);
    }

    @Override
    public void batchUpdateAmazonAdCampaign(Integer puid, List<AmazonAdCampaignAll> list, String type) {
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_campaign_all` set update_id=?,update_time=now() ");
        if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_BUDGET.equals(type)) {
            sql.append(",`budget` = ? ");
        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            sql.append(",`state`=? ");
        } else if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_ADVERTISING_SPACE.equals(type)) {
            sql.append(",`strategy`=?,`adjustments`=? ");
        } else {
            return;
        }
        sql.append(" where puid=? and shop_id=? and campaign_id=?");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg = null;
        for (AmazonAdCampaignAll amazonAdCampaign : list) {
            if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_BUDGET.equals(type)) {
                batchArg = new Object[]{
                        amazonAdCampaign.getUpdateId(),
                        amazonAdCampaign.getBudget(),
                        amazonAdCampaign.getPuid(),
                        amazonAdCampaign.getShopId(),
                        amazonAdCampaign.getCampaignId()
                };
            } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
                batchArg = new Object[]{
                        amazonAdCampaign.getUpdateId(),
                        amazonAdCampaign.getState(),
                        amazonAdCampaign.getPuid(),
                        amazonAdCampaign.getShopId(),
                        amazonAdCampaign.getCampaignId()
                };

            } else if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_ADVERTISING_SPACE.equals(type)) {
                batchArg = new Object[]{
                        amazonAdCampaign.getUpdateId(),
                        amazonAdCampaign.getStrategy(),
                        amazonAdCampaign.getAdjustments(),
                        amazonAdCampaign.getPuid(),
                        amazonAdCampaign.getShopId(),
                        amazonAdCampaign.getCampaignId()
                };
            }


            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate(sql.toString(), batchArgs);
    }

    @Override
    public List<AmazonAdCampaignAll> getCampaignsByShop(Integer puid, Integer shopId, String type) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("type", type)
                .inStrList("state", new String[]{"enabled", "paused"}).build();
        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonAdCampaignAll> getCampaignsByNoShop(Integer puid, Integer shopId, String type) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId).build();
        return listByCondition(puid, conditionBuilder);
    }


    /**
     * type null 查所有
     *
     * @param puid
     * @param shopId
     * @param portfolioId
     * @param type
     * @return
     */
    @Override
    public List<AmazonAdCampaignAll> getListByPortfolioId(Integer puid, Integer shopId, String portfolioId, String type) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("portfolio_id", portfolioId)
                .equalTo("type", type);
        builder.orderByDesc("data_update_time", "id");

        return listByCondition(puid, builder.build());
    }

    /**
     * type null 查所有
     *
     * @param puid
     * @param shopId
     * @param portfolioId
     * @return
     */
    @Override
    public List<AmazonAdCampaignAll> getListByPortfolioId(Integer puid, Integer shopId, String portfolioId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("portfolio_id", portfolioId);
        builder.orderByDesc("data_update_time", "id");

        return listByCondition(puid, builder.build());
    }

    @Override
    public void batchUpdatePortfolio(Integer puid, Integer shopId, String portfolioId, Integer uid, List<String> list) {
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_campaign_all` set portfolio_id = ?,")
                .append("`update_time`= now(),update_id=? where puid=? and shop_id=? and campaign_id=? ");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (String campaignId : list) {
            batchArg = new Object[]{
                    portfolioId,
                    uid,
                    puid,
                    shopId,
                    campaignId
            };
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate(sql.toString(), batchArgs);
    }

    @Override
    public List<AmazonAdCampaignAll> getAllCampaignsByShop(Integer puid, Integer shopId, String strategyType, List<String> campaignIdList, String type, String state, String servingStatus) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId);
        if (StringUtils.isNotBlank(type)) {
            builder.equalTo("type", type);
        }
        if (StringUtils.isNotBlank(strategyType)) {
            builder.equalTo("strategy", strategyType);
        }
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            builder.inStrList("campaign_id", campaignIdList.toArray(new String[]{}));
        }
        builder.orderByDesc("data_update_time", "id");
        if (StringUtils.isNotBlank(strategyType)) {
            builder.limit(Constants.TOTALSIZELIMIT);
        } else {
            builder.limit(Constants.PLAMENTLIMIT);
        }
        // todo heqiwen 做产品权限过滤
//        builder.inStrList("campaign_id", );
        return listByCondition(puid, builder.build());
    }


    @Override
    public String getMarketplaceIdByShopId(Integer puid, Integer shopId, String type) {
        StringBuilder sql = new StringBuilder("SELECT DISTINCT(marketplace_id)  FROM `t_amazon_ad_campaign_all` WHERE `shop_id` = ?");
        List<Object> args = new ArrayList<>();
        args.add(shopId);
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        return getJdbcTemplate(puid).queryForObject(sql.toString(), String.class, args.toArray());
    }

    @Override
    public String getNameByCpId(int puid, Integer shopId, String campaignId, String type) {
        StringBuilder sql = new StringBuilder("SELECT `name` FROM `t_amazon_ad_campaign_all` WHERE `puid`=? AND `shop_id`=? AND `campaign_id`= ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(shopId);
        args.add(campaignId);
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        return getJdbcTemplate(puid).queryForObject(sql.toString(), String.class, args.toArray());
    }

    @Override
    public String getTypeByCpId(int puid, Integer shopId, String marketplaceId, String campaignId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketplaceId)
                .equalTo("campaign_id", campaignId)
                .build();
        return getByCondition(puid, "targeting_type", String.class, builder);
    }

    @Override
    public List<AdCampaignOptionVo> getCampaignsByType(Integer puid, Integer shopId, String type, String campaignType, String groupType, String name,String campaignIds,String portfolioId) {

        StringBuilder sql = new StringBuilder("select id, campaign_id,name,ad_target_type,state,cost_type,create_time,type,brand_entity_id,ad_goal,is_multiAdGroups_enabled from t_amazon_ad_campaign_all where puid = ? and shop_id = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(shopId);

        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }

        if (StringUtils.isNotBlank(name)) {
            sql.append(" and name like ? ");
            args.add("%" + name + "%");
        }

        if (StringUtils.isNotBlank(campaignType)) {
            sql.append(" and ad_target_type = ? ");
            args.add(campaignType);
        }
        if(StringUtils.isNotBlank(portfolioId)){
            List<String> portfolioIds = StringUtil.splitStr(portfolioId, ",");
            sql.append(" and ( ");
            sql.append(" portfolio_id in ");
            sql.append(" ( "+SqlStringUtil.toInSqlStrs(portfolioIds)+" ) ");
            if (portfolioIds.contains("-1")) {
                sql.append("or portfolio_id is null ");
            }
            sql.append(" ) ");
        }

        if (StringUtils.isNotBlank(campaignIds)) {
            List<String> portfolioIds = StringUtil.splitStr(campaignIds, ",");
            sql.append(SqlStringUtil.dealInList("campaign_id", portfolioIds, args));
        }

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdCampaignOptionVo>() {
            @Override
            public AdCampaignOptionVo mapRow(ResultSet res, int i) throws SQLException {
                AdCampaignOptionVo optionVo = AdCampaignOptionVo.builder()
                    .name(res.getString("name"))
                    .campaignId(res.getString("campaign_id"))
                    .type(res.getString("type"))
                    .state(res.getString("state"))
                    .targetingType(res.getString("ad_target_type"))
                    .adGoal(res.getString("ad_goal"))
                    .isMultiAdGroupsEnabled(res.getBoolean("is_multiAdGroups_enabled"))
                        .build();
                if (StringUtils.isBlank(type)|| Constants.SB.equalsIgnoreCase(type) || Constants.SD.equalsIgnoreCase(type)) {
                    optionVo.setCostType(res.getString("cost_type"));
                }
                if (Constants.SB.equalsIgnoreCase(type)) {
                    optionVo.setBrandEntityId(res.getString("brand_entity_id"));
                }

                return optionVo;
            }
        }, args.stream().toArray());
    }

    @Override
    public List<AdCampaignOptionVo> getCampaignsByType(Integer puid, String shopId, String type, String campaignType, String groupType, String name, String campaignIds, String portfolioId) {


        StringBuilder sql = new StringBuilder("select id, campaign_id,name,ad_target_type,state,cost_type,create_time,type,brand_entity_id from t_amazon_ad_campaign_all where puid = ?");
        List<Object> args = new ArrayList<>();
        args.add(puid);

        if (StringUtils.isNotBlank(shopId)) {
            List<String> shopIdList = StringUtil.splitStr(shopId, ",");
            sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
        }

        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }

        if (StringUtils.isNotBlank(name)) {
            sql.append(" and name like ? ");
            args.add("%" + name + "%");
        }

        if (StringUtils.isNotBlank(campaignType)) {
            sql.append(" and ad_target_type = ? ");
            args.add(campaignType);
        }
        if (StringUtils.isNotBlank(portfolioId)) {
            List<String> portfolioIds = StringUtil.splitStr(portfolioId, ",");
            sql.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, args));
        }

        if (StringUtils.isNotBlank(campaignIds)) {
            List<String> portfolioIds = StringUtil.splitStr(campaignIds, ",");
            sql.append(SqlStringUtil.dealInList("campaign_id", portfolioIds, args));
        }

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdCampaignOptionVo>() {
            @Override
            public AdCampaignOptionVo mapRow(ResultSet res, int i) throws SQLException {
                AdCampaignOptionVo optionVo = AdCampaignOptionVo.builder()
                        .name(res.getString("name"))
                        .campaignId(res.getString("campaign_id"))
                        .type(res.getString("type"))
                        .state(res.getString("state"))
                        .targetingType(res.getString("ad_target_type"))
                        .build();
                if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                    optionVo.setCostType(res.getString("cost_type"));
                }
                if (Constants.SB.equalsIgnoreCase(type)) {
                    optionVo.setBrandEntityId(res.getString("brand_entity_id"));
                }

                return optionVo;
            }
        }, args.stream().toArray());
    }

    @Override
    public Long getId(Integer puid, Integer shopId, String campaignId, String type) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("type", type)
                .build();
        return getByCondition(puid, "id", Long.class, builder);
    }

    @Override
    public String getProfileId(int puid, Integer shopId, String marketPlaceId, String campaignId, String type) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("marketplace_id", marketPlaceId)
                .equalTo("type", type)
                .build();
        return getByCondition(puid, "profile_id", String.class, builder);
    }

    @Override
    public void updateState(int puid, Integer shopId, String campaignId, String state, int updateId, String type) {
        StringBuilder sql = new StringBuilder("update t_amazon_ad_campaign_all set state=?,update_id=?,update_time=now() where puid = ? and shop_id = ? and `campaign_id`=? ");
        List<Object> args = Lists.newArrayList(state, updateId, puid, shopId, campaignId);
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        getJdbcTemplate(puid).update(sql.toString(), args.toArray());
    }

    @Override
    public void updateDailyBudget(int puid, Integer shopId, String campaignId, Double dailyBudget, int updateId, String type) {

        StringBuilder sql = new StringBuilder("update t_amazon_ad_campaign_all set budget=?,update_id=?,update_time=now() where puid = ? and shop_id = ? and `campaign_id`=? ");
        List<Object> args = Lists.newArrayList(dailyBudget, updateId, puid, shopId, campaignId);
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        getJdbcTemplate(puid).update(sql.toString(), args.toArray());
    }

    @Override
    public int updateOutOfBudgetInfo(int puid, Integer shopId, String campaignId, Double dailyBudget,
                                      Long timeStamp, int updateId, String type) {

        StringBuilder sql = new StringBuilder("update t_amazon_ad_campaign_all set budget=? , serving_status = 'CAMPAIGN_OUT_OF_BUDGET'," +
                " out_of_budget_time = ? ,update_id=?,update_time=now() where puid = ? " +
                "and shop_id = ? and `campaign_id`=? ");
        List<Object> args = Lists.newArrayList(dailyBudget, timeStamp, updateId, puid, shopId, campaignId);
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
       return getJdbcTemplate(puid).update(sql.toString(), args.toArray());
    }


    /**
     * 注意这个方法只针对feed buget 当前未超预算是更新预算使用，会更新二级服务状态
     * @param puid
     * @param shopId
     * @param campaignId
     * @param dailyBudget
     * @param updateId
     * @param type
     */
    @Override
    public void updateDailyBudgetAndServingStatus(int puid, Integer shopId, String campaignId, Double dailyBudget, int updateId, String type) {

        StringBuilder sql = new StringBuilder("update t_amazon_ad_campaign_all set budget=?,update_id=?,update_time=now(),serving_status = 'CAMPAIGN_STATUS_ENABLED',out_of_budget_time = null where puid = ? and shop_id = ? and `campaign_id`=? ");
        List<Object> args = Lists.newArrayList(dailyBudget, updateId, puid, shopId, campaignId);
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        getJdbcTemplate(puid).update(sql.toString(), args.toArray());
    }



    @Override
    public String getNameByCampaignId(Integer puid, Integer shopId, String marketplaceId, String campaignId, String type) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("marketplace_id", marketplaceId)
                .equalTo("type", type)
                .build();
        return getByCondition(puid, "name", String.class, builder);
    }

    @Override
    public String getCampaignTypeByCampaignId(Integer puid, Integer shopId, String marketplaceId, String campaignId, String type) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("marketplace_id", marketplaceId)
                .equalTo("type", type)
                .build();
        return getByCondition(puid, "targeting_type", String.class, builder);
    }

    @Override
    public Page pageListForTask(int puid, CpcTaskSearchDto dto, Page page, String type) {
        StringBuilder selectSql = new StringBuilder("select campaign_id,name,create_time from t_amazon_ad_campaign_all");
        StringBuilder countSql = new StringBuilder("select count(*) from t_amazon_ad_campaign_all ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and state in ('enabled','paused') and type = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(type);
        if (Constants.ITEM_TYPE_CAMPAIGN.equals(dto.getSearchField()) && StringUtils.isNotEmpty(dto.getSearchValue())) {
            String searchValue = SqlStringUtil.dealLikeSql(dto.getSearchValue());
            whereSql.append(" and name like ? ");
            argsList.add(searchValue + "%");
        }
        selectSql.append(whereSql);
        countSql.append(whereSql);
        selectSql.append(" order by id desc");
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdCampaignAll.class);
    }

    @Override
    public List<Map<String, Object>> getCampaignNames(int puid, Integer shopId, String marketplaceId, String searchValue, Integer pageNo, Integer pageSize, String type) {

        List<Object> args = new ArrayList<>();
        String sql = "select `name`,campaign_id id from t_amazon_ad_campaign_all where puid=? and shop_id=? and marketplace_id=? and  state in ('enabled','paused')";
        args.add(puid);
        args.add(shopId);
        args.add(marketplaceId);
        if (StringUtils.isNotBlank(type)) {
            sql += " and type = ? ";
            args.add(type);
        }
        if (StringUtils.isNotBlank(searchValue)) {
            sql += " and `name` like ? ";
            args.add("%" + searchValue.trim() + "%");
        }
        sql += " limit ?, ? ";
        int start = pageSize * (pageNo - 1);
        args.add(start);
        args.add(pageSize);

        return getJdbcTemplate(puid).queryForList(sql, args.toArray());
    }

    @Override
    public Integer getCampaignNamesTotalSize(int puid, Integer shopId, String searchValue, String type) {
        List<Object> args = new ArrayList<>();
        String sql = "select count(*) from t_amazon_ad_campaign_all where puid=? and shop_id=? and  state in ('enabled','paused')";
        args.add(puid);
        args.add(shopId);
        if (StringUtils.isNotBlank(type)) {
            sql += " and type = ? ";
            args.add(type);
        }
        if (StringUtils.isNotBlank(searchValue)) {
            sql += " and `name` like ? ";
            args.add("%" + searchValue.trim() + "%");
        }
        return getJdbcTemplate(puid).queryForObject(sql, Integer.class, args.toArray());
    }

    @Override
    public List<AmazonAdCampaignAll> getNamesAndState(int puid, Integer shopId, String type) {
        StringBuilder sql = new StringBuilder("select `name`,campaign_id,state from t_amazon_ad_campaign_all where puid=? and shop_id=? and  state in ('enabled','paused') ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(shopId);
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        return getJdbcTemplate(puid).query(sql.toString(), getMapper(), args.toArray());
    }


    @Override
    public List<String> getCampaignIdsByState(Integer puid, Integer shopId, String state, String type) {
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where puid=? and shop_id=? and state=? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(shopId);
        args.add(state);
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), args.toArray(), String.class);
    }

    @Override
    public void updateBudgetPricing(Integer puid, Integer shopId, String campaignId, Integer isPricing, Integer pricingState, int updateId, String type) {
        StringBuilder sql = new StringBuilder("update t_amazon_ad_campaign_all set is_budget_pricing=?,pricing_budget_state=?,update_id=?,update_time=now() where puid = ? and shop_id = ? and `campaign_id`=? ");
        List<Object> args = Lists.newArrayList(isPricing, pricingState, updateId, puid, shopId, campaignId);
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        getJdbcTemplate(puid).update(sql.toString(), args.toArray());
    }

    @Override
    public void updateStatePricing(Integer puid, Integer shopId, String campaignId, Integer isPricing, Integer pricingState, int updateId, String type) {
        StringBuilder sql = new StringBuilder("update t_amazon_ad_campaign_all set is_state_pricing=?,pricing_state_state=?,update_id=?,update_time=now() where puid = ? and shop_id = ? and `campaign_id`=? ");
        List<Object> args = Lists.newArrayList(isPricing, pricingState, updateId, puid, shopId, campaignId);
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        getJdbcTemplate(puid).update(sql.toString(), args.toArray());
    }

    @Override
    public void updateDataUpdateTime(Integer puid, Integer shopId, String campaignId, LocalDate localDate, String type) {

        StringBuilder sql = new StringBuilder("update t_amazon_ad_campaign_all set data_update_time=?,update_time=now() where puid = ? and shop_id = ? and `campaign_id`=? ");
        List<Object> args = Lists.newArrayList(localDate, puid, shopId, campaignId);
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        getJdbcTemplate(puid).update(sql.toString(), args.toArray());
    }

    @Override
    public void updateSpacePricing(Integer puid, Integer shopId, String campaignId, Integer isPricing, Integer pricingState, int updateId, String type) {

        StringBuilder sql = new StringBuilder("update t_amazon_ad_campaign_all set is_space_pricing=?,pricing_space_state=?,update_id=?,update_time=now() where puid = ? and shop_id = ? and `campaign_id`=? ");
        List<Object> args = Lists.newArrayList(isPricing, pricingState, updateId, puid, shopId, campaignId);
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        getJdbcTemplate(puid).update(sql.toString(), args.toArray());
    }

    @Override
    public List<Map<String, Object>> getCampaignNameList(int puid, Integer shopId, String type) {
        StringBuilder sql = new StringBuilder("select `name`,campaign_id id from t_amazon_ad_campaign_all where puid=? and shop_id=? ");
        List<Object> args = Lists.newArrayList(puid, shopId);
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), args.toArray());
    }

    @Override
    public List<Map<String, Object>> getCampaignNameByShopIdsAndCampaignIdsList(int puid, List<Integer> shopIds, List<String> campaignIds, String type) {
        String sql = "select `name`,campaign_id id from t_amazon_ad_campaign_all where ";
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopIds.toArray())
                .in("campaign_id", campaignIds.toArray())
                .equalTo("type", type)
                .build();
        sql += conditionBuilder.getSql();

        return getJdbcTemplate(puid).queryForList(sql, conditionBuilder.getValues());
    }

    @Override
    public List<AmazonAdCampaignAll> getCampaignNameByShopIdsList(int puid, List<Integer> shopIds, String type) {
        StringBuilder sql = new StringBuilder("select `shop_id` as shopId , `marketplace_id` as marketplaceId, `name`,campaign_id as campaignId  from t_amazon_ad_campaign_all where puid=? ");
        List<Object> args = Lists.newArrayList(puid);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        return getJdbcTemplate(puid).query(sql.toString(), args.toArray(), BeanPropertyRowMapper.newInstance(AmazonAdCampaignAll.class));
    }

    /**
     * TODO 关联关键词库查询
     * @param puid
     * @param shopIds
     * @param campaignIds
     * @param type
     * @return
     */
    @Override
    public List<AmazonAdCampaignAll> getListByShopIdsAndCampaignIds(int puid, List<Integer> shopIds, List<String> campaignIds, String type) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .inIntList("shop_id",shopIds.toArray(new Integer[0]))
                .inStrList("campaign_id",campaignIds.toArray(new String[0]));
        if (StringUtils.isNotEmpty(type)) {
            builder.equalTo("type", type);
        }
        builder.groupBy("puid", "shop_id", "campaign_id");
        return listByCondition(puid,builder.build());
    }

    @Override
    public List<AmazonAdCampaignAll> getNameByShopIdsAndCampaignIds(int puid, List<Integer> shopIds,
                                                                    List<String> campaignIds, String type,
                                                                    String campaignName) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .inIntList("shop_id",shopIds.toArray(new Integer[0]))
                .inStrList("campaign_id",campaignIds.toArray(new String[0]));
        if (StringUtils.isNotEmpty(type)) {
            builder.equalTo("type", type);
        }
        if (StringUtils.isNotEmpty(campaignName)) {
            builder.like("name", campaignName);
        }
        return listByConditionWithFields(puid, Arrays.asList("campaign_id", "name", "type", "portfolio_id, cost_type, ad_goal"), builder.build());
    }

    @Override
    public List<AmazonAdCampaignAll> getNameAndStateByShopIdsAndCampaignIds(int puid, List<Integer> shopIds,
                                                                    List<String> campaignIds, String type,
                                                                    String campaignName) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .inIntList("shop_id",shopIds.toArray(new Integer[0]))
                .inStrList("campaign_id",campaignIds.toArray(new String[0]));
        if (StringUtils.isNotEmpty(type)) {
            builder.equalTo("type", type);
        }
        if (StringUtils.isNotEmpty(campaignName)) {
            builder.like("name", campaignName);
        }
        return listByConditionWithFields(puid, Arrays.asList("campaign_id", "name", "type", "portfolio_id", "state", "budget", "cost_type"), builder.build());
    }

    @Override
    public List<AmazonAdCampaignAll> getNameByShopIdsAndPortfolioIdsAndCampaignIds(Integer puid, List<Integer> shopIds, List<String> portfolioIds, List<String> campaignIds, String type, String campaignName) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .inIntList("shop_id",shopIds.toArray(new Integer[0]))
                .inIntList("portfolio_id",portfolioIds.toArray(new Integer[0]))
                .inStrList("campaign_id",campaignIds.toArray(new String[0]));
        if (StringUtils.isNotEmpty(type)) {
            builder.equalTo("type", type);
        }
        if (StringUtils.isNotEmpty(campaignName)) {
            builder.like("name", campaignName);
        }
        return listByConditionWithFields(puid, Arrays.asList("campaign_id", "name", "type"), builder.build());
    }

    @Override
    public List<AmazonAdCampaignAll> getAutoRuleCampaigns(int puid, int shopId, List<String> portfolioIds, String campaignName) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId);
        if (StringUtils.isNotBlank(campaignName)) {
            builder.like("name", campaignName);
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            builder.inStrList("portfolio_id",portfolioIds.toArray(new String[portfolioIds.size()]));
        }
        return listByCondition(puid,builder.build());
    }

    @Override
    public Page<AmazonAdCampaignAll> pageAutoRuleCampaigns(AdCampaignAutoRuleParam param, List<String> itemIds, List<String> similarRuleItemIdList) {
        StringBuilder sql = new StringBuilder("SELECT id,puid,shop_id,marketplace_id,campaign_id,`name`,type,budget,budget_type,portfolio_id,state,serving_status" +
                " FROM t_amazon_ad_campaign_all t");
        StringBuilder countSql = new StringBuilder("SELECT count(t.campaign_id) FROM ( select campaign_id FROM `t_amazon_ad_campaign_all`");
        List<Object> args = new ArrayList<>();
        StringBuilder whereSql = new StringBuilder(" where puid = ? ");
        args.add(param.getPuid());
        if (param.getShopId() != null) {
            whereSql.append(" and shop_id = ?");
            args.add(param.getShopId());
        }
        //过滤模板关系
        if (CollectionUtils.isNotEmpty(itemIds)) {
            //过滤模板关系
            whereSql.append(SqlStringUtil.dealNotInList("campaign_id", itemIds, args));
        }
        if (param.getHasSimilarRule() != null) {
            if (param.getHasSimilarRule() == 0) {
                if (CollectionUtils.isNotEmpty(similarRuleItemIdList)) {
                    //过滤模板关系
                    whereSql.append(SqlStringUtil.dealNotInList("campaign_id", similarRuleItemIdList, args));
                }
            } else if (param.getHasSimilarRule() == 1) {
                if (CollectionUtils.isNotEmpty(similarRuleItemIdList)) {
                    //过滤模板关系
                    whereSql.append(SqlStringUtil.dealInList("campaign_id", similarRuleItemIdList, args));
                }
            }
        }

        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            List<String> servings = param.getServingStatusList().stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(servings)) {
                whereSql.append(SqlStringUtil.dealInList("serving_status", servings, args));
            }
        }
        if (CollectionUtils.isNotEmpty(param.getPortfolioId())) {
            whereSql.append(" and ( ");
            whereSql.append(" portfolio_id in ");
            whereSql.append(" ( "+SqlStringUtil.toInSqlStrs(param.getPortfolioId())+" ) ");
            if (param.getPortfolioId().contains("-1")) {
                whereSql.append("or portfolio_id is null ");
            }
            whereSql.append(" ) ");
        }

        if (CollectionUtils.isNotEmpty(param.getStateList())) {
            whereSql.append(SqlStringUtil.dealInList("state", param.getStateList(), args));
        }
        if (CollectionUtils.isNotEmpty(param.getAdTypeList())) {
            whereSql.append(SqlStringUtil.dealInList("type", param.getAdTypeList(), args));
        }
        if (StringUtils.isNotBlank(param.getCampaignName())) {
            whereSql.append(" and name like ?");
            args.add("%"+param.getCampaignName()+"%");
        }
        whereSql.append(" group by campaign_id");
        sql.append(whereSql);
        countSql.append(whereSql).append(") t");
        return this.getPageResult(param.getPuid(),param.getPageNo(),param.getPageSize(),
                countSql.toString(),args.toArray(),sql.toString(),args.toArray(),AmazonAdCampaignAll.class);
    }

    @Override
    public List<String> getArchivedItems(Integer puid, Integer shopId, String type) {
        String sql = "select campaign_id from t_amazon_ad_campaign_all where ";
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("state", "archived")
                .equalTo("type", type)
                .build();
        sql += conditionBuilder.getSql();
        return getJdbcTemplate(puid).queryForList(sql, String.class, conditionBuilder.getValues());
    }

    @Override
    public List<String> getUpdateAfterReportSyncTimeItems(Integer puid, Integer shopId, LocalDateTime syncAt, String type) {

        String sql = "select campaign_id from t_amazon_ad_campaign_all where ";
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .greaterThan("update_time", syncAt)
                .equalTo("type", type)
                .build();
        sql += conditionBuilder.getSql();
        return getJdbcTemplate(puid).queryForList(sql, String.class, conditionBuilder.getValues());
    }

    @Override
    public List<Map<String, Object>> getSpCampaignNames(int puid, GetNamesDto dto) {

        StringBuilder sql = new StringBuilder("select `name`,campaign_id id from t_amazon_ad_campaign_all where puid=? and shop_id=? and marketplace_id=? and type = ? and  state in ('enabled','paused') ");
        List<Object> args = Lists.newArrayListWithExpectedSize(4);
        args.add(puid);
        args.add(dto.getShopId());
        args.add(dto.getMarketplaceId());
        args.add(CampaignTypeEnum.sp.getCampaignType());
        if (Constants.MANUAL.equals(dto.getTargetingType())) {
            sql.append(" and targeting_type=?");
            args.add(dto.getTargetingType());
        } else if (Constants.AUTO.equals(dto.getTargetingType())) {
            sql.append(" and targeting_type=?");
            args.add(dto.getTargetingType());
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), args.toArray());
    }

    @Override
    public List<AmazonAdCampaignAll> listNoCampaignTargetType(Integer puid, Integer shopId, String campaignId, String type) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId);

        if (StringUtils.isNotBlank(campaignId)) {
            builder.equalTo("campaign_id", campaignId);
        }

        if (StringUtils.isNotBlank(type)) {
            builder.equalTo("type", type);
        }


        ConditionBuilder conditionBuilder = builder.build();
        String sql = "select * from t_amazon_ad_campaign_all where (target_type is null or target_type='') and " + conditionBuilder.getSql();

        return getJdbcTemplate(puid).query(sql, getMapper(), conditionBuilder.getValues());
    }

    @Override
    public String getSbCreativeByCampaignId(Integer puid, Integer shopId, String campaignId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("type", CampaignTypeEnum.sb.getCampaignType())
                .build();
        return getByCondition(puid, "creative", String.class, builder);
    }


    @Override
    public List<AmazonAdCampaignAll> getListCampaign(int puid) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid);
        return listByCondition(puid, builder.build());
    }

    @Override
    public String getSbFormatByCampaignId(Integer puid, Integer shopId, String campaignId) {
        String cacheKey = String.format(RedisConstant.SELLFOX_AD_SB_CAMPAIGN_FORMAT, puid, shopId, campaignId);
        String value = redisService.getString(cacheKey);
        if (StringUtils.isBlank(value)) {
            ConditionBuilder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", shopId)
                    .equalTo("campaign_id", campaignId)
                    .equalTo("type", CampaignTypeEnum.sb.getCampaignType())
                    .build();
            value = getByCondition(puid, "ad_format", String.class, builder);
            if (StringUtils.isNotBlank(value)) {
                redisService.set(cacheKey, value, 1, TimeUnit.DAYS);
            }
        }
        return value;
    }

    @Override
    public Page<AmazonAdCampaignAll> queryAmazonAdCampaignAll(AdCampaignStrategyParam param) {
        StringBuilder sql = new StringBuilder("SELECT id,puid,shop_id,marketplace_id,campaign_id,`name`,type,budget,budget_type,portfolio_id,state,serving_status" +
                " FROM t_amazon_ad_campaign_all ");
        StringBuilder countSql = new StringBuilder("SELECT count(t.campaign_id) FROM ( select campaign_id FROM `t_amazon_ad_campaign_all`");
        List<Object> args = new ArrayList<>();
        StringBuilder whereSql = new StringBuilder(" where puid = ? and is_budget_pricing = 0 ");
        args.add(param.getPuid());
        if (param.getShopId() != null) {
            whereSql.append(" and shop_id = ?");
            args.add(param.getShopId());
        }
        whereSql.append(" and if ( type='sb', serving_status in ('asinNotBuyable','billingError'," +
                "'ended','landingPageNotAvailable','outOfBudget','paused','ready','running','scheduled','CAMPAIGN_OUT_OF_BUDGET','CAMPAIGN_PAUSED','CAMPAIGN_STATUS_ENABLED','PORTFOLIO_OUT_OF_BUDGET','ADVERTISER_PAYMENT_FAILURE') ,1=1)");
        if (CollectionUtils.isNotEmpty(param.getPortfolioIds())) {
            whereSql.append(SqlStringUtil.dealInList("portfolio_id",param.getPortfolioIds(),args));
        }
        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            //过滤模板关系
            whereSql.append(SqlStringUtil.dealInList("serving_status", param.getServingStatusList(), args));
        }
        if (CollectionUtils.isNotEmpty(param.getAdTypeList())) {
            whereSql.append(SqlStringUtil.dealInList("type",param.getAdTypeList(),args));
        }
        if (StringUtils.isNotBlank(param.getState())) {
            if (param.getState().equals("all")) {
                whereSql.append(" and state in ('enabled','paused')");
            } else {
                whereSql.append(" and state = ?");
                args.add(param.getState());
            }
        }
        if (StringUtils.isNotBlank(param.getCampaignName())) {
            whereSql.append(" and name like ?");
            args.add("%"+param.getCampaignName()+"%");
        }
        whereSql.append(" group by campaign_id");
        sql.append(whereSql);
        countSql.append(whereSql).append(") t");
        return this.getPageResult(param.getPuid(),param.getPageNo(),param.getPageSize(),
                countSql.toString(),args.toArray(),sql.toString(),args.toArray(),AmazonAdCampaignAll.class);
    }

    @Override
    public Page<AmazonAdCampaignAll> queryAmazonAdSpaceAll(AdSpaceStrategyParam param) {
        StringBuilder sql = new StringBuilder("SELECT id,puid,shop_id,marketplace_id,campaign_id,`name`,type,portfolio_id,state,`adjustments`,`strategy`,serving_status" +
                " FROM t_amazon_ad_campaign_all ");
        StringBuilder countSql = new StringBuilder("SELECT count(t.campaign_id) FROM ( select campaign_id FROM `t_amazon_ad_campaign_all`");
        List<Object> args = new ArrayList<>();
        StringBuilder whereSql = new StringBuilder("where puid = ? AND type = 'sp' and is_space_pricing = 0 ");
        args.add(param.getPuid());
        if (param.getShopId() != null) {
            whereSql.append(" and shop_id = ?");
            args.add(param.getShopId());
        }
        //分时广告位不支持sb，上边已限定sp，去掉此条件无效
        /*whereSql.append(" and if ( type='sb', serving_status in ('asinNotBuyable','billingError'," +
                "'ended','landingPageNotAvailable','outOfBudget','paused','ready','running','scheduled','CAMPAIGN_OUT_OF_BUDGET','CAMPAIGN_PAUSED','CAMPAIGN_STATUS_ENABLED','PORTFOLIO_OUT_OF_BUDGET','ADVERTISER_PAYMENT_FAILURE') ,1=1)");*/
        if (CollectionUtils.isNotEmpty(param.getPortfolioIds())) {
            whereSql.append(SqlStringUtil.dealInList("portfolio_id",param.getPortfolioIds(),args));
        }
        if (StringUtils.isNotBlank(param.getState())) {
            if (param.getState().equals("all")) {
                whereSql.append(" and state in ('enabled','paused')");
            } else {
                whereSql.append(" and state = ?");
                args.add(param.getState());
            }
        }
        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            //过滤模板关系
            whereSql.append(SqlStringUtil.dealInList("serving_status", param.getServingStatusList(), args));
        }
        if (StringUtils.isNotBlank(param.getCampaignName())) {
            whereSql.append(" and name like ?");
            args.add("%"+param.getCampaignName()+"%");
        }
        whereSql.append(" group by campaign_id");
        sql.append(whereSql);
        countSql.append(whereSql).append(") t");
        return this.getPageResult(param.getPuid(),param.getPageNo(),param.getPageSize(),
                countSql.toString(),args.toArray(),sql.toString(),args.toArray(),AmazonAdCampaignAll.class);
    }

    @Override
    public Page<AmazonAdCampaignAll> queryAmazonAdCampaignStateAll(AdCampaignStrategyParam param) {
        StringBuilder sql = new StringBuilder("SELECT id,puid,shop_id,marketplace_id,campaign_id,`name`,type,budget,budget_type,portfolio_id,state,serving_status" +
                " FROM t_amazon_ad_campaign_all ");
        StringBuilder countSql = new StringBuilder("SELECT count(t.campaign_id) FROM ( select campaign_id FROM `t_amazon_ad_campaign_all`");
        List<Object> args = new ArrayList<>();
        StringBuilder whereSql = new StringBuilder(" where puid = ? and is_state_pricing = 0 ");
        args.add(param.getPuid());
        if (param.getShopId() != null) {
            whereSql.append(" and shop_id = ?");
            args.add(param.getShopId());
        }
        whereSql.append(" and if ( type='sb', serving_status in ('asinNotBuyable','billingError'," +
                "'ended','landingPageNotAvailable','outOfBudget','paused','ready','running','scheduled','CAMPAIGN_OUT_OF_BUDGET','CAMPAIGN_PAUSED','CAMPAIGN_STATUS_ENABLED','PORTFOLIO_OUT_OF_BUDGET','ADVERTISER_PAYMENT_FAILURE') ,1=1)");

        if (CollectionUtils.isNotEmpty(param.getPortfolioIds())) {
            if (param.getPortfolioIds().size() == 1 && param.getPortfolioIds().contains("-1")) {
                whereSql.append(" and portfolio_id is null ");
            } else if (param.getPortfolioIds().contains("-1")) {
                whereSql.append(" and ( ").append(SqlStringUtil.dealInListNotAnd("portfolio_id", param.getPortfolioIds(), args))
                        .append(" or portfolio_id is null ) ");
            } else {
                whereSql.append(SqlStringUtil.dealInList("portfolio_id", param.getPortfolioIds(), args));
            }
        }

        if (CollectionUtils.isNotEmpty(param.getAdTypeList())) {
            whereSql.append(SqlStringUtil.dealInList("type",param.getAdTypeList(), args));
        }

        //服务状态查询
        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            List<String> servings = param.getServingStatusList().stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(servings)) {
                whereSql.append(SqlStringUtil.dealInList("serving_status", servings, args));
            }
        }

        if (StringUtils.isNotBlank(param.getState())) {
            if (param.getState().equals("all")) {
                whereSql.append(" and state in ('enabled','paused')");
            } else {
                whereSql.append(" and state = ?");
                args.add(param.getState());
            }
        }
        if (StringUtils.isNotBlank(param.getCampaignName())) {
            whereSql.append(" and name like ?");
            args.add("%"+param.getCampaignName()+"%");
        }
        whereSql.append(" group by campaign_id");
        sql.append(whereSql);
        countSql.append(whereSql).append(") t");
        return this.getPageResult(param.getPuid(),param.getPageNo(),param.getPageSize(),
                countSql.toString(),args.toArray(),sql.toString(),args.toArray(),AmazonAdCampaignAll.class);
    }


    @Override
    public AmazonAdCampaignAll getByCampaignId(int puid, Integer shopId, String campaignId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .equalTo("campaign_id",campaignId)
                .build();
        return getByCondition(puid,builder);
    }

    @Override
    public List<String> getCampaignIds(Integer puid, Integer shopId, String type) {
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where puid=? and shop_id=?  ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(shopId);
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), args.toArray(), String.class);
    }

    @Override
    public List<String> getCampaignIds(Integer puid, Integer shopId, String type, List<String> campaignIds) {
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where puid=? and shop_id=?  ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(shopId);
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, args));
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), args.toArray(), String.class);
    }


    @Override
    public void batchUpdateSbAdFormat(int puid, List<AmazonAdCampaignAll> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;

        for (AmazonAdCampaignAll campaign : list) {
            if (campaign.getPuid() == null || campaign.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(campaign.getAdFormat());
            arg.add(campaign.getAdFormat());
            arg.add(campaign.getPuid());
            arg.add(campaign.getShopId());
            arg.add(Constants.SB);
            arg.add(campaign.getCampaignId());
            argList.add(arg.toArray());
        }
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_campaign_all` set `ad_format` =?,`ad_target_type` = ? , update_time=now() where `puid` =? and `shop_id`=? and `type` = ? and `campaign_id`=? and (ad_format is null or ad_format = '' ) ");
        getJdbcTemplate(puid).batchUpdate(sql.toString(), argList);
    }

    @Override
    public List<AmazonAdCampaignAll> getListSbFormatIsNUll(Integer puid, Integer shopId, List<String> campaignIds) {
        StringBuilder sql = new StringBuilder("select *  from t_amazon_ad_campaign_all where puid=? and shop_id = ? and type = 'sb' and (ad_format is null or ad_format = '' ) ");
        List<Object> args = Lists.newArrayList(puid,shopId);
        if(CollectionUtils.isNotEmpty(campaignIds)){
            sql.append( SqlStringUtil.dealInList("campaign_id",campaignIds,args));
        }
        return getJdbcTemplate(puid).query(sql.toString(), args.toArray(), BeanPropertyRowMapper.newInstance(AmazonAdCampaignAll.class));
    }

    /**
     * 获取每日预算汇总字段
     */
    @Override
    public BigDecimal getSumDailyBudget(Integer puid, CampaignPageParam param) {
        if (param.getStatus() == null || !"enabled".equalsIgnoreCase(param.getStatus())) {
            return null;
        }

        StringBuilder selectSql = new StringBuilder("select COALESCE(sum(budget), 0) dailyBudget from t_amazon_ad_campaign_all ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and state = 'enabled' ");
        argsList.add(puid);

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), argsList));
        }
        //通过param.campaignId查询具体的广告活动
        if (StringUtils.isNotEmpty(param.getCampaignId())) {
            whereSql.append(" and campaign_id = ? ");
            argsList.add(param.getCampaignId());
        }
        if (StringUtils.isNotBlank(param.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", StringUtil.splitStr(param.getCampaignIds()), argsList));
        }

        if (StringUtils.isNotBlank(param.getType())) {
//            whereSql.append(" and type = ? ");
//            argsList.add(param.getType());
            whereSql.append(SqlStringUtil.dealInList("type", StringUtil.splitStr(param.getType()), argsList));
        }

        if (StringUtils.isNotBlank(param.getPortfolioId()) && param.getPortfolioId().equals("-1")) {
            whereSql.append(" and  portfolio_id is null ");
        }


        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and name like ? ");
            argsList.add("%" + param.getSearchValue().trim() + "%");
        }

        // 仅显示正在投放
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            if (Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status in (?, ?) ");
                argsList.add(AmazonAdCampaignAll.servingStatusEnum.CAMPAIGN_STATUS_ENABLED.getCode());
                argsList.add(AmazonAdCampaignAll.servingStatusEnum.running.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                List<String> servings = list.stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(servings)) {
                    whereSql.append(SqlStringUtil.dealInList("serving_status", servings, argsList));
                }
            }
        }

        if (StringUtils.isNotBlank(param.getStrategyType())) {  //竞价策略
            whereSql.append(" and strategy = ? ");
            argsList.add(param.getStrategyType());
        }

        //预算状态
        if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.budgetExceeded.getCode())) {
            whereSql.append(" and serving_status in (?,?) ");
            argsList.add(AmazonAdCampaignAll.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
            argsList.add(AmazonAdCampaignAll.servingStatusEnum.outOfBudget.getCode());
        } else if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.underBudget.getCode())) {
            whereSql.append(" and serving_status not in (?,?) ");
            argsList.add(AmazonAdCampaignAll.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
            argsList.add(AmazonAdCampaignAll.servingStatusEnum.outOfBudget.getCode());
        }

        selectSql.append(whereSql);

        Object[] args = argsList.toArray();
        return getJdbcTemplate(puid).queryForObject(selectSql.toString(), BigDecimal.class, args);
    }

    @Override
    public int setEndDateNull(AmazonAdCampaignAll campaignAll) {
        String updateSql = "update t_amazon_ad_campaign_all set end_date = null where puid = ? and shop_id = ? and type = ? and campaign_id = ?";
        return getJdbcTemplate(campaignAll.getPuid()).update(updateSql, campaignAll.getPuid()
        , campaignAll.getShopId(), campaignAll.getType(), campaignAll.getCampaignId());
    }

    @Override
    public void strategyUpdate(int puid, int shopId, String itemType, ScheduleTaskFinishedVo message, String type, String campaignId) {
        StringBuilder updateSql = new StringBuilder("update t_amazon_ad_campaign_all ");
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? and type = ? and campaign_id = ?");
        List<Object> args = Lists.newArrayList();
        if ("campaignBudget".equals(itemType)) {
            updateSql.append(" set budget=?");
            args.add(message.getModifiedValue());
        } else if ("campaignState".equals(itemType)) {
            updateSql.append(" set state=?");
            args.add(message.getState());
        }
        updateSql.append(whereSql);
        args.add(puid);
        args.add(shopId);
        args.add(type);
        args.add(campaignId);
        getJdbcTemplate(puid).update(updateSql.toString(),args.toArray());
    }

    @Override
    public void strategyUpdatePlacement(int puid, int shopId, List<CampaignAdjustment> adjustmentList, String campaignId) {
        String updateSql = "update t_amazon_ad_campaign_all set adjustments = ? where puid = ? and shop_id = ? and campaign_id = ?";
        List<Adjustment> adjustments = new ArrayList<>();
        adjustmentList.stream().forEach(e->{
            Adjustment adjustment = new Adjustment();
            adjustment.setPercentage(e.getPercentage());
            adjustment.setPredicate(e.getPredicate().name());
            adjustments.add(adjustment);
        });
        getJdbcTemplate(puid).update(updateSql,JSONUtil.objectToJson(adjustments),puid,shopId,campaignId);
    }

    @Override
    public void strategyUpdateStrategy(int puid, int shopId, CampaignStrategy strategy, String campaignId) {
        String updateSql = "update t_amazon_ad_campaign_all set strategy = ? where puid = ? and shop_id = ? and campaign_id = ?";
        getJdbcTemplate(puid).update(updateSql,strategy.name(),puid,shopId,campaignId);
    }

    @Override
    public String getCampaignIdByName(Integer puid, Integer shopId, String name) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("type","sp")
                .equalTo("name", name)
                .build();
        return getByCondition(puid,"campaign_id", String.class, builder);
    }

    @Override
    public List<AmazonAdCampaignAll> listByTypeAndCampaignNames(int puid, String adType, List<String> campaignNames, List<Integer> shopIds) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid).equalTo("type", adType.toLowerCase())
                .inStrList("name", campaignNames.toArray(new String[0]))
                .inIntList("shop_id", shopIds.toArray(new Integer[0]))
                .build()
        );
    }

    /**
     * 被组合活动id id或状态或服务状态
     * @param puid
     * @param shopId
     * @param portfolioId
     * @param state
     * @param servingStatus
     * @param type
     * @return
     */
    @Override
    public List<String> getCampaignIdsByPortfolioIdOrStatusOrServingStatus(Integer puid, Integer shopId, String portfolioId, String state, String servingStatus, String type) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("shop_id", shopId);
        // 选择‘未分类’：没有广告组合的广告活动
        if (StringUtils.isNotBlank(portfolioId)) {
            // portfolioIds筛选掉-1的情况
            List<String> portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                    .filter(item -> !item.equals("-1")).collect(Collectors.toList());
            if ("-1".equals(portfolioId)) {
                builder.isNull(LogicType.AND, "portfolio_id");
            } else if (CollectionUtils.isNotEmpty(portfolioIds)) {
                if (portfolioId.indexOf("-1") >= 0) {
                    builder.and().leftBracket().in(LogicType.EPT, "portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
                    builder.isNull(LogicType.OR, "portfolio_id").rightBracket();
                } else {
                    builder.inStrList("portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
                }
            }
        }
        if (StringUtils.isNotBlank(state)) {
            List<String> statues = StringUtil.splitStr(state, ",");
            builder.inStrList("state", statues.toArray(new String[statues.size()]));
        }
        if (StringUtils.isNotBlank(servingStatus)) {
            List<String> servingStatuses = StringUtil.splitStr(servingStatus, ",");
            builder.inStrList("serving_status", servingStatuses.stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).filter(Objects::nonNull).flatMap(Collection::stream).toArray(String[]::new));
        }
        if (StringUtils.isNotBlank(type)) {
            builder.equalTo("type", type);
        }
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where " + builder.build().getSql());
        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, builder.build().getValues());
    }

    @Override
    public List<String> getCampaignIdsByPortfolioIdOrStatusOrServingStatus(Integer puid, Integer shopId, String portfolioId, String campaignId, String state, String servingStatus, String type) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("shop_id", shopId);
        // 选择‘未分类’：没有广告组合的广告活动
        if (StringUtils.isNotBlank(portfolioId)) {
            // portfolioIds筛选掉-1的情况
            List<String> portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                    .filter(item -> !item.equals("-1")).collect(Collectors.toList());
            if ("-1".equals(portfolioId)) {
                builder.isNull(LogicType.AND, "portfolio_id");
            } else if (CollectionUtils.isNotEmpty(portfolioIds)) {
                if (portfolioId.indexOf("-1") >= 0) {
                    builder.and().leftBracket().in(LogicType.EPT, "portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
                    builder.isNull(LogicType.OR, "portfolio_id").rightBracket();
                } else {
                    builder.inStrList("portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
                }
            }
        }
        if (StringUtils.isNotBlank(campaignId)) {
            List<String> campaignIds = StringUtil.splitStr(campaignId, ",");
            builder.inStrList("campaign_id", campaignIds.toArray(new String[campaignIds.size()]));
        }
        if (StringUtils.isNotBlank(state)) {
            List<String> statues = StringUtil.splitStr(state, ",");
            builder.inStrList("state", statues.toArray(new String[statues.size()]));
        }
        if (StringUtils.isNotBlank(servingStatus)) {
            List<String> servingStatuses = StringUtil.splitStr(servingStatus, ",");
            builder.inStrList("serving_status", servingStatuses.stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).filter(Objects::nonNull).flatMap(Collection::stream).toArray(String[]::new));
        }
        if (StringUtils.isNotBlank(type)) {
            builder.equalTo("type", type);
        }
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where " + builder.build().getSql());
        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, builder.build().getValues());
    }

    @Override
    public List<String> getNameByCampaignIds(Integer puid, Integer shopId, List<String> campaignIds) {
        StringBuilder sql = new StringBuilder("select name from t_amazon_ad_campaign_all where puid=? and shop_id=? ");
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(shopId);
        sql.append(SqlStringUtil.dealInList("campaign_id",campaignIds,args));
        return getJdbcTemplate(puid).queryForList(sql.toString(),args.toArray(), String.class);
    }

    @Override
    public List<String> queryCampaignIds4StartStop(ControlledObjectParam param) {
        StringBuilder sql = new StringBuilder("select campaign_id from " + getJdbcHelper().getTable() + " where puid=? and shop_id=?  ");
        List<Object> args = new ArrayList<>();
        args.add(param.getPuid());
        args.add(param.getShopId());

        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) {
            sql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIds(), args));
        }

        if (StartStopItemTypeEnum.CAMPAIGN.getValue().equalsIgnoreCase(param.getStartStopItemType())) {
            if (StringUtils.isNotBlank(param.getSearchValue())) {
                sql.append(" and name like ?");
                args.add("%" + param.getSearchValue() + "%");
            }
        }

        if (CollectionUtils.isNotEmpty(param.getPortfolioIds())) {
            if (param.getPortfolioIds().size() == 1 && param.getPortfolioIds().contains("-1")) {
                sql.append(" and portfolio_id is null ");
            } else if (param.getPortfolioIds().contains("-1")) {
                sql.append(" and ( ").append(SqlStringUtil.dealInListNotAnd("portfolio_id", param.getPortfolioIds(), args))
                        .append(" or portfolio_id is null ) ");
            } else {
                sql.append(SqlStringUtil.dealInList("portfolio_id", param.getPortfolioIds(), args));
            }
        }
        if (CollectionUtils.isNotEmpty(param.getAdTypeList())) {
            sql.append(SqlStringUtil.dealInList("type", param.getAdTypeList(),args));
        }
        return getJdbcTemplate(param.getPuid()).queryForList(sql.toString(), args.toArray(), String.class);
    }

    @Override
    public List<String> queryCampaignIds(ControlledObjectParam param) {
        StringBuilder sql = new StringBuilder("select campaign_id from " + getJdbcHelper().getTable() + " where puid=? and shop_id=?  ");
        List<Object> args = new ArrayList<>();
        args.add(param.getPuid());
        args.add(param.getShopId());
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            sql.append(" and name like ?");
            args.add("%" + param.getSearchValue() + "%");
        }
        if (CollectionUtils.isNotEmpty(param.getPortfolioIds())) {
            if (param.getPortfolioIds().size() == 1 && param.getPortfolioIds().contains("-1")) {
                sql.append(" and portfolio_id is null ");
            } else if (param.getPortfolioIds().contains("-1")) {
                sql.append(" and ( ").append(SqlStringUtil.dealInListNotAnd("portfolio_id", param.getPortfolioIds(), args))
                        .append(" or portfolio_id is null ) ");
            } else {
                sql.append(SqlStringUtil.dealInList("portfolio_id", param.getPortfolioIds(), args));
            }
        }
        if (CollectionUtils.isNotEmpty(param.getAdTypeList())) {
            sql.append(SqlStringUtil.dealInList("type", param.getAdTypeList(),args));
        }
        return getJdbcTemplate(param.getPuid()).queryForList(sql.toString(), args.toArray(), String.class);
    }

    @Override
    public void autoRuleUpdate(AdvertiseRuleTaskExecuteRecordV2Message message) {
        StringBuilder updateSql = new StringBuilder("update t_amazon_ad_campaign_all ");
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? and type = ? and campaign_id = ?");
        List<Object> args = Lists.newArrayList();
        if ("restore".equals(message.getOperation().name())) {
            updateSql.append(" set budget=?");
            args.add(message.getRecoveryAdjustment().getExecuteValue());
        } else {
            if ("stateClose".equals(message.getPerformOperation().get(0).getRuleAction().name())) {
                updateSql.append(" set state=?");
                args.add(message.getPerformOperation().get(0).getExecuteValue());
            } else if ("editCampaignPlacement".equals(message.getPerformOperation().get(0).getRuleAction().name())) {
                updateSql.append(" set adjustments=?");
                List<Adjustment> adjustments = new ArrayList<>();
                Adjustment adjustment = new Adjustment();
                Adjustment adjustment1 = new Adjustment();
                Adjustment adjustment2 = new Adjustment();
                adjustment.setPredicate(PlacementPageParam.placementPredicateEnum.placementTop.name());
                adjustment.setPercentage(Double.valueOf(message.getPerformOperation().get(0).getExecuteAdPlaceTopValue()));
                adjustment1.setPredicate(PlacementPageParam.placementPredicateEnum.placementProductPage.name());
                adjustment1.setPercentage(Double.valueOf(message.getPerformOperation().get(0).getExecuteAdPlaceProductValue()));
                adjustments.add(adjustment);
                adjustments.add(adjustment1);
                if (StringUtils.isNotBlank(message.getPerformOperation().get(0).getExecuteAdOtherValue())) {
                    adjustment2.setPredicate(PlacementPageParam.placementPredicateEnum.Other.getCode());
                    adjustment2.setPercentage(Double.valueOf(message.getPerformOperation().get(0).getExecuteAdOtherValue()));
                    adjustments.add(adjustment2);
                }
                args.add(JSONUtil.objectToJson(adjustments));
            } else if ("editBidStrategy".equals(message.getPerformOperation().get(0).getRuleAction().name())) {
                updateSql.append("set strategy = ?");
                args.add(CampaignStrategyV3.fromValue(message.getPerformOperation().get(0).getExecuteValue()).getOldValue());
            } else {
                updateSql.append(" set budget=?");
                args.add(message.getPerformOperation().get(0).getExecuteValue());
            }
        }
        updateSql.append(whereSql);
        args.add(message.getPuid());
        args.add(message.getShopId());
        if(AmazonAdvertiseType.SB.equals(message.getAdType())){
            args.add(Constants.SB);
        }else{
            args.add(message.getAdType().getReportType());
        }
        args.add(message.getItemId());
        getJdbcTemplate(message.getPuid()).update(updateSql.toString(),args.toArray());
    }

    @Override
    public Page<AmazonAdCampaignAll> getPlacementViewPage(Integer puid, PlacementViewParam param) {
        StringBuilder selectSql = new StringBuilder("select * from " + getJdbcHelper().getTable());
        StringBuilder countSql = new StringBuilder("select count(*) from " + getJdbcHelper().getTable());
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", param.getShopIdList().toArray())
                .equalTo("type", param.getType());
        if (StringUtils.isNotBlank(param.getCampaignIds()) && CollectionUtils.isNotEmpty(param.getAdCamgaignIdList())) {
            List<String> campaignIds = Arrays.asList(param.getCampaignIds().split(","));
            campaignIds.retainAll(param.getAdCamgaignIdList());
            builder.in("campaign_id", campaignIds.toArray());
        } else if (StringUtils.isNotBlank(param.getCampaignIds())) {
            List<String> campaignIds = Arrays.asList(param.getCampaignIds().split(","));
            builder.in("campaign_id", campaignIds.toArray());
        } else if (CollectionUtils.isNotEmpty(param.getAdCamgaignIdList())) {
            builder.in("campaign_id", param.getAdCamgaignIdList().toArray());
        }
        if (StringUtils.isNotBlank(param.getStrategyType())) {
            List<String> strategyTypeList = Arrays.asList(param.getStrategyType().split(","));
            builder.in("strategy", strategyTypeList.toArray());
        }
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> stateList = Arrays.asList(param.getStatus().split(","));
            builder.in("state", stateList.toArray());
        }
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> servingStatusList = Arrays.asList(param.getServingStatus().split(","));
            if (servingStatusList.size() > 0) {
                List<String> statusList = getServingStatus(servingStatusList);
                if (CollectionUtils.isNotEmpty(statusList)) {
                    builder.in("serving_status", statusList.toArray());
                }
            }
        }
        ConditionBuilder build = builder.build();
        selectSql.append(" where ").append(build.getSql()).append(" order by data_update_time desc, id desc ");
        countSql.append(" where ").append(build.getSql());

        Object[] args = build.getValues();
        return getPageResult(puid, param.getPageNo(), param.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdCampaignAll.class);
    }

    @Override
    public List<AmazonAdCampaignAll> getPlacementViewList(Integer puid, PlacementViewParam param) {
        StringBuilder selectSql = new StringBuilder("select * from " + getJdbcHelper().getTable());
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", param.getShopIdList().toArray())
                .equalTo("type", param.getType());
        if (CollectionUtils.isNotEmpty(param.getAdCamgaignIdList())) {
            builder.in("campaign_id", param.getAdCamgaignIdList().toArray());
        }
        if (StringUtils.isNotBlank(param.getStrategyType())) {
            List<String> strategyTypeList = Arrays.asList(param.getStrategyType().split(","));
            builder.in("strategy", strategyTypeList.toArray());
        }
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> stateList = Arrays.asList(param.getStatus().split(","));
            builder.in("state", stateList.toArray());
        }
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> servingStatusList = Arrays.asList(param.getServingStatus().split(","));
            if (servingStatusList.size() > 0) {
                List<String> statusList = getServingStatus(servingStatusList);
                if (CollectionUtils.isNotEmpty(statusList)) {
                    builder.in("serving_status", statusList.toArray());
                }
            }
        }
        builder.orderByDesc("data_update_time", "id");
        ConditionBuilder build = builder.build();

        selectSql.append(" where ").append(build.getSql());

        return getJdbcTemplate(puid).query(selectSql.toString(), build.getValues(), BeanPropertyRowMapper.newInstance(AmazonAdCampaignAll.class));
    }

    @Override
    public List<AmazonAdCampaignTargetingTypeBO> getTargetingTypeByCampaignIdList(Integer puid, Integer shopId, List<String> campaignIdList) {
        StringBuilder sql = new StringBuilder("select campaign_id, targeting_type from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? ");
        sql.append(" and shop_id = ? ");
        sql.append(" and campaign_id in ('").append(StringUtils.join(campaignIdList, "','")).append("')");
        return getJdbcTemplate(puid).query(sql.toString(), new BeanPropertyRowMapper<>(AmazonAdCampaignTargetingTypeBO.class), puid, shopId);
    }


    @Override
    public List<String> getDiagnoseCountCampaignId(DiagnoseCountParam diagnoseCountParam) {
        StringBuilder sql = new StringBuilder("select distinct campaign_id from t_amazon_ad_campaign_all where ");
        sql.append(" puid = ? and shop_id in ( ").append(StringUtils.join(diagnoseCountParam.getShopIdList(), ",")).append(" )");
        sql.append(" and type = ?");
        //通过广告产品的活动id过滤
        if (CollectionUtils.isNotEmpty(diagnoseCountParam.getAdCamgaignIdList())) {
            sql.append(" and campaign_id in ( '").append(StringUtils.join(diagnoseCountParam.getAdCamgaignIdList(), "','")).append("') ");
        }

        sql.append(" limit ").append(Constants.TOTALSIZELIMIT);  // 限制10万

        return getJdbcTemplate(diagnoseCountParam.getPuid()).query(sql.toString(), new SingleColumnRowMapper<String>(), diagnoseCountParam.getPuid(), diagnoseCountParam.getType());
    }

    @Override
    public void insertList4BatchCreate(Integer puid, List<AmazonAdCampaignAll> successCampaignAllList) {
        StringBuilder sql = new StringBuilder(" INSERT INTO ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" (puid, shop_id, campaign_id, profile_id, marketplace_id, portfolio_id, name, campaign_type, targeting_type, state, ");
        sql.append(" create_in_amzup, ad_target_type, type, budget, start_date, end_date, strategy, adjustments, tags, create_time, create_id) values ");
        List<Object> argsList = new ArrayList<>(successCampaignAllList.size());
        for (AmazonAdCampaignAll campaign : successCampaignAllList) {
            sql.append(" (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),");
            argsList.add(campaign.getPuid());
            argsList.add(campaign.getShopId());
            argsList.add(campaign.getCampaignId());
            argsList.add(campaign.getProfileId());
            argsList.add(campaign.getMarketplaceId());
            argsList.add(campaign.getPortfolioId());
            argsList.add(campaign.getName());
            argsList.add(campaign.getCampaignType());
            argsList.add(campaign.getTargetingType());
            argsList.add(campaign.getState());
            argsList.add(campaign.getCreateInAmzup());
            argsList.add(campaign.getAdTargetType());
            argsList.add(campaign.getType());
            argsList.add(campaign.getBudget());
            argsList.add(campaign.getStartDate());
            argsList.add(campaign.getEndDate());
            argsList.add(campaign.getStrategy());
            argsList.add(campaign.getAdjustments());
            argsList.add(campaign.getTags());
            argsList.add(campaign.getCreateTime());
            argsList.add(campaign.getCreateId());
        }
        sql.deleteCharAt(sql.length() - 1);
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<AmazonAdCampaignAll> listByCampaignIds(Integer puid, List<String> campaignIds) {
        StringBuilder sql = new StringBuilder("select id, campaign_id from t_amazon_ad_campaign_all where ");
        sql.append(" 1=1 ");
        //通过广告产品的活动id过滤
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(" and campaign_id in ( '").append(StringUtils.join(campaignIds, "','")).append("') ");
        }

        sql.append(" limit ").append(Constants.TOTALSIZELIMIT);  // 限制10万

        return getJdbcTemplate(puid).query(sql.toString(), BeanPropertyRowMapper.newInstance(AmazonAdCampaignAll.class));
    }

    @Override
    public List<String> getCampaignIdsByStates(Integer puid, Integer shopId, List<String> states, String type) {
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where puid=? and shop_id=? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(shopId);
        sql.append(SqlStringUtil.dealInList("state", states, args));
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), args.toArray(), String.class);
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }


    @Override
    public Map<String, AmazonAdCampaignAll> getSdCampaignByCampaignIds(Integer puid, Integer shopId, List<String> campaignIds) {

        StringBuilder sql = new StringBuilder("select campaign_id, tactic,cost_type from t_amazon_ad_campaign_all where ");
        sql.append(" puid = ? and shop_id = ? ");
        sql.append(" and type = 'sd' ");
        List<Object> args = Lists.newArrayList(puid, shopId);
        sql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, args));
        List<AmazonAdCampaignAll> campaignAlls = getJdbcTemplate(puid).query(sql.toString(), getMapper(), args.toArray());
        if (CollectionUtils.isNotEmpty(campaignAlls)) {
            return campaignAlls.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));
        } else {
            return new HashMap<String, AmazonAdCampaignAll>();
        }
    }


    /**
     * 0 下标 Tactic 1 下标 cost_type
     * @param puid
     * @param shopId
     * @param campaignId
     * @return {@link List}<{@link String}>
     */
    @Override
    public List<String> getSdTacticAndCostTypeByCampaignIds(Integer puid, Integer shopId, String campaignId) {
        String cacheKey = String.format(RedisConstant.SELLFOX_AD_SD_CAMPAIGN_TYPE, puid, shopId, campaignId);
        String value = redisService.getString(cacheKey);
        if (StringUtils.isBlank(value)) {
            StringBuilder sql = new StringBuilder("select campaign_id, tactic,cost_type from t_amazon_ad_campaign_all where ");
            sql.append(" puid = ? and shop_id = ? and campaign_id = ? ");
            sql.append(" and type = 'sd' ");
            List<Object> args = Lists.newArrayList(puid, shopId, campaignId);
            List<AmazonAdCampaignAll> campaignAlls = getJdbcTemplate(puid).query(sql.toString(), getMapper(), args.toArray());
            if (CollectionUtils.isNotEmpty(campaignAlls)) {
                ArrayList<String> strings = Lists.newArrayList(campaignAlls.get(0).getTactic(), campaignAlls.get(0).getCostType());
                redisService.set(cacheKey, StringUtil.joinString(strings, ","), 1, TimeUnit.DAYS);
                return strings;

            } else {
                return null;
            }
        }
        return StringUtil.splitStr(value, ",");
    }

    @Override
    public Page<CampaignInfoPageVo> getAllCampaignPage(Integer puid, CampaignPageParam param) {
    List<Object> argsList = new ArrayList<>();
    StringBuffer countSql = new StringBuffer("SELECT COUNT(*) FROM t_amazon_ad_campaign_all ");
    StringBuffer selectSql = new StringBuffer("SELECT * ");
    selectSql.append(" FROM t_amazon_ad_campaign_all ");
    String whereSql = getCampaignPageWhereSql(puid, param, null, argsList);
    selectSql.append(whereSql);
    AdCampaignDefaultOrderEnum orderEnum = AdCampaignDefaultOrderEnum.getAdCampaignDefaultOrderEnumByKey(param.getOrderField());
    if (StringUtils.isNotBlank(param.getOrderType()) && Objects.nonNull(orderEnum)) {
        selectSql.append(" order by ").append(orderEnum.getOrderField()).append(OrderTypeEnum.desc.getType().equals(param.getOrderType()) ? " desc" : "").append(" ,id desc ");
    } else {
        selectSql.append(" order by id desc ");
    }
    countSql.append(whereSql);
    Object[] args = argsList.toArray();
    return getPageResultByClass(puid, param.getPageNo(), param.getPageSize(), countSql.toString(), args, selectSql.toString(), args, CampaignInfoPageVo.class);
    }

    private String getCampaignPageWhereSql(Integer puid, CampaignPageParam param, List<String> campaignList, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid = ? ");
        argsList.add(puid);
        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getCampaignId())){
            whereSql.append(" and campaign_id = ? ");
            argsList.add(param.getCampaignId());
        }
        if (StringUtils.isNotBlank(param.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", StringUtil.splitStr(param.getCampaignIds()), argsList));
        }

        if (CollectionUtils.isNotEmpty(campaignList)) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignList, argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and name like ? ");
            argsList.add("%" + param.getSearchValue().trim() + "%");
        }
        if (StringUtils.isNotBlank(param.getType())) {
//            whereSql.append(" and type = ? ");
//            argsList.add(param.getType());
            whereSql.append(SqlStringUtil.dealInList("type", StringUtil.splitStr(param.getType()), argsList));
        }
        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, argsList));
        }
        //过滤服务状态
        if(StringUtils.isNotBlank(param.getServingStatus())){
            List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
            List<String> servings = list.stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).flatMap(Collection::stream).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(servings)){
                whereSql.append(SqlStringUtil.dealInList("serving_status",servings,argsList));
            }
        }
        if(StringUtils.isNotBlank(param.getPortfolioId()) && param.getPortfolioId().equals("-1")){
            whereSql.append(" and portfolio_id is null ");
        }
        if (StringUtils.isNotBlank(param.getStrategyType())) {
            whereSql.append(" and strategy = ?");
            argsList.add(param.getStrategyType());
        }
        //预算状态
        if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.budgetExceeded.getCode())) {
            whereSql.append(" and serving_status = ? ");
            argsList.add(AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
        } else if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.underBudget.getCode())){
            whereSql.append(" and serving_status != ? ");
            argsList.add(AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
        }
        if(StringUtils.isNotBlank(param.getCostType())){
            whereSql.append(" and cost_type = ? ");
            argsList.add(param.getCostType());
        }
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (StringUtils.isNotBlank(param.getFilterTargetType())) {  //活动类型  auto自动投放,manual手动投放
                List<String> typeList = StringUtil.splitStr(param.getFilterTargetType(), ",");
                whereSql.append(SqlStringUtil.dealInList("ad_target_type", typeList, argsList));
            }
            if (param.getDailyBudgetMin() != null) {   //每日预算
                whereSql.append(" and budget >= ? ");
                argsList.add(param.getDailyBudgetMin());
            }
            if (param.getDailyBudgetMax() != null) {
                whereSql.append(" and budget <= ? ");
                argsList.add(param.getDailyBudgetMax());
            }
            if (StringUtils.isNotBlank(param.getFilterStartDate())) {  // 日期
                whereSql.append(" and start_date >= ? ");
                argsList.add(param.getFilterStartDate());
            }
            if (StringUtils.isNotBlank(param.getFilterEndDate())) {
                whereSql.append(" and end_date <= ? ");
                argsList.add(param.getFilterEndDate());
            }
        }
        return whereSql.toString();
    }

    @Override
    public List<String> getCampaignIdListByParamAndIds(Integer puid, CampaignPageParam param, List<String> campaignIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all ");
        sb.append(this.getCampaignPageWhereSql(puid, param, campaignIdList, argsList));
        return getJdbcTemplate(puid).queryForList(sb.toString(), argsList.toArray(), String.class);
    }

    @Override
    public List<CampaignInfoPageVo> getCampaignPageVoListByCampaignIdList(Integer puid, CampaignPageParam param, List<String> campaignIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder("SELECT * ");
        selectSql.append(" FROM t_amazon_ad_campaign_all c ");
        selectSql.append(" where c.puid = ? and c.shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            selectSql.append(SqlStringUtil.dealInList("c.campaign_id", campaignIdList, argsList));
            selectSql.append(" order by field(campaign_id, ").append(StringUtil.joinString(campaignIdList)).append(")");
        }
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), new ObjectMapper<>(CampaignInfoPageVo.class));
    }

    @Override
    public List<AllCampaignOrderBo> getCampaignIdAndOrderFieldList(Integer puid, CampaignPageParam param, List<String> campaignIdList, String orderField) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select campaign_id id")
                .append(StringUtils.isNotBlank(orderField) ? "," + orderField + " orderField" : "")
                .append(" from t_amazon_ad_campaign_all ");
        sb.append(this.getCampaignPageWhereSql(puid, param, campaignIdList, argsList));
        return getJdbcTemplate(puid).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AllCampaignOrderBo.class));
    }


    /**
     * type null 查所有
     *
     * @param puid
     * @param shopId
     * @param campaignId
     * @return
     */
    @Override
    public List<String> getVcpmCampaignIdsByCampaignIds(Integer puid, Integer shopId, List<String> campaignId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("type", Constants.SD)
                .equalTo("cost_type", Constants.SD_REPORT_VCPM)
                .inStrList("campaign_id", campaignId.toArray(new String[]{}));

        return listDistinctFieldByCondition(puid,"campaign_id", builder.build(), String.class);
    }

    @Override
    public List<FeedTargetCampaignDto> getFeedTargetListByCampaignIds(Integer puid, List<String> campaignIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select campaign_id as campaignId, cost_type as costType")
                .append(" from t_amazon_ad_campaign_all where puid = ?");
        argsList.add(puid);
        sql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
        return getJdbcTemplate(puid).query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(FeedTargetCampaignDto.class));

    }

    @Override
    public List<AmazonAdCampaignAll> listByGetCampaignIds(Integer puid, List<String> campaignIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("campaign_id", campaignIds.toArray())
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<String> getCampaignIds(ProcessTaskParam param) {
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where puid= ? ");
        List<Object> objects = Lists.newArrayList(param.getPuid());
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            sql.append(" and name like ? ");
            objects.add("%" + param.getSearchValue() + "%");
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) {
            sql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIds(), objects));
        }
        if (CollectionUtils.isNotEmpty(param.getPortfolioIds())) {
            if (param.getPortfolioIds().size() == 1 && param.getPortfolioIds().contains("-1")) {
                sql.append(" and portfolio_id is null ");
            } else if (param.getPortfolioIds().contains("-1")) {
                sql.append(" and ( ").append(SqlStringUtil.dealInListNotAnd("portfolio_id", param.getPortfolioIds(), objects))
                        .append(" or portfolio_id is null ) ");
            } else {
                sql.append(SqlStringUtil.dealInList("portfolio_id", param.getPortfolioIds(), objects));
            }
        }
        return getJdbcTemplate(param.getPuid()).queryForList(sql.toString(), objects.toArray(), String.class);
    }

    @Override
    public List<String> getAutoRuleCampaignIds(com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam param) {
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where puid= ? and shop_id = ? ");
        List<Object> objects = Lists.newArrayList(param.getPuid(), param.getShopId());
        if (CollectionUtils.isNotEmpty(param.getItemIdList())) {
            sql.append(SqlStringUtil.dealInList("campaign_id", param.getItemIdList(), objects));
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            sql.append(" and name like ? ");
            objects.add("%" + param.getSearchValue() + "%");
        }
        if (StringUtils.isNotBlank(param.getState())) {
            sql.append(" and state = ?");
            objects.add(param.getState());
        }
        if (CollectionUtils.isNotEmpty(param.getAdTypeList())) {
            sql.append(SqlStringUtil.dealInList("type", param.getAdTypeList(), objects));
        }

        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            List<String> servings = param.getServingStatusList().stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(servings)) {
                sql.append(SqlStringUtil.dealInList("serving_status", servings, objects));
            }
        }

        if (CollectionUtils.isNotEmpty(param.getPortfolioIds())) {
            if (param.getPortfolioIds().size() == 1 && param.getPortfolioIds().contains("-1")) {
                sql.append(" and portfolio_id is null ");
            } else if (param.getPortfolioIds().contains("-1")) {
                sql.append(" and ( ").append(SqlStringUtil.dealInListNotAnd("portfolio_id", param.getPortfolioIds(), objects))
                        .append(" or portfolio_id is null ) ");
            } else {
                sql.append(SqlStringUtil.dealInList("portfolio_id", param.getPortfolioIds(), objects));
            }
        }
        return getJdbcTemplate(param.getPuid()).queryForList(sql.toString(), objects.toArray(), String.class);
    }

    @Override
    public List<GpsCampaign> getGpsCampaignByName(Integer puid, List<String> nameList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select campaign_id as campaignId, name as campaignName")
                .append(" from t_amazon_ad_campaign_all where puid = ? and state != 'archived' ");
        argsList.add(puid);
        sql.append(SqlStringUtil.dealInList("name", nameList, argsList));
        return getJdbcTemplate(puid).query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(GpsCampaign.class));
    }

    @Override
    public AmazonAdCampaignAll getByLimitTime(int puid, int shopId, int seconds) {
        ConditionBuilder build = new ConditionBuilder.Builder()
            .equalToWithoutCheck("puid", puid)
            .equalToWithoutCheck("shop_id", shopId)
            .greaterThan("update_time", LocalDateTime.now().minusSeconds(seconds))
            .limit(1)
            .build();
        return getByCondition(puid, build);
    }

    @Override
    public Page<AmazonAdCampaignAll> getMultiShopCampaignList(Integer puid, MultiShopCampaignListParam param) {
        StringBuilder sql = new StringBuilder("SELECT * FROM t_amazon_ad_campaign_all ");
        StringBuilder countSql = new StringBuilder("SELECT count(id) FROM `t_amazon_ad_campaign_all`");
        List<Object> args = new ArrayList<>();
        StringBuilder whereSql = new StringBuilder(" where puid = ? ");
        args.add(puid);
        whereSql.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), args));

        if (CollectionUtils.isNotEmpty(param.getMarketplaceId())) {
            whereSql.append(SqlStringUtil.dealInList("marketplace_id", param.getMarketplaceId(), args));
        }

        if (CollectionUtils.isNotEmpty(param.getStateList())) {
            whereSql.append(SqlStringUtil.dealInList("state", param.getStateList(), args));
        }

        if (CollectionUtils.isNotEmpty(param.getAdTypeList())) {
            whereSql.append(SqlStringUtil.dealInList("type", param.getAdTypeList(), args));
        }

        if (CollectionUtils.isNotEmpty(param.getPortfolioIdList())) {
            whereSql.append(" and ( portfolio_id in ");
            whereSql.append(" ( " + SqlStringUtil.toInSqlStrs(param.getPortfolioIdList()) + " ) ");
            if (param.getPortfolioIdList().contains("-1")) {
                whereSql.append("or portfolio_id is null ");
            }
            whereSql.append(" ) ");
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), args));
        }

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            if ("exact".equals(param.getSearchType())) {
                whereSql.append(" and lower(name) = ? ");
                args.add(param.getSearchValue().trim().toLowerCase());
            }else {
                whereSql.append(" and lower(name) like ? ");
                args.add("%" + param.getSearchValue().trim().toLowerCase() + "%");
            }
        }
        if (StringUtils.isNotBlank(param.getAdTargetType())) {
            whereSql.append(" and ad_target_type = ? ");
            args.add(param.getAdTargetType());
        }

        List<String> searchValueList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            for (String value : param.getSearchValueList()) {
                searchValueList.add(value.toLowerCase());
            }
        }
        if (CollectionUtils.isNotEmpty(searchValueList)) {
            whereSql.append(" and lower(name) in ");
            whereSql.append(" ( " + SqlStringUtil.toInSqlStrs(searchValueList) + " ) ");
        }

        if (Boolean.TRUE.equals(param.getNeedOrder())) {
            whereSql.append(" order by")
                    .append(" case state")
                    .append(" when 'enabled' then 1")
                    .append(" when 'paused' then 2")
                    .append(" else 3")
                    .append(" end, ")
                    .append(" create_time desc ");
        }

        sql.append(whereSql);
        countSql.append(whereSql);
        return this.getPageResult(puid,
                param.getPageNo(),
                param.getPageSize(),
                countSql.toString(),
                args.toArray(),
                sql.toString(),
                args.toArray(),
                AmazonAdCampaignAll.class);
    }

    @Override
    public List<MultiShopCampaignIdVo> getMultiShopCampaignId(Integer puid, List<Integer> shopIdList, List<String> portfolioIdList, List<String> campaignIdList, List<String> adTypeList) {
        StringBuilder sql = new StringBuilder("SELECT type ad_type, campaign_id FROM t_amazon_ad_campaign_all ");
        List<Object> args = new ArrayList<>();
        StringBuilder whereSql = new StringBuilder(" where puid = ? ");
        args.add(puid);
        whereSql.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));

        if (CollectionUtils.isNotEmpty(portfolioIdList)) {
            whereSql.append(" and ( portfolio_id in ");
            whereSql.append(" ( " + SqlStringUtil.toInSqlStrs(portfolioIdList) + " ) ");
            if (portfolioIdList.contains("-1")) {
                whereSql.append("or portfolio_id is null ");
            }
            whereSql.append(" ) ");
        }

        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, args));
        }

        if (CollectionUtils.isNotEmpty(adTypeList)) {
            whereSql.append(SqlStringUtil.dealInList("type", adTypeList, args));
        }

        sql.append(whereSql);
        return getJdbcTemplate(puid).query(sql.toString(), args.toArray(), new BeanPropertyRowMapper<>(MultiShopCampaignIdVo.class));
    }

    @Override
    public List<String> getCampaignIdByShopAndPortfolio(Integer puid, List<Integer> shopIdList, List<String> portfolioIdList) {
        StringBuilder sql = new StringBuilder("SELECT campaign_id FROM t_amazon_ad_campaign_all ");
        List<Object> args = new ArrayList<>();
        StringBuilder whereSql = new StringBuilder(" where puid = ? ");
        args.add(puid);
        whereSql.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));

        if (CollectionUtils.isNotEmpty(portfolioIdList)) {
            whereSql.append(" and ( portfolio_id in ");
            whereSql.append(" ( " + SqlStringUtil.toInSqlStrs(portfolioIdList) + " ) ");
            if (portfolioIdList.contains("-1")) {
                whereSql.append("or portfolio_id is null ");
            }
            whereSql.append(" ) ");
        }

        sql.append(whereSql);
        return getJdbcTemplate(puid).query(sql.toString(), new SingleColumnRowMapper<String>(), args.toArray());

    }

    @Override
    public List<AmazonAdCampaignAll> getByShopCampaignPair(Integer puid, List<MultiShopCampaignListParam> paramList) {
        StringBuilder sql = new StringBuilder("SELECT * FROM `t_amazon_ad_campaign_all` where puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);

        String multiIn = SqlStringUtil.dealMultiInList(Arrays.asList("shop_id", "campaign_id"), paramList, args,
                Arrays.asList(i -> String.valueOf(i.getShopId()), MultiShopCampaignListParam::getCampaignId));
        sql.append(multiIn);

        return getJdbcTemplate(puid).query(sql.toString(), args.toArray(), BeanPropertyRowMapper.newInstance(AmazonAdCampaignAll.class));
    }

    @Override
    public List<AmazonAdCampaignAllBo> listBoByCampaignIds(Integer puid, List<Integer> shopIdList, List<String> campaignIdList) {
        StringBuilder sb = new StringBuilder();
        sb.append(" select id, puid, shop_id shopId, marketplace_id marketplaceId, campaign_id campaignId, type, state, name, budget, budget_type budgetType ")
                .append(" from ").append(this.getJdbcHelper().getTable());
        List<Object> argsList = new ArrayList<>();
        sb.append(" where puid = ? ");
        argsList.add(puid);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(campaignIdList)) {
            sb.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
        }
        return getJdbcTemplate(puid).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AmazonAdCampaignAllBo.class));
    }

    @Override
    public List<AmazonAdCampaignAllBo> listByShopIdListAndCampaignIdList(Integer puid, Collection<Integer> shopIdList, Collection<String> campaignIdList) {
        StringBuilder sb = new StringBuilder();
        sb.append(" select campaign_id campaignId, name ")
                .append(" from ").append(this.getJdbcHelper().getTable());
        List<Object> argsList = new ArrayList<>();
        sb.append(" where puid = ? ");
        argsList.add(puid);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("shop_id", new ArrayList<>(shopIdList), argsList));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(campaignIdList)) {
            sb.append(SqlStringUtil.dealInList("campaign_id", new ArrayList<>(campaignIdList), argsList));
        }
        return getJdbcTemplate(puid).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AmazonAdCampaignAllBo.class));
    }

    @Override
    public List<AmazonSdAdCampaignCostTypeBo> listSdCostTypeBoByCampaignIds(Integer puid, List<Integer> shopIdList, List<String> campaignIdList) {
        StringBuilder sb = new StringBuilder();
        sb.append(" select campaign_id campaignId, cost_type costType ")
                .append(" from ").append(this.getJdbcHelper().getTable());
        List<Object> argsList = new ArrayList<>();
        sb.append(" where puid = ? ");
        argsList.add(puid);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(campaignIdList)) {
            sb.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
        }
        return getJdbcTemplate(puid).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AmazonSdAdCampaignCostTypeBo.class));
    }


    @Override
    public List<String> getCampaignIdsByPortfolioIdOrStatusOrServingStatus(Integer puid, List<Integer> shopIds, String portfolioId, String state, String servingStatus, List<String> campaignIds, String type) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.in("shop_id", shopIds.toArray());
        // 选择‘未分类’：没有广告组合的广告活动
        if (StringUtils.isNotBlank(portfolioId)) {
            // portfolioIds筛选掉-1的情况
            List<String> portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                    .filter(item -> !item.equals("-1")).collect(Collectors.toList());
            if ("-1".equals(portfolioId)) {
                builder.isNull(LogicType.AND, "portfolio_id");
            } else if (CollectionUtils.isNotEmpty(portfolioIds)) {
                if (portfolioId.indexOf("-1") >= 0) {
                    builder.and().leftBracket().in(LogicType.EPT, "portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
                    builder.isNull(LogicType.OR, "portfolio_id").rightBracket();
                } else {
                    builder.inStrList("portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
                }
            }
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            builder.in("campaign_id", campaignIds.toArray());
        }

        if (StringUtils.isNotBlank(state)) {
            List<String> statues = StringUtil.splitStr(state, ",");
            builder.inStrList("state", statues.toArray(new String[statues.size()]));
        }
        if (StringUtils.isNotBlank(servingStatus)) {
            List<String> servingStatuses = StringUtil.splitStr(servingStatus, ",");
            builder.inStrList("serving_status", servingStatuses.stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).filter(Objects::nonNull).flatMap(Collection::stream).toArray(String[]::new));
        }
        if (StringUtils.isNotBlank(type)) {
            List<String> types = StringUtil.splitStr(type, ",");
            builder.inStrList("type", types.toArray(new String[0]));
        }
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where " + builder.build().getSql());
        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, builder.build().getValues());
    }

    @Override
    public int getValidShopCountByCondition(Integer puid, Integer shopId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
            .equalTo("puid", puid)
            .equalTo("shop_id", shopId)
            .equalTo("state", "enabled");
        return getCountByCondition(puid, builder.build());
    }


    @Override
    public List<String> queryArchivedByCampaignIdList(Integer puid, Integer shopId, List<String> campaignIdList) {

        StringBuilder sql = new StringBuilder();
        sql.append("select campaign_id itemId from ");
        sql.append(this.getJdbcHelper().getTable());
        List<Object> args = new ArrayList<>();
        sql.append(" where puid = ? and shop_id = ? and state = 'archived' ");
        args.add(puid);
        args.add(shopId);
        sql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, args));

        return getJdbcTemplate(puid).query(sql.toString(), new SingleColumnRowMapper<String>(), args.toArray());
    }

    @Override
    public List<String> getCampaignIdListByPortfolioIdAndType(Integer puid, Integer shopId, List<String> portfolioIds, List<String> adTypeList) {
        StringBuilder sql = new StringBuilder("select campaign_id from t_amazon_ad_campaign_all where puid= ? and shop_id= ?");
        List<Object> objects = Lists.newArrayList(puid, shopId);
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioIds.size() == 1 && portfolioIds.contains("-1")) {
                sql.append(" and portfolio_id is null ");
            } else if (portfolioIds.contains("-1")) {
                sql.append(" and ( ").append(SqlStringUtil.dealInListNotAnd("portfolio_id", portfolioIds, objects))
                        .append(" or portfolio_id is null ) ");
            } else {
                sql.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, objects));
            }
        }
        if (CollectionUtils.isNotEmpty(adTypeList)) {
            sql.append(SqlStringUtil.dealInList("type", adTypeList, objects));
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), objects.toArray(), String.class);
    }

    @Override
    public List<CampaignIdWithAdTypeVo> getCampaignIdListByPortfolioIdWithType(Integer puid, Integer shopId, List<String> portfolioIds, List<String> adTypeList) {
        StringBuilder sql = new StringBuilder("select campaign_id campaignId, type adType from t_amazon_ad_campaign_all where puid= ? and shop_id= ?");
        List<Object> objects = Lists.newArrayList(puid, shopId);
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioIds.size() == 1 && portfolioIds.contains("-1")) {
                sql.append(" and portfolio_id is null ");
            } else if (portfolioIds.contains("-1")) {
                sql.append(" and ( ").append(SqlStringUtil.dealInListNotAnd("portfolio_id", portfolioIds, objects))
                        .append(" or portfolio_id is null ) ");
            } else {
                sql.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, objects));
            }
        }
        if (CollectionUtils.isNotEmpty(adTypeList)) {
            sql.append(SqlStringUtil.dealInList("type", adTypeList, objects));
        }
        return getJdbcTemplate(puid).query(sql.toString(), new BeanPropertyRowMapper<>(CampaignIdWithAdTypeVo.class), objects.toArray());
    }

    @Override
    public List<AmazonAdCampaignAll> getByCampaignIdsAndShopIdListAndMarketplaceIdList(Integer puid, List<Integer> shopIds, List<String> marketplaceIds, List<String> ids, String type) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopIds.toArray())
                .inStrList("campaign_id", ids.toArray(new String[]{}));
        if (StringUtils.isNotBlank(type)) {
            builder.equalTo("type", type);
        }
        if (CollectionUtils.isNotEmpty(marketplaceIds)) {
            builder.in("marketplace_id", marketplaceIds.toArray(new String[]{}));
        }
        return listByCondition(puid, builder.build());
    }
}