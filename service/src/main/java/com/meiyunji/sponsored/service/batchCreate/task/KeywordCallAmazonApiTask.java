package com.meiyunji.sponsored.service.batchCreate.task;

import com.amazon.advertising.spV3.enumeration.SpV3MatchTypeEnum;
import com.amazon.advertising.spV3.enumeration.SpV3StateEnum;
import com.amazon.advertising.spV3.keyword.CreateSpKeywordV3Response;
import com.amazon.advertising.spV3.keyword.KeywordSpV3Client;
import com.amazon.advertising.spV3.keyword.ListSpKeywordV3Response;
import com.amazon.advertising.spV3.keyword.entity.CreateKeywordEntityV3;
import com.amazon.advertising.spV3.keyword.entity.KeywordExtendEntityV3;
import com.amazon.advertising.spV3.keyword.entity.KeywordSuccessResultV3;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchKeywordDao;
import com.meiyunji.sponsored.service.batchCreate.dto.task.BatchCreateReturnDto;
import com.meiyunji.sponsored.service.batchCreate.dto.task.CallAmazonApiTaskResultDto;
import com.meiyunji.sponsored.service.batchCreate.enums.AdAmazonErrorTypeEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchConstants;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdLevelStatusEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdTaskLevelEnum;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchKeyword;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchProduct;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeyword;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdKeyword;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 关键词批量处理任务
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-11-24  14:20
 */
@Component
@Slf4j
public class KeywordCallAmazonApiTask extends AbstractCallAmazonApiTask {
    @Autowired
    private IAmazonAdBatchKeywordDao amazonAdBatchKeywordDao;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private TaskStatusHelper taskStatusHelper;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Autowired
    private IDorisService dorisService;

    public void call(CallAmazonApiTaskResultDto resultDto) {
        log.info("sp batch create, create keyword, batch traceId: {}, taskId: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId());
        if (CollectionUtils.isEmpty(resultDto.getSuccessIdList())) {
            return ;
        }
        Integer puid = resultDto.getPuid();
        Integer shopId = resultDto.getShopId();
        Long taskId = resultDto.getTaskId();
        String traceId = resultDto.getTraceDto().getTraceId();
        //1、获取redisson分布式锁，锁标识为："sp_batch:keyword:"+ task_id;
        RLock lock = redissonClient.getLock(SpBatchCreateAdTaskLevelEnum.LEVEL_KEYWORD.getLockKey() + taskId);
        boolean b = false;
        try {
            b = lock.tryLock(SpBatchConstants.TRY_LOCK_SECONDS, SpBatchConstants.LOCK_MINUTES, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.info("keyword task tryLock err,puid：{},shopId:{},taskId:{},traceId:{}", puid, shopId, taskId, traceId);
            return ;
        }
        if (!b) {
            log.info("keyword task in progress,puid：{},shopId:{},taskId:{},traceId:{}", puid, shopId, taskId, traceId);
            return ;
        }

        List<AmazonAdBatchKeyword> keywords = new ArrayList<>();
        try {
            ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(resultDto.getShopId(), resultDto.getPuid());
            //2、根据传入的id列表获取不为成功或者终止状态的关键词集合
            List<Long> ids = resultDto.getSuccessIdList();
            if (resultDto.isCurrentLevel()) {
                keywords = amazonAdBatchKeywordDao.listByIdList(puid, shopId, ids,
                        Lists.newArrayList(SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode()));
            } else {
                keywords = amazonAdBatchKeywordDao.listByGroupIdList(puid, shopId, taskId, ids,
                        Lists.newArrayList(SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode()));
            }
            if (CollectionUtils.isEmpty(keywords)) {
                return ;
            }
            //3、分片请求亚马逊返回列表分成成功失败可重试三种类型
            Map<Integer, String> succMap = new HashMap<>();
            Map<Integer, String> errMap = new HashMap<>();
            List<Integer> retryList = new ArrayList<>();
            List<List<AmazonAdBatchKeyword>> keywordsPartition = Lists.partition(keywords, SpBatchConstants.REQUEST_PARTITION_SIZE);
            for (int i = 0; i < keywordsPartition.size(); i++) {
                BatchCreateReturnDto batchCreateReturnDto = this.amazonApiCreateByPartition(traceId, taskId, shop, keywordsPartition.get(i), i * SpBatchConstants.REQUEST_PARTITION_SIZE);
                succMap.putAll(batchCreateReturnDto.getSuccMap());
                errMap.putAll(batchCreateReturnDto.getErrMap());
                retryList.addAll(batchCreateReturnDto.getRetryList());
            }

            //4、成功处理
            if (succMap.size() > 0) {
                List<AmazonAdKeyword> succKeywords = new ArrayList<>();
                Map<Long, String> succBatchKeywordIdMap = new HashMap<>();
                Date date = new Date();
                for (Map.Entry<Integer, String> succMapEntry : succMap.entrySet()) {
                    AmazonAdBatchKeyword amazonAdBatchKeyword = keywords.get(succMapEntry.getKey());
                    succBatchKeywordIdMap.put(amazonAdBatchKeyword.getId(), succMapEntry.getValue());
                    //构建日志的po
                    AmazonAdKeyword amazonAdKeyword = this.batchKeyword2Keyword(amazonAdBatchKeyword);
                    amazonAdKeyword.setKeywordId(succMapEntry.getValue());
                    amazonAdKeyword.setCreateTime(date);
                    amazonAdKeyword.setUpdateTime(date);
                    succKeywords.add(amazonAdKeyword);
                }
                //插入成功的关键词
                amazonAdKeywordDaoRoutingService.insertOnDuplicateKeyUpdate(puid, succKeywords, Constants.BIDDABLE);
                //写入doris
                saveDoris(succKeywords);
                //修改当前层级任务的状态为成功
                amazonAdBatchKeywordDao.updateSuccTaskStatusByIdList(puid, succBatchKeywordIdMap);
                //记录成功日志
                this.collectSuccLog(succKeywords, resultDto.getLoginIp());
                log.info("sp batch create, create keyword success, batch traceId: {}, taskId: {}, succBatchProductIdMap: {}", traceId, resultDto.getTaskId(), succBatchKeywordIdMap);
            }

            //5、失败处理
            List<AmazonAdKeyword> errKeywords = new ArrayList<>();
            if (errMap.size() > 0) {
                Map<Long, String> idMsgMap = new HashMap<>();
                for (Map.Entry<Integer, String> errMapEntry : errMap.entrySet()) {
                    AmazonAdBatchKeyword amazonAdBatchKeyword = keywords.get(errMapEntry.getKey());
                    idMsgMap.put(amazonAdBatchKeyword.getId(), errMapEntry.getValue());
                    //构建日志po
                    AmazonAdKeyword amazonAdKeyword = this.batchKeyword2Keyword(amazonAdBatchKeyword);
                    amazonAdKeyword.setError(errMapEntry.getValue());
                    errKeywords.add(amazonAdKeyword);
                }
                //修改当前层级任务状态为失败
                taskStatusHelper.batchUpdateErrStatus(puid, taskId, idMsgMap, SpBatchCreateAdTaskLevelEnum.LEVEL_KEYWORD,  null, true);
                log.info("sp batch create, create keyword error, batch traceId: {}, taskId: {}, idMsgMap: {}", traceId, resultDto.getTaskId(), idMsgMap);
            }

            //6、重试处理
            if (retryList.size() > 0) {
                List<Long> retryIdList = new ArrayList<>();
                Map<Long, String> errIdMap = new HashMap<>();
                for (Integer index : retryList) {
                    AmazonAdBatchKeyword amazonAdBatchKeyword = keywords.get(index);
                    AmazonAdKeyword amazonAdKeyword = this.batchKeyword2Keyword(amazonAdBatchKeyword);
                    //若重试超过三次则直接置为失败
                    if (amazonAdBatchKeyword.getExecuteCount() + 1 >= SpBatchConstants.EXECUTE_COUNT_LIMIT) {
                        errIdMap.put(amazonAdBatchKeyword.getId(), SpBatchConstants.RETRY_ERROR_MSG);
                        amazonAdKeyword.setError(SpBatchConstants.RETRY_ERROR_MSG);
                    } else {
                        retryIdList.add(amazonAdBatchKeyword.getId());
                        amazonAdKeyword.setError(SpBatchConstants.RETRY_MSG);
                    }
                    errKeywords.add(amazonAdKeyword);
                }
                if (CollectionUtils.isNotEmpty(retryIdList)) {
                    amazonAdBatchKeywordDao.updateRetryTaskStatusByIdList(puid, retryIdList, DateUtil.addSecond(new Date(), SpBatchConstants.RETRY_AFTER_SECONDS));
                    log.info("sp batch create, create keyword retry, batch traceId: {}, taskId: {}, retryIdList: {}", traceId, resultDto.getTaskId(), StringUtils.join(retryIdList, ","));
                }
                if (!errIdMap.isEmpty()) {
                    amazonAdBatchKeywordDao.updateErrTaskStatusByIdList(puid, errIdMap, true);
                    log.info("sp batch create, create keyword error, batch traceId: {}, taskId: {}, errIdMap: {}", traceId, resultDto.getTaskId(), errIdMap);
                }
            }
            //7、记录失败日志
            this.collectFailLog(errKeywords, resultDto.getLoginIp());
        } catch (Exception e) {
            log.error(String.format("sp batch create keyword, exception, batch traceId: %s, taskId: %s", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId()), e);
            //有异常将本层级全部改为重试状态
            if (CollectionUtils.isEmpty(keywords)) {
                List<Long> idList = keywords.stream().map(AmazonAdBatchKeyword::getId).collect(Collectors.toList());
                amazonAdBatchKeywordDao.updateRetryTaskStatusByIdList(puid, idList, DateUtil.addSecond(new Date(), SpBatchConstants.RETRY_AFTER_SECONDS));
            }
        } finally {
            //8、释放redission锁
            lock.unlock();
        }
    }

    /**
     * 批量调亚马逊创建接口
     * @param shop
     * @param keywords
     * @param offset 偏移量，返回index加上这个值
     * @return
     */
    private BatchCreateReturnDto amazonApiCreateByPartition(String traceId, Long taskId, ShopAuth shop, List<AmazonAdBatchKeyword> keywords, int offset) {
        BatchCreateReturnDto dto = new BatchCreateReturnDto();
        Map<Integer, String> succMap = new HashMap<>();
        Map<Integer, String> errMap = new HashMap<>();
        List<Integer> retryList = new ArrayList<>();
        dto.setSuccMap(succMap);
        dto.setErrMap(errMap);
        dto.setRetryList(retryList);
        List<Integer> duplicateValueErrorList = new ArrayList<>();
        //组装亚马逊请求
        List<CreateKeywordEntityV3> keywordEntityV3List = Lists.newArrayList();
        for (AmazonAdBatchKeyword amazonAdBatchKeyword : keywords) {
            keywordEntityV3List.add(this.batchKeyword2CreateKeywordsV3(amazonAdBatchKeyword));
        }
        //批量将这些数据提交给亚马逊。
        String profileId = keywords.get(0).getProfileId();

        CreateSpKeywordV3Response response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createKeywords(shop.getAdAccessToken(), profileId,
                    shop.getMarketplaceId(), keywordEntityV3List, Boolean.TRUE);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            log.info("sp batch create keyword, response is null or http code not 200, batch traceId: {}, taskId: {}", traceId, taskId);
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createKeywords(shop.getAdAccessToken(), profileId,
                    shop.getMarketplaceId(), keywordEntityV3List, Boolean.TRUE);
        }

        //异常返回，全部加入到重试列表
        if (response == null || response.getData() == null || response.getStatusCode() == -1 || response.getStatusCode() == 429 || response.getStatusCode() == 500) {
            log.info("sp batch create, create keyword response none data, fail all, batch traceId: {}, taskId: {}", traceId, taskId);
            for (int i = 0; i < keywords.size(); i++) {
                retryList.add(i + offset);
            }
            return dto;
        }

        //4、活动提交给亚马逊之后，需要处理其响应，将成功的和失败的分成2组。
        String errMsg = "创建关键词失败";
        if (response.getData() != null && response.getData().getKeywords() != null) {
            List<KeywordSuccessResultV3> success = response.getData().getKeywords().getSuccess();
            List<ErrorItemResultV3> errorItemResultV3s = response.getData().getKeywords().getError();
            //成功的关键词，批量和单个关键词都设置关键词id
            for (KeywordSuccessResultV3 keywordSuccessResultV3 : success) {
                succMap.put(offset + keywordSuccessResultV3.getIndex(), keywordSuccessResultV3.getKeywordId());
            }
            //失败的关键词，批量和单个关键词都设置错误信息
            for (ErrorItemResultV3 keywordResult : errorItemResultV3s) {
                //如果错误信息为创建重复，直接设置为创建成功，后续查询关键词id
                if (AdAmazonErrorTypeEnum.DUPLICATE_VALUE_ERROR.getType().equals(keywordResult.getErrors().get(0).getErrorType())) {
                    duplicateValueErrorList.add(keywordResult.getIndex());
                    continue;
                }
                errMap.put(offset + keywordResult.getIndex(), keywordResult.getErrors().get(0).getErrorMessage());
            }
        } else if (response.getError() != null) {
            if (StringUtils.isNotBlank(response.getError().getMessage())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if (CollectionUtils.isNotEmpty(response.getError().getErrors())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }
            for (int i = 0; i < keywords.size(); i++) {
                errMap.put(offset + i, errMsg);
            }
        }

        //对创建重复的调用列表接口获取关键词id，若有则加入到成功列表中
        if (CollectionUtils.isNotEmpty(duplicateValueErrorList)) {
            Map<Integer, CreateKeywordEntityV3> duplicateValueErrorKeywordMap = duplicateValueErrorList.stream().collect(Collectors.toMap(e -> e, keywordEntityV3List::get));
            Map<Integer, String> retrySuccMap = this.duplicateKeywordGetId(shop, profileId, duplicateValueErrorKeywordMap, offset);
            succMap.putAll(retrySuccMap);
            retryList.addAll(duplicateValueErrorList.stream().filter(e -> !succMap.containsKey(e + offset)).collect(Collectors.toList()));
        }
        return dto;
    }

    /**
     * 创建重复的关键词获取关键词id，并且全部加入到创建成功列表
     */
    private Map<Integer, String> duplicateKeywordGetId(ShopAuth shop, String profileId, Map<Integer, CreateKeywordEntityV3> duplicateValueErrorKeywordMap, int offset) {
        //获取重复成功的index和关键词id
        Map<Integer, String> succMap = new HashMap<>();
        //关键词标识(广告组+关键词)与index的map
        Map<String, Integer> keywordTextIndexMap = new HashMap<>();
        //获取创建重复的活动和广告组
        List<String> campaignIds = new ArrayList<>();
        List<String> adGroupIds = new ArrayList<>();
        duplicateValueErrorKeywordMap.forEach((k, v) -> {
            campaignIds.add(v.getCampaignId());
            adGroupIds.add(v.getAdGroupId());
            keywordTextIndexMap.put(v.getAdGroupId() + "#" + v.getKeywordText(), k);
        });
        KeywordSpV3Client client = KeywordSpV3Client.getInstance();
        // 刷新token次数，保证不会无限刷新
        int refreshedToken = 0;
        String nextToken = null;
        ListSpKeywordV3Response response;
        Map<String, String> keywordTextIdMap;
        while (true) {
            response = client.listKeyword(shop.getAdAccessToken(), profileId, shop.getMarketplaceId(), campaignIds, adGroupIds, null, nextToken);
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode() == AmazonAdUtils.rateLimitingCode) {
                log.info("batch create keyword rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = client.listKeyword(shop.getAdAccessToken(), profileId, shop.getMarketplaceId(), campaignIds, adGroupIds, null, nextToken);
                retry++;
            }
            //刷新token
            if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401 && refreshedToken < 2) {
                shopAuthService.refreshCpcAuth(shop);
                refreshedToken++;
                continue;
            }
            if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getKeywords())) {
                break;
            }
            //将成功返回的keywordId存入成功列表中
            keywordTextIdMap = response.getData().getKeywords().stream().collect(Collectors.toMap(e -> e.getAdGroupId() + "#" + e.getKeywordText(), KeywordExtendEntityV3::getKeywordId));
            Iterator<Map.Entry<String, Integer>> iterator = keywordTextIndexMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, Integer> entry = iterator.next();
                if (keywordTextIdMap.containsKey(entry.getKey())) {
                    succMap.put(offset + entry.getValue(), keywordTextIdMap.get(entry.getKey()));
                    iterator.remove();
                }
            }
            //nextToken刷新
            if (StringUtils.isNotBlank(response.getData().getNextToken())){
                nextToken = response.getData().getNextToken();
            } else {
                break;
            }
        }
        return succMap;
    }

    /**
     * 记录日志
     */
    private void collectFailLog(List<AmazonAdKeyword> amazonAdKeywords, String loginIp) {
        List<AdManageOperationLog> keywordLogs = new ArrayList<>();
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return ;
        }
        Map<String, List<AmazonAdKeyword>> keywordGroupMap = amazonAdKeywords.stream().collect(Collectors.groupingBy(AmazonAdKeyword::getAdGroupId));
        Map<String, String> groupMsgMap = new HashMap<>();
        StringBuilder msgSb;
        for (Map.Entry<String, List<AmazonAdKeyword>> entry : keywordGroupMap.entrySet()) {
            msgSb = new StringBuilder();
            List<AmazonAdKeyword> keywordList = entry.getValue();
            for (AmazonAdKeyword keyword : keywordList) {
                msgSb.append("targetValue:").append(keyword.getKeywordText()).append(",desc:").append(keyword.getError()).append(";");
            }
            groupMsgMap.put(entry.getKey(), msgSb.toString());
        }

        String err = "创建关键词失败";
        for (AmazonAdKeyword amazonAdKeyword: amazonAdKeywords) {
            AdManageOperationLog keywordLog = adManageOperationLogService.getkeywordsLog(null, amazonAdKeyword);
            keywordLog.setIp(loginIp);
            keywordLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            keywordLog.setResultInfo(groupMsgMap.getOrDefault(amazonAdKeyword.getAdGroupId(), err));
            keywordLogs.add(keywordLog);
        }
        adManageOperationLogService.batchLogsMergeByAdGroup(keywordLogs);
    }

    /**
     * 记录日志
     */
    private void collectSuccLog(List<AmazonAdKeyword> amazonAdKeywords, String loginIp) {
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return;
        }
        List<AdManageOperationLog> keywordLogs = new ArrayList<>();
        for (AmazonAdKeyword amazonAdKeyword: amazonAdKeywords) {
            AdManageOperationLog keywordLog = adManageOperationLogService.getkeywordsLog(null, amazonAdKeyword);
            keywordLog.setIp(loginIp);
            keywordLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            keywordLogs.add(keywordLog);
        }
        adManageOperationLogService.batchLogsMergeByAdGroup(keywordLogs);
    }

    /**
     * 批量创建关键词实体类转亚马逊创建关键词实体类
     * @param amazonAdBatchKeyword
     */
    private CreateKeywordEntityV3 batchKeyword2CreateKeywordsV3(AmazonAdBatchKeyword amazonAdBatchKeyword) {
        CreateKeywordEntityV3 createKeywordEntityV3 = new CreateKeywordEntityV3();
        createKeywordEntityV3.setCampaignId(amazonAdBatchKeyword.getAmazonCampaignId());
        createKeywordEntityV3.setAdGroupId(amazonAdBatchKeyword.getAmazonAdGroupId());
        createKeywordEntityV3.setKeywordText(amazonAdBatchKeyword.getKeywordText().trim());
        createKeywordEntityV3.setMatchType(SpV3MatchTypeEnum.getSpV3MatchTypeEnumByValue(amazonAdBatchKeyword.getMatchType()).valueV3());
        createKeywordEntityV3.setState(SpV3StateEnum.ENABLED.valueV3());
        createKeywordEntityV3.setBid(amazonAdBatchKeyword.getBid() != null ? amazonAdBatchKeyword.getBid().doubleValue() : null);
        return createKeywordEntityV3;
    }

    /**
     * 批量创建关键词实体类转关键词实体类
     * @param amazonAdBatchKeyword
     */
    private AmazonAdKeyword batchKeyword2Keyword(AmazonAdBatchKeyword amazonAdBatchKeyword) {
        AmazonAdKeyword amazonAdKeyword = new AmazonAdKeyword();
        amazonAdKeyword.setPuid(amazonAdBatchKeyword.getPuid());
        amazonAdKeyword.setShopId(amazonAdBatchKeyword.getShopId());
        amazonAdKeyword.setMarketplaceId(amazonAdBatchKeyword.getMarketplaceId());
        amazonAdKeyword.setAdGroupId(amazonAdBatchKeyword.getAmazonAdGroupId());
        amazonAdKeyword.setDxmGroupId(amazonAdBatchKeyword.getId());
        amazonAdKeyword.setCampaignId(amazonAdBatchKeyword.getAmazonCampaignId());
        amazonAdKeyword.setProfileId(amazonAdBatchKeyword.getProfileId());
        amazonAdKeyword.setKeywordText(amazonAdBatchKeyword.getKeywordText().trim());
        amazonAdKeyword.setMatchType(amazonAdBatchKeyword.getMatchType());
        amazonAdKeyword.setType(Constants.BIDDABLE);
        amazonAdKeyword.setState(CpcStatusEnum.enabled.name());
        amazonAdKeyword.setBid(amazonAdBatchKeyword.getBid() != null ? amazonAdBatchKeyword.getBid().doubleValue() : null);

        if (amazonAdBatchKeyword.getSuggested() != null) {
            amazonAdKeyword.setSuggested(amazonAdBatchKeyword.getSuggested().doubleValue());
        }
        if (amazonAdBatchKeyword.getRangeStart() != null) {
            amazonAdKeyword.setRangeStart(amazonAdBatchKeyword.getRangeStart().doubleValue());
        }
        if (amazonAdBatchKeyword.getRangeEnd() != null) {
            amazonAdKeyword.setRangeEnd(amazonAdBatchKeyword.getRangeEnd().doubleValue());
        }
        amazonAdKeyword.setCreateId(amazonAdBatchKeyword.getCreateId());
        return amazonAdKeyword;
    }


    /**
     * routine load写入doris
     * @param keywordList
     */
    private void saveDoris(List<AmazonAdKeyword> keywordList) {
        try {
            Date date = new Date();
            List<OdsAmazonAdKeyword> collect = keywordList.stream().map(x -> {
                OdsAmazonAdKeyword odsAmazonAdKeyword = new OdsAmazonAdKeyword();
                BeanUtils.copyProperties(x, odsAmazonAdKeyword);
                odsAmazonAdKeyword.setCreateTime(date);
                odsAmazonAdKeyword.setUpdateTime(date);
                if (StringUtils.isNotBlank(odsAmazonAdKeyword.getState())) {
                    odsAmazonAdKeyword.setState(odsAmazonAdKeyword.getState().toLowerCase());
                }
                return odsAmazonAdKeyword;
            }).collect(Collectors.toList());
            dorisService.saveDorisByRoutineLoad(null, collect);
        } catch (Exception e) {
            log.error("sp keyword save doris error", e);
        }
    }
}
