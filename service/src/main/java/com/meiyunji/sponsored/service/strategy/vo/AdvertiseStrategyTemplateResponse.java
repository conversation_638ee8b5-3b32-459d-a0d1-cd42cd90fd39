package com.meiyunji.sponsored.service.strategy.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class AdvertiseStrategyTemplateResponse implements Serializable {

    private Long id;

    /**
     * puid
     */
    private Integer puid;

    /**
     * 店铺ID
     */
    private Integer shopId;

    /**
     * 站点ID
     */
    private String marketplaceId;

    /**
     * 广告类型:SP,SB，SD
     */
    private String adType;

    /**
     * 对象类型：CAMPAIGN->活动 CAMPAIGN_PLACEMENT->广告位，TARGET-> 投放
     */
    private String itemType;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 使用的数量
     */
    private Integer usageAmount;

    /**
     * 周期类型: DAILY->每日，WEEKLY->每周
     */
    private String type;

    /**
     * 规则(json数组)
     */
    private String rule;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人ID
     */
    private Integer createUid;

    /**
     * 更新人ID
     */
    private Integer updateUid;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 创建时间
     */
    private String createAt;

    /**
     * 更新时间
     */
    private String lastUpdateAt;
}
