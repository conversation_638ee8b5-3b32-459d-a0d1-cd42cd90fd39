package com.meiyunji.sponsored.service.stream.enums;

/**
 * @author: sun<PERSON><PERSON>
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2023-11-21  10:53
 */
public class StreamConstants {

    public static final int SP_MAX_CAMPAIGN_IDS_COUNT = 1000;
    public static  final int SB_MAX_CAMPAIGN_IDS_COUNT = 10;
    public static  final int SD_MAX_CAMPAIGN_IDS_COUNT = 100;

    public static final int SP_MAX_AD_IDS_COUNT = 1000;
    public static  final int SB_MAX_AD_IDS_COUNT = 10;
    public static  final int SD_MAX_AD_IDS_COUNT = 1000;

    public static final int SP_MAX_TARGET_IDS_COUNT = 1000;
    public static  final int SB_MAX_TARGET_IDS_COUNT = 100;
    public static  final int SD_MAX_TARGET_IDS_COUNT = 100;

    public static final int SP_MAX_KEYWORD_IDS_COUNT = 1000;
    public static  final int SB_MAX_KEYWORD_IDS_COUNT = 100;

    public static final int SP_MAX_GROUP_IDS_COUNT = 1000;
    public static  final int SB_MAX_GROUP_IDS_COUNT = 10;
    public static  final int SD_MAX_GROUP_IDS_COUNT = 100;

    public static final int SP_MAX_CAMPAIGN_NE_TARGET_IDS_COUNT = 1000;
    public static  final int SP_MAX_CAMPAIGN_NE_KEYWORD_IDS_COUNT = 1000;


    public static final int SP_MAX_NE_TARGET_IDS_COUNT = 1000;
    public static  final int SB_MAX_NE_TARGET_IDS_COUNT = 100;
    public static  final int SD_MAX_NE_TARGET_IDS_COUNT = 100;

    public static final int SP_MAX_NE_KEYWORD_IDS_COUNT = 1000;
    public static  final int SB_MAX_NE_KEYWORD_IDS_COUNT = 100;

    public static final int RETRY_COUNT = 3;

    public static final String SB_TARGET_QUERY_TYPE = "sbTarget";

    public static final String NOT_SB_TARGET_QUERY_TYPE = "notSbTarget";

}
