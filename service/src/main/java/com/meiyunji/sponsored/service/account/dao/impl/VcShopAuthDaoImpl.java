package com.meiyunji.sponsored.service.account.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.amazon.sellerpartner.base.RegionEnum;
import com.meiyunji.sponsored.common.springjdbc.BaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.enums.ShopAdStatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

/**
 * VcShopAuth
 *
 * <AUTHOR>
 */
@Repository
public class VcShopAuthDaoImpl extends BaseDaoImpl<VcShopAuth> implements IVcShopAuthDao {

    @Override
    public List<VcShopAuth> listAllByIds(Integer puid, List<Integer> shopIds) {
        /*if (CollectionUtils.isEmpty(shopIds)) {
            return Lists.newArrayList();
        }*/

        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
//                .in("id", shopIds.toArray())
                .build();

        return super.listByCondition(condition);
    }



    @Override
    public int copyToDeleteShop(int shopId) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO `t_vc_shop_auth_delete`(");
        sql.append("`id`,`puid`,`name`,`selling_partner_id`,`region`,`marketplace_id`,");
        sql.append("`refresh_token`,`access_token`,`auth_time`,");
        sql.append("`status`,`create_id`,`update_id`,`create_time`,`update_time`");
        sql.append(") SELECT `id`,`puid`,`name`,`selling_partner_id`,`region`,`marketplace_id`,");
        sql.append("`refresh_token`,`access_token`,`auth_time`,");
        sql.append("`status`,`create_id`,`update_id`,`create_time`,`update_time` ");
        sql.append("FROM `t_vc_shop_auth` WHERE id = ?;");

        return getJdbcTemplate().update(sql.toString(), shopId);
    }

    @Override
    public List<String> getAllSellerId(Integer puid) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        if (puid != null && puid > 0) {
            builder.equalTo("puid", puid);
        }
        return listDistinctFieldByCondition("selling_partner_id", builder.build(), String.class);
    }

    @Override
    public List<VcShopAuth> listBySellerId(String sellerId) {
        String sql = "SELECT * FROM t_vc_shop_auth WHERE selling_partner_id = ? ";
        return getJdbcTemplate().query(sql, getMapper(), sellerId);
    }



    @Override
    public int updateAccessToken(Integer puid, String sellingPartnerId, RegionEnum region, String accessToken) {
        String sql = "update t_vc_shop_auth set update_time = now(), access_token=? where puid = ? and selling_partner_id = ? and region = ? and refresh_token is not null";
        return getJdbcTemplate().update(sql, accessToken, puid, sellingPartnerId, region.getRegion());
    }

    @Override
    public List<Integer> getAllIdByPuid(Integer puid) {
        String sql = "select id from t_vc_shop_auth where puid = ?";
        return getJdbcTemplate().queryForList(sql, new Object[]{puid}, Integer.class);
    }

    @Override
    public void save(VcShopAuth shopAuth, Integer id) {
        String sql = "INSERT INTO `t_vc_shop_auth` (`id`, `puid`, `name`, `selling_partner_id`, `region`, `marketplace_id`, `refresh_token`, `access_token`, `auth_time`, `status`, `create_id`, `update_id`, `create_time`, `update_time`) " +
                "VALUES(?,?,?,?,?,?,?,?,?,?,?,?,now(3),now(3))";
        List<Object> args = Lists.newArrayList();
        args.add(id);
        args.add(shopAuth.getPuid());
        args.add(shopAuth.getName());
        args.add(shopAuth.getSellingPartnerId());
        args.add(shopAuth.getRegion());
        args.add(shopAuth.getMarketplaceId());
        args.add(shopAuth.getRefreshToken());
        args.add(shopAuth.getAccessToken());
        args.add(shopAuth.getAuthTime());
        args.add(shopAuth.getStatus());
        args.add(shopAuth.getCreateId());
        args.add(shopAuth.getUpdateId());
        super.getJdbcTemplate().update(sql, args.toArray());
    }

    @Override
    public List<Integer> getAllVCPuid() {
        String sql = "select puid from t_vc_shop_auth group by puid";
        return getJdbcTemplate().queryForList(sql, Integer.class);
    }

    @Override
    public List<VcShopAuth> listByPuidAndShopIds(Integer puid, List<Integer> vcShopIds) {
        if (CollectionUtils.isEmpty(vcShopIds)) {
            return Lists.newArrayList();
        }

        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("id", vcShopIds.toArray())
                .build();
        return super.listByCondition(condition);
    }



    @Override
    public List<Integer> getAllShopId(Integer puid) {
        String sql = "select id from t_vc_shop_auth where puid=?";
        return getJdbcTemplate().queryForList(sql, new Object[]{puid}, Integer.class);
    }

    @Override
    public int cleanAdToken(int puid, Integer shopId) {
        String sql = "update t_vc_shop_auth set update_time = now(),ad_refresh_token=null ,ad_access_token=null,ad_status='unauth',ad_auth_time=null where id = ? and puid=?";
        return getJdbcTemplate().update(sql, shopId, puid);
    }

    /**
     * 爱尔兰站点不支持广告授权sql 中直接剔除，后续支持后去掉
     * @param puid
     * @param sellingPartnerId
     * @param region
     * @return
     */
    @Override
    public List<VcShopAuth> listWaitAdAuthBySeller(int puid, String sellingPartnerId, String region) {
        return listByCondition(new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("selling_partner_id", sellingPartnerId)
                .equalTo("region", region)
                .notEqualTo("marketplace_id", "A28R8C7NBKEWEA")
                .inStrList("ad_status", Arrays.asList(ShopAdStatusEnum.UNAUTH.name(),
                        ShopAdStatusEnum.EXPIRE.name()).toArray(new String[0]))
                .build());
    }



    @Override
    public int clearAdAuth(Integer puid, Integer id) {
        String sql = "UPDATE `t_vc_shop_auth` SET update_time = now(), `ad_refresh_token` = NULL, `ad_access_token` = NULL, `ad_auth_time` = NULL, `ad_status` = ? WHERE `puid` = ? AND `id` = ?";
        return getJdbcTemplate().update(sql, ShopAdStatusEnum.UNAUTH.getName(), puid, id);
    }

    @Override
    public int updateAdAccessToken(Integer puid, String sellingPartnerId, String region, String accessToken) {
        try {
            if (accessToken == null) {
                logger.error("checkAdAccessTokenForNUll updateAdAccessToken puid: {}, adAccessToken: {}, stackTrace: {}", puid, accessToken, ThreadPoolUtil.getStackTrace());
            }
        } catch (Exception e) {
            logger.error("print stack stack error: {}", e);
        }
        String sql = "update t_vc_shop_auth set update_time = now(),ad_access_token=? where puid = ? and selling_partner_id=? and region=? and ad_refresh_token is not null ";
        return getJdbcTemplate().update(sql, accessToken, puid, sellingPartnerId, region);
    }


    @Override
    public List<Integer> getShopByMid(int puid, String marketplaceId) {
        StringBuilder sql = new StringBuilder("SELECT id FROM t_vc_shop_auth WHERE puid = ?");
        StringBuilder whereSql = new StringBuilder();
        List<Object> argList = Lists.newArrayList();
        argList.add(puid);
        if (StringUtils.isNotBlank(marketplaceId)) {
            whereSql.append(" and marketplace_id = ?");
            argList.add(marketplaceId);
        }
        whereSql.append(" and ad_status = 'auth'");
        sql.append(whereSql);
        return getJdbcTemplate().queryForList(sql.toString(), Integer.class, argList.toArray());
    }


    @Override
    public int updateAdStatus(Integer puid, Integer shopId, String adStatus) {
        String sql = "update t_vc_shop_auth set update_time = now(),`ad_status` = ? where id = ? and puid=?";;
        return getJdbcTemplate().update(sql, adStatus, shopId, puid);
    }

}