package com.meiyunji.sponsored.service.strategy.service.impl;

import com.google.api.client.util.Lists;
import com.google.api.client.util.Maps;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sellfox.aadas.types.enumeration.TaskTimeType;
import com.meiyunji.sellfox.aadas.types.exception.AmazonDuplicateAdItemIdException;
import com.meiyunji.sponsored.common.base.Constants;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.MD5Util;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.IndexStrategyConfig;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.log.enums.*;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.po.OperationContent;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.strategy.dao.*;
import com.meiyunji.sponsored.service.strategy.dao.impl.StrategyLimitConfigDao;
import com.meiyunji.sponsored.service.strategy.enums.AdType;
import com.meiyunji.sponsored.service.strategy.po.*;
import com.meiyunji.sponsored.service.strategy.service.IAdvertiseStrategyGroupTargetBidService;
import com.meiyunji.sponsored.service.strategy.service.IAdvertiseStrategyQueryService;
import com.meiyunji.sponsored.service.strategy.service.IAdvertiseStrategyStatusService;
import com.meiyunji.sponsored.service.strategy.service.helper.AdvertiseStrategyGroupTargetBidHelper;
import com.meiyunji.sponsored.service.strategy.vo.*;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyRealTimeBidDao;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyTaskDao;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyTaskRecordSequenceDao;
import com.meiyunji.sponsored.service.strategyTask.enums.TaskAdType;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyRealTimeBid;
import com.meiyunji.sponsored.service.taskGrpcApi.AadasApiFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.cpc.util.Constants.AD_GROUP_STRATEGY_MAX_COUNT_MSG;
import static com.meiyunji.sponsored.service.cpc.util.Constants.AD_GROUP_TEMPLATE_STRATEGY_MAX_COUNT_MSG;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-29  19:00
 */
@Service
@Slf4j
public class AdvertiseStrategyGroupTargetBidServiceImpl implements IAdvertiseStrategyGroupTargetBidService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private IAmazonAdPortfolioDao amazonAdPortfolioDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private AdvertiseStrategyTemplateDao advertiseStrategyTemplateDao;
    @Autowired
    private AdvertiseStrategyAdGroupDao advertiseStrategyAdGroupDao;
    @Autowired
    private AdvertiseStrategyStatusDao advertiseStrategyStatusDao;
    @Autowired
    private AdvertiseStrategyStatusDeleteDao advertiseStrategyStatusDeleteDao;
    @Autowired
    private AdvertiseStrategyScheduleDao advertiseStrategyScheduleDao;
    @Autowired
    private AadasApiFactory aadasApiFactory;
    @Autowired
    private AdvertiseStrategyStatusSequenceDao advertiseStrategyStatusSequenceDao;
    @Autowired
    private AdvertiseStrategyScheduleSequenceDao advertiseStrategyScheduleSequenceDao;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private AdvertiseStrategyGroupTargetBidHelper advertiseStrategyGroupTargetBidHelper;
    @Autowired
    private IAdvertiseStrategyQueryService advertiseStrategyQueryService;
    @Autowired
    private AdvertiseStrategyRealTimeBidDao advertiseStrategyRealTimeBidDao;
    @Autowired
    private AdvertiseStrategyTaskRecordSequenceDao advertiseStrategyTaskRecordSequenceDao;
    @Autowired
    private IAdvertiseStrategyStatusService advertiseStrategyStatusService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private IndexStrategyConfig indexStrategyConfig;
    @Autowired
    private AdvertiseStrategyTaskDao advertiseStrategyTaskDao;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Resource
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Autowired
    private StrategyLimitConfigDao strategyLimitConfigDao;

    @Override
    public Result<Page<AdStrategyGroupVo>> queryAdStrategyGroup(AdStrategyGroupParam param) {
        Result<Page<AdStrategyGroupVo>> result = new Result<>();
        Page<AdStrategyGroupVo> page = new Page<>();
        page.setPageNo(param.getPageNo());
        page.setPageSize(param.getPageSize());
        Map<String, AmazonAdCampaignAll> campaignAllMap = Maps.newHashMap();
        Map<String, AmazonAdPortfolio> portfolioMap = Maps.newHashMap();
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        try {
            if (CollectionUtils.isEmpty(param.getCampaignIdList())) {
                if (CollectionUtils.isNotEmpty(param.getPortfolioIdList())) {
                    List<String> campaignIdList= amazonAdCampaignAllDao.getCampaignIdListByPortfolioId(param.getPuid(),
                            param.getShopId(), param.getPortfolioIdList(), param.getAdType());
                    if (CollectionUtils.isNotEmpty(campaignIdList)) {
                        param.setCampaignIdList(campaignIdList);
                    } else {
                        Page<AdStrategyGroupVo> adStrategyGroupVoPage = new Page<>();
                        result.setCode(Result.SUCCESS);
                        adStrategyGroupVoPage.setPageSize(param.getPageSize());
                        adStrategyGroupVoPage.setPageNo(param.getPageNo());
                        result.setData(adStrategyGroupVoPage);
                        return result;
                    }
                }
            }
            List<AdStrategyGroupVo> adStrategyGroupVoList = Lists.newArrayList();
            if (AdType.SP.name().equalsIgnoreCase(param.getAdType())) {
                Page<AmazonAdGroup> amazonAdGroupPage = amazonAdGroupDao.getPageListByStrategy(param);
                page.setTotalPage(amazonAdGroupPage.getTotalPage());
                page.setTotalSize(amazonAdGroupPage.getTotalSize());
                if (amazonAdGroupPage != null && CollectionUtils.isNotEmpty(amazonAdGroupPage.getRows())) {
                    List<String> campaignIdList = amazonAdGroupPage.getRows().stream().map(AmazonAdGroup::getCampaignId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(campaignIdList)) {
                        List<AmazonAdCampaignAll> campaignAllList = amazonAdCampaignAllDao.listByCampaignId(param.getPuid(), param.getShopId(), campaignIdList, "sp");
                        if (CollectionUtils.isNotEmpty(campaignAllList)) {
                            campaignAllMap = campaignAllList.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (c1, c2) -> c1));
                            List<String> portfolioIdList = campaignAllList.stream().map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(portfolioIdList)) {
                                List<AmazonAdPortfolio> amazonAdPortfolioList = amazonAdPortfolioDao.getPortfolioList(param.getPuid(), param.getShopId(), portfolioIdList);
                                if (CollectionUtils.isNotEmpty(amazonAdPortfolioList)) {
                                    portfolioMap = amazonAdPortfolioList.stream().collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, Function.identity(), (c1, c2) -> c1));
                                }
                            }
                        }
                    }
                    Map<String, AmazonAdCampaignAll> finalCampaignAllMap = campaignAllMap;
                    Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;
                    amazonAdGroupPage.getRows().forEach(e->{
                        AdStrategyGroupVo adStrategyGroupVo = new AdStrategyGroupVo();
                        adStrategyGroupVo.setPuid(e.getPuid());
                        adStrategyGroupVo.setShopId(e.getShopId());
                        adStrategyGroupVo.setMarketplaceId(e.getMarketplaceId());
                        adStrategyGroupVo.setAdType("sp");
                        adStrategyGroupVo.setState(e.getState());
                        adStrategyGroupVo.setCampaignId(e.getCampaignId());
                        if (MapUtils.isNotEmpty(finalCampaignAllMap) && finalCampaignAllMap.containsKey(e.getCampaignId())) {
                            AmazonAdCampaignAll campaignAll = finalCampaignAllMap.get(e.getCampaignId());
                            adStrategyGroupVo.setCampaignName(campaignAll.getName());
                            adStrategyGroupVo.setPortfolioId(campaignAll.getPortfolioId());
                            if (MapUtils.isNotEmpty(finalPortfolioMap) && finalPortfolioMap.containsKey(campaignAll.getPortfolioId())) {
                                adStrategyGroupVo.setPortfolioName(finalPortfolioMap.get(campaignAll.getPortfolioId()).getName());
                            }
                        }
                        adStrategyGroupVo.setAdGroupId(e.getAdGroupId());
                        adStrategyGroupVo.setAdGroupName(e.getName());
                        adStrategyGroupVo.setDefaultBid(e.getDefaultBid().toString());
                        adStrategyGroupVo.setServingStatus(e.getServingStatus());
                        AmazonAdGroup.servingStatusEnum byCode = UCommonUtil.getByCode(e.getServingStatus(), AmazonAdGroup.servingStatusEnum.class);
                        if (byCode != null) {
                            adStrategyGroupVo.setServingStatusName(byCode.getName());
                            adStrategyGroupVo.setServingStatusDec(byCode.getDescription());
                        } else {
                            adStrategyGroupVo.setServingStatusDec(e.getServingStatus());
                            adStrategyGroupVo.setServingStatusName(e.getServingStatus());
                        }
                        if (shopAuth != null) {
                            adStrategyGroupVo.setShopName(shopAuth.getName());
                            AmznEndpoint endpoint = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId());
                            if (endpoint != null) {
                                adStrategyGroupVo.setCurrency(endpoint.getCurrencyCode().value());
                            }
                        }
                        adStrategyGroupVoList.add(adStrategyGroupVo);
                    });
                }
            } else if (AdType.SB.name().equalsIgnoreCase(param.getAdType())) {
                Page<AmazonSbAdGroup> amazonSbAdGroupPage = amazonSbAdGroupDao.getPageListByStrategy(param);
                page.setTotalPage(amazonSbAdGroupPage.getTotalPage());
                page.setTotalSize(amazonSbAdGroupPage.getTotalSize());
                if (amazonSbAdGroupPage != null && CollectionUtils.isNotEmpty(amazonSbAdGroupPage.getRows())) {
                    List<String> campaignIdList = amazonSbAdGroupPage.getRows().stream().map(AmazonSbAdGroup::getCampaignId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(campaignIdList)) {
                        List<AmazonAdCampaignAll> campaignAllList = amazonAdCampaignAllDao.listByCampaignId(param.getPuid(), param.getShopId(), campaignIdList, "sb");
                        if (CollectionUtils.isNotEmpty(campaignAllList)) {
                            campaignAllMap = campaignAllList.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (c1, c2) -> c1));
                            List<String> portfolioIdList = campaignAllList.stream().map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(portfolioIdList)) {
                                List<AmazonAdPortfolio> amazonAdPortfolioList = amazonAdPortfolioDao.getPortfolioList(param.getPuid(), param.getShopId(), portfolioIdList);
                                if (CollectionUtils.isNotEmpty(amazonAdPortfolioList)) {
                                    portfolioMap = amazonAdPortfolioList.stream().collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, Function.identity(), (c1, c2) -> c1));
                                }
                            }
                        }
                    }
                    Map<String, AmazonAdCampaignAll> finalCampaignAllMap = campaignAllMap;
                    Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;
                    amazonSbAdGroupPage.getRows().forEach(e->{
                        AdStrategyGroupVo adStrategyGroupVo = new AdStrategyGroupVo();
                        adStrategyGroupVo.setCampaignId(e.getCampaignId());
                        if (MapUtils.isNotEmpty(finalCampaignAllMap) && finalCampaignAllMap.containsKey(e.getCampaignId())) {
                            AmazonAdCampaignAll campaignAll = finalCampaignAllMap.get(e.getCampaignId());
                            adStrategyGroupVo.setCampaignName(campaignAll.getName());
                            adStrategyGroupVo.setPortfolioId(campaignAll.getPortfolioId());
                            if (MapUtils.isNotEmpty(finalPortfolioMap) && finalPortfolioMap.containsKey(campaignAll.getPortfolioId())) {
                                adStrategyGroupVo.setPortfolioName(finalPortfolioMap.get(campaignAll.getPortfolioId()).getName());
                            }
                        }
                        adStrategyGroupVo.setPuid(e.getPuid());
                        adStrategyGroupVo.setShopId(e.getShopId());
                        adStrategyGroupVo.setMarketplaceId(e.getMarketplaceId());
                        adStrategyGroupVo.setAdType("sb");
                        adStrategyGroupVo.setState(e.getState());
                        adStrategyGroupVo.setAdGroupId(e.getAdGroupId());
                        adStrategyGroupVo.setAdGroupName(e.getName());
                        adStrategyGroupVo.setServingStatus(e.getServingStatus());
                        AmazonSbAdGroup.servingStatusEnum byCode = UCommonUtil.getByCode(e.getServingStatus(), AmazonSbAdGroup.servingStatusEnum.class);
                        if (byCode != null) {
                            adStrategyGroupVo.setServingStatusName(byCode.getName());
                            adStrategyGroupVo.setServingStatusDec(byCode.getDescription());
                        } else {
                            adStrategyGroupVo.setServingStatusDec(e.getServingStatus());
                            adStrategyGroupVo.setServingStatusName(e.getServingStatus());
                        }
                        if (shopAuth != null) {
                            adStrategyGroupVo.setShopName(shopAuth.getName());
                            AmznEndpoint endpoint = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId());
                            if (endpoint != null) {
                                adStrategyGroupVo.setCurrency(endpoint.getCurrencyCode().value());
                            }
                        }
                        adStrategyGroupVoList.add(adStrategyGroupVo);
                    });
                }
            } else if (AdType.SD.name().equalsIgnoreCase(param.getAdType())) {
                Page<AmazonSdAdGroup> amazonSdAdGroupPage = amazonSdAdGroupDao.getPageListByStrategy(param);
                page.setTotalPage(amazonSdAdGroupPage.getTotalPage());
                page.setTotalSize(amazonSdAdGroupPage.getTotalSize());
                if (amazonSdAdGroupPage != null && CollectionUtils.isNotEmpty(amazonSdAdGroupPage.getRows())) {
                    List<String> campaignIdList = amazonSdAdGroupPage.getRows().stream().map(AmazonSdAdGroup::getCampaignId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(campaignIdList)) {
                        List<AmazonAdCampaignAll> campaignAllList = amazonAdCampaignAllDao.listByCampaignId(param.getPuid(), param.getShopId(), campaignIdList, "sd");
                        if (CollectionUtils.isNotEmpty(campaignAllList)) {
                            campaignAllMap = campaignAllList.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (c1, c2) -> c1));
                            List<String> portfolioIdList = campaignAllList.stream().map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(portfolioIdList)) {
                                List<AmazonAdPortfolio> amazonAdPortfolioList = amazonAdPortfolioDao.getPortfolioList(param.getPuid(), param.getShopId(), portfolioIdList);
                                if (CollectionUtils.isNotEmpty(amazonAdPortfolioList)) {
                                    portfolioMap = amazonAdPortfolioList.stream().collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, Function.identity(), (c1, c2) -> c1));
                                }
                            }
                        }
                    }
                    Map<String, AmazonAdCampaignAll> finalCampaignAllMap = campaignAllMap;
                    Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;
                    amazonSdAdGroupPage.getRows().forEach(e->{
                        AdStrategyGroupVo adStrategyGroupVo = new AdStrategyGroupVo();
                        adStrategyGroupVo.setPuid(e.getPuid());
                        adStrategyGroupVo.setShopId(e.getShopId());
                        adStrategyGroupVo.setMarketplaceId(e.getMarketplaceId());
                        adStrategyGroupVo.setCampaignId(e.getCampaignId());
                        if (MapUtils.isNotEmpty(finalCampaignAllMap) && finalCampaignAllMap.containsKey(e.getCampaignId())) {
                            AmazonAdCampaignAll campaignAll = finalCampaignAllMap.get(e.getCampaignId());
                            adStrategyGroupVo.setCampaignName(campaignAll.getName());
                            adStrategyGroupVo.setPortfolioId(campaignAll.getPortfolioId());
                            if (MapUtils.isNotEmpty(finalPortfolioMap) && finalPortfolioMap.containsKey(campaignAll.getPortfolioId())) {
                                adStrategyGroupVo.setPortfolioName(finalPortfolioMap.get(campaignAll.getPortfolioId()).getName());
                            }
                        }
                        adStrategyGroupVo.setAdType("sd");
                        adStrategyGroupVo.setState(e.getState());
                        adStrategyGroupVo.setAdGroupId(e.getAdGroupId());
                        adStrategyGroupVo.setAdGroupName(e.getName());
                        adStrategyGroupVo.setDefaultBid(e.getDefaultBid().toString());
                        adStrategyGroupVo.setServingStatus(e.getServingStatus());
                        AmazonSdAdGroup.servingStatusEnum byCode = UCommonUtil.getByCode(e.getServingStatus(), AmazonSdAdGroup.servingStatusEnum.class);
                        if (byCode != null) {
                            adStrategyGroupVo.setServingStatusName(byCode.getName());
                            adStrategyGroupVo.setServingStatusDec(byCode.getDescription());
                        } else {
                            adStrategyGroupVo.setServingStatusDec(e.getServingStatus());
                            adStrategyGroupVo.setServingStatusName(e.getServingStatus());
                        }
                        if (shopAuth != null) {
                            adStrategyGroupVo.setShopName(shopAuth.getName());
                            AmznEndpoint endpoint = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId());
                            if (endpoint != null) {
                                adStrategyGroupVo.setCurrency(endpoint.getCurrencyCode().value());
                            }
                        }
                        adStrategyGroupVoList.add(adStrategyGroupVo);
                    });
                }
            }
            page.setRows(adStrategyGroupVoList);
            result.setData(page);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("分时策略查询广告组异常");
            log.error("puid={} shopId:{} 分时策略查询广告组异常:", param.getPuid(), param.getShopId(), e);
        }
        return result;
    }

    private int getTemplateCount(int puid) {
        try {
            StrategyLimitConfig strategyLimitConfig = strategyLimitConfigDao.queryStrategyConfigByPuid(puid);
            if (strategyLimitConfig == null || strategyLimitConfig.getGroupTemplateNumLimit() <= 0) {
                return indexStrategyConfig.getAdGroupTemplateStrategyMaxSize();
            }
            return strategyLimitConfig.getGroupTemplateNumLimit();
        } catch (Exception e) {
            log.error("getTemplateCount error", e);
        }
        return indexStrategyConfig.getAdGroupTemplateStrategyMaxSize();
    }

    @Override
    public AddStrategyVo submitStrategy(Integer puid, List<SubmitStrategyVo> submitStrategyVos, Long templateId, Integer updateId, String loginIp, String traceId) {

        // 其他操作日志记录(编辑:添加受控对象)
        AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
        OperationContent operationContent = new OperationContent();
        List<AdManageOperationLog> adManageOperationLogList = new ArrayList<>();
        // 广告组name
        List<String> adGroupNames = Lists.newArrayList();

        AdvertiseStrategyTemplate vo = advertiseStrategyTemplateDao.selectByPrimaryKey(puid, templateId);
        AddStrategyVo addStrategyVo = new AddStrategyVo();
        List<String> data = Lists.newArrayList();
        String msg = "";
        if (vo == null) {
            addStrategyVo.setMsg("模板已被删除，请刷新页面重试");
            addStrategyVo.setItemIds(data);
            return addStrategyVo;
        }
        addControlledObjectsLog(puid, updateId, loginIp, vo.getItemType(), templateId, adManageOperationLog, true);
        adManageOperationLog.setCampaignId(submitStrategyVos.stream().map(SubmitStrategyVo::getCampaignId).collect(Collectors.joining("、")));
        List<Long> templateList = advertiseStrategyAdGroupDao.getTemplateCount(puid);
        if (!templateList.contains(templateId)) {
            if (templateList.size() + 1 > getTemplateCount(puid)) {
                addStrategyVo.setMsg(AD_GROUP_TEMPLATE_STRATEGY_MAX_COUNT_MSG);
                addStrategyVo.setItemIds(data);
                return addStrategyVo;
            }
        }
        List<Long> adGroupTaskIdList = advertiseStrategyStatusSequenceDao.batchGenId(submitStrategyVos.size());
        List<AdvertiseStrategyAdGroup> advertiseStrategyAdGroupList = Lists.newArrayList();
        List<AdvertiseStrategyAdGroup> advertiseStrategyAdGroups = advertiseStrategyAdGroupDao.getListByAdGroupId(puid, submitStrategyVos.stream().map(SubmitStrategyVo::getAdGroupId).collect(Collectors.toList()));
        List<String> adGoupIdList = null;
        if (CollectionUtils.isNotEmpty(advertiseStrategyAdGroups)) {
            adGoupIdList = advertiseStrategyAdGroups.stream().map(AdvertiseStrategyAdGroup::getAdGroupId).collect(Collectors.toList());
        }
        //获取广告组名称用于记录日志
        Map<String, String> groupIdMap = this.getGroupIdNameMap(puid, submitStrategyVos.get(0).getShopId(),
                submitStrategyVos.stream().collect(Collectors.toMap(SubmitStrategyVo::getAdGroupId, SubmitStrategyVo::getAdType)));
        for (int s = 0;s<submitStrategyVos.size();s++) {
            SubmitStrategyVo submitStrategyVo = submitStrategyVos.get(s);
            if (CollectionUtils.isNotEmpty(adGoupIdList) && adGoupIdList.contains(submitStrategyVo.getAdGroupId())) {
                msg = "不能添加重复的受控对象";
                continue;
            }
            String lockKey = String.format(RedisConstant.SPONSORED_AD_STRATEGY_AD_GROUP_ID_TASK_KEY, submitStrategyVo.getAdGroupId());
            RLock lock = redissonClient.getLock(lockKey);
            boolean b = lock.tryLock();
            if (!b) {
                msg = "当前广告组下投放还在执行中";
                continue;
            }
            lock.unlock();
            Long taskId = adGroupTaskIdList.get(s);
            AdvertiseStrategyAdGroup advertiseStrategyAdGroup = new AdvertiseStrategyAdGroup();
            advertiseStrategyAdGroup.setId(taskId);
            advertiseStrategyAdGroup.setTaskId(taskId);
            advertiseStrategyAdGroup.setPuid(puid);
            advertiseStrategyAdGroup.setShopId(submitStrategyVo.getShopId());
            advertiseStrategyAdGroup.setMarketplaceId(submitStrategyVo.getMarketplaceId());
            advertiseStrategyAdGroup.setAdType(submitStrategyVo.getAdType());
            advertiseStrategyAdGroup.setVersion(vo.getVersion());
            advertiseStrategyAdGroup.setTemplateId(templateId);
            advertiseStrategyAdGroup.setType(vo.getType());
            advertiseStrategyAdGroup.setCampaignId(submitStrategyVo.getCampaignId());
            advertiseStrategyAdGroup.setAdGroupId(submitStrategyVo.getAdGroupId());
            advertiseStrategyAdGroup.setItemType(vo.getItemType());
            advertiseStrategyAdGroup.setRule(vo.getRule());
            advertiseStrategyAdGroup.setStatus(submitStrategyVo.getStatus());
            advertiseStrategyAdGroup.setEffectiveStatus(1);
            if (adManageOperationLog.getShopId() == null) {
                adManageOperationLog.setShopId(submitStrategyVo.getShopId());
            }
            if (adManageOperationLog.getMarketplaceId() == null) {
                adManageOperationLog.setMarketplaceId(submitStrategyVo.getMarketplaceId());
            }
            if (submitStrategyVo.getDefaultBid() != null) {
                advertiseStrategyAdGroup.setDefaultBid(submitStrategyVo.getDefaultBid());
            }
            //开启异步任务处理投放
            CompletableFuture<Void> future = CompletableFuture.runAsync(() ->{
                RLock targetLock = redissonClient.getLock(lockKey);
                boolean t = targetLock.tryLock();
                if (!t) {
                    return;
                }
                try {
                    List<AmazonAdKeyword> amazonAdKeywordList = amazonAdKeywordDaoRoutingService.listKeywordByGroupId(puid, vo.getShopId(), submitStrategyVo.getAdGroupId());
                    List<AmazonAdTargeting> amazonAdTargetingList = amazonAdTargetDaoRoutingService.listTargetByGroupId(puid, vo.getShopId(), submitStrategyVo.getAdGroupId());
                    if (CollectionUtils.isNotEmpty(amazonAdTargetingList)) {
                        if (amazonAdTargetingList.size() > com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE) {
                            List<List<AmazonAdTargeting>> list = org.apache.pulsar.shade.com.google.common.collect.Lists.partition(amazonAdTargetingList, com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE);
                            List<CompletableFuture> futureList = Lists.newArrayList();
                            list.forEach(e -> {
                                CompletableFuture<Void> tarFuture = CompletableFuture.runAsync(() -> {
                                    try {
                                        advertiseStrategyGroupTargetBidHelper.spTargetSubmitStrategy(vo, taskId, submitStrategyVo.getDefaultBid(), e, updateId);
                                    } catch (Exception exception) {
                                        log.error("puid:{} shopId:{} adGroupId:{} 处理广告组下的投放添加策略异常:", puid, submitStrategyVo.getShopId(), submitStrategyVo.getAdGroupId(), exception);
                                    }
                                }, ThreadPoolUtil.getTargetProcessPool());
                                futureList.add(tarFuture);
                            });
                            //阻塞等待
                            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
                            try {
                                allFutures.get();
                            } catch (Exception e) {
                                log.error("处理广告组下的投放添加策略, 多线程执行异常", e);
                            }
                        } else {
                            try {
                                advertiseStrategyGroupTargetBidHelper.spTargetSubmitStrategy(vo, taskId, submitStrategyVo.getDefaultBid(), amazonAdTargetingList, updateId);
                            } catch (Exception exception) {
                                log.error("puid:{} shopId:{} adGroupId:{} 处理广告组下的投放添加策略异常:", puid, submitStrategyVo.getShopId(), submitStrategyVo.getAdGroupId(), exception);
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(amazonAdKeywordList)) {
                        if (amazonAdKeywordList.size() > com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE) {
                            List<List<AmazonAdKeyword>> list = org.apache.pulsar.shade.com.google.common.collect.Lists.partition(amazonAdKeywordList, com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE);
                            List<CompletableFuture> futureList = Lists.newArrayList();
                            list.forEach(e -> {
                                CompletableFuture<Void> tarFuture = CompletableFuture.runAsync(() -> {
                                    try {
                                        advertiseStrategyGroupTargetBidHelper.spKeywordSubmitStrategy(vo, taskId, submitStrategyVo.getDefaultBid(), e, updateId);
                                    } catch (Exception exception) {
                                        log.error("puid:{} shopId:{} adGroupId:{} 处理广告组下的投放添加策略异常:", puid, submitStrategyVo.getShopId(), submitStrategyVo.getAdGroupId(), exception);
                                    }
                                }, ThreadPoolUtil.getTargetProcessPool());
                                futureList.add(tarFuture);
                            });
                            //阻塞等待
                            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
                            try {
                                allFutures.get();
                            } catch (Exception e) {
                                log.error("处理广告组下的投放添加策略, 多线程执行异常", e);
                            }
                        } else {
                            try {
                                advertiseStrategyGroupTargetBidHelper.spKeywordSubmitStrategy(vo, taskId, submitStrategyVo.getDefaultBid(), amazonAdKeywordList, updateId);
                            } catch (Exception exception) {
                                log.error("puid:{} shopId:{} adGroupId:{} 处理广告组下的投放添加策略异常:", puid, submitStrategyVo.getShopId(), submitStrategyVo.getAdGroupId(), exception);
                            }
                        }
                    }
                    List<AmazonSbAdKeyword> amazonSbAdKeywordList = amazonSbAdKeywordDao.listByGroupId(puid, vo.getShopId(), submitStrategyVo.getAdGroupId());
                    List<AmazonSbAdTargeting> amazonSbAdTargetingList = amazonSbAdTargetingDao.listByGroupId(puid, vo.getShopId(), submitStrategyVo.getAdGroupId());
                    if (CollectionUtils.isNotEmpty(amazonSbAdKeywordList)) {
                        if (amazonSbAdKeywordList.size() > com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE) {
                            List<List<AmazonSbAdKeyword>> list = org.apache.pulsar.shade.com.google.common.collect.Lists.partition(amazonSbAdKeywordList, com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE);
                            List<CompletableFuture> futureList = Lists.newArrayList();
                            list.forEach(e -> {
                                CompletableFuture<Void> tarFuture = CompletableFuture.runAsync(() -> {
                                    try {
                                        advertiseStrategyGroupTargetBidHelper.sbKeywordSubmitStrategy(vo, taskId, submitStrategyVo.getDefaultBid(), e, updateId);
                                    } catch (Exception exception) {
                                        log.error("puid:{} shopId:{} adGroupId:{} 处理广告组下的投放添加策略异常:", puid, submitStrategyVo.getShopId(), submitStrategyVo.getAdGroupId(), exception);
                                    }
                                }, ThreadPoolUtil.getTargetProcessPool());
                                futureList.add(tarFuture);
                            });
                            //阻塞等待
                            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
                            try {
                                allFutures.get();
                            } catch (Exception e) {
                                log.error("处理广告组下的投放添加策略, 多线程执行异常", e);
                            }
                        } else {
                            try {
                                advertiseStrategyGroupTargetBidHelper.sbKeywordSubmitStrategy(vo, taskId, submitStrategyVo.getDefaultBid(), amazonSbAdKeywordList, updateId);
                            } catch (Exception exception) {
                                log.error("puid:{} shopId:{} adGroupId:{} 处理广告组下的投放添加策略异常:", puid, submitStrategyVo.getShopId(), submitStrategyVo.getAdGroupId(), exception);
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(amazonSbAdTargetingList)) {
                        if (amazonSbAdTargetingList.size() > com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE) {
                            List<List<AmazonSbAdTargeting>> list = org.apache.pulsar.shade.com.google.common.collect.Lists.partition(amazonSbAdTargetingList, com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE);
                            List<CompletableFuture> futureList = Lists.newArrayList();
                            list.forEach(e -> {
                                CompletableFuture<Void> tarFuture = CompletableFuture.runAsync(() -> {
                                    try {
                                        advertiseStrategyGroupTargetBidHelper.sbTargetSubmitStrategy(vo, taskId, submitStrategyVo.getDefaultBid(), e, updateId);
                                    } catch (Exception exception) {
                                        log.error("puid:{} shopId:{} adGroupId:{} 处理广告组下的投放添加策略异常:", puid, submitStrategyVo.getShopId(), submitStrategyVo.getAdGroupId(), exception);
                                    }
                                }, ThreadPoolUtil.getTargetProcessPool());
                                futureList.add(tarFuture);
                            });
                            //阻塞等待
                            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
                            try {
                                allFutures.get();
                            } catch (Exception e) {
                                log.error("处理广告组下的投放添加策略, 多线程执行异常", e);
                            }
                        } else {
                            try {
                                advertiseStrategyGroupTargetBidHelper.sbTargetSubmitStrategy(vo, taskId, submitStrategyVo.getDefaultBid(), amazonSbAdTargetingList, updateId);
                            } catch (Exception exception) {
                                log.error("puid:{} shopId:{} adGroupId:{} 处理广告组下的投放添加策略异常:", puid, submitStrategyVo.getShopId(), submitStrategyVo.getAdGroupId(), exception);
                            }
                        }
                    }
                    List<AmazonSdAdTargeting> amazonSdAdTargetingList = amazonSdAdTargetingDao.listByGroupId(puid, vo.getShopId(), submitStrategyVo.getAdGroupId());
                    if (CollectionUtils.isNotEmpty(amazonSdAdTargetingList)) {
                        if (amazonSdAdTargetingList.size() > com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE) {
                            List<List<AmazonSdAdTargeting>> list = org.apache.pulsar.shade.com.google.common.collect.Lists.partition(amazonSdAdTargetingList, com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE);
                            List<CompletableFuture> futureList = Lists.newArrayList();
                            list.forEach(e -> {
                                CompletableFuture<Void> tarFuture = CompletableFuture.runAsync(() -> {
                                    try {
                                        advertiseStrategyGroupTargetBidHelper.sdTargetSubmitStrategy(vo, taskId, submitStrategyVo.getDefaultBid(), e, submitStrategyVo.getAdGroupId(), updateId);
                                    } catch (Exception exception) {
                                        log.error("puid:{} shopId:{} adGroupId:{} 处理广告组下的投放添加策略异常:", puid, submitStrategyVo.getShopId(), submitStrategyVo.getAdGroupId(), exception);
                                    }
                                }, ThreadPoolUtil.getTargetProcessPool());
                                futureList.add(tarFuture);
                            });
                            //阻塞等待
                            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
                            try {
                                allFutures.get();
                            } catch (Exception e) {
                                log.error("处理广告组下的投放添加策略, 多线程执行异常", e);
                            }
                        } else {
                            try {
                                advertiseStrategyGroupTargetBidHelper.sdTargetSubmitStrategy(vo, taskId, submitStrategyVo.getDefaultBid(), amazonSdAdTargetingList, submitStrategyVo.getAdGroupId(), updateId);
                            } catch (Exception exception) {
                                log.error("puid:{} shopId:{} adGroupId:{} 处理广告组下的投放添加策略异常:", puid, submitStrategyVo.getShopId(), submitStrategyVo.getAdGroupId(), exception);
                            }
                        }
                    }
                } catch (Exception exception) {
                    log.error("puid:{} adGroupId:{} 广告组异步任务处理异常:",puid, advertiseStrategyAdGroup.getAdGroupId(), exception);
                } finally {
                    targetLock.unlock();
                }
            }, ThreadPoolUtil.getAdGroupProcessPool());
            advertiseStrategyAdGroupList.add(advertiseStrategyAdGroup);
            data.add(submitStrategyVo.getAdGroupId());
            if (AdType.SP.name().equalsIgnoreCase(submitStrategyVo.getAdType())) {
                amazonAdGroupDao.updatePricing(puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId(), 1, 1, updateId);
            } else if (AdType.SB.name().equalsIgnoreCase(submitStrategyVo.getAdType())) {
                amazonSbAdGroupDao.updatePricing(puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId(), 1, 1, updateId);
            } else if (AdType.SD.name().equalsIgnoreCase(submitStrategyVo.getAdType())) {
                amazonSdAdGroupDao.updatePricing(puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId(), 1, 1, updateId);
            }
            if (groupIdMap.containsKey(submitStrategyVo.getAdGroupId())) {
                adGroupNames.add(groupIdMap.get(submitStrategyVo.getAdGroupId()));
            }
        }

        operationContent.setTitle("添加受控对象");
        operationContent.setNewValue(String.join(",", adGroupNames));
        adManageOperationLog.setAdGroupId(StringUtils.join(groupIdMap.values(), "、"));
        adManageOperationLog.setOperationContent(JSONUtil.objectToJson(operationContent));
        if (StringUtils.isBlank(msg)) {
            filterBaseSuccess(adManageOperationLog, adManageOperationLogList, true);
        }
        if (CollectionUtils.isNotEmpty(advertiseStrategyAdGroupList)) {
            advertiseStrategyAdGroupDao.batchInsert(puid, advertiseStrategyAdGroupList);
        }
        addStrategyVo.setMsg(msg);
        addStrategyVo.setItemIds(data);
        return addStrategyVo;
    }


    @Override
    public Result<List<UpdateStrategyResponseVo>> updateStrategy(Integer puid, Long templateId, List<UpdateStrategyVo> updateStrategyVoList, AdvertiseStrategyTemplate vo, Integer updateType, Integer updateId, String traceId) {
//        Result<List<UpdateStrategyResponseVo>> result = new Result();
//        try{
//            Boolean updateValueFalg = false;
//            List<UpdateStrategyResponseVo> responseVoList = Lists.newArrayList();
//            Map<Long,AdvertiseStrategyStatus> advertiseStrategyStatusMap = null;
//            List<Long> statusIds = updateStrategyVoList.stream().map(UpdateStrategyVo::getStatusId).collect(Collectors.toList());
//            List<UpdateStrategyVo> successList = Lists.newArrayList();
//            List<AdvertiseStrategyAdGroup> advertiseStrategyAdGroupList = advertiseStrategyAdGroupDao.getByStatusIds(puid, statusIds);
//            List<AdvertiseStrategyStatus> strategyStatusList = advertiseStrategyStatusDao.getListByStrategyAdGroupIds(puid,advertiseStrategyAdGroupList.stream().map(AdvertiseStrategyAdGroup::getId).collect(Collectors.toList()));
//            if (Coll) {
//                for (int s = 0; s < updateStrategyVoList.size(); s++) {
//                    UpdateStrategyVo updateStrategyVo = updateStrategyVoList.get(s);
//                    UpdateStrategyResponseVo responseVo = new UpdateStrategyResponseVo();
//                    Boolean falg = true;
//                    List<AdvertiseStrategySchedule> list = org.apache.pulsar.shade.com.google.common.collect.Lists.newArrayList();
//                    AdvertiseStrategyStatus advertiseStrategyStatus = null;
//                    if (MapUtils.isNotEmpty(advertiseStrategyStatusMap) && advertiseStrategyStatusMap.containsKey(updateStrategyVo.getStatusId())) {
//                        advertiseStrategyStatus = advertiseStrategyStatusMap.get(updateStrategyVo.getStatusId());
//                    }
//                    if (advertiseStrategyStatus == null) {
//                        log.info("puid:{} statusId:{} 分时调价修改受控对象找不到", puid, updateStrategyVo.getStatusId());
//                        continue;
//                    }
//                    responseVo.setPuid(advertiseStrategyStatus.getPuid());
//                    responseVo.setShopId(advertiseStrategyStatus.getShopId());
//                    responseVo.setItemId(advertiseStrategyStatus.getItemId());
//                    responseVo.setItemType(advertiseStrategyStatus.getItemType());
//                    responseVo.setTargetType(advertiseStrategyStatus.getTargetType());
//                    responseVo.setAdType(advertiseStrategyStatus.getAdType());
//                    responseVo.setStatusId(advertiseStrategyStatus.getId());
//                    String originValueJson = null;
//                    TaskTimeType taskTimeType = null;
//                    String adTargetType = null;
//                    OriginValueVo originValueVo = new OriginValueVo();
//                    if (updateStrategyVo.getBiddingValue() == null) {
//                        originValueVo = JSONUtil.jsonToObject(advertiseStrategyStatus.getOriginValue(), OriginValueVo.class);
//                    } else {
//                        //只修改值时重置版本号并获取当前受控对象的策略规则
//                        updateValueFalg = true;
//                        originValueVo.setBiddingValue(updateStrategyVo.getBiddingValue());
//                        updateStrategyVo.setOriginBiddingValue(JSONUtil.jsonToObject(advertiseStrategyStatus.getOriginValue(),
//                                OriginValueVo.class).getBiddingValue());
//                        if (!advertiseStrategyStatus.getVersion().equals(vo.getVersion())) {
//                            vo.setVersion(-1);
//                        }
//                        vo.setRule(advertiseStrategyStatus.getRule());
//                        vo.setType(advertiseStrategyStatus.getType());
//                    }
//                    List<TargetRuleVo> ruleVoList = JSONUtil.jsonToArray(vo.getRule(), TargetRuleVo.class);
//                    //竞价值计算
//                    for (int i = 0; i < ruleVoList.size(); i++) {
//                        BigDecimal biddingValue = originValueVo.getBiddingValue();
//                        AdvertiseStrategySchedule advertiseStrategySchedule = new AdvertiseStrategySchedule();
//                        TargetRuleVo targetRuleVo = ruleVoList.get(i);
//                        advertiseStrategySchedule.setTaskId(advertiseStrategyStatus.getTaskId());
//                        advertiseStrategySchedule.setPuid(puid);
//                        advertiseStrategySchedule.setShopId(advertiseStrategyStatus.getShopId());
//                        advertiseStrategySchedule.setMarketplaceId(advertiseStrategyStatus.getMarketplaceId());
//                        advertiseStrategySchedule.setAdType(advertiseStrategyStatus.getAdType());
//                        advertiseStrategySchedule.setItemType(advertiseStrategyStatus.getItemType());
//                        advertiseStrategySchedule.setItemId(advertiseStrategyStatus.getItemId());
//                        advertiseStrategySchedule.setCampaignId(advertiseStrategyStatus.getCampaignId());
//                        advertiseStrategySchedule.setAdGroupId(advertiseStrategyStatus.getAdGroupId());
//                        advertiseStrategySchedule.setType(vo.getType());
//                        advertiseStrategySchedule.setDay(targetRuleVo.getSiteDate());
//                        advertiseStrategySchedule.setStart(targetRuleVo.getStartTimeSite() * 60);
//                        advertiseStrategySchedule.setEnd(targetRuleVo.getEndTimeSite() * 60);
//                        advertiseStrategySchedule.setTargetType(advertiseStrategyStatus.getTargetType());
//                        originValueJson = JSONUtil.objectToJson(originValueVo);
//                        advertiseStrategySchedule.setOriginValue(originValueJson);
//                        if (targetRuleVo.getBiddingType() == 0) {
//                            biddingValue = biddingValue.add(targetRuleVo.getBiddingValue()).setScale(2, BigDecimal.ROUND_HALF_UP);
//                        } else if (targetRuleVo.getBiddingType() == 1) {
//                            biddingValue = biddingValue.multiply(BigDecimal.valueOf(1).add(targetRuleVo.getBiddingValue().
//                                    multiply(BigDecimal.valueOf(0.01)))).setScale(2, BigDecimal.ROUND_HALF_UP);
//                        } else if (targetRuleVo.getBiddingType() == 2) {
//                            biddingValue = biddingValue.subtract(targetRuleVo.getBiddingValue()).
//                                    setScale(2, BigDecimal.ROUND_HALF_UP);
//                        } else if (targetRuleVo.getBiddingType() == 3) {
//                            biddingValue = biddingValue.multiply(BigDecimal.valueOf(1).subtract(targetRuleVo.getBiddingValue()
//                                    .multiply(BigDecimal.valueOf(0.01)))).setScale(2, BigDecimal.ROUND_HALF_UP);
//                        } else if (targetRuleVo.getBiddingType() == 4) {
//                            biddingValue = targetRuleVo.getBiddingValue();
//                        }
//                        if (targetRuleVo.getBiddingType() == 0 || targetRuleVo.getBiddingType() == 1) {
//                            if (targetRuleVo.getBiddingMaxValue() != null) {
//                                if (biddingValue.compareTo(targetRuleVo.getBiddingMaxValue()) > 0) {
//                                    biddingValue = targetRuleVo.getBiddingMaxValue();
//                                }
//                            }
//                        }
//                        if (targetRuleVo.getBiddingType() == 2 || targetRuleVo.getBiddingType() == 3) {
//                            if (targetRuleVo.getBiddingMinValue() != null) {
//                                if (biddingValue.compareTo(targetRuleVo.getBiddingMinValue()) < 0) {
//                                    biddingValue = targetRuleVo.getBiddingMinValue();
//                                }
//                            }
//                        }
//                        OriginValueVo newValueVo = new OriginValueVo();
//                        newValueVo.setBiddingValue(biddingValue);
//                        String newValueJson = JSONUtil.objectToJson(newValueVo);
//                        advertiseStrategySchedule.setNewValue(newValueJson);
//                        list.add(advertiseStrategySchedule);
//                    }
//                    if (!falg) {
//                        responseVoList.add(responseVo);
//                        continue;
//                    }
//                    try {
//                        // 更新状态时调用
//                        if ("ENABLED".equals(advertiseStrategyStatus.getStatus())) {
//                            aadasApiFactory.getStrategyApi(taskTimeType).setSchedule(advertiseStrategyStatus.getTaskId(),
//                                    list, false);
//                        }
//                        advertiseStrategyStatusDao.updateStatus(puid, templateId, vo.getType(),
//                                updateStrategyVo.getStatusId(), vo.getRule(), advertiseStrategyStatus.getStatus(), vo.getVersion(), originValueJson, vo.getReturnValue());
//                        List<Long> ids = advertiseStrategyScheduleDao.queryIdByTaskId(puid, advertiseStrategyStatus.getShopId(),
//                                advertiseStrategyStatus.getTaskId(), advertiseStrategyStatus.getItemType());
//                        if (CollectionUtils.isNotEmpty(ids)) {
//                            advertiseStrategyScheduleDao.deleteStrategyByIds(puid, ids);
//                        }
//                        List<Long> longIdList = advertiseStrategyScheduleSequenceDao.batchGenId(list.size());
//                        for (int i = 0; i < longIdList.size(); i++) {
//                            list.get(i).setId(longIdList.get(i));
//                        }
//                        advertiseStrategyScheduleDao.batchInsert(puid, list);
//                        successList.add(updateStrategyVo);
//                    } catch (Exception e) {
//                        if (e instanceof AmazonDuplicateAdItemIdException) {
//                            responseVo.setMsg(e.getMessage());
//                            responseVo.setIsGrayPlacement(false);
//                        } else {
//                            responseVo.setMsg("API接口超时，请重试");
//                            responseVo.setIsGrayPlacement(true);
//                        }
//                        responseVoList.add(responseVo);
//                        log.error("traceId:{} puid={} 分时调价任务修改异常:", traceId, puid, e);
//                    }
//                }
//            }
//            if (CollectionUtils.isNotEmpty(responseVoList)) {
//                //填充错误返回的数据
////                filterUpdateResponse(responseVoList);
//                result.setData(responseVoList);
//            }
//            if (updateValueFalg) {
//                List<AdManageOperationLog> list = Lists.newArrayList();
////                filterMatchData(puid, successList, list, responseVoList,updateId);
//            } else {
//                // 日志记录(同步模板只记录更新成功的)
//                addSyncTemplateOperationLog(puid, vo, updateStrategyVoList,updateId);
//            }
//        } catch (Exception e) {
//            result.setCode(Result.ERROR);
//            result.setMsg("分时调价任务修改异常");
//            log.error("traceId:{} puid:{} 分时调价任务修改异常:", traceId, puid, e);
//        }
//        return result;
        return null;
    }

    @Override
    public Result<String> updateStrategyStatus(Integer puid, Long statusId, String status, Integer updateId, String loginIp, String traceId) {
        Result<String> result = new Result<>();
        // 新增策略开启/暂停的
        AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
        OperationContent operationContent = new OperationContent();
        List<AdManageOperationLog> adManageOperationLogList = new ArrayList<>();
        AdvertiseStrategyAdGroup advertiseStrategyAdGroup = advertiseStrategyAdGroupDao.getByStatusId(puid, statusId);
        Integer count = advertiseStrategyTaskDao.queryCountByTemplateId(puid, advertiseStrategyAdGroup.getTemplateId());
        if (count > 0) {
            result.setCode(Result.ERROR);
            result.setMsg("批量任务未完成，请稍后提交");
            return result;
        }
        AdvertiseStrategyStatus advertiseStrategyStatus = advertiseStrategyStatusDao.getByStatusId(puid, statusId);
        if (advertiseStrategyAdGroup == null && advertiseStrategyStatus != null) {
            result = advertiseStrategyStatusService.updateStrategyStatus(puid,
                    statusId, status, updateId, loginIp, traceId);
            return result;
        }
        String lockKey = String.format(RedisConstant.SPONSORED_AD_STRATEGY_AD_GROUP_ID_TASK_KEY,advertiseStrategyAdGroup.getAdGroupId());
        RLock lock = redissonClient.getLock(lockKey);
        boolean b = lock.tryLock();
        if (!b) {
            result.setCode(Result.ERROR);
            result.setMsg("当前广告组下投放还在执行中");
            log.info("当前任务在执行中，不可操作puid:{} adGroupId:{}", puid, advertiseStrategyAdGroup.getAdGroupId());
            return result;
        }

        try {
            //记录日志
            addControlledObjectsLog(puid, updateId, loginIp, advertiseStrategyAdGroup.getItemType(), advertiseStrategyAdGroup.getTemplateId(), adManageOperationLog, false);
            filterBaseMessage(advertiseStrategyAdGroup.getAdType(), advertiseStrategyAdGroup.getMarketplaceId(),
                    advertiseStrategyAdGroup.getCampaignId(), advertiseStrategyAdGroup.getAdGroupId(), adManageOperationLog, null);
            adManageOperationLog.setShopId(advertiseStrategyAdGroup.getShopId());
            String statusName = "";
            if (AdType.SP.name().equalsIgnoreCase(advertiseStrategyAdGroup.getAdType())) {
                AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByAdGroupId(puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId());
                statusName = Optional.ofNullable(amazonAdGroup).map(AmazonAdGroup::getName).orElse("");
            } else if (AdType.SB.name().equalsIgnoreCase(advertiseStrategyAdGroup.getAdType())) {
                AmazonSbAdGroup amazonSbAdGroup = amazonSbAdGroupDao.getByAdGroupId(puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId());
                statusName = Optional.ofNullable(amazonSbAdGroup).map(AmazonSbAdGroup::getName).orElse("");
            } else if (AdType.SD.name().equalsIgnoreCase(advertiseStrategyAdGroup.getAdType())) {
                AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId());
                statusName = Optional.ofNullable(amazonSdAdGroup).map(AmazonSdAdGroup::getName).orElse("");
            }
            operationContent.setNewValue(statusName);
            adManageOperationLog.setTargetName(statusName);
            // 回写受控对象表数据
            if ("ENABLED".equals(status)) {
                String flag = "";
                if (AdType.SP.name().equalsIgnoreCase(advertiseStrategyAdGroup.getAdType())) {
                    amazonAdGroupDao.updatePricing(puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId(), 1, 1, updateId);
                } else if (AdType.SB.name().equalsIgnoreCase(advertiseStrategyAdGroup.getAdType())) {
                    amazonSbAdGroupDao.updatePricing(puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId(), 1, 1, updateId);
                } else if (AdType.SD.name().equalsIgnoreCase(advertiseStrategyAdGroup.getAdType())) {
                    amazonSdAdGroupDao.updatePricing(puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId(), 1, 1, updateId);
                }
                operationContent.setTitle("开启分时调竞价任务");
            } else {
                String flag = "";
                if (AdType.SP.name().equalsIgnoreCase(advertiseStrategyAdGroup.getAdType())) {
                    amazonAdGroupDao.updatePricing(puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId(), 1, 0, updateId);
                } else if (AdType.SB.name().equalsIgnoreCase(advertiseStrategyAdGroup.getAdType())) {
                    amazonSbAdGroupDao.updatePricing(puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId(), 1, 0, updateId);
                } else if (AdType.SD.name().equalsIgnoreCase(advertiseStrategyAdGroup.getAdType())) {
                    amazonSdAdGroupDao.updatePricing(puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId(), 1, 0, updateId);
                }
                operationContent.setTitle("暂停分时调竞价任务");
            }
            advertiseStrategyAdGroupDao.updateStrategyStatusById(puid, statusId, status);
            Long id = 0L;
            while (true) {
                //指针分页获取数据处理
                List<AdvertiseStrategyStatus> strategyStatuses = advertiseStrategyStatusDao.getListByStrategyAdGroupId(puid, advertiseStrategyAdGroup.getShopId(), id, advertiseStrategyAdGroup.getId());
                if (CollectionUtils.isEmpty(strategyStatuses)) {
                    break;
                }
                id = strategyStatuses.get(strategyStatuses.size() - 1).getId();
                if (strategyStatuses.size() > com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE) {
                    List<List<AdvertiseStrategyStatus>> list = org.apache.pulsar.shade.com.google.common.collect.Lists.partition(strategyStatuses, com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE);
                    List<CompletableFuture> futureList = Lists.newArrayList();
                    list.forEach(e -> {
                        futureList.add(CompletableFuture.runAsync(() -> {
                            try {
                                advertiseStrategyGroupTargetBidHelper.processTargetStatus(puid, status, e);
                            } catch (Exception exception) {
                                log.error("puid:{} shopId:{} adGroupId:{} 处理广告组下的投放添加策略异常:", puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId(), exception);
                            }
                        }, ThreadPoolUtil.getTargetProcessPool()));
                    });
                    //阻塞等待
                    CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
                    try {
                        allFutures.get();
                    } catch (Exception e) {
                        log.error("处理广告组下的投放添加策略, 多线程执行异常", e);
                    }
                } else {
                    try {
                        advertiseStrategyGroupTargetBidHelper.processTargetStatus(puid, status, strategyStatuses);
                    } catch (Exception exception) {
                        log.error("puid:{} shopId:{} adGroupId:{} 处理广告组下的投放添加策略异常:", puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId(), exception);
                    }
                }
            }
            result.setCode(Result.SUCCESS);
            result.setMsg("修改成功");

            adManageOperationLog.setOperationContent(JSONUtil.objectToJson(operationContent));
            adManageOperationLog.setResult(0);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("分时调价任务修改状态异常:"+e);
            log.error("traceId:{} puid:{} statusId:{} 分时调价任务修改状态异常", traceId,puid, statusId,e);

            adManageOperationLog.setResult(1);
            adManageOperationLog.setResultInfo("分时调价任务修改状态异常:"+e.getMessage());
        } finally {
            lock.unlock();
        }

        adManageOperationLogList.add(adManageOperationLog);
        adManageOperationLogService.printAdOtherOperationLog(adManageOperationLogList);
        return result;
    }

    @Override
    public Result<String> transferStrategy(Integer puid, Integer uid, Long templateId, List<Long> strategyAdGroupIdList, Integer operation, String loginIp, String traceId) {
        Result<String> result = new Result<>();

        // 其他操作日志记录(编辑:转移受控对象 包含移除和添加)
        AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
        OperationContent operationContent = new OperationContent();
        AdManageOperationLog addAdManageOperationLog = new AdManageOperationLog();
        OperationContent addOperationContent = new OperationContent();
        List<AdManageOperationLog> adManageOperationLogList = new ArrayList<>();
        List<Long> adGroupStatusIdList = Lists.newArrayList();
        boolean run = true;
        List<Long> templateList = advertiseStrategyAdGroupDao.getTemplateCount(puid);
        if (!templateList.contains(templateId)) {
            if (templateList.size() + 1 > indexStrategyConfig.getAdGroupTemplateStrategyMaxSize()) {
                result.setMsg(AD_GROUP_TEMPLATE_STRATEGY_MAX_COUNT_MSG);
                result.setCode(Result.ERROR);
                return result;
            }
        }
        Integer count = advertiseStrategyAdGroupDao.getCount(puid, templateId);
        if (strategyAdGroupIdList.size() + count > indexStrategyConfig.getAdGroupStrategyMaxSize()) {
            result.setMsg(String.format(AD_GROUP_STRATEGY_MAX_COUNT_MSG, indexStrategyConfig.getAdGroupStrategyMaxSize()));
            result.setCode(Result.ERROR);
            return result;
        }
        try {
            AdvertiseStrategyTemplate vo = advertiseStrategyTemplateDao.selectByPrimaryKey(puid, templateId);
            List<AdvertiseStrategyAdGroup> advertiseStrategyAdGroupList = advertiseStrategyAdGroupDao.getListByLongIdList(puid, strategyAdGroupIdList);
            if (CollectionUtils.isEmpty(advertiseStrategyAdGroupList)) {
                result.setCode(Result.ERROR);
                result.setMsg("当前没有可操作的广告组");
                return result;
            }
            Integer taskCount = advertiseStrategyTaskDao.queryCountByTemplateId(puid, advertiseStrategyAdGroupList.get(0).getTemplateId());
            if (taskCount > 0) {
                result.setCode(Result.ERROR);
                result.setMsg("批量任务未完成，请稍后提交");
                return result;
            }
            if (operation == 1) {
                for (AdvertiseStrategyAdGroup advertiseStrategyAdGroup : advertiseStrategyAdGroupList) {
                    String lockKey = String.format(RedisConstant.SPONSORED_AD_STRATEGY_AD_GROUP_ID_TASK_KEY,advertiseStrategyAdGroup.getAdGroupId());
                    RLock lock = redissonClient.getLock(lockKey);
                    boolean b = lock.tryLock();
                    if (!b) {
                        result.setMsg("当前广告组下投放还在执行中");
                        result.setCode(Result.ERROR);
                        return result;
                    }
                    lock.unlock();
                    adGroupStatusIdList.add(advertiseStrategyAdGroup.getId());
                    CompletableFuture<Void> future = CompletableFuture.runAsync(()->{
                        RLock targetLock = redissonClient.getLock(lockKey);
                        boolean t = targetLock.tryLock();
                        if (!t) {
                            log.error("当前广告组还有投放再执行中key:{}",lockKey);
                            return;
                        }
                        try {
                            Long id = 0L;
                            while (true) {
                                //指针分页获取数据处理
                                List<AdvertiseStrategyStatus> advertiseStrategyStatusList = advertiseStrategyStatusDao.getListByStrategyAdGroupId(puid, advertiseStrategyAdGroup.getShopId(), id, advertiseStrategyAdGroup.getId());
                                if (CollectionUtils.isEmpty(advertiseStrategyStatusList)) {
                                    break;
                                }
                                id = advertiseStrategyStatusList.get(advertiseStrategyStatusList.size() - 1).getId();
                                if (CollectionUtils.isNotEmpty(advertiseStrategyStatusList)) {
                                    List<List<AdvertiseStrategyStatus>> list = org.apache.pulsar.shade.com.google.common.collect.Lists.partition(advertiseStrategyStatusList, com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE);
                                    List<CompletableFuture> futureList = Lists.newArrayList();
                                    list.forEach(e -> {
                                        futureList.add(CompletableFuture.runAsync(() -> {
                                            for (AdvertiseStrategyStatus strategyStatus : e) {
                                                advertiseStrategyStatusDao.updateStatusTemplateById(puid, strategyStatus.getId(), templateId);
                                            }
                                        }, ThreadPoolUtil.getTargetProcessPool()));
                                    });
                                    //阻塞等待
                                    CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
                                    try {
                                        allFutures.get();
                                    } catch (Exception e) {
                                        log.error("处理广告组下的投放转移策略, 多线程执行异常", e);
                                    }
                                }
                            }
                        } catch (Exception exception) {
                            log.error("puid:{} adGroupId:{} 广告组异步任务处理异常:", puid, advertiseStrategyAdGroup.getAdGroupId(), exception);
                        } finally {
                            targetLock.unlock();
                        }
                    }, ThreadPoolUtil.getAdGroupProcessPool());
                }
            } else {
                String rule = vo.getRule();
                for (AdvertiseStrategyAdGroup advertiseStrategyAdGroup : advertiseStrategyAdGroupList) {
                    String lockKey = String.format(RedisConstant.SPONSORED_AD_STRATEGY_AD_GROUP_ID_TASK_KEY, advertiseStrategyAdGroup.getAdGroupId());
                    RLock lock = redissonClient.getLock(lockKey);
                    boolean b = lock.tryLock();
                    if (!b) {
                        result.setMsg("当前广告组下投放还在执行中");
                        result.setCode(Result.ERROR);
                        return result;
                    }
                    lock.unlock();
                    adGroupStatusIdList.add(advertiseStrategyAdGroup.getId());
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        RLock targetLock = redissonClient.getLock(lockKey);
                        boolean t = targetLock.tryLock();
                        if (!t) {
                            log.error("当前广告组还有投放再执行中key:{}",lockKey);
                            return;
                        }
                        try {
                            Long id = 0L;
                            while (true) {
                                //指针分页获取数据处理
                                List<AdvertiseStrategyStatus> advertiseStrategyStatusList = advertiseStrategyStatusDao.getListByStrategyAdGroupId(puid, advertiseStrategyAdGroup.getShopId(), id, advertiseStrategyAdGroup.getId());
                                if (CollectionUtils.isEmpty(advertiseStrategyStatusList)) {
                                    break;
                                }
                                id = advertiseStrategyStatusList.get(advertiseStrategyStatusList.size() - 1).getId();
                                if (CollectionUtils.isNotEmpty(advertiseStrategyStatusList)) {
                                    List<List<AdvertiseStrategyStatus>> list = org.apache.pulsar.shade.com.google.common.collect.Lists.partition(advertiseStrategyStatusList, com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE);
                                    List<CompletableFuture> futureList = Lists.newArrayList();
                                    list.forEach(e -> {
                                        futureList.add(CompletableFuture.runAsync(() -> {
                                            for (AdvertiseStrategyStatus strategyStatus : e) {
                                                List<AdvertiseStrategySchedule> advertiseStrategyScheduleList = com.google.api.client.util.Lists.newArrayList();
                                                OriginValueVo originValueVo = JSONUtil.jsonToObject(strategyStatus.getOriginValue(), OriginValueVo.class);
                                                String originValueJson = JSONUtil.objectToJson(originValueVo);
                                                strategyStatus.setOriginValue(originValueJson);
                                                strategyStatus.setType(vo.getType());
                                                TaskTimeType taskTimeType = null;
                                                List<TargetRuleVo> ruleVoList = JSONUtil.jsonToArray(rule, TargetRuleVo.class);
                                                if (strategyStatus.getTargetType().equals(Constants.KEYWORD_TARGET)) {
                                                    taskTimeType = TaskTimeType.adGroupKeyword;
                                                } else {
                                                    taskTimeType = TaskTimeType.adGroupTarget;
                                                }
                                                for (int i = 0; i < ruleVoList.size(); i++) {
                                                    Long scheduleId = advertiseStrategyScheduleSequenceDao.genId();
                                                    AdvertiseStrategySchedule advertiseStrategySchedule = new AdvertiseStrategySchedule();
                                                    TargetRuleVo targetRuleVo = ruleVoList.get(i);
                                                    advertiseStrategySchedule.setId(scheduleId);
                                                    advertiseStrategySchedule.setPuid(puid);
                                                    advertiseStrategySchedule.setShopId(strategyStatus.getShopId());
                                                    advertiseStrategySchedule.setAdType(strategyStatus.getAdType());
                                                    advertiseStrategySchedule.setMarketplaceId(strategyStatus.getMarketplaceId());
                                                    advertiseStrategySchedule.setTaskId(strategyStatus.getTaskId());
                                                    advertiseStrategySchedule.setItemType(vo.getItemType());
                                                    advertiseStrategySchedule.setItemId(strategyStatus.getItemId());
                                                    advertiseStrategySchedule.setCampaignId(strategyStatus.getCampaignId());
                                                    advertiseStrategySchedule.setAdGroupId(strategyStatus.getAdGroupId());
                                                    advertiseStrategySchedule.setType(vo.getType());
                                                    advertiseStrategySchedule.setDay(targetRuleVo.getSiteDate());
                                                    advertiseStrategySchedule.setStart(targetRuleVo.getStartTimeSite() * 60);
                                                    advertiseStrategySchedule.setEnd(targetRuleVo.getEndTimeSite() * 60);
                                                    BigDecimal biddingValue = originValueVo.getBiddingValue();
                                                    if (targetRuleVo.getBiddingType() == 0) {
                                                        biddingValue = biddingValue.add(targetRuleVo.getBiddingValue()).setScale(2, BigDecimal.ROUND_HALF_UP);
                                                    } else if (targetRuleVo.getBiddingType() == 1) {
                                                        biddingValue = biddingValue.multiply(BigDecimal.valueOf(1).add(targetRuleVo.getBiddingValue().
                                                                multiply(BigDecimal.valueOf(0.01)))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                                    } else if (targetRuleVo.getBiddingType() == 2) {
                                                        biddingValue = biddingValue.subtract(targetRuleVo.getBiddingValue()).
                                                                setScale(2, BigDecimal.ROUND_HALF_UP);
                                                    } else if (targetRuleVo.getBiddingType() == 3) {
                                                        biddingValue = biddingValue.multiply(BigDecimal.valueOf(1).subtract(targetRuleVo.getBiddingValue()
                                                                .multiply(BigDecimal.valueOf(0.01)))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                                    } else if (targetRuleVo.getBiddingType() == 4) {
                                                        biddingValue = targetRuleVo.getBiddingValue();
                                                    }

                                                    if (targetRuleVo.getBiddingType() == 0 || targetRuleVo.getBiddingType() == 1) {
                                                        if (targetRuleVo.getBiddingMaxValue() != null) {
                                                            if (biddingValue.compareTo(targetRuleVo.getBiddingMaxValue()) > 0) {
                                                                biddingValue = targetRuleVo.getBiddingMaxValue();
                                                            }
                                                        }
                                                    }
                                                    if (targetRuleVo.getBiddingType() == 2 || targetRuleVo.getBiddingType() == 3) {
                                                        if (targetRuleVo.getBiddingMinValue() != null) {
                                                            if (biddingValue.compareTo(targetRuleVo.getBiddingMinValue()) < 0) {
                                                                biddingValue = targetRuleVo.getBiddingMinValue();
                                                            }
                                                        }
                                                    }
                                                    OriginValueVo newValueVo = new OriginValueVo();
                                                    newValueVo.setBiddingValue(biddingValue);
                                                    String newValueJson = JSONUtil.objectToJson(newValueVo);
                                                    advertiseStrategySchedule.setNewValue(newValueJson);
                                                    advertiseStrategySchedule.setOriginValue(originValueJson);
                                                    advertiseStrategyScheduleList.add(advertiseStrategySchedule);
                                                }
                                                // 更新状态时调用
                                                if ("ENABLED".equals(strategyStatus.getStatus())) {
                                                    advertiseStrategyStatusDao.updateStatus(puid, templateId, vo.getType(),
                                                            strategyStatus.getId(), vo.getRule(), strategyStatus.getStatus(), vo.getVersion(), originValueJson, vo.getReturnValue());
                                                    advertiseStrategyScheduleDao.deleteStrategySchedule(puid, strategyStatus.getShopId(), strategyStatus.getTaskId(), strategyStatus.getItemType());
                                                    advertiseStrategyScheduleDao.batchInsert(puid, advertiseStrategyScheduleList);
                                                    //推送数据到aadas调度服务
                                                    try {
                                                        aadasApiFactory.getStrategyApi(taskTimeType).setSchedule(strategyStatus.getTaskId(),
                                                                templateId, advertiseStrategyScheduleList, false);
                                                    } catch (Exception exception) {
                                                        log.error("推送aadas服务异常:", exception);
                                                    }
                                                }
                                                // 更新状态时调用
                                                else if ("DISABLED".equals(strategyStatus.getStatus())) {
                                                    advertiseStrategyStatusDao.updateStatus(puid, templateId, vo.getType(),
                                                            strategyStatus.getId(), vo.getRule(), strategyStatus.getStatus(), vo.getVersion(), originValueJson, vo.getReturnValue());
                                                    advertiseStrategyScheduleDao.deleteStrategySchedule(puid, strategyStatus.getShopId(), strategyStatus.getTaskId(), strategyStatus.getItemType());
                                                    advertiseStrategyScheduleDao.batchInsert(puid, advertiseStrategyScheduleList);
                                                }
                                            }
                                        }, ThreadPoolUtil.getTargetProcessPool()));
                                    });
                                    //阻塞等待
                                    CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
                                    try {
                                        allFutures.get();
                                    } catch (Exception e) {
                                        log.error("处理广告组下的投放转移策略, 多线程执行异常", e);
                                    }
                                }
                            }
                        } catch (Exception exception) {
                            log.error("puid:{} adGroupId:{} 广告组异步任务处理异常:", puid, advertiseStrategyAdGroup.getAdGroupId(), exception);
                        } finally {
                            targetLock.unlock();
                        }
                    }, ThreadPoolUtil.getAdGroupProcessPool());
                }
            }
            if (CollectionUtils.isNotEmpty(adGroupStatusIdList)) {
                if (1 == operation) {
                    advertiseStrategyAdGroupDao.batchUpdateStatusTemplateByIds(puid, strategyAdGroupIdList, templateId);
                } else {
                    advertiseStrategyAdGroupDao.batchUpdateStatusTemplateById(puid, strategyAdGroupIdList, templateId, vo.getRule(), vo.getVersion());
                }
            }
            // 记录日志
            addControlledObjectsLog(puid, uid, loginIp, vo.getItemType(), templateId, addAdManageOperationLog, true);
            addControlledObjectsLog(puid, uid, loginIp, vo.getItemType(), advertiseStrategyAdGroupList.get(0).getTemplateId(), adManageOperationLog, true);
            addTransferLog(puid, adManageOperationLog, operationContent, addAdManageOperationLog, addOperationContent, vo, advertiseStrategyAdGroupList);
            adManageOperationLogList.add(adManageOperationLog);
            adManageOperationLogList.add(addAdManageOperationLog);
            adManageOperationLogService.printAdOtherOperationLog(adManageOperationLogList);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            if (e instanceof AmazonDuplicateAdItemIdException) {
                result.setMsg(e.getMessage());
            } else {
                result.setMsg("分时调价任务转移异常:" + e);
            }
            log.error("traceId:{} puid:{} 分时调价转移异常", traceId, puid, e);
            adManageOperationLog.setResult(0);
            addAdManageOperationLog.setResult(0);
            adManageOperationLogList.add(adManageOperationLog);
            adManageOperationLogList.add(addAdManageOperationLog);
            adManageOperationLogService.printAdOtherOperationLog(adManageOperationLogList);
        }
        return result;
    }

    @Override
    public Result<String> removeStrategy(Integer puid, List<RemoveStrategyVo> removeStrategyVoList, Integer updateId, String loginIp, String traceId) {
        Result<String> result = new Result<>();
        if (CollectionUtils.isEmpty(removeStrategyVoList) || removeStrategyVoList.size() == 0) {
            result.setCode(Result.SUCCESS);
            result.setMsg("无数据删除");
            return result;
        }

        // 其他操作日志记录(编辑:移除受控对象)
        AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
        OperationContent operationContent = new OperationContent();
        List<AdManageOperationLog> list = new ArrayList<>();

        // 广告活动name
        List<String> campaignNames = Lists.newArrayList();
        List<String> targetNames = Lists.newArrayList();
        List<String> targetTypes = Lists.newArrayList();
        List<String> spKeywordIds = Lists.newArrayList();
        List<String> sbKeywordIds = Lists.newArrayList();

        addControlledObjectsLog(puid, updateId, loginIp, removeStrategyVoList.get(0).getItemType(), null, adManageOperationLog, true);
        List<Long> ids = removeStrategyVoList.stream().map(RemoveStrategyVo::getStatusId).collect(Collectors.toList());
        List<AdvertiseStrategyAdGroup> advertiseStrategyAdGroupList = advertiseStrategyAdGroupDao.getByStatusIds(puid, ids);
        if (CollectionUtils.isEmpty(advertiseStrategyAdGroupList)) {
            result.setCode(Result.ERROR);
            result.setMsg("当前没有可操作的广告组");
            return result;
        }
        Integer taskCount = advertiseStrategyTaskDao.queryCountByTemplateId(puid, advertiseStrategyAdGroupList.get(0).getTemplateId());
        if (taskCount > 0) {
            result.setCode(Result.ERROR);
            result.setMsg("批量任务未完成，请稍后提交");
            return result;
        }
        List<AdvertiseStrategyAdGroup> deleteAdvertiseStrategyAdGroupList = Lists.newArrayList();
        for (AdvertiseStrategyAdGroup advertiseStrategyAdGroup : advertiseStrategyAdGroupList) {
            String lockKey = String.format(RedisConstant.SPONSORED_AD_STRATEGY_AD_GROUP_ID_TASK_KEY,advertiseStrategyAdGroup.getAdGroupId());
            RLock lock = redissonClient.getLock(lockKey);
            boolean b = lock.tryLock();
            if (!b) {
                result.setMsg("当前广告组下投放还在执行中");
                result.setCode(Result.ERROR);
                return result;
            }
            lock.unlock();
            deleteAdvertiseStrategyAdGroupList.add(advertiseStrategyAdGroup);
            CompletableFuture<Void> future = CompletableFuture.runAsync(()->{
                RLock targetLock = redissonClient.getLock(lockKey);
                boolean t = targetLock.tryLock();
                if (!t) {
                    log.error("当前广告组还有投放再执行中key:{}",lockKey);
                    return;
                }
                try {
                    Long id = 0L;
                    while (true) {
                        //指针分页获取数据处理
                        List<AdvertiseStrategyStatus> advertiseStrategyStatusList = advertiseStrategyStatusDao.getListByStrategyAdGroupId(puid, advertiseStrategyAdGroup.getShopId(), id, advertiseStrategyAdGroup.getId());
                        if (CollectionUtils.isEmpty(advertiseStrategyStatusList)) {
                            break;
                        }
                        id = advertiseStrategyStatusList.get(advertiseStrategyStatusList.size() - 1).getId();
                        if (CollectionUtils.isNotEmpty(advertiseStrategyStatusList)) {
                            List<List<AdvertiseStrategyStatus>> lists = org.apache.pulsar.shade.com.google.common.collect.Lists.partition(advertiseStrategyStatusList, com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE);
                            List<CompletableFuture> futureList = Lists.newArrayList();
                            lists.forEach(e -> {
                                futureList.add(CompletableFuture.runAsync(() -> {
                                    Map<Long, AdvertiseStrategyStatus> advertiseStrategyStatusMap = null;
                                    try {
                                        if (CollectionUtils.isNotEmpty(e)) {
                                            List<String> campaignIds = e.stream().map(AdvertiseStrategyStatus::getCampaignId).collect(Collectors.toList());
                                            List<String> sbGroupIds = e.stream().filter(s -> "TARGET".equals(s.getItemType()) && "SB".equals(s.getAdType())).
                                                    map(AdvertiseStrategyStatus::getAdGroupId).collect(Collectors.toList());
                                            advertiseStrategyStatusMap = e.stream().collect(Collectors.toMap(AdvertiseStrategyStatus::getId, Function.identity()));
                                            for (int s = 0; s < e.size(); s++) {
                                                TaskTimeType taskTimeType = null;
                                                AdvertiseStrategyStatus advertiseStrategyStatus = e.get(s);
                                                if (advertiseStrategyStatus.getTargetType().equals(Constants.KEYWORD_TARGET)) {
                                                    taskTimeType = TaskTimeType.adGroupKeyword;
                                                } else {
                                                    taskTimeType = TaskTimeType.adGroupTarget;
                                                    targetNames.add(advertiseStrategyStatus.getTargetName());
                                                    targetTypes.add(advertiseStrategyStatus.getTargetType());
                                                }

                                                //任务调度服务删除策略
                                                aadasApiFactory.getStrategyApi(taskTimeType).removeSchedule(puid, advertiseStrategyStatus.getShopId(), advertiseStrategyStatus.getTaskId(), true);
                                                //广告服务删除策略
                                                Long statusId = advertiseStrategyStatus.getId();
                                                Long taskId = advertiseStrategyStatus.getTaskId();
                                                advertiseStrategyStatusDao.deleteStrategyStatus(puid, statusId, advertiseStrategyStatus.getShopId());
                                                List<Long> scheduleIds = advertiseStrategyScheduleDao.queryIdByTaskId(puid, advertiseStrategyStatus.getShopId(), taskId, advertiseStrategyStatus.getItemType());
                                                if (CollectionUtils.isNotEmpty(scheduleIds)) {
                                                    advertiseStrategyScheduleDao.deleteStrategyByIds(puid, scheduleIds);
                                                }
                                                //回写受控对象表数据
                                                advertiseStrategyStatusDeleteDao.insetStrategyStatus(puid, advertiseStrategyStatus);
                                            }
                                        }
                                    } catch (Exception exception) {
                                        log.error("traceId:{} puid:{} 移除策略异常", traceId, puid, e);
                                    }
                                }, ThreadPoolUtil.getTargetProcessPool()));
                            });
                            //阻塞等待
                            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
                            try {
                                allFutures.get();
                            } catch (Exception e) {
                                log.error("处理广告组下的投放移除策略, 多线程执行异常", e);
                            }
                        }
                    }
                    //记录日志
                    Set<String> campaignIdSet = advertiseStrategyAdGroupList.stream().map(AdvertiseStrategyAdGroup::getCampaignId).collect(Collectors.toSet());
                    Map<String, String> groupIdMap = this.getGroupIdNameMap(puid, removeStrategyVoList.get(0).getShopId(),
                            advertiseStrategyAdGroupList.stream().collect(Collectors.toMap(AdvertiseStrategyAdGroup::getAdGroupId, AdvertiseStrategyAdGroup::getAdType)));
                    operationContent.setNewValue(StringUtils.join(groupIdMap.values(), "、"));
                    operationContent.setTitle("移除受控对象");
                    adManageOperationLog.setOperationContent(JSONUtil.objectToJson(operationContent));
                    adManageOperationLog.setCampaignId(StringUtils.join(campaignIdSet, "、"));
                    adManageOperationLog.setAdGroupId(StringUtils.join(groupIdMap.keySet(), "、"));
                    adManageOperationLog.setResult(0);
                    adManageOperationLog.setShopId(removeStrategyVoList.get(0).getShopId());
                    adManageOperationLog.setMarketplaceId(advertiseStrategyAdGroup.getMarketplaceId());
                    adManageOperationLog.setTemplateId(advertiseStrategyAdGroup.getTemplateId());
                    adManageOperationLog.setTargetId(String.valueOf(advertiseStrategyAdGroup.getTemplateId()));
                } catch (Exception exception) {
                    log.error("puid:{} adGroupId:{} 转移受控对象异常:", puid, advertiseStrategyAdGroup.getAdGroupId(), exception);
                    adManageOperationLog.setResult(1);
                    adManageOperationLog.setResultInfo("转移受控对象异常:" + exception.getMessage());
                } finally {
                    targetLock.unlock();
                    list.add(adManageOperationLog);
                    adManageOperationLogService.printAdOtherOperationLog(list);
                }
            }, ThreadPoolUtil.getAdGroupProcessPool());
        }
        if (CollectionUtils.isNotEmpty(deleteAdvertiseStrategyAdGroupList)) {
            advertiseStrategyAdGroupDao.batchDeleteStrategyStatus(puid, deleteAdvertiseStrategyAdGroupList.stream().map(AdvertiseStrategyAdGroup::getId).collect(Collectors.toList()));
            for (AdvertiseStrategyAdGroup advertiseStrategyAdGroup : deleteAdvertiseStrategyAdGroupList) {
                if (AdType.SP.name().equalsIgnoreCase(advertiseStrategyAdGroup.getAdType())) {
                    amazonAdGroupDao.updatePricing(puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId(), 0, 0, updateId);
                } else if (AdType.SB.name().equalsIgnoreCase(advertiseStrategyAdGroup.getAdType())) {
                    amazonSbAdGroupDao.updatePricing(puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId(), 0, 0, updateId);
                } else if (AdType.SD.name().equalsIgnoreCase(advertiseStrategyAdGroup.getAdType())) {
                    amazonSdAdGroupDao.updatePricing(puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId(), 0, 0, updateId);
                }
            }
        }
        return result;
    }

    @Override
    public Result<Page<AdvertiseStrategyStatus>> pageAdControlledAdGroupTarget(ControlledObjectParam param) {
        Result<Page<AdvertiseStrategyStatus>> pageResult = new Result<>();
        try {
            if (CollectionUtils.isEmpty(param.getCampaignIds())) {
                if (CollectionUtils.isNotEmpty(param.getPortfolioIds())) {
                    List<String> campaignIdList= amazonAdCampaignAllDao.getCampaignIdListByPortfolioIdNoType(param.getPuid(), param.getShopId(), param.getPortfolioIds());
                    if (CollectionUtils.isNotEmpty(campaignIdList)) {
                        param.setCampaignIds(campaignIdList);
                    } else {
                        Page<AdvertiseStrategyStatus> adStrategyGroupVoPage = new Page<>();
                        pageResult.setCode(Result.SUCCESS);
                        adStrategyGroupVoPage.setPageSize(param.getPageSize());
                        adStrategyGroupVoPage.setPageNo(param.getPageNo());
                        pageResult.setData(adStrategyGroupVoPage);
                        return pageResult;
                    }
                }
            }
            List<String> adAllGroupIdList = Lists.newArrayList();
            List<String> adGroupIdList = null;
            List<String> adSbGroupIdList = null;
            List<String> adSdGroupIdList = null;
            if (CollectionUtils.isEmpty(param.getAdTypeList()) || AdType.SP.name().equalsIgnoreCase(param.getAdTypeList().get(0))) {
                adGroupIdList = amazonAdGroupDao.queryAdGroupIdList(param);
                adAllGroupIdList.addAll(adGroupIdList);
            }
            if (CollectionUtils.isEmpty(param.getAdTypeList()) || AdType.SB.name().equalsIgnoreCase(param.getAdTypeList().get(0))) {
                adSbGroupIdList = amazonSbAdGroupDao.queryAdGroupIdList(param);
                adAllGroupIdList.addAll(adSbGroupIdList);
            }
            if (CollectionUtils.isEmpty(param.getAdTypeList()) || AdType.SD.name().equalsIgnoreCase(param.getAdTypeList().get(0))) {
                adSdGroupIdList = amazonSdAdGroupDao.queryAdGroupIdList(param);
                adAllGroupIdList.addAll(adSdGroupIdList);
            }
            if (CollectionUtils.isEmpty(adAllGroupIdList)) {
                Page<AdvertiseStrategyStatus> adStrategyGroupVoPage = new Page<>();
                pageResult.setCode(Result.SUCCESS);
                adStrategyGroupVoPage.setPageSize(param.getPageSize());
                adStrategyGroupVoPage.setPageNo(param.getPageNo());
                pageResult.setData(adStrategyGroupVoPage);
                return pageResult;
            }
            param.setGroupIds(adAllGroupIdList);
            List<String> queryIsTargetItemIdList = advertiseStrategyStatusDao.queryIsTargetItem(param.getPuid(), param.getGroupIds());
            if (StringUtils.isNotBlank(param.getAdGroupScreen()) && param.getAdGroupScreen().equals("exact")) {
                param.setItemIdList(queryIsTargetItemIdList);
            }
            Page<AdvertiseStrategyAdGroup> advertiseStrategyAdGroupPage = advertiseStrategyAdGroupDao.pageAdControlledAdGroupTarget(param);
            Page<AdvertiseStrategyStatus> voPage = new Page<>();
            voPage.setPageNo(advertiseStrategyAdGroupPage.getPageNo());
            voPage.setPageSize(advertiseStrategyAdGroupPage.getPageSize());
            voPage.setTotalPage(advertiseStrategyAdGroupPage.getTotalPage());
            voPage.setTotalSize(advertiseStrategyAdGroupPage.getTotalSize());
            if (CollectionUtils.isNotEmpty(advertiseStrategyAdGroupPage.getRows())) {
                List<AdvertiseStrategyAdGroup> advertiseStrategyAdGroupList = advertiseStrategyAdGroupPage.getRows();
                List<AdvertiseStrategyStatus> list = Lists.newArrayListWithCapacity(advertiseStrategyAdGroupList.size());
                List<String> campaignIds = advertiseStrategyAdGroupList.stream().map(AdvertiseStrategyAdGroup::getCampaignId).collect(Collectors.toList());
                List<String> adGroupIds = advertiseStrategyAdGroupList.stream().map(AdvertiseStrategyAdGroup::getAdGroupId).collect(Collectors.toList());
                List<AmazonAdCampaignAll> campaignAlls = amazonAdCampaignAllDao.listByCampaignIdNoType(param.getPuid(),param.getShopId(),campaignIds);
                Map<String, AmazonAdCampaignAll> campaignMap = null;
                Map<String, AmazonAdGroup> amazonAdGroupMap = null;
                Map<String, AmazonSbAdGroup> amazonSbAdGroupMap = null;
                Map<String, AmazonSdAdGroup> amazonSdAdGroupMap = null;
                Map<String, AmazonAdPortfolio> portfolioMap = null;
                if (CollectionUtils.isNotEmpty(campaignAlls)) {
                    List<String> portfolioIdList = campaignAlls.stream().map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(portfolioIdList)) {
                        List<AmazonAdPortfolio> amazonAdPortfolioList = portfolioDao.getPortfolioList(param.getPuid(),param.getShopId(),portfolioIdList);
                        if (CollectionUtils.isNotEmpty(amazonAdPortfolioList)) {
                            portfolioMap = amazonAdPortfolioList.stream().filter(Objects::nonNull).
                                    collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                        }
                    }
                    campaignMap = campaignAlls.stream().filter(Objects::nonNull).
                            collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
                if ((CollectionUtils.isEmpty(param.getAdTypeList()) || AdType.SP.name().equalsIgnoreCase(param.getAdTypeList().get(0))) && CollectionUtils.isNotEmpty(adGroupIds)) {
                    List<AmazonAdGroup> adGroupList = amazonAdGroupDao.getAdGroupByIds(param.getPuid(), adGroupIds);
                    if (CollectionUtils.isNotEmpty(adGroupList)) {
                        amazonAdGroupMap = adGroupList.stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity(), (e1,e2)->e1));
                    }
                }
                if ((CollectionUtils.isEmpty(param.getAdTypeList()) || AdType.SB.name().equalsIgnoreCase(param.getAdTypeList().get(0))) && CollectionUtils.isNotEmpty(adGroupIds)) {
                    List<AmazonSbAdGroup> adGroupList = amazonSbAdGroupDao.getAdGroupByIds(param.getPuid(), adGroupIds);
                    if (CollectionUtils.isNotEmpty(adGroupList)) {
                        amazonSbAdGroupMap = adGroupList.stream().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, Function.identity(), (e1,e2)->e1));
                    }
                }
                if ((CollectionUtils.isEmpty(param.getAdTypeList()) || AdType.SD.name().equalsIgnoreCase(param.getAdTypeList().get(0))) && CollectionUtils.isNotEmpty(adGroupIds)) {
                    List<AmazonSdAdGroup> adGroupList = amazonSdAdGroupDao.listByGroupId(param.getPuid(), param.getShopId(), adGroupIds);
                    if (CollectionUtils.isNotEmpty(adGroupList)) {
                        amazonSdAdGroupMap = adGroupList.stream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, Function.identity(), (e1,e2)->e1));
                    }
                }
                for (AdvertiseStrategyAdGroup vo : advertiseStrategyAdGroupList) {
                    AdvertiseStrategyStatus advertiseStrategyStatus = new AdvertiseStrategyStatus();
                    advertiseStrategyStatus.setPuid(vo.getPuid());
                    advertiseStrategyStatus.setShopId(vo.getShopId());
                    advertiseStrategyStatus.setId(vo.getId());
                    advertiseStrategyStatus.setStatus(vo.getStatus());
                    advertiseStrategyStatus.setAdType(vo.getAdType());
                    advertiseStrategyStatus.setTaskId(vo.getTaskId());
                    advertiseStrategyStatus.setMarketplaceId(vo.getMarketplaceId());
                    advertiseStrategyStatus.setVersion(vo.getVersion());
                    advertiseStrategyStatus.setTemplateId(vo.getTemplateId());
                    advertiseStrategyStatus.setCampaignId(vo.getCampaignId());
                    if (CollectionUtils.isNotEmpty(queryIsTargetItemIdList)) {
                        if (queryIsTargetItemIdList.contains(vo.getAdGroupId())) {
                            advertiseStrategyStatus.setIsTargetItem(1);
                        } else {
                            advertiseStrategyStatus.setIsTargetItem(0);
                        }
                    } else {
                        advertiseStrategyStatus.setIsTargetItem(0);
                    }
                    if (MapUtils.isNotEmpty(amazonAdGroupMap) && amazonAdGroupMap.containsKey(vo.getAdGroupId())) {
                        AmazonAdGroup amazonAdGroup = amazonAdGroupMap.get(vo.getAdGroupId());
                        advertiseStrategyStatus.setAdGroupId(vo.getAdGroupId());
                        advertiseStrategyStatus.setGroupName(amazonAdGroup.getName());
                        advertiseStrategyStatus.setState(amazonAdGroup.getState());
                        advertiseStrategyStatus.setServingStatus(amazonAdGroup.getServingStatus());
                        AmazonAdGroup.servingStatusEnum byCode = UCommonUtil.getByCode(amazonAdGroup.getServingStatus(), AmazonAdGroup.servingStatusEnum.class);
                        if (byCode != null) {
                            advertiseStrategyStatus.setServingStatusName(byCode.getName());
                            advertiseStrategyStatus.setServingStatusDec(byCode.getDescription());
                        } else {
                            advertiseStrategyStatus.setServingStatusDec(amazonAdGroup.getServingStatus());
                            advertiseStrategyStatus.setServingStatusName(amazonAdGroup.getServingStatus());
                        }
                    } else if (MapUtils.isNotEmpty(amazonSbAdGroupMap) && amazonSbAdGroupMap.containsKey(vo.getAdGroupId())) {
                        AmazonSbAdGroup amazonSbAdGroup = amazonSbAdGroupMap.get(vo.getAdGroupId());
                        advertiseStrategyStatus.setAdGroupId(vo.getAdGroupId());
                        advertiseStrategyStatus.setGroupName(amazonSbAdGroup.getName());
                        advertiseStrategyStatus.setState(amazonSbAdGroup.getState());
                        advertiseStrategyStatus.setServingStatus(amazonSbAdGroup.getServingStatus());
                        AmazonSbAdGroup.servingStatusEnum byCode = UCommonUtil.getByCode(amazonSbAdGroup.getServingStatus(), AmazonSbAdGroup.servingStatusEnum.class);
                        if (byCode != null) {
                            advertiseStrategyStatus.setServingStatusName(byCode.getName());
                            advertiseStrategyStatus.setServingStatusDec(byCode.getDescription());
                        } else {
                            advertiseStrategyStatus.setServingStatusDec(amazonSbAdGroup.getServingStatus());
                            advertiseStrategyStatus.setServingStatusName(amazonSbAdGroup.getServingStatus());
                        }
                    } else if (MapUtils.isNotEmpty(amazonSdAdGroupMap) && amazonSdAdGroupMap.containsKey(vo.getAdGroupId())) {
                        AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupMap.get(vo.getAdGroupId());
                        advertiseStrategyStatus.setAdGroupId(vo.getAdGroupId());
                        advertiseStrategyStatus.setGroupName(amazonSdAdGroup.getName());
                        advertiseStrategyStatus.setState(amazonSdAdGroup.getState());
                        advertiseStrategyStatus.setServingStatus(amazonSdAdGroup.getServingStatus());
                        AmazonSdAdGroup.servingStatusEnum byCode = UCommonUtil.getByCode(amazonSdAdGroup.getServingStatus(), AmazonSdAdGroup.servingStatusEnum.class);
                        if (byCode != null) {
                            advertiseStrategyStatus.setServingStatusName(byCode.getName());
                            advertiseStrategyStatus.setServingStatusDec(byCode.getDescription());
                        } else {
                            advertiseStrategyStatus.setServingStatusDec(amazonSdAdGroup.getServingStatus());
                            advertiseStrategyStatus.setServingStatusName(amazonSdAdGroup.getServingStatus());
                        }
                    }
                    if (MapUtils.isNotEmpty(campaignMap) && campaignMap.containsKey(vo.getCampaignId())) {
                        AmazonAdCampaignAll adCampaignAll = campaignMap.get(vo.getCampaignId());
                        advertiseStrategyStatus.setCampaignName(adCampaignAll.getName());
                        Optional.ofNullable(adCampaignAll.getBudgetType()).filter(StringUtils::isNotEmpty)
                                .ifPresent(advertiseStrategyStatus::setBudgetType);
                        if (StringUtils.isNotBlank(adCampaignAll.getPortfolioId())) {
                            advertiseStrategyStatus.setPortfolioId(adCampaignAll.getPortfolioId());
                            if (MapUtils.isNotEmpty(portfolioMap) && portfolioMap.containsKey(adCampaignAll.getPortfolioId())) {
                                AmazonAdPortfolio adPortfolio = portfolioMap.get(adCampaignAll.getPortfolioId());
                                if (adPortfolio != null) {
                                    advertiseStrategyStatus.setPortfolioName(adPortfolio.getName());
                                }
                            }
                        }
                    }
                    list.add(advertiseStrategyStatus);
                }
                voPage.setRows(list);
            }
            pageResult.setCode(Result.SUCCESS);
            pageResult.setData(voPage);
        } catch (Exception e) {
            pageResult.setCode(Result.ERROR);
            pageResult.setMsg("查询广告组受控对象异常");
            log.error("traceId:{} puid:{} shopId:{} 查询广告组受控对象异常:", param.getTraceId(),param.getPuid(), param.getShopId(), e);
        }
        return pageResult;
    }

    @Override
    public Integer queryTargetRealTimeBid(Integer puid, Integer shopId, List<TargetRealTimeBidParam> paramList) {
        Integer reslut = 1;
        try {
            Result<List<TargetRealTimeBidResult>> result = advertiseStrategyQueryService.queryTargetRealTimeBid(puid, shopId, paramList);
            Map<String, TargetRealTimeBidResult> targetRealTimeBidResultMap = null;
            if (result.getCode() == Result.SUCCESS && CollectionUtils.isNotEmpty(result.getData())) {
                targetRealTimeBidResultMap = result.getData().stream().collect(Collectors.toMap(TargetRealTimeBidResult::getTargetId, Function.identity(), (e1,e2)->e1));
            }
            List<AdvertiseStrategyRealTimeBid> advertiseStrategyRealTimeBidList = Lists.newArrayList();
            List<Long> recordIdList = advertiseStrategyTaskRecordSequenceDao.batchGenId(paramList.size());
            for (int i = 0; i < paramList.size(); i++) {
                AdvertiseStrategyRealTimeBid advertiseStrategyRealTimeBid = new AdvertiseStrategyRealTimeBid();
                TargetRealTimeBidParam param = paramList.get(i);
                advertiseStrategyRealTimeBid.setId(recordIdList.get(i));
                advertiseStrategyRealTimeBid.setPuid(puid);
                advertiseStrategyRealTimeBid.setShopId(shopId);
                advertiseStrategyRealTimeBid.setStatuId(param.getStatusId());
                advertiseStrategyRealTimeBid.setMarketplaceId(param.getMarketplaceId());
                advertiseStrategyRealTimeBid.setRecordId(param.getRecordId());
                advertiseStrategyRealTimeBid.setCampaignId(param.getCampaignId());
                advertiseStrategyRealTimeBid.setAdGroupId(param.getAdGroupId());
                advertiseStrategyRealTimeBid.setTargetId(param.getTargetId());
                advertiseStrategyRealTimeBid.setTargetName(param.getTargetName());
                advertiseStrategyRealTimeBid.setTargetType(param.getTargetType());
                Integer code = TaskAdType.getCode(param.getAdType());
                if (code == null) {
                    advertiseStrategyRealTimeBid.setAdType(3);
                } else {
                    advertiseStrategyRealTimeBid.setAdType(TaskAdType.getCode(param.getAdType()));
                }
                if (MapUtils.isNotEmpty(targetRealTimeBidResultMap) && targetRealTimeBidResultMap.containsKey(param.getTargetId())) {
                    if (!"获取失败".equals(targetRealTimeBidResultMap.get(param.getTargetId()).getRealTimeBiddingValue()) && !"0.00".equals(targetRealTimeBidResultMap.get(param.getTargetId()).getRealTimeBiddingValue())) {
                        advertiseStrategyRealTimeBid.setState(1);
                        advertiseStrategyRealTimeBid.setBiddingValue(new BigDecimal(targetRealTimeBidResultMap.get(param.getTargetId()).getRealTimeBiddingValue()));
                    } else {
                        reslut = -1;
                        advertiseStrategyRealTimeBid.setState(-1);
                        advertiseStrategyRealTimeBid.setStateError("获取实时竞价失败，请稍后重试");
                    }
                } else {
                    reslut = -1;
                    advertiseStrategyRealTimeBid.setState(-1);
                    advertiseStrategyRealTimeBid.setStateError("获取实时竞价失败，请稍后重试");
                }
                advertiseStrategyRealTimeBidList.add(advertiseStrategyRealTimeBid);
            }
            if (CollectionUtils.isNotEmpty(advertiseStrategyRealTimeBidList)) {
                advertiseStrategyRealTimeBidDao.batchInsert(puid, advertiseStrategyRealTimeBidList,false);
            }
        } catch (Exception e) {
            reslut = -1;
            log.error("puid:{} shopId:{} 查询实时竞价异常:",puid, shopId, e);
        }
        return reslut;
    }


    // (通用信息)及受控对象操作增加日志
    private void addControlledObjectsLog(Integer puid, Integer uid, String loginIp, String itemType, Long templateId, AdManageOperationLog adManageOperationLog, Boolean flag) {
        adManageOperationLog.setAction(OperationLogActionEnum.EDIT.getOperationValue());
        adManageOperationLog.setPuid(puid);
        adManageOperationLog.setUid(uid);
        adManageOperationLog.setIp(loginIp);
        adManageOperationLog.setTemplateId(templateId);
        if (flag) {
            if (ItemTypeEnum.TARGET.getItemType().equals(itemType)) {
                adManageOperationLog.setOperationObject(OperationLogTargetEnum.BIDDING_TEMPLATE.getTargetValue());
            }
        } else {
            if (ItemTypeEnum.TARGET.getItemType().equals(itemType)) {
                adManageOperationLog.setOperationObject(OperationLogTargetEnum.BIDDING_STRATEGY.getTargetValue());
            }
        }
    }


    // 执行成功通用处理方法
    private void filterBaseSuccess(AdManageOperationLog adManageOperationLog, List<AdManageOperationLog> adManageOperationLogList, Boolean flag) {
        adManageOperationLog.setResult(flag ? 0 : 1);
        adManageOperationLogList.add(adManageOperationLog);
        adManageOperationLogService.printAdOtherOperationLog(adManageOperationLogList);
    }

    // 执行成功通用处理方法
    private void filterSuccess(AdManageOperationLog adManageOperationLog, List<OperationContent> operationContents) {
        adManageOperationLog.setMessage(adManageOperationLog.handleMessage(operationContents));
        String operaContent = JSONUtil.objectToJson(operationContents);
        adManageOperationLog.setOperationContent(operaContent);
        adManageOperationLog.setResult(0);
    }

    // 填充基本信息
    private void filterBaseMessage(String adType, String marketplaceId, String campaignId, String adGroupId, AdManageOperationLog adManageOperationLog, String targetId) {
        adManageOperationLog.setAdType(adType.toLowerCase());
        adManageOperationLog.setCampaignId(campaignId);
        adManageOperationLog.setAdGroupId(StringUtils.isNotBlank(adGroupId) ? adGroupId : null);
        adManageOperationLog.setMarketplaceId(marketplaceId);
        adManageOperationLog.setModule(OperationLogModuleEnum.AD.getModuleValue());
        adManageOperationLog.setSubModule(OperationLogSubModuleEnum.AD_MANAGE.getSubModuleValue());
        adManageOperationLog.setTargetId(targetId);
    }

    // 填充策略编辑基本信息
    private void filterEditStrategyBaseMessage(AdManageOperationLog adManageOperationLog, AdvertiseStrategyStatus advertiseStrategyStatus, Integer puid, Integer updateId, Integer shopId, String loginIp) {
        adManageOperationLog.setAction(OperationLogActionEnum.EDIT.getOperationValue());
        adManageOperationLog.setPuid(puid);
        adManageOperationLog.setUid(updateId);
        adManageOperationLog.setShopId(shopId);
        adManageOperationLog.setIp(loginIp);
        adManageOperationLog.setMarketplaceId(advertiseStrategyStatus.getMarketplaceId());
        adManageOperationLog.setCampaignId(advertiseStrategyStatus.getCampaignId());
        adManageOperationLog.setAdGroupId(StringUtils.isNotBlank(advertiseStrategyStatus.getAdGroupId()) ? advertiseStrategyStatus.getAdGroupId() : null);
        adManageOperationLog.setAdType(advertiseStrategyStatus.getAdType().toLowerCase());
        adManageOperationLog.setTargetId(advertiseStrategyStatus.getItemId());
        adManageOperationLog.setOperationObject(OperationLogTargetEnum.SWITCH_STRATEGY.getTargetValue());

    }


    // 拼接分时启停策略编辑信息
    private void getOperationContent(Integer day, Integer startTimeSite, Integer endTimeSite, String state,
                                     List<OperationContent> operationContents) {
        StringBuilder builder = new StringBuilder();
        OperationContent switchRuleContent = new OperationContent();
        builder.append(startTimeSite).append(":00 ~ ").append(endTimeSite).append(":00 ").append(ItemTypeEnum.getItemTypeEnumItemValue(state));
        switchRuleContent.setTitle("活动当前策略规则");
        switchRuleContent.setName(getDayOfTheWeek(day));
        switchRuleContent.setNewValue(builder.toString());
        operationContents.add(switchRuleContent);
    }

    /**
     * 转换为星期几文案
     *
     * @param siteDate
     * @return
     */
    private String getDayOfTheWeek(Integer siteDate) {
        if (siteDate == 1) {
            return "每周一";
        }
        if (siteDate == 2) {
            return "每周二";
        }
        if (siteDate == 3) {
            return "每周三";
        }
        if (siteDate == 4) {
            return "每周四";
        }
        if (siteDate == 5) {
            return "每周五";
        }
        if (siteDate == 6) {
            return "每周六";
        }
        if (siteDate == 7) {
            return "每周日";
        }
        if (siteDate == 0) {
            return "每日";
        }
        return "";
    }


    /**
     * 同步更新模板日志记录
     *
     * @param puid
     * @param template
     * @param updateStrategyVoList
     */
    private void addSyncTemplateOperationLog(Integer puid, AdvertiseStrategyTemplate template, List<UpdateStrategyVo> updateStrategyVoList, Integer updateId) {
        List<AdManageOperationLog> list = new ArrayList<>();
        List<OperationContent> contentList = new ArrayList<>();
        List<Long> statusIdList = updateStrategyVoList.stream().map(UpdateStrategyVo::getStatusId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(statusIdList)) {
            return;
        }
        List<AdvertiseStrategyStatus> strategyStatuses = advertiseStrategyStatusDao.getListByLongIdList(puid, statusIdList);
        OperationContent operationContent = new OperationContent();
        operationContent.setTitle("分时启停同步更新为最新模板");
        contentList.add(operationContent);
        String content = JSONUtil.objectToJson(operationContent);

        for (AdvertiseStrategyStatus strategyStatus : strategyStatuses) {
            AdManageOperationLog adManageOperationLog = new AdManageOperationLog();

            adManageOperationLog.setPuid(puid);
            adManageOperationLog.setUid(updateId);
            adManageOperationLog.setShopId(template.getShopId());
            adManageOperationLog.setMarketplaceId(template.getMarketplaceId());
            adManageOperationLog.setTemplateId(template.getId());

            adManageOperationLog.setCampaignId(strategyStatus.getCampaignId());
            adManageOperationLog.setAdType(strategyStatus.getAdType().toLowerCase());
            adManageOperationLog.setAdGroupId(strategyStatus.getAdGroupId());
            adManageOperationLog.setTargetName(strategyStatus.getTargetName());
            adManageOperationLog.setTargetType(strategyStatus.getTargetType());
            adManageOperationLog.setTargetId(strategyStatus.getItemId());

            adManageOperationLog.setAdjustmentRange(OperationLogAdjustmentRangeEnum.SYNC_LASTED_TEMPLATED.getAdjustmentType());
            adManageOperationLog.setOperationContent(content);
            adManageOperationLog.setResult(0);
            adManageOperationLog.setUnixTimestamp(System.currentTimeMillis());
            adManageOperationLog.setId(MD5Util.getMD5(adManageOperationLog.deDeduplicationId()));
            adManageOperationLog.setMessage(adManageOperationLog.handleMessage(contentList));
            list.add(adManageOperationLog);
        }
        adManageOperationLogService.printAdOperationLog(list);
    }

    // 填充受控对象信息
    private void filterMatchData(Integer puid, List<UpdateStrategyVo> updateStrategyVoList, List<AdManageOperationLog> list,Integer updateId) {
        List<Long> statusIdList = updateStrategyVoList.stream().map(UpdateStrategyVo::getStatusId).collect(Collectors.toList());
        List<AdvertiseStrategyStatus> strategyStatuses = advertiseStrategyStatusDao.getListByLongIdList(puid, statusIdList);
        Map<Long, AdvertiseStrategyStatus> advertiseStrategyStatusMap = strategyStatuses.stream().collect(Collectors.toMap(AdvertiseStrategyStatus::getId, Function.identity(), (a, b) -> a));
        updateStrategyVoList.forEach(e -> {
            OperationContent operationContent = new OperationContent();
            AdvertiseStrategyStatus advertiseStrategyStatus = advertiseStrategyStatusMap.get(e.getStatusId());
            AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
            adManageOperationLog.setAction(OperationLogActionEnum.EDIT.getOperationValue());
            adManageOperationLog.setPuid(puid);
            adManageOperationLog.setShopId(advertiseStrategyStatus.getShopId());
            adManageOperationLog.setUid(updateId);
            adManageOperationLog.setMarketplaceId(advertiseStrategyStatus.getMarketplaceId());
            adManageOperationLog.setTargetId(advertiseStrategyStatus.getItemId());
            adManageOperationLog.setCampaignId(advertiseStrategyStatus.getCampaignId());
            adManageOperationLog.setAdType(advertiseStrategyStatus.getType());
            adManageOperationLog.setOperationObject(OperationLogTargetEnum.SWITCH_STRATEGY.getTargetValue());
            operationContent.setTitle(Constants.STATE_VALUE_TITLE);
            operationContent.setPreviousValue(e.getOriginState());
            operationContent.setNewValue(e.getState());
            adManageOperationLog.setOperationContent(JSONUtil.objectToJson(operationContent));
            list.add(adManageOperationLog);
        });
        filterBaseSuccess(new AdManageOperationLog(), list, true);
    }

    /**
     * 获取广告组id和名称map
     * @param puid
     * @param shopId
     * @param groupIdTypeMap 键：广告组id  值：广告类型sp、sb、sd
     * @return
     */
    private Map<String, String> getGroupIdNameMap(Integer puid, Integer shopId, Map<String, String> groupIdTypeMap) {
        if (MapUtils.isEmpty(groupIdTypeMap)) {
            return new HashMap<>();
        }
        //获取广告组名称
        List<String> groupIdList = new ArrayList<>();
        List<String> sbGroupIdList = new ArrayList<>();
        List<String> sdGroupIdList = new ArrayList<>();
        Map<String, String> groupIdMap = new HashMap<>();
        groupIdTypeMap.forEach((k, v) -> {
            if (AdType.SP.name().equalsIgnoreCase(v)) {
                groupIdList.add(k);
            } else if (AdType.SB.name().equalsIgnoreCase(v)) {
                sbGroupIdList.add(k);
            } else if (AdType.SD.name().equalsIgnoreCase(v)) {
                sdGroupIdList.add(k);
            }
        });
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            groupIdMap.putAll(amazonAdGroupDao.getAdGroupByIds(puid, shopId, groupIdList).stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, AmazonAdGroup::getName)));
        }
        if (CollectionUtils.isNotEmpty(sbGroupIdList)) {
            groupIdMap.putAll(amazonSbAdGroupDao.getAdGroupByIds(puid, shopId, sbGroupIdList).stream().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, AmazonSbAdGroup::getName)));
        }
        if (CollectionUtils.isNotEmpty(sdGroupIdList)) {
            groupIdMap.putAll(amazonSdAdGroupDao.getByGroupIds(puid, shopId, sdGroupIdList).stream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, AmazonSdAdGroup::getName)));
        }
        return groupIdMap;
    }

    //转移受控对象日志添加
    private void addTransferLog(Integer puid, AdManageOperationLog adManageOperationLog, OperationContent operationContent, AdManageOperationLog addAdManageOperationLog, OperationContent addOperationContent, AdvertiseStrategyTemplate vo, List<AdvertiseStrategyAdGroup> advertiseStrategyAdGroupList) {
        Set<String> campaignIdSet = advertiseStrategyAdGroupList.stream().map(AdvertiseStrategyAdGroup::getCampaignId).collect(Collectors.toSet());
        Map<String, String> groupIdTypeMap = advertiseStrategyAdGroupList.stream().collect(Collectors.toMap(AdvertiseStrategyAdGroup::getAdGroupId, AdvertiseStrategyAdGroup::getAdType));
        Map<String, String> groupIdNameMap = this.getGroupIdNameMap(puid, advertiseStrategyAdGroupList.get(0).getShopId(), groupIdTypeMap);
        Set<String> groupIdList = groupIdNameMap.keySet();
        adManageOperationLog.setAdGroupId(StringUtils.join(groupIdList, "、"));
        addAdManageOperationLog.setAdGroupId(StringUtils.join(groupIdList, "、"));
        adManageOperationLog.setCampaignId(StringUtils.join(campaignIdSet, "、"));
        addAdManageOperationLog.setCampaignId(StringUtils.join(campaignIdSet, "、"));
        operationContent.setTitle("移除受控对象");
        operationContent.setNewValue(StringUtils.join(groupIdNameMap.values(), "、"));
        adManageOperationLog.setOperationContent(JSONUtil.objectToJson(operationContent));
        addOperationContent.setTitle("添加受控对象");
        addOperationContent.setNewValue(StringUtils.join(groupIdNameMap.values(), "、"));
        addAdManageOperationLog.setOperationContent(JSONUtil.objectToJson(addOperationContent));
        adManageOperationLog.setResult(1);
        addAdManageOperationLog.setResult(1);
        adManageOperationLog.setShopId(vo.getShopId());
        adManageOperationLog.setMarketplaceId(vo.getMarketplaceId());
        addAdManageOperationLog.setShopId(vo.getShopId());
        addAdManageOperationLog.setMarketplaceId(vo.getMarketplaceId());
    }
}
