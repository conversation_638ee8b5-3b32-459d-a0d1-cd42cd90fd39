package com.meiyunji.sponsored.service.util;

import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sellfox.aadas.api.entry.TargetStatusPb;
import com.meiyunji.sellfox.aadas.api.enumeration.CampaignStatePb;
import com.meiyunji.sellfox.aadas.api.enumeration.MarketplacePb;
import com.meiyunji.sellfox.aadas.api.service.*;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.grpc.common.AdHourReportResponsePb;
import com.meiyunji.sponsored.grpc.common.AdProductHourReportApiServiceOuterClass;
import com.meiyunji.sponsored.grpc.common.AdWeekReportResponsePb;
import com.meiyunji.sponsored.grpc.entry.*;
import com.meiyunji.sponsored.rpc.adCommon.AdCampaignHourRpcVo;
import com.meiyunji.sponsored.rpc.pricing.template.Portfolio;
import com.meiyunji.sponsored.rpc.pricing.template.PortfolioHourRule;
import com.meiyunji.sponsored.rpc.vo.CommonResponse;
import com.meiyunji.sponsored.service.enums.CurrencyUnitEnum;
import com.meiyunji.sponsored.service.reportHour.vo.*;
import com.meiyunji.sponsored.service.strategy.enums.AdStrategyType;
import com.meiyunji.sponsored.service.strategy.enums.PolicyTypeEnum;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategySchedule;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTopBudgetSchedule;
import com.meiyunji.sponsored.service.strategy.vo.OriginValueVo;
import com.meiyunji.sponsored.service.strategy.vo.PortfolioHourRuleVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

public class PbUtil {
    public static List<SetAccountBudgetTaskRequestPb.SetAccountBudgetTaskRequest.Item> toAccountBudgetItem(List<AdvertiseStrategyTopBudgetSchedule> schedules) {
        return schedules.stream().map(item -> SetAccountBudgetTaskRequestPb.SetAccountBudgetTaskRequest.Item.newBuilder()
                .setDay(item.getDay())
                .setStart(item.getStart())
                .setEnd(item.getEnd())
                .setBudget(String.valueOf(item.getNewToBudget())).build()).collect(Collectors.toList());
    }

    public static List<SetCampaignBudgetStrategyScheduleRequestPb
            .SetCampaignBudgetStrategyScheduleRequest.Item> toCampaignBudgetItem(List<AdvertiseStrategySchedule> schedules) {
        return schedules.stream().map(item -> SetCampaignBudgetStrategyScheduleRequestPb
                .SetCampaignBudgetStrategyScheduleRequest.Item.newBuilder()
                .setDay(item.getDay())
                .setStart(item.getStart())
                .setEnd(item.getEnd())
                .setValue(String.valueOf(item.getNewBudgetValue())).build()).collect(Collectors.toList());
    }

    public static MarketplacePb.Marketplace toPb(Marketplace marketplace) {
        return MarketplacePb.Marketplace.valueOf(marketplace.name());
    }

    /**
     * 构造失败消息
     */
    public static CommonResponse buildFail(String message) {
        return CommonResponse.newBuilder()
                .setCode(Int32Value.of(Result.ERROR))
                .setMsg(message).build();
    }

    /**
     * 构造成功消息
     */
    public static CommonResponse buildSuccess(String message) {
        return CommonResponse.newBuilder()
                .setCode(Int32Value.of(Result.SUCCESS))
                .setMsg(message).build();
    }

    /**
     * 构造成功消息
     */
    public static CommonResponse buildSuccess(String message, String data) {
        return CommonResponse.newBuilder()
                .setCode(Int32Value.of(Result.SUCCESS))
                .setData(data)
                .setMsg(message).build();
    }

    /**
     * 复用成功返回对象
     */
    private final static CommonResponse SUCCESS = CommonResponse.newBuilder()
            .setCode(Int32Value.of(Result.SUCCESS))
            .setMsg("success").build();
    /**
     * 构造成功消息
     */
    public static CommonResponse buildSuccess() {
        return SUCCESS;
    }

    public static List<SetCampaignPlacementStrategyScheduleRequestPb
            .SetCampaignPlacementStrategyScheduleRequest.Item> toCampaignPlacementItem(List<AdvertiseStrategySchedule> schedules) {

        return schedules.stream().map(item -> {
            //拼装广告位参数
            ArrayList<TargetStatusPb.CampaignPlacementStatus.Adjustment> adjustments = new ArrayList<>(2);
            if (item.getNewAdPlaceTopValue() != null) {
                adjustments.add(TargetStatusPb.CampaignPlacementStatus.Adjustment.newBuilder()
                        .setPredicate(TargetStatusPb.CampaignPlacementStatus.PredicateType.placementTop)
                        .setPercentage(item.getNewAdPlaceTopValue().doubleValue()).build());
            }
            if (item.getNewAdPlaceProductValue() != null) {
                adjustments.add(TargetStatusPb.CampaignPlacementStatus.Adjustment.newBuilder()
                        .setPredicate(TargetStatusPb.CampaignPlacementStatus.PredicateType.placementProductPage)
                        .setPercentage(item.getNewAdPlaceProductValue().doubleValue()).build());
            }
            if (item.getNewAdOtherValue() != null) {
                adjustments.add(TargetStatusPb.CampaignPlacementStatus.Adjustment.newBuilder()
                        .setPredicate(TargetStatusPb.CampaignPlacementStatus.PredicateType.placementRestOfSearch)
                        .setPercentage(item.getNewAdOtherValue().doubleValue()).build());
            }
            TargetStatusPb.CampaignPlacementStatus.Builder addAllAdjustmentBuilder =
                    TargetStatusPb.CampaignPlacementStatus.newBuilder()
                            .addAllAdjustments(adjustments);
            if (StringUtils.isNotBlank(item.getNewStrategy())) {
                addAllAdjustmentBuilder.setStrategy(TargetStatusPb.CampaignPlacementStatus
                        .Strategy.valueOf(item.getNewStrategy()));
            }

            return SetCampaignPlacementStrategyScheduleRequestPb.SetCampaignPlacementStrategyScheduleRequest
                    .Item.newBuilder()
                    .setDay(item.getDay())
                    .setStart(item.getStart())
                    .setEnd(item.getEnd())
                    .setValue(addAllAdjustmentBuilder.build())
                    .build();
        }).collect(Collectors.toList());
    }

    public static List<SetKeywordBidStrategyScheduleRequestPb.SetKeywordBidStrategyScheduleRequest.Item> toKeywordItem(List<AdvertiseStrategySchedule> schedules) {
        return schedules.stream().map(item -> SetKeywordBidStrategyScheduleRequestPb
                .SetKeywordBidStrategyScheduleRequest.Item.newBuilder()
                .setDay(item.getDay())
                .setStart(item.getStart())
                .setEnd(item.getEnd())
                .setBid(String.valueOf(item.getNewBiddingValue())).build()).collect(Collectors.toList());
    }

    public static List<SetGroupKeywordBidStrategyScheduleRequestPb.SetGroupKeywordBidStrategyScheduleRequest.Item> toAdGroupKeywordItem(List<AdvertiseStrategySchedule> schedules) {
        return schedules.stream().map(item -> SetGroupKeywordBidStrategyScheduleRequestPb
                .SetGroupKeywordBidStrategyScheduleRequest.Item.newBuilder()
                .setDay(item.getDay())
                .setStart(item.getStart())
                .setEnd(item.getEnd())
                .setBid(String.valueOf(item.getNewBiddingValue())).build()).collect(Collectors.toList());
    }

    public static List<SetTargetBidStrategyScheduleRequestPb.SetTargetBidStrategyScheduleRequest.Item> toTargetItem(List<AdvertiseStrategySchedule> schedules) {
        return schedules.stream().map(item -> SetTargetBidStrategyScheduleRequestPb
                .SetTargetBidStrategyScheduleRequest.Item.newBuilder()
                .setDay(item.getDay())
                .setStart(item.getStart())
                .setEnd(item.getEnd())
                .setBid(String.valueOf(item.getNewBiddingValue())).build()).collect(Collectors.toList());
    }

    public static List<SetGroupTargetBidStrategyScheduleRequestPb.SetGroupTargetBidStrategyScheduleRequest.Item> toAdGroupTargetItem(List<AdvertiseStrategySchedule> schedules) {
        return schedules.stream().map(item -> SetGroupTargetBidStrategyScheduleRequestPb.
                SetGroupTargetBidStrategyScheduleRequest.Item.newBuilder()
                .setDay(item.getDay())
                .setStart(item.getStart())
                .setEnd(item.getEnd())
                .setBid(String.valueOf(item.getNewBiddingValue())).build()).collect(Collectors.toList());
    }

    public static List<SetCampaignStateTaskRequestPb.SetCampaignStateTaskRequest.Item> toCampaignStateItem(List<AdvertiseStrategySchedule> schedules) {
        return schedules.stream().map(item -> SetCampaignStateTaskRequestPb
                .SetCampaignStateTaskRequest.Item.newBuilder()
                .setDay(item.getDay())
                .setStart(item.getStart())
                .setEnd(item.getEnd())
                .setValue(CampaignStatePb.CampaignState.valueOf(item.getNewStateValue().toUpperCase())).build()).collect(Collectors.toList());
    }


    public static List<SetProductStateTaskRequestPb.SetProductStateTaskRequest.Item> toProductStateItem(List<AdvertiseStrategySchedule> schedules) {
        return schedules.stream().map(item -> SetProductStateTaskRequestPb
                .SetProductStateTaskRequest.Item.newBuilder()
                .setDay(item.getDay())
                .setStart(item.getStart())
                .setEnd(item.getEnd())
                .setValue(CampaignStatePb.CampaignState.valueOf(item.getNewStateValue().toUpperCase())).build()).collect(Collectors.toList());
    }

    public static List<SetCampaignPortfolioTaskRequestPb.CampaignPortfolio.Item> toPortfolioItem(List<AdvertiseStrategySchedule> schedules) {
        List<SetCampaignPortfolioTaskRequestPb.CampaignPortfolio.Item> list = new ArrayList<>();
        schedules.stream().forEach(e->{
            SetCampaignPortfolioTaskRequestPb.CampaignPortfolio.Item.Builder builder = SetCampaignPortfolioTaskRequestPb.CampaignPortfolio.Item.newBuilder();
            if (e.getDay() != null) {
                builder.setDay(e.getDay());
            }
            if (StringUtils.isNotBlank(e.getCountDate())) {
                builder.setDate(e.getCountDate());
            }
            builder.setBudget(e.getNewAmountValue().doubleValue());
            list.add(builder.build());
        });
        return list;
    }

    public static Portfolio buildPortfolioByJson(String json) {
        OriginValueVo valueVo = JSONUtil.jsonToObject(json, OriginValueVo.class);
        if (valueVo == null) {
            throw new RuntimeException("请求参数错误");
        }
        Portfolio.Builder returnValue = Portfolio.newBuilder();
        returnValue.setPolicy(valueVo.getPolicy());
        if (valueVo.getAmount() != null) {
            returnValue.setAmount(valueVo.getAmount().doubleValue());
        }
        return returnValue.build();
    }

    public static boolean isPortfolioHour(String childrenItemType) {
        return StringUtils.isNotBlank(childrenItemType) && ("HOUR".equals(childrenItemType) || AdStrategyType.WEEKLY.name().equals(childrenItemType));
    }


    public static List<PortfolioHourRule> buildPortfolioHourRuleByJson(String json, String marketplaceId) {
        List<PortfolioHourRuleVo> portfolioRuleList = JSONUtil.jsonToArray(json, PortfolioHourRuleVo.class);
        if (portfolioRuleList == null) {
            throw new RuntimeException("请求参数错误");
        }
        List<PortfolioHourRule> portfolioRules = Lists.newArrayList();
        Map<Integer, List<PortfolioHourRuleVo>> listMap = portfolioRuleList.stream().collect(Collectors.groupingBy(PortfolioHourRuleVo::getSiteDate));
        listMap.forEach((key, value) ->{
            PortfolioHourRule.Builder builder = PortfolioHourRule.newBuilder();
            builder.setSiteDate(key);
            builder.setSiteDateName(StringUtil.toStringSafe(DATE_MAP.get(key)));
            for (PortfolioHourRuleVo vo : value) {
                Portfolio.Builder portfolio = Portfolio.newBuilder();
                portfolio.setStartTimeSite(vo.getStartTimeSite());
                portfolio.setEndTimeSite(vo.getEndTimeSite());
                portfolio.setAmount(vo.getAmount().doubleValue());
                portfolio.setPolicy(vo.getPolicy());
                MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(marketplaceId);
                if (null != m) {
                    portfolio.setSymbol(m.getCurrencyCode());
                }
                builder.addPortfolios(portfolio);
            }
            portfolioRules.add(builder.build());
        });
        return portfolioRules;
    }

    public static StringBuilder builderLogInfo(PortfolioHourRuleVo portfolioHourRuleVo, MarketTimezoneAndCurrencyEnum m) {
        StringBuilder builder = new StringBuilder();
        String budgetValue = PolicyTypeEnum.noBudget.name().equals(portfolioHourRuleVo.getPolicy()) ? "无预算上限" : portfolioHourRuleVo.getAmount().setScale(2, RoundingMode.HALF_UP).toString();
        String currencyCode = PolicyTypeEnum.noBudget.name().equals(portfolioHourRuleVo.getPolicy()) ? "" : (m != null ? m.getCurrencyCode() : "");
        builder.append(DATE__MAP.get(portfolioHourRuleVo.getSiteDate())).append(portfolioHourRuleVo.getStartTimeSite()).append(":00 ~ ").append(portfolioHourRuleVo.getEndTimeSite()).append(":00").append(" 预算值调整为 ").append(budgetValue).append(currencyCode);
        return builder;
    }

    private static final ImmutableMap<Integer,String> DATE_MAP = new ImmutableMap.Builder<Integer,String>()
            .put(0, "每日")
            .put(1, "周一")
            .put(2, "周二")
            .put(3, "周三")
            .put(4, "周四")
            .put(5, "周五")
            .put(6, "周六")
            .put(7, "周日").build();

    private static final ImmutableMap<Integer,String> DATE__MAP = new ImmutableMap.Builder<Integer,String>()
            .put(0, "每日:")
            .put(1, "每周一:")
            .put(2, "每周二:")
            .put(3, "每周三:")
            .put(4, "每周四:")
            .put(5, "每周五:")
            .put(6, "每周六:")
            .put(7, "每周日:").build();

    public static AdReportDataRpcVoPb.AdReportDataRpcVo toHourReportPb(AdKeywordAndTargetHourVo vo) {
        AdReportDataRpcVoPb.AdReportDataRpcVo.Builder builderVo = AdReportDataRpcVoPb.AdReportDataRpcVo.newBuilder();
        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompare() != null){
            builderVo.setAdCostCompare(vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompareRate() != null){
            builderVo.setAdCostCompareRate(vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAcos() != null){
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getLabel() != null){
            builderVo.setTitle(vo.getLabel());
        }
        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if(vo.getImpressionsCompare() != null){
            builderVo.setImpressionsCompare(vo.getImpressionsCompare());
        }
        if(vo.getImpressionsCompareRate() != null){
            builderVo.setImpressionsCompareRate(vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if(vo.getClicksCompare() != null ){
            builderVo.setClicksCompare(vo.getClicksCompare());
        }
        if(vo.getClicksCompareRate() != null ){
            builderVo.setClicksCompareRate(vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if(vo.getAdOrderNumCompare()!= null){
            builderVo.setAdOrderNumCompare(vo.getAdOrderNumCompare());
        }
        if(vo.getAdOrderNumCompareRate()!= null){
            builderVo.setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if(vo.getAdSale()!= null){
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompare() != null){
            builderVo.setAdSaleCompare(vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompareRate() != null){
            builderVo.setAdSaleCompareRate(vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if(vo.getAdSaleNumCompare() != null){
            builderVo.setAdSaleNumCompare(vo.getAdSaleNumCompare());
        }
        if(vo.getAdSaleNumCompareRate() != null){
            builderVo.setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }

        if (vo.getBidAdjust() != null) {
            builderVo.setBidAdjust(vo.getBidAdjust());
        } else {
            builderVo.setBidAdjust("");
        }

        if (vo.getBidAdjustMax() != null) {
            builderVo.setBidAdjustMax(vo.getBidAdjustMax().toString());
        } else {
            builderVo.setBidAdjustMax("");
        }

        builderVo.setViewableImpressions(getIntegerZeroValue(vo.getViewableImpressions()));
        builderVo.setVcpm(getZeroValue(vo.getVcpm()));
        builderVo.setOrdersNewToBrand(getIntegerZeroValue(vo.getOrdersNewToBrand()));
        builderVo.setUnitsOrderedNewToBrand(getIntegerZeroValue(vo.getUnitsOrderedNewToBrand()));
        builderVo.setSalesNewToBrand(getZeroValue(vo.getSalesNewToBrand()));
        builderVo.setOrdersNewToBrandPercentage(getZeroValue(vo.getOrdersNewToBrandPercentage()));
        builderVo.setUnitsOrderedNewToBrandPercentage(getZeroValue(vo.getUnitsOrderedNewToBrandPercentage()));
        builderVo.setSalesNewToBrandPercentage(getZeroValue(vo.getSalesNewToBrandPercentage()));

        return builderVo.build();
    }

    public static AdReportDataRpcVoPb.AdReportDataRpcVo toHourReportPb(AdKeywordAndTargetHourVo vo, BigDecimal shopSalesByDate, boolean isVc) {
        AdReportDataRpcVoPb.AdReportDataRpcVo.Builder builderVo = AdReportDataRpcVoPb.AdReportDataRpcVo.newBuilder();
        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
//            builderVo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate).setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAcots() != null) {
            builderVo.setAcots(vo.getAcots().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompare() != null){
            builderVo.setAdCostCompare(vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompareRate() != null){
            builderVo.setAdCostCompareRate(vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getRoasCompare()).ifPresent(i -> builderVo.setRoasCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getRoasCompareRate()).ifPresent(i -> builderVo.setRoasCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getAdCostPerClickCompare()).ifPresent(i -> builderVo.setAdCostPerClickCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getAdCostPerClickCompareRate()).ifPresent(i -> builderVo.setAdCostPerClickCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getAcos() != null){
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getAcosCompare()).ifPresent(i -> builderVo.setAcosCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getAcosCompareRate()).ifPresent(i -> builderVo.setAcosCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getLabel() != null){
            builderVo.setTitle(vo.getLabel());
        }
        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if(vo.getImpressionsCompare() != null){
            builderVo.setImpressionsCompare(vo.getImpressionsCompare());
        }
        if(vo.getImpressionsCompareRate() != null){
            builderVo.setImpressionsCompareRate(vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if(vo.getClicksCompare() != null ){
            builderVo.setClicksCompare(vo.getClicksCompare());
        }
        if(vo.getClicksCompareRate() != null ){
            builderVo.setClicksCompareRate(vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getCtrCompare()).ifPresent(i -> builderVo.setCtrCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getCtrCompareRate()).ifPresent(i -> builderVo.setCtrCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getCvrCompare()).ifPresent(i -> builderVo.setCvrCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getCvrCompareRate()).ifPresent(i -> builderVo.setCvrCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if(vo.getAdOrderNumCompare()!= null){
            builderVo.setAdOrderNumCompare(vo.getAdOrderNumCompare());
        }
        if(vo.getAdOrderNumCompareRate()!= null){
            builderVo.setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if(vo.getAdSale()!= null){
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
//            builderVo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate).setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAsots() != null) {
            builderVo.setAsots(vo.getAsots().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompare() != null){
            builderVo.setAdSaleCompare(vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompareRate() != null){
            builderVo.setAdSaleCompareRate(vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if(vo.getAdSaleNumCompare() != null){
            builderVo.setAdSaleNumCompare(vo.getAdSaleNumCompare());
        }
        if(vo.getAdSaleNumCompareRate() != null){
            builderVo.setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() != null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() != null ? vo.getOrderNumPercentage().toString() : "0.00");

        builderVo.setViewableImpressions(getIntegerZeroValue(vo.getViewableImpressions()));
        builderVo.setVrt(getZeroValue(vo.getVrt()));
        builderVo.setVcpm(getZeroValue(vo.getVcpm()));
        builderVo.setVCtr(getZeroValue(vo.getVCtr()));
        builderVo.setOrdersNewToBrand(getIntegerZeroValue(vo.getOrdersNewToBrand()));
        builderVo.setUnitsOrderedNewToBrand(getIntegerZeroValue(vo.getUnitsOrderedNewToBrand()));
        builderVo.setSalesNewToBrand(getZeroValue(vo.getSalesNewToBrand()));
        builderVo.setAdvertisingUnitPrice(getZeroValue(vo.getAdvertisingUnitPrice()));
        builderVo.setAdvertisingProductUnitPrice(getZeroValue(vo.getAdvertisingProductUnitPrice()));
        builderVo.setAdvertisingOtherProductUnitPrice(getZeroValue(vo.getAdvertisingOtherProductUnitPrice()));
        builderVo.setOrdersNewToBrandPercentage(getZeroValue(vo.getOrdersNewToBrandPercentage()));
        builderVo.setUnitsOrderedNewToBrandPercentage(getZeroValue(vo.getUnitsOrderedNewToBrandPercentage()));
        builderVo.setSalesNewToBrandPercentage(getZeroValue(vo.getSalesNewToBrandPercentage()));
        if (isVc) {
            builderVo.setAsots("-");
            builderVo.setAcots("-");
        }
        return builderVo.build();
    }


    public static AdReportDataRpcVoPb.AdReportDataRpcVo toProductHourReportPb(AdProductHourVo vo) {
        AdReportDataRpcVoPb.AdReportDataRpcVo.Builder builderVo = AdReportDataRpcVoPb.AdReportDataRpcVo.newBuilder();
        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompare() != null){
            builderVo.setAdCostCompare(vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompareRate() != null){
            builderVo.setAdCostCompareRate(vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getRoasCompare()).ifPresent(i -> builderVo.setRoasCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getRoasCompareRate()).ifPresent(i -> builderVo.setRoasCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getAdCostPerClickCompare()).ifPresent(i -> builderVo.setAdCostPerClickCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getAdCostPerClickCompareRate()).ifPresent(i -> builderVo.setAdCostPerClickCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getAcos() != null){
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getAcosCompare()).ifPresent(i -> builderVo.setAcosCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getAcosCompareRate()).ifPresent(i -> builderVo.setAcosCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getLabel() != null){
            builderVo.setTitle(vo.getLabel());
        }
        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if(vo.getImpressionsCompare() != null){
            builderVo.setImpressionsCompare(vo.getImpressionsCompare());
        }
        if(vo.getImpressionsCompareRate() != null){
            builderVo.setImpressionsCompareRate(vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if(vo.getClicksCompare() != null ){
            builderVo.setClicksCompare(vo.getClicksCompare());
        }
        if(vo.getClicksCompareRate() != null ){
            builderVo.setClicksCompareRate(vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getCtrCompare()).ifPresent(i -> builderVo.setCtrCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getCtrCompareRate()).ifPresent(i -> builderVo.setCtrCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getCvrCompare()).ifPresent(i -> builderVo.setCvrCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getCvrCompareRate()).ifPresent(i -> builderVo.setCvrCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if(vo.getAdOrderNumCompare()!= null){
            builderVo.setAdOrderNumCompare(vo.getAdOrderNumCompare());
        }
        if(vo.getAdOrderNumCompareRate()!= null){
            builderVo.setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if(vo.getAdSale()!= null){
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompare() != null){
            builderVo.setAdSaleCompare(vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompareRate() != null){
            builderVo.setAdSaleCompareRate(vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if(vo.getAdSaleNumCompare() != null){
            builderVo.setAdSaleNumCompare(vo.getAdSaleNumCompare());
        }
        if(vo.getAdSaleNumCompareRate() != null){
            builderVo.setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() !=null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() !=null ? vo.getOrderNumPercentage().toString() : "0.00");

        return builderVo.build();
    }

    public static AdCampaignHourRpcVo toCampaignHourReportPb(AdCampaignHourVo vo,BigDecimal shopSalesByDate, boolean isVc) {
        AdCampaignHourRpcVo.Builder builderVo = AdCampaignHourRpcVo.newBuilder();
        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
            if (!isVc) {
                builderVo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate).setScale(2, RoundingMode.HALF_UP).toString());
            }
        }
        if(vo.getAdCostCompare() != null){
            builderVo.setAdCostCompare(vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompareRate() != null){
            builderVo.setAdCostCompareRate(vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getRoasCompare() != null) {
            builderVo.setRoasCompare(vo.getRoasCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getRoasCompareRate() != null) {
            builderVo.setRoasCompareRate(vo.getRoasCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostPerClickCompare() != null) {
            builderVo.setAdCostPerClickCompare(vo.getAdCostPerClickCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostPerClickCompareRate() != null) {
            builderVo.setAdCostPerClickCompareRate(vo.getAdCostPerClickCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }

        if (vo.getAcos() != null) {
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAcosCompare() != null) {
            builderVo.setAcosCompare(vo.getAcosCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAcosCompareRate() != null) {
            builderVo.setAcosCompareRate(vo.getAcosCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }


        if(vo.getLabel() != null){
            builderVo.setTitle(vo.getLabel());
        }
        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if(vo.getImpressionsCompare() != null){
            builderVo.setImpressionsCompare(vo.getImpressionsCompare());
        }
        if(vo.getImpressionsCompareRate() != null){
            builderVo.setImpressionsCompareRate(vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if(vo.getClicksCompare() != null ){
            builderVo.setClicksCompare(vo.getClicksCompare());
        }
        if(vo.getClicksCompareRate() != null ){
            builderVo.setClicksCompareRate(vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCtrCompare() != null) {
            builderVo.setCtrCompare(vo.getCtrCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCtrCompareRate() != null) {
            builderVo.setCtrCompareRate(vo.getCtrCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCvrCompare() != null) {
            builderVo.setCvrCompare(vo.getCvrCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCvrCompareRate() != null) {
            builderVo.setCvrCompareRate(vo.getCvrCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if(vo.getAdOrderNumCompare()!= null){
            builderVo.setAdOrderNumCompare(vo.getAdOrderNumCompare());
        }
        if(vo.getAdOrderNumCompareRate()!= null){
            builderVo.setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if (!isVc) {
            if(vo.getAdSale()!= null){
                builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
                builderVo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate).setScale(2, RoundingMode.HALF_UP).toString());
            }
        }

        if(vo.getAdSaleCompare() != null){
            builderVo.setAdSaleCompare(vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompareRate() != null){
            builderVo.setAdSaleCompareRate(vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if(vo.getAdSaleNumCompare() != null){
            builderVo.setAdSaleNumCompare(vo.getAdSaleNumCompare());
        }
        if(vo.getAdSaleNumCompareRate() != null){
            builderVo.setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() !=null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() !=null ? vo.getOrderNumPercentage().toString() : "0.00");

        builderVo.setViewableImpressions(getIntegerZeroValue(vo.getViewableImpressions()));
        builderVo.setVrt(getZeroValue(vo.getVrt()));
        builderVo.setVcpm(getZeroValue(vo.getVcpm()));
        builderVo.setVCtr(getZeroValue(vo.getVCtr()));
        builderVo.setOrdersNewToBrand(getIntegerZeroValue(vo.getOrdersNewToBrand()));
        builderVo.setUnitsOrderedNewToBrand(getIntegerZeroValue(vo.getUnitsOrderedNewToBrand()));
        builderVo.setSalesNewToBrand(getZeroValue(vo.getSalesNewToBrand()));
        builderVo.setAdvertisingUnitPrice(getZeroValue(vo.getAdvertisingUnitPrice()));
        builderVo.setAdvertisingProductUnitPrice(getZeroValue(vo.getAdvertisingProductUnitPrice()));
        builderVo.setAdvertisingOtherProductUnitPrice(getZeroValue(vo.getAdvertisingOtherProductUnitPrice()));
        builderVo.setOrdersNewToBrandPercentage(getZeroValue(vo.getOrdersNewToBrandPercentage()));
        builderVo.setUnitsOrderedNewToBrandPercentage(getZeroValue(vo.getUnitsOrderedNewToBrandPercentage()));
        builderVo.setSalesNewToBrandPercentage(getZeroValue(vo.getSalesNewToBrandPercentage()));

        if (vo.getBudgetAdjust() != null) {
            builderVo.setBudgetAdjust(vo.getBudgetAdjust());
        } else {
            builderVo.setBudgetAdjust("");
        }
        if (vo.getBudgetAdjustMax() != null) {
            builderVo.setBudgetAdjustMax(vo.getBudgetAdjustMax().toString());
        } else {
            builderVo.setBudgetAdjustMax("");
        }

        if (vo.getBudgetSurplus() != null) {
            builderVo.setBudgetSurplus(vo.getBudgetSurplus());
        } else {
            builderVo.setBudgetSurplus("");
        }
        if (vo.getBudgetUsage() != null) {
            AdCampaignHourRpcVo.BudgetUsage.Builder builder = AdCampaignHourRpcVo.BudgetUsage.newBuilder();
            if (vo.getBudgetUsage().getIsNoData() != null) {
                builder.setIsNoData(vo.getBudgetUsage().getIsNoData());
            }
            builder.setCurrentBudget(vo.getBudgetUsage().getCurrentBudget());
            builder.setPercent(vo.getBudgetUsage().getPercent());
            builderVo.addBudgetUsages(builder.build());
        }

        return builderVo.build();
    }

    public static AdPlacementHourRpcVoPb.AdPlacementHourRpcVo toPlacementHourReportPb(AdPlacementHourVo vo) {
        AdPlacementHourRpcVoPb.AdPlacementHourRpcVo.Builder builderVo = AdPlacementHourRpcVoPb.AdPlacementHourRpcVo.newBuilder();
        //广告花费
        if (vo.getAdCost() != null) {
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostCompare() != null) {
            builderVo.setAdCostCompare(vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostCompareRate() != null) {
            builderVo.setAdCostCompareRate(vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //广告曝光量
        if (vo.getImpressions() != null) {
            builderVo.setImpressions(vo.getImpressions());
        }
        if (vo.getImpressionsCompare() != null) {
            builderVo.setImpressionsCompare(vo.getImpressionsCompare());
        }
        if (vo.getImpressionsCompareRate() != null) {
            builderVo.setImpressionsCompareRate(vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //广告点击量
        if (vo.getClicks() != null) {
            builderVo.setClicks(vo.getClicks());
        }
        if (vo.getClicksCompare() != null) {
            builderVo.setClicksCompare(vo.getClicksCompare());
        }
        if (vo.getClicksCompareRate() != null) {
            builderVo.setClicksCompareRate(vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //cpa
        if (vo.getCpa() != null) {
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCpaCompare() != null) {
            builderVo.setCpaCompare(vo.getCpaCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCpaCompareRate() != null) {
            builderVo.setCpaCompareRate(vo.getCpaCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //CPC
        if (vo.getAdCostPerClick() != null) {
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostPerClickCompare() != null) {
            builderVo.setAdCostPerClickCompare(vo.getAdCostPerClickCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostPerClickCompareRate() != null) {
            builderVo.setAdCostPerClickCompareRate(vo.getAdCostPerClickCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //广告点击率
        if (vo.getCtr() != null) {
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCtrCompare() != null) {
            builderVo.setCtrCompare(vo.getCtrCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCtrCompareRate() != null) {
            builderVo.setCtrCompareRate(vo.getCtrCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //广告转化率
        if (vo.getCvr() != null) {
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCvrCompare() != null) {
            builderVo.setCvrCompare(vo.getCvrCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCvrCompareRate() != null) {
            builderVo.setCvrCompareRate(vo.getCvrCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //ACoS
        if (vo.getAcos() != null) {
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAcosCompare() != null) {
            builderVo.setAcosCompare(vo.getAcosCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAcosCompareRate() != null) {
            builderVo.setAcosCompareRate(vo.getAcosCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //ROAS
        if (vo.getRoas() != null) {
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getRoasCompare() != null) {
            builderVo.setRoasCompare(vo.getRoasCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getRoasCompareRate() != null) {
            builderVo.setRoasCompareRate(vo.getRoasCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //广告订单量
        if (vo.getAdOrderNum() != null) {
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if (vo.getAdOrderNumCompare() != null) {
            builderVo.setAdOrderNumCompare(vo.getAdOrderNumCompare());
        }
        if (vo.getAdOrderNumCompareRate() != null) {
            builderVo.setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //本广告产品订单量
        if (vo.getSelfAdOrderNum() != null) {
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if (vo.getSelfAdOrderNumCompare() != null) {
            builderVo.setSelfAdOrderNumCompare(vo.getSelfAdOrderNumCompare());
        }
        if (vo.getSelfAdOrderNumCompareRate() != null) {
            builderVo.setSelfAdOrderNumCompareRate(vo.getSelfAdOrderNumCompareRate().toString());
        }
        //其他产品广告订单量
        if (vo.getOtherAdOrderNum() != null) {
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if (vo.getOtherAdOrderNumCompare() != null) {
            builderVo.setOtherAdOrderNumCompare(vo.getOtherAdOrderNumCompare());
        }
        if (vo.getOtherAdOrderNumCompareRate() != null) {
            builderVo.setOtherAdOrderNumCompareRate(vo.getOtherAdOrderNumCompareRate().toString());
        }
        //广告销售额
        if (vo.getAdSale() != null) {
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleCompare() != null) {
            builderVo.setAdSaleCompare(vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleCompareRate() != null) {
            builderVo.setAdSaleCompareRate(vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //本广告产品销售额
        if (vo.getAdSelfSale() != null) {
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSaleCompare() != null) {
            builderVo.setAdSelfSaleCompare(vo.getAdSelfSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSaleCompareRate() != null) {
            builderVo.setAdSelfSaleCompareRate(vo.getAdSelfSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //其他产品广告销售额
        if (vo.getAdOtherSale() != null) {
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdOtherSaleCompare() != null) {
            builderVo.setAdOtherSaleCompare(vo.getAdOtherSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdOtherSaleCompareRate() != null) {
            builderVo.setAdOtherSaleCompareRate(vo.getAdOtherSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //广告销量
        if (vo.getAdSaleNum() != null) {
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if (vo.getAdSaleNumCompare() != null) {
            builderVo.setAdSaleNumCompare(vo.getAdSaleNumCompare());
        }
        if (vo.getAdSaleNumCompareRate() != null) {
            builderVo.setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //本广告产品销量
        if (vo.getAdSelfSaleNum() != null) {
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if (vo.getAdSelfSaleNumCompare() != null) {
            builderVo.setAdSelfSaleNumCompare(vo.getAdSelfSaleNumCompare());
        }
        if (vo.getAdSelfSaleNumCompareRate() != null) {
            builderVo.setAdSelfSaleNumCompareRate(vo.getAdSelfSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //其他产品广告销量
        if (vo.getAdOtherSaleNum() != null) {
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        if (vo.getAdOtherSaleNumCompare() != null) {
            builderVo.setAdOtherSaleNumCompare(vo.getAdOtherSaleNumCompare());
        }
        if (vo.getAdOtherSaleNumCompareRate() != null) {
            builderVo.setAdOtherSaleNumCompareRate(vo.getAdOtherSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }

        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() != null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() != null ? vo.getOrderNumPercentage().toString() : "0.00");

        if (vo.getLabel() != null) {
            builderVo.setTitle(vo.getLabel());
        }
        return builderVo.build();
    }

    public static AdGroupHourRpcVoPb.AdGroupHourRpcVo toGroupHourReportPb(AdGroupHourVo vo, BigDecimal shopSale) {
        AdGroupHourRpcVoPb.AdGroupHourRpcVo.Builder builderVo = AdGroupHourRpcVoPb.AdGroupHourRpcVo.newBuilder();
        //广告花费
        if (vo.getAdCost() != null) {
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostCompare() != null) {
            builderVo.setAdCostCompare(vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostCompareRate() != null) {
            builderVo.setAdCostCompareRate(vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //广告曝光量
        if (vo.getImpressions() != null) {
            builderVo.setImpressions(vo.getImpressions());
        }
        if (vo.getImpressionsCompare() != null) {
            builderVo.setImpressionsCompare(vo.getImpressionsCompare());
        }
        if (vo.getImpressionsCompareRate() != null) {
            builderVo.setImpressionsCompareRate(vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //广告点击量
        if (vo.getClicks() != null) {
            builderVo.setClicks(vo.getClicks());
        }
        if (vo.getClicksCompare() != null) {
            builderVo.setClicksCompare(vo.getClicksCompare());
        }
        if (vo.getClicksCompareRate() != null) {
            builderVo.setClicksCompareRate(vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //cpa
        if (vo.getCpa() != null) {
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCpaCompare() != null) {
            builderVo.setCpaCompare(vo.getCpaCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCpaCompareRate() != null) {
            builderVo.setCpaCompareRate(vo.getCpaCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //CPC
        if (vo.getAdCostPerClick() != null) {
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostPerClickCompare() != null) {
            builderVo.setAdCostPerClickCompare(vo.getAdCostPerClickCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostPerClickCompareRate() != null) {
            builderVo.setAdCostPerClickCompareRate(vo.getAdCostPerClickCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //广告点击率
        if (vo.getCtr() != null) {
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCtrCompare() != null) {
            builderVo.setCtrCompare(vo.getCtrCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCtrCompareRate() != null) {
            builderVo.setCtrCompareRate(vo.getCtrCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //广告转化率
        if (vo.getCvr() != null) {
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCvrCompare() != null) {
            builderVo.setCvrCompare(vo.getCvrCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCvrCompareRate() != null) {
            builderVo.setCvrCompareRate(vo.getCvrCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //ACoS
        if (vo.getAcos() != null) {
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAcosCompare() != null) {
            builderVo.setAcosCompare(vo.getAcosCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAcosCompareRate() != null) {
            builderVo.setAcosCompareRate(vo.getAcosCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //ROAS
        if (vo.getRoas() != null) {
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getRoasCompare() != null) {
            builderVo.setRoasCompare(vo.getRoasCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getRoasCompareRate() != null) {
            builderVo.setRoasCompareRate(vo.getRoasCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //广告订单量
        if (vo.getAdOrderNum() != null) {
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if (vo.getAdOrderNumCompare() != null) {
            builderVo.setAdOrderNumCompare(vo.getAdOrderNumCompare());
        }
        if (vo.getAdOrderNumCompareRate() != null) {
            builderVo.setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //本广告产品订单量
        if (vo.getSelfAdOrderNum() != null) {
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if (vo.getSelfAdOrderNumCompare() != null) {
            builderVo.setSelfAdOrderNumCompare(vo.getSelfAdOrderNumCompare());
        }
        if (vo.getSelfAdOrderNumCompareRate() != null) {
            builderVo.setSelfAdOrderNumCompareRate(vo.getSelfAdOrderNumCompareRate().toString());
        }
        //其他产品广告订单量
        if (vo.getOtherAdOrderNum() != null) {
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if (vo.getOtherAdOrderNumCompare() != null) {
            builderVo.setOtherAdOrderNumCompare(vo.getOtherAdOrderNumCompare());
        }
        if (vo.getOtherAdOrderNumCompareRate() != null) {
            builderVo.setOtherAdOrderNumCompareRate(vo.getOtherAdOrderNumCompareRate().toString());
        }
        //广告销售额
        if (vo.getAdSale() != null) {
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleCompare() != null) {
            builderVo.setAdSaleCompare(vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleCompareRate() != null) {
            builderVo.setAdSaleCompareRate(vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //本广告产品销售额
        if (vo.getAdSelfSale() != null) {
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSaleCompare() != null) {
            builderVo.setAdSelfSaleCompare(vo.getAdSelfSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSaleCompareRate() != null) {
            builderVo.setAdSelfSaleCompareRate(vo.getAdSelfSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //其他产品广告销售额
        if (vo.getAdOtherSale() != null) {
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdOtherSaleCompare() != null) {
            builderVo.setAdOtherSaleCompare(vo.getAdOtherSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdOtherSaleCompareRate() != null) {
            builderVo.setAdOtherSaleCompareRate(vo.getAdOtherSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //广告销量
        if (vo.getAdSaleNum() != null) {
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if (vo.getAdSaleNumCompare() != null) {
            builderVo.setAdSaleNumCompare(vo.getAdSaleNumCompare());
        }
        if (vo.getAdSaleNumCompareRate() != null) {
            builderVo.setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //本广告产品销量
        if (vo.getAdSelfSaleNum() != null) {
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if (vo.getAdSelfSaleNumCompare() != null) {
            builderVo.setAdSelfSaleNumCompare(vo.getAdSelfSaleNumCompare());
        }
        if (vo.getAdSelfSaleNumCompareRate() != null) {
            builderVo.setAdSelfSaleNumCompareRate(vo.getAdSelfSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //其他产品广告销量
        if (vo.getAdOtherSaleNum() != null) {
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        if (vo.getAdOtherSaleNumCompare() != null) {
            builderVo.setAdOtherSaleNumCompare(vo.getAdOtherSaleNumCompare());
        }
        if (vo.getAdOtherSaleNumCompareRate() != null) {
            builderVo.setAdOtherSaleNumCompareRate(vo.getAdOtherSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }

        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() != null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() != null ? vo.getOrderNumPercentage().toString() : "0.00");

        builderVo.setViewableImpressions(getIntegerZeroValue(vo.getViewableImpressions()));
        builderVo.setVrt(getZeroValue(vo.getVrt()));
        builderVo.setVcpm(getZeroValue(vo.getVcpm()));
        builderVo.setVCtr(getZeroValue(vo.getVCtr()));
        builderVo.setOrdersNewToBrand(getIntegerZeroValue(vo.getOrdersNewToBrand()));
        builderVo.setUnitsOrderedNewToBrand(getIntegerZeroValue(vo.getUnitsOrderedNewToBrand()));
        builderVo.setSalesNewToBrand(getZeroValue(vo.getSalesNewToBrand()));
        builderVo.setAdvertisingUnitPrice(getZeroValue(vo.getAdvertisingUnitPrice()));
        builderVo.setAdvertisingProductUnitPrice(getZeroValue(vo.getAdvertisingProductUnitPrice()));
        builderVo.setOrdersNewToBrandPercentage(getZeroValue(vo.getOrdersNewToBrandPercentage()));
        builderVo.setUnitsOrderedNewToBrandPercentage(getZeroValue(vo.getUnitsOrderedNewToBrandPercentage()));
        builderVo.setAdvertisingOtherProductUnitPrice(getZeroValue(vo.getAdvertisingOtherProductUnitPrice()));
        builderVo.setSalesNewToBrandPercentage(getZeroValue(vo.getSalesNewToBrandPercentage()));


        //广告销售额
        if (vo.getAdSale() != null) {
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
            builderVo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSale).setScale(2, RoundingMode.HALF_UP).toString());
        }

        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
            builderVo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSale).setScale(2, RoundingMode.HALF_UP).toString());
        }

        if (vo.getLabel() != null) {
            builderVo.setTitle(vo.getLabel());
        }
        return builderVo.build();
    }

    public static AdHourReportResponsePb.AdReportHourRpcVo toAdReportHourlyPb(AdReportHourlyVO vo, BigDecimal shopSalesByDate) {
        AdHourReportResponsePb.AdReportHourRpcVo.Builder builderVo = AdHourReportResponsePb.AdReportHourRpcVo.newBuilder();
        //广告花费
        if (vo.getAdCost() != null) {
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
            builderVo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate).setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostCompare() != null) {
            builderVo.setAdCostCompare(vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostCompareRate() != null) {
            builderVo.setAdCostCompareRate(vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //广告曝光量
        if (vo.getImpressions() != null) {
            builderVo.setImpressions(vo.getImpressions());
        }
        if (vo.getImpressionsCompare() != null) {
            builderVo.setImpressionsCompare(vo.getImpressionsCompare());
        }
        if (vo.getImpressionsCompareRate() != null) {
            builderVo.setImpressionsCompareRate(vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //广告点击量
        if (vo.getClicks() != null) {
            builderVo.setClicks(vo.getClicks());
        }
        if (vo.getClicksCompare() != null) {
            builderVo.setClicksCompare(vo.getClicksCompare());
        }
        if (vo.getClicksCompareRate() != null) {
            builderVo.setClicksCompareRate(vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //cpa
        if (vo.getCpa() != null) {
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCpaCompare() != null) {
            builderVo.setCpaCompare(vo.getCpaCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCpaCompareRate() != null) {
            builderVo.setCpaCompareRate(vo.getCpaCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //CPC
        if (vo.getAdCostPerClick() != null) {
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostPerClickCompare() != null) {
            builderVo.setAdCostPerClickCompare(vo.getAdCostPerClickCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostPerClickCompareRate() != null) {
            builderVo.setAdCostPerClickCompareRate(vo.getAdCostPerClickCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //广告点击率
        if (vo.getCtr() != null) {
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCtrCompare() != null) {
            builderVo.setCtrCompare(vo.getCtrCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCtrCompareRate() != null) {
            builderVo.setCtrCompareRate(vo.getCtrCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //广告转化率
        if (vo.getCvr() != null) {
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCvrCompare() != null) {
            builderVo.setCvrCompare(vo.getCvrCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCvrCompareRate() != null) {
            builderVo.setCvrCompareRate(vo.getCvrCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //ACoS
        if (vo.getAcos() != null) {
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAcosCompare() != null) {
            builderVo.setAcosCompare(vo.getAcosCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAcosCompareRate() != null) {
            builderVo.setAcosCompareRate(vo.getAcosCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //ROAS
        if (vo.getRoas() != null) {
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getRoasCompare() != null) {
            builderVo.setRoasCompare(vo.getRoasCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getRoasCompareRate() != null) {
            builderVo.setRoasCompareRate(vo.getRoasCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //广告订单量
        if (vo.getAdOrderNum() != null) {
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if (vo.getAdOrderNumCompare() != null) {
            builderVo.setAdOrderNumCompare(vo.getAdOrderNumCompare());
        }
        if (vo.getAdOrderNumCompareRate() != null) {
            builderVo.setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //本广告产品订单量
        if (vo.getSelfAdOrderNum() != null) {
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if (vo.getSelfAdOrderNumCompare() != null) {
            builderVo.setSelfAdOrderNumCompare(vo.getSelfAdOrderNumCompare());
        }
        if (vo.getSelfAdOrderNumCompareRate() != null) {
            builderVo.setSelfAdOrderNumCompareRate(vo.getSelfAdOrderNumCompareRate().toString());
        }
        //其他产品广告订单量
        if (vo.getOtherAdOrderNum() != null) {
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if (vo.getOtherAdOrderNumCompare() != null) {
            builderVo.setOtherAdOrderNumCompare(vo.getOtherAdOrderNumCompare());
        }
        if (vo.getOtherAdOrderNumCompareRate() != null) {
            builderVo.setOtherAdOrderNumCompareRate(vo.getOtherAdOrderNumCompareRate().toString());
        }
        //广告销售额
        if (vo.getAdSale() != null) {
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
            builderVo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate).setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleCompare() != null) {
            builderVo.setAdSaleCompare(vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleCompareRate() != null) {
            builderVo.setAdSaleCompareRate(vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //本广告产品销售额
        if (vo.getAdSelfSale() != null) {
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSaleCompare() != null) {
            builderVo.setAdSelfSaleCompare(vo.getAdSelfSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSaleCompareRate() != null) {
            builderVo.setAdSelfSaleCompareRate(vo.getAdSelfSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //其他产品广告销售额
        if (vo.getAdOtherSale() != null) {
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdOtherSaleCompare() != null) {
            builderVo.setAdOtherSaleCompare(vo.getAdOtherSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdOtherSaleCompareRate() != null) {
            builderVo.setAdOtherSaleCompareRate(vo.getAdOtherSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //广告销量
        if (vo.getAdSaleNum() != null) {
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if (vo.getAdSaleNumCompare() != null) {
            builderVo.setAdSaleNumCompare(vo.getAdSaleNumCompare());
        }
        if (vo.getAdSaleNumCompareRate() != null) {
            builderVo.setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //本广告产品销量
        if (vo.getAdSelfSaleNum() != null) {
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if (vo.getAdSelfSaleNumCompare() != null) {
            builderVo.setAdSelfSaleNumCompare(vo.getAdSelfSaleNumCompare());
        }
        if (vo.getAdSelfSaleNumCompareRate() != null) {
            builderVo.setAdSelfSaleNumCompareRate(vo.getAdSelfSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //其他产品广告销量
        if (vo.getAdOtherSaleNum() != null) {
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        if (vo.getAdOtherSaleNumCompare() != null) {
            builderVo.setAdOtherSaleNumCompare(vo.getAdOtherSaleNumCompare());
        }
        if (vo.getAdOtherSaleNumCompareRate() != null) {
            builderVo.setAdOtherSaleNumCompareRate(vo.getAdOtherSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }

        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() != null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() != null ? vo.getOrderNumPercentage().toString() : "0.00");

        if (vo.getLabel() != null) {
            builderVo.setTitle(vo.getLabel());
        }

        builderVo.setViewableImpressions(getIntegerZeroValue(vo.getViewableImpressions()));
        builderVo.setVrt(getZeroValue(vo.getVrt()));
        builderVo.setVcpm(getZeroValue(vo.getVcpm()));
        builderVo.setVCtr(getZeroValue(vo.getVCtr()));
        builderVo.setOrdersNewToBrand(getIntegerZeroValue(vo.getOrdersNewToBrand()));
        builderVo.setUnitsOrderedNewToBrand(getIntegerZeroValue(vo.getUnitsOrderedNewToBrand()));
        builderVo.setSalesNewToBrand(getZeroValue(vo.getSalesNewToBrand()));
        builderVo.setAdvertisingUnitPrice(getZeroValue(vo.getAdvertisingUnitPrice()));
        builderVo.setAdvertisingProductUnitPrice(getZeroValue(vo.getAdvertisingProductUnitPrice()));
        builderVo.setAdvertisingOtherProductUnitPrice(getZeroValue(vo.getAdvertisingOtherProductUnitPrice()));
        builderVo.setOrdersNewToBrandPercentage(getZeroValue(vo.getOrdersNewToBrandPercentage()));
        builderVo.setUnitsOrderedNewToBrandPercentage(getZeroValue(vo.getUnitsOrderedNewToBrandPercentage()));
        builderVo.setSalesNewToBrandPercentage(getZeroValue(vo.getSalesNewToBrandPercentage()));
        return builderVo.build();
    }

    private static String getZeroValue(BigDecimal bigDecimal) {
        return  bigDecimal != null ? bigDecimal.setScale(2, RoundingMode.HALF_UP).toString() : "0.00";
    }

    private static int getIntegerZeroValue(Integer number) {
        return  number != null ? number : 0;
    }

    public static AdTargetHourOfAdRpcVoPb.AdTargetHourOfAdRpcVo toTargetPb(AdKeywordTargetHourOfAdVo vo) {
        AdTargetHourOfAdRpcVoPb.AdTargetHourOfAdRpcVo.Builder builderVo =
                AdTargetHourOfAdRpcVoPb.AdTargetHourOfAdRpcVo.newBuilder();
        if (StringUtils.isNotBlank(vo.getAsin())) {
            builderVo.setAsin(vo.getAsin());
        }
        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAcos() != null){
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if(vo.getAdSale()!= null){
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        if (CollectionUtils.isNotEmpty(vo.getDetails())) {
            builderVo.addAllDetail(vo.getDetails().stream().map(PbUtil::toPb).collect(Collectors.toList()));
        }
        return builderVo.build();
    }

    public static AdKeywordHourOfAdRpcVoPb.AdKeywordHourOfAdRpcVo toKeywordPb(AdKeywordTargetHourOfAdVo vo) {
        AdKeywordHourOfAdRpcVoPb.AdKeywordHourOfAdRpcVo.Builder builderVo = AdKeywordHourOfAdRpcVoPb.AdKeywordHourOfAdRpcVo.newBuilder();

        if(StringUtils.isNotBlank(vo.getAsin())){
            builderVo.setAsin(vo.getAsin());
        }
        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAcos() != null){
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if(vo.getAdSale()!= null){
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        if (CollectionUtils.isNotEmpty(vo.getDetails())) {
            builderVo.addAllDetail(vo.getDetails().stream().map(PbUtil::toPb).collect(Collectors.toList()));
        }
        return builderVo.build();
    }

    public static AdProductHourReportApiServiceOuterClass.AdProductHourOfKeywordRpcVo toProductPb(AdProductHourOfKeywordVo vo) {
        AdProductHourReportApiServiceOuterClass.AdProductHourOfKeywordRpcVo.Builder builderVo = AdProductHourReportApiServiceOuterClass.AdProductHourOfKeywordRpcVo.newBuilder();

        if(StringUtils.isNotBlank(vo.getKeywordId())){
            builderVo.setKeywordId(vo.getKeywordId());
        }
        if (StringUtils.isNotBlank(vo.getKeywordText())) {
            builderVo.setKeywordText(vo.getKeywordText());
        }
        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAcos() != null){
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if(vo.getAdSale()!= null){
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        if (CollectionUtils.isNotEmpty(vo.getDetails())) {
            builderVo.addAllDetail(vo.getDetails().stream().map(PbUtil::toPb).collect(Collectors.toList()));
        }
        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() !=null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() !=null ? vo.getOrderNumPercentage().toString() : "0.00");
        return builderVo.build();
    }

    public static AdCampaignWeekReportRpcVoPb.AdCampaignWeekReportRpcVo toCampaignPb(AdCampaignWeekDayVo vo, BigDecimal shopSalesByDate) {
        AdCampaignWeekReportRpcVoPb.AdCampaignWeekReportRpcVo.Builder builderVo = AdCampaignWeekReportRpcVoPb.AdCampaignWeekReportRpcVo.newBuilder();

        if (vo.getWeekDay() != null) {
            builderVo.setWeekDay(vo.getWeekDay());
        }

        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
            builderVo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate).setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostCompare() != null) {
            builderVo.setAdCostCompare(vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostCompareRate() != null) {
            builderVo.setAdCostCompareRate(vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getRoasCompare() != null) {
            builderVo.setRoasCompare(vo.getRoasCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getRoasCompareRate() != null) {
            builderVo.setRoasCompareRate(vo.getRoasCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostPerClickCompare() != null) {
            builderVo.setAdCostPerClickCompare(vo.getAdCostPerClickCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostPerClickCompareRate() != null) {
            builderVo.setAdCostPerClickCompareRate(vo.getAdCostPerClickCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAcos() != null){
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAcosCompare() != null) {
            builderVo.setAcosCompare(vo.getAcosCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAcosCompareRate() != null) {
            builderVo.setAcosCompareRate(vo.getAcosCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if (vo.getImpressionsCompare() != null) {
            builderVo.setImpressionsCompare(vo.getImpressionsCompare());
        }
        if (vo.getImpressionsCompareRate() != null) {
            builderVo.setImpressionsCompareRate(vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if (vo.getClicksCompare() != null) {
            builderVo.setClicksCompare(vo.getClicksCompare());
        }
        if (vo.getClicksCompareRate() != null) {
            builderVo.setClicksCompareRate(vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCtrCompare() != null) {
            builderVo.setCtrCompare(vo.getCtrCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCtrCompareRate() != null) {
            builderVo.setCtrCompareRate(vo.getCtrCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCvrCompare() != null) {
            builderVo.setCvrCompare(vo.getCvrCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCvrCompareRate() != null) {
            builderVo.setCvrCompareRate(vo.getCvrCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if (vo.getAdOrderNumCompare() != null) {
            builderVo.setAdOrderNumCompare(vo.getAdOrderNumCompare());
        }
        if (vo.getAdOrderNumCompareRate() != null) {
            builderVo.setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if(vo.getAdSale()!= null){
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
            builderVo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate).setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleCompare() != null) {
            builderVo.setAdSaleCompare(vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleCompareRate() != null) {
            builderVo.setAdSaleCompareRate(vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if (vo.getAdSaleNumCompare() != null) {
            builderVo.setAdSaleNumCompare(vo.getAdSaleNumCompare());
        }
        if (vo.getAdSaleNumCompareRate() != null) {
            builderVo.setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        if (CollectionUtils.isNotEmpty(vo.getDetails())) {
            builderVo.addAllDetail(vo.getDetails().stream().map( key -> PbUtil.toPb(key, shopSalesByDate)).collect(Collectors.toList()));
        }
        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() !=null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() !=null ? vo.getOrderNumPercentage().toString() : "0.00");
        builderVo.setViewableImpressions(getIntegerZeroValue(vo.getViewableImpressions()));
        builderVo.setVrt(getZeroValue(vo.getVrt()));
        builderVo.setVcpm(getZeroValue(vo.getVcpm()));
        builderVo.setVCtr(getZeroValue(vo.getVCtr()));
        builderVo.setOrdersNewToBrand(getIntegerZeroValue(vo.getOrdersNewToBrand()));
        builderVo.setUnitsOrderedNewToBrand(getIntegerZeroValue(vo.getUnitsOrderedNewToBrand()));
        builderVo.setSalesNewToBrand(getZeroValue(vo.getSalesNewToBrand()));
        builderVo.setAdvertisingUnitPrice(getZeroValue(vo.getAdvertisingUnitPrice()));
        builderVo.setAdvertisingProductUnitPrice(getZeroValue(vo.getAdvertisingProductUnitPrice()));
        builderVo.setAdvertisingOtherProductUnitPrice(getZeroValue(vo.getAdvertisingOtherProductUnitPrice()));
        builderVo.setOrdersNewToBrandPercentage(getZeroValue(vo.getOrdersNewToBrandPercentage()));
        builderVo.setUnitsOrderedNewToBrandPercentage(getZeroValue(vo.getUnitsOrderedNewToBrandPercentage()));
        builderVo.setSalesNewToBrandPercentage(getZeroValue(vo.getSalesNewToBrandPercentage()));
        return builderVo.build();
    }

    public static AdWeekReportResponsePb.AdWeekReportRpcVo toCommonPb(AdCommonWeekDayVo vo, BigDecimal shopSalesByDate) {
        AdWeekReportResponsePb.AdWeekReportRpcVo.Builder builderVo = AdWeekReportResponsePb.AdWeekReportRpcVo.newBuilder();

        setIfNotNull(builderVo::setWeekDay, vo.getWeekDay());
        setBigDecimalIfNotNull(builderVo::setAdCost, vo.getAdCost(), 2);
        setBigDecimalIfNotNull(builderVo::setAdCostCompare, vo.getAdCostCompare(), 2);
        setBigDecimalIfNotNull(builderVo::setAdCostCompareRate, vo.getAdCostCompareRate(), 2);
        setBigDecimalIfNotNull(builderVo::setAcots, MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate), 2);
        setBigDecimalIfNotNull(builderVo::setRoas, vo.getRoas(), 2);
        setBigDecimalIfNotNull(builderVo::setRoasCompare, vo.getRoasCompare(), 2);
        setBigDecimalIfNotNull(builderVo::setRoasCompareRate, vo.getRoasCompareRate(), 2);
        setBigDecimalIfNotNull(builderVo::setCpa, vo.getCpa(), 2);
        setBigDecimalIfNotNull(builderVo::setAdCostPerClick, vo.getAdCostPerClick(), 2);
        setBigDecimalIfNotNull(builderVo::setAdCostPerClickCompare, vo.getAdCostPerClickCompare(), 2);
        setBigDecimalIfNotNull(builderVo::setAdCostPerClickCompareRate, vo.getAdCostPerClickCompareRate(), 2);
        setBigDecimalIfNotNull(builderVo::setAcos, vo.getAcos(), 2);
        setBigDecimalIfNotNull(builderVo::setAcosCompare, vo.getAcosCompare(), 2);
        setBigDecimalIfNotNull(builderVo::setAcosCompareRate, vo.getAcosCompareRate(), 2);
        setIfNotNull(builderVo::setImpressions, vo.getImpressions());
        setIfNotNull(builderVo::setImpressionsCompare, vo.getImpressionsCompare());
        setBigDecimalIfNotNull(builderVo::setImpressionsCompareRate, vo.getImpressionsCompareRate(), 2);
        setIfNotNull(builderVo::setClicks, vo.getClicks());
        setIfNotNull(builderVo::setClicksCompare, vo.getClicksCompare());
        setBigDecimalIfNotNull(builderVo::setClicksCompareRate, vo.getClicksCompareRate(), 2);
        setBigDecimalIfNotNull(builderVo::setCtr, vo.getCtr(), 2);
        setBigDecimalIfNotNull(builderVo::setCtrCompare, vo.getCtrCompare(), 2);
        setBigDecimalIfNotNull(builderVo::setCtrCompareRate, vo.getCtrCompareRate(), 2);
        setBigDecimalIfNotNull(builderVo::setCvr, vo.getCvr(), 2);
        setBigDecimalIfNotNull(builderVo::setCvrCompare, vo.getCvrCompare(), 2);
        setBigDecimalIfNotNull(builderVo::setCvrCompareRate, vo.getCvrCompareRate(), 2);
        setIfNotNull(builderVo::setAdOrderNum, vo.getAdOrderNum());
        setIfNotNull(builderVo::setAdOrderNumCompare, vo.getAdOrderNumCompare());
        setBigDecimalIfNotNull(builderVo::setAdOrderNumCompareRate, vo.getAdOrderNumCompareRate(), 2);
        setIfNotNull(builderVo::setSelfAdOrderNum, vo.getSelfAdOrderNum());
        setIfNotNull(builderVo::setOtherAdOrderNum, vo.getOtherAdOrderNum());
        setBigDecimalIfNotNull(builderVo::setAdSale, vo.getAdSale(), 2);
        setBigDecimalIfNotNull(builderVo::setAdSaleCompare, vo.getAdSaleCompare(), 2);
        setBigDecimalIfNotNull(builderVo::setAdSaleCompareRate, vo.getAdSaleCompareRate(), 2);
        setBigDecimalIfNotNull(builderVo::setAsots, MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate), 2);
        setBigDecimalIfNotNull(builderVo::setAdSelfSale, vo.getAdSelfSale(), 2);
        setBigDecimalIfNotNull(builderVo::setAdOtherSale, vo.getAdOtherSale(), 2);
        setIfNotNull(builderVo::setAdSaleNum, vo.getAdSaleNum());
        setIfNotNull(builderVo::setAdSaleNumCompare, vo.getAdSaleNumCompare());
        setBigDecimalIfNotNull(builderVo::setAdSaleNumCompareRate, vo.getAdSaleNumCompareRate(), 2);
        setIfNotNull(builderVo::setAdSelfSaleNum, vo.getAdSelfSaleNum());
        setIfNotNull(builderVo::setAdOtherSaleNum, vo.getAdOtherSaleNum());

        if (CollectionUtils.isNotEmpty(vo.getDetails())) {
            builderVo.addAllDetail(vo.getDetails().stream()
                .map(key -> PbUtil.toPb(key, shopSalesByDate))
                .collect(Collectors.toList()));
        }

        builderVo.setAdCostPercentage(getPercentageString(vo.getAdCostPercentage()));
        builderVo.setAdSalePercentage(getPercentageString(vo.getAdSalePercentage()));
        builderVo.setAdOrderNumPercentage(getPercentageString(vo.getAdOrderNumPercentage()));
        builderVo.setOrderNumPercentage(getPercentageString(vo.getOrderNumPercentage()));
        builderVo.setViewableImpressions(getIntegerZeroValue(vo.getViewableImpressions()));
        builderVo.setVrt(getZeroValue(vo.getVrt()));
        builderVo.setVcpm(getZeroValue(vo.getVcpm()));
        builderVo.setVCtr(getZeroValue(vo.getVCtr()));
        builderVo.setOrdersNewToBrand(getIntegerZeroValue(vo.getOrdersNewToBrand()));
        builderVo.setUnitsOrderedNewToBrand(getIntegerZeroValue(vo.getUnitsOrderedNewToBrand()));
        builderVo.setSalesNewToBrand(getZeroValue(vo.getSalesNewToBrand()));
        builderVo.setAdvertisingUnitPrice(getZeroValue(vo.getAdvertisingUnitPrice()));
        builderVo.setAdvertisingProductUnitPrice(getZeroValue(vo.getAdvertisingProductUnitPrice()));
        builderVo.setAdvertisingOtherProductUnitPrice(getZeroValue(vo.getAdvertisingOtherProductUnitPrice()));
        builderVo.setOrdersNewToBrandPercentage(getZeroValue(vo.getOrdersNewToBrandPercentage()));
        builderVo.setUnitsOrderedNewToBrandPercentage(getZeroValue(vo.getUnitsOrderedNewToBrandPercentage()));
        builderVo.setSalesNewToBrandPercentage(getZeroValue(vo.getSalesNewToBrandPercentage()));

        return builderVo.build();
    }

    private static <T> void setIfNotNull(Consumer<T> setter, T value) {
        if (value != null) {
            setter.accept(value);
        }
    }

    private static void setBigDecimalIfNotNull(Consumer<String> setter, BigDecimal value, int scale) {
        if (value != null) {
            setter.accept(value.setScale(scale, RoundingMode.HALF_UP).toString());
        }
    }

    private static String getPercentageString(BigDecimal value) {
        return value != null ? value.toString() : "0.00";
    }

    public static AdWeekReportResponsePb.AdWeekReportRpcVo toAdWeekPb(AdReportWeeklyDayVO vo, BigDecimal shopSalesByDate) {
        AdWeekReportResponsePb.AdWeekReportRpcVo.Builder builderVo = AdWeekReportResponsePb.AdWeekReportRpcVo.newBuilder();

        if (vo.getWeekDay() != null) {
            builderVo.setWeekDay(vo.getWeekDay());
        }

        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
            builderVo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate).setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostCompare() != null) {
            builderVo.setAdCostCompare(vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostCompareRate() != null) {
            builderVo.setAdCostCompareRate(vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getRoasCompare()).ifPresent(i -> builderVo.setRoasCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getRoasCompareRate()).ifPresent(i -> builderVo.setRoasCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getAdCostPerClickCompare()).ifPresent(i -> builderVo.setAdCostPerClickCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getAdCostPerClickCompareRate()).ifPresent(i -> builderVo.setAdCostPerClickCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getAcos() != null){
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getAcosCompare()).ifPresent(i -> builderVo.setAcosCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getAcosCompareRate()).ifPresent(i -> builderVo.setAcosCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if (vo.getImpressionsCompare() != null) {
            builderVo.setImpressionsCompare(vo.getImpressionsCompare());
        }
        if (vo.getImpressionsCompareRate() != null) {
            builderVo.setImpressionsCompareRate(vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }

        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if (vo.getClicksCompare() != null) {
            builderVo.setClicksCompare(vo.getClicksCompare());
        }
        if (vo.getClicksCompareRate() != null) {
            builderVo.setClicksCompareRate(vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }

        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getCtrCompare()).ifPresent(i -> builderVo.setCtrCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getCtrCompareRate()).ifPresent(i -> builderVo.setCtrCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getCvrCompare()).ifPresent(i -> builderVo.setCvrCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getCvrCompareRate()).ifPresent(i -> builderVo.setCvrCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if (vo.getAdOrderNumCompare() != null) {
            builderVo.setAdOrderNumCompare(vo.getAdOrderNumCompare());
        }
        if (vo.getAdOrderNumCompareRate() != null) {
            builderVo.setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }

        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if(vo.getAdSale()!= null){
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
            builderVo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate).setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleCompare() != null) {
            builderVo.setAdSaleCompare(vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleCompareRate() != null) {
            builderVo.setAdSaleCompareRate(vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }

        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if (vo.getAdSaleNumCompare() != null) {
            builderVo.setAdSaleNumCompare(vo.getAdSaleNumCompare());
        }
        if (vo.getAdSaleNumCompareRate() != null) {
            builderVo.setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }

        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        if (CollectionUtils.isNotEmpty(vo.getDetails())) {
            builderVo.addAllDetail(vo.getDetails().stream().map(key -> PbUtil.toPb(key, shopSalesByDate)).collect(Collectors.toList()));
        }
        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() !=null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() !=null ? vo.getOrderNumPercentage().toString() : "0.00");

        builderVo.setViewableImpressions(getIntegerZeroValue(vo.getViewableImpressions()));
        builderVo.setVrt(getZeroValue(vo.getVrt()));
        builderVo.setVcpm(getZeroValue(vo.getVcpm()));
        builderVo.setVCtr(getZeroValue(vo.getVCtr()));
        builderVo.setOrdersNewToBrand(getIntegerZeroValue(vo.getOrdersNewToBrand()));
        builderVo.setUnitsOrderedNewToBrand(getIntegerZeroValue(vo.getUnitsOrderedNewToBrand()));
        builderVo.setSalesNewToBrand(getZeroValue(vo.getSalesNewToBrand()));
        builderVo.setAdvertisingUnitPrice(getZeroValue(vo.getAdvertisingUnitPrice()));
        builderVo.setAdvertisingProductUnitPrice(getZeroValue(vo.getAdvertisingProductUnitPrice()));
        builderVo.setAdvertisingOtherProductUnitPrice(getZeroValue(vo.getAdvertisingOtherProductUnitPrice()));
        builderVo.setOrdersNewToBrandPercentage(getZeroValue(vo.getOrdersNewToBrandPercentage()));
        builderVo.setUnitsOrderedNewToBrandPercentage(getZeroValue(vo.getUnitsOrderedNewToBrandPercentage()));
        builderVo.setSalesNewToBrandPercentage(getZeroValue(vo.getSalesNewToBrandPercentage()));

        return builderVo.build();
    }

    public static AdPlacementWeekReportRpcVoPb.AdPlacementWeekReportRpcVo toPlacementPb(AdPlacementWeekDayVo vo) {
        AdPlacementWeekReportRpcVoPb.AdPlacementWeekReportRpcVo.Builder builderVo = AdPlacementWeekReportRpcVoPb.AdPlacementWeekReportRpcVo.newBuilder();
        if (vo.getWeekDay() != null) {
            builderVo.setWeekDay(vo.getWeekDay());
        }
        if (vo.getAdCost() != null) {
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getRoas() != null) {
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCpa() != null) {
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostPerClick() != null) {
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAcos() != null) {
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getImpressions() != null) {
            builderVo.setImpressions(vo.getImpressions());
        }
        if (vo.getClicks() != null) {
            builderVo.setClicks(vo.getClicks());
        }
        if (vo.getCtr() != null) {
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCvr() != null) {
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdOrderNum() != null) {
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if (vo.getSelfAdOrderNum() != null) {
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if (vo.getOtherAdOrderNum() != null) {
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if (vo.getAdSale() != null) {
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null) {
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdOtherSale() != null) {
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleNum() != null) {
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if (vo.getAdSelfSaleNum() != null) {
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if (vo.getAdOtherSaleNum() != null) {
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() != null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() != null ? vo.getOrderNumPercentage().toString() : "0.00");
        if (CollectionUtils.isNotEmpty(vo.getDetails())) {
            builderVo.addAllDetail(vo.getDetails().stream().map(PbUtil::toPb).collect(Collectors.toList()));
        }
        return builderVo.build();
    }

    public static AdGroupWeekReportRpcVoPb.AdGroupWeekReportRpcVo toGroupPb(AdGroupWeekDayVo vo) {
        AdGroupWeekReportRpcVoPb.AdGroupWeekReportRpcVo.Builder builderVo = AdGroupWeekReportRpcVoPb.AdGroupWeekReportRpcVo.newBuilder();
        if (vo.getWeekDay() != null) {
            builderVo.setWeekDay(vo.getWeekDay());
        }
        if (vo.getAdCost() != null) {
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getRoas() != null) {
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCpa() != null) {
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostPerClick() != null) {
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAcos() != null) {
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getImpressions() != null) {
            builderVo.setImpressions(vo.getImpressions());
        }
        if (vo.getClicks() != null) {
            builderVo.setClicks(vo.getClicks());
        }
        if (vo.getCtr() != null) {
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCvr() != null) {
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdOrderNum() != null) {
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if (vo.getSelfAdOrderNum() != null) {
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if (vo.getOtherAdOrderNum() != null) {
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if (vo.getAdSale() != null) {
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null) {
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdOtherSale() != null) {
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleNum() != null) {
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if (vo.getAdSelfSaleNum() != null) {
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if (vo.getAdOtherSaleNum() != null) {
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() != null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() != null ? vo.getOrderNumPercentage().toString() : "0.00");
        if (CollectionUtils.isNotEmpty(vo.getDetails())) {
            builderVo.addAllDetail(vo.getDetails().stream().map(PbUtil::toPb).collect(Collectors.toList()));
        }
        return builderVo.build();
    }

    public static AdTargetHourOfPlacementRpcVoPb.AdTargetHourOfPlacementRpcVo toTargetPb(AdKeywordTargetHourOfPlacementVo vo) {
        AdTargetHourOfPlacementRpcVoPb.AdTargetHourOfPlacementRpcVo.Builder builderVo =
                AdTargetHourOfPlacementRpcVoPb.AdTargetHourOfPlacementRpcVo.newBuilder();

        if(vo.getPlacement() != null){
            builderVo.setPlacement(vo.getPlacement());
        }
        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAcos() != null){
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if(vo.getAdSale()!= null){
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        if (CollectionUtils.isNotEmpty(vo.getDetails())) {
            builderVo.addAllDetail(vo.getDetails().stream().map(PbUtil::toPb).collect(Collectors.toList()));
        }
        return builderVo.build();
    }

    public static AdKeywordHourOfPlacementRpcVoPb.AdKeywordHourOfPlacementRpcVo toKeywordPb(AdKeywordTargetHourOfPlacementVo vo) {
        AdKeywordHourOfPlacementRpcVoPb.AdKeywordHourOfPlacementRpcVo.Builder builderVo =
                AdKeywordHourOfPlacementRpcVoPb.AdKeywordHourOfPlacementRpcVo.newBuilder();

        if(vo.getPlacement() != null){
            builderVo.setPlacement(vo.getPlacement());
        }
        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAcos() != null){
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if(vo.getAdSale()!= null){
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        if (CollectionUtils.isNotEmpty(vo.getDetails())) {
            builderVo.addAllDetail(vo.getDetails().stream().map(PbUtil::toPb).collect(Collectors.toList()));
        }
        return builderVo.build();
    }

    public static AdProductHourOfPlacementRpcVoPb.AdProductHourOfPlacementRpcVo toProductPb(AdProductHourOfPlacementVo vo) {
        AdProductHourOfPlacementRpcVoPb.AdProductHourOfPlacementRpcVo.Builder builderVo =
                AdProductHourOfPlacementRpcVoPb.AdProductHourOfPlacementRpcVo.newBuilder();

        if(vo.getPlacement() != null){
            builderVo.setPlacement(vo.getPlacement());
        }
        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAcos() != null){
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if(vo.getAdSale()!= null){
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        if (CollectionUtils.isNotEmpty(vo.getDetails())) {
            builderVo.addAllDetail(vo.getDetails().stream().map(PbUtil::toPb).collect(Collectors.toList()));
        }
        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() !=null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() !=null ? vo.getOrderNumPercentage().toString() : "0.00");
        return builderVo.build();
    }

    public static AdProductWeekReportRpcVoPb.AdProductWeekReportRpcVo toProductPb(AdProductWeekDayVo vo) {
        AdProductWeekReportRpcVoPb.AdProductWeekReportRpcVo.Builder builderVo =
                AdProductWeekReportRpcVoPb.AdProductWeekReportRpcVo.newBuilder();

        if(vo.getWeekDay() != null){
            builderVo.setWeekDay(vo.getWeekDay());
        }
        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostCompare() != null) {
            builderVo.setAdCostCompare(vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostCompareRate() != null) {
            builderVo.setAdCostCompareRate(vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getRoasCompare()).ifPresent(i -> builderVo.setRoasCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getRoasCompareRate()).ifPresent(i -> builderVo.setRoasCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getAdCostPerClickCompare()).ifPresent(i -> builderVo.setAdCostPerClickCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getAdCostPerClickCompareRate()).ifPresent(i -> builderVo.setAdCostPerClickCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getAcos() != null){
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getAcosCompare()).ifPresent(i -> builderVo.setAcosCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getAcosCompareRate()).ifPresent(i -> builderVo.setAcosCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if (vo.getImpressionsCompare() != null) {
            builderVo.setImpressionsCompare(vo.getImpressionsCompare());
        }
        if (vo.getImpressionsCompareRate() != null) {
            builderVo.setImpressionsCompareRate(vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if (vo.getClicksCompare() != null) {
            builderVo.setClicksCompare(vo.getClicksCompare());
        }
        if (vo.getClicksCompareRate() != null) {
            builderVo.setClicksCompareRate(vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getCtrCompare()).ifPresent(i -> builderVo.setCtrCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getCtrCompareRate()).ifPresent(i -> builderVo.setCtrCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getCvrCompare()).ifPresent(i -> builderVo.setCvrCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getCvrCompareRate()).ifPresent(i -> builderVo.setCvrCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if (vo.getAdOrderNumCompare() != null) {
            builderVo.setAdOrderNumCompare(vo.getAdOrderNumCompare());
        }
        if (vo.getAdOrderNumCompareRate() != null) {
            builderVo.setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if(vo.getAdSale()!= null){
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleCompare() != null) {
            builderVo.setAdSaleCompare(vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleCompareRate() != null) {
            builderVo.setAdSaleCompareRate(vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if (vo.getAdSaleNumCompare() != null) {
            builderVo.setAdSaleNumCompare(vo.getAdSaleNumCompare());
        }
        if (vo.getAdSaleNumCompareRate() != null) {
            builderVo.setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        if (CollectionUtils.isNotEmpty(vo.getDetails())) {
            builderVo.addAllDetail(vo.getDetails().stream().map(PbUtil::toPb).collect(Collectors.toList()));
        }
        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() !=null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() !=null ? vo.getOrderNumPercentage().toString() : "0.00");
        return builderVo.build();
    }


    public static AdReportDataRpcVoPb.AdReportDataRpcVo toPb(AdKeywordAndTargetHourVo vo) {
        AdReportDataRpcVoPb.AdReportDataRpcVo.Builder builderVo = AdReportDataRpcVoPb.AdReportDataRpcVo.newBuilder();

        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompare() != null){
            builderVo.setAdCostCompare(vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompareRate() != null){
            builderVo.setAdCostCompareRate(vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAcos() != null){
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getLabel() != null){
            builderVo.setTitle(vo.getLabel());
        }
        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if(vo.getImpressionsCompare() != null){
            builderVo.setImpressionsCompare(vo.getImpressionsCompare());
        }
        if(vo.getImpressionsCompareRate() != null){
            builderVo.setImpressionsCompareRate(vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if(vo.getClicksCompare() != null ){
            builderVo.setClicksCompare(vo.getClicksCompare());
        }
        if(vo.getClicksCompareRate() != null ){
            builderVo.setClicksCompareRate(vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if(vo.getAdOrderNumCompare()!= null){
            builderVo.setAdOrderNumCompare(vo.getAdOrderNumCompare());
        }
        if(vo.getAdOrderNumCompareRate()!= null){
            builderVo.setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if(vo.getAdSale()!= null){
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompare() != null){
            builderVo.setAdSaleCompare(vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompareRate() != null){
            builderVo.setAdSaleCompareRate(vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if(vo.getAdSaleNumCompare() != null){
            builderVo.setAdSaleNumCompare(vo.getAdSaleNumCompare());
        }
        if(vo.getAdSaleNumCompareRate() != null){
            builderVo.setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        return builderVo.build();
    }

    public static AdReportDataRpcVoPb.AdReportDataRpcVo toPb(AdProductHourVo vo) {
        AdReportDataRpcVoPb.AdReportDataRpcVo.Builder builderVo = AdReportDataRpcVoPb.AdReportDataRpcVo.newBuilder();

        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompare() != null){
            builderVo.setAdCostCompare(vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompareRate() != null){
            builderVo.setAdCostCompareRate(vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getRoasCompare()).ifPresent(i -> builderVo.setRoasCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getRoasCompareRate()).ifPresent(i -> builderVo.setRoasCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getAdCostPerClickCompare()).ifPresent(i -> builderVo.setAdCostPerClickCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getAdCostPerClickCompareRate()).ifPresent(i -> builderVo.setAdCostPerClickCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getAcos() != null){
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getAcosCompare()).ifPresent(i -> builderVo.setAcosCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getAcosCompareRate()).ifPresent(i -> builderVo.setAcosCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getLabel() != null){
            builderVo.setTitle(vo.getLabel());
        }
        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if(vo.getImpressionsCompare() != null){
            builderVo.setImpressionsCompare(vo.getImpressionsCompare());
        }
        if(vo.getImpressionsCompareRate() != null){
            builderVo.setImpressionsCompareRate(vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if(vo.getClicksCompare() != null ){
            builderVo.setClicksCompare(vo.getClicksCompare());
        }
        if(vo.getClicksCompareRate() != null ){
            builderVo.setClicksCompareRate(vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getCtrCompare()).ifPresent(i -> builderVo.setCtrCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getCtrCompareRate()).ifPresent(i -> builderVo.setCtrCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getCvrCompare()).ifPresent(i -> builderVo.setCvrCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getCvrCompareRate()).ifPresent(i -> builderVo.setCvrCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if(vo.getAdOrderNumCompare()!= null){
            builderVo.setAdOrderNumCompare(vo.getAdOrderNumCompare());
        }
        if(vo.getAdOrderNumCompareRate()!= null){
            builderVo.setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if(vo.getAdSale()!= null){
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompare() != null){
            builderVo.setAdSaleCompare(vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompareRate() != null){
            builderVo.setAdSaleCompareRate(vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if(vo.getAdSaleNumCompare() != null){
            builderVo.setAdSaleNumCompare(vo.getAdSaleNumCompare());
        }
        if(vo.getAdSaleNumCompareRate() != null){
            builderVo.setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() !=null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() !=null ? vo.getOrderNumPercentage().toString() : "0.00");
        return builderVo.build();
    }

    public static AdReportDataRpcVoPb.AdReportDataRpcVo toPb(AdCampaignHourVo vo, BigDecimal shopSalesByDate) {
        AdReportDataRpcVoPb.AdReportDataRpcVo.Builder builderVo = AdReportDataRpcVoPb.AdReportDataRpcVo.newBuilder();

        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
            builderVo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate).setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompare() != null){
            builderVo.setAdCostCompare(vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompareRate() != null){
            builderVo.setAdCostCompareRate(vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getRoasCompare() != null) {
            builderVo.setRoasCompare(vo.getRoasCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getRoasCompareRate() != null) {
            builderVo.setRoasCompareRate(vo.getRoasCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostPerClickCompare() != null) {
            builderVo.setAdCostPerClickCompare(vo.getAdCostPerClickCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostPerClickCompareRate() != null) {
            builderVo.setAdCostPerClickCompareRate(vo.getAdCostPerClickCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAcos() != null){
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAcosCompare() != null) {
            builderVo.setAcosCompare(vo.getAcosCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAcosCompareRate() != null) {
            builderVo.setAcosCompareRate(vo.getAcosCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getLabel() != null){
            builderVo.setTitle(vo.getLabel());
        }
        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if(vo.getImpressionsCompare() != null){
            builderVo.setImpressionsCompare(vo.getImpressionsCompare());
        }
        if(vo.getImpressionsCompareRate() != null){
            builderVo.setImpressionsCompareRate(vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if(vo.getClicksCompare() != null ){
            builderVo.setClicksCompare(vo.getClicksCompare());
        }
        if(vo.getClicksCompareRate() != null ){
            builderVo.setClicksCompareRate(vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCtrCompare() != null) {
            builderVo.setCtrCompare(vo.getCtrCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCtrCompareRate() != null) {
            builderVo.setCtrCompareRate(vo.getCtrCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCvrCompare() != null) {
            builderVo.setCvrCompare(vo.getCvrCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCvrCompareRate() != null) {
            builderVo.setCvrCompareRate(vo.getCvrCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if(vo.getAdOrderNumCompare()!= null){
            builderVo.setAdOrderNumCompare(vo.getAdOrderNumCompare());
        }
        if(vo.getAdOrderNumCompareRate()!= null){
            builderVo.setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if(vo.getAdSale()!= null){
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
            builderVo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate).setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompare() != null){
            builderVo.setAdSaleCompare(vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompareRate() != null){
            builderVo.setAdSaleCompareRate(vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if(vo.getAdSaleNumCompare() != null){
            builderVo.setAdSaleNumCompare(vo.getAdSaleNumCompare());
        }
        if(vo.getAdSaleNumCompareRate() != null){
            builderVo.setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() !=null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() !=null ? vo.getOrderNumPercentage().toString() : "0.00");

        builderVo.setViewableImpressions(getIntegerZeroValue(vo.getViewableImpressions()));
        builderVo.setVrt(getZeroValue(vo.getVrt()));
        builderVo.setVcpm(getZeroValue(vo.getVcpm()));
        builderVo.setVCtr(getZeroValue(vo.getVCtr()));
        builderVo.setOrdersNewToBrand(getIntegerZeroValue(vo.getOrdersNewToBrand()));
        builderVo.setUnitsOrderedNewToBrand(getIntegerZeroValue(vo.getUnitsOrderedNewToBrand()));
        builderVo.setSalesNewToBrand(getZeroValue(vo.getSalesNewToBrand()));
        builderVo.setAdvertisingUnitPrice(getZeroValue(vo.getAdvertisingUnitPrice()));
        builderVo.setAdvertisingProductUnitPrice(getZeroValue(vo.getAdvertisingProductUnitPrice()));
        builderVo.setAdvertisingOtherProductUnitPrice(getZeroValue(vo.getAdvertisingOtherProductUnitPrice()));
        builderVo.setOrdersNewToBrandPercentage(getZeroValue(vo.getOrdersNewToBrandPercentage()));
        builderVo.setUnitsOrderedNewToBrandPercentage(getZeroValue(vo.getUnitsOrderedNewToBrandPercentage()));
        builderVo.setSalesNewToBrandPercentage(getZeroValue(vo.getSalesNewToBrandPercentage()));
        return builderVo.build();
    }

    public static AdReportDataRpcVoPb.AdReportDataRpcVo toPb(AdCommonHourVo vo, BigDecimal shopSalesByDate) {
        AdReportDataRpcVoPb.AdReportDataRpcVo.Builder builderVo = AdReportDataRpcVoPb.AdReportDataRpcVo.newBuilder();

        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
            builderVo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate).setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompare() != null){
            builderVo.setAdCostCompare(vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompareRate() != null){
            builderVo.setAdCostCompareRate(vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getRoasCompare()).ifPresent(i -> builderVo.setRoasCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getRoasCompareRate()).ifPresent(i -> builderVo.setRoasCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));


        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getAdCostPerClickCompare()).ifPresent(i -> builderVo.setAdCostPerClickCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getAdCostPerClickCompareRate()).ifPresent(i -> builderVo.setAdCostPerClickCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getAcos() != null){
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getAcosCompare()).ifPresent(i -> builderVo.setAcosCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getAcosCompareRate()).ifPresent(i -> builderVo.setAcosCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getLabel() != null){
            builderVo.setTitle(vo.getLabel());
        }
        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if(vo.getImpressionsCompare() != null){
            builderVo.setImpressionsCompare(vo.getImpressionsCompare());
        }
        if(vo.getImpressionsCompareRate() != null){
            builderVo.setImpressionsCompareRate(vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if(vo.getClicksCompare() != null ){
            builderVo.setClicksCompare(vo.getClicksCompare());
        }
        if(vo.getClicksCompareRate() != null ){
            builderVo.setClicksCompareRate(vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getCtrCompare()).ifPresent(i -> builderVo.setCtrCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getCtrCompareRate()).ifPresent(i -> builderVo.setCtrCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getCvrCompare()).ifPresent(i -> builderVo.setCvrCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getCvrCompareRate()).ifPresent(i -> builderVo.setCvrCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if(vo.getAdOrderNumCompare()!= null){
            builderVo.setAdOrderNumCompare(vo.getAdOrderNumCompare());
        }
        if(vo.getAdOrderNumCompareRate()!= null){
            builderVo.setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if(vo.getAdSale()!= null){
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
            builderVo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate).setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompare() != null){
            builderVo.setAdSaleCompare(vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompareRate() != null){
            builderVo.setAdSaleCompareRate(vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if(vo.getAdSaleNumCompare() != null){
            builderVo.setAdSaleNumCompare(vo.getAdSaleNumCompare());
        }
        if(vo.getAdSaleNumCompareRate() != null){
            builderVo.setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() !=null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() !=null ? vo.getOrderNumPercentage().toString() : "0.00");

        builderVo.setViewableImpressions(getIntegerZeroValue(vo.getViewableImpressions()));
        builderVo.setVrt(getZeroValue(vo.getVrt()));
        builderVo.setVcpm(getZeroValue(vo.getVcpm()));
        builderVo.setVCtr(getZeroValue(vo.getVCtr()));
        builderVo.setOrdersNewToBrand(getIntegerZeroValue(vo.getOrdersNewToBrand()));
        builderVo.setUnitsOrderedNewToBrand(getIntegerZeroValue(vo.getUnitsOrderedNewToBrand()));
        builderVo.setSalesNewToBrand(getZeroValue(vo.getSalesNewToBrand()));
        builderVo.setAdvertisingUnitPrice(getZeroValue(vo.getAdvertisingUnitPrice()));
        builderVo.setAdvertisingProductUnitPrice(getZeroValue(vo.getAdvertisingProductUnitPrice()));
        builderVo.setAdvertisingOtherProductUnitPrice(getZeroValue(vo.getAdvertisingOtherProductUnitPrice()));
        builderVo.setOrdersNewToBrandPercentage(getZeroValue(vo.getOrdersNewToBrandPercentage()));
        builderVo.setUnitsOrderedNewToBrandPercentage(getZeroValue(vo.getUnitsOrderedNewToBrandPercentage()));
        builderVo.setSalesNewToBrandPercentage(getZeroValue(vo.getSalesNewToBrandPercentage()));
        return builderVo.build();
    }

    public static AdReportDataRpcVoPb.AdReportDataRpcVo toPb(AdPlacementHourVo vo) {
        AdReportDataRpcVoPb.AdReportDataRpcVo.Builder builderVo = AdReportDataRpcVoPb.AdReportDataRpcVo.newBuilder();

        if (vo.getAdCost() != null) {
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostCompare() != null) {
            builderVo.setAdCostCompare(vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostCompareRate() != null) {
            builderVo.setAdCostCompareRate(vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getRoas() != null) {
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCpa() != null) {
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostPerClick() != null) {
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAcos() != null) {
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getLabel() != null) {
            builderVo.setTitle(vo.getLabel());
        }
        if (vo.getImpressions() != null) {
            builderVo.setImpressions(vo.getImpressions());
        }
        if (vo.getImpressionsCompare() != null) {
            builderVo.setImpressionsCompare(vo.getImpressionsCompare());
        }
        if (vo.getImpressionsCompareRate() != null) {
            builderVo.setImpressionsCompareRate(vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getClicks() != null) {
            builderVo.setClicks(vo.getClicks());
        }
        if (vo.getClicksCompare() != null) {
            builderVo.setClicksCompare(vo.getClicksCompare());
        }
        if (vo.getClicksCompareRate() != null) {
            builderVo.setClicksCompareRate(vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCtr() != null) {
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCvr() != null) {
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdOrderNum() != null) {
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if (vo.getAdOrderNumCompare() != null) {
            builderVo.setAdOrderNumCompare(vo.getAdOrderNumCompare());
        }
        if (vo.getAdOrderNumCompareRate() != null) {
            builderVo.setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getSelfAdOrderNum() != null) {
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if (vo.getOtherAdOrderNum() != null) {
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if (vo.getAdSale() != null) {
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleCompare() != null) {
            builderVo.setAdSaleCompare(vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleCompareRate() != null) {
            builderVo.setAdSaleCompareRate(vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null) {
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdOtherSale() != null) {
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleNum() != null) {
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if (vo.getAdSaleNumCompare() != null) {
            builderVo.setAdSaleNumCompare(vo.getAdSaleNumCompare());
        }
        if (vo.getAdSaleNumCompareRate() != null) {
            builderVo.setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSaleNum() != null) {
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if (vo.getAdOtherSaleNum() != null) {
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() != null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() != null ? vo.getOrderNumPercentage().toString() : "0.00");
        return builderVo.build();
    }
    public static AdReportDataRpcVoPb.AdReportDataRpcVo toPb(AdGroupHourVo vo) {
        AdReportDataRpcVoPb.AdReportDataRpcVo.Builder builderVo = AdReportDataRpcVoPb.AdReportDataRpcVo.newBuilder();

        if (vo.getAdCost() != null) {
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostCompare() != null) {
            builderVo.setAdCostCompare(vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostCompareRate() != null) {
            builderVo.setAdCostCompareRate(vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getRoas() != null) {
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCpa() != null) {
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdCostPerClick() != null) {
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAcos() != null) {
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getLabel() != null) {
            builderVo.setTitle(vo.getLabel());
        }
        if (vo.getImpressions() != null) {
            builderVo.setImpressions(vo.getImpressions());
        }
        if (vo.getImpressionsCompare() != null) {
            builderVo.setImpressionsCompare(vo.getImpressionsCompare());
        }
        if (vo.getImpressionsCompareRate() != null) {
            builderVo.setImpressionsCompareRate(vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getClicks() != null) {
            builderVo.setClicks(vo.getClicks());
        }
        if (vo.getClicksCompare() != null) {
            builderVo.setClicksCompare(vo.getClicksCompare());
        }
        if (vo.getClicksCompareRate() != null) {
            builderVo.setClicksCompareRate(vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCtr() != null) {
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getCvr() != null) {
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdOrderNum() != null) {
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if (vo.getAdOrderNumCompare() != null) {
            builderVo.setAdOrderNumCompare(vo.getAdOrderNumCompare());
        }
        if (vo.getAdOrderNumCompareRate() != null) {
            builderVo.setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getSelfAdOrderNum() != null) {
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if (vo.getOtherAdOrderNum() != null) {
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if (vo.getAdSale() != null) {
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleCompare() != null) {
            builderVo.setAdSaleCompare(vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleCompareRate() != null) {
            builderVo.setAdSaleCompareRate(vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null) {
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdOtherSale() != null) {
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSaleNum() != null) {
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if (vo.getAdSaleNumCompare() != null) {
            builderVo.setAdSaleNumCompare(vo.getAdSaleNumCompare());
        }
        if (vo.getAdSaleNumCompareRate() != null) {
            builderVo.setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSaleNum() != null) {
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if (vo.getAdOtherSaleNum() != null) {
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() != null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() != null ? vo.getOrderNumPercentage().toString() : "0.00");
        return builderVo.build();
    }

    public static AdReportDataRpcVoPb.AdReportDataRpcVo toPb(AdReportHourlyVO vo, BigDecimal shopSalesByDate) {
        AdReportDataRpcVoPb.AdReportDataRpcVo.Builder builderVo = AdReportDataRpcVoPb.AdReportDataRpcVo.newBuilder();

        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
            builderVo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate).setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompare() != null){
            builderVo.setAdCostCompare(vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompareRate() != null){
            builderVo.setAdCostCompareRate(vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getRoasCompare()).ifPresent(i -> builderVo.setRoasCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getRoasCompareRate()).ifPresent(i -> builderVo.setRoasCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getAdCostPerClickCompare()).ifPresent(i -> builderVo.setAdCostPerClickCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getAdCostPerClickCompareRate()).ifPresent(i -> builderVo.setAdCostPerClickCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getAcos() != null){
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getAcosCompare()).ifPresent(i -> builderVo.setAcosCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getAcosCompareRate()).ifPresent(i -> builderVo.setAcosCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getLabel() != null){
            builderVo.setTitle(vo.getLabel());
        }
        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if(vo.getImpressionsCompare() != null){
            builderVo.setImpressionsCompare(vo.getImpressionsCompare());
        }
        if(vo.getImpressionsCompareRate() != null){
            builderVo.setImpressionsCompareRate(vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if(vo.getClicksCompare() != null ){
            builderVo.setClicksCompare(vo.getClicksCompare());
        }
        if(vo.getClicksCompareRate() != null ){
            builderVo.setClicksCompareRate(vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getCtrCompare()).ifPresent(i -> builderVo.setCtrCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getCtrCompareRate()).ifPresent(i -> builderVo.setCtrCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getCvrCompare()).ifPresent(i -> builderVo.setCvrCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getCvrCompareRate()).ifPresent(i -> builderVo.setCvrCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if(vo.getAdOrderNumCompare()!= null){
            builderVo.setAdOrderNumCompare(vo.getAdOrderNumCompare());
        }
        if(vo.getAdOrderNumCompareRate()!= null){
            builderVo.setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if(vo.getAdSale()!= null){
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
            builderVo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate).setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompare() != null){
            builderVo.setAdSaleCompare(vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompareRate() != null){
            builderVo.setAdSaleCompareRate(vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if(vo.getAdSaleNumCompare() != null){
            builderVo.setAdSaleNumCompare(vo.getAdSaleNumCompare());
        }
        if(vo.getAdSaleNumCompareRate() != null){
            builderVo.setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() !=null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() !=null ? vo.getOrderNumPercentage().toString() : "0.00");

        builderVo.setViewableImpressions(getIntegerZeroValue(vo.getViewableImpressions()));
        builderVo.setVrt(getZeroValue(vo.getVrt()));
        builderVo.setVcpm(getZeroValue(vo.getVcpm()));
        builderVo.setVCtr(getZeroValue(vo.getVCtr()));
        builderVo.setOrdersNewToBrand(getIntegerZeroValue(vo.getOrdersNewToBrand()));
        builderVo.setUnitsOrderedNewToBrand(getIntegerZeroValue(vo.getUnitsOrderedNewToBrand()));
        builderVo.setSalesNewToBrand(getZeroValue(vo.getSalesNewToBrand()));
        builderVo.setAdvertisingUnitPrice(getZeroValue(vo.getAdvertisingUnitPrice()));
        builderVo.setAdvertisingProductUnitPrice(getZeroValue(vo.getAdvertisingProductUnitPrice()));
        builderVo.setAdvertisingOtherProductUnitPrice(getZeroValue(vo.getAdvertisingOtherProductUnitPrice()));
        builderVo.setOrdersNewToBrandPercentage(getZeroValue(vo.getOrdersNewToBrandPercentage()));
        builderVo.setUnitsOrderedNewToBrandPercentage(getZeroValue(vo.getUnitsOrderedNewToBrandPercentage()));
        builderVo.setSalesNewToBrandPercentage(getZeroValue(vo.getSalesNewToBrandPercentage()));
        return builderVo.build();
    }



    public static AdHourReportResponsePb.AdReportHourRpcVo toQueryReportPb(AdQueryKeywordAndTargetVo vo, BigDecimal shopSalesByDate) {
        AdHourReportResponsePb.AdReportHourRpcVo.Builder builderVo = AdHourReportResponsePb.AdReportHourRpcVo.newBuilder();
        if(vo.getAdCost() != null){
            builderVo.setAdCost(vo.getAdCost().setScale(2, RoundingMode.HALF_UP).toString());
//            builderVo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate).setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAcots() != null) {
            builderVo.setAcots(vo.getAcots().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompare() != null){
            builderVo.setAdCostCompare(vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdCostCompareRate() != null){
            builderVo.setAdCostCompareRate(vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getRoas() != null){
            builderVo.setRoas(vo.getRoas().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getRoasCompare()).ifPresent(i -> builderVo.setRoasCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getRoasCompareRate()).ifPresent(i -> builderVo.setRoasCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getCpa() != null){
            builderVo.setCpa(vo.getCpa().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getCpaCompare()).ifPresent(i -> builderVo.setCpaCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getCpaCompareRate()).ifPresent(i -> builderVo.setCpaCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getAdCostPerClick() != null){
            builderVo.setAdCostPerClick(vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getAdCostPerClickCompare()).ifPresent(i -> builderVo.setAdCostPerClickCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getAdCostPerClickCompareRate()).ifPresent(i -> builderVo.setAdCostPerClickCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getAcos() != null){
            builderVo.setAcos(vo.getAcos().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getAcosCompare()).ifPresent(i -> builderVo.setAcosCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getAcosCompareRate()).ifPresent(i -> builderVo.setAcosCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getLabel() != null){
            builderVo.setTitle(vo.getLabel());
        }
        if(vo.getImpressions() != null){
            builderVo.setImpressions(vo.getImpressions());
        }
        if(vo.getImpressionsCompare() != null){
            builderVo.setImpressionsCompare(vo.getImpressionsCompare());
        }
        if(vo.getImpressionsCompareRate() != null){
            builderVo.setImpressionsCompareRate(vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getClicks() != null){
            builderVo.setClicks(vo.getClicks());
        }
        if(vo.getClicksCompare() != null ){
            builderVo.setClicksCompare(vo.getClicksCompare());
        }
        if(vo.getClicksCompareRate() != null ){
            builderVo.setClicksCompareRate(vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getCtr() != null){
            builderVo.setCtr(vo.getCtr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getCtrCompare()).ifPresent(i -> builderVo.setCtrCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getCtrCompareRate()).ifPresent(i -> builderVo.setCtrCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getCvr() != null){
            builderVo.setCvr(vo.getCvr().setScale(2, RoundingMode.HALF_UP).toString());
        }
        Optional.ofNullable(vo.getCvrCompare()).ifPresent(i -> builderVo.setCvrCompare(i.setScale(2, RoundingMode.HALF_UP).toString()));
        Optional.ofNullable(vo.getCvrCompareRate()).ifPresent(i -> builderVo.setCvrCompareRate(i.setScale(2, RoundingMode.HALF_UP).toString()));

        if(vo.getAdOrderNum()!= null){
            builderVo.setAdOrderNum(vo.getAdOrderNum());
        }
        if(vo.getAdOrderNumCompare()!= null){
            builderVo.setAdOrderNumCompare(vo.getAdOrderNumCompare());
        }
        if(vo.getAdOrderNumCompareRate()!= null){
            builderVo.setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getSelfAdOrderNum() != null){
            builderVo.setSelfAdOrderNum(vo.getSelfAdOrderNum());
        }
        if(vo.getOtherAdOrderNum() != null){
            builderVo.setOtherAdOrderNum(vo.getOtherAdOrderNum());
        }
        if(vo.getAdSale()!= null){
            builderVo.setAdSale(vo.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
//            builderVo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate).setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAsots() != null) {
            builderVo.setAsots(vo.getAsots().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompare() != null){
            builderVo.setAdSaleCompare(vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleCompareRate() != null){
            builderVo.setAdSaleCompareRate(vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if (vo.getAdSelfSale() != null){
            builderVo.setAdSelfSale(vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdOtherSale()!= null){
            builderVo.setAdOtherSale(vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSaleNum()!= null){
            builderVo.setAdSaleNum(vo.getAdSaleNum());
        }
        if(vo.getAdSaleNumCompare() != null){
            builderVo.setAdSaleNumCompare(vo.getAdSaleNumCompare());
        }
        if(vo.getAdSaleNumCompareRate() != null){
            builderVo.setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP).toString());
        }
        if(vo.getAdSelfSaleNum() != null){
            builderVo.setAdSelfSaleNum(vo.getAdSelfSaleNum());
        }
        if(vo.getAdOtherSaleNum() != null){
            builderVo.setAdOtherSaleNum(vo.getAdOtherSaleNum());
        }
        // 花费占比
        builderVo.setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() : "0.00");
        // 销售额占比
        builderVo.setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() : "0.00");
        // 订单量占比
        builderVo.setAdOrderNumPercentage(vo.getAdOrderNumPercentage() != null ? vo.getAdOrderNumPercentage().toString() : "0.00");
        // 销量占比
        builderVo.setOrderNumPercentage(vo.getOrderNumPercentage() != null ? vo.getOrderNumPercentage().toString() : "0.00");

        builderVo.setViewableImpressions(getIntegerZeroValue(vo.getViewableImpressions()));
        builderVo.setVrt(getZeroValue(vo.getVrt()));
        builderVo.setVcpm(getZeroValue(vo.getVcpm()));
        builderVo.setVCtr(getZeroValue(vo.getVCtr()));
        builderVo.setOrdersNewToBrand(getIntegerZeroValue(vo.getOrdersNewToBrand()));
        builderVo.setUnitsOrderedNewToBrand(getIntegerZeroValue(vo.getUnitsOrderedNewToBrand()));
        builderVo.setSalesNewToBrand(getZeroValue(vo.getSalesNewToBrand()));
        builderVo.setAdvertisingUnitPrice(getZeroValue(vo.getAdvertisingUnitPrice()));
        builderVo.setAdvertisingProductUnitPrice(getZeroValue(vo.getAdvertisingProductUnitPrice()));
        builderVo.setAdvertisingOtherProductUnitPrice(getZeroValue(vo.getAdvertisingOtherProductUnitPrice()));
        builderVo.setOrdersNewToBrandPercentage(getZeroValue(vo.getOrdersNewToBrandPercentage()));
        builderVo.setUnitsOrderedNewToBrandPercentage(getZeroValue(vo.getUnitsOrderedNewToBrandPercentage()));
        builderVo.setSalesNewToBrandPercentage(getZeroValue(vo.getSalesNewToBrandPercentage()));

        return builderVo.build();
    }




    public static String getCurrencyUnit(String marketplaceId){
        AmznEndpoint byMarketplaceId = AmznEndpoint.getByMarketplaceId(marketplaceId);
        if (byMarketplaceId == null) {
            return "";
        }
        CurrencyUnitEnum byCurrency = CurrencyUnitEnum.getByCurrency(byMarketplaceId.getCurrencyCode().value());
        if (byCurrency == null) {
            return "";
        }

        return byCurrency.getUnit();

    }

}
