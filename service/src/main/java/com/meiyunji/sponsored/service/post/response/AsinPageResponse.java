package com.meiyunji.sponsored.service.post.response;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.post.po.ChildAsin;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AsinPageResponse implements Serializable {
    private static final long serialVersionUID = 123456L;

    private Page<AsinInfo> page;

    @Data
    public static class AsinInfo {
        private Long id;
        private Integer shopId;
        private Long parentId;
        private String msku;
        private String asin;
        private String onlineStatus;
        private String title;
        private String imgUrl;
        private String domain;
        private String marketplaceId;
        private String dxmPublishState;
        private List<ChildAsin> childList;
        private Boolean isMeetConditions; //是否符合接口添加条件
        private String eligibilityStatus;//合格状态
        private Integer fbaAvailable;
        //价格
        private String price;
        //星级
        private String rating;
        // 评论数
        private String ratingCount;
        private Integer isVariation;
        private String parentAsin;
    }
}
