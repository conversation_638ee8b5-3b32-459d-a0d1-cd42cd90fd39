package com.meiyunji.sponsored.service.multiple.targets.enums;

import com.meiyunji.sponsored.common.util.StringUtil;
import lombok.Getter;

import java.util.HashSet;
import java.util.Set;

/**
 * 投放列表页-报告统计枚举
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Getter
public enum TargetSpColumnEnum {
    COST("costDoris", " ,IFNULL(sum(r.cost),0) `costDoris`", ",IFNULL(sum(r.cost*d.rate),0) `costDoris` "),
    TOTAL_SALES("totalSalesDoris", " ,IFNULL(sum(total_sales),0) totalSalesDoris", ",IFNULL(sum(total_sales*d.rate),0) totalSalesDoris"),
    AD_SALES("adSalesDoris", " ,IFNULL(sum(ad_sales),0) adSalesDoris", " ,IFNULL(sum(ad_sales*d.rate),0) adSalesDoris"),
    IMPRESSIONS("impressionsDoris", " ,IFNULL(sum(`impressions`),0) impressionsDoris",""),
    CLICKS("clicksDoris", " ,IFNULL(sum(`clicks`),0) clicksDoris",""),
    ORDER_NUM("saleNumDoris", " ,IFNULL(sum(order_num),0) saleNumDoris",""),
    AD_ORDER_NUM("adSaleNumDoris", " ,IFNULL(sum(ad_order_num),0) adSaleNumDoris",""),
    SALE_NUM("orderNumDoris", " ,IFNULL(sum(sale_num),0) orderNumDoris",""),
    AD_SALE_NUM("adOrderNumDoris", " ,IFNULL(sum(ad_sale_num),0) adOrderNumDoris",""),
    MIN_TOP_IS("minTopIsDoris", " ,IFNULL(min(top_of_search_is),0) minTopIsDoris",""),
    MAX_TOP_IS("maxTopIsDoris", " ,max(top_of_search_is) maxTopIsDoris",""),
    ;

    // 排序字段
    private final String code;
    // 字段
    private final String column;
    // 汇率字段
    private final String rateColumn;

    TargetSpColumnEnum(String code, String column, String rateColumn) {
        this.code = code;
        this.column = column;
        this.rateColumn = rateColumn;
    }

    /**
     * 根据code获取统计字段
     */
    public static String getColumnByCode(String code,Boolean changeRate) {
        for (TargetSpColumnEnum columnEnum : TargetSpColumnEnum.values()) {
            if (columnEnum.getCode().equals(code)) {
                if(changeRate && StringUtil.isNotEmpty(columnEnum.getRateColumn())){
                    return columnEnum.getRateColumn();
                }else{
                    return columnEnum.getColumn();
                }
            }
        }
        return null;
    }

    public static Set<String> getAllCode() {
        Set<String> list = new HashSet<>();
        for (TargetSpColumnEnum columnEnum : TargetSpColumnEnum.values()) {
            // 过滤MIN_TOP_IS、MAX_TOP_IS
            if("minTopIsDoris".equals(columnEnum.getCode()) || "maxTopIsDoris".equals(columnEnum.getCode())){
                continue;
            }
            list.add(columnEnum.getCode());
        }
        return list;
    }

    public static Set<String> getAllColumn() {
        Set<String> list = new HashSet<>();
        for (TargetSpColumnEnum columnEnum : TargetSpColumnEnum.values()) {
            list.add(columnEnum.getColumn());
        }
        return list;
    }
}
