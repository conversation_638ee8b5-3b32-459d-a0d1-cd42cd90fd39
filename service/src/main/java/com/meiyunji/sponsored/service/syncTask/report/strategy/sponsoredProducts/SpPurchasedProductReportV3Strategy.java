package com.meiyunji.sponsored.service.syncTask.report.strategy.sponsoredProducts;

import com.alibaba.fastjson.JSONReader;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportV3Type;
import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;
import com.meiyunji.sponsored.common.springjdbc.PartitionSqlUtil;
import com.meiyunji.sponsored.service.account.dao.ISlaveVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdAsinReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdAsinReportKeywordDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdAsinReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdAsinReportKeyword;
import com.meiyunji.sponsored.service.enums.ShopTypeEnum;
import com.meiyunji.sponsored.service.syncTask.entity.SponsoredProductPurchasedProduct;
import com.meiyunji.sponsored.service.syncTask.report.strategy.AbstractReportProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

@Component
@Slf4j
@ConditionalOnProperty(name = "aadas.scheduler.executors.reports-consumer.enabled", havingValue = "true")
public class SpPurchasedProductReportV3Strategy extends AbstractReportProcessStrategy {

    private final IAmazonAdAsinReportKeywordDao amazonAdAsinReportKeywordDao;
    private final IAmazonAdAsinReportDao amazonAdAsinReportDao;
    private final PartitionSqlUtil partitionSqlUtil;
    @Resource
    private ISlaveVcShopAuthDao vcShopAuthDao;
    public SpPurchasedProductReportV3Strategy(
            CosBucketClient dataBucketClient,
            IAmazonAdAsinReportKeywordDao amazonAdAsinReportKeywordDao,
            IAmazonAdAsinReportDao amazonAdAsinReportDao, PartitionSqlUtil partitionSqlUtil) {
        super(dataBucketClient);
        this.amazonAdAsinReportKeywordDao = amazonAdAsinReportKeywordDao;
        this.amazonAdAsinReportDao = amazonAdAsinReportDao;
        this.partitionSqlUtil = partitionSqlUtil;
    }

    @Override
    public Boolean checkValid(ReportReadyNotification notification) {
        return notification.getVersion() ==  3
                && notification.getV3Type() == AmazonReportV3Type.sp_asin_purchase;
    }

    @Override
    public void processReport(ReportReadyNotification notification) throws Exception {
        try (InputStreamReader inputStreamReader = new InputStreamReader(new GZIPInputStream(new ByteArrayInputStream(dataBucketClient.getObjectToBytes(notification.getPath())))); JSONReader jsonReader = new JSONReader(inputStreamReader)) {
            jsonReader.startArray();
            List<SponsoredProductPurchasedProduct> reports = Lists.newArrayListWithExpectedSize(batchSize);
            VcShopAuth byIdAndPuid = vcShopAuthDao.getByIdAndPuid(notification.getMarketplaceIdentifier(), notification.getSellerIdentifier());
            String shopType = ShopTypeEnum.SC.getCode();
            if (byIdAndPuid != null && byIdAndPuid.getId() != null) {
                shopType = ShopTypeEnum.VC.getCode();
            }
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                SponsoredProductPurchasedProduct report = new SponsoredProductPurchasedProduct();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                reports.add(report);
                if (reports.size() >= batchSize) {
                    dealReport(notification, reports, shopType);
                    reports = Lists.newArrayListWithExpectedSize(batchSize);
                }
            }
            jsonReader.endArray();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReport(notification, reports, shopType);
            }
        } catch (Exception e) {
            log.info("报告处理发生错误{}@{} reportType={} countDate={}", notification.getSellerIdentifier()
                    , notification.getMarketplaceIdentifier(), notification.getType(), notification.getDate(), e);            throw e;
        }
    }

    private void dealReport(ReportReadyNotification notification, List<SponsoredProductPurchasedProduct> reports, String shopType) {
        //处理target报告
        List<SponsoredProductPurchasedProduct> targetReports = reports.stream().filter(o -> o.getKeywordType()
                != null && Arrays.asList("TARGETING_EXPRESSION", "TARGETING_EXPRESSION_PREDEFINED")
                .contains(o.getKeywordType())).collect(Collectors.toList());
        List<AmazonAdAsinReport> poList = getPoByReportByAsin(notification, targetReports, shopType);
        List<List<AmazonAdAsinReport>> partition = Lists.partition(poList, batchSize);
        for (List<AmazonAdAsinReport> amazonAdAsinReports : partition) {
            partitionSqlUtil.save(notification.getSellerIdentifier(), amazonAdAsinReports, 0, amazonAdAsinReportDao::insertList);
        }
        //处理keyword报告
        List<SponsoredProductPurchasedProduct> keywordReports = reports.stream().filter(o -> o.getKeywordType()
                != null && Arrays.asList("BROAD", "PHRASE", "EXACT")
                .contains(o.getKeywordType())).collect(Collectors.toList());
        List<AmazonAdAsinReportKeyword> poList1 = getPoByReportByAsinKeyword(notification, keywordReports, shopType);
        List<List<AmazonAdAsinReportKeyword>> partition1 = Lists.partition(poList1, batchSize);
        for (List<AmazonAdAsinReportKeyword> amazonAdAsinReportKeywords : partition1) {
            partitionSqlUtil.save(notification.getSellerIdentifier(), amazonAdAsinReportKeywords, 0, amazonAdAsinReportKeywordDao::insertList);
        }
    }


    private List<AmazonAdAsinReportKeyword> getPoByReportByAsinKeyword(
            ReportReadyNotification notification, List<SponsoredProductPurchasedProduct> reports, String shopType) {
        List<AmazonAdAsinReportKeyword> list = Lists.newArrayList();
        AmazonAdAsinReportKeyword po;
        for (SponsoredProductPurchasedProduct reportAsins : reports) {
            po = new AmazonAdAsinReportKeyword();
            po.setCountDate(reportAsins.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            po.setPuid(notification.getSellerIdentifier());
            po.setShopId(notification.getMarketplaceIdentifier());
            po.setMarketplaceId(notification.getMarketplace().getId());
            po.setAsin(reportAsins.getAdvertisedAsin());
            po.setOtherAsin(reportAsins.getPurchasedAsin());
            po.setSku(reportAsins.getAdvertisedSku());
            po.setCampaignId(String.valueOf(reportAsins.getCampaignId()));
            po.setCampaignName(reportAsins.getCampaignName());
            po.setAdGroupId(String.valueOf(reportAsins.getAdGroupId()));
            po.setAdGroupName(reportAsins.getAdGroupName());
            po.setKeywordId(String.valueOf(reportAsins.getKeywordId()));
            po.setKeywordText(reportAsins.getKeyword());
            po.setMatchType(reportAsins.getMatchType());
            po.setCurrency(reportAsins.getCampaignBudgetCurrencyCode());

            if (ShopTypeEnum.VC.getCode().equalsIgnoreCase(shopType)) {
                po.setAttributedUnitsOrdered7d(reportAsins.getUnitsSoldOtherSku14d());
                po.setAttributedUnitsOrdered14d(reportAsins.getUnitsSoldOtherSku7d());
                po.setAttributedUnitsOrdered7dOtherSKU(reportAsins.getUnitsSoldOtherSku14d());
                po.setAttributedUnitsOrdered14dOtherSKU(reportAsins.getUnitsSoldOtherSku7d());
                po.setAttributedSales7dOtherSKU(reportAsins.getSalesOtherSku14d());
                po.setAttributedSales14dOtherSKU(reportAsins.getSalesOtherSku7d());
                po.setPurchasesOtherSku7d(reportAsins.getPurchasesOtherSku14d());
            } else {
                po.setAttributedUnitsOrdered7d(reportAsins.getUnitsSoldOtherSku7d());
                po.setAttributedUnitsOrdered14d(reportAsins.getUnitsSoldOtherSku14d());
                po.setAttributedUnitsOrdered7dOtherSKU(reportAsins.getUnitsSoldOtherSku7d());
                po.setAttributedUnitsOrdered14dOtherSKU(reportAsins.getUnitsSoldOtherSku14d());
                po.setAttributedSales7dOtherSKU(reportAsins.getSalesOtherSku7d());
                po.setAttributedSales14dOtherSKU(reportAsins.getSalesOtherSku14d());
                po.setPurchasesOtherSku7d(reportAsins.getPurchasesOtherSku7d());
            }

            po.setAttributedUnitsOrdered1d(reportAsins.getUnitsSoldOtherSku1d());
            po.setAttributedUnitsOrdered30d(reportAsins.getUnitsSoldOtherSku30d());
            po.setAttributedUnitsOrdered1dOtherSKU(reportAsins.getUnitsSoldOtherSku1d());
            po.setAttributedUnitsOrdered30dOtherSKU(reportAsins.getUnitsSoldOtherSku30d());
            po.setAttributedSales1dOtherSKU(reportAsins.getSalesOtherSku1d());
            po.setAttributedSales30dOtherSKU(reportAsins.getSalesOtherSku30d());
            list.add(po);
        }
        return list;
    }


    private List<AmazonAdAsinReport> getPoByReportByAsin(ReportReadyNotification notification, List<SponsoredProductPurchasedProduct> reports, String shopType) {
        List<AmazonAdAsinReport> list = Lists.newArrayList();
        AmazonAdAsinReport po;
        for (SponsoredProductPurchasedProduct reportAsins : reports) {
            po = new AmazonAdAsinReport();
            po.setCountDate(reportAsins.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            po.setPuid(notification.getSellerIdentifier());
            po.setShopId(notification.getMarketplaceIdentifier());
            po.setMarketplaceId(notification.getMarketplace().getId());
            po.setAsin(reportAsins.getAdvertisedAsin());
            po.setOtherAsin(reportAsins.getPurchasedAsin());
            po.setSku(reportAsins.getAdvertisedSku());
            po.setCampaignId(String.valueOf(reportAsins.getCampaignId()));
            po.setCampaignName(reportAsins.getCampaignName());
            po.setAdGroupId(String.valueOf(reportAsins.getAdGroupId()));
            po.setAdGroupName(reportAsins.getAdGroupName());
            po.setTargetId(String.valueOf(reportAsins.getKeywordId()));
            po.setTargetingText(reportAsins.getKeyword());
            po.setTargetType(reportAsins.getMatchType());
            po.setCurrency(reportAsins.getCampaignBudgetCurrencyCode());
            po.setMatchType(reportAsins.getMatchType());

            if (ShopTypeEnum.VC.getCode().equalsIgnoreCase(shopType)) {
                po.setAttributedUnitsOrdered7d(reportAsins.getUnitsSoldOtherSku14d());
                po.setAttributedUnitsOrdered14d(reportAsins.getUnitsSoldOtherSku7d());
                po.setAttributedUnitsOrdered7dOtherSKU(reportAsins.getUnitsSoldOtherSku14d());
                po.setAttributedUnitsOrdered14dOtherSKU(reportAsins.getUnitsSoldOtherSku7d());
                po.setAttributedSales7dOtherSKU(reportAsins.getSalesOtherSku14d());
                po.setAttributedSales14dOtherSKU(reportAsins.getSalesOtherSku7d());
                po.setPurchasesOtherSku7d(reportAsins.getPurchasesOtherSku14d());
            } else {
                po.setAttributedUnitsOrdered7d(reportAsins.getUnitsSoldOtherSku7d());
                po.setAttributedUnitsOrdered14d(reportAsins.getUnitsSoldOtherSku14d());
                po.setAttributedUnitsOrdered7dOtherSKU(reportAsins.getUnitsSoldOtherSku7d());
                po.setAttributedUnitsOrdered14dOtherSKU(reportAsins.getUnitsSoldOtherSku14d());
                po.setAttributedSales7dOtherSKU(reportAsins.getSalesOtherSku7d());
                po.setAttributedSales14dOtherSKU(reportAsins.getSalesOtherSku14d());
                po.setPurchasesOtherSku7d(reportAsins.getPurchasesOtherSku7d());
            }

            po.setAttributedUnitsOrdered1d(reportAsins.getUnitsSoldOtherSku1d());
            po.setAttributedUnitsOrdered30d(reportAsins.getUnitsSoldOtherSku30d());
            po.setAttributedUnitsOrdered1dOtherSKU(reportAsins.getUnitsSoldOtherSku1d());
            po.setAttributedUnitsOrdered30dOtherSKU(reportAsins.getUnitsSoldOtherSku30d());
            po.setAttributedSales1dOtherSKU(reportAsins.getSalesOtherSku1d());
            po.setAttributedSales30dOtherSKU(reportAsins.getSalesOtherSku30d());

            list.add(po);
        }
        return list;
    }
}
