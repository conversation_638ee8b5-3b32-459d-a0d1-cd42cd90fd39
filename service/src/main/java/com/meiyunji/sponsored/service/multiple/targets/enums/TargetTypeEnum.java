package com.meiyunji.sponsored.service.multiple.targets.enums;

import com.meiyunji.sponsored.service.enums.AdMarkupTargetTypeEnum;
import com.meiyunji.sponsored.service.enums.AutoRuleTargetTypeEnum;
import lombok.Getter;

/**
 * 广告管理-投放类型枚举
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Getter
public enum TargetTypeEnum {
    SP_KEYWORD("spKeywordProcessor", AdMarkupTargetTypeEnum.KEYWORD, AutoRuleTargetTypeEnum.keywordTarget, TargetTableEnum.SP_KEYWORD, AdMarkupTargetTypeEnum.KEYWORD, "sp关键词投放"),
    SB_KEYWORD("sbKeywordProcessor", AdMarkupTargetTypeEnum.KEYWORD, AutoRuleTargetTypeEnum.keywordTarget, TargetTableEnum.SB_KEYWORD, AdMarkupTargetTypeEnum.KEYWORD, "sb关键词投放"),
    SP_TARGET("spTargetProcessor", AdMarkupTargetTypeEnum.TARGET, AutoRuleTargetTypeEnum.productTarget, TargetTableEnum.SP_TARGET, AdMarkupTargetTypeEnum.TARGET, "sp商品投放"),
    SP_AUTO_TARGET("spTargetProcessor", AdMarkupTargetTypeEnum.TARGET, AutoRuleTargetTypeEnum.autoTarget, TargetTableEnum.SP_TARGET, AdMarkupTargetTypeEnum.TARGET, "sp自动投放"),
    SB_TARGET("sbTargetProcessor", AdMarkupTargetTypeEnum.TARGET, AutoRuleTargetTypeEnum.productTarget, TargetTableEnum.SB_TARGET, AdMarkupTargetTypeEnum.TARGET, "sb商品投放"),
    SD_TARGET("sdTargetProcessor", AdMarkupTargetTypeEnum.TARGET, AutoRuleTargetTypeEnum.productTarget, TargetTableEnum.SD_TARGET, AdMarkupTargetTypeEnum.TARGET, "sd商品投放"),
    SD_AUDIENCE_TARGET("sdTargetProcessor", AdMarkupTargetTypeEnum.TARGET, AutoRuleTargetTypeEnum.audienceTarget, TargetTableEnum.SD_TARGET, AdMarkupTargetTypeEnum.TARGET, "sd受众投放"),
    ;

    // 模版模式子类bean名称
    private String beanName;
    // 标签枚举
    private AdMarkupTargetTypeEnum adMarkupTargetTypeEnum;
    // 自动化规则枚举
    private AutoRuleTargetTypeEnum autoRuleTargetTypeEnum;
    // 投放层级表霉菌
    private TargetTableEnum targetTableEnum;
    // 投放层级表霉菌
    private AdMarkupTargetTypeEnum targetTagEnum;
    // 投放层级标签枚举
    private String desc;

    TargetTypeEnum(String beanName, AdMarkupTargetTypeEnum adMarkupTargetTypeEnum, AutoRuleTargetTypeEnum autoRuleTargetTypeEnum, TargetTableEnum targetTableEnum, AdMarkupTargetTypeEnum targetTagEnum, String desc) {
        this.beanName = beanName;
        this.adMarkupTargetTypeEnum = adMarkupTargetTypeEnum;
        this.autoRuleTargetTypeEnum = autoRuleTargetTypeEnum;
        this.targetTableEnum = targetTableEnum;
        this.targetTagEnum = targetTagEnum;
        this.desc = desc;
    }

    public static TargetTypeEnum getEnumByType(String adType, String targetType) {
        if ("sp".equals(adType) && "keyword".equals(targetType)) {
            return SP_KEYWORD;
        }
        if ("sp".equals(adType) && "targeting".equals(targetType)) {
            return SP_TARGET;
        }
        if ("sp".equals(adType) && "auto".equals(targetType)) {
            return SP_AUTO_TARGET;
        }
        if ("sb".equals(adType) && "keyword".equals(targetType)) {
            return SB_KEYWORD;
        }
        if ("sb".equals(adType) && "product".equals(targetType)) {
            return SB_TARGET;
        }
        if ("sd".equals(adType) && "T00020".equals(targetType)) {
            return SD_TARGET;
        }
        if ("sd".equals(adType) && "T00030".equals(targetType)) {
            return SD_AUDIENCE_TARGET;
        }
        return null;
    }
}
