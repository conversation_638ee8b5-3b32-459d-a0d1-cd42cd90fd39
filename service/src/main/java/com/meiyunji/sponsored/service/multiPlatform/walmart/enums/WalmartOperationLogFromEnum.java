package com.meiyunji.sponsored.service.multiPlatform.walmart.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
public enum WalmartOperationLogFromEnum {

    /**
     * 操作类型
     */
    SELLFOX("sellfox", "赛狐"),
    AUTO("auto", "自动化调整")
    ;

    private String operationType;

    private String operationValue;

    WalmartOperationLogFromEnum(String operationType, String operationValue) {
        this.operationType = operationType;
        this.operationValue = operationValue;
    }

    public static String getOperationValue(String operationType){
        WalmartOperationLogFromEnum[] values = values();
        for (WalmartOperationLogFromEnum value : values) {
            if(operationType.equalsIgnoreCase(value.getOperationType())){
                return value.getOperationValue();
            }
        }
        return "";
    }

    public static WalmartOperationLogFromEnum getByType(String operationType){
        WalmartOperationLogFromEnum[] values = values();
        for (WalmartOperationLogFromEnum value : values) {
            if(operationType.equalsIgnoreCase(value.getOperationType())){
                return value;
            }
        }
        return null;
    }
}
