package com.meiyunji.sponsored.service.download.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.UpdateBuilder;
import com.meiyunji.sponsored.service.download.dao.IAdDownloadCenterDao;
import com.meiyunji.sponsored.service.download.dao.mapper.AdDownloadCenterMapper;
import com.meiyunji.sponsored.service.download.po.AdDownloadCenter;
import com.meiyunji.sponsored.service.download.vo.AdDownloadCenterAddRequestVo;
import com.meiyunji.sponsored.service.download.vo.AdDownloadCenterQueryRequestVo;
import com.meiyunji.sponsored.service.download.vo.AdDownloadCenterQueryResponseVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public class AdDownloadCenterDaoImpl extends BaseShardingDaoImpl<AdDownloadCenter> implements IAdDownloadCenterDao {

    @Override
    public Page<AdDownloadCenterQueryResponseVo> pageList(AdDownloadCenterQueryRequestVo request) {
        StringBuilder sql = new StringBuilder("select t.id,t.puid,GROUP_CONCAT(s.shop_id) as shopIds," +
                " t.task_name,t.ad_type,t.report_type,t.report_state,report_url,t.report_cycle,t.time_unit,"+
                " t.report_start_date,t.report_end_date,t.create_time,t.create_id" +
                " from t_ad_download_center_task t" +
                " left join t_ad_download_center_task_details s on (t.puid = s.puid and t.id=s.task_id)" +
                " where t.puid = ?");
        List<Object> args = new ArrayList<>();
        args.add(request.getPuid());
        StringBuilder whereSql = new StringBuilder();
        if (StringUtils.isNotBlank(request.getTaskName())) {
            whereSql.append(" and t.task_name like ?");
            args.add("%"+request.getTaskName()+"%");
        }
        if (CollectionUtils.isNotEmpty(request.getAdTypeList())) {
            whereSql.append(" and t.ad_type in ('").append(StringUtils.join(request.getAdTypeList(),"','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(request.getTaskIds())) {
            whereSql.append(" and t.id in ('").append(StringUtils.join(request.getTaskIds(),"','")).append("') ");
        }
        if (request.getStartDate() != null) {
            whereSql.append(" and t.create_date >= ?");
            args.add(request.getStartDate());
        }
        if (request.getEndDate() != null) {
            whereSql.append(" and t.create_date <= ?");
            args.add(request.getEndDate());
        }
        if (CollectionUtils.isNotEmpty(request.getReportTypeList())) {
            whereSql.append(" and t.report_type in ('").append(StringUtils.join(request.getReportTypeList(),"','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(request.getShopIds())) {
            whereSql.append(" and s.shop_id in ('").append(StringUtils.join(request.getShopIds(),"','")).append("') ");
        }
        StringBuilder countSql = new StringBuilder("select COUNT(DISTINCT(t.id)) from t_ad_download_center_task t " +
                " left join t_ad_download_center_task_details s on (t.puid = s.puid and t.id=s.task_id)" +
                " where t.puid = ?");
        sql.append(whereSql);
        countSql.append(whereSql);
        sql.append(" group by t.id");
        sql.append(" order by t.create_time desc");
        return getPageByMapper(request.getPuid(),request.getPageNo(),request.getPageSize(),
                countSql.toString(),args.toArray(),sql.toString(),args.toArray(),new AdDownloadCenterMapper());
    }

    @Override
    public AdDownloadCenter copyDownloadTask(Integer puid, Long id) {
        ConditionBuilder build = new ConditionBuilder.Builder()
                .equalTo("id",id)
                .equalTo("puid",puid).build();
        return getByCondition(puid,build);
    }

    @Override
    public Integer delDownloadTask(Integer puid, Long id) {
        return this.deleteByPuidAndId(puid,id);
    }

    @Override
    public void updateDownloadTask(Integer puid, Long id, String url) {
        UpdateBuilder ub = new UpdateBuilder("t_ad_download_center_task");
        ub.forColumn("report_url",url);
        ub.forColumn("report_state","已生成");
        ub.where("id","=",id);
        getJdbcTemplate(puid).update(ub.toSql(),ub.getQueryValues());
    }
}
