package com.meiyunji.sponsored.service.doris.dao.impl;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.vo.SPPageParam;
import com.meiyunji.sponsored.service.doris.dao.IOdsVcProductDao;
import com.meiyunji.sponsored.service.product.po.OdsVcProduct;
import org.apache.commons.lang3.StringUtils;

public  class IOdsVcProductDaoImpl implements IOdsVcProductDao {

    @Override
    public Page<OdsVcProduct> getProductPageList(Integer puid, SPPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("vc_shop_id", param.getShopId())
                .in("is_variation", new Object[]{0, 2})
                .equalTo("status", 1);


        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if ("title".equals(param.getSearchField())) {
                builder.like("title", param.getSearchValue());
            } else if ("asin".equals(param.getSearchField())) {
                builder.inStrList("asin", param.getSearchValue().split(StringUtil.SPLIT_COMMA));
            } else if ("msku".equals(param.getSearchField())) {
                builder.like("msku", param.getSearchValue());
            }
        }

        String subquerySql = "select id from ods_t_vc_product where " + builder.build().getSql();
        String countSql = "select count(*) from (" + subquerySql + ") t";


        builder.groupBy("puid", "vc_shop_id", "asin");
        builder.orderBy("id");
        if ("asc".equalsIgnoreCase(param.getOrderType())) {
            builder.asc();
        } else {
            builder.desc();
        }

        ConditionBuilder condition = builder.build();
        String sql = "select max(id), puid, vc_shop_id, marketplace_id, asin, parent_asin, child_asins, main_image, msku, commodity_id, match_commodity_time, title, brand, manufacturer, manufacturer_code, variation_child_str, part_number, product_bundle, is_variation, standard_product_id, standard_product_type, standard_price, standard_price_currency, net_cost_amount, net_cost_amount_currency, status, display_group_rank, display_group_ranks, classification_rank, classification_ranks, last_sync_time, create_time, update_time, create_id, update_id from ods_t_vc_product where " + condition.getSql();

        return getPageResult(param.getPageNo(), param.getPageSize(), countSql, condition.getValues(), sql, condition.getValues(), OdsVcProduct.class);
    }
}
