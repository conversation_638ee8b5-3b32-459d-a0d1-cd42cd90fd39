package com.meiyunji.sponsored.service.export.handler;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.export.util.ExportStringUtil;
import com.meiyunji.sponsored.service.post.enums.PostPageExportFieldEnum;
import com.meiyunji.sponsored.service.post.enums.PostStatusEnum;
import com.meiyunji.sponsored.service.post.request.GetPostsRequest;
import com.meiyunji.sponsored.service.post.response.GetPostsResponse;
import com.meiyunji.sponsored.service.post.service.Impl.PostServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

@Slf4j
@Service(AdManagePageExportTaskConstant.POST)
public class PostPageExportTaskHandler implements AdManagePageExportTaskHandler {

    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private PostServiceImpl postService;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IExcelService excelService;

    @Override
    public void export(AdManagePageExportTask task) {
        GetPostsRequest request = JSONUtil.jsonToObject(task.getParam(), GetPostsRequest.class);
        if (request == null) {
            log.error(String.format("post export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        String fileName = "品牌帖子";
        GetPostsResponse response = postService.pageList(request);
        List<GetPostsResponse.Posts> rows = response.getPage().getRows();
        if (CollectionUtils.isEmpty(rows)) {
            //修改任务状态
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            //修改状态， 前端收到后转圈效果停止
            stringRedisService.set(request.getFilterDto().getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }
        List<List<GetPostsResponse.Posts>> partition = Lists.partition(rows, Constants.EXPORT_MAX_SIZE);

        List<String> urlList = customExport(partition, request.getExportSortField(), request.getFreezeNum(), fileName, request.getFilterDto().getPuid());

        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(request.getFilterDto().getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }

    private List<String> customExport(List<List<GetPostsResponse.Posts>> partition, String exportSortField, Integer freezeNum, String fileName, Integer puid) {
        //存储文件路径urlList
        List<String> downloadUrl = new ArrayList<>();

        //自定义排序：根据param的exportSortField，中的字段作为表格中的表头字段顺序导出
        List<String> sortFields = Arrays.asList(exportSortField.split(","));
        if (CollectionUtils.isEmpty(sortFields)) {
            return downloadUrl;
        }
        //导出
        for (List<GetPostsResponse.Posts> partitionList : partition) {
            if (CollectionUtils.isEmpty(partitionList)) {
                continue;
            }
            String url = customFieldSortExport(partitionList, sortFields, freezeNum, puid, fileName);
            downloadUrl.add(url);
        }
        return downloadUrl;
    }

    private String customFieldSortExport(List<GetPostsResponse.Posts> exportDtoList, List<String> sortFields, Integer freezeNum, Integer puid, String fileName) {
        //Excel样式builder
        WriteHandlerBuild build = new WriteHandlerBuild();
        //冻结前n列前1行
        if (freezeNum != null) {
            build.freezeRowAndCol(freezeNum, 1);
        }

        //构建行
        List<List<Object>> rows = new ArrayList<>(exportDtoList.size());
        for (GetPostsResponse.Posts exportDto : exportDtoList) {
            List<Object> cols = new ArrayList<>(PostPageExportFieldEnum.values().length);
            for (String fieldName : sortFields) {
                PostPageExportFieldEnum fieldEnum = PostPageExportFieldEnum.fromPoParamKey(fieldName);
                if (fieldEnum == null) {
                    break;
                }
                Object value = getObjectByField(exportDto, fieldEnum);
                cols.add(value);
                if (PostPageExportFieldEnum.STATUS == fieldEnum && PostStatusEnum.REJECTED.getName().equals(value) && StringUtils.isNotBlank(exportDto.getStatusMetadata())) {
                    // 对于状态为未批准的，进行添加批注
                    HashMap<String, String> map = new HashMap<>();
                    map.put(value.toString(), getRejectionDetails(exportDto.getStatusMetadata()));
                    build.cellComment(map);
                }
            }
            rows.add(cols);
        }
        //构建表头
        List<String> headNames = new ArrayList<>(sortFields.size());
        for (String sortField : sortFields) {
            PostPageExportFieldEnum fieldEnum = PostPageExportFieldEnum.fromPoParamKey(sortField);
            if (fieldEnum == null) {
                log.error("sortFields 包含非法字符，导出阻止，返回空, sortFields:{}", sortFields);
                throw new RuntimeException("sortFields 包含非法字符，导出阻止，返回空, sortFields:" + sortFields);
            }
            headNames.add(fieldEnum.getTableColName());
        }
        //导出
        return excelService.exportByCustomColSort(puid, headNames, rows, fileName + "(" + (0) + ")", build);
    }

    private Object getObjectByField(GetPostsResponse.Posts exportDto, PostPageExportFieldEnum fieldEnum) {
        Object value = null;
        switch (fieldEnum) {
            case STATUS:
                value =  PostStatusEnum.getNameByStatus(exportDto.getStatus());
                break;
            case SHOP_NAME:
                value = exportDto.getShopName();
                break;
            case BRAND_NAME:
                value = exportDto.getBrandName();
                break;
            case CAPTION:
                value = exportDto.getCaption();
                break;
            case ASIN:
                value = String.join(",", exportDto.getAsinList());
                break;
            case LIVE_DATE:
                value = exportDto.getLiveDate();
                break;
            case IMPRESSIONS:
                value = exportDto.getImpressions();
                break;
            case CLICKS:
                value = exportDto.getClicks();
                break;
            case CTR:
                if (!"0".equals(exportDto.getCtr())) {
                    value = exportDto.getCtr() + "%";
                } else {
                    value = "0%";
                }
                break;
            case CLICKS_TO_BRAND_STORE:
                value = exportDto.getClicksToBrandStore();
                break;
            case CLICKS_TO_DETAIL_PAGE:
                value = exportDto.getClicksToDetailPage();
                break;
            case CLICKS_TO_FOLLOW:
                value = exportDto.getClicksToFollow();
                break;
            case REACH:
                value = exportDto.getReach();
                break;
            case POST_CREATED_DATE:
                value = exportDto.getPostCreatedDate();
                break;
            case CREATOR:
                value = exportDto.getCreator();
                break;
            case REMARK:
                value = exportDto.getRemark();
                break;
        }
        return value;
    }

    public static String getRejectionDetails(String statusMetadataStr) {
        StringBuilder details = new StringBuilder();
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JsonNode statusMetadataNode = objectMapper.readTree(statusMetadataStr);
            JsonNode rejectionReasonsNode = statusMetadataNode.path("rejectionReasons");
            if (rejectionReasonsNode.isArray()) {
                for (JsonNode rejectionReasonNode : rejectionReasonsNode) {
                    JsonNode detailNode = rejectionReasonNode.path("detail");
                    if (!detailNode.isMissingNode()) {
                        details.append(detailNode.asText()).append(";");
                    }
                }
            }
        } catch (IOException e) {
            log.error("导出解析 statusMetadata 失败", e);
        }
        return details.toString();
    }

}
