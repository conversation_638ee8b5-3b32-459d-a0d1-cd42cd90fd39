package com.meiyunji.sponsored.service.reportDiffMonitor.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.rpc.syncReport.AdTypeEnum;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.bo.ReportMonitorBo;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdKeywordReportDao;
import com.meiyunji.sponsored.service.cpc.dao.ICpcTargetingReportDao;
import com.meiyunji.sponsored.service.reportDiffMonitor.DiffMonitorConstants;
import com.meiyunji.sponsored.service.reportDiffMonitor.dto.ShopDTO;
import com.meiyunji.sponsored.service.reportDiffMonitor.enums.LevelTypeEnum;
import com.meiyunji.sponsored.service.reportDiffMonitor.repository.ReportLevelDiffMonitorDao;
import com.meiyunji.sponsored.service.reportDiffMonitor.repository.po.ReportLevelDiffMonitor;
import com.meiyunji.sponsored.service.util.WxNotificationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.common.util.MathUtil.DEF_DIV_DIFF;

/**
 * 报告数据层级差异监控service
 *
 * @Author: hejh
 * @Date: 2024/5/30 11:15
 */
@Slf4j
@Service
public class ReportLevelDiffMonitorService {
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private ReportLevelDiffMonitorDao reportLevelDiffMonitorDao;
    @Autowired
    private IAmazonAdCampaignAllReportDao campaignAllReportDao;
    @Autowired
    private IAmazonAdGroupReportDao groupReportDao;
    @Autowired
    private IAmazonAdKeywordReportDao keywordReportDao;
    @Autowired
    private ICpcTargetingReportDao targetingReportDao;
    @Autowired
    private DynamicRefreshConfiguration configuration;
    @Autowired
    private ReportDateDiffMonitorService reportDateDiffMonitorService;

    /**
     * 处理报告层级差异监控
     */
    public void dealReportLevelDiffMonitor() {
        //1、随机抽样
        List<ShopDTO> shopDTOS = reportDateDiffMonitorService.getRandomShopDTO();
        if (CollectionUtils.isEmpty(shopDTOS)) {
            log.info("report level random shopDTO data list is empty");
            return;
        }
        //2、查询样本数据各指标，根据广告类型分类，分别生成指标监控
        List<ReportLevelDiffMonitor> diffMonitorList = new ArrayList<>();
        List<ReportLevelDiffMonitor> spReportLevelDiffMonitors = generateMonitorByAdType(AdTypeEnum.sp, shopDTOS);
        List<ReportLevelDiffMonitor> sbReportLevelDiffMonitors = generateMonitorByAdType(AdTypeEnum.sb, shopDTOS);
        List<ReportLevelDiffMonitor> sdReportLevelDiffMonitors = generateMonitorByAdType(AdTypeEnum.sd, shopDTOS);
        diffMonitorList.addAll(spReportLevelDiffMonitors);
        diffMonitorList.addAll(sbReportLevelDiffMonitors);
        diffMonitorList.addAll(sdReportLevelDiffMonitors);
        if (CollectionUtils.isEmpty(diffMonitorList)) {
            log.info("report level diff monitor list data is empty");
            return;
        }

        //3、落库
        reportLevelDiffMonitorDao.saveList(diffMonitorList);

        for (ReportLevelDiffMonitor monitor: diffMonitorList) {
            if (monitor.getDiff().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            WxNotificationUtil.sendBigContent(DiffMonitorConstants.WX_DIFF_WARN_URL,
                "店铺报告数据层级差异监控\n" + String.format("广告类型：%s, 采集时间：%s, %s相对于广告活动差异值: %s", monitor.getAdType(), LocalDate.now(), LevelTypeEnum.valueOf(monitor.getLevelType()).getDesc(), monitor.getDiffPercent()));
        }
    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    /**
     * 根据广告类型分类，分别生成指标监控
     *
     * @param adType   广告类型
     * @param shopDTOS shopDTOS
     */
    private List<ReportLevelDiffMonitor> generateMonitorByAdType(AdTypeEnum adType, List<ShopDTO> shopDTOS) {
        //统计前一天的报告数据
        String countDate = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        //1、查询shopList对应的数据,活动(cost、clicks、impressions)、组(cost、clicks、impressions)、投放(cost、clicks、impressions)
        //广告活动
        List<ReportMonitorBo> campaignBoList = campaignAllReportDao.getReportLevelMonitorBoList(shopDTOS, adType, countDate, countDate);
        if (CollectionUtils.isEmpty(campaignBoList)) {
            log.info("{} report level query campaignBoList is empty", adType.name());
            return Collections.emptyList();
        }
        List<ShopDTO> filterShops = new ArrayList<>();
        for (ReportMonitorBo monitorBo : campaignBoList) {
            ShopDTO shopDTO = new ShopDTO();
            shopDTO.setShopId(monitorBo.getShopId());
            shopDTO.setPuid(monitorBo.getPuid());
            filterShops.add(shopDTO);
        }
        //广告组
        List<ReportMonitorBo> groupBoList = groupReportDao.getReportLevelMonitorBoList(filterShops, adType, countDate, countDate);

        //关键词
        List<ReportMonitorBo> targetBoList = new ArrayList<>();
        if (!AdTypeEnum.sd.equals(adType)) {
            targetBoList.addAll(keywordReportDao.getReportLevelMonitorBoList(filterShops, adType, countDate, countDate));
        }
        //投放
        targetBoList.addAll(targetingReportDao.getReportLevelMonitorBoList(filterShops, adType, countDate, countDate));

        //2、计算差异值
        BigDecimal groupDiff = countTotalDiff(campaignBoList, groupBoList);
        BigDecimal targetDiff = countTotalDiff(campaignBoList, targetBoList);

        List<List<ReportMonitorBo>> groupExperimentData = new ArrayList<>(Arrays.asList(campaignBoList, groupBoList));
        List<List<ReportMonitorBo>> targetExperimentData = new ArrayList<>(Arrays.asList(campaignBoList, targetBoList));

        //3、生成指标监控记录，2条：广告组和广告投放相对于广告活动的差异
        List<ReportLevelDiffMonitor> result = new ArrayList<>();
        result.add(generateDiffRecord(adType, shopDTOS, countDate, groupExperimentData, groupDiff, LevelTypeEnum.GROUP));
        result.add(generateDiffRecord(adType, shopDTOS, countDate, targetExperimentData, targetDiff, LevelTypeEnum.TARGETING));
        return result;
    }

    /**
     * 生成监控记录
     *
     * @param adType         广告类型
     * @param shopDTOS       实验店铺数据
     * @param countDate      countDate
     * @param experimentData 实验数据
     * @param diff           差异值
     * @param levelTypeEnum  层级类型枚举
     * @return
     */
    private ReportLevelDiffMonitor generateDiffRecord(AdTypeEnum adType, List<ShopDTO> shopDTOS, String countDate, List<List<ReportMonitorBo>> experimentData, BigDecimal diff, LevelTypeEnum levelTypeEnum) {
        ReportLevelDiffMonitor reportLevelDiffMonitor = new ReportLevelDiffMonitor();
        reportLevelDiffMonitor.setAdType(adType.name());
        reportLevelDiffMonitor.setLevelType(levelTypeEnum.getValue());
        reportLevelDiffMonitor.setCountDate(countDate);
        reportLevelDiffMonitor.setExperimentJson(JSON.toJSONString(shopDTOS));
        reportLevelDiffMonitor.setMetricJson(JSON.toJSONString(experimentData));
        reportLevelDiffMonitor.setDiff(diff);
        reportLevelDiffMonitor.setDiffPercent(diff.multiply(new BigDecimal(100)).toPlainString() + "%");
        return reportLevelDiffMonitor;
    }

    /**
     * 计算总差异
     *
     * @param sourceBoList  sourceBoList
     * @param compareBoList compareBoList
     * @return
     */
    private BigDecimal countTotalDiff(List<ReportMonitorBo> sourceBoList, List<ReportMonitorBo> compareBoList) {
        //所有店铺差异列表
        List<BigDecimal> diffList = new ArrayList<>();
        Map<Integer, ReportMonitorBo> shopIdCampareBoMonitorBoMap = compareBoList.stream()
            .collect(Collectors.toMap(ReportMonitorBo::getShopId, Function.identity(), (bo1, bo2) -> {
                BigDecimal totalCost1 = Objects.isNull(bo1.getTotalCost()) ? BigDecimal.ZERO : bo1.getTotalCost();
                BigDecimal totalCost2 = Objects.isNull(bo2.getTotalCost()) ? BigDecimal.ZERO : bo2.getTotalCost();
                BigDecimal totalCost = totalCost1.add(totalCost2);
                ReportMonitorBo monitorBo = new ReportMonitorBo();
                long totalClicks = bo1.getTotalClicks() + bo2.getTotalClicks();
                long totalImpressions = bo1.getTotalImpressions() + bo2.getTotalImpressions();
                monitorBo.setTotalCost(totalCost);
                monitorBo.setTotalClicks(totalClicks);
                monitorBo.setTotalImpressions(totalImpressions);
                return monitorBo;
            }));
        for (ReportMonitorBo sourceBo : sourceBoList) {
            if (sourceBo.getShopId() == null || !shopIdCampareBoMonitorBoMap.containsKey(sourceBo.getShopId())) {
                continue;
            }
            ReportMonitorBo compareBo = shopIdCampareBoMonitorBoMap.get(sourceBo.getShopId());
            diffList.add(countDiff(sourceBo, compareBo));
        }
        return MathUtil.avg(diffList);
    }

    /**
     * 计算单个差异
     *
     * @param sourceBo  sourceBo
     * @param compareBo compareBo
     * @return 差异值
     */
    private BigDecimal countDiff(ReportMonitorBo sourceBo, ReportMonitorBo compareBo) {
        if (Objects.isNull(sourceBo) || Objects.isNull(compareBo)) {
            return BigDecimal.ZERO;
        }
        //1、计算目标监控指标总值
        BigDecimal compareCost = Objects.isNull(compareBo.getTotalCost()) ? BigDecimal.ZERO : compareBo.getTotalCost();
        BigDecimal compareClicks = new BigDecimal(compareBo.getTotalClicks());
        BigDecimal compareImpressions = new BigDecimal(compareBo.getTotalImpressions());

        BigDecimal divB1 = BigDecimal.ZERO;
        if (Objects.nonNull(sourceBo.getTotalCost()) && sourceBo.getTotalCost().compareTo(BigDecimal.ZERO) != 0) {
            divB1 = MathUtil.divide(compareCost, sourceBo.getTotalCost(), DEF_DIV_DIFF);
        }
        BigDecimal divB2 = BigDecimal.ZERO;
        if (sourceBo.getTotalClicks() != 0) {
            divB2 = MathUtil.divide(compareClicks, new BigDecimal(sourceBo.getTotalClicks()), DEF_DIV_DIFF);
        }
        BigDecimal divB3 = BigDecimal.ZERO;
        if (sourceBo.getTotalImpressions() != 0) {
            divB3 = MathUtil.divide(compareImpressions, new BigDecimal(sourceBo.getTotalImpressions()), DEF_DIV_DIFF);
        }

        // 计算 (b1/b + b2/b + b3/b)/3
        BigDecimal avg = MathUtil.divide(divB1.add(divB2).add(divB3), new BigDecimal(3), DEF_DIV_DIFF);

        // 计算最终差异值 1 - (b1/b + b2/b + b3/b)/3
        return BigDecimal.ONE.subtract(avg);
    }
}
