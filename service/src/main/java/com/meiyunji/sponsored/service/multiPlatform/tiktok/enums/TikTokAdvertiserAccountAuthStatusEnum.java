package com.meiyunji.sponsored.service.multiPlatform.tiktok.enums;

import lombok.Getter;

/**
 * @author: liwei<PERSON>
 * @email: <EMAIL>
 * @date: 2025-05-19  14:21
 */
@Getter
public enum TikTokAdvertiserAccountAuthStatusEnum {
    NO_AD_AUTH(0, "未授权"),

    AD_AUTH(1, "已授权")
            ;
    private Integer status;
    private String msg;

    TikTokAdvertiserAccountAuthStatusEnum(Integer status, String msg) {
        this.status = status;
        this.msg = msg;
    }
}
