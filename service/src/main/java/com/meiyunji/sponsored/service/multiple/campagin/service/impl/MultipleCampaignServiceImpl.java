package com.meiyunji.sponsored.service.multiple.campagin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.amazon.advertising.mode.Adjustment;
import com.amazon.advertising.mode.PredicateEnum;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeChartRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.adTagSystem.enums.AdManageTagTypeEnum;
import com.meiyunji.sponsored.service.adTagSystem.service.impl.AdManageTagRelationService;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDao;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.budgetUsage.AmazonAdBudgetUsageService;
import com.meiyunji.sponsored.service.budgetUsage.entity.AmazonAdBudgetUsage;
import com.meiyunji.sponsored.service.config.nacos.AdManageLimitConfig;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdOperationLogBO;
import com.meiyunji.sponsored.service.cpc.constants.AdManagePageExportTaskTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AmazonAdOperationLogChangeTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AmazonAdOperationLogEntityTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdCampaignStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.impl.AdChartDataProcessNew;
import com.meiyunji.sponsored.service.cpc.service2.IAdManagePageExportTaskService;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdOperationLogService;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCommService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformanceNewDto;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.dao.IDwsSaleProfitShopDayDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdPortfolioDao;
import com.meiyunji.sponsored.service.doris.po.DwsSaleProfitShopDay;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.export.handler.CampaignMultiplePageExportTaskHandler;
import com.meiyunji.sponsored.service.log.po.OperationContent;
import com.meiyunji.sponsored.service.missBudget.entity.AmazonAdMissBudget;
import com.meiyunji.sponsored.service.missBudget.service.AmazonAdMissBudgetService;
import com.meiyunji.sponsored.service.monitor.SaveMonitor;
import com.meiyunji.sponsored.service.monitor.enums.MonitorPageFunctionEnum;
import com.meiyunji.sponsored.service.monitor.enums.MonitorTypeEnum;
import com.meiyunji.sponsored.service.multiple.campagin.dto.CampaignPageDto;
import com.meiyunji.sponsored.service.multiple.campagin.enums.CampaignOrderByEnum;
import com.meiyunji.sponsored.service.multiple.campagin.service.IMultipleCampaignService;
import com.meiyunji.sponsored.service.multiple.campagin.vo.*;
import com.meiyunji.sponsored.service.multiple.common.utils.MultipleUtils;
import com.meiyunji.sponsored.service.multiple.common.vo.CommonCompareReport;
import com.meiyunji.sponsored.service.multiple.common.vo.CommonCompareReportRate;
import com.meiyunji.sponsored.service.multiple.common.vo.CommonReport;
import com.meiyunji.sponsored.service.reportHour.vo.AdAnalysisAndCompareVo;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import com.meiyunji.sponsored.service.util.ZoneUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 多店铺广告活动层级实现类
 *
 * @author: zzh
 * @create: 2024-11-21 10:05
 */
@Service
@Slf4j
public class MultipleCampaignServiceImpl implements IMultipleCampaignService {

    @Resource
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;

    @Resource
    private IAmazonAdProductDao amazonAdProductDao;

    @Resource
    private IScVcShopAuthDao scVcShopAuthDao;

    @Resource
    private IAmazonAdCampaignAllDorisDao amazonAdCampaignAllDorisDao;

    @Resource
    private IDwsSaleProfitShopDayDao dwsSaleProfitShopDayDao;

    @Resource
    private IAmazonAdPortfolioDao portfolioDao;

    @Resource
    private IOdsAmazonAdPortfolioDao odsAmazonAdPortfolioDao;

    @Resource
    private IUserDao userDao;

    @Resource
    private CpcCommService cpcCommService;

    @Resource
    private IAmazonAdOperationLogService amazonAdOperationLogService;

    @Resource
    private StringRedisService stringRedisService;

    @Resource
    private AmazonAdBudgetUsageService amazonAdBudgetUsageService;

    @Resource
    private AmazonAdMissBudgetService amazonAdMissBudgetService;

    @Resource
    private IAmazonSbAdsDao amazonSbAdsDao;

    @Resource
    private CpcPageIdsHandler cpcPageIdsHandler;

    @Resource
    private AdChartDataProcessNew adChartDataProcessNew;

    @Resource
    private IAdManagePageExportTaskService adManagePageExportTaskService;

    @Resource
    private AdManageTagRelationService adManageTagRelationService;

    @Resource
    private AdManageLimitConfig adCampaignLimitConfig;

    @Resource
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;

    @Resource
    private ThreadPoolTaskExecutor adExportExecutor;

    @Resource
    private ThreadPoolTaskExecutor campaignAdPageExecutor;

    @Override
    @SaveMonitor(type = MonitorTypeEnum.LIST, tableName = MonitorPageFunctionEnum.CAMPAIGN, monitorAnalysis = false)
    public Page<MultipleCampaignPageVo> getAllCampaignData(CampaignPageParam param, Boolean export) {
        // 参数校验
        checkParam(param);
        // 参数赋值
        setParam(param);
        // 前置过滤获取campaignId
        if (getQueryIdsByPageFilter(param.getPuid(), param)) {
            log.info("前置过滤获取campaignId为空过滤");
            return new Page<>(param.getPageNo(), param.getPageSize());
        }
        // 高级筛选只查询个数
        if (param.getOnlyCount() != null && param.getOnlyCount()) {
            int count = amazonAdCampaignAllDorisDao.listAmazonAdCampaignAllCountMultiple(param.getPuid(), param);
            return new Page<>(param.getPageNo(), param.getPageSize(), 0, count);
        }
        // 分页获取数据--这一步只获取活动id
        long t = Instant.now().toEpochMilli();
        Page<AmazonAdCampaignDorisAllReport> campaignIdPage = amazonAdCampaignAllDorisDao.listAmazonAdCampaignAllPageMultiple(param.getPuid(), param);
        log.info("广告管理--广告活动列表页sql调用花费时间 {}", (Instant.now().toEpochMilli() - t));
        if (CollectionUtils.isEmpty(campaignIdPage.getRows())) {
            return new Page<>(param.getPageNo(), param.getPageSize());
        }
        // 组装响应数据
        return buildCampaignPageVo(param, campaignIdPage, export);
    }

    @Override
    @SaveMonitor(type = MonitorTypeEnum.SUM, tableName = MonitorPageFunctionEnum.CAMPAIGN, monitorAnalysis = false)
    public MultipleCampaignAggregateVo getAllCampaignAggregateData(CampaignPageParam param) {
        // 参数校验
        checkParam(param);
        // 参数赋值
        setParam(param);
        // 前置过滤获取campaignId
        if (getQueryIdsByPageFilter(param.getPuid(), param)) {
            param.setCampaignId("-1"); // 赋值-1
        }
        MultipleCampaignAggregateVo aggregateVo = new MultipleCampaignAggregateVo();
        // 统计报告数量 超过限制不统计，否则会导致doris cpu过高
        Integer count = amazonAdCampaignAllDorisDao.countReport(param);
        if(count >= adCampaignLimitConfig.getAggregateLimit()){
            aggregateVo.setOverLimit(true);
            return aggregateVo;
        }
        // 统计对比报告数量 超过限制不统计，否则会导致doris cpu过高
        if (param.getIsCompare() && StringUtils.isNotBlank(param.getCompareStartDate()) && StringUtils.isNotBlank(param.getCompareEndDate())) {
            CampaignPageParam compareParam = new CampaignPageParam();
            BeanUtils.copyProperties(param, compareParam);
            compareParam.setStartDate(param.getCompareStartDate());
            compareParam.setEndDate(param.getCompareEndDate());
            count = amazonAdCampaignAllDorisDao.countReport(compareParam);
            if(count >= adCampaignLimitConfig.getAggregateLimit()){
                aggregateVo.setOverLimit(true);
                return aggregateVo;
            }
        }
        // 每日预算汇总数据
        BigDecimal sumDailyBudget = amazonAdCampaignAllDorisDao.getSumDailyBudget(param);
        aggregateVo.setDailyBudgetSum(sumDailyBudget == null ? null : sumDailyBudget.toString());
        // 组装图表数据
        buildChartData(param, aggregateVo);
        // 组装汇总数据
        buildReport(param, aggregateVo);
        return aggregateVo;
    }

    /**
     * 多店铺广告活动异步导出
     * 异步导出方法入口:{@link CampaignMultiplePageExportTaskHandler#export}
     */
    @Override
    public Boolean exportCampaignDetails(CampaignPageParam param) {
        // 参数校验
        checkParam(param);
        // 异步导出,插入任务
        Long id = adManagePageExportTaskService.saveExportTask(param.getPuid(), param.getUid(), 0,
                AdManagePageExportTaskTypeEnum.CAMPAIGN_MULTIPLE, param.getStartDate().replace("-", ""), param.getEndDate().replace("-", ""), param);
        return id != null;
    }

    /**
     * 组装汇总数据
     */
    private void buildReport(CampaignPageParam param, MultipleCampaignAggregateVo aggregateVo) {
        CommonCompareReportRate report = new CommonCompareReportRate();
        // 构建报告指标数据
        CpcCommPageNewVo campaignVo = buildCampaignVo(param, false);
        // 填充报告数据
        buildCampaignReport(report, campaignVo);
        // 环比数据
        if (param.getIsCompare()) {
            // 获取环比报告数据
            CpcCommPageNewVo compareVo = buildCampaignVo(param, true);
            // 填充环比值
            buildCompareValue(report, compareVo);
            // 填充环比增长率
            buildCompareRate(report);
        }
        // 报告字段特殊处理逻辑
        buildReportExtra(param, true, null, report);
        aggregateVo.setReport(report);
    }

    /**
     * 构建报告指标数据
     */
    private CpcCommPageNewVo buildCampaignVo(CampaignPageParam param, boolean selCompareDate) {
        // 查询统计数据
        AmazonAdCampaignDorisSumReport sumReport = amazonAdCampaignAllDorisDao.getSumReport(param, selCompareDate);
        // 查询店铺汇总数据
        BigDecimal shopSales;
        if (selCompareDate) {
            shopSales = dwsSaleProfitShopDayDao.sumShopSaleByDateRange(param.getPuid(), param.getShopIdList(), param.getCompareStartDate(), param.getCompareEndDate(), param.getChangeRate());
        } else {
            shopSales = dwsSaleProfitShopDayDao.sumShopSaleByDateRange(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate(), param.getChangeRate());
        }
        // 店铺销售额
        ShopSaleDto shopSaleDto = new ShopSaleDto();
        shopSaleDto.setSumRange(shopSales);
        // 填充汇总数据
        CpcCommPageNewVo campaignVo = new CpcCommPageNewVo();
        campaignVo.setSumCost(sumReport.getCost() == null ? BigDecimal.ZERO : sumReport.getCost());
        campaignVo.setSumAdSale(sumReport.getTotalSales() == null ? BigDecimal.ZERO : sumReport.getTotalSales());
        campaignVo.setSumAdOrderNum(sumReport.getOrderNum() == null ? BigDecimal.ZERO : BigDecimal.valueOf(sumReport.getOrderNum()));
        campaignVo.setSumOrderNum(sumReport.getSaleNum() == null ? BigDecimal.ZERO : BigDecimal.valueOf(sumReport.getSaleNum()));
        // 转公用报告基础数据对象
        ReportNewBase reportBase = buildReportBase(sumReport, null);
        // 使用公共方法填充报告基础指标+计算指标数据
        cpcCommService.fillReportDataIntoPageNewVo(campaignVo, reportBase, shopSaleDto);
        // 汇总数据特殊处理逻辑
        buildSumReportExtra(campaignVo, sumReport);
        return campaignVo;
    }

    /**
     * 汇总数据特殊处理逻辑
     */
    private void buildSumReportExtra(CpcCommPageNewVo campaignVo, AmazonAdCampaignDorisSumReport sumReport) {
        // 本广告产品销量 = sp广告销量
        campaignVo.setAdSelfSaleNum(sumReport.getSpAdSaleNum());
        // 其他广告产品销量 = 总广告销量 - 本产品广告销量 - sb广告销量 - sd广告销量
        campaignVo.setAdOtherSaleNum(sumReport.getSaleNum() - sumReport.getSpAdSaleNum() - sumReport.getSbSaleNum() - sumReport.getSdSaleNum());
        // vcpm =（sb可见展示次数不为0的广告花费+sd可见展示次数不为0的广告花费)/(sb可见展示次数+sd可见展示次数)*1000
        Long sumVcpmViewImpressions = sumReport.getSbViewImpressions() + sumReport.getSdViewImpressions();
        BigDecimal sumVcpmAdcost = (sumReport.getSbViewImpressions() == 0 ? BigDecimal.ZERO : sumReport.getSbCost()).
                add((sumReport.getSdViewImpressions() == 0 ? BigDecimal.ZERO : sumReport.getSdCost()));
        BigDecimal vcpm = sumVcpmViewImpressions == 0 ? BigDecimal.ZERO : MathUtil.multiplyZero(MathUtil.divideForScale(sumVcpmAdcost, new BigDecimal(sumVcpmViewImpressions), 6), BigDecimal.valueOf(1000));
        campaignVo.setVcpm(vcpm.toString());
    }

    /**
     * 构建图表数据
     */
    private void buildChartData(CampaignPageParam param, MultipleCampaignAggregateVo aggregateVo) {
        // 店铺销售额
        BigDecimal shopSales = dwsSaleProfitShopDayDao.sumShopSaleByDateRange(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate(), param.getChangeRate());
        // 根据pageSign缓存活动id集合，小时级汇总数据取去，不需要再查询一遍
        filterAndSaveIdTemporary(param.getPageSign(), param.getPuid(), param.getShopIdList(), param);
        // 查询图表数据
        List<AdHomePerformanceNewDto> reportDayList = amazonAdCampaignAllDorisDao.listTotalGroupDateByIdMultiple(param.getPuid(), param);
        // 获取币种
        String currency = MultipleUtils.getCurrency(param.getShopAuthList());
        // 日期格式转换
        for (AdHomePerformanceNewDto dto : reportDayList) {
            dto.setCountDate(DateUtil.dateStringFormat(dto.getCountDate(), DateUtil.PATTERN, DateUtil.PATTERN_YYYYMMDD));
        }
        boolean isVc = param.getShopAuthList().stream().anyMatch(e -> ShopTypeEnum.VC.getCode().equals(e.getType()));
        // 获取chart数据
        List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcessNew.getDayPerformanceVos(currency, reportDayList, shopSales, isVc);
        List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcessNew.getWeekPerformanceVos(currency, param.getStartDate(), param.getEndDate(), reportDayList, shopSales, isVc);
        List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcessNew.getMonthPerformanceVos(currency, reportDayList, shopSales, isVc);
        aggregateVo.setDay(dayPerformanceVos);
        aggregateVo.setWeek(weekPerformanceVos);
        aggregateVo.setMonth(monthPerformanceVos);
        aggregateVo.setCurrency(currency);
    }

    /**
     * 根据pageSign缓存活动id集合，小时级汇总数据取去使用，不需要再查询一遍
     */
    private void filterAndSaveIdTemporary(String pageSign, Integer puid, List<Integer> shopIdList, CampaignPageParam param) {
        // 需要将得到的Id进行过滤，过滤掉近60天没有报告的活动Id
        ThreadPoolUtil.getCpcAggregateIdsSyncPool().execute(() -> {
            List<String> validRecordIdList = new ArrayList<>();
            try {
                validRecordIdList = amazonAdCampaignAllDorisDao.getValidRecordByDate(puid, shopIdList
                        , LocalDate.now().minusDays(63).format(DateTimeFormatter.BASIC_ISO_DATE), param);
            } catch (Exception e) {
                log.error("query valid campaign id error, puid:{}, shopIdList:{}, pageSign:{}, e:{}", puid, shopIdList, pageSign, e.getMessage());
            }
            cpcPageIdsHandler.addIdsTemporarySynchronize(puid, validRecordIdList, pageSign, "");
        });
    }

    /**
     * 组装响应数据
     */
    private Page<MultipleCampaignPageVo> buildCampaignPageVo(CampaignPageParam param, Page<AmazonAdCampaignDorisAllReport> campaignIdPage, Boolean export) {
        List<MultipleCampaignPageVo> voList = new ArrayList<>();
        // 数据准备
        CampaignPageDto dto = prepareData(param, campaignIdPage, export);
        for (AmazonAdCampaignDorisAllReport record : campaignIdPage.getRows()) {
            MultipleCampaignPageVo vo = new MultipleCampaignPageVo();
            // 组装活动基础信息
            buildCampaignBaseInfo(param, record.getCampaignId(), dto, vo);
            // 组装活动额外信息
            buildCampaignExtraInfo(dto, vo, export);
            // 组装报告数据
            buildCampaignReport(param, dto, vo, export);
            voList.add(vo);
        }
        return new Page<>(param.getPageNo(), param.getPageSize(), campaignIdPage.getTotalPage(), campaignIdPage.getTotalSize(), voList);
    }

    /**
     * 组装报告数据
     */
    private void buildCampaignReport(CampaignPageParam param, CampaignPageDto dto, MultipleCampaignPageVo vo, Boolean export) {
        // 统计指标数据
        CpcCommPageNewVo campaignVo = getCampaignPageVo(vo, dto.getReportMap(), dto.getShopSalesMap(), dto.getAdMetricDto());
        // 填充报告数据
        buildCampaignReport(vo, campaignVo);
        // 列表查询切勾选对比才需要构建对比数据、导出不需要
        if (param.getIsCompare() && !export) {
            // 统计对比指标数据
            CpcCommPageNewVo compareVo = getCampaignPageVo(vo, dto.getCompareReportMap(), dto.getCompareShopSalesMap(), null);
            // 填充对比值
            buildCompareValue(vo, compareVo);
            // 填充对比增长率
            buildCompareRate(vo);
        }
        // 报告字段特殊处理逻辑
        buildReportExtra(param, false, vo.getCostType(), vo);

        ShopAuth shopAuth = param.getShopAuthMap().get(vo.getShopId());
        //有vc店铺不展示 ASOTS, ACOTS
        if (shopAuth != null && ShopTypeEnum.VC.getCode().equals(shopAuth.getType())) {
            vo.setAcots("-");
            vo.setCompareAcotsRate("-");
            vo.setCompareAcots("-");
            vo.setAsots("-");
            vo.setCompareAsotsRate("-");
            vo.setCompareAsots("-");
        }
    }

    /**
     * 报告字段特殊处理逻辑
     */
    private <E extends CommonCompareReportRate> void buildReportExtra(CampaignPageParam param, Boolean sum, String costType, E vo) {
        // vcpm值特殊处理
        if (!SBCampaignCostTypeEnum.VCPM.getCode().equals(costType) && !sum) {
            vo.setVcpm("-");
            if (param.getIsCompare()) {
                vo.setCompareVcpmRate("-");
            }
        }
        // 多币种百分比字段展示“-”
        if (param.getChangeRate()) {
            vo.setAdCostPercentage("-");
            vo.setAdSalePercentage("-");
        }
    }

    /**
     * 统计指标数据
     */
    private CpcCommPageNewVo getCampaignPageVo(MultipleCampaignPageVo vo, Map<String, AmazonAdCampaignDorisSumReport> reportMap,
                                             Map<Integer, BigDecimal> shopSalesMap, AdMetricDto adMetricDto) {
        AmazonAdCampaignDorisSumReport report = reportMap.get(vo.getCampaignId());
        if (report == null) {
            report = buildDefaultReport(vo.getCampaignId());
        }
        BigDecimal shopSale = shopSalesMap.get(vo.getShopId());
        CpcCommPageNewVo campaignVo = new CpcCommPageNewVo();
        // 填充汇总数据
        filterAdMetricData(adMetricDto, campaignVo);
        // 转公用报告基础数据对象
        ReportNewBase reportBase = buildReportBase(report, vo.getType());
        // 店铺销售额
        ShopSaleDto shopSaleDto = new ShopSaleDto();
        shopSaleDto.setSumRange(shopSale == null ? BigDecimal.ZERO : shopSale);
        // 使用公共方法填充报告基础指标+计算指标数据
        cpcCommService.fillReportDataIntoPageNewVo(campaignVo, reportBase, shopSaleDto);
        if (CampaignTypeEnum.sb.getCampaignType().equals(vo.getType()) || CampaignTypeEnum.sd.getCampaignType().equals(vo.getType())) {
            campaignVo.setAdSelfSaleNum(0L);
            campaignVo.setAdOtherSaleNum(0L);
        }
        return campaignVo;
    }

    private AmazonAdCampaignDorisSumReport buildDefaultReport(String campaignId) {
        AmazonAdCampaignDorisSumReport report = new AmazonAdCampaignDorisSumReport();
        report.setCampaignId(campaignId);
        report.setCost(BigDecimal.ZERO);
        report.setTotalSales(BigDecimal.ZERO);
        report.setAdSales(BigDecimal.ZERO);
        report.setImpressions(0L);
        report.setOrderNum(0L);
        report.setClicks(0L);
        report.setAdOrderNum(0L);
        report.setSaleNum(0L);
        report.setAdSaleNum(0L);
        report.setViewImpressions(0L);
        report.setOrdersNewToBrand(0L);
        report.setSalesNewToBrand(BigDecimal.ZERO);
        report.setUnitsOrderedNewToBrand(0L);
        report.setNewToBrandDetailPageViews(0L);
        report.setAddToCart(0L);
        report.setVideoFirstQuartileViews(0L);
        report.setVideo5secondViews(0L);
        report.setVideoMidpointViews(0L);
        report.setVideoThirdQuartileViews(0L);
        report.setVideoCompleteViews(0L);
        report.setVideoUnmutes(0L);
        report.setBrandedSearches(0L);
        report.setDetailPageView(0L);
        report.setCumulativeReach(0L);
        report.setImpressionsFrequencyAverage(BigDecimal.ZERO);
        return report;
    }

    /**
     * 填充报告数据
     */
    private <e extends CommonReport> void buildCampaignReport(e vo, CpcCommPageNewVo campaignVo) {
        vo.setImpressions(Optional.ofNullable(campaignVo.getImpressions()).orElse(0L));
        vo.setClicks(Optional.ofNullable(campaignVo.getClicks()).orElse(0L));
        vo.setAdOrderNum(Optional.ofNullable(campaignVo.getAdOrderNum()).orElse(0L));
        vo.setAdCostPerClick(StringUtils.isNotBlank(campaignVo.getAdCostPerClick()) ? campaignVo.getAdCostPerClick() : "0");
        vo.setCtr(StringUtils.isNotBlank(campaignVo.getCtr()) ? campaignVo.getCtr() : "0");
        vo.setCvr(StringUtils.isNotBlank(campaignVo.getCvr()) ? campaignVo.getCvr() : "0");
        vo.setAcos(StringUtils.isNotBlank(campaignVo.getAcos()) ? campaignVo.getAcos() : "0");
        vo.setRoas(StringUtils.isNotBlank(campaignVo.getRoas()) ? campaignVo.getRoas() : "0");
        vo.setAdCost(StringUtils.isNotBlank(campaignVo.getAdCost()) ? campaignVo.getAdCost() : "0");
        vo.setAcots(StringUtils.isNotBlank(campaignVo.getAcots()) ? campaignVo.getAcots() : "0");
        vo.setAsots(StringUtils.isNotBlank(campaignVo.getAsots()) ? campaignVo.getAsots() : "0");
        vo.setAdSale(StringUtils.isNotBlank(campaignVo.getAdSale()) ? campaignVo.getAdSale() : "0");
        vo.setViewImpressions(Optional.ofNullable(campaignVo.getViewImpressions()).orElse(0L));
        vo.setCpa(StringUtils.isNotBlank(campaignVo.getCpa()) ? campaignVo.getCpa() : "0");
        vo.setVcpm(StringUtils.isNotBlank(campaignVo.getVcpm()) ? campaignVo.getVcpm() : "0.00");
        vo.setAdSelfSaleNum(Optional.ofNullable(campaignVo.getAdSelfSaleNum()).orElse(0L));
        vo.setAdOtherSaleNum(Optional.ofNullable(campaignVo.getAdOtherSaleNum()).orElse(0L));
        vo.setAdSaleNum(Optional.ofNullable(campaignVo.getAdSaleNum()).orElse(0L));
        vo.setAdSaleNum(Optional.ofNullable(campaignVo.getAdSaleNum()).orElse(0L));
        vo.setAdOtherOrderNum(Optional.ofNullable(campaignVo.getAdOtherOrderNum()).orElse(0L));
        vo.setAdSales(StringUtils.isNotBlank(campaignVo.getAdSales()) ? campaignVo.getAdSales() : "0");
        vo.setAdOtherSales(StringUtils.isNotBlank(campaignVo.getAdOtherSales()) ? campaignVo.getAdOtherSales() : "0");
        vo.setOrderNum(Optional.ofNullable(campaignVo.getOrderNum()).orElse(0L));
        vo.setOrdersNewToBrandFTD(Optional.ofNullable(campaignVo.getOrdersNewToBrandFTD()).orElse(0L));
        vo.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(campaignVo.getOrderRateNewToBrandFTD()) ? campaignVo.getOrderRateNewToBrandFTD() : "0");
        vo.setSalesNewToBrandFTD(StringUtils.isNotBlank(campaignVo.getSalesNewToBrandFTD()) ? campaignVo.getSalesNewToBrandFTD() : "0");
        vo.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(campaignVo.getSalesRateNewToBrandFTD()) ? campaignVo.getSalesRateNewToBrandFTD() : "0");
        vo.setUnitsOrderedNewToBrandFTD(Optional.ofNullable(campaignVo.getUnitsOrderedNewToBrandFTD()).orElse(0L));
        vo.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(campaignVo.getUnitsOrderedRateNewToBrandFTD()) ? campaignVo.getUnitsOrderedRateNewToBrandFTD() : "0");
        vo.setAdCostPercentage(StringUtils.isNotBlank(campaignVo.getAdCostPercentage()) ? campaignVo.getAdCostPercentage() : "0");
        vo.setAdSalePercentage(StringUtils.isNotBlank(campaignVo.getAdSalePercentage()) ? campaignVo.getAdSalePercentage() : "0");
        vo.setAdOrderNumPercentage(StringUtils.isNotBlank(campaignVo.getAdOrderNumPercentage()) ? campaignVo.getAdOrderNumPercentage() : "0");
        vo.setOrderNumPercentage(StringUtils.isNotBlank(campaignVo.getOrderNumPercentage()) ? campaignVo.getOrderNumPercentage() : "0");
        vo.setNewToBrandDetailPageViews(Optional.ofNullable(campaignVo.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
        vo.setAddToCart(Optional.ofNullable(campaignVo.getAddToCart()).map(String::valueOf).orElse("0"));
        vo.setAddToCartRate(Optional.ofNullable(campaignVo.getAddToCartRate()).map(String::valueOf).orElse("0"));
        vo.setECPAddToCart(Optional.ofNullable(campaignVo.getECPAddToCart()).map(String::valueOf).orElse("0"));
        vo.setVideo5SecondViews(Optional.ofNullable(campaignVo.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
        vo.setVideo5SecondViewRate(Optional.ofNullable(campaignVo.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
        vo.setVideoFirstQuartileViews(Optional.ofNullable(campaignVo.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
        vo.setVideoMidpointViews(Optional.ofNullable(campaignVo.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
        vo.setVideoThirdQuartileViews(Optional.ofNullable(campaignVo.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
        vo.setVideoCompleteViews(Optional.ofNullable(campaignVo.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
        vo.setVideoUnmutes(Optional.ofNullable(campaignVo.getVideoUnmutes()).map(String::valueOf).orElse("0"));
        vo.setViewabilityRate(Optional.ofNullable(campaignVo.getViewabilityRate()).map(String::valueOf).orElse("0"));
        vo.setViewClickThroughRate(Optional.ofNullable(campaignVo.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
        vo.setBrandedSearches(Optional.ofNullable(campaignVo.getBrandedSearches()).map(String::valueOf).orElse("0"));
        vo.setDetailPageViews(Optional.ofNullable(campaignVo.getDetailPageViews()).map(String::valueOf).orElse("0"));
        vo.setCumulativeReach(Optional.ofNullable(campaignVo.getCumulativeReach()).map(String::valueOf).orElse("0"));
        vo.setImpressionsFrequencyAverage(Optional.ofNullable(campaignVo.getImpressionsFrequencyAverage()).map(it -> it.setScale(2, RoundingMode.HALF_UP).toString()).orElse("0"));
        vo.setAdvertisingUnitPrice(Optional.ofNullable(campaignVo.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
        vo.setTopImpressionShare(Optional.ofNullable(campaignVo.getTopImpressionShare()).orElse("-"));
    }

    /**
     * 填充环比增长率
     */
    private <E extends CommonCompareReportRate> void buildCompareRate(E vo) {
        vo.setCompareImpressionsRate(AdAnalysisAndCompareVo.calculateCompareRete(BigDecimal.valueOf(vo.getImpressions()), BigDecimal.valueOf(vo.getCompareImpressions())).toString());
        vo.setCompareClicksRate(AdAnalysisAndCompareVo.calculateCompareRete(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(vo.getCompareClicks())).toString());
        vo.setCompareCtrRate(AdAnalysisAndCompareVo.calculateCompareRete(new BigDecimal(vo.getCtr()), new BigDecimal(vo.getCompareCtr())).toString());
        vo.setCompareCvrRate(AdAnalysisAndCompareVo.calculateCompareRete(new BigDecimal(vo.getCvr()), new BigDecimal(vo.getCompareCvr())).toString());
        vo.setCompareAcosRate(AdAnalysisAndCompareVo.calculateCompareRete(new BigDecimal(vo.getAcos()), new BigDecimal(vo.getCompareAcos())).toString());
        vo.setCompareRoasRate(AdAnalysisAndCompareVo.calculateCompareRete(new BigDecimal(vo.getRoas()), new BigDecimal(vo.getCompareRoas())).toString());
        vo.setCompareAcotsRate(AdAnalysisAndCompareVo.calculateCompareRete(new BigDecimal(vo.getAcots()), new BigDecimal(vo.getCompareAcots())).toString());
        vo.setCompareAsotsRate(AdAnalysisAndCompareVo.calculateCompareRete(new BigDecimal(vo.getAsots()), new BigDecimal(vo.getCompareAsots())).toString());
        vo.setCompareAdOrderNumRate(AdAnalysisAndCompareVo.calculateCompareRete(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(vo.getCompareAdOrderNum())).toString());
        vo.setCompareAdCostRate(AdAnalysisAndCompareVo.calculateCompareRete(new BigDecimal(vo.getAdCost()), new BigDecimal(vo.getCompareAdCost())).toString());
        vo.setCompareAdCostPerClickRate(AdAnalysisAndCompareVo.calculateCompareRete(new BigDecimal(vo.getAdCostPerClick()), new BigDecimal(vo.getCompareAdCostPerClick())).toString());
        vo.setCompareAdSaleRate(AdAnalysisAndCompareVo.calculateCompareRete(new BigDecimal(vo.getAdSale()), new BigDecimal(vo.getCompareAdSale())).toString());
        vo.setCompareViewImpressionsRate(AdAnalysisAndCompareVo.calculateCompareRete(BigDecimal.valueOf(vo.getViewImpressions()), BigDecimal.valueOf(Integer.parseInt(vo.getCompareViewImpressions()))).toString());
        vo.setCompareCpaRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getCpa()), new BigDecimal(vo.getCompareCpa())));
        vo.setCompareVcpmRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getVcpm()), new BigDecimal(vo.getCompareVcpm())));
        vo.setCompareAdSaleNumRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdSaleNum()), new BigDecimal(vo.getCompareAdSaleNum())));
        vo.setCompareAdOtherOrderNumRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdOtherOrderNum()), new BigDecimal(vo.getCompareAdOtherOrderNum())));
        vo.setCompareAdSalesRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdSales()), new BigDecimal(vo.getCompareAdSales())));
        vo.setCompareAdOtherSalesRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdOtherSales()), new BigDecimal(vo.getCompareAdOtherSales())));
        vo.setCompareOrderNumRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getOrderNum()), new BigDecimal(vo.getCompareOrderNum())));
        vo.setCompareAdSelfSaleNumRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdSelfSaleNum()), new BigDecimal(vo.getCompareAdSelfSaleNum())));
        vo.setCompareAdOtherSaleNumRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdOtherSaleNum()), new BigDecimal(vo.getCompareAdOtherSaleNum())));
        vo.setCompareOrdersNewToBrandFTDRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getOrdersNewToBrandFTD()), new BigDecimal(vo.getCompareOrdersNewToBrandFTD())));
        vo.setCompareOrderRateNewToBrandFTDRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getOrderRateNewToBrandFTD()), new BigDecimal(vo.getCompareOrderRateNewToBrandFTD())));
        vo.setCompareSalesNewToBrandFTDRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getSalesNewToBrandFTD()), new BigDecimal(vo.getCompareSalesNewToBrandFTD())));
        vo.setCompareSalesRateNewToBrandFTDRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getSalesRateNewToBrandFTD()), new BigDecimal(vo.getCompareSalesRateNewToBrandFTD())));
        vo.setCompareUnitsOrderedNewToBrandFTDRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getUnitsOrderedNewToBrandFTD()), new BigDecimal(vo.getCompareUnitsOrderedNewToBrandFTD())));
        vo.setCompareUnitsOrderedRateNewToBrandFTDRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getUnitsOrderedRateNewToBrandFTD()), new BigDecimal(vo.getCompareUnitsOrderedRateNewToBrandFTD())));
        vo.setCompareNewToBrandDetailPageViewsRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getNewToBrandDetailPageViews()), new BigDecimal(vo.getCompareNewToBrandDetailPageViews())));
        vo.setCompareAdCostPercentageRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdCostPercentage()), new BigDecimal(vo.getCompareAdCostPercentage())));
        vo.setCompareAdSalePercentageRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdSalePercentage()), new BigDecimal(vo.getCompareAdSalePercentage())));
        vo.setCompareAdOrderNumPercentageRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdOrderNumPercentage()), new BigDecimal(vo.getCompareAdOrderNumPercentage())));
        vo.setCompareOrderNumPercentageRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getOrderNumPercentage()), new BigDecimal(vo.getCompareOrderNumPercentage())));
        vo.setCompareAddToCartRates(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAddToCart()), new BigDecimal(vo.getCompareAddToCart())));
        vo.setCompareAddToCartRateRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAddToCartRate()), new BigDecimal(vo.getCompareAddToCartRate())));
        vo.setCompareECPAddToCartRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getECPAddToCart()), new BigDecimal(vo.getCompareECPAddToCart())));
        vo.setCompareVideo5SecondViewsRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getVideo5SecondViews()), new BigDecimal(vo.getCompareVideo5SecondViews())));
        vo.setCompareVideo5SecondViewRateRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getVideo5SecondViewRate()), new BigDecimal(vo.getCompareVideo5SecondViewRate())));
        vo.setCompareVideoFirstQuartileViewsRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getVideoFirstQuartileViews()), new BigDecimal(vo.getCompareVideoFirstQuartileViews())));
        vo.setCompareVideoMidpointViewsRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getVideoMidpointViews()), new BigDecimal(vo.getCompareVideoMidpointViews())));
        vo.setCompareVideoThirdQuartileViewsRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getVideoThirdQuartileViews()), new BigDecimal(vo.getCompareVideoThirdQuartileViews())));
        vo.setCompareVideoCompleteViewsRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getVideoCompleteViews()), new BigDecimal(vo.getCompareVideoCompleteViews())));
        vo.setCompareVideoUnmutesRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getVideoUnmutes()), new BigDecimal(vo.getCompareVideoUnmutes())));
        vo.setCompareViewabilityRateRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getViewabilityRate()), new BigDecimal(vo.getCompareViewabilityRate())));
        vo.setCompareViewClickThroughRateRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getViewClickThroughRate()), new BigDecimal(vo.getCompareViewClickThroughRate())));
        vo.setCompareBrandedSearchesRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getBrandedSearches()), new BigDecimal(vo.getCompareBrandedSearches())));
        vo.setCompareImpressionsFrequencyAverageRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getImpressionsFrequencyAverage()), new BigDecimal(vo.getCompareImpressionsFrequencyAverage())));
        vo.setCompareAdvertisingUnitPriceRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdvertisingUnitPrice()), new BigDecimal(vo.getCompareAdvertisingUnitPrice())));
        vo.setCompareDetailPageViewsRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getDetailPageViews()), new BigDecimal(vo.getCompareDetailPageViews())));
    }

    /**
     * 填充环比值
     */
    private <E extends CommonCompareReport> void buildCompareValue(E vo, CpcCommPageNewVo compareVo) {
        vo.setCompareImpressions(Optional.ofNullable(compareVo.getImpressions()).orElse(0L));
        vo.setCompareClicks(Optional.ofNullable(compareVo.getClicks()).orElse(0L));
        vo.setCompareCtr(StringUtils.isNotBlank(compareVo.getCtr()) ? compareVo.getCtr() : "0");
        vo.setCompareCvr(StringUtils.isNotBlank(compareVo.getCvr()) ? compareVo.getCvr() : "0");
        vo.setCompareAcos(StringUtils.isNotBlank(compareVo.getAcos()) ? compareVo.getAcos() : "0");
        vo.setCompareRoas(StringUtils.isNotBlank(compareVo.getRoas()) ? compareVo.getRoas() : "0");
        vo.setCompareAcots(StringUtils.isNotBlank(compareVo.getAcots()) ? compareVo.getAcots() : "0");
        vo.setCompareAsots(StringUtils.isNotBlank(compareVo.getAsots()) ? compareVo.getAsots() : "0");
        vo.setCompareAdOrderNum(Optional.ofNullable(compareVo.getAdOrderNum()).orElse(0L));
        vo.setCompareAdCost(StringUtils.isNotBlank(compareVo.getAdCost()) ? compareVo.getAdCost() : "0");
        vo.setCompareAdCostPerClick(StringUtils.isNotBlank(compareVo.getAdCostPerClick()) ? compareVo.getAdCostPerClick() : "0");
        vo.setCompareAdSale(StringUtils.isNotBlank(compareVo.getAdSale()) ? compareVo.getAdSale() : "0");
        vo.setCompareViewImpressions(compareVo.getViewImpressions() == null ? "0" : String.valueOf(compareVo.getViewImpressions()));
        vo.setCompareCpa(StringUtils.isNotBlank(compareVo.getCpa()) ? compareVo.getCpa() : "0");
        vo.setCompareVcpm(StringUtils.isNotBlank(compareVo.getVcpm()) ? compareVo.getVcpm() : "0");
        vo.setCompareAdSaleNum(Optional.ofNullable(compareVo.getAdSaleNum()).orElse(0L));
        vo.setCompareAdOtherOrderNum(Optional.ofNullable(compareVo.getAdOtherOrderNum()).orElse(0L));
        vo.setCompareAdSales(StringUtils.isNotBlank(compareVo.getAdSales()) ? compareVo.getAdSales() : "0");
        vo.setCompareAdOtherSales(StringUtils.isNotBlank(compareVo.getAdOtherSales()) ? compareVo.getAdOtherSales() : "0");
        vo.setCompareOrderNum(Optional.ofNullable(compareVo.getOrderNum()).orElse(0L));
        vo.setCompareAdSelfSaleNum(Optional.ofNullable(compareVo.getAdSelfSaleNum()).orElse(0L));
        vo.setCompareAdOtherSaleNum(Optional.ofNullable(compareVo.getAdOtherSaleNum()).orElse(0L));
        vo.setCompareOrdersNewToBrandFTD(Optional.ofNullable(compareVo.getOrdersNewToBrandFTD()).orElse(0L));
        vo.setCompareOrderRateNewToBrandFTD(StringUtils.isNotBlank(compareVo.getOrderRateNewToBrandFTD()) ? compareVo.getOrderRateNewToBrandFTD() : "0");
        vo.setCompareSalesNewToBrandFTD(StringUtils.isNotBlank(compareVo.getSalesNewToBrandFTD()) ? compareVo.getSalesNewToBrandFTD() : "0");
        vo.setCompareSalesRateNewToBrandFTD(StringUtils.isNotBlank(compareVo.getSalesRateNewToBrandFTD()) ? compareVo.getSalesRateNewToBrandFTD() : "0");
        vo.setCompareUnitsOrderedNewToBrandFTD(Optional.ofNullable(compareVo.getUnitsOrderedNewToBrandFTD()).orElse(0L));
        vo.setCompareUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(compareVo.getUnitsOrderedRateNewToBrandFTD()) ? compareVo.getUnitsOrderedRateNewToBrandFTD() : "0");
        vo.setCompareNewToBrandDetailPageViews(Optional.ofNullable(compareVo.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
        vo.setCompareAdCostPercentage(StringUtils.isNotBlank(compareVo.getAdCostPercentage()) ? compareVo.getAdCostPercentage() : "0");
        vo.setCompareAdSalePercentage(StringUtils.isNotBlank(compareVo.getAdSalePercentage()) ? compareVo.getAdSalePercentage() : "0");
        vo.setCompareAdOrderNumPercentage(StringUtils.isNotBlank(compareVo.getAdOrderNumPercentage()) ? compareVo.getAdOrderNumPercentage() : "0");
        vo.setCompareOrderNumPercentage(StringUtils.isNotBlank(compareVo.getOrderNumPercentage()) ? compareVo.getOrderNumPercentage() : "0");
        vo.setCompareAddToCart(Optional.ofNullable(compareVo.getAddToCart()).map(String::valueOf).orElse("0"));
        vo.setCompareAddToCartRate(Optional.ofNullable(compareVo.getAddToCartRate()).map(String::valueOf).orElse("0"));
        vo.setCompareECPAddToCart(Optional.ofNullable(compareVo.getECPAddToCart()).map(String::valueOf).orElse("0"));
        vo.setCompareVideo5SecondViews(Optional.ofNullable(compareVo.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
        vo.setCompareVideo5SecondViewRate(Optional.ofNullable(compareVo.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
        vo.setCompareVideoFirstQuartileViews(Optional.ofNullable(compareVo.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
        vo.setCompareVideoMidpointViews(Optional.ofNullable(compareVo.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
        vo.setCompareVideoThirdQuartileViews(Optional.ofNullable(compareVo.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
        vo.setCompareVideoCompleteViews(Optional.ofNullable(compareVo.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
        vo.setCompareVideoUnmutes(Optional.ofNullable(compareVo.getVideoUnmutes()).map(String::valueOf).orElse("0"));
        vo.setCompareViewabilityRate(Optional.ofNullable(compareVo.getViewabilityRate()).map(String::valueOf).orElse("0"));
        vo.setCompareViewClickThroughRate(Optional.ofNullable(compareVo.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
        vo.setCompareBrandedSearches(Optional.ofNullable(compareVo.getBrandedSearches()).map(String::valueOf).orElse("0"));
        vo.setCompareImpressionsFrequencyAverage(Optional.ofNullable(compareVo.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
        vo.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareVo.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
        vo.setCompareDetailPageViews(Optional.ofNullable(compareVo.getDetailPageViews()).map(String::valueOf).orElse("0"));
    }

    /**
     * 转公用报告基础数据对象
     */
    private ReportNewBase buildReportBase(AmazonAdCampaignDorisSumReport amazonAdCampaignDorisAllReport, String type) {
        ReportNewBase reportBase = new ReportNewBase();
        reportBase.setCountDate("");
        reportBase.setCost(amazonAdCampaignDorisAllReport.getCost());
        reportBase.setTotalSales(amazonAdCampaignDorisAllReport.getTotalSales());
        reportBase.setAdSales(amazonAdCampaignDorisAllReport.getAdSales());
        reportBase.setImpressions(amazonAdCampaignDorisAllReport.getImpressions());
        reportBase.setClicks(amazonAdCampaignDorisAllReport.getClicks());
        reportBase.setOrderNum(amazonAdCampaignDorisAllReport.getSaleNum());
        reportBase.setSaleNum(amazonAdCampaignDorisAllReport.getOrderNum());
        reportBase.setAdSaleNum(amazonAdCampaignDorisAllReport.getAdOrderNum());
        reportBase.setAdOrderNum(amazonAdCampaignDorisAllReport.getAdSaleNum());
        reportBase.setSalesNewToBrand14d(amazonAdCampaignDorisAllReport.getSalesNewToBrand());
        reportBase.setOrdersNewToBrand14d(amazonAdCampaignDorisAllReport.getOrdersNewToBrand());
        reportBase.setUnitsOrderedNewToBrand14d(amazonAdCampaignDorisAllReport.getUnitsOrderedNewToBrand());
        reportBase.setViewImpressions(amazonAdCampaignDorisAllReport.getViewImpressions());
        reportBase.setType(type);
        reportBase.setMaxTopIs(amazonAdCampaignDorisAllReport.getMaxTopIs());
        reportBase.setMinTopIs(amazonAdCampaignDorisAllReport.getMinTopIs());
        reportBase.setNewToBrandDetailPageViews(amazonAdCampaignDorisAllReport.getNewToBrandDetailPageViews());
        reportBase.setAddToCart(amazonAdCampaignDorisAllReport.getAddToCart());
        reportBase.setVideo5SecondViews(amazonAdCampaignDorisAllReport.getVideo5secondViews());
        reportBase.setVideoFirstQuartileViews(amazonAdCampaignDorisAllReport.getVideoFirstQuartileViews());
        reportBase.setVideoMidpointViews(amazonAdCampaignDorisAllReport.getVideoMidpointViews());
        reportBase.setVideoThirdQuartileViews(amazonAdCampaignDorisAllReport.getVideoThirdQuartileViews());
        reportBase.setVideoCompleteViews(amazonAdCampaignDorisAllReport.getVideoCompleteViews());
        reportBase.setVideoUnmutes(amazonAdCampaignDorisAllReport.getVideoUnmutes());
        reportBase.setViewableImpressions(amazonAdCampaignDorisAllReport.getViewImpressions());
        reportBase.setBrandedSearches(amazonAdCampaignDorisAllReport.getBrandedSearches());
        reportBase.setDetailPageViews(amazonAdCampaignDorisAllReport.getDetailPageView());
        reportBase.setCumulativeReach(amazonAdCampaignDorisAllReport.getCumulativeReach());
        reportBase.setImpressionsFrequencyAverage(amazonAdCampaignDorisAllReport.getImpressionsFrequencyAverage());
        return reportBase;
    }

    /**
     * 填充占比数据
     */
    private void filterAdMetricData(AdMetricDto adMetricDto, CpcCommPageNewVo vo) {
        if (adMetricDto == null) {
            vo.setSumCost(BigDecimal.ZERO);
            vo.setSumAdSale(BigDecimal.ZERO);
            vo.setSumAdOrderNum(BigDecimal.ZERO);
            vo.setSumOrderNum(BigDecimal.ZERO);
            return;
        }
        vo.setSumCost(adMetricDto.getSumCost() == null ? BigDecimal.ZERO : adMetricDto.getSumCost());
        vo.setSumAdSale(adMetricDto.getSumAdSale() == null ? BigDecimal.ZERO : adMetricDto.getSumAdSale());
        vo.setSumAdOrderNum(adMetricDto.getSumAdOrderNum() == null ? BigDecimal.ZERO : adMetricDto.getSumAdOrderNum());
        vo.setSumOrderNum(adMetricDto.getSumOrderNum() == null ? BigDecimal.ZERO : adMetricDto.getSumOrderNum());
    }

    /**
     * 组装活动额外信息
     */
    private void buildCampaignExtraInfo(CampaignPageDto dto, MultipleCampaignPageVo vo, Boolean export) {
        AmazonAdCampaignAll amazonAdCampaign = dto.getCampaignAllMap().get(vo.getCampaignId());
        // 广告组合名称
        if (StringUtils.isNotBlank(amazonAdCampaign.getPortfolioId())) {
            Map<String, AmazonAdPortfolio> portfolioMap = dto.getPortfolioMap();
            if (portfolioMap.containsKey(amazonAdCampaign.getPortfolioId())) {
                vo.setPortfolioName(portfolioMap.get(amazonAdCampaign.getPortfolioId()).getName());
                vo.setIsHidden(portfolioMap.get(amazonAdCampaign.getPortfolioId()).getIsHidden());
            } else {
                vo.setPortfolioName("广告组合待同步");
            }
        } else {
            vo.setPortfolioName("-");
        }
        // 用户名称
        Map<Integer, User> userMap = dto.getUserMap();
        if (dto.getUserMap().containsKey(amazonAdCampaign.getCreateId())) {
            User user = userMap.get(amazonAdCampaign.getCreateId());
            if (StringUtils.isNotBlank(user.getNickname())) {
                vo.setCreator(user.getNickname());
            }
        }
        // 标签信息
        Map<String, List<AdTag>> relationTagMap = dto.getRelationTagMap();
        if (dto.getRelationTagMap().containsKey(amazonAdCampaign.getCampaignId())) {
            vo.setAdTags(relationTagMap.get(amazonAdCampaign.getCampaignId()));
        }
        // 构建广告策略信息
        buildStrategyTag(dto, vo);
        // 列表查询才需要构建以下数据、导出不需要
        if (!export) {
            // 构建日志信息
            buildCampaignHomeVoOperationLog(vo, dto);
            // 构建建议预算数据
            buildMissBudget(dto, vo);
            // 构建预算使用量
            buildBudgetUsage(dto, vo);
        }
    }

    /**
     * 构建规则标签
     */
    private static void buildStrategyTag(CampaignPageDto dto, MultipleCampaignPageVo vo) {
        Map<String, List<AdvertiseAutoRuleStatus>> autoRuleMap = dto.getAutoRuleMap();
        List<AdStrategyVo> adstrategyList = new ArrayList<>();
        // 分时标签
        if(vo.getIsBudgetPricing() == 1){
            buildStrategyVo(adstrategyList, AdCampaignStrategyTypeEnum.BUDGET_PRICING.getCode(), vo.getPricingBudgetState());
        }
        if(vo.getIsSpacePricing() == 1){
            buildStrategyVo(adstrategyList, AdCampaignStrategyTypeEnum.SPACE_PRICING.getCode(), vo.getPricingSpaceState());
        }
        if(vo.getIsStatePricing() == 1){
            buildStrategyVo(adstrategyList, AdCampaignStrategyTypeEnum.STATE_PRICING.getCode(), vo.getPricingStartStopState());
        }
        // 自动化规则标签
        if(autoRuleMap.containsKey(vo.getCampaignId())){
            // key 标签策略 status 状态集合  一个标签存在多种状态 如果存在已开启则为开启状态 否则为暂停状态
            Map<String,List<String>> strategyMap = new HashMap<>();
            Map<Integer, List<AdvertiseAutoRuleStatus>> autoRuleOperationMap = StreamUtil.groupingBy(autoRuleMap.get(vo.getCampaignId()), AdvertiseAutoRuleStatus::getOperationType);
            for (Integer operationType : autoRuleOperationMap.keySet()) {
                List<String> statusList = StreamUtil.toListDistinct(autoRuleOperationMap.get(operationType), AdvertiseAutoRuleStatus::getStatus);
                String strategy = AdCampaignStrategyTypeEnum.getStrategyMap().get(operationType);
                if(StringUtils.isNotEmpty(strategy)){
                    List<String> statusAllList = strategyMap.getOrDefault(strategy, new ArrayList<>());
                    statusAllList.addAll(statusList);
                    strategyMap.put(strategy,statusAllList);
                }
            }
            for (String strategy : strategyMap.keySet()) {
                int state = 0;
                List<String> statusList = strategyMap.get(strategy);
                if(statusList.contains("ENABLED")){
                    state = 1;
                }
                buildStrategyVo(adstrategyList, strategy, state);
            }
        }
        vo.setStrategyList(adstrategyList);
    }

    private static void buildStrategyVo(List<AdStrategyVo> adstrategyList, String adStrategyType, Integer status) {
        AdStrategyVo strategyVo = new AdStrategyVo();
        strategyVo.setAdStrategyType(adStrategyType);
        strategyVo.setStatus(status);
        adstrategyList.add(strategyVo);
    }

    /**
     * 构建预算使用量
     */
    private void buildBudgetUsage(CampaignPageDto dto, MultipleCampaignPageVo vo) {
        Map<String, Map<String, List<AmazonAdBudgetUsage>>> budgetUsageMap = dto.getBudgetUsageMap();
        Map<String, List<AmazonAdBudgetUsage>> adTypeMap = budgetUsageMap.get(vo.getType());
        List<BudgetUsage> budgetUsageList = new ArrayList<>();
        BudgetUsage todayBudgetUsage = null;
        Map<LocalDate, List<AmazonAdBudgetUsage>> budgetUsageDataListMap = new HashMap<>();
        if (MapUtils.isNotEmpty(adTypeMap)) {
            List<AmazonAdBudgetUsage> amazonAdBudgetUsages = adTypeMap.get(vo.getCampaignId());
            if (CollectionUtils.isNotEmpty(amazonAdBudgetUsages)) {
                budgetUsageDataListMap = amazonAdBudgetUsages.stream().collect(Collectors.groupingBy(AmazonAdBudgetUsage::getUsageUpdatedSiteDate));
            }
        }
        for (int x = 2; x >= 0; x--) {
            String dayType = "";
            if (x == 2) {
                dayType = "theDayBefore";
            }
            if (x == 1) {
                dayType = "yesterday";
            }
            if (x == 0) {
                dayType = "today";
            }
            LocalDate localDate = LocalDate.now(Marketplace.fromId(vo.getMarketplaceId()).getTimeZone().toZoneId()).minusDays(x);
            BudgetUsage budgetUsage = null;
            if (MapUtils.isNotEmpty(adTypeMap)) {
                budgetUsage = builderBudgetUsage(budgetUsageDataListMap.get(localDate));
            } else {
                budgetUsage = builderBudgetUsage(null);
            }
            budgetUsage.setDayType(dayType);
            budgetUsage.setDate(localDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
            if (x == 0) {
                todayBudgetUsage = budgetUsage;
            }
            budgetUsageList.add(budgetUsage);
        }
        vo.setBudgetUsage(todayBudgetUsage);
        vo.setBudgetUsageList(budgetUsageList);
    }

    /**
     * 构建预算使用量
     */
    private BudgetUsage builderBudgetUsage(List<AmazonAdBudgetUsage> amazonAdBudgetUsages) {
        BudgetUsage budgetUsage = new BudgetUsage();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(amazonAdBudgetUsages)) {
            List<AmazonAdBudgetUsage> sortedBudgetUsageList = amazonAdBudgetUsages.stream()
                    .sorted(Comparator.comparing(AmazonAdBudgetUsage::getUsageUpdatedSiteDate))
                    .collect(Collectors.toList());
            //最后更新记录
            AmazonAdBudgetUsage amazonAdBudgetUsage = sortedBudgetUsageList.get(sortedBudgetUsageList.size() - 1);
            budgetUsage.setIsNoData(false);
            budgetUsage.setCurrentBudget(amazonAdBudgetUsage.getBudget().doubleValue());
            budgetUsage.setLastUpdateAt(amazonAdBudgetUsage.getUsageUpdatedSiteTime()
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            budgetUsage.setPercent(amazonAdBudgetUsage.getBudgetUsagePercentage().doubleValue());
            //变化记录
            List<BudgetUsage.Item> budgetUsageItems =
                    sortedBudgetUsageList.stream().map(o -> {
                        BudgetUsage.Item item = new BudgetUsage.Item();
                        item.setCurrentBudget(o.getBudget().doubleValue());
                        item.setPercent(o.getBudgetUsagePercentage().doubleValue());
                        item.setUpdateAt(o.getUsageUpdatedSiteTime()
                                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        return item;
                    }).collect(Collectors.toList());
            budgetUsage.setItem(budgetUsageItems);
        } else {
            budgetUsage.setIsNoData(true);
        }
        return budgetUsage;
    }

    /**
     * 组装建议预算数据
     */
    private void buildMissBudget(CampaignPageDto dto, MultipleCampaignPageVo vo) {
        Map<String, AmazonAdMissBudget> missBudgetMap = dto.getMissBudgetMap();
        AmazonAdMissBudget amazonAdMissBudget = missBudgetMap.get(vo.getCampaignId());
        if (amazonAdMissBudget != null) {
            MissBudgetRecommendation missBudget = new MissBudgetRecommendation();
            missBudget.setCampaignId(amazonAdMissBudget.getCampaignId());
            if (amazonAdMissBudget.getSuggestedBudget() != null) {
                missBudget.setSuggestedBudget(amazonAdMissBudget.getSuggestedBudget().doubleValue());
            }
            if (amazonAdMissBudget.getEstimatedMissedSalesLower() != null) {
                missBudget.setEstimatedMissedSalesLower(amazonAdMissBudget.getEstimatedMissedSalesLower().doubleValue());
            }
            if (amazonAdMissBudget.getEstimatedMissedSalesUpper() != null) {
                missBudget.setEstimatedMissedSalesUpper(amazonAdMissBudget.getEstimatedMissedSalesUpper().doubleValue());
            }
            if (StringUtils.isNotBlank(amazonAdMissBudget.getStartDate())) {
                missBudget.setStartDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(amazonAdMissBudget.getStartDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
            }
            if (StringUtils.isNotBlank(amazonAdMissBudget.getEndDate())) {
                missBudget.setEndDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(amazonAdMissBudget.getEndDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
            }
            if (amazonAdMissBudget.getPercentTimeInBudget() != null) {
                missBudget.setPercentTimeInBudget(amazonAdMissBudget.getPercentTimeInBudget().doubleValue());
            }
            if (amazonAdMissBudget.getSuggestedBudgetIncreasePercent() != null) {
                missBudget.setSuggestedBudgetIncreasePercent(amazonAdMissBudget.getSuggestedBudgetIncreasePercent().doubleValue());
            }
            missBudget.setEstimatedMissedImpressionsLower(amazonAdMissBudget.getEstimatedMissedImpressionsLower());
            missBudget.setEstimatedMissedImpressionsUpper(amazonAdMissBudget.getEstimatedMissedImpressionsUpper());
            missBudget.setEstimatedMissedClicksLower(amazonAdMissBudget.getEstimatedMissedClicksLower());
            missBudget.setEstimatedMissedClicksUpper(amazonAdMissBudget.getEstimatedMissedClicksUpper());
            missBudget.setRuleId(amazonAdMissBudget.getRuleId());
            missBudget.setRuleName(amazonAdMissBudget.getRuleName());
            vo.setMissBudget(missBudget);
        }
    }

    /**
     * 组装日志信息
     */
    private void buildCampaignHomeVoOperationLog(MultipleCampaignPageVo vo, CampaignPageDto dto) {
        Map<String, AmazonAdOperationLogBO> amazonAdOperationLogMap = dto.getAmazonAdOperationLogMap();
        //预算日志
        String logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.CAMPAIGN_BUDGET_AMOUNT.getCode(), vo.getCampaignId());
        if (amazonAdOperationLogMap.containsKey(logKey)) {
            CampaignDataLog budgetLog = buildLog(amazonAdOperationLogMap, logKey);
            vo.setBudgetLog(budgetLog);
            vo.setIsUpdateBudget(true);
        } else {
            vo.setIsUpdateBudget(stringRedisService.get(logKey) != null);
        }
        //搜索结果顶部竞价日志
        logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_TOP_OF_SEARCH.getCode(), vo.getCampaignId());
        if (amazonAdOperationLogMap.containsKey(logKey)) {
            CampaignDataLog placementTopLog = buildLog(amazonAdOperationLogMap, logKey);
            vo.setPlacementTopLog(placementTopLog);
            vo.setIsUpdatePlacementTop(true);
        } else {
            vo.setIsUpdatePlacementTop(stringRedisService.get(logKey) != null);
        }
        //产品页面竞价日志
        logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_DETAIL_PAGE.getCode(), vo.getCampaignId());
        if (amazonAdOperationLogMap.containsKey(logKey)) {
            CampaignDataLog placementProductPageLog = buildLog(amazonAdOperationLogMap, logKey);
            vo.setPlacementProductPageLog(placementProductPageLog);
            vo.setIsUpdatePlacementProductPage(true);
        } else {
            vo.setIsUpdatePlacementProductPage(stringRedisService.get(logKey) != null);
        }
        //产品页面竞价日志
        logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_REST_OF_SEARCH.getCode(), vo.getCampaignId());
        if (amazonAdOperationLogMap.containsKey(logKey)) {
            CampaignDataLog placementRestOfSearchLog = buildLog(amazonAdOperationLogMap, logKey);
            vo.setPlacementRestOfSearchLog(placementRestOfSearchLog);
            vo.setIsUpdatePlacementRestOfSearch(true);
        } else {
            vo.setIsUpdatePlacementRestOfSearch(stringRedisService.get(logKey) != null);
        }
        //企业购竞价日志
        logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.PLACEMENT_SITE_AMAZON_BUSINESS.getCode(), vo.getCampaignId());
        if (amazonAdOperationLogMap.containsKey(logKey)) {
            CampaignDataLog placementSiteAmazonBusinessLog = buildLog(amazonAdOperationLogMap, logKey);
            vo.setPlacementSiteAmazonBusinessLog(placementSiteAmazonBusinessLog);
            vo.setIsUpdatePlacementSiteAmazonBusiness(true);
        } else {
            vo.setIsUpdatePlacementSiteAmazonBusiness(stringRedisService.get(logKey) != null);
        }
    }

    /**
     * 组装日志信息
     */
    private CampaignDataLog buildLog(Map<String, AmazonAdOperationLogBO> amazonAdOperationLogMap, String logKey) {
        AmazonAdOperationLogBO logBO = amazonAdOperationLogMap.get(logKey);
        if (StringUtils.isNotBlank(logBO.getContent())) {
            OperationContent operationContent = JSONUtil.jsonToObject(logBO.getContent(), OperationContent.class);
            if (operationContent != null) {
                CampaignDataLog log = new CampaignDataLog();
                log.setCount(logBO.getCount());
                if (StringUtils.isNotBlank(operationContent.getPreviousValue())) {
                    log.setPreviousValue(operationContent.getPreviousValue());
                }
                if (StringUtils.isNotBlank(operationContent.getNewValue())) {
                    log.setNewValue(operationContent.getNewValue());
                }
                log.setSiteOperationTime(logBO.getSiteOperationTime());
                return log;
            }
        }
        return null;
    }

    /**
     * 组装基础信息
     */
    private void buildCampaignBaseInfo(CampaignPageParam param, String campaignId, CampaignPageDto dto, MultipleCampaignPageVo vo) {
        Map<Integer, ShopAuth> shopAuthMap = dto.getShopAuthMap();
        AmazonAdCampaignAll amazonAdCampaign = dto.getCampaignAllMap().get(campaignId);
        AmazonAdCampaignAll amazonAdCampaignDoris = dto.getCampaignDorisAllMap().get(campaignId);
        // DORIS
        vo.setName(amazonAdCampaignDoris.getName());
        vo.setDailyBudget(String.valueOf(amazonAdCampaignDoris.getBudget()));
        vo.setState(StringUtils.isBlank(param.getStatus()) ? amazonAdCampaign.getState() : amazonAdCampaignDoris.getState());
        // mysql
        vo.setId(amazonAdCampaign.getId());
        vo.setShopId(amazonAdCampaign.getShopId());
        vo.setCampaignId(amazonAdCampaign.getCampaignId());
        vo.setSbType(amazonAdCampaign.getIsMultiAdGroupsEnabled());
        vo.setPortfolioId(amazonAdCampaign.getPortfolioId());
        vo.setBudgetType(amazonAdCampaign.getBudgetType());
        vo.setCampaignType(amazonAdCampaign.getCampaignType());
        vo.setTargetingType(amazonAdCampaign.getTargetingType());
        vo.setStrategy(amazonAdCampaign.getStrategy());
        vo.setStartDate(amazonAdCampaign.getStartDateStr());
        vo.setEndDate(amazonAdCampaign.getEndDateStr());
        vo.setCreateTime(DateUtil.dateToStrWithFormat(amazonAdCampaign.getCreateTime(), DateUtil.PATTERN_DATE_TIME));
        vo.setUpdateTime(DateUtil.dateToStrWithFormat(amazonAdCampaign.getUpdateTime(), DateUtil.PATTERN_DATE_TIME));
        vo.setType(amazonAdCampaign.getType());
        vo.setCampaignTargetingType(amazonAdCampaign.getAdTargetType());
        vo.setCostType(amazonAdCampaign.getCostType());
        if (CampaignTypeEnum.sp.getCampaignType().equals(vo.getType()) || StringUtils.isEmpty(vo.getCostType())) {
            vo.setCostType("cpc");
        }
        if (CampaignTypeEnum.sp.getCampaignType().equals(amazonAdCampaign.getType())) {
            vo.setCampaignTargetingType(amazonAdCampaign.getTargetingType());
        }
        if (CampaignTypeEnum.sb.getCampaignType().equals(amazonAdCampaign.getType())) {
            vo.setCampaignTargetingType(Constants.MANUAL);
            vo.setTargetType(amazonAdCampaign.getTargetType());
        }
        if (CampaignTypeEnum.sd.getCampaignType().equals(amazonAdCampaign.getType())) {
            vo.setCampaignTargetingType(amazonAdCampaign.getTactic());
        }
        //分时调价
        vo.setIsBudgetPricing(amazonAdCampaign.getIsBudgetPricing());
        vo.setPricingBudgetState(amazonAdCampaign.getPricingBudgetState());
        vo.setIsSpacePricing(amazonAdCampaign.getIsSpacePricing());
        vo.setPricingSpaceState(amazonAdCampaign.getPricingSpaceState());
        vo.setIsStatePricing(amazonAdCampaign.getIsStatePricing());
        vo.setPricingStartStopState(amazonAdCampaign.getPricingStartStopState());
        if (CampaignTypeEnum.sb.getCampaignType().equals(amazonAdCampaign.getType())) {
            vo.setBrandEntityId(amazonAdCampaign.getBrandEntityId());
            vo.setBidOptimization(amazonAdCampaign.getBidOptimization());
            if (amazonAdCampaign.getBidMultiplier() != null) {
                vo.setBidMultiplier(amazonAdCampaign.getBidMultiplier().doubleValue());
            }
        }
        vo.setMarketplaceId(amazonAdCampaign.getMarketplaceId());
        // 币种
        vo.setCurrency(AmznEndpoint.getByMarketplaceId(vo.getMarketplaceId()).getCurrencyCode().value());
        // 店铺名称
        ShopAuth shopAuth = shopAuthMap.get(vo.getShopId());
        vo.setShopName(shopAuth.getName());
        // 构建服务状态
        buildServingStatus(param, vo, amazonAdCampaign, amazonAdCampaignDoris);
        // 组装广告位
        buildPlacement(vo, amazonAdCampaign);
    }

    /**
     * 组装广告位
     */
    private void buildPlacement(MultipleCampaignPageVo vo, AmazonAdCampaignAll amazonAdCampaign) {
        // 默认值
        vo.setPlacementProductPage("0");
        vo.setPlacementTop("0");
        vo.setPlacementRestOfSearch("0");
        vo.setPlacementSiteAmazonBusiness("0");
        if (StringUtils.isNotBlank(amazonAdCampaign.getAdjustments())) {
            List<Adjustment> adjustments = null;
            try {
                adjustments = JSONUtil.jsonToObjectIgnoreUnKnownThrowable(amazonAdCampaign.getAdjustments(), new TypeReference<List<Adjustment>>() {
                });
            } catch (IOException e) {
                log.error("adjustment:", e);
            }
            if (CollectionUtils.isNotEmpty(adjustments)) {
                for (Adjustment adjustment : adjustments) {
                    if (PredicateEnum.PLACEMENTPRODUCTPAGE.value().equals(adjustment.getPredicate())) {
                        vo.setPlacementProductPage(String.valueOf(adjustment.getPercentage()));
                    } else if (PredicateEnum.PLACEMENTTOP.value().equals(adjustment.getPredicate())) {
                        vo.setPlacementTop(String.valueOf(adjustment.getPercentage()));
                    } else if (PredicateEnum.PLACEMENTRESTOFSEARCH.value().equals(adjustment.getPredicate())) {
                        vo.setPlacementRestOfSearch(String.valueOf(adjustment.getPercentage()));
                    } else if (PredicateEnum.SITEAMAZONBUSINESS.value().equals(adjustment.getPredicate()) && Constants.placementSiteAmazonBusinessMarketplaceIds.contains(vo.getMarketplaceId())) {
                        vo.setPlacementSiteAmazonBusiness(String.valueOf(adjustment.getPercentage()));
                    }
                }
            }
        }
    }

    /**
     * 组装服务状态
     */
    private void buildServingStatus(CampaignPageParam param, MultipleCampaignPageVo vo, AmazonAdCampaignAll amazonAdCampaign, AmazonAdCampaignAll amazonAdCampaignDoris) {
        // 服务状态名称、描述转换
        amazonAdCampaign.setServingStatus(amazonAdCampaign.getServingStatus());
        amazonAdCampaignDoris.setServingStatus(amazonAdCampaignDoris.getServingStatus());
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            vo.setServingStatus(amazonAdCampaignDoris.getServingStatus());
            vo.setServingStatusDec(amazonAdCampaignDoris.getServingStatusDec());
            vo.setServingStatusName(amazonAdCampaignDoris.getServingStatusName());
        } else {
            vo.setServingStatus(amazonAdCampaign.getServingStatus());
            vo.setServingStatusDec(amazonAdCampaign.getServingStatusDec());
            vo.setServingStatusName(amazonAdCampaign.getServingStatusName());
        }
        //根据状态code查询状态描述
        if (AmazonAdCampaignAll.stateEnum.enabled.getCode().equals(amazonAdCampaign.getState())) {
            AmazonAdCampaign.servingStatusEnum servingStatusEnum = UCommonUtil.getByCode(amazonAdCampaign.getServingStatus(), AmazonAdCampaign.servingStatusEnum.class);
            vo.setServingStatusDec(null == servingStatusEnum ? StringUtils.EMPTY : servingStatusEnum.getDescription());
            if (AmazonAdCampaignAll.servingStatusEnum.outOfBudget.getCode().equalsIgnoreCase(amazonAdCampaign.getServingStatus()) || AmazonAdCampaignAll.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode().equalsIgnoreCase(amazonAdCampaign.getServingStatus())) {
                if (AmazonAdCampaignAll.servingStatusEnum.outOfBudget.getCode().equalsIgnoreCase(amazonAdCampaign.getServingStatus())) {
                    vo.setServingStatus("CAMPAIGN_OUT_OF_BUDGET");
                }
                //如果状态是超过预算
                String outOfTimeStr = "";
                if (amazonAdCampaign.getOutOfBudgetTime() != null) {
                    try {
                        LocalDateTime localDateTime = LocalDateTimeUtil.ofEpochSecondToDateTime(amazonAdCampaign.getOutOfBudgetTime());
                        ZoneId zoneId = ZoneUtil.getZoneIdByAmzSite(amazonAdCampaign.getMarketplaceId());
                        localDateTime = LocalDateTimeUtil.getZoneTime(localDateTime, ZoneId.systemDefault(), zoneId);
                        Date date = LocalDateTimeUtil.convertLDTToDate(localDateTime);
                        outOfTimeStr = DateUtil.dateToStrWithFormat(date, "HH:mm");
                    } catch (Exception e) {
                        log.error("转换超预算时间错误", e);
                    }
                }
                vo.setServingStatusName(vo.getServingStatusName() + " " + outOfTimeStr);
                vo.setOutOfBudget(true);
            }
        }
    }

    /**
     * 数据准备
     */
    private CampaignPageDto prepareData(CampaignPageParam param, Page<AmazonAdCampaignDorisAllReport> campaignIdPage, Boolean export) {
        CampaignPageDto dto = new CampaignPageDto();
        // 获取汇总数据
        dto.setAdMetricDto(amazonAdCampaignAllDorisDao.getSumAdMetricMultiple(param));
        // 店铺信息
        dto.setShopAuthMap(StreamUtil.toMap(param.getShopAuthList(), ShopAuth::getId));
        // 分页的活动id集合
        param.setCampaignIdList(StreamUtil.toList(campaignIdPage.getRows(), AmazonAdCampaignDorisAllReport::getCampaignId));
        // 分页的店铺id集合
        param.setShopIdList(StreamUtil.toListDistinct(campaignIdPage.getRows(), AmazonAdCampaignDorisAllReport::getShopId));
        // mysql活动基础信息
        List<List<String>> lists = Lists.partition(param.getCampaignIdList(), 10000);
        List<AmazonAdCampaignAll> amazonAdCampaignAll = new ArrayList<>();
        for (List<String> list : lists) {
            amazonAdCampaignAll.addAll(amazonAdCampaignAllDao.listByShopIdAndCampaignIds(param.getPuid(), param.getShopIdList(), list));
        }
        dto.setCampaignAllMap(StreamUtil.toMap(amazonAdCampaignAll, AmazonAdCampaignAll::getCampaignId));
        // 异步多线程获取数据
        asyncPrepareData(param, export, dto, lists, amazonAdCampaignAll);
        return dto;
    }

    /**
     * 异步多线程获取数据
     */
    private void asyncPrepareData(CampaignPageParam param, Boolean export, CampaignPageDto dto, List<List<String>> lists, List<AmazonAdCampaignAll> amazonAdCampaignAll) {
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        // 异步获取报告数据
        setReportFeature(param, export, dto, futureList);
        // 异步获取店铺销售额数据
        setShopSaleFeature(param, export, dto, futureList);
        // 异步获取doris活动基础信息
        setDorisFeature(param, export, lists, dto, futureList);
        // 异步获取自动化规则策略信息
        setAutoRuleFeature(param, export, lists, dto, futureList);
        // 异步获取广告组合信息
        setPortfolioFeature(param, export, amazonAdCampaignAll, dto, futureList);
        // 异步获取puid下所有用户信息
        setUserFeature(param, export, dto, futureList);
        // 异步获取标签信息
        setTagFeature(param, export, dto, futureList);
        // 列表查询才需要查询以下数据、导出不需要
        if (!export) {
            // 异步获取24小时前的日志
            setLogFeature(param, dto, futureList);
            // 异步获取预算使用量
            setBudgetUsageFeature(param, dto, amazonAdCampaignAll, futureList);
            // 异步获取建议预算数据
            setMissBudgetFeature(param, amazonAdCampaignAll, dto, futureList);
            // 对比值
            if (param.getIsCompare()) {
                // 异步获取对比报告数据
                setCompareReportFeature(param, dto, futureList);
                // 异步获取对比店铺销售额
                setCompareDwsSales(param, dto, futureList);
            }
        }
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        try {
            // 等待所有 CompletableFuture 完成
            allOf.join();
        } catch (CompletionException e) {
            log.error("Error occurred while waiting for CompletableFuture completion", e);
        }
    }

    /**
     * 异步获取对比店铺销售额
     */
    private void setCompareDwsSales(CampaignPageParam param, CampaignPageDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            CampaignPageParam compareParam = BeanUtil.copyProperties(param, CampaignPageParam.class);
            compareParam.setStartDate(param.getCompareStartDate());
            compareParam.setEndDate(param.getCompareEndDate());
            List<DwsSaleProfitShopDay> compareShopSalesList = dwsSaleProfitShopDayDao.listShopSaleByDateRange(compareParam.getPuid(), compareParam.getShopIdList(), compareParam.getCompareStartDate(), compareParam.getCompareEndDate());
            dto.setCompareShopSalesMap(StreamUtil.toMap(compareShopSalesList, DwsSaleProfitShopDay::getShopId, DwsSaleProfitShopDay::getSalePrice));
            return null;
        }, getExecutor(false));
        futureList.add(reportFuture);

    }

    /**
     * 异步获取对比报告数据
     */
    private void setCompareReportFeature(CampaignPageParam param, CampaignPageDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            CampaignPageParam compareParam = BeanUtil.copyProperties(param, CampaignPageParam.class);
            compareParam.setStartDate(param.getCompareStartDate());
            compareParam.setEndDate(param.getCompareEndDate());
            List<AmazonAdCampaignDorisSumReport> compareReportList = amazonAdCampaignAllDorisDao.listCampaignReport(compareParam, false);
            dto.setCompareReportMap(StreamUtil.toMap(compareReportList, AmazonAdCampaignDorisSumReport::getCampaignId));
            return null;
        }, getExecutor(false));
        futureList.add(reportFuture);

    }

    /**
     * 异步获取建议预算数据
     */
    private void setMissBudgetFeature(CampaignPageParam param, List<AmazonAdCampaignAll> amazonAdCampaignAll, CampaignPageDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            List<String> campignIdFilterList = amazonAdCampaignAll.stream().filter(item -> Arrays.asList("enabled", "paused").contains(item.getState()))
                    .map(AmazonAdCampaignAll::getCampaignId).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(campignIdFilterList)){
                List<AmazonAdMissBudget> missBudgets = amazonAdMissBudgetService.listByCampaignIdsAndShopIds(param.getPuid(), param.getShopIdList(), campignIdFilterList);
                dto.setMissBudgetMap(StreamUtil.toMap(missBudgets, AmazonAdMissBudget::getCampaignId));
            }
            return null;
        }, getExecutor(false));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取预算使用量
     */
    private void setBudgetUsageFeature(CampaignPageParam param, CampaignPageDto dto, List<AmazonAdCampaignAll> amazonAdCampaignAll, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            Map<String, List<AmazonAdCampaignAll>> marketCampaignMap = amazonAdCampaignAll.stream().filter(item -> Arrays.asList("enabled", "paused").contains(item.getState()))
                    .collect(Collectors.groupingBy(AmazonAdCampaignAll::getMarketplaceId));
            List<AmazonAdBudgetUsage> usageList = new ArrayList<>();
            for (String marketPlaceId : marketCampaignMap.keySet()) {
                LocalDate siteDate = LocalDate.now(Marketplace.fromId(marketPlaceId).getTimeZone().toZoneId());
                List<AmazonAdCampaignAll> campaignAlls = marketCampaignMap.get(marketPlaceId);
                List<String> campaignIds = StreamUtil.toListDistinct(campaignAlls, AmazonAdCampaignAll::getCampaignId);
                List<Integer> shopIds = StreamUtil.toListDistinct(campaignAlls, AmazonAdCampaignAll::getShopId);
                List<AmazonAdBudgetUsage> budgetUsages = amazonAdBudgetUsageService
                        .listByCampaignIdsAndShopIdsAndData(param.getPuid(), shopIds, "CAMPAIGN", siteDate.minusDays(3), siteDate, campaignIds);
                if(CollectionUtils.isNotEmpty(budgetUsages)){
                    usageList.addAll(budgetUsages);
                }
            }
            Map<String, Map<String, List<AmazonAdBudgetUsage>>> budgetUsageMap = usageList.stream().collect(Collectors.groupingBy(AmazonAdBudgetUsage::getAdvertisingProductType,
                    Collectors.groupingBy(AmazonAdBudgetUsage::getBudgetScopeId)));
            dto.setBudgetUsageMap(budgetUsageMap);
            return null;
        }, getExecutor(false));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取24小时前的日志
     */
    private void setLogFeature(CampaignPageParam param, CampaignPageDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            Date lastDate = DateUtil.addDay(new Date(), -1);
            List<Integer> changeTypeList = Lists.newArrayList(AmazonAdOperationLogChangeTypeEnum.CAMPAIGN_BUDGET_AMOUNT.getCode(), AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_TOP_OF_SEARCH.getCode(),
                    AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_DETAIL_PAGE.getCode(), AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_REST_OF_SEARCH.getCode(), AmazonAdOperationLogChangeTypeEnum.PLACEMENT_SITE_AMAZON_BUSINESS.getCode());
            dto.setAmazonAdOperationLogMap(amazonAdOperationLogService.listGroupByTypeAndIdentifyIdNewAndShopIdList(param.getPuid(), param.getShopIdList(), Constants.SP, AmazonAdOperationLogEntityTypeEnum.CAMPAIGN.getCode(), changeTypeList, param.getCampaignIdList(), lastDate));
            return null;
        }, getExecutor(false));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取标签信息
     */
    private void setTagFeature(CampaignPageParam param, Boolean export, CampaignPageDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            dto.setRelationTagMap(adManageTagRelationService.getRelationTagInfo(param.getPuid(),
                    param.getIsAdmin() ? null : param.getUid(), AdManageTagTypeEnum.CAMPAIGN.getCode(), param.getCampaignIdList()));
            return null;
        }, getExecutor(export));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取puid下所有用户信息
     */
    private void setUserFeature(CampaignPageParam param, Boolean export, CampaignPageDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            dto.setUserMap(userDao.listByPuid(param.getPuid()).stream().collect(Collectors.toMap(User::getId, e -> e)));
            return null;
        }, getExecutor(export));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取广告组合信息
     */
    private void setPortfolioFeature(CampaignPageParam param, Boolean export, List<AmazonAdCampaignAll> amazonAdCampaignAll, CampaignPageDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            List<String> portfolioIds = amazonAdCampaignAll.stream().map(AmazonAdCampaignAll::getPortfolioId).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                dto.setPortfolioMap(portfolioDao.listByShopId(param.getPuid(), param.getShopIdList(), portfolioIds).stream()
                        .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e)));
            }
            return null;
        }, getExecutor(export));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取自动化规则策略信息
     */
    private void setAutoRuleFeature(CampaignPageParam param, Boolean export, List<List<String>> lists, CampaignPageDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            List<AdvertiseAutoRuleStatus> autoRuleStatuses = new ArrayList<>();
            for (List<String> list : lists) {
                autoRuleStatuses.addAll(advertiseAutoRuleStatusDao.listByItemIdMutiple(param.getPuid(), param.getShopIdList(), AutoRuleItemTypeEnum.CAMPAIGN.getName(), list, null));
            }
            dto.setAutoRuleMap(StreamUtil.groupingBy(autoRuleStatuses, AdvertiseAutoRuleStatus::getItemId));
            return null;
        }, getExecutor(export));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取doris活动基础信息
     */
    private void setDorisFeature(CampaignPageParam param, Boolean export, List<List<String>> lists, CampaignPageDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            List<AmazonAdCampaignAll> amazonAdCampaignDorisAll = new ArrayList<>();
            for (List<String> list : lists) {
                amazonAdCampaignDorisAll.addAll(amazonAdCampaignAllDorisDao.listByCampaignIds(param.getPuid(), param.getShopIdList(), list));
            }
            dto.setCampaignDorisAllMap(StreamUtil.toMap(amazonAdCampaignDorisAll, AmazonAdCampaignAll::getCampaignId));
            return null;
        }, getExecutor(export));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取店铺销售额数据
     */
    private void setShopSaleFeature(CampaignPageParam param, Boolean export, CampaignPageDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            List<DwsSaleProfitShopDay> shopSalesList = dwsSaleProfitShopDayDao.listShopSaleByDateRange(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate());
            dto.setShopSalesMap(StreamUtil.toMap(shopSalesList, DwsSaleProfitShopDay::getShopId, DwsSaleProfitShopDay::getSalePrice));
            return null;
        }, getExecutor(export));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取报告数据
     */
    private void setReportFeature(CampaignPageParam param, Boolean export, CampaignPageDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            List<AmazonAdCampaignDorisSumReport> reportList = amazonAdCampaignAllDorisDao.listCampaignReport(param, export);
            dto.setReportMap(StreamUtil.toMap(reportList, AmazonAdCampaignDorisSumReport::getCampaignId));
            return null;
        }, getExecutor(export));
        futureList.add(reportFuture);
    }

    /**
     * 获取异步线程池
     */
    private ThreadPoolTaskExecutor getExecutor(Boolean export) {
        if (export) {
            return adExportExecutor;
        } else {
            return campaignAdPageExecutor;
        }
    }

    /**
     * 参数校验
     */
    private void checkParam(CampaignPageParam param) {
        // 策略类型校验
        if (StringUtils.isNotBlank(param.getStrategyType())) {
            if (StringUtils.isBlank(StrategyEnum.getStrategyValue(param.getStrategyType()))) {
                throw new SponsoredBizException("请求参数错误");
            }
        }
        // 预算状态校验
        if (StringUtils.isNotBlank(param.getBudgetState())) {
            CampaignPageParam.BudgetStateEnum budgetStateEnum = UCommonUtil.getByCode(param.getBudgetState(), CampaignPageParam.BudgetStateEnum.class);
            if (budgetStateEnum == null) {
                throw new SponsoredBizException("请求参数错误");
            }
        }
        // 搜索字段校验
        if (StringUtils.isNotBlank(param.getSearchField())) {
            CampaignPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(param.getSearchField(), CampaignPageParam.SearchFieldEnum.class);
            if (searchFieldEnum == null) {
                throw new SponsoredBizException("请求参数错误");
            }
        }
        // 排序字段校验
        if (StringUtils.isNotBlank(param.getOrderField())) {
            CampaignOrderByEnum enumByCode = CampaignOrderByEnum.getEnumByCode(param.getOrderField());
            if (enumByCode == null) {
                throw new SponsoredBizException("请求参数错误");
            }
        }
        // 店铺状态校验
        List<ShopAuth> shopAuths = scVcShopAuthDao.listValidShopByIds(param.getPuid(), param.getShopIdList());
        if (CollectionUtils.isEmpty(shopAuths)) {
            throw new SponsoredBizException("店铺未授权");
        }
        param.setShopIdList(StreamUtil.toListDistinct(shopAuths, ShopAuth::getId));
        param.setShopAuthList(shopAuths);
        param.setShopAuthMap(StreamUtil.toMap(shopAuths, ShopAuth::getId));
    }

    /**
     * 参数赋值
     */
    private void setParam(CampaignPageParam param) {
        // 前端传的deliveryType 转成filterTargetType 历史逻辑
        param.setFilterTargetType(param.getDeliveryType());
        // 环比
        param.setIsCompare(param.getIsCompare() != null && param.getIsCompare() && StringUtil.isNotEmpty(param.getCompareStartDate()) && StringUtil.isNotEmpty(param.getCompareEndDate()));
        // 分页参数
        param.setPageNo(param.getPageNo() == null ? 1 : param.getPageNo());
        param.setPageSize(param.getPageSize() == null ? 20 : param.getPageSize());
        // 开始结束时间
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }
        // 广告活动列表页处理高级筛选百分比参数
        handlePercentParam(param);
        // SB类型筛选
        List<String> campaignIdFilterBySbCreativeType = amazonSbAdsDao.getCampaignIdBySbCreativeTypeMultiple(param.getPuid(), param.getShopIdList(), param.getFilterTargetType(), param.getUseAdvanced());
        param.setCreativeIds(campaignIdFilterBySbCreativeType);
        // 判断是否是多店铺且币种不同需要换币种
        param.setChangeRate(MultipleUtils.changeRate(param.getShopAuthList()));
        // 广告组合转成list
        if (StringUtil.isNotEmpty(param.getPortfolioId())) {
            param.setPortfolioIdList(StringUtil.splitStr(param.getPortfolioId(), ","));
            if(!param.getPortfolioIdList().contains("-1")){
                // 兼容前端异常场景 传的广告组合在店铺下不存在时清空广告组合筛选
                Integer count = odsAmazonAdPortfolioDao.countPortfolioList(param.getPuid(), param.getShopIdList(), param.getPortfolioIdList());
                if(count == 0){
                    param.setPortfolioIdList(null);
                }
            }
        }
        // 批量查询处理
        if (StringUtils.isNotBlank(param.getSearchValue()) && param.getSearchValue().contains(StringUtil.SPECIAL_COMMA)) {
            param.setSearchValueList(StringUtil.splitStr(param.getSearchValue().trim(), StringUtil.SPECIAL_COMMA));
            param.setSearchValue(null);
        }
        // 模糊查询传递特殊字符的处理
        if (param.getSearchValue() != null && !SearchTypeEnum.EXACT.getValue().equalsIgnoreCase(param.getSearchType())) {
            String searchValue = param.getSearchValue();
            if (searchValue.contains("\\")) {
                searchValue = searchValue.replace("\\", "\\\\");
            }
            if (searchValue.contains("%")) {
                searchValue = searchValue.replace("%", "\\%");
            }
            // 将替换后的字符串重新设置回 param 对象
            param.setSearchValue(searchValue);
        }
    }

    /**
     * 广告活动列表页处理高级筛选百分比参数
     */
    private void handlePercentParam(CampaignPageParam campaignPageParam) {
        campaignPageParam.setClickRateMin(campaignPageParam.getClickRateMin() != null ? MathUtil.divide(campaignPageParam.getClickRateMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setClickRateMax(campaignPageParam.getClickRateMax() != null ? MathUtil.divide(campaignPageParam.getClickRateMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcosMin(campaignPageParam.getAcosMin() != null ? MathUtil.divide(campaignPageParam.getAcosMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcosMax(campaignPageParam.getAcosMax() != null ? MathUtil.divide(campaignPageParam.getAcosMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setSalesConversionRateMin(campaignPageParam.getSalesConversionRateMin() != null ? MathUtil.divide(campaignPageParam.getSalesConversionRateMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setSalesConversionRateMax(campaignPageParam.getSalesConversionRateMax() != null ? MathUtil.divide(campaignPageParam.getSalesConversionRateMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcotsMin(campaignPageParam.getAcotsMin() != null ? MathUtil.divide(campaignPageParam.getAcotsMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcotsMax(campaignPageParam.getAcotsMax() != null ? MathUtil.divide(campaignPageParam.getAcotsMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAsotsMin(campaignPageParam.getAsotsMin() != null ? MathUtil.divide(campaignPageParam.getAsotsMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAsotsMax(campaignPageParam.getAsotsMax() != null ? MathUtil.divide(campaignPageParam.getAsotsMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setOrderRateNewToBrandFTDMin(campaignPageParam.getOrderRateNewToBrandFTDMin() != null ? MathUtil.divide(campaignPageParam.getOrderRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setOrderRateNewToBrandFTDMax(campaignPageParam.getOrderRateNewToBrandFTDMax() != null ? MathUtil.divide(campaignPageParam.getOrderRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setSalesRateNewToBrandFTDMin(campaignPageParam.getSalesRateNewToBrandFTDMin() != null ? MathUtil.divide(campaignPageParam.getSalesRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setSalesRateNewToBrandFTDMax(campaignPageParam.getSalesRateNewToBrandFTDMax() != null ? MathUtil.divide(campaignPageParam.getSalesRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setUnitsOrderedRateNewToBrandFTDMin(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMin() != null ? MathUtil.divide(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setUnitsOrderedRateNewToBrandFTDMax(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMax() != null ? MathUtil.divide(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
    }

    /**
     * 根据条件过滤活动id集合
     */
    private Boolean getQueryIdsByPageFilter(Integer puid, CampaignPageParam param) {
        List<String> campaignIds = new ArrayList<>();
        // 标签筛选活动id
        if (CollectionUtils.isNotEmpty(param.getAdTagIds())) {
            List<String> tagIds = param.getAdTagIds().stream().map(String::valueOf).collect(Collectors.toList());
            campaignIds = adManageTagRelationService.getRelationIdByTagIds(param.getPuid(), tagIds, AdManageTagTypeEnum.CAMPAIGN.getCode(), param.getShopIdList());
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 为空直接返回结果
                param.setCampaignIdList(campaignIds);
            } else {
                return true;
            }
        }
        // asin、父asin、msku筛选活动id #issue packet too large
        if (StringUtils.isNotBlank(param.getProductType()) && Constants.CAMPAIGN_PRODUCT_SELECT.contains(param.getProductType()) && StringUtils.isNotBlank(param.getProductValue())) {
            List<String> marketplaceIdList = StreamUtil.toListDistinct(param.getShopAuthList(), ShopAuth::getMarketplaceId);
            List<String> listProductValues = param.getListProductValues();
            campaignIds = amazonAdProductDao.getCampaignIdByAsinOrMskuShopList(puid, param.getShopIdList(), marketplaceIdList, param.getProductType(), listProductValues, param.getType(), campaignIds);
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 为空直接返回结果
                param.setCampaignIdList(campaignIds);
            } else {
                return true;
            }
        }
        // 自动化规则筛选活动id集合
        List<Integer> operationTypeList = AdCampaignStrategyTypeEnum.operationTypeList(param.getAdStrategyTypeList());
        if(CollectionUtils.isNotEmpty(operationTypeList)){
            List<String> campaignIdList = advertiseAutoRuleStatusDao.listItemIdByAdManage(param.getPuid(), param.getShopIdList(), AutoRuleItemTypeEnum.CAMPAIGN.getName(), operationTypeList, null, null, null, null);
            if(CollectionUtils.isEmpty(campaignIdList) && !param.getAdStrategyTypeList().contains(AdCampaignStrategyTypeEnum.NONE.getCode()) &&
                    !param.getAdStrategyTypeList().contains(AdCampaignStrategyTypeEnum.BUDGET_PRICING.getCode()) &&
                    !param.getAdStrategyTypeList().contains(AdCampaignStrategyTypeEnum.STATE_PRICING.getCode()) &&
                    !param.getAdStrategyTypeList().contains(AdCampaignStrategyTypeEnum.SPACE_PRICING.getCode())){
                // 只存在自动化规则筛选没数据时返回
                return true;
            }else{
                param.setAutoRuleIds(campaignIdList);
            }
        }
        return false;
    }
}
