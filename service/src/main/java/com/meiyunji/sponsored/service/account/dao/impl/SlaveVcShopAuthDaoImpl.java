package com.meiyunji.sponsored.service.account.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.amazon.sellerpartner.base.RegionEnum;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.SlaveBaseDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.dao.ISlaveShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.enums.ShopAdStatusEnum;
import com.meiyunji.sponsored.service.enums.ShopStatusEnum;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.ShopByPuidDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * ShopAuth
 *
 * <AUTHOR>
 */
@Repository
public class SlaveVcShopAuthDaoImpl extends SlaveBaseDaoImpl<VcShopAuth> implements ISlaveVcShopAuthDao {
    private final static String SHOPAUTH_PUID_KEY = "shopauth_puid_%s_%s"; //判断puid是否缓存了配置信息
    private final static int TIME_OUT = 60 * 60 * 4;//过期时间 1小时
    private String cacheKeyPrefix = "shopauth-"; // cache 前缀
    private int cacheValidation = 5 * 60; // cache 有效期 5 分钟

    @Override
    public List<VcShopAuth> listAllByIds(Integer puid, List<Integer> shopIds) {
        /*if (CollectionUtils.isEmpty(shopIds)) {
            return Lists.newArrayList();
        }*/

        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
//                .in("id", shopIds.toArray())
                .build();

        return super.listByCondition(condition);
    }





    @Override
    public List<String> getAllSellerId(Integer puid) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        if (puid != null && puid > 0) {
            builder.equalTo("puid", puid);
        }
        return listDistinctFieldByCondition("selling_partner_id", builder.build(), String.class);
    }

    @Override
    public List<VcShopAuth> listBySellerId(String sellerId) {
        String sql = "SELECT * FROM t_vc_shop_auth WHERE selling_partner_id = ? ";
        return getJdbcTemplate().query(sql, getMapper(), sellerId);
    }



    @Override
    public List<Integer> getAllIdByPuid(Integer puid) {
        String sql = "select id from t_vc_shop_auth where puid = ?";
        return getJdbcTemplate().queryForList(sql, new Object[]{puid}, Integer.class);
    }

    @Override
    public List<Integer> getAllVCPuid() {
        String sql = "select puid from t_vc_shop_auth group by puid";
        return getJdbcTemplate().queryForList(sql, Integer.class);
    }

    @Override
    public List<VcShopAuth> listByPuidAndShopIds(Integer puid, List<Integer> vcShopIds) {
        if (CollectionUtils.isEmpty(vcShopIds)) {
            return Lists.newArrayList();
        }

        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("id", vcShopIds.toArray())
                .build();
        return super.listByCondition(condition);
    }



    @Override
    public List<Integer> getAllShopId(Integer puid) {
        String sql = "select id from t_vc_shop_auth where puid=?";
        return getJdbcTemplate().queryForList(sql, new Object[]{puid}, Integer.class);
    }



    /**
     * 爱尔兰站点不支持广告授权sql 中直接剔除，后续支持后去掉
     * @param puid
     * @param sellingPartnerId
     * @param region
     * @return
     */
    @Override
    public List<VcShopAuth> listWaitAdAuthBySeller(int puid, String sellingPartnerId, String region) {
        return listByCondition(new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("selling_partner_id", sellingPartnerId)
                .equalTo("region", region)
                .notEqualTo("marketplace_id", "A28R8C7NBKEWEA")
                .inStrList("ad_status", Arrays.asList(ShopAdStatusEnum.UNAUTH.name(),
                        ShopAdStatusEnum.EXPIRE.name()).toArray(new String[0]))
                .build());
    }







    @Override
    public List<Integer> getShopByMid(int puid, String marketplaceId) {
        StringBuilder sql = new StringBuilder("SELECT id FROM t_vc_shop_auth WHERE puid = ?");
        StringBuilder whereSql = new StringBuilder();
        List<Object> argList = Lists.newArrayList();
        argList.add(puid);
        if (StringUtils.isNotBlank(marketplaceId)) {
            whereSql.append(" and marketplace_id = ?");
            argList.add(marketplaceId);
        }
        whereSql.append(" and ad_status = 'auth'");
        sql.append(whereSql);
        return getJdbcTemplate().queryForList(sql.toString(), Integer.class, argList.toArray());
    }




}