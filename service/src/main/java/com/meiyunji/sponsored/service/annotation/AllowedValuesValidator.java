package com.meiyunji.sponsored.service.annotation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.List;

public class AllowedValuesValidator implements ConstraintValidator<AllowedValues, String> {
    private List<String> allowedValues;
    private boolean allowNull;

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.isEmpty()) {
            return allowNull; // 根据allowNull参数决定是否允许为null
        }
        return allowedValues.contains(value);
    }

    @Override
    public void initialize(AllowedValues constraintAnnotation) {
        this.allowedValues = Arrays.asList(constraintAnnotation.allowedValues());
        this.allowNull = constraintAnnotation.allowNull();
    }
}
