package com.meiyunji.sponsored.service.doris.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProduct;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdProduct;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AdGroupAndAdIdDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AsinCampaignNumDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductSdDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdProductSd;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * amazon SD广告产品表(OdsAmazonAdProductSd)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:19
 */
@Repository
public class OdsAmazonAdProductSdDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdProductSd> implements IOdsAmazonAdProductSdDao {

    @Override
    public List<OdsAmazonAdProductSd> listByShopAsin(Integer puid, List<Integer> shopIdList, List<String> asinList, List<String> adGroupIds) {
        if (CollectionUtils.isEmpty(shopIdList) || CollectionUtils.isEmpty(asinList)) {
            return Collections.emptyList();
        }
        StringBuilder sql = new StringBuilder(" select shop_id, asin, sku, ad_group_id from ods_t_amazon_ad_product_sd where puid = ? ");
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        sql.append(SqlStringUtil.dealInList("asin", asinList, argsList));
        if (CollectionUtils.isNotEmpty(adGroupIds)) {
            sql.append(SqlStringUtil.dealInList("ad_group_id", adGroupIds, argsList));
        }
        sql.append(" group by shop_id, asin, sku, ad_group_id ");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(OdsAmazonAdProductSd.class), argsList.toArray());
    }

    @Override
    public List<OdsAmazonAdProductSd> listByShopParentAsin(Integer puid, List<Integer> shopIdList, List<String> parentAsinList) {
        if (CollectionUtils.isEmpty(shopIdList) || CollectionUtils.isEmpty(parentAsinList)) {
            return Collections.emptyList();
        }
        StringBuilder sql = new StringBuilder();
        List<Object> argsList = new ArrayList<>();

        sql.append(" select ")
                .append(" ap.shop_id shop_id,")
                .append(" ap.sku sku,")
                .append(" if(p.parent_asin is null or p.parent_asin='',p.asin,p.parent_asin) parent_asin");
        sql.append(" from ods_t_amazon_ad_product_sd ap join ods_t_product p")
                .append(" on ap.puid=p.puid and ap.shop_id=p.shop_id and ap.asin=p.asin and ap.sku=p.sku")
                .append(" and p.puid=? ");
        argsList.add(puid);
        sql.append(SqlStringUtil.dealInList("p.shop_id", shopIdList, argsList));
        sql.append(SqlStringUtil.dealInList("if(p.parent_asin is null or p.parent_asin='', p.asin, p.parent_asin)", parentAsinList, argsList));

        sql.append(" where ap.puid=? ");
        argsList.add(puid);
        sql.append(SqlStringUtil.dealInList("ap.shop_id", shopIdList, argsList));

        sql.append(" group by ap.shop_id, ap.sku, if(p.parent_asin is null or p.parent_asin='',p.asin,p.parent_asin) ");

        return getJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            OdsAmazonAdProductSd adProductSd = new OdsAmazonAdProductSd();
            adProductSd.setShopId(rs.getInt("shop_id"));
            adProductSd.setSku(rs.getString("sku"));
            adProductSd.setParentAsin(rs.getString("parent_asin"));
            return adProductSd;
        }, argsList.toArray());
    }

    @Override
    public List<OdsProduct> getOdsProductByIds(Integer puid, String marketplaceId, List<Integer> shopIds, List<Long> idList) {
        if (CollectionUtils.isEmpty(idList) || CollectionUtils.isEmpty(shopIds)) {
            return Collections.emptyList();
        }
        StringBuilder sql = new StringBuilder();
        List<Object> args = new ArrayList<>();

        sql.append(" select ")
                .append(" ap.shop_id, ")
                .append(" p.asin, ")
                .append(" if(p.parent_asin is null or p.parent_asin='',p.asin,p.parent_asin) parent_asin")
                .append(" from ods_t_amazon_ad_product_sd ap join ods_t_product p")
                .append(" on ap.puid=p.puid and ap.marketplace_id=p.marketplace_id and ap.shop_id=p.shop_id and ap.asin=p.asin and ap.sku=p.sku")
                .append(" and p.asin is not null and p.asin!='' and p.puid=? and p.marketplace_id=? ");
        args.add(puid);
        args.add(marketplaceId);
        sql.append(SqlStringUtil.dealInList("p.shop_id", shopIds, args));
        if (idList.size() < 10000) {
            sql.append(SqlStringUtil.dealInList("p.id", idList, args));
        } else {
            sql.append(SqlStringUtil.dealBitMapDorisInList("p.id", idList, args));
        }

        sql.append(" where ap.puid=? and ap.marketplace_id=? ");
        args.add(puid);
        args.add(marketplaceId);
        sql.append(SqlStringUtil.dealInList("ap.shop_id", shopIds, args));
        sql.append(" group by ap.shop_id, p.asin, if(p.parent_asin is null or p.parent_asin='',p.asin,p.parent_asin) ");

        return getJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            OdsProduct product = new OdsProduct();
            product.setShopId(rs.getInt("shop_id"));
            product.setAsin(rs.getString("asin"));
            product.setParentAsin(rs.getString("parent_asin"));
            return product;
        }, args.toArray());
    }

    @Override
    public List<AdGroupAndAdIdDto> getAdIdAndAdGroupIdByAsin(int puid, List<Integer> shopIdList, String asin) {
        StringBuilder sqlBuilder = new StringBuilder("select ad_id, ad_group_id from ods_t_amazon_ad_product_sd where puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        sqlBuilder.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
        sqlBuilder.append(" and asin = ? ");
        args.add(asin);
        return getJdbcTemplate().query(sqlBuilder.toString(), (rs, rowNum) -> {
            AdGroupAndAdIdDto dto = new AdGroupAndAdIdDto();
            dto.setAdGroupId(rs.getString("ad_group_id"));
            dto.setAdId(rs.getString("ad_id"));
            return dto;
        }, args.toArray());
    }

    @Override
    public List<AsinCampaignNumDto> getAsinCampaignNum(int puid, List<Integer> shopIdList, List<String> asinList) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select p.asin, p.shop_id as shopId, count(distinct p.campaign_id) as campaignNum ");
        sql.append(" from ods_t_amazon_ad_product_sd p  ");
        sql.append(" inner join (select puid, shop_id, campaign_id,serving_status from  ods_t_amazon_ad_campaign_all where puid = ? ");
        args.add(puid);
        sql .append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
        sql.append(" ) c on p.puid = c.puid and p.shop_id  = c.shop_id and p.campaign_id  = c.campaign_id  ");
        sql.append(" where p.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("p.shop_id", shopIdList, args));
        sql.append(SqlStringUtil.dealInList("p.asin", asinList, args));
        sql.append(" and c.serving_status in('CAMPAIGN_STATUS_ENABLED', 'RUNNING', 'running','CAMPAIGN_OUT_OF_BUDGET', 'OUT_OF_BUDGET', 'outOfBudget') ");
        sql.append(" group by p.asin, p.shop_id ");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(AsinCampaignNumDto.class), args.toArray());
    }
}

