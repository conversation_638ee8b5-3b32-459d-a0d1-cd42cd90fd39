package com.meiyunji.sponsored.service.cpc.service.impl;

import com.amazon.advertising.invoices.InvoiceResponse;
import com.amazon.advertising.invoices.InvoiceSummaries;
import com.amazon.advertising.invoices.InvoicesClient;
import com.amazon.advertising.invoices.Payload;
import com.amazon.advertising.invoicesDetails.*;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.util.GZipUtils;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdInvoice;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdInvoiceDetails;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdInvoiceSummary;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.LocalDataSourceJobStore;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Created by lm
 * 对接口二次封装，直接和广告接口交互
 */
@Component
@Slf4j
public class AmazonAdInvoiceApiService {

    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonAdInvoiceDao amazonAdInvoiceDao;
    @Autowired
    private IAmazonAdInvoiceDetailsDao amazonAdInvoiceDetailsDao;
    @Autowired
    private IAmazonAdInvoiceSummaryDao amazonAdInvoiceSummaryDao;
    @Resource
    private IShopAuthService shopAuthService;

    @Autowired
    private IAmazonAdInvoiceSyncStateDao amazonAdInvoiceSyncStateDao;

    private final Random random = new Random();

    /**
     *  同步广告信用卡费用
     */
    public void syncInvoiceListResponse(ShopAuth shop) {

        if (shop == null) {
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());

        if (amazonAdProfile == null) {
            log.error("syncSbTargets--配置信息为空");
            return;
        }

        InvoicesClient client = InvoicesClient.getInstance();

        Integer count = 100;
        String nextCursor = null;

        InvoiceResponse response;
        int i = 0;
        while (true) {
            Integer finalCount = count;
            String finalNextCursor = nextCursor;
            i++;
            response = cpcApiHelper.call(shop, () -> client.getListInvoiceResponse(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    null, finalCount, finalNextCursor));
            if (response == null) {
                break;
            }
            if (response.getResult() == null || response.getResult().getPayload() == null) {
                break;
            }

            Payload payload = response.getResult().getPayload();
            nextCursor = payload.getNextCursor();

            List<InvoiceSummaries> invoiceSummaries = payload.getInvoiceSummaries();
            if (CollectionUtils.isNotEmpty(invoiceSummaries)) {
                List<AmazonAdInvoice> adInvoiceList = new ArrayList<>(invoiceSummaries.size());
                AmazonAdInvoice adInvoice;
                for (InvoiceSummaries invoice : invoiceSummaries) {
                    adInvoice = new AmazonAdInvoice();
                    adInvoice.setPuid(shop.getPuid());
                    adInvoice.setShopId(shop.getId());
                    adInvoice.setMarketplaceId(shop.getMarketplaceId());
                    adInvoice.setInvoiceId(invoice.getId());
                    adInvoice.setStatus(invoice.getStatus());
                    adInvoice.setFromDate(invoice.getFromDate());
                    adInvoice.setToDate(invoice.getToDate());
                    adInvoice.setDueDate(invoice.getDueDate());
                    adInvoice.setInvoiceDate(invoice.getInvoiceDate());
                    adInvoice.setPaymentTermsDays(invoice.getPaymentTermsDays());
                    adInvoice.setTaxRate(invoice.getTaxRate());
                    adInvoice.setPurchaseOrderNumber(invoice.getPurchaseOrderNumber());
                    adInvoice.setPaymentTermsType(invoice.getPaymentTermsType());
                    adInvoice.setPaymentMethod(invoice.getPaymentMethod());

                    /**
                     * amountDue+taxAmountDue
                     */
                    BigDecimal taxAmountDue = BigDecimal.ZERO;
                    BigDecimal amountDue = BigDecimal.ZERO;
                    String currencyCode = "";
                    AmznEndpoint byMarketplaceId = AmznEndpoint.getByMarketplaceId(shop.getMarketplaceId());
                    if(byMarketplaceId != null){
                        currencyCode = byMarketplaceId.getCurrencyCode().value();
                    }
                    if (invoice.getTaxAmountDue() != null) {
                        if(invoice.getTaxAmountDue().getAmount() != null){
                            taxAmountDue = BigDecimal.valueOf(invoice.getTaxAmountDue().getAmount());
                        }
                        if(StringUtils.isNotBlank(invoice.getTaxAmountDue().getCurrencyCode())){
                            currencyCode = invoice.getTaxAmountDue().getCurrencyCode();
                        }
                        adInvoice.setTaxAmountDue(GZipUtils.compressStr(JSONUtil.objectToJson(invoice.getTaxAmountDue()),500));
                    }
                    if (invoice.getRemainingTaxAmountDue() != null) {
                        adInvoice.setRemainingTaxAmountDue(GZipUtils.compressStr(JSONUtil.objectToJson(invoice.getRemainingTaxAmountDue()), 500));
                    }
                    if (invoice.getRemainingAmountDue() != null) {
                        adInvoice.setRemainingAmountDue(GZipUtils.compressStr(JSONUtil.objectToJson(invoice.getRemainingAmountDue()), 500));
                    }
                    if (invoice.getAmountDue() != null) {
                        adInvoice.setAmountDue(GZipUtils.compressStr(JSONUtil.objectToJson(invoice.getAmountDue()),500));
                        if(invoice.getAmountDue().getAmount() != null){
                            amountDue =  BigDecimal.valueOf(invoice.getAmountDue().getAmount());
                        }
                        if(StringUtils.isNotBlank(invoice.getAmountDue().getCurrencyCode())){
                            currencyCode = invoice.getAmountDue().getCurrencyCode();
                        }
                    }
                    if (invoice.getDownloadableDocuments() != null) {
                        adInvoice.setDownloadableDocuments(GZipUtils.compressStr(JSONUtil.objectToJson(invoice.getDownloadableDocuments()), 500));
                    }
                    if (invoice.getFees() != null) {
                        adInvoice.setFees(GZipUtils.compressStr(JSONUtil.objectToJson(invoice.getFees()), 500));
                    }
                    if (invoice.getRemainingFees() != null) {
                        adInvoice.setRemainingFees(GZipUtils.compressStr(JSONUtil.objectToJson(invoice.getRemainingFees()), 500));
                    }
                    adInvoice.setInvoiceAmount(MathUtil.add(amountDue,taxAmountDue));
                    adInvoice.setCurrencyCode(currencyCode);
                    adInvoiceList.add(adInvoice);
                }

                try {
                    List<AmazonAdInvoice> collect = adInvoiceList.stream().filter(e -> StringUtils.isNotBlank(e.getInvoiceId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        amazonAdInvoiceDao.insertOrUpdateList(shop.getPuid(), collect);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }


            if (StringUtils.isBlank(nextCursor)) {
                break;
            }
            count = null;
        }

        System.out.println("同步完成,次数为:" + i);
    }

    public void syncInvoicesDetailsResponse(ShopAuth shop, String invoiceId) {

        if (shop == null) {
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());

        if (amazonAdProfile == null) {
            log.error("syncSbTargets--配置信息为空");
            return;
        }

        InvoicesClient client = InvoicesClient.getInstance();

        InvoicesDetailsResponse response = cpcApiHelper.call(shop, () -> client.getInvoiceDetailsResponse(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), invoiceId));

        if (response == null) {
            return;
        }
        InvoicesDetailsResult result = response.getResult();

        if (result != null && result.getPayload() != null) {
            InvoiceSummary invoiceSummary = result.getPayload().getInvoiceSummary();
            if (invoiceSummary != null && !invoiceId.equals(invoiceSummary.getId())) {
                amazonAdInvoiceDao.deleteByInvoiceId(amazonAdProfile.getPuid(), amazonAdProfile.getShopId(), invoiceId);
                amazonAdInvoiceDetailsDao.deleteByInvoiceId(amazonAdProfile.getPuid(), amazonAdProfile.getShopId(), invoiceId);
                amazonAdInvoiceSummaryDao.deleteByInvoiceId(amazonAdProfile.getPuid(), amazonAdProfile.getShopId(), invoiceId);
                return;
            }
            BigDecimal adjustmentsFee = BigDecimal.ZERO;
            PayloadDetails payload = result.getPayload();
            List<AmazonAdInvoiceDetails> detailsList = new ArrayList<>();
            AmazonAdInvoiceDetails details = new AmazonAdInvoiceDetails();
            detailsList.add(details);

            details.setPuid(shop.getPuid());
            details.setShopId(shop.getId());
            details.setMarketplaceId(shop.getMarketplaceId());
            details.setInvoiceId(invoiceId);
            if (payload.getPromotions() != null) {
                details.setPromotions(GZipUtils.compressStr(JSONUtil.objectToJson(payload.getPromotions()),800));
            }
            if (payload.getGovernmentInvoiceInformation() != null) {
                details.setGovernmentInvoiceInformation(GZipUtils.compressStr(JSONUtil.objectToJson(payload.getGovernmentInvoiceInformation()),800));
            }
            if (payload.getPayerContactInformation() != null) {
                details.setPayerContactInformation(GZipUtils.compressStr(JSONUtil.objectToJson(payload.getPayerContactInformation()),800));
            }
            if (payload.getTaxDetail() != null) {
                details.setTaxDetail(GZipUtils.compressStr(JSONUtil.objectToJson(payload.getTaxDetail()),800));
            }
            if (payload.getAdjustments() != null) {
                details.setAdjustments(GZipUtils.compressStr(JSONUtil.objectToJson(payload.getAdjustments()),800));
            }
            if (payload.getInvoiceLines() != null) {
                details.setInvoiceLines(GZipUtils.compressStr(JSONUtil.objectToJson(payload.getInvoiceLines()),800));
            }
            if (payload.getIssuerContactInformation() != null) {
                details.setIssuerContactInformation(GZipUtils.compressStr(JSONUtil.objectToJson(payload.getIssuerContactInformation()),800));
            }
            if (payload.getThirdPartyContactInformation() != null) {
                details.setThirdPartyContactInformation(GZipUtils.compressStr(JSONUtil.objectToJson(payload.getThirdPartyContactInformation()),800));
            }
            if (payload.getPayments() != null) {
                details.setPayments(GZipUtils.compressStr(JSONUtil.objectToJson(payload.getPayments()),800));
            }
            if (payload.getPortfolios() != null) {
                details.setPortfolios(GZipUtils.compressStr(JSONUtil.objectToJson(payload.getPortfolios()),800));
            }

            try {
                amazonAdInvoiceDetailsDao.insertOrUpdateList(shop.getPuid(), detailsList);
            } catch (Exception e) {
                e.printStackTrace();
                return;
            }

            if (invoiceSummary != null) {
                List<AmazonAdInvoiceSummary> list = new ArrayList<>();
                AmazonAdInvoiceSummary summary = new AmazonAdInvoiceSummary();
                list.add(summary);

                summary.setPuid(shop.getPuid());
                summary.setShopId(shop.getId());
                summary.setMarketplaceId(shop.getMarketplaceId());
                summary.setInvoiceId(invoiceSummary.getId());
                summary.setStatus(invoiceSummary.getStatus());
                summary.setFromDate(invoiceSummary.getFromDate());
                summary.setToDate(invoiceSummary.getToDate());
                summary.setDueDate(invoiceSummary.getDueDate());
                summary.setInvoiceDate(invoiceSummary.getInvoiceDate());
                summary.setPaymentTermsDays(invoiceSummary.getPaymentTermsDays());
                summary.setTaxRate(invoiceSummary.getTaxRate());
                summary.setPurchaseOrderNumber(invoiceSummary.getPurchaseOrderNumber());
                summary.setPaymentTermsType(invoiceSummary.getPaymentTermsType());
                summary.setPaymentMethod(invoiceSummary.getPaymentMethod());
                /**
                 * amountDue+taxAmountDue
                 */
                BigDecimal taxAmountDue = BigDecimal.ZERO;
                BigDecimal amountDue = BigDecimal.ZERO;

                String currencyCode = "";
                AmznEndpoint byMarketplaceId = AmznEndpoint.getByMarketplaceId(shop.getMarketplaceId());
                if(byMarketplaceId != null){
                    currencyCode = byMarketplaceId.getCurrencyCode().value();
                }
                if (invoiceSummary.getTaxAmountDue() != null) {
                    if(invoiceSummary.getTaxAmountDue().getAmount() != null){
                        taxAmountDue = BigDecimal.valueOf(invoiceSummary.getTaxAmountDue().getAmount());
                    }
                    if(StringUtils.isNotBlank(invoiceSummary.getTaxAmountDue().getCurrencyCode())){
                        currencyCode = invoiceSummary.getTaxAmountDue().getCurrencyCode();
                    }
                    summary.setTaxAmountDue(GZipUtils.compressStr(JSONUtil.objectToJson(invoiceSummary.getTaxAmountDue()),500));
                }
                if (invoiceSummary.getRemainingTaxAmountDue() != null) {
                    summary.setRemainingTaxAmountDue(GZipUtils.compressStr(JSONUtil.objectToJson(invoiceSummary.getRemainingTaxAmountDue()), 500));
                }
                if (invoiceSummary.getRemainingAmountDue() != null) {
                    summary.setRemainingAmountDue(GZipUtils.compressStr(JSONUtil.objectToJson(invoiceSummary.getRemainingAmountDue()), 500));
                }
                if (invoiceSummary.getAmountDue() != null) {
                    if(invoiceSummary.getAmountDue().getAmount() != null){
                        amountDue = BigDecimal.valueOf(invoiceSummary.getAmountDue().getAmount());
                    }
                    if(StringUtils.isNotBlank(invoiceSummary.getAmountDue().getCurrencyCode())){
                        currencyCode = invoiceSummary.getAmountDue().getCurrencyCode();
                    }
                    summary.setAmountDue(GZipUtils.compressStr(JSONUtil.objectToJson(invoiceSummary.getAmountDue()),500));
                }
                if (invoiceSummary.getDownloadableDocuments() != null) {
                    summary.setDownloadableDocuments(GZipUtils.compressStr(JSONUtil.objectToJson(invoiceSummary.getDownloadableDocuments()), 500));
                }
                if (invoiceSummary.getFees() != null) {
                    summary.setFees(GZipUtils.compressStr(JSONUtil.objectToJson(invoiceSummary.getFees()), 500));
                }
                if (invoiceSummary.getRemainingFees() != null) {
                    summary.setRemainingFees(GZipUtils.compressStr(JSONUtil.objectToJson(invoiceSummary.getRemainingFees()), 500));
                }

                summary.setInvoiceAmount(MathUtil.add(amountDue,taxAmountDue));
                summary.setCurrencyCode(currencyCode);
                amazonAdInvoiceSummaryDao.insertOrUpdateList(shop.getPuid(), list);
                LocalDateTime localDateTime = LocalDateTime.now().plusDays(1);
                boolean isSync = true;
                if(invoiceSummary != null && StringUtils.isNotBlank(invoiceSummary.getInvoiceDate())){
                    LocalDate parse = LocalDate.parse(invoiceSummary.getInvoiceDate(), DateTimeFormatter.BASIC_ISO_DATE);
                    LocalDate localDate = LocalDate.now();
                    long until = parse.until(localDate, ChronoUnit.DAYS);
                    if(until > 7){
                        localDateTime = localDateTime.plusDays(2);
                    }
                    if(until > 31){
                        isSync = false;
                    }
                }
                amazonAdInvoiceDao.updateNextSyncTime(shop.getPuid(),shop.getId(),invoiceId, localDateTime,isSync,invoiceSummary.getPaymentMethod());
            }

        }
    }


    /**
     * 同步广告信用卡费用
     */
    public void syncInvoiceList(ShopAuth shop, AmazonAdInvoiceSyncState amazonAdInvoiceSyncState) {

        if (shop == null) {
            amazonAdInvoiceSyncStateDao.updateNextTokenAndNextSyncTimeAndIsInitialize(null, LocalDateTime.now().plusDays(2), null, amazonAdInvoiceSyncState.getId(), false);
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());

        if (amazonAdProfile == null) {
            amazonAdInvoiceSyncStateDao.deleteById(amazonAdInvoiceSyncState.getId());
            log.error("syncSbTargets--配置信息为空");
            return;
        }

        InvoicesClient client = InvoicesClient.getInstance();
        LocalDate initDate = amazonAdInvoiceSyncState.getInitDate();
        Integer count = 100;
        String nextCursor = null;
        if (StringUtils.isNotBlank(amazonAdInvoiceSyncState.getNextToken())) {
            nextCursor = amazonAdInvoiceSyncState.getNextToken();
            count = null;
        }

        LocalDate date = LocalDate.now().plusDays(-7);
        InvoiceResponse response;
        int i = 0;
        int retryCount = 0;
        while (true) {
            Integer finalCount = count;
            String finalNextCursor = nextCursor;
            i++;
            response = cpcApiHelper.call(shop, () -> client.getListInvoiceResponse(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    null, finalCount, finalNextCursor));


            if (response == null) {
                amazonAdInvoiceSyncStateDao.updateNextSyncTime(LocalDateTime.now().plusHours(6).plusSeconds(random.nextInt(180)), amazonAdInvoiceSyncState.getId());
                break;
            } else if (response.getStatusCode() == 429) {
                amazonAdInvoiceSyncStateDao.updateNextSyncTime(LocalDateTime.now().plusMinutes(6).plusSeconds(random.nextInt(180)), amazonAdInvoiceSyncState.getId());
                break;
            } else if (response.getStatusCode() == 400) {
                amazonAdInvoiceSyncStateDao.updateNextSyncTimeAndNextToken(LocalDateTime.now().plusMinutes(6).plusSeconds(random.nextInt(180)), null, amazonAdInvoiceSyncState.getId());
                break;
            } else if (response.getStatusCode() == 504) {
                count = 50;
                if (retryCount >= 2) {
                    amazonAdInvoiceSyncStateDao.updateNextSyncTime(LocalDateTime.now().plusMinutes(6).plusSeconds(random.nextInt(180)), amazonAdInvoiceSyncState.getId());
                    break;
                }
                retryCount ++;
                continue;
            } else if (response.getStatusCode() != 200) {
                amazonAdInvoiceSyncStateDao.updateNextSyncTime(LocalDateTime.now().plusMinutes(6).plusSeconds(random.nextInt(180)), amazonAdInvoiceSyncState.getId());
                break;
            } else if (response.getResult() == null || response.getResult().getPayload() == null) {
                amazonAdInvoiceSyncStateDao.updateNextSyncTime(LocalDateTime.now().plusHours(6).plusSeconds(random.nextInt(180)), amazonAdInvoiceSyncState.getId());
                break;
            }


            Payload payload = response.getResult().getPayload();
            nextCursor = payload.getNextCursor();

            List<InvoiceSummaries> invoiceSummaries = payload.getInvoiceSummaries();
            LocalDate minInvoiceDate = LocalDate.now();
            if (CollectionUtils.isNotEmpty(invoiceSummaries)) {
                String invoiceDate = invoiceSummaries.get(invoiceSummaries.size() - 1).getInvoiceDate();
                if (StringUtils.isNotBlank(invoiceDate)) {
                    minInvoiceDate = LocalDate.parse(invoiceDate, DateTimeFormatter.BASIC_ISO_DATE);
                }
                List<AmazonAdInvoice> adInvoiceList = new ArrayList<>(invoiceSummaries.size());
                AmazonAdInvoice adInvoice;
                for (InvoiceSummaries invoice : invoiceSummaries) {
                    adInvoice = new AmazonAdInvoice();
                    adInvoice.setPuid(shop.getPuid());
                    adInvoice.setShopId(shop.getId());
                    adInvoice.setMarketplaceId(shop.getMarketplaceId());
                    adInvoice.setInvoiceId(invoice.getId());
                    adInvoice.setStatus(invoice.getStatus());
                    adInvoice.setFromDate(invoice.getFromDate());
                    adInvoice.setToDate(invoice.getToDate());
                    adInvoice.setDueDate(invoice.getDueDate());
                    adInvoice.setInvoiceDate(invoice.getInvoiceDate());
                    adInvoice.setPaymentTermsDays(invoice.getPaymentTermsDays());
                    adInvoice.setTaxRate(invoice.getTaxRate());
                    adInvoice.setPurchaseOrderNumber(invoice.getPurchaseOrderNumber());
                    adInvoice.setPaymentTermsType(invoice.getPaymentTermsType());
                    adInvoice.setPaymentMethod(invoice.getPaymentMethod());

                    /**
                     * amountDue+taxAmountDue
                     */
                    BigDecimal taxAmountDue = BigDecimal.ZERO;
                    BigDecimal amountDue = BigDecimal.ZERO;
                    String currencyCode = "";
                    AmznEndpoint byMarketplaceId = AmznEndpoint.getByMarketplaceId(shop.getMarketplaceId());
                    if (byMarketplaceId != null) {
                        currencyCode = byMarketplaceId.getCurrencyCode().value();
                    }
                    if (invoice.getTaxAmountDue() != null) {
                        if (invoice.getTaxAmountDue().getAmount() != null) {
                            taxAmountDue = BigDecimal.valueOf(invoice.getTaxAmountDue().getAmount());
                        }
                        if (StringUtils.isNotBlank(invoice.getTaxAmountDue().getCurrencyCode())) {
                            currencyCode = invoice.getTaxAmountDue().getCurrencyCode();
                        }
                        adInvoice.setTaxAmountDue(GZipUtils.compressStr(JSONUtil.objectToJson(invoice.getTaxAmountDue()), 500));
                    }
                    if (invoice.getRemainingTaxAmountDue() != null) {
                        adInvoice.setRemainingTaxAmountDue(GZipUtils.compressStr(JSONUtil.objectToJson(invoice.getRemainingTaxAmountDue()), 500));
                    }
                    if (invoice.getRemainingAmountDue() != null) {
                        adInvoice.setRemainingAmountDue(GZipUtils.compressStr(JSONUtil.objectToJson(invoice.getRemainingAmountDue()), 500));
                    }
                    if (invoice.getAmountDue() != null) {
                        adInvoice.setAmountDue(GZipUtils.compressStr(JSONUtil.objectToJson(invoice.getAmountDue()), 500));
                        if (invoice.getAmountDue().getAmount() != null) {
                            amountDue = BigDecimal.valueOf(invoice.getAmountDue().getAmount());
                        }
                        if (StringUtils.isNotBlank(invoice.getAmountDue().getCurrencyCode())) {
                            currencyCode = invoice.getAmountDue().getCurrencyCode();
                        }
                    }
                    if (invoice.getDownloadableDocuments() != null) {
                        adInvoice.setDownloadableDocuments(GZipUtils.compressStr(JSONUtil.objectToJson(invoice.getDownloadableDocuments()), 500));
                    }
                    if (invoice.getFees() != null) {
                        adInvoice.setFees(GZipUtils.compressStr(JSONUtil.objectToJson(invoice.getFees()), 500));
                    }
                    if (invoice.getRemainingFees() != null) {
                        adInvoice.setRemainingFees(GZipUtils.compressStr(JSONUtil.objectToJson(invoice.getRemainingFees()), 500));
                    }
                    adInvoice.setInvoiceAmount(MathUtil.add(amountDue, taxAmountDue));
                    adInvoice.setCurrencyCode(currencyCode);
                    adInvoiceList.add(adInvoice);
                }

                List<AmazonAdInvoice> collect = adInvoiceList.stream().filter(e -> StringUtils.isNotBlank(e.getInvoiceId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    amazonAdInvoiceDao.insertOrUpdateList(shop.getPuid(), collect);
                }

            }
            if (StringUtils.isBlank(nextCursor)) {
                amazonAdInvoiceSyncStateDao.updateNextTokenAndNextSyncTimeAndIsInitialize("", LocalDateTime.now().plusHours(6).plusMinutes(random.nextInt(120)), true, amazonAdInvoiceSyncState.getId(), true);
                break;
            }

            if (amazonAdInvoiceSyncState.getIsInitialize() && date.isAfter(minInvoiceDate)) {
                amazonAdInvoiceSyncStateDao.updateNextSyncTimeAndNextToken(LocalDateTime.now().plusHours(6).plusMinutes(random.nextInt(120)), "", amazonAdInvoiceSyncState.getId());
                break;
            }


            if (!amazonAdInvoiceSyncState.getIsInitialize() && minInvoiceDate.isBefore(initDate)) {
                amazonAdInvoiceSyncStateDao.updateNextTokenAndNextSyncTimeAndIsInitialize("", LocalDateTime.now().plusHours(6).plusMinutes(random.nextInt(120)), true, amazonAdInvoiceSyncState.getId(), true);
                break;
            }

            amazonAdInvoiceSyncStateDao.updateNextToken(nextCursor, amazonAdInvoiceSyncState.getId());
            count = null;
        }


    }

}
