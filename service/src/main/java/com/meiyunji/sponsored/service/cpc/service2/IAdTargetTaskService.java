package com.meiyunji.sponsored.service.cpc.service2;

import com.meiyunji.sponsored.grpc.adTargetTask.AdTargetTaskOuterClass;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetTaskDto;
import com.meiyunji.sponsored.service.cpc.po.AdTargetTaskDetail;

import java.util.List;

public interface IAdTargetTaskService {
    /**
     * 记录投放任务
     *
     * @param adTargetTaskDto
     * @return 主任务id
     */
    long recordTargetTask(AdTargetTaskDto adTargetTaskDto);

    /**
     * 任务列表(只能看到近7天的数据)
     *
     * @param puid
     * @param pageNo
     * @param pageSize
     * @return
     */
    AdTargetTaskOuterClass.AdTargetTaskPage getAdTargetTaskPage(int puid, int sourceShopId, int pageNo, int pageSize, int targetPageType, String sourceAdCampaignId, int uid);

    /**
     * 根据任务id列表获取任务列表(只能看到近7天的数据)
     *
     * @param puid
     * @param taskIds
     * @return
     */
    List<AdTargetTaskOuterClass.AdTargetTask> getAdTargetTaskByTaskIds(int puid, List<Long> taskIds);


    /**
     * 失败任务详情列表
     *
     * @param taskId
     * @param puid
     * @param pageNo
     * @param pageSize
     * @return
     */
    AdTargetTaskOuterClass.AdTargetTaskDetailPage getFailAdTargetTaskDetailPage(long taskId, int puid, int pageNo, int pageSize);

    /**
     * 投放词列表
     *
     * @param taskId
     * @param puid
     * @param pageNo
     * @param pageSize
     * @return
     */
    AdTargetTaskOuterClass.TargetObjectDetailPage getTargetObjectDetailPage(long taskId, int puid, int pageNo, int pageSize);

    /**
     * 任务详情列表
     *
     * @param taskId
     * @param puid
     * @return
     */
    List<AdTargetTaskDetail> getAdTargetTaskDetailList(long taskId, int puid);

    /**
     * 删除过期的主任务
     */
    void deleteExpiredRecords();

    /**
     * 删除没有任务详情的任务
     * 重启创建时间超过30分钟且没有执行结果的任务
     */
    void removeOrRestartAbnormalRecords();

    /**
     * 通用执行任务接口,根据任务的情况选择线程池或kafka消息的方式去执行
     *
     * @param taskId
     */
    void executeTask(long taskId);

    /**
     * 实际执行任务接口
     *
     * @param taskId
     */
    void doExecuteTask(long taskId);

    /**
     * 一键重试指定任务中的失败条目
     *
     * @param puid
     * @param taskId
     */
    String retry(int puid, long taskId);
}
