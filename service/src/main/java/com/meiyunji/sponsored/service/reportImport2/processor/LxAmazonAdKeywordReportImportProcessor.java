package com.meiyunji.sponsored.service.reportImport2.processor;

import com.alibaba.fastjson.JSONReader;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.util.GZipUtils;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdGroupReport;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdKeywordReport;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdProductTargetReport;
import com.meiyunji.sponsored.service.reportImport2.vo.AmazonAdReportImportMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * lx活动报告导入处理器
 *
 * <AUTHOR>
 * @date 2023/05/26
 */
@Service
@Slf4j
public class LxAmazonAdKeywordReportImportProcessor extends AbstractAmazonAdLxReportImportProcessor<LxAmazonAdGroupReport> {


    private final IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    private final IAmazonAdKeywordReportDao amazonAdKeywordReportDao;
    private final IAmazonAdSbKeywordReportDao amazonAdSbKeywordReportDao;
    private final IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;


    protected LxAmazonAdKeywordReportImportProcessor(IAmazonAdCampaignAllDao amazonAdCampaignAllDao, IAmazonAdGroupDao amazonAdGroupDao,
                                                     IAmazonSdAdGroupDao amazonSdAdGroupDao, IAmazonSbAdGroupDao amazonSbAdGroupDao,
                                                     IScVcShopAuthDao shopAuthDao, CosBucketClient tempBucketClient,
                                                     IAmazonSbAdKeywordDao amazonSbAdKeywordDao,
                                                     IAmazonAdKeywordReportDao amazonAdKeywordReportDao,
                                                     IAmazonAdSbKeywordReportDao amazonAdSbKeywordReportDao,
                                                     IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService) {
        super(amazonAdCampaignAllDao, amazonAdGroupDao, amazonSdAdGroupDao, amazonSbAdGroupDao, shopAuthDao, tempBucketClient);
        this.amazonSbAdKeywordDao = amazonSbAdKeywordDao;
        this.amazonAdKeywordReportDao = amazonAdKeywordReportDao;
        this.amazonAdSbKeywordReportDao = amazonAdSbKeywordReportDao;
        this.amazonAdKeywordDaoRoutingService = amazonAdKeywordDaoRoutingService;
    }


    @Override
    public void importReport(AmazonAdReportImportMessage importMessage) {

        //读取数据
        try (InputStreamReader inputStreamReader = new InputStreamReader(new ByteArrayInputStream(GZipUtils.release(tempBucketClient.getObjectToBytes(importMessage.getFileId().replaceFirst("/", "")))))) {
            ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(importMessage.getShopId(), importMessage.getPuid());
            JSONReader jsonReader = new JSONReader(inputStreamReader);
            jsonReader.startArray();
            List<LxAmazonAdKeywordReport> reports = Lists.newArrayListWithExpectedSize(500);
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                LxAmazonAdKeywordReport report = new LxAmazonAdKeywordReport();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.getImpressions() != 0) {
                    reports.add(report);
                    if (reports.size() >= 500) {
                        dealReport(importMessage, reports, shopAuth);
                        reports = Lists.newArrayListWithExpectedSize(500);
                    }
                }

            }
            jsonReader.endArray();
            jsonReader.close();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReport(importMessage, reports, shopAuth);
            }
        } catch (Exception e) {
            log.error("import campaign report error puid:{},shopId：{}，scheduleId :{} error:", importMessage.getPuid(), importMessage.getShopId(), importMessage.getScheduleId(), e);
        }

    }

    private void dealReport(AmazonAdReportImportMessage importMessage, List<LxAmazonAdKeywordReport> reports, ShopAuth shopAuth) {
        Integer puid = importMessage.getPuid();
        Integer shopId = importMessage.getShopId();

        String adType = importMessage.getAdType().toLowerCase();
        List<String> campaignIds = reports.stream().map(LxAmazonAdKeywordReport::getCampaignId).collect(Collectors.toList());
        List<AmazonAdCampaignAll> byCampaignIds = amazonAdCampaignAllDao.getByCampaignIds(puid, shopId, shopAuth.getMarketplaceId(), campaignIds, adType);
        List<String> keywordIds = reports.stream().map(LxAmazonAdKeywordReport::getKeywordId).collect(Collectors.toList());

        Map<String, AmazonAdKeyword> spKeyword = new HashMap<>();
        Map<String, AmazonSbAdKeyword> sbKeyword = new HashMap<>();

        if (CampaignTypeEnum.sp.name().equalsIgnoreCase(importMessage.getAdType())) {
            spKeyword = (Map<String, AmazonAdKeyword>) amazonAdKeywordDaoRoutingService.getByKeywordIds(puid, shopId, keywordIds, Constants.BIDDABLE).stream().collect(Collectors.toMap(AmazonAdKeyword::getKeywordId, Function.identity(), (e1, e2) -> e2));

        } else if (CampaignTypeEnum.sb.name().equalsIgnoreCase(importMessage.getAdType())) {
            sbKeyword = amazonSbAdKeywordDao.listByKeywordId(puid, shopId, keywordIds).stream().collect(Collectors.toMap(AmazonSbAdKeyword::getKeywordId, Function.identity(), (e1, e2) -> e2));
        }
        Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = byCampaignIds.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));


        List<AmazonAdKeywordReport> spReports = new ArrayList<>();
        List<AmazonAdSbKeywordReport> sbReports = new ArrayList<>();
        List<AmazonAdSdTargetingReport> sdReports = new ArrayList<>();
        Map<String, AmazonAdKeyword> finalSpKeyword = spKeyword;
        Map<String, AmazonSbAdKeyword> finalSbKeyword = sbKeyword;
        reports.forEach(e -> {
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(e.getCampaignId());
            if (amazonAdCampaignAll == null) {
                log.error("pxq-report-import puid : {} shop_id : {} campaignId : {} 不存在", puid, shopId, e.getCampaignId());
                return;
            }


            if (CampaignTypeEnum.sp.name().equalsIgnoreCase(importMessage.getAdType())) {
                AmazonAdKeyword keyword = finalSpKeyword.get(e.getKeywordId());
                if (keyword == null) {
                    log.error("pxq-report-import puid : {} shop_id : {} campaignId : {}, groupId:{}, keywordId: {} 不存在", puid, shopId, e.getCampaignId(), e.getAdGroupId(), e.getKeywordId());
                    return;
                }
                AmazonAdKeywordReport spReport = buildSpAdKeywordReport(importMessage.getCountDate(), e, keyword, amazonAdCampaignAll, shopAuth);
                spReports.add(spReport);
            } else if (CampaignTypeEnum.sb.name().equalsIgnoreCase(importMessage.getAdType())) {
                AmazonSbAdKeyword keyword = finalSbKeyword.get(e.getKeywordId());
                if (keyword == null) {
                    log.error("pxq-report-import puid : {} shop_id : {} campaignId : {}, kewordId:{} 不存在", puid, shopId, e.getCampaignId(), e.getKeywordId());
                    return;
                }
                AmazonAdSbKeywordReport sbReport = buildSbAdKeywordReport(importMessage.getCountDate(), e, keyword, amazonAdCampaignAll, shopAuth);
                sbReports.add(sbReport);
            }
        });


        //持久数据到数据库
        if (CollectionUtils.isNotEmpty(spReports)) {
            amazonAdKeywordReportDao.insertList(puid, spReports);
        }

        if (CollectionUtils.isNotEmpty(sbReports)) {
            amazonAdSbKeywordReportDao.insertOrUpdateList(puid, sbReports);
        }

    }


    /**
     * 构建sb广告组报告
     *
     * @param report            报告
     * @param amazonSbAdKeyword 亚马逊sb广告组
     * @return {@link AmazonAdSbGroupReport}
     */
    private AmazonAdSbKeywordReport buildSbAdKeywordReport(String countDate, LxAmazonAdKeywordReport report, AmazonSbAdKeyword amazonSbAdKeyword, AmazonAdCampaignAll campaignAll, ShopAuth shopAuth) {
        AmazonAdSbKeywordReport sbKeywordReport = new AmazonAdSbKeywordReport();
        sbKeywordReport.setPuid(shopAuth.getPuid());
        sbKeywordReport.setShopId(shopAuth.getId());
        sbKeywordReport.setMarketplaceId(shopAuth.getMarketplaceId());
        sbKeywordReport.setCountDate(countDate);
        sbKeywordReport.setCampaignName(report.getCampaignName());
        sbKeywordReport.setCampaignId(report.getCampaignId());
        sbKeywordReport.setAdGroupName(report.getAdGroupName());
        sbKeywordReport.setAdGroupId(report.getAdGroupId());
        sbKeywordReport.setKeywordText(amazonSbAdKeyword.getKeywordText());
        sbKeywordReport.setKeywordBid(amazonSbAdKeyword.getBid() != null ? amazonSbAdKeyword.getBid().doubleValue() : null);
        sbKeywordReport.setKeywordStatus(amazonSbAdKeyword.getState());
        sbKeywordReport.setKeywordId(amazonSbAdKeyword.getKeywordId());
        sbKeywordReport.setMatchType(amazonSbAdKeyword.getMatchType());
        sbKeywordReport.setAdFormat(Optional.ofNullable(campaignAll.getAdFormat()).orElse("manual"));
        sbKeywordReport.setSales14d(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        sbKeywordReport.setSales14dSameSKU(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);
        sbKeywordReport.setConversions14d(report.getOrders() != null ? report.getOrders() : 0);
        sbKeywordReport.setConversions14dSameSKU(report.getDirectOrders() != null ? report.getDirectOrders() : 0);

        sbKeywordReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        sbKeywordReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        sbKeywordReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);

        sbKeywordReport.setUnitsSold14d(report.getAdUnits() != null ? report.getAdUnits() : 0);


        //未定义
//        sbGroupReport.setDpv14d(isDxmNumeric(report.getDpv()) ? Integer.valueOf(report.getDpv()) : 0);
//
//        try {
//            sbGroupReport.setVideo5SecondViewRate(new BigDecimal(report.getVideo5SecondViewRate().replaceAll("%", "")).doubleValue());
//        } catch (Exception exception) {
//            log.error("buildSbAdGroupReport execute setVideo5SecondViewRate errorMsg: {}", exception.getMessage());
//        }
//        sbGroupReport.setVideo5SecondViews(isDxmNumeric(report.getVideo5SecondViews()) ? Integer.valueOf(report.getVideo5SecondViews()) : 0);
//        sbGroupReport.setVideoFirstQuartileViews(isDxmNumeric(report.getVideoFirstQuartileViews()) ? Integer.valueOf(report.getVideoFirstQuartileViews()) : 0);
//        sbGroupReport.setVideoMidpointViews(isDxmNumeric(report.getVideoMidpointViews()) ? Integer.valueOf(report.getVideoMidpointViews()) : 0);
//        sbGroupReport.setVideoThirdQuartileViews(isDxmNumeric(report.getVideoThirdQuartileViews()) ? Integer.valueOf(report.getVideoThirdQuartileViews()) : 0);
//        sbGroupReport.setVideoUnmutes(isDxmNumeric(report.getVideoUnmutes()) ? Integer.valueOf(report.getVideoUnmutes()) : 0);
//        sbGroupReport.setViewableImpressions(isDxmNumeric(report.getViewImpressions()) ? Integer.valueOf(report.getViewImpressions()) : null);
//        sbGroupReport.setVideoCompleteViews(isDxmNumeric(report.getVideoCompleteViews()) ? Integer.valueOf(report.getVideoCompleteViews()) : 0);
//
//
//        try {
//            sbGroupReport.setVctr(new BigDecimal(report.getVctr().replaceAll("%", "")).doubleValue());
//        } catch (Exception exception) {
//            log.error("buildSbAdGroupReport execute setVctr errorMsg: {}", exception.getMessage());
//        }
//
//        try {
//            sbGroupReport.setVtr(new BigDecimal(report.getVtr().replaceAll("%", "")).doubleValue());
//        } catch (Exception exception) {
//            log.error("buildSbAdGroupReport execute setVtr errorMsg: {}", exception.getMessage());
//        }

        return sbKeywordReport;
    }

    /**
     * 构建sp广告组报告
     *
     * @param report 报告
     * @return {@link AmazonAdKeywordReport}
     */
    private AmazonAdKeywordReport buildSpAdKeywordReport(String countDate, LxAmazonAdKeywordReport report, AmazonAdKeyword amazonAdKeyword, AmazonAdCampaignAll campaignAll, ShopAuth shopAuth) {
        AmazonAdKeywordReport amazonAdKeywordReport = new AmazonAdKeywordReport();
        amazonAdKeywordReport.setPuid(shopAuth.getPuid());
        amazonAdKeywordReport.setShopId(shopAuth.getId());
        amazonAdKeywordReport.setMarketplaceId(shopAuth.getMarketplaceId());
        amazonAdKeywordReport.setCountDate(countDate);
        amazonAdKeywordReport.setCampaignId(report.getCampaignId());
        amazonAdKeywordReport.setAdGroupId(report.getAdGroupId());
        amazonAdKeywordReport.setAdGroupName(report.getAdGroupName());
        amazonAdKeywordReport.setCampaignName(campaignAll.getName());
        amazonAdKeywordReport.setKeywordText(amazonAdKeyword.getKeywordText());
        amazonAdKeywordReport.setMatchType(amazonAdKeyword.getMatchType());
        amazonAdKeywordReport.setAdGroupName(report.getAdGroupName());
        amazonAdKeywordReport.setKeywordId(report.getKeywordId());

        amazonAdKeywordReport.setTotalSales(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        amazonAdKeywordReport.setAdSales(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);


        amazonAdKeywordReport.setOrderNum(report.getAdUnits() != null ? report.getAdUnits() : 0);
        amazonAdKeywordReport.setAdOrderNum(report.getDirectUnits() != null ? report.getDirectUnits() : 0);
        amazonAdKeywordReport.setSaleNum(report.getOrders() != null ? report.getOrders() : 0);
        amazonAdKeywordReport.setAdSaleNum(report.getDirectOrders() != null ? report.getDirectOrders() : 0);

        amazonAdKeywordReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        amazonAdKeywordReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        amazonAdKeywordReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);

        return amazonAdKeywordReport;
    }


}
