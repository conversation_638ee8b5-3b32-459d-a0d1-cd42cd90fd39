package com.meiyunji.sponsored.service.cpc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.protobuf.BoolValue;
import com.google.protobuf.DoubleValue;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.AmazonAd;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeAggregateDataRpcVo;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeChartRpcVo;
import com.meiyunji.sponsored.rpc.adCommon.AllQueryWordAggregateDataResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllQueryWordDataResponse;
import com.meiyunji.sponsored.rpc.vo.ReportRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDao;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.common.qo.WordTranslateQo;
import com.meiyunji.sponsored.service.common.service.IWordTranslateService;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdQueryStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdTargetStrategyTypeEnum;
import com.meiyunji.sponsored.service.export.dto.DownloadCenterCampaignBaseDataBO;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.ICpcSbQueryKeywordReportService;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdCampaignAllService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.dataWarehouse.service.SearchAnalysisStatsV2Client;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdKeywordSbDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdNeKeywordSbDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsCpcSbQueryKeywordReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonWordRootQueryDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdKeywordSb;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdNeKeywordSb;
import com.meiyunji.sponsored.service.doris.po.OdsWeekSearchTermsAnalysis;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.export.ReportFillBaseDataHelper;
import com.meiyunji.sponsored.service.searchAnalysis.po.SearchTermsAnalysis;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.searchTermsAnalysis.WeekSearchTermsAnalysisService;
import com.meiyunji.sponsored.service.util.GrayUtil;
import com.meiyunji.sponsored.service.wordFrequency.dao.IWordRootKeywordSbDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CpcSbQueryKeywordReportServiceImpl implements ICpcSbQueryKeywordReportService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private ICpcSbQueryKeywordReportDao cpcSbQueryKeywordReportDao;
    @Autowired
    private IOdsCpcSbQueryKeywordReportDao odsCpcSbQueryKeywordReportDao;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Autowired
    private IAmazonSbAdCampaignDao amazonAdSbCampaignDao;

    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;

    @Autowired
    private AdChartDataProcess adChartDataProcess;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private CpcShopDataService cpCShopDataService;

    @Autowired
    private IAmazonAdCampaignDao amazonAdCampaignDao;

    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private IAmazonAdCampaignAllService amazonAdCampaignAllService;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;

    @Autowired
    private IAmazonSbAdNeKeywordDao amazonSbAdNeKeywordDao;

    @Autowired
    private SearchAnalysisStatsV2Client searchAnalysisStatsV2Client;

    @Autowired
    private IWordRootKeywordSbDao wordRootKeywordSbDao;
    @Autowired
    private IOdsAmazonWordRootQueryDao odsAmazonWordRootQueryDao;
    @Autowired
    private IOdsAmazonAdKeywordSbDao odsAmazonAdKeywordSbDao;
    @Autowired
    private IOdsAmazonAdNeKeywordSbDao odsAmazonAdNeKeywordSbDao;
    @Autowired
    private WeekSearchTermsAnalysisService weekSearchTermsAnalysisService;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;
    @Autowired
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;

    @Autowired
    private ReportFillBaseDataHelper reportFillBaseDataHelper;
    @Autowired
    private IWordTranslateService wordTranslateService;

    @Override
    public Page pageList(Integer puid, CpcQueryWordDto dto, Page page) {

        page = cpcSbQueryKeywordReportDao.pageList(puid, dto, page);

        List<CpcSbQueryKeywordReport> poList = page.getRows();
        if (poList != null && poList.size() > 0) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
            BigDecimal sumRange = shopSaleDto == null ? BigDecimal.ZERO : shopSaleDto.getSumRange();
            poList.forEach(e -> {
                ReportVo vo = getVo(e, sumRange);
                vo.setQuery(e.getQuery());
                vo.setKeywordText(e.getKeywordText());
                vo.setMatchType(e.getMatchType());
                vo.setCampaignId(e.getCampaignId());
                vo.setCampaignName(e.getCampaignName());
                vo.setAdGroupId(e.getAdGroupId());
                vo.setAdGroupName(e.getAdGroupName());
                vo.setKeywordId(e.getKeywordId());
                vo.setTargetingType(Constants.MANUAL); //关键词应该只有手动
                //检查该搜索词是否已经加入到否定
                AmazonSbAdKeyword keyword = amazonSbAdKeywordDao.getByKeywordText(puid, dto.getShopId(), dto.getMarketplaceId(), e.getCampaignId(), e.getAdGroupId(), e.getQuery(), Constants.NEGATIVE);
                if (keyword != null && !Constants.ARCHIVED.equalsIgnoreCase(keyword.getState())) {
                    vo.setNegaType(keyword.getMatchType());
                }
                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }

    @Override
    public Page pageManageList(Integer puid, CpcQueryWordDto dto, Page page) {
        if (CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(dto.getType()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN.equals(dto.getSearchValue())) {
                dto.setSearchValue(Constants.keywords_related_to_your_brand);
            } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN.equals(dto.getSearchValue())) {
                dto.setSearchValue(Constants.keywords_related_to_your_landing_pages);
            }
        }
        if (StringUtils.isNotBlank(dto.getWordRoot())) {
            //获取列表页所有queryId
            List<String> queryIdList = cpcSbQueryKeywordReportDao.listQueryIdByQueryWordDto(dto);
            if (CollectionUtils.isEmpty(queryIdList)) {
                return page;
            }
            List<String> queryIds = wordRootKeywordSbDao.listQueryIdByWordRootAndQueryIdList(puid, dto.getShopId(), dto.getWordRoot(), queryIdList);
            if (CollectionUtils.isEmpty(queryIds)) {
                return page;
            }
            dto.setQueryIds(queryIds);
        }

        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal sumRange;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {  //店铺销售额
            sumRange = BigDecimal.ZERO;
        } else {
            sumRange = shopSaleDto.getSumRange();
        }
        dto.setShopSales(sumRange);

        if (StringUtils.isNotBlank(dto.getPortfolioId()) || StringUtils.isNotBlank(dto.getStatus()) || StringUtils.isNotBlank(dto.getServingStatus())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(puid, dto.getShopId(), dto.getPortfolioId(), dto.getStatus(), dto.getServingStatus(), CampaignTypeEnum.sb.getCampaignType());
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                dto.setCampaignIdList(campaignIds);
            } else {
                return page;
            }
        }

        if (CollectionUtils.isNotEmpty(dto.getQueryWordTagTypeList())) {
            List<SearchQueryTagParam> queryTagParams = cpcSbQueryKeywordReportDao.listAdGroupIdByQueryWordDto(dto);
            List<String> matchTypeList = Lists.newArrayList();
            for (String matchType : dto.getQueryWordTagTypeList()) {

                if (dto.getQueryWordTagTypeList().contains(Constants.QUERY_NOT_TARGET)) {
                    matchTypeList.add(Constants.EXACT);
                    matchTypeList.add(Constants.BROAD);
                    matchTypeList.add(Constants.PHRASE);
                    matchTypeList.add(Constants.NEGATIVEEXACT);
                    matchTypeList.add(Constants.NEGATIVEPHRASE);

                } else {
                    if ("isExact".equalsIgnoreCase(matchType)) {
                        matchTypeList.add(Constants.EXACT);
                    }
                    if ("isBroad".equalsIgnoreCase(matchType)) {
                        matchTypeList.add(Constants.BROAD);
                    }
                    if ("isPhrase".equalsIgnoreCase(matchType)) {
                        matchTypeList.add(Constants.PHRASE);
                    }
                    if ("isNegativeExact".equalsIgnoreCase(matchType)) {
                        matchTypeList.add(Constants.NEGATIVEEXACT);
                    }
                    if ("isNegativePhrase".equalsIgnoreCase(matchType)) {
                        matchTypeList.add(Constants.NEGATIVEPHRASE);
                    }
                }


            }
            if (CollectionUtils.isNotEmpty(queryTagParams)) {
                if (CollectionUtils.isNotEmpty(matchTypeList)) {
                    List<SearchQueryTagParam> searchQueryTagParamArrayList = Lists.newArrayList();
                    List<SearchQueryTagParam> searchQueryTagParamList = amazonSbAdKeywordDao.getSearchQueryTag(puid, dto.getShopId(), matchTypeList, queryTagParams);
                    List<SearchQueryTagParam> searchQueryTagParams = amazonSbAdNeKeywordDao.getSearchQueryTag(puid, dto.getShopId(), matchTypeList, queryTagParams);
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamList)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParamList);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParams)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParams);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamArrayList)) {
                        dto.setSearchQueryTagParamList(searchQueryTagParamArrayList);
                    } else {
                        return page;
                    }
                } else {
                    return page;
                }
            } else {
                return page;
            }
        }

        AdMetricDto adMetricDto;

        page = cpcSbQueryKeywordReportDao.pageList(puid, dto, page);
        adMetricDto = cpcSbQueryKeywordReportDao.getSumAdMetricDto(puid, dto);

        List<CpcSbQueryKeywordReport> poList = page.getRows();
        if (poList != null && poList.size() > 0) {

            List<String> campaignIds = poList.stream().filter(Objects::nonNull).map(CpcSbQueryKeywordReport::getCampaignId).distinct().collect(Collectors.toList());
            List<String> groupIds = poList.stream().filter(Objects::nonNull).map(CpcSbQueryKeywordReport::getAdGroupId).distinct().collect(Collectors.toList());

            Map<String, AmazonAdPortfolio> portfolioMap = null;
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                List<String> portfolioIds = amazonAdSbCampaignDao.getPortfolioListByCampaignIds(puid, dto.getShopId(), campaignIds);
                if (CollectionUtils.isNotEmpty(portfolioIds)) {
                    portfolioMap = portfolioDao.getPortfolioList(puid, dto.getShopId(), portfolioIds).stream()
                            .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                }
            }

            //批量查询广告活动和广告组
            List<AmazonAdCampaignAll> byCampaignIds = null;
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                byCampaignIds = amazonAdSbCampaignDao.getByCampaignIds(puid, dto.getShopId(), campaignIds);
            }

            Map<String, AmazonAdCampaignAll> campaignMap = null;
            if (CollectionUtils.isNotEmpty(byCampaignIds)) {
                campaignMap = byCampaignIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item -> item, (a, b) -> a));
            }

            Map<String, AmazonSbAdGroup> groupMap = null;
            List<AmazonSbAdGroup> adGroupByIds = null;
            if (CollectionUtils.isNotEmpty(groupIds)) {
                adGroupByIds = amazonSbAdGroupDao.getAdGroupByIds(puid, dto.getShopId(), groupIds);
            }

            if (CollectionUtils.isNotEmpty(adGroupByIds)) {
                groupMap = adGroupByIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, item -> item, (a, b) -> a));
            }

            Map<String, AmazonAdCampaignAll> finalCampaignMap = campaignMap;
            Map<String, AmazonSbAdGroup> finalGroupMap = groupMap;

            Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;

            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            poList.forEach(e -> {
                ReportVo vo = new ReportVo();
                vo.setCost(Optional.ofNullable(e.getCost()).orElse(BigDecimal.ZERO));
                vo.setSales(Optional.ofNullable(e.getSales14d()).orElse(BigDecimal.ZERO));
                vo.setImpressions(Optional.ofNullable(e.getImpressions()).orElse(0));
                vo.setClicks(Optional.ofNullable(e.getClicks()).orElse(0));
                vo.setOrderNum(Optional.ofNullable(e.getConversions14d()).orElse(0));
                vo.setSaleNum(Optional.ofNullable(e.getConversions14d()).orElse(0));

                filterMetricData(e, vo, adMetricDto);

                Double clickRate = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) * 100, Double.valueOf(vo.getImpressions()), 2);
                vo.setClickRate(clickRate);
                Double salesConversionRate = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getSaleNum()) * 100, Double.valueOf(vo.getClicks()), 2);
                vo.setSalesConversionRate(salesConversionRate);
                BigDecimal rate = BigDecimal.valueOf(vo.getClicks()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(vo.getClicks()));
                BigDecimal cpc = rate.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setCpc(cpc);


                BigDecimal rate2 = e.getSales14d().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), e.getSales14d());
                rate2 = MathUtil.multiply(rate2, BigDecimal.valueOf(100));
                BigDecimal acos = rate2.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setAcos(acos);

                BigDecimal rate3 = BigDecimal.valueOf(e.getConversions14d()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(e.getConversions14d()));
                BigDecimal cpa = rate3.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setCpa(cpa);

                if (e.getSales14d() != null && e.getCost() != null) {
                    vo.setRoas(e.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getSales14d().divide(e.getCost(), 2, RoundingMode.HALF_UP));
                }
                //新加指标,需要获取广告
                BigDecimal shopTotalSales = Optional.ofNullable(sumRange).orElse(BigDecimal.ZERO);
                vo.setAcots(shopTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getCost().multiply(BigDecimal.valueOf(100)).divide(sumRange, 2, RoundingMode.HALF_UP));
                vo.setAsots(shopTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getSales14d().multiply(BigDecimal.valueOf(100)).divide(sumRange, 2, RoundingMode.HALF_UP));

                vo.setQuery(e.getQuery());
                vo.setQueryCn(e.getQueryCn());
                if (StringUtils.isNotBlank(e.getKeywordText())) {
                    if (SbMatchValueEnum.THEME.getMatchType().equalsIgnoreCase(e.getMatchType())) {
                        String keywordText = e.getKeywordText();
                        if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(keywordText) || Constants.keywords_related_to_your_brand.equalsIgnoreCase(keywordText)) {
                            vo.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
                        } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(keywordText) || Constants.keywords_related_to_your_landing_pages.equalsIgnoreCase(keywordText)) {
                            vo.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
                        } else {
                            vo.setKeywordText(keywordText);
                        }
                    } else {
                        vo.setKeywordText(e.getKeywordText());
                    }
                }
                vo.setMatchType(e.getMatchType());
                vo.setKeywordId(e.getKeywordId());
                vo.setTargetingType(Constants.MANUAL); //关键词应该只有手动
                vo.setIsTargetType(false);
                vo.setCampaignId(e.getCampaignId());
                vo.setCampaignName(e.getCampaignName());
                vo.setAdGroupId(e.getAdGroupId());
                vo.setAdGroupName(e.getAdGroupName());

                //广告活动名称使用最新的
                if (MapUtils.isNotEmpty(finalCampaignMap) && finalCampaignMap.containsKey(e.getCampaignId())) {
                    AmazonAdCampaignAll campaign = finalCampaignMap.get(vo.getCampaignId());
                    vo.setCampaignName(campaign.getName());
                    vo.setAdFormat(campaign.getAdFormat());
                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        vo.setPortfolioId(campaign.getPortfolioId());
                        if (finalPortfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = finalPortfolioMap.get(campaign.getPortfolioId());
                            vo.setPortfolioName(amazonAdPortfolio.getName());
                        } else {
                            vo.setPortfolioName("广告组合待同步");
                        }
                    } else {
                        vo.setPortfolioName("-");
                    }
                }

                //广告组名称使用最新的
                if (MapUtils.isNotEmpty(finalGroupMap) && finalGroupMap.containsKey(e.getAdGroupId())) {
                    vo.setAdGroupName(finalGroupMap.get(e.getAdGroupId()).getName());
                    //vo.setAdGroupType(finalGroupMap.get(e.getAdGroupId()).getAdGroupType());
                    vo.setDefaultBid(finalGroupMap.get(e.getAdGroupId()).getBid() == null ? 0D : finalGroupMap.get(e.getAdGroupId()).getBid().doubleValue());
                }
                //“品牌新买家”订单量
                vo.setOrdersNewToBrand14d(e.getOrdersNewToBrand14d());
                //“品牌新买家”销售额
                vo.setSalesNewToBrand14d(e.getSalesNewToBrand14d());
                //搜索词展示量排名
                vo.setImpressionRank(e.getSearchTermImpressionRank());
                //搜索词展示份额
                vo.setImpressionShare(e.getSearchTermImpressionShare());
                //“品牌新买家”订单百分比
                BigDecimal orderRateNewToBrand14d = e.getConversions14d() == 0 ? BigDecimal.ZERO : new BigDecimal(Optional.ofNullable(e.getOrdersNewToBrand14d()).orElse(0)).multiply(new BigDecimal("100")).divide(new BigDecimal(e.getConversions14d()), 2, BigDecimal.ROUND_HALF_UP);
                vo.setOrderRateNewToBrand14d(orderRateNewToBrand14d.doubleValue());
                //“品牌新买家”销售额百分比
                BigDecimal salesRateNewToBrand14d = e.getSales14d().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : Optional.ofNullable(e.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO).multiply(new BigDecimal("100")).divide(e.getSales14d(), 2, BigDecimal.ROUND_HALF_UP);
                vo.setSalesRateNewToBrand14d(salesRateNewToBrand14d.doubleValue());
                //“品牌新买家”订单转化率
                BigDecimal ordersNewToBrandPercentage14d = e.getClicks() == 0 ? BigDecimal.ZERO : new BigDecimal(Optional.ofNullable(e.getOrdersNewToBrand14d()).orElse(0)).multiply(new BigDecimal("100")).divide(new BigDecimal(e.getClicks()), 2, BigDecimal.ROUND_HALF_UP);
                vo.setOrdersNewToBrandPercentage14d(ordersNewToBrandPercentage14d.doubleValue());

                vo.setVideo5SecondViews(Optional.ofNullable(e.getVideo5SecondViews()).orElse(0));
                vo.setVideo5SecondViewRate(vo.getImpressions() == 0 ? 0.00 :
                        MathUtil.divideOfObject(MathUtil.multiplyOfObject(vo.getVideo5SecondViews(), 100), vo.getImpressions(), 2).doubleValue());
                vo.setVideoFirstQuartileViews(Optional.ofNullable(e.getVideoFirstQuartileViews()).orElse(0));
                vo.setVideoMidpointViews(Optional.ofNullable(e.getVideoMidpointViews()).orElse(0));
                vo.setVideoThirdQuartileViews(Optional.ofNullable(e.getVideoThirdQuartileViews()).orElse(0));
                vo.setVideoCompleteViews(Optional.ofNullable(e.getVideoCompleteViews()).orElse(0));
                vo.setVideoUnmutes(Optional.ofNullable(e.getVideoUnmutes()).orElse(0));
                vo.setViewImpressions(Optional.ofNullable(e.getViewableImpressions()).orElse(0));
                vo.setViewabilityRate(vo.getImpressions() == 0 ? BigDecimal.ZERO :
                        MathUtil.divideOfObject(MathUtil.multiplyOfObject(vo.getViewImpressions(), 100), vo.getImpressions(), 2));
                vo.setViewClickThroughRate(vo.getViewImpressions() == 0 ? BigDecimal.ZERO :
                        MathUtil.divideOfObject(MathUtil.multiplyOfObject(vo.getClicks(), 100), vo.getViewImpressions(), 2));
                vo.setAdvertisingUnitPrice(vo.getOrderNum() == 0 ? BigDecimal.ZERO :
                        MathUtil.divideOfObject(vo.getSales(), vo.getOrderNum(), 2));
                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }

    @Override
    public Page dorisPageExportList(Integer puid, CpcQueryWordDto dto, Page page) {
        if (CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(dto.getType()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN.equals(dto.getSearchValue())) {
                dto.setSearchValue(Constants.keywords_related_to_your_brand);
            } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN.equals(dto.getSearchValue())) {
                dto.setSearchValue(Constants.keywords_related_to_your_landing_pages);
            }
        }
        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal sumRange;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {  //店铺销售额
            sumRange = BigDecimal.ZERO;
        } else {
            sumRange = shopSaleDto.getSumRange();
        }
        dto.setShopSales(sumRange);

        // 参数赋值
        if (setParam(puid, dto)) return page;
        if (dto.isQueryJoinSearchTermsRank()) {
            String date = weekSearchTermsAnalysisService.getLatestDate(dto.getMarketplaceId());
            dto.setLastWeekSearchTermsRankDate(date);
        }
        AdMetricDto adMetricDto = odsCpcSbQueryKeywordReportDao.getSumAdMetricDto(puid, dto);
        page = odsCpcSbQueryKeywordReportDao.pageList(puid, dto, page);

        List<CpcSbQueryKeywordReport> poList = page.getRows();
        if (poList != null && poList.size() > 0) {
            //获取翻译词
            List<WordTranslateQo> wordTranslateQos = poList.stream().map(e -> new WordTranslateQo(e.getMarketplaceId(), e.getQuery())).collect(Collectors.toList());
            Map<String, String> wordTranslateMap = wordTranslateService.getWordTranslateMap(puid, wordTranslateQos, true);

            List<String> campaignIds = poList.stream().filter(Objects::nonNull).map(CpcSbQueryKeywordReport::getCampaignId).distinct().collect(Collectors.toList());
            List<String> groupIds = poList.stream().filter(Objects::nonNull).map(CpcSbQueryKeywordReport::getAdGroupId).distinct().collect(Collectors.toList());

            Map<String, AmazonAdPortfolio> portfolioMap = null;
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                List<String> portfolioIds = amazonAdSbCampaignDao.getPortfolioListByCampaignIds(puid, dto.getShopId(), campaignIds);
                if (CollectionUtils.isNotEmpty(portfolioIds)) {
                    portfolioMap = portfolioDao.getPortfolioList(puid, dto.getShopId(), portfolioIds).stream()
                            .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                }
            }

            //批量查询广告活动和广告组
            List<AmazonAdCampaignAll> byCampaignIds = null;
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                byCampaignIds = amazonAdSbCampaignDao.getByCampaignIds(puid, dto.getShopId(), campaignIds);
            }

            Map<String, AmazonAdCampaignAll> campaignMap = null;
            if (CollectionUtils.isNotEmpty(byCampaignIds)) {
                campaignMap = byCampaignIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item -> item, (a, b) -> a));
            }

            Map<String, AmazonSbAdGroup> groupMap = null;
            List<AmazonSbAdGroup> adGroupByIds = null;
            List<OdsAmazonAdKeywordSb> keywordList = null;
            List<OdsAmazonAdNeKeywordSb> neKeywordList = null;
            if (CollectionUtils.isNotEmpty(groupIds)) {
                adGroupByIds = amazonSbAdGroupDao.getAdGroupByIds(puid, dto.getShopId(), groupIds);
                keywordList = odsAmazonAdKeywordSbDao.listByGroupIdList(puid, dto.getShopId(), groupIds);
                neKeywordList = odsAmazonAdNeKeywordSbDao.listByGroupIdList(puid, dto.getShopId(), groupIds);
            }

            Map<String, List<OdsAmazonAdKeywordSb>> groupKeywordMapList = new HashMap<>();
            if (CollectionUtils.isNotEmpty(keywordList)) {
                for (OdsAmazonAdKeywordSb keyword : keywordList) {
                    if (StringUtils.isBlank(keyword.getAdGroupId())) {
                        continue;
                    }
                    if (groupKeywordMapList.containsKey(keyword.getAdGroupId())) {
                        groupKeywordMapList.get(keyword.getAdGroupId()).add(keyword);
                    } else {
                        groupKeywordMapList.put(keyword.getAdGroupId(), Lists.newArrayList(keyword));
                    }
                }
            }

            Map<String, List<OdsAmazonAdNeKeywordSb>> groupNeKeywordMapList = new HashMap<>();
            if (CollectionUtils.isNotEmpty(neKeywordList)) {
                for (OdsAmazonAdNeKeywordSb keyword : neKeywordList) {
                    if (StringUtils.isBlank(keyword.getAdGroupId())) {
                        continue;
                    }
                    if (groupNeKeywordMapList.containsKey(keyword.getAdGroupId())) {
                        groupNeKeywordMapList.get(keyword.getAdGroupId()).add(keyword);
                    } else {
                        groupNeKeywordMapList.put(keyword.getAdGroupId(), Lists.newArrayList(keyword));
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(adGroupByIds)) {
                groupMap = adGroupByIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, item -> item, (a, b) -> a));
            }

            Map<String, AmazonAdCampaignAll> finalCampaignMap = campaignMap;
            Map<String, AmazonSbAdGroup> finalGroupMap = groupMap;

            Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;

            Map<String, OdsWeekSearchTermsAnalysis> searchTermsAnalysisMap = new HashMap<>();
            boolean supportAbaRankExport = GrayUtil.isHit(dto.getPuid(), dynamicRefreshConfiguration.getSupportAbaRankExportWhiteList(), dynamicRefreshConfiguration.getSupportAbaRankPercentage());
            if (!dto.isQueryJoinSearchTermsRank() && supportAbaRankExport) {
                List<String> searchTerms = poList.stream().map(CpcSbQueryKeywordReport::getQuery).distinct().collect(Collectors.toList());
                List<OdsWeekSearchTermsAnalysis> searchTermsAnalyses = weekSearchTermsAnalysisService.queryRanks(searchTerms, dto.getMarketplaceId());
                searchTermsAnalysisMap.putAll(searchTermsAnalyses.stream().collect(Collectors.toMap(OdsWeekSearchTermsAnalysis::getSearchTerm, Function.identity(), (o,n)->n)));
            }

            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            poList.forEach(e -> {
                ReportVo vo = new ReportVo();
                vo.setQueryId(e.getQueryId());
                vo.setCost(Optional.ofNullable(e.getCost()).orElse(BigDecimal.ZERO));
                vo.setSales(Optional.ofNullable(e.getSales14d()).orElse(BigDecimal.ZERO));
                vo.setImpressions(Optional.ofNullable(e.getImpressions()).orElse(0));
                vo.setClicks(Optional.ofNullable(e.getClicks()).orElse(0));
                vo.setOrderNum(Optional.ofNullable(e.getConversions14d()).orElse(0));
                vo.setSaleNum(Optional.ofNullable(e.getConversions14d()).orElse(0));

                filterMetricData(e, vo, adMetricDto);

                //检查该搜索词是否添加过
                if (groupKeywordMapList.size() > 0 && groupKeywordMapList.containsKey(e.getAdGroupId()) && StringUtils.isNotBlank(e.getQuery())) {
                    List<OdsAmazonAdKeywordSb> amazonAdKeywords = groupKeywordMapList.get(e.getAdGroupId());
                    if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                        for (OdsAmazonAdKeywordSb keyword : amazonAdKeywords) {
                            if (StringUtils.isNotBlank(e.getQuery()) && StringUtils.isNotBlank(keyword.getKeywordText())) {
                                if (e.getQuery().trim().equalsIgnoreCase(keyword.getKeywordText().trim())) {
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.EXACT)) {
                                        vo.setIsExact(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.BROAD)) {
                                        vo.setIsBroad(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.PHRASE)) {
                                        vo.setIsPhrase(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.NEGATIVEEXACT)) {
                                        vo.setIsNegativeExact(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.NEGATIVEPHRASE)) {
                                        vo.setIsNegativePhrase(true);
                                    }
                                }
                            }
                        }
                    }
                }

                //检查该搜索词是否添加过否定词
                if (groupNeKeywordMapList.size() > 0 && groupNeKeywordMapList.containsKey(e.getAdGroupId()) && StringUtils.isNotBlank(e.getQuery())) {
                    List<OdsAmazonAdNeKeywordSb> amazonAdKeywords = groupNeKeywordMapList.get(e.getAdGroupId());
                    if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                        for (OdsAmazonAdNeKeywordSb keyword : amazonAdKeywords) {
                            if (StringUtils.isNotBlank(e.getQuery()) && StringUtils.isNotBlank(keyword.getKeywordText())) {
                                if (e.getQuery().trim().equalsIgnoreCase(keyword.getKeywordText().trim())) {
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.EXACT)) {
                                        vo.setIsExact(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.BROAD)) {
                                        vo.setIsBroad(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.PHRASE)) {
                                        vo.setIsPhrase(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.NEGATIVEEXACT)) {
                                        vo.setIsNegativeExact(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.NEGATIVEPHRASE)) {
                                        vo.setIsNegativePhrase(true);
                                    }
                                }
                            }
                        }
                    }
                }

                Double clickRate = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) * 100, Double.valueOf(vo.getImpressions()), 2);
                vo.setClickRate(clickRate);
                Double salesConversionRate = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getSaleNum()) * 100, Double.valueOf(vo.getClicks()), 2);
                vo.setSalesConversionRate(salesConversionRate);
                BigDecimal rate = BigDecimal.valueOf(vo.getClicks()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(vo.getClicks()));
                BigDecimal cpc = rate.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setCpc(cpc);


                BigDecimal rate2 = e.getSales14d().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), e.getSales14d());
                rate2 = MathUtil.multiply(rate2, BigDecimal.valueOf(100));
                BigDecimal acos = rate2.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setAcos(acos);

                BigDecimal rate3 = BigDecimal.valueOf(e.getConversions14d()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(e.getConversions14d()));
                BigDecimal cpa = rate3.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setCpa(cpa);

                if (e.getSales14d() != null && e.getCost() != null) {
                    vo.setRoas(e.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getSales14d().divide(e.getCost(), 2, RoundingMode.HALF_UP));
                }
                //新加指标,需要获取广告
                BigDecimal shopTotalSales = Optional.ofNullable(sumRange).orElse(BigDecimal.ZERO);
                vo.setAcots(shopTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getCost().multiply(BigDecimal.valueOf(100)).divide(sumRange, 2, RoundingMode.HALF_UP));
                vo.setAsots(shopTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getSales14d().multiply(BigDecimal.valueOf(100)).divide(sumRange, 2, RoundingMode.HALF_UP));

                vo.setQuery(e.getQuery());
                vo.setQueryCn(wordTranslateMap.get(wordTranslateService.getWordTranslateKey(dto.getMarketplaceId(), e.getQuery())));
                if (StringUtils.isNotBlank(e.getKeywordText())) {
                    if (SbMatchValueEnum.THEME.getMatchType().equalsIgnoreCase(e.getMatchType())) {
                        String keywordText = e.getKeywordText();
                        if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(keywordText) || Constants.keywords_related_to_your_brand.equalsIgnoreCase(keywordText)) {
                            vo.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
                        } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(keywordText) || Constants.keywords_related_to_your_landing_pages.equalsIgnoreCase(keywordText)) {
                            vo.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
                        } else {
                            vo.setKeywordText(keywordText);
                        }
                    } else {
                        vo.setKeywordText(e.getKeywordText());
                    }
                }
                vo.setMatchType(e.getMatchType());
                vo.setKeywordId(e.getKeywordId());
                vo.setTargetingType(Constants.MANUAL); //关键词应该只有手动
                vo.setIsTargetType(false);
                vo.setCampaignId(e.getCampaignId());
                vo.setCampaignName(e.getCampaignName());
                vo.setAdGroupId(e.getAdGroupId());
                vo.setAdGroupName(e.getAdGroupName());

                //广告活动名称使用最新的
                if (MapUtils.isNotEmpty(finalCampaignMap) && finalCampaignMap.containsKey(e.getCampaignId())) {
                    AmazonAdCampaignAll campaign = finalCampaignMap.get(vo.getCampaignId());
                    vo.setCampaignName(campaign.getName());
                    vo.setAdFormat(campaign.getAdFormat());
                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        vo.setPortfolioId(campaign.getPortfolioId());
                        if (finalPortfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = finalPortfolioMap.get(campaign.getPortfolioId());
                            vo.setPortfolioName(amazonAdPortfolio.getName());
                        } else {
                            vo.setPortfolioName("广告组合待同步");
                        }
                    } else {
                        vo.setPortfolioName("-");
                    }
                }

                //广告组名称使用最新的
                if (MapUtils.isNotEmpty(finalGroupMap) && finalGroupMap.containsKey(e.getAdGroupId())) {
                    vo.setAdGroupName(finalGroupMap.get(e.getAdGroupId()).getName());
                    //vo.setAdGroupType(finalGroupMap.get(e.getAdGroupId()).getAdGroupType());
                    vo.setDefaultBid(finalGroupMap.get(e.getAdGroupId()).getBid() == null ? 0D : finalGroupMap.get(e.getAdGroupId()).getBid().doubleValue());
                }
                //“品牌新买家”订单量
                vo.setOrdersNewToBrand14d(e.getOrdersNewToBrand14d());
                //“品牌新买家”销售额
                vo.setSalesNewToBrand14d(e.getSalesNewToBrand14d());
                //搜索词展示量排名
                vo.setImpressionRank(e.getSearchTermImpressionRank());
                //搜索词展示份额
                vo.setImpressionShare(e.getSearchTermImpressionShare());
                //“品牌新买家”订单百分比
                BigDecimal orderRateNewToBrand14d = e.getConversions14d() == 0 ? BigDecimal.ZERO : new BigDecimal(Optional.ofNullable(e.getOrdersNewToBrand14d()).orElse(0)).multiply(new BigDecimal("100")).divide(new BigDecimal(e.getConversions14d()), 2, BigDecimal.ROUND_HALF_UP);
                vo.setOrderRateNewToBrand14d(orderRateNewToBrand14d.doubleValue());
                //“品牌新买家”销售额百分比
                BigDecimal salesRateNewToBrand14d = e.getSales14d().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : Optional.ofNullable(e.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO).multiply(new BigDecimal("100")).divide(e.getSales14d(), 2, BigDecimal.ROUND_HALF_UP);
                vo.setSalesRateNewToBrand14d(salesRateNewToBrand14d.doubleValue());
                //“品牌新买家”订单转化率
                BigDecimal ordersNewToBrandPercentage14d = e.getClicks() == 0 ? BigDecimal.ZERO : new BigDecimal(Optional.ofNullable(e.getOrdersNewToBrand14d()).orElse(0)).multiply(new BigDecimal("100")).divide(new BigDecimal(e.getClicks()), 2, BigDecimal.ROUND_HALF_UP);
                vo.setOrdersNewToBrandPercentage14d(ordersNewToBrandPercentage14d.doubleValue());

                vo.setVideo5SecondViews(Optional.ofNullable(e.getVideo5SecondViews()).orElse(0));
                vo.setVideo5SecondViewRate(vo.getImpressions() == 0 ? 0.00 :
                        MathUtil.divideOfObject(MathUtil.multiplyOfObject(vo.getVideo5SecondViews(), 100), vo.getImpressions(), 2).doubleValue());
                vo.setVideoFirstQuartileViews(Optional.ofNullable(e.getVideoFirstQuartileViews()).orElse(0));
                vo.setVideoMidpointViews(Optional.ofNullable(e.getVideoMidpointViews()).orElse(0));
                vo.setVideoThirdQuartileViews(Optional.ofNullable(e.getVideoThirdQuartileViews()).orElse(0));
                vo.setVideoCompleteViews(Optional.ofNullable(e.getVideoCompleteViews()).orElse(0));
                vo.setVideoUnmutes(Optional.ofNullable(e.getVideoUnmutes()).orElse(0));
                vo.setViewImpressions(Optional.ofNullable(e.getViewableImpressions()).orElse(0));
                vo.setViewabilityRate(vo.getImpressions() == 0 ? BigDecimal.ZERO :
                        MathUtil.divideOfObject(MathUtil.multiplyOfObject(vo.getViewImpressions(), 100), vo.getImpressions(), 2));
                vo.setViewClickThroughRate(vo.getViewImpressions() == 0 ? BigDecimal.ZERO :
                        MathUtil.divideOfObject(MathUtil.multiplyOfObject(vo.getClicks(), 100), vo.getViewImpressions(), 2));
                vo.setAdvertisingUnitPrice(vo.getOrderNum() == 0 ? BigDecimal.ZERO :
                        MathUtil.divideOfObject(vo.getSales(), vo.getOrderNum(), 2));
                // 兼容特殊排序逻辑
                // 联表时字段aba排名与周变化率肯定不为空
                if (dto.isQueryJoinSearchTermsRank()) {
                    vo.setSearchFrequencyRank(e.getSearchFrequencyRank() == Integer.MAX_VALUE ? 0 : e.getSearchFrequencyRank());
                    if (vo.getSearchFrequencyRank() > 0) {
                        BigDecimal weekRatio = e.getWeekRatio().intValue() == Integer.MIN_VALUE ? BigDecimal.ZERO : e.getWeekRatio();
                        vo.setWeekRatio(weekRatio.setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                } else {
                    OdsWeekSearchTermsAnalysis searchTermsAnalysis = searchTermsAnalysisMap.get(vo.getQuery().toLowerCase());
                    if (searchTermsAnalysis != null) {
                        vo.setSearchFrequencyRank(searchTermsAnalysis.getSearchFrequencyRank());
                        vo.setWeekRatio(Optional.ofNullable(searchTermsAnalysis.getWeekRatio()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                }
                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }

    /**
     * 构建关键词id和广告组合名字的map：campaignIds -> campaign entities -> portfolioIds -> portfolio entities -> portfolio name
     * 关键字最多会有100000个
     *
     * @param puid                 puid
     * @param poList               poList
     * @param shopIdCampaignIdsMap 店铺id -> campaignId列表
     * @return 关键词id和广告组合名字的map
     */
    private Map<String, String> buildKeywordIdPortfolioNameMap(int puid, List<CpcSbQueryKeywordReport> poList, Map<Integer, Set<String>> shopIdCampaignIdsMap) {
        //根据campaignIds查询一个任务中所有的广告活动列表
        List<AmazonAdCampaignAll> campaignList = new ArrayList<>();
        for (int shopId : shopIdCampaignIdsMap.keySet()) {
            //分区查
            List<List<String>> lists = Lists.partition(Lists.newArrayList(shopIdCampaignIdsMap.get(shopId)), 1000);
            for (List<String> campaignIdList : lists) {
                campaignList.addAll(amazonAdCampaignDao.getByCampaignIds(puid, shopId, campaignIdList));
            }
        }
        //根据portfolioIds查询一个任务中所有的广告组合列表
        Map<Integer, Set<String>> shopIdPortfolioIdsMap = campaignList.parallelStream().collect(Collectors.groupingBy(AmazonAdCampaignAll::getShopId, Collectors.mapping(AmazonAdCampaignAll::getPortfolioId, Collectors.toSet())));
        List<AmazonAdPortfolio> portfolioList = new ArrayList<>();
        for (int shopId : shopIdPortfolioIdsMap.keySet()) {
            List<List<String>> lists = Lists.partition(Lists.newArrayList(shopIdPortfolioIdsMap.get(shopId)), 1000);
            for (List<String> subList : lists) {
                portfolioList.addAll(portfolioDao.getByPortfolioIds(puid, shopId, subList));
            }
        }

        //通过得到的三个map，构建关键词id和广告组合名字的map
        Map<String, String> keywordIdCampaignIdMap = poList.stream().collect(Collectors.toMap(vo -> vo.getKeywordId() + vo.getCountDate(), CpcSbQueryKeywordReport::getCampaignId, (x, y) -> x));
        Map<String, String> campaignIdportfolioIdMap = campaignList.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, campaign -> StringUtils.isNotBlank(campaign.getPortfolioId()) ? campaign.getPortfolioId() : "", (x, y) -> x));
        Map<String, String> portfolioIdNameMap = portfolioList.stream().collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, AmazonAdPortfolio::getName, (x, y) -> x));
        Map<String, String> keywordIdPortfolioNameMap = new HashMap<>(poList.size());
        keywordIdCampaignIdMap.forEach((k, v) -> {
            keywordIdPortfolioNameMap.put(k, portfolioIdNameMap.get(campaignIdportfolioIdMap.get(v)));
        });
        return keywordIdPortfolioNameMap;
    }

    @Override
    public Page pageManageExportList(Integer puid, SearchVo searchVo, Page page) {

        page = cpcSbQueryKeywordReportDao.pageManageExportList(puid, searchVo, page);

        List<CpcSbQueryKeywordReport> poList = page.getRows();

        if (CollectionUtils.isNotEmpty(poList)) {
            List<ReportDataVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            //查询基础数据
            Map<Integer, Set<String>> shopIdCampaignIdsMap = poList.parallelStream().collect(Collectors.groupingBy(CpcSbQueryKeywordReport::getShopId, Collectors.mapping(CpcSbQueryKeywordReport::getCampaignId, Collectors.toSet())));
            //查询广告活动基础数据
            Map<Integer, Map<String, DownloadCenterCampaignBaseDataBO>> campaignBaseDataMap =  reportFillBaseDataHelper.queryBaseData4DownloadByShopGroupIdList(puid, shopIdCampaignIdsMap, (a, b, c) -> amazonAdCampaignAllDao.queryBaseData4DownloadByCampaignIdList(a, b, c));
            //分组查询portfolioName
            Map<String, String> keywordIdPortfolioNameMap;
            try {
                keywordIdPortfolioNameMap = buildKeywordIdPortfolioNameMap(puid, poList, shopIdCampaignIdsMap);
            } catch (Exception e) {
                log.error("sp query portfolioName fail: ", e);
                keywordIdPortfolioNameMap = new HashMap<>();
            }
            Map<String, String> finalKeywordIdPortfolioNameMap = keywordIdPortfolioNameMap;

            poList.forEach(e -> {
                ReportDataVo vo = new ReportDataVo();
                vo.setAdFormat(e.getAdFormat());
                vo.setShopId(e.getShopId());
                vo.setQuery(e.getQuery());
                vo.setKeywordId(e.getKeywordId());
                if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(e.getKeywordText()) || Constants.keywords_related_to_your_brand.equalsIgnoreCase(e.getKeywordText())) {
                    vo.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
                } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(e.getKeywordText()) || Constants.keywords_related_to_your_landing_pages.equalsIgnoreCase(e.getKeywordText())) {
                    vo.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
                } else {
                    vo.setKeywordText(e.getKeywordText());
                }

                vo.setAdGroupId(e.getAdGroupId());
                vo.setCampaignId(e.getCampaignId());
                vo.setCampaignName(e.getCampaignName());
                vo.setAdGroupName(e.getAdGroupName());
                vo.setPortfolioName(finalKeywordIdPortfolioNameMap.get(e.getKeywordId() + e.getCountDate()));
                vo.setCampaignBudgetCurrencyCode(AmznEndpoint.getByMarketplaceId(e.getMarketplaceId()).getCurrencyCode().value());

                reportFillBaseDataHelper.fillCampaignBaseData4ReportDataVo(vo, e.getShopId(), e.getCampaignId(), campaignBaseDataMap);

                vo.setSpCampaignType("手动");
                if (StringUtils.isNotBlank(e.getMatchType())) {
                    if (Constants.PHRASE.equalsIgnoreCase(e.getMatchType())) {
                        vo.setMatchType("词组匹配");
                    } else if (Constants.EXACT.equalsIgnoreCase(e.getMatchType())) {
                        vo.setMatchType("精确匹配");
                    } else if (Constants.BROAD.equalsIgnoreCase(e.getMatchType())) {
                        vo.setMatchType("广泛匹配");
                    } else if (Constants.TARGETING_EXPRESSION_CLOSE.equalsIgnoreCase(e.getMatchType())) {
                        vo.setMatchType("紧密匹配");
                    } else if (Constants.TARGETING_EXPRESSION_LOOSE.equalsIgnoreCase(e.getMatchType())) {
                        vo.setMatchType("宽泛匹配");
                    } else if (SbMatchValueEnum.THEME.getMatchType().equalsIgnoreCase(e.getMatchType())) {
                        vo.setMatchType("主题");
                    }
                }
                vo.setCost(Optional.ofNullable(e.getCost()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                vo.setImpressions(Optional.ofNullable(e.getImpressions()).orElse(0));
                vo.setClicks(Optional.ofNullable(e.getClicks()).orElse(0));
                vo.setCpc(e.getCost() != null && e.getClicks() != null ? calculationRateBigDecimal(e.getCost(), BigDecimal.valueOf(e.getClicks()), false) : BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP));
                vo.setClickRate(e.getClicks() != null && e.getImpressions() != null ? calculationRateDouble(Double.valueOf(e.getClicks()), Double.valueOf(e.getImpressions())) : 0.00);
                vo.setAcos(getAcos(e.getCost(), e.getSales14d()));
                vo.setRoas(getRoas(e.getSales14d(), e.getCost()));
                vo.setSalesConversionRate(getSalesConversionRate(e.getClicks(), e.getConversions14d()));
                vo.setAttributedConversions14d(Optional.ofNullable(e.getConversions14d()).orElse(0));
                vo.setAttributedSales14d(Optional.ofNullable(e.getSales14d()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                vo.setSearchTermImpressionRank(Optional.ofNullable(e.getSearchTermImpressionRank()).orElse(0));
                vo.setSearchTermImpressionShare(Optional.ofNullable(e.getSearchTermImpressionShare()).orElse(0.00));
                if (AdReportExportAdFormatEnum.video.getAdFormat().equals(e.getAdFormat())) {
                    vo.setAttributedOrderRateNewToBrand14d(Optional.ofNullable(e.getOrderRateNewToBrand14d()).orElse(0.00));
                    vo.setAttributedOrdersNewToBrand14d(Optional.ofNullable(e.getOrdersNewToBrand14d()).orElse(0));
                    vo.setAttributedOrdersNewToBrandPercentage14d(Optional.ofNullable(e.getOrdersNewToBrandPercentage14d()).orElse(0.00));
                    vo.setAttributedSalesNewToBrand14d(Optional.ofNullable(e.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP)));
                    vo.setAttributedSalesNewToBrandPercentage14d(Optional.ofNullable(e.getSalesNewToBrandPercentage14d()).orElse(0.00));
                    vo.setAttributedUnitsOrderedNewToBrand14d(Optional.ofNullable(e.getUnitsOrderedNewToBrand14d()).orElse(0));
                    vo.setAttributedUnitsOrderedNewToBrandPercentage14d(Optional.ofNullable(e.getUnitsOrderedNewToBrandPercentage14d()).orElse(0.00));
                    vo.setVctr(Optional.ofNullable(e.getVctr()).orElse(0.00));
                    vo.setVideo5SecondViewRate(Optional.ofNullable(e.getVideo5SecondViewRate()).orElse(0.00));
                    vo.setVideo5SecondViews(Optional.ofNullable(e.getVideo5SecondViews()).orElse(0));
                    vo.setVideoFirstQuartileViews(Optional.ofNullable(e.getVideoFirstQuartileViews()).orElse(0));
                    vo.setVideoMidpointViews(Optional.ofNullable(e.getVideoMidpointViews()).orElse(0));
                    vo.setVideoThirdQuartileViews(Optional.ofNullable(e.getVideoThirdQuartileViews()).orElse(0));
                    vo.setVideoUnmutes(Optional.ofNullable(e.getVideoUnmutes()).orElse(0));
                    vo.setViewImpressions(Optional.ofNullable(e.getViewableImpressions()).orElse(0));
                    vo.setVideoCompleteViews(Optional.ofNullable(e.getVideoCompleteViews()).orElse(0));
                    vo.setVtr(Optional.ofNullable(e.getVtr()).orElse(0.00));
                }
                if (StringUtils.isNotBlank(e.getCountDate())) {
                    vo.setCountDate(e.getCountDate());
                }
                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }

    public BigDecimal getAcos(BigDecimal cost, BigDecimal totalSales) {
        if (cost == null || totalSales == null) {
            return null;
        }
        return calculationRateBigDecimal(cost, totalSales, true);
    }

    public Double getSalesConversionRate(Integer clicks, Integer orderNum) {
        if (clicks == null || orderNum == null) {
            return null;
        }
        return calculationRateDouble(Double.valueOf(orderNum), Double.valueOf(clicks));
    }

    public BigDecimal getRoas(BigDecimal totalSales, BigDecimal cost) {
        if (totalSales == null || cost == null) {
            return null;
        }
        return cost.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : totalSales.divide(cost, 2, RoundingMode.HALF_UP);
    }

    private BigDecimal calculationRateBigDecimal(BigDecimal value1, BigDecimal value2, Boolean per) {
        BigDecimal rate = value2.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(value1, value2);
        if (per) {
            rate = MathUtil.multiply(rate, BigDecimal.valueOf(100));
        }
        return rate.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    private Double calculationRateDouble(Double value1, Double value2) {
        return value2 == 0 ? 0 : DoubleUtil.divide(value1 * 100, value2, 2);
    }

    @Override
    public AllQueryWordDataResponse.AdQueryWordsHomeVo getAllQueryWordData(Integer puid, CpcQueryWordDto dto, Page page) {

        if (CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(dto.getType()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN.equals(dto.getSearchValue())) {
                dto.setSearchValue(Constants.keywords_related_to_your_brand);
            } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN.equals(dto.getSearchValue())) {
                dto.setSearchValue(Constants.keywords_related_to_your_landing_pages);
            }
        }

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(dto.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal shopSaleData;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {  //店铺销售额
            shopSaleData = BigDecimal.ZERO;
        } else {
            shopSaleData = shopSaleDto.getSumRange();
        }
        dto.setShopSales(shopSaleData);

        //组装vo
        long allKeywordTime = System.currentTimeMillis();
        Page pageVo = getAdQueryWordsPageVo(puid, dto, page);
        log.info("==============================查询所有搜索词关键词花费时间 {} ==============================", System.currentTimeMillis() - allKeywordTime);
        if (pageVo.getTotalSize() > Constants.TOTALSIZELIMIT) {  //总数大于十万
            int totalPage = Constants.TOTALSIZELIMIT / pageVo.getPageSize();
            pageVo.setTotalPage(totalPage);
            pageVo.setTotalSize(Constants.TOTALSIZELIMIT);
        }

        //处理分页
        AllQueryWordDataResponse.AdQueryWordsHomeVo.Page.Builder pageBuilder = AllQueryWordDataResponse.AdQueryWordsHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(pageVo.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(pageVo.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(pageVo.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(pageVo.getTotalSize()));
        List<ReportVo> rows = pageVo.getRows();

        //填充ABA展示信息（仅支持美国站）
        long t1 = Instant.now().toEpochMilli();
        fillABARankField(rows);
        log.info("填充ABA展示信息花费时间：{}", Instant.now().toEpochMilli() - t1);

        if (CollectionUtils.isNotEmpty(rows)) {
            //环比数据
            Map<String, ReportVo> compareQueryWordMap = null;
            if (dto.getIsCompare()) {
                //对比时无须高级搜索条件
                dto.setUseAdvanced(false);
                ShopSaleDto shopSaleDtoCompare = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getCompareStartDate(), dto.getCompareEndDate());
                BigDecimal shopSaleDataCompare;
                if (shopSaleDtoCompare == null || shopSaleDtoCompare.getSumRange() == null) {  //店铺销售额
                    shopSaleDataCompare = BigDecimal.ZERO;
                } else {
                    shopSaleDataCompare = shopSaleDtoCompare.getSumRange();
                }
                dto.setShopSales(shopSaleDataCompare);
                dto.setStart(dto.getCompareStartDate());
                dto.setEnd(dto.getCompareEndDate());

                //通过asin精确查询本来
                dto.setSearchType("exact");
                dto.setSearchField("query");
                dto.setSearchValue(rows.stream().map(ReportVo::getQuery).collect(Collectors.joining(StringUtil.SPECIAL_COMMA)));

                Page<ReportVo> pageVoCompare = getAdQueryWordsPageVo(puid, dto, page);
                compareQueryWordMap = pageVoCompare.getRows().stream().collect(Collectors.
                        toMap(k -> Optional.ofNullable(k.getKeywordId()).orElse("").concat("#")
                                .concat(Optional.ofNullable(k.getQuery()).orElse("")), Function.identity(), (a, b) -> a));
            }

            Map<String, ReportVo> finalCompareQueryWordMap = compareQueryWordMap;
            List<ReportRpcVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                        ReportRpcVo.Builder voBuilder = ReportRpcVo.newBuilder();

                        if (org.apache.commons.lang.StringUtils.isNotBlank(item.getCountDate())) {
                            voBuilder.setCountDate(item.getCountDate());
                        }
                        if (item.getShopId() != null) {
                            voBuilder.setShopId(Int32Value.of(item.getShopId()));
                        }
                        if (item.getMarketplaceId() != null) {
                            voBuilder.setMarketplaceId(item.getMarketplaceId());
                        }

                        voBuilder.setCpa(DoubleValue.of(Optional.ofNullable(item.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCpc(DoubleValue.of(Optional.ofNullable(item.getCpc()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setRoas(DoubleValue.of(Optional.ofNullable(item.getRoas()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setAsots(DoubleValue.of(Optional.ofNullable(item.getAsots()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setAcots(DoubleValue.of(Optional.ofNullable(item.getAcots()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                        voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                        voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
                        voBuilder.setSaleNum(Int32Value.of(Optional.ofNullable(item.getSaleNum()).orElse(0)));
                        voBuilder.setClickRate(DoubleValue.of(Optional.ofNullable(item.getClickRate()).orElse(0.0)));
                        voBuilder.setCpa(DoubleValue.of(Optional.ofNullable(item.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setNaturalSales(DoubleValue.of(Optional.ofNullable(item.getNaturalSales()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setAdClickRatio(DoubleValue.of(Optional.ofNullable(item.getAdClickRatio()).orElse(0.0)));
                        voBuilder.setAdConversionRate(DoubleValue.of(Optional.ofNullable(item.getAdConversionRate()).orElse(0.0)));
                        voBuilder.setSales(DoubleValue.of(Optional.ofNullable(item.getSales()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setSalesConversionRate(DoubleValue.of(Optional.ofNullable(item.getSalesConversionRate()).orElse(0.0)));
                        voBuilder.setCost(DoubleValue.of(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setAcos(DoubleValue.of(Optional.ofNullable(item.getAcos()).orElse(BigDecimal.ZERO).doubleValue()));
                        // 花费占比
                        voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                        // 销售额占比
                        voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                        // 订单量占比
                        voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                        // 销量占比
                        voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");

                        voBuilder.setNaturalClicks(org.apache.commons.lang.StringUtils.isNotBlank(item.getNaturalClicks()) ? item.getNaturalClicks() : "0");

                        if (StringUtils.isNotBlank(item.getCampaignId())) {
                            voBuilder.setCampaignId(item.getCampaignId());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupId())) {
                            voBuilder.setAdGroupId(item.getAdGroupId());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupType())) {
                            voBuilder.setAdGroupType(item.getAdGroupType());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupName())) {
                            voBuilder.setAdGroupName(item.getAdGroupName());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupState())) {
                            voBuilder.setAdGroupState(item.getAdGroupState());
                        }

                        if (StringUtils.isNotBlank(item.getCampaignName())) {
                            voBuilder.setCampaignName(item.getCampaignName());
                        }
                        if (StringUtils.isNotBlank(item.getCampaignStatus())) {
                            voBuilder.setCampaignState(item.getCampaignStatus());
                        }

                        if (StringUtils.isNotBlank(item.getKeywordText())) {
                            if (SbMatchValueEnum.THEME.getMatchType().equalsIgnoreCase(item.getMatchType())) {
                                String keywordText = item.getKeywordText();
                                if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(keywordText) || Constants.keywords_related_to_your_brand.equalsIgnoreCase(keywordText)) {
                                    voBuilder.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
                                } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(keywordText) || Constants.keywords_related_to_your_landing_pages.equalsIgnoreCase(keywordText)) {
                                    voBuilder.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
                                } else {
                                    voBuilder.setKeywordText(keywordText);
                                }
                            } else {
                                voBuilder.setKeywordText(item.getKeywordText());
                            }
                        }
                        if (StringUtils.isNotBlank(item.getMatchType())) {
                            voBuilder.setMatchType(item.getMatchType());
                        }
                        if (StringUtils.isNotBlank(item.getSku())) {
                            voBuilder.setSku(item.getSku());
                        }
                        if (StringUtils.isNotBlank(item.getAsin())) {
                            voBuilder.setAsin(item.getAsin());
                        }

                        if (StringUtils.isNotBlank(item.getQuery())) {
                            voBuilder.setQuery(item.getQuery());
                        }

                        if (StringUtils.isNotBlank(item.getQueryCn())) {
                            voBuilder.setQueryCn(item.getQueryCn());
                        }

                        if (StringUtils.isNotBlank(item.getParentAsin())) {
                            voBuilder.setParentAsin(item.getParentAsin());
                        }
                        if (StringUtils.isNotBlank(item.getTitle())) {
                            voBuilder.setTitle(item.getTitle());
                        }
                        if (StringUtils.isNotBlank(item.getMainImage())) {
                            voBuilder.setMainImage(item.getMainImage());
                        }
                        if (StringUtils.isNotBlank(item.getNegaType())) {
                            voBuilder.setNegaType(item.getNegaType());
                        }
                        if (StringUtils.isNotBlank(item.getTargetingType())) {
                            voBuilder.setTargetingType(item.getTargetingType());
                        }
                        if (StringUtils.isNotBlank(item.getKeywordId())) {
                            voBuilder.setKeywordId(item.getKeywordId());
                        }
                        if (StringUtils.isNotBlank(item.getAdId())) {
                            voBuilder.setAdId(item.getAdId());
                        }
                        if (StringUtils.isNotBlank(item.getTargetingText())) {
                            voBuilder.setTargetingText(item.getTargetingText());
                        }
                        if (StringUtils.isNotBlank(item.getSpCampaignType())) {
                            voBuilder.setSpCampaignType(item.getSpCampaignType());
                        }
                        if (StringUtils.isNotBlank(item.getSpGroupType())) {
                            voBuilder.setSpGroupType(item.getSpGroupType());
                        }

                        if (StringUtils.isNotBlank(item.getSpTargetType())) {
                            voBuilder.setSpTargetType(item.getSpTargetType());
                        }

                        if (StringUtils.isNotBlank(item.getTargetId())) {
                            voBuilder.setTargetId(item.getTargetId());
                        }
                        if (item.getIsBroad() != null) {
                            voBuilder.setIsBroad(BoolValue.of(item.getIsBroad()));
                        }
                        if (item.getIsPhrase() != null) {
                            voBuilder.setIsPhrase(BoolValue.of(item.getIsPhrase()));
                        }
                        if (item.getIsExact() != null) {
                            voBuilder.setIsExact(BoolValue.of(item.getIsExact()));
                        }
                        if (item.getIsNegativeExact() != null) {
                            voBuilder.setIsNegativeExact(BoolValue.of(item.getIsNegativeExact()));
                        }
                        if (item.getIsNegativePhrase() != null) {
                            voBuilder.setIsNegativePhrase(BoolValue.of(item.getIsNegativePhrase()));
                        }
                        if (item.getIsTargetType() != null) {
                            voBuilder.setIsTargetType(BoolValue.of(item.getIsTargetType()));
                        }
                        if (item.getDefaultBid() != null) {
                            voBuilder.setDefaultBid(item.getDefaultBid());
                        }
                        if (item.getPortfolioId() != null) {
                            voBuilder.setPortfolioId(item.getPortfolioId());
                        }
                        if (item.getPortfolioName() != null) {
                            voBuilder.setPortfolioName(item.getPortfolioName());
                        }
                        if (item.getIsHidden() != null) {
                            voBuilder.setIsHidden(item.getIsHidden());
                        }
                        if (item.getAdFormat() != null) {
                            voBuilder.setAdFormat(item.getAdFormat());
                        }
                        if (item.getType() != null) {
                            voBuilder.setType(item.getType());
                        }
                        //“品牌新买家”订单量
                        if (item.getOrdersNewToBrand14d() != null) {
                            voBuilder.setOrdersNewToBrandFTD(Int32Value.of(item.getOrdersNewToBrand14d()));
                        }
                        //“品牌新买家”销售额
                        if (item.getSalesNewToBrand14d() != null) {
                            voBuilder.setSalesNewToBrandFTD(DoubleValue.of(item.getSalesNewToBrand14d().doubleValue()));
                        }
                        //搜索词展示量排名
                        if (item.getImpressionRank() != null) {
                            voBuilder.setImpressionRank(Int32Value.of(item.getImpressionRank()));
                        }
                        //搜索词展示份额
                        if (item.getImpressionShare() != null) {
                            voBuilder.setImpressionShare(DoubleValue.of(item.getImpressionShare()));
                        }
                        //“品牌新买家”订单百分比
                        if (item.getOrderRateNewToBrand14d() != null) {
                            voBuilder.setOrderRateNewToBrandFTD(DoubleValue.of(item.getOrderRateNewToBrand14d()));
                        }
                        //“品牌新买家”销售额百分比
                        if (item.getSalesRateNewToBrand14d() != null) {
                            voBuilder.setSalesRateNewToBrandFTD(DoubleValue.of(item.getSalesRateNewToBrand14d()));
                        }
                        //“品牌新买家”订单转化率
                        if (item.getOrdersNewToBrandPercentage14d() != null) {
                            voBuilder.setOrdersNewToBrandPercentageFTD(DoubleValue.of(item.getOrdersNewToBrandPercentage14d()));
                        }
                        // ABA搜索词排名
                        voBuilder.setSearchFrequencyRank(item.getSearchFrequencyRank());
                        // ABA排名周变化率
                        if (item.getWeekRatio() != null) {
                            voBuilder.setWeekRatio(String.valueOf(Optional.ofNullable(item.getWeekRatio()).orElse(BigDecimal.ZERO)));
                        }

                        voBuilder.setVideo5SecondViewRate(Optional.ofNullable(item.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideo5SecondViews(Optional.ofNullable(item.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoCompleteViews(Optional.ofNullable(item.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoFirstQuartileViews(Optional.ofNullable(item.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoMidpointViews(Optional.ofNullable(item.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoThirdQuartileViews(Optional.ofNullable(item.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoUnmutes(Optional.ofNullable(item.getVideoUnmutes()).map(String::valueOf).orElse("0"));
                        voBuilder.setViewImpressions(Optional.ofNullable(item.getViewImpressions()).map(String::valueOf).orElse("0"));
                        voBuilder.setViewabilityRate(Optional.ofNullable(item.getViewabilityRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setViewClickThroughRate(Optional.ofNullable(item.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                        // 填充环比数据
                        if (MapUtils.isNotEmpty(finalCompareQueryWordMap)) {
                            String mapKey = Optional.ofNullable(item.getKeywordId()).orElse("").concat("#").concat(Optional.ofNullable(item.getQuery()).orElse(""));
                            if (finalCompareQueryWordMap.containsKey(mapKey)) {
                                ReportVo compareItem = finalCompareQueryWordMap.get(mapKey);
                                voBuilder.setCompareCpc(DoubleValue.of(Optional.ofNullable(compareItem.getCpc()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareRoas(DoubleValue.of(Optional.ofNullable(compareItem.getRoas()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAsots(DoubleValue.of(Optional.ofNullable(compareItem.getAsots()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAcots(DoubleValue.of(Optional.ofNullable(compareItem.getAcots()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareImpressions(Int32Value.of(Optional.ofNullable(compareItem.getImpressions()).orElse(0)));
                                voBuilder.setCompareClicks(Int32Value.of(Optional.ofNullable(compareItem.getClicks()).orElse(0)));
                                voBuilder.setCompareOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getOrderNum()).orElse(0)));
                                voBuilder.setCompareSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getSaleNum()).orElse(0)));
                                voBuilder.setCompareClickRate(DoubleValue.of(Optional.ofNullable(compareItem.getClickRate()).orElse(0.0)));
                                voBuilder.setCompareCpa(DoubleValue.of(Optional.ofNullable(compareItem.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAdClickRatio(DoubleValue.of(Optional.ofNullable(compareItem.getAdClickRatio()).orElse(0.0)));
                                voBuilder.setCompareAdConversionRate(DoubleValue.of(Optional.ofNullable(compareItem.getAdConversionRate()).orElse(0.0)));
                                voBuilder.setCompareSales(DoubleValue.of(Optional.ofNullable(compareItem.getSales()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareSalesConversionRate(DoubleValue.of(Optional.ofNullable(compareItem.getSalesConversionRate()).orElse(0.0)));
                                voBuilder.setCompareCost(DoubleValue.of(Optional.ofNullable(compareItem.getCost()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAcos(DoubleValue.of(Optional.ofNullable(compareItem.getAcos()).orElse(BigDecimal.ZERO).doubleValue()));
                                // 花费占比
                                voBuilder.setCompareAdCostPercentage(StringUtils.isNotBlank(compareItem.getAdCostPercentage()) ? compareItem.getAdCostPercentage() : "0");
                                // 销售额占比
                                voBuilder.setCompareAdSalePercentage(StringUtils.isNotBlank(compareItem.getAdSalePercentage()) ? compareItem.getAdSalePercentage() : "0");
                                // 订单量占比
                                voBuilder.setCompareAdOrderNumPercentage(StringUtils.isNotBlank(compareItem.getAdOrderNumPercentage()) ? compareItem.getAdOrderNumPercentage() : "0");
                                // 销量占比
                                voBuilder.setCompareOrderNumPercentage(StringUtils.isNotBlank(compareItem.getOrderNumPercentage()) ? compareItem.getOrderNumPercentage() : "0");
                                voBuilder.setCompareOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getOrdersNewToBrand14d()).orElse(0)));
                                voBuilder.setCompareSalesNewToBrandFTD(DoubleValue.of(Optional.ofNullable(compareItem.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareImpressionRank(Int32Value.of(Optional.ofNullable(compareItem.getImpressionRank()).orElse(0)));
                                voBuilder.setCompareImpressionShare(DoubleValue.of(Optional.ofNullable(compareItem.getImpressionShare()).orElse(0.0)));
                                voBuilder.setCompareOrderRateNewToBrandFTD(DoubleValue.of(Optional.ofNullable(compareItem.getOrderRateNewToBrand14d()).orElse(0.0)));
                                voBuilder.setCompareSalesRateNewToBrandFTD(DoubleValue.of(Optional.ofNullable(compareItem.getSalesRateNewToBrand14d()).orElse(0.0)));
                                voBuilder.setCompareOrdersNewToBrandPercentageFTD(DoubleValue.of(Optional.ofNullable(compareItem.getOrdersNewToBrandPercentage14d()).orElse(0.0)));

                                voBuilder.setCompareVideo5SecondViewRate(Optional.ofNullable(compareItem.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideo5SecondViews(Optional.ofNullable(compareItem.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoCompleteViews(Optional.ofNullable(compareItem.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoFirstQuartileViews(Optional.ofNullable(compareItem.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoMidpointViews(Optional.ofNullable(compareItem.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoThirdQuartileViews(Optional.ofNullable(compareItem.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoUnmutes(Optional.ofNullable(compareItem.getVideoUnmutes()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareViewImpressions(Optional.ofNullable(compareItem.getViewImpressions()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareViewabilityRate(Optional.ofNullable(compareItem.getViewabilityRate()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareViewClickThroughRate(Optional.ofNullable(compareItem.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                                //环比值,计算
                                voBuilder.setCompareCpaRate(voBuilder.getCompareCpa().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getCpa().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareCpa().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareCpa().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareCpcRate(voBuilder.getCompareCpc().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getCpc().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareCpc().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareCpc().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareRoasRate(voBuilder.getCompareRoas().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getRoas().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareRoas().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareRoas().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAsotsRate(voBuilder.getCompareAsots().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAsots().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAsots().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAsots().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAcotsRate(voBuilder.getCompareAcots().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAcots().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAcots().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAcots().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getImpressions().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareImpressions().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareImpressions().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareClicksRate(voBuilder.getCompareClicks().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getClicks().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareClicks().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareClicks().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getOrderNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareOrderNum().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareOrderNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSaleNumRate(voBuilder.getCompareSaleNum().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSaleNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSaleNum().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSaleNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareClickRateRate(voBuilder.getCompareClickRate().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getClickRate().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareClickRate().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareClickRate().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdClickRatioRate(voBuilder.getCompareAdClickRatio().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdClickRatio().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdClickRatio().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdClickRatio().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdConversionRateRate(voBuilder.getCompareAdConversionRate().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdConversionRate().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdConversionRate().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdConversionRate().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSalesRate(voBuilder.getCompareSales().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSales().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSales().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSales().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSalesConversionRateRate(voBuilder.getCompareSalesConversionRate().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSalesConversionRate().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSalesConversionRate().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSalesConversionRate().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareCostRate(voBuilder.getCompareCost().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getCost().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareCost().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareCost().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAcosRate(voBuilder.getCompareAcos().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAcos().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAcos().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAcos().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                                voBuilder.setCompareAdCostPercentageRate(new BigDecimal(voBuilder.getCompareAdCostPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        (new BigDecimal(voBuilder.getAdCostPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdCostPercentage())))
                                                .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdSalePercentageRate(new BigDecimal(voBuilder.getCompareAdSalePercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        (new BigDecimal(voBuilder.getAdSalePercentage()).subtract(new BigDecimal(voBuilder.getCompareAdSalePercentage())))
                                                .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSalePercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        (new BigDecimal(voBuilder.getAdOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage())))
                                                .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        (new BigDecimal(voBuilder.getOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareOrderNumPercentage())))
                                                .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNumPercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrdersNewToBrandFTDRate(voBuilder.getCompareOrdersNewToBrandFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getOrdersNewToBrandFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareOrdersNewToBrandFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareOrdersNewToBrandFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSalesNewToBrandFTDRate(voBuilder.getCompareSalesNewToBrandFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSalesNewToBrandFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSalesNewToBrandFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSalesNewToBrandFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareImpressionRankRate(voBuilder.getCompareImpressionRank().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getImpressionRank().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareImpressionRank().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareImpressionRank().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareImpressionShareRate(voBuilder.getCompareImpressionShare().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getImpressionShare().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareImpressionShare().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareImpressionShare().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrderRateNewToBrandFTDRate(voBuilder.getCompareOrderRateNewToBrandFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getOrderRateNewToBrandFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareOrderRateNewToBrandFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareOrderRateNewToBrandFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSalesRateNewToBrandFTDRate(voBuilder.getCompareSalesRateNewToBrandFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSalesRateNewToBrandFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSalesRateNewToBrandFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSalesRateNewToBrandFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrdersNewToBrandPercentageFTDRate(voBuilder.getCompareOrdersNewToBrandPercentageFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getOrdersNewToBrandPercentageFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareOrdersNewToBrandPercentageFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareOrdersNewToBrandPercentageFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareVideo5SecondViewRateRate(MathUtil.isNullOrZero(voBuilder.getCompareVideo5SecondViewRate()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideo5SecondViewRate(), voBuilder.getCompareVideo5SecondViewRate(), 4), 100)));
                                voBuilder.setCompareVideo5SecondViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideo5SecondViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideo5SecondViews(), voBuilder.getCompareVideo5SecondViews(), 4), 100)));
                                voBuilder.setCompareVideoCompleteViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoCompleteViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoCompleteViews(), voBuilder.getCompareVideoCompleteViews(), 4), 100)));
                                voBuilder.setCompareVideoFirstQuartileViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoFirstQuartileViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoFirstQuartileViews(), voBuilder.getCompareVideoFirstQuartileViews(), 4), 100)));
                                voBuilder.setCompareVideoMidpointViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoMidpointViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoMidpointViews(), voBuilder.getCompareVideoMidpointViews(), 4), 100)));
                                voBuilder.setCompareVideoThirdQuartileViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoThirdQuartileViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoThirdQuartileViews(), voBuilder.getCompareVideoThirdQuartileViews(), 4), 100)));
                                voBuilder.setCompareVideoUnmutesRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoUnmutes()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoUnmutes(), voBuilder.getCompareVideoUnmutes(), 4), 100)));
                                voBuilder.setCompareViewImpressionsRate(MathUtil.isNullOrZero(voBuilder.getCompareViewImpressions()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewImpressions(), voBuilder.getCompareViewImpressions(), 4), 100)));
                                voBuilder.setCompareViewabilityRateRate(MathUtil.isNullOrZero(voBuilder.getCompareViewabilityRate()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewabilityRate(), voBuilder.getCompareViewabilityRate(), 4), 100)));
                                voBuilder.setCompareViewClickThroughRateRate(MathUtil.isNullOrZero(voBuilder.getCompareViewClickThroughRate()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewClickThroughRate(), voBuilder.getCompareViewClickThroughRate(), 4), 100)));
                                voBuilder.setCompareAdvertisingUnitPriceRate(MathUtil.isNullOrZero(voBuilder.getCompareAdvertisingUnitPrice()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAdvertisingUnitPrice(), voBuilder.getCompareAdvertisingUnitPrice(), 4), 100)));

                            }
                        }


                        return voBuilder.build();
                    }
            ).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }

        return AllQueryWordDataResponse.AdQueryWordsHomeVo.newBuilder()
                .setPage(pageBuilder.build())
                .build();
    }

    @Override
    public Page getAdQueryWordsPageVo(Integer puid, CpcQueryWordDto dto, Page page) {
        List<String> queryIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(dto.getWordRoot())) {
            //获取列表页所有queryId
            List<String> queryIdList = cpcSbQueryKeywordReportDao.listQueryIdByQueryWordDto(dto);
            if (CollectionUtils.isEmpty(queryIdList)) {
                return page;
            }
            queryIds = wordRootKeywordSbDao.listQueryIdByWordRootAndQueryIdList(puid, dto.getShopId(), dto.getWordRoot(), queryIdList);
            if (CollectionUtils.isEmpty(queryIds)) {
                return page;
            }
            dto.setQueryIds(queryIds);
        }

        if (CollectionUtils.isNotEmpty(queryIds)) {
            dto.setQueryIds(queryIds);
        }

        //todo:干掉不合理参数,避免前端误传
        dto.setCampaignIds(null);
        //分页数据
        long allKeywordTime = System.currentTimeMillis();
        if (StringUtils.isNotBlank(dto.getPortfolioId()) || StringUtils.isNotBlank(dto.getState()) || StringUtils.isNotBlank(dto.getServingStatus())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(puid, dto.getShopId(), dto.getPortfolioId(), dto.getState(), dto.getServingStatus(), CampaignTypeEnum.sb.getCampaignType());
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                dto.setCampaignIdList(campaignIds);
            } else {
                return page;
            }
        }

        if (CollectionUtils.isNotEmpty(dto.getQueryWordTagTypeList())) {
            List<SearchQueryTagParam> queryTagParams = cpcSbQueryKeywordReportDao.listAdGroupIdByQueryWordDto(dto);
            List<String> matchTypeList = Lists.newArrayList();
            for (String matchType : dto.getQueryWordTagTypeList()) {
                if ("isExact".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.EXACT);
                }
                if ("isBroad".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.BROAD);
                }
                if ("isPhrase".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.PHRASE);
                }
                if ("isNegativeExact".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.NEGATIVEEXACT);
                }
                if ("isNegativePhrase".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.NEGATIVEPHRASE);
                }
            }
            if (CollectionUtils.isNotEmpty(queryTagParams)) {
                if (CollectionUtils.isNotEmpty(matchTypeList)) {
                    List<SearchQueryTagParam> searchQueryTagParamArrayList = Lists.newArrayList();
                    List<SearchQueryTagParam> searchQueryTagParamList = amazonSbAdKeywordDao.getSearchQueryTag(puid, dto.getShopId(), matchTypeList, queryTagParams);
                    List<SearchQueryTagParam> searchQueryTagParams = amazonSbAdNeKeywordDao.getSearchQueryTag(puid, dto.getShopId(), matchTypeList, queryTagParams);
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamList)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParamList);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParams)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParams);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamArrayList)) {
                        dto.setSearchQueryTagParamList(searchQueryTagParamArrayList);
                    } else {
                        return page;
                    }
                } else {
                    return page;
                }
            } else {
                return page;
            }
        }

        AdMetricDto adMetricDto = cpcSbQueryKeywordReportDao.getSumAdMetricDto(puid, dto);
        page = cpcSbQueryKeywordReportDao.pageList(puid, dto, page);
        log.info("==============================查询所有搜索词关键词花费时间 分页 {} ==============================", System.currentTimeMillis() - allKeywordTime);

        List<CpcSbQueryKeywordReport> poList = page.getRows();

        List<String> campaignIds = poList.stream().filter(Objects::nonNull).map(CpcSbQueryKeywordReport::getCampaignId).distinct().collect(Collectors.toList());
        List<String> groupIds = poList.stream().filter(Objects::nonNull).map(CpcSbQueryKeywordReport::getAdGroupId).distinct().collect(Collectors.toList());

        Map<String, AmazonAdPortfolio> portfolioMap = null;
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            List<String> portfolioIds = amazonAdSbCampaignDao.getPortfolioListByCampaignIds(puid, dto.getShopId(), campaignIds);
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                portfolioMap = portfolioDao.getPortfolioList(puid, dto.getShopId(), portfolioIds).stream()
                        .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
            }
        }

        //批量查询广告活动和广告组

        long allKeywordTime2 = System.currentTimeMillis();
        List<AmazonAdCampaignAll> byCampaignIds = null;
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            byCampaignIds = amazonAdSbCampaignDao.getByCampaignIds(puid, dto.getShopId(), campaignIds);
        }

        Map<String, AmazonAdCampaignAll> campaignMap = null;
        if (CollectionUtils.isNotEmpty(byCampaignIds)) {
            campaignMap = byCampaignIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item -> item, (a, b) -> a));
        }

        Map<String, AmazonSbAdGroup> groupMap = null;
        List<AmazonSbAdGroup> adGroupByIds = null;
        if (CollectionUtils.isNotEmpty(groupIds)) {
            adGroupByIds = amazonSbAdGroupDao.getAdGroupByIds(puid, dto.getShopId(), groupIds);
        }

        if (CollectionUtils.isNotEmpty(adGroupByIds)) {
            groupMap = adGroupByIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, item -> item, (a, b) -> a));
        }
        log.info("==============================查询所有搜索词关键词花费时间 批量查询数据 {} ==============================", System.currentTimeMillis() - allKeywordTime2);

        if (CollectionUtils.isNotEmpty(poList)) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            Map<String, AmazonAdCampaignAll> finalCampaignMap = campaignMap;
            Map<String, AmazonSbAdGroup> finalGroupMap = groupMap;
            Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;

            List<String> groupIdList = poList.stream().filter(Objects::nonNull).map(CpcSbQueryKeywordReport::getAdGroupId)
                    .distinct().collect(Collectors.toList());

            List<AmazonSbAdKeyword> keywordList = null;
            List<AmazonSbAdNeKeyword> neKeywordList = null;
            if (CollectionUtils.isNotEmpty(groupIdList)) {
                keywordList = amazonSbAdKeywordDao.listByGroupIdList(puid, dto.getShopId(), groupIdList);
                neKeywordList = amazonSbAdNeKeywordDao.listByGroupIdList(puid, dto.getShopId(), groupIdList);
            }


            Map<String, List<AmazonSbAdKeyword>> groupKeywordMapList = new HashMap<>();
            if (CollectionUtils.isNotEmpty(keywordList)) {
                for (AmazonSbAdKeyword keyword : keywordList) {
                    if (StringUtils.isBlank(keyword.getAdGroupId())) {
                        continue;
                    }
                    if (groupKeywordMapList.containsKey(keyword.getAdGroupId())) {
                        groupKeywordMapList.get(keyword.getAdGroupId()).add(keyword);
                    } else {
                        groupKeywordMapList.put(keyword.getAdGroupId(), Lists.newArrayList(keyword));
                    }
                }
            }

            Map<String, List<AmazonSbAdNeKeyword>> groupNeKeywordMapList = new HashMap<>();
            if (CollectionUtils.isNotEmpty(neKeywordList)) {
                for (AmazonSbAdNeKeyword keyword : neKeywordList) {
                    if (StringUtils.isBlank(keyword.getAdGroupId())) {
                        continue;
                    }
                    if (groupNeKeywordMapList.containsKey(keyword.getAdGroupId())) {
                        groupNeKeywordMapList.get(keyword.getAdGroupId()).add(keyword);
                    } else {
                        groupNeKeywordMapList.put(keyword.getAdGroupId(), Lists.newArrayList(keyword));
                    }
                }
            }

            BigDecimal sumRange = dto.getShopSales();

            AdMetricDto finalAdMetricDto = adMetricDto;
            poList.stream().filter(Objects::nonNull).forEach(e -> {
                ReportVo vo = new ReportVo();
                vo.setMarketplaceId(e.getMarketplaceId());
                vo.setCost(e.getCost());
                vo.setSales(e.getSales14d());
                vo.setImpressions(e.getImpressions());
                vo.setClicks(e.getClicks());
                vo.setOrderNum(e.getConversions14d());
                vo.setSaleNum(e.getConversions14d());
                vo.setAdFormat(e.getAdFormat());
                vo.setType(Constants.SB);
                Double clickRate = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) * 100, Double.valueOf(vo.getImpressions()), 2);
                vo.setClickRate(clickRate);
                Double salesConversionRate = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getSaleNum()) * 100, Double.valueOf(vo.getClicks()), 2);
                vo.setSalesConversionRate(salesConversionRate);
                BigDecimal rate = BigDecimal.valueOf(vo.getClicks()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(vo.getClicks()));
                BigDecimal cpc = rate.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setCpc(cpc);


                BigDecimal rate2 = e.getSales14d().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), e.getSales14d());
                rate2 = MathUtil.multiply(rate2, BigDecimal.valueOf(100));
                BigDecimal acos = rate2.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setAcos(acos);

                BigDecimal rate3 = BigDecimal.valueOf(e.getConversions14d()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(e.getConversions14d()));
                BigDecimal cpa = rate3.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setCpa(cpa);

                if (e.getSales14d() != null && e.getCost() != null) {
                    vo.setRoas(e.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getSales14d().divide(e.getCost(), 2, RoundingMode.HALF_UP));
                }
                //新加指标,需要获取广告
                BigDecimal shopTotalSales = Optional.ofNullable(sumRange).orElse(BigDecimal.ZERO);
                vo.setAcots(shopTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getCost().multiply(BigDecimal.valueOf(100)).divide(sumRange, 2, RoundingMode.HALF_UP));
                vo.setAsots(shopTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getSales14d().multiply(BigDecimal.valueOf(100)).divide(sumRange, 2, RoundingMode.HALF_UP));
                vo.setKeywordText(e.getKeywordText());
                vo.setMatchType(e.getMatchType());
                vo.setKeywordId(e.getKeywordId());
                vo.setTargetingType(Constants.MANUAL); //关键词应该只有手动
                vo.setIsTargetType(false);
                vo.setQuery(e.getQuery());
                vo.setQueryCn(e.getQueryCn());

                filterMetricData(e, vo, finalAdMetricDto);

                //检查该搜索词是否添加过
                if (groupKeywordMapList.size() > 0 && groupKeywordMapList.containsKey(e.getAdGroupId()) && StringUtils.isNotBlank(e.getQuery())) {
                    List<AmazonSbAdKeyword> amazonAdKeywords = groupKeywordMapList.get(e.getAdGroupId());
                    if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                        for (AmazonSbAdKeyword keyword : amazonAdKeywords) {
                            if (StringUtils.isNotBlank(e.getQuery()) && StringUtils.isNotBlank(keyword.getKeywordText())) {
                                if (e.getQuery().trim().equalsIgnoreCase(keyword.getKeywordText().trim())) {
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.EXACT)) {
                                        vo.setIsExact(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.BROAD)) {
                                        vo.setIsBroad(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.PHRASE)) {
                                        vo.setIsPhrase(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.NEGATIVEEXACT)) {
                                        vo.setIsNegativeExact(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.NEGATIVEPHRASE)) {
                                        vo.setIsNegativePhrase(true);
                                    }
                                }
                            }
                        }
                    }
                }

                //检查该搜索词是否添加过否定词
                if (groupNeKeywordMapList.size() > 0 && groupNeKeywordMapList.containsKey(e.getAdGroupId()) && StringUtils.isNotBlank(e.getQuery())) {
                    List<AmazonSbAdNeKeyword> amazonAdKeywords = groupNeKeywordMapList.get(e.getAdGroupId());
                    if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                        for (AmazonSbAdNeKeyword keyword : amazonAdKeywords) {
                            if (StringUtils.isNotBlank(e.getQuery()) && StringUtils.isNotBlank(keyword.getKeywordText())) {
                                if (e.getQuery().trim().equalsIgnoreCase(keyword.getKeywordText().trim())) {
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.EXACT)) {
                                        vo.setIsExact(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.BROAD)) {
                                        vo.setIsBroad(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.PHRASE)) {
                                        vo.setIsPhrase(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.NEGATIVEEXACT)) {
                                        vo.setIsNegativeExact(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.NEGATIVEPHRASE)) {
                                        vo.setIsNegativePhrase(true);
                                    }
                                }
                            }
                        }
                    }
                }

                vo.setCampaignId(e.getCampaignId());
                //广告活动名称使用最新的
                if (MapUtils.isNotEmpty(finalCampaignMap) && finalCampaignMap.containsKey(e.getCampaignId())) {
                    AmazonAdCampaignAll campaign = finalCampaignMap.get(vo.getCampaignId());
                    vo.setCampaignName(campaign.getName());
                    vo.setCampaignStatus(campaign.getState());
                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        AmazonAdPortfolio adPortfolio = portfolioDao.getByPortfolioId(puid, campaign.getShopId(), campaign.getPortfolioId());
                        vo.setPortfolioId(campaign.getPortfolioId());
                        if (finalPortfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = finalPortfolioMap.get(campaign.getPortfolioId());
                            vo.setPortfolioName(amazonAdPortfolio.getName());
                            vo.setIsHidden(adPortfolio.getIsHidden());
                        } else {
                            vo.setPortfolioName("广告组合待同步");
                        }
                    } else {
                        vo.setPortfolioName("-");
                    }
                } else {
                    vo.setCampaignName(e.getCampaignName());
                }
                vo.setAdGroupId(e.getAdGroupId());
                //广告组名称使用最新的
                if (MapUtils.isNotEmpty(finalGroupMap) && finalGroupMap.containsKey(e.getAdGroupId())) {
                    vo.setAdGroupName(finalGroupMap.get(e.getAdGroupId()).getName());
                    vo.setAdGroupType(finalGroupMap.get(e.getAdGroupId()).getAdGroupType());
                    vo.setAdFormat(finalGroupMap.get(e.getAdGroupId()).getAdFormat());
                    vo.setAdGroupState(finalGroupMap.get(e.getAdGroupId()).getState());
                }
                //“品牌新买家”订单量
                vo.setOrdersNewToBrand14d(Optional.ofNullable(e.getOrdersNewToBrand14d()).orElse(0));
                //“品牌新买家”销售额
                vo.setSalesNewToBrand14d(Optional.ofNullable(e.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO));
                //搜索词展示量排名
                vo.setImpressionRank(Optional.ofNullable(e.getSearchTermImpressionRank()).orElse(0));
                //搜索词展示份额
                vo.setImpressionShare(Optional.ofNullable(e.getSearchTermImpressionShare()).orElse(0.00));
                //“品牌新买家”订单百分比
                BigDecimal orderRateNewToBrand14d = e.getConversions14d() == 0 ? BigDecimal.ZERO : new BigDecimal(Optional.ofNullable(e.getOrdersNewToBrand14d()).orElse(0)).multiply(new BigDecimal("100")).divide(new BigDecimal(e.getConversions14d()), 2, BigDecimal.ROUND_HALF_UP);
                vo.setOrderRateNewToBrand14d(orderRateNewToBrand14d.doubleValue());
                //“品牌新买家”销售额百分比
                BigDecimal salesRateNewToBrand14d = e.getSales14d().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : Optional.ofNullable(e.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO).multiply(new BigDecimal("100")).divide(e.getSales14d(), 2, BigDecimal.ROUND_HALF_UP);
                vo.setSalesRateNewToBrand14d(salesRateNewToBrand14d.doubleValue());
                //“品牌新买家”订单转化率
                BigDecimal ordersNewToBrandPercentage14d = e.getClicks() == 0 ? BigDecimal.ZERO : new BigDecimal(Optional.ofNullable(e.getOrdersNewToBrand14d()).orElse(0)).multiply(new BigDecimal("100")).divide(new BigDecimal(e.getClicks()), 2, BigDecimal.ROUND_HALF_UP);
                vo.setOrdersNewToBrandPercentage14d(ordersNewToBrandPercentage14d.doubleValue());

                vo.setVideo5SecondViews(Optional.ofNullable(e.getVideo5SecondViews()).orElse(0));
                vo.setVideo5SecondViewRate(vo.getImpressions() == 0 ? 0.00 :
                        MathUtil.divideOfObject(MathUtil.multiplyOfObject(vo.getVideo5SecondViews(), 100), vo.getImpressions()).doubleValue());
                vo.setVideoCompleteViews(Optional.ofNullable(e.getVideoCompleteViews()).orElse(0));
                vo.setVideoFirstQuartileViews(Optional.ofNullable(e.getVideoFirstQuartileViews()).orElse(0));
                vo.setVideoMidpointViews(Optional.ofNullable(e.getVideoMidpointViews()).orElse(0));
                vo.setVideoThirdQuartileViews(Optional.ofNullable(e.getVideoThirdQuartileViews()).orElse(0));
                vo.setVideoUnmutes(Optional.ofNullable(e.getVideoUnmutes()).orElse(0));
                vo.setViewImpressions(Optional.ofNullable(e.getViewableImpressions()).orElse(0));
                vo.setViewabilityRate(vo.getImpressions() == 0 ? BigDecimal.ZERO :
                        MathUtil.divideOfObject(MathUtil.multiplyOfObject(vo.getViewImpressions(), 100), vo.getImpressions()));
                vo.setViewClickThroughRate(vo.getViewImpressions() == 0 ? BigDecimal.ZERO :
                        MathUtil.divideOfObject(MathUtil.multiplyOfObject(vo.getClicks(), 100), vo.getViewImpressions()));
                if (vo.getSales() != null && vo.getOrderNum() != null && vo.getOrderNum() != 0) {
                    vo.setAdvertisingUnitPrice(MathUtil.divideOfObject(vo.getSales(), vo.getOrderNum()));
                } else {
                    vo.setAdvertisingUnitPrice(BigDecimal.ZERO);
                }

                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }


    @Override
    public AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo getAllQueryWordAggregateData(Integer puid, CpcQueryWordDto dto) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(dto.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal shopSaleData;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {  //店铺销售额
            shopSaleData = BigDecimal.ZERO;
        } else {
            shopSaleData = shopSaleDto.getSumRange();
        }
        dto.setShopSales(shopSaleData);

        String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();

        //按条件查询所有数据
        List<AdHomePerformancedto> list = new ArrayList<>();
        List<AdHomePerformancedto> listCompare = new ArrayList<>();
        List<AdHomePerformancedto> reportDayList = new ArrayList<>();  //每日汇总数据

        int searchDataType = Optional.ofNullable(dto.getSearchDataType()).orElse(SearchDataTypeEnum.ALL.getCode());
        //是否查询汇总数据
        boolean searchAggregateData = SearchDataTypeEnum.containsAggregate(searchDataType);
        //是否查询图表数据
        boolean searchChartData = SearchDataTypeEnum.containsChart(searchDataType);

        AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo.Builder builder = AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo.newBuilder();

        boolean isNull = false;  // 查询的数据为空
        if (StringUtils.isNotBlank(dto.getPortfolioId()) || StringUtils.isNotBlank(dto.getState()) || StringUtils.isNotBlank(dto.getServingStatus())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(puid, dto.getShopId(), dto.getPortfolioId(), dto.getState(), dto.getServingStatus(), CampaignTypeEnum.sb.getCampaignType());
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                dto.setCampaignIdList(campaignIds);
            } else {
                isNull = true;
            }
        }

        if (StringUtils.isNotBlank(dto.getWordRoot())) {
            //获取列表页所有queryId
            List<String> queryIdList = cpcSbQueryKeywordReportDao.listQueryIdByQueryWordDto(dto);
            if (CollectionUtils.isEmpty(queryIdList)) {
                isNull = true;
            }
            List<String> queryIds = wordRootKeywordSbDao.listQueryIdByWordRootAndQueryIdList(puid, dto.getShopId(), dto.getWordRoot(), queryIdList);
            if (CollectionUtils.isEmpty(queryIds)) {
                isNull = true;
            }
            dto.setQueryIds(queryIds);
        }

        if (CollectionUtils.isNotEmpty(dto.getQueryWordTagTypeList())) {
            List<SearchQueryTagParam> queryTagParams = cpcSbQueryKeywordReportDao.listAdGroupIdByQueryWordDto(dto);
            List<String> matchTypeList = Lists.newArrayList();
            for (String matchType : dto.getQueryWordTagTypeList()) {
                if ("isExact".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.EXACT);
                }
                if ("isBroad".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.BROAD);
                }
                if ("isPhrase".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.PHRASE);
                }
                if ("isNegativeExact".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.NEGATIVEEXACT);
                }
                if ("isNegativePhrase".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.NEGATIVEPHRASE);
                }
            }
            if (CollectionUtils.isNotEmpty(queryTagParams)) {
                if (CollectionUtils.isNotEmpty(matchTypeList)) {
                    List<SearchQueryTagParam> searchQueryTagParamArrayList = Lists.newArrayList();
                    List<SearchQueryTagParam> searchQueryTagParamList = amazonSbAdKeywordDao.getSearchQueryTag(puid, dto.getShopId(), matchTypeList, queryTagParams);
                    List<SearchQueryTagParam> searchQueryTagParams = amazonSbAdNeKeywordDao.getSearchQueryTag(puid, dto.getShopId(), matchTypeList, queryTagParams);
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamList)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParamList);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParams)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParams);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamArrayList)) {
                        dto.setSearchQueryTagParamList(searchQueryTagParamArrayList);
                    } else {
                        isNull = true;
                    }
                } else {
                    isNull = true;
                }
            } else {
                isNull = true;
            }
        }


        if (isNull) {  // 查询数据置为空
            list = new ArrayList<>();
            reportDayList = new ArrayList<>();
        } else {
            List<AdHomePerformancedto> keywordList = null;

            keywordList = cpcSbQueryKeywordReportDao.getReportKeywordByDate(puid, dto);
            if (dto.getIsCompare()) {
                CpcQueryWordDto compareDto = new CpcQueryWordDto();
                BeanUtils.copyProperties(dto, compareDto);
                compareDto.setStart(compareDto.getCompareStartDate());
                compareDto.setEnd(compareDto.getCompareEndDate());
                listCompare = cpcSbQueryKeywordReportDao.getReportKeywordByDate(puid, compareDto);
            }
            if (CollectionUtils.isNotEmpty(keywordList)) {
                list.addAll(keywordList);
                List<String> keywordIdList = keywordList.stream().map(AdHomePerformancedto::getKeywordId).collect(Collectors.toList());
                if (searchChartData) {
                    List<AdHomePerformancedto> keywordDayList = cpcSbQueryKeywordReportDao.getReportKeywordByKeywordIdList(puid, dto.getShopId(),
                            dto.getStart(), dto.getEnd(), keywordIdList, dto);

                    if (CollectionUtils.isNotEmpty(keywordDayList)) {
                        reportDayList.addAll(keywordDayList);
                    }
                }
            }
        }
        //环比数据
        BigDecimal shopSaleDataCompare = BigDecimal.ZERO;
        if (dto.getIsCompare()) {
            ShopSaleDto shopSaleDtoCompare = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getCompareStartDate(), dto.getCompareEndDate());

            if (shopSaleDtoCompare == null || shopSaleDtoCompare.getSumRange() == null) {  //店铺销售额
                shopSaleDataCompare = BigDecimal.ZERO;
            } else {
                shopSaleDataCompare = shopSaleDtoCompare.getSumRange();
            }
        }
        if (searchChartData) {
            //处理chart数据
            List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, dto.getStart(), dto.getEnd(), reportDayList, shopSaleData);
            List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, shopSaleData);
            List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, shopSaleData);
            builder.addAllDay(dayPerformanceVos)
                    .addAllMonth(monthPerformanceVos)
                    .addAllWeek(weekPerformanceVos);
        }
        //汇总指标数据
        AdHomeAggregateDataRpcVo aggregateDataVo = getQueryKeywordAggregateDataVo(list, listCompare, shopSaleData, shopSaleDataCompare, isVc);

        return builder
                .setAggregateDataVo(aggregateDataVo)
                .build();
    }

    @Override
    public AllQueryWordDataResponse.AdQueryWordsHomeVo getDorisAllQueryWordData(Integer puid, CpcQueryWordDto dto, Page page) {
        if (CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(dto.getType()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN.equals(dto.getSearchValue())) {
                dto.setSearchValue(Constants.keywords_related_to_your_brand);
            } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN.equals(dto.getSearchValue())) {
                dto.setSearchValue(Constants.keywords_related_to_your_landing_pages);
            }
        }
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(dto.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal shopSaleData;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {  //店铺销售额
            shopSaleData = BigDecimal.ZERO;
        } else {
            shopSaleData = shopSaleDto.getSumRange();
        }
        dto.setShopSales(shopSaleData);

        //组装vo
        long allKeywordTime = System.currentTimeMillis();
        if (dto.isQueryJoinSearchTermsRank()) {
            String date = weekSearchTermsAnalysisService.getLatestDate(dto.getMarketplaceId());
            dto.setLastWeekSearchTermsRankDate(date);
        }
        Page pageVo = this.getDorisAdQueryWordsPageVo(puid, dto, page);
        log.info("==============================查询所有搜索词关键词花费时间 {} ==============================", System.currentTimeMillis() - allKeywordTime);
        if (pageVo.getTotalSize() > Constants.TOTALSIZELIMIT) {  //总数大于十万
            int totalPage = Constants.TOTALSIZELIMIT / pageVo.getPageSize();
            pageVo.setTotalPage(totalPage);
            pageVo.setTotalSize(Constants.TOTALSIZELIMIT);
        }
        fillAdStrategy(dto,pageVo.getRows());

        //处理分页
        AllQueryWordDataResponse.AdQueryWordsHomeVo.Page.Builder pageBuilder = AllQueryWordDataResponse.AdQueryWordsHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(pageVo.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(pageVo.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(pageVo.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(pageVo.getTotalSize()));
        List<ReportVo> rows = pageVo.getRows();

        //填充ABA展示信息（仅支持美国站）
//        long t1 = Instant.now().toEpochMilli();
//        fillABARankField(rows);
//        log.info("填充ABA展示信息花费时间：{}", Instant.now().toEpochMilli() - t1);

        if (CollectionUtils.isNotEmpty(rows)) {
            //环比数据
            Map<String, ReportVo> compareQueryWordMap = null;
            if (dto.getIsCompare()) {
                //对比时无须高级搜索条件
                dto.setUseAdvanced(false);
                ShopSaleDto shopSaleDtoCompare = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getCompareStartDate(), dto.getCompareEndDate());
                BigDecimal shopSaleDataCompare;
                if (shopSaleDtoCompare == null || shopSaleDtoCompare.getSumRange() == null) {  //店铺销售额
                    shopSaleDataCompare = BigDecimal.ZERO;
                } else {
                    shopSaleDataCompare = shopSaleDtoCompare.getSumRange();
                }
                dto.setShopSales(shopSaleDataCompare);
                dto.setStart(dto.getCompareStartDate());
                dto.setEnd(dto.getCompareEndDate());

                //通过asin精确查询本来
                dto.setSearchType("exact");
                dto.setSearchField("query");
                dto.setSearchValue(rows.stream().map(ReportVo::getQuery).collect(Collectors.joining(StringUtil.SPECIAL_COMMA)));
                // 查对比数据的时候不需要联表
                boolean isQueryJoinSearchTermsRank = dto.isQueryJoinSearchTermsRank();
                dto.setQueryJoinSearchTermsRank(false);
                Page<ReportVo> pageVoCompare = this.getDorisAdQueryWordsPageVo(puid, dto, page);
                // 还原字段
                dto.setQueryJoinSearchTermsRank(isQueryJoinSearchTermsRank);
                compareQueryWordMap = pageVoCompare.getRows().stream().collect(Collectors.
                        toMap(k -> Optional.ofNullable(k.getKeywordId()).orElse("").concat("#")
                                .concat(Optional.ofNullable(k.getQuery()).orElse("")), Function.identity(), (a, b) -> a));
            }

            Map<String, ReportVo> finalCompareQueryWordMap = compareQueryWordMap;
            List<ReportRpcVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                        ReportRpcVo.Builder voBuilder = ReportRpcVo.newBuilder();
                        if (org.apache.commons.lang.StringUtils.isNotBlank(item.getCountDate())) {
                            voBuilder.setCountDate(item.getCountDate());
                        }
                        if (item.getShopId() != null) {
                            voBuilder.setShopId(Int32Value.of(item.getShopId()));
                        }
                        if (item.getMarketplaceId() != null) {
                            voBuilder.setMarketplaceId(item.getMarketplaceId());
                        }

                        voBuilder.setCpa(DoubleValue.of(Optional.ofNullable(item.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCpc(DoubleValue.of(Optional.ofNullable(item.getCpc()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setRoas(DoubleValue.of(Optional.ofNullable(item.getRoas()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setAsots(DoubleValue.of(Optional.ofNullable(item.getAsots()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setAcots(DoubleValue.of(Optional.ofNullable(item.getAcots()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                        voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                        voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
                        voBuilder.setSaleNum(Int32Value.of(Optional.ofNullable(item.getSaleNum()).orElse(0)));
                        voBuilder.setClickRate(DoubleValue.of(Optional.ofNullable(item.getClickRate()).orElse(0.0)));
                        voBuilder.setCpa(DoubleValue.of(Optional.ofNullable(item.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setNaturalSales(DoubleValue.of(Optional.ofNullable(item.getNaturalSales()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setAdClickRatio(DoubleValue.of(Optional.ofNullable(item.getAdClickRatio()).orElse(0.0)));
                        voBuilder.setAdConversionRate(DoubleValue.of(Optional.ofNullable(item.getAdConversionRate()).orElse(0.0)));
                        voBuilder.setSales(DoubleValue.of(Optional.ofNullable(item.getSales()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setSalesConversionRate(DoubleValue.of(Optional.ofNullable(item.getSalesConversionRate()).orElse(0.0)));
                        voBuilder.setCost(DoubleValue.of(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setAcos(DoubleValue.of(Optional.ofNullable(item.getAcos()).orElse(BigDecimal.ZERO).doubleValue()));
                        // 花费占比
                        voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                        // 销售额占比
                        voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                        // 订单量占比
                        voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                        // 销量占比
                        voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");

                        voBuilder.setNaturalClicks(org.apache.commons.lang.StringUtils.isNotBlank(item.getNaturalClicks()) ? item.getNaturalClicks() : "0");

                        if (StringUtils.isNotBlank(item.getCampaignId())) {
                            voBuilder.setCampaignId(item.getCampaignId());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupId())) {
                            voBuilder.setAdGroupId(item.getAdGroupId());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupType())) {
                            voBuilder.setAdGroupType(item.getAdGroupType());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupName())) {
                            voBuilder.setAdGroupName(item.getAdGroupName());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupState())) {
                            voBuilder.setAdGroupState(item.getAdGroupState());
                        }

                        if (StringUtils.isNotBlank(item.getCampaignName())) {
                            voBuilder.setCampaignName(item.getCampaignName());
                        }
                        if (StringUtils.isNotBlank(item.getCampaignStatus())) {
                            voBuilder.setCampaignState(item.getCampaignStatus());
                        }
                        if (StringUtils.isNotBlank(item.getTargetState())) {
                            voBuilder.setTargetState(item.getTargetState());
                        }

                        if (StringUtils.isNotBlank(item.getKeywordText())) {
                            if (SbMatchValueEnum.THEME.getMatchType().equalsIgnoreCase(item.getMatchType())) {
                                String keywordText = item.getKeywordText();
                                if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(keywordText) || Constants.keywords_related_to_your_brand.equalsIgnoreCase(keywordText)) {
                                    voBuilder.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
                                } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(keywordText) || Constants.keywords_related_to_your_landing_pages.equalsIgnoreCase(keywordText)) {
                                    voBuilder.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
                                } else {
                                    voBuilder.setKeywordText(keywordText);
                                }
                            } else {
                                voBuilder.setKeywordText(item.getKeywordText());
                            }
                        }
                        if (StringUtils.isNotBlank(item.getMatchType())) {
                            voBuilder.setMatchType(item.getMatchType().toLowerCase());
                        }
                        if (StringUtils.isNotBlank(item.getSku())) {
                            voBuilder.setSku(item.getSku());
                        }
                        if (StringUtils.isNotBlank(item.getAsin())) {
                            voBuilder.setAsin(item.getAsin());
                        }

                        if (StringUtils.isNotBlank(item.getQuery())) {
                            voBuilder.setQuery(item.getQuery());
                        }

                        if (StringUtils.isNotBlank(item.getQueryCn())) {
                            voBuilder.setQueryCn(item.getQueryCn());
                        }

                        if (StringUtils.isNotBlank(item.getParentAsin())) {
                            voBuilder.setParentAsin(item.getParentAsin());
                        }
                        if (StringUtils.isNotBlank(item.getTitle())) {
                            voBuilder.setTitle(item.getTitle());
                        }
                        if (StringUtils.isNotBlank(item.getMainImage())) {
                            voBuilder.setMainImage(item.getMainImage());
                        }
                        if (StringUtils.isNotBlank(item.getNegaType())) {
                            voBuilder.setNegaType(item.getNegaType());
                        }
                        if (StringUtils.isNotBlank(item.getTargetingType())) {
                            voBuilder.setTargetingType(item.getTargetingType());
                        }
                        if (StringUtils.isNotBlank(item.getKeywordId())) {
                            voBuilder.setKeywordId(item.getKeywordId());
                        }
                        if (StringUtils.isNotBlank(item.getAdId())) {
                            voBuilder.setAdId(item.getAdId());
                        }
                        if (StringUtils.isNotBlank(item.getTargetingText())) {
                            voBuilder.setTargetingText(item.getTargetingText());
                        }
                        if (StringUtils.isNotBlank(item.getSpCampaignType())) {
                            voBuilder.setSpCampaignType(item.getSpCampaignType());
                        }
                        if (StringUtils.isNotBlank(item.getSpGroupType())) {
                            voBuilder.setSpGroupType(item.getSpGroupType());
                        }

                        if (StringUtils.isNotBlank(item.getSpTargetType())) {
                            voBuilder.setSpTargetType(item.getSpTargetType());
                        }

                        if (StringUtils.isNotBlank(item.getTargetId())) {
                            voBuilder.setTargetId(item.getTargetId());
                        }
                        if (item.getIsBroad() != null) {
                            voBuilder.setIsBroad(BoolValue.of(item.getIsBroad()));
                        }
                        if (item.getIsPhrase() != null) {
                            voBuilder.setIsPhrase(BoolValue.of(item.getIsPhrase()));
                        }
                        if (item.getIsExact() != null) {
                            voBuilder.setIsExact(BoolValue.of(item.getIsExact()));
                        }
                        if (item.getIsNegativeExact() != null) {
                            voBuilder.setIsNegativeExact(BoolValue.of(item.getIsNegativeExact()));
                        }
                        if (item.getIsNegativePhrase() != null) {
                            voBuilder.setIsNegativePhrase(BoolValue.of(item.getIsNegativePhrase()));
                        }
                        if (item.getIsTargetType() != null) {
                            voBuilder.setIsTargetType(BoolValue.of(item.getIsTargetType()));
                        }
                        if (item.getDefaultBid() != null) {
                            voBuilder.setDefaultBid(item.getDefaultBid());
                        }
                        if (item.getPortfolioId() != null) {
                            voBuilder.setPortfolioId(item.getPortfolioId());
                        }
                        if (item.getPortfolioName() != null) {
                            voBuilder.setPortfolioName(item.getPortfolioName());
                        }
                        if (item.getIsHidden() != null) {
                            voBuilder.setIsHidden(item.getIsHidden());
                        }
                        if (item.getAdFormat() != null) {
                            voBuilder.setAdFormat(item.getAdFormat());
                        }
                        if (item.getType() != null) {
                            voBuilder.setType(item.getType());
                        }
                        if (StringUtils.isNotBlank(item.getQueryId())) {
                            voBuilder.setQueryId(item.getQueryId());
                        }
                        voBuilder.setQueryType(AmazonAd.AdQueryTypeEnum.SB_QUERY.getSearchField());
                        // 广告策略标签
                        if(CollectionUtils.isNotEmpty(item.getStrategyList())){
                            voBuilder.addAllAdStrategys(buildStrategyList(item));
                        }
                        //“品牌新买家”订单量
                        if (item.getOrdersNewToBrand14d() != null) {
                            voBuilder.setOrdersNewToBrandFTD(Int32Value.of(item.getOrdersNewToBrand14d()));
                        }
                        //“品牌新买家”销售额
                        if (item.getSalesNewToBrand14d() != null) {
                            voBuilder.setSalesNewToBrandFTD(DoubleValue.of(item.getSalesNewToBrand14d().doubleValue()));
                        }
                        //搜索词展示量排名
                        if (item.getImpressionRank() != null) {
                            voBuilder.setImpressionRank(Int32Value.of(item.getImpressionRank()));
                        }
                        //搜索词展示份额
                        if (item.getImpressionShare() != null) {
                            voBuilder.setImpressionShare(DoubleValue.of(item.getImpressionShare()));
                        }
                        //“品牌新买家”订单百分比
                        if (item.getOrderRateNewToBrand14d() != null) {
                            voBuilder.setOrderRateNewToBrandFTD(DoubleValue.of(item.getOrderRateNewToBrand14d()));
                        }
                        //“品牌新买家”销售额百分比
                        if (item.getSalesRateNewToBrand14d() != null) {
                            voBuilder.setSalesRateNewToBrandFTD(DoubleValue.of(item.getSalesRateNewToBrand14d()));
                        }
                        //“品牌新买家”订单转化率
                        if (item.getOrdersNewToBrandPercentage14d() != null) {
                            voBuilder.setOrdersNewToBrandPercentageFTD(DoubleValue.of(item.getOrdersNewToBrandPercentage14d()));
                        }
                        // ABA搜索词排名
                        voBuilder.setSearchFrequencyRank(item.getSearchFrequencyRank());
                        // ABA排名周变化率
                        if (item.getWeekRatio() != null) {
                            voBuilder.setWeekRatio(String.valueOf(Optional.ofNullable(item.getWeekRatio()).orElse(BigDecimal.ZERO)));
                        }

                        voBuilder.setVideo5SecondViewRate(Optional.ofNullable(item.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideo5SecondViews(Optional.ofNullable(item.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoCompleteViews(Optional.ofNullable(item.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoFirstQuartileViews(Optional.ofNullable(item.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoMidpointViews(Optional.ofNullable(item.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoThirdQuartileViews(Optional.ofNullable(item.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoUnmutes(Optional.ofNullable(item.getVideoUnmutes()).map(String::valueOf).orElse("0"));
                        voBuilder.setViewImpressions(Optional.ofNullable(item.getViewImpressions()).map(String::valueOf).orElse("0"));
                        voBuilder.setViewabilityRate(Optional.ofNullable(item.getViewabilityRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setViewClickThroughRate(Optional.ofNullable(item.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                        // 填充环比数据
                        if (MapUtils.isNotEmpty(finalCompareQueryWordMap)) {
                            String mapKey = Optional.ofNullable(item.getKeywordId()).orElse("").concat("#").concat(Optional.ofNullable(item.getQuery()).orElse(""));
                            if (finalCompareQueryWordMap.containsKey(mapKey)) {
                                ReportVo compareItem = finalCompareQueryWordMap.get(mapKey);
                                voBuilder.setCompareCpc(DoubleValue.of(Optional.ofNullable(compareItem.getCpc()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareRoas(DoubleValue.of(Optional.ofNullable(compareItem.getRoas()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAsots(DoubleValue.of(Optional.ofNullable(compareItem.getAsots()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAcots(DoubleValue.of(Optional.ofNullable(compareItem.getAcots()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareImpressions(Int32Value.of(Optional.ofNullable(compareItem.getImpressions()).orElse(0)));
                                voBuilder.setCompareClicks(Int32Value.of(Optional.ofNullable(compareItem.getClicks()).orElse(0)));
                                voBuilder.setCompareOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getOrderNum()).orElse(0)));
                                voBuilder.setCompareSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getSaleNum()).orElse(0)));
                                voBuilder.setCompareClickRate(DoubleValue.of(Optional.ofNullable(compareItem.getClickRate()).orElse(0.0)));
                                voBuilder.setCompareCpa(DoubleValue.of(Optional.ofNullable(compareItem.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAdClickRatio(DoubleValue.of(Optional.ofNullable(compareItem.getAdClickRatio()).orElse(0.0)));
                                voBuilder.setCompareAdConversionRate(DoubleValue.of(Optional.ofNullable(compareItem.getAdConversionRate()).orElse(0.0)));
                                voBuilder.setCompareSales(DoubleValue.of(Optional.ofNullable(compareItem.getSales()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareSalesConversionRate(DoubleValue.of(Optional.ofNullable(compareItem.getSalesConversionRate()).orElse(0.0)));
                                voBuilder.setCompareCost(DoubleValue.of(Optional.ofNullable(compareItem.getCost()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAcos(DoubleValue.of(Optional.ofNullable(compareItem.getAcos()).orElse(BigDecimal.ZERO).doubleValue()));
                                // 花费占比
                                voBuilder.setCompareAdCostPercentage(StringUtils.isNotBlank(compareItem.getAdCostPercentage()) ? compareItem.getAdCostPercentage() : "0");
                                // 销售额占比
                                voBuilder.setCompareAdSalePercentage(StringUtils.isNotBlank(compareItem.getAdSalePercentage()) ? compareItem.getAdSalePercentage() : "0");
                                // 订单量占比
                                voBuilder.setCompareAdOrderNumPercentage(StringUtils.isNotBlank(compareItem.getAdOrderNumPercentage()) ? compareItem.getAdOrderNumPercentage() : "0");
                                // 销量占比
                                voBuilder.setCompareOrderNumPercentage(StringUtils.isNotBlank(compareItem.getOrderNumPercentage()) ? compareItem.getOrderNumPercentage() : "0");
                                voBuilder.setCompareOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getOrdersNewToBrand14d()).orElse(0)));
                                voBuilder.setCompareSalesNewToBrandFTD(DoubleValue.of(Optional.ofNullable(compareItem.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareImpressionRank(Int32Value.of(Optional.ofNullable(compareItem.getImpressionRank()).orElse(0)));
                                voBuilder.setCompareImpressionShare(DoubleValue.of(Optional.ofNullable(compareItem.getImpressionShare()).orElse(0.0)));
                                voBuilder.setCompareOrderRateNewToBrandFTD(DoubleValue.of(Optional.ofNullable(compareItem.getOrderRateNewToBrand14d()).orElse(0.0)));
                                voBuilder.setCompareSalesRateNewToBrandFTD(DoubleValue.of(Optional.ofNullable(compareItem.getSalesRateNewToBrand14d()).orElse(0.0)));
                                voBuilder.setCompareOrdersNewToBrandPercentageFTD(DoubleValue.of(Optional.ofNullable(compareItem.getOrdersNewToBrandPercentage14d()).orElse(0.0)));

                                voBuilder.setCompareVideo5SecondViewRate(Optional.ofNullable(compareItem.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideo5SecondViews(Optional.ofNullable(compareItem.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoCompleteViews(Optional.ofNullable(compareItem.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoFirstQuartileViews(Optional.ofNullable(compareItem.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoMidpointViews(Optional.ofNullable(compareItem.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoThirdQuartileViews(Optional.ofNullable(compareItem.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoUnmutes(Optional.ofNullable(compareItem.getVideoUnmutes()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareViewImpressions(Optional.ofNullable(compareItem.getViewImpressions()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareViewabilityRate(Optional.ofNullable(compareItem.getViewabilityRate()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareViewClickThroughRate(Optional.ofNullable(compareItem.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                                //环比值,计算
                                voBuilder.setCompareCpaRate(voBuilder.getCompareCpa().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getCpa().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareCpa().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareCpa().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareCpcRate(voBuilder.getCompareCpc().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getCpc().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareCpc().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareCpc().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareRoasRate(voBuilder.getCompareRoas().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getRoas().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareRoas().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareRoas().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAsotsRate(voBuilder.getCompareAsots().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAsots().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAsots().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAsots().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAcotsRate(voBuilder.getCompareAcots().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAcots().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAcots().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAcots().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getImpressions().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareImpressions().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareImpressions().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareClicksRate(voBuilder.getCompareClicks().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getClicks().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareClicks().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareClicks().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getOrderNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareOrderNum().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareOrderNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSaleNumRate(voBuilder.getCompareSaleNum().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSaleNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSaleNum().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSaleNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareClickRateRate(voBuilder.getCompareClickRate().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getClickRate().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareClickRate().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareClickRate().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdClickRatioRate(voBuilder.getCompareAdClickRatio().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdClickRatio().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdClickRatio().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdClickRatio().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdConversionRateRate(voBuilder.getCompareAdConversionRate().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdConversionRate().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdConversionRate().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdConversionRate().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSalesRate(voBuilder.getCompareSales().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSales().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSales().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSales().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSalesConversionRateRate(voBuilder.getCompareSalesConversionRate().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSalesConversionRate().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSalesConversionRate().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSalesConversionRate().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareCostRate(voBuilder.getCompareCost().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getCost().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareCost().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareCost().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAcosRate(voBuilder.getCompareAcos().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAcos().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAcos().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAcos().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                                voBuilder.setCompareAdCostPercentageRate(new BigDecimal(voBuilder.getCompareAdCostPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        (new BigDecimal(voBuilder.getAdCostPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdCostPercentage())))
                                                .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdSalePercentageRate(new BigDecimal(voBuilder.getCompareAdSalePercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        (new BigDecimal(voBuilder.getAdSalePercentage()).subtract(new BigDecimal(voBuilder.getCompareAdSalePercentage())))
                                                .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSalePercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        (new BigDecimal(voBuilder.getAdOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage())))
                                                .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        (new BigDecimal(voBuilder.getOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareOrderNumPercentage())))
                                                .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNumPercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrdersNewToBrandFTDRate(voBuilder.getCompareOrdersNewToBrandFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getOrdersNewToBrandFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareOrdersNewToBrandFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareOrdersNewToBrandFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSalesNewToBrandFTDRate(voBuilder.getCompareSalesNewToBrandFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSalesNewToBrandFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSalesNewToBrandFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSalesNewToBrandFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareImpressionRankRate(voBuilder.getCompareImpressionRank().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getImpressionRank().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareImpressionRank().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareImpressionRank().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareImpressionShareRate(voBuilder.getCompareImpressionShare().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getImpressionShare().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareImpressionShare().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareImpressionShare().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrderRateNewToBrandFTDRate(voBuilder.getCompareOrderRateNewToBrandFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getOrderRateNewToBrandFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareOrderRateNewToBrandFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareOrderRateNewToBrandFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSalesRateNewToBrandFTDRate(voBuilder.getCompareSalesRateNewToBrandFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSalesRateNewToBrandFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSalesRateNewToBrandFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSalesRateNewToBrandFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrdersNewToBrandPercentageFTDRate(voBuilder.getCompareOrdersNewToBrandPercentageFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getOrdersNewToBrandPercentageFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareOrdersNewToBrandPercentageFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareOrdersNewToBrandPercentageFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareVideo5SecondViewRateRate(MathUtil.isNullOrZero(voBuilder.getCompareVideo5SecondViewRate()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideo5SecondViewRate(), voBuilder.getCompareVideo5SecondViewRate(), 4), 100)));
                                voBuilder.setCompareVideo5SecondViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideo5SecondViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideo5SecondViews(), voBuilder.getCompareVideo5SecondViews(), 4), 100)));
                                voBuilder.setCompareVideoCompleteViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoCompleteViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoCompleteViews(), voBuilder.getCompareVideoCompleteViews(), 4), 100)));
                                voBuilder.setCompareVideoFirstQuartileViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoFirstQuartileViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoFirstQuartileViews(), voBuilder.getCompareVideoFirstQuartileViews(), 4), 100)));
                                voBuilder.setCompareVideoMidpointViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoMidpointViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoMidpointViews(), voBuilder.getCompareVideoMidpointViews(), 4), 100)));
                                voBuilder.setCompareVideoThirdQuartileViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoThirdQuartileViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoThirdQuartileViews(), voBuilder.getCompareVideoThirdQuartileViews(), 4), 100)));
                                voBuilder.setCompareVideoUnmutesRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoUnmutes()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoUnmutes(), voBuilder.getCompareVideoUnmutes(), 4), 100)));
                                voBuilder.setCompareViewImpressionsRate(MathUtil.isNullOrZero(voBuilder.getCompareViewImpressions()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewImpressions(), voBuilder.getCompareViewImpressions(), 4), 100)));
                                voBuilder.setCompareViewabilityRateRate(MathUtil.isNullOrZero(voBuilder.getCompareViewabilityRate()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewabilityRate(), voBuilder.getCompareViewabilityRate(), 4), 100)));
                                voBuilder.setCompareViewClickThroughRateRate(MathUtil.isNullOrZero(voBuilder.getCompareViewClickThroughRate()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewClickThroughRate(), voBuilder.getCompareViewClickThroughRate(), 4), 100)));
                                voBuilder.setCompareAdvertisingUnitPriceRate(MathUtil.isNullOrZero(voBuilder.getCompareAdvertisingUnitPrice()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAdvertisingUnitPrice(), voBuilder.getCompareAdvertisingUnitPrice(), 4), 100)));

                            }
                        }


                        return voBuilder.build();
                    }
            ).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }

        return AllQueryWordDataResponse.AdQueryWordsHomeVo.newBuilder()
                .setPage(pageBuilder.build())
                .build();
    }

    private static List<com.meiyunji.sponsored.rpc.vo.AdStrategy> buildStrategyList(ReportVo item) {
        List<com.meiyunji.sponsored.rpc.vo.AdStrategy> strategyList = new ArrayList<>();
        for (AdStrategyVo strategyVo : item.getStrategyList()) {
            com.meiyunji.sponsored.rpc.vo.AdStrategy.Builder strategyBuilder = com.meiyunji.sponsored.rpc.vo.AdStrategy.newBuilder();
            strategyBuilder.setAdStrategyType(strategyVo.getAdStrategyType());
            strategyBuilder.setStatus(strategyVo.getStatus());
            strategyList.add(strategyBuilder.build());
        }
        return strategyList;
    }

    /**
     * 填充广告策略
     */
    public void fillAdStrategy(CpcQueryWordDto dto, List<ReportVo> rows){
        if(CollectionUtils.isEmpty(rows)){
            return ;
        }
        List<String> queryIds = rows.stream().map(ReportVo::getQueryId).distinct().collect(Collectors.toList());
        List<String> groupIds = rows.stream().map(ReportVo::getAdGroupId).distinct().collect(Collectors.toList());
        // 根据投放id集合获取自动化规则受控对象
        List<AdvertiseAutoRuleStatus> autoRuleStatuses = advertiseAutoRuleStatusDao.listByItemIdMutiple(dto.getPuid(), CollectionUtil.newArrayList(dto.getShopId()), AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName(), queryIds, AutoRuleChildrenItemTypeEnum.CHILDREN_SEARCH_QUERY.toString());
        Map<String, List<AdvertiseAutoRuleStatus>> autoRuleMap = StreamUtil.groupingBy(autoRuleStatuses, AdvertiseAutoRuleStatus::getItemId);
        List<AdvertiseAutoRuleStatus> autoRuleGroupStatuses = advertiseAutoRuleStatusDao.listByItemIdMutiple(dto.getPuid(), CollectionUtil.newArrayList(dto.getShopId()), AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName(), groupIds, AutoRuleChildrenItemTypeEnum.CHILDREN_SEARCH_QUERY_GROUP.toString());
        Map<String, List<AdvertiseAutoRuleStatus>> autoRuleGroupMap = StreamUtil.groupingBy(autoRuleGroupStatuses, AdvertiseAutoRuleStatus::getItemId);
        for (ReportVo vo : rows) {
            // 自动化规则标签
            List<AdStrategyVo> adstrategyList = new ArrayList<>();
            // key:标签 value:状态集合
            Map<String,List<String>> strategyMap = new HashMap<>();
            if(autoRuleMap.containsKey(vo.getQueryId())){
                // key 标签策略 status 状态集合  一个标签存在多种状态 如果存在已开启则为开启状态 否则为暂停状态
                Map<Integer, List<AdvertiseAutoRuleStatus>> autoRuleOperationMap = StreamUtil.groupingBy(autoRuleMap.get(vo.getQueryId()), AdvertiseAutoRuleStatus::getOperationType);
                for (Integer operationType : autoRuleOperationMap.keySet()) {
                    List<String> statusList = StreamUtil.toListDistinct(autoRuleOperationMap.get(operationType), AdvertiseAutoRuleStatus::getStatus);
                    String strategy = AdQueryStrategyTypeEnum.getStrategyMap().get(operationType);
                    if(StringUtil.isNotEmpty(strategy)){
                        List<String> statusAllList = strategyMap.getOrDefault(strategy, new ArrayList<>());
                        statusAllList.addAll(statusList);
                        strategyMap.put(strategy,statusAllList);
                    }
                }
            }
            if(autoRuleGroupMap.containsKey(vo.getAdGroupId())){
                // key 标签策略 status 状态集合  一个标签存在多种状态 如果存在已开启则为开启状态 否则为暂停状态
                Map<Integer, List<AdvertiseAutoRuleStatus>> autoRuleOperationMap = StreamUtil.groupingBy(autoRuleGroupMap.get(vo.getAdGroupId()), AdvertiseAutoRuleStatus::getOperationType);
                for (Integer operationType : autoRuleOperationMap.keySet()) {
                    List<String> statusList = StreamUtil.toListDistinct(autoRuleOperationMap.get(operationType), AdvertiseAutoRuleStatus::getStatus);
                    String strategy = AdQueryStrategyTypeEnum.getStrategyMap().get(operationType);
                    if(StringUtil.isNotEmpty(strategy)){
                        List<String> statusAllList = strategyMap.getOrDefault(strategy, new ArrayList<>());
                        statusAllList.addAll(statusList);
                        strategyMap.put(strategy,statusAllList);
                    }
                }
            }
            // 自动化规则标签
            for (String strategy : strategyMap.keySet()) {
                int status = 0;
                List<String> statusList = strategyMap.get(strategy);
                if(statusList.contains("ENABLED")){
                    status = 1;
                }
                AdStrategyVo strategyVo = new AdStrategyVo();
                strategyVo.setAdStrategyType(strategy);
                strategyVo.setStatus(status);
                adstrategyList.add(strategyVo);
            }
            if(SbMatchValueEnum.THEME.getMatchType().equalsIgnoreCase(vo.getMatchType())){
                vo.setStrategyList(new ArrayList<>());
            }else{
                vo.setStrategyList(adstrategyList);
            }
        }
    }

    @Override
    public Page getDorisAdQueryWordsPageVo(Integer puid, CpcQueryWordDto dto, Page page) {
        long allKeywordTime = System.currentTimeMillis();
        // 参数赋值
        if (setParam(puid, dto)) return page;

        if (dto.getOnlyCount() != null && dto.getOnlyCount()) {
            int size = odsCpcSbQueryKeywordReportDao.countAllList(puid, dto);
            return new Page(page.getPageNo(), page.getPageSize(), 0 , size, Lists.newArrayList());
        }

        AdMetricDto adMetricDto = odsCpcSbQueryKeywordReportDao.getSumAdMetricDto(puid, dto);
        page = odsCpcSbQueryKeywordReportDao.pageList(puid, dto, page);
        log.info("==============================查询所有搜索词关键词花费时间 分页 {} ==============================", System.currentTimeMillis() - allKeywordTime);

        List<CpcSbQueryKeywordReport> poList = page.getRows();

        List<String> campaignIds = poList.stream().filter(Objects::nonNull).map(CpcSbQueryKeywordReport::getCampaignId).distinct().collect(Collectors.toList());
        List<String> groupIds = poList.stream().filter(Objects::nonNull).map(CpcSbQueryKeywordReport::getAdGroupId).distinct().collect(Collectors.toList());
        List<String> keywordIds = poList.stream().filter(Objects::nonNull).map(CpcSbQueryKeywordReport::getKeywordId).distinct().collect(Collectors.toList());
        Map<String, AmazonAdPortfolio> portfolioMap = null;
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            List<String> portfolioIds = amazonAdSbCampaignDao.getPortfolioListByCampaignIds(puid, dto.getShopId(), campaignIds);
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                portfolioMap = portfolioDao.getPortfolioList(puid, dto.getShopId(), portfolioIds).stream()
                        .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
            }
        }

        //批量查询广告活动和广告组

        long allKeywordTime2 = System.currentTimeMillis();
        List<AmazonAdCampaignAll> byCampaignIds = null;
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            byCampaignIds = amazonAdSbCampaignDao.getByCampaignIds(puid, dto.getShopId(), campaignIds);
        }

        Map<String, AmazonAdCampaignAll> campaignMap = null;
        if (CollectionUtils.isNotEmpty(byCampaignIds)) {
            campaignMap = byCampaignIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item -> item, (a, b) -> a));
        }

        Map<String, AmazonSbAdGroup> groupMap = null;
        List<AmazonSbAdGroup> adGroupByIds = null;
        if (CollectionUtils.isNotEmpty(groupIds)) {
            adGroupByIds = amazonSbAdGroupDao.getAdGroupByIds(puid, dto.getShopId(), groupIds);
        }

        if (CollectionUtils.isNotEmpty(adGroupByIds)) {
            groupMap = adGroupByIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, item -> item, (a, b) -> a));
        }
        Map<String, AmazonSbAdKeyword> keywordMap = null;
        if (CollectionUtils.isNotEmpty(keywordIds)) {
            keywordMap = amazonSbAdKeywordDao.listByKeywordId(puid, dto.getShopId(), keywordIds).stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdKeyword::getKeywordId, item -> item, (a, b) -> a));
        }
        log.info("==============================查询所有搜索词关键词花费时间 批量查询数据 {} ==============================", System.currentTimeMillis() - allKeywordTime2);

        if (CollectionUtils.isNotEmpty(poList)) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            Map<String, AmazonAdCampaignAll> finalCampaignMap = campaignMap;
            Map<String, AmazonSbAdGroup> finalGroupMap = groupMap;
            Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;
            Map<String, AmazonSbAdKeyword> finalKeywordMap = keywordMap;
            List<String> groupIdList = poList.stream().filter(Objects::nonNull).map(CpcSbQueryKeywordReport::getAdGroupId)
                    .distinct().collect(Collectors.toList());

            List<OdsAmazonAdKeywordSb> keywordList = null;
            List<OdsAmazonAdNeKeywordSb> neKeywordList = null;
            if (CollectionUtils.isNotEmpty(groupIdList)) {
                keywordList = odsAmazonAdKeywordSbDao.listByGroupIdList(puid, dto.getShopId(), groupIdList);
                neKeywordList = odsAmazonAdNeKeywordSbDao.listByGroupIdList(puid, dto.getShopId(), groupIdList);
            }


            Map<String, List<OdsAmazonAdKeywordSb>> groupKeywordMapList = new HashMap<>();
            if (CollectionUtils.isNotEmpty(keywordList)) {
                for (OdsAmazonAdKeywordSb keyword : keywordList) {
                    if (StringUtils.isBlank(keyword.getAdGroupId())) {
                        continue;
                    }
                    if (groupKeywordMapList.containsKey(keyword.getAdGroupId())) {
                        groupKeywordMapList.get(keyword.getAdGroupId()).add(keyword);
                    } else {
                        groupKeywordMapList.put(keyword.getAdGroupId(), Lists.newArrayList(keyword));
                    }
                }
            }

            Map<String, List<OdsAmazonAdNeKeywordSb>> groupNeKeywordMapList = new HashMap<>();
            if (CollectionUtils.isNotEmpty(neKeywordList)) {
                for (OdsAmazonAdNeKeywordSb keyword : neKeywordList) {
                    if (StringUtils.isBlank(keyword.getAdGroupId())) {
                        continue;
                    }
                    if (groupNeKeywordMapList.containsKey(keyword.getAdGroupId())) {
                        groupNeKeywordMapList.get(keyword.getAdGroupId()).add(keyword);
                    } else {
                        groupNeKeywordMapList.put(keyword.getAdGroupId(), Lists.newArrayList(keyword));
                    }
                }
            }

            BigDecimal sumRange = dto.getShopSales();

            Map<String, OdsWeekSearchTermsAnalysis> searchTermsAnalysisMap = new HashMap<>();
            if (!dto.isQueryJoinSearchTermsRank()) {
                List<String> searchTerms = poList.stream().map(CpcSbQueryKeywordReport::getQuery).distinct().collect(Collectors.toList());
                List<OdsWeekSearchTermsAnalysis> searchTermsAnalyses = weekSearchTermsAnalysisService.queryRanks(searchTerms, dto.getMarketplaceId());
                searchTermsAnalysisMap.putAll(searchTermsAnalyses.stream().collect(Collectors.toMap(OdsWeekSearchTermsAnalysis::getSearchTerm, Function.identity(), (o,n)->n)));
            }
            //获取翻译词
            List<WordTranslateQo> wordTranslateQos = poList.stream().map(e -> new WordTranslateQo(e.getMarketplaceId(), e.getQuery())).collect(Collectors.toList());
            Map<String, String> wordTranslateMap = wordTranslateService.getWordTranslateMap(puid, wordTranslateQos, false);

            AdMetricDto finalAdMetricDto = adMetricDto;
            poList.stream().filter(Objects::nonNull).forEach(e -> {
                ReportVo vo = new ReportVo();
                vo.setQueryId(e.getQueryId());
                vo.setMarketplaceId(e.getMarketplaceId());
                vo.setCost(e.getCost());
                vo.setSales(e.getSales14d());
                vo.setImpressions(e.getImpressions());
                vo.setClicks(e.getClicks());
                vo.setOrderNum(e.getConversions14d());
                vo.setSaleNum(e.getConversions14d());
                vo.setAdFormat(e.getAdFormat());
                vo.setType(Constants.SB);
                Double clickRate = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) * 100, Double.valueOf(vo.getImpressions()), 2);
                vo.setClickRate(clickRate);
                Double salesConversionRate = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getSaleNum()) * 100, Double.valueOf(vo.getClicks()), 2);
                vo.setSalesConversionRate(salesConversionRate);
                BigDecimal rate = BigDecimal.valueOf(vo.getClicks()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(vo.getClicks()));
                BigDecimal cpc = rate.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setCpc(cpc);


                BigDecimal rate2 = e.getSales14d().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), e.getSales14d());
                rate2 = MathUtil.multiply(rate2, BigDecimal.valueOf(100));
                BigDecimal acos = rate2.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setAcos(acos);

                BigDecimal rate3 = BigDecimal.valueOf(e.getConversions14d()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(e.getConversions14d()));
                BigDecimal cpa = rate3.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setCpa(cpa);

                if (e.getSales14d() != null && e.getCost() != null) {
                    vo.setRoas(e.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getSales14d().divide(e.getCost(), 2, RoundingMode.HALF_UP));
                }
                //新加指标,需要获取广告
                BigDecimal shopTotalSales = Optional.ofNullable(sumRange).orElse(BigDecimal.ZERO);
                vo.setAcots(shopTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getCost().multiply(BigDecimal.valueOf(100)).divide(sumRange, 2, RoundingMode.HALF_UP));
                vo.setAsots(shopTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getSales14d().multiply(BigDecimal.valueOf(100)).divide(sumRange, 2, RoundingMode.HALF_UP));
                vo.setKeywordText(e.getKeywordText());
                vo.setMatchType(e.getMatchType());
                vo.setKeywordId(e.getKeywordId());
                vo.setTargetingType(Constants.MANUAL); //关键词应该只有手动
                vo.setIsTargetType(false);
                vo.setQuery(e.getQuery());
                vo.setQueryCn(wordTranslateMap.get(wordTranslateService.getWordTranslateKey(dto.getMarketplaceId(), e.getQuery())));

                this.filterMetricData(e, vo, finalAdMetricDto);

                //检查该搜索词是否添加过
                if (groupKeywordMapList.size() > 0 && groupKeywordMapList.containsKey(e.getAdGroupId()) && StringUtils.isNotBlank(e.getQuery())) {
                    List<OdsAmazonAdKeywordSb> amazonAdKeywords = groupKeywordMapList.get(e.getAdGroupId());
                    if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                        for (OdsAmazonAdKeywordSb keyword : amazonAdKeywords) {
                            if (StringUtils.isNotBlank(e.getQuery()) && StringUtils.isNotBlank(keyword.getKeywordText())) {
                                if (e.getQuery().trim().equalsIgnoreCase(keyword.getKeywordText().trim())) {
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.EXACT)) {
                                        vo.setIsExact(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.BROAD)) {
                                        vo.setIsBroad(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.PHRASE)) {
                                        vo.setIsPhrase(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.NEGATIVEEXACT)) {
                                        vo.setIsNegativeExact(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.NEGATIVEPHRASE)) {
                                        vo.setIsNegativePhrase(true);
                                    }
                                }
                            }
                        }
                    }
                }

                //检查该搜索词是否添加过否定词
                if (groupNeKeywordMapList.size() > 0 && groupNeKeywordMapList.containsKey(e.getAdGroupId()) && StringUtils.isNotBlank(e.getQuery())) {
                    List<OdsAmazonAdNeKeywordSb> amazonAdKeywords = groupNeKeywordMapList.get(e.getAdGroupId());
                    if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                        for (OdsAmazonAdNeKeywordSb keyword : amazonAdKeywords) {
                            if (StringUtils.isNotBlank(e.getQuery()) && StringUtils.isNotBlank(keyword.getKeywordText())) {
                                if (e.getQuery().trim().equalsIgnoreCase(keyword.getKeywordText().trim())) {
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.EXACT)) {
                                        vo.setIsExact(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.BROAD)) {
                                        vo.setIsBroad(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.PHRASE)) {
                                        vo.setIsPhrase(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.NEGATIVEEXACT)) {
                                        vo.setIsNegativeExact(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.NEGATIVEPHRASE)) {
                                        vo.setIsNegativePhrase(true);
                                    }
                                }
                            }
                        }
                    }
                }

                vo.setCampaignId(e.getCampaignId());
                //广告活动名称使用最新的
                if (MapUtils.isNotEmpty(finalCampaignMap) && finalCampaignMap.containsKey(e.getCampaignId())) {
                    AmazonAdCampaignAll campaign = finalCampaignMap.get(vo.getCampaignId());
                    vo.setCampaignName(campaign.getName());
                    vo.setCampaignStatus(campaign.getState());
                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        AmazonAdPortfolio adPortfolio = portfolioDao.getByPortfolioId(puid, campaign.getShopId(), campaign.getPortfolioId());
                        vo.setPortfolioId(campaign.getPortfolioId());
                        if (finalPortfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = finalPortfolioMap.get(campaign.getPortfolioId());
                            vo.setPortfolioName(amazonAdPortfolio.getName());
                            vo.setIsHidden(adPortfolio.getIsHidden());
                        } else {
                            vo.setPortfolioName("广告组合待同步");
                        }
                    } else {
                        vo.setPortfolioName("-");
                    }
                } else {
                    vo.setCampaignName(e.getCampaignName());
                }
                vo.setAdGroupId(e.getAdGroupId());
                //广告组名称使用最新的
                if (MapUtils.isNotEmpty(finalGroupMap) && finalGroupMap.containsKey(e.getAdGroupId())) {
                    vo.setAdGroupName(finalGroupMap.get(e.getAdGroupId()).getName());
                    vo.setAdGroupType(finalGroupMap.get(e.getAdGroupId()).getAdGroupType());
                    vo.setAdFormat(finalGroupMap.get(e.getAdGroupId()).getAdFormat());
                    vo.setAdGroupState(finalGroupMap.get(e.getAdGroupId()).getState());
                }
                if (MapUtils.isNotEmpty(finalKeywordMap) && finalKeywordMap.containsKey(e.getKeywordId())) {
                    vo.setTargetState(finalKeywordMap.get(e.getKeywordId()).getState());
                }
                //“品牌新买家”订单量
                vo.setOrdersNewToBrand14d(Optional.ofNullable(e.getOrdersNewToBrand14d()).orElse(0));
                //“品牌新买家”销售额
                vo.setSalesNewToBrand14d(Optional.ofNullable(e.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO));
                //搜索词展示量排名
                vo.setImpressionRank(Optional.ofNullable(e.getSearchTermImpressionRank()).orElse(0));
                //搜索词展示份额
                vo.setImpressionShare(Optional.ofNullable(e.getSearchTermImpressionShare()).orElse(0.00));
                //“品牌新买家”订单百分比
                BigDecimal orderRateNewToBrand14d = e.getConversions14d() == 0 ? BigDecimal.ZERO : new BigDecimal(Optional.ofNullable(e.getOrdersNewToBrand14d()).orElse(0)).multiply(new BigDecimal("100")).divide(new BigDecimal(e.getConversions14d()), 2, BigDecimal.ROUND_HALF_UP);
                vo.setOrderRateNewToBrand14d(orderRateNewToBrand14d.doubleValue());
                //“品牌新买家”销售额百分比
                BigDecimal salesRateNewToBrand14d = e.getSales14d().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : Optional.ofNullable(e.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO).multiply(new BigDecimal("100")).divide(e.getSales14d(), 2, BigDecimal.ROUND_HALF_UP);
                vo.setSalesRateNewToBrand14d(salesRateNewToBrand14d.doubleValue());
                //“品牌新买家”订单转化率
                BigDecimal ordersNewToBrandPercentage14d = e.getClicks() == 0 ? BigDecimal.ZERO : new BigDecimal(Optional.ofNullable(e.getOrdersNewToBrand14d()).orElse(0)).multiply(new BigDecimal("100")).divide(new BigDecimal(e.getClicks()), 2, BigDecimal.ROUND_HALF_UP);
                vo.setOrdersNewToBrandPercentage14d(ordersNewToBrandPercentage14d.doubleValue());

                vo.setVideo5SecondViews(Optional.ofNullable(e.getVideo5SecondViews()).orElse(0));
                vo.setVideo5SecondViewRate(vo.getImpressions() == 0 ? 0.00 :
                        MathUtil.divideOfObject(MathUtil.multiplyOfObject(vo.getVideo5SecondViews(), 100), vo.getImpressions()).doubleValue());
                vo.setVideoCompleteViews(Optional.ofNullable(e.getVideoCompleteViews()).orElse(0));
                vo.setVideoFirstQuartileViews(Optional.ofNullable(e.getVideoFirstQuartileViews()).orElse(0));
                vo.setVideoMidpointViews(Optional.ofNullable(e.getVideoMidpointViews()).orElse(0));
                vo.setVideoThirdQuartileViews(Optional.ofNullable(e.getVideoThirdQuartileViews()).orElse(0));
                vo.setVideoUnmutes(Optional.ofNullable(e.getVideoUnmutes()).orElse(0));
                vo.setViewImpressions(Optional.ofNullable(e.getViewableImpressions()).orElse(0));
                vo.setViewabilityRate(vo.getImpressions() == 0 ? BigDecimal.ZERO :
                        MathUtil.divideOfObject(MathUtil.multiplyOfObject(vo.getViewImpressions(), 100), vo.getImpressions()));
                vo.setViewClickThroughRate(vo.getViewImpressions() == 0 ? BigDecimal.ZERO :
                        MathUtil.divideOfObject(MathUtil.multiplyOfObject(vo.getClicks(), 100), vo.getViewImpressions()));
                if (vo.getSales() != null && vo.getOrderNum() != null && vo.getOrderNum() != 0) {
                    vo.setAdvertisingUnitPrice(MathUtil.divideOfObject(vo.getSales(), vo.getOrderNum()));
                } else {
                    vo.setAdvertisingUnitPrice(BigDecimal.ZERO);
                }
                // 兼容特殊排序逻辑
                // 联表时字段aba排名与周变化率肯定不为空
                if (dto.isQueryJoinSearchTermsRank()) {
                    vo.setSearchFrequencyRank(e.getSearchFrequencyRank() == Integer.MAX_VALUE ? 0 : e.getSearchFrequencyRank());
                    if (vo.getSearchFrequencyRank() > 0) {
                        BigDecimal weekRatio = e.getWeekRatio().intValue() == Integer.MIN_VALUE ? BigDecimal.ZERO : e.getWeekRatio();
                        vo.setWeekRatio(weekRatio.setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                } else {
                    OdsWeekSearchTermsAnalysis searchTermsAnalysis = searchTermsAnalysisMap.get(vo.getQuery().toLowerCase());
                    if (searchTermsAnalysis != null) {
                        vo.setSearchFrequencyRank(searchTermsAnalysis.getSearchFrequencyRank());
                        vo.setWeekRatio(Optional.ofNullable(searchTermsAnalysis.getWeekRatio()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                }

                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }

    private boolean setParam(Integer puid, CpcQueryWordDto dto) {
        //todo:干掉不合理参数,避免前端误传
        dto.setCampaignIds(null);
        //分页数据
        if (StringUtils.isNotBlank(dto.getPortfolioId()) || StringUtils.isNotBlank(dto.getState()) || StringUtils.isNotBlank(dto.getServingStatus())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(puid, dto.getShopId(), dto.getPortfolioId(), dto.getState(), dto.getServingStatus(), CampaignTypeEnum.sb.getCampaignType());
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                dto.setCampaignIdList(campaignIds);
            } else {
                return true;
            }
        }
        //若只选择了标签asin，则直接返回空列表
        if (CollectionUtils.isNotEmpty(dto.getQueryWordTagTypeList())) {
            Set<String> targetTagTypeSet = new HashSet<>(Lists.newArrayList(CpcQueryWordDto.QueryWordTagTypeEnum.TARGET_ASIN.getValue(), CpcQueryWordDto.QueryWordTagTypeEnum.NE_TARGET_ASIN.getValue()));
            if (targetTagTypeSet.containsAll(dto.getQueryWordTagTypeList())) {
                return true;
            }
        }
        // 自动化规则筛选投放id集合、组id集合
        List<Integer> operationTypeList = AdQueryStrategyTypeEnum.operationTypeList(dto.getAdStrategyTypeList());
        if (CollectionUtils.isNotEmpty(operationTypeList)) {
            String queryType = AmazonAd.AdQueryTypeEnum.SB_QUERY.getSearchField();
            List<String> targetIdList = advertiseAutoRuleStatusDao.listItemIdByAdManage(dto.getPuid(), cn.hutool.core.collection.CollectionUtil.newArrayList(dto.getShopId()), AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName(),
                    operationTypeList, dto.getType(), AutoRuleChildrenItemTypeEnum.CHILDREN_SEARCH_QUERY.toString(), null, queryType);
            dto.setAutoRuleIds(targetIdList);
            List<String> groupIdList = advertiseAutoRuleStatusDao.listItemIdByAdManage(dto.getPuid(), CollectionUtil.newArrayList(dto.getShopId()), AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName(),
                    operationTypeList, dto.getType(), AutoRuleChildrenItemTypeEnum.CHILDREN_SEARCH_QUERY_GROUP.toString(), null, null);
            dto.setAutoRuleGroupIds(groupIdList);
            if (CollectionUtils.isEmpty(targetIdList) && CollectionUtils.isEmpty(groupIdList) &&
                    !dto.getAdStrategyTypeList().contains(AdQueryStrategyTypeEnum.NONE.getCode())) {
                // 只存在自动化规则筛选没数据时返回
                return true;
            }
        }
        return false;
    }

    @Override
    public AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo getDorisAllQueryWordAggregateData(Integer puid, CpcQueryWordDto dto) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(dto.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal shopSaleData;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {  //店铺销售额
            shopSaleData = BigDecimal.ZERO;
        } else {
            shopSaleData = shopSaleDto.getSumRange();
        }
        dto.setShopSales(shopSaleData);
        String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();
        //按条件查询所有数据
        List<AdHomePerformancedto> listCompare = new ArrayList<>();
        List<AdHomePerformancedto> reportDayList;  //每日汇总数据

        boolean isNull = setParam(puid, dto);  // 查询的数据为空
        if (isNull) {  // 查询数据置为空
            reportDayList = new ArrayList<>();
        } else {
            String date = weekSearchTermsAnalysisService.getLatestDate(dto.getMarketplaceId());
            dto.setLastWeekSearchTermsRankDate(date);
            //获取投放维度列表
            reportDayList = odsCpcSbQueryKeywordReportDao.getQueryKeywordReportAggregate(puid, dto, true, false);
            if (dto.getIsCompare()) {
                listCompare = odsCpcSbQueryKeywordReportDao.getQueryKeywordReportAggregate(puid, dto, false, true);
            }
        }
        //环比数据
        BigDecimal shopSaleDataCompare = BigDecimal.ZERO;
        if (dto.getIsCompare()) {
            ShopSaleDto shopSaleDtoCompare = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getCompareStartDate(), dto.getCompareEndDate());

            if (shopSaleDtoCompare == null || shopSaleDtoCompare.getSumRange() == null) {  //店铺销售额
                shopSaleDataCompare = BigDecimal.ZERO;
            } else {
                shopSaleDataCompare = shopSaleDtoCompare.getSumRange();
            }
        }
        //汇总指标数据
        AdHomeAggregateDataRpcVo aggregateDataVo = getQueryKeywordAggregateDataVo(reportDayList, listCompare, shopSaleData, shopSaleDataCompare, isVc);
        //处理chart数据
        List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, dto.getStart(), dto.getEnd(), reportDayList, shopSaleData);
        List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, shopSaleData);
        List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, shopSaleData);
        return AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo.newBuilder()
                .setAggregateDataVo(aggregateDataVo)
                .addAllDay(dayPerformanceVos)
                .addAllMonth(monthPerformanceVos)
                .addAllWeek(weekPerformanceVos)
                .build();
    }

    /**
     * 汇总数据组装
     *
     * @param rows
     * @return
     */
    private AdHomeAggregateDataRpcVo getQueryKeywordAggregateDataVo(List<AdHomePerformancedto> rows, List<AdHomePerformancedto> rowsCompare, BigDecimal shopSales, BigDecimal shopSalesCompare, boolean isVc) {

        //点击量
        int sumClicks = rows.stream().filter(item -> item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNum = rows.stream().filter(item -> item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressions = rows.stream().filter(item -> item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSale = rows.stream().filter(item -> item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcost = rows.stream().filter(item -> item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);

        //ACoS
        BigDecimal sumAcos = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(sumAdSale, 2, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClick = sumClicks == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCVr = sumClicks == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNum).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtr = sumImpressions == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicks).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressions), 2, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roas = sumAdcost.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSale.divide(sumAdcost, 2, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(shopSales, 2, BigDecimal.ROUND_HALF_UP);//asots
        BigDecimal asots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSale.multiply(new BigDecimal("100")).divide(shopSales, 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal cpa = sumAdcost.compareTo(BigDecimal.ZERO) == 0 || BigDecimal.valueOf(sumAdOrderNum).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(sumAdcost, BigDecimal.valueOf(sumAdOrderNum));

        //“品牌新买家”订单量
        int sumOrdersNewToBrand14d = rows.stream().filter(item -> item.getOrdersNewToBrand14d() != null).mapToInt(AdHomePerformancedto::getOrdersNewToBrand14d).sum();
        //“品牌新买家”销售额
        BigDecimal sumSalesNewToBrand14d = rows.stream().filter(item -> item.getSalesNewToBrand14d() != null).map(e -> e.getSalesNewToBrand14d()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //搜索词展示量排名
        int sumImpressionRank = rows.stream().filter(item -> item.getImpressionRank() != null).mapToInt(AdHomePerformancedto::getImpressionRank).sum();
        //搜索词展示份额
        Double sumImpressionShare = rows.stream().filter(item -> item.getImpressionShare() != null).mapToDouble(AdHomePerformancedto::getImpressionShare).sum();
        //每笔订单花费
        BigDecimal sumCpa = sumAdOrderNum == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumAdOrderNum), 2, BigDecimal.ROUND_HALF_UP);
        //“品牌新买家”订单百分比
        BigDecimal sumOrderRateNewToBrand14d = sumAdOrderNum == 0 ? BigDecimal.ZERO : new BigDecimal(sumOrdersNewToBrand14d).multiply(new BigDecimal("100")).divide(new BigDecimal(sumAdOrderNum), 2, BigDecimal.ROUND_HALF_UP);
        //“品牌新买家”销售额百分比
        BigDecimal sumSalesRateNewToBrand14d = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumSalesNewToBrand14d.multiply(new BigDecimal("100")).divide(sumAdSale, 2, BigDecimal.ROUND_HALF_UP);
        //“品牌新买家”订单转化率
        BigDecimal sumOrdersNewToBrandPercentage14d = sumClicks == 0 ? BigDecimal.ZERO : new BigDecimal(sumOrdersNewToBrand14d).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        String sumAdCostPercentage = sumAdcost.compareTo(BigDecimal.ZERO) == 0 ? "0" : "100.0000";
        String sumAdSalePercentage = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? "0" : "100.0000";
        String sumAdOrderNumPercentage = sumAdOrderNum == 0 ? "0" : "100.0000";

        // 5秒观看次数
        int sumVideo5SecondViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideo5SecondViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 5秒观看率
        BigDecimal sumVideo5SecondViewRate = sumImpressions == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumVideo5SecondViews, 100), sumImpressions, 2);
        // 视频播至1/4次数
        int sumVideoFirstQuartileViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoFirstQuartileViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频播至1/2次数
        int sumVideoMidpointViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoMidpointViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频播至3/4次数
        int sumVideoThirdQuartileViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoThirdQuartileViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频完整播放次数
        int sumVideoCompleteViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoCompleteViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频取消静音
        int sumVideoUnmutes = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoUnmutes).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 可见展示次数
        int sumViewImpressions = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getViewableImpressions).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 观看率
        BigDecimal sumViewabilityRate = sumImpressions == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumViewImpressions, 100), sumImpressions, 2);
        // 观看点击率
        BigDecimal sumViewClickThroughRate = sumViewImpressions == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumClicks, 100), sumViewImpressions, 2);
        // 广告笔单价,广告销售额/广告订单量*100%
        BigDecimal sumAdvertisingUnitPrice = sumAdOrderNum == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdSale, sumAdOrderNum, 2);

        //环比数据
        //点击量
        int sumClicksCompare = rowsCompare.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNumCompare = rowsCompare.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressionsCompare = rowsCompare.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSaleCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcostCompare = rowsCompare.stream().filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);

        //ACoS
        BigDecimal sumAcosCompare = sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcostCompare.multiply(new BigDecimal("100")).divide(sumAdSaleCompare, 2, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClickCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : sumAdcostCompare.divide(new BigDecimal(sumClicksCompare), 2, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCvrCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNumCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicksCompare), 2, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtrCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicksCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressionsCompare), 2, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roasCompare = sumAdcostCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSaleCompare.divide(sumAdcostCompare, 2, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acotsCompare = shopSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcostCompare.multiply(new BigDecimal("100")).divide(shopSalesCompare, 2, BigDecimal.ROUND_HALF_UP);
        //asots
        BigDecimal asotsCompare = shopSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSaleCompare.multiply(new BigDecimal("100")).divide(shopSalesCompare, 2, BigDecimal.ROUND_HALF_UP);

        // 5秒观看次数
        int sumVideo5SecondViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideo5SecondViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 5秒观看率
        BigDecimal sumVideo5SecondViewRateCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumVideo5SecondViewsCompare, 100), sumImpressionsCompare, 2);
        // 视频播至1/4次数
        int sumVideoFirstQuartileViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoFirstQuartileViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频播至1/2次数
        int sumVideoMidpointViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoMidpointViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频播至3/4次数
        int sumVideoThirdQuartileViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoThirdQuartileViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频完整播放次数
        int sumVideoCompleteViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoCompleteViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频取消静音
        int sumVideoUnmutesCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoUnmutes).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 可见展示次数
        int sumViewImpressionsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getViewableImpressions).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 观看率
        BigDecimal sumViewabilityRateCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumViewImpressionsCompare, 100), sumImpressionsCompare, 2);
        // 观看点击率
        BigDecimal sumViewClickThroughRateCompare = sumViewImpressionsCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumClicksCompare, 100), sumViewImpressionsCompare, 2);
        // 广告笔单价,广告销售额/广告订单量*100%
        BigDecimal sumAdvertisingUnitPriceCompare = sumAdOrderNumCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdSaleCompare, sumAdOrderNumCompare, 2);
        //每笔订单花费
        BigDecimal sumCpaCompare = sumAdOrderNumCompare == 0 ? BigDecimal.ZERO : sumAdcostCompare.divide(new BigDecimal(sumAdOrderNumCompare), 2, RoundingMode.HALF_UP);
        //“品牌新买家”订单量
        int sumOrdersNewToBrand14dCompare = rowsCompare.stream().filter(item -> item != null && item.getOrdersNewToBrand14d() != null).mapToInt(AdHomePerformancedto::getOrdersNewToBrand14d).sum();
        //“品牌新买家”销售额
        BigDecimal sumSalesNewToBrand14dCompare = rowsCompare.stream().filter(item -> item != null && item.getSalesNewToBrand14d() != null).map(AdHomePerformancedto::getSalesNewToBrand14d).reduce(BigDecimal.ZERO, BigDecimal::add);
        //“品牌新买家”订单百分比
        BigDecimal sumOrderRateNewToBrand14dCompare = sumAdOrderNumCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumOrdersNewToBrand14dCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumAdOrderNumCompare), 2 , RoundingMode.HALF_UP);
        //“品牌新买家”销售额百分比
        BigDecimal sumSalesRateNewToBrand14dCompare = sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumSalesNewToBrand14dCompare.multiply(new BigDecimal("100")).divide(sumAdSaleCompare, 2, RoundingMode.HALF_UP);
        //“品牌新买家”订单转化率
        BigDecimal sumOrdersNewToBrandPercentage14dCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumOrdersNewToBrand14dCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicksCompare), 2, RoundingMode.HALF_UP);

        AdHomeAggregateDataRpcVo.Builder builder = AdHomeAggregateDataRpcVo.newBuilder()
                .setAcos(sumAcos.stripTrailingZeros().toPlainString())
                .setAcots(acots.stripTrailingZeros().toPlainString())
                .setAsots(asots.stripTrailingZeros().toPlainString())
                .setRoas(roas.stripTrailingZeros().toPlainString())
                .setAdCost(sumAdcost.stripTrailingZeros().toPlainString())
                .setAdCostPerClick(sumAdCostPerClick.stripTrailingZeros().toPlainString())
                .setAdOrderNum(Int32Value.of(sumAdOrderNum))
                .setCvr(sumCVr.stripTrailingZeros().toPlainString())
                .setCtr(sumCtr.stripTrailingZeros().toPlainString())
                .setAdSale(sumAdSale.stripTrailingZeros().toPlainString())
                .setClicks(Int32Value.of(sumClicks))
                .setImpressions(Int32Value.of(sumImpressions))
                .setCpa(cpa.stripTrailingZeros().toPlainString())
                .setOrdersNewToBrandFTD(Int32Value.of(sumOrdersNewToBrand14d))
                .setSalesNewToBrandFTD(sumSalesNewToBrand14d.stripTrailingZeros().toPlainString())
                .setImpressionRank(Int32Value.of(sumImpressionRank))
                .setImpressionShare(BigDecimal.valueOf(sumImpressionShare).stripTrailingZeros().toPlainString())
                .setCpa(sumCpa.stripTrailingZeros().toPlainString())
                .setOrderRateNewToBrandFTD(sumOrderRateNewToBrand14d.stripTrailingZeros().toPlainString())
                .setSalesRateNewToBrandFTD(sumSalesRateNewToBrand14d.stripTrailingZeros().toPlainString())
                .setOrdersNewToBrandPercentageFTD(sumOrdersNewToBrandPercentage14d.stripTrailingZeros().toPlainString())
                .setAdCostPercentage(sumAdCostPercentage)
                .setAdSalePercentage(sumAdSalePercentage)
                .setAdOrderNumPercentage(sumAdOrderNumPercentage)
                .setVideo5SecondViews(String.valueOf(sumVideo5SecondViews))
                .setVideo5SecondViewRate(String.valueOf(sumVideo5SecondViewRate))
                .setVideoFirstQuartileViews(String.valueOf(sumVideoFirstQuartileViews))
                .setVideoMidpointViews(String.valueOf(sumVideoMidpointViews))
                .setVideoThirdQuartileViews(String.valueOf(sumVideoThirdQuartileViews))
                .setVideoCompleteViews(String.valueOf(sumVideoCompleteViews))
                .setVideoUnmutes(String.valueOf(sumVideoUnmutes))
                .setViewImpressions(Int32Value.of(sumViewImpressions))
                .setViewabilityRate(String.valueOf(sumViewabilityRate))
                .setViewClickThroughRate(String.valueOf(sumViewClickThroughRate))
                .setAdvertisingUnitPrice(String.valueOf(sumAdvertisingUnitPrice))

                //环比数据
                .setCompareAcos(sumAcosCompare.toPlainString())
                .setCompareRoas(roasCompare.toPlainString())
                .setCompareAdCost(sumAdcostCompare.toPlainString())
                .setCompareAdCostPerClick(sumAdCostPerClickCompare.toPlainString())
                .setCompareAdOrderNum(Int32Value.of(sumAdOrderNumCompare))
                .setCompareCvr(sumCvrCompare.toPlainString())
                .setCompareCtr(sumCtrCompare.toPlainString())
                .setCompareAdSale(sumAdSaleCompare.toPlainString())
                .setCompareClicks(Int32Value.of(sumClicksCompare))
                .setCompareImpressions(Int32Value.of(sumImpressionsCompare))
                .setCompareAcots(acotsCompare.toPlainString())
                .setCompareAsots(asotsCompare.toPlainString())
                .setCompareVideo5SecondViews(String.valueOf(sumVideo5SecondViewsCompare))
                .setCompareVideo5SecondViewRate(String.valueOf(sumVideo5SecondViewRateCompare))
                .setCompareVideoFirstQuartileViews(String.valueOf(sumVideoFirstQuartileViewsCompare))
                .setCompareVideoMidpointViews(String.valueOf(sumVideoMidpointViewsCompare))
                .setCompareVideoThirdQuartileViews(String.valueOf(sumVideoThirdQuartileViewsCompare))
                .setCompareVideoCompleteViews(String.valueOf(sumVideoCompleteViewsCompare))
                .setCompareVideoUnmutes(String.valueOf(sumVideoUnmutesCompare))
                .setCompareViewImpressions(Int32Value.of(sumViewImpressionsCompare))
                .setCompareViewabilityRate(String.valueOf(sumViewabilityRateCompare))
                .setCompareViewClickThroughRate(String.valueOf(sumViewClickThroughRateCompare))
                .setCompareAdvertisingUnitPrice(String.valueOf(sumAdvertisingUnitPriceCompare))
                .setCompareCpa(sumCpaCompare.toPlainString())
                .setCompareOrdersNewToBrandFTD(Int32Value.of(sumOrdersNewToBrand14dCompare))
                .setCompareSalesNewToBrandFTD(sumSalesNewToBrand14dCompare.toPlainString())
                .setCompareOrderRateNewToBrandFTD(sumOrderRateNewToBrand14dCompare.toPlainString())
                .setCompareSalesRateNewToBrandFTD(sumSalesRateNewToBrand14dCompare.toPlainString())
                .setCompareOrdersNewToBrandPercentageFTD(sumOrdersNewToBrandPercentage14dCompare.stripTrailingZeros().toPlainString())

                //环比值
                .setCompareAcosRate(sumAcosCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAcos.subtract(sumAcosCompare))
                        .multiply(new BigDecimal(100)).divide(sumAcosCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareRoasRate(roasCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (roas.subtract(roasCompare))
                        .multiply(new BigDecimal(100)).divide(roasCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdCostRate(sumAdcostCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdcost.subtract(sumAdcostCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdcostCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdCostPerClickRate(sumAdCostPerClickCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdCostPerClick.subtract(sumAdCostPerClickCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdCostPerClickCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdOrderNumRate(sumAdOrderNumCompare == 0 ? "-" : new BigDecimal(sumAdOrderNum - sumAdOrderNumCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumAdOrderNumCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareCvrRate(sumCvrCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCVr.subtract(sumCvrCompare))
                        .multiply(new BigDecimal(100)).divide(sumCvrCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareCtrRate(sumCtrCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCtr.subtract(sumCtrCompare))
                        .multiply(new BigDecimal(100)).divide(sumCtrCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdSaleRate(sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdSale.subtract(sumAdSaleCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdSaleCompare, 2, RoundingMode.HALF_UP).toPlainString())


                .setCompareClicksRate(sumClicksCompare == 0 ? "-" : new BigDecimal(sumClicks - sumClicksCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumClicksCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareImpressionsRate(sumImpressionsCompare == 0 ? "-" : new BigDecimal(sumImpressions - sumImpressionsCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumImpressionsCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAcotsRate(acotsCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (acots.subtract(acotsCompare))
                        .multiply(new BigDecimal(100)).divide(acotsCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAsotsRate(asotsCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (asots.subtract(asotsCompare))
                        .multiply(new BigDecimal(100)).divide(asotsCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareVideo5SecondViewsRate(sumVideo5SecondViewsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideo5SecondViews, sumVideo5SecondViewsCompare, 4), 100)))
                .setCompareVideo5SecondViewRateRate(sumVideo5SecondViewRateCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideo5SecondViewRate, sumVideo5SecondViewRateCompare, 4), 100)))
                .setCompareVideoFirstQuartileViewsRate(sumVideoFirstQuartileViewsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoFirstQuartileViews, sumVideoFirstQuartileViewsCompare, 4), 100)))
                .setCompareVideoMidpointViewsRate(sumVideoMidpointViewsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoMidpointViews, sumVideoMidpointViewsCompare, 4), 100)))
                .setCompareVideoThirdQuartileViewsRate(sumVideoThirdQuartileViewsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoThirdQuartileViews, sumVideoThirdQuartileViewsCompare, 4), 100)))
                .setCompareVideoCompleteViewsRate(sumVideoCompleteViewsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoCompleteViews, sumVideoCompleteViewsCompare, 4), 100)))
                .setCompareVideoUnmutesRate(sumVideoUnmutesCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoUnmutes, sumVideoUnmutesCompare, 4), 100)))
                .setCompareViewImpressionsRate(sumViewImpressionsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumViewImpressions, sumViewImpressionsCompare, 4), 100)))
                .setCompareViewabilityRateRate(sumViewabilityRateCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumViewabilityRate, sumViewabilityRateCompare, 4), 100)))
                .setCompareViewClickThroughRateRate(sumViewClickThroughRateCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumViewClickThroughRate, sumViewClickThroughRateCompare, 4), 100)))
                .setCompareAdvertisingUnitPriceRate(sumAdvertisingUnitPriceCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdvertisingUnitPrice, sumAdvertisingUnitPriceCompare, 4), 100)))
                .setCompareCpaRate(sumCpaCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCpa.subtract(sumCpaCompare))
                        .multiply(new BigDecimal(100)).divide(sumCpaCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareOrdersNewToBrandFTDRate(sumOrdersNewToBrand14dCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumOrdersNewToBrand14d, sumOrdersNewToBrand14dCompare, 4), 100)))
                .setCompareSalesNewToBrandFTDRate(sumSalesNewToBrand14dCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumSalesNewToBrand14d.subtract(sumSalesNewToBrand14dCompare))
                        .multiply(new BigDecimal(100)).divide(sumSalesNewToBrand14dCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareOrderRateNewToBrandFTDRate(sumOrderRateNewToBrand14dCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumOrderRateNewToBrand14d.subtract(sumOrderRateNewToBrand14dCompare))
                        .multiply(new BigDecimal(100)).divide(sumOrderRateNewToBrand14dCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareSalesRateNewToBrandFTDRate(sumSalesRateNewToBrand14dCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumSalesRateNewToBrand14d.subtract(sumSalesRateNewToBrand14dCompare))
                        .multiply(new BigDecimal(100)).divide(sumSalesRateNewToBrand14dCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareOrdersNewToBrandPercentageFTDRate(sumOrdersNewToBrandPercentage14dCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumOrdersNewToBrandPercentage14d.subtract(sumOrdersNewToBrandPercentage14dCompare))
                        .multiply(new BigDecimal(100)).divide(sumOrdersNewToBrandPercentage14dCompare, 2, RoundingMode.HALF_UP).toPlainString());

        if (isVc) {
            builder.setAcots("-");
            builder.setCompareAcotsRate("-");
            builder.setCompareAcots("-");
            builder.setAsots("-");
            builder.setCompareAsotsRate("-");
            builder.setCompareAsots("-");
        }

        return  builder.build();

    }


    @Override
    public Page detailList(Integer puid, CpcQueryWordDetailDto dto, Page page) {
        page = cpcSbQueryKeywordReportDao.detailList(puid, dto, page);
        List<CpcSbQueryKeywordReport> poList = page.getRows();
        if (poList != null && poList.size() > 0) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            poList.forEach(e -> {
                ReportVo vo = getVoWithDateType(e, dto.getDateType(), null);
                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }


    private ReportVo getVoWithDateType(CpcSbQueryKeywordReport report, String dateType, BigDecimal shopSales) {
        //dateType:day,week,month
        String countDate = report.getCountDate();
        report.setCountDate(null);
        ReportVo vo = getVo(report, shopSales);
        //对日期格式进行处理
        if ("day".equals(dateType)) {
            if (StringUtils.isNotEmpty(countDate)) {
                countDate = DateUtil.dateToStrWithFormat(DateUtil.strToDate(countDate, "yyyyMMdd"), "yyyy-MM-dd");
            }
        } else if ("month".equals(dateType)) {
            if (StringUtils.isNotEmpty(countDate)) {
                countDate = DateUtil.dateToStrWithFormat(DateUtil.strToDate(countDate, "yyyyMM"), "yyyy-MM");
            }
        }
        vo.setCountDate(countDate);
        return vo;
    }

    private ReportVo getVo(CpcSbQueryKeywordReport report, BigDecimal shopSales) {
        ReportVo vo = new ReportVo();

        String countDate = report.getCountDate();
        if (StringUtils.isNotEmpty(report.getCountDate())) {
            countDate = DateUtil.dateToStrWithFormat(DateUtil.strToDate(countDate, "yyyyMMdd"), "yyyy-MM-dd");
        }
        vo.setCountDate(countDate);

        vo.setCost(report.getCost());
        vo.setSales(report.getSales14d());
        vo.setImpressions(report.getImpressions());
        vo.setClicks(report.getClicks());
        vo.setOrderNum(report.getConversions14d());
        vo.setSaleNum(report.getConversions14d());


        Double clickRate = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) * 100, Double.valueOf(vo.getImpressions()), 2);
        vo.setClickRate(clickRate);
        Double salesConversionRate = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getSaleNum()) * 100, Double.valueOf(vo.getClicks()), 2);
        vo.setSalesConversionRate(salesConversionRate);
        BigDecimal rate = BigDecimal.valueOf(vo.getClicks()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(vo.getClicks()));
        BigDecimal cpc = rate.setScale(2, BigDecimal.ROUND_HALF_UP);
        vo.setCpc(cpc);


        BigDecimal rate2 = report.getSales14d().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), report.getSales14d());
        rate2 = MathUtil.multiply(rate2, BigDecimal.valueOf(100));
        BigDecimal acos = rate2.setScale(2, BigDecimal.ROUND_HALF_UP);
        vo.setAcos(acos);

        BigDecimal rate3 = BigDecimal.valueOf(report.getConversions14d()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(report.getConversions14d()));
        BigDecimal cpa = rate3.setScale(2, BigDecimal.ROUND_HALF_UP);
        vo.setCpa(cpa);

        if (report.getSales14d() != null && report.getCost() != null) {
            vo.setRoas(report.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : report.getSales14d().divide(report.getCost(), 2, RoundingMode.HALF_UP));
        }
        //新加指标,需要获取广告
        BigDecimal shopTotalSales = Optional.ofNullable(shopSales).orElse(BigDecimal.ZERO);
        vo.setAcots(shopTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getCost().multiply(BigDecimal.valueOf(100)).divide(shopSales, 2, RoundingMode.HALF_UP));
        vo.setAsots(shopTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : report.getSales14d().multiply(BigDecimal.valueOf(100)).divide(shopSales, 2, RoundingMode.HALF_UP));

        return vo;
    }


    @Override
    public ReportVo sumDetailReport(Integer puid, CpcQueryWordDetailDto dto) {
        CpcSbQueryKeywordReport report = cpcSbQueryKeywordReportDao.sumDetailReport(puid, dto);
        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal sumRange = shopSaleDto == null ? BigDecimal.ZERO : shopSaleDto.getSumRange();
        return getVo(report, sumRange);
    }


    @Override
    public List<ReportVo> detailListChart(int puid, CpcQueryWordDetailDto dto) {
        List<CpcSbQueryKeywordReport> poList = cpcSbQueryKeywordReportDao.detailListChart(puid, dto);

        if (poList != null && poList.size() > 0) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            poList.forEach(e -> {
                ReportVo vo = getVoWithDateType(e, dto.getDateType(), null);
                list.add(vo);
            });
            return list;
        }
        return null;
    }


    // 填充指标占比数据
    private void filterMetricData(CpcSbQueryKeywordReport report, ReportVo vo, AdMetricDto adMetricDto) {
        if (adMetricDto == null) {
            vo.setAdCostPercentage("0");
            vo.setAdSalePercentage("0");
            vo.setAdOrderNumPercentage("0");
            vo.setOrderNumPercentage("0");
            return;
        }
        computeMetricData(adMetricDto, report, vo);
    }


    private void computeMetricData(AdMetricDto adMetricDto, CpcSbQueryKeywordReport report, ReportVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (report.getCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(MathUtil.multiply(MathUtil.divide(report.getCost().toString(), adMetricDto.getSumCost().toString()), "100"));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (report.getSales14d() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(MathUtil.multiply(MathUtil.divide(report.getSales14d().toString(), adMetricDto.getSumAdSale().toString()), "100"));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (report.getConversions14d() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(MathUtil.multiply(MathUtil.divide(report.getConversions14d().toString(), adMetricDto.getSumAdOrderNum().toString()), "100"));
        }

    }

    private void fillABARankField(List<ReportVo> rows) {
        if (CollectionUtils.isEmpty(rows)) {
            return;
        }
        //只获取当前页关键词里的搜索词排名及周变化率字段
        List<String> searchTermList = rows.stream()
                .map(item -> item.getQuery().trim().toLowerCase())
                .distinct()
                .collect(Collectors.toList());
        String searchTerms = StringUtil.joinString(searchTermList, StringUtil.SPLIT_COMMA);

        // 调取stateV2接口获取搜索词排名字段
        List<SearchTermsAnalysis> searchTermsAnalysisList = Lists.newArrayList();
        // 抛异常不捕获，避免影响程序执行
        try {
            log.info("start request stateV2 searchTermsAnalysis api");
            long s1 = Instant.now().toEpochMilli();
            searchTermsAnalysisList = searchAnalysisStatsV2Client.getSearchTermAnalysis(searchTerms);
            log.info("调用stateV2接口获取搜索词排名及周变化率，共耗时：{}", Instant.now().toEpochMilli() - s1);
        } finally {
            log.info("end request stateV2 searchTermsAnalysis api");
        }
        if (CollectionUtils.isEmpty(searchTermsAnalysisList)) {
            return;
        }
        Map<String, SearchTermsAnalysis> searchTermsAnalysisMap = searchTermsAnalysisList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(item -> item.getSearchTerm().trim().toLowerCase(), item -> item, (e1, e2) -> e1));
        rows.stream().peek(item -> {
            if (StringUtils.isNotBlank(item.getQuery()) && searchTermsAnalysisMap.get(item.getQuery().trim().toLowerCase()) != null) {
                SearchTermsAnalysis termsAnalysis = searchTermsAnalysisMap.get(item.getQuery().trim().toLowerCase());
                item.setSearchFrequencyRank(termsAnalysis.getSearchFrequencyRank());
                item.setWeekRatio(Optional.ofNullable(termsAnalysis.getWeekRatio()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
            }
        }).collect(Collectors.toList());
    }
}


