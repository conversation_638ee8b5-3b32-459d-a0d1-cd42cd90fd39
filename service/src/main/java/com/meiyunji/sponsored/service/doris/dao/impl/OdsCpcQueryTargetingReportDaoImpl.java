package com.meiyunji.sponsored.service.doris.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.bo.SearchTermAggregateBO;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdQueryStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.dao.helper.SearchQueryTagSqlHelper;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdQueryWordMatrixTopDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardQueryWordTypeEnum;
import com.meiyunji.sponsored.service.util.Constant;
import com.meiyunji.sponsored.service.wordFrequency.enums.WordRoot;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.service.doris.dao.IOdsCpcQueryTargetingReportDao;
import com.meiyunji.sponsored.service.doris.po.OdsCpcQueryTargetingReport;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryKeywordReport;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryTargetingReport;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.cpc.vo.CpcQueryWordDto;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.enums.MatchValueEnum;
import org.springframework.jdbc.core.RowMapper;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.cpc.util.Constants.ASIN_REGEX;

/**
 * 商品投放query报告(OdsCpcQueryTargetingReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:21
 */
@Repository
public class OdsCpcQueryTargetingReportDaoImpl extends DorisBaseDaoImpl<OdsCpcQueryTargetingReport> implements IOdsCpcQueryTargetingReportDao {

    @Override
    public String buildQueryAdQueryWordChartsSql(Integer puid, List<String> marketplaceIdList, List<Integer> shopIdList, String currency, String startDate, String endDate, List<Object> argsList) {
        StringBuilder sql = new StringBuilder();
        sql.append("select query queryWord, match_type matchType, ifnull(sum(r.cost * c.rate), 0) cost, ifnull(sum(total_sales * c.rate), 0) totalSales, ");
        sql.append("ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ");
        sql.append("ifnull(sum(sale_num), 0) orderNum, ifnull(sum(order_num), 0) saleNum ");
        sql.append("from ").append(getJdbcHelper().getTable()).append(" r ");
        sql.append("join (select m.marketplace_id,c.month,c.rate,c.puid from dim_currency_rate c join dim_marketplace_info m on ");
        sql.append("c.`from` = m.currency and c.puid=? and `to`=? and month>=? and month <=? ");
        argsList.add(puid);
        argsList.add(currency);
        argsList.add(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        argsList.add(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));

        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sql.append("and m.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }

        sql.append(") c on r.puid = ? and r.puid = c.puid and r.count_month = c.month and c.marketplace_id = r.marketplace_id and r.count_day >= ? and r.count_day <= ? ");
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sql.append("and r.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append("and r.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        sql.append("group by queryWord, matchType ");

        argsList.add(puid);
        argsList.add(startDate);
        argsList.add(endDate);
        return sql.toString();
    }

    @Override
    public String buildQueryAdQueryWordTopSimpleInfosSql(Integer puid,
                                                         List<String> marketplaceIdList,
                                                         List<Integer> shopIdList,
                                                         String currency,
                                                         String startDate,
                                                         String endDate,
                                                         List<Object> argsList,
                                                         String innerSelectColumns,
                                                         boolean isOrderByFieldAboutAmount,
                                                         boolean firstQuery,
                                                         List<String> queryWords, List<String> siteToday, Boolean isSiteToday,
                                                         List<String> portfolioIds, List<String> campaignIds,
                                                         Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum) {
        boolean isWhere = false;
        StringBuilder sql = new StringBuilder();
        sql.append("select query queryWord, match_type matchType, any(r.marketplace_id) marketplaceId, count(distinct target_id) targetNum, ");
        sql.append(innerSelectColumns);
        sql.append(" from ").append(getJdbcHelper().getTable()).append(" r ");
        if (firstQuery && isOrderByFieldAboutAmount || !firstQuery && !isOrderByFieldAboutAmount) {
            sql.append("join (select m.marketplace_id,c.month,c.rate from dim_currency_rate c join dim_marketplace_info m on ");
            sql.append("c.`from` = m.currency and c.puid=? and `to`=? and month>=? and month <=? ");
            argsList.add(puid);
            argsList.add(currency);
            argsList.add(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
            argsList.add(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));

            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append("and m.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
            }

            sql.append(") c on r.marketplace_id = c.marketplace_id and r.count_month = c.month and r.puid = ? and r.count_day>=? and r.count_day<=? ");
            argsList.add(puid);
            argsList.add(startDate);
            argsList.add(endDate);
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append("and r.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sql.append(SqlStringUtil.dealBitMapDorisInList("r.shop_id", shopIdList, argsList));
            }
        } else {
            isWhere = true;
            sql.append(" where r.puid = ? and r.count_day >=? and r.count_day <= ? ");
            argsList.add(puid);
            argsList.add(startDate);
            argsList.add(endDate);
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append("and r.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sql.append(SqlStringUtil.dealBitMapDorisInList("r.shop_id", shopIdList, argsList));
            }
        }


        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sql.append(SqlStringUtil.dealDorisInList("concat_ws('|', r.marketplace_id, r.count_day)", siteToday, argsList));
            sql.append(" and r.count_day >= ? and r.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sql.append(" and r.count_day >= ? and r.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealDorisInList("r.campaign_id", campaignIds, argsList));
        }



        if (CollectionUtils.isNotEmpty(queryWords) && !firstQuery) {
            sql.append(SqlStringUtil.dealInList("query", queryWords, argsList));
        }


        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (isWhere) {
                sql.append(" and ");
            } else {
                sql.append(" where ");
                isWhere = true;
            }
            sql.append(" r.campaign_id in ( ");
            sql.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = 'sp' ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sql.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sql.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sql.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sql.append(" ) ");
        }

        if (Boolean.TRUE.equals(noZero) && DashboardDataFieldEnum.noZeroFieldSet.contains(dashboardDataFieldEnum.getCode())) {
            if (isWhere) {
                sql.append(" and ");
            } else {
                sql.append(" where ");
                isWhere = true;
            }
            sql.append(" r." + getColumn(dashboardDataFieldEnum.getCode()) +" <> 0 ");
        }
        sql.append("group by queryWord, matchType ");
        return sql.toString();
    }

    @Override
    public List<DashboardAdQueryWordMatrixTopDto> queryMatrixInfo(Integer puid, List<String> marketplaceIdList, List<Integer> shopIdList, String currency,
                                                                  String startDate, String endDate, DashboardDataFieldEnum dataField, List<String> keywordIdList,
                                                                  Integer limit, DashboardOrderByEnum orderBy, DashboardQueryWordTypeEnum queryWordType,
                                                                  List<String> siteToday, Boolean isSiteToday,
                                                                  List<String> portfolioIds, List<String> campaignIds,
                                                                  Boolean noZero) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
//            sb.append(" SELECT 'SP' as campaignType, tReport.target_id targetingId, tReport.query query, ");
//            sb.append(" tReport.shop_id shopId, tReport.marketplace_id marketplaceId, 'targeting' as `type`,");
//            sb.append(" tReport.campaign_id campaignId, tReport.ad_group_id adGroupId, ");
//            sb.append(" tReport.campaign_name campaignName, tReport.ad_group_name adGroupName, tReport.match_type matchType, tReport.targeting_text as targetingText, ");
            sb.append(" SELECT 'sp' as campaignType, 'targeting' as type, report.query_id queryId, ");
            sb.append(" ifnull(sum(cost * c.rate), 0) cost, ifnull(sum(total_sales * c.rate), 0) totalSales, ");
            sb.append(" ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ");
            sb.append(" ifnull(sum(sale_num), 0) orderNum, ");
            sb.append(" ifnull(sum(order_num), 0) saleNum, ");
            sb.append(" ifnull(ROUND(ifnull(sum(clicks)/ sum(impressions), 0), 4), 0) clickRate, ");//点击率
            sb.append(" ifnull(ROUND(ifnull(sum(sale_num)/ sum(clicks), 0), 4), 0) conversionRate, ");//转化率
            sb.append(" ifnull(ROUND(ifnull(sum(cost * c.rate), 0)/ ifnull(sum(total_sales * c.rate), 0), 4), 0) acos, ");
            sb.append(" ifnull(ROUND(ifnull(sum(total_sales * c.rate), 0)/ ifnull(sum(cost * c.rate), 0), 4), 0) roas, ");
            sb.append(" ifnull(ROUND(ifnull(sum(cost * c.rate)/ sum(clicks), 0), 4), 0) cpc, ");//cpc
            sb.append(" ifnull(ROUND(ifnull(sum(cost * c.rate)/ sum(sale_num), 0), 4), 0) cpa ");//cpa
            sb.append(" from ods_t_cpc_query_targeting_report report ");
            sb.append(" join (select * from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency and puid = ? and `to` = ? ");
            sb.append(" and c.month >= ").append(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(" and c.month <= ").append(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(") c");
            sb.append(" on c.marketplace_id = report.marketplace_id and report.count_month = c.month ");
            sb.append(" and report.puid = ? ");
            argsList.add(puid);
            argsList.add(currency);
            argsList.add(puid);
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append("and report.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append("and report.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
            }
            if (CollectionUtils.isNotEmpty(keywordIdList)) {
                sb.append("and report.target_id in ('").append(StringUtils.join(keywordIdList, "','")).append("') ");
            }


        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', report.marketplace_id, report.count_day)", siteToday, argsList));
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("report.campaign_id", campaignIds, argsList));
        }
        if (Boolean.TRUE.equals(noZero)) {
            sb.append(" and " + dataField.getCode() + " <> 0 ");
        }


        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" where report.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = 'sp' ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }
            sb.append(" group by report.query_id ");
            sb.append(" ORDER BY ").append(dataField.getCode());
            if (StringUtils.isNotEmpty(orderBy.getCode())) {
                sb.append(" ").append(orderBy.getCode());
            }
            if (Objects.nonNull(limit) && limit != 0) {
                sb.append(" LIMIT ").append(limit);
            }
        return getJdbcTemplate().query(sb.toString(), new BeanPropertyRowMapper<>(DashboardAdQueryWordMatrixTopDto.class), argsList.toArray());
    }

    @Override
    public List<OdsCpcQueryTargetingReport> getByQueryIdList(Integer puid, List<Integer> shopIdList, Collection<String> queryIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT query_id, query, shop_id, marketplace_id, campaign_id, ad_group_id, target_id, ");
        sb.append(" match_type, targeting_text ");
        sb.append(" from ").append(getJdbcHelper().getTable());
        sb.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(queryIdList)) {
            sb.append("and query_id in ('").append(StringUtils.join(queryIdList, "','")).append("') ");
        }
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(OdsCpcQueryTargetingReport.class), argsList.toArray());
    }

    @Override
    public Page pageManageList(Integer puid, CpcQueryWordDto dto, Page page) {
        StringBuilder sql = new StringBuilder("SELECT query_id,`query`,marketplace_id, type, keywordId, target_id,main_image, keywordText,matchType,targeting_expression,targeting_type,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("impressions,clicks,cost,sale_num,ad_order_num,")
                .append("total_sales,`ad_sales`,`ad_sale_num`, ")
                .append("order_num  from ( ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");

        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectTargetSql = new StringBuilder("SELECT ANY(`query_id`) `query_id`, ANY(`query`) `query`, ANY(marketplace_id) marketplace_id,'target' as type,'' as keywordId, ANY(target_id) target_id, ANY(main_image) main_image, '' as keywordText,'' as matchType,ANY(targeting_expression) targeting_expression,ANY(targeting_type) targeting_type,ANY(ad_group_id) ad_group_id,ANY(ad_group_name) ad_group_name,ANY(campaign_id) campaign_id,ANY(campaign_name) campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ")
                .append(this.getJdbcHelper().getTable());
        List<Object> argsList = Lists.newArrayList();
        String whereSql = this.getWhereSqlCountByTarget(puid, dto, argsList);
        selectTargetSql.append(whereSql);

        StringBuilder selectKeywordSql = new StringBuilder("SELECT ANY(`query_id`) `query_id`, ANY(`query`) `query`,ANY(marketplace_id) marketplace_id,'keyword' as type, ANY(keyword_id) as keywordId, '' as target_id, '' as main_image, ANY(keyword_text) as keywordText,ANY(match_type) as matchType,'' as targeting_expression,'' as targeting_type, ANY(ad_group_id) ad_group_id,ANY(ad_group_name) ad_group_name,ANY(campaign_id) campaign_id,ANY(campaign_name) campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ")
                .append(" ods_t_cpc_query_keyword_report ");
        String keywordWhereSql = this.getWhereSqlCountByKeyword(puid, dto, argsList);
        selectKeywordSql.append(keywordWhereSql);

        sql.append(selectTargetSql);
        sql.append(" UNION ALL ");
        sql.append(selectKeywordSql);
        sql.append(" ) p ");

        if (StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())) {
            String orderField = ReportService.getOrderField(dto.getOrderField(), false);
            if (StringUtils.isNotBlank(orderField)) {
                sql.append(" order by ").append(orderField);
                if ("desc".equals(dto.getOrderValue())) {
                    sql.append(" desc");
                }
                sql.append(" , query desc ");
            }
        } else {
            sql.append("  order by query desc ");
        }

        countSql.append(selectTargetSql);
        countSql.append(" UNION ALL ");
        countSql.append(selectKeywordSql);
        countSql.append(" ) p ");

        Object[] args = argsList.toArray();
        return getPageResultByClass(page.getPageNo(), page.getPageSize(), countSql.toString(), args, sql.toString(), args, CpcQueryTargetingReport.class);
    }

    @Override
    public AdMetricDto getSumAdMetricDto(Integer puid, CpcQueryWordDto dto) {
        StringBuilder sql = new StringBuilder("SELECT SUM(cost) cost, SUM(sale_num) sale_num, ");
        sql.append("SUM(total_sales) total_sales, SUM(order_num) order_num from ( ");
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectTargetSql = new StringBuilder("SELECT ANY(`query`) `query`,'target' as type,'' as keywordId, ANY(target_id) target_id,ANY(main_image) main_image, '' as keywordText,'' as matchType,ANY(targeting_expression) targeting_expression,ANY(targeting_type) targeting_type,ANY(ad_group_id) ad_group_id,ANY(ad_group_name) ad_group_name,ANY(campaign_id) campaign_id,ANY(campaign_name) campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ")
                .append(this.getJdbcHelper().getTable());
        List<Object> argsList = Lists.newArrayList();
        String whereSql = this.getWhereSqlCountByTarget(puid, dto, argsList);
        selectTargetSql.append(whereSql);

        StringBuilder selectKeywordSql = new StringBuilder("SELECT ANY(`query`) `query`,'keyword' as type, ANY(keyword_id) as keywordId, '' as target_id, '' as main_image, ANY(keyword_text) as keywordText,ANY(match_type) as matchType,'' as targeting_expression,'' as targeting_type, ANY(ad_group_id) ad_group_id,ANY(ad_group_name) ad_group_name,ANY(campaign_id) campaign_id,ANY(campaign_name) campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ")
                .append(" ods_t_cpc_query_keyword_report ");
        String keywordWhereSql = this.getWhereSqlCountByKeyword(puid, dto, argsList);
        selectKeywordSql.append(keywordWhereSql);

        sql.append(selectTargetSql);
        sql.append(" UNION ALL ");
        sql.append(selectKeywordSql);
        sql.append(" ) p ");

        List<AdMetricDto> adMetricDtoList = getJdbcTemplate().query(sql.toString(), new RowMapper<AdMetricDto>() {
            @Override
            public AdMetricDto mapRow(ResultSet re, int i) throws SQLException {
                AdMetricDto dto = AdMetricDto.builder()
                        .sumCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .sumAdSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("sale_num")).orElse(BigDecimal.ZERO))
                        .sumOrderNum(Optional.ofNullable(re.getBigDecimal("order_num")).orElse(BigDecimal.ZERO))
                        .build();
                return dto;
            }
        }, argsList.toArray());
        return adMetricDtoList != null && adMetricDtoList.size() > 0 ? adMetricDtoList.get(0) : null;
    }

    @Override
    public List<AdHomePerformancedto> getQueryAsinReportAggregate(Integer puid, CpcQueryWordDto dto, boolean isGroupByDate, boolean selCompareData) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select ");
        if (isGroupByDate) {
            sql.append(" count_day, ");
        }
        sql.append(" SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`,SUM(order_num) order_num from ( ");
        //keyword搜索词表
        StringBuilder keywordSql = new StringBuilder();
        keywordSql.append("select ");
        if (isGroupByDate) {
            keywordSql.append(" count_day, ");
        }
        keywordSql.append(" SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`,SUM(order_num) order_num FROM ")
                .append(" ods_t_cpc_query_keyword_report ")
                .append(" where puid= ? and shop_id= ?  and count_day >= ? and count_day <= ?  ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        if (selCompareData) {
            argsList.add(dto.getCompareStartDate());
            argsList.add(dto.getCompareEndDate());
        } else {
            argsList.add(dto.getStart());
            argsList.add(dto.getEnd());
        }
        keywordSql.append(" and query_id in ")
                .append(" ( ")
                .append("SELECT query_id FROM ods_t_cpc_query_keyword_report")
                .append(this.getWhereSqlCountByKeyword(puid,dto,argsList))
                .append(" ) ");
        if (isGroupByDate) {
            keywordSql.append(" group by count_day ");
        }
        //target搜索词表
        StringBuilder targetSql = new StringBuilder();
        targetSql.append("select ");
        if (isGroupByDate) {
            targetSql.append(" count_day, ");
        }
        targetSql.append(" SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`,SUM(order_num) order_num FROM ")
                .append(this.getJdbcHelper().getTable())
                .append(" where puid= ? and shop_id= ? and count_day >= ? and count_day <= ? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        if (selCompareData) {
            argsList.add(dto.getCompareStartDate());
            argsList.add(dto.getCompareEndDate());
        } else {
            argsList.add(dto.getStart());
            argsList.add(dto.getEnd());
        }
        targetSql.append(" and query_id in ")
                .append(" ( ")
                .append("SELECT query_id  FROM ods_t_cpc_query_targeting_report ")
                .append(this.getWhereSqlCountByTarget(puid,dto,argsList))
                .append(" ) ");
        if (isGroupByDate) {
            targetSql.append(" group by count_day ");
        }

        sql.append(keywordSql);
        sql.append(" UNION ALL ");
        sql.append(targetSql);
        sql.append(" ) p ");

        if (isGroupByDate) {
            sql.append(" group by count_day ");
        }

        Object[] args = argsList.toArray();
        return getJdbcTemplate().query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
            @Override
            public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                return  AdHomePerformancedto.builder()
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        /**
                         * TODO 广告报告重构
                         * 本广告产品订单量
                         */
                        .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        //广告销量
                        .salesNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))
                        //本广告产品销量
                        .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                        //时间
                        .countDate(!isGroupByDate ? "" : DateUtil.dateToStrWithFormat(DateUtil.strToDate(re.getString("count_day"), DateUtil.PATTERN), DateUtil.PATTERN_YYYYMMDD))
                        .build();
            }
        }, args);
    }

    private String getWhereSqlCountByTarget(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and count_day>=? and count_day<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        if (StringUtils.isNotBlank(dto.getCampaignId())) {
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(dto.getGroupId())) {
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", list, argsList));
        }
        // 广告策略筛选
        if(CollectionUtils.isNotEmpty(dto.getAdStrategyTypeList())){
            String sql = AdQueryStrategyTypeEnum.getSql(dto.getAdStrategyTypeList(), dto.getAutoRuleIds(),dto.getAutoRuleGroupIds(), argsList, "query_id","ad_group_id");
            if(StringUtils.isNotEmpty(sql)){
                whereSql.append(sql);
            }
        }
        if (StringUtils.isNotBlank(dto.getFilterTargetType())) {
            if (dto.getFilterTargetType().contains("=")) {
                List<String> list = StringUtil.splitStr(dto.getFilterTargetType()).stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                if (list.size() < 7) {
                    List<String> inList = list.stream().filter(s -> !s.contains("=")).collect(Collectors.toList());
                    List<String> likeList = list.stream().filter(s -> s.contains("=")).collect(Collectors.toList());
                    whereSql.append(" and (");
                    if (CollectionUtils.isNotEmpty(inList)) {
                        whereSql.append(SqlStringUtil.dealInListNotAnd("lower(targeting_expression)", inList, argsList));
                        whereSql.append(" or ");
                        whereSql.append(SqlStringUtil.dealPrefixLikeListOr("lower(targeting_expression)", likeList, argsList));
                    } else {
                        whereSql.append(SqlStringUtil.dealPrefixLikeListOr("lower(targeting_expression)", likeList, argsList));
                    }
                    whereSql.append(")");
                }
            } else {
                whereSql.append(SqlStringUtil.dealInList("lower(targeting_expression)", StringUtil.splitStr(dto.getFilterTargetType()), argsList));
            }
        }

        whereSql.append(" and query REGEXP '" + ASIN_REGEX + "' ");
        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryTargetingReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                if (dto.getSearchField().equals(CpcQueryWordDto.SearchFieldEnum.QUERY.getValue())) {
                    dto.setSearchValue(dto.getSearchValue().toLowerCase());
                }
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    if ("targetingExpression".equalsIgnoreCase(dto.getSearchField())) {
                        whereSql.append(" and LOWER(").append(field).append(") like ?");
                    } else {
                        whereSql.append(" and ").append(field).append(" like ?");
                    }

                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue().toLowerCase()) + "%");
                } else {//默认精确
                    String f = field;
                    List<String> listSearchValue = dto.getListSearchValue();
                    if ("targetingExpression".equalsIgnoreCase(dto.getSearchField())) {
                        f = "LOWER(" + field + ")";
                        listSearchValue = listSearchValue.stream().map(String::toLowerCase).collect(Collectors.toList());
                    }

                    if (dto.getListSearchValue().size() > 1) {
                        whereSql.append(SqlStringUtil.dealInList(f, listSearchValue, argsList));
                    } else {
                        whereSql.append(" and ").append(f).append(" = ?");
                        argsList.add(dto.getSearchValue());
                    }
                }
            }
        }
        //标签
        if (CollectionUtils.isNotEmpty(dto.getQueryWordTagTypeList())) {
            if (dto.getQueryWordTagTypeList().contains(Constants.QUERY_NOT_TARGET)) {
                if (dto.getQueryWordTagTypeList().size() > 1) {
                    List<String> allSearchField = CpcQueryWordDto.QueryWordTagTypeEnum.getAllSearchField();
                    allSearchField.removeAll(dto.getQueryWordTagTypeList());
                    if (CollectionUtils.isNotEmpty(allSearchField)) {
                        whereSql.append(" AND concat_ws(',', ad_group_id, LOWER(query)) not in ")
                                .append(" ( ")
                                .append(SearchQueryTagSqlHelper.getTargetSearchQueryTagInSql(dto.getPuid(), dto, argsList, allSearchField))
                                .append(" ) ");
                    }
                } else {
                    whereSql.append(" and concat_ws(',', ad_group_id, LOWER(query)) not in ")
                            .append(" ( ")
                            .append(SearchQueryTagSqlHelper.getTargetSearchQueryTagInSql(dto.getPuid(), dto, argsList, CpcQueryWordDto.QueryWordTagTypeEnum.getAllSearchField()))
                            .append(" ) ");
                }
            } else {
                whereSql.append(" and concat_ws(',', ad_group_id, LOWER(query)) in ")
                        .append(" ( ")
                        .append(SearchQueryTagSqlHelper.getTargetSearchQueryTagInSql(puid, dto, argsList, dto.getQueryWordTagTypeList()))
                        .append(" ) ");
            }
        }
        //词根
        if (StringUtils.isNotBlank(dto.getWordRoot())) {
            whereSql.append(" and query_id in ( ")
                    .append(this.getWordRootQueryIdSqlByQuery(puid, dto, argsList, WordRoot.QueryType.SP_TARGETING.getType()))
                    .append(" ) ");
        }
        whereSql.append(" group by query_id ");
        if (dto.getUseAdvanced()) {
            whereSql.append(this.getHavingSql(dto, argsList));
        }
        return whereSql.toString();
    }

    private String getWhereSqlCountByKeyword(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder();
        //如果筛选的是商品定位不查keyword表
        if (dto.getSearchField().equals(CpcQueryWordDto.SearchFieldEnum.TARGETING_EXPRESSION.getValue())
                && StringUtils.isNotBlank(dto.getSearchValue())) {
           return " where 1=0 group by query_id ";
        }
        whereSql.append(" where puid=? and shop_id=? and count_day>=? and count_day<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        whereSql.append(" and query REGEXP '" + ASIN_REGEX + "' ");
        if (StringUtils.isNotBlank(dto.getFilterTargetType())) {
            List<String> stringList = StringUtil.splitStr(dto.getFilterTargetType()).stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (stringList.size() < 7) {
                whereSql.append(" and match_type = 'noType' ");
            }
        }

        /**
         *   根据匹配类型（matchType）查对应表的数据
         *   matchType in ('close-match','loose-match')紧密匹配，宽泛匹配 不查询该表数据
         *   matchType in ('broad','phrase','exact')'广泛匹配','词组匹配','精准匹配' 查询该表数据
         *   matchTypes：符合查询该表数据的条件('广泛匹配','词组匹配','精准匹配')数组
         *   MatchValueEnum值是('广泛匹配','词组匹配','精准匹配')的枚举
         */
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(), StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        //不带匹配类型条件查询不走下面逻辑
        // start
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isNotBlank(MatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            //如果匹配条件为(紧密匹配，宽泛匹配),则不查询数据
            //把matchTypes作为条件查不出数据
            if (CollectionUtils.isEmpty(matchTypes)) {
                whereSql.append(" group by query_id having 1=0 ");
                return whereSql.toString();
            }
        }
        //end
        if (CollectionUtils.isNotEmpty(matchTypes)) {
            whereSql.append(SqlStringUtil.dealInList("lower(match_type)", matchTypes, argsList));
        }
        if (StringUtils.isNotBlank(dto.getCampaignId())) {
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", list, argsList));

        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(dto.getGroupId())) {
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", list, argsList));
        }
        // 广告策略筛选
        if(CollectionUtils.isNotEmpty(dto.getAdStrategyTypeList())){
            String sql = AdQueryStrategyTypeEnum.getSql(dto.getAdStrategyTypeList(), dto.getAutoRuleIds(),dto.getAutoRuleGroupIds(), argsList, "query_id","ad_group_id");
            if(StringUtils.isNotEmpty(sql)){
                whereSql.append(sql);
            }
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryKeywordReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                if (dto.getSearchField().equals(CpcQueryWordDto.SearchFieldEnum.QUERY.getValue())) {
                    dto.setSearchValue(dto.getSearchValue().toLowerCase());
                }
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    whereSql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    if (dto.getListSearchValue().size() > 1) {
                        whereSql.append(SqlStringUtil.dealInList(field, dto.getListSearchValue(), argsList));
                    } else {
                        whereSql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getSearchValue());
                    }
                }
            }
        }
        //标签
        if (CollectionUtils.isNotEmpty(dto.getQueryWordTagTypeList())) {
            if (dto.getQueryWordTagTypeList().contains(Constants.QUERY_NOT_TARGET)) {

                if (dto.getQueryWordTagTypeList().size() > 1) {
                    List<String> allSearchField = CpcQueryWordDto.QueryWordTagTypeEnum.getAllSearchField();
                    allSearchField.removeAll(dto.getQueryWordTagTypeList());
                    if (CollectionUtils.isNotEmpty(allSearchField)) {
                        whereSql.append(" AND concat_ws(',', ad_group_id, query) not in ")
                                .append(" ( ")
                                .append(SearchQueryTagSqlHelper.getTargetSearchQueryTagInSql(dto.getPuid(), dto, argsList, allSearchField))
                                .append(" ) ");
                    }
                } else {
                    whereSql.append(" and concat_ws(',', ad_group_id, query) not in ")
                            .append(" ( ")
                            .append(SearchQueryTagSqlHelper.getTargetSearchQueryTagInSql(dto.getPuid(), dto, argsList, CpcQueryWordDto.QueryWordTagTypeEnum.getAllSearchField()))
                            .append(" ) ");
                }
            } else {
                whereSql.append(" and concat_ws(',', ad_group_id, query) in ")
                        .append(" ( ")
                        .append(SearchQueryTagSqlHelper.getTargetSearchQueryTagInSql(puid, dto, argsList, dto.getQueryWordTagTypeList()))
                        .append(" ) ");
            }

        }
        //词根
        if (StringUtils.isNotBlank(dto.getWordRoot())) {
            whereSql.append(" and query_id in ( ")
                    .append(this.getWordRootQueryIdSqlByQuery(puid, dto, argsList, WordRoot.QueryType.SP_QUERY.getType()))
                    .append(" ) ");
        }
        whereSql.append(" group by query_id ");
        if (dto.getUseAdvanced()) {
            whereSql.append(this.getHavingSql(dto, argsList));
        }
        return whereSql.toString();
    }

    private String getHavingSql(CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder havingSql = new StringBuilder();
        BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

        havingSql.append(" having 1=1 ");
        //展示量
        if (dto.getImpressionsMin() != null) {
            havingSql.append(" and SUM(impressions) >= ?");
            argsList.add(dto.getImpressionsMin());
        }
        if (dto.getImpressionsMax() != null) {
            havingSql.append(" and SUM(impressions) <= ?");
            argsList.add(dto.getImpressionsMax());
        }
        //点击量
        if (dto.getClicksMin() != null) {
            havingSql.append(" and SUM(clicks) >= ?");
            argsList.add(dto.getClicksMin());
        }
        if (dto.getClicksMax() != null) {
            havingSql.append(" and SUM(clicks) <= ?");
            argsList.add(dto.getClicksMax());
        }
        //点击率（clicks/impressions）
        if (dto.getClickRateMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(clicks)/SUM(impressions),0),4) >= ?");
            argsList.add(dto.getClickRateMin());
        }
        if (dto.getClickRateMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(clicks)/SUM(impressions),0),4) <= ?");
            argsList.add(dto.getClickRateMax());
        }
        //花费
        if (dto.getCostMin() != null) {
            havingSql.append(" and SUM(cost) >= ?");
            argsList.add(dto.getCostMin());
        }
        if (dto.getCostMax() != null) {
            havingSql.append(" and SUM(cost) <= ?");
            argsList.add(dto.getCostMax());
        }
        //cpc  平均点击费用
        if (dto.getCpcMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(clicks),0),2) >= ?");
            argsList.add(dto.getCpcMin());
        }
        if (dto.getCpcMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(clicks),0),2) <= ?");
            argsList.add(dto.getCpcMax());
        }
        //广告订单量
        if (dto.getOrderNumMin() != null) {
            havingSql.append(" and SUM(sale_num) >= ?");
            argsList.add(dto.getOrderNumMin());
        }
        if (dto.getOrderNumMax() != null) {
            havingSql.append(" and SUM(sale_num) <= ?");
            argsList.add(dto.getOrderNumMax());
        }
        //广告销售额
        if (dto.getSalesMin() != null) {
            havingSql.append(" and SUM(total_sales) >= ?");
            argsList.add(dto.getSalesMin());
        }
        if (dto.getSalesMax() != null) {
            havingSql.append(" and SUM(total_sales) <= ?");
            argsList.add(dto.getSalesMax());
        }
        //订单转化率
        if (dto.getSalesConversionRateMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(sale_num)/SUM(clicks),0),4) >= ?");
            argsList.add(dto.getSalesConversionRateMin());
        }
        if (dto.getSalesConversionRateMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(sale_num)/SUM(clicks),0),4) <= ?");
            argsList.add(dto.getSalesConversionRateMax());
        }
        //acos
        if (dto.getAcosMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(total_sales),0),4) >= ?");
            argsList.add(dto.getAcosMin());
        }
        if (dto.getAcosMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(total_sales),0),4) <= ?");
            argsList.add(dto.getAcosMax());
        }
        // roas
        if (dto.getRoasMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(total_sales)/SUM(cost),0),2) >= ?");
            argsList.add(dto.getRoasMin());
        }
        // roas
        if (dto.getRoasMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(total_sales)/SUM(cost),0),2) <= ?");
            argsList.add(dto.getRoasMax());
        }
        // acots  需要乘以店铺销售额
        if (dto.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                havingSql.append(" and ROUND((ifnull(SUM(cost),0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(dto.getAcotsMin());
            } else {
                havingSql.append(" and 0 >= ? ");
                argsList.add(dto.getAcotsMin());
            }
        }
        // acots  需要乘以店铺销售额
        if (dto.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                havingSql.append(" and ROUND((ifnull(SUM(cost),0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(dto.getAcotsMax());
            } else {
                havingSql.append(" and 0 <= ? ");
                argsList.add(dto.getAcotsMin());
            }
        }
        // asots 需要乘以店铺销售额
        if (dto.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                havingSql.append(" and ROUND((ifnull(SUM(total_sales),0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(dto.getAsotsMin());
            } else {
                havingSql.append(" and 0 >= ? ");
                argsList.add(dto.getAcotsMin());
            }
        }
        // asots  需要乘以店铺销售额
        if (dto.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                havingSql.append(" and ROUND((ifnull(SUM(total_sales),0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(dto.getAsotsMax());
            } else {
                havingSql.append(" and 0 <= ? ");
                argsList.add(dto.getAcotsMin());
            }
        }
        //CPA
        if (dto.getCpaMin() != null) {
            havingSql.append(" and ROUND(ROUND(ifnull(SUM(cost)/SUM(sale_num),0), 4), 2) >= ?");
            argsList.add(dto.getCpaMin());
        }
        if (dto.getCpaMax() != null) {
            havingSql.append(" and ROUND(ROUND(ifnull(SUM(cost)/SUM(sale_num),0), 4), 2) <= ?");
            argsList.add(dto.getCpaMax());
        }

        //本广告产品订单量
        if (dto.getAdSaleNumMin() != null) {
            havingSql.append(" and ifnull(SUM(ad_sale_num), 0) >= ?");
            argsList.add(dto.getAdSaleNumMin());
        }
        if (dto.getAdSaleNumMax() != null) {
            havingSql.append(" and ifnull(SUM(ad_sale_num), 0) <= ?");
            argsList.add(dto.getAdSaleNumMax());
        }
        //其他产品广告订单量
        if (dto.getAdOtherOrderNumMin() != null) {
            havingSql.append(" and ifnull(SUM(sale_num) - SUM(ad_sale_num), 0) >= ?");
            argsList.add(dto.getAdOtherOrderNumMin());
        }
        if (dto.getAdOtherOrderNumMax() != null) {
            havingSql.append(" and ifnull(SUM(sale_num) - SUM(ad_sale_num), 0) <= ?");
            argsList.add(dto.getAdOtherOrderNumMax());
        }
        //本广告产品销售额
        if (dto.getAdSalesMin() != null) {
            havingSql.append(" and ifnull(SUM(ad_sales), 0) >= ?");
            argsList.add(dto.getAdSalesMin());
        }
        if (dto.getAdSalesMax() != null) {
            havingSql.append(" and ifnull(SUM(ad_sales), 0) <= ?");
            argsList.add(dto.getAdSalesMax());
        }
        //其他产品广告销售额
        if (dto.getAdOtherSalesMin() != null) {
            havingSql.append(" and ifnull(SUM(total_sales) - SUM(ad_sales), 0) >= ?");
            argsList.add(dto.getAdOtherSalesMin());
        }
        if (dto.getAdOtherSalesMax() != null) {
            havingSql.append(" and ifnull(SUM(total_sales) - SUM(ad_sales), 0) <= ?");
            argsList.add(dto.getAdOtherSalesMax());
        }
        //广告销量
        if (dto.getAdSalesTotalMin() != null) {
            havingSql.append(" and ifnull(SUM(order_num), 0) >= ?");
            argsList.add(dto.getAdSalesTotalMin());
        }
        if (dto.getAdSalesTotalMax() != null) {
            havingSql.append(" and ifnull(SUM(order_num), 0) <= ?");
            argsList.add(dto.getAdSalesTotalMax());
        }
        //本广告产品销量
        if (dto.getAdSelfSaleNumMin() != null) {
            havingSql.append(" and ifnull(SUM(ad_order_num), 0) >= ?");
            argsList.add(dto.getAdSelfSaleNumMin());
        }
        if (dto.getAdSelfSaleNumMax() != null) {
            havingSql.append(" and ifnull(SUM(ad_order_num), 0) <= ?");
            argsList.add(dto.getAdSelfSaleNumMax());
        }
        //其他产品广告销量
        if (dto.getAdOtherSaleNumMin() != null) {
            havingSql.append(" and ifnull(SUM(order_num) - SUM(ad_order_num), 0) >= ?");
            argsList.add(dto.getAdOtherSaleNumMin());
        }
        if (dto.getAdOtherSaleNumMax() != null) {
            havingSql.append(" and ifnull(SUM(order_num) - SUM(ad_order_num), 0) <= ?");
            argsList.add(dto.getAdOtherSaleNumMax());
        }

        // 广告笔单价(广告销售额÷广告订单量×100%)
        if (dto.getAdvertisingUnitPriceMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(total_sales)/SUM(sale_num), 0), 2) >= ?");
            argsList.add(dto.getAdvertisingUnitPriceMin());
        }
        if (dto.getAdvertisingUnitPriceMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(total_sales)/SUM(sale_num), 0), 2) <= ?");
            argsList.add(dto.getAdvertisingUnitPriceMax());
        }
        return havingSql.toString();
    }

    private String getWordRootQueryIdSqlByQuery(int puid, CpcQueryWordDto dto, List<Object> argsList, Integer wordRootQueryType) {
        StringBuilder sb = new StringBuilder("select distinct query_id from ods_t_amazon_word_root_query ");
        sb.append(" where puid = ? and shop_id = ? and count_day >= ? and count_day <= ? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());

        sb.append(" and query_type = ? ");
        argsList.add(wordRootQueryType);

        //词根
        if (StringUtils.isNotBlank(dto.getWordRoot())) {
            sb.append(" and word_root = ? ");
            argsList.add(dto.getWordRoot());
        }
        return sb.toString();
    }

    /**
     * 用户排除为0 的字段处理
     * @param orderByField
     * @return
     */
    private String getColumn(String orderByField) {

        switch (orderByField) {
            case "totalSales":
                return "total_sales";
            case "orderNum":
                return "sale_num";
            case "saleNum":
                return "order_num";
            default:
                return orderByField ;
        }
    }


    @Override
    public String buildQueryAdQueryWordPageSql(Integer puid,
                                               List<String> marketplaceIdList,
                                               List<Integer> shopIdList,
                                               String startDate,
                                               String endDate,
                                               List<Object> argsList,
                                             String queryWord, List<String> siteToday, Boolean isSiteToday,
                                               List<String> portfolioIds, List<String> campaignIds,
                                               Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum, String matchType) {

        StringBuilder sql = new StringBuilder();
        sql.append("select 'sp' adType, 'target' type,any(shop_id) shopId, any(target_id) keywordId, any(campaign_id) campaignId, any(ad_group_id) adGroupId, any(match_type) matchType, any(targeting_text) keywordText  ");

        sql.append(" from ").append(getJdbcHelper().getTable());

        sql.append(" where puid = ?  ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sql.append("and marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("shop_id", shopIdList, argsList));
        }

        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sql.append(SqlStringUtil.dealDorisInList("concat_ws('|', marketplace_id, count_day)", siteToday, argsList));
            sql.append(" and count_day >= ? and count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sql.append(" and count_day >= ? and count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealDorisInList("campaign_id", campaignIds, argsList));
        }

        sql.append( " and query = ? ");
        argsList.add(queryWord);



        if (CollectionUtils.isNotEmpty(portfolioIds)) {

            sql.append(" and ");


            sql.append(" campaign_id in ( ");
            sql.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = 'sp' ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sql.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sql.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append(" )  ");
                }
            } else {
                sql.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sql.append(" ) ");
        }

        if (Boolean.TRUE.equals(noZero) && DashboardDataFieldEnum.noZeroFieldSet.contains(dashboardDataFieldEnum.getCode())) {

            sql.append(" and ");

            sql.append(" " + getColumn(dashboardDataFieldEnum.getCode()) + " <> 0 ");
        }

        if (StringUtils.isNotBlank(matchType)) {
            sql.append(" and LOWER(match_type) = ? ");
            argsList.add(matchType.toLowerCase());
        }

        sql.append("group by target_id ");
        return sql.toString();
    }

    @Override
    public List<SearchTermAggregateBO> allSearchTermAggregateDataByQueryList(Integer puid, CpcQueryWordDto dto, String startDate, String endDate,
                                                                             List<String> queryList) {
        if (CollectionUtils.isEmpty(queryList)) {
            return new ArrayList<>();
        }
        List<Object> args = new ArrayList<>();
        StringJoiner sj = new StringJoiner(" union all ");
        List<List<String>> queryPartitionList = Lists.partition(queryList, 8000);
        StringBuilder patitionSql;
        for (List<String> querys : queryPartitionList) {
            patitionSql = new StringBuilder("SELECT ")
                    .append(" IFNULL(SUM(impressions), 0) impressions, IFNULL(SUM(clicks), 0) clicks, IFNULL(SUM(cost), 0) cost, IFNULL(SUM(sale_num), 0) sale_num,")
                    .append(" IFNULL(SUM(total_sales), 0) total_sales, IFNULL(SUM(ad_sales), 0) `ad_sales`, IFNULL(SUM(total_sales) - SUM(ad_sales), 0) ad_other_sales")
                    .append(" from ").append(this.getJdbcHelper().getTable());
            patitionSql.append(" where puid=? and marketplace_id=? and count_day>=? and count_day<=?");
            args.add(puid);
            args.add(dto.getMarketplaceId());
            args.add(startDate);
            args.add(endDate);
            patitionSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", dto.getGroupIdList(), args));
            patitionSql.append(SqlStringUtil.dealInList("query", querys, args));
            sj.add(patitionSql.toString());
        }
        return getJdbcTemplate().query(sj.toString(), new BeanPropertyRowMapper<>(SearchTermAggregateBO.class), args.toArray());
    }

}

