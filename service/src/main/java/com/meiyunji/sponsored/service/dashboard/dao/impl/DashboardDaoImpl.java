package com.meiyunji.sponsored.service.dashboard.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.DorisTemplateService;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.dashboard.dao.IDashboardDao;
import com.meiyunji.sponsored.service.dashboard.dto.*;
import com.meiyunji.sponsored.service.strategy.enums.AdType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
public class DashboardDaoImpl implements IDashboardDao {
    private final DorisTemplateService dorisTemplateService;

    public DashboardDaoImpl(DorisTemplateService dorisTemplateService) {
        this.dorisTemplateService =  dorisTemplateService;
    }

    @Override
    public List<AdContributesDto> getAdContributes(
            Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate,
            LocalDate endDate, List<String> campaigns,String portfolioId,String status) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select r.count_date count_date, sum(r.cost * c.rate) cost, " +
                "sum(if(r.type='sp', r.sales7d, r.sales14d) * c.rate) sales from ods_t_amazon_ad_campaign_all_report r ");
        sql.append("  join (select * from dim_currency_rate where puid = ? and `to` = ? ) c ");
        args.add(puid);
        args.add(currency);
        sql.append(" on r.puid = c.puid and r.count_month = c.month " +
                "join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append("where r.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("r.shop_id", shopIds, args));
        if (StringUtils.isNotBlank(adType)) {
            sql.append(" and r.type = ? ");
            args.add(adType.toLowerCase());
        }
        sql.append(" and r.campaign_id in(").append(listCampaignsByCompidAndPortfidAndState(puid,shopIds,campaigns,portfolioId,status,args)).append(")");
        sql.append(" and r.is_summary = 1  and r.count_day >= ? and r.count_day <= ? ");
        args.add(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        args.add(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        sql.append("group by r.count_date ");

        return dorisTemplateService.getAdJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            AdContributesDto vo = new AdContributesDto();
            vo.setCountDate(LocalDate.parse(rs.getString("count_date"),
                    DateTimeFormatter.ofPattern("yyyyMMdd")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            vo.setCost(Optional.ofNullable(rs.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
            vo.setSales(Optional.ofNullable(rs.getBigDecimal("sales")).orElse(BigDecimal.ZERO));
            return vo;
        }, args.toArray());
    }


    @Override
    public List<AdContributesDto> getAdContributes(
            Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate,
            LocalDate endDate, List<CampaignInfoDto> campaigns) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select r.count_date count_date, sum(r.cost * c.rate) cost, " +
                "sum(if(r.type='sp', r.sales7d, r.sales14d) * c.rate) sales from ods_t_amazon_ad_campaign_all_report r ");
        sql.append("  join (select * from dim_currency_rate where puid = ? and `to` = ? ) c ");
        args.add(puid);
        args.add(currency);
        sql.append(" on r.puid = c.puid and r.count_month = c.month " +
                "join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append("where r.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("r.shop_id", shopIds, args));
        if (StringUtils.isNotBlank(adType)) {
            sql.append(" and r.type = ? ");
            args.add(adType.toLowerCase());
        }
        if (CollectionUtils.isNotEmpty(campaigns)) {
            sql.append(SqlStringUtil.dealDorisInList("r.campaign_id", campaigns.stream()
                    .map(CampaignInfoDto::getCampaignId).collect(Collectors.toList()), args));
        }
        sql.append(" and r.is_summary = 1  and r.count_day >= ? and r.count_day <= ? ");
        args.add(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        args.add(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        sql.append("group by r.count_date ");

        return dorisTemplateService.getAdJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            AdContributesDto vo = new AdContributesDto();
            vo.setCountDate(LocalDate.parse(rs.getString("count_date"),
                    DateTimeFormatter.ofPattern("yyyyMMdd")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            vo.setCost(Optional.ofNullable(rs.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
            vo.setSales(Optional.ofNullable(rs.getBigDecimal("sales")).orElse(BigDecimal.ZERO));
            return vo;
        }, args.toArray());
    }



    @Override
    public List<DashBoardCampaignDto> listCampaignsByPuidAndShopId(
            Integer puid, List<Integer> shopIds, String adType, String name, String portfolioId, String status,
            Integer pageNo, Integer pageSize) {
        StringBuilder sql = new StringBuilder("select shop_id, campaign_id, name, type " +
                " from ods_t_amazon_ad_campaign_all where puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIds, args));
        if (StringUtils.isNotBlank(adType)) {
            sql.append("and type = ? ");
            args.add(adType.toLowerCase());
        }

        if (StringUtils.isNotBlank(portfolioId)) {
            // portfolioIds筛选掉-1的情况
            List<String> portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                    .filter(item -> !item.equals("-1")).collect(Collectors.toList());
            if ("-1".equals(portfolioId)) {
                sql.append("and portfolio_id is null ");
            } else if (org.apache.commons.collections.CollectionUtils.isNotEmpty(portfolioIds)) {
                if (portfolioId.indexOf("-1") >= 0) {
                    sql.append("and (portfolio_id is null ").append(SqlStringUtil.
                            dealInListOr("portfolio_id", portfolioIds, args)).append(") ");
                } else {
                    sql.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, args));
                }
            }
        }

        if (StringUtils.isNotBlank(status)) {
            List<String> statusList = StringUtil.splitStr(status, ",");
            sql.append(SqlStringUtil.dealDorisInList("state", statusList, args));
        }
        if (StringUtils.isNotBlank(name)) {
            sql.append(" and name like ? ");
            args.add("%" + SqlStringUtil.dealLikeSql(name) + "%");
        }
        sql.append(" order by campaign_id limit " + pageSize * (pageNo - 1) + "," + pageSize);

        return dorisTemplateService.getAdJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            DashBoardCampaignDto vo = new DashBoardCampaignDto();
            vo.setCampaignId(rs.getString("campaign_id"));
            vo.setAdType(AdType.valueOf(rs.getString("type").toUpperCase()));
            vo.setName(rs.getString("name"));
            vo.setShopId(rs.getInt("shop_id"));
            return vo;
        }, args.toArray());
    }


    private String listCampaignsByCompidAndPortfidAndState(
            Integer puid, List<Integer> shopIds, List<String> campaignIds, String portfolioId, String status,List<Object> args) {
        StringBuilder sql = new StringBuilder("select campaign_id " +
                " from ods_t_amazon_ad_campaign_all where puid = ? ");

        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIds, args));

        if (campaignIds != null && campaignIds.size() > 0) {
            sql.append(SqlStringUtil.dealDorisInList("campaign_id", campaignIds, args));
        }

        if (StringUtils.isNotBlank(portfolioId)) {
            // portfolioIds筛选掉-1的情况
            List<String> portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                    .filter(item -> !item.equals("-1")).collect(Collectors.toList());
            if ("-1".equals(portfolioId)) {
                sql.append("and portfolio_id is null ");
            } else if (org.apache.commons.collections.CollectionUtils.isNotEmpty(portfolioIds)) {
                if (portfolioId.indexOf("-1") >= 0) {
                    sql.append("and (portfolio_id is null ").append(SqlStringUtil.
                            dealInListOr("portfolio_id", portfolioIds, args)).append(") ");
                } else {
                    sql.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, args));
                }
            }
        }

        if (StringUtils.isNotBlank(status)) {
            List<String> statusList = StringUtil.splitStr(status, ",");
            sql.append(SqlStringUtil.dealDorisInList("state", statusList, args));
        }

        return sql.toString();

    }

    @Override
    public Integer countAllCampaigns(Integer puid, List<Integer> shopIds, String adType, String name, String portfolioId,
                                     String status) {
        StringBuilder sql = new StringBuilder("select count(1) cnt from ods_t_amazon_ad_campaign_all where puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIds, args));
        if (StringUtils.isNotBlank(adType)) {
            sql.append("and type = ? ");
            args.add(adType.toLowerCase());
        }
        if (StringUtils.isNotBlank(portfolioId)) {
            // portfolioIds筛选掉-1的情况
            List<String> portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                    .filter(item -> !item.equals("-1")).collect(Collectors.toList());
            if ("-1".equals(portfolioId)) {
                sql.append("and portfolio_id is null ");
            } else if (org.apache.commons.collections.CollectionUtils.isNotEmpty(portfolioIds)) {
                if (portfolioId.indexOf("-1") >= 0) {
                    sql.append("and (portfolio_id is null ").append(SqlStringUtil.
                            dealInListOr("portfolio_id", portfolioIds, args)).append(") ");
                } else {
                    sql.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, args));
                }
            }
        }
        if (StringUtils.isNotBlank(status)) {
            List<String> statusList = StringUtil.splitStr(status, ",");
            sql.append(SqlStringUtil.dealInList("state", statusList, args));
        }
        if (StringUtils.isNotBlank(name)) {
            sql.append(" and name like ? ");
            args.add("%" + SqlStringUtil.dealLikeSql(name) + "%");
        }
        return Optional.ofNullable(dorisTemplateService.getAdJdbcTemplate().queryForObject(sql.toString(), args.toArray(), Integer.class)).orElse(0);
    }

    @Override
    public List<AdContributesDto> getShopSales(
            Integer puid, List<Integer> shopIds, String currency, LocalDate startDate, LocalDate endDate) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select s.now_date now_date, sum(s.sale_price * c.rate) sales from dws_sale_profit_shop_day s  join ");
        sql.append(" (select * from dim_currency_rate where puid = ? and `to` = ? ) c ");
        args.add(puid);
        args.add(currency);
        sql.append("on s.puid = c.puid and s.now_month = c.month and s.currency = c.`from` ");
        sql.append("where s.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIds, args));
        sql.append(" and s.now_date >= ? and s.now_date <= ? group by s.now_date ");
        args.add(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        args.add(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

        return dorisTemplateService.getJdbcTemplate(puid).query(sql.toString(), (rs, rowNum) -> {
            AdContributesDto dto = new AdContributesDto();
            dto.setShopSale(Optional.ofNullable(rs.getBigDecimal("sales")).orElse(BigDecimal.ZERO));
            dto.setCountDate(rs.getString("now_date"));
            return dto;
        }, args.toArray());
    }

    @Override
    public List<AdvertisingDataDto> getAdvertisingData(
            Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate,
            LocalDate endDate, List<String> campaigns, String portfolioId, String status) {

        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select r.type type, sum(r.clicks) clicks, sum(r.cost * c.rate) cost," +
                " sum(if(r.type='sp', r.sales7d, r.sales14d) * c.rate) sales, sum(if(r.type='sp', r.conversions7d, r.conversions14d)) conversions7d from ods_t_amazon_ad_campaign_all_report r ");
        sql.append("  join (select * from dim_currency_rate where puid = ? and `to` = ? ) c  ");
        args.add(puid);
        args.add(currency);
        sql.append(" on r.puid = c.puid and r.count_month = c.month join " +
                "dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append("where r.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("r.shop_id", shopIds, args));
        if (StringUtils.isNotBlank(adType)) {
            sql.append(" and r.type = ? ");
            args.add(adType.toLowerCase());
        }
        sql.append(" and r.campaign_id in(").append(listCampaignsByCompidAndPortfidAndState(puid,shopIds,campaigns,portfolioId,status,args)).append(")");
        sql.append(" and r.is_summary = 1 and r.count_day >= ? and r.count_day <= ? ");
        args.add(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        args.add(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        sql.append("group by r.type ");

        return dorisTemplateService.getAdJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            AdvertisingDataDto vo = new AdvertisingDataDto();
            vo.setType(AdType.valueOf(rs.getString("type").toUpperCase()));
            vo.setCost(Optional.ofNullable(rs.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
            vo.setSales(Optional.ofNullable(rs.getBigDecimal("sales")).orElse(BigDecimal.ZERO));
            vo.setAdOrder(rs.getLong("conversions7d"));
            vo.setClicks(rs.getLong("clicks"));
            return vo;
        }, args.toArray());
    }

    @Override
    public List<AdvertisingDataDto> getAdvertisingData(
            Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate,
            LocalDate endDate, List<CampaignInfoDto> campaigns) {

        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select r.type type, sum(r.clicks) clicks, sum(r.cost * c.rate) cost," +
                " sum(if(r.type='sp', r.sales7d, r.sales14d) * c.rate) sales, sum(if(r.type='sp', r.conversions7d, r.conversions14d)) conversions7d from ods_t_amazon_ad_campaign_all_report r ");
        sql.append("  join (select * from dim_currency_rate where puid = ? and `to` = ? ) c  ");
        args.add(puid);
        args.add(currency);
        sql.append(" on r.puid = c.puid and r.count_month = c.month join " +
                "dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append("where r.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("r.shop_id", shopIds, args));
        if (StringUtils.isNotBlank(adType)) {
            sql.append(" and r.type = ? ");
            args.add(adType.toLowerCase());
        }
        if (CollectionUtils.isNotEmpty(campaigns)) {
            sql.append(SqlStringUtil.dealDorisInList("r.campaign_id", campaigns.stream()
                    .map(CampaignInfoDto::getCampaignId).collect(Collectors.toList()), args));
        }
        sql.append(" and r.is_summary = 1 and r.count_day >= ? and r.count_day <= ? ");
        args.add(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        args.add(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        sql.append("group by r.type ");

        return dorisTemplateService.getAdJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            AdvertisingDataDto vo = new AdvertisingDataDto();
            vo.setType(AdType.valueOf(rs.getString("type").toUpperCase()));
            vo.setCost(Optional.ofNullable(rs.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
            vo.setSales(Optional.ofNullable(rs.getBigDecimal("sales")).orElse(BigDecimal.ZERO));
            vo.setAdOrder(rs.getLong("conversions7d"));
            vo.setClicks(rs.getLong("clicks"));
            return vo;
        }, args.toArray());
    }

    @Override
    public List<AdPerformanceDto> listCampaignReport(
            Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate,
            LocalDate endDate, List<CampaignInfoDto> campaigns) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select r.count_date count_date, sum(r.clicks) clicks, " +
                "sum(r.impressions) impressions, sum(r.cost * c.rate) cost, sum(if(r.type='sp', r.sales7d, r.sales14d) * c.rate) sales , " +
                "sum(if(r.type='sp', r.conversions7d, r.conversions14d)) conversions7d from ods_t_amazon_ad_campaign_all_report r ");
        sql.append("  join (select * from dim_currency_rate where puid = ? and `to` = ? ) c  ");
        args.add(puid);
        args.add(currency);
        sql.append(" on r.puid = c.puid and r.count_month = c.month " +
                "join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append("where r.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIds, args));
        if (StringUtils.isNotBlank(adType)) {
            sql.append(" and r.type = ? ");
            args.add(adType.toLowerCase());
        }
        if (CollectionUtils.isNotEmpty(campaigns)) {
            sql.append(SqlStringUtil.dealDorisInList("r.campaign_id", campaigns.stream()
                    .map(CampaignInfoDto::getCampaignId).collect(Collectors.toList()), args));
        }
        sql.append(" and r.is_summary = 1 and r.count_day >= ? and r.count_day <= ? ");
        args.add(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        args.add(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        sql.append("group by r.count_date ");

        return dorisTemplateService.getAdJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            AdPerformanceDto vo1 = new AdPerformanceDto();
            vo1.setCountDate(LocalDate.parse(rs.getString("count_date"),
                    DateTimeFormatter.ofPattern("yyyyMMdd")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            vo1.setCost(Optional.ofNullable(rs.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
            vo1.setAdSales(Optional.ofNullable(rs.getBigDecimal("sales")).orElse(BigDecimal.ZERO));
            vo1.setAdOrder(rs.getInt("conversions7d"));
            vo1.setClicks(rs.getLong("clicks"));
            vo1.setImpressions(rs.getLong("impressions"));
            return vo1;
        }, args.toArray());
    }

    @Override
    public List<AdPerformanceDto> listCampaignReport(
            Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate,
            LocalDate endDate, List<String> campaigns, String portfolioId, String status) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select r.count_date count_date, sum(r.clicks) clicks, " +
                "sum(r.impressions) impressions, sum(r.cost * c.rate) cost, sum(if(r.type='sp', r.sales7d, r.sales14d) * c.rate) sales , " +
                "sum(if(r.type='sp', r.conversions7d, r.conversions14d)) conversions7d from ods_t_amazon_ad_campaign_all_report r ");
        sql.append("  join (select * from dim_currency_rate where puid = ? and `to` = ? ) c  ");
        args.add(puid);
        args.add(currency);
        sql.append(" on r.puid = c.puid and r.count_month = c.month " +
                "join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append("where r.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIds, args));
        if (StringUtils.isNotBlank(adType)) {
            sql.append(" and r.type = ? ");
            args.add(adType.toLowerCase());
        }
        sql.append(" and r.campaign_id in(").append(listCampaignsByCompidAndPortfidAndState(puid,shopIds,campaigns,portfolioId,status,args)).append(")");
        sql.append(" and r.is_summary = 1 and r.count_day >= ? and r.count_day <= ? ");
        args.add(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        args.add(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        sql.append("group by r.count_date ");

        return dorisTemplateService.getAdJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            AdPerformanceDto vo1 = new AdPerformanceDto();
            vo1.setCountDate(LocalDate.parse(rs.getString("count_date"),
                    DateTimeFormatter.ofPattern("yyyyMMdd")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            vo1.setCost(Optional.ofNullable(rs.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
            vo1.setAdSales(Optional.ofNullable(rs.getBigDecimal("sales")).orElse(BigDecimal.ZERO));
            vo1.setAdOrder(rs.getInt("conversions7d"));
            vo1.setClicks(rs.getLong("clicks"));
            vo1.setImpressions(rs.getLong("impressions"));
            return vo1;
        }, args.toArray());
    }

    @Override
    public List<AdPerformanceDto> sumCampaignReport(
            Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate, LocalDate endDate,
            List<CampaignInfoDto> campaigns) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select sum(r.clicks) clicks, sum(r.impressions) impressions,  " +
                "sum(r.cost * c.rate) cost, sum(if(r.type='sp', r.sales7d, r.sales14d) * c.rate) sales , sum(if(r.type='sp', r.conversions7d, r.conversions14d)) conversions7d " +
                "from ods_t_amazon_ad_campaign_all_report r ");
        sql.append("  join (select * from dim_currency_rate where puid = ? and `to` = ? ) c  ");
        args.add(puid);
        args.add(currency);
        sql.append(" on r.puid = c.puid and r.count_month = c.month " +
                "join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append("where r.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("r.shop_id", shopIds, args));
        if (StringUtils.isNotBlank(adType)) {
            sql.append(" and r.type = ? ");
            args.add(adType.toLowerCase());
        }
        if (CollectionUtils.isNotEmpty(campaigns)) {
            sql.append(SqlStringUtil.dealDorisInList("r.campaign_id", campaigns.stream()
                    .map(CampaignInfoDto::getCampaignId).collect(Collectors.toList()), args));
        }
        sql.append(" and r.is_summary = 1 and r.count_day >= ? and r.count_day <= ? ");
        args.add(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        args.add(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));


        return dorisTemplateService.getAdJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            AdPerformanceDto vo2 = new AdPerformanceDto();
            vo2.setCost(Optional.ofNullable(rs.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
            vo2.setAdSales(Optional.ofNullable(rs.getBigDecimal("sales")).orElse(BigDecimal.ZERO));
            vo2.setAdOrder(rs.getInt("conversions7d"));
            vo2.setClicks(rs.getLong("clicks"));
            vo2.setImpressions(rs.getLong("impressions"));
            return vo2;
        }, args.toArray());
    }

    @Override
    public List<AdPerformanceDto> sumCampaignReport(
            Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate, LocalDate endDate,
            List<String> campaigns, String portfolioId, String status) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select sum(r.clicks) clicks, sum(r.impressions) impressions,  " +
                "sum(r.cost * c.rate) cost, sum(if(r.type='sp', r.sales7d, r.sales14d) * c.rate) sales , sum(if(r.type='sp', r.conversions7d, r.conversions14d)) conversions7d " +
                "from ods_t_amazon_ad_campaign_all_report r ");
        sql.append("  join (select * from dim_currency_rate where puid = ? and `to` = ? ) c  ");
        args.add(puid);
        args.add(currency);
        sql.append(" on r.puid = c.puid and r.count_month = c.month " +
                "join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append("where r.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("r.shop_id", shopIds, args));
        if (StringUtils.isNotBlank(adType)) {
            sql.append(" and r.type = ? ");
            args.add(adType.toLowerCase());
        }
        sql.append(" and r.campaign_id in(").append(listCampaignsByCompidAndPortfidAndState(puid,shopIds,campaigns,portfolioId,status,args)).append(")");
        sql.append(" and r.is_summary = 1 and r.count_day >= ? and r.count_day <= ? ");
        args.add(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        args.add(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));


        return dorisTemplateService.getAdJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            AdPerformanceDto vo2 = new AdPerformanceDto();
            vo2.setCost(Optional.ofNullable(rs.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
            vo2.setAdSales(Optional.ofNullable(rs.getBigDecimal("sales")).orElse(BigDecimal.ZERO));
            vo2.setAdOrder(rs.getInt("conversions7d"));
            vo2.setClicks(rs.getLong("clicks"));
            vo2.setImpressions(rs.getLong("impressions"));
            return vo2;
        }, args.toArray());
    }

    @Override
    public List<CampaignTopTenDto> getCampaignTopTen(
            Integer puid, List<Integer> shopIds, String adType, List<String> campaignIds, String currency,
            String orderBy, LocalDate startDate, LocalDate endDate) {

        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select * from (select r.shop_id, r.campaign_id, " +
                "r.type type, r.campaign_name, sum(r.cost * c.rate) cost, sum(if(r.type='sp', r.sales7d, r.sales14d) * c.rate) sales " +
                "from ods_t_amazon_ad_campaign_all_report r ");
        sql.append("  join (select * from dim_currency_rate where puid = ? and `to` = ? ) c ");
        args.add(puid);
        args.add(currency);
        sql.append(" on r.puid = c.puid and r.count_month = c.month " +
                " join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append("where r.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("r.shop_id", shopIds, args));
        if (StringUtils.isNotBlank(adType)) {
            sql.append(" and r.type = ? ");
            args.add(adType.toLowerCase());
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealDorisInList("r.campaign_id", campaignIds, args));
        }
        sql.append(" and r.is_summary = 1 and r.count_day >= ? and r.count_day <= ? ");
        args.add(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        args.add(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        sql.append(" group by r.shop_id, r.type, r.campaign_id, r.campaign_name) t order by ");
        sql.append(orderBy);
        sql.append(" desc limit 10 ");

        //查询所有店铺广告花费和广告销售额
        return dorisTemplateService.getAdJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            CampaignTopTenDto vo = new CampaignTopTenDto();
            vo.setCampaignName(rs.getString("campaign_name"));
            vo.setShopId(rs.getInt("shop_id"));
            vo.setCampaignId(rs.getString("campaign_id"));
            vo.setCost(Optional.ofNullable(rs.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
            vo.setSales(Optional.ofNullable(rs.getBigDecimal("sales")).orElse(BigDecimal.ZERO));
            vo.setAdType(rs.getString("type"));
            return vo;
        }, args.toArray());
    }

    @Override
    public List<CampaignTopTenDto> getCampaignTopTen(
            Integer puid, List<Integer> shopIds, String adType, List<String> campaigns, String portfolioId, String status, String currency,
            String orderBy, LocalDate startDate, LocalDate endDate) {

        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select * from (select r.shop_id, r.campaign_id, " +
                "r.type type, r.campaign_name, sum(r.cost * c.rate) cost, sum(if(r.type='sp', r.sales7d, r.sales14d) * c.rate) sales " +
                "from ods_t_amazon_ad_campaign_all_report r ");
        sql.append("  join (select * from dim_currency_rate where puid = ? and `to` = ? ) c ");
        args.add(puid);
        args.add(currency);
        sql.append(" on r.puid = c.puid and r.count_month = c.month " +
                " join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append("where r.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("r.shop_id", shopIds, args));
        if (StringUtils.isNotBlank(adType)) {
            sql.append(" and r.type = ? ");
            args.add(adType.toLowerCase());
        }
        sql.append(" and r.campaign_id in(").append(listCampaignsByCompidAndPortfidAndState(puid,shopIds,campaigns,portfolioId,status,args)).append(")");
        sql.append(" and r.is_summary = 1 and r.count_day >= ? and r.count_day <= ? ");
        args.add(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        args.add(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        sql.append(" group by r.shop_id, r.type, r.campaign_id, r.campaign_name) t order by ");
        sql.append(orderBy);
        sql.append(" desc limit 10 ");

        //查询所有店铺广告花费和广告销售额
        return dorisTemplateService.getAdJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            CampaignTopTenDto vo = new CampaignTopTenDto();
            vo.setCampaignName(rs.getString("campaign_name"));
            vo.setShopId(rs.getInt("shop_id"));
            vo.setCampaignId(rs.getString("campaign_id"));
            vo.setCost(Optional.ofNullable(rs.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
            vo.setSales(Optional.ofNullable(rs.getBigDecimal("sales")).orElse(BigDecimal.ZERO));
            vo.setAdType(rs.getString("type"));
            return vo;
        }, args.toArray());
    }

    @Override
    public List<CampaignTopTenDto> listSumCampaignPerformance(
            Integer puid, List<Integer> shopIds,
            String currency, LocalDate startDate, LocalDate endDate) {
        if (CollectionUtils.isEmpty(shopIds)) {
            return new ArrayList<>();
        }

        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select r.shop_id shop_id, sum(r.cost * c.rate) cost, sum(if(r.type='sp', r.sales7d, r.sales14d) * c.rate) sales" +
                " from ods_t_amazon_ad_campaign_all_report r ");
        sql.append("  join (select * from dim_currency_rate where puid = ? and `to` = ? ) c  ");
        args.add(puid);
        args.add(currency);
        sql.append(" on r.puid = c.puid and r.count_month = c.month join " +
                "dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append("where r.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("r.shop_id", shopIds, args));
        sql.append(" and r.is_summary = 1 and r.count_day >= ? and r.count_day <= ? group by r.shop_id ");
        args.add(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        args.add(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

        //查询所有店铺广告花费和广告销售额
        return dorisTemplateService.getAdJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
                    CampaignTopTenDto dto = new CampaignTopTenDto();
                    dto.setShopId(rs.getInt("shop_id"));
                    dto.setTotalCost(Optional.ofNullable(rs.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
                    dto.setTotalSales(Optional.ofNullable(rs.getBigDecimal("sales")).orElse(BigDecimal.ZERO));
                    return dto;
                }, args.toArray());
    }

    @Override
    public List<UserCurrencyRateDto> listCurrencyRateByPuidAndMonth(int puid, String startMonth, String endMonth) {
        StringBuilder sql = new StringBuilder("select puid, month, `from`, `to`, `rate` from dim_currency_rate where " +
                "puid = ? and month >= ? and month <= ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(startMonth);
        args.add(endMonth);

        return dorisTemplateService.getAdJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            UserCurrencyRateDto dto = new UserCurrencyRateDto();
            dto.setPuid(rs.getInt("puid"));
            dto.setMonth(rs.getString("month"));
            dto.setFrom(rs.getString("from"));
            dto.setTo(rs.getString("to"));
            dto.setRate(rs.getBigDecimal("rate"));
            return dto;
        }, args.toArray());
    }

    @Override
    public List<UserCurrencyRateDto> coverRateListByCurrency(int puid, String startMonth, String endMonth, String currency) {
        StringBuilder sql = new StringBuilder("select puid, month, `from`, `to`, `rate` from dim_currency_rate where " +
                "puid = ? and month >= ? and month <= ? and `to` = ?");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(startMonth);
        args.add(endMonth);
        args.add(currency);

        return dorisTemplateService.getAdJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            UserCurrencyRateDto dto = new UserCurrencyRateDto();
            dto.setPuid(rs.getInt("puid"));
            dto.setMonth(rs.getString("month"));
            dto.setFrom(rs.getString("from"));
            dto.setTo(rs.getString("to"));
            dto.setRate(rs.getBigDecimal("rate"));
            return dto;
        }, args.toArray());
    }

    @Override
    public List<AdQueryWordDto> listSpQueryWord(
            Integer puid, LocalDate start, LocalDate end, String currency, String orderByField, List<AdQueryTop5Dto> list) {
        List<Object> args = new ArrayList<>();
        List<String> sqlList = new ArrayList<>(list.size());

        for (int i = 0; i < list.size(); i++) {
            AdQueryTop5Dto dto = list.get(i);
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT 'sp' as ad_type, 'sp_query_word' as query_type, r.`query` query, r.campaign_id campaign_id, " +
                    "r.campaign_name campaign_name, r.shop_id shop_id,  r.marketplace_id marketplace_id, r.ad_group_id ad_group_id, sum(r.clicks) clicks," +
                    " sum(r.impressions) impressions, sum(r.cost * c.rate) cost, sum(r.sale_num) sale_num,  " +
                    "sum(r.total_sales  * c.rate) sales ");
            sql.append("FROM   ods_t_cpc_query_keyword_report r join (select * from dim_currency_rate " +
                    "where puid = ? and `to` = ? ) c " +
                    "  on r.puid = c.puid and r.count_month = c.month " +
                    "join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency   WHERE ");
            sql.append(" r.puid  = ? AND  r.shop_id  = ? AND campaign_id = ? AND r.count_day >= ? AND r.count_day <= ? ");
            sql.append("group by r.shop_id,r.marketplace_id, r.campaign_id, r.ad_group_id, r.campaign_name, r.query ");
            sql.append("order by ").append(orderByField).append(" desc limit 5 ");
            args.add(puid);
            args.add(currency);
            args.add(puid);
            args.add(dto.getShopId());
            args.add(dto.getCampaignId());
            args.add(start.toString());
            args.add(end.toString());
            sqlList.add(sql.toString());
        }

        StringBuilder unionSql = new StringBuilder();
        for (int i = 0; i < sqlList.size(); i++) {
            String str = sqlList.get(i);
            unionSql.append("(").append(str).append(")");
            if (i < sqlList.size() - 1) {
                unionSql.append(" union all ");
            }
        }

        return dorisTemplateService.getAdJdbcTemplate().query(unionSql.toString(), (rs, rowNum) -> {
            AdQueryWordDto report = new AdQueryWordDto();
            report.setQuery(rs.getString("query"));
            report.setAdType(rs.getString("ad_type"));
            report.setQueryType(rs.getString("query_type"));
            report.setCampaignName(rs.getString("campaign_name"));
            report.setShopId(rs.getInt("shop_id"));
            report.setMarketplaceId(rs.getString("marketplace_id"));
            report.setCampaignId(rs.getString("campaign_id"));
            report.setAdGroupId(rs.getString("ad_group_id"));
            report.setClicks((long) rs.getInt("clicks"));
            report.setImpressions((long) rs.getInt("impressions"));
            report.setCost(Optional.ofNullable(rs.getBigDecimal("cost"))
                    .orElse(BigDecimal.ZERO));
            report.setAdOrder(rs.getInt("sale_num"));
            report.setAdSales(Optional.ofNullable(rs.getBigDecimal("sales"))
                    .orElse(BigDecimal.ZERO));
            return report;
        }, args.toArray());
    }

    @Override
    public List<AdQueryWordDto> listSpQueryTargeting(
            Integer puid, LocalDate start, LocalDate end, String currency, String orderByField, List<AdQueryTop5Dto> list) {
        List<Object> args = new ArrayList<>();
        List<String> sqlList = new ArrayList<>(list.size());

        for (int i = 0; i < list.size(); i++) {
            AdQueryTop5Dto dto = list.get(i);
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT 'sp' as ad_type, if(`is_asin` = 1, 'sp_query_targeting', 'sp_query_word') as query_type, r.`query` query, r.campaign_id campaign_id, " +
                    "r.campaign_name campaign_name,  r.shop_id shop_id,  r.marketplace_id marketplace_id, r.ad_group_id ad_group_id, sum(r.clicks) clicks," +
                    " sum(r.impressions) impressions," +
                    " sum(r.cost * c.rate) cost, sum(r.sale_num) sale_num,  sum(r.total_sales  * c.rate) sales ");
            sql.append("FROM   ods_t_cpc_query_targeting_report r join (select * from dim_currency_rate " +
                    "where puid = ? and `to` = ? ) c  " +
                    "  on r.puid = c.puid and r.count_month = c.month join " +
                    "dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency  WHERE ");
            sql.append(" r.puid  = ? AND  r.shop_id  = ? AND campaign_id = ? AND r.count_day >= ? AND r.count_day <= ? ");
            sql.append("group by r.shop_id,r.marketplace_id, r.campaign_id, r.ad_group_id, r.campaign_name, r.query, r.is_asin ");
            sql.append("order by ").append(orderByField).append(" desc limit 5 ");
            args.add(puid);
            args.add(currency);
            args.add(puid);
            args.add(dto.getShopId());
            args.add(dto.getCampaignId());
            args.add(start.toString());
            args.add(end.toString());
            sqlList.add(sql.toString());
        }

        StringBuilder unionSql = new StringBuilder();
        for (int i = 0; i < sqlList.size(); i++) {
            String str = sqlList.get(i);
            unionSql.append("(").append(str).append(")");
            if (i < sqlList.size() - 1) {
                unionSql.append(" union all ");
            }
        }

        return dorisTemplateService.getAdJdbcTemplate().query(unionSql.toString(), (rs, rowNum) -> {
            AdQueryWordDto report = new AdQueryWordDto();
            report.setQuery(rs.getString("query"));
            report.setAdType(rs.getString("ad_type"));
            report.setQueryType(rs.getString("query_type"));
            report.setCampaignName(rs.getString("campaign_name"));
            report.setCampaignId(rs.getString("campaign_id"));
            report.setAdGroupId(rs.getString("ad_group_id"));
            report.setShopId(rs.getInt("shop_id"));
            report.setMarketplaceId(rs.getString("marketplace_id"));
            report.setClicks((long) rs.getInt("clicks"));
            report.setImpressions((long) rs.getInt("impressions"));
            report.setCost(Optional.ofNullable(rs.getBigDecimal("cost"))
                    .orElse(BigDecimal.ZERO));
            report.setAdOrder(rs.getInt("sale_num"));
            report.setAdSales(Optional.ofNullable(rs.getBigDecimal("sales"))
                    .orElse(BigDecimal.ZERO));
            return report;
        }, args.toArray());
    }

    @Override
    public List<AdQueryWordDto> listSbQueryWord(
            Integer puid, LocalDate start, LocalDate end, String currency, String orderByField, List<AdQueryTop5Dto> list) {
        List<Object> args = new ArrayList<>();
        List<String> sqlList = new ArrayList<>(list.size());

        for (int i = 0; i < list.size(); i++) {
            AdQueryTop5Dto dto = list.get(i);
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT 'sb' as ad_type, 'sb_query_word' as query_type, r.`query` query, r.campaign_id campaign_id, " +
                    "r.campaign_name campaign_name, r.shop_id shop_id, r.marketplace_id marketplace_id, r.ad_group_id ad_group_id,  sum(r.clicks) clicks," +
                    " sum(r.impressions) impressions," +
                    " sum(r.cost * c.rate) cost, sum(r.conversions14d) order_num,  sum(r.sales14d  * c.rate) sales ");
            sql.append("FROM ods_t_cpc_sb_query_keyword_report r " +
                    "join (select * from dim_currency_rate where puid = ? and `to` = ? ) c  " +
                    " on r.puid = c.puid and r.count_month = c.month " +
                    "join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency   WHERE ");
            sql.append(" r.puid  = ? AND  r.shop_id  = ? AND campaign_id = ? AND r.count_day >= ? AND r.count_day <= ? ");
            sql.append("group by r.shop_id,r.marketplace_id, r.campaign_id, r.ad_group_id, r.campaign_name, r.query ");
            sql.append("order by ").append(orderByField).append(" desc limit 5 ");
            args.add(puid);
            args.add(currency);
            args.add(puid);
            args.add(dto.getShopId());
            args.add(dto.getCampaignId());
            args.add(start.toString());
            args.add(end.toString());
            sqlList.add(sql.toString());
        }

        StringBuilder unionSql = new StringBuilder();
        for (int i = 0; i < sqlList.size(); i++) {
            String str = sqlList.get(i);
            unionSql.append("(").append(str).append(")");
            if (i < sqlList.size() - 1) {
                unionSql.append(" union all ");
            }
        }

        return dorisTemplateService.getAdJdbcTemplate().query(unionSql.toString(), (rs, rowNum) -> {
            AdQueryWordDto report = new AdQueryWordDto();
            report.setQuery(rs.getString("query"));
            report.setAdType(rs.getString("ad_type"));
            report.setQueryType(rs.getString("query_type"));
            report.setCampaignName(rs.getString("campaign_name"));
            report.setCampaignId(rs.getString("campaign_id"));
            report.setAdGroupId(rs.getString("ad_group_id"));
            report.setShopId(rs.getInt("shop_id"));
            report.setMarketplaceId(rs.getString("marketplace_id"));
            report.setClicks((long) rs.getInt("clicks"));
            report.setImpressions((long) rs.getInt("impressions"));
            report.setCost(Optional.ofNullable(rs.getBigDecimal("cost"))
                    .orElse(BigDecimal.ZERO));
            report.setAdOrder(rs.getInt("order_num"));
            report.setAdSales(Optional.ofNullable(rs.getBigDecimal("sales"))
                    .orElse(BigDecimal.ZERO));
            return report;
        }, args.toArray());
    }


    @Override
    public List<UserCurrencyRateDto> listCurrencyRateByPuidAndMonthAndFromAndTo(int puid, String startMonth, String endMonth, List<String> from, String to) {
        StringBuilder sql = new StringBuilder("select puid, month, `from`, `to`, `rate` from dim_currency_rate where " +
                "puid = ? and month >= ? and month <= ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(startMonth);
        args.add(endMonth);
        if(StringUtils.isNotBlank(to)){
            sql.append(" and `to` = ? ");
            args.add(to);
        }

        if(CollectionUtils.isNotEmpty(from)){
            sql.append(SqlStringUtil.dealInList("`from`",from,args));
        }

        return dorisTemplateService.getAdJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            UserCurrencyRateDto dto = new UserCurrencyRateDto();
            dto.setPuid(rs.getInt("puid"));
            dto.setMonth(rs.getString("month"));
            dto.setFrom(rs.getString("from"));
            dto.setTo(rs.getString("to"));
            dto.setRate(rs.getBigDecimal("rate"));
            return dto;
        }, args.toArray());
    }

    /**
     * @param puid
     * @param shopIds
     * @param adType
     * @param currency
     * @param orderBy
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public List<WxCampaignTopTenDto> getWxCampaignTopTen(
            Integer puid, List<Integer> shopIds, String adType, String currency,
            String orderBy, LocalDate startDate, LocalDate endDate) {

        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select r.marketplace_id,");
        sql.append(" r.shop_id,r.campaign_id,r.`type`,r.campaign_name,sum(r.cost * c.rate) cost,");
        sql.append(" sum(if(r.`type` = 'sp', r.conversions7d, r.conversions14d)) adOrder,");
        sql.append(" sum(if(r.`type` = 'sp', r.sales7d, r.sales14d) * c.rate) adSales,");
        sql.append(" COALESCE((sum(r.cost * c.rate) * 100) / (sum(if(r.`type` = 'sp', r.sales7d, r.sales14d) * c.rate)), 0) `acos`,");
        sql.append(" COALESCE(sum(if(r.`type` = 'sp', r.sales7d, r.sales14d) * c.rate) / sum(r.clicks), 0) `spc`,");
        sql.append(" COALESCE(sum(r.cost * c.rate) / sum(r.clicks), 0) `cpc`,");
        sql.append(" COALESCE(sum(if(r.`type` = 'sp', r.conversions7d, r.conversions14d))* 100 / sum(r.clicks), 0) `salesConversionRate`,");
        sql.append(" sum(r.impressions) impressions,sum(r.clicks) clicks,COALESCE(sum(r.clicks) * 100 / sum(r.impressions), 0) clickRate ");
        sql.append(" from");
        sql.append(" ods_t_amazon_ad_campaign_all_report r,");
        sql.append(" dim_currency_rate c,");
        sql.append(" dim_marketplace_info m ");
        sql.append(" where r.puid = c.puid and r.count_month = c.`month` ");
        sql.append(" and m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append(" and c.`to` = ? and r.puid = ? ");
        args.add(currency);
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("r.shop_id", shopIds, args));
        if (StringUtils.isNotBlank(adType)) {
            sql.append(" and r.type = ? ");
            args.add(adType.toLowerCase());
        }
        sql.append(" and r.is_summary = 1 and r.count_day >= ? and r.count_day <= ? ");
        args.add(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        args.add(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        sql.append(" group by r.marketplace_id, r.shop_id, r.type, r.campaign_id, r.campaign_name order by ");
        sql.append(orderBy);
        sql.append(" desc limit 10 ");

        //查询所有店铺广告花费和广告销售额
        return dorisTemplateService.getAdJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            WxCampaignTopTenDto vo = new WxCampaignTopTenDto();
            vo.setCampaignId(rs.getString("campaign_id"));
            vo.setCampaignName(rs.getString("campaign_name"));
            vo.setShopId(rs.getInt("shop_id"));
            vo.setMarketplaceId(rs.getString("marketplace_id"));
            vo.setAdType(rs.getString("type"));
            // 效果数据
            vo.setCost(Optional.ofNullable(rs.getBigDecimal("cost")).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP));
            vo.setAdOrder(rs.getInt("adOrder"));
            vo.setAdSales(Optional.ofNullable(rs.getBigDecimal("adSales")).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP));
            vo.setAcos(rs.getBigDecimal("acos").setScale(2, RoundingMode.HALF_UP));
            // 转化数据
            vo.setSpc(rs.getBigDecimal("spc").setScale(2, RoundingMode.HALF_UP));
            vo.setCpc(rs.getBigDecimal("cpc").setScale(2, RoundingMode.HALF_UP));
            vo.setSalesConversionRate(rs.getBigDecimal("salesConversionRate").setScale(2, RoundingMode.HALF_UP));
            // 展示数据
            vo.setImpressions(rs.getLong("impressions"));
            vo.setClicks(rs.getLong("clicks"));
            vo.setClickRate(rs.getBigDecimal("clickRate").setScale(2, RoundingMode.HALF_UP));
            return vo;
        }, args.toArray());
    }

    @Override
    public List<DashBoardCampaignDto> listCampaignsByCompidAndPortfidAndState(
            Integer puid, List<Integer> shopIds, List<String> campaignIds, String portfolioId, String status) {
        StringBuilder sql = new StringBuilder("select shop_id, campaign_id, name, type " +
                " from ods_t_amazon_ad_campaign_all where puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIds, args));

        if (campaignIds != null && campaignIds.size() > 0) {
            sql.append(SqlStringUtil.dealDorisInList("campaign_id", campaignIds, args));
        }

        if (StringUtils.isNotBlank(portfolioId)) {
            // portfolioIds筛选掉-1的情况
            List<String> portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                    .filter(item -> !item.equals("-1")).collect(Collectors.toList());
            if ("-1".equals(portfolioId)) {
                sql.append("and portfolio_id is null ");
            } else if (org.apache.commons.collections.CollectionUtils.isNotEmpty(portfolioIds)) {
                if (portfolioId.indexOf("-1") >= 0) {
                    sql.append("and (portfolio_id is null ").append(SqlStringUtil.
                            dealInListOr("portfolio_id", portfolioIds, args)).append(") ");
                } else {
                    sql.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, args));
                }
            }
        }

        if (StringUtils.isNotBlank(status)) {
            List<String> statusList = StringUtil.splitStr(status, ",");
            sql.append(SqlStringUtil.dealDorisInList("state", statusList, args));
        }

        return dorisTemplateService.getAdJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            DashBoardCampaignDto vo = new DashBoardCampaignDto();
            vo.setCampaignId(rs.getString("campaign_id"));
            vo.setAdType(AdType.valueOf(rs.getString("type").toUpperCase()));
            vo.setName(rs.getString("name"));
            vo.setShopId(rs.getInt("shop_id"));
            return vo;
        }, args.toArray());
    }
}
