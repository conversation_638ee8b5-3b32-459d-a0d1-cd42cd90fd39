package com.meiyunji.sponsored.service.reportHour.service.impl;

import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sellfox.ams.api.entry.HourlyReportDataPb;
import com.meiyunji.sellfox.ams.api.service.*;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.nacos.AdManageLimitConfig;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdProductPerspectiveBO;
import com.meiyunji.sponsored.service.cpc.bo.FeedTargetCampaignDto;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.CampaignHourlyReportSelectDto;
import com.meiyunji.sponsored.service.cpc.dto.FeedHourlySelectDTO;
import com.meiyunji.sponsored.service.cpc.dto.TargetReportHourlyDTO;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdFeedReportService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProduct;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSbTargetingReport;
import com.meiyunji.sponsored.service.cpc.po.CpcTargetingReport;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.vo.TargetHourParam;
import com.meiyunji.sponsored.service.doris.dao.*;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdSbTargetingReport;
import com.meiyunji.sponsored.service.doris.po.OdsCpcTargetingReport;
import com.meiyunji.sponsored.service.enums.AudienceCategoryTypeEnum;
import com.meiyunji.sponsored.service.multiple.common.utils.MultipleUtils;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.handler.ViewManageHandler;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.AudienceTargetViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.TargetViewHourParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.TargetViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.ViewBaseParam;
import com.meiyunji.sponsored.service.reportHour.constants.HourConvert;
import com.meiyunji.sponsored.service.reportHour.service.IAmazonAdTargetHourReportService;
import com.meiyunji.sponsored.service.reportHour.utils.AggregationDataUtil;
import com.meiyunji.sponsored.service.reportHour.utils.ReportChartUtil;
import com.meiyunji.sponsored.service.reportHour.vo.AdKeywordAndTargetHourVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdKeywordTargetHourOfAdVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdKeywordTargetHourOfPlacementVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AmazonAdTargetHourReportServiceImpl implements IAmazonAdTargetHourReportService {
    private final IScVcShopAuthDao shopAuthDao;
    private final AmsApiGrpc.AmsApiBlockingStub adFeedBlockingStub;
    private final IAmazonAdProductDao amazonAdProductDao;
    private final ICpcTargetingReportDao cpcTargetingReportDao;
    private final IAmazonAdSdTargetingReportDao amazonAdSdTargetingReportDao;
    private final IAmazonAdSbTargetingReportDao amazonAdSbTargetingReportDao;
    private final IAmazonMarketingStreamDataDao amazonMarketingStreamDataDao;
    private final IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    private final IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    private final IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    private final IAmazonSdAdGroupDao amazonSdAdGroupDao;
    private final CpcShopDataService cpcShopDataService;
    private final IAmazonAdFeedReportService amazonAdFeedReportService;
    private final IOdsAmazonAdProductDao odsAmazonAdProductDao;
    private final IOdsAmazonAdProductReportDao odsAmazonAdProductReportDao;
    private final ViewManageHandler viewManageHandler;

    @Resource
    private MultiThreadQueryAndMergeUtil multiThreadQueryAndMergeUtil;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Resource
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Resource
    private AdManageLimitConfig adManageLimitConfig;

    @Resource
    private IOdsCpcTargetingReportDao odsCpcTargetingReportDao;
    @Resource
    private IOdsAmazonAdSbTargetingReportDao odsAmazonAdSbTargetingReportDao;
    @Resource
    private IOdsAmazonAdSdTargetingReportDao odsAmazonAdSdTargetingReportDao;
    @Resource
    private IDwsSaleProfitShopDayDao dwsSaleProfitShopDayDao;

    public AmazonAdTargetHourReportServiceImpl(
            IScVcShopAuthDao shopAuthDao,
            @Qualifier("adFeedBlockingStub") AmsApiGrpc.AmsApiBlockingStub adFeedBlockingStub,
            IAmazonAdProductDao amazonAdProductDao,
            ICpcTargetingReportDao cpcTargetingReportDao,
            IAmazonAdSdTargetingReportDao amazonAdSdTargetingReportDao,
            IAmazonAdSbTargetingReportDao amazonAdSbTargetingReportDao,
            IAmazonMarketingStreamDataDao amazonMarketingStreamDataDao,
            IAmazonSdAdTargetingDao amazonSdAdTargetingDao,
            IAmazonAdCampaignAllDao amazonAdCampaignAllDao,
            IAmazonSbAdTargetingDao amazonSbAdTargetingDao,
            IAmazonSdAdGroupDao amazonSdAdGroupDao,
            CpcShopDataService cpcShopDataService,
            IAmazonAdFeedReportService amazonAdFeedReportService,
            IOdsAmazonAdProductDao odsAmazonAdProductDao,
            IOdsAmazonAdProductReportDao odsAmazonAdProductReportDao,
            ViewManageHandler viewManageHandler) {
        this.shopAuthDao = shopAuthDao;
        this.adFeedBlockingStub = adFeedBlockingStub;
        this.amazonAdProductDao = amazonAdProductDao;
        this.cpcTargetingReportDao = cpcTargetingReportDao;
        this.amazonAdSdTargetingReportDao = amazonAdSdTargetingReportDao;
        this.amazonAdSbTargetingReportDao = amazonAdSbTargetingReportDao;
        this.amazonMarketingStreamDataDao = amazonMarketingStreamDataDao;
        this.amazonSdAdTargetingDao = amazonSdAdTargetingDao;
        this.amazonAdCampaignAllDao = amazonAdCampaignAllDao;
        this.amazonSbAdTargetingDao = amazonSbAdTargetingDao;
        this.amazonSdAdGroupDao = amazonSdAdGroupDao;
        this.cpcShopDataService = cpcShopDataService;
        this.amazonAdFeedReportService = amazonAdFeedReportService;
        this.odsAmazonAdProductDao = odsAmazonAdProductDao;
        this.odsAmazonAdProductReportDao = odsAmazonAdProductReportDao;
        this.viewManageHandler = viewManageHandler;
    }

    /**
     * 查询投放小时级数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordAndTargetHourVo> getList(int puid, TargetHourParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        selectDto.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
        selectDto.setMarketplaceId(shopAuth.getMarketplaceId());
        selectDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), param.getStartDate()));
        selectDto.setEndDate(param.getEndDate());
        List<Integer> weekdayList = Optional.of(param.getWeeks()).filter(StringUtils::isNotBlank).map(w -> StringUtil.splitInt(w, ",")).orElse(HourConvert.weeKs);
        selectDto.setWeekdayList(weekdayList);
        if (StringUtils.isNotBlank(param.getTargetId())) {
            selectDto.setItemIds(Lists.newArrayList(param.getTargetId()));
        }
        boolean returnEmptyList = false;
        if (StringUtils.isNotBlank(param.getFindType()) && StringUtils.isNotBlank(param.getFindValue())) {
            List<String> adIdList = odsAmazonAdProductReportDao.adIdListByProduct(puid, Collections.singletonList(shopAuth.getId()), shopAuth.getMarketplaceId(), param.getFindType(), param.getFindValue(), selectDto.getStartDate(), selectDto.getEndDate());
            if (CollectionUtils.isEmpty(adIdList)) {
                returnEmptyList = true;
            }
            selectDto.setAdIds(adIdList);
        }

        List<AmazonMarketingStreamData> statisticsByHourResponse = (returnEmptyList ? new ArrayList<>() : amazonMarketingStreamDataDao.statisticsTargetHourlyReport(selectDto));
        List<AmazonMarketingStreamData> compareResponse = null;
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = new HashMap<>();
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            selectDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), param.getStartDateCompare()));
            selectDto.setEndDate(param.getEndDateCompare());
            boolean returnCompareEmptyList = false;
            if (StringUtils.isNotBlank(param.getFindType()) && StringUtils.isNotBlank(param.getFindValue())) {
                List<String> adIdList = odsAmazonAdProductReportDao.adIdListByProduct(puid, Collections.singletonList(shopAuth.getId()), shopAuth.getMarketplaceId(), param.getFindType(), param.getFindValue(), selectDto.getStartDate(), selectDto.getEndDate());
                if (CollectionUtils.isEmpty(adIdList)) {
                    returnCompareEmptyList = true;
                }
                selectDto.setAdIds(adIdList);
            }
            compareResponse = (returnCompareEmptyList ? new ArrayList<>() : amazonMarketingStreamDataDao.statisticsTargetHourlyReport(selectDto));
            hourlyReportDataCompareMap = getIntegerHourlyReportDataMap(compareResponse, hourlyReportDataCompareMap);
        }
        hourlyReportDataMap = getIntegerHourlyReportDataMap(statisticsByHourResponse, hourlyReportDataMap);
        List<AdKeywordAndTargetHourVo> voList = new ArrayList<>(24);

        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdKeywordAndTargetHourVo adTargetHourVo = handleVo(hour, hourlyReportDataMap.get(hour),
                    hourlyReportDataCompareMap.get(hour));
            voList.add(adTargetHourVo);
        }
        return voList;
    }

    /**
     * 查询投放竞价小时级数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordAndTargetHourVo> getBidList(int puid, TargetHourParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        CampaignHourlyReportSelectDto queryDto = new CampaignHourlyReportSelectDto();
        queryDto.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
        queryDto.setMarketplaceId(shopAuth.getMarketplaceId());
        queryDto.setStartDate(param.getStartDate());
        queryDto.setEndDate(param.getEndDate());
        if (StringUtils.isNotBlank(param.getTargetId())) {
            queryDto.setItemIds(Collections.singletonList(param.getTargetId()));
        }
        boolean returnEmptyList = false;
        if (StringUtils.isNotBlank(param.getFindType()) && StringUtils.isNotBlank(param.getFindValue())) {
            List<String> adIdList = odsAmazonAdProductReportDao.adIdListByProduct(puid, Collections.singletonList(shopAuth.getId()), shopAuth.getMarketplaceId(), param.getFindType(), param.getFindValue(), param.getStartDate(), param.getEndDate());
            if (CollectionUtils.isEmpty(adIdList)) {
                returnEmptyList = true;
            }
            queryDto.setAdIds(adIdList);
        }

        List<AmazonMarketingStreamData> statisticsByHourResponse = (returnEmptyList ? new ArrayList<>() : amazonMarketingStreamDataDao.getHourReportByKeywordId(queryDto));
        GetHourReportByKeywordIdResponsePb.GetHourReportByKeywordIdResponse compareResponse = null;
        Map<String, AmazonMarketingStreamData> hourlyReportDataMap = new HashMap<>();
        hourlyReportDataMap = getBidHourlyReportDataMap(statisticsByHourResponse, hourlyReportDataMap);
        List<AdKeywordAndTargetHourVo> voList = new ArrayList<>();

        String startDate = param.getStartDate();
        String endDate = param.getEndDate();
        // 日期格式化
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            // 起始日期
            Date d1 = sdf.parse(startDate);
            // 结束日期
            Date d2 = sdf.parse(endDate);
            Date tmp = d1;
            Calendar dd = Calendar.getInstance();
            dd.setTime(d1);
            while (tmp.getTime() <= d2.getTime()) {
                Date finalTmp = tmp;
                Map<String, AmazonMarketingStreamData> finalHourlyReportDataMap =
                        hourlyReportDataMap;
                HourConvert.twentyFourHoursList.stream().forEachOrdered(item -> {
                    String dateKey = sdf.format(finalTmp) + " " + item;
                    if (null != finalHourlyReportDataMap.get(dateKey)) {
                        AdKeywordAndTargetHourVo adKeywordAndTargetHourVo = handleBidVo(finalHourlyReportDataMap.get(dateKey));
                        voList.add(adKeywordAndTargetHourVo);
                    }
                });
                // 天数加上1
                dd.add(Calendar.DAY_OF_MONTH, 1);
                tmp = dd.getTime();
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return voList;
    }

    @Override
    public List<AdKeywordAndTargetHourVo> getMonthlyList(int puid, String adType, TargetHourParam param, List<AdKeywordAndTargetHourVo> compares) {
        List<AdKeywordAndTargetHourVo> reports = ReportChartUtil.getMonthReportVos(getAdTargetDailyReports(puid, adType, param, false));
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            List<AdKeywordAndTargetHourVo> compareList = ReportChartUtil.getMonthReportVos(getAdTargetDailyReports(puid, adType, param, true));
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            return paddingMonthCompare(param, reports, compareList);
        }
        return reports;
    }

    private List<AdKeywordAndTargetHourVo> paddingMonthCompare(TargetHourParam param, List<AdKeywordAndTargetHourVo> reports, List<AdKeywordAndTargetHourVo> compareList) {
        LocalDate start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate startCompare = LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
        List<AdKeywordAndTargetHourVo> resList = new ArrayList<>();
        Map<String, AdKeywordAndTargetHourVo> map = StreamUtil.toMap(reports, AdKeywordAndTargetHourVo::getLabel);
        Map<String, AdKeywordAndTargetHourVo> compareMap = StreamUtil.toMap(compareList, AdKeywordAndTargetHourVo::getLabel);
        DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
        for (; !YearMonth.from(start).isAfter(YearMonth.from(end));
             start = start.plusMonths(1), startCompare = startCompare.plusMonths(1)) {
            AdKeywordAndTargetHourVo report = map.get(start.format(monthFormatter));
            AdKeywordAndTargetHourVo compareReport = compareMap.get(startCompare.format(monthFormatter));
            if (Objects.isNull(report) && Objects.isNull(compareReport)) {
                continue;
            }
            AdKeywordAndTargetHourVo vo;
            if (Objects.isNull(report)) {
                vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(start.format(monthFormatter));
                vo.setDate(vo.getLabel());
            } else {
                vo = report;
            }
            vo.compareDataSet(compareReport);
            resList.add(vo);
        }
        return resList;
    }

    @Override
    public List<AdKeywordAndTargetHourVo> getWeeklyList(int puid, String adType, TargetHourParam param, List<AdKeywordAndTargetHourVo> compares) {
        List<AdKeywordAndTargetHourVo> reports = ReportChartUtil.getWeekReportVosNew(param.getStartDate(), param.getEndDate(),
                getAdTargetDailyReports(puid, adType, param, false));
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            List<AdKeywordAndTargetHourVo> compareList = ReportChartUtil.getWeekReportVosNew(param.getStartDateCompare(), param.getEndDateCompare(),
                    getAdTargetDailyReports(puid, adType, param, true));
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            return paddingWeekCompare(reports, compareList);
        } else {
            return reports;
        }
    }

    private List<AdKeywordAndTargetHourVo> paddingWeekCompare(List<AdKeywordAndTargetHourVo> reports, List<AdKeywordAndTargetHourVo> compareList) {
        if (CollectionUtils.isEmpty(reports)) {
            return reports;
        }
        for (int i = 0; i < reports.size() && i < compareList.size(); i++) {
            reports.get(i).compareDataSet(compareList.get(i));
        }
        return reports;
    }

    @Override
    public List<AdKeywordAndTargetHourVo> getDailyList(int puid, String adType, TargetHourParam param, List<AdKeywordAndTargetHourVo> compares) {
        List<AdKeywordAndTargetHourVo> reports = getAdTargetDailyReports(puid, adType, param, false);
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            List<AdKeywordAndTargetHourVo> compareList = getAdTargetDailyReports(puid, adType, param, true);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            return paddingDayCompare(param, reports, compareList);
        } else {
            return reports;
        }
    }

    private List<AdKeywordAndTargetHourVo> paddingDayCompare(TargetHourParam param, List<AdKeywordAndTargetHourVo> reportList, List<AdKeywordAndTargetHourVo> compareList) {
        LocalDate start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate startCompare = LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
        List<AdKeywordAndTargetHourVo> resList = new ArrayList<>();
        Map<String, AdKeywordAndTargetHourVo> map = StreamUtil.toMap(reportList, AdKeywordAndTargetHourVo::getLabel);
        Map<String, AdKeywordAndTargetHourVo> compareMap = StreamUtil.toMap(compareList, AdKeywordAndTargetHourVo::getLabel);
        for (; !start.isAfter(end); start = start.plusDays(1), startCompare = startCompare.plusDays(1)) {
            AdKeywordAndTargetHourVo report = map.get(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
            AdKeywordAndTargetHourVo compareReport = compareMap.get(startCompare.format(DateTimeFormatter.ISO_LOCAL_DATE));
            if (Objects.isNull(report) && Objects.isNull(compareReport)) {
                continue;
            }
            AdKeywordAndTargetHourVo vo;
            if (Objects.isNull(report)) {
                vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
            } else {
                vo = report;
            }
            vo.compareDataSet(compareReport);
            resList.add(vo);
        }
        return resList;
    }

    private List<AdKeywordAndTargetHourVo> getAdTargetDailyReports(int puid, String adType, TargetHourParam param, boolean isCompare) {
        LocalDate start = LocalDate.parse(param.getStartDate());
        LocalDate end = LocalDate.parse(param.getEndDate());
        if (isCompare) {
            start = LocalDate.parse(param.getStartDateCompare());
            end = LocalDate.parse(param.getEndDateCompare());
        }
        //日,周,月报告数据
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), param.getPuid());

        //SP
        if ("SP".equalsIgnoreCase(adType)) {
            List<CpcTargetingReport> reports = cpcTargetingReportDao.getReportByTargetId(puid, param.getShopId(),
                    start.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                    end.format(DateTimeFormatter.ofPattern("yyyyMMdd")), shopAuth.getMarketplaceId(), param.getTargetId());
            return reports.stream().map(item -> {
                AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                        .format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
                vo.setAdSale(item.getTotalSales());
                vo.setAdSelfSale(item.getAdSales());
                vo.setAdOtherSale(item.getAdOtherSales());
                vo.setAdOrderNum(item.getSaleNum());
                vo.setSelfAdOrderNum(item.getAdSaleNum());
                vo.setOtherAdOrderNum(item.getAdOtherOrderNum());
                vo.setAdSaleNum(item.getOrderNum());
                vo.setAdSelfSaleNum(item.getAdOrderNum());
                vo.setAdOtherSaleNum(item.getAdOtherSaleNum());
                vo.setAdCost(item.getCost());
                vo.setClicks(Long.valueOf(item.getClicks()));
                vo.setImpressions(Long.valueOf(item.getImpressions()));
                vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
                vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
                vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
                vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
                vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
                vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
                return vo;
            }).collect(Collectors.toList());
        } else if ("SB".equalsIgnoreCase(adType)) {
            List<AmazonAdSbTargetingReport> reports = amazonAdSbTargetingReportDao.getReportByTargetId(puid, param.getShopId(),
                    start.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                    end.format(DateTimeFormatter.ofPattern("yyyyMMdd")), shopAuth.getMarketplaceId(), param.getTargetId());
            List<AdKeywordAndTargetHourVo> adKeywordAndTargetHourVoList = reports.stream().map(item -> {
                AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                        .format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
                vo.setAdSale(item.getSales14d());
                vo.setAdSelfSale(item.getSales14dSameSKU());
                vo.setAdOtherSale(item.getSales14d().subtract(item.getSales14dSameSKU()));
                vo.setAdOrderNum(item.getConversions14d());
                vo.setSelfAdOrderNum(item.getConversions14dSameSKU());
                vo.setOtherAdOrderNum(item.getConversions14d() - item.getConversions14dSameSKU());
                vo.setAdSaleNum(Optional.ofNullable(item.getUnitsSold14d()).orElse(0));
                vo.setAdCost(item.getCost());
                vo.setClicks(Long.valueOf(item.getClicks()));
                vo.setImpressions(Long.valueOf(item.getImpressions()));
                vo.setViewableImpressions(item.getViewableImpressions());
                vo.setOrdersNewToBrand(item.getOrdersNewToBrand14d());
                vo.setUnitsOrderedNewToBrand(item.getUnitsOrderedNewToBrand14d());
                vo.setSalesNewToBrand(item.getSalesNewToBrand14d());
                vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
                vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
                vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
                vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
                vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
                vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
//                vo.setVrt(MathUtil.divideIntegerByOneHundred(item.getViewableImpressions(), item.getImpressions()));
//                vo.setVCtr(MathUtil.divideIntegerByOneHundred(item.getClicks(), item.getViewableImpressions()));
//                vo.setAdvertisingUnitPrice(MathUtil.divideByZero(item.getSales14d(), BigDecimal.valueOf(item.getConversions14d()), 2));
//                vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(item.getSales14dSameSKU(), item.getConversions14dSameSKU() != null ? BigDecimal.valueOf(item.getConversions14dSameSKU()) : BigDecimal.ZERO,2));
//                vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(item.getSales14d().subtract(item.getSales14dSameSKU()), BigDecimal.valueOf(item.getConversions14d() - (Optional.ofNullable(item.getConversions14dSameSKU()).orElse(0)))));
//                vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(item.getOrdersNewToBrand14d()), BigDecimal.valueOf(100)),BigDecimal.valueOf(item.getConversions14d())));
//                vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(item.getSalesNewToBrand14d(), BigDecimal.valueOf(100)), item.getSales14d()));
//                vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(item.getUnitsOrderedNewToBrand14d()), BigDecimal.valueOf(100)),BigDecimal.valueOf(item.getConversions14d())));
                return vo;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(adKeywordAndTargetHourVoList)) {
                //新增占比数据指标
                AdMetricDto adMetricDto = new AdMetricDto();
                filterSumMetricData(adKeywordAndTargetHourVoList, adMetricDto);
                filterMetricData(adKeywordAndTargetHourVoList, adMetricDto);
            }
            return adKeywordAndTargetHourVoList;
        } else if ("SD".equalsIgnoreCase(adType)) {
            List<TargetReportHourlyDTO> reports = amazonAdSdTargetingReportDao.getReportByTargetId(puid, param.getShopId(),
                    start.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                    end.format(DateTimeFormatter.ofPattern("yyyyMMdd")), shopAuth.getMarketplaceId(), Lists.newArrayList(param.getTargetId()));
            List<AdKeywordAndTargetHourVo> adKeywordAndTargetHourVoList = reports.stream().map(item -> {
                AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                        .format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
                vo.setAdSale(Optional.ofNullable(item.getTotalSales()).orElse(BigDecimal.ZERO));
                vo.setAdSelfSale(Optional.ofNullable(item.getAdSales()).orElse(BigDecimal.ZERO));
                vo.setAdOtherSale(MathUtil.subtract(item.getTotalSales(), item.getAdSales()));
                vo.setAdOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
                vo.setAdSaleNum(Optional.ofNullable(item.getSaleNum()).orElse(0));
                vo.setAdCost(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO));
                vo.setClicks(Long.valueOf(item.getClicks()));
                vo.setImpressions(Long.valueOf(item.getImpressions()));
                vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
                vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
                vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
                vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
                vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
                vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));

                // 新加字段
                vo.setOrdersNewToBrand(Optional.ofNullable(item.getOrdersNewToBrand14d()).orElse(0));
                vo.setUnitsOrderedNewToBrand(Optional.ofNullable(item.getUnitsOrderedNewToBrand14d()).orElse(0));
                vo.setSalesNewToBrand(Optional.ofNullable(item.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO));


                vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
                if (Constants.SD_REPORT_CPC.equals(item.getCostType())) {
                    vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
                    vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getAdOrderNum() - vo.getSelfAdOrderNum())));

                } else {
                    //新增字段
                    vo.setViewableImpressions(Optional.ofNullable(item.getViewableImpressions()).orElse(0));
                    vo.setVrt(vo.getImpressions() == 0 || vo.getViewableImpressions() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(vo.getViewableImpressions()).divide(BigDecimal.valueOf(vo.getImpressions()), 2, RoundingMode.HALF_UP));
                    vo.setVCtr(vo.getViewableImpressions() == 0 || vo.getClicks() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(vo.getClicks()).divide(BigDecimal.valueOf(vo.getViewableImpressions()), 2, RoundingMode.HALF_UP));
                    vo.setVcpm(MathUtil.divideByThousand(item.getVcpmCost(), item.getViewableImpressions()));
                }
                vo.setSelfAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
                vo.setOtherAdOrderNum(MathUtil.subtractInteger(item.getOrderNum(), item.getAdOrderNum()));
                vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(item.getOrdersNewToBrand14d()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
                vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(item.getUnitsOrderedNewToBrand14d()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
                vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));

                return vo;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(adKeywordAndTargetHourVoList)) {
                //新增占比数据指标
                AdMetricDto adMetricDto = new AdMetricDto();
                filterSumMetricData(adKeywordAndTargetHourVoList, adMetricDto);
                filterMetricData(adKeywordAndTargetHourVoList, adMetricDto);
            }
            return adKeywordAndTargetHourVoList;
        }
        return null;
    }


    /**
     * 查询产品维度投放小时数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordTargetHourOfAdVo> getListOfAd(int puid, TargetHourParam param) {
        return convertToHourOfAdVos(getDetailListOfAd(puid, param));

    }

    /**
     * 查询产品维度投放小时明细数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordAndTargetHourVo> getDetailListOfAd(int puid, TargetHourParam param) {
        //校验店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        selectDto.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
        selectDto.setMarketplaceId(shopAuth.getMarketplaceId());
        selectDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), selectDto.getMarketplaceId(), param.getStartDate()));
        selectDto.setEndDate(param.getEndDate());
        if (StringUtils.isNotBlank(param.getTargetId())) {
            selectDto.setItemIds(Collections.singletonList(param.getTargetId()));
        }

        List<AmazonMarketingStreamData> dataList =
                amazonMarketingStreamDataDao.statisticsTargetHourlyReportOfAd(selectDto);

        //pb对象转vo对象
        List<AdKeywordAndTargetHourVo> hourVos = dataList.stream().map(this::spConvertTo).collect(Collectors.toList());
        //查询所有对应的asin
        List<String> adIds = hourVos.stream().map(AdKeywordAndTargetHourVo::getAdId).distinct().collect(Collectors.toList());
        List<AmazonAdProduct> adProducts = amazonAdProductDao.getByAdIds(puid, shopAuth.getId(), adIds);
        Map<String, AmazonAdProduct> adProductMap = adProducts.stream().collect(Collectors.toMap(
                AmazonAdProduct::getAdId, Function.identity(), (a, b) -> a));
        //填充asin信息
        List<AdKeywordAndTargetHourVo> voList = hourVos.stream().peek(item -> {
            if (adProductMap.containsKey(item.getAdId())) {
                item.setAsin(adProductMap.get(item.getAdId()).getAsin());
            }
        }).collect(Collectors.toList());

        //时段无数据填充0值
        Map<String, List<AdKeywordAndTargetHourVo>> voMap =
                voList.stream().collect(Collectors.groupingBy(each -> StringUtils.defaultIfEmpty(each.getAsin(), "接口暂未返回ASIN")));
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();

        voMap.forEach((k, v) -> {
            List<String> hourList = v.stream().map(AdKeywordAndTargetHourVo::getLabel).collect(Collectors.toList());
            List<String> needFilledHour = allHours.stream()
                    .filter(item -> !hourList.contains(item)).collect(Collectors.toList());
            for (String hour : needFilledHour) {
                AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(hour);
                vo.setHour(Integer.valueOf(hour.split("-")[0]));
                vo.setAsin(k);
                voList.add(vo);
            }
        });

        return voList.stream().filter(o -> StringUtils.isNotBlank(o.getAsin()))
                .sorted(Comparator.comparing(AdKeywordAndTargetHourVo::getAsin)
                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());
    }

    /**
     * 查询广告位维度小时级数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordTargetHourOfPlacementVo> getListOfPlacement(int puid, TargetHourParam param) {
        return convertToHourOfPlacementVos(getDetailListOfPlacement(puid, param));
    }

    /**
     * 查询广告位维度小时级明细数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordAndTargetHourVo> getDetailListOfPlacement(int puid, TargetHourParam param) {
        //校验店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        selectDto.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
        selectDto.setMarketplaceId(shopAuth.getMarketplaceId());
        selectDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), selectDto.getMarketplaceId(), param.getStartDate()));
        selectDto.setEndDate(param.getEndDate());
        if (StringUtils.isNotBlank(param.getTargetId())) {
            selectDto.setItemIds(Collections.singletonList(param.getTargetId()));
        }

        List<AmazonMarketingStreamData> dataList =
                amazonMarketingStreamDataDao.statisticsTargetHourlyReportOfPlacement(selectDto);

        //pb对象转vo对象
        List<AdKeywordAndTargetHourVo> voList = dataList.stream().map(this::spConvertTo).collect(Collectors.toList());
        //填充无数据的时间段
        Map<String, List<AdKeywordAndTargetHourVo>> voMap =
                voList.stream().collect(Collectors.groupingBy(AdKeywordAndTargetHourVo::getPlacement));
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();

        voMap.forEach((k, v) -> {
            List<String> hourList = v.stream().map(AdKeywordAndTargetHourVo::getLabel).collect(Collectors.toList());
            List<String> needFilledHour = allHours.stream()
                    .filter(item -> !hourList.contains(item)).collect(Collectors.toList());
            for (String hour : needFilledHour) {
                AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(hour);
                vo.setHour(Integer.valueOf(hour.split("-")[0]));
                vo.setPlacement(k);
                //广告位排序
                if ("产品页面".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(3);
                } else if ("搜索结果顶部(首页)".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(1);
                } else if ("搜索结果的其余位置".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(2);
                } else {
                    vo.setPlacementOrder(4);
                }
                voList.add(vo);
            }
        });

        return voList.stream().sorted(Comparator.comparingInt(AdKeywordAndTargetHourVo::getPlacementOrder)
                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());
    }


    private List<AdKeywordTargetHourOfAdVo> convertToHourOfAdVos(List<AdKeywordAndTargetHourVo> hourVos) {
        //按asin汇总数据
        Map<String, List<AdKeywordAndTargetHourVo>> hourVoMap =
                hourVos.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AdKeywordAndTargetHourVo::getAsin));

        List<AdKeywordTargetHourOfAdVo> adKeywordTargetHourOfAdVos = new ArrayList<>(hourVoMap.size());

        for (Map.Entry<String, List<AdKeywordAndTargetHourVo>> entry : hourVoMap.entrySet()) {
            AdKeywordTargetHourOfAdVo adKeywordTargetHourOfAdVo = new AdKeywordTargetHourOfAdVo();
            List<AdKeywordAndTargetHourVo> asinHourVos = entry.getValue();
            adKeywordTargetHourOfAdVo.setAsin(entry.getKey());
            adKeywordTargetHourOfAdVo.setDetails(asinHourVos);

            adKeywordTargetHourOfAdVo.staticsFromHourVos(asinHourVos);
            adKeywordTargetHourOfAdVos.add(adKeywordTargetHourOfAdVo);
        }
        return adKeywordTargetHourOfAdVos;
    }


    private List<AdKeywordTargetHourOfPlacementVo> convertToHourOfPlacementVos(List<AdKeywordAndTargetHourVo> hourVos) {
        //按广告位汇总数据
        Map<String, List<AdKeywordAndTargetHourVo>> hourVoMap =
                hourVos.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AdKeywordAndTargetHourVo::getPlacement));

        List<AdKeywordTargetHourOfPlacementVo> placementVos = new ArrayList<>(3);

        for (Map.Entry<String, List<AdKeywordAndTargetHourVo>> entry : hourVoMap.entrySet()) {
            AdKeywordTargetHourOfPlacementVo adKeywordTargetHourOfPlacementVo = new AdKeywordTargetHourOfPlacementVo();

            List<AdKeywordAndTargetHourVo> asinHourVos = entry.getValue();
            adKeywordTargetHourOfPlacementVo.setDetails(asinHourVos);
            adKeywordTargetHourOfPlacementVo.staticsFromHourVos(asinHourVos);
            adKeywordTargetHourOfPlacementVo.setPlacement(entry.getKey());
            placementVos.add(adKeywordTargetHourOfPlacementVo);
        }
        return placementVos.stream().collect(Collectors.toList());
    }


    private AdKeywordAndTargetHourVo handleBaseVo(Integer hour, AmazonMarketingStreamData data,
                                                AmazonMarketingStreamData dataCompare, BigDecimal shopSalesByDate) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        vo.setLabel(HourConvert.hourMap.get(hour));
        vo.setHour(hour);

        AdKeywordAndTargetHourVo adTargetHourVoCompare = baseConvertTo(dataCompare);
        AdKeywordAndTargetHourVo adTargetHourVo = baseConvertTo(data);

        vo.setAdSale(adTargetHourVo.getAdSale());
        vo.setAdSelfSale(adTargetHourVo.getAdSelfSale());
        vo.setAdOtherSale(adTargetHourVo.getAdOtherSale());
        vo.setAdOrderNum(adTargetHourVo.getAdOrderNum());
        vo.setSelfAdOrderNum(adTargetHourVo.getSelfAdOrderNum());
        vo.setOtherAdOrderNum(adTargetHourVo.getOtherAdOrderNum());
        vo.setAdSaleNum(adTargetHourVo.getAdSaleNum());
        vo.setAdSelfSaleNum(adTargetHourVo.getAdSelfSaleNum());
        vo.setAdOtherSaleNum(adTargetHourVo.getAdOtherSaleNum());
        vo.setAdCost(adTargetHourVo.getAdCost());
        vo.setClicks(adTargetHourVo.getClicks());
        vo.setImpressions(adTargetHourVo.getImpressions());
        vo.setAdCostPerClick(adTargetHourVo.getAdCostPerClick());
        vo.setCpa(adTargetHourVo.getCpa());
        vo.setAcos(adTargetHourVo.getAcos());
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setViewableImpressions(adTargetHourVo.getViewableImpressions());
        vo.setOrdersNewToBrand(adTargetHourVo.getOrdersNewToBrand());
        vo.setUnitsOrderedNewToBrand(adTargetHourVo.getUnitsOrderedNewToBrand());
        vo.setSalesNewToBrand(adTargetHourVo.getSalesNewToBrand());
        vo.setClicksCompare(adTargetHourVoCompare.getClicks());
        vo.setImpressionsCompare(adTargetHourVoCompare.getImpressions());
        vo.setAdSaleCompare(adTargetHourVoCompare.getAdSale());
        vo.setAdSaleNumCompare(adTargetHourVoCompare.getAdSaleNum());
        vo.setAdCostCompare(adTargetHourVoCompare.getAdCost());
        vo.setAdOrderNumCompare(adTargetHourVoCompare.getAdOrderNum());
        vo.setAdCostPerClickCompare(MathUtil.divideByZero(vo.getAdCostCompare(), BigDecimal.valueOf(vo.getClicksCompare())));
        vo.setAcosCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                vo.getAdCostCompare().multiply(new BigDecimal("100")).divide(vo.getAdSaleCompare(), 4, RoundingMode.HALF_UP));
        vo.setRoasCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                vo.getAdSaleCompare().divide(vo.getAdCostCompare(), 4, RoundingMode.HALF_UP));
        vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicksCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressionsCompare())));
        vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNumCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicksCompare())));
        vo.setVrt(MathUtil.divideIntegerByOneHundred(adTargetHourVo.getViewableImpressions(), adTargetHourVo.getImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(adTargetHourVo.getClicks(), adTargetHourVo.getViewableImpressions()));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(adTargetHourVo.getAdSale(), BigDecimal.valueOf(adTargetHourVo.getAdOrderNum()), 2));
        vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(adTargetHourVo.getAdSelfSale(), BigDecimal.valueOf(adTargetHourVo.getSelfAdOrderNum()), 2));
        vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(adTargetHourVo.getAdSale().subtract(adTargetHourVo.getAdSelfSale()), BigDecimal.valueOf(adTargetHourVo.getAdOrderNum() - adTargetHourVo.getSelfAdOrderNum())));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adTargetHourVo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adTargetHourVo.getAdOrderNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(adTargetHourVo.getSalesNewToBrand(), BigDecimal.valueOf(100)), adTargetHourVo.getAdSale()));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adTargetHourVo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adTargetHourVo.getAdSaleNum())));
        vo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate));
        vo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate));
        vo.setVcpm(MathUtil.divideByThousand(vo.getAdCost(), vo.getViewableImpressions()));
        //set compare rate data
        vo.calculateCompareRate();
        return vo;
    }


    private AdKeywordAndTargetHourVo handleVo(Integer hour, HourlyReportDataPb.HourlyReportData data,
                                              HourlyReportDataPb.HourlyReportData dataCompare) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        vo.setLabel(HourConvert.hourMap.get(hour));
        vo.setHour(hour);

        AdKeywordAndTargetHourVo adTargetHourVoCompare = spConvertTo(dataCompare);

        AdKeywordAndTargetHourVo adTargetHourVo = spConvertTo(data);

        vo.setAdSale(adTargetHourVo.getAdSale());
        vo.setAdSelfSale(adTargetHourVo.getAdSelfSale());
        vo.setAdOtherSale(adTargetHourVo.getAdOtherSale());
        vo.setAdOrderNum(adTargetHourVo.getAdOrderNum());
        vo.setSelfAdOrderNum(adTargetHourVo.getSelfAdOrderNum());
        vo.setOtherAdOrderNum(adTargetHourVo.getOtherAdOrderNum());
        vo.setAdSaleNum(adTargetHourVo.getAdSaleNum());
        vo.setAdSelfSaleNum(adTargetHourVo.getAdSelfSaleNum());
        vo.setAdOtherSaleNum(adTargetHourVo.getAdOtherSaleNum());
        vo.setAdCost(adTargetHourVo.getAdCost());
        vo.setClicks(adTargetHourVo.getClicks());
        vo.setImpressions(adTargetHourVo.getImpressions());
        vo.setAdCostPerClick(adTargetHourVo.getAdCostPerClick());
        vo.setCpa(adTargetHourVo.getCpa());
        vo.setAcos(adTargetHourVo.getAcos());
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));

        vo.setClicksCompare(adTargetHourVoCompare.getClicks());
        vo.setImpressionsCompare(adTargetHourVoCompare.getImpressions());
        vo.setAdSaleCompare(adTargetHourVoCompare.getAdSale());
        vo.setAdSaleNumCompare(adTargetHourVoCompare.getAdSaleNum());
        vo.setAdCostCompare(adTargetHourVoCompare.getAdCost());
        vo.setAdOrderNumCompare(adTargetHourVoCompare.getAdOrderNum());
        //set compare rate data
        vo.afterPropertiesSet();
        return vo;
    }

    private AdKeywordAndTargetHourVo handleVo(Integer hour, AmazonMarketingStreamData data, AmazonMarketingStreamData dataCompare) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        vo.setLabel(HourConvert.hourMap.get(hour));
        vo.setHour(hour);

        AdKeywordAndTargetHourVo adTargetHourVoCompare = spConvertTo(dataCompare);

        AdKeywordAndTargetHourVo adTargetHourVo = spConvertTo(data);

        vo.setAdSale(adTargetHourVo.getAdSale());
        vo.setAdSelfSale(adTargetHourVo.getAdSelfSale());
        vo.setAdOtherSale(adTargetHourVo.getAdOtherSale());
        vo.setAdOrderNum(adTargetHourVo.getAdOrderNum());
        vo.setSelfAdOrderNum(adTargetHourVo.getSelfAdOrderNum());
        vo.setOtherAdOrderNum(adTargetHourVo.getOtherAdOrderNum());
        vo.setAdSaleNum(adTargetHourVo.getAdSaleNum());
        vo.setAdSelfSaleNum(adTargetHourVo.getAdSelfSaleNum());
        vo.setAdOtherSaleNum(adTargetHourVo.getAdOtherSaleNum());
        vo.setAdCost(adTargetHourVo.getAdCost());
        vo.setClicks(adTargetHourVo.getClicks());
        vo.setImpressions(adTargetHourVo.getImpressions());
        vo.setAdCostPerClick(adTargetHourVo.getAdCostPerClick());
        vo.setCpa(adTargetHourVo.getCpa());
        vo.setAcos(adTargetHourVo.getAcos());
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));

        vo.setClicksCompare(adTargetHourVoCompare.getClicks());
        vo.setImpressionsCompare(adTargetHourVoCompare.getImpressions());
        vo.setAdSaleCompare(adTargetHourVoCompare.getAdSale());
        vo.setAdSaleNumCompare(adTargetHourVoCompare.getAdSaleNum());
        vo.setAdCostCompare(adTargetHourVoCompare.getAdCost());
        vo.setAdOrderNumCompare(adTargetHourVoCompare.getAdOrderNum());
        vo.setAdCostPerClickCompare(adTargetHourVoCompare.getAdCostPerClick());
        vo.setAcosCompare(adTargetHourVoCompare.getAcos());
        vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adTargetHourVoCompare.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adTargetHourVoCompare.getImpressions())));
        vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adTargetHourVoCompare.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adTargetHourVoCompare.getClicks())));
        vo.setRoasCompare(adTargetHourVoCompare.getAdSale().compareTo(BigDecimal.ZERO) == 0 || adTargetHourVoCompare.getAdCost().compareTo(BigDecimal.ZERO) == 0 ?
                        BigDecimal.ZERO : adTargetHourVoCompare.getAdSale().divide(adTargetHourVoCompare.getAdCost(), 4, RoundingMode.HALF_UP));
        //set compare rate data
        vo.calculateCompareRate();
        return vo;
    }

    private AdKeywordAndTargetHourVo handleSbVo(Integer hour, AmazonMarketingStreamData data,
                                                AmazonMarketingStreamData dataCompare, BigDecimal shopSalesByDate) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        vo.setLabel(HourConvert.hourMap.get(hour));
        vo.setHour(hour);

        AdKeywordAndTargetHourVo adTargetHourVoCompare = sbConvertTo(dataCompare);

        AdKeywordAndTargetHourVo adTargetHourVo = sbConvertTo(data);

        vo.setAdSale(adTargetHourVo.getAdSale());
        vo.setAdSelfSale(adTargetHourVo.getAdSelfSale());
        vo.setAdOtherSale(adTargetHourVo.getAdOtherSale());
        vo.setAdOrderNum(adTargetHourVo.getAdOrderNum());
        vo.setSelfAdOrderNum(adTargetHourVo.getSelfAdOrderNum());
        vo.setOtherAdOrderNum(adTargetHourVo.getOtherAdOrderNum());
        vo.setAdSaleNum(adTargetHourVo.getAdSaleNum());
        vo.setAdSelfSaleNum(adTargetHourVo.getAdSelfSaleNum());
        vo.setAdOtherSaleNum(adTargetHourVo.getAdOtherSaleNum());
        vo.setAdCost(adTargetHourVo.getAdCost());
        vo.setClicks(adTargetHourVo.getClicks());
        vo.setImpressions(adTargetHourVo.getImpressions());
        vo.setAdCostPerClick(adTargetHourVo.getAdCostPerClick());
        vo.setCpa(adTargetHourVo.getCpa());
        vo.setAcos(adTargetHourVo.getAcos());
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setViewableImpressions(adTargetHourVo.getViewableImpressions());
        vo.setOrdersNewToBrand(adTargetHourVo.getOrdersNewToBrand());
        vo.setUnitsOrderedNewToBrand(adTargetHourVo.getUnitsOrderedNewToBrand());
        vo.setSalesNewToBrand(adTargetHourVo.getSalesNewToBrand());
        vo.setClicksCompare(adTargetHourVoCompare.getClicks());
        vo.setImpressionsCompare(adTargetHourVoCompare.getImpressions());
        vo.setAdSaleCompare(adTargetHourVoCompare.getAdSale());
        vo.setAdSaleNumCompare(adTargetHourVoCompare.getAdSaleNum());
        vo.setAdCostCompare(adTargetHourVoCompare.getAdCost());
        vo.setAdOrderNumCompare(adTargetHourVoCompare.getAdOrderNum());
        vo.setAdCostPerClickCompare(adTargetHourVoCompare.getAdCostPerClick());
        vo.setAcosCompare(adTargetHourVoCompare.getAcos());
        vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adTargetHourVoCompare.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adTargetHourVoCompare.getImpressions())));
        vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adTargetHourVoCompare.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adTargetHourVoCompare.getClicks())));
        vo.setRoasCompare(adTargetHourVoCompare.getAdSale().compareTo(BigDecimal.ZERO) == 0 || adTargetHourVoCompare.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : adTargetHourVoCompare.getAdSale().divide(adTargetHourVoCompare.getAdCost(), 4, RoundingMode.HALF_UP));

        vo.setVrt(MathUtil.divideIntegerByOneHundred(adTargetHourVo.getViewableImpressions(), adTargetHourVo.getImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(adTargetHourVo.getClicks(), adTargetHourVo.getViewableImpressions()));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(adTargetHourVo.getAdSale(), BigDecimal.valueOf(adTargetHourVo.getAdOrderNum()), 2));
        vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(adTargetHourVo.getAdSelfSale(), BigDecimal.valueOf(adTargetHourVo.getSelfAdOrderNum()), 2));
        vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(adTargetHourVo.getAdSale().subtract(adTargetHourVo.getAdSelfSale()), BigDecimal.valueOf(adTargetHourVo.getAdOrderNum() - adTargetHourVo.getSelfAdOrderNum())));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adTargetHourVo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adTargetHourVo.getAdOrderNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(adTargetHourVo.getSalesNewToBrand(), BigDecimal.valueOf(100)), adTargetHourVo.getAdSale()));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adTargetHourVo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adTargetHourVo.getAdSaleNum())));
        vo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate));
        vo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate));
        //set compare rate data
        vo.calculateCompareRate();
        return vo;
    }

    private AdKeywordAndTargetHourVo handleSdCpcVo(Integer hour, AmazonMarketingStreamData data,
                                                   AmazonMarketingStreamData dataCompare, BigDecimal shopSalesByDate) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        vo.setLabel(HourConvert.hourMap.get(hour));
        vo.setHour(hour);

        AdKeywordAndTargetHourVo adTargetHourVoCompare = convertCpcTo(dataCompare);

        AdKeywordAndTargetHourVo adTargetHourVo = convertCpcTo(data);


        vo.setVcpmCost(BigDecimal.ZERO);
        vo.setVcpmImpressions(0L);
        vo.setTotalImpressions(0L);
        vo.setTotalClicks(0L);
        vo.setTotalAdSale(adTargetHourVo.getAdSale());
        vo.setTotalAdSelfSale(adTargetHourVo.getAdSelfSale());
        vo.setAdSale(adTargetHourVo.getAdSale());
        vo.setAdSelfSale(adTargetHourVo.getAdSelfSale());
        vo.setAdOtherSale(adTargetHourVo.getAdOtherSale());
        vo.setAdOrderNum(adTargetHourVo.getAdOrderNum());
        vo.setSelfAdOrderNum(adTargetHourVo.getSelfAdOrderNum());
        vo.setOtherAdOrderNum(adTargetHourVo.getOtherAdOrderNum());
        vo.setAdSaleNum(adTargetHourVo.getAdSaleNum());
        vo.setAdSelfSaleNum(adTargetHourVo.getAdSelfSaleNum());
        vo.setAdOtherSaleNum(adTargetHourVo.getAdOtherSaleNum());
        vo.setAdCost(adTargetHourVo.getAdCost());
        vo.setClicks(adTargetHourVo.getClicks());
        vo.setImpressions(adTargetHourVo.getImpressions());
        vo.setAdCostPerClick(adTargetHourVo.getAdCostPerClick());
        vo.setCpa(adTargetHourVo.getCpa());
        vo.setAcos(adTargetHourVo.getAcos());
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setClicksCompare(adTargetHourVoCompare.getClicks());
        vo.setImpressionsCompare(adTargetHourVoCompare.getImpressions());
        vo.setAdSaleCompare(adTargetHourVoCompare.getAdSale());
        vo.setAdSaleNumCompare(adTargetHourVoCompare.getAdSaleNum());
        vo.setAdCostCompare(adTargetHourVoCompare.getAdCost());
        vo.setAdOrderNumCompare(adTargetHourVoCompare.getAdOrderNum());
        vo.setAdCostPerClickCompare(adTargetHourVoCompare.getAdCostPerClick());
        vo.setAcosCompare(adTargetHourVoCompare.getAcos());
        vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adTargetHourVoCompare.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adTargetHourVoCompare.getImpressions())));
        vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adTargetHourVoCompare.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adTargetHourVoCompare.getClicks())));
        vo.setRoasCompare(adTargetHourVoCompare.getAdSale().compareTo(BigDecimal.ZERO) == 0 || adTargetHourVoCompare.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : adTargetHourVoCompare.getAdSale().divide(adTargetHourVoCompare.getAdCost(), 4, RoundingMode.HALF_UP));

        vo.setOrdersNewToBrand(adTargetHourVo.getOrdersNewToBrand());
        vo.setUnitsOrderedNewToBrand(adTargetHourVo.getUnitsOrderedNewToBrand());
        vo.setSalesNewToBrand(adTargetHourVo.getSalesNewToBrand());
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum()), 2));
        vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
        vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getAdOrderNum() - vo.getSelfAdOrderNum())));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
        vo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate));
        vo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate));
        //set compare rate data
        vo.calculateCompareRate();
        return vo;
    }

    private AdKeywordAndTargetHourVo handleSdVcpmVo(Integer hour, AmazonMarketingStreamData data, AmazonMarketingStreamData dataCompare, BigDecimal shopSalesByDate) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        vo.setLabel(HourConvert.hourMap.get(hour));
        vo.setHour(hour);

        AdKeywordAndTargetHourVo adTargetHourVoCompare = convertToVcpm(dataCompare);

        AdKeywordAndTargetHourVo adTargetHourVo = convertToVcpm(data);

        vo.setVcpmCost(adTargetHourVo.getAdCost());
        vo.setVcpmImpressions(adTargetHourVo.getViewableImpressions().longValue());
        vo.setTotalImpressions(adTargetHourVo.getImpressions());
        vo.setTotalClicks(adTargetHourVo.getClicks());
        vo.setTotalAdSale(BigDecimal.ZERO);
        vo.setTotalAdSelfSale(BigDecimal.ZERO);
        vo.setAdSale(adTargetHourVo.getAdSale());
        vo.setAdSelfSale(adTargetHourVo.getAdSelfSale());
        vo.setAdOtherSale(adTargetHourVo.getAdOtherSale());
        vo.setAdOrderNum(adTargetHourVo.getAdOrderNum());
        vo.setAdSaleNum(adTargetHourVo.getAdSaleNum());
        vo.setAdCost(adTargetHourVo.getAdCost());
        vo.setClicks(adTargetHourVo.getClicks());
        vo.setImpressions(adTargetHourVo.getImpressions());
        vo.setAdCostPerClick(adTargetHourVo.getAdCostPerClick());
        vo.setCpa(adTargetHourVo.getCpa());
        vo.setAcos(adTargetHourVo.getAcos());
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));

        vo.setClicksCompare(adTargetHourVoCompare.getClicks());
        vo.setImpressionsCompare(adTargetHourVoCompare.getImpressions());
        vo.setAdSaleCompare(adTargetHourVoCompare.getAdSale());
        vo.setAdSaleNumCompare(adTargetHourVoCompare.getAdSaleNum());
        vo.setAdCostCompare(adTargetHourVoCompare.getAdCost());
        vo.setAdOrderNumCompare(adTargetHourVoCompare.getAdOrderNum());
        vo.setAdCostPerClickCompare(adTargetHourVoCompare.getAdCostPerClick());
        vo.setAcosCompare(adTargetHourVoCompare.getAcos());
        vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adTargetHourVoCompare.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adTargetHourVoCompare.getImpressions())));
        vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adTargetHourVoCompare.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adTargetHourVoCompare.getClicks())));
        vo.setRoasCompare(adTargetHourVoCompare.getAdSale().compareTo(BigDecimal.ZERO) == 0 || adTargetHourVoCompare.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : adTargetHourVoCompare.getAdSale().divide(adTargetHourVoCompare.getAdCost(), 4, RoundingMode.HALF_UP));

        vo.setViewableImpressions(adTargetHourVo.getViewableImpressions());
        vo.setOrdersNewToBrand(adTargetHourVo.getOrdersNewToBrand());
        vo.setUnitsOrderedNewToBrand(adTargetHourVo.getUnitsOrderedNewToBrand());
        vo.setSalesNewToBrand(adTargetHourVo.getSalesNewToBrand());
        vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));
        vo.setVcpm(MathUtil.divideByThousand(vo.getAdCost(), vo.getViewableImpressions()));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum()), 2));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
        vo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate));
        vo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate));
        //set compare rate data
        vo.calculateCompareRate();
        return vo;
    }

    private AdKeywordAndTargetHourVo handleBidVo(AmazonMarketingStreamData data) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (null == data) {
            return vo;
        }

        vo.setLabel(DateUtil.dateToStrWithTime(data.getTimeWindowStart(), "yyyy-MM-dd HH"));
//        vo.setHour(hour);

//        AdKeywordAndTargetHourVo adTargetHourVoCompare = bidConvertTo(dataCompare);

        AdKeywordAndTargetHourVo adTargetHourVo = bidConvertTo(data);

        vo.setAdSale(adTargetHourVo.getAdSale());
        vo.setAdSelfSale(adTargetHourVo.getAdSelfSale());
        vo.setAdOtherSale(adTargetHourVo.getAdOtherSale());
        vo.setAdOrderNum(adTargetHourVo.getAdOrderNum());
        vo.setSelfAdOrderNum(adTargetHourVo.getSelfAdOrderNum());
        vo.setOtherAdOrderNum(adTargetHourVo.getOtherAdOrderNum());
        vo.setAdSaleNum(adTargetHourVo.getAdSaleNum());
        vo.setAdSelfSaleNum(adTargetHourVo.getAdSelfSaleNum());
        vo.setAdOtherSaleNum(adTargetHourVo.getAdOtherSaleNum());
        vo.setAdCost(adTargetHourVo.getAdCost());
        vo.setClicks(adTargetHourVo.getClicks());
        vo.setImpressions(adTargetHourVo.getImpressions());
        vo.setAdCostPerClick(adTargetHourVo.getAdCostPerClick());
        vo.setCpa(adTargetHourVo.getCpa());
        vo.setAcos(adTargetHourVo.getAcos());
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));

//        vo.setClicksCompare(adTargetHourVoCompare.getClicks());
//        vo.setClicksCompareRate(MathUtil.divideByZero(MathUtil.subtract(BigDecimal.valueOf(vo.getClicksCompare()), BigDecimal.valueOf(vo.getClicks())).multiply(BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
//        vo.setImpressionsCompare(adTargetHourVoCompare.getImpressions());
//        vo.setImpressionsCompareRate(MathUtil.divideByZero(MathUtil.subtract(BigDecimal.valueOf(vo.getImpressionsCompare()), BigDecimal.valueOf(vo.getImpressions())).multiply(BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
//        vo.setAdSaleNumCompare(adTargetHourVoCompare.getAdOrderNum());
//        vo.setAdSaleNumCompareRate(MathUtil.divideByZero(MathUtil.subtract(BigDecimal.valueOf(vo.getAdSaleNumCompare()), BigDecimal.valueOf(vo.getAdOrderNum())).multiply(BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
//        vo.setAdSaleCompare(adTargetHourVoCompare.getAdSale());
//        vo.setAdSaleCompareRate(MathUtil.divideByZero(MathUtil.subtract(vo.getAdSaleCompare(), vo.getAdSale()).multiply(BigDecimal.valueOf(100)), vo.getAdSale()));
//        vo.setAdSaleNumCompare(adTargetHourVoCompare.getAdSaleNum());
//        vo.setAdSaleNumCompareRate(MathUtil.divideByZero(MathUtil.subtract(BigDecimal.valueOf(vo.getAdSaleNumCompare()), BigDecimal.valueOf(vo.getAdSaleNum())).multiply(BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
//        vo.setAdCostCompare(adTargetHourVoCompare.getAdCost());
//        vo.setAdCostCompareRate(MathUtil.divideByZero(MathUtil.subtract(vo.getAdCostCompare(), vo.getAdCost()).multiply(BigDecimal.valueOf(100)), vo.getAdCost()));
//        vo.setAdOrderNumCompare(adTargetHourVoCompare.getAdOrderNum());
//        vo.setAdOrderNumCompareRate(MathUtil.divideByZero(MathUtil.subtract(BigDecimal.valueOf(vo.getAdOrderNumCompare()), BigDecimal.valueOf(vo.getAdOrderNum())).multiply(BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
        return vo;
    }

    private AdKeywordAndTargetHourVo bidConvertTo(AmazonMarketingStreamData data) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (data == null) {
            return vo;
        }
//        vo.setAdId(data.getAdId());
//        vo.setPlacement(data.getPlacement());
        //广告位排序
        if ("Detail Page on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("产品页面");
            vo.setPlacementOrder(3);
        } else if ("Top of Search on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("搜索结果顶部(首页)");
            vo.setPlacementOrder(1);
        } else if ("Other on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacementOrder(2);
            vo.setPlacement("搜索结果的其余位置");
        } else {
            vo.setPlacementOrder(4);
        }
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7d());
        vo.setSelfAdOrderNum(data.getAttributedConversions7dSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7d(), data.getAttributedConversions7dSameSku()));
        vo.setAdSale(data.getAttributedSales7d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(data.getAttributedSales7dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(data.getAttributedSales7d(), data.getAttributedSales7dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7d());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7dSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7d(), data.getAttributedUnitsOrdered7dSameSku()));
        vo.setImpressions(data.getImpressions());
//        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(DateUtil.dateToStrWithTime(data.getTimeWindowStart(), "yyyy-MM-dd HH"));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }

    private AdKeywordAndTargetHourVo spConvertTo(HourlyReportDataPb.HourlyReportData data) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (data == null) {
            return vo;
        }
        vo.setAdId(data.getAdId());
        vo.setPlacement(data.getPlacement());
        //广告位排序
        if ("Detail Page on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("产品页面");
            vo.setPlacementOrder(3);
        } else if ("Top of Search on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("搜索结果顶部(首页)");
            vo.setPlacementOrder(1);
        } else if ("Other on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacementOrder(2);
            vo.setPlacement("搜索结果的其余位置");
        } else {
            vo.setPlacementOrder(4);
        }
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7D());
        vo.setSelfAdOrderNum(data.getAttributedConversions7DSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7D(), data.getAttributedConversions7DSameSku()));
        vo.setAdSale(BigDecimal.valueOf(data.getAttributedSales7D()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(BigDecimal.valueOf(data.getAttributedSales7DSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(BigDecimal.valueOf(data.getAttributedSales7D()), BigDecimal.valueOf(data.getAttributedSales7DSameSku())).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7D());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7DSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7D(), data.getAttributedUnitsOrdered7DSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }

    private AdKeywordAndTargetHourVo spConvertTo(AmazonMarketingStreamData data) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (data == null) {
            return vo;
        }
        vo.setAdId(data.getAdId());
        vo.setPlacement(data.getPlacement());
        //广告位排序
        if ("Detail Page on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("产品页面");
            vo.setPlacementOrder(3);
        } else if ("Top of Search on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("搜索结果顶部(首页)");
            vo.setPlacementOrder(1);
        } else if ("Other on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacementOrder(2);
            vo.setPlacement("搜索结果的其余位置");
        } else {
            vo.setPlacementOrder(4);
        }
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7d());
        vo.setSelfAdOrderNum(data.getAttributedConversions7dSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7d(), data.getAttributedConversions7dSameSku()));
        vo.setAdSale(data.getAttributedSales7d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(data.getAttributedSales7dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(data.getAttributedSales7d(), data.getAttributedSales7dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7d());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7dSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7d(), data.getAttributedUnitsOrdered7dSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }

    private AdKeywordAndTargetHourVo convertCpcTo(AmazonMarketingStreamData data) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (data == null) {
            return vo;
        }
        vo.setAdId(data.getAdId());
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions14d());
        vo.setSelfAdOrderNum(data.getAttributedConversions14dSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions14d(), data.getAttributedConversions14dSameSku()));
        vo.setAdSale(data.getAttributedSales14d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(data.getAttributedSales14dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(data.getAttributedSales14d(), data.getAttributedSales14dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered14d());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered14dSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered14d(), data.getAttributedUnitsOrdered14dSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setViewableImpressions(0);
        vo.setOrdersNewToBrand(data.getAttributedOrdersNewToBrand14d());
        vo.setUnitsOrderedNewToBrand(data.getAttributedUnitsOrderedNewToBrand14d());
        vo.setSalesNewToBrand(data.getAttributedSalesNewToBrand14d());
        return vo;
    }

    private AdKeywordAndTargetHourVo sbConvertTo(AmazonMarketingStreamData data) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (data == null) {
            return vo;
        }
        vo.setAdId(data.getAdId());
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions14d());
        vo.setSelfAdOrderNum(data.getAttributedConversions14dSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions14d(), data.getAttributedConversions14dSameSku()));
        vo.setAdSale(data.getAttributedSales14d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(data.getAttributedSales14dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(data.getAttributedSales14d(), data.getAttributedSales14dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered14d());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered14dSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered14d(), data.getAttributedUnitsOrdered14dSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setViewableImpressions(data.getViewableImpressions().intValue());
        vo.setOrdersNewToBrand(data.getAttributedOrdersNewToBrand14d());
        vo.setUnitsOrderedNewToBrand(data.getAttributedUnitsOrderedNewToBrand14d());
        vo.setSalesNewToBrand(data.getAttributedSalesNewToBrand14d());
        return vo;
    }

    private AdKeywordAndTargetHourVo baseConvertTo(AmazonMarketingStreamData data) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (data == null) {
            return vo;
        }
        vo.setAdId(data.getAdId());
        vo.setPlacement(data.getPlacement());
        //广告位排序
        if ("Detail Page on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("产品页面");
            vo.setPlacementOrder(3);
        } else if ("Top of Search on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("搜索结果顶部(首页)");
            vo.setPlacementOrder(1);
        } else if ("Other on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacementOrder(2);
            vo.setPlacement("搜索结果的其余位置");
        } else {
            vo.setPlacementOrder(4);
        }
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7d());
        vo.setSelfAdOrderNum(data.getAttributedConversions7dSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7d(), data.getAttributedConversions7dSameSku()));
        vo.setAdSale(data.getAttributedSales7d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(data.getAttributedSales7dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(data.getAttributedSales7d(), data.getAttributedSales7dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7d());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7dSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7d(), data.getAttributedUnitsOrdered7dSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setViewableImpressions(data.getViewableImpressions().intValue());
        vo.setOrdersNewToBrand(data.getAttributedOrdersNewToBrand14d());
        vo.setUnitsOrderedNewToBrand(data.getAttributedUnitsOrderedNewToBrand14d());
        vo.setSalesNewToBrand(data.getAttributedSalesNewToBrand14d());
        return vo;
    }

    private AdKeywordAndTargetHourVo convertToVcpm(AmazonMarketingStreamData data) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (data == null) {
            return vo;
        }
        vo.setAdId(data.getAdId());
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getViewAttributedConversions14d());
        vo.setAdSale(data.getViewAttributedSales14d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(data.getAttributedSales14dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(data.getViewAttributedSales14d(), data.getAttributedSales14dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getViewAttributedUnitsOrdered14d());
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setViewableImpressions(data.getViewImpressions().intValue());
        vo.setOrdersNewToBrand(data.getViewAttributedOrdersNewToBrand14d());
        vo.setUnitsOrderedNewToBrand(data.getViewAttributedUnitsOrderedNewToBrand14d());
        vo.setSalesNewToBrand(data.getViewAttributedSalesNewToBrand14d());
        return vo;
    }

    private Map<Integer, HourlyReportDataPb.HourlyReportData> getIntegerHourlyReportDataMap(
            TargetHourlyReportResponsePb.TargetHourlyReportResponse statisticsByHourResponse,
            Map<Integer, HourlyReportDataPb.HourlyReportData> hourlyReportDataMap) {
        if (statisticsByHourResponse != null && statisticsByHourResponse.getDataCount() > 0) {
            hourlyReportDataMap = statisticsByHourResponse.getDataList().stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, (e1, e2) -> e2));
        }
        return hourlyReportDataMap;
    }

    private Map<Integer, AmazonMarketingStreamData> getIntegerHourlyReportDataMap(
            List<AmazonMarketingStreamData> amazonMarketingStreamDataList,
            Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap) {
        if (CollectionUtils.isNotEmpty(amazonMarketingStreamDataList)) {
            hourlyReportDataMap = amazonMarketingStreamDataList.stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, (e1, e2) -> e2));
        }
        return hourlyReportDataMap;
    }

    private Map<String, AmazonMarketingStreamData> getBidHourlyReportDataMap(
            List<AmazonMarketingStreamData> statisticsByHourResponse,
            Map<String, AmazonMarketingStreamData> hourlyReportDataMap) {
        if (CollectionUtils.isNotEmpty(statisticsByHourResponse)) {
            hourlyReportDataMap = statisticsByHourResponse.stream().collect(Collectors.toMap(e1 -> DateUtil.dateToStrWithTime(e1.getTimeWindowStart(), "yyyy-MM-dd HH"), e1 -> e1));
        }
        return hourlyReportDataMap;
    }

    /**
     * 查询投放小时级数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordAndTargetHourVo> getAllList(int puid, List<ShopAuth> shopAuths, TargetHourParam param) {
        FeedHourlySelectDTO selectDto = buildMultiple(LocalDate.parse(param.getStartDate()),
                LocalDate.parse(param.getEndDate()),
                param.getTargetIds(),
                param.getWeeks(),
                shopAuths
        );
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = getFeedHourlyReport(selectDto, param.getTargetIds());
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            selectDto.setStart(LocalDate.parse(param.getStartDateCompare()));
            selectDto.setEnd(LocalDate.parse(param.getEndDateCompare()));
            hourlyReportDataCompareMap = getFeedHourlyReport(selectDto, param.getTargetIds());
        }
        List<AdKeywordAndTargetHourVo> voList = new ArrayList<>(24);

        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdKeywordAndTargetHourVo adTargetHourVo = handleVo(hour, hourlyReportDataMap.get(hour),
                    hourlyReportDataCompareMap.get(hour));
            voList.add(adTargetHourVo);
        }
        return voList.stream().sorted(Comparator.comparing(AdKeywordAndTargetHourVo::getHour)).collect(Collectors.toList());
    }

    /**
     * 多店铺构造条件
     */
    public static FeedHourlySelectDTO buildMultiple(LocalDate start, LocalDate end, List<String> targetId, String week, List<ShopAuth> shopAuths){
        FeedHourlySelectDTO feedHourlySelectDTO = new FeedHourlySelectDTO();
        if (CollectionUtils.isNotEmpty(targetId)) {
            feedHourlySelectDTO.setKeywordIds(targetId);
        }
        feedHourlySelectDTO.setPuid(shopAuths.get(0).getPuid());
        feedHourlySelectDTO.setStart(start);
        feedHourlySelectDTO.setEnd(end);
        if (StringUtils.isNotBlank(week)) {
            feedHourlySelectDTO.setWeekdayList(StringUtil.splitInt(week));
        }
        feedHourlySelectDTO.setIsCompare(0);
        feedHourlySelectDTO.setShopAuths(shopAuths);
        List<String> sellerIdList = StreamUtil.toListDistinct(shopAuths, ShopAuth::getSellingPartnerId);
        List<String> marketplaceIdList = StreamUtil.toListDistinct(shopAuths, ShopAuth::getMarketplaceId);
        List<String> concatWsList = StreamUtil.toListDistinct(shopAuths, it-> it.getSellingPartnerId()+","+it.getMarketplaceId());
        feedHourlySelectDTO.setSellerIdList(sellerIdList);
        feedHourlySelectDTO.setMarketplaceIdList(marketplaceIdList);
        feedHourlySelectDTO.setConcatWsList(concatWsList);
        return feedHourlySelectDTO;
    }


    private Map<Integer, AmazonMarketingStreamData> getAllTypeFeedHourlyReport(CampaignHourlyReportSelectDto selectDto, List<String> keywordIds) {
        if (CollectionUtils.isEmpty(keywordIds)) {
            return new HashMap<>();
        }
        List<List<AmazonMarketingStreamData>> productHourlyReportResponses = multiThreadQueryAndMergeUtil.multiThreadQuery(() -> keywordIds, selectDto, (x, y) -> callAllTypeFeedHourlyReport(y, x));
        Map<Integer, AmazonMarketingStreamData> reportDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productHourlyReportResponses)) {
            reportDataMap = productHourlyReportResponses.stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport));
        }
        return reportDataMap;
    }

    private List<AmazonMarketingStreamData> callAllTypeFeedHourlyReport(CampaignHourlyReportSelectDto selectDto, List<String> targetIds) {
        CampaignHourlyReportSelectDto queryDto = new CampaignHourlyReportSelectDto();
        BeanUtils.copyProperties(selectDto, queryDto);
        queryDto.setItemIds(targetIds);

        List<AmazonMarketingStreamData> result = Lists.newArrayList();
        List<String> sellerIds = queryDto.getSellerIds();
        List<List<String>> sellerIdPartition = Lists.partition(sellerIds, Constants.DORIS_IN_PARTITION_MAX);
        for (List<String> sellerIdList : sellerIdPartition) {
            queryDto.setSellerIds(sellerIdList);
            List<AmazonMarketingStreamData> data = amazonMarketingStreamDataDao.statisticsAllTypeTargetHourlyReport(queryDto);
            if (CollectionUtils.isNotEmpty(data)) {
                result.addAll(data);
            }
        }
        queryDto.setSellerIds(sellerIds);
        return result;
    }

    private Map<Integer, AmazonMarketingStreamData> getFeedHourlyReport(CampaignHourlyReportSelectDto selectDto, List<String> keywordIds) {
        if (CollectionUtils.isEmpty(keywordIds)) {
            return new HashMap<>();
        }

        List<List<AmazonMarketingStreamData>> productHourlyReportResponses = multiThreadQueryAndMergeUtil.multiThreadQuery(() -> keywordIds, selectDto, (x, y) -> callFeedHourlyReport(y, x));
        Map<Integer, AmazonMarketingStreamData> reportDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productHourlyReportResponses)) {
            reportDataMap = productHourlyReportResponses.stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport));
        }

        return reportDataMap;

    }

    private Map<Integer, AmazonMarketingStreamData> getFeedHourlyReport(FeedHourlySelectDTO selectDto, List<String> keywordIds) {
        if (CollectionUtils.isEmpty(keywordIds)) {
            return new HashMap<>();
        }
        List<List<AmazonMarketingStreamData>> productHourlyReportResponses = multiThreadQueryAndMergeUtil.multiThreadQuery(() -> keywordIds, selectDto, (x, y) -> callFeedHourlyReport(y, x));
        Map<Integer, AmazonMarketingStreamData> reportDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productHourlyReportResponses)) {
            reportDataMap = productHourlyReportResponses.stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport));
        }
        return reportDataMap;
    }

    private Map<Integer, AmazonMarketingStreamData> getSbFeedHourlyReport(FeedHourlySelectDTO builder, List<String> keywordIds) {
        if (CollectionUtils.isEmpty(keywordIds)) {
            return new HashMap<>();
        }

        builder.setKeywordIds(keywordIds);
        List<AmazonMarketingStreamData> productHourlyReportResponses = amazonAdFeedReportService.getHourStreamData(builder);
        Map<Integer, AmazonMarketingStreamData> reportDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productHourlyReportResponses)) {
            reportDataMap = productHourlyReportResponses.stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport));
        }
        return reportDataMap;
    }

    private List<AdKeywordAndTargetHourVo> getSdFeedHourlyReport(TargetHourParam param, List<ShopAuth> shopAuths, List<String> keywordIds) {
        if (CollectionUtils.isEmpty(keywordIds)) {
            return Lists.newArrayList();
        }
        List<AdKeywordAndTargetHourVo> voList = Lists.newArrayList();
        List<List<AdKeywordAndTargetHourVo>> list = Lists.newArrayList();
        List<CompletableFuture<List<AdKeywordAndTargetHourVo>>> completableFutures = new ArrayList<>();
        List<List<String>> queryListPartition = Lists.partition(keywordIds, dynamicRefreshNacosConfiguration.getMAX_CAMPAIGN_SIZE());
        if (queryListPartition.size() == 1) {
            list.add(querySdFeedDataTask(shopAuths, param, keywordIds));
        } else {
            queryListPartition.forEach(e -> {
                completableFutures.add(CompletableFuture.supplyAsync(() -> this.querySdFeedDataTask(shopAuths, param, e),
                        ThreadPoolUtil.getAggregateMultiQueryThreadPool()).exceptionally((exception) -> {
                    log.error("单线程查询sd投放异常:", exception);
                    return null;
                }));
            });
            //阻塞等待
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[completableFutures.size()]));
            try {
                allFutures.get();
            } catch (Exception e) {
                log.error("查询sd小时级数据处理异常, 多线程执行异常", e);
            }
            for (CompletableFuture<List<AdKeywordAndTargetHourVo>> completableFuture : completableFutures) {
                try {
                    list.add(completableFuture.get());
                } catch (Exception e) {
                    log.error("获取sd投放线程结果异常:", e);
                }
            }
        }
        BigDecimal shopSalesByDate = dwsSaleProfitShopDayDao.sumShopSaleByDateRange(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate(), MultipleUtils.changeRate(shopAuths));
        //汇总数据
        voList = ReportChartUtil.getAdTargetHourReportVos(list, shopSalesByDate);
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            filterMetricData(voList, adMetricDto);
        }
        return voList;

    }

    private List<AdKeywordAndTargetHourVo> querySdFeedDataTask(List<ShopAuth> shopAuths, TargetHourParam param, List<String> keywordIds) {
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getShopId(), param.getStartDate().replace("-", ""), param.getEndDate().replace("-", ""));
        FeedHourlySelectDTO builder = new FeedHourlySelectDTO();
        builder.setPuid(shopAuths.get(0).getPuid());
        builder.setShopAuths(shopAuths);
        builder.setSellerIdList(StreamUtil.toListDistinct(shopAuths, ShopAuth::getSellingPartnerId));
        builder.setMarketplaceIdList(StreamUtil.toListDistinct(shopAuths, ShopAuth::getMarketplaceId));
        builder.setConcatWsList(StreamUtil.toListDistinct(shopAuths, it-> it.getSellingPartnerId()+","+it.getMarketplaceId()));
        builder.setStart(LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE));
        builder.setEnd(LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE));
        builder.setIsCompare(param.getIsCompare());
        builder.setType(param.getType());
        if (StringUtils.isNotBlank(param.getWeeks())) {
            builder.setWeekdayList(StringUtil.splitInt(param.getWeeks(), ","));
        }
        builder.setKeywordIds(keywordIds);
        List<String> adSdGroupIdList = amazonSdAdTargetingDao.getAdGroupIdsByTargetIds(param.getPuid(), keywordIds);
        List<String> campaignIdList = amazonSdAdGroupDao.getAdCampaignIdsByGroupIds(param.getPuid(), StreamUtil.toListDistinct(shopAuths, ShopAuth::getId),
                builder.getMarketplaceIdList(), adSdGroupIdList);
        List<FeedTargetCampaignDto> feedTargetCampaignDtoList = amazonAdCampaignAllDao.getFeedTargetListByCampaignIds(param.getPuid(), campaignIdList);
        List<FeedTargetCampaignDto> cpcFeedCampignDtoList = feedTargetCampaignDtoList.stream().filter(e -> Constants.SD_REPORT_CPC.equalsIgnoreCase(e.getCostType())).collect(Collectors.toList());
        List<FeedTargetCampaignDto> vcpmFeedCampignDtoList = feedTargetCampaignDtoList.stream().filter(e -> Constants.SD_REPORT_VCPM.equalsIgnoreCase(e.getCostType())).collect(Collectors.toList());
        List<AdKeywordAndTargetHourVo> voList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(cpcFeedCampignDtoList)) {
            Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = new HashMap<>();
            Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
            builder.setCampaignIds(cpcFeedCampignDtoList.stream().map(FeedTargetCampaignDto::getCampaignId).collect(Collectors.toList()));
            List<AmazonMarketingStreamData> cpcAmazonMarketingStreamDataList = amazonAdFeedReportService.getHourStreamData(builder);
            hourlyReportDataMap = cpcAmazonMarketingStreamDataList.stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport));
            if (Integer.valueOf(1).equals(builder.getIsCompare())) {
                builder.setStart(LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE));
                builder.setEnd(LocalDate.parse(param.getEndDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE));
                List<AmazonMarketingStreamData> cpcAmazonMarketingStreamDataCompareList = amazonAdFeedReportService.getHourStreamData(builder);
                hourlyReportDataCompareMap = cpcAmazonMarketingStreamDataCompareList.stream().collect(Collectors.toMap(e1 -> {
                    LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                    return localTime.getHour();
                }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport));
            }

            for (Integer hour : HourConvert.hourMap.keySet()) {
                AdKeywordAndTargetHourVo adTargetHourVo = handleSdCpcVo(hour, hourlyReportDataMap.get(hour), hourlyReportDataCompareMap.get(hour), shopSalesByDate);
                voList.add(adTargetHourVo);
            }
        }
        if (CollectionUtils.isNotEmpty(vcpmFeedCampignDtoList)) {
            Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = new HashMap<>();
            Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
            builder.setStart(LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE));
            builder.setEnd(LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE));
            builder.setCampaignIds(vcpmFeedCampignDtoList.stream().map(FeedTargetCampaignDto::getCampaignId).collect(Collectors.toList()));
            List<AmazonMarketingStreamData> vcpmAmazonMarketingStreamDataList = amazonAdFeedReportService.getHourStreamData(builder);
            hourlyReportDataMap = vcpmAmazonMarketingStreamDataList.stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport));
            if (Integer.valueOf(1).equals(builder.getIsCompare())) {
                builder.setStart(LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE));
                builder.setEnd(LocalDate.parse(param.getEndDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE));
                List<AmazonMarketingStreamData> vcpmAmazonMarketingStreamDataCompareList = amazonAdFeedReportService.getHourStreamData(builder);
                hourlyReportDataCompareMap = vcpmAmazonMarketingStreamDataCompareList.stream().collect(Collectors.toMap(e1 -> {
                    LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                    return localTime.getHour();
                }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport));
            }
            for (Integer hour : HourConvert.hourMap.keySet()) {
                AdKeywordAndTargetHourVo adTargetHourVo = handleSdVcpmVo(hour, hourlyReportDataMap.get(hour), hourlyReportDataCompareMap.get(hour), shopSalesByDate);
                voList.add(adTargetHourVo);
            }
        }
        return voList;
    }

    private List<AmazonMarketingStreamData> callFeedHourlyReport(CampaignHourlyReportSelectDto selectDto, List<String> targetIds) {
        CampaignHourlyReportSelectDto queryDto = new CampaignHourlyReportSelectDto();
        BeanUtils.copyProperties(selectDto, queryDto);
        queryDto.setItemIds(targetIds);
        return amazonMarketingStreamDataDao.statisticsTargetHourlyReport(queryDto);
    }

    private List<AmazonMarketingStreamData> callFeedHourlyReport(FeedHourlySelectDTO selectDTO, List<String> targetIds) {
        //复制防并发问题
        FeedHourlySelectDTO copySelectDTO = new FeedHourlySelectDTO();
        BeanUtils.copyProperties(selectDTO, copySelectDTO);
        selectDTO.setKeywordIds(targetIds);
        return amazonAdFeedReportService.getHourStreamData(copySelectDTO);
    }


    private HourlyReportDataPb.HourlyReportData aggregationHourlyReport(HourlyReportDataPb.HourlyReportData value1, HourlyReportDataPb.HourlyReportData value2) {
        HourlyReportDataPb.HourlyReportData.Builder newBuilder = HourlyReportDataPb.HourlyReportData.newBuilder();
        newBuilder.setKeywordId(value1.getKeywordId());
        newBuilder.setAdId(value1.getAdId());
        newBuilder.setAdGroupId(value1.getAdGroupId());
        newBuilder.setDate(value1.getDate());
        newBuilder.setKeywordText(value1.getKeywordText());
        newBuilder.setPlacement(value1.getPlacement());
        newBuilder.setTime(value1.getTime());
        newBuilder.setWeekday(value1.getWeekday());

        newBuilder.setAttributedConversions1D(MathUtil.sumInteger(value1.getAttributedConversions1D(), value2.getAttributedConversions1D()));
        newBuilder.setAttributedConversions7D(MathUtil.sumInteger(value1.getAttributedConversions7D(), value2.getAttributedConversions7D()));
        newBuilder.setAttributedConversions14D(MathUtil.sumInteger(value1.getAttributedConversions14D(), value2.getAttributedConversions14D()));
        newBuilder.setAttributedConversions30D(MathUtil.sumInteger(value1.getAttributedConversions30D(), value2.getAttributedConversions30D()));
        newBuilder.setAttributedConversions1DSameSku(MathUtil.sumInteger(value1.getAttributedConversions1DSameSku(), value2.getAttributedConversions1DSameSku()));
        newBuilder.setAttributedConversions7DSameSku(MathUtil.sumInteger(value1.getAttributedConversions7DSameSku(), value2.getAttributedConversions7DSameSku()));
        newBuilder.setAttributedConversions14DSameSku(MathUtil.sumInteger(value1.getAttributedConversions14DSameSku(), value2.getAttributedConversions14DSameSku()));
        newBuilder.setAttributedConversions30DSameSku(MathUtil.sumInteger(value1.getAttributedConversions30DSameSku(), value2.getAttributedConversions30DSameSku()));

        newBuilder.setAttributedSales1D(MathUtil.sum(value1.getAttributedSales1D(), value2.getAttributedSales1D()));
        newBuilder.setAttributedSales7D(MathUtil.sum(value1.getAttributedSales7D(), value2.getAttributedSales7D()));
        newBuilder.setAttributedSales14D(MathUtil.sum(value1.getAttributedSales14D(), value2.getAttributedSales14D()));
        newBuilder.setAttributedSales30D(MathUtil.sum(value1.getAttributedSales30D(), value2.getAttributedSales30D()));
        newBuilder.setAttributedSales1DSameSku(MathUtil.sum(value1.getAttributedSales1DSameSku(), value2.getAttributedSales1DSameSku()));
        newBuilder.setAttributedSales7DSameSku(MathUtil.sum(value1.getAttributedSales7DSameSku(), value2.getAttributedSales7DSameSku()));
        newBuilder.setAttributedSales14DSameSku(MathUtil.sum(value1.getAttributedSales14DSameSku(), value2.getAttributedSales14DSameSku()));
        newBuilder.setAttributedSales30DSameSku(MathUtil.sum(value1.getAttributedSales30DSameSku(), value2.getAttributedSales30DSameSku()));

        newBuilder.setAttributedUnitsOrdered1D(MathUtil.sumInteger(value1.getAttributedUnitsOrdered1D(), value2.getAttributedUnitsOrdered1D()));
        newBuilder.setAttributedUnitsOrdered7D(MathUtil.sumInteger(value1.getAttributedUnitsOrdered7D(), value2.getAttributedUnitsOrdered7D()));
        newBuilder.setAttributedUnitsOrdered14D(MathUtil.sum(value1.getAttributedUnitsOrdered14D(), value2.getAttributedUnitsOrdered14D()));
        newBuilder.setAttributedUnitsOrdered30D(MathUtil.sumInteger(value1.getAttributedUnitsOrdered30D(), value2.getAttributedUnitsOrdered30D()));
        newBuilder.setAttributedUnitsOrdered1DSameSku(MathUtil.sumInteger(value1.getAttributedUnitsOrdered1DSameSku(), value2.getAttributedUnitsOrdered1DSameSku()));
        newBuilder.setAttributedUnitsOrdered7DSameSku(MathUtil.sumInteger(value1.getAttributedUnitsOrdered7DSameSku(), value2.getAttributedUnitsOrdered7DSameSku()));
        newBuilder.setAttributedUnitsOrdered14DSameSku(MathUtil.sumInteger(value1.getAttributedUnitsOrdered14DSameSku(), value2.getAttributedUnitsOrdered14DSameSku()));
        newBuilder.setAttributedUnitsOrdered30DSameSku(MathUtil.sumInteger(value1.getAttributedUnitsOrdered30DSameSku(), value2.getAttributedUnitsOrdered30DSameSku()));


        newBuilder.setClicks(MathUtil.sumLong(value1.getClicks(), value2.getClicks()));
        newBuilder.setCost(MathUtil.sum(value1.getCost(), value2.getCost()));
        newBuilder.setImpressions(MathUtil.sumLong(value1.getImpressions(), value2.getImpressions()));

        return newBuilder.build();

    }


    @Override
    public List<AdKeywordAndTargetHourVo> getDailyAllList(int puid, List<ShopAuth> shopAuths, String adType, TargetHourParam param, List<AdKeywordAndTargetHourVo> compares) {
        List<AdKeywordAndTargetHourVo> reports = getAdTargetDailyAllReports(puid, shopAuths, adType, param, false);
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            List<AdKeywordAndTargetHourVo> compareList = getAdTargetDailyAllReports(puid, shopAuths, adType, param, true);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            return paddingDayCompare(param, reports, compareList);
        } else {
            return reports;
        }
    }


    private List<AdKeywordAndTargetHourVo> getAdTargetDailyAllReports(int puid, List<ShopAuth> shopAuths, String adType, TargetHourParam param, boolean isCompare) {
        if (CollectionUtils.isEmpty(param.getTargetIds())) {
            return new ArrayList<>();
        }
        LocalDate start = LocalDate.parse(param.getStartDate());
        LocalDate end = LocalDate.parse(param.getEndDate());
        if (isCompare) {
            start = LocalDate.parse(param.getStartDateCompare());
            end = LocalDate.parse(param.getEndDateCompare());
        }
        List<Integer> shopIds = shopAuths.stream().map(ShopAuth::getId).collect(Collectors.toList());
        List<String> marketplaceIds = shopAuths.stream().map(ShopAuth::getMarketplaceId).distinct().collect(Collectors.toList());
        //SP
        if ("SP".equalsIgnoreCase(adType)) {
            List<OdsCpcTargetingReport> reports = odsCpcTargetingReportDao.getSumReportByTargetIdsByCountDate(puid, shopIds, marketplaceIds,
                    start.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                    end.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), param.getTargetIds(), MultipleUtils.changeRate(shopAuths), AmznEndpoint.US.getCurrencyCode().value());
            return reports.stream().map(item -> {
                AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(DateUtil.dateToStrWithFormat(item.getCountDay(), DateUtil.PATTERN));
                vo.setDate(vo.getLabel());
                vo.setAdSale(BigDecimal.valueOf(item.getTotalSales()));
                vo.setAdSelfSale(BigDecimal.valueOf(item.getAdSales()));
                vo.setAdOtherSale(MathUtil.subtract(vo.getAdSale(), vo.getAdSelfSale()));
                vo.setAdOrderNum(item.getSaleNum());
                vo.setSelfAdOrderNum(item.getAdSaleNum());
                vo.setOtherAdOrderNum(MathUtil.subtractInteger(item.getSaleNum(), item.getAdSaleNum()));
                vo.setAdSaleNum(item.getOrderNum());
                vo.setAdSelfSaleNum(item.getAdOrderNum());
                vo.setAdOtherSaleNum(MathUtil.subtractInteger(item.getOrderNum(), item.getAdOrderNum()));
                vo.setAdCost(BigDecimal.valueOf(item.getCost()));
                vo.setClicks(Long.valueOf(item.getClicks()));
                vo.setImpressions(Long.valueOf(item.getImpressions()));
                vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
                vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
                vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
                vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
                vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
                vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
                return vo;
            }).collect(Collectors.toList());
        } else if ("SB".equalsIgnoreCase(adType)) {
            List<OdsAmazonAdSbTargetingReport> reports = odsAmazonAdSbTargetingReportDao.getReportByTargetIdsByCountDate(puid, shopIds, marketplaceIds,
                    start.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                    end.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), param.getTargetIds(), MultipleUtils.changeRate(shopAuths), AmznEndpoint.US.getCurrencyCode().value());
            List<AdKeywordAndTargetHourVo> adKeywordAndTargetHourVoList = reports.stream().map(item -> {
                AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(DateUtil.dateToStrWithFormat(item.getCountDay(), DateUtil.PATTERN));
                vo.setDate(vo.getLabel());
                vo.setAdSale(BigDecimal.valueOf(item.getSales14d()));
                vo.setAdSelfSale(BigDecimal.valueOf(item.getSales14dSameSku()));
                vo.setAdOtherSale(MathUtil.subtract(vo.getAdSale(), vo.getAdSelfSale()));
                vo.setAdOrderNum(item.getConversions14d());
                vo.setSelfAdOrderNum(item.getConversions14dSameSku());
                vo.setOtherAdOrderNum(item.getConversions14d() - item.getConversions14dSameSku());
                vo.setAdSaleNum(Optional.ofNullable(item.getUnitsSold14d()).orElse(0));
                vo.setAdCost(BigDecimal.valueOf(item.getCost()));
                vo.setClicks(Long.valueOf(item.getClicks()));
                vo.setImpressions(Long.valueOf(item.getImpressions()));
                vo.setViewableImpressions(item.getViewableImpressions());
//                vo.setOrdersNewToBrand(item.getOrdersNewToBrand14d());
//                vo.setUnitsOrderedNewToBrand(item.getUnitsOrderedNewToBrand14d());
//                vo.setSalesNewToBrand(BigDecimal.valueOf(item.getSalesNewToBrand14d()));
                vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
                vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
                vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
                vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
                vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
                vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
                return vo;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(adKeywordAndTargetHourVoList)) {
                //新增占比数据指标
                AdMetricDto adMetricDto = new AdMetricDto();
                filterSumMetricData(adKeywordAndTargetHourVoList, adMetricDto);
                filterMetricData(adKeywordAndTargetHourVoList, adMetricDto);
            }
            return adKeywordAndTargetHourVoList;
        } else if ("SD".equalsIgnoreCase(adType)) {
            List<TargetReportHourlyDTO> reports = odsAmazonAdSdTargetingReportDao.getReportByTargetId(puid, shopIds, marketplaceIds,
                    start.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                    end.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), param.getTargetIds(), MultipleUtils.changeRate(shopAuths), AmznEndpoint.US.getCurrencyCode().value());

            List<AdKeywordAndTargetHourVo> adKeywordAndTargetHourVoList = reports.stream().map(item -> {
                AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                        .format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
                vo.setAdSale(Optional.ofNullable(item.getTotalSales()).orElse(BigDecimal.ZERO));
                vo.setAdSelfSale(Optional.ofNullable(item.getAdSales()).orElse(BigDecimal.ZERO));
                vo.setAdOtherSale(MathUtil.subtract(item.getTotalSales(), item.getAdSales()));
                vo.setAdOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
                vo.setAdSaleNum(Optional.ofNullable(item.getSaleNum()).orElse(0));
                vo.setAdCost(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO));
                vo.setClicks(Long.valueOf(item.getClicks()));
                vo.setImpressions(Long.valueOf(item.getImpressions()));
                vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
                vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
                vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
                vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
                vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
                vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));

                // 新加字段
//                vo.setOrdersNewToBrand(Optional.ofNullable(item.getOrdersNewToBrand14d()).orElse(0));
//                vo.setUnitsOrderedNewToBrand(Optional.ofNullable(item.getUnitsOrderedNewToBrand14d()).orElse(0));
//                vo.setSalesNewToBrand(Optional.ofNullable(item.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO));
//
//
//                vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
//                if (Constants.SD_REPORT_CPC.equals(item.getCostType())) {
//                    vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
//                    vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getAdOrderNum() - vo.getSelfAdOrderNum())));
//                } else {
//                    //新增字段
//                    vo.setViewableImpressions(Optional.ofNullable(item.getViewableImpressions()).orElse(0));
//                    vo.setVrt(vo.getImpressions() == 0 || vo.getViewableImpressions() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(vo.getViewableImpressions()).divide(BigDecimal.valueOf(vo.getImpressions()), 2, RoundingMode.HALF_UP));
//                    vo.setVCtr(vo.getViewableImpressions() == 0 || vo.getClicks() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(vo.getClicks()).divide(BigDecimal.valueOf(vo.getViewableImpressions()), 2, RoundingMode.HALF_UP));
//                    vo.setVcpm(MathUtil.divideByThousand(item.getVcpmCost(), item.getViewableImpressions()));
//                }
                vo.setSelfAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
                vo.setOtherAdOrderNum(MathUtil.subtractInteger(item.getOrderNum(), item.getAdOrderNum()));
//                vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(item.getOrdersNewToBrand14d()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
//                vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(item.getUnitsOrderedNewToBrand14d()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
//                vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));

                return vo;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(adKeywordAndTargetHourVoList)) {
                //新增占比数据指标
                AdMetricDto adMetricDto = new AdMetricDto();
                filterSumMetricData(adKeywordAndTargetHourVoList, adMetricDto);
                filterMetricData(adKeywordAndTargetHourVoList, adMetricDto);
            }
            return adKeywordAndTargetHourVoList;
        }
        return null;
    }

    @Override
    public List<AdKeywordAndTargetHourVo> getWeeklyAllList(int puid, List<ShopAuth> shopAuths, String adType, TargetHourParam param, List<AdKeywordAndTargetHourVo> compares) {
        List<AdKeywordAndTargetHourVo> reports = ReportChartUtil.getWeekReportVos(param.getStartDate(), param.getEndDate(),
                getAdTargetDailyAllReports(puid, shopAuths, adType, param, false));
        if (Integer.valueOf(1).equals(param.getIsCompare())){
            List<AdKeywordAndTargetHourVo> compareReports = ReportChartUtil.getWeekReportVos(param.getStartDateCompare(), param.getEndDateCompare(),
                    getAdTargetDailyAllReports(puid, shopAuths, adType, param, true));
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareReports)) {
                compares.addAll(compareReports);
            }
            return paddingWeekCompare(reports, compareReports);
        }else {
            return reports;
        }
    }

    @Override
    public List<AdKeywordAndTargetHourVo> getMonthlyAllList(int puid, List<ShopAuth> shopAuths, String adType, TargetHourParam param, List<AdKeywordAndTargetHourVo> compares) {
        List<AdKeywordAndTargetHourVo> reportVos = ReportChartUtil.getMonthReportVos(getAdTargetDailyAllReports(puid, shopAuths, adType, param, false));
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            List<AdKeywordAndTargetHourVo> compareReports = ReportChartUtil.getMonthReportVos(getAdTargetDailyAllReports(puid, shopAuths, adType, param, true));
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareReports)) {
                compares.addAll(compareReports);
            }
            return paddingMonthCompare(param, reportVos, compareReports);
        } else {
            return reportVos;
        }
    }

    /**
     * 查询广告位维度小时级数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordTargetHourOfPlacementVo> getListOfPlacementAll(int puid, List<ShopAuth> shopAuths, TargetHourParam param) {
        return convertToHourOfPlacementVos(getDetailListOfPlacementAll(puid, shopAuths, param));
    }

    /**
     * 查询广告位维度小时级明细数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordAndTargetHourVo> getDetailListOfPlacementAll(int puid, List<ShopAuth> shopAuths, TargetHourParam param) {
        //获取小时级数据
        FeedHourlySelectDTO selectDto = buildMultiple(LocalDate.parse(param.getStartDate()),
                LocalDate.parse(param.getEndDate()),
                null,
                param.getWeeks(),
                shopAuths
        );

        //pb对象转vo对象
        List<AmazonMarketingStreamData> dataList = getFeedHourlyOfPlacementReport(selectDto, param.getTargetIds());

        List<AdKeywordAndTargetHourVo> voList = dataList.stream().map(this::spConvertTo).collect(Collectors.toList());
        //填充无数据的时间段
        Map<String, List<AdKeywordAndTargetHourVo>> voMap =
                voList.stream().collect(Collectors.groupingBy(AdKeywordAndTargetHourVo::getPlacement));
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();

        voMap.forEach((k, v) -> {
            List<String> hourList = v.stream().map(AdKeywordAndTargetHourVo::getLabel).collect(Collectors.toList());
            List<String> needFilledHour = allHours.stream()
                    .filter(item -> !hourList.contains(item)).collect(Collectors.toList());
            for (String hour : needFilledHour) {
                AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(hour);
                vo.setHour(Integer.valueOf(hour.split("-")[0]));
                vo.setPlacement(k);
                //广告位排序
                if ("产品页面".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(3);
                } else if ("搜索结果顶部(首页)".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(1);
                } else if ("搜索结果的其余位置".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(2);
                } else {
                    vo.setPlacementOrder(4);
                }
                voList.add(vo);
            }
        });

        return voList.stream().sorted(Comparator.comparingInt(AdKeywordAndTargetHourVo::getPlacementOrder)
                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());
    }

    @Override
    public List<AdKeywordAndTargetHourVo> getViewHourListAll(int puid, TargetViewHourParam param) {
        List<ShopAuth> shopAuths = shopAuthDao.listByPuidAndMarketplace(puid, Collections.singletonList(param.getMarketplaceId()), param.getShopIdList());
        if (CollectionUtils.isEmpty(shopAuths)) {
            return new ArrayList<>();
        }
        CampaignHourlyReportSelectDto builder = new CampaignHourlyReportSelectDto();
        List<String> sellerIds = shopAuths.stream().map(ShopAuth::getSellingPartnerId).collect(Collectors.toList());
        builder.setSellerIds(sellerIds);
        builder.setMarketplaceId(param.getMarketplaceId());
        builder.setStartDate(amazonAdFeedReportService.getSellerIdsDataStartTime(sellerIds, param.getMarketplaceId(), param.getStartDate()));
        builder.setEndDate(param.getEndDate());
        List<Integer> weekdayList = Optional.of(param.getWeeks()).filter(StringUtils::isNotBlank).map(w -> StringUtil.splitInt(w, ",")).orElse(HourConvert.weeKs);
        builder.setWeekdayList(weekdayList);
        if (CollectionUtils.isNotEmpty(param.getAdIdList())) {
            builder.setAdIds(param.getAdIdList());
        }

        //判断是否有类目或asin值，商品投放才会传
        if (StringUtils.isNotBlank(param.getAggregateType())) {
            //若为商品投放某个类目或asin的汇总，先获取该投放的targetIds与总汇总id取交集
            List<String> targetIds = amazonAdTargetDaoRoutingService.getTargetIdListByTargetViewHourParam(puid, param, false);
            if (CollectionUtils.isEmpty(targetIds) || CollectionUtils.isEmpty(param.getTargetIdList())) {
                param.setTargetIdList(new ArrayList<>());
            } else {
                targetIds.retainAll(param.getTargetIdList());
                param.setTargetIdList(targetIds);
            }
        }

        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = getFeedHourlyReport(builder, param.getTargetIdList());
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            builder.setStartDate(amazonAdFeedReportService.getSellerIdsDataStartTime(sellerIds, param.getMarketplaceId(), param.getStartDateCompare()));
            builder.setEndDate(param.getEndDateCompare());
            hourlyReportDataCompareMap = getFeedHourlyReport(builder, param.getTargetIdList());
        }
        List<AdKeywordAndTargetHourVo> voList = new ArrayList<>(24);

        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdKeywordAndTargetHourVo adTargetHourVo = handleVo(hour, hourlyReportDataMap.get(hour),
                    hourlyReportDataCompareMap.get(hour));
            voList.add(adTargetHourVo);
        }
        return voList.stream().sorted(Comparator.comparing(AdKeywordAndTargetHourVo::getHour)).collect(Collectors.toList());
    }

    @Override
    public List<AdKeywordAndTargetHourVo> getViewHourListAllType(int puid, TargetViewHourParam param) {
        List<ShopAuth> shopAuths = shopAuthDao.listByPuidAndMarketplace(puid, Collections.singletonList(param.getMarketplaceId()), param.getShopIdList());
        if (CollectionUtils.isEmpty(shopAuths)) {
            return new ArrayList<>();
        }
        CampaignHourlyReportSelectDto builder = new CampaignHourlyReportSelectDto();
        List<String> sellerIds = shopAuths.stream().map(ShopAuth::getSellingPartnerId).collect(Collectors.toList());
        builder.setSellerIds(sellerIds);
        builder.setMarketplaceId(param.getMarketplaceId());
        builder.setStartDate(amazonAdFeedReportService.getSellerIdsDataStartTime(sellerIds, param.getMarketplaceId(), param.getStartDate()));
        builder.setEndDate(param.getEndDate());
        List<Integer> weekdayList = Optional.of(param.getWeeks()).filter(StringUtils::isNotBlank).map(w -> StringUtil.splitInt(w, ",")).orElse(HourConvert.weeKs);
        builder.setWeekdayList(weekdayList);
        if (CollectionUtils.isNotEmpty(param.getAdIdList())) {
            builder.setAdIds(param.getAdIdList());
        }

        //判断是否有类目或asin值，商品投放才会传
        if (StringUtils.isNotBlank(param.getAggregateType())) {
            List<String> targetIds = new ArrayList<>();
            if (StringUtils.isNotBlank(param.getTargetText())) {
                //受众投放
                AudienceTargetViewParam targetViewParam = this.buildAudienceTargetViewParamByHourParam(param);
                targetIds.addAll(amazonSdAdTargetingDao.getAudienceTargetViewIdList(puid, targetViewParam));
            } else {
                //商品投放
                TargetViewParam targetViewParam = this.buildTargetViewParamByHourParam(param);
                //若为商品投放某个类目或asin的汇总，先获取该投放的targetIds与总汇总id取交集
                List<String> typeList = StringUtil.splitStr(param.getAdType()).stream().map(String::toLowerCase).collect(Collectors.toList());
                if (typeList.contains(Constants.SP)) {
                    targetIds.addAll(amazonAdTargetDaoRoutingService.getTargetViewIdList(puid, targetViewParam, false));
                }
                if (typeList.contains(Constants.SB)) {
                    targetIds.addAll(amazonSbAdTargetingDao.getTargetViewIdList(puid, targetViewParam));
                }
                if (typeList.contains(Constants.SD)) {
                    targetIds.addAll(amazonSdAdTargetingDao.getAsinTargetViewIdList(puid, targetViewParam));
                }
            }
            if (CollectionUtils.isEmpty(targetIds) || CollectionUtils.isEmpty(param.getTargetIdList())) {
                param.setTargetIdList(new ArrayList<>());
            } else {
                targetIds.retainAll(param.getTargetIdList());
                param.setTargetIdList(targetIds);
            }
        }

        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = this.getAllTypeFeedHourlyReport(builder, param.getTargetIdList());
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            builder.setStartDate(amazonAdFeedReportService.getSellerIdsDataStartTime(sellerIds, param.getMarketplaceId(), param.getStartDateCompare()));
            builder.setEndDate(param.getEndDateCompare());
            hourlyReportDataCompareMap = this.getAllTypeFeedHourlyReport(builder, param.getTargetIdList());
        }
        List<AdKeywordAndTargetHourVo> voList = new ArrayList<>(24);
        //获取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(puid, param.getShopIdList(), param.getStartDate(), param.getEndDate());
        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdKeywordAndTargetHourVo adTargetHourVo = this.handleBaseVo(hour, hourlyReportDataMap.get(hour),
                    hourlyReportDataCompareMap.get(hour), shopSalesByDate);
            voList.add(adTargetHourVo);
        }
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            filterMetricData(voList, adMetricDto);
        }
        return voList.stream().sorted(Comparator.comparing(AdKeywordAndTargetHourVo::getHour)).collect(Collectors.toList());
    }

    @Override
    public List<AdKeywordAndTargetHourVo> getSbAndSdHourList(int puid, TargetHourParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getShopId(), param.getStartDate().replace("-", ""), param.getEndDate().replace("-", ""));
        FeedHourlySelectDTO builder = new FeedHourlySelectDTO();
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setMarketplaceId(shopAuth.getMarketplaceId());
        builder.setStart(LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE));
        builder.setEnd(LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE));
        builder.setType(param.getType());
        if (StringUtils.isNotBlank(param.getWeeks())) {
            builder.setWeekdayList(StringUtil.splitInt(param.getWeeks(), ","));
        }
        if (StringUtils.isNotBlank(param.getTargetId())) {
            List<String> targetIds = Lists.newArrayList();
            targetIds.add(param.getTargetId());
            builder.setKeywordIds(targetIds);
        }
        //产品透视需要查adid再过滤一层
        if (StringUtils.isNotBlank(param.getFindType()) && StringUtils.isNotBlank(param.getFindValue())) {
            ViewBaseParam viewBaseParam = new ViewBaseParam();
            viewBaseParam.setPuid(shopAuth.getPuid());
            viewBaseParam.setShopIdList(Collections.singletonList(shopAuth.getId()));
            viewBaseParam.setMarketplaceId(shopAuth.getMarketplaceId());
            viewBaseParam.setSearchType(param.getFindType());
            viewBaseParam.setSearchValue(param.getFindValue());
            viewBaseParam.setType(param.getType());
            List<String> adIds = viewManageHandler.queryProductBoLists(viewBaseParam.getPuid(), viewBaseParam)
                    .stream().map(AmazonAdProductPerspectiveBO::getAdId).distinct().collect(Collectors.toList());
            builder.setAdIds(adIds);
        }
        List<AmazonMarketingStreamData> amazonMarketingStreamDataList = amazonMarketingStreamDataDao.listByKeywordHourly(builder);
        String campaignId = null;
        if (Constants.SB.equalsIgnoreCase(param.getType())) {
            AmazonSbAdTargeting amazonSbAdTargeting = amazonSbAdTargetingDao.getByTargetId(param.getPuid(), param.getShopId(), param.getTargetId());
            campaignId = amazonSbAdTargeting.getCampaignId();
        } else {
            AmazonSdAdTargeting amazonSdAdTargeting = amazonSdAdTargetingDao.getbyTargetId(param.getPuid(), param.getShopId(), param.getTargetId());
            AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(param.getPuid(), param.getShopId(), amazonSdAdTargeting.getAdGroupId());
            campaignId = amazonSdAdGroup.getCampaignId();
        }
        AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllDao.getByCampaignId(param.getPuid(), param.getShopId(), campaignId);
        List<AmazonMarketingStreamData> compareResponse = null;
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = new HashMap<>();
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            builder.setStart(LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE));
            builder.setEnd(LocalDate.parse(param.getEndDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE));
            compareResponse = amazonMarketingStreamDataDao.listByKeywordHourly(builder);
            hourlyReportDataCompareMap = getIntegerHourlyReportDataMap(compareResponse, hourlyReportDataCompareMap);
        }
        hourlyReportDataMap = getIntegerHourlyReportDataMap(amazonMarketingStreamDataList, hourlyReportDataMap);
        List<AdKeywordAndTargetHourVo> voList = new ArrayList<>(24);
        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdKeywordAndTargetHourVo adTargetHourVo = null;
            if (Constants.SB.equalsIgnoreCase(param.getType())) {
                adTargetHourVo = handleSbVo(hour, hourlyReportDataMap.get(hour), hourlyReportDataCompareMap.get(hour), shopSalesByDate);
            } else {
                if (Constants.SD_REPORT_CPC.equalsIgnoreCase(amazonAdCampaignAll.getCostType())) {
                    adTargetHourVo = handleSdCpcVo(hour, hourlyReportDataMap.get(hour), hourlyReportDataCompareMap.get(hour), shopSalesByDate);
                } else if (Constants.SD_REPORT_VCPM.equalsIgnoreCase(amazonAdCampaignAll.getCostType())) {
                    adTargetHourVo = handleSdVcpmVo(hour, hourlyReportDataMap.get(hour), hourlyReportDataCompareMap.get(hour), shopSalesByDate);
                } else {
                    adTargetHourVo = handleSdCpcVo(hour, hourlyReportDataMap.get(hour), hourlyReportDataCompareMap.get(hour), shopSalesByDate);
                }
            }
            adTargetHourVo.setCostType(amazonAdCampaignAll.getCostType());
            voList.add(adTargetHourVo);
        }
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            filterMetricData(voList, adMetricDto);
        }
        return voList;
    }

    @Override
    public List<AdKeywordAndTargetHourVo> getSbHourAllList(int puid, List<ShopAuth> shopAuths, TargetHourParam param) {
        FeedHourlySelectDTO builder = new FeedHourlySelectDTO();
        builder.setPuid(puid);
        builder.setShopAuths(shopAuths);
        builder.setSellerIdList(StreamUtil.toListDistinct(shopAuths, ShopAuth::getSellingPartnerId));
        builder.setMarketplaceIdList(StreamUtil.toListDistinct(shopAuths, ShopAuth::getMarketplaceId));
        builder.setConcatWsList(StreamUtil.toListDistinct(shopAuths, it-> it.getSellingPartnerId()+","+it.getMarketplaceId()));
        builder.setStart(LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE));
        builder.setEnd(LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE));
        builder.setType(param.getType());
        if (StringUtils.isNotBlank(param.getWeeks())) {
            builder.setWeekdayList(StringUtil.splitInt(param.getWeeks(), ","));
        }
        BigDecimal shopSalesByDate = dwsSaleProfitShopDayDao.sumShopSaleByDateRange(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate(), MultipleUtils.changeRate(shopAuths));
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = getSbFeedHourlyReport(builder, param.getTargetIds());
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            builder.setStart(LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE));
            builder.setEnd(LocalDate.parse(param.getEndDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE));
            hourlyReportDataCompareMap = getSbFeedHourlyReport(builder, param.getTargetIds());
        }
        List<AdKeywordAndTargetHourVo> voList = new ArrayList<>(24);

        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdKeywordAndTargetHourVo adTargetHourVo = handleSbVo(hour, hourlyReportDataMap.get(hour), hourlyReportDataCompareMap.get(hour), shopSalesByDate);
            voList.add(adTargetHourVo);
        }
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            filterMetricData(voList, adMetricDto);
        }
        return voList.stream().sorted(Comparator.comparing(AdKeywordAndTargetHourVo::getHour)).collect(Collectors.toList());
    }

    @Override
    public List<AdKeywordAndTargetHourVo> getSdHourAllList(int puid, List<ShopAuth> shopAuths, TargetHourParam param) {
        List<AdKeywordAndTargetHourVo> voList = getSdFeedHourlyReport(param, shopAuths, param.getTargetIds());
//        if (CollectionUtils.isNotEmpty(voList)) {
//            //新增占比数据指标
//            AdMetricDto adMetricDto = new AdMetricDto();
//            filterSumMetricData(voList, adMetricDto);
//            filterMetricData(voList, adMetricDto);
//        }
        return voList.stream().sorted(Comparator.comparing(AdKeywordAndTargetHourVo::getHour)).collect(Collectors.toList());
    }

    private List<AmazonMarketingStreamData> getFeedHourlyOfPlacementReport(CampaignHourlyReportSelectDto builder, List<String> targetIds) {
        if (CollectionUtils.isEmpty(targetIds)) {
            return new ArrayList<>();
        }
        List<AmazonMarketingStreamData> result = new ArrayList<>();
        List<List<AmazonMarketingStreamData>> productHourlyReportResponses = multiThreadQueryAndMergeUtil.multiThreadQuery(() -> targetIds, builder, (x, y) -> callFeedHourlyOfPlacementReport(y, x));
        if (CollectionUtils.isNotEmpty(productHourlyReportResponses)) {
            Collection<AmazonMarketingStreamData> values = productHourlyReportResponses.stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour() + "&&" + e1.getPlacement();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport)).values();
            if (CollectionUtils.isNotEmpty(values)) {
                result = Lists.newArrayList(values);
            }
        }
        return result;


    }

    private List<AmazonMarketingStreamData> getFeedHourlyOfPlacementReport(FeedHourlySelectDTO builder, List<String> targetIds) {
        if (CollectionUtils.isEmpty(targetIds)) {
            return new ArrayList<>();
        }
        List<AmazonMarketingStreamData> result = new ArrayList<>();
        List<List<AmazonMarketingStreamData>> productHourlyReportResponses = multiThreadQueryAndMergeUtil.multiThreadQuery(() -> targetIds, builder, (x, y) -> callFeedHourlyOfPlacementReport(y, x));
        if (CollectionUtils.isNotEmpty(productHourlyReportResponses)) {
            Collection<AmazonMarketingStreamData> values = productHourlyReportResponses.stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour() + "&&" + e1.getPlacement();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport)).values();
            if (CollectionUtils.isNotEmpty(values)) {
                result = Lists.newArrayList(values);
            }
        }
        return result;
    }

    private List<AmazonMarketingStreamData> callFeedHourlyOfPlacementReport(CampaignHourlyReportSelectDto builder, List<String> targetIds) {
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        BeanUtils.copyProperties(builder, selectDto);
        selectDto.setItemIds(targetIds);
        return amazonMarketingStreamDataDao.statisticsTargetHourlyReportOfPlacement(selectDto);
    }

    private List<AmazonMarketingStreamData> callFeedHourlyOfPlacementReport(FeedHourlySelectDTO builder, List<String> targetIds) {
        FeedHourlySelectDTO selectDto = new FeedHourlySelectDTO();
        BeanUtils.copyProperties(builder, selectDto);
        selectDto.setKeywordIds(targetIds);
        return amazonAdFeedReportService.getTargetOfPlacementStreamData(selectDto);
    }

    private Map<Integer, HourlyReportDataPb.HourlyReportData> getFeedHourlyReport(AggregateTargetHourlyReportRequestPb.AggregateTargetHourlyReportRequest.Builder builder, List<String> keywordIds) {
        if (CollectionUtils.isEmpty(keywordIds) || CollectionUtils.isEmpty(builder.getAdIdList())) {
            return new HashMap<>();
        }
        List<AggregateTargetHourlyReportResponsePb.AggregateTargetHourlyReportResponse> productHourlyReportResponses = multiThreadQueryAndMergeUtil.multiThreadQuery(() -> keywordIds, builder, (x, y) -> callFeedHourlyReport(y, x));
        Map<Integer, HourlyReportDataPb.HourlyReportData> reportDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productHourlyReportResponses)) {
            reportDataMap = productHourlyReportResponses.stream().filter(Objects::nonNull).filter(e -> e.getDataCount() > 0).map(AggregateTargetHourlyReportResponsePb.AggregateTargetHourlyReportResponse::getDataList).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport));
        }
        return reportDataMap;

    }

    private AggregateTargetHourlyReportResponsePb.AggregateTargetHourlyReportResponse callFeedHourlyReport(AggregateTargetHourlyReportRequestPb.AggregateTargetHourlyReportRequest.Builder builder, List<String> targetIds) {
        builder.addAllTargetId(targetIds);
        AggregateTargetHourlyReportRequestPb.AggregateTargetHourlyReportRequest.Builder sendBuilder = AggregateTargetHourlyReportRequestPb.AggregateTargetHourlyReportRequest.newBuilder();
        sendBuilder.addAllSellerId(builder.getSellerIdList());
        sendBuilder.setMarketplaceId(builder.getMarketplaceId());
        sendBuilder.setStartDate(builder.getStartDate());
        sendBuilder.setEndDate(builder.getEndDate());
        if (CollectionUtils.isNotEmpty(builder.getWeekdayList())) {
            sendBuilder.addAllWeekday(builder.getWeekdayList());
        }
        sendBuilder.addAllTargetId(targetIds);
        sendBuilder.addAllAdId(builder.getAdIdList());
        return adFeedBlockingStub.aggregateTargetHourlyReport(sendBuilder.build());
    }

    private void filterSumMetricData(List<AdKeywordAndTargetHourVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        for (AdKeywordAndTargetHourVo vo : voList) {
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdCost())).
                    map(AdKeywordAndTargetHourVo::getAdCost).ifPresent(v -> adMetricDto.setSumCost(Optional.ofNullable(adMetricDto.getSumCost()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdSale())).
                    map(AdKeywordAndTargetHourVo::getAdSale).ifPresent(v -> adMetricDto.setSumAdSale(Optional.ofNullable(adMetricDto.getSumAdSale()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdOrderNum())).
                    map(AdKeywordAndTargetHourVo::getAdOrderNum).map(BigDecimal::valueOf).ifPresent(v -> adMetricDto.setSumAdOrderNum(Optional.ofNullable(adMetricDto.getSumAdOrderNum()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdSaleNum())).
                    map(AdKeywordAndTargetHourVo::getAdSaleNum).map(BigDecimal::valueOf).ifPresent(v -> adMetricDto.setSumOrderNum(Optional.ofNullable(adMetricDto.getSumOrderNum()).orElse(BigDecimal.ZERO).add(v)));
        }
    }

    // 填充指标占比数据
    private void filterMetricData(List<AdKeywordAndTargetHourVo> voList, AdMetricDto adMetricDto) {
        for (AdKeywordAndTargetHourVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                continue;
            }
            computeMetricData(adMetricDto, vo);
        }
    }

    private void computeMetricData(AdMetricDto adMetricDto, AdKeywordAndTargetHourVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdCost().toString(), adMetricDto.getSumCost().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSale().toString(), adMetricDto.getSumAdSale().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getAdSaleNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSaleNum().toString(), adMetricDto.getSumOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }
    }

    private TargetViewParam buildTargetViewParamByHourParam(TargetViewHourParam param) {
        TargetViewParam targetViewParam = new TargetViewParam();
        targetViewParam.setShopIdList(param.getShopIdList());
        targetViewParam.setTargetType(param.getTargetType());
        targetViewParam.setCampaignId(param.getCampaignId());
        targetViewParam.setTargetIdList(param.getTargetIdList());
        if (StringUtils.isNotBlank(param.getGroupId())) {
            targetViewParam.setAdGroupIdList(StringUtil.splitStr(param.getGroupId()));
        }
        targetViewParam.setStatus(param.getStatus());
        targetViewParam.setServingStatus(param.getServingStatus());
        targetViewParam.setSelectType(param.getSelectType());
        targetViewParam.setTargetText(param.getAggregateType());
        targetViewParam.setQueryValue(param.getQueryValue());
        targetViewParam.setQueryType(param.getQueryType());
        targetViewParam.setQueryField(param.getQueryField());
        return targetViewParam;
    }

    private AudienceTargetViewParam buildAudienceTargetViewParamByHourParam(TargetViewHourParam param) {
        AudienceTargetViewParam audienceTargetViewParam = new AudienceTargetViewParam();
        audienceTargetViewParam.setShopIdList(param.getShopIdList());
        audienceTargetViewParam.setTargetIdList(param.getTargetIdList());
        if (StringUtils.isNotBlank(param.getGroupId())) {
            audienceTargetViewParam.setAdGroupIdList(StringUtil.splitStr(param.getGroupId()));
        }
        audienceTargetViewParam.setTargetType(param.getAggregateType());
        AudienceCategoryTypeEnum typeEnum = AudienceCategoryTypeEnum.fromDesc(param.getTargetText());
        audienceTargetViewParam.setTargetText(typeEnum != null ? typeEnum.getType() : param.getTargetText());
        audienceTargetViewParam.setQueryValue(param.getQueryValue());
        audienceTargetViewParam.setStatus(param.getStatus());
        audienceTargetViewParam.setServingStatus(param.getServingStatus());
        return audienceTargetViewParam;
    }
}
