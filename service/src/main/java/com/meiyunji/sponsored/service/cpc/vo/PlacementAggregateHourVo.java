package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.rpc.adAggregateHour.AdPageBasic;
import com.meiyunji.sponsored.rpc.adAggregateHour.AdPageBasicData;
import com.meiyunji.sponsored.rpc.vo.AdAdvancedFilterData;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: ys
 * @date: 2023/10/30 11:25
 * @describe:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PlacementAggregateHourVo {

    private AdPageBasicData adPageBasicData;

    @ApiModelProperty("结束时间")
    private String weeks;

    @ApiModelProperty("广告活动")
    private String campaignId;

    private AdAdvancedFilterData advancedFilter;

    /**
     * {@link com.amazon.advertising.mode.PredicateEnum}
     */
    @ApiModelProperty("广告位")
    private String predicate;

    /**
     * {@link com.amazon.advertising.mode.PredicateEnum}
     */
    private String campaignSite;
}
