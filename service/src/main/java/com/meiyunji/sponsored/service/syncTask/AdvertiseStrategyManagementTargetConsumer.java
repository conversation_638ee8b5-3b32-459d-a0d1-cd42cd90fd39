package com.meiyunji.sponsored.service.syncTask;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Constants;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.IndexStrategyConfig;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyAdGroupDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusDeleteDao;
import com.meiyunji.sponsored.service.strategy.enums.AdType;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyAdGroup;
import com.meiyunji.sponsored.service.strategy.service.IAdvertiseStrategyGroupTargetBidService;
import com.meiyunji.sponsored.service.strategy.vo.SubmitStrategyVo;
import com.meiyunji.sponsored.service.syncTask.helper.AdvertiseStrategyManagementTargetHelper;
import com.meiyunji.sponsored.service.syncTask.management.strgtegy.enume.AdProductEnum;
import com.meiyunji.sponsored.service.syncTask.management.strgtegy.enume.TargetingTypeEnum;
import com.meiyunji.sponsored.service.syncTask.message.ManagementTargetStreamMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-02-23  17:05
 */
@Slf4j
@Component
public class AdvertiseStrategyManagementTargetConsumer {

    @Resource
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Resource
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAdvertiseStrategyGroupTargetBidService advertiseStrategyGroupTargetBidService;
    @Autowired
    private AdvertiseStrategyStatusDao advertiseStrategyStatusDao;
    @Autowired
    private AdvertiseStrategyAdGroupDao advertiseStrategyAdGroupDao;
    @Autowired
    private AdvertiseStrategyManagementTargetHelper advertiseStrategyManagementTargetHelper;
    @Autowired
    private AdvertiseStrategyStatusDeleteDao advertiseStrategyStatusDeleteDao;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private IndexStrategyConfig indexStrategyConfig;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Resource
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;

    public void process(List<ManagementTargetStreamMessage> messages) throws Exception {
        Set<String> sellerIds = new HashSet<>();
        Set<String> marketplaceIds = new HashSet<>();
        //转成店铺维度数据
        log.info("targeting stream data, {}", JSON.toJSONString(messages));
        Map<String, List<ManagementTargetStreamMessage>> collect = new HashMap<>();
        if (CollectionUtils.isEmpty(messages)) {
            log.info("ad stream target message empty");
            return;
        }
        for (ManagementTargetStreamMessage message : messages) {
            sellerIds.add(message.getAdvertiserId());
            marketplaceIds.add(message.getMarketplaceId());
            collect.computeIfAbsent(getKey(message), key -> new ArrayList<>()).add(message);
        }
        //查询shopAuth
        List<ShopAuth> shopAuths = shopAuthDao.getBySellerIdsAndMarketplaceIds(Lists.newArrayList(sellerIds), Lists.newArrayList(marketplaceIds));
        if (CollectionUtils.isEmpty(shopAuths)) {
            log.info("targeting stream not fund shopAuths,sellerId:{},marketplaceId:{}", StringUtils.join(sellerIds, ","), StringUtils.join(marketplaceIds, ","));
            return;
        }

        //查询所有的shopProfile
        List<Integer> shopIds = shopAuths.stream().map(ShopAuth::getId).collect(Collectors.toList());
        List<AmazonAdProfile> profilesByShopIds = amazonAdProfileDao.getProfilesByShopIds(shopIds);
        if (CollectionUtils.isEmpty(profilesByShopIds)) {
            log.info("campaign stream not fund profile, {}", StringUtils.join(shopIds, ","));
            return;
        }

        //sellerId+marketplaceId -- shopAuth映射map
        Map<String, ShopAuth> shopAuthMap = shopAuths.stream().collect(Collectors.toMap(this::getKey, Function.identity()));
        Map<Integer, AmazonAdProfile> adProfileMap = profilesByShopIds.stream().collect(Collectors.toMap(AmazonAdProfile::getShopId, Function.identity()));

        //遍历各店铺消息集合
        for (Map.Entry<String, List<ManagementTargetStreamMessage>> entry : collect.entrySet()) {
            ShopAuth shopAuth = shopAuthMap.get(entry.getKey());
            if (shopAuth == null) {
                continue;
            }
            AmazonAdProfile adProfile = adProfileMap.get(shopAuth.getId());
            if (adProfile == null) {
                continue;
            }
            //处理当前店铺
            processGroupStrategy(shopAuth, adProfile, entry.getValue());
        }
    }

    /**
     * 处理一个店铺的投放数据
     * @param shopAuth
     * @param shopTargetingMessageList
     */
    public void processGroupStrategy(ShopAuth shopAuth, AmazonAdProfile adProfile, List<ManagementTargetStreamMessage> shopTargetingMessageList) {
        List<String> adGroupIdList = shopTargetingMessageList.stream().filter(e-> !"ARCHIVED".equals(e.getState())).map(ManagementTargetStreamMessage::getAdGroupId).distinct().collect(Collectors.toList());
        List<AdvertiseStrategyAdGroup> advertiseStrategyAdGroupList = advertiseStrategyAdGroupDao.getAdGroupIds(shopAuth.getPuid(), shopAuth.getId(), adGroupIdList);
        if (CollectionUtils.isEmpty(advertiseStrategyAdGroupList)) {
            return;
        }
        for (AdvertiseStrategyAdGroup advertiseStrategyAdGroup : advertiseStrategyAdGroupList) {
            List<String> itemIds = Lists.newArrayList();
            List<String> itemIdList = advertiseStrategyStatusDao.getItemIdList(shopAuth.getPuid(), shopAuth.getId(), advertiseStrategyAdGroup.getAdGroupId());
            List<String> deleteItemIdList = advertiseStrategyStatusDeleteDao.getItemIdList(shopAuth.getPuid(), shopAuth.getId(), advertiseStrategyAdGroup.getAdGroupId());
            if (CollectionUtils.isNotEmpty(itemIdList)) {
                itemIds.addAll(itemIdList);
            }
            if (CollectionUtils.isNotEmpty(deleteItemIdList)) {
                itemIds.addAll(deleteItemIdList);
            }
            List<AmazonAdKeyword> amazonAdKeywordList = amazonAdKeywordDaoRoutingService.listByGroupIdAndItemIdList(shopAuth.getPuid(), shopAuth.getId(), advertiseStrategyAdGroup.getAdGroupId(), itemIds);
            List<AmazonAdTargeting> amazonAdTargetingList = amazonAdTargetDaoRoutingService.listByGroupIdAndItemIdList(shopAuth.getPuid(), shopAuth.getId(), advertiseStrategyAdGroup.getAdGroupId(), itemIds);
            List<AmazonSbAdKeyword> amazonSbAdKeywordList = amazonSbAdKeywordDao.listByGroupIdAndItemIdList(shopAuth.getPuid(), shopAuth.getId(), advertiseStrategyAdGroup.getAdGroupId(), itemIds);
            List<AmazonSbAdTargeting> amazonSbAdTargetingList = amazonSbAdTargetingDao.listByGroupIdAndItemIdList(shopAuth.getPuid(), shopAuth.getId(), advertiseStrategyAdGroup.getAdGroupId(), itemIds);
            List<AmazonSdAdTargeting> amazonSdAdTargetingList = amazonSdAdTargetingDao.listByGroupIdAndItemIdList(shopAuth.getPuid(), shopAuth.getId(), advertiseStrategyAdGroup.getAdGroupId(), itemIds);
            if (CollectionUtils.isNotEmpty(amazonAdKeywordList)) {
                advertiseStrategyManagementTargetHelper.spKeywordSubmitStrategy(advertiseStrategyAdGroup, amazonAdKeywordList);
            }
            if (CollectionUtils.isNotEmpty(amazonAdTargetingList)) {
                advertiseStrategyManagementTargetHelper.spTargetSubmitStrategy(advertiseStrategyAdGroup, amazonAdTargetingList);
            }
            if (CollectionUtils.isNotEmpty(amazonSbAdKeywordList)) {
                advertiseStrategyManagementTargetHelper.sbKeywordSubmitStrategy(advertiseStrategyAdGroup, amazonSbAdKeywordList);
            }
            if (CollectionUtils.isNotEmpty(amazonSbAdTargetingList)) {
                advertiseStrategyManagementTargetHelper.sbTargetSubmitStrategy(advertiseStrategyAdGroup, amazonSbAdTargetingList);
            }
            if (CollectionUtils.isNotEmpty(amazonSdAdTargetingList)) {
                advertiseStrategyManagementTargetHelper.sdTargetSubmitStrategy(advertiseStrategyAdGroup, amazonSdAdTargetingList);
            }
        }
    }

    private String getKey(ManagementTargetStreamMessage targetingStreamMessage) {
        return targetingStreamMessage.getAdvertiserId() + "#" + targetingStreamMessage.getMarketplaceId();
    }

    private String getKey(ShopAuth shopAuth) {
        return shopAuth.getSellingPartnerId() + "#" + shopAuth.getMarketplaceId();
    }
}
