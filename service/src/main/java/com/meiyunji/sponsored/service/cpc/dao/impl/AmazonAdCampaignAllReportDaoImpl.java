package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingSphereDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.syncReport.AdTypeEnum;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.bo.AllCampaignOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.ReportMonitorBo;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.cpc.dto.AdReportData;
import com.meiyunji.sponsored.service.cpc.dto.CampaignAndReportSearchDTO;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaign;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdShopReport;
import com.meiyunji.sponsored.service.cpc.qo.ReportAdvancedFilterBaseQo;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.doris.util.DorisJSONUtil;
import com.meiyunji.sponsored.service.enums.AmazonAdvertisePredicateEnum;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.index.po.SponsoredIndexCampaignData;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.DiagnoseCountParam;
import com.meiyunji.sponsored.service.reportDiffMonitor.dto.ShopDTO;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.SingleColumnRowMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by lm on 2021/5/14.
 *
 */
@Repository
@Slf4j
public class    AmazonAdCampaignAllReportDaoImpl extends BaseShardingSphereDaoImpl<AmazonAdCampaignAllReport> implements IAmazonAdCampaignAllReportDao {

    @Autowired
    private DynamicRefreshNacosConfiguration nacosConfiguration;

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Autowired
    private IDorisService dorisService;

    @Resource(name = "jdbcTemplateFeedDorisDb")
    private JdbcTemplate jdbcTemplateFeedDorisDb;

    @Override
    public void insertOrUpdateList(Integer puid, List<AmazonAdCampaignAllReport> list) {
        //插入原表
        insertOrUpdateListOriginAndHotTable(puid, list, getJdbcHelper().getTable(), false);

        //写入开关开启且数据是95天内的，就插入热表
        if (nacosConfiguration.isHotTableWriteEnable()) {
            //筛选出95天内的数据
            List<AmazonAdCampaignAllReport> hotList = list.stream()
                .filter(k -> (StringUtils.isNotBlank(k.getCountDate())))
                .filter(k -> DateUtil.getDayBetween(DateUtil.strToDate(k.getCountDate(), DateUtil.PATTERN_YYYYMMDD), new Date()) <= com.meiyunji.sponsored.common.base.Constants.HOT_SAVE_DAYS)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hotList)) {
                //插入热表
                insertOrUpdateListOriginAndHotTable(puid, hotList, getHotTableName(), false);
            }
        }

    }

    @Override
    public void insertDorisList(List<AmazonAdCampaignAllReport> list) {
        try {
            String time = LocalDateTimeUtil.formatTime(LocalDateTime.now(), LocalDateTimeUtil.YMDHMS_DATE_FORMAT);
            List<Map<String, Object>> map = list.stream().map(k -> {
                Map<String, Object> objectMap = DorisJSONUtil.dbObj2FieldMap(k);
                LocalDateTimeUtil.setDorisValue(objectMap, k.getCountDate(), time);
                return objectMap;
            }).collect(Collectors.toList());
            dorisService.saveDorisMapByRoutineLoad("doris_ods_t_amazon_ad_campaign_all_report", map);
        } catch (Exception e) {
            log.error("save doris kafka error = {}", e.getMessage());
        }
    }

    /**
     * 插入campaignAll表报告数据时请注意，由于sp活动报告近7天报告数据是通过sp广告产品表聚合而成，
     * 所以插入时有些字段是缺失的，不能进行更新插入，isProductAggregation 如果是true 表示是聚合而成的数据，
     * 不插入top_of_search_is字段，请务必注意；
     * @param puid
     * @param list
     * @param tableName
     * @param isProductAggregation true 广告产品聚合
     */
    private void insertOrUpdateListOriginAndHotTable(Integer puid, List<AmazonAdCampaignAllReport> list, String tableName, boolean isProductAggregation) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(tableName);
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`type`,`is_summary`,`count_date`,`campaign_type`,`ad_format`,`placement`,`tactic_type`,`campaign_name`,")
                .append("`campaign_id`,`campaign_status`,`campaign_budget`,`campaign_budget_type`,`campaign_rule_based_budget`,`applicable_budget_rule_id`,`applicable_budget_rule_name`,")
                .append("`impressions`,`clicks`,`currency`,`cost`,`conversions1d`,`conversions7d`,`conversions14d`,`conversions30d`, ")
                .append("`conversions1d_same_sku`,`conversions7d_same_sku`,`conversions14d_same_sku`,`conversions30d_same_sku`,`units_ordered1d`,")
                .append("`units_ordered7d`,`units_ordered14d`,`units_ordered30d`,`units_ordered1d_same_sku`,")
                .append("`units_ordered7d_same_sku`,`units_ordered14d_same_sku`,`units_ordered30d_same_sku`,`sales1d`,`sales7d`,`sales14d`,`sales30d`,")
                .append("`sales1d_same_sku`,`sales7d_same_sku`,`sales14d_same_sku`,`sales30d_same_sku`,`detail_page_views_clicks14d`,")
                .append("`orders_new_to_brand14d`,`orders_new_to_brand_percentage14d`,`order_rate_new_to_brand14d`,")
                .append("`sales_new_to_brand14d`,`sales_new_to_brand_percentage14d`,`units_ordered_new_to_brand14d` ,`units_ordered_new_to_brand_percentage14d`,`units_sold14d`,")
                .append("`dpv14d`,`detail_page_view14d`,`view_impressions`,`cost_type`,")
                .append("`vctr`,`video5second_view_rate`,`video5second_views`,`video_first_quartile_views`,`video_midpoint_views`,`video_third_quartile_views` ,")
                .append("`video_unmutes`,`viewable_impressions`,`video_complete_views`, `vtr`, " )
                .append("`new_to_brand_detail_page_views`,`add_to_cart`,`add_to_cart_rate`,`e_cp_add_to_cart`,")
                .append("`branded_searches14d`,`cumulative_reach`,`impressions_frequency_average`,")
                .append(isProductAggregation ? "" : "`top_of_search_is`,")
                .append("`create_time`,`update_time` ) values");
        List<Object> argsList = Lists.newArrayList();

        for (AmazonAdCampaignAllReport report : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,")
                    .append(isProductAggregation ? "" : "?,")
                    .append("now(),now()),");
            argsList.add(puid);
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getType());
            argsList.add(report.getIsSummary());
            argsList.add(report.getCountDate());
            argsList.add(report.getCampaignType());
            argsList.add(report.getAdFormat());
            argsList.add(report.getPlacement());
            argsList.add(report.getTacticType());
            argsList.add(report.getCampaignName() == null ? "" : report.getCampaignName());
            argsList.add(report.getCampaignId());
            argsList.add(report.getCampaignStatus());
            argsList.add(report.getCampaignBudget());
            argsList.add(report.getCampaignBudgetType());
            argsList.add(report.getCampaignRuleBasedBudget());
            argsList.add(report.getApplicableBudgetRuleId());
            argsList.add(report.getApplicableBudgetRuleName());
            argsList.add(report.getImpressions());
            argsList.add(report.getClicks());
            Marketplace marketplace = Marketplace.fromId(report.getMarketplaceId());
            if (marketplace != null) {
                // 设置币种 考虑后续同步doris逻辑
                report.setCurrency(marketplace.getCurrencyCode().name());
            }
            argsList.add(report.getCurrency());
            argsList.add(report.getCost());
            argsList.add(report.getConversions1d());
            argsList.add(report.getConversions7d());
            argsList.add(report.getConversions14d());
            argsList.add(report.getConversions30d() );
            argsList.add(report.getConversions1dSameSKU());
            argsList.add(report.getConversions7dSameSKU());
            argsList.add(report.getConversions14dSameSKU());
            argsList.add(report.getConversions30dSameSKU());
            argsList.add(report.getUnitsOrdered1d());
            argsList.add(report.getUnitsOrdered7d());
            argsList.add(report.getUnitsOrdered14d());
            argsList.add(report.getUnitsOrdered30d());
            argsList.add(report.getUnitsOrdered1dSameSKU());
            argsList.add(report.getUnitsOrdered7dSameSKU());
            argsList.add(report.getUnitsOrdered14dSameSKU());
            argsList.add(report.getUnitsOrdered30dSameSKU());
            argsList.add(report.getSales1d() );
            argsList.add(report.getSales7d());
            argsList.add(report.getSales14d());
            argsList.add(report.getSales30d());
            argsList.add(report.getSales1dSameSKU());
            argsList.add(report.getSales7dSameSKU());
            argsList.add(report.getSales14dSameSKU());
            argsList.add(report.getSales30dSameSKU());
            argsList.add(report.getDetailPageViewsClicks14d() );
            argsList.add(report.getOrdersNewToBrand14d());
            argsList.add(report.getOrdersNewToBrandPercentage());
            argsList.add(report.getOrderRateNewToBrand() );
            argsList.add(report.getSalesNewToBrand14d() );
            argsList.add(report.getSalesNewToBrandPercentage14d() );
            argsList.add(report.getUnitsOrderedNewToBrand14d());
            argsList.add(report.getUnitsOrderedNewToBrandPercentage14d());
            argsList.add(report.getUnitsSold14d());
            argsList.add(report.getDpv14d());
            argsList.add(report.getDetailPageView14d() );
            argsList.add(report.getViewImpressions());
            argsList.add(report.getCostType());
            argsList.add(report.getVctr());
            argsList.add(report.getVideo5SecondViewRate());
            argsList.add(report.getVideo5SecondViews());
            argsList.add(report.getVideoFirstQuartileViews());
            argsList.add(report.getVideoMidpointViews());
            argsList.add(report.getVideoThirdQuartileViews());
            argsList.add(report.getVideoUnmutes());
            argsList.add(report.getViewableImpressions());
            argsList.add(report.getVideoCompleteViews());
            argsList.add(report.getVtr());
            argsList.add(report.getNewToBrandDetailPageViews());
            argsList.add(report.getAddToCart());
            argsList.add(report.getAddToCartRate());
            argsList.add(report.getECPAddToCart());
            argsList.add(report.getBrandedSearches());
            argsList.add(report.getCumulativeReach());
            argsList.add(report.getImpressionsFrequencyAverage());
            if(!isProductAggregation) {
                argsList.add(report.getTopOfSearchIs());
            }
        }

        sql.deleteCharAt(sql.length()-1);
        sql.append(" on duplicate key update `campaign_name`=values(campaign_name),`campaign_status`=values(campaign_status),`campaign_budget`=values(campaign_budget),`campaign_budget_type`=values(campaign_budget_type),`campaign_rule_based_budget`=values(campaign_rule_based_budget),");
        sql.append("`applicable_budget_rule_id`=values(applicable_budget_rule_id),`applicable_budget_rule_name`=values(applicable_budget_rule_name),");
        sql.append("`impressions`=values(impressions),`clicks`=values(clicks),`currency`=values(currency),`cost`=values(cost),`conversions1d`=values(conversions1d),`conversions7d`=values(conversions7d),`conversions14d`=values(conversions14d),`conversions30d`=values(conversions30d), ");
        sql.append("`conversions1d_same_sku`=values(conversions1d_same_sku),`conversions7d_same_sku`=values(conversions7d_same_sku),`conversions14d_same_sku`=values(conversions14d_same_sku),`conversions30d_same_sku`=values(conversions30d_same_sku),`units_ordered1d`=values(units_ordered1d),");
        sql.append("`units_ordered7d`=values(units_ordered7d),`units_ordered14d`=values(units_ordered14d),`units_ordered30d`=values(units_ordered30d),`sales1d`=values(sales1d),`sales7d`=values(sales7d),`sales14d`=values(sales14d),`sales30d`=values(sales30d),");
        sql.append("`units_ordered1d_same_sku`=values(units_ordered1d_same_sku),`units_ordered7d_same_sku`=values(units_ordered7d_same_sku),`units_ordered14d_same_sku`=values(units_ordered14d_same_sku),`units_ordered30d_same_sku`=values(units_ordered30d_same_sku),");
        sql.append("`sales1d_same_sku`=values(sales1d_same_sku),`sales7d_same_sku`=values(sales7d_same_sku),`sales14d_same_sku`=values(sales14d_same_sku),`sales30d_same_sku`=values(sales30d_same_sku),`detail_page_views_clicks14d`=values(detail_page_views_clicks14d),");
        sql.append("`orders_new_to_brand14d`=values(orders_new_to_brand14d),`orders_new_to_brand_percentage14d`=values(orders_new_to_brand_percentage14d),`order_rate_new_to_brand14d`=values(order_rate_new_to_brand14d),");
        sql.append("`sales_new_to_brand14d`=values(sales_new_to_brand14d),`sales_new_to_brand_percentage14d`=values(sales_new_to_brand_percentage14d),`units_ordered_new_to_brand14d`=values(units_ordered_new_to_brand14d),`units_ordered_new_to_brand_percentage14d`=values(units_ordered_new_to_brand_percentage14d) ,`units_sold14d`=values(units_sold14d),");
        sql.append("`dpv14d`=values(dpv14d),`detail_page_view14d`=values(detail_page_view14d),`view_impressions`=values(view_impressions),`cost_type`=values(cost_type),");
        sql.append("`vctr`=values(vctr),`video5second_view_rate`=values(video5second_view_rate),`video5second_views`=values(video5second_views),`video_first_quartile_views`=values(video_first_quartile_views),");
        sql.append("`video_midpoint_views`=values(video_midpoint_views),`video_third_quartile_views`=values(video_third_quartile_views),`video_unmutes`=values(video_unmutes),");
        sql.append("`viewable_impressions`=values(viewable_impressions),`video_complete_views`=values(video_complete_views),`vtr`=values(vtr)");
        sql.append(",`new_to_brand_detail_page_views`=values(new_to_brand_detail_page_views),`add_to_cart`=values(add_to_cart),`add_to_cart_rate`=values(add_to_cart_rate),`e_cp_add_to_cart`=values(e_cp_add_to_cart)");
        sql.append(",`branded_searches14d`=values(branded_searches14d),`cumulative_reach`=values(cumulative_reach),`impressions_frequency_average`=values(impressions_frequency_average)");
        sql.append(isProductAggregation ? "" : ", `top_of_search_is`=values(top_of_search_is)");

        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(),argsList.toArray());
        } finally {
            hintManager.close();
        }
    }


    @Override
    public AmazonAdCampaignAllReport getSumReport(Integer puid, Integer shopId, String marketplaceId, String reportDate,String type) {
        StringBuilder sql = new StringBuilder("SELECT type,sum(`cost`) cost,sum(if (type = 'sp', sales7d,sales14d)) total_sales,")
                .append("sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) sale_num, sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_sale_num,")
                .append("sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) order_num ,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_order_num , ")
                .append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d,sum(view_impressions) view_impressions ")
                .append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(reportDate, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where `puid`=? and `shop_id`=? and `marketplace_id`=? and `count_date`=? and `is_summary`=1 ");
        List<Object> args = Lists.newArrayList(puid,shopId,marketplaceId,reportDate);
        if(StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForObject(sql.toString(),args.toArray(),getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdCampaignAllReport> getSumDailyReportByDateRange(Integer puid, Integer shopId, String marketplaceId, String start, String end,String type) {
        StringBuilder sql = new StringBuilder("SELECT count_date, type,sum(`cost`) cost,sum(sales7d) total_sales,")
                .append("sum(sales7d_same_sku) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(conversions7d) sale_num, sum(conversions7d_same_sku) ad_sale_num,")
                .append("sum(units_ordered7d) order_num ,sum(units_ordered7d_same_sku) ad_order_num , ")
                .append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d,sum(view_impressions) view_impressions ")
                .append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(start, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where`puid`=? and`shop_id`=? and `is_summary`= 1 and `type` = ? and `count_date`>= ? and `count_date`<= ? ");
        List<Object> args = Lists.newArrayList(puid,shopId, type,start, end);
        sql.append(" group by count_date ");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(),args.toArray(),getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdCampaignAllReport> getSumByDate(Integer puid, Integer shopId, String marketplaceId, String startDate, String endDate,String type) {
        StringBuilder sql = new StringBuilder("SELECT campaign_id,campaign_name,campaign_status,campaign_budget,type,sum(`cost`) cost,sum(if (type = 'sp', sales7d,sales14d)) total_sales,")
                .append("sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) order_num, sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_order_num,")
                .append("sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) sale_num,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_sale_num, ")
                .append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d,sum(view_impressions) view_impressions ")
                .append("FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where`puid`=? and`shop_id`=? and`marketplace_id`=? and `count_date`>=? and `count_date`<=? and `is_summary`=1  ");
        List<Object> args = Lists.newArrayList(puid, shopId, marketplaceId, startDate, endDate);
        if(StringUtils.isNotBlank(type)){
            sql.append(" and type = ? ");
            args.add(type);
        }
        sql.append(" group by campaign_id ");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(),args.toArray(),getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public Page getPageList(Integer puid, SearchVo search, Page page) {
        StringBuilder selectSql = new StringBuilder("SELECT cost_type,shop_id,count_date,marketplace_id,type,campaign_id,campaign_name,campaign_type,sum(`cost`) cost,sum(if (type = 'sp', sales7d,sales14d)) total_sales,")
                .append("sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) order_num, sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_order_num,")
                .append("sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) sale_num,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_sale_num, ")
                .append("sum(view_impressions) viewImpressions ,sum(`detail_page_view14d`) detail_page_view14d,")
                .append("sum(`orders_new_to_brand14d`) orders_new_to_brand14d,sum(`orders_new_to_brand_percentage14d`) orders_new_to_brand_percentage14d,sum(`order_rate_new_to_brand14d`) order_rate_new_to_brand14d,")
                .append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d ,sum(`sales_new_to_brand_percentage14d`) sales_new_to_brand_percentage14d,sum(`units_ordered_new_to_brand14d`) units_ordered_new_to_brand14d,sum(`units_ordered_new_to_brand_percentage14d`) units_ordered_new_to_brand_percentage14d ,")
                .append("sum(`vctr`) vctr,sum(`video5second_view_rate`) video5second_view_rate,sum(`video5second_views`) video5second_views,sum(`video_first_quartile_views`) video_first_quartile_views,")
                .append("sum(`video_midpoint_views`) video_midpoint_views,sum(`video_third_quartile_views`) video_third_quartile_views,sum(`video_unmutes`) video_unmutes,sum(`viewable_impressions`) viewable_impressions,")
                .append("sum(`video_complete_views`) video_complete_views,sum(`vtr`) vtr FROM ");
        String queryTable = getTableNameByStartDate(search.getStart());
        selectSql.append(queryTable);
        StringBuilder countSql = new StringBuilder("select count(*) from ( select campaign_id FROM ");
        countSql.append(queryTable);
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        if (search.getShopId() != null) {
            whereSql.append("and shop_id=? ");
            argsList.add(search.getShopId());
        } else if (CollectionUtils.isNotEmpty(search.getShopIds())) {
            whereSql.append("and shop_id in ('").append(StringUtils.join(search.getShopIds(),"','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(search.getCampaignIds())) {
            whereSql.append("and campaign_id in ('").append(StringUtils.join(search.getCampaignIds(),"','")).append("') ");
        }
        whereSql.append("and count_date>=? and count_date<=? and `is_summary`=1 and type = ? ");
        argsList.add(DateUtil.dateToStrWithFormat(search.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(search.getEnd(),"yyyyMMdd"));
        argsList.add(search.getType());
        if(StringUtils.isNotBlank(search.getSearchValue())){
            search.setSearchValue(SqlStringUtil.dealLikeSql(search.getSearchValue()));
            whereSql.append(" and campaign_name like ?");
            argsList.add(search.getSearchValue()+"%");
        }
        whereSql.append("group by shop_id,campaign_id ");
        if ("daily".equals(search.getTabType())) {
            whereSql.append(", count_date");
        }
        if ("sd".equals(search.getType())) {
            whereSql.append(", cost_type");
        }
        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");
        if(StringUtils.isNotBlank(search.getOrderField()) && StringUtils.isNotBlank(search.getOrderValue())){
            String orderField = ReportService.getOrderField(search.getOrderField(),true);
            if(StringUtils.isNotBlank(orderField)){
                selectSql.append(" order by ").append(orderField);
                if("desc".equals(search.getOrderValue())){
                    selectSql.append("desc");
                }
                selectSql.append(" ,campaign_id ");   //增加一个排序字段
                if("desc".equals(search.getOrderValue())){
                    selectSql.append("desc");
                }
            }
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args,AmazonAdCampaignAllReport.class);
    }

    @Override
    public List<AmazonAdCampaignAllReport> listSumReports(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, List<String> campaignIds) {
        String sql = "SELECT campaign_id,type,sum(`cost`) cost, "+
                "sum(if (type = 'sp', sales7d,sales14d)) total_sales ," +
                "sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) order_num, " +
                "sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_order_num," +
                "sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) sale_num,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_sale_num, " +
                "sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d,sum(view_impressions) view_impressions, max(top_of_search_is) max_top_is, min(top_of_search_is) min_top_is, " +
                "sum(`new_to_brand_detail_page_views`) AS `new_to_brand_detail_page_views`, " +
                "sum(`add_to_cart`) AS `add_to_cart`, " +
                "sum(`video5second_views`) AS `video5second_views`, " +
                "sum(`video_first_quartile_views`) AS `video_first_quartile_views`, " +
                "sum(`video_Midpoint_Views`) AS `video_Midpoint_Views`, " +
                "sum(`video_third_quartile_views`) AS `video_third_quartile_views`, " +
                "sum(`video_complete_views`) AS `video_complete_views`, " +
                "sum(`video_unmutes`) AS `video_unmutes`, " +
                "sum(`viewable_impressions`) AS `viewable_impressions`, " +
                "sum(`branded_searches14d`) AS `branded_searches14d`, " +
                "sum(`detail_page_view14d`) AS `detail_page_view14d` " +
                "FROM "+ getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)) +" where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketplaceId)
                .greaterThanOrEqualTo("count_date", startStr)
                .lessThanOrEqualTo("count_date", endStr)
                .equalTo("is_summary",1)
                .in("campaign_id", campaignIds.toArray())
                .groupBy("campaign_id")
                .build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AdHomePerformancedto> listLatestReports(Integer puid, Integer shopId, List<String> campaignIds, boolean isAggregation) {
        if (isAggregation && CollectionUtils.isEmpty(campaignIds)) {
            return Collections.emptyList();
        }

        String sql = "SELECT campaign_id, type " +
                ", max(`cumulative_reach`) AS `cumulative_reach` " +
                ", max(`impressions_frequency_average`) AS `impressions_frequency_average` " +
                " FROM " + getTableNameByStartDate(new Date()) +
                " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("is_summary", 1)
                .in("campaign_id", campaignIds.toArray())
                .groupBy("campaign_id")
                .build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, (re, i) -> AdHomePerformancedto.builder()
                            .campaignId(re.getString("campaign_id"))
                            .type(re.getString("type"))
                            .cumulativeReach(re.getInt("cumulative_reach"))
                            .impressionsFrequencyAverage(Optional.ofNullable(re.getBigDecimal("impressions_frequency_average")).orElse(BigDecimal.ZERO))
                            .build(),
                    conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }
    }


    @Override
    public AdMetricDto getSumAdMetric(Integer puid, Integer shopId, String startStr, String endStr, CampaignPageParam param) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select sum(r.cost) `cost`, sum(if (r.type = 'sp', sales7d,sales14d)) total_sales,")
                .append("sum(if (r.type = 'sp' , conversions7d,conversions14d)) order_num, ")
                .append("sum(if (r.type = 'sp' ,`units_ordered7d`,if (r.type = 'sb' ,`units_sold14d`,`units_ordered14d`))) sale_num from");
        sql.append(" t_amazon_ad_campaign_all c join ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" r on r.puid=c.puid and r.shop_id=c.shop_id and r.campaign_id=c.campaign_id where c.puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and c.name like ? ");
            argsList.add("%" + param.getSearchValue().trim() + "%");
        }
        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }
        // 仅显示正在投放
        if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
            whereSql.append(" and c.serving_status in (?, ?)");
            argsList.add(AmazonAdCampaignAll.servingStatusEnum.CAMPAIGN_STATUS_ENABLED.getCode());
            argsList.add(AmazonAdCampaignAll.servingStatusEnum.running.getCode());
        }
        if(StringUtils.isNotBlank(param.getPortfolioId()) && param.getPortfolioId().equals("-1")){
            whereSql.append(" and c.portfolio_id is null ");
        }
        if (StringUtils.isNotBlank(param.getStrategyType())) {
            whereSql.append(" and c.strategy = ?");
            argsList.add(param.getStrategyType());
        }
        if(StringUtils.isNotBlank(param.getType())){
//            whereSql.append(" and c.type = ? ");
//            argsList.add(param.getType());
            whereSql.append(SqlStringUtil.dealInList("c.type",  StringUtil.splitStr(param.getType()), argsList));
        }
        //预算状态
        if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.budgetExceeded.getCode())) {
            whereSql.append(" and c.serving_status = ? ");
            argsList.add(AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
        } else if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.underBudget.getCode())){
            whereSql.append(" and c.serving_status != ? ");
            argsList.add(AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
        }
        if(StringUtils.isNotBlank(param.getCostType())){
            whereSql.append(" and c.cost_type = ? ");
            argsList.add(param.getCostType());
        }
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (StringUtils.isNotBlank(param.getFilterTargetType())) {  //活动类型  auto自动投放,manual手动投放
                List<String> typeList = StringUtil.splitStr(param.getFilterTargetType(), ",");
                whereSql.append(SqlStringUtil.dealInList("c.ad_target_type", typeList, argsList));
            }
            if (param.getDailyBudgetMin() != null) {   //每日预算
                whereSql.append(" and c.budget >= ? ");
                argsList.add(param.getDailyBudgetMin());
            }
            if (param.getDailyBudgetMax() != null) {
                whereSql.append(" and c.budget <= ? ");
                argsList.add(param.getDailyBudgetMax());
            }
            if (StringUtils.isNotBlank(param.getFilterStartDate())) {  // 日期
                whereSql.append(" and c.start_date >= ? ");
                argsList.add(param.getFilterStartDate());
            }
            if (StringUtils.isNotBlank(param.getFilterEndDate())) {
                whereSql.append(" and c.end_date <= ? ");
                argsList.add(param.getFilterEndDate());
            }
        }

        whereSql.append(" and is_summary=1 and r.count_date >= ? and r.count_date <= ? ");
        argsList.add(startStr);
        argsList.add(endStr);

        whereSql.append(subWhereSql(param, argsList)); // 高级筛选
        sql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();

        try {
            List<AdMetricDto> list = getJdbcTemplate(puid, hintManager).query(sql.toString(), (re, i) -> {
                AdMetricDto dto = AdMetricDto.builder()
                        .sumCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .sumAdSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("order_num")).orElse(BigDecimal.ZERO))
                        .sumOrderNum(Optional.ofNullable(re.getBigDecimal("sale_num")).orElse(BigDecimal.ZERO))
                        .build();
                return dto;
            }, argsList.toArray());
            return list!= null && list.size() > 0 ? list.get(0) : null;
        } finally {
            hintManager.close();
        }
    }

    @Override
    public AdMetricDto getCampaignPageSumMetricDataByCampaignIdList(Integer puid, CampaignPageParam param, List<String> campaignIdList) {
        if (CollectionUtils.isEmpty(campaignIdList)) {
            return new AdMetricDto();
        }
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select sum(cost) `cost`, sum(if (type = 'sp', sales7d,sales14d)) total_sales,")
                .append("sum(if (type = 'sp' , conversions7d,conversions14d)) order_num, ")
                .append("sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) sale_num from ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where puid = ? ");
        argsList.add(puid);
        if (param.getShopId() != null) {
            sql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }
        if (StringUtils.isNotEmpty(param.getMarketplaceId())) {
            sql.append(" and marketplace_id = ? ");
            argsList.add(param.getMarketplaceId());
        }
        sql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
        sql.append(" and count_date >= ? and count_date <= ? and is_summary=1 ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        HintManager hintManager = HintManager.getInstance();
        try {
            List<AdMetricDto> list = getJdbcTemplate(puid, hintManager).query(sql.toString(), (re, i) -> {
                AdMetricDto dto = AdMetricDto.builder()
                        .sumCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .sumAdSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("order_num")).orElse(BigDecimal.ZERO))
                        .sumOrderNum(Optional.ofNullable(re.getBigDecimal("sale_num")).orElse(BigDecimal.ZERO))
                        .build();
                return dto;
            }, argsList.toArray());
            return list!= null && list.size() > 0 ? list.get(0) : null;
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdHomePerformancedto> getReportByDate(Integer puid, Integer shopId, String startStr, String endStr, CampaignPageParam param) {
        return getReportByDate(puid, shopId, startStr, endStr, param, false);
    }

    @Override
    public List<AdHomePerformancedto> getReportByDate(Integer puid, Integer shopId, String startStr, String endStr, CampaignPageParam param, boolean isLatest) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select c.campaign_id campaign_id, count_date, r.type, sum(r.cost) `cost`, sum(if (r.type = 'sp', sales7d,sales14d)) total_sales,")
                .append("sum(if (r.type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (r.type = 'sp' , conversions7d,conversions14d)) order_num, sum(if (r.type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_order_num,")
                .append("sum(if (r.type = 'sp' ,`units_ordered7d`,if (r.type = 'sb' ,`units_sold14d`,`units_ordered14d`))) sale_num,sum(if (r.type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_sale_num, ")
                .append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d, sum(view_impressions) view_impressions, ")
                .append("sum(`new_to_brand_detail_page_views`) AS `new_to_brand_detail_page_views`, ")
                .append("sum(`add_to_cart`) AS `add_to_cart`, ")
                .append("sum(`video5second_views`) AS `video5second_views`, ")
                .append("sum(`video_first_quartile_views`) AS `video_first_quartile_views`, ")
                .append("sum(`video_Midpoint_Views`) AS `video_Midpoint_Views`, ")
                .append("sum(`video_third_quartile_views`) AS `video_third_quartile_views`, ")
                .append("sum(`video_complete_views`) AS `video_complete_views`, ")
                .append("sum(`video_unmutes`) AS `video_unmutes`, ")
                .append("sum(`viewable_impressions`) AS `viewable_impressions`, ")
                .append("sum(`branded_searches14d`) AS `branded_searches14d`, ")
                .append("sum(`detail_page_view14d`) AS `detail_page_view14d`,")
                .append(" max(`cumulative_reach`) AS `cumulative_reach`,")
                .append(" max(`impressions_frequency_average`) AS `impressions_frequency_average`")
                .append(" from ");
        sql.append(" t_amazon_ad_campaign_all c join ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" r on r.puid=c.puid and r.shop_id=c.shop_id and r.campaign_id=c.campaign_id where c.puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }

        //通过param.campaignId查询具体的广告活动
        if (StringUtils.isNotEmpty(param.getCampaignId())) {
            whereSql.append(" and c.campaign_id = ? ");
            argsList.add(param.getCampaignId());
        }
        if (StringUtils.isNotBlank(param.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", StringUtil.splitStr(param.getCampaignIds()), argsList));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and c.name like ? ");
            argsList.add("%" + param.getSearchValue().trim() + "%");
        }
        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }
        // 筛选服务状态

        if(StringUtils.isNotBlank(param.getServingStatus())){
            List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
            List<String> servings = list.stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).flatMap(Collection::stream).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(servings)){
                whereSql.append(SqlStringUtil.dealInList("c.serving_status",servings,argsList));
            }
        }

        if(StringUtils.isNotBlank(param.getPortfolioId()) && param.getPortfolioId().equals("-1")){
            whereSql.append(" and c.portfolio_id is null ");
        }
        if (StringUtils.isNotBlank(param.getStrategyType())) {
            whereSql.append(" and c.strategy = ?");
            argsList.add(param.getStrategyType());
        }
        if(StringUtils.isNotBlank(param.getType())){
//            whereSql.append(" and c.type = ? ");
//            argsList.add(param.getType());
            whereSql.append(SqlStringUtil.dealInList("c.type",  StringUtil.splitStr(param.getType()), argsList));
        }
        //预算状态
        if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.budgetExceeded.getCode())) {
            whereSql.append(" and c.serving_status = ? ");
            argsList.add(AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
        } else if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.underBudget.getCode())){
            whereSql.append(" and c.serving_status != ? ");
            argsList.add(AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
        }
        if(StringUtils.isNotBlank(param.getCostType())){
            whereSql.append(" and c.cost_type = ? ");
            argsList.add(param.getCostType());
        }
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (StringUtils.isNotBlank(param.getFilterTargetType())) {  //活动类型  auto自动投放,manual手动投放
                List<String> typeList = StringUtil.splitStr(param.getFilterTargetType(), ",");
                whereSql.append(SqlStringUtil.dealInList("c.ad_target_type", typeList, argsList));
            }
            if (param.getDailyBudgetMin() != null) {   //每日预算
                whereSql.append(" and c.budget >= ? ");
                argsList.add(param.getDailyBudgetMin());
            }
            if (param.getDailyBudgetMax() != null) {
                whereSql.append(" and c.budget <= ? ");
                argsList.add(param.getDailyBudgetMax());
            }
            if (StringUtils.isNotBlank(param.getFilterStartDate())) {  // 日期
                whereSql.append(" and c.start_date >= ? ");
                argsList.add(param.getFilterStartDate());
            }
            if (StringUtils.isNotBlank(param.getFilterEndDate())) {
                whereSql.append(" and c.end_date <= ? ");
                argsList.add(param.getFilterEndDate());
            }
        }

        whereSql.append(" and is_summary=1 ");

        if (!isLatest) {
            whereSql.append(" and r.count_date >= ? and r.count_date <= ? ");
            argsList.add(startStr);
            argsList.add(endStr);
        }
        whereSql.append(" group by c.campaign_id ");

        whereSql.append(subWhereSql(param, argsList)); // 高级筛选
        sql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), (re, i) -> {
                AdHomePerformancedto dto = AdHomePerformancedto.builder()
                        .campaignId(re.getString("campaign_id"))
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        //广告订单量
                        .adOrderNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))  //销量字段订单
                        //本广告产品订单量
                        .adSaleNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                        //广告销售额
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        //广告销量
                        .salesNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                        //本广告产品销量
                        .orderNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                        //“品牌新买家”销售额
                        .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                        //“品牌新买家”订单量
                        .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                        //“品牌新买家”销量
                        .unitsOrderedNewToBrand14d(Optional.ofNullable(re.getInt("units_ordered_new_to_brand14d")).orElse(0))
                        //可见展示次数
                        .viewImpressions(Optional.ofNullable(re.getInt("view_impressions")).orElse(0))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .countDate(re.getString("count_date"))
                        .type(re.getString("type"))
                        .newToBrandDetailPageViews(Optional.ofNullable(re.getInt("new_to_brand_detail_page_views")).orElse(0))
                        .addToCart(Optional.ofNullable(re.getInt("add_to_cart")).orElse(0))
                        .video5SecondViews(Optional.ofNullable(re.getInt("video5second_views")).orElse(0))
                        .videoFirstQuartileViews(Optional.ofNullable(re.getInt("video_first_quartile_views")).orElse(0))
                        .videoMidpointViews(Optional.ofNullable(re.getInt("video_Midpoint_Views")).orElse(0))
                        .videoThirdQuartileViews(Optional.ofNullable(re.getInt("video_third_quartile_views")).orElse(0))
                        .videoCompleteViews(Optional.ofNullable(re.getInt("video_complete_views")).orElse(0))
                        .videoUnmutes(Optional.ofNullable(re.getInt("video_unmutes")).orElse(0))
                        .viewableImpressions(Optional.ofNullable(re.getInt("viewable_impressions")).orElse(0))
                        .brandedSearches(Optional.ofNullable(re.getInt("branded_searches14d")).orElse(0))
                        .detailPageViews(Optional.ofNullable(re.getInt("detail_page_view14d")).orElse(0))
                        .cumulativeReach(re.getInt("cumulative_reach"))
                        .impressionsFrequencyAverage(Optional.ofNullable(re.getBigDecimal("impressions_frequency_average")).orElse(BigDecimal.ZERO))
                        .build();
                return dto;
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdHomePerformancedto> getReportBySearchParam(Integer puid, CampaignAndReportSearchDTO searchParam) {
        CampaignPageParam param = new CampaignPageParam();
        BeanUtils.copyProperties(searchParam, param);
        return getReportByDate(puid, searchParam.getShopId(), searchParam.getStartDate(), searchParam.getEndDate(), param);
    }

    @Override
    public List<AdHomePerformancedto> getReportByCampaignIdsAndDate(Integer puid, Integer shopId, String startStr, String endStr, List<String> campaignIdList) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select sum(`cost`) cost, sum(if (r.type = 'sp', sales7d,sales14d)) total_sales,")
                .append("sum(if (r.type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (r.type = 'sp' , conversions7d,conversions14d)) order_num, sum(if (r.type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_order_num,")
                .append("sum(if (r.type = 'sp' ,`units_ordered7d`,if (r.type = 'sb' ,`units_sold14d`,`units_ordered14d`))) sale_num,sum(if (r.type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_sale_num, ")
                .append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d, sum(view_impressions) view_impressions from ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" r where r.puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (shopId != null) {
            whereSql.append(" and r.shop_id = ? ");
            argsList.add(shopId);
        }

        whereSql.append(SqlStringUtil.dealInList("r.campaign_id", campaignIdList, argsList));

        whereSql.append(" and is_summary=1 and r.count_date >= ? and r.count_date <= ? ");
        argsList.add(startStr);
        argsList.add(endStr);

        sql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .orderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  // 广告销量
                            .adOrderNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))  // 广告订单
                            .salesNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                            .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                            .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                            .viewImpressions(Optional.ofNullable(re.getInt("view_impressions")).orElse(0))
                            .unitsOrderedNewToBrand14d(Optional.ofNullable(re.getInt("units_ordered_new_to_brand14d")).orElse(0))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdReportData> getAllReportByCampaignIdsAndDate(Integer puid, Integer shopId, String startStr, String endStr, List<String> campaignIdList) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder("select campaign_id,type,count_date,")
                .append("IFNULL(sum(cost),0) `cost`,")
                .append("IFNULL(sum(if (type = 'sp', sales7d,sales14d)),0) adSale,")
                .append("IFNULL(sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)),0) adSelfSale,")
                .append("IFNULL(sum(`impressions`),0) impressions,")
                .append("IFNULL(sum(`clicks`),0) clicks,")
                .append("IFNULL(sum(if (type = 'sp' , conversions7d,conversions14d)),0) adOrderNum,")
                .append("IFNULL(sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)),0) adSelfOrderNum,")
                .append("IFNULL(sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))),0) adSaleNum,")
                .append("IFNULL(sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)),0) adSelfSaleNum FROM ");
        startStr = startStr.contains("-") ? startStr.replaceAll("-", "") : startStr;
        endStr = endStr.contains("-") ? endStr.replaceAll("-", "") : endStr;
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
//        sql.append("t_amazon_ad_campaign_all_report");
        sql.append(" where puid= ? ");
        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (shopId != null) {
            whereSql.append(" and shop_id = ? ");
            argsList.add(shopId);
        }
        whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
        whereSql.append(" and is_summary=1 and count_date >= ? and count_date <= ? ");
        argsList.add(startStr);
        argsList.add(endStr);
        sql.append(whereSql);
        sql.append(" group by campaign_id,count_date");
        HintManager hintManager = HintManager.getInstance();
        try {
            // 也可以直接在对象 写注解
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), (re, i) -> {
                AdReportData dto = new AdReportData();
                dto.setCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
                dto.setItemId(re.getString("campaign_id"));
                dto.setImpressions(Optional.of(re.getLong("impressions")).orElse(0L));
                dto.setClicks(Optional.of(re.getLong("clicks")).orElse(0L));
                dto.setAdOrderNum(Optional.of(re.getInt("adOrderNum")).orElse(0));
                dto.setAdSelfOrderNum(Optional.of(re.getInt("adSelfOrderNum")).orElse(0));
                dto.setAdSale(Optional.ofNullable(re.getBigDecimal("adSale")).orElse(BigDecimal.ZERO));
                dto.setAdSelfSale(Optional.ofNullable(re.getBigDecimal("adSelfSale")).orElse(BigDecimal.ZERO));
                dto.setAdSaleNum(Optional.of(re.getInt("adSaleNum")).orElse(0));
                dto.setAdSelfSaleNum(Optional.of(re.getInt("adSelfSaleNum")).orElse(0));
                dto.setType(re.getString("type"));
                dto.setCountDate(re.getString("count_date"));
                return dto;
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }


    @Override
    public List<CampaignInfoPageVo> getReportByCampaignIds(Integer puid, CampaignPageParam param, List<String> campaignIdList) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuffer selectSql = new StringBuffer("SELECT campaign_id,type,sum(`cost`) cost, sum(if (type = 'sp', sales7d,sales14d)) total_sales ,");
        selectSql.append("sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales, sum(`impressions`) impressions, sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) sale_num, ");
        selectSql.append("sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_sale_num,");
        selectSql.append("sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) order_num,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_order_num, ");
        selectSql.append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d,sum(view_impressions) view_impressions, max(top_of_search_is) max_top_is, min(top_of_search_is) min_top_is, ");
        selectSql.append("sum(`new_to_brand_detail_page_views`) AS newToBrandDetailPageViews, ");
        selectSql.append("sum(`add_to_cart`) AS addToCart, ");
        selectSql.append("sum(`video5second_views`) AS video5SecondViews, ");
        selectSql.append("sum(`video_first_quartile_views`) AS videoFirstQuartileViews, ");
        selectSql.append("sum(`video_Midpoint_Views`) AS videoMidpointViews, ");
        selectSql.append("sum(`video_third_quartile_views`) AS videoThirdQuartileViews, ");
        selectSql.append("sum(`video_complete_views`) AS videoCompleteViews, ");
        selectSql.append("sum(`video_unmutes`) AS videoUnmutes, ");
        selectSql.append("sum(`viewable_impressions`) AS viewableImpressions, ");
        selectSql.append("sum(`branded_searches14d`) AS brandedSearches, ");
        selectSql.append("sum(`detail_page_view14d`) AS detailPageViews ");
        //todo check
        selectSql.append("FROM " + getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)) + " IGNORE INDEX(idx_campaign_id_cost) where ")                      ;

        StringBuffer whereSql = new StringBuffer();
        whereSql.append("puid = ? ");
        argsList.add(param.getPuid());
        whereSql.append(" and shop_id = ? ");
        argsList.add(param.getShopId());
        whereSql.append(" and marketplace_id = ? ");
        argsList.add(param.getMarketplaceId());
        whereSql.append(" and is_summary=1 and count_date >= ? and count_date <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
        }
        whereSql.append(" group by campaign_id");
        selectSql.append(whereSql);
        if (param.getUseAdvanced()) {//高级搜索sql拼接
            selectSql.append(getCampaignPageHavingSql(param, argsList));
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(CampaignInfoPageVo.class));
        } finally {
            hintManager.close();
        }
    }

    private String getCampaignPageHavingSql(CampaignPageParam param, List<Object> argsList) {
        ReportAdvancedFilterBaseQo qo = new ReportAdvancedFilterBaseQo();
        BeanUtils.copyProperties(param, qo);
        return SqlStringReportUtil.getCampaignHavingSqlNew(qo, argsList);
    }

    @Override
    public List<AdHomePerformancedto> getSpReportByCampaignIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> campaignIdList, String type) {
        if (CollectionUtils.isEmpty(campaignIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select  campaign_id,count_date,type,sum(`cost`) cost,sum(if (type = 'sp', sales7d,sales14d)) total_sales,")
                .append("sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) order_num, sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_order_num,")
                .append("sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) sale_num,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_sale_num, ")
                .append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d ,sum(view_impressions) view_impressions ");
        sql.append(" from ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where puid= ? and shop_id= ? ");

        argsList.add(puid);
        argsList.add(shopId);
        if(StringUtils.isNotBlank(type)){
//            sql.append(" and type = ? ");
//            argsList.add(type);
            sql.append(SqlStringUtil.dealInList("type", StringUtil.splitStr(type), argsList));

        }
        sql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));

        sql.append(" and is_summary=1 and count_date >= ? and count_date <= ? group by count_date ");
        argsList.add(startStr);
        argsList.add(endStr);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .campaignId(re.getString("campaign_id"))
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            //广告订单量
                            .adOrderNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))  //销量字段订单
                            //本广告产品订单量
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            //广告销售额
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //“品牌新买家”销售额
                            .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                            //“品牌新买家”订单量
                            .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                            //“品牌新买家”销量
                            .unitsOrderedNewToBrand14d(Optional.ofNullable(re.getInt("units_ordered_new_to_brand14d")).orElse(0))
                            //可见展示次数
                            .viewImpressions(Optional.ofNullable(re.getInt("view_impressions")).orElse(0))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            .type(re.getString("type"))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }


    private StringBuilder subWhereSql(CampaignPageParam param, List<Object> argsList) {
        final String VIEW_IMPRESSIONS = "if (type = 'sb' ,`viewable_impressions`,`view_impressions`)";

        StringBuilder subWhereSql = new StringBuilder();
        //高级筛选
        if(param.getUseAdvanced()){
            BigDecimal shopSales = param.getShopSales() != null ? param.getShopSales() : BigDecimal.valueOf(0);

            subWhereSql.append(" having 1=1 ");
            //展示量
            if(param.getImpressionsMin() != null){
                subWhereSql.append(" and impressions >= ?");
                argsList.add(param.getImpressionsMin());
            }
            if(param.getImpressionsMax() != null){
                subWhereSql.append(" and impressions <= ?");
                argsList.add(param.getImpressionsMax());
            }
            //点击量
            if(param.getClicksMin() != null){
                subWhereSql.append(" and clicks >= ?");
                argsList.add(param.getClicksMin());
            }
            if(param.getClicksMax() != null){
                subWhereSql.append(" and clicks <= ?");
                argsList.add(param.getClicksMax());
            }
            //点击率（clicks/impressions）
            if(param.getClickRateMin() != null){
                subWhereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(param.getClickRateMin());
            }
            if(param.getClickRateMax() != null){
                subWhereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(param.getClickRateMax());
            }
            //花费
            if(param.getCostMin() != null){
                subWhereSql.append(" and cost >= ?");
                argsList.add(param.getCostMin());
            }
            if(param.getCostMax() != null){
                subWhereSql.append(" and cost <= ?");
                argsList.add(param.getCostMax());
            }
            //cpc  平均点击费用
            if(param.getCpcMin() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(param.getCpcMin());
            }
            if(param.getCpcMax() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(param.getCpcMax());
            }
            //广告订单量
            if(param.getOrderNumMin() != null){
                subWhereSql.append(" and order_num >= ?");
                argsList.add(param.getOrderNumMin());
            }
            if(param.getOrderNumMax() != null){
                subWhereSql.append(" and order_num <= ?");
                argsList.add(param.getOrderNumMax());
            }
            //广告销售额
            if(param.getSalesMin() != null){
                subWhereSql.append(" and total_sales >= ?");
                argsList.add(param.getSalesMin());
            }
            if(param.getSalesMax() != null){
                subWhereSql.append(" and total_sales <= ?");
                argsList.add(param.getSalesMax());
            }
            //订单转化率
            if(param.getSalesConversionRateMin() != null){
                subWhereSql.append(" and ROUND(ifnull(order_num/clicks,0),4) >= ?");
                argsList.add(param.getSalesConversionRateMin());
            }
            if(param.getSalesConversionRateMax() != null){
                subWhereSql.append(" and ROUND(ifnull(order_num/clicks,0),4) <= ?");
                argsList.add(param.getSalesConversionRateMax());
            }
            //acos
            if(param.getAcosMin() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(param.getAcosMin());
            }
            if(param.getAcosMax() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(param.getAcosMax());
            }
            // roas
            if (param.getRoasMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(param.getRoasMin());
            }
            // roas
            if (param.getRoasMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(param.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAcotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAcotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (param.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAsotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (param.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAsotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }

            /******************************高级搜索新增查询指标*****************************/
            //可见展示次数
            if (param.getViewImpressionsMin() != null) {
                subWhereSql.append(" and " + VIEW_IMPRESSIONS + " >= ? ");
                argsList.add(param.getViewImpressionsMin());
            }
            if (param.getViewImpressionsMax() != null) {
                subWhereSql.append(" and " + VIEW_IMPRESSIONS + " <= ? ");
                argsList.add(param.getViewImpressionsMax());
            }


            //广告销量
            if(param.getAdSalesTotalMin() != null){
                subWhereSql.append(" and sale_num >= ? ");
                argsList.add(param.getAdSalesTotalMin());
            }

            if(param.getAdSalesTotalMax() != null){
                subWhereSql.append(" and sale_num <= ? ");
                argsList.add(param.getAdSalesTotalMax());
            }


            //CPA
            if (param.getCpaMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(cost/order_num, 0), 2) >= ? ");
                argsList.add(param.getCpaMin());
            }
            if (param.getCpaMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(cost/order_num, 0), 2) <= ? ");
                argsList.add(param.getCpaMax());
            }
            //VCPM= 每千次展现费用=广告花费/可见展现次数x1000
            if (param.getVcpmMin() != null) {
                subWhereSql.append(" and ROUND(ifnull((cost/" + VIEW_IMPRESSIONS + ") * 1000, 0), 4) >= ? ");
                argsList.add(param.getVcpmMin());
            }
            if (param.getVcpmMax() != null) {
                subWhereSql.append(" and ROUND(ifnull((cost/" + VIEW_IMPRESSIONS + ") * 1000, 0), 4) <= ? ");
                argsList.add(param.getVcpmMax());
            }
            //本广告产品订单量（绝对值）adSaleNumMin
            if (param.getAdSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(ad_order_num, 0) >= ? ");
                argsList.add(param.getAdSaleNumMin());
            }
            if (param.getAdSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(ad_order_num, 0) <= ? ");
                argsList.add(param.getAdSaleNumMax());
            }

            //其他产品广告订单量（绝对值） adOtherOrderNumMin
            if (param.getAdOtherOrderNumMin() != null) {
                subWhereSql.append(" and ifnull(order_num - ad_order_num, 0) >= ? ");
                argsList.add(param.getAdOtherOrderNumMin());
            }

            if (param.getAdOtherOrderNumMax() != null) {
                subWhereSql.append(" and ifnull(order_num - ad_order_num, 0) <= ? ");
                argsList.add(param.getAdOtherOrderNumMax());
            }

            //本广告产品销售额（绝对值） adSalesMin
            if (param.getAdSalesMin() != null) {
                subWhereSql.append(" and ifnull(ad_sales, 0) >= ? ");
                argsList.add(param.getAdSalesMin());
            }

            if (param.getAdSalesMax() != null) {
                subWhereSql.append(" and ifnull(ad_sales, 0) <= ? ");
                argsList.add(param.getAdSalesMax());
            }

            //其他产品广告销售额（绝对值）adOtherSalesMin
            if (param.getAdOtherSalesMin() != null) {
                subWhereSql.append(" and ifnull(total_sales - ad_sales, 0) >= ? ");
                argsList.add(param.getAdOtherSalesMin());
            }

            if (param.getAdOtherSalesMax() != null) {
                subWhereSql.append(" and ifnull(total_sales - ad_sales, 0) <= ? ");
                argsList.add(param.getAdOtherSalesMax());
            }

            //本广告产品销量（绝对值）adSelfSaleNumMin units_ordered7d_same_sku
            if (param.getAdSelfSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(ad_sale_num, 0) >= ? ");
                argsList.add(param.getAdSelfSaleNumMin());
            }
            if (param.getAdSelfSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(ad_sale_num, 0) <= ? ");
                argsList.add(param.getAdSelfSaleNumMax());
            }

            //其他产品广告销量（绝对值）adOtherSaleNumMin
            if (param.getAdOtherSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(sale_num - ad_sale_num, 0) >= ? ");
                argsList.add(param.getAdOtherSaleNumMin());
            }
            if (param.getAdOtherSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(sale_num - ad_sale_num, 0) <= ? ");
                argsList.add(param.getAdOtherSaleNumMax());
            }

            //“品牌新买家”订单量（绝对值） orders_new_to_brand14d
            if (param.getOrdersNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ifnull(orders_new_to_brand14d, 0) >= ? ");
                argsList.add(param.getOrdersNewToBrandFTDMin());
            }
            if (param.getOrdersNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ifnull(orders_new_to_brand14d, 0) <= ? ");
                argsList.add(param.getOrdersNewToBrandFTDMax());
            }



            //“品牌新买家”订单百分比（百分比） orderRateNewToBrandFTDMin
            if (param.getOrderRateNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((orders_new_to_brand14d/order_num) * 100, 0), 4), 2) >= ? ");
                argsList.add(MathUtil.multiplyZero(param.getOrderRateNewToBrandFTDMin(), BigDecimal.valueOf(100)));
            }
            if (param.getOrderRateNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((orders_new_to_brand14d/order_num) * 100, 0), 4), 2) <= ? ");
                argsList.add(MathUtil.multiplyZero(param.getOrderRateNewToBrandFTDMax(), BigDecimal.valueOf(100)));
            }

            //“品牌新买家”销售额（绝对值） salesNewToBrandFTDMin
            if (param.getSalesNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ifnull(sales_new_to_brand14d, 0) >= ? ");
                argsList.add(param.getSalesNewToBrandFTDMin());
            }

            if (param.getSalesNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ifnull(sales_new_to_brand14d, 0) <= ? ");
                argsList.add(param.getSalesNewToBrandFTDMax());
            }


            //“品牌新买家”销售额百分比（百分比） salesRateNewToBrandFTDMin
            if (param.getSalesRateNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((sales_new_to_brand14d/total_sales) * 100, 0), 4), 2) >= ? ");
                argsList.add(MathUtil.multiplyZero(param.getSalesRateNewToBrandFTDMin(), BigDecimal.valueOf(100)));
            }
            if (param.getSalesRateNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((sales_new_to_brand14d/total_sales) * 100, 0), 4), 2) <= ? ");
                argsList.add(MathUtil.multiplyZero(param.getSalesRateNewToBrandFTDMax(), BigDecimal.valueOf(100)));
            }

            //“品牌新买家”销量（绝对值）unitsOrderedNewToBrandFTDMin   units_ordered_new_to_brand14d
            if (param.getUnitsOrderedNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ifnull(units_ordered_new_to_brand14d, 0) >= ? ");
                argsList.add(param.getUnitsOrderedNewToBrandFTDMin());
            }
            if (param.getUnitsOrderedNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ifnull(units_ordered_new_to_brand14d, 0) <= ? ");
                argsList.add(param.getUnitsOrderedNewToBrandFTDMax());
            }


            //“品牌新买家”销量百分比（百分比）unitsOrderedRateNewToBrandFTDMin
            if (param.getUnitsOrderedRateNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((units_ordered_new_to_brand14d/sale_num) * 100, 0), 4), 2) >= ? ");
                argsList.add(MathUtil.multiplyZero(param.getUnitsOrderedRateNewToBrandFTDMin(), BigDecimal.valueOf(100)));
            }
            if (param.getUnitsOrderedRateNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((units_ordered_new_to_brand14d/sale_num) * 100, 0), 4), 2) <= ? ");
                argsList.add(MathUtil.multiplyZero(param.getUnitsOrderedRateNewToBrandFTDMax(), BigDecimal.valueOf(100)));
            }

            // 加购次数 筛选
            if (param.getAddToCartMin() != null) {
                subWhereSql.append(" and ifnull(add_to_cart , 0) >= ? ");
                argsList.add(param.getAddToCartMin());
            }
            if (param.getAddToCartMax() != null ) {
                subWhereSql.append(" and ifnull(add_to_cart , 0) <= ? ");
                argsList.add(param.getAddToCartMax());
            }

            // 5秒观看次数 筛选
            if (param.getVideo5SecondViewsMin() != null ) {
                subWhereSql.append(" and ifnull(video5second_views , 0) >= ? ");
                argsList.add(param.getVideo5SecondViewsMin());
            }
            if (param.getVideo5SecondViewsMax() != null) {
                subWhereSql.append(" and ifnull(video5second_views , 0) <= ? ");
                argsList.add(param.getVideo5SecondViewsMax());
            }

            // 视频完整播放次数 筛选
            if (param.getVideoCompleteViewsMin() != null ) {
                subWhereSql.append(" and ifnull(video_complete_views , 0) >= ? ");
                argsList.add(param.getVideoCompleteViewsMin());
            }
            if (param.getVideoCompleteViewsMax() != null ) {
                subWhereSql.append(" and ifnull(video_complete_views , 0) <= ? ");
                argsList.add(param.getVideoCompleteViewsMax());
            }

            // 观看率 筛选
            if (param.getViewabilityRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull((" + VIEW_IMPRESSIONS + "/impressions) * 100, 0), 2) >= ? ");
                argsList.add(param.getViewabilityRateMin());
            }
            if (param.getViewabilityRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull((" + VIEW_IMPRESSIONS + "/impressions) * 100, 0), 2) <= ? ");
                argsList.add(param.getViewabilityRateMax());
            }

            // 观看点击率 筛选
            if (param.getViewClickThroughRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull((clicks/" + VIEW_IMPRESSIONS + ") * 100, 0), 2) >= ? ");
                argsList.add(param.getViewClickThroughRateMin());
            }
            if (param.getViewClickThroughRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull((clicks/" + VIEW_IMPRESSIONS + ") * 100, 0), 2) <= ? ");
                argsList.add(param.getViewClickThroughRateMax());
            }

            // 品牌搜索次数 筛选
            if (param.getBrandedSearchesMin() != null) {
                subWhereSql.append(" and ifnull(branded_searches14d , 0) >= ? ");
                argsList.add(param.getBrandedSearchesMin()); }
            if (param.getBrandedSearchesMax() != null) {
                subWhereSql.append(" and ifnull(branded_searches14d , 0) <= ? ");
                argsList.add(param.getBrandedSearchesMax());
            }

            // 广告笔单价 筛选
            if (param.getAdvertisingUnitPriceMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/order_num, 0), 2) >= ?");
                argsList.add(param.getAdvertisingUnitPriceMin());
            }
            if (param.getAdvertisingUnitPriceMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/order_num, 0), 2) <= ?");
                argsList.add(param.getAdvertisingUnitPriceMax());
            }

            // 搜索结果首页首位IS
            if (param.getTopImpressionShareMin() != null){
                subWhereSql.append(" and max(top_of_search_is) >= ?");
                argsList.add(param.getTopImpressionShareMin());
            }
            if (param.getTopImpressionShareMax() != null){
                subWhereSql.append(" and min(top_of_search_is) <= ?");
                argsList.add(param.getTopImpressionShareMax());
            }
        }
        return subWhereSql;
    }

    @Override
    public AmazonAdCampaignAllReport getSumReportByCampaignId(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId) {
        StringBuilder sql = new StringBuilder("SELECT type,sum(`cost`) cost,sum(if (type = 'sp', sales7d,sales14d)) total_sales,")
                .append("sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) order_num, sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_order_num,")
                .append("sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) sale_num,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_sale_num, ")
                .append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d,sum(view_impressions) view_impressions ")
                .append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where`puid`=? and `shop_id`=?");
        sql.append("  and`marketplace_id`=? and campaign_id=? and `count_date`>=? and count_date<=? and `is_summary`=1 ");

        HintManager hintManager = HintManager.getInstance();
        try {
            List<AmazonAdCampaignAllReport> list = getJdbcTemplate(puid, hintManager).query(sql.toString(),new Object[]{puid,shopId,marketplaceId,campaignId,startStr,endStr},getMapper());
            return list!=null && list.size()>0?list.get(0):null;
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdCampaignAllReport> getSumReportByAllCampaignIds(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, List<String> campaignIds) {
        String sql = "SELECT campaign_id,sum(`cost`) cost, "+
                "sum(if (type = 'sp', sales7d,sales14d)) total_sales ," +
                "sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) order_num, " +
                "sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_order_num," +
                "sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) sale_num,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_sale_num, " +
                "sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d,sum(view_impressions) view_impressions " +
                "FROM " + getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketplaceId)
                .greaterThanOrEqualTo("count_date", startStr)
                .lessThanOrEqualTo("count_date", endStr)
                .equalTo("is_summary",1)
                .in("campaign_id", campaignIds.toArray())
                .groupBy("campaign_id")
                .build();

        sql += conditionBuilder.getSql();

        HintManager hintManager = HintManager.getInstance();
        try {
            List<AmazonAdCampaignAllReport> list = getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
            return list;
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AdHomePerformancedto> getAllDayReportByAllCampaignIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> campaignIdList) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select count_date, sum(`cost`) cost, sum(if (r.type = 'sp', sales7d,sales14d)) total_sales,")
                .append("sum(if (r.type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (r.type = 'sp' , conversions7d,conversions14d)) order_num, sum(if (r.type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_order_num,")
                .append("sum(if (r.type = 'sp' ,`units_ordered7d`,if (r.type = 'sb' ,`units_sold14d`,`units_ordered14d`))) sale_num,sum(if (r.type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_sale_num, ")
                .append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d, sum(view_impressions) view_impressions ");
        sql.append(" from ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" r where r.puid= ? and shop_id= ?");

        argsList.add(puid);
        argsList.add(shopId);

        sql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));

        sql.append(" and is_summary=1 and count_date >= ? and count_date <= ? group by count_date ");
        argsList.add(startStr);
        argsList.add(endStr);


        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .countDate(re.getString("count_date"))
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .orderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                            .adOrderNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))  //销量字段订单
                            .salesNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                            .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                            .viewImpressions(Optional.ofNullable(re.getInt("view_impressions")).orElse(0))
                            .unitsOrderedNewToBrand14d(Optional.ofNullable(re.getInt("units_ordered_new_to_brand14d")).orElse(0))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdCampaignAllReport> listReports(Integer puid, Integer shopId, String startStr, String endStr, String campaignId, String type) {
        String sql = "SELECT count_date,campaign_id,shop_id,type,sum(`cost`) cost,sum(if (type = 'sp', sales7d,sales14d)) total_sales,"
                +" sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) order_num, sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_order_num,"
                +"sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) sale_num,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_sale_num, "
                +"sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d ,sum(view_impressions) view_impressions "
                +" FROM " + getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("type",type)
                .greaterThanOrEqualTo("count_date", startStr)
                .lessThanOrEqualTo("count_date", endStr)
                .equalTo("is_summary", 1);

        if (StringUtils.isNotBlank(campaignId)) {
            builder.equalTo("campaign_id", campaignId);
        }

        builder.groupBy("count_date");
        ConditionBuilder conditionBuilder = builder.build();

        sql += conditionBuilder.getSql();

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdCampaignAllReport> listPlacementReports(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId) {
        String sql = "SELECT puid,shop_id,campaign_id,type,campaign_type,sum(cost) cost,sum(if (type = 'sp', sales7d,sales14d)) total_sales,"
                + " sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) order_num, sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_sale_num,"
                + " sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) order_num,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_order_num, "
                + " sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d ,sum(view_impressions) view_impressions "
                + " FROM " + getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketplaceId)
                .equalTo("campaign_id", campaignId)
                .equalTo("type",CampaignTypeEnum.sp.getCampaignType())
                .greaterThanOrEqualTo("count_date", startStr)
                .lessThanOrEqualTo("count_date", endStr)
                .groupBy("campaign_type")
                .build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdCampaignAllReport> listPlacementReports(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId,String placement) {
        String sql = "SELECT puid,shop_id,`count_date`,campaign_id,type,campaign_type,sum(cost) cost,sum(if (type = 'sp', sales7d,sales14d)) total_sales,"
                + " sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) order_num, sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_order_num,"
                + " sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) sale_num,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_sale_num, "
                + " sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d ,sum(view_impressions) view_impressions "
                + " FROM " + getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketplaceId)
                .equalTo("campaign_type",placement)
                .equalTo("campaign_id", campaignId)
                .equalTo("type",CampaignTypeEnum.sp.getCampaignType())
                .greaterThanOrEqualTo("count_date", startStr)
                .lessThanOrEqualTo("count_date", endStr)
                .groupBy("count_date")
                .build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdCampaignAllReport> getChartList(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId, String type) {
        StringBuilder sql = new StringBuilder(" SELECT count_date,campaign_id,campaign_type,shop_id,type,sum(cost) cost,sum(if (type = 'sp', sales7d,sales14d)) total_sales,sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) sale_num, sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_sale_num,"
                +" sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) order_num,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_order_num, "
                +" sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d ,sum(view_impressions) view_impressions "
                +" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where puid=? and shop_id=? and marketplace_id=? and campaign_id=? and count_date>=? and count_date<=? and is_summary=1  ");
        List<Object> args = Lists.newArrayList(puid,shopId,marketplaceId,campaignId,startStr,endStr);
        if(StringUtils.isNotBlank(type)){
            sql.append(" and type = ? ");
            args.add(type);
        }
        sql.append(" order by count_date ");

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(),args.toArray(),getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public Page detailPageList(Integer puid, Integer shopId, String marketplaceId, ReportParam param, Page page) {
        StringBuilder selectSql = new StringBuilder(" SELECT count_date,campaign_id,campaign_name,campaign_type,shop_id,type,sum(cost) cost,sum(if (type = 'sp', sales7d,sales14d)) total_sales,sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) sale_num, sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_sale_num,"
                +" sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) order_num,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_order_num, "
                +" sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d ,sum(view_impressions) view_impressions "
                +" FROM ");
        String queryTable = getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD));
        selectSql.append(queryTable);
        StringBuilder countSql = new StringBuilder("select count(*)").append(" FROM ").append(queryTable);
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and campaign_id=? and count_date>=? and count_date<=? and is_summary=1  ");

        List<Object> argsList = Lists.newArrayList();

        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);
        argsList.add(param.getCampaignId());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        selectSql.append(whereSql);
        countSql.append(whereSql);
        if(StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderValue())){
            String orderField = ReportService.getOrderField(param.getOrderField(),false);
            if(StringUtils.isNotBlank(orderField)){
                selectSql.append(" order by ").append(orderField);
                if("desc".equals(param.getOrderValue())){
                    selectSql.append("desc");
                }
            }
        }else{
            selectSql.append(" order by count_date desc");
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdCampaignAllReport.class);
    }

    @Override
    public AmazonAdCampaignAllReport getDetailInfo(Integer puid, Integer shopId, String marketplaceId, String campaignId) {
        StringBuilder sql = new StringBuilder("select campaign_name,campaign_state,campaign_budget from t_amazon_ad_campaign_all_report where puid=? and shop_id=? and marketplace_id=? and campaign_id=?")
                .append(" order by count_date desc limit 1");

        HintManager hintManager = HintManager.getInstance();
        try {
            List<AmazonAdCampaignAllReport> list = getJdbcTemplate(puid, hintManager).query(sql.toString(),new Object[]{puid,shopId,marketplaceId,campaignId},getMapper());
            return list!=null&&list.size()>0?list.get(0):null;
        } finally {
            hintManager.close();
        }
    }

    @Override
    public AmazonAdCampaignAllReport statByDateRange(Integer puid, Integer shopId, String start, String end, String type) {
        StringBuilder selectSql = new StringBuilder(" SELECT count_date,campaign_id,campaign_type,shop_id,type,sum(cost) cost,sum(if (type = 'sp', sales7d,sales14d)) total_sales,sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) sale_num, sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_sale_num,"
                + " sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) order_num,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_order_num, "
                + " sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d ,sum(view_impressions) view_impressions "
                + " FROM ");
        selectSql.append(getTableNameByStartDate(DateUtil.strToDate(start, DateUtil.PATTERN_YYYYMMDD)));
        selectSql.append(" where puid=? and shop_id=? and count_date>=? and count_date<=?  and is_summary=1 ");
        List<Object> args = Lists.newArrayList(puid, shopId, start, end);
        if (StringUtils.isNotBlank(type)) {
            selectSql.append(" and type = ? ");
            args.add(type);
        }

        HintManager hintManager = HintManager.getInstance();
        try {
            List<AmazonAdCampaignAllReport> list = getJdbcTemplate(puid, hintManager).query(selectSql.toString(), getMapper(), args.toArray());
            return list != null && list.size() > 0 ? list.get(0) : null;
        } finally {
            hintManager.close();
        }

    }

    @Override
    public AmazonAdCampaignAllReport getReportVoByCampaignId(Integer puid, String campaignId, CampaignReportSearchVo searchVo) {
        String sql = " SELECT count_date,campaign_id,campaign_name,campaign_type,shop_id,type,sum(cost) cost,sum(if (type = 'sp', sales7d,sales14d)) total_sales,sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) sale_num, sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_sale_num,"
                +" sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) order_num,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_order_num, "
                +" sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d ,sum(view_impressions) view_impressions "
                +" FROM " + getTableNameByStartDate(searchVo.getStart()) + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", searchVo.getShopId())
                .greaterThanOrEqualTo("count_date", DateUtil.dateToStrWithFormat(searchVo.getStart(),"yyyyMMdd"))
                .lessThanOrEqualTo("count_date", DateUtil.dateToStrWithFormat(searchVo.getEnd(),"yyyyMMdd"))
                .equalTo("campaign_id", campaignId)
                .equalTo("is_summary", 1)
                .equalTo("type",searchVo.getType())
                .build();

        sql += conditionBuilder.getSql();

        HintManager hintManager = HintManager.getInstance();
        try {
            List<AmazonAdCampaignAllReport> query = getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
            return CollectionUtils.isNotEmpty(query) ? query.get(0) : null;
        } finally {
            hintManager.close();
        }

    }

    @Override
    public AmazonAdCampaignAllReport getDetailsSumVo(Integer puid, CampaignReportDetails detailsVo) {
        StringBuilder selectSql = new StringBuilder(" SELECT count_date,campaign_id,campaign_type,shop_id,type,sum(cost) cost,sum(if (type = 'sp', sales7d,sales14d)) total_sales,sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) sale_num, sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_sale_num,"
                + " sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) order_num,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_order_num, "
                + " sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d ,sum(view_impressions) view_impressions "
                + " FROM ");
        selectSql.append(getTableNameByStartDate(DateUtil.strToDate(detailsVo.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and campaign_id= ? and is_summary = 1  ");
        argsList.add(puid);
        argsList.add(detailsVo.getShopId());
        argsList.add(detailsVo.getCampaignId());
        whereSql.append("and count_date>=? and count_date<=? ");
        argsList.add(detailsVo.getStartDate());
        argsList.add(detailsVo.getEndDate());
        if(StringUtils.isNotBlank(detailsVo.getType())){
            whereSql.append(" and type = ? ");
            argsList.add(detailsVo.getType());
        }
        selectSql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            List<AmazonAdCampaignAllReport> reportList = getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
            return CollectionUtils.isNotEmpty(reportList) ? reportList.get(0) : null;
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdCampaignAllReport> getListCampaignDetailsDay(Integer puid, CampaignReportDetails detailsVo) {
        StringBuilder selectSql = new StringBuilder(" SELECT count_date,campaign_id,campaign_type,shop_id,type,sum(cost) cost,sum(if (type = 'sp', sales7d,sales14d)) total_sales,sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) sale_num, sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_sale_num,"
                + " sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) order_num,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_order_num, "
                + " sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d ,sum(view_impressions) view_impressions "
                + " FROM ");
        selectSql.append(getTableNameByStartDate(detailsVo.getStart()));

        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and campaign_id= ?  and is_summary = 1 ");
        argsList.add(puid);
        argsList.add(detailsVo.getShopId());
        argsList.add(detailsVo.getCampaignId());
        if(StringUtils.isNotBlank(detailsVo.getType())){
            whereSql.append(" and type = ? ");
            argsList.add(detailsVo.getType());
        }
        whereSql.append("and count_date>=? and count_date<=?  group by count_date ");
        argsList.add(DateUtil.dateToStrWithFormat(detailsVo.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(detailsVo.getEnd(),"yyyyMMdd"));

        selectSql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }

    }



    @Override
    public AmazonAdCampaignAllReport getSbSumReport(Integer puid, Integer shopId, String marketplaceId, String reportDate, String adFormat) {
        StringBuilder sql = new StringBuilder("SELECT type, sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d,")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks")
                .append(" FROM ")
                .append(getTableNameByStartDate(DateUtil.strToDate(reportDate, DateUtil.PATTERN_YYYYMMDD)))
                .append(" where`puid`=? and`shop_id`=? and`marketplace_id`=? and`count_date`=? and type = 'sb' and`ad_format`=?");

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForObject(sql.toString(),new Object[]{puid,shopId,marketplaceId,reportDate, adFormat},getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdCampaignAllReport> getSbTaskSumReport(Integer puid, Integer shopId, String startDate, String endDate) {
        String sql = "select type, currency, sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`conversions14d`) conversions14d FROM " +
                getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)) +
                " where puid=? and shop_id=?  and type = ? and count_date >= ? and count_date < ? GROUP by count_date";
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(shopId);
        args.add(CampaignTypeEnum.sb.getCampaignType());
        args.add(startDate);
        args.add(endDate);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql,args.toArray(),getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public AmazonAdCampaignAllReport getSbSumReportByDate(Integer puid, Integer shopId, String marketplaceId, String startDate, String endDate) {
        StringBuilder sql = new StringBuilder("SELECT sum(clicks) clicks, sum(impressions) impressions,sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d ");
        sql.append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where`puid`=? and`shop_id`=? and`marketplace_id`=? and count_date>=? and count_date<=? and type = 'sb' ");

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForObject(sql.toString(), new Object[]{puid, shopId, marketplaceId, startDate, endDate}, getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public AmazonAdCampaignAllReport getSbSumReportByDateAndType(Integer puid, Integer shopId, String type, String startDate, String endDate) {
        StringBuilder sql = new StringBuilder("SELECT sum(clicks) clicks, sum(impressions) impressions,sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d ");
        sql.append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where`puid`=? and`shop_id`=? and`ad_format`=? and count_date>=? and count_date<=? and type = 'sb' ");

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForObject(sql.toString(), new Object[]{puid, shopId, type, startDate, endDate}, getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdCampaignAllReport> getSbVideoReportSumByDateAndType(Integer puid, Integer shopId, String type, String startDate, String endDate) {
        StringBuilder sql = new StringBuilder("SELECT campaign_id, sum(clicks) clicks, sum(impressions) impressions,sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d ");
        sql.append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where`puid`=? and`shop_id`=? and`ad_format`=? and count_date>=? and count_date<=? and type = 'sb' group by campaign_id ");
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(shopId);
        args.add(type);
        args.add(startDate);
        args.add(endDate);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(),args.toArray(),getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public Page getSbPageList(Integer puid, SearchVo search, Page page) {
        StringBuilder selectSql = new StringBuilder("select shop_id,marketplace_id,campaign_name,campaign_id,sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`conversions14d`) conversions14d");
        selectSql.append(" from ");
        String queryTable = getTableNameByStartDate(search.getStart());
        selectSql.append(queryTable);
        StringBuilder countSql = new StringBuilder("select count(*) from ( select campaign_id FROM ");
        countSql.append(queryTable);
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        if (search.getShopId() != null) {
            whereSql.append("and shop_id=? ");
            argsList.add(search.getShopId());
        } else if (CollectionUtils.isNotEmpty(search.getShopIds())) {
            whereSql.append("and shop_id in ('").append(StringUtils.join(search.getShopIds(),"','")).append("') ");
        }
        whereSql.append("and count_date>=? and count_date<=? ");
        whereSql.append(" and type = 'sb' ");
        argsList.add(DateUtil.dateToStrWithFormat(search.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(search.getEnd(),"yyyyMMdd"));
        if(StringUtils.isNotBlank(search.getSearchValue())){  //搜索查询
            search.setSearchValue(SqlStringUtil.dealLikeSql(search.getSearchValue()));
            whereSql.append(" and campaign_name like ?");
            argsList.add("%"+ search.getSearchValue()+"%");
        }

        whereSql.append("group by campaign_id ");
        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");
        if(StringUtils.isNotBlank(search.getOrderField()) && StringUtils.isNotBlank(search.getOrderValue())){  //排序
            String field = ReportService.getSbReportField(search.getOrderField(), true);
            if(StringUtils.isNotBlank(field)){
                selectSql.append(" order by ").append(field);
                if("desc".equals(search.getOrderValue())){
                    selectSql.append("desc");
                }
                selectSql.append(" ,campaign_id ");   //增加一个排序字段
                if("desc".equals(search.getOrderValue())){
                    selectSql.append("desc");
                }
            }
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdCampaignAllReport.class);
    }

    @Override
    public Page sbDetailPageList(Integer puid, Integer shopId, String marketplaceId, ReportParam param, Page page) {
        String queryTable = getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD));
        StringBuilder selectSql = new StringBuilder("SELECT * from ").append(queryTable);
        StringBuilder countSql = new StringBuilder("select count(*) from ").append(queryTable);
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and campaign_id=? and type = 'sb' and count_date>=? and count_date<=? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);
        argsList.add(param.getCampaignId());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        selectSql.append(whereSql);
        countSql.append(whereSql);
        if(StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderValue())){
            String field = ReportService.getSbReportField(param.getOrderField(), false);
            if(StringUtils.isNotBlank(field)){
                selectSql.append(" order by ").append(field);
                if("desc".equals(param.getOrderValue())){
                    selectSql.append("desc");
                }
            }
        } else {
            selectSql.append(" order by count_date desc");
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdCampaignAllReport.class);
    }

    @Override
    public List<AmazonAdCampaignAllReport> getSbChartList(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId) {
        String sql = "select * from "+ getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)) +" where puid=? and shop_id=? and marketplace_id=? and campaign_id=? and type = 'sb' and count_date>=? and count_date<=?  order by count_date";
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql,new Object[]{puid,shopId,marketplaceId,campaignId,startStr,endStr},getMapper());
        } finally {
            hintManager.close();
        }

    }


    @Override
    public AmazonAdCampaignAllReport getSbSumReportByCampaignId(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId) {
        StringBuilder sql = new StringBuilder("SELECT sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d,")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks ")
                .append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where`puid`=? and`shop_id`=? ");
        sql.append("  and`marketplace_id`=? and campaign_id=? and `count_date`>=? and count_date<=? and type = 'sb' ");
        HintManager hintManager = HintManager.getInstance();
        try {
            List<AmazonAdCampaignAllReport> list = getJdbcTemplate(puid, hintManager).query(sql.toString(),new Object[]{puid,shopId,marketplaceId,campaignId,startStr,endStr},getMapper());
            return list!=null && list.size() > 0 ? list.get(0) : null;
        } finally {
            hintManager.close();
        }
    }

    @Override
    public AmazonAdCampaignAllReport sbStatByDateRange(Integer puid, Integer shopId, String start, String end) {
        String sql = "SELECT puid, shop_id, campaign_id, sum(`cost`) cost, sum(`sales14d`) sales14d," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks," +
                "sum(`conversions14d`) conversions14d, sum(`conversions14d_same_sku`) conversions14d_same_sku " +
                " FROM " + getTableNameByStartDate(DateUtil.strToDate(start, DateUtil.PATTERN_YYYYMMDD)) + " where puid=? and shop_id=? and count_date>=? and count_date<=? and type = 'sb' ";
        HintManager hintManager = HintManager.getInstance();
        try {
            List<AmazonAdCampaignAllReport> list = getJdbcTemplate(puid, hintManager).query(sql, getMapper(), puid, shopId, start, end);
            return list != null && list.size() > 0 ? list.get(0) : null;
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdCampaignAllReport> sbListReports(Integer puid, Integer shopId, String startDate, String endDate, String campaignId) {
        String sql = "SELECT puid, shop_id, count_date, campaign_id, sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`sales14d_same_sku`) sales14d_same_sku," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`conversions14d`) conversions14d," +
                "sum(`conversions14d_same_sku`) conversions14d_same_sku " +
                " FROM " + getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate)
                .equalTo("type",CampaignTypeEnum.sb.getCampaignType());

        if (StringUtils.isNotBlank(campaignId)) {
            builder.equalToWithoutCheck("campaign_id", campaignId);
        }

        builder.groupBy("count_date");
        ConditionBuilder conditionBuilder = builder.build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdCampaignAllReport> sbListSumReports(Integer puid, Integer shopId, String startDate, String endDate, List<String> campaignIds) {
        String sql = "SELECT puid, shop_id, campaign_id, sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`sales14d_same_sku`) sales14d_same_sku," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`conversions14d`) conversions14d," +
                "sum(`conversions14d_same_sku`) conversions14d_same_sku " +
                " FROM " + getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate)
                .equalTo("type",CampaignTypeEnum.sb.getCampaignType())
                .in("campaign_id", campaignIds.toArray())
                .groupBy("campaign_id")
                .build();

        sql += conditionBuilder.getSql();

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }


    @Override
    public Page getSdPageList(Integer puid, SearchVo search, Page page) {
        StringBuilder selectSql = new StringBuilder("select shop_id,marketplace_id,campaign_name,campaign_id,sum(`cost`) cost, " +
                "sum(`sales14d`) sales14d, sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`conversions14d`) conversions14d,sum(`units_ordered14d`) units_ordered14d")
                .append(" from ");
        String queryTable = getTableNameByStartDate(search.getStart());
        selectSql.append(queryTable);
        StringBuilder countSql = new StringBuilder("select count(*) from ( select campaign_id FROM ");
        countSql.append(queryTable);
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        if (search.getShopId() != null) {
            whereSql.append("and shop_id=? ");
            argsList.add(search.getShopId());
        } else if (CollectionUtils.isNotEmpty(search.getShopIds())) {
            whereSql.append("and shop_id in ('").append(StringUtils.join(search.getShopIds(),"','")).append("') ");
        }
        whereSql.append(" and type = 'sd' ");
        whereSql.append("and count_date>=? and count_date<=? ");
        argsList.add(DateUtil.dateToStrWithFormat(search.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(search.getEnd(),"yyyyMMdd"));
        if(StringUtils.isNotBlank(search.getSearchValue())){  //搜索查询
            search.setSearchValue(SqlStringUtil.dealLikeSql(search.getSearchValue()));
            whereSql.append(" and campaign_name like ?");
            argsList.add("%"+ search.getSearchValue()+"%");
        }
        whereSql.append("group by campaign_id ");
        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");
        if(StringUtils.isNotBlank(search.getOrderField()) && StringUtils.isNotBlank(search.getOrderValue())){  //排序
            String field = ReportService.getSdReportField(search.getOrderField(), true);
            if(StringUtils.isNotBlank(field)){
                selectSql.append(" order by ").append(field);
                if("desc".equals(search.getOrderValue())){
                    selectSql.append("desc");
                }
                selectSql.append(" ,campaign_id ");   //增加一个排序字段
                if("desc".equals(search.getOrderValue())){
                    selectSql.append("desc");
                }
            }
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdCampaignAllReport.class);
    }

    @Override
    public Page sdDetailPageList(Integer puid, Integer shopId, String marketplaceId, ReportParam param, Page page) {
        String queryTable = getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD));
        StringBuilder selectSql = new StringBuilder(" SELECT * from ").append(queryTable);
        StringBuilder countSql = new StringBuilder("select count(*) from ").append(queryTable);
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and campaign_id=? and count_date>=? and count_date<=? and type = 'sd' ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);
        argsList.add(param.getCampaignId());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        selectSql.append(whereSql);
        countSql.append(whereSql);
        if(StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderValue())){
            String field = ReportService.getSdReportField(param.getOrderField(), false);
            if(StringUtils.isNotBlank(field)){
                selectSql.append(" order by ").append(field);
                if("desc".equals(param.getOrderValue())){
                    selectSql.append("desc");
                }
            }
        } else {
            selectSql.append(" order by count_date desc");
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args,AmazonAdCampaignAllReport.class);
    }

    @Override
    public AmazonAdCampaignAllReport getSdSumReportByCampaignId(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId) {
        StringBuilder sql = new StringBuilder("SELECT sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d,")
                .append("sum(`units_ordered14d`) units_ordered14d, sum(`impressions`) impressions,sum(`clicks`) clicks ")
                .append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where`puid`=? and`shop_id`=?");
        sql.append("  and`marketplace_id`=? and campaign_id=? and `count_date`>=? and count_date<=? and type = 'sd'  ");

        HintManager hintManager = HintManager.getInstance();
        try {
            List<AmazonAdCampaignAllReport> list = getJdbcTemplate(puid, hintManager).query(sql.toString(),new Object[]{puid,shopId,marketplaceId,campaignId,startStr,endStr},getMapper());
            return list!=null && list.size() > 0 ? list.get(0) : null;
        } finally {
            hintManager.close();
        }
    }


    @Override
    public List<AmazonAdCampaignAllReport> getSdChartList(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId) {
        String sql = "select * from " + getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)) + " where puid=? and shop_id=? and marketplace_id=?" +
                " and campaign_id=? and count_date>=? and count_date<=? and type = 'sd'  order by count_date";

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql,new Object[]{puid,shopId,marketplaceId,campaignId,startStr,endStr},getMapper());

        } finally {
            hintManager.close();
        }
    }

    @Override
    public AmazonAdCampaignAllReport getSdSumReport(Integer puid, Integer shopId, String marketplaceId, String reportDate, String tacticType) {
        StringBuilder sql = new StringBuilder("SELECT sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d,sum(`units_ordered14d`) units_ordered14d,")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks")
                .append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(reportDate, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where`puid`=? and`shop_id`=? and`marketplace_id`=? and`count_date`=? and`tactic_type`=? and type = 'sd' ");

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForObject(sql.toString(),new Object[]{puid,shopId,marketplaceId,reportDate, tacticType},getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdCampaignAllReport> sdListSumReports(Integer puid, Integer shopId, String startDate, String endDate, List<String> campaignIds) {
        String sql = "SELECT puid, shop_id, campaign_id, sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`sales14d_same_sku`) sales14d_same_sku," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`conversions14d`) units_ordered14d, sum(`conversions14d`) conversions14d," +
                "sum(`conversions14d_same_sku`) conversions14d_same_sku " +
                " FROM " + getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate)
                .equalTo("type",CampaignTypeEnum.sd.getCampaignType())
                .in("campaign_id", campaignIds.toArray())
                .groupBy("campaign_id")
                .build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdCampaignAllReport> sdListReports(Integer puid, Integer shopId, String startDate, String endDate, String campaignId) {
        String sql = "SELECT puid, shop_id, count_date, campaign_id, sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`sales14d_same_sku`) sales14d_same_sku," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`units_ordered14d`) units_ordered14d, sum(`conversions14d`) conversions14d," +
                "sum(`conversions14d_same_sku`) conversions14d_same_sku " +
                " FROM " + getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate)
                .equalTo("type",CampaignTypeEnum.sd.getCampaignType());

        if (StringUtils.isNotBlank(campaignId)) {
            builder.equalToWithoutCheck("campaign_id", campaignId);
        }

        builder.groupBy("count_date");
        ConditionBuilder conditionBuilder = builder.build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public AmazonAdCampaignAllReport sdStatByDateRange(Integer puid, Integer shopId, String start, String end) {
        String sql = "SELECT puid, shop_id, campaign_id, sum(`cost`) cost, sum(`sales14d`) sales14d," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`units_ordered14d`) units_ordered14d," +
                "sum(`conversions14d`) conversions14d, sum(`conversions14d_same_sku`) conversions14d_same_sku " +
                " FROM " + getTableNameByStartDate(DateUtil.strToDate(start, DateUtil.PATTERN_YYYYMMDD)) + " where puid=? and shop_id=? and count_date>=? and count_date<=? and type = 'sd' ";

        HintManager hintManager = HintManager.getInstance();
        try {
            List<AmazonAdCampaignAllReport> list = getJdbcTemplate(puid, hintManager).query(sql, getMapper(), puid, shopId, start, end);
            return list.size() > 0 ? list.get(0) : null;
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> getSdCampaignListByUpdateTime(Integer puid, Integer shopId, Date date) {
        String sql = "select campaign_id from (select sum(`impressions`) impressions, campaign_id from t_amazon_ad_campaign_all_report where " +
                " puid = ? and shop_id=?  and update_time > ? and type = 'sd' group by campaign_id having impressions > 0) a ";
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql, new Object[]{puid,shopId,date}, String.class);
        } finally {
            hintManager.close();
        }

    }

    @Override
    public AmazonAdCampaignAllReport getSdReportVoByCampaignId(Integer puid, String campaignId, CampaignReportSearchVo searchVo) {
        String sql = "SELECT campaign_id,campaign_name,shop_id,marketplace_id,sum(`cost`) cost," +
                " sum(`sales14d`) sales14d, " +
                " sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`conversions14d`) conversions14d " +
                " FROM " + getTableNameByStartDate(searchVo.getStart()) + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", searchVo.getShopId())
                .greaterThanOrEqualTo("count_date", DateUtil.dateToStrWithFormat(searchVo.getStart(),"yyyyMMdd"))
                .lessThanOrEqualTo("count_date", DateUtil.dateToStrWithFormat(searchVo.getEnd(),"yyyyMMdd"))
                .equalTo("campaign_id", campaignId)
                .equalTo("type",CampaignTypeEnum.sd.getCampaignType())
                .groupBy("campaign_id")
                .build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            List<AmazonAdCampaignAllReport> query = getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
            return CollectionUtils.isNotEmpty(query) ? query.get(0) : null;
        } finally {
            hintManager.close();
        }
    }

    @Override
    public AmazonAdCampaignAllReport getSdDetailsSumVo(Integer puid, CampaignReportDetails detailsVo) {
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,marketplace_id, ")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`sales14d`) sales14d, sum(`cost`) cost,")
                .append("sum(`conversions14d`) conversions14d  FROM ");
        selectSql.append(getTableNameByStartDate(DateUtil.strToDate(detailsVo.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and campaign_id= ? ");
        argsList.add(puid);
        argsList.add(detailsVo.getShopId());
        argsList.add(detailsVo.getCampaignId());

        whereSql.append("and count_date>=? and count_date<=? ");
        whereSql.append(" and type = 'sd' ");
        argsList.add(detailsVo.getStartDate());
        argsList.add(detailsVo.getEndDate());
        selectSql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            List<AmazonAdCampaignAllReport> reportList = getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
            return CollectionUtils.isNotEmpty(reportList) ? reportList.get(0) : null;
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdCampaignAllReport> getSdListCampaignDetailsDay(Integer puid, CampaignReportDetails detailsVo) {
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,marketplace_id,count_date, ")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`sales14d`) sales14d, sum(`cost`) cost,")
                .append("sum(`conversions14d`) conversions14d  FROM ");
        selectSql.append(getTableNameByStartDate(detailsVo.getStart()));
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and campaign_id= ? ");
        argsList.add(puid);
        argsList.add(detailsVo.getShopId());
        argsList.add(detailsVo.getCampaignId());

        whereSql.append("and count_date>=? and count_date<=? and type = 'sd' group by count_date ");
        argsList.add(DateUtil.dateToStrWithFormat(detailsVo.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(detailsVo.getEnd(),"yyyyMMdd"));
        selectSql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdCampaignAllReport> getReportByCampaignId(Integer puid, Integer shopId, String startDate, String endDate, String marketplaceId, String campaignId, String type) {
        StringBuilder selectSql = new StringBuilder("SELECT cost_type,shop_id,count_date,marketplace_id,type,campaign_id,campaign_name,cost,if (type = 'sp', sales7d,sales14d) total_sales,")
                .append(" if (type = 'sp', sales7d_same_sku,sales14d_same_sku) ad_sales,`impressions`,clicks,if (type = 'sp' , conversions7d,conversions14d) order_num, if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`) ad_order_num,")
                .append(" if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`)) sale_num,if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`) ad_sale_num, ")
                .append(" view_impressions, detail_page_view14d,")
                .append(" orders_new_to_brand14d, orders_new_to_brand_percentage14d,order_rate_new_to_brand14d,")
                .append(" sales_new_to_brand14d , sales_new_to_brand_percentage14d,units_ordered_new_to_brand14d, units_ordered_new_to_brand_percentage14d ,")
                .append(" vctr, video5second_view_rate, video5second_views, video_first_quartile_views,")
                .append(" video_midpoint_views, video_third_quartile_views, video_unmutes, viewable_impressions,")
                .append(" video_complete_views, vtr FROM ");
        selectSql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)));
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and campaign_id= ? " +
                " and count_date>=? and count_date <= ? and is_summary = 1 and type = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);
        argsList.add(campaignId);
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(type);
        selectSql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }
    }


    @Override
    public List<AmazonAdCampaignAllReport> getReportByCampaignIdList(Integer puid, Integer shopId, String startDate, String endDate, String marketplaceId, List<String> campaignIdList) {
        StringBuilder selectSql = new StringBuilder("SELECT cost_type,shop_id,count_date,marketplace_id,type,campaign_id,campaign_name,cost,if (type = 'sp', sales7d,sales14d) total_sales,")
                .append(" if (type = 'sp', sales7d_same_sku,sales14d_same_sku) ad_sales,`impressions`,clicks,if (type = 'sp' , conversions7d,conversions14d) order_num, if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`) ad_order_num,")
                .append(" if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`)) sale_num,if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`) ad_sale_num, ")
                .append(" view_impressions, detail_page_view14d,")
                .append(" orders_new_to_brand14d, orders_new_to_brand_percentage14d,order_rate_new_to_brand14d,")
                .append(" sales_new_to_brand14d , sales_new_to_brand_percentage14d,units_ordered_new_to_brand14d, units_ordered_new_to_brand_percentage14d ,")
                .append(" vctr, video5second_view_rate, video5second_views, video_first_quartile_views,")
                .append(" video_midpoint_views, video_third_quartile_views, video_unmutes, viewable_impressions,")
                .append(" video_complete_views, vtr FROM ");
        selectSql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)));

        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? " +
                " and count_date>=? and count_date <= ? and is_summary = 1 and type = 'sp'");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);
        argsList.add(startDate);
        argsList.add(endDate);
        if(CollectionUtils.isNotEmpty(campaignIdList)){
            whereSql.append(" and campaign_id in ('").append(StringUtils.join(campaignIdList, "','")).append("') ");
        }
        selectSql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }    }

    @Override
    public List<AmazonAdCampaignAllReport> getReportByCampaignIdListAll(Integer puid, Integer shopId, String startDate, String endDate, String marketplaceId, List<String> campaignIdList) {
        StringBuilder selectSql = new StringBuilder("SELECT cost_type,shop_id,count_date,marketplace_id,type,campaign_id,campaign_name,cost,if (type = 'sp', sales7d,sales14d) total_sales,")
                .append(" if (type = 'sp', sales7d_same_sku,sales14d_same_sku) ad_sales,`impressions`,clicks,if (type = 'sp' , conversions7d,conversions14d) order_num, if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`) ad_order_num,")
                .append(" if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`)) sale_num,if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`) ad_sale_num, ")
                .append(" view_impressions, detail_page_view14d,")
                .append(" orders_new_to_brand14d, orders_new_to_brand_percentage14d,order_rate_new_to_brand14d,")
                .append(" sales_new_to_brand14d , sales_new_to_brand_percentage14d,units_ordered_new_to_brand14d, units_ordered_new_to_brand_percentage14d ,")
                .append(" vctr, video5second_view_rate, video5second_views, video_first_quartile_views,")
                .append(" video_midpoint_views, video_third_quartile_views, video_unmutes, viewable_impressions,")
                .append(" video_complete_views, vtr  FROM ");
        selectSql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)));

        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? " +
                " and count_date>=? and count_date <= ? and is_summary = 1 ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);
        argsList.add(startDate);
        argsList.add(endDate);
        if(CollectionUtils.isNotEmpty(campaignIdList)){
            whereSql.append(" and campaign_id in ('").append(StringUtils.join(campaignIdList, "','")).append("') ");
        }
        selectSql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }
    }

    private List<AmazonAdShopReport> getNewHomeCpcDataOld(Integer puid, List<Integer> shopIds, String countDate){
        countDate = countDate.replaceAll("-","");
        StringBuilder sql = new StringBuilder("SELECT puid, shop_id, count_date, max(update_time) update_time,  SUM(cost) cost, SUM( IF( type= 'sp', sales7d, sales14d)) sales, ");
        sql.append("SUM( IF( type= 'sp', conversions7d, conversions14d)) order_num, SUM(impressions) impressions, SUM(clicks) clicks FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(countDate, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" WHERE puid = ? ");
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        sql.append(" AND count_date >= ? AND is_summary = 1 group by puid, shop_id, count_date");
        args.add(countDate);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), args.toArray(), (re, i)-> {
                AmazonAdShopReport amazonAdShopReport = new AmazonAdShopReport();
                amazonAdShopReport.setPuid(re.getInt("puid"));
                amazonAdShopReport.setShopId(re.getInt("shop_id"));
                amazonAdShopReport.setCountDate(re.getString("count_date"));
                amazonAdShopReport.setCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
                amazonAdShopReport.setTotalSales(Optional.ofNullable(re.getBigDecimal("sales")).orElse(BigDecimal.ZERO));
                amazonAdShopReport.setOrderNum(re.getInt("order_num"));
                amazonAdShopReport.setImpressions(re.getInt("impressions"));
                amazonAdShopReport.setClicks(re.getInt("clicks"));
                amazonAdShopReport.setUpdateTime(new Date(re.getTimestamp("update_time").getTime()));
                return amazonAdShopReport;
            });
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdShopReport> getNewHomeCpcData(Integer puid, List<Integer> shopIds, String countDate) {
        if (dynamicRefreshConfiguration.verifyDorisHome(puid)) {
            StringBuilder sql = new StringBuilder("SELECT puid, shop_id, count_date, max(update_time) update_time,  SUM(cost) cost, SUM( IF( type= 'sp', sales7d, sales14d)) sales, ");
            sql.append("SUM( IF( type= 'sp', conversions7d, conversions14d)) order_num, SUM(impressions) impressions, SUM(clicks) clicks FROM ");
            sql.append(" ods_t_amazon_ad_campaign_all_report ");
            sql.append("WHERE puid = ? ");
            List<Object> args = Lists.newArrayList();
            args.add(puid);
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
            sql.append(" AND count_day >= ? AND is_summary = 1 group by puid, shop_id, count_date");
            args.add(countDate);
            return jdbcTemplateFeedDorisDb.query(sql.toString(), args.toArray(), (re, i)-> {
                AmazonAdShopReport amazonAdShopReport = new AmazonAdShopReport();
                amazonAdShopReport.setPuid(re.getInt("puid"));
                amazonAdShopReport.setShopId(re.getInt("shop_id"));
                amazonAdShopReport.setCountDate(re.getString("count_date"));
                amazonAdShopReport.setCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
                amazonAdShopReport.setTotalSales(Optional.ofNullable(re.getBigDecimal("sales")).orElse(BigDecimal.ZERO));
                amazonAdShopReport.setOrderNum(re.getInt("order_num"));
                amazonAdShopReport.setImpressions(re.getInt("impressions"));
                amazonAdShopReport.setClicks(re.getInt("clicks"));
                amazonAdShopReport.setUpdateTime(new Date(re.getTimestamp("update_time").getTime()));
                return amazonAdShopReport;
            });
        }
        return getNewHomeCpcDataOld(puid, shopIds, countDate);
    }

    @Override
    public List<String> getDiagnoseCountCampaignId(DiagnoseCountParam diagnoseCountParam) {
        StringBuilder sql = new StringBuilder("select distinct campaign_id from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where ");
        sql.append(" puid = ? and shop_id in ( ").append(StringUtils.join(diagnoseCountParam.getShopIdList(), ",")).append(" )");
        sql.append(" and marketplace_id = ?");
        sql.append(" and type = ?");
        sql.append(" and count_date >= ? ");
        sql.append(" and count_date <= ? ");
        //通过广告产品的活动id过滤
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(diagnoseCountParam.getAdCamgaignIdList())) {
            sql.append(" and campaign_id in ( '").append(StringUtils.join(diagnoseCountParam.getAdCamgaignIdList(), "','")).append("') ");
        }

        sql.append(" limit ").append(Constants.TOTALSIZELIMIT);  // 限制10万

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(diagnoseCountParam.getPuid(), hintManager).query(sql.toString(), new SingleColumnRowMapper<String>(),
                    diagnoseCountParam.getPuid(), diagnoseCountParam.getMarketplaceId(), diagnoseCountParam.getType(),
                    DateUtil.format(DateUtil.strToDate(diagnoseCountParam.getStartDate(), DateUtil.PATTERN), DateUtil.PATTERN_YYYYMMDD),
                    DateUtil.format(DateUtil.strToDate(diagnoseCountParam.getEndDate(), DateUtil.PATTERN), DateUtil.PATTERN_YYYYMMDD));
        } finally {
            hintManager.close();
        }
    }

    public List<String> getValidRecordByDate(int puid, int shopId, String marketId, String startDate, List<String> campaignIds){
        StringBuilder sqlSb = new StringBuilder(" select distinct campaign_id from ");
        sqlSb.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)));
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? and marketplace_id = ? and count_date >= ? and impressions > 0 ");
        List<Object> argsList = Lists.newArrayList(puid, shopId, marketId, startDate);
        whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sqlSb.append(whereSql).toString(), argsList.toArray(), String.class);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdCampaignAllReport> getFirstPlaceIsByCampaignId(int puid, int shopId, String marketId, String startDate, String endDate, String campaignId, String adType) {
        StringBuilder sql = new StringBuilder("SELECT count_date, sum(`top_of_search_is`) top_of_search_is")
                .append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN)));
        sql.append(" where `puid`=? and`shop_id`=? and campaign_id = ? and `is_summary`= 1 and `type` = ? and `count_date`>= ? and `count_date`<= ? ");
        List<Object> args = Lists.newArrayList(puid, shopId, campaignId,adType, DateUtil.dateToStrWithFormat(DateUtil.strToDate(startDate, DateUtil.PATTERN),"yyyyMMdd"), DateUtil.dateToStrWithFormat(DateUtil.strToDate(endDate, DateUtil.PATTERN),"yyyyMMdd"));
        sql.append(" group by count_date");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(),args.toArray(),getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdCampaignAllReport> getFirstPlaceIsByCampaignId(int puid, List<Integer> shopIdList,String startDate, String endDate, List<String> campaignId) {
        StringBuilder sql = new StringBuilder("SELECT campaign_id, max(top_of_search_is) max_top_is, min(top_of_search_is) min_top_is")
                .append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN)));
        sql.append(" where `puid`=?  and `is_summary`= 1 and `type` = 'sp' and `count_date`>= ? and `count_date`<= ? ");
        List<Object> args = Lists.newArrayList(puid, DateUtil.dateToStrWithFormat(DateUtil.strToDate(startDate, DateUtil.PATTERN),"yyyyMMdd"), DateUtil.dateToStrWithFormat(DateUtil.strToDate(endDate, DateUtil.PATTERN),"yyyyMMdd"));
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealInList("shop_id",shopIdList,args));
        }
        if (CollectionUtils.isNotEmpty(campaignId)) {
            sql.append(SqlStringUtil.dealInList("campaign_id",campaignId,args));
        }
        sql.append(" group by campaign_id ");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(),args.toArray(),getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append(" and shop_id = ? ");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }


    @Override
    public List<AmazonAdCampaignAllReport> getSdSumReportGroupByCountDate(Integer puid, Integer shopId, String marketplaceId, String startDate, String endDate, String tacticType) {
        StringBuilder sql = new StringBuilder("SELECT count_date, sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d,sum(`units_ordered14d`) units_ordered14d,")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks")
                .append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where`puid`=? and`shop_id`=? and `marketplace_id`=? and `count_date` <= ? and count_date >= ? and `tactic_type`=? and type = 'sd' group by count_date ");

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(),new Object[]{puid,shopId,marketplaceId,endDate,startDate, tacticType},getMapper());
        } finally {
            hintManager.close();
        }
    }


    @Override
    public List<AmazonAdCampaignAllReport> getSbSumReportGroupByCountDate(Integer puid, Integer shopId, String marketplaceId, String startDate, String endDate, String adFormat) {
        StringBuilder sql = new StringBuilder("SELECT type,count_date, sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d,")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks")
                .append(" FROM ")
                .append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)))
                .append(" where`puid`=? and`shop_id`=? and`marketplace_id`=?  and `count_date` <= ? and count_date >= ? and type = 'sb' and`ad_format`=? group by count_date ");

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new Object[]{puid, shopId, marketplaceId, endDate, startDate, adFormat}, getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> getCampaignIdListByParam(Integer puid, CampaignPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select campaign_id campaignId from ")
                .append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));
        sb.append(" where puid = ? and shop_id = ? and marketplace_id = ? and is_summary=1 and count_date >= ? and count_date <= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(param.getMarketplaceId());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            sb.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), argsList));
        }
        //因为只有sp广告存在本广告销量和其他广告销量，所以在高级搜索时，直接过滤SP数据防止数据不一致
        if (Objects.nonNull(param.getAdSelfSaleNumMin()) || Objects.nonNull(param.getAdSelfSaleNumMax()) ||
                Objects.nonNull(param.getAdOtherSaleNumMin()) || Objects.nonNull(param.getAdOtherSaleNumMax())) {
            sb.append(" and `type` = 'sp' ");
        }
        sb.append(" group by campaign_id ");
        if (param.getUseAdvanced()) {
            sb.append(this.getCampaignPageHavingSql(param, argsList));
        }
        sb.append(" limit " + Constants.CAMPAIGN_PAGE_SUM_METRIC_TIME);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sb.toString(), argsList.toArray(), String.class);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AllCampaignOrderBo> getCampaignIdAndIndexList(Integer puid, CampaignPageParam param) {
        return this.getCampaignIdAndIndexList(puid, param, false, true);
    }

    public List<AllCampaignOrderBo> getCampaignIdAndIndexList(Integer puid, CampaignPageParam param, boolean isLatest, boolean isUseAdvanced) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select campaign_id id, ")
                .append(SqlStringReportUtil.getAllCampaignOrderField(param.getOrderField())).append(" orderField ")
                .append(" from ").append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));

        sb.append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (StringUtils.isNotEmpty(param.getMarketplaceId())) {
            sb.append(" and marketplace_id = ? ");
            argsList.add(param.getMarketplaceId());
        }
        sb.append(" and is_summary=1 ");
        if (!isLatest) {
            sb.append(" and count_date >= ? and count_date <= ? ");
            argsList.add(param.getStartDate());
            argsList.add(param.getEndDate());
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            sb.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), argsList));
        }
        sb.append(" group by campaign_id ");
        if (isUseAdvanced) {
            sb.append(this.getCampaignPageHavingSql(param, argsList));
        }
        sb.append(" limit " + Constants.CAMPAIGN_PAGE_QUERY_REPORT_ID_LIMIT);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AllCampaignOrderBo.class));
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdHomePerformancedto> getReportDataByCampaignIdList(Integer puid, CampaignPageParam param, List<String> campaignIdList) {
        //按天聚合
        StringBuilder selectSql = new StringBuilder(" select  count_date, `type`,sum(`cost`) cost,sum(if (type = 'sp', sales7d,sales14d)) total_sales,")
                .append("sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) order_num, sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_order_num,")
                .append("sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) sale_num,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_sale_num, ")
                .append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d , ")
                .append("sum(if (type = 'sb', `viewable_impressions`, `view_impressions`)) `view_impressions`, ")
                .append("sum(`new_to_brand_detail_page_views`) AS `new_to_brand_detail_page_views`, ")
                .append("sum(`add_to_cart`) AS `add_to_cart`, ")
                .append("sum(`video5second_views`) AS `video5second_views`, ")
                .append("sum(`video_first_quartile_views`) AS `video_first_quartile_views`, ")
                .append("sum(`video_Midpoint_Views`) AS `video_Midpoint_Views`, ")
                .append("sum(`video_third_quartile_views`) AS `video_third_quartile_views`, ")
                .append("sum(`video_complete_views`) AS `video_complete_views`, ")
                .append("sum(`video_unmutes`) AS `video_unmutes`, ")
                .append("sum(`branded_searches14d`) AS `branded_searches14d`, ")
                .append("sum(`detail_page_view14d`) AS `detail_page_view14d` ")
                .append("FROM ").append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD))).append(" IGNORE INDEX(idx_campaign_id_cost) where ");//这里之所以需要忽略掉此条索引是因为，大用户库中的数据存在不均匀的情况

        //无法走puid,shopId,marketplaceId..此条sql，如果不忽略此条索引，mysql执行优化器会因为检索条数问题，不走含有count_date的索引，导致查询变慢
        StringBuilder whereSql = new StringBuilder();
        List<Object> argsList = new ArrayList<>();
        whereSql.append(" puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (StringUtils.isNotEmpty(param.getMarketplaceId())) {
            whereSql.append(" and marketplace_id = ? ");
            argsList.add(param.getMarketplaceId());
        }
        whereSql.append(" and count_date >= ? and count_date <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        whereSql.append(" and is_summary = 1 ");
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
        }
        whereSql.append(" group by count_date ");
        selectSql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), (re, i) -> {
                AdHomePerformancedto dto = AdHomePerformancedto.builder()
//                        .campaignId(re.getString("campaign_id"))
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        //广告订单量
                        .adOrderNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))  //销量字段订单
                        //本广告产品订单量
                        .adSaleNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                        //广告销售额
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        //广告销量
                        .salesNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                        //本广告产品销量
                        .orderNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                        //“品牌新买家”销售额
                        .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                        //“品牌新买家”订单量
                        .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                        //“品牌新买家”销量
                        .unitsOrderedNewToBrand14d(Optional.ofNullable(re.getInt("units_ordered_new_to_brand14d")).orElse(0))
                        //可见展示次数
                        .viewImpressions(Optional.ofNullable(re.getInt("view_impressions")).orElse(0))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .countDate(re.getString("count_date"))
                        .type(re.getString("type"))
                        .newToBrandDetailPageViews(Optional.ofNullable(re.getInt("new_to_brand_detail_page_views")).orElse(0))
                        .addToCart(Optional.ofNullable(re.getInt("add_to_cart")).orElse(0))
                        .video5SecondViews(Optional.ofNullable(re.getInt("video5second_views")).orElse(0))
                        .videoFirstQuartileViews(Optional.ofNullable(re.getInt("video_first_quartile_views")).orElse(0))
                        .videoMidpointViews(Optional.ofNullable(re.getInt("video_Midpoint_Views")).orElse(0))
                        .videoThirdQuartileViews(Optional.ofNullable(re.getInt("video_third_quartile_views")).orElse(0))
                        .videoCompleteViews(Optional.ofNullable(re.getInt("video_complete_views")).orElse(0))
                        .videoUnmutes(Optional.ofNullable(re.getInt("video_unmutes")).orElse(0))
                        .brandedSearches(Optional.ofNullable(re.getInt("branded_searches14d")).orElse(0))
                        .detailPageViews(Optional.ofNullable(re.getInt("detail_page_view14d")).orElse(0))
                        .build();
                return dto;
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }


    @Override
    public List<AmazonAdCampaignAllReport> getShopCost(Integer puid, List<Integer> shopIds, String reportDate, String type) {
        StringBuilder sql = new StringBuilder("SELECT shop_id,sum(`cost`) cost ")
                .append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(reportDate, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where `puid`=? and `count_date`=? and `is_summary`=1 ");

        List<Object> args = Lists.newArrayList(puid, reportDate);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        if(StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(),args.toArray(),getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdCampaignAllReport> getShopCampaignOrderByCostLimit(Integer puid, List<Integer> shopIds, String reportDate, String type) {
        StringBuilder sql = new StringBuilder("SELECT campaign_name , type, cost ")
            .append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(reportDate, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where `puid`=? and `count_date`=? and `is_summary`=1 and cost > 0");

        List<Object> args = Lists.newArrayList(puid, reportDate);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        if(StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }

        sql.append(" order by cost desc ");
        sql.append(" limit 3");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), args.toArray(), getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<SponsoredIndexCampaignData> listSponsoredIndex(Integer puid, List<Integer> shopId, List<String> campaignIdList, Set<String> fields, String startDate, String endState) {
        StringBuilder sql = new StringBuilder("SELECT campaign_id ");
        List<Object> args = Lists.newArrayList();
        Set<String> fieldKey = fields.stream().filter(SQL_MAP::containsKey).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(fieldKey)) {
            sql.append(",");
            sql.append(fieldKey.stream().map(SQL_MAP::get).collect(Collectors.joining(",")));
        }
        sql.append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN)));
//        sql.append(" t_amazon_ad_campaign_all_report ");
        sql.append(" where `puid`=? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopId)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopId, args));
        }
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            sql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, args));
        }
        sql.append( " and `is_summary`=1 and `count_date` >= ? and `count_date` <= ? ");
        args.add(startDate.contains("-") ? startDate.replaceAll("-","") : startDate);
        args.add(endState.contains("-") ? endState.replaceAll("-","") : endState);
        sql.append(" group by campaign_id ");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new ObjectMapper<>(SponsoredIndexCampaignData.class), args.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<SponsoredIndexCampaignData> listSponsoredIndexPortfolio(Integer puid, List<Integer> shopId, List<String> campaignIdList, Set<String> fields, String startDate, String endState) {
        if (CollectionUtils.isEmpty(campaignIdList)) {
            return Lists.newArrayList();
        }
        StringBuilder sql = new StringBuilder("SELECT campaign_id ");
        List<Object> args = Lists.newArrayList();
        Set<String> fieldKey = fields.stream().filter(SQL_MAP::containsKey).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(fieldKey)) {
            sql.append(",");
            sql.append(fieldKey.stream().map(SQL_MAP::get).collect(Collectors.joining(",")));
        }
        sql.append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN)));
//        sql.append(" t_amazon_ad_campaign_all_report ");
        sql.append(" where `puid`=? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopId)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopId, args));
        }
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            sql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, args));
        }
        sql.append( " and `is_summary`=1 and `count_date` >= ? and `count_date` <= ? ");
        args.add(startDate.contains("-") ? startDate.replaceAll("-","") : startDate);
        args.add(endState.contains("-") ? endState.replaceAll("-","") : endState);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new ObjectMapper<>(SponsoredIndexCampaignData.class), args.toArray());
        } finally {
            hintManager.close();
        }
    }

    private static final Map<String, String> SQL_MAP = Collections.unmodifiableMap(new HashMap<String, String>() {
        {
            put("costTotal", "sum(cost) `costTotal`");
            put("costAvg", "avg(cost) `costAvg`");
            put("adSalesTotal", "sum(if (type = 'sp', sales7d,sales14d)) adSalesTotal");
            put("adSalesAvg", "avg(if (type = 'sp', sales7d,sales14d)) adSalesAvg");
            put("adOrderNumTotal", "sum(if (type = 'sp' , conversions7d,conversions14d)) adOrderNumTotal");
            put("adOrderNumAvg", "avg(if (type = 'sp' , conversions7d,conversions14d)) adOrderNumAvg");
            put("clicksTotal", "sum(`clicks`) clicksTotal");
            put("clicksAvg", "avg(`clicks`) clicksAvg");
            put("impressionsTotal", "sum(`impressions`) impressionsTotal");
            put("impressionsAvg", "avg(`impressions`) impressionsAvg");
            put("adSalesNumTotal", "sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) adSalesNumTotal");
            put("adSalesNumAvg", "avg(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) adSalesNumAvg");
        }
    });

    @Override
    public List<ReportMonitorBo> getReportLevelMonitorBoList(List<ShopDTO> shopDTOS, AdTypeEnum adType, String startCountDate, String endCountDate) {
        String campaignTable = getTableNameByStartDate(DateUtil.strToDate(startCountDate, DateUtil.PATTERN_YYYYMMDD));
        Map<Integer, List<Integer>> puidShopIdsMap = shopDTOS.stream().collect(Collectors.groupingBy(ShopDTO::getPuid, Collectors.mapping(ShopDTO::getShopId, Collectors.toList())));
        List<ReportMonitorBo> resultBolist = new ArrayList<>();
        puidShopIdsMap.forEach((puid, shopIds) -> {
            if (CollectionUtils.isEmpty(shopIds)) {
                return;
            }
            StringBuilder sql = new StringBuilder();
            sql.append("select sum(cost) total_cost,sum(clicks) total_clicks,sum(impressions) total_impressions, puid, shop_id from ")
                .append(campaignTable)
                .append(" where puid = ? and placement='all' and type= ? and count_date between ? and ? ");
            List<Object> args = Lists.newArrayList(puid, adType.name(), startCountDate, endCountDate);
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args))
                .append(" group by shop_id ");
            HintManager hintManager = HintManager.getInstance();
            try {
                List<ReportMonitorBo> reportMonitorBoList = getJdbcTemplate(puid, hintManager).query(sql.toString(), args.toArray(), (row, i) -> ReportMonitorBo.builder()
                    .totalCost(row.getBigDecimal("total_cost"))
                    .totalClicks(row.getLong("total_clicks"))
                    .totalImpressions(row.getLong("total_impressions"))
                    .puid(row.getInt("puid"))
                    .shopId(row.getInt("shop_id"))
                    .build());
                resultBolist.addAll(reportMonitorBoList);
            } finally {
                hintManager.close();
            }
        });
        return resultBolist;

    }

    @Override
    public List<Long> listNoCurrencyData(Integer puid, Integer shopId, String marketplaceId, String countDay) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select id from t_amazon_ad_campaign_all_report ");
        sb.append(" where puid = ? and shop_id = ? and marketplace_id = ? and count_date = ? and is_summary=1 and (currency is null or currency = '' ) ");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);
        argsList.add(countDay.replaceAll("-", ""));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sb.toString(), argsList.toArray(), Long.class);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public void updateCurrencyByIds(Integer puid, List<Long> ids, String currency) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_campaign_all_report` set `currency` = ?  where ");
        argsList.add(currency);
        sql.append(SqlStringUtil.dealInListNotAnd("id", ids, argsList));
        sql.append(" and puid = ? ");
        argsList.add(puid);
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }
}
