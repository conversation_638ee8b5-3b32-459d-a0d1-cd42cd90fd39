package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IAdBaseDao;
import com.meiyunji.sponsored.service.cpc.po.AdTargetTask;

import java.util.Date;
import java.util.List;

public interface IAdTargetTaskDao extends IAdBaseDao<AdTargetTask> {
    /**
     * 分页查询指定日期后的任务列表
     *
     * @param puid
     * @param pageNo
     * @param pageSize
     * @param targetPageType
     * @param sourceAdCampaignId
     * @param date
     * @return
     */
    Page<AdTargetTask> getPageByPuidAfterTargetDate(int puid, int sourceShopId, int pageNo, int pageSize, int targetPageType, String sourceAdCampaignId, Date date, int uid);

    /**
     * 分页查询指定日期后的任务列表
     *
     * @param puid
     * @param taskIds
     * @return
     */
    List<AdTargetTask> getByPuidAndIds(int puid, List<Long> taskIds);

    /**
     * 查询指定日期前的任务列表
     *
     * @param date
     * @return
     */
    List<AdTargetTask> getListBeforeTargetDate(Date date);

    /**
     * 删除任务
     *
     * @param ids
     */
    void deleteByIds(List<Long> ids);

    /**
     * 查询未执行完成的任务列表
     *
     * @return
     */
    List<AdTargetTask> getUnfinishedListBeforeTargetDate(Date date);

    /**
     * 查询任务
     *
     * @param puid
     * @param taskId
     * @return
     */
    AdTargetTask getByPuid(int puid, long taskId);

    /**
     * 更新任务状态
     *
     * @param puid
     * @param taskId
     * @param status
     * @return
     */
    void updateStatus(int puid, long taskId, int status);
}
