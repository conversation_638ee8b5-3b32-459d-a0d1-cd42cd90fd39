package com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class IdentityGetParam {
    @NotBlank(message = "advertiserId不能为空")
    private String advertiserId;
    @NotBlank(message = "shopId不能为空")
    private Integer shopId;
    @NotBlank(message = "bcId不能为空")
    private String storeAuthorizedBcId;

    private Integer pageNo;

    private Integer pageSize;
}
