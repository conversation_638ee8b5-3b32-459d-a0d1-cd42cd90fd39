package com.meiyunji.sponsored.service.newDashboard.service.impl;

import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardInsufficientBudgetResponse;
import com.meiyunji.sponsored.service.account.bo.ShopAuthBo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDao;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdCampaignAllBo;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdOperationLogBO;
import com.meiyunji.sponsored.service.cpc.constants.AmazonAdOperationLogChangeTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AmazonAdOperationLogEntityTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdOperationLogService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.bo.AmazonAdCampaignAllReportBo;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdBudgetUsageDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdBudgetUsage;
import com.meiyunji.sponsored.service.enums.CurrencyType;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.missBudget.dao.IAmazonAdMissBudgetDao;
import com.meiyunji.sponsored.service.missBudget.entity.AmazonAdMissBudget;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardInsufficientBudgetDataExportDto;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardInsufficientBudgetService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.newDashboard.util.OrderByUtil;
import com.meiyunji.sponsored.service.newDashboard.util.PageUtils;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardInsufficientBudgetReqVo;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-04-15  10:46
 */
@Service
@Slf4j
public class DashboardInsufficientBudgetServiceImpl implements IDashboardInsufficientBudgetService {

    @Autowired
    private IOdsAmazonAdCampaignAllReportDao odsAmazonAdCampaignAllReportDao;

    @Autowired
    private IOdsAmazonAdBudgetUsageDao odsAmazonAdBudgetUsageDao;

    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;

    @Autowired
    private IAmazonAdMissBudgetDao amazonAdMissBudgetDao;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;

    @Autowired
    private IAmazonAdOperationLogService amazonAdOperationLogService;

    @Autowired
    @Lazy
    private IExcelService excelService;

    @Autowired
    private StringRedisService stringRedisService;

    private static final List<String> baseHeaderList = Arrays.asList(
            "campaignName", "shopName", "marketplaceName", "marketplaceTime",
            "budgetUsage", "displaySuggestedBudget", "displayBudget",
            "displayTotalSales", "displayAcos", "displayCost", "roas", "clicks", "impressions",
            "orderNum", "displayClickRate", "displayConversionRate", "saleNum", "displayCpc");

    @Override
    public DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page queryInsufficientBudgetData(DashboardInsufficientBudgetReqVo reqVo) {
        DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.Builder pageBuilder = DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.newBuilder();
        pageBuilder.setPageNo(reqVo.getPageNo());
        pageBuilder.setPageSize(reqVo.getPageSize());
        List<ShopAuthBo> shopAuths = shopAuthDao.getShopAuthBoByIds(reqVo.getPuid(), reqVo.getShopIdList());
        if (CollectionUtils.isEmpty(shopAuths)) {
            return null;
        }
        Map<Integer, ShopAuthBo> shopAuthMap = shopAuths.stream().collect(Collectors.toMap(ShopAuthBo::getId, Function.identity()));

        List<String> siteToday = null;
        if (Boolean.TRUE.equals(reqVo.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(reqVo.getMarketplaceIdList());
        }

        //获取页面筛选条件内指定前n广告活动campaignId
        List<AmazonAdCampaignAllReportBo> campaignInfoList = odsAmazonAdCampaignAllReportDao.campaignIdListByInsufficientBudget(reqVo.getPuid(), reqVo.getShopIdList(), reqVo.getMarketplaceIdList(),
                reqVo.getStartDate(), reqVo.getEndDate(), CurrencyType.USD.code(), reqVo.getOrderBy(), reqVo.getLimit(),
                siteToday, reqVo.getSiteToday(), reqVo.getPortfolioIds(), reqVo.getCampaignIds(), reqVo.getNoZero());
        if (CollectionUtils.isEmpty(campaignInfoList)) {
            return pageBuilder.build();
        }
        //查询报告数据
        Map<String, AmazonAdCampaignAllReportBo> reportMap = campaignInfoList.parallelStream().collect(Collectors.toMap(AmazonAdCampaignAllReportBo::getCampaignId, v1 -> v1, (old, current) -> current));
        List<String> campaignIdList = new ArrayList<>(reportMap.keySet());
        //根据今日预算剩余过滤、排序、分页
        Integer limit = reqVo.getLimit();
        if (limit == null) {
            limit = 200;
        } else if (limit > 200) {
            limit = 200;
        }
        Page<String> idPage = odsAmazonAdBudgetUsageDao.getInsufficientBudgetPage(reqVo.getPuid(), reqVo.getShopIdList(), reqVo.getMarketplaceIdList(), reqVo.getBudgetLeft(),
                campaignIdList, 1, limit);
        pageBuilder.setTotalPage(idPage.getTotalPage());
        pageBuilder.setTotalSize(idPage.getTotalSize());
        if (CollectionUtils.isEmpty(idPage.getRows())) {
            return pageBuilder.build();
        }

        //查询基础数据
        Map<String, AmazonAdCampaignAllBo> campaignMap = amazonAdCampaignAllDao.listBoByCampaignIds(reqVo.getPuid(), reqVo.getShopIdList(), campaignIdList)
                .stream().collect(Collectors.toMap(AmazonAdCampaignAllBo::getCampaignId, Function.identity(), (e1, e2) -> e2));

        //查询建议预算
        Map<String, AmazonAdMissBudget> suggestedBudgetMap = amazonAdMissBudgetDao.listByCampaignIdsAndShopIds(reqVo.getPuid(), reqVo.getShopIdList(), campaignIdList)
                .stream().collect(Collectors.toMap(AmazonAdMissBudget::getCampaignId, Function.identity()));
        //查询预算剩余
        Map<String, List<OdsAmazonAdBudgetUsage>> budgetUsageMap = odsAmazonAdBudgetUsageDao.listByDays(reqVo.getPuid(), reqVo.getShopIdList(), campaignIdList, 3)
                .stream().collect(Collectors.groupingBy(OdsAmazonAdBudgetUsage::getBudgetScopeId));
        //获取每个站点当前时间
        Map<String, Date> marketplaceDateMap = this.getMarketplaceDateMap(campaignMap.values().stream().map(AmazonAdCampaignAllBo::getMarketplaceId).collect(Collectors.toList()));
        //获取自动化规则受控对象
        Set<String> existAutoCampaignIdSet = advertiseAutoRuleStatusDao.getListByItemIds(reqVo.getPuid(), reqVo.getShopIdList(), campaignIdList).stream().map(AdvertiseAutoRuleStatus::getItemId).collect(Collectors.toSet());
        //获取24小时前竞价调整日志
        Date lastDate = DateUtil.addDay(new Date(), -1);
        Map<String, AmazonAdOperationLogBO> amazonAdOperationLogMap = amazonAdOperationLogService.listGroupByTypeAndIdentifyIdAndShopIdList(reqVo.getPuid(), reqVo.getShopIdList(), Constants.SP,
                AmazonAdOperationLogEntityTypeEnum.CAMPAIGN.getCode(), AmazonAdOperationLogChangeTypeEnum.CAMPAIGN_BUDGET_AMOUNT.getCode(), campaignIdList, lastDate);

        List<DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.InsufficientBudgetPageVo> voList = new ArrayList<>();
        DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.InsufficientBudgetPageVo.Builder vo;

        for (String campaignId : idPage.getRows()) {
            vo = DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.InsufficientBudgetPageVo.newBuilder();
            vo.setCampaignId(campaignId);
            this.fillCampaignBaseData(vo, campaignMap, shopAuthMap, existAutoCampaignIdSet);
            this.fillReportData(vo, reportMap);
            this.fillBudgetData(vo, suggestedBudgetMap, budgetUsageMap, marketplaceDateMap);
            this.setCampaignBudgetLog(vo, amazonAdOperationLogMap);
            voList.add(vo.build());
        }
        //是否需要排序
        if (StringUtils.isNotBlank(reqVo.getListOrderField()) || StringUtils.isNotBlank(reqVo.getListOrderType())) {
            OrderByUtil.sortedByOrderField(voList, reqVo.getListOrderField(), reqVo.getListOrderType(), "campaignId");
        }
        DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page insufficientBudgetPageInfo = PageUtils.getInsufficientBudgetPageInfo(voList, reqVo.getPageSize(), reqVo.getPageNo());
        pageBuilder.setPageNo(insufficientBudgetPageInfo.getPageNo());
        pageBuilder.setPageSize(insufficientBudgetPageInfo.getPageSize());
        pageBuilder.setTotalPage(insufficientBudgetPageInfo.getTotalPage());
        pageBuilder.addAllRows(insufficientBudgetPageInfo.getRowsList());
        return pageBuilder.build();
    }

    @Override
    public List<String> exportInsufficientBudgetData(DashboardInsufficientBudgetReqVo reqVo) {
        //导出只需设置分页数为前n条即可导出全部数据
        reqVo.setPageNo(1);
        reqVo.setPageSize(reqVo.getLimit());
        //获取数据
        DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page page = this.queryInsufficientBudgetData(reqVo);
        List<DashboardInsufficientBudgetDataExportDto> dtoList = new ArrayList<>();
        DashboardInsufficientBudgetDataExportDto dto;
        for (DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.InsufficientBudgetPageVo vo : page.getRowsList()) {
            dto = new DashboardInsufficientBudgetDataExportDto();
            String currency = AmznEndpoint.getByMarketplaceId(vo.getMarketplaceId()).getCurrencyCode().value();
            dto.setCampaignName(vo.getCampaignName());
            dto.setShopName(vo.getShopName());
            dto.setMarketplaceName(vo.getMarketplaceName());
            dto.setMarketplaceTime(vo.getMarketplaceTime());
            dto.setBudgetUsage(CalculateUtil.formatPercent(MathUtil.subtract(new BigDecimal("100"), BigDecimal.valueOf(vo.getBudgetUsage().getPercent())).divide(BigDecimal.valueOf(100), 5, RoundingMode.HALF_UP)));
            if (StringUtils.isNotBlank(vo.getSuggestedBudget())) {
                dto.setDisplaySuggestedBudget(currency + vo.getSuggestedBudget());
            }
            dto.setDisplayBudget(currency + vo.getBudget());
            dto.setDisplayTotalSales(currency + vo.getTotalSales());
            dto.setDisplayAcos(vo.getAcos());
            dto.setDisplayCost(currency + vo.getCost());
            dto.setRoas(new BigDecimal(vo.getRoas()));
            dto.setClicks(Integer.valueOf(vo.getClicks()));
            dto.setImpressions(Long.valueOf(vo.getImpressions()));
            dto.setOrderNum(Integer.valueOf(vo.getOrderNum()));
            dto.setDisplayClickRate(vo.getClickRate());
            dto.setDisplayConversionRate(vo.getConversionRate());
            dto.setSaleNum(Integer.valueOf(vo.getSaleNum()));
            dto.setDisplayCpc(currency + vo.getCpc());
            dtoList.add(dto);
        }
        List<String> list = new ArrayList<>();
        list.add(excelService.easyExcelHandlerDownload(reqVo.getPuid(), dtoList, "预算不足", DashboardInsufficientBudgetDataExportDto.class, baseHeaderList, true));
        return list;
    }

    /**
     * 获取每个站点当前时间map
     * @param marketplaceIdList
     * @return
     */
    private Map<String, Date> getMarketplaceDateMap(List<String> marketplaceIdList) {
        Map<String, Date> marketplaceDateMap = new HashMap<>();
        String today = Instant.now().toString();
        for (String marketplaceId : marketplaceIdList) {
            Date date = LocalDateTimeUtil.utcToSiteTimeDate(today, marketplaceId);
            if (date != null) {
                marketplaceDateMap.put(marketplaceId, date);
            }
        }
        return marketplaceDateMap;
    }

    /**
     * 填充广告活动基础数据
     * @param vo
     * @param campaignMap
     * @param shopAuthMap
     */
    private void fillCampaignBaseData(DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.InsufficientBudgetPageVo.Builder vo,
                                      Map<String, AmazonAdCampaignAllBo> campaignMap, Map<Integer, ShopAuthBo> shopAuthMap, Set<String> existAutoCampaignIdSet) {
        if (campaignMap.containsKey(vo.getCampaignId())) {
            AmazonAdCampaignAllBo campaign = campaignMap.get(vo.getCampaignId());
            vo.setId(campaign.getId());
            vo.setType(campaign.getType());
            vo.setCampaignName(campaign.getName());
            vo.setShopId(campaign.getShopId());
            vo.setIsArchived("archived".equalsIgnoreCase(campaign.getState()));
            vo.setShopName(shopAuthMap.containsKey(campaign.getShopId()) ? shopAuthMap.get(campaign.getShopId()).getName() : "");
            vo.setMarketplaceId(campaign.getMarketplaceId());
            AmznEndpoint byMarketplaceId = AmznEndpoint.getByMarketplaceId(campaign.getMarketplaceId());
            if (byMarketplaceId != null) {
                vo.setMarketplaceName(byMarketplaceId.getMarketplaceCN());
            }
            vo.setBudget(CalculateUtil.formatDecimal(campaign.getBudget()));
            vo.setIsApplyAuto(existAutoCampaignIdSet.contains(campaign.getCampaignId()));
            if (campaign.getBudgetType() != null) {
                vo.setBudgetType(campaign.getBudgetType());
            }
        }
    }

    /**
     * 填充广告活动报告数据
     * @param vo
     * @param reportMap
     */
    private void fillReportData(DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.InsufficientBudgetPageVo.Builder vo,
                                Map<String, AmazonAdCampaignAllReportBo> reportMap) {
        if (reportMap.containsKey(vo.getCampaignId())) {
            AmazonAdCampaignAllReportBo report = reportMap.get(vo.getCampaignId());
            CalculateAdDataUtil.calAdCalDataReflex(report);
            vo.setTotalSales(CalculateUtil.formatDecimal(report.getTotalSales()));
            vo.setAcos(CalculateUtil.formatPercent(report.getAcos()));
            vo.setCost(CalculateUtil.formatDecimal(report.getCost()));
            vo.setRoas(CalculateUtil.formatDecimal(report.getRoas()));
            vo.setClicks(report.getClicks().toString());
            vo.setImpressions(report.getImpressions().toString());
            vo.setOrderNum(report.getOrderNum().toString());
            vo.setClickRate(CalculateUtil.formatPercent(report.getClickRate()));
            vo.setConversionRate(CalculateUtil.formatPercent(report.getConversionRate()));
            vo.setSaleNum(report.getSaleNum().toString());
            vo.setCpc(CalculateUtil.formatDecimal(report.getCpc()));
        }
    }

    /**
     * 填充广告活动建议预算、预算详情数据
     * @param vo
     * @param suggestedBudgetMap
     * @param budgetUsageMap
     */
    private void fillBudgetData(DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.InsufficientBudgetPageVo.Builder vo,
                                Map<String, AmazonAdMissBudget> suggestedBudgetMap, Map<String, List<OdsAmazonAdBudgetUsage>> budgetUsageMap,
                                Map<String, Date> marketplaceDateMap) {
        if (suggestedBudgetMap.containsKey(vo.getCampaignId())) {
            AmazonAdMissBudget amazonAdMissBudget = suggestedBudgetMap.get(vo.getCampaignId());
            if (amazonAdMissBudget != null && amazonAdMissBudget.getSuggestedBudget() != null) {
                vo.setSuggestedBudget(amazonAdMissBudget.getSuggestedBudget().toString());
            }
        }
        DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.BudgetUsage todayBudgetUsage = null;
        List<DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.BudgetUsage> budgetUsageList = new ArrayList<>();
        Map<String, List<OdsAmazonAdBudgetUsage>> budgetUsageDataListMap = new HashMap<>();

        List<OdsAmazonAdBudgetUsage> amazonAdBudgetUsages = budgetUsageMap.get(vo.getCampaignId());
        if (CollectionUtils.isNotEmpty(amazonAdBudgetUsages)) {
            budgetUsageDataListMap = amazonAdBudgetUsages.stream().collect(Collectors.groupingBy(e -> DateUtil.dateToStrWithTime(e.getUsageUpdatedSiteDate(), DateUtil.PATTERN)));
        }
        Date today = marketplaceDateMap.getOrDefault(vo.getMarketplaceId(), new Date());
        for (int x = 2; x >= 0 ; x--) {
            String dayType = "";
            if (x == 2) {
                dayType = "theDayBefore";
            }
            if (x == 1) {
                dayType = "yesterday";
            }
            if (x == 0) {
                dayType = "today";
            }
            Date date = DateUtil.addDay(today, -x);
            DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.BudgetUsage.Builder buil =
                    this.builderBudgetUsage(budgetUsageDataListMap.get(DateUtil.dateToStrWithTime(date, DateUtil.PATTERN)));
            buil.setDayType(dayType);
            buil.setDate(DateUtil.dateToStrWithTime(date));
            DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.BudgetUsage budgetUsage = buil.build();
            if (x == 0) {
                todayBudgetUsage = budgetUsage;
            }
            budgetUsageList.add(budgetUsage);
        }
        if (todayBudgetUsage != null){
            //预算剩余
            BigDecimal currentBudget = BigDecimal.valueOf(todayBudgetUsage.getCurrentBudget());
            BigDecimal percent = BigDecimal.valueOf(todayBudgetUsage.getPercent());
            BigDecimal surplus =new BigDecimal(100).subtract(percent);
            if(surplus.compareTo(BigDecimal.ZERO)< 0){
                surplus = new BigDecimal(0);
            }
            vo.setBudgetSurplus(currentBudget.multiply(surplus).
                    divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString());
            vo.setBudgetUsage(todayBudgetUsage);
        }
        vo.addAllBudgetUsages(budgetUsageList);
        vo.setMarketplaceTime(DateUtil.dateToStrWithTime(today, DateUtil.PATTERN_HH_MM));
    }

    /**
     * 预算使用详情转化vo
     * @param amazonAdBudgetUsages
     * @return
     */
    private DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.BudgetUsage.Builder builderBudgetUsage(List<OdsAmazonAdBudgetUsage> amazonAdBudgetUsages){
        DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.BudgetUsage.Builder budgetUsageBuilder =
                DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.BudgetUsage.newBuilder();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(amazonAdBudgetUsages)) {
            List<OdsAmazonAdBudgetUsage> sortedBudgetUsageList = amazonAdBudgetUsages.stream()
                    .sorted(Comparator.comparing(OdsAmazonAdBudgetUsage::getUsageUpdatedSiteTime))
                    .collect(Collectors.toList());
            //最后更新记录
            OdsAmazonAdBudgetUsage amazonAdBudgetUsage = sortedBudgetUsageList.get(sortedBudgetUsageList.size() - 1);
            budgetUsageBuilder.setIsNoData(false);
            Optional.ofNullable(amazonAdBudgetUsage.getBudget()).map(BigDecimal::doubleValue).ifPresent(budgetUsageBuilder::setCurrentBudget);
            budgetUsageBuilder.setLastUpdateAt(DateUtil.dateToStrWithTime(amazonAdBudgetUsage.getUsageUpdatedSiteTime()));
            Optional.ofNullable(amazonAdBudgetUsage.getBudgetUsagePercentage()).map(BigDecimal::doubleValue).ifPresent(budgetUsageBuilder::setPercent);
            //变化记录
            List<DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.BudgetUsage.Item> budgetUsageItems =
                    sortedBudgetUsageList.stream().map(o -> {
                        DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.BudgetUsage.Item.Builder itemBuilder =
                                DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.BudgetUsage.Item.newBuilder();
                        Optional.ofNullable(o.getBudget()).map(BigDecimal::doubleValue).ifPresent(itemBuilder::setCurrentBudget);
                        Optional.ofNullable(o.getBudgetUsagePercentage()).map(BigDecimal::doubleValue).ifPresent(itemBuilder::setPercent);
                        itemBuilder.setUpdateAt(DateUtil.dateToStrWithTime(o.getUsageUpdatedSiteTime()));
                        return itemBuilder.build();
                    }).collect(Collectors.toList());
            budgetUsageBuilder.addAllItem(budgetUsageItems);

        } else {
            budgetUsageBuilder.setIsNoData(true);
        }
        return budgetUsageBuilder;
    }

    /**
     * 广告活动列表页设置竞价、预算日志
     * @param voBuilder
     * @param amazonAdOperationLogMap
     */
    private void setCampaignBudgetLog(DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page.InsufficientBudgetPageVo.Builder voBuilder, Map<String, AmazonAdOperationLogBO> amazonAdOperationLogMap) {
        //预算日志
        String logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.CAMPAIGN_BUDGET_AMOUNT.getCode(), voBuilder.getCampaignId());
        if (amazonAdOperationLogMap.containsKey(logKey)) {
            AmazonAdOperationLogBO logBO = amazonAdOperationLogMap.get(logKey);
            if (StringUtils.isNotBlank(logBO.getContent())) {
                com.meiyunji.sponsored.service.log.po.OperationContent operationContent = JSONUtil.jsonToObject(logBO.getContent(), com.meiyunji.sponsored.service.log.po.OperationContent.class);
                if (operationContent != null) {
                    DashboardInsufficientBudgetResponse.DataLog.Builder budgetLog = DashboardInsufficientBudgetResponse.DataLog.newBuilder();
                    budgetLog.setCount(logBO.getCount());
                    if (StringUtils.isNotBlank(operationContent.getPreviousValue())) {
                        budgetLog.setPreviousValue(operationContent.getPreviousValue());
                    }
                    if (StringUtils.isNotBlank(operationContent.getNewValue())) {
                        budgetLog.setNewValue(operationContent.getNewValue());
                    }
                    budgetLog.setSiteOperationTime(logBO.getSiteOperationTime());
                    voBuilder.setBudgetLog(budgetLog);
                }
            }
            voBuilder.setIsUpdateBudget(true);
        } else {
            voBuilder.setIsUpdateBudget(stringRedisService.get(logKey) != null);
        }
    }
}
