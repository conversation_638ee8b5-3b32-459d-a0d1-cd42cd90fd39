package com.meiyunji.sponsored.service.multiple.common.dto;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import lombok.*;

import java.math.BigDecimal;

/**
 * 报告查询基础数据
 *
 * @author: zzh
 * @create: 2025-04-25 10:05
 */
@Getter
@Setter
public class ReportDorisBase {

    @DbColumn(value = "costDoris")
    private BigDecimal costDoris = BigDecimal.ZERO;

    @DbColumn(value = "totalSalesDoris")
    private BigDecimal totalSalesDoris = BigDecimal.ZERO;

    @DbColumn(value = "adSalesDoris")
    private BigDecimal adSalesDoris = BigDecimal.ZERO;

    @DbColumn(value = "impressionsDoris")
    private Long impressionsDoris = 0L;

    @DbColumn(value = "orderNumDoris")
    private Long orderNumDoris = 0L;

    @DbColumn(value = "clicksDoris")
    private Long clicksDoris = 0L;

    @DbColumn(value = "adOrderNumDoris")
    private Long adOrderNumDoris = 0L;

    @DbColumn(value = "saleNumDoris")
    private Long saleNumDoris = 0L;

    @DbColumn(value = "adSaleNumDoris")
    private Long adSaleNumDoris = 0L;

    @DbColumn(value = "viewImpressionsDoris")
    private Long viewImpressionsDoris = 0L;

    @DbColumn(value = "ordersNewToBrand14dDoris")
    private Long ordersNewToBrand14dDoris = 0L;
    /**
     * video 不支持  The total sales of new-to-brand orders. Not available for search term report.
     */
    @DbColumn(value = "salesNewToBrand14dDoris")
    private BigDecimal salesNewToBrand14dDoris = BigDecimal.ZERO;

    @DbColumn(value = "unitsOrderedNewToBrand14dDoris")
    private Long unitsOrderedNewToBrand14dDoris = 0L;

    @DbColumn(value = "newToBrandDetailPageViewsDoris")
    private Long newToBrandDetailPageViewsDoris = 0L;

    @DbColumn(value = "addToCartDoris")
    private Long addToCartDoris = 0L;

    @DbColumn(value = "videoFirstQuartileViewsDoris")
    private Long videoFirstQuartileViewsDoris = 0L;

    @DbColumn(value = "video5secondViewsDoris")
    private Long video5secondViewsDoris = 0L;

    @DbColumn(value = "videoMidpointViewsDoris")
    private Long videoMidpointViewsDoris = 0L;

    @DbColumn(value = "videoThirdQuartileViewsDoris")
    private Long videoThirdQuartileViewsDoris = 0L;

    @DbColumn(value = "videoCompleteViewsDoris")
    private Long videoCompleteViewsDoris = 0L;

    @DbColumn(value = "videoUnmutesDoris")
    private Long videoUnmutesDoris = 0L;

    @DbColumn(value = "brandedSearches14dDoris")
    private Long brandedSearches14dDoris = 0L;

    @DbColumn(value = "detailPageView14dDoris")
    private Long detailPageView14dDoris = 0L;

    @DbColumn(value = "minTopIsDoris")
    private BigDecimal minTopIsDoris = BigDecimal.ZERO;

    @DbColumn(value = "maxTopIsDoris")
    private BigDecimal maxTopIsDoris = BigDecimal.ZERO;

    @DbColumn(value = "cumulativeReachDoris")
    private Long cumulativeReachDoris = 0L;

    @DbColumn(value = "impressionsFrequencyAverageDoris")
    private BigDecimal impressionsFrequencyAverageDoris = BigDecimal.ZERO;
}
