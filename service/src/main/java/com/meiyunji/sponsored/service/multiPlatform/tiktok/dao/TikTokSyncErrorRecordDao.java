package com.meiyunji.sponsored.service.multiPlatform.tiktok.dao;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokSyncErrorRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class TikTokSyncErrorRecordDao extends AdBaseDaoImpl<TikTokSyncErrorRecord> {

    public void add(Integer puid, String taskType, String advertiserId, String errorMessage, String requestParams) {
        String sql = "INSERT INTO t_tiktok_sync_error_record (puid, task_type, advertiser_id, error_message, request_params) " +
                "VALUES (?, ?, ?, ?, ?)";
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(taskType);
        args.add(advertiserId);
        args.add(errorMessage);
        args.add(requestParams);
        getJdbcTemplate().update(sql, args.toArray());
    }

    public List<TikTokSyncErrorRecord> getInitErrorRecords() {
        String sql = "SELECT * FROM t_tiktok_sync_error_record " +
                " WHERE task_type in ('initGmvMaxCampaignInfo', 'initGmvMaxCampaign', 'initGmvMaxCampaignReport') " +
                " and status = 0 " +
                " and retry_count < 5 " +
                " order by id limit 100";
        return getJdbcTemplate().query(sql, getRowMapper());
    }

    public List<TikTokSyncErrorRecord> getErrorRecords() {
        String sql = "SELECT * FROM t_tiktok_sync_error_record " +
                " WHERE task_type not in ('initGmvMaxCampaignInfo', 'initGmvMaxCampaign', 'initGmvMaxCampaignReport') " +
                " and status = 0 " +
                " and retry_count < 5 " +
                " order by id limit 100";
        return getJdbcTemplate().query(sql, getRowMapper());
    }

    public void updateStatus(Long id, Integer status) {
        String sql = "UPDATE t_tiktok_sync_error_record SET status = ? WHERE id = ?";
        getJdbcTemplate().update(sql, status, id);
    }


    public void incrementRetryCount(Long id) {
        String sql = "UPDATE t_tiktok_sync_error_record SET retry_count = retry_count + 1 WHERE id = ?";
        getJdbcTemplate().update(sql, id);
    }
}
