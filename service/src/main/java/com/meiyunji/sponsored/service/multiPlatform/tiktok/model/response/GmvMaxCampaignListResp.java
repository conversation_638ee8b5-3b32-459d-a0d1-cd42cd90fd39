package com.meiyunji.sponsored.service.multiPlatform.tiktok.model.response;

import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.GmvMaxCampaignReportInfo;
import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
public class GmvMaxCampaignListResp {

    private List<GmvMaxCampaignReportInfo> rows;
    private Integer pageNo;
    private Integer pageSize;
    private Integer totalPage;
    private Integer totalSize;

    public static GmvMaxCampaignListResp empty(Integer pageNum, Integer pageSize) {
        GmvMaxCampaignListResp resp = new GmvMaxCampaignListResp();
        resp.setRows(Collections.emptyList());
        resp.setPageNo(pageNum);
        resp.setPageSize(pageSize);
        resp.setTotalPage(0);
        resp.setTotalSize(0);
        return resp;
    }

}
