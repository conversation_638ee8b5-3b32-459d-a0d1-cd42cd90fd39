package com.meiyunji.sponsored.service.multiPlatform.walmart.util;

import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description
 */
public class WalmartAdDaoUtil {

    /**
     * 报告的相同字段
     */
    public static final String REPORT_FIELDS = "`adCost`,`adSale`,`impressions`,`clicks`,`adOrderNum`,`adSaleNum`";

    public static final String REPORT_FIELDS_SUM = "sum(`adCost`) adCost,sum(`adSale`) adSale,sum(`impressions`) impressions," +
            "sum(`clicks`) clicks,sum(`adOrderNum`) adOrderNum,sum(`adSaleNum`) adSaleNum";

    public static final String CAMPAIGN_REPORT_FIELDS = "`ad_spend`,`sales`,`num_ads_shown`,`num_ads_clicks`,`order_quantity`,`sale_quantity`";

    public static final String CAMPAIGN_REPORT_FIELDS_SUM = "sum(`ad_spend`) ad_spend,sum(`sales`) sales,sum(`num_ads_shown`) num_ads_shown," +
            "sum(`num_ads_clicks`) num_ads_clicks,sum(`order_quantity`) order_quantity,sum(`sale_quantity`) sale_quantity";

    private static final Map<String, String> aliasMap;

    static {
        // 按时间区间统计报表之后才能计算的字段，key 为字段名，value 计算规则（用作排序）
        aliasMap = Maps.newHashMap();
        aliasMap.put("adCostPerClick", "adCost/clicks");
        aliasMap.put("acos", "adCost/adSale");
        aliasMap.put("ctr", "clicks/impressions");
        aliasMap.put("cvr", "adOrderNum/clicks");
    }

    private static final Map<String, String> searchAliasMap;

    static {
        // 按时间区间统计报表之后才能计算的字段，key 为字段名，value 计算规则（用作排序）
        searchAliasMap = Maps.newHashMap();
        searchAliasMap.put("adCostPerClick", "sum(`ad_spend`) / sum(`num_ads_clicks`)");
        searchAliasMap.put("acos", "sum(`ad_spend`) / sum(`attributed_sales_%sdays`)");
        searchAliasMap.put("ctr", "sum(`num_ads_clicks`) / sum(`num_ads_shown`)");
        searchAliasMap.put("cvr", "sum(`attributed_orders_%sdays`) / sum(`num_ads_clicks`)");
    }

    public static void setLeftJoinConditions(Map<String, Object> queryParams, StringBuilder leftJoinSql, List<Object> argsList) {
        Object shopIds = MapUtils.getObject(queryParams, "shopId");
        if (Objects.nonNull(shopIds)) {
            leftJoinSql.append(" and shop_id in (").append(Joiner.on(",").join((List<Long>) shopIds)).append(") ");
        }
        if (queryParams.containsKey("campaignId")) {
            leftJoinSql.append(" and campaign_id = ?");
            argsList.add(queryParams.get("campaignId"));
        }
        if (queryParams.containsKey("groupId")) {
            leftJoinSql.append(" and ad_group_id = ?");
            argsList.add(queryParams.get("groupId"));
        }
        if (queryParams.containsKey("itemId")) {
            leftJoinSql.append(" and item_id = ?");
            argsList.add(queryParams.get("itemId"));
        }
        if (queryParams.containsKey("keywordId")) {
            leftJoinSql.append(" and keyword_id = ?");
            argsList.add(queryParams.get("keywordId"));
        }

        if (queryParams.containsKey("startDate")) {
            leftJoinSql.append(" and report_date >= ? ");
            argsList.add(queryParams.get("startDate"));
        }
        if (queryParams.containsKey("endDate")) {
            leftJoinSql.append(" and report_date <= ? ");
            argsList.add(queryParams.get("endDate"));
        }
        Object campaignIds = MapUtils.getObject(queryParams, "campaignIds");
        if (Objects.nonNull(campaignIds)) {
            leftJoinSql.append(" and t1.campaign_id in (").append(Joiner.on(",").join((List<Long>) campaignIds)).append(") ");
        }
    }
    public static String getOrderByState(String orderField, Integer isDesc) {
        return getOrderByState(orderField, isDesc, "");
    }
    public static String getOrderByState(String orderField, Integer isDesc, String table) {
        if (table == null) {
            table = "";
        }
        StringBuilder orderBy = new StringBuilder();
        if (StringUtils.isNotEmpty(orderField) && isDesc != null) {
            orderBy.append(" order by ").append(table);
            orderBy.append(aliasMap.getOrDefault(orderField, orderField));
            orderBy.append(isDesc % 2 == 0 ? " desc " : "");
            orderBy.append(" , id desc");
        } else {
            orderBy.append(" order by id desc");
        }
        return orderBy.toString();
    }

    public static String getOrderBySql(String orderField, String orderType, String defaultOrderField) {
        StringBuilder orderBy = new StringBuilder();
        orderBy.append(" order by ");
        if (StringUtils.isNotEmpty(orderField) && StringUtils.isNotBlank(orderType)) {
            orderBy.append(aliasMap.getOrDefault(orderField, orderField)).append(" ")
                    .append(OrderTypeEnum.desc.getType().equals(orderType) ? OrderTypeEnum.desc : "")
                    .append(" ,");
        }
        orderBy.append(defaultOrderField).append(" ").append(OrderTypeEnum.desc);
        return orderBy.toString();
    }

    public static String reportFieldsSum(Integer day) {
        if (day == null || day > 30) {
            day = 3;
        }
        return String.format("sum(`ad_spend`) adCost,sum(`attributed_sales_%sdays`) adSale,sum(`num_ads_shown`) impressions," +
                "sum(`num_ads_clicks`) clicks,sum(`attributed_orders_%sdays`) adOrderNum,sum(`attributed_units_%sdays`) adSaleNum", day, day, day);
    }

    public static String searchImpressionReportFieldsSum(Integer day) {
        if (day == null || day > 30) {
            day = 3;
        }
        return String.format("sum(`ad_spend`) adCost,sum(`num_ads_shown`) impressions,sum(`num_ads_clicks`) clicks," +
                "sum(`attributed_orders_%sdays`) adOrderNum,sum(`attributed_sales_%sdays`) adSale,sum(`attributed_units_%sdays`) adSaleNum," +
                "sum(`advertised_sku_sales_%sdays`) adSelfSale,sum(`other_sku_sales_%sdays`) adOtherSales," +
                "sum(`advertised_sku_units_%sdays`) adSelfSaleNum,sum(`other_sku_units_%sdays`) adOtherSaleNum", day, day, day, day, day, day, day);
    }

    public static String getSearchImpressionOrderBySql(String orderField, String orderType, String defaultOrderField, Integer day) {
        StringBuilder orderBy = new StringBuilder();
        orderBy.append(" order by ");
        if (StringUtils.isNotEmpty(orderField) && StringUtils.isNotBlank(orderType)) {
            String order = searchAliasMap.getOrDefault(orderField, orderField);
            if (order.contains("%s")) {
                order = order.replaceAll("%s", day.toString());
            }
            orderBy.append(order).append(" ")
                    .append(OrderTypeEnum.desc.getType().equals(orderType) ? OrderTypeEnum.desc : "")
                    .append(" ,");
        }
        orderBy.append(defaultOrderField).append(" ").append(OrderTypeEnum.desc);
        return orderBy.toString();
    }

    public static String campaignReportFieldsSum(Integer day) {
        if (day == null || day > 30) {
            day = 3;
        }
        return String.format("sum(`ad_spend`) ad_spend,sum(`attributed_sales_%sdays`) sales,sum(`num_ads_shown`) num_ads_shown," +
                "sum(`num_ads_clicks`) num_ads_clicks,sum(`attributed_orders_%sdays`) order_quantity,sum(`attributed_units_%sdays`) sale_quantity", day, day, day);
    }
}
