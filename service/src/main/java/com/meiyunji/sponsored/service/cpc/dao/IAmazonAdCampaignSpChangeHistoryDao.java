package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaign;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignSpChangeHistory;
import com.meiyunji.sponsored.service.cpc.vo.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * AmazonAdCampaign
 * <AUTHOR>
 */
public interface IAmazonAdCampaignSpChangeHistoryDao extends IBaseShardingDao<AmazonAdCampaignSpChangeHistory> {

    List<AmazonAdCampaignSpChangeHistory> getAllHistoryByCampaignId(AdCampaignChangeHistoryParam param);

    List<AmazonAdCampaignSpChangeHistory> getAllHistoryByCampaignIdHour(AdCampaignChangeHistoryParam param);

    Page<AmazonAdCampaignSpChangeHistory> pageList(AdCampaignChangeHistoryParam param);

    void insertOnDuplicateKeyUpdate(Integer puid, List<AmazonAdCampaignChangeHistoryVo> amazonAdCampaignList);

    AmazonAdCampaignSpChangeHistory getAllHistoryByCampaignIdMaxTimestamp(Integer puid, Integer shopId, String campaignId);


    List<AmazonAdCampaignSpChangeHistory> getMaxTimeOutOfBudgetByCampaignIds(Integer puid, Integer shopId, List<String> campaignIds);

    AmazonAdCampaignSpChangeHistory getAllHistoryByCampaignIdAndBudgetAmount(Integer puid, Integer shopId, String campaignId, Long time);
}