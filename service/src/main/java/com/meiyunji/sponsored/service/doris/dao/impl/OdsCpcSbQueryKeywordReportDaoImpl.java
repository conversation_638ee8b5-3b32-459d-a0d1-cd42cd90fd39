package com.meiyunji.sponsored.service.doris.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.bo.SearchTermAggregateBO;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdQueryStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dto.AdProductReportSearchTermsViewDto;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.dao.IOdsCpcSbQueryKeywordReportDao;
import com.meiyunji.sponsored.service.doris.po.OdsCpcSbQueryKeywordReport;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.po.CpcSbQueryKeywordReport;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.cpc.vo.CpcQueryWordDto;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.dao.helper.SearchQueryTagSqlHelper;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdQueryWordMatrixTopDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.util.Constant;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.SearchTermsViewParam;
import com.meiyunji.sponsored.service.wordFrequency.enums.WordRoot;
import com.meiyunji.sponsored.service.wordFrequency.qo.QueryWordTopQo;
import com.meiyunji.sponsored.service.wordFrequency.vo.WordRootTopVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * amazon关键词搜索词报告表(OdsCpcSbQueryKeywordReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:22
 */
@Repository
public class OdsCpcSbQueryKeywordReportDaoImpl extends DorisBaseDaoImpl<OdsCpcSbQueryKeywordReport> implements IOdsCpcSbQueryKeywordReportDao {
    private final Integer SEATCH_TERMS_VIEW_LIMIT = 100000;

    @Override
    public String buildQueryAdQueryWordChartsSql(Integer puid, List<String> marketplaceIdList, List<Integer> shopIdList, String currency, String startDate, String endDate, List<Object> argsList) {
        StringBuilder sql = new StringBuilder();
        sql.append("select query queryWord, match_type matchType, ifnull(sum(r.cost * c.rate), 0) cost, ifnull(sum(sales14d * c.rate), 0) totalSales, ");
        sql.append("ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ");
        sql.append("ifnull(sum(conversions14d), 0) orderNum, 0 saleNum ");
        sql.append("from ").append(getJdbcHelper().getTable()).append(" r ");
        sql.append("join (select m.marketplace_id,c.month,c.rate,c.puid from dim_currency_rate c join dim_marketplace_info m on ");
        sql.append("c.`from` = m.currency and c.puid=? and `to`=? and month>=? and month <=? ");
        argsList.add(puid);
        argsList.add(currency);
        argsList.add(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        argsList.add(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));

        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sql.append("and m.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }

        sql.append(") c on r.puid = ? and r.puid = c.puid and r.count_month = c.month and c.marketplace_id = r.marketplace_id and r.count_day >= ? and r.count_day <= ? ");
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sql.append("and r.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append("and r.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        sql.append("group by queryWord, matchType ");

        argsList.add(puid);
        argsList.add(startDate);
        argsList.add(endDate);
        return sql.toString();
    }

    @Override
    public String buildQueryAdQueryWordTopSimpleInfosSql(Integer puid,
                                                         List<String> marketplaceIdList,
                                                         List<Integer> shopIdList,
                                                         String currency,
                                                         String startDate,
                                                         String endDate,
                                                         List<Object> argsList,
                                                         String innerSelectColumns,
                                                         boolean isOrderByFieldAboutAmount,
                                                         boolean firstQuery,
                                                         List<String> queryWords, List<String> siteToday, Boolean isSiteToday,
                                                         List<String> portfolioIds, List<String> campaignIds,
                                                         Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum) {
        boolean isWhere = false;
        StringBuilder sql = new StringBuilder();
        sql.append("select query queryWord, match_type matchType, any(r.marketplace_id) marketplaceId, count(distinct keyword_id) targetNum,");
        sql.append(innerSelectColumns);
        sql.append(" from ").append(getJdbcHelper().getTable()).append(" r ");
        if (firstQuery && isOrderByFieldAboutAmount || !firstQuery && !isOrderByFieldAboutAmount) {
            sql.append("join (select m.marketplace_id,c.month,c.rate from dim_currency_rate c join dim_marketplace_info m on ");
            sql.append("c.`from` = m.currency and c.puid=? and `to`=? and month>=? and month <=? ");
            argsList.add(puid);
            argsList.add(currency);
            argsList.add(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
            argsList.add(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));

            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append("and m.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
            }

            sql.append(") c on r.marketplace_id = c.marketplace_id and r.count_month = c.month and r.puid = ? and r.count_day>=? and r.count_day<=? ");
            argsList.add(puid);
            argsList.add(startDate);
            argsList.add(endDate);
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append("and r.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sql.append(SqlStringUtil.dealBitMapDorisInList("r.shop_id", shopIdList, argsList));
            }
        } else {
            isWhere = true;
            sql.append(" where r.puid = ? and r.count_day >=? and r.count_day <= ? ");
            argsList.add(puid);
            argsList.add(startDate);
            argsList.add(endDate);
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append("and r.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sql.append(SqlStringUtil.dealBitMapDorisInList("r.shop_id", shopIdList, argsList));
            }
        }




        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sql.append(SqlStringUtil.dealDorisInList("concat_ws('|', r.marketplace_id, r.count_day)", siteToday, argsList));
            sql.append(" and r.count_day >= ? and r.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sql.append(" and r.count_day >= ? and r.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealDorisInList("r.campaign_id", campaignIds, argsList));
        }


        if (CollectionUtils.isNotEmpty(queryWords) && !firstQuery) {
            sql.append(SqlStringUtil.dealInList("query", queryWords, argsList));
        }

        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (isWhere) {
                sql.append(" and ");
            } else {
                sql.append(" where ");
                isWhere = true;
            }
            sql.append(" r.campaign_id in ( ");
            sql.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = 'sb' ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sql.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sql.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sql.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sql.append(" ) ");
        }

        if (Boolean.TRUE.equals(noZero) && DashboardDataFieldEnum.noZeroFieldSet.contains(dashboardDataFieldEnum.getCode())) {
            if (isWhere) {
                sql.append(" and ");
            } else {
                sql.append(" where ");
                isWhere = true;
            }
            sql.append(getColumn(dashboardDataFieldEnum.getCode()) +" <> 0 ");
        }
        sql.append("group by queryWord, matchType ");
        return sql.toString();
    }

    @Override
    public List<DashboardAdQueryWordMatrixTopDto> queryMatrixInfo(Integer puid, List<String> marketplaceIdList, List<Integer> shopIdList, String currency,
                                                                  String startDate, String endDate, DashboardDataFieldEnum dataField,
                                                                  List<String> keywordIdList, Integer limit, DashboardOrderByEnum orderBy, List<String> siteToday, Boolean isSiteToday,
                                                                  List<String> portfolioIds, List<String> campaignIds,
                                                                  Boolean noZero) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
//        sb.append(" SELECT 'SB' as campaignType, report.keyword_id, report.keyword_text query,  ");
//        sb.append(" report.shop_id shopId, report.marketplace_id marketplaceId, 'keyword' as `type`, ");
//        sb.append(" report.campaign_id campaignId, report.ad_group_id adGroupId, ");
//        sb.append(" report.campaign_name campaignName, report.ad_group_name adGroupName, report.match_type matchType, ");
        sb.append(" SELECT 'sb' as campaignType, 'keyword' as type, report.query_id queryId, ");
        sb.append(" ifnull(sum(cost * c.rate), 0) cost, ifnull(sum(sales14d * c.rate), 0) totalSales, ");
        sb.append(" ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ");
        sb.append(" ifnull(sum(conversions14d), 0) orderNum, ");
        sb.append(" ifnull(ROUND(ifnull(sum(clicks)/ sum(impressions), 0), 4), 0) clickRate, ");//点击率
        sb.append(" ifnull(ROUND(ifnull(sum(conversions14d)/ sum(clicks), 0), 4), 0) conversionRate, ");//转化率
        sb.append(" ifnull(ROUND(ifnull(sum(cost * c.rate), 0)/ ifnull(sum(sales14d * c.rate), 0), 4), 0) acos, ");
        sb.append(" ifnull(ROUND(ifnull(sum(sales14d * c.rate), 0)/ ifnull(sum(cost * c.rate), 0), 4), 0) roas, ");
        sb.append(" ifnull(ROUND(ifnull(sum(cost * c.rate)/ sum(clicks), 0), 4), 0) cpc, ");//cpc
        sb.append(" ifnull(ROUND(ifnull(sum(cost * c.rate)/ sum(conversions14d), 0), 4), 0) cpa ");//cpa
        sb.append(" from ").append(getJdbcHelper().getTable()).append("  report ");
        sb.append(" join (select * from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency and puid = ? and `to` = ? ");
        sb.append(" and c.month >= ").append(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
        sb.append(" and c.month <= ").append(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
        sb.append(") c");
        sb.append(" on c.marketplace_id = report.marketplace_id and report.count_month = c.month ");
        sb.append(" and report.puid = ? ");
        argsList.add(puid);
        argsList.add(currency);
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and report.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and report.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(keywordIdList)) {
            sb.append("and report.keyword_id in ('").append(StringUtils.join(keywordIdList, "','")).append("') ");
        }




        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', report.marketplace_id, report.count_day)", siteToday, argsList));
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("report.campaign_id", campaignIds, argsList));
        }
        if (Boolean.TRUE.equals(noZero)) {
            sb.append(" and " + dataField.getCode() + " <> 0 ");
        }

        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" where report.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = 'sb' ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }

        sb.append(" group by report.query_id ");
        if ("saleNum".equals(orderBy.getCode())) {
            sb.append(" ORDER BY ").append(dataField.getCode());
            if (StringUtils.isNotEmpty(orderBy.getCode())) {
                sb.append(" ").append(orderBy.getCode());
            }
        }
        if (Objects.nonNull(limit) && limit != 0) {
            sb.append(" LIMIT ").append(limit);
        }
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(DashboardAdQueryWordMatrixTopDto.class), argsList.toArray());
    }

    @Override
    public List<OdsCpcSbQueryKeywordReport> getByQueryIdList(Integer puid, List<Integer> shopIdList, Collection<String> queryIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT query_id, query, shop_id, marketplace_id, campaign_id, ad_group_id, keyword_id, ");
        sb.append(" match_type ");
        sb.append(" from ").append(getJdbcHelper().getTable());
        sb.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(queryIdList)) {
            sb.append("and query_id in ('").append(StringUtils.join(queryIdList, "','")).append("') ");
        }
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(OdsCpcSbQueryKeywordReport.class), argsList.toArray());
    }

    @Override
    public Page<CpcSbQueryKeywordReport> pageList(Integer puid, CpcQueryWordDto dto, Page page) {
        StringBuilder selectSql = new StringBuilder("SELECT query_id, ANY(puid) puid, ANY(shop_id) shop_id, ANY(`marketplace_id`) marketplace_id, ANY(count_date) count_date,")
                .append(" ANY(`query`) query, ANY(`query_cn`) query_cn, ANY(ad_format) ad_format, ANY(keyword_id) keyword_id, ANY(keyword_text) keyword_text,")
                .append(" ANY(match_type) match_type, ANY(ad_group_id) ad_group_id, ANY(campaign_id) campaign_id,")
                .append(" sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`conversions14d`) conversions14d, ")
                .append(" sum(orders_new_to_brand14d) `orders_new_to_brand14d`, sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(impression_rank) `impression_rank`,")
                .append(" sum(impression_share) `impression_share`, ")
                .append(" sum(`video5second_views`) AS `video5second_views`, ")
                .append(" sum(`video_complete_views`) AS `video_complete_views`, ")
                .append(" sum(`video_first_quartile_views`) AS `video_first_quartile_views`, ")
                .append(" sum(`video_midpoint_views`) AS `video_midpoint_views`, ")
                .append(" sum(`video_third_quartile_views`) AS `video_third_quartile_views`, ")
                .append(" sum(`video_unmutes`) AS `video_unmutes`, ")
                .append(" sum(`viewable_impressions`) AS `viewable_impressions` ");

        selectSql.append(" FROM ods_t_cpc_sb_query_keyword_report t ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        List<Object> argsList = Lists.newArrayList();
        selectSql.append(this.getWhereSqlCountByKeyword(puid, dto, argsList));

        StringBuilder sql = new StringBuilder("select query_id, puid,shop_id,t.marketplace_id,count_date,query,query_cn,ad_format,keyword_id,");
        sql.append("keyword_text,match_type,ad_group_id,campaign_id,cost,sales14d,impressions,clicks,conversions14d,orders_new_to_brand14d,");
        sql.append("sales_new_to_brand14d,impression_rank,impression_share,video5second_views,video_complete_views,video_first_quartile_views,");
        sql.append("video_midpoint_views,video_third_quartile_views,video_unmutes,viewable_impressions");
        if (dto.isQueryJoinSearchTermsRank()) {
            sql.append(" ,ifnull(search_frequency_rank, 2147483647) search_frequency_rank, ")
                    .append(" ifnull(round(week_ratio*100,2), -2147483648) week_ratio ");
        }
        sql.append(" from ( ");
        sql.append(selectSql);
        sql.append(") t  ");

        if (dto.isQueryJoinSearchTermsRank()) {
            sql.append(" left join ods_t_week_search_terms_analysis a on lower(query) = a.search_term and t.marketplace_id = a.marketplace_id and a.start_date=? and a.marketplace_id=? ");
            argsList.add(dto.getLastWeekSearchTermsRankDate());
            argsList.add(dto.getMarketplaceId());

            if (dto.isQueryJoinSearchTermsRank() && dto.getUseAdvanced() && (
                    dto.getSearchFrequencyRankMin() != null || dto.getSearchFrequencyRankMax() != null ||
                            dto.getWeekRatioMin() != null || dto.getWeekRatioMax() != null)) {
                if (dto.getSearchFrequencyRankMin() != null) {
                    sql.append(" and ifnull(search_frequency_rank, 2147483647) >= ? ");
                    argsList.add(dto.getSearchFrequencyRankMin());
                }
                if (dto.getSearchFrequencyRankMax() != null) {
                    sql.append(" and ifnull(search_frequency_rank, 2147483647) <= ? ");
                    argsList.add(dto.getSearchFrequencyRankMax());
                }
                if (dto.getWeekRatioMin() != null) {
                    sql.append(" and ifnull(round(week_ratio*100,2),-2147483648) >= ? ");
                    argsList.add(dto.getWeekRatioMin());
                }
                if (dto.getWeekRatioMax() != null) {
                    sql.append(" and ifnull(round(week_ratio*100,2),-2147483648) <= ? ");
                    if (dto.getWeekRatioMin() == null) {
                        sql.append(" and ifnull(round(week_ratio*100,2),-2147483648) > -2147483648 ");
                    }
                    argsList.add(dto.getWeekRatioMax());
                }
            }

            sql.append(" where 1=1 ");
            if (dto.isQueryJoinSearchTermsRank() && dto.getUseAdvanced() && (
                    dto.getSearchFrequencyRankMin() != null || dto.getSearchFrequencyRankMax() != null ||
                            dto.getWeekRatioMin() != null || dto.getWeekRatioMax() != null)) {
                sql.append(" and search_frequency_rank is not null ");
                if (dto.getSearchFrequencyRankMin() != null) {
                    sql.append(" and search_frequency_rank >= ? ");
                    argsList.add(dto.getSearchFrequencyRankMin());
                }
                if (dto.getSearchFrequencyRankMax() != null) {
                    sql.append(" and search_frequency_rank <= ? ");
                    argsList.add(dto.getSearchFrequencyRankMax());
                }
                if (dto.getWeekRatioMin() != null) {
                    sql.append(" and round(week_ratio*100,2) >= ? ");
                    argsList.add(dto.getWeekRatioMin());
                }
                if (dto.getWeekRatioMax() != null) {
                    sql.append(" and round(week_ratio*100,2) <= ? ");
                    argsList.add(dto.getWeekRatioMax());
                }
            }
        }

        countSql.append(sql).append(") c ");
        Object[] args = argsList.toArray();

        if (StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())) {
            String orderField = ReportService.getSbReportField(dto.getOrderField(), false, dto.isQueryJoinSearchTermsRank());
            if (StringUtils.isNotBlank(orderField)) {
                sql.append(" order by ").append(orderField);
                if ("desc".equals(dto.getOrderValue())) {
                    sql.append(" desc");
                }
                sql.append(" , query desc, keyword_id desc ");
            } else {
                sql.append(" order by query desc, keyword_id desc ");
            }
        } else {
            sql.append(" order by query desc, keyword_id desc ");
        }

        return this.getPageResultByClass(page.getPageNo(), page.getPageSize(), countSql.toString(), args, sql.toString(), args, CpcSbQueryKeywordReport.class);
    }

    @Override
    public int countAllList(Integer puid, CpcQueryWordDto dto) {
        StringBuilder selectSql = new StringBuilder("SELECT ANY(puid) puid, ANY(shop_id) shop_id, ANY(`marketplace_id`) marketplace_id, ANY(count_date) count_date,")
                .append(" ANY(`query`) query, ANY(`query_cn`) query_cn, ANY(ad_format) ad_format, ANY(keyword_id) keyword_id, ANY(keyword_text) keyword_text,")
                .append(" ANY(match_type) match_type, ANY(ad_group_id) ad_group_id, ANY(campaign_id) campaign_id,")
                .append(" sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`conversions14d`) conversions14d, ")
                .append(" sum(orders_new_to_brand14d) `orders_new_to_brand14d`, sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(impression_rank) `impression_rank`,")
                .append(" sum(impression_share) `impression_share`, ")
                .append(" sum(`video5second_views`) AS `video5second_views`, ")
                .append(" sum(`video_complete_views`) AS `video_complete_views`, ")
                .append(" sum(`video_first_quartile_views`) AS `video_first_quartile_views`, ")
                .append(" sum(`video_midpoint_views`) AS `video_midpoint_views`, ")
                .append(" sum(`video_third_quartile_views`) AS `video_third_quartile_views`, ")
                .append(" sum(`video_unmutes`) AS `video_unmutes`, ")
                .append(" sum(`viewable_impressions`) AS `viewable_impressions`")
                .append(" FROM ods_t_cpc_sb_query_keyword_report ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        List<Object> argsList = Lists.newArrayList();
        selectSql.append(this.getWhereSqlCountByKeyword(puid, dto, argsList));
        String sql = "select t.* from ( " + selectSql +
                ") t  ";
        countSql.append(sql).append(") c");
        Object[] args = argsList.toArray();
        return countPageResult(puid, countSql.toString(), args);
    }

    @Override
    public AdMetricDto getSumAdMetricDto(Integer puid, CpcQueryWordDto dto) {
        StringBuilder sql = new StringBuilder("SELECT sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`conversions14d`) conversions14d FROM ( ");
        boolean isFilterAba = dto.isQueryJoinSearchTermsRank() && dto.getUseAdvanced() && (
                dto.getSearchFrequencyRankMin() != null || dto.getSearchFrequencyRankMax() != null ||
                        dto.getWeekRatioMin() != null || dto.getWeekRatioMax() != null);
        StringBuilder selectSql = new StringBuilder("SELECT sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`conversions14d`) conversions14d");
        if (isFilterAba) {
            selectSql.append(",any(query) query,any(marketplace_id) marketplace_id");
        }
        selectSql.append(" FROM ods_t_cpc_sb_query_keyword_report t ");
        List<Object> argsList = Lists.newArrayList();
        selectSql.append(this.getWhereSqlCountByKeyword(puid, dto, argsList));
        sql.append(selectSql).append(" ) c");
        if (isFilterAba) {
            sql.append(" left join ods_t_week_search_terms_analysis a on lower(query) = a.search_term and c.marketplace_id = a.marketplace_id and a.start_date=? and a.marketplace_id=? ");
            argsList.add(dto.getLastWeekSearchTermsRankDate());
            argsList.add(dto.getMarketplaceId());
            if (dto.getSearchFrequencyRankMin() != null) {
                sql.append(" and ifnull(search_frequency_rank, 2147483647) >= ? ");
                argsList.add(dto.getSearchFrequencyRankMin());
            }
            if (dto.getSearchFrequencyRankMax() != null) {
                sql.append(" and ifnull(search_frequency_rank, 2147483647) <= ? ");
                argsList.add(dto.getSearchFrequencyRankMax());
            }
            if (dto.getWeekRatioMin() != null) {
                sql.append(" and ifnull(round(week_ratio*100,2),-2147483648) >= ? ");
                argsList.add(dto.getWeekRatioMin());
            }
            if (dto.getWeekRatioMax() != null) {
                sql.append(" and ifnull(round(week_ratio*100,2),-2147483648) <= ? ");
                if (dto.getWeekRatioMin() == null) {
                    sql.append(" and ifnull(round(week_ratio*100,2),-2147483648) > -2147483648 ");
                }
                argsList.add(dto.getWeekRatioMax());
            }
            sql.append(" where 1=1 ");
            sql.append(" and search_frequency_rank is not null ");
            if (dto.getSearchFrequencyRankMin() != null) {
                sql.append(" and search_frequency_rank >= ? ");
                argsList.add(dto.getSearchFrequencyRankMin());
            }
            if (dto.getSearchFrequencyRankMax() != null) {
                sql.append(" and search_frequency_rank <= ? ");
                argsList.add(dto.getSearchFrequencyRankMax());
            }
            if (dto.getWeekRatioMin() != null) {
                sql.append(" and round(week_ratio*100,2) >= ? ");
                argsList.add(dto.getWeekRatioMin());
            }
            if (dto.getWeekRatioMax() != null) {
                sql.append(" and round(week_ratio*100,2) <= ? ");
                argsList.add(dto.getWeekRatioMax());
            }
        }
        List<AdMetricDto> adMetricDtoList = getJdbcTemplate().query(sql.toString(), new RowMapper<AdMetricDto>() {
            @Override
            public AdMetricDto mapRow(ResultSet re, int i) throws SQLException {
                AdMetricDto dto = AdMetricDto.builder()
                        .sumCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .sumAdSale(Optional.ofNullable(re.getBigDecimal("sales14d")).orElse(BigDecimal.ZERO))
                        .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("conversions14d")).orElse(BigDecimal.ZERO))
                        .build();
                return dto;
            }
        }, argsList.toArray());

        return adMetricDtoList != null && adMetricDtoList.size() > 0 ? adMetricDtoList.get(0) : null;
    }

    @Override
    public List<AdHomePerformancedto> getQueryKeywordReportAggregate(Integer puid, CpcQueryWordDto dto, boolean isGroupByDate, boolean selCompareData) {
        List<Object> argsList = Lists.newArrayList();
        //keyword表
        StringBuilder sql = new StringBuilder("select ");
        if (isGroupByDate) {
            sql.append(" count_day, ");
        }
        sql.append(" sum(impressions) `impressions`, sum(clicks) `clicks`, SUM(cost) cost, sum(conversions14d) as sale_num, SUM(sales14d) total_sales, ")
                .append(" sum(orders_new_to_brand14d) `orders_new_to_brand14d`, sum(sales_new_to_brand14d) `sales_new_to_brand14d`, sum(impression_rank) `impression_rank`, ")
                .append(" sum(impression_share) `impression_share`,")
                .append(" sum(`video5second_views`) AS `video5second_views`, ")
                .append(" sum(`video_complete_views`) AS `video_complete_views`, ")
                .append(" sum(`video_first_quartile_views`) AS `video_first_quartile_views`, ")
                .append(" sum(`video_midpoint_views`) AS `video_midpoint_views`, ")
                .append(" sum(`video_third_quartile_views`) AS `video_third_quartile_views`, ")
                .append(" sum(`video_unmutes`) AS `video_unmutes`, ")
                .append(" sum(`viewable_impressions`) AS `viewable_impressions`");
        if (dto.isQueryJoinSearchTermsRank()) {
            sql.append(",query,marketplace_id ");
        }
        sql.append(" FROM ").append(this.getJdbcHelper().getTable()).append(" r ");

        sql.append(" where puid = ? and shop_id = ? and count_day >= ? and count_day <= ? and query_id in ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        if (selCompareData) {
            argsList.add(dto.getCompareStartDate());
            argsList.add(dto.getCompareEndDate());
        } else {
            argsList.add(dto.getStart());
            argsList.add(dto.getEnd());
        }
        //子查询
        sql.append("(select query_id queryId")
                .append(" FROM ").append(this.getJdbcHelper().getTable()).append(" t ");
        sql.append(this.getWhereSqlCountByKeyword(puid, dto, argsList)).append(") ");

        if (isGroupByDate) {
            sql.append(" group by count_day ");
        }
        if (dto.isQueryJoinSearchTermsRank()) {
            if (isGroupByDate) {
                sql.append(" ,query,marketplace_id ");
            } else {
                sql.append(" group by query,marketplace_id ");
            }
        }

        StringBuilder outerSql = new StringBuilder("select ");
        if (isGroupByDate) {
            outerSql.append(" count_day, ");
        }
        outerSql.append(" sum(impressions) `impressions`, sum(clicks) `clicks`, SUM(cost) cost, sum(sale_num) sale_num, SUM(total_sales) total_sales, ")
                .append(" sum(orders_new_to_brand14d) `orders_new_to_brand14d`, sum(sales_new_to_brand14d) `sales_new_to_brand14d`, sum(impression_rank) `impression_rank`, ")
                .append(" sum(impression_share) `impression_share`,")
                .append(" sum(`video5second_views`) `video5second_views`, ")
                .append(" sum(`video_complete_views`) `video_complete_views`, ")
                .append(" sum(`video_first_quartile_views`) `video_first_quartile_views`, ")
                .append(" sum(`video_midpoint_views`) `video_midpoint_views`, ")
                .append(" sum(`video_third_quartile_views`) `video_third_quartile_views`, ")
                .append(" sum(`video_unmutes`) `video_unmutes`, ")
                .append(" sum(`viewable_impressions`) `viewable_impressions`");
        outerSql.append(" from (").append(sql).append(") o ");
        if (dto.isQueryJoinSearchTermsRank()) {
            outerSql.append(" left join ods_t_week_search_terms_analysis a on lower(query) = a.search_term and o.marketplace_id = a.marketplace_id and a.start_date=? and a.marketplace_id=? ");
            argsList.add(dto.getLastWeekSearchTermsRankDate());
            argsList.add(dto.getMarketplaceId());

            if (dto.isQueryJoinSearchTermsRank() && dto.getUseAdvanced() && (
                    dto.getSearchFrequencyRankMin() != null || dto.getSearchFrequencyRankMax() != null ||
                            dto.getWeekRatioMin() != null || dto.getWeekRatioMax() != null)) {
                if (dto.getSearchFrequencyRankMin() != null) {
                    outerSql.append(" and ifnull(search_frequency_rank, 2147483647) >= ? ");
                    argsList.add(dto.getSearchFrequencyRankMin());
                }
                if (dto.getSearchFrequencyRankMax() != null) {
                    outerSql.append(" and ifnull(search_frequency_rank, 2147483647) <= ? ");
                    argsList.add(dto.getSearchFrequencyRankMax());
                }
                if (dto.getWeekRatioMin() != null) {
                    outerSql.append(" and ifnull(round(week_ratio*100,2),-2147483648) >= ? ");
                    argsList.add(dto.getWeekRatioMin());
                }
                if (dto.getWeekRatioMax() != null) {
                    outerSql.append(" and ifnull(round(week_ratio*100,2),-2147483648) <= ? ");
                    if (dto.getWeekRatioMin() == null) {
                        outerSql.append(" and ifnull(round(week_ratio*100,2),-2147483648) > -2147483648 ");
                    }
                    argsList.add(dto.getWeekRatioMax());
                }
                outerSql.append(" where 1=1 ");
                outerSql.append(" and search_frequency_rank is not null ");
                if (dto.getSearchFrequencyRankMin() != null) {
                    outerSql.append(" and search_frequency_rank >= ? ");
                    argsList.add(dto.getSearchFrequencyRankMin());
                }
                if (dto.getSearchFrequencyRankMax() != null) {
                    outerSql.append(" and search_frequency_rank <= ? ");
                    argsList.add(dto.getSearchFrequencyRankMax());
                }
                if (dto.getWeekRatioMin() != null) {
                    outerSql.append(" and round(week_ratio*100,2) >= ? ");
                    argsList.add(dto.getWeekRatioMin());
                }
                if (dto.getWeekRatioMax() != null) {
                    outerSql.append(" and round(week_ratio*100,2) <= ? ");
                    argsList.add(dto.getWeekRatioMax());
                }
            }

        }
        if (isGroupByDate) {
            outerSql.append(" group by count_day ");
        }

        return getJdbcTemplate().query(outerSql.toString(), new RowMapper<AdHomePerformancedto>() {
            @Override
            public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                AdHomePerformancedto dto = AdHomePerformancedto.builder()
                        .countDate(!isGroupByDate ? "" : DateUtil.dateToStrWithFormat(DateUtil.strToDate(re.getString("count_day"), DateUtil.PATTERN), DateUtil.PATTERN_YYYYMMDD))
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                        .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                        .impressionRank(Optional.ofNullable(re.getInt("impression_rank")).orElse(0))
                        .impressionShare(Optional.ofNullable(re.getDouble("impression_share")).orElse(0.0))
                        .video5SecondViews(Optional.ofNullable(re.getInt("video5second_views")).orElse(0))
                        .videoFirstQuartileViews(Optional.ofNullable(re.getInt("video_first_quartile_views")).orElse(0))
                        .videoMidpointViews(Optional.ofNullable(re.getInt("video_Midpoint_Views")).orElse(0))
                        .videoThirdQuartileViews(Optional.ofNullable(re.getInt("video_third_quartile_views")).orElse(0))
                        .videoCompleteViews(Optional.ofNullable(re.getInt("video_complete_views")).orElse(0))
                        .videoUnmutes(Optional.ofNullable(re.getInt("video_unmutes")).orElse(0))
                        .viewableImpressions(Optional.ofNullable(re.getInt("viewable_impressions")).orElse(0))
                        .build();
                return dto;
            }
        }, argsList.toArray());
    }

    @Override
    public List<AdProductReportSearchTermsViewDto> selectProductSearchTermsViewInfo(Integer puid, SearchTermsViewParam param) {
        StringBuilder sql = new StringBuilder("SELECT 'sb' as type, `query`,any(query_cn) query_cn,")
                .append(" ad_group_id,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(conversions14d) conversions14d,")
                .append("SUM(sales14d) sales14d, SUM(viewable_impressions) viewImpressions, SUM(orders_new_to_brand14d) ordersNewToBrandFTD, SUM(sales_new_to_brand14d) salesNewToBrandFTD ")
                .append(" FROM ").append(this.getJdbcHelper().getTable());
        List<Object> argList = new ArrayList<>();
        sql.append(this.getWhereSqlCountByKeyword(puid, param, argList));
        sql.append(" group by ad_group_id, query ");
        sql.append(" limit " + SEATCH_TERMS_VIEW_LIMIT);

        return getJdbcTemplate().query(sql.toString(), (re, i) -> AdProductReportSearchTermsViewDto.builder()
                .type(re.getString("type"))
                .query(re.getString("query"))
                .queryCn(re.getString("query_cn"))
                .adGroupId(re.getString("ad_group_id"))
                .impressions(re.getLong("impressions"))
                .clicks(re.getLong("clicks"))
                .cost(re.getBigDecimal("cost") != null ? re.getBigDecimal("cost") : BigDecimal.ZERO)
                // 本广告产品销量
                .orderNum(0)
                .adOrderNum(0)
                .totalSales(re.getBigDecimal("sales14d") != null ? re.getBigDecimal("sales14d") : BigDecimal.ZERO)
                .adSaleNum(0)
                .adSales(BigDecimal.ZERO)
                .saleNum(Optional.ofNullable(re.getInt("conversions14d")).orElse(0))
                .viewImpressions(Optional.ofNullable(re.getLong("viewImpressions")).orElse(0L))
                .ordersNewToBrandFTD(Optional.ofNullable(re.getInt("ordersNewToBrandFTD")).orElse(0))
                .salesNewToBrandFTD(re.getBigDecimal("salesNewToBrandFTD") != null ? re.getBigDecimal("salesNewToBrandFTD") : BigDecimal.ZERO)
                .build(), argList.toArray());
    }

    @Override
    public List<WordRootTopVo> getWordRootToplist(QueryWordTopQo dto) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select word_root, count(distinct query_id) count ")
                .append(" from ods_t_amazon_word_root_query ");
        sqlSb.append(" where puid = ? and shop_id = ? and count_day >= ? and count_day <= ? ");
        argsList.add(dto.getPuid());
        argsList.add(dto.getShopId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        //搜索词来源类型，只取sb的
        sqlSb.append(" and query_type = ? ");
        argsList.add(WordRoot.QueryType.SB_QUERY.getType());
        if (StringUtils.isNotBlank(dto.getWordFrequencyType())) {
            WordRoot.WordFrequencyType wordFrequencyType = WordRoot.WordFrequencyType.getWordFrequencyType(Integer.valueOf(dto.getWordFrequencyType()));
            if (wordFrequencyType != null) {
                sqlSb.append(" and word_frequency_type = ? ");
                argsList.add(dto.getWordFrequencyType());
            }
        }

        //查询queryId
        StringBuilder queryIdSql;
        if (!dto.isQueryJoinSearchTermsRank()) {
            queryIdSql = new StringBuilder("SELECT query_id FROM ").append(this.getJdbcHelper().getTable())
                    .append(this.getWhereSqlCountByKeyword(dto.getPuid(), dto, argsList));
        } else {
            queryIdSql = new StringBuilder("SELECT query_id FROM (").append("SELECT query_id, any(marketplace_id) marketplace_id, any(query) query FROM ").append(this.getJdbcHelper().getTable())
                    .append(this.getWhereSqlCountByKeyword(dto.getPuid(), dto, argsList)).append(") r ");
            queryIdSql.append(" left join ods_t_week_search_terms_analysis a on lower(query) = a.search_term and r.marketplace_id = a.marketplace_id and a.start_date=? and a.marketplace_id=? ");
            argsList.add(dto.getLastWeekSearchTermsRankDate());
            argsList.add(dto.getMarketplaceId());

            if (dto.getSearchFrequencyRankMin() != null) {
                queryIdSql.append(" and ifnull(search_frequency_rank, 2147483647) >= ? ");
                argsList.add(dto.getSearchFrequencyRankMin());
            }
            if (dto.getSearchFrequencyRankMax() != null) {
                queryIdSql.append(" and ifnull(search_frequency_rank, 2147483647) <= ? ");
                argsList.add(dto.getSearchFrequencyRankMax());
            }
            if (dto.getWeekRatioMin() != null) {
                queryIdSql.append(" and ifnull(round(week_ratio*100,2),-2147483648) >= ? ");
                argsList.add(dto.getWeekRatioMin());
            }
            if (dto.getWeekRatioMax() != null) {
                queryIdSql.append(" and ifnull(round(week_ratio*100,2),-2147483648) <= ? ");
                if (dto.getWeekRatioMin() == null) {
                    queryIdSql.append(" and ifnull(round(week_ratio*100,2),-2147483648) > -2147483648 ");
                }
                argsList.add(dto.getWeekRatioMax());
            }
            queryIdSql.append(" where 1=1 ");
            queryIdSql.append(" and search_frequency_rank is not null ");
            if (dto.getSearchFrequencyRankMin() != null) {
                queryIdSql.append(" and search_frequency_rank >= ?");
                argsList.add(dto.getSearchFrequencyRankMin());
            }
            if (dto.getSearchFrequencyRankMax() != null) {
                queryIdSql.append(" and search_frequency_rank <= ?");
                argsList.add(dto.getSearchFrequencyRankMax());
            }
            if (dto.getWeekRatioMin() != null) {
                queryIdSql.append(" and round(week_ratio*100,2) >= ?");
                argsList.add(dto.getWeekRatioMin());
            }
            if (dto.getWeekRatioMax() != null) {
                queryIdSql.append(" and round(week_ratio*100,2) <= ?");
                argsList.add(dto.getWeekRatioMax());
            }
        }

        sqlSb.append(" and query_id in (").append(queryIdSql).append(") ");
        sqlSb.append(" group by word_root order by count desc ");
        if (dto.getTop() != null) {
            sqlSb.append(" limit ").append(dto.getTop().toString());
        }
        return getJdbcTemplate().query(sqlSb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordRootTopVo.class));
    }

    private String getWhereSqlCountByKeyword(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? and count_day >= ? and count_day <= ? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(), StringUtil.SPLIT_COMMA);
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            whereSql.append(SqlStringUtil.dealInList("lower(match_type)", matchTypeList, argsList));
        }
        if (StringUtils.isNotBlank(dto.getCampaignId())) {
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", list, argsList));

        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(dto.getGroupId())) {
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", list, argsList));
        }
        // 广告策略筛选
        if(CollectionUtils.isNotEmpty(dto.getAdStrategyTypeList())){
            String sql = AdQueryStrategyTypeEnum.getSql(dto.getAdStrategyTypeList(), dto.getAutoRuleIds(),dto.getAutoRuleGroupIds(), argsList, "query_id","ad_group_id");
            if(StringUtils.isNotEmpty(sql)){
                if(!dto.getAdStrategyTypeList().contains(AdQueryStrategyTypeEnum.NONE.getCode())){
                    whereSql.append(sql);
                    whereSql.append(" and lower(match_type) != 'theme' ");
                }else{
                    whereSql.append(" and ( ");
                    // 去掉第一个and
                    sql = sql.replaceFirst("and", "");
                    whereSql.append(sql);
                    whereSql.append(" or lower(match_type) = 'theme' )");
                }
            }
        }
        //标签
        if (CollectionUtils.isNotEmpty(dto.getQueryWordTagTypeList())) {
            if (dto.getQueryWordTagTypeList().contains(Constants.QUERY_NOT_TARGET)) {

                if (dto.getQueryWordTagTypeList().size() > 1) {
                    List<String> allSearchField = CpcQueryWordDto.QueryWordTagTypeEnum.getAllSearchField();
                    allSearchField.removeAll(dto.getQueryWordTagTypeList());
                    if (CollectionUtils.isNotEmpty(allSearchField)) {
                        whereSql.append(" AND concat_ws(',', ad_group_id, query) not in ")
                                .append(" ( ")
                                .append(SearchQueryTagSqlHelper.getSbKeywordSearchQueryTagInSql(dto.getPuid(), dto, argsList, allSearchField))
                                .append(" ) ");
                    }
                } else {
                    whereSql.append(" and concat_ws(',', ad_group_id, query) not in ")
                            .append(" ( ")
                            .append(SearchQueryTagSqlHelper.getSbKeywordSearchQueryTagInSql(dto.getPuid(), dto, argsList, CpcQueryWordDto.QueryWordTagTypeEnum.getAllSearchField()))
                            .append(" ) ");
                }
            } else {
                whereSql.append(" and concat_ws(',', ad_group_id, query) in ")
                        .append(" ( ")
                        .append(SearchQueryTagSqlHelper.getSbKeywordSearchQueryTagInSql(dto.getPuid(), dto, argsList, dto.getQueryWordTagTypeList()))
                        .append(" ) ");
            }

        }
        //词根
        if (StringUtils.isNotBlank(dto.getWordRoot())) {
            whereSql.append(" and query_id in ( ")
                    .append(this.getWordRootQueryIdSqlByQuery(puid, dto, argsList, WordRoot.QueryType.SB_QUERY.getType()))
                    .append(" ) ");
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcSbQueryKeywordReport.class, dto.getSearchField());
            //将搜索词转小写和数据库中函数小写匹配
            field = "lower(" + field + ")";
            dto.setSearchValue(dto.getSearchValue().toLowerCase());
            if (StringUtils.isNotEmpty(field)) {
                //将搜索词转小写和数据库中函数小写匹配
                field = "lower(" + field + ")";
                dto.setSearchValue(dto.getSearchValue().toLowerCase());
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    whereSql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    if (dto.getListSearchValue().size() > 1) {
                        whereSql.append(SqlStringUtil.dealInList(field, dto.getListSearchValue(), argsList));
                    } else {
                        whereSql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getSearchValue());
                    }
                }
            }
        }
        whereSql.append(" group by query_id ");
        if (dto.getUseAdvanced()) {
            whereSql.append(this.getHavingSqlCountByKeyword(dto, argsList));
        }
        return whereSql.toString();
    }

    private String getHavingSqlCountByKeyword(CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder havingSql = new StringBuilder();
        BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

        havingSql.append(" having 1=1 ");
        //展示量
        if (dto.getImpressionsMin() != null) {
            havingSql.append(" and SUM(impressions) >= ?");
            argsList.add(dto.getImpressionsMin());
        }
        if (dto.getImpressionsMax() != null) {
            havingSql.append(" and SUM(impressions) <= ?");
            argsList.add(dto.getImpressionsMax());
        }
        //点击量
        if (dto.getClicksMin() != null) {
            havingSql.append(" and SUM(clicks) >= ?");
            argsList.add(dto.getClicksMin());
        }
        if (dto.getClicksMax() != null) {
            havingSql.append(" and SUM(clicks) <= ?");
            argsList.add(dto.getClicksMax());
        }
        //点击率（clicks/impressions）
        if (dto.getClickRateMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(clicks)/SUM(impressions),0),4) >= ?");
            argsList.add(dto.getClickRateMin());
        }
        if (dto.getClickRateMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(clicks)/SUM(impressions),0),4) <= ?");
            argsList.add(dto.getClickRateMax());
        }
        //花费
        if (dto.getCostMin() != null) {
            havingSql.append(" and SUM(cost) >= ?");
            argsList.add(dto.getCostMin());
        }
        if (dto.getCostMax() != null) {
            havingSql.append(" and SUM(cost) <= ?");
            argsList.add(dto.getCostMax());
        }
        //cpc  平均点击费用
        if (dto.getCpcMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(clicks),0),2) >= ?");
            argsList.add(dto.getCpcMin());
        }
        if (dto.getCpcMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(clicks),0),2) <= ?");
            argsList.add(dto.getCpcMax());
        }

        //广告订单量
        if (dto.getOrderNumMin() != null) {
            havingSql.append(" and SUM(conversions14d) >= ?");
            argsList.add(dto.getOrderNumMin());
        }
        if (dto.getOrderNumMax() != null) {
            havingSql.append(" and SUM(conversions14d) <= ?");
            argsList.add(dto.getOrderNumMax());
        }
        //广告销售额
        if (dto.getSalesMin() != null) {
            havingSql.append(" and SUM(sales14d) >= ?");
            argsList.add(dto.getSalesMin());
        }
        if (dto.getSalesMax() != null) {
            havingSql.append(" and SUM(sales14d) <= ?");
            argsList.add(dto.getSalesMax());
        }
        //订单转化率
        if (dto.getSalesConversionRateMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(conversions14d)/SUM(clicks),0),4) >= ?");
            argsList.add(dto.getSalesConversionRateMin());
        }
        if (dto.getSalesConversionRateMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(conversions14d)/SUM(clicks),0),4) <= ?");
            argsList.add(dto.getSalesConversionRateMax());
        }
        //acos
        if (dto.getAcosMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(sales14d),0),4) >= ?");
            argsList.add(dto.getAcosMin());
        }
        if (dto.getAcosMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(sales14d),0),4) <= ?");
            argsList.add(dto.getAcosMax());
        }
        // roas
        if (dto.getRoasMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(sales14d)/SUM(cost),0),2) >= ?");
            argsList.add(dto.getRoasMin());
        }
        // roas
        if (dto.getRoasMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(sales14d)/SUM(cost),0),2) <= ?");
            argsList.add(dto.getRoasMax());
        }
        // acots  需要乘以店铺销售额
        if (dto.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                havingSql.append(" and ROUND((ifnull(SUM(cost),0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(dto.getAcotsMin());
            } else {
                havingSql.append(" and 0 >= ? ");
                argsList.add(dto.getAcotsMin());
            }
        }
        // acots  需要乘以店铺销售额
        if (dto.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                havingSql.append(" and ROUND((ifnull(SUM(cost),0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(dto.getAcotsMax());
            } else {
                havingSql.append(" and 0 <= ? ");
                argsList.add(dto.getAcotsMin());
            }
        }
        // asots 需要乘以店铺销售额
        if (dto.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                havingSql.append(" and ROUND((ifnull(SUM(sales14d),0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(dto.getAsotsMin());
            } else {
                havingSql.append(" and 0 >= ? ");
                argsList.add(dto.getAcotsMin());
            }
        }
        // asots  需要乘以店铺销售额
        if (dto.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                havingSql.append(" and ROUND((ifnull(SUM(sales14d),0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(dto.getAsotsMax());
            } else {
                havingSql.append(" and 0 <= ? ");
                argsList.add(dto.getAcotsMin());
            }
        }
        //CPA:广告花费除以广告订单量
        if (dto.getCpaMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(conversions14d),0), 4) >= ?");
            argsList.add(dto.getCpaMin());
        }
        if (dto.getCpaMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(conversions14d),0), 4) <= ?");
            argsList.add(dto.getCpaMax());
        }
        //“品牌新买家”订单量 orders_new_to_brand14d
        if (dto.getOrdersNewToBrandFTDMin() != null) {
            havingSql.append(" and ifnull(SUM(orders_new_to_brand14d), 0) >= ?");
            argsList.add(dto.getOrdersNewToBrandFTDMin());
        }
        if (dto.getOrdersNewToBrandFTDMax() != null) {
            havingSql.append(" and ifnull(SUM(orders_new_to_brand14d), 0) <= ?");
            argsList.add(dto.getOrdersNewToBrandFTDMax());
        }
        //“品牌新买家”订单百分比
        if (dto.getOrderRateNewToBrandFTDMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(orders_new_to_brand14d)/SUM(conversions14d),0), 4) >= ?");
            argsList.add(dto.getOrderRateNewToBrandFTDMin());
        }
        if (dto.getOrderRateNewToBrandFTDMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(orders_new_to_brand14d)/SUM(conversions14d),0), 4) <= ?");
            argsList.add(dto.getOrderRateNewToBrandFTDMax());
        }
        //“品牌新买家”销售额
        if (dto.getSalesNewToBrandFTDMin() != null) {
            havingSql.append(" and ifnull(SUM(sales_new_to_brand14d), 0) >= ? ");
            argsList.add(dto.getSalesNewToBrandFTDMin().doubleValue());
        }
        if (dto.getSalesNewToBrandFTDMax() != null) {
            havingSql.append(" and ifnull(SUM(sales_new_to_brand14d), 0) <= ? ");
            argsList.add(dto.getSalesNewToBrandFTDMax().doubleValue());
        }
        //“品牌新买家”销售额百分比
        if (dto.getSalesRateNewToBrandFTDMin() != null) {
            havingSql.append(" and ROUND(ROUND(ifnull((SUM(sales_new_to_brand14d) * 100 / SUM(sales14d)), 0), 4), 2) >= ? ");
            argsList.add(MathUtil.multiply(dto.getSalesRateNewToBrandFTDMin(), BigDecimal.valueOf(100)));
        }
        if (dto.getSalesRateNewToBrandFTDMax() != null) {
            havingSql.append(" and ROUND(ROUND(ifnull((SUM(sales_new_to_brand14d) * 100 / SUM(sales14d)), 0), 4), 2) <= ? ");
            argsList.add(MathUtil.multiply(dto.getSalesRateNewToBrandFTDMax(), BigDecimal.valueOf(100)));
        }
        //“品牌新买家”订单转化率 orders_new_to_brand14d/clicks
        if (dto.getBrandNewBuyerOrderConversionRateMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(orders_new_to_brand14d)/SUM(clicks), 0), 4) >= ?");
            argsList.add(MathUtil.divide(dto.getBrandNewBuyerOrderConversionRateMin(), BigDecimal.valueOf(100), 4));
        }
        if (dto.getBrandNewBuyerOrderConversionRateMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(orders_new_to_brand14d)/SUM(clicks), 0), 4) <= ?");
            argsList.add(MathUtil.divide(dto.getBrandNewBuyerOrderConversionRateMax(), BigDecimal.valueOf(100), 4));
        }


        if (dto.getVideo5SecondViewsMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(video5second_views), 0), 4) >= ?");
            argsList.add(dto.getVideo5SecondViewsMin());
        }
        if (dto.getVideo5SecondViewsMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(video5second_views), 0), 4) <= ?");
            argsList.add(dto.getVideo5SecondViewsMax());
        }

        if (dto.getVideoCompleteViewsMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(video_complete_views), 0), 4) >= ?");
            argsList.add(dto.getVideoCompleteViewsMin());
        }
        if (dto.getVideoCompleteViewsMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(video_complete_views), 0), 4) <= ?");
            argsList.add(dto.getVideoCompleteViewsMax());
        }

        //可见展示次数
        if (dto.getViewImpressionsMin() != null) {
            havingSql.append(" and sum(viewable_impressions) >= ?");
            argsList.add(dto.getViewImpressionsMin());
        }
        if (dto.getViewImpressionsMax() != null) {
            havingSql.append(" and sum(viewable_impressions) <= ?");
            argsList.add(dto.getViewImpressionsMax());
        }

        // 观看率 筛选
        if (dto.getViewabilityRateMin() != null) {
            havingSql.append(" and ROUND(ifnull((SUM(viewable_impressions)/SUM(impressions)) * 100, 0), 2) >= ? ");
            argsList.add(dto.getViewabilityRateMin());
        }
        if (dto.getViewabilityRateMax() != null) {
            havingSql.append(" and ROUND(ifnull((SUM(viewable_impressions)/SUM(impressions)) * 100, 0), 2) <= ? ");
            argsList.add(dto.getViewabilityRateMax());
        }

        // 观看点击率 筛选
        if (dto.getViewClickThroughRateMin() != null) {
            havingSql.append(" and ROUND(ifnull((SUM(clicks)/SUM(viewable_impressions)) * 100, 0), 2) >= ? ");
            argsList.add(dto.getViewClickThroughRateMin());
        }
        if (dto.getViewClickThroughRateMax() != null) {
            havingSql.append(" and ROUND(ifnull((SUM(clicks)/SUM(viewable_impressions)) * 100, 0), 2) <= ? ");
            argsList.add(dto.getViewClickThroughRateMax());
        }

        // 广告笔单价(广告销售额÷广告订单量×100%)
        if (dto.getAdvertisingUnitPriceMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(sales14d)/SUM(conversions14d), 0), 2) >= ?");
            argsList.add(dto.getAdvertisingUnitPriceMin());
        }
        if (dto.getAdvertisingUnitPriceMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(sales14d)/SUM(conversions14d), 0), 2) <= ?");
            argsList.add(dto.getAdvertisingUnitPriceMax());
        }
        return havingSql.toString();
    }

    private String getWordRootQueryIdSqlByQuery(int puid, CpcQueryWordDto dto, List<Object> argsList, Integer wordRootQueryType) {
        StringBuilder sb = new StringBuilder("select distinct query_id from ods_t_amazon_word_root_query ");
        sb.append(" where puid = ? and shop_id = ? and count_day >= ? and count_day <= ? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());

        sb.append(" and query_type = ? ");
        argsList.add(wordRootQueryType);

        //词根
        if (StringUtils.isNotBlank(dto.getWordRoot())) {
            sb.append(" and word_root = ? ");
            argsList.add(dto.getWordRoot());
        }
        return sb.toString();
    }

    /**
     * 用户排除为0 的字段处理
     * @param orderByField
     * @return
     */
    private String getColumn(String orderByField) {

        switch (orderByField) {
            case "totalSales":
                return " r.sales14d ";
            case "orderNum":
                return " r.conversions14d ";
            case "saleNum":
                return " ifnull(0,0) ";
            default:
                return orderByField ;
        }
    }


    @Override
    public String buildQueryAdQueryWordPageSql(Integer puid,
                                               List<String> marketplaceIdList,
                                               List<Integer> shopIdList,
                                               String startDate,
                                               String endDate,
                                               List<Object> argsList,
                                               String queryWord, List<String> siteToday, Boolean isSiteToday,
                                               List<String> portfolioIds, List<String> campaignIds,
                                               Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum, String matchType) {

        StringBuilder sql = new StringBuilder();
        sql.append("select  'sb' adType, 'keyword' type, any(shop_id) shopId,any(keyword_id) keywordId, any(campaign_id) campaignId, any(ad_group_id) adGroupId, any(match_type) matchType, any(keyword_text) keywordText  ");
        sql.append(" from ").append(getJdbcHelper().getTable());
        sql.append(" where puid = ?  ");
        argsList.add(puid);

        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sql.append("and marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("shop_id", shopIdList, argsList));
        }


        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sql.append(SqlStringUtil.dealDorisInList("concat_ws('|', marketplace_id, count_day)", siteToday, argsList));
            sql.append(" and count_day >= ? and count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sql.append(" and count_day >= ? and count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealDorisInList("campaign_id", campaignIds, argsList));
        }

        sql.append(" and query = ? ");
        argsList.add(queryWord);


        if (CollectionUtils.isNotEmpty(portfolioIds)) {

            sql.append(" and ");

            sql.append(" campaign_id in ( ");
            sql.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = 'sb' ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sql.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sql.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append(" )  ");
                }
            } else {
                sql.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sql.append(" ) ");
        }

        if (Boolean.TRUE.equals(noZero) && DashboardDataFieldEnum.noZeroFieldSet.contains(dashboardDataFieldEnum.getCode())) {

            sql.append(" and ");

            sql.append(getColumn(dashboardDataFieldEnum.getCode()) + " <> 0 ");
        }
        if (StringUtils.isNotBlank(matchType)) {
            sql.append(" and LOWER(match_type) = ? ");
            argsList.add(matchType.toLowerCase());
        }

        sql.append("group by keyword_id ");
        return sql.toString();
    }

    private String getWhereSqlCountByKeyword(int puid, SearchTermsViewParam dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid = ? ");
        argsList.add(puid);
        whereSql.append(SqlStringUtil.dealBitMapDorisInList("shop_id",dto.getShopIdList(), argsList));
        whereSql.append(" and count_day >= ? and count_day <= ? ");
        argsList.add(dto.getStartDate());
        argsList.add(dto.getEndDate());
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(), StringUtil.SPLIT_COMMA);
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            whereSql.append(SqlStringUtil.dealInList("lower(match_type)", matchTypeList, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getSbAdGroupIdList())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", dto.getSbAdGroupIdList(), argsList));
        }

        if (StringUtils.isNotBlank(dto.getQueryField()) && StringUtils.isNotBlank(dto.getQueryValue())) {
            String field = "query";
            if (dto.getQueryType().equals("keywordText")) {
                field = "keyword_text";
            }
            if ("blur".equals(dto.getQueryType())) { //模糊搜索
                whereSql.append(" and ").append(field).append(" like ?");
                argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getQueryValue().toLowerCase()) + "%");
            } else {//默认精确
                List<String> querys = dto.getListQueryValueNew().stream().map(String::toLowerCase).collect(Collectors.toList());
                if (dto.getListQueryValueNew().size() > 1) {
                    whereSql.append(SqlStringUtil.dealInList(field, querys, argsList));
                } else {
                    whereSql.append(" and ").append(field).append(" = ?");
                    argsList.add(querys.get(0));
                }
            }
        }
        return whereSql.toString();
    }

    @Override
    public List<SearchTermAggregateBO> allSearchTermAggregateDataByQueryList(Integer puid, CpcQueryWordDto dto, String startDate, String endDate,
                                                                             List<String> queryList) {
        if (CollectionUtils.isEmpty(queryList)) {
            return new ArrayList<>();
        }
        List<Object> args = new ArrayList<>();
        StringJoiner sj = new StringJoiner(" union all ");
        List<List<String>> queryPartitionList = Lists.partition(queryList, 8000);
        StringBuilder patitionSql;
        for (List<String> querys : queryPartitionList) {
            patitionSql = new StringBuilder("SELECT ")
                    .append(" IFNULL(sum(`impressions`), 0) impressions, IFNULL(sum(`clicks`), 0) clicks, IFNULL(sum(`cost`), 0) cost, IFNULL(sum(`conversions14d`), 0) sale_num,")
                    .append(" IFNULL(sum(`sales14d`), 0) total_sales, 0 ad_sales, 0 ad_other_sales")
                    .append(" from ").append(this.getJdbcHelper().getTable());
            patitionSql.append(" where puid=? and marketplace_id=? and count_day>=? and count_day<=?");
            args.add(puid);
            args.add(dto.getMarketplaceId());
            args.add(startDate);
            args.add(endDate);
            patitionSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", dto.getGroupIdList(), args));
            patitionSql.append(SqlStringUtil.dealInList("query", querys, args));
            sj.add(patitionSql.toString());
        }
        return getJdbcTemplate().query(sj.toString(), new BeanPropertyRowMapper<>(SearchTermAggregateBO.class), args.toArray());
    }
}

