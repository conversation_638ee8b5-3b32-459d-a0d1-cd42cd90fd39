package com.meiyunji.sponsored.service.cpc.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.service.adTagSystem.param.TagAdCampaignListParam;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdCampaignStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDorisDao;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.cpc.vo.CampaignPageParam;
import com.meiyunji.sponsored.service.cpc.vo.PortfolioPageParam;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformanceNewDto;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.multiple.campagin.enums.CampaignColumnEnum;
import com.meiyunji.sponsored.service.multiple.campagin.enums.CampaignOrderByEnum;
import com.meiyunji.sponsored.service.util.Constant;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class AmazonAdCampaignAllDorisDaoImpl extends DorisBaseDaoImpl<AmazonAdCampaignDorisAllReport> implements IAmazonAdCampaignAllDorisDao {


    @Override
    public int listAmazonAdCampaignAllCount(Integer puid, CampaignPageParam param) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        StringBuilder countSql = new StringBuilder("select count(*) from (select c.campaign_id campaign_id");
        if (param.getUseAdvanced()) {
            Set<String> strings = getSelectKey(param);
            if (CollectionUtils.isNotEmpty(strings)) {
                Set<String> fieldKey = strings.stream().filter(SQL_MAP::containsKey).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(fieldKey)) {
                    countSql.append(",");
                    countSql.append(fieldKey.stream().map(SQL_MAP::get).collect(Collectors.joining(",")));
                }
            }
        }
        countSql.append(" from  ods_t_amazon_ad_campaign_all c left join ods_t_amazon_ad_campaign_all_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.campaign_id=c.campaign_id  and r.puid = ? and r.shop_id = ? and r.is_summary = 1 and r.count_day >= ? and r.count_day <= ? where c.puid= ?");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(puid);
        StringBuilder whereBuilder = buildCampaignAllWhere(param, argsList, " group by campaign_id ", false);
        countSql.append(whereBuilder);
        countSql.append(" ) r");
        Object[] args = argsList.toArray();
        return countPageResult(puid, countSql.toString(), args);
    }

    @Override
    public Page<AmazonAdCampaignDorisAllReport> listAmazonAdCampaignAllPage(Integer puid, CampaignPageParam param) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        StringBuilder selectSql = new StringBuilder(" select c.campaign_id campaign_id, ")
                .append(String.join(",", SQL_MAP.values()));
        // false 默认排序  true 需要字段排序
        boolean bool = StringUtils.isNotBlank(param.getOrderType()) && !"null".equalsIgnoreCase(param.getOrderType());
        if (bool) {
            String field = getSqlField(param.getOrderField(),param);
            if (StringUtils.isNotBlank(field)) {
                selectSql.append(",").append(field).append("  ");
            }
        } else {
            selectSql.append(",").append(getSqlField(null,param)).append("  ");
        }
        StringBuilder countSql = new StringBuilder("select count(*) from (select c.campaign_id campaign_id");
        if (param.getUseAdvanced()) {
            Set<String> strings = getSelectKey(param);
            if (CollectionUtils.isNotEmpty(strings)) {
                Set<String> fieldKey = strings.stream().filter(SQL_MAP::containsKey).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(fieldKey)) {
                    countSql.append(",");
                    countSql.append(fieldKey.stream().map(SQL_MAP::get).collect(Collectors.joining(",")));
                }
            }
        }
        selectSql.append(" from  ods_t_amazon_ad_campaign_all c left join ods_t_amazon_ad_campaign_all_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.campaign_id=c.campaign_id and r.puid = ? and r.shop_id = ? and r.is_summary = 1 and r.count_day >= ? and r.count_day <= ? where c.puid= ?");
        countSql.append(" from  ods_t_amazon_ad_campaign_all c left join ods_t_amazon_ad_campaign_all_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.campaign_id=c.campaign_id  and r.puid = ? and r.shop_id = ? and r.is_summary = 1 and r.count_day >= ? and r.count_day <= ? where c.puid= ?");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(puid);
        //支持sb分层级筛选
        StringBuilder whereBuilder = buildCampaignAllWhere(param, argsList, " group by campaign_id ", false);
        selectSql.append(whereBuilder);
        countSql.append(whereBuilder);
        countSql.append(" ) r");
        // 查看是否排序
        selectSql.append(" order by ").append(getOrderField(bool ? param.getOrderField() : null)).append(" ");
        if (StringUtils.isBlank(param.getOrderType()) || "desc".equalsIgnoreCase(param.getOrderType())) {
            selectSql.append(" desc");
        }
        selectSql.append(" ,campaign_id desc");
        Object[] args = argsList.toArray();
        return getPageResult(param.getPageNo(), param.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdCampaignDorisAllReport.class);
    }

    @Override
    public int listAmazonAdCampaignAllCountMultiple(Integer puid, CampaignPageParam param) {
        List<Object> argsList = new ArrayList<>();
        boolean bool = StringUtils.isNotBlank(param.getOrderType()) && !"null".equalsIgnoreCase(param.getOrderType());
        // 获取需要统计的报告表字段
        Set<String> reportColumnList = getReportColumnList(param, bool);
        // 关联sql
        String joinSql = joinSql(param, bool, argsList, reportColumnList,false);
        String countSql = " select count(*) from ( select c.campaign_id campaign_id from  ods_t_amazon_ad_campaign_all c "
                + joinSql + ") r";
        Object[] args = argsList.toArray();
        return countPageResult(puid, countSql, args);
    }

    @Override
    public Page<AmazonAdCampaignDorisAllReport> listAmazonAdCampaignAllPageMultiple(Integer puid, CampaignPageParam param) {
        List<Object> argsList = new ArrayList<>();
        boolean bool = StringUtils.isNotBlank(param.getOrderType()) && !"null".equalsIgnoreCase(param.getOrderType());
        StringBuilder selectSql = new StringBuilder(" select c.campaign_id campaign_id, c.shop_id shop_id from  ods_t_amazon_ad_campaign_all c ");
        // 获取需要统计的报告表字段
        Set<String> reportColumnList = getReportColumnList(param, bool);
        // 关联sql
        String joinSql = joinSql(param, bool, argsList, reportColumnList, false);
        selectSql.append(joinSql);
        String countSql = " select count(*) from ( " + selectSql + ") r";
        // 查看是否排序
        selectSql.append(" order by ").append(CampaignOrderByEnum.getOrderByByCode(bool ? param.getOrderField() : null, param.getOrderType())).append(" ");
        if (StringUtils.isBlank(param.getOrderType()) || "null".equalsIgnoreCase(param.getOrderType()) || "desc".equalsIgnoreCase(param.getOrderType())) {
            selectSql.append(" desc");
        }
        selectSql.append(" ,c.campaign_id desc");
        Object[] args = argsList.toArray();
        String sql = SqlStringUtil.exactSql(selectSql.toString(), argsList);
        logger.info("广告活动列表页sql: " + sql);
        return getPageResultByRowMapper(param.getPageNo(), param.getPageSize(), countSql, args, selectSql.toString(), args, (re, i) -> AmazonAdCampaignDorisAllReport.builder()
                .campaignId(Optional.ofNullable(re.getString("campaign_id")).orElse(""))
                .shopId(re.getInt("shop_id")).build());
    }

    /**
     * 活动列表页公共关联sql
     */
    private String joinSql(CampaignPageParam param, boolean order, List<Object> argsList, Set<String> reportColumnList,
                           boolean innerJoin) {
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        StringBuilder joinSql = new StringBuilder();
        // 是否关联销售额表
        boolean joinShopSale = isJoinShopSale(param, order);
        boolean joinShopInfo = order && CampaignOrderByEnum.SHOP_NAME.getCode().equals(param.getOrderField());
        boolean joinPortfolioInfo = order && CampaignOrderByEnum.PORTFOLIO_NAME.getCode().equals(param.getOrderField());
        // 关联报告表
        if (CollectionUtils.isNotEmpty(reportColumnList)) {
            if(!innerJoin){
                joinSql.append(" left join ( select a.* ");
            }else{
                // 汇总使用join效率高
                joinSql.append(" join ( select a.* ");
            }
            if (joinShopSale) {
                joinSql.append(" ,s.shopSalesDoris ");
            }
            joinSql.append(" from ( ");
            joinSql.append(" select any_value(r.puid) as puid,any_value(r.shop_id) as shop_id,r.campaign_id ");
            // 获取需要排序或高级筛选的报告字段
            for (String column : reportColumnList) {
                String columnByCode = CampaignColumnEnum.getColumnByCode(column, false);
                if(StringUtils.isNotEmpty(columnByCode)){
                    joinSql.append(columnByCode);
                }
            }
            joinSql.append(" from ods_t_amazon_ad_campaign_all_report r");
            joinSql.append(" where r.puid = ?  and r.is_summary = 1  and r.count_day >= ?  and r.count_day <= ? ");
            argsList.add(param.getPuid());
            argsList.add(startDate);
            argsList.add(endDate);
            joinSql.append(SqlStringUtil.dealInList(" r.shop_id ", param.getShopIdList(), argsList));
            joinSql.append("  group by r.campaign_id ");
            joinSql.append("  ) a ");
            // 关联店铺销售额表
            if (joinShopSale) {
                joinSql.append(" join ( select shop_id, ifnull(sum(sale_price), 0) shopSalesDoris from dws_sale_profit_shop_day");
                joinSql.append("  where puid = ?  and now_date >= ? and now_date <= ? ");
                argsList.add(param.getPuid());
                argsList.add(startDate);
                argsList.add(endDate);
                joinSql.append(SqlStringUtil.dealInList(" shop_id ", param.getShopIdList(), argsList));
                joinSql.append("  group by shop_id ) s on s.shop_id = a.shop_id   ");
            }
            joinSql.append(" ) r on r.puid = c.puid and r.shop_id = c.shop_id and r.campaign_id = c.campaign_id ");
        }
        // 店铺排序关联店铺表
        if (joinShopInfo) {
            joinSql.append(" join ( select puid,shop_id,name from dim_t_shop_auth where puid = ? ");
            argsList.add(param.getPuid());
            joinSql.append(SqlStringUtil.dealInList(" shop_id ", param.getShopIdList(), argsList));
            joinSql.append(") s on s.puid = c.puid and s.shop_id = c.shop_id ");
        }
        // 广告组合排序关联店铺表
        if (joinPortfolioInfo) {
            joinSql.append(" left join ( select puid,shop_id,name,portfolio_id from ods_t_amazon_ad_portfolio where puid = ? ");
            argsList.add(param.getPuid());
            joinSql.append(SqlStringUtil.dealInList(" shop_id ", param.getShopIdList(), argsList));
            joinSql.append(") portfolio on portfolio.puid = c.puid and portfolio.shop_id = c.shop_id and portfolio.portfolio_id = c.portfolio_id ");
        }
        // where 条件
        StringBuilder whereBuilder = buildCampaignAllWhereMultiple(param, argsList);
        joinSql.append(whereBuilder);
        return joinSql.toString();
    }

    /**
     * 是否关联销售额表
     */
    private boolean isJoinShopSale(CampaignPageParam param, boolean order) {
        boolean joinShopSale = false;
        if (order && ("acots".equals(param.getOrderField()) || "asots".equals(param.getOrderField()))) {
            joinShopSale = true;
        }
        if (param.getUseAdvanced() && (param.getAcotsMin() != null || param.getAcotsMax() != null
                || param.getAsotsMax() != null || param.getAsotsMin() != null)) {
            joinShopSale = true;
        }
        return joinShopSale;
    }

    /**
     * 是否关联销售额表
     */
    private Set<String> getReportColumnList(CampaignPageParam param, boolean bool) {
        Set<String> reportColumnList = new HashSet<>();
        // 获取排序字段
        if (bool) {
            Set<String> columnList = CampaignOrderByEnum.getSetByCode(param.getOrderField());
            if(CollectionUtils.isNotEmpty(columnList)){
                reportColumnList.addAll(columnList);
            }
        }
        // 获取高级筛选字段
        if (param.getUseAdvanced()) {
            reportColumnList.addAll(getSelectKey(param));
        }
        return reportColumnList;
    }

    @Override
    public List<AmazonAdCampaignDorisSumReport> listCampaignReport(CampaignPageParam param, Boolean export) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        StringBuilder selectSql = new StringBuilder(" select r.campaign_id campaign_id, ")
                .append(String.join(",", SQL_MAP.values()));
        selectSql.append(" from ods_t_amazon_ad_campaign_all_report r ");
        selectSql.append(" where puid = ? and r.count_day >= ? and r.count_day <= ? and is_summary = 1 ");
        argsList.add(param.getPuid());
        argsList.add(startDate);
        argsList.add(endDate);
        selectSql.append(SqlStringUtil.dealInList("r.shop_id", param.getShopIdList(), argsList));
        if(export){
            // 导出使用子查询
            selectSql.append(" and r.campaign_id in ( select c.campaign_id campaign_id from  ods_t_amazon_ad_campaign_all c ");
            Set<String> reportColumnList = new HashSet<>();
            if (param.getUseAdvanced()) {
                reportColumnList.addAll(getSelectKey(param));
            }
            String joinSql = joinSql(param, false, argsList, reportColumnList,true);
            selectSql.append(joinSql);
            selectSql.append(" )");
        }else{
            selectSql.append(SqlStringUtil.dealInList("r.campaign_id", param.getCampaignIdList(), argsList));
        }
        selectSql.append(" group by r.campaign_id ");
        return getJdbcTemplate().query(selectSql.toString(), new ObjectMapper<>(AmazonAdCampaignDorisSumReport.class), argsList.toArray());
    }


    @Override
    public List<AmazonAdCampaignAll> listByCampaignIds(Integer puid, Integer shopId, List<String> campaignId) {
        StringBuilder sql = new StringBuilder(" select * from ods_t_amazon_ad_campaign_all where puid = ? AND shop_id = ? ");
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        sql.append(SqlStringUtil.dealInList("campaign_id", campaignId, argsList));
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(AmazonAdCampaignAll.class), argsList.toArray());
    }

    @Override
    public List<AmazonAdCampaignAll> listByCampaignIds(Integer puid, List<Integer> shopIdList, List<String> campaignId) {
        StringBuilder sql = new StringBuilder(" select * from ods_t_amazon_ad_campaign_all where puid = ? ");
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        sql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", campaignId, argsList));
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(AmazonAdCampaignAll.class), argsList.toArray());
    }

    @Override
    public AdMetricDto getSumAdMetric(Integer puid, CampaignPageParam param) {
        List<Object> argsList = new ArrayList<>();
        Set<String> keySet = new HashSet<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        keySet.add("costDoris");
        keySet.add("totalSalesDoris");
        keySet.add("orderNumDoris");
        keySet.add("saleNumDoris");
        boolean useAdvanced = param.getUseAdvanced() != null && param.getUseAdvanced();
        if (useAdvanced) {
            Set<String> strings = getSelectKey(param);
            if (CollectionUtils.isNotEmpty(strings)) {
                Set<String> fieldKey = strings.stream().filter(SQL_MAP::containsKey).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(fieldKey)) {
                    keySet.addAll(fieldKey);
                }
            }
        }
        StringBuilder sumSql = new StringBuilder(" select ").append(useAdvanced ? " c.campaign_id campaign_id," : " ")
                .append(keySet.stream().map(SQL_MAP::get).collect(Collectors.joining(",")));
        sumSql.append(" from  ods_t_amazon_ad_campaign_all c join ods_t_amazon_ad_campaign_all_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.campaign_id=c.campaign_id and r.puid = ? and r.shop_id = ? and r.is_summary = 1 and r.count_day >= ? and r.count_day <= ? where c.puid= ?");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(puid);
        StringBuilder whereBuilder = buildCampaignAllWhere(param, argsList, useAdvanced ? " group by campaign_id " : null, true);
        sumSql.append(whereBuilder);
        List<AdMetricDto> list = getJdbcTemplate().query(sumSql.toString(), (re, i) -> AdMetricDto.builder()
                .sumCost(Optional.ofNullable(re.getBigDecimal("costDoris")).orElse(BigDecimal.ZERO))
                .sumAdSale(Optional.ofNullable(re.getBigDecimal("totalSalesDoris")).orElse(BigDecimal.ZERO))
                .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("orderNumDoris")).orElse(BigDecimal.ZERO))
                .sumOrderNum(Optional.ofNullable(re.getBigDecimal("saleNumDoris")).orElse(BigDecimal.ZERO))
                .build(), argsList.toArray());
        if (useAdvanced) {
            if (CollectionUtils.isEmpty(list)) {
                return null;
            }
            return AdMetricDto.builder()
                    .sumCost(list.stream().map(AdMetricDto::getSumCost).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .sumAdSale(list.stream().map(AdMetricDto::getSumAdSale).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .sumAdOrderNum(list.stream().map(AdMetricDto::getSumAdOrderNum).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .sumOrderNum(list.stream().map(AdMetricDto::getSumOrderNum).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .build();
        } else {
            return list.size() > 0 ? list.get(0) : null;
        }
    }

    @Override
    public AdMetricDto getSumAdMetricMultiple(CampaignPageParam param) {
        List<Object> argsList = new ArrayList<>();
        Set<String> reportColumnList = new HashSet<>();
        reportColumnList.add("costDoris");
        reportColumnList.add("totalSalesDoris");
        reportColumnList.add("orderNumDoris");
        reportColumnList.add("saleNumDoris");
        if (param.getUseAdvanced()) {
            reportColumnList.addAll(getSelectKey(param));
        }
        StringBuilder selectSql = new StringBuilder(" select sum(costDoris) as sumCost,sum(totalSalesDoris) as sumAdSale,");
        selectSql.append(" sum(orderNumDoris) as sumAdOrderNum,sum(saleNumDoris) as sumOrderNum ");
        selectSql.append(" from (select costDoris,totalSalesDoris,orderNumDoris,saleNumDoris from  ods_t_amazon_ad_campaign_all c ");
        // 关联sql
        String joinSql = joinSql(param, false, argsList, reportColumnList,true);
        selectSql.append(joinSql);
        selectSql.append(") r");
        return getJdbcTemplate().queryForObject(selectSql.toString(), new ObjectMapper<>(AdMetricDto.class), argsList.toArray());
    }

    @Override
    public AmazonAdCampaignDorisSumReport getSumReport(CampaignPageParam param, boolean selCompareDate) {
        List<Object> argsList = new ArrayList<>();
        String startDate;
        String endDate;
        //查对比数据时用对比的时间
        if (selCompareDate) {
            startDate = DateUtil.getDateSqlFormat(param.getCompareStartDate());
            endDate = DateUtil.getDateSqlFormat(param.getCompareEndDate());
        } else {
            startDate = DateUtil.getDateSqlFormat(param.getStartDate());
            endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        }
        StringBuilder selectSql = new StringBuilder(" select 1");
        Set<String> columnList = CampaignColumnEnum.getAllCode();
        for(String column : columnList){
            String code = CampaignColumnEnum.getColumnByCode(column, param.getChangeRate());
            selectSql.append(code);
        }
        selectSql.append(" from ods_t_amazon_ad_campaign_all_report r ");
        // 关联汇率表 多店铺
        if(param.getChangeRate()){
            String start = DateUtil.dateToStrWithFormat(DateUtil.strToDate(startDate, "yyyy-MM-dd"), "yyyyMM");
            String end = DateUtil.dateToStrWithFormat(DateUtil.strToDate(endDate, "yyyy-MM-dd"), "yyyyMM");
            // 关联汇率表
            selectSql.append(" join (select rate,month,`from`,puid from dim_currency_rate ");
            selectSql.append(" where puid = ? and `to` = 'USD' and month >= ? and month <= ?  ) d ");
            selectSql.append(" on d.puid = r.puid and d.month = r.count_month and  d.`from` = r.currency ");
            argsList.add(param.getPuid());
            argsList.add(start);
            argsList.add(end);
        }
        selectSql.append(" where r.puid= ? and r.is_summary=1 and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(param.getPuid());
        argsList.add(startDate);
        argsList.add(endDate);
        selectSql.append(SqlStringUtil.dealInList(" r.shop_id ", param.getShopIdList(), argsList));
        selectSql.append(" and r.campaign_id in ( select c.campaign_id campaign_id from  ods_t_amazon_ad_campaign_all c ");
        Set<String> reportColumnList = new HashSet<>();
        if (param.getUseAdvanced()) {
            reportColumnList.addAll(getSelectKey(param));
        }
        //查对比数据时并且使用高级筛选时，需要使用left join，才能把列表页展示的id都查出来(不使用高级筛选时子表只查基础表，过滤出的id就是列表页的id)，然后查询这些id的报告数据汇总起来
        //如果采用join的话查出的对比数据只有当前时间有报告数据的id，会较少对不上
        boolean isInnerJoin = !(selCompareDate && param.getUseAdvanced());
        String joinSql = joinSql(param, false, argsList, reportColumnList, isInnerJoin);
        selectSql.append(joinSql);
        selectSql.append(" )");
        return getJdbcTemplate().queryForObject(selectSql.toString(), new ObjectMapper<>(AmazonAdCampaignDorisSumReport.class), argsList.toArray());
    }

    @Override
    public Integer countReport(CampaignPageParam param) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        StringBuilder selectSql = new StringBuilder("  ");
        selectSql.append(" select count(*) from ods_t_amazon_ad_campaign_all_report r ");
        selectSql.append(" where r.puid= ? and r.is_summary=1 and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(param.getPuid());
        argsList.add(startDate);
        argsList.add(endDate);
        selectSql.append(SqlStringUtil.dealInList(" r.shop_id ", param.getShopIdList(), argsList));
        selectSql.append(" and r.campaign_id in ( select c.campaign_id campaign_id from  ods_t_amazon_ad_campaign_all c ");
        Set<String> reportColumnList = new HashSet<>();
        if (param.getUseAdvanced()) {
            reportColumnList.addAll(getSelectKey(param));
        }
        String joinSql = joinSql(param, false, argsList, reportColumnList,true);
        selectSql.append(joinSql);
        selectSql.append(" )");
        return getJdbcTemplate().queryForObject(selectSql.toString(), Integer.class, argsList.toArray());
    }

    @Override
    public BigDecimal getSumDailyBudget(CampaignPageParam param) {
        if (param.getStatus() == null || !"enabled".equalsIgnoreCase(param.getStatus())) {
            return null;
        }
        List<Object> argsList = new ArrayList<>();
        Set<String> reportColumnList = new HashSet<>();
        if (param.getUseAdvanced()) {
            reportColumnList.addAll(getSelectKey(param));
        }
        StringBuilder selectSql = new StringBuilder(" select ");
        if(param.getChangeRate()){
            selectSql.append(" ifnull(sum(r.budget * d.rate),0) dailyBudget ");
        }else{
            selectSql.append(" ifnull(sum(r.budget) ,0) dailyBudget ");
        }
        selectSql.append(" from (select c.budget,c.marketplace_id,c.puid ");
        selectSql.append(" from  ods_t_amazon_ad_campaign_all c ");
        // 关联sql
        String joinSql = joinSql(param, false, argsList, reportColumnList,false);
        selectSql.append(joinSql);
        selectSql.append(") r");
        if(param.getChangeRate()){
            // 多店铺跨币种关联汇率表转美元
            selectSql.append(" join ( select m.marketplace_id, c.puid, c.rate ");
            selectSql.append(" from dim_currency_rate c ");
            selectSql.append(" join dim_marketplace_info m on c.`from` = m.currency ");
            selectSql.append(" and c.puid = ? and `to` = 'USD' and month = ? ");
            argsList.add(param.getPuid());
            argsList.add(LocalDateTimeUtil.formatDate(LocalDate.now(), "yyyyMM"));
            selectSql.append(" )d on r.puid = d.puid and r.marketplace_id = d.marketplace_id ");
        }
        return getJdbcTemplate().queryForObject(selectSql.toString(), BigDecimal.class, argsList.toArray());
    }

    @Override
    public List<String> getCampaginIdList(CampaignPageParam param) {
        List<Object> argsList = new ArrayList<>();
        Set<String> reportColumnList = new HashSet<>();
        if (param.getUseAdvanced()) {
            reportColumnList.addAll(getSelectKey(param));
        }
        StringBuilder selectSql = new StringBuilder(" select c.campaign_id campaign_id ");
        selectSql.append(" from  ods_t_amazon_ad_campaign_all c ");
        // 关联sql
        String joinSql = joinSql(param, false, argsList, reportColumnList,true);
        selectSql.append(joinSql);
        return getJdbcTemplate().query(selectSql.toString(), (rs, rowNum) -> rs.getString("campaign_id"),argsList.toArray());
    }

    @Override
    public List<AdHomePerformancedto> listTotalAmazonAdCampaignAllGroupDateById(Integer puid, CampaignPageParam param, List<String> campaignIdList) {
        if (CollectionUtils.isEmpty(campaignIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select  count_date ,sum(`cost`) cost,sum(if (type = 'sp', sales7d,sales14d)) total_sales,")
                .append("sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (type = 'sp' , conversions7d,conversions14d)) order_num, sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_order_num,")
                .append("sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) sale_num,sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_sale_num, ")
                .append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d ,sum(view_impressions) view_impressions ");
        sql.append(" from ");
        sql.append(" ods_t_amazon_ad_campaign_all_report ");
        sql.append(" where puid= ? and shop_id= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (campaignIdList.size() > 10000) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", campaignIdList, argsList));
        } else {
            sql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
        }
        sql.append(" and is_summary=1 and count_day >= ? and count_day <= ? group by count_date ");
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        argsList.add(startDate);
        argsList.add(endDate);
        return getJdbcTemplate().query(sql.toString(), (re, i) -> AdHomePerformancedto.builder()
                .campaignId("")
                .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                //广告订单量
                .adOrderNum(Optional.of(re.getInt("order_num")).orElse(0))  //销量字段订单
                //本广告产品订单量
                .adSaleNum(Optional.of(re.getInt("ad_order_num")).orElse(0))
                //广告销售额
                .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                //本广告产品销售额
                .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                //广告销量
                .salesNum(Optional.of(re.getInt("sale_num")).orElse(0))
                //本广告产品销量
                .orderNum(Optional.of(re.getInt("ad_sale_num")).orElse(0))
                //“品牌新买家”销售额
                .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                //“品牌新买家”订单量
                .ordersNewToBrand14d(Optional.of(re.getInt("orders_new_to_brand14d")).orElse(0))
                //“品牌新买家”销量
                .unitsOrderedNewToBrand14d(Optional.of(re.getInt("units_ordered_new_to_brand14d")).orElse(0))
                //可见展示次数
                .viewImpressions(Optional.of(re.getInt("view_impressions")).orElse(0))
                .clicks(Optional.of(re.getInt("clicks")).orElse(0))
                .impressions(Optional.of(re.getInt("impressions")).orElse(0))
                .countDate(re.getString("count_date"))
                .type("")
                .build(), argsList.toArray());
    }

    @Override
    public List<AdHomePerformanceNewDto> listTotalGroupDateByIdMultiple(Integer puid, CampaignPageParam param) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        //按天聚合
        StringBuilder sql = new StringBuilder(" select count_day countDate,")
                .append("sum(`impressions`) impressions,")
                .append("sum(`clicks`) clicks,")
                .append("sum(if (type = 'sp' , conversions7d,conversions14d)) adOrderNum,")
                .append("sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) adSaleNum,")
                .append("sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) salesNum,")
                .append("sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) orderNum, ")
                .append("sum(orders_new_to_brand14d) ordersNewToBrand14d, ")
                .append("sum(units_ordered_new_to_brand14d) unitsOrderedNewToBrand14d, ")
                .append("sum(view_impressions) viewImpressions, ");
        if(param.getChangeRate()){
            sql.append("sum(`cost` * d.rate) adCost,")
                    .append("sum(if (type = 'sp', sales7d ,sales14d) * d.rate) adSale,")
                    .append("sum(if (type = 'sp', sales7d_same_sku ,sales14d_same_sku)* d.rate) adSales,")
                    .append("sum(`sales_new_to_brand14d`* d.rate) salesNewToBrand14d  ");
        }else{
            sql.append("sum(`cost`) adCost,")
                    .append("sum(if (type = 'sp', sales7d,sales14d)) adSale,")
                    .append("sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)) adSales,")
                    .append("sum(`sales_new_to_brand14d`) salesNewToBrand14d ");
        }
        sql.append(" from ods_t_amazon_ad_campaign_all_report r");
        if(param.getChangeRate()){
            String start = DateUtil.dateToStrWithFormat(DateUtil.strToDate(startDate, "yyyy-MM-dd"), "yyyyMM");
            String end = DateUtil.dateToStrWithFormat(DateUtil.strToDate(endDate, "yyyy-MM-dd"), "yyyyMM");
            // 关联汇率表
            sql.append(" join (select rate,month,`from`,puid from dim_currency_rate ");
            sql.append(" where puid = ? and `to` = 'USD' and month >= ? and month <= ?  ) d ");
            sql.append(" on d.puid = r.puid and d.month = r.count_month and  d.`from` = r.currency ");
            argsList.add(param.getPuid());
            argsList.add(start);
            argsList.add(end);
        }
        sql.append(" where r.puid= ? and r.is_summary=1 and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(puid);
        argsList.add(startDate);
        argsList.add(endDate);
        sql.append(SqlStringUtil.dealInList(" r.shop_id ", param.getShopIdList(), argsList));
        sql.append(" and r.campaign_id in ( select c.campaign_id campaign_id from  ods_t_amazon_ad_campaign_all c ");
        Set<String> reportColumnList = new HashSet<>();
        if (param.getUseAdvanced()) {
            reportColumnList.addAll(getSelectKey(param));
        }
        String joinSql = joinSql(param, false, argsList, reportColumnList,true);
        sql.append(joinSql);
        sql.append(" )");
        sql.append(" group by r.count_day ");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(AdHomePerformanceNewDto.class), argsList.toArray());
    }

    @Override
    public List<AdHomePerformancedto> listTotalAmazonAdCampaignAllGroupCampaignId(Integer puid, CampaignPageParam param) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        StringBuilder selectSql = new StringBuilder(" select c.campaign_id campaign_id, any(c.type) type, ")
                .append(String.join(",", SQL_MAP.values()));
        selectSql.append(" from ods_t_amazon_ad_campaign_all c join ods_t_amazon_ad_campaign_all_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.campaign_id=c.campaign_id and r.puid = ? and r.shop_id = ? and r.is_summary = 1 and r.count_day >= ? and r.count_day <= ? where c.puid= ?");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(puid);
        StringBuilder whereBuilder = buildCampaignAllWhere(param, argsList, " group by campaign_id ", true);
        selectSql.append(whereBuilder);
        return getJdbcTemplate().query(selectSql.toString(), (re, i) -> AdHomePerformancedto.builder()
                .campaignId(re.getString("campaign_id"))
                .adCost(Optional.ofNullable(re.getBigDecimal("costDoris")).orElse(BigDecimal.ZERO))
                //广告订单量
                .adOrderNum(Optional.of(re.getInt("orderNumDoris")).orElse(0))  //销量字段订单
                //本广告产品订单量
                .adSaleNum(Optional.of(re.getInt("adOrderNumDoris")).orElse(0))
                //广告销售额
                .adSale(Optional.ofNullable(re.getBigDecimal("totalSalesDoris")).orElse(BigDecimal.ZERO))
                //本广告产品销售额
                .adSales(Optional.ofNullable(re.getBigDecimal("adSalesDoris")).orElse(BigDecimal.ZERO))
                //广告销量
                .salesNum(Optional.of(re.getInt("saleNumDoris")).orElse(0))
                //本广告产品销量
                .orderNum(Optional.of(re.getInt("adSaleNumDoris")).orElse(0))
                //“品牌新买家”销售额
                .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("salesNewToBrand14dDoris")).orElse(BigDecimal.ZERO))
                //“品牌新买家”订单量
                .ordersNewToBrand14d(Optional.of(re.getInt("ordersNewToBrand14dDoris")).orElse(0))
                //“品牌新买家”销量
                .unitsOrderedNewToBrand14d(Optional.of(re.getInt("unitsOrderedNewToBrand14dDoris")).orElse(0))
                //可见展示次数
                .viewImpressions(Optional.of(re.getInt("viewImpressionsDoris")).orElse(0))
                .clicks(Optional.of(re.getInt("clicksDoris")).orElse(0))
                .impressions(Optional.of(re.getInt("impressionsDoris")).orElse(0))
                .countDate(null)
                .type(re.getString("type"))
                .newToBrandDetailPageViews(Optional.of(re.getInt("newToBrandDetailPageViewsDoris")).orElse(0))
                .addToCart(Optional.of(re.getInt("addToCartDoris")).orElse(0))
                .video5SecondViews(Optional.of(re.getInt("video5secondViewsDoris")).orElse(0))
                .videoFirstQuartileViews(Optional.of(re.getInt("videoFirstQuartileViewsDoris")).orElse(0))
                .videoMidpointViews(Optional.of(re.getInt("videoMidpointViewsDoris")).orElse(0))
                .videoThirdQuartileViews(Optional.of(re.getInt("videoThirdQuartileViewsDoris")).orElse(0))
                .videoCompleteViews(Optional.of(re.getInt("videoCompleteViewsDoris")).orElse(0))
                .videoUnmutes(Optional.of(re.getInt("videoUnmutesDoris")).orElse(0))
                .viewableImpressions(Optional.of(re.getInt("viewImpressionsDoris")).orElse(0))
                .brandedSearches(Optional.of(re.getInt("brandedSearches14dDoris")).orElse(0))
                .detailPageViews(Optional.of(re.getInt("detailPageView14dDoris")).orElse(0))
                .cumulativeReach(re.getInt("cumulativeReachDoris"))
                .impressionsFrequencyAverage(Optional.ofNullable(re.getBigDecimal("impressionsFrequencyAverageDoris")).orElse(BigDecimal.ZERO))
                .build(), argsList.toArray());
    }

    @Override
    public List<AdHomePerformancedto> getReportByCampaignIdsAndDate(Integer puid, Integer shopId, String startDate, String endDate, List<String> allCampaignId) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select sum(`cost`) cost, sum(if (r.type = 'sp', sales7d,sales14d)) total_sales,")
                .append("sum(if (r.type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (r.type = 'sp' , conversions7d,conversions14d)) order_num, sum(if (r.type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_order_num,")
                .append("sum(if (r.type = 'sp' ,`units_ordered7d`,if (r.type = 'sb' ,`units_sold14d`,`units_ordered14d`))) sale_num,sum(if (r.type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_sale_num, ")
                .append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d, sum(view_impressions) view_impressions from ods_t_amazon_ad_campaign_all_report ");
        sql.append(" r where r.puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (shopId != null) {
            whereSql.append(" and r.shop_id = ? ");
            argsList.add(shopId);
        }

        whereSql.append(SqlStringUtil.dealBitMapDorisInList("r.campaign_id", allCampaignId, argsList));

        whereSql.append(" and is_summary=1 and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN_YYYYMMDD, DateUtil.PATTERN));
        argsList.add(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN_YYYYMMDD, DateUtil.PATTERN));

        sql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate().query(sql.toString(), (re, i) -> {
                AdHomePerformancedto dto = AdHomePerformancedto.builder()
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .orderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  // 广告销量
                        .adOrderNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))  // 广告订单
                        .salesNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                        .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                        .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                        .viewImpressions(Optional.ofNullable(re.getInt("view_impressions")).orElse(0))
                        .unitsOrderedNewToBrand14d(Optional.ofNullable(re.getInt("units_ordered_new_to_brand14d")).orElse(0))
                        .build();
                return dto;
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdHomePerformancedto> getAllDayReportByAllCampaignIdList(Integer puid, Integer shopId, String startDate, String endDate, List<String> allCampaignId) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select count_date, sum(`cost`) cost, sum(if (r.type = 'sp', sales7d,sales14d)) total_sales,")
                .append("sum(if (r.type = 'sp', sales7d_same_sku,sales14d_same_sku)) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(if (r.type = 'sp' , conversions7d,conversions14d)) order_num, sum(if (r.type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)) ad_order_num,")
                .append("sum(if (r.type = 'sp' ,`units_ordered7d`,if (r.type = 'sb' ,`units_sold14d`,`units_ordered14d`))) sale_num,sum(if (r.type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)) ad_sale_num, ")
                .append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(orders_new_to_brand14d) orders_new_to_brand14d,sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d, sum(view_impressions) view_impressions ");
        sql.append(" from ods_t_amazon_ad_campaign_all_report ");
        sql.append(" r where r.puid= ? and shop_id= ?");

        argsList.add(puid);
        argsList.add(shopId);

        sql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", allCampaignId, argsList));

        sql.append(" and is_summary=1 and count_day >= ? and count_day <= ? group by count_date ");
        argsList.add(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN_YYYYMMDD, DateUtil.PATTERN));
        argsList.add(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN_YYYYMMDD, DateUtil.PATTERN));


        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate().query(sql.toString(), (re, i) -> {
                AdHomePerformancedto dto = AdHomePerformancedto.builder()
                        .countDate(re.getString("count_date"))
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .orderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                        .adOrderNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))  //销量字段订单
                        .salesNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                        .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                        .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                        .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                        .viewImpressions(Optional.ofNullable(re.getInt("view_impressions")).orElse(0))
                        .unitsOrderedNewToBrand14d(Optional.ofNullable(re.getInt("units_ordered_new_to_brand14d")).orElse(0))
                        .build();
                return dto;
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public Page<AmazonAdPortfolioDorisAllReport> getSumReportByAllCampaignIds(Integer puid, PortfolioPageParam param, List<String> portfolioIds, boolean queryAll) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder countSql = new StringBuilder("select count(*) count from (select count(*) from ods_t_amazon_ad_campaign_all_report r ");
        StringBuilder selectSql = new StringBuilder(" select t.portfolio_id portfolio_id,count(distinct t.campaign_id) campaign_count,any(t.last_updated_date) last_updated_date,")
                .append(String.join(",", SQL_PORTFOLIO_MAP.values()));
        // false 默认排序  true 需要字段排序
        boolean bool = StringUtils.isNotBlank(param.getOrderType()) && !"null".equalsIgnoreCase(param.getOrderType());
        // queryAll为true时不排序也不分页
        if (!queryAll) {
            if (bool) {
                String field = getPortfolioSqlField(param.getOrderField());
                if (StringUtils.isNotBlank(field)) {
                    selectSql.append(",").append(field).append("  ");
                }
            }
        }
        selectSql.append(" FROM ods_t_amazon_ad_campaign_all_report r ");
        StringBuilder joinSql = new StringBuilder();
        joinSql.append("right join (select p.rank,p.last_updated_date,p.portfolio_id,c.campaign_id,p.puid,p.shop_id,p.marketplace_id from ods_t_amazon_ad_portfolio p left join ods_t_amazon_ad_campaign_all c on ");
        joinSql.append("p.portfolio_id = c.portfolio_id and p.shop_id = c.shop_id and p.puid = c.puid where ");
        joinSql.append("p.puid=? and p.shop_id=? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        joinSql.append(SqlStringUtil.dealBitMapDorisInList("p.portfolio_id", portfolioIds, argsList));

        joinSql.append(") t on t.campaign_id = r.campaign_id and t.puid=r.puid and t.shop_id=r.shop_id and t.marketplace_id=r.marketplace_id ");
        joinSql.append("and count_day>=? and count_day<=? and is_summary = 1 ");

        argsList.add(DateUtil.dateStringFormat(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD, DateUtil.PATTERN));
        argsList.add(DateUtil.dateStringFormat(param.getEndDate(), DateUtil.PATTERN_YYYYMMDD, DateUtil.PATTERN));

        joinSql.append("group by t.portfolio_id ");

        selectSql.append(joinSql);
        countSql.append(joinSql).append(") c ");

        if (!queryAll) {
            String orderField = bool ? param.getOrderField() : null;
            selectSql.append(" order by ").append(getPortfolioOrderField(orderField)).append(" ");
            if (StringUtils.isNotBlank(orderField) && (StringUtils.isBlank(param.getOrderType()) || "desc".equalsIgnoreCase(param.getOrderType()))) {
                selectSql.append(" desc");
            }
            if (StringUtils.isNotBlank(orderField)) {
                selectSql.append(", last_updated_date desc ");
            }
            Object[] args = argsList.toArray();
            return getPageResultByClass(param.getPageNo(), param.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdPortfolioDorisAllReport.class);
        } else {
            Page<AmazonAdPortfolioDorisAllReport> page = new Page<>();
            page.setRows(getJdbcTemplate().query(selectSql.toString(), new ObjectMapper<>(AmazonAdPortfolioDorisAllReport.class), argsList.toArray()));
            return page;
        }
    }

    @Override
    public AmazonAdCampaignDorisAllReport getSumReport(Integer puid, PortfolioPageParam param, List<String> portfolioIds) {
        if (CollectionUtils.isEmpty(portfolioIds)) {
            return new AmazonAdCampaignDorisAllReport();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder(" select IFNULL(sum(r.cost),0) `costDoris`,");
        selectSql.append("IFNULL(sum(if (r.type = 'sp', sales7d,sales14d)),0) totalSalesDoris,");
        selectSql.append("IFNULL(sum(if (r.type = 'sp' ,`units_ordered7d`,if (r.type = 'sb' ,`units_sold14d`,`units_ordered14d`))),0) orderNumDoris,");
        selectSql.append("IFNULL(sum(if (r.type = 'sp' , conversions7d,conversions14d)),0) saleNumDoris ");
        selectSql.append("from ods_t_amazon_ad_campaign_all_report r ");
        StringBuilder joinSql = new StringBuilder("join ods_t_amazon_ad_campaign_all c on c.campaign_id = r.campaign_id and c.puid=r.puid and c.shop_id=r.shop_id and c.marketplace_id=r.marketplace_id ");
        joinSql.append(SqlStringUtil.dealBitMapDorisInList("c.portfolio_id", portfolioIds, argsList));
        joinSql.append(" and r.puid=? and r.shop_id=? and count_day>=? and count_day<=? and is_summary = 1 ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(DateUtil.dateStringFormat(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD, DateUtil.PATTERN));
        argsList.add(DateUtil.dateStringFormat(param.getEndDate(), DateUtil.PATTERN_YYYYMMDD, DateUtil.PATTERN));

        selectSql.append(joinSql);

        List<AmazonAdCampaignDorisAllReport> allReports = getJdbcTemplate().query(selectSql.toString(), (re, i) -> {
            AmazonAdCampaignDorisAllReport allReport = new AmazonAdCampaignDorisAllReport();
            allReport.setCost(Optional.ofNullable(re.getBigDecimal("costDoris")).orElse(BigDecimal.ZERO));
            allReport.setTotalSales(Optional.ofNullable(re.getBigDecimal("totalSalesDoris")).orElse(BigDecimal.ZERO));
            allReport.setOrderNum(Optional.ofNullable(re.getInt("orderNumDoris")).orElse(0));
            allReport.setSaleNum(Optional.ofNullable(re.getInt("saleNumDoris")).orElse(0));
            return allReport;
        }, argsList.toArray());
        return CollectionUtils.isEmpty(allReports) ? new AmazonAdPortfolioDorisAllReport() : allReports.get(0);
    }


    private String getSqlField(String field, CampaignPageParam param) {
        if (StringUtils.isBlank(field)) {
            return " any(c.create_time) dataUpdateTime ";
        }
        switch (field) {
            case "name":
                return " any(c.name) nameDoris ";
            case "dailyBudget":
                return " any(c.budget) budgetDoris";
            case "startDate":
                return " any(c.start_date) startDateDoris";
            case "endDate":
                return " any(c.end_date) endDateDoris";
            case "topImpressionShare":
                return " max(top_of_search_is) maxTopOfSearch ";
            case "adCostPerClick":
                return " ifnull(sum(r.cost)/sum(`clicks`),0) as adCostPerClickDoris ";
            case "vcpm":
                //return " ifnull((sum(r.cost)/sum(if (r.type = 'sb', `viewable_impressions`, `view_impressions`))) * 1000, 0) as vcpmDoris ";
                if (StringUtils.isBlank(param.getOrderType()) || "desc".equalsIgnoreCase(param.getOrderType())) {
                    return " if(any(c.cost_type) = 'vcpm',ifnull((sum(r.cost)/ sum(if (r.type = 'sb', `viewable_impressions`, `view_impressions`))) * 1000, 0),-2147483647) as vcpmDoris ";
                }else{
                    return " if(any(c.cost_type) = 'vcpm',ifnull((sum(r.cost)/ sum(if (r.type = 'sb', `viewable_impressions`, `view_impressions`))) * 1000, 0),2147483647) as vcpmDoris ";
                }
            case "ctr":
                return " ifnull(sum(`clicks`)/sum(`impressions`),0) as ctrDoris ";
            case "cvr":
                return " ifnull(sum(if (r.type = 'sp' , conversions7d, conversions14d))/sum(`clicks`),0) as cvrDoris ";
            case "cpc":
                return " ifnull(sum(r.cost)/sum(`clicks`),0) as cpcDoris ";
            case "acos":
                return " ifnull(sum(r.cost)/sum(if (r.type = 'sp', sales7d, sales14d)),0) as acosDoris ";
            case "roas":
                return " ifnull(sum(if (r.type = 'sp', sales7d, sales14d))/sum(r.cost),0) as roasDoris ";
            case "cpa":
                return " ifnull(sum(r.cost)/sum(if (r.type = 'sp' , conversions7d,conversions14d)),0) as cpaDoris ";
            //其他产品广告订单量
            case "adOtherOrderNum":
                return " ifnull(sum(if (r.type = 'sp' , conversions7d, conversions14d))-sum(if (r.type = 'sp' , `conversions7d_same_sku`, `conversions14d_same_sku`)),0) as adOtherOrderNumDoris ";
            //其他产品广告销售额
            case "adOtherSales":
                return "ifnull(sum(if (r.type = 'sp', sales7d, sales14d))-sum(if (r.type = 'sp', sales7d_same_sku, sales14d_same_sku)),0) as adOtherSalesDoris ";
            //广告销量
            case "adOtherSaleNum":
                return " ifnull(sum(if (r.type = 'sp',units_ordered7d - units_ordered7d_same_sku,0)),0) as adOtherSaleNumDoris ";
            case "orderRateNewToBrandFTD":
                return " ifnull(sum(orders_new_to_brand14d)/sum(if (r.type = 'sp' , conversions7d, conversions14d)),0) as orderRateNewToBrandFTDDoris ";
            case "salesRateNewToBrandFTD":
                return " ifnull((sum(`sales_new_to_brand14d`)/sum(if (r.type = 'sp', sales7d, sales14d))) * 100, 0) as salesRateNewToBrandFTDDoris ";
            case "unitsOrderedRateNewToBrandFTD":
                return " ROUND(ROUND(ifnull((sum(units_ordered_new_to_brand14d)/sum(if (r.type = 'sp' , `units_ordered7d`, if (r.type = 'sb' , `units_sold14d`, `units_ordered14d`)))) * 100, 0), 4), 2)  as unitsOrderedRateNewToBrandFTDDoris ";
            case "addToCartRate":
                return " ifnull(sum(`add_to_cart`)/sum(`impressions`),0)  as addToCartRateDoris ";
            case "eCPAddToCart":
                return " ifnull(sum(r.cost)/sum(`add_to_cart`),0) as eCPAddToCartDoris ";
            case "video5SecondViewRate":
                return " ifnull(sum(`video5second_views`)/sum(`impressions`),0) as video5SecondViewRateDoris ";
            case "viewabilityRate":
                return " ifnull(sum(if (r.type = 'sb', `viewable_impressions`, `view_impressions`))/sum(`impressions`), 0) as viewabilityRateDoris ";
            case "advertisingUnitPrice":
                return " ifnull(sum(if (r.type = 'sp', sales7d,sales14d)) / sum(if (r.type = 'sp' , conversions7d,conversions14d)), 0) as advertisingUnitPriceDoris ";
            case "viewClickThroughRate":
                return " ifnull(sum(`clicks`) / sum(if (r.type = 'sb', `viewable_impressions`, `view_impressions`)), 0) as viewClickThroughRateDoris ";
            default:
                return " ";
        }
    }

    private String getPortfolioSqlField(String field) {
        if (StringUtils.isBlank(field)) {
            return " ";
        }
        switch (field) {
            case "ctr":
                return " ifnull(sum(`clicks`)/sum(`impressions`),0) as ctrDoris ";
            case "cvr":
                return " ifnull(sum(if (r.type = 'sp' , conversions7d, conversions14d))/sum(`clicks`),0) as cvrDoris ";
            case "adCostPerClick":
                return " ifnull(sum(r.cost)/sum(`clicks`),0) as adCostPerClickDoris ";
            case "acos":
                return " ifnull(sum(r.cost)/sum(if (r.type = 'sp', sales7d, sales14d)),0) as acosDoris ";
            case "roas":
                return " ifnull(sum(if (r.type = 'sp', sales7d, sales14d))/sum(r.cost),0) as roasDoris ";
            case "cpa":
                return " ifnull(sum(r.cost)/sum(if (r.type = 'sp' , conversions7d,conversions14d)),0) as cpaDoris ";
            case "advertisingUnitPrice":
                return " ifnull(sum(if (r.type = 'sp', sales7d, sales14d)) / sum(if (r.type = 'sp' , conversions7d, conversions14d)), 0) as advertisingUnitPriceDoris ";
            default:
                return " ";
        }
    }

    private String getOrderField(String field) {
        if (StringUtils.isBlank(field)) {
            return " dataUpdateTime ";
        }
        switch (field) {
            case "dailyBudget":
                return " budgetDoris ";
            case "adCost":
            case "adCostPercentage":
            case "acots":
                return " costDoris ";
            case "topImpressionShare":
                return " maxTopOfSearch ";
            case "adSale":
            case "adSalePercentage":
            case "asots":
                return " totalSalesDoris ";
            // 广告订单量
            case "adOrderNum":
                return " orderNumDoris ";
            case "adOrderNumPercentage":
                return " orderNumDoris ";
            //本广告产品订单量
            case "adSaleNum":
                return " adOrderNumDoris ";
            //广告销量
            case "saleNum":
                return " orderNumDoris ";
            case "orderNum":
            case "orderNumPercentage":
                return " saleNumDoris ";
            //本广告产品销量
            case "adSelfSaleNum":
                return " adSaleNumDoris ";
            //其他产品广告销量
            case "ordersNewToBrandFTD":
                return " ordersNewToBrand14dDoris ";
            case "salesNewToBrandFTD":
                return " salesNewToBrand14dDoris ";
            case "unitsOrderedNewToBrandFTD":
                return " unitsOrderedNewToBrand14dDoris ";
            case "brandedSearches":
                return " brandedSearches14dDoris ";
            case "detailPageViews":
                return " detailPageView14dDoris ";
            default:
                return " " + field + "Doris ";
        }
    }

    private String getOrderFieldMultiple(String field) {
        if (StringUtils.isBlank(field)) {
            return " dataUpdateTime ";
        }
        switch (field) {
            case "name":
                return " c.name ";
            case "dailyBudget":
                return " c.budget ";
            case "startDate":
                return " c.start_date ";
            case "endDate":
                return " c.end_date ";
            case "topImpressionShare":
                return " maxTopIsDoris ";
            case "adCost":
            case "adCostPercentage":
                return " costDoris ";
            case "acots":
                return " acotsDoris ";
            case "adSale":
            case "adSalePercentage":
                return " totalSalesDoris ";
            case "asots":
                return " asotsDoris ";
            // 广告订单量
            case "adOrderNum":
                return " orderNumDoris ";
            case "adOrderNumPercentage":
                return " orderNumDoris ";
            //本广告产品订单量
            case "adSaleNum":
                return " adOrderNumDoris ";
            //广告销量
            case "saleNum":
                return " orderNumDoris ";
            case "orderNum":
            case "orderNumPercentage":
                return " saleNumDoris ";
            //本广告产品销量
            case "adSelfSaleNum":
                return " adSaleNumDoris ";
            //其他产品广告销量
            case "ordersNewToBrandFTD":
                return " ordersNewToBrand14dDoris ";
            case "salesNewToBrandFTD":
                return " salesNewToBrand14dDoris ";
            case "unitsOrderedNewToBrandFTD":
                return " unitsOrderedNewToBrand14dDoris ";
            case "brandedSearches":
                return " brandedSearches14dDoris ";
            case "detailPageViews":
                return " detailPageView14dDoris ";
            default:
                return " " + field + "Doris ";
        }
    }

    private String getPortfolioOrderField(String field) {
        if (StringUtils.isBlank(field)) {
            return " last_updated_date desc ";
        }

        switch (field) {
            case "adCost":
            case "adCostPercentage":
            case "acots":
                return " costDoris ";
            case "adSale":
            case "adSalePercentage":
            case "asots":
                return " totalSalesDoris ";
            // 广告订单量
            case "adOrderNum":
                return " saleNumDoris ";
            case "adOrderNumPercentage":
                return " saleNumDoris ";
            //本广告产品订单量
            case "adSaleNum":
                return " adSaleNumDoris ";
            //广告销量
            case "saleNum":
                return " salesNumDoris ";
            case "orderNum":
            case "orderNumPercentage":
                return " orderNumDoris ";
            default:
                return " " + field + "Doris ";
        }
    }

    private static final Map<String, String> SQL_PORTFOLIO_MAP = Collections.unmodifiableMap(new HashMap<String, String>() {
        {
            put("costDoris", "IFNULL(sum(r.cost),0) `costDoris`");
            put("totalSalesDoris", "IFNULL(sum(if (type = 'sp', sales7d,sales14d)),0) totalSalesDoris");
            put("adSalesDoris", "IFNULL(sum(if (type = 'sp', sales7d_same_sku,sales14d_same_sku)),0) adSalesDoris");
            put("impressionsDoris", "IFNULL(sum(`impressions`),0) impressionsDoris");
            put("clicksDoris", "IFNULL(sum(`clicks`),0) clicksDoris");
            put("orderNumDoris", "IFNULL(sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))),0) orderNumDoris");
            put("adOrderNumDoris", "IFNULL(sum(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)),0) adOrderNumDoris");
            put("saleNumDoris", "IFNULL(sum(if (type = 'sp' , conversions7d,conversions14d)),0) saleNumDoris");
            put("adSaleNumDoris", "IFNULL(sum(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)),0) adSaleNumDoris");
            put("salesNewToBrand14dDoris", "IFNULL(sum(`sales_new_to_brand14d`),0) salesNewToBrand14dDoris");
            put("ordersNewToBrand14dDoris", "IFNULL(sum(orders_new_to_brand14d),0) ordersNewToBrand14dDoris");
            put("unitsOrderedNewToBrand14dDoris", "IFNULL(sum(units_ordered_new_to_brand14d),0) unitsOrderedNewToBrand14dDoris");
            put("viewImpressionsDoris", "IFNULL(sum(if (r.type = 'sb', `viewable_impressions`,`view_impressions`)),0) viewImpressionsDoris");
        }
    });

    private static final Map<String, String> SQL_MAP = Collections.unmodifiableMap(new HashMap<String, String>() {
        {
            put("costDoris", "IFNULL(sum(r.cost),0) `costDoris`");
            put("totalSalesDoris", "IFNULL(sum(if (r.type = 'sp', sales7d,sales14d)),0) totalSalesDoris");
            put("adSalesDoris", "IFNULL(sum(if (r.type = 'sp', sales7d_same_sku,sales14d_same_sku)),0) adSalesDoris");
            put("impressionsDoris", "IFNULL(sum(`impressions`),0) impressionsDoris");
            put("clicksDoris", "IFNULL(sum(`clicks`),0) clicksDoris");

            put("orderNumDoris", "IFNULL(sum(if (r.type = 'sp' , conversions7d,conversions14d)),0) orderNumDoris");
            put("adOrderNumDoris", "IFNULL(sum(if (r.type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)),0) adOrderNumDoris");
            put("saleNumDoris", "IFNULL(sum(if (r.type = 'sp' ,`units_ordered7d`,if (r.type = 'sb' ,`units_sold14d`,`units_ordered14d`))),0) saleNumDoris");
            put("adSaleNumDoris", "IFNULL(sum(if (r.type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)),0) adSaleNumDoris");

            put("viewImpressionsDoris", "IFNULL(sum(if (r.type = 'sb', `viewable_impressions`,`view_impressions`)),0) viewImpressionsDoris");
            put("salesNewToBrand14dDoris", "IFNULL(sum(`sales_new_to_brand14d`),0) salesNewToBrand14dDoris");
            put("ordersNewToBrand14dDoris", "IFNULL(sum(orders_new_to_brand14d),0) ordersNewToBrand14dDoris");
            put("unitsOrderedNewToBrand14dDoris", "IFNULL(sum(units_ordered_new_to_brand14d),0) unitsOrderedNewToBrand14dDoris");
            put("newToBrandDetailPageViewsDoris", "IFNULL(sum(`new_to_brand_detail_page_views`),0) AS `newToBrandDetailPageViewsDoris`");
            put("addToCartDoris", "IFNULL(sum(`add_to_cart`),0) AS `addToCartDoris`");
            put("video5secondViewsDoris", "IFNULL(sum(`video5second_views`),0) AS `video5secondViewsDoris`");
            put("videoFirstQuartileViewsDoris", "IFNULL(sum(`video_first_quartile_views`),0) AS `videoFirstQuartileViewsDoris`");
            put("videoMidpointViewsDoris", "IFNULL(sum(`video_Midpoint_Views`),0) AS `videoMidpointViewsDoris`");
            put("videoThirdQuartileViewsDoris", "IFNULL(sum(`video_third_quartile_views`),0) AS `videoThirdQuartileViewsDoris`");
            put("videoCompleteViewsDoris", "IFNULL(sum(`video_complete_views`),0) AS `videoCompleteViewsDoris`");
            put("videoUnmutesDoris", "IFNULL(sum(`video_unmutes`),0) AS `videoUnmutesDoris`");
            put("brandedSearches14dDoris", "IFNULL(sum(`branded_searches14d`),0) AS `brandedSearches14dDoris`");
            put("detailPageView14dDoris", "IFNULL(sum(`detail_page_view14d`),0) AS `detailPageView14dDoris`");
            put("minTopIsDoris", "IFNULL(min(top_of_search_is),0) minTopIsDoris");
            put("maxTopIsDoris", "max(top_of_search_is) maxTopIsDoris");
            put("cumulativeReachDoris", "IFNULL(max(`cumulative_reach`),0) AS `cumulativeReachDoris`");
            put("impressionsFrequencyAverageDoris", "IFNULL(max(`impressions_frequency_average`),0) AS `impressionsFrequencyAverageDoris`");
        }
    });


    private StringBuilder buildCampaignAllWhere(CampaignPageParam param, List<Object> argsList, String groupSql, boolean isNull) {
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            if (param.getCampaignIdList().size() < 10000) {
                whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
            } else {
                whereSql.append(SqlStringUtil.dealBitMapDorisInList("c.campaign_id", param.getCampaignIdList(), argsList));
            }
        }
        //通过param.campaignId查询具体的广告活动
        if (StringUtils.isNotEmpty(param.getCampaignId())) {
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", StringUtil.splitStr(param.getCampaignId()), argsList));
        }
        if (StringUtils.isNotBlank(param.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", StringUtil.splitStr(param.getCampaignIds()), argsList));
        }
        if (StringUtils.isNotBlank(param.getType())) {
            whereSql.append(SqlStringUtil.dealInList("c.type", StringUtil.splitStr(param.getType()), argsList));
        }
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
            List<String> servings = list.stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(servings)) {
                whereSql.append(SqlStringUtil.dealInList("c.serving_status", servings, argsList));
            }
        }
        if (StringUtils.isNotBlank(param.getPortfolioId()) && param.getPortfolioId().equals("-1")) {
            whereSql.append(" and c.portfolio_id is null ");
        }
        if (StringUtils.isNotBlank(param.getStrategyType())) {
            whereSql.append(" and c.strategy = ?");
            argsList.add(param.getStrategyType());
        }
        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and lower(c.name) like ? ");
            argsList.add("%" + param.getSearchValue().trim().toLowerCase() + "%");
        }
        //预算状态
        if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.budgetExceeded.getCode())) {
            whereSql.append(" and c.serving_status = ? ");
            argsList.add(AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
        } else if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.underBudget.getCode())) {
            whereSql.append(" and c.serving_status != ? ");
            argsList.add(AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
        }
        if (StringUtils.isNotBlank(param.getCostType())) {
            whereSql.append(" and c.cost_type = ? ");
            argsList.add(param.getCostType());
        }
        // 广告策略筛选
        String sql = AdCampaignStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(), argsList, "c.campaign_id");
        if(StringUtils.isNotEmpty(sql)){
            whereSql.append(sql);
        }
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (StringUtils.isNotBlank(param.getFilterTargetType())) {  //活动类型  auto自动投放,manual手动投放
                List<String> typeList = StringUtil.splitStr(param.getFilterTargetType(), ",");
                if (CollectionUtils.isNotEmpty(param.getCreativeIds())) {
                    whereSql.append(" and (");
                    whereSql.append(SqlStringUtil.dealInListNotAnd("c.ad_target_type", typeList, argsList));
                    whereSql.append(" or ");
                    if (param.getCreativeIds().size() < 10000) {
                        whereSql.append(SqlStringUtil.dealInListNotAnd("c.campaign_id", param.getCreativeIds(), argsList));
                    } else {
                        whereSql.append(SqlStringUtil.dealBitMapDorisInNoAndList("c.campaign_id", param.getCreativeIds(), argsList));
                    }
                    whereSql.append(" ) ");
                } else {
                    whereSql.append(SqlStringUtil.dealInList("c.ad_target_type", typeList, argsList));
                }
            }
            if (param.getDailyBudgetMin() != null) {   //每日预算
                whereSql.append(" and c.budget >= ? ");
                argsList.add(param.getDailyBudgetMin());
            }
            if (param.getDailyBudgetMax() != null) {
                whereSql.append(" and c.budget <= ? ");
                argsList.add(param.getDailyBudgetMax());
            }
            if (StringUtils.isNotBlank(param.getFilterStartDate())) {  // 日期
                whereSql.append(" and c.start_date >= ? ");
                argsList.add(param.getFilterStartDate());
            }
            if (StringUtils.isNotBlank(param.getFilterEndDate())) {
                whereSql.append(" and c.end_date <= ? ");
                argsList.add(param.getFilterEndDate());
            }
            if (param.getVcpmMin() != null || param.getVcpmMax() != null) {
                // vcpm高级筛选只筛选费用类型为vcpm的
                whereSql.append(" and c.cost_type = 'vcpm' ");
            }
        }
        if (isNull) {
            whereSql.append(" and r.is_summary = 1 ");
        }
        if (StringUtils.isNotBlank(groupSql)) {
            whereSql.append(groupSql);
        }
        whereSql.append(subWhereSql(param, argsList));
        return whereSql;
    }


    private StringBuilder buildCampaignAllWhereMultiple(CampaignPageParam param, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" where c.puid = ? ");
        argsList.add(param.getPuid());
        whereSql.append(SqlStringUtil.dealInList(" c.shop_id ", param.getShopIdList(), argsList));
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            if (param.getCampaignIdList().size() < 10000) {
                whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
            } else {
                whereSql.append(SqlStringUtil.dealBitMapDorisInList("c.campaign_id", param.getCampaignIdList(), argsList));
            }
        }
        //通过param.campaignId查询具体的广告活动
        if (StringUtils.isNotEmpty(param.getCampaignId())) {
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", StringUtil.splitStr(param.getCampaignId()), argsList));
        }
        if (StringUtils.isNotBlank(param.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", StringUtil.splitStr(param.getCampaignIds()), argsList));
        }
        if (StringUtils.isNotBlank(param.getType())) {
            whereSql.append(SqlStringUtil.dealInList("c.type", StringUtil.splitStr(param.getType()), argsList));
        }
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
            List<String> servings = list.stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(servings)) {
                whereSql.append(SqlStringUtil.dealInList("c.serving_status", servings, argsList));
            }
        }
        // 广告组合筛选包括未分类
        if(CollectionUtils.isNotEmpty(param.getPortfolioIdList())){
            if (param.getPortfolioIdList().contains(Constant.NON_PORTFOLIO_ID)) {
                whereSql.append(" and ( ").append(" c.portfolio_id is null or c.portfolio_id = '' ");
                if (param.getPortfolioIdList().size() > 1) {
                    whereSql.append(SqlStringUtil.dealInListOr("c.portfolio_id", param.getPortfolioIdList(), argsList));
                }
                whereSql.append(" ) ");
            } else {
                whereSql.append(SqlStringUtil.dealDorisInList("c.portfolio_id",  param.getPortfolioIdList(), argsList));
            }
        }
        // 广告策略筛选
        if(CollectionUtil.isNotEmpty(param.getAdStrategyTypeList())) {
            String sql = AdCampaignStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(), argsList, "c.campaign_id");
            if(StringUtils.isNotEmpty(sql)){
                whereSql.append(sql);
            }
        }
        if (StringUtils.isNotBlank(param.getStrategyType())) {
            whereSql.append(" and c.strategy = ?");
            argsList.add(param.getStrategyType());
        }
        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if (SearchTypeEnum.EXACT.getValue().equals(param.getSearchType())) {
                whereSql.append(" and lower(c.name) = ? ");
                argsList.add(param.getSearchValue().trim().toLowerCase());
            } else {
                whereSql.append(" and lower(c.name) like ? ");
                argsList.add("%" + param.getSearchValue().trim().toLowerCase() + "%");
            }
        }

        List<String> searchValueList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            for (String value : param.getSearchValueList()) {
                searchValueList.add(value.toLowerCase());
            }
        }

        if (CollectionUtils.isNotEmpty(searchValueList)) {
            whereSql.append(SqlStringUtil.dealInList("lower(c.name)", searchValueList, argsList));
        }

        //预算状态
        if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.budgetExceeded.getCode())) {
            whereSql.append(" and c.serving_status = ? ");
            argsList.add(AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
        } else if (StringUtils.isNotBlank(param.getBudgetState()) && param.getBudgetState().equalsIgnoreCase(CampaignPageParam.BudgetStateEnum.underBudget.getCode())) {
            whereSql.append(" and c.serving_status != ? ");
            argsList.add(AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
        }
        if (StringUtils.isNotBlank(param.getCostType())) {
            whereSql.append(" and c.cost_type = ? ");
            argsList.add(param.getCostType());
        }
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (StringUtils.isNotBlank(param.getFilterTargetType())) {  //活动类型  auto自动投放,manual手动投放
                List<String> typeList = StringUtil.splitStr(param.getFilterTargetType(), ",");
                // todo 高级搜索场景下 param.getCreativeIds() 赋值逻辑
                if (CollectionUtils.isNotEmpty(param.getCreativeIds())) {
                    whereSql.append(" and (");
                    whereSql.append(SqlStringUtil.dealInListNotAnd("c.ad_target_type", typeList, argsList));
                    whereSql.append(" or ");
                    if (param.getCreativeIds().size() < 10000) {
                        whereSql.append(SqlStringUtil.dealInListNotAnd("c.campaign_id", param.getCreativeIds(), argsList));
                    } else {
                        whereSql.append(SqlStringUtil.dealBitMapDorisInNoAndList("c.campaign_id", param.getCreativeIds(), argsList));
                    }
                    whereSql.append(" ) ");
                } else {
                    whereSql.append(SqlStringUtil.dealInList("c.ad_target_type", typeList, argsList));
                }
            }
            if (param.getDailyBudgetMin() != null) {   //每日预算
                whereSql.append(" and c.budget >= ? ");
                argsList.add(param.getDailyBudgetMin());
            }
            if (param.getDailyBudgetMax() != null) {
                whereSql.append(" and c.budget <= ? ");
                argsList.add(param.getDailyBudgetMax());
            }
            if (StringUtils.isNotBlank(param.getFilterStartDate())) {  // 日期
                whereSql.append(" and c.start_date >= ? ");
                argsList.add(param.getFilterStartDate());
            }
            if (StringUtils.isNotBlank(param.getFilterEndDate())) {
                whereSql.append(" and c.end_date <= ? ");
                argsList.add(param.getFilterEndDate());
            }
            if (param.getVcpmMin() != null || param.getVcpmMax() != null) {
                // vcpm高级筛选只筛选费用类型为vcpm的
                whereSql.append(" and c.cost_type = 'vcpm' ");
            }
        }
        whereSql.append(subWhereSqlMultiple(param, argsList));
        return whereSql;
    }

    private Set<String> getSelectKey(CampaignPageParam param) {
        Set<String> keySet = new HashSet<>();
        if (param.getImpressionsMin() != null) {
            keySet.add("impressionsDoris");
        }
        if (param.getImpressionsMax() != null) {
            keySet.add("impressionsDoris");
        }
        //点击量
        if (param.getClicksMin() != null) {
            keySet.add("clicksDoris");
        }
        if (param.getClicksMax() != null) {
            keySet.add("clicksDoris");
        }
        //点击率（clicks/impressions）
        if (param.getClickRateMin() != null) {
            keySet.add("clicksDoris");
            keySet.add("impressionsDoris");
        }
        if (param.getClickRateMax() != null) {
            keySet.add("clicksDoris");
            keySet.add("impressionsDoris");
        }
        //花费
        if (param.getCostMin() != null) {
            keySet.add("costDoris");
        }
        if (param.getCostMax() != null) {
            keySet.add("costDoris");
        }
        //cpc  平均点击费用
        if (param.getCpcMin() != null) {
            keySet.add("costDoris");
            keySet.add("clicksDoris");
        }
        if (param.getCpcMax() != null) {
            keySet.add("costDoris");
            keySet.add("clicksDoris");
        }
        //广告订单量
        if (param.getOrderNumMin() != null) {
            keySet.add("orderNumDoris");
        }
        if (param.getOrderNumMax() != null) {
            keySet.add("orderNumDoris");
        }
        //广告销售额
        if (param.getSalesMin() != null) {
            keySet.add("totalSalesDoris");
        }
        if (param.getSalesMax() != null) {
            keySet.add("totalSalesDoris");
        }
        //订单转化率
        if (param.getSalesConversionRateMin() != null) {
            keySet.add("orderNumDoris");
            keySet.add("clicksDoris");
        }
        if (param.getSalesConversionRateMax() != null) {
            keySet.add("orderNumDoris");
            keySet.add("clicksDoris");
        }
        //acos
        if (param.getAcosMin() != null) {
            keySet.add("costDoris");
            keySet.add("totalSalesDoris");
        }
        if (param.getAcosMax() != null) {
            keySet.add("costDoris");
            keySet.add("totalSalesDoris");
        }
        // roas
        if (param.getRoasMin() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("costDoris");
        }
        // roas
        if (param.getRoasMax() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("costDoris");
        }
        // acots  需要乘以店铺销售额
        if (param.getAcotsMin() != null) {
            keySet.add("costDoris");
        }
        // acots  需要乘以店铺销售额
        if (param.getAcotsMax() != null) {
            keySet.add("costDoris");
        }
        // asots 需要乘以店铺销售额
        if (param.getAsotsMin() != null) {
            keySet.add("totalSalesDoris");
        }
        // asots  需要乘以店铺销售额
        if (param.getAsotsMax() != null) {
            keySet.add("totalSalesDoris");
        }

        //可见展示次数
        if (param.getViewImpressionsMin() != null) {
            keySet.add("viewImpressionsDoris");
        }
        if (param.getViewImpressionsMax() != null) {
            keySet.add("viewImpressionsDoris");
        }

        //广告销量
        if (param.getAdSalesTotalMin() != null) {
            keySet.add("saleNumDoris");
        }

        if (param.getAdSalesTotalMax() != null) {
            keySet.add("saleNumDoris");
        }
        //CPA
        if (param.getCpaMin() != null) {
            keySet.add("costDoris");
            keySet.add("orderNumDoris");
        }
        if (param.getCpaMax() != null) {
            keySet.add("costDoris");
            keySet.add("orderNumDoris");
        }
        //VCPM= 每千次展现费用=广告花费/可见展现次数x1000
        if (param.getVcpmMin() != null) {
            keySet.add("costDoris");
            keySet.add("viewImpressionsDoris");
        }
        if (param.getVcpmMax() != null) {
            keySet.add("costDoris");
            keySet.add("viewImpressionsDoris");
        }
        //本广告产品订单量（绝对值）adSaleNumMin
        if (param.getAdSaleNumMin() != null) {
            keySet.add("adOrderNumDoris");
        }
        if (param.getAdSaleNumMax() != null) {
            keySet.add("adOrderNumDoris");
        }

        //其他产品广告订单量（绝对值） adOtherOrderNumMin
        if (param.getAdOtherOrderNumMin() != null) {
            keySet.add("orderNumDoris");
            keySet.add("adOrderNumDoris");
        }

        if (param.getAdOtherOrderNumMax() != null) {
            keySet.add("orderNumDoris");
            keySet.add("adOrderNumDoris");
        }

        //本广告产品销售额（绝对值） adSalesMin
        if (param.getAdSalesMin() != null) {
            keySet.add("adSalesDoris");
        }

        if (param.getAdSalesMax() != null) {
            keySet.add("adSalesDoris");
        }

        //其他产品广告销售额（绝对值）adOtherSalesMin
        if (param.getAdOtherSalesMin() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("adSalesDoris");
        }

        if (param.getAdOtherSalesMax() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("adSalesDoris");
        }

        //本广告产品销量（绝对值）adSelfSaleNumMin units_ordered7d_same_sku
        if (param.getAdSelfSaleNumMin() != null) {
            keySet.add("adSaleNumDoris");
        }
        if (param.getAdSelfSaleNumMax() != null) {
            keySet.add("adSaleNumDoris");
        }

        //其他产品广告销量（绝对值）adOtherSaleNumMin
        if (param.getAdOtherSaleNumMin() != null) {
            keySet.add("saleNumDoris");
            keySet.add("adSaleNumDoris");
        }
        if (param.getAdOtherSaleNumMax() != null) {
            keySet.add("saleNumDoris");
            keySet.add("adSaleNumDoris");
        }

        //“品牌新买家”订单量（绝对值） orders_new_to_brand14d
        if (param.getOrdersNewToBrandFTDMin() != null) {
            keySet.add("ordersNewToBrand14dDoris");
        }
        if (param.getOrdersNewToBrandFTDMax() != null) {
            keySet.add("ordersNewToBrand14dDoris");
        }


        //“品牌新买家”订单百分比（百分比） orderRateNewToBrandFTDMin
        if (param.getOrderRateNewToBrandFTDMin() != null) {
            keySet.add("ordersNewToBrand14dDoris");
            keySet.add("orderNumDoris");
        }
        if (param.getOrderRateNewToBrandFTDMax() != null) {
            keySet.add("ordersNewToBrand14dDoris");
            keySet.add("orderNumDoris");
        }

        //“品牌新买家”销售额（绝对值） salesNewToBrandFTDMin
        if (param.getSalesNewToBrandFTDMin() != null) {
            keySet.add("salesNewToBrand14dDoris");
        }

        if (param.getSalesNewToBrandFTDMax() != null) {
            keySet.add("salesNewToBrand14dDoris");
        }


        //“品牌新买家”销售额百分比（百分比） salesRateNewToBrandFTDMin
        if (param.getSalesRateNewToBrandFTDMin() != null) {
            keySet.add("salesNewToBrand14dDoris");
            keySet.add("totalSalesDoris");
        }
        if (param.getSalesRateNewToBrandFTDMax() != null) {
            keySet.add("salesNewToBrand14dDoris");
            keySet.add("totalSalesDoris");
        }

        //“品牌新买家”销量（绝对值）unitsOrderedNewToBrandFTDMin   units_ordered_new_to_brand14d
        if (param.getUnitsOrderedNewToBrandFTDMin() != null) {
            keySet.add("unitsOrderedNewToBrand14dDoris");
        }
        if (param.getUnitsOrderedNewToBrandFTDMax() != null) {
            keySet.add("unitsOrderedNewToBrand14dDoris");
        }

        //“品牌新买家”销量百分比（百分比）unitsOrderedRateNewToBrandFTDMin
        if (param.getUnitsOrderedRateNewToBrandFTDMin() != null) {
            keySet.add("unitsOrderedNewToBrand14dDoris");
            keySet.add("saleNumDoris");
        }
        if (param.getUnitsOrderedRateNewToBrandFTDMax() != null) {
            keySet.add("unitsOrderedNewToBrand14dDoris");
            keySet.add("saleNumDoris");
        }

        // 加购次数 筛选
        if (param.getAddToCartMin() != null) {
            keySet.add("addToCartDoris");
        }
        if (param.getAddToCartMax() != null) {
            keySet.add("addToCartDoris");
        }

        // 5秒观看次数 筛选
        if (param.getVideo5SecondViewsMin() != null) {
            keySet.add("video5secondViewsDoris");
        }
        if (param.getVideo5SecondViewsMax() != null) {
            keySet.add("video5secondViewsDoris");
        }

        // 视频完整播放次数 筛选
        if (param.getVideoCompleteViewsMin() != null) {
            keySet.add("videoCompleteViewsDoris");
        }
        if (param.getVideoCompleteViewsMax() != null) {
            keySet.add("videoCompleteViewsDoris");
        }

        // 观看率 筛选
        if (param.getViewabilityRateMin() != null) {
            keySet.add("viewImpressionsDoris");
            keySet.add("impressionsDoris");
        }
        if (param.getViewabilityRateMax() != null) {
            keySet.add("viewImpressionsDoris");
            keySet.add("impressionsDoris");
        }

        // 观看点击率 筛选
        if (param.getViewClickThroughRateMin() != null) {
            keySet.add("clicksDoris");
            keySet.add("viewImpressionsDoris");
        }
        if (param.getViewClickThroughRateMax() != null) {
            keySet.add("clicksDoris");
            keySet.add("viewImpressionsDoris");
        }

        // 品牌搜索次数 筛选
        if (param.getBrandedSearchesMin() != null) {
            keySet.add("brandedSearches14dDoris");
        }
        if (param.getBrandedSearchesMax() != null) {
            keySet.add("brandedSearches14dDoris");
        }

        // 广告笔单价 筛选
        if (param.getAdvertisingUnitPriceMin() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("orderNumDoris");
        }
        if (param.getAdvertisingUnitPriceMax() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("orderNumDoris");
        }
        if(param.getTopImpressionShareMax() != null || param.getTopImpressionShareMin() != null){
            keySet.add("maxTopIsDoris");
            keySet.add("minTopIsDoris");
        }
        return keySet;
    }

    /**
     * 高级排序
     */
    private StringBuilder subWhereSql(CampaignPageParam param, List<Object> argsList) {
        StringBuilder subWhereSql = new StringBuilder();
        //高级筛选
        if (param.getUseAdvanced()) {
            BigDecimal shopSales = param.getShopSales() != null ? param.getShopSales() : BigDecimal.ZERO;

            subWhereSql.append(" having 1=1 ");
            //展示量
            if (param.getImpressionsMin() != null) {
                subWhereSql.append(" and impressionsDoris >= ?");
                argsList.add(param.getImpressionsMin());
            }
            if (param.getImpressionsMax() != null) {
                subWhereSql.append(" and impressionsDoris <= ?");
                argsList.add(param.getImpressionsMax());
            }
            //点击量
            if (param.getClicksMin() != null) {
                subWhereSql.append(" and clicksDoris >= ?");
                argsList.add(param.getClicksMin());
            }
            if (param.getClicksMax() != null) {
                subWhereSql.append(" and clicksDoris <= ?");
                argsList.add(param.getClicksMax());
            }
            //点击率（clicks/impressions）
            if (param.getClickRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(clicksDoris/impressionsDoris,0),4) >= ?");
                argsList.add(param.getClickRateMin());
            }
            if (param.getClickRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(clicksDoris/impressionsDoris,0),4) <= ?");
                argsList.add(param.getClickRateMax());
            }
            //花费
            if (param.getCostMin() != null) {
                subWhereSql.append(" and costDoris >= ?");
                argsList.add(param.getCostMin());
            }
            if (param.getCostMax() != null) {
                subWhereSql.append(" and costDoris <= ?");
                argsList.add(param.getCostMax());
            }
            //cpc  平均点击费用
            if (param.getCpcMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/clicksDoris,0),2) >= ?");
                argsList.add(param.getCpcMin());
            }
            if (param.getCpcMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/clicksDoris,0),2) <= ?");
                argsList.add(param.getCpcMax());
            }
            //广告订单量
            if (param.getOrderNumMin() != null) {
                subWhereSql.append(" and orderNumDoris >= ?");
                argsList.add(param.getOrderNumMin());
            }
            if (param.getOrderNumMax() != null) {
                subWhereSql.append(" and orderNumDoris <= ?");
                argsList.add(param.getOrderNumMax());
            }
            //广告销售额
            if (param.getSalesMin() != null) {
                subWhereSql.append(" and totalSalesDoris >= ?");
                argsList.add(param.getSalesMin());
            }
            if (param.getSalesMax() != null) {
                subWhereSql.append(" and totalSalesDoris <= ?");
                argsList.add(param.getSalesMax());
            }
            //订单转化率
            if (param.getSalesConversionRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(orderNumDoris/clicksDoris,0),4) >= ?");
                argsList.add(param.getSalesConversionRateMin());
            }
            if (param.getSalesConversionRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(orderNumDoris/clicksDoris,0),4) <= ?");
                argsList.add(param.getSalesConversionRateMax());
            }
            //acos
            if (param.getAcosMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/totalSalesDoris,0),4) >= ?");
                argsList.add(param.getAcosMin());
            }
            if (param.getAcosMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/totalSalesDoris,0),4) <= ?");
                argsList.add(param.getAcosMax());
            }
            // roas
            if (param.getRoasMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/costDoris,0),2) >= ?");
                argsList.add(param.getRoasMin());
            }
            // roas
            if (param.getRoasMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/costDoris,0),2) <= ?");
                argsList.add(param.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(costDoris,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAcotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(costDoris,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAcotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (param.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(totalSalesDoris,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAsotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (param.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(totalSalesDoris,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAsotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }

            //可见展示次数
            if (param.getViewImpressionsMin() != null) {
                subWhereSql.append(" and  viewImpressionsDoris >= ? ");
                argsList.add(param.getViewImpressionsMin());
            }
            if (param.getViewImpressionsMax() != null) {
                subWhereSql.append(" and viewImpressionsDoris <= ? ");
                argsList.add(param.getViewImpressionsMax());
            }

            //广告销量
            if (param.getAdSalesTotalMin() != null) {
                subWhereSql.append(" and saleNumDoris >= ? ");
                argsList.add(param.getAdSalesTotalMin());
            }

            if (param.getAdSalesTotalMax() != null) {
                subWhereSql.append(" and saleNumDoris <= ? ");
                argsList.add(param.getAdSalesTotalMax());
            }

            //CPA
            if (param.getCpaMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/orderNumDoris, 0), 2) >= ? ");
                argsList.add(param.getCpaMin());
            }
            if (param.getCpaMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/orderNumDoris, 0), 2) <= ? ");
                argsList.add(param.getCpaMax());
            }
            //VCPM= 每千次展现费用=广告花费/可见展现次数x1000
            if (param.getVcpmMin() != null) {
                subWhereSql.append(" and ROUND(ifnull((costDoris/viewImpressionsDoris) * 1000, 0), 4) >= ? ");
                argsList.add(param.getVcpmMin());
            }
            if (param.getVcpmMax() != null) {
                subWhereSql.append(" and ROUND(ifnull((costDoris/viewImpressionsDoris) * 1000, 0), 4) <= ? ");
                argsList.add(param.getVcpmMax());
            }
            //本广告产品订单量（绝对值）adSaleNumMin
            if (param.getAdSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(adOrderNumDoris, 0) >= ? ");
                argsList.add(param.getAdSaleNumMin());
            }
            if (param.getAdSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(adOrderNumDoris, 0) <= ? ");
                argsList.add(param.getAdSaleNumMax());
            }

            //其他产品广告订单量（绝对值） adOtherOrderNumMin
            if (param.getAdOtherOrderNumMin() != null) {
                subWhereSql.append(" and ifnull(orderNumDoris - adOrderNumDoris, 0) >= ? ");
                argsList.add(param.getAdOtherOrderNumMin());
            }

            if (param.getAdOtherOrderNumMax() != null) {
                subWhereSql.append(" and ifnull(orderNumDoris - adOrderNumDoris, 0) <= ? ");
                argsList.add(param.getAdOtherOrderNumMax());
            }

            //本广告产品销售额（绝对值） adSalesMin
            if (param.getAdSalesMin() != null) {
                subWhereSql.append(" and ifnull(adSalesDoris, 0) >= ? ");
                argsList.add(param.getAdSalesMin());
            }

            if (param.getAdSalesMax() != null) {
                subWhereSql.append(" and ifnull(adSalesDoris, 0) <= ? ");
                argsList.add(param.getAdSalesMax());
            }

            //其他产品广告销售额（绝对值）adOtherSalesMin
            if (param.getAdOtherSalesMin() != null) {
                subWhereSql.append(" and ifnull(totalSalesDoris - adSalesDoris, 0) >= ? ");
                argsList.add(param.getAdOtherSalesMin());
            }

            if (param.getAdOtherSalesMax() != null) {
                subWhereSql.append(" and ifnull(totalSalesDoris - adSalesDoris, 0) <= ? ");
                argsList.add(param.getAdOtherSalesMax());
            }

            //本广告产品销量（绝对值）adSelfSaleNumMin units_ordered7d_same_sku
            if (param.getAdSelfSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(adSaleNumDoris, 0) >= ? ");
                argsList.add(param.getAdSelfSaleNumMin());
            }
            if (param.getAdSelfSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(adSaleNumDoris, 0) <= ? ");
                argsList.add(param.getAdSelfSaleNumMax());
            }

            //其他产品广告销量（绝对值）adOtherSaleNumMin
            if (param.getAdOtherSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(saleNumDoris - adSaleNumDoris, 0) >= ? ");
                argsList.add(param.getAdOtherSaleNumMin());
            }
            if (param.getAdOtherSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(saleNumDoris - adSaleNumDoris, 0) <= ? ");
                argsList.add(param.getAdOtherSaleNumMax());
            }

            //“品牌新买家”订单量（绝对值） orders_new_to_brand14d
            if (param.getOrdersNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ifnull(ordersNewToBrand14dDoris, 0) >= ? ");
                argsList.add(param.getOrdersNewToBrandFTDMin());
            }
            if (param.getOrdersNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ifnull(ordersNewToBrand14dDoris, 0) <= ? ");
                argsList.add(param.getOrdersNewToBrandFTDMax());
            }


            //“品牌新买家”订单百分比（百分比） orderRateNewToBrandFTDMin
            if (param.getOrderRateNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((ordersNewToBrand14dDoris/orderNumDoris) * 100, 0), 4), 2) >= ? ");
                argsList.add(MathUtil.multiplyZero(param.getOrderRateNewToBrandFTDMin(), BigDecimal.valueOf(100)));
            }
            if (param.getOrderRateNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((ordersNewToBrand14dDoris/orderNumDoris) * 100, 0), 4), 2) <= ? ");
                argsList.add(MathUtil.multiplyZero(param.getOrderRateNewToBrandFTDMax(), BigDecimal.valueOf(100)));
            }

            //“品牌新买家”销售额（绝对值） salesNewToBrandFTDMin
            if (param.getSalesNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ifnull(salesNewToBrand14dDoris, 0) >= ? ");
                argsList.add(param.getSalesNewToBrandFTDMin());
            }

            if (param.getSalesNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ifnull(salesNewToBrand14dDoris, 0) <= ? ");
                argsList.add(param.getSalesNewToBrandFTDMax());
            }


            //“品牌新买家”销售额百分比（百分比） salesRateNewToBrandFTDMin
            if (param.getSalesRateNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((salesNewToBrand14dDoris/totalSalesDoris) * 100, 0), 4), 2) >= ? ");
                argsList.add(MathUtil.multiplyZero(param.getSalesRateNewToBrandFTDMin(), BigDecimal.valueOf(100)));
            }
            if (param.getSalesRateNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((salesNewToBrand14dDoris/totalSalesDoris) * 100, 0), 4), 2) <= ? ");
                argsList.add(MathUtil.multiplyZero(param.getSalesRateNewToBrandFTDMax(), BigDecimal.valueOf(100)));
            }

            //“品牌新买家”销量（绝对值）unitsOrderedNewToBrandFTDMin   units_ordered_new_to_brand14d
            if (param.getUnitsOrderedNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ifnull(unitsOrderedNewToBrand14dDoris, 0) >= ? ");
                argsList.add(param.getUnitsOrderedNewToBrandFTDMin());
            }
            if (param.getUnitsOrderedNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ifnull(unitsOrderedNewToBrand14dDoris, 0) <= ? ");
                argsList.add(param.getUnitsOrderedNewToBrandFTDMax());
            }


            //“品牌新买家”销量百分比（百分比）unitsOrderedRateNewToBrandFTDMin
            if (param.getUnitsOrderedRateNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((unitsOrderedNewToBrand14dDoris/saleNumDoris) * 100, 0), 4), 2) >= ? ");
                argsList.add(MathUtil.multiplyZero(param.getUnitsOrderedRateNewToBrandFTDMin(), BigDecimal.valueOf(100)));
            }
            if (param.getUnitsOrderedRateNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((unitsOrderedNewToBrand14dDoris/saleNumDoris) * 100, 0), 4), 2) <= ? ");
                argsList.add(MathUtil.multiplyZero(param.getUnitsOrderedRateNewToBrandFTDMax(), BigDecimal.valueOf(100)));
            }

            // 加购次数 筛选
            if (param.getAddToCartMin() != null) {
                subWhereSql.append(" and ifnull(addToCartDoris , 0) >= ? ");
                argsList.add(param.getAddToCartMin());
            }
            if (param.getAddToCartMax() != null) {
                subWhereSql.append(" and ifnull(addToCartDoris , 0) <= ? ");
                argsList.add(param.getAddToCartMax());
            }

            // 5秒观看次数 筛选
            if (param.getVideo5SecondViewsMin() != null) {
                subWhereSql.append(" and ifnull(video5secondViewsDoris , 0) >= ? ");
                argsList.add(param.getVideo5SecondViewsMin());
            }
            if (param.getVideo5SecondViewsMax() != null) {
                subWhereSql.append(" and ifnull(video5secondViewsDoris , 0) <= ? ");
                argsList.add(param.getVideo5SecondViewsMax());
            }

            // 视频完整播放次数 筛选
            if (param.getVideoCompleteViewsMin() != null) {
                subWhereSql.append(" and ifnull(videoCompleteViewsDoris , 0) >= ? ");
                argsList.add(param.getVideoCompleteViewsMin());
            }
            if (param.getVideoCompleteViewsMax() != null) {
                subWhereSql.append(" and ifnull(videoCompleteViewsDoris , 0) <= ? ");
                argsList.add(param.getVideoCompleteViewsMax());
            }

            // 观看率 筛选
            if (param.getViewabilityRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull((viewImpressionsDoris/impressionsDoris) * 100, 0), 2) >= ? ");
                argsList.add(param.getViewabilityRateMin());
            }
            if (param.getViewabilityRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull((viewImpressionsDoris/impressionsDoris) * 100, 0), 2) <= ? ");
                argsList.add(param.getViewabilityRateMax());
            }

            // 观看点击率 筛选
            if (param.getViewClickThroughRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull((clicksDoris/viewImpressionsDoris) * 100, 0), 2) >= ? ");
                argsList.add(param.getViewClickThroughRateMin());
            }
            if (param.getViewClickThroughRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull((clicksDoris/viewImpressionsDoris) * 100, 0), 2) <= ? ");
                argsList.add(param.getViewClickThroughRateMax());
            }

            // 品牌搜索次数 筛选
            if (param.getBrandedSearchesMin() != null) {
                subWhereSql.append(" and brandedSearches14dDoris >= ? ");
                argsList.add(param.getBrandedSearchesMin());
            }
            if (param.getBrandedSearchesMax() != null) {
                subWhereSql.append(" and brandedSearches14dDoris <= ? ");
                argsList.add(param.getBrandedSearchesMax());
            }

            // 广告笔单价 筛选
            if (param.getAdvertisingUnitPriceMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/orderNumDoris, 0), 2) >= ?");
                argsList.add(param.getAdvertisingUnitPriceMin());
            }
            if (param.getAdvertisingUnitPriceMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/orderNumDoris, 0), 2) <= ?");
                argsList.add(param.getAdvertisingUnitPriceMax());
            }

            // 搜索结果首页首位IS
            if (param.getTopImpressionShareMin() != null) {
                subWhereSql.append(" and max(top_of_search_is) >= ?");
                argsList.add(param.getTopImpressionShareMin());
            }
            if (param.getTopImpressionShareMax() != null) {
                subWhereSql.append(" and min(top_of_search_is) <= ?");
                argsList.add(param.getTopImpressionShareMax());
            }
        }
        return subWhereSql;
    }
    /**
     * 高级排序
     */
    private StringBuilder subWhereSqlMultiple(CampaignPageParam param, List<Object> argsList) {
        StringBuilder subWhereSql = new StringBuilder();
        //高级筛选
        if (param.getUseAdvanced()) {
            subWhereSql.append(" having 1=1 ");
            //展示量
            if (param.getImpressionsMin() != null) {
                subWhereSql.append(" and impressionsDoris >= ?");
                argsList.add(param.getImpressionsMin());
            }
            if (param.getImpressionsMax() != null) {
                subWhereSql.append(" and impressionsDoris <= ?");
                argsList.add(param.getImpressionsMax());
            }
            //点击量
            if (param.getClicksMin() != null) {
                subWhereSql.append(" and clicksDoris >= ?");
                argsList.add(param.getClicksMin());
            }
            if (param.getClicksMax() != null) {
                subWhereSql.append(" and clicksDoris <= ?");
                argsList.add(param.getClicksMax());
            }
            //点击率（clicks/impressions）
            if (param.getClickRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(clicksDoris/impressionsDoris,0),4) >= ?");
                argsList.add(param.getClickRateMin());
            }
            if (param.getClickRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(clicksDoris/impressionsDoris,0),4) <= ?");
                argsList.add(param.getClickRateMax());
            }
            //花费
            if (param.getCostMin() != null) {
                subWhereSql.append(" and costDoris >= ?");
                argsList.add(param.getCostMin());
            }
            if (param.getCostMax() != null) {
                subWhereSql.append(" and costDoris <= ?");
                argsList.add(param.getCostMax());
            }
            //cpc  平均点击费用
            if (param.getCpcMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/clicksDoris,0),2) >= ?");
                argsList.add(param.getCpcMin());
            }
            if (param.getCpcMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/clicksDoris,0),2) <= ?");
                argsList.add(param.getCpcMax());
            }
            //广告订单量
            if (param.getOrderNumMin() != null) {
                subWhereSql.append(" and orderNumDoris >= ?");
                argsList.add(param.getOrderNumMin());
            }
            if (param.getOrderNumMax() != null) {
                subWhereSql.append(" and orderNumDoris <= ?");
                argsList.add(param.getOrderNumMax());
            }
            //广告销售额
            if (param.getSalesMin() != null) {
                subWhereSql.append(" and totalSalesDoris >= ?");
                argsList.add(param.getSalesMin());
            }
            if (param.getSalesMax() != null) {
                subWhereSql.append(" and totalSalesDoris <= ?");
                argsList.add(param.getSalesMax());
            }
            //订单转化率
            if (param.getSalesConversionRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(orderNumDoris/clicksDoris,0),4) >= ?");
                argsList.add(param.getSalesConversionRateMin());
            }
            if (param.getSalesConversionRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(orderNumDoris/clicksDoris,0),4) <= ?");
                argsList.add(param.getSalesConversionRateMax());
            }
            //acos
            if (param.getAcosMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/totalSalesDoris,0),4) >= ?");
                argsList.add(param.getAcosMin());
            }
            if (param.getAcosMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/totalSalesDoris,0),4) <= ?");
                argsList.add(param.getAcosMax());
            }
            // roas
            if (param.getRoasMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/costDoris,0),4) >= ?");
                argsList.add(param.getRoasMin());
            }
            // roas
            if (param.getRoasMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/costDoris,0),4) <= ?");
                argsList.add(param.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/shopSalesDoris,0),4) >= ? ");
                argsList.add(param.getAcotsMin());
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/shopSalesDoris,0),4) <= ? ");
                argsList.add(param.getAcotsMax());
            }
            // asots 需要乘以店铺销售额
            if (param.getAsotsMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/shopSalesDoris,0),4) >= ? ");
                argsList.add(param.getAsotsMin());
            }
            // asots  需要乘以店铺销售额
            if (param.getAsotsMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/shopSalesDoris,0),4) <= ? ");
                argsList.add(param.getAsotsMax());
            }
            //可见展示次数
            if (param.getViewImpressionsMin() != null) {
                subWhereSql.append(" and  viewImpressionsDoris >= ? ");
                argsList.add(param.getViewImpressionsMin());
            }
            if (param.getViewImpressionsMax() != null) {
                subWhereSql.append(" and viewImpressionsDoris <= ? ");
                argsList.add(param.getViewImpressionsMax());
            }
            //广告销量
            if (param.getAdSalesTotalMin() != null) {
                subWhereSql.append(" and saleNumDoris >= ? ");
                argsList.add(param.getAdSalesTotalMin());
            }
            if (param.getAdSalesTotalMax() != null) {
                subWhereSql.append(" and saleNumDoris <= ? ");
                argsList.add(param.getAdSalesTotalMax());
            }
            //CPA
            if (param.getCpaMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/orderNumDoris, 0), 4) >= ? ");
                argsList.add(param.getCpaMin());
            }
            if (param.getCpaMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/orderNumDoris, 0), 4) <= ? ");
                argsList.add(param.getCpaMax());
            }
            //VCPM= 每千次展现费用=广告花费/可见展现次数x1000
            if (param.getVcpmMin() != null) {
                subWhereSql.append(" and ROUND(ifnull((costDoris/viewImpressionsDoris) * 1000, 0), 4) >= ? ");
                argsList.add(param.getVcpmMin());
            }
            if (param.getVcpmMax() != null) {
                subWhereSql.append(" and ROUND(ifnull((costDoris/viewImpressionsDoris) * 1000, 0), 4) <= ? ");
                argsList.add(param.getVcpmMax());
            }
            //本广告产品订单量（绝对值）adSaleNumMin
            if (param.getAdSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(adOrderNumDoris, 0) >= ? ");
                argsList.add(param.getAdSaleNumMin());
            }
            if (param.getAdSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(adOrderNumDoris, 0) <= ? ");
                argsList.add(param.getAdSaleNumMax());
            }
            //其他产品广告订单量（绝对值） adOtherOrderNumMin
            if (param.getAdOtherOrderNumMin() != null) {
                subWhereSql.append(" and ifnull(orderNumDoris - adOrderNumDoris, 0) >= ? ");
                argsList.add(param.getAdOtherOrderNumMin());
            }
            if (param.getAdOtherOrderNumMax() != null) {
                subWhereSql.append(" and ifnull(orderNumDoris - adOrderNumDoris, 0) <= ? ");
                argsList.add(param.getAdOtherOrderNumMax());
            }
            //本广告产品销售额（绝对值） adSalesMin
            if (param.getAdSalesMin() != null) {
                subWhereSql.append(" and ifnull(adSalesDoris, 0) >= ? ");
                argsList.add(param.getAdSalesMin());
            }
            if (param.getAdSalesMax() != null) {
                subWhereSql.append(" and ifnull(adSalesDoris, 0) <= ? ");
                argsList.add(param.getAdSalesMax());
            }
            //其他产品广告销售额（绝对值）adOtherSalesMin
            if (param.getAdOtherSalesMin() != null) {
                subWhereSql.append(" and ifnull(totalSalesDoris - adSalesDoris, 0) >= ? ");
                argsList.add(param.getAdOtherSalesMin());
            }
            if (param.getAdOtherSalesMax() != null) {
                subWhereSql.append(" and ifnull(totalSalesDoris - adSalesDoris, 0) <= ? ");
                argsList.add(param.getAdOtherSalesMax());
            }
            //本广告产品销量（绝对值）adSelfSaleNumMin units_ordered7d_same_sku
            if (param.getAdSelfSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(adSaleNumDoris, 0) >= ? ");
                argsList.add(param.getAdSelfSaleNumMin());
            }
            if (param.getAdSelfSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(adSaleNumDoris, 0) <= ? ");
                argsList.add(param.getAdSelfSaleNumMax());
            }
            //其他产品广告销量（绝对值）adOtherSaleNumMin
            if (param.getAdOtherSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(saleNumDoris - adSaleNumDoris, 0) >= ? ");
                argsList.add(param.getAdOtherSaleNumMin());
            }
            if (param.getAdOtherSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(saleNumDoris - adSaleNumDoris, 0) <= ? ");
                argsList.add(param.getAdOtherSaleNumMax());
            }
            //“品牌新买家”订单量（绝对值） orders_new_to_brand14d
            if (param.getOrdersNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ifnull(ordersNewToBrand14dDoris, 0) >= ? ");
                argsList.add(param.getOrdersNewToBrandFTDMin());
            }
            if (param.getOrdersNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ifnull(ordersNewToBrand14dDoris, 0) <= ? ");
                argsList.add(param.getOrdersNewToBrandFTDMax());
            }
            //“品牌新买家”订单百分比（百分比） orderRateNewToBrandFTDMin
            if (param.getOrderRateNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((ordersNewToBrand14dDoris/orderNumDoris) * 100, 0), 4), 2) >= ? ");
                argsList.add(MathUtil.multiplyZero(param.getOrderRateNewToBrandFTDMin(), BigDecimal.valueOf(100)));
            }
            if (param.getOrderRateNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((ordersNewToBrand14dDoris/orderNumDoris) * 100, 0), 4), 2) <= ? ");
                argsList.add(MathUtil.multiplyZero(param.getOrderRateNewToBrandFTDMax(), BigDecimal.valueOf(100)));
            }
            //“品牌新买家”销售额（绝对值） salesNewToBrandFTDMin
            if (param.getSalesNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ifnull(salesNewToBrand14dDoris, 0) >= ? ");
                argsList.add(param.getSalesNewToBrandFTDMin());
            }
            if (param.getSalesNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ifnull(salesNewToBrand14dDoris, 0) <= ? ");
                argsList.add(param.getSalesNewToBrandFTDMax());
            }
            //“品牌新买家”销售额百分比（百分比） salesRateNewToBrandFTDMin
            if (param.getSalesRateNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((salesNewToBrand14dDoris/totalSalesDoris) * 100, 0), 4), 2) >= ? ");
                argsList.add(MathUtil.multiplyZero(param.getSalesRateNewToBrandFTDMin(), BigDecimal.valueOf(100)));
            }
            if (param.getSalesRateNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((salesNewToBrand14dDoris/totalSalesDoris) * 100, 0), 4), 2) <= ? ");
                argsList.add(MathUtil.multiplyZero(param.getSalesRateNewToBrandFTDMax(), BigDecimal.valueOf(100)));
            }
            //“品牌新买家”销量（绝对值）unitsOrderedNewToBrandFTDMin   units_ordered_new_to_brand14d
            if (param.getUnitsOrderedNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ifnull(unitsOrderedNewToBrand14dDoris, 0) >= ? ");
                argsList.add(param.getUnitsOrderedNewToBrandFTDMin());
            }
            if (param.getUnitsOrderedNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ifnull(unitsOrderedNewToBrand14dDoris, 0) <= ? ");
                argsList.add(param.getUnitsOrderedNewToBrandFTDMax());
            }
            //“品牌新买家”销量百分比（百分比）unitsOrderedRateNewToBrandFTDMin
            if (param.getUnitsOrderedRateNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((unitsOrderedNewToBrand14dDoris/saleNumDoris) * 100, 0), 4), 2) >= ? ");
                argsList.add(MathUtil.multiplyZero(param.getUnitsOrderedRateNewToBrandFTDMin(), BigDecimal.valueOf(100)));
            }
            if (param.getUnitsOrderedRateNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((unitsOrderedNewToBrand14dDoris/saleNumDoris) * 100, 0), 4), 2) <= ? ");
                argsList.add(MathUtil.multiplyZero(param.getUnitsOrderedRateNewToBrandFTDMax(), BigDecimal.valueOf(100)));
            }
            // 加购次数 筛选
            if (param.getAddToCartMin() != null) {
                subWhereSql.append(" and ifnull(addToCartDoris , 0) >= ? ");
                argsList.add(param.getAddToCartMin());
            }
            if (param.getAddToCartMax() != null) {
                subWhereSql.append(" and ifnull(addToCartDoris , 0) <= ? ");
                argsList.add(param.getAddToCartMax());
            }
            // 5秒观看次数 筛选
            if (param.getVideo5SecondViewsMin() != null) {
                subWhereSql.append(" and ifnull(video5secondViewsDoris , 0) >= ? ");
                argsList.add(param.getVideo5SecondViewsMin());
            }
            if (param.getVideo5SecondViewsMax() != null) {
                subWhereSql.append(" and ifnull(video5secondViewsDoris , 0) <= ? ");
                argsList.add(param.getVideo5SecondViewsMax());
            }
            // 视频完整播放次数 筛选
            if (param.getVideoCompleteViewsMin() != null) {
                subWhereSql.append(" and ifnull(videoCompleteViewsDoris , 0) >= ? ");
                argsList.add(param.getVideoCompleteViewsMin());
            }
            if (param.getVideoCompleteViewsMax() != null) {
                subWhereSql.append(" and ifnull(videoCompleteViewsDoris , 0) <= ? ");
                argsList.add(param.getVideoCompleteViewsMax());
            }
            // 观看率 筛选
            if (param.getViewabilityRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull((viewImpressionsDoris/impressionsDoris) * 100, 0), 2) >= ? ");
                argsList.add(param.getViewabilityRateMin());
            }
            if (param.getViewabilityRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull((viewImpressionsDoris/impressionsDoris) * 100, 0), 2) <= ? ");
                argsList.add(param.getViewabilityRateMax());
            }
            // 观看点击率 筛选
            if (param.getViewClickThroughRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull((clicksDoris/viewImpressionsDoris) * 100, 0), 2) >= ? ");
                argsList.add(param.getViewClickThroughRateMin());
            }
            if (param.getViewClickThroughRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull((clicksDoris/viewImpressionsDoris) * 100, 0), 2) <= ? ");
                argsList.add(param.getViewClickThroughRateMax());
            }
            // 品牌搜索次数 筛选
            if (param.getBrandedSearchesMin() != null) {
                subWhereSql.append(" and brandedSearches14dDoris >= ? ");
                argsList.add(param.getBrandedSearchesMin());
            }
            if (param.getBrandedSearchesMax() != null) {
                subWhereSql.append(" and brandedSearches14dDoris <= ? ");
                argsList.add(param.getBrandedSearchesMax());
            }
            // 广告笔单价 筛选
            if (param.getAdvertisingUnitPriceMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/orderNumDoris, 0), 2) >= ?");
                argsList.add(param.getAdvertisingUnitPriceMin());
            }
            if (param.getAdvertisingUnitPriceMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/orderNumDoris, 0), 2) <= ?");
                argsList.add(param.getAdvertisingUnitPriceMax());
            }
            // 搜索结果首页首位IS
            if (param.getTopImpressionShareMin() != null) {
                subWhereSql.append(" and maxTopIsDoris >= ?");
                argsList.add(param.getTopImpressionShareMin());
            }
            if (param.getTopImpressionShareMax() != null) {
                subWhereSql.append(" and minTopIsDoris <= ?");
                argsList.add(param.getTopImpressionShareMax());
            }
        }
        return subWhereSql;
    }

    @Override
    public AmazonAdCampaignAll getMostSaleNumMarketplaceId(Integer puid, List<Integer> shopIdList, String startDate, String endDate) {
//        String sql = "select marketplace_id, IFNULL(sum(if (type = 'sp' , conversions7d,conversions14d)),0) orderNumDoris " +
//                "from ods_t_amazon_ad_campaign_all_report where puid=? and marketplace_id is not null and marketplace_id != '' and count_day>=? and count_day<=? " +
//                "group by marketplace_id having orderNumDoris > 0 order by orderNumDoris desc limit 1";

        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder(" select marketplace_id, IFNULL(sum(if (type = 'sp' , conversions7d,conversions14d)),0) orderNumDoris ")
                .append(" from ods_t_amazon_ad_campaign_all_report where puid=? and marketplace_id is not null and marketplace_id != '' and count_day>=? and count_day<=? ");
        argsList.add(puid);
        argsList.add(startDate);
        argsList.add(endDate);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        sql.append(" group by marketplace_id having orderNumDoris > 0 order by orderNumDoris desc limit 1 ");

        List<AmazonAdCampaignAll> campaigns = getJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            AmazonAdCampaignAll campaign = new AmazonAdCampaignAll();
            campaign.setMarketplaceId(rs.getString("marketplace_id"));
            return campaign;
        }, argsList.toArray());
        if (CollectionUtils.isNotEmpty(campaigns)) {
            return campaigns.get(0);
        }
        return null;
    }

    @Override
    public List<AmazonAdCampaignDorisAllReport> listByCampaignIdAndDate(Integer puid, List<Integer> shopId, List<String> ids, List<String> date, List<String> idDate) {
        List<Object> argsList = new ArrayList<>();
        Set<String> field = new HashSet<>(Lists.newArrayList("costDoris","totalSalesDoris", "adSalesDoris", "impressionsDoris", "clicksDoris", "orderNumDoris", "adOrderNumDoris", "saleNumDoris", "adSaleNumDoris"));
        StringBuilder selectSql = new StringBuilder(" select campaign_id, count_day,")
                .append(field.stream().map(SQL_MAP_DATE::get).collect(Collectors.joining(",")));
        selectSql.append(" from ods_t_amazon_ad_campaign_all_report  where puid = ? and is_summary = 1 ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("shop_id", shopId, argsList));
        selectSql.append(SqlStringUtil.dealInList("count_day", date, argsList));
        if (ids.size() < 1000) {
            selectSql.append(SqlStringUtil.dealInList("campaign_id", ids, argsList));
        } else {
            selectSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", ids, argsList));
        }
        if (idDate.size() < 1000) {
            selectSql.append(SqlStringUtil.dealInList("CONCAT_WS('|', campaign_id, `count_day`)", idDate, argsList));
        }
        return getJdbcTemplate().query(selectSql.toString(), getRowMapper(), argsList.toArray());
    }

    private static final Map<String, String> SQL_MAP_DATE = Collections.unmodifiableMap(new HashMap<String, String>() {
        {
            put("costDoris", "IFNULL(cost,0) `costDoris`");
            put("totalSalesDoris", "IFNULL(if (type = 'sp', sales7d,sales14d),0) totalSalesDoris");
            put("adSalesDoris", "IFNULL(if (type = 'sp', sales7d_same_sku,sales14d_same_sku),0) adSalesDoris");
            put("impressionsDoris", "IFNULL(`impressions`,0) impressionsDoris");
            put("clicksDoris", "IFNULL(`clicks`,0) clicksDoris");
            put("orderNumDoris", "IFNULL(if (type = 'sp' , conversions7d,conversions14d),0) orderNumDoris");
            put("adOrderNumDoris", "IFNULL(if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`),0) adOrderNumDoris");
            put("saleNumDoris", "IFNULL(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`)),0) saleNumDoris");
            put("adSaleNumDoris", "IFNULL(if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`),0) adSaleNumDoris");
        }
    });


    @Override
    public List<AdHomePerformancedto> totalBudgetByType(Integer puid, List<Integer> shopId, String status) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder(" select type,sum(budget) totalBudget from ods_t_amazon_ad_campaign_all where puid = ?");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("shop_id", shopId, argsList));
        if (StringUtils.isNotBlank(status)) {
            List<String> statusList = StringUtil.splitStr(status, ",");
            selectSql.append(SqlStringUtil.dealInList("state", statusList, argsList));
        }
        selectSql.append(" group by type ");
        return getJdbcTemplate().query(selectSql.toString(), (re, i) -> AdHomePerformancedto.builder()
                .type(Optional.of(re.getString("type")).orElse(Constants.SP))
                .adCost(Optional.ofNullable(re.getBigDecimal("totalBudget")).orElse(BigDecimal.ZERO))
                .build(), argsList.toArray());
    }

    @Override
    public List<AdHomePerformancedto> totalBudgetByTypeAndRate(Integer puid, List<Integer> shopId, String status, String month) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder(" select aca.type type, sum(aca.budget * c.rate) totalBudget from ods_t_amazon_ad_campaign_all aca  join (select * from dim_currency_rate where puid = ? and `to` = 'USD' ) c on aca.puid = c.puid and c.month = ?  join dim_marketplace_info m on m.marketplace_id = aca.marketplace_id and c.`from` = m.currency  where aca.puid = ?");
        argsList.add(puid);
        argsList.add(Integer.parseInt(month));
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("aca.shop_id", shopId, argsList));
        if (StringUtils.isNotBlank(status)) {
            List<String> statusList = StringUtil.splitStr(status, ",");
            selectSql.append(SqlStringUtil.dealInList("aca.state", statusList, argsList));
        }
        selectSql.append(" group by aca.type ");
        return getJdbcTemplate().query(selectSql.toString(), (re, i) -> AdHomePerformancedto.builder()
                .type(Optional.of(re.getString("type")).orElse("sp"))
                .adCost(Optional.ofNullable(re.getBigDecimal("totalBudget")).orElse(BigDecimal.ZERO))
                .build(), argsList.toArray());
    }

    @Override
    public List<String> getValidRecordByDate(Integer puid, List<Integer> shopIdList, String startDate, CampaignPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder(" select distinct campaign_id from ods_t_amazon_ad_campaign_all_report ");
        sql.append(" where puid = ? and count_day >= ? and impressions > 0 ");
        argsList.add(puid);
        argsList.add(startDate);
        sql.append(SqlStringUtil.dealInList(" shop_id ", shopIdList, argsList));
        sql.append(" and campaign_id in ( select c.campaign_id campaign_id from  ods_t_amazon_ad_campaign_all c ");
        Set<String> reportColumnList = new HashSet<>();
        if (param.getUseAdvanced()) {
            reportColumnList.addAll(getSelectKey(param));
        }
        String joinSql = joinSql(param, false, argsList, reportColumnList,true);
        sql.append(joinSql);
        sql.append(" )");
        return getJdbcTemplate().query(sql.toString(), (rs, rowNum) -> rs.getString("campaign_id"),argsList.toArray());
    }

    @Override
    public List<AmazonAdPortfolioDorisSumReport> listByPortfolio(Integer puid, List<Integer> shopId, String startDate, String endDate, List<String> portfolioIdList) {
        List<Object> argsList = new ArrayList<>();
        Set<String> field = new HashSet<>(Lists.newArrayList("costDoris", "totalSalesDoris", "impressionsDoris", "clicksDoris", "orderNumDoris", "saleNumDoris"));
        StringBuilder selectSql = new StringBuilder(" select c.portfolio_id portfolio_id,")
                .append(field.stream().map(SQL_MAP::get).collect(Collectors.joining(",")));
        selectSql.append(" from ods_t_amazon_ad_campaign_all c join ods_t_amazon_ad_campaign_all_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.campaign_id=c.campaign_id and r.puid = ? and r.is_summary = 1 and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(puid);
        argsList.add(startDate);
        argsList.add(endDate);
        selectSql.append(SqlStringUtil.dealInList("r.shop_id", shopId, argsList));
        selectSql.append(" where c.puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("c.shop_id", shopId, argsList));
        selectSql.append(SqlStringUtil.dealBitMapDorisInList("c.portfolio_id", portfolioIdList, argsList));
        selectSql.append(" group by c.portfolio_id ");
        return getJdbcTemplate().query(selectSql.toString(), new ObjectMapper<>(AmazonAdPortfolioDorisSumReport.class), argsList.toArray());
    }

    @Override
    public List<AmazonAdPortfolioDorisAllReport> countCampaignByPortfolio(Integer puid, List<Integer> shopId, List<String> portfolioIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder(" select c.portfolio_id portfolio_id,count(distinct c.campaign_id) campaign_count");
        selectSql.append(" from ods_t_amazon_ad_campaign_all c ");
        selectSql.append(" where c.puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("c.shop_id", shopId, argsList));
        selectSql.append(SqlStringUtil.dealBitMapDorisInList("c.portfolio_id", portfolioIdList, argsList));
        selectSql.append(" group by c.portfolio_id ");
        return getJdbcTemplate().query(selectSql.toString(), new ObjectMapper<>(AmazonAdPortfolioDorisAllReport.class), argsList.toArray());
    }

    @Override
    public Page<AmazonAdCampaignAll> list4AdTag(TagAdCampaignListParam param) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select marketplace_id, shop_id, campaign_id, name,")
                .append(" type, portfolio_id, state, serving_status from ods_t_amazon_ad_campaign_all c")
                .append(" where puid=? ");
        args.add(param.getPuid());
        sql.append(SqlStringUtil.dealInList("shop_id", param.getShopIds(), args));
        List<String> portfolioIds = param.getPortfolioIds();
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioIds.size() == 1 && portfolioIds.contains("-1")) {
                sql.append(" and portfolio_id is null ");
            } else if (portfolioIds.contains("-1")) {
                sql.append(" and ( ").append(SqlStringUtil.dealInListNotAnd("portfolio_id", portfolioIds, args))
                        .append(" or portfolio_id is null ) ");
            } else {
                sql.append(SqlStringUtil.dealBitMapDorisInList("portfolio_id", portfolioIds, args));
            }
        }
        if (CollectionUtils.isNotEmpty(param.getAdTypes())) {
            sql.append(SqlStringUtil.dealInList("type", param.getAdTypes(), args));
        }
        if (StringUtils.isNotBlank(param.getState())) {
            sql.append(" and state = ? ");
            args.add(param.getState());
        }
        if (CollectionUtils.isNotEmpty(param.getServingStatus())) {
            List<String> servingStatusList = param.getServingStatus().stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
            sql.append(SqlStringUtil.dealInList("serving_status", servingStatusList, args));
        }
        if (StringUtils.isNotBlank(param.getCampaignName())) {
            sql.append(" and lower(name) like ? ");
            args.add("%" + SqlStringUtil.dealLikeSql(param.getCampaignName().trim().toLowerCase()) + "%");
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", param.getCampaignIds(), args));
        }
        if (Boolean.TRUE.equals(param.isOnlyNoTagCampaign()) && CollectionUtils.isNotEmpty(param.getTagGroupIds())) {
            // 只筛选自己能看到的标签
            sql.append(" and campaign_id not in (")
                    .append(" select relation_id from ods_t_ad_manage_tag_relation")
                    .append(" where puid=? and type=0 and del_flag=0 and tag_id is not null and tag_id>0 ");
            args.add(param.getPuid());
            sql.append(SqlStringUtil.dealBitMapDorisInList("group_id", param.getTagGroupIds(), args)).append(")");
        }
        sql.append(" limit 100000 ");

        String selectSql = "select * from (" + sql + ") st order by campaign_id";
        String countSql = "select count(*) from (" + sql + ") ct";

        Object[] argArray = args.toArray();
        return getPageResultByClass(param.getPageNum(), param.getPageSize(), countSql, argArray,
                selectSql, argArray, AmazonAdCampaignAll.class);
    }

    @Override
    public List<LimitCostShopInfo> listLimitCostShopInfo(Integer puid, String startDate, String endDate, BigDecimal cost){
        List<Object> argsList = new ArrayList<>();
        String end = DateUtil.dateToStrWithFormat(DateUtil.strToDate(endDate, "yyyy-MM-dd"), "yyyyMM");
        StringBuilder selectSql = new StringBuilder(" select  a.shop_id as shopId, a.name as shopName, r.cost * d.rate as cost, a.selling_partner_id as sellerId, a.region as region  from ( ");
        selectSql.append(" select puid, shop_id, sum(cost) as cost ");
        selectSql.append(" from ods_t_amazon_ad_campaign_all_report ");
        selectSql.append(" where count_day >= ? and count_day <= ? and is_summary = 1 ");
        selectSql.append(" group by puid , shop_id) r ");
        selectSql.append(" join dim_t_shop_auth a on r.puid = a.puid and r.shop_id = a.shop_id ");
        selectSql.append(" join dim_marketplace_info m on m.marketplace_id = a.marketplace_id ");
        selectSql.append(" join ( select rate, month, `from`, puid  from dim_currency_rate ");
        selectSql.append(" where `to` = 'USD'  and month = ? ) d on d.puid = r.puid and d.`from` = m.currency ");
        selectSql.append(" where r.puid = ? ");
        selectSql.append(" having r.cost * d.rate >= ? ");
        selectSql.append(" order by cost desc  ");
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(end);
        argsList.add(puid);
        argsList.add(cost);
        return getJdbcTemplate().query(selectSql.toString(), new ObjectMapper<>(LimitCostShopInfo.class), argsList.toArray());
    }
}
