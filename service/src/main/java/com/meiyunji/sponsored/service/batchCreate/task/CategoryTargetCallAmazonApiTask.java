package com.meiyunji.sponsored.service.batchCreate.task;

import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.amazon.advertising.spV3.enumeration.SpV3ExpressionEnum;
import com.amazon.advertising.spV3.enumeration.SpV3StateEnum;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.amazon.advertising.spV3.targeting.CreateSpTargetV3Response;
import com.amazon.advertising.spV3.targeting.ListSpTargetV3Response;
import com.amazon.advertising.spV3.targeting.TargetSpV3Client;
import com.amazon.advertising.spV3.targeting.entity.*;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchTargetingDao;
import com.meiyunji.sponsored.service.batchCreate.dto.task.BatchCreateReturnDto;
import com.meiyunji.sponsored.service.batchCreate.dto.task.CallAmazonApiTaskResultDto;
import com.meiyunji.sponsored.service.batchCreate.enums.AdAmazonErrorTypeEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchConstants;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdLevelStatusEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdTaskLevelEnum;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchTargeting;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcTargetingApiService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdTargeting;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 商品投放批量处理任务
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-11-28  14:20
 */
@Component
@Slf4j
public class CategoryTargetCallAmazonApiTask extends AbstractCallAmazonApiTask {
    @Autowired
    private IAmazonAdBatchTargetingDao amazonAdBatchTargetingDao;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private TaskStatusHelper taskStatusHelper;
    @Autowired
    private CpcTargetingApiService cpcTargetingApiService;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Autowired
    private IDorisService dorisService;

    public void call(CallAmazonApiTaskResultDto resultDto) {
        log.info("sp batch create, create category target, batch traceId: {}, taskId: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId());
        if (CollectionUtils.isEmpty(resultDto.getSuccessIdList())) {
            return ;
        }
        Integer puid = resultDto.getPuid();
        Integer shopId = resultDto.getShopId();
        Long taskId = resultDto.getTaskId();
        String traceId = resultDto.getTraceDto().getTraceId();
        //1、获取redisson分布式锁，锁标识为："sp_batch:category_targeting:"+ task_id;
        RLock lock = redissonClient.getLock(SpBatchCreateAdTaskLevelEnum.LEVEL_CATEGORY_TARGETING.getLockKey() + taskId);
        boolean b = false;
        try {
            b = lock.tryLock(SpBatchConstants.TRY_LOCK_SECONDS, SpBatchConstants.LOCK_MINUTES, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.info("category target task tryLock err,puid：{},shopId:{},taskId:{},traceId:{}", puid, shopId, taskId, traceId);
            return ;
        }
        if (!b) {
            log.info("category target task tryLock err,puid：{},shopId:{},taskId:{},traceId:{}", puid, shopId, taskId, traceId);
            return ;
        }

        List<AmazonAdBatchTargeting> targets = new ArrayList<>();
        try {
            ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(resultDto.getShopId(), resultDto.getPuid());
            //2、根据传入的id获取不为成功或者终止状态的商品投放集合
            List<Long> ids = resultDto.getSuccessIdList();
            if (resultDto.isCurrentLevel()) {
                targets = amazonAdBatchTargetingDao.listByIdList(puid, shopId, ids,
                        Lists.newArrayList(SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode()), Constants.MANUAL);
            } else {
                targets = amazonAdBatchTargetingDao.listByGroupIdList(puid, shopId, taskId, ids,
                        Lists.newArrayList(SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode()), Constants.MANUAL);
            }
            if (CollectionUtils.isEmpty(targets)) {
                return ;
            }
            //3、分片请求亚马逊返回列表分成成功失败可重试三种类型
            Map<Integer, String> succMap = new HashMap<>();
            Map<Integer, String> errMap = new HashMap<>();
            List<Integer> retryList = new ArrayList<>();
            List<List<AmazonAdBatchTargeting>> targetingsPartition = Lists.partition(targets, SpBatchConstants.REQUEST_PARTITION_SIZE);
            for (int i = 0; i < targetingsPartition.size(); i++) {
                BatchCreateReturnDto batchCreateReturnDto = this.amazonApiCreateByPartition(traceId, taskId, shop, targetingsPartition.get(i), i * SpBatchConstants.REQUEST_PARTITION_SIZE);
                succMap.putAll(batchCreateReturnDto.getSuccMap());
                errMap.putAll(batchCreateReturnDto.getErrMap());
                retryList.addAll(batchCreateReturnDto.getRetryList());
            }

            //4、成功处理
            if (succMap.size() > 0) {
                List<AmazonAdTargeting> succTargetings = new ArrayList<>();
                Map<Long, String> succBatchTargetIdMap = new HashMap<>();
                Date date = new Date();
                for (Map.Entry<Integer, String> succMapEntry : succMap.entrySet()) {
                    AmazonAdBatchTargeting amazonAdBatchTargeting = targets.get(succMapEntry.getKey());
                    succBatchTargetIdMap.put(amazonAdBatchTargeting.getId(), succMapEntry.getValue());
                    //构建日志的po
                    AmazonAdTargeting amazonAdTargeting = this.batchTarget2Target(amazonAdBatchTargeting);
                    amazonAdTargeting.setTargetId(succMapEntry.getValue());
                    amazonAdTargeting.setCreateTime(date);
                    amazonAdTargeting.setUpdateTime(date);
                    succTargetings.add(amazonAdTargeting);
                }
                //插入成功的商品投放
                amazonAdTargetDaoRoutingService.insertOnDuplicateKeyUpdate(puid, succTargetings, Constants.TARGETING_TYPE_ASIN);
                //写入doris
                saveDoris(succTargetings);
                //修改当前层级任务的状态为成功
                amazonAdBatchTargetingDao.updateSuccTaskStatusByIdList(puid, succBatchTargetIdMap);
                //记录成功日志
                this.collectSuccLog(succTargetings, resultDto.getLoginIp());
                //同步投放表达式resolvedExpression
                String campaignIds = succTargetings.stream().map(AmazonAdTargeting::getCampaignId).collect(Collectors.joining(","));
                String groupIds = succTargetings.stream().map(AmazonAdTargeting::getAdGroupId).collect(Collectors.joining(","));
                String targetIds = succTargetings.stream().map(AmazonAdTargeting::getTargetId).collect(Collectors.joining(","));
                cpcTargetingApiService.syncTargetings(shop, campaignIds, groupIds, targetIds);
                log.info("sp batch create, create category target success, batch traceId: {}, taskId: {}, succBatchProductIdMap: {}", traceId, resultDto.getTaskId(), succBatchTargetIdMap);
            }

            //5、失败处理
            List<AmazonAdTargeting> errTargetings = new ArrayList<>();
            if (errMap.size() > 0) {
                Map<Long, String> idMsgMap = new HashMap<>();
                for (Map.Entry<Integer, String> errMapEntry : errMap.entrySet()) {
                    AmazonAdBatchTargeting amazonAdBatchTargeting = targets.get(errMapEntry.getKey());
                    idMsgMap.put(amazonAdBatchTargeting.getId(), errMapEntry.getValue());
                    //构建日志po
                    AmazonAdTargeting amazonAdTargeting = this.batchTarget2Target(amazonAdBatchTargeting);
                    amazonAdTargeting.setError(errMapEntry.getValue());
                    errTargetings.add(amazonAdTargeting);
                }
                //修改当前层级任务状态为失败
                taskStatusHelper.batchUpdateErrStatus(puid, taskId, idMsgMap, SpBatchCreateAdTaskLevelEnum.LEVEL_TARGETING,  null, true);
                log.info("sp batch create, create category target error, batch traceId: {}, taskId: {}, idMsgMap: {}", traceId, resultDto.getTaskId(), idMsgMap);
            }

            //6、重试处理
            if (retryList.size() > 0) {
                List<Long> retryIdList = new ArrayList<>();
                Map<Long, String> errIdMap = new HashMap<>();
                for (Integer index : retryList) {
                    AmazonAdBatchTargeting amazonAdBatchTargeting = targets.get(index);
                    AmazonAdTargeting amazonAdTargeting = this.batchTarget2Target(amazonAdBatchTargeting);
                    //若重试超过三次则直接置为失败
                    if (amazonAdBatchTargeting.getExecuteCount() + 1 >= SpBatchConstants.EXECUTE_COUNT_LIMIT) {
                        errIdMap.put(amazonAdBatchTargeting.getId(), SpBatchConstants.RETRY_ERROR_MSG);
                        amazonAdTargeting.setError(SpBatchConstants.RETRY_ERROR_MSG);
                    } else {
                        retryIdList.add(amazonAdBatchTargeting.getId());
                        amazonAdTargeting.setError(SpBatchConstants.RETRY_MSG);
                    }
                    errTargetings.add(amazonAdTargeting);
                }
                if (CollectionUtils.isNotEmpty(retryIdList)) {
                    amazonAdBatchTargetingDao.updateRetryTaskStatusByIdList(puid, retryIdList, DateUtil.addSecond(new Date(), SpBatchConstants.RETRY_AFTER_SECONDS));
                    log.info("sp batch create, create category target retry, batch traceId: {}, taskId: {}, retryIdList: {}", traceId, resultDto.getTaskId(), StringUtils.join(retryIdList, ","));
                }
                if (!errIdMap.isEmpty()) {
                    amazonAdBatchTargetingDao.updateErrTaskStatusByIdList(puid, errIdMap, true);
                    log.info("sp batch create, create category target error, batch traceId: {}, taskId: {}, errIdMap: {}", traceId, resultDto.getTaskId(), errIdMap);
                }
            }
            //7、记录失败日志
            this.collectFailLog(errTargetings, resultDto.getLoginIp());
        } catch (Exception e) {
            log.error(String.format("sp batch create category target, exception, batch traceId: %s, taskId: %s", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId()), e);
            //有异常将本层级全部改为重试状态
            if (CollectionUtils.isEmpty(targets)) {
                List<Long> idList = targets.stream().map(AmazonAdBatchTargeting::getId).collect(Collectors.toList());
                amazonAdBatchTargetingDao.updateRetryTaskStatusByIdList(puid, idList, DateUtil.addSecond(new Date(), SpBatchConstants.RETRY_AFTER_SECONDS));
            }
        } finally {
            //8、释放redission锁
            lock.unlock();
        }
    }

    /**
     * 构建成功的日志
     */
    private void collectSuccLog(List<AmazonAdTargeting> amazonAdTargetings, String loginIp) {
        List<AdManageOperationLog> targetLogs = new ArrayList<>();
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return ;
        }
        for (AmazonAdTargeting taraget: amazonAdTargetings) {
            AdManageOperationLog targetLog = adManageOperationLogService.getTargetsLog(null, taraget);
            targetLog.setIp(loginIp);
            targetLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            targetLogs.add(targetLog);
        }
        adManageOperationLogService.batchLogsMergeByAdGroup(targetLogs);
    }

    /**
     * 构建失败的日志（异常为广告组维度）
     */
    private void collectFailLog(List<AmazonAdTargeting> amazonAdTargetings, String loginIp) {
        List<AdManageOperationLog> targetLogs = new ArrayList<>();
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return ;
        }
        Map<String, List<AmazonAdTargeting>> targetGroupMap = amazonAdTargetings.stream().collect(Collectors.groupingBy(AmazonAdTargeting::getAdGroupId));
        Map<String, String> groupMsgMap = new HashMap<>();
        StringBuilder msgSb;
        for (Map.Entry<String, List<AmazonAdTargeting>> entry : targetGroupMap.entrySet()) {
            msgSb = new StringBuilder();
            List<AmazonAdTargeting> targetingList = entry.getValue();
            for (AmazonAdTargeting targeting : targetingList) {
                msgSb.append("targetValue:").append(targeting.getTargetingValue()).append(",desc:").append(targeting.getError()).append(";");
            }
            groupMsgMap.put(entry.getKey(), msgSb.toString());
        }

        String err = "创建商品投放失败";
        for (AmazonAdTargeting target: amazonAdTargetings) {
            AdManageOperationLog targetLog = adManageOperationLogService.getTargetsLog(null, target);
            targetLog.setIp(loginIp);
            targetLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            targetLog.setResultInfo(groupMsgMap.getOrDefault(target.getAdGroupId(), err));
            targetLogs.add(targetLog);
        }
        adManageOperationLogService.batchLogsMergeByAdGroup(targetLogs);
    }

    /**
     * 批量调亚马逊创建接口
     * @param shop
     * @param targets
     * @param offset 偏移量，返回index加上这个值
     * @return
     */
    private BatchCreateReturnDto amazonApiCreateByPartition(String traceId, Long taskId, ShopAuth shop, List<AmazonAdBatchTargeting> targets, int offset) {
        BatchCreateReturnDto dto = new BatchCreateReturnDto();
        Map<Integer, String> succMap = new HashMap<>();
        Map<Integer, String> errMap = new HashMap<>();
        List<Integer> retryList = new ArrayList<>();
        dto.setSuccMap(succMap);
        dto.setErrMap(errMap);
        dto.setRetryList(retryList);
        List<Integer> duplicateValueErrorList = new ArrayList<>();
        //组装亚马逊请求
        List<CreateTargetEntityV3> targetEntityV3List = Lists.newArrayList();
        for (AmazonAdBatchTargeting amazonAdBatchTargeting : targets) {
            targetEntityV3List.add(this.batchTarget2TargetEntityV3(amazonAdBatchTargeting));
        }
        //批量将这些数据提交给亚马逊。
        String profileId = targets.get(0).getProfileId();

        CreateSpTargetV3Response response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createTargets(shop.getAdAccessToken(), profileId,
                shop.getMarketplaceId(), targetEntityV3List, Boolean.TRUE);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            log.info("sp batch create category target, response is null or http code not 200, batch traceId: {}, taskId: {}", traceId, taskId);
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createTargets(shop.getAdAccessToken(), profileId,
                    shop.getMarketplaceId(), targetEntityV3List, Boolean.TRUE);
        }

        //异常返回，全部加入到重试列表
        if (response == null || response.getData() == null || response.getStatusCode() == -1 || response.getStatusCode() == 429 || response.getStatusCode() == 500) {
            log.info("sp batch create, create category target response none data, fail all, batch traceId: {}, taskId: {}", traceId, taskId);
            for (int i = 0; i < targets.size(); i++) {
                retryList.add(i + offset);
            }
            return dto;
        }

        //4、活动提交给亚马逊之后，需要处理其响应，将成功的和失败的分成2组。
        String errMsg = "创建商品投放失败";
        if (response.getData() != null && response.getData().getTargetingClauses() != null) {
            List<TargetSuccessResultV3> success = response.getData().getTargetingClauses().getSuccess();
            List<ErrorItemResultV3> errorItemResultV3s = response.getData().getTargetingClauses().getError();
            //成功的商品投放，批量和单个商品投放都设置投放id
            for (TargetSuccessResultV3 targetSuccessResultV3 : success) {
                succMap.put(offset + targetSuccessResultV3.getIndex(), targetSuccessResultV3.getTargetId());
            }
            //失败的商品投放，批量和单个商品投放都设置错误信息
            for (ErrorItemResultV3 targetResult : errorItemResultV3s) {
                //如果错误信息为创建重复，直接设置为创建成功，后续查询投放id
                if (AdAmazonErrorTypeEnum.DUPLICATE_VALUE_ERROR.getType().equals(targetResult.getErrors().get(0).getErrorType())) {
                    duplicateValueErrorList.add(targetResult.getIndex());
                    continue;
                }
                errMap.put(offset + targetResult.getIndex(), targetResult.getErrors().get(0).getErrorMessage());
            }
        } else if (response.getError() != null) {
            if (StringUtils.isNotBlank(response.getError().getMessage())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if (CollectionUtils.isNotEmpty(response.getError().getErrors())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }
            for (int i = 0; i < targets.size(); i++) {
                errMap.put(offset + i, errMsg);
            }
        }

        //对创建重复的调用列表接口获取投放id，若有则加入到成功列表中
        if (CollectionUtils.isNotEmpty(duplicateValueErrorList)) {
            Map<Integer, CreateTargetEntityV3> duplicateValueErrorTargetingMap = duplicateValueErrorList.stream().collect(Collectors.toMap(e -> e, targetEntityV3List::get));
            Map<Integer, String> retrySuccMap = this.duplicateTargetGetId(shop, profileId, duplicateValueErrorTargetingMap, offset);
            succMap.putAll(retrySuccMap);
            retryList.addAll(duplicateValueErrorList.stream().filter(e -> !succMap.containsKey(e + offset)).collect(Collectors.toList()));
        }
        return dto;
    }

    /**
     * 创建重复的商品投放获取投放id，并且全部加入到创建成功列表
     */
    private Map<Integer, String> duplicateTargetGetId(ShopAuth shop, String profileId, Map<Integer, CreateTargetEntityV3> duplicateValueErrorTargetingMap, int offset) {
        //获取重复成功的index和投放id
        Map<Integer, String> succMap = new HashMap<>();
        //投放标识(广告组+表达式)与index的map
        Map<String, Integer> expresionIndexMap = new HashMap<>();
        //获取创建重复的活动和广告组
        List<String> campaignIds = new ArrayList<>();
        List<String> adGroupIds = new ArrayList<>();
        duplicateValueErrorTargetingMap.forEach((k, v) -> {
            campaignIds.add(v.getCampaignId());
            adGroupIds.add(v.getAdGroupId());
            expresionIndexMap.put(v.getAdGroupId() + "#" + JSONUtil.objectToJson(v.getExpression()), k);
        });
        TargetSpV3Client client = TargetSpV3Client.getInstance();
        // 刷新token次数，保证不会无限刷新
        int refreshedToken = 0;
        String nextToken = null;
        ListSpTargetV3Response response;
        Map<String, String> expresionIdMap;
        while (true) {
            response = client.listTarget(shop.getAdAccessToken(), profileId, shop.getMarketplaceId(), campaignIds, adGroupIds, null, nextToken);
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode() == AmazonAdUtils.rateLimitingCode) {
                log.info("batch create category targetings rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = client.listTarget(shop.getAdAccessToken(), profileId, shop.getMarketplaceId(), campaignIds, adGroupIds, null, nextToken);
                retry++;
            }
            //刷新token
            if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401 && refreshedToken < 2) {
                shopAuthService.refreshCpcAuth(shop);
                refreshedToken++;
                continue;
            }
            if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getTargetingClauses())) {
                break;
            }
            //将成功返回的targetId存入成功列表中
            expresionIdMap = response.getData().getTargetingClauses().stream().collect(Collectors.toMap(e -> e.getAdGroupId() + "#" + JSONUtil.objectToJson(e.getExpression()), TargetEntityV3::getTargetId));
            Iterator<Map.Entry<String, Integer>> iterator = expresionIndexMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, Integer> entry = iterator.next();
                if (expresionIdMap.containsKey(entry.getKey())) {
                    succMap.put(offset + entry.getValue(), expresionIdMap.get(entry.getKey()));
                    iterator.remove();
                }
            }
            //nextToken刷新
            if (StringUtils.isNotBlank(response.getData().getNextToken())){
                nextToken = response.getData().getNextToken();
            } else {
                break;
            }
        }
        return succMap;
    }

    /**
     * 批量创建商品投放实体类转商品投放实体类
     */
    private AmazonAdTargeting batchTarget2Target(AmazonAdBatchTargeting amazonAdBatchTargeting) {
        AmazonAdTargeting amazonAdTargeting = new AmazonAdTargeting();
        Expression expression;

        amazonAdTargeting.setPuid(amazonAdBatchTargeting.getPuid());
        amazonAdTargeting.setShopId(amazonAdBatchTargeting.getShopId());
        amazonAdTargeting.setMarketplaceId(amazonAdBatchTargeting.getMarketplaceId());
        amazonAdTargeting.setAdGroupId(amazonAdBatchTargeting.getAmazonAdGroupId());
        amazonAdTargeting.setCampaignId(amazonAdBatchTargeting.getAmazonCampaignId());
        amazonAdTargeting.setProfileId(amazonAdBatchTargeting.getProfileId());
        amazonAdTargeting.setExpressionType(Constants.MANUAL);
        amazonAdTargeting.setState(CpcStatusEnum.enabled.name());
        amazonAdTargeting.setType(amazonAdBatchTargeting.getType());
        amazonAdTargeting.setBid(amazonAdBatchTargeting.getBid() != null ? amazonAdBatchTargeting.getBid().doubleValue() : null);
        amazonAdTargeting.setCreateId(amazonAdBatchTargeting.getCreateId());

        if (amazonAdBatchTargeting.getSuggested() != null) {
            amazonAdTargeting.setSuggested(amazonAdBatchTargeting.getSuggested());
        }
        if (amazonAdBatchTargeting.getRangeStart() != null) {
            amazonAdTargeting.setRangeStart(amazonAdBatchTargeting.getRangeStart());
        }
        if (amazonAdBatchTargeting.getRangeEnd() != null) {
            amazonAdTargeting.setRangeEnd(amazonAdBatchTargeting.getRangeEnd());
        }

        List<Expression> expressions = new ArrayList<>();
        if (TargetTypeEnum.asin.name().equals(amazonAdBatchTargeting.getType())) {
            expression = new Expression();
            expression.setType(amazonAdBatchTargeting.getSelectType());
            expression.setValue(amazonAdBatchTargeting.getAsin());
            expressions.add(expression);
            amazonAdTargeting.setSelectType(amazonAdBatchTargeting.getSelectType());
            amazonAdTargeting.setTargetingValue(amazonAdBatchTargeting.getAsin());
            amazonAdTargeting.setImgUrl(amazonAdBatchTargeting.getImgUrl());
            amazonAdTargeting.setTitle(amazonAdBatchTargeting.getTitle());
        } else {
            expression = new Expression();
            expression.setType(ExpressionEnum.asinCategorySameAs.value());
            expression.setValue(amazonAdBatchTargeting.getCategoryId());
            expressions.add(expression);
            amazonAdTargeting.setTargetingValue(amazonAdBatchTargeting.getCategoryName());

            if (StringUtils.isNotBlank(amazonAdBatchTargeting.getBrand())) {
                expression = new Expression();
                expression.setType(ExpressionEnum.asinBrandSameAs.value());
                expression.setValue(amazonAdBatchTargeting.getBrand());
                expressions.add(expression);
            }
            if (amazonAdBatchTargeting.getMinPrice() != null || amazonAdBatchTargeting.getMaxPrice() != null) {
                expression = new Expression();
                if (amazonAdBatchTargeting.getMinPrice() == null) {
                    expression.setType(ExpressionEnum.asinPriceLessThan.value());
                    expression.setValue(amazonAdBatchTargeting.getMaxPrice().toString());
                    expressions.add(expression);
                } else if (amazonAdBatchTargeting.getMaxPrice() == null) {
                    expression.setType(ExpressionEnum.asinPriceGreaterThan.value());
                    expression.setValue(amazonAdBatchTargeting.getMinPrice().toString());
                    expressions.add(expression);
                } else {
                    expression.setType(ExpressionEnum.asinPriceBetween.value());
                    expression.setValue(amazonAdBatchTargeting.getMinPrice() + "-" + amazonAdBatchTargeting.getMaxPrice());
                    expressions.add(expression);
                }
            }
            if (amazonAdBatchTargeting.getMinReviewRating() != null || amazonAdBatchTargeting.getMaxReviewRating() != null) {
                expression = new Expression();
                if (amazonAdBatchTargeting.getMinReviewRating() == null) {
                    expression.setType(ExpressionEnum.asinReviewRatingLessThan.value());
                    expression.setValue(String.valueOf(amazonAdBatchTargeting.getMaxReviewRating()));
                    expressions.add(expression);
                } else if (amazonAdBatchTargeting.getMaxReviewRating() == null) {
                    expression.setType(ExpressionEnum.asinReviewRatingGreaterThan.value());
                    expression.setValue(String.valueOf(amazonAdBatchTargeting.getMinReviewRating()));
                    expressions.add(expression);
                } else {
                    expression.setType(ExpressionEnum.asinReviewRatingBetween.value());
                    expression.setValue(amazonAdBatchTargeting.getMinReviewRating() + "-" + amazonAdBatchTargeting.getMaxReviewRating());
                    expressions.add(expression);
                }
            }
            if (amazonAdBatchTargeting.getPrimeShippingEligible() != null) {
                expression = new Expression();
                expression.setType(ExpressionEnum.asinIsPrimeShippingEligible.value());
                expression.setValue(amazonAdBatchTargeting.getPrimeShippingEligible().toString());
                expressions.add(expression);
            }
        }
        amazonAdTargeting.setExpression(JSONUtil.objectToJson(expressions));
        return amazonAdTargeting;
    }

    /**
     * 批量创建商品投放类转亚马逊创建投放类
     */
    private CreateTargetEntityV3 batchTarget2TargetEntityV3(AmazonAdBatchTargeting amazonAdBatchTargeting) {
        CreateTargetEntityV3 targetingClause = new CreateTargetEntityV3();
        targetingClause.setCampaignId(amazonAdBatchTargeting.getAmazonCampaignId());
        targetingClause.setAdGroupId(amazonAdBatchTargeting.getAmazonAdGroupId());
        targetingClause.setExpressionType(amazonAdBatchTargeting.getExpressionType());
        targetingClause.setState(SpV3StateEnum.ENABLED.valueV3());
        targetingClause.setBid(amazonAdBatchTargeting.getBid() != null ? amazonAdBatchTargeting.getBid().doubleValue() : null);
        if (StringUtils.isNotBlank(amazonAdBatchTargeting.getExpression())) {
            List<Expression> expressions = JSONUtil.jsonToArray(amazonAdBatchTargeting.getExpression(), Expression.class);
            if (expressions != null) {
                List<TargetExpression> targetExpressions = new ArrayList<>();
                for (Expression expression : expressions){
                    TargetExpression e = new TargetExpression();
                    e.setType(SpV3ExpressionEnum.getExpressionByValue(expression.getType()).getValueV3());
                    e.setValue(expression.getValue());
                    targetExpressions.add(e);
                }
                targetingClause.setExpression(targetExpressions);
            }
        }
        return targetingClause;
    }

    /**
     * routine load写入doris
     * @param targetingList
     */
    private void saveDoris(List<AmazonAdTargeting> targetingList) {
        try {
            Date date = new Date();
            List<OdsAmazonAdTargeting> collect = targetingList.stream().map(x -> {
                OdsAmazonAdTargeting odsAmazonAdTargeting = new OdsAmazonAdTargeting();
                BeanUtils.copyProperties(x, odsAmazonAdTargeting);
                odsAmazonAdTargeting.setCreateTime(date);
                odsAmazonAdTargeting.setUpdateTime(date);
                if (StringUtils.isNotBlank(odsAmazonAdTargeting.getState())) {
                    odsAmazonAdTargeting.setState(odsAmazonAdTargeting.getState().toLowerCase());
                }
                return odsAmazonAdTargeting;
            }).collect(Collectors.toList());
            dorisService.saveDorisByRoutineLoad(null, collect);
        } catch (Exception e) {
            log.error("sp target save doris error", e);
        }
    }

}
