package com.meiyunji.sponsored.service.multiple.targets.enums;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Getter;

import java.util.HashSet;
import java.util.Set;

/**
 * 投放sb列表页-排序高级刷新字段枚举
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Getter
public enum TargetSbOrderByEnum {
    CREATE_TIME("createTime", " t.create_time ", new HashSet<>()),
    BID("bid", " IFNULL(t.bid, g.bid) ", new HashSet<>()),
    SHOP_NAME("shopName", " CONVERT(s.`name` USING gbk) COLLATE gbk_chinese_ci ", new HashSet<>()),
    AD_COST("adCost", " ifnull(costDoris,0) ", CollectionUtil.newHashSet("costDoris")),
    AD_COST_PERCENTAGE("adCostPercentage", " ifnull(costDoris,0) ", CollectionUtil.newHashSet("costDoris")),
    TOTAL_SALES("adSale", " ifnull(totalSalesDoris,0) ", CollectionUtil.newHashSet("totalSalesDoris")),
    TOTAL_SALES_PERCENTAGE("adSalePercentage", " ifnull(totalSalesDoris,0) ", CollectionUtil.newHashSet("totalSalesDoris")),
    AD_SALES("adSales", " ifnull(adSalesDoris,0) ", CollectionUtil.newHashSet("adSalesDoris")),
    IMPRESSIONS("impressions", " ifnull(impressionsDoris,0) ", CollectionUtil.newHashSet("impressionsDoris")),
    CLICKS("clicks", " ifnull(clicksDoris,0) ", CollectionUtil.newHashSet("clicksDoris")),
    ORDER_NUM("adOrderNum", " ifnull(orderNumDoris,0) ", CollectionUtil.newHashSet("orderNumDoris")),
    ORDER_NUM_PERCENTAGE("adOrderNumPercentage", " ifnull(orderNumDoris,0) ", CollectionUtil.newHashSet("orderNumDoris")),
    AD_ORDER_NUM("adSaleNum", " ifnull(adOrderNumDoris,0) ", CollectionUtil.newHashSet("adOrderNumDoris")),
    SALE_NUM("orderNum", " ifnull(saleNumDoris,0) ", CollectionUtil.newHashSet("saleNumDoris")),
    SALE_NUM_PERCENTAGE("orderNumPercentage", " ifnull(saleNumDoris,0) ", CollectionUtil.newHashSet("saleNumDoris")),
    VIEW_IMPRESSIONS("viewImpressions", " ifnull(viewImpressionsDoris,0) ", CollectionUtil.newHashSet("viewImpressionsDoris")),
    SALES_NEW_TO_BRAND("salesNewToBrandFTD", " ifnull(salesNewToBrand14dDoris,0) ", CollectionUtil.newHashSet("salesNewToBrand14dDoris")),
    ORDERS_NEW_TO_BRAND("ordersNewToBrandFTD", " ifnull(ordersNewToBrand14dDoris,0) ", CollectionUtil.newHashSet("ordersNewToBrand14dDoris")),
    ORDERS_NEW_TO_BRAND_PERCENTAGE_FTD("ordersNewToBrandPercentageFTD", " ifnull(ordersNewToBrand14dDoris/clicksDoris ,0) ", CollectionUtil.newHashSet("ordersNewToBrand14dDoris", "clicksDoris")),
    VIDEO5SECOND_VIEWS("video5SecondViews", "  ifnull(video5secondViewsDoris,0) ", CollectionUtil.newHashSet("video5secondViewsDoris")),
    VIDEO_FIRST_QUARTILE_VIEWS("videoFirstQuartileViews", " ifnull(videoFirstQuartileViewsDoris,0) ", CollectionUtil.newHashSet("videoFirstQuartileViewsDoris")),
    VIDEO_MIDPOINT_VIEWS("videoMidpointViews", " ifnull(videoMidpointViewsDoris,0) ", CollectionUtil.newHashSet("videoMidpointViewsDoris")),
    VIDEO_THIRD_QUARTILE_VIEWS("videoThirdQuartileViews", " ifnull(videoThirdQuartileViewsDoris,0) ", CollectionUtil.newHashSet("videoThirdQuartileViewsDoris")),
    VIDEO_COMPLETE_VIEWS("videoCompleteViews", " ifnull(videoCompleteViewsDoris,0) ", CollectionUtil.newHashSet("videoCompleteViewsDoris")),
    VIDEO_UNMUTES("videoUnmutes", " ifnull(videoUnmutesDoris,0) ", CollectionUtil.newHashSet("videoUnmutesDoris")),
    BRANDED_SEARCHES("brandedSearches", " ifnull(brandedSearches14dDoris,0) ", CollectionUtil.newHashSet("brandedSearches14dDoris")),
    TOP_IMPRESSION_SHARE("topImpressionShare", " maxTopIsDoris ", CollectionUtil.newHashSet("maxTopIsDoris")),
    CPA("cpa", " ifnull(costDoris/orderNumDoris ,0) ", CollectionUtil.newHashSet("costDoris", "orderNumDoris")),
    AD_COST_PER_CLICK("adCostPerClick", " ifnull(costDoris/clicksDoris ,0) ", CollectionUtil.newHashSet("costDoris", "clicksDoris")),
    CTR("ctr", " ifnull(clicksDoris/impressionsDoris ,0) ", CollectionUtil.newHashSet("clicksDoris", "impressionsDoris")),
    CVR("cvr", " ifnull(orderNumDoris/clicksDoris ,0) ", CollectionUtil.newHashSet("orderNumDoris", "clicksDoris")),
    ACOS("acos", " ifnull(costDoris/totalSalesDoris ,0) ", CollectionUtil.newHashSet("costDoris", "totalSalesDoris")),
    ROAS("roas", " ifnull(totalSalesDoris/costDoris ,0) ", CollectionUtil.newHashSet("totalSalesDoris", "costDoris")),
    ACOTS("acots", " ifnull(costDoris/shopSalesDoris ,0) ", CollectionUtil.newHashSet("costDoris")),
    ASOTS("asots", " ifnull(totalSalesDoris/shopSalesDoris ,0) ", CollectionUtil.newHashSet("totalSalesDoris")),
    ADVERTISING_UNIT_PRICE("advertisingUnitPrice", " ifnull(totalSalesDoris/orderNumDoris ,0) ", CollectionUtil.newHashSet("totalSalesDoris", "orderNumDoris")),
    ORDER_RATE_NEW_TO_BRAND_FTD("orderRateNewToBrandFTD", " ifnull(ordersNewToBrand14dDoris/orderNumDoris ,0) ", CollectionUtil.newHashSet("ordersNewToBrand14dDoris", "orderNumDoris")),
    SALES_RATE_NEW_TO_BRAND_FTD("salesRateNewToBrandFTD", " ifnull(salesNewToBrand14dDoris/totalSalesDoris ,0) ", CollectionUtil.newHashSet("salesNewToBrand14dDoris", "totalSalesDoris")),
    VIDEO5_SECOND_VIEW_RATE("video5SecondViewRate", " ifnull(video5secondViewsDoris/impressionsDoris ,0) ", CollectionUtil.newHashSet("video5secondViewsDoris", "impressionsDoris")),
    VIEW_ABILITY_RATE("viewabilityRate", " ifnull(viewImpressionsDoris/impressionsDoris ,0) ", CollectionUtil.newHashSet("viewImpressionsDoris", "impressionsDoris")),
    VIEW_CLICK_THROUGH_RATE("viewClickThroughRate", " ifnull(clicksDoris/viewImpressionsDoris ,0) ", CollectionUtil.newHashSet("clicksDoris", "viewImpressionsDoris")),
    SEARCH_FREQUENCY_RANK("searchFrequencyRank", " ifnull(search_frequency_rank, 2147483647) ", null),
    WEEK_RATIO("weekRatio", " ifnull(round(week_ratio*100,2), -2147483648) ", null),
    ;

    // 排序字段
    private final String code;
    // 字段
    private final String orderBy;
    // 字段
    private final Set<String> columnList;

    TargetSbOrderByEnum(String code, String orderBy, Set<String> columnList) {
        this.code = code;
        this.orderBy = orderBy;
        this.columnList = columnList;
    }

    /**
     * 根据code获取枚举
     */
    public static TargetSbOrderByEnum getEnumByCode(String code) {
        for (TargetSbOrderByEnum orderByEnum : TargetSbOrderByEnum.values()) {
            if (orderByEnum.getCode().equals(code)) {
                return orderByEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取统计字段集合
     */
    public static Set<String> getSetByCode(String code) {
        for (TargetSbOrderByEnum orderByEnum : TargetSbOrderByEnum.values()) {
            if (orderByEnum.getCode().equals(code)) {
                return orderByEnum.getColumnList();
            }
        }
        return new HashSet<>();
    }

    /**
     * 根据code获取排序
     */
    public static String getOrderByByCode(String code , String targetType) {
        if(!"keyword".equals(targetType) && ("searchFrequencyRank".equals(code) || "weekRatio".equals(code))) {
            return TargetSbOrderByEnum.CREATE_TIME.getOrderBy();
        }
        for (TargetSbOrderByEnum orderByEnum : TargetSbOrderByEnum.values()) {
            if (orderByEnum.getCode().equals(code)) {
                return orderByEnum.getOrderBy();
            }
        }
        return TargetSbOrderByEnum.CREATE_TIME.getOrderBy();
    }
}
