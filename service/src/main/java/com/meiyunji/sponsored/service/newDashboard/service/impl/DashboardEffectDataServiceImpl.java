package com.meiyunji.sponsored.service.newDashboard.service.impl;

import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeChartRpcVo;
import com.meiyunji.sponsored.rpc.adCommon.ShopDataChartRpcVo;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardEffectDataResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonMarketingStreamDataDao;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.SplitDateByWeekUtil;
import com.meiyunji.sponsored.service.dataWarehouse.service.DWShopSalesService;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleOrigDto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdPortfolioDao;
import com.meiyunji.sponsored.service.enums.MarketplaceTimeZoneEnum;
import com.meiyunji.sponsored.service.enums.ShopTypeEnum;
import com.meiyunji.sponsored.service.newDashboard.bo.EffectDataBo;
import com.meiyunji.sponsored.service.newDashboard.bo.ShopEffectDataBo;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardRateEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.EffectDataModelTypeEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.ShopDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardEffectDataService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.newDashboard.util.OrderByUtil;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardBaseReqVo;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardEffectDataReqVo;
import com.meiyunji.sponsored.service.reportHour.constants.HourConvert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.format.DateTimeFormat;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-04-07  17:23
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DashboardEffectDataServiceImpl implements IDashboardEffectDataService {

    private final IScVcShopAuthDao shopAuthDao;

    private final IAmazonMarketingStreamDataDao amazonMarketingStreamDataDao;

    private final IOdsAmazonAdCampaignAllReportDao odsAmazonAdCampaignAllReportDao;

    private final IOdsAmazonAdCampaignAllDao odsAmazonAdCampaignAllDao;

    private final DWShopSalesService dwShopSalesService;

    private final IOdsAmazonAdPortfolioDao odsAmazonAdPortfolioDao;
    private final DynamicRefreshConfiguration dynamicRefreshConfiguration;
    private static final int DAY_PARTITION_SIZE = 8;

    @Override
    public DashboardEffectDataResponse.EffectData queryEffectData(DashboardEffectDataReqVo reqVo) {
        DashboardEffectDataResponse.EffectData.Builder effectData = DashboardEffectDataResponse.EffectData.newBuilder();
        //获取店铺是否存在
        List<ShopAuth> shopAuths = shopAuthDao.listByPuidAndMarketplace(reqVo.getPuid(), reqVo.getMarketplaceIdList(), reqVo.getShopIdList());
        List<Integer> shopIds = new ArrayList<>();
        Set<String> marketplaceIdSet = new HashSet<>();
        Set<String> sellerIdSet = new HashSet<>();

        Map<Integer, ShopAuth> shopAuthMap = new HashMap<>();
        for (ShopAuth shopAuth : shopAuths) {
            shopIds.add(shopAuth.getId());
            marketplaceIdSet.add(shopAuth.getMarketplaceId());
            sellerIdSet.add(shopAuth.getSellingPartnerId());
            shopAuthMap.put(shopAuth.getId(), shopAuth);
        }
        boolean isVc = shopAuths.stream().anyMatch(e-> ShopTypeEnum.VC.getCode().equals(e.getType()));
        List<String> marketplaceIds = new ArrayList<>(marketplaceIdSet);
        List<String> sellerIds = new ArrayList<>(sellerIdSet);
        List<EffectDataBo> dataList = new ArrayList<>();
        List<EffectDataBo> yesterdayDataList = new ArrayList<>();
        List<ShopEffectDataBo> effectDataGroupByShop = new ArrayList<>();
        EffectDataBo aggregateData;
        EffectDataBo momAggregateData;
        //站点今天
        List<String> siteToday = null;
        //站点昨天
        List<String> siteYesterday = null;
        if (Boolean.TRUE.equals(reqVo.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(marketplaceIds);
            siteYesterday = CalculateAdDataUtil.getSiteYesterday(marketplaceIds);
        }
        if (reqVo.getHourData()) {
            // 小时级数据查询时会校正开始时间,但是查询汇总数据不需要处理
            String aggregateStartTime = reqVo.getStartDate();
            String aggregateMomStartTime = reqVo.getMomStartDate();
            this.handleHourDate(reqVo);
            if (CollectionUtils.isNotEmpty(shopAuths)) {
                dataList = getHourData(reqVo, sellerIds, marketplaceIds, shopAuths, false);
                if (Boolean.TRUE.equals(reqVo.getSiteToday())) {
                    yesterdayDataList = getHourData(reqVo, sellerIds, marketplaceIds, shopAuths, true);
                }
            }
            //计算指标数据
            dataList.forEach(CalculateAdDataUtil::calAdCalDataReflex);
            //将指标数据列表转为小时图表数据
            this.buildHourChartRpcVo(reqVo, effectData, dataList, yesterdayDataList);

            //汇总数据都是使用报告数据而非小时级数据聚合起来的
            if (CollectionUtils.isNotEmpty(shopAuths)) {
                aggregateData = odsAmazonAdCampaignAllReportDao.getEffectAggregateData(reqVo.getPuid(), shopIds, marketplaceIds, reqVo.getCurrency(),
                        aggregateStartTime, reqVo.getEndDate(), reqVo.getModelType(), siteToday, reqVo.getSiteToday(), reqVo.getPortfolioIds(), reqVo.getCampaignIds());
                momAggregateData = odsAmazonAdCampaignAllReportDao.getEffectAggregateData(reqVo.getPuid(), shopIds, marketplaceIds, reqVo.getCurrency(),
                        aggregateMomStartTime, reqVo.getMomEndDate(), reqVo.getModelType(), siteYesterday, reqVo.getSiteToday(), reqVo.getPortfolioIds(), reqVo.getCampaignIds());
                if (Boolean.TRUE.equals(reqVo.getSiteToday())) {
                    effectDataGroupByShop = odsAmazonAdCampaignAllReportDao.getEffectDataGroupByShop(reqVo.getPuid(), shopIds, marketplaceIds, reqVo.getCurrency(),
                            reqVo.getStartDate(), reqVo.getEndDate(), reqVo.getModelType(), siteToday, reqVo.getSiteToday(), reqVo.getPortfolioIds(), reqVo.getCampaignIds());
                    Map<Integer, ShopEffectDataBo> shopEffectDataBoMap = effectDataGroupByShop.stream().collect(Collectors.toMap(ShopEffectDataBo::getShopId, Function.identity(), (e1, e2) -> e1));
                    List<ShopSaleOrigDto> shopSaleDtos = dwShopSalesService.shopSaleGroupByNowDateShopId(reqVo.getPuid(), shopIds, reqVo.getStartDate(), reqVo.getEndDate(), reqVo.getCurrency(), siteToday, reqVo.getSiteToday());
                    Set<Integer> collect = shopSaleDtos.stream().filter(e-> e.getShopId() != null && e.getShopId() > 0).map(ShopSaleOrigDto::getShopId).collect(Collectors.toSet());
                    collect.addAll(shopEffectDataBoMap.keySet());
                    effectDataGroupByShop = collect.stream().map(e -> shopEffectDataBoMap.getOrDefault(e, builderShopEffectDataBo(shopAuthMap.get(e)))).collect(Collectors.toList());
                    setShopSaleAndSaleNumTotal(effectDataGroupByShop, shopSaleDtos);
                    if (CollectionUtils.isNotEmpty(effectDataGroupByShop)) {
                        effectDataGroupByShop.forEach(this::calAdCalDataReflexOther);
                        effectDataGroupByShop.forEach(this::calAdCalShopDataOrig);
                        effectData.addAllShopData(builderShopData(effectDataGroupByShop, shopAuths, isVc));
                    }

                }

            } else {
                aggregateData = CalculateAdDataUtil.buildAggregateAdBaseData(new ArrayList<>(), new EffectDataBo());
                momAggregateData = new EffectDataBo();
            }
        } else {
            if (CollectionUtils.isNotEmpty(shopAuths)) {
                dataList = odsAmazonAdCampaignAllReportDao.getEffectData(reqVo.getPuid(), shopIds, marketplaceIds, reqVo.getCurrency(),
                        reqVo.getStartDate(), reqVo.getEndDate(), reqVo.getModelType(), siteToday, reqVo.getSiteToday(), reqVo.getPortfolioIds(), reqVo.getCampaignIds());
            } else {
                dataList = new ArrayList<>();
            }
            //计算指标数据
            dataList.forEach(CalculateAdDataUtil::calAdCalDataReflex);
            //将指标数据列表转为日月周图表数据
            this.buildDayChartRpcVo(reqVo, effectData, dataList, new HashMap<>(), isVc);
            //获取汇总数据
            aggregateData = CalculateAdDataUtil.buildAggregateAdBaseData(dataList, new EffectDataBo());
            if (CollectionUtils.isNotEmpty(shopAuths)) {
                momAggregateData = odsAmazonAdCampaignAllReportDao.getEffectAggregateData(reqVo.getPuid(), shopIds, marketplaceIds, reqVo.getCurrency(),
                        reqVo.getMomStartDate(), reqVo.getMomEndDate(), reqVo.getModelType(), null, null, reqVo.getPortfolioIds(), reqVo.getCampaignIds());
            } else {
                momAggregateData = new EffectDataBo();
            }
        }
        //计算指标数据
        CalculateAdDataUtil.calAdCalDataReflex(aggregateData);
        CalculateAdDataUtil.calAdCalDataReflex(momAggregateData);
        //计算环比增长率
        CalculateAdDataUtil.calAdMomDataReflex(aggregateData, momAggregateData);
        //汇总数据
        effectData.addAllIndicatorList(this.buildIndicatorVo(aggregateData, momAggregateData, reqVo.getModelType()));
        if (reqVo.getHourData() && Boolean.TRUE.equals(reqVo.getSiteToday())) {
            effectData.addAllShopChartData(builderShopDataChartRpcVo(aggregateData, effectDataGroupByShop, shopAuthMap, isVc));
        }
        return effectData.build();
    }

    private ShopEffectDataBo builderShopEffectDataBo(ShopAuth shopAuth) {
        ShopEffectDataBo bo = new ShopEffectDataBo();
        bo.setShopId(shopAuth.getId());
        bo.setDate(LocalDate.now(ZoneId.of(MarketplaceTimeZoneEnum.valueOf(shopAuth.getMarketplaceId()).getZone_id())).format(DateTimeFormatter.ISO_LOCAL_DATE));
        return bo;
    }

    @Override
    public DashboardEffectDataResponse.EffectData queryAllEffectData(DashboardEffectDataReqVo reqVo) {
        DashboardEffectDataResponse.EffectData.Builder effectData = DashboardEffectDataResponse.EffectData.newBuilder();
        //获取店铺是否存在
        List<ShopAuth> shopAuths = shopAuthDao.listByPuidAndMarketplace(reqVo.getPuid(), reqVo.getMarketplaceIdList(), reqVo.getShopIdList());
        List<String> marketplaceIds = shopAuths.stream().map(ShopAuth::getMarketplaceId).distinct().collect(Collectors.toList());
        List<String> sellerIds = shopAuths.stream().map(ShopAuth::getSellingPartnerId).distinct().collect(Collectors.toList());
        Map<Integer, ShopAuth> shopAuthMap = new HashMap<>();
        boolean isVc = shopAuths.stream().anyMatch(e-> ShopTypeEnum.VC.getCode().equals(e.getType()));
        List<Integer> shopIds = shopAuths.stream().peek(e-> shopAuthMap.put(e.getId(), e)).map(ShopAuth::getId).collect(Collectors.toList());
        List<EffectDataBo> nowData = new ArrayList<>();
        List<EffectDataBo> yesterdayDataList = new ArrayList<>();
        List<ShopEffectDataBo> effectDataGroupByShop = new ArrayList<>();
        List<EffectDataBo> momData;
        List<EffectDataBo> yoyData;
        EffectDataBo aggregateData;
        EffectDataBo momAggregateData;
        EffectDataBo yoyAggregateData;
        if (CollectionUtils.isEmpty(shopAuths)) {
            shopIds.add(-1);
        }
        //站点今天
        List<String> siteToday = null;
        //站点昨天
        List<String> siteYesterday = null;
        if (Boolean.TRUE.equals(reqVo.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(marketplaceIds);
            siteYesterday = CalculateAdDataUtil.getSiteYesterday(marketplaceIds);
        }
        // 小时级
        if (reqVo.getHourData()) {
            this.handleHourDate(reqVo);
            if (CollectionUtils.isNotEmpty(shopAuths)) {
                nowData = getHourData(reqVo, sellerIds, marketplaceIds, shopAuths, false);
                if (Boolean.TRUE.equals(reqVo.getSiteToday())) {
                    yesterdayDataList = getHourData(reqVo, sellerIds, marketplaceIds, shopAuths, true);
                }
            }
            nowData.forEach(this::calAdCalDataReflexBase);
            this.buildHourChartRpcVo(reqVo, effectData, nowData, yesterdayDataList);
            momAggregateData = odsAmazonAdCampaignAllReportDao.getEffectAggregateData(reqVo.getPuid(), shopIds, marketplaceIds, reqVo.getCurrency(),
                    reqVo.getMomStartDate(), reqVo.getMomEndDate(), reqVo.getModelType(), siteYesterday, reqVo.getSiteToday(), reqVo.getPortfolioIds(), reqVo.getCampaignIds());
            yoyAggregateData = odsAmazonAdCampaignAllReportDao.getEffectAggregateData(reqVo.getPuid(), shopIds, marketplaceIds, reqVo.getCurrency(),
                    reqVo.getYoyStartDate(), reqVo.getYoyEndDate(), reqVo.getModelType(), null, null, reqVo.getPortfolioIds(), reqVo.getCampaignIds());
            aggregateData = odsAmazonAdCampaignAllReportDao.getEffectAggregateData(reqVo.getPuid(), shopIds, marketplaceIds, reqVo.getCurrency(),
                    reqVo.getStartDate(), reqVo.getEndDate(), reqVo.getModelType(), siteToday, reqVo.getSiteToday(), reqVo.getPortfolioIds(), reqVo.getCampaignIds());
            setShopSaleAndSaleNumTotal(momAggregateData, dwShopSalesService.shopSaleGroupByNowDate(reqVo.getPuid(), shopIds, reqVo.getMomStartDate(), reqVo.getMomEndDate(), reqVo.getCurrency(), true, siteYesterday, reqVo.getSiteToday()));
            setShopSaleAndSaleNumTotal(yoyAggregateData, dwShopSalesService.shopSaleGroupByNowDate(reqVo.getPuid(), shopIds, reqVo.getYoyStartDate(), reqVo.getYoyEndDate(), reqVo.getCurrency(), true));
            setShopSaleAndSaleNumTotal(aggregateData, dwShopSalesService.shopSaleGroupByNowDate(reqVo.getPuid(), shopIds, reqVo.getStartDate(), reqVo.getEndDate(), reqVo.getCurrency(), true, siteToday, reqVo.getSiteToday()));
            if (Boolean.TRUE.equals(reqVo.getSiteToday())) {
                effectDataGroupByShop = odsAmazonAdCampaignAllReportDao.getEffectDataGroupByShop(reqVo.getPuid(), shopIds, marketplaceIds, reqVo.getCurrency(),
                        reqVo.getStartDate(), reqVo.getEndDate(), reqVo.getModelType(), siteToday, reqVo.getSiteToday(), reqVo.getPortfolioIds(), reqVo.getCampaignIds());
                Map<Integer, ShopEffectDataBo> shopEffectDataBoMap = effectDataGroupByShop.stream().collect(Collectors.toMap(ShopEffectDataBo::getShopId, Function.identity(), (e1, e2) -> e1));
                List<ShopSaleOrigDto> shopSaleDtos = dwShopSalesService.shopSaleGroupByNowDateShopId(reqVo.getPuid(), shopIds, reqVo.getStartDate(), reqVo.getEndDate(), reqVo.getCurrency(), siteToday, reqVo.getSiteToday());
                Set<Integer> collect = shopSaleDtos.stream().filter(e-> e.getShopId() != null && e.getShopId() > 0).map(ShopSaleOrigDto::getShopId).collect(Collectors.toSet());
                collect.addAll(shopEffectDataBoMap.keySet());
                effectDataGroupByShop = collect.stream().map(e -> shopEffectDataBoMap.getOrDefault(e, builderShopEffectDataBo(shopAuthMap.get(e)))).collect(Collectors.toList());
                setShopSaleAndSaleNumTotal(effectDataGroupByShop, shopSaleDtos);
                if (CollectionUtils.isNotEmpty(effectDataGroupByShop)) {
                    effectDataGroupByShop.forEach(this::calAdCalDataReflexOther);
                    effectDataGroupByShop.forEach(this::calAdCalShopDataOrig);
                    effectData.addAllShopData(builderShopData(effectDataGroupByShop, shopAuths, isVc));
                }
            }
        } else {
            // 本期一定是按天查找出来
            nowData = odsAmazonAdCampaignAllReportDao.getEffectData(reqVo.getPuid(), shopIds, marketplaceIds, reqVo.getCurrency(),
                    reqVo.getStartDate(), reqVo.getEndDate(), reqVo.getModelType(), siteToday, reqVo.getSiteToday(), reqVo.getPortfolioIds(), reqVo.getCampaignIds());

            // 本期的店铺和销售额
            Map<String, ShopSaleDto> map = dwShopSalesService.shopSaleGroupByNowDate(reqVo.getPuid(), shopIds, reqVo.getStartDate(), reqVo.getEndDate(), reqVo.getCurrency(), false)
                    .stream().collect(Collectors.toMap(k -> k.getNowDate().toString(), Function.identity()));
            // 设置店铺销售额和销量
            nowData.forEach(k -> {
                setShopSaleAndSaleNum(k, map.get(k.getDate()));
                calAdCalDataReflexOther(k);
            });
            if (!reqVo.getTimeDate()) {
                // 环比
                momAggregateData = odsAmazonAdCampaignAllReportDao.getEffectAggregateData(reqVo.getPuid(), shopIds, marketplaceIds, reqVo.getCurrency(),
                        reqVo.getMomStartDate(), reqVo.getMomEndDate(), reqVo.getModelType(), null, null, reqVo.getPortfolioIds(), reqVo.getCampaignIds());
                setShopSaleAndSaleNumTotal(momAggregateData, dwShopSalesService.shopSaleGroupByNowDate(reqVo.getPuid(), shopIds, reqVo.getMomStartDate(), reqVo.getMomEndDate(), reqVo.getCurrency(), true));
                // 同步
                yoyAggregateData = odsAmazonAdCampaignAllReportDao.getEffectAggregateData(reqVo.getPuid(), shopIds, marketplaceIds, reqVo.getCurrency(),
                        reqVo.getYoyStartDate(), reqVo.getYoyEndDate(), reqVo.getModelType(), null, null, reqVo.getPortfolioIds(), reqVo.getCampaignIds());
                setShopSaleAndSaleNumTotal(yoyAggregateData, dwShopSalesService.shopSaleGroupByNowDate(reqVo.getPuid(), shopIds, reqVo.getYoyStartDate(), reqVo.getYoyEndDate(), reqVo.getCurrency(), true));
                //将指标数据列表转为日月周图表数据
                this.buildDayChartRpcVo(reqVo, effectData, nowData, map, isVc);
            } else {

                // 环比的店铺和销售额
                Map<String, ShopSaleDto> momMap = dwShopSalesService.shopSaleGroupByNowDate(reqVo.getPuid(), shopIds, reqVo.getMomStartDate(), reqVo.getMomEndDate(), reqVo.getCurrency(), false)
                        .stream().collect(Collectors.toMap(k -> k.getNowDate().toString(), Function.identity()));
                // 环比的店铺和销售额
                Map<String, ShopSaleDto> yoyMap = dwShopSalesService.shopSaleGroupByNowDate(reqVo.getPuid(), shopIds, reqVo.getYoyStartDate(), reqVo.getYoyEndDate(), reqVo.getCurrency(), false)
                        .stream().collect(Collectors.toMap(k -> k.getNowDate().toString(), Function.identity()));
                // 时间对比 需要按天
                momData = odsAmazonAdCampaignAllReportDao.getEffectData(reqVo.getPuid(), shopIds, marketplaceIds, reqVo.getCurrency(),
                        reqVo.getMomStartDate(), reqVo.getMomEndDate(), reqVo.getModelType(), null, null, reqVo.getPortfolioIds(), reqVo.getCampaignIds());
                yoyData = odsAmazonAdCampaignAllReportDao.getEffectData(reqVo.getPuid(), shopIds, marketplaceIds, reqVo.getCurrency(),
                        reqVo.getYoyStartDate(), reqVo.getYoyEndDate(), reqVo.getModelType(), null, null, reqVo.getPortfolioIds(), reqVo.getCampaignIds());
                if (CollectionUtils.isNotEmpty(momData)) {
                    momData.forEach(k -> {
                        setShopSaleAndSaleNum(k, momMap.get(k.getDate()));
                        calAdCalDataReflexOther(k);
                    });
                }
                if (CollectionUtils.isNotEmpty(yoyData)) {
                    yoyData.forEach(k -> {
                        setShopSaleAndSaleNum(k, yoyMap.get(k.getDate()));
                        calAdCalDataReflexOther(k);
                    });
                }
                momAggregateData = CalculateAdDataUtil.buildAggregateAdBaseDataMore(momData, new EffectDataBo());
                yoyAggregateData = CalculateAdDataUtil.buildAggregateAdBaseDataMore(yoyData, new EffectDataBo());
                setShopSaleAndSaleNum(momAggregateData, new ArrayList<>(momMap.values()));
                setShopSaleAndSaleNum(yoyAggregateData, new ArrayList<>(yoyMap.values()));
                this.buildDayChartRpcVo(reqVo, effectData, nowData, momData, yoyData, map, momMap, yoyMap, isVc);
            }
            aggregateData = CalculateAdDataUtil.buildAggregateAdBaseDataMore(nowData, new EffectDataBo());
            // 设置汇总的店铺销售额和店铺销量
            setShopSaleAndSaleNum(aggregateData, new ArrayList<>(map.values()));
        }
        calAdCalDataReflexOther(aggregateData);
        calAdCalDataReflexOther(momAggregateData);
        calAdCalDataReflexOther(yoyAggregateData);
        // 设置环比 同比率
        CalculateAdDataUtil.calAdMomAndYoyDataReflex(aggregateData, momAggregateData, DashboardRateEnum.Mom);
        CalculateAdDataUtil.calAdMomAndYoyDataReflex(aggregateData, yoyAggregateData, DashboardRateEnum.Yoy);
        effectData.addAllIndicatorList(this.buildIndicatorVo(reqVo, aggregateData, momAggregateData, yoyAggregateData, isVc));
        if (reqVo.getHourData() && Boolean.TRUE.equals(reqVo.getSiteToday())) {
            effectData.addAllShopChartData(builderShopDataChartRpcVo(aggregateData, effectDataGroupByShop, shopAuthMap, isVc));
        }
        return effectData.build();
    }


    private void calAdCalShopDataOrig(ShopEffectDataBo vo) {
        //原币种
        vo.setOrigCpc(CalculateUtil.calPercent4DecimalAndInt(vo.getOrigCost(), vo.getClicks()));
        vo.setOrigCpa(CalculateUtil.calPercent4DecimalAndInt(vo.getOrigCost(), vo.getOrderNum()));
        vo.setOrigAdvertisingUnitPrice(CalculateUtil.calPercent4DecimalAndInt(vo.getOrigTotalSales(), vo.getOrderNum()));
        vo.setOrigSpc(CalculateUtil.calPercent4DecimalAndInt(vo.getOrigTotalSales(), vo.getClicks()));
    }

    /**
     * 设置店铺和销量
     */
    private void setShopSaleAndSaleNum(EffectDataBo effectDataBo, ShopSaleDto shopSaleDto) {
        if (shopSaleDto == null) {
            effectDataBo.setShopSales(BigDecimal.ZERO);
            effectDataBo.setShopSaleNum(0);
            return;
        }
        effectDataBo.setShopSales(shopSaleDto.getSumRange() != null ? shopSaleDto.getSumRange() : BigDecimal.ZERO);
        effectDataBo.setShopSaleNum(shopSaleDto.getSumSaleNum() != null ? shopSaleDto.getSumSaleNum().intValue() : 0);
    }

    /**
     * 设置其他字段
     */
    private void calAdCalDataReflexOther(EffectDataBo vo) {
        calAdCalDataReflexBase(vo);
        vo.setAcots(CalculateUtil.calPercent4DecimalScale4(vo.getCost(), vo.getShopSales()));
        vo.setAsots(CalculateUtil.calPercent4DecimalScale4(vo.getTotalSales(), vo.getShopSales()));
    }

    private void calAdCalDataReflexBase(EffectDataBo vo) {
        vo.setAcos(CalculateUtil.calPercent4DecimalScale4(vo.getCost(), vo.getTotalSales()));
        //roas 销售额/广告费(非百分比数据)
        vo.setRoas(CalculateUtil.calPercent4DecimalScale4(vo.getTotalSales(), vo.getCost()));
        //广告点击率 点击量/曝光量 * 100%  Impressions 如果超过int
        vo.setClickRate(CalculateUtil.calPercent4IntScale4(vo.getClicks(), vo.getImpressions()));
        //广告转化率 订单量/点击量 * 100%
        vo.setConversionRate(CalculateUtil.calPercent4IntScale4(vo.getOrderNum(), vo.getClicks()));
        vo.setCpc(CalculateUtil.calPercent4DecimalAndIntScale4(vo.getCost(), vo.getClicks()));
        vo.setCpa(CalculateUtil.calPercent4DecimalAndIntScale4(vo.getCost(), vo.getOrderNum()));
        vo.setAdvertisingUnitPrice(CalculateUtil.calPercent4DecimalAndIntScale4(vo.getTotalSales(), vo.getOrderNum()));
        vo.setSpc(CalculateUtil.calPercent4DecimalAndIntScale4(vo.getTotalSales(), vo.getClicks()));
    }


    private void setShopSaleAndSaleNum(EffectDataBo effectDataBo, List<ShopSaleDto> shopSaleList) {
        if (CollectionUtils.isEmpty(shopSaleList)) {
            effectDataBo.setShopSales(BigDecimal.ZERO);
            effectDataBo.setShopSaleNum(0);
            return;
        }
        BigDecimal totalSale = shopSaleList.stream().map(ShopSaleDto::getSumRange).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        int totalSaleNum = shopSaleList.stream().map(ShopSaleDto::getSumSaleNum).filter(Objects::nonNull).mapToInt(Long::intValue).sum();
        effectDataBo.setShopSales(totalSale);
        effectDataBo.setShopSaleNum(totalSaleNum);
    }

    private void setShopSaleAndSaleNumTotal(EffectDataBo effectDataBo, List<ShopSaleDto> shopSaleList) {
        if (CollectionUtils.isEmpty(shopSaleList)) {
            effectDataBo.setShopSales(BigDecimal.ZERO);
            effectDataBo.setShopSaleNum(0);
            return;
        }
        ShopSaleDto shopSaleDto = shopSaleList.get(0);
        if (shopSaleDto == null) {
            effectDataBo.setShopSales(BigDecimal.ZERO);
            effectDataBo.setShopSaleNum(0);
            return;
        }
        effectDataBo.setShopSales(shopSaleDto.getSumRange() != null ? shopSaleDto.getSumRange() : BigDecimal.ZERO);
        effectDataBo.setShopSaleNum(shopSaleDto.getSumSaleNum() != null ? shopSaleDto.getSumSaleNum().intValue() : 0);
    }

    private List<EffectDataBo> getHourData(DashboardEffectDataReqVo reqVo, List<String> sellerIds, List<String> marketplaceIds, List<ShopAuth> shopAuths, boolean isYesterday) {
        // 按时间进行切分,多线程8天一次查询,小于等于8天直接执行
        DateTime startDateTime = DateTime.parse(reqVo.getStartDate(), DateTimeFormat.forPattern(DateUtil.PATTERN));
        DateTime endTime = DateTime.parse(reqVo.getEndDate(), DateTimeFormat.forPattern(DateUtil.PATTERN));
        int days = Days.daysBetween(startDateTime, endTime).getDays();
        List<String> siteTime = null;
        if (Boolean.TRUE.equals(reqVo.getSiteToday())) {
            //如果是站点昨天就查询站点昨天数据
            if (isYesterday) {
                siteTime = CalculateAdDataUtil.getSiteYesterday(reqVo.getMarketplaceIdList());
            } else {
                siteTime = CalculateAdDataUtil.getSiteToday(reqVo.getMarketplaceIdList());
            }

        }
        // 提前过滤掉无数据的sellerId
        if (sellerIds.size() > Constants.DORIS_IN_PARTITION_MAX && dynamicRefreshConfiguration.getDashboardEffectDataSellerIdFilter()) {
            List<String> validSellerIds = this.filterSellerIds(reqVo.getPuid(), sellerIds, shopAuths, startDateTime, endTime);
            if (CollectionUtils.isEmpty(validSellerIds)) {
                return new ArrayList<>();
            }
            sellerIds.clear();
            sellerIds.addAll(validSellerIds);
        }
        if (days <= DAY_PARTITION_SIZE || (Boolean.TRUE.equals(reqVo.getSiteToday()) && siteTime != null)) {
            //获取小时级数据
            return getEffectData(reqVo, sellerIds, marketplaceIds, shopAuths, isYesterday, siteTime, reqVo.getSiteToday(), reqVo.getStartDate(), reqVo.getEndDate());
        } else {
            List<CompletableFuture<List<EffectDataBo>>> futures = new ArrayList<>();
            for (int i = 0; i < days; ) {
                int plusDays = Math.min(days - i, DAY_PARTITION_SIZE - 1);
                String subStartTime = startDateTime.plusDays(i).toString("yyyy-MM-dd");
                String subEndTime = startDateTime.plusDays(i + plusDays).toString("yyyy-MM-dd");
                CompletableFuture<List<EffectDataBo>> future = CompletableFuture.supplyAsync(() ->
                                getEffectData(reqVo, sellerIds, marketplaceIds, shopAuths, null, null, null,subStartTime, subEndTime),
                        ThreadPoolUtil.getAdDashboardHourDataThreadPool());
                futures.add(future);
                i += DAY_PARTITION_SIZE;
            }
            List<EffectDataBo> dataList = new ArrayList<>();
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                for (CompletableFuture<List<EffectDataBo>> future : futures) {
                    dataList.addAll(future.get());
                }
                dataList = mergeEffectData(dataList);
            } catch (Exception e) {
                log.info("queryEffectData error. reqVo:{} ", reqVo, e);
                return Collections.emptyList();
            }
            return dataList;
        }
    }

    /**
     * 按时间分组合并指标数据
     */
    private static List<EffectDataBo> mergeEffectData(List<EffectDataBo> dataList) {
        Map<String, List<EffectDataBo>> dataListMap = dataList.stream().collect(Collectors.groupingBy(EffectDataBo::getTime));
        dataList = new ArrayList<>();
        for (Map.Entry<String, List<EffectDataBo>> entry : dataListMap.entrySet()) {
            List<EffectDataBo> subDataList = entry.getValue();
            if (subDataList.size() == 1) {
                dataList.add(subDataList.get(0));
            } else {
                EffectDataBo effectDataBo = subDataList.get(0);
                for (int i = 1; i < subDataList.size(); i++) {
                    EffectDataBo current = subDataList.get(i);
                    effectDataBo.setCost(effectDataBo.getCost().add(current.getCost()));
                    effectDataBo.setTotalSales(effectDataBo.getTotalSales().add(current.getTotalSales()));
                    effectDataBo.setImpressions(effectDataBo.getImpressions() + current.getImpressions());
                    effectDataBo.setClicks(effectDataBo.getClicks() + current.getClicks());
                    effectDataBo.setOrderNum(effectDataBo.getOrderNum() + current.getOrderNum());
                    effectDataBo.setSaleNum(effectDataBo.getSaleNum() + current.getSaleNum());
                }
                dataList.add(effectDataBo);
            }
        }
        return dataList;
    }

    /**
     * 由于小时级索引
     */
    private List<EffectDataBo> getEffectData(DashboardEffectDataReqVo reqVo, List<String> sellerIds, List<String> marketplaceIds
            , List<ShopAuth> shopAuths, Boolean isYesterday, List<String> siteTime, Boolean siteToday, String startDate, String endDate) {
        List<List<String>> partition = Lists.partition(sellerIds, Constants.DORIS_IN_PARTITION_MAX);
        List<EffectDataBo> dataList = new ArrayList<>();
        for (List<String> sellerIdList : partition) {
            List<EffectDataBo> effectData = amazonMarketingStreamDataDao.getEffectData(reqVo.getPuid(), sellerIdList, marketplaceIds, reqVo.getCurrency(),
                    startDate, endDate, reqVo.getModelType(), shopAuths, siteTime, siteToday,
                    reqVo.getPortfolioIds(), reqVo.getCampaignIds(), reqVo.getShopIdList(), isYesterday);
            dataList.addAll(effectData);
        }
        return mergeEffectData(dataList);
    }

    /**
     * 根据店铺id查广告活动过滤掉无数据的店铺
     */
    private List<String> filterSellerIds(Integer puid, List<String> sellerIds, List<ShopAuth> shopAuths, DateTime startDateTime, DateTime endTime) {
        if (CollectionUtils.isEmpty(sellerIds) || CollectionUtils.isEmpty(shopAuths)) {
            return new ArrayList<>();
        }
        List<Integer> shopIds = shopAuths.stream().map(ShopAuth::getId).collect(Collectors.toList());
        List<Integer> validShopIds = odsAmazonAdCampaignAllReportDao.selectValidShopIds(puid, shopIds, startDateTime, endTime);
        if (CollectionUtils.isEmpty(validShopIds)) {
            return new ArrayList<>();
        }
        return shopAuths.stream().filter(e -> validShopIds.contains(e.getId())).map(ShopAuth::getSellingPartnerId).distinct().collect(Collectors.toList());
    }

    //处理小时级数据时间，开始时间不能超过小时级报告的存储时间范围
    private void handleHourDate(DashboardEffectDataReqVo reqVo) {
        Date startDate = DateUtil.strToDate(reqVo.getStartDate(), DateUtil.PATTERN);
//        Date momStartDate = DateUtil.strToDate(reqVo.getMomStartDate(), DateUtil.PATTERN);
        Date beginDate = DateUtil.addDay(new Date(), -Constants.FEED_SYNC_DAY);
        if (DateUtil.compareDate(startDate, beginDate) == -1) {
            reqVo.setStartDate(DateUtil.dateToStrWithTime(beginDate, DateUtil.PATTERN));
        } else {
            reqVo.setStartDate(DateUtil.dateToStrWithTime(startDate, DateUtil.PATTERN));
        }
//        if (DateUtil.compareDate(momStartDate, beginDate) == -1) {
//            reqVo.setMomStartDate(DateUtil.dateToStrWithTime(beginDate, DateUtil.PATTERN));
//        } else {
//            reqVo.setMomStartDate(DateUtil.dateToStrWithTime(momStartDate, DateUtil.PATTERN));
//        }
        reqVo.setEndDate(DateUtil.dateToStrWithTime(DateUtil.strToDate(reqVo.getEndDate(), DateUtil.PATTERN), DateUtil.PATTERN));
        reqVo.setMomEndDate(DateUtil.dateToStrWithTime(DateUtil.strToDate(reqVo.getMomEndDate(), DateUtil.PATTERN), DateUtil.PATTERN));
    }

    private void buildDayChartRpcVo(DashboardEffectDataReqVo reqVo, DashboardEffectDataResponse.EffectData.Builder effectData, List<EffectDataBo> dataList, Map<String, ShopSaleDto> map, boolean isVc) {
        Date startDate = DateUtil.strToDate(reqVo.getStartDate(), DateUtil.PATTERN);
        Date endDate = DateUtil.strToDate(reqVo.getEndDate(), DateUtil.PATTERN);
        if (startDate != null && endDate != null) {
            effectData.addAllDay(this.getChart(this.getDayList(startDate, endDate, dataList, map), reqVo.getModelType(), isVc));
            effectData.addAllWeek(this.getChart(this.getWeekList(startDate, endDate, dataList, map), reqVo.getModelType(), isVc));
            effectData.addAllMonth(this.getChart(this.getMonthList(startDate, endDate, dataList, map), reqVo.getModelType(), isVc));
        }
    }

    private void buildDayChartRpcVo(DashboardEffectDataReqVo reqVo, DashboardEffectDataResponse.EffectData.Builder effectData,
                                    List<EffectDataBo> nowData, List<EffectDataBo> momData, List<EffectDataBo> yoyData,
                                    Map<String, ShopSaleDto> map, Map<String, ShopSaleDto> momMap, Map<String, ShopSaleDto> yoyMap, boolean isVc) {
        Date startDate = DateUtil.strToDate(reqVo.getStartDate(), DateUtil.PATTERN);
        Date endDate = DateUtil.strToDate(reqVo.getEndDate(), DateUtil.PATTERN);
        Date momStartDate = DateUtil.strToDate(reqVo.getMomStartDate(), DateUtil.PATTERN);
        Date momEndDate = DateUtil.strToDate(reqVo.getMomEndDate(), DateUtil.PATTERN);
        Date yoyStartDate = DateUtil.strToDate(reqVo.getYoyStartDate(), DateUtil.PATTERN);
        Date yoyEndDate = DateUtil.strToDate(reqVo.getYoyEndDate(), DateUtil.PATTERN);
        if (startDate != null && endDate != null && momStartDate != null && momEndDate != null && yoyStartDate != null && yoyEndDate != null) {
            effectData.addAllDay(this.getChart(this.getDayList(startDate, endDate, nowData, map), this.getDayList(momStartDate, momEndDate, momData, momMap), this.getDayList(yoyStartDate, yoyEndDate, yoyData, yoyMap), isVc));
            effectData.addAllWeek(this.getChart(this.getWeekList(startDate, endDate, nowData, map), this.getWeekList(momStartDate, momEndDate, momData, momMap), this.getWeekList(yoyStartDate, yoyEndDate, yoyData, yoyMap), isVc));
            effectData.addAllMonth(this.getChart(this.getMonthList(startDate, endDate, nowData, map), this.getMonthList(momStartDate, momEndDate, momData, momMap), this.getMonthList(yoyStartDate, yoyEndDate, yoyData, yoyMap), isVc));
        }
    }

    private void buildHourChartRpcVo(DashboardEffectDataReqVo reqVo, DashboardEffectDataResponse.EffectData.Builder effectData, List<EffectDataBo> dataList, List<EffectDataBo> momDataList) {
        Map<Integer, EffectDataBo> hourlyEffectDataMap = this.getHourlyEffectDataMap(dataList);
        Map<Integer, EffectDataBo> momHourlyEffectDataMap = this.getHourlyEffectDataMap(momDataList);
        //按24小时返回数据
        List<EffectDataBo> effectDataBoList = new ArrayList<>(24);
        for (Integer hour : HourConvert.hourMap.keySet()) {
            EffectDataBo effectDataBo = hourlyEffectDataMap.getOrDefault(hour, new EffectDataBo());
            EffectDataBo momEffectDataBo = momHourlyEffectDataMap.getOrDefault(hour, new EffectDataBo());
            effectDataBo.setTime(HourConvert.hourMap.get(hour));
            setMomHourData(effectDataBo, momEffectDataBo);
            effectDataBoList.add(effectDataBo);
        }

        effectData.addAllHour(this.getHourlyChart(effectDataBoList, reqVo.getModelType()));
    }

    private void setMomHourData (EffectDataBo effectDataBo, EffectDataBo momEffectDataBo) {

        //广告花费环比值
        effectDataBo.setCostMomValue(momEffectDataBo.getCost() == null ? "0.00" : momEffectDataBo.getCost().setScale(2, RoundingMode.HALF_UP).toPlainString());
        //广告销售额环比值
        effectDataBo.setTotalSalesMomValue(momEffectDataBo.getTotalSales() == null ? "0.00" : momEffectDataBo.getTotalSales().setScale(2, RoundingMode.HALF_UP).toPlainString());
        //ACoS环比值
        effectDataBo.setAcosMomValue(momEffectDataBo.getAcos() == null ? "0.00" : momEffectDataBo.getAcos().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toPlainString());
        //ROAS环比值
        effectDataBo.setRoasMomValue(momEffectDataBo.getRoas() == null ? "0.00" : momEffectDataBo.getRoas().setScale(2, RoundingMode.HALF_UP).toPlainString());
        //广告曝光量环比值
        effectDataBo.setImpressionsMomValue(momEffectDataBo.getImpressions() == null ? "0.00" : String.valueOf(momEffectDataBo.getImpressions()));
        //广告点击量环比值
        effectDataBo.setClicksMomValue(momEffectDataBo.getClicks() == null ? "0.00" : String.valueOf(momEffectDataBo.getClicks()));
        //广告点击率环比值
        effectDataBo.setClickRateMomValue(momEffectDataBo.getClickRate() == null ? "0.00" : momEffectDataBo.getClickRate().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toPlainString());
        //广告转化率环比值
        effectDataBo.setConversionRateMomValue(momEffectDataBo.getConversionRate() == null ? "0.00" : momEffectDataBo.getConversionRate().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toPlainString());

        /**
         * 广告订单量
         */
        effectDataBo.setOrderNumMomValue(getGrpcValue(momEffectDataBo.getOrderNum()));
        /**
         * 广告销量
         */
        effectDataBo.setSaleNumMomValue(getGrpcValue(momEffectDataBo.getSaleNum()));

        effectDataBo.setCpcMomValue(getGrpcValue(momEffectDataBo.getCpc()));
        effectDataBo.setSpcMomValue(getGrpcValue(momEffectDataBo.getSpc()));
        effectDataBo.setCpaMomValue(getGrpcValue(momEffectDataBo.getCpa()));
        effectDataBo.setAdvertisingUnitPriceMomValue(getGrpcValue(momEffectDataBo.getAdvertisingUnitPrice()));

    }



    private List<AdHomeChartRpcVo> getChart(List<EffectDataBo> now, List<EffectDataBo> mom, List<EffectDataBo> yoy, boolean isVc) {
        List<AdHomeChartRpcVo> chartVos = new ArrayList<>(now.size());
        int sizeIndex = now.size();
        List<AdHomeChartRpcVo.ChartRpcRecord> costList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> salesList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> acosList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> roasList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> impressionsList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> clicksList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> clickRateList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> salesConversionRateList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> orderNumList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> saleNumList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> shopSaleList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> shopSaleNumList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> acotsList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> asotsList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> cpcList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> spcList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> cpaList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> advertisingUnitPriceList = Lists.newArrayList();
        for (int i = 0; i < sizeIndex; i++) {
            EffectDataBo nowData = now.get(i);
            EffectDataBo momData = getIndex(i, mom);
            EffectDataBo yoyData = getIndex(i, yoy);
            AdHomeChartRpcVo.ChartRpcRecord.Builder cost = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            cost.setDate(nowData.getDate());
            cost.setMomDate(getGrpcDateValue(momData.getDate()));
            cost.setYoyDate(getGrpcDateValue(yoyData.getDate()));
            cost.setMomValue(getGrpcValue(momData.getCost()));
            cost.setYoyValue(getGrpcValue(yoyData.getCost()));
            cost.setValue(getGrpcValue(nowData.getCost()));
            costList.add(cost.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder sales = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            sales.setDate(nowData.getDate());
            sales.setMomDate(getGrpcDateValue(momData.getDate()));
            sales.setYoyDate(getGrpcDateValue(yoyData.getDate()));
            sales.setMomValue(getGrpcValue(momData.getTotalSales()));
            sales.setYoyValue(getGrpcValue(yoyData.getTotalSales()));
            sales.setValue(getGrpcValue(nowData.getTotalSales()));
            salesList.add(sales.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder acos = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            acos.setDate(nowData.getDate());
            acos.setMomDate(getGrpcDateValue(momData.getDate()));
            acos.setYoyDate(getGrpcDateValue(yoyData.getDate()));
            acos.setMomValue(getGrpcValueNoPercent(momData.getAcos()));
            acos.setYoyValue(getGrpcValueNoPercent(yoyData.getAcos()));
            acos.setValue(getGrpcValueNoPercent(nowData.getAcos()));
            acosList.add(acos.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder roas = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            roas.setDate(nowData.getDate());
            roas.setMomDate(getGrpcDateValue(momData.getDate()));
            roas.setYoyDate(getGrpcDateValue(yoyData.getDate()));
            roas.setMomValue(getGrpcValue(momData.getRoas()));
            roas.setYoyValue(getGrpcValue(yoyData.getRoas()));
            roas.setValue(getGrpcValue(nowData.getRoas()));
            roasList.add(roas.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder impressions = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();

            impressions.setDate(nowData.getDate());
            impressions.setMomDate(getGrpcDateValue(momData.getDate()));
            impressions.setYoyDate(getGrpcDateValue(yoyData.getDate()));
            impressions.setMomValue(getGrpcValue(momData.getImpressions()));
            impressions.setYoyValue(getGrpcValue(yoyData.getImpressions()));
            impressions.setValue(getGrpcValue(nowData.getImpressions()));
            impressionsList.add(impressions.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder clicks = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            clicks.setDate(nowData.getDate());
            clicks.setMomDate(getGrpcDateValue(momData.getDate()));
            clicks.setYoyDate(getGrpcDateValue(yoyData.getDate()));
            clicks.setMomValue(getGrpcValue(momData.getClicks()));
            clicks.setYoyValue(getGrpcValue(yoyData.getClicks()));
            clicks.setValue(getGrpcValue(nowData.getClicks()));
            clicksList.add(clicks.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder clickRate = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            clickRate.setDate(nowData.getDate());
            clickRate.setMomDate(getGrpcDateValue(momData.getDate()));
            clickRate.setYoyDate(getGrpcDateValue(yoyData.getDate()));
            clickRate.setMomValue(getGrpcValueNoPercent(momData.getClickRate()));
            clickRate.setYoyValue(getGrpcValueNoPercent(yoyData.getClickRate()));
            clickRate.setValue(getGrpcValueNoPercent(nowData.getClickRate()));
            clickRateList.add(clickRate.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder salesConversionRate = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();

            salesConversionRate.setDate(nowData.getDate());
            salesConversionRate.setMomDate(getGrpcDateValue(momData.getDate()));
            salesConversionRate.setYoyDate(getGrpcDateValue(yoyData.getDate()));
            salesConversionRate.setMomValue(getGrpcValueNoPercent(momData.getConversionRate()));
            salesConversionRate.setYoyValue(getGrpcValueNoPercent(yoyData.getConversionRate()));
            salesConversionRate.setValue(getGrpcValueNoPercent(nowData.getConversionRate()));
            salesConversionRateList.add(salesConversionRate.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder orderNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            orderNum.setDate(nowData.getDate());
            orderNum.setMomDate(getGrpcDateValue(momData.getDate()));
            orderNum.setYoyDate(getGrpcDateValue(yoyData.getDate()));
            orderNum.setMomValue(getGrpcValue(momData.getOrderNum()));
            orderNum.setYoyValue(getGrpcValue(yoyData.getOrderNum()));
            orderNum.setValue(getGrpcValue(nowData.getOrderNum()));
            orderNumList.add(orderNum.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder salesNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            salesNum.setDate(nowData.getDate());
            salesNum.setMomDate(getGrpcDateValue(momData.getDate()));
            salesNum.setYoyDate(getGrpcDateValue(yoyData.getDate()));
            salesNum.setMomValue(getGrpcValue(momData.getSaleNum()));
            salesNum.setYoyValue(getGrpcValue(yoyData.getSaleNum()));
            salesNum.setValue(getGrpcValue(nowData.getSaleNum()));
            saleNumList.add(salesNum.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder shopSale = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            shopSale.setDate(nowData.getDate());
            shopSale.setMomDate(getGrpcDateValue(momData.getDate()));
            shopSale.setYoyDate(getGrpcDateValue(yoyData.getDate()));
            shopSale.setMomValue(getGrpcValue(momData.getShopSales()));
            shopSale.setYoyValue(getGrpcValue(yoyData.getShopSales()));
            shopSale.setValue(getGrpcValue(nowData.getShopSales()));
            shopSaleList.add(shopSale.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder shopSaleNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            shopSaleNum.setDate(nowData.getDate());
            shopSaleNum.setMomDate(getGrpcDateValue(momData.getDate()));
            shopSaleNum.setYoyDate(getGrpcDateValue(yoyData.getDate()));
            shopSaleNum.setMomValue(getGrpcValue(momData.getShopSaleNum()));
            shopSaleNum.setYoyValue(getGrpcValue(yoyData.getShopSaleNum()));
            shopSaleNum.setValue(getGrpcValue(nowData.getShopSaleNum()));
            shopSaleNumList.add(shopSaleNum.build());
            if(!isVc) {
                AdHomeChartRpcVo.ChartRpcRecord.Builder acots = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();

                acots.setDate(nowData.getDate());
                acots.setMomDate(getGrpcDateValue(momData.getDate()));
                acots.setYoyDate(getGrpcDateValue(yoyData.getDate()));
                acots.setMomValue(getGrpcValueNoPercent(momData.getAcots()));
                acots.setYoyValue(getGrpcValueNoPercent(yoyData.getAcots()));
                acots.setValue(getGrpcValueNoPercent(nowData.getAcots()));
                acotsList.add(acots.build());

                AdHomeChartRpcVo.ChartRpcRecord.Builder asots = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();

                asots.setDate(nowData.getDate());
                asots.setMomDate(getGrpcDateValue(momData.getDate()));
                asots.setYoyDate(getGrpcDateValue(yoyData.getDate()));
                asots.setMomValue(getGrpcValueNoPercent(momData.getAsots()));
                asots.setYoyValue(getGrpcValueNoPercent(yoyData.getAsots()));
                asots.setValue(getGrpcValueNoPercent(nowData.getAsots()));

                asotsList.add(asots.build());

            }


            AdHomeChartRpcVo.ChartRpcRecord.Builder cpc = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();

            cpc.setDate(nowData.getDate());
            cpc.setMomDate(getGrpcDateValue(momData.getDate()));
            cpc.setYoyDate(getGrpcDateValue(yoyData.getDate()));
            cpc.setMomValue(getGrpcValue(momData.getCpc()));
            cpc.setYoyValue(getGrpcValue(yoyData.getCpc()));
            cpc.setValue(getGrpcValue(nowData.getCpc()));

            cpcList.add(cpc.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder cpa = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            cpa.setDate(nowData.getDate());
            cpa.setMomDate(getGrpcDateValue(momData.getDate()));
            cpa.setYoyDate(getGrpcDateValue(yoyData.getDate()));
            cpa.setMomValue(getGrpcValue(momData.getCpa()));
            cpa.setYoyValue(getGrpcValue(yoyData.getCpa()));
            cpa.setValue(getGrpcValue(nowData.getCpa()));
            cpaList.add(cpa.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder spc = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();

            spc.setDate(nowData.getDate());
            spc.setMomDate(getGrpcDateValue(momData.getDate()));
            spc.setYoyDate(getGrpcDateValue(yoyData.getDate()));
            spc.setMomValue(getGrpcValue(momData.getSpc()));
            spc.setYoyValue(getGrpcValue(yoyData.getSpc()));
            spc.setValue(getGrpcValue(nowData.getSpc()));
            spcList.add(spc.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder advertisingUnitPrice = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();

            advertisingUnitPrice.setDate(nowData.getDate());
            advertisingUnitPrice.setMomDate(getGrpcDateValue(momData.getDate()));
            advertisingUnitPrice.setYoyDate(getGrpcDateValue(yoyData.getDate()));
            advertisingUnitPrice.setMomValue(getGrpcValue(momData.getAdvertisingUnitPrice()));
            advertisingUnitPrice.setYoyValue(getGrpcValue(yoyData.getAdvertisingUnitPrice()));
            advertisingUnitPrice.setValue(getGrpcValue(nowData.getAdvertisingUnitPrice()));
            advertisingUnitPriceList.add(advertisingUnitPrice.build());
        }
        Int32Value size = Int32Value.of(sizeIndex);

        chartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("广告曝光量")
                .setName("impressions")
                .setTotal(size)
                .addAllRecords(impressionsList)
                .build());

        chartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("广告点击量")
                .setName("clicks")
                .setTotal(size)
                .addAllRecords(clicksList)
                .build());

        chartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("广告点击率")
                .setName("clickRate")
                .setTotal(size)
                .addAllRecords(clickRateList)
                .build());

        chartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("广告转化率")
                .setName("conversionRate")
                .setTotal(size)
                .addAllRecords(salesConversionRateList)
                .build());
        chartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("广告花费")
                .setName("cost")
                .setTotal(size)
                .addAllRecords(costList)
                .build());

        chartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("广告销售额")
                .setName("totalSales")
                .setTotal(size)
                .addAllRecords(salesList)
                .build());

        chartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("ACoS")
                .setName("acos")
                .setTotal(size)
                .addAllRecords(acosList)
                .build());

        chartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("ROAS")
                .setName("roas")
                .setTotal(size)
                .addAllRecords(roasList)
                .build());


        chartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("广告订单量")
                .setName("orderNum")
                .setTotal(size)
                .addAllRecords(orderNumList)
                .build());

        chartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("广告销量")
                .setName("saleNum")
                .setTotal(size)
                .addAllRecords(saleNumList)
                .build());


        chartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("店铺销售额")
                .setName("shopSales")
                .setTotal(size)
                .addAllRecords(shopSaleList)
                .build());

        chartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("店铺销量")
                .setName("shopSaleNum")
                .setTotal(size)
                .addAllRecords(shopSaleNumList)
                .build());
        if  (!isVc) {
            chartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("ASoTS")
                    .setName("asots")
                    .setTotal(size)
                    .addAllRecords(asotsList)
                    .build());

            chartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("ACoTS")
                    .setName("acots")
                    .setTotal(size)
                    .addAllRecords(acotsList)
                    .build());
        }


        chartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("CPC")
                .setName("cpc")
                .setTotal(size)
                .addAllRecords(cpcList)
                .build());

        chartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("SPC")
                .setName("spc")
                .setTotal(size)
                .addAllRecords(spcList)
                .build());

        chartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("CPA")
                .setName("cpa")
                .setTotal(size)
                .addAllRecords(cpaList)
                .build());

        chartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("广告笔单价")
                .setName("advertisingUnitPrice")
                .setTotal(size)
                .addAllRecords(advertisingUnitPriceList)
                .build());
        return chartVos;
    }


    /**
     * 顺序拿对象
     */
    private EffectDataBo getIndex(int index, List<EffectDataBo> data) {
        if (index < data.size()) {
            return data.get(index);
        }
        return new EffectDataBo();
    }

    //生成chart图数据
    private List<AdHomeChartRpcVo> getChart(List<EffectDataBo> list, String modelType, boolean isVc) {
        List<AdHomeChartRpcVo> chartVos = new ArrayList<>();
        //封装数据
        List<AdHomeChartRpcVo.ChartRpcRecord> costList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> salesList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> acosList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> roasList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> impressionsList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> clicksList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> clickRateList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> salesConversionRateList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> orderNumList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> saleNumList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> shopSaleList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> shopSaleNumList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> acotsList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> asotsList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> cpcList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> spcList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> cpaList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> advertisingUnitPriceList = Lists.newArrayList();

        list.stream().filter(Objects::nonNull).forEachOrdered(item -> {
            AdHomeChartRpcVo.ChartRpcRecord.Builder cost = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            cost.setDate(item.getDate());
            cost.setValue(item.getCost() == null ? "0.00" : item.getCost().setScale(2, RoundingMode.HALF_UP).toPlainString());
            costList.add(cost.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder sales = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            sales.setDate(item.getDate());
            sales.setValue(item.getTotalSales() == null ? "0.00" : item.getTotalSales().setScale(2, RoundingMode.HALF_UP).toPlainString());
            salesList.add(sales.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder acos = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            acos.setDate(item.getDate());
            acos.setValue(item.getAcos() == null ? "0.00" : item.getAcos().setScale(2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).toPlainString());
            acosList.add(acos.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder roas = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            roas.setDate(item.getDate());
            roas.setValue(item.getRoas() == null ? "0.00" : item.getRoas().setScale(2, RoundingMode.HALF_UP).toPlainString());
            roasList.add(roas.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder impressions = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            impressions.setDate(item.getDate());
            impressions.setValue(item.getImpressions() == null ? "0.00" : String.valueOf(item.getImpressions()));
            impressionsList.add(impressions.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder clicks = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            clicks.setDate(item.getDate());
            clicks.setValue(item.getClicks() == null ? "0.00" : String.valueOf(item.getClicks()));
            clicksList.add(clicks.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder clickRate = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            clickRate.setDate(item.getDate());
            clickRate.setValue(item.getClickRate() == null ? "0.00" : item.getClickRate().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toPlainString());
            clickRateList.add(clickRate.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder salesConversionRate = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            salesConversionRate.setDate(item.getDate());
            salesConversionRate.setValue(item.getConversionRate() == null ? "0.00" : item.getConversionRate().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toPlainString());
            salesConversionRateList.add(salesConversionRate.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder orderNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            orderNum.setDate(item.getDate());
            orderNum.setValue(item.getOrderNum() == null ? "0.00" : String.valueOf(item.getOrderNum()));
            orderNumList.add(orderNum.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder salesNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            salesNum.setDate(item.getDate());
            salesNum.setValue(item.getSaleNum() == null ? "0.00" : String.valueOf(item.getSaleNum()));
            saleNumList.add(salesNum.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder shopSale = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            shopSale.setDate(item.getDate());
            shopSale.setValue(item.getShopSales() == null ? "0.00" : item.getShopSales().setScale(2, RoundingMode.HALF_UP).toPlainString());
            shopSaleList.add(shopSale.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder shopSaleNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            shopSaleNum.setDate(item.getDate());
            shopSaleNum.setValue(item.getShopSaleNum() == null ? "0.00" : String.valueOf(item.getShopSaleNum()));
            shopSaleNumList.add(shopSaleNum.build());
            if (isVc) {
                AdHomeChartRpcVo.ChartRpcRecord.Builder acots = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
                acots.setDate(item.getDate());
                acots.setValue("0.00");
                acotsList.add(acots.build());

                AdHomeChartRpcVo.ChartRpcRecord.Builder asots = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
                asots.setDate(item.getDate());
                asots.setValue("0.00");
                asotsList.add(asots.build());
            } else {
                AdHomeChartRpcVo.ChartRpcRecord.Builder acots = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
                acots.setDate(item.getDate());
                acots.setValue(item.getAcots() == null ? "0.00" : item.getAcots().multiply(BigDecimal.valueOf(100L)).setScale(2, RoundingMode.HALF_UP).toPlainString());
                acotsList.add(acots.build());

                AdHomeChartRpcVo.ChartRpcRecord.Builder asots = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
                asots.setDate(item.getDate());
                asots.setValue(item.getAsots() == null ? "0.00" : item.getAsots().multiply(BigDecimal.valueOf(100L)).setScale(2, RoundingMode.HALF_UP).toPlainString());
                asotsList.add(asots.build());
            }


            AdHomeChartRpcVo.ChartRpcRecord.Builder cpc = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            cpc.setDate(item.getDate());
            cpc.setValue(item.getCpc() == null ? "0.00" : item.getCpc().setScale(2, RoundingMode.HALF_UP).toPlainString());
            cpcList.add(cpc.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder cpa = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            cpa.setDate(item.getDate());
            cpa.setValue(item.getCpa() == null ? "0.00" : item.getCpa().setScale(2, RoundingMode.HALF_UP).toPlainString());
            cpaList.add(cpa.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder spc = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            spc.setDate(item.getDate());
            spc.setValue(item.getSpc() == null ? "0.00" : item.getSpc().setScale(2, RoundingMode.HALF_UP).toPlainString());
            spcList.add(spc.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder advertisingUnitPrice = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            advertisingUnitPrice.setDate(item.getDate());
            advertisingUnitPrice.setValue(item.getAdvertisingUnitPrice() == null ? "0.00" : item.getAdvertisingUnitPrice().setScale(2, RoundingMode.HALF_UP).toPlainString());
            advertisingUnitPriceList.add(advertisingUnitPrice.build());
        });

        Int32Value size = Int32Value.of(list.size());

        if (EffectDataModelTypeEnum.AD_EFFECT.getCode().equals(modelType)) {
            chartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("广告花费")
                    .setName("cost")
                    .setTotal(size)
                    .addAllRecords(costList)
                    .build());

            chartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("广告销售额")
                    .setName("totalSales")
                    .setTotal(size)
                    .addAllRecords(salesList)
                    .build());

            chartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("ACoS")
                    .setName("acos")
                    .setTotal(size)
                    .addAllRecords(acosList)
                    .build());

            chartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("ROAS")
                    .setName("roas")
                    .setTotal(size)
                    .addAllRecords(roasList)
                    .build());
        } else {
            chartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("广告曝光量")
                    .setName("impressions")
                    .setTotal(size)
                    .addAllRecords(impressionsList)
                    .build());

            chartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("广告点击量")
                    .setName("clicks")
                    .setTotal(size)
                    .addAllRecords(clicksList)
                    .build());

            chartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("广告点击率")
                    .setName("clickRate")
                    .setTotal(size)
                    .addAllRecords(clickRateList)
                    .build());

            chartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("广告转化率")
                    .setName("conversionRate")
                    .setTotal(size)
                    .addAllRecords(salesConversionRateList)
                    .build());

            if (EffectDataModelTypeEnum.AD_INDEX.getCode().equals(modelType)) {
                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("广告花费")
                        .setName("cost")
                        .setTotal(size)
                        .addAllRecords(costList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("广告销售额")
                        .setName("totalSales")
                        .setTotal(size)
                        .addAllRecords(salesList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("ACoS")
                        .setName("acos")
                        .setTotal(size)
                        .addAllRecords(acosList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("ROAS")
                        .setName("roas")
                        .setTotal(size)
                        .addAllRecords(roasList)
                        .build());


                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("广告订单量")
                        .setName("orderNum")
                        .setTotal(size)
                        .addAllRecords(orderNumList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("广告销量")
                        .setName("saleNum")
                        .setTotal(size)
                        .addAllRecords(saleNumList)
                        .build());


                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("店铺销售额")
                        .setName("shopSales")
                        .setTotal(size)
                        .addAllRecords(shopSaleList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("店铺销量")
                        .setName("shopSaleNum")
                        .setTotal(size)
                        .addAllRecords(shopSaleNumList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("ASoTS")
                        .setName("asots")
                        .setTotal(size)
                        .addAllRecords(asotsList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("ACoTS")
                        .setName("acots")
                        .setTotal(size)
                        .addAllRecords(acotsList)
                        .build());



                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("CPC")
                        .setName("cpc")
                        .setTotal(size)
                        .addAllRecords(cpcList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("SPC")
                        .setName("spc")
                        .setTotal(size)
                        .addAllRecords(spcList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("CPA")
                        .setName("cpa")
                        .setTotal(size)
                        .addAllRecords(cpaList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("广告笔单价")
                        .setName("advertisingUnitPrice")
                        .setTotal(size)
                        .addAllRecords(advertisingUnitPriceList)
                        .build());
            }
        }
        return chartVos;
    }

    //按日-生成chart图数据
    private List<EffectDataBo> getDayList(Date startDate, Date endDate, List<EffectDataBo> list, Map<String, ShopSaleDto> shopSaleDtoMap) {
        List<EffectDataBo> vos = new ArrayList<>();
        Map<String, EffectDataBo> dateEffectDataMap = list.stream().collect(Collectors.toMap(EffectDataBo::getDate, Function.identity(), (e1, e2) -> e2));
        LocalDate startLocalDate = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endLocalDate = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        EffectDataBo effectDataBo;
        for (LocalDate date = startLocalDate; date.isBefore(endLocalDate.plusDays(1)); date = date.plusDays(1)) {
            String formatDate = date.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            if (dateEffectDataMap.containsKey(formatDate)) {
                effectDataBo = dateEffectDataMap.get(formatDate);
            } else {
                effectDataBo = new EffectDataBo();
            }
            //展示日期需为yyyy-MM-dd
            effectDataBo.setDate(date.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
            setShopSaleAndSaleNum(effectDataBo, shopSaleDtoMap.get(effectDataBo.getDate()));
            vos.add(effectDataBo);
        }
        return vos;
    }

    //按周-生成chart图数据
    private List<EffectDataBo> getWeekList(Date startDate, Date endDate, List<EffectDataBo> list, Map<String, ShopSaleDto> shopSaleDtoMap) {
        List<List<String>> weekDate = SplitDateByWeekUtil.split(startDate, endDate);
        Map<String, Integer> weekClickMap = new ConcurrentHashMap<>();
        Map<String, Long> weekImpressionsMap = new ConcurrentHashMap<>();
        Map<String, Integer> weekAdOrderMap = new ConcurrentHashMap<>();
        Map<String, BigDecimal> weekAdSalesMap = new ConcurrentHashMap<>();
        Map<String, BigDecimal> weekAdCostMap = new ConcurrentHashMap<>();
        Map<String, BigDecimal> weekShopSalesMap = new ConcurrentHashMap<>();
        Map<String, Long> weekShopSaleNumMap = new ConcurrentHashMap<>();
        Map<String, Integer> weekAdSaleNumMap = new ConcurrentHashMap<>();

        weekDate.stream().filter(Objects::nonNull).forEach(week -> {
            String weekDay = String.join("~", week);

            Integer wClicks = 0;
            long wImpressions = 0;
            BigDecimal wAdCost = BigDecimal.ZERO;
            BigDecimal wAdSale = BigDecimal.ZERO;
            int wAdOrder = 0;
            BigDecimal wShopSales = BigDecimal.ZERO;
            long wShopSaleNum = 0L;
            int wAdSaleNum = 0;
            List<EffectDataBo> wList = list.stream().filter(Objects::nonNull).
                    filter(item -> DateUtil.compareDate(DateUtil.strToDate(item.getDate(), DateUtil.PATTERN), DateUtil.strToDate4(week.get(0))) >= 0
                            && DateUtil.compareDate(DateUtil.strToDate(item.getDate(), DateUtil.PATTERN), DateUtil.strToDate4(week.get(1))) <= 0)
                    .collect(Collectors.toList());

            List<String> shopInfo = shopSaleDtoMap.keySet().stream().filter(item -> DateUtil.compareDate(DateUtil.strToDate(item, DateUtil.PATTERN), DateUtil.strToDate4(week.get(0))) >= 0
                            && DateUtil.compareDate(DateUtil.strToDate(item, DateUtil.PATTERN), DateUtil.strToDate4(week.get(1))) <= 0)
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(shopInfo)) {
                for (String shopDate : shopInfo) {
                    ShopSaleDto shopSaleDto = shopSaleDtoMap.get(shopDate);
                    if (shopSaleDto != null) {
                        if (shopSaleDto.getSumRange() != null) {
                            wShopSales = wShopSales.add(shopSaleDto.getSumRange());
                        }
                        if (shopSaleDto.getSumSaleNum() != null) {
                            wShopSaleNum += shopSaleDto.getSumSaleNum();
                        }
                    }
                }
            }

            for (EffectDataBo dto : wList) {
                if (dto.getClicks() != null) {
                    wClicks += dto.getClicks();
                }
                if (dto.getImpressions() != null) {
                    wImpressions += dto.getImpressions();
                }
                if (dto.getCost() != null) {
                    wAdCost = wAdCost.add(dto.getCost());
                }
                if (dto.getTotalSales() != null) {
                    wAdSale = wAdSale.add(dto.getTotalSales());
                }
                if (dto.getOrderNum() != null) {
                    wAdOrder += dto.getOrderNum();
                }
                if (dto.getSaleNum() != null) {
                    wAdSaleNum += dto.getSaleNum();
                }
            }

            weekClickMap.put(weekDay, wClicks);
            weekAdCostMap.put(weekDay, wAdCost);
            weekImpressionsMap.put(weekDay, wImpressions);
            weekAdSalesMap.put(weekDay, wAdSale);
            weekAdOrderMap.put(weekDay, wAdOrder);
            weekAdSaleNumMap.put(weekDay, wAdSaleNum);
            weekShopSalesMap.put(weekDay, wShopSales);
            weekShopSaleNumMap.put(weekDay, wShopSaleNum);
        });

        List<String> weekList = weekDate.stream().map(item -> String.join("~", item)).collect(Collectors.toList());

        List<EffectDataBo> vos = new ArrayList<>();

        weekList.stream().filter(Objects::nonNull).forEach(item -> {
            BigDecimal adSale = Optional.ofNullable(weekAdSalesMap.get(item)).orElse(BigDecimal.ZERO);
            BigDecimal adCost = Optional.ofNullable(weekAdCostMap.get(item)).orElse(BigDecimal.ZERO);
            Integer clicks = Optional.ofNullable(weekClickMap.get(item)).orElse(0);
            int adOrder = Optional.ofNullable(weekAdOrderMap.get(item)).orElse(0);
            Long impressions = Optional.ofNullable(weekImpressionsMap.get(item)).orElse(0L);
            Long shopSaleNum = weekShopSaleNumMap.getOrDefault(item, 0L);
            BigDecimal shopSale = weekShopSalesMap.getOrDefault(item, BigDecimal.ZERO);
            Integer saleNum = weekAdSaleNumMap.getOrDefault(item, 0);
            EffectDataBo vo = new EffectDataBo();
            vo.setDate(item);
            vo.setClicks(clicks);
            vo.setImpressions(impressions);
            vo.setCost(adCost.setScale(2, RoundingMode.HALF_UP));
            vo.setTotalSales(adSale.setScale(2, RoundingMode.HALF_UP));
            vo.setOrderNum(adOrder);
            vo.setSaleNum(saleNum);
            vo.setShopSaleNum(shopSaleNum.intValue());
            vo.setShopSales(shopSale);
            calAdCalDataReflexOther(vo);
            vos.add(vo);
        });

        return vos;
    }

    //按月-生成chart图数据
    private List<EffectDataBo> getMonthList(Date startDate, Date endDate, List<EffectDataBo> list, Map<String, ShopSaleDto> shopSaleDtoMap) {
        Map<String, Integer> monthClick = list.stream().collect(Collectors.groupingBy(item -> DateUtil.dateToStrWithFormat(DateUtil.strToDate(item.getDate(), DateUtil.PATTERN), DateUtil.PATTERN_MONTH), Collectors.summingInt(e -> e.getClicks() == null ? 0 : e.getClicks())));
        Map<String, Long> monthImpressions = list.stream().collect(Collectors.groupingBy(item -> DateUtil.dateToStrWithFormat(DateUtil.strToDate(item.getDate(), DateUtil.PATTERN), DateUtil.PATTERN_MONTH), Collectors.summingLong(e -> e.getImpressions() == null ? 0L : e.getImpressions())));
        Map<String, Integer> monthAdOrder = list.stream().collect(Collectors.groupingBy(item -> DateUtil.dateToStrWithFormat(DateUtil.strToDate(item.getDate(), DateUtil.PATTERN), DateUtil.PATTERN_MONTH), Collectors.summingInt(e -> e.getOrderNum() == null ? 0 : e.getOrderNum())));
        Map<String, BigDecimal> monthCost = list.stream().collect(Collectors.groupingBy(item -> DateUtil.dateToStrWithFormat(DateUtil.strToDate(item.getDate(), DateUtil.PATTERN), DateUtil.PATTERN_MONTH), Collectors.reducing(BigDecimal.ZERO, (e -> e.getCost() == null ? BigDecimal.ZERO : e.getCost()), BigDecimal::add)));
        Map<String, BigDecimal> monthAdSale = list.stream().collect(Collectors.groupingBy(item -> DateUtil.dateToStrWithFormat(DateUtil.strToDate(item.getDate(), DateUtil.PATTERN), DateUtil.PATTERN_MONTH), Collectors.reducing(BigDecimal.ZERO, (e -> e.getTotalSales() == null ? BigDecimal.ZERO : e.getTotalSales()), BigDecimal::add)));
        Map<String, BigDecimal> monthShopSalesMap = new HashMap<>();
        Map<String, Integer> monthShopSaleNumMap = new HashMap<>();
        Map<String, Integer> monthAdSaleNumMap = list.stream().collect(Collectors.groupingBy(item -> DateUtil.dateToStrWithFormat(DateUtil.strToDate(item.getDate(), DateUtil.PATTERN), DateUtil.PATTERN_MONTH), Collectors.summingInt(e -> e.getSaleNum() == null ? 0 : e.getSaleNum())));


        Map<String, List<String>> setMap = shopSaleDtoMap.keySet().stream().collect(Collectors.groupingBy(item -> DateUtil.dateToStrWithFormat(DateUtil.strToDate(item, DateUtil.PATTERN), DateUtil.PATTERN_MONTH)));
        if (setMap != null && setMap.size() > 0) {
            setMap.forEach((key, value) -> {
                int wShopSaleNum = 0;
                BigDecimal wShopSales = BigDecimal.ZERO;
                for (String date : value) {
                    ShopSaleDto shopSaleDto = shopSaleDtoMap.get(date);
                    if (shopSaleDto != null) {
                        if (shopSaleDto.getSumRange() != null) {
                            wShopSales = wShopSales.add(shopSaleDto.getSumRange());
                        }
                        if (shopSaleDto.getSumSaleNum() != null) {
                            wShopSaleNum += shopSaleDto.getSumSaleNum();
                        }
                    }
                }
                monthShopSalesMap.put(key, wShopSales);
                monthShopSaleNumMap.put(key, wShopSaleNum);
            });
        }


        List<String> dateList = new ArrayList<>();
        LocalDate startLocalDate = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().withDayOfMonth(1);
        LocalDate endLocalDate = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().withDayOfMonth(1);
        for (LocalDate date = startLocalDate; date.isBefore(endLocalDate.plusMonths(1)); date = date.plusMonths(1)) {
            dateList.add(date.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_MONTH)));
        }
        List<EffectDataBo> vos = new ArrayList<>();

        dateList.stream().filter(Objects::nonNull).forEach(item -> {
            BigDecimal adSale = Optional.ofNullable(monthAdSale.get(item)).orElse(BigDecimal.ZERO);
            BigDecimal adCost = Optional.ofNullable(monthCost.get(item)).orElse(BigDecimal.ZERO);
            Integer clicks = Optional.ofNullable(monthClick.get(item)).orElse(0);
            int adOrder = Optional.ofNullable(monthAdOrder.get(item)).orElse(0);
            Long impressions = Optional.ofNullable(monthImpressions.get(item)).orElse(0L);
            int shopSaleNum = monthShopSaleNumMap.getOrDefault(item, 0);
            BigDecimal shopSale = monthShopSalesMap.getOrDefault(item, BigDecimal.ZERO);
            Integer saleNum = monthAdSaleNumMap.getOrDefault(item, 0);

            EffectDataBo vo = new EffectDataBo();
            vo.setDate(item);
            vo.setClicks(clicks);
            vo.setImpressions(impressions);
            vo.setCost(adCost.setScale(2, RoundingMode.HALF_UP));
            vo.setTotalSales(adSale.setScale(2, RoundingMode.HALF_UP));
            vo.setOrderNum(adOrder);
            vo.setSaleNum(saleNum);
            vo.setShopSaleNum(shopSaleNum);
            vo.setShopSales(shopSale);
            calAdCalDataReflexOther(vo);
            vos.add(vo);
        });
        return vos;
    }

    //小时级-生成chart图表数据
    private List<AdHomeChartRpcVo> getHourlyChart(List<EffectDataBo> sortVos, String modelType) {
        List<AdHomeChartRpcVo> chartVos = new ArrayList<>();
        //封装数据
        List<AdHomeChartRpcVo.ChartRpcRecord> adCostList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adSaleList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> acosList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> roasList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> impressionsList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> clicksList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> cvrList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> ctrList = Lists.newArrayList();

        List<AdHomeChartRpcVo.ChartRpcRecord> orderNumList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> saleNumList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> cpcList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> spcList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> cpaList = Lists.newArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> advertisingUnitPriceList = Lists.newArrayList();

        sortVos.stream().filter(Objects::nonNull).forEachOrdered(item -> {
            AdHomeChartRpcVo.ChartRpcRecord adCost = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getTime())
                    .setValue(item.getCost() == null ? "0.00" : item.getCost().setScale(2, RoundingMode.HALF_UP).toPlainString())
                    .setMomValue(StringUtils.isBlank(item.getCostMomValue()) ? "0.00" : item.getCostMomValue())
                    .build();
            adCostList.add(adCost);

            AdHomeChartRpcVo.ChartRpcRecord adSale = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getTime())
                    .setValue(item.getTotalSales() == null ? "0.00" : item.getTotalSales().setScale(2, RoundingMode.HALF_UP).toPlainString())
                    .setMomValue(StringUtils.isBlank(item.getTotalSalesMomValue()) ? "0.00" : item.getTotalSalesMomValue())
                    .build();
            adSaleList.add(adSale);

            AdHomeChartRpcVo.ChartRpcRecord acos = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getTime())
                    .setValue(item.getAcos() == null ? "0.00" : item.getAcos().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toPlainString())
                    .setMomValue(StringUtils.isBlank(item.getAcosMomValue()) ? "0.00" : item.getAcosMomValue())
                    .build();
            acosList.add(acos);
            AdHomeChartRpcVo.ChartRpcRecord roas = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getTime())
                    .setValue(item.getRoas() == null ? "0.00" : item.getRoas().setScale(2, RoundingMode.HALF_UP).toPlainString())
                    .setMomValue(StringUtils.isBlank(item.getRoasMomValue()) ? "0.00" : item.getRoasMomValue())
                    .build();
            roasList.add(roas);

            AdHomeChartRpcVo.ChartRpcRecord impression = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getTime())
                    .setValue(item.getImpressions() == null ? "0.00" : String.valueOf(item.getImpressions()))
                    .setMomValue(StringUtils.isBlank(item.getImpressionsMomValue()) ? "0.00" : item.getImpressionsMomValue())
                    .build();
            impressionsList.add(impression);

            AdHomeChartRpcVo.ChartRpcRecord clicks = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getTime())
                    .setValue(item.getClicks() == null ? "0.00" : String.valueOf(item.getClicks()))
                    .setMomValue(StringUtils.isBlank(item.getClicksMomValue()) ? "0.00" : item.getClicksMomValue())
                    .build();
            clicksList.add(clicks);

            AdHomeChartRpcVo.ChartRpcRecord cvr = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getTime())
                    .setValue(item.getConversionRate() == null ? "0.00" : item.getConversionRate().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toPlainString())
                    .setMomValue(StringUtils.isBlank(item.getConversionRateMomValue()) ? "0.00" : item.getConversionRateMomValue())
                    .build();
            cvrList.add(cvr);

            AdHomeChartRpcVo.ChartRpcRecord ctr = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getTime())
                    .setValue(item.getClickRate() == null ? "0.00" : item.getClickRate().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toPlainString())
                    .setMomValue(StringUtils.isBlank(item.getClickRateMomValue()) ? "0.00" : item.getClickRateMomValue())
                    .build();
            ctrList.add(ctr);

            AdHomeChartRpcVo.ChartRpcRecord.Builder orderNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            orderNum.setDate(item.getTime());
            orderNum.setValue(getGrpcValue(item.getOrderNum()));
            orderNum.setMomValue(getGrpcValue(item.getOrderNumMomValue()));
            orderNumList.add(orderNum.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder salesNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            salesNum.setDate(item.getTime());
            salesNum.setValue(getGrpcValue(item.getSaleNum()));
            salesNum.setMomValue(getGrpcValue(item.getSaleNumMomValue()));
            saleNumList.add(salesNum.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder cpc = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            cpc.setDate(item.getTime());
            cpc.setValue(getGrpcValue(item.getCpc()));
            cpc.setMomValue(getGrpcValue(item.getCpcMomValue()));
            cpcList.add(cpc.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder cpa = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            cpa.setDate(item.getTime());
            cpa.setValue(getGrpcValue(item.getCpa()));
            cpa.setMomValue(getGrpcValue(item.getCpaMomValue()));
            cpaList.add(cpa.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder spc = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            spc.setDate(item.getTime());
            spc.setValue(getGrpcValue(item.getSpc()));
            spc.setMomValue(getGrpcValue(item.getSpcMomValue()));
            spcList.add(spc.build());

            AdHomeChartRpcVo.ChartRpcRecord.Builder advertisingUnitPrice = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            advertisingUnitPrice.setDate(item.getTime());
            advertisingUnitPrice.setValue(getGrpcValue(item.getAdvertisingUnitPrice()));
            advertisingUnitPrice.setMomValue(getGrpcValue(item.getAdvertisingUnitPriceMomValue()));
            advertisingUnitPriceList.add(advertisingUnitPrice.build());
        });

        Int32Value size = Int32Value.of(24);
        if (EffectDataModelTypeEnum.AD_EFFECT.getCode().equals(modelType)) {
            chartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("广告花费")
                    .setName("cost")
                    .setTotal(size)
                    .addAllRecords(adCostList)
                    .build());
            chartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("广告销售额")
                    .setName("totalSales")
                    .setTotal(size)
                    .addAllRecords(adSaleList)
                    .build());
            chartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("ACoS")
                    .setName("acos")
                    .setTotal(size)
                    .addAllRecords(acosList)
                    .build());
            chartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("ROAS")
                    .setName("roas")
                    .setTotal(size)
                    .addAllRecords(roasList)
                    .build());
        } else {
            chartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("广告曝光量")
                    .setName("impressions")
                    .setTotal(size)
                    .addAllRecords(impressionsList)
                    .build());
            chartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("广告点击量")
                    .setName("clicks")
                    .setTotal(size)
                    .addAllRecords(clicksList)
                    .build());
            chartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("广告转化率")
                    .setName("conversionRate")
                    .setTotal(size)
                    .addAllRecords(cvrList)
                    .build());
            chartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("广告点击率")
                    .setName("clickRate")
                    .setTotal(size)
                    .addAllRecords(ctrList)
                    .build());

            if (EffectDataModelTypeEnum.AD_INDEX.getCode().equals(modelType)) {

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("广告花费")
                        .setName("cost")
                        .setTotal(size)
                        .addAllRecords(adCostList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("广告销售额")
                        .setName("totalSales")
                        .setTotal(size)
                        .addAllRecords(adSaleList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("ACoS")
                        .setName("acos")
                        .setTotal(size)
                        .addAllRecords(acosList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("ROAS")
                        .setName("roas")
                        .setTotal(size)
                        .addAllRecords(roasList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("广告订单量")
                        .setName("orderNum")
                        .setTotal(size)
                        .addAllRecords(orderNumList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("广告销量")
                        .setName("saleNum")
                        .setTotal(size)
                        .addAllRecords(saleNumList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("CPC")
                        .setName("cpc")
                        .setTotal(size)
                        .addAllRecords(cpcList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("SPC")
                        .setName("spc")
                        .setTotal(size)
                        .addAllRecords(spcList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("CPA")
                        .setName("cpa")
                        .setTotal(size)
                        .addAllRecords(cpaList)
                        .build());

                chartVos.add(AdHomeChartRpcVo.newBuilder()
                        .setDescription("广告笔单价")
                        .setName("advertisingUnitPrice")
                        .setTotal(size)
                        .addAllRecords(advertisingUnitPriceList)
                        .build());
            }

        }
        return chartVos;
    }

    //汇总数据
    private List<DashboardEffectDataResponse.IndicatorVo> buildIndicatorVo(EffectDataBo effectDataBo, EffectDataBo momEffectDataBo, String modelType) {
        List<DashboardEffectDataResponse.IndicatorVo> voList = new ArrayList<>();
        DashboardEffectDataResponse.IndicatorVo indicatorVo;
        if (EffectDataModelTypeEnum.AD_EFFECT.getCode().equals(modelType)) {
            BigDecimal currentCost = effectDataBo.getCost() == null ? new BigDecimal("0.00") : effectDataBo.getCost()
                    .setScale(2, RoundingMode.HALF_UP);
            BigDecimal momCost = momEffectDataBo.getCost() == null ? new BigDecimal("0.00") : momEffectDataBo.getCost()
                    .setScale(2, RoundingMode.HALF_UP);
            indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                    .setIndicator("cost")
                    .setName("广告花费")
                    .setValue(currentCost.toPlainString())
                    .setMomValue(momCost.toPlainString())
                    .setMomRate(CalculateUtil.calRate4Decimal(currentCost, momCost)).build();
            voList.add(indicatorVo);

            BigDecimal currentTotalSales = effectDataBo.getTotalSales() == null ? new BigDecimal("0.00") : effectDataBo.getTotalSales()
                    .setScale(2, RoundingMode.HALF_UP);
            BigDecimal momTotalSales = momEffectDataBo.getTotalSales() == null ? new BigDecimal("0.00") : momEffectDataBo.getTotalSales()
                    .setScale(2, RoundingMode.HALF_UP);
            indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                    .setIndicator("totalSales")
                    .setName("广告销售额")
                    .setValue(currentTotalSales.toPlainString())
                    .setMomValue(momTotalSales.toPlainString())
                    .setMomRate(CalculateUtil.calRate4Decimal(currentTotalSales, momTotalSales)).build();
            voList.add(indicatorVo);

            BigDecimal currentAcos = effectDataBo.getAcos() == null ? new BigDecimal("0.00") : effectDataBo.getAcos()
                    .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
            BigDecimal momAcos = momEffectDataBo.getAcos() == null ? new BigDecimal("0.00") : momEffectDataBo.getAcos()
                    .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
            indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                    .setIndicator("acos")
                    .setName("ACoS")
                    .setValue(currentAcos.toPlainString())
                    .setMomValue(momAcos.toPlainString())
                    .setMomRate(CalculateUtil.calRate4Decimal(currentAcos, momAcos)).build();
            voList.add(indicatorVo);

            BigDecimal currentRoas = effectDataBo.getRoas() == null ? new BigDecimal("0.00") : effectDataBo.getRoas()
                    .setScale(2, RoundingMode.HALF_UP);
            BigDecimal momRoas = momEffectDataBo.getRoas() == null ? new BigDecimal("0.00") : momEffectDataBo.getRoas()
                    .setScale(2, RoundingMode.HALF_UP);
            indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                    .setIndicator("roas")
                    .setName("ROAS")
                    .setValue(currentRoas.toPlainString())
                    .setMomValue(momRoas.toPlainString())
                    .setMomRate(CalculateUtil.calRate4Decimal(currentRoas, momRoas)).build();
            voList.add(indicatorVo);
        } else {
            indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                    .setIndicator("impressions")
                    .setName("广告曝光量")
                    .setValue(effectDataBo.getImpressions() == null ? "0.00" : effectDataBo.getImpressions().toString())
                    .setMomValue(momEffectDataBo.getImpressions() == null ? "0.00" : momEffectDataBo.getImpressions().toString())
                    .setMomRate(effectDataBo.getImpressionsMomRate() == null ? "0.00%" : effectDataBo.getImpressionsMomRate()).build();
            voList.add(indicatorVo);

            indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                    .setIndicator("clicks")
                    .setName("广告点击量")
                    .setValue(effectDataBo.getClicks() == null ? "0.00" : effectDataBo.getClicks().toString())
                    .setMomValue(momEffectDataBo.getClicks() == null ? "0.00" : momEffectDataBo.getClicks().toString())
                    .setMomRate(effectDataBo.getClicksMomRate() == null ? "0.00%" : effectDataBo.getClicksMomRate()).build();
            voList.add(indicatorVo);

            BigDecimal currentConversionRate = effectDataBo.getConversionRate() == null ? new BigDecimal("0.00") : effectDataBo.getConversionRate()
                    .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
            BigDecimal momConversionRate = momEffectDataBo.getConversionRate() == null ? new BigDecimal("0.00") : momEffectDataBo.getConversionRate()
                    .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
            indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                    .setIndicator("conversionRate")
                    .setName("广告转化率")
                    .setValue(currentConversionRate.toPlainString())
                    .setMomValue(momConversionRate.toPlainString())
                    .setMomRate(CalculateUtil.calRate4Decimal(currentConversionRate, momConversionRate)).build();
            voList.add(indicatorVo);

            BigDecimal currentClickRate = effectDataBo.getClickRate() == null ? new BigDecimal("0.00") : effectDataBo.getClickRate()
                    .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
            BigDecimal momClickRate = momEffectDataBo.getClickRate() == null ? new BigDecimal("0.00") : momEffectDataBo.getClickRate()
                    .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
            indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                    .setIndicator("clickRate")
                    .setName("广告点击率")
                    .setValue(currentClickRate.toPlainString())
                    .setMomValue(momClickRate.toPlainString())
                    .setMomRate(CalculateUtil.calRate4Decimal(currentClickRate, momClickRate)).build();
//                    .setMomRate(effectDataBo.getClickRateMomRate() == null ? "0.00%" : effectDataBo.getClickRateMomRate()).build();

            voList.add(indicatorVo);
        }
        return voList;
    }

    private List<DashboardEffectDataResponse.IndicatorVo> buildIndicatorVo(DashboardEffectDataReqVo reqVo, EffectDataBo effectDataBo, EffectDataBo momEffectDataBo, EffectDataBo yoyEffectDataBo, boolean isVc) {

        List<DashboardEffectDataResponse.IndicatorVo> voList = new ArrayList<>();
        DashboardEffectDataResponse.IndicatorVo indicatorVo;
        String nowDate = getDate(reqVo.getStartDate(), reqVo.getEndDate());
        String momDate = getDate(reqVo.getMomStartDate(), reqVo.getMomEndDate());
        String yoyDate = getDate(reqVo.getYoyStartDate(), reqVo.getYoyEndDate());

        indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                .setIndicator("cost")
                .setName("广告花费")
                .setValue(getGrpcValue(effectDataBo.getCost()))
                .setMomValue(getGrpcValue(momEffectDataBo.getCost()))
                .setYoyValue(getGrpcValue(yoyEffectDataBo.getCost()))
                .setNowDate(nowDate)
                .setYoyDate(yoyDate)
                .setMomDate(momDate)
                .setMomRate(getGrpcValue(effectDataBo.getCostMomRate()))
                .setYoyRate(getGrpcValue(effectDataBo.getCostYoyRate()))
                .setYoyDiffValue(getDiffGrpcValue(effectDataBo.getCost(), yoyEffectDataBo.getCost()))
                .setMomDiffValue(getDiffGrpcValue(effectDataBo.getCost(), momEffectDataBo.getCost()))
                .build();
        voList.add(indicatorVo);


        indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                .setIndicator("totalSales")
                .setName("广告销售额")
                .setValue(getGrpcValue(effectDataBo.getTotalSales()))
                .setMomValue(getGrpcValue(momEffectDataBo.getTotalSales()))
                .setYoyValue(getGrpcValue(yoyEffectDataBo.getTotalSales()))
                .setNowDate(nowDate)
                .setYoyDate(yoyDate)
                .setMomDate(momDate)
                .setMomRate(getGrpcValue(effectDataBo.getTotalSalesMomRate()))
                .setYoyRate(getGrpcValue(effectDataBo.getTotalSalesYoyRate()))
                .setYoyDiffValue(getDiffGrpcValue(effectDataBo.getTotalSales(), yoyEffectDataBo.getTotalSales()))
                .setMomDiffValue(getDiffGrpcValue(effectDataBo.getTotalSales(), momEffectDataBo.getTotalSales()))
                .build();
        voList.add(indicatorVo);

        indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                .setIndicator("acos")
                .setName("ACoS")
                .setValue(getGrpcValuePercent(effectDataBo.getAcos()))
                .setMomValue(getGrpcValuePercent(momEffectDataBo.getAcos()))
                .setYoyValue(getGrpcValuePercent(yoyEffectDataBo.getAcos()))
                .setNowDate(nowDate)
                .setYoyDate(yoyDate)
                .setMomDate(momDate)
                .setMomRate(getGrpcValue(effectDataBo.getAcosMomRate()))
                .setYoyRate(getGrpcValue(effectDataBo.getAcosYoyRate()))
                .setYoyDiffValue(getDiffGrpcValueFormat(effectDataBo.getAcos(), yoyEffectDataBo.getAcos()))
                .setMomDiffValue(getDiffGrpcValueFormat(effectDataBo.getAcos(), momEffectDataBo.getAcos()))
                .build();
        voList.add(indicatorVo);

        indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                .setIndicator("roas")
                .setName("ROAS")
                .setValue(getGrpcValue(effectDataBo.getRoas()))
                .setMomValue(getGrpcValue(momEffectDataBo.getRoas()))
                .setYoyValue(getGrpcValue(yoyEffectDataBo.getRoas()))
                .setNowDate(nowDate)
                .setYoyDate(yoyDate)
                .setMomDate(momDate)
                .setMomRate(getGrpcValue(effectDataBo.getRoasMomRate()))
                .setYoyRate(getGrpcValue(effectDataBo.getRoasYoyRate()))
                .setYoyDiffValue(getDiffGrpcValue(effectDataBo.getRoas(), yoyEffectDataBo.getRoas()))
                .setMomDiffValue(getDiffGrpcValue(effectDataBo.getRoas(), momEffectDataBo.getRoas()))
                .build();
        voList.add(indicatorVo);

        indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                .setIndicator("impressions")
                .setName("广告曝光量")
                .setValue(getGrpcValue(effectDataBo.getImpressions()))
                .setMomValue(getGrpcValue(momEffectDataBo.getImpressions()))
                .setYoyValue(getGrpcValue(yoyEffectDataBo.getImpressions()))
                .setNowDate(nowDate)
                .setYoyDate(yoyDate)
                .setMomDate(momDate)
                .setMomRate(getGrpcValue(effectDataBo.getImpressionsMomRate()))
                .setYoyRate(getGrpcValue(effectDataBo.getImpressionsYoyRate()))
                .setYoyDiffValue(getDiffGrpcValue(effectDataBo.getImpressions(), yoyEffectDataBo.getImpressions()))
                .setMomDiffValue(getDiffGrpcValue(effectDataBo.getImpressions(), momEffectDataBo.getImpressions()))
                .build();
        voList.add(indicatorVo);

        indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                .setIndicator("clicks")
                .setName("广告点击量")
                .setValue(getGrpcValue(effectDataBo.getClicks()))
                .setMomValue(getGrpcValue(momEffectDataBo.getClicks()))
                .setYoyValue(getGrpcValue(yoyEffectDataBo.getClicks()))
                .setNowDate(nowDate)
                .setYoyDate(yoyDate)
                .setMomDate(momDate)
                .setMomRate(getGrpcValue(effectDataBo.getClicksMomRate()))
                .setYoyRate(getGrpcValue(effectDataBo.getClicksYoyRate()))
                .setYoyDiffValue(getDiffGrpcValue(effectDataBo.getClicks(), yoyEffectDataBo.getClicks()))
                .setMomDiffValue(getDiffGrpcValue(effectDataBo.getClicks(), momEffectDataBo.getClicks()))
                .build();
        voList.add(indicatorVo);

        indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                .setIndicator("clickRate")
                .setName("广告点击率")
                .setValue(getGrpcValuePercent(effectDataBo.getClickRate()))
                .setMomValue(getGrpcValuePercent(momEffectDataBo.getClickRate()))
                .setYoyValue(getGrpcValuePercent(yoyEffectDataBo.getClickRate()))
                .setNowDate(nowDate)
                .setYoyDate(yoyDate)
                .setMomDate(momDate)
                .setMomRate(getGrpcValue(effectDataBo.getClickRateMomRate()))
                .setYoyRate(getGrpcValue(effectDataBo.getClickRateYoyRate()))
                .setYoyDiffValue(getDiffGrpcValueFormat(effectDataBo.getClickRate(), yoyEffectDataBo.getClickRate()))
                .setMomDiffValue(getDiffGrpcValueFormat(effectDataBo.getClickRate(), momEffectDataBo.getClickRate()))
                .build();
        voList.add(indicatorVo);

        indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                .setIndicator("conversionRate")
                .setName("广告转化率")
                .setValue(getGrpcValuePercent(effectDataBo.getConversionRate()))
                .setMomValue(getGrpcValuePercent(momEffectDataBo.getConversionRate()))
                .setYoyValue(getGrpcValuePercent(yoyEffectDataBo.getConversionRate()))
                .setNowDate(nowDate)
                .setYoyDate(yoyDate)
                .setMomDate(momDate)
                .setMomRate(getGrpcValue(effectDataBo.getConversionRateMomRate()))
                .setYoyRate(getGrpcValue(effectDataBo.getConversionRateYoyRate()))
                .setYoyDiffValue(getDiffGrpcValueFormat(effectDataBo.getConversionRate(), yoyEffectDataBo.getConversionRate()))
                .setMomDiffValue(getDiffGrpcValueFormat(effectDataBo.getConversionRate(), momEffectDataBo.getConversionRate()))
                .build();
        voList.add(indicatorVo);

        indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                .setIndicator("orderNum")
                .setName("广告订单量")
                .setValue(getGrpcValue(effectDataBo.getOrderNum()))
                .setMomValue(getGrpcValue(momEffectDataBo.getOrderNum()))
                .setYoyValue(getGrpcValue(yoyEffectDataBo.getOrderNum()))
                .setNowDate(nowDate)
                .setYoyDate(yoyDate)
                .setMomDate(momDate)
                .setMomRate(getGrpcValue(effectDataBo.getOrderNumMomRate()))
                .setYoyRate(getGrpcValue(effectDataBo.getOrderNumYoyRate()))
                .setYoyDiffValue(getDiffGrpcValue(effectDataBo.getOrderNum(), yoyEffectDataBo.getOrderNum()))
                .setMomDiffValue(getDiffGrpcValue(effectDataBo.getOrderNum(), momEffectDataBo.getOrderNum()))
                .build();
        voList.add(indicatorVo);

        indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                .setIndicator("saleNum")
                .setName("广告销量")
                .setValue(getGrpcValue(effectDataBo.getSaleNum()))
                .setMomValue(getGrpcValue(momEffectDataBo.getSaleNum()))
                .setYoyValue(getGrpcValue(yoyEffectDataBo.getSaleNum()))
                .setNowDate(nowDate)
                .setYoyDate(yoyDate)
                .setMomDate(momDate)
                .setMomRate(getGrpcValue(effectDataBo.getSaleNumMomRate()))
                .setYoyRate(getGrpcValue(effectDataBo.getSaleNumYoyRate()))
                .setYoyDiffValue(getDiffGrpcValue(effectDataBo.getSaleNum(), yoyEffectDataBo.getSaleNum()))
                .setMomDiffValue(getDiffGrpcValue(effectDataBo.getSaleNum(), momEffectDataBo.getSaleNum()))
                .build();
        voList.add(indicatorVo);

        indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                .setIndicator("shopSales")
                .setName("店铺销售额")
                .setValue(getGrpcValue(effectDataBo.getShopSales()))
                .setMomValue(getGrpcValue(momEffectDataBo.getShopSales()))
                .setYoyValue(getGrpcValue(yoyEffectDataBo.getShopSales()))
                .setNowDate(nowDate)
                .setYoyDate(yoyDate)
                .setMomDate(momDate)
                .setMomRate(getGrpcValue(effectDataBo.getShopSalesMomRate()))
                .setYoyRate(getGrpcValue(effectDataBo.getShopSalesYoyRate()))
                .setYoyDiffValue(getDiffGrpcValue(effectDataBo.getShopSales(), yoyEffectDataBo.getShopSales()))
                .setMomDiffValue(getDiffGrpcValue(effectDataBo.getShopSales(), momEffectDataBo.getShopSales()))
                .build();
        voList.add(indicatorVo);

        indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                .setIndicator("shopSaleNum")
                .setName("店铺销量")
                .setValue(getGrpcValue(effectDataBo.getShopSaleNum()))
                .setMomValue(getGrpcValue(momEffectDataBo.getShopSaleNum()))
                .setYoyValue(getGrpcValue(yoyEffectDataBo.getShopSaleNum()))
                .setNowDate(nowDate)
                .setYoyDate(yoyDate)
                .setMomDate(momDate)
                .setMomRate(getGrpcValue(effectDataBo.getShopSaleNumMomRate()))
                .setYoyRate(getGrpcValue(effectDataBo.getShopSaleNumYoyRate()))
                .setYoyDiffValue(getDiffGrpcValue(effectDataBo.getShopSaleNum(), yoyEffectDataBo.getShopSaleNum()))
                .setMomDiffValue(getDiffGrpcValue(effectDataBo.getShopSaleNum(), momEffectDataBo.getShopSaleNum()))
                .build();
        voList.add(indicatorVo);

        if (!isVc) {
            indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                    .setIndicator("acots")
                    .setName("ACoTS")
                    .setValue(getGrpcValuePercent(effectDataBo.getAcots()))
                    .setMomValue(getGrpcValuePercent(momEffectDataBo.getAcots()))
                    .setYoyValue(getGrpcValuePercent(yoyEffectDataBo.getAcots()))
                    .setNowDate(nowDate)
                    .setYoyDate(yoyDate)
                    .setMomDate(momDate)
                    .setMomRate(getGrpcValue(effectDataBo.getAcotsMomRate()))
                    .setYoyRate(getGrpcValue(effectDataBo.getAcotsYoyRate()))
                    .setYoyDiffValue(getDiffGrpcValueFormat(effectDataBo.getAcots(), yoyEffectDataBo.getAcots()))
                    .setMomDiffValue(getDiffGrpcValueFormat(effectDataBo.getAcots(), momEffectDataBo.getAcots()))
                    .build();
            voList.add(indicatorVo);

            indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                    .setIndicator("asots")
                    .setName("ASoTS")
                    .setValue(getGrpcValuePercent(effectDataBo.getAsots()))
                    .setMomValue(getGrpcValuePercent(momEffectDataBo.getAsots()))
                    .setYoyValue(getGrpcValuePercent(yoyEffectDataBo.getAsots()))
                    .setNowDate(nowDate)
                    .setYoyDate(yoyDate)
                    .setMomDate(momDate)
                    .setMomRate(getGrpcValue(effectDataBo.getAsotsMomRate()))
                    .setYoyRate(getGrpcValue(effectDataBo.getAsotsYoyRate()))
                    .setYoyDiffValue(getDiffGrpcValueFormat(effectDataBo.getAsots(), yoyEffectDataBo.getAsots()))
                    .setMomDiffValue(getDiffGrpcValueFormat(effectDataBo.getAsots(), momEffectDataBo.getAsots()))
                    .build();
            voList.add(indicatorVo);

        } else {
            indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                    .setIndicator("acots")
                    .setName("ACoTS")
                    .setValue("-")
                    .setMomValue("-")
                    .setYoyValue("-")
                    .setNowDate(nowDate)
                    .setYoyDate(yoyDate)
                    .setMomDate(momDate)
                    .setMomRate("-")
                    .setYoyRate("-")
                    .setYoyDiffValue("-")
                    .setMomDiffValue("-")
                    .build();
            voList.add(indicatorVo);

            indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                    .setIndicator("asots")
                    .setName("ASoTS")
                    .setValue("-")
                    .setMomValue("-")
                    .setYoyValue("-")
                    .setNowDate(nowDate)
                    .setYoyDate(yoyDate)
                    .setMomDate(momDate)
                    .setMomRate("-")
                    .setYoyRate("-")
                    .setYoyDiffValue("-")
                    .setMomDiffValue("-")
                    .build();
            voList.add(indicatorVo);
        }

        indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                .setIndicator("cpc")
                .setName("CPC")
                .setValue(getGrpcValue(effectDataBo.getCpc()))
                .setMomValue(getGrpcValue(momEffectDataBo.getCpc()))
                .setYoyValue(getGrpcValue(yoyEffectDataBo.getCpc()))
                .setNowDate(nowDate)
                .setYoyDate(yoyDate)
                .setMomDate(momDate)
                .setMomRate(getGrpcValue(effectDataBo.getCpcMomRate()))
                .setYoyRate(getGrpcValue(effectDataBo.getCpcYoyRate()))
                .setYoyDiffValue(getDiffGrpcValue(effectDataBo.getCpc(), yoyEffectDataBo.getCpc()))
                .setMomDiffValue(getDiffGrpcValue(effectDataBo.getCpc(), momEffectDataBo.getCpc()))
                .build();
        voList.add(indicatorVo);


        indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                .setIndicator("spc")
                .setName("SPC")
                .setValue(getGrpcValue(effectDataBo.getSpc()))
                .setMomValue(getGrpcValue(momEffectDataBo.getSpc()))
                .setYoyValue(getGrpcValue(yoyEffectDataBo.getSpc()))
                .setNowDate(nowDate)
                .setYoyDate(yoyDate)
                .setMomDate(momDate)
                .setMomRate(getGrpcValue(effectDataBo.getSpcMomRate()))
                .setYoyRate(getGrpcValue(effectDataBo.getSpcYoyRate()))
                .setYoyDiffValue(getDiffGrpcValue(effectDataBo.getSpc(), yoyEffectDataBo.getSpc()))
                .setMomDiffValue(getDiffGrpcValue(effectDataBo.getSpc(), momEffectDataBo.getSpc()))
                .build();
        voList.add(indicatorVo);


        indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                .setIndicator("cpa")
                .setName("CPA")
                .setValue(getGrpcValue(effectDataBo.getCpa()))
                .setMomValue(getGrpcValue(momEffectDataBo.getCpa()))
                .setYoyValue(getGrpcValue(yoyEffectDataBo.getCpa()))
                .setNowDate(nowDate)
                .setYoyDate(yoyDate)
                .setMomDate(momDate)
                .setMomRate(getGrpcValue(effectDataBo.getCpaMomRate()))
                .setYoyRate(getGrpcValue(effectDataBo.getCpaYoyRate()))
                .setYoyDiffValue(getDiffGrpcValue(effectDataBo.getCpa(), yoyEffectDataBo.getCpa()))
                .setMomDiffValue(getDiffGrpcValue(effectDataBo.getCpa(), momEffectDataBo.getCpa()))
                .build();
        voList.add(indicatorVo);

        indicatorVo = DashboardEffectDataResponse.IndicatorVo.newBuilder()
                .setIndicator("advertisingUnitPrice")
                .setName("广告笔单价")
                .setValue(getGrpcValue(effectDataBo.getAdvertisingUnitPrice()))
                .setMomValue(getGrpcValue(momEffectDataBo.getAdvertisingUnitPrice()))
                .setYoyValue(getGrpcValue(yoyEffectDataBo.getAdvertisingUnitPrice()))
                .setNowDate(nowDate)
                .setYoyDate(yoyDate)
                .setMomDate(momDate)
                .setMomRate(getGrpcValue(effectDataBo.getAdvertisingUnitPriceMomRate()))
                .setYoyRate(getGrpcValue(effectDataBo.getAdvertisingUnitPriceYoyRate()))
                .setYoyDiffValue(getDiffGrpcValue(effectDataBo.getAdvertisingUnitPrice(), yoyEffectDataBo.getAdvertisingUnitPrice()))
                .setMomDiffValue(getDiffGrpcValue(effectDataBo.getAdvertisingUnitPrice(), momEffectDataBo.getAdvertisingUnitPrice()))
                .build();
        voList.add(indicatorVo);
        return voList;
    }

    private String getGrpcValue(BigDecimal value) {
        if (value == null) {
            return "0.00";
        }
        return value.setScale(2, RoundingMode.HALF_UP).toPlainString();
    }

    private String getDiffGrpcValue(BigDecimal value, BigDecimal value1) {
        if (value == null || value1 == null) {
            return "0.00";
        }
        return value.subtract(value1).setScale(2, RoundingMode.HALF_UP).toPlainString();
    }

    private static String getDiffGrpcValueFormat(BigDecimal value, BigDecimal value1) {
        if (value == null || value1 == null) {
            return "0.00%";
        }
        return CalculateUtil.formatPercent(value.subtract(value1).setScale(5, RoundingMode.HALF_UP));
    }

    private String getDiffGrpcValue(Integer value, Integer value1) {
        if (value == null || value1 == null) {
            return "0";
        }
        return String.valueOf(value - value1);
    }

    private String getDiffGrpcValue(Long value, Long value1) {
        if (value == null || value1 == null) {
            return "0";
        }
        return String.valueOf(value - value1);
    }

    private String getGrpcDateValue(String value) {
        return value == null ? "-" : value;
    }

    private String getGrpcValue(Long value) {
        if (value == null) {
            return "0";
        }
        return value.toString();
    }

    private String getGrpcValue(Integer value) {
        if (value == null) {
            return "0";
        }
        return value.toString();
    }

    private static String getGrpcValuePercent(BigDecimal value) {
        if (value == null) {
            return "0.00%";
        }
        return CalculateUtil.formatPercent(value);
    }

    private static String getGrpcValueNoPercent(BigDecimal value) {
        if (value == null) {
            return "0.00";
        }
        return CalculateUtil.formatPercent(value).replace("%", "");
    }

    private String getGrpcValue(String value) {
        return value == null ? "0.00" : value;
    }

    /**
     * 获取日期格式
     */
    private String getDate(String stareDate, String endDate) {
        if (StringUtils.isBlank(stareDate) || StringUtils.isBlank(endDate)) {
            return "-";
        }
        if (stareDate.equals(endDate)) {
            return stareDate;
        }
        return stareDate + "~" + endDate;
    }

    //获取小时级维度map
    private Map<Integer, EffectDataBo> getHourlyEffectDataMap(List<EffectDataBo> dataList) {
        if (CollectionUtils.isNotEmpty(dataList)) {
            return dataList.stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, (e1, e2) -> e2));
        }
        return new HashMap<>();
    }

    @Override
    public List<Integer> getShopIdsByCampaignIdsOrPortfolioIds(int puid, List<Integer> shopIdList, List<String> campaignIds, List<String> portfolioIds) {
        return odsAmazonAdCampaignAllDao.getShopIdsByCampaignIdsOrPortfolioIds(puid, shopIdList, campaignIds, portfolioIds);
    }


    @Override
    public void setShopIdsByCampaignIdsOrPortfolioIds(DashboardBaseReqVo reqVo) {


        if (CollectionUtils.isNotEmpty(reqVo.getPortfolioIds()) && reqVo.getPortfolioIds().contains("-1")) {
            return;
        }
        List<Integer> shopIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(reqVo.getPortfolioIds()) || CollectionUtils.isNotEmpty(reqVo.getCampaignIds())) {
            if (CollectionUtils.isNotEmpty(reqVo.getCampaignIds())) {
                List<Integer> shopIdsByCampaignIdsOrPortfolioIds = getShopIdsByCampaignIdsOrPortfolioIds(reqVo.getPuid(), reqVo.getShopIdList(), reqVo.getCampaignIds(), null);
                if (CollectionUtils.isNotEmpty(shopIdsByCampaignIdsOrPortfolioIds)) {
                    shopIds.addAll(shopIdsByCampaignIdsOrPortfolioIds);
                }
            }else{
                if (CollectionUtils.isNotEmpty(reqVo.getPortfolioIds())) {
                    List<Integer> shopIdsByPortfolioIds = odsAmazonAdPortfolioDao.getShopIdsByPortfolioIds(reqVo.getPuid(), reqVo.getShopIdList(), reqVo.getPortfolioIds());
                    if (CollectionUtils.isNotEmpty(shopIdsByPortfolioIds)) {
                        shopIds.addAll(shopIdsByPortfolioIds);
                    }
                }
            }
            if (CollectionUtils.isEmpty(shopIds)) {
                reqVo.setShopIdList(Lists.newArrayList(-1));
            } else {
                reqVo.setShopIdList(Lists.newArrayList(reqVo.getShopIdList()));
                reqVo.getShopIdList().retainAll(shopIds);
                if (CollectionUtils.isEmpty(reqVo.getShopIdList())) {
                    reqVo.setShopIdList(Lists.newArrayList(-1));
                }
            }
        }
    }

    private void setShopSaleAndSaleNumTotal(List<ShopEffectDataBo> effectDataBo, List<ShopSaleOrigDto> shopSaleList) {
        Map<Integer, ShopSaleOrigDto> shopSaleDtoMap = shopSaleList.stream().collect(Collectors.toMap(ShopSaleOrigDto::getShopId, Function.identity()));
        effectDataBo.forEach(e->{
            ShopSaleOrigDto shopSaleDto = shopSaleDtoMap.get(e.getShopId());
            if (shopSaleDto == null) {
                e.setShopSales(BigDecimal.ZERO);
                e.setOrigShopSales(BigDecimal.ZERO);
                e.setShopSaleNum(0);
                return;
            }
            e.setShopSales(shopSaleDto.getSumRange() != null ? shopSaleDto.getSumRange() : BigDecimal.ZERO);
            e.setOrigShopSales(shopSaleDto.getOrigSumRange() != null ? shopSaleDto.getOrigSumRange() : BigDecimal.ZERO);
            e.setShopSaleNum(shopSaleDto.getSumSaleNum() != null ? shopSaleDto.getSumSaleNum().intValue() : 0);
        });
    }

    private List<DashboardEffectDataResponse.ShopData> builderShopData(List<ShopEffectDataBo> effectDataBo, List<ShopAuth> shopAuths, boolean isVc) {
        Map<Integer, ShopAuth> shopAuthMap = shopAuths.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
        return effectDataBo.stream().map(e->{
            return conversionShopDataVo(e, shopAuthMap.get(e.getShopId()), isVc);
       }).collect(Collectors.toList());
    }

    private DashboardEffectDataResponse.ShopData  conversionShopDataVo(ShopEffectDataBo effectDataBo, ShopAuth shopAuth, boolean isVc) {
        DashboardEffectDataResponse.ShopData.Builder builder = DashboardEffectDataResponse.ShopData.newBuilder();
        if (shopAuth != null) {
            builder.setShopId(effectDataBo.getShopId());
            builder.setShopName(shopAuth.getName());
            builder.setMarketplaceId(shopAuth.getMarketplaceId());
            builder.setMarketplaceName(shopAuth.getMarketplaceName());
        }
        builder.setDate(effectDataBo.getDate());
        builder.setCost(getGrpcValue(effectDataBo.getCost()));
        builder.setOrigCost(getGrpcValue(effectDataBo.getOrigCost()));
        builder.setTotalSales(getGrpcValue(effectDataBo.getTotalSales()));
        builder.setOrigTotalSales(getGrpcValue(effectDataBo.getOrigTotalSales()));
        builder.setAcos(getGrpcValuePercent(effectDataBo.getAcos()));
        builder.setRoas(getGrpcValue(effectDataBo.getRoas()));
        builder.setImpressions(getGrpcValue(effectDataBo.getImpressions()));
        builder.setClicks(getGrpcValue(effectDataBo.getClicks()));
        builder.setClickRate(getGrpcValuePercent(effectDataBo.getClickRate()));
        builder.setConversionRate(getGrpcValuePercent(effectDataBo.getConversionRate()));
        builder.setOrderNum(getGrpcValue(effectDataBo.getOrderNum()));
        builder.setSaleNum(getGrpcValue(effectDataBo.getSaleNum()));
        builder.setShopSales(getGrpcValue(effectDataBo.getShopSales()));
        builder.setShopSaleNum(getGrpcValue(effectDataBo.getShopSaleNum()));
        builder.setOrigShopSales(getGrpcValue(effectDataBo.getOrigShopSales()));
        if (isVc) {
            builder.setAcots("0.00");
            builder.setAsots("0.00");
        } else {
            builder.setAcots(getGrpcValuePercent(effectDataBo.getAcots()));
            builder.setAsots(getGrpcValuePercent(effectDataBo.getAsots()));
        }

        builder.setCpc(getGrpcValue(effectDataBo.getCpc()));
        builder.setSpc(getGrpcValue(effectDataBo.getSpc()));
        builder.setCpa(getGrpcValue(effectDataBo.getCpa()));
        builder.setAdvertisingUnitPrice(getGrpcValue(effectDataBo.getAdvertisingUnitPrice()));
        builder.setOrigCpc(getGrpcValue(effectDataBo.getOrigCpc()));
        builder.setOrigSpc(getGrpcValue(effectDataBo.getOrigSpc()));
        builder.setOrigCpa(getGrpcValue(effectDataBo.getOrigCpa()));
        builder.setOrigAdvertisingUnitPrice(getGrpcValue(effectDataBo.getOrigAdvertisingUnitPrice()));

        return builder.build();
    }





    private ShopDataChartRpcVo.ChartRpcRecord builderShopDataChartRpcRecord(ShopEffectDataBo effectShopDataBo, ShopAuth shopAuth, ShopDataFieldEnum fieldEnum) {
        ShopDataChartRpcVo.ChartRpcRecord.Builder builder = ShopDataChartRpcVo.ChartRpcRecord.newBuilder();
        if (shopAuth != null) {
            builder.setShopId(shopAuth.getId());
            builder.setShopName(shopAuth.getName());
            builder.setMarketplaceId(shopAuth.getMarketplaceId());
            builder.setMarketplaceName(shopAuth.getMarketplaceName());
        }
        builder.setDate(effectShopDataBo.getDate());
        builder.setValue(getShopFieldValue(effectShopDataBo, fieldEnum));
        if (ShopDataFieldEnum.COST == fieldEnum) {
            builder.setOrigValue(getGrpcValue(effectShopDataBo.getOrigCost()));
        }
        if (ShopDataFieldEnum.CPC == fieldEnum) {
            builder.setOrigValue(getGrpcValue(effectShopDataBo.getOrigCpc()));
        }
        if (ShopDataFieldEnum.CPA == fieldEnum) {
            builder.setOrigValue(getGrpcValue(effectShopDataBo.getOrigCpa()));
        }
        if (ShopDataFieldEnum.SPC == fieldEnum) {
            builder.setOrigValue(getGrpcValue(effectShopDataBo.getOrigSpc()));
        }
        if (ShopDataFieldEnum.ADVERTISING_UNIT_PRICE == fieldEnum) {
            builder.setOrigValue(getGrpcValue(effectShopDataBo.getOrigAdvertisingUnitPrice()));
        }
        if (ShopDataFieldEnum.SHOP_SALES == fieldEnum) {
            builder.setOrigValue(getGrpcValue(effectShopDataBo.getOrigShopSales()));
        }
        if (ShopDataFieldEnum.TOTAL_SALES == fieldEnum) {
            builder.setOrigValue(getGrpcValue(effectShopDataBo.getOrigTotalSales()));
        }
        return builder.build();
    }


    private List<ShopDataChartRpcVo> builderShopDataChartRpcVo(EffectDataBo effectDataBo, List<ShopEffectDataBo> effectShopDataBos, Map<Integer, ShopAuth> shopAuthMap, boolean isvc) {

        List<ShopDataChartRpcVo> result = new ArrayList<>();
        for (ShopDataFieldEnum shopDataFieldEnum : ShopDataFieldEnum.values()) {
            if (shopDataFieldEnum == ShopDataFieldEnum.ORIG_SHOP_SALES
                    || shopDataFieldEnum == ShopDataFieldEnum.ORIG_COST
                    || shopDataFieldEnum == ShopDataFieldEnum.ORIG_TOTAL_SALES
                    || shopDataFieldEnum == ShopDataFieldEnum.ORIG_CPC
                    || shopDataFieldEnum == ShopDataFieldEnum.ORIG_CPA
                    || shopDataFieldEnum == ShopDataFieldEnum.ORIG_SPC
                    || shopDataFieldEnum == ShopDataFieldEnum.ORIG_ADVERTISING_UNIT_PRICE) {
                continue;
            }
            ShopDataChartRpcVo.Builder builder = ShopDataChartRpcVo.newBuilder();
            builder.setDescription(shopDataFieldEnum.getDesc());
            builder.setName(shopDataFieldEnum.getCode());
            builder.setTotalValue(getEffectDataBoFieldValue(effectDataBo, shopDataFieldEnum));
            OrderByUtil.sortedByOrderField(effectShopDataBos, shopDataFieldEnum.getCode(), "desc", null);
            List<ShopDataChartRpcVo.ChartRpcRecord> collect = effectShopDataBos.stream().map(e -> builderShopDataChartRpcRecord(e, shopAuthMap.get(e.getShopId()), shopDataFieldEnum)).collect(Collectors.toList());
            builder.addAllRecords(collect);
            result.add(builder.build());
        }
        return result;

    }


    private String getShopFieldValue(ShopEffectDataBo adShopData, ShopDataFieldEnum field) {
        switch (field) {
            case COST:
                return getGrpcValue(adShopData.getCost());
            case ORIG_COST:
                return getGrpcValue(adShopData.getOrigCost());
            case TOTAL_SALES:
                return getGrpcValue(adShopData.getTotalSales());
            case ORIG_TOTAL_SALES:
                return getGrpcValue(adShopData.getOrigTotalSales());
            case ACOS:
                return getGrpcValuePercent(adShopData.getAcos());
            case ROAS:
                return getGrpcValue(adShopData.getRoas());
            case IMPRESSIONS:
                return getGrpcValue(adShopData.getImpressions());
            case CLICKS:
                return getGrpcValue(adShopData.getClicks());
            case CLICK_RATE:
                return getGrpcValuePercent(adShopData.getClickRate());
            case CONVERSION_RATE:
                return getGrpcValuePercent(adShopData.getConversionRate());
            case ORDER_NUM:
                return getGrpcValue(adShopData.getOrderNum());
            case SALE_NUM:
                return getGrpcValue(adShopData.getSaleNum());
            case SHOP_SALES:
                return getGrpcValue(adShopData.getShopSales());
            case SHOP_SALE_NUM:
                return getGrpcValue(adShopData.getShopSaleNum());
            case ACOTS:
                return getGrpcValuePercent(adShopData.getAcots());
            case ASOTS:
                return getGrpcValuePercent(adShopData.getAsots());
            case CPC:
                return getGrpcValue(adShopData.getCpc());
            case SPC:
                return getGrpcValue(adShopData.getSpc());
            case CPA:
                return getGrpcValue(adShopData.getCpa());
            case ADVERTISING_UNIT_PRICE:
                return getGrpcValue(adShopData.getAdvertisingUnitPrice());
            default:
                return "0.00";
        }
    }





    private String getEffectDataBoFieldValue(EffectDataBo adShopData, ShopDataFieldEnum field) {
        switch (field) {
            case COST:
                return getGrpcValue(adShopData.getCost());
            case TOTAL_SALES:
                return getGrpcValue(adShopData.getTotalSales());
            case ACOS:
                return getGrpcValue(adShopData.getAcos());
            case ROAS:
                return getGrpcValue(adShopData.getRoas());
            case IMPRESSIONS:
                return getGrpcValue(adShopData.getImpressions());
            case CLICKS:
                return getGrpcValue(adShopData.getClicks());
            case CLICK_RATE:
                return getGrpcValue(adShopData.getClickRate());
            case CONVERSION_RATE:
                return getGrpcValue(adShopData.getConversionRate());
            case ORDER_NUM:
                return getGrpcValue(adShopData.getOrderNum());
            case SALE_NUM:
                return getGrpcValue(adShopData.getSaleNum());
            case SHOP_SALES:
                return getGrpcValue(adShopData.getShopSales());
            case SHOP_SALE_NUM:
                return getGrpcValue(adShopData.getShopSaleNum());
            case ACOTS:
                return getGrpcValue(adShopData.getAcots());
            case ASOTS:
                return getGrpcValue(adShopData.getAsots());
            case CPC:
                return getGrpcValue(adShopData.getCpc());
            case SPC:
                return getGrpcValue(adShopData.getSpc());
            case CPA:
                return getGrpcValue(adShopData.getCpa());
            case ADVERTISING_UNIT_PRICE:
                return getGrpcValue(adShopData.getAdvertisingUnitPrice());
            default:
                return "0.00";
        }
    }

}
