package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName ReportVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/6/17 20:56
 **/
@Data
@ApiModel
public class ReportVo {
    @ApiModelProperty("店铺ID")
    private Integer shopId;
    @ApiModelProperty("统计时间")
    private String countDate;
    @ApiModelProperty("CPC")
    private BigDecimal cpc;
    @ApiModelProperty("广告花费")
    private BigDecimal cost;
    @ApiModelProperty("广告销售")
    private BigDecimal sales;
    @ApiModelProperty("acos")
    private BigDecimal acos;
    @ApiModelProperty("roas")
    private BigDecimal roas;
    private BigDecimal acots;
    private BigDecimal asots;
    @ApiModelProperty("曝光量")
    private Integer impressions;
    @ApiModelProperty("点击量")
    private Integer clicks;
    @ApiModelProperty("广告订单量")
    private Integer orderNum;
    @ApiModelProperty("广告销量")
    private Integer saleNum;
    @ApiModelProperty("点击率")
    private Double clickRate;
    @ApiModelProperty("广告转换率 = 订单数/点击量")
    private Double salesConversionRate;//广告转换率 = 订单数/点击量
    //每笔订单花费
    @ApiModelProperty("cost/orderNum")
    private BigDecimal cpa; //cost/orderNum
    @ApiModelProperty(value = "其他Asin")
    private String otherAsin;
    private String campaignType;
    //店铺和产品报告才有
    private BigDecimal naturalSales;
    private BigDecimal naturalSalesRmb;
    private BigDecimal naturalSalesUsd;
    private String naturalClicks;
    private Integer naturalOrderNum;         //自然订单就是总的订单数，跟广告订单数无关联
    private Double adClickRatio; //广告点击占比 广告点击/总点击
    private BigDecimal allSales;   //总销售额=naturalSales   跟广告销售额无关联   todo 废弃
    private BigDecimal allSalesUsd;  // todo 废弃
    private BigDecimal allSalesRmb;  // todo 废弃
    private BigDecimal salesRatio; //广告销售占比=sales/allSales       todo 废弃
    private Integer allOrderNum; //总订单数=naturalOrderNum ,跟广告订单数无关联   todo 废弃
    private Double adOrderRatio; //广告订单占比 orderNum/allOrderNum   todo 废弃
    private Double adConversionRate; //自然转换率=总订单数/自然点击
    //活动信息
    private String campaignId;
    private String campaignName;
//    private String campaignState;
    private String adGroupId;
    private String adGroupType;
    private String adGroupName;
    private String adGroupState;
    private String keywordText;
    private String matchType;
    private String keywordId;
    private String sku;
    private String asin;
    private String parentAsin;
    private String title;
    private String mainImage;
    private String queryId;
    private String query;
    private String queryCn;
    private String negaType; //negativeExact:精确否定，negativePhrase:词组否定
    private String targetingType; //auto自动投放,manual手动投放
    private String targetingExpression;
    private String targetId;
    private String adId;
    private String targetingText;
    private String targetState;
    private Double defaultBid;

    private String spCampaignType;  //sp广告活动类型
    private String spGroupType;     //sp广告组类型
    private String spTargetType;    //sp商品投放类型

    private Boolean isBroad;  //广泛
    private Boolean isPhrase; //词组
    private Boolean isExact;  //精准
    private Boolean isNegativeExact;  //精确否定
    private Boolean isNegativePhrase; //词组否定
    private Boolean isTargetAsin;   //投ASIN
    private Boolean isNeTargetAsin; //否ASIN
    private Boolean isTargetType;  //投放类型，默认关键词， true 关键词  false、 投放

    @ApiModelProperty("广告组合ID")
    private String portfolioId;
    @ApiModelProperty("广告组合名称")
    private String portfolioName;
    @ApiModelProperty("广告组合隐藏状态 1:隐藏  0:可见")
    private Integer isHidden;
    @ApiModelProperty("sb广告类型video,productCollection")
    private String adFormat;
    @ApiModelProperty("广告类型sb,sp")
    private String type;
    private String marketplaceId;
    @ApiModelProperty("币种")
    private String campaignBudgetCurrencyCode;


    private Integer attributedConversions7d;
    @ApiModelProperty("本广告产品订单量")
    private Integer attributedConversions7dSameSKU;
    private Integer attributedConversions7dOtherSameSKU;
    private BigDecimal attributedSales7d;
    private BigDecimal attributedSales7dSameSKU;
    private BigDecimal attributedSales7dOtherSameSKU;
    private Integer attributedUnitsOrdered7d;
    private Integer attributedUnitsOrdered7dSameSKU;
    private Integer attributedUnitsOrdered7dOtherSameSKU;
    private Integer attributedUnitsOrdered7dOtherSKU;
    private BigDecimal attributedSales7dOtherSKU;

    private Integer attributedUnitsOrdered14dOtherSKU;
    private BigDecimal attributedSales14dOtherSKU;

    /**
     * TODO 广告报告重构
     * 本广告产品销售额
     */
    private BigDecimal adSales;
    //本广告产品订单量
    private Integer adSaleNum;
    //本广告产品销量
    private Integer adSelfSaleNum;
    //“品牌新买家”订单量
    private Integer ordersNewToBrand14d;
    //“品牌新买家”销售额
    private BigDecimal salesNewToBrand14d;
    //搜索词展示量排名
    private Integer impressionRank;
    //搜索词展示份额
    private Double impressionShare;
    //其他产品广告订单量
    private Integer adOtherOrderNum;
    //其他产品广告销售额
    private BigDecimal adOtherSales;
    //其他产品广告销量
    private Integer adOtherSaleNum;
    //“品牌新买家”订单百分比
    private Double orderRateNewToBrand14d;
    //“品牌新买家”销售额百分比
    private Double salesRateNewToBrand14d;
    //“品牌新买家”订单转化率
    private Double ordersNewToBrandPercentage14d;
    //广告销售额
    private BigDecimal totalSales;
    @ApiModelProperty("广告花费占比:单个广告组合花费/查询结果广告组合花费之和*100%")
    private String adCostPercentage;
    @ApiModelProperty("广告销售额占比:单个广告组合销售额/查询结果广告组合销售额之和*100%")
    private String adSalePercentage;
    @ApiModelProperty("广告订单量占比:单个广告组合订单数/查询结果广告组合订单数之和*100%")
    private String adOrderNumPercentage;
    @ApiModelProperty("广告销量占比:单个广告组合广告销量/查询结果广告组合广告销量之和*100%")
    private String orderNumPercentage;

    @ApiModelProperty("微信端是否可添加商品投放标签")
    private Integer isAdd;
    @ApiModelProperty("微信端是否可添加否定商品投放标签")
    private Integer isNegativeAdd;
    @ApiModelProperty("搜索词排名（调接口获取）")
    private int searchFrequencyRank;
    @ApiModelProperty("排名周变化率（调接口获取）")
    private BigDecimal weekRatio;
    @ApiModelProperty("封装关键词实时排名参数")
    private KeywordsRankParamVo rankParamVo;

    /**
     * 5秒观看率
     * <p>
     * The percentage of impressions where the customer watched the complete video or 5 seconds of the video (whichever is shorter). Sponsored Brands video-only metric.
     */
    @ApiModelProperty(value = "5秒观看率")
    private Double video5SecondViewRate;

    /**
     * 5秒观看次数
     * <p>
     * The number of impressions where the customer watched the complete video or 5 seconds (whichever is shorter). Sponsored Brands video-only metric.
     */
    @ApiModelProperty(value = "5秒观看次数")
    private Integer video5SecondViews;

    /**
     * 视频完整播放次数
     * <p>
     * The number of impressions where the video was viewed to 100%. Sponsored Brands video-only metric.
     */
    @ApiModelProperty(value = "视频完整播放次数")
    private Integer videoCompleteViews;

    /**
     * 视频播至1/4次数
     * <p>
     * The number of impressions where the video was viewed to 25%. Sponsored Brands video-only metric.
     */
    @ApiModelProperty(value = "视频播至1/4次数")
    private Integer videoFirstQuartileViews;

    /**
     * 视频播至1/2次数
     * <p>
     * The number of impressions where the video was viewed to 50%. Sponsored Brands video-only metric.
     */
    @ApiModelProperty(value = "视频播至1/2次数")
    private Integer videoMidpointViews;

    /**
     * 视频播至3/4次数
     * <p>
     * The number of impressions where the video was viewed to 75%. Sponsored Brands video-only metric.
     */
    @ApiModelProperty(value = "视频播至3/4次数")
    private Integer videoThirdQuartileViews;

    /**
     * 视频取消静音
     * <p>
     * The number of impressions where a customer unmuted the video. Sponsored Brands video-only metric.
     */
    @ApiModelProperty(value = "视频取消静音")
    private Integer videoUnmutes;

    /**
     * 可见展示次数
     * <p>
     * Number of impressions that met the Media Ratings Council (MRC) viewability standard (50 percent of the ad viewable with two seconds playback completed). Sponsored Brands video-only metric.
     */
    @ApiModelProperty(value = "可见展示次数")
    private Integer viewImpressions;

    /**
     * 观看率
     */
    @ApiModelProperty(value = "观看率")
    private BigDecimal viewabilityRate;

    /**
     * 观看点击率
     */
    @ApiModelProperty(value = "观看点击率")
    private BigDecimal viewClickThroughRate;

    @ApiModelProperty("广告笔单价,广告销售额/广告订单量*100%")
    private BigDecimal advertisingUnitPrice;

    private Integer keywordMonitor;
    /**
     * 活动状态（enabled，paused，archived）
     */
    private String campaignStatus;

    /**
     * 总点击量
     */
    private Long grossClickThroughs;

    /**
     * 总曝光量
     */
    private Long grossImpressions;

    /**
     * 无效点击占比
     */
    private BigDecimal invalidClickThroughRate;

    /**
     * 无效点击量
     */
    private Long invalidClickThroughs;

    /**
     * 无效曝光占比
     */
    private BigDecimal invalidImpressionRate;

    /**
     * 无效曝光量
     */
    private Long invalidImpressions;

    private Integer occurrenceNum;
    private Boolean isKeywordType;
    private String searchTermIdStr;

    @ApiModelProperty("广告策略集合")
    private List<AdStrategyVo> strategyList;

    //新增价格、星级、评分
    private Double price;
    private String rating;
    private Integer ratingCount;

    //增加活动开始结束时间，层级运行状态(开启暂停归档)
    private String campaignStartDate;
    private String campaignEndDate;
    private String state;

    //投放类型为ASIN的商品信息
    private String imgUrl;
    private String targetTitle;
    //投放类型为类目投放
    private String category;
    private String brandName;
    private String commodityPriceRange;
    private String categoryRating;
    private String distribution;
    //回溯期:lookBack
    private String lookBack;


    public String getNaturalClicks() {
        return naturalClicks;
    }

    public void setNaturalClicks(Integer naturalClicks) {
        if (naturalClicks != null) {
            if (naturalClicks <= 0) {
                this.naturalClicks = "0";
            } else {
                this.naturalClicks = String.valueOf(naturalClicks);
            }
        } else
            this.naturalClicks = "0";
    }
}
