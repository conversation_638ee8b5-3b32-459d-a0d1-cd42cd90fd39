package com.meiyunji.sponsored.service.newDashboard.service.impl;

import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.google.protobuf.Int64Value;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdKeywordResponseVo;
import com.meiyunji.sponsored.rpc.newDashboard.KeywordListDataResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbKeywordServiceImpl;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcKeywordsService;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdKeywordDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdKeywordReportDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdKeyword;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.SBCampaignGoalEnum;
import com.meiyunji.sponsored.service.enums.SBThemesEnum;
import com.meiyunji.sponsored.service.enums.SpKeywordGroupValueEnum;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdKeywordDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.*;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdKeywordService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.newDashboard.util.OrderByUtil;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardAdUpdateKeywordRequestVo;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardKeywordReqVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-04-07 13:48
 */
@Service
@Slf4j
public class DashboardAdKeywordServiceImpl implements IDashboardAdKeywordService {
    //基础表头
    private List<String> baseHeaderList = Arrays.asList("keyword", "matchType", "displayCost", "displayTotalSales", "displayAcos", "displayRoas",
            "impressions", "clicks", "orderNum", "displayClickRate", "displayConversionRate", "saleNum", "displayCpc", "displayCpa");
    private String fileNameTemplate = "投放关键词%s%s";

    @Autowired
    private IOdsAmazonAdKeywordReportDao odsAmazonAdKeywordReportDao;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private IOdsAmazonAdKeywordDao odsAmazonAdKeywordDao;
    @Autowired
    private IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private CpcSbKeywordServiceImpl cpcSbKeywordService;
    @Autowired
    private ICpcKeywordsService cpcKeywordsService;

    @Override
    public List<DashboardAdKeywordResponseVo> queryAdKeywordCharts(DashboardKeywordReqVo reqVo) {
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(reqVo.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(reqVo.getMarketplaceIdList());
        }
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(reqVo.getOrderByField());
        List<DashboardAdKeywordDataDto> baseDataList = odsAmazonAdKeywordReportDao.queryAdKeywordCharts(
                reqVo.getPuid(), reqVo.getMarketplaceIdList(), reqVo.getShopIdList(), reqVo.getCurrency(),
                reqVo.getStartDate(), reqVo.getEndDate(), reqVo.getOrderByField(), reqVo.getOrderBy(), reqVo.getLimit(), siteToday, reqVo.getSiteToday(), reqVo.getPortfolioIds(), reqVo.getCampaignIds(), reqVo.getNoZero(), dataField);
        if (StringUtils.isNotBlank(reqVo.getListOrderField()) || StringUtils.isNotBlank(reqVo.getListOrderType())) {
            OrderByUtil.sortedByOrderField(baseDataList, reqVo.getListOrderField(), reqVo.getListOrderType(), "keyword");
        }
        List<DashboardAdKeywordResponseVo> dashboardAdKeywordResponseVos = baseDataList.stream().map(this::buildResponse).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(dashboardAdKeywordResponseVos)) {
            return fillKeyword(dashboardAdKeywordResponseVos, reqVo);
        }
        return dashboardAdKeywordResponseVos;
    }

    @Override
    public List<String> exportAdKeywordCharts(DashboardKeywordReqVo reqVo) {
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(reqVo.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(reqVo.getMarketplaceIdList());
        }
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(reqVo.getOrderByField());
        List<DashboardAdKeywordDataDto> baseDataList = odsAmazonAdKeywordReportDao.queryAdKeywordCharts(
                reqVo.getPuid(), reqVo.getMarketplaceIdList(), reqVo.getShopIdList(), reqVo.getCurrency(),
                reqVo.getStartDate(), reqVo.getEndDate(), reqVo.getOrderByField(), reqVo.getOrderBy(), reqVo.getLimit(), siteToday, reqVo.getSiteToday(), reqVo.getPortfolioIds(), reqVo.getCampaignIds(), reqVo.getNoZero(), dataField);
        if (StringUtils.isNotBlank(reqVo.getListOrderField()) || StringUtils.isNotBlank(reqVo.getListOrderType())) {
            OrderByUtil.sortedByOrderField(baseDataList, reqVo.getListOrderField(), reqVo.getListOrderType(), "keyword");
        }
        List<DashboardAdKeywordDataDto> exportDataList = new ArrayList<>(reqVo.getLimit() * DashboardKeywordMatchTypeEnum.values().length);
        // 广告投放关键词不需要打印汇总数据
        for (DashboardAdKeywordDataDto baseData : baseDataList) {
            exportDataList.addAll(buildChildList(baseData, reqVo.getCurrency()));
        }
        log.info("dashboard export adkeyword charts, query finished and begin export, puid: {}", reqVo.getPuid());
        String url = writeExcelAndUpload(exportDataList, reqVo);
        if (StringUtils.isBlank(url)) {
            return null;
        }
        log.info("dashboard export adkeyword charts, puid: {}, url: {}", reqVo.getPuid(), url);
        return Collections.singletonList(url);
    }

    private DashboardAdKeywordResponseVo buildResponse(DashboardAdKeywordDataDto baseData) {
        List<DashboardAdKeywordDataDto> childList = buildChildList(baseData, null);
        DashboardAdKeywordResponseVo.Builder parent = buildGrpcResponseVo(baseData);
        List<DashboardAdKeywordResponseVo> childDataList = childList.stream()
                .map(eachChild -> buildGrpcResponseVo(eachChild).build())
                .collect(Collectors.toList());
        parent.addAllChild(childDataList);
        return parent.build();
    }

    private List<DashboardAdKeywordDataDto> buildChildList(DashboardAdKeywordDataDto baseData, String currency) {
        String detailData = baseData.getDetailData();
        String[] childDataArr = detailData.split(";");
        Map<String, DashboardAdKeywordDataDto> matchType2Data = new HashMap<>(DashboardKeywordMatchTypeEnum.values().length);
        for (String each : childDataArr) {
            DashboardAdKeywordDataDto child = buildChildData(each, baseData, matchType2Data);
            if (StringUtils.isNotEmpty(currency)) {
                CalculateAdDataUtil.calAdCalDataForExport(child, currency);
            } else {
                CalculateAdDataUtil.calAdCalData(child);
            }
        }
        return Arrays.stream(DashboardKeywordMatchTypeEnum.values())
                .map(each -> matchType2Data.get(each.getCode()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private DashboardAdKeywordDataDto buildChildData(String childData, DashboardAdKeywordDataDto baseData, Map<String, DashboardAdKeywordDataDto> matchType2Data) {
        String[] childDataDetailArr = childData.split(",");
        String matchType = Optional.ofNullable(childDataDetailArr[0]).map(String::toUpperCase).orElse("");
        String displayMatchType = Optional.ofNullable(childDataDetailArr[0])
                .map(String::toUpperCase)
                .map(DashboardKeywordMatchTypeEnum.dataMap::get)
                .map(DashboardKeywordMatchTypeEnum::getDesc)
                .orElse(StringUtils.EMPTY);
        DashboardAdKeywordDataDto child = matchType2Data.getOrDefault(matchType, buildDefaultData());
        child.setKeyword(baseData.getKeyword());
        child.setMatchType(displayMatchType);
        child.setMatchTypeCode(matchType);
        child.setCost(child.getCost().add(new BigDecimal(childDataDetailArr[1])));
        child.setTotalSales(child.getTotalSales().add(new BigDecimal(childDataDetailArr[2])));
        child.setImpressions(child.getImpressions() + NumberUtils.toInt(childDataDetailArr[3]));
        child.setClicks(child.getClicks() + NumberUtils.toInt(childDataDetailArr[4]));
        child.setOrderNum(child.getOrderNum() + NumberUtils.toInt(childDataDetailArr[5]));
        child.setSaleNum(child.getSaleNum() + NumberUtils.toInt(childDataDetailArr[6]));
        transformKeyword(baseData, child);
        matchType2Data.put(matchType, child);
        return child;
    }

    private DashboardAdKeywordDataDto buildDefaultData() {
        DashboardAdKeywordDataDto dataDto = new DashboardAdKeywordDataDto();
        dataDto.setCost(BigDecimal.ZERO);
        dataDto.setTotalSales(BigDecimal.ZERO);
        dataDto.setImpressions(0L);
        dataDto.setClicks(0);
        dataDto.setOrderNum(0);
        dataDto.setSaleNum(0);
        return dataDto;
    }

    /**
     * 单条数据构建grpc响应对象
     *
     * @param dataDto
     * @return
     */
    private DashboardAdKeywordResponseVo.Builder buildGrpcResponseVo(DashboardAdKeywordDataDto dataDto) {
        DashboardAdKeywordResponseVo.Builder builder = DashboardAdKeywordResponseVo.newBuilder();
        //String类型直接拷贝
        BeanUtils.copyProperties(dataDto, builder);
        //手动设置的类型
        builder.setCost(CalculateUtil.formatDecimal(dataDto.getCost()));
        builder.setTotalSales(CalculateUtil.formatDecimal(dataDto.getTotalSales()));
        builder.setImpressions(String.valueOf(dataDto.getImpressions()));
        builder.setClicks(String.valueOf(dataDto.getClicks()));
        builder.setOrderNum(String.valueOf(dataDto.getOrderNum()));
        builder.setSaleNum(String.valueOf(dataDto.getSaleNum()));
        builder.setAcos(CalculateUtil.formatPercent(dataDto.getAcos()));
        builder.setRoas(CalculateUtil.formatDecimal(dataDto.getRoas()));
        builder.setClickRate(CalculateUtil.formatPercent(dataDto.getClickRate()));
        builder.setConversionRate(CalculateUtil.formatPercent(dataDto.getConversionRate()));
        builder.setCpc(CalculateUtil.formatDecimal(dataDto.getCpc()));
        builder.setCpa(CalculateUtil.formatDecimal(dataDto.getCpa()));
        return builder;
    }

    /**
     * 把数据写入excel并传到对象存储
     *
     * @param exportDataList
     * @param reqVo
     * @return
     */
    private String writeExcelAndUpload(List<DashboardAdKeywordDataDto> exportDataList, DashboardKeywordReqVo reqVo) {
        //所有数据计算完毕，构建导出数据
        String order = DashboardOrderByEnum.ASC.getCode().equals(reqVo.getOrderBy()) ? "后" : "前";
        String fileName = String.format(fileNameTemplate, order, reqVo.getLimit());

        List<String> headers = new ArrayList<>(baseHeaderList);

        //写excel并上传
        return excelService.easyExcelHandlerDownload(reqVo.getPuid(), exportDataList, fileName, DashboardAdKeywordDataDto.class, headers, true);

    }

    private void transformKeyword(DashboardAdKeywordDataDto parent, DashboardAdKeywordDataDto child) {
        if (!DashboardKeywordMatchTypeEnum.THEME.getDesc().equals(child.getMatchType())) {
            return;
        } else {
            DashboardThemeKeywordTextEnum textEnum = DashboardThemeKeywordTextEnum.enumMap.get(child.getKeyword());
            if (textEnum != null) {
                child.setKeyword(textEnum.getTextCn());
                parent.setKeyword(textEnum.getTextCn());
                return;
            }
            SpKeywordGroupValueEnum keywordGroupValueEnumByText = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByText(parent.getKeyword());
            if (keywordGroupValueEnumByText != null) {
                child.setKeyword(keywordGroupValueEnumByText.getTextCn());
                parent.setKeyword(keywordGroupValueEnumByText.getTextCn());
            }
        }
    }

    private List<DashboardAdKeywordResponseVo> fillKeyword(List<DashboardAdKeywordResponseVo> dashboardAdKeywordResponseVos, DashboardKeywordReqVo reqVo) {
        if (CollectionUtils.isEmpty(dashboardAdKeywordResponseVos)) {
            return dashboardAdKeywordResponseVos;
        }
        List<String> keywords = dashboardAdKeywordResponseVos.stream().map(DashboardAdKeywordResponseVo::getKeyword).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<OdsAmazonAdKeyword> odsAmazonAdKeywords = odsAmazonAdKeywordDao.geAllKeywordByKeywordText(keywords, reqVo);
        if (CollectionUtils.isEmpty(odsAmazonAdKeywords)) {
            log.info("not find keyword targeting");
            return dashboardAdKeywordResponseVos;
        }
        Map<String, List<OdsAmazonAdKeyword>> keywordMap = odsAmazonAdKeywords.stream().collect(Collectors.groupingBy(e -> {
                    SpKeywordGroupValueEnum keywordGroupValueEnum = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByText(e.getKeywordText());
                    SBThemesEnum sbThemesEnumByVal = SBThemesEnum.getSBThemesEnumByVal(e.getKeywordText());
                    if (keywordGroupValueEnum != null) {
                        return keywordGroupValueEnum.getTextCn();
                    } else if (sbThemesEnumByVal != null) {
                        return sbThemesEnumByVal.getMsg();
                    } else {
                        return e.getKeywordText();
                    }
                }
        ));
        List<DashboardAdKeywordResponseVo> keywordResponseVos = dashboardAdKeywordResponseVos.stream().map(e -> {
            DashboardAdKeywordResponseVo.Builder builder = e.toBuilder();
            List<OdsAmazonAdKeyword> odsAmazonAdKeywordList = keywordMap.get(e.getKeyword());
            if (CollectionUtils.isNotEmpty(odsAmazonAdKeywordList)) {
                Optional<Integer> reduce = odsAmazonAdKeywordList.stream().map(OdsAmazonAdKeyword::getCountNum).filter(Objects::nonNull).reduce(Integer::sum);
                builder.setTargetNum(reduce.orElse(0));
                List<DashboardAdKeywordResponseVo> childList = e.getChildList();
                if (CollectionUtils.isNotEmpty(childList)) {
                    Map<String, List<OdsAmazonAdKeyword>> collect = odsAmazonAdKeywordList.stream().collect(Collectors.groupingBy(OdsAmazonAdKeyword::getMatchType));
                    List<DashboardAdKeywordResponseVo> child = childList.stream().map(e1 -> {
                        List<OdsAmazonAdKeyword> odsAmazonAdKeywords1 = collect.get(e1.getMatchTypeCode().toLowerCase());

                        DashboardAdKeywordResponseVo.Builder builder1 = e1.toBuilder();
                        if (CollectionUtils.isNotEmpty(odsAmazonAdKeywords1)) {
                            Optional<Integer> reduce1 = odsAmazonAdKeywords1.stream().map(OdsAmazonAdKeyword::getCountNum).filter(Objects::nonNull).reduce(Integer::sum);
                            builder1.setTargetNum(reduce1.orElse(0));
                        } else {
                            builder1.setTargetNum(0);
                        }
                        return builder1.build();
                    }).collect(Collectors.toList());
                    builder.clearChild();
                    builder.addAllChild(child);
                }
            } else {
                builder.setTargetNum(0);
            }
            return builder.build();
        }).collect(Collectors.toList());

        return keywordResponseVos;
    }

    @Override
    public KeywordListDataResponse.KeywordListDataRpcVo.Page queryAdKeywordListByKeywordTextPage(DashboardKeywordReqVo reqVo) {
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(reqVo.getOrderByField());
        //获取sp关键词
        long t3 = Instant.now().toEpochMilli();
        Page<OdsAmazonAdKeyword> page = odsAmazonAdKeywordDao.listPageByConditionByKeywordText(Lists.newArrayList(reqVo.getKeyword()), reqVo);
        log.info("获取关键词列表数据，共耗时：{}", Instant.now().toEpochMilli() - t3);
        List<OdsAmazonAdKeyword> rows = page.getRows();
        KeywordListDataResponse.KeywordListDataRpcVo.Page.Builder builder = KeywordListDataResponse.KeywordListDataRpcVo.Page.newBuilder();
        builder.setPageNo(page.getPageNo());
        builder.setPageSize(page.getPageSize());
        builder.setTotalPage(page.getTotalPage());
        builder.setTotalSize(page.getTotalSize());
        if (CollectionUtils.isNotEmpty(rows)) {
            builder.addAllRows(buildersPageVo(rows, reqVo));
        }
        return builder.build();
    }


    private List<KeywordListDataResponse.KeywordListDataRpcVo.Page.KeywordsPageRpcVo> buildersPageVo (List<OdsAmazonAdKeyword> rows, DashboardKeywordReqVo reqVo) {
        Set<String> campaignIds = new HashSet<>();
        Set<String> spAdGroupIds = new HashSet<>();
        Set<String> sbAdGroupIds = new HashSet<>();
        Set<String> spKeywordIds = new HashSet<>();
        Set<String> sbKeywordIds = new HashSet<>();
        Set<Integer> shopIds = new HashSet<>();
        Map<String, OdsAmazonAdKeyword> keywordMap = rows.stream().peek(e -> {
            if (CampaignTypeEnum.sp.getCampaignType().equalsIgnoreCase(e.getType())) {
                spKeywordIds.add(e.getKeywordId());
            } else {
                sbKeywordIds.add(e.getKeywordId());
            }
            shopIds.add(e.getShopId());

        }).collect(Collectors.toMap(e -> e.getKeywordId() + "|" + e.getShopId(), Function.identity()));



        Map<String, AmazonAdKeyword> adSpKeywordMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(spKeywordIds)) {
            //获取sp关键词
            long t3 = Instant.now().toEpochMilli();
            List<AmazonAdKeyword> listKeywordByKeywordIds = amazonAdKeywordShardingDao.getListKeywordByKeywordIds(reqVo.getPuid(), Lists.newArrayList(shopIds), Lists.newArrayList(spKeywordIds));
            for (AmazonAdKeyword  amazonAdKeyword: listKeywordByKeywordIds) {
                spAdGroupIds.add(amazonAdKeyword.getAdGroupId());
                campaignIds.add(amazonAdKeyword.getCampaignId());
                adSpKeywordMap.put(amazonAdKeyword.getKeywordId(), amazonAdKeyword);
            }
            log.info("获取sp关键词，共耗时：{}", Instant.now().toEpochMilli() - t3);
        }


        Map<String, AmazonSbAdKeyword> adSbKeywordMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sbKeywordIds)) {
            //获取sb关键词
            long t4 = Instant.now().toEpochMilli();
            List<AmazonSbAdKeyword> listKeywordByKeywordIds = amazonSbAdKeywordDao.getListKeywordByKeywordIds(reqVo.getPuid(), Lists.newArrayList(shopIds), Lists.newArrayList(sbKeywordIds));
            for (AmazonSbAdKeyword  amazonAdKeyword: listKeywordByKeywordIds) {
                sbAdGroupIds.add(amazonAdKeyword.getAdGroupId());
                campaignIds.add(amazonAdKeyword.getCampaignId());
                adSbKeywordMap.put(amazonAdKeyword.getKeywordId(), amazonAdKeyword);
            }
            log.info("获取sb关键词，共耗时：{}", Instant.now().toEpochMilli() - t4);
        }

        //获取店铺名称
        long t1 = Instant.now().toEpochMilli();
        Map<Integer, ShopAuth> shopAuthMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(shopIds)) {
            shopAuthMap.putAll(shopAuthDao.listAllByIds(reqVo.getPuid(), Lists.newArrayList(shopIds)).
                    parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(ShopAuth::getId, Function.identity())));
        }
        log.info("获取店铺名称，共耗时：{}", Instant.now().toEpochMilli() - t1);
        //获取广告活动名称
        long t2 = Instant.now().toEpochMilli();
        Map<String, AmazonAdCampaignAll> campaignAllMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            campaignAllMap.putAll(amazonAdCampaignAllDao.getNameByShopIdsAndCampaignIds(reqVo.getPuid(), Lists.newArrayList(shopIds), Lists.newArrayList(campaignIds), null, null).
                    stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity())));
        }

        log.info("获取广告活动名称，共耗时：{}", Instant.now().toEpochMilli() - t2);
        Map<String, AmazonAdGroup> adGroupMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(spAdGroupIds)) {
            //获取sp广告组名称
            long t3 = Instant.now().toEpochMilli();
            adGroupMap.putAll(amazonAdGroupDao.getNameByShopIdsAndGroupIds(reqVo.getPuid(), Lists.newArrayList(shopIds), Lists.newArrayList(spAdGroupIds), null).
                    stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity())));
            log.info("获取sp广告组名称，共耗时：{}", Instant.now().toEpochMilli() - t3);
        }


        Map<String, AmazonSbAdGroup> sbAdGroupMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sbAdGroupIds)) {
            //获取sb广告组名称
            long t4 = Instant.now().toEpochMilli();
            sbAdGroupMap.putAll(amazonSbAdGroupDao.getNameListByShopIdsAndGroupIds(reqVo.getPuid(), Lists.newArrayList(shopIds), Lists.newArrayList(campaignIds), Lists.newArrayList(sbAdGroupIds), null).
                    stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, Function.identity())));
            log.info("获取sb广告组名称，共耗时：{}", Instant.now().toEpochMilli() - t4);
        }

        
        return rows.stream().filter(Objects::nonNull).map(e -> {

            KeywordListDataResponse.KeywordListDataRpcVo.Page.KeywordsPageRpcVo.Builder builder = KeywordListDataResponse.KeywordListDataRpcVo.Page.KeywordsPageRpcVo.newBuilder();
            builder.setType(e.getType());
            if (CampaignTypeEnum.sp.getCampaignType().equalsIgnoreCase(e.getType())) {
                AmazonAdKeyword amazonAdKeyword = adSpKeywordMap.get(e.getKeywordId());
                setSpKeywordBuild(builder, adGroupMap.get(amazonAdKeyword.getAdGroupId()),amazonAdKeyword);
                builder.setMatchTypeCode(amazonAdKeyword.getMatchType());
                DashboardKeywordMatchTypeEnum dashboardKeywordMatchTypeEnum = DashboardKeywordMatchTypeEnum.dataMap.get(amazonAdKeyword.getMatchType().toUpperCase());
                if (dashboardKeywordMatchTypeEnum != null) {
                    builder.setMatchType(dashboardKeywordMatchTypeEnum.getDesc());
                }
                AmazonAdCampaignAll amazonAdCampaignAll = campaignAllMap.get(amazonAdKeyword.getCampaignId());
                if (amazonAdCampaignAll != null) {
                    Optional.ofNullable(amazonAdCampaignAll.getName()).ifPresent(builder::setCampaignName);
                }
            } else {
                AmazonSbAdKeyword amazonAdKeyword = adSbKeywordMap.get(e.getKeywordId());
                DashboardKeywordMatchTypeEnum dashboardKeywordMatchTypeEnum = DashboardKeywordMatchTypeEnum.dataMap.get(amazonAdKeyword.getMatchType().toUpperCase());
                if (dashboardKeywordMatchTypeEnum != null) {
                    builder.setMatchType(dashboardKeywordMatchTypeEnum.getDesc());
                }
                builder.setMatchTypeCode(amazonAdKeyword.getMatchType());
                setSbKeywordBuild(builder, sbAdGroupMap.get(amazonAdKeyword.getAdGroupId()),amazonAdKeyword);
                AmazonAdCampaignAll amazonAdCampaignAll = campaignAllMap.get(amazonAdKeyword.getCampaignId());
                if (amazonAdCampaignAll != null) {
                    Optional.ofNullable(amazonAdCampaignAll.getName()).ifPresent(builder::setCampaignName);
                    Optional.ofNullable(amazonAdCampaignAll.getCostType()).ifPresent(builder::setCostType);
                    Optional.ofNullable(amazonAdCampaignAll.getAdGoal())
                            .map(SBCampaignGoalEnum::getSBCampaignGoalEnumByType)
                            .filter(Objects::nonNull).map(SBCampaignGoalEnum::getCode).map(String::valueOf).ifPresent(builder::setGoal);
                }
            }

            ShopAuth shopAuth = shopAuthMap.get(e.getShopId());
            if (shopAuth != null) {
                Optional.ofNullable(shopAuth.getName()).ifPresent(builder::setShopName);
            }

            return builder.build();

        }).collect(Collectors.toList());

    }

    private void setSpKeywordBuild(KeywordListDataResponse.KeywordListDataRpcVo.Page.KeywordsPageRpcVo.Builder builder,
                                   AmazonAdGroup adGroup,
                                   AmazonAdKeyword adKeyword){
        builder.setAdGroupId(adKeyword.getAdGroupId());
        if (adGroup != null) {
            Optional.ofNullable(adGroup.getName()).ifPresent(builder::setAdGroupName);
            builder.setBid(adKeyword.getBid() == null ? adGroup.getDefaultBid().toString() : adKeyword.getBid().toString());
        }
        builder.setAdGroupId(adKeyword.getAdGroupId());
        Optional.ofNullable(adKeyword.getBid()).ifPresent(e->builder.setBid(e.toString()));
        builder.setCampaignId(adKeyword.getCampaignId());
        builder.setId(Int64Value.of(adKeyword.getId()));
        builder.setKeywordId(adKeyword.getKeywordId());
        builder.setKeywordText(adKeyword.getKeywordText());
        builder.setState(adKeyword.getState());
        builder.setKeywordId(adKeyword.getKeywordId());
        builder.setMatchType(adKeyword.getMatchType());
        Optional.ofNullable(adKeyword.getRangeEnd()).ifPresent(e->builder.setRangeEnd(e.toString()));
        Optional.ofNullable(adKeyword.getRangeStart()).ifPresent(e->builder.setRangeStart(e.toString()));
        builder.setShopId(Int32Value.of(adKeyword.getShopId()));
        builder.setState(adKeyword.getState());
        Optional.ofNullable(adKeyword.getMarketplaceId()).ifPresent(builder::setMarketplaceId);
    }

    private void setSbKeywordBuild(KeywordListDataResponse.KeywordListDataRpcVo.Page.KeywordsPageRpcVo.Builder builder,
                                   AmazonSbAdGroup adGroup,
                                   AmazonSbAdKeyword adKeyword){
        builder.setAdGroupId(adKeyword.getAdGroupId());
        if (adGroup != null) {
            Optional.ofNullable(adGroup.getName()).ifPresent(builder::setAdGroupName);
            Optional.ofNullable(adGroup.getAdFormat()).filter(StringUtils::isNotEmpty).ifPresent(builder::setAdFormat);
        }
        builder.setAdGroupId(adKeyword.getAdGroupId());
        Optional.ofNullable(adKeyword.getBid()).ifPresent(e->builder.setBid(e.toString()));
        builder.setCampaignId(adKeyword.getCampaignId());
        builder.setId(Int64Value.of(adKeyword.getId()));
        builder.setKeywordId(adKeyword.getKeywordId());
        builder.setKeywordText(adKeyword.getKeywordText());
        builder.setState(adKeyword.getState());
        builder.setKeywordId(adKeyword.getKeywordId());
        Optional.ofNullable(adKeyword.getRangeEnd()).ifPresent(e->builder.setRangeEnd(e.toString()));
        Optional.ofNullable(adKeyword.getRangeStart()).ifPresent(e->builder.setRangeStart(e.toString()));
        builder.setShopId(Int32Value.of(adKeyword.getShopId()));
        builder.setState(adKeyword.getState());
        builder.setMarketplaceId(adKeyword.getMarketplaceId());

    }


    @Override
    public Result updateKeyword(DashboardAdUpdateKeywordRequestVo requestVo) {
        if (CampaignTypeEnum.sp.getCampaignType().equalsIgnoreCase(requestVo.getAdType())) {
            return updateSpKeyword(requestVo);
        } else if (CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(requestVo.getAdType())) {
            return updateSbKeyword(requestVo);
        }
        return ResultUtil.error("数据错误");
    }


    private Result updateSpKeyword(DashboardAdUpdateKeywordRequestVo requestVo) {

        //处理业务
        Result res = null;
        DashboardUpdateKeywordTypeEnum dashboardUpdateKeywordTypeEnumByCode = DashboardUpdateKeywordTypeEnum.getDashboardUpdateKeywordTypeEnumByCode(requestVo.getUpdateType());
        if (DashboardUpdateKeywordTypeEnum.UPDATE_BID_TYPE == dashboardUpdateKeywordTypeEnumByCode) {
            res = cpcKeywordsService.updateBid(requestVo.getPuid(), requestVo.getUid(),
                    requestVo.getId(), requestVo.getBid(), requestVo.getIp());
        } else if (DashboardUpdateKeywordTypeEnum.UPDATE_STATUS_TYPE == dashboardUpdateKeywordTypeEnumByCode) {
            res = cpcKeywordsService.updateState(requestVo.getPuid(), requestVo.getUid(),
                    requestVo.getId(), requestVo.getStatus(), requestVo.getIp());
        } else if (DashboardUpdateKeywordTypeEnum.UPDATE_ARCHIVE_TYPE == dashboardUpdateKeywordTypeEnumByCode) {
            res = cpcKeywordsService.archive(requestVo.getPuid(), requestVo.getUid(), requestVo.getId(), requestVo.getIp());
        }
        return res;

    }


    private Result updateSbKeyword(DashboardAdUpdateKeywordRequestVo requestVo) {

        //处理业务
        Result res = null;
        DashboardUpdateKeywordTypeEnum dashboardUpdateKeywordTypeEnumByCode = DashboardUpdateKeywordTypeEnum.getDashboardUpdateKeywordTypeEnumByCode(requestVo.getUpdateType());
        if (DashboardUpdateKeywordTypeEnum.UPDATE_BID_TYPE == dashboardUpdateKeywordTypeEnumByCode) {
             res = cpcSbKeywordService.update(requestVo.getPuid(), requestVo.getShopId(),
                    requestVo.getUid(), requestVo.getId(),null, requestVo.getBid(), requestVo.getIp());
        } else if (DashboardUpdateKeywordTypeEnum.UPDATE_STATUS_TYPE == dashboardUpdateKeywordTypeEnumByCode) {
            res = cpcSbKeywordService.update(requestVo.getPuid(), requestVo.getShopId(),
                    requestVo.getUid(), requestVo.getId(),requestVo.getStatus(), null, requestVo.getIp());
        } else if (DashboardUpdateKeywordTypeEnum.UPDATE_ARCHIVE_TYPE == dashboardUpdateKeywordTypeEnumByCode) {
            res = cpcSbKeywordService.archive(requestVo.getPuid(),requestVo.getShopId(), requestVo.getUid(),  requestVo.getId(), requestVo.getIp());
        }
        return res;
    }
}
