package com.meiyunji.sponsored.service.multiple.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 活动报告环比指标数据
 *
 * @author: zzh
 * @create: 2024-11-21 10:05
 */
@Data
public class CommonCompareReport extends CommonReport {

    /**
     * 环比曝光量
     */
    private Long compareImpressions;

    /**
     * 环比点击量
     */
    private Long compareClicks;

    /**
     * 环比点击率（CTR）
     */
    private String compareCtr;

    /**
     * 环比订单转化率
     */
    private String compareCvr;

    /**
     * 环比ACoS
     */
    private String compareAcos;

    /**
     * 环比ROAS
     */
    private String compareRoas;

    /**
     * 环比ACoTS
     */
    private String compareAcots;

    /**
     * 环比ASoTS
     */
    private String compareAsots;

    /**
     * 环比广告订单数
     */
    private Long compareAdOrderNum;

    /**
     * 环比广告花费
     */
    private String compareAdCost;

    /**
     * 环比平均点击费
     */
    private String compareAdCostPerClick;

    /**
     * 环比广告销售额
     */
    private String compareAdSale;

    /**
     * 环比可见广告展示总数
     */
    private String compareViewImpressions;

    /**
     * 环比每笔订单花费
     */
    private String compareCpa;

    /**
     * 环比每千次展现费用
     */
    private String compareVcpm;

    /**
     * 环比本广告产品订单量
     */
    private Long compareAdSaleNum;

    /**
     * 环比其他产品广告订单量
     */
    private Long compareAdOtherOrderNum;

    /**
     * 环比本广告产品销售额
     */
    private String compareAdSales;

    /**
     * 环比其他产品广告销售额
     */
    private String compareAdOtherSales;

    /**
     * 环比广告销量
     */
    private Long compareOrderNum;

    /**
     * 环比本广告产品销量
     */
    private Long compareAdSelfSaleNum;

    /**
     * 环比其他产品广告销量
     */
    private Long compareAdOtherSaleNum;

    /**
     * 环比“品牌新买家”订单量
     */
    private Long compareOrdersNewToBrandFTD;

    /**
     * 环比“品牌新买家”订单百分比
     */
    private String compareOrderRateNewToBrandFTD;

    /**
     * 环比“品牌新买家”销售额
     */
    private String compareSalesNewToBrandFTD;

    /**
     * 环比“品牌新买家”销售额占比
     */
    private String compareSalesRateNewToBrandFTD;

    /**
     * 环比“品牌新买家”订单转化率
     */
    private String compareOrdersNewToBrandPercentageFTD;

    /**
     * 环比“品牌新买家”销量
     */
    private Long compareUnitsOrderedNewToBrandFTD;

    /**
     * 环比“品牌新买家”销量百分比
     */
    private String compareUnitsOrderedRateNewToBrandFTD;

    /**
     * 环比“品牌新买家”观看量
     */
    private String compareNewToBrandDetailPageViews;

    /**
     * 环比广告花费占比
     */
    private String compareAdCostPercentage;

    /**
     * 环比广告销售额占比
     */
    private String compareAdSalePercentage;

    /**
     * 环比广告订单量占比
     */
    private String compareAdOrderNumPercentage;

    /**
     * 环比广告销量占比
     */
    private String compareOrderNumPercentage;

    /**
     * 环比加购次数
     */
    private String compareAddToCart;

    /**
     * 环比加购率
     */
    private String compareAddToCartRate;

    /**
     * 环比单次加购花费
     */
    private String compareECPAddToCart;

    /**
     * 环比5秒观看次数
     */
    private String compareVideo5SecondViews;

    /**
     * 环比5秒观看率
     */
    private String compareVideo5SecondViewRate;

    /**
     * 环比视频播至1/4次数
     */
    private String compareVideoFirstQuartileViews;

    /**
     * 环比视频播至1/2次数
     */
    private String compareVideoMidpointViews;

    /**
     * 环比视频播至3/4次数
     */
    private String compareVideoThirdQuartileViews;

    /**
     * 环比视频完整播放次数
     */
    private String compareVideoCompleteViews;

    /**
     * 环比视频取消静音
     */
    private String compareVideoUnmutes;

    /**
     * 环比可见展示次数
     */
    private String compareViewableImpressions;

    /**
     * 环比观看率
     */
    private String compareViewabilityRate;

    /**
     * 环比观看点击率
     */
    private String compareViewClickThroughRate;

    /**
     * 环比品牌搜索次数
     */
    private String compareBrandedSearches;

    /**
     * 环比平均触达次数
     */
    private String compareImpressionsFrequencyAverage;

    /**
     * 环比广告笔单价
     */
    private String compareAdvertisingUnitPrice;

    /**
     * 环比DPV
     */
    private String compareDetailPageViews;
}
