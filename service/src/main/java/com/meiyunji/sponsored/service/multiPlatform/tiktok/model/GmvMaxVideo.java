package com.meiyunji.sponsored.service.multiPlatform.tiktok.model;

import lombok.Data;

@Data
public class GmvMaxVideo {

    private String videoId;
    private String videoCoverUrl;
    private String previewUrl;
    private Integer height;
    private Integer width;
    private Integer duration;
    private Long size;
    private String format;

    public static GmvMaxVideo fromTkGmvMaxVideo(com.tiktok.advertising.model.gmv_max.Video video) {
        GmvMaxVideo result = new GmvMaxVideo();
        result.setVideoId(video.getVideoId());
        result.setVideoCoverUrl(video.getVideoCoverUrl());
        result.setPreviewUrl(video.getPreviewUrl());
        result.setHeight(video.getHeight());
        result.setWidth(video.getWidth());
        result.setDuration(video.getDuration());
        result.setSize(video.getSize());
        result.setFormat(video.getFormat());
        return result;
    }

    public static com.tiktok.advertising.model.gmv_max.Video toTkGmvMaxVideo(GmvMaxVideo video) {
        com.tiktok.advertising.model.gmv_max.Video result = new com.tiktok.advertising.model.gmv_max.Video();
        result.setVideoId(video.getVideoId());
//        result.setVideoCoverUrl(video.getVideoCoverUrl());
//        result.setPreviewUrl(video.getPreviewUrl());
//        result.setHeight(video.getHeight());
//        result.setWidth(video.getWidth());
//        result.setDuration(video.getDuration());
//        result.setSize(video.getSize());
//        result.setFormat(video.getFormat());
        return result;
    }

}
