package com.meiyunji.sponsored.service.syncTask;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdCampaignAllService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbCampaignApiService;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdCampaignApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCampaignApiService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.enums.CampaignPlacementV3;
import com.meiyunji.sponsored.service.enums.CampaignStrategyV3;
import com.meiyunji.sponsored.service.stream.enums.AmazonStreamTaskTypeEnum;
import com.meiyunji.sponsored.service.stream.enums.StreamConstants;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamLogService;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamRedisCountService;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamTaskRetryService;
import com.meiyunji.sponsored.service.syncTask.entity.monitor.MonitorStreamTypeEnum;
import com.meiyunji.sponsored.service.syncTask.entity.monitor.MonitorTypeEnum;
import com.meiyunji.sponsored.service.syncTask.management.strgtegy.enume.AdProductEnum;
import com.meiyunji.sponsored.service.syncTask.message.ManagementCampaignStreamMessage;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ManagementCampaignConsumer {

    @Resource
    private IAmazonAdCampaignAllService campaignAllService;
    @Resource
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Resource
    private IScVcShopAuthDao shopAuthDao;
    @Resource
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Resource
    private CpcCampaignApiService cpcCampaignApiService;
    @Resource
    private CpcSbCampaignApiService cpcSbCampaignApiService;
    @Resource
    private CpcSdCampaignApiService cpcSdCampaignApiService;
    @Resource
    private IAmazonManagementStreamTaskRetryService amazonManagementStreamTaskRetryService;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;
    @Autowired
    private IAmazonManagementStreamLogService amazonManagementStreamLogService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private IAmazonManagementStreamRedisCountService amazonManagementStreamRedisCountService;


    private final LocalDateTime end = OffsetDateTime.parse("1969-12-31T23:59:59.999Z", DateTimeFormatter.ISO_OFFSET_DATE_TIME).toLocalDateTime();


    public void process(List<ManagementCampaignStreamMessage> messages) throws Exception {
        log.info("campaign stream data, {}", JSON.toJSONString(messages));
        Set<String> sellerIds = new HashSet<>();
        Set<String> marketplaceIds = new HashSet<>();
        Map<String, List<ManagementCampaignStreamMessage>> collect = new HashMap<>();
        if (CollectionUtils.isEmpty(messages)) {
            log.info("ad stream campaign message empty");
            return;
        }
        for (ManagementCampaignStreamMessage message : messages) {
            sellerIds.add(message.getAdvertiseId());
            marketplaceIds.add(message.getMarketplaceId());
            collect.computeIfAbsent(getKey(message), key -> new ArrayList<>()).add(message);
        }
        Date nowDate = new Date();
        amazonManagementStreamLogService.printAllManagementStreamCount(MonitorTypeEnum.all, MonitorStreamTypeEnum.ad_campaign, nowDate, messages.size());
        LocalDateTime localDateTime = LocalDateTime.now();
//        amazonManagementStreamRedisCountService.countAllAmazonManagementStreamHour(localDateTime, messages.size());
        List<ShopAuth> shopAuths = shopAuthDao.getBySellerIdsAndMarketplaceIds(Lists.newArrayList(sellerIds), Lists.newArrayList(marketplaceIds));
        if (CollectionUtils.isEmpty(shopAuths)) {
            log.info("campaign stream not fund shopAuths,sellerId:{},marketplaceId:{}", StringUtils.join(sellerIds, ","), StringUtils.join(marketplaceIds, ","));
            return;
        }


        Map<String, ShopAuth> shopAuthMap = shopAuths.stream().collect(Collectors.toMap(this::getKey, Function.identity()));
        for (Map.Entry<String, List<ManagementCampaignStreamMessage>> entry : collect.entrySet()) {
            ShopAuth shopAuth = shopAuthMap.get(entry.getKey());
            if (shopAuth == null) {
                continue;
            }
            List<ManagementCampaignStreamMessage> managementAdGroupStreamMessages = entry.getValue();
            amazonManagementStreamLogService.printPuidManagementStreamCount(shopAuth.getPuid(), shopAuth.getId(), MonitorTypeEnum.puid, MonitorStreamTypeEnum.ad_campaign, nowDate, managementAdGroupStreamMessages.size());

            Map<String, List<ManagementCampaignStreamMessage>> stringListMap = managementAdGroupStreamMessages.stream()
                    .collect(Collectors.groupingBy(ManagementCampaignStreamMessage::getAdProduct));
            //缺失字段比较多，广告活动层级数据量比较小，先尝试直接使同步的方式取回来
            //即使入库了也如需同步一遍活动服务状态，保持一直，可以考虑用异步；
            //查询profile和店铺逻辑可以考虑加缓存提供公共服务
            for (Map.Entry<String, List<ManagementCampaignStreamMessage>> e : stringListMap.entrySet()) {
                String k = e.getKey();
                List<ManagementCampaignStreamMessage> v = e.getValue();
                AdProductEnum adProductEnum = AdProductEnum.getAdProductEnum(k);
                if (adProductEnum == null) {
                    continue;
                }
                if (AdProductEnum.SPONSORED_PRODUCTS == adProductEnum) {
                    List<List<String>> partition = Lists.partition(v.stream().map(ManagementCampaignStreamMessage::getCampaignId).distinct().collect(Collectors.toList()), StreamConstants.SP_MAX_CAMPAIGN_IDS_COUNT);
                    for (List<String> ids : partition) {
                        try {
                            cpcCampaignApiService.syncCampaigns(shopAuth, StringUtils.join(ids, ","), false, true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
                            amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(localDateTime, ids.size());
                        } catch (Exception exception) {
                            amazonManagementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), adProductEnum, AmazonStreamTaskTypeEnum.CAMPAIGN, ids);
                        }
                    }
                } else if (AdProductEnum.SPONSORED_BRANDS == adProductEnum) {
                    List<List<String>> partition = Lists.partition(v.stream().map(ManagementCampaignStreamMessage::getCampaignId).distinct().collect(Collectors.toList()), StreamConstants.SB_MAX_CAMPAIGN_IDS_COUNT);
                    for (List<String> ids : partition) {
                        try {
                            cpcSbCampaignApiService.syncCampaignsV4(shopAuth, StringUtils.join(ids, ","), false, true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
                            amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(localDateTime, ids.size());
                        } catch (Exception exception) {
                            amazonManagementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), adProductEnum, AmazonStreamTaskTypeEnum.CAMPAIGN, ids);
                        }
                    }

                } else if (AdProductEnum.SPONSORED_DISPLAY == adProductEnum) {
                    List<List<String>> partition = Lists.partition(v.stream().map(ManagementCampaignStreamMessage::getCampaignId).distinct().collect(Collectors.toList()), StreamConstants.SD_MAX_CAMPAIGN_IDS_COUNT);
                    for (List<String> ids : partition) {
                        try {
                            cpcSdCampaignApiService.syncCampaigns(shopAuth, StringUtils.join(ids, ","), null, false, true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
                            amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(localDateTime, ids.size());
                        } catch (Exception exception) {
                            amazonManagementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), adProductEnum, AmazonStreamTaskTypeEnum.CAMPAIGN, ids);
                        }
                    }
                }
            }

        }


    }


    private String getKey(ManagementCampaignStreamMessage campaignStreamMessage) {
        return campaignStreamMessage.getAdvertiseId() + "#" + campaignStreamMessage.getMarketplaceId();
    }

    private String getKey(ShopAuth shopAuth) {
        return shopAuth.getSellingPartnerId() + "#" + shopAuth.getMarketplaceId();
    }

    private AmazonAdCampaignAll turnSpCampaignToAllPO(ManagementCampaignStreamMessage campaignStreamMessage) {
        AmazonAdCampaignAll amazonAdCampaign = new AmazonAdCampaignAll();
        amazonAdCampaign.setType(Constants.SP);
        //兼容v2版本数据,targetingType存储时转成小写
        amazonAdCampaign.setAdTargetType(campaignStreamMessage.getTargetingSettings().toLowerCase());
        amazonAdCampaign.setTargetingType(campaignStreamMessage.getTargetingSettings().toLowerCase());

        return amazonAdCampaign;
    }

    /**
     * 1.有些字段Stream是有缺失的，如果没有落库到本地，直接传入campaignId调用接口进行同步处理
     */
    private List<String> getCampaignList(int puid, int shopId, List<String> campaignIds) {
        return amazonAdCampaignAllDao.getPortfolioListByCampaignIds(puid, shopId, campaignIds);
    }

}
