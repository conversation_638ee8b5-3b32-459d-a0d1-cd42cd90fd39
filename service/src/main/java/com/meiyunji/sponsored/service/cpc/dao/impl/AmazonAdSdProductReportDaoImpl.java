package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSdProductReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSdProductReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdProduct;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.doris.util.DorisJSONUtil;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by lm on 2021/5/14.
 */
@Repository
@Slf4j
public class AmazonAdSdProductReportDaoImpl extends BaseShardingDaoImpl<AmazonAdSdProductReport> implements IAmazonAdSdProductReportDao {

    @Resource(name = "shardingJdbcMap")
    private Map<String, JdbcTemplate> jdbcTemplateMap;

    @Autowired
    private DynamicRefreshNacosConfiguration nacosConfiguration;

    @Autowired
    private IDorisService dorisService;

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Autowired
    private IOdsProductDao odsProductDao;

    @Override
    public void insertOrUpdateList(Integer puid, List<AmazonAdSdProductReport> list) {
        //插入原表
        insertOrUpdateListOriginAndHotTable(puid, list, getJdbcHelper().getTable());

        //写入开关开启且数据是95天内的，就插入热表
        if (nacosConfiguration.isHotTableWritePhase2Enable()) {
            //筛选出95天内的数据
            List<AmazonAdSdProductReport> hotList = list.stream()
                .filter(k -> (StringUtils.isNotBlank(k.getCountDate())))
                .filter(k -> DateUtil.getDayBetween(DateUtil.strToDate(k.getCountDate(), DateUtil.PATTERN_YYYYMMDD), new Date()) <= com.meiyunji.sponsored.common.base.Constants.HOT_SAVE_DAYS)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hotList)) {
                //插入热表
                insertOrUpdateListOriginAndHotTable(puid, hotList, getHotTableName());
            }
        }

    }

    @Override
    public void insertDorisList(Integer puid, List<AmazonAdSdProductReport> list) {
        if (!dynamicRefreshConfiguration.verifyGroupAndAdReport(puid)){
            return;
        }
        try {
            String time = LocalDateTimeUtil.formatTime(LocalDateTime.now(), LocalDateTimeUtil.YMDHMS_DATE_FORMAT);
            List<Map<String, Object>> map = list.stream().map(k -> {
                Map<String, Object> objectMap = DorisJSONUtil.dbObj2FieldMap(k);
                LocalDateTimeUtil.setDorisValue(objectMap, k.getCountDate(), time);
                return objectMap;
            }).collect(Collectors.toList());
            dorisService.saveDorisMapByRoutineLoad("doris_ods_t_amazon_ad_sd_product_report", map);
        } catch (Exception e) {
            log.error("save doris kafka error = {}", e.getMessage());
        }
    }

    private void insertOrUpdateListOriginAndHotTable(Integer puid, List<AmazonAdSdProductReport> list, String tableName) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(tableName);
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`count_date`,`tactic_type`,`campaign_name`,`campaign_id`,")
                .append("`ad_group_name`,`ad_group_id`,`asin`,`parent_asin`,`sku`,`ad_id`,")
                .append("`impressions`,`clicks`,`cost`,`currency`,`conversions1d`,`conversions7d`,`conversions14d`,`conversions30d`,`conversions1d_same_sku`,`conversions7d_same_sku`,")
                .append("`conversions14d_same_sku`,`conversions30d_same_sku`,`units_ordered1d`,`units_ordered7d`,`units_ordered14d`,`units_ordered30d`,`sales1d`,`sales7d`,`sales14d`,`sales30d`,")
                .append("`sales1d_same_sku`,`sales7d_same_sku`,`sales14d_same_sku`,`sales30d_same_sku`,`orders_new_to_brand14d`,`sales_new_to_brand14d`,`units_ordered_new_to_brand14d`,`detail_page_view14d`,`view_impressions`,`cost_type`,`bid_optimization`,")
                .append("`new_to_brand_detail_page_views`,`add_to_cart`,`add_to_cart_rate`,`e_cp_add_to_cart`,`video_first_quartile_views`,")
                .append("`video_Midpoint_Views`,`video_third_quartile_views`,`video_complete_views`,`video_unmutes`,`vtr`,")
                .append("`vctr`,`branded_searches14d`,`cumulative_reach`,`impressions_frequency_average`,")
                .append("`create_time`,`update_time`) values ");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdSdProductReport report : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getCountDate());
            argsList.add(report.getTacticType());
            argsList.add(report.getCampaignName());
            argsList.add(report.getCampaignId());
            argsList.add(report.getAdGroupName());
            argsList.add(report.getAdGroupId());
            argsList.add(report.getAsin());
            argsList.add(report.getParentAsin());
            argsList.add(report.getSku());
            argsList.add(report.getAdId());
            argsList.add(report.getImpressions());
            argsList.add(report.getClicks());
            argsList.add(report.getCost());
            argsList.add(report.getCurrency());
            argsList.add(report.getConversions1d());
            argsList.add(report.getConversions7d());
            argsList.add(report.getConversions14d());
            argsList.add(report.getConversions30d());
            argsList.add(report.getConversions1dSameSKU());
            argsList.add(report.getConversions7dSameSKU());
            argsList.add(report.getConversions14dSameSKU());
            argsList.add(report.getConversions30dSameSKU());
            argsList.add(report.getUnitsOrdered1d());
            argsList.add(report.getUnitsOrdered7d());
            argsList.add(report.getUnitsOrdered14d());
            argsList.add(report.getUnitsOrdered30d());
            argsList.add(report.getSales1d());
            argsList.add(report.getSales7d());
            argsList.add(report.getSales14d());
            argsList.add(report.getSales30d());
            argsList.add(report.getSales1dSameSKU());
            argsList.add(report.getSales7dSameSKU());
            argsList.add(report.getSales14dSameSKU());
            argsList.add(report.getSales30dSameSKU());
            argsList.add(report.getOrdersNewToBrand14d());
            argsList.add(report.getSalesNewToBrand14d());
            argsList.add(report.getUnitsOrderedNewToBrand14d());
            argsList.add(report.getDetailPageView14d());
            argsList.add(report.getViewImpressions());
            argsList.add(report.getCostType());
            argsList.add(report.getBidOptimization());
            argsList.add(report.getNewToBrandDetailPageViews());
            argsList.add(report.getAddToCart());
            argsList.add(report.getAddToCartRate());
            argsList.add(report.getECPAddToCart());
            argsList.add(report.getVideoFirstQuartileViews());
            argsList.add(report.getVideoMidpointViews());
            argsList.add(report.getVideoThirdQuartileViews());
            argsList.add(report.getVideoCompleteViews());
            argsList.add(report.getVideoUnmutes());
            argsList.add(report.getViewabilityRate());
            argsList.add(report.getViewClickThroughRate());
            argsList.add(report.getBrandedSearches());
            argsList.add(report.getCumulativeReach());
            argsList.add(report.getImpressionsFrequencyAverage());
        }

        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `campaign_name`=values(campaign_name),`ad_group_name`=values(ad_group_name),`asin`=values(asin),`sku`=values(sku),`impressions`=values(impressions),`clicks`=values(clicks),`cost`=values(cost),`currency`=values(currency),");
        sql.append("`conversions1d`=values(conversions1d),`conversions7d`=values(conversions7d),`conversions14d`=values(conversions14d),`conversions30d`=values(conversions30d),`conversions1d_same_sku`=values(conversions1d_same_sku),`conversions7d_same_sku`=values(conversions7d_same_sku),");
        sql.append("`conversions14d_same_sku`=values(conversions14d_same_sku),`conversions30d_same_sku`=values(conversions30d_same_sku),`units_ordered1d`=values(units_ordered1d),`units_ordered7d`=values(units_ordered7d),`units_ordered14d`=values(units_ordered14d),`units_ordered30d`=values(units_ordered30d),");
        sql.append("`sales1d`=values(sales1d),`sales7d`=values(sales7d),`sales14d`=values(sales14d),`sales30d`=values(sales30d),`sales1d_same_sku`=values(sales1d_same_sku),`sales7d_same_sku`=values(sales7d_same_sku),");
        sql.append("`sales14d_same_sku`=values(sales14d_same_sku),`sales30d_same_sku`=values(sales30d_same_sku),`orders_new_to_brand14d`=values(orders_new_to_brand14d),`sales_new_to_brand14d`=values(sales_new_to_brand14d),`units_ordered_new_to_brand14d`=values(units_ordered_new_to_brand14d),`detail_page_view14d`=values(detail_page_view14d),`view_impressions`=values(view_impressions),`cost_type`=values(cost_type),`bid_optimization`=values(bid_optimization),");
        sql.append("`new_to_brand_detail_page_views`=values(new_to_brand_detail_page_views),`add_to_cart`=values(add_to_cart),`add_to_cart_rate`=values(add_to_cart_rate),`e_cp_add_to_cart`=values(e_cp_add_to_cart),`video_first_quartile_views`=values(video_first_quartile_views),");
        sql.append("`video_Midpoint_Views`=values(video_Midpoint_Views),`video_third_quartile_views`=values(video_third_quartile_views),`video_complete_views`=values(video_complete_views),`video_unmutes`=values(video_unmutes),`vtr`=values(vtr),");
        sql.append("`vctr`=values(vctr),`branded_searches14d`=values(branded_searches14d),`cumulative_reach`=values(cumulative_reach),`impressions_frequency_average`=values(impressions_frequency_average)");
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public Page getPageList(Integer puid, SearchVo search, Page page) {
        String tableName = getTableNameByStartDate(search.getStart());
        StringBuilder selectSql = new StringBuilder("select count_date,shop_id,marketplace_id,campaign_name,campaign_id,ad_id,cost_type,bid_optimization," +
                "ad_group_name,ad_group_id,asin,parent_asin,sku,sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`detail_page_view14d`) detail_page_view14d,sum(`view_impressions`) view_impressions," +
                "sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`conversions14d`) conversions14d,sum(`units_ordered14d`) units_ordered14d,")
                .append("sum(`conversions14d_same_sku`) conversions14d_same_sku,sum(`sales14d_same_sku`) sales14d_same_sku,")
                .append("sum(`orders_new_to_brand14d`) orders_new_to_brand14d,sum(`sales_new_to_brand14d`) sales_new_to_brand14d,sum(`units_ordered_new_to_brand14d`) units_ordered_new_to_brand14d")
                .append(" from ").append(tableName).append(" ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( select ad_id FROM ").append(tableName).append(" ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        if (search.getShopId() != null) {
            whereSql.append("and shop_id=? ");
            argsList.add(search.getShopId());
        } else if (CollectionUtils.isNotEmpty(search.getShopIds())) {
            whereSql.append("and shop_id in ('").append(StringUtils.join(search.getShopIds(), "','")).append("') ");
        }
        whereSql.append("and count_date>=? and count_date<=? ");
        argsList.add(DateUtil.dateToStrWithFormat(search.getStart(), "yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(search.getEnd(), "yyyyMMdd"));
        if (StringUtils.isNotBlank(search.getCampaignId())) {
            whereSql.append(" and campaign_id = ?");
            argsList.add(search.getCampaignId());
        }
        if (StringUtils.isNotBlank(search.getGroupId())) {
            whereSql.append(" and ad_group_id= ?");
            argsList.add(search.getGroupId());
        }
        if (StringUtils.isNotBlank(search.getSearchValue())) {  //搜索查询
            search.setSearchValue(SqlStringUtil.dealLikeSql(search.getSearchValue()));
            if ("sku".equals(search.getSearchType())) {
                whereSql.append(" and sku like ?");
                argsList.add(search.getSearchValue() + "%");
            } else if ("asin".equals(search.getSearchType())) {
                whereSql.append(" and asin like ?");
                argsList.add(search.getSearchValue() + "%");
            } else if ("parentAsin".equals(search.getSearchType())) {
                whereSql.append(" and parent_asin like ?");
                argsList.add(search.getSearchValue() + "%");
            }
        }
        whereSql.append(" group by shop_id,`campaign_id`,ad_group_id,cost_type,`asin`");
        if ("daily".equals(search.getTabType())) {
            whereSql.append(", count_date");
        }
        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");
        if (StringUtils.isNotBlank(search.getOrderField()) && StringUtils.isNotBlank(search.getOrderValue())) {  //排序
            String field = ReportService.getSdReportField(search.getOrderField(), true);
            if (StringUtils.isNotBlank(field)) {
                selectSql.append(" order by ").append(field);
                if ("desc".equals(search.getOrderValue())) {
                    selectSql.append("desc");
                }
                selectSql.append(" ,ad_id ");   //增加一个排序字段
                if ("desc".equals(search.getOrderValue())) {
                    selectSql.append("desc");
                }
            }
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdSdProductReport.class);
    }

    @Override
    public Page detailPageList(Integer puid, Integer shopId, String marketplaceId, ReportParam param, Page page) {
        String tableName = getTableNameByStartDate(param.getStart());
        StringBuilder selectSql = new StringBuilder("select shop_id,marketplace_id,campaign_name,ad_group_name," +
                "count_date,asin,parent_asin,sku,sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`impressions`) impressions,sum(`units_ordered14d`) units_ordered14d," +
                "sum(`clicks`) clicks,sum(`conversions14d`) conversions14d")
                .append(" from ").append(tableName).append(" ");
        StringBuilder countSql = new StringBuilder("select count(*)").append(" from ( select count_date FROM ").append(tableName).append(" ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id= ? and campaign_id=? and ad_group_id=? ");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(param.getCampaignId());
        argsList.add(param.getGroupId());
        if (Constants.PRODUCT_TYPE_ASIN.equals(param.getTabType())) {
            whereSql.append(" and asin=? ");
        } else if (Constants.PRODUCT_TYPE_SKU.equals(param.getTabType())) {
            whereSql.append(" and sku = ? ");
        }
        argsList.add(param.getTabId());
        whereSql.append(" and count_date>=? and count_date<=? ");
        argsList.add(DateUtil.dateToStrWithFormat(param.getStart(), "yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(param.getEnd(), "yyyyMMdd"));
        whereSql.append(" group by count_date ");
        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");
        if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderValue())) {
            String field = ReportService.getSdReportField(param.getOrderField(), false);
            if (StringUtils.isNotBlank(field)) {
                selectSql.append(" order by ").append(field);
                if ("desc".equals(param.getOrderValue())) {
                    selectSql.append("desc");
                }
            }
        } else {
            selectSql.append(" order by count_date desc");
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdSdProductReport.class);
    }

    @Override
    public AmazonAdSdProductReport getSumReport(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, ReportParam param) {
        StringBuilder sql = new StringBuilder("SELECT sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d, sum(`units_ordered14d`) units_ordered14d,")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks ")
                .append(" FROM ")
                .append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)))
                .append(" where`puid`=? and`shop_id`=?")
                .append("  and`marketplace_id`=? and campaign_id=? and ad_group_id=? and `count_date`>=? and count_date<=?  ");
        if (Constants.PRODUCT_TYPE_ASIN.equals(param.getTabType())) {
            sql.append(" and asin=? ");
        } else if (Constants.PRODUCT_TYPE_SKU.equals(param.getTabType())) {
            sql.append(" and sku = ? ");
        }
        List<AmazonAdSdProductReport> list = getJdbcTemplate(puid).query(sql.toString(), new Object[]{puid, shopId, marketplaceId, param.getCampaignId(), param.getGroupId(), startStr, endStr, param.getTabId()}, getMapper());
        return list != null && list.size() > 0 ? list.get(0) : null;
    }

    @Override
    public List<AmazonAdSdProductReport> getChartList(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, ReportParam param) {
        String sql = "select * from " + getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)) + " where puid=? and shop_id=? and marketplace_id=? and campaign_id=? and ad_group_id=? and count_date>=? and count_date<=?  ";
        if (Constants.PRODUCT_TYPE_ASIN.equals(param.getTabType())) {
            sql += " and asin=? ";
        } else if (Constants.PRODUCT_TYPE_SKU.equals(param.getTabType())) {
            sql += " and sku = ? ";
        }
        sql += " order by count_date";
        return getJdbcTemplate(puid).query(sql, new Object[]{puid, shopId, marketplaceId, param.getCampaignId(), param.getGroupId(), startStr, endStr, param.getTabId()}, getMapper());
    }

    @Override
    public List<Map<String, Object>> getCampaignOrAdGroupNames(int puid, Integer shopId, String marketplaceId, String campaignId) {
        String sql;
        List<Object> arg = Lists.newArrayList();
        arg.add(puid);
        arg.add(shopId);
        arg.add(marketplaceId);
        if (StringUtils.isBlank(campaignId)) {
            sql = "select campaign_id id,campaign_name `name` from t_amazon_ad_sd_product_report where puid=? and shop_id=? and marketplace_id=? GROUP BY campaign_id";
        } else {
            sql = "select ad_group_id id,ad_group_name `name` from t_amazon_ad_sd_product_report where puid=? and shop_id=? and marketplace_id=? and campaign_id=? GROUP BY ad_group_id";
            arg.add(campaignId);
        }
        return getJdbcTemplate(puid).queryForList(sql, arg.toArray());
    }

    @Override
    public List<AmazonAdSdProductReport> getTaskSumReport(Integer puid, Integer shopId, String marketplaceId, String startDate, String endDate) {
        String sql = "select sku, currency, sum(`cost`) cost, sum(`sales7d`) sales7d, sum(`units_ordered7d`) units_ordered7d, sum(`sales14d`) sales14d," +
                " sum(`units_ordered14d`) units_ordered14d, sum(`conversions14d`) conversions14d, sum(`conversions7d`) conversions7d FROM " +
                getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)) +
                " where puid=? and shop_id=? and marketplace_id=? and count_date >= ? and count_date < ? GROUP by sku";
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(shopId);
        args.add(marketplaceId);
        args.add(startDate);
        args.add(endDate);
        return getJdbcTemplate(puid).query(sql, args.toArray(), getMapper());
    }

    @Override
    public AmazonAdSdProductReport getSkuSumData(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String msku) {

        String sql = "SELECT shop_id,marketplace_id,campaign_name,campaign_id,ad_group_name,ad_group_id,asin,parent_asin,sku,IFNULL(sum(`cost`),0) cost, IFNULL(sum(`sales14d`),0) sales14d, " +
                " IFNULL(sum(`conversions14d`),0) conversions14d, currency ,sum(clicks) clicks, sum(impressions) impressions " +
                "  FROM " + getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)) + " WHERE puid = ? AND shop_id = ? AND marketplace_id = ? AND count_date>= ? AND count_date<= ? ";
        List<Object> argsList = Lists.newArrayList(puid, shopId, marketplaceId, startStr, endStr);
        if (StringUtils.isNotBlank(msku)) {
            sql += " AND sku = ? ";
            argsList.add(msku);
        }

        List<AmazonAdSdProductReport> query = getJdbcTemplate(puid).query(sql, argsList.toArray(), getMapper());
        return CollectionUtils.isNotEmpty(query) ? query.get(0) : null;
    }

    @Override
    public List<AmazonAdSdProductReport> getListSkuSumData(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, List<String> msku) {

        String sql = "SELECT shop_id,count_date,marketplace_id,campaign_name,campaign_id,ad_group_name,ad_group_id,asin,parent_asin,sku,IFNULL(sum(`cost`),0) cost, IFNULL(sum(`sales14d`),0) sales14d, " +
                " IFNULL(sum(`conversions14d`),0) conversions14d, currency ,sum(clicks) clicks, sum(impressions) impressions " +
                "  FROM " + getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)) + " WHERE puid = ? AND shop_id = ? AND marketplace_id = ? AND count_date>= ? AND count_date<= ? ";
        List<Object> argsList = Lists.newArrayList(puid, shopId, marketplaceId, startStr, endStr);
        if (CollectionUtils.isNotEmpty(msku)) {
            sql += SqlStringUtil.dealInList("sku",msku,argsList);

        }
        sql += " group by count_date,sku ";

        List<AmazonAdSdProductReport> query = getJdbcTemplate(puid).query(sql, argsList.toArray(), getMapper());
        return query;
    }

    @Override
    public List<AmazonAdSdProductReport> getListByDate(Integer puid, Integer shopId, String marketplaceId, String startTime, String endTime, String filed) {
        ConditionBuilder.Builder builder =  new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketplaceId)
                .greaterThanOrEqualTo(filed, startTime)
                .lessThanOrEqualTo(filed, endTime);

        return listByCondition(puid,builder.build());
    }

    @Override
    public List<AmazonAdSdProductReport> listSumReports(Integer puid, Integer shopId, String startDate, String endDate, List<String> adIds) {
        String sql = "SELECT puid, shop_id, campaign_id, ad_group_id, ad_id, sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`sales14d_same_sku`) sales14d_same_sku," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`units_ordered14d`) units_ordered14d, sum(`conversions14d`) conversions14d," +
                "sum(`conversions14d_same_sku`) conversions14d_same_sku " +
                " FROM " + getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate)
                .in("ad_id", adIds.toArray())
                .groupBy("ad_id")
                .build();

        sql += conditionBuilder.getSql();

        return getJdbcTemplate(puid).query(sql, getMapper(), conditionBuilder.getValues());
    }

    @Override
    public List<AmazonAdSdProductReport> listReports(Integer puid, Integer shopId, String startDate, String endDate, String adId) {
        String sql = "SELECT puid, shop_id, count_date, campaign_id, ad_group_id, ad_id, sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`sales14d_same_sku`) sales14d_same_sku," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`units_ordered14d`) units_ordered14d, sum(`conversions14d`) conversions14d," +
                "sum(`conversions14d_same_sku`) conversions14d_same_sku " +
                " FROM " + getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate);

        if (StringUtils.isNotBlank(adId)) {
            builder.equalToWithoutCheck("ad_id", adId);
        }

        builder.groupBy("count_date");
        ConditionBuilder conditionBuilder = builder.build();

        sql += conditionBuilder.getSql();

        return getJdbcTemplate(puid).query(sql, getMapper(), conditionBuilder.getValues());
    }


    @Override
    public List<AmazonAdSdProductReport> getListByParentAsinIsNull(Integer puid, Integer shopId, String sku, Date date) {
        List<AmazonAdSdProductReport> productList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select puid, shop_id from t_amazon_ad_sd_product_report where parent_asin is null  ");
        List<Object> args = new ArrayList<>();
        if (puid != null) {
            sql.append(" and puid=?");
            args.add(puid);
        }
        if(shopId != null){
            sql.append(" and shop_id=?");
            args.add(shopId);
        }
        if (StringUtils.isNotBlank(sku)) {
            sql.append(" and sku=?");
            args.add(sku);
        }
        sql.append(" and create_time > ? ");
        args.add(date);

        sql.append(" group by puid,shop_id ");
        for (Map.Entry<String, JdbcTemplate> entry : jdbcTemplateMap.entrySet()) {
            List<AmazonAdSdProductReport> products = entry.getValue().query(sql.toString(), getMapper(), args.toArray());
            if (CollectionUtils.isNotEmpty(products)) {
                productList.addAll(products);
            }
        }
        return productList;
    }

    @Override
    public List<AmazonAdSdProductReport> getProductListByParentAsinIsNull(Integer puid, Integer shopId, Date date) {
        String sql = "select * from t_amazon_ad_sd_product_report where puid=? and shop_id=? and  parent_asin is null and create_time > ?  ";
        return getJdbcTemplate(puid).query(sql, getMapper(), new Object[]{puid,shopId, date});
    }

    @Override
    public void batchUpdateParentAsin(Integer puid, Integer shopId, List<AmazonAdSdProductReport> newList) {
        batchUpdateParentAsinOrigin(puid, shopId, newList);
        if (nacosConfiguration.isHotTableWritePhase2Enable()) {
            batchUpdateParentAsinHot(puid, shopId, newList);
        }
    }

    private void batchUpdateParentAsinOrigin(Integer puid, Integer shopId, List<AmazonAdSdProductReport> newList) {
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_sd_product_report` set `parent_asin` = ? where puid= ? and shop_id= ? and id=?   ");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonAdSdProductReport product : newList) {
            batchArg = new Object[]{
                    product.getParentAsin(),
                    puid,
                    shopId,
                    product.getId()
            };
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate(sql.toString(), batchArgs);
    }

    private void batchUpdateParentAsinHot(Integer puid, Integer shopId, List<AmazonAdSdProductReport> newList) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getHotTableName());
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`count_date`,`tactic_type`,`ad_id`, `parent_asin`) values ");
        List<Object> batchArgs = Lists.newArrayList();

        for (AmazonAdSdProductReport report : newList) {
            sql.append(" (?, ?, ?, ?, ?, ?, ?),");
            batchArgs.add(puid);
            batchArgs.add(shopId);
            batchArgs.add(report.getMarketplaceId());
            batchArgs.add(report.getCountDate());
            batchArgs.add(report.getTacticType());
            batchArgs.add(report.getAdId());
            batchArgs.add(report.getParentAsin());
        }

        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `parent_asin`=values(parent_asin)");
        getJdbcTemplate(puid).update(sql.toString(), batchArgs.toArray());
    }

    @Override
    public List<AdHomePerformancedto> listSumReportByAdIds(Integer puid, Integer shopId, String startStr, String endStr, AdProductPageParam param, List<String> adIds) {
        if (CollectionUtils.isEmpty(adIds)) {
            return null;
        }
        List<Object> argsList = new ArrayList<>();

        StringBuilder sql = new StringBuilder(" select   'sd' as type, `ad_id`,sum(cost) `cost`, sum(impressions) `impressions`, sum(clicks) `clicks`, ");
        sql.append(" sum(sales14d) total_sales,sum(conversions14d) order_num,sum(view_impressions) `view_impressions`,sum(conversions14d_same_sku) `conversions14d_same_sku`,");
        sql.append(" sum(sales14d_same_sku) `sales14d_same_sku`,sum(units_ordered14d) `units_ordered14d`,sum(orders_new_to_brand14d) `orders_new_to_brand14d`,");
        sql.append(" sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(units_ordered_new_to_brand14d) `units_ordered_new_to_brand14d`,");
        sql.append(" sum(`new_to_brand_detail_page_views`) AS `new_to_brand_detail_page_views`,");
        sql.append(" sum(`add_to_cart`) AS `add_to_cart`,");
        sql.append(" sum(`video_first_quartile_views`) AS `video_first_quartile_views`,");
        sql.append(" sum(`video_Midpoint_Views`) AS `video_Midpoint_Views`,");
        sql.append(" sum(`video_third_quartile_views`) AS `video_third_quartile_views`,");
        sql.append(" sum(`video_complete_views`) AS `video_complete_views`,");
        sql.append(" sum(`video_unmutes`) AS `video_unmutes`,");
        sql.append(" sum(`branded_searches14d`) AS `branded_searches14d`,");
        sql.append(" sum(`detail_page_view14d`) AS `detail_page_view14d`");
        sql.append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }
        whereSql.append(SqlStringUtil.dealInList("ad_id", adIds, argsList));

        whereSql.append("  and count_date >= ? and count_date <= ? group by ad_id ");
        argsList.add(startStr);
        argsList.add(endStr);

        sql.append(whereSql);

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
            @Override
            public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                AdHomePerformancedto dto = AdHomePerformancedto.builder()
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        /**
                         * TODO 广告报告重构
                         * CPC,VCPM广告订单量
                         */
                        .adOrderNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))
                        //CPC,VCPM广告销售额
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .adId(re.getString("ad_id"))
                        .type(re.getString("type"))
                        //可见展示次数(VCPM专用)
                        .viewImpressions(Optional.ofNullable(re.getInt("view_impressions")).orElse(0))
                        //本广告产品订单量
                        .adSaleNum(Optional.ofNullable(re.getInt("conversions14d_same_sku")).orElse(0))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(re.getBigDecimal("sales14d_same_sku")).orElse(BigDecimal.ZERO))
                        //CPC,VCPM广告销量
                        .salesNum(Optional.ofNullable(re.getInt("units_ordered14d")).orElse(0))
                        //CPC,VCPM-“品牌新买家”订单量
                        .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                        //CPC,VCPM-“品牌新买家”销售额
                        .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                        //CPC,VCPM-“品牌新买家”销量
                        .unitsOrderedNewToBrand14d(Optional.ofNullable(re.getInt("units_ordered_new_to_brand14d")).orElse(0))
                        .newToBrandDetailPageViews(Optional.ofNullable(re.getInt("new_to_brand_detail_page_views")).orElse(0))
                        .addToCart(Optional.ofNullable(re.getInt("add_to_cart")).orElse(0))
                        .videoFirstQuartileViews(Optional.ofNullable(re.getInt("video_first_quartile_views")).orElse(0))
                        .videoMidpointViews(Optional.ofNullable(re.getInt("video_Midpoint_Views")).orElse(0))
                        .videoThirdQuartileViews(Optional.ofNullable(re.getInt("video_third_quartile_views")).orElse(0))
                        .videoCompleteViews(Optional.ofNullable(re.getInt("video_complete_views")).orElse(0))
                        .videoUnmutes(Optional.ofNullable(re.getInt("video_unmutes")).orElse(0))
                        .brandedSearches(Optional.ofNullable(re.getInt("branded_searches14d")).orElse(0))
                        .detailPageViews(Optional.ofNullable(re.getInt("detail_page_view14d")).orElse(0))
                        .build();
                return dto;
            }
        }, argsList.toArray());
    }

    @Override
    public List<AdHomePerformancedto> listLatestReports(Integer puid, Integer shopId, List<String> adIds, boolean isAggregation) {
        if (CollectionUtils.isEmpty(adIds)) {
            return Collections.emptyList();
        }

        StringBuilder sql = new StringBuilder();
        sql.append(" select 'sd' as type, `ad_id`")
                .append(", max(`cumulative_reach`) AS `cumulative_reach`")
                .append(", max(`impressions_frequency_average`) AS `impressions_frequency_average`")
                .append(" FROM ")
                .append(getTableNameByStartDate(new Date()))
                .append(" where ");

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("ad_id", adIds.toArray())
                .groupBy("ad_id")
                .build();

        sql.append(conditionBuilder.getSql());

        return getJdbcTemplate(puid).query(sql.toString(), (re, i) -> AdHomePerformancedto.builder()
                        .type(re.getString("type"))
                        .adId(re.getString("ad_id"))
                        .cumulativeReach(re.getInt("cumulative_reach"))
                        .impressionsFrequencyAverage(Optional.ofNullable(re.getBigDecimal("impressions_frequency_average")).orElse(BigDecimal.ZERO))
                        .build(),
                conditionBuilder.getValues());
    }

    @Override
    public List<AdHomePerformancedto> getSdReportByDate(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, AdProductPageParam param) {
        return this.getSdReportByDate(puid, shopId, marketplaceId, startStr, endStr, param, false);
    }

    @Override
    public List<AdHomePerformancedto> getSdReportByDate(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, AdProductPageParam param, boolean isLatest) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sd' `type`,c.ad_id ad_id,count_date,sum(cost) cost, sum(sales14d)  total_sales,sum(impressions) impressions,sum(clicks)  clicks, ");
        sql.append(" sum(conversions14d) sale_num,sum(view_impressions) `view_impressions`,sum(conversions14d_same_sku) `conversions14d_same_sku`, ");
        sql.append(" sum(sales14d_same_sku) `sales14d_same_sku`,sum(units_ordered14d) `units_ordered14d`,sum(orders_new_to_brand14d) `orders_new_to_brand14d`,");
        sql.append(" sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(units_ordered_new_to_brand14d) `units_ordered_new_to_brand14d`,");
        sql.append(" sum(`new_to_brand_detail_page_views`) AS `new_to_brand_detail_page_views`,");
        sql.append(" sum(`add_to_cart`) AS `add_to_cart`,");
        sql.append(" sum(`video_first_quartile_views`) AS `video_first_quartile_views`,");
        sql.append(" sum(`video_Midpoint_Views`) AS `video_Midpoint_Views`,");
        sql.append(" sum(`video_third_quartile_views`) AS `video_third_quartile_views`,");
        sql.append(" sum(`video_complete_views`) AS `video_complete_views`,");
        sql.append(" sum(`video_unmutes`) AS `video_unmutes`,");
        sql.append(" sum(`branded_searches14d`) AS `branded_searches14d`,");
        sql.append(" sum(`detail_page_view14d`) AS `detail_page_view14d`,");
        sql.append(" max(`cumulative_reach`) AS `cumulative_reach`,");
        sql.append(" max(`impressions_frequency_average`) AS `impressions_frequency_average`");
        sql.append(" from t_amazon_ad_product_sd c join ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" r on r.puid=c.puid and r.shop_id=c.shop_id and r.ad_id=c.ad_id ");
        if(param.getUseAdvanced() != null && param.getUseAdvanced() && (param.getVcpmMax() != null || param.getVcpmMin() != null)){
            sql.append(" join t_amazon_ad_campaign_all a on a.puid=c.puid and a.shop_id=c.shop_id and a.campaign_id=c.campaign_id ");
        }
        sql.append(" where c.puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }
        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", campaignIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(param.getGroupId())) {
            //广告组id
            List<String> groupIds = StringUtil.splitStr(param.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", groupIds, argsList));
        }

        //标签筛选
        if (CollectionUtils.isNotEmpty(param.getAdIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.ad_id", param.getAdIds(), argsList));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值

            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                if(param.getListSearchValueNew().size() > 1){
                    whereSql.append(SqlStringUtil.dealInList("c.asin",param.getListSearchValueNew(),argsList));
                }else {
                    whereSql.append(" and c.asin = ? ");
                    argsList.add(param.getListSearchValueNew().get(0).trim());
                }

            } else if ("msku".equalsIgnoreCase(param.getSearchField())) {
                if(param.getListSearchValueNew().size() > 1){
                    whereSql.append(SqlStringUtil.dealInList("c.sku",param.getListSearchValueNew(),argsList));
                }else {
                    whereSql.append(" and c.sku like ? ");
                    argsList.add("%" + param.getListSearchValueNew().get(0).trim() + "%");
                }
            } else if ("parentAsin".equalsIgnoreCase(param.getSearchField())) {
                List<String> asin = odsProductDao.listByParentAsin(param.getPuid(), param.getShopId(), param.getListSearchValueNew());
                if (asin.size() == 0) {
                    asin.add("-1");
                }
                whereSql.append(SqlStringUtil.dealInList("c.asin", asin, argsList));
            }

        }
        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }

        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ?");
                argsList.add(AmazonSdAdProduct.servingStatusEnum.AD_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }
        }

        if (StringUtils.isNotBlank(marketplaceId)) {
            whereSql.append(" and r.marketplace_id = ? ");
            argsList.add(marketplaceId);
        }

        if (!isLatest) {
            whereSql.append(" and r.count_date >= ? and r.count_date <= ? ");
            argsList.add(startStr);
            argsList.add(endStr);
        }
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (param.getVcpmMin() != null || param.getVcpmMax() != null) {
                // vcpm高级筛选只筛选费用类型为vcpm
                whereSql.append(" and a.cost_type = 'vcpm' ");
            }
        }
        whereSql.append(" group by c.ad_id ");

        whereSql.append(subWhereSql(param, argsList)); // 高级筛选
        sql.append(whereSql);


        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
            @Override
            public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                AdHomePerformancedto dto = AdHomePerformancedto.builder()
                        .adId(re.getString("ad_id"))
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .countDate(re.getString("count_date"))
                        //可见展示次数(VCPM专用)
                        .viewImpressions(Optional.ofNullable(re.getInt("view_impressions")).orElse(0))
                        //本广告产品订单量
                        .adSaleNum(Optional.ofNullable(re.getInt("conversions14d_same_sku")).orElse(0))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(re.getBigDecimal("sales14d_same_sku")).orElse(BigDecimal.ZERO))
                        //CPC,VCPM广告销量
                        .salesNum(Optional.ofNullable(re.getInt("units_ordered14d")).orElse(0))
                        //CPC,VCPM-“品牌新买家”订单量
                        .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                        //CPC,VCPM-“品牌新买家”销售额
                        .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                        //CPC,VCPM-“品牌新买家”销量
                        .unitsOrderedNewToBrand14d(Optional.ofNullable(re.getInt("units_ordered_new_to_brand14d")).orElse(0))
                        .type(re.getString("type"))
                        .newToBrandDetailPageViews(Optional.ofNullable(re.getInt("new_to_brand_detail_page_views")).orElse(0))
                        .addToCart(Optional.ofNullable(re.getInt("add_to_cart")).orElse(0))
                        .videoFirstQuartileViews(Optional.ofNullable(re.getInt("video_first_quartile_views")).orElse(0))
                        .videoMidpointViews(Optional.ofNullable(re.getInt("video_Midpoint_Views")).orElse(0))
                        .videoThirdQuartileViews(Optional.ofNullable(re.getInt("video_third_quartile_views")).orElse(0))
                        .videoCompleteViews(Optional.ofNullable(re.getInt("video_complete_views")).orElse(0))
                        .videoUnmutes(Optional.ofNullable(re.getInt("video_unmutes")).orElse(0))
                        .brandedSearches(Optional.ofNullable(re.getInt("branded_searches14d")).orElse(0))
                        .detailPageViews(Optional.ofNullable(re.getInt("detail_page_view14d")).orElse(0))
                        .cumulativeReach(re.getInt("cumulative_reach"))
                        .impressionsFrequencyAverage(Optional.ofNullable(re.getBigDecimal("impressions_frequency_average")).orElse(BigDecimal.ZERO))
                        .build();
                return dto;
            }
        }, argsList.toArray());
    }

    @Override
    public List<AdHomePerformancedto> getSdReportByAdIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> adIdList) {
        if (CollectionUtils.isEmpty(adIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sd' `type`,count_date,sum(cost) cost, sum(sales14d)  total_sales,sum(impressions) impressions,sum(clicks)  clicks, ");
        sql.append(" sum(conversions14d) sale_num,sum(view_impressions) `view_impressions`,sum(conversions14d_same_sku) `conversions14d_same_sku`, ");
        sql.append(" sum(sales14d_same_sku) `sales14d_same_sku`,sum(units_ordered14d) `units_ordered14d`,sum(orders_new_to_brand14d) `orders_new_to_brand14d`,");
        sql.append(" sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(units_ordered_new_to_brand14d) `units_ordered_new_to_brand14d` from ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where puid= ? and shop_id= ? ");

        argsList.add(puid);
        argsList.add(shopId);

        sql.append(SqlStringUtil.dealInList("ad_id", adIdList, argsList));
        sql.append("  and count_date >= ? and count_date <= ? group by count_date ");
        argsList.add(startStr);
        argsList.add(endStr);

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
            @Override
            public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                AdHomePerformancedto dto = AdHomePerformancedto.builder()
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .countDate(re.getString("count_date"))
                        //可见展示次数(VCPM专用)
                        .viewImpressions(Optional.ofNullable(re.getInt("view_impressions")).orElse(0))
                        //本广告产品订单量
                        .adSaleNum(Optional.ofNullable(re.getInt("conversions14d_same_sku")).orElse(0))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(re.getBigDecimal("sales14d_same_sku")).orElse(BigDecimal.ZERO))
                        //CPC,VCPM广告销量
                        .salesNum(Optional.ofNullable(re.getInt("units_ordered14d")).orElse(0))
                        //CPC,VCPM-“品牌新买家”订单量
                        .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                        //CPC,VCPM-“品牌新买家”销售额
                        .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                        //CPC,VCPM-“品牌新买家”销量
                        .unitsOrderedNewToBrand14d(Optional.ofNullable(re.getInt("units_ordered_new_to_brand14d")).orElse(0))
                        .type(re.getString("type"))
                        .build();
                return dto;
            }
        }, argsList.toArray());
    }

    @Override
    public List<String> getProductListByUpdateTime(Integer puid, Integer shopId, Date date) {
        String sql = "select ad_id from (select sum(`impressions`) impressions, ad_id from t_amazon_ad_sd_product_report where " +
                " puid = ? and shop_id=?  and update_time > ? group by ad_id having impressions > 0) a ";

        return getJdbcTemplate(puid).queryForList(sql, new Object[]{puid,shopId,date}, String.class);
    }

    @Override
    public List<AmazonAdSdProductReport> getListReportByDate(Integer puid, Integer shopId, String startDate, String endDate) {
        String sql = "select * from " + getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)) + " where puid= ? and shop_id=? and `count_date` >= ? and  `count_date` <= ? and impressions > 0 and asin is not null";
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(shopId);
        args.add(startDate);
        args.add(endDate);
        return getJdbcTemplate(puid).query(sql,args.toArray(),getMapper());
    }


    @Override
    public List<AdAsinProductDto> getAsinPageSdProductList(AdAsinPageParam param, List<String> asinList) {
        if (CollectionUtils.isEmpty(asinList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT asin, parent_asin, sku, campaign_id, campaign_name, ad_group_id, ad_group_name, SUM(units_ordered14d) as totalSaleNum, SUM(conversions14d_same_sku) as adSaleNum FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" WHERE puid = ? and shop_id = ? ");
        argsList.add(param.getPuid());
        argsList.add(param.getShopId());
        if (StringUtils.isNotBlank(param.getMarketplaceId())) {
            sql.append("and marketplace_id = ? ");
            argsList.add(param.getMarketplaceId());
        }
        sql.append("and count_date >= ? and count_date <= ?");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (CollectionUtils.isNotEmpty(asinList)) {
            sql.append(SqlStringUtil.dealInList("asin", asinList, argsList));
        }
        sql.append("group by asin");
        return getJdbcTemplate(param.getPuid()).query(sql.toString(), new RowMapper<AdAsinProductDto>() {
            @Override
            public AdAsinProductDto mapRow(ResultSet re, int i) throws SQLException {
                AdAsinProductDto dto = AdAsinProductDto.builder()
                        .asin(re.getString("asin"))
                        .parentAsin(re.getString("parent_asin"))
                        .sku(re.getString("sku"))
                        .campaignId(re.getString("campaign_id"))
                        .campaignName(re.getString("campaign_name"))
                        .adGroupId(re.getString("ad_group_id"))
                        .adGroupName(re.getString("ad_group_name"))
                        .orderNum(Optional.ofNullable(re.getInt("totalSaleNum")).orElse(0))
                        .adSelfSaleNum(Optional.ofNullable(re.getInt("adSaleNum")).orElse(0))
                        .build();
                return dto;
            }
        }, argsList.toArray());
    }


    @Override
    public List<AdAsinAggregateDto> getAsinAggregateDto(AdAsinPageParam param, List<String> asinList) {
        if (CollectionUtils.isEmpty(asinList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT SUM(units_ordered14d) as totalSaleNum, SUM(conversions14d_same_sku) as adSaleNum FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" WHERE puid = ? and shop_id = ? ");
        argsList.add(param.getPuid());
        argsList.add(param.getShopId());
        if (StringUtils.isNotBlank(param.getMarketplaceId())) {
            sql.append("and marketplace_id = ? ");
            argsList.add(param.getMarketplaceId());
        }
        sql.append("and count_date >= ? and count_date <= ?");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (CollectionUtils.isNotEmpty(asinList)) {
            sql.append(SqlStringUtil.dealInList("asin", asinList, argsList));
        }
        return getJdbcTemplate(param.getPuid()).query(sql.toString(), new RowMapper<AdAsinAggregateDto>() {
            @Override
            public AdAsinAggregateDto mapRow(ResultSet re, int i) throws SQLException {
                AdAsinAggregateDto dto = AdAsinAggregateDto.builder()
                        .orderNum(Optional.ofNullable(re.getInt("totalSaleNum")).orElse(0))
                        .adSelfSaleNum(Optional.ofNullable(re.getInt("adSaleNum")).orElse(0))
                        .build();
                return dto;
            }
        }, argsList.toArray());
    }

    @Override
    public List<AmazonAdSdProductReport> getReportByAdId(Integer puid, Integer shopId, String startDate, String endDate, String marketplaceId, String adId) {

        String sql = "SELECT * FROM " + getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)) + " where ";
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid).equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketplaceId)
                .equalTo("ad_id", adId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate).build();
        sql += " " + builder.getSql();

        return getJdbcTemplate(puid).query(sql, builder.getValues(), getMapper());
    }

    @Override
    public List<AmazonAdSdProductReport> getSales(Integer puid, Integer shopId, String startDate, String endDate,List<String> asins, List<String> campaignIds) {
        StringBuilder sql = new StringBuilder("SELECT puid,shop_id,marketplace_id,count_date,asin,campaign_id,");
        sql.append(" sum(orders_new_to_brand14d) as orders_new_to_brand14d,sum(conversions14d) as conversions14d");
        sql.append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)));
        StringBuilder whereSql = new StringBuilder(" WHERE puid = ? and shop_id = ? and count_date >= ? and count_date <= ? ");
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(shopId);
        args.add(startDate);
        args.add(endDate);
        if (CollectionUtils.isNotEmpty(asins)) {
            whereSql.append(SqlStringUtil.dealInList("asin", asins, args));
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, args));
        }
        whereSql.append(" group by count_date");
        sql.append(whereSql);
        return getJdbcTemplate(puid).query(sql.toString(),getRowMapper(),args.toArray());
    }


    private StringBuilder subWhereSql(AdProductPageParam param, List<Object> argsList) {
        StringBuilder subWhereSql = new StringBuilder();
        //高级筛选
        if(param.getUseAdvanced()){
            BigDecimal shopSales = param.getShopSales() != null ? param.getShopSales() : BigDecimal.valueOf(0);

            subWhereSql.append(" having 1=1 ");
            //展示量
            if(param.getImpressionsMin() != null){
                subWhereSql.append(" and impressions >= ?");
                argsList.add(param.getImpressionsMin());
            }
            if(param.getImpressionsMax() != null){
                subWhereSql.append(" and impressions <= ?");
                argsList.add(param.getImpressionsMax());
            }
            //点击量
            if(param.getClicksMin() != null){
                subWhereSql.append(" and clicks >= ?");
                argsList.add(param.getClicksMin());
            }
            if(param.getClicksMax() != null){
                subWhereSql.append(" and clicks <= ?");
                argsList.add(param.getClicksMax());
            }
            //点击率（clicks/impressions）
            if(param.getClickRateMin() != null){
                subWhereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(param.getClickRateMin());
            }
            if(param.getClickRateMax() != null){
                subWhereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(param.getClickRateMax());
            }
            //花费
            if(param.getCostMin() != null){
                subWhereSql.append(" and cost >= ?");
                argsList.add(param.getCostMin());
            }
            if(param.getCostMax() != null){
                subWhereSql.append(" and cost <= ?");
                argsList.add(param.getCostMax());
            }
            //cpc  平均点击费用
            if(param.getCpcMin() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(param.getCpcMin());
            }
            if(param.getCpcMax() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(param.getCpcMax());
            }
            //广告订单量
            if(param.getOrderNumMin() != null){
                subWhereSql.append(" and sale_num >= ?");
                argsList.add(param.getOrderNumMin());
            }
            if(param.getOrderNumMax() != null){
                subWhereSql.append(" and sale_num <= ?");
                argsList.add(param.getOrderNumMax());
            }
            //广告销售额
            if(param.getSalesMin() != null){
                subWhereSql.append(" and total_sales >= ?");
                argsList.add(param.getSalesMin());
            }
            if(param.getSalesMax() != null){
                subWhereSql.append(" and total_sales <= ?");
                argsList.add(param.getSalesMax());
            }
            //订单转化率
            if(param.getSalesConversionRateMin() != null){
                subWhereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(param.getSalesConversionRateMin());
            }
            if(param.getSalesConversionRateMax() != null){
                subWhereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(param.getSalesConversionRateMax());
            }
            //acos
            if(param.getAcosMin() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(param.getAcosMin());
            }
            if(param.getAcosMax() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(param.getAcosMax());
            }
            // roas
            if (param.getRoasMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(param.getRoasMin());
            }
            // roas
            if (param.getRoasMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(param.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAcotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAcotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (param.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAsotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (param.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAsotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }

            /*************************sd广告产品高级筛选新增查询指标*******************************/

            //可见展示次数
            if (param.getViewImpressionsMin() != null) {
                subWhereSql.append(" and view_impressions >= ? ");
                argsList.add(param.getViewImpressionsMin());
            }
            if (param.getViewImpressionsMax() != null) {
                subWhereSql.append(" and view_impressions <= ? ");
                argsList.add(param.getViewImpressionsMax());
            }
            //CPA
            if (param.getCpaMin() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) >= ?");
                argsList.add(param.getCpaMin());
            }
            if (param.getCpaMax() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) <= ?");
                argsList.add(param.getCpaMax());
            }
            //VCPM
            if (param.getVcpmMin() != null) {
                subWhereSql.append(" and ROUND(ifnull((cost/view_impressions) * 1000,0),2) >= ? ");
                argsList.add(param.getVcpmMin());
            }
            if (param.getVcpmMax() != null) {
                subWhereSql.append(" and ROUND(ifnull((cost/view_impressions) * 1000,0),2) <= ? ");
                argsList.add(param.getVcpmMax());
            }
            //本广告产品订单量
            if (param.getAdSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(conversions14d_same_sku, 0) >= ? ");
                argsList.add(param.getAdSaleNumMin());
            }
            if (param.getAdSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(conversions14d_same_sku, 0) <= ? ");
                argsList.add(param.getAdSaleNumMax());
            }

            //本广告产品销售额
            if (param.getAdSalesMin() != null) {
                subWhereSql.append(" and ifnull(sales14d_same_sku, 0) >= ? ");
                argsList.add(param.getAdSalesMin());
            }
            if (param.getAdSalesMax() != null) {
                subWhereSql.append(" and ifnull(sales14d_same_sku, 0) <= ? ");
                argsList.add(param.getAdSalesMax());
            }

            //广告销量 units_ordered14d
            if (param.getAdSalesTotalMin() != null) {
                subWhereSql.append(" and ifnull(units_ordered14d, 0) >= ? ");
                argsList.add(param.getAdSalesTotalMin());
            }
            if (param.getAdSalesTotalMax() != null) {
                subWhereSql.append(" and ifnull(units_ordered14d, 0) <= ? ");
                argsList.add(param.getAdSalesTotalMax());
            }

            //品牌新买家订单量
            if (param.getOrdersNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ifnull(orders_new_to_brand14d, 0) >= ? ");
                argsList.add(param.getOrdersNewToBrandFTDMin());
            }
            if (param.getOrdersNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ifnull(orders_new_to_brand14d, 0) <= ? ");
                argsList.add(param.getOrdersNewToBrandFTDMax());
            }
            //品牌新买家订单百分比
            if (param.getOrderRateNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((orders_new_to_brand14d/sale_num) * 100, 0), 4), 2) >= ? ");
                argsList.add(MathUtil.multiplyZero(param.getOrderRateNewToBrandFTDMin(), BigDecimal.valueOf(100)));
            }
            if (param.getOrderRateNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((orders_new_to_brand14d/sale_num) * 100, 0), 4), 2) <= ? ");
                argsList.add(MathUtil.multiplyZero(param.getOrderRateNewToBrandFTDMax(), BigDecimal.valueOf(100)));
            }
            //品牌新买家销售额
            if (param.getSalesNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ifnull(sales_new_to_brand14d, 0) >= ?");
                argsList.add(param.getSalesNewToBrandFTDMin());
            }
            if (param.getSalesNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ifnull(sales_new_to_brand14d, 0) <= ?");
                argsList.add(param.getSalesNewToBrandFTDMax());
            }
            //品牌新买家销售额百分比
            if (param.getSalesRateNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((sales_new_to_brand14d/total_sales) * 100, 0), 4), 2) >= ? ");
                argsList.add(MathUtil.multiplyZero(param.getSalesRateNewToBrandFTDMin(), BigDecimal.valueOf(100)));
            }
            if (param.getSalesRateNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((sales_new_to_brand14d/total_sales) * 100, 0), 4), 2) <= ? ");
                argsList.add(MathUtil.multiplyZero(param.getSalesRateNewToBrandFTDMax(), BigDecimal.valueOf(100)));
            }
            //品牌新买家销量
            if (param.getUnitsOrderedNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ifnull(units_ordered_new_to_brand14d, 0) >= ?");
                argsList.add(param.getUnitsOrderedNewToBrandFTDMin());
            }
            if (param.getUnitsOrderedNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ifnull(units_ordered_new_to_brand14d, 0) <= ?");
                argsList.add(param.getUnitsOrderedNewToBrandFTDMax());
            }
            //品牌新买家销量百分比
            if (param.getUnitsOrderedRateNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((units_ordered_new_to_brand14d/units_ordered14d) * 100, 0), 4), 2) >= ? ");
                argsList.add(MathUtil.multiplyZero(param.getUnitsOrderedRateNewToBrandFTDMin(), BigDecimal.valueOf(100)));
            }
            if (param.getUnitsOrderedRateNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((units_ordered_new_to_brand14d/units_ordered14d) * 100, 0), 4), 2) <= ? ");
                argsList.add(MathUtil.multiplyZero(param.getUnitsOrderedRateNewToBrandFTDMax(), BigDecimal.valueOf(100)));
            }

            // 加购次数 筛选
            if (param.getAddToCartMin() != null) {
                subWhereSql.append(" and ifnull(add_to_cart , 0) >= ? ");
                argsList.add(param.getAddToCartMin());
            }
            if (param.getAddToCartMax() != null ) {
                subWhereSql.append(" and ifnull(add_to_cart , 0) <= ? ");
                argsList.add(param.getAddToCartMax());
            }

            // 视频完整播放次数 筛选
            if (param.getVideoCompleteViewsMin() != null ) {
                subWhereSql.append(" and ifnull(video_complete_views , 0) >= ? ");
                argsList.add(param.getVideoCompleteViewsMin());
            }
            if (param.getVideoCompleteViewsMax() != null ) {
                subWhereSql.append(" and ifnull(video_complete_views , 0) <= ? ");
                argsList.add(param.getVideoCompleteViewsMax());
            }

            // 观看率 筛选
            if (param.getViewabilityRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull((view_impressions/impressions) * 100, 0), 2) >= ? ");
                argsList.add(param.getViewabilityRateMin());
            }
            if (param.getViewabilityRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull((view_impressions/impressions) * 100, 0), 2) <= ? ");
                argsList.add(param.getViewabilityRateMax());
            }

            // 观看点击率 筛选
            if (param.getViewClickThroughRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull((clicks/view_impressions) * 100, 0), 2) >= ? ");
                argsList.add(param.getViewClickThroughRateMin());
            }
            if (param.getViewClickThroughRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull((clicks/view_impressions) * 100, 0), 2) <= ? ");
                argsList.add(param.getViewClickThroughRateMax());
            }

            // 品牌搜索次数 筛选
            if (param.getBrandedSearchesMin() != null) {
                subWhereSql.append(" and ifnull(branded_searches14d , 0) >= ? ");
                argsList.add(param.getBrandedSearchesMin()); }
            if (param.getBrandedSearchesMax() != null) {
                subWhereSql.append(" and ifnull(branded_searches14d , 0) <= ? ");
                argsList.add(param.getBrandedSearchesMax());
            }

            // 广告笔单价 筛选
            if (param.getAdvertisingUnitPriceMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) >= ?");
                argsList.add(param.getAdvertisingUnitPriceMin());
            }
            if (param.getAdvertisingUnitPriceMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) <= ?");
                argsList.add(param.getAdvertisingUnitPriceMax());
            }

        }
        return subWhereSql;
    }

    @Override
    public List<AmazonAdSdProductReport> listReportsByAdIdsGroupByCountDate(int puid, Integer shopId, String marketplaceId, String startDate, String endDate, List<String> adIds) {
        String sql = "SELECT puid, shop_id, count_date, campaign_id, ad_group_id, ad_id, sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`sales14d_same_sku`) sales14d_same_sku," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`units_ordered14d`) units_ordered14d, sum(`conversions14d`) conversions14d," +
                "sum(`conversions14d_same_sku`) conversions14d_same_sku " +
                " FROM `t_amazon_ad_sd_product_report` where ";

        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .equalToWithoutCheck("marketplace_id", marketplaceId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate)
                .in("ad_id",adIds.toArray(new String []{}));

        builder.groupBy("count_date");
        ConditionBuilder conditionBuilder = builder.build();

        sql += conditionBuilder.getSql();

        return getJdbcTemplate(puid).query(sql, getMapper(), conditionBuilder.getValues());
    }

    @Override
    public List<String> getAdIdsByProduct(int puid, int shopId, String marketplaceId, String start, List<String> adId) {
        StringBuilder sql = new StringBuilder(" select distinct ad_id from `t_amazon_ad_sd_product_report` ");
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? and marketplace_id = ? and count_date >= ? and impressions > 0 ");
        List<Object> argsList = Lists.newArrayList(puid, shopId, marketplaceId, start);
        whereSql.append(SqlStringUtil.dealInList("ad_id", adId, argsList));
        return getJdbcTemplate(puid).queryForList(sql.append(whereSql).toString(), argsList.toArray(), String.class);

    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }


    @Override
    public List<InvoiceProductDto> getInvoiceProductList(Integer puid, Integer shopId, String marketplaceId, List<String> campaignId, String start, String end) {
        if (CollectionUtils.isEmpty(campaignId)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT sum(cost) cost, asin, sku, campaign_id, max(update_time) update_time FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(start, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" WHERE puid = ? and shop_id = ? and marketplace_id = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);
        sql.append(" and count_date >= ? and count_date <= ? ");
        argsList.add(start);
        argsList.add(end);
        sql.append(SqlStringUtil.dealInList("campaign_id", campaignId, argsList));
        sql.append(" group by puid, shop_id, campaign_id, sku ");
        sql.append(" limit 100000 ");


        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<InvoiceProductDto>() {
            @Override
            public InvoiceProductDto mapRow(ResultSet re, int i) throws SQLException {
                InvoiceProductDto dto = InvoiceProductDto.builder()
                        .asin(re.getString("asin"))
                        .sku(re.getString("sku"))
                        .campaignId(re.getString("campaign_id"))
                        .cost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .updateTime(re.getTimestamp("update_time"))
                        .build();
                return dto;
            }
        }, argsList.toArray());


    }


    @Override
    public LocalDateTime getInvoiceMaxUpdateTime(Integer puid, Integer shopId, String marketplaceId, List<String> campaignId, String start, String end) {
        if (CollectionUtils.isEmpty(campaignId)) {
            return null;
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT max(update_time) FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(start, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" WHERE puid = ? and shop_id = ? and marketplace_id =? ");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);

        sql.append(" and count_date >= ? and count_date <= ? ");
        argsList.add(start);
        argsList.add(end);
        sql.append(SqlStringUtil.dealInList("campaign_id", campaignId, argsList));
        sql.append(" and  cost > 0 ");


        return getJdbcTemplate(puid).queryForObject(sql.toString(), LocalDateTime.class, argsList.toArray());


    }

}
