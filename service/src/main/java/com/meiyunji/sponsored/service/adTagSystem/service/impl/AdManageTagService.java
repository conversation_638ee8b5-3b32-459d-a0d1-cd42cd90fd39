package com.meiyunji.sponsored.service.adTagSystem.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.grpc.adTagSystem.TagDetailResponse;
import com.meiyunji.sponsored.grpc.adTagSystem.TagRemoveResponse;
import com.meiyunji.sponsored.grpc.adTagSystem.TagResponse;
import com.meiyunji.sponsored.service.account.bo.ShopAuthBo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.adTagSystem.dao.IAdManageTagDao;
import com.meiyunji.sponsored.service.adTagSystem.dao.IAdManageTagGroupDao;
import com.meiyunji.sponsored.service.adTagSystem.dao.IAdManageTagGroupUserDao;
import com.meiyunji.sponsored.service.adTagSystem.dao.IAdManageTagLogDao;
import com.meiyunji.sponsored.service.adTagSystem.enums.AdManageTagTypeEnum;
import com.meiyunji.sponsored.service.adTagSystem.enums.AdTagLogModuleEnum;
import com.meiyunji.sponsored.service.adTagSystem.enums.AdTagPermissionTypeEnum;
import com.meiyunji.sponsored.service.adTagSystem.param.AdTagParam;
import com.meiyunji.sponsored.service.adTagSystem.param.TagsRemoveParam;
import com.meiyunji.sponsored.service.adTagSystem.po.AdManageTag;
import com.meiyunji.sponsored.service.adTagSystem.po.AdManageTagGroup;
import com.meiyunji.sponsored.service.adTagSystem.service.IAdManageTagRelationService;
import com.meiyunji.sponsored.service.adTagSystem.service.IAdManageTagService;
import com.meiyunji.sponsored.service.doris.po.OdsAdManageTag;
import com.meiyunji.sponsored.service.doris.service.impl.DorisServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-10-23  16:14
 */
@Service
@Slf4j
public class AdManageTagService implements IAdManageTagService {

    @Autowired
    private IUserDao userDao;
    @Autowired
    private IAdManageTagGroupDao tagGroupDao;
    @Autowired
    private IAdManageTagDao tagDao;
    @Autowired
    private IAdManageTagGroupUserDao tagGroupUserDao;
    @Autowired
    private DorisServiceImpl dorisService;
    @Autowired
    private IAdManageTagRelationService tagRelationService;
    @Autowired
    private IAdManageTagLogDao tagLogDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Override
    public TagResponse addTag(AdTagParam param) {
        TagResponse.Builder builder = TagResponse.newBuilder();
        long groupId = param.getGroupId();
        int puid = param.getPuid();
        int uid = param.getUid();

        AdManageTagGroup tagGroup = tagGroupDao.getByPuidAndId(puid, groupId);
        if (Objects.isNull(tagGroup)) {
            builder.setCode(Result.ERROR);
            builder.setMsg("标签组不存在");
            return builder.build();
        }
        if (!(param.getIsAdmin() != null && param.getIsAdmin()) && !tagGroupUserDao.hasGroupPermission(puid, uid, groupId)) {
            builder.setCode(Result.ERROR);
            builder.setMsg("没有权限");
            return builder.build();
        }
        Integer type = tagGroup.getType();
        AdManageTag existTag = tagDao.getByName(puid, type, groupId, param.getName());
        if (Objects.nonNull(existTag)) {
            Integer createId = existTag.getCreateId();
            List<User> users = userDao.listByIds(puid, Lists.newArrayList(createId));
            String creatorName = null;
            if (CollectionUtils.isNotEmpty(users)) {
                creatorName = users.get(0).getUserNickname();
            }
            builder.setCode(Result.ERROR);
            builder.setMsg("广告活动标签名称已被其他账户使用" + (StringUtils.isNotBlank(creatorName) ? "(" + creatorName + ")" : ""));
            return builder.build();
        }
        if (tagDao.countByPuidType(puid, type) >= 200) {
            builder.setCode(Result.ERROR);
            builder.setMsg("广告活动标签数量已达200个（包括其他人创建的），无法添加");
            return builder.build();
        }

        AdManageTag tag = new AdManageTag();
        tag.setPuid(puid);
        tag.setType(type);
        tag.setGroupId(groupId);
        tag.setName(param.getName());
        tag.setColor(param.getColor());
        tag.setPermissionType(tagGroup.getPermissionType());
        tag.setDefinition(param.getDefinition());
        tag.setRemark(param.getRemark());
        tag.setCreateId(uid);

        try {
            tagDao.save(puid, tag);
            AdManageTag saveTag = tagDao.getByPuidAndId(puid, tag.getId());
            OdsAdManageTag odsTag = new OdsAdManageTag();
            BeanUtils.copyProperties(saveTag, odsTag);
            dorisService.saveDoris(Lists.newArrayList(odsTag));
            tagLogDao.addLog(puid, uid, type, AdTagLogModuleEnum.ADD_TAG.getCode(),
                    String.format("添加标签, %s", saveTag.toBaseString()));
        } catch (Exception e) {
            log.error("标签添加异常, param={}", param, e);
            builder.setCode(Result.ERROR);
            builder.setMsg("标签添加异常");
            return builder.build();
        }

        builder.setCode(Result.SUCCESS);
        builder.setMsg("标签添加成功");
        return builder.build();
    }

    @Override
    public TagResponse updateTag(AdTagParam param) {
        TagResponse.Builder builder = TagResponse.newBuilder();
        long groupId = param.getGroupId();
        int puid = param.getPuid();
        int uid = param.getUid();
        Long id = param.getId();
        String name = param.getName();

        AdManageTag tag = tagDao.getByPuidAndId(puid, id);
        if (Objects.isNull(tag)) {
            builder.setCode(Result.ERROR);
            builder.setMsg("找不到该标签");
            return builder.build();
        }
        String oldTagStr = tag.toBaseString();
        AdManageTagGroup tagGroup = tagGroupDao.getByPuidAndId(puid, groupId);
        if (Objects.isNull(tagGroup)) {
            builder.setCode(Result.ERROR);
            builder.setMsg("标签组不存在");
            return builder.build();
        }
        if (!(param.getIsAdmin() != null && param.getIsAdmin()) && !tagGroupUserDao.hasGroupPermission(puid, uid, groupId)) {
            builder.setCode(Result.ERROR);
            builder.setMsg("没有权限");
            return builder.build();
        }
        Integer type = tagGroup.getType();
        if (!StringUtils.equals(tag.getName(), name)
                || !Objects.equals(tag.getGroupId(), groupId)) {
            AdManageTag existTag = tagDao.getByName(puid, type, groupId, name);
            if (Objects.nonNull(existTag)) {
                Integer createId = existTag.getCreateId();
                List<User> users = userDao.listByIds(puid, Lists.newArrayList(createId));
                String creatorName = null;
                if (CollectionUtils.isNotEmpty(users)) {
                    creatorName = users.get(0).getUserNickname();
                }
                builder.setCode(Result.ERROR);
                builder.setMsg("广告活动标签名称已被其他账户使用" + (StringUtils.isNotBlank(creatorName) ? "(" + creatorName + ")" : ""));
                return builder.build();
            }
        }

        tag.setName(name);
        tag.setGroupId(groupId);
        tag.setType(tagGroup.getType());
        tag.setPermissionType(tagGroup.getPermissionType());
        tag.setColor(param.getColor());
        tag.setDefinition(param.getDefinition());
        tag.setRemark(param.getRemark());
        tag.setUpdateId(uid);
        tagDao.updateNullByIdAndPuid(puid, tag);
        OdsAdManageTag odsTag = new OdsAdManageTag();
        BeanUtils.copyProperties(tagDao.getByPuidAndId(puid, id), odsTag);
        dorisService.saveDoris(Lists.newArrayList(odsTag));
        if (!Objects.equals(tag.getGroupId(), groupId)) {
            tagRelationService.updateGroupIdByTagId(puid, tagGroup.getType(), id, groupId);
        }
        tagLogDao.addLog(puid, uid, type, AdTagLogModuleEnum.UPDATE_TAG.getCode(),
                String.format("编辑标签, 原标签：%s, 新标签：%s", oldTagStr, odsTag.toBaseString()));
        builder.setCode(Result.SUCCESS);
        builder.setMsg("标签更新成功");
        return builder.build();
    }

    @Override
    public TagResponse updateTagSort(AdTagParam param) {
        TagResponse.Builder builder = TagResponse.newBuilder();
        int puid = param.getPuid();
        int uid = param.getUid();
        Long groupId = param.getGroupId();
        AdManageTagGroup tagGroup = tagGroupDao.getByPuidAndId(puid, groupId);
        if (Objects.isNull(tagGroup)) {
            builder.setCode(Result.ERROR);
            builder.setMsg("标签组不存在");
            return builder.build();
        }
        if (!(param.getIsAdmin() != null && param.getIsAdmin()) && !tagGroupUserDao.hasGroupPermission(puid, uid, groupId)) {
            builder.setCode(Result.ERROR);
            builder.setMsg("没有权限");
            return builder.build();
        }
        Map<Long, Integer> sortMap = param.getSortList().stream()
                .collect(Collectors.toMap(AdTagParam.TagSortParam::getId, AdTagParam.TagSortParam::getSort, (v1, v2) -> v2));
        tagDao.updateTagSort(puid, groupId, tagGroup.getType(), sortMap);
        List<AdManageTag> adManageTags = tagDao.listSortUpdateTag(puid, groupId, tagGroup.getType(), sortMap);
        if (CollectionUtils.isNotEmpty(adManageTags)) {
            dorisService.saveDoris(adManageTags.stream().map(i -> {
                OdsAdManageTag odsTag = new OdsAdManageTag();
                BeanUtils.copyProperties(i, odsTag);
                return odsTag;
            }).collect(Collectors.toList()));
        }
        builder.setCode(Result.SUCCESS);
        builder.setMsg("标签排序更新成功");
        return builder.build();
    }

    @Override
    public TagRemoveResponse removeTagCampaign(TagsRemoveParam param) {
        TagRemoveResponse.Builder builder = TagRemoveResponse.newBuilder();
        int puid = param.getPuid();
        int uid = param.getUid();
        processRemoveIds(param);
        List<Long> removeTagIds = param.getRemoveTagIds();
        List<Long> removeTagGroupIds = param.getRemoveTagGroupIds();
        if (CollectionUtils.isEmpty(removeTagIds) && CollectionUtils.isEmpty(removeTagGroupIds)) {
            builder.setCode(Result.SUCCESS);
            return builder.build();
        }
        //获取标签店铺
        List<String> marketplaceIdList = CollectionUtils.isEmpty(param.getMarketplaceIdList()) ? new ArrayList<>() : param.getMarketplaceIdList();
        List<Integer> shopIdList = shopAuthDao.getShopAuthBoByMarketPlaceAndIds(puid, param.getShopIdList(), marketplaceIdList)
                .stream().map(ShopAuthBo::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shopIdList)) {
            builder.setCode(Result.SUCCESS);
            return builder.build();
        }
        tagRelationService.deleteByTagGroupIds(puid, shopIdList, removeTagGroupIds);
        tagRelationService.deleteByTagIds(puid, shopIdList, removeTagIds);
        tagLogDao.addLog(puid, uid, AdManageTagTypeEnum.CAMPAIGN.getCode(), AdTagLogModuleEnum.DEL_TAG_RELATION.getCode(),
                String.format("清除广告活动, 清除的groupIds：%s, 清除的标签ids：%s", removeTagGroupIds, removeTagIds));
        builder.setCode(Result.SUCCESS);
        return builder.build();
    }

    @Override
    public TagRemoveResponse removeTags(TagsRemoveParam param) {
        TagRemoveResponse.Builder builder = TagRemoveResponse.newBuilder();
        int puid = param.getPuid();
        int uid = param.getUid();
        processRemoveIds(param);
        List<Long> removeTagIds = param.getRemoveTagIds();
        List<Long> removeTagGroupIds = param.getRemoveTagGroupIds();
        if (CollectionUtils.isEmpty(removeTagIds) && CollectionUtils.isEmpty(removeTagGroupIds)) {
            builder.setCode(Result.SUCCESS);
            return builder.build();
        }
        removeTagGroups(puid, removeTagGroupIds);
        removeTags(puid, removeTagIds);
        tagLogDao.addLog(puid, uid, AdManageTagTypeEnum.CAMPAIGN.getCode(), AdTagLogModuleEnum.DEL_TAG_RELATION.getCode(),
                String.format("删除标签, 删除的groupIds：%s, 删除的标签ids：%s", removeTagGroupIds, removeTagIds));
        builder.setCode(Result.SUCCESS);
        return builder.build();
    }

    private void processRemoveIds(TagsRemoveParam param) {
        int puid = param.getPuid();
        int uid = param.getUid();
        List<Long> groupIds;
        if (param.getIsAdmin() != null && param.getIsAdmin()) {
            groupIds = tagGroupUserDao.listGroupIdByPuid(puid, AdManageTagTypeEnum.CAMPAIGN.getCode());
        } else {
            groupIds = tagGroupUserDao.listGroupIdByUid(puid, AdManageTagTypeEnum.CAMPAIGN.getCode(), uid);
        }

        // 筛选出有权限的组id
        List<Long> tagGroupIds = CollectionUtils.isNotEmpty(param.getTagGroupIds()) ? param.getTagGroupIds() : Lists.newArrayList();
        tagGroupIds.retainAll(groupIds);
        param.setRemoveTagGroupIds(tagGroupIds);

        List<Long> tagIds = CollectionUtils.isNotEmpty(param.getTagIds()) ? param.getTagIds() : Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(tagIds)) {
            List<AdManageTag> adManageTags = tagDao.listByIds(puid, tagIds);
            // 先删除掉没有权限的标签
            adManageTags.removeIf(t -> !groupIds.contains(t.getGroupId()));
            // 再删除需要整组delete的标签
            adManageTags.removeIf(t -> tagGroupIds.contains(t.getGroupId()));
            if (CollectionUtils.isNotEmpty(adManageTags)) {
                param.setRemoveTagIds(adManageTags.stream().map(AdManageTag::getId).distinct().collect(Collectors.toList()));
            }
        }
        log.info("processRemoveIds param: {}", param);
    }

    private void removeTagGroups(int puid, List<Long> tagGroupIds) {
        if (CollectionUtils.isEmpty(tagGroupIds)) {
            return;
        }
        tagRelationService.deleteByTagGroupIds(puid, null, tagGroupIds);
        deleteByGroupIds(puid, tagGroupIds);
        tagGroupDao.deleteByIds(puid, tagGroupIds);
        tagGroupUserDao.delByPuidGroupIds(puid, tagGroupIds);
    }

    private void removeTags(int puid, List<Long> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            return;
        }
        tagRelationService.deleteByTagIds(puid, null, tagIds);
        deleteByIds(puid, tagIds);
    }

    private void deleteByGroupIds(int puid, List<Long> tagGroupIds) {
        if (CollectionUtils.isEmpty(tagGroupIds)) {
            return;
        }
        tagDao.deleteByGroupIds(puid, tagGroupIds);
        List<AdManageTag> adManageTags = tagDao.listDelByGroupIds(puid, tagGroupIds);
        if (CollectionUtils.isNotEmpty(adManageTags)) {
            List<OdsAdManageTag> odsAdManageTags = adManageTags.stream().map(i -> {
                OdsAdManageTag odsAdManageTag = new OdsAdManageTag();
                BeanUtils.copyProperties(i, odsAdManageTag);
                return odsAdManageTag;
            }).collect(Collectors.toList());
            dorisService.saveDoris(odsAdManageTags);
        }
    }

    private void deleteByIds(int puid, List<Long> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            return;
        }
        tagDao.deleteByIds(puid, tagIds);
        List<AdManageTag> adManageTags = tagDao.listDelByIds(puid, tagIds);
        if (CollectionUtils.isNotEmpty(adManageTags)) {
            List<OdsAdManageTag> odsAdManageTags = adManageTags.stream().map(i -> {
                OdsAdManageTag odsAdManageTag = new OdsAdManageTag();
                BeanUtils.copyProperties(i, odsAdManageTag);
                return odsAdManageTag;
            }).collect(Collectors.toList());
            dorisService.saveDoris(odsAdManageTags);
        }
    }

    @Override
    public TagDetailResponse.TagDetailResponseData getTagDetail(Integer puid, Long id, Boolean isGroup) {
        TagDetailResponse.TagDetailResponseData.Builder builder = TagDetailResponse.TagDetailResponseData.newBuilder();
        if (isGroup != null && isGroup) {
            AdManageTagGroup adManageTagGroup = tagGroupDao.getByPuidAndId(puid, id);
            if (adManageTagGroup != null) {
                builder.setId(adManageTagGroup.getId());
                builder.setName(adManageTagGroup.getName());
                builder.setDefinition(StringUtils.isNotBlank(adManageTagGroup.getDefinition()) ? adManageTagGroup.getDefinition() : "");
                builder.setRemark(StringUtils.isNotBlank(adManageTagGroup.getRemark()) ? adManageTagGroup.getRemark() : "");
                builder.setPermissionType(adManageTagGroup.getPermissionType());
                if (!AdTagPermissionTypeEnum.ALL.getCode().equals(adManageTagGroup.getPermissionType())) {
                    List<Integer> uidList = tagGroupUserDao.listUserIdByGroupId(puid, id);
                    builder.addAllUidList(uidList);
                }
                if (adManageTagGroup.getCreateId().equals(puid)) {
                    builder.setGroupCreateName("管理员");
                } else {
                    User user = userDao.getByIdAndPuid(adManageTagGroup.getCreateId(), puid);
                    builder.setGroupCreateName(user.getNickname());
                }
            }
        } else {
            AdManageTag adManageTag = tagDao.getByPuidAndId(puid, id);
            if (adManageTag != null) {
                builder.setId(adManageTag.getId());
                builder.setColor(adManageTag.getColor());
                builder.setName(adManageTag.getName());
                builder.setDefinition(StringUtils.isNotBlank(adManageTag.getDefinition()) ? adManageTag.getDefinition() : "");
                builder.setRemark(StringUtils.isNotBlank(adManageTag.getRemark()) ? adManageTag.getRemark() : "");
                builder.setGroupId(adManageTag.getGroupId());
                AdManageTagGroup adManageTagGroup = tagGroupDao.getByPuidAndId(puid, adManageTag.getGroupId());
                if (adManageTagGroup.getCreateId().equals(puid)) {
                    builder.setGroupCreateName("管理员");
                } else {
                    User user = userDao.getByIdAndPuid(adManageTagGroup.getCreateId(), puid);
                    builder.setGroupCreateName(user.getNickname());
                }
            }
        }
        return builder.build();
    }

}
