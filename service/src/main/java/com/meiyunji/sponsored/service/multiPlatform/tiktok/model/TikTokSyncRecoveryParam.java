package com.meiyunji.sponsored.service.multiPlatform.tiktok.model;

import lombok.Data;

import java.util.List;

@Data
public class TikTokSyncRecoveryParam {

    // 外层有puid和advertiserId

    //    重试活动详情 通过puid和advertiserId查询某个店铺再获取token 再通过campaignId获取详情
    private String campaignId;

    //    重试活动分页获取
    /*
    通过puid和advertiserId查询某个店铺再获取token
    查询这些storeIds对应的shopId
    从当前记录的 page 开始查询
     */
    private Integer page;
    private List<String> storeIds;

    // 重试报告分页
    /*
    通过店铺获取token
    从当前页重试
     */
    private Integer shopId;
    private String storeId;
    private String startDate;
    private String endDate;

    private String primaryStatus;


}
