package com.meiyunji.sponsored.service.cpc.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by xp on 2021/4/13.
 * 关键词列表页显示的字段
 */
@Data
@ApiModel
public class KeywordsPageVo extends CpcCommPageVo {

    @ApiModelProperty("类型 sp sb sd")
    private String type;
    private Long id;
    @ApiModelProperty("店铺ID")
    private Integer shopId;
    private Long dxmAdGroupId;
    @ApiModelProperty("广告活动ID")
    private String campaignId;
    @ApiModelProperty("广告活动名称")
    private String campaignName;
    @ApiModelProperty("广告活动状态")
    private String campaignState;
    @ApiModelProperty("广告活动投放类型")
    private String campaignTargetingType;
    @ApiModelProperty("广告组ID")
    private String adGroupId;
    @ApiModelProperty("广告组名称")
    private String adGroupName;
    @ApiModelProperty("广告组状态")
    private String adGroupState;
    @ApiModelProperty("sb广告类型 product keyword")
    private String groupType;
    @ApiModelProperty("关键词ID")
    private String keywordId;
    @ApiModelProperty("状态")
    private String state;
    @ApiModelProperty("关键词")
    private String keywordText;
    //关键词翻译
    private String keywordTextCn;
    @ApiModelProperty("匹配方式")
    private String matchType;
    @ApiModelProperty("竞价")
    private String bid;
    @ApiModelProperty("建议竞价")
    private String suggestBid;
    @ApiModelProperty("建议竞价范围开始")
    private String rangeStart;
    @ApiModelProperty("建议竞价范围结束")
    private String rangeEnd;
    @ApiModelProperty("是否开启分时调价")
    private Integer isPricing;
    @ApiModelProperty("分时调价任务状态")
    private Integer pricingState;
    @ApiModelProperty("站点")
    private String marketplaceId;
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty("广告组合ID")
    private String portfolioId;

    @ApiModelProperty("广告组合名称")
    private String portfolioName;

    @ApiModelProperty("广告组合隐藏状态 1:隐藏  0:可见")
    private Integer isHidden;

    @ApiModelProperty("二级状态")
    private String servingStatus;

    @ApiModelProperty("二级状态描述")
    private String servingStatusDec;

    @ApiModelProperty("二级状态名称")
    private String servingStatusName;

    @ApiModelProperty("标签")
    private List<AdTag> adTags;

    @ApiModelProperty("关键词实时排名参数")
    private KeywordsRankParamVo keywordsRankParamVo;

    @ApiModelProperty("关键词广告排名")
    private String advRank;

    @ApiModelProperty("搜索词首页首位曝光率 最小值~最大值")
    private String topImpressionShare;
    private Integer isStateBidding;
    private Integer pricingStateBidding;
    private Boolean sbType;
    @ApiModelProperty("默认竞价")
    private String defaultBid;
    private String adFormat;
    private String adGoal;
    private String costType;
    @ApiModelProperty("广告策略集合")
    private List<AdStrategyVo> strategyList;
}
