package com.meiyunji.sponsored.service.multiPlatform.walmart.service;

import com.meiyunji.sponsored.common.exception.ServiceException;
import com.walmart.oms.advertiser.base.dto.CreateNewAdGroupDTO;
import com.walmart.oms.advertiser.base.dto.UpdateExistingAdGroupDTO;
import com.walmart.oms.advertiser.base.vo.AdGroupsResponseVO;

import java.util.List;

/**
 * @author: ys
 * @date: 2025/3/25 10:39
 * @describe:
 */
public interface IWalmartAdvertisingGroupClientService {
    AdGroupsResponseVO createNewAdGroup(List<CreateNewAdGroupDTO> groupDto) throws ServiceException;
    void updateAdGroup(List<UpdateExistingAdGroupDTO> groupDto) throws ServiceException;
}
