package com.meiyunji.sponsored.service.export.handler;

import com.amazon.advertising.sd.constant.TacticEnum;
import com.amazon.advertising.spV3.enumeration.SpV3ExpressionEnum;
import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.export.TargetingDataResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.constants.BrandMessageConstants;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdTargetStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.dao.IAdMarkupTagDao;
import com.meiyunji.sponsored.service.cpc.dao.IAdTagDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.cpc.po.TargetTypeEnum;
import com.meiyunji.sponsored.service.cpc.service2.ICpcCommonService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcTargetingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdMarkupTagVo;
import com.meiyunji.sponsored.service.cpc.vo.TargetingPageParam;
import com.meiyunji.sponsored.service.cpc.vo.TargetingPageVo;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.export.util.ExportStringUtil;
import com.meiyunji.sponsored.service.util.ReportParamUtil;
import com.meiyunji.sponsored.service.vo.TargetingDetailsVo;
import com.meiyunji.sponsored.service.vo.TargetingSbDetailsVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-07-18  16:40
 */
@Service(AdManagePageExportTaskConstant.TARGET)
@Slf4j
public class TargetPageExportTaskHandler implements AdManagePageExportTaskHandler {
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private ICpcCommonService cpcCommonService;
    @Autowired
    private ICpcTargetingService cpcTargetingService;
    @Autowired
    private IAdMarkupTagDao adMarkupTagDao;
    @Autowired
    private IAdTagDao adTagDao;

    @Override
    public void export(AdManagePageExportTask task) {
        TargetingPageParam param = JSONUtil.jsonToObject(task.getParam(), TargetingPageParam.class);
        if (param == null) {
            log.error(String.format("target export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        ShopAuth shop = shopAuthDao.getScAndVcById(param.getShopId());
        // 取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getShopId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);
        param.setMarketplaceId(shop.getMarketplaceId());
//        this.targetingPageParamDateFormat(param);
        List<TargetingPageVo> voList = cpcCommonService.getExportAllTargetData(shop, param.getPuid(), param);
        //填充标签
        this.fillAdTagData(param.getPuid(), param.getShopId(), param, voList);
        // 填充广告策略标签
        cpcTargetingService.fillAdStrategy(param, voList);
        if (CollectionUtils.isEmpty(voList)) {
            //修改任务状态
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            //修改状态，前端收到后转圈效果停止
            stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }
        List<TargetingDataResponse.TargetingPageVo> list = voList.stream().filter(Objects::nonNull).map(item -> buildGrpcVo(param, item)).collect(Collectors.toList());
        //文件名称
        String fileName = shop.getName() + "_投放" + "_" + param.getStartDate() + "_" + param.getEndDate();

        List<String> urlList;

        //集合分片
        List<List<TargetingDataResponse.TargetingPageVo>> partition = Lists.partition(list, Constants.EXPORT_MAX_SIZE);
        if (StringUtils.isNotBlank(param.getExportSortField()) && Objects.nonNull(param.getFreezeNum())) {
            urlList = newExport(partition, param, fileName, shop);
        } else {
            urlList = oldExport(partition, param, fileName);
        }
        //修改任务状态
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(param.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }

    private List<String> newExport(List<List<TargetingDataResponse.TargetingPageVo>> partition, TargetingPageParam param, String fileName, ShopAuth shop) {
        //存储文件路径urlList
        List<String> downloadUrl = new ArrayList<>();

        //组装需要排除的字段
        List<String> excludeFileds;
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            excludeFileds = Lists.newArrayList("viewImpressions", "vcpm", "ordersNewToBrandFTD", "orderRateNewToBrandFTD", "salesNewToBrandFTD", "salesRateNewToBrandFTD", "ordersNewToBrandPercentageFTD",
                "newToBrandDetailPageViews", "addToCart", "addToCartRate", "ecpAddToCart", "video5SecondViews", "video5SecondViewRate", "videoFirstQuartileViews", "videoMidpointViews", "videoThirdQuartileViews",
                "videoCompleteViews", "videoUnmutes", "viewabilityRate", "viewClickThroughRate", "brandedSearches", "detailPageViews");

        } else if (Constants.SB.equalsIgnoreCase(param.getType())) {
            excludeFileds = Lists.newArrayList("selectType", "vcpm", "adSelfSaleNum", "adOtherSaleNum", "newToBrandDetailPageViews", "addToCart", "addToCartRate", "ecpAddToCart", "detailPageViews");
        } else if (Constants.SD.equalsIgnoreCase(param.getType())) {
            excludeFileds = Lists.newArrayList("selectType", "adSelfSaleNum", "adOtherSaleNum", "topImpressionShare", "video5SecondViews", "video5SecondViewRate");
        } else {
            excludeFileds = Collections.emptyList();
        }

        //自定义排序：根据param的exportSortField，中的字段作为表格中的表头字段顺序导出
        List<String> sortFields = Arrays.asList(param.getExportSortField().split(","));
        //排除字段
        sortFields = sortFields.stream().filter(item -> !excludeFileds.contains(item)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sortFields)) {
            return downloadUrl;
        }
        // 默认导出广告策略标签
        sortFields.add("adStrategyTag");
        //导出
        for (List<TargetingDataResponse.TargetingPageVo> partitionList : partition) {
            List<TargetingDetailsVo> targetingDetailsVoList = new LinkedList<>();
            for (TargetingDataResponse.TargetingPageVo tpVo : partitionList) {
                targetingDetailsVoList.add(this.buildExportVo(param, tpVo));
            }
            if (CollectionUtils.isEmpty(targetingDetailsVoList)) {
                continue;
            }

            String url = customFieldSortExport(targetingDetailsVoList, sortFields, param.getFreezeNum(), shop, fileName);
            downloadUrl.add(url);
        }
        return downloadUrl;
    }

    private String customFieldSortExport(List<TargetingDetailsVo> targetingDetailsVoList, List<String> sortFields, Integer freezeNum, ShopAuth shop, String fileName) {
        //站点币种
        String currency = AmznEndpoint.getByMarketplaceId(shop.getMarketplaceId()).getCurrencyCode().value();

        //Excel样式builder
        WriteHandlerBuild build = new WriteHandlerBuild()
            .rate();
        //冻结前n列前1行
        if (freezeNum != null) {
            build.freezeRowAndCol(freezeNum, 1);
        }
        build.noModleHandler(getCurrencyIndex(sortFields));

        //构建行
        List<List<Object>> rows = new ArrayList<>(targetingDetailsVoList.size());
        for (TargetingDetailsVo cpVo : targetingDetailsVoList) {
            rows.add(buildExportSingleRow(cpVo, sortFields, currency));
        }
        //构建表头
        List<String> headNames = new ArrayList<>(sortFields.size());
        for (String sortField : sortFields) {
            AdvertisingTargetExportFieldEnum fieldEnum = AdvertisingTargetExportFieldEnum.fromPoParamKey(sortField);
            if (fieldEnum == null) {
                log.error("sortFields 包含非法字符，导出阻止，返回空, sortFields:{}", sortFields);
                throw new RuntimeException("sortFields 包含非法字符，导出阻止，返回空, sortFields:" + sortFields);
            }
            headNames.add(fieldEnum.getTableColName());
        }
        //导出
        return excelService.exportByCustomColSort(shop.getPuid(), headNames, rows, fileName + "(" + (0) + ")", build);
    }

    private List<Object> buildExportSingleRow(TargetingDetailsVo cpVo, List<String> sortFields, String currency) {
        List<Object> cols = new ArrayList<>(AdvertisingTargetExportFieldEnum.values().length);
        for (String fieldName : sortFields) {
            AdvertisingTargetExportFieldEnum fieldEnum = AdvertisingTargetExportFieldEnum.fromPoParamKey(fieldName);
            if (fieldEnum == null) {
                return Collections.emptyList();
            }
            Object value = getObjectByField(cpVo, fieldEnum, currency);
            cols.add(value);
        }

        return cols;
    }

    private List<Integer> getCurrencyIndex(List<String> sortFields) {
        List<Integer> currencyIndex = new ArrayList<>();
        for (int i = 0; i < sortFields.size(); i++) {
            AdvertisingTargetExportFieldEnum keywordExportFieldEnum = AdvertisingTargetExportFieldEnum.fromPoParamKey(sortFields.get(i));
            //出现这种情况就是有问题，暂时不考虑
            if (keywordExportFieldEnum == null) {
                return Collections.emptyList();
            }
            if (keywordExportFieldEnum.getCurrencyStyle()) {
                currencyIndex.add(i);
            }
        }

        return currencyIndex;
    }

    private Object getObjectByField(TargetingDetailsVo keyVo, AdvertisingTargetExportFieldEnum fieldEnum, String currency) {
        Object value = null;
        switch (fieldEnum) {
            case POSITIONING:
                value = keyVo.getTarget();
                break;
            case STATE:
                value = keyVo.getState();
                break;
            case CAMPAIGN_STATE:
                value = keyVo.getServingStatusName();
                break;
            case SELECT_TYPE:
                value = keyVo.getSelectType();
                break;
            case TYPE:
                value = keyVo.getType();
                break;
            case TARGETING_TYPE:
                value = keyVo.getCampaignTargetingType();
                break;
            case GROUP_NAME:
                value = keyVo.getAdvertisingGroup();
                break;
            case CAMPAIGN_NAME:
                value = keyVo.getAdvertisingActivities();
                break;
            case PORTFOLIO_NAME:
                value = keyVo.getPortfolioName();
                break;
            case SUGGEST_BID:
                value = keyVo.getSuggestBid();
                break;
            case SUGGEST_BID_RANGE:
                value = keyVo.getSuggestBidScope();
                break;
            case BID:
                value = keyVo.getBid();
                break;
            case AD_COST:
                value = keyVo.getAdCost();
                break;
            case AD_COST_PERCENTAGE:
                value = keyVo.getAdCostPercentage();
                break;
            case IMPRESSIONS:
                value = keyVo.getImpressions();
                break;
            case TOP_IMPRESSION_SHARE:
                value = keyVo.getTopImpressionShare();
                break;
            case VIEW_IMPRESSIONS:
                value = keyVo.getViewImpressions();
                break;
            case CLICKS:
                value = keyVo.getClicks();
                break;
            case CPA:
                value = keyVo.getCpa();
                break;
            case AD_COST_PER_CLICK:
                value = keyVo.getAdCostPerClick();
                break;
            case VCPM:
                value = keyVo.getVcpm();
                break;
            case CTR:
                value = keyVo.getCtr();
                break;
            case CVR:
                value = keyVo.getCvr();
                break;
            case ACOS:
                value = keyVo.getAcos();
                break;
            case ROAS:
                value = keyVo.getRoas();
                break;
            case ACOTS:
                value = keyVo.getAcots();
                break;
            case ASOTS:
                value = keyVo.getAsots();
                break;
            case ADVERTISING_UNIT_PRICE:
                value = keyVo.getAdvertisingUnitPrice();
                break;
            case AD_ORDER_NUM:
                value = keyVo.getAdOrderNum();
                break;
            case AD_ORDER_NUM_PERCENTAGE:
                value = keyVo.getAdOrderNumPercentage();
                break;
            case SELF_AD_ORDER_NUM:
                value = keyVo.getAdSaleNum();
                break;
            case OTHER_AD_ORDER_NUM:
                value = keyVo.getAdOtherOrderNum();
                break;
            case AD_SALE:
                value = keyVo.getAdSale();
                break;
            case AD_SALE_PERCENTAGE:
                value = keyVo.getAdSalePercentage();
                break;
            case AD_SELF_SALE:
                value = keyVo.getAdSales();
                break;
            case AD_OTHER_SALES:
                value = keyVo.getAdOtherSales();
                break;
            case AD_SALE_NUM:
                value = keyVo.getOrderNum();
                break;
            case AD_SALE_NUM_PERCENTAGE:
                value = keyVo.getOrderNumPercentage();
                break;
            case AD_SELF_SALE_NUM:
                value = keyVo.getAdSelfSaleNum();
                break;
            case AD_OTHER_SALE_NUM:
                value = keyVo.getAdOtherSaleNum();
                break;
            case ORDERS_NEW_TO_BRAND_FTD:
                value = keyVo.getOrdersNewToBrandFTD();
                break;
            case ORDER_RATE_NEW_TO_BRAND_FTD:
                value = keyVo.getOrderRateNewToBrandFTD();
                break;
            case SALES_NEW_TO_BRAND_FTD:
                value = keyVo.getSalesNewToBrandFTD();
                break;
            case SALES_RATE_NEW_TO_BRAND_FTD:
                value = keyVo.getSalesRateNewToBrandFTD();
                break;
            case ORDERS_NEW_TO_BRAND_PERCENTAGE_FTD:
                value = keyVo.getOrdersNewToBrandPercentageFTD();
                break;
            case NEW_TO_BRAND_DETAIL_PAGE_VIEWS:
                value = keyVo.getNewToBrandDetailPageViews();
                break;
            case ADD_TO_CART:
                value = keyVo.getAddToCart();
                break;
            case ADD_TO_CART_RATE:
                value = keyVo.getAddToCartRate();
                break;
            case ECP_ADD_TO_CART:
                value = keyVo.getEcpAddToCart();
                break;
            case VIDEO_5_SECOND_VIEWS:
                value = keyVo.getVideo5SecondViews();
                break;
            case VIDEO_5_SECOND_VIEW_RATE:
                value = keyVo.getVideo5SecondViewRate();
                break;
            case VIDEO_FIRST_QUARTILE_VIEWS:
                value = keyVo.getVideoFirstQuartileViews();
                break;
            case VIDEO_MIDPOINT_VIEWS:
                value = keyVo.getVideoMidpointViews();
                break;
            case VIDEO_THIRD_QUARTILE_VIEWS:
                value = keyVo.getVideoThirdQuartileViews();
                break;
            case VIDEO_COMPLETE_VIEWS:
                value = keyVo.getVideoCompleteViews();
                break;
            case VIDEO_UNMUTES:
                value = keyVo.getVideoUnmutes();
                break;
            case VIEWABILITY_RATE:
                value = keyVo.getViewabilityRate();
                break;
            case VIEW_CLICK_THROUGH_RATE:
                value = keyVo.getViewClickThroughRate();
                break;
            case BRANDED_SEARCHES:
                value = keyVo.getBrandedSearches();
                break;
            case DETAIL_PAGE_VIEWS:
                value = keyVo.getDetailPageViews();
                break;
            case AD_TAGS:
                value = keyVo.getAdTag();
                break;
            case AD_STRATEGY_TAG:
                value = keyVo.getAdStrategyTag();
                break;
            default:
        }

        return value;
    }

    private List<String> oldExport(List<List<TargetingDataResponse.TargetingPageVo>> partition, TargetingPageParam param, String fileName) {
        List<String> urlList = new ArrayList<>();
        int count = 0;
        WriteHandlerBuild build = new WriteHandlerBuild().rate();
        for (List<TargetingDataResponse.TargetingPageVo> partitionList : partition) {
            List<TargetingDetailsVo> targetingDetailsVoList = new LinkedList<>();
            for (TargetingDataResponse.TargetingPageVo tpVo : partitionList) {
                targetingDetailsVoList.add(this.buildExportVo(param, tpVo));
            }
            if (targetingDetailsVoList.size() > 0) {
                //SB不导出广告组字段
                Class clazz = TargetingDetailsVo.class;
                if (Constants.SB.equalsIgnoreCase(param.getType())) {
                    clazz = TargetingSbDetailsVo.class;
                }
                List<String> excludeFileds = Lists.newArrayList();
                if (Constants.SP.equalsIgnoreCase(param.getType())) {
                    excludeFileds = Lists.newArrayList("viewImpressions", "vcpm", "ordersNewToBrandFTD", "orderRateNewToBrandFTD", "salesNewToBrandFTD", "salesRateNewToBrandFTD", "ordersNewToBrandPercentageFTD",
                        "newToBrandDetailPageViews", "addToCart", "addToCartRate", "ecpAddToCart", "video5SecondViews", "video5SecondViewRate", "videoFirstQuartileViews", "videoMidpointViews", "videoThirdQuartileViews",
                        "videoCompleteViews", "videoUnmutes", "viewabilityRate", "viewClickThroughRate", "brandedSearches", "detailPageViews");

                }
                if (Constants.SB.equalsIgnoreCase(param.getType())) {
                    excludeFileds = Lists.newArrayList("selectType", "vcpm", "adSelfSaleNum", "adOtherSaleNum", "newToBrandDetailPageViews", "addToCart", "addToCartRate", "ecpAddToCart", "detailPageViews");
                }
                if (Constants.SD.equalsIgnoreCase(param.getType())) {
                    excludeFileds = Lists.newArrayList("selectType", "adSelfSaleNum", "adOtherSaleNum", "topImpressionShare", "video5SecondViews", "video5SecondViewRate");
                }
                build = build.currencyNew(clazz);
                urlList.add(excelService.easyExcelHandlerExport(param.getPuid(), targetingDetailsVoList, fileName + "(" + (count++) + ")", clazz, build, excludeFileds));
            }
        }
        return urlList;
    }

    private void targetingPageParamDateFormat(TargetingPageParam param) {
        //日期转换格式
        param.setStartDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        param.setEndDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getEndDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
    }

    private TargetingDetailsVo buildExportVo(TargetingPageParam param, TargetingDataResponse.TargetingPageVo tpVo) {
        TargetingDetailsVo tdVo = new TargetingDetailsVo();
        tdVo.setServingStatusName(tpVo.getServingStatusName());
        tdVo.setCampaignTargetingType(tpVo.getCampaignTargetingType());
        tdVo.setImpressions(tpVo.getImpressions().getValue());
        tdVo.setClicks(tpVo.getClicks().getValue());
        tdVo.setTopImpressionShare(ReportParamUtil.getExportTopIS(tpVo.getTopImpressionShare()));
        tdVo.setAdOrderNum(tpVo.getAdOrderNum().getValue());
        //  asin 或 类目
        if (StringUtils.isNotEmpty(tpVo.getAsin()) || StringUtils.isNotEmpty(tpVo.getCategory())) {
            if (StringUtils.isNotEmpty(tpVo.getAsin())) {
                tdVo.setTarget("ASIN：" + tpVo.getAsin());
            } else if (StringUtils.isNotEmpty(tpVo.getCategory())) {
                tdVo.setTarget("类目：" + tpVo.getCategory());
            }
        } else {
            tdVo.setTarget(tpVo.getTargetText());
        }

        if (StringUtils.isNotBlank(tpVo.getSelectType())) {
            if (SpV3ExpressionEnum.asinSameAs.getValue().equals(tpVo.getSelectType()) || SpV3ExpressionEnum.asinSameAs.getValueV3().equals(tpVo.getSelectType())) {
                tdVo.setSelectType("精准");
            } else if (SpV3ExpressionEnum.asinExpandedFrom.getValue().equals(tpVo.getSelectType()) || SpV3ExpressionEnum.asinExpandedFrom.getValueV3().equals(tpVo.getSelectType())) {
                tdVo.setSelectType("扩展");
            } else {
                tdVo.setSelectType(tpVo.getSelectType());
            }
        }

        //自动
        if (TargetingEnum.auto.getTargetingType().equalsIgnoreCase(tpVo.getType())) {
            tdVo.setTarget(AutoTargetTypeEnum.getAutoTargetValue(tpVo.getTargetText()));
        }

        // 受众投放导出时需要拼接title和targetText
        if (TargetingEnum.audience.getTargetingType().equals(param.getChosenTargetType()) && StringUtils.isNotBlank(tdVo.getTarget())) {
            if (StringUtils.isNotBlank(tpVo.getTitle())) {
                tdVo.setTarget(tpVo.getTitle().concat(" ").concat(tdVo.getTarget()));
            }
            if (StringUtils.isNotBlank(tpVo.getLookback())) {
                tdVo.setTarget(tdVo.getTarget().concat(" 回溯期：").concat(tpVo.getLookback()).concat("天"));
            }
        }

        if (TargetTypeEnum.category.name().equalsIgnoreCase(tpVo.getType())) {
            StringBuilder sb = new StringBuilder();
            if (StringUtils.isNotBlank(tpVo.getBrandName())) {
                sb.append(" 品牌：").append(tpVo.getBrandName());
            }
            if (StringUtils.isNotBlank(tpVo.getCommodityPriceRange())) {
                String range = tpVo.getCommodityPriceRange();
                if (BrandMessageConstants.DEFAULT_COMMODITY_PRICE_RANGE.equals(range)) {
                    sb.append(" 价格：").append(range);
                } else {
                    String[] ranges = tpVo.getCommodityPriceRange().split(",");
                    range = ranges.length == 2 ? ExportStringUtil.getSuggestBidScope(ranges[0], ranges[1], param.getIcon()) : ExportStringUtil.getSuggest(range, param.getIcon());
                    sb.append(" 价格：").append(range);
                }
            }
            if (StringUtils.isNotBlank(tpVo.getRating())) {
                sb.append(" 星级：").append(tpVo.getRating());
            }
            if (StringUtils.isNotBlank(tpVo.getDistribution())) {
                sb.append(" 配送：").append(tpVo.getDistribution());
            }
            tdVo.setTarget(tdVo.getTarget().concat(sb.toString()));
        }

        //广告组合
        tdVo.setPortfolioName(tpVo.getPortfolioName());
        //所属广告名称
        tdVo.setAdvertisingActivities(tpVo.getCampaignName());
        //所属广告组
        tdVo.setAdvertisingGroup(tpVo.getAdGroupName());
        //推广类型
        tdVo.setType(CampaignTypeEnum.getCampaignValue(tpVo.getAType()));
        //投放类型
        tdVo.setCampaignTargetingType(TargetingEnum.getTargetingValue(tpVo.getCampaignTargetingType()));
        //状态
        tdVo.setState(AllAdStateEnum.getStateValue(tpVo.getState()));
        //建议竞价
        if (StringUtils.isNotBlank(tpVo.getSuggestBid())) {
            tdVo.setSuggestBid(ExportStringUtil.getSuggest(tpVo.getSuggestBid(), param.getIcon()));
        }
        //建议竞价范围
        if (StringUtils.isNotBlank(tpVo.getRangeStart()) && StringUtils.isNotBlank(tpVo.getRangeEnd())) {
            tdVo.setSuggestBidScope(ExportStringUtil.getSuggestBidScope(tpVo.getRangeStart(), tpVo.getRangeEnd(), param.getIcon()));
        }

        //竞价
        tdVo.setBid(param.getCurrency() + ExportStringUtil.formatToNumber(tpVo.getBid()));
        //点击率
        tdVo.setCtr(ExportStringUtil.modifyFormat(tpVo.getCtr()));
        //订单转化率
        tdVo.setCvr(ExportStringUtil.modifyFormat(tpVo.getCvr()));
        //ACoS
        tdVo.setAcos(ExportStringUtil.modifyFormat(tpVo.getAcos()));
        //ACoTS
        tdVo.setAcots(ExportStringUtil.modifyFormat(tpVo.getAcots()));
        tdVo.setRoas(tpVo.getRoas());
        //ASoTS
        tdVo.setAsots(ExportStringUtil.modifyFormat(tpVo.getAsots()));
        //广告花费
        tdVo.setAdCost(param.getCurrency() + ExportStringUtil.formatToNumber(tpVo.getAdCost()));
        //平均点击费用(特殊处理)
        tdVo.setAdCostPerClick(param.getCurrency() + ExportStringUtil.getAdCostPerClick(tpVo.getAdCostPerClick()));
        //广告销售额
        tdVo.setAdSale(param.getCurrency() + ExportStringUtil.getAdCostPerClick(tpVo.getAdSale()));
        tdVo.setViewImpressions(tpVo.getViewImpressions().getValue());
        tdVo.setCpa(param.getCurrency() + ExportStringUtil.formatToNumber(tpVo.getCpa()));
        tdVo.setVcpm("-".equals(tpVo.getVcpm()) ? "-" : param.getCurrency() + ExportStringUtil.formatToNumber(tpVo.getVcpm()));
        tdVo.setAdSaleNum(tpVo.getAdSaleNum().getValue());
        tdVo.setAdOtherOrderNum(tpVo.getAdOtherOrderNum().getValue());
        tdVo.setAdSales(param.getCurrency() + ExportStringUtil.formatToNumber(tpVo.getAdSales()));
        tdVo.setAdOtherSales(param.getCurrency() + ExportStringUtil.formatToNumber(tpVo.getAdOtherSales()));
        tdVo.setOrderNum(tpVo.getOrderNum().getValue());
        tdVo.setAdSelfSaleNum(tpVo.getAdSelfSaleNum().getValue());
        tdVo.setAdOtherSaleNum(tpVo.getAdOtherSaleNum().getValue());
        tdVo.setOrdersNewToBrandFTD(tpVo.getOrdersNewToBrandFTD().getValue());
        tdVo.setOrderRateNewToBrandFTD(ExportStringUtil.modifyFormat(tpVo.getOrderRateNewToBrandFTD()));
        tdVo.setSalesNewToBrandFTD(param.getCurrency() + ExportStringUtil.formatToNumber(tpVo.getSalesNewToBrandFTD()));
        tdVo.setSalesRateNewToBrandFTD(ExportStringUtil.modifyFormat(tpVo.getSalesRateNewToBrandFTD()));
        tdVo.setOrdersNewToBrandPercentageFTD(ExportStringUtil.modifyFormat(tpVo.getOrdersNewToBrandPercentageFTD()));
        // 花费占比
        tdVo.setAdCostPercentage(ExportStringUtil.modifyFormat(tpVo.getAdCostPercentage()));
        // 销售额占比
        tdVo.setAdSalePercentage(ExportStringUtil.modifyFormat(tpVo.getAdSalePercentage()));
        // 订单量占比
        tdVo.setAdOrderNumPercentage(ExportStringUtil.modifyFormat(tpVo.getAdOrderNumPercentage()));
        // 销量占比
        tdVo.setOrderNumPercentage(ExportStringUtil.modifyFormat(tpVo.getOrderNumPercentage()));

        tdVo.setNewToBrandDetailPageViews(tpVo.getNewToBrandDetailPageViews());
        tdVo.setAddToCart(tpVo.getAddToCart());
        tdVo.setAddToCartRate(ExportStringUtil.modifyFormat(tpVo.getAddToCartRate()));
        tdVo.setEcpAddToCart(param.getCurrency() + ExportStringUtil.formatToNumber(tpVo.getECPAddToCart()));
        tdVo.setVideo5SecondViews(tpVo.getVideo5SecondViews());
        tdVo.setVideo5SecondViewRate(ExportStringUtil.modifyFormat(tpVo.getVideo5SecondViewRate()));
        tdVo.setVideoFirstQuartileViews(tpVo.getVideoFirstQuartileViews());
        tdVo.setVideoMidpointViews(tpVo.getVideoMidpointViews());
        tdVo.setVideoThirdQuartileViews(tpVo.getVideoThirdQuartileViews());
        tdVo.setVideoCompleteViews(tpVo.getVideoCompleteViews());
        tdVo.setVideoUnmutes(tpVo.getVideoUnmutes());
        tdVo.setViewabilityRate(ExportStringUtil.modifyFormat(tpVo.getViewabilityRate()));
        tdVo.setViewClickThroughRate(ExportStringUtil.modifyFormat(tpVo.getViewClickThroughRate()));
        tdVo.setBrandedSearches(tpVo.getBrandedSearches());
        tdVo.setDetailPageViews(tpVo.getDetailPageViews());
        tdVo.setAdvertisingUnitPrice(param.getCurrency() + ExportStringUtil.formatToNumber(tpVo.getAdvertisingUnitPrice()));
        tdVo.setAdTag(tpVo.getAdTag());
        tdVo.setAdStrategyTag(tpVo.getAdStrategyTag());
        return tdVo;
    }

    private TargetingDataResponse.TargetingPageVo buildGrpcVo(TargetingPageParam param, TargetingPageVo item) {
        TargetingDataResponse.TargetingPageVo.Builder vo = TargetingDataResponse.TargetingPageVo.newBuilder();
        if (StringUtils.isNotBlank(item.getAType())) {
            vo.setAType(item.getAType());
        }
        if (StringUtils.isNotBlank(item.getType())) {
            vo.setType(item.getType());
        }
        if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
            vo.setCampaignTargetingType(item.getCampaignTargetingType());
        }
        if (StringUtils.isNotBlank(item.getCampaignName())) {
            vo.setCampaignName(item.getCampaignName());
        }
        if (StringUtils.isNotBlank(item.getAdGroupName())) {
            vo.setAdGroupName(item.getAdGroupName());
        }
        if (StringUtils.isNotBlank(item.getTargetText())) {
            vo.setTargetText(item.getTargetText());
        }
        if (StringUtils.isNotBlank(item.getAsin())) {
            vo.setAsin(item.getAsin());
        }
        if (StringUtils.isNotBlank(item.getCategory())) {
            vo.setCategory(item.getCategory());
        }
        if (StringUtils.isNotBlank(item.getState())) {
            vo.setState(item.getState());
        }
        if (StringUtils.isNotBlank(item.getSuggestBid())) {
            vo.setSuggestBid(item.getSuggestBid());
        }
        if (StringUtils.isNotBlank(item.getRangeEnd())) {
            vo.setRangeEnd(item.getRangeEnd());
        }
        if (StringUtils.isNotBlank(item.getRangeStart())) {
            vo.setRangeStart(item.getRangeStart());
        }
        if (StringUtils.isNotBlank(item.getBid())) {
            vo.setBid(item.getBid());
        }
        if (StringUtils.isNotBlank(item.getPortfolioName())) {
            vo.setPortfolioName(item.getPortfolioName());
        }
        if (StringUtils.isNotBlank(item.getServingStatusName())) {
            vo.setServingStatusName(item.getServingStatusName());
        }
        if (item.getSelectType() != null) {
            vo.setSelectType(item.getSelectType());
        }
        if (StringUtils.isNotBlank(item.getTitle())) {
            vo.setTitle(item.getTitle());
        }
        if (StringUtils.isNotBlank(item.getLookback())) {
            vo.setLookback(item.getLookback());
        }
        this.fillDefaultBrandMessage(vo, item, !TacticEnum.T00030.name().equals(param.getChosenTargetType()));
        vo.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
        vo.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
        vo.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
        vo.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
        vo.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
        vo.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
        vo.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
        vo.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
        vo.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
        vo.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
        vo.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
        vo.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
        /**
         * TODO 广告报告重构
         */
        //可见展示次数
        vo.setViewImpressions(Int32Value.of(Optional.ofNullable(item.getViewImpressions()).orElse(0)));
        //每笔订单花费
        vo.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
        //vcpm
        if(SBCampaignCostTypeEnum.VCPM.getCode().equals(item.getCostType())){
            vo.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0");
        }else{
            vo.setVcpm("-");
        }
        //本广告产品订单量
        vo.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
        //其他产品广告订单量
        vo.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
        //本广告产品销售额
        vo.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
        //其他产品广告销售额
        vo.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
        //广告销量
        vo.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
        //本广告产品销量
        vo.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
        //其他产品广告销量
        vo.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));
        //“品牌新买家”订单量
        vo.setOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0)));
        //“品牌新买家”订单百分比
        vo.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
        //“品牌新买家”销售额
        vo.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
        //“品牌新买家”销售额百分比
        vo.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
        //“品牌新买家”订单转化率
        vo.setOrdersNewToBrandPercentageFTD(StringUtils.isNotBlank(item.getOrdersNewToBrandPercentageFTD()) ? item.getOrdersNewToBrandPercentageFTD() : "0");
        // 花费占比
        vo.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
        // 销售额占比
        vo.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
        // 订单量占比
        vo.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
        // 销量占比
        vo.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");
        vo.setTopImpressionShare(Optional.ofNullable(item.getTopImpressionShare()).orElse(""));

        vo.setNewToBrandDetailPageViews(Optional.ofNullable(item.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
        vo.setAddToCart(Optional.ofNullable(item.getAddToCart()).map(String::valueOf).orElse("0"));
        vo.setAddToCartRate(Optional.ofNullable(item.getAddToCartRate()).map(String::valueOf).orElse("0"));
        vo.setECPAddToCart(Optional.ofNullable(item.getECPAddToCart()).map(String::valueOf).orElse("0"));
        vo.setVideo5SecondViews(Optional.ofNullable(item.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
        vo.setVideo5SecondViewRate(Optional.ofNullable(item.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
        vo.setVideoFirstQuartileViews(Optional.ofNullable(item.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
        vo.setVideoMidpointViews(Optional.ofNullable(item.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
        vo.setVideoThirdQuartileViews(Optional.ofNullable(item.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
        vo.setVideoCompleteViews(Optional.ofNullable(item.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
        vo.setVideoUnmutes(Optional.ofNullable(item.getVideoUnmutes()).map(String::valueOf).orElse("0"));
        vo.setViewabilityRate(Optional.ofNullable(item.getViewabilityRate()).map(String::valueOf).orElse("0"));
        vo.setViewClickThroughRate(Optional.ofNullable(item.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
        vo.setBrandedSearches(Optional.ofNullable(item.getBrandedSearches()).map(String::valueOf).orElse("0"));
        vo.setDetailPageViews(Optional.ofNullable(item.getDetailPageViews()).map(String::valueOf).orElse("0"));
        vo.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
        //处理标签，取出标签名称进行导出
        String adTagName = "";
        if (CollectionUtils.isNotEmpty(item.getAdTags())) {
            List<String> adTag = item.getAdTags().stream().map(AdTag::getName).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(adTag) && adTag.size() > 1) {
                adTagName = String.join(",", adTag);
                vo.setAdTag(adTagName);
            } else {
                adTagName = String.join("", adTag);
                vo.setAdTag(Optional.of(adTagName).orElse(""));
            }
        }
        vo.setAdTag(adTagName);
        // 处理广告策略标签，取出标签名称进行导出
        if (CollectionUtils.isNotEmpty(item.getStrategyList())) {
            List<String> adStrategyTag = item.getStrategyList().stream().map(it-> AdTargetStrategyTypeEnum.getEnumByCode(it.getAdStrategyType()).getDesc()).collect(Collectors.toList());
            vo.setAdStrategyTag(String.join(",", adStrategyTag));
        }else{
            vo.setAdStrategyTag("");
        }
        return vo.build();
    }

    /**
     * 填充 品牌细节 默认信息
     */
    private void fillDefaultBrandMessage(TargetingDataResponse.TargetingPageVo.Builder voBuilder, TargetingPageVo item, boolean fillDefault) {
        if (TargetTypeEnum.category.name().equalsIgnoreCase(item.getType())) {
            String brandName = StringUtils.isNotBlank(item.getBrandName()) ? item.getBrandName() : fillDefault ? BrandMessageConstants.DEFAULT_BRAND_NAME : null;
            String commodityPriceRange = StringUtils.isNotBlank(item.getCommodityPriceRange()) ? item.getCommodityPriceRange() : fillDefault ? BrandMessageConstants.DEFAULT_COMMODITY_PRICE_RANGE : null;
            String rating = StringUtils.isNotBlank(item.getRating()) ? item.getRating() : fillDefault ? BrandMessageConstants.DEFAULT_RATING : null;
            String distribution = StringUtils.isNotBlank(item.getDistribution()) ? item.getDistribution() : fillDefault ? BrandMessageConstants.DEFAULT_DISTRIBUTION : null;

            if (StringUtils.isNotBlank(brandName)) {
                voBuilder.setBrandName(brandName);
            }
            if (StringUtils.isNotBlank(commodityPriceRange)) {
                voBuilder.setCommodityPriceRange(commodityPriceRange);
            }
            if (StringUtils.isNotBlank(rating)) {
                voBuilder.setRating(rating);
            }
            if (StringUtils.isNotBlank(distribution)) {
                voBuilder.setDistribution(distribution);
            }
        }
        if (StringUtils.isNotBlank(item.getLookback())) {
            voBuilder.setLookback(item.getLookback());
        }
    }

    private void fillAdTagData(Integer puid, Integer shopId, TargetingPageParam param, List<TargetingPageVo> rows) {
        if (CollectionUtils.isEmpty(rows)) {
            return;
        }
        List<String> relationIds = rows.stream().map(TargetingPageVo::getTargetId).distinct().collect(Collectors.toList());
        List<AdMarkupTagVo> relationVos = adMarkupTagDao.getRelationVos(puid, shopId, AdTagTypeEnum.TARGET.getType(), param.getType(), AdMarkupTargetTypeEnum.TARGET.getType(), null, relationIds);
        if (CollectionUtils.isEmpty(relationVos)) {
            return;
        }
        List<Long> collect = relationVos.stream().map(AdMarkupTagVo::getTagIds).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        List<AdTag> byLongIdList = adTagDao.getListByLongIdList(puid, collect);
        if (CollectionUtils.isEmpty(byLongIdList)) {
            return;
        }
        Map<Long, AdTag> adTagMap = byLongIdList.stream().collect(Collectors.toMap(AdTag::getId, e -> e, (e1, e2) -> e2));
        Map<String, AdMarkupTagVo> adMarkupTagVoMap = relationVos.stream().collect(Collectors.toMap(AdMarkupTagVo::getRelationId, e -> e, (e1, e2) -> e2));
        for (TargetingPageVo vo : rows) {
            AdMarkupTagVo adMarkupTagVo = adMarkupTagVoMap.get(vo.getTargetId());
            if (adMarkupTagVo == null) {
                continue;
            }
            List<Long> tagIds = adMarkupTagVo.getTagIds();
            if (tagIds == null) {
                continue;
            }
            List<AdTag> collect1 = tagIds.stream().map(e -> adTagMap.get(e)).collect(Collectors.toList());
            vo.setAdTags(collect1);
        }
    }
}
