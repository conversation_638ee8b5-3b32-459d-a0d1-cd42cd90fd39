package com.meiyunji.sponsored.service.cpc.service2.impl;

import com.google.api.client.util.Maps;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeAggregateDataRpcVo;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeChartRpcVo;
import com.meiyunji.sponsored.rpc.adCommon.AllPortfolioAggregateDataListResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllPortfolioDataListResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.impl.AdChartDataProcess;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdPortfolioService;
import com.meiyunji.sponsored.service.cpc.service2.ICpcPortfolioService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.enums.SearchDataTypeEnum;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTemplateDao;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatus;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.cpc.util.Constants.EXPORT_MAX_SIZE;

/**
 * <AUTHOR>
 * @date 2022/08/18
 */
@Service
@Slf4j
public class CpcPortfolioServiceImpl implements ICpcPortfolioService {

    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private IAmazonAdCampaignAllDao campaignAllDao;
    @Autowired
    private IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao;
    @Autowired
    private IAmazonAdCampaignAllDorisDao amazonAdCampaignAllDorisDao;
    @Autowired
    private AdChartDataProcess adChartDataProcess;
    @Autowired
    private IAmazonAdPortfolioService amazonAdPortfolioService;
    @Autowired
    private AdvertiseStrategyStatusDao advertiseStrategyStatusDao;
    @Autowired
    private AdvertiseStrategyTemplateDao advertiseStrategyTemplateDao;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Override
    public AllPortfolioDataListResponse.PortfolioHomeVo getAllPortfolioDataList(Integer puid, PortfolioPageParam param) {
        String uuid = UUID.randomUUID().toString();
        log.info("广告管理 {} --广告组合接口调用-参数 {}", uuid, JSONUtil.objectToJson(param));
        long t1 = Instant.now().toEpochMilli();
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        log.info("广告管理 {} --广告组合接口调用-获取门店信息- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t1);
        //取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);
        log.info("广告管理 {} --广告组合接口调用-获取店铺销售额- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t1);

        // 分页处理
        Page<PortfolioPageVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());

        boolean verifyDorisPage = dynamicRefreshConfiguration.verifyDorisPage(puid, dynamicRefreshConfiguration.getDorisPagePortfolio());
        if (verifyDorisPage) {
            getDorisPortfolioVoList(puid, param, voPage, false);
        } else {
            getPortfolioVoList(puid, param, voPage, false);
        }

        List<PortfolioPageVo> rows = voPage.getRows();


        AllPortfolioDataListResponse.PortfolioHomeVo.Builder builder = AllPortfolioDataListResponse.PortfolioHomeVo.newBuilder();
        AllPortfolioDataListResponse.PortfolioHomeVo.Page.Builder pageBuilder = AllPortfolioDataListResponse.PortfolioHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(voPage.getPageNo());
        pageBuilder.setPageSize(voPage.getPageSize());
        pageBuilder.setTotalPage(voPage.getTotalPage());
        pageBuilder.setTotalSize(voPage.getTotalSize());
        if (CollectionUtils.isNotEmpty(rows)) {
            //填充环比数据
            Map<String, PortfolioPageVo> comparePortfolioMap = null;
            if (param.getIsCompare() && CollectionUtils.isNotEmpty(rows)) {
                //查询对比数据
                param.setStartDate(param.getCompareStartDate());
                param.setEndDate(param.getCompareEndDate());
                List<String> portfolioIds = rows.stream().map(PortfolioPageVo::getPortfolioId).collect(Collectors.toList());
                param.setPortfolioIds(portfolioIds);

                //取店铺销售额
                BigDecimal shopSalesByDateCompare = cpcShopDataService.getShopSalesByDate(shopAuth.getId(),
                        param.getCompareStartDate(), param.getCompareEndDate());
                if (shopSalesByDateCompare == null) {
                    shopSalesByDateCompare = BigDecimal.ZERO;
                }
                param.setShopSales(shopSalesByDateCompare);

                List<PortfolioPageVo> portfolioPageVoList;
                if (verifyDorisPage) {
                    portfolioPageVoList = amazonAdPortfolioService.getDorisPortfolioVoList(puid, param);
                } else {
                    portfolioPageVoList = amazonAdPortfolioService.getPortfolioPageVoList(puid, param);
                }
                comparePortfolioMap = portfolioPageVoList.stream().collect(Collectors.toMap(PortfolioPageVo::getPortfolioId, Function.identity(), (a, b) -> a));
            }

            Map<String, PortfolioPageVo> finalComparePortfolioMap = comparePortfolioMap;
            Map<String, AdvertiseStrategyStatus> advertiseStrategyStatusMap = Maps.newHashMap();
            List<String> portfolioIds = rows.stream().map(PortfolioPageVo::getPortfolioId).distinct().collect(Collectors.toList());
            List<AdvertiseStrategyStatus> advertiseStrategyStatusList = advertiseStrategyStatusDao.getByItemIds(puid, shopAuth.getId(), "PORTFOLIO", portfolioIds);
            if (CollectionUtils.isNotEmpty(advertiseStrategyStatusList)) {
                advertiseStrategyStatusMap = advertiseStrategyStatusList.stream().collect(Collectors.toMap(AdvertiseStrategyStatus::getItemId, Function.identity(), (a, b) -> a));
            }
            Map<String, AdvertiseStrategyStatus> finalAdvertiseStrategyStatusMap = advertiseStrategyStatusMap;
            List<AllPortfolioDataListResponse.PortfolioHomeVo.Page.PortfolioPageVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                AllPortfolioDataListResponse.PortfolioHomeVo.Page.PortfolioPageVo.Builder voBuilder = AllPortfolioDataListResponse.PortfolioHomeVo.Page.PortfolioPageVo.newBuilder();
                voBuilder.setId(item.getId());
                voBuilder.setShopId(item.getShopId());
                voBuilder.setPortfolioId(item.getPortfolioId());
                voBuilder.setIsHidden(item.getIsHidden());

                if (StringUtils.isNotBlank(item.getMarketplaceId())) {
                    voBuilder.setMarketplaceId(item.getMarketplaceId());
                }
                voBuilder.setPortfolioName(StringUtils.isNotBlank(item.getName()) ? item.getName() : "广告组合未命名");
                voBuilder.setState(StringUtils.isNotBlank(item.getState()) ? item.getState() : "enabled");
                if (StringUtils.isNotBlank(item.getServingStatus())) {
                    voBuilder.setServingStatus(item.getServingStatus());
                }
                if (StringUtils.isNotBlank(item.getServingStatusDec())) {
                    voBuilder.setServingStatusDec(item.getServingStatusDec());
                }
                if (StringUtils.isNotBlank(item.getServingStatusName())) {
                    voBuilder.setServingStatusName(item.getServingStatusName());
                }
                if (MapUtils.isNotEmpty(finalAdvertiseStrategyStatusMap) && finalAdvertiseStrategyStatusMap.containsKey(item.getPortfolioId())) {
                    AdvertiseStrategyTemplate advertiseStrategyTemplate = advertiseStrategyTemplateDao.
                            selectByPrimaryKey(puid, finalAdvertiseStrategyStatusMap.get(item.getPortfolioId()).getTemplateId());
                    if (advertiseStrategyTemplate != null) {
                        voBuilder.setTemplateName(advertiseStrategyTemplate.getTemplateName());
                    }
                }
                voBuilder.setPolicy(StringUtils.isNotBlank(item.getPolicy()) ? item.getPolicy() : "noBudget");
                voBuilder.setAmount(String.valueOf(Optional.ofNullable(item.getAmount()).orElse(0.00)));
                voBuilder.setBudgetStartDate(StringUtils.isNotBlank(item.getBudgetStartDate()) ? item.getBudgetStartDate() : "-");
                voBuilder.setBudgetEndDate(StringUtils.isNotBlank(item.getBudgetEndDate()) ? item.getBudgetEndDate() : "-");
                voBuilder.setCampaignNumber(Optional.ofNullable(item.getCampaignNumber()).orElse(0));
                voBuilder.setImpressions(Optional.ofNullable(item.getImpressions()).orElse(0));
                voBuilder.setClicks(Optional.ofNullable(item.getClicks()).orElse(0));
                voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                voBuilder.setAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
                voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
                voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                voBuilder.setIsAmountPricing(item.getIsAmountPricing());
                voBuilder.setPricingAmountState(item.getPricingAmountState());

                /**
                 * TODO 广告报告重构
                 * sd广告vcpm类型报告特殊字段。
                 */
                //每笔订单花费
                voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                //广告销量
                voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));

                // 花费占比
                voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                // 销售额占比
                voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                // 订单量占比
                voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                // 销量占比
                voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");
                // 广告笔单价 广告销售额÷广告订单量×100%
                voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                //填充环比数据
                if (MapUtils.isNotEmpty(finalComparePortfolioMap)) {
                    if (finalComparePortfolioMap.containsKey(item.getPortfolioId())) {
                        PortfolioPageVo compareItem = finalComparePortfolioMap.get(item.getPortfolioId());

                        voBuilder.setCompareImpressions(Optional.ofNullable(compareItem.getImpressions()).orElse(0));
                        //曝光环比值
                        int impressionDiff = voBuilder.getImpressions() - voBuilder.getCompareImpressions();
                        voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions() == 0 ? "-" :
                                new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareClicks(Optional.ofNullable(compareItem.getClicks()).orElse(0));
                        //点击量环比值
                        int clicksDiff = voBuilder.getClicks() - voBuilder.getCompareClicks();
                        voBuilder.setCompareClicksRate(voBuilder.getCompareClicks() == 0 ? "-" :
                                new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
                        //ctr环比值
                        BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
                        voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
                        //cvr环比值
                        BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
                        voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
                        //Acos环比值
                        BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
                        voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
                        //Acots环比值
                        BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
                        voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
                        //Asots环比值
                        BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
                        voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNum(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0));
                        //AdOrderNum环比值
                        int adOrderNumDiff = voBuilder.getAdOrderNum() - voBuilder.getCompareAdOrderNum();
                        voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum() == 0 ? "-" :
                                new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
                        //AdCost环比值
                        BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
                        voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
                        //AdCostPerClick环比值
                        BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
                        voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
                        //AdSale环比值
                        BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
                        voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
                        //Roas环比值
                        BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
                        voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                        //Cpa环比值
                        BigDecimal CpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                        voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                CpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getOrderNum()).orElse(0)));
                        //OrderNum比值
                        int orderNumDiff = voBuilder.getOrderNum().getValue() - voBuilder.getCompareOrderNum().getValue();
                        voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPercentage(StringUtils.isNotBlank(compareItem.getAdCostPercentage()) ?
                                compareItem.getAdCostPercentage() : "0");
                        //AdCostPercentage环比值
                        BigDecimal adCostPercentageDiff = new BigDecimal(voBuilder.getAdCostPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdCostPercentage()));
                        voBuilder.setCompareAdCostPercentageRate(new BigDecimal(voBuilder.getCompareAdCostPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPercentage()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSalePercentage(StringUtils.isNotBlank(compareItem.getAdSalePercentage()) ? compareItem.getAdSalePercentage() : "0");
                        //AdSalePercentage环比值
                        BigDecimal adSalePercentageDiff = new BigDecimal(voBuilder.getAdSalePercentage()).subtract(new BigDecimal(voBuilder.getCompareAdSalePercentage()));
                        voBuilder.setCompareAdSalePercentageRate(new BigDecimal(voBuilder.getCompareAdSalePercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSalePercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSalePercentage()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNumPercentage(StringUtils.isNotBlank(compareItem.getAdOrderNumPercentage()) ? compareItem.getAdOrderNumPercentage() : "0");
                        //AdOrderNumPercentage环比值
                        BigDecimal adOrderNumPercentageDiff = new BigDecimal(voBuilder.getAdOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()));
                        voBuilder.setCompareAdOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adOrderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNumPercentage(StringUtils.isNotBlank(compareItem.getOrderNumPercentage()) ? compareItem.getOrderNumPercentage() : "0");
                        //OrderNumPercentage环比值
                        BigDecimal orderNumPercentageDiff = new BigDecimal(voBuilder.getOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareOrderNumPercentage()));
                        voBuilder.setCompareOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                orderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                        // 广告笔单价 环比
                        voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAdvertisingUnitPriceRate(compareItem.getAdvertisingUnitPrice().compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(item.getAdvertisingUnitPrice(), compareItem.getAdvertisingUnitPrice(), 4), 100)));
                    }
                }

                return voBuilder.build();

            }).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }

        builder.setPage(pageBuilder.build());
        log.info("广告组合排序分页花费时间 {}", Instant.now().toEpochMilli() - t1);

        return builder.build();
    }

    @Override
    public AllPortfolioAggregateDataListResponse.PortfolioHomeVo getAllPortfolioAggregateDataList(Integer puid, PortfolioPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        //取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);

        // 查询汇总指标数据
        List<AdHomePerformancedto> reportList;
        // 对比周期汇总数据
        List<AdHomePerformancedto> reportListCompare = new ArrayList<>();

        List<String> portfolioIdsList;
        // 每日汇总数据
        List<AdHomePerformancedto> reportDayList = null;

        //兼容其他接口共用该方法时，缺少searchDataType导致的npe
        int searchDataType = Optional.ofNullable(param.getSearchDataType()).orElse(SearchDataTypeEnum.ALL.getCode());
        //是否查询汇总数据
        boolean searchAggregateData = SearchDataTypeEnum.containsAggregate(searchDataType);
        //是否查询图表数据
        boolean searchChartData = SearchDataTypeEnum.containsChart(searchDataType);

        AllPortfolioAggregateDataListResponse.PortfolioHomeVo.Builder builder = AllPortfolioAggregateDataListResponse.PortfolioHomeVo.newBuilder();

        // 标记广告组合查询数据是否为空
        boolean isNull = false;

        // 获取广告组合对象
        List<AmazonAdPortfolio> amazonAdPortfoliosList = portfolioDao.getPortfolioSumList(puid, param);
        portfolioIdsList = amazonAdPortfoliosList.stream().map(AmazonAdPortfolio::getPortfolioId).collect(Collectors.toList());
        // 获取所有广告活动Id
        List<String> allCampaignId = new ArrayList<>();
        int sumCampaignNumber = 0;

        if (CollectionUtils.isEmpty(amazonAdPortfoliosList)) {
            isNull = true;
        }
        if (CollectionUtils.isNotEmpty(portfolioIdsList)) {
            // 获取所有广告活动Id
            allCampaignId = campaignAllDao.getCampaignIdsByPortfolioIdList(puid, param.getShopId(), portfolioIdsList);
            sumCampaignNumber = allCampaignId.size();
        }
        if (isNull || CollectionUtils.isEmpty(allCampaignId)) {
            reportList = new ArrayList<>();
            reportDayList = new ArrayList<>();
        } else {

            reportList = amazonAdCampaignAllReportDao.getReportByCampaignIdsAndDate(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), allCampaignId);
            if (param.getIsCompare()) {
                reportListCompare = amazonAdCampaignAllReportDao.getReportByCampaignIdsAndDate(puid, param.getShopId(), param.getCompareStartDate(), param.getCompareEndDate(), allCampaignId);
            }
            //查询图表数据
            if (searchChartData){
                reportDayList = amazonAdCampaignAllReportDao.getAllDayReportByAllCampaignIdList(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), allCampaignId);
            }
        }

        //环比数据取销售额
        BigDecimal shopSalesByDateCompare = BigDecimal.ZERO;
        if (param.getIsCompare()) {
            //取店铺销售额
            shopSalesByDateCompare = cpcShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
            if (shopSalesByDateCompare == null) {
                shopSalesByDateCompare = BigDecimal.ZERO;
            }
        }

        if (searchChartData){
            String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();

            // 获取chart数据
            List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, shopSalesByDate);
            List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, shopSalesByDate);
            List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, param.getStartDate(), param.getEndDate(), reportDayList, shopSalesByDate);
            builder.addAllDay(dayPerformanceVos)
                    .addAllMonth(monthPerformanceVos)
                    .addAllWeek(weekPerformanceVos);
        }

        // 汇总指标数据
        AdHomeAggregateDataRpcVo aggregateDataRpcVo = getCampaignAggregateDataVo(reportList, reportListCompare, shopSalesByDate, shopSalesByDateCompare, sumCampaignNumber);

        return builder
                .setAggregateDataVo(aggregateDataRpcVo)
                .build();
    }

    @Override
    public AllPortfolioAggregateDataListResponse.PortfolioHomeVo getAllDorisPortfolioAggregateDataList(Integer puid, PortfolioPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        //取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);

        // 查询汇总指标数据
        List<AdHomePerformancedto> reportList;
        // 对比周期汇总数据
        List<AdHomePerformancedto> reportListCompare = new ArrayList<>();

        List<String> portfolioIdsList;
        // 每日汇总数据
        List<AdHomePerformancedto> reportDayList;

        // 标记广告组合查询数据是否为空
        boolean isNull = false;

        // 获取广告组合对象
        List<AmazonAdPortfolio> amazonAdPortfoliosList = portfolioDao.getPortfolioSumList(puid, param);
        portfolioIdsList = amazonAdPortfoliosList.stream().map(AmazonAdPortfolio::getPortfolioId).collect(Collectors.toList());

        List<String> allCampaignId = new ArrayList<>();
        int sumCampaignNumber = 0;

        if (CollectionUtils.isEmpty(amazonAdPortfoliosList)) {
            isNull = true;
        }

        if (CollectionUtils.isNotEmpty(portfolioIdsList)) {
            // 获取所有广告活动Id
            allCampaignId = campaignAllDao.getCampaignIdsByPortfolioIdList(puid, param.getShopId(), portfolioIdsList);
            sumCampaignNumber = allCampaignId.size();
        }
        if (isNull || CollectionUtils.isEmpty(allCampaignId)) {
            reportList = new ArrayList<>();
            reportDayList = new ArrayList<>();
        } else {
            reportList = amazonAdCampaignAllDorisDao.getReportByCampaignIdsAndDate(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), allCampaignId);
            if (param.getIsCompare()) {
                reportListCompare = amazonAdCampaignAllDorisDao.getReportByCampaignIdsAndDate(puid, param.getShopId(), param.getCompareStartDate(), param.getCompareEndDate(), allCampaignId);
            }
            reportDayList = amazonAdCampaignAllDorisDao.getAllDayReportByAllCampaignIdList(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), allCampaignId);

        }

        //环比数据取销售额
        BigDecimal shopSalesByDateCompare = BigDecimal.ZERO;
        if (param.getIsCompare()) {
            //取店铺销售额
            shopSalesByDateCompare = cpcShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
            if (shopSalesByDateCompare == null) {
                shopSalesByDateCompare = BigDecimal.ZERO;
            }
        }

        // 汇总指标数据
        AdHomeAggregateDataRpcVo aggregateDataRpcVo = getCampaignAggregateDataVo(reportList, reportListCompare, shopSalesByDate, shopSalesByDateCompare, sumCampaignNumber);

        String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();

        // 获取chart数据
        List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, shopSalesByDate);
        List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, shopSalesByDate);
        List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, param.getStartDate(), param.getEndDate(), reportDayList, shopSalesByDate);

        return AllPortfolioAggregateDataListResponse.PortfolioHomeVo.newBuilder()
                .setAggregateDataVo(aggregateDataRpcVo)
                .addAllDay(dayPerformanceVos)
                .addAllMonth(monthPerformanceVos)
                .addAllWeek(weekPerformanceVos)
                .build();
    }

    private AdHomeAggregateDataRpcVo getCampaignAggregateDataVo(List<AdHomePerformancedto> rows, List<AdHomePerformancedto> rowsCompare, BigDecimal shopSales, BigDecimal shopSalesCompare, int sumCampaignNumber) {

        //点击量
        int sumClicks = rows.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNum = rows.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressions = rows.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSale = rows.stream().filter(item -> item != null && item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcost = rows.stream().filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);

        int sumOrderNum = rows.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(AdHomePerformancedto::getOrderNum).sum();
        //ACoS
        BigDecimal sumAcos = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(sumAdSale, 2, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClick = sumClicks == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCvr = sumClicks == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNum).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtr = sumImpressions == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicks).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressions), 2, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roas = sumAdcost.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSale.divide(sumAdcost, 2, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(shopSales, 2, BigDecimal.ROUND_HALF_UP);
        //asots
        BigDecimal asots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSale.multiply(new BigDecimal("100")).divide(shopSales, 2, BigDecimal.ROUND_HALF_UP);
        //cpa
        BigDecimal sumCpa = sumAdOrderNum == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumAdOrderNum), 2, BigDecimal.ROUND_HALF_UP);
        // 广告笔单价,广告销售额/广告订单量*100%
        BigDecimal sumAdvertisingUnitPrice = sumAdOrderNum == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdSale, sumAdOrderNum, 2);

        //环比数据
        //点击量
        int sumClicksCompare = rowsCompare.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNumCompare = rowsCompare.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressionsCompare = rowsCompare.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSaleCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcostCompare = rowsCompare.stream().filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);

        int sumOrderNumCompare = rowsCompare.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(AdHomePerformancedto::getOrderNum).sum();

        //ACoS
        BigDecimal sumAcosCompare = sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcostCompare.multiply(new BigDecimal("100")).divide(sumAdSaleCompare, 2, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClickCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : sumAdcostCompare.divide(new BigDecimal(sumClicksCompare), 2, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCvrCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNumCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicksCompare), 2, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtrCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicksCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressionsCompare), 2, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roasCompare = sumAdcostCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSaleCompare.divide(sumAdcostCompare, 2, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acotsCompare = shopSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcostCompare.multiply(new BigDecimal("100")).divide(shopSalesCompare, 2, BigDecimal.ROUND_HALF_UP);
        //asots
        BigDecimal asotsCompare = shopSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSaleCompare.multiply(new BigDecimal("100")).divide(shopSalesCompare, 2, BigDecimal.ROUND_HALF_UP);
        // 广告笔单价,广告销售额/广告订单量*100%
        BigDecimal sumAdvertisingUnitPriceCompare = sumAdOrderNumCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdSaleCompare, sumAdOrderNumCompare, 2);

        return com.meiyunji.sponsored.rpc.adCommon.AdHomeAggregateDataRpcVo.newBuilder()
                .setSumCampaignNumber(sumCampaignNumber)
                .setAcos(sumAcos.toPlainString())
                .setRoas(roas.toPlainString())
                .setAcots(acots.toPlainString())
                .setAsots(asots.toPlainString())
                .setAdCost(sumAdcost.toPlainString())
                .setAdCostPerClick(sumAdCostPerClick.toPlainString())
                .setAdOrderNum(Int32Value.of(sumAdOrderNum))
                .setCvr(sumCvr.toPlainString())
                .setCtr(sumCtr.toPlainString())
                .setAdSale(sumAdSale.toPlainString())
                .setClicks(Int32Value.of(sumClicks))
                .setImpressions(Int32Value.of(sumImpressions))
                .setOrderNum(Int32Value.of(sumOrderNum))
                .setOrderNumPercentage("100.0000")
                .setAdCostPercentage("100.0000")
                .setAdSalePercentage("100.0000")
                .setAdOrderNumPercentage("100.0000")
                .setCpa(sumCpa.toPlainString())
                .setAdvertisingUnitPrice(String.valueOf(sumAdvertisingUnitPrice))

                //环比数据
                .setCompareAcos(sumAcosCompare.toPlainString())
                .setCompareRoas(roasCompare.toPlainString())
                .setCompareAdCost(sumAdcostCompare.toPlainString())
                .setCompareAdCostPerClick(sumAdCostPerClickCompare.toPlainString())
                .setCompareAdOrderNum(Int32Value.of(sumAdOrderNumCompare))
                .setCompareCvr(sumCvrCompare.toPlainString())
                .setCompareCtr(sumCtrCompare.toPlainString())
                .setCompareAdSale(sumAdSaleCompare.toPlainString())
                .setCompareClicks(Int32Value.of(sumClicksCompare))
                .setCompareImpressions(Int32Value.of(sumImpressionsCompare))
                .setCompareAcots(acotsCompare.toPlainString())
                .setCompareAsots(asotsCompare.toPlainString())
                .setCompareAdvertisingUnitPrice(String.valueOf(sumAdvertisingUnitPriceCompare))

                //环比值
                .setCompareAcosRate(sumAcosCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAcos.subtract(sumAcosCompare))
                        .multiply(new BigDecimal(100)).divide(sumAcosCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareRoasRate(roasCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (roas.subtract(roasCompare))
                        .multiply(new BigDecimal(100)).divide(roasCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdCostRate(sumAdcostCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdcost.subtract(sumAdcostCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdcostCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdCostPerClickRate(sumAdCostPerClickCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdCostPerClick.subtract(sumAdCostPerClickCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdCostPerClickCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdOrderNumRate(sumAdOrderNumCompare == 0 ? "-" : new BigDecimal(sumAdOrderNum - sumAdOrderNumCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumAdOrderNumCompare), 2, RoundingMode.HALF_UP).toPlainString())


                .setCompareCvrRate(sumCvrCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCvr.subtract(sumCvrCompare))
                        .multiply(new BigDecimal(100)).divide(sumCvrCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareCtrRate(sumCtrCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCtr.subtract(sumCtrCompare))
                        .multiply(new BigDecimal(100)).divide(sumCtrCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdSaleRate(sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdSale.subtract(sumAdSaleCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdSaleCompare, 2, RoundingMode.HALF_UP).toPlainString())


                .setCompareClicksRate(sumClicksCompare == 0 ? "-" : new BigDecimal(sumClicks - sumClicksCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumClicksCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareImpressionsRate(sumImpressionsCompare == 0 ? "-" : new BigDecimal(sumImpressions - sumImpressionsCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumImpressionsCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAcotsRate(acotsCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (acots.subtract(acotsCompare))
                        .multiply(new BigDecimal(100)).divide(acotsCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAsotsRate(asotsCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (asots.subtract(asotsCompare))
                        .multiply(new BigDecimal(100)).divide(asotsCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareAdvertisingUnitPriceRate(sumAdvertisingUnitPriceCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdvertisingUnitPrice, sumAdvertisingUnitPriceCompare, 4), 100)))

                .build();
    }

    @Override
    public List<PortfolioPageVo> getAllPortfolioVoList(Integer puid, Integer shopId, PortfolioPageParam param) {
        long t = System.currentTimeMillis();
        List<PortfolioPageVo> voList;
        if (dynamicRefreshConfiguration.verifyDorisPage(puid, dynamicRefreshConfiguration.getDorisPagePortfolio())) {
            voList = getDorisPortfolioVoList(puid, param, null, true);
        } else {
            voList = getPortfolioVoList(puid, param, null, true);
        }
        log.info("广告管理--广告组合接口调用-获取广告组合数据- 花费时间 {} ,params: {}", (Instant.now().toEpochMilli() - t), JSONUtil.objectToJson(param));

        if (CollectionUtils.isNotEmpty(voList) && voList.size() > EXPORT_MAX_SIZE) {
            return voList.subList(0, EXPORT_MAX_SIZE);
        }

        return voList;
    }

    @Override
    public List<PortfolioPageVo> getPortfolioVoList(Integer puid, PortfolioPageParam param, Page<PortfolioPageVo> voPage, boolean isExport) {
        List<PortfolioPageVo> voList;

        // 导出
        if (isExport) {
            voList = amazonAdPortfolioService.getPortfolioPageVoList(puid, param);
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = Constants.isADperformanceOrderField(param.getOrderField());
                if (isSorted && !"null".equals(param.getOrderType())) {
                    voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
                }
            }
            return voList;
        }

        if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
            voList = amazonAdPortfolioService.getPortfolioPageVoList(puid, param);
            boolean isSorted = Constants.isADOrderField(param.getOrderField(), PortfolioPageVo.class);
            if (isSorted && !"null".equals(param.getOrderType())) {
                voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
            }
            PageUtil.getPage(voPage, voList);

        } else {
            Page page = new Page(voPage.getPageNo(), voPage.getPageSize());
            page = amazonAdPortfolioService.getPortfolioPageVoPageList(puid, param, page);
            if (page.getTotalSize() > Constants.TOTALSIZELIMIT) {
                int totalPage = Constants.TOTALSIZELIMIT / voPage.getPageSize();
                voPage.setTotalPage(totalPage);
                voPage.setTotalSize(Constants.TOTALSIZELIMIT);
            } else {
                voPage.setTotalPage(page.getTotalPage());
                voPage.setTotalSize(page.getTotalSize());
            }
            voPage.setRows(page.getRows());
        }

        return null;
    }

    @Override
    public List<PortfolioPageVo> getDorisPortfolioVoList(Integer puid, PortfolioPageParam param, Page<PortfolioPageVo> voPage, boolean isExport) {
        if (isExport) {
            return amazonAdPortfolioService.getDorisPortfolioVoList(puid, param);
        } else {
            Page<PortfolioPageVo> page = amazonAdPortfolioService.getDorisPortfolioVoPage(puid, param, false);
            voPage.setRows(page.getRows());
            voPage.setTotalPage(page.getTotalPage());
            voPage.setTotalSize(page.getTotalSize());
        }
        return null;
    }

    @Override
    public Page<AmazonAdPortfolio> getMultiShopPortfolioList(MultiShopPortfolioListParam param) {
        return portfolioDao.getMultiShopPortfolioList(param.getPuid(), param);
    }
}
