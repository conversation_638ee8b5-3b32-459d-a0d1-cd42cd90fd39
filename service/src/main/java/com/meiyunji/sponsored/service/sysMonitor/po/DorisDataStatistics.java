package com.meiyunji.sponsored.service.sysMonitor.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@DbTable(value = "t_doris_data_statistics")
public class DorisDataStatistics implements Serializable {
    @DbColumn(value = "id")
    private Long id;

    @DbColumn(value = "table_schema")
    private String tableSchema;

    @DbColumn(value = "table_name")
    private String tableName;

    @DbColumn(value = "table_comment")
    private String tableComment;

    @DbColumn(value = "table_rows")
    private Long tableRows;

    @DbColumn(value = "avg_row_length")
    private Long avgRowLength;

    @DbColumn(value = "data_length")
    private Long dataLength;

    @DbColumn(value = "data_length_gb")
    private BigDecimal dataLengthGb;

    @DbColumn(value = "data_length_tb")
    private BigDecimal dataLengthTb;

    @DbColumn(value = "stat_date")
    private String statDate;

    @DbColumn(value = "create_time")
    private Date createTime;

    @DbColumn(value = "update_time")
    private Date updateTime;

}
