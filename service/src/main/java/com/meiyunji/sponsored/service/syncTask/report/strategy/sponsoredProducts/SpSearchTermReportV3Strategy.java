package com.meiyunji.sponsored.service.syncTask.report.strategy.sponsoredProducts;

import com.alibaba.fastjson.JSONReader;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportV3Type;
import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;
import com.meiyunji.sellfox.aadras.api.enumeration.AdvertiseRuleTaskTypePb;
import com.meiyunji.sellfox.aadras.types.message.task.GroupSearchQueryScheduleMessage;
import com.meiyunji.sellfox.aadras.types.message.task.QueryKeywordScheduleMessage;
import com.meiyunji.sponsored.common.enums.AmazonAd;
import com.meiyunji.sponsored.common.springjdbc.PartitionSqlUtil;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.MD5Util;
import com.meiyunji.sponsored.service.account.dao.ISlaveVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDao;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.ICpcQueryKeywordReportDao;
import com.meiyunji.sponsored.service.cpc.dao.ICpcQueryTargetingReportDao;
import com.meiyunji.sponsored.service.cpc.dto.AdSyncAsinInfoHandleMessage;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryKeywordReport;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryTargetingReport;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.ShopTypeEnum;
import com.meiyunji.sponsored.service.enums.SpKeywordGroupValueEnum;
import com.meiyunji.sponsored.service.kafka.AdSyncAsinInfoHandleKafkaProducer;
import com.meiyunji.sponsored.service.localization.dao.IAmazonKeywordLocalizationScheduleDao;
import com.meiyunji.sponsored.service.localization.po.AmazonKeywordLocalizationSchedule;
import com.meiyunji.sponsored.service.product.dao.IAsinInfoDao;
import com.meiyunji.sponsored.service.syncTask.entity.SponsoredProductSearchTerm;
import com.meiyunji.sponsored.service.syncTask.report.strategy.AbstractReportProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.client.api.Producer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

import static com.meiyunji.sponsored.service.cpc.util.Constants.ASIN_REGEX;

@Component
@Slf4j
@ConditionalOnProperty(name = "aadas.scheduler.executors.reports-consumer.enabled", havingValue = "true")
public class SpSearchTermReportV3Strategy extends AbstractReportProcessStrategy {
    private final ICpcQueryTargetingReportDao cpcQueryTargetingReportDao;
    private final ICpcQueryKeywordReportDao cpcQueryKeywordReportDao;
    private final IAmazonKeywordLocalizationScheduleDao amazonKeywordLocalizationScheduleDao;
    private final IAmazonAdProfileDao amazonAdProfileDao;
    private final IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;
    private final Producer<byte[]> queryWordReportsChangeProducer;
    private final Producer<byte[]> queryKeywordReportsChangeProducer;
    private final PartitionSqlUtil partitionSqlUtil;
    @Resource
    private IAsinInfoDao asinInfoDao;
    @Resource
    private AdSyncAsinInfoHandleKafkaProducer adSyncAsinInfoHandleKafkaProducer;
    @Resource
    private ISlaveVcShopAuthDao vcShopAuthDao;

    public SpSearchTermReportV3Strategy(
            CosBucketClient dataBucketClient,
            ICpcQueryTargetingReportDao cpcQueryTargetingReportDao,
            ICpcQueryKeywordReportDao cpcQueryKeywordReportDao,
            IAmazonKeywordLocalizationScheduleDao amazonKeywordLocalizationScheduleDao,
            IAmazonAdProfileDao amazonAdProfileDao,
            IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao,
            Producer<byte[]> queryKeywordReportsChangeProducer,
            Producer<byte[]> queryWordReportsChangeProducer, PartitionSqlUtil partitionSqlUtil) {
        super(dataBucketClient);
        this.cpcQueryTargetingReportDao = cpcQueryTargetingReportDao;
        this.cpcQueryKeywordReportDao = cpcQueryKeywordReportDao;
        this.amazonKeywordLocalizationScheduleDao = amazonKeywordLocalizationScheduleDao;
        this.amazonAdProfileDao = amazonAdProfileDao;
        this.advertiseAutoRuleStatusDao = advertiseAutoRuleStatusDao;
        this.queryWordReportsChangeProducer = queryWordReportsChangeProducer;
        this.queryKeywordReportsChangeProducer = queryKeywordReportsChangeProducer;
        this.partitionSqlUtil = partitionSqlUtil;
    }

    @Override
    public Boolean checkValid(ReportReadyNotification notification) {
        return notification.getVersion() == 3 &&
                notification.getV3Type() == AmazonReportV3Type.sp_keyword_target_searchTerm;
    }

    @Override
    public void processReport(ReportReadyNotification notification) throws Exception {
        try (InputStreamReader inputStreamReader = new InputStreamReader(new GZIPInputStream(new ByteArrayInputStream(dataBucketClient.getObjectToBytes(notification.getPath()))));JSONReader jsonReader = new JSONReader(inputStreamReader)) {
            jsonReader.startArray();
            List<SponsoredProductSearchTerm> reports = Lists.newArrayListWithExpectedSize(batchSize);
            Set<String> groupList = new HashSet<>();
            Set<String> queryIdList = new HashSet<>();
            VcShopAuth byIdAndPuid = vcShopAuthDao.getByIdAndPuid(notification.getMarketplaceIdentifier(), notification.getSellerIdentifier());
            String shopType = ShopTypeEnum.SC.getCode();
            if (byIdAndPuid != null && byIdAndPuid.getId() != null) {
                shopType = ShopTypeEnum.VC.getCode();
            }
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                SponsoredProductSearchTerm report = new SponsoredProductSearchTerm();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.getImpressions() != 0 || (report.getImpressions() == 0 && (report.getUnitsSoldClicks7d() != null && report.getUnitsSoldClicks7d() > 0))) {
                    reports.add(report);
                    if (report.getAdGroupId() != null) {
                        groupList.add(String.valueOf(report.getAdGroupId()));
                    }
                    if (report.getSearchTerm() != null && report.getKeywordId() != null) {
//                        log.info("sendQueryKeywordMessage, keywordId: {}, searchTerm: {}", report.getKeywordId(), report.getSearchTerm());
                        queryIdList.add(MD5Util.getMD5(report.getKeywordId() + report.getSearchTerm()));
                    }
                }
                if (reports.size() >= batchSize) {
                    dealReports(notification, reports, shopType);
                    reports = Lists.newArrayListWithExpectedSize(batchSize);
                }
            }
            jsonReader.endArray();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReports(notification, reports, shopType);
            }
            //判断是否有受控对象推送消息至数据
            if (!groupList.isEmpty()) {
                try {
                    List<AdvertiseAutoRuleStatus> listByItemIdsAndEnabled = advertiseAutoRuleStatusDao.getListByItemIdsAndEnabled(notification.getSellerIdentifier(), notification.getMarketplaceIdentifier(), "SP", AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.GROUP_SEARCH_QUERY.name(),Lists.newArrayList(groupList));
                    if(CollectionUtils.isNotEmpty(listByItemIdsAndEnabled)){
                        List<String> collect = listByItemIdsAndEnabled.stream().map(AdvertiseAutoRuleStatus::getItemId).collect(Collectors.toList());
                        for (AdvertiseAutoRuleStatus item: listByItemIdsAndEnabled){
                            conversionMessage(notification.getSellerId(), item);
                            queryWordReportsChangeProducer.send(JSONUtil.objectToJson(conversionMessage(notification.getSellerId(), item)).getBytes(StandardCharsets.UTF_8));
                        }
                    }
                } catch (Exception e) {
                    log.error("推送自动化规则搜索词数据错误",e);
                }
            }
            if (!queryIdList.isEmpty()) {
                try {
                    List<AdvertiseAutoRuleStatus> listByItemIdsAndEnabled = advertiseAutoRuleStatusDao.ListByItemIdsByChildItemType(notification.getSellerIdentifier(), notification.getMarketplaceIdentifier(), "SP", AmazonAd.ChildrenItemType.CHILDREN_SEARCH_QUERY.getName(), Lists.newArrayList(queryIdList));
                    if (CollectionUtils.isNotEmpty(listByItemIdsAndEnabled)) {
                        for (AdvertiseAutoRuleStatus item: listByItemIdsAndEnabled) {
                            queryKeywordReportsChangeProducer.send(JSONUtil.objectToJson(buildQueryKeywordScheduleMessage(notification.getSellerId(), item)).getBytes(StandardCharsets.UTF_8));
                        }
                    }
                } catch (Exception e) {
                    log.error("推送自动化规则搜索词数据错误",e);
                }
            }
        } catch (Exception e) {
            log.info("报告处理发生错误{}@{} reportType={} countDate={}", notification.getSellerIdentifier()
                    , notification.getMarketplaceIdentifier(), notification.getType(), notification.getDate(), e);
            throw e;
        }
    }

    private void dealReports(ReportReadyNotification notification, List<SponsoredProductSearchTerm> reports, String shopType) throws Exception {
        //处理target报告
        Set<String> asin = new HashSet<>();
        List<SponsoredProductSearchTerm> targetReports = reports.stream().peek(e->{
            if (StringUtils.isNotBlank(e.getSearchTerm()) && Pattern.compile(ASIN_REGEX).matcher(e.getSearchTerm()).matches() && Period.between(e.getDate(), LocalDate.now(ZoneOffset.UTC)).getDays() <= 3) {
                asin.add(e.getSearchTerm().toLowerCase());
            }
        }).filter(o -> o.getKeywordType()
                != null && Arrays.asList("TARGETING_EXPRESSION", "TARGETING_EXPRESSION_PREDEFINED")
                .contains(o.getKeywordType())).collect(Collectors.toList());
        dealTargetReports(notification, targetReports, shopType);
        //处理keyword报告
        List<SponsoredProductSearchTerm> keywordReports = reports.stream().filter(o -> o.getKeywordType()
                != null && Arrays.asList("BROAD", "PHRASE", "EXACT")
                .contains(o.getKeywordType())).collect(Collectors.toList());
        dealKeywordReports(notification, keywordReports, shopType);
        try {
            long s = System.currentTimeMillis();
            AdSyncAsinInfoHandleMessage message = new AdSyncAsinInfoHandleMessage();
            message.setPuid(notification.getSellerIdentifier());
            message.setMarketplaceId(notification.getMarketplace().getId());
            message.setAsins(asin);
            adSyncAsinInfoHandleKafkaProducer.send(message);
//            asinInfoDao.insertOrUpdateIsNeedSync(Lists.newArrayList(asin), notification.getMarketplace().getId());
            log.info("发送asin信息耗时：{}", System.currentTimeMillis() - s);
        } catch (Exception e) {
            log.error("发送asin 信息错误：", e);
        }
    }

    private void dealTargetReports(ReportReadyNotification notification, List<SponsoredProductSearchTerm> reports, String shopType) throws Exception {
        List<CpcQueryTargetingReport> poList = getQueryPoByReportTargeting(notification, reports, shopType);
        List<List<CpcQueryTargetingReport>> partition = Lists.partition(poList, batchSize);
        for (List<CpcQueryTargetingReport> cpcQueryTargetingReports : partition) {
            partitionSqlUtil.save(notification.getSellerIdentifier(), cpcQueryTargetingReports, 0, cpcQueryTargetingReportDao::insertList);
        }

        AmazonKeywordLocalizationSchedule schedule =
                amazonKeywordLocalizationScheduleDao.getByShopId(notification.getSellerIdentifier(), notification.getMarketplaceIdentifier());
        if (schedule != null) {
            schedule.setSpNextSyncAt(LocalDateTime.now());
            amazonKeywordLocalizationScheduleDao.updateSpNextSyncAtById(schedule);
        } else {
            AmazonAdProfile profile = amazonAdProfileDao.getProfile(notification.getSellerIdentifier(), notification.getMarketplaceIdentifier());
            if (profile == null) {
                return;
            }
            AmazonKeywordLocalizationSchedule localizationSchedule = new AmazonKeywordLocalizationSchedule();
            localizationSchedule.setPuid(notification.getSellerIdentifier());
            localizationSchedule.setShopId(notification.getMarketplaceIdentifier());
            localizationSchedule.setMarketplaceId(profile.getMarketplaceId());
            localizationSchedule.setProfileId(profile.getProfileId());
            amazonKeywordLocalizationScheduleDao.save(localizationSchedule);
        }
    }


    private List<CpcQueryTargetingReport> getQueryPoByReportTargeting(ReportReadyNotification notification, List<SponsoredProductSearchTerm> reports, String shopType) {
        List<CpcQueryTargetingReport> list = Lists.newArrayList();
        CpcQueryTargetingReport po;
        for (SponsoredProductSearchTerm report : reports) {
            po = new CpcQueryTargetingReport();
            po.setCountDate(report.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            po.setPuid(notification.getSellerIdentifier());
            po.setShopId(notification.getMarketplaceIdentifier());
            po.setMarketplaceId(notification.getMarketplace().getId());
            po.setCampaignId(String.valueOf(report.getCampaignId()));
            po.setAdGroupId(String.valueOf(report.getAdGroupId()));
            po.setTargetId(String.valueOf(report.getKeywordId()));
            if ("TARGETING_EXPRESSION".equals(report.getKeywordType()) && report.getTargeting().toLowerCase().contains(SpKeywordGroupValueEnum.getKeywordGroupKey())) {
                po.setTargetingText(report.getKeyword());
                po.setTargetingExpression(report.getKeyword());
            } else {
                po.setTargetingText(report.getTargeting());
                po.setTargetingExpression(report.getTargeting());
            }
            po.setTargetingType(report.getKeywordType());
            po.setAdGroupName(report.getAdGroupName());
            po.setCampaignName(report.getCampaignName());
            po.setQuery(report.getSearchTerm());

            //搜索词符合 b0 开头，后面跟8位字母和数字的组合
            po.setIsAsin(StringUtils.isNotBlank(po.getQuery()) && Pattern.compile(ASIN_REGEX).matcher(po.getQuery()).matches());

            po.setImpressions(report.getImpressions());
            po.setClicks(report.getClicks());
            po.setCost(report.getCost());
            if (ShopTypeEnum.VC.getCode().equalsIgnoreCase(shopType)) {
                po.setTotalSales(report.getSales14d().setScale(2, BigDecimal.ROUND_HALF_UP));
                po.setAdSales(report.getAttributedSalesSameSku14d().setScale(2, BigDecimal.ROUND_HALF_UP));
                po.setOrderNum(report.getUnitsSoldClicks14d());
                po.setAdOrderNum(report.getUnitsSoldSameSku14d());
                po.setSaleNum(report.getPurchases14d());
                po.setAdSaleNum(report.getPurchasesSameSku14d());
            } else {
                po.setTotalSales(report.getSales7d().setScale(2, BigDecimal.ROUND_HALF_UP));
                po.setAdSales(report.getAttributedSalesSameSku7d().setScale(2, BigDecimal.ROUND_HALF_UP));
                po.setOrderNum(report.getUnitsSoldClicks7d());
                po.setAdOrderNum(report.getUnitsSoldSameSku7d());
                po.setSaleNum(report.getPurchases7d());
                po.setAdSaleNum(report.getPurchasesSameSku7d());
            }
            po.setQueryId(MD5Util.getMD5(po.getTargetId() + po.getQuery()));
            list.add(po);
        }
        return list;
    }

    private void dealKeywordReports(ReportReadyNotification notification, List<SponsoredProductSearchTerm> reports, String shopType) throws Exception {
        List<CpcQueryKeywordReport> poList = getQueryPoByReportAdKeyword(notification, reports, shopType);
        List<List<CpcQueryKeywordReport>> partition = Lists.partition(poList, batchSize);
        for (List<CpcQueryKeywordReport> cpcQueryKeywordReports : partition) {
            partitionSqlUtil.save(notification.getSellerIdentifier(), cpcQueryKeywordReports, 0, cpcQueryKeywordReportDao::insertList);
        }

        AmazonKeywordLocalizationSchedule schedule =
                amazonKeywordLocalizationScheduleDao.getByShopId(notification.getSellerIdentifier(), notification.getMarketplaceIdentifier());
        if (schedule != null) {
            schedule.setSpNextSyncAt(LocalDateTime.now());
            amazonKeywordLocalizationScheduleDao.updateSpNextSyncAtById(schedule);
        } else {
            AmazonAdProfile profile = amazonAdProfileDao.getProfile(notification.getSellerIdentifier(), notification.getMarketplaceIdentifier());
            if (profile == null) {
                return;
            }
            AmazonKeywordLocalizationSchedule localizationSchedule = new AmazonKeywordLocalizationSchedule();
            localizationSchedule.setPuid(notification.getSellerIdentifier());
            localizationSchedule.setShopId(notification.getMarketplaceIdentifier());
            localizationSchedule.setMarketplaceId(profile.getMarketplaceId());
            localizationSchedule.setProfileId(profile.getProfileId());
            amazonKeywordLocalizationScheduleDao.save(localizationSchedule);
        }
    }

    private List<CpcQueryKeywordReport> getQueryPoByReportAdKeyword(ReportReadyNotification notification, List<SponsoredProductSearchTerm> reports, String shopType) {
        List<CpcQueryKeywordReport> list = Lists.newArrayList();
        CpcQueryKeywordReport po;
        for (SponsoredProductSearchTerm report : reports) {
            po = new CpcQueryKeywordReport();
            po.setCountDate(report.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            po.setPuid(notification.getSellerIdentifier());
            po.setShopId(notification.getMarketplaceIdentifier());
            po.setMarketplaceId(notification.getMarketplace().getId());
            po.setCampaignId(String.valueOf(report.getCampaignId()));
            po.setAdGroupId(String.valueOf(report.getAdGroupId()));
            po.setKeywordId(String.valueOf(report.getKeywordId()));
            po.setKeywordText(report.getKeyword());
            po.setMatchType(report.getMatchType());
            po.setAdGroupName(report.getAdGroupName());
            po.setCampaignName(report.getCampaignName());
            po.setQuery(report.getSearchTerm());

            po.setImpressions(report.getImpressions());
            po.setClicks(report.getClicks());
            po.setCost(report.getCost());
            if (ShopTypeEnum.VC.getCode().equalsIgnoreCase(shopType)) {
                po.setTotalSales(report.getSales14d().setScale(2, BigDecimal.ROUND_HALF_UP));
                po.setAdSales(report.getAttributedSalesSameSku14d().setScale(2, BigDecimal.ROUND_HALF_UP));
                po.setOrderNum(report.getUnitsSoldClicks14d());
                po.setAdOrderNum(report.getUnitsSoldSameSku14d());
                po.setSaleNum(report.getPurchases14d());
                po.setAdSaleNum(report.getPurchasesSameSku14d());
            } else {
                po.setTotalSales(report.getSales7d().setScale(2, BigDecimal.ROUND_HALF_UP));
                po.setAdSales(report.getAttributedSalesSameSku7d().setScale(2, BigDecimal.ROUND_HALF_UP));
                po.setOrderNum(report.getUnitsSoldClicks7d());
                po.setAdOrderNum(report.getUnitsSoldSameSku7d());
                po.setSaleNum(report.getPurchases7d());
                po.setAdSaleNum(report.getPurchasesSameSku7d());
            }
            po.setQueryId(MD5Util.getMD5(po.getKeywordId() + po.getQuery()));
            list.add(po);
        }
        return list;
    }

    private GroupSearchQueryScheduleMessage conversionMessage(String sellerId,AdvertiseAutoRuleStatus autoRuleStatus){
        GroupSearchQueryScheduleMessage message = new GroupSearchQueryScheduleMessage();
        message.setMarketplaceId(autoRuleStatus.getMarketplaceId());
        message.setPuid(autoRuleStatus.getPuid());
        message.setGroupId(autoRuleStatus.getItemId());
        message.setType(CampaignTypeEnum.sp.name());
        message.setSellerId(sellerId);
        message.setItemType(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.GROUP_SEARCH_QUERY.name());
        return message;
    }

    private QueryKeywordScheduleMessage buildQueryKeywordScheduleMessage(String sellerId, AdvertiseAutoRuleStatus autoRuleStatus) {
        QueryKeywordScheduleMessage queryKeywordScheduleMessage = new QueryKeywordScheduleMessage();
        queryKeywordScheduleMessage.setMarketplaceId(autoRuleStatus.getMarketplaceId());
        queryKeywordScheduleMessage.setPuid(autoRuleStatus.getPuid());
        queryKeywordScheduleMessage.setQueryId(autoRuleStatus.getItemId());
        queryKeywordScheduleMessage.setType(CampaignTypeEnum.sp.name());
        queryKeywordScheduleMessage.setSellerId(sellerId);
        queryKeywordScheduleMessage.setItemType(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.QUERY_KEYWORD.name());
        return queryKeywordScheduleMessage;
    }
}
