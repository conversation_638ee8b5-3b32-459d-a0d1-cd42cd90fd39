package com.meiyunji.sponsored.service.export.handler;

import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.export.CampaignDataResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.adTagSystem.dao.IAdManageTagDao;
import com.meiyunji.sponsored.service.adTagSystem.dao.impl.AdManageTagDaoGroupUserImpl;
import com.meiyunji.sponsored.service.adTagSystem.enums.AdManageTagTypeEnum;
import com.meiyunji.sponsored.service.adTagSystem.po.AdManageTag;
import com.meiyunji.sponsored.service.adTagSystem.service.impl.AdManageTagRelationService;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.dao.IAdMarkupTagDao;
import com.meiyunji.sponsored.service.cpc.dao.IAdTagDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.cpc.service2.ICpcCampaignService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdMarkupTagVo;
import com.meiyunji.sponsored.service.cpc.vo.CampaignPageParam;
import com.meiyunji.sponsored.service.cpc.vo.CampaignPageVo;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.export.util.ExportStringUtil;
import com.meiyunji.sponsored.service.util.ReportParamUtil;
import com.meiyunji.sponsored.service.vo.AdvertisingCampaignVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-07-17  18:45
 */
@Service(AdManagePageExportTaskConstant.CAMPAIGN)
@Slf4j
public class CampaignPageExportTaskHandler implements AdManagePageExportTaskHandler {
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private ICpcCampaignService cpcCampaignService;
    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private AdManageTagRelationService adManageTagRelationService;

    @Override
    public void export(AdManagePageExportTask task) {
        //1，解析参数
        CampaignPageParam param = JSONUtil.jsonToObject(task.getParam(), CampaignPageParam.class);
        if (param == null) {
            log.error(String.format("campaign export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        String err = checkParams(param.getExportSortField());
        if (StringUtils.isNotBlank(err)) {
            log.error(String.format("campaign export error, exportSortField is err: %s, task id : %d", err, task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }

        //2，组装导出数据
        List<CampaignDataResponse.CampaignPageVo> sourceDataList = assembleDataForExport(param);
        if (CollectionUtils.isEmpty(sourceDataList)) {
            //修改任务状态
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            //修改状态，前端收到后转圈效果停止
            stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }
        //3，导出数据
        List<String> downloadUrl = exportData(sourceDataList, param, task);

        //4，更新任务状态为导出成功
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(downloadUrl), 100);
        stringRedisService.set(param.getUuid(), new ProcessMsg(1, downloadUrl.size(), "导出成功", downloadUrl));
    }

    private String checkParams(String exportSortFields) {

        if (StringUtils.isBlank(exportSortFields)) {
            return null;
        }
        //排序字段校验
        try {
            String[] split = exportSortFields.split(",");
            for (String s : split) {
                if (!AdvertisingCampaignExportFieldEnum.getPoParamKeyList().contains(s)) {
                    return "参数错误：" + s;
                }
            }
        } catch (Exception e) {
            return "参数错误：" + exportSortFields;
        }
        return null;
    }

    //导出数据到excel
    private List<String> exportData(List<CampaignDataResponse.CampaignPageVo> sourceDataList, CampaignPageParam param, AdManagePageExportTask task) {
        ShopAuth shop = shopAuthDao.getScAndVcById(param.getShopId());
        //文件名
        String fileName = shop.getName() + "_广告活动" + "_" + param.getStartDate() + "_" + param.getEndDate();
        //站点币种
        String currency = AmznEndpoint.getByMarketplaceId(shop.getMarketplaceId()).getCurrencyCode().value();

        //Excel样式builder
        WriteHandlerBuild build = new WriteHandlerBuild()
            .rate();
        //冻结前n列前1行
        if (param.getFreezeNum() != null) {
            build.freezeRowAndCol(param.getFreezeNum(), 1);
        }
        //根据活动类型组装需要排除的字段
        List<String> excludeFields = assembleExcludeFields(param.getType());

        //自定义排序：根据param的exportSortField，中的字段作为表格中的表头字段顺序导出
        if (StringUtils.isNotBlank(param.getExportSortField())) {
            List<String> sortFields = new ArrayList<>(Arrays.asList(param.getExportSortField().split(",")));
            sortFields = sortFields.stream().filter(item -> !excludeFields.contains(item)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sortFields)) {
                //非对象导出货币样式
                build.noModleHandler(getCurrencyIndex(sortFields));
                return customFieldSortExport(sourceDataList, sortFields, currency, task.getPuid(), fileName, build);
            }
        }

        //类形式导出货币样式
        build.currencyNew(AdvertisingCampaignVo.class);
        //存储文件路径urlList
        List<String> downloadUrl = new ArrayList<>();
        //数据有可能大于5万条,采用分片处理
        List<List<CampaignDataResponse.CampaignPageVo>> partition = Lists.partition(sourceDataList, Constants.EXPORT_MAX_SIZE);
        int count = 0;
        for (List<CampaignDataResponse.CampaignPageVo> partitionList : partition) {
            List<AdvertisingCampaignVo> adverList = new LinkedList<>();
            for (CampaignDataResponse.CampaignPageVo cpVo : partitionList) {
                adverList.add(this.buildExportVo(currency, cpVo));
            }
            if (adverList.size() > 0) {
                downloadUrl.add(excelService.easyExcelHandlerExport(task.getPuid(),
                    adverList, fileName + "(" + (count++) + ")", AdvertisingCampaignVo.class, build, excludeFields));
            }
        }
        return downloadUrl;
    }

    private List<Integer> getCurrencyIndex(List<String> sortFields) {
        List<Integer> currencyIndex = new ArrayList<>();
        for (int i = 0; i < sortFields.size(); i++) {
            AdvertisingCampaignExportFieldEnum advertisingCampaignExportFieldEnum = AdvertisingCampaignExportFieldEnum.fromPoParamKey(sortFields.get(i));
            //出现这种情况就是有问题，暂时不考虑
            if (advertisingCampaignExportFieldEnum == null) {
                return Collections.emptyList();
            }
            if (advertisingCampaignExportFieldEnum.isCurrencyStyle()) {
                currencyIndex.add(i);
            }
        }

        return currencyIndex;
    }

    private static CampaignDataResponse.CampaignPageVo buildGrpcVo(CampaignPageVo item) {
        CampaignDataResponse.CampaignPageVo.Builder vo = CampaignDataResponse.CampaignPageVo.newBuilder();
        if (item.getType() != null) {
            vo.setType(item.getType());
        }
        if (StringUtils.isNotBlank(item.getName())) {
            vo.setName(item.getName());
        }
        if (StringUtils.isNotBlank(item.getState())) {
            vo.setState(item.getState());
        }
        if (StringUtils.isNotBlank(item.getType())) {
            vo.setCampaignType(item.getType());
        }
        if (StringUtils.isNotBlank(item.getTargetingType())) {
            vo.setTargetingType(item.getTargetingType());
        }
        if (StringUtils.isNotBlank(item.getCampaignType())) {
            vo.setCampaignType(item.getCampaignType());
        }
        if (StringUtils.isNotBlank(item.getStrategy())) {
            vo.setStrategy(item.getStrategy());
        }
        if (StringUtils.isNotBlank(item.getDailyBudget())) {
            vo.setDailyBudget(item.getDailyBudget());
        }
        if (StringUtils.isNotBlank(item.getPlacementProductPage())) {
            vo.setPlacementProductPage(item.getPlacementProductPage());
        }
        if (StringUtils.isNotBlank(item.getPlacementTop())) {
            vo.setPlacementTop(item.getPlacementTop());
        }
        if (StringUtils.isNotBlank(item.getPlacementRestOfSearch())) {
            vo.setPlacementRestOfSearch(item.getPlacementRestOfSearch());
        }
        if (StringUtils.isNotBlank(item.getStartDate())) {
            vo.setStartDate(item.getStartDate());
        }
        if (StringUtils.isNotBlank(item.getEndDate())) {
            vo.setEndDate(item.getEndDate());
        }
        if (StringUtils.isNotBlank(item.getCostType())) {
            vo.setCostType(item.getCostType());
        }
        if (CampaignTypeEnum.sp.getCampaignType().equals(item.getType()) || StringUtils.isEmpty(item.getCostType())) {
            vo.setCostType("cpc");
        }

        if (StringUtils.isNotBlank(item.getPortfolioName())) {
            vo.setPortfolioName(item.getPortfolioName());
        }
        vo.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));

        vo.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
        vo.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
        vo.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
        vo.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
        vo.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
        vo.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
        vo.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
        vo.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
        vo.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
        vo.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
        vo.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
        vo.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");


        if (StringUtils.isNotBlank(item.getCreator())) {
            vo.setCreator(item.getCreator());
        }
        if (StringUtils.isNotBlank(item.getServingStatusName())) {
            vo.setServingStatusName(item.getServingStatusName());
        }
        vo.setViewImpressions(Int32Value.of(Optional.ofNullable(item.getViewImpressions()).orElse(0)));
        vo.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
        if(SBCampaignCostTypeEnum.VCPM.getCode().equals(item.getCostType())){
            vo.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0");
        }else{
            vo.setVcpm("-");
        }
        //本广告产品订单量
        vo.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
        //其他广告产品订单量
        vo.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
        //本广告产品销售额
        vo.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
        //其他广告产品销售额
        vo.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
        //广告销量
        vo.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
        //本广告产品销量
        vo.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
        //其他广告产品销量
        vo.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));
        vo.setOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0)));
        vo.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
        vo.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
        vo.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
        vo.setUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getUnitsOrderedNewToBrandFTD()).orElse(0)));
        vo.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(item.getUnitsOrderedRateNewToBrandFTD()) ? item.getUnitsOrderedRateNewToBrandFTD() : "0");
        // 花费占比
        vo.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
        // 销售额占比
        vo.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
        // 订单量占比
        vo.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
        // 销量占比
        vo.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");

        vo.setTopImpressionShare(Optional.ofNullable(item.getTopImpressionShare()).orElse(""));

        vo.setNewToBrandDetailPageViews(Optional.ofNullable(item.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
        vo.setAddToCart(Optional.ofNullable(item.getAddToCart()).map(String::valueOf).orElse("0"));
        vo.setAddToCartRate(Optional.ofNullable(item.getAddToCartRate()).map(String::valueOf).orElse("0"));
        vo.setECPAddToCart(Optional.ofNullable(item.getECPAddToCart()).map(String::valueOf).orElse("0"));
        vo.setVideo5SecondViews(Optional.ofNullable(item.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
        vo.setVideo5SecondViewRate(Optional.ofNullable(item.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
        vo.setVideoFirstQuartileViews(Optional.ofNullable(item.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
        vo.setVideoMidpointViews(Optional.ofNullable(item.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
        vo.setVideoThirdQuartileViews(Optional.ofNullable(item.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
        vo.setVideoCompleteViews(Optional.ofNullable(item.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
        vo.setVideoUnmutes(Optional.ofNullable(item.getVideoUnmutes()).map(String::valueOf).orElse("0"));
        vo.setViewabilityRate(Optional.ofNullable(item.getViewabilityRate()).map(String::valueOf).orElse("0"));
        vo.setViewClickThroughRate(Optional.ofNullable(item.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
        vo.setBrandedSearches(Optional.ofNullable(item.getBrandedSearches()).map(String::valueOf).orElse("0"));
        vo.setDetailPageViews(Optional.ofNullable(item.getDetailPageViews()).map(String::valueOf).orElse("0"));
        vo.setCumulativeReach(Optional.ofNullable(item.getCumulativeReach()).map(String::valueOf).orElse("0"));
        vo.setImpressionsFrequencyAverage(Optional.ofNullable(item.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
        vo.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
        //处理标签，取出标签名称进行导出
        String adTagName = "";
        if (CollectionUtils.isNotEmpty(item.getAdTags())) {
            List<String> adTag = item.getAdTags().stream().map(AdTag::getName).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(adTag) && adTag.size() > 1) {
                adTagName = String.join(",", adTag);
                vo.setAdTag(adTagName);
            } else {
                adTagName = String.join("", adTag);
                vo.setAdTag(Optional.of(adTagName).orElse(""));
            }
        }
        vo.setAdTag(adTagName);

        return vo.build();
    }

    private List<String> customFieldSortExport(List<CampaignDataResponse.CampaignPageVo> sourceDataList, List<String> sortFields, String currency, Integer puid, String fileName, WriteHandlerBuild build) {
        //存储文件路径urlList
        List<String> downloadUrl = new ArrayList<>();
        //数据有可能大于6万条,采用分片处理
        List<List<CampaignDataResponse.CampaignPageVo>> partition = Lists.partition(sourceDataList, Constants.EXPORT_MAX_SIZE);
        int count = 0;
        for (List<CampaignDataResponse.CampaignPageVo> partitionList : partition) {
            List<List<Object>> rows = new ArrayList<>(sourceDataList.size());
            for (CampaignDataResponse.CampaignPageVo cpVo : partitionList) {
                rows.add(buildExportRow(cpVo, sortFields, currency));
            }
            //构建表头
            List<String> headNames = new ArrayList<>(sortFields.size());
            for (String sortField : sortFields) {
                AdvertisingCampaignExportFieldEnum fieldEnum = AdvertisingCampaignExportFieldEnum.fromPoParamKey(sortField);
                if (fieldEnum == null) {
                    log.error("sortFields 包含非法字符，导出阻止，返回空, sortFields:{}", sortFields);
                    return Collections.emptyList();
                }
                headNames.add(fieldEnum.getTableColName());
            }
            //导出
            if (rows.size() > 0) {
                downloadUrl.add(excelService.exportByCustomColSort(puid, headNames, rows, fileName + "(" + (count++) + ")", build));
            }

        }
        return downloadUrl;
    }

    //组装要排除的字段
    private List<String> assembleExcludeFields(String type) {
        List<String> excludeFileds = new ArrayList<>();
        if (Constants.SD.equalsIgnoreCase(type)) {
            excludeFileds.add("topOfSearchImpressionShare");
        }
        // 单店铺过滤店铺名称
        excludeFileds.add("shopName");
        return excludeFileds;
    }

    //组装导出的数据列表：
    private List<CampaignDataResponse.CampaignPageVo> assembleDataForExport(CampaignPageParam param) {
        // 取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getShopId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);
        List<String> campaignIds = this.getQueryIdsByPageFilter(param.getPuid(), param);
        List<CampaignPageVo> allCampaignVoList = new ArrayList<>();
        if (campaignIds == null || !campaignIds.isEmpty()) {
            allCampaignVoList.addAll(cpcCampaignService.getExportAllCampaignVoList(param.getPuid(), param));
        }
        return allCampaignVoList.stream().filter(Objects::nonNull).map(CampaignPageExportTaskHandler::buildGrpcVo).collect(Collectors.toList());
    }

    private List<String> getQueryIdsByPageFilter(Integer puid, CampaignPageParam param) {
        List<String> campaignIds = null;
        //标签过滤
        if (CollectionUtils.isNotEmpty(param.getAdTagIdList())) {
            //通过标签Ids筛选广告活动Ids
            int type = 0;
            List<String> tagIds = param.getAdTagIdList().stream().map(String::valueOf).collect(Collectors.toList());
            List<Integer> shopIds = Collections.singletonList(param.getShopId());
            campaignIds = adManageTagRelationService.getRelationIdByTagIds(param.getPuid(), tagIds, type, shopIds);
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                param.setCampaignIdList(campaignIds);
            } else {
                campaignIds = Collections.emptyList();
                return campaignIds;
            }
        }
        return campaignIds;
    }

    private AdvertisingCampaignVo buildExportVo(String currency, CampaignDataResponse.CampaignPageVo cpVo) {
        //处理数据
        AdvertisingCampaignVo adverVo = new AdvertisingCampaignVo();
        adverVo.setServingStatusName(cpVo.getServingStatusName());
        adverVo.setAdOrderNum(cpVo.getAdOrderNum().getValue());
        adverVo.setImpressions(cpVo.getImpressions().getValue());
        adverVo.setTopOfSearchImpressionShare(ReportParamUtil.getExportTopIS(cpVo.getTopImpressionShare()));
        adverVo.setClicks(cpVo.getClicks().getValue());
        adverVo.setName(cpVo.getName());
        adverVo.setPortfolioName(cpVo.getPortfolioName());
        //状态
        adverVo.setState(StateEnum.getStateValue(cpVo.getState()));
        //每日预算
        adverVo.setDailyBudget(currency + cpVo.getDailyBudget());
        //点击率
        adverVo.setCtr(ExportStringUtil.modifyFormat(cpVo.getCtr()));
        //订单转换率
        adverVo.setCvr(ExportStringUtil.modifyFormat(cpVo.getCvr()));
        //ACoS
        adverVo.setAcos(ExportStringUtil.modifyFormat(cpVo.getAcos()));
        //ACoTS
        adverVo.setAcots(ExportStringUtil.modifyFormat(cpVo.getAcots()));
        adverVo.setRoas(cpVo.getRoas());
        //ASoTS
        adverVo.setAsots(ExportStringUtil.modifyFormat(cpVo.getAsots()));
        //广告花费（数据为空时展示为0.00%）
        adverVo.setAdCost(currency + ExportStringUtil.formatToNumber(cpVo.getAdCost()));
        //平均点击费用（数据为空时展示为0.00%）
        adverVo.setAdCostPerClick(currency + ExportStringUtil.getAdCostPerClick(cpVo.getAdCostPerClick()));
        //广告销售额（数据为空时展示为0.00%）
        adverVo.setAdSale(currency + ExportStringUtil.formatToNumber(cpVo.getAdSale()));
        // 花费占比
        adverVo.setAdCostPercentage(ExportStringUtil.modifyFormat(cpVo.getAdCostPercentage()));
        // 销售额占比
        adverVo.setAdSalePercentage(ExportStringUtil.modifyFormat(cpVo.getAdSalePercentage()));
        // 订单量占比
        adverVo.setAdOrderNumPercentage(ExportStringUtil.modifyFormat(cpVo.getAdOrderNumPercentage()));
        // 销量占比
        adverVo.setOrderNumPercentage(ExportStringUtil.modifyFormat(cpVo.getOrderNumPercentage()));
        if (CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(cpVo.getType())) {
            //推广类型
            adverVo.setCampaignType(CampaignEnum.sponsoredBrands.getCampaignValue());
            //投放类型
            adverVo.setTargetingType(TargetingEnum.manual.getTargetingValue());
        } else if (CampaignTypeEnum.sd.getCampaignType().equalsIgnoreCase(cpVo.getType())) {
            //推广类型
            adverVo.setCampaignType(CampaignEnum.sponsoredDisplay.getCampaignValue());
            //投放类型
            adverVo.setTargetingType(TargetingEnum.manual.getTargetingValue());
        } else {
            //推广类型
            adverVo.setCampaignType(CampaignEnum.getCampaignValue(cpVo.getCampaignType()));
            //投放类型
            adverVo.setTargetingType(TargetingEnum.getTargetingValue(cpVo.getTargetingType()));
        }

        //广告活动竞价策略
        adverVo.setStrategy(StrategyEnum.getStrategyValue(cpVo.getStrategy()));
        //搜索结果顶部(首页)广告位
        adverVo.setPlacementTop(ExportStringUtil.getPlacementProductPage(cpVo.getPlacementTop()));
        //产品页面广告位
        adverVo.setPlacementProductPage(ExportStringUtil.getPlacementProductPage(cpVo.getPlacementProductPage()));
        //其他搜索位置
        adverVo.setPlacementRestOfSearch(ExportStringUtil.getPlacementRestOfSearchPage(cpVo.getPlacementRestOfSearch()));
        adverVo.setStartDate(cpVo.getStartDate());
        adverVo.setEndDate(ExportStringUtil.getDateState(cpVo.getEndDate()));
        adverVo.setCreator(cpVo.getCreator());
        adverVo.setViewImpressions(cpVo.getViewImpressions().getValue());
        adverVo.setCpa(currency + ExportStringUtil.formatToNumber(cpVo.getCpa()));
        adverVo.setVcpm("-".equals(cpVo.getVcpm()) ? "-" : currency + ExportStringUtil.formatToNumber(cpVo.getVcpm()));
        //本广告产品订单量
        adverVo.setAdSaleNum(cpVo.getAdSaleNum().getValue());
        //其他广告产品订单量
        adverVo.setAdOtherOrderNum(cpVo.getAdOtherOrderNum().getValue());
        //本广告产品销售额
        adverVo.setAdSales(currency + ExportStringUtil.formatToNumber(cpVo.getAdSales()));
        //其他广告产品销售额
        adverVo.setAdOtherSales(currency + ExportStringUtil.formatToNumber(cpVo.getAdOtherSales()));
        //广告销量
        adverVo.setOrderNum(cpVo.getOrderNum().getValue());
        if (CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(cpVo.getType())) {
            //本广告产品销量
            adverVo.setAdSelfSaleNum("0");
            //其他广告产品销量
            adverVo.setAdOtherSaleNum("0");
        } else if (CampaignTypeEnum.sd.getCampaignType().equalsIgnoreCase(cpVo.getType())) {
            //本广告产品销量
            adverVo.setAdSelfSaleNum("-");
            //其他广告产品销量
            adverVo.setAdOtherSaleNum("-");
        } else {
            //本广告产品销量
            adverVo.setAdSelfSaleNum(String.valueOf(cpVo.getAdSelfSaleNum().getValue()));
            //其他广告产品销量
            adverVo.setAdOtherSaleNum(String.valueOf(cpVo.getAdOtherSaleNum().getValue()));
        }

        adverVo.setOrdersNewToBrand14d(cpVo.getOrdersNewToBrandFTD().getValue());
        adverVo.setOrderRateNewToBrand14d(cpVo.getOrderRateNewToBrandFTD());
        adverVo.setSalesNewToBrand14d(currency + ExportStringUtil.formatToNumber(cpVo.getSalesNewToBrandFTD()));
        adverVo.setSalesRateNewToBrand14d(cpVo.getSalesRateNewToBrandFTD());
        adverVo.setUnitsOrderedNewToBrand14d(cpVo.getUnitsOrderedNewToBrandFTD().getValue());
        adverVo.setUnitsOrderedRateNewToBrand14d(cpVo.getUnitsOrderedRateNewToBrandFTD());

        adverVo.setNewToBrandDetailPageViews(cpVo.getNewToBrandDetailPageViews());
        adverVo.setAddToCart(cpVo.getAddToCart());
        adverVo.setAddToCartRate(ExportStringUtil.modifyFormat(cpVo.getAddToCartRate()));
        adverVo.setEcpAddToCart(currency + ExportStringUtil.formatToNumber(cpVo.getECPAddToCart()));
        adverVo.setVideo5SecondViews(cpVo.getVideo5SecondViews());
        adverVo.setVideo5SecondViewRate(ExportStringUtil.modifyFormat(cpVo.getVideo5SecondViewRate()));
        adverVo.setVideoFirstQuartileViews(cpVo.getVideoFirstQuartileViews());
        adverVo.setVideoMidpointViews(cpVo.getVideoMidpointViews());
        adverVo.setVideoThirdQuartileViews(cpVo.getVideoThirdQuartileViews());
        adverVo.setVideoCompleteViews(cpVo.getVideoCompleteViews());
        adverVo.setVideoUnmutes(cpVo.getVideoUnmutes());
        adverVo.setViewabilityRate(ExportStringUtil.modifyFormat(cpVo.getViewabilityRate()));
        adverVo.setViewClickThroughRate(ExportStringUtil.modifyFormat(cpVo.getViewClickThroughRate()));
        adverVo.setBrandedSearches(cpVo.getBrandedSearches());
        adverVo.setDetailPageViews(cpVo.getDetailPageViews());
        adverVo.setCumulativeReach(cpVo.getCumulativeReach());
        adverVo.setImpressionsFrequencyAverage(cpVo.getImpressionsFrequencyAverage());
        adverVo.setAdvertisingUnitPrice(currency + ExportStringUtil.formatToNumber(cpVo.getAdvertisingUnitPrice()));
        adverVo.setAdTag(cpVo.getAdTag());
        adverVo.setCostType(cpVo.getCostType());
        return adverVo;
    }

    //构建单行
    private List<Object> buildExportRow(CampaignDataResponse.CampaignPageVo cpVo, List<String> sortFields, String currency) {
        List<Object> cols = new ArrayList<>(AdvertisingCampaignExportFieldEnum.values().length);
        for (String fieldName : sortFields) {
            AdvertisingCampaignExportFieldEnum fieldEnum = AdvertisingCampaignExportFieldEnum.fromPoParamKey(fieldName);
            if (fieldEnum == null) {
                return Collections.emptyList();
            }
            Object value = getObjectByField(cpVo, fieldEnum, currency);
            cols.add(value);
        }
        return cols;
    }

    //映射列
    private Object getObjectByField(CampaignDataResponse.CampaignPageVo cpVo, AdvertisingCampaignExportFieldEnum fieldEnum, String currency) {
        Object value;
        switch (fieldEnum) {
            case NAME:
                value = cpVo.getName();
                break;
            case STATE:
                value = StateEnum.getStateValue(cpVo.getState());
                break;
            case PORTFOLIO_NAME:
                value = cpVo.getPortfolioName();
                break;
            case SERVING_STATUS_NAME:
                value = cpVo.getServingStatusName();
                break;
            case STRATEGY:
                value = StrategyEnum.getStrategyValue(cpVo.getStrategy());
                break;
            case DAILY_BUDGET:
                value = currency + cpVo.getDailyBudget();
                break;
            case CAMPAIGN_TYPE:
                if (CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(cpVo.getType())) {
                    value = CampaignEnum.sponsoredBrands.getCampaignValue();
                } else if (CampaignTypeEnum.sd.getCampaignType().equalsIgnoreCase(cpVo.getType())) {
                    value = CampaignEnum.sponsoredDisplay.getCampaignValue();
                } else {
                    value = CampaignEnum.getCampaignValue(cpVo.getCampaignType());
                }
                break;
            case TARGETING_TYPE:
                if (CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(cpVo.getType()) ||
                    CampaignTypeEnum.sd.getCampaignType().equalsIgnoreCase(cpVo.getType())) {
                    value = TargetingEnum.manual.getTargetingValue();
                } else {
                    value = TargetingEnum.getTargetingValue(cpVo.getTargetingType());
                }
                break;
            case PLACEMENT_TOP:
                value = ExportStringUtil.getPlacementProductPage(cpVo.getPlacementTop());
                break;
            case PLACEMENT_PRODUCT_PAGE:
                value = ExportStringUtil.getPlacementProductPage(cpVo.getPlacementProductPage());
                break;
            case PLACEMENT_REST_OF_SEARCH:
                value = ExportStringUtil.getPlacementRestOfSearchPage(cpVo.getPlacementRestOfSearch());
                break;
            case START_DATE:
                value = cpVo.getStartDate();
                break;
            case END_DATE:
                value = ExportStringUtil.getDateState(cpVo.getEndDate());
                break;
            case AD_COST:
                value = currency + ExportStringUtil.formatToNumber(cpVo.getAdCost());
                break;
            case AD_COST_PERCENTAGE:
                value = ExportStringUtil.modifyFormat(cpVo.getAdCostPercentage());
                break;
            case IMPRESSIONS:
                value = cpVo.getImpressions().getValue();
                break;
            case TOP_OF_SEARCH_IMPRESSION_SHARE:
                value = ReportParamUtil.getExportTopIS(cpVo.getTopImpressionShare());
                break;
            case CLICKS:
                value = cpVo.getClicks().getValue();
                break;
            case VIEW_IMPRESSIONS:
                value = cpVo.getViewImpressions().getValue();
                break;
            case CPA:
                value = currency + ExportStringUtil.formatToNumber(cpVo.getCpa());
                break;
            case AD_COST_PER_CLICK:
                value = currency + ExportStringUtil.getAdCostPerClick(cpVo.getAdCostPerClick());
                break;
            case VCPM:
                value = "-".equals(cpVo.getVcpm()) ? "-" : currency + ExportStringUtil.formatToNumber(cpVo.getVcpm());
                break;
            case CTR:
                value = ExportStringUtil.modifyFormat(cpVo.getCtr());
                break;
            case CVR:
                value = ExportStringUtil.modifyFormat(cpVo.getCvr());
                break;
            case ACOS:
                value = ExportStringUtil.modifyFormat(cpVo.getAcos());
                break;
            case ROAS:
                value = cpVo.getRoas();
                break;
            case ACOTS:
                value = ExportStringUtil.modifyFormat(cpVo.getAcots());
                break;
            case ASOTS:
                value = ExportStringUtil.modifyFormat(cpVo.getAsots());
                break;
            case ADVERTISING_UNIT_PRICE:
                value = currency + ExportStringUtil.formatToNumber(cpVo.getAdvertisingUnitPrice());
                break;
            case AD_ORDER_NUM:
                value = cpVo.getAdOrderNum().getValue();
                break;
            case AD_ORDER_NUM_PERCENTAGE:
                value = ExportStringUtil.modifyFormat(cpVo.getAdOrderNumPercentage());
                break;
            case AD_SALE_NUM:
                value = cpVo.getAdSaleNum().getValue();
                break;
            case AD_OTHER_ORDER_NUM:
                value = cpVo.getAdOtherOrderNum().getValue();
                break;
            case AD_SALE:
                value = currency + ExportStringUtil.formatToNumber(cpVo.getAdSale());
                break;
            case AD_SALE_PERCENTAGE:
                value = ExportStringUtil.modifyFormat(cpVo.getAdSalePercentage());
                break;
            case AD_SALES:
                value = currency + ExportStringUtil.formatToNumber(cpVo.getAdSales());
                break;
            case AD_OTHER_SALES:
                value = currency + ExportStringUtil.formatToNumber(cpVo.getAdOtherSales());
                break;
            case ORDER_NUM:
                value = cpVo.getOrderNum().getValue();
                break;
            case ORDER_NUM_PERCENTAGE:
                value = ExportStringUtil.modifyFormat(cpVo.getOrderNumPercentage());
                break;
            case AD_SELF_SALE_NUM:
                if (CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(cpVo.getType())) {
                    value = "0";
                } else if (CampaignTypeEnum.sd.getCampaignType().equalsIgnoreCase(cpVo.getType())) {
                    value = "-";
                } else {
                    value = String.valueOf(cpVo.getAdSelfSaleNum().getValue());
                }
                break;
            case AD_OTHER_SALE_NUM:
                if (CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(cpVo.getType())) {
                    value = "0";
                } else if (CampaignTypeEnum.sd.getCampaignType().equalsIgnoreCase(cpVo.getType())) {
                    value = "-";
                } else {
                    value = String.valueOf(cpVo.getAdOtherSaleNum().getValue());
                }
                break;
            case ORDERS_NEW_TO_BRAND_14D:
                value = cpVo.getOrdersNewToBrandFTD().getValue();
                break;
            case ORDER_RATE_NEW_TO_BRAND_14D:
                value = cpVo.getOrderRateNewToBrandFTD();
                break;
            case SALES_NEW_TO_BRAND_14D:
                value = currency + ExportStringUtil.formatToNumber(cpVo.getSalesNewToBrandFTD());
                break;
            case SALES_RATE_NEW_TO_BRAND_14D:
                value = cpVo.getSalesRateNewToBrandFTD();
                break;
            case UNITS_ORDERED_NEW_TO_BRAND_14D:
                value = cpVo.getUnitsOrderedNewToBrandFTD().getValue();
                break;
            case UNITS_ORDERED_RATE_NEW_TO_BRAND_14D:
                value = cpVo.getUnitsOrderedRateNewToBrandFTD();
                break;
            case NEW_TO_BRAND_DETAIL_PAGE_VIEWS:
                value = cpVo.getNewToBrandDetailPageViews();
                break;
            case ADD_TO_CART:
                value = cpVo.getAddToCart();
                break;
            case ADD_TO_CART_RATE:
                value = ExportStringUtil.modifyFormat(cpVo.getAddToCartRate());
                break;
            case ECP_ADD_TO_CART:
                value = currency + ExportStringUtil.formatToNumber(cpVo.getECPAddToCart());
                break;
            case VIDEO_5_SECOND_VIEWS:
                value = cpVo.getVideo5SecondViews();
                break;
            case VIDEO_5_SECOND_VIEW_RATE:
                value = ExportStringUtil.modifyFormat(cpVo.getVideo5SecondViewRate());
                break;
            case VIDEO_FIRST_QUARTILE_VIEWS:
                value = cpVo.getVideoFirstQuartileViews();
                break;
            case VIDEO_MIDPOINT_VIEWS:
                value = cpVo.getVideoMidpointViews();
                break;
            case VIDEO_THIRD_QUARTILE_VIEWS:
                value = cpVo.getVideoThirdQuartileViews();
                break;
            case VIDEO_COMPLETE_VIEWS:
                value = cpVo.getVideoCompleteViews();
                break;
            case VIDEO_UNMUTES:
                value = cpVo.getVideoUnmutes();
                break;
            case VIEWABILITY_RATE:
                value = ExportStringUtil.modifyFormat(cpVo.getViewabilityRate());
                break;
            case VIEW_CLICK_THROUGH_RATE:
                value = ExportStringUtil.modifyFormat(cpVo.getViewClickThroughRate());
                break;
            case BRANDED_SEARCHES:
                value = cpVo.getBrandedSearches();
                break;
            case DETAIL_PAGE_VIEWS:
                value = cpVo.getDetailPageViews();
                break;
            case CUMULATIVE_REACH:
                value = cpVo.getCumulativeReach();
                break;
            case IMPRESSIONS_FREQUENCY_AVERAGE:
                value = cpVo.getImpressionsFrequencyAverage();
                break;
            case CREATOR:
                value = cpVo.getCreator();
                break;
            case AD_TAG:
                value = cpVo.getAdTag();
                break;
            case COST_TYPE:
                value = cpVo.getCostType();
                break;
            default:
                value = null;
        }

        return value;
    }
}
