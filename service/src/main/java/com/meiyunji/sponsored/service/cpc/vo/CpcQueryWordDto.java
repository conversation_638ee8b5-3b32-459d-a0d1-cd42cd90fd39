package com.meiyunji.sponsored.service.cpc.vo;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.service.cpc.po.SearchQueryTagParam;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
@ApiModel
public class CpcQueryWordDto {

    private String query;

    @ApiModelProperty(required = true,value = "puid")
    private Integer puid;
    private String uuid;
    @ApiModelProperty(required = true,value = "门店ID")
    private Integer shopId;
    private List<Integer> shopIdList;
    @ApiModelProperty(required = true,value = "uid")
    private Integer uid;
    @ApiModelProperty(required = true,value = "ip地址")
    private String loginIp;

    @ApiModelProperty(required = true,value = "区域ID")
    private String marketplaceId;

    private String searchTermType;
    @ApiModelProperty(value = "匹配类型 auto,manual")
    private String matchType; //匹配类型

    private List<String> spMatchTypes;
    private List<String> sbMatchTypes;

    @ApiModelProperty(value = "开始时间",required = true)
    private String start; //yyyy-MM-dd

    @ApiModelProperty(value = "结束时间",required = true)
    private String end; //yyyy-MM-dd

    @ApiModelProperty(value = "搜索类型 exact blur")
    private String searchType; //精确（exact），模糊搜索（blur）

    @ApiModelProperty(value = "用户搜索词（query），关键词（keywordText）,商品定位（targetingExpression）")
    private String searchField; //用户搜索词（query），关键词（keywordText）,商品定位（targetingExpression）

    @ApiModelProperty(value = "搜索内容")
    private String searchValue; //搜索内容

    @ApiModelProperty(value = "排序字段")
    private String orderField; //排序字段

    @ApiModelProperty(value = "desc，asc")
    private String orderValue; //desc，asc

    //兼容前端,统一传参orderType, orderValue用户搜索词中保留
    @ApiModelProperty(value = "兼容前端,统一传参orderType orderValue用户搜索词中保留; desc，asc")
    private String orderType; //desc，asc

    @ApiModelProperty(value = "状态")
    private String state;

    @ApiModelProperty(value = "状态(兼容前端)")
    private String status;

    @ApiModelProperty(value = "广告活动ID")
    private String campaignId;

    @ApiModelProperty(value = "广告组ID")
    private String groupId;
    private List<String> groupIdList;

    @ApiModelProperty(value = "广告活动ID数组")
    private List<String> campaignIds;

    @ApiModelProperty("广告组合ID")
    private String portfolioId;
    private List<String> portfolioIds;

    @ApiModelProperty(value = "广告组合下的活动id")
    private List<String> campaignIdList;

    @ApiModelProperty(value = "广告组ID")
    private List<String> adGroupIdList;

    @ApiModelProperty(value = "广告组ID")
    private List<String> queryList;

    @ApiModelProperty("导出排序字段")
    private String exportSortField;

    @ApiModelProperty("冻结前num列")
    private Integer freezeNum;

    //高级搜索
    @ApiModelProperty(value = "默认true")
    private Boolean useAdvanced = true; //true表示使用高级搜索，暂时不生效，都去走这个

    //高级搜索值
    @ApiModelProperty(value = "高级搜索展示量最小值")
    private Integer impressionsMin;  //展示量
    @ApiModelProperty(value = "高级搜索展示量最大值")
    private Integer impressionsMax;
    @ApiModelProperty(value = "高级搜索点击量小值")
    private Integer clicksMin;  //点击量
    @ApiModelProperty(value = "高级搜索点击量大值")
    private Integer clicksMax;
    @ApiModelProperty(value = "高级搜索点击率小值")
    private BigDecimal clickRateMin;  //点击率
    @ApiModelProperty(value = "高级搜索点击率大值")
    private BigDecimal clickRateMax;
    @ApiModelProperty(value = "高级搜索花费小值")
    private BigDecimal costMin; //花费
    @ApiModelProperty(value = "高级搜索花费大值")
    private BigDecimal costMax;
    @ApiModelProperty(value = "高级搜索cpc小值")
    private BigDecimal cpcMin;  //cpc
    @ApiModelProperty(value = "高级搜索cpc大值")
    private BigDecimal cpcMax;
    @ApiModelProperty(value = "高级搜索广告订单量小值")
    private Integer orderNumMin;  //广告订单量
    @ApiModelProperty(value = "高级搜索广告订单量大值")
    private Integer orderNumMax;
    @ApiModelProperty(value = "高级搜索sales小值")
    private BigDecimal salesMin;  //sales
    @ApiModelProperty(value = "高级搜索sales大值")
    private BigDecimal salesMax;
    @ApiModelProperty(value = "高级搜索acos小值")
    private BigDecimal acosMin;  //acos
    @ApiModelProperty(value = "高级搜索acos大值")
    private BigDecimal acosMax;
    @ApiModelProperty(value = "高级搜索roas小值")
    private BigDecimal roasMin;   //roas
    @ApiModelProperty(value = "高级搜索roas大值")
    private BigDecimal roasMax;
    @ApiModelProperty(value = "高级搜索广告订单转化率小值")
    private BigDecimal salesConversionRateMin;  //订单转化率
    @ApiModelProperty(value = "高级搜索广告订单转化率大值")
    private BigDecimal salesConversionRateMax;
    @ApiModelProperty(value = "高级搜索acots小值")
    private BigDecimal acotsMin;  //acots
    @ApiModelProperty(value = "高级搜索acots大值")
    private BigDecimal acotsMax;
    @ApiModelProperty(value = "高级搜索asots小值")
    private BigDecimal asotsMin;  //acos
    @ApiModelProperty(value = "高级搜索asots大值")
    private BigDecimal asotsMax;

    @ApiModelProperty(value = "高级搜索投放类型")
    private String filterTargetType;
    @ApiModelProperty(value = "高级搜索匹配类型")
    private String filterMatchType;
    @ApiModelProperty(value = "高级搜索添加属性")
    private String filterAddProperty;
    private List<String> excludList;


    @ApiModelProperty(value = "高级搜索CPA小值")
    private BigDecimal cpaMin;  //CPA
    @ApiModelProperty(value = "高级搜索CPA大值")
    private BigDecimal cpaMax;
    @ApiModelProperty(value = "高级搜索销售额小值")
    private BigDecimal totalSalesMin;  //销售额
    @ApiModelProperty(value = "高级搜索销售额大值")
    private BigDecimal totalSalesMax;

    @ApiModelProperty(value = "高级搜索店铺销售额")
    private BigDecimal shopSales;

    @ApiModelProperty(value = "类型 sb,sp")
    private String type;

    /*******************高级搜索新增查询字段***************************/
    @ApiModelProperty(value = "可见展示次数最小值")
    private Integer viewImpressionsMin;
    @ApiModelProperty(value = "可见展示次数最大值")
    private Integer viewImpressionsMax;


    @ApiModelProperty(value = "vcpm最小值")
    private BigDecimal vcpmMin;
    @ApiModelProperty(value = "vcpm最大值")
    private BigDecimal vcpmMax;


    @ApiModelProperty(value = "本广告产品订单量最小值")
    private Integer adSaleNumMin;
    @ApiModelProperty(value = "本广告产品订单量最大值")
    private Integer adSaleNumMax;


    @ApiModelProperty(value = "其他产品广告订单量最小值")
    private Integer adOtherOrderNumMin;
    @ApiModelProperty(value = "其他产品广告订单量最大值")
    private Integer adOtherOrderNumMax;


    @ApiModelProperty(value = "本广告产品销售额最小值")
    private BigDecimal adSalesMin;
    @ApiModelProperty(value = "本广告产品销售额最大值")
    private BigDecimal adSalesMax;


    @ApiModelProperty(value = "其他产品广告销售额最小值")
    private BigDecimal adOtherSalesMin;
    @ApiModelProperty(value = "其他产品广告销售额最大值")
    private BigDecimal adOtherSalesMax;


    @ApiModelProperty(value = "本廣告产品销量最小值")
    private Integer adSelfSaleNumMin;
    @ApiModelProperty(value = "本廣告产品销量最大值")
    private Integer adSelfSaleNumMax;


    @ApiModelProperty(value = "其他产品广告销量最小值")
    private Integer adOtherSaleNumMin;
    @ApiModelProperty(value = "其他产品广告销量最大值")
    private Integer adOtherSaleNumMax;


    @ApiModelProperty(value = "“品牌新买家”订单量最小值")
    private Integer ordersNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”订单量最大值")
    private Integer ordersNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”订单百分比最小值")
    private BigDecimal orderRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”订单百分比最大值")
    private BigDecimal orderRateNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销售额最小值")
    private BigDecimal salesNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销售额最大值")
    private BigDecimal salesNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销售额百分比最小值")
    private BigDecimal salesRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销售额百分比最大值")
    private BigDecimal salesRateNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销量最小值")
    private Integer unitsOrderedNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销量最大值")
    private Integer unitsOrderedNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销量百分比最小值")
    private BigDecimal unitsOrderedRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销量百分比最大值")
    private BigDecimal unitsOrderedRateNewToBrandFTDMax;


    @ApiModelProperty(value = "广告销量最小值")
    private Integer adSalesTotalMin;
    @ApiModelProperty(value = "广告销量最大值")
    private Integer adSalesTotalMax;


    @ApiModelProperty(value = "“品牌新买家”订单转化率")
    private BigDecimal brandNewBuyerOrderConversionRateMin;

    @ApiModelProperty(value = "“品牌新买家”订单转化率")
    private BigDecimal brandNewBuyerOrderConversionRateMax;

    @ApiModelProperty("5秒观看次数最小值")
    private Integer video5SecondViewsMin;
    @ApiModelProperty("5秒观看次数最大值")
    private Integer video5SecondViewsMax;

    @ApiModelProperty("视频完整播放次数最小值")
    private Integer videoCompleteViewsMin;
    @ApiModelProperty("视频完整播放次数最大值")
    private Integer videoCompleteViewsMax;

    @ApiModelProperty("观看率最小值")
    private BigDecimal viewabilityRateMin;
    @ApiModelProperty("观看率最大值")
    private BigDecimal viewabilityRateMax;

    @ApiModelProperty("观看点击率最小值")
    private BigDecimal viewClickThroughRateMin;
    @ApiModelProperty("观看点击率最大值")
    private BigDecimal viewClickThroughRateMax;

    @ApiModelProperty("广告笔单价最小值")
    private BigDecimal advertisingUnitPriceMin;
    @ApiModelProperty("广告笔单价最大值")
    private BigDecimal advertisingUnitPriceMax;

    @ApiModelProperty("搜索词排名最小值")
    private Integer searchFrequencyRankMin;
    @ApiModelProperty("搜索词排名最大值")
    private Integer searchFrequencyRankMax;

    @ApiModelProperty("搜索词排名周变化率最小值")
    private BigDecimal weekRatioMin;
    @ApiModelProperty("搜索词排名周变化率最大值")
    private BigDecimal weekRatioMax;

    private BigDecimal sumCost;
    private BigDecimal sumAdSale;
    private int sumAdOrderNum;
    private int sumOrderNum;

    @ApiModelProperty(value = "投放Id")
    private String targetId;
    @ApiModelProperty(value = "关键词Id")
    private String keywordId;

    //环比相关参数
    private Boolean isCompare;
    private String compareStartDate;
    private String compareEndDate;


    @ApiModelProperty(value = "广告活动服务状态筛选，多个使用逗号分隔")
    private String servingStatus;

    private String pageSign;

    private String wordRoot;

    private List<String> queryIds;

    private List<String> queryWordTagTypeList;
    private String queryWordTargetType;

    private List<SearchQueryTagParam> searchQueryTagParamList;

    // 站点最近的周排名日期
    private String lastWeekSearchTermsRankDate;

    /** 查询数据类型 {@link com.meiyunji.sponsored.service.enums.SearchDataTypeEnum}*/
    @ApiModelProperty("查询数据类型")
    private Integer searchDataType;

    // 查询是否与aba排名表联表查询
    private boolean queryJoinSearchTermsRank;

    private List<String> searchTermIds;

    /**
     *  只查询数量 简化查询
     */
    private Boolean onlyCount;

    @ApiModelProperty(value = "广告策略类型")
    private List<String> adStrategyTypeList;
    @ApiModelProperty(value = "经过广告策略筛选后的广告投放id集合")
    private List<String> autoRuleIds;
    @ApiModelProperty(value = "经过广告策略筛选后的广告组合id集合")
    private List<String> autoRuleGroupIds;

    public List<String> getSearchVelueList(){
        if(StringUtils.isNotBlank(this.searchValue)){
            return com.meiyunji.sponsored.common.util.StringUtil.splitStr(this.searchValue.trim(),",");
        }
        return new ArrayList<>();
    }

    //关键词投放-》增加批量搜索
    public List<String> getListSearchValue(){
        if(StringUtils.isNotBlank(this.searchValue)){
            return com.meiyunji.sponsored.common.util.StringUtil.splitStr(this.searchValue.trim(),"%±%");
        }
        return new ArrayList<>();
    }

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum SearchFieldEnum {
        QUERY("query", "用户搜索词"),
        TARGETING_EXPRESSION("targetingExpression", "商品定位");

        private String value;
        private String desc;

        public static CpcQueryWordDto.SearchFieldEnum getSearchField(String value) {
            CpcQueryWordDto.SearchFieldEnum[] values = values();
            for (CpcQueryWordDto.SearchFieldEnum e : values) {
                if (e.getValue().equalsIgnoreCase(value)) {
                    return e;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum QueryWordTagTypeEnum {
        EXACT("isExact", "投-精确", Constants.EXACT),
        BROAD("isBroad", "投-广泛", Constants.BROAD),
        PHRASE("isPhrase", "投-词组", Constants.PHRASE),
        NEGATIVE_EXACT("isNegativeExact", "否精确", Constants.NEGATIVEEXACT),
        NEGATIVE_PHRASE("isNegativePhrase", "否-词组", Constants.NEGATIVEPHRASE),
        TARGET_ASIN("isTargetAsin", "投ASIN", "asin"),
        NE_TARGET_ASIN("isNeTargetAsin", "否ASIN", "negativeAsin"),
        ;

        private String value;
        private String desc;
        private String type;

        public static CpcQueryWordDto.QueryWordTagTypeEnum getSearchField(String value) {
            CpcQueryWordDto.QueryWordTagTypeEnum[] values = values();
            for (CpcQueryWordDto.QueryWordTagTypeEnum e : values) {
                if (e.getValue().equalsIgnoreCase(value)) {
                    return e;
                }
            }
            return null;
        }

        public static List<String> getAllSearchField() {
            return Arrays.stream(values()).map(QueryWordTagTypeEnum::getValue).collect(Collectors.toList());
        }

        public static List<String> getKeywordSearchField() {
            return Lists.newArrayList(EXACT.getValue(), BROAD.getValue(), PHRASE.getValue());
        }

        public static List<String> getTargetSearchField() {
            return Lists.newArrayList(TARGET_ASIN.getValue());
        }

    }
}
