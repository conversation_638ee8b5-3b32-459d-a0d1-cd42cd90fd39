package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolioDorisAllReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolioDorisSumReport;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.cpc.vo.PortfolioPageParam;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdPortfolio;
import com.meiyunji.sponsored.service.newDashboard.dto.CampaignOrGroupOrPortfolioDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTopDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByRateEnum;

import java.util.List;

/**
 * 广告组合(OdsAmazonAdPortfolio)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:12
 */
public interface IOdsAmazonAdPortfolioDao extends IDorisBaseDao<OdsAmazonAdPortfolio> {

    String portfolioQuerySql(Integer puid, List<Integer> shopIdList,
                             List<String> marketplaceIdList, List<String> campaignIdList,
                             String currency, String startDate,
                             String endDate, List<Object> argsList, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds, List<String> campaignIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum);

    List<CampaignOrGroupOrPortfolioDto> queryAdPortfolioCharts(Integer puid, List<Integer> shopIdList,
                                                               List<String> marketplaceIdList, List<String> portfolioIdList,
                                                               String currency, String startDate,
                                                               String endDate);
    List<DashboardAdTopDataDto> queryAdPortfolioYoyOrMomTop(String subSqlA, String subSqlB,
                                                            List<Object> queryParam, DashboardDataFieldEnum dataField,
                                                            DashboardOrderByRateEnum orderField, String orderBy,
                                                            Integer limit, Boolean noZero);


    List<Integer> getShopIdsByPortfolioIds(int puid, List<Integer> shopIdList, List<String> portfolioIds);

    /**
     * 获取店铺下广告组合数量
     */
    Integer countPortfolioList(int puid, List<Integer> shopIdList, List<String> portfolioIds);


    /**
     * 根据广告组合Id查询
     */
    List<OdsAmazonAdPortfolio> listByPortfolioId(Integer puid, List<Integer> shopIdList ,List<String> portfolioIds);
    /**
     * 多店铺分页查询广告组合
     */
    Page<OdsAmazonAdPortfolio> listMultiplePortfolioPage(Integer puid, PortfolioPageParam param);

    /**
     * 快速查找指定广告组合Id
     */
    List<String> listPortfolioIdByName(Integer puid, PortfolioPageParam param);

    /**
     * 获取广告组合 全部指标
     */
    AdMetricDto getSumAdMetric(Integer puid, PortfolioPageParam param);

    /**
     * 根据广告组合按照日期进行汇总
     */
    List<AmazonAdPortfolioDorisSumReport> getAllPortfolioAggregateByDate(Integer puid, PortfolioPageParam param, Boolean rate);

    /**
     * 获取广告组合的广告活动数量
     */
    Integer getPortfolioCampaignIdNum(Integer puid, PortfolioPageParam param);

}

