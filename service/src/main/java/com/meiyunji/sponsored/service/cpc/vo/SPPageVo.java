package com.meiyunji.sponsored.service.cpc.vo;

import lombok.Data;

import java.util.List;

/**
 * Created by xp on 2021/4/14.
 * 可推广的产品列表页数据
 */
@Data
public class SPPageVo {

    private Integer id;
    private Integer shopId;
    private String sku;
    private String asin;
    private String onlineStatus;
    private String title;
    private String imgUrl;
    private String domain;
    private Boolean isAdded; // 是否在同一广告组已经添加过了
    private List<SPPageVo> childList;
    private String marketplaceId;
    private Boolean isMeetConditions; //是否符合接口添加条件
    private String eligibilityStatus;//合格状态

    //用于批量创建时命名模板的父asin，如果isVariation为0即无父asin则取asin，如果isVariation为2即为子体则取parentAsin
    private String batchCreateParentAsin;
}
