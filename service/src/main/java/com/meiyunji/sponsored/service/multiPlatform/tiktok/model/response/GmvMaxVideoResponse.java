package com.meiyunji.sponsored.service.multiPlatform.tiktok.model.response;

import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.GmvMaxVideoItem;
import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
public class GmvMaxVideoResponse {

    private List<GmvMaxVideoItem> itemList;
    private Integer pageNum;
    private Integer pageSize;
    private Integer totalPage;
    private Integer totalNum;

    public static GmvMaxVideoResponse emptyInstance(int pageNum, int pageSize) {
        GmvMaxVideoResponse resp = new GmvMaxVideoResponse();
        resp.setItemList(Collections.emptyList());
        resp.setPageNum(pageNum);
        resp.setPageSize(pageSize);
        resp.setTotalPage(0);
        resp.setTotalNum(0);
        return resp;
    }


}
