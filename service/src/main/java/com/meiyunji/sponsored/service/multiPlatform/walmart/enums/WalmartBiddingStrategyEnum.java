package com.meiyunji.sponsored.service.multiPlatform.walmart.enums;

import lombok.Getter;

/**
 * @author: ys
 * @date: 2025/4/18 14:38
 * @describe:
 */
@Getter
public enum WalmartBiddingStrategyEnum {
    DYNAMIC("DYNAMIC","动态出价"),
    FIXED("FIXED", "固定出价"),
    ;
    private String code;
    private String desc;

    WalmartBiddingStrategyEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WalmartBiddingStrategyEnum getWalmartBiddingStrategyEnumByCode (String code) {
        for (WalmartBiddingStrategyEnum en : WalmartBiddingStrategyEnum.values()) {
            if (en.code.equalsIgnoreCase(code)) {
                return en;
            }
        }
        return null;
    }
}
