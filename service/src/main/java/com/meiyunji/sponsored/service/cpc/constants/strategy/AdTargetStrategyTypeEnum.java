package com.meiyunji.sponsored.service.cpc.constants.strategy;

import cn.hutool.core.collection.CollectionUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 广告投放策略枚举
 * @author: zzh
 * @create: 2024-05-07 11:01
 */
@Getter
public enum AdTargetStrategyTypeEnum {

    NONE("none", CollectionUtil.newArrayList(3,4,5,8),"未使用"),
    BID_PRICING("bidPricing", null,"分时竞价"),
    BID_AUTO("bidAuto", CollectionUtil.newArrayList(3,4),"自动竞价"),
    STATE_AUTO("stateAuto", CollectionUtil.newArrayList(5),"自动启停"),
    ADD_NOT_TARGET_AUTO("addNotTargetAuto", CollectionUtil.newArrayList(8),"自动加否"),
    ;

    private String code;
    private List<Integer> operationTypeList;
    private String desc;

    AdTargetStrategyTypeEnum(String code, List<Integer> operationTypeList, String desc) {
        this.code = code;
        this.operationTypeList = operationTypeList;
        this.desc = desc;
    }

    public static Map<Integer, String> getStrategyMap(){
        Map<Integer, String> map = new HashMap<>(16);
        map.put(3,"bidAuto");
        map.put(4,"bidAuto");
        map.put(5,"stateAuto");
        map.put(8,"addNotTargetAuto");
        return map;
    }

    public static AdTargetStrategyTypeEnum getEnumByCode(String code) {
        for (AdTargetStrategyTypeEnum value : AdTargetStrategyTypeEnum.values()) {
            if(value.getCode().equals(code)){
                return value;
            }
        }
        return null;
    }

    public static List<Integer> operationTypeList(List<String> adStrategyType) {
        List<Integer> operationTypeList = new ArrayList<>();
        if(CollectionUtil.isEmpty(adStrategyType)) {
            return operationTypeList;
        }
        if(adStrategyType.size() == AdTargetStrategyTypeEnum.values().length) {
            return operationTypeList;
        }
        if(adStrategyType.contains(AdTargetStrategyTypeEnum.NONE.getCode())){
            return AdTargetStrategyTypeEnum.NONE.getOperationTypeList();
        }
        for (String type : adStrategyType) {
            AdTargetStrategyTypeEnum enumByCode = getEnumByCode(type);
            if(enumByCode != null && CollectionUtil.isNotEmpty(enumByCode.getOperationTypeList())){
                operationTypeList.addAll(enumByCode.getOperationTypeList());
            }
        }
        return operationTypeList;
    }

    public static String getSql(List<String> adStrategyType, List<String> itemIdList, List<String> groupIdList, List<Object> argsList, String targetIdName, String groupIdName) {
        if(CollectionUtil.isEmpty(adStrategyType)) {
            return null;
        }
        if(adStrategyType.size() == AdTargetStrategyTypeEnum.values().length) {
            return null;
        }
        if(adStrategyType.contains(AdTargetStrategyTypeEnum.NONE.getCode())){
            // 未使用sql
            StringBuilder builder = new StringBuilder();
            if(CollectionUtil.isNotEmpty(itemIdList)){
                itemIdList = itemIdList.stream().filter(it -> (StringUtil.isNotEmpty(it) && !"null".equals(it))).collect(Collectors.toList());
                builder.append(SqlStringUtil.dealBitMapDorisNotInList(targetIdName, itemIdList, argsList));
            }
            if(CollectionUtil.isNotEmpty(groupIdList)){
                builder.append(SqlStringUtil.dealBitMapDorisNotInList(groupIdName, groupIdList, argsList));
            }
            return builder.toString();
        }
        StringBuilder builder = new StringBuilder();
        if(CollectionUtil.isNotEmpty(itemIdList)){
            if(StringUtil.isEmpty(builder.toString())){
                builder.append(" and ( ");
            }
            itemIdList = itemIdList.stream().filter(it -> (StringUtil.isNotEmpty(it) && !"null".equals(it))).collect(Collectors.toList());
            builder.append(SqlStringUtil.dealBitMapDorisInNoAndList(targetIdName, itemIdList, argsList));
        }
        if(CollectionUtil.isNotEmpty(groupIdList)){
            if(StringUtil.isEmpty(builder.toString())){
                builder.append(" and ( ");
            }else{
                builder.append(" or ");
            }
            builder.append(SqlStringUtil.dealBitMapDorisInNoAndList(groupIdName, groupIdList, argsList));
        }
        if(StringUtil.isNotEmpty(builder.toString())){
            builder.append(" ) ");
        }
        return builder.toString();
    }

    public static void main(String[] args) {
        List<String> itemIdList = new ArrayList<>();
        itemIdList.add("1");
        itemIdList.add("null");

        itemIdList = itemIdList.stream().filter(it -> (StringUtil.isNotEmpty(it) && !"null".equals(it))).collect(Collectors.toList());
        System.out.println(itemIdList);
    }
}
