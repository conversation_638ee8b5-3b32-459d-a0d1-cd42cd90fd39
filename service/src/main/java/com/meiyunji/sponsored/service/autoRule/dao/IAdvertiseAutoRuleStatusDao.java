package com.meiyunji.sponsored.service.autoRule.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleTemplate;
import com.meiyunji.sponsored.service.autoRule.po.ChildrenItemTypeVo;
import com.meiyunji.sponsored.service.autoRule.vo.*;
import com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam;
import com.meiyunji.sponsored.service.autoRuleTask.vo.StatusVo;
import com.meiyunji.sponsored.service.strategy.vo.ExecuteParam;
import com.meiyunji.sponsored.service.strategy.vo.QueryTargetTypeVo;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

public interface IAdvertiseAutoRuleStatusDao extends IBaseShardingDao<AdvertiseAutoRuleStatus> {

    AdvertiseAutoRuleStatus getByTaskId(int puid, int shopId , Long taskId);

    AdvertiseAutoRuleStatus getByStatusId(int puid, Long statusId);

    List<AdvertiseAutoRuleStatus> getByStatusIds(int puid, List<Long> statusIds);

    List<QueryItemCountVo> queryItemCount(int puid, List<Long> templateIds);

    Integer queryItemCount(int puid,Long templateId);

    List<String> queryItemIdByTemplateId(int puid,Long templateId);

    Integer queryItemIdByTemplateId(int puid);

    int updateByPrimaryKey(int puid, AdvertiseAutoRuleStatus record);

    int batchInsert(int puid, List<AdvertiseAutoRuleStatus> list);

    int batchUpdate(int puid, List<Long> idList, AdvertiseAutoRuleTemplate template);

    Integer insetStrategyStatus(int puid,AdvertiseAutoRuleStatus advertiseAutoRuleStatus);

    Integer updateStrategyStatus(int puid,AdvertiseAutoRuleStatus advertiseAutoRuleStatus);

    Integer deleteAutoRuleStatus(int puid,Long statusId);

    Integer deleteStrategyByTaskId(int puid,int shopId,Long taskId);

    Page<AdvertiseAutoRuleStatus> pageAdControlled(AutoRuleObjectParam param);

    List<AdvertiseAutoRuleStatus> selectAdControlledList(AutoRuleObjectParam param);

    void updateStatus(int puid, Long templateId, Long statusId, String status, Integer operationType, Integer hasSimilarRule, AdvertiseAutoRuleTemplate template, Integer updateUid);

    void updateKeywordCardBid(Long templateId,AdvertiseAutoRuleStatus status);

    void updateKeywordCardStatus(Long templateId,AdvertiseAutoRuleStatus status);

    void updateRule(int puid,Integer shopId,String executeType,Long statusId,String rule,String performOperation);

    void updateStrategyStatusById(int puid,Integer updateUid,Long statusId,String status);

    void updateStatusTemplateById(int puid,Long statusId,Long templateId);

    List<AdvertiseAutoRuleStatus> getListByPuidAndShopIds(AllUpdateAutoRuleParam param);

    List<AdvertiseAutoRuleStatus> getSimilarRuleListByQueryIds(AdQueryKeywordAutoRuleParam adQueryKeywordAutoRuleParam, List<String> queryIdList) throws Exception;

    AdvertiseAutoRuleStatus getObjectByPuidAndShopId(int puid,int shopId,String ruleType,String itemId,String itemType);

    List<AdvertiseAutoRuleStatus> getListByCampaignId(int puid,Integer shopId,Long templateId,List<String> campaignIds);

    List<String> getListByItemIdList(Integer puid, Integer shopId, Long templateId);

    List<String> getLisByItemIdList(Integer puid, Integer shopId,Long templateId,Integer hasSimilarRule,List<Long> excludedStatusIdList);

    List<String> getLisBySimilarRuleItemIdList(Integer puid, Integer shopId,Long templateId,Integer operationType);

    List<String> getItemIdListBySimilarRule(Integer puid, Integer shopId, Integer operationType, Integer hasSimilarRule);

    List<AdvertiseAutoRuleStatus> getListByItemIds(Integer puid, List<Integer> shopId, List<String> itemIds);

    List<String> getListByItemIdList(Integer puid, Integer shopId);

    List<AdvertiseAutoRuleStatus> getListByItemIdsAndEnabled(Integer puid, Integer shopId, String adType, String itemType, List<String> itemIds);

    List<AdvertiseAutoRuleStatus> ListByItemIdsByChildItemType(Integer puid, Integer shopId, String adType, String childItemType, List<String> itemIds);

    Integer updateAutoRuleStatus(Integer puid, Long templateId, String timeRuleJson, LocalDate localDate);

    Integer updateAutoRuleStatus(Integer puid);

    /**
     * 获取自动化规则当前模板的受控对象数量
     * @param puid
     * @param templateId
     */
    Integer getAutoRuleStatusSize(Integer puid, Long templateId);

    /**
     * 分页获取相似规则列表模板id
     * @param puid
     * @param itemId
     * @param query
     * @return
     */
    Integer isSimilarRule(Integer puid, String itemId, Integer operationType, Long templateId);

    /**
     * 分页获取相似规则列表模板id
     * @param param
     * @return
     */
    Page<AdvertiseAutoRuleStatus> pageSimilarRuleTemplateIdList(SimilarRulePageParam param);

    /**
     * 取相似规则列表模板id
     * @param puid
     * @param templateId
     * @param pageNo
     * @param pageSize
     * @return
     */
    List<AdvertiseAutoRuleStatus> allSimilarRuleList(Integer puid, Integer shopId,Integer operationType,Long templateId,List<String> itemIdList,String itemType);

    /**
     * 取相似规则列表的受控对象id
     * @param puid
     * @param shopId
     * @param templateId
     * @return
     */
    List<String> similarRuleItemIdList(Integer puid, Integer shopId,Long templateId);

    /**
     * 取相似规则列表的受控对象id
     * @param param
     * @return
     */
    List<AdvertiseAutoRuleStatus> selectChildrenItemType(Integer puid, Integer shopId, Long templateId);

    List<ChildrenItemTypeVo> selectChildrenItemType(Integer puid, List<Long> templateIdList);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

    List<Long> getIdByPuidAndShopIds(int puid, Integer shopId, Long templateId, String itemType, List<StatusVo> statusVoList, List<Long> includeStatusIdList);

    Integer getCount(int puid, Long templateId, List<Long> includeStatusIdList, List<Long> idList);

    List<AdvertiseAutoRuleStatusTaskVo> getAutoRuleStatusTaskVoList(int puid, Long templateId, Long statusId, List<Long> statusIdList);

    List<QueryTargetTypeVo> queryListItemIdAndTargetType(Integer puid, Integer shopId, Long templateId);

    List<AllUpdateStatusVo> getAllUpdateStatus(int puid, Long templateId, List<Long> includeStatusIdList, List<Long> idList);

    List<String> getSimilarRuleItemId(Integer puid, Integer operationType, Long templateId, List<String> itemIds);

    List<AutoRuleProcessTaskVo> getStatusIdsByProcessTaskParam(ProcessTaskParam param);

    List<QueryTargetTypeVo> queryListItemIdAndTargetType(AutoRuleObjectParam param);

    Page<AdvertiseAutoRuleStatus> selectAdControlledPage(AutoRuleObjectParam param);

    List<AutoRuleScheduleTaskDto> getList4AutoRuleScheduleTask(Integer puid, Integer shopId, List<String> itemTypeList, List<String> itemIdList, LocalDate localDate, String startIndex, int limit, String adType);

    void batchUpdateArchivedState(Integer puid, Integer shopId, String itemType, List<String> itemIdList);

    List<AutoRuleScheduleShopDto> getShopId4AutoRuleScheduleTask(Integer puid, List<String> itemTypeList, List<String> itemIdList, String adType);

    void batchUpdateNextExecuteTime(Integer puid, Integer shopId, String itemType, List<UpdateNextExecuteTimeVo> nextExecuteTimeVoList);

    void batchAddNextExecuteTime(Integer puid, Integer shopId, List<Long> taskIdList, LocalDateTime nextExecuteTime);

    List<AdvertiseAutoRuleStatus> queryUpdateSearchQueryToAadras(Integer puid, List<Long> taskIdList);

    void updateDefaultExecuteTimeSpace(Integer puid, List<Integer> shopIdList, Date nextExecuteTime);

    List<AutoRuleMarketplaceDto> selectDistinctMarket(Integer puid);

    void updateDefaultExecuteTimeSpace(Integer puid, List<Integer> shopIdList, String ruleType, String timeSpace, String timeSpaceUnit, Date date);

    List<AutoRuleAdAndTargetTypeVo> distinctAdAndTargetTypeByTemplateId(int puid, long templateId, List<Long> statusIdList);

    List<AdvertiseAutoRuleStatus> getList4AutoRuleScheduleTask(Integer puid, Integer shopId, List<String> itemTypeList, Long id, int limit);

    List<AutoRuleScheduleShopDto> getShopIdByItemType(Integer puid, String itemType);
    /**
     * 分页查询
     */
    Page<AdvertiseAutoRuleStatus> pageAutoRuleStatus(PageAutoRuleStatusParam param);

    /**
     * 根据受控对象id，受控对象类型获取存在的模版id
     */
    List<Long> selectTemplateIdByItemIdItemType(int puid,List<Integer> shopIdList,String itemId,List<String> itemTypeList);

    /**
     * 获取组受控模板id
     */
    List<Long> getGroupTemplateId(int puid,List<Integer> shopIdList,List<String> itemTypeList);

    /**
     * 获取模版存在的受控对象id
     */
    List<String> getExistItemId(int puid, List<String> itemIdList, Long templateId);

    /**
     * 广告管理筛选已存在的受控对象id
     */
    List<String> listItemIdByAdManage(int puid, List<Integer> shopIdList, String itemType, List<Integer> operationTypeList, String adType, String childrenItemType, String targetType, String queryType);

    /**
     * 根据受控对象id获取所有受控对象信息
     */
    List<AdvertiseAutoRuleStatus> listByItemIdMutiple(int puid, List<Integer> shopIdList, String itemType, List<String> itemIdList, String childrenItemType);
}
