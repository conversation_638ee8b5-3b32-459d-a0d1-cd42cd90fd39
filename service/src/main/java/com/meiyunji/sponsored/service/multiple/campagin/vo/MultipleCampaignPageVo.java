package com.meiyunji.sponsored.service.multiple.campagin.vo;

import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.cpc.vo.AdStrategyVo;
import com.meiyunji.sponsored.service.multiple.common.vo.CommonCompareReportRate;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 广告活动列表页响应数据
 *
 * @author: zzh
 * @create: 2024-11-21 10:05
 */
@Data
public class MultipleCampaignPageVo extends CommonCompareReportRate {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("店铺id")
    private Integer shopId;

    @ApiModelProperty("店铺名称")
    private String shopName;

    @ApiModelProperty("广告活动ID")
    private String campaignId;

    @ApiModelProperty("广告组合ID")
    private String portfolioId;

    @ApiModelProperty("广告组合名称")
    private String portfolioName;

    @ApiModelProperty("广告组合隐藏状态 1:隐藏  0:可见")
    private Integer isHidden;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("状态")
    private String state;

    @ApiModelProperty("二级状态")
    private String servingStatus;

    @ApiModelProperty("二级状态描述")
    private String servingStatusDec;

    @ApiModelProperty("二级状态名称")
    private String servingStatusName;

    @ApiModelProperty("是否预算不足")
    private Boolean outOfBudget;

    @ApiModelProperty("日预算")
    private String dailyBudget;

    @ApiModelProperty("预算类型")
    private String budgetType;

    @ApiModelProperty("活动类型")
    private String campaignType;

    @ApiModelProperty("sp sb sd 投放类型统一转换成此字段")
    private String campaignTargetingType;

    @ApiModelProperty("投放类型")
    private String targetingType;

    @ApiModelProperty("广告活动竞价策略")
    private String strategy;

    @ApiModelProperty("详情")
    private String placementProductPage;

    @ApiModelProperty("首页")
    private String placementTop;

    @ApiModelProperty("其他位置")
    private String placementRestOfSearch;

    @ApiModelProperty("企业购")
    private String placementSiteAmazonBusiness;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("startDate")
    private String startDate;

    @ApiModelProperty("endDate")
    private String endDate;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("updateTime")
    private String updateTime;

    @ApiModelProperty("createTime")
    private String createTime;

    @ApiModelProperty("sb 活动上的投放类型  keyword、product")
    private String targetType;

    @ApiModelProperty("广告形式")
    private String adFormat;

    @ApiModelProperty("预算是否开启分时调价")
    private Integer isBudgetPricing;

    @ApiModelProperty("预算分时调价任务状态")
    private Integer pricingBudgetState;

    @ApiModelProperty("广告位是否开启分时调价")
    private Integer isSpacePricing;

    @ApiModelProperty("广告位分时调价任务状态")
    private Integer pricingSpaceState;

    @ApiModelProperty("活动启停是否开启分时调价")
    private Integer isStatePricing;

    @ApiModelProperty("广告活动启停任务状态")
    private Integer pricingStartStopState;

    @ApiModelProperty("站点")
    private String marketplaceId;

    @ApiModelProperty("支付方式")
    private String costType;

    @ApiModelProperty("品牌id")
    private String brandEntityId;

    @ApiModelProperty("sb自动竞价开关(sb 设置为true，允许亚马逊自动优化放置在搜索顶部以下的出价)")
    private Boolean bidOptimization;

    @ApiModelProperty("sb当开启自动竞价(bidOptimization = true),调整竞价百分比")
    private Double bidMultiplier;

    @ApiModelProperty("是否sb类型")
    private Boolean sbType;

    @ApiModelProperty("标签")
    private List<AdTag> adTags;

    @ApiModelProperty("当天预算剩余量")
    private BudgetUsage budgetUsage;

    @ApiModelProperty("近三天预算剩余量")
    private List<BudgetUsage> budgetUsageList;

    @ApiModelProperty("建议预算")
    private MissBudgetRecommendation missBudget;

    @ApiModelProperty("预算日志")
    private CampaignDataLog budgetLog;

    @ApiModelProperty("广告位竞价搜索结果顶部（首页）日志")
    private CampaignDataLog placementTopLog;

    @ApiModelProperty("产品页面广告位日志")
    private CampaignDataLog placementProductPageLog;

    @ApiModelProperty("搜索结果其余位置日志")
    private CampaignDataLog placementRestOfSearchLog;

    @ApiModelProperty("企业购日志")
    private CampaignDataLog placementSiteAmazonBusinessLog;

    @ApiModelProperty("是否更新预算")
    private Boolean isUpdateBudget;

    @ApiModelProperty("是否更新广告位竞价搜索结果顶部（首页）")
    private Boolean isUpdatePlacementTop;

    @ApiModelProperty("是否更新产品页面广告位")
    private Boolean isUpdatePlacementProductPage;

    @ApiModelProperty("是否搜索结果其余位置")
    private Boolean isUpdatePlacementRestOfSearch;

    @ApiModelProperty("是否更新企业购")
    private Boolean isUpdatePlacementSiteAmazonBusiness;

    @ApiModelProperty("币种")
    private String currency;

    @ApiModelProperty("广告策略集合")
    private List<AdStrategyVo> strategyList;
}
