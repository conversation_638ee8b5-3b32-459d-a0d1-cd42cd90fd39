package com.meiyunji.sponsored.service.adActivity.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AdCampaignProductQueryReq   {

    /**
     * 店铺id
     */
    @NotNull(message = "无店铺id信息")
    private Integer shopId;

//    /**
//     * 活动id信息
//     */
//    private String adCampaignId;

    /**
     * 广告组ids
     */
    @NotEmpty(message = "无广告组信息")
    private List<String> adGroupIds;

    /**
     * 限制返回row数
     */
    private Integer limit;

}
