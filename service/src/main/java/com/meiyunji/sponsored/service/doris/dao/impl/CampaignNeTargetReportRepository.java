package com.meiyunji.sponsored.service.doris.dao.impl;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.LogicType;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportDataDto;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportFilterDto;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.CampaignNeTargetingSpParam;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdCampaignNeTargeting;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.NeTargetReportParamsMappingEnum;
import com.meiyunji.sponsored.service.enums.NeTargetReportTableEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 活动层级否定投放关键词repository
 * @Author: hejh
 * @Date: 2024/11/13 14:02
 */
@Repository
public class CampaignNeTargetReportRepository extends DorisBaseDaoImpl<OdsAmazonAdCampaignNeTargeting> {

    /**
     * 只查询基础数据，未筛选报告数据
     * @param param param
     * @return Page<String> row = keywordId
     */
    public Page<String> pageCampaignNeTargetWithoutReportFilter(CampaignNeTargetingSpParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder baseSql = new StringBuilder();
        baseSql.append(" from ")
            .append(NeTargetReportTableEnum.SP_CAMPAIGN_NE_TARGET.getBasicTableName())
            .append(" where puid = ? and shop_id = ? and create_state = 'SUCCESS' ");
        argsList.add(param.getPuid());
        argsList.add(param.getShopId());
        //广告活动筛选
        List<String> campaignIdList = StringUtil.stringToList(param.getCampaignId(), ",");
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            baseSql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
        }
        //广告组合筛选
        baseSql.append(" and campaign_id in ( ").append(getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sp.getCampaignType(), param.getStatus(), param.getServingStatus(), argsList)).append(") ");
        //否定关键词状态筛选
        baseSql.append(SqlStringUtil.dealSingleField("state", param.getState(), argsList));
        //搜索
        if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
            baseSql.append(" and target_text like ? ");
            argsList.add("%" + param.getSearchValue().trim() + "%");
        }

        StringBuilder countSql = new StringBuilder().append("select count(*) ").append(baseSql);
        StringBuilder selectSql = new StringBuilder().append("select target_id, create_time, creation_date  ").append(baseSql).append(" order by ifnull(creation_date, create_time) desc ");
        return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(), selectSql.toString(), argsList.toArray(), (res, i) -> res.getString("target_id"));
    }

    /**
     * 筛选活动层级否定关键词前后30天报告数据
     * @param param param
     * @param reportTableName 报告表：要么是来自投放，要么是来自搜索词
     * @return Page<String> row = keywordId
     */
    public Page<String> page30DaysCampaignNeTargetWithReportFilter(@NotNull CampaignNeTargetingSpParam param, String reportTableName) {
        // Initialize DTO and arguments list
        NeTargetReportFilterDto filterDto = param.getNeTargetReportFilterDto();
        boolean doAdvancedFilter = Boolean.TRUE.equals(filterDto.getDoAdvancedFilter());
        boolean order = StringUtils.isNotBlank(filterDto.getOrderField()) && StringUtils.isNotBlank(filterDto.getOrderType());
        NeTargetReportParamsMappingEnum orderFieldEnum = NeTargetReportParamsMappingEnum.getByFrontParam(filterDto.getOrderField());
        List<Object> argsList = new ArrayList<>();
        StringBuilder baseSql = new StringBuilder();

        // Start building the SQL query
        baseSql.append(" from ")
            .append(NeTargetReportTableEnum.SP_CAMPAIGN_NE_TARGET.getBasicTableName())
            .append(" t left join (")
            .append("  SELECT target_id ")
            .append(SqlStringReportUtil.deal30SelectSqlForNeTarget(filterDto.getAdvanceFilterParams(), filterDto.getOnlyShowImpressions(), Constants.SP, filterDto.getDoAdvancedFilter(), order, orderFieldEnum))
            .append("  FROM ").append(reportTableName)
            .append("  WHERE puid = ? AND shop_id = ? and report_date_type = ? ");
        // Add parameters to argsList
        argsList.add(param.getPuid());
        argsList.add(param.getShopId());
        argsList.add(filterDto.getReportDateType());

        baseSql//.append(doAdvancedFilter ? SqlStringReportUtil.deal30AdvancedFilterForNeTargetWithOnAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions(), Constants.SP): SqlStringReportUtil.dealOnlyShowImpressionsWithOnAnd(filterDto.getOnlyShowImpressions()))
            .append(") r ")
            .append("ON t.puid = ? AND t.shop_id = ? AND t.target_id = r.target_id ")
            .append("WHERE t.puid = ? AND t.shop_id = ?  and t.create_state = 'SUCCESS' ");

        argsList.add(param.getPuid());
        argsList.add(param.getShopId());
        argsList.add(param.getPuid());
        argsList.add(param.getShopId());

        //广告活动筛选
        List<String> campaignIdList = StringUtil.stringToList(param.getCampaignId(), ",");
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            baseSql.append(SqlStringUtil.dealInList("t.campaign_id", campaignIdList, argsList));
        }
        //广告组合筛选
        baseSql.append(" and t.campaign_id in ( ").append(getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sp.getCampaignType(), param.getStatus(), param.getServingStatus(), argsList)).append(") ");
        //否定关键词状态筛选
        baseSql.append(SqlStringUtil.dealSingleField("t.state", param.getState(), argsList));
        //搜索
        if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
            baseSql.append(" and target_text like ? ");
            argsList.add("%" + param.getSearchValue().trim() + "%");
        }
        baseSql.append(doAdvancedFilter ?SqlStringReportUtil.dealAdvancedFilterForNeTargetWithWhereAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions()): SqlStringReportUtil.dealOnlyShowImpressionsWithWhereAnd(filterDto.getOnlyShowImpressions()));

        // SQL to count the total rows
        StringBuilder countSql = new StringBuilder()
            .append("SELECT COUNT(*) ")
            .append(baseSql);

        // SQL to select keyword IDs, ordered by sum_acos and creation date
        StringBuilder selectSql = new StringBuilder()
            .append("SELECT t.target_id, t.create_time, t.creation_date ")
            .append(baseSql)
            .append("ORDER BY ");
        if (order && Objects.nonNull(orderFieldEnum)) {
            selectSql.append("r.")
                .append(orderFieldEnum.getPoSumParam())
                .append(filterDto.getOrderType())
                .append(",")
            ;
        }
        selectSql.append(" IFNULL(t.creation_date, t.create_time) DESC");

        // Execute query and return paged results
        return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(),
            selectSql.toString(), argsList.toArray(), (res, i) -> res.getString("target_id"));
    }

    /**
     * 筛选活动层级否定关键词自定义时间范围内报告数据
     * @param param param
     * @param reportTableName 报告表：要么是来自投放，要么是来自搜索词
     * @return Page<String> row = keywordId
     */
    public Page<String> pageCampaignNeTargetWithReportFilter(@NotNull CampaignNeTargetingSpParam param, String reportTableName) {
        // Initialize DTO and arguments list
        NeTargetReportFilterDto filterDto = param.getNeTargetReportFilterDto();
        boolean doAdvancedFilter = Boolean.TRUE.equals(filterDto.getDoAdvancedFilter());
        boolean order = StringUtils.isNotBlank(filterDto.getOrderField()) && StringUtils.isNotBlank(filterDto.getOrderType());
        NeTargetReportParamsMappingEnum orderFieldEnum = NeTargetReportParamsMappingEnum.getByFrontParam(filterDto.getOrderField());

        List<Object> argsList = new ArrayList<>();
        StringBuilder baseSql = new StringBuilder();

        // Start building the SQL query
        baseSql.append(" from ")
            .append(NeTargetReportTableEnum.SP_CAMPAIGN_NE_TARGET.getBasicTableName())
            .append(" t left join (")
            .append("  SELECT target_id ")
            .append(SqlStringReportUtil.dealSelectSqlForNeTarget(filterDto.getAdvanceFilterParams(), filterDto.getOnlyShowImpressions(), Constants.SP, filterDto.getDoAdvancedFilter(), order, orderFieldEnum))
            .append("  FROM ").append(reportTableName)
            .append("  WHERE puid = ? AND shop_id = ? and count_day >= ? and count_day <= ? ");
        // Add parameters to argsList
        argsList.add(param.getPuid());
        argsList.add(param.getShopId());
        argsList.add(filterDto.getReportStartDate());
        argsList.add(filterDto.getReportEndDate());
        baseSql.append(" group by target_id HAVING 1=1 " );
        baseSql//.append(doAdvancedFilter ? SqlStringReportUtil.deal30AdvancedFilterForNeTargetWithHavingAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions()): SqlStringReportUtil.dealOnlyShowImpressionsWithHavingAnd(filterDto.getOnlyShowImpressions()))
            .append(") r ")
            .append("ON t.puid = ? AND t.shop_id = ? AND t.target_id = r.target_id ")
            .append("WHERE t.puid = ? AND t.shop_id = ? and t.create_state = 'SUCCESS' ");

        argsList.add(param.getPuid());
        argsList.add(param.getShopId());
        argsList.add(param.getPuid());
        argsList.add(param.getShopId());

        //广告活动筛选
        List<String> campaignIdList = StringUtil.stringToList(param.getCampaignId(), ",");
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            baseSql.append(SqlStringUtil.dealInList("t.campaign_id", campaignIdList, argsList));
        }
        //广告组合筛选
        baseSql.append(" and t.campaign_id in ( ").append(getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sp.getCampaignType(), param.getStatus(), param.getServingStatus(), argsList)).append(") ");
        //否定关键词状态筛选
        baseSql.append(SqlStringUtil.dealSingleField("t.state", param.getState(), argsList));
        //搜索
        if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
            baseSql.append(" and target_text like ? ");
            argsList.add("%" + param.getSearchValue().trim() + "%");
        }
        baseSql.append(doAdvancedFilter ? SqlStringReportUtil.dealAdvancedFilterForNeTargetWithWhereAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions()): SqlStringReportUtil.dealOnlyShowImpressionsWithWhereAnd(filterDto.getOnlyShowImpressions()));

        // SQL to count the total rows
        StringBuilder countSql = new StringBuilder()
            .append("SELECT COUNT(*) ")
            .append(baseSql);

        // SQL to select keyword IDs, ordered by sum_acos and creation date
        StringBuilder selectSql = new StringBuilder()
            .append("SELECT t.target_id, t.create_time, t.creation_date ")
            .append(baseSql)
            .append("ORDER BY ");
        if (order && Objects.nonNull(orderFieldEnum)) {
            selectSql.append("r.")
                .append(orderFieldEnum.getPoSumParam())
                .append(filterDto.getOrderType())
                .append(",")
            ;
        }
        selectSql.append(" IFNULL(t.creation_date, t.create_time) DESC");

        // Execute query and return paged results
        return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(),
            selectSql.toString(), argsList.toArray(), (res, i) -> res.getString("target_id"));
    }

    /**
     * 通过keywordid列表查询30天报告数据
     * @param puid puid
     * @param shopId shopId
     * @param keywordIds keywordIds
     * @param reportTableName 报告表：要么是来自投放，要么是来自搜索词
     * @return List<NeTargetReportDataDto>
     */
    public List<NeTargetReportDataDto> get30ReportByTargetIds(int puid, int shopId, List<String> keywordIds, String reportTableName, String reportDateType) {
        StringBuilder sql = new StringBuilder();
        sql.append("select target_id, cost, total_sales, impressions, clicks, sale_num ad_order_num")
            .append(" from ").append(reportTableName)
            .append(" where puid = ? and shop_id = ? and report_date_type = ? ");
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(reportDateType);

        if (keywordIds.size() >= 10000) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("target_id", keywordIds, argsList));
        } else {
            sql.append(SqlStringUtil.dealInList("target_id", keywordIds, argsList));
        }

        return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(NeTargetReportDataDto.class), argsList.toArray());
    }

    /**
     * 通过keywordid列表查询自定义范围报告数据
     * @param puid puid
     * @param shopId shopId
     * @param keywordIds keywordIds
     * @param reportTableName 报告表：要么是来自投放，要么是来自搜索词
     * @return List<NeTargetReportDataDto>
     */
    public List<NeTargetReportDataDto> getReportByTargetIds(int puid, int shopId, List<String> keywordIds, String reportTableName, String startDate, String endDate) {
        StringBuilder sql = new StringBuilder();
        sql.append("select target_id "
            + ",sum(cost) cost "
            + ",sum(total_sales) total_sales "
            + ",sum(impressions) impressions "
            + ",sum(clicks) clicks "
            + ",sum(sale_num) ad_order_num ")
            .append(" from ").append(reportTableName);
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(startDate);
        argsList.add(endDate);
        sql.append(" where puid = ? and shop_id = ? and count_day between ? and ? ")
            .append(keywordIds.size() >= 10000 ? SqlStringUtil.dealBitMapDorisInList("target_id", keywordIds, argsList) : SqlStringUtil.dealInList("target_id", keywordIds, argsList))
            .append(" group by target_id");

        return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(NeTargetReportDataDto.class), argsList.toArray());
    }

    /**
     * 通过广告组合id查询活动id的sql
     *
     * @param puid
     * @param shopId
     * @param portfolioId
     * @param type
     * @param state
     * @param servingStatus
     * @return
     */
    private String getCampaignIdsByPortfolioIdSql(Integer puid, Integer shopId, String portfolioId, String type, String state, String servingStatus, List<Object> argsList) {
        // portfolioIds筛选掉-1的情况
        List<String> portfolioIds = new ArrayList<>();
        if (StringUtils.isNotBlank(portfolioId)) {
            portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                .filter(item -> !item.equals("-1")).collect(Collectors.toList());
        }
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        if (StringUtils.isNotBlank(state)) {
            List<String> stateList = Arrays.asList(state.split(","));
            if (stateList.size() > 0) {
                builder.in("state", stateList.toArray());
            }
        }
        if (StringUtils.isNotBlank(servingStatus)) {
            List<String> servingStatusList = Arrays.asList(servingStatus.split(","));
            if (servingStatusList.size() > 0) {
                List<String> statusList = getServingStatus(servingStatusList);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(statusList)) {
                    builder.in("serving_status", statusList.toArray());
                }
            }
        }
        builder.equalTo("puid", puid);
        builder.equalTo("shop_id", shopId);
        // 选择‘未分类’：没有广告组合的广告活动
        if ("-1".equals(portfolioId)) {
            builder.isNull(LogicType.AND, "portfolio_id");
        } else if (org.apache.commons.collections.CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioId.contains("-1")) {
                builder.and().leftBracket().in(LogicType.EPT, "portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
                builder.isNull(LogicType.OR, "portfolio_id").rightBracket();
            } else {
                builder.inStrList("portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
            }
        }
        if (StringUtils.isNotBlank(type)) {
            builder.in("type", StringUtil.splitStr(type).toArray());
            //            builder.equalTo("type", type);
        }
        argsList.addAll(Arrays.asList(builder.build().getValues()));
        return "select campaign_id from ods_t_amazon_ad_campaign_all where " + builder.build().getSql();
    }

    private List<String> getServingStatus(List<String> servingStatusList) {
        List<String> statusList = new ArrayList<>();
        for (String servingStatusKey : servingStatusList) {
            List<String> servingStatusValueList = Constants.SERVER_STATUS_SELECT.get(servingStatusKey);
            if (servingStatusValueList != null) {
                statusList.addAll(servingStatusValueList);
            }
        }
        return statusList;
    }



}











