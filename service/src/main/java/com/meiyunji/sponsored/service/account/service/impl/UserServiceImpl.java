package com.meiyunji.sponsored.service.account.service.impl;

import com.google.common.collect.Maps;
import com.meiyunji.sponsored.common.springjdbc.BaseServiceImpl;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.account.service.IUserService;
import com.meiyunji.sponsored.service.enums.PayPlanTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * user service
 */
@Service
public class UserServiceImpl extends BaseServiceImpl<User> implements IUserService {

    @Autowired
    private IUserDao userDao;

    /**
     * @param uids 用户id集合
     * @return Entry<id, name>
     */
    @Override
    public Map<String, String> getUserNameIdMap(List<Integer> uids) {
        List<User> users = CollectionUtils.isEmpty(uids) ? null : userDao.getListByIdList(uids);
        Map<String, String> nameMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(users)) return nameMap;
        for (User user : users) {
            if (user.getId().equals(user.getPuid())) {
                nameMap.put(String.valueOf(user.getId()), "管理员");
            } else {
                nameMap.put(String.valueOf(user.getId()), user.getNickname());
            }
        }
        return nameMap;
    }

    @Override
    public boolean isFree(int puid) {
        User user = userDao.getById(puid);
        return user.getPlanType() <= PayPlanTypeEnum.FREE.getValue();
    }
}