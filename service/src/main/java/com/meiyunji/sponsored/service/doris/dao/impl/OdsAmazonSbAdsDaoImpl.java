package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdProductPerspectiveBO;
import com.meiyunji.sponsored.service.cpc.dto.GroupCampaignDto;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.MultiShopGroupByProductParam;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonSbAdsDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonSbAds;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.enums.SBAdFormatEnum;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.ViewBaseParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AsinListDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * amazon SB ads表(OdsAmazonSbAds)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:21
 */
@Repository
@Slf4j
public class OdsAmazonSbAdsDaoImpl extends DorisBaseDaoImpl<OdsAmazonSbAds> implements IOdsAmazonSbAdsDao {

    @Override
    public List<OdsAmazonSbAds> getGroupIdByAsins(MultiShopGroupByProductParam param, Map<Integer, Set<String>> shopAsinMap) {
        List<Object> args = Lists.newArrayList();
        StringBuilder sql = new StringBuilder();

        sql.append(" select shop_id, ad_group_id,")
                .append(" GROUP_CONCAT(distinct asins,',') asins_str")
                .append(" from ods_t_amazon_sb_ads where puid=? and marketplace_id=? ");
        args.add(param.getPuid());
        args.add(param.getMarketplaceId());
        sql.append(SqlStringUtil.dealInList("shop_id", param.getShopIds(), args));

        if (StringUtils.equalsIgnoreCase(param.getContainType(), MultiShopGroupByProductParam.CONTAIN_ANY)) {
            if (StringUtils.equalsAnyIgnoreCase(param.getSearchType(), Constants.PRODUCT_TYPE_PARENTASIN, Constants.PRODUCT_TYPE_MSKU)) {
                List<String> regexpSql = Lists.newArrayList();
                shopAsinMap.forEach((key, asinSet) -> {
                    // 避免多店铺造成影响，此处需要限制shop_id
                    int shopId = key;
                    regexpSql.add(" (shop_id=" + shopId + " and asins REGEXP '" + String.join("|", asinSet) + "') ");
                });
                sql.append(" and (").append(String.join(" or ", regexpSql)).append(")");
            } else {
                List<String> asinList = shopAsinMap.values().stream().flatMap(Set::stream).distinct().collect(Collectors.toList());
                sql.append(" and asins REGEXP '").append(String.join("|", asinList)).append("'");
            }
        }

        sql.append(" group by shop_id, ad_group_id ");

        if (StringUtils.equalsIgnoreCase(param.getContainType(), MultiShopGroupByProductParam.CONTAIN_ALL)) {
            sql.append(" having ");
            List<String> havingSql = Lists.newArrayList();
            if (StringUtils.equalsIgnoreCase(param.getSearchType(), Constants.PRODUCT_TYPE_PARENTASIN)) {
                Map<Integer, List<OdsProduct>> shopProductMap = param.getProductList().stream().filter(i -> param.getShopIds().contains(i.getShopId()))
                        .collect(Collectors.groupingBy(OdsProduct::getShopId));
                shopProductMap.forEach((key, productList) -> {
                    Map<String, Set<String>> parentAsinsMap = StreamUtil.groupingByToSetValue(productList, OdsProduct::getParentAsin, OdsProduct::getAsin);
                    StringBuilder shopRegexpSql = new StringBuilder();
                    shopRegexpSql.append(" (shop_id=").append(key).append(" AND (");
                    List<String> regexpSql = Lists.newArrayList();
                    parentAsinsMap.forEach((parentAsin, asinSet) -> regexpSql.add(" asins_str REGEXP '" + String.join("|", asinSet) + "' "));
                    shopRegexpSql.append(String.join(" AND ", regexpSql)).append("))");
                    havingSql.add(shopRegexpSql.toString());
                });
                sql.append(String.join(" or ", havingSql));
            } else if (StringUtils.equalsIgnoreCase(param.getSearchType(), Constants.PRODUCT_TYPE_MSKU)) {
                shopAsinMap.forEach((key, asinSet) -> {
                    // 避免多店铺造成影响，此处需要限制shop_id
                    StringBuilder shopRegexpSql = new StringBuilder();
                    shopRegexpSql.append(" (shop_id=").append(key).append(" AND (");
                    List<String> regexpSql = Lists.newArrayList();
                    asinSet.forEach(asin -> regexpSql.add(" FIND_IN_SET('" + asin + "', asins_str) > 0 "));
                    shopRegexpSql.append(String.join(" AND ", regexpSql)).append("))");
                    havingSql.add(shopRegexpSql.toString());
                });
                sql.append(String.join(" or ", havingSql));
            } else {
                shopAsinMap.values().stream().flatMap(Set::stream).distinct().collect(Collectors.toList())
                        .forEach(asin -> havingSql.add(" FIND_IN_SET('" + asin + "', asins_str) > 0 "));
                sql.append(String.join(" and ", havingSql));
            }
        }


        return getJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            OdsAmazonSbAds product = new OdsAmazonSbAds();
            product.setShopId(rs.getInt("shop_id"));
            product.setAdGroupId(rs.getString("ad_group_id"));
            product.setAsins(rs.getString("asins_str"));
            return product;
        }, args.toArray());
    }

    @Override
    public List<AmazonAdProductPerspectiveBO> productPerspectiveBoListByProduct(Integer puid, List<Integer> shopIdList, String marketPlaceId,
                                                                                String searchType, String searchValue) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select 'sb' as type, r.shop_id shopId, r.campaign_id campaignId, r.ad_group_id adGroupId, r.ad_id adId from ods_t_product p")
                .append(" join ").append(this.getJdbcHelper().getTable()).append(" r ")
                .append(" on p.puid = r.puid and p.shop_id = r.shop_id and p.asin = r.asins ");
        sb.append(" and r.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("r.shop_id", shopIdList, argsList));
        }
        if (StringUtils.isNotBlank(marketPlaceId)) {
            sb.append(" and r.marketplace_id = ? ");
            argsList.add(marketPlaceId);
        }
        sb.append(" and r.ad_format = ? ");
        sb.append(" and r.ad_id is not null and r.ad_id != '' ");
        argsList.add(SBAdFormatEnum.VIDEO.getValue());
        sb.append(" where p.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("p.shop_id", shopIdList, argsList));
        }
        if (StringUtils.isNotBlank(marketPlaceId)) {
            sb.append(" and p.marketplace_id = ? ");
            argsList.add(marketPlaceId);
        }
        List<String> searchValues = StringUtil.splitStr(searchValue, StringUtil.SPECIAL_COMMA);
        if (ViewBaseParam.SearchTypeEnum.ASIN.getValue().equals(searchType)) {
//            sb.append(" and p.asin = ? ");
            sb.append(SqlStringUtil.dealInList("p.asin", searchValues, argsList));
        } else if (ViewBaseParam.SearchTypeEnum.MSKU.getValue().equals(searchType)) {
//            sb.append(" and p.sku = ? ");
            sb.append(SqlStringUtil.dealInList("p.sku", searchValues, argsList));
        } else if (ViewBaseParam.SearchTypeEnum.PARENT_ASIN.getValue().equals(searchType)) {
//            sb.append(" and (p.parent_asin = ? or (p.id = p.parent_id and p.asin = ?)) ");
//            argsList.add(searchValue);
            sb.append(" and (")
                    .append(SqlStringUtil.dealInListNotAnd("p.parent_asin", searchValues, argsList))
                    .append(" or (p.id = p.parent_id ").append(SqlStringUtil.dealInList("p.asin", searchValues, argsList)).append(")")
                    .append(") ");
        }
//        argsList.add(searchValue);
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AmazonAdProductPerspectiveBO.class));
    }

    @Override
    public List<GroupCampaignDto> getGroupIdsAndCampaignIdsByCampaignIdsAndAsin(Integer puid, List<Integer> shopIds, List<String> campaignIds, List<String> groupIds,
                                                                                String searchType, String searchValue) {
        //不带查询条件 直接返回空数组
        if (StringUtils.isBlank(searchType) || StringUtils.isBlank(searchValue)) {
            return new ArrayList<>();
        }
        StringBuilder sql = new StringBuilder(" select 'sb' as type, r.ad_group_id ad_group_id, any(r.campaign_id) as campaign_id from ods_t_product p ")
                .append(" join ").append(this.getJdbcHelper().getTable()).append(" r ")
                .append(" on p.puid = r.puid and p.shop_id = r.shop_id and p.asin = r.asins")
                .append(" and r.puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("r.shop_id", shopIds, args));
        sql.append(" and r.ad_format = ? ");
        args.add(SBAdFormatEnum.VIDEO.getValue());
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("r.campaign_id", campaignIds, args));
        }
        if (CollectionUtils.isNotEmpty(groupIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", groupIds, args));
        }
        sql.append(" where p.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("p.shop_id", shopIds, args));

        List<String> searchValues = StringUtil.splitStr(searchValue, StringUtil.SPECIAL_COMMA);
        if (ViewBaseParam.SearchTypeEnum.ASIN.getValue().equals(searchType)) {
//            sql.append(" and p.asin = ? ");
            sql.append(SqlStringUtil.dealInList("p.asin", searchValues, args));
        } else if (ViewBaseParam.SearchTypeEnum.MSKU.getValue().equals(searchType)) {
//            sql.append(" and p.sku = ? ");
            sql.append(SqlStringUtil.dealInList("p.sku", searchValues, args));
        } else if (ViewBaseParam.SearchTypeEnum.PARENT_ASIN.getValue().equals(searchType)) {
//            sql.append(" and (p.parent_asin = ? or (p.id = p.parent_id and p.asin = ?))");
//            args.add(searchValue);
            sql.append(" and (")
                    .append(SqlStringUtil.dealInListNotAnd("p.parent_asin", searchValues, args))
                    .append(" or (p.id = p.parent_id ").append(SqlStringUtil.dealInList("p.asin", searchValues, args)).append(")")
                    .append(") ");
        }
//        args.add(searchValue);
        sql.append(" group by r.ad_group_id ");
        return getJdbcTemplate().query(sql.toString(), (re, i) -> GroupCampaignDto.builder()
                .type(re.getString("type"))
                .adGroupId(re.getString("ad_group_id"))
                .campaignId(re.getString("campaign_id"))
                .build(), args.toArray());
    }

    @Override
    public List<String> getGroupIdsByCampaignIdsAndAsin(Integer puid, List<Integer> shopIds, List<String> campaignIds, List<String> groupIds,
                                                                                String searchType, String searchValue) {
        //不带查询条件 直接返回空数组
        if (StringUtils.isBlank(searchType) || StringUtils.isBlank(searchValue)) {
            return new ArrayList<>();
        }
        StringBuilder sql = new StringBuilder(" select distinct r.ad_group_id ad_group_id from ods_t_product p ")
                .append(" join ").append(this.getJdbcHelper().getTable()).append(" r ")
                .append(" on p.puid = r.puid and p.shop_id = r.shop_id and p.asin = r.asins")
                .append(" and r.puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("r.shop_id", shopIds, args));
        sql.append(" and r.ad_format = ? ");
        args.add(SBAdFormatEnum.VIDEO.getValue());
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("r.campaign_id", campaignIds, args));
        }
        if (CollectionUtils.isNotEmpty(groupIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", groupIds, args));
        }
        sql.append(" where p.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("p.shop_id", shopIds, args));
        // 多个适配
        List<String> searchValues = StringUtil.splitStr(searchValue, StringUtil.SPECIAL_COMMA);
        if (ViewBaseParam.SearchTypeEnum.ASIN.getValue().equals(searchType)) {
//            sql.append(" and p.asin = ? ");
            sql.append(SqlStringUtil.dealInList("p.asin", searchValues, args));
        } else if (ViewBaseParam.SearchTypeEnum.MSKU.getValue().equals(searchType)) {
//            sql.append(" and p.sku = ? ");
            sql.append(SqlStringUtil.dealInList("p.sku", searchValues, args));
        } else if (ViewBaseParam.SearchTypeEnum.PARENT_ASIN.getValue().equals(searchType)) {
//            sql.append(" and (p.parent_asin = ? or (p.id = p.parent_id and p.asin = ?))");
//            args.add(searchValue);
            sql.append(" and (")
                    .append(SqlStringUtil.dealInListNotAnd("p.parent_asin", searchValues, args))
                    .append(" or (p.id = p.parent_id ").append(SqlStringUtil.dealInList("p.asin", searchValues, args)).append(")")
                    .append(") ");
        }
//        args.add(searchValue);
        return getJdbcTemplate().queryForList(sql.toString(), String.class, args.toArray());
    }

    @Override
    public List<String> getAsinByParentAsinAndGroupIdsAndCampaignIds(Integer puid, List<Integer> shopIds, List<String> groupIds, String parentAsin) {
        StringBuilder sql = new StringBuilder(" select distinct p.asin from ods_t_product p ")
                .append(" join ").append(this.getJdbcHelper().getTable()).append(" r ")
                .append(" on p.puid = r.puid and p.shop_id = r.shop_id and p.asin = r.asins ")
                .append(" and r.puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("r.shop_id", shopIds, args));
        if (CollectionUtils.isNotEmpty(groupIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", groupIds, args));
        }
        sql.append(" where p.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("p.shop_id", shopIds, args));
        sql.append(" and (p.parent_asin = ? or (p.id = p.parent_id and p.asin = ?)) ");
        args.add(parentAsin);
        args.add(parentAsin);
        return getJdbcTemplate().queryForList(sql.toString(), String.class, args.toArray());
    }

    @Override
    public List<AsinListDto> getAsinByParentAsinList(Integer puid, List<Integer> shopIds, List<String> groupIds, List<String> parentAsin) {
        StringBuilder sql = new StringBuilder(" select distinct p.shop_id shopId, p.asin asin, p.sku msku, p.parent_asin parentAsin from ods_t_product p ")
                .append(" join ").append(this.getJdbcHelper().getTable()).append(" r ")
                .append(" on p.puid = r.puid and p.shop_id = r.shop_id and p.asin = r.asins ")
                .append(" and r.puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("r.shop_id", shopIds, args));
        if (CollectionUtils.isNotEmpty(groupIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", groupIds, args));
        }
        sql.append(" where p.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("p.shop_id", shopIds, args));
        sql.append(" and (")
                .append(SqlStringUtil.dealInListNotAnd("p.parent_asin", parentAsin, args))
                .append(" or (p.id = p.parent_id ").append(SqlStringUtil.dealInList("p.asin", parentAsin, args)).append(")")
                .append(") ");
        return getJdbcTemplate().query(sql.toString(), args.toArray(), new BeanPropertyRowMapper<>(AsinListDto.class));
    }

    @Override
    public List<String> getAsinByMskuAndGroupIdsAndCampaignIds(Integer puid, List<Integer> shopIds, List<String> groupIds, String msku) {
        StringBuilder sql = new StringBuilder(" select distinct r.asins from ods_t_product p ")
                .append(" join ").append(this.getJdbcHelper().getTable()).append(" r ")
                .append(" on p.puid = r.puid and p.shop_id = r.shop_id and p.asin = r.asins ")
                .append(" and r.puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("r.shop_id", shopIds, args));
        if (CollectionUtils.isNotEmpty(groupIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", groupIds, args));
        }
        sql.append(" where p.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("p.shop_id", shopIds, args));
        sql.append(" and p.sku = ? ");
        args.add(msku);
        return getJdbcTemplate().queryForList(sql.toString(), String.class, args.toArray());
    }

    @Override
    public List<AsinListDto> getAsinByMskuList(Integer puid, List<Integer> shopIds, List<String> groupIds, List<String> msku) {
        StringBuilder sql = new StringBuilder(" select distinct p.shop_id shopId, p.asin asin, p.sku msku, p.parent_asin parentAsin from ods_t_product p ")
                .append(" join ").append(this.getJdbcHelper().getTable()).append(" r ")
                .append(" on p.puid = r.puid and p.shop_id = r.shop_id and p.asin = r.asins ")
                .append(" and r.puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("r.shop_id", shopIds, args));
        if (CollectionUtils.isNotEmpty(groupIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", groupIds, args));
        }
        sql.append(" where p.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("p.shop_id", shopIds, args));
        sql.append(SqlStringUtil.dealInList("p.sku", msku, args));
        return getJdbcTemplate().query(sql.toString(), args.toArray(), new BeanPropertyRowMapper<>(AsinListDto.class));
    }

    @Override
    public List<OdsAmazonSbAds> getAsinCampaignNum(int puid, List<Integer> shopIdList, List<String> asinList) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select distinct p.* from ");
        sql.append(" ( select puid,shop_id, asins,campaign_id from ods_t_amazon_sb_ads ");
        sql.append(" where puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
        sql.append(" and asins REGEXP '" + String.join("|", asinList) + "') p  ");
        sql.append(" inner join( ");
        sql.append(" select puid,shop_id,campaign_id from ods_t_amazon_ad_campaign_all ");
        sql.append(" where puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
        sql.append(" and serving_status in('CAMPAIGN_STATUS_ENABLED', 'RUNNING', 'running','CAMPAIGN_OUT_OF_BUDGET', 'OUT_OF_BUDGET', 'outOfBudget') ");
        sql.append(" ) c on p.puid = c.puid and p.shop_id  = c.shop_id and p.campaign_id  = c.campaign_id ");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(OdsAmazonSbAds.class), args.toArray());
    }

}

