package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.asins.PageListAsinsRequest;
import com.meiyunji.sponsored.service.cpc.bo.AdAsinOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.AdKeywordOrderBo;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdTargetStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.qo.ReportAdvancedFilterBaseQo;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.dao.IOdsCpcTargetingReportDao;
import com.meiyunji.sponsored.service.doris.po.OdsCpcTargetingReport;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTargetingMatrixTopDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.KeywordDataFieldEnum;
import com.meiyunji.sponsored.service.util.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.beans.BeanUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 商品投放报告(OdsCpcTargetingReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:22
 */
@Repository
@Slf4j
public class OdsCpcTargetingReportDaoImpl extends DorisBaseDaoImpl<OdsCpcTargetingReport> implements IOdsCpcTargetingReportDao {
    private static final Map<String, String> targetingFieldSumMap = Maps.newHashMap();
    private static final Map<String, String> targetingContainRateFieldSumMap = Maps.newHashMap();
    private static final Map<String, String> targetingOrderFieldMap = Maps.newHashMap();

    static {
        targetingContainRateFieldSumMap.put(DashboardDataFieldEnum.COST.getCode(), "ifnull(sum(report.cost * c.rate), 0) as cost ");
        targetingContainRateFieldSumMap.put(DashboardDataFieldEnum.TOTAL_SALES.getCode(), "ifnull(sum(total_sales * c.rate), 0) as totalSales ");
        targetingContainRateFieldSumMap.put(DashboardDataFieldEnum.ACOS.getCode(), " ifnull(ROUND(ifnull(sum(cost * c.rate), 0)/ ifnull(sum(total_sales * c.rate), 0), 4), 0) as acos ");
        targetingContainRateFieldSumMap.put(DashboardDataFieldEnum.ROAS.getCode(), " ifnull(ROUND(ifnull(sum(total_sales * c.rate), 0)/ ifnull(sum(cost * c.rate), 0), 4), 0) as roas ");

        targetingFieldSumMap.put(DashboardDataFieldEnum.IMPRESSIONS.getCode(), "ifnull(sum(report.impressions), 0) as impressions ");
        targetingFieldSumMap.put(DashboardDataFieldEnum.CLICKS.getCode(), "ifnull(sum(report.clicks), 0) as clicks ");
        targetingFieldSumMap.put(DashboardDataFieldEnum.ORDER_NUM.getCode(), " ifnull(sum(sale_num), 0) as orderNum ");
        targetingFieldSumMap.put(DashboardDataFieldEnum.SALE_NUM.getCode(), "ifnull(sum(order_num), 0) as saleNum ");
        targetingFieldSumMap.put(DashboardDataFieldEnum.CLICK_RATE.getCode(), " ifnull(ROUND(ifnull(sum(clicks)/ sum(impressions), 0), 4), 0) as clickRate ");//点击率
        targetingFieldSumMap.put(DashboardDataFieldEnum.CONVERSION_RATE.getCode(), " ifnull(ROUND(ifnull(sum(order_num)/ sum(clicks), 0), 4), 0) as conversionRate ");//转化率

        targetingOrderFieldMap.put(KeywordDataFieldEnum.IMPRESSIONS.getCode(), " impressions as impressions ");
        targetingOrderFieldMap.put(KeywordDataFieldEnum.CLICKS.getCode(), " clicks as clicks ");
        targetingOrderFieldMap.put(KeywordDataFieldEnum.COST.getCode(), " cost as cost ");
        targetingOrderFieldMap.put(KeywordDataFieldEnum.ORDER_NUM.getCode(), " sale_num as adOrder ");
        targetingOrderFieldMap.put(KeywordDataFieldEnum.TOTAL_SALES.getCode(), " total_sales as adSales ");
        targetingOrderFieldMap.put(KeywordDataFieldEnum.CPC.getCode(), " cost as cost,clicks as clicks ");
        targetingOrderFieldMap.put(KeywordDataFieldEnum.CPA.getCode(), " cost as cost,sale_num as adOrder ");
        targetingOrderFieldMap.put(KeywordDataFieldEnum.ACOS.getCode(), " cost as cost,total_sales as adSales ");
        targetingOrderFieldMap.put(KeywordDataFieldEnum.ROAS.getCode(), " cost as cost,total_sales as adSales ");
        targetingOrderFieldMap.put(KeywordDataFieldEnum.CONVERSION_RATE.getCode(), " clicks as clicks,sale_num as adOrder ");
        targetingOrderFieldMap.put(KeywordDataFieldEnum.CLICK_RATE.getCode(), " clicks as clicks,impressions as impressions ");
    }
    @Override
    public Page<TargetPageVo> getTargetPage(Integer puid, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();

        StringBuilder sqlCountSb = new StringBuilder("select count(*) from (select t.target_id ");
        StringBuilder sqlSb = new StringBuilder("select ANY(t.target_id) targetId ");
        StringBuilder sb = new StringBuilder(" from ods_t_amazon_ad_targeting t left join ");
        //报告数据先聚合，减少内存占用
        sb.append(this.getTargetPageJoinReportSql(puid, param, param.getStartDate(), param.getEndDate(), argsList, false));
        sb.append(" on t.puid = r.puid and t.shop_id = r.shop_id and t.target_id = r.target_id ");
        if ((param.getUseAdvanced() && (param.getBidMax() != null || param.getBidMin() != null))
                || (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType()) && SqlStringReportUtil.BID.equals(param.getOrderField()))) {
            sb.append(" join ods_t_amazon_ad_group g on t.puid = g.puid and t.shop_id = g.shop_id and t.ad_group_id = g.ad_group_id ")
                    .append(" and g.puid = ? and g.shop_id = ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }

        sb.append(this.getTargetPageWhereSql(puid, param, argsList));
        sb.append(" group by t.target_id ");
        if (param.getUseAdvanced()) {
            sb.append(this.getTargetPageHavingSql(param, argsList));
        }
        sqlCountSb.append(sb).append(" ) f");
        sqlSb.append(sb);
        //排序
        sqlSb.append(SqlStringReportUtil.getDorisTargetPageOrderBySql(param.getOrderField(), param.getOrderType(), "ANY(t.create_time) desc", param.getType()) + ", targetId desc");

        Object[] args = argsList.toArray();
        return getPageResultByClass(param.getPageNo(), param.getPageSize(), sqlCountSb.toString(), args, sqlSb.toString(), args, TargetPageVo.class);
    }

    @Override
    public int getTargetAllCount(Integer puid, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();

        StringBuilder sqlCountSb = new StringBuilder("select count(*) from (select t.target_id ");
        StringBuilder sb = new StringBuilder(" from ods_t_amazon_ad_targeting t left join ");
        //报告数据先聚合，减少内存占用
        sb.append(this.getTargetPageJoinReportSql(puid, param, param.getStartDate(), param.getEndDate(), argsList, false));
        sb.append(" on t.puid = r.puid and t.shop_id = r.shop_id and t.target_id = r.target_id ");

        if ((param.getUseAdvanced() && (param.getBidMax() != null || param.getBidMin() != null))
                || (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType()) && SqlStringReportUtil.BID.equals(param.getOrderField()))) {
            sb.append(" join ods_t_amazon_ad_group g on t.puid = g.puid and t.shop_id = g.shop_id and t.ad_group_id = g.ad_group_id ")
                    .append(" and g.puid = ? and g.shop_id = ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }

        sb.append(this.getTargetPageWhereSql(puid, param, argsList));
        sb.append(" group by t.target_id ");
        if (param.getUseAdvanced()) {
            sb.append(this.getTargetPageHavingSql(param, argsList));
        }
        sqlCountSb.append(sb).append(" ) f");
        Object[] args = argsList.toArray();
        return countPageResult(puid, sqlCountSb.toString(), args);
    }

    @Override
    public List<TargetPageVo> getReportListByTargetIds(Integer puid, Integer shopId, List<String> keywordIds, String startStr, String endStr) {
        if (CollectionUtils.isEmpty(keywordIds)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ANY(target_id) targetId")
                .append(",IFNULL(SUM(impressions), 0) impressions,IFNULL(SUM(clicks), 0) clicks,IFNULL(SUM(cost), 0) cost, IFNULL(SUM(sale_num), 0) sale_num, IFNULL(SUM(ad_order_num), 0) ad_order_num, IFNULL(SUM(total_sales), 0) total_sales, IFNULL(SUM(ad_sales), 0) `ad_sales`,IFNULL(SUM(ad_sale_num), 0) `ad_sale_num`, IFNULL(SUM(order_num), 0) order_num, IFNULL(MAX(top_of_search_is), 0) max_top_is, IFNULL(MIN(top_of_search_is), 0) min_top_is")
                .append(" from ").append(this.getJdbcHelper().getTable());
        sb.append(" where puid = ? and shop_id = ? and count_day >= ? and count_day <= ? ");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(startStr);
        argsList.add(endStr);
        sb.append(SqlStringUtil.dealBitMapDorisInList("target_id", keywordIds, argsList));
        sb.append(" group by target_id ");
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(TargetPageVo.class));
    }

    @Override
    public AdMetricDto getTargetPageSumMetricData(Integer puid, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();

        StringBuilder topSb = new StringBuilder("select SUM(cost) sumCost,SUM(sale_num) sumAdOrderNum,SUM(total_sales) sumAdSale, SUM(order_num) sumOrderNum from ( ");
        StringBuilder sqlSb = new StringBuilder(" select ANY(t.puid) puid, ANY(t.shop_id) shopId, ANY(t.ad_group_id) adGroupId, ANY(t.bid) bid, SUM(cost) cost,SUM(sale_num) sale_num,SUM(total_sales) total_sales, SUM(order_num) order_num ")
                .append(" from ods_t_amazon_ad_targeting t join ");
        //报告数据先聚合，减少内存占用
        sqlSb.append(this.getTargetPageJoinReportSql(puid, param, param.getStartDate(), param.getEndDate(), argsList, true));
        sqlSb.append(" on t.puid = r.puid and t.shop_id = r.shop_id and t.target_id = r.target_id ");
        if ((param.getUseAdvanced() && (param.getBidMax() != null || param.getBidMin() != null))) {
            sqlSb.append(" join ods_t_amazon_ad_group g on t.puid = g.puid and t.shop_id = g.shop_id and t.ad_group_id = g.ad_group_id ")
                    .append(" and g.puid = ? and g.shop_id = ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }

        sqlSb.append(this.getTargetPageWhereSql(puid, param, argsList));
        sqlSb.append(" group by t.target_id ");
        if (param.getUseAdvanced()) {
            sqlSb.append(this.getTargetPageHavingSql(param, argsList));
        }

        sqlSb = new StringBuilder(topSb).append(sqlSb).append(" ) s");
        Object[] args = argsList.toArray();
        List<AdMetricDto> list = getJdbcTemplate().query(sqlSb.toString(), args, new BeanPropertyRowMapper<>(AdMetricDto.class));
        return list.size() == 1 ? list.get(0) : null;
    }

    @Override
    public List<String> getTargetIdListByPage(Integer puid, String startStr, String endStr, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();
        String sql = this.getTargetIdListByPageSql(puid, param, argsList, false);
        return this.getJdbcTemplate().queryForList(sql, String.class, argsList.toArray());
    }

    private String getTargetIdListByPageSql(Integer puid, TargetingPageParam param, List<Object> argsList, boolean isLeftJoin) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select t.target_id from ods_t_amazon_ad_targeting t ");
        if (isLeftJoin) {
            sql.append(" left ");
        }
        sql.append(" join ");
        //报告数据先聚合，减少内存占用
        sql.append(this.getTargetPageJoinReportSql(puid, param, param.getStartDate(), param.getEndDate(), argsList, false));
        sql.append(" on t.puid = r.puid and t.shop_id = r.shop_id and t.target_id = r.target_id ");
        //若有竞价排序或者竞价的高级筛选，需要连广告组表，获取广告组默认竞价
        if ((param.getUseAdvanced() && (param.getBidMax() != null || param.getBidMin() != null))) {
            sql.append(" join ods_t_amazon_ad_group g on t.puid = g.puid and t.shop_id = g.shop_id and t.ad_group_id = g.ad_group_id ")
                    .append(" and g.puid = ? and g.shop_id = ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }
        sql.append(this.getTargetPageWhereSql(puid, param, argsList));
        sql.append(" group by t.target_id ");
        if (param.getUseAdvanced()) {
            sql.append(this.getTargetPageHavingSql(param, argsList));
        }
        return sql.toString();
    }

    @Override
    public List<AdHomePerformancedto> getReportAggregateByTargetIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> targetIdList, boolean isGroupByDate) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select ");
        if (isGroupByDate) {
            sql.append(" ANY(count_date) count_date, ");
        }
        sql.append(" sum(cost) `adCost`,sum(total_sales) `adSale`,sum(ad_sales)  `adSales`,sum(impressions) `impressions`, ");
        sql.append(" sum(clicks) `clicks`,sum(order_num) salesNum,sum(ad_order_num) `orderNum`,sum(sale_num) `adOrderNum`,sum(ad_sale_num) adSaleNum from  ");
        sql.append(this.getJdbcHelper().getTable());
        sql.append(" where puid= ? and shop_id= ? ");
        argsList.add(puid);
        argsList.add(shopId);
        sql.append(SqlStringUtil.dealBitMapDorisInList("target_id", targetIdList, argsList));
        sql.append("  and count_day >= ? and count_day <= ? ");
        if (isGroupByDate) {
            sql.append(" group by count_day ");
        }
        argsList.add(startStr);
        argsList.add(endStr);
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AdHomePerformancedto.class));
    }

    @Override
    public AdHomePerformancedto getReportAggregateCompareData(Integer puid, TargetingPageParam param, String startStr, String endStr) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select ");
        sql.append(" sum(cost) `adCost`,sum(total_sales) `adSale`,sum(ad_sales)  `adSales`,sum(impressions) `impressions`, ");
        sql.append(" sum(clicks) `clicks`,sum(order_num) salesNum,sum(ad_order_num) `orderNum`,sum(sale_num) `adOrderNum`,sum(ad_sale_num) adSaleNum from  ");
        sql.append(this.getJdbcHelper().getTable());
        sql.append(" where puid= ? and shop_id= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        sql.append("  and count_day >= ? and count_day <= ? ");
        argsList.add(startStr);
        argsList.add(endStr);
        sql.append(" and target_id in ( ").append(this.getTargetIdListByPageSql(puid, param, argsList, true)).append(" ) ");
        List<AdHomePerformancedto> list = getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(AdHomePerformancedto.class), argsList.toArray());
        return list.size() == 1 ? list.get(0) : new AdHomePerformancedto();
    }

    /**
     * 投放列表页where条件sql拼接
     */
    private String getTargetPageWhereSql(Integer puid, TargetingPageParam param, List<Object> argsList) {
        StringBuilder sb = new StringBuilder();
        sb.append(" where t.puid = ? and t.shop_id = ? and type != 'negativeAsin' ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            List<String> list = StringUtil.splitStr(param.getCampaignId());
            sb.append(SqlStringUtil.dealBitMapDorisInList("t.campaign_id", list, argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            sb.append(SqlStringUtil.dealBitMapDorisInList("t.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> list = StringUtil.splitStr(param.getGroupId());
            sb.append(SqlStringUtil.dealBitMapDorisInList("t.ad_group_id", list, argsList));

        }
        //标签管理
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("t.target_id", param.getTargetIds(), argsList));
        }

        // 广告策略筛选
        if(CollectionUtils.isNotEmpty(param.getAdStrategyTypeList())){
            String sql = AdTargetStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(),param.getAutoRuleGroupIds(), argsList, "t.target_id","t.ad_group_id");
            if(StringUtils.isNotEmpty(sql)){
                sb.append(sql);
            }
        }

        if (StringUtils.isNotBlank(param.getChosenTargetType())) {
            if ("auto".equalsIgnoreCase(param.getChosenTargetType())) {
                sb.append(" and t.type = 'auto' ");
            } else {
                sb.append(" and t.type in ('asin','category') ");
            }
        }

        if (StringUtils.isNotBlank(param.getFilterTargetType())) {
            if ("asin".equalsIgnoreCase(param.getFilterTargetType())) {
                sb.append(" and t.type='asin' ");
            } else if ("category".equalsIgnoreCase(param.getFilterTargetType())) {
                sb.append(" and t.type='category'  ");
            } else {
                sb.append(" and t.targeting_value = ? and t.type = 'auto' ");
                argsList.add(param.getFilterTargetType());
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                sb.append(" and lower(t.targeting_value) = ? and t.type = 'asin' ");
                argsList.add(param.getSearchValue().toLowerCase());
            } else if ("category".equalsIgnoreCase(param.getSearchField())) {
                sb.append(" and lower(t.targeting_value) like ? and t.type = 'category' ");
                argsList.add("%" + param.getSearchValue().toLowerCase() + "%");
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            sb.append(SqlStringUtil.dealInList("t.state", statusList, argsList));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                sb.append(" and t.serving_status = ? ");
                argsList.add(AmazonAdTargeting.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                sb.append(SqlStringUtil.dealInList("t.serving_status", list, argsList));
            }
        }
        return sb.toString();
    }


    /**
     * 投放投放列表页having条件sql拼接
     */
    private String getTargetPageHavingSql(TargetingPageParam param, List<Object> argsList) {
        ReportAdvancedFilterBaseQo qo = new ReportAdvancedFilterBaseQo();
        BeanUtils.copyProperties(param, qo);
        return SqlStringReportUtil.getDorisSpTargetPageHavingSql(qo, argsList);
    }

    @Override
    public List<DashboardAdTargetingMatrixTopDto> getTargetingTopList(Integer puid, List<String> marketplaceIdList,
                                                                      List<Integer> shopIdList, String currency,
                                                                      String startDate, String endDate,
                                                                      DashboardDataFieldEnum dataField, List<String> targetingIdList,
                                                                      Integer limit, DashboardOrderByEnum orderBy, List<String> siteToday, Boolean isSiteToday,
                                                                      List<String> portfolioIds, List<String> campaignIds, Boolean noZero) {
        boolean isWhere = false;
        if (Objects.isNull(targetingFieldSumMap.get(dataField.getCode())) && Objects.isNull(targetingContainRateFieldSumMap.get(dataField.getCode()))) {
            log.error("query can not get sort sql from the map, dataFiled:{}", dataField.getCode());
            return Collections.emptyList();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        //需要使用哪个字段排序就从map中取出哪个字段
        sb.append(" SELECT 'sp' as `type`, report.target_id targetingId, ");
        if (Objects.nonNull(targetingContainRateFieldSumMap.get(dataField.getCode()))) {
            for (String rateField : targetingContainRateFieldSumMap.keySet()) {
                sb.append(targetingContainRateFieldSumMap.get(rateField));
                sb.append(",");
            }
            sb.deleteCharAt(sb.length() - 1); //去除最后一列查询语句末尾的","
        } else {
            sb.append(targetingFieldSumMap.get(dataField.getCode()));
        }
        sb.append(" from ").append(getJdbcHelper().getTable()).append(" report ");
        if (Objects.nonNull(targetingContainRateFieldSumMap.get(dataField.getCode()))) {
            sb.append(" join (select * from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency and puid = ? and `to` = ? ");
            sb.append(" and c.month >= ").append(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(" and c.month <= ").append(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(") c");
            sb.append(" on c.marketplace_id = report.marketplace_id and report.count_month = c.month ");
            sb.append(" and report.puid = ? ");
            argsList.add(puid);
            argsList.add(currency);
        } else {
            sb.append(" where report.puid = ? ");
            isWhere = true;
        }
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append(" and report.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(" and report.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(targetingIdList)) {
            sb.append(" and report.target_id in ('").append(StringUtils.join(targetingIdList, "','")).append("') ");
        }

        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', report.marketplace_id, report.count_day)", siteToday, argsList));
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("report.campaign_id", campaignIds, argsList));
        }
        if (Boolean.TRUE.equals(noZero)) {
            sb.append(" and " + dataField.getCode() + " <> 0 ");
        }


        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (isWhere) {
                sb.append(" and ");
            } else {
                sb.append(" where ");
            }
            sb.append(" report.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = 'sp' ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }

        sb.append(" group by report.target_id ");
        sb.append(" ORDER BY ").append(dataField.getCode());
        if (StringUtils.isNotEmpty(orderBy.getCode())) {
            sb.append(" ").append(orderBy.getCode());
        }
        if (Objects.nonNull(limit) && limit != 0) {
            sb.append(" LIMIT ").append(limit);
        }
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(DashboardAdTargetingMatrixTopDto.class), argsList.toArray());
    }

    @Override
    public List<DashboardAdTargetingMatrixTopDto> getTargetingInfoList(Integer puid, List<String> marketplaceIdList, List<Integer> shopIdList,
                                                                       String currency, String startDate, String endDate, DashboardDataFieldEnum dataField, List<String> targetingIdList, DashboardOrderByEnum orderBy,
                                                                       List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds, List<String> campaignIds, Boolean noZero) {
        if (Objects.isNull(targetingFieldSumMap.get(dataField.getCode())) && Objects.isNull(targetingContainRateFieldSumMap.get(dataField.getCode()))) {
            log.error("query can not get sort sql from the map, dataFiled:{}", dataField.getCode());
            return Collections.emptyList();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        //需要使用哪个字段排序就从map中取出哪个字段
        sb.append(" SELECT report.target_id targetingId, ");
        if (Objects.nonNull(targetingContainRateFieldSumMap.get(dataField.getCode()))) {
            targetingFieldSumMap.forEach((k, v) -> {
                    sb.append(v);
                    sb.append(",");
            });
            sb.deleteCharAt(sb.length() - 1); //去除最后一列查询语句末尾的","
        } else {
            for (String rateField : targetingContainRateFieldSumMap.keySet()) {
                sb.append(targetingContainRateFieldSumMap.get(rateField));
                sb.append(",");
            }
            targetingFieldSumMap.forEach((k, v) -> {
                if (!k.equals((dataField.getCode()))) {
                    sb.append(v);
                    sb.append(",");
                }
            });
            sb.deleteCharAt(sb.length() - 1); //去除最后一列查询语句末尾的","
        }
        sb.append(" from ").append(getJdbcHelper().getTable()).append(" report ");
        if (Objects.isNull(targetingContainRateFieldSumMap.get(dataField.getCode()))) {
            sb.append(" join (select * from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency and puid = ? and `to` = ? ");
            sb.append(" and c.month >= ").append(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(" and c.month <= ").append(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(") c");
            sb.append(" on c.marketplace_id = report.marketplace_id and report.count_month = c.month and report.puid = ?");
            argsList.add(puid);
            argsList.add(currency);
        } else {
            sb.append(" where report.puid = ? ");
        }
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append(" and report.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(" and report.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(targetingIdList)) {
            sb.append(" and report.target_id in ('").append(StringUtils.join(targetingIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', report.marketplace_id, report.count_day)", siteToday, argsList));
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" and report.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = 'sp' ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("report.campaign_id", campaignIds, argsList));
        }
        if (Boolean.TRUE.equals(noZero)) {
            sb.append(" and " + dataField.getCode() + " <> 0 ");
        }
        sb.append(" group by report.target_id ");
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(DashboardAdTargetingMatrixTopDto.class), argsList.toArray());
    }

    @Override
    public List<AsinLibsDetailVo> getSpAsinReportDataByTargetId(Integer puid, AsinLibsDetailParam param, List<String> spTargetIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder();
        selectSql.append("select target_id targetId, any(targeting_text) targetingText, sum(cost * c.rate) cost, sum(impressions) impressions, " +
                "sum(clicks) clicks, sum(sale_num) saleNum, sum(total_sales * c.rate) totalSales, ifnull(ROUND(ifnull(sum(cost), 0)/ ifnull(sum(total_sales), 0), 4), 0) acos, " +
                "ifnull(ROUND(ifnull(sum(total_sales), 0)/ ifnull(sum(cost), 0), 4), 0) roas, ifnull(sum(cost * c.rate)/sum(clicks),0) cpc, ifnull(sum(cost * c.rate)/sum(sale_num),0) cpa, " +
                "ifnull(ROUND(ifnull(sum(sale_num)/ sum(clicks), 0), 4), 0) salesConversionRate, ifnull(ROUND(ifnull(sum(clicks)/ sum(impressions), 0), 4), 0) clickRate ");
        selectSql.append(" from ").append(getJdbcHelper().getTable()).append(" r ");
        selectSql.append(" join (select * from dim_currency_rate where puid = ? and `to` = ?) c on r.puid = c.puid and DATE_FORMAT(r.count_date, '%Y%m') = c.month ");
        argsList.add(puid);
        argsList.add(param.getTo());
        selectSql.append(" join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        selectSql.append(" where r.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
            selectSql.append(SqlStringUtil.dealInList("r.shop_id", param.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCountry())) {
            selectSql.append(SqlStringUtil.dealInList("r.marketplace_id", param.getCountry(), argsList));
        }
        selectSql.append(" and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (CollectionUtils.isNotEmpty(spTargetIds)) {
            selectSql.append(SqlStringUtil.dealInList("r.target_id", spTargetIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchAdGroupList())) {
            selectSql.append(SqlStringUtil.dealInList("r.ad_group_id", param.getSearchAdGroupList(), argsList));
        }
        selectSql.append(" group by target_id ");
        return getJdbcTemplate().query(selectSql.toString(), new ObjectMapper<>(AsinLibsDetailVo.class), argsList.toArray());
    }

    @Override
    public List<AdAsinOrderBo> getOrderFieldByAsins(Integer puid, List<String> shopIds, PageListAsinsRequest param, List<String> asinList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSp = new StringBuilder("select targeting_text asin ");
        if (StringUtils.isNotBlank(param.getOrderField())) {
            sqlSp.append(", ");
            sqlSp.append(targetingOrderFieldMap.get(param.getOrderField()));
        }
        sqlSp.append(" from ods_t_cpc_targeting_report where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sqlSp.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        //通过来源站点筛选
        if (StringUtils.isNotEmpty(param.getMarketplaceId())) {
            sqlSp.append(SqlStringUtil.dealInList("marketplace_id", Arrays.asList(param.getMarketplaceId().split(",")), argsList));
        }

        if (StringUtils.isNotBlank(param.getStartDate()) && StringUtils.isNotBlank(param.getEndDate())) {
            sqlSp.append(" and count_day >= ? and count_day <= ? ");
            argsList.add(param.getStartDate());
            argsList.add(param.getEndDate());
        }
//        if (CollectionUtils.isNotEmpty(asinList)) {
//            // 创建一个新的列表来存储转换后的字符串
//            List<String> lowerCaseList = new ArrayList<>();
//            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
//            for (String str : asinList) {
//                lowerCaseList.add(str.toLowerCase());
//            }
//            sqlSp.append(SqlStringUtil.dealInList("lower(targeting_text)", lowerCaseList, argsList));
//        }
        sqlSp.append(" and target_id in ");

        sqlSp.append(" (SELECT target_id FROM ods_t_amazon_ad_targeting s WHERE s.puid = ? ");
        argsList.add(puid);
        sqlSp.append("and s.type = 'asin' ");

        if (CollectionUtils.isNotEmpty(asinList)) {
            // 创建一个新的列表来存储转换后的字符串
            List<String> lowerCaseList = new ArrayList<>();
            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
            for (String str : asinList) {
                lowerCaseList.add(str.toLowerCase());
            }
            sqlSp.append(SqlStringUtil.dealInList("lower(s.targeting_value)", lowerCaseList, argsList));
        }
        sqlSp.append(" ) ");
        return getJdbcTemplate().query(sqlSp.toString(), new ObjectMapper<>(AdAsinOrderBo.class), argsList.toArray());
    }

    @Override
    public List<AsinLibsDetailVo> getAsinReportByAsins(Integer puid, PageListAsinsRequest param,
                                                       String startDate, String endDate,
                                                       List<String> asinList, String currency) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select targeting_text asin, sum(cost * c.rate) cost, sum(impressions) impressions, sum(clicks) clicks, sum(sale_num) saleNum, sum(total_sales * c.rate) totalSales from ods_t_cpc_targeting_report r ");
        sql.append("join (select * from dim_currency_rate where puid = ?");
        argsList.add(puid);
        sql.append(" and `to` = ? ");
        argsList.add(currency);
        sql.append(" ) c on r.puid = c.puid and DATE_FORMAT(r.count_date, '%Y%m') = c.month ");
        sql.append("join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append("where r.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopListList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", param.getShopListList(), argsList));
        }
        //通过来源站点筛选
        if (CollectionUtils.isNotEmpty(param.getContryList())) {
            sql.append(SqlStringUtil.dealInList("r.marketplace_id", param.getContryList(), argsList));
        }

        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            sql.append(" and count_day >= ? and count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }
//        if (CollectionUtils.isNotEmpty(asinList)) {
//            // 创建一个新的列表来存储转换后的字符串
//            List<String> lowerCaseList = new ArrayList<>();
//            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
//            for (String str : asinList) {
//                lowerCaseList.add(str.toLowerCase());
//            }
//            sql.append(SqlStringUtil.dealInList("lower(targeting_text)", lowerCaseList, argsList));
//        }
        sql.append(" and target_id in ");

        sql.append(" (SELECT target_id FROM ods_t_amazon_ad_targeting s WHERE s.puid = ? ");
        argsList.add(puid);

        sql.append("and s.type = 'asin' ");

        if (CollectionUtils.isNotEmpty(asinList)) {
            // 创建一个新的列表来存储转换后的字符串
            List<String> lowerCaseList = new ArrayList<>();
            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
            for (String str : asinList) {
                lowerCaseList.add(str.toLowerCase());
            }
            sql.append(SqlStringUtil.dealInList("lower(s.targeting_value)", lowerCaseList, argsList));
        }
        sql.append(" ) ");
        sql.append(" group by targeting_text");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(AsinLibsDetailVo.class), argsList.toArray());
    }

    @Override
    public List<OdsCpcTargetingReport> getSumReportByTargetIdsByCountDate(int puid, List<Integer> shopIds, List<String> marketplaceIds, String startStr, String endStr,
                                                                          List<String> targetIds, boolean changeRate, String currency) {
        List<Object> args = new ArrayList<>();
        //子查询sql
        StringBuilder sql = new StringBuilder("SELECT count_day,")
                .append("ifnull(sum(`impressions`), 0) impressions, ifnull(sum(`clicks`), 0) clicks, ifnull(sum(`order_num`), 0) order_num, ifnull(sum(ad_order_num), 0) ad_order_num,")
                .append("ifnull(sum(`sale_num`), 0) sale_num, ifnull(sum(`ad_sale_num`), 0) ad_sale_num, ");
        if (changeRate) {
            sql.append(" ifnull(sum(`cost` * d.rate), 0) cost, ")
                    .append(" ifnull(sum(`total_sales` * d.rate), 0) total_sales, ")
                    .append(" ifnull(sum(`ad_sales` * d.rate), 0) ad_sales ");
        } else {
            sql.append(" ifnull(sum(`cost`), 0) cost, ")
                    .append(" ifnull(sum(`total_sales`), 0) total_sales, ")
                    .append(" ifnull(sum(`ad_sales`), 0) ad_sales ");
        }
        sql.append(" from ").append(this.getJdbcHelper().getTable()).append(" r ");
        if (changeRate) {
            String start = DateUtil.dateToStrWithFormat(DateUtil.strToDate(startStr, "yyyy-MM-dd"), "yyyyMM");
            String end = DateUtil.dateToStrWithFormat(DateUtil.strToDate(endStr, "yyyy-MM-dd"), "yyyyMM");
            //关联币种表、汇率表
            sql.append(" join ");
            sql.append(" ( select m.marketplace_id,c.month,c.rate from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency and puid = ? and `to` = ? and c.month >= ? and c.month <= ? ");
            args.add(puid);
            args.add(currency);
            args.add(start);
            args.add(end);
            sql.append(SqlStringUtil.dealInList("m.marketplace_id", marketplaceIds, args));
            sql.append(" ) d ");
            sql.append(" on d.marketplace_id = r.marketplace_id and d.month = r.count_month ");
        }
        sql.append(" where`puid`=? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealBitMapDorisInList("shop_id", shopIds, args));
        sql.append(" and `count_day`>=? and count_day<=? ");
        args.add(startStr);
        args.add(endStr);
        sql.append(SqlStringUtil.dealBitMapDorisInList("target_id", targetIds, args));
        sql.append(" group by count_day ");
        return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(OdsCpcTargetingReport.class), args.toArray());
    }

    /**
     * 投放页面join报告表sql公有方法
     */
    private String getTargetPageJoinReportSql(Integer puid, TargetingPageParam param, String startStr, String endStr, List<Object> argsList, boolean isSumMetricData) {
        StringBuilder sb = new StringBuilder(" (select any_value(puid) puid, any_value(shop_id) shop_id, target_id ");
        Set<String> field = SqlStringReportUtil.getDorisSpTargetPageHavingFieldSet(param.buildReportAdvancedFilterBaseQo());
        if (StringUtils.isNotBlank(param.getOrderField())) {
            field.addAll(SqlStringReportUtil.getDorisSpTargetPageOrderFieldSet(param.getOrderField()));
        }
        //兼容占比接口固定查出四个占比指标
        if (isSumMetricData) {
            field.addAll(Arrays.asList(SqlStringReportUtil.TargetReportSumEnum.cost.getField(), SqlStringReportUtil.TargetReportSumEnum.sale_num.getField(),
                    SqlStringReportUtil.TargetReportSumEnum.total_sales.getField(), SqlStringReportUtil.TargetReportSumEnum.order_num.getField()));
        }
        if (CollectionUtils.isNotEmpty(field)) {
            sb.append(", ").append(String.join(", ", field));
        }
        sb.append(" from ods_t_cpc_targeting_report ")
                .append(" where puid = ? and shop_id = ? and count_day >= ? and count_day <= ? ")
                .append(" group by target_id ) r");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startStr);
        argsList.add(endStr);
        return sb.toString();
    }
}

