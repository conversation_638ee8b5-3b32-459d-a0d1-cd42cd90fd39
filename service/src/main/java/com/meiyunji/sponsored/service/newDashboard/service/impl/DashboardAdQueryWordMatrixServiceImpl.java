package com.meiyunji.sponsored.service.newDashboard.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.protobuf.ProtocolStringList;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.rpc.newDashboard.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.batchCreate.dto.targeting.TargetingExpressionDTO;
import com.meiyunji.sponsored.service.batchCreate.enums.KeywordAndTargetingExcludeNeEnum;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdCampaignAllBo;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdGroupDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdGroup;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdTargetingDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsCpcQueryKeywordReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsCpcQueryTargetingReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsCpcSbQueryKeywordReportDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdTargeting;
import com.meiyunji.sponsored.service.doris.po.OdsCpcQueryKeywordReport;
import com.meiyunji.sponsored.service.doris.po.OdsCpcQueryTargetingReport;
import com.meiyunji.sponsored.service.doris.po.OdsCpcSbQueryKeywordReport;
import com.meiyunji.sponsored.service.enums.TargetingExpressionPredicate;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdQueryWordMatrixTopDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTargetingMatrixDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTargetingMatrixTopDto;
import com.meiyunji.sponsored.service.newDashboard.dto.QueryWordTargetingResolveExpressionDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardQueryWordMatchTypeEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardQueryWordTypeEnum;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdQueryWordMatrixService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.newDashboard.util.TargetingInfoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2024/4/26 16:42
 * @describe:
 */
@Service
@Slf4j
public class DashboardAdQueryWordMatrixServiceImpl implements IDashboardAdQueryWordMatrixService {

    @Autowired
    private IOdsCpcQueryKeywordReportDao odsCpcQueryKeywordReportDao;

    @Autowired
    private IOdsCpcQueryTargetingReportDao odsCpcQueryTargetingReportDao;

    @Autowired
    private IOdsCpcSbQueryKeywordReportDao odsCpcSbQueryKeywordReportDao;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IOdsAmazonAdTargetingDao odsAmazonAdTargetingDao;

    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;

    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;

    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;

    @Override
    public DashboardAdQueryWordMatrixVo getQueryWordMatrix(DashboardAdQueryWordMatrixRequest req) {
        //包含关键词投放，和商品投放
        //数据源为:sp的关键词投放，商品投放 ods_t_cpc_query_keyword_report, ods_t_cpc_query_targeting_report
        //sb:关键词投放 ods_t_cpc_sb_query_keyword_report
        List<DashboardAdTargetingMatrixDto> matrixResult = getMatrixList(req);
        //先填充计算属性
        matrixResult.forEach(CalculateAdDataUtil::calAdCalDataReflex);
        DashboardAdQueryWordMatrixVo.Builder result = DashboardAdQueryWordMatrixVo.newBuilder();
        result.addAllRows(dtoConvert(matrixResult));
        return result.build();
    }

    @Override
    public DashboardAdQueryWordMatrixSingleVo getQueryWordMatrixSingle(DashboardAdQueryWordMatrixRequest req) {
        List<DashboardAdTargetingMatrixDto> matrixResult = getMatrixList(req);
        DashboardAdQueryWordMatrixSingleVo.Builder result = DashboardAdQueryWordMatrixSingleVo.newBuilder();
        result.addAllRows(dtoConvert(matrixResult));
        return result.build();
    }

    private List<DashboardAdQueryWordMatrixData> dtoConvert(List<DashboardAdTargetingMatrixDto> matrixResult) {
        return matrixResult.stream().map(m -> {
            DashboardAdQueryWordMatrixData.Builder builder = DashboardAdQueryWordMatrixData.newBuilder();
            BeanUtils.copyProperties(m, builder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(m));
            Optional.ofNullable(m.getShopId()).ifPresent(builder::setShopId);
            Optional.ofNullable(m.getCost()).map(CalculateUtil::formatDecimal).ifPresent(builder::setCost);
            Optional.ofNullable(m.getTotalSales()).map(CalculateUtil::formatDecimal).ifPresent(builder::setTotalSales);
            Optional.ofNullable(m.getImpressions()).map(String::valueOf).ifPresent(builder::setImpressions);
            Optional.ofNullable(m.getClicks()).map(String::valueOf).ifPresent(builder::setClicks);
            Optional.ofNullable(m.getOrderNum()).map(String::valueOf).ifPresent(builder::setOrderNum);
            Optional.ofNullable(m.getSaleNum()).map(String::valueOf).ifPresent(builder::setSaleNum);
            Optional.ofNullable(m.getAcos()).map(CalculateUtil::formatPercent).ifPresent(builder::setAcos);
            Optional.ofNullable(m.getRoas()).map(String::valueOf).ifPresent(builder::setRoas);
            Optional.ofNullable(m.getClickRate()).map(CalculateUtil::formatPercent).ifPresent(builder::setClickRate);
            Optional.ofNullable(m.getConversionRate()).map(CalculateUtil::formatPercent).ifPresent(builder::setConversionRate);
            Optional.ofNullable(m.getCpc()).map(CalculateUtil::formatDecimal).ifPresent(builder::setCpc);
            Optional.ofNullable(m.getCpa()).map(CalculateUtil::formatDecimal).ifPresent(builder::setCpa);
            return builder.build();
        }).collect(Collectors.toList());
    }

    private QueryWordMatrixDataAvg getAvgData(List<DashboardAdTargetingMatrixDto> matrixResult) {
        //再计算各属性平均值
        OptionalDouble avgCostOp = matrixResult.stream().mapToDouble(m -> Optional.ofNullable(m.getCost())
                .map(BigDecimal::doubleValue).orElse(null)).average();
        OptionalDouble avgTotalSaleOp = matrixResult.stream().mapToDouble(m -> Optional.ofNullable(m.getTotalSales())
                .map(BigDecimal::doubleValue).orElse(0d)).average();
        OptionalDouble avgImpressionOp = matrixResult.stream()
                .mapToLong(m -> Optional.ofNullable(m.getImpressions()).orElse(0L)).average();
        OptionalDouble avgClicksOp = matrixResult.stream()
                .mapToLong(m -> Optional.ofNullable(m.getClicks()).orElse(0)).average();
        OptionalDouble avgOrderNumOp = matrixResult.stream()
                .mapToLong(m -> Optional.ofNullable(m.getOrderNum()).orElse(0)).average();
        OptionalDouble avgSaleNumOp = matrixResult.stream()
                .mapToLong(m -> Optional.ofNullable(m.getSaleNum()).orElse(0)).average();
        OptionalDouble avgAcosOp = matrixResult.stream()
                .mapToDouble(m -> Optional.ofNullable(m.getAcos()).map(BigDecimal::doubleValue).orElse(0d)).average();
        OptionalDouble avgRoasOp = matrixResult.stream()
                .mapToDouble(m -> Optional.ofNullable(m.getRoas()).map(BigDecimal::doubleValue).orElse(0d)).average();
        OptionalDouble avgClickRateOp = matrixResult.stream()
                .mapToDouble(m -> Optional.ofNullable(m.getClickRate()).map(BigDecimal::doubleValue).orElse(0d)).average();
        OptionalDouble avgConversionRateOp = matrixResult.stream()
                .mapToDouble(m -> Optional.ofNullable(m.getConversionRate()).map(BigDecimal::doubleValue).orElse(0d)).average();
        OptionalDouble avgCpcOp = matrixResult.stream()
                .mapToDouble(m -> Optional.ofNullable(m.getCpc()).map(BigDecimal::doubleValue).orElse(0d)).average();
        OptionalDouble avgCpaOp = matrixResult.stream()
                .mapToDouble(m -> Optional.ofNullable(m.getCpa()).map(BigDecimal::doubleValue).orElse(0d)).average();

        QueryWordMatrixDataAvg.Builder dataAvg = QueryWordMatrixDataAvg.newBuilder();
        //按统一格式返回平均数
        avgCostOp.ifPresent(cost -> {
            BigDecimal costDecimal = new BigDecimal(cost);
            dataAvg.setCostAvg(CalculateUtil.formatDecimal(costDecimal));
        });
        avgTotalSaleOp.ifPresent(totalSales -> {
            BigDecimal totalSalesDecimal = new BigDecimal(totalSales);
            dataAvg.setTotalSalesAvg(CalculateUtil.formatDecimal(totalSalesDecimal));
        });
        avgImpressionOp.ifPresent(impressions -> {
            BigDecimal impressionsDecimal = new BigDecimal(impressions);
            dataAvg.setTotalSalesAvg(CalculateUtil.formatDecimal(impressionsDecimal));
        });
        avgClicksOp.ifPresent(clicks -> {
            BigDecimal clicksDecimal = new BigDecimal(clicks);
            dataAvg.setClicksAvg(CalculateUtil.formatDecimal(clicksDecimal));
        });
        avgOrderNumOp.ifPresent(orderNum -> {
            BigDecimal orderNumDecimal = new BigDecimal(orderNum);
            dataAvg.setOrderNumAvg(CalculateUtil.formatDecimal(orderNumDecimal));
        });
        avgSaleNumOp.ifPresent(saleNum -> {
            BigDecimal saleNumNumDecimal = new BigDecimal(saleNum);
            dataAvg.setSaleNumAvg(CalculateUtil.formatDecimal(saleNumNumDecimal));
        });
        avgAcosOp.ifPresent(acos -> {
            BigDecimal acosNumNumDecimal = new BigDecimal(acos);
            dataAvg.setAcosAvg(CalculateUtil.formatDecimal(acosNumNumDecimal));
        });
        avgRoasOp.ifPresent(roas -> {
            BigDecimal roasNumDecimal = new BigDecimal(roas);
            dataAvg.setRoasAvg(CalculateUtil.formatDecimal(roasNumDecimal));
        });
        avgClickRateOp.ifPresent(clickRate -> {
            BigDecimal clickRateDecimal = new BigDecimal(clickRate);
            dataAvg.setClickRateAvg(CalculateUtil.formatDecimal(clickRateDecimal));
        });
        avgConversionRateOp.ifPresent(conversionRate -> {
            BigDecimal conversionRateDecimal = new BigDecimal(conversionRate);
            dataAvg.setConversionRateAvg(CalculateUtil.formatDecimal(conversionRateDecimal));
        });
        avgCpcOp.ifPresent(cpc -> {
            BigDecimal cpcDecimal = new BigDecimal(cpc);
            dataAvg.setCpcAvg(CalculateUtil.formatDecimal(cpcDecimal));
        });
        avgCpaOp.ifPresent(cpa -> {
            BigDecimal cpaDecimal = new BigDecimal(cpa);
            dataAvg.setCpaAvg(CalculateUtil.formatDecimal(cpaDecimal));
        });
        return dataAvg.build();
    }

    private List<DashboardAdTargetingMatrixDto> getMatrixList(DashboardAdQueryWordMatrixRequest req) {
        StopWatch sw = new StopWatch("get query word matrix start");
        List<DashboardAdTargetingMatrixDto> dtoList = Lists.newArrayList();
        int puid = req.getPuid();
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(req.getDataField());
        int limit = req.getLimit();
        DashboardOrderByEnum orderBy = DashboardOrderByEnum.orderByMap.get(Optional.of(req.getOrderBy()).map(String::toLowerCase).orElse("desc"));
        DashboardQueryWordTypeEnum queryWordType = DashboardQueryWordTypeEnum.queryWordTypeMap.get(req.getTargetType());

        List<DashboardAdQueryWordMatrixTopDto> matrixList = new ArrayList<>();
        Optional.ofNullable(getSpMatrixInfoList(req)).filter(CollectionUtils::isNotEmpty).ifPresent(matrixList::addAll);
        if (DashboardQueryWordTypeEnum.ASIN_AND_CATEGORY_TARGET_TYPE != queryWordType) {//sb不存在投放产生的搜索词ASIN
            Optional.ofNullable(getSbMatrixInfoList(req)).filter(CollectionUtils::isNotEmpty).ifPresent(matrixList::addAll);
        }
        sw.start("sort result");
        //需要对结果集进行排序，取前n，相当于内存排序
        //还需要对结果进行排序，取前limit行数据
        matrixList = keywordAndTargetingComparator(matrixList, dataField, limit, orderBy);

        if (CollectionUtils.isEmpty(matrixList)) {
            return dtoList;
        }
        sw.stop();
        //还需要区分sp,sb的keyword和targeting
        //需要根据queryId查询对应搜索词报告中的基本信息，包括query，keywordId,targetingId,shopId,marketplaceId,campaignId
        //,adGroupId, campaigName,AdGroupName,matchType, targetingText
        Map<String, DashboardAdQueryWordMatrixTopDto> spKeywordMap = new HashMap<>();
        Map<String, DashboardAdQueryWordMatrixTopDto> spTargetingMap = new HashMap<>();
        Map<String, DashboardAdQueryWordMatrixTopDto> sbKeywordMap = new HashMap<>();
        for (DashboardAdQueryWordMatrixTopDto dto : matrixList) {
            if ("keyword".equals(dto.getType())) {
                if (Constants.SP.equals(dto.getCampaignType())) {
                    spKeywordMap.put(dto.getQueryId(), dto);
                }
                if (Constants.SB.equals(dto.getCampaignType())) {
                    sbKeywordMap.put(dto.getQueryId(), dto);
                }
            }
            if ("targeting".equals(dto.getType())) {
                if (Constants.SP.equals(dto.getCampaignType())) {
                    spTargetingMap.put(dto.getQueryId(), dto);
                }
            }
        }

        Map<String, OdsCpcQueryKeywordReport> keywordReportInfoMap = new HashMap<>();
        Map<String, OdsCpcQueryTargetingReport> targetingReportInfoMap = new HashMap<>();
        Map<String, OdsCpcSbQueryKeywordReport> sbKeywordReportInfoMap = new HashMap<>();
        sw.start("query basic info from report");
        //还需要从query report表中获取基本属性
        if (CollectionUtils.isNotEmpty(spKeywordMap.keySet())) {
            List<OdsCpcQueryKeywordReport> keywordReportInfoList = odsCpcQueryKeywordReportDao.getByQueryIdList(puid, req.getShopIdList(), spKeywordMap.keySet());
            keywordReportInfoMap = keywordReportInfoList.parallelStream().collect(Collectors.toMap(OdsCpcQueryKeywordReport::getQueryId, v1 -> v1, (oldVal, newVal) -> newVal));
        }

        if (CollectionUtils.isNotEmpty(spTargetingMap.keySet())) {
            List<OdsCpcQueryTargetingReport> targetingReportInfoList = odsCpcQueryTargetingReportDao.getByQueryIdList(puid, req.getShopIdList(), spTargetingMap.keySet());
            targetingReportInfoMap = targetingReportInfoList.parallelStream().collect(Collectors.toMap(OdsCpcQueryTargetingReport::getQueryId, v1 -> v1, (oldVal, newVal) -> newVal));
        }

        if (CollectionUtils.isNotEmpty(sbKeywordMap.keySet())) {
            List<OdsCpcSbQueryKeywordReport> sbKeywordReportInfoList = odsCpcSbQueryKeywordReportDao.getByQueryIdList(puid, req.getShopIdList(), sbKeywordMap.keySet());
            sbKeywordReportInfoMap = sbKeywordReportInfoList.parallelStream().collect(Collectors.toMap(OdsCpcSbQueryKeywordReport::getQueryId, v1 -> v1, (oldVal, newVal) -> newVal));
        }
        sw.stop();

        sw.start("fill basic info ");
        for (DashboardAdQueryWordMatrixTopDto dto : matrixList) {
            if ("keyword".equals(dto.getType())) {
                if (Constants.SP.equals(dto.getCampaignType()) && Objects.nonNull(keywordReportInfoMap.get(dto.getQueryId()))) {
                    fillKeywordInfo(dto, keywordReportInfoMap.get(dto.getQueryId()));
                }
                if (Constants.SB.equals(dto.getCampaignType()) && Objects.nonNull(sbKeywordReportInfoMap.get(dto.getQueryId()))) {
                    fillSbKeywordInfo(dto, sbKeywordReportInfoMap.get(dto.getQueryId()));
                }
            }
            if ("targeting".equals(dto.getType()) && Objects.nonNull(targetingReportInfoMap.get(dto.getQueryId()))) {
                if (Constants.SP.equals(dto.getCampaignType())) {
                    fillTargetingInfo(dto, targetingReportInfoMap.get(dto.getQueryId()));
                }
            }
        }
        sw.stop();

        sw.start("query sp targeting ");
        Map<String, OdsAmazonAdTargeting> targetingInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(spTargetingMap.keySet())) {
            List<OdsAmazonAdTargeting> targetingInfoList = odsAmazonAdTargetingDao.getByTargetingIds(puid, null,
                    new ArrayList<>(spTargetingMap.keySet()));
            targetingInfoMap = targetingInfoList.parallelStream().collect(Collectors.toMap(OdsAmazonAdTargeting::getTargetId, v1 -> v1, (old, current) -> current));
        }
        sw.stop();
        Set<String> allCampaignIdSet = matrixList.parallelStream().map(DashboardAdQueryWordMatrixTopDto::getCampaignId).collect(Collectors.toSet());
        Set<String> allSpAdGroupIdSet = matrixList.parallelStream().filter(m -> Constants.SP.equals(m.getCampaignType())).map(DashboardAdQueryWordMatrixTopDto::getAdGroupId).collect(Collectors.toSet());
        Set<String> allSbAdGroupIdSet = matrixList.parallelStream().filter(m -> Constants.SB.equals(m.getCampaignType())).map(DashboardAdQueryWordMatrixTopDto::getAdGroupId).collect(Collectors.toSet());
        Set<Integer> allShopIdSet = matrixList.parallelStream().map(DashboardAdQueryWordMatrixTopDto::getShopId).collect(Collectors.toSet());

        sw.start("query all group and campaign info");
        List<AmazonAdCampaignAllBo> campaignNameList = new ArrayList<>();
        List<AmazonAdGroup> spGroupList = new ArrayList<>();
        List<AmazonSbAdGroup> sbGroupList = new ArrayList<>();
        List<ShopAuth> shopAuthList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allCampaignIdSet)) {
            campaignNameList = amazonAdCampaignAllDao.listByShopIdListAndCampaignIdList(puid, allShopIdSet, allCampaignIdSet);
        }
        if (CollectionUtils.isNotEmpty(allSpAdGroupIdSet)) {
            spGroupList = amazonAdGroupDao.listByGroupIdsAndShopIdList(puid, new ArrayList<>(allShopIdSet), new ArrayList<>(allSpAdGroupIdSet));
        }
        if (CollectionUtils.isNotEmpty(allSbAdGroupIdSet)) {
            sbGroupList = amazonSbAdGroupDao.getGroupByShopIdsAndGroupIds(puid, new ArrayList<>(allShopIdSet), new ArrayList<>(allSbAdGroupIdSet));
        }
        if (CollectionUtils.isNotEmpty(allShopIdSet)) {
            shopAuthList = shopAuthDao.listAllByIds(puid, new ArrayList<>(allShopIdSet));

        }
        Map<Integer, ShopAuth> shopInfoMap = shopAuthList.parallelStream().collect(Collectors.toMap(ShopAuth::getId, v1 -> v1, (oldVal, newVal) -> newVal));
        Map<String, AmazonAdCampaignAllBo> allCampaignInfoMap = campaignNameList.parallelStream().collect(Collectors.toMap(AmazonAdCampaignAllBo::getCampaignId, v1 -> v1, (oldVal, newVal) -> newVal));
        Map<String, AmazonAdGroup> allspGroupMap = spGroupList.parallelStream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, v1 -> v1, (oldVal, newVal) -> newVal));
        Map<String, AmazonSbAdGroup> allsbGroupMap = sbGroupList.parallelStream().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, v1 -> v1, (oldVal, newVal) -> newVal));
        sw.stop();

        //组装数据
        for (DashboardAdQueryWordMatrixTopDto dto : matrixList) {
            dtoList.add(convertDto(dto, shopInfoMap, targetingInfoMap, allCampaignInfoMap, allspGroupMap, allsbGroupMap));
        }
        log.info(sw.prettyPrint());
        return dtoList;
    }

    private void fillKeywordInfo(DashboardAdQueryWordMatrixTopDto dto, OdsCpcQueryKeywordReport info) {
        Optional.ofNullable(info.getQuery()).ifPresent(dto::setQuery);
        Optional.ofNullable(info.getShopId()).ifPresent(dto::setShopId);
        Optional.ofNullable(info.getMarketplaceId()).ifPresent(dto::setMarketplaceId);
        Optional.ofNullable(info.getCampaignId()).ifPresent(dto::setCampaignId);
        Optional.ofNullable(info.getAdGroupId()).ifPresent(dto::setAdGroupId);
        Optional.ofNullable(info.getMatchType()).ifPresent(dto::setMatchType);
        Optional.ofNullable(info.getKeywordId()).ifPresent(dto::setKeywordId);
    }

    private void fillTargetingInfo(DashboardAdQueryWordMatrixTopDto dto, OdsCpcQueryTargetingReport info) {
        Optional.ofNullable(info.getQuery()).ifPresent(dto::setQuery);
        Optional.ofNullable(info.getShopId()).ifPresent(dto::setShopId);
        Optional.ofNullable(info.getMarketplaceId()).ifPresent(dto::setMarketplaceId);
        Optional.ofNullable(info.getCampaignId()).ifPresent(dto::setCampaignId);
        Optional.ofNullable(info.getAdGroupId()).ifPresent(dto::setAdGroupId);
        Optional.ofNullable(info.getMatchType()).ifPresent(dto::setMatchType);
        Optional.ofNullable(info.getTargetId()).ifPresent(dto::setTargetingId);
    }

    private void fillSbKeywordInfo(DashboardAdQueryWordMatrixTopDto dto, OdsCpcSbQueryKeywordReport info) {
        Optional.ofNullable(info.getQuery()).ifPresent(dto::setQuery);
        Optional.ofNullable(info.getShopId()).ifPresent(dto::setShopId);
        Optional.ofNullable(info.getMarketplaceId()).ifPresent(dto::setMarketplaceId);
        Optional.ofNullable(info.getCampaignId()).ifPresent(dto::setCampaignId);
        Optional.ofNullable(info.getAdGroupId()).ifPresent(dto::setAdGroupId);
        Optional.ofNullable(info.getMatchType()).ifPresent(dto::setMatchType);
        Optional.ofNullable(info.getKeywordId()).ifPresent(dto::setKeywordId);
    }

    private List<DashboardAdQueryWordMatrixTopDto> getSpMatrixInfoList(DashboardAdQueryWordMatrixRequest req) {
        List<String> portfolioIds = req.getPortfolioIdsList().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> campaignIds = req.getCampaignIdsList().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(req.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(req.getMarketplaceIdList());
        }
        int puid = req.getPuid();
        List<Integer> shopList = req.getShopIdList();
        List<String> marketplaceIdList = req.getMarketplaceIdList();
        String currency = req.getCurrency();
        String startDate = Optional.of(req.getStartDate()).map(DateUtil::toFormatDate).orElse("");
        String endDate = Optional.of(req.getEndDate()).map(DateUtil::toFormatDate).orElse("");
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(req.getDataField());
        Integer limit = req.getLimit();
        DashboardOrderByEnum orderBy = DashboardOrderByEnum.orderByMap.get(req.getOrderBy());
        DashboardQueryWordTypeEnum queryWordType = DashboardQueryWordTypeEnum.queryWordTypeMap.get(req.getTargetType());
        List<DashboardAdQueryWordMatrixTopDto> keywordMatrixList = new ArrayList<>();
        List<DashboardAdQueryWordMatrixTopDto> targetingMatrixList = new ArrayList<>();
        StopWatch sw = new StopWatch("query keyword/targeting query word by target id start");
        sw.start();
        //分别查询keyword和targeting的前n排名数据
        if (Objects.isNull(queryWordType) || DashboardQueryWordTypeEnum.KEYWORD_TARGET_TYPE == queryWordType) {
            keywordMatrixList = odsCpcQueryKeywordReportDao.queryMatrixInfo(puid, marketplaceIdList, shopList, currency, startDate, endDate,
                    dataField, Optional.of(req.getTargetId()).filter(StringUtils::isNotEmpty)
                            .map(Collections::singletonList).orElse(null), limit, orderBy, queryWordType, siteToday, req.getSiteToday(), portfolioIds, campaignIds, req.getNoZero());
        }
        if (Objects.isNull(queryWordType) || DashboardQueryWordTypeEnum.ASIN_AND_CATEGORY_TARGET_TYPE == queryWordType) {
            targetingMatrixList = odsCpcQueryTargetingReportDao.queryMatrixInfo(puid, marketplaceIdList, shopList, currency, startDate, endDate,
                    dataField, Optional.of(req.getTargetId()).filter(StringUtils::isNotEmpty)
                            .map(Collections::singletonList).orElse(null), limit, orderBy, queryWordType, siteToday, req.getSiteToday(), portfolioIds, campaignIds, req.getNoZero());
        }
        sw.stop();
        log.info(sw.prettyPrint());
        if (Objects.nonNull(keywordMatrixList) && CollectionUtils.isNotEmpty(targetingMatrixList)) {
            keywordMatrixList.addAll(targetingMatrixList);
        }
        return keywordMatrixList;
    }

    private List<DashboardAdQueryWordMatrixTopDto> getSbMatrixInfoList(DashboardAdQueryWordMatrixRequest req) {
        List<String> portfolioIds = req.getPortfolioIdsList().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> campaignIds = req.getCampaignIdsList().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(req.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(req.getMarketplaceIdList());
        }
        int puid = req.getPuid();
        List<Integer> shopList = req.getShopIdList();
        List<String> marketplaceIdList = req.getMarketplaceIdList();
        String currency = req.getCurrency();
        String startDate = Optional.of(req.getStartDate()).map(DateUtil::toFormatDate).orElse("");
        String endDate = Optional.of(req.getEndDate()).map(DateUtil::toFormatDate).orElse("");
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(req.getDataField());
        Integer limit = req.getLimit();
        DashboardOrderByEnum orderBy = DashboardOrderByEnum.orderByMap.get(req.getOrderBy());
        //分别查询keyword和targeting的前n排名数据
        return odsCpcSbQueryKeywordReportDao.queryMatrixInfo(puid, marketplaceIdList, shopList, currency, startDate, endDate,
                dataField, Optional.of(req.getTargetId()).filter(StringUtils::isNotEmpty)
                        .map(Collections::singletonList).orElse(null), limit, orderBy, siteToday, req.getSiteToday(), portfolioIds, campaignIds, req.getNoZero());
    }

    private DashboardAdTargetingMatrixDto convertDto(DashboardAdQueryWordMatrixTopDto dto, Map<Integer, ShopAuth> shopInfoMap,
                                                     Map<String, OdsAmazonAdTargeting> spTargetingInfoMap, Map<String, AmazonAdCampaignAllBo> allCampaignNameMap,
                                                     Map<String, AmazonAdGroup> allSpGroupInfoMap, Map<String, AmazonSbAdGroup> allsbGroupInfoMap) {
        DashboardAdTargetingMatrixDto result = new DashboardAdTargetingMatrixDto();
        BeanUtils.copyProperties(dto, result, ParamCopyUtil.checkPropertiesNullOrEmptySuper(dto));
        Optional.ofNullable(dto.getCost()).ifPresent(result::setCost);
        Optional.ofNullable(dto.getTotalSales()).ifPresent(result::setTotalSales);
        Optional.ofNullable(dto.getImpressions()).ifPresent(result::setImpressions);
        Optional.ofNullable(dto.getClicks()).ifPresent(result::setClicks);
        Optional.ofNullable(dto.getOrderNum()).ifPresent(result::setOrderNum);
        Optional.ofNullable(dto.getSaleNum()).ifPresent(result::setSaleNum);
        Optional.ofNullable(dto.getShopId()).ifPresent(result::setShopId);
        Optional.ofNullable(allCampaignNameMap.get(dto.getCampaignId())).map(AmazonAdCampaignAllBo::getName).ifPresent(result::setCampaignName);
        if (Constants.SP.equals(dto.getCampaignType())) {
            Optional.ofNullable(allSpGroupInfoMap.get(dto.getAdGroupId())).map(AmazonAdGroup::getName).ifPresent(result::setAdGroupName);
            if ("targeting".equals(dto.getType()) && "asin".equals(dto.getMatchType())) {
                dto.setMatchType("asinSameAs");
            }
        }
        if (Constants.SB.equals(dto.getCampaignType())) {
            Optional.ofNullable(allsbGroupInfoMap.get(dto.getAdGroupId())).map(AmazonSbAdGroup::getName).ifPresent(result::setAdGroupName);
        }
        if (StringUtils.isNotBlank(dto.getTargetingId()) && Objects.nonNull(spTargetingInfoMap.get(dto.getTargetingId()))) {
            OdsAmazonAdTargeting spTargetingInfo = spTargetingInfoMap.get(dto.getTargetingId());
            TargetingInfoUtil.getExpressionInfo(spTargetingInfo.getType(), spTargetingInfo.getResolvedExpression(), result);
        }
        if (Objects.nonNull(DashboardQueryWordMatchTypeEnum.dataMap.get(dto.getMatchType()))) {
            result.setMatchType(DashboardQueryWordMatchTypeEnum.dataMap.get(dto.getMatchType()).getMsg());
        } else {
            result.setMatchType(dto.getMatchType());
        }
        //需要通过matchType来获取对应的搜索词报告的投放类型
        Optional.ofNullable(DashboardQueryWordMatchTypeEnum.dataMap.get(dto.getMatchType()))
                .map(DashboardQueryWordMatchTypeEnum::getTargetType)
                .ifPresent(result::setExpressionType);
        result.setTargetText(dto.getQuery());
        result.setCampaignType(StringUtils.upperCase(dto.getCampaignType()));
        Optional.ofNullable(shopInfoMap.get(dto.getShopId())).map(ShopAuth::getName).ifPresent(result::setShopName);
        return result;
    }

    private List<DashboardAdQueryWordMatrixTopDto> keywordAndTargetingComparator(List<DashboardAdQueryWordMatrixTopDto> matrixList, DashboardDataFieldEnum dataField,
                                                                                 int limit, DashboardOrderByEnum orderBy) {
        Class<DashboardAdQueryWordMatrixTopDto> cla = DashboardAdQueryWordMatrixTopDto.class;
        Field[] fields = getAllField(cla);
        Map<String, Field> fieldMap = Arrays.stream(fields).peek(f -> f.setAccessible(true))
                .filter(e-> !e.isSynthetic()).collect(Collectors.toMap(Field::getName, v1 -> v1, (v1, v2)-> v1));
        StringBuilder compareKey = new StringBuilder(dataField.getCode());
        matrixList = matrixList.stream().sorted((o1, o2) -> {
            try {
                int result = 0;
                Field compareField = fieldMap.get(compareKey.toString());
                if (Objects.nonNull(compareField)) {
                    Object val1 = compareField.get(o1);
                    Object val2 = compareField.get(o2);
                    if(Objects.isNull(val1) && Objects.isNull(val2) ){
                        return 0;
                    }
                    if(Objects.nonNull(val1) && Objects.isNull(val2) ){
                        return 1;
                    }
                    if(Objects.isNull(val1)){
                        return -1;
                    }
                    if(String.class.isAssignableFrom(compareField.getType()) && StringUtils.isNotEmpty(val1.toString())
                            && StringUtils.isNotEmpty(val2.toString())){
                        String rateValue1 = val1.toString();
                        String rateValue2 = val2.toString();
                        result = new BigDecimal(rateValue1).compareTo(new BigDecimal(rateValue2));
                    }
                    if(Integer.class.isAssignableFrom(compareField.getType())){
                        Integer compareVal1 = (Integer) val1;
                        Integer compareVal2 = (Integer) val2;
                        result = compareVal1.compareTo(compareVal2);
                    }
                    if(Long.class.isAssignableFrom(compareField.getType())){
                        Long compareVal1 = (Long) val1;
                        Long compareVal2 = (Long) val2;
                        result = compareVal1.compareTo(compareVal2);
                    }
                    if(BigDecimal.class.isAssignableFrom(compareField.getType())){
                        BigDecimal compareVal1 = (BigDecimal) val1;
                        BigDecimal compareVal2 = (BigDecimal) val2;
                        result = compareVal1.compareTo(compareVal2);
                    }
                }
//                if (result == 0) {
//                    if (Objects.nonNull(o1.getKeywordId()) && Objects.nonNull(o2.getKeywordId()) )
//                        return o1.getKeywordId().compareTo(o2.getKeywordId());
//                    if (Objects.nonNull(o1.getTargetingId()) && Objects.nonNull(o2.getTargetingId()) )
//                        return o1.getTargetingId().compareTo(o2.getTargetingId());
//                }else{
                    return result;
//                }
            } catch (IllegalAccessException e) {
                log.error("compare keyword and targeting list data error", e);
            }
            return 0;
        }).collect(Collectors.toList());
        int subLimit = Math.min(limit, matrixList.size());

        //按请求字段进行升降序
        if (DashboardOrderByEnum.DESC == orderBy) {
            Collections.reverse(matrixList);
        }
        matrixList = matrixList.subList(0, subLimit);
        return matrixList;
    }

    private String keywordAndTargetingTypeHandler(String manualType, String matchType) {
        if (Constants.CATAGORY.equals(manualType)) {
            return Constants.CATAGORY;
        } else {
            try {
                List<TargetingExpressionDTO> dtoList = JSONObject.parseArray(matchType, TargetingExpressionDTO.class);
                return Optional.ofNullable(dtoList)
                        .filter(CollectionUtils::isNotEmpty)
                        .map(l -> l.get(0))
                        .map(TargetingExpressionDTO::getType)
                        .map(KeywordAndTargetingExcludeNeEnum::getKeywordAndTargetingExcludeNeEnumByCode)
                        .map(KeywordAndTargetingExcludeNeEnum::getMsg).orElse("");
            } catch (Exception e) {
                log.error("format error ", e);
            }
        }
        return "";
    }

    private <T> Field[] getAllField(Class<T> cla) {
        Class clazz = cla;
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null){
            fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            clazz = clazz.getSuperclass();
        }
        Field[] fields = new Field[fieldList.size()];
        fieldList.toArray(fields);
        return fields;
    }
}
