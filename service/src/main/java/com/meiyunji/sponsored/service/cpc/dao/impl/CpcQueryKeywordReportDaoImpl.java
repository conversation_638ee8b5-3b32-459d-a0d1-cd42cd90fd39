package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadras.types.enumeration.AddNegativeTargetType;
import com.meiyunji.sellfox.aadras.types.enumeration.RuleIndexType;
import com.meiyunji.sellfox.aadras.types.enumeration.RuleOperatorType;
import com.meiyunji.sellfox.aadras.types.enumeration.RuleStatisticalModeType;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.enums.AmazonAd;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingSphereDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.autoRule.vo.AdQueryKeywordAutoRuleParam;
import com.meiyunji.sponsored.service.autoRule.vo.AutoRuleObjectParam;
import com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.ICpcQueryKeywordReportDao;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.enums.MatchValueEnum;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordBo;
import com.meiyunji.sponsored.service.wordFrequency.enums.WordRoot;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.regex.Pattern;

import static com.meiyunji.sponsored.service.cpc.util.Constants.ASIN_REGEX;


/**
 * CpcQueryKeywordReport
 *
 * <AUTHOR>
 * 顾客反馈order_num数据不准,排查发现应该使用sale_num字段作为订单量值,以下把order_num值设为sale_num的值,但字段名不变
 */
@Repository
public class CpcQueryKeywordReportDaoImpl extends BaseShardingSphereDaoImpl<CpcQueryKeywordReport> implements ICpcQueryKeywordReportDao {

    private final int LIMIT = 2000;

    @Autowired
    private DynamicRefreshNacosConfiguration nacosConfiguration;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Override
    public void insertList(Integer puid, List<CpcQueryKeywordReport> list) {
        //插入原表
        insertListOriginAndHotTable(puid, list, getJdbcHelper().getTable());
        if (nacosConfiguration.isHotTableWritePhase2Enable()) {
            //插入热表
            insertListOriginAndHotTable(puid, list, getHotTableName());
        }
    }

    private void insertListOriginAndHotTable(Integer puid, List<CpcQueryKeywordReport> list, String tableName) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(tableName);
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`campaign_id`,`ad_group_id`,")
                .append("`keyword_id`,`count_date`,`keyword_text`,`match_type`,`ad_group_name`,`campaign_name`,`query`,`cost`,`cost_rmb`,")
                .append("`cost_usd`,`total_sales`,`total_sales_rmb`,`total_sales_usd`,`ad_sales`,`ad_sales_rmb`,`ad_sales_usd`, ")
                .append("`impressions`,`clicks`,`order_num`,`ad_order_num`,`sale_num`,`ad_sale_num`,`query_id`, `create_time`,`update_time` )VALUES");
        List<Object> argsList = Lists.newArrayList();
        for (CpcQueryKeywordReport report : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getCampaignId());
            argsList.add(report.getAdGroupId());
            argsList.add(report.getKeywordId());
            argsList.add(report.getCountDate());
            argsList.add(report.getKeywordText());
            argsList.add(report.getMatchType());
            argsList.add(report.getAdGroupName());
            argsList.add(report.getCampaignName());
            argsList.add(report.getQuery());
            argsList.add(report.getCost());
            argsList.add(report.getCostRmb());
            argsList.add(report.getCostUsd());
            argsList.add(report.getTotalSales());
            argsList.add(report.getTotalSalesRmb());
            argsList.add(report.getTotalSalesUsd());
            argsList.add(report.getAdSales());
            argsList.add(report.getAdSalesRmb());
            argsList.add(report.getAdSalesUsd());
            argsList.add(report.getImpressions());
            argsList.add(report.getClicks());
            argsList.add(report.getOrderNum());
            argsList.add(report.getAdOrderNum());
            argsList.add(report.getSaleNum());
            argsList.add(report.getAdSaleNum());
            argsList.add(report.getQueryId());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `ad_group_name`=values(ad_group_name),`campaign_name`=values(campaign_name),")
                .append("`cost`=values(cost),cost_rmb=values(cost_rmb),cost_usd=values(cost_usd),`total_sales`=values(total_sales),`total_sales_rmb`=values(total_sales_rmb),`total_sales_usd`=values(total_sales_usd),")
                .append("`ad_sales`=values(ad_sales),`ad_sales_rmb`=values(ad_sales_rmb),`ad_sales_usd`=values(ad_sales_usd),`impressions`=values(impressions),`clicks`=values(clicks),")
                .append("`order_num`=values(order_num),`ad_order_num`=values(ad_order_num),`sale_num`=values(sale_num),`ad_sale_num`=values(ad_sale_num),`query_id`=values(query_id)");

        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public Page pageList(Integer puid, CpcQueryWordDto dto, Page page) {
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectSql = new StringBuilder("SELECT `query`,`query_cn`,keyword_id,keyword_text,match_type,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) as total_sales FROM ");
        selectSql.append(getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD))).append(" ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByKeyword(puid, dto, argsList);
        selectSql.append(whereSql);

        StringBuilder sql = new StringBuilder("");
        sql.append("select t.query,t.query_cn,t.keyword_id,t.keyword_text,");
        sql.append("t.match_type,t.ad_group_id,t.ad_group_name,t.campaign_id,t.campaign_name,");
        sql.append("t.impressions,t.clicks,t.cost,t.sale_num,t.ad_order_num,t.total_sales from ( ");
        sql.append(selectSql);
        sql.append(") t left join t_amazon_ad_keyword w on t.keyword_id = w.keyword_id ");
        sql.append(" where w.shop_id=? ");
        argsList.add(dto.getShopId());

        if (StringUtils.isNotBlank(dto.getState())) {
            sql.append(" and w.state = ? ");
            argsList.add(dto.getState());
        }

        if (StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())) {
            String orderField = ReportService.getOrderField(dto.getOrderField(), false);
            if (StringUtils.isNotBlank(orderField)) {
                sql.append(" order by ").append(orderField);
                if ("desc".equals(dto.getOrderValue())) {
                    sql.append(" desc");
                }
                sql.append(" , query desc ");
            }
        }

        countSql.append(sql).append(") c");

        Object[] args = argsList.toArray();

        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, sql.toString(), args, CpcQueryKeywordReport.class);
    }


    /**
     * TODO 相同sql 待优化
     */
    @Override
    public int countPageManageList(Integer puid, CpcQueryWordDto dto) {
        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));
        // 不以热表结尾开启多线程查询
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectKeywordSql = new StringBuilder("SELECT `marketplace_id`, `query`,`query_cn`,'keyword' as type, keyword_id, '' as target_id,")
                .append(" keyword_text,match_type,'' as targeting_expression,'' as targeting_type,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ");
        selectKeywordSql.append(tableName).append(" ");
        List<Object> argList = new ArrayList<>();
        String whereSql = getWhereSqlCountByKeyword(puid, dto, argList);
        selectKeywordSql.append(whereSql);
        String unionTableName = getTableNameByStartDateAndTableName(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD), "t_cpc_query_targeting_report");
        StringBuilder selectTargetSql = new StringBuilder("SELECT `marketplace_id`, `query`, `query_cn`,'target' as type,'' as keyword_id,  target_id, ")
                .append(" '' as keyword_text,'' as match_type,targeting_expression,targeting_type,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ");
        selectTargetSql.append(unionTableName).append(" ");
        String whereSqlTarget = getWhereSqlCountByTargetId(puid, dto, argList);
        selectTargetSql.append(whereSqlTarget);
        countSql.append(selectKeywordSql);
        countSql.append(" UNION ALL ");
        countSql.append(selectTargetSql);
        countSql.append(" ) p ");
        countSql.append(" ");
        return countPageResult(puid, countSql.toString(), argList.toArray());
    }

    /**
     * TODO 相同sql 待优化
     */
    @Override
    public Page pageThreadManageList(Integer puid, CpcQueryWordDto dto, Page page) {
        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));
        // 不以热表结尾开启多线程查询
        StringBuilder sql = new StringBuilder("SELECT `marketplace_id`,`query`,`query_cn`,type, keyword_id,target_id,")
                .append(" keyword_text,match_type,targeting_expression,targeting_type,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("impressions,clicks,cost,sale_num,ad_order_num,")
                .append("total_sales,`ad_sales`,`ad_sale_num`, ")
                .append("order_num FROM ( ");
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectKeywordSql = new StringBuilder("SELECT `marketplace_id`, `query`,`query_cn`,'keyword' as type, keyword_id, '' as target_id,")
                .append(" keyword_text,match_type,'' as targeting_expression,'' as targeting_type,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ");
        selectKeywordSql.append(tableName).append(" ");
        List<Object> argList = new ArrayList<>();
        String whereSql = getWhereSqlCountByKeyword(puid, dto, argList);
        selectKeywordSql.append(whereSql);
        String unionTableName = getTableNameByStartDateAndTableName(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD), "t_cpc_query_targeting_report");
        StringBuilder selectTargetSql = new StringBuilder("SELECT `marketplace_id`, `query`, `query_cn`,'target' as type,'' as keyword_id,  target_id, ")
                .append(" '' as keyword_text,'' as match_type,targeting_expression,targeting_type,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ");
        selectTargetSql.append(unionTableName).append(" ");
        String whereSqlTarget = getWhereSqlCountByTargetId(puid, dto, argList);
        selectTargetSql.append(whereSqlTarget);
        sql.append(selectKeywordSql);
        sql.append(" UNION ALL ");
        sql.append(selectTargetSql);
        sql.append(" ) p ");

        if (StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())) {
            String orderField = ReportService.getOrderField(dto.getOrderField(), false);
            if (StringUtils.isNotBlank(orderField)) {
                sql.append(" order by ").append(orderField);
                if ("desc".equals(dto.getOrderValue())) {
                    sql.append(" desc");
                }
                sql.append(" , query desc ");
            }
        }
        return getWebPageResult(puid, page.getPageNo(), page.getPageSize(), sql.toString(), argList.toArray(), new RowMapper<com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo>() {
            @Override
            public com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo mapRow(ResultSet res, int i) throws SQLException {
                com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo optionVo = com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo.builder()
                        .marketplaceId(res.getString("marketplace_id"))
                        .query(res.getString("query"))
                        .queryCn(res.getString("query_cn"))
                        .type(res.getString("type"))
                        .keywordId(res.getString("keyword_id"))
                        .targetId(res.getString("target_id"))
                        .keywordText(res.getString("keyword_text"))
                        .matchType(res.getString("match_type"))
                        .targetingExpression(res.getString("targeting_expression"))
                        .targetingType(res.getString("targeting_type"))
                        .adGroupId(res.getString("ad_group_id"))
                        .adGroupName(res.getString("ad_group_name"))
                        .campaignId(res.getString("campaign_id"))
                        .campaignName(res.getString("campaign_name"))
                        .impressions(res.getInt("impressions"))
                        .clicks(res.getInt("clicks"))
                        .cost(res.getBigDecimal("cost") != null ? res.getBigDecimal("cost") : BigDecimal.ZERO)
                        .saleNum(res.getInt("sale_num"))
                        /**
                         * TODO 广告报告重构
                         * 本广告产品销量
                         */
                        .adOrderNum(res.getInt("ad_order_num"))
                        //广告销售额
                        .totalSales(res.getBigDecimal("total_sales") != null ? res.getBigDecimal("total_sales") : BigDecimal.ZERO)
                        //本广告产品订单量
                        .adSaleNum(Optional.ofNullable(res.getInt("ad_sale_num")).orElse(0))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(res.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        //广告销量
                        .salesNum(Optional.ofNullable(res.getInt("order_num")).orElse(0))
                        .build();
                return optionVo;
            }
        });
    }

    @Override
    public Page pageManageList(Integer puid, CpcQueryWordDto dto, Page page) {
        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));
        // 不以热表结尾开启多线程查询
        StringBuilder sql = new StringBuilder("SELECT `marketplace_id`,`query`,`query_cn`,type, keyword_id,target_id,")
                .append(" keyword_text,match_type,targeting_expression,targeting_type,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("impressions,clicks,cost,sale_num,ad_order_num,")
                .append("total_sales,`ad_sales`,`ad_sale_num`, ")
                .append("order_num FROM ( ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");

        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectKeywordSql = new StringBuilder("SELECT `marketplace_id`, `query`,`query_cn`,'keyword' as type, keyword_id, '' as target_id,")
                .append(" keyword_text,match_type,'' as targeting_expression,'' as targeting_type,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ");
        selectKeywordSql.append(tableName).append(" ");
        List<Object> argList = new ArrayList<>();
        String whereSql = getWhereSqlCountByKeyword(puid, dto, argList);
        selectKeywordSql.append(whereSql);

        String unionTableName = getTableNameByStartDateAndTableName(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD), "t_cpc_query_targeting_report");
        StringBuilder selectTargetSql = new StringBuilder("SELECT `marketplace_id`, `query`, `query_cn`,'target' as type,'' as keyword_id,  target_id, ")
                .append(" '' as keyword_text,'' as match_type,targeting_expression,targeting_type,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ");
        selectTargetSql.append(unionTableName).append(" ");

        String whereSqlTarget = getWhereSqlCountByTargetId(puid, dto, argList);
        selectTargetSql.append(whereSqlTarget);


        sql.append(selectKeywordSql);
        sql.append(" UNION ALL ");
        sql.append(selectTargetSql);
        sql.append(" ) p ");

        if (StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())) {
            String orderField = ReportService.getOrderField(dto.getOrderField(), false);
            if (StringUtils.isNotBlank(orderField)) {
                sql.append(" order by ").append(orderField);
                if ("desc".equals(dto.getOrderValue())) {
                    sql.append(" desc");
                }
                sql.append(" , query desc ");
            }
        }

        countSql.append(selectKeywordSql);
        countSql.append(" UNION ALL ");
        countSql.append(selectTargetSql);
        countSql.append(" ) p ");
        //排除asin格式搜素词
        countSql.append(" ");
        return getPageByMapper(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), argList.toArray(), sql.toString(), argList.toArray(), new RowMapper<com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo>() {
            @Override
            public com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo mapRow(ResultSet res, int i) throws SQLException {
                com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo optionVo = com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo.builder()
                        .marketplaceId(res.getString("marketplace_id"))
                        .query(res.getString("query"))
                        .queryCn(res.getString("query_cn"))
                        .type(res.getString("type"))
                        .keywordId(res.getString("keyword_id"))
                        .targetId(res.getString("target_id"))
                        .keywordText(res.getString("keyword_text"))
                        .matchType(res.getString("match_type"))
                        .targetingExpression(res.getString("targeting_expression"))
                        .targetingType(res.getString("targeting_type"))
                        .adGroupId(res.getString("ad_group_id"))
                        .adGroupName(res.getString("ad_group_name"))
                        .campaignId(res.getString("campaign_id"))
                        .campaignName(res.getString("campaign_name"))
                        .impressions(res.getInt("impressions"))
                        .clicks(res.getInt("clicks"))
                        .cost(res.getBigDecimal("cost") != null ? res.getBigDecimal("cost") : BigDecimal.ZERO)
                        .saleNum(res.getInt("sale_num"))
                        /**
                         * TODO 广告报告重构
                         * 本广告产品销量
                         */
                        .adOrderNum(res.getInt("ad_order_num"))
                        //广告销售额
                        .totalSales(res.getBigDecimal("total_sales") != null ? res.getBigDecimal("total_sales") : BigDecimal.ZERO)
                        //本广告产品订单量
                        .adSaleNum(Optional.ofNullable(res.getInt("ad_sale_num")).orElse(0))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(res.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        //广告销量
                        .salesNum(Optional.ofNullable(res.getInt("order_num")).orElse(0))
                        .build();
                return optionVo;
            }
        });
    }



    /**
     * 判断条件字段 来判断是否需要查询
     * @param dto
     * @return
     */
    private List<String> getConditionField(CpcQueryWordDto dto) {
        Set<String> fieldSet = new HashSet<>();
        //展示量
        if (dto.getImpressionsMin() != null || dto.getImpressionsMax() != null) {
            fieldSet.add("impressions");
        }
        //点击量
        if (dto.getClicksMin() != null || dto.getClicksMax() != null) {
            fieldSet.add("clicks");
        }
        //点击率（clicks/impressions）
        if (dto.getClickRateMin() != null || dto.getClickRateMax() != null) {
            fieldSet.add("impressions");
            fieldSet.add("clicks");
        }
        //cpc  平均点击费用
        if (dto.getCpcMin() != null || dto.getCpcMax() != null) {

            fieldSet.add("clicks");
        }
        //订单转化率
        if (dto.getSalesConversionRateMin() != null || dto.getSalesConversionRateMax() != null) {
            fieldSet.add("clicks");
        }
        //本广告产品订单量
        if (dto.getAdSaleNumMin() != null || dto.getAdSaleNumMax() != null) {
            fieldSet.add("ad_sale_num");
        }
        //其他产品广告订单量
        if (dto.getAdOtherOrderNumMin() != null || dto.getAdOtherOrderNumMax() != null) {
            fieldSet.add("ad_sale_num");
        }
        //本广告产品销售额
        if (dto.getAdSalesMin() != null || dto.getAdSalesMax() != null) {
            fieldSet.add("ad_sales");
        }
        //其他产品广告销售额
        if (dto.getAdOtherSalesMin() != null || dto.getAdOtherSalesMax() != null) {
            fieldSet.add("ad_sales");
        }
        //本广告产品销量
        if (dto.getAdSelfSaleNumMin() != null || dto.getAdSelfSaleNumMax() != null) {
            fieldSet.add("ad_order_num");
        }
        //其他产品广告销量
        if (dto.getAdOtherSaleNumMin() != null || dto.getAdOtherSaleNumMax() != null) {
            fieldSet.add("ad_order_num");
        }
        return new ArrayList<>(fieldSet);
    }

    @Override
    public AdMetricDto getSumAdMetricDto(Integer puid, CpcQueryWordDto dto) {
        StringBuilder sql = new StringBuilder("SELECT SUM(cost) cost,SUM(sale_num) sale_num,SUM(total_sales) total_sales,SUM(order_num) order_num FROM ( ");

        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));

        List<String> list = getConditionField(dto);
        String fieldName = null;
        if (CollectionUtils.isNotEmpty(list)) {
            StringBuilder fieldBuilder = new StringBuilder();
            for (String field : list) {
                fieldBuilder.append(" , SUM(").append(field).append(") ").append(field).append(" ");
            }
            fieldName = fieldBuilder.toString();
        }
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectKeywordSql = new StringBuilder("SELECT SUM(cost) cost,SUM(sale_num) sale_num, SUM(total_sales) total_sales, SUM(order_num) order_num ");
        if (fieldName != null){
            selectKeywordSql.append(fieldName);
        }
        selectKeywordSql.append(" FROM ");
        selectKeywordSql.append(tableName).append(" ");
        List<Object> argList = new ArrayList<>();
        String whereSql = getWhereSqlCountByKeyword(puid,dto,argList);
        selectKeywordSql.append(whereSql);

        String unionTableName = getTableNameByStartDateAndTableName(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD), "t_cpc_query_targeting_report");
        StringBuilder selectTargetSql = new StringBuilder("SELECT SUM(cost) cost,SUM(sale_num) sale_num, SUM(total_sales) total_sales, SUM(order_num) order_num ");
        if (fieldName != null){
            selectTargetSql.append(fieldName);
        }
        selectTargetSql.append(" FROM ");
        selectTargetSql.append(unionTableName).append(" ");

        String whereSqlTarget = getWhereSqlCountByTargetId(puid,dto,argList);
        selectTargetSql.append(whereSqlTarget);

        sql.append(selectKeywordSql);
        sql.append(" UNION ALL ");
        sql.append(selectTargetSql);
        sql.append(" ) p");

        HintManager hintManager = HintManager.getInstance();
        try {
            List<AdMetricDto> adMetricDtoList = getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdMetricDto>() {
                @Override
                public AdMetricDto mapRow(ResultSet re, int i) throws SQLException {
                    AdMetricDto dto = AdMetricDto.builder()
                            .sumCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .sumAdSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("sale_num")).orElse(BigDecimal.ZERO))
                            .sumOrderNum(Optional.ofNullable(re.getBigDecimal("order_num")).orElse(BigDecimal.ZERO))
                            .build();
                    return dto;
                }
            }, argList.toArray());
            return adMetricDtoList.size() > 0 ? adMetricDtoList.get(0) : null;
        } finally {
            hintManager.close();
        }

    }

    @Override
    public Page pageManageListExport(Integer puid, SearchVo searchVo, Page page) {
        StringBuilder sql = new StringBuilder("SELECT `puid`,`shop_id`,`marketplace_id`,`count_date`,`query`,type, keyword_id,target_id,")
                .append(" keyword_text,match_type,targeting_expression,targeting_type,targeting_text,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("impressions,clicks,cost,sale_num,ad_order_num,")
                .append("total_sales,`ad_sales`,`ad_sale_num`, ")
                .append("order_num from ( ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");

        String tableName = getTableNameByStartDate(searchVo.getStart());
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectKeywordSql = new StringBuilder("SELECT `puid`,`shop_id`,`marketplace_id`,`count_date`,`query`,'keyword' as type, keyword_id, '' as target_id,")
                .append(" keyword_text,match_type,'' as targeting_expression,'' as targeting_type,'' as targeting_text,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ");
        selectKeywordSql.append(tableName).append(" ");
        List<Object> argList = new ArrayList<>();
        String whereSql = getWhereSqlCountByKeyword(puid, searchVo, argList);
        selectKeywordSql.append(whereSql);

        String unionTableName = getTableNameByStartDateAndTableName(searchVo.getStart(), "t_cpc_query_targeting_report");
        StringBuilder selectTargetSql = new StringBuilder("SELECT `puid`,`shop_id`,`marketplace_id`,`count_date`,`query`,'target' as type,'' as keyword_id,  target_id, ")
                .append(" '' as keyword_text,'' as match_type,targeting_expression,targeting_type,targeting_text,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ");
        selectTargetSql.append(unionTableName).append(" ");
        String whereSqlTarget = getWhereSqlCountByTargetId(puid, searchVo, argList);
        selectTargetSql.append(whereSqlTarget);


        sql.append(selectKeywordSql);
        sql.append(" UNION ALL ");
        sql.append(selectTargetSql);
        sql.append(" ) p");

        countSql.append(selectKeywordSql);
        countSql.append(" UNION ALL ");
        countSql.append(selectTargetSql);
        countSql.append(" ) p");

        return getPageByMapper(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), argList.toArray(), sql.toString(), argList.toArray(), new RowMapper<com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo>() {
            @Override
            public com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo mapRow(ResultSet res, int i) throws SQLException {
                com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo optionVo = com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo.builder()
                        .query(res.getString("query"))
                        .type(res.getString("type"))
                        .keywordId(res.getString("keyword_id"))
                        .targetId(res.getString("target_id"))
                        .keywordText(res.getString("keyword_text"))
                        .matchType(res.getString("match_type"))
                        .targetingExpression(res.getString("targeting_expression"))
                        .targetingType(res.getString("targeting_type"))
                        .adGroupId(res.getString("ad_group_id"))
                        .adGroupName(res.getString("ad_group_name"))
                        .campaignId(res.getString("campaign_id"))
                        .campaignName(res.getString("campaign_name"))
                        .impressions(res.getInt("impressions"))
                        .clicks(res.getInt("clicks"))
                        .cost(res.getBigDecimal("cost") != null ? res.getBigDecimal("cost") : BigDecimal.ZERO)
                        /**
                         * TODO 广告报告下载中心
                         * 本广告产品销量
                         */
                        .puid(res.getInt("puid"))
                        .orderNum(Optional.ofNullable(res.getInt("order_num")).orElse(0))
                        .shopId(res.getInt("shop_id"))
                        .marketplaceId(res.getString("marketplace_id"))
                        .targetingText(res.getString("targeting_text"))
                        .adOrderNum(Optional.ofNullable(res.getInt("ad_order_num")).orElse(0))
                        .totalSales(res.getBigDecimal("total_sales") != null ? res.getBigDecimal("total_sales") : BigDecimal.ZERO)
                        //本广告产品订单量
                        .adSaleNum(Optional.ofNullable(res.getInt("ad_sale_num")).orElse(0))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(res.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        //广告销量
                        .saleNum(Optional.ofNullable(res.getInt("sale_num")).orElse(0))
                        .countDate(res.getString("count_date"))
                        .build();
                return optionVo;
            }
        });
    }

    private String getWhereSqlCountByKeyword(int puid, SearchVo searchVo, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" where puid=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(DateUtil.dateToStrWithFormat(searchVo.getStart(), "yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(searchVo.getEnd(), "yyyyMMdd"));
        if (CollectionUtils.isNotEmpty(searchVo.getShopIds())) {
            whereSql.append(" and shop_id in ('").append(StringUtils.join(searchVo.getShopIds(), "','")).append("') ");
        }
        whereSql.append(" group by shop_id,keyword_id,`query` ");
        if ("daily".equals(searchVo.getTabType())) {
            whereSql.append(", count_date");
        }
        return whereSql.toString();
    }

    private String getWhereSqlCountByTargetId(int puid, SearchVo searchVo, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" where puid=? and count_date>=? and count_date<=?");
        argsList.add(puid);
        argsList.add(DateUtil.dateToStrWithFormat(searchVo.getStart(), "yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(searchVo.getEnd(), "yyyyMMdd"));
        if (CollectionUtils.isNotEmpty(searchVo.getShopIds())) {
            whereSql.append(" and shop_id in ('").append(StringUtils.join(searchVo.getShopIds(), "','")).append("') ");
        }
        whereSql.append(" group by shop_id,`target_id`,`query` ");
        if ("daily".equals(searchVo.getTabType())) {
            whereSql.append(", count_date");
        }
        return whereSql.toString();
    }


    @Override
    public CpcQueryKeywordReport sumReport(Integer puid, CpcQueryWordDto dto) {
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectSql = new StringBuilder("SELECT SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales FROM ");
        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));
        selectSql.append(tableName).append(" ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByKeyword(puid, dto, argsList);
        selectSql.append(whereSql);
        StringBuilder sumSql = new StringBuilder("select SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales from ( ")
                .append(selectSql).append(" ) t");
        Object[] args = argsList.toArray();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForObject(sumSql.toString(), args, getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public CpcQueryKeywordReport sumManageReport(Integer puid, CpcQueryWordDto dto) {
        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectSql = new StringBuilder("SELECT SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales FROM ");
        selectSql.append(tableName).append(" ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByKeyword(puid, dto, argsList);
        selectSql.append(whereSql);

        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectTargetSql = new StringBuilder("SELECT SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales FROM ");
        String unionTableName = getTableNameByStartDateAndTableName(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD), "t_cpc_query_targeting_report");
        selectTargetSql.append(unionTableName).append(" ");
        String whereTargetSql = getWhereSqlCountByTargetId(puid, dto, argsList);
        selectTargetSql.append(whereTargetSql);

        StringBuilder sql = new StringBuilder(selectSql).append(" union all ").append(selectTargetSql);

        StringBuilder sumSql = new StringBuilder("select SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales from ( ")
                .append(sql).append(" ) t");
        Object[] args = argsList.toArray();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForObject(sumSql.toString(), args, getMapper());
        } finally {
            hintManager.close();
        }

    }

    private String getWhereSqlCountByKeyword(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder();
        /**
         *   根据匹配类型（matchType）查对应表的数据
         *   matchType in ('close-match','loose-match')紧密匹配，宽泛匹配 不查询该表数据
         *   matchType in ('broad','phrase','exact')'广泛匹配','词组匹配','精准匹配' 查询该表数据
         *   matchTypes：符合查询该表数据的条件('广泛匹配','词组匹配','精准匹配')数组
         *   MatchValueEnum值是('广泛匹配','词组匹配','精准匹配')的枚举
         */
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(), StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        //不带匹配类型条件查询不走下面逻辑
        // start
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isNotBlank(MatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            //如果匹配条件为(紧密匹配，宽泛匹配),则不查询数据
            //把matchTypes作为条件查不出数据
            if (CollectionUtils.isEmpty(matchTypes)) {
                whereSql.append(" group by keyword_id,`query` having 1=0 ");
                return whereSql.toString();
            }
        }
        //end
        whereSql.append(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        whereSql.append(" and  query not REGEXP '" + ASIN_REGEX + "' ");
        if (CollectionUtils.isNotEmpty(matchTypes)){
            whereSql.append(SqlStringUtil.dealInList("match_type",matchTypes,argsList));
        }
        if (StringUtils.isNotBlank(dto.getCampaignId())) {
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));

        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(dto.getGroupId())) {
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getSearchQueryTagParamList())) {
            whereSql.append(" and (campaign_id, ad_group_id, query) ");
            if (dto.getQueryWordTagTypeList().contains(Constants.QUERY_NOT_TARGET)) {
                whereSql.append(" not in ");
            } else {
                whereSql.append(" in ");
            }
            StringBuilder stringBuilder = new StringBuilder(" ( ");
            for (SearchQueryTagParam param : dto.getSearchQueryTagParamList()) {
                stringBuilder.append("( ?,?,? ),");
                argsList.add(param.getCampaignId());
                argsList.add(param.getAdGroupId());
                argsList.add(param.getQuery());
            }
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
            stringBuilder.append(" )");
            whereSql.append(stringBuilder);
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryKeywordReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    whereSql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    if (dto.getListSearchValue().size() > 1) {
                        whereSql.append(SqlStringUtil.dealInList(field, dto.getListSearchValue(), argsList));
                    } else {
                        whereSql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getListSearchValue().get(0));
                    }
                }
            }
        }
        //词根搜索
        if (CollectionUtils.isNotEmpty(dto.getQueryIds())) {
            whereSql.append(SqlStringUtil.dealInList("query_id", dto.getQueryIds(), argsList));
        }
        whereSql.append(" group by keyword_id,`query` ");
        if (dto.getUseAdvanced()) {
            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if (dto.getImpressionsMin() != null) {
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if (dto.getImpressionsMax() != null) {
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if (dto.getClicksMin() != null) {
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if (dto.getClicksMax() != null) {
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if (dto.getClickRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if (dto.getClickRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if (dto.getCostMin() != null) {
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if (dto.getCostMax() != null) {
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if (dto.getCpcMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if (dto.getCpcMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }
            //广告订单量
            if (dto.getOrderNumMin() != null) {
                whereSql.append(" and sale_num >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if (dto.getOrderNumMax() != null) {
                whereSql.append(" and sale_num <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if (dto.getSalesMin() != null) {
                whereSql.append(" and total_sales >= ?");
                argsList.add(dto.getSalesMin());
            }
            if (dto.getSalesMax() != null) {
                whereSql.append(" and total_sales <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if (dto.getSalesConversionRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if (dto.getSalesConversionRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if (dto.getAcosMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if (dto.getAcosMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }

            /**************************高级搜索新增查询指标***************************/
            //CPA
            if (dto.getCpaMin() != null) {
                whereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) >= ?");
                argsList.add(dto.getCpaMin());
            }
            if (dto.getCpaMax() != null) {
                whereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) <= ?");
                argsList.add(dto.getCpaMax());
            }

            //本广告产品订单量
            if (dto.getAdSaleNumMin() != null) {
                whereSql.append(" and ifnull(ad_sale_num, 0) >= ?");
                argsList.add(dto.getAdSaleNumMin());
            }
            if (dto.getAdSaleNumMax() != null) {
                whereSql.append(" and ifnull(ad_sale_num, 0) <= ?");
                argsList.add(dto.getAdSaleNumMax());
            }
            //其他产品广告订单量
            if (dto.getAdOtherOrderNumMin() != null) {
                whereSql.append(" and ifnull(sale_num - ad_sale_num, 0) >= ?");
                argsList.add(dto.getAdOtherOrderNumMin());
            }
            if (dto.getAdOtherOrderNumMax() != null) {
                whereSql.append(" and ifnull(sale_num - ad_sale_num, 0) <= ?");
                argsList.add(dto.getAdOtherOrderNumMax());
            }
            //本广告产品销售额
            if (dto.getAdSalesMin() != null) {
                whereSql.append(" and ifnull(ad_sales, 0) >= ?");
                argsList.add(dto.getAdSalesMin());
            }
            if (dto.getAdSalesMax() != null) {
                whereSql.append(" and ifnull(ad_sales, 0) <= ?");
                argsList.add(dto.getAdSalesMax());
            }
            //其他产品广告销售额
            if (dto.getAdOtherSalesMin() != null) {
                whereSql.append(" and ifnull(total_sales - ad_sales, 0) >= ?");
                argsList.add(dto.getAdOtherSalesMin());
            }
            if (dto.getAdOtherSalesMax() != null) {
                whereSql.append(" and ifnull(total_sales - ad_sales, 0) <= ?");
                argsList.add(dto.getAdOtherSalesMax());
            }
            //广告销量
            if (dto.getAdSalesTotalMin() != null) {
                whereSql.append(" and ifnull(order_num, 0) >= ?");
                argsList.add(dto.getAdSalesTotalMin());
            }
            if (dto.getAdSalesTotalMax() != null) {
                whereSql.append(" and ifnull(order_num, 0) <= ?");
                argsList.add(dto.getAdSalesTotalMax());
            }
            //本广告产品销量
            if (dto.getAdSelfSaleNumMin() != null) {
                whereSql.append(" and ifnull(ad_order_num, 0) >= ?");
                argsList.add(dto.getAdSelfSaleNumMin());
            }
            if (dto.getAdSelfSaleNumMax() != null) {
                whereSql.append(" and ifnull(ad_order_num, 0) <= ?");
                argsList.add(dto.getAdSelfSaleNumMax());
            }
            //其他产品广告销量
            if (dto.getAdOtherSaleNumMin() != null) {
                whereSql.append(" and ifnull(order_num - ad_order_num, 0) >= ?");
                argsList.add(dto.getAdOtherSaleNumMin());
            }
            if (dto.getAdOtherSaleNumMax() != null) {
                whereSql.append(" and ifnull(order_num - ad_order_num, 0) <= ?");
                argsList.add(dto.getAdOtherSaleNumMax());
            }

            // 广告笔单价(广告销售额÷广告订单量×100%)
            if (dto.getAdvertisingUnitPriceMin() != null){
                whereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) >= ?");
                argsList.add(dto.getAdvertisingUnitPriceMin());
            }
            if (dto.getAdvertisingUnitPriceMax() != null){
                whereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) <= ?");
                argsList.add(dto.getAdvertisingUnitPriceMax());
            }
        }
        return whereSql.toString();
    }


    private String getWhereSqlCountByTargetId(int puid, CpcQueryWordDto dto, List<Object> argsList) {

        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        whereSql.append(" and  query not REGEXP '" + ASIN_REGEX + "' ");
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(),StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        List<String> matchAsinTypes = Lists.newArrayList();
        /**
         *   根据匹配类型（matchType）查对应表的数据
         *   matchType in ('close-match','loose-match')'广泛匹配','词组匹配','精准匹配' 不查询该表数据
         *   matchType in ('broad','phrase','exact')'紧密匹配'，'宽泛匹配'查询该表数据
         *   matchTypes：符合查询该表数据的条件('紧密匹配'，'宽泛匹配')数组
         *   MatchValueEnum值是('广泛匹配','词组匹配','精准匹配')的枚举
         */
        //不带匹配类型条件查询不走下面逻辑
        // start
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isBlank(MatchValueEnum.getMatchValue(matchType))) {
                    if (!matchType.contains("=")){
                        matchTypes.add(matchType);
                    }
                }
                if (matchType.contains("=")) {
                    matchAsinTypes.add(matchType);
                }
            }
            //如果匹配条件不是('紧密匹配'，'宽泛匹配'),则不查询数据

            if (CollectionUtils.isEmpty(matchTypes) && CollectionUtils.isEmpty(matchAsinTypes)) {
                whereSql.append(" group by target_id,`query` having 1=0 ");
                return whereSql.toString();
            }
        }
        //end
        if(StringUtils.isNotBlank(dto.getCampaignId())){
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(dto.getGroupId())) {
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getSearchQueryTagParamList())) {
            whereSql.append(" and (campaign_id, ad_group_id, query) ");
            if (dto.getQueryWordTagTypeList().contains(Constants.QUERY_NOT_TARGET)) {
                whereSql.append(" not in ");
            } else {
                whereSql.append(" in ");
            }
            StringBuilder stringBuilder = new StringBuilder(" ( ");
            for (SearchQueryTagParam param : dto.getSearchQueryTagParamList()) {
                stringBuilder.append("( ?,?,? ),");
                argsList.add(param.getCampaignId());
                argsList.add(param.getAdGroupId());
                argsList.add(param.getQuery());
            }
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
            stringBuilder.append(" )");
            whereSql.append(stringBuilder);
        }

        if (CollectionUtils.isNotEmpty(matchTypes) || CollectionUtils.isNotEmpty(matchAsinTypes)) {
            whereSql.append(" and (");
            if (CollectionUtils.isNotEmpty(matchTypes) && CollectionUtils.isNotEmpty(matchAsinTypes)) {
                whereSql.append(SqlStringUtil.dealInListNotAnd("targeting_expression", matchTypes, argsList));
                whereSql.append(" or ");
                whereSql.append(SqlStringUtil.dealPrefixLikeListOr("targeting_expression", matchAsinTypes, argsList));
            } else if (CollectionUtils.isNotEmpty(matchTypes)) {
                whereSql.append(SqlStringUtil.dealInListNotAnd("targeting_expression", matchTypes, argsList));
            } else {
                whereSql.append(SqlStringUtil.dealPrefixLikeListOr("targeting_expression", matchAsinTypes, argsList));
            }
            whereSql.append(")");
        }
        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryTargetingReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    whereSql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    if (dto.getListSearchValue().size() > 1) {
                        whereSql.append(SqlStringUtil.dealInList(field, dto.getListSearchValue(), argsList));
                    } else {
                        whereSql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getListSearchValue().get(0));
                    }
                }
            } else if ("keywordText".equalsIgnoreCase(dto.getSearchField())) {
                if (!dto.getSearchValue().equalsIgnoreCase("自动投放组")) {
                    whereSql.append(" and targeting_text = ? ");  // 需要查不出这个表的数据
                    argsList.add(dto.getSearchValue());
                }
            }
        }
        //词根搜索
        if (CollectionUtils.isNotEmpty(dto.getQueryIds())) {
            whereSql.append(SqlStringUtil.dealInList("query_id", dto.getQueryIds(), argsList));
        }
        whereSql.append(" group by target_id,`query` ");
        if (dto.getUseAdvanced()) {
            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if (dto.getImpressionsMin() != null) {
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if (dto.getImpressionsMax() != null) {
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if (dto.getClicksMin() != null) {
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if (dto.getClicksMax() != null) {
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if (dto.getClickRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if (dto.getClickRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if (dto.getCostMin() != null) {
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if (dto.getCostMax() != null) {
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if (dto.getCpcMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if (dto.getCpcMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }
            //广告订单量
            if (dto.getOrderNumMin() != null) {
                whereSql.append(" and sale_num >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if (dto.getOrderNumMax() != null) {
                whereSql.append(" and sale_num <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if (dto.getSalesMin() != null) {
                whereSql.append(" and total_sales >= ?");
                argsList.add(dto.getSalesMin());
            }
            if (dto.getSalesMax() != null) {
                whereSql.append(" and total_sales <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if (dto.getSalesConversionRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if (dto.getSalesConversionRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if (dto.getAcosMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if (dto.getAcosMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }

            /**************************高级搜索新增查询指标***************************/
            //CPA
            if (dto.getCpaMin() != null) {
                whereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) >= ?");
                argsList.add(dto.getCpaMin());
            }
            if (dto.getCpaMax() != null) {
                whereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) <= ?");
                argsList.add(dto.getCpaMax());
            }

            //本广告产品订单量
            if (dto.getAdSaleNumMin() != null) {
                whereSql.append(" and ifnull(ad_sale_num, 0) >= ?");
                argsList.add(dto.getAdSaleNumMin());
            }
            if (dto.getAdSaleNumMax() != null) {
                whereSql.append(" and ifnull(ad_sale_num, 0) <= ?");
                argsList.add(dto.getAdSaleNumMax());
            }
            //其他产品广告订单量
            if (dto.getAdOtherOrderNumMin() != null) {
                whereSql.append(" and ifnull(sale_num - ad_sale_num, 0) >= ?");
                argsList.add(dto.getAdOtherOrderNumMin());
            }
            if (dto.getAdOtherOrderNumMax() != null) {
                whereSql.append(" and ifnull(sale_num - ad_sale_num, 0) <= ?");
                argsList.add(dto.getAdOtherOrderNumMax());
            }
            //本广告产品销售额
            if (dto.getAdSalesMin() != null) {
                whereSql.append(" and ifnull(ad_sales, 0) >= ?");
                argsList.add(dto.getAdSalesMin());
            }
            if (dto.getAdSalesMax() != null) {
                whereSql.append(" and ifnull(ad_sales, 0) <= ?");
                argsList.add(dto.getAdSalesMax());
            }
            //其他产品广告销售额
            if (dto.getAdOtherSalesMin() != null) {
                whereSql.append(" and ifnull(total_sales - ad_sales, 0) >= ?");
                argsList.add(dto.getAdOtherSalesMin());
            }
            if (dto.getAdOtherSalesMax() != null) {
                whereSql.append(" and ifnull(total_sales - ad_sales, 0) <= ?");
                argsList.add(dto.getAdOtherSalesMax());
            }
            //广告销量
            if (dto.getAdSalesTotalMin() != null) {
                whereSql.append(" and ifnull(order_num, 0) >= ?");
                argsList.add(dto.getAdSalesTotalMin());
            }
            if (dto.getAdSalesTotalMax() != null) {
                whereSql.append(" and ifnull(order_num, 0) <= ?");
                argsList.add(dto.getAdSalesTotalMax());
            }
            //本广告产品销量
            if (dto.getAdSelfSaleNumMin() != null) {
                whereSql.append(" and ifnull(ad_order_num, 0) >= ?");
                argsList.add(dto.getAdSelfSaleNumMin());
            }
            if (dto.getAdSelfSaleNumMax() != null) {
                whereSql.append(" and ifnull(ad_order_num, 0) <= ?");
                argsList.add(dto.getAdSelfSaleNumMax());
            }
            //其他产品广告销量
            if (dto.getAdOtherSaleNumMin() != null) {
                whereSql.append(" and ifnull(order_num - ad_order_num, 0) >= ?");
                argsList.add(dto.getAdOtherSaleNumMin());
            }
            if (dto.getAdOtherSaleNumMax() != null) {
                whereSql.append(" and ifnull(order_num - ad_order_num, 0) <= ?");
                argsList.add(dto.getAdOtherSaleNumMax());
            }
            // 广告笔单价(广告销售额÷广告订单量×100%)
            if (dto.getAdvertisingUnitPriceMin() != null){
                whereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) >= ?");
                argsList.add(dto.getAdvertisingUnitPriceMin());
            }
            if (dto.getAdvertisingUnitPriceMax() != null){
                whereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) <= ?");
                argsList.add(dto.getAdvertisingUnitPriceMax());
            }

        }
        return whereSql.toString();
    }


    /**
     * 汇总
     * @param puid
     * @param dto
     * @param argsList
     * @return
     */
    private String getWhereSqlCountByDate(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=?  and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        /**
         *   根据匹配类型（matchType）查对应表的数据
         *   matchType in ('close-match','loose-match')'紧密匹配'，'宽泛匹配' 不查询该表数据
         *   matchType in ('broad','phrase','exact')'广泛匹配','词组匹配','精准匹配'查询该表数据
         *   matchTypes：符合查询该表数据的条件('广泛匹配','词组匹配','精准匹配')数组
         *   MatchValueEnum值是('广泛匹配','词组匹配','精准匹配')的枚举
         */
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(), StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        //不带匹配类型条件查询不走下面逻辑
        // start
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isNotBlank(MatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            //如果匹配条件为(紧密匹配，宽泛匹配),则不查询数据
            if (CollectionUtils.isEmpty(matchTypes)) {
                whereSql.append(" group by keyword_id,`query` having 1=0 ");
                return whereSql.toString();
            }
        }
        // end
        whereSql.append(" and  query not REGEXP '" + ASIN_REGEX + "' ");
        if (CollectionUtils.isNotEmpty(matchTypes)) {
            whereSql.append(SqlStringUtil.dealInList("match_type", matchTypes, argsList));
        }
        if (StringUtils.isNotBlank(dto.getCampaignId())) {
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));

        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(dto.getGroupId())) {
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getSearchQueryTagParamList())) {
            whereSql.append(" and (campaign_id, ad_group_id, query) in ");
            StringBuilder stringBuilder = new StringBuilder(" ( ");
            for (SearchQueryTagParam param : dto.getSearchQueryTagParamList()) {
                stringBuilder.append("( ?,?,? ),");
                argsList.add(param.getCampaignId());
                argsList.add(param.getAdGroupId());
                argsList.add(param.getQuery());
            }
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
            stringBuilder.append(" )");
            whereSql.append(stringBuilder);
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryKeywordReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    whereSql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    if (dto.getListSearchValue().size() > 1) {
                        whereSql.append(SqlStringUtil.dealInList(field, dto.getListSearchValue(), argsList));
                    } else {
                        whereSql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getListSearchValue().get(0));
                    }
                }
            }
        }
        //词根搜索
        if (CollectionUtils.isNotEmpty(dto.getQueryIds())) {
            whereSql.append(SqlStringUtil.dealInList("query_id", dto.getQueryIds(), argsList));
        }
        whereSql.append(" group by keyword_id,`query` ");
        if (dto.getUseAdvanced()) {

            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if (dto.getImpressionsMin() != null) {
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if (dto.getImpressionsMax() != null) {
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if (dto.getClicksMin() != null) {
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if (dto.getClicksMax() != null) {
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if (dto.getClickRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if (dto.getClickRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if (dto.getCostMin() != null) {
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if (dto.getCostMax() != null) {
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if (dto.getCpcMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if (dto.getCpcMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }
            //广告订单量
            if (dto.getOrderNumMin() != null) {
                whereSql.append(" and sale_num >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if (dto.getOrderNumMax() != null) {
                whereSql.append(" and sale_num <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if (dto.getSalesMin() != null) {
                whereSql.append(" and total_sales >= ?");
                argsList.add(dto.getSalesMin());
            }
            if (dto.getSalesMax() != null) {
                whereSql.append(" and total_sales <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if (dto.getSalesConversionRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if (dto.getSalesConversionRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if (dto.getAcosMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if (dto.getAcosMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }

            /*****************高级搜索新增查询指标*************************************/
            //CPA:广告花费除以广告订单量
            if (dto.getCpaMin() != null) {
                whereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) >= ?");
                argsList.add(dto.getCpaMin());
            }
            if (dto.getCpaMax() != null) {
                whereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) <= ?");
                argsList.add(dto.getCpaMax());
            }
            //“品牌新买家”订单量 orders_new_to_brand14d
            if (dto.getOrdersNewToBrandFTDMin() != null) {
                whereSql.append(" and ifnull(orders_new_to_brand14d, 0) >= ?");
                argsList.add(dto.getOrdersNewToBrandFTDMin());
            }
            if (dto.getOrdersNewToBrandFTDMax() != null) {
                whereSql.append(" and ifnull(orders_new_to_brand14d, 0) <= ?");
                argsList.add(dto.getOrdersNewToBrandFTDMax());
            }
            //“品牌新买家”订单百分比
            if (dto.getOrderRateNewToBrandFTDMin() != null) {
                whereSql.append(" and ROUND(ifnull(orders_new_to_brand14d/total_sales,0), 6) >= ?");
                argsList.add(dto.getOrderRateNewToBrandFTDMin());
            }
            if (dto.getOrderRateNewToBrandFTDMax() != null) {
                whereSql.append(" and ROUND(ifnull(orders_new_to_brand14d/total_sales,0), 6) <= ?");
                argsList.add(dto.getOrderRateNewToBrandFTDMax());
            }
            //“品牌新买家”销售额
            if (dto.getSalesNewToBrandFTDMin() != null) {
                whereSql.append(" and ifnull(sales_new_to_brand14d, 0) >= ?");
                argsList.add(dto.getSalesNewToBrandFTDMin());
            }
            if (dto.getSalesNewToBrandFTDMax() != null) {
                whereSql.append(" and ifnull(sales_new_to_brand14d, 0) <= ?");
                argsList.add(dto.getSalesNewToBrandFTDMax());
            }
            //“品牌新买家”销售额百分比
            if (dto.getSalesRateNewToBrandFTDMin() != null) {
                whereSql.append(" and ROUND(ifnull(sales_new_to_brand14d/total_sales,0), 6) >= ?");
                argsList.add(dto.getSalesRateNewToBrandFTDMin());
            }
            if (dto.getSalesRateNewToBrandFTDMax() != null) {
                whereSql.append(" and ROUND(ifnull(sales_new_to_brand14d/total_sales,0), 6) <= ?");
                argsList.add(dto.getSalesRateNewToBrandFTDMax());
            }
            //“品牌新买家”订单转化率 orders_new_to_brand14d/clicks
            if (dto.getBrandNewBuyerOrderConversionRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(orders_new_to_brand14d/clicks,0), 6) >= ?");
                argsList.add(dto.getBrandNewBuyerOrderConversionRateMin());
            }
            if (dto.getBrandNewBuyerOrderConversionRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(orders_new_to_brand14d/clicks,0), 6) <= ?");
                argsList.add(dto.getBrandNewBuyerOrderConversionRateMax());
            }
            //本广告产品销量
            if (dto.getAdSelfSaleNumMin() != null) {
                whereSql.append(" and ifnull(ad_order_num, 0) >= ?");
                argsList.add(dto.getAdSelfSaleNumMin());
            }
            if (dto.getAdSelfSaleNumMax() != null) {
                whereSql.append(" and ifnull(ad_order_num, 0) <= ?");
                argsList.add(dto.getAdSelfSaleNumMax());
            }
            //其他产品广告销量
            if (dto.getAdOtherSaleNumMin() != null) {
                whereSql.append(" and ifnull(sales_num - ad_order_num, 0) >= ?");
                argsList.add(dto.getAdOtherSaleNumMin());
            }
            if (dto.getAdOtherSaleNumMax() != null) {
                whereSql.append(" and ifnull(sales_num - ad_order_num, 0) <= ?");
                argsList.add(dto.getAdOtherSaleNumMax());
            }

            //广告销量sales_num
            if (dto.getAdSalesTotalMin() != null) {
                whereSql.append(" and ifnull(sales_num, 0) >= ?");
                argsList.add(dto.getAdSalesTotalMin());
            }
            if (dto.getAdSalesTotalMax() != null) {
                whereSql.append(" and ifnull(sales_num, 0) <= ?");
                argsList.add(dto.getAdSalesTotalMax());
            }
            //其他产品广告销售额
            if (dto.getAdOtherSalesMin() != null) {
                whereSql.append(" and ifnull(total_sales - ad_sales, 0) >= ?");
                argsList.add(dto.getAdOtherSalesMin());
            }
            if (dto.getAdOtherSalesMax() != null) {
                whereSql.append(" and ifnull(total_sales - ad_sales, 0) <= ?");
                argsList.add(dto.getAdOtherSalesMax());
            }
            //本广告产品销售额
            if (dto.getAdSalesMin() != null) {
                whereSql.append(" and ifnull(ad_sales, 0) >= ?");
                argsList.add(dto.getAdSalesMin());
            }
            if (dto.getAdSalesMax() != null) {
                whereSql.append(" and ifnull(ad_sales, 0) <= ?");
                argsList.add(dto.getAdSalesMax());
            }
            //本广告产品订单量
            if (dto.getAdSaleNumMin() != null) {
                whereSql.append(" and ifnull(ad_sale_num, 0) >= ?");
                argsList.add(dto.getAdSaleNumMin());
            }
            if (dto.getAdSaleNumMax() != null) {
                whereSql.append(" and ifnull(ad_sale_num, 0) <= ?");
                argsList.add(dto.getAdSaleNumMax());
            }
            //其他广告产品订单量
            if (dto.getAdOtherOrderNumMin() != null) {
                whereSql.append(" and ifnull(sale_num - ad_sale_num, 0) >= ?");
                argsList.add(dto.getAdOtherOrderNumMin());
            }
            if (dto.getAdOtherOrderNumMax() != null) {
                whereSql.append(" and ifnull(sale_num - ad_sale_num, 0) <= ?");
                argsList.add(dto.getAdOtherOrderNumMax());
            }

            //品牌新买家订单转化率
            if (dto.getBrandNewBuyerOrderConversionRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(orders_new_to_brand14d/clicks,0), 6) >= ?");
                argsList.add(MathUtil.divide(dto.getBrandNewBuyerOrderConversionRateMin(), BigDecimal.valueOf(100)));
            }
            if (dto.getBrandNewBuyerOrderConversionRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(orders_new_to_brand14d/clicks,0), 6) <= ?");
                argsList.add(MathUtil.divide(dto.getBrandNewBuyerOrderConversionRateMax(), BigDecimal.valueOf(100)));
            }

            // 广告笔单价(广告销售额÷广告订单量×100%)
            if (dto.getAdvertisingUnitPriceMin() != null){
                whereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) >= ?");
                argsList.add(dto.getAdvertisingUnitPriceMin());
            }
            if (dto.getAdvertisingUnitPriceMax() != null){
                whereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) <= ?");
                argsList.add(dto.getAdvertisingUnitPriceMax());
            }

        }
        return whereSql.toString();
    }

    /**
     * 汇总
     * @param puid
     * @param dto
     * @param argsList
     * @return
     */
    private String getWhereTargetSqlCountByDate(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=?  and count_date>=? and count_date<=?  ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        whereSql.append(" and  query not REGEXP '" + ASIN_REGEX + "' ");
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(),StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        List<String> matchAsinTypes = Lists.newArrayList();
        /**
         *   根据匹配类型（matchType）查对应表的数据
         *   matchType in ('close-match','loose-match')'广泛匹配','词组匹配','精准匹配' 不查询该表数据
         *   matchType in ('broad','phrase','exact')'紧密匹配'，'宽泛匹配'查询该表数据
         *   matchTypes：符合查询该表数据的条件('紧密匹配'，'宽泛匹配')数组
         *   MatchValueEnum值是('广泛匹配','词组匹配','精准匹配')的枚举
         */
        //不带匹配类型条件查询不走下面逻辑
        // start
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isBlank(MatchValueEnum.getMatchValue(matchType))) {
                    if (!matchType.contains("=")){
                        matchTypes.add(matchType);
                    }
                }
                if (matchType.contains("=")) {
                    matchAsinTypes.add(matchType);
                }
            }
            //如果匹配条件不是('紧密匹配'，'宽泛匹配'),则不查询数据

            if (CollectionUtils.isEmpty(matchTypes) && CollectionUtils.isEmpty(matchAsinTypes)) {
                whereSql.append(" group by target_id,`query` having 1=0 ");
                return whereSql.toString();
            }
        }
        //end
        if (StringUtils.isNotBlank(dto.getCampaignId())) {
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(dto.getGroupId())) {
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getSearchQueryTagParamList())) {
            whereSql.append(" and (campaign_id, ad_group_id, query) in ");
            StringBuilder stringBuilder = new StringBuilder(" ( ");
            for (SearchQueryTagParam param : dto.getSearchQueryTagParamList()) {
                stringBuilder.append("( ?,?,? ),");
                argsList.add(param.getCampaignId());
                argsList.add(param.getAdGroupId());
                argsList.add(param.getQuery());
            }
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
            stringBuilder.append(" )");
            whereSql.append(stringBuilder);
        }

//        whereSql.append(" and targeting_type = 'TARGETING_EXPRESSION_PREDEFINED' and targeting_expression in ('close-match','loose-match') ");
//        whereSql.append(" and targeting_type = 'TARGETING_EXPRESSION_PREDEFINED' ");
//        if (CollectionUtils.isNotEmpty(matchTypes)) {
//            whereSql.append(SqlStringUtil.dealInList("targeting_expression", matchTypes, argsList));
//        }

        if (CollectionUtils.isNotEmpty(matchTypes) || CollectionUtils.isNotEmpty(matchAsinTypes)) {
            whereSql.append(" and (");
            if (CollectionUtils.isNotEmpty(matchTypes) && CollectionUtils.isNotEmpty(matchAsinTypes)) {
                whereSql.append(SqlStringUtil.dealInListNotAnd("targeting_expression", matchTypes, argsList));
                whereSql.append(" or ");
                whereSql.append(SqlStringUtil.dealPrefixLikeListOr("targeting_expression", matchAsinTypes, argsList));
            } else if (CollectionUtils.isNotEmpty(matchTypes)) {
                whereSql.append(SqlStringUtil.dealInListNotAnd("targeting_expression", matchTypes, argsList));
            } else {
                whereSql.append(SqlStringUtil.dealPrefixLikeListOr("targeting_expression", matchAsinTypes, argsList));
            }
            whereSql.append(")");
        }


        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryTargetingReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    whereSql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    if (dto.getListSearchValue().size() > 1) {
                        whereSql.append(SqlStringUtil.dealInList(field, dto.getListSearchValue(), argsList));
                    } else {
                        whereSql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getListSearchValue().get(0));
                    }
                }
            } else if ("keywordText".equalsIgnoreCase(dto.getSearchField())) {
                if (!dto.getSearchValue().equalsIgnoreCase("自动投放组")) {
                    whereSql.append(" and targeting_text = ? ");  // 需要查不出这个表的数据
                    argsList.add(dto.getSearchValue());
                }
            }
        }
        //词根搜索
        if (CollectionUtils.isNotEmpty(dto.getQueryIds())) {
            whereSql.append(SqlStringUtil.dealInList("query_id", dto.getQueryIds(), argsList));
        }
        whereSql.append(" group by target_id,`query` ");
        if (dto.getUseAdvanced()) {
            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if (dto.getImpressionsMin() != null) {
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if (dto.getImpressionsMax() != null) {
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if (dto.getClicksMin() != null) {
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if (dto.getClicksMax() != null) {
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if (dto.getClickRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if (dto.getClickRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if (dto.getCostMin() != null) {
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if (dto.getCostMax() != null) {
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if (dto.getCpcMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if (dto.getCpcMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }
            //广告订单量
            if (dto.getOrderNumMin() != null) {
                whereSql.append(" and sale_num >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if (dto.getOrderNumMax() != null) {
                whereSql.append(" and sale_num <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if (dto.getSalesMin() != null) {
                whereSql.append(" and total_sales >= ?");
                argsList.add(dto.getSalesMin());
            }
            if (dto.getSalesMax() != null) {
                whereSql.append(" and total_sales <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if (dto.getSalesConversionRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if (dto.getSalesConversionRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if (dto.getAcosMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if (dto.getAcosMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }

            /******************高级筛选新增查询指标*****************************/

            //CPA:广告花费除以广告订单量
            if (dto.getCpaMin() != null) {
                whereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) >= ?");
                argsList.add(dto.getCpaMin());
            }
            if (dto.getCpaMax() != null) {
                whereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) <= ?");
                argsList.add(dto.getCpaMax());
            }
            //“品牌新买家”订单量 orders_new_to_brand14d
            if (dto.getOrdersNewToBrandFTDMin() != null) {
                whereSql.append(" and ifnull(orders_new_to_brand14d, 0) >= ?");
                argsList.add(dto.getOrdersNewToBrandFTDMin());
            }
            if (dto.getOrdersNewToBrandFTDMax() != null) {
                whereSql.append(" and ifnull(orders_new_to_brand14d, 0) <= ?");
                argsList.add(dto.getOrdersNewToBrandFTDMax());
            }
            //“品牌新买家”订单百分比
            if (dto.getOrderRateNewToBrandFTDMin() != null) {
                whereSql.append(" and ROUND(ifnull(orders_new_to_brand14d/total_sales,0),6) >= ?");
                argsList.add(dto.getOrderRateNewToBrandFTDMin());
            }
            if (dto.getOrderRateNewToBrandFTDMax() != null) {
                whereSql.append(" and ROUND(ifnull(orders_new_to_brand14d/total_sales,0),6) <= ?");
                argsList.add(dto.getOrderRateNewToBrandFTDMax());
            }
            //“品牌新买家”销售额
            if (dto.getSalesNewToBrandFTDMin() != null) {
                whereSql.append(" and ifnull(sales_new_to_brand14d, 0) >= ?");
                argsList.add(dto.getSalesNewToBrandFTDMin());
            }
            if (dto.getSalesNewToBrandFTDMax() != null) {
                whereSql.append(" and ifnull(sales_new_to_brand14d, 0) <= ?");
                argsList.add(dto.getSalesNewToBrandFTDMax());
            }
            //“品牌新买家”销售额百分比
            if (dto.getSalesRateNewToBrandFTDMin() != null) {
                whereSql.append(" and ROUND(ifnull(sales_new_to_brand14d/total_sales,0), 6) >= ?");
                argsList.add(dto.getSalesRateNewToBrandFTDMin());
            }
            if (dto.getSalesRateNewToBrandFTDMax() != null) {
                whereSql.append(" and ROUND(ifnull(sales_new_to_brand14d/total_sales,0), 6) <= ?");
                argsList.add(dto.getSalesRateNewToBrandFTDMax());
            }
            //“品牌新买家”订单转化率 orders_new_to_brand14d/clicks
            if (dto.getBrandNewBuyerOrderConversionRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(orders_new_to_brand14d/clicks,0), 6) >= ?");
                argsList.add(dto.getBrandNewBuyerOrderConversionRateMin());
            }
            if (dto.getBrandNewBuyerOrderConversionRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(orders_new_to_brand14d/clicks,0), 6) <= ?");
                argsList.add(dto.getBrandNewBuyerOrderConversionRateMax());
            }
            //本广告产品销量
            if (dto.getAdSelfSaleNumMin() != null) {
                whereSql.append(" and ifnull(ad_order_num, 0) >= ?");
                argsList.add(dto.getAdSelfSaleNumMin());
            }
            if (dto.getAdSelfSaleNumMax() != null) {
                whereSql.append(" and ifnull(ad_order_num, 0) <= ?");
                argsList.add(dto.getAdSelfSaleNumMax());
            }
            //其他产品广告销量
            if (dto.getAdOtherSaleNumMin() != null) {
                whereSql.append(" and ifnull(sales_num - ad_order_num, 0) >= ?");
                argsList.add(dto.getAdOtherSaleNumMin());
            }
            if (dto.getAdOtherSaleNumMax() != null) {
                whereSql.append(" and ifnull(sales_num - ad_order_num, 0) <= ?");
                argsList.add(dto.getAdOtherSaleNumMax());
            }

            //广告销量sales_num
            if (dto.getAdSalesTotalMin() != null) {
                whereSql.append(" and ifnull(sales_num, 0) >= ?");
                argsList.add(dto.getAdSalesTotalMin());
            }
            if (dto.getAdSalesTotalMax() != null) {
                whereSql.append(" and ifnull(sales_num, 0) <= ?");
                argsList.add(dto.getAdSalesTotalMax());
            }

            //本广告产品销售额
            if (dto.getAdSalesMin() != null) {
                whereSql.append(" and ifnull(ad_sales, 0) >= ?");
                argsList.add(dto.getAdSalesMin());
            }
            if (dto.getAdSalesMax() != null) {
                whereSql.append(" and ifnull(ad_sales, 0) <= ?");
                argsList.add(dto.getAdSalesMax());
            }
            //其他广告产品销售额
            if (dto.getAdOtherSalesMin() != null) {
                whereSql.append(" and ifnull(total_sales - ad_sales, 0) >= ?");
                argsList.add(dto.getAdOtherSalesMin());
            }
            if (dto.getAdOtherSalesMax() != null) {
                whereSql.append(" and ifnull(total_sales - ad_sales, 0) <= ?");
                argsList.add(dto.getAdOtherSalesMax());
            }

            //本广告产品订单量
            if (dto.getAdSaleNumMin() != null) {
                whereSql.append(" and ifnull(ad_sale_num, 0) >= ?");
                argsList.add(dto.getAdSaleNumMin());
            }
            if (dto.getAdSaleNumMax() != null) {
                whereSql.append(" and ifnull(ad_sale_num, 0) <= ?");
                argsList.add(dto.getAdSaleNumMax());
            }

            //其他广告产品订单量
            if (dto.getAdOtherOrderNumMin() != null) {
                whereSql.append(" and ifnull(sale_num - ad_sale_num, 0) >= ?");
                argsList.add(dto.getAdOtherOrderNumMin());
            }
            if (dto.getAdOtherOrderNumMax() != null) {
                whereSql.append(" and ifnull(sale_num - ad_sale_num, 0) <= ?");
                argsList.add(dto.getAdOtherOrderNumMax());
            }
            // 广告笔单价(广告销售额÷广告订单量×100%)
            if (dto.getAdvertisingUnitPriceMin() != null){
                whereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) >= ?");
                argsList.add(dto.getAdvertisingUnitPriceMin());
            }
            if (dto.getAdvertisingUnitPriceMax() != null){
                whereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) <= ?");
                argsList.add(dto.getAdvertisingUnitPriceMax());
            }
        }
        return whereSql.toString();
    }

    @Override
    public Page detailList(Integer puid, CpcQueryWordDetailDto dto, Page page) {
        StringBuilder selectSql = new StringBuilder(" SELECT ");
        if ("day".equals(dto.getDateType())) {
            selectSql.append(" count_date,");
        } else if ("week".equals(dto.getDateType())) {
            selectSql.append(" concat(from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 2),'~',from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 8)) as count_date ,");
        } else if ("month".equals(dto.getDateType())) {
            selectSql.append(" substring(count_date, 1, 6) as count_date,");
        } else {
            return page;
        }

        if ("day".equals(dto.getDateType()) && StringUtils.isNotEmpty(dto.getKeywordId()) && StringUtils.isNotEmpty(dto.getQuery())) {
            selectSql.append("impressions,clicks,cost,sale_num as sale_num,ad_order_num,total_sales ");
        } else {
            selectSql.append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales ");
        }

        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));

        selectSql.append(" FROM ").append(tableName).append(" ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( select 1 FROM ").append(tableName).append(" ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        if (StringUtils.isNotEmpty(dto.getKeywordId()) && StringUtils.isNotEmpty(dto.getQuery())) {
            whereSql.append(" and keyword_id=? and `query`=? ");
            argsList.add(dto.getKeywordId());
            argsList.add(dto.getQuery());
        }
        if ("day".equals(dto.getDateType())) {
            whereSql.append(" group by count_date ");
        } else if ("week".equals(dto.getDateType())) {
            whereSql.append(" group by concat(from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 2),'~',from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 8)) ");
        } else if ("month".equals(dto.getDateType())) {
            whereSql.append(" group by substring(count_date, 1, 6) ");
        }
        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");

        if (StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())) {
            String orderField = ReportService.getOrderField(dto.getOrderField(), true);
            if (StringUtils.isNotBlank(orderField)) {
                selectSql.append(" order by ").append(orderField);
                if ("desc".equals(dto.getOrderValue())) {
                    selectSql.append(" desc");
                }
            }
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, CpcQueryKeywordReport.class);
    }

    @Override
    public CpcQueryKeywordReport sumDetailReport(Integer puid, CpcQueryWordDetailDto dto) {
        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));
        StringBuilder selectSql = new StringBuilder(" SELECT SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales FROM ");
        selectSql.append(tableName).append(" ");
        selectSql.append(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        if (StringUtils.isNotEmpty(dto.getKeywordId()) && StringUtils.isNotEmpty(dto.getQuery())) {
            selectSql.append(" and keyword_id=? and `query`=?");
            argsList.add(dto.getKeywordId());
            argsList.add(dto.getQuery());
        }

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForObject(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<CpcQueryKeywordReport> detailListChart(Integer puid, CpcQueryWordDetailDto dto) {
        StringBuilder selectSql = new StringBuilder(" SELECT ");
        if ("day".equals(dto.getDateType())) {
            selectSql.append(" count_date,");
        } else if ("week".equals(dto.getDateType())) {
            selectSql.append(" concat(from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 2),'~',from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 8)) as count_date ,");
        } else if ("month".equals(dto.getDateType())) {
            selectSql.append(" substring(count_date, 1, 6) as count_date,");
        } else {
            return null;
        }

        if ("day".equals(dto.getDateType()) && StringUtils.isNotEmpty(dto.getKeywordId()) && StringUtils.isNotEmpty(dto.getQuery())) {
            selectSql.append("impressions,clicks,cost,sale_num as sale_num,ad_order_num,total_sales ");
        } else {
            selectSql.append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales ");
        }

        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));

        selectSql.append(" FROM ").append(tableName).append(" ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        if (StringUtils.isNotEmpty(dto.getKeywordId()) && StringUtils.isNotEmpty(dto.getQuery())) {
            whereSql.append(" and keyword_id=? and `query`=?");
            argsList.add(dto.getKeywordId());
            argsList.add(dto.getQuery());
        }
        if ("day".equals(dto.getDateType())) {
            whereSql.append(" group by count_date ");
        } else if ("week".equals(dto.getDateType())) {
            whereSql.append(" group by concat(from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 2),'~',from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 8)) ");
        } else if ("month".equals(dto.getDateType())) {
            whereSql.append(" group by substring(count_date, 1, 6) ");
        }
        selectSql.append(whereSql).append(" order by count_date");

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<CpcQueryKeywordReport> listByPageParam(Integer puid, QueryWordPageParam param) {
        String sql = "SELECT id,puid,shop_id,marketplace_id,campaign_id,ad_group_id,keyword_id,keyword_text,match_type,ad_group_name,campaign_name," +
                "`query`,sum(`cost`) cost, sum(`cost_rmb`) cost_rmb, sum(`cost_usd`) cost_usd," +
                "sum(`total_sales`) total_sales, sum(`total_sales_rmb`) total_sales_rmb, sum(`total_sales_usd`) total_sales_usd," +
                "sum(`ad_sales`) ad_sales,sum(`ad_sales_rmb`) ad_sales_rmb,sum(`ad_sales_usd`) ad_sales_usd," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`sale_num`) order_num, sum(`ad_order_num`) ad_order_num, sum(`sale_num`) sale_num," +
                "sum(`ad_sale_num`) ad_sale_num " +
                "FROM " + getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("campaign_id", param.getCampaignId())
                .equalTo("ad_group_id", param.getGroupId())
                .greaterThanOrEqualTo("count_date", param.getStartDate())
                .lessThanOrEqualTo("count_date", param.getEndDate());

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if (StringUtils.isNotBlank(param.getSearchType()) && "exact".equalsIgnoreCase(param.getSearchType())) {  // asin看板跳转精确搜索
                conditionBuilder.equalTo("`query`", param.getSearchValue());
            } else {
                conditionBuilder.like("query", param.getSearchValue());
            }
        }

        conditionBuilder.groupBy("keyword_id", "`query`");

        ConditionBuilder condition = conditionBuilder.build();
        sql += condition.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), condition.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<CpcQueryKeywordReport> listReports(Integer puid, Integer shopId, String startDate, String endDate, String keywordId, String query) {
        String sql = "SELECT puid,shop_id,marketplace_id,count_date,keyword_id," +
                "sum(`cost`) cost, sum(`cost_rmb`) cost_rmb, sum(`cost_usd`) cost_usd," +
                "sum(`total_sales`) total_sales, sum(`total_sales_rmb`) total_sales_rmb, sum(`total_sales_usd`) total_sales_usd," +
                "sum(`ad_sales`) ad_sales,sum(`ad_sales_rmb`) ad_sales_rmb,sum(`ad_sales_usd`) ad_sales_usd," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`sale_num`) order_num, sum(`ad_order_num`) ad_order_num, sum(`sale_num`) sale_num," +
                "sum(`ad_sale_num`) ad_sale_num " +
                "FROM " + getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate)
                .equalTo("keyword_id", keywordId)
                .equalTo("`query`", query)
                .groupBy("count_date")
                .build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AdHomePerformancedto> listAllReportsByDate(Integer puid, CpcQueryWordDto dto) {
        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));
        StringBuilder selectSql = new StringBuilder("select count_date, SUM(impressions) impressions,SUM(clicks) clicks," +
                "SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales")
                .append(" FROM ").append(tableName).append(" ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByDate(puid, dto, argsList);
        selectSql.append(whereSql);

        String unionTableName = getTableNameByStartDateAndTableName(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD), "t_cpc_query_targeting_report");
        StringBuilder selectTargetSql = new StringBuilder("select count_date, SUM(impressions) impressions,SUM(clicks) clicks," +
                "SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales")
                .append(" FROM ").append(unionTableName).append(" ");
        String whereTargetSql = getWhereTargetSqlCountByDate(puid, dto, argsList);
        selectTargetSql.append(whereTargetSql);

        StringBuilder sql = new StringBuilder("select count_date, SUM(impressions) impressions,SUM(clicks) clicks," +
                "SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales")
                .append(" FROM ( ");

        sql.append(selectSql).append(" union all ").append(selectTargetSql).append(" ) p ");

        Object[] args = argsList.toArray();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    return AdHomePerformancedto.builder()
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            .build();
                }
            }, args);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdHomePerformancedto> getReportKeywordByDate(Integer puid, CpcQueryWordDto dto) {
        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));
        StringBuilder sql = new StringBuilder("select keyword_id, count_date, SUM(impressions) impressions,SUM(clicks) clicks,")
                .append(" SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales,")
                .append(" SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`,SUM(order_num) sales_num ")
                .append(" FROM ").append(tableName).append(" ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByDate(puid, dto, argsList);
        sql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    return AdHomePerformancedto.builder()
                            .keywordId(re.getString("keyword_id"))
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .build();
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<CpcQueryKeywordReport> getReportKeywordByDate(Integer puid, Integer shopId, String start, String end) {
        String sql = "select * from " + getTableNameByStartDate(DateUtil.strToDate(start, DateUtil.PATTERN_YYYYMMDD)) + " where puid= ? and shop_id=? and `count_date` >= ? and `count_date` <= ? ";
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(shopId);
        args.add(start);
        args.add(end);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, args.toArray(), getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AdHomePerformancedto> getReportKeywordByKeywordIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> keywordIdList,
                                                                      CpcQueryWordDto dto) {
        if (CollectionUtils.isEmpty(keywordIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder("select count_date, SUM(impressions) impressions,SUM(clicks) clicks,")
                .append("SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales,")
                .append(" SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`,SUM(order_num) sales_num ")
                .append(" FROM " + getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)) + " where puid= ? and shop_id= ?  ");

        argsList.add(puid);
        argsList.add(shopId);

        sql.append(SqlStringUtil.dealInList("keyword_id", keywordIdList, argsList));
        //查询搜索词类型
        sql.append(" and query not REGEXP '" + ASIN_REGEX + "' ");

        //词根搜索
        if (CollectionUtils.isNotEmpty(dto.getQueryIds())) {
            sql.append(SqlStringUtil.dealInList("query_id", dto.getQueryIds(), argsList));
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryKeywordReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    sql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    if (dto.getListSearchValue().size() > 1) {
                        sql.append(SqlStringUtil.dealInList(field, dto.getListSearchValue(), argsList));
                    } else {
                        sql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getListSearchValue().get(0));
                    }
                }
            }
        }

        sql.append(" and count_date >= ? and count_date <= ? group by count_date ");
        argsList.add(startStr);
        argsList.add(endStr);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AdHomePerformancedto> getReportTargetByDate(Integer puid, CpcQueryWordDto dto) {
        StringBuilder sql = new StringBuilder("select target_id, count_date, SUM(impressions) impressions,SUM(clicks) clicks,")
                .append("SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales,")
                .append(" SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`,SUM(order_num) sales_num ")
                .append(" FROM ");
        sql.append(getTableNameByStartDateAndTableName(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD), "t_cpc_query_targeting_report")).append(" ");
        List<Object> argsList = Lists.newArrayList();
        String whereTargetSql = getWhereTargetSqlCountByDate(puid, dto, argsList);
        sql.append(whereTargetSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    return AdHomePerformancedto.builder()
                            .targetId(re.getString("target_id"))
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .build();
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }


    }

    @Override
    public List<AdHomePerformancedto> getReportTargetByTargetIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> targetIdList,
                                                                    CpcQueryWordDto dto) {
        if (CollectionUtils.isEmpty(targetIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        String tableName = getTableNameByStartDateAndTableName(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD), "t_cpc_query_targeting_report");
        //按天聚合
        StringBuilder sql = new StringBuilder("select  count_date, SUM(impressions) impressions,SUM(clicks) clicks,")
                .append(" SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales,")
                .append(" SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`,SUM(order_num) sales_num ")
                .append(" FROM ")
                .append(tableName)
                .append(" where puid= ? and shop_id= ? ");

        argsList.add(puid);
        argsList.add(shopId);

        sql.append(SqlStringUtil.dealInList("target_id", targetIdList, argsList));
        //查询搜索词类型
        sql.append(" and query not REGEXP '" + ASIN_REGEX + "' ");

        //词根搜索
        if (CollectionUtils.isNotEmpty(dto.getQueryIds())) {
            sql.append(SqlStringUtil.dealInList("query_id", dto.getQueryIds(), argsList));
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryTargetingReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    sql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    sql.append(" and ").append(field).append(" = ?");
                    argsList.add(dto.getSearchValue());
                }
            } else if ("keywordText".equalsIgnoreCase(dto.getSearchField())) {
                if (!dto.getSearchValue().equalsIgnoreCase("自动投放组")) {
                    sql.append(" and targeting_text = ? ");  // 需要查不出这个表的数据
                    argsList.add(dto.getSearchValue());
                }
            }
        }

        sql.append(" and count_date >= ? and count_date <= ? group by count_date ");
        argsList.add(startStr);
        argsList.add(endStr);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }


    }

    @Override
    public List<AdHomePerformancedto> getReportByKeywordIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> keywordIdList,
                                                                    CpcQueryWordDto dto) {
        if (CollectionUtils.isEmpty(keywordIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        String tableName = getTableNameByStartDateAndTableName(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD), this.getJdbcHelper().getTable());
        //按天聚合
        StringBuilder sql = new StringBuilder("select  count_date, SUM(impressions) impressions,SUM(clicks) clicks,")
                .append(" SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales,")
                .append(" SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`,SUM(order_num) sales_num ")
                .append(" FROM ")
                .append(tableName)
                .append(" where puid= ? and shop_id= ? ");

        argsList.add(puid);
        argsList.add(shopId);

        sql.append(SqlStringUtil.dealInList("keyword_id", keywordIdList, argsList));
        //查询搜索词类型
        sql.append(" and query REGEXP '" + ASIN_REGEX + "' ");

        //词根搜索
        if (CollectionUtils.isNotEmpty(dto.getQueryIds())) {
            sql.append(SqlStringUtil.dealInList("query_id", dto.getQueryIds(), argsList));
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryTargetingReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    sql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    sql.append(" and ").append(field).append(" = ?");
                    argsList.add(dto.getSearchValue());
                }
            } else if ("keywordText".equalsIgnoreCase(dto.getSearchField())) {
                if (!dto.getSearchValue().equalsIgnoreCase("自动投放组")) {
                    sql.append(" and targeting_text = ? ");  // 需要查不出这个表的数据
                    argsList.add(dto.getSearchValue());
                }
            }
        }

        sql.append(" and count_date >= ? and count_date <= ? group by count_date ");
        argsList.add(startStr);
        argsList.add(endStr);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<CpcQueryKeywordReport> getReportVoListByGroupIds(Integer puid, List<String> spGroupIds, QueryReportSearchVo searchVo) {
        String sql = "SELECT campaign_id,ad_group_id,campaign_name,ad_group_name,keyword_id,shop_id,marketplace_id,`query`,match_type,sum(`cost`) cost," +
                " sum(`total_sales`) total_sales, " +
                " sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`sale_num`) sale_num " +
                " FROM " + getTableNameByStartDate(searchVo.getStart()) + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", searchVo.getShopId())
                .greaterThanOrEqualTo("count_date", DateUtil.dateToStrWithFormat(searchVo.getStart(), "yyyyMMdd"))
                .lessThanOrEqualTo("count_date", DateUtil.dateToStrWithFormat(searchVo.getEnd(), "yyyyMMdd"))
                .inStrList("ad_group_id", spGroupIds.toArray(new String[]{}))
                .groupBy("keyword_id", "query")
                .build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<CpcQueryKeywordReport> getListByKeywordId(Integer puid, String keywordId, TargetQuerySearchVo searchVo) {
        String sql = "SELECT campaign_id,ad_group_id,campaign_name,ad_group_name,keyword_id,shop_id,marketplace_id,`query`,match_type,sum(`cost`) cost," +
                " sum(`total_sales`) total_sales, " +
                " sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`sale_num`) sale_num " +
                " FROM  " + getTableNameByStartDate(searchVo.getStart()) + "  where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", searchVo.getShopId())
                .greaterThanOrEqualTo("count_date", DateUtil.dateToStrWithFormat(searchVo.getStart(), "yyyyMMdd"))
                .lessThanOrEqualTo("count_date", DateUtil.dateToStrWithFormat(searchVo.getEnd(), "yyyyMMdd"))
                .equalTo("keyword_id", keywordId)
                .groupBy("query")
                .build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public CpcQueryKeywordReport getDetailsSumVo(Integer puid, QueryReportDetailsVo detailsVo) {
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,marketplace_id, ")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`sale_num`) sale_num,sum(`cost`) cost,")
                .append("sum(`total_sales`) total_sales  FROM ");
        selectSql.append(getTableNameByStartDate(DateUtil.strToDate(detailsVo.getStartDate(), DateUtil.PATTERN_YYYYMMDD))).append(" ");

        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and keyword_id= ? and `query`=? ");
        argsList.add(puid);
        argsList.add(detailsVo.getShopId());
        argsList.add(detailsVo.getTargetId());
        argsList.add(detailsVo.getQuery());

        whereSql.append("and count_date>=? and count_date<=?  ");
        argsList.add(detailsVo.getStartDate());
        argsList.add(detailsVo.getEndDate());
        selectSql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            List<CpcQueryKeywordReport> reportList = getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
            return CollectionUtils.isNotEmpty(reportList) ? reportList.get(0) : null;
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<CpcQueryKeywordReport> getListQueryDetailsDay(Integer puid, QueryReportDetailsVo detailsVo) {
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,marketplace_id,count_date, ")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`sale_num`) sale_num,sum(`cost`) cost,")
                .append("sum(`total_sales`) total_sales FROM ")
                .append(getTableNameByStartDate(detailsVo.getStart())).append(" ");

        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and keyword_id= ? and `query`=? ");
        argsList.add(puid);
        argsList.add(detailsVo.getShopId());
        argsList.add(detailsVo.getTargetId());
        argsList.add(detailsVo.getQuery());

        whereSql.append("and count_date>=? and count_date<=? group by count_date ");
        argsList.add(DateUtil.dateToStrWithFormat(detailsVo.getStart(), "yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(detailsVo.getEnd(), "yyyyMMdd"));
        selectSql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<CpcQueryKeywordReport> getNeedsLocalizationKeywords(Integer puid, Integer shopId, Integer limit) {
        return listByCondition(puid, new ConditionBuilder.Builder().equalTo("puid", puid)
                .equalTo("shop_id", shopId).equalToWithoutCheck("query_cn", "").limit(limit).build());
    }

    @Override
    public int updateQueryCnById(Integer puid, CpcQueryKeywordReport report) {
        //更新总表
        int i = updateQueryCnByIdOrigin(puid, report);
        if (nacosConfiguration.isHotTableWritePhase2Enable()) {
            //更新hot表
            updateQueryCnByIdHot(puid, report);
        }
        return i;

    }

    private int updateQueryCnByIdOrigin(Integer puid, CpcQueryKeywordReport report) {
        StringBuilder sql = new StringBuilder("update t_cpc_query_keyword_report " +
                "set query_cn = ?,update_time = NOW(3)  where id = ? and puid = ? and shop_id = ?");
        List<Object> args = Lists.newArrayList(report.getQueryCn(), report.getId(), report.getPuid(), report.getShopId());
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).update(sql.toString(), args.toArray());
        } finally {
            hintManager.close();
        }
    }

    private void updateQueryCnByIdHot(Integer puid, CpcQueryKeywordReport report) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getHotTableName());
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`campaign_id`,`ad_group_id`,")
                .append("`keyword_id`,`count_date`,`keyword_text`,`match_type`,`ad_group_name`,`campaign_name`,`query`, `query_cn`, `cost`,`cost_rmb`,")
                .append("`cost_usd`,`total_sales`,`total_sales_rmb`,`total_sales_usd`,`ad_sales`,`ad_sales_rmb`,`ad_sales_usd`, ")
                .append("`impressions`,`clicks`,`order_num`,`ad_order_num`,`sale_num`,`ad_sale_num`,`query_id`, `create_time`,`update_time` )VALUES");
        List<Object> argsList = Lists.newArrayList();
        sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now())");
        argsList.add(puid);
        argsList.add(report.getShopId());
        argsList.add(report.getMarketplaceId());
        argsList.add(report.getCampaignId());
        argsList.add(report.getAdGroupId());
        argsList.add(report.getKeywordId());
        argsList.add(report.getCountDate());
        argsList.add(report.getKeywordText());
        argsList.add(report.getMatchType());
        argsList.add(report.getAdGroupName());
        argsList.add(report.getCampaignName());
        argsList.add(report.getQuery());
        argsList.add(report.getQueryCn());
        argsList.add(report.getCost());
        argsList.add(report.getCostRmb());
        argsList.add(report.getCostUsd());
        argsList.add(report.getTotalSales());
        argsList.add(report.getTotalSalesRmb());
        argsList.add(report.getTotalSalesUsd());
        argsList.add(report.getAdSales());
        argsList.add(report.getAdSalesRmb());
        argsList.add(report.getAdSalesUsd());
        argsList.add(report.getImpressions());
        argsList.add(report.getClicks());
        argsList.add(report.getOrderNum());
        argsList.add(report.getAdOrderNum());
        argsList.add(report.getSaleNum());
        argsList.add(report.getAdSaleNum());
        argsList.add(report.getQueryId());

        sql.append(" on duplicate key update `query_cn`=values(query_cn), `update_time`=now(3)");
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } catch (Exception e) {
            logger.error("query keyword hot table update queryCn error", e);
        } finally {
            hintManager.close();
        }
    }


    @Override
    public List<AdQueryAutoRuleVo> getAutoRuleDataList(Integer puid, AutoRuleQueryWordDto dto) {

        StringBuilder sql = new StringBuilder("");

        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectKeywordSql = new StringBuilder("SELECT `query`,`query_cn`,'keyword' as type, keyword_id, '' as target_id,")
                .append(" keyword_text,match_type,'' as targeting_expression,'' as targeting_type,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ")
                .append(tableName).append(" ");
        List<Object> argList = new ArrayList<>();
        String whereSql = getAutoRuleWhereSqlCountByKeyword(puid, dto, argList);
        selectKeywordSql.append(whereSql);

        String unionTableName = getTableNameByStartDateAndTableName(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD), "t_cpc_query_targeting_report");
        StringBuilder selectTargetSql = new StringBuilder("SELECT `query`, `query_cn`,'target' as type,'' as keyword_id,  target_id, ")
                .append(" '' as keyword_text,'' as match_type,targeting_expression,targeting_type,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ")
                .append(unionTableName).append(" ");

        String whereSqlTarget = getAutoRuleWhereSqlCountByTargetId(puid, dto, argList);
        selectTargetSql.append(whereSqlTarget);


        sql.append(selectKeywordSql);
        sql.append(" UNION ALL ");
        sql.append(selectTargetSql);


        HintManager hintManager = HintManager.getInstance();
        try {

            Pattern compile = Pattern.compile(ASIN_REGEX);
            return getJdbcTemplate(puid, hintManager).query( sql.toString(), argList.toArray(), new RowMapper<AdQueryAutoRuleVo>() {
                @Override
                public AdQueryAutoRuleVo mapRow(ResultSet res, int i) throws SQLException {
                    AdQueryAutoRuleVo adQueryAutoRuleVo = AdQueryAutoRuleVo.builder()
                            .query(res.getString("query"))
                            .type(res.getString("type"))
                            .keywordId(res.getString("keyword_id"))
                            .targetId(res.getString("target_id"))
                            .keywordText(res.getString("keyword_text"))
                            .matchType(res.getString("match_type"))
                            .targetingExpression(res.getString("targeting_expression"))
                            .targetingType(res.getString("targeting_type"))
                            .adGroupId(res.getString("ad_group_id"))
                            .campaignId(res.getString("campaign_id"))
                            .impressions(res.getLong("impressions"))
                            .clicks(res.getLong("clicks"))
                            .cost(res.getBigDecimal("cost") != null ? res.getBigDecimal("cost") : BigDecimal.ZERO)
                            .totalSales(res.getBigDecimal("total_sales") != null ? res.getBigDecimal("total_sales") : BigDecimal.ZERO)
                            .saleNum(res.getInt("sale_num"))
                            .isAsin(compile.matcher(res.getString("query")).matches())
                            .adSale(res.getBigDecimal("total_sales") != null ? res.getBigDecimal("total_sales") : BigDecimal.ZERO)
                            .adOrderNum(res.getInt("sale_num"))
                            .orderNum(res.getInt("order_num"))
                            .build();
                    return adQueryAutoRuleVo;
                }
            });
        } finally {
            hintManager.close();
        }
    }

    @Override
    public Page<CpcQueryKeywordReport> listByAuoRule(AdQueryKeywordAutoRuleParam param, List<String> itemIds) {
        StringBuilder selectSqlAll = new StringBuilder();
        List<Object> argsList = Lists.newArrayList();
        StringBuilder selectSqlCount = new StringBuilder(" select count(*) from (");
        StringBuilder selectSql;
        if (AmazonAd.AdQueryTypeEnum.SB_QUERY.getQueryType().equals(param.getQueryType())) {
            selectSql = new StringBuilder(" select 'sb' adType, 'sbQuery' queryType, r.id, r.query_id, r.campaign_id, r.ad_group_id, r.query, r.puid, r.shop_id, r.marketplace_id, r.match_type, t.state, t.keyword_text as targetKeywordText " +
                    "from t_cpc_sb_query_keyword_report r join t_amazon_ad_keyword_sb t on r.puid = t.puid and r.shop_id = t.shop_id and r.marketplace_id = t.marketplace_id and r.keyword_id = t.keyword_id ");
            selectSql.append(this.listByAuoRuleWhere(param, itemIds, argsList));
            selectSql.append(" and r.match_type != 'theme' ");
            if (param.getMatchType() != null) {
                selectSql.append(" and r.match_type = ?");
                argsList.add(param.getMatchType());
            }

        } else if (AmazonAd.AdQueryTypeEnum.SP_QUERY.getQueryType().equals(param.getQueryType())) {
            selectSql = new StringBuilder(" select 'sp' adType, 'spQuery' queryType, id, query_id, campaign_id, ad_group_id, query, puid, shop_id, marketplace_id, match_type, state, serving_status as servingStatus, targetKeywordText from " +
                    "( select r.id, r.query_id, r.campaign_id, r.keyword_id, '' as target_id, t.ad_group_id, r.query, r.puid, r.shop_id, r.marketplace_id, r.match_type, t.state, t.serving_status, t.keyword_text as targetKeywordText from t_cpc_query_keyword_report r " +
                    "join t_amazon_ad_keyword t on r.puid = t.puid and r.shop_id = t.shop_id and r.marketplace_id = t.marketplace_id and r.keyword_id = t.keyword_id ");
            selectSql.append(this.listByAuoRuleWhere(param, itemIds, argsList));
            if (param.getMatchType() != null) {
                selectSql.append(" and r.match_type = ?");
                argsList.add(param.getMatchType());
            }
            selectSql.append(" and r.match_type != 'theme' ");
            selectSql.append(" and r.query not REGEXP '" + ASIN_REGEX + "' ");

            selectSql.append(" UNION ALL " +
                            " select r.id, r.query_id, r.campaign_id, '' as keyword_id, r.target_id, t.ad_group_id, r.query, r.puid, r.shop_id, r.marketplace_id, r.targeting_text as match_type, t.state, t.serving_status, '自动投放组' as targetKeywordText from t_cpc_query_targeting_report r " +
                            "join t_amazon_ad_targeting t on r.puid = t.puid and r.shop_id = t.shop_id and r.marketplace_id = t.marketplace_id and r.target_id = t.target_id ");
            selectSql.append(this.listByAuoRuleWhere(param, itemIds, argsList));
            if (param.getMatchType() != null) {
                selectSql.append(" and r.targeting_text = ?");
                argsList.add(param.getMatchType());
            }
            selectSql.append(" and r.targeting_text not like 'keyword-group=%' ");
            selectSql.append(" and r.query not REGEXP '" + ASIN_REGEX + "' ");

            selectSql.append(" ) u");
        } else {
            if (param.getMatchType() != null) {
                return new Page<>(param.getPageNo(), param.getPageSize());
            }

            selectSql = new StringBuilder("select 'sp' adType, 'spTargeting' queryType, id, query_id, campaign_id, ad_group_id, query, puid, shop_id, target_id targetId, marketplace_id, match_type, state, serving_status as servingStatus, targeting_type targetingType, targetKeywordText from " +
                    "( select r.id, r.query_id, r.campaign_id, t.ad_group_id, r.keyword_id, '' as target_id, r.query, r.puid, r.shop_id, r.marketplace_id, r.match_type, t.state, t.serving_status, '' as targeting_type, '' as targetKeywordText from t_cpc_query_keyword_report r " +
                    "join t_amazon_ad_keyword t on r.puid = t.puid and r.shop_id = t.shop_id and r.marketplace_id = t.marketplace_id and r.keyword_id = t.keyword_id");
            selectSql.append(this.listByAuoRuleWhere(param, itemIds, argsList));
            selectSql.append(" and t.match_type != 'theme' ");
            selectSql.append(" and r.query REGEXP '" + ASIN_REGEX + "' ");

            selectSql.append(" UNION ALL " +
                    " select r.id, r.query_id, r.campaign_id, t.ad_group_id, '' as keyword_id, r.target_id, r.query, r.puid, r.shop_id, r.marketplace_id, targeting_text as matchType, t.state, t.serving_status, r.targeting_type as targeting_type, r.targeting_expression as targetKeywordText from t_cpc_query_targeting_report r " +
                    "join t_amazon_ad_targeting t on r.puid = t.puid and r.shop_id = t.shop_id and r.marketplace_id = t.marketplace_id and r.target_id = t.target_id");
            selectSql.append(this.listByAuoRuleWhere(param, itemIds, argsList));
            selectSql.append(" and r.query REGEXP '" + ASIN_REGEX + "' ");
            selectSql.append(" and r.targeting_text not like 'keyword-group=%' ");
            selectSql.append(" ) u");
        }

        selectSql.append(" group by query_id ");
        selectSqlAll.append(selectSql);
        selectSqlCount.append(selectSql).append(")t");
        String sql = SqlStringUtil.exactSql(selectSqlAll.toString(), argsList);
        return getPageResult(param.getPuid(), param.getPageNo(), param.getPageSize(), String.valueOf(selectSqlCount), argsList.toArray(), String.valueOf(selectSqlAll), argsList.toArray(), CpcQueryKeywordReport.class);
    }

    private String listByAuoRuleWhere(AdQueryKeywordAutoRuleParam param, List<String> itemIds, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where r.puid = ? ");
        argsList.add(param.getPuid());

        if (param.getShopId() != null) {
            whereSql.append(" and r.shop_id = ?");
            argsList.add(param.getShopId());
        }
        if (param.getMarketplaceId() != null) {
            whereSql.append(" and r.marketplace_id = ?");
            argsList.add(param.getMarketplaceId());
        }

        if (StringUtils.isNotBlank(param.getHasSimilarRule())) {
            if (Integer.parseInt(param.getHasSimilarRule()) == AmazonAd.HasSimilarRuleEnum.YES.getHasSimilarRule()) {
                whereSql.append(SqlStringUtil.dealInList("r.query_id", param.getQueryIdList(), argsList));
            } else {
                whereSql.append(SqlStringUtil.dealNotInList("r.query_id", param.getQueryIdList(), argsList));
            }
        }

        //过滤模板关系
        if (CollectionUtils.isNotEmpty(itemIds)) {
            //过滤模板关系
            whereSql.append(SqlStringUtil.dealNotInList("r.query_id", itemIds, argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告活动查询
            whereSql.append(SqlStringUtil.dealInList("r.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getAdGroupIdList())) { //广告组查询
            whereSql.append(SqlStringUtil.dealInList("r.ad_group_id", param.getAdGroupIdList(), argsList));
        }
        if(CollectionUtils.isNotEmpty(param.getNotInAdGroupIdList())) {
            whereSql.append(SqlStringUtil.dealNotInList("r.ad_group_id", param.getNotInAdGroupIdList(), argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            whereSql.append(SqlStringUtil.dealInList("t.serving_status", param.getServingStatusList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getState())) {
            whereSql.append(" and t.state = ?");
            argsList.add(param.getState());
        }

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            whereSql.append(" and r.query like ?");
            argsList.add("%" + param.getSearchValue() + "%");
        }

        return whereSql.toString();
    }

    @Override
    public List<Long> getAllIdByPuidLimit(Integer puid, int start, int limit) {
        String sql = "select id from " + getJdbcHelper().getTable() + " where puid = ? and query_id = '' order by id limit ?, ?";
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql, Long.class, puid, start, limit);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public void updateQueryId(Integer puid, Long startId, Long endId) {
        String sql = "update " + getJdbcHelper().getTable() + " set query_id = md5(CONCAT(keyword_id, query)) where puid = ? and id >= ? and id <= ?";
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql, puid, startId, endId);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<CpcQueryKeywordReport> listSpByKeywordRule(AutoRuleObjectParam param, List<String> itemIdList) {
        StringBuilder selectSqlAll = new StringBuilder();
        List<Object> argsList = Lists.newArrayList();
        StringBuilder spKeywordWhereSql = new StringBuilder(" where u.puid = ? ");
//        StringBuilder spTargetWhereSql = new StringBuilder(" where u.puid = ? ");
//        StringBuilder sbKeywordWhereSql = new StringBuilder(" where u.puid = ? ");
        argsList.add(param.getPuid());
//        argsList.add(param.getPuid());
//        argsList.add(param.getPuid());
        StringBuilder selectSqlSpKeyword = new StringBuilder(" select 'sp' adType, 'spQuery' queryType, u.id, u.query_id, t.campaign_id, t.ad_group_id, u.campaign_name, u.ad_group_name,u.query, u.puid, u.shop_id, u.marketplace_id, u.match_type, t.state, t.serving_status as servingStatus, t.keyword_text as targetKeywordText from t_cpc_query_keyword_report u " +
                " left join t_amazon_ad_keyword t on u.puid = t.puid and u.shop_id = t.shop_id and u.keyword_id = t.keyword_id ");
//        StringBuilder selectSqlSpTargeting = new StringBuilder("  select 'sp' adType, 'spTargeting' queryType, u.id, u.query_id, u.campaign_id, u.ad_group_id, u.query, u.puid, u.shop_id, u.marketplace_id, u.targeting_type as matchType, t.state, t.serving_status as servingStatus, u.targeting_text as targetKeywordText from t_cpc_query_targeting_report u " +
//                " left join t_amazon_ad_targeting t on u.puid = t.puid and u.target_id = t.target_id  ");
//        StringBuilder selectSqlSb = new StringBuilder(" select 'sb' adType, 'sbQuery' queryType, u.id, u.query_id, u.campaign_id, u.ad_group_id, u.query, u.puid, u.shop_id, u.marketplace_id, u.match_type, t.state, '' as serving_status,t.keyword_text as targetKeywordText " +
//                "from t_cpc_sb_query_keyword_report u left join t_amazon_ad_keyword_sb t on u.puid = t.puid and u.keyword_id = t.keyword_id  ");
        if (StringUtils.isNotBlank(param.getMatchType())) {
            spKeywordWhereSql.append(" and u.match_type = ?");
            argsList.add(param.getMatchType());
        }
        if (param.getShopId() != null) {
            spKeywordWhereSql.append(" and u.shop_id = ?");
            argsList.add(param.getShopId());
        }

        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            spKeywordWhereSql.append(SqlStringUtil.dealInList(" t.serving_status", param.getServingStatusList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getState())) {
            spKeywordWhereSql.append(" and t.state = ?");
            argsList.add(param.getState());
        }

        //过滤模板关系
        if (CollectionUtils.isNotEmpty(itemIdList)) {
            //过滤模板关系
            spKeywordWhereSql.append(SqlStringUtil.dealInList("u.query_id", itemIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) { //广告活动查询
            spKeywordWhereSql.append(SqlStringUtil.dealInList("t.campaign_id", param.getCampaignIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIds())) { //广告组查询
            spKeywordWhereSql.append(SqlStringUtil.dealInList("t.ad_group_id", param.getGroupIds(), argsList));
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            spKeywordWhereSql.append(" and u.query like ? ");
            argsList.add("%" + param.getSearchValue() + "%");
        }
        selectSqlSpKeyword.append(spKeywordWhereSql);
        selectSqlAll.append(selectSqlSpKeyword);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(param.getPuid(), hintManager).query(selectSqlAll.toString(),argsList.toArray(),getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<CpcQueryKeywordReport> listSpKeywordReport(Integer puid, Integer shopId, Integer page) {
        StringBuilder selectSql = new StringBuilder("SELECT * from `t_cpc_query_keyword_report` ");
        List<Object> argsList = Lists.newArrayList();
        selectSql.append(" where query not REGEXP '" + Constants.ASIN_REGEX + "' ");
        if (page != null) {
            selectSql.append(" and puid=? and shop_id=? group by query_id order by update_time asc limit " + (page - 1) * LIMIT + "," + LIMIT);
        } else {
            selectSql.append(" and puid=? and shop_id=? group by query_id order by update_time desc limit " + LIMIT);
        }
        argsList.add(puid);
        argsList.add(shopId);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<CpcQueryKeywordReport> listSpKeywordReportByTimeRange(Integer puid, Integer shopId, Integer timeRange) {
        StringBuilder selectSql = new StringBuilder("SELECT * from " + getHotTableName());
        List<Object> argsList = Lists.newArrayList();
        selectSql.append(" where query not REGEXP '" + Constants.ASIN_REGEX + "' ");
        selectSql.append(" and puid=? and shop_id=? and update_time > ? group by query_id order by update_time desc limit " + LIMIT);
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(DateUtil.getDayByDaysAgo(timeRange, DateUtil.PATTERN_DATE_TIME));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> listQueryIdByQueryWordDto(CpcQueryWordDto dto) {
        StringBuilder sql = new StringBuilder("SELECT distinct `query_id` queryId FROM ( ");

        StringBuilder selectKeywordSql = new StringBuilder("SELECT `marketplace_id`, `query_id`, `query`,`query_cn`,'keyword' as type, keyword_id, '' as target_id,")
                .append(" keyword_text,match_type,'' as targeting_expression,'' as targeting_type,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM `t_cpc_query_keyword_report`");
        List<Object> argList = new ArrayList<>();
        String whereSql = getWhereSqlCountByKeyword(dto.getPuid(), dto, argList);
        selectKeywordSql.append(whereSql);


        StringBuilder selectTargetSql = new StringBuilder("SELECT `marketplace_id`, `query_id`, `query`, `query_cn`,'target' as type,'' as keyword_id,  target_id, ")
                .append(" '' as keyword_text,'' as match_type,targeting_expression,targeting_type,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM `t_cpc_query_targeting_report` ");

        String whereSqlTarget = getWhereSqlCountByTargetId(dto.getPuid(), dto, argList);
        selectTargetSql.append(whereSqlTarget);


        sql.append(selectKeywordSql);
        sql.append(" UNION ALL ");
        sql.append(selectTargetSql);
        sql.append(" ) c ");

        if (StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())) {
            String orderField = ReportService.getOrderField(dto.getOrderField(), false);
            if (StringUtils.isNotBlank(orderField)) {
                sql.append(" order by ").append(orderField);
                if ("desc".equals(dto.getOrderValue())) {
                    sql.append(" desc");
                }
                sql.append(" , query desc ");
            }
        }

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(dto.getPuid(), hintManager).queryForList(sql.toString(), argList.toArray(), String.class);
        } catch (Exception e) {
            logger.error("获取所有搜索词接口查询失败: ", e);
            throw e;
        } finally {
            hintManager.close();
        }
    }


    private String getAutoRuleWhereSqlCountByKeyword(int puid, AutoRuleQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder();

        //end
        whereSql.append(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        if(AddNegativeTargetType.keyword == dto.getTargetType()){
            whereSql.append(" and  query not REGEXP '" + ASIN_REGEX + "' ");
        } else if(AddNegativeTargetType.targeting == dto.getTargetType()) {
            whereSql.append(" and  query REGEXP '" + ASIN_REGEX + "' ");
        }

        if (StringUtils.isNotBlank(dto.getCampaignId())) {
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));

        }
        if (StringUtils.isNotBlank(dto.getGroupId())) {
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }

        if (StringUtils.isNotBlank(dto.getQueryId())) {
            List<String> list = StringUtil.splitStr(dto.getQueryId());
            whereSql.append(SqlStringUtil.dealInList("query_id", list, argsList));
            whereSql.append(" group by query_id ");
            whereSql.append(" having 1=1 ");
        } else {
            whereSql.append(" group by keyword_id,`query` ");
            whereSql.append(" having 1=1 ");
        }
        dto.getRuleParams().forEach(e->{
            argsList.add(e.getValue());
            if (RuleOperatorType.BE == e.getComparator()) {
                argsList.add(e.getAfterValue());
            }
            whereSql.append(assemblyQuery(e));
        });
        return whereSql.toString();
    }

    private String assemblyQuery(AutoRuleQueryWordRuleParam param){
        if(RuleStatisticalModeType.avg == param.getRuleStatisticalModeType()){
            if (RuleOperatorType.BE == param.getComparator()) {
                return " and ROUND(ifnull("+getField(param.getRuleIndex())+"/"+param.getDay()+ ",0),"+getFields(param.getRuleIndex())+") "+" "+ "BETWEEN ? AND "+" ? ";
            } else {
                return " and ROUND(ifnull("+getField(param.getRuleIndex())+"/"+param.getDay()+ ",0),"+getFields(param.getRuleIndex())+") "+param.getComparator().getValue() +" ? ";
            }
        }  else {
            if (RuleOperatorType.BE == param.getComparator()) {
                return " and "+getField(param.getRuleIndex())+" "+ "BETWEEN ? AND "+" ? ";
            } else {
                return " and "+getField(param.getRuleIndex())+" "+ " "+param.getComparator().getValue()+" ? ";
            }
        }
    }

    private String getField(RuleIndexType ruleIndex){
        if (ruleIndex == null ){
            return null;
        }
        switch (ruleIndex) {
            case cost:
                return "cost";
            case acos:
                return "ROUND(ifnull(cost/total_sales,0)*100,2)";
            case adOrderNum:
                return "sale_num";
            case orderNum:
                return "order_num";
            case adSale:
                return "total_sales";
            case conversionRate:
                return "ROUND(ifnull(sale_num/clicks,0)*100,2)";
            case roas:
                return "ROUND(ifnull(total_sales/cost,0),2)";
            case adImpressions:
                return "impressions";
            case clicks:
                return "clicks";
            case clickRate:
                return "ROUND(ifnull(clicks/impressions,0)*100,2)";
            case cpc:
                return "ROUND(ifnull(cost/clicks,0),2)";
            case cpa:
                return "ROUND(ifnull(cost/sale_num,0),2)";
            default:
                return null;
        }
    }

    private String getFields(RuleIndexType ruleIndex){
        if (ruleIndex == null ){
            return null;
        }
        switch (ruleIndex) {
            case cost:
                return "2";
            case acos:
                return "2";
            case adOrderNum:
                return "0";
            case orderNum:
                return "0";
            case adSale:
                return "2";
            case conversionRate:
                return "2";
            case roas:
                return "2";
            case adImpressions:
                return "0";
            case clicks:
                return "0";
            case clickRate:
                return "2";
            case cpc:
                return "2";
            case cpa:
                return "2";
            default:
                return "2";
        }
    }


    private String getAutoRuleWhereSqlCountByTargetId(int puid, AutoRuleQueryWordDto dto, List<Object> argsList) {

        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        if(AddNegativeTargetType.keyword == dto.getTargetType()){
            whereSql.append(" and  query not REGEXP '" + ASIN_REGEX + "' ");
        } else if(AddNegativeTargetType.targeting == dto.getTargetType()) {
            whereSql.append(" and  query REGEXP '" + ASIN_REGEX + "' ");
        }
        List<String> campaignList = StringUtil.splitStr(dto.getCampaignId());
        whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignList, argsList));
        List<String> groupList = StringUtil.splitStr(dto.getGroupId());
        whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupList, argsList));


        if (StringUtils.isNotBlank(dto.getQueryId())) {
            List<String> list = StringUtil.splitStr(dto.getQueryId());
            whereSql.append(SqlStringUtil.dealInList("query_id", list, argsList));
            whereSql.append(" group by query_id ");
            whereSql.append(" having 1=1 ");
        } else {
            whereSql.append(" group by target_id,`query` ");
            whereSql.append(" having 1=1 ");
        }
        dto.getRuleParams().forEach(e->{
            argsList.add(e.getValue());
            if (RuleOperatorType.BE == e.getComparator()) {
                argsList.add(e.getAfterValue());
            }
            whereSql.append(assemblyQuery(e));
        });
        return whereSql.toString();

    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append(" and shop_id = ? ");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> listQueryId(ProcessTaskParam param) {
        StringBuilder selectSqlAll = new StringBuilder();
        List<Object> argsList = Lists.newArrayList();
        StringBuilder spKeywordWhereSql = new StringBuilder(" where u.puid = ? ");
        argsList.add(param.getPuid());
        StringBuilder selectSqlSpKeyword = new StringBuilder(" select u.query_id from t_cpc_query_keyword_report u " +
                " left join t_amazon_ad_keyword t on u.puid = t.puid and u.shop_id = t.shop_id and u.keyword_id = t.keyword_id ");
        if (StringUtils.isNotBlank(param.getMatchType())) {
            spKeywordWhereSql.append(" and u.match_type = ?");
            argsList.add(param.getMatchType());
        }
        if (param.getShopId() != null) {
            spKeywordWhereSql.append(" and u.shop_id = ?");
            argsList.add(param.getShopId());
        }

        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            spKeywordWhereSql.append(SqlStringUtil.dealInList(" t.serving_status", param.getServingStatusList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getState())) {
            spKeywordWhereSql.append(" and t.state = ?");
            argsList.add(param.getState());
        }

        //过滤模板关系
        if (CollectionUtils.isNotEmpty(param.getItemIdList())) {
            //过滤模板关系
            spKeywordWhereSql.append(SqlStringUtil.dealInList("u.query_id", param.getItemIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) { //广告活动查询
            spKeywordWhereSql.append(SqlStringUtil.dealInList("t.campaign_id", param.getCampaignIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIds())) { //广告组查询
            spKeywordWhereSql.append(SqlStringUtil.dealInList("t.ad_group_id", param.getGroupIds(), argsList));
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            spKeywordWhereSql.append(" and u.query like ? ");
            argsList.add("%" + param.getSearchValue() + "%");
        }
        selectSqlSpKeyword.append(spKeywordWhereSql);
        selectSqlAll.append(selectSqlSpKeyword);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(param.getPuid(), hintManager).queryForList(selectSqlAll.toString(), argsList.toArray(), String.class);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<SearchQueryTagParam> listAdGroupIdByQueryWordDto(CpcQueryWordDto dto) {
        StringBuilder sql = new StringBuilder("SELECT `ad_group_id` adGroupId, query  query FROM ( ");

        StringBuilder selectKeywordSql = new StringBuilder("SELECT `marketplace_id`, `query_id`, `query`,`query_cn`,'keyword' as type, keyword_id, '' as target_id,")
                .append(" keyword_text,match_type,'' as targeting_expression,'' as targeting_type,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM `t_cpc_query_keyword_report`");
        List<Object> argList = new ArrayList<>();
        String whereSql = getWhereSqlCountByKeyword(dto.getPuid(), dto, argList);
        selectKeywordSql.append(whereSql);


        StringBuilder selectTargetSql = new StringBuilder("SELECT `marketplace_id`, `query_id`, `query`, `query_cn`,'target' as type,'' as keyword_id,  target_id, ")
                .append(" '' as keyword_text,'' as match_type,targeting_expression,targeting_type,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM `t_cpc_query_targeting_report` ");

        String whereSqlTarget = getWhereSqlCountByTargetId(dto.getPuid(), dto, argList);
        selectTargetSql.append(whereSqlTarget);


        sql.append(selectKeywordSql);
        sql.append(" UNION ALL ");
        sql.append(selectTargetSql);
        sql.append(" ) c ");

        if (StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())) {
            String orderField = ReportService.getOrderField(dto.getOrderField(), false);
            if (StringUtils.isNotBlank(orderField)) {
                sql.append(" order by ").append(orderField);
                if ("desc".equals(dto.getOrderValue())) {
                    sql.append(" desc");
                }
                sql.append(" , query desc ");
            }
        }

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(dto.getPuid(), hintManager).query(sql.toString(), argList.toArray(), new BeanPropertyRowMapper<>(SearchQueryTagParam.class));
        } catch (Exception e) {
            logger.error("获取所有搜索词接口查询失败: ", e);
            throw e;
        } finally {
            hintManager.close();
        }
    }


    @Override
    public List<CpcQueryKeywordReport> getDetailList(Integer puid, CpcQueryWordDetailDto dto) {
        StringBuilder selectSql = new StringBuilder(" SELECT ");

        selectSql.append(" count_date,");
        selectSql.append("impressions,clicks,cost,sale_num as sale_num,ad_order_num,order_num,total_sales ");


        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));
        selectSql.append(" FROM ").append(tableName).append(" ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        whereSql.append(" and keyword_id=? and `query`=? ");
        argsList.add(dto.getKeywordId());
        argsList.add(dto.getQuery());
        selectSql.append(whereSql);
        Object[] args = argsList.toArray();
        HintManager hintManager = HintManager.getInstance();
        try {
            return this.getJdbcTemplate(puid, hintManager).query(selectSql.toString(), getMapper(), args);
        } catch (Exception e) {
            logger.error("获取所有搜索词接口查询失败: ", e);
            throw e;
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<WordBo> listWordBo(Integer puid, Integer shopId, Integer page) {
        StringBuilder selectSql = new StringBuilder("SELECT id wordId, query word from " + getJdbcHelper().getTable() + " use index (uk_puid_shop_mk_kw_qy_cd) ");
        List<Object> argsList = Lists.newArrayList();
        if (page != null) {
            selectSql.append(" where puid=? and shop_id=? and count_date >= ? and query not REGEXP '" + Constants.ASIN_REGEX + "' order by id desc limit " + (page - 1) * dynamicRefreshConfiguration.getWordRootCalculateLimit() + "," + dynamicRefreshConfiguration.getWordRootCalculateLimit());
        } else {
            selectSql.append(" where puid=? and shop_id=? and count_date >= ? and query not REGEXP '" + Constants.ASIN_REGEX + "' order by id desc limit " + dynamicRefreshConfiguration.getWordRootCalculateLimit());
        }
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(DateUtil.dateToStrWithTime(DateUtil.addDay(new Date(), dynamicRefreshConfiguration.getWordRootCalculateDateBeforeLimit()), DateUtil.PATTERN_YYYYMMDD));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordBo.class));
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<WordBo> listWordBoTimeRange(Integer puid, Integer shopId, Integer timeRange) {
        StringBuilder selectSql = new StringBuilder("SELECT id wordId, query word from " + getJdbcHelper().getTable() + " use index (uk_puid_shop_mk_kw_qy_cd) ");
        List<Object> argsList = Lists.newArrayList();
        selectSql.append(" where puid=? and shop_id=? and count_date >= ? and update_time > ? and query not REGEXP '" + Constants.ASIN_REGEX + "' order by id desc limit " + Constants.WORD_ROOT_QUERY_INCREMENT_LIMIE);
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(DateUtil.dateToStrWithTime(DateUtil.addDay(new Date(), dynamicRefreshConfiguration.getWordRootCalculateDateBeforeLimit()), DateUtil.PATTERN_YYYYMMDD));
        argsList.add(DateUtil.getDayByDaysAgo(timeRange, DateUtil.PATTERN_DATE_TIME));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordBo.class));
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<CpcQueryKeywordReport> wordListByIds(int puid, Integer shopId, List<Long> ids) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select id, puid, shop_id, query_id, query, query_cn, keyword_id, keyword_text, marketplace_id, campaign_id, ad_group_id, count_date, match_type, ")
                .append(" cost, total_sales, ad_sales, impressions, clicks, order_num, ad_order_num, sale_num, ad_sale_num from ")
                .append(this.getJdbcHelper().getTable());
        sql.append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        sql.append(SqlStringUtil.dealInList("id", ids, argsList));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(CpcQueryKeywordReport.class));
        } finally {
            hintManager.close();
        }
    }

}