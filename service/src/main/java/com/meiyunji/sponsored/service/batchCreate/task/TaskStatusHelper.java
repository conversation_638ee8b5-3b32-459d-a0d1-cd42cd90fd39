package com.meiyunji.sponsored.service.batchCreate.task;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.batchCreate.dao.*;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdLevelStatusEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdTaskLevelEnum;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.*;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.stream.enums.AmazonStreamTaskTypeEnum;
import com.meiyunji.sponsored.service.stream.enums.StreamConstants;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamTaskRetryService;
import com.meiyunji.sponsored.service.syncTask.management.strgtegy.enume.AdProductEnum;
import com.meiyunji.sponsored.service.syncTask.message.ManagementCampaignStreamMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-11-22  20:09
 */

@Component
@Slf4j
public class TaskStatusHelper {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private CpcCampaignApiService cpcCampaignApiService;

    @Autowired
    private CpcAdGroupApiService cpcAdGroupApiService;

    @Autowired
    private CpcAdProductApiService cpcAdProductApiService;

    @Autowired
    private CpcKeywordsApiService keywordsApiService;

    @Autowired
    private CpcTargetingApiService targetingApiService;

    @Autowired
    private CpcNeKeywordsApiService neKeywordsApiService;

    @Autowired
    private IAmazonAdBatchCampaignDao amazonAdBatchCampaignDao;

    @Autowired
    private IAmazonAdBatchGroupDao amazonAdBatchGroupDao;

    @Autowired
    private IAmazonAdBatchProductDao amazonAdBatchProductDao;

    @Autowired
    private IAmazonAdBatchKeywordDao amazonAdBatchKeywordDao;

    @Autowired
    private IAmazonAdBatchTargetingDao amazonAdBatchTargetingDao;

    @Autowired
    private IAmazonAdBatchNekeywordDao amazonAdBatchNekeywordDao;

    @Autowired
    private IAmazonAdBatchNetargetingDao amazonAdBatchNetargetingDao;

    @Autowired
    private IAmazonManagementStreamTaskRetryService amazonManagementStreamTaskRetryService;


    /**
     * 批量修改当前层级任务和层级下的任务都为失败状态
     * @param puid
     * @param taskId
     * @param idErrMsgMap
     * @param taskLevelEnum 当前层级枚举
     * @param initialErrMsg 下层报错信息
     * @param updateExecuteCount 是否更新执行次数
     */
    public void batchUpdateErrStatus(Integer puid, Long taskId, Map<Long, String> idErrMsgMap, SpBatchCreateAdTaskLevelEnum taskLevelEnum, String initialErrMsg, boolean updateExecuteCount) {
        if (MapUtils.isEmpty(idErrMsgMap)) {
            return;
        }
        switch (taskLevelEnum) {
            case LEVEL_CAMPAIGN:
                amazonAdBatchCampaignDao.updateErrTaskStatusByIdList(puid, idErrMsgMap, updateExecuteCount);
                //查出广告组id
                idErrMsgMap = amazonAdBatchGroupDao.listIdByCampaignId(puid, new ArrayList<>(idErrMsgMap.keySet()))
                        .stream().collect(Collectors.toMap(e -> e, e -> initialErrMsg));
                this.batchUpdateErrStatus(puid, taskId, idErrMsgMap, SpBatchCreateAdTaskLevelEnum.LEVEL_GROUP, initialErrMsg, false);
                break;
            case LEVEL_GROUP:
                List<Long> groupIdList = new ArrayList<>(idErrMsgMap.keySet());
                amazonAdBatchGroupDao.updateErrTaskStatusByIdList(puid, idErrMsgMap, updateExecuteCount);
                //查出广告产品id
                List<Long> productIds = amazonAdBatchProductDao.listIdByGroupIdList(puid, taskId, groupIdList);
                idErrMsgMap = productIds.stream().collect(Collectors.toMap(e -> e, e -> initialErrMsg));
                this.batchUpdateErrStatus(puid, taskId, idErrMsgMap, SpBatchCreateAdTaskLevelEnum.LEVEL_PRODUCT, initialErrMsg, false);
                //查出关键词id
                List<Long> keywordIds = amazonAdBatchKeywordDao.listIdByGroupIdList(puid, taskId, groupIdList);
                idErrMsgMap = keywordIds.stream().collect(Collectors.toMap(e -> e, e -> initialErrMsg));
                this.batchUpdateErrStatus(puid, taskId, idErrMsgMap, SpBatchCreateAdTaskLevelEnum.LEVEL_KEYWORD, initialErrMsg, false);
                //查出商品投放和自动投放id
                List<Long> targetingIds = amazonAdBatchTargetingDao.listIdByGroupIdList(puid, taskId, groupIdList, null);
                idErrMsgMap = targetingIds.stream().collect(Collectors.toMap(e -> e, e -> initialErrMsg));
                this.batchUpdateErrStatus(puid, taskId, idErrMsgMap, SpBatchCreateAdTaskLevelEnum.LEVEL_TARGETING, initialErrMsg, false);
                //查出否定关键词id
                List<Long> nekeywordIds = amazonAdBatchNekeywordDao.listIdByGroupIdList(puid, taskId, groupIdList);
                idErrMsgMap = nekeywordIds.stream().collect(Collectors.toMap(e -> e, e -> initialErrMsg));
                this.batchUpdateErrStatus(puid, taskId, idErrMsgMap, SpBatchCreateAdTaskLevelEnum.LEVEL_NEKEYWORD, initialErrMsg, false);
                //查出否定投放id
                List<Long> netargetingIds = amazonAdBatchNetargetingDao.listIdByGroupIdList(puid, taskId, groupIdList);
                idErrMsgMap = netargetingIds.stream().collect(Collectors.toMap(e -> e, e -> initialErrMsg));
                this.batchUpdateErrStatus(puid, taskId, idErrMsgMap, SpBatchCreateAdTaskLevelEnum.LEVEL_NETATRGETING, initialErrMsg, false);
                break;
            case LEVEL_PRODUCT:
                amazonAdBatchProductDao.updateErrTaskStatusByIdList(puid, idErrMsgMap, updateExecuteCount);
                break;
            case LEVEL_KEYWORD:
                amazonAdBatchKeywordDao.updateErrTaskStatusByIdList(puid, idErrMsgMap, updateExecuteCount);
                break;
            case LEVEL_TARGETING:
                amazonAdBatchTargetingDao.updateErrTaskStatusByIdList(puid, idErrMsgMap, updateExecuteCount);
                break;
            case LEVEL_NEKEYWORD:
                amazonAdBatchNekeywordDao.updateErrTaskStatusByIdList(puid, idErrMsgMap, updateExecuteCount);
                break;
            case LEVEL_NETATRGETING:
                amazonAdBatchNetargetingDao.updateErrTaskStatusByIdList(puid, idErrMsgMap, updateExecuteCount);
                break;
        }
    }

    public void batchSyncAmazonStatus(Integer puid, Integer shopId, Long taskId, List<Long> idList, List<String> amazonIdList, AdProductEnum adProductEnum,
                                      SpBatchCreateAdTaskLevelEnum taskLevelEnum) {
        if (CollectionUtils.isEmpty(idList) || CollectionUtils.isEmpty(amazonIdList)) {
            return;
        }
        switch (taskLevelEnum) {
            case LEVEL_CAMPAIGN:
                try {
                    List<List<String>> partition = Lists.partition(amazonIdList, StreamConstants.SP_MAX_CAMPAIGN_IDS_COUNT);
                    for (List<String> ids : partition) {//需要拆分,防止插入重试表中字段过长
                        amazonManagementStreamTaskRetryService.saveTaskRetry(puid, shopId, adProductEnum,
                                AmazonStreamTaskTypeEnum.CAMPAIGN, ids);
                    }
                } catch (Exception e) {//向同步任务表中插入失败，直接调用接口进行同步
                    log.error("insert sp campaign to stream task retry table error, begin sync by api:{}, taskId:{}", e.getMessage(), taskId);
                    ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
                    cpcCampaignApiService.syncCampaigns(shopAuth, StringUtils.join(amazonIdList, ","), false, true);
                }
                //查出广告组id
                Map<Long, String> groupAmazonIdMap = amazonAdBatchGroupDao.listIdByTaskIdAndCampaignIdAndStatus(puid, shopId,
                        taskId,  idList, Collections.singletonList((int) SpBatchCreateAdLevelStatusEnum.SUCCESS.getCode()));
                this.batchSyncAmazonStatus(puid, shopId, taskId, new ArrayList<>(groupAmazonIdMap.keySet()),
                        new ArrayList<>(groupAmazonIdMap.values()), adProductEnum, SpBatchCreateAdTaskLevelEnum.LEVEL_GROUP);
                break;
            case LEVEL_GROUP:
                try {
                    List<List<String>> partition = Lists.partition(amazonIdList, StreamConstants.SP_MAX_GROUP_IDS_COUNT);
                    for (List<String> ids : partition) {//需要拆分,防止插入重试表中字段过长
                        amazonManagementStreamTaskRetryService.saveTaskRetry(puid, shopId, adProductEnum,
                                AmazonStreamTaskTypeEnum.GROUP, ids);
                    }
                } catch (Exception e) {//向同步任务表中插入失败，直接调用接口进行同步
                    log.error("insert sp group to stream task retry table error, begin sync by api:{}, taskId:{}", e.getMessage(), taskId);
                    ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
                    cpcAdGroupApiService.syncAdGroups(shopAuth, null, StringUtils.join(amazonIdList, ","), true);
                }

                //查出广告产品id
                Map<Long, String> productAmazonIdList = amazonAdBatchProductDao.listIdByGroupIdListAndStatus(puid, shopId,
                        taskId, idList, Collections.singletonList(SpBatchCreateAdLevelStatusEnum.SUCCESS.getCode()));
                this.batchSyncAmazonStatus(puid, shopId, taskId, new ArrayList<>(productAmazonIdList.keySet()),
                        new ArrayList<>(productAmazonIdList.values()), adProductEnum, SpBatchCreateAdTaskLevelEnum.LEVEL_PRODUCT);

                //查出关键词id
                Map<Long, String> keywordAmazonIdList = amazonAdBatchKeywordDao.listIdByGroupIdListAndStatus(puid, taskId, idList,
                        Collections.singletonList((int)SpBatchCreateAdLevelStatusEnum.SUCCESS.getCode()));
                this.batchSyncAmazonStatus(puid, shopId, taskId, new ArrayList<>(keywordAmazonIdList.keySet()),
                        new ArrayList<>(keywordAmazonIdList.values()), adProductEnum, SpBatchCreateAdTaskLevelEnum.LEVEL_KEYWORD);

                //查出商品投放和自动投放id
                Map<Long, String> targetingAmazonIdList = amazonAdBatchTargetingDao.listIdByGroupIdListAndStatus(puid, taskId, idList,
                        Collections.singletonList((int)SpBatchCreateAdLevelStatusEnum.SUCCESS.getCode()));
                this.batchSyncAmazonStatus(puid, shopId, taskId, new ArrayList<>(targetingAmazonIdList.keySet()),
                        new ArrayList<>(targetingAmazonIdList.values()), adProductEnum, SpBatchCreateAdTaskLevelEnum.LEVEL_TARGETING);

                //查出否定关键词id
                Map<Long, String> neKeywordAmazonIdList = amazonAdBatchNekeywordDao.listIdByGroupIdListAndStatus(puid, taskId, idList,
                        Collections.singletonList((int)SpBatchCreateAdLevelStatusEnum.SUCCESS.getCode()));
                this.batchSyncAmazonStatus(puid, shopId, taskId, new ArrayList<>(neKeywordAmazonIdList.keySet()),
                        new ArrayList<>(neKeywordAmazonIdList.values()), adProductEnum, SpBatchCreateAdTaskLevelEnum.LEVEL_NEKEYWORD);

                //查出否定投放id
                Map<Long, String> netargetingAmazonIds = amazonAdBatchNetargetingDao.listIdByGroupIdListAndStatus(puid, taskId, idList,
                        Collections.singletonList((int)SpBatchCreateAdLevelStatusEnum.SUCCESS.getCode()));
                this.batchSyncAmazonStatus(puid, shopId, taskId, new ArrayList<>(netargetingAmazonIds.keySet()),
                        new ArrayList<>(netargetingAmazonIds.values()), adProductEnum, SpBatchCreateAdTaskLevelEnum.LEVEL_NETATRGETING);
                break;
            case LEVEL_PRODUCT:
                try {
                    List<List<String>> partition = Lists.partition(amazonIdList, StreamConstants.SP_MAX_AD_IDS_COUNT);
                    for (List<String> ids : partition) {//需要拆分,防止插入重试表中字段过长
                        amazonManagementStreamTaskRetryService.saveTaskRetry(puid, shopId, adProductEnum,
                                AmazonStreamTaskTypeEnum.AD, ids);
                    }
                } catch (Exception e) {//向同步任务表中插入失败，直接调用接口进行同步
                    log.error("insert sp ad to stream task retry table error, begin sync by api:{}, taskId:{}", e.getMessage(), taskId);
                    ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
                    cpcAdProductApiService.syncAds(shopAuth, null, null, StringUtils.join(amazonIdList, ","), true);
                }
                break;
            case LEVEL_KEYWORD:
                try {
                    List<List<String>> partition = Lists.partition(amazonIdList, StreamConstants.SP_MAX_KEYWORD_IDS_COUNT);
                    for (List<String> ids : partition) {//需要拆分,防止插入重试表中字段过长
                        amazonManagementStreamTaskRetryService.saveTaskRetry(puid, shopId, adProductEnum,
                                AmazonStreamTaskTypeEnum.KEYWORD, ids);
                    }
                } catch (Exception e) {
                    log.error("insert sp keyword to stream task retry table error, begin sync by api:{}, taskId:{}", e.getMessage(), taskId);
                    ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
                    keywordsApiService.syncKeywords(shopAuth, null, null, StringUtils.join(amazonIdList, ","), true);
                }
                break;
            case LEVEL_TARGETING:
                try {
                    List<List<String>> partition = Lists.partition(amazonIdList, StreamConstants.SP_MAX_TARGET_IDS_COUNT);
                    for (List<String> ids : partition) {//需要拆分,防止插入重试表中字段过长
                        amazonManagementStreamTaskRetryService.saveTaskRetry(puid, shopId, adProductEnum,
                                AmazonStreamTaskTypeEnum.TARGET, ids);
                    }
                } catch (Exception e) {
                    log.error("insert sp targeting to stream task retry table error, begin sync by api:{}, taskId:{}", e.getMessage(), taskId);
                    ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
                    targetingApiService.syncTargetings(shopAuth, null, null, StringUtils.join(amazonIdList, ","), true);
                }
                break;
            case LEVEL_NEKEYWORD:
                try {
                    List<List<String>> partition = Lists.partition(amazonIdList, StreamConstants.SP_MAX_NE_KEYWORD_IDS_COUNT);
                    for (List<String> ids : partition) {//需要拆分,防止插入重试表中字段过长
                        amazonManagementStreamTaskRetryService.saveTaskRetry(puid, shopId, adProductEnum,
                                AmazonStreamTaskTypeEnum.NE_KEYWORD, ids);
                    }
                } catch (Exception e) {
                    log.error("insert sp neKeyword to stream task retry table error, begin sync by api:{}, taskId:{}", e.getMessage(), taskId);
                    ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
                    neKeywordsApiService.syncNeKeywords(shopAuth, null, null, StringUtils.join(amazonIdList, ","), true);
                }
                break;
            case LEVEL_NETATRGETING:
                try {
                    List<List<String>> partition = Lists.partition(amazonIdList, StreamConstants.SP_MAX_NE_TARGET_IDS_COUNT);
                    for (List<String> ids : partition) {//需要拆分,防止插入重试表中字段过长
                        amazonManagementStreamTaskRetryService.saveTaskRetry(puid, shopId, adProductEnum,
                                AmazonStreamTaskTypeEnum.NE_TARGET, ids);
                    }
                } catch (Exception e) {
                    log.error("insert sp neTargeting to stream task retry table error, begin sync by api:{}, taskId:{}", e.getMessage(), taskId);
                    ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
                    targetingApiService.syncNeTargetings(shopAuth, null, null, StringUtils.join(amazonIdList, ","), true);
                }
                break;
        }
    }

    /**
     * 广告组下广告产品失败时，将投放全部改为失败状态
     * @param puid
     * @param groupIdList
     */
    public void batchUpdateUnderProductErrStatus(Integer puid, Long taskId, List<Long> groupIdList) {
        String msg = "广告组下所有广告产品创建失败";
        List<Long> ids;
        Map<Long, String> idErrMsgMap;
        ids = amazonAdBatchKeywordDao.listIdByGroupIdList(puid, taskId, groupIdList);
        if (CollectionUtils.isNotEmpty(ids)) {
            idErrMsgMap = ids.stream().collect(Collectors.toMap(e -> e, e -> msg));
            amazonAdBatchKeywordDao.updateErrTaskStatusByIdList(puid, idErrMsgMap, false);
        }

        //只查出商品投放id
        ids = amazonAdBatchTargetingDao.listIdByGroupIdList(puid, taskId, groupIdList, Constants.MANUAL);
        if (CollectionUtils.isNotEmpty(ids)) {
            idErrMsgMap = ids.stream().collect(Collectors.toMap(e -> e, e -> msg));
            amazonAdBatchTargetingDao.updateErrTaskStatusByIdList(puid, idErrMsgMap, false);
        }

        ids = amazonAdBatchNekeywordDao.listIdByGroupIdList(puid, taskId, groupIdList);
        if (CollectionUtils.isNotEmpty(ids)) {
            idErrMsgMap = ids.stream().collect(Collectors.toMap(e -> e, e -> msg));
            amazonAdBatchNekeywordDao.updateErrTaskStatusByIdList(puid, idErrMsgMap, false);
        }

        ids = amazonAdBatchNetargetingDao.listIdByGroupIdList(puid, taskId, groupIdList);
        if (CollectionUtils.isNotEmpty(ids)) {
            idErrMsgMap = ids.stream().collect(Collectors.toMap(e -> e, e -> msg));
            amazonAdBatchNetargetingDao.updateErrTaskStatusByIdList(puid, idErrMsgMap, false);
        }
    }
}
