package com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.impl;

import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.asinInfoQuery.AllAsinListResponseVo;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.asinInfoQuery.AsinListResponseVo;
import com.meiyunji.sponsored.service.account.bo.ShopAuthBo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchProductDao;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdLevelStatusEnum;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchProduct;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdsDao;
import com.meiyunji.sponsored.service.cpc.dto.CampaignTypeDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAds;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.hotdata.HotDataTypeEnum;
import com.meiyunji.sponsored.service.hotdata.dao.IAmazonShopHotDataDao;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AsinOrderNumDto;
import com.meiyunji.sponsored.service.hotdata.po.AmazonShopHotData;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.IAsinInfoQueryService;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.*;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-08-31  20:38
 */

@Service
@Slf4j
public class AsinInfoQueryServiceImpl implements IAsinInfoQueryService {

    @Autowired
    private IAmazonAdProductReportDao amazonAdProductReportDao;

    @Autowired
    private IAmazonAdBatchProductDao amazonAdBatchProductDao;

    @Autowired
    private IAmazonShopHotDataDao amazonShopHotDataDao;

    @Autowired
    private DynamicRefreshNacosConfiguration refreshNacosConfiguration;

    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;

    @Autowired
    private IAmazonSbAdsDao amazonSbAdsDao;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IOdsAmazonAdProductDao odsAmazonAdProductDao;

    @Autowired
    private IOdsProductDao odsProductDao;
    @Autowired
    private IOdsAmazonAdProductReportDao odsAmazonAdProductReportDao;
    @Autowired
    private IVcShopAuthDao vcShopAuthDao;

    private static final List<String> marketplaceIdList = Arrays.stream(AmznEndpoint.values()).map(AmznEndpoint::getMarketplaceId).collect(Collectors.toList());

    private static final int LAST_7_DAY = 6;
    private static final int LAST_30_DAY = 29;

    private static final int DEFAULT_INIT_ASIN_SHOP_PARTITION = 30;

    @Override
    public InitAsinVo getInitAsinInfo(InitAsinInfoReqVo reqVo) {
        log.info("产品透视分析--店铺asin初始化 puid {} ,参数 {}", reqVo.getPuid(), JSONUtil.objectToJson(reqVo));
        //以asin为维度，查询最近7天订单量最高的asin，
        LocalDateTime now = LocalDateTime.now();
        InitAsinVo asinInfo;
        asinInfo = getInitAsinInfoLast7Day(reqVo, now);
        if (Objects.nonNull(asinInfo)) {
            return asinInfo;
        }
        //如果近7天没数据，则从近30天随机取一个
        asinInfo = getRandomLast30Day(reqVo, now);
        return asinInfo;
    }

    @Override
    public AsinListResponseVo getAsinList(AsinListReqVo reqVo) {
        String uuid = UUID.randomUUID().toString();
        log.info("产品透视分析{}--asin列表查询-参数 {}", uuid, JSONUtil.objectToJson(reqVo));
        long t = Instant.now().toEpochMilli();

        //查询asin列表
        //todo 数据源需要从报告表改成在线产品表
        Page<AsinListDto> page = amazonAdProductReportDao.getAsinPageByPuidAndShopId(reqVo);
        log.info("产品透视分析{}--asin列表查询-花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        //封装数据
        //获取店铺名称
        List<AsinListDto> rows = page.getRows();
        Map<Integer, String> shopIdNameMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(rows)) {
            shopIdNameMap = shopAuthDao.getShopAuthBoByIds(reqVo.getPuid(), rows.stream().map(AsinListDto::getShopId).collect(Collectors.toList()))
                    .stream().collect(Collectors.toMap(ShopAuthBo::getId, ShopAuthBo::getName));
        }

        //asin集合
        List<AsinListResponseVo.AsinListVo> asinList = new ArrayList<>();
        AsinListResponseVo.AsinListVo.Builder asinBuilder = null;
        for (AsinListDto asinListDto : page.getRows()) {
            asinBuilder = AsinListResponseVo.AsinListVo.newBuilder();
            asinBuilder.setAsin(asinListDto.getAsin());
            asinBuilder.setParentAsin(Optional.ofNullable(asinListDto.getParentAsin()).orElse(""));
            asinBuilder.setMsku(asinListDto.getMsku());
            asinBuilder.setShopId(asinListDto.getShopId());
            asinBuilder.setShopName(shopIdNameMap.getOrDefault(asinListDto.getShopId(), ""));
            AmznEndpoint amznEndpoint = AmznEndpoint.getByMarketplaceId(asinListDto.getMarketplaceId());
            asinBuilder.setMarketplaceCN(Optional.ofNullable(amznEndpoint).map(AmznEndpoint::getMarketplaceCN).orElse(""));
            asinList.add(asinBuilder.build());
        }
        //page对象
        AsinListResponseVo.Builder builder = AsinListResponseVo.newBuilder();
        builder.addAllRows(asinList);
        builder.setPageNo(page.getPageNo());
        builder.setPageSize(page.getPageSize());
        builder.setTotalPage(page.getTotalPage());
        builder.setTotalSize(page.getTotalSize());
        return builder.build();
    }

    @Override
    public AllAsinListResponseVo getAllAsinList(AsinListReqVo reqVo) {
        String uuid = UUID.randomUUID().toString();
        log.info("产品透视分析{}--asin列表查询-参数 {}", uuid, JSONUtil.objectToJson(reqVo));
        long t = Instant.now().toEpochMilli();
        //page对象
        AllAsinListResponseVo.Builder builder = AllAsinListResponseVo.newBuilder();
        builder.setPageNo(reqVo.getPageNo());
        builder.setPageSize(reqVo.getPageSize());

        //查询asin列表
        Page<AllAsinListDto> asinPage;
        Page<AsinListDto> page;
        if (AsinListReqVo.SearchTypeEnum.PARENT_ASIN.getType().equals(reqVo.getSearchType())) {
            page = odsAmazonAdProductReportDao.getParentAsinIdPage(reqVo);
        } else {
            page = odsAmazonAdProductReportDao.getAsinSkuPageByPuidAndShopId(reqVo);
        }
        if (CollectionUtils.isEmpty(page.getRows())) {
            return builder.build();
        }
        if (AsinListReqVo.SearchTypeEnum.PARENT_ASIN.getType().equals(reqVo.getSearchType())) {
            asinPage = this.buildParentAsinPage(reqVo, page);
        } else if (AsinListReqVo.SearchTypeEnum.ASIN.getType().equals(reqVo.getSearchType())) {
            asinPage = this.buildAsinPage(reqVo, page);
        } else {
            asinPage = this.buildSkuPage(reqVo, page);
        }
        log.info("产品透视分析{}--asin列表查询-花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        //封装数据
        List<AllAsinListDto> rows = asinPage.getRows();
        //获取店铺名称
        Map<Integer, String> shopIdNameMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(rows)) {
            shopIdNameMap = shopAuthDao.getShopAuthBoByIds(reqVo.getPuid(), rows.stream().map(AllAsinListDto::getShopId).collect(Collectors.toList()))
                    .stream().collect(Collectors.toMap(ShopAuthBo::getId, ShopAuthBo::getName));
        }

        //asin集合
        List<AllAsinListResponseVo.AsinListVo> asinList = new ArrayList<>();
        AllAsinListResponseVo.AsinListVo.Builder asinBuilder = null;
        for (AllAsinListDto allAsinListDto : rows) {
            asinBuilder = AllAsinListResponseVo.AsinListVo.newBuilder();
            asinBuilder.addAllAsin(Optional.ofNullable(allAsinListDto.getAsin()).orElse(new ArrayList<>()));
            asinBuilder.addAllParentAsin(Optional.ofNullable(allAsinListDto.getParentAsin()).orElse(new ArrayList<>()));
            asinBuilder.addAllMsku(Optional.ofNullable(allAsinListDto.getMsku()).orElse(new ArrayList<>()));
            asinBuilder.setShopId(allAsinListDto.getShopId());
            asinBuilder.setShopName(shopIdNameMap.getOrDefault(allAsinListDto.getShopId(), ""));
            asinBuilder.setMainImage(Optional.ofNullable(allAsinListDto.getMainImage()).orElse(""));
            AmznEndpoint amznEndpoint = AmznEndpoint.getByMarketplaceId(allAsinListDto.getMarketplaceId());
            asinBuilder.setMarketplaceCN(Optional.ofNullable(amznEndpoint).map(AmznEndpoint::getMarketplaceCN).orElse(""));
            asinList.add(asinBuilder.build());
        }
        //page对象
        builder.addAllRows(asinList);
        builder.setPageNo(page.getPageNo());
        builder.setPageSize(page.getPageSize());
        builder.setTotalPage(page.getTotalPage());
        builder.setTotalSize(page.getTotalSize());
        return builder.build();
    }

    private Page<AllAsinListDto> buildSkuPage(AsinListReqVo reqVo, Page<AsinListDto> page) {
        List<AllAsinListDto> voList = new ArrayList<>();
        Page<AllAsinListDto> asinPage = new Page<>(page.getPageNo(), page.getPageSize(), page.getTotalPage(), page.getTotalSize(), voList);
        List<String> skus = page.getRows().stream().map(AsinListDto::getMsku).collect(Collectors.toList());
        Map<String, List<OdsProduct>> skuMap = odsProductDao.listProductBySkus(reqVo.getPuid(), reqVo.getShopIdList(), skus)
                .stream().collect(Collectors.groupingBy(OdsProduct::getSku));
        page.getRows().forEach(e -> {
            AllAsinListDto dto = new AllAsinListDto();
            dto.setShopId(e.getShopId());
            dto.setMarketplaceId(e.getMarketplaceId());
            dto.setMsku(Collections.singletonList(e.getMsku()));
            dto.setMainImage(e.getMainImage());
            List<String> parentAsinList = new ArrayList<>();
            parentAsinList.add(StringUtils.isBlank(e.getParentAsin()) ? e.getAsin() : e.getParentAsin());
            dto.setParentAsin(parentAsinList);
            List<String> asinList = new ArrayList<>();
            if (StringUtils.isNotBlank(e.getAsin())) {
                asinList.add(e.getAsin());
                dto.setAsin(asinList);
            }
            if (skuMap.containsKey(e.getAsin())) {
                skuMap.get(e.getAsin()).forEach(p -> {
                    parentAsinList.add(StringUtils.isBlank(p.getParentAsin()) ? p.getAsin() : p.getParentAsin());
                    asinList.add(p.getAsin());
                });
                dto.setParentAsin(parentAsinList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
                dto.setAsin(asinList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
            }
            voList.add(dto);
        });
        return asinPage;
    }

    private Page<AllAsinListDto> buildAsinPage(AsinListReqVo reqVo, Page<AsinListDto> page) {
        List<AllAsinListDto> voList = new ArrayList<>();
        Page<AllAsinListDto> asinPage = new Page<>(page.getPageNo(), page.getPageSize(), page.getTotalPage(), page.getTotalSize(), voList);
        List<String> asins = page.getRows().stream().map(AsinListDto::getAsin).collect(Collectors.toList());
        Map<String, List<OdsProduct>> asinMap = odsProductDao.listProductByAsins(reqVo.getPuid(), reqVo.getShopIdList(), asins)
                .stream().collect(Collectors.groupingBy(e -> e.getShopId() + "#" + e.getAsin()));
        page.getRows().forEach(e -> {
            AllAsinListDto dto = new AllAsinListDto();
            dto.setShopId(e.getShopId());
            dto.setMarketplaceId(e.getMarketplaceId());
            dto.setAsin(Collections.singletonList(e.getAsin()));
            dto.setMainImage(e.getMainImage());
            List<String> parentAsinList = new ArrayList<>();
            parentAsinList.add(StringUtils.isBlank(e.getParentAsin()) ? e.getAsin() : e.getParentAsin());
            dto.setParentAsin(parentAsinList);
            List<String> skuList = new ArrayList<>();
            if (StringUtils.isNotBlank(e.getMsku())) {
                skuList.add(e.getMsku());
                dto.setMsku(skuList);
            }
            String key = e.getShopId() + "#" + e.getAsin();
            if (asinMap.containsKey(key)) {
                asinMap.get(key).forEach(p -> {
                    parentAsinList.add(StringUtils.isBlank(p.getParentAsin()) ? p.getAsin() : p.getParentAsin());
                    skuList.add(p.getSku());
                });
                dto.setParentAsin(parentAsinList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
                dto.setMsku(skuList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
            }
            voList.add(dto);
        });
        return asinPage;
    }

    private Page<AllAsinListDto> buildParentAsinPage(AsinListReqVo reqVo, Page<AsinListDto> page) {
        List<AllAsinListDto> voList = new ArrayList<>();
        Page<AllAsinListDto> asinPage = new Page<>(page.getPageNo(), page.getPageSize(), page.getTotalPage(), page.getTotalSize(), voList);
        if (CollectionUtils.isEmpty(page.getRows())) {
            return asinPage;
        }
        List<Long> parentIdList = new ArrayList<>();
        page.getRows().forEach(e -> {
            parentIdList.addAll(StringUtil.splitStr(e.getIds()).stream().map(i -> Long.parseLong(i.trim())).collect(Collectors.toList()));
        });
        Map<String, List<OdsProduct>> childProductMap = odsProductDao.listByParentId(reqVo.getPuid(), reqVo.getShopIdList(), parentIdList)
                .stream().collect(Collectors.groupingBy(e -> e.getShopId() + "#" + e.getParentId()));
        page.getRows().forEach(e -> {
            AllAsinListDto dto = new AllAsinListDto();
            dto.setShopId(e.getShopId());
            dto.setMarketplaceId(e.getMarketplaceId());
            dto.setParentAsin(Collections.singletonList(e.getAsin()));
            List<OdsProduct> odsProducts = new ArrayList<>();
            StringUtil.splitStr(e.getIds()).forEach(i -> {
                String key = e.getShopId() + "#" + i.trim();
                if (childProductMap.containsKey(key)) {
                    odsProducts.addAll(childProductMap.get(key));
                }
            });
            if (CollectionUtils.isNotEmpty(odsProducts)) {
                List<String> asinList = new ArrayList<>();
                List<String> mskuList = new ArrayList<>();
                odsProducts.forEach(p -> {
                    asinList.add(p.getAsin());
                    mskuList.add(p.getSku());
                });
                dto.setAsin(asinList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
                dto.setMsku(mskuList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
                dto.setMainImage(odsProducts.get(0).getMainImage());
            }
            voList.add(dto);
        });
        return asinPage;
    }

    @Override
    public AsinListResponseVo getAsinAllList(AsinListReqVo reqVo) {
        List<VcShopAuth> listByIdList = vcShopAuthDao.getListByIdList(reqVo.getShopIdList());
        List<Integer> shopIds = Lists.newArrayList(reqVo.getShopIdList());
        List<Integer> scShopIds = new ArrayList<>();
        List<Integer> vcShopIds = new ArrayList<>();
        boolean isVc = CollectionUtils.isNotEmpty(listByIdList);
        if (CollectionUtils.isNotEmpty(listByIdList)) {
            List<Integer> collect = listByIdList.stream().map(VcShopAuth::getId).collect(Collectors.toList());
            shopIds.removeAll(collect);
            vcShopIds.addAll(collect);
        }
        scShopIds.addAll(shopIds);
        AsinListReqVo.SearchTypeEnum searchTypeEnum = UCommonUtil.getByCode(reqVo.getSearchType(), AsinListReqVo.SearchTypeEnum.class);
        //如果选择店铺只有vc 并且选择的MSKU不返回数据
        if (AsinListReqVo.SearchTypeEnum.MSKU.equals(searchTypeEnum)) {
            if (CollectionUtils.isNotEmpty(vcShopIds)) {
                ArrayList<Integer> shops = Lists.newArrayList(reqVo.getShopIdList());
                shops.removeAll(vcShopIds);
                reqVo.setShopIdList(shops);
                vcShopIds.clear();
            }
            if (CollectionUtils.isEmpty(scShopIds)) {
                return this.buildAsinListEmptyResponseVo(reqVo);
            }
        }

        if (StringUtils.isNotBlank(reqVo.getPortfolioId()) || StringUtils.isNotBlank(reqVo.getCampaignId()) || StringUtils.isNotBlank(reqVo.getStatus()) || StringUtils.isNotBlank(reqVo.getServingStatus())) {
            List<CampaignTypeDto> campaignTypeDtos = amazonAdCampaignAllDao.getCampaignIdsTypeByPortfolioId(reqVo.getPuid(), reqVo.getShopIdList(), reqVo.getPortfolioId(), reqVo.getCampaignId(), reqVo.getAdType(), reqVo.getStatus(), reqVo.getServingStatus());
            if (CollectionUtils.isEmpty(campaignTypeDtos)) {
                return this.buildAsinListEmptyResponseVo(reqVo);
            }
            reqVo.setIsGroup(true);
            reqVo.setCampaignIdMap(campaignTypeDtos.stream().collect(Collectors.groupingBy(CampaignTypeDto::getType, Collectors.mapping(CampaignTypeDto::getCampaignId, Collectors.toList()))));
        } else {
            reqVo.setIsGroup(false);
        }
        long sbNow = System.currentTimeMillis();
        // 处理sb数据问题
        if (reqVo.getIsGroup()) {
            if (reqVo.getCampaignIdMap().containsKey(Constants.SB)) {
                List<AmazonSbAds> sbAds = amazonSbAdsDao.getAsinsByCampaignIds(reqVo.getPuid(), reqVo.getShopIdList(), reqVo.getCampaignIdMap().get(Constants.SB), reqVo.getGroupId());
                dealSbAsins(sbAds, reqVo);
            }
        } else {
            if (StringUtils.isBlank(reqVo.getAdType()) || (StringUtils.isNotBlank(reqVo.getAdType()) && reqVo.getAdType().contains(Constants.SB))) {
                List<AmazonSbAds> sbAds = amazonSbAdsDao.getAsinsByCampaignIds(reqVo.getPuid(), reqVo.getShopIdList(), null, reqVo.getGroupId());
                dealSbAsins(sbAds, reqVo);
            }
        }
        long now = System.currentTimeMillis();
        log.info("select sb time = {}", now - sbNow);
        Page<AsinListDto> asinListDtoPage;
        if (!AsinListReqVo.SearchTypeEnum.PARENT_ASIN.equals(searchTypeEnum)) {
            asinListDtoPage = odsAmazonAdProductDao.getAsinAllPage(reqVo.getPuid(), reqVo, isVc);
        } else {
            if (isVc) {
                asinListDtoPage = odsAmazonAdProductDao.getParentAsinAllPageVc(reqVo.getPuid(), reqVo, scShopIds, vcShopIds);
            } else {
                asinListDtoPage = odsAmazonAdProductDao.getParentAsinAllPage(reqVo.getPuid(), reqVo);
            }
            dealParentAsin(reqVo.getPuid(), asinListDtoPage.getRows(), vcShopIds);
        }
        log.info("select time = {}", System.currentTimeMillis() - now);
        List<AsinListResponseVo.AsinListVo> asinList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(asinListDtoPage.getRows())) {
            Map<Integer, ShopAuthBo> shopIdNameMap = shopAuthDao.getShopAuthBoByIds(reqVo.getPuid(), asinListDtoPage.getRows().stream().map(AsinListDto::getShopId).collect(Collectors.toList()))
                    .stream().collect(Collectors.toMap(ShopAuthBo::getId, Function.identity()));
            AsinListResponseVo.AsinListVo.Builder asinBuilder;
            for (AsinListDto asinListDto : asinListDtoPage.getRows()) {
                asinBuilder = AsinListResponseVo.AsinListVo.newBuilder();
                asinBuilder.setAsin(asinListDto.getAsin());
                if (StringUtils.isNotEmpty(asinListDto.getMsku())) {
                    asinBuilder.setMsku(asinListDto.getMsku());
                }
                asinBuilder.setShopId(asinListDto.getShopId());
                ShopAuthBo shopAuthBo = shopIdNameMap.getOrDefault(asinListDto.getShopId(), null);
                if (shopAuthBo != null) {
                    asinBuilder.setShopName(StringUtil.toStringSafe(shopAuthBo.getName()));
                    AmznEndpoint amznEndpoint = AmznEndpoint.getByMarketplaceId(shopAuthBo.getMarketplaceId());
                    asinBuilder.setMarketplaceCN(Optional.ofNullable(amznEndpoint).map(AmznEndpoint::getMarketplaceCN).orElse(""));
                }
                if (CollectionUtils.isNotEmpty(asinListDto.getChildAsin())) {
                    asinBuilder.addAllChildAsins(asinListDto.getChildAsin());
                }
                if (CollectionUtils.isNotEmpty(asinListDto.getChildSku())) {
                    asinBuilder.addAllChildSku(asinListDto.getChildSku());
                }
                asinList.add(asinBuilder.build());
            }
        }
        //page对象
        AsinListResponseVo.Builder builder = AsinListResponseVo.newBuilder();
        builder.addAllRows(asinList);
        builder.setPageNo(asinListDtoPage.getPageNo());
        builder.setPageSize(asinListDtoPage.getPageSize());
        builder.setTotalPage(asinListDtoPage.getTotalPage());
        builder.setTotalSize(asinListDtoPage.getTotalSize());
        return builder.build();
    }

    /**
     * 处理父asin问题
     */
    private void dealParentAsin(Integer puid, List<AsinListDto> list, List<Integer> vcShopIds) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Set<Integer> vcShops = new HashSet<>();
        Set<Long> vcParentId = new HashSet<>();
        List<Long> parentId = list.stream().peek(e->{
            if (vcShopIds.contains(e.getShopId())) {
                vcShops.add(e.getShopId());
                vcParentId.add(e.getParentId());

            }
        }).map(AsinListDto::getParentId).distinct().collect(Collectors.toList());
        List<Integer> shopId = list.stream().map(AsinListDto::getShopId).distinct().collect(Collectors.toList());

        Map<Long, OdsProduct> parentAsinMap = odsProductDao.listParentAsinById(puid, shopId, parentId).stream()
                .collect(Collectors.toMap(OdsProduct::getId, Function.identity(), (k1, k2) -> k1));
        Map<Long, List<OdsProduct>> listChildAsin = odsProductDao.listByParentId(puid, shopId, parentId).stream()
                .collect(Collectors.groupingBy(OdsProduct::getParentId));
        Map<Long, OdsProduct> vcParentAsinMap = new HashMap<>();
        Map<String, List<OdsProduct>> vcListChildAsin = new HashMap<>();
        if (CollectionUtils.isNotEmpty(vcParentId)) {
            Set<String> parentAsin = new HashSet<>();
            vcParentAsinMap = odsProductDao.listVcParentAsinById(puid, Lists.newArrayList(vcShops), Lists.newArrayList(vcParentId)).stream()
                    .peek(e->{
                        parentAsin.add(e.getAsin());
                    })
                    .collect(Collectors.toMap(OdsProduct::getId, Function.identity(), (k1, k2) -> k1));

            if (CollectionUtils.isNotEmpty(parentAsin)) {
                vcListChildAsin = odsProductDao.listVcByParentAsin(puid, Lists.newArrayList(vcShops), Lists.newArrayList(parentAsin)).stream()
                        .collect(Collectors.groupingBy(OdsProduct::getParentAsin));
            }
        }
        for (AsinListDto asinListDto : list) {

            if (vcShops.contains(asinListDto.getShopId())) {
                OdsProduct odsProduct = vcParentAsinMap.get(asinListDto.getParentId());
                asinListDto.setAsin(odsProduct == null ? "" : odsProduct.getAsin());
                asinListDto.setMsku(odsProduct == null ? "" : odsProduct.getSku());
                asinListDto.setChildAsin(vcListChildAsin.getOrDefault(odsProduct.getAsin(),
                        new ArrayList<>()).stream().map(OdsProduct::getAsin).collect(Collectors.toList()));

            } else {
                OdsProduct odsProduct = parentAsinMap.get(asinListDto.getParentId());
                asinListDto.setAsin(odsProduct == null ? "" : odsProduct.getAsin());
                asinListDto.setMsku(odsProduct == null ? "" : odsProduct.getSku());
                asinListDto.setChildAsin(listChildAsin.getOrDefault(asinListDto.getParentId(),
                        new ArrayList<>()).stream().map(OdsProduct::getAsin).collect(Collectors.toList()));
                asinListDto.setChildSku(listChildAsin.getOrDefault(asinListDto.getParentId(),
                        new ArrayList<>()).stream().map(OdsProduct::getSku).collect(Collectors.toList()));
            }


        }
    }


    /**
     * 处理sb asins
     */
    private void dealSbAsins(List<AmazonSbAds> sbAds, AsinListReqVo reqVo) {
        if (CollectionUtils.isEmpty(sbAds)) {
            return;
        }
        Set<String> asins = new HashSet<>();
        if (AsinListReqVo.SearchTypeEnum.ASIN.getType().equals(reqVo.getSearchType()) && StringUtils.isNotBlank(reqVo.getSearchValue())) {
            List<String> asinSearchList = StringUtil.splitStr(reqVo.getSearchValue(), StringUtil.SPECIAL_COMMA)
                    .stream().map(SqlStringUtil::dealLikeSql).distinct().collect(Collectors.toList());
            boolean type = asinSearchList.size() == 1;
            String value = type ? asinSearchList.get(0) : "";
            Set<String> asinSearchSet = new HashSet<>(asinSearchList);
            sbAds.forEach(k -> {
                List<String> asinList = StringUtil.splitStr(k.getAsins());
                for (String asin : asinList) {
                    if (StringUtils.isBlank(asin)) {
                        continue;
                    }
                    if (type ? asin.contains(value) : asinSearchSet.contains(asin)) {
                        asins.add(asin);
                    }
                }
            });
        } else {
            sbAds.forEach(k -> asins.addAll(StringUtil.splitStr(k.getAsins()).stream().filter(StringUtils::isNotBlank).collect(Collectors.toList())));
        }
        reqVo.setSbAsin(new ArrayList<>(asins));
    }

    private AsinListResponseVo buildAsinListEmptyResponseVo(AsinListReqVo qo) {
        //page对象
        AsinListResponseVo.Builder builder = AsinListResponseVo.newBuilder();
        builder.addAllRows(new ArrayList<>());
        builder.setPageNo(qo.getPageNo());
        builder.setPageSize(qo.getPageSize());
        builder.setTotalPage(0);
        builder.setTotalSize(0);
        return builder.build();
    }


    @Override
    public InitAsinVo getBatchSpTaskInitAsinInfo(InitAsinInfoReqVo reqVo) {
        String uuid = UUID.randomUUID().toString();
        log.info("产品透视分析{}--店铺asin初始化-参数 {}", uuid, JSONUtil.objectToJson(reqVo));
        //尝试从batch prduct表中根据puid查询数据，如果存在，则返回最近的一条成功的一条asin。
        InitAsinVo result = new InitAsinVo();
        Optional.ofNullable(reqVo.getMarketplaceId())
                .filter(StringUtils::isNotEmpty).ifPresent(result::setMarketplaceId);
        Optional.ofNullable(reqVo.getShopIdList())
                .filter(CollectionUtils::isNotEmpty).ifPresent(result::setShopId);
        List<Integer> shopIdList = reqVo.getShopIdList();
        shopIdList = Optional.ofNullable(shopIdList).map(l -> l.stream()
                        .filter(Objects::nonNull).distinct()
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        List<String> marketIdList = Optional.ofNullable(reqVo.getMarketplaceId())
                .filter(StringUtils::isNotEmpty)
                .map(Collections::singletonList)
                .orElse(Collections.emptyList());
        AmazonAdBatchProduct recentlyAsin = amazonAdBatchProductDao.getProductRecentlyByShopIdAndMarketIdAndStatus(reqVo.getPuid(), shopIdList,
                marketIdList, Collections.singletonList(SpBatchCreateAdLevelStatusEnum.SUCCESS.getCode()));
        if (Objects.nonNull(recentlyAsin)) {
            result = new InitAsinVo(Collections.singletonList(recentlyAsin.getShopId()), recentlyAsin.getMarketplaceId(), recentlyAsin.getAsin());
        }
        return result;
    }

    /**
     * 查询近近7天订单量最高asin-前端初始化asin-查询热表或实时计算
     *
     * @param reqVo
     * @param now
     * @return
     */
    private InitAsinVo getInitAsinInfoLast7Day(InitAsinInfoReqVo reqVo, LocalDateTime now) {

        //先查询热点asin表
        if (refreshNacosConfiguration.isPerspectiveEnableHotAsin()) {
            //查询热点数据表
            Date updateTime = LocalDateTimeUtil.convertLDTToDate(LocalDateTimeUtil.getDayStart(now.plusDays(-LAST_7_DAY)));
            List<AmazonShopHotData> dataList = amazonShopHotDataDao.getByShopIdAndType(reqVo.getPuid(), reqVo.getMarketplaceId(), reqVo.getShopIdList(), HotDataTypeEnum.PERSPECTIVE_INIT_ASIN.getType(), updateTime);

            //热点数据不为空
            if (!CollectionUtils.isEmpty(dataList)) {
                AmazonShopHotData hotData = dataList.get(0);
                if (Objects.nonNull(hotData) && Objects.nonNull(hotData.getShopId()) && StringUtils.isNoneBlank(hotData.getMarketplaceId(), hotData.getDataValue())) {
                    return new InitAsinVo(Collections.singletonList(hotData.getShopId()), hotData.getMarketplaceId(), hotData.getDataValue());
                }
            }
        }

        //走原来的初始化逻辑
        Integer shopIdPartition = refreshNacosConfiguration.getInitAsinShopIdPartition();
        if (shopIdPartition == null) {
            shopIdPartition = DEFAULT_INIT_ASIN_SHOP_PARTITION;
        }
        return getTopOrderNumAsinLastSomeDays(reqVo, now, LAST_7_DAY, shopIdPartition);

    }

    /**
     * 查询近近7天订单量最高asin-后端实时处理逻辑
     *
     * @param reqVo
     * @param now
     * @param days
     * @param partition
     * @return
     */
    @Override
    public InitAsinVo getTopOrderNumAsinLastSomeDays(InitAsinInfoReqVo reqVo, LocalDateTime now, Integer days, int partition) {

        //查询所有店铺数据
        List<AsinOrderNumDto> todoList = getOrderNumAsinForShopList(reqVo, now, days, partition);

        //比较取得最高订单量的数据
        if (!CollectionUtils.isEmpty(todoList)) {
            if (todoList.size() == 1) {
                AsinOrderNumDto dto = todoList.get(0);
                return new InitAsinVo(Arrays.asList(dto.getShopId()), dto.getMarketplaceId(), dto.getAsin());
            }

            //排序取最高
            todoList.sort((o1, o2) -> o2.getAdOrderNum().compareTo(o1.getAdOrderNum()));
            AsinOrderNumDto dto = todoList.get(0);
            return new InitAsinVo(Arrays.asList(dto.getShopId()), dto.getMarketplaceId(), dto.getAsin());
        }

        return null;
    }

    /**
     * 根据传参查询近7天每个店铺的订单量最高asin
     *
     * @param reqVo
     * @param now
     * @param days
     * @param partition
     * @return
     */
    @Override
    public List<AsinOrderNumDto> getOrderNumAsinForShopList(InitAsinInfoReqVo reqVo, LocalDateTime now, Integer days, int partition) {
        long t = Instant.now().toEpochMilli();
        //以asin为维度，查询最近7天订单量最高的asin
        if (Objects.isNull(days)) {
            days = LAST_7_DAY;
        }
        reqVo.setStartDate(LocalDateTimeUtil.formatTime(now.plusDays(-days), LocalDateTimeUtil.YYYYMMDD_DATE_FORMATE));
        reqVo.setEndDate(LocalDateTimeUtil.formatTime(now, LocalDateTimeUtil.YYYYMMDD_DATE_FORMATE));

        //分批查询近7天各店铺订单量最高asin
        List<List<Integer>> shopIdPartitionList = Lists.partition(reqVo.getShopIdList(), partition);
        List<CompletableFuture<List<AsinOrderNumDto>>> futureList = new ArrayList<>(shopIdPartitionList.size());
        shopIdPartitionList.forEach(x -> futureList.add(CompletableFuture.supplyAsync(() -> {
            long start = Instant.now().toEpochMilli();
            //构建新的查询实体
            InitAsinInfoReqVo curReqVo = new InitAsinInfoReqVo();
            BeanUtils.copyProperties(reqVo, curReqVo);
            //传入站点集合以满足索引
            if (StringUtils.isBlank(reqVo.getMarketplaceId()) && CollectionUtils.isEmpty(reqVo.getMarketplaceIdList())) {
                curReqVo.setMarketplaceIdList(marketplaceIdList);
            }
            curReqVo.setShopIdList(x);
            List<AsinOrderNumDto> partitionTop = amazonAdProductReportDao.getTopOrderNumAsinRecentLast7Day(curReqVo);
            log.info("产品透视分析--分片查询 puid {}, 花费时间 {}, 参数 {}", curReqVo.getPuid(), Instant.now().toEpochMilli() - start, JSONUtil.objectToJson(curReqVo));
            return partitionTop;
        }, ThreadPoolUtil.getProductPerspectiveInitAsinPool())));
        //阻塞等待
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
        try {
            allFutures.get();
        } catch (Exception e) {
            log.error("初始化asin, 多线程执行异常", e);
        }
        log.info("产品透视分析--店铺asin初始化-查询近7天订单量最高asin- 花费时间 {}", Instant.now().toEpochMilli() - t);

        List<AsinOrderNumDto> todoList = new ArrayList<>(futureList.size());
        futureList.forEach(x -> {
            List<AsinOrderNumDto> curList = null;
            try {
                curList = x.get();
            } catch (Exception e) {
                log.error("初始化asin, 多线程汇总失败", e);
            }
            if (!CollectionUtils.isEmpty(curList) && StringUtils.isNotBlank(curList.get(0).getAsin())) {
                todoList.add(curList.get(0));
            }
        });

        return todoList;
    }

    /**
     * 近30天随机取asin
     *
     * @param reqVo
     * @param now
     * @return
     */
    private InitAsinVo getRandomLast30Day(InitAsinInfoReqVo reqVo, LocalDateTime now) {
        long t = Instant.now().toEpochMilli();
        //如果近7天没数据，则从近30天随机取一个
        reqVo.setStartDate(LocalDateTimeUtil.formatTime(now.plusDays(-LAST_30_DAY), LocalDateTimeUtil.YYYYMMDD_DATE_FORMATE));
        reqVo.setEndDate(LocalDateTimeUtil.formatTime(now, LocalDateTimeUtil.YYYYMMDD_DATE_FORMATE));
        //传入站点集合以满足索引
        if (StringUtils.isBlank(reqVo.getMarketplaceId())) {
            reqVo.setMarketplaceIdList(marketplaceIdList);
        }
        List<InitOneAsinVo> initAsinVos = amazonAdProductReportDao.getOneByTime(reqVo);
        log.info("产品透视分析--店铺asin初始化-近30天随机取一个- 花费时间 {}", Instant.now().toEpochMilli() - t);
        if (!CollectionUtils.isEmpty(initAsinVos)) {
            InitOneAsinVo oneAsinVo = initAsinVos.get(0);
            return new InitAsinVo(Arrays.asList(oneAsinVo.getShopId()), oneAsinVo.getMarketplaceId(), oneAsinVo.getAsin());
        }
        return null;
    }

    @Override
    public AsinListResponseVo getAdProducts(AsinListReqVo reqVo) {

        AsinListReqVo.SearchTypeEnum searchFieldEnum = UCommonUtil.getByCode(reqVo.getSearchField(), AsinListReqVo.SearchTypeEnum.class);
        String searchField = Objects.nonNull(searchFieldEnum) ? searchFieldEnum.getField() : "";
        // sb数据处理
        long sbNow = System.currentTimeMillis();
        List<AmazonSbAds> sbAds = amazonSbAdsDao.getAsinsByCampaignIds(reqVo.getPuid(), reqVo.getShopIdList(), null, null);
        String searchValue = reqVo.getSearchValue();
        if (!StringUtils.equals(AsinListReqVo.SearchTypeEnum.ASIN.getType(), reqVo.getSearchField())
                && StringUtils.isNotBlank(searchValue)) {
            reqVo.setSearchValue(StringUtils.EMPTY);
        }
        dealSbAsins(sbAds, reqVo);
        if (StringUtils.isNotBlank(searchField) && StringUtils.isNotBlank(searchValue)) {
            List<String> asinList = odsProductDao.listAsinBySearchParam(reqVo.getPuid(), reqVo.getMarketplaceId(), reqVo.getShopIdList(),
                    reqVo.getSearchType(), searchField, searchValue, reqVo.getSearchFlag());
            if (CollectionUtils.isEmpty(asinList)) {
                reqVo.setSbAsin(Lists.newArrayList());
            } else {
                if (CollectionUtils.isNotEmpty(reqVo.getSbAsin())) {
                    List<String> sbAsin = reqVo.getSbAsin();
                    sbAsin.retainAll(asinList);
                    reqVo.setSbAsin(sbAsin);
                }
            }
        }
        if (CollectionUtils.size(reqVo.getSbAsin()) >= 10000) {
            reqVo.setSbAsin(reqVo.getSbAsin().subList(0, 9999));
        }
        long now = System.currentTimeMillis();
        log.info("select sb time = {}", now - sbNow);
        if (StringUtils.isNotBlank(searchValue) && StringUtils.isBlank(reqVo.getSearchValue())) {
            reqVo.setSearchValue(searchValue);
        }

        AsinListReqVo.SearchTypeEnum searchTypeEnum = UCommonUtil.getByCode(reqVo.getSearchType(), AsinListReqVo.SearchTypeEnum.class);
        Page<AsinListDto> asinListDtoPage;
        if (!AsinListReqVo.SearchTypeEnum.PARENT_ASIN.equals(searchTypeEnum)) {
            asinListDtoPage = odsAmazonAdProductDao.getAdProductAllPage(reqVo.getPuid(), reqVo);
        } else {
            asinListDtoPage = odsAmazonAdProductDao.getAdParentAsinAllPage(reqVo.getPuid(), reqVo);
        }
        log.info("select time = {}", System.currentTimeMillis() - now);

        List<AsinListResponseVo.AsinListVo> asinList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(asinListDtoPage.getRows())) {
            AsinListResponseVo.AsinListVo.Builder asinBuilder;
            for (AsinListDto asinListDto : asinListDtoPage.getRows()) {
                asinBuilder = AsinListResponseVo.AsinListVo.newBuilder();
                if (StringUtils.isNotBlank(asinListDto.getShopIdStr())) {
                    asinBuilder.addAllShopIds(Arrays.stream(asinListDto.getShopIdStr().split(","))
                            .map(s -> {
                                if (StringUtils.isNotBlank(s)) {
                                    return Integer.parseInt(s.trim());
                                }
                                return null;
                            }).filter(Objects::nonNull)
                            .collect(Collectors.toList()));
                }
                if (StringUtils.isNotBlank(asinListDto.getAsin())) {
                    asinBuilder.setAsin(asinListDto.getAsin());
                }
                if (StringUtils.isNotBlank(asinListDto.getParentAsin())) {
                    asinBuilder.setParentAsin(asinListDto.getParentAsin());
                }
                if (StringUtils.isNotBlank(asinListDto.getMsku())) {
                    asinBuilder.setMsku(asinListDto.getMsku());
                }
                asinList.add(asinBuilder.build());
            }
        }

        AsinListResponseVo.Builder builder = AsinListResponseVo.newBuilder();
        builder.addAllRows(asinList);
        builder.setPageNo(asinListDtoPage.getPageNo());
        builder.setPageSize(asinListDtoPage.getPageSize());
        builder.setTotalPage(asinListDtoPage.getTotalPage());
        builder.setTotalSize(asinListDtoPage.getTotalSize());
        return builder.build();
    }
}
