package com.meiyunji.sponsored.service.cpc.po;

import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.enums.BaseEnum;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import com.meiyunji.sponsored.common.util.MD5Util;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.service.annotation.AdLogFormat;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * AmazonAdKeyword
 * <AUTHOR>
 */
@DbTable(value = "t_amazon_ad_keyword_extend")
@Data
public class AmazonAdKeywordExtend extends BasePo {
	/**
	 * id
	 */
	@DbColumn(value = "id",autoIncrement = true,key = true)
	private Long id;

	/**
	 * 商户uid
	 */
	@DbColumn(value = "puid")
	private Integer puid;
	/**
	 * 店铺ID
	 */
	@DbColumn(value = "shop_id")
	private Integer shopId;
	/**
	 * 站点
	 */
	@DbColumn(value = "marketplace_id")
	private String marketplaceId;
	/**
	 * 关键词id
	 */
	@DbColumn(value = "keyword_id")
	private String keywordId;
	/**
	 * 广告组id
	 */
	@DbColumn(value = "ad_group_id")
	private String adGroupId;

	/**
	 * 活动id
	 */
	@DbColumn(value = "campaign_id")
	private String campaignId;

	/**
	 * 预估曝光量下限
	 */
	@DbColumn(value = "estimated_impression_lower")
	private Long estimatedImpressionLower;
	/**
	 * 预估曝光量上限
	 */
	@DbColumn(value = "estimated_impression_upper")
	private Long estimatedImpressionUpper;

}