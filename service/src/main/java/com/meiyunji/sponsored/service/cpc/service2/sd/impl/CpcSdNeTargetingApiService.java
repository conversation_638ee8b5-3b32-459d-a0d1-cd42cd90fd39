package com.meiyunji.sponsored.service.cpc.service2.sd.impl;

import com.amazon.advertising.mode.StateEnum;
import com.amazon.advertising.mode.netargeting.NegativeTargetingExpression;
import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.amazon.advertising.mode.targeting.TargetingClause;
import com.amazon.advertising.mode.targeting.TargetingClauseResult;
import com.amazon.advertising.sd.entity.neTargeting.*;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdNeTargetingDao;
import com.meiyunji.sponsored.service.cpc.dto.AmazonServingStatusDto;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.BatchNeTargetVo;
import com.meiyunji.sponsored.service.cpc.vo.BatchResponseVo;
import com.meiyunji.sponsored.service.cpc.vo.SdBatchNeTargetingVo;
import com.meiyunji.sponsored.service.enums.SDNeTargetingExpressionTypeEnum;
import com.meiyunji.sponsored.service.stream.enums.ManagementStreamResponseStatusEnum;
import com.meiyunji.sponsored.service.syncAd.enums.SdStateEnum;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by xp on 2021/7/7.
 * 对接口二次封装，直接和广告接口交互
 */
@Component
@Slf4j
public class CpcSdNeTargetingApiService {

    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonSdAdNeTargetingDao amazonSdAdNeTargetingDao;
    @Autowired
    private IAmazonSdAdGroupDao sdAdGroupDao;
    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    public Result create(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<SdBatchNeTargetingVo> amazonSdAdTargetings) {
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }
        if (amazonAdProfile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        if (CollectionUtils.isEmpty(amazonSdAdTargetings)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        List<TargetingClause> targetingClauses = amazonSdAdTargetings.stream().map(e -> {
            TargetingClause targetingClause = new TargetingClause();
            if (StringUtils.isNotBlank(e.getAdGroupId())) {
                targetingClause.setAdGroupId(Long.valueOf(e.getAdGroupId()));
            }
            targetingClause.setExpressionType(e.getExpressionType());
            targetingClause.setState(e.getState());
            if (StringUtils.isNotBlank(e.getExpression())) {
                targetingClause.setExpressions(JSONUtil.jsonToArray(e.getExpression(), Expression.class));
            }
            return targetingClause;
        }).collect(Collectors.toList());

        CreateNegativeTargetingClausesResponse response = cpcApiHelper.call(shop, () -> NeProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).create(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), targetingClauses));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "网络延迟，请稍后重试";
        if (CollectionUtils.isNotEmpty(response.getResultList())) {
            List<TargetingClauseResult> resultList = response.getResultList();
            int index = 0;
            for (TargetingClauseResult productAdResult : resultList) {
                if ("SUCCESS".equals(productAdResult.getCode())) {
                    amazonSdAdTargetings.get(index).setTargetId(String.valueOf(productAdResult.getTargetId()));
                } else {
                    amazonSdAdTargetings.get(index).setErrMsg(AmazonErrorUtils.getError(StringUtils.isNotBlank(productAdResult.getDetails())
                            ? productAdResult.getDetails() : productAdResult.getDescription()));
                }
                index++;
            }

            return ResultUtil.success();
        } else if (response.getError() != null) {
            errMsg = AmazonErrorUtils.getError(response.getError().getDetails());
        }

        return ResultUtil.error(errMsg);
    }

    public Result createNew(ShopAuth shop, AmazonAdProfile amazonAdProfile, String adGroupId, List<SdBatchNeTargetingVo> amazonSdAdTargetings) {
        if (shop == null) {
            log.error("shop is null");
            return ResultUtil.returnErr("没有CPC授权");
        }
        if (amazonAdProfile == null) {
            log.error("ad profile is null");
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        if (CollectionUtils.isEmpty(amazonSdAdTargetings)) {
            log.error("neTargeting list is empty");
            return ResultUtil.returnErr("请求参数错误");
        }
        List<NegativeTargetingExpression> netargetingExpressionList = new ArrayList<>();
        for (SdBatchNeTargetingVo e : amazonSdAdTargetings) {
            List<NegativeTargetingExpression> neExpression = new ArrayList<>();
            if (StringUtils.isNotBlank(e.getExpression())) {
                neExpression = JSONUtil.jsonToArray(e.getExpression(), NegativeTargetingExpression.class);
            }
            if (CollectionUtils.isNotEmpty(neExpression)) {
                netargetingExpressionList.addAll(neExpression);
            }
        }

        CreateNegativeTargetingNewResponse response = cpcApiHelper.call(shop, () ->
                NeProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createNew(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), Long.valueOf(adGroupId), StateEnum.ENABLED.value(), netargetingExpressionList, SDNeTargetingExpressionTypeEnum.MANUAL.getCode()));

        if (response == null) {
            log.error("invoke amazon remote sd neTargeting api error, response is null");
            throw new ServiceException("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "网络延迟，请稍后重试";
        if (CollectionUtils.isNotEmpty(response.getResult())) {
            List<CreateNegativeTargetingResult> resultList = response.getResult();
            int index = 0;
            for (CreateNegativeTargetingResult neTargetingResult : resultList) {
                if ("SUCCESS".equals(neTargetingResult.getCode())) {
                    amazonSdAdTargetings.get(index).setTargetId(String.valueOf(neTargetingResult.getTargetId()));
                } else {
                    amazonSdAdTargetings.get(index).setErrMsg(AmazonErrorUtils.getError(neTargetingResult.getDescription()));
                }
                index++;
            }

            return ResultUtil.success();
        } else if (response.getError() != null) {
            errMsg = AmazonErrorUtils.getError(response.getError().getDetails());
        }

        throw new ServiceException(errMsg);
    }

    /**
     * 同步所有的投放
     *
     * @param shop：
     */
    public void syncNeTargetings(ShopAuth shop, String campaignId) {
        syncNeTargetings(shop, campaignId, null, null, false);
    }

    public void syncNeByTargetId(ShopAuth shop, String targetId) {
        syncNeTargetings(shop, null, null, targetId, false);
    }

    public void syncNeTargetings(ShopAuth shop, String campaignId, String groupId, String targetId, boolean nullResThrowException) {
        syncNeTargetings(shop, campaignId, groupId, targetId, null, nullResThrowException);
    }


    public void syncNeTargetings(ShopAuth shop, String campaignId, String groupId, String targetId, List<SdStateEnum> stateList, boolean nullResThrowException) {
        syncNeTargetings(shop, campaignId, groupId, targetId, stateList, nullResThrowException, false) ;
    }
    public List<AmazonServingStatusDto> listByIds(Integer puid, Integer shopId, String groupIds){
        List<AmazonSdAdNeTargeting> amazons = amazonSdAdNeTargetingDao.listByTargetId(puid, shopId, StringUtil.stringToList(groupIds,","));
        return CollectionUtils.isEmpty(amazons) ? new ArrayList<>() : amazons.stream().map(key -> AmazonServingStatusDto.build(key.getTargetId(), key.getServingStatus(), "", "")).collect(Collectors.toList());
    }

    /**
     * 同步所有的投放
     *
     * @param shop：
     */
    public void syncNeTargetings(ShopAuth shop, String campaignId, String groupId, String targetId, List<SdStateEnum> stateList, boolean nullResThrowException, boolean isProxy) {
        if (shop == null) {
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncSdGroup--配置信息为空");
            return;
        }
        NeProductTargetingClient client = NeProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        if (isProxy) {
            client = NeProductTargetingClient.getInstance(true);
        }

        int startIndex = 0;
        int count = 500;
        ListNegativeTargetingClauseExResponse response;

        String stateFilter;
        if (CollectionUtils.isEmpty(stateList)) {
            stateFilter = Arrays.stream(SdStateEnum.values()).map(SdStateEnum::getStateType).collect(Collectors.joining(","));
        } else {
            stateFilter = stateList.stream().map(SdStateEnum::getStateType).collect(Collectors.joining(","));
        }

        while (true) {
            int finalSartIndex = startIndex;
            NeProductTargetingClient finalClient1 = client;
            response = cpcApiHelper.call(shop, () -> finalClient1.getListEx(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    finalSartIndex, count, stateFilter, campaignId, groupId, targetId));
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SD Netargeting rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                NeProductTargetingClient finalClient = client;
                response = cpcApiHelper.call(shop, () -> finalClient.getListEx(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        finalSartIndex, count, stateFilter, campaignId, groupId, targetId));
                retry++;
            }

            if (AmazonResponseUtil.isError(response) && nullResThrowException) {
                throw new ServiceException("sd syncNeTargetings error");
            }

            if (response == null || CollectionUtils.isEmpty(response.getTargetingClauseList())) {
                break;
            }

            int size = response.getTargetingClauseList().size();
            AmazonSdAdNeTargeting amazonSdAdTargeting;
            List<AmazonSdAdNeTargeting> amazonSdAdTargetings= new ArrayList<>(size);
            for (TargetingClause targetingClause : response.getTargetingClauseList()) {
                amazonSdAdTargeting = turnEntityToPO(targetingClause);
                if (StringUtils.isNotBlank(amazonSdAdTargeting.getTargetId())) {
                    amazonSdAdTargeting.setPuid(shop.getPuid());
                    amazonSdAdTargeting.setShopId(shop.getId());
                    amazonSdAdTargeting.setMarketplaceId(shop.getMarketplaceId());
                    amazonSdAdTargeting.setProfileId(amazonAdProfile.getProfileId());
                    amazonSdAdTargetings.add(amazonSdAdTargeting);
                }
            }

            if (amazonSdAdTargetings.size() > 0) {
                Map<String, AmazonSdAdNeTargeting> groupMap = amazonSdAdNeTargetingDao.listByTargetId(shop.getPuid(), shop.getId(),
                        amazonSdAdTargetings.stream().map(AmazonSdAdNeTargeting::getTargetId).collect(Collectors.toList()))
                        .stream().collect(Collectors.toMap(AmazonSdAdNeTargeting::getTargetId, Function.identity()));

                List<AmazonSdAdNeTargeting> insertList = new ArrayList<>();
                List<AmazonSdAdNeTargeting> updateList = new ArrayList<>();
                AmazonSdAdNeTargeting old;

                for (AmazonSdAdNeTargeting c : amazonSdAdTargetings) {
                    if (groupMap.containsKey(c.getTargetId())) {
                        old = groupMap.get(c.getTargetId());
                        if (StringUtils.isNotBlank(c.getState())) {
                            old.setState(c.getState());
                        }
                        if (StringUtils.isNotBlank(c.getType())) {
                            old.setType(c.getType());
                        }
                        if (StringUtils.isNotBlank(c.getTargetText())) {
                            old.setTargetText(c.getTargetText());
                        }
                        if (StringUtils.isNotBlank(c.getExpressionType())) {
                            old.setExpressionType(c.getExpressionType());
                        }
                        if (StringUtils.isNotBlank(c.getExpression())) {
                            old.setExpression(c.getExpression());
                        }
                        if (StringUtils.isNotBlank(c.getResolvedExpression())) {
                            old.setResolvedExpression(c.getResolvedExpression());
                        }
                        if (StringUtils.isNotBlank(c.getServingStatus())) {
                            old.setServingStatus(c.getServingStatus());
                        }
                        if (c.getCreationDate() != null) {
                            old.setCreationDate(c.getCreationDate());
                        }
                        if (c.getLastUpdatedDate() != null) {
                            old.setLastUpdatedDate(c.getLastUpdatedDate());
                        }
                        updateList.add(old);
                    } else {
                        c.setCreateInAmzup(0);
                        insertList.add(c);
                    }
                }

                try {
                    amazonSdAdNeTargetingDao.batchAdd(shop.getPuid(), insertList);
                    amazonSdAdNeTargetingDao.batchUpdate(shop.getPuid(), updateList);
                } catch (Exception e) {
                    log.error("syncSdTargeting:", e);
                }
            }

            if (size < count) {
                break;
            }

            startIndex += size;
        }
    }

    /**
     * 归档
     *
     * @param amazonSdAdTargeting：
     * @return ：Result
     */
    public Result archive(AmazonSdAdNeTargeting amazonSdAdTargeting) {
        if (amazonSdAdTargeting == null) {
            return ResultUtil.error("没有投放定位信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonSdAdTargeting.getShopId(), amazonSdAdTargeting.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        ArchiveNegativeTargetingClauseResponse response = cpcApiHelper.call(shop, () -> NeProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).archive(shopAuthService.getAdToken(shop),
                amazonSdAdTargeting.getProfileId(), shop.getMarketplaceId(), Long.valueOf(amazonSdAdTargeting.getTargetId())));

        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getResult() != null && response.getResult().getTargetId() != null) {
            return ResultUtil.success();
        }

        //处理返回结果中的错误信息
        String msg = "网络延迟，请稍后重试";
        if (response.getResult() != null) {
            msg = AmazonErrorUtils.getError(response.getResult().getDetails());
        }
        return ResultUtil.error(msg);
    }

    /**
     * 批量更新SD否定投放信息
     * @param shop
     * @param amazonAdProfile
     * @param amazonSdAdNeTargetings
     * @return
     */
    public Result<BatchResponseVo<BatchNeTargetVo, AmazonSdAdNeTargeting>> update(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonSdAdNeTargeting> amazonSdAdNeTargetings) {
        BatchResponseVo<BatchNeTargetVo, AmazonSdAdNeTargeting> batchResponseVo = new BatchResponseVo<>();
        List<TargetingClause> targetingClauseList = toNeSdAdNeTargeting(amazonSdAdNeTargetings);
        Map<String, AmazonSdAdNeTargeting> amazonSdAdNeTargetingMap = amazonSdAdNeTargetings.stream().collect(Collectors.toMap(AmazonSdAdNeTargeting::getTargetId, e -> e));
        UpdateNegativeTargetingClausesResponse response = cpcApiHelper.call(shop, () -> NeProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop),
                amazonAdProfile.getProfileId(), shop.getMarketplaceId(), targetingClauseList));

        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = cpcApiHelper.call(shop, () -> NeProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop),
                    amazonAdProfile.getProfileId(), shop.getMarketplaceId(), targetingClauseList));
        }

        List<AmazonSdAdNeTargeting> successList = Lists.newArrayList();
        List<BatchNeTargetVo> errorList = Lists.newArrayList();

        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getResultList() != null && response.getResultList().size() > 0) {
            List<TargetingClauseResult> resultList = response.getResultList();
            for (TargetingClauseResult targetingClauseResult : resultList) {

                if ("SUCCESS".equals(targetingClauseResult.getCode())) {
                    AmazonSdAdNeTargeting amazonSdAdNeTargetingSuccess = amazonSdAdNeTargetingMap.remove(String.valueOf(targetingClauseResult.getTargetId()));
                    if (amazonSdAdNeTargetingSuccess != null) {
                        amazonSdAdNeTargetingSuccess.setState(CpcStatusEnum.archived.name());
                        successList.add(amazonSdAdNeTargetingSuccess);
                    }

                } else {
                    AmazonSdAdNeTargeting amazonSdAdNeTargetingFail = amazonSdAdNeTargetingMap.remove(String.valueOf(targetingClauseResult.getTargetId()));
                    if (amazonSdAdNeTargetingFail != null) {
                        BatchNeTargetVo sdUpdateNeTargetVoError = new BatchNeTargetVo();
                        sdUpdateNeTargetVoError.setTargetId(amazonSdAdNeTargetingFail.getTargetId());
                        sdUpdateNeTargetVoError.setTargetText(amazonSdAdNeTargetingFail.getTargetText());

                        // 更新失败数据处理
                        if (StringUtils.isNotBlank(targetingClauseResult.getDescription())) {
                            sdUpdateNeTargetVoError.setFailReason(AmazonErrorUtils.getError(targetingClauseResult.getDescription()));
                        } else {
                            sdUpdateNeTargetVoError.setFailReason("更新失败，请稍后重试");
                        }
                        errorList.add(sdUpdateNeTargetVoError);
                    }
                }
            }
            // 剩余未匹配到的数据是接口未返回KeywordId的数据,一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonSdAdNeTargetingMap)) {
                amazonSdAdNeTargetingMap.forEach((k, v) -> {
                    BatchNeTargetVo sdUpdateNeTargetVoError = new BatchNeTargetVo();
                    sdUpdateNeTargetVoError.setTargetId(v.getTargetId());
                    sdUpdateNeTargetVoError.setTargetText(v.getTargetText());
                    sdUpdateNeTargetVoError.setFailReason("更新失败，请稍后重试");
                    errorList.add(sdUpdateNeTargetVoError);
                });
            }
        } else if (response.getError() != null && StringUtils.isNotBlank(response.getError().getDescription())) {
            return ResultUtil.error(AmazonErrorUtils.getError(response.getError().getDescription()));
        } else {
            //所有数据没有执行成功
            if (MapUtils.isNotEmpty(amazonSdAdNeTargetingMap)) {
                amazonSdAdNeTargetingMap.forEach((k, v) -> {
                    BatchNeTargetVo sdUpdateNeTargetVoError = new BatchNeTargetVo();
                    sdUpdateNeTargetVoError.setTargetId(v.getTargetId());
                    sdUpdateNeTargetVoError.setTargetText(v.getTargetText());
                    sdUpdateNeTargetVoError.setFailReason("更新失败，请稍后重试");
                    errorList.add(sdUpdateNeTargetVoError);
                });
            }
        }
        batchResponseVo.setCountNum(amazonSdAdNeTargetings.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);
        return ResultUtil.success(batchResponseVo);
    }


    /**
     * 转亚马逊接口对象
     * @param amazonSdAdNeTargetings
     * @return
     */
    private List<TargetingClause> toNeSdAdNeTargeting(List<AmazonSdAdNeTargeting> amazonSdAdNeTargetings) {
        List<TargetingClause> list = Lists.newArrayList();
        List<String> sdAdGroupIdList = amazonSdAdNeTargetings.stream().map(AmazonSdAdNeTargeting::getAdGroupId).collect(Collectors.toList());
        List<AmazonSdAdGroup> sdAdGroups = sdAdGroupDao.getByGroupIds(amazonSdAdNeTargetings.get(0).getPuid(), amazonSdAdNeTargetings.get(0).getShopId(), sdAdGroupIdList);
        Map<String, AmazonSdAdGroup> sdAdGroupMap = sdAdGroups.stream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, e -> e));
        TargetingClause targetingClause;
        for (AmazonSdAdNeTargeting sdAdNeTargeting : amazonSdAdNeTargetings) {
            targetingClause = new TargetingClause();
            if (StringUtils.isNotBlank(sdAdNeTargeting.getAdGroupId())) {
                targetingClause.setAdGroupId(Long.valueOf(sdAdNeTargeting.getAdGroupId()));
                AmazonSdAdGroup sdAdGroup = sdAdGroupMap.get(sdAdNeTargeting.getAdGroupId());
                if (StringUtils.isNotBlank(sdAdGroup.getCampaignId())) {
                    targetingClause.setCampaignId(Long.valueOf(sdAdGroup.getCampaignId()));
                }
            }
            targetingClause.setTargetId(Long.valueOf(sdAdNeTargeting.getTargetId()));
            targetingClause.setState(CpcStatusEnum.archived.name());
            list.add(targetingClause);
        }
        return list;
    }

    // 把接口返回的dto转换成po
    private AmazonSdAdNeTargeting turnEntityToPO(TargetingClause targetingClause) {
        AmazonSdAdNeTargeting amazonSdAdTargeting = new AmazonSdAdNeTargeting();
        if (targetingClause.getAdGroupId() != null) {
            amazonSdAdTargeting.setAdGroupId(targetingClause.getAdGroupId().toString());
        }
        if (targetingClause.getTargetId() != null) {
            amazonSdAdTargeting.setTargetId(targetingClause.getTargetId().toString());
        }

        amazonSdAdTargeting.setState(targetingClause.getState());
        amazonSdAdTargeting.setExpressionType(targetingClause.getExpressionType());

        if (CollectionUtils.isNotEmpty(targetingClause.getExpressions())) {
            amazonSdAdTargeting.setExpression(JSONUtil.objectToJson(targetingClause.getExpressions()));
        }

        // 确定投放的类型
        List<Expression> resolvedExpressions = targetingClause.getResolvedExpressions();
        if (CollectionUtils.isNotEmpty(resolvedExpressions)) {
            amazonSdAdTargeting.setResolvedExpression(JSONUtil.objectToJson(resolvedExpressions));
            for (Expression expression : targetingClause.getResolvedExpressions()) {
                if (ExpressionEnum.asinSameAs.value().equalsIgnoreCase(expression.getType())) {
                    amazonSdAdTargeting.setType(SdTargetTypeEnum.asin.getValue());
                    amazonSdAdTargeting.setTargetText(expression.getValue());
                    break;
                }
                if (ExpressionEnum.asinCategorySameAs.value().equalsIgnoreCase(expression.getType())) {
                    amazonSdAdTargeting.setType(SdTargetTypeEnum.category.getValue());
                    amazonSdAdTargeting.setTargetText(expression.getValue());
                    break;
                }
            }
        }

        amazonSdAdTargeting.setServingStatus(targetingClause.getServingStatus());

        if (StringUtils.isNotBlank(targetingClause.getCreationDate())) {
            amazonSdAdTargeting.setCreationDate(DateUtil.getDateByMillisecond(Long.valueOf(targetingClause.getCreationDate())));
        }
        if (StringUtils.isNotBlank(targetingClause.getLastUpdatedDate())) {
            amazonSdAdTargeting.setLastUpdatedDate(DateUtil.getDateByMillisecond(Long.valueOf(targetingClause.getLastUpdatedDate())));
        }

        return amazonSdAdTargeting;
    }
}
