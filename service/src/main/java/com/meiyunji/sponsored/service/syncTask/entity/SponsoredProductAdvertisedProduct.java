package com.meiyunji.sponsored.service.syncTask.entity;

import com.alibaba.fastjson.JSONReader;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Slf4j
public class SponsoredProductAdvertisedProduct {
    private LocalDate date;
    private BigDecimal attributedSalesSameSku1d;
    private String roasClicks14d;
    private Integer unitsSoldClicks1d;
    private BigDecimal attributedSalesSameSku14d;
    private BigDecimal sales7d;
    private BigDecimal attributedSalesSameSku30d;
    private Integer kindleEditionNormalizedPagesRoyalties14d;
    private Integer unitsSoldSameSku1d;
    private String campaignStatus;
    private String advertisedSku;
    private Integer salesOtherSku7d;
    private Integer purchasesSameSku7d;
    private Integer campaignBudgetAmount;
    private Integer purchases7d;
    private Integer unitsSoldSameSku30d;
    private String costPerClick;
    private Integer unitsSoldClicks14d;
    private String adGroupName;
    private Long campaignId;
    private Integer clickThroughRate;
    private Integer kindleEditionNormalizedPagesRead14d;
    private String acosClicks14d;
    private Integer unitsSoldClicks30d;
    private Long portfolioId;
    private Long adId;
    private String campaignBudgetCurrencyCode;
    private String roasClicks7d;
    private Integer unitsSoldSameSku14d;
    private Integer unitsSoldClicks7d;
    private BigDecimal attributedSalesSameSku7d;
    private BigDecimal sales1d;
    private Long adGroupId;
    private Integer purchasesSameSku14d;
    private Integer unitsSoldOtherSku7d;
    private Integer spend;
    private Integer purchasesSameSku1d;
    private String campaignBudgetType;
    private String advertisedAsin;
    private Integer purchases1d;
    private Integer unitsSoldSameSku7d;
    private BigDecimal cost;
    private BigDecimal sales14d;
    private String acosClicks7d;
    private BigDecimal sales30d;
    private Integer impressions;
    private Integer purchasesSameSku30d;
    private Integer purchases14d;
    private Integer purchases30d;
    private Integer clicks;
    private String campaignName;

    public SponsoredProductAdvertisedProduct readFromJsonReader(JSONReader jsonReader) {
        while (jsonReader.hasNext()) {
            String key = jsonReader.readString();
            switch (key) {
                case "date":
                    this.date = LocalDate.parse(jsonReader.readString());
                    break;
                case "attributedSalesSameSku1d":
                    String attributedSalesSameSku1dValue = jsonReader.readString();
                    this.attributedSalesSameSku1d = StringUtils.isNotBlank(attributedSalesSameSku1dValue)? new BigDecimal(attributedSalesSameSku1dValue) : BigDecimal.ZERO;
                    break;
                case "roasClicks14d":
                    this.roasClicks14d = jsonReader.readString();
                    break;
                case "unitsSoldClicks1d":
                    this.unitsSoldClicks1d = jsonReader.readInteger();
                    break;
                case "attributedSalesSameSku14d":
                    String attributedSalesSameSku14dValue = jsonReader.readString();
                    this.attributedSalesSameSku14d = StringUtils.isNotBlank(attributedSalesSameSku14dValue)? new BigDecimal(attributedSalesSameSku14dValue) : BigDecimal.ZERO;
                    break;
                case "sales7d":
                    String sales7dValue =  jsonReader.readString();
                    this.sales7d = sales7dValue != null ? new BigDecimal(sales7dValue) : null;
                    break;
                case "attributedSalesSameSku30d":
                    String attributedSalesSameSku30ddValue = jsonReader.readString();
                    this.attributedSalesSameSku30d = StringUtils.isNotBlank(attributedSalesSameSku30ddValue)? new BigDecimal(attributedSalesSameSku30ddValue) : BigDecimal.ZERO;
                    break;
                case "kindleEditionNormalizedPagesRoyalties14d":
                    this.kindleEditionNormalizedPagesRoyalties14d = jsonReader.readInteger();
                    break;
                case "unitsSoldSameSku1d":
                    this.unitsSoldSameSku1d = jsonReader.readInteger();
                    break;
                case "campaignStatus":
                    this.campaignStatus = jsonReader.readString();
                    break;
                case "advertisedSku":
                    this.advertisedSku = jsonReader.readString();
                    break;
                case "salesOtherSku7d":
                    this.salesOtherSku7d = jsonReader.readInteger();
                    break;
                case "purchasesSameSku7d":
                    this.purchasesSameSku7d = jsonReader.readInteger();
                    break;
                case "campaignBudgetAmount":
                    this.campaignBudgetAmount = jsonReader.readInteger();
                    break;
                case "purchases7d":
                    this.purchases7d = jsonReader.readInteger();
                    break;
                case "unitsSoldSameSku30d":
                    this.unitsSoldSameSku30d = jsonReader.readInteger();
                    break;
                case "costPerClick":
                    this.costPerClick = jsonReader.readString();
                    break;
                case "unitsSoldClicks14d":
                    this.unitsSoldClicks14d = jsonReader.readInteger();
                    break;
                case "adGroupName":
                    this.adGroupName = jsonReader.readString();
                    break;
                case "campaignId":
                    this.campaignId = jsonReader.readLong();
                    break;
                case "clickThroughRate":
                    this.clickThroughRate = jsonReader.readInteger();
                    break;
                case "kindleEditionNormalizedPagesRead14d":
                    this.kindleEditionNormalizedPagesRead14d = jsonReader.readInteger();
                    break;
                case "acosClicks14d":
                    this.acosClicks14d = jsonReader.readString();
                    break;
                case "unitsSoldClicks30d":
                    this.unitsSoldClicks30d = jsonReader.readInteger();
                    break;
                case "portfolioId":
                    this.portfolioId = jsonReader.readLong();
                    break;
                case "adId":
                    this.adId = jsonReader.readLong();
                    break;
                case "campaignBudgetCurrencyCode":
                    this.campaignBudgetCurrencyCode = jsonReader.readString();
                    break;
                case "roasClicks7d":
                    this.roasClicks7d = jsonReader.readString();
                    break;
                case "unitsSoldSameSku14d":
                    this.unitsSoldSameSku14d = jsonReader.readInteger();
                    break;
                case "unitsSoldClicks7d":
                    this.unitsSoldClicks7d = jsonReader.readInteger();
                    break;
                case "attributedSalesSameSku7d":
                    String attributedSalesSameSku7dValue = jsonReader.readString();
                    this.attributedSalesSameSku7d = attributedSalesSameSku7dValue != null ?
                            new BigDecimal(attributedSalesSameSku7dValue) : BigDecimal.ZERO;
                    break;
                case "sales1d":
                    String sales1dValue = jsonReader.readString();
                    this.sales1d = StringUtils.isNotBlank(sales1dValue)? new BigDecimal(sales1dValue) : BigDecimal.ZERO;
                    break;
                case "adGroupId":
                    this.adGroupId = jsonReader.readLong();
                    break;
                case "purchasesSameSku14d":
                    this.purchasesSameSku14d = jsonReader.readInteger();
                    break;
                case "unitsSoldOtherSku7d":
                    this.unitsSoldOtherSku7d = jsonReader.readInteger();
                    break;
                case "spend":
                    this.spend = jsonReader.readInteger();
                    break;
                case "purchasesSameSku1d":
                    this.purchasesSameSku1d = jsonReader.readInteger();
                    break;
                case "campaignBudgetType":
                    this.campaignBudgetType = jsonReader.readString();
                    break;
                case "advertisedAsin":
                    this.advertisedAsin = jsonReader.readString();
                    break;
                case "purchases1d":
                    this.purchases1d = jsonReader.readInteger();
                    break;
                case "unitsSoldSameSku7d":
                    this.unitsSoldSameSku7d = jsonReader.readInteger();
                    break;
                case "cost":
                    String costValue = jsonReader.readString();
                    this.cost = costValue != null ?
                            new BigDecimal(costValue) : BigDecimal.ZERO;
                    break;
                case "sales14d":
                    String sales14dValue = jsonReader.readString();
                    this.sales14d = StringUtils.isNotBlank(sales14dValue)? new BigDecimal(sales14dValue) : BigDecimal.ZERO;
                    break;
                case "acosClicks7d":
                    this.acosClicks7d = jsonReader.readString();
                    break;
                case "sales30d":
                    String sales30dValue = jsonReader.readString();
                    this.sales30d = StringUtils.isNotBlank(sales30dValue)? new BigDecimal(sales30dValue) : BigDecimal.ZERO;
                    break;
                case "impressions":
                    this.impressions = jsonReader.readInteger();
                    break;
                case "purchasesSameSku30d":
                    this.purchasesSameSku30d = jsonReader.readInteger();
                    break;
                case "purchases14d":
                    this.purchases14d = jsonReader.readInteger();
                    break;
                case "purchases30d":
                    this.purchases30d = jsonReader.readInteger();
                    break;
                case "clicks":
                    this.clicks = jsonReader.readInteger();
                    break;
                case "campaignName":
                    this.campaignName = jsonReader.readString();
                    break;
                default:
                    log.info("亚马逊新增加字符,跳过 key:{}", key);
                    jsonReader.readString();
                    break;
            }
        }
        return this;
    }
}
