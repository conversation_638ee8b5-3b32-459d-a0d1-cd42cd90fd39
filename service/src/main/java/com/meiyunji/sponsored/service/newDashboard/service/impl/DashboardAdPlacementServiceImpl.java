package com.meiyunji.sponsored.service.newDashboard.service.impl;

import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdPlacementResponseVo;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdCampaignPlacementReportDao;
import com.meiyunji.sponsored.service.enums.AmazonAdvertisePredicateEnum;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdPlacementDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByRateEnum;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdPlacementService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardBaseReqVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-04-01 18:40
 */
@Service
@Slf4j
public class DashboardAdPlacementServiceImpl implements IDashboardAdPlacementService {
    @Autowired
    private IOdsAmazonAdCampaignPlacementReportDao odsAmazonAdCampaignPlacementReportDao;

    @Override
    public List<DashboardAdPlacementResponseVo> queryAdPlacementCharts(DashboardBaseReqVo reqVo) {
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(reqVo.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(reqVo.getMarketplaceIdList());
        }
        List<DashboardAdPlacementDataDto> baseDataList = odsAmazonAdCampaignPlacementReportDao.queryAdPlacementCharts(reqVo.getPuid(),
                reqVo.getMarketplaceIdList(), reqVo.getShopIdList(), reqVo.getCurrency(),reqVo.getStartDate(), reqVo.getEndDate(),
                siteToday, reqVo.getSiteToday(), reqVo.getPortfolioIds(), reqVo.getCampaignIds());
        Map<String, DashboardAdPlacementDataDto> baseDataMap = baseDataList.stream()
                .collect(Collectors.toMap(DashboardAdPlacementDataDto::getCampaignType, Function.identity(), (oldVal, newVal) -> newVal));
        //所有数据计算完毕，构建grpc响应数据
        List<DashboardAdPlacementResponseVo> list = new ArrayList<>(baseDataMap.size());
        for (AmazonAdvertisePredicateEnum predicateEnum : AmazonAdvertisePredicateEnum.values()) {
            DashboardAdPlacementDataDto baseData = baseDataMap.getOrDefault(predicateEnum.getValue(), buildDefaultData(predicateEnum));
            baseData.setDataType(predicateEnum.getCode());
            CalculateAdDataUtil.calAdCalData(baseData);
            list.add(buildGrpcResponseVo(baseData).build());
        }
        return list;
    }

    /**
     * 单条数据构建grpc响应对象
     *
     * @param dataDto
     * @return
     */
    private DashboardAdPlacementResponseVo.Builder buildGrpcResponseVo(DashboardAdPlacementDataDto dataDto) {
        DashboardAdPlacementResponseVo.Builder builder = DashboardAdPlacementResponseVo.newBuilder();
        //String类型直接拷贝
        BeanUtils.copyProperties(dataDto, builder);
        //手动设置的类型
        builder.setDataType(dataDto.getDataType());
        builder.setCost(CalculateUtil.formatDecimal(dataDto.getCost()));
        builder.setTotalSales(CalculateUtil.formatDecimal(dataDto.getTotalSales()));
        builder.setImpressions(String.valueOf(dataDto.getImpressions()));
        builder.setClicks(String.valueOf(dataDto.getClicks()));
        builder.setOrderNum(String.valueOf(dataDto.getOrderNum()));
        builder.setSaleNum(String.valueOf(dataDto.getSaleNum()));
        builder.setAcos(CalculateUtil.formatPercent(dataDto.getAcos()));
        builder.setRoas(CalculateUtil.formatDecimal(dataDto.getRoas()));
        builder.setClickRate(CalculateUtil.formatPercent(dataDto.getClickRate()));
        builder.setConversionRate(CalculateUtil.formatPercent(dataDto.getConversionRate()));
        builder.setCpc(CalculateUtil.formatDecimal(dataDto.getCpc()));
        builder.setCpa(CalculateUtil.formatDecimal(dataDto.getCpa()));
        return builder;
    }

    private DashboardAdPlacementDataDto buildDefaultData(AmazonAdvertisePredicateEnum predicateEnum) {
        DashboardAdPlacementDataDto adPlacementDataDto = new DashboardAdPlacementDataDto();
        CalculateAdDataUtil.buildZeroAdBaseData(adPlacementDataDto);
        return adPlacementDataDto;
    }
}
