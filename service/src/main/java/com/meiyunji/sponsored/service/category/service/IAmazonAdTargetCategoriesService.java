package com.meiyunji.sponsored.service.category.service;


import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.service.category.vo.TargetCategoryParam;
import com.meiyunji.sponsored.service.category.vo.TargetCategoryVo;

public interface IAmazonAdTargetCategoriesService {
    Result<Page<TargetCategoryVo>> getPageList(TargetCategoryParam param);
}
