package com.meiyunji.sponsored.service.multiPlatform.tiktok.service;

import com.meiyunji.sellfox.core.exception.BizServiceException;
import com.meiyunji.sellfox.right.utils.ShopRightUtils;
import com.meiyunji.sponsored.service.multiPlatform.shop.enums.MultiPlatformTypeEnum;
import com.meiyunji.sponsored.service.multiPlatform.shop.service.IMultiPlatformShopService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class TikTokCommonService {

    @Autowired
    private ShopRightUtils shopRightUtils;
    @Autowired
    private IMultiPlatformShopService multiPlatformShopService;

    public List<Integer> checkShopRight(int uid, List<Integer> shopIdList) {
        List<Integer> shopIds = shopRightUtils.checkMultiPlatformShopRight(uid, shopIdList, MultiPlatformTypeEnum.TIKTOK.getCode());
        if (CollectionUtils.isEmpty(shopIds)) {
            throw new BizServiceException("无店铺权限");
        }
        return shopIds;
    }

    public List<Integer> checkAdShopAuthList(int puid, List<Integer> shopIdList) {
        List<Integer> shopIds = multiPlatformShopService.getTiktokAdShopAuthList(puid, shopIdList);
        if (CollectionUtils.isEmpty(shopIds)) {
            throw new BizServiceException("店铺未授权广告权限");
        }
        return shopIds;
    }

}
