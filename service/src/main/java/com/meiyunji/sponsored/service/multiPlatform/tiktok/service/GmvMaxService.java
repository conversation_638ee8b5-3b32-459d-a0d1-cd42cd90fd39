package com.meiyunji.sponsored.service.multiPlatform.tiktok.service;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.core.exception.BizServiceException;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.service.multiPlatform.shop.dao.IMultiPlatformShopAuthDao;
import com.meiyunji.sponsored.service.multiPlatform.shop.enums.MultiPlatformTypeEnum;
import com.meiyunji.sponsored.service.multiPlatform.shop.po.MultiPlatformShopAuth;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.client.GmvMaxApiClient;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.dao.TikTokAdvertiserAccountDao;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.dao.TikTokGmvMaxCampaignDao;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.dao.TikTokStoreInfoDao;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.dao.TikTokStoreTokenDao;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.*;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokAdvertiserAccount;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokGmvMaxCampaign;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokGmvMaxCampaignPage;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokStoreInfo;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request.*;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.response.GmvMaxCampaignListResp;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.response.GmvMaxIdentityGetResponse;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.response.GmvMaxVideoResponse;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.response.ShopProductResp;
import com.meiyunji.sponsored.service.util.AdPageUtil;
import com.tiktok.advertising.model.gmv_max.Store;
import com.tiktok.advertising.model.gmv_max.Video;
import com.tiktok.advertising.model.gmv_max.enums.CampaignGetPrimaryStatusEnum;
import com.tiktok.advertising.model.gmv_max.enums.CampaignOperationStatusEnum;
import com.tiktok.advertising.model.gmv_max.enums.ProductListGmvMaAdsStatusEnum;
import com.tiktok.advertising.model.gmv_max.enums.ProductListStatusEnum;
import com.tiktok.advertising.model.gmv_max.request.CampaignStatusUpdateRequest;
import com.tiktok.advertising.model.gmv_max.request.FilteringStoreProductGet;
import com.tiktok.advertising.model.gmv_max.request.GmvMaxCampaignCreateRequest;
import com.tiktok.advertising.model.gmv_max.request.GmvMaxCampaignUpdateRequest;
import com.tiktok.advertising.model.gmv_max.response.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.security.SecureRandom;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GmvMaxService {

    private static final SecureRandom random = new SecureRandom();
    private static final int BIT_LENGTH = 64;

    @Autowired
    private GmvMaxApiClient apiClient;
    @Autowired
    private TikTokGmvMaxCampaignDao tiktokGmvMaxCampaignDao;
    @Autowired
    private IMultiPlatformShopAuthDao multiPlatformShopAuthDao;
    @Autowired
    private TikTokStoreInfoDao tiktokStoreInfoDao;
    @Autowired
    private TikTokStoreTokenDao tiktokStoreTokenDao;
    @Autowired
    private TikTokAdvertiserAccountDao tikTokAdvertiserAccountDao;
    @Autowired
    private TikTokSyncService tikTokSyncService;
    @Resource
    private ThreadPoolTaskExecutor tiktokAddOrUpdateCampaignSyncExecutor;
    @Autowired
    private RedisTemplate<String, Object> redisTemplateNew;

    /**
     * 生成64位整数的字符串格式Request ID
     */
    public static String generateRequestId() {
        // 生成64位随机数，范围0到2^64-1
        BigInteger bigInteger = new BigInteger(BIT_LENGTH, random);
        return bigInteger.toString();
    }

    private String getAccessToken(Integer puid, String advertiserId) {
        List<TikTokStoreInfo> tikTokStoreInfos = tiktokStoreInfoDao.listByAdvertiserId(puid, advertiserId);
        if (CollectionUtils.isEmpty(tikTokStoreInfos)) {
            throw new BizServiceException("没有已授权的店铺");
        }
        TikTokStoreInfo tikTokStoreInfo = tikTokStoreInfos.get(0);
        return getAccessToken(puid, tikTokStoreInfo.getShopId());
    }

    private String getAccessToken(Integer puid, Integer shopId) {
        return tiktokStoreTokenDao.getAccessTokenByShopId(puid, shopId);
    }

    private String getAdvertiserOwnerBcId(Integer puid, String advertiserId) {
        return tikTokAdvertiserAccountDao.getByPuidAndAdvertiserId(puid, advertiserId).getOwnerBcId();
    }

    private String getAdvertiserName(Integer puid, String advertiserId) {
        return tikTokAdvertiserAccountDao.getByPuidAndAdvertiserId(puid, advertiserId).getName();
    }

    private String getStoreId(Integer puid, Integer shopId, String advertiserId) {
        TikTokStoreInfo store = tiktokStoreInfoDao.getStore(puid, shopId, advertiserId);
        return store.getStoreId();
    }

    private String getShopName(Integer puid, Integer shopId) {
        MultiPlatformShopAuth shopAuth = multiPlatformShopAuthDao.listByIdAndPlatform(puid, shopId, MultiPlatformTypeEnum.TIKTOK.getCode());
        return Objects.nonNull(shopAuth) ? shopAuth.getName() : "";
    }

    private TikTokStoreInfo getStore(Integer puid, Integer shopId, String advertiserId) {
        return tiktokStoreInfoDao.getStore(puid, shopId, advertiserId);
    }

    public List<GmvMaxShop> gmvStoreList(Integer puid, String advertiserId) {
        if (StringUtils.isBlank(advertiserId)) {
            return Collections.emptyList();
        }
        String accessToken = getAccessToken(puid, advertiserId);
        List<Store> storeList = apiClient.gmvStoreList(accessToken, advertiserId);
        if (CollectionUtils.isEmpty(storeList)) {
            return Collections.emptyList();
        }
        List<String> storeIds = storeList.stream().map(Store::getStoreId).distinct().collect(Collectors.toList());

        List<TikTokStoreInfo> tikTokStoreInfos = tiktokStoreInfoDao.listByStoreIds(puid, advertiserId, storeIds);
        if (CollectionUtils.isEmpty(tikTokStoreInfos)) {
            return Collections.emptyList();
        }
        // 一个store 可能有多个shop
        Map<String, Integer> storeShopIdMap = StreamUtil.toMap(tikTokStoreInfos, TikTokStoreInfo::getStoreId, TikTokStoreInfo::getShopId);
        List<Integer> shopIds = tikTokStoreInfos.stream().map(TikTokStoreInfo::getShopId).distinct().collect(Collectors.toList());
        Map<Integer, String> shopNameMap = multiPlatformShopAuthDao.listByPuidAndShopIds(puid, shopIds).stream().collect(Collectors.toMap(MultiPlatformShopAuth::getId, MultiPlatformShopAuth::getName));

        return storeList.stream().map(i -> {
            String storeId = i.getStoreId();
            if (!storeShopIdMap.containsKey(storeId)) {
                return null;
            }
            Integer shopId = storeShopIdMap.get(storeId);
            String shopName = shopNameMap.getOrDefault(shopId, "");
            GmvMaxShop gmvMaxShop = new GmvMaxShop();
            gmvMaxShop.setShopId(shopId);
            gmvMaxShop.setShopName(shopName);
            gmvMaxShop.setStoreId(storeId);
            gmvMaxShop.setStoreName(i.getStoreName());
            gmvMaxShop.setStoreAuthorizedBcId(i.getStoreAuthorizedBcId());

            boolean canCreateGmvMax = Boolean.TRUE.equals(i.getIsGmvMaxAvailable())
                    && StringUtils.equalsIgnoreCase("ACTIVE", i.getStoreStatus())
                    && Objects.nonNull(i.getExclusiveAuthorizedAdvertiserInfo())
                    && StringUtils.equalsIgnoreCase(advertiserId, i.getExclusiveAuthorizedAdvertiserInfo().getAdvertiserId())
                    && StringUtils.equalsIgnoreCase("STATUS_ENABLE", i.getExclusiveAuthorizedAdvertiserInfo().getAdvertiserStatus());

            gmvMaxShop.setCanCreateGmvMax(canCreateGmvMax);
            return gmvMaxShop;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public Boolean shopAdUsageCheck(Integer puid, String advertiserId, Integer shopId) {
        String accessToken = getAccessToken(puid, shopId);
        String storeId = getStoreId(puid, shopId, advertiserId);
        return Boolean.TRUE.equals(apiClient.shopAdUsageCheck(advertiserId, storeId, accessToken));
    }

    public ShopProductResp shopProductList(Integer puid, ShopProductParam param) {
        String accessToken = getAccessToken(puid, param.getShopId());
        String storeId = getStoreId(puid, param.getShopId(), param.getAdvertiserId());
//        String bcId = getAdvertiserOwnerBcId(puid, param.getAdvertiserId());
        String bcId = param.getStoreAuthorizedBcId();

        FilteringStoreProductGet filtering = new FilteringStoreProductGet();
        if (CollectionUtils.isNotEmpty(param.getSpuIdList())) {
            filtering.setItemGroupIds(param.getSpuIdList());
        }
        if (StringUtils.isNotBlank(param.getProductName())) {
            filtering.setProductName(param.getProductName());
        }
        filtering.setAdCreationEligible("GMV_MAX");
        //接口spuIdList文档限制传10个（实际测试调接口50个以上都没问题，若后续文档增加上限直接去掉该if分支），需要多次调用接口。
        if (CollectionUtils.isNotEmpty(param.getSpuIdList()) && param.getSpuIdList().size() > 10) {
            List<List<String>> spuIdsList = Lists.partition(param.getSpuIdList(), 10);
            ShopProductResp resp = new ShopProductResp();
            List<ShopProduct> shopProducts = new ArrayList<>();
            for (List<String> spuIds : spuIdsList) {
                filtering.setItemGroupIds(spuIds);
                StoreProductResponse response = apiClient.storeProduct(param, accessToken, bcId, storeId, filtering);
                if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getStoreProducts())) {
                    return ShopProductResp.emptyInstance(param.getPageNo(), param.getPageSize());
                }
                shopProducts.addAll(response.getStoreProducts().stream().map(i -> {
                    ShopProduct shopProduct = new ShopProduct();
                    shopProduct.setSpuId(i.getItemGroupId());
                    shopProduct.setTitle(i.getTitle());
                    shopProduct.setImageUrl(i.getProductImageUrl());
                    shopProduct.setMinPrice(i.getMinPrice());
                    shopProduct.setMaxPrice(i.getMaxPrice());
                    shopProduct.setCurrency(i.getCurrency());
                    shopProduct.setSales(i.getHistoricalSales() == null ? 0L : i.getHistoricalSales());
                    shopProduct.setAdForGmvMax(ProductListStatusEnum.AVAILABLE.getValue().equalsIgnoreCase(i.getStatus()) && ProductListGmvMaAdsStatusEnum.UNOCCUPIED.getValue().equalsIgnoreCase(i.getGmvMaxAdsStatus()));
                    shopProduct.setGmvMaxAdsStatus(ProductListGmvMaAdsStatusEnum.OCCUPIED.getValue().equalsIgnoreCase(i.getGmvMaxAdsStatus()));
                    shopProduct.setRunningCustomShopAds(Boolean.TRUE.equals(i.getIsRunningCustomShopAds()));
                    shopProduct.setStatus(ProductListStatusEnum.AVAILABLE.getValue().equalsIgnoreCase(i.getStatus()));
                    return shopProduct;
                }).collect(Collectors.toList()));
            }
            resp.setProductList(shopProducts);
            resp.setPageNo(param.getPageNo());
            resp.setPageSize(param.getPageSize());
            resp.setTotalPage(1);
            resp.setTotalSize(shopProducts.size());
            return resp;
        }

        StoreProductResponse response = apiClient.storeProduct(param, accessToken, bcId, storeId, filtering);
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getStoreProducts())) {
            return ShopProductResp.emptyInstance(param.getPageNo(), param.getPageSize());
        }
        ShopProductResp resp = new ShopProductResp();
        List<ShopProduct> shopProducts = response.getStoreProducts().stream().map(i -> {
            ShopProduct shopProduct = new ShopProduct();
            shopProduct.setSpuId(i.getItemGroupId());
            shopProduct.setTitle(i.getTitle());
            shopProduct.setImageUrl(i.getProductImageUrl());
            shopProduct.setMinPrice(i.getMinPrice());
            shopProduct.setMaxPrice(i.getMaxPrice());
            shopProduct.setCurrency(i.getCurrency());
            shopProduct.setSales(i.getHistoricalSales() == null ? 0L : i.getHistoricalSales());
            shopProduct.setAdForGmvMax(ProductListStatusEnum.AVAILABLE.getValue().equalsIgnoreCase(i.getStatus()) && ProductListGmvMaAdsStatusEnum.UNOCCUPIED.getValue().equalsIgnoreCase(i.getGmvMaxAdsStatus()));
            shopProduct.setGmvMaxAdsStatus(ProductListGmvMaAdsStatusEnum.OCCUPIED.getValue().equalsIgnoreCase(i.getGmvMaxAdsStatus()));
            shopProduct.setRunningCustomShopAds(Boolean.TRUE.equals(i.getIsRunningCustomShopAds()));
            shopProduct.setStatus(ProductListStatusEnum.AVAILABLE.getValue().equalsIgnoreCase(i.getStatus()));
            return shopProduct;
        }).collect(Collectors.toList());
        resp.setProductList(shopProducts);
        resp.setPageNo(response.getPageInfo().getPage());
        resp.setPageSize(response.getPageInfo().getPageSize());
        resp.setTotalPage(response.getPageInfo().getTotalPage());
        resp.setTotalSize(response.getPageInfo().getTotalNumber());
        return resp;
    }

    public GmvMaxIdentityGetResponse identityGet(Integer puid, String advertiserId, Integer shopId, String bcId, Integer pageNo, Integer pageSize) {
        GmvMaxIdentityGetResponse identityGetResponse = new GmvMaxIdentityGetResponse();
        identityGetResponse.setPageNum(pageNo);
        identityGetResponse.setPageSize(pageSize);
        identityGetResponse.setTotalPage(0);
        identityGetResponse.setTotalNum(0);
        identityGetResponse.setIdentityList(new ArrayList<>());

        String accessToken = getAccessToken(puid, shopId);
        String storeId = getStoreId(puid, shopId, advertiserId);
        IdentityGetResponse response = apiClient.identityGetByRedis(puid, accessToken, advertiserId, storeId, bcId);
        if (response == null || CollectionUtils.isEmpty(response.getIdentityList())) {
            return identityGetResponse;
        }
        Page<com.tiktok.advertising.model.gmv_max.Identity> page = new Page<>();
        List<com.tiktok.advertising.model.gmv_max.Identity> list = AdPageUtil.getOrderPage(pageNo, pageSize, response.getIdentityList(), null, page);
        List<Identity> identityList = list.stream().map(i -> {
            Identity identity = new Identity();
            identity.setIdentityId(i.getIdentityId());
            identity.setIdentityType(Identity.IdentityTypeEnum.getDescByType(i.getIdentityType()));
            identity.setDisplayName(i.getDisplayName());
            identity.setProductGmvMaxAvailable(Boolean.TRUE.equals(i.getProductGmvMaxAvailable()));
            identity.setIdentityAuthorizedBcId(i.getIdentityAuthorizedBcId());
            identity.setIdentityAuthorizedShopId(i.getIdentityAuthorizedShopId());
            identity.setStoreId(i.getStoreId());
            return identity;
        }).collect(Collectors.toList());
        identityGetResponse.setIdentityList(identityList);
        identityGetResponse.setTotalPage(page.getTotalPage());
        identityGetResponse.setTotalNum(page.getTotalSize());
        return identityGetResponse;
    }

    public GmvMaxVideoResponse gmvMaxVideoGet(Integer puid, GmvMaxVideoGetParam param) {
        String accessToken = getAccessToken(puid, param.getShopId());
        String advertiserId = param.getAdvertiserId();
        String storeId = getStoreId(puid, param.getShopId(), advertiserId);
        String bcId = param.getStoreAuthorizedBcId();

        Boolean needAuthCodeVideo = Boolean.TRUE;
        IdentityGetResponse identityResponse = apiClient.identityGet(accessToken, advertiserId, storeId, bcId);

        VideoGetResponse response = apiClient.videoGet(accessToken, param, storeId, bcId,
                needAuthCodeVideo, identityResponse.getIdentityList().stream().limit(20).collect(Collectors.toList()));
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getItemList())) {
            return GmvMaxVideoResponse.emptyInstance(param.getPageNo(), param.getPageSize());
        }

        GmvMaxVideoResponse resp = new GmvMaxVideoResponse();
        List<GmvMaxVideoItem> list = response.getItemList().stream().map(i -> {
            GmvMaxVideoItem item = new GmvMaxVideoItem();
            item.setItemId(i.getItemId());
            item.setText(StringUtils.isBlank(i.getText()) ? "-" : i.getText());
            item.setSpuIdList(i.getSpuIdList());
            item.setId(i.getItemId() + "#" + String.join(",", param.getSpuIdList()));

            com.tiktok.advertising.model.gmv_max.Identity tkIdentityInfo = i.getIdentityInfo();
            if (Objects.nonNull(tkIdentityInfo)) {
                Identity identity = new Identity();
                identity.setIdentityId(StringUtils.isBlank(tkIdentityInfo.getIdentityId()) ? "-" : tkIdentityInfo.getIdentityId());
                identity.setIdentityType(Identity.IdentityTypeEnum.getDescByType(tkIdentityInfo.getIdentityType()));
                identity.setDisplayName(StringUtils.isBlank(tkIdentityInfo.getDisplayName()) ? "-" : tkIdentityInfo.getDisplayName());
                identity.setUserName(StringUtils.isBlank(tkIdentityInfo.getUserName()) ? "-" : tkIdentityInfo.getUserName());
                identity.setProductGmvMaxAvailable(Boolean.TRUE.equals(tkIdentityInfo.getProductGmvMaxAvailable()));
                identity.setIdentityAuthorizedBcId(tkIdentityInfo.getIdentityAuthorizedBcId());
                identity.setIdentityAuthorizedShopId(tkIdentityInfo.getIdentityAuthorizedShopId());
                identity.setStoreId(tkIdentityInfo.getStoreId());
                identity.setProfileImage(tkIdentityInfo.getProfileImage());
                item.setIdentityInfo(identity);
            }

            Video videoInfo = i.getVideoInfo();
            if (Objects.nonNull(videoInfo)) {
                GmvMaxVideo video = new GmvMaxVideo();
                video.setVideoId(videoInfo.getVideoId());
                video.setVideoCoverUrl(videoInfo.getVideoCoverUrl());
                video.setPreviewUrl(videoInfo.getPreviewUrl());
                video.setHeight(videoInfo.getHeight());
                video.setWidth(videoInfo.getWidth());
                video.setDuration(videoInfo.getDuration());
                video.setSize(videoInfo.getSize());
                video.setFormat(videoInfo.getFormat());
                item.setVideoInfo(video);
            }

            return item;
        }).collect(Collectors.toList());
        resp.setItemList(list);
        resp.setPageNum(response.getPageInfo().getPage());
        resp.setPageSize(response.getPageInfo().getPageSize());
        resp.setTotalPage(response.getPageInfo().getTotalPage());
        resp.setTotalNum(response.getPageInfo().getTotalNumber());
        return resp;
    }

    public String create(Integer puid, GmvMaxCampaignCreateParam param) {
        String accessToken = getAccessToken(puid, param.getShopId());
        String storeId = getStoreId(puid, param.getShopId(), param.getAdvertiserId());
//        String bcId = getAdvertiserOwnerBcId(puid, param.getAdvertiserId());
        String bcId = param.getStoreAuthorizedBcId();

        GmvMaxCampaignCreateRequest request = new GmvMaxCampaignCreateRequest();
        request.setRequestId(generateRequestId());
        request.setStoreId(storeId);
        request.setStoreAuthorizedBcId(bcId);
        request.setAdvertiserId(param.getAdvertiserId());
        // 当前只支持商品类型广告，暂时写死
        request.setShoppingAdsType("PRODUCT");
        request.setProductSpecificType(param.getProductSpecificType());
        if (CollectionUtils.isNotEmpty(param.getSpuIdList())) {
            request.setItemGroupIds(param.getSpuIdList());
        }
        request.setOptimizationGoal("VALUE");
        request.setDeepBidType("VO_MIN_ROAS");
        request.setRoasBid(param.getRoasBid());
        request.setBudget(param.getDailyBudget());
        request.setScheduleStartTime(param.getScheduleStartTime());
        if (StringUtils.isNotBlank(param.getScheduleEndTime())) {
            request.setScheduleEndTime(param.getScheduleEndTime());
            request.setScheduleType("SCHEDULE_START_END");
        } else {
            request.setScheduleType("SCHEDULE_FROM_NOW");
        }
        request.setProductVideoSpecificType(param.getProductVideoSpecificType());
        if (CollectionUtils.isNotEmpty(param.getIdentityList())) {
            request.setIdentityList(param.getIdentityList().stream().map(Identity::toTkIdentity).collect(Collectors.toList()));
        }
        request.setAffiliatePostsEnabled(param.getAffiliatePostsEnabled());
        if (CollectionUtils.isNotEmpty(param.getItemList())) {
            request.setItemList(param.getItemList().stream().map(GmvMaxVideoItem::toTkGmvMaxVideoItem).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(param.getCustomAnchorVideoList())) {
            request.setCustomAnchorVideoList(param.getCustomAnchorVideoList().stream().map(GmvMaxVideoItem::toTkGmvMaxVideoItem).collect(Collectors.toList()));
            //前端分开存储，调接口需要合并
            if (CollectionUtils.isNotEmpty(request.getItemList())) {
                request.getItemList().addAll(request.getCustomAnchorVideoList());
            } else {
                request.setItemList(request.getCustomAnchorVideoList());
            }
        }
        request.setCampaignName(param.getCampaignName());

        GmvMaxCampaignInfo response = apiClient.campaignCreate(accessToken, request);
        if (Objects.isNull(response) || StringUtils.isBlank(response.getCampaignId())) {
            throw new BizServiceException("创建GmvMax广告接口异常");
        }
        //设置认知身份标题保存到数据库
        if (CollectionUtils.isNotEmpty(param.getIdentityList())) {
            Map<String, Identity> identityMap = param.getIdentityList().stream().collect(Collectors.toMap(Identity::getIdentityId, Function.identity()));
            response.getIdentityList().forEach(e -> e.setDisplayName(identityMap.get(e.getIdentityId()).getDisplayName()));
        }
        tiktokGmvMaxCampaignDao.add(puid, param.getShopId(), response);
        //同步数据，主要是为了同步一级状态过来
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(20000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            tikTokSyncService.addOrUpdateGmvMaxCampaignSync(puid, param.getShopId(), param.getAdvertiserId(), storeId, Collections.singletonList(response.getCampaignId()),
                    Collections.singletonList(CampaignGetPrimaryStatusEnum.STATUS_DELIVERY_OK.getValue()));
        }, tiktokAddOrUpdateCampaignSyncExecutor).exceptionally(e -> {
            log.error(String.format("创建后同步活动数据失败:puid:%s, shop_id:%s, campaign_id:%s",  puid, param.getShopId(), response.getCampaignId()), e);
            return null;
        });
        return response.getCampaignId();
    }

    public TikTokGmvMaxCampaignInfo campaignInfoGet(Integer puid, String advertiserId, Integer shopId, String campaignId) {
        TikTokGmvMaxCampaign campaign = tiktokGmvMaxCampaignDao.getByCampaignId(puid, shopId, advertiserId, campaignId);
        if (Objects.isNull(campaign)) {
            throw new BizServiceException("未查询到GmvMax广告信息");
        }
        TikTokAdvertiserAccount tikTokAdvertiserAccount = tikTokAdvertiserAccountDao.getByPuidAndAdvertiserId(puid, advertiserId);
        if (Objects.isNull(tikTokAdvertiserAccount)) {
            throw new BizServiceException("未查询到GmvMax账号信息");
        }
        TikTokGmvMaxCampaignInfo campaignInfo = new TikTokGmvMaxCampaignInfo();
        campaignInfo.setPuid(puid);
        campaignInfo.setShopId(shopId);
        campaignInfo.setShopName(getShopName(puid, shopId));
        campaignInfo.setAdvertiserId(advertiserId);
        campaignInfo.setAdvertiserName(tikTokAdvertiserAccount.getName());
        TikTokStoreInfo store = this.getStore(puid, shopId, advertiserId);
        if (store != null) {
            campaignInfo.setStoreId(store.getStoreId());
            campaignInfo.setStoreName(store.getStoreName());
        }
        campaignInfo.setCurrency(tikTokAdvertiserAccount.getCurrency());
        campaignInfo.setCampaignId(campaign.getCampaignId());
        campaignInfo.setCampaignName(campaign.getCampaignName());
        campaignInfo.setShoppingAdsType(campaign.getShoppingAdsType());
        campaignInfo.setProductSpecificType(campaign.getProductSpecificType());
        if (StringUtils.isNotBlank(campaign.getItemGroupIds())) {
            campaignInfo.setSpuIdList(JSON.parseArray(campaign.getItemGroupIds(), String.class));
        }
        campaignInfo.setOptimizationGoal("商品销量");
        campaignInfo.setDeepBidType(campaign.getDeepBidType());
        campaignInfo.setRoasBid(campaign.getRoasBid());
        campaignInfo.setBudget(campaign.getBudget());
        campaignInfo.setScheduleType(campaign.getScheduleType());

        campaignInfo.setScheduleStartTime(campaign.getScheduleStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        if (Objects.nonNull(campaign.getScheduleEndTime())) {
            campaignInfo.setScheduleEndTime(campaign.getScheduleEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }

        campaignInfo.setProductVideoSpecificType(campaign.getProductVideoSpecificType());
        if (StringUtils.isNotBlank(campaign.getIdentityList())) {
            campaignInfo.setIdentityList(JSONUtil.jsonToArray(campaign.getIdentityList(), Identity.class));
        }
        campaignInfo.setAffiliatePostsEnabled(Boolean.TRUE.equals(campaign.getAffiliatePostsEnabled()));
        if (StringUtils.isNotBlank(campaign.getItemList())) {
            campaignInfo.setItemList(JSONUtil.jsonToArray(campaign.getItemList(), GmvMaxVideoItem.class));
            campaignInfo.getItemList().forEach(e -> e.setId(e.buildGmvMaxVideoItemIdentifyId()));
        }
        campaignInfo.setCampaignCustomAnchorVideoId(campaign.getCampaignCustomAnchorVideoId());
        if (StringUtils.isNotBlank(campaign.getCustomAnchorVideoList())) {
            List<String> customAnchorVideoItemIdList = Objects.requireNonNull(JSONUtil.jsonToArray(campaign.getCustomAnchorVideoList(), GmvMaxVideoItem.class)).stream().map(GmvMaxVideoItem::buildGmvMaxVideoItemIdentifyId).collect(Collectors.toList());
            Map<String, GmvMaxVideoItem> itemMap = campaignInfo.getItemList().stream().collect(Collectors.toMap(GmvMaxVideoItem::buildGmvMaxVideoItemIdentifyId, Function.identity()));
            //取itemList里的自定义作品，数据库存储的customAnchorVideoList字段不全，接口有的没返回
            campaignInfo.setCustomAnchorVideoList(customAnchorVideoItemIdList.stream().filter(itemMap::containsKey).map(itemMap::get).collect(Collectors.toList()));
            campaignInfo.getCustomAnchorVideoList().forEach(e -> e.setId(e.buildGmvMaxVideoItemIdentifyId()));
            //排除掉自定义的作品
            campaignInfo.setItemList(campaignInfo.getItemList().stream().filter(e -> !customAnchorVideoItemIdList.contains(e.buildGmvMaxVideoItemIdentifyId())).collect(Collectors.toList()));
        }

        return campaignInfo;
    }

    public String campaignStatusUpdate(Integer puid, GmvMaxCampaignStatusUpdateParam param) {
        TikTokGmvMaxCampaign campaign = tiktokGmvMaxCampaignDao.getByCampaignId(puid, param.getShopId(), param.getAdvertiserId(), param.getCampaignId());
        if (Objects.isNull(campaign)) {
            throw new BizServiceException("未查询到该广告");
        }
        String accessToken = getAccessToken(puid, param.getShopId());

        CampaignStatusUpdateRequest request = new CampaignStatusUpdateRequest();
        request.setAdvertiserId(param.getAdvertiserId());
        request.setCampaignIds(Lists.newArrayList(param.getCampaignId()));
        request.setOperationStatus(param.getStatus());

        CampaignStatusUpdateResponse response = apiClient.campaignStatusUpdate(accessToken, request);
        if (Objects.isNull(response) || StringUtils.isBlank(response.getStatus())) {
            throw new BizServiceException("更新GmvMax广告状态接口异常");
        }
        if (CampaignOperationStatusEnum.DELETE.getValue().equalsIgnoreCase(param.getStatus())) {
            tiktokGmvMaxCampaignDao.updatePrimaryStatus(puid, param.getShopId(), param.getAdvertiserId(), param.getCampaignId(), CampaignGetPrimaryStatusEnum.STATUS_DELETE.getValue());
        } else {
            tiktokGmvMaxCampaignDao.updateOperationStatus(puid, param.getShopId(), param.getAdvertiserId(), param.getCampaignId(), response.getStatus());
        }
        //同步数据，主要是为了同步一级状态过来
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(20000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            tikTokSyncService.addOrUpdateGmvMaxCampaignSync(puid, param.getShopId(), param.getAdvertiserId(), campaign.getStoreId(), Collections.singletonList(param.getCampaignId()),
                    Arrays.asList(CampaignGetPrimaryStatusEnum.STATUS_DELIVERY_OK.getValue(), CampaignGetPrimaryStatusEnum.STATUS_DISABLE.getValue(), CampaignGetPrimaryStatusEnum.STATUS_DELETE.getValue()));
        }, tiktokAddOrUpdateCampaignSyncExecutor).exceptionally(e -> {
            log.error(String.format("编辑后同步活动数据失败:puid:%s, shop_id:%s, campaign_id:%s",  puid, param.getShopId(), param.getCampaignId()), e);
            return null;
        });
        return param.getCampaignId();
    }

    public String update(Integer puid, GmvMaxCampaignUpdateParam param) {
        String accessToken = getAccessToken(puid, param.getShopId());

        GmvMaxCampaignUpdateRequest updateRequest = new GmvMaxCampaignUpdateRequest();

        updateRequest.setAdvertiserId(param.getAdvertiserId());
        updateRequest.setCampaignId(param.getCampaignId());

        if (StringUtils.isNotBlank(param.getCampaignName())) {
            updateRequest.setCampaignName(param.getCampaignName());
        }
        if (Objects.nonNull(param.getRoasBid())) {
            updateRequest.setRoasBid(param.getRoasBid());
        }
        if (Objects.nonNull(param.getBudget())) {
            updateRequest.setBudget(param.getBudget());
        }
        if (StringUtils.isNotBlank(param.getScheduleEndTime())) {
            updateRequest.setScheduleEndTime(param.getScheduleEndTime());
            updateRequest.setScheduleType("SCHEDULE_START_END");
        } else {
            updateRequest.setScheduleType("SCHEDULE_FROM_NOW");
        }

        if (Objects.nonNull(param.getAffiliatePostsEnabled())) {
            updateRequest.setAffiliatePostsEnabled(param.getAffiliatePostsEnabled());
        }
        if (CollectionUtils.isNotEmpty(param.getItemList())) {
            updateRequest.setItemList(param.getItemList().stream().map(GmvMaxVideoItem::toTkGmvMaxVideoItem).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(param.getCustomAnchorVideoList())) {
            updateRequest.setCustomAnchorVideoList(param.getCustomAnchorVideoList().stream().map(GmvMaxVideoItem::toTkGmvMaxVideoItem).collect(Collectors.toList()));
            //前端分开存储，调接口需要合并
            if (CollectionUtils.isNotEmpty(param.getItemList())) {
                updateRequest.getItemList().addAll(updateRequest.getCustomAnchorVideoList());
            } else {
                updateRequest.setItemList(updateRequest.getCustomAnchorVideoList());
            }
        }

        GmvMaxCampaignInfo response = apiClient.campaignUpdate(accessToken, updateRequest);
        if (Objects.isNull(response) || StringUtils.isBlank(response.getCampaignId())) {
            throw new BizServiceException("更新GmvMax广告接口异常");
        }

        tiktokGmvMaxCampaignDao.addOrUpdate(puid, param.getShopId(), response);
        //同步数据，主要是为了同步一级状态过来
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(20000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            tikTokSyncService.addOrUpdateGmvMaxCampaignSync(puid, param.getShopId(), param.getAdvertiserId(), response.getStoreId(), Collections.singletonList(response.getCampaignId()),
                    Arrays.asList(CampaignGetPrimaryStatusEnum.STATUS_DELIVERY_OK.getValue(), CampaignGetPrimaryStatusEnum.STATUS_DISABLE.getValue(), CampaignGetPrimaryStatusEnum.STATUS_DELETE.getValue()));
        }, tiktokAddOrUpdateCampaignSyncExecutor).exceptionally(e -> {
            log.error(String.format("编辑后同步活动数据失败:puid:%s, shop_id:%s, campaign_id:%s",  puid, param.getShopId(), response.getCampaignId()), e);
            return null;
        });
        //同步数据，主要是为了同步一级状态过来
        return response.getCampaignId();
    }

    public GmvMaxCampaignListResp list(Integer puid, List<Integer> shopIdList, GmvMaxCampaignListParam param) {

        Page<TikTokGmvMaxCampaignPage> page = tiktokGmvMaxCampaignDao.getPageListWithReport(puid, shopIdList, param);
        if (CollectionUtils.isEmpty(page.getRows())) {
            return GmvMaxCampaignListResp.empty(param.getPageNo(), param.getPageSize());
        }

        GmvMaxCampaignListResp resp = new GmvMaxCampaignListResp();
        resp.setPageNo(page.getPageNo());
        resp.setPageSize(page.getPageSize());
        resp.setTotalPage(page.getTotalPage());
        resp.setTotalSize(page.getTotalSize());

        List<Integer> shopIds = page.getRows().stream().map(TikTokGmvMaxCampaignPage::getShopId).distinct().collect(Collectors.toList());
        Map<Integer, String> shopNameMap = multiPlatformShopAuthDao.listByPuidAndShopIds(puid, shopIds).stream().collect(Collectors.toMap(MultiPlatformShopAuth::getId, MultiPlatformShopAuth::getName));

        List<String> advertiserIds = page.getRows().stream().map(TikTokGmvMaxCampaignPage::getAdvertiserId).distinct().collect(Collectors.toList());
        List<TikTokAdvertiserAccount> advertiserList = tikTokAdvertiserAccountDao.getByPuidAndAdvertiserIds(puid, advertiserIds);
        Map<String, TikTokAdvertiserAccount> advertiserMap = StreamUtil.toMap(advertiserList, TikTokAdvertiserAccount::getAdvertiserId, Function.identity());

        List<String> storeIds = page.getRows().stream().map(TikTokGmvMaxCampaignPage::getStoreId).distinct().collect(Collectors.toList());
        List<TikTokStoreInfo> storeInfoList = tiktokStoreInfoDao.getList(puid, advertiserIds, shopIds, storeIds);
        Map<String, String> storeNameMap = StreamUtil.toMap(storeInfoList, TikTokStoreInfo::getStoreId, TikTokStoreInfo::getStoreName);


        // todo 细节再看看

        List<GmvMaxCampaignReportInfo> campaignList = page.getRows().stream().map(i -> {
            GmvMaxCampaignReportInfo campaign = new GmvMaxCampaignReportInfo();
            campaign.setPuid(puid);
            campaign.setShopId(i.getShopId());
            campaign.setShopName(shopNameMap.get(i.getShopId()));
            campaign.setAdvertiserId(i.getAdvertiserId());
            TikTokAdvertiserAccount account = advertiserMap.get(i.getAdvertiserId());
            if (account != null) {
                campaign.setAdvertiserName(account.getName());
                campaign.setCurrency(account.getCurrency());
            }
            campaign.setStoreId(i.getStoreId());
            campaign.setStoreName(storeNameMap.get(i.getStoreId()));
            campaign.setStoreAuthorizedBcId(i.getStoreAuthorizedBcId());
            campaign.setCampaignId(i.getCampaignId());
            campaign.setCampaignName(i.getCampaignName());
            campaign.setShoppingAdsType(i.getShoppingAdsType());
            campaign.setOptimizationGoal("商品销量");
            campaign.setStatus(i.getStatus());
            campaign.setPrimaryStatus(i.getPrimaryStatus());
            campaign.setBudget(i.getBudget() == null ? null : MathUtil.safeDecimal(i.getBudget(), 2));
            campaign.setRoasBid(i.getRoasBid() == null ? null : MathUtil.safeDecimal(i.getRoasBid(), 2));
            if (i.getHasReport().equals(0)) {
                campaign.setCost(null);
                campaign.setOrders(null);
                campaign.setRoi(null);
                campaign.setNetCost(null);
                campaign.setGrossRevenue(null);
                campaign.setCostPerOrder(null);
            } else {
                campaign.setCost(MathUtil.safeDecimal(i.getCost(), 2).toPlainString());
                campaign.setOrders(MathUtil.safeDecimal(i.getOrders(), 2).toPlainString());
                campaign.setRoi(MathUtil.safeDecimal(i.getRoi(), 2).toPlainString());
                campaign.setNetCost(MathUtil.safeDecimal(i.getNetCost(), 2).toPlainString());
                campaign.setGrossRevenue(MathUtil.safeDecimal(i.getGrossRevenue(), 2).toPlainString());
                campaign.setCostPerOrder(MathUtil.safeDecimal(i.getCostPerOrder(), 2).toPlainString());
            }
            return campaign;
        }).collect(Collectors.toList());
        resp.setRows(campaignList);
        return resp;
    }


}
