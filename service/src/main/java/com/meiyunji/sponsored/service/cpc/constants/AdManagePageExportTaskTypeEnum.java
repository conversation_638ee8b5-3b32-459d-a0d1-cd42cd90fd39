package com.meiyunji.sponsored.service.cpc.constants;

import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @create: 2024-07-17 10:27
 */
@Getter
@AllArgsConstructor
public enum AdManagePageExportTaskTypeEnum {
    PORTFOLIO(1, "广告组合", "广告组合", AdManagePageExportTaskConstant.PORTFOLIO),
    CAMPAIGN(2, "广告活动", "广告活动", AdManagePageExportTaskConstant.CAMPAIGN),
    PLACEMENT(3, "广告位", "广告位", AdManagePageExportTaskConstant.PLACEMENT),
    GROUP(4, "广告组", "广告组", AdManagePageExportTaskConstant.GROUP),
    PRODUCT(5, "广告产品", "广告产品", AdManagePageExportTaskConstant.PRODUCT),
    KEYWORD(6, "关键词投放", "投放", AdManagePageExportTaskConstant.KEYWORD),
    TARGET(7, "商品、自动投放", "投放", AdManagePageExportTaskConstant.TARGET),
    QUERY_KEYWORD(8, "搜索词产生关键词", "搜索词", AdManagePageExportTaskConstant.QUERY_KEYWORD),
    QUERY_ASIN(9, "搜索词产生ASIN", "搜索词", AdManagePageExportTaskConstant.QUERY_ASIN),
    CAMPAIGN_NE_KEYWORD(10, "广告活动否定关键词", "否定投放", AdManagePageExportTaskConstant.CAMPAIGN_NE_KEYWORD),
    CAMPAIGN_NE_TARGET(11, "广告活动否定商品", "否定投放", AdManagePageExportTaskConstant.CAMPAIGN_NE_TARGET),
    GROUP_NE_KEYWORD(12, "广告组否定关键词", "否定投放", AdManagePageExportTaskConstant.GROUP_NE_KEYWORD),
    GROUP_NE_TARGET(13, "广告组否定商品", "否定投放", AdManagePageExportTaskConstant.GROUP_NE_TARGET),
    SELLFOX_LOG(14, "赛狐手动调整", "广告日志", AdManagePageExportTaskConstant.AD_LOG),
    AMAZON_LOG(15, "亚马逊记录", "广告日志", AdManagePageExportTaskConstant.AD_LOG),
    NE_TARGET(17, "否定商品投放", "否定投放", AdManagePageExportTaskConstant.NE_TARGET),
    NE_KEYWORD(16, "否定关键词投放", "否定投放", AdManagePageExportTaskConstant.NE_KEYWORD),
    REPEAT_TARGETING(18, "关键词重复投放", "重复投放", AdManagePageExportTaskConstant.REPEAT_TARGETING),
    ALL_SEARCH_TERM(19, "sp,sb搜索词数据", "出单搜索词", AdManagePageExportTaskConstant.ALL_SEARCH_TERM),
    BUDGET_ANALYSIS(20, "预算分析", "预算分析", AdManagePageExportTaskConstant.BUDGET_ANALYSIS),
    CAMPAIGN_MULTIPLE(21, "多店铺广告活动", "广告活动", AdManagePageExportTaskConstant.CAMPAIGN_MULTIPLE),
    MULTIPLE_PORTFOLIO(22, "广告组合", "广告组合", AdManagePageExportTaskConstant.MULTIPLE_PORTFOLIO),
    AMC_QUERY_WORD_AD_SPACE(23, "AMC搜索词", "AMC搜索词", AdManagePageExportTaskConstant.AMC_QUERY_WORD_AD_SPACE),
    PERSPECTIVE_CAMPAIGN_VIEW(24, "广告活动视图", "产品广告透视", AdManagePageExportTaskConstant.PERSPECTIVE_CAMPAIGN_VIEW),
    PERSPECTIVE_KEYWORD_VIEW_AGGREGATE(25, "关键词投放视图列表汇总", "产品广告透视", AdManagePageExportTaskConstant.PERSPECTIVE_KEYWORD_VIEW_AGGREGATE),
    PERSPECTIVE_KEYWORD_VIEW(26, "关键词投放视图列表", "产品广告透视", AdManagePageExportTaskConstant.PERSPECTIVE_KEYWORD_VIEW),
    PERSPECTIVE_TARGET_VIEW_AGGREGATE(27, "商品投放视图列表汇总", "产品广告透视", AdManagePageExportTaskConstant.PERSPECTIVE_TARGET_VIEW_AGGREGATE),
    PERSPECTIVE_TARGET_VIEW(28, "商品投放视图列表", "产品广告透视", AdManagePageExportTaskConstant.PERSPECTIVE_TARGET_VIEW),
    PERSPECTIVE_AUTO_TARGET_VIEW(29, "自动投放视图", "产品广告透视", AdManagePageExportTaskConstant.PERSPECTIVE_AUTO_TARGET_VIEW),
    PERSPECTIVE_PLACEMENT_VIEW_AGGREGATE(30, "广告位视图列表汇总", "产品广告透视", AdManagePageExportTaskConstant.PERSPECTIVE_PLACEMENT_VIEW_AGGREGATE),
    PERSPECTIVE_PLACEMENT_VIEW(31, "广告位视图列表", "产品广告透视", AdManagePageExportTaskConstant.PERSPECTIVE_PLACEMENT_VIEW),
    PERSPECTIVE_AUDIENCE_TARGET_VIEW_AGGREGATE(32, "受众投放视图列表汇总", "产品广告透视", AdManagePageExportTaskConstant.PERSPECTIVE_AUDIENCE_TARGET_VIEW_AGGREGATE),
    PERSPECTIVE_AUDIENCE_TARGET_VIEW(33, "受众投放视图列表", "产品广告透视", AdManagePageExportTaskConstant.PERSPECTIVE_AUDIENCE_TARGET_VIEW),
    PERSPECTIVE_SEARCH_TERMS_VIEW(34, "搜索词视图", "产品广告透视", AdManagePageExportTaskConstant.PERSPECTIVE_SEARCH_TERMS_VIEW),
    POST(35, "品牌帖子", "品牌帖子", AdManagePageExportTaskConstant.POST),
    TARGET_MULTIPLE(36, "多店铺投放", "投放", AdManagePageExportTaskConstant.TARGET_MULTIPLE),
    ;
    private Integer code;
    private String desc;
    private String page;
    private String handler;

    public static AdManagePageExportTaskTypeEnum getEnumByCode(Integer code) {
        AdManagePageExportTaskTypeEnum[] values = values();
        for (AdManagePageExportTaskTypeEnum e : values) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

    public static final List<AdManagePageExportTaskTypeEnum> AD_LOG_TYPES = Arrays.asList(SELLFOX_LOG, AMAZON_LOG);

    public static final List<Integer> NO_NEED_SHOP_ID_TYPES = Arrays.asList(REPEAT_TARGETING.getCode());

    public static final Set<Integer> NE_TARGET_TYPE_SET = Stream.of(CAMPAIGN_NE_KEYWORD, CAMPAIGN_NE_TARGET,
                    GROUP_NE_TARGET, GROUP_NE_KEYWORD, NE_KEYWORD, NE_TARGET)
            .map(AdManagePageExportTaskTypeEnum::getCode).collect(Collectors.toSet());
}
