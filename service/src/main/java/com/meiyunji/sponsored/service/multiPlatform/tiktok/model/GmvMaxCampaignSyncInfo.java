package com.meiyunji.sponsored.service.multiPlatform.tiktok.model;

import com.tiktok.advertising.model.gmv_max.GmvMaxCampaign;
import com.tiktok.advertising.model.gmv_max.Identity;
import com.tiktok.advertising.model.gmv_max.VideoItem;
import com.tiktok.advertising.model.gmv_max.response.GmvMaxCampaignInfo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class GmvMaxCampaignSyncInfo {

    private Integer shopId;

    private String advertiserId;
    private String operationStatus;
    private String campaignId;
    private String campaignName;
    private String storeId;
    private String storeAuthorizedBcId;
    private String shoppingAdsType;
    private String productSpecificType;
    private List<String> itemGroupIds;
    private String optimizationGoal;
    private String deepBidType;
    private BigDecimal roasBid;
    private BigDecimal budget;
    private String scheduleType;
    private String scheduleStartTime;
    private String scheduleEndTime;
    private List<String> placements;
    private List<String> locationIds;
    private List<String> ageGroups;
    private String productVideoSpecificType;
    private List<Identity> identityList;
    private Boolean affiliatePostsEnabled;
    private List<VideoItem> itemList;
    private String campaignCustomAnchorVideoId;
    private List<VideoItem> customAnchorVideoList;
    private String createTimeUtc = "";
    private String modifyTimeUtc = "";
    private String objectiveType = "";
    private String secondaryStatus = "";
    private String primaryStatus = "";

    public void buildFrom(GmvMaxCampaign campaign, String primaryStatus) {
        this.setCreateTimeUtc(campaign.getCreateTime());
        this.setModifyTimeUtc(campaign.getModifyTime());
        this.setObjectiveType(campaign.getObjectiveType());
        this.setPrimaryStatus(primaryStatus);
        this.setSecondaryStatus(campaign.getSecondaryStatus());
    }

    public void buildFrom(Integer shopId, GmvMaxCampaignInfo gmvMaxCampaignInfo) {
        this.setShopId(shopId);
        this.setAdvertiserId(gmvMaxCampaignInfo.getAdvertiserId());
        this.setOperationStatus(gmvMaxCampaignInfo.getOperationStatus());
        this.setCampaignId(gmvMaxCampaignInfo.getCampaignId());
        this.setCampaignName(gmvMaxCampaignInfo.getCampaignName());
        this.setStoreId(gmvMaxCampaignInfo.getStoreId());
        this.setStoreAuthorizedBcId(gmvMaxCampaignInfo.getStoreAuthorizedBcId());
        this.setShoppingAdsType(gmvMaxCampaignInfo.getShoppingAdsType());
        this.setProductSpecificType(gmvMaxCampaignInfo.getProductSpecificType());
        this.setItemGroupIds(gmvMaxCampaignInfo.getItemGroupIds());
        this.setOptimizationGoal(gmvMaxCampaignInfo.getOptimizationGoal());
        this.setDeepBidType(gmvMaxCampaignInfo.getDeepBidType());
        this.setRoasBid(gmvMaxCampaignInfo.getRoasBid());
        this.setBudget(gmvMaxCampaignInfo.getBudget());
        this.setScheduleType(gmvMaxCampaignInfo.getScheduleType());
        this.setScheduleStartTime(gmvMaxCampaignInfo.getScheduleStartTime());
        this.setScheduleEndTime(gmvMaxCampaignInfo.getScheduleEndTime());
        this.setPlacements(gmvMaxCampaignInfo.getPlacements());
        this.setLocationIds(gmvMaxCampaignInfo.getLocationIds());
        this.setAgeGroups(gmvMaxCampaignInfo.getAgeGroups());
        this.setProductVideoSpecificType(gmvMaxCampaignInfo.getProductVideoSpecificType());
        this.setIdentityList(gmvMaxCampaignInfo.getIdentityList());
        this.setAffiliatePostsEnabled(gmvMaxCampaignInfo.getAffiliatePostsEnabled());
        this.setItemList(gmvMaxCampaignInfo.getItemList());
        this.setCampaignCustomAnchorVideoId(gmvMaxCampaignInfo.getCampaignCustomAnchorVideoId());
        this.setCustomAnchorVideoList(gmvMaxCampaignInfo.getCustomAnchorVideoList());
    }
}
