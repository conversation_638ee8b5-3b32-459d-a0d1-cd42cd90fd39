package com.meiyunji.sponsored.service.reportImport2.processor;

import com.alibaba.fastjson.JSONReader;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.util.GZipUtils;
import com.meiyunji.sponsored.common.util.MD5Util;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.reportImport.constanst.LxReportConstant;
import com.meiyunji.sponsored.service.reportImport.model.LxCampaignReport;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdCampaignReport;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdQueryKeywordReport;
import com.meiyunji.sponsored.service.reportImport2.vo.AmazonAdReportImportMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-11-28  14:13
 */
@Service
@Slf4j
public class LxAmazonAdQueryKeywordReportImportProcessor extends AbstractAmazonAdLxReportImportProcessor<LxAmazonAdQueryKeywordReport> {

    protected final IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao;
    protected final IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    protected final ICpcQueryKeywordReportDao cpcQueryKeywordReportDao;
    protected final ICpcSbQueryKeywordReportDao cpcSbQueryKeywordReportDao;

    protected LxAmazonAdQueryKeywordReportImportProcessor(IAmazonAdCampaignAllDao amazonAdCampaignAllDao, IAmazonAdGroupDao amazonAdGroupDao, IAmazonSdAdGroupDao amazonSdAdGroupDao, IAmazonSbAdGroupDao amazonSbAdGroupDao, IScVcShopAuthDao shopAuthDao, CosBucketClient tempBucketClient,
                                                          IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao, IAmazonSbAdKeywordDao amazonSbAdKeywordDao, ICpcQueryKeywordReportDao cpcQueryKeywordReportDao, ICpcSbQueryKeywordReportDao cpcSbQueryKeywordReportDao) {
        super(amazonAdCampaignAllDao, amazonAdGroupDao, amazonSdAdGroupDao, amazonSbAdGroupDao, shopAuthDao, tempBucketClient);
        this.amazonAdKeywordShardingDao = amazonAdKeywordShardingDao;
        this.amazonSbAdKeywordDao = amazonSbAdKeywordDao;
        this.cpcQueryKeywordReportDao = cpcQueryKeywordReportDao;
        this.cpcSbQueryKeywordReportDao = cpcSbQueryKeywordReportDao;
    }


    @Override
    public void importReport(AmazonAdReportImportMessage importMessage) {
        //读取数据
        try (InputStreamReader inputStreamReader = new InputStreamReader(new ByteArrayInputStream(GZipUtils.release(tempBucketClient.getObjectToBytes(importMessage.getFileId().replaceFirst("/", "")))))) {
            ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(importMessage.getShopId(), importMessage.getPuid());
            JSONReader jsonReader = new JSONReader(inputStreamReader);
            jsonReader.startArray();
            List<LxAmazonAdQueryKeywordReport> reports = Lists.newArrayListWithExpectedSize(500);
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                LxAmazonAdQueryKeywordReport report = new LxAmazonAdQueryKeywordReport();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.isValid()) {
                    reports.add(report);
                    if (reports.size() >= 500) {
                        this.dealReport(importMessage, reports, shopAuth);
                        reports = Lists.newArrayListWithExpectedSize(500);
                    }
                }
            }
            jsonReader.endArray();
            jsonReader.close();
            if (CollectionUtils.isNotEmpty(reports)) {
                this.dealReport(importMessage, reports, shopAuth);
            }
        } catch (Exception e) {
            log.error("import query keyword report error puid:{},shopId：{}，scheduleId :{} error:", importMessage.getPuid(), importMessage.getShopId(), importMessage.getScheduleId(), e);
        }
    }

    private void dealReport(AmazonAdReportImportMessage importMessage, List<LxAmazonAdQueryKeywordReport> reports, ShopAuth shopAuth) {
        Integer puid = importMessage.getPuid();
        Integer shopId = importMessage.getShopId();
        String adType = importMessage.getAdType().toLowerCase();
        //获取广告活动详情
        List<String> campaignIds = reports.stream().map(LxAmazonAdQueryKeywordReport::getCampaignId).collect(Collectors.toList());
        Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = amazonAdCampaignAllDao.getByCampaignIds(puid, shopId, shopAuth.getMarketplaceId(), campaignIds, adType)
                .stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));
        //获取广告组详情
        Map<String, AmazonAdGroup> amazonAdGroupMap = new HashMap<>();
        Map<String, AmazonSbAdGroup> amazonSbAdGroupMap = new HashMap<>();
        //获取关键词详情
        Map<String, AmazonAdKeyword> amazonAdKeywordMap = new HashMap<>();
        Map<String, AmazonSbAdKeyword> amazonSbAdKeywordMap = new HashMap<>();
        if (CampaignTypeEnum.sp.name().equalsIgnoreCase(importMessage.getAdType())) {
            //sp广告组
            List<String> adGroupIds = reports.stream().map(LxAmazonAdQueryKeywordReport::getAdGroupId).collect(Collectors.toList());
            amazonAdGroupMap.putAll(amazonAdGroupDao.getListByAdGroupIds(puid, shopId, adGroupIds)
                    .stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity(), (e1, e2) -> e2)));
            //sp关键词
            List<String> keywordIds = reports.stream().map(LxAmazonAdQueryKeywordReport::getKeywordId).collect(Collectors.toList());
            amazonAdKeywordMap.putAll(amazonAdKeywordShardingDao.getByKeywordIds(puid, shopId, keywordIds)
                    .stream().collect(Collectors.toMap(AmazonAdKeyword::getKeywordId, Function.identity(), (e1, e2) -> e2)));
        } else if (CampaignTypeEnum.sb.name().equalsIgnoreCase(importMessage.getAdType())) {
            //sb广告组
            List<String> adGroupIds = reports.stream().map(LxAmazonAdQueryKeywordReport::getAdGroupId).collect(Collectors.toList());
            amazonSbAdGroupMap.putAll(amazonSbAdGroupDao.getAdGroupByIds(puid, shopId, adGroupIds)
                    .stream().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, Function.identity(), (e1, e2) -> e2)));
            //sb关键词
            List<String> keywordIds = reports.stream().map(LxAmazonAdQueryKeywordReport::getKeywordId).collect(Collectors.toList());
            amazonSbAdKeywordMap.putAll(amazonSbAdKeywordDao.listByKeywordId(puid, shopId, keywordIds)
                    .stream().collect(Collectors.toMap(AmazonSbAdKeyword::getKeywordId, Function.identity(), (e1, e2) -> e2)));
        }

        List<CpcQueryKeywordReport> insertSpReport = new ArrayList<>();
        List<CpcSbQueryKeywordReport> insertSbReport = new ArrayList<>();
        reports.forEach(e -> {
            if (StringUtils.isBlank(e.getQuery())) {
                log.error("pxq-report-import puid : {} shop_id : {} query 不存在", puid, shopId);
                return;
            }
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(e.getCampaignId());
            if (amazonAdCampaignAll == null) {
                log.error("pxq-report-import puid : {} shop_id : {} campaignId : {} 不存在", puid, shopId, e.getCampaignId());
                return;
            }
            if (CampaignTypeEnum.sp.name().equalsIgnoreCase(importMessage.getAdType())) {
                AmazonAdGroup amazonAdGroup = amazonAdGroupMap.get(e.getAdGroupId());
                if (amazonAdGroup == null) {
                    log.error("pxq-report-import puid : {} shop_id : {} sp adGroupId : {} 不存在", puid, shopId, e.getAdGroupId());
                    return;
                }
                AmazonAdKeyword amazonAdKeyword = amazonAdKeywordMap.get(e.getKeywordId());
                if (amazonAdKeyword == null) {
                    log.error("pxq-report-import puid : {} shop_id : {} sp keywordId : {} 不存在", puid, shopId, e.getKeywordId());
                    return;
                }
                CpcQueryKeywordReport spReport = this.getSpReport(puid, shopAuth, importMessage.getCountDate(), e, amazonAdCampaignAll, amazonAdGroup, amazonAdKeyword);
                insertSpReport.add(spReport);
            } else if (CampaignTypeEnum.sb.name().equalsIgnoreCase(importMessage.getAdType())) {
                if (StringUtils.isBlank(e.getQuery())) {
                    log.error("pxq-report-import puid : {} shop_id : {} query 不存在", puid, shopId);
                    return;
                }
                AmazonSbAdGroup amazonSbAdGroup = amazonSbAdGroupMap.get(e.getAdGroupId());
                if (amazonSbAdGroup == null) {
                    log.error("pxq-report-import puid : {} shop_id : {} sb adGroupId : {} 不存在", puid, shopId, e.getAdGroupId());
                    return;
                }
                AmazonSbAdKeyword amazonSbAdKeyword = amazonSbAdKeywordMap.get(e.getKeywordId());
                if (amazonSbAdKeyword == null) {
                    log.error("pxq-report-import puid : {} shop_id : {} sb keywordId : {} 不存在", puid, shopId, e.getKeywordId());
                    return;
                }
                CpcSbQueryKeywordReport sbReport = this.getSbReport(puid, shopAuth, importMessage.getCountDate(), e, amazonAdCampaignAll, amazonSbAdGroup, amazonSbAdKeyword);
                insertSbReport.add(sbReport);
            }
        });
        if (CollectionUtils.isNotEmpty(insertSpReport)) {
            cpcQueryKeywordReportDao.insertList(puid, insertSpReport);
        }
        if (CollectionUtils.isNotEmpty(insertSbReport)) {
            cpcSbQueryKeywordReportDao.insertList(puid, insertSbReport);
        }
    }

    private CpcQueryKeywordReport getSpReport(Integer puid, ShopAuth shopAuth, String countDate, LxAmazonAdQueryKeywordReport report, AmazonAdCampaignAll campaign, AmazonAdGroup group, AmazonAdKeyword keyword) {
        CpcQueryKeywordReport cpcQueryKeywordReport = new CpcQueryKeywordReport();
        cpcQueryKeywordReport.setPuid(puid);
        cpcQueryKeywordReport.setShopId(shopAuth.getId());
        cpcQueryKeywordReport.setMarketplaceId(shopAuth.getMarketplaceId());
        cpcQueryKeywordReport.setCampaignId(campaign.getCampaignId());
        cpcQueryKeywordReport.setAdGroupId(group.getAdGroupId());
        cpcQueryKeywordReport.setKeywordId(keyword.getKeywordId());
        cpcQueryKeywordReport.setCountDate(countDate);
        cpcQueryKeywordReport.setKeywordText(keyword.getKeywordText());
        cpcQueryKeywordReport.setMatchType(keyword.getMatchType());
        cpcQueryKeywordReport.setCampaignName(campaign.getName());
        cpcQueryKeywordReport.setAdGroupName(group.getName());
        cpcQueryKeywordReport.setQuery(report.getQuery().toLowerCase());
        cpcQueryKeywordReport.setQueryId(MD5Util.getMD5(cpcQueryKeywordReport.getKeywordId() + cpcQueryKeywordReport.getQuery()));
        //数据字段
        cpcQueryKeywordReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
        cpcQueryKeywordReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        cpcQueryKeywordReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        cpcQueryKeywordReport.setSaleNum(report.getOrders() != null ? report.getOrders() : 0);
        cpcQueryKeywordReport.setAdSaleNum(report.getDirectOrders() != null ? report.getDirectOrders() : 0);
        cpcQueryKeywordReport.setTotalSales(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        cpcQueryKeywordReport.setAdSales(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);
        cpcQueryKeywordReport.setOrderNum(report.getAdUnits() != null ? report.getAdUnits() : 0);
        cpcQueryKeywordReport.setAdOrderNum(report.getDirectUnits() != null ? report.getDirectUnits() : 0);
        return cpcQueryKeywordReport;
    }


    private CpcSbQueryKeywordReport getSbReport(Integer puid, ShopAuth shopAuth, String countDate, LxAmazonAdQueryKeywordReport report, AmazonAdCampaignAll campaign, AmazonSbAdGroup group, AmazonSbAdKeyword keyword) {
        CpcSbQueryKeywordReport cpcSbQueryKeywordReport = new CpcSbQueryKeywordReport();
        cpcSbQueryKeywordReport.setPuid(puid);
        cpcSbQueryKeywordReport.setShopId(shopAuth.getId());
        cpcSbQueryKeywordReport.setMarketplaceId(shopAuth.getMarketplaceId());
        cpcSbQueryKeywordReport.setCountDate(countDate);
        cpcSbQueryKeywordReport.setAdFormat(campaign.getAdFormat());
        cpcSbQueryKeywordReport.setCampaignName(campaign.getName());
        cpcSbQueryKeywordReport.setCampaignId(campaign.getCampaignId());
        cpcSbQueryKeywordReport.setCampaignStatus(campaign.getState());
        cpcSbQueryKeywordReport.setCampaignBudget(campaign.getBudget().doubleValue());
        cpcSbQueryKeywordReport.setCampaignBudgetType(campaign.getBudgetType());
        cpcSbQueryKeywordReport.setAdGroupName(group.getName());
        cpcSbQueryKeywordReport.setAdGroupId(group.getAdGroupId());
        cpcSbQueryKeywordReport.setKeywordText(keyword.getKeywordText());
        cpcSbQueryKeywordReport.setKeywordBid(keyword.getBid().doubleValue());
        cpcSbQueryKeywordReport.setKeywordStatus(keyword.getState());
        cpcSbQueryKeywordReport.setKeywordId(keyword.getKeywordId());
        cpcSbQueryKeywordReport.setMatchType(keyword.getMatchType());
        cpcSbQueryKeywordReport.setQuery(report.getQuery().toLowerCase());
        cpcSbQueryKeywordReport.setQueryId(MD5Util.getMD5(cpcSbQueryKeywordReport.getKeywordId() + cpcSbQueryKeywordReport.getQuery()));
        cpcSbQueryKeywordReport.setCurrency(Marketplace.fromId(cpcSbQueryKeywordReport.getMarketplaceId()).getCurrencyCode().name());
        //数据字段
        cpcSbQueryKeywordReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
        cpcSbQueryKeywordReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        cpcSbQueryKeywordReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        cpcSbQueryKeywordReport.setConversions14d(report.getOrders() != null ? report.getOrders() : 0);
        cpcSbQueryKeywordReport.setSales14d(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        return cpcSbQueryKeywordReport;
    }
}
