package com.meiyunji.sponsored.service.reportImport2.processor;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONReader;
import com.amazon.advertising.sd.mode.report.SdReportProduct;
import com.amazonaws.util.json.Jackson;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.util.GZipUtils;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.aggregationReport.po.AmazonAggregationReportSchedule;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.reportImport.dao.ICpcReportsImportPlatformDao;
import com.meiyunji.sponsored.service.reportImport.dao.ICpcReportsImportTaskScheduleDao;
import com.meiyunji.sponsored.service.reportImport.entity.CpcReportsImportPlatform;
import com.meiyunji.sponsored.service.reportImport.entity.CpcReportsImportTaskSchedule;
import com.meiyunji.sponsored.service.reportImport.enums.LxReportImportType;
import com.meiyunji.sponsored.service.reportImport.enums.LxReportType;
import com.meiyunji.sponsored.service.reportImport.enums.ReportImportErrType;
import com.meiyunji.sponsored.service.reportImport.enums.ReportImportStatus;
import com.meiyunji.sponsored.service.reportImport.listener.AbstractLxReportReadListener;
import com.meiyunji.sponsored.service.reportImport.message.AdReportImportMessage;
import com.meiyunji.sponsored.service.reportImport.vo.ReportImportDetailVo;
import com.meiyunji.sponsored.service.reportImport2.dao.IAmazonAdReportsImportTaskDao;
import com.meiyunji.sponsored.service.reportImport2.dao.IAmazonAdReportsImportTaskScheduleDao;
import com.meiyunji.sponsored.service.reportImport2.entity.AmazonAdReportsImportTaskSchedule;
import com.meiyunji.sponsored.service.reportImport2.enums.AmazonAdLxReportImportType;
import com.meiyunji.sponsored.service.reportImport2.enums.AmazonLxReportType;
import com.meiyunji.sponsored.service.reportImport2.vo.AmazonAdReportImportMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

/**
 * 导入lx报告处理类
 *
 * <AUTHOR>
 * @date 2023/05/26
 */
@Service
@Slf4j
public class AmazonAdLxReportImportFromInterfaceHolder implements ApplicationContextAware {

    private ApplicationContext applicationContext;
    private final IAmazonAdReportsImportTaskDao amazonAdReportsImportTaskDao;
    private final IAmazonAdReportsImportTaskScheduleDao amazonAdReportsImportTaskScheduleDao;
    private final IScVcShopAuthDao shopAuthDao;
    private final CosBucketClient tempBucketClient;

    public AmazonAdLxReportImportFromInterfaceHolder(
            IAmazonAdReportsImportTaskDao amazonAdReportsImportTaskDao,
            IAmazonAdReportsImportTaskScheduleDao amazonAdReportsImportTaskScheduleDao,
            IScVcShopAuthDao shopAuthDao,
            CosBucketClient tempBucketClient) {
        this.amazonAdReportsImportTaskDao = amazonAdReportsImportTaskDao;
        this.amazonAdReportsImportTaskScheduleDao = amazonAdReportsImportTaskScheduleDao;
        this.shopAuthDao = shopAuthDao;
        this.tempBucketClient = tempBucketClient;
    }

    public void executor(AmazonAdReportImportMessage message) throws IOException {
        //把任务状态修改为处理中,修改失败,直接丢弃任务
        int res = amazonAdReportsImportTaskScheduleDao.updateStatusById(message.getPuid(), message.getScheduleId(),
                ReportImportStatus.PROCESSING, "", ReportImportStatus.WAITING);

        if (res == 0) {
            log.error("wade-import-report puid: {} scheduleId: {} 修改状态修改,任务可能已删除或已处理", message.getPuid(), message.getScheduleId());
            return;
        }

        try {
            AmazonAdLxReportImportType importType = AmazonAdLxReportImportType.getByReportType(AmazonLxReportType.valueOf(message.getReportType()));
            AbstractAmazonAdLxReportImportProcessor processor = applicationContext.getBean(importType.getImportProcessorClass());
            processor.importReport(message);
            amazonAdReportsImportTaskScheduleDao.updateStatusById(message.getPuid(), message.getScheduleId(),
                    ReportImportStatus.SUCCESS, null,
                    ReportImportStatus.PROCESSING);
        } catch (Exception e) {
            log.error("wade-report-import puid: {} taskId: {} report-type: {} read file with error: ",
                    message.getPuid(), message.getScheduleId(), message.getReportType(), e);
            AmazonAdReportsImportTaskSchedule schedule = amazonAdReportsImportTaskScheduleDao.getById(message.getPuid(), message.getScheduleId());
            ReportImportDetailVo fileVo = new ReportImportDetailVo();
            fileVo.setStatus(ReportImportStatus.FATAL.name());
            fileVo.setFileName(schedule.getFileName());
            fileVo.setRowNumber(0);
            fileVo.setErrType(e.getMessage());
            fileVo.setErrInfos(Lists.newArrayList(e.getMessage()));

            amazonAdReportsImportTaskScheduleDao.updateStatusById(message.getPuid(), message.getScheduleId(),
                    ReportImportStatus.FATAL, Jackson.toJsonString(fileVo),
                    ReportImportStatus.PROCESSING);
        }

    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

}
