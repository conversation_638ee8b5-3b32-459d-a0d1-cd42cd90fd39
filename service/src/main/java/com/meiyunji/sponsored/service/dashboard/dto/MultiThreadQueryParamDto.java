package com.meiyunji.sponsored.service.dashboard.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * @author: ys
 * @date: 2023/10/25 9:14
 * @describe:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MultiThreadQueryParamDto {

    private Integer puid;
    private String marketplaceId;
    private List<Integer> shopIds;
    private Integer hour;
    private String currency;
    private LocalDate startDate;
    private LocalDate endDate;
    private String predicate;
    private String campaignType;
    private List<String> adGroupIdList;
    private String campaignSite;
}
