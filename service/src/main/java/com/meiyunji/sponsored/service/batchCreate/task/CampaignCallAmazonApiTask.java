package com.meiyunji.sponsored.service.batchCreate.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.amazon.advertising.spV3.campaign.CampaignSpV3Client;
import com.amazon.advertising.spV3.campaign.CreateSpCampaignV3Response;
import com.amazon.advertising.spV3.campaign.ListSpCampaignV3Response;
import com.amazon.advertising.spV3.campaign.entity.*;
import com.amazon.advertising.spV3.response.ApiResponseV3;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.batchCreate.dao.*;
import com.meiyunji.sponsored.service.batchCreate.dto.task.CallAmazonApiTaskResultDto;
import com.meiyunji.sponsored.service.batchCreate.enums.AdAmazonErrorTypeEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchConstants;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdLevelStatusEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdTaskLevelEnum;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchCampaign;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdSyncService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcSpCampaignService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdCampaignAll;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-11-17  17:07
 */

/**
 * 广告活动批量处理任务
 */

@Component
@Slf4j
public class CampaignCallAmazonApiTask extends AbstractCallAmazonApiTask {

    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;

    @Autowired
    private ICpcSpCampaignService cpcSpCampaignService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private IAmazonAdBatchCampaignDao amazonAdBatchCampaignDao;

    @Autowired
    private TaskStatusHelper taskStatusHelper;

    @Autowired
    private ICpcAdSyncService cpcAdSyncService;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IShopAuthService shopAuthService;

    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;

    @Autowired
    private IAmazonAdBatchGroupDao amazonAdBatchGroupDao;

    @Autowired
    private IAmazonAdBatchProductDao amazonAdBatchProductDao;

    @Autowired
    private IAmazonAdBatchKeywordDao amazonAdBatchKeywordDao;

    @Autowired
    private IAmazonAdBatchTargetingDao amazonAdBatchTargetingDao;

    @Autowired
    private IAmazonAdBatchNekeywordDao amazonAdBatchNekeywordDao;

    @Autowired
    private IAmazonAdBatchNetargetingDao amazonAdBatchNetargetingDao;

    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Autowired
    private IDorisService dorisService;


    /**
     * 调用亚马逊接口批量创建广告活动
     * @param resultDto
     * @return
     */
    public CallAmazonApiTaskResultDto call(CallAmazonApiTaskResultDto resultDto) {

        log.info("sp batch create, create campaign, batch traceId: {}, taskId: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId());
        if (CollectionUtils.isEmpty(resultDto.getSuccessIdList())) {
            log.info("sp batch create, create campaign, successIdList is empty, batch traceId: {}, taskId: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId());
            return resultDto;
        }

        //1.获取锁
        RLock lock = redissonClient.getLock(SpBatchCreateAdTaskLevelEnum.LEVEL_CAMPAIGN.getLockKey() + resultDto.getTaskId());
        try {
            boolean lockSuccess = lock.tryLock(SpBatchConstants.TRY_LOCK_SECONDS, SpBatchConstants.LOCK_SECONDS, TimeUnit.SECONDS);
            if (!lockSuccess) {
                log.info("sp batch create campaign, try lock fail, batch traceId: {}, taskId: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId());
                resultDto.setSuccessIdList(Collections.emptyList());
                return resultDto;
            }
        } catch (Exception e) {
            log.error(String.format("get redisson lock error, batch traceId: {}, taskId: {}"), e);
            resultDto.setSuccessIdList(Collections.emptyList());
            return resultDto;
        }

        List<AmazonAdBatchCampaign> campaignList = new ArrayList<>();
        Integer puid = resultDto.getPuid();
        Integer shopId = resultDto.getShopId();
        Long taskId = resultDto.getTaskId();

        try {
            //2.查询数据
            campaignList = amazonAdBatchCampaignDao.selectNeedSubmitAmazon(resultDto.getPuid(), resultDto.getShopId(), resultDto.getSuccessIdList());
            if (CollectionUtils.isEmpty(campaignList)) {
                return resultDto;
            }

            //3.公共参数
            String profileId = campaignList.get(0).getProfileId();
            String marketplaceId = campaignList.get(0).getMarketplaceId();
            List<Long> campaignIdList = campaignList.stream().map(AmazonAdBatchCampaign::getId).collect(Collectors.toList());
            resultDto.setSuccessIdList(Collections.emptyList());

            //4.转成AmazonAdCampaignAll和CampaignEntityV3
            List<AmazonAdCampaignAll> campaignAllList = new ArrayList<>(campaignList.size());
            List<CampaignEntityV3> campaignEntityV3List = new ArrayList<>(campaignList.size());
            campaignList.forEach(x -> {
                AmazonAdCampaignAll campaignAll = convertVoToCreatePo(x);
                campaignAll.setType(CampaignTypeEnum.sp.getCampaignType());
                CampaignEntityV3 campaignEntityV3 = cpcSpCampaignService.makeCampaignByCampaignPoV3(campaignAll);
                if (StringUtils.isNotBlank(x.getTags())) {
                    campaignEntityV3.setTags(JSON.parseObject(x.getTags(), new TypeReference<Map<String, String>>() {
                    }));
                }
                campaignAllList.add(campaignAll);
                campaignEntityV3List.add(campaignEntityV3);
            });

            //5.请求亚马逊
            ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
            String accessToken = shop.getAdAccessToken();

            CreateSpCampaignV3Response response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createCampaigns(accessToken, profileId, marketplaceId, campaignEntityV3List, true);
            //token过期再重试一次
            if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
                //刷新token
                accessToken = shopAuthService.refreshCpcAuth(shop);
                response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createCampaigns(accessToken, profileId, marketplaceId, campaignEntityV3List, true);
            }

            //6.网络等重试的情况
            if (response == null || response.getStatusCode() == 401 || response.getStatusCode() == 429 || response.getStatusCode() == 500 || response.getStatusCode() ==-1 || response.getData() == null) {
                //更新为重试或失败(达到执行次数上限)
                log.info("sp batch create campaign, response is null or http code not 200, batch traceId: {}, taskId: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId());
                campaignList.forEach(x -> x.setErrMsg("失败"));
                updateRetry(puid, shopId, taskId, campaignList, campaignAllList, resultDto.getLoginIp());
                return resultDto;
            }

            //6.无响应内容
            if (response.getData().getCampaigns() == null) {
                log.info("sp batch create, create campaign response none data, fail all, batch traceId: {}, taskId: {}, response: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId(), JSON.toJSONString(response));
                //认为全部失败且不重试
                String errMsg = "创建广告活动失败";
                if (response.getError() != null) {
                    if (StringUtils.isNotBlank(response.getError().getMessage())) {
                        errMsg = AmazonErrorUtils.getError(response.getError().getMessage());
                    } else if (CollectionUtils.isNotEmpty(response.getError().getErrors())) {
                        errMsg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
                    }
                }
                // 更新为全部失败
                Map<Long, String> idMsgMap = new HashMap<>(campaignIdList.size());
                for (Long id : campaignIdList) {
                    idMsgMap.put(id, errMsg);
                }
                taskStatusHelper.batchUpdateErrStatus(puid, taskId, idMsgMap, SpBatchCreateAdTaskLevelEnum.LEVEL_CAMPAIGN, SpBatchCreateAdTaskLevelEnum.LEVEL_CAMPAIGN.getDesc() + "创建失败", true);
                //记录日志
                printCreateCampaignLogAllFail(campaignAllList, errMsg, resultDto.getLoginIp());
                return resultDto;
            }


            //7.处理响应
            ApiResponseV3<campaignSuccessResultV3> campaigns = response.getData().getCampaigns();
            //成功,记录索引和亚马逊campaignId
            Map<Integer, String> successIndexIdMap = new HashMap<>();
            //失败,记录索引和错误信息
            Map<Integer, String> errorIndexMsgMap = new HashMap<>();
            //重复,记录索引和错误信息
            Map<Integer, String> duplicateIndexMsgMap = new HashMap<>();
            //重试,只需要记录索引
            List<Integer> retryIndexList = new ArrayList<>();

            //解析并填充结果到上述集合中
            processResponse(campaigns, successIndexIdMap, errorIndexMsgMap, duplicateIndexMsgMap, retryIndexList, campaignList, shop, profileId);

            //重试的
            if (CollectionUtils.isNotEmpty(retryIndexList)) {
                List<AmazonAdBatchCampaign> retryBatchCampaignList = new ArrayList<>(retryIndexList.size());
                List<AmazonAdCampaignAll> retryCampaignAllList = new ArrayList<>(retryIndexList.size());
                for (int i = 0; i < retryIndexList.size(); i++) {
                    retryBatchCampaignList.add(campaignList.get(i));
                    retryCampaignAllList.add(campaignAllList.get(i));
                }
                List<Long> collect = retryBatchCampaignList.stream().map(AmazonAdBatchCampaign::getId).collect(Collectors.toList());
                log.info("sp batch create campaign, duplicate and need retry, batch traceId: {}, taskId: {}, retry campaign id: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId(), collect);
                updateRetry(puid, shopId, taskId, retryBatchCampaignList, retryCampaignAllList, resultDto.getLoginIp());
            }

            //失败的
            if (!errorIndexMsgMap.isEmpty()) {
                //更新失败
                Map<Long, String> idMsgMap = new HashMap<>(errorIndexMsgMap.size());
                errorIndexMsgMap.forEach((k, v) -> idMsgMap.put(campaignIdList.get(k), v));
                log.info("sp batch create, create campaign exist fail, batch traceId: {}, taskId: {}, errorIdMap: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId(), idMsgMap);
                //更新失败及下层失败
                taskStatusHelper.batchUpdateErrStatus(puid, taskId, idMsgMap, SpBatchCreateAdTaskLevelEnum.LEVEL_CAMPAIGN, SpBatchCreateAdTaskLevelEnum.LEVEL_CAMPAIGN.getDesc() + "创建失败", true);
                //记录日志
                printCreateCampaignLogFail(campaignAllList, errorIndexMsgMap, resultDto.getLoginIp());
            }

            //成功的
            if (!successIndexIdMap.isEmpty()) {
                log.info("sp batch create, create campaign success, batch traceId: {}, taskId: {}, successIndexIdMap: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId(), successIndexIdMap);
                List<AmazonAdCampaignAll> successCampaignAllList = new ArrayList<>(successIndexIdMap.size());
                List<AmazonAdBatchCampaign> successBatchCampaignList = new ArrayList<>(successIndexIdMap.size());
                List<AmazonAdBatchCampaign> finalCampaignList = campaignList;
                Date date = new Date();
                successIndexIdMap.forEach((k, v) -> {
                    AmazonAdBatchCampaign batchCampaign = finalCampaignList.get(k);
                    batchCampaign.setTaskStatus(SpBatchCreateAdLevelStatusEnum.SUCCESS.getCode());
                    batchCampaign.setAmazonCampaignId(v);
                    successBatchCampaignList.add(batchCampaign);
                    AmazonAdCampaignAll campaignAll = campaignAllList.get(k);
                    campaignAll.setCampaignId(v);
                    campaignAll.setCreateTime(date);
                    campaignAll.setUpdateTime(date);
                    successCampaignAllList.add(campaignAll);
                });

                //批量保存campaignAll数据
                amazonAdCampaignAllDao.insertList4BatchCreate(puid, successCampaignAllList);

                //写入doris
                saveDoris(successCampaignAllList);

                //批量更新batchCampaign数据
                amazonAdBatchCampaignDao.batchUpdateStatusByIdList(puid, successBatchCampaignList, SpBatchCreateAdLevelStatusEnum.SUCCESS.getCode());

                //更新广告组、产品、投放等层级的亚马逊活动id
                updateAmazonCampaignId(successBatchCampaignList);

                //创建后同步一次 广告二级活动状态
                cpcAdSyncService.syncSpCampaignState(shop, StringUtils.join(successIndexIdMap.values(), ","));

                //记操作日志
                printCreateCampaignLogSuccess(successCampaignAllList, resultDto.getLoginIp());
                //设置下层需要的successId
                resultDto.setSuccessIdList(successBatchCampaignList.stream().map(AmazonAdBatchCampaign::getId).collect(Collectors.toList()));
            }
        } catch (Exception e) {
            log.error(String.format("sp batch create campaign, exception, batch traceId: %s, taskId: %s", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId()), e);
            //更新为重试
            campaignList.forEach(x -> x.setErrMsg("失败"));
            updateRetry(puid, shopId, taskId, campaignList, null, resultDto.getLoginIp());
            return resultDto;
        } finally {
            lock.unlock();
        }
        return resultDto;
    }

    /**
     * 转换BatchCampaign数据为AmazonAdCampaignAll
     * @param campaign
     * @return
     */
    private AmazonAdCampaignAll convertVoToCreatePo(AmazonAdBatchCampaign campaign) {
        AmazonAdCampaignAll amazonAdCampaign = new AmazonAdCampaignAll();
        amazonAdCampaign.setPuid(campaign.getPuid());
        amazonAdCampaign.setName(campaign.getName().trim());
        amazonAdCampaign.setBudget(campaign.getBudget());
        amazonAdCampaign.setShopId(campaign.getShopId());
        amazonAdCampaign.setProfileId(campaign.getProfileId());
        amazonAdCampaign.setMarketplaceId(campaign.getMarketplaceId());
        amazonAdCampaign.setCreateId(campaign.getCreateId());
        amazonAdCampaign.setTargetingType(campaign.getTargetingType());
        amazonAdCampaign.setCampaignType(Constants.SPONSORED_PRODUCTS);
        amazonAdCampaign.setState(Constants.ENABLED);
        amazonAdCampaign.setCreateInAmzup(Constants.CREATE_IN_AMZUP);
        if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
            amazonAdCampaign.setPortfolioId(campaign.getPortfolioId());
        }
        amazonAdCampaign.setStartDate(campaign.getStartDate());
        if (Objects.nonNull(campaign.getEndDate())) {
            amazonAdCampaign.setEndDate(campaign.getEndDate());
        }
        amazonAdCampaign.setStrategy(campaign.getStrategy());
        amazonAdCampaign.setAdjustments(campaign.getAdjustments());
        amazonAdCampaign.setAdTargetType(campaign.getTargetingType());
        amazonAdCampaign.setType(CampaignTypeEnum.sp.getCampaignType());
        amazonAdCampaign.setTags(campaign.getTags());
        amazonAdCampaign.setType(CampaignTypeEnum.sp.getCampaignType());
        return amazonAdCampaign;
    }


    /**
     * 更新需要重试的活动为重试或失败(达到上限)
     * @param puid
     * @param shopId
     * @param taskId
     * @param batchCampaignList
     * @param campaignAllList
     * @param loginIp
     */
    private void updateRetry(Integer puid, Integer shopId, Long taskId, List<AmazonAdBatchCampaign> batchCampaignList, List<AmazonAdCampaignAll> campaignAllList, String loginIp) {
        //拆分成2组，一组为需要重试的，一组为达到执行上限的则更新为失败
        List<Long> retryList = new ArrayList<>();
        List<AmazonAdBatchCampaign> failBatchCampaignList = new ArrayList<>();
        List<AmazonAdCampaignAll> failCampaignAllList = new ArrayList<>();
        for (int i = 0; i < batchCampaignList.size(); i++) {
            AmazonAdBatchCampaign x = batchCampaignList.get(i);
            if (x.getExecuteCount() >= SpBatchConstants.EXECUTE_COUNT_LIMIT - 1) {
                failBatchCampaignList.add(x);
                if (CollectionUtils.isNotEmpty(campaignAllList)) {
                    failCampaignAllList.add(campaignAllList.get(i));
                }
            } else {
                retryList.add(x.getId());
            }
        }

        //重试
        if (CollectionUtils.isNotEmpty(retryList)) {
            amazonAdBatchCampaignDao.updateStatusAndRetryByTaskId(puid, shopId, taskId, retryList, SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode(), DateUtil.addSecond(new Date(), SpBatchConstants.RETRY_AFTER_SECONDS));
        }

        //失败
        if (CollectionUtils.isNotEmpty(failBatchCampaignList)) {
            //更新失败及下层失败
            Map<Long, String> idMsgMap = new HashMap<>(failBatchCampaignList.size());
            failBatchCampaignList.forEach(x -> idMsgMap.put(x.getId(), x.getErrMsg()));
            taskStatusHelper.batchUpdateErrStatus(puid, taskId, idMsgMap, SpBatchCreateAdTaskLevelEnum.LEVEL_CAMPAIGN, SpBatchCreateAdTaskLevelEnum.LEVEL_CAMPAIGN.getDesc() + "创建失败", true);
            //记录日志
            if (CollectionUtils.isNotEmpty(failBatchCampaignList)) {
                Map<Integer, String> indexMsgMap = new HashMap<>(failBatchCampaignList.size());
                for (int i = 0; i < failCampaignAllList.size(); i++) {
                    indexMsgMap.put(i, failBatchCampaignList.get(i).getErrMsg());
                }
                printCreateCampaignLogFail(failCampaignAllList, indexMsgMap, loginIp);
            }
        }
    }

    /**
     * 创建广告活动，解析结果
     * @param campaigns
     * @param successIndexIdMap
     * @param errorIndexMsgMap
     * @param duplicateIndexMsgMap
     * @param retryIndexList
     * @param campaignList
     * @param shop
     * @param profileId
     */
    private void processResponse(ApiResponseV3<campaignSuccessResultV3> campaigns,
                                 Map<Integer, String> successIndexIdMap,
                                 Map<Integer, String> errorIndexMsgMap,
                                 Map<Integer, String> duplicateIndexMsgMap,
                                 List<Integer> retryIndexList,
                                 List<AmazonAdBatchCampaign> campaignList,
                                 ShopAuth shop,
                                 String profileId) {
        //成功
        if (CollectionUtils.isNotEmpty(campaigns.getSuccess())) {
            campaigns.getSuccess().forEach(x -> successIndexIdMap.put(x.getIndex(), x.getCampaignId()));
        }

        //失败和重复
        if (CollectionUtils.isNotEmpty(campaigns.getError())) {
            campaigns.getError().forEach(x -> {
                String errorType = x.getErrors().get(0).getErrorType();
                String msg = errorType;
                try {
                    msg = String.valueOf(x.getErrors().get(0).getErrorValue().get(errorType).get("message"));
                } catch (Exception e) {
                    log.error("sp batch create campaign get error message exception", e);
                }
                if (AdAmazonErrorTypeEnum.DUPLICATE_VALUE_ERROR.getType().equals(errorType)) {
                    duplicateIndexMsgMap.put(x.getIndex(), msg);
                } else {
                    errorIndexMsgMap.put(x.getIndex(), msg);
                }
            });
        }

        //重复的再次检查，最终可能加入重试、失败、成功
        if (!duplicateIndexMsgMap.isEmpty()) {
            //key为name+tag，value为索引
            Map<String, Integer> tagIndexMap = new HashMap<>(duplicateIndexMsgMap.size());
            //重复的广告活动的名称
            List<String> duplicateNameList = new ArrayList<>(duplicateIndexMsgMap.size());
            //填充数据
            duplicateIndexMsgMap.forEach((k, v) -> {
                AmazonAdBatchCampaign batchCampaign = campaignList.get(k);
                JSONObject tagJson = JSON.parseObject(batchCampaign.getTags());
                tagIndexMap.put(batchCampaign.getName() + tagJson.getString(SpBatchConstants.CAMPAIGN_TAG_KEY), k);
                duplicateNameList.add(batchCampaign.getName());
            });

            //调用亚马逊列表
            ListSpCampaignV3Response listCampaignResponse = CampaignSpV3Client.getInstance()
                    .listCampaigns(shop.getAdAccessToken(), profileId, shop.getMarketplaceId(), null, null, null, duplicateNameList, QueryTermMatchType.EXACT_MATCH, false, null, duplicateNameList.size());
            //token过期再重试一次
            if (listCampaignResponse != null && listCampaignResponse.getStatusCode() != null && listCampaignResponse.getStatusCode() == 401) {
                //刷新token
                String accessToken = shopAuthService.refreshCpcAuth(shop);
                listCampaignResponse = CampaignSpV3Client.getInstance()
                        .listCampaigns(accessToken, profileId, shop.getMarketplaceId(), null, null, null, duplicateNameList, QueryTermMatchType.EXACT_MATCH, false, null, duplicateNameList.size());
            }

            //重复列表结果解析
            if (listCampaignResponse != null && listCampaignResponse.getData() != null && CollectionUtils.isNotEmpty(listCampaignResponse.getData().getCampaigns())) {
                //标记成功或失败
                for (CampaignExtendEntityV3 campaign : listCampaignResponse.getData().getCampaigns()) {
                    Integer index = null;
                    if ((!campaign.getTags().isEmpty()) && (index = tagIndexMap.get(campaign.getName() + campaign.getTags().getOrDefault(SpBatchConstants.CAMPAIGN_TAG_KEY, ""))) != null) {
                        //认为成功
                        successIndexIdMap.put(index, campaign.getCampaignId());
                        //移除重复
                        duplicateIndexMsgMap.remove(index);
                    }
                }
                //把剩余的放入失败
                errorIndexMsgMap.putAll(duplicateIndexMsgMap);
            } else {
                //全部重试
                retryIndexList.addAll(duplicateIndexMsgMap.keySet());
            }
        }
    }

    /**
     * 更新组、产品、投放、否定投放的活动id
     * @param batchCampaignList
     */
    private void updateAmazonCampaignId(List<AmazonAdBatchCampaign> batchCampaignList) {
        //广告组
        amazonAdBatchGroupDao.batchUpdateCampaignId(batchCampaignList);
        //广告产品
        amazonAdBatchProductDao.batchUpdateCampaignId(batchCampaignList);
        //关键词投放
        amazonAdBatchKeywordDao.batchUpdateCampaignId(batchCampaignList);
        //商品投放
        amazonAdBatchTargetingDao.batchUpdateCampaignId(batchCampaignList);
        //否定关键词
        amazonAdBatchNekeywordDao.batchUpdateCampaignId(batchCampaignList);
        //否定投放
        amazonAdBatchNetargetingDao.batchUpdateCampaignId(batchCampaignList);
    }


    /**
     * 打印成功日志
     * @param campaignAllList
     * @param loginIp
     */
    private void printCreateCampaignLogSuccess(List<AmazonAdCampaignAll> campaignAllList, String loginIp) {
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        campaignAllList.forEach(x -> {
            AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(null, x);
            adManageOperationLog.setIp(loginIp);
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            adManageOperationLog.setCampaignId(x.getCampaignId());
            adManageOperationLogs.add(adManageOperationLog);
        });
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
    }


    /**
     * 打印失败日志，日志统一
     * @param campaignAllList
     * @param errMsg
     * @param loginIp
     */
    private void printCreateCampaignLogAllFail(List<AmazonAdCampaignAll> campaignAllList, String errMsg, String loginIp) {
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        campaignAllList.forEach(x -> {
            AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(null, x);
            adManageOperationLog.setIp(loginIp);
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(errMsg);
            adManageOperationLogs.add(adManageOperationLog);
        });
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
    }

    /**
     * 打印失败日志
     * @param campaignAllList
     * @param errorIndexMsgMap
     * @param loginIp
     */
    private void printCreateCampaignLogFail(List<AmazonAdCampaignAll> campaignAllList, Map<Integer, String> errorIndexMsgMap, String loginIp) {
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        errorIndexMsgMap.forEach((k, v) -> {
            AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(null, campaignAllList.get(k));
            adManageOperationLog.setIp(loginIp);
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(v);
            adManageOperationLogs.add(adManageOperationLog);
        });
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
    }

    /**
     * routine load写入doris
     * @param amazonAdCampaignList
     */
    private void saveDoris(List<AmazonAdCampaignAll> amazonAdCampaignList) {
        try {
            List<OdsAmazonAdCampaignAll> collect = amazonAdCampaignList.stream().map(x -> {
                OdsAmazonAdCampaignAll odsAmazonAdCampaignAll = new OdsAmazonAdCampaignAll();
                BeanUtils.copyProperties(x, odsAmazonAdCampaignAll);
                if (StringUtils.isNotBlank(odsAmazonAdCampaignAll.getState())) {
                    odsAmazonAdCampaignAll.setState(odsAmazonAdCampaignAll.getState().toLowerCase());
                }
                return odsAmazonAdCampaignAll;
            }).collect(Collectors.toList());
            dorisService.saveDorisByRoutineLoad(null, collect);
        } catch (Exception e) {
            log.error("sp campaign save doris error", e);
        }
    }


}
