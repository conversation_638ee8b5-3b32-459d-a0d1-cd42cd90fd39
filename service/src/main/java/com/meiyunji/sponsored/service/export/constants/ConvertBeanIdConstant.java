package com.meiyunji.sponsored.service.export.constants;

/**
 * @author: sunlin<PERSON>
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2023-09-12  11:04
 */


public class ConvertBeanIdConstant {

    public static final String SP_CAMPAIGN = "campaignSpReportExportConvert";
    public static final String SP_ADGROUP = "adGroupSpReportExportConvert";
    public static final String SP_PRODUCT = "productSpReportExportConvert";
    public static final String SP_TARGETING = "targetingSpReportExportConvert";
    public static final String SP_SPACE = "spaceSpReportExportConvert";
    public static final String AMAZON_BUSINESS = "amazonBusinessReportExportConvert";
    public static final String SP_SEARCHTERM = "searchTermSpReportExportConvert";
    public static final String SP_PURCHASED = "purchasedSpReportExportConvert";
    public static final String AD_FLOW = "flowReportExportConvert";

    public static final String SB_CAMPAIGN = "campaignSbReportExportConvert";
    public static final String SB_TARGETING = "targetingSbReportExportConvert";
    public static final String SB_SPACE = "spaceSbReportExportConvert";
    public static final String SB_SEARCHTERM = "searchTermSbReportExportConvert";

    public static final String SBV_CAMPAIGN = "campaignSbvReportExportConvert";
    public static final String SBV_TARGETING = "targetingSbvReportExportConvert";
    public static final String SBV_SPACE = "spaceSbvReportExportConvert";
    public static final String SBV_SEARCHTERM = "searchTermSbvReportExportConvert";

    public static final String SD_CAMPAIGN = "campaignSdReportExportConvert";
    public static final String SD_ADGROUP = "adGroupSdReportExportConvert";
    public static final String SD_PRODUCT = "productSdReportExportConvert";
    public static final String SD_PURCHASED = "purchasedSdReportExportConvert";
    public static final String SD_TARGETING = "targetingSdReportExportConvert";
    public static final String SD_MATCHED = "campaignMatchedTargetSdReportExportConvert";


    ;

}
