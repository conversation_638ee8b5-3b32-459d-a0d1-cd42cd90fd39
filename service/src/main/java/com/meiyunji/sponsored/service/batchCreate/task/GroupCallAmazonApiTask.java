package com.meiyunji.sponsored.service.batchCreate.task;

import com.alibaba.fastjson.JSON;
import com.amazon.advertising.spV3.group.CreateSpGroupV3Response;
import com.amazon.advertising.spV3.group.GroupSpV3Client;
import com.amazon.advertising.spV3.group.ListSpGroupV3Response;
import com.amazon.advertising.spV3.group.entity.GroupEntityV3;
import com.amazon.advertising.spV3.group.entity.GroupExtendEntityV3;
import com.amazon.advertising.spV3.group.entity.GroupSuccessResultV3;
import com.amazon.advertising.spV3.response.ApiResponseV3;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.batchCreate.dao.*;
import com.meiyunji.sponsored.service.batchCreate.dto.task.CallAmazonApiTaskResultDto;
import com.meiyunji.sponsored.service.batchCreate.enums.AdAmazonErrorTypeEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchConstants;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdLevelStatusEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdTaskLevelEnum;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchGroup;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdGroupService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdGroup;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-11-17  17:07
 */

/**
 * 广告组批量处理任务
 */

@Component
@Slf4j
public class GroupCallAmazonApiTask extends AbstractCallAmazonApiTask {

    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;

    @Autowired
    private ICpcAdGroupService cpcAdGroupService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private IAmazonAdBatchGroupDao amazonAdBatchGroupDao;

    @Autowired
    private TaskStatusHelper taskStatusHelper;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IShopAuthService shopAuthService;

    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;

    @Autowired
    private IAmazonAdBatchProductDao amazonAdBatchProductDao;

    @Autowired
    private IAmazonAdBatchKeywordDao amazonAdBatchKeywordDao;

    @Autowired
    private IAmazonAdBatchTargetingDao amazonAdBatchTargetingDao;

    @Autowired
    private IAmazonAdBatchNekeywordDao amazonAdBatchNekeywordDao;

    @Autowired
    private IAmazonAdBatchNetargetingDao amazonAdBatchNetargetingDao;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Autowired
    private IDorisService dorisService;


    /**
     * 调用亚马逊接口批量创建广告活动
     * @param resultDto
     * @return
     */
    public CallAmazonApiTaskResultDto call(CallAmazonApiTaskResultDto resultDto) {

        log.info("sp batch create, create group, batch traceId: {}, taskId: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId());
        if (CollectionUtils.isEmpty(resultDto.getSuccessIdList())) {
            log.info("sp batch create, create group, successIdList is empty, batch traceId: {}, taskId: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId());
            return resultDto;
        }

        //1.获取锁
        RLock lock = redissonClient.getLock(SpBatchCreateAdTaskLevelEnum.LEVEL_GROUP.getLockKey() + resultDto.getTaskId());
        try {
            boolean lockSuccess = lock.tryLock(SpBatchConstants.TRY_LOCK_SECONDS, SpBatchConstants.LOCK_SECONDS, TimeUnit.SECONDS);
            if (!lockSuccess) {
                log.info("sp batch create group, try lock fail, batch traceId: {}, taskId: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId());
                resultDto.setSuccessIdList(Collections.emptyList());
                return resultDto;
            }
        } catch (Exception e) {
            log.error(String.format("get redisson lock error, batch traceId: {}, taskId: {}"), e);
            resultDto.setSuccessIdList(Collections.emptyList());
            return resultDto;
        }

        List<AmazonAdBatchGroup> batchGroupList = new ArrayList<>();
        Integer puid = resultDto.getPuid();
        Integer shopId = resultDto.getShopId();
        Long taskId = resultDto.getTaskId();

        try {

            //2.查询数据
            batchGroupList = amazonAdBatchGroupDao.selectNeedSubmitAmazon(resultDto.getPuid(), resultDto.getShopId(), resultDto.getTaskId(), resultDto.getSuccessIdList(), resultDto.isCurrentLevel());
            if (CollectionUtils.isEmpty(batchGroupList)) {
                return resultDto;
            }

            //3.公共参数
            String profileId = batchGroupList.get(0).getProfileId();
            String marketplaceId = batchGroupList.get(0).getMarketplaceId();
            List<Long> batchGroupIdList = batchGroupList.stream().map(AmazonAdBatchGroup::getId).collect(Collectors.toList());
            resultDto.setSuccessIdList(Collections.emptyList());

            //4.转成AmazonAdGroup和GroupEntityV3
            List<AmazonAdGroup> adGroupList = new ArrayList<>(batchGroupList.size());
            List<GroupEntityV3> groupEntityV3List = new ArrayList<>(batchGroupList.size());
            batchGroupList.forEach(x -> {
                AmazonAdGroup adGroup = convertVoToCreatePo(x);
                GroupEntityV3 groupEntityV3 = cpcAdGroupService.makeAdGroupByAdGroupPo(adGroup);
                adGroupList.add(adGroup);
                groupEntityV3List.add(groupEntityV3);
            });

            //5.请求亚马逊
            ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
            String accessToken = shop.getAdAccessToken();

            CreateSpGroupV3Response response = GroupSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createGroups(accessToken, profileId, marketplaceId, groupEntityV3List,true);
            //token过期再重试一次
            if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
                //刷新token
                accessToken = shopAuthService.refreshCpcAuth(shop);
                response = GroupSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createGroups(accessToken, profileId, marketplaceId, groupEntityV3List,true);
            }

            //6.网络等重试的情况
            if (response == null || response.getStatusCode() == 401 || response.getStatusCode() == 429 || response.getStatusCode() == 500 || response.getStatusCode() ==-1 || response.getData() == null) {
                //更新为重试或失败(达到执行次数上限)
                log.info("sp batch create group, response is null or http code not 200, batch traceId: {}, taskId: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId());
                batchGroupList.forEach(x -> x.setErrMsg("网络异常"));
                updateRetry(puid, shopId, taskId, batchGroupList, adGroupList, resultDto.getLoginIp());
                return resultDto;
            }

            //6.无响应内容
            if (response.getData().getAdGroups() == null) {
                log.info("sp batch create, create group response none data, fail all, batch traceId: {}, taskId: {}, response: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId(), JSON.toJSONString(response));
                //认为全部失败且不重试
                String errMsg = "创建广告组失败";
                if (response.getError() != null) {
                    if (StringUtils.isNotBlank(response.getError().getMessage())) {
                        errMsg = AmazonErrorUtils.getError(response.getError().getMessage());
                    } else if (CollectionUtils.isNotEmpty(response.getError().getErrors())) {
                        errMsg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
                    }
                }
                // 更新为全部失败
                Map<Long, String> idMsgMap = new HashMap<>(batchGroupIdList.size());
                for (Long id : batchGroupIdList) {
                    idMsgMap.put(id, errMsg);
                }
                taskStatusHelper.batchUpdateErrStatus(puid, taskId, idMsgMap, SpBatchCreateAdTaskLevelEnum.LEVEL_GROUP, SpBatchCreateAdTaskLevelEnum.LEVEL_GROUP.getDesc() + "创建失败", true);
                //记录日志
                printCreateCampaignLogAllFail(adGroupList, errMsg, resultDto.getLoginIp());
                return resultDto;
            }


            //7.处理响应
            ApiResponseV3<GroupSuccessResultV3> groups = response.getData().getAdGroups();
            //成功,记录索引和亚马逊groupId
            Map<Integer, String> successIndexIdMap = new HashMap<>();
            //失败,记录索引和错误信息
            Map<Integer, String> errorIndexMsgMap = new HashMap<>();
            //重复,记录索引和错误信息
            Map<Integer, String> duplicateIndexMsgMap = new HashMap<>();
            //重试,只需要记录索引
            List<Integer> retryIndexList = new ArrayList<>();

            //解析并填充结果到上述集合中
            processResponse(groups, successIndexIdMap, errorIndexMsgMap, duplicateIndexMsgMap, retryIndexList, batchGroupList, shop, profileId);

            //重试的
            if (CollectionUtils.isNotEmpty(retryIndexList)) {
                List<AmazonAdBatchGroup> retryBatchGroupList = new ArrayList<>(retryIndexList.size());
                List<AmazonAdGroup> retryAdGroupList = new ArrayList<>(retryIndexList.size());
                for (int i = 0; i < retryIndexList.size(); i++) {
                    retryBatchGroupList.add(batchGroupList.get(i));
                    retryAdGroupList.add(adGroupList.get(i));
                }
                List<Long> collect = retryBatchGroupList.stream().map(AmazonAdBatchGroup::getId).collect(Collectors.toList());
                log.info("sp batch create group, duplicate and need retry, batch traceId: {}, taskId: {}, retry group id: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId(), StringUtils.join(collect, ","));
                updateRetry(puid, shopId, taskId, retryBatchGroupList, retryAdGroupList, resultDto.getLoginIp());
            }

            //失败的
            if (!errorIndexMsgMap.isEmpty()) {
                //更新失败
                Map<Long, String> idMsgMap = new HashMap<>(errorIndexMsgMap.size());
                errorIndexMsgMap.forEach((k, v) -> idMsgMap.put(batchGroupIdList.get(k), v));
                log.info("sp batch create, create group exist fail, batch traceId: {}, taskId: {}, errorIdMap: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId(), idMsgMap);
                //更新失败及下层失败
                taskStatusHelper.batchUpdateErrStatus(puid, taskId, idMsgMap, SpBatchCreateAdTaskLevelEnum.LEVEL_GROUP, SpBatchCreateAdTaskLevelEnum.LEVEL_GROUP.getDesc() + "创建失败", true);
                //记录日志
                printCreateCampaignLogFail(adGroupList, errorIndexMsgMap, resultDto.getLoginIp());
            }

            //成功的
            if (!successIndexIdMap.isEmpty()) {
                log.info("sp batch create, create group success, batch traceId: {}, taskId: {}, successIndexIdMap: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId(), successIndexIdMap);
                List<AmazonAdGroup> successAdGroupList = new ArrayList<>(successIndexIdMap.size());
                List<AmazonAdBatchGroup> successBatchGroupList = new ArrayList<>(successIndexIdMap.size());
                List<AmazonAdBatchGroup> finalBatchGroupList = batchGroupList;
                Date date = new Date();
                successIndexIdMap.forEach((k, v) -> {
                    AmazonAdBatchGroup batchGroup = finalBatchGroupList.get(k);
                    batchGroup.setTaskStatus(SpBatchCreateAdLevelStatusEnum.SUCCESS.getCode());
                    batchGroup.setAmazonAdGroupId(v);
                    successBatchGroupList.add(batchGroup);
                    AmazonAdGroup adGroup = adGroupList.get(k);
                    adGroup.setAdGroupId(v);
                    adGroup.setCreateTime(date);
                    adGroup.setUpdateTime(date);
                    successAdGroupList.add(adGroup);
                });

                //批量保存adGroup数据
                amazonAdGroupDao.insertList4BatchCreate(puid, successAdGroupList);

                //写入doris
                saveDoris(successAdGroupList);

                //批量更新batchGroup数据
                amazonAdBatchGroupDao.batchUpdateStatusByIdList(puid, successBatchGroupList, SpBatchCreateAdLevelStatusEnum.SUCCESS.getCode());

                //更新产品、投放等的amazon_ad_group_id
                updateAmazonAdGroupId(successBatchGroupList);

                //记操作日志
                printCreateCampaignLogSuccess(successAdGroupList, resultDto.getLoginIp());
                //设置下层需要的successId
                resultDto.setSuccessIdList(successBatchGroupList.stream().map(AmazonAdBatchGroup::getId).collect(Collectors.toList()));
            }
        } catch (Exception e) {
            log.error(String.format("sp batch create group, exception, batch traceId: %s, taskId: %s", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId()), e);
            //更新为重试
            batchGroupList.forEach(x -> x.setErrMsg("失败"));
            updateRetry(puid, shopId, taskId, batchGroupList, null, resultDto.getLoginIp());
            return resultDto;
        } finally {
            lock.unlock();
        }
        return resultDto;
    }

    /**
     * 转换BatchGroup数据为AmazonAdGroup
     * @param batchGroup
     * @return
     */
    private AmazonAdGroup convertVoToCreatePo(AmazonAdBatchGroup batchGroup) {
        AmazonAdGroup amazonAdGroup = new AmazonAdGroup();
        amazonAdGroup.setPuid(batchGroup.getPuid());
        amazonAdGroup.setShopId(batchGroup.getShopId());
        amazonAdGroup.setMarketplaceId(batchGroup.getMarketplaceId());
        amazonAdGroup.setCampaignId(batchGroup.getAmazonCampaignId());
        amazonAdGroup.setProfileId(batchGroup.getProfileId());
        amazonAdGroup.setName(batchGroup.getName());
        amazonAdGroup.setDefaultBid(batchGroup.getDefaultBid().doubleValue());
        amazonAdGroup.setAdGroupType(batchGroup.getAdGroupType());
        amazonAdGroup.setState(Constants.ENABLED);
        amazonAdGroup.setCreateId(batchGroup.getCreateId());
        amazonAdGroup.setCreateTime(new Date());
        return amazonAdGroup;
    }


    /**
     * 更新需要重试的组为重试或失败(达到上限)
     * @param puid
     * @param shopId
     * @param taskId
     * @param batchGroupList
     * @param adGroupList
     * @param loginIp
     */
    private void updateRetry(Integer puid, Integer shopId, Long taskId, List<AmazonAdBatchGroup> batchGroupList, List<AmazonAdGroup> adGroupList, String loginIp) {
        //拆分成2组，一组为需要重试的，一组为达到执行上限的则更新为失败
        List<Long> retryList = new ArrayList<>();
        List<AmazonAdBatchGroup> failBatchGroupList = new ArrayList<>();
        List<AmazonAdGroup> failAdGroupList = new ArrayList<>();
        for (int i = 0; i < batchGroupList.size(); i++) {
            AmazonAdBatchGroup x = batchGroupList.get(i);
            if (x.getExecuteCount() >= SpBatchConstants.EXECUTE_COUNT_LIMIT - 1) {
                failBatchGroupList.add(x);
                if (CollectionUtils.isNotEmpty(adGroupList)) {
                    failAdGroupList.add(adGroupList.get(i));
                }
            } else {
                retryList.add(x.getId());
            }
        }

        //重试
        if (CollectionUtils.isNotEmpty(retryList)) {
            amazonAdBatchGroupDao.updateStatusAndRetryByTaskId(puid, shopId, taskId, retryList, SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode(), DateUtil.addSecond(new Date(), SpBatchConstants.RETRY_AFTER_SECONDS));
        }

        //失败
        if (CollectionUtils.isNotEmpty(failBatchGroupList)) {
            //更新失败及下层失败
            Map<Long, String> idMsgMap = new HashMap<>(failBatchGroupList.size());
            failBatchGroupList.forEach(x -> idMsgMap.put(x.getId(), x.getErrMsg()));
            taskStatusHelper.batchUpdateErrStatus(puid, taskId, idMsgMap, SpBatchCreateAdTaskLevelEnum.LEVEL_GROUP, SpBatchCreateAdTaskLevelEnum.LEVEL_GROUP.getDesc() + "创建失败", true);
            //记录日志
            if (CollectionUtils.isNotEmpty(failBatchGroupList)) {
                Map<Integer, String> indexMsgMap = new HashMap<>(failBatchGroupList.size());
                for (int i = 0; i < failAdGroupList.size(); i++) {
                    indexMsgMap.put(i, failBatchGroupList.get(i).getErrMsg());
                }
                printCreateCampaignLogFail(failAdGroupList, indexMsgMap, loginIp);
            }
        }
    }

    /**
     * 创建广告组，解析结果
     * @param groups
     * @param successIndexIdMap
     * @param errorIndexMsgMap
     * @param duplicateIndexMsgMap
     * @param retryIndexList
     * @param batchGroupList
     * @param shop
     * @param profileId
     */
    private void processResponse(ApiResponseV3<GroupSuccessResultV3> groups,
                                 Map<Integer, String> successIndexIdMap,
                                 Map<Integer, String> errorIndexMsgMap,
                                 Map<Integer, String> duplicateIndexMsgMap,
                                 List<Integer> retryIndexList,
                                 List<AmazonAdBatchGroup> batchGroupList,
                                 ShopAuth shop,
                                 String profileId) {
        //成功
        if (CollectionUtils.isNotEmpty(groups.getSuccess())) {
            groups.getSuccess().forEach(x -> successIndexIdMap.put(x.getIndex(), x.getAdGroupId()));
        }

        //失败和重复
        if (CollectionUtils.isNotEmpty(groups.getError())) {
            groups.getError().forEach(x -> {
                String errorType = x.getErrors().get(0).getErrorType();
                String msg = errorType;
                try {
                    msg = String.valueOf(x.getErrors().get(0).getErrorValue().get(errorType).get("message"));
                } catch (Exception e) {
                    log.error("sp batch create campaign get error message exception", e);
                }
                if (AdAmazonErrorTypeEnum.DUPLICATE_VALUE_ERROR.getType().equals(errorType)) {
                    duplicateIndexMsgMap.put(x.getIndex(), msg);
                } else {
                    errorIndexMsgMap.put(x.getIndex(), msg);
                }
            });
        }

        //重复的再次检查，最终可能加入重试、成功
        if (!duplicateIndexMsgMap.isEmpty()) {
            //key为活动id+广告组名称，value为索引
            Map<String, Integer> nameIndexMap = new HashMap<>(duplicateIndexMsgMap.size());
            //重复的广告活动id(亚马逊)和对应的广告组名称集合
            Map<String, List<String>> duplicateNameMap = new HashMap<>();
            //填充数据
            duplicateIndexMsgMap.forEach((k, v) -> {
                AmazonAdBatchGroup batchGroup = batchGroupList.get(k);
                nameIndexMap.put(batchGroup.getAmazonCampaignId() + batchGroup.getName(), k);
                List<String> duplicateNameList = duplicateNameMap.get(batchGroup.getAmazonAdGroupId());
                if (CollectionUtils.isEmpty(duplicateNameList)) {
                    duplicateNameList = new ArrayList<>();
                }
                duplicateNameList.add(batchGroup.getName());
                duplicateNameMap.put(batchGroup.getAmazonCampaignId(), duplicateNameList);
            });

            List<String> campaignIdList = new ArrayList<>(duplicateNameMap.keySet());
            //调用亚马逊列表
            ListSpGroupV3Response listGroupResponse = GroupSpV3Client.getInstance().listGroups(shop.getAdAccessToken(), profileId, shop.getMarketplaceId(), campaignIdList, null, null);
            //token过期再重试一次
            if (listGroupResponse != null && listGroupResponse.getStatusCode() != null && listGroupResponse.getStatusCode() == 401) {
                //刷新token
                String accessToken = shopAuthService.refreshCpcAuth(shop);
                listGroupResponse = GroupSpV3Client.getInstance().listGroups(accessToken, profileId, shop.getMarketplaceId(), campaignIdList, null, null);
            }

            //重复列表结果解析
            if (listGroupResponse != null && listGroupResponse.getData() != null && CollectionUtils.isNotEmpty(listGroupResponse.getData().getAdGroups())) {
                //标记成功或失败
                for (GroupExtendEntityV3 group : listGroupResponse.getData().getAdGroups()) {
                    Integer index = null;
                    if ((index = nameIndexMap.get(group.getCampaignId() + group.getName())) != null) {
                        //认为成功
                        successIndexIdMap.put(index, group.getAdGroupId());
                        //移除重复
                        duplicateIndexMsgMap.remove(index);
                    }
                }
                //把剩余的放入重试
                retryIndexList.addAll(duplicateIndexMsgMap.keySet());
            } else {
                //全部重试
                retryIndexList.addAll(duplicateIndexMsgMap.keySet());
            }
        }
    }

    /**
     * 修改广告产品、投放、否定投放表的亚马逊广告组id
     * @param batchGroupList
     */
    private void updateAmazonAdGroupId(List<AmazonAdBatchGroup> batchGroupList) {
        //广告产品
        amazonAdBatchProductDao.batchUpdateAdGroupId(batchGroupList);
        //关键词投放
        amazonAdBatchKeywordDao.batchUpdateAdGroupId(batchGroupList);
        //商品投放
        amazonAdBatchTargetingDao.batchUpdateAdGroupId(batchGroupList);
        //否定关键词
        amazonAdBatchNekeywordDao.batchUpdateAdGroupId(batchGroupList);
        //否定投放
        amazonAdBatchNetargetingDao.batchUpdateAdGroupId(batchGroupList);
    }


    /**
     * 打印成功日志
     * @param adGroupList
     * @param loginIp
     */
    private void printCreateCampaignLogSuccess(List<AmazonAdGroup> adGroupList, String loginIp) {
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        adGroupList.forEach(x -> {
            AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdGroupLog(null, x);
            adManageOperationLog.setIp(loginIp);
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            adManageOperationLog.setAdGroupId(x.getAdGroupId());
            adManageOperationLogs.add(adManageOperationLog);
        });
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
    }


    /**
     * 打印失败日志，日志统一
     * @param adGroupList
     * @param errMsg
     * @param loginIp
     */
    private void printCreateCampaignLogAllFail(List<AmazonAdGroup> adGroupList, String errMsg, String loginIp) {
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        adGroupList.forEach(x -> {
            AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdGroupLog(null, x);
            adManageOperationLog.setIp(loginIp);
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(errMsg);
            adManageOperationLogs.add(adManageOperationLog);
        });
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
    }

    /**
     * 打印失败日志
     * @param adGroupList
     * @param errorIndexMsgMap
     * @param loginIp
     */
    private void printCreateCampaignLogFail(List<AmazonAdGroup> adGroupList, Map<Integer, String> errorIndexMsgMap, String loginIp) {
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        errorIndexMsgMap.forEach((k, v) -> {
            AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdGroupLog(null, adGroupList.get(k));
            adManageOperationLog.setIp(loginIp);
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(v);
            adManageOperationLogs.add(adManageOperationLog);
        });
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
    }

    /**
     * routine load写入doris
     * @param adGroupList
     */
    private void saveDoris(List<AmazonAdGroup> adGroupList) {
        try {
            Date date = new Date();
            List<OdsAmazonAdGroup> collect = adGroupList.stream().map(x -> {
                OdsAmazonAdGroup odsAmazonAdGroup = new OdsAmazonAdGroup();
                BeanUtils.copyProperties(x, odsAmazonAdGroup);
                odsAmazonAdGroup.setCreateTime(date);
                odsAmazonAdGroup.setUpdateTime(date);
                if (StringUtils.isNotBlank(odsAmazonAdGroup.getState())) {
                    odsAmazonAdGroup.setState(odsAmazonAdGroup.getState().toLowerCase());
                }
                return odsAmazonAdGroup;
            }).collect(Collectors.toList());
            dorisService.saveDorisByRoutineLoad(null, collect);
        } catch (Exception e) {
            log.error("sp group save doris error", e);
        }
    }


}
