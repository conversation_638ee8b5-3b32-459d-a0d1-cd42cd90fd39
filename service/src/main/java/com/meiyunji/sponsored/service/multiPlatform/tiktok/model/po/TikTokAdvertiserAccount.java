package com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po;

import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2025-05-19  10:50
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@DbTable(value = "t_tiktok_advertiser_account")
public class TikTokAdvertiserAccount extends BasePo {
    @DbColumn(value = "id", autoIncrement = true, key = true)
    private Long id;

    /**
     * 用户puid
     */
    @DbColumn(value = "puid")
    private Integer puid;

    /**
     * 店铺id
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;

    /**
     * 商务中心ID
     */
    @DbColumn(value = "owner_bc_id")
    private String ownerBcId;

    /**
     * 广告账号ID
     */
    @DbColumn(value = "advertiser_id")
    private String advertiserId;

    /**
     * 广告账号名称
     */
    @DbColumn(value = "name")
    private String name;

    /**
     * 店铺状态,0未授权,1授权成功（com.meiyunji.sponsored.service.multiPlatform.tiktok.enums.TikTokAdvertiserAccountAuthStatusEnum）
     */
    @DbColumn(value = "auth_status")
    private Integer authStatus;

    /**
     * 广告账号使用的货币类型
     */
    @DbColumn(value = "currency")
    private String currency;

    /**
     * 广告账号与 GMT 偏移的时区信息，或广告账号所在时区在时区数据库中的名字，格式为“地域/城市”
     */
    @DbColumn(value = "timezone")
    private String timezone;

    /**
     * 广告账号所在时区在时区数据库中的名字
     */
    @DbColumn(value = "display_timezone")
    private String displayTimezone;
}
