package com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request;

import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.Identity;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class GmvMaxVideoGetParam {

    @NotBlank(message = "advertiserId不能为空")
    private String advertiserId;
    @NotNull(message = "shopId不能为空")
    private Integer shopId;
    @NotBlank(message = "bcId不能为空")
    private String storeAuthorizedBcId;
    private String keyword;
    @Size(max = 50, message = "最多支持查询50个spu")
    private List<String> spuIdList;
    private Boolean customPostsEligible;
    private String orderField;
    private String orderValue;
    private Integer pageNo;
    private Integer pageSize;

    private Boolean needAuthCodeVideo;
    private List<Identity> identityList;

}
