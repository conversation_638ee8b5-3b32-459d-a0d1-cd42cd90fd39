package com.meiyunji.sponsored.service.budgetUsage.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormat;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormatType;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class BudgetAnalysisExportDto {

    /**
     * 名称
     */
    @ExcelProperty(value = "广告活动")
    private String name;

    /**
     * 状态名称
     */
    @ExcelProperty(value = "有效")
    private String stateName;

    /**
     * 店铺名称
     */
    @ExcelProperty(value = "店铺")
    private String shopName;

    /**
     * 站点名称
     */
    @ExcelProperty(value = "站点")
    private String siteName;

    /**
     * 广告组合名称
     */
    @ExcelProperty(value = "广告组合")
    private String portfolioName;

    @ExcelProperty(value = "服务状态")
    private String servingStatusName;
    /**
     * 活动类型
     */
    @ExcelProperty(value = "广告类型")
    private String type;

    /**
     * 日期
     */
    @ExcelProperty(value = "日期")
    private String date;

    /**
     * 当天的活动预算
     */
    @ExcelProperty(value = "当天预算")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String budget;

    @ExcelProperty(value = "每日预算")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String dailyBudget;
    /**
     * 预算剩余
     */
    @ExcelProperty(value = "预算剩余")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String budgetRemaining;
    /**
     * 建议预算
     */
    @ExcelProperty(value = "建议预算")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String suggestedBudget;
    /**
     * 百分比 用于导出
     */
    @ExcelProperty(value = "平均预算内活跃时间")
    private String suggestedBudgetIncreasePercent;

    /**
     * 预算内活跃时长
     */
    @ExcelProperty(value = "预算内活跃时长")
    private String budgetTime;
    /**
     * 调整次数
     */
    @ExcelProperty(value = "预算调整次数")
    private Integer budgetAdjustmentNum;
    /**
     * 预算调整时间
     */
    @ExcelProperty(value = "预算调整时间点")
    private String budgetAdjustmentTime;

    @ExcelProperty(value = "广告曝光量")
    private Integer impressions;


    @ExcelProperty(value = "广告点击量")
    private Integer clicks;

    @ExcelProperty(value = "广告订单量")
    private Integer adOrderNum;

    @ExcelProperty(value = "广告点击率")
    private String ctr;

    @ExcelProperty(value = "广告转化率")
    private String cvr;
    @ExcelProperty(value = "广告销量")
    private Integer orderNum;
    @ExcelProperty(value = "广告花费")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adCost;

    @ExcelProperty(value = "广告销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adSale;
    @ExcelProperty(value = "ACoS")
    private String acos;
    @ExcelProperty(value = "ROAS")
    private String roas;
    @ExcelProperty(value = "CPC")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adCostPerClick;
    /**
     * 已花费
     */
    @ExcelProperty(value = "已花费")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String spent;
    /**
     * 估算点击量
     */
    @ExcelProperty(value = "估算点击量")
    private Integer estimateClicks;
    /**
     * cpa
     */
    @ExcelProperty(value = "CPA")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String cpa;
    /**
     * 广告笔单价
     */
    @ExcelProperty(value = "广告笔单价")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String advertisingUnitPrice;

    public static BudgetAnalysisExportDto setAll(BudgetAnalysisPageDto budgetAnalysisPageDto) {
        BudgetAnalysisExportDto budgetAnalysisExportDto = new BudgetAnalysisExportDto();
        String currency = AmznEndpoint.getByMarketplaceId(budgetAnalysisPageDto.getMarketplaceId()).getCurrencyCode().value();
        budgetAnalysisExportDto.setServingStatusName(budgetAnalysisPageDto.getServingStatusName());
        budgetAnalysisExportDto.setName(budgetAnalysisPageDto.getName());
        budgetAnalysisExportDto.setStateName(budgetAnalysisPageDto.getStateName());
        budgetAnalysisExportDto.setShopName(budgetAnalysisPageDto.getShopName());
        budgetAnalysisExportDto.setSiteName(budgetAnalysisPageDto.getSiteName());
        budgetAnalysisExportDto.setPortfolioName(budgetAnalysisPageDto.getPortfolioName());
        budgetAnalysisExportDto.setType(budgetAnalysisPageDto.getType());
        budgetAnalysisExportDto.setDate(budgetAnalysisPageDto.getDate());
        budgetAnalysisExportDto.setBudget(budgetAnalysisPageDto.getBudget() != null ?  currency + budgetAnalysisPageDto.getBudget().toString() : currency + "0.00");
        budgetAnalysisExportDto.setDailyBudget(budgetAnalysisPageDto.getDailyBudget() != null ? currency + budgetAnalysisPageDto.getDailyBudget().toString() : currency + "0.00");
        budgetAnalysisExportDto.setBudgetRemaining(budgetAnalysisPageDto.getBudgetRemaining() != null ? currency + budgetAnalysisPageDto.getBudgetRemaining().toString() : currency + "0.00");
        budgetAnalysisExportDto.setSuggestedBudget(budgetAnalysisPageDto.getSuggestedBudget() != null ? currency + budgetAnalysisPageDto.getSuggestedBudget().toString() : null);
        budgetAnalysisExportDto.setSuggestedBudgetIncreasePercent(StringUtils.isNotBlank(budgetAnalysisPageDto.getSuggestedBudgetIncreasePercent()) ? budgetAnalysisPageDto.getSuggestedBudgetIncreasePercent() + "%" : null);
        budgetAnalysisExportDto.setBudgetTime(budgetAnalysisPageDto.getBudgetTime());
        budgetAnalysisExportDto.setBudgetAdjustmentNum(budgetAnalysisPageDto.getBudgetAdjustmentNum());
        budgetAnalysisExportDto.setBudgetAdjustmentTime(budgetAnalysisPageDto.getBudgetAdjustmentTime());
        budgetAnalysisExportDto.setImpressions(budgetAnalysisPageDto.getImpressions());
        budgetAnalysisExportDto.setClicks(budgetAnalysisPageDto.getClicks());
        budgetAnalysisExportDto.setAdOrderNum(budgetAnalysisPageDto.getAdOrderNum());
        budgetAnalysisExportDto.setCtr(budgetAnalysisPageDto.getCtr() != null ? budgetAnalysisPageDto.getCtr().toString() + "%" : "0.00%");
        budgetAnalysisExportDto.setCvr(budgetAnalysisPageDto.getCvr() != null ? budgetAnalysisPageDto.getCvr().toString() + "%" : "0.00%");
        budgetAnalysisExportDto.setOrderNum(budgetAnalysisPageDto.getOrderNum());
        budgetAnalysisExportDto.setAdCost(budgetAnalysisPageDto.getAdCost() != null ? currency + budgetAnalysisPageDto.getAdCost().toString() : null);
        budgetAnalysisExportDto.setAdSale(budgetAnalysisPageDto.getAdSale() != null ? currency + budgetAnalysisPageDto.getAdSale().toString() : null);
        budgetAnalysisExportDto.setAcos(budgetAnalysisPageDto.getAcos() != null ? budgetAnalysisPageDto.getAcos().toString() + "%" : "0.00%");
        budgetAnalysisExportDto.setRoas(budgetAnalysisPageDto.getRoas() != null ? budgetAnalysisPageDto.getRoas().toString() : "0.00");
        budgetAnalysisExportDto.setAdCostPerClick(budgetAnalysisPageDto.getAdCostPerClick() != null ? currency + budgetAnalysisPageDto.getAdCostPerClick().toString() : null);
        budgetAnalysisExportDto.setEstimateClicks(budgetAnalysisPageDto.getEstimateClicks());
        budgetAnalysisExportDto.setSpent(budgetAnalysisPageDto.getSpent() != null ? currency + budgetAnalysisPageDto.getSpent().toString() : null);
        budgetAnalysisExportDto.setCpa(budgetAnalysisPageDto.getCpa() != null ? currency + budgetAnalysisPageDto.getCpa().toString() : null);
        budgetAnalysisExportDto.setAdvertisingUnitPrice(budgetAnalysisPageDto.getAdvertisingUnitPrice() != null ? currency + budgetAnalysisPageDto.getAdvertisingUnitPrice().toString() : null);
        return budgetAnalysisExportDto;
    }
}
