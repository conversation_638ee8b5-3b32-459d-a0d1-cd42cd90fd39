package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.adProductRight.vo.ShopSiteVo;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonIAdProductPermissionDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProductPermission;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.InitOneAsinVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public class AmazonIAdProductPermissionDaoImpl extends BaseShardingDaoImpl<AmazonAdProductPermission> implements IAmazonIAdProductPermissionDao {

    @Override
    public List<ShopSiteVo> getAllShopSiteByPuid(int puid) {
        StringBuilder selectSql = new StringBuilder("select shop_id shopId, marketplace_id marketplaceId from ").append(getJdbcHelper().getTable());
        List<Object> argsList = new ArrayList<>();
        selectSql.append(" where puid = ? ");
        argsList.add(puid);
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(ShopSiteVo.class));
    }

    @Override
    public List<Integer> getAllUser(int puid, List<Integer> shopIdList, List<String> marketplaceIdList) {
        StringBuilder selectSql = new StringBuilder("select uid from ").append(getJdbcHelper().getTable());
        List<Object> argsList = new ArrayList<>();
        selectSql.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            selectSql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            selectSql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIdList, argsList));
        }
        return getJdbcTemplate(puid).queryForList(selectSql.toString(), argsList.toArray(), Integer.class);
    }

    @Override
    public Integer delete(int puid, Integer shopId, int uid) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder();
        selectSql.append("delete from ").append(getJdbcHelper().getTable());
        selectSql.append(" where puid = ? and shop_id = ? and uid = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(uid);
        return getJdbcTemplate(puid).update(selectSql.toString(), argsList.toArray());
    }

    @Override
    public int addRight(List<AmazonAdProductPermission> partitions) {
        if (CollectionUtils.isEmpty(partitions)) {
            return 0;
        }
        int puid = partitions.get(0).getPuid();
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" (`puid`, `shop_id`, `marketplace_id`, `uid`, `asin`) values ");
        List<Object> argsList = new ArrayList<>(partitions.size());
        for (AmazonAdProductPermission partition : partitions) {
            sql.append(" (?, ?, ?, ?, ?),");
            argsList.add(puid);
            argsList.add(partition.getShopId());
            argsList.add(partition.getMarketplaceId());
            argsList.add(partition.getUid());
            argsList.add(partition.getAsin());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" ON DUPLICATE KEY UPDATE\n" +
                "    `update_time`=now()");
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public int deleteByAsin(int puid, Integer shopId, Integer uid, List<String> removeAsins) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder();
        selectSql.append("delete from ").append(getJdbcHelper().getTable());
        selectSql.append(" where puid = ? and shop_id = ? and uid = ?");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(uid);
        selectSql.append(SqlStringUtil.dealInList("asin", removeAsins, argsList));
        return getJdbcTemplate(puid).update(selectSql.toString(), argsList.toArray());
    }

    @Override
    public int deleteByUid(int puid, Integer shopId, Integer uid) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder();
        selectSql.append("delete from ").append(getJdbcHelper().getTable());
        selectSql.append(" where puid = ? and shop_id = ? and uid = ?");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(uid);
        return getJdbcTemplate(puid).update(selectSql.toString(), argsList.toArray());
    }

    @Override
    public List<InitOneAsinVo> getRandomOne(Integer puid, Integer shopId, int uid) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder();
        selectSql.append("select shop_id shopId, marketplace_id marketplaceId, asin from ").append(getJdbcHelper().getTable());
        selectSql.append(" where puid = ? and shop_id = ? and uid = ? limit 1");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(uid);
        return getJdbcTemplate(puid).query(selectSql.toString(), new BeanPropertyRowMapper<>(InitOneAsinVo.class), argsList.toArray());
    }
}
