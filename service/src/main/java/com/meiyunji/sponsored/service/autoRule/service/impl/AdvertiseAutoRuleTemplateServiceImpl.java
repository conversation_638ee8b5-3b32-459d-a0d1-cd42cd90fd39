package com.meiyunji.sponsored.service.autoRule.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.enums.AmazonAd;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.service.IUserService;
import com.meiyunji.sponsored.service.autoRule.dao.*;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleEnableStatusEnum;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleExecuteTimeSpaceUnitEnum;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleTypeEnum;
import com.meiyunji.sponsored.service.autoRule.po.AdAutoRuleTemplateDisabled;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleTemplate;
import com.meiyunji.sponsored.service.autoRule.service.AdvertiseAutoRuleTemplateService;
import com.meiyunji.sponsored.service.autoRule.util.ReportTriggerAutoRuleHelper;
import com.meiyunji.sponsored.service.autoRule.vo.AdvertiseAutoRuleTemplateRequest;
import com.meiyunji.sponsored.service.autoRule.vo.QueryItemCountVo;
import com.meiyunji.sponsored.service.autoRule.vo.QueryOptimizedTimesVo;
import com.meiyunji.sponsored.service.autoRule.vo.*;
import com.meiyunji.sponsored.service.autoRuleTask.dao.AutoRuleTaskDao;
import com.meiyunji.sponsored.service.autoRuleTask.po.AutoRuleTask;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.enums.AutoRuleItemTypeEnum;
import com.meiyunji.sponsored.service.grabRankings.service.GrabRankingsPostcodeService;
import com.meiyunji.sponsored.service.grabRankings.vo.PostcodeResult;
import com.meiyunji.sponsored.service.log.enums.OperationLogActionEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogTargetEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.po.OperationContent;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTemplateSequenceDao;
import com.meiyunji.sponsored.service.vo.DisabledTemplateDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.pulsar.shade.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AdvertiseAutoRuleTemplateServiceImpl implements AdvertiseAutoRuleTemplateService {

    @Autowired
    private IAdvertiseAutoRuleTemplateDao advertiseAutoRuleTemplateDao;
    @Autowired
    private AdvertiseStrategyTemplateSequenceDao advertiseStrategyTemplateSequenceDao;
    @Autowired
    private IUserService userService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;
    @Autowired
    private IAdvertiseAutoRuleExecuteRecordDao advertiseAutoRuleExecuteRecordDao;
    @Autowired
    private AdvertiseAutoRuleTemplatePuidDao advertiseAutoRuleTemplatePuidDao;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private AutoRuleTaskDao autoRuleTaskDao;

    @Autowired
    private ReportTriggerAutoRuleHelper reportTriggerAutoruleHelper;
    @Autowired
    private GrabRankingsPostcodeService grabRankingsPostcodeService;

    @Resource
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Autowired
    private IAdAutoRuleTemplateDisabledDao adAutoRuleTemplateDisabledDao;

    @Autowired
    private RedisTemplate<String, Object> redisTemplateNew;

    @Override
    public Page<AdvertiseAutoRuleTemplate> pageList(Integer puid,AdvertiseAutoRuleTemplateRequest request) {
        // 1.设置需要过滤的模板id
        setFilterTemplateId(puid, request);
        // 2.分页获取模板数据
        Page<AdvertiseAutoRuleTemplate> page = advertiseAutoRuleTemplateDao.pageList(puid,request);
        if (CollectionUtils.isEmpty(page.getRows())) {
            return new Page<>(request.getPageNo(), request.getPageSize());
        }
        // 3.组装响应数据
        return buildPage(puid, request, page);
    }

    /**
     * 设置需要过滤的模板id
     */
    private void setFilterTemplateId(Integer puid, AdvertiseAutoRuleTemplateRequest request) {
        Set<Long> filterTemplateIdList = new HashSet<>();
        // 1.根据受控对象id获取存在的模版id过滤
        if(StringUtil.isNotEmpty(request.getItemId()) && CollectionUtils.isNotEmpty(request.getItemType())){
            List<Long> templateIdList = advertiseAutoRuleStatusDao.selectTemplateIdByItemIdItemType(puid, request.getShopIdList(), request.getItemId(), request.getItemType());
            if(CollectionUtils.isNotEmpty(templateIdList)){
                filterTemplateIdList.addAll(templateIdList);
            }
        }
        // 2.过滤组受控的模板id
        if(request.getFilterGroup() != null && request.getFilterGroup() && CollectionUtils.isNotEmpty(request.getItemType()) &&
                (request.getItemType().contains(AutoRuleItemTypeEnum.TARGET.getName()) || request.getItemType().contains(AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName()))){
            List<Long> templateIdList = advertiseAutoRuleStatusDao.getGroupTemplateId(puid, request.getShopIdList(), request.getItemType());
            if(CollectionUtils.isNotEmpty(templateIdList)){
                filterTemplateIdList.addAll(templateIdList);
            }
        }
        if(CollectionUtils.isNotEmpty(filterTemplateIdList)){
            request.setFilterTemplateIdList(CollectionUtil.newArrayList(filterTemplateIdList));
        }
    }

    /**
     * 组装响应数据
     */
    private Page<AdvertiseAutoRuleTemplate> buildPage(Integer puid, AdvertiseAutoRuleTemplateRequest request, Page<AdvertiseAutoRuleTemplate> page) {
        List<Long> templateIds = page.getRows().stream().map(AdvertiseAutoRuleTemplate::getId).collect(Collectors.toList());
        // 获取已优化次数
        List<QueryOptimizedTimesVo> queryOptimizedTimesVos = advertiseAutoRuleExecuteRecordDao.queryOptimizedTimes(puid,templateIds);
        Map<Long,QueryOptimizedTimesVo> optimizedTimesMap = queryOptimizedTimesVos.stream().filter(Objects::nonNull).collect(Collectors.toMap(QueryOptimizedTimesVo::getId,s -> s));
        // 获取受控对象个数
        List<QueryItemCountVo> itemCountVos = advertiseAutoRuleStatusDao.queryItemCount(puid,templateIds);
        Map<Long,QueryItemCountVo> itemCountMap = itemCountVos.stream().filter(Objects::nonNull).collect(Collectors.toMap(QueryItemCountVo::getTemplateId,s -> s));
        // 获取自动化规则任务状态
        List<AutoRuleTask> taskList = autoRuleTaskDao.queryListByTemplateIdList(puid, request.getShopIdList(), templateIds);
        Map<Long, List<AutoRuleTask>> taskMap = taskList.stream().collect(Collectors.groupingBy(AutoRuleTask::getOldTemplateId));
        for (AdvertiseAutoRuleTemplate e : page.getRows()) {
            if (MapUtils.isNotEmpty(optimizedTimesMap) && optimizedTimesMap.containsKey(e.getId())) {
                // 已优化次数
                e.setOptimizedTimes(optimizedTimesMap.get(e.getId()).getQueryOptimizedTimes());
            }
            if (MapUtils.isNotEmpty(itemCountMap) && itemCountMap.containsKey(e.getId())) {
                // 受控对象个数
                e.setItemCount(itemCountMap.get(e.getId()).getItemCount());
            }
            if (MapUtils.isNotEmpty(taskMap) && taskMap.containsKey(e.getId())) {
                // 任务执行状态
                List<AutoRuleTask> tasks = taskMap.get(e.getId());
                for (AutoRuleTask autoRuleTask : tasks) {
                    if (autoRuleTask.getState().equals(0)) {
                        e.setState(0);
                        break;
                    }
                }
            }
        }
        return page;
    }

    @Override
    public Result<AddTemplateVo> insertTemplate(Integer puid, AdvertiseAutoRuleTemplate template, String traceId) {
        Result<AddTemplateVo> result = new Result<>();
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(template.getPuid(),template.getShopId());
        if (amazonAdProfile == null) {
            return ResultUtil.error("没有配置信息");
        } else {
            if (amazonAdProfile.getProfileId() == null) {
                return ResultUtil.error("没有配置信息id");
            }
        }
        if (verifyUserCreateTemplate(puid)) {
            return ResultUtil.error("免费套餐最多使用1条自动化规则模板，使用更多请升级套餐");
        }

        if (StringUtils.isBlank(template.getTemplateName())) {
            return ResultUtil.error("模板名称为空");
        }

        if (template.getTemplateName().trim().length() > 100) {
            return ResultUtil.error("模板名称不能超过100个字符");
        }

        if (advertiseAutoRuleTemplateDao.existByName(puid, template.getShopId(),template.getTemplateName().trim(),null)) {
            return ResultUtil.error("模板名称已存在");
        }
        AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
        List<AdManageOperationLog> list = Lists.newArrayList();
        try {
            AddTemplateVo addTemplateVo = new AddTemplateVo();
            Long id = advertiseStrategyTemplateSequenceDao.genId();
            template.setId(id);
            template.setProfileId(amazonAdProfile.getProfileId());
            advertiseAutoRuleTemplateDao.insertTemplate(puid,template);
            advertiseAutoRuleTemplatePuidDao.insertTemplatePuid(puid,id);
            result.setCode(Result.SUCCESS);
            addTemplateVo.setTemplateId(id);
            if (StringUtils.isNotBlank(template.getPerformOperation()) && "customRule".equals(template.getRuleType())) {
                PerformOperationJson performOperationJson = JSONUtil.jsonToArray(template.getPerformOperation(), PerformOperationJson.class).get(0);
                addTemplateVo.setOperationType(AmazonAd.AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction()));
            }
            result.setData(addTemplateVo);
            adManageOperationLog.setResult(0);
        } catch (Exception e) {
            log.error("traceId:{} puid:{} shopId:{} 模板插入异常:", traceId, puid, template.getShopId(), e);
            result.setCode(Result.ERROR);
            result.setMsg(e.getMessage());
            adManageOperationLog.setResult(1);
            adManageOperationLog.setResultInfo(result.getMsg());
        }

        if ("keywordCard".equals(template.getRuleType())) {
            adManageOperationLog.setAction(OperationLogActionEnum.ADD.getOperationValue());
            // 日志信息拼接
            getOperationContent(adManageOperationLog, template, template.getItemType());
            filterAdManageLog(puid, template.getCreateUid(), null, template, adManageOperationLog, list, null);
        }
        return result;
    }

    @Override
    public Result<Long> updateTemplate(Integer puid, AdvertiseAutoRuleTemplate template, String traceId) {
        Result<Long> result = new Result<>();

        // 增加日志操作记录(编辑模板)
        AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
        List<AdManageOperationLog> list = new ArrayList<>();

        try {
            if (template.getTemplateName().trim().length() > 100) {
                return ResultUtil.error("模板名称不能超过100个字符");
            }
            if (advertiseAutoRuleTemplateDao.existByName(puid, template.getShopId(),template.getTemplateName().trim(),template.getId())) {
                return ResultUtil.error("模板名称已存在");
            }
            //版本号处理
            AdvertiseAutoRuleTemplate oldTemplate = advertiseAutoRuleTemplateDao.selectByPrimaryKey(puid, template.getId());
            if (oldTemplate == null) {
                return ResultUtil.error("当前模板已删除");
            }

            if (AutoRuleTypeEnum.keywordCard.getValue().equals(oldTemplate.getRuleType())) {
                template.setExecuteTimeSpaceValue(null);
                template.setExecuteTimeSpaceUnit(null);
                //如果是抢排名进行邮编可用校验
                if (Boolean.TRUE.equals(dynamicRefreshConfiguration.getKeywordCardCheckPostcode())) {
                    List<String> postCodes = StringUtil.splitStr(template.getPostalCodeSettings());
                    Map<String, PostcodeResult> stringPostcodeResultMap = grabRankingsPostcodeService.queryPostcode(oldTemplate.getMarketplaceId(), postCodes);
                    List<String> errorCode = postCodes.stream().filter(e -> !stringPostcodeResultMap.containsKey(e)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(errorCode)) {
                        return  ResultUtil.error("邮编不合法：" + StringUtil.joinString(errorCode));
                    }
                }
            }

            if (org.apache.commons.lang3.StringUtils.equalsAny(oldTemplate.getRuleType(),
                    AutoRuleTypeEnum.campaignOverBudgetRaiseBudget.getValue(),
                    AutoRuleTypeEnum.campaignAcosRaiseBudget.getValue(),
                    AutoRuleTypeEnum.searchQueryAutoUpdateBidding.getValue(),
                    AutoRuleTypeEnum.keywordAcosLowerPrice.getValue(),
                    AutoRuleTypeEnum.keywordAcosRaisePrice.getValue())) {

                if (!reportTriggerAutoruleHelper.isGray(puid)) {
                    template.setExecuteTimeSpaceValue(null);
                    template.setExecuteTimeSpaceUnit(null);
                } else {
                    if (StringUtils.isBlank(template.getExecuteTimeSpaceValue()) || StringUtils.isBlank(template.getExecuteTimeSpaceUnit())) {
                        template.setExecuteTimeSpaceValue("12");
                        template.setExecuteTimeSpaceUnit(AutoRuleExecuteTimeSpaceUnitEnum.HOUR.getValue());
                    }
                }
            }

            template.setMarketplaceId(oldTemplate.getMarketplaceId());
            template.setShopId(oldTemplate.getShopId());
            template.setItemType(oldTemplate.getItemType());
            advertiseAutoRuleTemplateDao.updateTemplate(puid,template);
            advertiseAutoRuleTemplatePuidDao.insertTemplatePuid(puid,template.getId());
            result.setCode(Result.SUCCESS);
            result.setData(template.getId());
            result.setMsg("修改成功");

            adManageOperationLog.setResult(0);
        } catch (Exception e) {
            log.error("traceId:{} puid={} id={},模板修改异常:", traceId, puid, template.getId(), e);
            result.setCode(Result.ERROR);
            result.setMsg(e.getMessage());
            adManageOperationLog.setResult(1);
            adManageOperationLog.setResultInfo(result.getMsg());
        }

        if ("KEYWORD_TARGET".equals(template.getItemType())) {
            // 日志信息拼接
            adManageOperationLog.setAction(OperationLogActionEnum.EDIT.getOperationValue());
            getOperationContent(adManageOperationLog, template, template.getItemType());
            filterAdManageLog(puid, template.getUpdateUid(), null, template, adManageOperationLog, list, null);
        }
        return result;
    }

    @Override
    public Result<String> deleteTemplate(Integer puid, Long id, String traceId) {
        Result<String> result = new Result<>();

        try {
            if (advertiseAutoRuleStatusDao.queryItemCount(puid,id) > 0) {
                return ResultUtil.error("当前模板存在受控对象不能删除");
            }
            // 删除模板
            advertiseAutoRuleTemplateDao.deleteTemplateId(puid, id);
            advertiseAutoRuleTemplatePuidDao.deleteTemplatePuid(puid,id);
            result.setCode(Result.SUCCESS);
            result.setMsg("删除成功");
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("当前模板删除异常");
            log.error("traceId:{} templateId:{} 查询当前模板数据异常:", traceId, id, e);
        }
        return result;
    }

    public boolean verifyUserCreateTemplate (Integer puid) {
        // 是否免费用户
        boolean isFree = userService.isFree(puid);   // 付费用户不做限制
        if (!isFree) {
            return false;
        }

        return advertiseAutoRuleTemplateDao.verifyTemplateCount(puid);
    }

    @Override
    public AdvertiseAutoRuleTemplate getTemplate(Integer puid, Long id, String traceId) {
        return advertiseAutoRuleTemplateDao.selectByPrimaryKey(puid,id);
    }

    @Override
    public void updateReportCompletePush(Map<Integer, List<Integer[]>> puidShopIdMap) {
        puidShopIdMap.forEach((k, v) -> {
            if (Objects.nonNull(k) && CollectionUtils.isNotEmpty(v)) {
                advertiseAutoRuleTemplateDao.updateReportCompletePush(k, v);
            }
        });
    }

    @Override
    public void updateTemplateStatus(int puid, List<AdvertiseAutoRuleTemplate> templateList, String status, Long updateUid) {

        List<Long> templateIdList = new ArrayList<>(templateList.size());
        List<AdAutoRuleTemplateDisabled> disabledList = new ArrayList<>(templateList.size());
        Date date = new Date();
        templateList.forEach(x -> {
            templateIdList.add(x.getId());
            if (AutoRuleEnableStatusEnum.DISABLED.getCode().equals(status)) {
                disabledList.add(new AdAutoRuleTemplateDisabled(puid, x.getShopId(), x.getId(), date, date));
            }
        });

        //更新模板状态
        advertiseAutoRuleTemplateDao.updateAutoRuleTemplateStatus(puid, templateIdList, status, updateUid);
        log.info("自动化规则更新模板状态成功, puid: {}, templateIdList: {}, status: {}, uid: {}", puid, templateIdList, status, updateUid);

        //写入广告主库并刷新缓存
        String key = String.format(RedisConstant.AUTO_RULE_DISABLED_TEMPLATE_KEY, puid);
        //禁用
        if (AutoRuleEnableStatusEnum.DISABLED.getCode().equals(status)) {
            //插入主库
            adAutoRuleTemplateDisabledDao.insert(disabledList);
        } else {
            //启用
            //删除主库
            adAutoRuleTemplateDisabledDao.deleteByTemplateId(puid, templateIdList);
        }
        //重新使用最新数据刷新缓存
        List<Long> allDisabledList = adAutoRuleTemplateDisabledDao.distinctTemplateId(puid);
        if (CollectionUtils.isEmpty(allDisabledList)) {
            allDisabledList = new ArrayList<>();
            allDisabledList.add(-1L);
        }

        //执行redis脚本
        atomicUpdateSetDisabledTemplate(key, allDisabledList);

        log.info("自动化规则禁用模板，写入广告主库和写入redis成功, puid: {}, templateIdList: {}, status: {}, uid: {}, disabledTemplateIdList: {}", puid, templateIdList, status, updateUid, allDisabledList);
    }

    private void atomicUpdateSetDisabledTemplate(String key, List<Long> allDisabledList) {
        // Lua 脚本：先删除 key，再添加元素，最后设置过期时间（原子操作）
        String script = "redis.call('DEL', KEYS[1]) " +
                        "if #ARGV > 0 then " +
                        "    redis.call('SADD', KEYS[1], unpack(ARGV)) " +
                        "end " +
                        "redis.call('EXPIRE', KEYS[1], 1800) " +  // 30 分钟过期时间
                        "return 1";

        // 执行 Lua 脚本
        redisTemplateNew.execute(new DefaultRedisScript<>(script, Long.class), Collections.singletonList(key), allDisabledList.toArray());
    }

    @Override
    public void disableAllTemplateStatus(Integer puid, Integer shopId) {
        //已开启的模板
        log.info("自动化规则自动更新模板状态, puid: {}, shopId: {}", puid, shopId);
        List<AdvertiseAutoRuleTemplate> disabledList = advertiseAutoRuleTemplateDao.getEnabledTemplate(puid, shopId);

        if (CollectionUtils.isEmpty(disabledList)) {
            return;
        }

        updateTemplateStatus(puid, disabledList, AutoRuleEnableStatusEnum.DISABLED.getCode(), null);
    }

    @Override
    public void retryDisableTemplate(Integer puid, List<DisabledTemplateDto> templateList) {
        if (CollectionUtils.isEmpty(templateList)) {
            return;
        }

        List<AdAutoRuleTemplateDisabled> disabledList = new ArrayList<>(templateList.size());
        List<Long> disabledIdList = new ArrayList<>(templateList.size());
        Date date = new Date();
        templateList.forEach(x -> {
            disabledIdList.add(x.getTemplateId());
            disabledList.add(new AdAutoRuleTemplateDisabled(puid, x.getShopId(), x.getTemplateId(), date, date));
        });

        adAutoRuleTemplateDisabledDao.insert(disabledList);

        //删除
        adAutoRuleTemplateDisabledDao.deleteByExcludeTemplateIdList(puid, disabledIdList);
    }


    /**
     * 拼接编辑模板信息
     * @param adManageOperationLog
     * @param newTemplate
     * @param itemType
     */
    private void getOperationContent(AdManageOperationLog adManageOperationLog, AdvertiseAutoRuleTemplate newTemplate, String itemType) {
        List<OperationContent> operationContents = new ArrayList<>();


        if (StringUtils.isNotBlank(newTemplate.getTemplateName())) {
            OperationContent content = new OperationContent();
            content.setName("规则名称");
            content.setNewValue(newTemplate.getTemplateName());
            operationContents.add(content);
        }

        if (StringUtils.isNotBlank(newTemplate.getTimeRule())) {
            OperationContent timeContent = new OperationContent();
            List<TimeRuleJson> newTimeRuleList = JSONUtil.jsonToArray(newTemplate.getTimeRule(),TimeRuleJson.class);
            timeContent.setName("生效时间");
            timeContent.setPreviousValue(newTemplate.getStartDate().format(DateTimeFormatter.ISO_DATE));
            if (newTemplate.getEndDate() != null) {
                timeContent.setNewValue(newTemplate.getEndDate().format(DateTimeFormatter.ISO_DATE));
            } else {
                timeContent.setNewValue("无结束日期");
            }
            operationContents.add(timeContent);
            if (CollectionUtils.isNotEmpty(newTimeRuleList)) {
                newTimeRuleList.forEach(e -> {
                    OperationContent content = new OperationContent();
                    StringBuilder builder = new StringBuilder();
                    content.setName(getDayOfTheWeek(e.getSiteDate()));
                    builder.append(e.getStartTimeSite()).append(":00 ~ ").append(e.getEndTimeSite()).append(":00 ");
                    content.setNewValue(builder.toString());
                    operationContents.add(content);
                });
            }
        }

        if (StringUtils.isNotBlank(newTemplate.getDesiredPosition())) {
            DesiredPositionJson desiredPositionJson = JSONUtil.jsonToObject(newTemplate.getDesiredPosition(),DesiredPositionJson.class);
            if (desiredPositionJson != null) {
                OperationContent content = new OperationContent();
                content.setName("期望位置");
                content.setNewValue("第" + desiredPositionJson.getPage().toString() + "页, " + "第" + desiredPositionJson.getFrom() + "位~第" + desiredPositionJson.getTo() + "位");
                operationContents.add(content);
            }
        }

        if (StringUtils.isNotBlank(newTemplate.getAdDataRule())) {
            List<AdDataRuleJson> adDataRuleJsonList = JSONUtil.jsonToArray(newTemplate.getAdDataRule(),AdDataRuleJson.class);
            if (CollectionUtils.isNotEmpty(adDataRuleJsonList)) {
                int j = 1;
                for (int i = 0; i < adDataRuleJsonList.size(); i++) {
                    AdDataRuleJson adDataRuleJson = adDataRuleJsonList.get(i);
                    OperationContent content = new OperationContent();
                    content.setTitle("广告数据");
                    content.setName("条件" + j);
                    content.setNewValue(getRuleIndexDay(adDataRuleJson.getDay(),
                            adDataRuleJson.getExcludeDay()) + "," +
                            getRuleIndex(adDataRuleJson.getRuleIndex()) + "," +
                            getRuleStatisticalModeType(adDataRuleJson.getRuleStatisticalModeType()) + "," +
                            getRuleOperator(adDataRuleJson.getRuleOperator()) + "," + adDataRuleJson.getRuleValue());
                    operationContents.add(content);
                    j++;
                }
            }
        }

        //自动加价日志记录
        if (StringUtils.isNotBlank(newTemplate.getAdDataOperate())) {
            AdDataOperateJson adDataOperateJson = JSONUtil.jsonToObject(newTemplate.getAdDataOperate(),AdDataOperateJson.class);
            if (adDataOperateJson != null) {
                OperationContent content = new OperationContent();
                if (Objects.nonNull(adDataOperateJson.getBid()) || Objects.nonNull(adDataOperateJson.getPlacementTopBidRatio())) {
                    content.setName("执行操作");
                    StringBuilder newValue = new StringBuilder();
                    if (Objects.nonNull(adDataOperateJson.getBid())) {
                        newValue.append(getRuleAction(adDataOperateJson.getBid().getRuleAction())).append(",")
                                .append(getRuleAdjust(adDataOperateJson.getBid().getRuleAdjust())).append(",")
                                .append(adDataOperateJson.getBid().getAdJustValue()).append(getSymbol(adDataOperateJson.getBid().getRuleAdjust()))
                                .append(",竞价上限").append(adDataOperateJson.getBid().getLimitValue()).append(";");
                    }
                    if (Objects.nonNull(adDataOperateJson.getPlacementTopBidRatio())) {
                        newValue.append(getRuleAction(adDataOperateJson.getPlacementTopBidRatio().getRuleAction())).append(",")
                                .append(getRuleAdjust(adDataOperateJson.getPlacementTopBidRatio().getRuleAdjust())).append(",")
                                .append(adDataOperateJson.getPlacementTopBidRatio().getAdJustValue()).append("%");
                        if (StringUtils.isNotBlank(adDataOperateJson.getPlacementTopBidRatio().getLimitValue())) {
                            newValue.append(",竞价比例上限").append(adDataOperateJson.getPlacementTopBidRatio().getLimitValue()).append("%");
                        }
                    }
                    content.setNewValue(newValue.toString());
                } else {
                    //兼容旧模板
                    content.setName("执行操作");
                    content.setNewValue(getRuleAction(adDataOperateJson.getRuleAction()) + "," +
                            getRuleAdjust(adDataOperateJson.getRuleAdjust()) + "," + adDataOperateJson.getAdJustValue() + getSymbol(adDataOperateJson.getRuleAdjust())+
                            ",竞价上限" + adDataOperateJson.getLimitValue());
                }
                operationContents.add(content);
            }
        }

        if (StringUtils.isNotBlank(newTemplate.getAutoPriceRule())) {
            AutoPriceRuleJson autoPriceRuleJson = JSONUtil.jsonToObject(newTemplate.getAutoPriceRule(),AutoPriceRuleJson.class);
            if (autoPriceRuleJson != null) {
                OperationContent content = new OperationContent();
                content.setName("自动降价");
                content.setNewValue("连续" + autoPriceRuleJson.getContinuousFrequency() + "次,低于第" + autoPriceRuleJson.getPage() + "页" + "," + "第" + autoPriceRuleJson.getRank() + "名");
                operationContents.add(content);
            }
        }

        //自动降价日志记录
        if (StringUtils.isNotBlank(newTemplate.getAutoPriceOperate())) {
            AutoPriceOperateJson autoPriceOperateJson = JSONUtil.jsonToObject(newTemplate.getAutoPriceOperate(),AutoPriceOperateJson.class);
            if (autoPriceOperateJson != null) {
                OperationContent content = new OperationContent();
                if (Objects.nonNull(autoPriceOperateJson.getBid()) || Objects.nonNull(autoPriceOperateJson.getPlacementTopBidRatio())) {
                    content.setName("执行操作");
                    StringBuilder newValue = new StringBuilder();
                    if (Objects.nonNull(autoPriceOperateJson.getBid())) {
                        newValue.append(getRuleAction(autoPriceOperateJson.getBid().getRuleAction())).append(",")
                                .append(getRuleAdjust(autoPriceOperateJson.getBid().getRuleAdjust())).append(",")
                                .append(autoPriceOperateJson.getBid().getAdJustValue()).append(getSymbol(autoPriceOperateJson.getBid().getRuleAdjust()))
                                .append(",竞价下限").append(autoPriceOperateJson.getBid().getLimitValue()).append(";");
                    }
                    if (Objects.nonNull(autoPriceOperateJson.getPlacementTopBidRatio())) {
                        newValue.append(getRuleAction(autoPriceOperateJson.getPlacementTopBidRatio().getRuleAction())).append(",")
                                .append(getRuleAdjust(autoPriceOperateJson.getPlacementTopBidRatio().getRuleAdjust())).append(",")
                                .append(autoPriceOperateJson.getPlacementTopBidRatio().getAdJustValue()).append("%");
                        if (StringUtils.isNotBlank(autoPriceOperateJson.getPlacementTopBidRatio().getLimitValue())) {
                            newValue.append(",竞价比例下限").append(autoPriceOperateJson.getPlacementTopBidRatio().getLimitValue()).append("%");
                        }
                    }
                    content.setNewValue(newValue.toString());
                } else {
                    content.setName("执行操作");
                    content.setNewValue(getRuleAction(autoPriceOperateJson.getRuleAction()) + "," +
                            getRuleAdjust(autoPriceOperateJson.getRuleAdjust()) + "," + autoPriceOperateJson.getAdJustValue() + getSymbol(autoPriceOperateJson.getRuleAdjust())+
                            ",竞价下限" + autoPriceOperateJson.getLimitValue());
                }
                operationContents.add(content);
            }
        }

        //竞价回调日志记录
        if (StringUtils.isNotBlank(newTemplate.getBiddingCallbackOperate())) {
            BiddingCallbackOperateJson biddingCallbackOperateJson = JSONUtil.jsonToObject(newTemplate.getBiddingCallbackOperate(),BiddingCallbackOperateJson.class);
            if (biddingCallbackOperateJson != null) {
                if (Objects.nonNull(biddingCallbackOperateJson.getBid()) || Objects.nonNull(biddingCallbackOperateJson.getPlacementTopBidRatio())) {
                    if (Objects.nonNull(biddingCallbackOperateJson.getBid())) {
                        StringBuilder stringBuilder = new StringBuilder();
                        OperationContent content = new OperationContent();
                        content.setName("竞价回调");
                        stringBuilder.append(getAdjustType(biddingCallbackOperateJson.getBid().getAdjustType()));
                        if (StringUtils.isNotBlank(biddingCallbackOperateJson.getBid().getAdJustValue())) {
                            stringBuilder.append(",");
                            stringBuilder.append(biddingCallbackOperateJson.getBid().getAdJustValue());
                            stringBuilder.append(getSymbols(biddingCallbackOperateJson.getBid().getAdjustType()));
                        }
                        if ("addPercentage".equals(biddingCallbackOperateJson.getBid().getAdjustType())) {
                            stringBuilder.append(",竞价上限");
                            stringBuilder.append(biddingCallbackOperateJson.getBid().getLimitValue());
                        }
                        if ("reducePercentage".equals(biddingCallbackOperateJson.getBid().getAdjustType())) {
                            stringBuilder.append(",竞价下限");
                            stringBuilder.append(biddingCallbackOperateJson.getBid().getLimitValue());
                        }
                        if ("addFixed".equals(biddingCallbackOperateJson.getBid().getAdjustType())) {
                            stringBuilder.append(",竞价上限");
                            stringBuilder.append(biddingCallbackOperateJson.getBid().getLimitValue());
                        }
                        if ("reduceFixed".equals(biddingCallbackOperateJson.getBid().getAdjustType())) {
                            stringBuilder.append(",竞价下限");
                            stringBuilder.append(biddingCallbackOperateJson.getBid().getLimitValue());
                        }
                        content.setNewValue(stringBuilder.toString());
                        operationContents.add(content);
                    }
                    if (Objects.nonNull(biddingCallbackOperateJson.getPlacementTopBidRatio())) {
                        StringBuilder stringBuilder = new StringBuilder();
                        OperationContent content = new OperationContent();
                        content.setName("搜索结果顶部(首页)竞价比例回调");
                        stringBuilder.append(getAdjustType(biddingCallbackOperateJson.getPlacementTopBidRatio().getAdjustType()));
                        if (StringUtils.isNotBlank(biddingCallbackOperateJson.getPlacementTopBidRatio().getAdJustValue())) {
                            stringBuilder.append(",");
                            stringBuilder.append(biddingCallbackOperateJson.getPlacementTopBidRatio().getAdJustValue());
                            stringBuilder.append("%");
                        }
                        if ("addFixedValue".equals(biddingCallbackOperateJson.getPlacementTopBidRatio().getAdjustType())) {
                            stringBuilder.append(",竞价比例上限");
                            stringBuilder.append(biddingCallbackOperateJson.getPlacementTopBidRatio().getLimitValue());
                            stringBuilder.append("%");
                        }
                        if ("reduceFixedValue".equals(biddingCallbackOperateJson.getPlacementTopBidRatio().getAdjustType())) {
                            stringBuilder.append(",竞价比例下限");
                            stringBuilder.append(biddingCallbackOperateJson.getPlacementTopBidRatio().getLimitValue());
                            stringBuilder.append("%");
                        }
                        if ("addFixed".equals(biddingCallbackOperateJson.getPlacementTopBidRatio().getAdjustType())) {
                            stringBuilder.append(",竞价比例上限");
                            stringBuilder.append(biddingCallbackOperateJson.getPlacementTopBidRatio().getLimitValue());
                            stringBuilder.append("%");
                        }
                        if ("reduceFixed".equals(biddingCallbackOperateJson.getPlacementTopBidRatio().getAdjustType())) {
                            stringBuilder.append(",竞价比例下限");
                            stringBuilder.append(biddingCallbackOperateJson.getPlacementTopBidRatio().getLimitValue());
                            stringBuilder.append("%");
                        }
                        content.setNewValue(stringBuilder.toString());
                        operationContents.add(content);
                    }
                } else {
                    StringBuilder stringBuilder = new StringBuilder();
                    OperationContent content = new OperationContent();
                    content.setName("竞价回调");
                    stringBuilder.append(getAdjustType(biddingCallbackOperateJson.getAdjustType()));
                    if (StringUtils.isNotBlank(biddingCallbackOperateJson.getAdJustValue())) {
                        stringBuilder.append(",");
                        stringBuilder.append(biddingCallbackOperateJson.getAdJustValue());
                        stringBuilder.append(getSymbols(biddingCallbackOperateJson.getAdjustType()));
                    }
                    if ("addPercentage".equals(biddingCallbackOperateJson.getAdjustType())) {
                        stringBuilder.append(",竞价上限");
                        stringBuilder.append(biddingCallbackOperateJson.getLimitValue());
                    }
                    if ("reducePercentage".equals(biddingCallbackOperateJson.getAdjustType())) {
                        stringBuilder.append(",竞价下限");
                        stringBuilder.append(biddingCallbackOperateJson.getLimitValue());
                    }
                    if ("addFixed".equals(biddingCallbackOperateJson.getAdjustType())) {
                        stringBuilder.append(",竞价上限");
                        stringBuilder.append(biddingCallbackOperateJson.getLimitValue());
                    }
                    if ("reduceFixed".equals(biddingCallbackOperateJson.getAdjustType())) {
                        stringBuilder.append(",竞价下限");
                        stringBuilder.append(biddingCallbackOperateJson.getLimitValue());
                    }
                    content.setNewValue(stringBuilder.toString());
                    operationContents.add(content);
                }
            }
        }

        if (StringUtils.isNotBlank(newTemplate.getCheckFrequency())) {
            OperationContent content = new OperationContent();
            content.setName("检查频率");
            content.setNewValue(newTemplate.getCheckFrequency()+"小时/次");
            operationContents.add(content);
        }

        if (StringUtils.isNotBlank(newTemplate.getPushMessageType())) {
            StringBuilder builder = new StringBuilder();
            OperationContent content = new OperationContent();
            content.setName("消息提醒");
            if ("wx".equals(newTemplate.getPushMessageType())) {
                builder.append("排名高于预期时通知，");
                builder.append("微信通知");
            } else if ("instation".equals(newTemplate.getPushMessageType())) {
                builder.append("排名高于预期时通知，");
                builder.append("站内通知");
            } else if ("all".equals(newTemplate.getPushMessageType())) {
                builder.append("排名高于预期时通知，");
                builder.append("站内微信通知");
            } else {
                builder.append("不通知");
            }
            content.setNewValue(builder.toString());
            operationContents.add(content);
        }

        if (StringUtils.isNotBlank(newTemplate.getPostalCodeSettings())) {
            OperationContent content = new OperationContent();
            content.setName("邮编设置");
            content.setNewValue(newTemplate.getPostalCodeSettings());
            operationContents.add(content);
        }

        adManageOperationLog.setMessage(adManageOperationLog.handleMessage(operationContents));
        String operaContent = JSONUtil.objectToJson(operationContents);
        adManageOperationLog.setOperationContent(operaContent);
    }

    private String getSymbols(String adjustType) {
        StringBuilder index = new StringBuilder();
        if ("addPercentage".equals(adjustType)) {
            index.append("%");
        }
        if ("reducePercentage".equals(adjustType)) {
            index.append("%");
        }
        if ("addFixed".equals(adjustType)) {
            index.append("US$");
        }
        if ("reduceFixed".equals(adjustType)) {
            index.append("US$");
        }
        if ("adjustFixed".equals(adjustType)) {
            index.append("US$");
        }
        if ("originalValue".equals(adjustType)) {
            index.append("");
        }
        return index.toString();
    }

    private String getSymbol(String ruleAction) {
        StringBuilder index = new StringBuilder();
//        if ("fixed".equals(ruleAction)) {
//            index.append("US$");
//        }
        if ("percentage".equals(ruleAction)) {
            index.append("%");
        }
//        if ("inputValue".equals(ruleAction)) {
//            index.append("US$");
//        }
        return index.toString();
    }

    private String getAdjustType (String adjustType) {
        StringBuilder index = new StringBuilder();
        if ("addPercentage".equals(adjustType)) {
            index.append("按百分比提高");
        }
        if ("reducePercentage".equals(adjustType)) {
            index.append("按百分比降低");
        }
        if ("addFixed".equals(adjustType)) {
            index.append("按固定值提高");
        }
        if ("reduceFixed".equals(adjustType)) {
            index.append("按固定值降低");
        }
        if ("adjustFixed".equals(adjustType)) {
            index.append("调整至固定值");
        }
        if ("originalValue".equals(adjustType)) {
            index.append("按原始值回调");
        }
        return index.toString();
    }

    private String getRuleAdjust (String ruleAction) {
        StringBuilder index = new StringBuilder();
        if ("fixed".equals(ruleAction)) {
            index.append("按固定值调整");
        }
        if ("percentage".equals(ruleAction)) {
            index.append("按百分比调整");
        }
        if ("inputValue".equals(ruleAction)) {
            index.append("自定义输入值");
        }
        if ("fixedValue".equals(ruleAction)) {
            index.append("调整至固定值");
        }
        return index.toString();
    }

    private String getRuleAction (String ruleAction) {
        StringBuilder index = new StringBuilder();
        if ("budgetAdd".equals(ruleAction)) {
            index.append("预算增加");
        }
        if ("budgetReduce".equals(ruleAction)) {
            index.append("预算减少");
        }
        if ("bidAdd".equals(ruleAction)) {
            index.append("投放竞价增加");
        }
        if ("bidReduce".equals(ruleAction)) {
            index.append("投放竞价减少");
        }
        if ("placementTopBidRatioAdd".equals(ruleAction)) {
            index.append("提高搜索结果顶部(首页)竞价比例");
        }
        if ("placementTopBidRatioReduce".equals(ruleAction)) {
            index.append("降低搜索结果顶部(首页)竞价比例");
        }
        if ("stateClose".equals(ruleAction)) {
            index.append("状态关闭");
        }
        return index.toString();
    }

    /**
     * 指标天数
     * @param ruleOperator
     * @return
     */
    private String getRuleOperator(String ruleOperator) {
        StringBuilder index = new StringBuilder();
        if ("EQ".equals(ruleOperator)) {
            index.append("等于");
        }
        if ("NQ".equals(ruleOperator)) {
            index.append("不等于");
        }
        if ("GT".equals(ruleOperator)) {
            index.append("大于");
        }
        if ("LT".equals(ruleOperator)) {
            index.append("小于");
        }
        if ("GE".equals(ruleOperator)) {
            index.append("大于等于");
        }
        if ("LE".equals(ruleOperator)) {
            index.append("小于等于");
        }
        return index.toString();
    }

    /**
     * 指标天数
     * @param ruleStatisticalModeType
     * @return
     */
    private String getRuleStatisticalModeType(String ruleStatisticalModeType) {
        StringBuilder index = new StringBuilder();
        if ("total".equals(ruleStatisticalModeType)) {
            index.append("总和");
        }
        if ("avg".equals(ruleStatisticalModeType)) {
            index.append("平均值");
        }
        return index.toString();
    }

    /**
     * 指标
     * @param ruleIndex
     * @return
     */
    private String getRuleIndex(String ruleIndex) {
        StringBuilder index = new StringBuilder();
        if ("adImpressions".equals(ruleIndex)) {
            index.append("广告曝光量");
        }
        if ("clicks".equals(ruleIndex)) {
            index.append("广告点击量");
        }
        if ("cost".equals(ruleIndex)) {
            index.append("广告花费");
        }
        if ("adOrderNum".equals(ruleIndex)) {
            index.append("广告订单量");
        }
        if ("orderNum".equals(ruleIndex)) {
            index.append("广告销量");
        }
        if ("adSale".equals(ruleIndex)) {
            index.append("广告销售额");
        }
        if ("clickRate".equals(ruleIndex)) {
            index.append("广告点击率");
        }
        if ("conversionRate".equals(ruleIndex)) {
            index.append("广告转化率");
        }
        if ("acos".equals(ruleIndex)) {
            index.append("Acos");
        }
        if ("roas".equals(ruleIndex)) {
            index.append("Roas");
        }
        return index.toString();
    }

    /**
     * 指标天数
     * @param excludeDay
     * @return
     */
    private String getRuleIndexDay(int day,int excludeDay) {
        StringBuilder ruleIndexDay = new StringBuilder();
        if (day == 1) {
            ruleIndexDay.append("当天");
        }
        if (day == 3) {
            ruleIndexDay.append("近3天");
        }
        if (day == 5) {
            ruleIndexDay.append("近5天");
        }
        if (day == 7) {
            ruleIndexDay.append("近7天");
        }
        if (day == 14) {
            ruleIndexDay.append("近14天");
        }
        if (day == 30) {
            ruleIndexDay.append("近30天");
        }
        if (excludeDay == 2) {
            ruleIndexDay.append("(不含近2天)");
        }
        return ruleIndexDay.toString();
    }

    /**
     * 转换为星期几文案
     * @param siteDate
     * @return
     */
    private String getDayOfTheWeek(Integer siteDate) {
        if (siteDate == 1) {
            return "每周一";
        }
        if (siteDate == 2) {
            return "每周二";
        }
        if (siteDate == 3) {
            return "每周三";
        }
        if (siteDate == 4) {
            return "每周四";
        }
        if (siteDate == 5) {
            return "每周五";
        }
        if (siteDate == 6) {
            return "每周六";
        }
        if (siteDate == 7) {
            return "每周日";
        }
        if (siteDate == 0) {
            return "每日";
        }
        return "";
    }

    /**
     * 填充日志对象公用方法
     * @param puid
     * @param uid
     * @param loginIp
     * @param template
     * @param adManageOperationLog
     * @param list
     * @param content
     */
    private void filterAdManageLog(Integer puid, Integer uid, String loginIp, AdvertiseAutoRuleTemplate template, AdManageOperationLog adManageOperationLog, List<AdManageOperationLog> list, OperationContent content) {
        if (template == null) {
            return;
        }
        adManageOperationLog.setPuid(puid);
        adManageOperationLog.setUid(uid);
//        adManageOperationLog.setIp(loginIp);
        adManageOperationLog.setTemplateId(template.getId());
        adManageOperationLog.setTargetId(String.valueOf(template.getId()));
        adManageOperationLog.setShopId(template.getShopId());
        if (StringUtils.isNotBlank(template.getMarketplaceId())) {
            adManageOperationLog.setMarketplaceId(template.getMarketplaceId());
        }
        // 存模板id和模板名称关系(便于后续模板删除后查询以往的信息)
        adManageOperationLog.setTarget(template.getTemplateName() + "：" + template.getId());
        adManageOperationLog.setOperationObject(OperationLogTargetEnum.KEYWORD_CARD_TEMPLATE.getTargetValue());
        list.add(adManageOperationLog);
        if (content != null) {
            List<OperationContent> contentList = new ArrayList<>();
            contentList.add(content);
            adManageOperationLog.setMessage(adManageOperationLog.handleMessage(contentList));
        }


        adManageOperationLogService.printAdOtherOperationLog(list);
    }


}
