package com.meiyunji.sponsored.service.doris.po;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * amazon广告组表(OdsAmazonAdGroup)实体类
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:17
 */
@Data
@DbTable("ods_t_amazon_ad_group")
public class OdsAmazonAdGroup  implements Serializable {
	/**
     * 商户uid
     */    
	@DbColumn(value = "puid")
    private Integer puid;

	/**
     * 店铺ID
     */    
	@DbColumn(value = "shop_id")
    private Integer shopId;

	/**
     * 广告组id
     */    
	@DbColumn(value = "ad_group_id")
    private String adGroupId;

	/**
     * md5(puid,shop_id,market_place_id,campaign_id,name)
     */    
	@DbColumn(value = "unique_key")
    private String uniqueKey;

	/**
     * 站点
     */    
	@DbColumn(value = "marketplace_id")
    private String marketplaceId;

	/**
     * 活动id
     */    
	@DbColumn(value = "campaign_id")
    private String campaignId;

	/**
     * 配置ID
     */    
	@DbColumn(value = "profile_id")
    private String profileId;

	/**
     * 广告组名称
     */    
	@DbColumn(value = "name")
    private String name;

	/**
     * 默认竞价
     */    
	@DbColumn(value = "default_bid")
    private Double defaultBid;

	/**
     * 手动:keyword,targeting,自动:auto
     */    
	@DbColumn(value = "ad_group_type")
    private String adGroupType;

	/**
     * 广告组状态（enabled，paused，archived）
     */    
	@DbColumn(value = "state")
    private String state;

	/**
     * 二级状态
     */    
	@DbColumn(value = "serving_status")
    private String servingStatus;

	/**
     * 创建广告组
     */    
	@DbColumn(value = "publish_state")
    private String publishState;

	/**
     * 错误信息
     */    
	@DbColumn(value = "error_msg")
    private String errorMsg;

	/**
     * 创建人id
     */    
	@DbColumn(value = "create_id")
    private Integer createId;

	/**
     * 修改人id
     */    
	@DbColumn(value = "update_id")
    private Integer updateId;

	/**
     * 报告数据最新更新时间 yyyy-MM-dd
     */    
	@DbColumn(value = "data_update_time")
    private LocalDate dataUpdateTime;

	/**
     * 创建时间
     */    
	@DbColumn(value = "create_time")
    private Date createTime;

	/**
     * 更新的时间
     */    
	@DbColumn(value = "update_time")
    private Date updateTime;

	/**
     * 广告组状态是否应用分时调竞价 0,1
     */    
	@DbColumn(value = "is_state_bidding")
    private Integer isStateBidding;

	/**
     * 广告组状态分时调价状态 0,1 关闭，开启
     */    
	@DbColumn(value = "pricing_state_bidding")
    private Integer pricingStateBidding;

}

