package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.common.enums.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by xp on 2021/4/9.
 * 广告位列表页参数
 */
@Data
@ApiModel
public class PlacementPageParam {

    @ApiModelProperty("pageNo")
    private Integer pageNo;

    @ApiModelProperty("pageSize")
    private Integer pageSize;

    private Integer uid;
    private String uuid;
    private String icon;
    private String currency;

    @ApiModelProperty(value = "shopId",required = true)
    private Integer shopId;

    @ApiModelProperty(value = "puid",required = true)
    private Integer puid;

    @ApiModelProperty("店小秘广告活动ID 弃用")
    private Integer dxmCampaignId;

    @ApiModelProperty("广告活动ID")
    private String campaignId;

    @ApiModelProperty(value = "活动id集合")
    private List<String> campaignIds;

    @ApiModelProperty(value = "广告组合下的活动id")
    private List<String> campaignIdList;

    @ApiModelProperty("广告组合ID")
    private String portfolioId;

    @ApiModelProperty("广告组ID")
    private String groupId;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("排序字段")
    private String orderField;

    @ApiModelProperty("排序方式 desc asc")
    private String orderType;

    @ApiModelProperty("位置")
    private String predicate;

    @ApiModelProperty("strategyType")
    private String strategyType;

    //高级搜索
    @ApiModelProperty(value = "是否开启高级筛选")
    private Boolean useAdvanced;

    //高级搜索值
    @ApiModelProperty(value = "高级搜索展示量最小值")
    private Integer impressionsMin;  //展示量
    @ApiModelProperty(value = "高级搜索展示量最大值")
    private Integer impressionsMax;
    @ApiModelProperty(value = "高级搜索点击量小值")
    private Integer clicksMin;  //点击量
    @ApiModelProperty(value = "高级搜索点击量大值")
    private Integer clicksMax;
    @ApiModelProperty(value = "高级搜索点击率小值")
    private BigDecimal clickRateMin;  //点击率
    @ApiModelProperty(value = "高级搜索点击率大值")
    private BigDecimal clickRateMax;
    @ApiModelProperty(value = "高级搜索花费小值")
    private BigDecimal costMin; //花费
    @ApiModelProperty(value = "高级搜索花费大值")
    private BigDecimal costMax;
    @ApiModelProperty(value = "高级搜索cpc小值")
    private BigDecimal cpcMin;  //cpc
    @ApiModelProperty(value = "高级搜索cpc大值")
    private BigDecimal cpcMax;
    @ApiModelProperty(value = "高级搜索广告订单量小值")
    private Integer orderNumMin;  //广告订单量
    @ApiModelProperty(value = "高级搜索广告订单量大值")
    private Integer orderNumMax;
    @ApiModelProperty(value = "高级搜索sales小值")
    private BigDecimal salesMin;  //sales
    @ApiModelProperty(value = "高级搜索sales大值")
    private BigDecimal salesMax;
    @ApiModelProperty(value = "高级搜索acos小值")
    private BigDecimal acosMin;  //acos
    @ApiModelProperty(value = "高级搜索acos大值")
    private BigDecimal acosMax;
    @ApiModelProperty(value = "高级搜索roas小值")
    private BigDecimal roasMin;   //roas
    @ApiModelProperty(value = "高级搜索roas大值")
    private BigDecimal roasMax;
    @ApiModelProperty(value = "高级搜索广告订单转化率小值")
    private BigDecimal salesConversionRateMin;  //订单转化率
    @ApiModelProperty(value = "高级搜索广告订单转化率大值")
    private BigDecimal salesConversionRateMax;
    @ApiModelProperty(value = "高级搜索acots小值")
    private BigDecimal acotsMin;  //acots
    @ApiModelProperty(value = "高级搜索acots大值")
    private BigDecimal acotsMax;
    @ApiModelProperty(value = "高级搜索asots小值")
    private BigDecimal asotsMin;  //acos
    @ApiModelProperty(value = "高级搜索asots大值")
    private BigDecimal asotsMax;

    @ApiModelProperty(value = "高级搜索默认调整最小")
    private BigDecimal adjustValueMin;
    @ApiModelProperty(value = "高级搜索默认调整最大")
    private BigDecimal adjustValueMax;

    @ApiModelProperty(value = "高级搜索店铺销售额")
    private BigDecimal shopSales;

    @ApiModelProperty("广告活动状态")
    private String state;
    @ApiModelProperty("操作状态")
    private String status;
    @ApiModelProperty("服务状态")
    private String servingStatus;
    //环比相关参数
    private Boolean isCompare;
    private String compareStartDate;
    private String compareEndDate;

    /*******************高级搜索新增查询字段***************************/
    @ApiModelProperty(value = "可见展示次数最小值")
    private Integer viewImpressionsMin;
    @ApiModelProperty(value = "可见展示次数最大值")
    private Integer viewImpressionsMax;


    @ApiModelProperty(value = "cpa最小值")
    private BigDecimal cpaMin;
    @ApiModelProperty(value = "cpa最大值")
    private BigDecimal cpaMax;


    @ApiModelProperty(value = "vcpm最小值")
    private BigDecimal vcpmMin;
    @ApiModelProperty(value = "vcpm最大值")
    private BigDecimal vcpmMax;


    @ApiModelProperty(value = "本广告产品订单量最小值")
    private Integer adSaleNumMin;
    @ApiModelProperty(value = "本广告产品订单量最大值")
    private Integer adSaleNumMax;


    @ApiModelProperty(value = "其他产品广告订单量最小值")
    private Integer adOtherOrderNumMin;
    @ApiModelProperty(value = "其他产品广告订单量最大值")
    private Integer adOtherOrderNumMax;


    @ApiModelProperty(value = "本广告产品销售额最小值")
    private BigDecimal adSalesMin;
    @ApiModelProperty(value = "本广告产品销售额最大值")
    private BigDecimal adSalesMax;


    @ApiModelProperty(value = "其他产品广告销售额最小值")
    private BigDecimal adOtherSalesMin;
    @ApiModelProperty(value = "其他产品广告销售额最大值")
    private BigDecimal adOtherSalesMax;


    @ApiModelProperty(value = "本廣告产品销量最小值")
    private Integer adSelfSaleNumMin;
    @ApiModelProperty(value = "本廣告产品销量最大值")
    private Integer adSelfSaleNumMax;


    @ApiModelProperty(value = "其他产品广告销量最小值")
    private Integer adOtherSaleNumMin;
    @ApiModelProperty(value = "其他产品广告销量最大值")
    private Integer adOtherSaleNumMax;


    @ApiModelProperty(value = "“品牌新买家”订单量最小值")
    private Integer ordersNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”订单量最大值")
    private Integer ordersNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”订单百分比最小值")
    private BigDecimal orderRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”订单百分比最大值")
    private BigDecimal orderRateNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销售额最小值")
    private BigDecimal salesNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销售额最大值")
    private BigDecimal salesNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销售额百分比最小值")
    private BigDecimal salesRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销售额百分比最大值")
    private BigDecimal salesRateNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销量最小值")
    private Integer unitsOrderedNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销量最大值")
    private Integer unitsOrderedNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销量百分比最小值")
    private BigDecimal unitsOrderedRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销量百分比最大值")
    private BigDecimal unitsOrderedRateNewToBrandFTDMax;


    @ApiModelProperty(value = "广告销量最小值")
    private Integer adSalesTotalMin;
    @ApiModelProperty(value = "广告销量最大值")
    private Integer adSalesTotalMax;

    @ApiModelProperty("广告笔单价最小值")
    private BigDecimal advertisingUnitPriceMin;
    @ApiModelProperty("广告笔单价最大值")
    private BigDecimal advertisingUnitPriceMax;

    private String pageSign;
    /**
     *  只查询数量 简化查询
     */
    private Boolean onlyCount;

    /** 查询数据类型 {@link com.meiyunji.sponsored.service.enums.SearchDataTypeEnum}*/
    @ApiModelProperty("查询数据类型")
    private Integer searchDataType;

    /**
     * {@link com.amazon.advertising.mode.PredicateEnum}
     */
    private String campaignSite;
    /**
     * 是否是查询二级列表
     */
    private boolean querySublist;

    public enum placementPredicateEnum implements BaseEnum {

        Other("placementRestOfSearch","Other on-Amazon","搜索结果的其余位置"),
        placementProductPage("placementProductPage","Detail Page on-Amazon","产品页面"),
        placementTop("placementTop","Top of Search on-Amazon","搜索结果顶部(首页)"),
        offAmazon("offAmazon", "Off Amazon", "Off Amazon"),
        siteAmazonBusiness("siteAmazonBusiness", "Amazon Business", "企业购"),
        ;
        private String code;
        private String content;
        private String desc;

        placementPredicateEnum(String code, String content, String desc) {
            this.code = code;
            this.content = content;
            this.desc = desc;
        }

        public String getContent() {
            return content;
        }

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getDescription() {
            return desc;
        }

        public static String getDescription(String content) {
            for (placementPredicateEnum status : values()) {
                if (status.getContent().equals(content)) {
                    return status.getDescription();
                }
            }
            return null;
        }
    }

    public enum AllPlacementPredicateEnum implements BaseEnum {

        placementRestOfSearch("placementRestOfSearch","Other on-Amazon","搜索结果的其余位置","2"),
        placementProductPage("placementProductPage","Detail Page on-Amazon","产品页面", "1"),
        placementTop("placementTop","Top of Search on-Amazon","搜索结果顶部(首页)", "3");
        ;
        private String code;
        private String content;
        private String desc;
        private String sort;

        AllPlacementPredicateEnum(String code, String content, String desc, String sort) {
            this.code = code;
            this.content = content;
            this.desc = desc;
            this.sort = sort;
        }

        public String getContent() {
            return content;
        }

        public String getSort() {
            return sort;
        }

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getDescription() {
            return desc;
        }

        public static String getDescription(String content) {
            for (AllPlacementPredicateEnum status : values()) {
                if (status.getContent().equals(content)) {
                    return status.getDescription();
                }
            }
            return null;
        }
    }
}
