package com.meiyunji.sponsored.service.kafka.consumer;

import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.sd.mode.ExpressionNested;
import com.amazon.advertising.spV3.enumeration.SpV3NegativeMatchTypeEnum;
import com.google.api.client.util.Lists;
import com.meiyunji.sellfox.aadras.api.enumeration.AdvertiseRuleTaskTypePb;
import com.meiyunji.sellfox.aadras.types.enumeration.AdvertiseRuleTaskResultCode;
import com.meiyunji.sellfox.aadras.types.enumeration.AdvertiseRuleTaskType;
import com.meiyunji.sellfox.aadras.types.enumeration.RuleExecuteOperationType;
import com.meiyunji.sellfox.aadras.types.enumeration.SpV3ExpressionEnum;
import com.meiyunji.sellfox.aadras.types.schedule.AdvertiseRuleTaskExecuteRecordV2Message;
import com.meiyunji.sellfox.aadras.types.schedule.entity.NegativeKeywordExtendEntity;
import com.meiyunji.sellfox.aadras.types.schedule.entity.NegativeTargetExtendEntity;
import com.meiyunji.sellfox.aadras.types.schedule.entity.NegativeTargetingExpression;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.autoRule.dao.*;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleExecuteRecord;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatusDelete;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleTemplate;
import com.meiyunji.sponsored.service.autoRule.vo.*;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service2.ICpcCampaignService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbNeKeywordApiService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbNeTargetApiService;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdNeTargetingApiService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.enums.AutoRuleTargetTypeEnum;
import com.meiyunji.sponsored.service.enums.AutoTargetTypeEnum;
import com.meiyunji.sponsored.service.enums.TargetingEnum;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.jooq.JSON;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2023-08-24  14:48
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "spring.kafka.custom-rule.enabled", havingValue = "true")
public class ScheduleCustomRuleMessageConsumer {
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private IAdvertiseAutoRuleTemplateDao advertiseAutoRuleTemplateDao;
    @Autowired
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;
    @Autowired
    private IAdvertiseAutoRuleStatusDeleteDao advertiseAutoRuleStatusDeleteDao;
    @Autowired
    private IAdvertiseAutoRuleExecuteRecordDao advertiseAutoRuleExecuteRecordDao;
    @Autowired
    private AdvertiseAutoRuleExecuteRecordSequenceDao advertiseAutoRuleExecuteRecordSequenceDao;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private ICpcCampaignService cpcCampaignService;
    @Autowired
    private IAmazonSbAdNeKeywordDao amazonSbAdNeKeywordDao;


    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Autowired
    private IAmazonSbAdNeTargetingDao amazonSbAdNeTargetingDao;
    @Autowired
    private IAmazonSdAdNeTargetingDao amazonSdAdNeTargetingDao;

    @Autowired
    private CpcSbNeKeywordApiService cpcSbNeKeywordApiService;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private CpcSbNeTargetApiService cpcSbNeTargetApiService;

    @Autowired
    private CpcSdNeTargetingApiService cpcSdNeTargetingApiService;

    @KafkaListener(topics = "${kafka.consumers.custom-rule-execute-record.topic}", containerFactory = "customRuleKafkaContainerFactory")
    public void consumer(ConsumerRecord<?, byte[]> record) throws Exception {
        try {
            if (record == null || record.value() == null) {
                log.error("custom-rule-finished record is null");
                return;
            }
            AdvertiseRuleTaskExecuteRecordV2Message message = JSONUtil.jsonToObject(new String(record.value()), AdvertiseRuleTaskExecuteRecordV2Message.class);
            if (message == null) {
                log.error("custom-rule-finished message is null");
                return;
            }
            log.info("message: {}", JSONUtil.objectToJson(message));
            //自动化规则回写执行后和待确认消息数据入表
            AdvertiseAutoRuleStatus advertiseAutoRuleStatus = new AdvertiseAutoRuleStatus();
            AdvertiseAutoRuleStatusDelete advertiseAutoRuleStatusDelete = null;
            AdvertiseAutoRuleStatus autoRuleStatusDaoByTaskId = advertiseAutoRuleStatusDao.getByTaskId(message.getPuid(), message.getShopId(), message.getTaskId());
            if (autoRuleStatusDaoByTaskId == null) {
                advertiseAutoRuleStatusDelete = advertiseAutoRuleStatusDeleteDao.getByTaskId(message.getPuid(), message.getShopId(), message.getTaskId());
                if (advertiseAutoRuleStatusDelete == null) {
                    log.error("当前受控对象为空 taskId={}", message.getTaskId());
                    return;
                }
                BeanUtils.copyProperties(advertiseAutoRuleStatusDelete, advertiseAutoRuleStatus);
            } else {
                BeanUtils.copyProperties(autoRuleStatusDaoByTaskId, advertiseAutoRuleStatus);
            }
            List<AmazonAdKeyword> amazonAdKeywords = new ArrayList<>();
            List<AmazonAdTargeting> amazonAdTargetings = new ArrayList<>();
            List<AmazonSbAdNeKeyword> amazonSbAdNeKeywords = new ArrayList<>();
            List<AmazonSbAdNeTargeting> amazonSbAdNeTargetings = new ArrayList<>();
            List<AmazonSdAdNeTargeting> amazonSdAdNeTargetings = new ArrayList<>();
            List<AmazonAdKeyword> queryAmazonAdKeywordList = new ArrayList<>();
            List<AmazonAdTargeting> queryAmazonAdTargetingList = new ArrayList<>();
            List<AmazonSbAdKeyword> queryAmazonSbAdKeywordList = new ArrayList<>();
            //自动化规则回写执行后和待确认消息数据入表
            List<AdvertiseAutoRuleExecuteRecord> list = Lists.newArrayList();
            AdvertiseAutoRuleTemplate advertiseAutoRuleTemplate = advertiseAutoRuleTemplateDao.selectByPrimaryKey(message.getPuid(), message.getTemplateId());
            AdvertiseAutoRuleExecuteRecord advertiseAutoRuleExecuteRecord = new AdvertiseAutoRuleExecuteRecord();
            advertiseAutoRuleExecuteRecord.setId(advertiseAutoRuleExecuteRecordSequenceDao.genId());
            advertiseAutoRuleExecuteRecord.setPuid(message.getPuid());
            advertiseAutoRuleExecuteRecord.setShopId(message.getShopId());
            advertiseAutoRuleExecuteRecord.setMarketplaceId(message.getMarketplaceId());
            advertiseAutoRuleExecuteRecord.setRuleType(message.getRuleType());
            advertiseAutoRuleExecuteRecord.setTaskId(message.getTaskId());
            advertiseAutoRuleExecuteRecord.setProfileId(message.getProfileId());
            advertiseAutoRuleExecuteRecord.setItemType(message.getItemType().name());
            if (AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.QUERY_KEYWORD.name().equals(advertiseAutoRuleExecuteRecord.getItemType())) {
                advertiseAutoRuleExecuteRecord.setItemType(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.SEARCH_QUERY.name());
            }
            advertiseAutoRuleExecuteRecord.setItemId(message.getItemId());
            advertiseAutoRuleExecuteRecord.setRecordId(message.getRecordId());
            advertiseAutoRuleExecuteRecord.setAdType(message.getAdType().name());
            advertiseAutoRuleExecuteRecord.setOriginalValue(message.getOriginalValue());
            if (StringUtils.isNotBlank(message.getExecuteTimeSpaceUnit())) {
                advertiseAutoRuleExecuteRecord.setExecuteTimeSpaceUnit(message.getExecuteTimeSpaceUnit());
            }
            advertiseAutoRuleExecuteRecord.setExecuteTimeSpaceValue(message.getExecuteTimeSpaceValue());
            advertiseAutoRuleExecuteRecord.setSetRelation(message.getSetRelation());
            if (message.getExecuteAt() != null) {
                advertiseAutoRuleExecuteRecord.setExecuteAt(convertDateFromSite(message.getExecuteAt(), message.getMarketplaceId()));
                advertiseAutoRuleExecuteRecord.setExecuteSiteDate(message.getExecuteAt());
            }
            if (message.getTriggerAt() != null) {
                advertiseAutoRuleExecuteRecord.setTriggerAt(message.getTriggerAt().plusHours(8));
            }
            advertiseAutoRuleExecuteRecord.setExecuteType(message.getExecuteType().name());
            advertiseAutoRuleExecuteRecord.setCode(message.getCode().name());
            List<AutoRuleJson> ruleJsonList = Lists.newArrayList();
            List<AdvertiseRuleTaskExecuteRecordV2Message.Item> rules = message.getRule();
            List<RuleIndexJson> ruleIndexJsonList = Lists.newArrayList();
            AdvertiseRuleTaskExecuteRecordV2Message.PerformOperation performOperation = message.getPerformOperation().get(0);
            rules.forEach(e -> {
                AutoRuleJson ruleJson = new AutoRuleJson();
                RuleIndexJson ruleIndexJson = new RuleIndexJson();
                ruleJson.setDay(e.getDay());
                ruleIndexJson.setDay(e.getDay());
                ruleIndexJson.setExcludeDay(e.getExcludeDay());
                ruleJson.setExcludeDay(e.getExcludeDay());
                ruleJson.setRuleIndex(e.getRuleIndex().name());
                ruleJson.setRuleOperator(e.getRuleOperator().name());
                ruleJson.setRuleStatisticalModeType(e.getRuleStatisticalMode().name());
                ruleJson.setRuleValue(e.getRuleValue());
                ruleJson.setAfterRuleValue(e.getAfterRuleValue());
                ruleIndexJson.setRuleIndex(e.getRuleIndex().name());
                ruleIndexJson.setRuleIndexValue(e.getTriggerValue());
                ruleIndexJsonList.add(ruleIndexJson);
                ruleJsonList.add(ruleJson);
            });
            List<PerformOperationJson> performOperationJsonList = Lists.newArrayList();
            PerformOperationJson performOperationJson = new PerformOperationJson();
            performOperationJson.setRuleAction(performOperation.getRuleAction().name());
            if (performOperation.getRuleAdjust() != null) {
                performOperationJson.setRuleAdjust(performOperation.getRuleAdjust().name());
            }
            if (performOperation.getBaseValueType() != null) {
                performOperationJson.setBaseValueType(performOperation.getBaseValueType().name());
            }

            performOperationJson.setCampaignId(performOperation.getTargetCampaignId());
            performOperationJson.setAdGroupId(performOperation.getTargetAdGroupId());
            performOperationJson.setAdJustValue(performOperation.getAdJustValue());
            performOperationJson.setLimitValue(performOperation.getLimitValue());
            performOperationJson.setAdPlaceTopValue(performOperation.getAdPlaceTopValue());
            performOperationJson.setAdPlaceProductValue(performOperation.getAdPlaceProductValue());
            if (StringUtils.isNotBlank(performOperation.getAdOtherValue())) {
                performOperationJson.setAdOtherValue(performOperation.getAdOtherValue());
            }
            performOperationJsonList.add(performOperationJson);
            DataDetailJson dataDetailJson = new DataDetailJson();
            dataDetailJson.setOriginalAdPlaceTopValue(performOperation.getOriginalAdPlaceTopValue());
            dataDetailJson.setOriginalAdPlaceProductValue(performOperation.getOriginalAdPlaceProductValue());
            advertiseAutoRuleExecuteRecord.setOriginalAdPlaceTopValue(performOperation.getOriginalAdPlaceTopValue());
            advertiseAutoRuleExecuteRecord.setOriginalAdPlaceProductValue(performOperation.getOriginalAdPlaceProductValue());
            if (StringUtils.isNotBlank(performOperation.getOriginalAdOtherValue())) {
                dataDetailJson.setOriginalAdOtherValue(performOperation.getOriginalAdOtherValue());
                advertiseAutoRuleExecuteRecord.setOriginalAdOtherValue(performOperation.getOriginalAdOtherValue());
            }
            dataDetailJson.setExecuteAdPlaceTopValue(performOperation.getExecuteAdPlaceTopValue());
            dataDetailJson.setExecuteAdPlaceProductValue(performOperation.getExecuteAdPlaceProductValue());
            if (StringUtils.isNotBlank(performOperation.getExecuteAdOtherValue())) {
                dataDetailJson.setExecuteAdOtherValue(performOperation.getExecuteAdOtherValue());
                advertiseAutoRuleExecuteRecord.setExecuteAdOtherValue(performOperation.getExecuteAdOtherValue());
            }
            advertiseAutoRuleExecuteRecord.setExecuteAdPlaceProductValue(performOperation.getExecuteAdPlaceProductValue());
            advertiseAutoRuleExecuteRecord.setExecuteAdPlaceTopValue(performOperation.getExecuteAdPlaceTopValue());
            CallbackOperateJson callbackOperateJson = new CallbackOperateJson();
            if (message.getOperation() != null) {
                advertiseAutoRuleExecuteRecord.setOperation(message.getOperation().name());
            }
            if (RuleExecuteOperationType.restore.name().equals(advertiseAutoRuleExecuteRecord.getOperation())) {
                AdvertiseRuleTaskExecuteRecordV2Message.RecoveryAdjustment recoveryAdjustment = message.getRecoveryAdjustment();
                dataDetailJson.setOriginalValue(recoveryAdjustment.getOriginalValue());
                dataDetailJson.setExecuteValue(recoveryAdjustment.getExecuteValue());
                advertiseAutoRuleExecuteRecord.setExecuteValue(recoveryAdjustment.getExecuteValue());
                advertiseAutoRuleExecuteRecord.setOriginalValue(recoveryAdjustment.getOriginalValue());
                dataDetailJson.setRuleIndexList(ruleIndexJsonList);
                callbackOperateJson.setAdjustType(recoveryAdjustment.getAdjustType().name());
                callbackOperateJson.setAdJustValue(recoveryAdjustment.getAdJustValue());
                callbackOperateJson.setOriginalValue(recoveryAdjustment.getOriginalValue());
                advertiseAutoRuleExecuteRecord.setItemStatus(message.getItemStatus());
                advertiseAutoRuleExecuteRecord.setCallbackOperate(JSONUtil.objectToJson(callbackOperateJson));
            } else {
                dataDetailJson.setOriginalValue(performOperation.getOriginalValue());
                dataDetailJson.setExecuteValue(performOperation.getExecuteValue());
                advertiseAutoRuleExecuteRecord.setOriginalValue(performOperation.getOriginalValue());
                advertiseAutoRuleExecuteRecord.setExecuteValue(performOperation.getExecuteValue());
                dataDetailJson.setRuleIndexList(ruleIndexJsonList);
                if (performOperation.getMatchType() != null) {
                    dataDetailJson.setMatchType(performOperation.getMatchType().name());
                }
                if (StringUtils.isNotBlank(performOperation.getBiddingPrice())) {
                    dataDetailJson.setBidValue(performOperation.getBiddingPrice());
                }
                if (performOperation.getBiddingType() != null) {
                    dataDetailJson.setBidType(performOperation.getBiddingType().name());
                }
                if (message.getNegativeTargetType() != null) {
                    dataDetailJson.setNegativeTargetType(message.getNegativeTargetType().name());
                }
            }
            advertiseAutoRuleExecuteRecord.setRule(JSONUtil.objectToJson(ruleJsonList));
            advertiseAutoRuleExecuteRecord.setPerformOperation(JSONUtil.objectToJson(performOperationJsonList));
            advertiseAutoRuleExecuteRecord.setTemplateId(message.getTemplateId());
            advertiseAutoRuleExecuteRecord.setTimeType("WEEKLY");
            advertiseAutoRuleExecuteRecord.setStartDate(message.getStart());
            advertiseAutoRuleExecuteRecord.setEndDate(message.getEnd());
            List<AdvertiseRuleTaskExecuteRecordV2Message.TimeRuleItem> timeRuleItems = message.getTimeRule();
            if (CollectionUtils.isNotEmpty(timeRuleItems)) {
                List<TimeRuleJson> timeRuleJsonList = new ArrayList<>();
                timeRuleItems.forEach(t -> {
                    TimeRuleJson timeRuleJson = new TimeRuleJson();
                    timeRuleJson.setSiteDate(t.getSiteDate());
                    timeRuleJson.setStartTimeSite(t.getStartTimeSite());
                    timeRuleJson.setEndTimeSite(t.getEndTimeSite());
                    timeRuleJsonList.add(timeRuleJson);
                });
                advertiseAutoRuleExecuteRecord.setTimeRule(JSONUtil.objectToJson(timeRuleJsonList));
            }
            if (advertiseAutoRuleTemplate == null) {
                advertiseAutoRuleExecuteRecord.setTemplateName("-");
            } else {
                advertiseAutoRuleExecuteRecord.setTemplateName(advertiseAutoRuleTemplate.getTemplateName());
            }
            advertiseAutoRuleExecuteRecord.setRuleActionType(performOperation.getRuleAction().name());
            advertiseAutoRuleExecuteRecord.setStateErrMsg(message.getStateErrMsg());
            if ("CAMPAIGN".equals(message.getItemType().name())) {
                AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllDao.getByCampaignId(message.getPuid(), message.getShopId(), message.getItemId());
                advertiseAutoRuleExecuteRecord.setItemName(amazonAdCampaignAll.getName());
                advertiseAutoRuleExecuteRecord.setItemOperateName(amazonAdCampaignAll.getName());
                advertiseAutoRuleExecuteRecord.setItemOperateId(message.getItemId());
                advertiseAutoRuleExecuteRecord.setItemOperateType("CAMPAIGN");
                advertiseAutoRuleExecuteRecord.setCampaignId(amazonAdCampaignAll.getCampaignId());
            } else if ("AD_GROUP".equals(message.getItemType().name())) {
                fillRecordInfosForAdGroup(message, advertiseAutoRuleExecuteRecord);
            } else if ("KEYWORD".equals(message.getItemType().name())) {
                fillRecordInfosForKeyword(message, advertiseAutoRuleExecuteRecord, amazonAdKeywords, amazonSbAdNeKeywords);
            } else if ("TARGET".equals(message.getItemType().name())) {
                fillRecordInfosForTarget(message, advertiseAutoRuleExecuteRecord, amazonAdTargetings, amazonSbAdNeTargetings, amazonSdAdNeTargetings);
            } else if ("GROUP_SEARCH_QUERY".equals(message.getItemType().name()) || "QUERY_KEYWORD".equals(message.getItemType().name())) {
                AmazonAdGroup amazonAdGroup = new AmazonAdGroup();
                AmazonSbAdGroup amazonSbAdGroup = new AmazonSbAdGroup();
                AmazonAdGroup queryAmazonAdGroup = null;
                AmazonSbAdGroup queryAmazonSbAdGroup = null;
                if (StringUtils.isNotBlank(performOperation.getTargetAdGroupId())) {
                    queryAmazonAdGroup = amazonAdGroupDao.getByAdGroupId(message.getPuid(), message.getShopId(), performOperation.getTargetAdGroupId());
                    queryAmazonSbAdGroup = amazonSbAdGroupDao.getByAdGroupId(message.getPuid(), message.getShopId(), performOperation.getTargetAdGroupId());
                    if (queryAmazonAdGroup != null) {
                        dataDetailJson.setAdGroupName(queryAmazonAdGroup.getName());
                        dataDetailJson.setAdGroupId(performOperation.getTargetAdGroupId());
                    }
                    if (queryAmazonSbAdGroup != null) {
                        dataDetailJson.setAdGroupName(queryAmazonSbAdGroup.getName());
                        dataDetailJson.setAdGroupId(performOperation.getTargetAdGroupId());
                    }
                }
                if ("sp".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
                    amazonAdGroup = amazonAdGroupDao.getByAdGroupId(message.getPuid(), message.getShopId(), message.getItemId());
                    advertiseAutoRuleExecuteRecord.setItemName(amazonAdGroup.getName());
                    advertiseAutoRuleExecuteRecord.setCampaignId(amazonAdGroup.getCampaignId());
                } else if ("sb".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
                    amazonSbAdGroup = amazonSbAdGroupDao.getByGroupId(message.getPuid(), message.getShopId(), message.getItemId());
                    advertiseAutoRuleExecuteRecord.setItemName(amazonSbAdGroup.getName());
                    advertiseAutoRuleExecuteRecord.setCampaignId(amazonSbAdGroup.getCampaignId());
                }
                advertiseAutoRuleExecuteRecord.setAdGroupId(message.getItemId());
                dataDetailJson.setNegativeTargetType(performOperation.getNegativeType().name());
                advertiseAutoRuleExecuteRecord.setItemOperateName(performOperation.getQuery());
                advertiseAutoRuleExecuteRecord.setItemOperateType("SEARCH_QUERY");
                advertiseAutoRuleExecuteRecord.setItemOperateId(message.getTargetId());
                if (message.getNegativeTargetType() != null) {
                    advertiseAutoRuleExecuteRecord.setNegativeTargetType(message.getNegativeTargetType().name());
                }
                if (message.getAddTargetType() != null) {
                    advertiseAutoRuleExecuteRecord.setAddTargetType(message.getAddTargetType().name());
                }
                if ("SUCCESS".equals(message.getCode().name())
                        && "addTarget".equals(performOperationJson.getRuleAction())
                        && "targeting".equals(advertiseAutoRuleExecuteRecord.getNegativeTargetType())) {
                    AmazonAdTargeting queryAmazonAdTargeting = new AmazonAdTargeting();
                    queryAmazonAdTargeting.setPuid(message.getPuid());
                    queryAmazonAdTargeting.setShopId(message.getShopId());
                    queryAmazonAdTargeting.setMarketplaceId(message.getMarketplaceId());
                    queryAmazonAdTargeting.setProfileId(message.getProfileId());
                    queryAmazonAdTargeting.setTargetId(performOperation.getTargetKeywordId());
                    queryAmazonAdTargeting.setTargetingValue(performOperation.getQuery().toUpperCase());
                    queryAmazonAdTargeting.setCampaignId(performOperation.getTargetCampaignId());
                    queryAmazonAdTargeting.setAdGroupId(performOperation.getTargetAdGroupId());
                    queryAmazonAdTargeting.setState("enabled");
                    queryAmazonAdTargeting.setExpressionType("MANUAL");
                    queryAmazonAdTargeting.setType("asin");
                    queryAmazonAdTargeting.setSelectType(SpV3ExpressionEnum.getExpressionByValueV3(message.getExpressionType()).getValue());
                    List<Expression> expressionList = Lists.newArrayList();
                    Expression expression = new Expression();
                    expression.setType(SpV3ExpressionEnum.getExpressionByValueV3(message.getExpressionType()).getValue());
                    expression.setValue(performOperation.getQuery().toUpperCase());
                    expressionList.add(expression);
                    List<Expression> resolvedExpressionList = Lists.newArrayList();
                    Expression resolvedExpression = new Expression();
                    resolvedExpression.setType(message.getExpressionType());
                    resolvedExpression.setValue(performOperation.getQuery().toUpperCase());
                    resolvedExpressionList.add(resolvedExpression);
                    queryAmazonAdTargeting.setExpression(JSONUtil.objectToJson(expressionList));
                    queryAmazonAdTargeting.setResolvedExpression(JSONUtil.objectToJson(resolvedExpressionList));
                    queryAmazonAdTargeting.setBid(Double.valueOf(performOperation.getBiddingPrice()));
                    queryAmazonAdTargetingList.add(queryAmazonAdTargeting);
                } else if ("SUCCESS".equals(message.getCode().name())
                        && "addTarget".equals(performOperationJson.getRuleAction())
                        && "keyword".equals(advertiseAutoRuleExecuteRecord.getNegativeTargetType())) {
                    if ("SP".equals(performOperation.getTargetAdType())) {
                        AmazonAdKeyword queryAmazonAdKeyword = new AmazonAdKeyword();
                        queryAmazonAdKeyword.setPuid(message.getPuid());
                        queryAmazonAdKeyword.setShopId(message.getShopId());
                        queryAmazonAdKeyword.setMarketplaceId(message.getMarketplaceId());
                        queryAmazonAdKeyword.setProfileId(message.getProfileId());
                        queryAmazonAdKeyword.setKeywordId(performOperation.getTargetKeywordId());
                        queryAmazonAdKeyword.setKeywordText(performOperation.getQuery());
                        queryAmazonAdKeyword.setCampaignId(performOperation.getTargetCampaignId());
                        queryAmazonAdKeyword.setAdGroupId(performOperation.getTargetAdGroupId());
                        queryAmazonAdKeyword.setState("enabled");
                        queryAmazonAdKeyword.setMatchType(performOperation.getMatchType().name().toLowerCase());
                        queryAmazonAdKeyword.setType("biddable");
                        queryAmazonAdKeyword.setBid(Double.valueOf(performOperation.getBiddingPrice()));
                        queryAmazonAdKeywordList.add(queryAmazonAdKeyword);
                    } else if ("SB".equals(performOperation.getTargetAdType())) {
                        AmazonSbAdKeyword queryAmazonSbAdKeyword = new AmazonSbAdKeyword();
                        queryAmazonSbAdKeyword.setPuid(message.getPuid());
                        queryAmazonSbAdKeyword.setShopId(message.getShopId());
                        queryAmazonSbAdKeyword.setMarketplaceId(message.getMarketplaceId());
                        queryAmazonSbAdKeyword.setProfileId(message.getProfileId());
                        queryAmazonSbAdKeyword.setKeywordId(performOperation.getTargetKeywordId());
                        queryAmazonSbAdKeyword.setKeywordText(performOperation.getQuery());
                        queryAmazonSbAdKeyword.setCampaignId(performOperation.getTargetCampaignId());
                        queryAmazonSbAdKeyword.setAdGroupId(performOperation.getTargetAdGroupId());
                        queryAmazonSbAdKeyword.setState("enabled");
                        queryAmazonSbAdKeyword.setMatchType(performOperation.getMatchType().name().toLowerCase());
                        queryAmazonSbAdKeyword.setBid(new BigDecimal(performOperation.getBiddingPrice()));
                        queryAmazonSbAdKeywordList.add(queryAmazonSbAdKeyword);
                    }
                }
                if ("targeting".equals(advertiseAutoRuleExecuteRecord.getNegativeTargetType())) {
                    NegativeTargetExtendEntity negativeTargetExtendEntity = message.getNegativeTargetExtendEntity();
                    if (negativeTargetExtendEntity != null) {
                        AmazonAdTargeting amazonAdTargeting = new AmazonAdTargeting();
                        amazonAdTargeting.setTargetId(negativeTargetExtendEntity.getTargetId());
                        amazonAdTargeting.setPuid(amazonAdGroup.getPuid());
                        amazonAdTargeting.setShopId(amazonAdGroup.getShopId());
                        amazonAdTargeting.setMarketplaceId(amazonAdGroup.getMarketplaceId());
                        amazonAdTargeting.setAdGroupId(amazonAdGroup.getAdGroupId());
                        amazonAdTargeting.setDxmGroupId(amazonAdGroup.getId());
                        amazonAdTargeting.setCampaignId(amazonAdGroup.getCampaignId());
                        amazonAdTargeting.setProfileId(amazonAdGroup.getProfileId());
                        amazonAdTargeting.setExpressionType(Constants.MANUAL);
                        amazonAdTargeting.setState(negativeTargetExtendEntity.getState().toLowerCase());
                        amazonAdTargeting.setType(Constants.TARGETING_TYPE_NEGATIVEASIN);
                        amazonAdTargeting.setTargetingValue(performOperation.getQuery());
                        if (CollectionUtils.isNotEmpty(negativeTargetExtendEntity.getExpression())) {
                            List<Expression> expressions = new ArrayList<>();
                            List<NegativeTargetingExpression> negativeTargetingExpressions = negativeTargetExtendEntity.getExpression();
                            for (NegativeTargetingExpression negativeTargetingExpression : negativeTargetingExpressions) {
                                Expression expression = new Expression();
                                expression.setType(negativeTargetingExpression.getType());
                                expression.setValue(negativeTargetingExpression.getValue());
                                expressions.add(expression);
                            }
                            amazonAdTargeting.setExpression(JSONUtil.objectToJson(expressions));
                        }
                        if (CollectionUtils.isNotEmpty(negativeTargetExtendEntity.getResolvedExpression())) {
                            List<Expression> resolveExpressions = new ArrayList<>();
                            List<NegativeTargetingExpression> negativeTargetingExpressions1 = negativeTargetExtendEntity.getResolvedExpression();
                            for (NegativeTargetingExpression negativeTargetingExpression : negativeTargetingExpressions1) {
                                Expression expression = new Expression();
                                expression.setType(negativeTargetingExpression.getType());
                                expression.setValue(negativeTargetingExpression.getValue());
                                resolveExpressions.add(expression);
                            }
                            amazonAdTargeting.setResolvedExpression(JSONUtil.objectToJson(resolveExpressions));
                        }
                        amazonAdTargetings.add(amazonAdTargeting);
                    }
                } else if ("keyword".equals(message.getNegativeTargetType().name())) {
                    NegativeKeywordExtendEntity negativeKeywordExtendEntity = message.getNegativeKeywordExtendEntity();
                    if (negativeKeywordExtendEntity != null) {
                        if ("sp".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
                            AmazonAdKeyword amazonAdKeyword = new AmazonAdKeyword();
                            amazonAdKeyword.setPuid(message.getPuid());
                            amazonAdKeyword.setKeywordId(negativeKeywordExtendEntity.getKeywordId());
                            amazonAdKeyword.setShopId(message.getShopId());
                            amazonAdKeyword.setMarketplaceId(message.getMarketplaceId());
                            amazonAdKeyword.setProfileId(message.getProfileId());
                            amazonAdKeyword.setAdGroupId(negativeKeywordExtendEntity.getAdGroupId());
                            amazonAdKeyword.setDxmGroupId(amazonAdGroup.getId());
                            amazonAdKeyword.setCampaignId(amazonAdGroup.getCampaignId());
                            amazonAdKeyword.setKeywordText(negativeKeywordExtendEntity.getKeywordText());
                            amazonAdKeyword.setMatchType(SpV3NegativeMatchTypeEnum.getSpV3NegativeMatchTypeEnumByValueV3(negativeKeywordExtendEntity.getMatchType()).value());
                            amazonAdKeyword.setType(Constants.NEGATIVE);
                            amazonAdKeyword.setState(negativeKeywordExtendEntity.getState().toLowerCase());
                            amazonAdKeywords.add(amazonAdKeyword);
                        } else if ("sb".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
                            if (!amazonSbAdNeKeywordDao.exist(message.getPuid(), message.getShopId(), negativeKeywordExtendEntity.getKeywordId())) {
                                AmazonSbAdNeKeyword amazonSbAdNeKeyword = new AmazonSbAdNeKeyword();
                                amazonSbAdNeKeyword.setPuid(message.getPuid());
                                amazonSbAdNeKeyword.setKeywordId(negativeKeywordExtendEntity.getKeywordId());
                                amazonSbAdNeKeyword.setShopId(message.getShopId());
                                amazonSbAdNeKeyword.setMarketplaceId(message.getMarketplaceId());
                                amazonSbAdNeKeyword.setProfileId(message.getProfileId());
                                amazonSbAdNeKeyword.setAdGroupId(negativeKeywordExtendEntity.getAdGroupId());
                                amazonSbAdNeKeyword.setCampaignId(amazonSbAdGroup.getCampaignId());
                                amazonSbAdNeKeyword.setKeywordText(negativeKeywordExtendEntity.getKeywordText());
                                amazonSbAdNeKeyword.setMatchType(negativeKeywordExtendEntity.getMatchType());
                                amazonSbAdNeKeyword.setState(negativeKeywordExtendEntity.getState().toLowerCase());
                                amazonSbAdNeKeywords.add(amazonSbAdNeKeyword);
                            }
                        }
                    }
                }
            }
            advertiseAutoRuleExecuteRecord.setDataDetail(JSONUtil.objectToJson(dataDetailJson));
            list.add(advertiseAutoRuleExecuteRecord);
            advertiseAutoRuleExecuteRecordDao.batchInsert(message.getPuid(), list);
            //写入ES日志并同步最新数据到广告管理
            if (AdvertiseRuleTaskResultCode.SUCCESS.equals(message.getCode())) {
                try {
                    if (AdvertiseRuleTaskType.CAMPAIGN.equals(message.getItemType())) {
                        amazonAdCampaignAllDao.autoRuleUpdate(message);
                    } else if (AdvertiseRuleTaskType.AD_GROUP.equals(message.getItemType())) {
                        autoAdGroupInfo(advertiseAutoRuleExecuteRecord.getAdType(), message);
                    } else if (AdvertiseRuleTaskType.KEYWORD.equals(message.getItemType())) {
                        if (Constants.SP.equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
                            if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                                //先用双写来进行数据写入， 双写统一了类，所以这里还得转一次
                                amazonAdKeywordDaoRoutingService.insertOnDuplicateKeyUpdate(message.getPuid(), amazonAdKeywords, Constants.NEGATIVE);
                            } else {
                                amazonAdKeywordDaoRoutingService.autoUpdate(message.getPuid(), message.getShopId(), message);
                            }
                        } else if (Constants.SB.equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
                            if (CollectionUtils.isNotEmpty(amazonSbAdNeKeywords)) {
                                //先用双写来进行数据写入， 双写统一了类，所以这里还得转一次
//                                amazonSbAdNeKeywordDao.batchAdd(message.getPuid(), amazonSbAdNeKeywords);
                                ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(message.getShopId(), message.getPuid());
                                if (shopAuth != null) {
                                    cpcSbNeKeywordApiService.syncNeKeywords(shopAuth, null, amazonSbAdNeKeywords.stream().map(AmazonSbAdNeKeyword::getKeywordId).collect(Collectors.toList()));
                                }
                            } else {
                                amazonSbAdKeywordDao.autoUpdate(message.getPuid(), message.getShopId(), message);
                            }
                        }
                    } else if (AdvertiseRuleTaskType.TARGET.equals(message.getItemType())) {
                        if (Constants.SP.equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
                            if (CollectionUtils.isNotEmpty(amazonAdTargetings)) {
                                amazonAdTargetDaoRoutingService.insertOnDuplicateKeyUpdate(message.getPuid(), amazonAdTargetings, Constants.TARGETING_TYPE_NEGATIVEASIN);
                            } else {
                                amazonAdTargetDaoRoutingService.autoUpdate(message.getPuid(), message.getShopId(), message);
                            }
                        }
                        if (Constants.SB.equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
                            if (CollectionUtils.isNotEmpty(amazonSbAdNeTargetings)) {
                                ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(message.getShopId(), message.getPuid());
                                if (shopAuth != null) {
                                    cpcSbNeTargetApiService.syncNeTargets(shopAuth, null, amazonSbAdNeTargetings.get(0).getAdGroupId(), false);
                                }
                            } else {
                                amazonSbAdTargetingDao.autoUpdate(message.getPuid(), message.getShopId(), message);
                            }
                        }
                        if (Constants.SD.equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
                            if (CollectionUtils.isNotEmpty(amazonSdAdNeTargetings)) {
                                ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(message.getShopId(), message.getPuid());
                                if (shopAuth != null) {
                                    cpcSdNeTargetingApiService.syncNeByTargetId(shopAuth,  amazonSdAdNeTargetings.get(0).getTargetId());
                                }
                            } else {
                                amazonSdAdTargetingDao.autoUpdate(message.getPuid(), message.getShopId(), message);
                            }
                        }
                    } else if ("GROUP_SEARCH_QUERY".equals(message.getItemType().name()) || "QUERY_KEYWORD".equals(message.getItemType().name())) {
                        if ("addNotTarget".equals(performOperationJson.getRuleAction())) {
                            if ("targeting".equals(message.getNegativeTargetType().name())) {
                                amazonAdTargetDaoRoutingService.insertOnDuplicateKeyUpdate(message.getPuid(), amazonAdTargetings, Constants.TARGETING_TYPE_NEGATIVEASIN);
                            } else if ("keyword".equals(message.getNegativeTargetType().name())) {
                                if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                                    amazonAdKeywordDaoRoutingService.insertOnDuplicateKeyUpdate(message.getPuid(), amazonAdKeywords, Constants.NEGATIVE);
                                }
                                if (CollectionUtils.isNotEmpty(amazonSbAdNeKeywords)) {
                                    amazonSbAdNeKeywordDao.batchAdd(message.getPuid(), amazonSbAdNeKeywords);
                                }
                            }
                        } else {
                            if (CollectionUtils.isNotEmpty(queryAmazonAdTargetingList)) {
                                amazonAdTargetDaoRoutingService.insertOnDuplicateKeyUpdate(message.getPuid(), queryAmazonAdTargetingList, Constants.TARGETING_TYPE_ASIN);
                            }
                            if (CollectionUtils.isNotEmpty(queryAmazonAdKeywordList)) {
                                amazonAdKeywordDaoRoutingService.insertOnDuplicateKeyUpdate(message.getPuid(), queryAmazonAdKeywordList, Constants.BIDDABLE);
                            }
                            if (CollectionUtils.isNotEmpty(queryAmazonSbAdKeywordList)) {
                                amazonSbAdKeywordDao.batchAdd(message.getPuid(), queryAmazonSbAdKeywordList);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("puid={} shopId={} itemType={} recordId={} 同步广告管理数据失败:", message.getPuid(), message.getShopId(), message.getItemType(), message.getRecordId(), e);
                }
                adManageOperationLogService.getAutoRuleTaskFinishedLog(advertiseAutoRuleExecuteRecord);
            } else if ("FAILURE".equals(message.getCode().name()) && !Constants.NOT_TARGET_MSG.equals(message.getStateErrMsg()) && !Constants.TARGET_MSG.equals(message.getStateErrMsg())) {
                adManageOperationLogService.getAutoRuleTaskFinishedLog(advertiseAutoRuleExecuteRecord);
            }
        } catch (Exception e) {
            log.error("Auto rule kafka error:", e);
        }
    }

    private void autoAdGroupInfo(String adType, AdvertiseRuleTaskExecuteRecordV2Message message) {
        if (Constants.SP.equalsIgnoreCase(adType)) {
            amazonAdGroupDao.autoRuleUpdate(message);
            return;
        }
        if (Constants.SB.equalsIgnoreCase(adType)) {
            amazonSbAdGroupDao.autoRuleUpdate(message);
            return;
        }
        if (Constants.SD.equalsIgnoreCase(adType)) {
            amazonSdAdGroupDao.autoRuleUpdate(message);
        }
    }

    private LocalDateTime convertDateFromSite(LocalDateTime localDateTime, String marketplaceId) {
        if (localDateTime == null) {
            return null;
        }
        MarketTimezoneAndCurrencyEnum cpcTime = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(marketplaceId);
        if (cpcTime == null) {
            return null;
        }
        ZoneId srcZone = ZoneId.of(cpcTime.getTimezone());
        localDateTime = LocalDateTimeUtil.getZoneTime(localDateTime, srcZone, ZoneId.systemDefault());
        return localDateTime;
    }

    private void fillRecordInfosForAdGroup(AdvertiseRuleTaskExecuteRecordV2Message message, AdvertiseAutoRuleExecuteRecord advertiseAutoRuleExecuteRecord) {
        String campaignId = null;
        if ("sp".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
            AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByAdGroupId(message.getPuid(), message.getShopId(), message.getItemId());
            advertiseAutoRuleExecuteRecord.setItemName(amazonAdGroup.getName());
            advertiseAutoRuleExecuteRecord.setItemOperateName(amazonAdGroup.getName());
            advertiseAutoRuleExecuteRecord.setCampaignId(amazonAdGroup.getCampaignId());
            advertiseAutoRuleExecuteRecord.setAdGroupId(amazonAdGroup.getAdGroupId());
            advertiseAutoRuleExecuteRecord.setAdGroupName(amazonAdGroup.getName());
            campaignId = amazonAdGroup.getCampaignId();
        } else if ("sb".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
            AmazonSbAdGroup amazonSbAdGroup = amazonSbAdGroupDao.getByAdGroupId(message.getPuid(), message.getShopId(), message.getItemId());
            advertiseAutoRuleExecuteRecord.setItemName(amazonSbAdGroup.getName());
            advertiseAutoRuleExecuteRecord.setItemOperateName(amazonSbAdGroup.getName());
            advertiseAutoRuleExecuteRecord.setCampaignId(amazonSbAdGroup.getCampaignId());
            advertiseAutoRuleExecuteRecord.setAdGroupId(amazonSbAdGroup.getAdGroupId());
            advertiseAutoRuleExecuteRecord.setAdGroupName(amazonSbAdGroup.getName());
            campaignId = amazonSbAdGroup.getCampaignId();
        } else if ("sd".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
            AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByAdGroupId(message.getPuid(), message.getShopId(), message.getItemId());
            advertiseAutoRuleExecuteRecord.setItemName(amazonSdAdGroup.getName());
            advertiseAutoRuleExecuteRecord.setItemOperateName(amazonSdAdGroup.getName());
            advertiseAutoRuleExecuteRecord.setCampaignId(amazonSdAdGroup.getCampaignId());
            advertiseAutoRuleExecuteRecord.setAdGroupId(amazonSdAdGroup.getAdGroupId());
            advertiseAutoRuleExecuteRecord.setAdGroupName(amazonSdAdGroup.getName());
            campaignId = amazonSdAdGroup.getCampaignId();
        }

        advertiseAutoRuleExecuteRecord.setItemOperateId(message.getItemId());
        advertiseAutoRuleExecuteRecord.setItemOperateType("AD_GROUP");
        if (campaignId != null) {
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllDao.getByCampaignId(message.getPuid(), message.getShopId(), campaignId);
            if (amazonAdCampaignAll != null) {
                advertiseAutoRuleExecuteRecord.setCampaignName(amazonAdCampaignAll.getName());
            }
        }
        advertiseAutoRuleExecuteRecord.setItemType("AD_GROUP");
    }

    private void fillRecordInfosForKeyword(AdvertiseRuleTaskExecuteRecordV2Message message,
                                           AdvertiseAutoRuleExecuteRecord advertiseAutoRuleExecuteRecord,
                                           List<AmazonAdKeyword> amazonAdKeywords,
                                           List<AmazonSbAdNeKeyword> amazonSbAdNeKeywords) {
        NegativeKeywordExtendEntity negativeKeywordExtendEntity = message.getNegativeKeywordExtendEntity();
        if ("sp".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
            AmazonAdKeyword amazonAdKeyword = amazonAdKeywordDaoRoutingService.getByKeywordId(message.getPuid(), message.getShopId(), message.getItemId());
            advertiseAutoRuleExecuteRecord.setItemName(amazonAdKeyword.getKeywordText());
            advertiseAutoRuleExecuteRecord.setItemOperateName(amazonAdKeyword.getKeywordText());
            advertiseAutoRuleExecuteRecord.setCampaignId(amazonAdKeyword.getCampaignId());
            advertiseAutoRuleExecuteRecord.setAdGroupId(amazonAdKeyword.getAdGroupId());
            if (negativeKeywordExtendEntity != null) {
                AmazonAdKeyword amazonAdKeyword1 = new AmazonAdKeyword();
                amazonAdKeyword1.setPuid(message.getPuid());
                amazonAdKeyword1.setKeywordId(negativeKeywordExtendEntity.getKeywordId());
                amazonAdKeyword1.setShopId(message.getShopId());
                amazonAdKeyword1.setMarketplaceId(message.getMarketplaceId());
                amazonAdKeyword1.setProfileId(message.getProfileId());
                amazonAdKeyword1.setAdGroupId(negativeKeywordExtendEntity.getAdGroupId());
                amazonAdKeyword1.setDxmGroupId(amazonAdKeyword.getId());
                amazonAdKeyword1.setCampaignId(amazonAdKeyword.getCampaignId());
                amazonAdKeyword1.setKeywordText(negativeKeywordExtendEntity.getKeywordText());
                amazonAdKeyword1.setMatchType(SpV3NegativeMatchTypeEnum.getSpV3NegativeMatchTypeEnumByValueV3(negativeKeywordExtendEntity.getMatchType()).value());
                amazonAdKeyword1.setType(Constants.NEGATIVE);
                amazonAdKeyword1.setState(negativeKeywordExtendEntity.getState().toLowerCase());
                amazonAdKeywords.add(amazonAdKeyword1);
            }
        } else if ("sb".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
            AmazonSbAdKeyword amazonAdKeyword = amazonSbAdKeywordDao.getByKeywordId(message.getPuid(), message.getShopId(), message.getItemId());
            advertiseAutoRuleExecuteRecord.setItemName(amazonAdKeyword.getKeywordText());
            advertiseAutoRuleExecuteRecord.setItemOperateName(amazonAdKeyword.getKeywordText());
            advertiseAutoRuleExecuteRecord.setCampaignId(amazonAdKeyword.getCampaignId());
            advertiseAutoRuleExecuteRecord.setAdGroupId(amazonAdKeyword.getAdGroupId());
            if (negativeKeywordExtendEntity != null) {
                AmazonSbAdNeKeyword amazonSbAdNeKeyword = new AmazonSbAdNeKeyword();
                amazonSbAdNeKeyword.setPuid(message.getPuid());
                amazonSbAdNeKeyword.setKeywordId(negativeKeywordExtendEntity.getKeywordId());
                amazonSbAdNeKeyword.setShopId(message.getShopId());
                amazonSbAdNeKeyword.setMarketplaceId(message.getMarketplaceId());
                amazonSbAdNeKeyword.setProfileId(message.getProfileId());
                amazonSbAdNeKeyword.setAdGroupId(negativeKeywordExtendEntity.getAdGroupId());
                amazonSbAdNeKeyword.setCampaignId(amazonAdKeyword.getCampaignId());
                amazonSbAdNeKeyword.setKeywordText(negativeKeywordExtendEntity.getKeywordText());
                amazonSbAdNeKeyword.setMatchType(negativeKeywordExtendEntity.getMatchType());
//                amazonSbAdNeKeyword.setState(negativeKeywordExtendEntity.getState().toLowerCase());
                amazonSbAdNeKeywords.add(amazonSbAdNeKeyword);
            }
        }

        advertiseAutoRuleExecuteRecord.setItemOperateId(message.getItemId());
        advertiseAutoRuleExecuteRecord.setItemOperateType("TARGET");
        advertiseAutoRuleExecuteRecord.setItemType("TARGET");
        advertiseAutoRuleExecuteRecord.setTargetType("keywordTarget");
    }

    private void fillRecordInfosForTarget(AdvertiseRuleTaskExecuteRecordV2Message message,
                                          AdvertiseAutoRuleExecuteRecord advertiseAutoRuleExecuteRecord,
                                          List<AmazonAdTargeting> amazonAdTargetings,
                                          List<AmazonSbAdNeTargeting> amazonSbAdNeTargetings,
                                          List<AmazonSdAdNeTargeting> amazonSdAdNeTargetings) {
        if ("sp".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
            AmazonAdTargeting amazonAdTargeting = amazonAdTargetDaoRoutingService.getByAdTargetId(message.getPuid(), message.getShopId(), message.getItemId());
            if ("auto".equals(amazonAdTargeting.getType())) {
                advertiseAutoRuleExecuteRecord.setItemName(AutoTargetTypeEnum.getAutoTargetValue(amazonAdTargeting.getTargetingValue()));
                advertiseAutoRuleExecuteRecord.setItemOperateName(AutoTargetTypeEnum.getAutoTargetValue(amazonAdTargeting.getTargetingValue()));
                advertiseAutoRuleExecuteRecord.setTargetType("autoTarget");
            } else {
                advertiseAutoRuleExecuteRecord.setItemName(amazonAdTargeting.getTargetingValue());
                advertiseAutoRuleExecuteRecord.setItemOperateName(amazonAdTargeting.getTargetingValue());
                advertiseAutoRuleExecuteRecord.setTargetType("productTarget");
            }
            NegativeTargetExtendEntity negativeTargetExtendEntity = message.getNegativeTargetExtendEntity();
            if (negativeTargetExtendEntity != null) {
                AmazonAdTargeting amazonAdTargeting1 = new AmazonAdTargeting();
                amazonAdTargeting1.setTargetId(negativeTargetExtendEntity.getTargetId());
                amazonAdTargeting1.setPuid(amazonAdTargeting.getPuid());
                amazonAdTargeting1.setShopId(amazonAdTargeting.getShopId());
                amazonAdTargeting1.setMarketplaceId(amazonAdTargeting.getMarketplaceId());
                amazonAdTargeting1.setAdGroupId(amazonAdTargeting.getAdGroupId());
                amazonAdTargeting1.setDxmGroupId(amazonAdTargeting.getId());
                amazonAdTargeting1.setCampaignId(amazonAdTargeting.getCampaignId());
                amazonAdTargeting1.setProfileId(amazonAdTargeting.getProfileId());
                amazonAdTargeting1.setExpressionType(Constants.MANUAL);
                amazonAdTargeting1.setState(negativeTargetExtendEntity.getState().toLowerCase());
                amazonAdTargeting1.setType(Constants.TARGETING_TYPE_NEGATIVEASIN);
                if (CollectionUtils.isNotEmpty(negativeTargetExtendEntity.getExpression())) {
                    List<Expression> expressions = new ArrayList<>();
                    List<NegativeTargetingExpression> negativeTargetingExpressions = negativeTargetExtendEntity.getExpression();
                    for (NegativeTargetingExpression negativeTargetingExpression : negativeTargetingExpressions) {
                        Expression expression = new Expression();
                        expression.setType(negativeTargetingExpression.getType());
                        expression.setValue(negativeTargetingExpression.getValue());
                        amazonAdTargeting1.setTargetingValue(negativeTargetingExpression.getValue());
                        expressions.add(expression);
                    }
                    amazonAdTargeting1.setExpression(JSONUtil.objectToJson(expressions));
                }
                if (CollectionUtils.isNotEmpty(negativeTargetExtendEntity.getResolvedExpression())) {
                    List<Expression> resolveExpressions = new ArrayList<>();
                    List<NegativeTargetingExpression> negativeTargetingExpressions1 = negativeTargetExtendEntity.getResolvedExpression();
                    for (NegativeTargetingExpression negativeTargetingExpression : negativeTargetingExpressions1) {
                        Expression expression = new Expression();
                        expression.setType(negativeTargetingExpression.getType());
                        expression.setValue(negativeTargetingExpression.getValue());
                        resolveExpressions.add(expression);
                    }
                    amazonAdTargeting1.setResolvedExpression(JSONUtil.objectToJson(resolveExpressions));
                }
                amazonAdTargetings.add(amazonAdTargeting1);
            }

            advertiseAutoRuleExecuteRecord.setCampaignId(amazonAdTargeting.getCampaignId());
            advertiseAutoRuleExecuteRecord.setAdGroupId(amazonAdTargeting.getAdGroupId());
        } else if ("sb".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
            AmazonSbAdTargeting amazonAdTargeting = amazonSbAdTargetingDao.getByTargetId(message.getPuid(), message.getShopId(), message.getItemId());
            advertiseAutoRuleExecuteRecord.setItemName(amazonAdTargeting.getTargetText());
            advertiseAutoRuleExecuteRecord.setItemOperateName(amazonAdTargeting.getTargetText());
            advertiseAutoRuleExecuteRecord.setTargetType("productTarget");

            NegativeTargetExtendEntity negativeTargetExtendEntity = message.getNegativeTargetExtendEntity();
            if (negativeTargetExtendEntity != null) {
                AmazonSbAdNeTargeting amazonAdTargeting1 = new AmazonSbAdNeTargeting();
                amazonAdTargeting1.setTargetId(negativeTargetExtendEntity.getTargetId());
                amazonAdTargeting1.setPuid(amazonAdTargeting.getPuid());
                amazonAdTargeting1.setShopId(amazonAdTargeting.getShopId());
                amazonAdTargeting1.setMarketplaceId(amazonAdTargeting.getMarketplaceId());
                amazonAdTargeting1.setAdGroupId(amazonAdTargeting.getAdGroupId());
                amazonSbAdNeTargetings.add(amazonAdTargeting1);
            }

            advertiseAutoRuleExecuteRecord.setCampaignId(amazonAdTargeting.getCampaignId());
            advertiseAutoRuleExecuteRecord.setAdGroupId(amazonAdTargeting.getAdGroupId());
        } else if ("sd".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
            AmazonSdAdTargeting amazonAdTargeting = amazonSdAdTargetingDao.getbyTargetId(message.getPuid(), message.getShopId(), message.getItemId());
//            AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByAdGroupId(message.getPuid(), message.getShopId(), amazonAdTargeting.getAdGroupId());
            // todo:与广告管理页面逻辑保持一致,后续新版sd上线后需要一起修改
            advertiseAutoRuleExecuteRecord.setItemName(amazonAdTargeting.getTargetText());
            advertiseAutoRuleExecuteRecord.setItemOperateName(amazonAdTargeting.getTargetText());
            advertiseAutoRuleExecuteRecord.setTargetType(AutoRuleTargetTypeEnum.productTarget.getTargetType());
            List<ExpressionNested> array = JSONUtil.jsonToArray(amazonAdTargeting.getExpression(), ExpressionNested.class);
            if (CollectionUtils.isNotEmpty(array)){
                // 设置为受众投放
                if (com.google.common.collect.Lists.newArrayList("views", "audience", "purchases", "contentCategorySameAs").contains(array.get(0).getType())){
                    advertiseAutoRuleExecuteRecord.setTargetType(AutoRuleTargetTypeEnum.audienceTarget.getTargetType());
                }
            }
            NegativeTargetExtendEntity negativeTargetExtendEntity = message.getNegativeTargetExtendEntity();
            if (negativeTargetExtendEntity != null) {
                AmazonSdAdNeTargeting amazonAdTargeting1 = new AmazonSdAdNeTargeting();
                amazonAdTargeting1.setTargetId(negativeTargetExtendEntity.getTargetId());
                amazonAdTargeting1.setPuid(amazonAdTargeting.getPuid());
                amazonAdTargeting1.setShopId(amazonAdTargeting.getShopId());
                amazonAdTargeting1.setMarketplaceId(amazonAdTargeting.getMarketplaceId());
                amazonAdTargeting1.setAdGroupId(amazonAdTargeting.getAdGroupId());
                amazonAdTargeting1.setCampaignId(amazonAdTargeting.getCampaignId());
                amazonAdTargeting1.setProfileId(amazonAdTargeting.getProfileId());
                amazonSdAdNeTargetings.add(amazonAdTargeting1);
            }

            advertiseAutoRuleExecuteRecord.setAdGroupId(amazonAdTargeting.getAdGroupId());
            AmazonSdAdGroup sdGroup = amazonSdAdGroupDao.getByAdGroupId(message.getPuid(), message.getShopId(), amazonAdTargeting.getAdGroupId());
            //受众投放表没有活动id，需要从组上查
            if (Objects.nonNull(sdGroup)) {
                advertiseAutoRuleExecuteRecord.setCampaignId(sdGroup.getCampaignId());
            }
        }

        advertiseAutoRuleExecuteRecord.setItemOperateId(message.getItemId());
        advertiseAutoRuleExecuteRecord.setItemOperateType("TARGET");
    }
}
