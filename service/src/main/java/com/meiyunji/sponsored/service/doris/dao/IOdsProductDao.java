package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.cpc.vo.RepeatTargetingProductVo;
import com.meiyunji.sponsored.service.cpc.vo.ReportVo;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.newDashboard.dto.CategoryNameDto;
import com.meiyunji.sponsored.service.post.request.GetSuggestAsinRequest;
import com.meiyunji.sponsored.service.post.response.AsinPageResponse;
import com.meiyunji.sponsored.service.post.vo.AsinProductVo;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * 在线产品表-主项目分库(OdsProduct)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:22
 */
public interface IOdsProductDao extends IDorisBaseDao<OdsProduct> {

    List<OdsProduct> getMainImgByAsinOrSku(int puid, List<Integer> shopIds, List<String> asin, List<String> skus);
    List<String> listByParentAsin(Integer puid, Integer shopId, List<String> parentAsin);

    List<String> listByParentAsin(Integer puid, String marketplaceId, List<Integer> shopIds, List<String> parentAsins);

    List<String> listByParentAsin(Integer puid, List<String> marketplaceId, List<Integer> shopIds, List<String> parentAsins);

    List<String> listByParentAsin(Integer puid, List<Integer> shopIds, List<String> parentAsins);

    List<OdsProduct> listByParentAsins(Integer puid, String marketplaceId, List<Integer> shopIds, List<String> parentAsins);

    List<OdsProduct> listByTrueParentAsin(Integer puid, String marketplaceId, List<Integer> shopIds, List<String> parentAsins);

    List<OdsProduct> listParentAsin(Integer puid, String marketplaceId, List<Integer> shopIds,
                                    List<String> skus, List<String> asin, List<String> parentAsins);

    List<String> listBySku(Integer puid, Integer shopId, List<String> sku);

    List<String> listBySkuAllShop(Integer puid, List<Integer> shopId, List<String> marketplaceId, List<String> sku);

    List<OdsProduct> listBySkuList(Integer puid, String marketplaceId, List<Integer> shopIds, List<String> sku);

    List<String> listAsinBySku(Integer puid, List<Integer> shopIds, List<String> sku);
    /**
     * 根据父Id 查找产品
     */
    List<OdsProduct> listByParentId(Integer puid, List<Integer> shopIds, List<Long> parentId);

    /**
     * 根据父Id 查找产品
     */
    List<OdsProduct> listParentAsinById(Integer puid, List<Integer> shopIds, List<Long> parentId);

    List<OdsProduct> listByAsin(Integer puid, Integer shopId, List<String> asin, String title);

    List<OdsProduct> listByAsinSku(Integer puid, String marketplaceId, List<Integer> shopIds, List<String> asinList, List<String> skuList);

    List<OdsProduct> listByAsins(Integer puid, String marketplaceId, List<Integer> shopIds, List<String> asinList);

    /**
     * 根据asin查询在线产品列表
     */
    List<OdsProduct> listProductByAsins(Integer puid, List<Integer> shopIds, List<String> asins);

    List<OdsProduct> listTitleAndImgByAsins(Integer puid, List<Integer> shopIds, List<String> asins);

    /**
     * 根据sku查询在线产品列表
     */
    List<OdsProduct> listProductBySkus(Integer puid, List<Integer> shopIds, List<String> skus);

    List<RepeatTargetingProductVo> getSpProductByGroupId(Integer puid, List<Integer> shopIdList, List<String> spGroupIdList);

    List<String> listByParentAsinMultiShop(Integer puid, List<Integer> shopIdList, List<String> productValue);

    List<String> listBySkuMultiShop(Integer puid, List<Integer> shopIdList, List<String> productValue);
    List<CategoryNameDto> getCategoryName(Integer puid, List<String> fullCid);

    List<CategoryNameDto> getCategoryByName(Integer puid, List<String> names);

    Integer getProductCommodityRelaNowMonthCount(Integer puid);

    List<String> listAsinBySearchParam(int puid, String marketplaceId, List<Integer> shopIdList,
                                       String searchType, String searchField, String searchValue, Boolean searchFlag);

    List<ReportVo> getRatingByAsinList(Integer puid, Integer shopId, String marketplaceId, HashSet<String> asins);

    List<ReportVo> getRatingByAsinList(Integer puid, Integer shopId, String marketplaceId, List<List<String>> batches);

    List<AsinProductVo> listProductInfoByAsins(Integer puid, List<Integer> shopId, String marketplaceId, List<String> asins);

    List<OdsProduct> getAsinParentId(Integer puid, GetSuggestAsinRequest req, List<String> asins);

    List<OdsProduct> getListByParentId(Integer puid, Integer shopId, String marketplaceId, ArrayList<Long> idList);

    Page<AsinPageResponse.AsinInfo> getPageList(Integer puid, GetSuggestAsinRequest req);

    List<OdsProduct> listVcParentAsinById(Integer puid, List<Integer> shopIds, List<Long> parentId);

    List<OdsProduct> listVcByParentAsin(Integer puid, List<Integer> shopIds, List<String> parentAsin);
}

