package com.meiyunji.sponsored.service.newDashboard.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdCampaignOrGroupOrPortfolioResponseVo;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardAdTypeReqVo;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardCampaignOrGroupOrPortfolioReqVo;

import java.util.List;

/**
 * @author: ys
 * @date: 2024/4/9 15:49
 * @describe:
 */
public interface IDashboardAdCampaignService {
    DashboardAdCampaignOrGroupOrPortfolioResponseVo.Page queryCampaignCharts(DashboardCampaignOrGroupOrPortfolioReqVo req);

    List<String> exportCampaignCharts(DashboardCampaignOrGroupOrPortfolioReqVo reqVo);

}
