package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.cpc.vo.AdProductPageVo;
import com.meiyunji.sponsored.service.cpc.vo.MultiShopGroupByProductParam;
import com.meiyunji.sponsored.service.cpc.dto.AdProductDetailDto;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdProduct;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AdGroupAndAdIdDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AsinCampaignNumDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AsinListDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AsinListReqVo;
import com.meiyunji.sponsored.service.wordFrequency.qo.GetAsinListQo;

import java.util.List;

/**
 * amazon广告产品表(OdsAmazonAdProduct)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:18
 */
public interface IOdsAmazonAdProductDao extends IDorisBaseDao<OdsAmazonAdProduct> {

    /**
     * 根据广告活动id、广告组id、广告产品运行状态查询adId
     */
    List<String> getAdIdByState(Integer puid, Integer shopId, String campaignIds, String groupIds, String states);

    /**
     * 根据广告活动id、广告组id、广告产品运行状态查询groupId
     */
    List<String> getGroupIdByState(Integer puid, Integer shopId, String campaignIds, String groupIds, String states, String asin, String sku);

    /**
     * 词频独立页-查询asin下拉列表
     */
    Page<AsinListDto> getAsinPage(Integer puid, GetAsinListQo qo);

    /**
     * 查询所有类型的sku
     */
    Page<AsinListDto> getAsinAllPage(Integer puid, AsinListReqVo qo, boolean isVc);

    /**
     * 父asin聚合
     */
    Page<AsinListDto> getParentAsinAllPageVc(Integer puid, AsinListReqVo qo, List<Integer> scShopIds, List<Integer> vcShopIds);

    Page<AsinListDto> getParentAsinAllPage(Integer puid, AsinListReqVo qo);

    Page<AsinListDto> getAdProductAllPage(Integer puid, AsinListReqVo qo);

    Page<AsinListDto> getAdParentAsinAllPage(Integer puid, AsinListReqVo qo);

    List<OdsAmazonAdProduct> listByShopAsin(Integer puid, List<Integer> shopIdList, List<String> asinList, List<String> adGroupIds);

    List<OdsAmazonAdProduct> listByShopParentAsin(Integer puid, List<Integer> shopIdList, List<String> parentAsinList);

    List<OdsProduct> getOdsProductByIds(Integer puid, String marketplaceId, List<Integer> shopIds, List<Long> idList);

    List<OdsProduct> listOdsProduct(Integer puid, String marketplaceId, List<Integer> shopIds,
                                    List<String> skuList, List<String> asinList, List<String> parentAsinList);

    List<OdsAmazonAdProduct> getGroupIdByProduct(MultiShopGroupByProductParam param);

    List<OdsAmazonAdProduct> getGroupIdByParentAsin(MultiShopGroupByProductParam param);

    List<AdProductPageVo> getProductClickData(MultiShopGroupByProductParam param, List<OdsAmazonAdProduct> productGroup);

    List<AdProductPageVo> getProductClickDataByParent(MultiShopGroupByProductParam param, List<OdsAmazonAdProduct> productGroup);
    /**
     * 根据广告组id集合获取广告产品列表 限制每组个数
     */
    int countByGroupIdList(Integer puid, Integer shopId, List<String> adGroupIds);
    List<AdProductDetailDto> listByGroupIdList(Integer puid, Integer shopId, List<String> adGroupIds);

    List<AdGroupAndAdIdDto> getAdIdAndAdGroupIdByAsin(int puid, List<Integer> shopIdList, List<String> asinList, List<String> mskuList, String campaignId, String groupId);

    List<AsinCampaignNumDto> getAsinCampaignNum(int puid, List<Integer> shopIdList, List<String> asinList);

    List<AdProductDetailDto> listProductsByGroupIdList(Integer puid, Integer targetShopId, List<String> adGroupIds , Integer limit);
}

