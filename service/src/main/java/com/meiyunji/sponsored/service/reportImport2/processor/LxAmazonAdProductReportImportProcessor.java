package com.meiyunji.sponsored.service.reportImport2.processor;

import com.alibaba.fastjson.JSONReader;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.util.GZipUtils;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.reportImport.model.LxAdProductReport;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdGroupReport;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdProductReport;
import com.meiyunji.sponsored.service.reportImport2.vo.AmazonAdReportImportMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * lx活动报告导入处理器
 *
 * <AUTHOR>
 * @date 2023/05/26
 */
@Service
@Slf4j
public class LxAmazonAdProductReportImportProcessor extends AbstractAmazonAdLxReportImportProcessor<LxAmazonAdProductReport> {


    private final IAmazonSdAdProductDao amazonSdAdProductDao;
    private final IAmazonAdProductDao amazonAdProductDao;
    private final IAmazonSbAdsDao amazonSbAdsDao;
    private final IAmazonAdProductReportDao amazonAdProductReportDao;
    private final IAmazonAdSdProductReportDao amazonAdSdProductReportDao;
    private final IAmazonAdSbAdsReportDao amazonAdSbAdsReportDao;


    protected LxAmazonAdProductReportImportProcessor(IAmazonAdCampaignAllDao amazonAdCampaignAllDao, IAmazonAdGroupDao amazonAdGroupDao,
                                                     IAmazonSdAdGroupDao amazonSdAdGroupDao, IAmazonSbAdGroupDao amazonSbAdGroupDao,
                                                     IScVcShopAuthDao shopAuthDao, CosBucketClient tempBucketClient,
                                                     IAmazonAdProductReportDao amazonAdProductReportDao,
                                                     IAmazonAdSdProductReportDao amazonAdSdProductReportDao,
                                                     IAmazonSdAdProductDao amazonSdAdProductDao,
                                                     IAmazonAdProductDao amazonAdProductDao,
                                                     IAmazonSbAdsDao amazonSbAdsDao,
                                                     IAmazonAdSbAdsReportDao amazonAdSbAdsReportDao) {
        super(amazonAdCampaignAllDao, amazonAdGroupDao, amazonSdAdGroupDao, amazonSbAdGroupDao, shopAuthDao, tempBucketClient);
        this.amazonAdProductReportDao = amazonAdProductReportDao;
        this.amazonAdSdProductReportDao = amazonAdSdProductReportDao;
        this.amazonSdAdProductDao = amazonSdAdProductDao;
        this.amazonAdProductDao = amazonAdProductDao;
        this.amazonSbAdsDao = amazonSbAdsDao;
        this.amazonAdSbAdsReportDao = amazonAdSbAdsReportDao;
    }


    @Override
    public void importReport(AmazonAdReportImportMessage importMessage) {

        //读取数据
        try (InputStreamReader inputStreamReader = new InputStreamReader(new ByteArrayInputStream(GZipUtils.release(tempBucketClient.getObjectToBytes(importMessage.getFileId().replaceFirst("/", "")))))) {
            ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(importMessage.getShopId(), importMessage.getPuid());
            JSONReader jsonReader = new JSONReader(inputStreamReader);
            jsonReader.startArray();
            List<LxAmazonAdProductReport> reports = Lists.newArrayListWithExpectedSize(500);
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                LxAmazonAdProductReport report = new LxAmazonAdProductReport();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.getImpressions() != 0) {
                    reports.add(report);
                    if (reports.size() >= 500) {
                        dealReport(importMessage, reports, shopAuth);
                        reports = Lists.newArrayListWithExpectedSize(500);
                    }
                }

            }
            jsonReader.endArray();
            jsonReader.close();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReport(importMessage, reports, shopAuth);
            }
        } catch (Exception e) {
            log.error("import campaign report error puid:{},shopId：{}，scheduleId :{} error:", importMessage.getPuid(), importMessage.getShopId(), importMessage.getScheduleId(), e);
        }

    }

    private void dealReport(AmazonAdReportImportMessage importMessage, List<LxAmazonAdProductReport> reports, ShopAuth shopAuth) {
        Integer puid = importMessage.getPuid();
        Integer shopId = importMessage.getShopId();

        String adType = importMessage.getAdType().toLowerCase();
        List<String> campaignIds = reports.stream().map(LxAmazonAdProductReport::getCampaignId).collect(Collectors.toList());
        List<AmazonAdCampaignAll> byCampaignIds = amazonAdCampaignAllDao.getByCampaignIds(puid, shopId, shopAuth.getMarketplaceId(), campaignIds, adType);
        List<String> adGroupIds = reports.stream().map(LxAmazonAdProductReport::getAdGroupId).collect(Collectors.toList());


        Map<String, AmazonAdGroup> spAdGroups = new HashMap<>();
        Map<String, AmazonSbAdGroup> sbAdGroups = new HashMap<>();
        Map<String, AmazonSdAdGroup> sdAdGroups = new HashMap<>();

        Map<String, AmazonAdProduct> spProduct = new HashMap<>();
        Map<String, AmazonSdAdProduct> sdProduct = new HashMap<>();
        Map<String, AmazonSbAds> sbProdut = new HashMap<>();


        if (CampaignTypeEnum.sp.name().equalsIgnoreCase(importMessage.getAdType())) {
            spAdGroups = amazonAdGroupDao.getAdGroupByIds(puid, shopId, adGroupIds).stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity(), (e1, e2) -> e2));

        } else if (CampaignTypeEnum.sb.name().equalsIgnoreCase(importMessage.getAdType())) {
            sbAdGroups = amazonSbAdGroupDao.getAdGroupByIds(puid, shopId, adGroupIds).stream().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, Function.identity(), (e1, e2) -> e2));
        } else if (CampaignTypeEnum.sd.name().equalsIgnoreCase(importMessage.getAdType())) {
            sdAdGroups = amazonSdAdGroupDao.getByGroupIds(puid, shopId, adGroupIds).stream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, Function.identity(), (e1, e2) -> e2));
        }

        if (CampaignTypeEnum.sp.name().equalsIgnoreCase(importMessage.getAdType())) {
            List<String> getAdIds = reports.stream().map(LxAmazonAdProductReport::getAdId).collect(Collectors.toList());
            spProduct = amazonAdProductDao.getByAdIds(puid, shopId, getAdIds).stream().collect(Collectors.toMap(AmazonAdProduct::getAdId, Function.identity(), (e1, e2) -> e2));

        } else if (CampaignTypeEnum.sb.name().equalsIgnoreCase(importMessage.getAdType())) {
            List<String> getAdIds = reports.stream().map(e -> {
                if (StringUtils.isNotBlank(e.getAdId())) {
                    return e.getAdId();
                } else {
                    return e.getAdGroupId();
                }
            }).collect(Collectors.toList());
            sbProdut = amazonSbAdsDao.getAdsByQueryIds(puid, shopId, getAdIds).stream().collect(Collectors.toMap(AmazonSbAds::getQueryId, Function.identity(), (e1, e2) -> e2));
        } else if (CampaignTypeEnum.sd.name().equalsIgnoreCase(importMessage.getAdType())) {
            List<String> getAdIds = reports.stream().map(LxAmazonAdProductReport::getAdId).collect(Collectors.toList());
            sdProduct = amazonSdAdProductDao.listByAdId(puid, shopId, getAdIds).stream().collect(Collectors.toMap(AmazonSdAdProduct::getAdId, Function.identity(), (e1, e2) -> e2));
        }

        Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = byCampaignIds.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));


        Map<String, AmazonAdGroup> finalSpAdGroups = spAdGroups;
        Map<String, AmazonSbAdGroup> finalSbAdGroups = sbAdGroups;
        Map<String, AmazonSdAdGroup> finalSdAdGroups = sdAdGroups;

        List<AmazonAdProductReport> spReports = new ArrayList<>();
        List<AmazonAdSbAdsReport> sbReports = new ArrayList<>();
        List<AmazonAdSdProductReport> sdReports = new ArrayList<>();
        Map<String, AmazonAdProduct> finalSpProduct = spProduct;
        Map<String, AmazonSbAds> finalSbProdut = sbProdut;
        reports.forEach(e -> {
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(e.getCampaignId());
            if (amazonAdCampaignAll == null) {
                log.error("pxq-report-import puid : {} shop_id : {} campaignId : {} 不存在", puid, shopId, e.getCampaignId());
                return;
            }


            if (CampaignTypeEnum.sp.name().equalsIgnoreCase(importMessage.getAdType())) {
                AmazonAdGroup amazonAdGroup = finalSpAdGroups.get(e.getAdGroupId());
                if (amazonAdGroup == null) {
                    log.error("pxq-report-import puid : {} shop_id : {} campaignId : {}, groupId:{} 不存在", puid, shopId, e.getCampaignId(), e.getAdGroupId());
                    return;
                }

                AmazonAdProduct amazonAdProduct = finalSpProduct.get(e.getAdId());
                if (amazonAdProduct == null) {
                    log.error("pxq-report-import puid : {} shop_id : {} campaignId : {}, groupId:{}, adId:{} 不存在", puid, shopId, e.getCampaignId(), e.getAdGroupId(), e.getAdId());
                    return;
                }
                AmazonAdProductReport spReport = buildSpAdProductReport(importMessage.getCountDate(), e, amazonAdGroup, amazonAdCampaignAll, shopAuth);
                spReports.add(spReport);
            } else if (CampaignTypeEnum.sb.name().equalsIgnoreCase(importMessage.getAdType())) {
                AmazonSbAdGroup amazonSbAdGroup = finalSbAdGroups.get(e.getAdGroupId());
                if (amazonSbAdGroup == null) {
                    log.error("pxq-report-import puid : {} shop_id : {} campaignId : {}, groupId:{} 不存在", puid, shopId, e.getCampaignId(), e.getAdGroupId());
                    return;
                }
                String adId = e.getAdGroupId();
                if (StringUtils.isNotBlank(e.getAdId())) {
                    adId =  e.getAdId();
                }
                AmazonSbAds amazonAdProduct = finalSbProdut.get(adId);
                if (amazonAdProduct == null) {
                    log.error("pxq-report-import puid : {} shop_id : {} campaignId : {}, groupId:{}, adId:{} 不存在", puid, shopId, e.getCampaignId(), e.getAdGroupId(), e.getAdId());
                    return;
                }
                AmazonAdSbAdsReport sbReport = buildSbAdProductReport(importMessage.getCountDate(), e, amazonSbAdGroup, amazonAdCampaignAll, shopAuth, amazonAdProduct);
                sbReports.add(sbReport);
            } else if (CampaignTypeEnum.sd.name().equalsIgnoreCase(importMessage.getAdType())) {
                AmazonSdAdGroup amazonSdAdGroup = finalSdAdGroups.get(e.getAdGroupId());
                if (amazonSdAdGroup == null) {
                    log.error("pxq-report-import puid : {} shop_id : {} campaignId : {}, groupId:{} 不存在", puid, shopId, e.getCampaignId(), e.getAdGroupId());
                    return;
                }
                AmazonAdSdProductReport sdReport = buildSdProductReport(importMessage.getCountDate(), e, amazonSdAdGroup, amazonAdCampaignAll, shopAuth);
                sdReports.add(sdReport);
            }
        });


        //持久数据到数据库
        if (CollectionUtils.isNotEmpty(spReports)) {
            amazonAdProductReportDao.insertList(puid, spReports);
        }

        if (CollectionUtils.isNotEmpty(sbReports)) {
            amazonAdSbAdsReportDao.insertOrUpdateList(puid, sbReports);
        }

        if (CollectionUtils.isNotEmpty(sdReports)) {
            amazonAdSdProductReportDao.insertOrUpdateList(puid, sdReports);
        }
    }


    /**
     * 构建sd组报告
     *
     * @param report          报告
     * @param amazonSdAdGroup 亚马逊sd广告组
     * @param campaignAll     活动所有
     * @return {@link AmazonAdSdProductReport}
     */
    private AmazonAdSdProductReport buildSdProductReport(String countDate, LxAmazonAdProductReport report, AmazonSdAdGroup amazonSdAdGroup, AmazonAdCampaignAll campaignAll, ShopAuth shopAuth) {
        AmazonAdSdProductReport sdProductReport = new AmazonAdSdProductReport();
        sdProductReport.setPuid(shopAuth.getPuid());
        sdProductReport.setShopId(shopAuth.getId());
        sdProductReport.setMarketplaceId(shopAuth.getMarketplaceId());
        sdProductReport.setCountDate(countDate);
        sdProductReport.setTacticType(campaignAll.getTactic());
        sdProductReport.setCurrency(Marketplace.fromId(shopAuth.getMarketplaceId()).getCurrencyCode().name());
        sdProductReport.setCampaignName(campaignAll.getName());
        sdProductReport.setCampaignId(campaignAll.getCampaignId());
        sdProductReport.setAdGroupName(report.getAdGroupName());
        sdProductReport.setAdGroupId(report.getAdGroupId());
        sdProductReport.setAsin(report.getAsin());
        sdProductReport.setSku(report.getSku());
        sdProductReport.setAdId(report.getAdId());

        sdProductReport.setSales14d(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        sdProductReport.setSales14dSameSKU(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);
        sdProductReport.setUnitsOrdered14d(report.getAdUnits() != null ? report.getAdUnits() : 0);
        sdProductReport.setConversions14d(report.getOrders() != null ? report.getOrders() : 0);
        sdProductReport.setConversions14dSameSKU(report.getDirectOrders() != null ? report.getDirectOrders() : 0);
        sdProductReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        sdProductReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        sdProductReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
        sdProductReport.setViewImpressions(report.getViewImpressions() != null ? report.getViewImpressions() : 0);
        sdProductReport.setCostType(campaignAll.getCostType());
        return sdProductReport;
    }

    /**
     * 构建sb广告组报告
     *
     * @param report          报告
     * @param amazonSbAdGroup 亚马逊sb广告组
     * @return {@link AmazonAdSbAdsReport}
     */
    private AmazonAdSbAdsReport buildSbAdProductReport(String countDate, LxAmazonAdProductReport report, AmazonSbAdGroup amazonSbAdGroup, AmazonAdCampaignAll campaignAll, ShopAuth shopAuth, AmazonSbAds amazonSbAds) {
        AmazonAdSbAdsReport sbAdsReport = new AmazonAdSbAdsReport();
        sbAdsReport.setPuid(shopAuth.getPuid());
        sbAdsReport.setShopId(shopAuth.getId());
        sbAdsReport.setMarketplaceId(shopAuth.getMarketplaceId());
        sbAdsReport.setCountDate(countDate);
        sbAdsReport.setCurrency(Marketplace.fromId(shopAuth.getMarketplaceId()).getCurrencyCode().name());
        sbAdsReport.setAdFormat(amazonSbAds.getAdFormat());
        sbAdsReport.setQueryId(amazonSbAds.getQueryId());
        sbAdsReport.setAdId(report.getAdId() == null ? "" : report.getAdId());
        sbAdsReport.setCampaignName(campaignAll.getName());
        sbAdsReport.setCampaignId(report.getCampaignId());
        sbAdsReport.setAdGroupName(amazonSbAdGroup.getName());
        sbAdsReport.setAdGroupId(amazonSbAdGroup.getAdGroupId());


        sbAdsReport.setSales14d(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        sbAdsReport.setSales14dSameSKU(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);
        sbAdsReport.setConversions14d(report.getOrders() != null ? report.getOrders() : 0);
        sbAdsReport.setConversions14dSameSKU(report.getDirectOrders() != null ? report.getDirectOrders() : 0);

        sbAdsReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        sbAdsReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        sbAdsReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);

        sbAdsReport.setUnitsSold14d(report.getAdUnits() != null ? report.getAdUnits() : 0);

        //未定义
//        sbGroupReport.setDpv14d(isDxmNumeric(report.getDpv()) ? Integer.valueOf(report.getDpv()) : 0);
//
//        try {
//            sbGroupReport.setVideo5SecondViewRate(new BigDecimal(report.getVideo5SecondViewRate().replaceAll("%", "")).doubleValue());
//        } catch (Exception exception) {
//            log.error("buildSbAdGroupReport execute setVideo5SecondViewRate errorMsg: {}", exception.getMessage());
//        }
//        sbGroupReport.setVideo5SecondViews(isDxmNumeric(report.getVideo5SecondViews()) ? Integer.valueOf(report.getVideo5SecondViews()) : 0);
//        sbGroupReport.setVideoFirstQuartileViews(isDxmNumeric(report.getVideoFirstQuartileViews()) ? Integer.valueOf(report.getVideoFirstQuartileViews()) : 0);
//        sbGroupReport.setVideoMidpointViews(isDxmNumeric(report.getVideoMidpointViews()) ? Integer.valueOf(report.getVideoMidpointViews()) : 0);
//        sbGroupReport.setVideoThirdQuartileViews(isDxmNumeric(report.getVideoThirdQuartileViews()) ? Integer.valueOf(report.getVideoThirdQuartileViews()) : 0);
//        sbGroupReport.setVideoUnmutes(isDxmNumeric(report.getVideoUnmutes()) ? Integer.valueOf(report.getVideoUnmutes()) : 0);
//        sbGroupReport.setViewableImpressions(isDxmNumeric(report.getViewImpressions()) ? Integer.valueOf(report.getViewImpressions()) : null);
//        sbGroupReport.setVideoCompleteViews(isDxmNumeric(report.getVideoCompleteViews()) ? Integer.valueOf(report.getVideoCompleteViews()) : 0);
//
//
//        try {
//            sbGroupReport.setVctr(new BigDecimal(report.getVctr().replaceAll("%", "")).doubleValue());
//        } catch (Exception exception) {
//            log.error("buildSbAdGroupReport execute setVctr errorMsg: {}", exception.getMessage());
//        }
//
//        try {
//            sbGroupReport.setVtr(new BigDecimal(report.getVtr().replaceAll("%", "")).doubleValue());
//        } catch (Exception exception) {
//            log.error("buildSbAdGroupReport execute setVtr errorMsg: {}", exception.getMessage());
//        }

        return sbAdsReport;
    }

    /**
     * 构建sp广告组报告
     *
     * @param report 报告
     * @return {@link AmazonAdProductReport}
     */
    private AmazonAdProductReport buildSpAdProductReport(String countDate, LxAmazonAdProductReport report, AmazonAdGroup amazonSpAdGroup, AmazonAdCampaignAll campaignAll, ShopAuth shopAuth) {
        AmazonAdProductReport amazonAdProductReport = new AmazonAdProductReport();
        amazonAdProductReport.setCountDate(countDate);
        amazonAdProductReport.setPuid(shopAuth.getPuid());
        amazonAdProductReport.setShopId(shopAuth.getId());
        amazonAdProductReport.setMarketplaceId(shopAuth.getMarketplaceId());
        amazonAdProductReport.setAdId(report.getAdId());
        amazonAdProductReport.setCampaignId(report.getCampaignId());
        amazonAdProductReport.setAdGroupId(report.getAdGroupId());
        amazonAdProductReport.setAsin(report.getAsin());
        amazonAdProductReport.setSku(report.getSku());
        amazonAdProductReport.setAdGroupName(report.getAdGroupName());
        amazonAdProductReport.setCampaignName(report.getCampaignName());
        amazonAdProductReport.setTotalSales(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        amazonAdProductReport.setAdSales(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);
        amazonAdProductReport.setOrderNum(report.getAdUnits() != null ? report.getAdUnits() : 0);
        amazonAdProductReport.setAdOrderNum(report.getDirectUnits() != null ? report.getDirectUnits() : 0);
        amazonAdProductReport.setSaleNum(report.getOrders() != null ? report.getOrders() : 0);
        amazonAdProductReport.setAdSaleNum(report.getDirectOrders() != null ? report.getDirectOrders() : 0);

        amazonAdProductReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        amazonAdProductReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        amazonAdProductReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);

        return amazonAdProductReport;
    }


}
