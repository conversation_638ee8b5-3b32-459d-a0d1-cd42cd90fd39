package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignNeKeywordsDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignNeKeywords;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.CampaignNeKeywordsPageParam;
import com.meiyunji.sponsored.service.cpc.vo.KeywordLibsDetailVo;
import com.meiyunji.sponsored.service.cpc.vo.KeywordLibsPageParam;
import com.meiyunji.sponsored.service.cpc.vo.KeywordLibsVo;
import com.meiyunji.sponsored.service.negative.request.NegativeArchiveRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * Created by DXM_0123 on 2021/4/17.
 */
@Repository
public class AmazonAdCampaignNeKeywordsDaoImpl extends BaseShardingDaoImpl<AmazonAdCampaignNeKeywords> implements IAmazonAdCampaignNeKeywordsDao {

    @Override
    public void insertOnDuplicateKeyUpdate(Integer puid, List<AmazonAdCampaignNeKeywords> amazonAdKeywords) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_ad_campaign_nekeywords` (`puid`,`shop_id`,`marketplace_id`,")
                .append("`profile_id`,`campaign_id`,`keyword_id`,`keyword_text`,`match_type`,")
                .append("`state`,`serving_status`,`create_id`,`update_id`,`creation_date`,`create_time`,`update_time` ) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdCampaignNeKeywords amazonAdKeyword : amazonAdKeywords) {
            sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(amazonAdKeyword.getPuid());
            argsList.add(amazonAdKeyword.getShopId());
            argsList.add(amazonAdKeyword.getMarketplaceId());
            argsList.add(amazonAdKeyword.getProfileId());
            argsList.add(amazonAdKeyword.getCampaignId());
            argsList.add(amazonAdKeyword.getKeywordId());
            argsList.add(amazonAdKeyword.getKeywordText());
            argsList.add(amazonAdKeyword.getMatchType());
            argsList.add(amazonAdKeyword.getState());
            argsList.add(amazonAdKeyword.getServingStatus());
            argsList.add(amazonAdKeyword.getCreateId());
            argsList.add(amazonAdKeyword.getUpdateId());
            argsList.add(amazonAdKeyword.getCreationDate());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `state`=values(state),`serving_status`=values(serving_status),`creation_date`=values(creation_date) ");
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public Page<AmazonAdCampaignNeKeywords> pageList(Integer puid, CampaignNeKeywordsPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId());

        if (StringUtils.isNotBlank(param.getCampaignId())) {
            builder.inStrList("campaign_id", StringUtil.splitStr(param.getCampaignId()).toArray(new String[]{}));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            builder.inStrList("campaign_id",param.getCampaignIdList().toArray(new String[]{}));
        }

        if (StringUtils.isNotBlank(param.getState())) {
            builder.equalTo("state", param.getState());
        }

        if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
            if ("blur".equals(param.getSearchType())) { //模糊搜索
                builder.like("keyword_text", "%" + param.getSearchValue().trim() + "%");
            } else {//默认精确
                if (param.getListSearchValue().size() > 1) {
                    builder.inStrList("keyword_text", param.getListSearchValue().toArray(new String[]{}));
                } else {
                    builder.equalTo("keyword_text", param.getSearchValue().trim());
                }
            }
        }

        if (StringUtils.isNotBlank(param.getMatchType())) {
            builder.equalTo("match_type", param.getMatchType());
        }

        String orderBySql = " order by ifnull(creation_date, create_time) desc,id desc";

        return page(puid, param.getPageNo(), param.getPageSize(), orderBySql, builder.build());
    }

    @Override
    public List<AmazonAdCampaignNeKeywords> ListByCampaignId(Integer puid, Integer shopId, String campaignId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid)
            .equalTo("shop_id", shopId)
            .equalTo("campaign_id", campaignId)
            .equalTo("state", "enabled");
        return listByCondition(puid, builder.build());
    }

    @Override
    public boolean exist(Integer puid, String campaignId, String keywordText, String matchType) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
            .equalTo("puid", puid)
            .equalTo("campaign_id", campaignId)
            .in("state", new Object[] {CpcStatusEnum.enabled.name()})
            .equalTo("keyword_text", keywordText)
            .equalTo("match_type", matchType);
        Integer count = getCountByCondition(puid, builder.build());
        return count > 0;
    }

    @Override
    public List<String> getArchivedItems(Integer puid, Integer shopId) {
        String sql = "select keyword_id from t_amazon_ad_campaign_nekeywords where ";
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id",shopId)
                .equalTo("state", "archived")
                .build();
        sql += conditionBuilder.getSql();
        return getJdbcTemplate(puid).queryForList(sql, String.class, conditionBuilder.getValues());
    }

    @Override
    public List<String> getUpdateAfterReportSyncTimeItems(Integer puid, Integer shopId, LocalDateTime syncAt) {
        String sql = "select keyword_id from t_amazon_ad_campaign_nekeywords where ";
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id",shopId)
                .greaterThan("update_time", syncAt)
                .build();
        sql += conditionBuilder.getSql();
        return getJdbcTemplate(puid).queryForList(sql, String.class, conditionBuilder.getValues());
    }

    @Override
    public void batchUpdateArchive(Integer puid, List<AmazonAdCampaignNeKeywords> amazonAdKeywords) {
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_campaign_nekeywords` set `state`=?,")
                .append("`update_time`= now(),update_id=? where puid=? and shop_id=? and campaign_id=? and keyword_id=?");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonAdCampaignNeKeywords amazonAdKeyword : amazonAdKeywords) {
            batchArg = new Object[]{
                    amazonAdKeyword.getState(),
                    amazonAdKeyword.getUpdateId(),
                    amazonAdKeyword.getPuid(),
                    amazonAdKeyword.getShopId(),
                    amazonAdKeyword.getCampaignId(),
                    amazonAdKeyword.getKeywordId()
            };
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate(sql.toString(), batchArgs);
    }

    /**
     * TODO 关联关键词库查询
     * @param puid
     * @param keywordTextList
     * @return
     */
    @Override
    public List<KeywordLibsDetailVo> aggregateNeKeyword(Integer puid, List<String> keywordTextList) {

        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select `puid`, `shop_id`, `marketplace_id`, `keyword_id`, `keyword_text`, `campaign_id`, `match_type`," +
                " `state` from t_amazon_ad_campaign_nekeywords where puid = ? ");
        args.add(puid);

        if (CollectionUtils.isNotEmpty(keywordTextList)) {
            sql.append(SqlStringUtil.dealInList("keyword_text", keywordTextList, args));
        }

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<KeywordLibsDetailVo>() {
            @Override
            public KeywordLibsDetailVo mapRow(ResultSet res, int i) throws SQLException {
                return KeywordLibsDetailVo.builder()
                        .puid(res.getInt("puid"))
                        .shopId(res.getInt("shop_id"))
                        .marketplaceId(res.getString("marketplace_id"))
                        .keywordId(res.getString("keyword_id"))
                        .keywordText(res.getString("keyword_text"))
                        .campaignId(res.getString("campaign_id"))
                        .matchType(res.getString("match_type"))
                        .state(res.getString("state"))
                        .type("sp")
                        .targetType("negative")
                        .build();
            }
        }, args.toArray());
    }

    @Override
    public List<KeywordLibsVo> getCampaignNeKeywordCount(Integer puid, List<String> shopIds, KeywordLibsPageParam param, List<String> keywordTexts) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select keyword_text keywordText, count(*) negateTargetNum from t_amazon_ad_campaign_nekeywords " +
                "where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)){
            sql.append(SqlStringUtil.dealInList("shop_id",shopIds,argsList));
        }
        //通过来源站点筛选
        if(CollectionUtils.isNotEmpty(param.getCountry())){
            sql.append(SqlStringUtil.dealInList("marketplace_id", param.getCountry(), argsList));
        }
        if (CollectionUtils.isNotEmpty(keywordTexts)){
            sql.append(SqlStringUtil.dealInList("keyword_text",keywordTexts,argsList));
        }
        sql.append(" group by keywordText");
        return getJdbcTemplate(puid).query(sql.toString(), new ObjectMapper<>(KeywordLibsVo.class), argsList.toArray());
    }


    @Override
    public List<AmazonAdCampaignNeKeywords> listByKeywordId(Integer puid, Integer shopId, List<String> keywordIds) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("keyword_id", keywordIds.toArray(new String[0]));
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdCampaignNeKeywords> getCampaignNeKeywordsByCondition(Integer puid, Integer shopId,
                                                                             Set<String> campaignIds, Set<String> keywordTexts,
                                                                             Set<String> matchTypes) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("campaign_id", campaignIds.toArray())
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()})
                .in("keyword_text", keywordTexts.toArray())
                .in("match_type", matchTypes.toArray());
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdCampaignNeKeywords> listByKeywordText(Integer puid, Integer shopId, List<NegativeArchiveRequest.NegativeInfo> infoList) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select id, keyword_id, keyword_text, campaign_id, match_type," +
                " state from t_amazon_ad_campaign_nekeywords where state != 'archived' and puid = ? and shop_id = ? ");
        args.add(puid);
        args.add(shopId);
        sql.append(SqlStringUtil.dealMultiColumnIn(Lists.newArrayList("campaign_id", "keyword_text COLLATE utf8mb4_bin"), infoList,
                Lists.newArrayList(
                        NegativeArchiveRequest.NegativeInfo::getCampaignId,
                        NegativeArchiveRequest.NegativeInfo::getNegativeText
                ),
                args));
        return getJdbcTemplate(puid).query(sql.toString(), new ObjectMapper<>(AmazonAdCampaignNeKeywords.class), args.toArray());
    }
}
