package com.meiyunji.sponsored.service.multiple.targets.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdTargetingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdTargeting;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdTargetingSbDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdTargetingSb;
import com.meiyunji.sponsored.service.multiple.targets.dto.GroupInfo;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetDataDto;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetInfo;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetReqDto;
import com.meiyunji.sponsored.service.multiple.targets.enums.TargetSbOrderByEnum;
import com.meiyunji.sponsored.service.multiple.targets.resp.TargetResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * sp关键词投放子类
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Slf4j
@Service
public class SbTargetProcessor extends AbstractTargetProcessor{

    @Resource
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;

    @Resource
    private IOdsAmazonAdTargetingSbDao odsAmazonAdTargetingSbDao;

    @Resource
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;

    @Override
    public void abstractCheckParam(TargetReqDto req) {

    }

    @Override
    public void abstractSetParam(TargetReqDto req) {

    }

    @Override
    public Boolean abstractFilterTargetIds(TargetReqDto req) {

        return false;
    }

    @Override
    public void abstractBuildWhereSql(TargetReqDto req, StringBuilder whereSql, List<Object> argsList) {
        // 定位类型筛选
        if (StringUtils.isNotBlank(req.getProductTargetType())) {
            if ("asin".equalsIgnoreCase(req.getProductTargetType())) {
                whereSql.append(" and t.type='asin' ");
            } else if ("category".equalsIgnoreCase(req.getProductTargetType())) {
                whereSql.append(" and t.type='category'  ");
            }
        }
        // 搜索词搜索
        if (StringUtils.isNotBlank(req.getSearchField()) && StringUtils.isNotBlank(req.getSearchValue())) {  //搜索值
            if ("asin".equalsIgnoreCase(req.getSearchField())) {
                whereSql.append(" and lower(t.target_text) = ? and t.type = 'asin' ");
                argsList.add(req.getSearchValue().toLowerCase());
            } else if ("category".equalsIgnoreCase(req.getSearchField())) {
                whereSql.append(" and lower(t.target_text) like ? and t.type = 'category' ");
                argsList.add("%" + req.getSearchValue().toLowerCase() + "%");
            }
        }
    }

    @Override
    public void abstractPrepareData(TargetReqDto req, Boolean export, TargetDataDto dto) {
        if (!export) {
            // 获取asin信息填充asin、图片信息
            dto.setAsinMap(getAsinMap(req, CollectionUtil.newArrayList(dto.getTargetMap().values())));
        }
    }

    @Override
    public void abstractBuildParam(TargetResp row, TargetDataDto dto, TargetReqDto req) {
    }

    @Override
    public List<TargetInfo> abstractMysqlTargetList(TargetReqDto req) {
        List<TargetInfo> targetList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(req.getTargetIds())){
            // 分批获取
            List<List<String>> targetIdsList = Lists.partition(req.getTargetIds(), 10000);
            for (List<String> targetIds : targetIdsList) {
                List<AmazonSbAdTargeting> targetingList = amazonSbAdTargetingDao.listByShopIdsAndTargetIds(req.getPuid(), req.getShopIdList(), targetIds);
                for (AmazonSbAdTargeting targeting : targetingList) {
                    TargetInfo targetInfo = BeanUtil.copyProperties(targeting, TargetInfo.class);
                    targetInfo.setProductTargetType(targeting.getType());
                    targetList.add(targetInfo);
                }
            }
        }
        return targetList;
    }

    @Override
    public List<TargetInfo> abstractDorisTargetList(TargetReqDto req) {
        List<TargetInfo> targetList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(req.getTargetIds())){
            // 分批获取
            List<List<String>> targetIdsList = Lists.partition(req.getTargetIds(), 9000);
            for (List<String> targetIds : targetIdsList) {
                List<OdsAmazonAdTargetingSb> targetingList = odsAmazonAdTargetingSbDao.getByTargetingIds(req.getPuid(), req.getShopIdList(), targetIds);
                for (OdsAmazonAdTargetingSb targeting : targetingList) {
                    TargetInfo targetInfo = BeanUtil.copyProperties(targeting, TargetInfo.class);
                    targetInfo.setProductTargetType(targeting.getType());
                    targetList.add(targetInfo);
                }
            }
        }
        return targetList;
    }

    @Override
    public List<GroupInfo> abstractGroupInfoList(TargetReqDto req, List<String> adGroupIdList) {
        List<GroupInfo> groupInfoList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(adGroupIdList)){
            // 分批获取
            List<List<String>> adGroupIdsList = Lists.partition(adGroupIdList, 10000);
            for (List<String> adGroupIds : adGroupIdsList) {
                List<AmazonSbAdGroup> adGroupInfoList = amazonSbAdGroupDao.getListByShopIdsAndGroupIds(req.getPuid(), req.getShopIdList(), adGroupIds);
                for (AmazonSbAdGroup adGroup : adGroupInfoList) {
                    GroupInfo groupInfo = BeanUtil.copyProperties(adGroup, GroupInfo.class);
                    groupInfo.setDefaultBid(adGroup.getBid());
                    groupInfoList.add(groupInfo);
                }
            }
        }
        return groupInfoList;
    }

    @Override
    public List<String> excludeFiledList(TargetReqDto req) {
        return Lists.newArrayList("servingStatusName","selectType", "vcpm", "adSelfSaleNum", "adOtherSaleNum", "newToBrandDetailPageViews", "addToCart", "addToCartRate", "ecpAddToCart", "detailPageViews",
                "keywordText","matchType", "keywordTextCn", "searchFrequencyRank", "weekRatio");
    }

}
