package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdNeTargeting;
import com.meiyunji.sponsored.service.cpc.vo.AdGroupTargetCountDto;
import com.meiyunji.sponsored.service.cpc.vo.SbTargetingPageParam;

import java.util.List;

/**
 * Created by lm on 2021/7/29.
 */
public interface IAmazonSbAdNeTargetingDao extends IBaseShardingDao<AmazonSbAdNeTargeting> {

    void insertOrUpdate(Integer puid, List<AmazonSbAdNeTargeting> list);

    void batchAdd(int puid, List<AmazonSbAdNeTargeting> list);

    void batchUpdate(int puid, List<AmazonSbAdNeTargeting> list);

    List<AmazonSbAdNeTargeting> listByTargetId(int puid, int shopId, List<String> targetIds);

    List<AmazonSbAdNeTargeting> listByTargetId(int puid, List<Integer> shopIds, List<String> targetIds);

    Page<AmazonSbAdNeTargeting> pageList(int puid, SbTargetingPageParam param);

    AmazonSbAdNeTargeting getByTargetId(Integer puid, Integer shopId, String targetId);

    List<AdGroupTargetCountDto> countByGroupIdList(Integer puid, Integer shopId, List<String> groupIdList);

    List<AmazonSbAdNeTargeting> getListTargetByQuery(Integer puid, Integer shopId, String adGroupId, String targetValue, String type);
}
