package com.meiyunji.sponsored.service.account.dao;

import com.meiyunji.amazon.sellerpartner.base.RegionEnum;
import com.meiyunji.sponsored.common.springjdbc.ISlaveBaseDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.ShopByPuidDto;

import java.util.List;

/**
 * ShopAuth
 *
 * <AUTHOR>
 */
public interface ISlaveVcShopAuthDao extends ISlaveBaseDao<VcShopAuth> {

    /**
     * 批量获取店铺信息
     *
     * @param puid puid
     * @param shopIds VC店铺列表
     * @return List
     */
    List<VcShopAuth> listAllByIds(Integer puid, List<Integer> shopIds);

    /**
     * 获取puid 所有sellerId
     */
    List<String> getAllSellerId(Integer puid);

    /**
     * 根据sellerId 获取店铺信息
     */
    List<VcShopAuth> listBySellerId(String sellerId);



    /**
     * 通过puid获取所有vc店铺id
     */
    List<Integer> getAllIdByPuid(Integer puid);



    /**
     * 获取所有vc用户
     * @return
     */
    List<Integer> getAllVCPuid();

    /**
     * 根据puid和id获取Vc店铺信息
     * @param puid
     * @param vcShopIds
     * @return
     */
    List<VcShopAuth> listByPuidAndShopIds(Integer puid, List<Integer> vcShopIds);



    /**
     * 根据puid获取所有vc店铺id
     * @param puid
     * @return
     */
    List<Integer> getAllShopId(Integer puid);



    List<VcShopAuth> listWaitAdAuthBySeller(int puid, String sellingPartnerId, String region);

    List<Integer> getShopByMid(int puid, String marketplaceId);

}