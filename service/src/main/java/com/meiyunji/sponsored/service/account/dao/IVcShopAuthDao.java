package com.meiyunji.sponsored.service.account.dao;

import com.meiyunji.amazon.sellerpartner.base.RegionEnum;
import com.meiyunji.sponsored.common.springjdbc.IBaseDao;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;

import java.util.List;

/**
 * VcShopAuth
 * <AUTHOR>
 */
public interface IVcShopAuthDao extends IBaseDao<VcShopAuth> {

    /**
     * 批量获取店铺信息
     *
     * @param puid puid
     * @param shopIds VC店铺列表
     * @return List
     */
    List<VcShopAuth> listAllByIds(Integer puid, List<Integer> shopIds);


    /**
     * 复制要删除店铺数据到备份表
     * @param shopId shop id
     * @return int
     */
    int copyToDeleteShop(int shopId);

    /**
     * 获取puid 所有sellerId
     */
    List<String> getAllSellerId(Integer puid);

    /**
     * 根据sellerId 获取店铺信息
     */
    List<VcShopAuth> listBySellerId(String sellerId);



    /**
     * 更新token
     */
    int updateAccessToken(Integer puid, String sellingPartnerId, RegionEnum region, String accessToken);

    /**
     * 通过puid获取所有vc店铺id
     */
    List<Integer> getAllIdByPuid(Integer puid);

    /**
     * 根据id保存店铺
     */
    void save(VcShopAuth shopAuth, Integer id);

    /**
     * 获取所有vc用户
     * @return
     */
    List<Integer> getAllVCPuid();

    /**
     * 根据puid和id获取Vc店铺信息
     * @param puid
     * @param vcShopIds
     * @return
     */
    List<VcShopAuth> listByPuidAndShopIds(Integer puid, List<Integer> vcShopIds);



    /**
     * 根据puid获取所有vc店铺id
     * @param puid
     * @return
     */
    List<Integer> getAllShopId(Integer puid);

    int cleanAdToken(int puid, Integer shopId);

    List<VcShopAuth> listWaitAdAuthBySeller(int puid, String sellingPartnerId, String region);


    int clearAdAuth(Integer puid, Integer id);

    int updateAdAccessToken(Integer puid, String sellingPartnerId, String region, String accessToken);

    List<Integer> getShopByMid(int puid, String marketplaceId);

    int updateAdStatus(Integer puid, Integer shopId, String adStatus);
}