package com.meiyunji.sponsored.service.cpc.service2.sp.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.amazon.advertising.spV3.enumeration.SpV3StateEnum;
import com.amazon.advertising.spV3.group.CreateSpGroupV3Response;
import com.amazon.advertising.spV3.group.GroupSpV3Client;
import com.amazon.advertising.spV3.group.UpdateSpGroupV3Response;
import com.amazon.advertising.spV3.group.entity.GroupEntityV3;
import com.amazon.advertising.spV3.group.entity.GroupSuccessResultV3;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.google.protobuf.Int64Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.permission.annotation.AdProductPermissionFilter;
import com.meiyunji.sponsored.common.permission.enums.PermissionFilterStrategy;
import com.meiyunji.sponsored.common.permission.enums.PermissionFilterType;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.grpc.common.AdHourReportRequest;
import com.meiyunji.sponsored.rpc.adAggregateHour.AdPageBasicData;
import com.meiyunji.sponsored.rpc.adCommon.*;
import com.meiyunji.sponsored.rpc.vo.AdTagVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.adGroup.AdGroupDefaultOrderEnum;
import com.meiyunji.sponsored.service.adProductRight.service.IAdProductRightService;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDao;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.bo.AllGroupOrderBo;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdGroupStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.AdGroupReportHourlyDTO;
import com.meiyunji.sponsored.service.cpc.dto.AdProductDetailDto;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdProductMetadataService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.impl.AdChartDataProcess;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdGroupService;
import com.meiyunji.sponsored.service.cpc.util.AdPageAdvancedSearchUtils;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdGroupReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdGroup;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdProduct;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.reportHour.vo.AdReportHourlyVO;
import com.meiyunji.sponsored.service.util.AdPageUtil;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import com.meiyunji.sponsored.service.util.WxNotificationUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.cpc.util.Constants.CPC_BATCH_UPDATE_STATUS;
import static com.meiyunji.sponsored.service.cpc.util.Constants.EXPORT_MAX_SIZE;
import static com.meiyunji.sponsored.service.util.WxNotificationUtil.CREATE_SP_ADS_WX_URL;

/**
 * Created by xp on 2021/3/30.
 * 实现类
 */
@Service
@Slf4j
public class CpcAdGroupServiceImpl implements ICpcAdGroupService {

    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignDao;
    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Autowired
    private IAmazonSdAdProductDao amazonSdAdProductDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IUserDao userDao;
    @Autowired
    private IAmazonAdGroupReportDao amazonAdGroupReportDao;
    @Autowired
    private CpcCommService cpcCommService;
    @Autowired
    private CpcShopDataService cpCShopDataService;
    @Autowired
    private CpcAdGroupApiService cpcAdGroupApiService;
    @Autowired
    private AdChartDataProcess adChartDataProcess;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private IAmazonSdAdCampaignDao amazonSdAdCampaignDao;
    @Autowired
    private IAmazonAdSdGroupReportDao amazonAdSdGroupReportDao;
    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private IAdManageOperationLogService operationAdGroupLogService;
    @Autowired
    private IAdMarkupTagDao adMarkupTagDao;
    @Autowired
    private IAdTagDao adTagDao;
    @Autowired
    private IAmazonAdSbGroupReportDao amazonAdSbGroupReportDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private CpcPageIdsHandler cpcPageIdsHandler;
    @Autowired
    private RedisService redisService;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;
    @Autowired
    private IAmazonSbAdCampaignDao sbAdCampaignDao;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Resource
    private IAmazonAdGroupDorisDao amazonAdGroupDorisDao;
    @Autowired
    private IOdsAmazonAdGroupReportDao odsAmazonAdGroupReportDao;
    @Autowired
    private IDorisService dorisService;
    @Resource
    private IOdsAmazonAdProductDao odsAmazonAdProductDao;
    @Resource
    private IOdsProductDao odsProductDao;
    @Resource
    private IAmazonAdProductMetadataService amazonAdProductMetadataService;
    @Autowired
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;

    @Autowired
    private IAdProductRightService adProductRightService;

    @Override
    public AllGroupDataResponse.GroupHomeVo getNewAllGroupData(Integer puid, GroupPageParam param) {
        //分页处理
        Page<GroupPageVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());
        ShopAuth shopAuth = getShopAndCheckParam(param);
        AllGroupDataResponse.GroupHomeVo.Page.Builder pageBuilder = AllGroupDataResponse.GroupHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        if(param.getAdTagId() != null){
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.GROUP.getType(), param.getAdTagId(), param.getType(),null);
            if (CollectionUtils.isNotEmpty(relationIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                param.setGroupIds(relationIds);
            } else {
                return AllGroupDataResponse.GroupHomeVo.newBuilder()
                        .setPage(pageBuilder.build())
                        .build();
            }
        }
        if(CollectionUtils.isNotEmpty(param.getAdTagIdList())){
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.GROUP.getType(), param.getAdTagIdList(), param.getType(),null);
            if (CollectionUtils.isNotEmpty(relationIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                param.setGroupIds(relationIds);
            } else {
                return AllGroupDataResponse.GroupHomeVo.newBuilder()
                        .setPage(pageBuilder.build())
                        .build();
            }
        }
        if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), param.getType(), null, null);
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                param.setCampaignIdList(campaignIds);
            } else {
                return AllGroupDataResponse.GroupHomeVo.newBuilder()
                        .setPage(pageBuilder.build())
                        .build();
            }
        }
        //获取不同类型数据 sp、sd
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            getSpGroupVoListNew(puid, param, voPage, false, false);
        } else if (Constants.SD.equalsIgnoreCase(param.getType())) {
            getSdGroupVoListNew(puid, param, voPage, false, false);
        } else if (Constants.SB.equalsIgnoreCase(param.getType())) {
            getSbGroupVoListNew(puid, param, voPage, false, false);
        }

        //填充标签数据
        StopWatch sw = new StopWatch();
        sw.start("group list ad tag data fill");
        fillAdTagData(puid, param.getShopId(), param,voPage.getRows());
        sw.stop();
        log.info("ad tag fill data spend time: {}/ms", sw.getTotalTimeMillis());
        //分页后填充广告信息
        sw.start("group list ad info data fill");
        fillAdInfo(puid, param.getShopId(), voPage.getRows());
        sw.stop();
        log.info("ad info fill data spend time: {}/ms", sw.getTotalTimeMillis());

        List<GroupPageVo> rows = voPage.getRows();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        if (CollectionUtils.isNotEmpty(rows)) {
            //填充环比数据
            Map<String, GroupPageVo> compareGroupMap = null;

            if (param.getIsCompare()) {
                //对比时无须高级搜索条件
                param.setUseAdvanced(false);
                // 取店铺销售额
                BigDecimal shopSalesByDataCompare = cpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
                if (shopSalesByDataCompare == null) {
                    shopSalesByDataCompare = BigDecimal.ZERO;
                }
                param.setShopSales(shopSalesByDataCompare);
                param.setStartDate(param.getCompareStartDate());
                param.setEndDate(param.getCompareEndDate());
                param.setGroupIds(rows.stream().map(GroupPageVo::getAdGroupId).collect(Collectors.toList()));
                List<GroupPageVo> spGroupPageVoListCompare = new ArrayList<>();
                if (Constants.SP.equalsIgnoreCase(param.getType())) {
                    spGroupPageVoListCompare = getSpGroupVoListNew(puid, param, voPage, true, false);
                } else if (Constants.SD.equalsIgnoreCase(param.getType())) {
                    spGroupPageVoListCompare = getSdGroupVoListNew(puid, param, voPage, true, false);
                } else if (Constants.SB.equalsIgnoreCase(param.getType())) {
                    spGroupPageVoListCompare = getSbGroupVoListNew(puid, param, voPage, true, false);
                }
                compareGroupMap = spGroupPageVoListCompare.stream().collect(Collectors.toMap(
                        GroupPageVo::getAdGroupId, Function.identity(), (a, b) -> a));
            }

            Map<String, GroupPageVo> finalCompareGroupMap = compareGroupMap;
            List<AllGroupDataResponse.GroupHomeVo.Page.GroupPageVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                AllGroupDataResponse.GroupHomeVo.Page.GroupPageVo.Builder voBuilder = AllGroupDataResponse.GroupHomeVo.Page.GroupPageVo.newBuilder();
                if (StringUtils.isNotBlank(item.getType())) {
                    voBuilder.setType(item.getType());
                }
                if(shopAuth.getMarketplaceId() != null){
                    voBuilder.setMarketplaceId(shopAuth.getMarketplaceId());
                }
                if (item.getId() != null) {
                    voBuilder.setId(Int64Value.of(item.getId()));
                }
                if (item.getShopId() != null) {
                    voBuilder.setShopId(Int32Value.of(item.getShopId()));
                }
                if (StringUtils.isNotBlank(item.getCampaignId())) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (StringUtils.isNotBlank(item.getName())) {
                    voBuilder.setName(item.getName());
                }
                if (StringUtils.isNotBlank(item.getState())) {
                    voBuilder.setState(item.getState());
                }
                if (StringUtils.isNotBlank(item.getCampaignName())) {
                    voBuilder.setCampaignName(item.getCampaignName());
                }
                if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                    voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                }

                if (StringUtils.isNotBlank(item.getTargetingType())) {
                    voBuilder.setTargetingType(item.getTargetingType());
                }
                if (StringUtils.isNotBlank(item.getCampaignState())) {
                    voBuilder.setCampaignState(item.getCampaignState());
                }
                if (StringUtils.isNotBlank(item.getDailyBudget())) {
                    voBuilder.setDailyBudget(item.getDailyBudget());
                }
                if (StringUtils.isNotBlank(item.getAdGroupId())) {
                    voBuilder.setAdGroupId(item.getAdGroupId());
                }

                if (StringUtils.isNotBlank(item.getCreator())) {
                    voBuilder.setCreator(item.getCreator());
                }
                if (StringUtils.isNotBlank(item.getState())) {
                    voBuilder.setState(item.getState());
                }
                if (StringUtils.isNotBlank(item.getDefaultBid())) {
                    voBuilder.setDefaultBid(item.getDefaultBid());
                }
                if (item.getCreateTime() != null) {
                    voBuilder.setCreateTime(DateUtil.dateToStrWithFormat(item.getCreateTime(), "yyyy-MM-dd HH:mm"));
                }
                if (StringUtils.isNotBlank(item.getAdGroupType())) {
                    voBuilder.setAdGroupType(item.getAdGroupType());
                }
                if (item.getTargetingNum() != null) {
                    voBuilder.setTargetingNum(Int32Value.of(item.getTargetingNum()));
                }
                if (item.getAdProductNum() != null) {
                    voBuilder.setAdProductNum(Int32Value.of(item.getAdProductNum()));
                }
                if (item.getPortfolioId() != null) {
                    voBuilder.setPortfolioId(item.getPortfolioId());
                }
                if (item.getPortfolioName() != null) {
                    voBuilder.setPortfolioName(item.getPortfolioName());
                }
                if (item.getIsHidden() != null ) {
                    voBuilder.setIsHidden(item.getIsHidden());
                }

                if(StringUtils.isNotBlank(item.getBidOptimization())){
                    voBuilder.setBidOptimization(item.getBidOptimization());
                }

                if(StringUtils.isNotBlank(item.getCostType())){
                    voBuilder.setCostType(item.getCostType());
                }
                if(item.getServingStatus() != null){
                    voBuilder.setServingStatus(item.getServingStatus());
                }
                if(item.getServingStatusDec() != null){
                    voBuilder.setServingStatusDec(item.getServingStatusDec());
                }
                if(item.getServingStatusName() != null){
                    voBuilder.setServingStatusName(item.getServingStatusName());
                }

                if (item.getSbType() != null) {
                    voBuilder.setSbType(item.getSbType());
                }

                if(CollectionUtils.isNotEmpty(item.getAdTags())){
                    item.getAdTags().forEach(e->{
                        AdTagVo.Builder builder = AdTagVo.newBuilder();
                        AdTagVo tagVo = builder.setId(Int64Value.of(e.getId())).setColor(e.getColor()).setName(e.getName()).build();
                        voBuilder.addAdTags(tagVo);
                    });
                }

                voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                voBuilder.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
                voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");

                /**
                 * TODO 广告报告重构
                 * sd广告vcpm类型报告特殊字段。
                 */
                //可见展示次数(VCPM专用)
                voBuilder.setViewImpressions(Int32Value.of(Optional.ofNullable(item.getViewImpressions()).orElse(0)));
                //每笔订单花费
                voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                //vcpm
                voBuilder.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0");
                //本广告产品订单量
                voBuilder.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
                //其他产品广告订单量
                voBuilder.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
                //本广告产品销售额
                voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                //其他产品广告销售额
                voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                //广告销量
                voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
                //本广告产品销量
                voBuilder.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
                //其他产品广告销量
                voBuilder.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));
                //“品牌新买家”订单量
                voBuilder.setOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0)));
                //“品牌新买家”订单百分比
                voBuilder.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
                //“品牌新买家”销售额
                voBuilder.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
                //“品牌新买家”销售额百分比
                voBuilder.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
                //“品牌新买家”销量
                voBuilder.setUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getUnitsOrderedNewToBrandFTD()).orElse(0)));
                //“品牌新买家”销量百分比
                voBuilder.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(item.getUnitsOrderedRateNewToBrandFTD()) ? item.getUnitsOrderedRateNewToBrandFTD() : "0");

                // 花费占比
                voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                // 销售额占比
                voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                // 订单量占比
                voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                // 销量占比
                voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");

                if (StringUtils.isNotBlank(item.getLandingPage())) {
                    voBuilder.setLandingPage(item.getLandingPage());
                }
                if (StringUtils.isNotBlank(item.getAdFormat())) {
                    voBuilder.setAdFormat(item.getAdFormat());
                }
                if (StringUtils.isNotBlank(item.getBrandEntityId())) {
                    voBuilder.setBrandEntityId(item.getBrandEntityId());
                }
                if (StringUtils.isNotBlank(item.getCreativeType())) {
                    voBuilder.setCreativeType(item.getCreativeType());
                }

                voBuilder.setNewToBrandDetailPageViews(Optional.ofNullable(item.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
                voBuilder.setAddToCart(Optional.ofNullable(item.getAddToCart()).map(String::valueOf).orElse("0"));
                voBuilder.setAddToCartRate(Optional.ofNullable(item.getAddToCartRate()).map(String::valueOf).orElse("0"));
                voBuilder.setECPAddToCart(Optional.ofNullable(item.getECPAddToCart()).map(String::valueOf).orElse("0"));
                voBuilder.setVideo5SecondViews(Optional.ofNullable(item.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
                voBuilder.setVideo5SecondViewRate(Optional.ofNullable(item.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
                voBuilder.setVideoFirstQuartileViews(Optional.ofNullable(item.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
                voBuilder.setVideoMidpointViews(Optional.ofNullable(item.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
                voBuilder.setVideoThirdQuartileViews(Optional.ofNullable(item.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
                voBuilder.setVideoCompleteViews(Optional.ofNullable(item.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
                voBuilder.setVideoUnmutes(Optional.ofNullable(item.getVideoUnmutes()).map(String::valueOf).orElse("0"));
                voBuilder.setViewabilityRate(Optional.ofNullable(item.getViewabilityRate()).map(String::valueOf).orElse("0"));
                voBuilder.setViewClickThroughRate(Optional.ofNullable(item.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
                voBuilder.setBrandedSearches(Optional.ofNullable(item.getBrandedSearches()).map(String::valueOf).orElse("0"));
                voBuilder.setDetailPageViews(Optional.ofNullable(item.getDetailPageViews()).map(String::valueOf).orElse("0"));
                voBuilder.setCumulativeReach(Optional.ofNullable(item.getCumulativeReach()).map(String::valueOf).orElse("0"));
                voBuilder.setImpressionsFrequencyAverage(Optional.ofNullable(item.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
                voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                //环比指标数据
                if (MapUtils.isNotEmpty(finalCompareGroupMap)) {
                    if (finalCompareGroupMap.containsKey(item.getAdGroupId())) {
                        GroupPageVo compareItem = finalCompareGroupMap.get(item.getAdGroupId());

                        voBuilder.setCompareImpressions(Int32Value.of(Optional.ofNullable(compareItem.getImpressions()).orElse(0)));
                        //曝光环比值
                        int impressionDiff = voBuilder.getImpressions().getValue() - voBuilder.getCompareImpressions().getValue();
                        voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions().getValue() == 0 ? "-" :
                                new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions().getValue()),
                                        2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareViewImpressions(Int32Value.of(Optional.ofNullable(compareItem.getViewImpressions()).orElse(0)));
                        //可见展示环比值
                        int viewImpressionDiff = voBuilder.getViewImpressions().getValue() - voBuilder.getCompareViewImpressions().getValue();
                        voBuilder.setCompareViewImpressionsRate(voBuilder.getCompareViewImpressions().getValue() == 0 ? "-" :
                                new BigDecimal(viewImpressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareViewImpressions().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareClicks(Int32Value.of(Optional.ofNullable(compareItem.getClicks()).orElse(0)));
                        //点击量环比值
                        int clicksDiff = voBuilder.getClicks().getValue() - voBuilder.getCompareClicks().getValue();
                        voBuilder.setCompareClicksRate(voBuilder.getCompareClicks().getValue() == 0 ? "-" :
                                new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
                        //ctr环比值
                        BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
                        voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
                        //cvr环比值
                        BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
                        voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
                        //Acos环比值
                        BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
                        voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
                        //Acots环比值
                        BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
                        voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
                        //Asots环比值
                        BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
                        voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0)));
                        //AdOrderNum环比值
                        int adOrderNumDiff = voBuilder.getAdOrderNum().getValue() - voBuilder.getCompareAdOrderNum().getValue();
                        voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
                        //AdCost环比值
                        BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
                        voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
                        //AdCostPerClick环比值
                        BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
                        voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
                        //AdSale环比值
                        BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
                        voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
                        //Roas环比值
                        BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
                        voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                        //Cpa环比值
                        BigDecimal CpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                        voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                CpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareVcpm(StringUtils.isNotBlank(compareItem.getVcpm()) ? compareItem.getVcpm() : "0");
                        //Vcpm环比值
                        BigDecimal vcpmDiff = new BigDecimal(voBuilder.getVcpm()).subtract(new BigDecimal(voBuilder.getCompareVcpm()));
                        voBuilder.setCompareVcpmRate(new BigDecimal(voBuilder.getCompareVcpm()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                vcpmDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareVcpm()), 2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareAdSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0)));
                        //AdSaleNum比值
                        int adSaleNumDiff = voBuilder.getAdSaleNum().getValue() - voBuilder.getCompareAdSaleNum().getValue();
                        voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum().getValue() == 0 ? "-" :
                                new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0)));
                        //AdOtherOrderNum比值
                        int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum().getValue() - voBuilder.getCompareAdOtherOrderNum().getValue();
                        voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                        //AdSales环比值
                        BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                        voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                        //AdOtherSales环比值
                        BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                        voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getOrderNum()).orElse(0)));
                        //OrderNum比值
                        int orderNumDiff = voBuilder.getOrderNum().getValue() - voBuilder.getCompareOrderNum().getValue();
                        voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSelfSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0)));
                        //AdSelfSaleNum比值
                        int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum().getValue() - voBuilder.getCompareAdSelfSaleNum().getValue();
                        voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum().getValue() == 0 ? "-" :
                                new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0)));
                        //AdOtherSaleNum比值
                        int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum().getValue() - voBuilder.getCompareAdOtherSaleNum().getValue();
                        voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum().getValue() == 0 ? "-" :
                                new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getOrdersNewToBrandFTD()).orElse(0)));
                        //OrdersNewToBrandFTD比值
                        int ordersNewToBrandFTDDiff = voBuilder.getOrdersNewToBrandFTD().getValue() - voBuilder.getCompareOrdersNewToBrandFTD().getValue();
                        voBuilder.setCompareOrdersNewToBrandFTDRate(voBuilder.getCompareOrdersNewToBrandFTD().getValue() == 0 ? "-" :
                                new BigDecimal(ordersNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrdersNewToBrandFTD().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getOrderRateNewToBrandFTD()) ?
                                compareItem.getOrderRateNewToBrandFTD() : "0");
                        //OrderRateNewToBrandFTD环比值
                        BigDecimal orderRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getOrderRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()));
                        voBuilder.setCompareOrderRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                orderRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareSalesNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesNewToBrandFTD()) ?
                                compareItem.getSalesNewToBrandFTD() : "0");
                        //SalesNewToBrandFTD环比值
                        BigDecimal salesNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()));
                        voBuilder.setCompareSalesNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                salesNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareSalesRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesRateNewToBrandFTD()) ?
                                compareItem.getSalesRateNewToBrandFTD() : "0");
                        //SalesRateNewToBrandFTD环比值
                        BigDecimal salesRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()));
                        voBuilder.setCompareSalesRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                salesRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getUnitsOrderedNewToBrandFTD()).orElse(0)));
                        //UnitsOrderedNewToBrandFTD比值
                        int unitsOrderedNewToBrandFTDDiff = voBuilder.getUnitsOrderedNewToBrandFTD().getValue() - voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue();
                        voBuilder.setCompareUnitsOrderedNewToBrandFTDRate(voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue() == 0 ? "-" :
                                new BigDecimal(unitsOrderedNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getUnitsOrderedRateNewToBrandFTD()) ?
                                compareItem.getUnitsOrderedRateNewToBrandFTD() : "0");
                        //UnitsOrderedRateNewToBrandFTD环比值
                        BigDecimal UnitsOrderedRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getUnitsOrderedRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()));
                        voBuilder.setCompareUnitsOrderedRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                UnitsOrderedRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPercentage(StringUtils.isNotBlank(compareItem.getAdCostPercentage()) ?
                                compareItem.getAdCostPercentage() : "0");
                        //AdCostPercentage环比值
                        BigDecimal adCostPercentageDiff = new BigDecimal(voBuilder.getAdCostPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdCostPercentage()));
                        voBuilder.setCompareAdCostPercentageRate(new BigDecimal(voBuilder.getCompareAdCostPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPercentage()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSalePercentage(StringUtils.isNotBlank(compareItem.getAdSalePercentage()) ? compareItem.getAdSalePercentage() : "0");
                        //AdSalePercentage环比值
                        BigDecimal adSalePercentageDiff = new BigDecimal(voBuilder.getAdSalePercentage()).subtract(new BigDecimal(voBuilder.getCompareAdSalePercentage()));
                        voBuilder.setCompareAdSalePercentageRate(new BigDecimal(voBuilder.getCompareAdSalePercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSalePercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSalePercentage()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNumPercentage(StringUtils.isNotBlank(compareItem.getAdOrderNumPercentage()) ? compareItem.getAdOrderNumPercentage() : "0");
                        //AdOrderNumPercentage环比值
                        BigDecimal adOrderNumPercentageDiff = new BigDecimal(voBuilder.getAdOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()));
                        voBuilder.setCompareAdOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adOrderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNumPercentage(StringUtils.isNotBlank(compareItem.getOrderNumPercentage()) ? compareItem.getOrderNumPercentage() : "0");
                        //OrderNumPercentage环比值
                        BigDecimal orderNumPercentageDiff = new BigDecimal(voBuilder.getOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareOrderNumPercentage()));
                        voBuilder.setCompareOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                orderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareNewToBrandDetailPageViews(Optional.ofNullable(compareItem.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareNewToBrandDetailPageViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareNewToBrandDetailPageViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getNewToBrandDetailPageViews(), voBuilder.getCompareNewToBrandDetailPageViews(), 4), 100)));
                        voBuilder.setCompareAddToCart(Optional.ofNullable(compareItem.getAddToCart()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAddToCartRates(MathUtil.isNullOrZero(voBuilder.getCompareAddToCart()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAddToCart(), voBuilder.getCompareAddToCart(), 4), 100)));
                        voBuilder.setCompareAddToCartRate(Optional.ofNullable(compareItem.getAddToCartRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAddToCartRateRate(MathUtil.isNullOrZero(voBuilder.getCompareAddToCartRate()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAddToCartRate(), voBuilder.getCompareAddToCartRate(), 4), 100)));
                        voBuilder.setCompareECPAddToCart(Optional.ofNullable(compareItem.getECPAddToCart()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareECPAddToCartRate(MathUtil.isNullOrZero(voBuilder.getCompareECPAddToCart()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getECPAddToCart(), voBuilder.getCompareECPAddToCart(), 4), 100)));
                        voBuilder.setCompareVideo5SecondViews(Optional.ofNullable(compareItem.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideo5SecondViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideo5SecondViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideo5SecondViews(), voBuilder.getCompareVideo5SecondViews(), 4), 100)));
                        voBuilder.setCompareVideo5SecondViewRate(Optional.ofNullable(compareItem.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideo5SecondViewRateRate(MathUtil.isNullOrZero(voBuilder.getCompareVideo5SecondViewRate()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideo5SecondViewRate(), voBuilder.getCompareVideo5SecondViewRate(), 4), 100)));
                        voBuilder.setCompareVideoFirstQuartileViews(Optional.ofNullable(compareItem.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideoFirstQuartileViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoFirstQuartileViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoFirstQuartileViews(), voBuilder.getCompareVideoFirstQuartileViews(), 4), 100)));
                        voBuilder.setCompareVideoMidpointViews(Optional.ofNullable(compareItem.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideoMidpointViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoMidpointViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoMidpointViews(), voBuilder.getCompareVideoMidpointViews(), 4), 100)));
                        voBuilder.setCompareVideoThirdQuartileViews(Optional.ofNullable(compareItem.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideoThirdQuartileViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoThirdQuartileViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoThirdQuartileViews(), voBuilder.getCompareVideoThirdQuartileViews(), 4), 100)));
                        voBuilder.setCompareVideoCompleteViews(Optional.ofNullable(compareItem.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideoCompleteViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoCompleteViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoCompleteViews(), voBuilder.getCompareVideoCompleteViews(), 4), 100)));
                        voBuilder.setCompareVideoUnmutes(Optional.ofNullable(compareItem.getVideoUnmutes()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideoUnmutesRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoUnmutes()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoUnmutes(), voBuilder.getCompareVideoUnmutes(), 4), 100)));
                        voBuilder.setCompareViewabilityRate(Optional.ofNullable(compareItem.getViewabilityRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareViewabilityRateRate(MathUtil.isNullOrZero(voBuilder.getCompareViewabilityRate()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewabilityRate(), voBuilder.getCompareViewabilityRate(), 4), 100)));
                        voBuilder.setCompareViewClickThroughRate(Optional.ofNullable(compareItem.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareViewClickThroughRateRate(MathUtil.isNullOrZero(voBuilder.getCompareViewClickThroughRate()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewClickThroughRate(), voBuilder.getCompareViewClickThroughRate(), 4), 100)));
                        voBuilder.setCompareBrandedSearches(Optional.ofNullable(compareItem.getBrandedSearches()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareBrandedSearchesRate(MathUtil.isNullOrZero(voBuilder.getCompareBrandedSearches()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getBrandedSearches(), voBuilder.getCompareBrandedSearches(), 4), 100)));
                        voBuilder.setCompareDetailPageViews(Optional.ofNullable(compareItem.getDetailPageViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareDetailPageViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareDetailPageViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getDetailPageViews(), voBuilder.getCompareDetailPageViews(), 4), 100)));
                        voBuilder.setCompareImpressionsFrequencyAverage(Optional.ofNullable(compareItem.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareImpressionsFrequencyAverageRate(MathUtil.isNullOrZero(voBuilder.getCompareImpressionsFrequencyAverage()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getImpressionsFrequencyAverage(), voBuilder.getCompareImpressionsFrequencyAverage(), 4), 100)));
                        voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAdvertisingUnitPriceRate(MathUtil.isNullOrZero(voBuilder.getCompareAdvertisingUnitPrice()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAdvertisingUnitPrice(), voBuilder.getCompareAdvertisingUnitPrice(), 4), 100)));
                    }
                }

                if (isVc) {
                    voBuilder.setAcots("-");
                    voBuilder.setCompareAcotsRate("-");
                    voBuilder.setCompareAcots("-");
                    voBuilder.setAsots("-");
                    voBuilder.setCompareAsotsRate("-");
                    voBuilder.setCompareAsots("-");
                }
                return voBuilder.build();
            }).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }
        return AllGroupDataResponse.GroupHomeVo.newBuilder()
                .setPage(pageBuilder.buildPartial())
                .build();
    }

    @Override
    public List<GroupPageVo> getSpGroupVoListNew(int puid, GroupPageParam param, Page<GroupPageVo> voPage, boolean isCompare, boolean isExport) {
        StopWatch sw = new StopWatch("get all sp group vo list");
        GroupPageMetricVo<GroupInfoPageVo> groupPageMetricVo;
        AdGroupDefaultOrderEnum orderField = AdGroupDefaultOrderEnum.getAdGroupDefaultOrderEnumByKey(param.getOrderField());
        //将页面列表查询分为三种
        if (StringUtils.isNotBlank(param.getOrderType()) && StringUtils.isNotBlank(param.getOrderField()) && Objects.isNull(orderField)) {
            //3.高级搜索，使用指标字段排序
            groupPageMetricVo = getAllGroupPageOrderByReport(puid, param, isExport);
        } else {
            if (AdPageAdvancedSearchUtils.isUseReportDataAdvanced(param)) {
                //2.高级搜索，使用基本字段排序
                groupPageMetricVo = getAllGroupPageFilterAndOrderByGroup(puid, param, isExport);
            } else {
                //1.无高级搜索，使用基本字段排序
                groupPageMetricVo = getAllSpGroupPageNoFilterAndOrder(puid, param, isExport);
            }
        }
        return groupPageDataHandler(puid, param, voPage, groupPageMetricVo, isCompare);
    }

    @Override
    public List<GroupPageVo> getSdGroupVoListNew(int puid, GroupPageParam param, Page<GroupPageVo> voPage, boolean isCompare, boolean isExport) {
        StopWatch sw = new StopWatch("get all sd group vo list");
        GroupPageMetricVo<GroupInfoPageVo> groupPageMetricVo;
        AdGroupDefaultOrderEnum orderField = AdGroupDefaultOrderEnum.getAdGroupDefaultOrderEnumByKey(param.getOrderField());
        //将页面列表查询分为三种
        if (StringUtils.isNotBlank(param.getOrderType()) && StringUtils.isNotBlank(param.getOrderField()) && Objects.isNull(orderField)) {
            //3.高级搜索，使用指标字段排序
            groupPageMetricVo = getAllSdGroupPageOrderByReport(puid, param, isExport);
        } else {
            if (AdPageAdvancedSearchUtils.isUseReportDataAdvanced(param)) {
                //2.高级搜索，使用基本字段排序
                groupPageMetricVo = getAllSdGroupPageFilterAndOrderByGroup(puid, param, isExport);
            } else {
                //1.无高级搜索，使用基本字段排序
                groupPageMetricVo = getAllSdGroupPageNoFilterAndOrder(puid, param, isExport);
            }
        }
        return groupPageDataHandler(puid, param, voPage, groupPageMetricVo, isCompare);
    }

    @Override
    public List<GroupPageVo> getSbGroupVoListNew(int puid, GroupPageParam param, Page<GroupPageVo> voPage, boolean isCompare, boolean isExport) {
        GroupPageMetricVo<GroupInfoPageVo> groupPageMetricVo;
        AdGroupDefaultOrderEnum orderField = AdGroupDefaultOrderEnum.getAdGroupDefaultOrderEnumByKey(param.getOrderField());
        //将页面列表查询分为三种
        if (StringUtils.isNotBlank(param.getOrderType()) && StringUtils.isNotBlank(param.getOrderField()) && Objects.isNull(orderField)) {
            //3.高级搜索，使用指标字段排序
            groupPageMetricVo = getAllSbGroupPageOrderByReport(puid, param, isExport);
        } else {
            if (AdPageAdvancedSearchUtils.isUseReportDataAdvanced(param)) {
                //2.高级搜索，使用基本字段排序
                groupPageMetricVo = getAllSbGroupPageFilterAndOrderByGroup(puid, param, isExport);
            } else {
                //1.无高级搜索，使用基本字段排序
                groupPageMetricVo = getAllSbGroupPageNoFilterAndOrder(puid, param, isExport);
            }
        }
        return groupPageDataHandler(puid, param, voPage, groupPageMetricVo, isCompare);
    }

    private List<GroupPageVo> groupPageDataHandler(int puid, GroupPageParam param, Page<GroupPageVo> voPage,
                                                   GroupPageMetricVo<GroupInfoPageVo> groupPageMetricVo, boolean isCompare) {
        List<GroupPageVo> voList = Lists.newArrayList();
        if (Objects.nonNull(groupPageMetricVo.getPage()) && CollectionUtils.isEmpty(groupPageMetricVo.getPage().getRows())) {
            return voList;
        }
        List<GroupInfoPageVo> poList = groupPageMetricVo.getPage().getRows();
        List<String> portfolioIds = poList.stream().filter(p -> p.getPortfolioId() != null).map(GroupInfoPageVo::getPortfolioId).collect(Collectors.toList());
        Map<String, AmazonAdPortfolio> portfolioMap = null;
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            portfolioMap = portfolioDao.getPortfolioList(puid, param.getShopId(), portfolioIds).stream()
                    .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
        }

        Map<Integer, User> userMap = userDao.listByPuid(puid).stream().collect(Collectors.toMap(User::getId, e -> e));//判断user表中id是否存在
        Page<GroupInfoPageVo> page = groupPageMetricVo.getPage();
        AdMetricCompareDto adMetricCompareDto = groupPageMetricVo.getAdMetricDto();
        AdMetricDto adMetricDto = new AdMetricDto();
        if (Objects.nonNull(adMetricCompareDto)) {
            if (isCompare) {
                adMetricDto.setSumCost(adMetricCompareDto.getSumCompareCost());
                adMetricDto.setSumAdSale(adMetricCompareDto.getSumCompareAdSale());
                adMetricDto.setSumAdOrderNum(adMetricCompareDto.getSumCompareAdOrderNum());
                adMetricDto.setSumOrderNum(adMetricCompareDto.getSumCompareOrderNum());
            } else {
                adMetricDto.setSumCost(adMetricCompareDto.getSumCost());
                adMetricDto.setSumAdSale(adMetricCompareDto.getSumAdSale());
                adMetricDto.setSumAdOrderNum(adMetricCompareDto.getSumAdOrderNum());
                adMetricDto.setSumOrderNum(adMetricCompareDto.getSumOrderNum());
            }
        } else {
            adMetricDto = null;
        }
        voPage.setTotalSize(page.getTotalSize());
        voPage.setTotalPage(page.getTotalPage());
        if (CollectionUtils.isNotEmpty(page.getRows())) {
            GroupPageVo vo;
            for (GroupInfoPageVo groupInfoPageVo : page.getRows()) {
                vo = new GroupPageVo();
                //填充广告组数据
                this.fillGroupPageVo(groupInfoPageVo, vo);
                //填充广告组报告数据
                cpcCommService.fillReportDataIntoPageVo(vo, groupInfoPageVo, new ShopSaleDto(param.getShopSales()));
                //填充占比
                cpcCommService.filterMetricData(vo, adMetricDto);

                if (StringUtils.isNotBlank(groupInfoPageVo.getPortfolioId())) {
                    if (portfolioMap != null && portfolioMap.containsKey(groupInfoPageVo.getPortfolioId())) {
                        vo.setPortfolioName(portfolioMap.get(groupInfoPageVo.getPortfolioId()).getName());
                        vo.setIsHidden(portfolioMap.get(groupInfoPageVo.getPortfolioId()).getIsHidden());
                    } else {
                        vo.setPortfolioName("广告组合待同步");
                    }
                } else {
                    vo.setPortfolioName("-");
                }
                // 创建人
                if (userMap.containsKey(groupInfoPageVo.getCreateId())) {
                    User user = userMap.get(groupInfoPageVo.getCreateId());
                    if (StringUtils.isNotBlank(user.getNickname())) {
                        vo.setCreator(user.getNickname());
                    }
                }
                //兼容本产品广告销量（sd无广告销量）
                if (CampaignTypeEnum.sb.getCampaignType().equals(vo.getType())) {
                    vo.setAdSelfSaleNum(0);
                }
                if (CampaignTypeEnum.sd.getCampaignType().equals(vo.getType())) {
                    vo.setAdSelfSaleNum(0);
                    vo.setAdOtherSaleNum(0);
                }
                voList.add(vo);
            }
        }
        voPage.setRows(voList);
        return voList;
    }

    private GroupPageMetricVo<GroupInfoPageVo> getAllGroupPageOrderByReport(Integer puid, GroupPageParam param, boolean isExport) {
        GroupPageMetricVo<GroupInfoPageVo> groupPageMetricVo = new GroupPageMetricVo<>();
        Page<GroupInfoPageVo> page = new Page<>(param.getPageNo(), param.getPageSize(), 0, 0);
        groupPageMetricVo.setPage(page);
        StopWatch sw = new StopWatch("get sp group page list with advanced search and report order");
        sw.start("query adGroupId and report field from report");
        List<AllGroupOrderBo> adOrderBoList = amazonAdGroupReportDao.getAdGroupIdAndIndexList(puid, param);
        if (CollectionUtils.isEmpty(adOrderBoList)) {
            return groupPageMetricVo;
        }
        sw.stop();
        //2. 查询t_amazon_ad_group表获取出列表页所有的sp广告组
        sw.start("query adGroupId from sp group table");
        List<String> adGroupIdList = adOrderBoList.parallelStream().map(AllGroupOrderBo::getId).collect(Collectors.toList());
        List<String> adGroupIds = amazonAdGroupDao.getGroupIdListByParamAndIds(puid, param, adGroupIdList);
        adOrderBoList = adOrderBoList.stream().filter(e -> adGroupIds.contains(e.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adOrderBoList)) {
            return groupPageMetricVo;
        }
        //获取列表页所有的adGroupId，用于查询占比数据
        List<String> allAdGroupIdList = adOrderBoList.parallelStream().map(AllGroupOrderBo::getId).collect(Collectors.toList());
        sw.stop();
        //3. 将广告组根据adGroupIdList排序分页，获取列表页数据
        sw.start("将adGroupId根据adGroupIdList排序分页，获取列表页数据");
        groupPageMetricVo.setPage(this.getAllGroupPageByOrderBos(puid, param, adOrderBoList));
        sw.stop();
        //占比数据
        sw.start("获取占比数据");
        if (isExport) {
            AdMetricDto spAdMetricDto = this.getGroupAdMetricDto(puid, param, allAdGroupIdList);
            AdMetricCompareDto adMetricCompareDto = new AdMetricCompareDto();
            adMetricCompareDto.setSumAdOrderNum(spAdMetricDto.getSumAdOrderNum());
            adMetricCompareDto.setSumAdSale(spAdMetricDto.getSumAdSale());
            adMetricCompareDto.setSumCost(spAdMetricDto.getSumCost());
            adMetricCompareDto.setSumOrderNum(spAdMetricDto.getSumOrderNum());
            groupPageMetricVo.setAdMetricDto(adMetricCompareDto);
        } else {
            groupPageMetricVo.setAdMetricDto(this.waitAggregateMetric(param.getPageSign()));
        }
        sw.stop();
        log.info(sw.prettyPrint());
        return groupPageMetricVo;
    }

    private GroupPageMetricVo<GroupInfoPageVo> getAllSdGroupPageOrderByReport(Integer puid, GroupPageParam param, boolean isExport) {
        GroupPageMetricVo<GroupInfoPageVo> groupPageMetricVo = new GroupPageMetricVo<>();
        Page<GroupInfoPageVo> page = new Page<>(param.getPageNo(), param.getPageSize(), 0, 0);
        groupPageMetricVo.setPage(page);
        StopWatch sw = new StopWatch("get sd group page list with advanced search and report order");
        sw.start("query sd adGroupId and report field from report");
        List<AllGroupOrderBo> adOrderBoList = amazonAdSdGroupReportDao.getSdGroupIdAndIndexList(puid, param);
        if (CollectionUtils.isEmpty(adOrderBoList)) {
            return groupPageMetricVo;
        }
        sw.stop();
        //2. 查询t_amazon_ad_group_sd表获取出列表页所有的sp广告组
        sw.start("query adGroupId from sd group table");
        List<String> adGroupIdList = adOrderBoList.parallelStream().map(AllGroupOrderBo::getId).collect(Collectors.toList());
        List<String> adGroupIds = amazonSdAdGroupDao.getSdGroupIdListByParamAndIds(puid, param, adGroupIdList);
        adOrderBoList = adOrderBoList.stream().filter(e -> adGroupIds.contains(e.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adOrderBoList)) {
            return groupPageMetricVo;
        }
        //获取列表页所有的adGroupId，用于查询占比数据
        List<String> allAdGroupIdList = adOrderBoList.parallelStream().map(AllGroupOrderBo::getId).collect(Collectors.toList());
        sw.stop();
        //3. 将广告组根据adGroupIdList排序分页，获取列表页数据
        sw.start("将adGroupId根据adGroupIdList排序分页，获取列表页数据");
        groupPageMetricVo.setPage(this.getAllGroupPageByOrderBos(puid, param, adOrderBoList));
        sw.stop();
        //占比数据
        sw.start("获取占比数据");
        if (isExport) {
            AdMetricDto spAdMetricDto = this.getGroupAdMetricDto(puid, param, allAdGroupIdList);
            AdMetricCompareDto adMetricCompareDto = new AdMetricCompareDto();
            adMetricCompareDto.setSumAdOrderNum(spAdMetricDto.getSumAdOrderNum());
            adMetricCompareDto.setSumAdSale(spAdMetricDto.getSumAdSale());
            adMetricCompareDto.setSumCost(spAdMetricDto.getSumCost());
            adMetricCompareDto.setSumOrderNum(spAdMetricDto.getSumOrderNum());
            groupPageMetricVo.setAdMetricDto(adMetricCompareDto);
        } else {
            groupPageMetricVo.setAdMetricDto(this.waitAggregateMetric(param.getPageSign()));
        }
        sw.stop();
        log.info(sw.prettyPrint());
        return groupPageMetricVo;
    }

    private GroupPageMetricVo<GroupInfoPageVo> getAllSbGroupPageOrderByReport(Integer puid, GroupPageParam param, boolean isExport) {
        GroupPageMetricVo<GroupInfoPageVo> groupPageMetricVo = new GroupPageMetricVo<>();
        Page<GroupInfoPageVo> page = new Page<>(param.getPageNo(), param.getPageSize(), 0, 0);
        groupPageMetricVo.setPage(page);
        StopWatch sw = new StopWatch("get sb group page list with advanced search and report order");
        sw.start("query sb adGroupId and report field from report");
        List<AllGroupOrderBo> adOrderBoList = amazonAdSbGroupReportDao.getSbGroupIdAndIndexList(puid, param);
        if (CollectionUtils.isEmpty(adOrderBoList)) {
            return groupPageMetricVo;
        }
        sw.stop();
        //2. 查询t_amazon_ad_group_sb表获取出列表页所有的sp广告组
        sw.start("query adGroupId from sb group table");
        List<String> adGroupIdList = adOrderBoList.parallelStream().map(AllGroupOrderBo::getId).collect(Collectors.toList());
        List<String> adGroupIds = amazonSbAdGroupDao.getSbGroupIdListByParamAndIds(puid, param, adGroupIdList);
        adOrderBoList = adOrderBoList.stream().filter(e -> adGroupIds.contains(e.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adOrderBoList)) {
            return groupPageMetricVo;
        }
        //获取列表页所有的adGroupId，用于查询占比数据
        List<String> allAdGroupIdList = adOrderBoList.parallelStream().map(AllGroupOrderBo::getId).collect(Collectors.toList());
        sw.stop();
        //3. 将广告组根据adGroupIdList排序分页，获取列表页数据
        sw.start("将adGroupId根据adGroupIdList排序分页，获取列表页数据");
        groupPageMetricVo.setPage(this.getAllGroupPageByOrderBos(puid, param, adOrderBoList));
        sw.stop();
        //占比数据
        sw.start("获取占比数据");
        if (isExport) {
            AdMetricDto spAdMetricDto = this.getGroupAdMetricDto(puid, param, allAdGroupIdList);
            AdMetricCompareDto adMetricCompareDto = new AdMetricCompareDto();
            adMetricCompareDto.setSumAdOrderNum(spAdMetricDto.getSumAdOrderNum());
            adMetricCompareDto.setSumAdSale(spAdMetricDto.getSumAdSale());
            adMetricCompareDto.setSumCost(spAdMetricDto.getSumCost());
            adMetricCompareDto.setSumOrderNum(spAdMetricDto.getSumOrderNum());
            groupPageMetricVo.setAdMetricDto(adMetricCompareDto);
        } else {
            groupPageMetricVo.setAdMetricDto(this.waitAggregateMetric(param.getPageSign()));
        }
        sw.stop();
        log.info(sw.prettyPrint());
        return groupPageMetricVo;
    }

    private GroupPageMetricVo<GroupInfoPageVo> getAllGroupPageFilterAndOrderByGroup(Integer puid, GroupPageParam param, boolean isExport) {
        GroupPageMetricVo<GroupInfoPageVo> groupPageMetricVo = new GroupPageMetricVo<>();
        Page<GroupInfoPageVo> page = new Page<>(param.getPageNo(), param.getPageSize(), 0, 0);
        groupPageMetricVo.setPage(page);
        StopWatch sw = new StopWatch("get sp group page list with advanced search and normal order");
        sw.start("query sp adGroupId from report by advanced search param");
        List<String> adGroupIdList = amazonAdGroupReportDao.getGroupIdListByParam(puid, param);
        sw.stop();
        log.info("query sp adGroupId from report by advanced search param spend time:{} /ms", sw.getTotalTimeMillis());
        if (CollectionUtils.isEmpty(adGroupIdList)) {
            return groupPageMetricVo;
        }
        sw.start("query sp adGroupId from sp group by adGroupIdList param");
        AdGroupDefaultOrderEnum orderEnum = AdGroupDefaultOrderEnum.getAdGroupDefaultOrderEnumByKey(param.getOrderField());
        List<AllGroupOrderBo> adGroupOrderBoList = amazonAdGroupDao.getGroupIdAndOrderFieldList(puid, param, adGroupIdList,
                (Objects.nonNull(orderEnum)) ? orderEnum.getOrderField() : SqlStringReportUtil.ID);
        if (CollectionUtils.isEmpty(adGroupOrderBoList)) {
            return groupPageMetricVo;
        }
        //获取列表页所有的groupId，用于查询占比数据
        List<String> allAdGroupIdList = adGroupOrderBoList.stream().map(AllGroupOrderBo::getId).collect(Collectors.toList());
        sw.stop();
        log.info("query sp adGroupId from sp group by adGroupIdList param spend time:{} /ms", sw.getTotalTimeMillis());

        //3、内存中根据默认、竞价排序，获取列表页
        sw.start("get all data and ordered by field");
        groupPageMetricVo.setPage(this.getAllGroupPageByOrderBos(puid, param, adGroupOrderBoList));
        sw.stop();
        sw.start("get sp group metric");
        if (isExport) {//占比数据
            AdMetricDto spAdMetricDto = this.getGroupAdMetricDto(puid, param, allAdGroupIdList);
            AdMetricCompareDto adMetricCompareDto = new AdMetricCompareDto();
            adMetricCompareDto.setSumAdOrderNum(spAdMetricDto.getSumAdOrderNum());
            adMetricCompareDto.setSumAdSale(spAdMetricDto.getSumAdSale());
            adMetricCompareDto.setSumCost(spAdMetricDto.getSumCost());
            adMetricCompareDto.setSumOrderNum(spAdMetricDto.getSumOrderNum());
            groupPageMetricVo.setAdMetricDto(adMetricCompareDto);
        } else {
            groupPageMetricVo.setAdMetricDto(this.waitAggregateMetric(param.getPageSign()));
        }
        sw.stop();
        log.info(sw.prettyPrint());
        return groupPageMetricVo;
    }

    private GroupPageMetricVo<GroupInfoPageVo> getAllSdGroupPageFilterAndOrderByGroup(Integer puid, GroupPageParam param, boolean isExport) {
        GroupPageMetricVo<GroupInfoPageVo> groupPageMetricVo = new GroupPageMetricVo<>();
        Page<GroupInfoPageVo> page = new Page<>(param.getPageNo(), param.getPageSize(), 0, 0);
        groupPageMetricVo.setPage(page);
        StopWatch sw = new StopWatch("get sd group page list with advanced search and normal order");
        sw.start("query sd adGroupId from report by advanced search param");
        List<String> adGroupIdList = amazonAdSdGroupReportDao.getSdGroupIdListByParam(puid, param);
        sw.stop();
        log.info("query sd adGroupId from report by advanced search param spend time:{} /ms", sw.getTotalTimeMillis());
        if (CollectionUtils.isEmpty(adGroupIdList)) {
            return groupPageMetricVo;
        }
        sw.start("query sd adGroupId from sd group by adGroupIdList param");
        AdGroupDefaultOrderEnum orderEnum = AdGroupDefaultOrderEnum.getAdGroupDefaultOrderEnumByKey(param.getOrderField());
        List<AllGroupOrderBo> adGroupOrderBoList = amazonSdAdGroupDao.getSdGroupIdAndOrderFieldList(puid, param, adGroupIdList,
                (Objects.nonNull(orderEnum)) ? orderEnum.getOrderField() : SqlStringReportUtil.ID);
        if (CollectionUtils.isEmpty(adGroupOrderBoList)) {
            return groupPageMetricVo;
        }
        //获取列表页所有的groupId，用于查询占比数据
        List<String> allAdGroupIdList = adGroupOrderBoList.stream().map(AllGroupOrderBo::getId).collect(Collectors.toList());
        sw.stop();
        log.info("query sd adGroupId from sd group by adGroupIdList param spend time:{} /ms", sw.getTotalTimeMillis());

        //3、内存中根据默认、竞价排序，获取列表页
        sw.start("get all sd group data and ordered by field");
        groupPageMetricVo.setPage(this.getAllGroupPageByOrderBos(puid, param, adGroupOrderBoList));
        sw.stop();
        sw.start("get sd group metric");
        if (isExport) {//占比数据
            AdMetricDto spAdMetricDto = this.getSdGroupAdMetricDto(puid, param, allAdGroupIdList);
            AdMetricCompareDto adMetricCompareDto = new AdMetricCompareDto();
            adMetricCompareDto.setSumAdOrderNum(spAdMetricDto.getSumAdOrderNum());
            adMetricCompareDto.setSumAdSale(spAdMetricDto.getSumAdSale());
            adMetricCompareDto.setSumCost(spAdMetricDto.getSumCost());
            adMetricCompareDto.setSumOrderNum(spAdMetricDto.getSumOrderNum());
            groupPageMetricVo.setAdMetricDto(adMetricCompareDto);
        } else {
            groupPageMetricVo.setAdMetricDto(this.waitAggregateMetric(param.getPageSign()));
        }
        sw.stop();
        log.info(sw.prettyPrint());
        return groupPageMetricVo;
    }

    private GroupPageMetricVo<GroupInfoPageVo> getAllSbGroupPageFilterAndOrderByGroup(Integer puid, GroupPageParam param, boolean isExport) {
        GroupPageMetricVo<GroupInfoPageVo> groupPageMetricVo = new GroupPageMetricVo<>();
        Page<GroupInfoPageVo> page = new Page<>(param.getPageNo(), param.getPageSize(), 0, 0);
        groupPageMetricVo.setPage(page);
        StopWatch sw = new StopWatch("get sb group page list with advanced search and normal order");
        sw.start("query sb adGroupId from report by advanced search param");
        List<String> adGroupIdList = amazonAdSbGroupReportDao.getSbGroupIdListByParam(puid, param);
        sw.stop();
        log.info("query sb adGroupId from report by advanced search param spend time:{} /ms", sw.getTotalTimeMillis());
        if (CollectionUtils.isEmpty(adGroupIdList)) {
            return groupPageMetricVo;
        }
        sw.start("query sb adGroupId from sb group by adGroupIdList param");
        AdGroupDefaultOrderEnum orderEnum = AdGroupDefaultOrderEnum.getAdGroupDefaultOrderEnumByKey(param.getOrderField());
        List<AllGroupOrderBo> adGroupOrderBoList = amazonSbAdGroupDao.getSbGroupIdAndOrderFieldList(puid, param, adGroupIdList,
                (Objects.nonNull(orderEnum)) ? orderEnum.getOrderField() : SqlStringReportUtil.ID);
        if (CollectionUtils.isEmpty(adGroupOrderBoList)) {
            return groupPageMetricVo;
        }
        //获取列表页所有的groupId，用于查询占比数据
        List<String> allAdGroupIdList = adGroupOrderBoList.stream().map(AllGroupOrderBo::getId).collect(Collectors.toList());
        sw.stop();
        log.info("query sb adGroupId from sb group by adGroupIdList param spend time:{} /ms", sw.getTotalTimeMillis());

        //3、内存中根据默认、竞价排序，获取列表页
        sw.start("get all sb group data and ordered by field");
        groupPageMetricVo.setPage(this.getAllGroupPageByOrderBos(puid, param, adGroupOrderBoList));
        sw.stop();
        sw.start("get sb group metric");
        if (isExport) {//占比数据
            AdMetricDto spAdMetricDto = this.getSbGroupAdMetricDto(puid, param, allAdGroupIdList);
            AdMetricCompareDto adMetricCompareDto = new AdMetricCompareDto();
            adMetricCompareDto.setSumAdOrderNum(spAdMetricDto.getSumAdOrderNum());
            adMetricCompareDto.setSumAdSale(spAdMetricDto.getSumAdSale());
            adMetricCompareDto.setSumCost(spAdMetricDto.getSumCost());
            adMetricCompareDto.setSumOrderNum(spAdMetricDto.getSumOrderNum());
            groupPageMetricVo.setAdMetricDto(adMetricCompareDto);
        } else {
            groupPageMetricVo.setAdMetricDto(this.waitAggregateMetric(param.getPageSign()));
        }
        sw.stop();
        log.info(sw.prettyPrint());
        return groupPageMetricVo;
    }

    private Page<GroupInfoPageVo> getAllGroupPageByOrderBos(Integer puid, GroupPageParam param, List<AllGroupOrderBo> adOrderBos) {
        StopWatch sw = new StopWatch("get page by group orderBos");
        Page<GroupInfoPageVo> page = new Page<>();
        boolean isOrder = StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType());
        sw.start("start order and page");
        adOrderBos = AdPageUtil.getGroupOrderPage(param.getPageNo(), param.getPageSize(), adOrderBos, (isOrder ? param.getOrderType() : null), page);
        sw.stop();
        //获取列表页数据
        sw.start("get all group info");
        List<GroupInfoPageVo> voList;
        if (Constants.SP.equals(param.getType())) {
            voList = this.getAllGroupPageByAdGroupIdList(puid, param, adOrderBos.stream().map(AllGroupOrderBo::getId).collect(Collectors.toList()));
        }
        else if (Constants.SB.equals(param.getType())) {
            voList = this.getSbGroupPageByAdGroupIdList(puid, param, adOrderBos.stream().map(AllGroupOrderBo::getId).collect(Collectors.toList()));
        }else {
            voList = this.getSdGroupPageByAdGroupIdList(puid, param, adOrderBos.stream().map(AllGroupOrderBo::getId).collect(Collectors.toList()));
        }
        sw.stop();
        if (CollectionUtils.isEmpty(voList)) {
            return page;
        }
        page.setRows(voList);
        return page;
    }

    private List<GroupInfoPageVo> getAllGroupPageByAdGroupIdList(Integer puid, GroupPageParam param, List<String> adGroupIdList) {
        List<GroupInfoPageVo> groupPageVoList = amazonAdGroupDao.getGroupPageVoListByGroupIdList(puid, param, adGroupIdList);
        if (CollectionUtils.isEmpty(groupPageVoList)) {
            return groupPageVoList;
        }
        List<String> adGroupIds = groupPageVoList.parallelStream().map(GroupInfoPageVo::getAdGroupId).collect(Collectors.toList());
        Map<String, GroupInfoPageVo> reportMap = amazonAdGroupReportDao.getReportByGroupIds(puid, param, adGroupIds)
                .stream().collect(Collectors.toMap(GroupInfoPageVo::getAdGroupId, Function.identity()));
        groupPageVoList.forEach(e -> {
            if (reportMap.containsKey(e.getAdGroupId())) {
                this.groupPageVoReportDataCopy(reportMap.get(e.getAdGroupId()), e);
            }
        });
        return groupPageVoList;
    }

    private List<GroupInfoPageVo> getSbGroupPageByAdGroupIdList(Integer puid, GroupPageParam param, List<String> adGroupIdList) {
        List<GroupInfoPageVo> groupPageVoList = amazonSbAdGroupDao.getSbGroupPageVoListByGroupIdList(puid, param, adGroupIdList);
        if (CollectionUtils.isEmpty(groupPageVoList)) {
            return groupPageVoList;
        }
        List<String> adGroupIds = groupPageVoList.parallelStream().map(GroupInfoPageVo::getAdGroupId).collect(Collectors.toList());
        Map<String, GroupInfoPageVo> reportMap = amazonAdSbGroupReportDao.getSbReportByGroupIds(puid, param, adGroupIds)
                .stream().collect(Collectors.toMap(GroupInfoPageVo::getAdGroupId, Function.identity()));
        groupPageVoList.forEach(e -> {
            if (reportMap.containsKey(e.getAdGroupId())) {
                this.groupPageVoReportDataCopy(reportMap.get(e.getAdGroupId()), e);
            }
        });
        return groupPageVoList;
    }

    private List<GroupInfoPageVo> getSdGroupPageByAdGroupIdList(Integer puid, GroupPageParam param, List<String> adGroupIdList) {
        List<GroupInfoPageVo> groupPageVoList = amazonSdAdGroupDao.getSdGroupPageVoListByGroupIdList(puid, param, adGroupIdList);
        if (CollectionUtils.isEmpty(groupPageVoList)) {
            return groupPageVoList;
        }
        List<String> adGroupIds = groupPageVoList.parallelStream().map(GroupInfoPageVo::getAdGroupId).collect(Collectors.toList());
        Map<String, GroupInfoPageVo> reportMap = amazonAdSdGroupReportDao.getSdReportByGroupIds(puid, param, adGroupIds)
                .stream().collect(Collectors.toMap(GroupInfoPageVo::getAdGroupId, Function.identity()));
        Map<String, AdHomePerformancedto> latestReportMap = amazonAdSdGroupReportDao.listLatestReports(puid, param.getShopId(), adGroupIds, false)
                .stream().collect(Collectors.toMap(AdHomePerformancedto::getGroupId, Function.identity()));
        groupPageVoList.forEach(e -> {
            if (reportMap.containsKey(e.getAdGroupId())) {
                this.groupPageVoReportDataCopy(reportMap.get(e.getAdGroupId()), e);
            }
            if (latestReportMap.containsKey(e.getAdGroupId())) {
                this.groupPageVoLatestReportDataCopy(latestReportMap.get(e.getAdGroupId()), e);
            }
        });
        return groupPageVoList;
    }

    @Override
    public GroupPageMetricVo<GroupInfoPageVo> getAllSpGroupPageNoFilterAndOrder(Integer puid, GroupPageParam param, boolean isExport) {
        GroupPageMetricVo<GroupInfoPageVo> groupPageMetricVo = new GroupPageMetricVo<>();
        StopWatch sw = new StopWatch("get sp group page list with normal search and order");
        sw.start("start query sp group page list");
        Page<GroupInfoPageVo> page = amazonAdGroupDao.getAllGroupPage(puid, param);
        groupPageMetricVo.setPage(page);
        sw.stop();
        if (CollectionUtils.isEmpty(page.getRows())) {
            return groupPageMetricVo;
        }
        //查询广告组报告
        sw.start("get sp group page list report with normal search and order");
        List<String> adGroupIdList = page.getRows().parallelStream().map(GroupInfoPageVo::getAdGroupId).collect(Collectors.toList());
        Map<String, GroupInfoPageVo> reportMap = amazonAdGroupReportDao.getReportByGroupIds(puid, param, adGroupIdList).
                parallelStream().collect(Collectors.toMap(GroupInfoPageVo::getAdGroupId, Function.identity()));
        sw.stop();
        page.getRows().forEach(e -> {
            if (reportMap.containsKey(e.getAdGroupId())) {
                this.groupPageVoReportDataCopy(reportMap.get(e.getAdGroupId()), e);
            }
        });
        sw.start("get sp group metric");
        if (isExport) {
            AdMetricDto spAdMetricDto = this.getGroupAdMetricDto(puid, param, null);
            AdMetricCompareDto adMetricCompareDto = new AdMetricCompareDto();
            adMetricCompareDto.setSumAdOrderNum(spAdMetricDto.getSumAdOrderNum());
            adMetricCompareDto.setSumAdSale(spAdMetricDto.getSumAdSale());
            adMetricCompareDto.setSumCost(spAdMetricDto.getSumCost());
            adMetricCompareDto.setSumOrderNum(spAdMetricDto.getSumOrderNum());
            groupPageMetricVo.setAdMetricDto(adMetricCompareDto);
        } else {
            groupPageMetricVo.setAdMetricDto(this.waitAggregateMetric(param.getPageSign()));
        }
        sw.stop();
        log.info(sw.prettyPrint());
        return groupPageMetricVo;
    }

    private GroupPageMetricVo<GroupInfoPageVo> getAllSdGroupPageNoFilterAndOrder(Integer puid, GroupPageParam param, boolean isExport) {
        GroupPageMetricVo<GroupInfoPageVo> groupPageMetricVo = new GroupPageMetricVo<>();
        StopWatch sw = new StopWatch("get sd group page list with normal search and order");
        sw.start("start query sd group page list");
        Page<GroupInfoPageVo> page = amazonSdAdGroupDao.getAllSdGroupPage(puid, param);
        groupPageMetricVo.setPage(page);
        sw.stop();
        if (CollectionUtils.isEmpty(page.getRows())) {
            return groupPageMetricVo;
        }
        //查询广告组报告
        sw.start("get sd group page list report with normal search and order");
        List<String> adGroupIdList = page.getRows().parallelStream().map(GroupInfoPageVo::getAdGroupId).collect(Collectors.toList());
        Map<String, GroupInfoPageVo> reportMap = amazonAdSdGroupReportDao.getSdReportByGroupIds(puid, param, adGroupIdList).
                parallelStream().collect(Collectors.toMap(GroupInfoPageVo::getAdGroupId, Function.identity()));
        sw.stop();
        page.getRows().forEach(e -> {
            if (reportMap.containsKey(e.getAdGroupId())) {
                this.groupPageVoReportDataCopy(reportMap.get(e.getAdGroupId()), e);
            }
        });
        sw.start("get sd group metric");
        if (isExport) {
            AdMetricDto spAdMetricDto = this.getGroupAdMetricDto(puid, param, null);
            AdMetricCompareDto adMetricCompareDto = new AdMetricCompareDto();
            adMetricCompareDto.setSumAdOrderNum(spAdMetricDto.getSumAdOrderNum());
            adMetricCompareDto.setSumAdSale(spAdMetricDto.getSumAdSale());
            adMetricCompareDto.setSumCost(spAdMetricDto.getSumCost());
            adMetricCompareDto.setSumOrderNum(spAdMetricDto.getSumOrderNum());
            groupPageMetricVo.setAdMetricDto(adMetricCompareDto);
        } else {
            groupPageMetricVo.setAdMetricDto(this.waitAggregateMetric(param.getPageSign()));
        }
        sw.stop();
        log.info(sw.prettyPrint());
        return groupPageMetricVo;
    }

    private GroupPageMetricVo<GroupInfoPageVo> getAllSbGroupPageNoFilterAndOrder(Integer puid, GroupPageParam param, boolean isExport) {
        GroupPageMetricVo<GroupInfoPageVo> groupPageMetricVo = new GroupPageMetricVo<>();
        StopWatch sw = new StopWatch("get sb group page list with normal search and order");
        sw.start("start query sb group page list");
        Page<GroupInfoPageVo> page = amazonSbAdGroupDao.getAllSbGroupPage(puid, param);
        groupPageMetricVo.setPage(page);
        sw.stop();
        if (CollectionUtils.isEmpty(page.getRows())) {
            return groupPageMetricVo;
        }
        //查询广告组报告
        sw.start("get sb group page list report with normal search and order");
        List<String> adGroupIdList = page.getRows().parallelStream().map(GroupInfoPageVo::getAdGroupId).collect(Collectors.toList());
        Map<String, GroupInfoPageVo> reportMap = amazonAdSbGroupReportDao.getSbReportByGroupIds(puid, param, adGroupIdList).
                parallelStream().collect(Collectors.toMap(GroupInfoPageVo::getAdGroupId, Function.identity()));
        sw.stop();
        page.getRows().forEach(e -> {
            if (reportMap.containsKey(e.getAdGroupId())) {
                this.groupPageVoReportDataCopy(reportMap.get(e.getAdGroupId()), e);
            }
        });
        sw.start("get sb group metric");
        if (isExport) {
            AdMetricDto spAdMetricDto = this.getGroupAdMetricDto(puid, param, null);
            AdMetricCompareDto adMetricCompareDto = new AdMetricCompareDto();
            adMetricCompareDto.setSumAdOrderNum(spAdMetricDto.getSumAdOrderNum());
            adMetricCompareDto.setSumAdSale(spAdMetricDto.getSumAdSale());
            adMetricCompareDto.setSumCost(spAdMetricDto.getSumCost());
            adMetricCompareDto.setSumOrderNum(spAdMetricDto.getSumOrderNum());
            groupPageMetricVo.setAdMetricDto(adMetricCompareDto);
        } else {
            groupPageMetricVo.setAdMetricDto(this.waitAggregateMetric(param.getPageSign()));
        }
        sw.stop();
        log.info(sw.prettyPrint());
        return groupPageMetricVo;
    }

    private void groupPageVoReportDataCopy(GroupInfoPageVo report, GroupInfoPageVo target) {
        target.setImpressions(report.getImpressions());
        target.setClicks(report.getClicks());
        target.setCost(report.getCost());
        target.setSaleNum(report.getSaleNum());
        target.setAdOrderNum(report.getAdOrderNum());
        target.setTotalSales(report.getTotalSales());
        target.setAdSales(report.getAdSales());
        target.setAdSaleNum(report.getAdSaleNum());
        target.setOrderNum(report.getOrderNum());
        target.setMaxTopIs(report.getMaxTopIs());
        target.setMinTopIs(report.getMinTopIs());
        target.setViewImpressions(report.getViewImpressions());
        target.setOrdersNewToBrand14d(report.getOrdersNewToBrand14d());
        target.setSalesNewToBrand14d(report.getSalesNewToBrand14d());
        target.setUnitsOrderedNewToBrand14d(report.getUnitsOrderedNewToBrand14d());
        target.setNewToBrandDetailPageViews(report.getNewToBrandDetailPageViews());
        target.setAddToCart(report.getAddToCart());
        target.setVideo5SecondViews(report.getVideo5SecondViews());
        target.setVideoFirstQuartileViews(report.getVideoFirstQuartileViews());
        target.setVideoMidpointViews(report.getVideoMidpointViews());
        target.setVideoThirdQuartileViews(report.getVideoThirdQuartileViews());
        target.setVideoCompleteViews(report.getVideoCompleteViews());
        target.setVideoUnmutes(report.getVideoUnmutes());
        target.setViewableImpressions(report.getViewableImpressions());
        target.setBrandedSearches(report.getBrandedSearches());
        target.setDetailPageViews(report.getDetailPageViews());
    }

    private void groupPageVoLatestReportDataCopy(AdHomePerformancedto latestReport, GroupInfoPageVo target){
        target.setCumulativeReach(latestReport.getCumulativeReach());
        target.setImpressionsFrequencyAverage(latestReport.getImpressionsFrequencyAverage());
    }

    private AdMetricDto getGroupAdMetricDto(Integer puid, GroupPageParam param, List<String> adGroupIdList) {
        AdMetricDto adMetricDto = new AdMetricDto();
        if (CollectionUtils.isEmpty(adGroupIdList)) {//当groupId为空时，需要重新查询一遍
            adGroupIdList = amazonAdGroupReportDao.getGroupIdListByParam(puid, param);
            if (CollectionUtils.isEmpty(adGroupIdList)) {
                return adMetricDto;
            }
            adGroupIdList = amazonAdGroupDao.getGroupIdListByParamAndIds(puid, param, adGroupIdList);
            if (CollectionUtils.isEmpty(adGroupIdList)) {
                return adMetricDto;
            }
        }
        //通过adGroupIds查询占比信息
        return amazonAdGroupReportDao.getGroupPageSumMetricDataByCampaignIdList(puid, param, adGroupIdList);
    }

    private AdMetricDto getSbGroupAdMetricDto(Integer puid, GroupPageParam param, List<String> adGroupIdList) {
        AdMetricDto adMetricDto = new AdMetricDto();
        if (CollectionUtils.isEmpty(adGroupIdList)) {//当groupId为空时，需要重新查询一遍
            adGroupIdList = amazonAdSbGroupReportDao.getSbGroupIdListByParam(puid, param);
            if (CollectionUtils.isEmpty(adGroupIdList)) {
                return adMetricDto;
            }
            adGroupIdList = amazonSbAdGroupDao.getSbGroupIdListByParamAndIds(puid, param, adGroupIdList);
            if (CollectionUtils.isEmpty(adGroupIdList)) {
                return adMetricDto;
            }
        }
        //通过adGroupIds查询占比信息
        return amazonAdSbGroupReportDao.getSbGroupPageSumMetricDataByCampaignIdList(puid, param, adGroupIdList);
    }

    private AdMetricDto getSdGroupAdMetricDto(Integer puid, GroupPageParam param, List<String> adGroupIdList) {
        AdMetricDto adMetricDto = new AdMetricDto();
        if (CollectionUtils.isEmpty(adGroupIdList)) {//当groupId为空时，需要重新查询一遍
            adGroupIdList = amazonAdSdGroupReportDao.getSdGroupIdListByParam(puid, param);
            if (CollectionUtils.isEmpty(adGroupIdList)) {
                return adMetricDto;
            }
            adGroupIdList = amazonSdAdGroupDao.getSdGroupIdListByParamAndIds(puid, param, adGroupIdList);
            if (CollectionUtils.isEmpty(adGroupIdList)) {
                return adMetricDto;
            }
        }
        //通过adGroupIds查询占比信息
        return amazonAdSdGroupReportDao.getSdGroupPageSumMetricDataByCampaignIdList(puid, param, adGroupIdList);
    }

    private AdMetricCompareDto waitAggregateMetric(String pageSign) {
        for (int i = 0; i < 50; i++) {
            Object objectAdMetricDto = redisService.get(Constants.GROUP_PAGE_SUM_METRIC + pageSign);
            if (objectAdMetricDto == null) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error(String.format("wait sp group aggregate metric error, pageSign:%s", pageSign), e);
                    throw new RuntimeException(e);
                }
                continue;
            }
            return JSONUtil.jsonToObject((String) objectAdMetricDto, AdMetricCompareDto.class);
        }
        return null;
    }

    @Override
    public AllGroupDataResponse.GroupHomeVo getSpGroupDorisData(Integer puid, GroupPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        BigDecimal shopSalesByData = cpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByData == null) {
            shopSalesByData = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByData);
        if (StringUtils.isBlank(param.getType())) {
            param.setType(Constants.SP);
        }
        Page<GroupPageVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());

        //勾选正在投放按钮移除其它
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> list = StringUtil.splitStr(param.getServingStatus());
            if (list.contains("enabled")) {
                param.setServingStatus("enabled");
            }
        }
        getSpDorisPageList(puid, param, voPage, false);
        long t1 = Instant.now().toEpochMilli();
        //分页后填充标签信息信息
        fillAdTagData(puid, param.getShopId(), param,voPage.getRows());
        log.info("标签数据填充花费时间 {}", Instant.now().toEpochMilli() - t1);
        //分页后填充广告信息
        fillAdInfo(puid, param.getShopId(), voPage.getRows());
        //分页后填充广告策略信息
        fillAdStrategy(param, voPage.getRows());
        AllGroupDataResponse.GroupHomeVo.Page.Builder pageBuilder = AllGroupDataResponse.GroupHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        List<GroupPageVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //填充环比数据
            Map<String, GroupPageVo> compareGroupMap = null;
            if (param.getIsCompare()) {
                //对比时无须高级搜索条件
                param.setUseAdvanced(false);
                // 取店铺销售额
                BigDecimal shopSalesByDataCompare = cpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
                if (shopSalesByDataCompare == null) {
                    shopSalesByDataCompare = BigDecimal.ZERO;
                }
                param.setShopSales(shopSalesByDataCompare);
                param.setStartDate(param.getCompareStartDate());
                param.setEndDate(param.getCompareEndDate());
                param.setGroupIds(rows.stream().map(GroupPageVo::getAdGroupId).collect(Collectors.toList()));

                Page<GroupPageVo> comparePage = new Page<>();
                comparePage.setPageNo(param.getPageNo());
                comparePage.setPageSize(param.getPageSize());
                List<GroupPageVo> spGroupPageVoListCompare = getSpDorisPageList(puid, param, comparePage, false).getRows();

                compareGroupMap = spGroupPageVoListCompare.stream().collect(Collectors.toMap(
                        GroupPageVo::getAdGroupId, Function.identity(), (a, b) -> a));
            }

            Map<String, GroupPageVo> finalCompareGroupMap = compareGroupMap;
            boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
            List<AllGroupDataResponse.GroupHomeVo.Page.GroupPageVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                AllGroupDataResponse.GroupHomeVo.Page.GroupPageVo.Builder voBuilder = AllGroupDataResponse.GroupHomeVo.Page.GroupPageVo.newBuilder();
                if (StringUtils.isNotBlank(item.getType())) {
                    voBuilder.setType(item.getType());
                }

                if(shopAuth.getMarketplaceId() != null){
                    voBuilder.setMarketplaceId(shopAuth.getMarketplaceId());
                }
                if (item.getId() != null) {
                    voBuilder.setId(Int64Value.of(item.getId()));
                }
                if (item.getShopId() != null) {
                    voBuilder.setShopId(Int32Value.of(item.getShopId()));
                }
                if (StringUtils.isNotBlank(item.getCampaignId())) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (StringUtils.isNotBlank(item.getName())) {
                    voBuilder.setName(item.getName());
                }
                if (StringUtils.isNotBlank(item.getState())) {
                    voBuilder.setState(item.getState());
                }
                if (StringUtils.isNotBlank(item.getCampaignName())) {
                    voBuilder.setCampaignName(item.getCampaignName());
                }
                if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                    voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                }

                if (StringUtils.isNotBlank(item.getTargetingType())) {
                    voBuilder.setTargetingType(item.getTargetingType());
                }
                if (StringUtils.isNotBlank(item.getCampaignState())) {
                    voBuilder.setCampaignState(item.getCampaignState());
                }
                if (StringUtils.isNotBlank(item.getDailyBudget())) {
                    voBuilder.setDailyBudget(item.getDailyBudget());
                }
                if (StringUtils.isNotBlank(item.getAdGroupId())) {
                    voBuilder.setAdGroupId(item.getAdGroupId());
                }

                if (StringUtils.isNotBlank(item.getCreator())) {
                    voBuilder.setCreator(item.getCreator());
                }
                if (StringUtils.isNotBlank(item.getState())) {
                    voBuilder.setState(item.getState());
                }
                if (StringUtils.isNotBlank(item.getDefaultBid())) {
                    voBuilder.setDefaultBid(item.getDefaultBid());
                }
                if (item.getCreateTime() != null) {
                    voBuilder.setCreateTime(DateUtil.dateToStrWithFormat(item.getCreateTime(), "yyyy-MM-dd HH:mm"));
                }
                if (StringUtils.isNotBlank(item.getAdGroupType())) {
                    voBuilder.setAdGroupType(item.getAdGroupType());
                }
                if (item.getTargetingNum() != null) {
                    voBuilder.setTargetingNum(Int32Value.of(item.getTargetingNum()));
                }
                if (item.getAdProductNum() != null) {
                    voBuilder.setAdProductNum(Int32Value.of(item.getAdProductNum()));
                }
                if (item.getPortfolioId() != null) {
                    voBuilder.setPortfolioId(item.getPortfolioId());
                }
                if (item.getPortfolioName() != null) {
                    voBuilder.setPortfolioName(item.getPortfolioName());
                }
                if (item.getIsHidden() != null ) {
                    voBuilder.setIsHidden(item.getIsHidden());
                }

                if(StringUtils.isNotBlank(item.getBidOptimization())){
                    voBuilder.setBidOptimization(item.getBidOptimization());
                }

                if(StringUtils.isNotBlank(item.getCostType())){
                    voBuilder.setCostType(item.getCostType());
                }
                if(item.getServingStatus() != null){
                    voBuilder.setServingStatus(item.getServingStatus());
                }
                if(item.getServingStatusDec() != null){
                    voBuilder.setServingStatusDec(item.getServingStatusDec());
                }
                if(item.getServingStatusName() != null){
                    voBuilder.setServingStatusName(item.getServingStatusName());
                }
                if (item.getIsStateBidding() != null) {
                    voBuilder.setIsAdGroupBidding(item.getIsStateBidding());
                }
                if (item.getPricingStateBidding() != null) {
                    voBuilder.setPricingAdGroupBidding(item.getPricingStateBidding());
                }

                if (item.getSbType() != null) {
                    voBuilder.setSbType(item.getSbType());
                }

                if(CollectionUtils.isNotEmpty(item.getAdTags())){
                    item.getAdTags().forEach(e->{
                        AdTagVo.Builder builder = AdTagVo.newBuilder();
                        AdTagVo tagVo = builder.setId(Int64Value.of(e.getId())).setColor(e.getColor()).setName(e.getName()).build();
                        voBuilder.addAdTags(tagVo);
                    });
                }
                // 广告策略标签
                if(CollectionUtils.isNotEmpty(item.getStrategyList())){
                    voBuilder.addAllAdStrategys(buildStrategyList(item));
                }
                voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                voBuilder.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
                voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");

                /**
                 * TODO 广告报告重构
                 * sd广告vcpm类型报告特殊字段。
                 */
                //可见展示次数(VCPM专用)
                voBuilder.setViewImpressions(Int32Value.of(Optional.ofNullable(item.getViewImpressions()).orElse(0)));
                //每笔订单花费
                voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                //vcpm
                voBuilder.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0");
                //本广告产品订单量
                voBuilder.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
                //其他产品广告订单量
                voBuilder.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
                //本广告产品销售额
                voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                //其他产品广告销售额
                voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                //广告销量
                voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
                //本广告产品销量
                voBuilder.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
                //其他产品广告销量
                if (Constants.SB.equalsIgnoreCase(param.getType())) {
                    voBuilder.setAdOtherSaleNum(Int32Value.of(0));
                } else {
                    voBuilder.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));
                }
                //“品牌新买家”订单量
                voBuilder.setOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0)));
                //“品牌新买家”订单百分比
                voBuilder.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
                //“品牌新买家”销售额
                voBuilder.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
                //“品牌新买家”销售额百分比
                voBuilder.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
                //“品牌新买家”销量
                voBuilder.setUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getUnitsOrderedNewToBrandFTD()).orElse(0)));
                //“品牌新买家”销量百分比
                voBuilder.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(item.getUnitsOrderedRateNewToBrandFTD()) ? item.getUnitsOrderedRateNewToBrandFTD() : "0");

                // 花费占比
                voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                // 销售额占比
                voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                // 订单量占比
                voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                // 销量占比
                voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");

                if (StringUtils.isNotBlank(item.getLandingPage())) {
                    voBuilder.setLandingPage(item.getLandingPage());
                }
                if (StringUtils.isNotBlank(item.getAdFormat())) {
                    voBuilder.setAdFormat(item.getAdFormat());
                }
                if (StringUtils.isNotBlank(item.getBrandEntityId())) {
                    voBuilder.setBrandEntityId(item.getBrandEntityId());
                }

                voBuilder.setNewToBrandDetailPageViews(Optional.ofNullable(item.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
                voBuilder.setAddToCart(Optional.ofNullable(item.getAddToCart()).map(String::valueOf).orElse("0"));
                voBuilder.setAddToCartRate(Optional.ofNullable(item.getAddToCartRate()).map(String::valueOf).orElse("0"));
                voBuilder.setECPAddToCart(Optional.ofNullable(item.getECPAddToCart()).map(String::valueOf).orElse("0"));
                voBuilder.setVideo5SecondViews(Optional.ofNullable(item.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
                voBuilder.setVideo5SecondViewRate(Optional.ofNullable(item.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
                voBuilder.setVideoFirstQuartileViews(Optional.ofNullable(item.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
                voBuilder.setVideoMidpointViews(Optional.ofNullable(item.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
                voBuilder.setVideoThirdQuartileViews(Optional.ofNullable(item.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
                voBuilder.setVideoCompleteViews(Optional.ofNullable(item.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
                voBuilder.setVideoUnmutes(Optional.ofNullable(item.getVideoUnmutes()).map(String::valueOf).orElse("0"));
                voBuilder.setViewabilityRate(Optional.ofNullable(item.getViewabilityRate()).map(String::valueOf).orElse("0"));
                voBuilder.setViewClickThroughRate(Optional.ofNullable(item.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
                voBuilder.setBrandedSearches(Optional.ofNullable(item.getBrandedSearches()).map(String::valueOf).orElse("0"));
                voBuilder.setDetailPageViews(Optional.ofNullable(item.getDetailPageViews()).map(String::valueOf).orElse("0"));
                voBuilder.setCumulativeReach(Optional.ofNullable(item.getCumulativeReach()).map(String::valueOf).orElse("0"));
                voBuilder.setImpressionsFrequencyAverage(Optional.ofNullable(item.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
                voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                //环比指标数据
                if (MapUtils.isNotEmpty(finalCompareGroupMap)) {
                    if (finalCompareGroupMap.containsKey(item.getAdGroupId())) {
                        GroupPageVo compareItem = finalCompareGroupMap.get(item.getAdGroupId());

                        voBuilder.setCompareImpressions(Int32Value.of(Optional.ofNullable(compareItem.getImpressions()).orElse(0)));
                        //曝光环比值
                        int impressionDiff = voBuilder.getImpressions().getValue() - voBuilder.getCompareImpressions().getValue();
                        voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions().getValue() == 0 ? "-" :
                                new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions().getValue()),
                                        2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareViewImpressions(Int32Value.of(Optional.ofNullable(compareItem.getViewImpressions()).orElse(0)));
                        //可见展示环比值
                        int viewImpressionDiff = voBuilder.getViewImpressions().getValue() - voBuilder.getCompareViewImpressions().getValue();
                        voBuilder.setCompareViewImpressionsRate(voBuilder.getCompareViewImpressions().getValue() == 0 ? "-" :
                                new BigDecimal(viewImpressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareViewImpressions().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareClicks(Int32Value.of(Optional.ofNullable(compareItem.getClicks()).orElse(0)));
                        //点击量环比值
                        int clicksDiff = voBuilder.getClicks().getValue() - voBuilder.getCompareClicks().getValue();
                        voBuilder.setCompareClicksRate(voBuilder.getCompareClicks().getValue() == 0 ? "-" :
                                new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
                        //ctr环比值
                        BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
                        voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
                        //cvr环比值
                        BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
                        voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
                        //Acos环比值
                        BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
                        voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
                        //Acots环比值
                        BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
                        voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
                        //Asots环比值
                        BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
                        voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0)));
                        //AdOrderNum环比值
                        int adOrderNumDiff = voBuilder.getAdOrderNum().getValue() - voBuilder.getCompareAdOrderNum().getValue();
                        voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
                        //AdCost环比值
                        BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
                        voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
                        //AdCostPerClick环比值
                        BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
                        voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
                        //AdSale环比值
                        BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
                        voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
                        //Roas环比值
                        BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
                        voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                        //Cpa环比值
                        BigDecimal CpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                        voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                CpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareVcpm(StringUtils.isNotBlank(compareItem.getVcpm()) ? compareItem.getVcpm() : "0");
                        //Vcpm环比值
                        BigDecimal vcpmDiff = new BigDecimal(voBuilder.getVcpm()).subtract(new BigDecimal(voBuilder.getCompareVcpm()));
                        voBuilder.setCompareVcpmRate(new BigDecimal(voBuilder.getCompareVcpm()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                vcpmDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareVcpm()), 2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareAdSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0)));
                        //AdSaleNum比值
                        int adSaleNumDiff = voBuilder.getAdSaleNum().getValue() - voBuilder.getCompareAdSaleNum().getValue();
                        voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum().getValue() == 0 ? "-" :
                                new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0)));
                        //AdOtherOrderNum比值
                        int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum().getValue() - voBuilder.getCompareAdOtherOrderNum().getValue();
                        voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                        //AdSales环比值
                        BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                        voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                        //AdOtherSales环比值
                        BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                        voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getOrderNum()).orElse(0)));
                        //OrderNum比值
                        int orderNumDiff = voBuilder.getOrderNum().getValue() - voBuilder.getCompareOrderNum().getValue();
                        voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSelfSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0)));
                        //AdSelfSaleNum比值
                        int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum().getValue() - voBuilder.getCompareAdSelfSaleNum().getValue();
                        voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum().getValue() == 0 ? "-" :
                                new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0)));
                        //AdOtherSaleNum比值
                        int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum().getValue() - voBuilder.getCompareAdOtherSaleNum().getValue();
                        voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum().getValue() == 0 ? "-" :
                                new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getOrdersNewToBrandFTD()).orElse(0)));
                        //OrdersNewToBrandFTD比值
                        int ordersNewToBrandFTDDiff = voBuilder.getOrdersNewToBrandFTD().getValue() - voBuilder.getCompareOrdersNewToBrandFTD().getValue();
                        voBuilder.setCompareOrdersNewToBrandFTDRate(voBuilder.getCompareOrdersNewToBrandFTD().getValue() == 0 ? "-" :
                                new BigDecimal(ordersNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrdersNewToBrandFTD().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getOrderRateNewToBrandFTD()) ?
                                compareItem.getOrderRateNewToBrandFTD() : "0");
                        //OrderRateNewToBrandFTD环比值
                        BigDecimal orderRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getOrderRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()));
                        voBuilder.setCompareOrderRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                orderRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareSalesNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesNewToBrandFTD()) ?
                                compareItem.getSalesNewToBrandFTD() : "0");
                        //SalesNewToBrandFTD环比值
                        BigDecimal salesNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()));
                        voBuilder.setCompareSalesNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                salesNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareSalesRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesRateNewToBrandFTD()) ?
                                compareItem.getSalesRateNewToBrandFTD() : "0");
                        //SalesRateNewToBrandFTD环比值
                        BigDecimal salesRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()));
                        voBuilder.setCompareSalesRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                salesRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getUnitsOrderedNewToBrandFTD()).orElse(0)));
                        //UnitsOrderedNewToBrandFTD比值
                        int unitsOrderedNewToBrandFTDDiff = voBuilder.getUnitsOrderedNewToBrandFTD().getValue() - voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue();
                        voBuilder.setCompareUnitsOrderedNewToBrandFTDRate(voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue() == 0 ? "-" :
                                new BigDecimal(unitsOrderedNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getUnitsOrderedRateNewToBrandFTD()) ?
                                compareItem.getUnitsOrderedRateNewToBrandFTD() : "0");
                        //UnitsOrderedRateNewToBrandFTD环比值
                        BigDecimal UnitsOrderedRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getUnitsOrderedRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()));
                        voBuilder.setCompareUnitsOrderedRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                UnitsOrderedRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPercentage(StringUtils.isNotBlank(compareItem.getAdCostPercentage()) ?
                                compareItem.getAdCostPercentage() : "0");
                        //AdCostPercentage环比值
                        BigDecimal adCostPercentageDiff = new BigDecimal(voBuilder.getAdCostPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdCostPercentage()));
                        voBuilder.setCompareAdCostPercentageRate(new BigDecimal(voBuilder.getCompareAdCostPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPercentage()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSalePercentage(StringUtils.isNotBlank(compareItem.getAdSalePercentage()) ? compareItem.getAdSalePercentage() : "0");
                        //AdSalePercentage环比值
                        BigDecimal adSalePercentageDiff = new BigDecimal(voBuilder.getAdSalePercentage()).subtract(new BigDecimal(voBuilder.getCompareAdSalePercentage()));
                        voBuilder.setCompareAdSalePercentageRate(new BigDecimal(voBuilder.getCompareAdSalePercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSalePercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSalePercentage()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNumPercentage(StringUtils.isNotBlank(compareItem.getAdOrderNumPercentage()) ? compareItem.getAdOrderNumPercentage() : "0");
                        //AdOrderNumPercentage环比值
                        BigDecimal adOrderNumPercentageDiff = new BigDecimal(voBuilder.getAdOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()));
                        voBuilder.setCompareAdOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adOrderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNumPercentage(StringUtils.isNotBlank(compareItem.getOrderNumPercentage()) ? compareItem.getOrderNumPercentage() : "0");
                        //OrderNumPercentage环比值
                        BigDecimal orderNumPercentageDiff = new BigDecimal(voBuilder.getOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareOrderNumPercentage()));
                        voBuilder.setCompareOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                orderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareNewToBrandDetailPageViews(Optional.ofNullable(compareItem.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareNewToBrandDetailPageViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareNewToBrandDetailPageViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getNewToBrandDetailPageViews(), voBuilder.getCompareNewToBrandDetailPageViews(), 4), 100)));
                        voBuilder.setCompareAddToCart(Optional.ofNullable(compareItem.getAddToCart()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAddToCartRates(MathUtil.isNullOrZero(voBuilder.getCompareAddToCart()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAddToCart(), voBuilder.getCompareAddToCart(), 4), 100)));
                        voBuilder.setCompareAddToCartRate(Optional.ofNullable(compareItem.getAddToCartRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAddToCartRateRate(MathUtil.isNullOrZero(voBuilder.getCompareAddToCartRate()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAddToCartRate(), voBuilder.getCompareAddToCartRate(), 4), 100)));
                        voBuilder.setCompareECPAddToCart(Optional.ofNullable(compareItem.getECPAddToCart()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareECPAddToCartRate(MathUtil.isNullOrZero(voBuilder.getCompareECPAddToCart()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getECPAddToCart(), voBuilder.getCompareECPAddToCart(), 4), 100)));
                        voBuilder.setCompareVideo5SecondViews(Optional.ofNullable(compareItem.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideo5SecondViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideo5SecondViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideo5SecondViews(), voBuilder.getCompareVideo5SecondViews(), 4), 100)));
                        voBuilder.setCompareVideo5SecondViewRate(Optional.ofNullable(compareItem.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideo5SecondViewRateRate(MathUtil.isNullOrZero(voBuilder.getCompareVideo5SecondViewRate()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideo5SecondViewRate(), voBuilder.getCompareVideo5SecondViewRate(), 4), 100)));
                        voBuilder.setCompareVideoFirstQuartileViews(Optional.ofNullable(compareItem.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideoFirstQuartileViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoFirstQuartileViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoFirstQuartileViews(), voBuilder.getCompareVideoFirstQuartileViews(), 4), 100)));
                        voBuilder.setCompareVideoMidpointViews(Optional.ofNullable(compareItem.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideoMidpointViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoMidpointViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoMidpointViews(), voBuilder.getCompareVideoMidpointViews(), 4), 100)));
                        voBuilder.setCompareVideoThirdQuartileViews(Optional.ofNullable(compareItem.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideoThirdQuartileViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoThirdQuartileViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoThirdQuartileViews(), voBuilder.getCompareVideoThirdQuartileViews(), 4), 100)));
                        voBuilder.setCompareVideoCompleteViews(Optional.ofNullable(compareItem.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideoCompleteViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoCompleteViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoCompleteViews(), voBuilder.getCompareVideoCompleteViews(), 4), 100)));
                        voBuilder.setCompareVideoUnmutes(Optional.ofNullable(compareItem.getVideoUnmutes()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideoUnmutesRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoUnmutes()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoUnmutes(), voBuilder.getCompareVideoUnmutes(), 4), 100)));
                        voBuilder.setCompareViewabilityRate(Optional.ofNullable(compareItem.getViewabilityRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareViewabilityRateRate(MathUtil.isNullOrZero(voBuilder.getCompareViewabilityRate()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewabilityRate(), voBuilder.getCompareViewabilityRate(), 4), 100)));
                        voBuilder.setCompareViewClickThroughRate(Optional.ofNullable(compareItem.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareViewClickThroughRateRate(MathUtil.isNullOrZero(voBuilder.getCompareViewClickThroughRate()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewClickThroughRate(), voBuilder.getCompareViewClickThroughRate(), 4), 100)));
                        voBuilder.setCompareBrandedSearches(Optional.ofNullable(compareItem.getBrandedSearches()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareBrandedSearchesRate(MathUtil.isNullOrZero(voBuilder.getCompareBrandedSearches()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getBrandedSearches(), voBuilder.getCompareBrandedSearches(), 4), 100)));
                        voBuilder.setCompareDetailPageViews(Optional.ofNullable(compareItem.getDetailPageViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareDetailPageViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareDetailPageViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getDetailPageViews(), voBuilder.getCompareDetailPageViews(), 4), 100)));
                        voBuilder.setCompareImpressionsFrequencyAverage(Optional.ofNullable(compareItem.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareImpressionsFrequencyAverageRate(MathUtil.isNullOrZero(voBuilder.getCompareImpressionsFrequencyAverage()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getImpressionsFrequencyAverage(), voBuilder.getCompareImpressionsFrequencyAverage(), 4), 100)));
                        voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAdvertisingUnitPriceRate(MathUtil.isNullOrZero(voBuilder.getCompareAdvertisingUnitPrice()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAdvertisingUnitPrice(), voBuilder.getCompareAdvertisingUnitPrice(), 4), 100)));
                    }
                }


                if (isVc) {
                    voBuilder.setAcots("-");
                    voBuilder.setCompareAcotsRate("-");
                    voBuilder.setCompareAcots("-");
                    voBuilder.setAsots("-");
                    voBuilder.setCompareAsotsRate("-");
                    voBuilder.setCompareAsots("-");
                }
                return voBuilder.build();
            }).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }
        return AllGroupDataResponse.GroupHomeVo.newBuilder()
                .setPage(pageBuilder.buildPartial())
                .build();
    }

    private static List<AdStrategy> buildStrategyList(GroupPageVo item) {
        List<AdStrategy> strategyList = new ArrayList<>();
        for (AdStrategyVo strategyVo : item.getStrategyList()) {
            AdStrategy.Builder strategyBuilder = AdStrategy.newBuilder();
            strategyBuilder.setAdStrategyType(strategyVo.getAdStrategyType());
            strategyBuilder.setStatus(strategyVo.getStatus());
            strategyList.add(strategyBuilder.build());
        }
        return strategyList;
    }

    @AdProductPermissionFilter(
            type = PermissionFilterType.CAMPAIGN,
            shopIdsExpression = "#param.shopId",
            adTypeExpression ="#param.type",
            strategy = PermissionFilterStrategy.FILTER
    )
    @Override
    public Page<GroupPageVo> getSpDorisPageList(Integer puid, GroupPageParam param, Page<GroupPageVo> voPage, boolean isExport) {
        // 参数设置 活动id为空则返回
        if (setParam(puid, param)) return voPage;
        // 高级筛选统计个数
        if (param.getOnlyCount() != null && param.getOnlyCount()) {
            voPage.setTotalSize(amazonAdGroupDorisDao.listAmazonAdGroupAllCount(puid, param));
            return voPage;
        }
        Page<AmazonAdGroupDorisAllReport> amazonAdGroupDorisAllReportPage;
        if (isExport) {
            List<String> adGroupIdList = amazonAdGroupDorisDao.idListByAmazonAdGroup(puid, param);
            if (CollectionUtils.isEmpty(adGroupIdList)) {
                return voPage;
            }
            Map<String, AmazonAdGroupDorisAllReport> reportMap = odsAmazonAdGroupReportDao.listAmazonAdGroupReportListByIds(puid, param, adGroupIdList).stream()
                    .collect(Collectors.toMap(AmazonAdGroupDorisAllReport::getAdGroupId, Function.identity()));
            List<AmazonAdGroupDorisAllReport> voList = new ArrayList<>();
            adGroupIdList.forEach(e -> {
                AmazonAdGroupDorisAllReport report = reportMap.getOrDefault(e, this.initGroupReportData(e));
                voList.add(report);
            });
            //设置分页参数，导出只导出一页6w条数据
            amazonAdGroupDorisAllReportPage = new Page<>(voPage.getPageNo(), voPage.getPageSize(), 1, EXPORT_MAX_SIZE, voList);
        } else {
            amazonAdGroupDorisAllReportPage = amazonAdGroupDorisDao.listAmazonAdGroupPage(puid, param);
        }
        if (CollectionUtils.isNotEmpty(amazonAdGroupDorisAllReportPage.getRows())) {
            List<AmazonAdGroupDorisAllReport> poList = amazonAdGroupDorisAllReportPage.getRows();
            List<String> groupIds = poList.stream().map(AmazonAdGroupDorisAllReport::getAdGroupId).collect(Collectors.toList());
            Map<Integer, User> userMap = userDao.listByPuid(puid).stream().filter(Objects::nonNull).collect(Collectors.toMap(User::getId, e -> e));
            AdMetricDto adMetricDto = amazonAdGroupDorisDao.getSumAdMetric(puid, param);
            Map<String, AmazonAdGroup> amazonAdGroupMap = amazonAdGroupDao.getListByAdGroupIds(puid, param.getShopId(), groupIds).stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity()));
            Map<String, AmazonAdGroup> amazonAdGroupDorisMap = amazonAdGroupDorisDao.listByAdGroupIds(puid, param.getShopId(), groupIds).stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity()));
            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();  //最外层已经查了
            shopSaleDto.setSumRange(param.getShopSales());
            GroupPageVo vo;
            List<GroupPageVo> list = new ArrayList<>(poList.size());
            for (AmazonAdGroupDorisAllReport amazonAdGroupDorisAllReport : poList) {
                vo = new GroupPageVo();
                AmazonAdGroup amazonAdGroup = amazonAdGroupMap.get(amazonAdGroupDorisAllReport.getAdGroupId());
                AmazonAdGroup amazonAdGroupDoris = amazonAdGroupDorisMap.get(amazonAdGroupDorisAllReport.getAdGroupId());
                convertSpPoToPageVo(amazonAdGroup, vo, amazonAdGroupDoris, param);
                filterAdMetricData(adMetricDto, vo);
                vo.setTargetingType(vo.getAdGroupType());
                // 创建人
                if (MapUtils.isNotEmpty(userMap) && userMap.containsKey(amazonAdGroup.getCreateId())) {
                    User user = userMap.get(amazonAdGroup.getCreateId());
                    if (StringUtils.isNotBlank(user.getNickname())) {
                        vo.setCreator(user.getNickname());
                    }
                }
                cpcCommService.fillReportDataIntoPageVo(vo, buildByAmazonAdGroupDoris(amazonAdGroupDorisAllReport), shopSaleDto);
                list.add(vo);
            }
            if (isExport) {
                //填充广告信息
                this.fillAdInfo(puid, param.getShopId(), list);
            }
            voPage.setRows(list);
            voPage.setTotalSize(amazonAdGroupDorisAllReportPage.getTotalSize());
            voPage.setTotalPage(amazonAdGroupDorisAllReportPage.getTotalPage());
        }
        return voPage;
    }

    /**
     * 参数设置
     */
    private boolean setParam(Integer puid, GroupPageParam param) {
        // 标签筛选
        if(param.getAdTagId() != null){
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.GROUP.getType(), param.getAdTagId(), param.getType(),null);
            if (CollectionUtils.isNotEmpty(relationIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                param.setGroupIds(relationIds);
            } else {
                return true;
            }
        }
        if(CollectionUtils.isNotEmpty(param.getAdTagIdList())){
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.GROUP.getType(), param.getAdTagIdList(), param.getType(),null);
            if (CollectionUtils.isNotEmpty(relationIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                param.setGroupIds(relationIds);
            } else {
                return true;
            }
        }
        // 广告策略筛选
        List<Integer> operationTypeList = AdGroupStrategyTypeEnum.operationTypeList(param.getAdStrategyTypeList());
        if(CollectionUtils.isNotEmpty(operationTypeList)){
            List<String> adGroupIdList = advertiseAutoRuleStatusDao.listItemIdByAdManage(param.getPuid(), CollectionUtil.newArrayList(param.getShopId()), AutoRuleItemTypeEnum.AD_GROUP.getName(), operationTypeList, param.getType(), null, null, null);
            if(CollectionUtils.isNotEmpty(adGroupIdList)){
                param.setAutoRuleIds(adGroupIdList);
            }else{
                return true;
            }
        }
        // 广告组合筛选
        if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), param.getType(), null, null);
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                param.setCampaignIdList(campaignIds);
            } else {
                return true;
            }
        }

        Pair<Boolean, List<String>> checkProductRightPair = adProductRightService.getProductRightCampaignIdsFromGrpc(puid, param.getShopId(), CampaignTypeEnum.valueOf(param.getType().toLowerCase()));
        param.setCheckProductRightPair(checkProductRightPair);

        if (checkProductRightPair.getKey() && CollectionUtils.isEmpty(checkProductRightPair.getValue())) {
            return false;
        }

        return false;
    }

    private AmazonAdGroupReport buildByAmazonAdGroupDoris(AmazonAdGroupDorisAllReport amazonAdGroupDorisAllReport){
        AmazonAdGroupReport report = new AmazonAdGroupReport();
        report.setType("sp");
        report.setImpressions(amazonAdGroupDorisAllReport.getImpressions());
        report.setClicks(amazonAdGroupDorisAllReport.getClicks());
        report.setTotalSales(amazonAdGroupDorisAllReport.getTotalSales());  //兼容旧代码
        /**
         * TODO 广告报告重构
         * 本广告产品销售额
         */
        report.setAdSales(amazonAdGroupDorisAllReport.getAdSales());   //兼容旧代码
        report.setSaleNum(amazonAdGroupDorisAllReport.getOrderNum());
        report.setCost(amazonAdGroupDorisAllReport.getCost());
        //本广告产品订单量
        report.setAdSaleNum(amazonAdGroupDorisAllReport.getAdSaleNum());
        //广告销量
        report.setOrderNum(amazonAdGroupDorisAllReport.getSaleNum());
        //本广告产品销量
        report.setAdOrderNum(amazonAdGroupDorisAllReport.getAdOrderNum());
        return report;
    }


    @Override
    public AllGroupDataResponse.GroupHomeVo getAllGroupData(Integer puid, GroupPageParam param) {
        long t1 = Instant.now().toEpochMilli();
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        // 取店铺销售额
        BigDecimal shopSalesByData = cpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByData == null) {
            shopSalesByData = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByData);
        log.info("wade广告组取店铺销售额数据花费时间 {}", Instant.now().toEpochMilli() - t1);

        if (StringUtils.isBlank(param.getType())) {
            param.setType("sp");
        }
        //分页处理
        Page<GroupPageVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());


        //勾选正在投放按钮移除其它
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> list = StringUtil.splitStr(param.getServingStatus());
            if (list.contains("enabled")) {
                param.setServingStatus("enabled");
            }
        }


        //获取不同类型数据 sp、sd
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            getSpGroupVoList(puid, param, voPage, false);
        } else if (Constants.SD.equalsIgnoreCase(param.getType())) {
            getSdGroupVoList(puid, param, voPage, false);
        } else if (Constants.SB.equalsIgnoreCase(param.getType())) {
            getSbGroupVoList(puid, param, voPage, false);
        }

        //填充标签数据
        t1 = Instant.now().toEpochMilli();
        fillAdTagData(puid, param.getShopId(), param,voPage.getRows());
        log.info("标签数据填充花费时间 {}", Instant.now().toEpochMilli() - t1);

        //分页后填充广告信息
        log.info("wade广告组排序分页花费时间 {}", Instant.now().toEpochMilli() - t1);
        fillAdInfo(puid, param.getShopId(), voPage.getRows());
        log.info("wade广告组分页后填充报告数据花费时间 {}", Instant.now().toEpochMilli() - t1);
        // 分页后填充广告策略
        fillAdStrategy(param, voPage.getRows());

        AllGroupDataResponse.GroupHomeVo.Page.Builder pageBuilder = AllGroupDataResponse.GroupHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        List<GroupPageVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //填充环比数据
            Map<String, GroupPageVo> compareGroupMap = null;

            if (param.getIsCompare()) {
                //对比时无须高级搜索条件
                param.setUseAdvanced(false);
                // 取店铺销售额
                BigDecimal shopSalesByDataCompare = cpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
                if (shopSalesByDataCompare == null) {
                    shopSalesByDataCompare = BigDecimal.ZERO;
                }
                param.setShopSales(shopSalesByDataCompare);
                param.setStartDate(param.getCompareStartDate());
                param.setEndDate(param.getCompareEndDate());
                param.setGroupIds(rows.stream().map(GroupPageVo::getAdGroupId).collect(Collectors.toList()));
                List<GroupPageVo> spGroupPageVoListCompare = new ArrayList<>();
                if (Constants.SP.equalsIgnoreCase(param.getType())) {
                    spGroupPageVoListCompare = getSpGroupPageVoList(puid, param);
                } else if (Constants.SD.equalsIgnoreCase(param.getType())) {
                    spGroupPageVoListCompare = getSdGroupPageVoList(puid, param);
                } else if (Constants.SB.equalsIgnoreCase(param.getType())) {
                    spGroupPageVoListCompare = getSbGroupPageVoList(puid, param);
                }

                 compareGroupMap = spGroupPageVoListCompare.stream().collect(Collectors.toMap(
                        GroupPageVo::getAdGroupId, Function.identity(), (a, b) -> a));
            }

            Map<String, GroupPageVo> finalCompareGroupMap = compareGroupMap;
            boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
            List<AllGroupDataResponse.GroupHomeVo.Page.GroupPageVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                AllGroupDataResponse.GroupHomeVo.Page.GroupPageVo.Builder voBuilder = AllGroupDataResponse.GroupHomeVo.Page.GroupPageVo.newBuilder();
                if (StringUtils.isNotBlank(item.getType())) {
                    voBuilder.setType(item.getType());
                }

                if(shopAuth.getMarketplaceId() != null){
                    voBuilder.setMarketplaceId(shopAuth.getMarketplaceId());
                }
                if (item.getId() != null) {
                    voBuilder.setId(Int64Value.of(item.getId()));
                }
                if (item.getShopId() != null) {
                    voBuilder.setShopId(Int32Value.of(item.getShopId()));
                }
                if (StringUtils.isNotBlank(item.getCampaignId())) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (StringUtils.isNotBlank(item.getName())) {
                    voBuilder.setName(item.getName());
                }
                if (StringUtils.isNotBlank(item.getState())) {
                    voBuilder.setState(item.getState());
                }
                if (StringUtils.isNotBlank(item.getCampaignName())) {
                    voBuilder.setCampaignName(item.getCampaignName());
                }
                if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                    voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                }

                if (StringUtils.isNotBlank(item.getTargetingType())) {
                    voBuilder.setTargetingType(item.getTargetingType());
                }
                if (StringUtils.isNotBlank(item.getCampaignState())) {
                    voBuilder.setCampaignState(item.getCampaignState());
                }
                if (StringUtils.isNotBlank(item.getDailyBudget())) {
                    voBuilder.setDailyBudget(item.getDailyBudget());
                }
                if (StringUtils.isNotBlank(item.getAdGroupId())) {
                    voBuilder.setAdGroupId(item.getAdGroupId());
                }

                if (StringUtils.isNotBlank(item.getCreator())) {
                    voBuilder.setCreator(item.getCreator());
                }
                if (StringUtils.isNotBlank(item.getState())) {
                    voBuilder.setState(item.getState());
                }
                if (StringUtils.isNotBlank(item.getDefaultBid())) {
                    voBuilder.setDefaultBid(item.getDefaultBid());
                }
                if (item.getCreateTime() != null) {
                    voBuilder.setCreateTime(DateUtil.dateToStrWithFormat(item.getCreateTime(), "yyyy-MM-dd HH:mm"));
                }
                if (StringUtils.isNotBlank(item.getAdGroupType())) {
                    voBuilder.setAdGroupType(item.getAdGroupType());
                }
                if (item.getTargetingNum() != null) {
                    voBuilder.setTargetingNum(Int32Value.of(item.getTargetingNum()));
                }
                if (item.getAdProductNum() != null) {
                    voBuilder.setAdProductNum(Int32Value.of(item.getAdProductNum()));
                }
                if (item.getPortfolioId() != null) {
                    voBuilder.setPortfolioId(item.getPortfolioId());
                }
                if (item.getPortfolioName() != null) {
                    voBuilder.setPortfolioName(item.getPortfolioName());
                }
                if (item.getIsHidden() != null ) {
                    voBuilder.setIsHidden(item.getIsHidden());
                }

                if(StringUtils.isNotBlank(item.getBidOptimization())){
                    voBuilder.setBidOptimization(item.getBidOptimization());
                }

                if(StringUtils.isNotBlank(item.getCostType())){
                    voBuilder.setCostType(item.getCostType());
                }
                if(item.getServingStatus() != null){
                    voBuilder.setServingStatus(item.getServingStatus());
                }
                if(item.getServingStatusDec() != null){
                    voBuilder.setServingStatusDec(item.getServingStatusDec());
                }
                if(item.getServingStatusName() != null){
                    voBuilder.setServingStatusName(item.getServingStatusName());
                }
                if (item.getIsStateBidding() != null) {
                    voBuilder.setIsAdGroupBidding(item.getIsStateBidding());
                }
                if (item.getPricingStateBidding() != null) {
                    voBuilder.setPricingAdGroupBidding(item.getPricingStateBidding());
                }

                if (item.getSbType() != null) {
                    voBuilder.setSbType(item.getSbType());
                }

                if(CollectionUtils.isNotEmpty(item.getAdTags())){
                    item.getAdTags().forEach(e->{
                        AdTagVo.Builder builder = AdTagVo.newBuilder();
                        AdTagVo tagVo = builder.setId(Int64Value.of(e.getId())).setColor(e.getColor()).setName(e.getName()).build();
                        voBuilder.addAdTags(tagVo);
                    });
                }
                // 广告策略标签
                if(CollectionUtils.isNotEmpty(item.getStrategyList())){
                    voBuilder.addAllAdStrategys(buildStrategyList(item));
                }

                if(StringUtils.isNotEmpty(item.getCreativeType())){
                    voBuilder.setCreativeType(item.getCreativeType());
                }

                voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                voBuilder.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
                voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");

                /**
                 * TODO 广告报告重构
                 * sd广告vcpm类型报告特殊字段。
                 */
                //可见展示次数(VCPM专用)
                voBuilder.setViewImpressions(Int32Value.of(Optional.ofNullable(item.getViewImpressions()).orElse(0)));
                //每笔订单花费
                voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                //vcpm
                if(SBCampaignCostTypeEnum.VCPM.getCode().equals(item.getCostType())){
                    voBuilder.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0");
                }else{
                    voBuilder.setVcpm("-");
                }
                //本广告产品订单量
                voBuilder.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
                //其他产品广告订单量
                voBuilder.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
                //本广告产品销售额
                voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                //其他产品广告销售额
                voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                //广告销量
                voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
                //本广告产品销量
                voBuilder.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
                //其他产品广告销量
                if (Constants.SB.equalsIgnoreCase(param.getType())) {
                    voBuilder.setAdOtherSaleNum(Int32Value.of(0));
                } else {
                    voBuilder.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));
                }
                //“品牌新买家”订单量
                voBuilder.setOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0)));
                //“品牌新买家”订单百分比
                voBuilder.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
                //“品牌新买家”销售额
                voBuilder.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
                //“品牌新买家”销售额百分比
                voBuilder.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
                //“品牌新买家”销量
                voBuilder.setUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getUnitsOrderedNewToBrandFTD()).orElse(0)));
                //“品牌新买家”销量百分比
                voBuilder.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(item.getUnitsOrderedRateNewToBrandFTD()) ? item.getUnitsOrderedRateNewToBrandFTD() : "0");

                // 花费占比
                voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                // 销售额占比
                voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                // 订单量占比
                voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                // 销量占比
                voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");

                if (StringUtils.isNotBlank(item.getLandingPage())) {
                    voBuilder.setLandingPage(item.getLandingPage());
                }
                if (StringUtils.isNotBlank(item.getAdFormat())) {
                    voBuilder.setAdFormat(item.getAdFormat());
                }
                if (StringUtils.isNotBlank(item.getBrandEntityId())) {
                    voBuilder.setBrandEntityId(item.getBrandEntityId());
                }

                voBuilder.setNewToBrandDetailPageViews(Optional.ofNullable(item.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
                voBuilder.setAddToCart(Optional.ofNullable(item.getAddToCart()).map(String::valueOf).orElse("0"));
                voBuilder.setAddToCartRate(Optional.ofNullable(item.getAddToCartRate()).map(String::valueOf).orElse("0"));
                voBuilder.setECPAddToCart(Optional.ofNullable(item.getECPAddToCart()).map(String::valueOf).orElse("0"));
                voBuilder.setVideo5SecondViews(Optional.ofNullable(item.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
                voBuilder.setVideo5SecondViewRate(Optional.ofNullable(item.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
                voBuilder.setVideoFirstQuartileViews(Optional.ofNullable(item.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
                voBuilder.setVideoMidpointViews(Optional.ofNullable(item.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
                voBuilder.setVideoThirdQuartileViews(Optional.ofNullable(item.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
                voBuilder.setVideoCompleteViews(Optional.ofNullable(item.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
                voBuilder.setVideoUnmutes(Optional.ofNullable(item.getVideoUnmutes()).map(String::valueOf).orElse("0"));
                voBuilder.setViewabilityRate(Optional.ofNullable(item.getViewabilityRate()).map(String::valueOf).orElse("0"));
                voBuilder.setViewClickThroughRate(Optional.ofNullable(item.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
                voBuilder.setBrandedSearches(Optional.ofNullable(item.getBrandedSearches()).map(String::valueOf).orElse("0"));
                voBuilder.setDetailPageViews(Optional.ofNullable(item.getDetailPageViews()).map(String::valueOf).orElse("0"));
                voBuilder.setCumulativeReach(Optional.ofNullable(item.getCumulativeReach()).map(String::valueOf).orElse("0"));
                voBuilder.setImpressionsFrequencyAverage(Optional.ofNullable(item.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
                voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                //环比指标数据
                if (MapUtils.isNotEmpty(finalCompareGroupMap)) {
                    if (finalCompareGroupMap.containsKey(item.getAdGroupId())) {
                        GroupPageVo compareItem = finalCompareGroupMap.get(item.getAdGroupId());

                        voBuilder.setCompareImpressions(Int32Value.of(Optional.ofNullable(compareItem.getImpressions()).orElse(0)));
                        //曝光环比值
                        int impressionDiff = voBuilder.getImpressions().getValue() - voBuilder.getCompareImpressions().getValue();
                        voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions().getValue() == 0 ? "-" :
                                new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions().getValue()),
                                        2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareViewImpressions(Int32Value.of(Optional.ofNullable(compareItem.getViewImpressions()).orElse(0)));
                        //可见展示环比值
                        int viewImpressionDiff = voBuilder.getViewImpressions().getValue() - voBuilder.getCompareViewImpressions().getValue();
                        voBuilder.setCompareViewImpressionsRate(voBuilder.getCompareViewImpressions().getValue() == 0 ? "-" :
                                new BigDecimal(viewImpressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareViewImpressions().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareClicks(Int32Value.of(Optional.ofNullable(compareItem.getClicks()).orElse(0)));
                        //点击量环比值
                        int clicksDiff = voBuilder.getClicks().getValue() - voBuilder.getCompareClicks().getValue();
                        voBuilder.setCompareClicksRate(voBuilder.getCompareClicks().getValue() == 0 ? "-" :
                                new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
                        //ctr环比值
                        BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
                        voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
                        //cvr环比值
                        BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
                        voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
                        //Acos环比值
                        BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
                        voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
                        //Acots环比值
                        BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
                        voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
                        //Asots环比值
                        BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
                        voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0)));
                        //AdOrderNum环比值
                        int adOrderNumDiff = voBuilder.getAdOrderNum().getValue() - voBuilder.getCompareAdOrderNum().getValue();
                        voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
                        //AdCost环比值
                        BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
                        voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
                        //AdCostPerClick环比值
                        BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
                        voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
                        //AdSale环比值
                        BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
                        voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
                        //Roas环比值
                        BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
                        voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                        //Cpa环比值
                        BigDecimal CpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                        voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                CpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareVcpm(StringUtils.isNotBlank(compareItem.getVcpm()) ? compareItem.getVcpm() : "0");
                        //Vcpm环比值
                        if(SBCampaignCostTypeEnum.VCPM.getCode().equals(item.getCostType())){
                            BigDecimal vcpmDiff = new BigDecimal(voBuilder.getVcpm()).subtract(new BigDecimal(voBuilder.getCompareVcpm()));
                            voBuilder.setCompareVcpmRate(new BigDecimal(voBuilder.getCompareVcpm()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    vcpmDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareVcpm()), 2, RoundingMode.HALF_UP).toString());
                        }else{
                            voBuilder.setCompareVcpmRate("-");
                        }

                        voBuilder.setCompareAdSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0)));
                        //AdSaleNum比值
                        int adSaleNumDiff = voBuilder.getAdSaleNum().getValue() - voBuilder.getCompareAdSaleNum().getValue();
                        voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum().getValue() == 0 ? "-" :
                                new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0)));
                        //AdOtherOrderNum比值
                        int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum().getValue() - voBuilder.getCompareAdOtherOrderNum().getValue();
                        voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                        //AdSales环比值
                        BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                        voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                        //AdOtherSales环比值
                        BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                        voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getOrderNum()).orElse(0)));
                        //OrderNum比值
                        int orderNumDiff = voBuilder.getOrderNum().getValue() - voBuilder.getCompareOrderNum().getValue();
                        voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSelfSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0)));
                        //AdSelfSaleNum比值
                        int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum().getValue() - voBuilder.getCompareAdSelfSaleNum().getValue();
                        voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum().getValue() == 0 ? "-" :
                                new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0)));
                        //AdOtherSaleNum比值
                        int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum().getValue() - voBuilder.getCompareAdOtherSaleNum().getValue();
                        voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum().getValue() == 0 ? "-" :
                                new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getOrdersNewToBrandFTD()).orElse(0)));
                        //OrdersNewToBrandFTD比值
                        int ordersNewToBrandFTDDiff = voBuilder.getOrdersNewToBrandFTD().getValue() - voBuilder.getCompareOrdersNewToBrandFTD().getValue();
                        voBuilder.setCompareOrdersNewToBrandFTDRate(voBuilder.getCompareOrdersNewToBrandFTD().getValue() == 0 ? "-" :
                                new BigDecimal(ordersNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrdersNewToBrandFTD().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getOrderRateNewToBrandFTD()) ?
                                compareItem.getOrderRateNewToBrandFTD() : "0");
                        //OrderRateNewToBrandFTD环比值
                        BigDecimal orderRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getOrderRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()));
                        voBuilder.setCompareOrderRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                orderRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareSalesNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesNewToBrandFTD()) ?
                                compareItem.getSalesNewToBrandFTD() : "0");
                        //SalesNewToBrandFTD环比值
                        BigDecimal salesNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()));
                        voBuilder.setCompareSalesNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                salesNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareSalesRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesRateNewToBrandFTD()) ?
                                compareItem.getSalesRateNewToBrandFTD() : "0");
                        //SalesRateNewToBrandFTD环比值
                        BigDecimal salesRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()));
                        voBuilder.setCompareSalesRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                salesRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getUnitsOrderedNewToBrandFTD()).orElse(0)));
                        //UnitsOrderedNewToBrandFTD比值
                        int unitsOrderedNewToBrandFTDDiff = voBuilder.getUnitsOrderedNewToBrandFTD().getValue() - voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue();
                        voBuilder.setCompareUnitsOrderedNewToBrandFTDRate(voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue() == 0 ? "-" :
                                new BigDecimal(unitsOrderedNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getUnitsOrderedRateNewToBrandFTD()) ?
                                compareItem.getUnitsOrderedRateNewToBrandFTD() : "0");
                        //UnitsOrderedRateNewToBrandFTD环比值
                        BigDecimal UnitsOrderedRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getUnitsOrderedRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()));
                        voBuilder.setCompareUnitsOrderedRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                UnitsOrderedRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPercentage(StringUtils.isNotBlank(compareItem.getAdCostPercentage()) ?
                                compareItem.getAdCostPercentage() : "0");
                        //AdCostPercentage环比值
                        BigDecimal adCostPercentageDiff = new BigDecimal(voBuilder.getAdCostPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdCostPercentage()));
                        voBuilder.setCompareAdCostPercentageRate(new BigDecimal(voBuilder.getCompareAdCostPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPercentage()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSalePercentage(StringUtils.isNotBlank(compareItem.getAdSalePercentage()) ? compareItem.getAdSalePercentage() : "0");
                        //AdSalePercentage环比值
                        BigDecimal adSalePercentageDiff = new BigDecimal(voBuilder.getAdSalePercentage()).subtract(new BigDecimal(voBuilder.getCompareAdSalePercentage()));
                        voBuilder.setCompareAdSalePercentageRate(new BigDecimal(voBuilder.getCompareAdSalePercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSalePercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSalePercentage()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNumPercentage(StringUtils.isNotBlank(compareItem.getAdOrderNumPercentage()) ? compareItem.getAdOrderNumPercentage() : "0");
                        //AdOrderNumPercentage环比值
                        BigDecimal adOrderNumPercentageDiff = new BigDecimal(voBuilder.getAdOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()));
                        voBuilder.setCompareAdOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adOrderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNumPercentage(StringUtils.isNotBlank(compareItem.getOrderNumPercentage()) ? compareItem.getOrderNumPercentage() : "0");
                        //OrderNumPercentage环比值
                        BigDecimal orderNumPercentageDiff = new BigDecimal(voBuilder.getOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareOrderNumPercentage()));
                        voBuilder.setCompareOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                orderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareNewToBrandDetailPageViews(Optional.ofNullable(compareItem.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareNewToBrandDetailPageViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareNewToBrandDetailPageViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getNewToBrandDetailPageViews(), voBuilder.getCompareNewToBrandDetailPageViews(), 4), 100)));
                        voBuilder.setCompareAddToCart(Optional.ofNullable(compareItem.getAddToCart()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAddToCartRates(MathUtil.isNullOrZero(voBuilder.getCompareAddToCart()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAddToCart(), voBuilder.getCompareAddToCart(), 4), 100)));
                        voBuilder.setCompareAddToCartRate(Optional.ofNullable(compareItem.getAddToCartRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAddToCartRateRate(MathUtil.isNullOrZero(voBuilder.getCompareAddToCartRate()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAddToCartRate(), voBuilder.getCompareAddToCartRate(), 4), 100)));
                        voBuilder.setCompareECPAddToCart(Optional.ofNullable(compareItem.getECPAddToCart()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareECPAddToCartRate(MathUtil.isNullOrZero(voBuilder.getCompareECPAddToCart()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getECPAddToCart(), voBuilder.getCompareECPAddToCart(), 4), 100)));
                        voBuilder.setCompareVideo5SecondViews(Optional.ofNullable(compareItem.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideo5SecondViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideo5SecondViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideo5SecondViews(), voBuilder.getCompareVideo5SecondViews(), 4), 100)));
                        voBuilder.setCompareVideo5SecondViewRate(Optional.ofNullable(compareItem.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideo5SecondViewRateRate(MathUtil.isNullOrZero(voBuilder.getCompareVideo5SecondViewRate()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideo5SecondViewRate(), voBuilder.getCompareVideo5SecondViewRate(), 4), 100)));
                        voBuilder.setCompareVideoFirstQuartileViews(Optional.ofNullable(compareItem.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideoFirstQuartileViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoFirstQuartileViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoFirstQuartileViews(), voBuilder.getCompareVideoFirstQuartileViews(), 4), 100)));
                        voBuilder.setCompareVideoMidpointViews(Optional.ofNullable(compareItem.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideoMidpointViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoMidpointViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoMidpointViews(), voBuilder.getCompareVideoMidpointViews(), 4), 100)));
                        voBuilder.setCompareVideoThirdQuartileViews(Optional.ofNullable(compareItem.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideoThirdQuartileViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoThirdQuartileViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoThirdQuartileViews(), voBuilder.getCompareVideoThirdQuartileViews(), 4), 100)));
                        voBuilder.setCompareVideoCompleteViews(Optional.ofNullable(compareItem.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideoCompleteViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoCompleteViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoCompleteViews(), voBuilder.getCompareVideoCompleteViews(), 4), 100)));
                        voBuilder.setCompareVideoUnmutes(Optional.ofNullable(compareItem.getVideoUnmutes()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareVideoUnmutesRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoUnmutes()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoUnmutes(), voBuilder.getCompareVideoUnmutes(), 4), 100)));
                        voBuilder.setCompareViewabilityRate(Optional.ofNullable(compareItem.getViewabilityRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareViewabilityRateRate(MathUtil.isNullOrZero(voBuilder.getCompareViewabilityRate()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewabilityRate(), voBuilder.getCompareViewabilityRate(), 4), 100)));
                        voBuilder.setCompareViewClickThroughRate(Optional.ofNullable(compareItem.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareViewClickThroughRateRate(MathUtil.isNullOrZero(voBuilder.getCompareViewClickThroughRate()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewClickThroughRate(), voBuilder.getCompareViewClickThroughRate(), 4), 100)));
                        voBuilder.setCompareBrandedSearches(Optional.ofNullable(compareItem.getBrandedSearches()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareBrandedSearchesRate(MathUtil.isNullOrZero(voBuilder.getCompareBrandedSearches()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getBrandedSearches(), voBuilder.getCompareBrandedSearches(), 4), 100)));
                        voBuilder.setCompareDetailPageViews(Optional.ofNullable(compareItem.getDetailPageViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareDetailPageViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareDetailPageViews()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getDetailPageViews(), voBuilder.getCompareDetailPageViews(), 4), 100)));
                        voBuilder.setCompareImpressionsFrequencyAverage(Optional.ofNullable(compareItem.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareImpressionsFrequencyAverageRate(MathUtil.isNullOrZero(voBuilder.getCompareImpressionsFrequencyAverage()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getImpressionsFrequencyAverage(), voBuilder.getCompareImpressionsFrequencyAverage(), 4), 100)));
                        voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAdvertisingUnitPriceRate(MathUtil.isNullOrZero(voBuilder.getCompareAdvertisingUnitPrice()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAdvertisingUnitPrice(), voBuilder.getCompareAdvertisingUnitPrice(), 4), 100)));
                    }
                }

                if (isVc) {
                    voBuilder.setAcots("-");
                    voBuilder.setCompareAcotsRate("-");
                    voBuilder.setCompareAcots("-");
                    voBuilder.setAsots("-");
                    voBuilder.setCompareAsotsRate("-");
                    voBuilder.setCompareAsots("-");
                }
                return voBuilder.build();
            }).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }
        return AllGroupDataResponse.GroupHomeVo.newBuilder()
                .setPage(pageBuilder.buildPartial())
                .build();
    }

    private ShopAuth getShopAndCheckParam(GroupPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        // 取店铺销售额
        BigDecimal shopSalesByData = cpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByData == null) {
            shopSalesByData = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByData);
        if (StringUtils.isBlank(param.getType())) {
            param.setType("sp");
        }
        //勾选正在投放按钮移除其它
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> list = StringUtil.splitStr(param.getServingStatus());
            if (list.contains("enabled")) {
                param.setServingStatus("enabled");
            }
        }
        return shopAuth;
    }

    /**
     * 填充广告策略
     */
    public void fillAdStrategy(GroupPageParam param,List<GroupPageVo> rows){
        if(CollectionUtils.isEmpty(rows)){
            return ;
        }
        List<String> groupIds = rows.stream().map(GroupPageVo::getAdGroupId).distinct().collect(Collectors.toList());
        // 根据活动id集合获取自动化规则受控对象
        List<AdvertiseAutoRuleStatus> autoRuleStatuses = advertiseAutoRuleStatusDao.listByItemIdMutiple(param.getPuid(), CollectionUtil.newArrayList(param.getShopId()), AutoRuleItemTypeEnum.AD_GROUP.getName(), groupIds, null);
        Map<String, List<AdvertiseAutoRuleStatus>> autoRuleMap = StreamUtil.groupingBy(autoRuleStatuses, AdvertiseAutoRuleStatus::getItemId);
        for (GroupPageVo group : rows) {
            // 自动化规则标签
            if(autoRuleMap.containsKey(group.getAdGroupId())){
                List<AdStrategyVo> adstrategyList = new ArrayList<>();
                // key 标签策略 status 状态集合  一个标签存在多种状态 如果存在已开启则为开启状态 否则为暂停状态
                Map<String,List<String>> strategyMap = new HashMap<>();
                Map<Integer, List<AdvertiseAutoRuleStatus>> autoRuleOperationMap = StreamUtil.groupingBy(autoRuleMap.get(group.getAdGroupId()), AdvertiseAutoRuleStatus::getOperationType);
                for (Integer operationType : autoRuleOperationMap.keySet()) {
                    List<String> statusList = StreamUtil.toListDistinct(autoRuleOperationMap.get(operationType), AdvertiseAutoRuleStatus::getStatus);
                    String strategy = AdGroupStrategyTypeEnum.getStrategyMap().get(operationType);
                    if(StringUtils.isNotEmpty(strategy)){
                        List<String> statusAllList = strategyMap.getOrDefault(strategy, new ArrayList<>());
                        statusAllList.addAll(statusList);
                        strategyMap.put(strategy,statusAllList);
                    }
                }
                for (String strategy : strategyMap.keySet()) {
                    int status = 0;
                    List<String> statusList = strategyMap.get(strategy);
                    if(statusList.contains("ENABLED")){
                        status = 1;
                    }
                    AdStrategyVo strategyVo = new AdStrategyVo();
                    strategyVo.setAdStrategyType(strategy);
                    strategyVo.setStatus(status);
                    adstrategyList.add(strategyVo);
                }
                group.setStrategyList(adstrategyList);
            }
        }
    }

    private void fillAdTagData(Integer puid, Integer shopId,GroupPageParam param,List<GroupPageVo> rows){
        if(CollectionUtils.isEmpty(rows)){
            return ;
        }
        List<String> groups = rows.stream().map(GroupPageVo::getAdGroupId).distinct().collect(Collectors.toList());
        List<AdMarkupTagVo> relationVos = adMarkupTagDao.getRelationVos(puid, shopId, AdTagTypeEnum.GROUP.getType(), param.getType(), null, null, groups);
        if(CollectionUtils.isEmpty(relationVos)){
            return;
        }
        List<Long> collect = relationVos.stream().map(AdMarkupTagVo::getTagIds).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(collect)){
            return;
        }
        List<AdTag> byLongIdList = adTagDao.getListByLongIdList(puid, collect);
        if(CollectionUtils.isEmpty(byLongIdList)){
            return;
        }
        Map<Long, AdTag> adTagMap = byLongIdList.stream().collect(Collectors.toMap(AdTag::getId, e -> e, (e1, e2) -> e2));
        Map<String, AdMarkupTagVo> adMarkupTagVoMap = relationVos.stream().collect(Collectors.toMap(AdMarkupTagVo::getRelationId, e -> e, (e1, e2) -> e2));
        for (GroupPageVo vo : rows) {
            AdMarkupTagVo adMarkupTagVo = adMarkupTagVoMap.get(vo.getAdGroupId());
            if(adMarkupTagVo == null){
                continue;
            }
            List<Long> tagIds = adMarkupTagVo.getTagIds();
            if(tagIds == null){
                continue;
            }
            List<AdTag> collect1 = tagIds.stream().map(e -> adTagMap.get(e)).collect(Collectors.toList());
            vo.setAdTags(collect1);
        }
    }

    @Override
    public AllGroupAggregateDataResponse.GroupHomeVo getAllGroupAggregateDataNew(Integer puid, GroupPageParam param) {
        ShopAuth shopAuth = checkShopAuth(param);
        if (StringUtils.isBlank(param.getType())) {
            param.setType("sp");
        }
        List<String> reportGroupIdList;
        List<AdHomePerformancedto> reportListCompare = new ArrayList<>();
        //获取不同类型数据 sp、sd
        List<AdHomePerformancedto> reportDayList = null;  //每日汇总数据
        List<AdHomePerformancedto> latestReportList = Lists.newArrayList(); // 最新汇总数据，不受时间影响
        List<String> status = Lists.newArrayList(CpcStatusEnum.enabled.name());

        //兼容其他接口共用该方法时，缺少searchDataType导致的npe
        int searchDataType = Optional.ofNullable(param.getSearchDataType()).orElse(SearchDataTypeEnum.ALL.getCode());
        //是否查询汇总数据
        boolean searchAggregateData = SearchDataTypeEnum.containsAggregate(searchDataType);
        //是否查询图表数据
        boolean searchChartData = SearchDataTypeEnum.containsChart(searchDataType);

        int productCount = 0;
        int targetCount  = 0;
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            List<String> queryGroupId = getSpQueryIdByPageFilter(puid, param);
            if (Objects.nonNull(queryGroupId) && CollectionUtils.isEmpty(queryGroupId)) {
                reportGroupIdList = new ArrayList<>();
                reportDayList  = new ArrayList<>();
            } else {
                GroupReportDto dto = getSpGroupAggregate(puid, param, status);
                if (param.getIsCompare()) {
                    param.setStartDate(param.getCompareStartDate());
                    param.setEndDate(param.getCompareEndDate());
                    reportListCompare = Optional.of(getSpGroupAggregate(puid, param, status)).map(GroupReportDto::getReportList).orElse(Lists.newArrayList());
                }
                reportGroupIdList = dto.getIdList();
                if (searchChartData){
                    reportDayList = Optional.ofNullable(dto.getReportList()).orElse(Lists.newArrayList());
                }
                targetCount = dto.getTargetCnt();
                productCount = dto.getProductCnt();
            }
        } else if (Constants.SD.equalsIgnoreCase(param.getType())) {
            List<String> sdQueryGroupId = getSdQueryIdByPageFilter(puid, param);
            if (Objects.nonNull(sdQueryGroupId) && CollectionUtils.isEmpty(sdQueryGroupId)) {
                reportGroupIdList = new ArrayList<>();
                reportDayList  = new ArrayList<>();
            } else {
                GroupReportDto dto = getSdGroupAggregate(puid, param, status);
                if (param.getIsCompare()) {
                    param.setStartDate(param.getCompareStartDate());
                    param.setEndDate(param.getCompareEndDate());
                    reportListCompare = Optional.of(getSdGroupAggregate(puid, param, status)).map(GroupReportDto::getReportList).orElse(Lists.newArrayList());
                }
                reportGroupIdList = dto.getIdList();
                if (searchChartData){
                    reportDayList = Optional.ofNullable(dto.getReportList()).orElse(Lists.newArrayList());
                }
                latestReportList = Optional.ofNullable(dto.getLatestReportList()).orElse(Lists.newArrayList());
                targetCount = dto.getTargetCnt();
                productCount = dto.getProductCnt();
            }
        } else {
            List<String> sbQueryGroupId = getSbQueryIdByPageFilter(puid, param);
            if (Objects.nonNull(sbQueryGroupId) && CollectionUtils.isEmpty(sbQueryGroupId)) {
                reportGroupIdList = new ArrayList<>();
                reportDayList  = new ArrayList<>();
            } else {
                GroupReportDto dto = getSbGroupAggregate(puid, param, status);
                if (param.getIsCompare()) {
                    param.setStartDate(param.getCompareStartDate());
                    param.setEndDate(param.getCompareEndDate());
                    reportListCompare = Optional.of(getSbGroupAggregate(puid, param, status)).map(GroupReportDto::getReportList).orElse(Lists.newArrayList());
                }
                reportGroupIdList = dto.getIdList();
                if (searchChartData){
                    reportDayList = Optional.ofNullable(dto.getReportList()).orElse(Lists.newArrayList());
                }
                targetCount = dto.getTargetCnt();
                productCount = dto.getProductCnt();
            }
        }
        //将groupId暂存
        cpcPageIdsHandler.addIdsTemporarySynchronize(puid, reportGroupIdList, param.getPageSign(), "");
        //环比数据
        BigDecimal shopSalesByDataCompare = BigDecimal.ZERO;
        if (param.getIsCompare()) {
            shopSalesByDataCompare = cpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
            if (shopSalesByDataCompare == null) {
                shopSalesByDataCompare = BigDecimal.ZERO;
            }
        }
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());

        //汇总指标数据
        AdHomeAggregateDataRpcVo groupAggregateDataVo = getGroupAggregateDataVo(reportDayList, reportListCompare, param.getShopSales(), shopSalesByDataCompare, latestReportList, isVc);
        //将汇总占比指标总数据存入redis，给列表页接口获取算占比指标
        AdMetricCompareDto adMetricDto = new AdMetricCompareDto();
        adMetricDto.setSumOrderNum(new BigDecimal(groupAggregateDataVo.getOrderNum().getValue()));
        adMetricDto.setSumAdOrderNum(new BigDecimal(groupAggregateDataVo.getAdOrderNum().getValue()));
        adMetricDto.setSumAdSale(new BigDecimal(groupAggregateDataVo.getAdSale()));
        adMetricDto.setSumCost(new BigDecimal(groupAggregateDataVo.getAdCost()));
        //对比没有广告销量
        adMetricDto.setSumCompareAdOrderNum(new BigDecimal(groupAggregateDataVo.getCompareAdOrderNum().getValue()));
        adMetricDto.setSumCompareAdSale(new BigDecimal(groupAggregateDataVo.getCompareAdSale()));
        adMetricDto.setSumCompareCost(new BigDecimal(groupAggregateDataVo.getCompareAdCost()));
        //优化列表查询逻辑需在此处添加向redis中条件汇总指标数据
        redisService.set(Constants.GROUP_PAGE_SUM_METRIC + param.getPageSign(), JSONUtil.objectToJson(adMetricDto), Constants.KEYWORD_PAGE_SUM_METRIC_TIME);

        //sb分组字段特殊设置为0
        if(CampaignTypeEnum.sb.getCampaignType().equals(param.getType())){
            groupAggregateDataVo = groupAggregateDataVo.toBuilder()
                    .setAdSelfSaleNum(Int32Value.of(0))
                    .build();
        }

        //sd没有本产品广告销量字段，设置为0
        if(CampaignTypeEnum.sd.getCampaignType().equals(param.getType())){
            groupAggregateDataVo = groupAggregateDataVo.toBuilder()
                    .setAdSelfSaleNum(Int32Value.of(0))
                    .setAdOtherSaleNum(Int32Value.of(0))
                    .build();
        }

        groupAggregateDataVo = groupAggregateDataVo.toBuilder().setAdProductNum(Int32Value.of(productCount)).setTargetingNum(Int32Value.of(targetCount)).build();
        //查询货币类型
        String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();

        //获取chart数据
        List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, param.getStartDate(), param.getEndDate(), reportDayList, param.getShopSales());
        List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, param.getShopSales());
        List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, param.getShopSales());

        return AllGroupAggregateDataResponse.GroupHomeVo.newBuilder()
                .setAggregateDataVo(groupAggregateDataVo)
                .addAllDay(dayPerformanceVos)
                .addAllMonth(monthPerformanceVos)
                .addAllWeek(weekPerformanceVos)
                .build();
    }

    private ShopAuth checkShopAuth(GroupPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        //勾选正在投放按钮移除其它
        if(StringUtils.isNotBlank(param.getServingStatus())){
            List<String> list = StringUtil.splitStr(param.getServingStatus());
            if(list.contains("enabled")){
                param.setServingStatus("enabled");
            }
        }
        BigDecimal shopSalesByDate = cpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);
        return shopAuth;
    }

    private List<String> getSpQueryIdByPageFilter(Integer puid, GroupPageParam param) {
        List<String> groupIds = null;
        //标签筛选
        if(param.getAdTagId() != null){
            groupIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.GROUP.getType(), param.getAdTagId(), param.getType(),null);
            if (CollectionUtils.isNotEmpty(groupIds)) {
                param.setGroupIds(groupIds);
            } else {
                groupIds = Collections.emptyList();
            }
        }
        if(CollectionUtils.isNotEmpty(param.getAdTagIdList())){
            groupIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.GROUP.getType(), param.getAdTagIdList(), param.getType(),null);
            if (CollectionUtils.isNotEmpty(groupIds)) {
                param.setGroupIds(groupIds);
            } else {
                groupIds = Collections.emptyList();
            }
        }
        if(Objects.isNull(groupIds) || CollectionUtils.isNotEmpty(groupIds)){
            if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
                List<String> campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), param.getType(), null, null);
                if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                    param.setCampaignIdList(campaignIds);
                } else {
                    groupIds = Collections.emptyList();
                }
            }
        }
        return groupIds;
    }

    private List<String> getSdQueryIdByPageFilter(Integer puid, GroupPageParam param) {
        List<String> groupIds = null;
        //标签筛选
        if(param.getAdTagId() != null){
            groupIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.GROUP.getType(), param.getAdTagId(), param.getType(),null);
            if (CollectionUtils.isNotEmpty(groupIds)) {
                param.setGroupIds(groupIds);
            } else {
                groupIds = Collections.emptyList();
            }
        }
        if(CollectionUtils.isNotEmpty(param.getAdTagIdList())){
            groupIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.GROUP.getType(), param.getAdTagIdList(), param.getType(),null);
            if (CollectionUtils.isNotEmpty(groupIds)) {
                param.setGroupIds(groupIds);
            } else {
                groupIds = Collections.emptyList();
            }
        }
        if(Objects.isNull(groupIds) || CollectionUtils.isNotEmpty(groupIds)){
            if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
                List<String> campaignIds = amazonSdAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId());
                if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                    param.setCampaignIdList(campaignIds);
                } else {
                    groupIds = Collections.emptyList();
                }
            }
        }
        return groupIds;
    }

    private List<String> getSbQueryIdByPageFilter(Integer puid, GroupPageParam param) {
        List<String> groupIds = null;
        //标签筛选
        if(param.getAdTagId() != null){
            groupIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.GROUP.getType(), param.getAdTagId(), param.getType(),null);
            if (CollectionUtils.isNotEmpty(groupIds)) {
                param.setGroupIds(groupIds);
            } else {
                groupIds = Collections.emptyList();
            }
        }
        if(CollectionUtils.isNotEmpty(param.getAdTagIdList())){
            groupIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.GROUP.getType(), param.getAdTagIdList(), param.getType(),null);
            if (CollectionUtils.isNotEmpty(groupIds)) {
                param.setGroupIds(groupIds);
            } else {
                groupIds = Collections.emptyList();
            }
        }
        if(Objects.isNull(groupIds) || CollectionUtils.isNotEmpty(groupIds)){
            if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
                List<String> campaignIds = sbAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId());
                if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                    param.setCampaignIdList(campaignIds);
                } else {
                    groupIds = Collections.emptyList();
                }
            }
        }
        return groupIds;
    }

    private GroupReportDto getSpGroupAggregate(Integer puid, GroupPageParam param, List<String> status) {
        GroupReportDto dto = new GroupReportDto();
        List<String> groupIdList;
        int targetCount = 0, productCount = 0;
        //开始获取具体的汇总数据
        StopWatch sw = new StopWatch();
        sw.start("query sp group id list from table 't_amazon_ad_group_report'");
        groupIdList = amazonAdGroupReportDao.getGroupIdListByParam(puid, param);
        sw.stop();
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            sw.start("query sp group id list from table 't_amazon_ad_group'");
            //根据有报告数据的广告组id，查询t_amazon_ad_group表，并过滤查询条件
            groupIdList = amazonAdGroupDao.getGroupIdListByParamAndIds(puid, param, groupIdList);
            sw.stop();
            //得到符合查询条件的groupId，进行分片多线程查询
            log.info("query campaign id num :{}", groupIdList.size());
            log.info("partition num: {}", dynamicRefreshConfiguration.getGroupPagePartition());
            List<AdHomePerformancedto> reportDayList = Lists.newArrayList();
            sw.start("query group report data by multi thread");
            if (groupIdList.size() > dynamicRefreshConfiguration.getGroupPagePartition()) {
                Set<AdHomePerformancedto> reportResult = new HashSet<>();
                ThreadPoolExecutor groupReportPool = ThreadPoolUtil.getGroupPagePool();
                List<List<String>> groupIdPartitionList = Lists.partition(groupIdList, dynamicRefreshConfiguration.getGroupPagePartition());
                try {
                    CompletableFuture<Void> allReport = CompletableFuture.allOf(groupIdPartitionList.stream().map(l ->
                            CompletableFuture.runAsync(() -> reportResult.addAll(amazonAdGroupReportDao.getReportDataByGroupIdList(puid, param, l)), groupReportPool).exceptionally((e) -> {
                                log.error("query aggregate sp group report error", e);
                                return null;
                            })).toArray(CompletableFuture[]::new));
                    allReport.join();//等待全部分片查询完成
                } catch (Exception e) {
                    log.error("query sp group report partition error{}", e);
                    throw new BizServiceException("查询广告组报告数据异常，请联系管理员");
                }
                reportDayList.addAll(reportResult);
            } else {
                reportDayList = amazonAdGroupReportDao.getReportDataByGroupIdList(puid, param, groupIdList);
            }
            sw.stop();
            log.info(sw.prettyPrint());
            dto.setReportList(reportDayList);
            dto.setIdList(groupIdList);
        }
        //如果筛选了报告数据直接用报告数据中的groupId 如果不是则需要查询group 表
        if(AdPageAdvancedSearchUtils.isUseReportDataAdvanced(param)){
            if (CollectionUtils.isNotEmpty(dto.getIdList())) {
                //sp 广告组下的产品开启的投放数
                Integer count = amazonAdTargetDaoRoutingService.statSumCountByAdGroupId(puid, param.getShopId(), status, dto.getIdList());
                if (count != null) {
                    targetCount = count;
                }
                count = amazonAdKeywordDaoRoutingService.statSumCountByAdGroup(puid, param.getShopId(), status, dto.getIdList());
                if(count != null){
                    targetCount = targetCount + count;
                }
                //sp 广告组下的开启的广告数
                count = amazonAdProductDao.statSumCountByAdGroup(puid, param.getShopId(), status, dto.getIdList());
                if(count != null){
                    productCount = count;
                }
            }
        } else {
            //sp 广告组下的产品开启的投放数
            Integer count = amazonAdTargetDaoRoutingService.statSumCountByAdGroupPage(puid, param.getShopId(), status, param);
            if (count != null) {
                targetCount = count;
            }
            count = amazonAdKeywordDaoRoutingService.statSumCountByAdGroupPage(puid, param.getShopId(), status, param);
            if(count != null){
                targetCount = targetCount + count;
            }
            //sp 广告组下的开启的广告数
            count = amazonAdProductDao.statSumCountGroupProduct(puid,param.getShopId(),status,param);
            if(count != null){
                productCount = count;
            }
        }
        dto.setTargetCnt(targetCount);
        dto.setProductCnt(productCount);
        return dto;
    }

    private GroupReportDto getSdGroupAggregate(Integer puid, GroupPageParam param, List<String> status) {
        GroupReportDto dto = new GroupReportDto();
        List<String> groupIdList;
        StopWatch sw = new StopWatch();
        sw.start("query sd group id list from table 't_amazon_ad_sd_group_report'");
        groupIdList = amazonAdSdGroupReportDao.getSdGroupIdListByParam(puid, param);
        sw.stop();
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            sw.start("query sd group id list from table 't_amazon_ad_group_sd'");
            //根据有报告数据的广告组id，查询t_amazon_ad_group_sd表，并过滤查询条件
            groupIdList = amazonSdAdGroupDao.getSdGroupIdListByParamAndIds(puid, param, groupIdList);
            sw.stop();
            //得到符合查询条件的groupId，进行分片多线程查询
            log.info("query campaign id num :{}", groupIdList.size());
            log.info("partition num: {}", dynamicRefreshConfiguration.getGroupPagePartition());
            List<AdHomePerformancedto> reportDayList = Lists.newArrayList();
            List<AdHomePerformancedto> latestReports = Lists.newArrayList();
            if (groupIdList.size() > dynamicRefreshConfiguration.getGroupPagePartition()) {
                Set<AdHomePerformancedto> reportResult = new HashSet<>();
                List<AdHomePerformancedto> latestReportResult = Collections.synchronizedList(new ArrayList<>());
                ThreadPoolExecutor groupReportPool = ThreadPoolUtil.getGroupPagePool();
                List<List<String>> groupIdPartitionList = Lists.partition(groupIdList, dynamicRefreshConfiguration.getGroupPagePartition());
                try {
                    List<CompletableFuture<Void>> reportFutures = groupIdPartitionList.stream().map(l ->
                            CompletableFuture.runAsync(() -> reportResult.addAll(amazonAdSdGroupReportDao.getSdReportByGroupIdList(puid, param, l)), groupReportPool).exceptionally((e) -> {
                                log.error("query aggregate sd group report error", e);
                                return null;
                            })).collect(Collectors.toList());
                    reportFutures.addAll(groupIdPartitionList.stream().map(l ->
                            CompletableFuture.runAsync(() -> latestReportResult.addAll(amazonAdSdGroupReportDao.listLatestReports(puid, param.getShopId(), l, true)), groupReportPool).exceptionally((e) -> {
                                log.error("query aggregate sd group latest report error", e);
                                return null;
                            })).collect(Collectors.toList()));
                    CompletableFuture<Void> allReport = CompletableFuture.allOf(reportFutures.toArray(new CompletableFuture[0]));
                    allReport.join();//等待全部分片查询完成
                } catch (Exception e) {
                    log.error("query sd group report partition error", e);
                    throw new BizServiceException("查询sd广告组报告数据异常，请联系管理员");
                }
                reportDayList.addAll(reportResult);
                latestReports.addAll(latestReportResult);
            } else {
                reportDayList = amazonAdSdGroupReportDao.getSdReportByGroupIdList(puid, param, groupIdList);
                latestReports = amazonAdSdGroupReportDao.listLatestReports(puid, param.getShopId(), groupIdList, true);
            }
            dto.setReportList(reportDayList);
            dto.setIdList(groupIdList);
            dto.setLatestReportList(latestReports);
        }
        int targetCount = 0, productCount = 0;
        //如果筛选了报告数据直接用报告数据中的groupId 如果不是则需要查询group 表
        if(AdPageAdvancedSearchUtils.isUseReportDataAdvanced(param)){
            if(CollectionUtils.isNotEmpty(groupIdList)){
                //sd 广告组下的产品开启的投放数
                Integer count = amazonSdAdTargetingDao.statSumCountByAdGroup(puid, param.getShopId(), status, groupIdList);
                if (count != null) {
                    targetCount = count;
                }
                // sd广告组下的开启的广告数
                count = amazonSdAdProductDao.statSumCountByAdGroup(puid, param.getShopId(), status, groupIdList);
                if(count != null){
                    productCount = count;
                }
            }
        } else {
            //sd 广告组下的产品开启的投放数
            Integer count = amazonSdAdTargetingDao.statSumCountByAdGroupPage(puid, param.getShopId(), status, param);
            if (count != null) {
                targetCount = count;
            }
            // sd广告组下的开启的广告数
            count = amazonSdAdProductDao.statSumCountByAdGroupPage(puid, param.getShopId(), status, param);
            if(count != null){
                productCount = count;
            }
        }
        dto.setTargetCnt(targetCount);
        dto.setProductCnt(productCount);
        return dto;
    }

    private GroupReportDto getSbGroupAggregate(Integer puid, GroupPageParam param, List<String> status) {
        GroupReportDto dto = new GroupReportDto();
        List<String> groupIdList;
        StopWatch sw = new StopWatch();
        sw.start();
        groupIdList = amazonAdSbGroupReportDao.getSbGroupIdListByParam(puid, param);
        sw.stop();
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            sw.start();
            //根据有报告数据的广告组id，查询t_amazon_ad_group_sb表，并过滤查询条件
            groupIdList = amazonSbAdGroupDao.getSbGroupIdListByParamAndIds(puid, param, groupIdList);
            sw.stop();
            //得到符合查询条件的groupId，进行分片多线程查询
            log.info("query sb group id num :{}", groupIdList.size());
            log.info("partition num: {}", dynamicRefreshConfiguration.getGroupPagePartition());
            List<AdHomePerformancedto> reportDayList = Lists.newArrayList();
            if (groupIdList.size() > dynamicRefreshConfiguration.getGroupPagePartition()) {
                Set<AdHomePerformancedto> reportResult = new HashSet<>();
                ThreadPoolExecutor groupReportPool = ThreadPoolUtil.getGroupPagePool();
                List<List<String>> groupIdPartitionList = Lists.partition(groupIdList, dynamicRefreshConfiguration.getGroupPagePartition());
                try {
                    CompletableFuture<Void> allReport = CompletableFuture.allOf(groupIdPartitionList.stream().map(l ->
                            CompletableFuture.runAsync(() -> reportResult.addAll(amazonAdSbGroupReportDao.getSbReportByGroupIdList(puid, param, l)), groupReportPool).exceptionally((e) -> {
                                log.error("query aggregate sb group report error", e);
                                return null;
                            })).toArray(CompletableFuture[]::new));
                    allReport.join();//等待全部分片查询完成
                } catch (Exception e) {
                    log.error("query sb group report partition error{}", e);
                    throw new BizServiceException("查询sb广告组报告数据异常，请联系管理员");
                }
                reportDayList.addAll(reportResult);
            } else {
                reportDayList = amazonAdSbGroupReportDao.getSbReportByGroupIdList(puid, param, groupIdList);
            }
            dto.setReportList(reportDayList);
            dto.setIdList(groupIdList);
        }
        log.info(sw.prettyPrint());
        return dto;
    }

    @Override
    public AllGroupAggregateDataResponse.GroupHomeVo getSpGroupDorisAggregateData(Integer puid, GroupPageParam param) {
        long t1 = Instant.now().toEpochMilli();
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());


        // 取店铺销售额
        BigDecimal shopSalesByData = cpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByData == null) {
            shopSalesByData = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByData);
        log.info("wade广告组取店铺销售额数据花费时间 {}", Instant.now().toEpochMilli() - t1);

        if (StringUtils.isBlank(param.getType())) {
            param.setType("sp");
        }
        //勾选正在投放按钮移除其它
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> list = StringUtil.splitStr(param.getServingStatus());
            if (list.contains("enabled")) {
                param.setServingStatus("enabled");
            }
        }

        List<AdHomePerformancedto> reportList;
        // 最新的数据，没有环比
        List<AdHomePerformancedto> latestReports = Collections.emptyList();
        List<AdHomePerformancedto> reportListCompare = new ArrayList<>();
        //获取不同类型数据 sp、sd
        List<AdHomePerformancedto> reportDayList = null;  //每日汇总数据
        List<String> groupList = Lists.newArrayList();
        List<String> status = Lists.newArrayList(CpcStatusEnum.enabled.name());
        int productCount = 0;
        int targetCount  = 0;

        //兼容其他接口共用该方法时，缺少searchDataType导致的npe
        int searchDataType = Optional.ofNullable(param.getSearchDataType()).orElse(SearchDataTypeEnum.ALL.getCode());
        //是否查询汇总数据
        boolean searchAggregateData = SearchDataTypeEnum.containsAggregate(searchDataType);
        //是否查询图表数据
        boolean searchChartData = SearchDataTypeEnum.containsChart(searchDataType);

        AllGroupAggregateDataResponse.GroupHomeVo.Builder builder = AllGroupAggregateDataResponse.GroupHomeVo.newBuilder();

        boolean isNull = setParam(puid,param);  // 查询的数据为空

        if (isNull) {
            reportList = new ArrayList<>();
            reportDayList  = new ArrayList<>();
        } else {
            reportList = amazonAdGroupDorisDao.listTotalAmazonAdGroupGroupByAdGroupId(puid, param);
            if (param.getIsCompare()) {
                reportListCompare.add(amazonAdGroupDorisDao.getTotalAmazonAdGroupCompareData(puid, param, param.getCompareStartDate(), param.getCompareEndDate()));
            }
            groupList = reportList.stream().map(AdHomePerformancedto::getGroupId).collect(Collectors.toList());
            if (searchChartData){
                if (groupList.size() > 9000) {
                    reportDayList = this.getDorisThreadAdSpGroupAggregate(puid, param, groupList);
                } else {
                    reportDayList = amazonAdGroupDorisDao.listTotalAmazonSpAdGroupGroupDateById(puid, param, groupList);
                }
            }
            //如果筛选了报告数据直接用报告数据中的groupId 如果不是则需要查询group 表
            if (AdPageAdvancedSearchUtils.isUseReportDataAdvanced(param)) {
                //当高级筛选最小值都不大于0时，需要把为0的广告组也加上
                if (!AdPageAdvancedSearchUtils.isUseReportDataGreaterThanZeroAdvanced(param)) {
                    List<String> removeGroupIdList = odsAmazonAdGroupReportDao.idListByGroupPageParamSelAmazonAdGroupReport(puid, param, groupList);
                    groupList = amazonAdGroupDorisDao.idListByGroupPageParamSelAmazonAdGroup(puid, param);
                    if (CollectionUtils.isNotEmpty(removeGroupIdList)) {
                        groupList.removeAll(removeGroupIdList);
                    }
                }
                if (CollectionUtils.isNotEmpty(groupList)) {
                    //sp 广告组下的产品开启的投放数
                    Integer count = amazonAdTargetDaoRoutingService.statSumCountByAdGroupId(puid, param.getShopId(), status, groupList);
                    if (count != null) {
                        targetCount = count;
                    }
                    count = amazonAdKeywordDaoRoutingService.statSumCountByAdGroup(puid, param.getShopId(), status, groupList);
                    if (count != null) {
                        targetCount = targetCount + count;
                    }
                    //sp 广告组下的开启的广告数
                    count = amazonAdProductDao.statSumCountByAdGroup(puid, param.getShopId(), status, groupList);
                    if (count != null) {
                        productCount = count;
                    }
                }
            } else {
                //sp 广告组下的产品开启的投放数
                Integer count = amazonAdTargetDaoRoutingService.statSumCountByAdGroupPage(puid, param.getShopId(), status, param);
                if (count != null) {
                    targetCount = count;
                }
                count = amazonAdKeywordDaoRoutingService.statSumCountByAdGroupPage(puid, param.getShopId(), status, param);
                if (count != null) {
                    targetCount = targetCount + count;
                }
                //sp 广告组下的开启的广告数
                count = amazonAdProductDao.statSumCountGroupProduct(puid, param.getShopId(), status, param);
                if (count != null) {
                    productCount = count;
                }
            }
        }
        //将groupId暂存
        cpcPageIdsHandler.addIdsTemporarySynchronize(puid, groupList, param.getPageSign(), "");
        //环比数据
        BigDecimal shopSalesByDataCompare = BigDecimal.ZERO;
        if (param.getIsCompare()) {
            shopSalesByDataCompare = cpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
            if (shopSalesByDataCompare == null) {
                shopSalesByDataCompare = BigDecimal.ZERO;
            }
        }
        if (searchChartData){
            //查询货币类型
            String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();
            //获取chart数据
            List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, param.getStartDate(), param.getEndDate(), reportDayList, shopSalesByData);
            List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, shopSalesByData);
            List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, shopSalesByData);
            builder.addAllDay(dayPerformanceVos)
                    .addAllMonth(monthPerformanceVos)
                    .addAllWeek(weekPerformanceVos);
        }
        //汇总指标数据
        AdHomeAggregateDataRpcVo groupAggregateDataVo = getGroupAggregateDataVo(reportList, reportListCompare, shopSalesByData, shopSalesByDataCompare, latestReports, isVc);
        //sb分组字段特殊设置为0
        groupAggregateDataVo = groupAggregateDataVo.toBuilder().setAdProductNum(Int32Value.of(productCount)).setTargetingNum(Int32Value.of(targetCount)).build();

        return builder.setAggregateDataVo(groupAggregateDataVo)
                .build();
    }

    private List<AdHomePerformancedto> getDorisThreadAdSpGroupAggregate(int puid, GroupPageParam param, List<String> list){
        ThreadPoolExecutor campaignReportPool = ThreadPoolUtil.getGroupPagePool();
        List<List<String>> campaignIdPartitionList = Lists.partition(list, 9000);
        List<AdHomePerformancedto> reportResult = Collections.synchronizedList(new ArrayList<>());
        try {
            List<CompletableFuture<List<AdHomePerformancedto>>> reportFutures = new ArrayList<>(campaignIdPartitionList.size());
            campaignIdPartitionList.forEach(k -> reportFutures.add(CompletableFuture.supplyAsync(() -> amazonAdGroupDorisDao.listTotalAmazonSpAdGroupGroupDateById(puid, param, k), campaignReportPool)));
            CompletableFuture<Void> all = CompletableFuture.allOf(reportFutures.toArray(new CompletableFuture[0]));
            CompletableFuture<List<List<AdHomePerformancedto>>> results = all.thenApply(v -> reportFutures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
            results.get().stream().filter(Objects::nonNull).forEach(reportResult::addAll);
        } catch (Exception e) {
            log.error("query ad product report partition error", e);
            throw new BizServiceException("查询getDorisThreadAdSpProductAggregate数据异常，请联系管理员");
        }
        return reportResult;
    }


    @Override
    public AllGroupAggregateDataResponse.GroupHomeVo getAllGroupAggregateData(Integer puid, GroupPageParam param) {
        long t1 = Instant.now().toEpochMilli();
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());


        // 取店铺销售额
        BigDecimal shopSalesByData = cpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByData == null) {
            shopSalesByData = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByData);
        log.info("wade广告组取店铺销售额数据花费时间 {}", Instant.now().toEpochMilli() - t1);

        if (StringUtils.isBlank(param.getType())) {
            param.setType("sp");
        }
        //勾选正在投放按钮移除其它
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> list = StringUtil.splitStr(param.getServingStatus());
            if (list.contains("enabled")) {
                param.setServingStatus("enabled");
            }
        }
        List<AdHomePerformancedto> reportList;
        List<AdHomePerformancedto> reportListCompare = new ArrayList<>();
        //获取不同类型数据 sp、sd
        List<AdHomePerformancedto> reportDayList = null;  //每日汇总数据
        List<String> groupList = Lists.newArrayList();
        List<String> status = Lists.newArrayList(CpcStatusEnum.enabled.name());
        int productCount = 0;
        int targetCount  = 0;
        //兼容其他接口共用该方法时，缺少searchDataType导致的npe
        int searchDataType = Optional.ofNullable(param.getSearchDataType()).orElse(SearchDataTypeEnum.ALL.getCode());
        //是否查询图表数据
        boolean searchChartData = SearchDataTypeEnum.containsChart(searchDataType);

        AllGroupAggregateDataResponse.GroupHomeVo.Builder builder = AllGroupAggregateDataResponse.GroupHomeVo.newBuilder();
        boolean isNull = setParam(puid,param);  // 查询的数据为空
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            if (isNull) {
                reportList = new ArrayList<>();
                reportDayList  = new ArrayList<>();
            } else {

                reportList = amazonAdGroupReportDao.getSpReportByDate(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param);
                if (param.getIsCompare()) {
                    reportListCompare = amazonAdGroupReportDao.getSpReportByDate(puid, param.getShopId(), param.getCompareStartDate(), param.getCompareEndDate(), param);
                }
                groupList = reportList.stream().map(AdHomePerformancedto::getGroupId).collect(Collectors.toList());
                if (searchChartData){
                    reportDayList = amazonAdGroupReportDao.getSpReportByGroupIdList(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), groupList);
                }

                //汇总
                long t3 = Instant.now().toEpochMilli();
                //如果筛选了报告数据直接用报告数据中的groupId 如果不是则需要查询group 表
                if(AdPageAdvancedSearchUtils.isUseReportDataAdvanced(param)){
                    if (CollectionUtils.isNotEmpty(groupList)) {
                        //sp 广告组下的产品开启的投放数
                        Integer count = amazonAdTargetDaoRoutingService.statSumCountByAdGroupId(puid, param.getShopId(), status, groupList);
                        if (count != null) {
                            targetCount = count;
                        }
                        count = amazonAdKeywordDaoRoutingService.statSumCountByAdGroup(puid, param.getShopId(), status, groupList);
                        if(count != null){
                            targetCount = targetCount + count;
                        }
                        //sp 广告组下的开启的广告数
                        count = amazonAdProductDao.statSumCountByAdGroup(puid, param.getShopId(), status, groupList);
                        if(count != null){
                            productCount = count;
                        }
                    }
                } else {
                    //sp 广告组下的产品开启的投放数
                    Integer count = amazonAdTargetDaoRoutingService.statSumCountByAdGroupPage(puid, param.getShopId(), status, param);
                    if (count != null) {
                        targetCount = count;
                    }
                    count = amazonAdKeywordDaoRoutingService.statSumCountByAdGroupPage(puid, param.getShopId(), status, param);
                    if(count != null){
                        targetCount = targetCount + count;
                    }
                    //sp 广告组下的开启的广告数
                    count = amazonAdProductDao.statSumCountGroupProduct(puid,param.getShopId(),status,param);
                    if(count != null){
                        productCount = count;
                    }
                }
                log.info("pxq广告组sp汇总定位总数和产品总数时间 {}", Instant.now().toEpochMilli() - t3);
            }

        } else if (Constants.SD.equalsIgnoreCase(param.getType())) {
            if (isNull) {
                reportList = new ArrayList<>();
                reportDayList  = new ArrayList<>();
            } else {
                Pair<Boolean, List<String>> checkProductRightPair = adProductRightService.getProductRightCampaignIdsFromGrpc(puid, param.getShopId(), CampaignTypeEnum.sd);
                param.setCheckProductRightPair(checkProductRightPair);
                reportList = amazonAdSdGroupReportDao.getSdReportByDate(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, false);
                groupList = reportList.stream().map(AdHomePerformancedto::getGroupId).collect(Collectors.toList());
                if (searchChartData){
                    reportDayList = amazonAdSdGroupReportDao.getSdReportByGroupIdList(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), groupList);
                }



                long t3 = Instant.now().toEpochMilli();
                if(AdPageAdvancedSearchUtils.isUseReportDataAdvanced(param)){
                    //当高级筛选最小值都不大于0时，需要把为0的广告组也加上
                    if (!AdPageAdvancedSearchUtils.isUseReportDataGreaterThanZeroAdvanced(param)) {
                        List<String> removeGroupIdList = amazonAdSdGroupReportDao.idListByGroupPageParamSelAmazonAdGroupReport(puid, param, groupList);
                        groupList = amazonSdAdGroupDao.getList(puid, param).stream().map(AmazonSdAdGroup::getAdGroupId).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(removeGroupIdList)) {
                            groupList.removeAll(removeGroupIdList);
                        }
                    }
                    if(CollectionUtils.isNotEmpty(groupList)){
                        //sd 广告组下的产品开启的投放数
                        Integer count = amazonSdAdTargetingDao.statSumCountByAdGroup(puid, param.getShopId(), status, groupList);
                        if (count != null) {
                            targetCount = count;
                        }
                        // sd广告组下的开启的广告数
                        count = amazonSdAdProductDao.statSumCountByAdGroup(puid, param.getShopId(), status, groupList);
                        if(count != null){
                            productCount = count;
                        }
                    }
                } else {
                    param.setGroupIds(param.getTagGroupIds());
                    List<String> adGroupIds = amazonSdAdGroupDao.getAdGroupIdsByGroup(puid,param);
                    param.setGroupIds(adGroupIds);
                    //sd 广告组下的产品开启的投放数
                    Integer count = amazonSdAdTargetingDao.statSumCountByAdGroupPage(puid, param.getShopId(), status, param);
                    if (count != null) {
                        targetCount = count;
                    }
                    // sd广告组下的开启的广告数
                    count = amazonSdAdProductDao.statSumCountByAdGroupPage(puid, param.getShopId(), status, param);
                    if(count != null){
                        productCount = count;
                    }
                }
                log.info("pxq广告组sd汇总定位总数和产品总数时间 {}", Instant.now().toEpochMilli() - t3);
            }
        } else {
            if (isNull) {
                reportList = new ArrayList<>();
                reportDayList  = new ArrayList<>();
            } else {

                reportList = amazonAdSbGroupReportDao.getSbReportByDate(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param);
                reportList.forEach(e -> e.setViewImpressions(e.getViewableImpressions()));
                groupList = reportList.stream().map(AdHomePerformancedto::getGroupId).collect(Collectors.toList());
                if (searchChartData){
                    reportDayList = amazonAdSbGroupReportDao.getSbReportByGroupIdList(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), groupList);
                }

                long t3 = Instant.now().toEpochMilli();
                log.info("pxq广告组sb汇总定位总数和产品总数时间 {}", Instant.now().toEpochMilli() - t3);
            }
        }
        //将groupId暂存
        cpcPageIdsHandler.addIdsTemporarySynchronize(puid, groupList, param.getPageSign(), "");
        //环比数据
        BigDecimal shopSalesByDataCompare = BigDecimal.ZERO;
        if (param.getIsCompare()) {
            shopSalesByDataCompare = cpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
            if (shopSalesByDataCompare == null) {
                shopSalesByDataCompare = BigDecimal.ZERO;
            }
        }

        //汇总指标数据
        AdHomeAggregateDataRpcVo groupAggregateDataVo = getGroupAggregateDataVo(reportList, reportListCompare, shopSalesByData, shopSalesByDataCompare, Collections.emptyList(), isVc);
        //sb分组字段特殊设置为0
        if(CampaignTypeEnum.sb.getCampaignType().equals(param.getType())){
            groupAggregateDataVo = groupAggregateDataVo.toBuilder()
                    .setAdSelfSaleNum(Int32Value.of(0))
                    .setAdOtherSaleNum(Int32Value.of(0))
                    .build();
        }
        //sd没有本产品广告销量字段，设置为0
        if(CampaignTypeEnum.sd.getCampaignType().equals(param.getType())){
            groupAggregateDataVo = groupAggregateDataVo.toBuilder()
                    .setAdSelfSaleNum(Int32Value.of(0))
                    .setAdOtherSaleNum(Int32Value.of(0))
                    .build();
        }
        if (searchChartData){
            //查询货币类型
            String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();

            //获取chart数据
            List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, param.getStartDate(), param.getEndDate(), reportDayList, shopSalesByData);
            List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, shopSalesByData);
            List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, shopSalesByData);
            builder.addAllDay(dayPerformanceVos)
                    .addAllMonth(monthPerformanceVos)
                    .addAllWeek(weekPerformanceVos);
        }
        groupAggregateDataVo = groupAggregateDataVo.toBuilder().setAdProductNum(Int32Value.of(productCount)).setTargetingNum(Int32Value.of(targetCount)).build();

        return builder
                .setAggregateDataVo(groupAggregateDataVo)
                .build();
    }

    private void fillAdInfo(Integer puid, Integer shopId, List<GroupPageVo> rows) {
        if (CollectionUtils.isNotEmpty(rows)) {
            ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
            //sp活动信息查询
            Map<String, AmazonAdCampaignAll> spCampaignMap = null;
            Map<String, AmazonAdCampaignAll> sdCampaignMap = null;
            Map<String,AmazonAdCampaignAll> sbCampaignMap = null;
            Map<String, Integer> spAdCountMap = null;
            Map<String, Integer> sdTargetCountMap = null;
            Map<String, Integer> sdAdCountMap = null;
            Map<String, Integer> keywordCountMap = null;
            Map<String, Integer> spTargetCountMap = null;
            List<String> spAdGroupIds = rows.stream().filter(Objects::nonNull).filter(item -> Constants.SP.equalsIgnoreCase(item.getType()))
                    .filter(item -> item.getState().equals(StateEnum.enabled.getStateType()) || item.getState().equals(StateEnum.paused.getStateType()))
                    .map(GroupPageVo::getAdGroupId).collect(Collectors.toList());
            List<String> sdAdGroupIds = rows.stream().filter(Objects::nonNull).filter(item -> Constants.SD.equalsIgnoreCase(item.getType()))
                    .filter(item -> item.getState().equals(StateEnum.enabled.getStateType()) || item.getState().equals(StateEnum.paused.getStateType()))
                    .map(GroupPageVo::getAdGroupId).collect(Collectors.toList());
            List<String> status = Lists.newArrayList(CpcStatusEnum.enabled.name());

            //sp 广告组下的关键词开启的投放数
            if (CollectionUtils.isNotEmpty(spAdGroupIds)) {
                keywordCountMap = amazonAdKeywordDaoRoutingService.statCountByAdGroup(puid, shopId, status, spAdGroupIds);
            }

            //sp 广告组下的产品开启的投放数
            if (CollectionUtils.isNotEmpty(spAdGroupIds)) {
                spTargetCountMap = amazonAdTargetDaoRoutingService.statCountByAdGroup(puid, shopId, status, spAdGroupIds);
                if (MapUtils.isNotEmpty(keywordCountMap)) {
                    // 都是统计投放数合到一起
                    Map<String, Integer> finalSpTargetCountMap = spTargetCountMap;
                    keywordCountMap.forEach((key, value) -> finalSpTargetCountMap.merge(key, value, Integer::sum));
                }
            }

            //sp 广告组下的开启的广告数
            if (CollectionUtils.isNotEmpty(spAdGroupIds)) {
                spAdCountMap = amazonAdProductDao.statCountByAdGroup(puid, shopId, status, spAdGroupIds);
            }
            //sd 广告组下的产品开启的投放数
            if (CollectionUtils.isNotEmpty(sdAdGroupIds)) {
                sdTargetCountMap = amazonSdAdTargetingDao.statCountByAdGroup(puid, shopId, status, sdAdGroupIds);
            }
            // sd广告组下的开启的广告数
            if (CollectionUtils.isNotEmpty(sdAdGroupIds)) {
                sdAdCountMap = amazonSdAdProductDao.statCountByAdGroup(puid, shopId, status, sdAdGroupIds);
            }

            //广告名称和类型
            List<String> spCampaignIds = rows.stream().filter(Objects::nonNull).filter(item -> Constants.SP.equalsIgnoreCase(item.getType())).map(GroupPageVo::getCampaignId).collect(Collectors.toList());
            List<String> sdCampaignIds = rows.stream().filter(Objects::nonNull).filter(item -> Constants.SD.equalsIgnoreCase(item.getType())).map(GroupPageVo::getCampaignId).collect(Collectors.toList());
            List<String> sbCampaignIds = rows.stream().filter(Objects::nonNull).filter(item -> Constants.SB.equalsIgnoreCase(item.getType())).map(GroupPageVo::getCampaignId).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(spCampaignIds)) {

                List<AmazonAdCampaignAll> spAdCampaigns = amazonAdCampaignDao.getByCampaignIds(puid, shopId, shopAuth.getMarketplaceId(), spCampaignIds, Constants.SP);

                if (CollectionUtils.isNotEmpty(spAdCampaigns)) {
                    spCampaignMap = spAdCampaigns.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
            }
            //sd活动信息查询

            if (CollectionUtils.isNotEmpty(sdCampaignIds)) {
                List<AmazonAdCampaignAll> sdCampaignList = amazonAdCampaignDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId(), sdCampaignIds, Constants.SD);
                if (CollectionUtils.isNotEmpty(sdCampaignList)) {
                    sdCampaignMap = sdCampaignList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
            }

            if (CollectionUtils.isNotEmpty(sbCampaignIds)) {
                List<AmazonAdCampaignAll> sbCampaignList = amazonAdCampaignDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId(), sbCampaignIds, Constants.SB);
                if (CollectionUtils.isNotEmpty(sbCampaignList)) {
                    sbCampaignMap = sbCampaignList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
            }

            Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>(); //广告组合信息
            for (GroupPageVo vo : rows) {
                if (Constants.SP.equalsIgnoreCase(vo.getType())) {
                    // 广告组下的产品投放数
                    if (MapUtils.isNotEmpty(spTargetCountMap) && spTargetCountMap.containsKey(vo.getAdGroupId())) {
                        vo.setTargetingNum(spTargetCountMap.get(vo.getAdGroupId()));
                    } else {
                        vo.setTargetingNum(0);
                    }

                    // 广告组下的广告数
                    if (MapUtils.isNotEmpty(spAdCountMap) && spAdCountMap.containsKey(vo.getAdGroupId())) {
                        vo.setAdProductNum(spAdCountMap.get(vo.getAdGroupId()));
                    } else {
                        vo.setAdProductNum(0);
                    }
                }

                if (Constants.SD.equalsIgnoreCase(vo.getType())) {
                    // 广告组下的产品投放数
                    if (MapUtils.isNotEmpty(sdTargetCountMap) && sdTargetCountMap.containsKey(vo.getAdGroupId())) {
                        vo.setTargetingNum(sdTargetCountMap.get(vo.getAdGroupId()));
                    } else {
                        vo.setTargetingNum(0);
                    }

                    // 广告组下的广告数
                    if (MapUtils.isNotEmpty(sdAdCountMap) && sdAdCountMap.containsKey(vo.getAdGroupId())) {
                        vo.setAdProductNum(sdAdCountMap.get(vo.getAdGroupId()));
                    } else {
                        vo.setAdProductNum(0);
                    }
                }

                //广告信息填充
                if (Constants.SP.equalsIgnoreCase(vo.getType()) && MapUtils.isNotEmpty(spCampaignMap) && spCampaignMap.containsKey(vo.getCampaignId())) {
                    AmazonAdCampaignAll campaign = spCampaignMap.get(vo.getCampaignId());
                    vo.setCampaignName(campaign.getName());
                    vo.setCampaignTargetingType(campaign.getTargetingType());
                    vo.setCampaignState(campaign.getState());
                    vo.setDailyBudget(campaign.getBudget().toString());

                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        vo.setPortfolioId(campaign.getPortfolioId());
                        if (portfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(campaign.getPortfolioId());
                            vo.setPortfolioName(amazonAdPortfolio.getName());
                            vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                        } else {
                            AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(puid, campaign.getShopId(), campaign.getPortfolioId());
                            if (amazonAdPortfolio != null) {
                                vo.setPortfolioName(amazonAdPortfolio.getName());
                                portfolioMap.put(campaign.getPortfolioId(), amazonAdPortfolio);
                                vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                            } else {
                                vo.setPortfolioName("广告组合待同步");
                            }
                        }
                    } else {
                        vo.setPortfolioName("-");
                    }

                }

                //广告信息填充
                if (Constants.SB.equalsIgnoreCase(vo.getType()) && MapUtils.isNotEmpty(sbCampaignMap) && sbCampaignMap.containsKey(vo.getCampaignId())) {
                    AmazonAdCampaignAll campaign = sbCampaignMap.get(vo.getCampaignId());
                    vo.setCampaignName(campaign.getName());
                    vo.setCampaignState(campaign.getState());
                    vo.setDailyBudget(campaign.getBudget().toString());
                    vo.setSbType(campaign.getIsMultiAdGroupsEnabled());
                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        vo.setPortfolioId(campaign.getPortfolioId());
                        if (portfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(campaign.getPortfolioId());
                            vo.setPortfolioName(amazonAdPortfolio.getName());
                            vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                        } else {
                            AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(puid, campaign.getShopId(), campaign.getPortfolioId());
                            if (amazonAdPortfolio != null) {
                                vo.setPortfolioName(amazonAdPortfolio.getName());
                                portfolioMap.put(campaign.getPortfolioId(), amazonAdPortfolio);
                                vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                            } else {
                                vo.setPortfolioName("广告组合待同步");
                            }
                        }
                    } else {
                        vo.setPortfolioName("-");
                    }

                }
                //广告信息填充
                if (Constants.SD.equalsIgnoreCase(vo.getType()) && MapUtils.isNotEmpty(sdCampaignMap) && sdCampaignMap.containsKey(vo.getCampaignId())) {
                    AmazonAdCampaignAll amazonSdAdCampaign = sdCampaignMap.get(vo.getCampaignId());
                    vo.setCampaignName(amazonSdAdCampaign.getName());
                    vo.setCampaignTargetingType(amazonSdAdCampaign.getTactic());
                    vo.setCampaignState(amazonSdAdCampaign.getState());
                    vo.setDailyBudget(amazonSdAdCampaign.getBudget().toString());
                    if(StringUtils.isNotBlank(amazonSdAdCampaign.getCostType())){
                        vo.setCostType(amazonSdAdCampaign.getCostType());
                    }
                    if (StringUtils.isNotBlank(amazonSdAdCampaign.getPortfolioId())) {
                        vo.setPortfolioId(amazonSdAdCampaign.getPortfolioId());
                        if (portfolioMap.containsKey(amazonSdAdCampaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(amazonSdAdCampaign.getPortfolioId());
                            vo.setPortfolioName(amazonAdPortfolio.getName());
                            vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                        } else {
                            AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(puid, amazonSdAdCampaign.getShopId(), amazonSdAdCampaign.getPortfolioId());
                            if (amazonAdPortfolio != null) {
                                vo.setPortfolioName(amazonAdPortfolio.getName());
                                portfolioMap.put(amazonSdAdCampaign.getPortfolioId(), amazonAdPortfolio);
                                vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                            } else {
                                vo.setPortfolioName("广告组合待同步");
                            }
                        }
                    } else {
                        vo.setPortfolioName("-");
                    }
                }

            }
        }
    }


    /**
     * 汇总指标数据
     *
     * @param rows
     * @return
     */
    private AdHomeAggregateDataRpcVo getGroupAggregateDataVo(List<AdHomePerformancedto> rows, List<AdHomePerformancedto> rowsCompare, BigDecimal shopSales, BigDecimal shopSalesCompare, boolean isVc) {
        return this.getGroupAggregateDataVo(rows, rowsCompare, shopSales, shopSalesCompare, Collections.emptyList(), isVc);
    }

    /**
     * 汇总指标数据
     *
     * @param rows
     * @return
     */
    private AdHomeAggregateDataRpcVo getGroupAggregateDataVo(List<AdHomePerformancedto> rows, List<AdHomePerformancedto> rowsCompare, BigDecimal shopSales, BigDecimal shopSalesCompare, List<AdHomePerformancedto> latestReports, boolean isVc) {
        //点击量
        int sumClicks = rows.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNum = rows.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressions = rows.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSale = rows.stream().filter(item -> item != null && item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcost = rows.stream().filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
        /**
         * TODO 广告报告重构
         * 可见展示次数(VCPM专用)
         * 兼容 sb
         */
        //可见展示次数
        int sumViewImpressions = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getViewImpressions).filter(Objects::nonNull).reduce(0, Integer::sum);
        //本广告产品订单量
        int sumAdSaleNum = rows.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdHomePerformancedto::getAdSaleNum).sum();
        //本广告产品销售额
        BigDecimal sumAdSales = rows.stream().filter(item -> item != null && item.getAdSales() != null).map(e -> e.getAdSales()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //CPC,VCPM广告销量
        int sumSalesNum = rows.stream().filter(item -> item != null && item.getSalesNum() != null).mapToInt(AdHomePerformancedto::getSalesNum).sum();
        //CPC,VCPM-“品牌新买家”订单量
        int sumOrdersNewToBrand14d = rows.stream().filter(item -> item != null && item.getOrdersNewToBrand14d() != null).mapToInt(AdHomePerformancedto::getOrdersNewToBrand14d).sum();
        //CPC,VCPM-“品牌新买家”销售额
        BigDecimal sumSalesNewToBrand14d = rows.stream().filter(item -> item != null && item.getSalesNewToBrand14d() != null).map(e -> e.getSalesNewToBrand14d()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //CPC,VCPM-“品牌新买家”销量
        int sumUnitsOrderedNewToBrand14d = rows.stream().filter(item -> item != null && item.getUnitsOrderedNewToBrand14d() != null).mapToInt(AdHomePerformancedto::getUnitsOrderedNewToBrand14d).sum();
        //每笔订单花费
        BigDecimal sumCpa = sumAdOrderNum == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumAdOrderNum), 2, RoundingMode.HALF_UP);
        //vcpm
        //BigDecimal sumVcpm = sumViewImpressions == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumViewImpressions).multiply(new BigDecimal("1000")), 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal sumVcpm = sumViewImpressions == 0 ? BigDecimal.ZERO : MathUtil.multiplyZero(MathUtil.divideForScale(sumAdcost, new BigDecimal(sumViewImpressions), 6), BigDecimal.valueOf(1000));
        //其他产品广告订单量
        int sumAdOtherOrderNum = sumAdOrderNum - sumAdSaleNum;
        //其他产品广告销售额
        BigDecimal sumAdOtherSales = sumAdSale.subtract(sumAdSales);
        //本广告产品销量
        int sumOrderNum = rows.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(AdHomePerformancedto::getOrderNum).sum();
        //其他产品广告销量
        int sumAdOtherSaleNum = sumSalesNum - sumOrderNum;
        //“品牌新买家”订单百分比
        BigDecimal sumOrderRateNewToBrand14d = sumAdOrderNum == 0 ? BigDecimal.ZERO : new BigDecimal(sumOrdersNewToBrand14d).multiply(new BigDecimal("100")).divide(new BigDecimal(sumAdOrderNum), 2 , RoundingMode.HALF_UP);
        //“品牌新买家”销售额百分比
        BigDecimal sumSalesRateNewToBrand14d = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumSalesNewToBrand14d.multiply(new BigDecimal("100")).divide(sumAdSale, 2, RoundingMode.HALF_UP);
        //“品牌新买家”销量百分比
        BigDecimal sumUnitsOrderedRateNewToBrand14d = sumSalesNum == 0 ? BigDecimal.ZERO : new BigDecimal(sumUnitsOrderedNewToBrand14d).multiply(new BigDecimal("100")).divide(new BigDecimal(sumSalesNum), 2, RoundingMode.HALF_UP);
        //ACoS
        BigDecimal sumAcos = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(sumAdSale, 2, RoundingMode.HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClick = sumClicks == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumClicks), 2, RoundingMode.HALF_UP);
        //订单转化率
        BigDecimal sumCVr = sumClicks == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNum).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicks), 2, RoundingMode.HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtr = sumImpressions == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicks).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressions), 2, RoundingMode.HALF_UP);
        //roas
        BigDecimal roas = sumAdcost.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSale.divide(sumAdcost, 2, RoundingMode.HALF_UP);
        //acots
        BigDecimal acots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(shopSales, 2, RoundingMode.HALF_UP);//asots
        BigDecimal asots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSale.multiply(new BigDecimal("100")).divide(shopSales, 2, RoundingMode.HALF_UP);
        String sumAdCostPercentage = sumAdcost.compareTo(BigDecimal.ZERO) == 0 ? "0" : "100.0000";
        String sumAdSalePercentage = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? "0" : "100.0000";
        String sumAdOrderNumPercentage = sumAdOrderNum == 0 ? "0" : "100.0000";
        String sumOrderNumPercentage = sumSalesNum == 0 ? "0" : "100.0000";

        // “品牌新买家”观看量
        int sumNewToBrandDetailPageViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getNewToBrandDetailPageViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 加购次数
        int sumAddToCart = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getAddToCart).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 加购率
        BigDecimal sumAddToCartRate = sumImpressions == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumAddToCart, 100), sumImpressions, 2);
        // 单次加购花费
        BigDecimal sumECPAddToCart = sumAddToCart == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdcost, sumAddToCart, 2);
        // 5秒观看次数
        int sumVideo5SecondViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideo5SecondViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 5秒观看率
        BigDecimal sumVideo5SecondViewRate = sumImpressions == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumVideo5SecondViews, 100), sumImpressions, 2);
        // 视频播至1/4次数
        int sumVideoFirstQuartileViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoFirstQuartileViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频播至1/2次数
        int sumVideoMidpointViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoMidpointViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频播至3/4次数
        int sumVideoThirdQuartileViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoThirdQuartileViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频完整播放次数
        int sumVideoCompleteViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoCompleteViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频取消静音
        int sumVideoUnmutes = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoUnmutes).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 观看率
        BigDecimal sumViewabilityRate = sumImpressions == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumViewImpressions, 100), sumImpressions, 2);
        // 观看点击率
        BigDecimal sumViewClickThroughRate = sumViewImpressions == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumClicks, 100), sumViewImpressions, 2);
        // 品牌搜索次数
        int sumBrandedSearches = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getBrandedSearches).filter(Objects::nonNull).reduce(0, Integer::sum);
        // DPV
        int sumDetailPageViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getDetailPageViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 累计触达用户
        int sumCumulativeReach = latestReports.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getCumulativeReach).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 平均触达次数
        BigDecimal sumImpressionsFrequencyAverage = latestReports.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getImpressionsFrequencyAverage).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 广告笔单价,广告销售额/广告订单量*100%
        BigDecimal sumAdvertisingUnitPrice = sumAdOrderNum == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdSale, sumAdOrderNum, 2);


        //环比数据
        //点击量
        int sumClicksCompare = rowsCompare.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNumCompare = rowsCompare.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressionsCompare = rowsCompare.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSaleCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSale() != null).map(e ->e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcostCompare = rowsCompare.stream().filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
        /**
         * 可见展示次数(VCPM专用)
         * 兼容 sb
         */
        //可见展示次数
        int sumViewImpressionsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getViewImpressions).filter(Objects::nonNull).reduce(0, Integer::sum);
        //ACoS
        BigDecimal sumAcosCompare = sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcostCompare.multiply(new BigDecimal("100")).divide(sumAdSaleCompare, 2, RoundingMode.HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClickCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : sumAdcostCompare.divide(new BigDecimal(sumClicksCompare), 2, RoundingMode.HALF_UP);
        //订单转化率
        BigDecimal sumCvrCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNumCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicksCompare), 2, RoundingMode.HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtrCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicksCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressionsCompare), 2, RoundingMode.HALF_UP);
        //roas
        BigDecimal roasCompare = sumAdcostCompare.compareTo(BigDecimal.ZERO) == 0  ? BigDecimal.ZERO : sumAdSaleCompare.divide(sumAdcostCompare, 2, RoundingMode.HALF_UP);
        //acots
        BigDecimal acotsCompare = shopSalesCompare.compareTo(BigDecimal.ZERO) == 0  ? BigDecimal.ZERO : sumAdcostCompare.multiply(new BigDecimal("100")).divide(shopSalesCompare, 2, RoundingMode.HALF_UP);
        //asots
        BigDecimal asotsCompare = shopSalesCompare.compareTo(BigDecimal.ZERO) == 0  ? BigDecimal.ZERO : sumAdSaleCompare.multiply(new BigDecimal("100")).divide(shopSalesCompare, 2, RoundingMode.HALF_UP);

        // “品牌新买家”观看量
        int sumNewToBrandDetailPageViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getNewToBrandDetailPageViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 加购次数
        int sumAddToCartCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getAddToCart).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 加购率
        BigDecimal sumAddToCartRateCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumAddToCartCompare, 100), sumImpressionsCompare, 2);
        // 单次加购花费
        BigDecimal sumECPAddToCartCompare = sumAddToCartCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdcostCompare, sumAddToCartCompare, 2);
        // 5秒观看次数
        int sumVideo5SecondViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideo5SecondViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 5秒观看率
        BigDecimal sumVideo5SecondViewRateCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumVideo5SecondViewsCompare, 100), sumImpressionsCompare, 2);
        // 视频播至1/4次数
        int sumVideoFirstQuartileViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoFirstQuartileViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频播至1/2次数
        int sumVideoMidpointViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoMidpointViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频播至3/4次数
        int sumVideoThirdQuartileViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoThirdQuartileViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频完整播放次数
        int sumVideoCompleteViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoCompleteViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频取消静音
        int sumVideoUnmutesCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoUnmutes).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 观看率
        BigDecimal sumViewabilityRateCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumViewImpressionsCompare, 100), sumImpressionsCompare, 2);
        // 观看点击率
        BigDecimal sumViewClickThroughRateCompare = sumViewImpressionsCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumClicksCompare, 100), sumViewImpressionsCompare, 2);
        // 品牌搜索次数
        int sumBrandedSearchesCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getBrandedSearches).filter(Objects::nonNull).reduce(0, Integer::sum);
        // DPV
        int sumDetailPageViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getDetailPageViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 广告笔单价,广告销售额/广告订单量*100%
        BigDecimal sumAdvertisingUnitPriceCompare = sumAdOrderNumCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdSaleCompare , sumAdOrderNumCompare , 2);
        //vcpm
        BigDecimal sumVcpmCompare = sumViewImpressionsCompare == 0 ? BigDecimal.ZERO : MathUtil.multiplyZero(MathUtil.divideForScale(sumAdcostCompare, new BigDecimal(sumViewImpressionsCompare), 6), BigDecimal.valueOf(1000));
        //每笔订单花费
        BigDecimal sumCpaCompare = sumAdOrderNumCompare == 0 ? BigDecimal.ZERO : sumAdcostCompare.divide(new BigDecimal(sumAdOrderNumCompare), 2, RoundingMode.HALF_UP);
        //广告销量
        int sumSalesNumCompare = rowsCompare.stream().filter(item -> item != null && item.getSalesNum() != null).mapToInt(AdHomePerformancedto::getSalesNum).sum();
        //本广告产品订单量
        int sumAdSaleNumCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdHomePerformancedto::getAdSaleNum).sum();
        //其他产品广告订单量
        int sumAdOtherOrderNumCompare = sumAdOrderNumCompare - sumAdSaleNumCompare;
        //本广告产品销售额
        BigDecimal sumAdSalesCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSales() != null).map(AdHomePerformancedto::getAdSales).reduce(BigDecimal.ZERO, BigDecimal::add);
        //其他产品广告销售额
        BigDecimal sumAdOtherSalesCompare = sumAdSaleCompare.subtract(sumAdSalesCompare);
        //本广告产品销量
        int sumOrderNumCompare = rowsCompare.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(AdHomePerformancedto::getOrderNum).sum();
        //其他产品广告销量
        int sumAdOtherSaleNumCompare = sumSalesNumCompare - sumOrderNumCompare;
        //“品牌新买家”订单量
        int sumOrdersNewToBrand14dCompare = rowsCompare.stream().filter(item -> item != null && item.getOrdersNewToBrand14d() != null).mapToInt(AdHomePerformancedto::getOrdersNewToBrand14d).sum();
        //“品牌新买家”销售额
        BigDecimal sumSalesNewToBrand14dCompare = rowsCompare.stream().filter(item -> item != null && item.getSalesNewToBrand14d() != null).map(AdHomePerformancedto::getSalesNewToBrand14d).reduce(BigDecimal.ZERO, BigDecimal::add);
        //“品牌新买家”销量
        int sumUnitsOrderedNewToBrand14dCompare = rowsCompare.stream().filter(item -> item != null && item.getUnitsOrderedNewToBrand14d() != null).mapToInt(AdHomePerformancedto::getUnitsOrderedNewToBrand14d).sum();
        //“品牌新买家”订单百分比
        BigDecimal sumOrderRateNewToBrand14dCompare = sumAdOrderNumCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumOrdersNewToBrand14dCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumAdOrderNumCompare), 2 , RoundingMode.HALF_UP);
        //“品牌新买家”销售额百分比
        BigDecimal sumSalesRateNewToBrand14dCompare = sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumSalesNewToBrand14dCompare.multiply(new BigDecimal("100")).divide(sumAdSaleCompare, 2, RoundingMode.HALF_UP);
        //“品牌新买家”销量百分比
        BigDecimal sumUnitsOrderedRateNewToBrand14dCompare = sumSalesNumCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumUnitsOrderedNewToBrand14dCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumSalesNumCompare), 2, RoundingMode.HALF_UP);

        AdHomeAggregateDataRpcVo.Builder builder = AdHomeAggregateDataRpcVo.newBuilder()
                .setAcos(sumAcos.stripTrailingZeros().toString())
                .setRoas(roas.stripTrailingZeros().toString())
                .setAcots(acots.stripTrailingZeros().toString())
                .setAsots(asots.stripTrailingZeros().toString())
                .setAdCost(sumAdcost.stripTrailingZeros().toString())
                .setAdCostPerClick(sumAdCostPerClick.stripTrailingZeros().toString())
                .setAdOrderNum(Int32Value.of(sumAdOrderNum))
                .setCvr(sumCVr.stripTrailingZeros().toString())
                .setCtr(sumCtr.stripTrailingZeros().toString())
                .setAdSale(sumAdSale.stripTrailingZeros().toString())
                .setClicks(Int32Value.of(sumClicks))
                .setImpressions(Int32Value.of(sumImpressions))
                .setViewImpressions(Int32Value.of(sumViewImpressions))
                .setCpa(sumCpa.toString())
                .setVcpm(sumVcpm.toString())
                .setAdSaleNum(Int32Value.of(sumAdSaleNum))
                .setAdOtherOrderNum(Int32Value.of(sumAdOtherOrderNum))
                .setAdSales(sumAdSales.toString())
                .setAdOtherSales(sumAdOtherSales.toString())
                .setOrderNum(Int32Value.of(sumSalesNum))
                .setAdSelfSaleNum(Int32Value.of(sumOrderNum))
                .setAdOtherSaleNum(Int32Value.of(sumAdOtherSaleNum))
                .setOrdersNewToBrandFTD(Int32Value.of(sumOrdersNewToBrand14d))
                .setOrderRateNewToBrandFTD(sumOrderRateNewToBrand14d.toString())
                .setSalesNewToBrandFTD(sumSalesNewToBrand14d.toString())
                .setSalesRateNewToBrandFTD(sumSalesRateNewToBrand14d.toString())
                .setUnitsOrderedNewToBrandFTD(Int32Value.of(sumUnitsOrderedNewToBrand14d))
                .setUnitsOrderedRateNewToBrandFTD(sumUnitsOrderedRateNewToBrand14d.toString())
                .setAdCostPercentage(sumAdCostPercentage)
                .setAdSalePercentage(sumAdSalePercentage)
                .setAdOrderNumPercentage(sumAdOrderNumPercentage)
                .setOrderNumPercentage(sumOrderNumPercentage)
                .setNewToBrandDetailPageViews(String.valueOf(sumNewToBrandDetailPageViews))
                .setAddToCart(String.valueOf(sumAddToCart))
                .setAddToCartRate(String.valueOf(sumAddToCartRate))
                .setECPAddToCart(String.valueOf(sumECPAddToCart))
                .setVideo5SecondViews(String.valueOf(sumVideo5SecondViews))
                .setVideo5SecondViewRate(String.valueOf(sumVideo5SecondViewRate))
                .setVideoFirstQuartileViews(String.valueOf(sumVideoFirstQuartileViews))
                .setVideoMidpointViews(String.valueOf(sumVideoMidpointViews))
                .setVideoThirdQuartileViews(String.valueOf(sumVideoThirdQuartileViews))
                .setVideoCompleteViews(String.valueOf(sumVideoCompleteViews))
                .setVideoUnmutes(String.valueOf(sumVideoUnmutes))
                .setViewabilityRate(String.valueOf(sumViewabilityRate))
                .setViewClickThroughRate(String.valueOf(sumViewClickThroughRate))
                .setBrandedSearches(String.valueOf(sumBrandedSearches))
                .setDetailPageViews(String.valueOf(sumDetailPageViews))
                .setCumulativeReach(String.valueOf(sumCumulativeReach))
                .setImpressionsFrequencyAverage(String.valueOf(sumImpressionsFrequencyAverage))
                .setAdvertisingUnitPrice(String.valueOf(sumAdvertisingUnitPrice))

                //环比数据
                .setCompareAcos(sumAcosCompare.toPlainString())
                .setCompareRoas(roasCompare.toPlainString())
                .setCompareAdCost(sumAdcostCompare.toPlainString())
                .setCompareAdCostPerClick(sumAdCostPerClickCompare.toPlainString())
                .setCompareAdOrderNum(Int32Value.of(sumAdOrderNumCompare))
                .setCompareCvr(sumCvrCompare.toPlainString())
                .setCompareCtr(sumCtrCompare.toPlainString())
                .setCompareAdSale(sumAdSaleCompare.toPlainString())
                .setCompareClicks(Int32Value.of(sumClicksCompare))
                .setCompareImpressions(Int32Value.of(sumImpressionsCompare))
                .setCompareAcots(acotsCompare.toPlainString())
                .setCompareAsots(asotsCompare.toPlainString())
                .setCompareNewToBrandDetailPageViews(String.valueOf(sumNewToBrandDetailPageViewsCompare))
                .setCompareAddToCart(String.valueOf(sumAddToCartCompare))
                .setCompareAddToCartRate(String.valueOf(sumAddToCartRateCompare))
                .setCompareECPAddToCart(String.valueOf(sumECPAddToCartCompare))
                .setCompareVideo5SecondViews(String.valueOf(sumVideo5SecondViewsCompare))
                .setCompareVideo5SecondViewRate(String.valueOf(sumVideo5SecondViewRateCompare))
                .setCompareVideoFirstQuartileViews(String.valueOf(sumVideoFirstQuartileViewsCompare))
                .setCompareVideoMidpointViews(String.valueOf(sumVideoMidpointViewsCompare))
                .setCompareVideoThirdQuartileViews(String.valueOf(sumVideoThirdQuartileViewsCompare))
                .setCompareVideoCompleteViews(String.valueOf(sumVideoCompleteViewsCompare))
                .setCompareVideoUnmutes(String.valueOf(sumVideoUnmutesCompare))
                .setCompareViewImpressions(Int32Value.of(sumViewImpressionsCompare))
                .setCompareViewabilityRate(String.valueOf(sumViewabilityRateCompare))
                .setCompareViewClickThroughRate(String.valueOf(sumViewClickThroughRateCompare))
                .setCompareBrandedSearches(String.valueOf(sumBrandedSearchesCompare))
                .setCompareDetailPageViews(String.valueOf(sumDetailPageViewsCompare))
                .setCompareAdvertisingUnitPrice(String.valueOf(sumAdvertisingUnitPriceCompare))
                .setCompareVcpm(sumVcpmCompare.toPlainString())
                .setCompareCpa(sumCpaCompare.toPlainString())
                .setCompareOrderNum(Int32Value.of(sumSalesNumCompare))
                .setCompareAdSaleNum(Int32Value.of(sumAdSaleNumCompare))
                .setCompareAdOtherOrderNum(Int32Value.of(sumAdOtherOrderNumCompare))
                .setCompareAdSales(sumAdSalesCompare.toPlainString())
                .setCompareAdOtherSales(sumAdOtherSalesCompare.toPlainString())
                .setCompareAdSelfSaleNum(Int32Value.of(sumOrderNumCompare))
                .setCompareAdOtherSaleNum(Int32Value.of(sumAdOtherSaleNumCompare))
                .setCompareOrdersNewToBrandFTD(Int32Value.of(sumOrdersNewToBrand14dCompare))
                .setCompareSalesNewToBrandFTD(sumSalesNewToBrand14dCompare.toPlainString())
                .setCompareUnitsOrderedNewToBrandFTD(Int32Value.of(sumUnitsOrderedNewToBrand14dCompare))
                .setCompareOrderRateNewToBrandFTD(sumOrderRateNewToBrand14dCompare.toPlainString())
                .setCompareSalesRateNewToBrandFTD(sumSalesRateNewToBrand14dCompare.toPlainString())
                .setCompareUnitsOrderedRateNewToBrandFTD(sumUnitsOrderedRateNewToBrand14dCompare.toPlainString())

                //环比值
                .setCompareAcosRate(sumAcosCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAcos.subtract(sumAcosCompare))
                        .multiply(new BigDecimal(100)).divide(sumAcosCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareRoasRate(roasCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (roas.subtract(roasCompare))
                        .multiply(new BigDecimal(100)).divide(roasCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdCostRate(sumAdcostCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdcost.subtract(sumAdcostCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdcostCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdCostPerClickRate(sumAdCostPerClickCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdCostPerClick.subtract(sumAdCostPerClickCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdCostPerClickCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdOrderNumRate(sumAdOrderNumCompare == 0 ? "-" : new BigDecimal(sumAdOrderNum - sumAdOrderNumCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumAdOrderNumCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareCvrRate(sumCvrCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCVr.subtract(sumCvrCompare))
                        .multiply(new BigDecimal(100)).divide(sumCvrCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareCtrRate(sumCtrCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCtr.subtract(sumCtrCompare))
                        .multiply(new BigDecimal(100)).divide(sumCtrCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdSaleRate(sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdSale.subtract(sumAdSaleCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdSaleCompare, 2, RoundingMode.HALF_UP).toPlainString())


                .setCompareClicksRate(sumClicksCompare == 0 ? "-" : new BigDecimal(sumClicks - sumClicksCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumClicksCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareImpressionsRate(sumImpressionsCompare == 0 ? "-" : new BigDecimal(sumImpressions - sumImpressionsCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumImpressionsCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAcotsRate(acotsCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (acots.subtract(acotsCompare))
                        .multiply(new BigDecimal(100)).divide(acotsCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAsotsRate(asotsCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (asots.subtract(asotsCompare))
                        .multiply(new BigDecimal(100)).divide(asotsCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareNewToBrandDetailPageViewsRate(sumNewToBrandDetailPageViewsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumNewToBrandDetailPageViews, sumNewToBrandDetailPageViewsCompare, 4), 100)))
                .setCompareAddToCartRates(sumAddToCartCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAddToCart, sumAddToCartCompare, 4), 100)))
                .setCompareAddToCartRateRate(sumAddToCartRateCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAddToCartRate, sumAddToCartRateCompare, 4), 100)))
                .setCompareECPAddToCartRate(sumECPAddToCartCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumECPAddToCart, sumECPAddToCartCompare, 4), 100)))
                .setCompareVideo5SecondViewsRate(sumVideo5SecondViewsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideo5SecondViews, sumVideo5SecondViewsCompare, 4), 100)))
                .setCompareVideo5SecondViewRateRate(sumVideo5SecondViewRateCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideo5SecondViewRate, sumVideo5SecondViewRateCompare, 4), 100)))
                .setCompareVideoFirstQuartileViewsRate(sumVideoFirstQuartileViewsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoFirstQuartileViews, sumVideoFirstQuartileViewsCompare, 4), 100)))
                .setCompareVideoMidpointViewsRate(sumVideoMidpointViewsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoMidpointViews, sumVideoMidpointViewsCompare, 4), 100)))
                .setCompareVideoThirdQuartileViewsRate(sumVideoThirdQuartileViewsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoThirdQuartileViews, sumVideoThirdQuartileViewsCompare, 4), 100)))
                .setCompareVideoCompleteViewsRate(sumVideoCompleteViewsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoCompleteViews, sumVideoCompleteViewsCompare, 4), 100)))
                .setCompareVideoUnmutesRate(sumVideoUnmutesCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoUnmutes, sumVideoUnmutesCompare, 4), 100)))
                .setCompareViewImpressionsRate(sumViewImpressionsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumViewImpressions, sumViewImpressionsCompare, 4), 100)))
                .setCompareViewabilityRateRate(sumViewabilityRateCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumViewabilityRate, sumViewabilityRateCompare, 4), 100)))
                .setCompareViewClickThroughRateRate(sumViewClickThroughRateCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumViewClickThroughRate, sumViewClickThroughRateCompare, 4), 100)))
                .setCompareBrandedSearchesRate(sumBrandedSearchesCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumBrandedSearches, sumBrandedSearchesCompare, 4), 100)))
                .setCompareDetailPageViewsRate(sumDetailPageViewsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumDetailPageViews, sumDetailPageViewsCompare, 4), 100)))
                .setCompareAdvertisingUnitPriceRate(sumAdvertisingUnitPriceCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdvertisingUnitPrice, sumAdvertisingUnitPriceCompare, 4), 100)))
                .setCompareVcpmRate(sumVcpmCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumVcpm.subtract(sumVcpmCompare))
                        .multiply(new BigDecimal(100)).divide(sumVcpmCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareCpaRate(sumCpaCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCpa.subtract(sumCpaCompare))
                        .multiply(new BigDecimal(100)).divide(sumCpaCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareOrderNumRate(sumSalesNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumSalesNum, sumSalesNumCompare, 4), 100)))
                .setCompareAdSaleNumRate(sumAdSaleNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdSaleNum, sumAdSaleNumCompare, 4), 100)))
                .setCompareAdOtherOrderNumRate(sumAdOtherOrderNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdOtherOrderNum, sumAdOtherOrderNumCompare, 4), 100)))
                .setCompareAdSalesRate(sumAdSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdSales.subtract(sumAdSalesCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdSalesCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareAdOtherSalesRate(sumAdOtherSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdOtherSales.subtract(sumAdOtherSalesCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdOtherSalesCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareAdSelfSaleNumRate(sumOrderNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumOrderNum, sumOrderNumCompare, 4), 100)))
                .setCompareAdOtherSaleNumRate(sumAdOtherSaleNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdOtherSaleNum, sumAdOtherSaleNumCompare, 4), 100)))
                .setCompareOrdersNewToBrandFTDRate(sumOrdersNewToBrand14dCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumOrdersNewToBrand14d, sumOrdersNewToBrand14dCompare, 4), 100)))
                .setCompareSalesNewToBrandFTDRate(sumSalesNewToBrand14dCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumSalesNewToBrand14d.subtract(sumSalesNewToBrand14dCompare))
                        .multiply(new BigDecimal(100)).divide(sumSalesNewToBrand14dCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareUnitsOrderedNewToBrandFTDRate(sumUnitsOrderedNewToBrand14dCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumUnitsOrderedNewToBrand14d, sumUnitsOrderedNewToBrand14dCompare, 4), 100)))
                .setCompareOrderRateNewToBrandFTDRate(sumOrderRateNewToBrand14dCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumOrderRateNewToBrand14d.subtract(sumOrderRateNewToBrand14dCompare))
                        .multiply(new BigDecimal(100)).divide(sumOrderRateNewToBrand14dCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareSalesRateNewToBrandFTDRate(sumSalesRateNewToBrand14dCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumSalesRateNewToBrand14d.subtract(sumSalesRateNewToBrand14dCompare))
                        .multiply(new BigDecimal(100)).divide(sumSalesRateNewToBrand14dCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareUnitsOrderedRateNewToBrandFTDRate(sumUnitsOrderedRateNewToBrand14dCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumUnitsOrderedRateNewToBrand14d.subtract(sumUnitsOrderedRateNewToBrand14dCompare))
                        .multiply(new BigDecimal(100)).divide(sumUnitsOrderedRateNewToBrand14dCompare, 2, RoundingMode.HALF_UP).toPlainString());
        if (isVc) {
            builder.setAcots("-");
            builder.setCompareAcots("-");
            builder.setCompareAcotsRate("-");
            builder.setAsots("-");
            builder.setCompareAsots("-");
            builder.setCompareAsotsRate("-");
        }

        return builder.build();
    }

    @Override
    public List<GroupPageVo> getSpGroupVoList(Integer puid, GroupPageParam param, Page<GroupPageVo> voPage, boolean isExport) {

        List<GroupPageVo> pageVoList;
        if (isExport) { //导出
            pageVoList = getSpGroupPageVoList(puid, param);
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADOrderField(param.getOrderField(),GroupPageVo.class);
                if (isSorted) {
                    //TODO 根据名称排序
                    if ("name".equals(param.getOrderField())) {
                        PageUtil.sortedByOrderField(pageVoList, param.getOrderField(), param.getOrderType());
                    } else {
                        pageVoList = PageUtil.sort(pageVoList, param.getOrderField(), param.getOrderType());
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(pageVoList) && pageVoList.size() > 30000) {  //限制30000条，需要优化
                pageVoList =  pageVoList.subList(0, 30000);
            }
            //填充广告信息
            fillAdInfo(puid, param.getShopId(), pageVoList);
            return pageVoList;
        }


        if ((StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) || param.getUseAdvanced()) {  //页面有排序或有高级筛选 程序排序
            List<GroupPageVo> voList = getSpGroupPageVoList(puid, param);
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADOrderField(param.getOrderField(),GroupPageVo.class);
                if (isSorted) {
                    //TODO 根据名称排序
                    if ("name".equals(param.getOrderField())) {
                        PageUtil.sortedByOrderField(voList, param.getOrderField(), param.getOrderType());
                    } else {
                        voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
                    }
                }
            }
            //分页
            PageUtil.getPage(voPage, voList);
        } else {   // 走广告组管理表分页处理
            Page page = new Page(voPage.getPageNo(), voPage.getPageSize());
            page =  getSpPageList(puid, param, page);

            if (page.getTotalSize() > Constants.TOTALSIZELIMIT) {  //总数大于十万
                int totalPage = Constants.TOTALSIZELIMIT / voPage.getPageSize();
                voPage.setTotalPage(totalPage);
                voPage.setTotalSize(Constants.TOTALSIZELIMIT);
            } else {
                voPage.setTotalPage(page.getTotalPage());
                voPage.setTotalSize(page.getTotalSize());
            }
            voPage.setRows(page.getRows());
        }

        return null;
    }

    @AdProductPermissionFilter(
            type = PermissionFilterType.CAMPAIGN,
            shopIdsExpression = "#param.shopId",
            adTypeExpression ="#param.type",
            strategy = PermissionFilterStrategy.FILTER
    )
    @Override
    public List<GroupPageVo> getSdGroupVoList(Integer puid, GroupPageParam param, Page<GroupPageVo> voPage, boolean isExport) {
        List<GroupPageVo> pageVoList;
        if (isExport) { //导出
            pageVoList = getSdGroupPageVoList(puid, param);
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADOrderField(param.getOrderField(),GroupPageVo.class);
                if (isSorted) {
                    //TODO 根据名称排序
                    if ("name".equals(param.getOrderField())) {
                        PageUtil.sortedByOrderField(pageVoList, param.getOrderField(), param.getOrderType());
                    } else {
                        pageVoList = PageUtil.sort(pageVoList, param.getOrderField(), param.getOrderType());
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(pageVoList) && pageVoList.size() > EXPORT_MAX_SIZE) {  //限制60000条，需要优化
                pageVoList =  pageVoList.subList(0, EXPORT_MAX_SIZE);
            }
            //填充广告信息
            fillAdInfo(puid, param.getShopId(), pageVoList);
            return pageVoList;
        }

        if ((StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) || param.getUseAdvanced()) {  //页面有排序或有高级筛选 程序排序
            List<GroupPageVo> voList = getSdGroupPageVoList(puid, param);
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADOrderField(param.getOrderField(),GroupPageVo.class);
                if (isSorted) {
                    //TODO 根据名称排序
                    if ("name".equals(param.getOrderField())) {
                        PageUtil.sortedByOrderField(voList, param.getOrderField(), param.getOrderType());
                    } else {
                        voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
                    }
                }
            }
            //分页
            PageUtil.getPage(voPage, voList);
        } else {   // 走广告组管理表分页处理
            Page page = new Page(voPage.getPageNo(), voPage.getPageSize());
            page =  getSdPageList(puid, param, page);

            if (page.getTotalSize() > Constants.TOTALSIZELIMIT) {  //总数大于十万
                int totalPage = Constants.TOTALSIZELIMIT / voPage.getPageSize();
                voPage.setTotalPage(totalPage);
                voPage.setTotalSize(Constants.TOTALSIZELIMIT);
            } else {
                voPage.setTotalPage(page.getTotalPage());
                voPage.setTotalSize(page.getTotalSize());
            }
            voPage.setRows(page.getRows());
        }

        return null;
    }


    @AdProductPermissionFilter(
            type = PermissionFilterType.CAMPAIGN,
            shopIdsExpression = "#param.shopId",
            adTypeExpression ="#param.type",
            strategy = PermissionFilterStrategy.FILTER
    )
    @Override
    public List<GroupPageVo> getSbGroupVoList(Integer puid, GroupPageParam param, Page<GroupPageVo> voPage, boolean isExport) {
        List<GroupPageVo> pageVoList;
        if (isExport) { //导出
            pageVoList = getSbGroupPageVoList(puid, param);
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADOrderField(param.getOrderField(),GroupPageVo.class);
                if (isSorted) {
                    //TODO 根据名称排序
                    if ("name".equals(param.getOrderField())) {
                        PageUtil.sortedByOrderField(pageVoList, param.getOrderField(), param.getOrderType());
                    } else {
                        pageVoList = PageUtil.sort(pageVoList, param.getOrderField(), param.getOrderType());
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(pageVoList) && pageVoList.size() > EXPORT_MAX_SIZE) {  //限制60000条，需要优化
                pageVoList =  pageVoList.subList(0, EXPORT_MAX_SIZE);
            }
            //填充广告信息
            fillAdInfo(puid, param.getShopId(), pageVoList);
            return pageVoList;
        }

        if ((StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) || param.getUseAdvanced()) {  //页面有排序或有高级筛选 程序排序
            List<GroupPageVo> voList = getSbGroupPageVoList(puid, param);
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADOrderField(param.getOrderField(),GroupPageVo.class);
                if (isSorted) {
                    //TODO 根据名称排序
                    if ("name".equals(param.getOrderField())) {
                        PageUtil.sortedByOrderField(voList, param.getOrderField(), param.getOrderType());
                    } else {
                        voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
                    }
                }
            }
            //分页
            PageUtil.getPage(voPage, voList);
        } else {   // 走广告组管理表分页处理
            Page page = new Page(voPage.getPageNo(), voPage.getPageSize());
            page =  getSbPageList(puid, param, page);

            if (page.getTotalSize() > Constants.TOTALSIZELIMIT) {  //总数大于十万
                int totalPage = Constants.TOTALSIZELIMIT / voPage.getPageSize();
                voPage.setTotalPage(totalPage);
                voPage.setTotalSize(Constants.TOTALSIZELIMIT);
            } else {
                voPage.setTotalPage(page.getTotalPage());
                voPage.setTotalSize(page.getTotalSize());
            }
            voPage.setRows(page.getRows());
        }

        return null;
    }

    @Override
    public Result<String> createAdGroup(SPadGroupVo vo) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();

        // 查出所属的广告活动
        AmazonAdCampaignAll amazonAdCampaign = amazonAdCampaignDao.getCampaignByCampaignId(puid, shopId, vo.getCampaignId(), Constants.SP);
        if (amazonAdCampaign == null) {
            return ResultUtil.returnErr("对象不存在");
        }

        // 判断广告组名称是否存在
        if (amazonAdGroupDao.exist(puid, vo.getShopId(), amazonAdCampaign.getCampaignId(), vo.getName().trim())) {
            return ResultUtil.returnErr("名称已存在");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        AmazonAdGroup amazonAdGroup = convertVoToCreatePo(vo.getUid(), vo, amazonAdCampaign);

        GroupEntityV3 adGroup = makeAdGroupByAdGroupPo(amazonAdGroup);

        CreateSpGroupV3Response response = GroupSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createGroups(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                amazonAdCampaign.getMarketplaceId(), Lists.newArrayList(adGroup),true);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = GroupSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createGroups(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                    amazonAdCampaign.getMarketplaceId(), Lists.newArrayList(adGroup),true);
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "创建广告组失败";
        /**
         * TODO 广告组增加日志
         * 操作类型：新增广告组
         * 逻辑：调用新增广告日志方法，传空对象作为旧值
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        AdManageOperationLog adManageOperationLog = operationAdGroupLogService.getAdGroupLog(null, amazonAdGroup);
        adManageOperationLog.setIp(vo.getLoginIp());
        if (response.getData() != null && response.getData().getAdGroups() != null) {
            List<GroupSuccessResultV3> success = response.getData().getAdGroups().getSuccess();
            if (CollectionUtils.isNotEmpty(success)) {
                amazonAdGroup.setAdGroupId(success.get(0).getAdGroupId());
                adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                try {
                    amazonAdGroupDao.save(puid, amazonAdGroup);

                    //写入doris
                    saveDoris(Collections.singletonList(amazonAdGroup), true, true);

                    //记操作日志
                    adManageOperationLogs.add(adManageOperationLog);
                    adManageOperationLog.setAdGroupId(amazonAdGroup.getAdGroupId());
                    operationAdGroupLogService.printAdOperationLog(adManageOperationLogs);
                    return ResultUtil.returnSucc(amazonAdGroup.getAdGroupId());
                } catch (Exception e) {
                    log.error("createAdGroup:", e);
                }
            } else if (CollectionUtils.isNotEmpty(response.getData().getAdGroups().getError())) {
                errMsg = AmazonErrorUtils.getError(response.getData().getAdGroups().getError().get(0).getErrors().get(0).getErrorMessage());
                adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                adManageOperationLog.setResultInfo(errMsg);
            }
        } else if (response.getError() != null ) {
            if(StringUtils.isNotBlank(response.getError().getMessage())){
                errMsg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if(CollectionUtils.isNotEmpty(response.getError().getErrors())){
                errMsg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(errMsg);
        }
        adManageOperationLogs.add(adManageOperationLog);
        operationAdGroupLogService.printAdOperationLog(adManageOperationLogs);
        return ResultUtil.returnErr(errMsg);
    }

    @Override
    public Result<String> createAdGroupWithAuthed(SPadGroupVo vo, ShopAuth shop) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();

        // 查出所属的广告活动
        AmazonAdCampaignAll amazonAdCampaign = amazonAdCampaignDao.getCampaignByCampaignId(puid, shopId, vo.getCampaignId(), Constants.SP);
        if (amazonAdCampaign == null) {
            return ResultUtil.returnErr("对象不存在");
        }

        // 判断广告组名称是否存在
        if (amazonAdGroupDao.exist(puid, vo.getShopId(), amazonAdCampaign.getCampaignId(), vo.getName().trim())) {
            return ResultUtil.returnErr("名称已存在");
        }

        AmazonAdGroup amazonAdGroup = convertVoToCreatePo(vo.getUid(), vo, amazonAdCampaign);

        GroupEntityV3 adGroup = makeAdGroupByAdGroupPo(amazonAdGroup);

        CreateSpGroupV3Response response = GroupSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createGroups(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
            amazonAdCampaign.getMarketplaceId(), Lists.newArrayList(adGroup), true);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = GroupSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createGroups(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                amazonAdCampaign.getMarketplaceId(), Lists.newArrayList(adGroup), true);
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "创建广告组失败";
        /**
         * TODO 广告组增加日志
         * 操作类型：新增广告组
         * 逻辑：调用新增广告日志方法，传空对象作为旧值
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        AdManageOperationLog adManageOperationLog = operationAdGroupLogService.getAdGroupLog(null, amazonAdGroup);
        adManageOperationLog.setIp(vo.getLoginIp());
        if (response.getData() != null && response.getData().getAdGroups() != null) {
            List<GroupSuccessResultV3> success = response.getData().getAdGroups().getSuccess();
            if (CollectionUtils.isNotEmpty(success)) {
                amazonAdGroup.setAdGroupId(success.get(0).getAdGroupId());
                adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                adManageOperationLogs.add(adManageOperationLog);
                adManageOperationLog.setAdGroupId(amazonAdGroup.getAdGroupId());
                try {
                    //写入mysql
                    amazonAdGroupDao.save(puid, amazonAdGroup);
                } catch (Exception e) {
                    log.error("create group save TdSql error, ", e);
                }
                try {
                    //写入doris
                    saveDoris(Collections.singletonList(amazonAdGroup), true, true);
                } catch (Exception e) {
                    log.error("create group, save doris error:", e);
                }

                CompletableFuture.runAsync(() -> operationAdGroupLogService.printAdOperationLog(adManageOperationLogs), ThreadPoolUtil.getCreateAdForEsExecutor()).exceptionally(e -> {
                    log.error("create group, add log to es error:", e);
                    return null;
                });
                return ResultUtil.returnSucc(amazonAdGroup.getAdGroupId());
            } else if (CollectionUtils.isNotEmpty(response.getData().getAdGroups().getError())) {
                errMsg = AmazonErrorUtils.getError(response.getData().getAdGroups().getError().get(0).getErrors().get(0).getErrorMessage());
                adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                adManageOperationLog.setResultInfo(errMsg);
            }
        } else if (response.getError() != null) {
            if (StringUtils.isNotBlank(response.getError().getMessage())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if (CollectionUtils.isNotEmpty(response.getError().getErrors())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(errMsg);
        }
        adManageOperationLogs.add(adManageOperationLog);

        CompletableFuture.runAsync(() -> operationAdGroupLogService.printAdOperationLog(adManageOperationLogs), ThreadPoolUtil.getCreateAdForEsExecutor()).exceptionally(e -> {
            log.error("create group, add log to es error:", e);
            return null;
        });
        return ResultUtil.returnErr(errMsg);
    }

    @Override
    public Result updateAdGroup(SPadGroupVo vo) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();

        AmazonAdGroup oldAmazonAdGroup = amazonAdGroupDao.getByPuidAndId(puid, vo.getDxmGroupId());
        if (oldAmazonAdGroup == null) {
            return ResultUtil.error("对象不存在");
        }

        //判断活动名称是否存在
        if (!oldAmazonAdGroup.getName().equals(vo.getName()) && amazonAdGroupDao.exist(puid, shopId, oldAmazonAdGroup.getCampaignId(), vo.getName().trim())) {
            return ResultUtil.error("名称已存在");
        }

        AmazonAdGroup amazonAdGroup = new AmazonAdGroup();
        BeanUtils.copyProperties(oldAmazonAdGroup, amazonAdGroup);
        convertVoToUpdatePo(vo.getUid(), amazonAdGroup, vo);

        Result result = commUpdate(amazonAdGroup);
        /**
         * TODO 广告组增加日志
         * 操作类型：编辑广告组
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        AdManageOperationLog adManageOperationLog = operationAdGroupLogService.getAdGroupLog(oldAmazonAdGroup, amazonAdGroup);
        adManageOperationLog.setIp(vo.getLoginIp());
        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayListWithExpectedSize(2);
        if (result.success()) {
            //记操作日志
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
        }
        if (result.error()) {
            result.setMsg(result.getMsg() != null ? result.getMsg() : "操作失败");
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        adManageOperationLogs.add(adManageOperationLog);
        operationAdGroupLogService.printAdOperationLog(adManageOperationLogs);
        return result;
    }

    @Override
    public Result updateStatus(Integer puid, Integer uid, String loginIp, Long id, String state, List<Integer> authedShopIdList) {
        AmazonAdGroup oldAmazonAdGroup = amazonAdGroupDao.getByPuidAndId(puid, id);
        if (oldAmazonAdGroup == null) {
            return ResultUtil.error("对象不存在");
        }
        if (CollectionUtils.isEmpty(authedShopIdList) || !authedShopIdList.contains(oldAmazonAdGroup.getShopId())) {
            return ResultUtil.error("未授权");
        }
        AmazonAdGroup amazonAdGroup = new AmazonAdGroup();
        BeanUtils.copyProperties(oldAmazonAdGroup, amazonAdGroup);

        String oldState = oldAmazonAdGroup.getState();
        if (oldState.equals(state)) {
            return ResultUtil.success();
        }

        oldAmazonAdGroup.setState(state);
        oldAmazonAdGroup.setUpdateId(uid);

        Result result = commUpdate(oldAmazonAdGroup);
        /**
         * TODO 广告组增加日志
         * 操作类型：编辑广告组状态
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        AdManageOperationLog adManageOperationLog = operationAdGroupLogService.getAdGroupLog(amazonAdGroup, oldAmazonAdGroup);
        adManageOperationLog.setIp(loginIp);
        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayListWithExpectedSize(2);
        if (result.success()) {
            //记操作日志
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
        }
        if (result.error()) {
            result.setMsg(result.getMsg() != null ? result.getMsg() : "操作失败");
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        adManageOperationLogs.add(adManageOperationLog);
        operationAdGroupLogService.printAdOperationLog(adManageOperationLogs);
        return result;
    }

    @Override
    public Result updateName(Integer puid, Integer uid, String loginIp, Long id, String name) {
        AmazonAdGroup oldAmazonAdGroup = amazonAdGroupDao.getByPuidAndId(puid, id);
        if (oldAmazonAdGroup == null) {
            return ResultUtil.error("对象不存在");
        }
        AmazonAdGroup amazonAdGroup = new AmazonAdGroup();
        BeanUtils.copyProperties(oldAmazonAdGroup, amazonAdGroup);


        //判断名称是否存在
        String oldName = oldAmazonAdGroup.getName();
        if (!oldName.equalsIgnoreCase(name)) {
            if (amazonAdGroupDao.exist(puid, oldAmazonAdGroup.getShopId(), oldAmazonAdGroup.getCampaignId(), name.trim())) {
                return ResultUtil.error("名称已存在");
            }

            oldAmazonAdGroup.setName(name.trim());
        }

        oldAmazonAdGroup.setUpdateId(uid);

        Result result = commUpdate(oldAmazonAdGroup);
        /**
         * TODO 广告组增加日志
         * 操作类型：编辑广告组名称
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        AdManageOperationLog adManageOperationLog = operationAdGroupLogService.getAdGroupLog(amazonAdGroup, oldAmazonAdGroup);
        adManageOperationLog.setIp(loginIp);
        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayListWithExpectedSize(2);
        if (result.success()) {
            //记操作日志
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
        }
        if (result.error()) {
            result.setMsg(result.getMsg() != null ? result.getMsg() : "操作失败");
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        adManageOperationLogs.add(adManageOperationLog);
        operationAdGroupLogService.printAdOperationLog(adManageOperationLogs);
        return result;
    }

    @Override
    public Result updateBid(Integer puid, Integer uid, String loginIp, Long id, Double bid, List<Integer> authedShopIdList) {
        AmazonAdGroup oldAmazonAdGroup = amazonAdGroupDao.getByPuidAndId(puid, id);
        if (oldAmazonAdGroup == null) {
            return ResultUtil.error("对象不存在");
        }
        if (CollectionUtils.isEmpty(authedShopIdList) || !authedShopIdList.contains(oldAmazonAdGroup.getShopId())) {
            return ResultUtil.error("未授权");
        }
        AmazonAdGroup adGroup = new AmazonAdGroup();
        BeanUtils.copyProperties(oldAmazonAdGroup, adGroup);

        Double oldBid = oldAmazonAdGroup.getDefaultBid();
        if (Objects.equals(oldBid, bid)) {
            return ResultUtil.success();
        }

        oldAmazonAdGroup.setDefaultBid(bid);
        oldAmazonAdGroup.setUpdateId(uid);

        Result result = commUpdate(oldAmazonAdGroup);
        /**
         * TODO 广告组增加日志
         * 操作类型：编辑广告组竞价
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        AdManageOperationLog adManageOperationLog = operationAdGroupLogService.getAdGroupLog(adGroup, oldAmazonAdGroup);
        adManageOperationLog.setIp(loginIp);
        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayListWithExpectedSize(2);
        if (result.success()) {
            //记操作日志
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
        }
        if (result.error()) {
            result.setMsg(result.getMsg() != null ? result.getMsg() : "操作失败");
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        adManageOperationLogs.add(adManageOperationLog);
        operationAdGroupLogService.printAdOperationLog(adManageOperationLogs);
        return result;
    }

    @Override
    public Result archive(Integer puid, Integer uid, String loginIp, Long id) {
        AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByPuidAndId(puid, id);
        if (amazonAdGroup == null) {
            return ResultUtil.error("对象不存在");
        }
        /**
         * TODO 广组告增加日志
         * 操作类型：归档广告组
         * 逻辑：创建新对象归档状态修改，比较归档状态
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayListWithExpectedSize(2);
        AmazonAdGroup newAmazonAdGroup = new AmazonAdGroup();
        BeanUtils.copyProperties(amazonAdGroup, newAmazonAdGroup);
        newAmazonAdGroup.setState(CpcStatusEnum.archived.name());
        newAmazonAdGroup.setUpdateId(uid);
        AdManageOperationLog adManageOperationLog = operationAdGroupLogService.getAdGroupLog(amazonAdGroup, newAmazonAdGroup);
        Result result = cpcAdGroupApiService.archive(amazonAdGroup);
        if (result.success()) {
            amazonAdGroup.setUpdateId(uid);
            amazonAdGroup.setState(CpcStatusEnum.archived.name());
            amazonAdGroupDao.updateById(puid, amazonAdGroup);
            saveDoris(Collections.singletonList(amazonAdGroup), false, true);
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
        }
        if (result.error()) {
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        adManageOperationLogs.add(adManageOperationLog);
        operationAdGroupLogService.printAdOperationLog(adManageOperationLogs);
        return result;
    }

    @Override
    public Result<List<GroupNameVo>> listNames(int puid, Integer shopId, String campaignId, String adaptType) {
        if (StringUtils.isBlank(campaignId) || StringUtils.isBlank(adaptType)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        List<GroupNameVo> nameVos = new ArrayList<>();

        List<String> adGroupTypes = null;
        if (Constants.CPC_CAMPAIGN_ADAPT_TYPE_KEYWORD.equalsIgnoreCase(adaptType)) {
            adGroupTypes = Lists.newArrayList(Constants.GROUP_TYPE_KEYWORD);
        } else if (Constants.CPC_CAMPAIGN_ADAPT_TYPE_NE_KEYWORD.equalsIgnoreCase(adaptType)) {
            adGroupTypes = Lists.newArrayList(Constants.GROUP_TYPE_KEYWORD, Constants.GROUP_TYPE_AUTO);
        } else if (Constants.CPC_CAMPAIGN_ADAPT_TYPE_TARGET.equalsIgnoreCase(adaptType)) {
            adGroupTypes = Lists.newArrayList(Constants.GROUP_TYPE_TARGETING);
        } else if (Constants.CPC_CAMPAIGN_ADAPT_TYPE_NE_TARGET.equalsIgnoreCase(adaptType)) {
            adGroupTypes = Lists.newArrayList(Constants.GROUP_TYPE_TARGETING, Constants.GROUP_TYPE_AUTO);
        }
        if (CollectionUtils.isNotEmpty(adGroupTypes)) {
            List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.getListByCampaignId(puid, shopId, campaignId, adGroupTypes);
            nameVos = amazonAdGroups.stream().filter(e -> CpcStatusEnum.validList().contains(e.getState())).map(GroupNameVo::new).collect(Collectors.toList());
        }

        return ResultUtil.returnSucc(nameVos);
    }

    @Override
    public Result<AdPerformanceVo> showGroupPerformance(int puid, AdPerformanceParam param) {
        if (param.getShopId() == null
                || StringUtils.isBlank(param.getGroupId())) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByAdGroupId(puid, param.getShopId(), param.getGroupId());
        if (amazonAdGroup == null) {
            return ResultUtil.returnErr("没有活动信息");
        }

        // 拼装返回的数据VO
        AdPerformanceVo adPerformanceVo = new AdPerformanceVo();
        adPerformanceVo.setShopId(amazonAdGroup.getShopId());
        adPerformanceVo.setCampaignId(amazonAdGroup.getCampaignId());
        adPerformanceVo.setGroupId(amazonAdGroup.getAdGroupId());

        // 初始化每天数据，应前端要求保证日期是连续的
        Map<String, CpcCommPageVo> map = new LinkedHashMap<>();
        adPerformanceVo.setMap(map);
        LocalDate startLocalDate = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        LocalDate endLocalDate = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        while (startLocalDate.equals(endLocalDate) || startLocalDate.isBefore(endLocalDate)) {
            map.put(startLocalDate.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)), new CpcCommPageVo());
            startLocalDate = startLocalDate.plusDays(1);
        }

        List<AmazonAdGroupReport> reports = amazonAdGroupReportDao.listReports(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param.getGroupId());

        if (CollectionUtils.isNotEmpty(reports)) {
            // 取店铺销售额
            ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(param.getShopId(), param.getStartDate(), param.getEndDate());
            ShopSaleDto shopSaleDto1 = shopSaleDto;

            Map<String, CpcCommPageVo> resultMap = reports.stream().collect(Collectors.toMap(
                    e -> LocalDate.parse(e.getCountDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)),
                    e -> {
                        CpcCommPageVo campaignPageVo = new CpcCommPageVo();

                        // 填充报告数据
                        cpcCommService.fillReportDataIntoPageVo(campaignPageVo, e, shopSaleDto1);
                        return campaignPageVo;

                    }, (p1, p2) -> p1));

            adPerformanceVo.getMap().putAll(resultMap);
        }

        return ResultUtil.returnSucc(adPerformanceVo);
    }


    private Page getSpPageList(Integer puid, GroupPageParam param, Page page) {
        List<GroupPageVo> voList = Lists.newArrayList();

        if(param.getAdTagId() != null){
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.GROUP.getType(), param.getAdTagId(), param.getType(),null);
            if (CollectionUtils.isNotEmpty(relationIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                param.setGroupIds(relationIds);
            } else {
                return page;
            }
        }

        if(CollectionUtils.isNotEmpty(param.getAdTagIdList())){
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.GROUP.getType(), param.getAdTagIdList(), param.getType(),null);
            if (CollectionUtils.isNotEmpty(relationIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                param.setGroupIds(relationIds);
            } else {
                return page;
            }
        }

        if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), Constants.SP, null, null);
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                param.setCampaignIdList(campaignIds);
            } else {
                return page;
            }
        }

        long start = System.currentTimeMillis();
        page = amazonAdGroupDao.getPageList(puid, param, page);
        log.info(" ==============================广告组 sp 分页数据花费时间 {} ==============================", System.currentTimeMillis() - start);

        List<AmazonAdGroup> poList = page.getRows();
        if (CollectionUtils.isNotEmpty(poList)) {

            List<String> groupIds = poList.stream().map(AmazonAdGroup::getAdGroupId).collect(Collectors.toList());

            long start2 = System.currentTimeMillis();
            Map<Integer, User> userMap = userDao.listByPuid(puid).stream().filter(Objects::nonNull).collect(Collectors.toMap(User::getId, e -> e));

            List<AdHomePerformancedto> list = null;
            AdMetricDto adMetricDto = new AdMetricDto();

            list = amazonAdGroupReportDao.listSumReportByGroupIds(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, groupIds);

            adMetricDto = amazonAdGroupReportDao.getSumMetric(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param);

            log.info(" ==============================广告组 sp 报告数据花费时间 {} ==============================", System.currentTimeMillis() - start2);

            Map<String, AdHomePerformancedto> groupReportMap = list.stream().filter(Objects::nonNull).collect(Collectors.toMap(AdHomePerformancedto::getGroupId, e -> e));

            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();  //最外层已经查了
            shopSaleDto.setSumRange(param.getShopSales());

            GroupPageVo vo;
            page.setRows(voList);
            long start3 = System.currentTimeMillis();
            for (AmazonAdGroup amazonAdGroup : poList) {
                vo = new GroupPageVo();
                voList.add(vo);
                convertSpPoToPageVo(amazonAdGroup, vo);
                filterAdMetricData(adMetricDto, vo);
                vo.setTargetingType(vo.getAdGroupType());

                // 创建人
                if (MapUtils.isNotEmpty(userMap) && userMap.containsKey(amazonAdGroup.getCreateId())) {
                    User user = userMap.get(amazonAdGroup.getCreateId());
                    if (StringUtils.isNotBlank(user.getNickname())) {
                        vo.setCreator(user.getNickname());
                    }
                }
                if (groupReportMap.size() > 0) {
                    AdHomePerformancedto adHomePerformancedto = groupReportMap.get(amazonAdGroup.getAdGroupId());
                    if (adHomePerformancedto != null) {
                        AmazonAdGroupReport report = new AmazonAdGroupReport();
                        report.setType(adHomePerformancedto.getType());
                        report.setImpressions(adHomePerformancedto.getImpressions());
                        report.setClicks(adHomePerformancedto.getClicks());
                        report.setTotalSales(adHomePerformancedto.getAdSale());  //兼容旧代码
                        /**
                         * TODO 广告报告重构
                         * 本广告产品销售额
                         */
                        report.setAdSales(adHomePerformancedto.getAdSales());   //兼容旧代码
                        report.setSaleNum(adHomePerformancedto.getAdOrderNum());
                        report.setCost(adHomePerformancedto.getAdCost());
                        //本广告产品订单量
                        report.setAdSaleNum(adHomePerformancedto.getAdSaleNum());
                        //广告销量
                        report.setOrderNum(adHomePerformancedto.getSalesNum());
                        //本广告产品销量
                        report.setAdOrderNum(adHomePerformancedto.getOrderNum());
                        // 填充报告数据
                        cpcCommService.fillReportDataIntoPageVo(vo, report, shopSaleDto);
                    }
                }
            }
            log.info(" ==============================广告组 sp 循环处理数据花费时间 {} ==============================", System.currentTimeMillis() - start3);
        }

        return page;
    }

    private List<GroupPageVo> getSpGroupPageVoList(Integer puid, GroupPageParam param) {
        List<GroupPageVo> voList = Lists.newArrayList();

        if(param.getAdTagId() != null){
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.GROUP.getType(), param.getAdTagId(), param.getType(),null);
            if (CollectionUtils.isNotEmpty(relationIds)) {
                param.setGroupIds(relationIds);
            } else {
                return voList;
            }
        }

        if(CollectionUtils.isNotEmpty(param.getAdTagIdList())){
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.GROUP.getType(), param.getAdTagIdList(), param.getType(),null);
            if (CollectionUtils.isNotEmpty(relationIds)) {
                param.setGroupIds(relationIds);
            } else {
                return voList;
            }
        }

        if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), Constants.SP, null, null);
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                param.setCampaignIdList(campaignIds);
            } else {
                return voList;
            }
        }

        List<AmazonAdGroup> poList = amazonAdGroupDao.getList(puid, param);

        if (CollectionUtils.isNotEmpty(poList)) {

            Map<Integer, User> userMap = userDao.listByPuid(puid).stream().filter(Objects::nonNull).collect(Collectors.toMap(User::getId, e -> e));

            List<String> groupIds = poList.stream().map(AmazonAdGroup::getAdGroupId).collect(Collectors.toList());

            List<AdHomePerformancedto> list = new ArrayList<>();
            if (groupIds.size() > 20000) {
                List<List<String>> lists = Lists.partition(groupIds, 20000);
                List<AdHomePerformancedto> dtoList = null;
                for (List<String> subList : lists) {

                    dtoList = amazonAdGroupReportDao.listSumReportByGroupIds(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, subList);

                    list.addAll(dtoList);
                }
            } else {

                list = amazonAdGroupReportDao.listSumReportByGroupIds(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, groupIds);

            }

            groupIds = null;

            Map<String, AdHomePerformancedto> groupReportMap = list.stream().filter(Objects::nonNull).collect(Collectors.toMap(AdHomePerformancedto::getGroupId, e -> e));

            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();  //最外层已经查了
            shopSaleDto.setSumRange(param.getShopSales());

            GroupPageVo vo;
            for (AmazonAdGroup amazonAdGroup : poList) {
                vo = new GroupPageVo();
                voList.add(vo);
                convertSpPoToPageVo(amazonAdGroup, vo);
                vo.setTargetingType(vo.getAdGroupType());
                // 创建人
                if (MapUtils.isNotEmpty(userMap) && userMap.containsKey(amazonAdGroup.getCreateId())) {
                    User user = userMap.get(amazonAdGroup.getCreateId());
                    if (StringUtils.isNotBlank(user.getNickname())) {
                        vo.setCreator(user.getNickname());
                    }
                }
                if (groupReportMap.size() > 0) {
                    AdHomePerformancedto adHomePerformancedto = groupReportMap.get(amazonAdGroup.getAdGroupId());
                    if (adHomePerformancedto != null) {
                        AmazonAdGroupReport report = new AmazonAdGroupReport();
                        report.setType(adHomePerformancedto.getType());
                        report.setImpressions(adHomePerformancedto.getImpressions());
                        report.setClicks(adHomePerformancedto.getClicks());
                        report.setTotalSales(adHomePerformancedto.getAdSale());  //兼容旧代码
                        /**
                         * TODO 广告报告重构
                         * 本广告产品销售额
                         */
                        report.setAdSales(adHomePerformancedto.getAdSales());   //兼容旧代码
                        report.setSaleNum(adHomePerformancedto.getAdOrderNum());
                        report.setCost(adHomePerformancedto.getAdCost());
                        //本广告产品订单量
                        report.setAdSaleNum(adHomePerformancedto.getAdSaleNum());
                        //广告销量
                        report.setOrderNum(adHomePerformancedto.getSalesNum());
                        //本广告产品销量
                        report.setAdOrderNum(adHomePerformancedto.getOrderNum());



                        // 填充报告数据
                        cpcCommService.fillReportDataIntoPageVo(vo, report, shopSaleDto);
                    }
                }
            }
        }

        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索,需要过滤
            cpcCommService.filterGroupAdvanceData(voList, param);
        }

        // 获取汇总信息
        AdMetricDto adMetricDto = new AdMetricDto();
        filterSumMetricData(voList, adMetricDto);
        // 填充指标占比
        filterMetricData(voList, adMetricDto);

        return voList;
    }

    private Page getSdPageList(Integer puid, GroupPageParam param, Page page) {
        List<GroupPageVo> voList = Lists.newArrayList();

        if (setParam(puid, param)) return page;

        page = amazonSdAdGroupDao.getPageList(puid, param, page);

        List<AmazonSdAdGroup> poList = page.getRows();
        if (CollectionUtils.isNotEmpty(poList)) {

            List<String> groupIds = poList.stream().map(AmazonSdAdGroup::getAdGroupId).collect(Collectors.toList());

            Map<Integer, User> userMap = userDao.listByPuid(puid).stream().filter(Objects::nonNull).collect(Collectors.toMap(User::getId, e -> e));

            List<AdHomePerformancedto> list = null;
            List<AdHomePerformancedto> latestReports = new ArrayList<>();
            AdMetricDto adMetricDto = new AdMetricDto();

            list = amazonAdSdGroupReportDao.listSumReportByGroupIds(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, groupIds);
            latestReports = amazonAdSdGroupReportDao.listLatestReports(puid, param.getShopId(), groupIds, false);
            adMetricDto = amazonAdSdGroupReportDao.getSumMetric(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param);


            Map<String, AdHomePerformancedto> groupReportMap = list.stream().filter(Objects::nonNull).collect(Collectors.toMap(AdHomePerformancedto::getGroupId, e -> e));
            Map<String, AdHomePerformancedto> latestReportMap = latestReports.stream().collect(Collectors.toMap(AdHomePerformancedto::getGroupId, Function.identity(), (x, y) -> x));

            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();  //最外层已经查了
            shopSaleDto.setSumRange(param.getShopSales());

            GroupPageVo vo;
            page.setRows(voList);
            for (AmazonSdAdGroup amazonAdGroup : poList) {
                vo = new GroupPageVo();
                voList.add(vo);
                convertSdPoToPageVo(amazonAdGroup, vo);
                filterAdMetricData(adMetricDto, vo);
                vo.setTargetingType(vo.getAdGroupType());
                // 创建人
                if (MapUtils.isNotEmpty(userMap) && userMap.containsKey(amazonAdGroup.getCreateId())) {
                    User user = userMap.get(amazonAdGroup.getCreateId());
                    if (StringUtils.isNotBlank(user.getNickname())) {
                        vo.setCreator(user.getNickname());
                    }
                }
                if (groupReportMap.size() > 0) {
                    AdHomePerformancedto adHomePerformancedto = groupReportMap.get(amazonAdGroup.getAdGroupId());
                    if (adHomePerformancedto != null) {
                        AmazonAdGroupReport report = new AmazonAdGroupReport();
                        report.setType(adHomePerformancedto.getType());
                        report.setImpressions(adHomePerformancedto.getImpressions());
                        report.setClicks(adHomePerformancedto.getClicks());
                        report.setTotalSales(adHomePerformancedto.getAdSale());  //兼容旧代码
                        /**
                         * TODO 广告报告重构
                         * 本广告产品销售额
                         */
                        report.setAdSales(adHomePerformancedto.getAdSales());   //兼容旧代码
                        report.setSaleNum(adHomePerformancedto.getAdOrderNum());
                        report.setCost(adHomePerformancedto.getAdCost());
                        //可见展示次数(VCPM专用)
                        report.setViewImpressions(adHomePerformancedto.getViewImpressions());
                        //本广告产品订单量
                        report.setAdSaleNum(adHomePerformancedto.getAdSaleNum());
                        //CPC,VCPM-广告销量
                        report.setOrderNum(adHomePerformancedto.getSalesNum());
                        //CPC,VCPM-“品牌新买家”订单量
                        report.setOrdersNewToBrand14d(adHomePerformancedto.getOrdersNewToBrand14d());
                        //CPC,VCPM-“品牌新买家”销售额
                        report.setSalesNewToBrand14d(adHomePerformancedto.getSalesNewToBrand14d());
                        //CPC,VCPM-“品牌新买家”销量
                        report.setUnitsOrderedNewToBrand14d(adHomePerformancedto.getUnitsOrderedNewToBrand14d());

                        //设置sd广告组的本广告产品的广告销量为默认0
                        report.setAdOrderNum(0);

                        report.setNewToBrandDetailPageViews(adHomePerformancedto.getNewToBrandDetailPageViews());
                        report.setAddToCart(adHomePerformancedto.getAddToCart());
                        report.setAddToCartRate(adHomePerformancedto.getAddToCartRate());
                        report.setECPAddToCart(adHomePerformancedto.getECPAddToCart());
                        report.setVideoFirstQuartileViews(adHomePerformancedto.getVideoFirstQuartileViews());
                        report.setVideoMidpointViews(adHomePerformancedto.getVideoMidpointViews());
                        report.setVideoThirdQuartileViews(adHomePerformancedto.getVideoThirdQuartileViews());
                        report.setVideoCompleteViews(adHomePerformancedto.getVideoCompleteViews());
                        report.setVideoUnmutes(adHomePerformancedto.getVideoUnmutes());
                        report.setViewabilityRate(adHomePerformancedto.getViewabilityRate());
                        report.setViewClickThroughRate(adHomePerformancedto.getViewClickThroughRate());
                        report.setBrandedSearches(adHomePerformancedto.getBrandedSearches());
                        report.setDetailPageViews(adHomePerformancedto.getDetailPageViews());
                        report.setCumulativeReach(adHomePerformancedto.getCumulativeReach());
                        report.setImpressionsFrequencyAverage(adHomePerformancedto.getImpressionsFrequencyAverage());
                        // 填充报告数据
                        cpcCommService.fillReportDataIntoPageVo(vo, report, shopSaleDto);

                        //sd没有本产品广告销量字段，故其他产品广告销量也设置为0
                        vo.setAdOtherSaleNum(0);
                    }
                }
                // 填充最新数据
                if (latestReportMap.get(amazonAdGroup.getAdGroupId()) != null) {
                    cpcCommService.fillLatestReportDataIntoPageVo(vo, latestReportMap.get(amazonAdGroup.getAdGroupId()));
                }
            }
        }

        return page;
    }


    private List<GroupPageVo> getSdGroupPageVoList(Integer puid, GroupPageParam param) {
        List<GroupPageVo> voList = Lists.newArrayList();

        if(setParam(puid,param)) return voList;

        List<AmazonSdAdGroup> poList = amazonSdAdGroupDao.getList(puid, param);


        if (CollectionUtils.isNotEmpty(poList)) {

            Map<Integer, User> userMap = userDao.listByPuid(puid).stream().filter(Objects::nonNull).collect(Collectors.toMap(User::getId, e -> e));

            List<String> groupIds = poList.stream().map(AmazonSdAdGroup::getAdGroupId).collect(Collectors.toList());

            List<AdHomePerformancedto> list = new ArrayList<>();
            List<AdHomePerformancedto> latestReports = new ArrayList<>();
            if (groupIds.size() > 20000) {
                List<List<String>> lists = Lists.partition(groupIds, 20000);
                List<AdHomePerformancedto> dtoList = new ArrayList<>();
                for (List<String> subList : lists) {

                    dtoList = amazonAdSdGroupReportDao.listSumReportByGroupIds(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, subList);

                    list.addAll(dtoList);
                    latestReports.addAll(amazonAdSdGroupReportDao.listLatestReports(puid, param.getShopId(), subList, false));
                }

                groupIds = null;
            } else {

                list = amazonAdSdGroupReportDao.listSumReportByGroupIds(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, groupIds);
                latestReports = amazonAdSdGroupReportDao.listLatestReports(puid, param.getShopId(), groupIds, false);

            }

            Map<String, AdHomePerformancedto> groupReportMap = list.stream().filter(Objects::nonNull).collect(Collectors.toMap(AdHomePerformancedto::getGroupId, e -> e));
            Map<String, AdHomePerformancedto> latestReportMap = latestReports.stream().collect(Collectors.toMap(AdHomePerformancedto::getGroupId, Function.identity(), (x, y) -> x));

            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();  //最外层已经查了
            shopSaleDto.setSumRange(param.getShopSales());

            GroupPageVo vo;
            for (AmazonSdAdGroup amazonAdGroup : poList) {
                vo = new GroupPageVo();
                voList.add(vo);
                convertSdPoToPageVo(amazonAdGroup, vo);
                vo.setTargetingType(vo.getAdGroupType());
                // 创建人
                if (MapUtils.isNotEmpty(userMap) && userMap.containsKey(amazonAdGroup.getCreateId())) {
                    User user = userMap.get(amazonAdGroup.getCreateId());
                    if (StringUtils.isNotBlank(user.getNickname())) {
                        vo.setCreator(user.getNickname());
                    }
                }
                if (groupReportMap.size() > 0) {
                    AdHomePerformancedto adHomePerformancedto = groupReportMap.get(amazonAdGroup.getAdGroupId());
                    if (adHomePerformancedto != null) {
                        AmazonAdGroupReport report = new AmazonAdGroupReport();
                        report.setType(adHomePerformancedto.getType());
                        report.setImpressions(adHomePerformancedto.getImpressions());
                        report.setClicks(adHomePerformancedto.getClicks());
                        report.setTotalSales(adHomePerformancedto.getAdSale());  //兼容旧代码
                        /**
                         * TODO 广告报告重构
                         * 本广告产品销售额
                         */
                        report.setAdSales(adHomePerformancedto.getAdSales());   //兼容旧代码
                        report.setSaleNum(adHomePerformancedto.getAdOrderNum());
                        report.setCost(adHomePerformancedto.getAdCost());
                        //可见展示次数(VCPM专用)
                        report.setViewImpressions(adHomePerformancedto.getViewImpressions());
                        //本广告产品订单量
                        report.setAdSaleNum(adHomePerformancedto.getAdSaleNum());
                        //CPC,VCPM-广告销量
                        report.setOrderNum(adHomePerformancedto.getSalesNum());
                        //CPC,VCPM-“品牌新买家”订单量
                        report.setOrdersNewToBrand14d(adHomePerformancedto.getOrdersNewToBrand14d());
                        //CPC,VCPM-“品牌新买家”销售额
                        report.setSalesNewToBrand14d(adHomePerformancedto.getSalesNewToBrand14d());
                        //CPC,VCPM-“品牌新买家”销量
                        report.setUnitsOrderedNewToBrand14d(adHomePerformancedto.getUnitsOrderedNewToBrand14d());
                        // 填充报告数据

                        //设置sd广告组的本广告产品的广告销量为默认0
                        report.setAdOrderNum(0);

                        report.setNewToBrandDetailPageViews(adHomePerformancedto.getNewToBrandDetailPageViews());
                        report.setAddToCart(adHomePerformancedto.getAddToCart());
                        report.setAddToCartRate(adHomePerformancedto.getAddToCartRate());
                        report.setECPAddToCart(adHomePerformancedto.getECPAddToCart());
                        report.setVideoFirstQuartileViews(adHomePerformancedto.getVideoFirstQuartileViews());
                        report.setVideoMidpointViews(adHomePerformancedto.getVideoMidpointViews());
                        report.setVideoThirdQuartileViews(adHomePerformancedto.getVideoThirdQuartileViews());
                        report.setVideoCompleteViews(adHomePerformancedto.getVideoCompleteViews());
                        report.setVideoUnmutes(adHomePerformancedto.getVideoUnmutes());
                        report.setViewabilityRate(adHomePerformancedto.getViewabilityRate());
                        report.setViewClickThroughRate(adHomePerformancedto.getViewClickThroughRate());
                        report.setBrandedSearches(adHomePerformancedto.getBrandedSearches());
                        report.setDetailPageViews(adHomePerformancedto.getDetailPageViews());
                        report.setCumulativeReach(adHomePerformancedto.getCumulativeReach());
                        report.setImpressionsFrequencyAverage(adHomePerformancedto.getImpressionsFrequencyAverage());

                        cpcCommService.fillReportDataIntoPageVo(vo, report, shopSaleDto);

                        //sd没有本产品广告销量字段，故其他产品广告销量也设置为0
                        vo.setAdOtherSaleNum(0);
                    }
                }
                // 填充最新数据
                if (latestReportMap.get(amazonAdGroup.getAdGroupId()) != null) {
                    cpcCommService.fillLatestReportDataIntoPageVo(vo, latestReportMap.get(amazonAdGroup.getAdGroupId()));
                }
                if(AdOrderByFieldEnum.VCPM.getCode().equals(param.getOrderField())){
                    // vcpm取值逻辑调整 用于排序放至最后
                    int value = OrderTypeEnum.desc.getType().equals(param.getOrderType()) ? Integer.MIN_VALUE : Integer.MAX_VALUE;
                    if(!SBCampaignCostTypeEnum.VCPM.getCode().equals(vo.getCostType())){
                        vo.setVcpm(Integer.toString(value));
                    }else if(StringUtil.isEmpty(vo.getVcpm())){
                        vo.setVcpm("0");
                    }
                }
            }
        }

        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索,需要过滤
            cpcCommService.filterGroupAdvanceData(voList, param);
        }

        // 获取汇总信息
        AdMetricDto adMetricDto = new AdMetricDto();
        filterSumMetricData(voList, adMetricDto);
        // 填充指标占比
        filterMetricData(voList, adMetricDto);

        return voList;
    }


    private Result commUpdate(AmazonAdGroup amazonAdGroup) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdGroup.getShopId(), amazonAdGroup.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        GroupEntityV3 adGroup = makeAdGroupByAdGroupPo(amazonAdGroup);

        UpdateSpGroupV3Response response = GroupSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putGroups(shopAuthService.getAdToken(shop), amazonAdGroup.getProfileId(),
                amazonAdGroup.getMarketplaceId(), Lists.newArrayList(adGroup),true);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = GroupSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putGroups(shopAuthService.getAdToken(shop), amazonAdGroup.getProfileId(),
                    amazonAdGroup.getMarketplaceId(), Lists.newArrayList(adGroup),true);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "创建广告组失败";
        if (response.getData() != null && response.getData().getAdGroups() != null) {

            if (CollectionUtils.isNotEmpty(response.getData().getAdGroups().getSuccess())) {
                try {
                    amazonAdGroupDao.updateById(shop.getPuid(), amazonAdGroup);
                    saveDoris(Collections.singletonList(amazonAdGroup), false, true);
                    return ResultUtil.success();
                } catch (Exception e) {
                    log.error("createAdGroup:", e);
                }
            } else if (CollectionUtils.isNotEmpty(response.getData().getAdGroups().getError()) && StringUtils.isNotBlank(response.getData().getAdGroups().getError().get(0).getErrors().get(0).getErrorMessage())) {
                errMsg = AmazonErrorUtils.getError(response.getData().getAdGroups().getError().get(0).getErrors().get(0).getErrorMessage());
            }
        } else if (response.getError() != null) {
            if(StringUtils.isNotBlank(response.getError().getMessage())){
                errMsg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if(CollectionUtils.isNotEmpty(response.getError().getErrors())){
                errMsg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }
        }

        return ResultUtil.error(errMsg);
    }

    private void fillGroupPageVo(GroupInfoPageVo groupInfoPageVo, GroupPageVo vo) {
        BeanUtils.copyProperties(groupInfoPageVo, vo, ParamCopyUtil.checkPropertiesNullOrEmpty(groupInfoPageVo));
        Optional.ofNullable(groupInfoPageVo.getTotalSales()).map(BigDecimal::toString).ifPresent(vo::setAdSales);
        Optional.ofNullable(groupInfoPageVo.getDefaultBid()).map(String::valueOf).ifPresent(vo::setDefaultBid);
        //sd类型广告组需要单独处理此字段
        if (Constants.SB.equals(groupInfoPageVo.getType())) {
            vo.setCampaignTargetingType(groupInfoPageVo.getAdFormat());
        }
        if (Constants.SD.equals(groupInfoPageVo.getType())) {
            vo.setCostType(Constants.VCPM_OPTIMIZATION.contains(groupInfoPageVo.getBidOptimization())?"vcpm":"cpc");
            vo.setAdGroupType(groupInfoPageVo.getTactic());
        }
        vo.setServingStatusDec(groupInfoPageVo.getServingStatusDec());
        vo.setServingStatus(groupInfoPageVo.getServingStatus());
        vo.setServingStatusName(groupInfoPageVo.getServingStatusName());
    }

    private void convertSpPoToPageVo(AmazonAdGroup amazonAdGroup, GroupPageVo vo) {
        vo.setType(Constants.SP);
        vo.setId(amazonAdGroup.getId());
        vo.setCampaignId(amazonAdGroup.getCampaignId());
        vo.setShopId(amazonAdGroup.getShopId());
        vo.setAdGroupId(amazonAdGroup.getAdGroupId());
        vo.setName(amazonAdGroup.getName());
        vo.setState(amazonAdGroup.getState());
        vo.setDefaultBid(String.valueOf(amazonAdGroup.getDefaultBid()));
        vo.setAdGroupType(amazonAdGroup.getAdGroupType());
        amazonAdGroup.setServingStatus(amazonAdGroup.getServingStatus());
        vo.setServingStatus(amazonAdGroup.getServingStatus());
        vo.setServingStatusDec(amazonAdGroup.getServingStatusDec());
        vo.setServingStatusName(amazonAdGroup.getServingStatusName());
        vo.setIsStateBidding(amazonAdGroup.getIsStateBidding());
        vo.setPricingStateBidding(amazonAdGroup.getPricingStateBidding());
        vo.setCreateTime(amazonAdGroup.getCreateTime());
        vo.setUpdateTime(amazonAdGroup.getUpdateTime());
    }

    private void convertSpPoToPageVo(AmazonAdGroup amazonAdGroup, GroupPageVo vo, AmazonAdGroup amazonAdGroupDoris, GroupPageParam param) {
        vo.setType(Constants.SP);
        vo.setId(amazonAdGroup.getId());
        vo.setCampaignId(amazonAdGroup.getCampaignId());
        vo.setShopId(amazonAdGroup.getShopId());
        vo.setAdGroupId(amazonAdGroup.getAdGroupId());
        vo.setAdGroupType(amazonAdGroup.getAdGroupType());
        vo.setName(amazonAdGroup.getName());

        if (StringUtils.isNotBlank(param.getStatus())) {
            vo.setState(amazonAdGroupDoris.getState());
        } else {
            vo.setState(amazonAdGroup.getState());
        }

        if (param.getBidMax() != null || param.getBidMin() != null) {
            vo.setDefaultBid(String.valueOf(amazonAdGroupDoris.getDefaultBid()));
        } else {
            vo.setDefaultBid(String.valueOf(amazonAdGroup.getDefaultBid()));
        }

        if (StringUtils.isNotBlank(param.getServingStatus())) {
            amazonAdGroupDoris.setServingStatus(amazonAdGroupDoris.getServingStatus());
            vo.setServingStatus(amazonAdGroupDoris.getServingStatus());
            vo.setServingStatusDec(amazonAdGroupDoris.getServingStatusDec());
            vo.setServingStatusName(amazonAdGroupDoris.getServingStatusName());
        } else {
            amazonAdGroup.setServingStatus(amazonAdGroup.getServingStatus());
            vo.setServingStatus(amazonAdGroup.getServingStatus());
            vo.setServingStatusDec(amazonAdGroup.getServingStatusDec());
            vo.setServingStatusName(amazonAdGroup.getServingStatusName());
        }

        vo.setIsStateBidding(amazonAdGroup.getIsStateBidding());
        vo.setPricingStateBidding(amazonAdGroup.getPricingStateBidding());
        vo.setCreateTime(amazonAdGroup.getCreateTime());
        vo.setUpdateTime(amazonAdGroup.getUpdateTime());
    }


    private void convertSdPoToPageVo(AmazonSdAdGroup amazonAdGroup, GroupPageVo vo) {
        vo.setType(Constants.SD);
        vo.setBidOptimization(amazonAdGroup.getBidOptimization());
        vo.setId(amazonAdGroup.getId());
        vo.setCampaignId(amazonAdGroup.getCampaignId());
        vo.setShopId(amazonAdGroup.getShopId());
        vo.setAdGroupId(amazonAdGroup.getAdGroupId());
        vo.setName(amazonAdGroup.getName());
        vo.setState(amazonAdGroup.getState());
        vo.setDefaultBid(String.valueOf(amazonAdGroup.getDefaultBid()));
        vo.setAdGroupType(amazonAdGroup.getTactic());
        amazonAdGroup.setServingStatus(amazonAdGroup.getServingStatus());
        vo.setServingStatus(amazonAdGroup.getServingStatus());
        vo.setServingStatusDec(amazonAdGroup.getServingStatusDec());
        vo.setServingStatusName(amazonAdGroup.getServingStatusName());
        vo.setCreateTime(amazonAdGroup.getCreateTime());
        vo.setUpdateTime(amazonAdGroup.getUpdateTime());
        //注意，costType 本身在广告活动维度，但是现在可以通过广告组的 BidOptimization 竞价优化方式来确定CostType付费方式，
        // 如果后期出现变动注意需要修改
        //TODO
        vo.setCostType(Constants.VCPM_OPTIMIZATION.contains(amazonAdGroup.getBidOptimization())?"vcpm":"cpc");
        vo.setIsStateBidding(amazonAdGroup.getIsStateBidding());
        vo.setPricingStateBidding(amazonAdGroup.getPricingStateBidding());
        Optional.of(amazonAdGroup.getCreativeType()).filter(StringUtils::isNotEmpty).ifPresent(vo::setCreativeType);
    }


    private void convertSbPoToPageVo(AmazonSbAdGroup amazonAdGroup, GroupPageVo vo) {
        vo.setType(Constants.SB);
        vo.setId(amazonAdGroup.getId());
        vo.setCampaignId(amazonAdGroup.getCampaignId());
        vo.setShopId(amazonAdGroup.getShopId());
        vo.setAdGroupId(amazonAdGroup.getAdGroupId());
        vo.setName(amazonAdGroup.getName());
        vo.setState(amazonAdGroup.getState() != null ? amazonAdGroup.getState().toLowerCase():null);
        vo.setCampaignTargetingType(amazonAdGroup.getAdFormat());
        amazonAdGroup.setServingStatus(amazonAdGroup.getServingStatus());
        vo.setServingStatus(amazonAdGroup.getServingStatus());
        vo.setCreateTime(amazonAdGroup.getCreateTime());
        vo.setUpdateTime(amazonAdGroup.getUpdateTime());
        vo.setTargetingType(amazonAdGroup.getAdGroupType());
        vo.setAdGroupType(amazonAdGroup.getAdGroupType());
        vo.setIsStateBidding(amazonAdGroup.getIsStateBidding());
        vo.setPricingStateBidding(amazonAdGroup.getPricingStateBidding());
        Optional.ofNullable(amazonAdGroup.getAdFormat()).filter(StringUtils::isNotEmpty).ifPresent(vo::setAdFormat);
    }


    // 获取指标总数据
    private void filterSumMetricData(List<GroupPageVo> voList,AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        adMetricDto.setSumCost(voList.stream().filter(item -> item != null && item.getAdCost() != null).map(item -> new BigDecimal(item.getAdCost())).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdSale(voList.stream().filter(item -> item != null && item.getAdSale() != null).map(item -> new BigDecimal(item.getAdSale())).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(GroupPageVo::getAdOrderNum).sum()));
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(GroupPageVo::getOrderNum).sum()));
    }

    // 填充指标总和数据
    private void filterAdMetricData(AdMetricDto adMetricDto, GroupPageVo vo) {
        if (adMetricDto == null) {
            vo.setSumCost(BigDecimal.ZERO);
            vo.setSumAdSale(BigDecimal.ZERO);
            vo.setSumAdOrderNum(BigDecimal.ZERO);
            vo.setSumOrderNum(BigDecimal.ZERO);
            return;
        }
        vo.setSumCost(adMetricDto.getSumCost() == null ? BigDecimal.ZERO : adMetricDto.getSumCost());
        vo.setSumAdSale(adMetricDto.getSumAdSale() == null ? BigDecimal.ZERO : adMetricDto.getSumAdSale());
        vo.setSumAdOrderNum(adMetricDto.getSumAdOrderNum() == null ? BigDecimal.ZERO : adMetricDto.getSumAdOrderNum());
        vo.setSumOrderNum(adMetricDto.getSumOrderNum() == null ? BigDecimal.ZERO : adMetricDto.getSumOrderNum());
    }

    // 填充指标占比数据
    private void filterMetricData(List<GroupPageVo> voList, AdMetricDto adMetricDto) {
        for (GroupPageVo vo: voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage("0");
                vo.setAdSalePercentage("0");
                vo.setAdOrderNumPercentage("0");
                vo.setOrderNumPercentage("0");
                continue;
            }
            computeMetricData(adMetricDto, vo);
        }
    }


    private void computeMetricData(AdMetricDto adMetricDto, GroupPageVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(MathUtil.multiply(MathUtil.divide(vo.getAdCost(), adMetricDto.getSumCost().toString()), "100"));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(MathUtil.multiply(MathUtil.divide(vo.getAdSale(), adMetricDto.getSumAdSale().toString()), "100"));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100"));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getOrderNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(MathUtil.multiply(MathUtil.divide(vo.getOrderNum().toString(), adMetricDto.getSumOrderNum().toString()), "100"));
        }
    }


    // 创建活动时vo->po
    private AmazonAdGroup convertVoToCreatePo(Integer uid, SPadGroupVo vo, AmazonAdCampaignAll amazonAdCampaign) {
        AmazonAdGroup amazonAdGroup = new AmazonAdGroup();
        amazonAdGroup.setPuid(vo.getPuid());
        amazonAdGroup.setShopId(amazonAdCampaign.getShopId());
        amazonAdGroup.setMarketplaceId(amazonAdCampaign.getMarketplaceId());
        amazonAdGroup.setCampaignId(amazonAdCampaign.getCampaignId());
        amazonAdGroup.setProfileId(amazonAdCampaign.getProfileId());
        amazonAdGroup.setName(vo.getName().trim());
        amazonAdGroup.setDefaultBid(vo.getDefaultBid());
        amazonAdGroup.setAdGroupType(Constants.AUTO.equals(amazonAdCampaign.getTargetingType()) ? Constants.GROUP_TYPE_AUTO : null);
        amazonAdGroup.setState(Constants.ENABLED);
        amazonAdGroup.setCreateId(uid);
        return amazonAdGroup;
    }

    @Override
    // 组装接口数据
    public GroupEntityV3 makeAdGroupByAdGroupPo(AmazonAdGroup amazonAdGroup) {
        GroupEntityV3 adGroup = new GroupEntityV3();
        adGroup.setName(amazonAdGroup.getName());
        adGroup.setCampaignId(amazonAdGroup.getCampaignId());
        if (StringUtils.isNotBlank(amazonAdGroup.getAdGroupId())) {
            adGroup.setAdGroupId(amazonAdGroup.getAdGroupId());
        }
        adGroup.setDefaultBid(amazonAdGroup.getDefaultBid());
        adGroup.setState(SpV3StateEnum.getSpV3StateEnumByValue(amazonAdGroup.getState()).valueV3());
        return adGroup;
    }

    // 更新广告组时vo->po
    private void convertVoToUpdatePo(Integer uid, AmazonAdGroup amazonAdGroup, SPadGroupVo vo) {
        amazonAdGroup.setName(vo.getName().trim());
        amazonAdGroup.setDefaultBid(vo.getDefaultBid());
        amazonAdGroup.setUpdateId(uid);
    }

    @Override
    public Result batchUpdateAdGroup(List<SPadGroupVo> vos, Integer puid, Integer uid, Integer shopId, String loginIp, String type) {


        List<SPadGroupVo> errorList = Lists.newArrayList();

        List<Long> ids = vos.stream().filter(e-> e.getDxmGroupId()!= null && e.getDxmGroupId()>0).map(SPadGroupVo::getDxmGroupId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(ids)){
            return ResultUtil.error("参数错误");
        }
        List<AmazonAdGroup> listByIdList = amazonAdGroupDao.getListByLongIdList(puid, ids);
        List<AmazonAdGroup> updateList = Lists.newArrayList();
        Map<Long, AmazonAdGroup> amazonAdGroupMap = listByIdList.stream().collect(Collectors.toMap(AmazonAdGroup::getId, e -> e));
        for (SPadGroupVo vo: vos) {
            checkBatchVo(vo, type);

            AmazonAdGroup oldAmazonAdCGroup= amazonAdGroupMap.get(vo.getDxmGroupId());
            if (oldAmazonAdCGroup == null) {
                vo.setId(vo.getDxmGroupId());
                vo.setGroupId(oldAmazonAdCGroup.getAdGroupId());
                vo.setFailReason("对象不存在");
                errorList.add(vo);
                continue;
            }
            if(StringUtils.isNotBlank(vo.getFailReason())){
                vo.setId(vo.getDxmGroupId());
                vo.setGroupId(oldAmazonAdCGroup.getAdGroupId());
                vo.setName(oldAmazonAdCGroup.getName());
                errorList.add(vo);
                continue;
            }
            AmazonAdGroup amazonAdGroup = new AmazonAdGroup();
            BeanUtils.copyProperties(oldAmazonAdCGroup, amazonAdGroup);
            convertVoToBatchUpdatePo(amazonAdGroup, vo,type);
            updateList.add(amazonAdGroup);
        }
        if(CollectionUtils.isEmpty(updateList)){
            BatchResponseVo<SPadGroupVo, AmazonAdGroup> data =  new  BatchResponseVo<> ();
            data.setErrorList(errorList);
            data.setCountNum(vos.size());
            data.setFailNum(errorList.size());
            data.setSuccessNum(0);
            return ResultUtil.success(data);
        }

        Result result = commUpdateBatch(updateList,type);
        /**
         * TODO 广告组增加日志
         * 操作类型：批量调整广告组默认竞价，状态
         * 逻辑：创建广告组新对象（默认竞价，状态）修改
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayListWithExpectedSize(2);
        for (AmazonAdGroup group :updateList) {
            AmazonAdGroup oldGroup = amazonAdGroupMap.get(group.getId());
            AdManageOperationLog adManageOperationLog = operationAdGroupLogService.getAdGroupLog(oldGroup, group);
            adManageOperationLog.setIp(loginIp);
            adManageOperationLogs.add(adManageOperationLog);
        }
        if (result.success()) {
            BatchResponseVo<SPadGroupVo,AmazonAdGroup> data = (BatchResponseVo<SPadGroupVo,AmazonAdGroup>) result.getData();
            List<SPadGroupVo> groupError = data.getErrorList();
            if(CollectionUtils.isNotEmpty(errorList)){
                groupError.addAll(errorList);
                data.setFailNum(data.getErrorList().size());
                data.setCountNum( (data.getErrorList() == null? 0 : data.getErrorList().size())+(data.getSuccessList() == null? 0 : data.getSuccessList().size())) ;


            }
            List<AmazonAdGroup> campaignSuccess = data.getSuccessList();
            Map<String, SPadGroupVo> errorMap = groupError.stream().collect(Collectors.toMap(SPadGroupVo::getGroupId, e->e));
            Map<String, AmazonAdGroup> successMap = campaignSuccess.stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e->e));
            for (AdManageOperationLog log : adManageOperationLogs) {
                if (!StringUtil.isEmptyObject(errorMap.get(log.getAdGroupId()))) {
                    log.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    log.setResultInfo(errorMap.get(log.getAdGroupId()).getFailReason());
                }
                if (!StringUtil.isEmptyObject(successMap.get(log.getAdGroupId()))) {
                    log.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                }
            }

            //记操作日志
            if(CollectionUtils.isNotEmpty(campaignSuccess)){
                //更新成功数据打日志
                log.info("用户批量更新成功，typ:{},updateId:{},puid :{},shopid:{},logoinIp:{},更新成功数据：{}",type,uid,puid,shopId,loginIp,JSONUtil.objectToJson(campaignSuccess));

                //前端不需要展示成功消息，减少消耗移除成功数据
                data.getSuccessList().clear();
            }


        }
        if (result.error()) {
            for (AdManageOperationLog log : adManageOperationLogs) {
                log.setResult(OperationLogResultEnum.FAIL.getResultValue());
                log.setResultInfo(result.getMsg());
            }
        }
        operationAdGroupLogService.printAdOperationLog(adManageOperationLogs);
        return result;
    }



    // 更新广告组时vo->po
    private void convertVoToBatchUpdatePo(AmazonAdGroup amazonAdGroup, SPadGroupVo vo,String type) {

        if (vo.getDefaultBid() != null){
            amazonAdGroup.setDefaultBid(vo.getDefaultBid());
        }
        if(StringUtils.isNotBlank(vo.getState())){
            amazonAdGroup.setState(vo.getState());
        }

        amazonAdGroup.setUpdateId(vo.getUid());
    }

    private SPadGroupVo checkBatchVo(SPadGroupVo vo,String type) {

        if(vo.getDxmGroupId() == null || vo.getDxmGroupId() < 1 ){
            vo.setFailReason("参数错误");
            return vo;
        }



        if (Constants.CPC_SP_GROUP_BATCH_UPDATE_BID.equals(type)) {
            if(vo.getDefaultBid() == null || vo.getDefaultBid().isNaN() ||  vo.getDefaultBid() < 0.02 || vo.getDefaultBid() >1000){
                vo.setFailReason("默认竞价请输入0.02~1000 之间");
            }
            return vo;
        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            if(StringUtils.isEmpty(vo.getState())){
                vo.setFailReason("参数错误");
            }
            return vo;
        } else {
            vo.setFailReason("参数错误");
            return vo;
        }

    }

    // 组装接口数据
    private GroupEntityV3 makeAdGroupByAdGroupPo(AmazonAdGroup amazonAdGroup,String type) {
        GroupEntityV3 adGroup = new GroupEntityV3();

        adGroup.setCampaignId(amazonAdGroup.getCampaignId());
        adGroup.setAdGroupId(amazonAdGroup.getAdGroupId());
        adGroup.setName(amazonAdGroup.getName());
        adGroup.setDefaultBid(amazonAdGroup.getDefaultBid());
        adGroup.setState(SpV3StateEnum.getSpV3StateEnumByValue(amazonAdGroup.getState()).valueV3());
        return adGroup;
    }

    /**
     * 批量更新
     * @param amazonAdGroups
     * @param type
     * @return
     */
    private Result commUpdateBatch(List<AmazonAdGroup> amazonAdGroups,String type) {
        AmazonAdGroup amazonAdGroup = amazonAdGroups.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdGroup.getShopId(), amazonAdGroup.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }
        boolean isDelete = false;
        if (CPC_BATCH_UPDATE_STATUS.equals(type)) {
            List<String> collect = amazonAdGroups.stream().map(AmazonAdGroup::getState).distinct().collect(Collectors.toList());
            if (collect.size() > 1) {
                return ResultUtil.error("更新状态错误");
            }
            isDelete = SpV3StateEnum.ARCHIVED.value().equalsIgnoreCase(collect.get(0));
        }
        List<GroupEntityV3> groups = Lists.newArrayListWithCapacity(amazonAdGroups.size());
        List<String> groupList = amazonAdGroups.stream().map(AmazonAdGroup::getAdGroupId).distinct().collect(Collectors.toList());
        for (AmazonAdGroup a : amazonAdGroups) {
            groups.add(makeAdGroupByAdGroupPo(a, type));
        }
        UpdateSpGroupV3Response response = null;
        if (isDelete) {
            response = GroupSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delGroups(shopAuthService.getAdToken(shop), amazonAdGroup.getProfileId(),
                    amazonAdGroup.getMarketplaceId(), groupList, true);
        } else {
            response = GroupSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putGroups(shopAuthService.getAdToken(shop), amazonAdGroup.getProfileId(),
                    amazonAdGroup.getMarketplaceId(), groups, true);
        }

        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            if (isDelete) {
                response = GroupSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delGroups(shopAuthService.getAdToken(shop), amazonAdGroup.getProfileId(),
                        amazonAdGroup.getMarketplaceId(), groupList, true);
            } else {
                response = GroupSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putGroups(shopAuthService.getAdToken(shop), amazonAdGroup.getProfileId(),
                        amazonAdGroup.getMarketplaceId(), groups, true);
            }
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }
        Map<String, AmazonAdGroup> amazonAdGroupMap = amazonAdGroups.stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e));
        BatchResponseVo<SPadGroupVo, AmazonAdGroup> batchResponseVo = new BatchResponseVo();
        List<SPadGroupVo> errorList = Lists.newArrayList();
        List<AmazonAdGroup> successList = Lists.newArrayList();
        //处理返回结果中的错误信息
        if (response.getData() != null && response.getData().getAdGroups() != null) {
            List<GroupSuccessResultV3> success = response.getData().getAdGroups().getSuccess();
            List<ErrorItemResultV3> error = response.getData().getAdGroups().getError();

            List<Long> successId = Lists.newArrayList();
            for (GroupSuccessResultV3 adGroupResult : success) {

                AmazonAdGroup amazonAdGroupSuccess = amazonAdGroupMap.remove(adGroupResult.getAdGroupId());
                if (amazonAdGroupSuccess != null) {
                    successList.add(amazonAdGroupSuccess);
                }
                successId.add(Long.valueOf(adGroupResult.getAdGroupId()));


            }
            for (ErrorItemResultV3 adGroupResult : error) {
                if (CollectionUtils.isNotEmpty(adGroupResult.getErrors())) {
                    GroupEntityV3 groupEntityV3 = groups.get(adGroupResult.getIndex());
                    AmazonAdGroup amazonAdGroupFail = amazonAdGroupMap.remove(String.valueOf(groupEntityV3.getAdGroupId()));
                    if (amazonAdGroupFail != null) {
                        SPadGroupVo amazonAdGroupVoError = new SPadGroupVo();
                        amazonAdGroupVoError.setDxmGroupId(amazonAdGroupFail.getId());
                        amazonAdGroupVoError.setId(amazonAdGroupFail.getId());
                        amazonAdGroupVoError.setName(amazonAdGroupFail.getName());
                        amazonAdGroupVoError.setGroupId(amazonAdGroupFail.getAdGroupId());
                        //更新失败数据处理
                        if (StringUtils.isNotBlank(adGroupResult.getErrors().get(0).getErrorMessage())) {
                            amazonAdGroupVoError.setFailReason(AmazonErrorUtils.getError(adGroupResult.getErrors().get(0).getErrorMessage()));
                        } else {
                            amazonAdGroupVoError.setFailReason("更新失败，请稍后重试");
                        }
                        errorList.add(amazonAdGroupVoError);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(successList)) {
                amazonAdGroupDao.batchUpdateAmazonAdGroup(amazonAdGroup.getPuid(), successList, type);
                saveDoris(shop.getPuid(), shop.getId(), successList.stream().map(AmazonAdGroup::getAdGroupId).collect(Collectors.toList()));
            }
            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonAdGroupMap)) {
                amazonAdGroupMap.forEach((k, v) -> {
                    SPadGroupVo campaignVoError = new SPadGroupVo();
                    campaignVoError.setDxmGroupId(v.getId());
                    campaignVoError.setId(v.getId());
                    campaignVoError.setName(v.getName());
                    campaignVoError.setGroupId(v.getAdGroupId());
                    campaignVoError.setFailReason("更新失败，请稍后重试");
                    errorList.add(campaignVoError);
                });
            }

        } else if (response.getError() != null) {
            String errMsg = "网络异常";
            if (StringUtils.isNotBlank(response.getError().getMessage())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if (CollectionUtils.isNotEmpty(response.getError().getErrors())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }
            return ResultUtil.error(AmazonErrorUtils.getError(errMsg));
        } else {
            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonAdGroupMap)) {
                amazonAdGroupMap.forEach((k, v) -> {
                    SPadGroupVo campaignVoError = new SPadGroupVo();
                    campaignVoError.setId(v.getId());
                    campaignVoError.setDxmGroupId(v.getId());
                    campaignVoError.setName(v.getName());
                    campaignVoError.setGroupId(v.getAdGroupId());
                    campaignVoError.setFailReason("更新失败，请稍后重试");
                    errorList.add(campaignVoError);
                });
            }
        }
        batchResponseVo.setCountNum(amazonAdGroups.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);
        return ResultUtil.success(batchResponseVo);
    }

    private List<Object> buildUpLogMessage(Map<Long, AmazonAdGroup> oldList,List<AmazonAdGroup> newList,String type){
        List<Object> dataList = new ArrayList<>();
        StringBuilder builder = new StringBuilder();
        if(Constants.CPC_SP_GROUP_BATCH_UPDATE_BID.equals(type)){
            dataList.add("SP广告组批量修改默认竞价");

        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
            dataList.add("SP广告组批量修改状态");

        }
        newList.forEach(e ->{
            AmazonAdGroup old = oldList.get(e.getId());

            if(Constants.CPC_SP_GROUP_BATCH_UPDATE_BID.equals(type)){

                builder.append("adGroupId:").append(e.getId());
                 if(old != null){
                    builder.append(",旧值:").append(old.getDefaultBid());
                }
                builder.append(",新值:").append(e.getDefaultBid());
            } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){

                builder.append("adGroupId:").append(e.getId());
                if(old != null){
                    builder.append(",旧值:").append(old.getState());
                }
                builder.append(",新值:").append(e.getState());
            }
            dataList.add(builder.toString());
            builder.delete(0,builder.length());
        });
        return dataList;
    }


    private List<GroupPageVo> getSbGroupPageVoList(Integer puid, GroupPageParam param) {
        List<GroupPageVo> voList = Lists.newArrayList();

        if(setParam(puid,param)) return voList;

        List<AmazonSbAdGroup> poList = amazonSbAdGroupDao.getList(puid, param);

        if (CollectionUtils.isNotEmpty(poList)) {

            Map<Integer, User> userMap = userDao.listByPuid(puid).stream().filter(Objects::nonNull).collect(Collectors.toMap(User::getId, e -> e));

            List<String> groupIds = poList.stream().map(AmazonSbAdGroup::getAdGroupId).collect(Collectors.toList());

            List<AdHomePerformancedto> list = new ArrayList<>();
            if (groupIds.size() > 20000) {
                List<List<String>> lists = Lists.partition(groupIds, 20000);
                List<AdHomePerformancedto> dtoList = new ArrayList<>();
                for (List<String> subList : lists) {

                    dtoList = amazonAdSbGroupReportDao.listSumReportByGroupIds(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, subList);

                    list.addAll(dtoList);
                }

                groupIds = null;
            } else {

                list = amazonAdSbGroupReportDao.listSumReportByGroupIds(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, groupIds);

            }

            Map<String, AdHomePerformancedto> groupReportMap = list.stream().filter(Objects::nonNull).collect(Collectors.toMap(AdHomePerformancedto::getGroupId, e -> e));

            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();  //最外层已经查了
            shopSaleDto.setSumRange(param.getShopSales());

            GroupPageVo vo;
            for (AmazonSbAdGroup amazonAdGroup : poList) {
                vo = new GroupPageVo();
                voList.add(vo);
                convertSbPoToPageVo(amazonAdGroup, vo);
                // 创建人
                if (MapUtils.isNotEmpty(userMap) && userMap.containsKey(amazonAdGroup.getCreateId())) {
                    User user = userMap.get(amazonAdGroup.getCreateId());
                    if (StringUtils.isNotBlank(user.getNickname())) {
                        vo.setCreator(user.getNickname());
                    }
                }
                if (groupReportMap.size() > 0) {
                    AdHomePerformancedto adHomePerformancedto = groupReportMap.get(amazonAdGroup.getAdGroupId());
                    if (adHomePerformancedto != null) {
                        //字段对应关系完全乱了, 注意这里有坑
                        AmazonAdGroupReport report = new AmazonAdGroupReport();
                        report.setType(adHomePerformancedto.getType());
                        report.setImpressions(adHomePerformancedto.getImpressions());
                        report.setClicks(adHomePerformancedto.getClicks());
                        report.setTotalSales(adHomePerformancedto.getAdSale());  //兼容旧代码
                        /**
                         * TODO 广告报告重构
                         * 本广告产品销售额
                         */
                        report.setAdSales(adHomePerformancedto.getAdSales());   //兼容旧代码
                        report.setSaleNum(adHomePerformancedto.getAdOrderNum());
                        report.setCost(adHomePerformancedto.getAdCost());
                        //可见展示次数(VCPM专用)
                        report.setViewImpressions(adHomePerformancedto.getViewImpressions());
                        //本广告产品订单量
                        report.setAdSaleNum(adHomePerformancedto.getAdSaleNum());
                        //CPC,VCPM-广告销量
                        report.setOrderNum(adHomePerformancedto.getSalesNum());
                        //CPC,VCPM-“品牌新买家”订单量
                        report.setOrdersNewToBrand14d(adHomePerformancedto.getOrdersNewToBrand14d());
                        //CPC,VCPM-“品牌新买家”销售额
                        report.setSalesNewToBrand14d(adHomePerformancedto.getSalesNewToBrand14d());
                        //CPC,VCPM-“品牌新买家”销量
                        report.setUnitsOrderedNewToBrand14d(adHomePerformancedto.getUnitsOrderedNewToBrand14d());

                        //设置sb广告组的本广告产品的广告销量为默认0
                        report.setAdOrderNum(0);

                        report.setVideo5SecondViews(adHomePerformancedto.getVideo5SecondViews());
                        report.setVideo5SecondViewRate(Optional.ofNullable(adHomePerformancedto.getVideo5SecondViewRate()).map(BigDecimal::doubleValue).orElse(null));
                        report.setVideoFirstQuartileViews(adHomePerformancedto.getVideoFirstQuartileViews());
                        report.setVideoMidpointViews(adHomePerformancedto.getVideoMidpointViews());
                        report.setVideoThirdQuartileViews(adHomePerformancedto.getVideoThirdQuartileViews());
                        report.setVideoCompleteViews(adHomePerformancedto.getVideoCompleteViews());
                        report.setVideoUnmutes(adHomePerformancedto.getVideoUnmutes());
                        report.setViewableImpressions(adHomePerformancedto.getViewableImpressions());
                        report.setViewabilityRate(adHomePerformancedto.getViewabilityRate());
                        report.setViewClickThroughRate(adHomePerformancedto.getViewClickThroughRate());
                        report.setBrandedSearches(adHomePerformancedto.getBrandedSearches());

                        // 填充报告数据
                        cpcCommService.fillReportDataIntoPageVo(vo, report, shopSaleDto);
                    }
                }
            }
        }

        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索,需要过滤
            cpcCommService.filterGroupAdvanceData(voList, param);
        }

        // 获取汇总信息
        AdMetricDto adMetricDto = new AdMetricDto();
        filterSumMetricData(voList, adMetricDto);
        // 填充指标占比
        filterMetricData(voList, adMetricDto);

        return voList;
    }

    private Page getSbPageList(Integer puid, GroupPageParam param, Page page) {
        List<GroupPageVo> voList = Lists.newArrayList();

        if (setParam(puid, param)) return page;

        page = amazonSbAdGroupDao.getPageList(puid, param, page);

        List<AmazonSbAdGroup> poList = page.getRows();
        if (CollectionUtils.isNotEmpty(poList)) {

            List<String> groupIds = poList.stream().map(AmazonSbAdGroup::getAdGroupId).collect(Collectors.toList());

            Map<Integer, User> userMap = userDao.listByPuid(puid).stream().filter(Objects::nonNull).collect(Collectors.toMap(User::getId, e -> e));

            List<AdHomePerformancedto> list = null;
            AdMetricDto adMetricDto = new AdMetricDto();

            list = amazonAdSbGroupReportDao.listSumReportByGroupIds(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, groupIds);

            adMetricDto = amazonAdSbGroupReportDao.getSumMetric(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param);


            Map<String, AdHomePerformancedto> groupReportMap = list.stream().filter(Objects::nonNull).collect(Collectors.toMap(AdHomePerformancedto::getGroupId, e -> e));

            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();  //最外层已经查了
            shopSaleDto.setSumRange(param.getShopSales());
            // 获取广告活动数据
            List<String> campaignIds = poList.stream().filter(Objects::nonNull).map(AmazonSbAdGroup::getCampaignId).collect(Collectors.toList());
            Map<String, AmazonAdCampaignAll> campaignMap = null;
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                campaignMap = amazonAdCampaignDao.listByCampaignId(puid, param.getShopId(), campaignIds, Constants.SB)
                        .stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item -> item, (a, b) -> a));
            }

            GroupPageVo vo;
            page.setRows(voList);
            for (AmazonSbAdGroup amazonAdGroup : poList) {
                vo = new GroupPageVo();
                voList.add(vo);
                convertSbPoToPageVo(amazonAdGroup, vo);
                filterAdMetricData(adMetricDto, vo);

                // 创建人
                if (MapUtils.isNotEmpty(userMap) && userMap.containsKey(amazonAdGroup.getCreateId())) {
                    User user = userMap.get(amazonAdGroup.getCreateId());
                    if (StringUtils.isNotBlank(user.getNickname())) {
                        vo.setCreator(user.getNickname());
                    }
                }
                if (groupReportMap.size() > 0) {
                    AdHomePerformancedto adHomePerformancedto = groupReportMap.get(amazonAdGroup.getAdGroupId());
                    if (adHomePerformancedto != null) {
                        AmazonAdGroupReport report = new AmazonAdGroupReport();
                        report.setType(adHomePerformancedto.getType());
                        report.setImpressions(adHomePerformancedto.getImpressions());
                        report.setClicks(adHomePerformancedto.getClicks());
                        report.setTotalSales(adHomePerformancedto.getAdSale());  //兼容旧代码
                        /**
                         * TODO 广告报告重构
                         * 本广告产品销售额
                         */
                        report.setAdSales(adHomePerformancedto.getAdSales());   //兼容旧代码
                        report.setSaleNum(adHomePerformancedto.getAdOrderNum());
                        report.setOrderNum(adHomePerformancedto.getSalesNum());
                        report.setCost(adHomePerformancedto.getAdCost());
                        //可见展示次数(VCPM专用)
                        report.setViewImpressions(adHomePerformancedto.getViewImpressions());
                        //本广告产品订单量
                        report.setAdSaleNum(adHomePerformancedto.getAdSaleNum());
                        //CPC,VCPM-广告销量
                        report.setOrderNum(adHomePerformancedto.getSalesNum());
                        //CPC,VCPM-“品牌新买家”订单量
                        report.setOrdersNewToBrand14d(adHomePerformancedto.getOrdersNewToBrand14d());
                        //CPC,VCPM-“品牌新买家”销售额
                        report.setSalesNewToBrand14d(adHomePerformancedto.getSalesNewToBrand14d());
                        //CPC,VCPM-“品牌新买家”销量
                        report.setUnitsOrderedNewToBrand14d(adHomePerformancedto.getUnitsOrderedNewToBrand14d());

                        //sb广告组没有本广告产品销量，设置默认值是0
                        report.setAdOrderNum(0);

                        report.setVideo5SecondViews(adHomePerformancedto.getVideo5SecondViews());
                        report.setVideo5SecondViewRate(Optional.ofNullable(adHomePerformancedto.getVideo5SecondViewRate()).map(BigDecimal::doubleValue).orElse(null));
                        report.setVideoFirstQuartileViews(adHomePerformancedto.getVideoFirstQuartileViews());
                        report.setVideoMidpointViews(adHomePerformancedto.getVideoMidpointViews());
                        report.setVideoThirdQuartileViews(adHomePerformancedto.getVideoThirdQuartileViews());
                        report.setVideoCompleteViews(adHomePerformancedto.getVideoCompleteViews());
                        report.setVideoUnmutes(adHomePerformancedto.getVideoUnmutes());
                        report.setViewableImpressions(adHomePerformancedto.getViewableImpressions());
                        report.setViewabilityRate(adHomePerformancedto.getViewabilityRate());
                        report.setViewClickThroughRate(adHomePerformancedto.getViewClickThroughRate());
                        report.setBrandedSearches(adHomePerformancedto.getBrandedSearches());
                        // 填充报告数据
                        cpcCommService.fillReportDataIntoPageVo(vo, report, shopSaleDto);
                    }
                }

                // 单独创建创意需要获取广告组里的着陆数据
                if (StringUtils.isNotBlank(amazonAdGroup.getLandingPage())) {
                    vo.setLandingPage(amazonAdGroup.getLandingPage());
                }
                if (StringUtils.isNotBlank(amazonAdGroup.getAdFormat())) {
                    vo.setAdFormat(amazonAdGroup.getAdFormat());
                }
                if (MapUtils.isNotEmpty(campaignMap) && campaignMap.get(vo.getCampaignId()) != null) {
                    AmazonAdCampaignAll adCampaign = campaignMap.get(vo.getCampaignId());
                    String brandEntityId = StringUtils.isNotBlank(adCampaign.getBrandEntityId()) ? adCampaign.getBrandEntityId() : "";
                    vo.setBrandEntityId(brandEntityId);
                }
            }
        }
        // 获取汇总信息
        AdMetricDto adMetricDto = new AdMetricDto();
        filterSumMetricData(voList, adMetricDto);
        // 填充指标占比
        filterMetricData(voList, adMetricDto);

        return page;
    }

    @Override
    public AllGroupAggregateDataResponse.GroupHomeVo getWxAllGroupAggregateDataNew(Integer puid, GroupPageParam param) {
        ShopAuth shopAuth = checkShopAuth(param);
        if (StringUtils.isBlank(param.getType())) {
            param.setType("sp");
        }
        //先取出所有数据包括对比周期
        LocalDate startDate = LocalDate.parse(param.getStartDate(),DateTimeFormatter.BASIC_ISO_DATE);
        LocalDate endDate = LocalDate.parse(param.getEndDate(),DateTimeFormatter.BASIC_ISO_DATE);
        Period between = Period.between(startDate, endDate);
        LocalDate compareStartDate = startDate.minus(between).minusDays(1L);
        LocalDate compareEndDate = startDate.minusDays(1L);
        param.setCompareStartDate(compareStartDate.toString());
        param.setCompareEndDate(compareEndDate.toString());
        AllGroupAggregateDataResponse.GroupHomeVo result = getAllGroupAggregateDataNew(puid, param);
        return AllGroupAggregateDataResponse.GroupHomeVo.newBuilder()
                .setAggregateDataVo(result.getAggregateDataVo())
                .addAllDay(result.getDayList())
                //.addAllMonth(monthPerformanceVos)
                //.addAllWeek(weekPerformanceVos)
                .build();
    }

    /**
     * 微信公众号需要增加环比和比值，
     * 查询区间扩大一倍查询，查询完毕后，按对比区间数据进行数据聚合和汇总
     * @param puid
     * @param param
     * @return
     */
    @Override
    public AllGroupAggregateDataResponse.GroupHomeVo getOldWxAllGroupAggregateData(Integer puid, GroupPageParam param) {
        long t1 = Instant.now().toEpochMilli();
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());

        // 取店铺销售额
        BigDecimal shopSalesByData = cpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByData == null) {
            shopSalesByData = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByData);
        log.info("wade广告组取店铺销售额数据花费时间 {}", Instant.now().toEpochMilli() - t1);

        if (StringUtils.isBlank(param.getType())) {
            param.setType("sp");
        }

        //先取出所有数据包括对比周期
        LocalDate startDate = LocalDate.parse(param.getStartDate(),DateTimeFormatter.BASIC_ISO_DATE);
        LocalDate endDate = LocalDate.parse(param.getEndDate(),DateTimeFormatter.BASIC_ISO_DATE);
        Period between = Period.between(startDate, endDate);
        LocalDate compareStartDate = startDate.minus(between).minusDays(1L);
        LocalDate compareEndDate = startDate.minusDays(1L);
        List<AdHomePerformancedto> reportList;
        List<AdHomePerformancedto> reportListCompare;
        //获取不同类型数据 sp、sd
        List<AdHomePerformancedto> reportDayList;  //每日汇总数据
        List<String> groupList;
        List<String> status = Lists.newArrayList(CpcStatusEnum.enabled.name());
        int productCount = 0;
        int targetCount  = 0;
        boolean isNull = false;  // 查询的数据为空
        if (Constants.SP.equalsIgnoreCase(param.getType())) {

            //标签筛选
            if(param.getAdTagId() != null){
                List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.GROUP.getType(), param.getAdTagId(), param.getType(),null);
                if (CollectionUtils.isNotEmpty(relationIds)) {
                    param.setGroupIds(relationIds);
                } else {
                    isNull = true;
                }
            }

            if(!isNull){
                if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
                    List<String> campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), param.getType(), null, null);
                    if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                        param.setCampaignIdList(campaignIds);
                    } else {
                        isNull = true;
                    }
                }
            }




            if (isNull) {
                reportList = new ArrayList<>();
                reportListCompare = new ArrayList<>();
                reportDayList  = new ArrayList<>();
            } else {

                reportList = amazonAdGroupReportDao.getSpReportByDate(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param);
                reportListCompare = amazonAdGroupReportDao.getSpReportByDate(puid, param.getShopId(), compareStartDate.format(DateTimeFormatter.BASIC_ISO_DATE), compareEndDate.format(DateTimeFormatter.BASIC_ISO_DATE), param);

                groupList = reportList.stream().map(AdHomePerformancedto::getGroupId).collect(Collectors.toList());

                reportDayList = amazonAdGroupReportDao.getSpReportByGroupIdList(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), groupList);


            }

        } else if (Constants.SD.equalsIgnoreCase(param.getType())) {

            //标签筛选
            if(param.getAdTagId() != null){
                List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.GROUP.getType(), param.getAdTagId(), param.getType(),null);
                if (CollectionUtils.isNotEmpty(relationIds)) {
                    param.setGroupIds(relationIds);
                } else {
                    isNull = true;
                }
            }

            if(!isNull){
                if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
                    List<String> campaignIds = amazonSdAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId());
                    if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                        param.setCampaignIdList(campaignIds);
                    } else {
                        isNull = true;
                    }
                }

            }


            if (isNull) {
                reportList = new ArrayList<>();
                reportListCompare = new ArrayList<>();
                reportDayList  = new ArrayList<>();
            } else {

                    reportList = amazonAdSdGroupReportDao.getSdReportByDate(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param);
                    reportListCompare = amazonAdSdGroupReportDao.getSdReportByDate(puid, param.getShopId(), compareStartDate.format(DateTimeFormatter.BASIC_ISO_DATE), compareEndDate.format(DateTimeFormatter.BASIC_ISO_DATE), param);
                    groupList = reportList.stream().map(AdHomePerformancedto::getGroupId).collect(Collectors.toList());
                    reportDayList = amazonAdSdGroupReportDao.getSdReportByGroupIdList(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), groupList);


            }
        } else {
            //标签筛选
            if(param.getAdTagId() != null){
                List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.GROUP.getType(), param.getAdTagId(), param.getType(),null);
                if (CollectionUtils.isNotEmpty(relationIds)) {
                    param.setGroupIds(relationIds);
                } else {
                    isNull = true;
                }
            }

            if(!isNull){
                if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
                    List<String> campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(),param.getType(), null, null);
                    if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                        param.setCampaignIdList(campaignIds);
                    } else {
                        isNull = true;
                    }
                }

            }


            if (isNull) {
                reportList = new ArrayList<>();
                reportListCompare = new ArrayList<>();
                reportDayList  = new ArrayList<>();
            } else {

                reportList = amazonAdSbGroupReportDao.getSbReportByDate(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param);
                reportListCompare = amazonAdSbGroupReportDao.getSbReportByDate(puid, param.getShopId(), compareStartDate.format(DateTimeFormatter.BASIC_ISO_DATE), compareEndDate.format(DateTimeFormatter.BASIC_ISO_DATE), param);
                groupList = reportList.stream().map(AdHomePerformancedto::getGroupId).collect(Collectors.toList());
                reportDayList = amazonAdSbGroupReportDao.getSbReportByGroupIdList(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), groupList);



            }
        }

        //汇总指标数据
        AdHomeAggregateDataRpcVo groupAggregateDataVo = getGroupAggregateDataVo(reportList, new ArrayList<>(), shopSalesByData, BigDecimal.ZERO, isVc);
        AdHomeAggregateDataRpcVo groupAggregateDataVoCompare = getGroupAggregateDataVo(reportListCompare, new ArrayList<>(), shopSalesByData, BigDecimal.ZERO, isVc);

        AdHomeAggregateDataRpcVo groupAggregateDataChainVo = getGroupAggregateDataChainVo(groupAggregateDataVo, groupAggregateDataVoCompare);
        groupAggregateDataVo = groupAggregateDataVo.toBuilder().setAdProductNum(Int32Value.of(productCount)).setTargetingNum(Int32Value.of(targetCount)).build();
        //查询货币类型
        String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();
        //获取chart数据
        //List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, param.getStartDate(), param.getEndDate(), reportDayList, shopSalesByData);
        List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, shopSalesByData);
        //List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, shopSalesByData);

        return AllGroupAggregateDataResponse.GroupHomeVo.newBuilder()
                .setAggregateDataVo(groupAggregateDataChainVo)
                .addAllDay(dayPerformanceVos)
                //.addAllMonth(monthPerformanceVos)
                //.addAllWeek(weekPerformanceVos)
                .build();
    }

    private AdHomeAggregateDataRpcVo getGroupAggregateDataChainVo(AdHomeAggregateDataRpcVo groupAggregateDataVo,AdHomeAggregateDataRpcVo groupAggregateDataVoCompare){
        AdHomeAggregateDataRpcVo.Builder builder = groupAggregateDataVo.toBuilder();
        if (groupAggregateDataVoCompare == null) {
            return builder
                    .setAcosCompare("0")
                    .setClicksCompare(0)
                    .setCtrCompare("0")
                    .setCvrCompare("0")
                    .setImpressionsCompare(0)
                    .setAdCostCompare("0")
                    .setAdSaleCompare("0")
                    .setAdOrderNumCompare(0)
                    .setAdCostPerClickCompare("0")
                    .setAcosChain("0")
                    .setClicksChain("0")
                    .setCtrChain("0")
                    .setCvrChain("0")
                    .setImpressionsChain("0")
                    .setAdCostChain("0")
                    .setAdSaleChain("0")
                    .setAdOrderNumChain("0")
                    .setAdCostPerClickChain("0")
                    .build();
        }

        return builder
                .setAcosCompare(groupAggregateDataVoCompare.getAcos())
                .setClicksCompare(groupAggregateDataVoCompare.getClicks().getValue())
                .setCtrCompare(groupAggregateDataVoCompare.getCtr())
                .setCvrCompare(groupAggregateDataVoCompare.getCvr())
                .setImpressionsCompare(groupAggregateDataVoCompare.getImpressions().getValue())
                .setAdCostCompare(groupAggregateDataVoCompare.getAdCost())
                .setAdSaleCompare(groupAggregateDataVoCompare.getAdSale())
                .setAdOrderNumCompare(groupAggregateDataVoCompare.getAdOrderNum().getValue())
                .setAdCostPerClickCompare(groupAggregateDataVoCompare.getAdCostPerClick())
                .setAcosChain(calculationChain(new BigDecimal(groupAggregateDataVo.getAcos()),new BigDecimal(groupAggregateDataVoCompare.getAcos())))
                .setClicksChain(calculationChain(BigDecimal.valueOf(groupAggregateDataVo.getClicks().getValue()),BigDecimal.valueOf(groupAggregateDataVoCompare.getClicks().getValue())))
                .setCtrChain(calculationChain(new BigDecimal(groupAggregateDataVo.getCtr()),new BigDecimal(groupAggregateDataVoCompare.getCtr())))
                .setCvrChain(calculationChain(new BigDecimal(groupAggregateDataVo.getCvr()),new BigDecimal(groupAggregateDataVoCompare.getCvr())))
                .setImpressionsChain(calculationChain(BigDecimal.valueOf(groupAggregateDataVo.getImpressions().getValue()),BigDecimal.valueOf(groupAggregateDataVoCompare.getImpressions().getValue())))
                .setAdCostChain(calculationChain(new BigDecimal(groupAggregateDataVo.getAdCost()),new BigDecimal(groupAggregateDataVoCompare.getAdCost())))
                .setAdSaleChain(calculationChain(new BigDecimal(groupAggregateDataVo.getAdSale()),new BigDecimal(groupAggregateDataVoCompare.getAdSale())))
                .setAdOrderNumChain(calculationChain(BigDecimal.valueOf(groupAggregateDataVo.getAdOrderNum().getValue()),BigDecimal.valueOf(groupAggregateDataVoCompare.getAdOrderNum().getValue())))
                .setAdCostPerClickChain(calculationChain(new BigDecimal(groupAggregateDataVo.getAdCostPerClick()),new BigDecimal(groupAggregateDataVoCompare.getAdCostPerClick())))
                .build();
    }

    private String calculationChain(BigDecimal data,BigDecimal dataCompare){
        if((dataCompare == null || dataCompare.compareTo(BigDecimal.ZERO) == 0) && (data != null && data.compareTo(BigDecimal.ZERO) != 0)){
            return "100.00";
        }
        return MathUtil.divideByZero(MathUtil.subtract(data,dataCompare).multiply(BigDecimal.valueOf(100)),dataCompare).toString();
    }


    @Override
    public AllGroupDataResponse.GroupHomeVo getAllWxGroupData(Integer puid, GroupPageParam param) {
        long t1 = Instant.now().toEpochMilli();
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        // 取店铺销售额
        BigDecimal shopSalesByData = cpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByData == null) {
            shopSalesByData = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByData);
        log.info("wade广告组取店铺销售额数据花费时间 {}", Instant.now().toEpochMilli() - t1);

        if (StringUtils.isBlank(param.getType())) {
            param.setType("sp");
        }
        //分页处理
        Page<GroupPageVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());


        //获取不同类型数据 sp、sd
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            getSpGroupVoList(puid, param, voPage, false);
        } else if (Constants.SD.equalsIgnoreCase(param.getType())) {
            getSdGroupVoList(puid, param, voPage, false);
        } else if (Constants.SB.equalsIgnoreCase(param.getType())) {
            getSbGroupVoList(puid, param, voPage, false);
        }

        //填充标签数据
        t1 = Instant.now().toEpochMilli();
        fillAdTagData(puid, param.getShopId(), param,voPage.getRows());
        log.info("标签数据填充花费时间 {}", Instant.now().toEpochMilli() - t1);



        //分页后填充广告信息
        log.info("wade广告组排序分页花费时间 {}", Instant.now().toEpochMilli() - t1);
        fillAdInfo(puid, param.getShopId(), voPage.getRows());
        log.info("wade广告组分页后填充报告数据花费时间 {}", Instant.now().toEpochMilli() - t1);
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        AllGroupDataResponse.GroupHomeVo.Page.Builder pageBuilder = AllGroupDataResponse.GroupHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        List<GroupPageVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            List<AllGroupDataResponse.GroupHomeVo.Page.GroupPageVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                AllGroupDataResponse.GroupHomeVo.Page.GroupPageVo.Builder voBuilder = AllGroupDataResponse.GroupHomeVo.Page.GroupPageVo.newBuilder();
                if (StringUtils.isNotBlank(item.getType())) {
                    voBuilder.setType(item.getType());
                }
                if(shopAuth.getMarketplaceId() != null){
                    voBuilder.setMarketplaceId(shopAuth.getMarketplaceId());
                }
                if (item.getId() != null) {
                    voBuilder.setId(Int64Value.of(item.getId()));
                }
                if (item.getShopId() != null) {
                    voBuilder.setShopId(Int32Value.of(item.getShopId()));
                }
                if (StringUtils.isNotBlank(item.getCampaignId())) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (StringUtils.isNotBlank(item.getName())) {
                    voBuilder.setName(item.getName());
                }
                if (StringUtils.isNotBlank(item.getState())) {
                    voBuilder.setState(item.getState());
                }
                if (StringUtils.isNotBlank(item.getCampaignName())) {
                    voBuilder.setCampaignName(item.getCampaignName());
                }
                if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                    voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                }

                if (StringUtils.isNotBlank(item.getTargetingType())) {
                    voBuilder.setTargetingType(item.getTargetingType());
                }
                if (StringUtils.isNotBlank(item.getCampaignState())) {
                    voBuilder.setCampaignState(item.getCampaignState());
                }
                if (StringUtils.isNotBlank(item.getDailyBudget())) {
                    voBuilder.setDailyBudget(item.getDailyBudget());
                }
                if (StringUtils.isNotBlank(item.getAdGroupId())) {
                    voBuilder.setAdGroupId(item.getAdGroupId());
                }

                if (StringUtils.isNotBlank(item.getCreator())) {
                    voBuilder.setCreator(item.getCreator());
                }
                if (StringUtils.isNotBlank(item.getState())) {
                    voBuilder.setState(item.getState());
                }
                if (StringUtils.isNotBlank(item.getDefaultBid())) {
                    voBuilder.setDefaultBid(item.getDefaultBid());
                }
                if (item.getCreateTime() != null) {
                    voBuilder.setCreateTime(DateUtil.dateToStrWithFormat(item.getCreateTime(), "yyyy-MM-dd HH:mm"));
                }
                if (StringUtils.isNotBlank(item.getAdGroupType())) {
                    voBuilder.setAdGroupType(item.getAdGroupType());
                }
                if (item.getTargetingNum() != null) {
                    voBuilder.setTargetingNum(Int32Value.of(item.getTargetingNum()));
                }
                if (item.getAdProductNum() != null) {
                    voBuilder.setAdProductNum(Int32Value.of(item.getAdProductNum()));
                }
                if (item.getPortfolioId() != null) {
                    voBuilder.setPortfolioId(item.getPortfolioId());
                }
                if (item.getPortfolioName() != null) {
                    voBuilder.setPortfolioName(item.getPortfolioName());
                }
                if (item.getIsHidden() != null ) {
                    voBuilder.setIsHidden(item.getIsHidden());
                }

                if(StringUtils.isNotBlank(item.getBidOptimization())){
                    voBuilder.setBidOptimization(item.getBidOptimization());
                }

                if(StringUtils.isNotBlank(item.getCostType())){
                    voBuilder.setCostType(item.getCostType());
                }
                if(item.getServingStatus() != null){
                    voBuilder.setServingStatus(item.getServingStatus());
                }
                if(item.getServingStatusDec() != null){
                    voBuilder.setServingStatusDec(item.getServingStatusDec());
                }
                if(item.getServingStatusName() != null){
                    voBuilder.setServingStatusName(item.getServingStatusName());
                }
                if(CollectionUtils.isNotEmpty(item.getAdTags())){
                    item.getAdTags().forEach(e->{
                        AdTagVo.Builder builder = AdTagVo.newBuilder();
                        AdTagVo tagVo = builder.setId(Int64Value.of(e.getId())).setColor(e.getColor()).setName(e.getName()).build();
                        voBuilder.addAdTags(tagVo);
                    });
                }

                voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                voBuilder.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
                voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");

                /**
                 * TODO 广告报告重构
                 * sd广告vcpm类型报告特殊字段。
                 */
                //可见展示次数(VCPM专用)
                voBuilder.setViewImpressions(Int32Value.of(Optional.ofNullable(item.getViewImpressions()).orElse(0)));
                //每笔订单花费
                voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                //vcpm
                voBuilder.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0");
                //本广告产品订单量
                voBuilder.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
                //其他产品广告订单量
                voBuilder.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
                //本广告产品销售额
                voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                //其他产品广告销售额
                voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                //广告销量
                voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
                //本广告产品销量
                voBuilder.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
                //其他产品广告销量
                voBuilder.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));
                //“品牌新买家”订单量
                voBuilder.setOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0)));
                //“品牌新买家”订单百分比
                voBuilder.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
                //“品牌新买家”销售额
                voBuilder.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
                //“品牌新买家”销售额百分比
                voBuilder.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
                //“品牌新买家”销量
                voBuilder.setUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getUnitsOrderedNewToBrandFTD()).orElse(0)));
                //“品牌新买家”销量百分比
                voBuilder.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(item.getUnitsOrderedRateNewToBrandFTD()) ? item.getUnitsOrderedRateNewToBrandFTD() : "0");

                // 花费占比
                voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                // 销售额占比
                voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                // 订单量占比
                voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                // 销量占比
                voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");

                if (isVc) {
                    voBuilder.setAcots("-");
                    voBuilder.setCompareAcotsRate("-");
                    voBuilder.setCompareAcots("-");
                    voBuilder.setAsots("-");
                    voBuilder.setCompareAsotsRate("-");
                    voBuilder.setCompareAsots("-");
                }
                return voBuilder.build();
            }).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }


        return AllGroupDataResponse.GroupHomeVo.newBuilder()
                .setPage(pageBuilder.buildPartial())
                .build();
    }

    @Override
    public List<AdReportHourlyVO> getAdGroupDailyReport(int puid, AdHourReportRequest param, boolean isCompare) {
        //需要区分sp,sb,sd
        AdPageBasicData pageBasicData = param.getPageBasic();
        LocalDate start = LocalDate.parse(pageBasicData.getStartDate());
        LocalDate end = LocalDate.parse(pageBasicData.getEndDate());
        if (isCompare) {
            if (!pageBasicData.hasEndDateCompare() || !pageBasicData.hasStartDateCompare()) {
                //查询对比但是对比时间为空返回空list；
                return new ArrayList<>();
            }
            start = LocalDate.parse(pageBasicData.getStartDateCompare());
            end = LocalDate.parse(pageBasicData.getEndDateCompare());

        }

        List<AdGroupReportHourlyDTO> reports = Lists.newArrayList();
        Integer shopId = Optional.ofNullable(pageBasicData.getShopId()).map(Int32Value::getValue).orElse(null);
        if (Constants.SP.equalsIgnoreCase(pageBasicData.getType())) {
            reports = amazonAdGroupReportDao.getGroupReportByGroupId(puid, shopId,
                             start.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                            end.format(DateTimeFormatter.ofPattern("yyyyMMdd")), Collections.singletonList(param.getGroupId()));
        }
        if (Constants.SB.equalsIgnoreCase(pageBasicData.getType())) {
            reports = amazonAdSbGroupReportDao.getSbGroupReportByGroupId(puid, shopId,
                    start.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                    end.format(DateTimeFormatter.ofPattern("yyyyMMdd")), Collections.singletonList(param.getGroupId()));
        }
        if (Constants.SD.equalsIgnoreCase(pageBasicData.getType())) {
            reports = amazonAdSdGroupReportDao.getSdGroupReportByGroupId(puid, shopId,
                    start.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                    end.format(DateTimeFormatter.ofPattern("yyyyMMdd")), Collections.singletonList(param.getGroupId()));
        }
        return reports.stream().map(item -> {
            AdReportHourlyVO vo = new AdReportHourlyVO();

            vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                    .format(DateTimeFormatter.ISO_LOCAL_DATE));
            vo.setDate(vo.getLabel());
            vo.setAdSale(Optional.ofNullable(item.getTotalSales()).orElse( BigDecimal.ZERO));
            vo.setAdSelfSale(Optional.ofNullable(item.getAdSales()).orElse(BigDecimal.ZERO));
            vo.setAdOtherSale(Optional.ofNullable(item.getAdOtherSales()).orElse(BigDecimal.ZERO));
            vo.setAdOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
            vo.setSelfAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
            vo.setOtherAdOrderNum(MathUtil.subtractInteger(item.getOrderNum(), item.getAdOrderNum()));
            vo.setAdSaleNum(Optional.ofNullable(item.getSaleNum()).orElse(0));
            vo.setAdSelfSaleNum(Optional.ofNullable(item.getAdSaleNum()).orElse(0));
            if (Constants.SB.equalsIgnoreCase(pageBasicData.getType()) || Constants.SD.equalsIgnoreCase(pageBasicData.getType())) {
                vo.setAdOtherSaleNum(0);
            } else {
                vo.setAdOtherSaleNum(MathUtil.subtractInteger(item.getSaleNum(), item.getAdSaleNum()));
            }

            vo.setAdCost(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO));
            vo.setClicks(Long.valueOf(item.getClicks()));
            vo.setImpressions(Long.valueOf(item.getImpressions()));
            vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
            vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
            vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
            vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
            vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));

            vo.setVcpm(MathUtil.divideByThousand(vo.getAdCost(), vo.getImpressions()));

            //新增字段
            vo.setViewableImpressions(Optional.ofNullable(item.getViewableImpressions()).orElse(0));
            // 新加字段
            vo.setOrdersNewToBrand(Optional.ofNullable(item.getOrdersNewToBrand14d()).orElse(0));
            vo.setUnitsOrderedNewToBrand(Optional.ofNullable(item.getUnitsOrderedNewToBrand14d()).orElse(0));
            vo.setSalesNewToBrand(Optional.ofNullable(item.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO));

            vo.setVrt(vo.getImpressions() == 0 || vo.getViewableImpressions() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(vo.getViewableImpressions()).divide(BigDecimal.valueOf(vo.getImpressions()), 2, RoundingMode.HALF_UP));
            vo.setVCtr(vo.getViewableImpressions() == 0 || vo.getClicks() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(vo.getClicks()).divide(BigDecimal.valueOf(vo.getViewableImpressions()), 2, RoundingMode.HALF_UP));


            vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), vo.getAdOrderNum() == null ? BigDecimal.ZERO : BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), vo.getSelfAdOrderNum() == null ? BigDecimal.ZERO : BigDecimal.valueOf(vo.getSelfAdOrderNum())));
            vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(MathUtil.subtract(vo.getAdSale(), vo.getAdSelfSale()), BigDecimal.valueOf(vo.getAdOrderNum() == null ? 0 : vo.getAdOrderNum(), vo.getSelfAdOrderNum() == null ? 0 : vo.getSelfAdOrderNum())));

            vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(Optional.ofNullable(item.getOrdersNewToBrand14d()).orElse(0)), BigDecimal.valueOf(100)), BigDecimal.valueOf(Optional.ofNullable(vo.getAdOrderNum()).orElse(0))));
            vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(Optional.ofNullable(item.getUnitsOrderedNewToBrand14d()).orElse(0)), BigDecimal.valueOf(100)), BigDecimal.valueOf(Optional.ofNullable(vo.getAdSaleNum()).orElse(0))));
            vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));

            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public GroupPageVo getInfo(Integer puid, Integer shopId, String campaignId, String adGroupId, String type) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        GroupPageVo result = new GroupPageVo();
        //分类请求对应的广告组数据
        if (Constants.SP.equalsIgnoreCase(type)) {
            AmazonAdGroup spInfo = amazonAdGroupDao.getByCampaignIdAndAdGroupId(puid, shopId, campaignId, adGroupId);
            Optional.ofNullable(spInfo).ifPresent(x -> BeanUtils.copyProperties(x, result, ParamCopyUtil.checkPropertiesNullOrEmpty(spInfo)));
            Optional.ofNullable(spInfo).map(AmazonAdGroup::getDefaultBid).map(d -> Double.toString(d)).ifPresent(result::setDefaultBid);
        }
        if (Constants.SB.equalsIgnoreCase(type)) {
            //获取sb广告组数据
            AmazonSbAdGroup sbInfo = amazonSbAdGroupDao.getByCampaignIdAndGroupId(puid, shopId, campaignId, adGroupId);
            Optional.ofNullable(sbInfo).ifPresent(x -> BeanUtils.copyProperties(sbInfo, result, ParamCopyUtil.checkPropertiesNullOrEmpty(sbInfo)));
            Optional.ofNullable(sbInfo).map(AmazonSbAdGroup::getBid).map(BigDecimal::toString).ifPresent(result::setDefaultBid);
        }
        if (Constants.SD.equalsIgnoreCase(type)) {
            AmazonSdAdGroup sdInfo = amazonSdAdGroupDao.getByAdGroupId(puid, shopId, adGroupId);
            Optional.ofNullable(sdInfo).ifPresent(x -> BeanUtils.copyProperties(sdInfo, result, ParamCopyUtil.checkPropertiesNullOrEmpty(sdInfo)));
            Optional.ofNullable(sdInfo).map(AmazonSdAdGroup::getDefaultBidFixedScale).ifPresent(result::setDefaultBid);

            //SD还需要查询该广告组下所有的广告产品类型，如果包含着陆页类型的广告产品，那么需要返回标识给前端
            List<AmazonSdAdProduct> productList = amazonSdAdProductDao.listByGroupId(puid, shopId, adGroupId);
            if (CollectionUtils.isNotEmpty(productList)) {
                boolean storeProduct = productList.stream().map(AmazonSdAdProduct::getLandingPageType).filter(StringUtils::isNotEmpty)
                        .anyMatch(SDAdsLandingPageTypeEnum.STORE.getType()::equals);
                if (storeProduct) {
                    result.setLandingPageType(SDAdsLandingPageTypeEnum.STORE.getType());
                } else {
                    result.setLandingPageType("");
                }
            }

            //如果creativeType为空，其值为亚马逊默认值"IMAGE"
            if (StringUtils.isEmpty(result.getCreativeType())) {
                result.setCreativeType(SDCreativeTypeEnum.IMAGE.getValue());
            }
        }

        //还需要获取广告活动名称
        AmazonAdCampaignAll campaignInfo = amazonAdCampaignDao.getCampaignByCampaignId(puid, shopId, campaignId, type);
        Optional.ofNullable(campaignInfo).map(AmazonAdCampaignAll::getName).ifPresent(result::setCampaignName);
        Optional.ofNullable(campaignInfo).map(AmazonAdCampaignAll::getTargetingType).ifPresent(result::setCampaignTargetingType);
        Optional.ofNullable(campaignInfo).map(AmazonAdCampaignAll::getType).ifPresent(result::setType);
        Optional.ofNullable(campaignInfo).map(AmazonAdCampaignAll::getCostType).ifPresent(result::setCostType);

        if(Objects.nonNull(campaignInfo) && CampaignTypeEnum.sp.getCampaignType().equals(campaignInfo.getType())){
            Optional.ofNullable(campaignInfo.getTargetType()).ifPresent(result::setTargetingType);
        }
        if(Objects.nonNull(campaignInfo) && CampaignTypeEnum.sb.getCampaignType().equals(campaignInfo.getType())){
            Optional.ofNullable(campaignInfo.getTargetType()).ifPresent(result::setTargetingType);
            Optional.ofNullable(campaignInfo.getBrandEntityId()).ifPresent(result::setBrandEntityId);
        }
        //SB类型需要单独处理
        if(Objects.nonNull(campaignInfo) && CampaignTypeEnum.sd.getCampaignType().equals(campaignInfo.getType())){
            Optional.ofNullable(campaignInfo.getTactic()).ifPresent(result::setTargetingType);
        }
        return result;
    }

    @Override
    public Page<MultiShopGroupListVo> getMultiShopGroupList(MultiShopGroupListParam param) {

        if(CollectionUtils.isNotEmpty(param.getPortfolioIdList()) || CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            //先过滤广告活动
            List<MultiShopCampaignIdVo> campaignIdVoList = null;
            campaignIdVoList = amazonAdCampaignDao.getMultiShopCampaignId(param.getPuid(), param.getShopIdList(), param.getPortfolioIdList(), param.getCampaignIdList(), param.getAdTypeList());
            if(CollectionUtils.isNotEmpty(campaignIdVoList)) {
                Map<String,List<String>> map = campaignIdVoList.stream()
                        .collect(Collectors.groupingBy(MultiShopCampaignIdVo :: getAdType,
                                Collectors.mapping(MultiShopCampaignIdVo :: getCampaignId, Collectors.toList())));
                param.setCampaignIdMap(map);
            } else {
                //没有广告活动，直接返回空
                return new Page<MultiShopGroupListVo>(param.getPageNo(), param.getPageSize(), 0, 0, null);
            }
        }

        return amazonAdGroupDao.getMultiShopGroupList(param.getPuid(), param);
    }


    @Override
    public Page<MultiShopGroupListVo> getMultiShopGroupListDoris(MultiShopGroupListParam param) {

        if(CollectionUtils.isNotEmpty(param.getPortfolioIdList()) || CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            //先过滤广告活动
            List<MultiShopCampaignIdVo> campaignIdVoList = null;
            campaignIdVoList = amazonAdCampaignDao.getMultiShopCampaignId(param.getPuid(), param.getShopIdList(), param.getPortfolioIdList(), param.getCampaignIdList(), param.getAdTypeList());
            if(CollectionUtils.isNotEmpty(campaignIdVoList)) {
                Map<String,List<String>> map = campaignIdVoList.stream()
                        .collect(Collectors.groupingBy(MultiShopCampaignIdVo :: getAdType,
                                Collectors.mapping(MultiShopCampaignIdVo :: getCampaignId, Collectors.toList())));
                param.setCampaignIdMap(map);
            } else {
                //没有广告活动，直接返回空
                return new Page<MultiShopGroupListVo>(param.getPageNo(), param.getPageSize(), 0, 0, null);
            }
        }

        return amazonAdGroupDorisDao.getMultiShopGroupList(param.getPuid(), param);
    }

    @Override
    public List<MultiShopGroupListVo> getByShopGroupIdPair(Integer puid, List<MultiShopGroupListParam> paramList) {
        return amazonAdGroupDao.getByShopGroupIdPair(puid, paramList);
    }

    /**
     * 写入doris
     * @param amazonAdGroups
     * @param create
     */
    @Override
    public void saveDoris(List<AmazonAdGroup> amazonAdGroups, boolean create, boolean update) {
        try {
            Date date = new Date();
            List<OdsAmazonAdGroup> collect = amazonAdGroups.stream().map(x -> {
                OdsAmazonAdGroup odsAmazonAdGroup = new OdsAmazonAdGroup();
                BeanUtils.copyProperties(x, odsAmazonAdGroup);
                if (create) {
                    odsAmazonAdGroup.setCreateTime(date);
                }
                if (update) {
                    odsAmazonAdGroup.setUpdateTime(date);
                }
                if (StringUtils.isNotBlank(odsAmazonAdGroup.getState())) {
                    odsAmazonAdGroup.setState(odsAmazonAdGroup.getState().toLowerCase());
                }
                return odsAmazonAdGroup;
            }).collect(Collectors.toList());

            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sp group save doris error", e);
        }
    }

    @Override
    public List<String> getAdGroupTypeByCampaignId(Integer puid, Integer shopId, String campaignId) {
        return amazonAdGroupDao.getAdGroupTypeByCampaignId(puid, shopId, campaignId);
    }

    @Override
    public GroupProductVo getGroupProductList(Integer puid, Integer sourceShopId, Integer targetShopId, String campaignId) {
        // 从配置文件获取广告组和广告产品限制条数
        Integer productLimit = dynamicRefreshConfiguration.getProductLimit(puid);

        // 根据活动Id获取广告组列表 限制条数
        List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.listByCampaignIdLimit(puid, sourceShopId, campaignId, 20000);
        if (CollectionUtils.isEmpty(amazonAdGroups)) {
            return new GroupProductVo();
        }
        List<String> adGroupIdList = StreamUtil.toList(amazonAdGroups, AmazonAdGroup::getAdGroupId);
        int productNumber = odsAmazonAdProductDao.countByGroupIdList(puid, sourceShopId, adGroupIdList);
        if (productNumber > productLimit) {
            CompletableFuture.runAsync(() -> {
                String msg = String.format("复制广告：广告产品的数量相加之和:%d,超过%d，puid：%d，sourceShopId：%d, targetShopId：%d，campaignId： %s", productNumber, productLimit, puid, sourceShopId, targetShopId, campaignId);
                WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, msg);
            }, ThreadPoolUtil.getPrintWxLogExecutor());
            throw new SponsoredBizException("该广告活动所包含的广告产品超过"+productLimit+"个，暂不支持复制，请调整后再进行复制");
        }

        List<AdProductDetailDto> adProductDetailDtos = odsAmazonAdProductDao.listByGroupIdList(puid, sourceShopId, adGroupIdList);
        // 在线产品表不存在则从广告商品表中获取标题图片
        fillNoExistProduct(puid, sourceShopId, adProductDetailDtos);
        Map<String, List<AdProductDetailDto>> groupMap = StreamUtil.groupingBy(adProductDetailDtos, AdProductDetailDto::getAdGroupId);
        Map<String, List<AdProductDetailDto>> existgroupMap = new HashMap<>();
        Map<String, List<AdProductDetailDto>> notExistgroupMap = new HashMap<>();
        // 跨店铺处理逻辑，则获取相同Asin下的广告产品
        crossShopProduct(puid, sourceShopId, targetShopId, adProductDetailDtos, groupMap, existgroupMap, notExistgroupMap, productLimit);
        // 封装返回参数
        return buildReturnVo(amazonAdGroups, groupMap, existgroupMap, notExistgroupMap, sourceShopId, targetShopId);
    }

    /**
     * 在线产品表不存在则从广告商品表中获取标题图片填充
     */
    private void fillNoExistProduct(Integer puid, Integer sourceShopId, List<AdProductDetailDto> adProductDetailDtos) {
        if (CollectionUtils.isEmpty(adProductDetailDtos)) {
            return;
        }
        List<String> noExistSkuList = adProductDetailDtos.stream().filter(it -> it.getId() == null).map(AdProductDetailDto::getSku).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noExistSkuList)) {
            return;
        }
        List<AmazonAdProductMetadata> asinBySkus = amazonAdProductMetadataService.getAsinBySkus(puid, sourceShopId, noExistSkuList, null);
        if (CollectionUtils.isEmpty(asinBySkus)) {
            return;
        }
        Map<String, AmazonAdProductMetadata> map = StreamUtil.toMap(asinBySkus, AmazonAdProductMetadata::getSku);
        for (AdProductDetailDto adProductDetailDto : adProductDetailDtos) {
            AmazonAdProductMetadata metadata = map.get(adProductDetailDto.getSku());
            if (metadata == null) {
                continue;
            }
            adProductDetailDto.setMainImage(metadata.getImageUrl());
            adProductDetailDto.setTitle(metadata.getTitle());
            adProductDetailDto.setMarketplaceId(metadata.getMarketplaceId());
            adProductDetailDto.setExist(Boolean.FALSE);
        }
    }

    /**
     * 封装返回参数
     */
    private GroupProductVo buildReturnVo(List<AmazonAdGroup> amazonAdGroups, Map<String, List<AdProductDetailDto>> groupMap,
                                         Map<String, List<AdProductDetailDto>> existgroupMap, Map<String, List<AdProductDetailDto>> notExistgroupMap, Integer sourceShopId, Integer targetShopId) {
        GroupProductVo vo = new GroupProductVo();
        List<GroupProductVo.GroupInfo> groupInfoList = new ArrayList<>();
        for (AmazonAdGroup adGroup : amazonAdGroups) {
            GroupProductVo.GroupInfo groupInfo = BeanUtil.copyProperties(adGroup, GroupProductVo.GroupInfo.class);
            List<GroupProductVo.ProductInfo> productVoList = new ArrayList<>();
            List<AdProductDetailDto> productList = groupMap.get(adGroup.getAdGroupId());
            List<AdProductDetailDto> notExistProductList = new ArrayList<>();
            List<AdProductDetailDto> existProductList = new ArrayList<>();
            if (!sourceShopId.equals(targetShopId)) {//跨店铺
                existProductList = existgroupMap.get(adGroup.getAdGroupId());
                notExistProductList = notExistgroupMap.get(adGroup.getAdGroupId());
            } else if (CollectionUtils.isNotEmpty(productList)) {
                existProductList = productList.stream().filter(AdProductDetailDto::getExist).collect(Collectors.toList());
                notExistProductList = productList.stream().filter(it -> !it.getExist()).collect(Collectors.toList());
            }

            if (CollectionUtils.isNotEmpty(existProductList)) {
                List<GroupProductVo.ProductInfo> productInfos = BeanUtil.copyToList(existProductList, GroupProductVo.ProductInfo.class);
                productInfos.forEach(it -> {
                    it.setExist(Boolean.TRUE);
                });
                productVoList.addAll(productInfos);
            }
            if (CollectionUtils.isNotEmpty(notExistProductList)) {
                List<GroupProductVo.ProductInfo> notExistProducts = BeanUtil.copyToList(notExistProductList, GroupProductVo.ProductInfo.class);
                notExistProducts.forEach(it -> {
                    it.setExist(Boolean.FALSE);
                });
                productVoList.addAll(notExistProducts);
            }
            groupInfo.setProductList(productVoList);
            groupInfoList.add(groupInfo);
        }
        vo.setGroupList(groupInfoList);
        return vo;
    }

    /**
     * 复制广告-跨店铺获取存在和不存在的广告产品
     * 存在的广告产品为相同asin下的所有广告产品
     */
    private void crossShopProduct(Integer puid, Integer sourceShopId, Integer targetShopId, List<AdProductDetailDto> adProductDetailDtos,
                                  Map<String, List<AdProductDetailDto>> groupMap, Map<String, List<AdProductDetailDto>> existgroupMap,
                                  Map<String, List<AdProductDetailDto>> notExistgroupMap, Integer productLimit) {
        if (!sourceShopId.equals(targetShopId) && CollectionUtils.isNotEmpty(adProductDetailDtos)) {
            List<String> asinList = StreamUtil.toList(adProductDetailDtos, AdProductDetailDto::getAsin);
            List<OdsProduct> odsProducts = new ArrayList<>();
            Lists.partition(asinList, 5000).forEach(list -> {
                odsProducts.addAll(odsProductDao.listByAsin(puid, targetShopId, list, null));
            });
            Map<String, List<OdsProduct>> asinMap = StreamUtil.groupingBy(odsProducts, OdsProduct::getAsin);
            for (String groupId : groupMap.keySet()) {
                List<AdProductDetailDto> productList = groupMap.get(groupId);
                if (CollectionUtils.isEmpty(productList)) {
                    continue;
                }
                productList = productList.subList(0, Math.min(productLimit, productList.size()));
                List<AdProductDetailDto> existProductList = new ArrayList<>();
                List<AdProductDetailDto> notExistProductList = new ArrayList<>();
                List<String> containAsinList = new ArrayList<>();
                for (AdProductDetailDto product : productList) {
                    List<OdsProduct> products = asinMap.get(product.getAsin());
                    if (CollectionUtils.isEmpty(products)) {
                        notExistProductList.add(product);
                    } else {
                        // 当存在asin相同的产品时，会添加两次 过滤相同的数据
                        if (containAsinList.contains(product.getAsin())) {
                            continue;
                        }
                        existProductList.addAll(BeanUtil.copyToList(products, AdProductDetailDto.class));
                        containAsinList.add(product.getAsin());
                    }
                }
                existgroupMap.put(groupId, existProductList);
                notExistgroupMap.put(groupId, notExistProductList);
            }
        }
    }

    private void saveDoris(Integer puid, Integer shopId, List<String> groupIdList) {
        try {
            if (CollectionUtils.isEmpty(groupIdList)) {
                return;
            }
            Date date = new Date();
            List<AmazonAdGroup> list = amazonAdGroupDao.getAdGroupByIds(puid, shopId, groupIdList);
            List<OdsAmazonAdGroup> collect = list.stream().map(x -> {
                OdsAmazonAdGroup odsAmazonAdGroup = new OdsAmazonAdGroup();
                BeanUtils.copyProperties(x, odsAmazonAdGroup);
                if (StringUtils.isNotBlank(odsAmazonAdGroup.getState())) {
                    odsAmazonAdGroup.setState(odsAmazonAdGroup.getState().toLowerCase());
                }
                return odsAmazonAdGroup;
            }).collect(Collectors.toList());

            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sp group save doris error", e);
        }
    }

    private AmazonAdGroupDorisAllReport initGroupReportData(String adGroupId) {
        AmazonAdGroupDorisAllReport amazonAdGroupDorisAllReport = new AmazonAdGroupDorisAllReport();
        amazonAdGroupDorisAllReport.setAdGroupId(adGroupId);
        amazonAdGroupDorisAllReport.setCost(BigDecimal.ZERO);
        amazonAdGroupDorisAllReport.setTotalSales(BigDecimal.ZERO);
        amazonAdGroupDorisAllReport.setAdSales(BigDecimal.ZERO);
        amazonAdGroupDorisAllReport.setImpressions(0);
        amazonAdGroupDorisAllReport.setOrderNum(0);
        amazonAdGroupDorisAllReport.setClicks(0);
        amazonAdGroupDorisAllReport.setAdOrderNum(0);
        amazonAdGroupDorisAllReport.setSaleNum(0);
        amazonAdGroupDorisAllReport.setAdSaleNum(0);
        return amazonAdGroupDorisAllReport;
    }

    @Override
    public List<AmazonAdGroupDorisAllReport> getGroupClickData(MultiShopGroupByProductParam param, List<OdsAmazonAdProduct> productGroup) {
        return amazonAdGroupDorisDao.getGroupClickData(param, productGroup);
    }
}
