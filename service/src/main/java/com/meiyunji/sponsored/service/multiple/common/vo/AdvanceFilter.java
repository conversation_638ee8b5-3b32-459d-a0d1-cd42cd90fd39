package com.meiyunji.sponsored.service.multiple.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 高级刷选参数
 * @Author: zzh
 * @Date: 2025/4/17 13:24
 */
@Data
public class AdvanceFilter {

    @ApiModelProperty(value = "高级搜索展示量最小值")
    private Integer impressionsMin;

    @ApiModelProperty(value = "高级搜索展示量最大值")
    private Integer impressionsMax;

    @ApiModelProperty(value = "高级搜索点击量小值")
    private Integer clicksMin;

    @ApiModelProperty(value = "高级搜索点击量大值")
    private Integer clicksMax;

    @ApiModelProperty(value = "高级搜索点击率小值")
    private BigDecimal clickRateMin;

    @ApiModelProperty(value = "高级搜索点击率大值")
    private BigDecimal clickRateMax;

    @ApiModelProperty(value = "高级搜索花费小值")
    private BigDecimal costMin;

    @ApiModelProperty(value = "高级搜索花费大值")
    private BigDecimal costMax;

    @ApiModelProperty(value = "高级搜索cpc小值")
    private BigDecimal cpcMin;

    @ApiModelProperty(value = "高级搜索cpc大值")
    private BigDecimal cpcMax;

    @ApiModelProperty(value = "高级搜索广告订单量小值")
    private Integer orderNumMin;

    @ApiModelProperty(value = "高级搜索广告订单量大值")
    private Integer orderNumMax;

    @ApiModelProperty(value = "高级搜索sales小值")
    private BigDecimal salesMin;

    @ApiModelProperty(value = "高级搜索sales大值")
    private BigDecimal salesMax;

    @ApiModelProperty(value = "高级搜索acos小值")
    private BigDecimal acosMin;

    @ApiModelProperty(value = "高级搜索acos大值")
    private BigDecimal acosMax;

    @ApiModelProperty(value = "高级搜索roas小值")
    private BigDecimal roasMin;

    @ApiModelProperty(value = "高级搜索roas大值")
    private BigDecimal roasMax;

    @ApiModelProperty(value = "高级搜索广告订单转化率小值")
    private BigDecimal salesConversionRateMin;

    @ApiModelProperty(value = "高级搜索广告订单转化率大值")
    private BigDecimal salesConversionRateMax;

    @ApiModelProperty(value = "高级搜索acots小值")
    private BigDecimal acotsMin;

    @ApiModelProperty(value = "高级搜索acots大值")
    private BigDecimal acotsMax;

    @ApiModelProperty(value = "高级搜索asots小值")
    private BigDecimal asotsMin;

    @ApiModelProperty(value = "高级搜索asots大值")
    private BigDecimal asotsMax;

    @ApiModelProperty(value = "高级搜索投放类型")
    private String filterTargetType;

    @ApiModelProperty(value = "高级搜索默认竞价最小")
    private BigDecimal biddingMin;

    @ApiModelProperty(value = "高级搜索默认竞价最大")
    private BigDecimal biddingMax;

    @ApiModelProperty(value = "高级搜索店铺销售额")
    private BigDecimal shopSales;

    @ApiModelProperty(value = "高级搜索广告花费占比最小")
    private BigDecimal adCostPercentageMin;

    @ApiModelProperty(value = "高级搜索广告花费占比最大")
    private BigDecimal adCostPercentageMax;

    @ApiModelProperty(value = "高级搜索广告销售额占比最小")
    private BigDecimal adSalePercentageMin;

    @ApiModelProperty(value = "高级搜索广告销售额占比最大")
    private BigDecimal adSalePercentageMax;

    @ApiModelProperty(value = "高级搜索广告订单量占比最小")
    private BigDecimal adOrderNumPercentageMin;

    @ApiModelProperty(value = "高级搜索广告订单量占比最大")
    private BigDecimal adOrderNumPercentageMax;

    @ApiModelProperty(value = "高级搜索广告销量占比最小")
    private BigDecimal orderNumPercentageMin;

    @ApiModelProperty(value = "高级搜索广告销量占比最大")
    private BigDecimal orderNumPercentageMax;

    @ApiModelProperty(value = "可见展示次数最小值")
    private Integer viewImpressionsMin;

    @ApiModelProperty(value = "可见展示次数最大值")
    private Integer viewImpressionsMax;

    @ApiModelProperty(value = "cpa最小值")
    private BigDecimal cpaMin;

    @ApiModelProperty(value = "cpa最大值")
    private BigDecimal cpaMax;

    @ApiModelProperty(value = "vcpm最小值")
    private BigDecimal vcpmMin;

    @ApiModelProperty(value = "vcpm最大值")
    private BigDecimal vcpmMax;

    @ApiModelProperty(value = "本广告产品订单量最小值")
    private Integer adSaleNumMin;

    @ApiModelProperty(value = "本广告产品订单量最大值")
    private Integer adSaleNumMax;

    @ApiModelProperty(value = "其他产品广告订单量最小值")
    private Integer adOtherOrderNumMin;

    @ApiModelProperty(value = "其他产品广告订单量最大值")
    private Integer adOtherOrderNumMax;

    @ApiModelProperty(value = "本广告产品销售额最小值")
    private BigDecimal adSalesMin;

    @ApiModelProperty(value = "本广告产品销售额最大值")
    private BigDecimal adSalesMax;

    @ApiModelProperty(value = "其他产品广告销售额最小值")
    private BigDecimal adOtherSalesMin;

    @ApiModelProperty(value = "其他产品广告销售额最大值")
    private BigDecimal adOtherSalesMax;

    @ApiModelProperty(value = "本廣告产品销量最小值")
    private Integer adSelfSaleNumMin;

    @ApiModelProperty(value = "本廣告产品销量最大值")
    private Integer adSelfSaleNumMax;

    @ApiModelProperty(value = "其他产品广告销量最小值")
    private Integer adOtherSaleNumMin;

    @ApiModelProperty(value = "其他产品广告销量最大值")
    private Integer adOtherSaleNumMax;

    @ApiModelProperty(value = "广告销量最小值")
    private Integer adSalesTotalMin;

    @ApiModelProperty(value = "广告销量最大值")
    private Integer adSalesTotalMax;

    @ApiModelProperty(value = "“品牌新买家”订单量最小值")
    private Integer ordersNewToBrandFTDMin;

    @ApiModelProperty(value = "“品牌新买家”订单量最大值")
    private Integer ordersNewToBrandFTDMax;

    @ApiModelProperty(value = "“品牌新买家”订单百分比最小值")
    private BigDecimal orderRateNewToBrandFTDMin;

    @ApiModelProperty(value = "“品牌新买家”订单百分比最大值")
    private BigDecimal orderRateNewToBrandFTDMax;

    @ApiModelProperty(value = "“品牌新买家”销售额最小值")
    private BigDecimal salesNewToBrandFTDMin;

    @ApiModelProperty(value = "“品牌新买家”销售额最大值")
    private BigDecimal salesNewToBrandFTDMax;

    @ApiModelProperty(value = "“品牌新买家”销售额百分比最小值")
    private BigDecimal salesRateNewToBrandFTDMin;

    @ApiModelProperty(value = "“品牌新买家”销售额百分比最大值")
    private BigDecimal salesRateNewToBrandFTDMax;

    @ApiModelProperty(value = "“品牌新买家”销量最小值")
    private Integer unitsOrderedNewToBrandFTDMin;

    @ApiModelProperty(value = "“品牌新买家”销量最大值")
    private Integer unitsOrderedNewToBrandFTDMax;

    @ApiModelProperty(value = "“品牌新买家”销量百分比最小值")
    private BigDecimal unitsOrderedRateNewToBrandFTDMin;

    @ApiModelProperty(value = "“品牌新买家”销量百分比最大值")
    private BigDecimal unitsOrderedRateNewToBrandFTDMax;

    @ApiModelProperty(value = "“品牌新买家”订单转化率最小值")
    private BigDecimal brandNewBuyerOrderConversionRateMin;

    @ApiModelProperty(value = "“品牌新买家”订单转化率最大值")
    private BigDecimal brandNewBuyerOrderConversionRateMax;

    @ApiModelProperty("5秒观看次数最小值")
    private Integer video5SecondViewsMin;

    @ApiModelProperty("5秒观看次数最大值")
    private Integer video5SecondViewsMax;

    @ApiModelProperty("视频完整播放次数最小值")
    private Integer videoCompleteViewsMin;

    @ApiModelProperty("视频完整播放次数最大值")
    private Integer videoCompleteViewsMax;

    @ApiModelProperty("观看率最小值")
    private BigDecimal viewabilityRateMin;

    @ApiModelProperty("观看率最大值")
    private BigDecimal viewabilityRateMax;

    @ApiModelProperty("观看点击率最小值")
    private BigDecimal viewClickThroughRateMin;

    @ApiModelProperty("观看点击率最大值")
    private BigDecimal viewClickThroughRateMax;

    @ApiModelProperty("品牌搜索次数最小值")
    private Integer brandedSearchesMin;

    @ApiModelProperty("品牌搜索次数最大值")
    private Integer brandedSearchesMax;

    @ApiModelProperty("广告笔单价最小值")
    private BigDecimal advertisingUnitPriceMin;

    @ApiModelProperty("广告笔单价最大值")
    private BigDecimal advertisingUnitPriceMax;

    @ApiModelProperty("搜索结果首页首位IS最小值")
    private BigDecimal topImpressionShareMin;

    @ApiModelProperty("搜索结果首页首位IS最大值")
    private BigDecimal topImpressionShareMax;

    @ApiModelProperty("搜索词排名最小值")
    private Integer searchFrequencyRankMin;

    @ApiModelProperty("搜索词排名最大值")
    private Integer searchFrequencyRankMax;

    @ApiModelProperty("搜索词排名周变化率最小值")
    private BigDecimal weekRatioMin;

    @ApiModelProperty("搜索词排名周变化率最大值")
    private BigDecimal weekRatioMax;

    @ApiModelProperty("加购次数最小值")
    private Integer addToCartMin;

    @ApiModelProperty("加购次数最大值")
    private Integer addToCartMax;
}
