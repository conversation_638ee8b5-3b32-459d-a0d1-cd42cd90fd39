package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdKeywordDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdKeyword;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.SBThemesEnum;
import com.meiyunji.sponsored.service.enums.SpKeywordGroupValueEnum;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardKeywordReqVo;
import com.meiyunji.sponsored.service.util.Constant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * amazon广告关键词表(OdsAmazonAdKeyword)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:18
 */
@Repository
public class OdsAmazonAdKeywordDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdKeyword> implements IOdsAmazonAdKeywordDao {

    @Override
    public List<OdsAmazonAdKeyword> getByKeywordIds(int puid, Integer shopId, List<String> keywordIds) {
        StringBuilder sb = new StringBuilder("select * from ").append(this.getJdbcHelper().getTable())
                .append(" where puid = ? and shop_id = ? ");
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        sb.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", keywordIds, argsList));
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(OdsAmazonAdKeyword.class));
    }

    @Override
    public List<OdsAmazonAdKeyword> getByKeywordIdsAndShopIds(int puid, List<Integer> shopList, List<String> keywordIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuffer selectSql = new StringBuffer("SELECT * ");
        selectSql.append(" from ").append(getJdbcHelper().getTable()).append(" k ");
        selectSql.append(" where k.puid = ? ");
        argsList.add(puid);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(shopList)) {
            selectSql.append(SqlStringUtil.dealInList("k.shop_id", shopList, argsList));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(keywordIds)) {
            selectSql.append(SqlStringUtil.dealInList("k.keyword_id", keywordIds, argsList));
        }
        return getJdbcTemplate().query(selectSql.toString(), argsList.toArray(), new ObjectMapper<>(OdsAmazonAdKeyword.class));
    }

    @Override
    public List<OdsAmazonAdKeyword> listKeyWordAndMatchTypeByGroupIdList(Integer puid, Integer shopId, List<String> groupIdList) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("ad_group_id", groupIdList.toArray(new String[]{}))
                .equalTo("type", "biddable")
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()})
                .build();
        String sql = "select keyword_text, match_type, ad_group_id from " + this.getJdbcHelper().getTable() + " where " + builder.getSql();
        return getJdbcTemplate().query(sql, builder.getValues(), new ObjectMapper<>(OdsAmazonAdKeyword.class));
    }

    @Override
    public List<KeywordLibsVo> getSpKeywordCount(Integer puid, List<String> shopIds, KeywordLibsPageParam param, List<String> keywordTexts) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select keyword_text keywordText, count(*) targetNum from ods_t_amazon_ad_keyword " +
                "where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)){
            sql.append(SqlStringUtil.dealInList("shop_id",shopIds,argsList));
        }
        //通过来源站点筛选
        if(CollectionUtils.isNotEmpty(param.getCountry())){
            sql.append(SqlStringUtil.dealInList("marketplace_id", param.getCountry(), argsList));
        }
        if (CollectionUtils.isNotEmpty(keywordTexts)){
            // 创建一个新的列表来存储转换后的字符串
            List<String> lowerCaseList = new ArrayList<>();
            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
            for (String str : keywordTexts) {
                lowerCaseList.add(str.toLowerCase());
            }
            sql.append(SqlStringUtil.dealInList("lower(keyword_text)",lowerCaseList,argsList));
        }
        sql.append(" group by keyword_text");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(KeywordLibsVo.class), argsList.toArray());
    }

    @Override
    public Page<OdsAmazonAdKeyword> listPageByCondition(Integer puid, List<Integer> shopIds, KeywordLibsPageParam param) {
        List<Object> args = new ArrayList<>();
        String spSql = buildSql(puid, shopIds, param, "ods_t_amazon_ad_keyword", args);
        String sbSql = buildSql(puid, shopIds, param, "ods_t_amazon_ad_keyword_sb", args);
        String totalSql = spSql + " union all " + sbSql;
        String countSql = " select count(*)  from ( " + totalSql + " ) r";
        String selectSql = " select shop_id, campaign_id, create_time from ( " + totalSql + " ) r order by create_time desc";
        Object[] arg = args.toArray();
        return getPageResult(param.getPageNo(), param.getPageSize(), countSql, arg, selectSql, arg, OdsAmazonAdKeyword.class);
    }

    @Override
    public List<OdsAmazonAdKeyword> listAllGroupByCondition(Integer puid, List<Integer> shopIds, KeywordLibsPageParam param) {
        List<Object> args = new ArrayList<>();
        String spSql = buildGroupSql(puid, shopIds, param, "ods_t_amazon_ad_keyword", args);
        String sbSql = buildGroupSql(puid, shopIds, param, "ods_t_amazon_ad_keyword_sb", args);
        String totalSql = spSql + " union all " + sbSql;
        String selectSql = " select shop_id , ad_group_id  from ( " + totalSql + " ) r ";
        Object[] arg = args.toArray();
        return getJdbcTemplate().query(selectSql, arg, new ObjectMapper<>(OdsAmazonAdKeyword.class));
    }

    @Override
    public List<OdsAmazonAdKeyword> listAllNeGroupByCondition(Integer puid, List<Integer> shopIds, KeywordLibsPageParam param) {
        List<Object> args = new ArrayList<>();
        String spSql = buildGroupSql(puid, shopIds, param, "ods_t_amazon_ad_ne_keyword", args);
        String sbSql = buildGroupSql(puid, shopIds, param, "ods_t_amazon_ad_nekeyword_sb", args);
        String totalSql = spSql + " union all " + sbSql;
        String selectSql = " select shop_id , ad_group_id  from ( " + totalSql + " ) r ";
        Object[] arg = args.toArray();
        return getJdbcTemplate().query(selectSql, arg, new ObjectMapper<>(OdsAmazonAdKeyword.class));
    }

    @Override
    public List<OdsAmazonAdKeyword> getKeywordText(Integer puid, int start, int limit) {
        String sql = "select puid, shop_id, keyword_id, keyword_text, keyword_size from ods_t_amazon_ad_keyword where puid = ? order by id limit ?, ?";
        return getJdbcTemplate().queryForList(sql, OdsAmazonAdKeyword.class, puid, start, limit);
    }

    @Override
    public void updateKeywordSizeById(Integer puid, List<OdsAmazonAdKeyword> keywords) {
        if (CollectionUtils.isEmpty(keywords)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(keywords.size());
        List<Object> arg;
        for (OdsAmazonAdKeyword keyword : keywords) {
            arg = new ArrayList<>();
            arg.add(keyword.getKeywordSize());
            arg.add(keyword.getPuid());
            arg.add(keyword.getShopId());
            arg.add(keyword.getKeywordId());
            arg.add(keyword.getKeywordText());
        }

        String sql = "update " + getJdbcHelper().getTable() + " set keyword_size = ? where puid = ? and shop_id = ? and keyword_id = ? and keyword_text = ?";
        getJdbcTemplate().batchUpdate(sql, argList);
    }

    @Override
    public List<String> getKeywordIdsByAdGroupId(int puid, List<Integer> shopIdList, String spCampaignId, List<String> spAdGroupIds) {
        StringBuilder sqlBuilder = new StringBuilder("select keyword_id from ods_t_amazon_ad_keyword where puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        sqlBuilder.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));

        if (StringUtils.isNotBlank(spCampaignId)) {
            sqlBuilder.append(" and campaign_id = ? ");
            args.add(spCampaignId);
        }

        sqlBuilder.append(SqlStringUtil.dealInList("ad_group_id", spAdGroupIds, args));
        return getJdbcTemplate().query(sqlBuilder.toString(), (rs, rowNum) -> rs.getString("keyword_id"), args.toArray());
    }

    @Override
    public Page<RepeatTargetingCountVo> getKeywordTargeting(Integer puid, RepeatTargetingCountPageVo vo) {
        List<Object> args = new ArrayList<>();
        StringBuilder countSql = new StringBuilder("select count(*) from (");
        StringBuilder sql = new StringBuilder("select keyword_text keywordText, match_type matchType, 'sp' as type, count(*) targetNum ");
        sql.append(" from ods_t_amazon_ad_keyword where puid = ? and marketplace_id = ? ");
        args.add(puid);
        args.add(vo.getMarketplaceId());
        sql.append(" and keyword_text != '(_targeting_auto_)' and keyword_text != '*' ");
        //顶部店铺筛选
        if (CollectionUtils.isNotEmpty(vo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", vo.getShopIdList(), args));
        }
        //词组数量筛选
        if (CollectionUtils.isNotEmpty(vo.getKeywordSize())) {
            sql.append(SqlStringUtil.dealInList("(keyword_size", vo.getKeywordSize(), args));
            if (vo.getKeywordSize().contains(5)) {
                sql.append(" or keyword_size > 5");
            }
            sql.append(")");
        }
        //ASIN筛选-转换为通过广告组Id筛选
        if (CollectionUtils.isNotEmpty(vo.getSpAdGroupId())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", vo.getSpAdGroupId(), args));
        }
        //匹配类型筛选
        if (CollectionUtils.isNotEmpty(vo.getMatchType())) {
            sql.append(SqlStringUtil.dealInList("match_type", vo.getMatchType(), args));
        }
        //关键词筛选
        //模糊与精准匹配查询
        if (StringUtils.isNotEmpty(vo.getSearchValue()) && StringUtils.isNotEmpty(vo.getSearchType())) {
            if ("exact".equalsIgnoreCase(vo.getSearchType())) {
                sql.append(" and lower(keyword_text) = ? ");
                args.add(vo.getSearchValue().trim().toLowerCase());
            } else {
                sql.append(" and lower(keyword_text) like ? ");
                args.add("%" + vo.getSearchValue().trim().toLowerCase() + "%");
            }
        }
        sql.append(" group by keyword_text, match_type order by targetNum ");
        if (StringUtils.isNotBlank(vo.getOrderType())) {
            sql.append(vo.getOrderType());
        } else {
            sql.append("desc");
        }
        sql.append(", keywordText ");
        if (StringUtils.isNotBlank(vo.getOrderType())) {
            sql.append(vo.getOrderType());
        } else {
            sql.append("desc");
        }
        countSql.append(sql + ") s");
        Object[] arg = args.toArray();
        return getPageResultByClass(vo.getPageNo(), vo.getPageSize(), countSql.toString(), arg, sql.toString(), arg, RepeatTargetingCountVo.class);
    }

    @Override
    public List<RepeatTargetingCountVo> getKeywordTargetingByReportingIndex(Integer puid, RepeatTargetingCountPageVo vo, List<RepeatTargetingCountVo> rows) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select any(keyword_text) keywordText, any(match_type) matchType, 'sp' as type, count(*) targetNum ");
        sql.append(" from ods_t_amazon_ad_keyword where puid = ? and marketplace_id = ? ");
        args.add(puid);
        args.add(vo.getMarketplaceId());
        //顶部店铺筛选
        if (CollectionUtils.isNotEmpty(vo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", vo.getShopIdList(), args));
        }
        //词组数量筛选
        if (CollectionUtils.isNotEmpty(vo.getKeywordSize())) {
            sql.append(SqlStringUtil.dealInList("(keyword_size", vo.getKeywordSize(), args));
            if (vo.getKeywordSize().contains(5)) {
                sql.append(" or keyword_size > 5");
            }
            sql.append(")");
        }
        //ASIN筛选-转换为通过广告组Id筛选
        if (CollectionUtils.isNotEmpty(vo.getSpAdGroupId())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", vo.getSpAdGroupId(), args));
        }
        //匹配类型筛选
        if (CollectionUtils.isNotEmpty(vo.getMatchType())) {
            sql.append(SqlStringUtil.dealInList("match_type", vo.getMatchType(), args));
        }
        sql.append(SqlStringUtil.dealMultiInList(Arrays.asList("keyword_text", "match_type"), rows, args, Arrays.asList(RepeatTargetingCountVo::getKeywordText, i -> i.getMatchType().toLowerCase())));
        sql.append(" group by keyword_text, match_type ");
        Object[] arg = args.toArray();
        return getJdbcTemplate().query(sql.toString(), arg, new ObjectMapper<>(RepeatTargetingCountVo.class));
    }

    @Override
    public int listAllKeywordByCondition(Integer puid, RepeatTargetingDetailPageVo detailPageVo) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select count(*) from (select count(*) from ods_t_amazon_ad_keyword k ");
        //高级筛选
        if (detailPageVo.getUseAdvanced() && this.isBoolean(detailPageVo)) {
            sql.append(" left join ods_t_amazon_ad_keyword_report r on k.puid = r.puid and k.shop_id = r.shop_id and k.keyword_id = r.keyword_id and k.marketplace_id = r.marketplace_id ");
            sql.append(" and r.puid = ? and r.marketplace_id = ? ");
            args.add(puid);
            args.add(detailPageVo.getMarketplaceId());
            sql.append(" and lower(r.keyword_text) = ? and lower(r.match_type) = ? ");
            args.add(detailPageVo.getKeywordText().toLowerCase());
            args.add(detailPageVo.getDetailMatchType().toLowerCase());
            sql.append(" and r.count_day >= ? and r.count_day <= ? ");
            args.add(detailPageVo.getStartDate());
            args.add(detailPageVo.getEndDate());
        }
        sql.append(this.whereSql(puid, detailPageVo, args));
        //竞价高级筛选
        if (detailPageVo.getUseAdvanced()) {
            if (detailPageVo.getBidMin() != null) {
                sql.append(" and k.bid >= ? ");
                args.add(detailPageVo.getBidMin());
            }
            if (detailPageVo.getBidMax() != null) {
                sql.append(" and k.bid <= ? ");
                args.add(detailPageVo.getBidMax());
            }
        }
        sql.append(" group by k.keyword_id, k.ad_group_id ");
        //高级筛选
        if (detailPageVo.getUseAdvanced() && this.isBoolean(detailPageVo)) {
            sql.append(this.getHavingSql(detailPageVo, args));
        }
        sql.append(" ) d");
        return countPageResult(puid, sql.toString(), args.toArray());
    }

    @Override
    public List<RepeatTargetingDetailVo> getKeywordIds(Integer puid, RepeatTargetingDetailPageVo detailPageVo) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select k.keyword_id keywordId, k.ad_group_id adGroupId from ods_t_amazon_ad_keyword k ");
        //高级筛选
        if (detailPageVo.getUseAdvanced() && this.isBoolean(detailPageVo)) {
            sql.append(" left join ods_t_amazon_ad_keyword_report r on k.puid = r.puid and k.shop_id = r.shop_id and k.keyword_id = r.keyword_id and k.marketplace_id = r.marketplace_id ");
            sql.append(" and r.puid = ? and r.marketplace_id = ? ");
            args.add(puid);
            args.add(detailPageVo.getMarketplaceId());
            sql.append(" and lower(r.keyword_text) = ? and lower(r.match_type) = ? ");
            args.add(detailPageVo.getKeywordText().toLowerCase());
            args.add(detailPageVo.getDetailMatchType().toLowerCase());
            sql.append(" and r.count_day >= ? and r.count_day <= ? ");
            args.add(detailPageVo.getStartDate());
            args.add(detailPageVo.getEndDate());
        }
        sql.append(this.whereSql(puid, detailPageVo, args));
        //竞价高级筛选
        if (detailPageVo.getUseAdvanced()) {
            if (detailPageVo.getBidMin() != null) {
                sql.append(" and k.bid >= ? ");
                args.add(detailPageVo.getBidMin());
            }
            if (detailPageVo.getBidMax() != null) {
                sql.append(" and k.bid <= ? ");
                args.add(detailPageVo.getBidMax());
            }
        }
        sql.append(" group by k.keyword_id, k.ad_group_id ");
        //高级筛选
        if (detailPageVo.getUseAdvanced() && this.isBoolean(detailPageVo)) {
            sql.append(this.getHavingSql(detailPageVo, args));
        }
        return getJdbcTemplate().query(sql.toString(), args.toArray(), new ObjectMapper<>(RepeatTargetingDetailVo.class));
    }

    private String whereSql(Integer puid, RepeatTargetingDetailPageVo detailPageVo, List<Object> args) {
        StringBuilder sql = new StringBuilder();
        sql.append("where k.puid = ? and k.marketplace_id = ?");
        args.add(puid);
        args.add(detailPageVo.getMarketplaceId());
        sql.append(" and k.keyword_text = ? and lower(k.match_type) = ? ");
        args.add(detailPageVo.getKeywordText());
        args.add(detailPageVo.getDetailMatchType().trim().toLowerCase());
        if (CollectionUtils.isNotEmpty(detailPageVo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("k.shop_id", detailPageVo.getShopIdList(), args));
        }
        //广告组合和广告活动筛选
        if (CollectionUtils.isNotEmpty(detailPageVo.getCampaignIdList())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("k.campaign_id", detailPageVo.getCampaignIdList(), args));
        }
        //广告组筛选
        if (CollectionUtils.isNotEmpty(detailPageVo.getSpAdGroupId())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("k.ad_group_id", detailPageVo.getSpAdGroupId(), args));
        }
        //投放运行状态
        if (CollectionUtils.isNotEmpty(detailPageVo.getStatus())) {
            sql.append(SqlStringUtil.dealInList("k.state", detailPageVo.getStatus(), args));
        }
        //投放服务状态
        if (CollectionUtils.isNotEmpty(detailPageVo.getServingStatus())) {
            sql.append(SqlStringUtil.dealInList("k.serving_status", detailPageVo.getServingStatus(), args));
        }
        return sql.toString();
    }

    private Boolean isBoolean(RepeatTargetingDetailPageVo detailPageVo) {
        if (detailPageVo.getBidMin() == null && detailPageVo.getBidMax() == null &&
                (
                        detailPageVo.getImpressionsMin() != null || detailPageVo.getImpressionsMax() != null ||
                        detailPageVo.getClicksMin() != null || detailPageVo.getClicksMax() != null ||
                        detailPageVo.getClickRateMin() != null || detailPageVo.getClickRateMax() != null ||
                        detailPageVo.getCostMin() != null || detailPageVo.getCostMax() != null ||
                        detailPageVo.getCpaMin() != null || detailPageVo.getCpaMax() != null ||
                        detailPageVo.getOrderNumMin() != null || detailPageVo.getOrderNumMax() != null ||
                        detailPageVo.getSalesMin() != null || detailPageVo.getSalesMax() != null ||
                        detailPageVo.getAcosMin() != null || detailPageVo.getAcosMax() != null ||
                        detailPageVo.getRoasMin() != null || detailPageVo.getRoasMax() != null ||
                        detailPageVo.getSalesConversionRateMin() != null || detailPageVo.getSalesConversionRateMax() != null ||
                        detailPageVo.getCpcMin() != null || detailPageVo.getCpcMax() != null
                )
        ) {
            return true;
        } else {
            return false;
        }
    }

    private String getHavingSql(RepeatTargetingDetailPageVo detailPageVo, List<Object> args) {
        if (!detailPageVo.getUseAdvanced()) {
            return "";
        }
        StringBuilder havingSql = new StringBuilder();
        havingSql.append(" having 1=1 ");
        //展示量
        if (detailPageVo.getImpressionsMin() != null) {
            havingSql.append(" and IFNULL(SUM(r.impressions), 0) >= ?");
            args.add(detailPageVo.getImpressionsMin());
        }
        if (detailPageVo.getImpressionsMax() != null) {
            havingSql.append(" and IFNULL(SUM(r.impressions), 0) <= ?");
            args.add(detailPageVo.getImpressionsMax());
        }
        //点击量
        if (detailPageVo.getClicksMin() != null) {
            havingSql.append(" and IFNULL(SUM(r.clicks), 0) >= ?");
            args.add(detailPageVo.getClicksMin());
        }
        if (detailPageVo.getClicksMax() != null) {
            havingSql.append(" and IFNULL(SUM(r.clicks), 0) <= ?");
            args.add(detailPageVo.getClicksMax());
        }
        //点击率（clicks/impressions）
        if (detailPageVo.getClickRateMin() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.clicks)/SUM(r.impressions),0),4) >= ?");
            args.add(detailPageVo.getClickRateMin());
        }
        if (detailPageVo.getClickRateMax() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.clicks)/SUM(r.impressions),0),4) <= ?");
            args.add(detailPageVo.getClickRateMax());
        }
        //花费
        if (detailPageVo.getCostMin() != null) {
            havingSql.append(" and IFNULL(SUM(r.cost), 0) >= ?");
            args.add(detailPageVo.getCostMin());
        }
        if (detailPageVo.getCostMax() != null) {
            havingSql.append(" and IFNULL(SUM(r.cost), 0) <= ?");
            args.add(detailPageVo.getCostMax());
        }
        //cpc  平均点击费用
        if (detailPageVo.getCpcMin() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.cost)/SUM(r.clicks),0),2) >= ?");
            args.add(detailPageVo.getCpcMin());
        }
        if (detailPageVo.getCpcMax() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.cost)/SUM(r.clicks),0),2) <= ?");
            args.add(detailPageVo.getCpcMax());
        }
        //广告订单量
        if (detailPageVo.getOrderNumMin() != null) {
            havingSql.append(" and IFNULL(SUM(r.sale_num), 0) >= ?");
            args.add(detailPageVo.getOrderNumMin());
        }
        if (detailPageVo.getOrderNumMax() != null) {
            havingSql.append(" and IFNULL(SUM(r.sale_num), 0) <= ?");
            args.add(detailPageVo.getOrderNumMax());
        }
        //广告销售额
        if (detailPageVo.getSalesMin() != null) {
            havingSql.append(" and IFNULL(SUM(r.total_sales), 0) >= ?");
            args.add(detailPageVo.getSalesMin());
        }
        if (detailPageVo.getSalesMax() != null) {
            havingSql.append(" and IFNULL(SUM(r.total_sales), 0) <= ?");
            args.add(detailPageVo.getSalesMax());
        }
        //订单转化率
        if (detailPageVo.getSalesConversionRateMin() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.sale_num)/SUM(r.clicks),0),4) >= ?");
            args.add(detailPageVo.getSalesConversionRateMin());
        }
        if (detailPageVo.getSalesConversionRateMax() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.sale_num)/SUM(r.clicks),0),4) <= ?");
            args.add(detailPageVo.getSalesConversionRateMax());
        }
        //acos
        if (detailPageVo.getAcosMin() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.cost)/SUM(r.total_sales),0),4) >= ?");
            args.add(detailPageVo.getAcosMin());
        }
        if (detailPageVo.getAcosMax() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.cost)/SUM(r.total_sales),0),4) <= ?");
            args.add(detailPageVo.getAcosMax());
        }
        // roas
        if (detailPageVo.getRoasMin() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.total_sales)/SUM(r.cost),0),2) >= ?");
            args.add(detailPageVo.getRoasMin());
        }
        // roas
        if (detailPageVo.getRoasMax() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.total_sales)/SUM(r.cost),0),2) <= ?");
            args.add(detailPageVo.getRoasMax());
        }
        //CPA
        if (detailPageVo.getCpaMin() != null) {
            havingSql.append(" and ROUND(ROUND(IFNULL(SUM(r.cost)/SUM(r.sale_num),0), 4), 2) >= ?");
            args.add(detailPageVo.getCpaMin());
        }
        if (detailPageVo.getCpaMax() != null) {
            havingSql.append(" and ROUND(ROUND(IFNULL(SUM(r.cost)/SUM(r.sale_num),0), 4), 2) <= ?");
            args.add(detailPageVo.getCpaMax());
        }
        return havingSql.toString();
    }

    @Override
    public Page<RepeatTargetingDetailVo> getDetailKeywordData(Integer puid, RepeatTargetingDetailPageVo detailPageVo, List<String> keywordIds) {
        List<Object> args = new ArrayList<>();
        StringBuilder countSql = new StringBuilder("select count(*) from (");
        StringBuilder sql = new StringBuilder("select any(shop_id) shopId, keyword_id keywordId, 'sp' type, any(match_type) matchType, any(state) state, any(serving_status) status, any(bid) bid,");
        sql.append(" any(suggested) suggestBid, any(range_start) rangeStart, any(range_end) rangeEnd, any(campaign_id) campaignId, any(ad_group_id) adGroupId from ods_t_amazon_ad_keyword ");
        sql.append("where puid = ? and marketplace_id = ?");
        args.add(puid);
        args.add(detailPageVo.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(detailPageVo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", detailPageVo.getShopIdList(), args));
        }
        sql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", keywordIds, args));
        sql.append(" group by keyword_id ");
        if (StringUtils.isNotBlank(detailPageVo.getOrderField()) && StringUtils.isNotBlank(detailPageVo.getOrderType()) && !"null".equals(detailPageVo.getOrderType())) {
            sql.append("order by bid ");
            sql.append(detailPageVo.getOrderType());
        } else {
            sql.append(" order by keyword_id desc");
        }
        countSql.append(sql + ") c");
        Object[] arg = args.toArray();
        return getPageResultByClass(detailPageVo.getPageNo(), detailPageVo.getPageSize(), countSql.toString(), arg, sql.toString(), arg, RepeatTargetingDetailVo.class);
    }

    @Override
    public List<RepeatTargetingDetailVo> getSpKeywordDataOrderByIndex(Integer puid, RepeatTargetingDetailPageVo detailPageVo, List<String> keywordIds) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select any(shop_id) shopId, keyword_id keywordId, 'sp' type, any(match_type) matchType, any(state) state, any(serving_status) status, any(bid) bid,");
        sql.append(" any(suggested) suggestBid, any(range_start) rangeStart, any(range_end) rangeEnd, any(campaign_id) campaignId, any(ad_group_id) adGroupId from ods_t_amazon_ad_keyword ");
        sql.append("where puid = ? and marketplace_id = ?");
        args.add(puid);
        args.add(detailPageVo.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(detailPageVo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", detailPageVo.getShopIdList(), args));
        }
        sql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", keywordIds, args));
        sql.append(" group by keyword_id ");
        Object[] arg = args.toArray();
        return getJdbcTemplate().query(sql.toString(), arg, new ObjectMapper<>(RepeatTargetingDetailVo.class));
    }

    @Override
    public List<OdsAmazonAdKeyword> listAllCampaignByCondition(Integer puid, List<Integer> shopIds, KeywordLibsPageParam param) {
        List<Object> args = new ArrayList<>();
        String spSql = buildSql(puid, shopIds, param, "ods_t_amazon_ad_keyword", args);
        String sbSql = buildSql(puid, shopIds, param, "ods_t_amazon_ad_keyword_sb", args);
        String totalSql = spSql + " union all " + sbSql;
        String selectSql = " select shop_id, campaign_id, create_time from ( " + totalSql + " ) r ";
        Object[] arg = args.toArray();
        return getJdbcTemplate().query(selectSql, arg, new ObjectMapper<>(OdsAmazonAdKeyword.class));
    }

    private String buildGroupSql(Integer puid, List<Integer> shopIds, KeywordLibsPageParam param, String tableName, List<Object> args) {
        StringBuilder sbSql = new StringBuilder("select shop_id, ad_group_id from " + tableName +
                " where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sbSql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchCampaignList())) {
            sbSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", param.getSearchCampaignList(), args));
        }
        if (StringUtils.isNotEmpty(param.getKeywordText())) {
            sbSql.append(" and lower(keyword_text) = ? ");
            args.add(param.getKeywordText().toLowerCase());
        }
        sbSql.append(" group by ad_group_id,shop_id ");
        return sbSql.toString();
    }

    private String buildSql(Integer puid, List<Integer> shopIds, KeywordLibsPageParam param, String tableName, List<Object> args) {
        StringBuilder sbSql = new StringBuilder("select shop_id, campaign_id, any(create_time) create_time from " + tableName +
                " where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sbSql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchCampaignList())) {
            sbSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", param.getSearchCampaignList(), args));
        }
        if (StringUtils.isNotEmpty(param.getKeywordText())) {
            sbSql.append(" and lower(keyword_text) = ? ");
            args.add(param.getKeywordText().toLowerCase());
        }
        sbSql.append(" group by campaign_id,shop_id ");
        return sbSql.toString();
    }

    @Override
    public List<OdsAmazonAdKeyword> geAllKeywordByKeywordText(List<String> keywords, DashboardKeywordReqVo param) {

        List<Object> args = new ArrayList<>();
        String spSql = buildGetAllKeywordByKeywordText(keywords, param, "ods_t_amazon_ad_keyword", args, CampaignTypeEnum.sp.getCampaignType());
        String sbSql = buildGetAllKeywordByKeywordText(keywords, param, "ods_t_amazon_ad_keyword_sb", args, CampaignTypeEnum.sb.getCampaignType());
        String totalSql = spSql + " union all " + sbSql;
        return getJdbcTemplate().query(totalSql, new ObjectMapper<>(OdsAmazonAdKeyword.class), args.toArray());
    }


    private String buildGetAllKeywordByKeywordText(List<String> keywords, DashboardKeywordReqVo param, String tableName, List<Object> argsList, String adType) {
        StringBuilder sql = new StringBuilder(" select keyword_text, match_type, count(1) countNum ");
        List<String> collect = keywords.stream()
                .map(e -> {
                    if (CampaignTypeEnum.sp.getCampaignType().equals(adType)) {
                        SpKeywordGroupValueEnum keywordGroupValueEnum = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByTextCn(e);
                        return keywordGroupValueEnum == null ? e : keywordGroupValueEnum.getKeywordText();
                    } else {
                        SBThemesEnum sbThemesEnumByMsg = SBThemesEnum.getSBThemesEnumByMsg(e);
                        return sbThemesEnumByMsg == null ? e : sbThemesEnumByMsg.getValue();
                    }

                }).collect(Collectors.toList());
        sql.append(buildSqlByKeywordTextWhere(collect, param, tableName ,argsList, adType, false));
        sql.append(" group by keyword_text, match_type ");
        return sql.toString();
    }






    private String buildGetAllKeywordByKeywordTextCountSql(List<String> keywords, DashboardKeywordReqVo param, String tableName, List<Object> argsList, String adType) {
        StringBuilder sql = new StringBuilder(" select count(*) num ");
        sql.append(buildSqlByKeywordTextWhere(keywords, param, tableName ,argsList, adType, true));
        return sql.toString();
    }


    private String buildGetAllKeywordByKeywordTextListSql(List<String> keywords, DashboardKeywordReqVo param, String tableName, List<Object> argsList, String adType) {
        StringBuilder sql = new StringBuilder();
        if (CampaignTypeEnum.sp.getCampaignType().equalsIgnoreCase(adType) && StringUtils.isNotBlank(param.getOrderByField()) && StringUtils.isNotBlank(param.getOrderBy()) && param.getOrderByField().equals("bid")) {
            sql.append(" select sp_k.keyword_id keyword_id, sp_k.shop_id shop_id , sp_k.create_time create_time, ifnull(sp_k.bid, sp_g.default_bid) bid ");
            sql.append(",  'sp' as type from  ");
            sql.append(" ( select puid,keyword_id, shop_id, ad_group_id, create_time , bid ");
            sql.append(buildSqlByKeywordTextWhere(keywords, param, tableName ,argsList, adType, true));
            sql.append(" ) sp_k join ods_t_amazon_ad_group sp_g " +
                    "on sp_k.puid = sp_g.puid and sp_k.shop_id = sp_g.shop_id " +
                    "and sp_k.ad_group_id = sp_g.ad_group_id and sp_g.puid = ?   ");
            argsList.add(param.getPuid());
            sql.append(SqlStringUtil.dealDorisInList("sp_g.shop_id", param.getShopIdList(), argsList));
        } else {
            sql = new StringBuilder(" select keyword_id, shop_id, create_time , bid ");
            if (CampaignTypeEnum.sp.getCampaignType().equalsIgnoreCase(adType)) {
                sql.append(",  'sp' as type ");
            } else {
                sql.append(", 'sb' as type ");
            }
            sql.append(buildSqlByKeywordTextWhere(keywords, param, tableName ,argsList, adType, true));
        }
        return sql.toString();
    }

    @Override
    public Page<OdsAmazonAdKeyword> listPageByConditionByKeywordText(List<String> keywords, DashboardKeywordReqVo param) {
        List<Object> args = new ArrayList<>();
        List<Object> countArgs = new ArrayList<>();
        List<String> sp = keywords.stream().map(e -> SpKeywordGroupValueEnum.getKeywordGroupValueEnumByTextCn(e) == null ? e : SpKeywordGroupValueEnum.getKeywordGroupValueEnumByTextCn(e).getKeywordText()).collect(Collectors.toList());
        String spSql = buildGetAllKeywordByKeywordTextListSql(sp, param, "ods_t_amazon_ad_keyword", args, CampaignTypeEnum.sp.getCampaignType());
        String spCountSql = buildGetAllKeywordByKeywordTextCountSql(sp, param, "ods_t_amazon_ad_keyword", countArgs, CampaignTypeEnum.sp.getCampaignType());
        List<String> sb = keywords.stream().map(e -> SBThemesEnum.getSBThemesEnumByMsg(e) == null ? e : SBThemesEnum.getSBThemesEnumByMsg(e).getValue()).collect(Collectors.toList());
        String sbSql = buildGetAllKeywordByKeywordTextListSql(sb, param, "ods_t_amazon_ad_keyword_sb", args, CampaignTypeEnum.sb.getCampaignType());
        String sbCountSql = buildGetAllKeywordByKeywordTextCountSql(sb, param, "ods_t_amazon_ad_keyword_sb", countArgs, CampaignTypeEnum.sb.getCampaignType());

        String pageSql = spSql + " union all " + sbSql;
        String totalSql = spCountSql + " union all " + sbCountSql;
        String countSql = " select sum(num)  from ( " + totalSql + " ) r";
        String selectSql =  "select * from (" +pageSql +") c" ;
        if (StringUtils.isNotBlank(param.getOrderByField()) && StringUtils.isNotBlank(param.getOrderBy()) && param.getOrderByField().equals("bid")) {
            selectSql = selectSql + " order by bid  " + param.getOrderBy() +  " ";
        } else {

            selectSql = selectSql + " order by create_time,keyword_id desc ";
        }

        Object[] arg = args.toArray();
        Object[] countArg = countArgs.toArray();
        return getPageResult(param.getPageNo(), param.getPageSize(), countSql, countArg, selectSql, arg, OdsAmazonAdKeyword.class);


    }

    private String buildSqlByKeywordTextWhere(List<String> keywords, DashboardKeywordReqVo param, String tableName, List<Object> argsList, String adType, boolean isList) {
        StringBuilder sql = new StringBuilder();
        sql.append(" from " + tableName + " where puid = ? ");
        argsList.add(param.getPuid());
        if (isList) {
            if (CollectionUtils.isNotEmpty(keywords)) {
                sql.append(SqlStringUtil.dealDorisInList("keyword_text", keywords, argsList));
            }
            if (StringUtils.isNotBlank(param.getMatchType())) {
                sql.append(" and  LOWER(match_type) = ? ");
                argsList.add(param.getMatchType().toLowerCase());
            }

        } else {
            sql.append(SqlStringUtil.dealDorisInList("keyword_text", keywords, argsList));
        }
        if ("sp".equalsIgnoreCase(adType)) {
            sql.append(" and type = 'biddable' ");
        }


        if (CollectionUtils.isNotEmpty(param.getMarketplaceIdList())) {
            sql.append("and marketplace_id in ('").append(StringUtils.join(param.getMarketplaceIdList(), "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
            sql.append(SqlStringUtil.dealDorisInList("shop_id", param.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) {
            sql.append(SqlStringUtil.dealDorisInList("campaign_id", param.getCampaignIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getPortfolioIds())) {

            sql.append(" and campaign_id in ( ");
            sql.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = ? ");
            argsList.add(param.getPuid());
            argsList.add(adType);
            if (param.getPortfolioIds().contains(Constant.NON_PORTFOLIO_ID)) {
                if (param.getPortfolioIds().size() == 1) {
                    sql.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(param.getPortfolioIds());
                    pr.add("");
                    sql.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append(" )  ");
                }
            } else {
                sql.append(SqlStringUtil.dealDorisInList("portfolio_id", param.getPortfolioIds(), argsList));
            }
            if (CollectionUtils.isNotEmpty(param.getMarketplaceIdList())) {
                sql.append(SqlStringUtil.dealDorisInList("marketplace_id", param.getMarketplaceIdList(), argsList));
            }
            sql.append(SqlStringUtil.dealDorisInList("shop_id", param.getShopIdList(), argsList));
            sql.append(" ) ");
        }

        return sql.toString();
    }

}

