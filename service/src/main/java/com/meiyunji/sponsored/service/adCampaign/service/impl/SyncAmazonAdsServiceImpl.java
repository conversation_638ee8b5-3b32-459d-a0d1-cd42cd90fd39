package com.meiyunji.sponsored.service.adCampaign.service.impl;

import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.adCampaign.enums.AdSyncRecord;
import com.meiyunji.sponsored.service.adCampaign.service.IAdSyncRecord;
import com.meiyunji.sponsored.service.adCampaign.service.ISyncAmazonAdService;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdSyncService;
import com.meiyunji.sponsored.service.system.po.UserSyncTypeEnum;
import com.meiyunji.sponsored.service.system.service.IUserSyncTimeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;

@Service
@Slf4j
public class SyncAmazonAdsServiceImpl implements ISyncAmazonAdService {
    @Resource
    private IAmazonAdProfileDao amazonAdProfileDao;

    @Autowired
    private ICpcAdSyncService cpcAdSyncService;

    @Autowired
    private IUserSyncTimeService syncTimeService;

    @Resource
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IAdSyncRecord iAdSyncRecord;

    @Override
    public void syncAmazonAds(List<ShopAuth> shopAuthList, int triggerChannel) throws InterruptedException {
        ThreadPoolExecutor threadPoolUtil = ThreadPoolUtil.getBatchAdManagePool();
        ThreadPoolUtil.waiting(threadPoolUtil);
        threadPoolUtil.execute(() -> {
            try {
                syncSdAds(shopAuthList, triggerChannel);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });

        threadPoolUtil.execute(() -> {
            try {
                syncSbAds(shopAuthList, triggerChannel);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });

        threadPoolUtil.execute(() -> {
            try {
                syncSpAds(shopAuthList, triggerChannel);
            }  catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });
        ThreadPoolUtil.waitingFinish(threadPoolUtil);
    }

    @Override
    public void syncSdAds(List<ShopAuth> shopAuthList, int triggerChannel) throws InterruptedException {
        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getCpcSdSyncPool();
        CountDownLatch countDownLatch = new CountDownLatch(shopAuthList.size());
        for (ShopAuth shop : shopAuthList) {
            ThreadPoolUtil.waiting(threadExecutor);
            threadExecutor.execute(() -> {
                try {
                    //先获取到配置信息
                    AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
                    if (amazonAdProfile != null && amazonAdProfile.getIsSd() == 1) {
                        cpcAdSyncService.syncSdByShop(shop, null, null);
                        syncTimeService.saveOrUpdate(shop.getPuid(), shop.getId(), shop.getMarketplaceId(), UserSyncTypeEnum.AD_SD_MANAGE);
                    }
                } catch (Exception e) {
                    log.error("cpcSdSync:", e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        countDownLatch.await();
        ThreadPoolUtil.waitingFinish(threadExecutor);
    }

    @Override
    public void syncSbAds(List<ShopAuth> shopAuthList, int triggerChannel) throws InterruptedException {
        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getCpcSbSyncPool();
        CountDownLatch countDownLatch = new CountDownLatch(shopAuthList.size());
        for (ShopAuth shopAuth : shopAuthList) {
            ThreadPoolUtil.waiting(threadExecutor);
            threadExecutor.execute(() -> {
                try {
                    //先获取到配置信息
                    AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shopAuth.getPuid(), shopAuth.getId());
                    if (amazonAdProfile != null && amazonAdProfile.getIsSb() == 1) {
                        cpcAdSyncService.syncSbByShop(shopAuth, null, null);
                        syncTimeService.saveOrUpdate(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId(), UserSyncTypeEnum.AD_SB_MANAGE);
                    }
                } catch (Exception e) {
                    log.error("syncSbAd:", e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        countDownLatch.await();
        ThreadPoolUtil.waitingFinish(threadExecutor);
    }

    @Override
    public void syncSpAds(List<ShopAuth> shopAuthList, int triggerChannel) throws InterruptedException {
        CountDownLatch countDownLatch = new CountDownLatch(shopAuthList.size());
        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getCpcSpSyncPool();
        for (ShopAuth shop : shopAuthList) {
            ThreadPoolUtil.waiting(threadExecutor);
            threadExecutor.execute(() -> {
                try {
                    //先获取到配置信息
                    AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
                    if (amazonAdProfile != null && amazonAdProfile.getIsSp() == 1) {
                        cpcAdSyncService.syncSpByShop(shop, null, null);
                        syncTimeService.saveOrUpdate(shop.getPuid(), shop.getId(), shop.getMarketplaceId(), UserSyncTypeEnum.AD_SP_MANAGE);
                    } else {
                        if (amazonAdProfile != null) {
                            cpcAdSyncService.syncSpByShop(shop, null, "3");
                            syncTimeService.saveOrUpdate(shop.getPuid(), shop.getId(), shop.getMarketplaceId(), UserSyncTypeEnum.AD_SP_MANAGE);
                        }
                    }
                } catch (Exception e) {
                    log.error("cpcSpSync:", e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        countDownLatch.await();
        ThreadPoolUtil.waitingFinish(threadExecutor);
    }

    @Override
    public void syncAmazonAdsByUid(Integer puid, int triggerChannel) throws InterruptedException {
        int start = 0, limit = 1000;
        while (true) {
            List<ShopAuth> shopAuthList = shopAuthDao.getAllValidAdShopByLimit(puid, null, start, limit);
            syncAmazonAds(shopAuthList, triggerChannel);
            if (shopAuthList.size() < limit) {
                break;
            }
            start += limit;
        }
    }

    @Override
    public void syncAmazonAdsByShop(ShopAuth shopAuth, int triggerChannel) throws InterruptedException {
        if (shopAuth == null) {
            return;
        }
        iAdSyncRecord.doSync(shopAuth.getPuid(), shopAuth.getId(), AdSyncRecord.AdTypeEnum.SHOP.getType(), triggerChannel);
        List<ShopAuth> shopAuthList = new ArrayList<>();
        shopAuthList.add(shopAuth);
        syncAmazonAds(shopAuthList, triggerChannel);
        iAdSyncRecord.doneSync(shopAuth.getPuid(), shopAuth.getId(), AdSyncRecord.AdTypeEnum.SHOP.getType(), triggerChannel);
    }

    @Override
    public void syncAmazonAdsByShopId(Integer puid, Integer shopId, int triggerChannel) throws InterruptedException {
        List<ShopAuth> shopAuthList = shopAuthDao.getAllValidAdShopByLimit(puid, shopId, 0, 1);
        if(shopAuthList.size() == 0) {
            return;
        }
        syncAmazonAds(shopAuthList, triggerChannel);
    }

}