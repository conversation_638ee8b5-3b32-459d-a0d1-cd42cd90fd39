package com.meiyunji.sponsored.service.multiPlatform.tiktok.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;

@Data
@JsonInclude(JsonInclude.Include.ALWAYS)
public class GmvMaxCampaignReportInfo {

    private Integer puid;

    private Integer shopId;
    private String shopName;
    private String storeId;
    private String storeName;

    private String advertiserId;
    private String advertiserName;

    private String currency;

    private String storeAuthorizedBcId;

    private String campaignId;
    private String campaignName;

    private String status;

    private String primaryStatus;
    private String secondaryStatus;

    private String shoppingAdsType; // 商品还是直播

    private String optimizationGoal;
    private BigDecimal roasBid;
    private BigDecimal budget;

    private String cost;
    private String orders;
    private String roi;
    private String netCost;
    private String costPerOrder;
    private String grossRevenue;

}
