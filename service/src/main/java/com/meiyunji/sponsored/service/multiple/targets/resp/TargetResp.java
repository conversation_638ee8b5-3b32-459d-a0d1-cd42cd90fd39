package com.meiyunji.sponsored.service.multiple.targets.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.cpc.vo.AdStrategyVo;
import com.meiyunji.sponsored.service.cpc.vo.KeywordsRankParamVo;
import com.meiyunji.sponsored.service.multiple.common.vo.CommonCompareReportRate;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 投放层级多店铺 响应参数
 * @Author: zzh
 * @Date: 2025/4/17 13:24
 */
@Data
@Builder
public class TargetResp extends CommonCompareReportRate {

    /**
     * 广告类型 sp sb sd
     */
    private String adType;

    /**
     * 投放类型
     */
    private String targetType;

    /**
     * 商品投放类型
     */
    private String productTargetType;

    /**
     * sd 受众投放类型
     * SBTargetingAudienceTypeEnum
     */
    private String audienceTargetType;

    /**
     * 店铺ID
     */
    private Integer shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 币种
     */
    private String currency;

    /**
     * 广告活动ID
     */
    private String campaignId;

    /**
     * 广告活动名称
     */
    private String campaignName;

    /**
     * 广告活动状态
     */
    private String campaignState;


    /**
     * 广告组状态
     */
    private String adGroupState;

    /**
     * 广告活动投放类型
     */
    private String campaignTargetingType;

    /**
     * 广告组ID
     */
    private String adGroupId;

    /**
     * 广告组名称
     */
    private String adGroupName;

    /**
     * sb广告类型 product keyword
     */
    private String groupType;

    /**
     * 关键词ID
     */
    private String keywordId;

    /**
     * 数据库主键id
     */
    private String id;

    /**
     * 投放ID 包含关键词id
     */
    private String targetId;

    /**
     * 状态
     */
    private String state;

    /**
     * 关键词
     */
    private String keywordText;

    /**
     * 关键词翻译
     */
    private String keywordTextCn;

    /**
     * 匹配方式
     */
    private String matchType;

    /**
     * 竞价
     */
    private String bid;

    /**
     * 建议竞价
     */
    private String suggestBid;

    /**
     * 建议竞价范围开始
     */
    private String rangeStart;

    /**
     * 建议竞价范围结束
     */
    private String rangeEnd;

    /**
     * 预估曝光量上限
     */
    private String estimatedImpressionUpper;

    /**
     * 预估曝光量下限
     */
    private String estimatedImpressionLower;

    /**
     * 站点id
     */
    private String marketplaceId;

    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    /**
     * 广告组合ID
     */
    private String portfolioId;

    /**
     * 广告组合名称
     */
    private String portfolioName;

    /**
     * 广告组合隐藏状态 1:隐藏  0:可见
     */
    private Integer isHidden;

    /**
     * 二级状态
     */
    private String servingStatus;

    /**
     * 二级状态描述
     */
    private String servingStatusDec;

    /**
     * 二级状态名称
     */
    private String servingStatusName;

    /**
     * 关键词实时排名参数
     */
    private KeywordsRankResp rankVo;

    /**
     * 关键词广告排名
     */
    private String advRank;

    /**
     * 搜索词首页首位曝光率 最小值~最大值
     */
    private String topImpressionShare;

    /**
     * 是否sb类型 用于判断小时级展示
     */
    private Boolean sbType;

    /**
     * 默认竞价
     */
    private BigDecimal defaultBid;

    private String adFormat;

    private String adGoal;

    private String costType;

    private String creativeType;

    /**
     * 是否更新竞价
     */
    private String isUpdateBid;

    /**
     * 是否是组受控 分时竞价
     */
    private Integer isAdGroupBidding;

    /**
     * 组分时竞价状态
     */
    private Integer pricingAdGroupBidding;

    /**
     * 域名
     */
    private String domain;

    /**
     * 投放内容
     */
    private String targetText;

    /**
     * 商品投放精准：asinSameAs  扩展类型 asinExpandedFrom
     */
    private String selectType;

    /**
     * 目录
     */
    private String category;

    /**
     * asin
     */
    private String asin;

    /**
     * 标题
     */
    private String title;

    /**
     * 图片url
     */
    private String imgUrl;

    /**
     * 品牌名：brandName
     */
    private String brandName;

    /**
     * 商品价格范围:commodityPriceRange
     */
    private String commodityPriceRange;

    /**
     * 星级:rating
     */
    private String rating;

    /**
     * 配送:distribution
     */
    private String distribution;

    /**
     * 回溯期:lookback
     */
    private String lookback;

    /**
     * 广告预算
     */
    private String dailyBudget;

    /**
     * ABA搜索词排名
     */
    private Integer searchFrequencyRank;

    /**
     * ABA搜索词排名周变化率
     */
    private BigDecimal weekRatio;

    /**
     * 竞价日志
     */
    private BidLog bidLog;

    /**
     * 广告策略集合
     */
    private List<AdStrategyVo> adStrategys;

    /**
     * 广告标签集合
     */
    private List<AdTag> adTags;
}
