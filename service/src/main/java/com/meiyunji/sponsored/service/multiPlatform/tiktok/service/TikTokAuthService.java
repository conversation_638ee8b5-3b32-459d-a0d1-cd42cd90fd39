package com.meiyunji.sponsored.service.multiPlatform.tiktok.service;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.multiPlatform.resp.AdvertiserResp;
import com.meiyunji.sponsored.service.multiPlatform.resp.AuthAdvertiserResp;
import com.meiyunji.sponsored.service.multiPlatform.shop.dao.IMultiPlatformShopAuthDao;
import com.meiyunji.sponsored.service.multiPlatform.shop.enums.TikTokAdAuthEnum;
import com.meiyunji.sponsored.service.multiPlatform.shop.dao.IMultiPlatformShopAuthDao;
import com.meiyunji.sponsored.service.multiPlatform.shop.po.MultiPlatformShopAuth;
import com.meiyunji.sponsored.service.multiPlatform.shop.service.IMultiPlatformShopService;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.dao.TikTokAdvertiserAccountDao;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.dao.TikTokStoreInfoDao;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.dto.GetAdTikTokAuthUrlDto;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.enums.TikTokAdvertiserAccountAuthStatusEnum;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokAdvertiserAccount;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokStoreToken;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request.AdTikTokAuthReq;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request.GetAdTikTokAdvertiserListReq;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request.GetAdTikTokAuthAdvertiserListReq;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request.RevokeAdTikTokAuthReq;
import com.tiktok.advertising.api.AuthApi;
import com.tiktok.advertising.exception.TikTokSDKException;
import com.tiktok.advertising.model.auth.response.AdvertiserInfoResponse;
import com.tiktok.advertising.model.auth.response.AllStoreListResponse;
import com.tiktok.advertising.model.auth.response.Oauth2AccessTokenBodyResponse;
import com.tiktok.advertising.model.auth.response.Oauth2AdvertiserGetResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TikTokAuthService {

    @Autowired
    private IMultiPlatformShopService multiPlatformShopService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplateNew;

    @Resource
    private TikTokAuthSaveService tikTokAuthSaveService;

    private final AuthApi authApi = AuthApi.getInstance();

    @Autowired
    private TikTokAdvertiserAccountDao tikTokAdvertiserAccountDao;

    @Autowired
    private IMultiPlatformShopAuthDao multiPlatformShopAuthDao;

    @Autowired
    private TikTokSyncService tikTokSyncService;

    @Resource
    private ThreadPoolTaskExecutor tiktokCampaignSyncExecutor;

    public Result<String> getAuthUrl(Integer puid, Integer uid, Integer shopId) {
        MultiPlatformShopAuth shopAuth = multiPlatformShopService.getTiktokAdShopAuth(puid, shopId);
        if (shopAuth == null) {
            return ResultUtil.returnErr("店铺已被删除，请刷新页面重试");
        }
        //授权信息存入redis，过期时间一小时
        String state = UUID.randomUUID().toString().replace("-", "");
        String cacheKey = this.getCacheKeyByState(puid, state);
        GetAdTikTokAuthUrlDto dto = new GetAdTikTokAuthUrlDto(puid, uid, shopId);
        redisTemplateNew.opsForValue().set(cacheKey, dto, 1, TimeUnit.HOURS);
        //拼接授权地址
        String authUrl = String.format(Constants.TIKTOK_AUTH_URL, state);
        return ResultUtil.returnSucc(authUrl);
    }

    public Result<AuthAdvertiserResp> getAdTikTokAuthAdvertiserList(Integer puid, Integer uid, GetAdTikTokAuthAdvertiserListReq req) {
        AuthAdvertiserResp authAdvertiserResp = new AuthAdvertiserResp();
        if (StringUtils.isBlank(req.getAuthCode()) || StringUtils.isBlank(req.getState())) {
            return ResultUtil.returnErr("店铺广告授权失败，请刷新页面重试");
        }
        String cacheKey = this.getCacheKeyByState(puid, req.getState());
        GetAdTikTokAuthUrlDto dto = (GetAdTikTokAuthUrlDto) redisTemplateNew.opsForValue().get(cacheKey);
        if (dto == null || !puid.equals(dto.getPuid()) || !uid.equals(dto.getUid())) {
            return ResultUtil.returnErr("店铺广告授权失败，请刷新页面重试");
        }
        MultiPlatformShopAuth tiktokAdShopAuth = multiPlatformShopService.getTiktokAdShopAuth(puid, dto.getShopId());
        if (tiktokAdShopAuth == null) {
            return ResultUtil.returnErr("店铺已被删除，请刷新页面重试");
        }
        try {
            //获取授权token
            Oauth2AccessTokenBodyResponse oauth2AccessTokenBodyResponse = authApi.getAccessToken(req.getAuthCode());
            dto.setToken(oauth2AccessTokenBodyResponse.getAccessToken());
            redisTemplateNew.opsForValue().set(cacheKey, dto, 1, TimeUnit.HOURS);
            //获取广告账号列表
            Oauth2AdvertiserGetResponse oauth2AdvertiserGetResponse = authApi.oauth2AdvertiserGet(oauth2AccessTokenBodyResponse.getAccessToken());
            Map<String, String> advertiserMap = oauth2AdvertiserGetResponse.getList().stream()
                    .collect(Collectors.toMap(Oauth2AdvertiserGetResponse.Advertiser::getAdvertiserId, Oauth2AdvertiserGetResponse.Advertiser::getAdvertiserName));
            Set<String> advertiserIds = advertiserMap.keySet();
            if (CollectionUtils.isEmpty(advertiserIds)) {
                return ResultUtil.returnErr("当前授权账号下暂无账号，请重试");
            }
            //获取广告账号下的店铺列表
            List<String> relaxAdvertiserIds = new ArrayList<>();
            AllStoreListResponse allStoreListResponse;
            try {
                for (String advertiserId : advertiserIds) {
                    allStoreListResponse = authApi.storeList(oauth2AccessTokenBodyResponse.getAccessToken(), advertiserId, null);
                    if (allStoreListResponse.getStores().stream().anyMatch(e -> e.getStoreId().equals(tiktokAdShopAuth.getPlatformShopId()))) {
                        relaxAdvertiserIds.add(advertiserId);
                    }
                }
            } catch (TikTokSDKException e) {
                if (com.tiktok.advertising.base.Constants.NOT_PERMISSION.equals(e.getCode())) {
                    return ResultUtil.success(new AuthAdvertiserResp("当前未获取到广告账号下的店铺，无法判断是否可授权，请重试", new ArrayList<>()));
                }
                throw e;
            }
            if (CollectionUtils.isEmpty(relaxAdvertiserIds)) {
                return ResultUtil.success(new AuthAdvertiserResp("当前暂无与店铺相关的广告账号，请更换账号重试", new ArrayList<>()));
            }
            List<AuthAdvertiserResp.AuthAdvertiser> advertiserRespList = relaxAdvertiserIds.stream().map(e -> new AuthAdvertiserResp.AuthAdvertiser(e, advertiserMap.get(e))).collect(Collectors.toList());
            authAdvertiserResp.setList(advertiserRespList);
            return ResultUtil.success(authAdvertiserResp);
        } catch (Exception e) {
            log.error("tiktok广告授权获取广告账号信息失败", e);
            return ResultUtil.returnErr("授权异常，请刷新页面重试");
        }
    }

    public Result<String> adTikTokAuth(Integer puid, Integer uid, AdTikTokAuthReq req) {
        if (StringUtils.isBlank(req.getState()) || CollectionUtils.isEmpty(req.getAdvertiserIds())) {
            return ResultUtil.returnErr("店铺广告授权失败，请刷新页面重试");
        }
        String cacheKey = this.getCacheKeyByState(puid, req.getState());
        GetAdTikTokAuthUrlDto dto = (GetAdTikTokAuthUrlDto) redisTemplateNew.opsForValue().get(cacheKey);
        if (dto == null || !puid.equals(dto.getPuid()) || !uid.equals(dto.getUid())) {
            return ResultUtil.returnErr("店铺广告授权失败，请刷新页面重试");
        }
        MultiPlatformShopAuth tiktokAdShopAuth = multiPlatformShopService.getTiktokAdShopAuth(puid, dto.getShopId());
        if (tiktokAdShopAuth == null) {
            return ResultUtil.returnErr("店铺已被删除，请刷新页面重试");
        }
        try {
            AdvertiserInfoResponse advertiserInfoResponse = authApi.advertiserInfo(dto.getToken(), req.getAdvertiserIds());
            if (CollectionUtils.isEmpty(advertiserInfoResponse.getList())) {
                return ResultUtil.returnErr("当前授权账号下暂无账号，请重试");
            }
            Map<String, List<AllStoreListResponse.Store>> storesMap = new HashMap<>();
            for (String advertiserId : req.getAdvertiserIds()) {
                AllStoreListResponse allStoreListResponse = authApi.storeList(dto.getToken(), advertiserId, tiktokAdShopAuth.getPlatformShopId());
                storesMap.put(advertiserId, allStoreListResponse.getStores());
            }
            if (MapUtils.isEmpty(storesMap)) {
                return ResultUtil.returnErr("当前授权账号下暂无店铺，请重试");
            }
            //保存授权信息
            tikTokAuthSaveService.saveTikTokAuthData(puid, tiktokAdShopAuth.getId(), dto.getToken(), advertiserInfoResponse.getList(), storesMap);
            //修改多平台店铺授权状态
            tikTokAuthSaveService.updateMultiPlatformShopAdAuthStatus(puid, tiktokAdShopAuth.getId());
        } catch (Exception e) {
            log.error("tiktok广告授权保存授权信息失败", e);
            return ResultUtil.returnErr("店铺广告授权失败，请刷新页面重试");
        }
        //初始化同步店铺数据
        CompletableFuture.runAsync(() -> {
            tikTokAuthSaveService.initGmvMaxCampaignByShop(puid, tiktokAdShopAuth.getId());
        }, tiktokCampaignSyncExecutor).exceptionally(e -> {
            log.error(String.format("tiktok广告授权初始化同步店铺数据失败:puid:%s, shop_id:%s",  puid, tiktokAdShopAuth.getId()), e);
            return null;
        });
        return ResultUtil.success("tiktok广告授权成功");
    }

    public Result<List<AdvertiserResp>> getAdTikTokAdvertiserList(Integer puid, GetAdTikTokAdvertiserListReq req) {
        // 无限制
        List<TikTokAdvertiserAccount> accounts = tikTokAdvertiserAccountDao.listAllAccountByPuidAndShopIds(puid, Lists.newArrayList(req.getShopId()));
        if (CollectionUtils.isEmpty(accounts)) {
            return ResultUtil.returnSucc(Lists.newArrayList());
        }
        List<AdvertiserResp> advertiserRespList = accounts.stream()
                .map(e -> {
                    AdvertiserResp resp = new AdvertiserResp();
                    resp.setAdvertiserId(e.getAdvertiserId());
                    resp.setAdvertiserName(e.getName());
                    resp.setAdStatus(e.getAuthStatus());
                    return resp;
                }).collect(Collectors.toList());

        return ResultUtil.returnSucc(advertiserRespList);
    }

    public Result<String> revokeAdTikTokAuthReq(Integer puid, RevokeAdTikTokAuthReq req) {
        try {
            tikTokAuthSaveService.revokeAdTikTokAuth(puid, req.getShopId(), req.getAdvertiserId());
            multiPlatformShopAuthDao.updateShopAdAuth(puid, req.getShopId(), TikTokAdAuthEnum.AD_EXIT_FAILURE.getStatus());
        } catch (Exception e) {
            log.error(String.format("tiktok广告授权解除授权失败，puid:%s, shopId:%s, advertiserId:%s", puid, req.getShopId(), req.getAdvertiserId()), e);
            return ResultUtil.returnErr("解除授权失败，请刷新页面重试");
        }
        return ResultUtil.returnSucc("success");
    }

    /**
     * 获取tiktok广告授权缓存key
     * @param puid
     * @param state
     * @return
     */
    private String getCacheKeyByState(Integer puid, String state) {
        return String.format(RedisConstant.TIKTOK_AD_AUTH_UUID, puid, state);
    }
}
