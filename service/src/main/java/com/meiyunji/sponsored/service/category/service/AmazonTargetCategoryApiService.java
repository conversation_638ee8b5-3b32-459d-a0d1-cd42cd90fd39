package com.meiyunji.sponsored.service.category.service;

import com.amazon.advertising.mode.targeting.TargetCategory;
import com.amazon.advertising.targeting.GetTargetingCategoriesResponse;
import com.amazon.advertising.targeting.ProductTargetingClient;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.category.entity.AmazonAdTargetCategories;
import com.meiyunji.sponsored.service.category.dao.AmazonAdTargetCategoriesDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AmazonTargetCategoryApiService {

    @Autowired
    private AmazonAdTargetCategoriesDao amazonAdTargetCategoriesDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Resource
    private IShopAuthService shopAuthService;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    private final Logger logger = LoggerFactory.getLogger(AmazonTargetCategoryApiService.class);

    public Result syncCategories(Integer shopId, String profileId, String marketPlaceId) {
        Result result = ResultUtil.error("网络延迟，请稍后重试");
        ShopAuth shop = shopAuthDao.getScAndVcById(shopId);
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shopId);
        if (amazonAdProfile == null) {
            return ResultUtil.error("没有配置信息");
        }
        ProductTargetingClient client = ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        GetTargetingCategoriesResponse response;
        response = client.getTargetingCategoriesResponse(shopAuthService.getAdToken(shop), profileId, marketPlaceId, true, null);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token,再请求一次
            shopAuthService.refreshCpcAuth(shop);
            response = client.getTargetingCategoriesResponse(shopAuthService.getAdToken(shop), profileId, marketPlaceId, true, null);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }
        //处理返回结果中的错误信息
        if (response.getTargetCategoryList() != null) {
            result = ResultUtil.success(response.getTargetCategoryList());
            List<TargetCategory> targetCategoryList = response.getTargetCategoryList();

            String path = "";
            List<TargetCategory> targetCategories = Lists.newArrayList();
            // TODO 递归遍历所有节点以名称作为目录生成路径并赋值给path和parentId,并且传targetCategories合成所有节点对象
            parseTree(targetCategoryList, path, null, targetCategories);
            List<Long> categoryIds = targetCategories.stream().map(TargetCategory::getId)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            Map<Long, AmazonAdTargetCategories> categoryMap = amazonAdTargetCategoriesDao.listCategoryByCategoryIds(marketPlaceId, categoryIds).stream()
                    .filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdTargetCategories::getCategoryId, Function.identity()));
            AmazonAdTargetCategories category;
            List<AmazonAdTargetCategories> addList = Lists.newArrayList();
            List<AmazonAdTargetCategories> updateList = Lists.newArrayList();
            for (TargetCategory amazonCategory : targetCategories) {
                if (!categoryMap.isEmpty() && categoryMap.get(amazonCategory.getId()) != null) {
                    category = categoryMap.get(amazonCategory.getId());
                    // 只有名称以及路径修改才更新
                    if (!category.getName().equals(amazonCategory.getName()) ||
                            !category.getPath().equals(amazonCategory.getPath())) {
                        updateList.add(category);
                    }
                } else {
                    category = new AmazonAdTargetCategories();
                    category.setMarketplaceId(marketPlaceId);
                    category.setCategoryId(amazonCategory.getId());
                    addList.add(category);
                }
                category.setName(amazonCategory.getName());
                category.setPath(amazonCategory.getPath());
                category.setAsinCountRange(JSONUtil.objectToJson(amazonCategory.getAsinCountRange()));
                category.setParentId(amazonCategory.getParentId());
            }
            try {
                amazonAdTargetCategoriesDao.batchAdd(addList);
                amazonAdTargetCategoriesDao.batchUpdate(updateList);
            } catch (Exception e) {
                logger.error("error:", e);
            }
        }
        if (response.getError() != null) {
            if (response.getError().getDescription() != null) {
                result = ResultUtil.error(response.getError().getDescription());
            } else if (response.getError().getMessage() != null) {
                result = ResultUtil.error(response.getError().getMessage());
            } else if (response.getError().getDetails() != null) {
                result = ResultUtil.error(response.getError().getDetails());
            }

        }
        return result;
    }

    /**
     * 赋值path以及parentId,合成所有节点list
     * @param targetCategoryList 同一层所有节点
     * @param path 以名称作为目录给每个节点定路径
     * @param parentId 父节点categoryId
     * @param targetCategories  合成所有节点list
     */
    private void parseTree(List<TargetCategory> targetCategoryList, String path, Long parentId, List<TargetCategory> targetCategories) {
        String path1 = path;
        for (TargetCategory targetCategory : targetCategoryList) {
            if (parentId != null && parentId > 0) {
                targetCategory.setParentId(parentId);
            }
            path += "/" + targetCategory.getName();
            targetCategory.setPath(path);

            TargetCategory category = new TargetCategory();
            category.setId(targetCategory.getId());
            category.setTargetable(targetCategory.isTargetable());
            category.setName(targetCategory.getName());
            category.setPath(targetCategory.getPath());
            category.setAsinCountRange(targetCategory.getAsinCountRange());
            category.setParentId(targetCategory.getParentId());
            targetCategories.add(category);

            if (!targetCategory.getChildCategories().isEmpty()) {
                this.parseTree(targetCategory.getChildCategories(), path, targetCategory.getId(), targetCategories);
            }
            path = path1;
        }
    }


}
