package com.meiyunji.sponsored.service.adProductRight.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 广告产品权限列表页商品详情req
 * @Author: heqiwen
 * @Date: 2025/05/21 17:22
 */
@Data
public class AsinDetailRequest {
    /**
     * 站点id
     */
    private String marketplaceId;
    /**
     * 店铺id
     */
    private Integer shopId;
    /**
     * VC店铺还是SC店铺
     */
    private String type;
    /**
     * 业务员id
     */
    private Integer uid;

    @NotNull(message = "分页页码为空")
    private Integer pageNo;

    @NotNull(message = "分页页大小为空")
    private Integer pageSize;
}
