package com.meiyunji.sponsored.service.multiPlatform.walmart.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description: walmart广告活动
 */
@Data
public class WalmartAdvertisingCampaignReportPage implements Serializable, IBaseWalmartAdReportGrowthRate {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 商户uid
     */
    private Integer puid;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 活动id
     */
    private Long campaignId;

    @DbColumn(value = "adCost")
    private Double adCost;
    /**
     * 点击量
     */
    @DbColumn(value = "clicks")
    private Integer clicks;

    /**
     * 展示量
     */
    @DbColumn(value = "impressions")
    private Integer impressions;

    /**
     * 销售额
     */
    @DbColumn(value = "adSale")
    private Double adSale;

    /**
     * 订单量
     */
    @DbColumn(value = "adOrderNum")
    private Integer adOrderNum;

    /**
     * 销量
     */
    @DbColumn(value = "adSaleNum")
    private Integer adSaleNum;
    /**
     * 平均点击费用（adSpend/num_ads_clicks）
     */
    private Double adCostPerClick;

    /**
     * 花费销售比（adSpend/sales）
     */
    private Double acos;

    /**
     * 点击率（num_ads_clicks/num_ads_shown）
     */
    @DbColumn(value = "click_rate")
    private Double ctr;

    /**
     * 下单转化率（order_quantity/num_ads_clicks）
     */
    @DbColumn(value = "sales_conversion_rate")
    private Double cvr;

    /**
     * 销售额环比增长
     */
    private Double salesChainGrowth;

    /**
     * 广告花费环比增长
     */
    private Double adSpendChainGrowth;

    /**
     * cpc环比增长
     */
    private Double cpcChainGrowth;

    /**
     * acos环比增长
     */
    private Double acosChainGrowth;

    /**
     * 展示量环比增长
     */
    private Double numAdsShownChainGrowth;

    /**
     * 点击量环比增长
     */
    private Double numAdsClicksChainGrowth;

    /**
     * 订单数环比增长
     */
    private Double orderQuantityChainGrowth;

    /**
     * 销量环比增长
     */
    private Double saleQuantityChainGrowth;

    /**
     * 点击率环比增长
     */
    private Double clickRateChainGrowth;

    /**
     * 转化率环比增长
     */
    private Double salesConversionRateChainGrowth;

    public String getIdStr() {
        return id == null ? null : id.toString();
    }

}
