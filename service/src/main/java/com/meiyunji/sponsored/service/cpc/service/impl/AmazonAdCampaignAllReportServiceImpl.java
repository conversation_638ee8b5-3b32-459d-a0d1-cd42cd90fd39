package com.meiyunji.sponsored.service.cpc.service.impl;


import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.DoubleUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSbPlacementReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSbPlacementReport;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdCampaignAllReportService;
import com.meiyunji.sponsored.service.cpc.service2.IAdCampaignPlacementReportRoutingService;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.enums.AdReportExportAdFormatEnum;
import com.meiyunji.sponsored.service.export.ReportFillBaseDataHelper;
import com.meiyunji.sponsored.service.export.dto.DownloadCenterCampaignBaseDataBO;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
public class AmazonAdCampaignAllReportServiceImpl extends ReportService<AmazonAdCampaignAllReport> implements IAmazonAdCampaignAllReportService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdSbPlacementReportDao amazonAdSbPlacementReportDao;
    @Autowired
    private ReportFillBaseDataHelper reportFillBaseDataHelper;
    @Autowired
    private IAdCampaignPlacementReportRoutingService adCampaignPlacementReportRoutingService;

    @Override
    public SumReportVo getSumReport(int puid, Integer shopId, String marketplaceId, Date start, Date end, Integer lastMonth, String campaignId) {
        SumReportVo vo = new SumReportVo();
        //获取时间段的报告数据汇总
        String startStr = DateUtil.dateToStrWithFormat(start,"yyyyMMdd");
        String endStr = DateUtil.dateToStrWithFormat(end,"yyyyMMdd");
        AmazonAdCampaignAllReport sumReport;
        sumReport = getSumReportByCampaignId(puid,shopId,marketplaceId,startStr,endStr,campaignId);

        vo.setReportVo(getVo(sumReport,null));
        //获取上个时间段的数据
        Map<String,String> map = getLastTime(start,end,lastMonth);
        AmazonAdCampaignAllReport lastSumReport;
        lastSumReport = getSumReportByCampaignId(puid,shopId,marketplaceId,map.get("startStr"),map.get("endStr"),campaignId);

        vo.setLastReportVo(getVo(lastSumReport,null));
        vo.calculationGrow(false);
        return vo;
    }

    @Override
    public List<AmazonAdCampaignAllReport> listSumReports(int puid, Integer shopId, String marketplaceId, String startStr, String endStr, List<String> campaignIds) {
        return amazonAdCampaignAllReportDao.listSumReports(puid, shopId, marketplaceId, startStr, endStr, campaignIds);
    }

    @Override
    public List<AdHomePerformancedto> listLatestReports(Integer puid, Integer shopId, List<String> campaignIds, boolean isAggregation) {
        return amazonAdCampaignAllReportDao.listLatestReports(puid, shopId, campaignIds, isAggregation);
    }

    @Override
    public AdMetricDto getSumAdMetric(Integer puid, Integer shopId, String startStr, String endStr, CampaignPageParam param) {
            return amazonAdCampaignAllReportDao.getSumAdMetric(puid, shopId,
                    startStr, endStr, param);
    }


    @Override
    public List<AmazonAdCampaignAllReport> getAllCampaignIdsSumReport(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, List<String> campaignIds) {
            return amazonAdCampaignAllReportDao.getSumReportByAllCampaignIds(puid, shopId,
                    marketplaceId, startStr, endStr, campaignIds);
    }


    @Override
    public Page pageList(int puid, SearchVo search, Page page) {

        if (StringUtils.isNotBlank(search.getSpCampaignType()) && search.getShopId() != null) {
            List<String> campaignIds = amazonAdCampaignAllDao.getSpCampaignIdsByTargetType(puid, search.getShopId(), search.getSpCampaignType());
            search.setCampaignIds(campaignIds);
        }

        page = getPageList(puid,search,page);

        List<AmazonAdCampaignAllReport> poList = page.getRows();
        if(poList!= null && poList.size()>0){
            if ("sp".equals(search.getAdType())){
                getSpConvertData(poList,puid,page);
            } else if ("sb".equals(search.getAdType())) {
                getSbConvertData(poList,puid,page);
            } else if ("sbv".equals(search.getAdType())) {
                getSbvConvertData(poList,puid,page);
            } else {
                getSdConvertData(poList,puid,page);
            }
        }
        return page;
    }

    @Override
    public Page pageSpaceList(int puid, SearchVo search, Page page) {
        if (StringUtils.isNotBlank(search.getSpCampaignType()) && search.getShopId() != null) {
            List<String> campaignIds = amazonAdCampaignAllDao.getSpCampaignIdsByTargetType(puid, search.getShopId(), search.getSpCampaignType());
            search.setCampaignIds(campaignIds);
        }

        page = getPageSpaceList(puid,search,page);

        List<AmazonAdCampaignAllReport> poList = page.getRows();

        if(poList!= null && poList.size() > 0) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());

            //分组查询基础数据
            Map<Integer, Set<String>> collect = poList.parallelStream().collect(Collectors.groupingBy(AmazonAdCampaignAllReport::getShopId, Collectors.mapping(AmazonAdCampaignAllReport::getCampaignId, Collectors.toSet())));
            Map<Integer, Map<String, DownloadCenterCampaignBaseDataBO>> campaignBaseDataMap =  reportFillBaseDataHelper.queryBaseData4DownloadByShopGroupIdList(puid, collect, (a, b, c) -> amazonAdCampaignAllDao.queryBaseData4DownloadByCampaignIdList(a, b, c));

            poList.forEach(e -> {
                ReportVo vo = getVo(e,null);

                reportFillBaseDataHelper.fillCampaignBaseData4ReportVo(vo, e.getShopId(), e.getCampaignId(), campaignBaseDataMap);

                vo.setCampaignType(e.getCampaignType());
                vo.setShopId(e.getShopId());
                vo.setCampaignId(e.getCampaignId());
                vo.setCampaignName(e.getCampaignName());
                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }

    @Override
    public Page pageSbAndSbvSpaceList(int puid, SearchVo search, Page page) {
        page = getPageSbSpaceList(puid,search,page);
        List<AmazonAdSbPlacementReport> poList = page.getRows();
        if(poList!= null && poList.size()>0) {
            getSbAndSbvPlacementConvertData(puid, poList, page, search.getType());
        }
        return page;
    }

    private void getSpConvertData (List<AmazonAdCampaignAllReport> poList, Integer puid, Page page) {
        List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());

        //分组查询基础数据
        Map<Integer, Set<String>> collect = poList.parallelStream().collect(Collectors.groupingBy(AmazonAdCampaignAllReport::getShopId, Collectors.mapping(AmazonAdCampaignAllReport::getCampaignId, Collectors.toSet())));
        Map<Integer, Map<String, DownloadCenterCampaignBaseDataBO>> campaignBaseDataMap =  reportFillBaseDataHelper.queryBaseData4DownloadByShopGroupIdList(puid, collect, (a, b, c) -> amazonAdCampaignAllDao.queryBaseData4DownloadByCampaignIdList(a, b, c));

        poList.forEach(e -> {
            ReportVo vo = getVo(e,null);

            reportFillBaseDataHelper.fillCampaignBaseData4ReportVo(vo, e.getShopId(), e.getCampaignId(), campaignBaseDataMap);

            vo.setCountDate(e.getCountDate());
            vo.setShopId(e.getShopId());
            vo.setCampaignId(e.getCampaignId());
            vo.setCampaignName(e.getCampaignName());
            list.add(vo);
        });
        page.setRows(list);
    }

    private void getSbConvertData (List<AmazonAdCampaignAllReport> poList,Integer puid,Page page) {
        List<ReportDataVo> list = Lists.newArrayListWithExpectedSize(poList.size());
        ReportDataVo vo;

        //分组查询基础数据
        Map<Integer, Set<String>> collect = poList.parallelStream().collect(Collectors.groupingBy(AmazonAdCampaignAllReport::getShopId, Collectors.mapping(AmazonAdCampaignAllReport::getCampaignId, Collectors.toSet())));
        Map<Integer, Map<String, DownloadCenterCampaignBaseDataBO>> campaignBaseDataMap =  reportFillBaseDataHelper.queryBaseData4DownloadByShopGroupIdList(puid, collect, (a, b, c) -> amazonAdCampaignAllDao.queryBaseData4DownloadByCampaignIdList(a, b, c));

        for (AmazonAdCampaignAllReport report : poList) {
            vo = new ReportDataVo();

            reportFillBaseDataHelper.fillCampaignBaseData4ReportDataVo(vo, report.getShopId(), report.getCampaignId(), campaignBaseDataMap);

            vo.setAdFormat(report.getCampaignType());
            vo.setShopId(report.getShopId());
            vo.setCountDate(report.getCountDate());
            vo.setCampaignName(report.getCampaignName());
            vo.setCampaignId(report.getCampaignId());
            vo.setCost(report.getCost());
            vo.setSales(report.getSales14d());
            vo.setImpressions(report.getImpressions());
            vo.setClicks(report.getClicks());
            vo.setOrderNum(report.getConversions14d());
            vo.setAcos(report.getAcos());
            vo.setRoas(report.getRoas());
            vo.setCpc(report.getCpc());
            vo.setClickRate(report.getClickRate());
            vo.setSalesConversionRate(report.getSalesConversionRate());
            //新版报告下载中心
            vo.setAttributedConversions14d(Optional.ofNullable(report.getOrderNum()).orElse(0));
            vo.setAttributedConversions14dSameSKU(Optional.ofNullable(report.getAdOrderNum()).orElse(0));
            if (report.getOrderNum() != null) {
                if (report.getAdOrderNum() != null) {
                    vo.setAttributedConversions14dOtherSameSKU(report.getOrderNum() - report.getAdOrderNum());
                } else {
                    vo.setAttributedConversions14dOtherSameSKU(report.getOrderNum());
                }
            }else {
                vo.setAttributedConversions14dOtherSameSKU(0);
            }
            vo.setAttributedSales14d(Optional.ofNullable(report.getTotalSales()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
            vo.setAttributedSales14dSameSKU(Optional.ofNullable(report.getAdSales()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
            if (report.getTotalSales() != null) {
                if (report.getAdSales() != null) {
                    vo.setAttributedSales14dOtherSameSKU(report.getTotalSales().subtract(report.getAdSales()));
                } else {
                    vo.setAttributedSales14dOtherSameSKU(report.getTotalSales());
                }
            }else {
                vo.setAttributedSales14dOtherSameSKU(BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP));
            }
            vo.setAttributedUnitsOrdered14d(Optional.ofNullable(report.getSaleNum()).orElse(0));
            vo.setAttributedOrderRateNewToBrand14d(Optional.ofNullable(report.getOrderRateNewToBrand14d()).orElse(0.00));
            vo.setAttributedOrdersNewToBrand14d(Optional.ofNullable(report.getOrdersNewToBrand14d()).orElse(0));
            vo.setAttributedOrdersNewToBrandPercentage14d(Optional.ofNullable(report.getOrdersNewToBrandPercentage14d()).orElse(0.00));
            vo.setAttributedSalesNewToBrand14d(Optional.ofNullable(report.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP)));
            vo.setAttributedSalesNewToBrandPercentage14d(Optional.ofNullable(report.getSalesNewToBrandPercentage14d()).orElse(0.00));
            vo.setAttributedUnitsOrderedNewToBrand14d(Optional.ofNullable(report.getUnitsOrderedNewToBrand14d()).orElse(0));
            vo.setAttributedUnitsOrderedNewToBrandPercentage14d(Optional.ofNullable(report.getUnitsOrderedNewToBrandPercentage14d()).orElse(0.00));
            if (AdReportExportAdFormatEnum.video.getAdFormat().equals(report.getCampaignType())) {
                vo.setVctr(Optional.ofNullable(report.getVctr()).orElse(0.00));
                vo.setVideo5SecondViewRate(Optional.ofNullable(report.getVideo5SecondViewRate()).orElse(0.00));
                vo.setVideo5SecondViews(Optional.ofNullable(report.getVideo5SecondViews()).orElse(0));
                vo.setVideoFirstQuartileViews(Optional.ofNullable(report.getVideoFirstQuartileViews()).orElse(0));
                vo.setVideoMidpointViews(Optional.ofNullable(report.getVideoMidpointViews()).orElse(0));
                vo.setVideoThirdQuartileViews(Optional.ofNullable(report.getVideoThirdQuartileViews()).orElse(0));
                vo.setVideoUnmutes(Optional.ofNullable(report.getVideoUnmutes()).orElse(0));
                vo.setViewableImpressions(Optional.ofNullable(report.getViewableImpressions()).orElse(0));
                vo.setVideoCompleteViews(Optional.ofNullable(report.getVideoCompleteViews()).orElse(0));
                vo.setVtr(Optional.ofNullable(report.getVtr()).orElse(0.00));
            }
            list.add(vo);
        }
        page.setRows(list);
    }

    private void getSbvConvertData (List<AmazonAdCampaignAllReport> poList,Integer puid,Page page) {
        List<ReportDataVo> list = Lists.newArrayListWithExpectedSize(poList.size());
        ReportDataVo vo;

        //分组查询基础数据
        Map<Integer, Set<String>> collect = poList.parallelStream().collect(Collectors.groupingBy(AmazonAdCampaignAllReport::getShopId, Collectors.mapping(AmazonAdCampaignAllReport::getCampaignId, Collectors.toSet())));
        Map<Integer, Map<String, DownloadCenterCampaignBaseDataBO>> campaignBaseDataMap =  reportFillBaseDataHelper.queryBaseData4DownloadByShopGroupIdList(puid, collect, (a, b, c) -> amazonAdCampaignAllDao.queryBaseData4DownloadByCampaignIdList(a, b, c));


        for (AmazonAdCampaignAllReport report : poList) {
            vo = new ReportDataVo();

            reportFillBaseDataHelper.fillCampaignBaseData4ReportDataVo(vo, report.getShopId(), report.getCampaignId(), campaignBaseDataMap);

            vo.setShopId(report.getShopId());
            vo.setCountDate(report.getCountDate());
            vo.setCampaignName(report.getCampaignName());
            vo.setCampaignId(report.getCampaignId());
            vo.setCost(report.getCost());
            vo.setSales(report.getSales14d());
            vo.setImpressions(report.getImpressions());
            vo.setClicks(report.getClicks());
            vo.setOrderNum(report.getConversions14d());
            vo.setAcos(report.getAcos());
            vo.setRoas(report.getRoas());
            vo.setCpc(report.getCpc());
            vo.setClickRate(report.getClickRate());
            vo.setSalesConversionRate(report.getSalesConversionRate());
            //新版报告下载中心
            vo.setAttributedConversions14d(Optional.ofNullable(report.getOrderNum()).orElse(0));
            vo.setAttributedConversions14dSameSKU(Optional.ofNullable(report.getAdOrderNum()).orElse(0));
            if (report.getOrderNum() != null) {
                if (report.getAdOrderNum() != null) {
                    vo.setAttributedConversions14dOtherSameSKU(report.getOrderNum() - report.getAdOrderNum());
                } else {
                    vo.setAttributedConversions14dOtherSameSKU(report.getOrderNum());
                }
            }else {
                vo.setAttributedConversions14dOtherSameSKU(0);
            }
            vo.setAttributedSales14d(Optional.ofNullable(report.getTotalSales()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
            vo.setAttributedSales14dSameSKU(Optional.ofNullable(report.getAdSales()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
            if (report.getTotalSales() != null) {
                if (report.getAdSales() != null) {
                    vo.setAttributedSales14dOtherSameSKU(report.getTotalSales().subtract(report.getAdSales()));
                } else {
                    vo.setAttributedSales14dOtherSameSKU(report.getTotalSales());
                }
            }else {
                vo.setAttributedSales14dOtherSameSKU(BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP));
            }
            vo.setAttributedUnitsOrdered14d(Optional.ofNullable(report.getSaleNum()).orElse(0));
            vo.setAttributedOrderRateNewToBrand14d(Optional.ofNullable(report.getOrderRateNewToBrand14d()).orElse(0.00));
            vo.setAttributedOrdersNewToBrand14d(Optional.ofNullable(report.getOrdersNewToBrand14d()).orElse(0));
            vo.setAttributedOrdersNewToBrandPercentage14d(Optional.ofNullable(report.getOrdersNewToBrandPercentage14d()).orElse(0.00));
            vo.setAttributedSalesNewToBrand14d(Optional.ofNullable(report.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP)));
            vo.setAttributedSalesNewToBrandPercentage14d(Optional.ofNullable(report.getSalesNewToBrandPercentage14d()).orElse(0.00));
            vo.setAttributedUnitsOrderedNewToBrand14d(Optional.ofNullable(report.getUnitsOrderedNewToBrand14d()).orElse(0));
            vo.setAttributedUnitsOrderedNewToBrandPercentage14d(Optional.ofNullable(report.getUnitsOrderedNewToBrandPercentage14d()).orElse(0.00));
            vo.setVctr(Optional.ofNullable(report.getVctr()).orElse(0.00));
            vo.setVideo5SecondViewRate(Optional.ofNullable(report.getVideo5SecondViewRate()).orElse(0.00));
            vo.setVideo5SecondViews(Optional.ofNullable(report.getVideo5SecondViews()).orElse(0));
            vo.setVideoFirstQuartileViews(Optional.ofNullable(report.getVideoFirstQuartileViews()).orElse(0));
            vo.setVideoMidpointViews(Optional.ofNullable(report.getVideoMidpointViews()).orElse(0));
            vo.setVideoThirdQuartileViews(Optional.ofNullable(report.getVideoThirdQuartileViews()).orElse(0));
            vo.setVideoUnmutes(Optional.ofNullable(report.getVideoUnmutes()).orElse(0));
            vo.setViewableImpressions(Optional.ofNullable(report.getViewableImpressions()).orElse(0));
            vo.setVideoCompleteViews(Optional.ofNullable(report.getVideoCompleteViews()).orElse(0));
            vo.setVtr(Optional.ofNullable(report.getVtr()).orElse(0.00));
            list.add(vo);
        }
        page.setRows(list);
    }


    private void getSbAndSbvPlacementConvertData (int puid, List<AmazonAdSbPlacementReport> poList, Page page, String type) {
        List<ReportDataVo> list = Lists.newArrayListWithExpectedSize(poList.size());

        //分组查询基础数据
        Map<Integer, Set<String>> collect = poList.parallelStream().collect(Collectors.groupingBy(AmazonAdSbPlacementReport::getShopId, Collectors.mapping(AmazonAdSbPlacementReport::getCampaignId, Collectors.toSet())));
        Map<Integer, Map<String, DownloadCenterCampaignBaseDataBO>> campaignBaseDataMap =  reportFillBaseDataHelper.queryBaseData4DownloadByShopGroupIdList(puid, collect, (a, b, c) -> amazonAdCampaignAllDao.queryBaseData4DownloadByCampaignIdList(a, b, c));

        ReportDataVo vo;
        for (AmazonAdSbPlacementReport report : poList) {
            vo = new ReportDataVo();

            reportFillBaseDataHelper.fillCampaignBaseData4ReportDataVo(vo, report.getShopId(), report.getCampaignId(), campaignBaseDataMap);

            vo.setAdFormat(report.getAdFormat());
            vo.setShopId(report.getShopId());
            vo.setCountDate(report.getCountDate());
            vo.setPlacement(report.getPlacement());
            vo.setCampaignId(report.getCampaignId());
            vo.setCampaignName(report.getCampaignName());
            vo.setCost(Optional.ofNullable(report.getCost()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
            vo.setImpressions(Optional.ofNullable(report.getImpressions()).orElse(0));
            vo.setClicks(Optional.ofNullable(report.getClicks()).orElse(0));
            vo.setCpc(report.getCpc());
            vo.setClickRate(report.getReportBase().getClickRate());
            vo.setSalesConversionRate(report.getReportBase().getSalesConversionRate());
            vo.setAcos(report.getReportBase().getAcos());
            vo.setRoas(report.getReportBase().getRoas());

            vo.setOrderNum(Optional.ofNullable(report.getConversions14d()).orElse(0));
            vo.setAttributedConversions14d(Optional.ofNullable(report.getConversions14d()).orElse(0));
            vo.setAttributedConversions14dSameSKU(Optional.ofNullable(report.getConversions14dSameSKU()).orElse(0));
            if (report.getConversions14d() != null) {
                if (report.getConversions14dSameSKU() != null) {
                    vo.setAttributedConversions14dOtherSameSKU(report.getConversions14d() - report.getConversions14dSameSKU());
                } else {
                    vo.setAttributedConversions14dOtherSameSKU(report.getConversions14d());
                }
            }else {
                vo.setAttributedConversions14dOtherSameSKU(0);
            }

            vo.setSales(Optional.ofNullable(report.getSales14d()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
            vo.setAttributedSales14d(Optional.ofNullable(report.getSales14d()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
            vo.setAttributedSales14dSameSKU(Optional.ofNullable(report.getSales14dSameSKU()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
            if (report.getSales14d() != null) {
                if (report.getSales14dSameSKU() != null) {
                    vo.setAttributedSales14dOtherSameSKU(report.getSales14d().subtract(report.getSales14dSameSKU()));
                } else {
                    vo.setAttributedSales14dOtherSameSKU(report.getSales14d());
                }
            }else {
                vo.setAttributedSales14dOtherSameSKU(BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP));
            }

            vo.setAttributedOrderRateNewToBrand14d(Optional.ofNullable(report.getReportBase().getOrderRateNewToBrand14dSb()).orElse(0.00));
            vo.setAttributedOrdersNewToBrand14d(Optional.ofNullable(report.getOrdersNewToBrand14d()).orElse(0));
            vo.setAttributedOrdersNewToBrandPercentage14d(Optional.ofNullable(report.getReportBase().getOrdersNewToBrandPercentage14dSb()).orElse(0.00));
            vo.setAttributedSalesNewToBrand14d(Optional.ofNullable(report.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP)));
            vo.setAttributedSalesNewToBrandPercentage14d(Optional.ofNullable(report.getReportBase().getSalesNewToBrandPercentage14dSb()).orElse(0.00));
            vo.setAttributedUnitsOrderedNewToBrand14d(Optional.ofNullable(report.getUnitsOrderedNewToBrand14d()).orElse(0));
            vo.setAttributedUnitsOrderedNewToBrandPercentage14d(Optional.ofNullable(report.getReportBase().getUnitsOrderedNewToBrandPercentage14dSb()).orElse(0.00));
            vo.setAttributedBrandedSearches14d(Optional.ofNullable(report.getBrandedSearches14d()).orElse(0));

            if (AdReportExportAdFormatEnum.video.getAdFormat().equals(report.getAdFormat())) {
                vo.setVctr(Optional.ofNullable(report.getVctr()).orElse(0.00));
                vo.setVideo5SecondViewRate(Optional.ofNullable(report.getVideo5SecondViewRate()).orElse(0.00));
                vo.setVideo5SecondViews(Optional.ofNullable(report.getVideo5SecondViews()).orElse(0));
                vo.setVideoFirstQuartileViews(Optional.ofNullable(report.getVideoFirstQuartileViews()).orElse(0));
                vo.setVideoMidpointViews(Optional.ofNullable(report.getVideoMidpointViews()).orElse(0));
                vo.setVideoThirdQuartileViews(Optional.ofNullable(report.getVideoThirdQuartileViews()).orElse(0));
                vo.setVideoUnmutes(Optional.ofNullable(report.getVideoUnmutes()).orElse(0));
                vo.setViewableImpressions(Optional.ofNullable(report.getViewableImpressions()).orElse(0));
                vo.setVideoCompleteViews(Optional.ofNullable(report.getVideoCompleteViews()).orElse(0));
                vo.setVtr(Optional.ofNullable(report.getVtr()).orElse(0.00));
            }

            list.add(vo);
        }
        page.setRows(list);
    }



    private void getSdConvertData (List<AmazonAdCampaignAllReport> poList,Integer puid,Page page) {
        List<ReportDataVo> list = Lists.newArrayListWithExpectedSize(poList.size());
        ReportDataVo vo;

        //分组查询基础数据
        Map<Integer, Set<String>> collect = poList.parallelStream().collect(Collectors.groupingBy(AmazonAdCampaignAllReport::getShopId, Collectors.mapping(AmazonAdCampaignAllReport::getCampaignId, Collectors.toSet())));
        Map<Integer, Map<String, DownloadCenterCampaignBaseDataBO>> campaignBaseDataMap =  reportFillBaseDataHelper.queryBaseData4DownloadByShopGroupIdList(puid, collect, (a, b, c) -> amazonAdCampaignAllDao.queryBaseData4DownloadByCampaignIdList(a, b, c));


        for (AmazonAdCampaignAllReport report : poList) {
            vo = new ReportDataVo();

            reportFillBaseDataHelper.fillCampaignBaseData4ReportDataVo(vo, report.getShopId(), report.getCampaignId(), campaignBaseDataMap);

            vo.setShopId(report.getShopId());
            vo.setCountDate(report.getCountDate());
            vo.setCampaignName(report.getCampaignName());
            vo.setCampaignId(report.getCampaignId());
            vo.setCost(report.getCost());
            vo.setSales(report.getSales14d());
            vo.setImpressions(report.getImpressions());
            vo.setClicks(report.getClicks());
            //attributedConversions14d  字段作为订单量更为准确
            vo.setOrderNum(report.getConversions14d());
            vo.setAcos(report.getAcos());
            vo.setClickRate(report.getClickRate());
            vo.setSalesConversionRate(report.getSalesConversionRate());
            vo.setCostType(report.getCostType());
            vo.setCpc(report.getCpc());
            vo.setVcpm(report.getVcpm());
            vo.setRoas(report.getRoas());
            vo.setViewImpressions(Optional.ofNullable(report.getViewImpressions()).orElse(0));
            vo.setAttributedConversions14d(Optional.ofNullable(report.getOrderNum()).orElse(0));
            vo.setAttributedConversions14dSameSKU(Optional.ofNullable(report.getAdOrderNum()).orElse(0));
            vo.setAttributedDetailPageView14d(Optional.ofNullable(report.getDetailPageView14d()).orElse(0));
            if (report.getOrderNum() != null) {
                if (report.getAdOrderNum() != null) {
                    vo.setAttributedConversions14dOtherSameSKU(report.getOrderNum() - report.getAdOrderNum());
                } else {
                    vo.setAttributedConversions14dOtherSameSKU(report.getOrderNum());
                }
            }else {
                vo.setAttributedConversions14dOtherSameSKU(0);
            }
            vo.setAttributedSales14d(Optional.ofNullable(report.getTotalSales()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
            vo.setAttributedSales14dSameSKU(Optional.ofNullable(report.getAdSales()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
            if (report.getTotalSales() != null) {
                if (report.getAdSales() != null) {
                    vo.setAttributedSales14dOtherSameSKU(report.getTotalSales().subtract(report.getAdSales()));
                } else {
                    vo.setAttributedSales14dOtherSameSKU(report.getTotalSales());
                }
            }else {
                vo.setAttributedSales14dOtherSameSKU(BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP));
            }
            vo.setAttributedUnitsOrdered14d(Optional.ofNullable(report.getSaleNum()).orElse(0));
            //品牌计算
            vo.setAttributedOrdersNewToBrand14d(Optional.ofNullable(report.getOrdersNewToBrand14d()).orElse(0));
            if (report.getOrdersNewToBrand14d() != null ) {
                if (report.getOrderNum() != null) {
                    vo.setAttributedOrderRateNewToBrand14d(calculationRateBigDecimal
                            (BigDecimal.valueOf(report.getOrdersNewToBrand14d()),BigDecimal.valueOf(report.getOrderNum()),true).doubleValue());
                } else {
                    vo.setAttributedOrderRateNewToBrand14d(0.00);
                }
            } else {
                vo.setAttributedOrderRateNewToBrand14d(0.00);
            }
            vo.setAttributedSalesNewToBrand14d(Optional.ofNullable(report.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
            if (report.getSalesNewToBrand14d() != null) {
                if (report.getTotalSales() != null) {
                    vo.setAttributedSalesNewToBrandPercentage14d(calculationRateBigDecimal(report.getSalesNewToBrand14d(),
                            report.getTotalSales(), true).doubleValue());
                } else {
                    vo.setAttributedSalesNewToBrandPercentage14d(0.00);
                }
            } else {
                vo.setAttributedSalesNewToBrandPercentage14d(0.00);
            }
            vo.setAttributedUnitsOrderedNewToBrand14d(Optional.ofNullable(report.getUnitsOrderedNewToBrand14d()).orElse(0));
            if (report.getUnitsOrderedNewToBrand14d() != null) {
                if (report.getSaleNum() != null) {
                    vo.setAttributedUnitsOrderedNewToBrandPercentage14d(calculationRateBigDecimal(BigDecimal.valueOf(report.getUnitsOrderedNewToBrand14d()),
                            BigDecimal.valueOf(report.getSaleNum()), true).doubleValue());
                } else {
                    vo.setAttributedUnitsOrderedNewToBrandPercentage14d(0.00);
                }
            } else {
                vo.setAttributedUnitsOrderedNewToBrandPercentage14d(0.00);
            }
            list.add(vo);
        }
        page.setRows(list);
    }

    /**
     * @param value1 被除数
     * @param value2 除数
     * @return
     */
    private BigDecimal calculationRateBigDecimal(BigDecimal value1, BigDecimal value2,Boolean per) {
        BigDecimal rate = value2.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(value1,value2);
        if(per){
            rate = MathUtil.multiply(rate,BigDecimal.valueOf(100));
        }
        return rate.setScale(2,BigDecimal.ROUND_HALF_UP);
    }
    private Double calculationRateDouble(Double value1, Double value2) {
        return value2 == 0 ? 0 : DoubleUtil.divide(value1*100,value2,2);
    }

    private Page getPageSpaceList(int puid, SearchVo search, Page page) {
        return adCampaignPlacementReportRoutingService.getPageSpaceList(puid,search,page,search.getCampaignSite());
    }

    private Page getPageSbSpaceList(int puid, SearchVo search, Page page) {
        return amazonAdSbPlacementReportDao.getPageSpaceList(puid,search,page);
    }

    private Page getPageList(int puid, SearchVo search, Page page) {
        return amazonAdCampaignAllReportDao.getPageList(puid,search,page);
    }

    private AmazonAdCampaignAllReport getSumReportByCampaignId(int puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId) {
        return amazonAdCampaignAllReportDao.getSumReportByCampaignId(puid,shopId,marketplaceId,startStr,endStr,campaignId);
    }

}
