package com.meiyunji.sponsored.service.cpc.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.LogicType;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdGroupStrategyTypeEnum;
import com.meiyunji.sponsored.service.export.dto.DownloadCenterBaseDataBO;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdProductDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdProduct;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.AdProductPageParam;
import com.meiyunji.sponsored.service.cpc.vo.GroupPageParam;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.strategy.enums.ProductSearchFieldEnum;
import com.meiyunji.sponsored.service.strategy.vo.AdProductStrategyParam;
import com.meiyunji.sponsored.service.strategy.vo.AdProductStrategyVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR> on 2021/7/6
 */
@Repository
public class AmazonSdAdProductDaoImpl extends BaseShardingDaoImpl<AmazonSdAdProduct> implements IAmazonSdAdProductDao {

    @Autowired
    private IOdsProductDao odsProductDao;

    @Override
    public void batchAdd(int puid, List<AmazonSdAdProduct> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;
        for (AmazonSdAdProduct po : list) {
            if (po.getPuid() == null || po.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(po.getPuid());
            arg.add(po.getShopId());
            arg.add(po.getMarketplaceId());
            arg.add(po.getProfileId());
            arg.add(po.getCampaignId());
            arg.add(po.getAdGroupId());
            arg.add(po.getAdId());
            arg.add(po.getState());
            arg.add(po.getSku());
            arg.add(po.getAsin());
            arg.add(Optional.ofNullable(po.getLandingPageType()).orElse(""));
            arg.add(Optional.ofNullable(po.getLandingPageURL()).orElse(""));
            arg.add(Optional.ofNullable(po.getAdName()).orElse(""));
            arg.add(po.getServingStatus());
            arg.add(po.getCreationDate());
            arg.add(po.getLastUpdatedDate());
            arg.add(po.getCreateInAmzup());
            arg.add(po.getCreateId());
            arg.add(po.getUpdateId());
            argList.add(arg.toArray());
        }

        String sql = "insert into t_amazon_ad_product_sd (`puid`,`shop_id`,`marketplace_id`,`profile_id`,`campaign_id`," +
                "`ad_group_id`,`ad_id`,`state`,`sku`,`asin`, `landing_page_type`, `landing_page_url`, `ad_name`, `serving_status`,`creation_date`,`last_updated_date`,`create_in_amzup`," +
                "`create_id`,`update_id`,`create_time`,`update_time`) values (?,?, ?,?, ?,?, ?,?," +
                "?,?, ?,?, ?,?,?,?,?, ?,?, now(3), now(3))";

        getJdbcTemplate(puid).batchUpdate(sql, argList);
    }

    @Override
    public void batchUpdate(int puid, List<AmazonSdAdProduct> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;
        for (AmazonSdAdProduct po : list) {
            if (po.getPuid() == null || po.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(po.getSku());
            arg.add(po.getAsin());
            arg.add(po.getState());
            arg.add(po.getServingStatus());
            arg.add(po.getCreationDate());
            arg.add(po.getLastUpdatedDate());
            arg.add(po.getUpdateId());
            arg.add(Optional.ofNullable(po.getLandingPageType()).orElse(""));
            arg.add(Optional.ofNullable(po.getLandingPageURL()).orElse(""));
            arg.add(Optional.ofNullable(po.getAdName()).orElse(""));
            arg.add(po.getPuid());
            arg.add(po.getShopId());
            arg.add(po.getAdId());
            argList.add(arg.toArray());
        }

        String sql = "update t_amazon_ad_product_sd set `sku`=?,`asin`=?,`state`=?,`serving_status`=?,`creation_date`=?," +
                "`last_updated_date`=?,`update_id`=?,  `landing_page_type`=?, `landing_page_url`=?, `ad_name`=? " +
                " where puid=? and shop_id=? and ad_id=?";

        getJdbcTemplate(puid).batchUpdate(sql, argList);
    }

    @Override
    public List<AmazonSdAdProduct> listByAdId(int puid, int shopId, List<String> adIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .in("ad_id", adIds.toArray())
                .build();

        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public AmazonSdAdProduct getByAdId(int puid, int shopId, String adId) {
        if (StringUtils.isBlank(adId)) {
            return null;
        }
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .equalToWithoutCheck("ad_id", adId)
                .build();

        return getByCondition(puid, conditionBuilder);
    }

    @Override
    public Map<String, Integer> statCountByAdGroup(int puid, Integer shopId, List<String> status, List<String> adGroupIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .in("ad_group_id", adGroupIds.toArray())
                .in("state", status.toArray())
                .groupBy("ad_group_id").build();

        String sql = "select ad_group_id adGroupId, count(*) c from t_amazon_ad_product_sd where " + conditionBuilder.getSql();
        return getJdbcTemplate(puid).queryForList(sql, conditionBuilder.getValues())
                .stream().collect(Collectors.toMap(e -> e.get("adGroupId").toString(), e -> Integer.parseInt(e.get("c").toString())));
    }

    @Override
    public Page<AmazonSdAdProduct> pageList(Integer puid, AdProductPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("ad_group_id", param.getGroupId());

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            builder.and()
                    .leftBracket()
                    .like(LogicType.EPT, "asin", param.getSearchValue())
                    .or()
                    .like(LogicType.EPT, "sku", param.getSearchValue())
                    .rightBracket();
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            builder.equalTo("state", param.getStatus());
        }

        String orderBySql = " order by update_time desc";

        return page(puid, param.getPageNo(), param.getPageSize(), orderBySql, builder.build());
    }

    @Override
    public List<AmazonSdAdProduct> listByCondition(Integer puid, AdProductPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("ad_group_id", param.getGroupId());

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            builder.and()
                    .leftBracket()
                    .like(LogicType.EPT, "asin", param.getSearchValue())
                    .or()
                    .like(LogicType.EPT, "sku", param.getSearchValue())
                    .rightBracket();
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            builder.equalTo("state", param.getStatus());
        }

        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonSdAdProduct> listBySku(int puid, int shopId, String groupId, Set<String> skus) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", groupId)
                .in("sku", skus.toArray())
                .in("state", CpcStatusEnum.validList().toArray());

        return listByCondition(puid, builder.build());
    }

    @Override
    public List<String> listValidAsin(int puid, Integer shopId, String groupId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", groupId)
                .in("state", CpcStatusEnum.validList().toArray());

        return listDistinctFieldByCondition(puid, "asin", builder.build(), String.class);
    }

    @Override
    public List<String> listValidAsinByAsin(int puid, Integer shopId, List<String> asinList) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("asin", asinList.toArray())
                .in("state", CpcStatusEnum.validList().toArray());

        return listDistinctFieldByCondition(puid, "asin", builder.build(), String.class);    }

    @Override
    public List<String> listSkus(Integer puid, Integer shopId, String groupId, List<String> stateList) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .equalTo("ad_group_id", groupId)
                .in("`state`", stateList.toArray())
                .build();
        return listDistinctFieldByCondition(puid, "sku", builder, String.class);
    }

    @Override
    public Page getPageList(Integer puid, AdProductPageParam param, Page page) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder("select * from t_amazon_ad_product_sd ");
        StringBuilder countSql = new StringBuilder("select count(*) from t_amazon_ad_product_sd ");
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }
        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> groupIds = StringUtil.splitStr(param.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, argsList));
        }
        //标签筛选
        if (CollectionUtils.isNotEmpty(param.getAdIds())) {
            whereSql.append(SqlStringUtil.dealInList("ad_id", param.getAdIds(), argsList));
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, argsList));
        }

        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ?");
                argsList.add(AmazonSdAdProduct.servingStatusEnum.AD_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }

        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                if (param.getListSearchValueNew().size() > 1) {
                    whereSql.append(SqlStringUtil.dealInList("asin", param.getListSearchValueNew(), argsList));
                } else {
                    whereSql.append(" and asin = ? ");
                    argsList.add(param.getListSearchValueNew().get(0).trim());
                }

            } else if ("msku".equalsIgnoreCase(param.getSearchField())) {
                if (param.getListSearchValueNew().size() > 1) {
                    whereSql.append(SqlStringUtil.dealInList("sku", param.getListSearchValueNew(), argsList));
                } else {
                    whereSql.append(" and sku like ? ");
                    argsList.add("%" + param.getListSearchValueNew().get(0).trim() + "%");
                }
            } else if ("parentAsin".equalsIgnoreCase(param.getSearchField())) {
                List<String> asin = odsProductDao.listByParentAsin(param.getPuid(), param.getShopId(), param.getListSearchValueNew());
                if (asin.size() == 0) {
                    asin.add("-1");
                }
                whereSql.append(SqlStringUtil.dealInList("asin", asin, argsList));
            }
        }


        sql.append(whereSql);
        countSql.append(whereSql);
        sql.append(" order by data_update_time desc, id desc ");

        Object[] args = argsList.toArray();
        return this.getPageResult(puid,page.getPageNo(), page.getPageSize(), countSql.toString(), args, sql.toString(), args, AmazonSdAdProduct.class);
    }

    @Override
    public List<AmazonSdAdProduct> getList(Integer puid, AdProductPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId());

        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId());
            builder.inStrList("campaign_id", campaignIds.toArray(new String[]{}));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            builder.inStrList("campaign_id", param.getCampaignIdList().toArray(new String[]{}));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getGroupId());
            builder.inStrList("ad_group_id", groupIds.toArray(new String[]{}));
        }
        //标签查询
        if (CollectionUtils.isNotEmpty(param.getAdIds())) {
            builder.inStrList("ad_id",param.getAdIds().toArray(new String[]{}));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                if(param.getListSearchValueNew().size() > 1){
                    builder.in("asin",param.getListSearchValueNew().toArray());
                }else {
                    builder.equalTo("asin",param.getListSearchValueNew().get(0).trim());
                }

            } else if ("msku".equalsIgnoreCase(param.getSearchField())) {
                if(param.getListSearchValueNew().size() > 1){
                    builder.in("sku",param.getListSearchValueNew().toArray());
                }else {
                    builder.like("sku", param.getListSearchValueNew().get(0).trim());
                }

            } else if ("parentAsin".equalsIgnoreCase(param.getSearchField())) {
                List<String> asin = odsProductDao.listByParentAsin(param.getPuid(), param.getShopId(), param.getListSearchValueNew());
                if (asin.size() == 0) {
                    asin.add("-1");
                }
                builder.in("asin",asin.toArray());
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            builder.inStrList("state", statusList.toArray(new String[]{}));
        }

        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                builder.equalTo("serving_status", AmazonSdAdProduct.servingStatusEnum.AD_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                builder.inStrList("serving_status", list.toArray(new String[]{}));
            }

        }

        builder.orderByDesc("data_update_time", "id");
        builder.limit(Constants.TOTALSIZELIMIT);  // 限制10万

        return listByCondition(puid,builder.build());
    }

    @Override
    public void updateDataUpdateTime(Integer puid, Integer shopId, String adId, LocalDate localDate) {
        String sql = "update t_amazon_ad_product_sd set data_update_time=?,update_time=now() where puid = ? and shop_id = ? and `ad_id`=?";
        getJdbcTemplate(puid).update(sql,new Object[]{localDate,puid,shopId,adId});
    }

    @Override
    public void updateList(Integer puid, List<AmazonSdAdProduct> list) {
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_product_sd` set `state`=?,")
                .append("`update_time` = now(),update_id=? where puid=? and shop_id=? and campaign_id=? and ad_id=?");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonSdAdProduct amazonAdProduct : list) {
            batchArg = new Object[]{
                    amazonAdProduct.getState(),
                    amazonAdProduct.getUpdateId(),
                    amazonAdProduct.getPuid(),
                    amazonAdProduct.getShopId(),
                    amazonAdProduct.getCampaignId(),
                    amazonAdProduct.getAdId()
            };
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate(sql.toString(), batchArgs);
    }

    @Override
    public List<AmazonSdAdProduct> listByIds(int puid, int shopId, List<Long> ids) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .in("id", ids.toArray())
                .build();

        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public List<String> getAsinByCampaignId(int puid, Integer shopId, String campaignId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("state","enabled")
                .build();
        return listDistinctFieldByCondition(puid,"asin",builder,String.class);
    }

    @Override
    public List<String> getAsinByCampaignIdAndGroupId(int puid, Integer shopId, String campaignId, String groupId) {
        String sql = " SELECT DISTINCT(asin) FROM `t_amazon_ad_product_sd` WHERE puid = ? AND shop_id = ? AND campaign_id = ?";
        List<String> list;
        if (StringUtils.isNotBlank(groupId)) {
            sql += " AND ad_group_id = ? AND state = 'enabled' ";
            list =  getJdbcTemplate(puid).queryForList(sql, String.class, puid, shopId, campaignId, groupId);
        } else {
            sql += " AND state = 'enabled' ";
            list =  getJdbcTemplate(puid).queryForList(sql, String.class, puid, shopId, campaignId);
        }

        return list;
    }

    @Override
    public String getAsinByAdGroupId(int puid, Integer shopId, String campaignId, String adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("ad_group_id", adGroupId)
                .equalTo("state","enabled")
                .orderByDesc("creation_date").limit(1)
                .build();
        return getByCondition(puid, "asin", String.class, builder);
    }

    @Override
    public List<String> getAdIdsByProductArg(int puid,AdProductPageParam param){
        List<Object> argsList = Lists.newArrayList();
        StringBuilder adIdsSql = new StringBuilder(" select c.ad_id from t_amazon_ad_product_sd c where c.puid = ? ");
        argsList.add(puid);

        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }
        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            whereSql.append(" and c.campaign_id = ? ");
            argsList.add(param.getCampaignId());
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            whereSql.append(" and c.ad_group_id = ? ");
            argsList.add(param.getGroupId());
        }

        //标签筛选
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getAdIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.ad_id", param.getAdIds(), argsList));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值

            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                if(param.getListSearchValueNew().size() > 1){
                    whereSql.append(SqlStringUtil.dealInList("c.asin",param.getListSearchValueNew(),argsList));
                }else {
                    whereSql.append(" and c.asin = ? ");
                    argsList.add(param.getListSearchValueNew().get(0).trim());
                }

            } else if ("msku".equalsIgnoreCase(param.getSearchField())) {
                if(param.getListSearchValueNew().size() > 1){
                    whereSql.append(SqlStringUtil.dealInList("c.sku",param.getListSearchValueNew(),argsList));
                }else {
                    whereSql.append(" and c.sku like ? ");
                    argsList.add("%" + param.getListSearchValueNew().get(0).trim() + "%");
                }

            }

        }
        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }

        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ?");
                argsList.add(AmazonSdAdProduct.servingStatusEnum.AD_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }
        }

        adIdsSql.append(whereSql);
        List<String> adIdsList = getJdbcTemplate(puid).queryForList(adIdsSql.toString(),argsList.toArray(),String.class);
        return adIdsList;
    }

    @Override
    public Integer statSumCountByAdGroup(int puid, Integer shopId, List<String> status, List<String> adGroupIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .in("ad_group_id", adGroupIds.toArray())
                .in("state", status.toArray())
                .build();

        String sql = "select count(*) c from t_amazon_ad_product_sd where " + conditionBuilder.getSql();
        List<Integer> list = getJdbcTemplate(puid).queryForList(sql, Integer.class,conditionBuilder.getValues());
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : 0 ;
    }
    @Override
    public Integer statSumCountByAdGroupPage(int puid, Integer shopId, List<String> status, GroupPageParam param) {
        StringBuilder builder = new StringBuilder("select count(*) c from t_amazon_ad_product_sd where puid = ? and shop_id = ? ");
        List<Object> args = Lists.newArrayList(puid,shopId);
        builder.append(SqlStringUtil.dealInList("state" ,status,args));
        builder.append(" and ad_group_id in ( ").append(getGroupPageSql(puid,param,args)).append(" )");
        List<Integer> list = getJdbcTemplate(puid).queryForList(builder.toString(), Integer.class,args.toArray());
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : 0 ;
    }

    private String getGroupPageSql(Integer puid, GroupPageParam param,List<Object> args) {
        StringBuilder selectSql = new StringBuilder("select ad_group_id from t_amazon_ad_group_sd ");

        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        args.add(puid);

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            args.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            whereSql.append(SqlStringUtil.dealInList("campaign_id", StringUtil.splitStr(param.getCampaignId()), args));
        }
        if (StringUtils.isNotBlank(param.getMultiCampaignId())) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", StringUtil.splitStr(param.getMultiCampaignId()), args));
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), args));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getGroupId());
            if (groupIds.size() < 10000) {
                whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, args));
            } else {
                whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", groupIds, args));
            }
        }
        if (StringUtils.isNotBlank(param.getMultiGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getMultiGroupId());
            if (groupIds.size() < 10000) {
                whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, args));
            } else {
                whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", groupIds, args));
            }
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getGroupIds())) {  // 广告标签
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", param.getGroupIds(), args));
        }

        // 广告策略筛选
        if(CollectionUtil.isNotEmpty(param.getAdStrategyTypeList())) {
            String subSql = AdGroupStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(), args, "ad_group_id", false);
            if(StringUtils.isNotEmpty(subSql)){
                whereSql.append(subSql);
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if (StringUtils.isNotBlank(param.getSearchType()) && SearchTypeEnum.EXACT.getValue().equals(param.getSearchType())) {
                whereSql.append(" and name = ? ");
                args.add(param.getSearchValue().trim());
            } else {
                whereSql.append(" and name like ? ");
                args.add("%" + param.getSearchValue().trim() + "%");
            }
        }

        if (CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            whereSql.append(SqlStringUtil.dealInList("name", param.getSearchValueList(), args));
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, args));
        }
        if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
            whereSql.append(" and serving_status = 'AD_GROUP_STATUS_ENABLED' ");
        }
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (param.getBidMin() != null) {   //默认竞价
                whereSql.append(" and default_bid >= ? ");
                args.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and default_bid <= ? ");
                args.add(param.getBidMax());
            }
        }
        selectSql.append(whereSql);
        return selectSql.toString();
    }

    @Override
    public List<String> getAdIds(Integer puid, Integer shopId, String marketPlaceId, List<String> onlineAdId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .equalTo("marketplace_id",marketPlaceId)
                .inStrList("ad_id",onlineAdId.toArray(new String[]{}))
                .build();
        return listDistinctFieldByCondition(puid,"ad_id",builder,String.class);
    }

    @Override
    public List<AmazonSdAdProduct> listValidByGroupIds(Integer puid, Integer shopId, List<String> adGroupIds) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("ad_group_id", adGroupIds.toArray(new String[]{}))
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()});

        return listByCondition(puid,builder.build());
    }

    @Override
    public List<AmazonSdAdProduct> listValidIdByGroupIds(Integer puid, Integer shopId, List<String> adGroupIds) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("ad_group_id", adGroupIds.toArray(new String[]{}))
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()});

        return listByConditionWithFields(puid, Arrays.asList("campaign_id", "ad_id", "ad_group_id", "landing_page_type", "sku", "asin", "landing_page_url", "ad_name"), builder.build());
    }

    @Override
    public List<AmazonSdAdProduct> getBySkus(int puid, List<String> skus) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inStrList("sku", skus.toArray(new String[0]))
                .build());
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<AmazonSdAdProduct> listByAdIds(int puid, int shopId, Set<String> adIds) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("ad_id", adIds.toArray(new String[0]));
        return listByCondition(puid,builder.build());
    }

    @Override
    public List<AmazonSdAdProduct> listByGroupId(int puid, int shopId, String adGroupId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId);
        return listByCondition(puid,builder.build());
    }

    @Override
    public Page<AdProductStrategyVo> queryAdProductStrategy(AdProductStrategyParam param) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder selectSql = new StringBuilder("select p.puid puid, p.shop_id shopId, p.marketplace_id marketplaceId, p.ad_id adId, p.asin asin, p.sku sku,c.type adType, ");
        selectSql.append("c.portfolio_id portfolioId, p.campaign_id campaignId, c.name campaignName, c.state campaignState, ");
        selectSql.append("p.ad_group_id adGroupId, g.name adGroupName, g.state adGroupState, p.state productState ");
        selectSql.append("from t_amazon_ad_product_sd p ");

        StringBuilder joinSql = new StringBuilder("inner join t_amazon_ad_group_sd g on p.puid = g.puid and p.shop_id=g.shop_id and p.campaign_id = g.campaign_id and p.ad_group_id = g.ad_group_id ");
        joinSql.append("inner join t_amazon_ad_campaign_all c on p.puid = c.puid and p.shop_id=c.shop_id and p.campaign_id = c.campaign_id ");

        StringBuilder countSql = new StringBuilder("select count(*) from t_amazon_ad_product_sd p ");

        selectSql.append(joinSql);
        countSql.append(joinSql);

        StringBuilder whereSql = new StringBuilder("where p.puid = ? and p.shop_id = ? and p.is_state_pricing = 0 ");
        argsList.add(param.getPuid());
        argsList.add(param.getShopId());

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            whereSql.append(SqlStringUtil.dealInList("p.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getAdGroupIdList())) {
            whereSql.append(SqlStringUtil.dealInList("p.ad_group_id", param.getAdGroupIdList(), argsList));
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getPortfolioIdList())) {
            if (param.getPortfolioIdList().size() == 1 && param.getPortfolioIdList().contains("-1")) {
                whereSql.append(" and c.portfolio_id is null ");
            } else if (param.getPortfolioIdList().contains("-1")) {
                whereSql.append(" and ( ");
                whereSql.append(SqlStringUtil.dealInListNotAnd("c.portfolio_id", param.getPortfolioIdList(), argsList));
                whereSql.append(" or c.portfolio_id is null ) ");
            } else {
                whereSql.append(SqlStringUtil.dealInList("c.portfolio_id", param.getPortfolioIdList(), argsList));
            }
        }

        whereSql.append(" and p.asin is not null and p.sku is not null");

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getProductValueList())) {
            if (ProductSearchFieldEnum.asin.getCode().equals(param.getProductType())) {
                whereSql.append(SqlStringUtil.dealInList("p.asin", param.getProductValueList(), argsList));
            } else if (ProductSearchFieldEnum.msku.getCode().equals(param.getProductType())) {
                whereSql.append(SqlStringUtil.dealInList("p.sku", param.getProductValueList(), argsList));
            }
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getStateList())) {
            whereSql.append(SqlStringUtil.dealInList("p.state", param.getStateList(), argsList));
        }

        selectSql.append(whereSql);
        countSql.append(whereSql);

        return getPageResultByClass(param.getPuid(), param.getPageNo(), param.getPageSize(),
                countSql.toString(), argsList.toArray(), selectSql.toString(), argsList.toArray(),
                AdProductStrategyVo.class);
    }

    @Override
    public void updateStatePricing(Integer puid, Integer shopId, String adId, int isPricing, int pricingState, Integer updateId) {
        StringBuilder sql = new StringBuilder("update t_amazon_ad_product_sd set is_state_pricing=?,pricing_start_stop_state=?, update_id=?,update_time=now() where puid = ? and shop_id = ? and `ad_id`=? ");
        List<Object> args = Lists.newArrayList(isPricing, pricingState, updateId, puid, shopId, adId);
        getJdbcTemplate(puid).update(sql.toString(), args.toArray());
    }


    @Override
    public void updateState(Integer puid, Integer shopId, String adId, String state) {
        StringBuilder sql = new StringBuilder("update t_amazon_ad_product_sd set state=?,update_time=now() where puid = ? and shop_id = ? and `ad_id`=? ");
        List<Object> args = Lists.newArrayList(state, puid, shopId, adId);
        getJdbcTemplate(puid).update(sql.toString(), args.toArray());
    }


    @Override
    public List<String> getListAsinByCampaignIdAndGroupIdAndEnabled(Integer puid, Integer shopId, String campaignId, String groupId) {
        StringBuilder sql = new StringBuilder(" SELECT DISTINCT(asin) FROM `t_amazon_ad_product_sd` WHERE puid = ? AND shop_id = ? AND campaign_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(campaignId);
        sql.append(" AND state in ('enabled','paused')  ");
        if (StringUtils.isNotBlank(groupId)) {
            sql.append(" and ad_group_id = ? ");
            argsList.add(groupId);
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, argsList.toArray());
    }

    @Override
    public List<DownloadCenterBaseDataBO> queryBaseData4DownloadByProductIdList(Integer puid, Integer shopId, List<String> adIdList) {
        StringBuilder sql = new StringBuilder("select ad_id id, state from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? ");
        sql.append(" and shop_id = ? ");
        sql.append(" and ad_id in ('").append(StringUtils.join(adIdList, "','")).append("')");
        return getJdbcTemplate(puid).query(sql.toString(), new BeanPropertyRowMapper<>(DownloadCenterBaseDataBO.class), puid, shopId);
    }
}