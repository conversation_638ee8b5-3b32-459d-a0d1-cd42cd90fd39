package com.meiyunji.sponsored.service.cpc.service2.sp.impl;

import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.cpc.po.ReportBase;
import com.meiyunji.sponsored.service.cpc.po.ReportNewBase;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.SBCampaignCostTypeEnum;
import com.meiyunji.sponsored.service.util.ReportParamUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;

/**
 * Created by xp on 2021/4/17.
 * 统计报表数据
 */
@Service
public class CpcCommService {

    /**
     * 填充最新的数据，与时间无关
     */
    public void fillLatestReportDataIntoPageVo(CpcCommPageVo vo, AdHomePerformancedto latest) {
        vo.setCumulativeReach(Optional.of(latest.getCumulativeReach()).orElse(vo.getCumulativeReach()));
        vo.setImpressionsFrequencyAverage(Optional.of(latest.getImpressionsFrequencyAverage()).orElse(vo.getImpressionsFrequencyAverage()));
    }

    /**
     * 将报告PO的数据填给vo
     *
     * @param vo:
     * @param report:
     * @param shopSaleDto: 店铺销售额
     */
    public void fillReportDataIntoPageVo(CpcCommPageVo vo, ReportBase report, ShopSaleDto shopSaleDto) {
        if (report != null) {
            ReportMetadata reportMetadata = new ReportMetadata();
            reportMetadata.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
            reportMetadata.setClicks(report.getClicks() != null ? report.getClicks() : 0);
            /**
             * TODO 广告报告重构
             * CPC,VCPM广告销量
             */
            reportMetadata.setOrderNum(report.getOrderNum() != null ? report.getOrderNum() : 0);
            //本广告产品销量
            reportMetadata.setAdOrderNum(report.getAdOrderNum() != null ? report.getAdOrderNum() : 0);
            //广告订单量
            reportMetadata.setSaleNum(report.getSaleNum() != null ? report.getSaleNum() : 0);
            //本广告产品订单量
            reportMetadata.setAdSaleNum(report.getAdSaleNum() != null ? report.getAdSaleNum() : 0);
            reportMetadata.setCost(report.getCost() != null ? report.getCost() : BigDecimal.ZERO);
            reportMetadata.setSales(report.getTotalSales() != null ? report.getTotalSales() : BigDecimal.ZERO);
            reportMetadata.setAdSales(report.getTotalSales() != null ? report.getTotalSales() : BigDecimal.ZERO);
            //每笔订单花费
            reportMetadata.setCpa(report.getCpa() != null ? report.getCpa() : BigDecimal.ZERO);
            //其他产品广告订单量
            reportMetadata.setAdOtherOrderNum(report.getAdOtherOrderNum() != null ? report.getAdOtherOrderNum() : 0);
            //本广告产品销售额
            reportMetadata.setAdSale(report.getAdSales() != null ? report.getAdSales() : BigDecimal.ZERO);
            //其他产品广告销售额
            reportMetadata.setAdOtherSales(report.getAdOtherSales() != null ? report.getAdOtherSales() : BigDecimal.ZERO);
            //其他产品广告销量
            reportMetadata.setAdOtherSaleNum(report.getAdOtherSaleNum() != null ? report.getAdOtherSaleNum() : 0);
            //可见展示次数(VCPM专用)
            // 兼容 sb
            reportMetadata.setViewImpressions(Optional.ofNullable(
                            CampaignTypeEnum.sb.getCampaignType().equals(report.getType()) ? report.getViewableImpressions() : report.getViewImpressions())
                    .orElse(0));
            //VCPM(每千次展现费用,只有sd广告且是vcpm类型才展示) todo sb也会计算
            reportMetadata.setVcpm(reportMetadata.getViewImpressions() == 0 ? BigDecimal.ZERO :
                    MathUtil.divide(MathUtil.multiply(reportMetadata.getCost(), new BigDecimal(1000)), new BigDecimal(reportMetadata.getViewImpressions()), 2));
            //“品牌新买家”订单量
            reportMetadata.setOrdersNewToBrand14d(report.getOrdersNewToBrand14d() != null ? report.getOrdersNewToBrand14d() : 0);
            //“品牌新买家”订单百分比
            reportMetadata.setOrderRateNewToBrand14d(report.getOrderRateNewToBrand() != null ? report.getOrderRateNewToBrand() : BigDecimal.ZERO);
            //“品牌新买家”销售额
            reportMetadata.setSalesNewToBrand14d(report.getSalesNewToBrand14d() != null ? report.getSalesNewToBrand14d() : BigDecimal.ZERO);
            //“品牌新买家”销售额百分比
            reportMetadata.setSalesRateNewToBrand14d(report.getSalesRateNewToBrand() != null ? report.getSalesRateNewToBrand() : BigDecimal.ZERO);
            //“品牌新买家”销量
            reportMetadata.setUnitsOrderedNewToBrand14d(report.getUnitsOrderedNewToBrand14d() != null ? report.getUnitsOrderedNewToBrand14d() : 0);
            //“品牌新买家”销量百分比
            reportMetadata.setUnitsOrderedRateNewToBrand14d(report.getUnitsOrderedRateNewToBrand() != null ? report.getUnitsOrderedRateNewToBrand() : BigDecimal.ZERO);
            //“品牌新买家”订单转化率
            reportMetadata.setOrdersNewToBrandPercentage14d(report.getOrdersNewToBrandPercentage() != null ? report.getOrdersNewToBrandPercentage() : BigDecimal.ZERO);
            if (shopSaleDto != null) {
                reportMetadata.setShopSales(shopSaleDto.getSumRange() != null ? shopSaleDto.getSumRange() : BigDecimal.ZERO);
            }

            //设置搜索词首页首位曝光量字段
            reportMetadata.setTopImpressionShare(ReportParamUtil
                    .getTopOfSearchImpressionShare(report.getMaxTopIs(), report.getMinTopIs()));

            reportMetadata.setNewToBrandDetailPageViews(Optional.ofNullable(report.getNewToBrandDetailPageViews()).orElse(0));
            reportMetadata.setAddToCart(Optional.ofNullable(report.getAddToCart()).orElse(0));
            reportMetadata.setAddToCartRate(reportMetadata.getImpressions() == 0 ? BigDecimal.ZERO :
                    MathUtil.divideOfObject(MathUtil.multiplyOfObject(reportMetadata.getAddToCart(), 100), reportMetadata.getImpressions(), 2));
            reportMetadata.setECPAddToCart(reportMetadata.getAddToCart() == 0 ? BigDecimal.ZERO :
                    MathUtil.divideOfObject(reportMetadata.getCost(), reportMetadata.getAddToCart(), 2));
            reportMetadata.setVideo5SecondViews(Optional.ofNullable(report.getVideo5SecondViews()).orElse(0));
            reportMetadata.setVideo5SecondViewRate(reportMetadata.getImpressions() == 0 ? BigDecimal.ZERO :
                    MathUtil.divideOfObject(MathUtil.multiplyOfObject(reportMetadata.getVideo5SecondViews(), 100), reportMetadata.getImpressions(), 2));
            reportMetadata.setVideoFirstQuartileViews(Optional.ofNullable(report.getVideoFirstQuartileViews()).orElse(0));
            reportMetadata.setVideoMidpointViews(Optional.ofNullable(report.getVideoMidpointViews()).orElse(0));
            reportMetadata.setVideoThirdQuartileViews(Optional.ofNullable(report.getVideoThirdQuartileViews()).orElse(0));
            reportMetadata.setVideoCompleteViews(Optional.ofNullable(report.getVideoCompleteViews()).orElse(0));
            reportMetadata.setVideoUnmutes(Optional.ofNullable(report.getVideoUnmutes()).orElse(0));
            Integer viewImpressions = reportMetadata.getViewImpressions();
            reportMetadata.setViewabilityRate(reportMetadata.getImpressions() == 0 ? BigDecimal.ZERO :
                    MathUtil.divideOfObject(MathUtil.multiplyOfObject(viewImpressions, 100), reportMetadata.getImpressions(), 2));
            reportMetadata.setViewClickThroughRate(viewImpressions == 0 ? BigDecimal.ZERO :
                    MathUtil.divideOfObject(MathUtil.multiplyOfObject(report.getClicks(), 100), viewImpressions, 2));
            reportMetadata.setBrandedSearches(Optional.ofNullable(report.getBrandedSearches()).orElse(0));
            reportMetadata.setDetailPageViews(Optional.ofNullable(report.getDetailPageViews()).orElse(0));
            reportMetadata.setCumulativeReach(Optional.ofNullable(report.getCumulativeReach()).orElse(0));
            reportMetadata.setImpressionsFrequencyAverage(Optional.ofNullable(report.getImpressionsFrequencyAverage()).orElse(BigDecimal.ZERO));
            reportMetadata.setAdvertisingUnitPrice(reportMetadata.getSaleNum() == 0 ? BigDecimal.ZERO :
                    MathUtil.divide(reportMetadata.getSales(), new BigDecimal(reportMetadata.getSaleNum()), 2));


            completeCpcCommPageVo(vo, reportMetadata);
        }
    }

    /**
     * 将报告PO的数据填给vo
     *
     * @param vo:
     * @param report:
     * @param shopSaleDto: 店铺销售额
     */
    public void fillReportDataIntoPageNewVo(CpcCommPageNewVo vo, ReportNewBase report, ShopSaleDto shopSaleDto) {
        if (report != null) {
            ReportMetadataNew reportMetadata = new ReportMetadataNew();
            reportMetadata.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
            reportMetadata.setClicks(report.getClicks() != null ? report.getClicks() : 0);
            /**
             * TODO 广告报告重构
             * CPC,VCPM广告销量
             */
            reportMetadata.setOrderNum(report.getOrderNum() != null ? report.getOrderNum() : 0);
            //本广告产品销量
            reportMetadata.setAdOrderNum(report.getAdOrderNum() != null ? report.getAdOrderNum() : 0);
            //广告订单量
            reportMetadata.setSaleNum(report.getSaleNum() != null ? report.getSaleNum() : 0);
            //本广告产品订单量
            reportMetadata.setAdSaleNum(report.getAdSaleNum() != null ? report.getAdSaleNum() : 0);
            reportMetadata.setCost(report.getCost() != null ? report.getCost() : BigDecimal.ZERO);
            reportMetadata.setSales(report.getTotalSales() != null ? report.getTotalSales() : BigDecimal.ZERO);
            reportMetadata.setAdSales(report.getTotalSales() != null ? report.getTotalSales() : BigDecimal.ZERO);
            //每笔订单花费
            reportMetadata.setCpa(report.getCpa() != null ? report.getCpa() : BigDecimal.ZERO);
            //其他产品广告订单量
            reportMetadata.setAdOtherOrderNum(report.getAdOtherOrderNum() != null ? report.getAdOtherOrderNum() : 0);
            //本广告产品销售额
            reportMetadata.setAdSale(report.getAdSales() != null ? report.getAdSales() : BigDecimal.ZERO);
            //其他产品广告销售额
            reportMetadata.setAdOtherSales(report.getAdOtherSales() != null ? report.getAdOtherSales() : BigDecimal.ZERO);
            //其他产品广告销量
            reportMetadata.setAdOtherSaleNum(report.getAdOtherSaleNum() != null ? report.getAdOtherSaleNum() : 0);
            //可见展示次数(VCPM专用)
            // 兼容 sb
            reportMetadata.setViewImpressions(Optional.ofNullable(
                            CampaignTypeEnum.sb.getCampaignType().equals(report.getType()) ? report.getViewableImpressions() : report.getViewImpressions())
                    .orElse(0L));
            //VCPM(每千次展现费用,只有sd广告且是vcpm类型才展示) todo sb也会计算
            reportMetadata.setVcpm(reportMetadata.getViewImpressions() == 0 ? BigDecimal.ZERO :
                    MathUtil.divide(MathUtil.multiply(reportMetadata.getCost(), new BigDecimal(1000)), new BigDecimal(reportMetadata.getViewImpressions()), 2));
            //“品牌新买家”订单量
            reportMetadata.setOrdersNewToBrand14d(report.getOrdersNewToBrand14d() != null ? report.getOrdersNewToBrand14d() : 0);
            //“品牌新买家”订单百分比
            reportMetadata.setOrderRateNewToBrand14d(report.getOrderRateNewToBrand() != null ? report.getOrderRateNewToBrand() : BigDecimal.ZERO);
            //“品牌新买家”销售额
            reportMetadata.setSalesNewToBrand14d(report.getSalesNewToBrand14d() != null ? report.getSalesNewToBrand14d() : BigDecimal.ZERO);
            //“品牌新买家”销售额百分比
            reportMetadata.setSalesRateNewToBrand14d(report.getSalesRateNewToBrand() != null ? report.getSalesRateNewToBrand() : BigDecimal.ZERO);
            //“品牌新买家”销量
            reportMetadata.setUnitsOrderedNewToBrand14d(report.getUnitsOrderedNewToBrand14d() != null ? report.getUnitsOrderedNewToBrand14d() : 0);
            //“品牌新买家”销量百分比
            reportMetadata.setUnitsOrderedRateNewToBrand14d(report.getUnitsOrderedRateNewToBrand() != null ? report.getUnitsOrderedRateNewToBrand() : BigDecimal.ZERO);
            //“品牌新买家”订单转化率
            reportMetadata.setOrdersNewToBrandPercentage14d(report.getOrdersNewToBrandPercentage() != null ? report.getOrdersNewToBrandPercentage() : BigDecimal.ZERO);
            if (shopSaleDto != null) {
                reportMetadata.setShopSales(shopSaleDto.getSumRange() != null ? shopSaleDto.getSumRange() : BigDecimal.ZERO);
            }

            //设置搜索词首页首位曝光量字段
            reportMetadata.setTopImpressionShare(ReportParamUtil
                    .getTopOfSearchImpressionShare(report.getMaxTopIs(), report.getMinTopIs()));

            reportMetadata.setNewToBrandDetailPageViews(Optional.ofNullable(report.getNewToBrandDetailPageViews()).orElse(0L));
            reportMetadata.setAddToCart(Optional.ofNullable(report.getAddToCart()).orElse(0L));
            reportMetadata.setAddToCartRate(reportMetadata.getImpressions() == 0 ? BigDecimal.ZERO :
                    MathUtil.divideOfObject(MathUtil.multiplyOfObject(reportMetadata.getAddToCart(), 100), reportMetadata.getImpressions(), 2));
            reportMetadata.setECPAddToCart(reportMetadata.getAddToCart() == 0 ? BigDecimal.ZERO :
                    MathUtil.divideOfObject(reportMetadata.getCost(), reportMetadata.getAddToCart(), 2));
            reportMetadata.setVideo5SecondViews(Optional.ofNullable(report.getVideo5SecondViews()).orElse(0L));
            reportMetadata.setVideo5SecondViewRate(reportMetadata.getImpressions() == 0 ? BigDecimal.ZERO :
                    MathUtil.divideOfObject(MathUtil.multiplyOfObject(reportMetadata.getVideo5SecondViews(), 100), reportMetadata.getImpressions(), 2));
            reportMetadata.setVideoFirstQuartileViews(Optional.ofNullable(report.getVideoFirstQuartileViews()).orElse(0L));
            reportMetadata.setVideoMidpointViews(Optional.ofNullable(report.getVideoMidpointViews()).orElse(0L));
            reportMetadata.setVideoThirdQuartileViews(Optional.ofNullable(report.getVideoThirdQuartileViews()).orElse(0L));
            reportMetadata.setVideoCompleteViews(Optional.ofNullable(report.getVideoCompleteViews()).orElse(0L));
            reportMetadata.setVideoUnmutes(Optional.ofNullable(report.getVideoUnmutes()).orElse(0L));
            Long viewImpressions = reportMetadata.getViewImpressions();
            reportMetadata.setViewabilityRate(reportMetadata.getImpressions() == 0 ? BigDecimal.ZERO :
                    MathUtil.divideOfObject(MathUtil.multiplyOfObject(viewImpressions, 100), reportMetadata.getImpressions(), 2));
            reportMetadata.setViewClickThroughRate(viewImpressions == 0 ? BigDecimal.ZERO :
                    MathUtil.divideOfObject(MathUtil.multiplyOfObject(report.getClicks(), 100), viewImpressions, 2));
            reportMetadata.setBrandedSearches(Optional.ofNullable(report.getBrandedSearches()).orElse(0L));
            reportMetadata.setDetailPageViews(Optional.ofNullable(report.getDetailPageViews()).orElse(0L));
            reportMetadata.setCumulativeReach(Optional.ofNullable(report.getCumulativeReach()).orElse(0L));
            reportMetadata.setImpressionsFrequencyAverage(Optional.ofNullable(report.getImpressionsFrequencyAverage()).orElse(BigDecimal.ZERO));
            reportMetadata.setAdvertisingUnitPrice(reportMetadata.getSaleNum() == 0 ? BigDecimal.ZERO :
                    MathUtil.divide(reportMetadata.getSales(), new BigDecimal(reportMetadata.getSaleNum()), 2));


            completeCpcCommPageNewVo(vo, reportMetadata);
        }
    }

    /**
     * 根据reportMetadata组装完善cpc报表字段
     *
     * @param cpcCommPageVo：
     * @param reportMetadata：
     */
    public void completeCpcCommPageVo(CpcCommPageVo cpcCommPageVo, ReportMetadata reportMetadata) {
        if (cpcCommPageVo == null || reportMetadata == null) {
            return;
        }

        cpcCommPageVo.setImpressions(reportMetadata.getImpressions());
        cpcCommPageVo.setClicks(reportMetadata.getClicks());

        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (reportMetadata.getCost() != null
                && cpcCommPageVo.getSumCost() != null
                && cpcCommPageVo.getSumCost().doubleValue() > 0) {
            cpcCommPageVo.setAdCostPercentage(MathUtil.multiply(MathUtil.divide(reportMetadata.getCost().toString(), cpcCommPageVo.getSumCost().toString()), "100"));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (reportMetadata.getSales() != null
                && cpcCommPageVo.getSumAdSale() != null
                && cpcCommPageVo.getSumAdSale().doubleValue() > 0) {
            cpcCommPageVo.setAdSalePercentage(MathUtil.multiply(MathUtil.divide(reportMetadata.getSales().toString(), cpcCommPageVo.getSumAdSale().toString()), "100"));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (reportMetadata.getSaleNum() != null
                && cpcCommPageVo.getSumAdOrderNum() != null
                && cpcCommPageVo.getSumAdOrderNum().doubleValue() > 0) {
            cpcCommPageVo.setAdOrderNumPercentage(MathUtil.multiply(MathUtil.divide(reportMetadata.getSaleNum().toString(), cpcCommPageVo.getSumAdOrderNum().toString()), "100"));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (reportMetadata.getOrderNum() != null
                && cpcCommPageVo.getSumOrderNum() != null
                && cpcCommPageVo.getSumOrderNum().doubleValue() > 0) {
            cpcCommPageVo.setOrderNumPercentage(MathUtil.multiply(MathUtil.divide(reportMetadata.getOrderNum().toString(), cpcCommPageVo.getSumOrderNum().toString()), "100"));
        }


        // 点击率（CTR）:广告展示时被买家点击的比率。该值由点击量除以曝光量计算得出。
        if (reportMetadata.getImpressions() != null
                && reportMetadata.getImpressions() > 0
                && reportMetadata.getClicks() != null) {
            cpcCommPageVo.setCtr(MathUtil.multiply(MathUtil.divide(reportMetadata.getClicks().toString(), reportMetadata.getImpressions().toString()), "100"));
        }

        // attributedUnitsOrdered7dSKU 变更为 attributedConversions7d(更加准确) / clicks: 订单转化率:此值由总广告订单数除以点击量计算得出
        if (reportMetadata.getSaleNum() != null
                && reportMetadata.getClicks() != null
                && reportMetadata.getClicks() > 0) {
            cpcCommPageVo.setCvr(MathUtil.multiply(MathUtil.divide(reportMetadata.getSaleNum().toString(), reportMetadata.getClicks().toString()), "100"));
        }

        // cost / attributedSales7d: ACoS是在指定时间范围内，广告花费与广告销售额的百分比，
        // 此值由广告花费除以广告销售额计算得出
        if (reportMetadata.getCost() != null
                && reportMetadata.getSales() != null
                && reportMetadata.getSales().doubleValue() > 0) {
            cpcCommPageVo.setAcos(MathUtil.multiply(MathUtil.divide(reportMetadata.getCost().toString(), reportMetadata.getSales().toString()), "100"));
        }

        // cost / 店铺销售额: ACoTS是在指定时间范围内，广告总花费与总销售额的百分比，此值由广告总花费除以总销售额计算得出
        if (reportMetadata.getCost() != null
                && reportMetadata.getShopSales() != null
                && reportMetadata.getShopSales().doubleValue() > 0) {
            cpcCommPageVo.setAcots(MathUtil.multiply(MathUtil.divide(reportMetadata.getCost().toString(), reportMetadata.getShopSales().toString()), "100"));
        }

        // attributedSales7d /店铺销售额: ASoTS是在指定时间范围内，广告销售额与原价销售额的百分比，
        // 此值由广告销售额除以原价销售额计算得出
        if (reportMetadata.getSales() != null
                && reportMetadata.getShopSales() != null
                && reportMetadata.getShopSales().doubleValue() > 0) {
            cpcCommPageVo.setAsots(MathUtil.multiply(MathUtil.divide(reportMetadata.getSales().toString(), reportMetadata.getShopSales().toString()), "100"));
        }

        // 广告订单数: attributedUnitsOrdered7dSKU  变更为 attributedConversions7d(更加准确)
        cpcCommPageVo.setAdOrderNum(reportMetadata.getSaleNum());

        // 广告花费：cost广告活动的广告总花费
        if (reportMetadata.getCost() != null) {
            cpcCommPageVo.setAdCost(reportMetadata.getCost().setScale(2,RoundingMode.HALF_UP).toString());
        }

        // 平均点击费用：cost / clicks, 广告的每次点击费用
        if (reportMetadata.getCost() != null
                && reportMetadata.getClicks() != null
                && reportMetadata.getClicks() > 0) {
            cpcCommPageVo.setAdCostPerClick(MathUtil.divide(reportMetadata.getCost().toString(), reportMetadata.getClicks().toString(), 2));
        }

        // 广告销售额:广告销售额是指通过广告售出产品的销售额
        if (reportMetadata.getSales() != null) {
            cpcCommPageVo.setAdSale(reportMetadata.getSales().setScale(2,RoundingMode.HALF_UP).toString());
        }

        // 广告销售额:广告销售额是指通过广告售出产品的销售额
        if (reportMetadata.getCost() != null && reportMetadata.getAdSales() != null) {
            BigDecimal roas = reportMetadata.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : reportMetadata.getAdSales().divide(reportMetadata.getCost(), 2, BigDecimal.ROUND_HALF_UP);
            cpcCommPageVo.setRoas(roas.toString());
        }

        /**
         * TODO 广告报告重构
         * 可见展示次数(VCPM专用)
         */
        if (reportMetadata.getViewImpressions() != null) {
            cpcCommPageVo.setViewImpressions(reportMetadata.getViewImpressions());
        }
        //每笔订单花费
        if (reportMetadata.getCpa() != null) {
            cpcCommPageVo.setCpa(reportMetadata.getCpa().toString());
        }
        //本广告产品订单量
        if (reportMetadata.getAdSaleNum() != null) {
            cpcCommPageVo.setAdSaleNum(reportMetadata.getAdSaleNum());
        }
        //其他产品广告订单量
        if (reportMetadata.getAdOtherOrderNum() != null) {
            cpcCommPageVo.setAdOtherOrderNum(reportMetadata.getAdOtherOrderNum());
        }
        //本广告产品销售额
        if (reportMetadata.getAdSale() != null) {
            cpcCommPageVo.setAdSales(reportMetadata.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //其他产品广告销售额
        if (reportMetadata.getAdOtherSales() != null) {
            cpcCommPageVo.setAdOtherSales(reportMetadata.getAdOtherSales().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //CPC,VCPM广告销量
        if (reportMetadata.getOrderNum() != null) {
            cpcCommPageVo.setOrderNum(reportMetadata.getOrderNum());
        }
        //本广告产品销量
        if (reportMetadata.getAdOrderNum() != null) {
            cpcCommPageVo.setAdSelfSaleNum(reportMetadata.getAdOrderNum());
        }
        //其他产品广告销量
        if (reportMetadata.getAdOtherSaleNum() != null) {
            cpcCommPageVo.setAdOtherSaleNum(reportMetadata.getAdOtherSaleNum());
        }
        //VCPM(每千次展现费用,只有sd广告且是vcpm类型才展示)
        if (reportMetadata.getVcpm() != null) {
            cpcCommPageVo.setVcpm(reportMetadata.getVcpm().toString());
        }
        //“品牌新买家”订单量
        if (reportMetadata.getOrdersNewToBrand14d() != null) {
            cpcCommPageVo.setOrdersNewToBrandFTD(reportMetadata.getOrdersNewToBrand14d());
        }
        //“品牌新买家”订单百分比
        if (reportMetadata.getOrderRateNewToBrand14d() != null) {
            cpcCommPageVo.setOrderRateNewToBrandFTD(reportMetadata.getOrderRateNewToBrand14d().toString());
        }
        //“品牌新买家”销售额
        if (reportMetadata.getSalesNewToBrand14d() != null) {
            cpcCommPageVo.setSalesNewToBrandFTD(reportMetadata.getSalesNewToBrand14d().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //“品牌新买家”销售额百分比
        if (reportMetadata.getSalesRateNewToBrand14d() != null) {
            cpcCommPageVo.setSalesRateNewToBrandFTD(reportMetadata.getSalesRateNewToBrand14d().toString());
        }
        //“品牌新买家”销量
        if (reportMetadata.getUnitsOrderedNewToBrand14d() != null) {
            cpcCommPageVo.setUnitsOrderedNewToBrandFTD(reportMetadata.getUnitsOrderedNewToBrand14d());
        }
        //“品牌新买家”销量百分比
        if (reportMetadata.getUnitsOrderedRateNewToBrand14d() != null) {
            cpcCommPageVo.setUnitsOrderedRateNewToBrandFTD(reportMetadata.getUnitsOrderedRateNewToBrand14d().toString());
        }
        //“品牌新买家”订单转化率
        if (reportMetadata.getOrdersNewToBrandPercentage14d() != null) {
            cpcCommPageVo.setOrdersNewToBrandPercentageFTD(reportMetadata.getOrdersNewToBrandPercentage14d().toString());
        }

        if (reportMetadata.getTopImpressionShare() != null) {
            cpcCommPageVo.setTopImpressionShare(reportMetadata.getTopImpressionShare());
        }

        cpcCommPageVo.setNewToBrandDetailPageViews(reportMetadata.getNewToBrandDetailPageViews());
        cpcCommPageVo.setAddToCart(reportMetadata.getAddToCart());
        cpcCommPageVo.setAddToCartRate(reportMetadata.getAddToCartRate());
        cpcCommPageVo.setECPAddToCart(reportMetadata.getECPAddToCart());
        cpcCommPageVo.setVideo5SecondViews(reportMetadata.getVideo5SecondViews());
        cpcCommPageVo.setVideo5SecondViewRate(reportMetadata.getVideo5SecondViewRate());
        cpcCommPageVo.setVideoFirstQuartileViews(reportMetadata.getVideoFirstQuartileViews());
        cpcCommPageVo.setVideoMidpointViews(reportMetadata.getVideoMidpointViews());
        cpcCommPageVo.setVideoThirdQuartileViews(reportMetadata.getVideoThirdQuartileViews());
        cpcCommPageVo.setVideoCompleteViews(reportMetadata.getVideoCompleteViews());
        cpcCommPageVo.setVideoUnmutes(reportMetadata.getVideoUnmutes());
        cpcCommPageVo.setViewabilityRate(reportMetadata.getViewabilityRate());
        cpcCommPageVo.setViewClickThroughRate(reportMetadata.getViewClickThroughRate());
        cpcCommPageVo.setBrandedSearches(reportMetadata.getBrandedSearches());
        cpcCommPageVo.setDetailPageViews(reportMetadata.getDetailPageViews());
        cpcCommPageVo.setCumulativeReach(reportMetadata.getCumulativeReach());
        cpcCommPageVo.setImpressionsFrequencyAverage(reportMetadata.getImpressionsFrequencyAverage());
        cpcCommPageVo.setAdvertisingUnitPrice(reportMetadata.getAdvertisingUnitPrice());
    }

    /**
     * 根据reportMetadata组装完善cpc报表字段
     *
     * @param cpcCommPageVo：
     * @param reportMetadata：
     */
    public void completeCpcCommPageNewVo(CpcCommPageNewVo cpcCommPageVo, ReportMetadataNew reportMetadata) {
        if (cpcCommPageVo == null || reportMetadata == null) {
            return;
        }

        cpcCommPageVo.setImpressions(reportMetadata.getImpressions());
        cpcCommPageVo.setClicks(reportMetadata.getClicks());

        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (reportMetadata.getCost() != null
                && cpcCommPageVo.getSumCost() != null
                && cpcCommPageVo.getSumCost().doubleValue() > 0) {
            cpcCommPageVo.setAdCostPercentage(MathUtil.multiply(MathUtil.divide(reportMetadata.getCost().toString(), cpcCommPageVo.getSumCost().toString()), "100"));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (reportMetadata.getSales() != null
                && cpcCommPageVo.getSumAdSale() != null
                && cpcCommPageVo.getSumAdSale().doubleValue() > 0) {
            cpcCommPageVo.setAdSalePercentage(MathUtil.multiply(MathUtil.divide(reportMetadata.getSales().toString(), cpcCommPageVo.getSumAdSale().toString()), "100"));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (reportMetadata.getSaleNum() != null
                && cpcCommPageVo.getSumAdOrderNum() != null
                && cpcCommPageVo.getSumAdOrderNum().doubleValue() > 0) {
            cpcCommPageVo.setAdOrderNumPercentage(MathUtil.multiply(MathUtil.divide(reportMetadata.getSaleNum().toString(), cpcCommPageVo.getSumAdOrderNum().toString()), "100"));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (reportMetadata.getOrderNum() != null
                && cpcCommPageVo.getSumOrderNum() != null
                && cpcCommPageVo.getSumOrderNum().doubleValue() > 0) {
            cpcCommPageVo.setOrderNumPercentage(MathUtil.multiply(MathUtil.divide(reportMetadata.getOrderNum().toString(), cpcCommPageVo.getSumOrderNum().toString()), "100"));
        }


        // 点击率（CTR）:广告展示时被买家点击的比率。该值由点击量除以曝光量计算得出。
        if (reportMetadata.getImpressions() != null
                && reportMetadata.getImpressions() > 0
                && reportMetadata.getClicks() != null) {
            cpcCommPageVo.setCtr(MathUtil.multiply(MathUtil.divide(reportMetadata.getClicks().toString(), reportMetadata.getImpressions().toString()), "100"));
        }

        // attributedUnitsOrdered7dSKU 变更为 attributedConversions7d(更加准确) / clicks: 订单转化率:此值由总广告订单数除以点击量计算得出
        if (reportMetadata.getSaleNum() != null
                && reportMetadata.getClicks() != null
                && reportMetadata.getClicks() > 0) {
            cpcCommPageVo.setCvr(MathUtil.multiply(MathUtil.divide(reportMetadata.getSaleNum().toString(), reportMetadata.getClicks().toString()), "100"));
        }

        // cost / attributedSales7d: ACoS是在指定时间范围内，广告花费与广告销售额的百分比，
        // 此值由广告花费除以广告销售额计算得出
        if (reportMetadata.getCost() != null
                && reportMetadata.getSales() != null
                && reportMetadata.getSales().doubleValue() > 0) {
            cpcCommPageVo.setAcos(MathUtil.multiply(MathUtil.divide(reportMetadata.getCost().toString(), reportMetadata.getSales().toString()), "100"));
        }

        // cost / 店铺销售额: ACoTS是在指定时间范围内，广告总花费与总销售额的百分比，此值由广告总花费除以总销售额计算得出
        if (reportMetadata.getCost() != null
                && reportMetadata.getShopSales() != null
                && reportMetadata.getShopSales().doubleValue() > 0) {
            cpcCommPageVo.setAcots(MathUtil.multiply(MathUtil.divide(reportMetadata.getCost().toString(), reportMetadata.getShopSales().toString()), "100"));
        }

        // attributedSales7d /店铺销售额: ASoTS是在指定时间范围内，广告销售额与原价销售额的百分比，
        // 此值由广告销售额除以原价销售额计算得出
        if (reportMetadata.getSales() != null
                && reportMetadata.getShopSales() != null
                && reportMetadata.getShopSales().doubleValue() > 0) {
            cpcCommPageVo.setAsots(MathUtil.multiply(MathUtil.divide(reportMetadata.getSales().toString(), reportMetadata.getShopSales().toString()), "100"));
        }

        // 广告订单数: attributedUnitsOrdered7dSKU  变更为 attributedConversions7d(更加准确)
        cpcCommPageVo.setAdOrderNum(reportMetadata.getSaleNum());

        // 广告花费：cost广告活动的广告总花费
        if (reportMetadata.getCost() != null) {
            cpcCommPageVo.setAdCost(reportMetadata.getCost().setScale(2,RoundingMode.HALF_UP).toString());
        }

        // 平均点击费用：cost / clicks, 广告的每次点击费用
        if (reportMetadata.getCost() != null
                && reportMetadata.getClicks() != null
                && reportMetadata.getClicks() > 0) {
            cpcCommPageVo.setAdCostPerClick(MathUtil.divide(reportMetadata.getCost().toString(), reportMetadata.getClicks().toString(), 2));
        }

        // 广告销售额:广告销售额是指通过广告售出产品的销售额
        if (reportMetadata.getSales() != null) {
            cpcCommPageVo.setAdSale(reportMetadata.getSales().setScale(2,RoundingMode.HALF_UP).toString());
        }

        // 广告销售额:广告销售额是指通过广告售出产品的销售额
        if (reportMetadata.getCost() != null && reportMetadata.getAdSales() != null) {
            BigDecimal roas = reportMetadata.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : reportMetadata.getAdSales().divide(reportMetadata.getCost(), 2, BigDecimal.ROUND_HALF_UP);
            cpcCommPageVo.setRoas(roas.toString());
        }

        /**
         * TODO 广告报告重构
         * 可见展示次数(VCPM专用)
         */
        if (reportMetadata.getViewImpressions() != null) {
            cpcCommPageVo.setViewImpressions(reportMetadata.getViewImpressions());
        }
        //每笔订单花费
        if (reportMetadata.getCpa() != null) {
            cpcCommPageVo.setCpa(reportMetadata.getCpa().toString());
        }
        //本广告产品订单量
        if (reportMetadata.getAdSaleNum() != null) {
            cpcCommPageVo.setAdSaleNum(reportMetadata.getAdSaleNum());
        }
        //其他产品广告订单量
        if (reportMetadata.getAdOtherOrderNum() != null) {
            cpcCommPageVo.setAdOtherOrderNum(reportMetadata.getAdOtherOrderNum());
        }
        //本广告产品销售额
        if (reportMetadata.getAdSale() != null) {
            cpcCommPageVo.setAdSales(reportMetadata.getAdSale().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //其他产品广告销售额
        if (reportMetadata.getAdOtherSales() != null) {
            cpcCommPageVo.setAdOtherSales(reportMetadata.getAdOtherSales().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //CPC,VCPM广告销量
        if (reportMetadata.getOrderNum() != null) {
            cpcCommPageVo.setOrderNum(reportMetadata.getOrderNum());
        }
        //本广告产品销量
        if (reportMetadata.getAdOrderNum() != null) {
            cpcCommPageVo.setAdSelfSaleNum(reportMetadata.getAdOrderNum());
        }
        //其他产品广告销量
        if (reportMetadata.getAdOtherSaleNum() != null) {
            cpcCommPageVo.setAdOtherSaleNum(reportMetadata.getAdOtherSaleNum());
        }
        //VCPM(每千次展现费用,只有sd广告且是vcpm类型才展示)
        if (reportMetadata.getVcpm() != null) {
            cpcCommPageVo.setVcpm(reportMetadata.getVcpm().toString());
        }
        //“品牌新买家”订单量
        if (reportMetadata.getOrdersNewToBrand14d() != null) {
            cpcCommPageVo.setOrdersNewToBrandFTD(reportMetadata.getOrdersNewToBrand14d());
        }
        //“品牌新买家”订单百分比
        if (reportMetadata.getOrderRateNewToBrand14d() != null) {
            cpcCommPageVo.setOrderRateNewToBrandFTD(reportMetadata.getOrderRateNewToBrand14d().toString());
        }
        //“品牌新买家”销售额
        if (reportMetadata.getSalesNewToBrand14d() != null) {
            cpcCommPageVo.setSalesNewToBrandFTD(reportMetadata.getSalesNewToBrand14d().setScale(2, RoundingMode.HALF_UP).toString());
        }
        //“品牌新买家”销售额百分比
        if (reportMetadata.getSalesRateNewToBrand14d() != null) {
            cpcCommPageVo.setSalesRateNewToBrandFTD(reportMetadata.getSalesRateNewToBrand14d().toString());
        }
        //“品牌新买家”销量
        if (reportMetadata.getUnitsOrderedNewToBrand14d() != null) {
            cpcCommPageVo.setUnitsOrderedNewToBrandFTD(reportMetadata.getUnitsOrderedNewToBrand14d());
        }
        //“品牌新买家”销量百分比
        if (reportMetadata.getUnitsOrderedRateNewToBrand14d() != null) {
            cpcCommPageVo.setUnitsOrderedRateNewToBrandFTD(reportMetadata.getUnitsOrderedRateNewToBrand14d().toString());
        }
        //“品牌新买家”订单转化率
        if (reportMetadata.getOrdersNewToBrandPercentage14d() != null) {
            cpcCommPageVo.setOrdersNewToBrandPercentageFTD(reportMetadata.getOrdersNewToBrandPercentage14d().toString());
        }

        if (reportMetadata.getTopImpressionShare() != null) {
            cpcCommPageVo.setTopImpressionShare(reportMetadata.getTopImpressionShare());
        }

        cpcCommPageVo.setNewToBrandDetailPageViews(reportMetadata.getNewToBrandDetailPageViews());
        cpcCommPageVo.setAddToCart(reportMetadata.getAddToCart());
        cpcCommPageVo.setAddToCartRate(reportMetadata.getAddToCartRate());
        cpcCommPageVo.setECPAddToCart(reportMetadata.getECPAddToCart());
        cpcCommPageVo.setVideo5SecondViews(reportMetadata.getVideo5SecondViews());
        cpcCommPageVo.setVideo5SecondViewRate(reportMetadata.getVideo5SecondViewRate());
        cpcCommPageVo.setVideoFirstQuartileViews(reportMetadata.getVideoFirstQuartileViews());
        cpcCommPageVo.setVideoMidpointViews(reportMetadata.getVideoMidpointViews());
        cpcCommPageVo.setVideoThirdQuartileViews(reportMetadata.getVideoThirdQuartileViews());
        cpcCommPageVo.setVideoCompleteViews(reportMetadata.getVideoCompleteViews());
        cpcCommPageVo.setVideoUnmutes(reportMetadata.getVideoUnmutes());
        cpcCommPageVo.setViewabilityRate(reportMetadata.getViewabilityRate());
        cpcCommPageVo.setViewClickThroughRate(reportMetadata.getViewClickThroughRate());
        cpcCommPageVo.setBrandedSearches(reportMetadata.getBrandedSearches());
        cpcCommPageVo.setDetailPageViews(reportMetadata.getDetailPageViews());
        cpcCommPageVo.setCumulativeReach(reportMetadata.getCumulativeReach());
        cpcCommPageVo.setImpressionsFrequencyAverage(reportMetadata.getImpressionsFrequencyAverage());
        cpcCommPageVo.setAdvertisingUnitPrice(reportMetadata.getAdvertisingUnitPrice());
    }


    /**
     * 过滤活动高级筛选数据
     */
    public void filterCampaignAdvanceData(List<CampaignPageVo> voList, CampaignPageParam param) {
        if (CollectionUtils.isNotEmpty(voList)) {
            Iterator<CampaignPageVo> it = voList.iterator();
            CampaignPageVo vo;
            while (it.hasNext()) {
                vo = it.next();
                Integer orderNum = vo.getAdOrderNum() != null ? vo.getAdOrderNum() : 0;
                vo.setAdOrderNum(orderNum);
                Integer impressions = vo.getImpressions() != null ? vo.getImpressions() : 0;
                vo.setImpressions(impressions);
                Integer clicks = vo.getClicks() != null ? vo.getClicks() : 0;
                vo.setClicks(clicks);

                //展示量
                if (param.getImpressionsMin() != null) {
                    if (!(vo.getImpressions() >= param.getImpressionsMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getImpressionsMax() != null) {
                    if (!(vo.getImpressions() <= param.getImpressionsMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //点击量
                if (param.getClicksMin() != null) {
                    if (!(vo.getClicks() >= param.getClicksMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getClicksMax() != null) {
                    if (!(vo.getClicks() <= param.getClicksMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //点击率（clicks/impressions）
                if (param.getClickRateMin() != null) {
                    double a = vo.getCtr() != null ? Double.parseDouble(vo.getCtr()) : 0;
                    double b = param.getClickRateMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getClickRateMax() != null) {
                    double a = vo.getCtr() != null ? Double.parseDouble(vo.getCtr()) : 0;
                    double b = param.getClickRateMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //花费
                if (param.getCostMin() != null) {
                    double a = vo.getAdCost() != null ? Double.parseDouble(vo.getAdCost()) : 0;
                    double b = param.getCostMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getCostMax() != null) {
                    double a = vo.getAdCost() != null ? Double.parseDouble(vo.getAdCost()) : 0;
                    double b = param.getCostMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //cpc  平均点击费用
                if (param.getCpcMin() != null) {
                    double a = vo.getAdCostPerClick() != null ? Double.parseDouble(vo.getAdCostPerClick()) : 0;
                    double b = param.getCpcMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getCpcMax() != null) {
                    double a = vo.getAdCostPerClick() != null ? Double.parseDouble(vo.getAdCostPerClick()) : 0;
                    double b = param.getCpcMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //广告订单量
                if (param.getOrderNumMin() != null) {
                    if (!(vo.getAdOrderNum() >= param.getOrderNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getOrderNumMax() != null) {
                    if (!(vo.getAdOrderNum() <= param.getOrderNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //广告销售额
                if (param.getSalesMin() != null) {
                    double a = vo.getAdSale() != null ? Double.parseDouble(vo.getAdSale()) : 0;
                    double b = param.getSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getSalesMax() != null) {
                    double a = vo.getAdSale() != null ? Double.parseDouble(vo.getAdSale()) : 0;
                    double b = param.getSalesMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //订单转化率
                if (param.getSalesConversionRateMin() != null) {
                    double a = vo.getCvr() != null ? Double.parseDouble(vo.getCvr()) : 0;
                    double b = param.getSalesConversionRateMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getSalesConversionRateMax() != null) {
                    double a = vo.getCvr() != null ? Double.parseDouble(vo.getCvr()) : 0;
                    double b = param.getSalesConversionRateMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //acos
                if (param.getAcosMin() != null) {
                    double a = vo.getAcos() != null ? Double.parseDouble(vo.getAcos()) : 0;
                    double b = param.getAcosMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAcosMax() != null) {
                    double a = vo.getAcos() != null ? Double.parseDouble(vo.getAcos()) : 0;
                    double b = param.getAcosMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // roas
                if (param.getRoasMin() != null) {
                    double a = vo.getRoas() != null ? Double.parseDouble(vo.getRoas()) : 0;
                    double b = param.getRoasMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // roas
                if (param.getRoasMax() != null) {
                    double a = vo.getRoas() != null ? Double.parseDouble(vo.getRoas()) : 0;
                    double b = param.getRoasMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // acots  需要乘以店铺销售额
                if (param.getAcotsMin() != null) {
                    double a = vo.getAcots() != null ? Double.parseDouble(vo.getAcots()) : 0;
                    double b = param.getAcotsMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // acots  需要乘以店铺销售额
                if (param.getAcotsMax() != null) {
                    double a = vo.getAcots() != null ? Double.parseDouble(vo.getAcots()) : 0;
                    double b = param.getAcotsMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // asots 需要乘以店铺销售额
                if (param.getAsotsMin() != null) {
                    double a = vo.getAsots() != null ? Double.parseDouble(vo.getAsots()) : 0;
                    double b = param.getAsotsMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // asots  需要乘以店铺销售额
                if (param.getAsotsMax() != null) {
                    double a = vo.getAsots() != null ? Double.parseDouble(vo.getAsots()) : 0;
                    double b = param.getAsotsMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                /***********************************高级搜索新增查询指标字段**********************************/

                //本廣告产品销量过滤
                Integer adSelfSaleNum = vo.getAdSelfSaleNum() != null ? vo.getAdSelfSaleNum().intValue() : 0;
                if (param.getAdSelfSaleNumMin() != null) {
                    if (!(adSelfSaleNum >= param.getAdSelfSaleNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdSelfSaleNumMax() != null) {
                    if (!(adSelfSaleNum <= param.getAdSelfSaleNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                Integer viewImpressions = vo.getViewImpressions() != null ? vo.getViewImpressions().intValue() : 0;
                if (param.getViewImpressionsMin() != null) {
                    if (!(viewImpressions >= param.getViewImpressionsMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getViewImpressionsMax() != null) {
                    if (!(viewImpressions <= param.getViewImpressionsMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //cpa过滤
                if (param.getCpaMin() != null) {
                    double a = vo.getCpa() != null ? Double.parseDouble(vo.getCpa()) : 0;
                    double b = param.getCpaMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getCpaMax() != null) {
                    double a = vo.getCpa() != null ? Double.parseDouble(vo.getCpa()) : 0;
                    double b = param.getCpaMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //vcpm过滤
                if (param.getVcpmMin() != null) {
                    double a = vo.getVcpm() != null ? Double.parseDouble(vo.getVcpm()) : 0;
                    double b = param.getVcpmMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getVcpmMax() != null) {
                    double a = vo.getVcpm() != null ? Double.parseDouble(vo.getVcpm()) : 0;
                    double b = param.getVcpmMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //本广告产品订单量过滤
                Integer adSaleNum = vo.getAdSaleNum() != null ? vo.getAdSaleNum().intValue() : 0;
                if (param.getAdSaleNumMin() != null) {
                    if (!(adSaleNum >= param.getAdSaleNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdSaleNumMax() != null) {
                    if (!(adSaleNum <= param.getAdSaleNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //其他产品广告订单量过滤
                Integer adOtherOrderNum = vo.getAdOtherOrderNum() != null ? vo.getAdOtherOrderNum().intValue() : 0;
                if (param.getAdOtherOrderNumMin() != null) {
                    if (!(adOtherOrderNum >= param.getAdOtherOrderNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdOtherOrderNumMax() != null) {
                    if (!(adOtherOrderNum <= param.getAdOtherOrderNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //本广告产品销售额过滤
                if (param.getAdSalesMin() != null) {
                    double a = vo.getAdSales() != null ? Double.parseDouble(vo.getAdSales()) : 0;
                    double b = param.getAdSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdSalesMax() != null) {
                    double a = vo.getAdSales() != null ? Double.parseDouble(vo.getAdSales()) : 0;
                    double b = param.getAdSalesMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //其他产品广告销售额 过滤
                if (param.getAdOtherSalesMin() != null) {
                    double a = vo.getAdOtherSales() != null ? Double.parseDouble(vo.getAdOtherSales()) : 0;
                    double b = param.getAdOtherSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdOtherSalesMax() != null) {
                    double a = vo.getAdOtherSales() != null ? Double.parseDouble(vo.getAdOtherSales()) : 0;
                    double b = param.getAdOtherSalesMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //其他产品广告销量 过滤
                Integer adOtherSaleNum = vo.getAdOtherSaleNum() != null ? vo.getAdOtherSaleNum().intValue() : 0;
                if (param.getAdOtherSaleNumMin() != null) {
                    if (!(adOtherSaleNum >= param.getAdOtherSaleNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdOtherSaleNumMax() != null) {
                    if (!(adOtherSaleNum <= param.getAdOtherSaleNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”订单量过滤
                Integer ordersNewToBrandFTD = vo.getOrdersNewToBrandFTD() != null ? vo.getOrdersNewToBrandFTD().intValue() : 0;
                if (param.getOrdersNewToBrandFTDMin() != null) {
                    if (!(ordersNewToBrandFTD >= param.getOrdersNewToBrandFTDMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getOrdersNewToBrandFTDMax() != null) {
                    if (!(ordersNewToBrandFTD <= param.getOrdersNewToBrandFTDMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //“品牌新买家”订单百分比过滤
                if (param.getOrderRateNewToBrandFTDMin() != null) {
                    double a = vo.getOrderRateNewToBrandFTD() != null ? Double.parseDouble(vo.getOrderRateNewToBrandFTD()) : 0;
                    double b = param.getOrderRateNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getOrderRateNewToBrandFTDMax() != null) {
                    double a = vo.getOrderRateNewToBrandFTD() != null ? Double.parseDouble(vo.getOrderRateNewToBrandFTD()) : 0;
                    double b = param.getOrderRateNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”销售额 过滤
                if (param.getSalesNewToBrandFTDMin() != null) {
                    double a = vo.getSalesNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesNewToBrandFTD()) : 0;
                    double b = param.getSalesNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getSalesNewToBrandFTDMax() != null) {
                    double a = vo.getSalesNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesNewToBrandFTD()) : 0;
                    double b = param.getSalesNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”销售额百分比 过滤salesRateNewToBrandFTDMin
                if (param.getSalesRateNewToBrandFTDMin() != null) {
                    double a = vo.getSalesRateNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesRateNewToBrandFTD()) : 0;
                    double b = param.getSalesRateNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getSalesRateNewToBrandFTDMax() != null) {
                    double a = vo.getSalesRateNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesRateNewToBrandFTD()) : 0;
                    double b = param.getSalesRateNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”销量 过滤
                Integer unitsOrderedNewToBrandFTD = vo.getUnitsOrderedNewToBrandFTD() != null ? vo.getUnitsOrderedNewToBrandFTD().intValue() : 0;
                if (param.getUnitsOrderedNewToBrandFTDMin() != null) {
                    if (!(unitsOrderedNewToBrandFTD >= param.getUnitsOrderedNewToBrandFTDMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getUnitsOrderedNewToBrandFTDMax() != null) {
                    if (!(unitsOrderedNewToBrandFTD <= param.getUnitsOrderedNewToBrandFTDMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //“品牌新买家”销量百分比
                if (param.getUnitsOrderedRateNewToBrandFTDMin() != null) {
                    double a = vo.getUnitsOrderedRateNewToBrandFTD() != null ? Double.parseDouble(vo.getUnitsOrderedRateNewToBrandFTD()) : 0;
                    double b = param.getUnitsOrderedRateNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getUnitsOrderedRateNewToBrandFTDMax() != null) {
                    double a = vo.getUnitsOrderedRateNewToBrandFTD() != null ? Double.parseDouble(vo.getUnitsOrderedRateNewToBrandFTD()) : 0;
                    double b = param.getUnitsOrderedRateNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //广告销量筛选过滤
                Integer voAdSalesTotal = vo.getOrderNum() != null ? vo.getOrderNum().intValue() : 0;
                if (param.getAdSalesTotalMin() != null) {
                    if (!(voAdSalesTotal >= param.getAdSalesTotalMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdSalesTotalMax() != null) {
                    if (!(voAdSalesTotal <= param.getAdSalesTotalMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                // 加购次数 筛选
                Integer addToCart = Optional.ofNullable(vo.getAddToCart()).orElse(0);
                if (param.getAddToCartMin() != null && addToCart < param.getAddToCartMin()) {
                    it.remove();
                    continue;
                }
                if (param.getAddToCartMax() != null && addToCart > param.getAddToCartMax()) {
                    it.remove();
                    continue;
                }

                // 5秒观看次数 筛选
                Integer video5SecondViews = Optional.ofNullable(vo.getVideo5SecondViews()).orElse(0);
                if (param.getVideo5SecondViewsMin() != null && video5SecondViews < param.getVideo5SecondViewsMin()) {
                    it.remove();
                    continue;
                }
                if (param.getVideo5SecondViewsMax() != null && video5SecondViews > param.getVideo5SecondViewsMax()) {
                    it.remove();
                    continue;
                }

                // 视频完整播放次数 筛选
                Integer videoCompleteViews = Optional.ofNullable(vo.getVideoCompleteViews()).orElse(0);
                if (param.getVideoCompleteViewsMin() != null && videoCompleteViews < param.getVideoCompleteViewsMin()) {
                    it.remove();
                    continue;
                }
                if (param.getVideoCompleteViewsMax() != null && videoCompleteViews > param.getVideoCompleteViewsMax()) {
                    it.remove();
                    continue;
                }

                // 观看率 筛选
                BigDecimal viewabilityRate = Optional.ofNullable(vo.getViewabilityRate()).orElse(BigDecimal.ZERO);
                if (param.getViewabilityRateMin() != null && viewabilityRate.compareTo(param.getViewabilityRateMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getViewabilityRateMax() != null && viewabilityRate.compareTo(param.getViewabilityRateMax()) > 0) {
                    it.remove();
                    continue;
                }

                // 观看点击率 筛选
                BigDecimal viewClickThroughRate = Optional.ofNullable(vo.getViewClickThroughRate()).orElse(BigDecimal.ZERO);
                if (param.getViewClickThroughRateMin() != null && viewClickThroughRate.compareTo(param.getViewClickThroughRateMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getViewClickThroughRateMax() != null && viewClickThroughRate.compareTo(param.getViewClickThroughRateMax()) > 0) {
                    it.remove();
                    continue;
                }

                // 品牌搜索次数 筛选
                Integer brandedSearches = Optional.ofNullable(vo.getBrandedSearches()).orElse(0);
                if (param.getBrandedSearchesMin() != null && brandedSearches < param.getBrandedSearchesMin()) {
                    it.remove();
                    continue;
                }
                if (param.getBrandedSearchesMax() != null && brandedSearches > param.getBrandedSearchesMax()) {
                    it.remove();
                    continue;
                }

                // 累计触达用户 筛选
                Integer cumulativeReach = Optional.ofNullable(vo.getCumulativeReach()).orElse(0);
                if (param.getCumulativeReachMin() != null && cumulativeReach < param.getCumulativeReachMin()) {
                    it.remove();
                    continue;
                }
                if (param.getCumulativeReachMax() != null && cumulativeReach > param.getCumulativeReachMax()) {
                    it.remove();
                    continue;
                }

                // 平均触达次数 筛选
                BigDecimal impressionsFrequencyAverage = Optional.ofNullable(vo.getImpressionsFrequencyAverage()).orElse(BigDecimal.ZERO);
                if (param.getImpressionsFrequencyAverageMin() != null && impressionsFrequencyAverage.compareTo(param.getImpressionsFrequencyAverageMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getImpressionsFrequencyAverageMax() != null && impressionsFrequencyAverage.compareTo(param.getImpressionsFrequencyAverageMax()) > 0) {
                    it.remove();
                    continue;
                }

                // 广告笔单价 筛选
                BigDecimal advertisingUnitPrice = Optional.ofNullable(vo.getAdvertisingUnitPrice()).orElse(BigDecimal.ZERO);
                if (param.getAdvertisingUnitPriceMin() != null && advertisingUnitPrice.compareTo(param.getAdvertisingUnitPriceMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getAdvertisingUnitPriceMax() != null && advertisingUnitPrice.compareTo(param.getAdvertisingUnitPriceMax()) > 0) {
                    it.remove();
                    continue;
                }

                BigDecimal[] topImpressionShares = ReportParamUtil.unTopOfSearchImpressionShare(vo.getTopImpressionShare());
                if ((param.getTopImpressionShareMin() != null || param.getTopImpressionShareMax() != null)
                        && !ReportParamUtil.intersectionOrNot(topImpressionShares[0], topImpressionShares[1], param.getTopImpressionShareMin(), param.getTopImpressionShareMax())) {
                    it.remove();
                    continue;
                }
            }
        }
    }

    /**
     * 过滤广告组高级筛选数据
     */
    public void filterGroupAdvanceData(List<GroupPageVo> voList, GroupPageParam param) {
        if (CollectionUtils.isNotEmpty(voList)) {
            Iterator<GroupPageVo> it = voList.iterator();
            GroupPageVo vo;
            while (it.hasNext()) {
                vo = it.next();

                Integer orderNum = vo.getAdOrderNum() != null ? vo.getAdOrderNum() : 0;
                vo.setAdOrderNum(orderNum);
                Integer impressions = vo.getImpressions() != null ? vo.getImpressions() : 0;
                vo.setImpressions(impressions);
                Integer clicks = vo.getClicks() != null ? vo.getClicks() : 0;
                vo.setClicks(clicks);

                //展示量
                if (param.getImpressionsMin() != null) {
                    if (!(vo.getImpressions() >= param.getImpressionsMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getImpressionsMax() != null) {
                    if (!(vo.getImpressions() <= param.getImpressionsMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //点击量
                if (param.getClicksMin() != null) {
                    if (!(vo.getClicks() >= param.getClicksMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getClicksMax() != null) {
                    if (!(vo.getClicks() <= param.getClicksMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //点击率（clicks/impressions）
                if (param.getClickRateMin() != null) {
                    double a = vo.getCtr() != null ? Double.parseDouble(vo.getCtr()) : 0;
                    double b = param.getClickRateMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getClickRateMax() != null) {
                    double a = vo.getCtr() != null ? Double.parseDouble(vo.getCtr()) : 0;
                    double b = param.getClickRateMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //花费
                if (param.getCostMin() != null) {
                    double a = vo.getAdCost() != null ? Double.parseDouble(vo.getAdCost()) : 0;
                    double b = param.getCostMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getCostMax() != null) {
                    double a = vo.getAdCost() != null ? Double.parseDouble(vo.getAdCost()) : 0;
                    double b = param.getCostMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //cpc  平均点击费用
                if (param.getCpcMin() != null) {
                    double a = vo.getAdCostPerClick() != null ? Double.parseDouble(vo.getAdCostPerClick()) : 0;
                    double b = param.getCpcMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getCpcMax() != null) {
                    double a = vo.getAdCostPerClick() != null ? Double.parseDouble(vo.getAdCostPerClick()) : 0;
                    double b = param.getCpcMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //广告订单量
                if (param.getOrderNumMin() != null) {
                    if (!(vo.getAdOrderNum() >= param.getOrderNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getOrderNumMax() != null) {
                    if (!(vo.getAdOrderNum() <= param.getOrderNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //广告销售额
                if (param.getSalesMin() != null) {
                    double a = vo.getAdSale() != null ? Double.parseDouble(vo.getAdSale()) : 0;
                    double b = param.getSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getSalesMax() != null) {
                    double a = vo.getAdSale() != null ? Double.parseDouble(vo.getAdSale()) : 0;
                    double b = param.getSalesMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //订单转化率
                if (param.getSalesConversionRateMin() != null) {
                    double a = vo.getCvr() != null ? Double.parseDouble(vo.getCvr()) : 0;
                    double b = param.getSalesConversionRateMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getSalesConversionRateMax() != null) {
                    double a = vo.getCvr() != null ? Double.parseDouble(vo.getCvr()) : 0;
                    double b = param.getSalesConversionRateMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //acos
                if (param.getAcosMin() != null) {
                    double a = vo.getAcos() != null ? Double.parseDouble(vo.getAcos()) : 0;
                    double b = param.getAcosMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAcosMax() != null) {
                    double a = vo.getAcos() != null ? Double.parseDouble(vo.getAcos()) : 0;
                    double b = param.getAcosMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // roas
                if (param.getRoasMin() != null) {
                    double a = vo.getRoas() != null ? Double.parseDouble(vo.getRoas()) : 0;
                    double b = param.getRoasMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // roas
                if (param.getRoasMax() != null) {
                    double a = vo.getRoas() != null ? Double.parseDouble(vo.getRoas()) : 0;
                    double b = param.getRoasMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // acots  需要乘以店铺销售额
                if (param.getAcotsMin() != null) {
                    double a = vo.getAcots() != null ? Double.parseDouble(vo.getAcots()) : 0;
                    double b = param.getAcotsMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // acots  需要乘以店铺销售额
                if (param.getAcotsMax() != null) {
                    double a = vo.getAcots() != null ? Double.parseDouble(vo.getAcots()) : 0;
                    double b = param.getAcotsMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // asots 需要乘以店铺销售额
                if (param.getAsotsMin() != null) {
                    double a = vo.getAsots() != null ? Double.parseDouble(vo.getAsots()) : 0;
                    double b = param.getAsotsMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // asots  需要乘以店铺销售额
                if (param.getAsotsMax() != null) {
                    double a = vo.getAsots() != null ? Double.parseDouble(vo.getAsots()) : 0;
                    double b = param.getAsotsMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                /*******************广告管理高级筛选新增查询指标********************/
                //可见展示次数过滤
                Integer viewImpressions = vo.getViewImpressions() != null ? vo.getViewImpressions().intValue() : 0;
                if (param.getViewImpressionsMin() != null) {
                    if (!(viewImpressions >= param.getViewImpressionsMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getViewImpressionsMax() != null) {
                    if (!(viewImpressions <= param.getViewImpressionsMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //cpa过滤
                if (param.getCpaMin() != null) {
                    double a = vo.getCpa() != null ? Double.parseDouble(vo.getCpa()) : 0;
                    double b = param.getCpaMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getCpaMax() != null) {
                    double a = vo.getCpa() != null ? Double.parseDouble(vo.getCpa()) : 0;
                    double b = param.getCpaMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //vcpm过滤
                if (param.getVcpmMin() != null) {
                    double a = vo.getVcpm() != null ? Double.parseDouble(vo.getVcpm()) : 0;
                    double b = param.getVcpmMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                    if(!SBCampaignCostTypeEnum.VCPM.getCode().equals(vo.getCostType())){
                        it.remove();  //花费类型不是vcpm的过滤掉,过滤掉
                        continue;
                    }
                }

                if (param.getVcpmMax() != null) {
                    double a = vo.getVcpm() != null ? Double.parseDouble(vo.getVcpm()) : 0;
                    double b = param.getVcpmMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                    if(!SBCampaignCostTypeEnum.VCPM.getCode().equals(vo.getCostType())){
                        it.remove();  //花费类型不是vcpm的过滤掉,过滤掉
                        continue;
                    }
                }

                //本广告产品订单量过滤
                Integer adSaleNum = vo.getAdSaleNum() != null ? vo.getAdSaleNum().intValue() : 0;
                if (param.getAdSaleNumMin() != null) {
                    if (!(adSaleNum >= param.getAdSaleNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdSaleNumMax() != null) {
                    if (!(adSaleNum <= param.getAdSaleNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //其他产品广告订单量过滤
                Integer adOtherOrderNum = vo.getAdOtherOrderNum() != null ? vo.getAdOtherOrderNum().intValue() : 0;
                if (param.getAdOtherOrderNumMin() != null) {
                    if (!(adOtherOrderNum >= param.getAdOtherOrderNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdOtherOrderNumMax() != null) {
                    if (!(adOtherOrderNum <= param.getAdOtherOrderNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //本广告产品销售额过滤
                if (param.getAdSalesMin() != null) {
                    double a = vo.getAdSales() != null ? Double.parseDouble(vo.getAdSales()) : 0;
                    double b = param.getAdSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdSalesMax() != null) {
                    double a = vo.getAdSales() != null ? Double.parseDouble(vo.getAdSales()) : 0;
                    double b = param.getAdSalesMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //其他产品广告销售额 过滤
                if (param.getAdOtherSalesMin() != null) {
                    double a = vo.getAdOtherSales() != null ? Double.parseDouble(vo.getAdOtherSales()) : 0;
                    double b = param.getAdOtherSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdOtherSalesMax() != null) {
                    double a = vo.getAdOtherSales() != null ? Double.parseDouble(vo.getAdOtherSales()) : 0;
                    double b = param.getAdOtherSalesMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //本廣告产品销量过滤
                Integer adSelfSaleNum = vo.getAdSelfSaleNum() != null ? vo.getAdSelfSaleNum().intValue() : 0;
                if (param.getAdSelfSaleNumMin() != null) {
                    if (!(adSelfSaleNum >= param.getAdSelfSaleNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdSelfSaleNumMax() != null) {
                    if (!(adSelfSaleNum <= param.getAdSelfSaleNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //其他产品广告销量 过滤
                Integer adOtherSaleNum = vo.getAdOtherSaleNum() != null ? vo.getAdOtherSaleNum().intValue() : 0;
                if (param.getAdOtherSaleNumMin() != null) {
                    if (!(adOtherSaleNum >= param.getAdOtherSaleNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdOtherSaleNumMax() != null) {
                    if (!(adOtherSaleNum <= param.getAdOtherSaleNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”订单量过滤
                Integer ordersNewToBrandFTD = vo.getOrdersNewToBrandFTD() != null ? vo.getOrdersNewToBrandFTD().intValue() : 0;
                if (param.getOrdersNewToBrandFTDMin() != null) {
                    if (!(ordersNewToBrandFTD >= param.getOrdersNewToBrandFTDMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getOrdersNewToBrandFTDMax() != null) {
                    if (!(ordersNewToBrandFTD <= param.getOrdersNewToBrandFTDMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //“品牌新买家”订单百分比过滤
                if (param.getOrderRateNewToBrandFTDMin() != null) {
                    double a = vo.getOrderRateNewToBrandFTD() != null ? Double.parseDouble(vo.getOrderRateNewToBrandFTD()) : 0;
                    double b = param.getOrderRateNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getOrderRateNewToBrandFTDMax() != null) {
                    double a = vo.getOrderRateNewToBrandFTD() != null ? Double.parseDouble(vo.getOrderRateNewToBrandFTD()) : 0;
                    double b = param.getOrderRateNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”销售额 过滤
                if (param.getSalesNewToBrandFTDMin() != null) {
                    double a = vo.getSalesNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesNewToBrandFTD()) : 0;
                    double b = param.getSalesNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getSalesNewToBrandFTDMax() != null) {
                    double a = vo.getSalesNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesNewToBrandFTD()) : 0;
                    double b = param.getSalesNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”销售额百分比 过滤salesRateNewToBrandFTDMin
                if (param.getSalesRateNewToBrandFTDMin() != null) {
                    double a = vo.getSalesRateNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesRateNewToBrandFTD()) : 0;
                    double b = param.getSalesRateNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getSalesRateNewToBrandFTDMax() != null) {
                    double a = vo.getSalesRateNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesRateNewToBrandFTD()) : 0;
                    double b = param.getSalesRateNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”销量 过滤
                Integer unitsOrderedNewToBrandFTD = vo.getUnitsOrderedNewToBrandFTD() != null ? vo.getUnitsOrderedNewToBrandFTD().intValue() : 0;
                if (param.getUnitsOrderedNewToBrandFTDMin() != null) {
                    if (!(unitsOrderedNewToBrandFTD >= param.getUnitsOrderedNewToBrandFTDMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getUnitsOrderedNewToBrandFTDMax() != null) {
                    if (!(unitsOrderedNewToBrandFTD <= param.getUnitsOrderedNewToBrandFTDMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //“品牌新买家”销量百分比
                if (param.getUnitsOrderedRateNewToBrandFTDMin() != null) {
                    double a = vo.getUnitsOrderedRateNewToBrandFTD() != null ? Double.parseDouble(vo.getUnitsOrderedRateNewToBrandFTD()) : 0;
                    double b = param.getUnitsOrderedRateNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getUnitsOrderedRateNewToBrandFTDMax() != null) {
                    double a = vo.getUnitsOrderedRateNewToBrandFTD() != null ? Double.parseDouble(vo.getUnitsOrderedRateNewToBrandFTD()) : 0;
                    double b = param.getUnitsOrderedRateNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //广告销量筛选过滤
                Integer voAdSalesTotal = vo.getOrderNum() != null ? vo.getOrderNum().intValue() : 0;
                if (param.getAdSalesTotalMin() != null) {
                    if (!(voAdSalesTotal >= param.getAdSalesTotalMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdSalesTotalMax() != null) {
                    if (!(voAdSalesTotal <= param.getAdSalesTotalMax())) {
                        it.remove();  //不符合,过滤掉
                    }
                }

                // 加购次数 筛选
                Integer addToCart = Optional.ofNullable(vo.getAddToCart()).orElse(0);
                if (param.getAddToCartMin() != null && addToCart < param.getAddToCartMin()) {
                    it.remove();
                    continue;
                }
                if (param.getAddToCartMax() != null && addToCart > param.getAddToCartMax()) {
                    it.remove();
                    continue;
                }

                // 5秒观看次数 筛选
                Integer video5SecondViews = Optional.ofNullable(vo.getVideo5SecondViews()).orElse(0);
                if (param.getVideo5SecondViewsMin() != null && video5SecondViews < param.getVideo5SecondViewsMin()) {
                    it.remove();
                    continue;
                }
                if (param.getVideo5SecondViewsMax() != null && video5SecondViews > param.getVideo5SecondViewsMax()) {
                    it.remove();
                    continue;
                }

                // 视频完整播放次数 筛选
                Integer videoCompleteViews = Optional.ofNullable(vo.getVideoCompleteViews()).orElse(0);
                if (param.getVideoCompleteViewsMin() != null && videoCompleteViews < param.getVideoCompleteViewsMin()) {
                    it.remove();
                    continue;
                }
                if (param.getVideoCompleteViewsMax() != null && videoCompleteViews > param.getVideoCompleteViewsMax()) {
                    it.remove();
                    continue;
                }

                // 观看率 筛选
                BigDecimal viewabilityRate = Optional.ofNullable(vo.getViewabilityRate()).orElse(BigDecimal.ZERO);
                if (param.getViewabilityRateMin() != null && viewabilityRate.compareTo(param.getViewabilityRateMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getViewabilityRateMax() != null && viewabilityRate.compareTo(param.getViewabilityRateMax()) > 0) {
                    it.remove();
                    continue;
                }

                // 观看点击率 筛选
                BigDecimal viewClickThroughRate = Optional.ofNullable(vo.getViewClickThroughRate()).orElse(BigDecimal.ZERO);
                if (param.getViewClickThroughRateMin() != null && viewClickThroughRate.compareTo(param.getViewClickThroughRateMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getViewClickThroughRateMax() != null && viewClickThroughRate.compareTo(param.getViewClickThroughRateMax()) > 0) {
                    it.remove();
                    continue;
                }

                // 品牌搜索次数 筛选
                Integer brandedSearches = Optional.ofNullable(vo.getBrandedSearches()).orElse(0);
                if (param.getBrandedSearchesMin() != null && brandedSearches < param.getBrandedSearchesMin()) {
                    it.remove();
                    continue;
                }
                if (param.getBrandedSearchesMax() != null && brandedSearches > param.getBrandedSearchesMax()) {
                    it.remove();
                    continue;
                }

                // 累计触达用户 筛选
                Integer cumulativeReach = Optional.ofNullable(vo.getCumulativeReach()).orElse(0);
                if (param.getCumulativeReachMin() != null && cumulativeReach < param.getCumulativeReachMin()) {
                    it.remove();
                    continue;
                }
                if (param.getCumulativeReachMax() != null && cumulativeReach > param.getCumulativeReachMax()) {
                    it.remove();
                    continue;
                }

                // 平均触达次数 筛选
                BigDecimal impressionsFrequencyAverage = Optional.ofNullable(vo.getImpressionsFrequencyAverage()).orElse(BigDecimal.ZERO);
                if (param.getImpressionsFrequencyAverageMin() != null && impressionsFrequencyAverage.compareTo(param.getImpressionsFrequencyAverageMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getImpressionsFrequencyAverageMax() != null && impressionsFrequencyAverage.compareTo(param.getImpressionsFrequencyAverageMax()) > 0) {
                    it.remove();
                    continue;
                }

                // 广告笔单价 筛选
                BigDecimal advertisingUnitPrice = Optional.ofNullable(vo.getAdvertisingUnitPrice()).orElse(BigDecimal.ZERO);
                if (param.getAdvertisingUnitPriceMin() != null && advertisingUnitPrice.compareTo(param.getAdvertisingUnitPriceMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getAdvertisingUnitPriceMax() != null && advertisingUnitPrice.compareTo(param.getAdvertisingUnitPriceMax()) > 0) {
                    it.remove();
                    continue;
                }


            }
        }
    }

    /**
     * 过滤关键词高级筛选数据
     */
    public void filterKeywordAdVanceData(List<KeywordsPageVo> voList, KeywordsPageParam param) {
        if (CollectionUtils.isNotEmpty(voList)) {
            Iterator<KeywordsPageVo> it = voList.iterator();
            KeywordsPageVo vo;
            while (it.hasNext()) {
                vo = it.next();

                Integer orderNum = vo.getAdOrderNum() != null ? vo.getAdOrderNum() : 0;
                vo.setAdOrderNum(orderNum);
                Integer impressions = vo.getImpressions() != null ? vo.getImpressions() : 0;
                vo.setImpressions(impressions);
                Integer clicks = vo.getClicks() != null ? vo.getClicks() : 0;
                vo.setClicks(clicks);

                //展示量
                if (param.getImpressionsMin() != null) {
                    if (!(vo.getImpressions() >= param.getImpressionsMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getImpressionsMax() != null) {
                    if (!(vo.getImpressions() <= param.getImpressionsMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //点击量
                if (param.getClicksMin() != null) {
                    if (!(vo.getClicks() >= param.getClicksMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getClicksMax() != null) {
                    if (!(vo.getClicks() <= param.getClicksMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //点击率（clicks/impressions）
                if (param.getClickRateMin() != null) {
                    double a = vo.getCtr() != null ? Double.parseDouble(vo.getCtr()) : 0;
                    double b = param.getClickRateMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getClickRateMax() != null) {
                    double a = vo.getCtr() != null ? Double.parseDouble(vo.getCtr()) : 0;
                    double b = param.getClickRateMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //花费
                if (param.getCostMin() != null) {
                    double a = vo.getAdCost() != null ? Double.parseDouble(vo.getAdCost()) : 0;
                    double b = param.getCostMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getCostMax() != null) {
                    double a = vo.getAdCost() != null ? Double.parseDouble(vo.getAdCost()) : 0;
                    double b = param.getCostMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //cpc  平均点击费用
                if (param.getCpcMin() != null) {
                    double a = vo.getAdCostPerClick() != null ? Double.parseDouble(vo.getAdCostPerClick()) : 0;
                    double b = param.getCpcMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getCpcMax() != null) {
                    double a = vo.getAdCostPerClick() != null ? Double.parseDouble(vo.getAdCostPerClick()) : 0;
                    double b = param.getCpcMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //广告订单量
                if (param.getOrderNumMin() != null) {
                    if (!(vo.getAdOrderNum() >= param.getOrderNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getOrderNumMax() != null) {
                    if (!(vo.getAdOrderNum() <= param.getOrderNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //广告销售额
                if (param.getSalesMin() != null) {
                    double a = vo.getAdSale() != null ? Double.parseDouble(vo.getAdSale()) : 0;
                    double b = param.getSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getSalesMax() != null) {
                    double a = vo.getAdSale() != null ? Double.parseDouble(vo.getAdSale()) : 0;
                    double b = param.getSalesMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //订单转化率
                if (param.getSalesConversionRateMin() != null) {
                    double a = vo.getCvr() != null ? Double.parseDouble(vo.getCvr()) : 0;
                    double b = param.getSalesConversionRateMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getSalesConversionRateMax() != null) {
                    double a = vo.getCvr() != null ? Double.parseDouble(vo.getCvr()) : 0;
                    double b = param.getSalesConversionRateMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //acos
                if (param.getAcosMin() != null) {
                    double a = vo.getAcos() != null ? Double.parseDouble(vo.getAcos()) : 0;
                    double b = param.getAcosMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAcosMax() != null) {
                    double a = vo.getAcos() != null ? Double.parseDouble(vo.getAcos()) : 0;
                    double b = param.getAcosMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // roas
                if (param.getRoasMin() != null) {
                    double a = vo.getRoas() != null ? Double.parseDouble(vo.getRoas()) : 0;
                    double b = param.getRoasMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // roas
                if (param.getRoasMax() != null) {
                    double a = vo.getRoas() != null ? Double.parseDouble(vo.getRoas()) : 0;
                    double b = param.getRoasMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // acots  需要乘以店铺销售额
                if (param.getAcotsMin() != null) {
                    double a = vo.getAcots() != null ? Double.parseDouble(vo.getAcots()) : 0;
                    double b = param.getAcotsMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // acots  需要乘以店铺销售额
                if (param.getAcotsMax() != null) {
                    double a = vo.getAcots() != null ? Double.parseDouble(vo.getAcots()) : 0;
                    double b = param.getAcotsMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // asots 需要乘以店铺销售额
                if (param.getAsotsMin() != null) {
                    double a = vo.getAsots() != null ? Double.parseDouble(vo.getAsots()) : 0;
                    double b = param.getAsotsMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // asots  需要乘以店铺销售额
                if (param.getAsotsMax() != null) {
                    double a = vo.getAsots() != null ? Double.parseDouble(vo.getAsots()) : 0;
                    double b = param.getAsotsMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                /**********************************高级搜索新增查询指标***************************************/
                Integer adSelfSaleNum = vo.getAdSelfSaleNum() != null ? vo.getAdSelfSaleNum().intValue() : 0;
                if (param.getAdSelfSaleNumMin() != null) {
                    if (!(adSelfSaleNum >= param.getAdSelfSaleNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdSelfSaleNumMax() != null) {
                    if (!(adSelfSaleNum <= param.getAdSelfSaleNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                Integer viewImpressions = vo.getViewImpressions() != null ? vo.getViewImpressions().intValue() : 0;
                if (param.getViewImpressionsMin() != null) {
                    if (!(viewImpressions >= param.getViewImpressionsMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getViewImpressionsMax() != null) {
                    if (!(viewImpressions <= param.getViewImpressionsMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //cpa过滤
                if (param.getCpaMin() != null) {
                    double a = vo.getCpa() != null ? Double.parseDouble(vo.getCpa()) : 0;
                    double b = param.getCpaMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getCpaMax() != null) {
                    double a = vo.getCpa() != null ? Double.parseDouble(vo.getCpa()) : 0;
                    double b = param.getCpaMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //vcpm过滤
                if (param.getVcpmMin() != null) {
                    double a = vo.getVcpm() != null ? Double.parseDouble(vo.getVcpm()) : 0;
                    double b = param.getVcpmMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getVcpmMax() != null) {
                    double a = vo.getVcpm() != null ? Double.parseDouble(vo.getVcpm()) : 0;
                    double b = param.getVcpmMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //本广告产品订单量过滤
                Integer adSaleNum = vo.getAdSaleNum() != null ? vo.getAdSaleNum().intValue() : 0;
                if (param.getAdSaleNumMin() != null) {
                    if (!(adSaleNum >= param.getAdSaleNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdSaleNumMax() != null) {
                    if (!(adSaleNum <= param.getAdSaleNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //其他产品广告订单量过滤
                Integer adOtherOrderNum = vo.getAdOtherOrderNum() != null ? vo.getAdOtherOrderNum().intValue() : 0;
                if (param.getAdOtherOrderNumMin() != null) {
                    if (!(adOtherOrderNum >= param.getAdOtherOrderNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdOtherOrderNumMax() != null) {
                    if (!(adOtherOrderNum <= param.getAdOtherOrderNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //本广告产品销售额过滤
                if (param.getAdSalesMin() != null) {
                    double a = vo.getAdSales() != null ? Double.parseDouble(vo.getAdSales()) : 0;
                    double b = param.getAdSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdSalesMax() != null) {
                    double a = vo.getAdSales() != null ? Double.parseDouble(vo.getAdSales()) : 0;
                    double b = param.getAdSalesMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //其他产品广告销售额 过滤
                if (param.getAdOtherSalesMin() != null) {
                    double a = vo.getAdOtherSales() != null ? Double.parseDouble(vo.getAdOtherSales()) : 0;
                    double b = param.getAdOtherSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdOtherSalesMax() != null) {
                    double a = vo.getAdOtherSales() != null ? Double.parseDouble(vo.getAdOtherSales()) : 0;
                    double b = param.getAdOtherSalesMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //其他产品广告销量 过滤
                Integer adOtherSaleNum = vo.getAdOtherSaleNum() != null ? vo.getAdOtherSaleNum().intValue() : 0;
                if (param.getAdOtherSaleNumMin() != null) {
                    if (!(adOtherSaleNum >= param.getAdOtherSaleNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdOtherSaleNumMax() != null) {
                    if (!(adOtherSaleNum <= param.getAdOtherSaleNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”订单量过滤
                Integer ordersNewToBrandFTD = vo.getOrdersNewToBrandFTD() != null ? vo.getOrdersNewToBrandFTD().intValue() : 0;
                if (param.getOrdersNewToBrandFTDMin() != null) {
                    if (!(ordersNewToBrandFTD >= param.getOrdersNewToBrandFTDMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getOrdersNewToBrandFTDMax() != null) {
                    if (!(ordersNewToBrandFTD <= param.getOrdersNewToBrandFTDMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //“品牌新买家”订单百分比过滤
                if (param.getOrderRateNewToBrandFTDMin() != null) {
                    double a = vo.getOrderRateNewToBrandFTD() != null ? Double.parseDouble(vo.getOrderRateNewToBrandFTD()) : 0;
                    double b = param.getOrderRateNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getOrderRateNewToBrandFTDMax() != null) {
                    double a = vo.getOrderRateNewToBrandFTD() != null ? Double.parseDouble(vo.getOrderRateNewToBrandFTD()) : 0;
                    double b = param.getOrderRateNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”销售额 过滤
                if (param.getSalesNewToBrandFTDMin() != null) {
                    double a = vo.getSalesNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesNewToBrandFTD()) : 0;
                    double b = param.getSalesNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getSalesNewToBrandFTDMax() != null) {
                    double a = vo.getSalesNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesNewToBrandFTD()) : 0;
                    double b = param.getSalesNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”销售额百分比 过滤salesRateNewToBrandFTDMin
                if (param.getSalesRateNewToBrandFTDMin() != null) {
                    double a = vo.getSalesRateNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesRateNewToBrandFTD()) : 0;
                    double b = param.getSalesRateNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getSalesRateNewToBrandFTDMax() != null) {
                    double a = vo.getSalesRateNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesRateNewToBrandFTD()) : 0;
                    double b = param.getSalesRateNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”销量 过滤
                Integer unitsOrderedNewToBrandFTD = vo.getUnitsOrderedNewToBrandFTD() != null ? vo.getUnitsOrderedNewToBrandFTD().intValue() : 0;
                if (param.getUnitsOrderedNewToBrandFTDMin() != null) {
                    if (!(unitsOrderedNewToBrandFTD >= param.getUnitsOrderedNewToBrandFTDMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getUnitsOrderedNewToBrandFTDMax() != null) {
                    if (!(unitsOrderedNewToBrandFTD <= param.getUnitsOrderedNewToBrandFTDMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //“品牌新买家”销量百分比
                if (param.getUnitsOrderedRateNewToBrandFTDMin() != null) {
                    double a = vo.getUnitsOrderedRateNewToBrandFTD() != null ? Double.parseDouble(vo.getUnitsOrderedRateNewToBrandFTD()) : 0;
                    double b = param.getUnitsOrderedRateNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getUnitsOrderedRateNewToBrandFTDMax() != null) {
                    double a = vo.getUnitsOrderedRateNewToBrandFTD() != null ? Double.parseDouble(vo.getUnitsOrderedRateNewToBrandFTD()) : 0;
                    double b = param.getUnitsOrderedRateNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //广告销量筛选过滤
                Integer voAdSalesTotal = vo.getOrderNum() != null ? vo.getOrderNum().intValue() : 0;
                if (param.getAdSalesTotalMin() != null) {
                    if (!(voAdSalesTotal >= param.getAdSalesTotalMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdSalesTotalMax() != null) {
                    if (!(voAdSalesTotal <= param.getAdSalesTotalMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”订单转化率 ordersNewToBrandPercentageFTD
                if (param.getBrandNewBuyerOrderConversionRateMin() != null) {
                    double a = vo.getOrdersNewToBrandPercentageFTD() != null ? Double.parseDouble(vo.getOrdersNewToBrandPercentageFTD()) : 0;
                    double b = param.getBrandNewBuyerOrderConversionRateMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getBrandNewBuyerOrderConversionRateMax() != null) {
                    double a = vo.getOrdersNewToBrandPercentageFTD() != null ? Double.parseDouble(vo.getOrdersNewToBrandPercentageFTD()) : 0;
                    double b = param.getBrandNewBuyerOrderConversionRateMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                    }
                }


                // 5秒观看次数 筛选
                Integer video5SecondViews = Optional.ofNullable(vo.getVideo5SecondViews()).orElse(0);
                if (param.getVideo5SecondViewsMin() != null && video5SecondViews < param.getVideo5SecondViewsMin()) {
                    it.remove();
                    continue;
                }
                if (param.getVideo5SecondViewsMax() != null && video5SecondViews > param.getVideo5SecondViewsMax()) {
                    it.remove();
                    continue;
                }

                // 视频完整播放次数 筛选
                Integer videoCompleteViews = Optional.ofNullable(vo.getVideoCompleteViews()).orElse(0);
                if (param.getVideoCompleteViewsMin() != null && videoCompleteViews < param.getVideoCompleteViewsMin()) {
                    it.remove();
                    continue;
                }
                if (param.getVideoCompleteViewsMax() != null && videoCompleteViews > param.getVideoCompleteViewsMax()) {
                    it.remove();
                    continue;
                }

                // 观看率 筛选
                BigDecimal viewabilityRate = Optional.ofNullable(vo.getViewabilityRate()).orElse(BigDecimal.ZERO);
                if (param.getViewabilityRateMin() != null && viewabilityRate.compareTo(param.getViewabilityRateMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getViewabilityRateMax() != null && viewabilityRate.compareTo(param.getViewabilityRateMax()) > 0) {
                    it.remove();
                    continue;
                }

                // 观看点击率 筛选
                BigDecimal viewClickThroughRate = Optional.ofNullable(vo.getViewClickThroughRate()).orElse(BigDecimal.ZERO);
                if (param.getViewClickThroughRateMin() != null && viewClickThroughRate.compareTo(param.getViewClickThroughRateMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getViewClickThroughRateMax() != null && viewClickThroughRate.compareTo(param.getViewClickThroughRateMax()) > 0) {
                    it.remove();
                    continue;
                }

                // 品牌搜索次数 筛选
                Integer brandedSearches = Optional.ofNullable(vo.getBrandedSearches()).orElse(0);
                if (param.getBrandedSearchesMin() != null && brandedSearches < param.getBrandedSearchesMin()) {
                    it.remove();
                    continue;
                }
                if (param.getBrandedSearchesMax() != null && brandedSearches > param.getBrandedSearchesMax()) {
                    it.remove();
                    continue;
                }

                // 广告笔单价 筛选
                BigDecimal advertisingUnitPrice = Optional.ofNullable(vo.getAdvertisingUnitPrice()).orElse(BigDecimal.ZERO);
                if (param.getAdvertisingUnitPriceMin() != null && advertisingUnitPrice.compareTo(param.getAdvertisingUnitPriceMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getAdvertisingUnitPriceMax() != null && advertisingUnitPrice.compareTo(param.getAdvertisingUnitPriceMax()) > 0) {
                    it.remove();
                    continue;
                }

                BigDecimal[] topImpressionShares = ReportParamUtil.unTopOfSearchImpressionShare(vo.getTopImpressionShare());
                if ((param.getTopImpressionShareMin() != null || param.getTopImpressionShareMax() != null)
                        && !ReportParamUtil.intersectionOrNot(topImpressionShares[0], topImpressionShares[1], param.getTopImpressionShareMin(), param.getTopImpressionShareMax())) {
                    it.remove();
                    continue;
                }

                // 搜索词排名筛选
                Integer searchFrequencyRank = Optional.ofNullable(vo.getSearchFrequencyRank()).orElse(Integer.MAX_VALUE);
                if (param.getSearchFrequencyRankMax() != null && searchFrequencyRank > param.getSearchFrequencyRankMax()) {
                    it.remove();
                    continue;
                }
                if (param.getSearchFrequencyRankMin() != null && (searchFrequencyRank < param.getSearchFrequencyRankMin() || param.getSearchFrequencyRankMax() == null && searchFrequencyRank.equals(Integer.MAX_VALUE))) {
                    it.remove();
                    continue;
                }
                BigDecimal weekRatio = Optional.ofNullable(vo.getWeekRatio()).orElse(new BigDecimal(Integer.MIN_VALUE)).setScale(2, RoundingMode.HALF_UP);
                if (param.getWeekRatioMax() != null && (weekRatio.compareTo(param.getWeekRatioMax()) > 0 || param.getWeekRatioMin() == null && weekRatio.intValue() == Integer.MIN_VALUE)) {
                    it.remove();
                    continue;
                }
                if (param.getWeekRatioMin() != null && weekRatio.compareTo(param.getWeekRatioMin()) < 0) {
                    it.remove();
                    continue;
                }
            }
        }
    }

    /**
     * 过滤投放高级筛选数据
     */
    public void filterTargetAdVanceData(List<TargetingPageVo> voList, TargetingPageParam param) {
        if (CollectionUtils.isNotEmpty(voList)) {
            Iterator<TargetingPageVo> it = voList.iterator();
            TargetingPageVo vo;
            while (it.hasNext()) {
                vo = it.next();

                Integer orderNum = vo.getAdOrderNum() != null ? vo.getAdOrderNum() : 0;
                vo.setAdOrderNum(orderNum);
                Integer impressions = vo.getImpressions() != null ? vo.getImpressions() : 0;
                vo.setImpressions(impressions);
                Integer clicks = vo.getClicks() != null ? vo.getClicks() : 0;
                vo.setClicks(clicks);

                //展示量
                if (param.getImpressionsMin() != null) {
                    if (!(vo.getImpressions() >= param.getImpressionsMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getImpressionsMax() != null) {
                    if (!(vo.getImpressions() <= param.getImpressionsMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //点击量
                if (param.getClicksMin() != null) {
                    if (!(vo.getClicks() >= param.getClicksMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getClicksMax() != null) {
                    if (!(vo.getClicks() <= param.getClicksMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //点击率（clicks/impressions）
                if (param.getClickRateMin() != null) {
                    double a = vo.getCtr() != null ? Double.parseDouble(vo.getCtr()) : 0;
                    double b = param.getClickRateMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getClickRateMax() != null) {
                    double a = vo.getCtr() != null ? Double.parseDouble(vo.getCtr()) : 0;
                    double b = param.getClickRateMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //花费
                if (param.getCostMin() != null) {
                    double a = vo.getAdCost() != null ? Double.parseDouble(vo.getAdCost()) : 0;
                    double b = param.getCostMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getCostMax() != null) {
                    double a = vo.getAdCost() != null ? Double.parseDouble(vo.getAdCost()) : 0;
                    double b = param.getCostMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //cpc  平均点击费用
                if (param.getCpcMin() != null) {
                    double a = vo.getAdCostPerClick() != null ? Double.parseDouble(vo.getAdCostPerClick()) : 0;
                    double b = param.getCpcMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getCpcMax() != null) {
                    double a = vo.getAdCostPerClick() != null ? Double.parseDouble(vo.getAdCostPerClick()) : 0;
                    double b = param.getCpcMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //广告订单量
                if (param.getOrderNumMin() != null) {
                    if (!(vo.getAdOrderNum() >= param.getOrderNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getOrderNumMax() != null) {
                    if (!(vo.getAdOrderNum() <= param.getOrderNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //广告销售额
                if (param.getSalesMin() != null) {
                    double a = vo.getAdSale() != null ? Double.parseDouble(vo.getAdSale()) : 0;
                    double b = param.getSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getSalesMax() != null) {
                    double a = vo.getAdSale() != null ? Double.parseDouble(vo.getAdSale()) : 0;
                    double b = param.getSalesMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //订单转化率
                if (param.getSalesConversionRateMin() != null) {
                    double a = vo.getCvr() != null ? Double.parseDouble(vo.getCvr()) : 0;
                    double b = param.getSalesConversionRateMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getSalesConversionRateMax() != null) {
                    double a = vo.getCvr() != null ? Double.parseDouble(vo.getCvr()) : 0;
                    double b = param.getSalesConversionRateMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //acos
                if (param.getAcosMin() != null) {
                    double a = vo.getAcos() != null ? Double.parseDouble(vo.getAcos()) : 0;
                    double b = param.getAcosMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAcosMax() != null) {
                    double a = vo.getAcos() != null ? Double.parseDouble(vo.getAcos()) : 0;
                    double b = param.getAcosMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // roas
                if (param.getRoasMin() != null) {
                    double a = vo.getRoas() != null ? Double.parseDouble(vo.getRoas()) : 0;
                    double b = param.getRoasMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // roas
                if (param.getRoasMax() != null) {
                    double a = vo.getRoas() != null ? Double.parseDouble(vo.getRoas()) : 0;
                    double b = param.getRoasMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // acots  需要乘以店铺销售额
                if (param.getAcotsMin() != null) {
                    double a = vo.getAcots() != null ? Double.parseDouble(vo.getAcots()) : 0;
                    double b = param.getAcotsMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // acots  需要乘以店铺销售额
                if (param.getAcotsMax() != null) {
                    double a = vo.getAcots() != null ? Double.parseDouble(vo.getAcots()) : 0;
                    double b = param.getAcotsMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // asots 需要乘以店铺销售额
                if (param.getAsotsMin() != null) {
                    double a = vo.getAsots() != null ? Double.parseDouble(vo.getAsots()) : 0;
                    double b = param.getAsotsMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // asots  需要乘以店铺销售额
                if (param.getAsotsMax() != null) {
                    double a = vo.getAsots() != null ? Double.parseDouble(vo.getAsots()) : 0;
                    double b = param.getAsotsMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                /********************广告投放高级筛选新增指标***************************/
                //本廣告产品销量过滤
                Integer adSelfSaleNum = vo.getAdSelfSaleNum() != null ? vo.getAdSelfSaleNum().intValue() : 0;
                if (param.getAdSelfSaleNumMin() != null) {
                    if (!(adSelfSaleNum >= param.getAdSelfSaleNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdSelfSaleNumMax() != null) {
                    if (!(adSelfSaleNum <= param.getAdSelfSaleNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                Integer viewImpressions = vo.getViewImpressions() != null ? vo.getViewImpressions().intValue() : 0;
                if (param.getViewImpressionsMin() != null) {
                    if (!(viewImpressions >= param.getViewImpressionsMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getViewImpressionsMax() != null) {
                    if (!(viewImpressions <= param.getViewImpressionsMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //cpa过滤
                if (param.getCpaMin() != null) {
                    double a = vo.getCpa() != null ? Double.parseDouble(vo.getCpa()) : 0;
                    double b = param.getCpaMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getCpaMax() != null) {
                    double a = vo.getCpa() != null ? Double.parseDouble(vo.getCpa()) : 0;
                    double b = param.getCpaMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //vcpm过滤
                if (param.getVcpmMin() != null) {
                    double a = vo.getVcpm() != null ? Double.parseDouble(vo.getVcpm()) : 0;
                    double b = param.getVcpmMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                    if(!SBCampaignCostTypeEnum.VCPM.getCode().equals(vo.getCostType())){
                        it.remove();  //花费类型不是vcpm的过滤掉,过滤掉
                        continue;
                    }
                }

                if (param.getVcpmMax() != null) {
                    double a = vo.getVcpm() != null ? Double.parseDouble(vo.getVcpm()) : 0;
                    double b = param.getVcpmMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                    if(!SBCampaignCostTypeEnum.VCPM.getCode().equals(vo.getCostType())){
                        it.remove();  //花费类型不是vcpm的过滤掉,过滤掉
                        continue;
                    }
                }

                //本广告产品订单量过滤
                Integer adSaleNum = vo.getAdSaleNum() != null ? vo.getAdSaleNum().intValue() : 0;
                if (param.getAdSaleNumMin() != null) {
                    if (!(adSaleNum >= param.getAdSaleNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdSaleNumMax() != null) {
                    if (!(adSaleNum <= param.getAdSaleNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //其他产品广告订单量过滤
                Integer adOtherOrderNum = vo.getAdOtherOrderNum() != null ? vo.getAdOtherOrderNum().intValue() : 0;
                if (param.getAdOtherOrderNumMin() != null) {
                    if (!(adOtherOrderNum >= param.getAdOtherOrderNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdOtherOrderNumMax() != null) {
                    if (!(adOtherOrderNum <= param.getAdOtherOrderNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //本广告产品销售额过滤
                if (param.getAdSalesMin() != null) {
                    double a = vo.getAdSales() != null ? Double.parseDouble(vo.getAdSales()) : 0;
                    double b = param.getAdSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdSalesMax() != null) {
                    double a = vo.getAdSales() != null ? Double.parseDouble(vo.getAdSales()) : 0;
                    double b = param.getAdSalesMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //其他产品广告销售额 过滤
                if (param.getAdOtherSalesMin() != null) {
                    double a = vo.getAdOtherSales() != null ? Double.parseDouble(vo.getAdOtherSales()) : 0;
                    double b = param.getAdOtherSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdOtherSalesMax() != null) {
                    double a = vo.getAdOtherSales() != null ? Double.parseDouble(vo.getAdOtherSales()) : 0;
                    double b = param.getAdOtherSalesMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //其他产品广告销量 过滤
                Integer adOtherSaleNum = vo.getAdOtherSaleNum() != null ? vo.getAdOtherSaleNum().intValue() : 0;
                if (param.getAdOtherSaleNumMin() != null) {
                    if (!(adOtherSaleNum >= param.getAdOtherSaleNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdOtherSaleNumMax() != null) {
                    if (!(adOtherSaleNum <= param.getAdOtherSaleNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”订单量过滤
                Integer ordersNewToBrandFTD = vo.getOrdersNewToBrandFTD() != null ? vo.getOrdersNewToBrandFTD().intValue() : 0;
                if (param.getOrdersNewToBrandFTDMin() != null) {
                    if (!(ordersNewToBrandFTD >= param.getOrdersNewToBrandFTDMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getOrdersNewToBrandFTDMax() != null) {
                    if (!(ordersNewToBrandFTD <= param.getOrdersNewToBrandFTDMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //“品牌新买家”订单百分比过滤
                if (param.getOrderRateNewToBrandFTDMin() != null) {
                    double a = vo.getOrderRateNewToBrandFTD() != null ? Double.parseDouble(vo.getOrderRateNewToBrandFTD()) : 0;
                    double b = MathUtil.multiplyZero(param.getOrderRateNewToBrandFTDMin(), BigDecimal.valueOf(100)).doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getOrderRateNewToBrandFTDMax() != null) {
                    double a = vo.getOrderRateNewToBrandFTD() != null ? Double.parseDouble(vo.getOrderRateNewToBrandFTD()) : 0;
                    double b = MathUtil.multiplyZero(param.getOrderRateNewToBrandFTDMax(), BigDecimal.valueOf(100)).doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”销售额 过滤
                if (param.getSalesNewToBrandFTDMin() != null) {
                    double a = vo.getSalesNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesNewToBrandFTD()) : 0;
                    double b = param.getSalesNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getSalesNewToBrandFTDMax() != null) {
                    double a = vo.getSalesNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesNewToBrandFTD()) : 0;
                    double b = param.getSalesNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”销售额百分比 过滤salesRateNewToBrandFTDMin
                if (param.getSalesRateNewToBrandFTDMin() != null) {
                    double a = vo.getSalesRateNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesRateNewToBrandFTD()) : 0;
                    double b = MathUtil.multiplyZero(param.getSalesRateNewToBrandFTDMin(), BigDecimal.valueOf(100)).doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getSalesRateNewToBrandFTDMax() != null) {
                    double a = vo.getSalesRateNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesRateNewToBrandFTD()) : 0;
                    double b = MathUtil.multiplyZero(param.getSalesRateNewToBrandFTDMax(), BigDecimal.valueOf(100)).doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”销量 过滤
                Integer unitsOrderedNewToBrandFTD = vo.getUnitsOrderedNewToBrandFTD() != null ? vo.getUnitsOrderedNewToBrandFTD().intValue() : 0;
                if (param.getUnitsOrderedNewToBrandFTDMin() != null) {
                    if (!(unitsOrderedNewToBrandFTD >= param.getUnitsOrderedNewToBrandFTDMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getUnitsOrderedNewToBrandFTDMax() != null) {
                    if (!(unitsOrderedNewToBrandFTD <= param.getUnitsOrderedNewToBrandFTDMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //“品牌新买家”销量百分比
                if (param.getUnitsOrderedRateNewToBrandFTDMin() != null) {
                    double a = vo.getUnitsOrderedRateNewToBrandFTD() != null ? Double.parseDouble(vo.getUnitsOrderedRateNewToBrandFTD()) : 0;
                    double b = param.getUnitsOrderedRateNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getUnitsOrderedRateNewToBrandFTDMax() != null) {
                    double a = vo.getUnitsOrderedRateNewToBrandFTD() != null ? Double.parseDouble(vo.getUnitsOrderedRateNewToBrandFTD()) : 0;
                    double b = param.getUnitsOrderedRateNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //品牌新买家订单转化率
                if (param.getBrandNewBuyerOrderConversionRateMin() != null) {
                    double a = vo.getOrdersNewToBrandPercentageFTD() != null ? Double.parseDouble(vo.getOrdersNewToBrandPercentageFTD()) : 0;
                    double b = MathUtil.multiplyZero(param.getBrandNewBuyerOrderConversionRateMin(), BigDecimal.valueOf(100)).doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getBrandNewBuyerOrderConversionRateMax() != null) {
                    double a = vo.getOrdersNewToBrandPercentageFTD() != null ? Double.parseDouble(vo.getOrdersNewToBrandPercentageFTD()) : 0;
                    double b = MathUtil.multiplyZero(param.getBrandNewBuyerOrderConversionRateMax(), BigDecimal.valueOf(100)).doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //广告销量
                Integer voAdSalesTotal = vo.getOrderNum() != null ? vo.getOrderNum().intValue() : 0;
                if (param.getAdSalesTotalMin() != null) {
                    if (!(voAdSalesTotal >= param.getAdSalesTotalMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdSalesTotalMax() != null) {
                    if (!(voAdSalesTotal <= param.getAdSalesTotalMax())) {
                        it.remove();  //不符合,过滤掉
                    }
                }


                // 加购次数 筛选
                Integer addToCart = Optional.ofNullable(vo.getAddToCart()).orElse(0);
                if (param.getAddToCartMin() != null && addToCart < param.getAddToCartMin()) {
                    it.remove();
                    continue;
                }
                if (param.getAddToCartMax() != null && addToCart > param.getAddToCartMax()) {
                    it.remove();
                    continue;
                }

                // 5秒观看次数 筛选
                Integer video5SecondViews = Optional.ofNullable(vo.getVideo5SecondViews()).orElse(0);
                if (param.getVideo5SecondViewsMin() != null && video5SecondViews < param.getVideo5SecondViewsMin()) {
                    it.remove();
                    continue;
                }
                if (param.getVideo5SecondViewsMax() != null && video5SecondViews > param.getVideo5SecondViewsMax()) {
                    it.remove();
                    continue;
                }

                // 视频完整播放次数 筛选
                Integer videoCompleteViews = Optional.ofNullable(vo.getVideoCompleteViews()).orElse(0);
                if (param.getVideoCompleteViewsMin() != null && videoCompleteViews < param.getVideoCompleteViewsMin()) {
                    it.remove();
                    continue;
                }
                if (param.getVideoCompleteViewsMax() != null && videoCompleteViews > param.getVideoCompleteViewsMax()) {
                    it.remove();
                    continue;
                }

                // 观看率 筛选
                BigDecimal viewabilityRate = Optional.ofNullable(vo.getViewabilityRate()).orElse(BigDecimal.ZERO);
                if (param.getViewabilityRateMin() != null && viewabilityRate.compareTo(param.getViewabilityRateMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getViewabilityRateMax() != null && viewabilityRate.compareTo(param.getViewabilityRateMax()) > 0) {
                    it.remove();
                    continue;
                }

                // 观看点击率 筛选
                BigDecimal viewClickThroughRate = Optional.ofNullable(vo.getViewClickThroughRate()).orElse(BigDecimal.ZERO);
                if (param.getViewClickThroughRateMin() != null && viewClickThroughRate.compareTo(param.getViewClickThroughRateMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getViewClickThroughRateMax() != null && viewClickThroughRate.compareTo(param.getViewClickThroughRateMax()) > 0) {
                    it.remove();
                    continue;
                }

                // 品牌搜索次数 筛选
                Integer brandedSearches = Optional.ofNullable(vo.getBrandedSearches()).orElse(0);
                if (param.getBrandedSearchesMin() != null && brandedSearches < param.getBrandedSearchesMin()) {
                    it.remove();
                    continue;
                }
                if (param.getBrandedSearchesMax() != null && brandedSearches > param.getBrandedSearchesMax()) {
                    it.remove();
                    continue;
                }

                // 广告笔单价 筛选
                BigDecimal advertisingUnitPrice = Optional.ofNullable(vo.getAdvertisingUnitPrice()).orElse(BigDecimal.ZERO);
                if (param.getAdvertisingUnitPriceMin() != null && advertisingUnitPrice.compareTo(param.getAdvertisingUnitPriceMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getAdvertisingUnitPriceMax() != null && advertisingUnitPrice.compareTo(param.getAdvertisingUnitPriceMax()) > 0) {
                    it.remove();
                    continue;
                }

                BigDecimal[] topImpressionShares = ReportParamUtil.unTopOfSearchImpressionShare(vo.getTopImpressionShare());
                if ((param.getTopImpressionShareMin() != null || param.getTopImpressionShareMax() != null)
                        && !ReportParamUtil.intersectionOrNot(topImpressionShares[0], topImpressionShares[1], param.getTopImpressionShareMin(), param.getTopImpressionShareMax())) {
                    it.remove();
                    continue;
                }
            }
        }
    }

    /**
     * 过滤广告位高级筛选数据
     */
    public void filterPlacementAdVanceData(List<PlacementPageVo> voList, PlacementPageParam param) {
        if (CollectionUtils.isNotEmpty(voList)) {
            Iterator<PlacementPageVo> it = voList.iterator();
            PlacementPageVo vo;
            while (it.hasNext()) {
                vo = it.next();

                Integer orderNum = vo.getAdOrderNum() != null ? vo.getAdOrderNum() : 0;
                vo.setAdOrderNum(orderNum);
                Integer impressions = vo.getImpressions() != null ? vo.getImpressions() : 0;
                vo.setImpressions(impressions);
                Integer clicks = vo.getClicks() != null ? vo.getClicks() : 0;
                vo.setClicks(clicks);

                //展示量
                if (param.getImpressionsMin() != null) {
                    if (!(vo.getImpressions() >= param.getImpressionsMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getImpressionsMax() != null) {
                    if (!(vo.getImpressions() <= param.getImpressionsMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //点击量
                if (param.getClicksMin() != null) {
                    if (!(vo.getClicks() >= param.getClicksMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getClicksMax() != null) {
                    if (!(vo.getClicks() <= param.getClicksMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //点击率（clicks/impressions）
                if (param.getClickRateMin() != null) {
                    double a = vo.getCtr() != null ? Double.parseDouble(vo.getCtr()) : 0;
                    double b = param.getClickRateMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getClickRateMax() != null) {
                    double a = vo.getCtr() != null ? Double.parseDouble(vo.getCtr()) : 0;
                    double b = param.getClickRateMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //花费
                if (param.getCostMin() != null) {
                    double a = vo.getAdCost() != null ? Double.parseDouble(vo.getAdCost()) : 0;
                    double b = param.getCostMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getCostMax() != null) {
                    double a = vo.getAdCost() != null ? Double.parseDouble(vo.getAdCost()) : 0;
                    double b = param.getCostMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //cpc  平均点击费用
                if (param.getCpcMin() != null) {
                    double a = vo.getAdCostPerClick() != null ? Double.parseDouble(vo.getAdCostPerClick()) : 0;
                    double b = param.getCpcMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getCpcMax() != null) {
                    double a = vo.getAdCostPerClick() != null ? Double.parseDouble(vo.getAdCostPerClick()) : 0;
                    double b = param.getCpcMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //广告订单量
                if (param.getOrderNumMin() != null) {
                    if (!(vo.getAdOrderNum() >= param.getOrderNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getOrderNumMax() != null) {
                    if (!(vo.getAdOrderNum() <= param.getOrderNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //广告销售额
                if (param.getSalesMin() != null) {
                    double a = vo.getAdSale() != null ? Double.parseDouble(vo.getAdSale()) : 0;
                    double b = param.getSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getSalesMax() != null) {
                    double a = vo.getAdSale() != null ? Double.parseDouble(vo.getAdSale()) : 0;
                    double b = param.getSalesMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //订单转化率
                if (param.getSalesConversionRateMin() != null) {
                    double a = vo.getCvr() != null ? Double.parseDouble(vo.getCvr()) : 0;
                    double b = param.getSalesConversionRateMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getSalesConversionRateMax() != null) {
                    double a = vo.getCvr() != null ? Double.parseDouble(vo.getCvr()) : 0;
                    double b = param.getSalesConversionRateMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //acos
                if (param.getAcosMin() != null) {
                    double a = vo.getAcos() != null ? Double.parseDouble(vo.getAcos()) : 0;
                    double b = param.getAcosMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAcosMax() != null) {
                    double a = vo.getAcos() != null ? Double.parseDouble(vo.getAcos()) : 0;
                    double b = param.getAcosMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // roas
                if (param.getRoasMin() != null) {
                    double a = vo.getRoas() != null ? Double.parseDouble(vo.getRoas()) : 0;
                    double b = param.getRoasMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // roas
                if (param.getRoasMax() != null) {
                    double a = vo.getRoas() != null ? Double.parseDouble(vo.getRoas()) : 0;
                    double b = param.getRoasMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // acots  需要乘以店铺销售额
                if (param.getAcotsMin() != null) {
                    double a = vo.getAcots() != null ? Double.parseDouble(vo.getAcots()) : 0;
                    double b = param.getAcotsMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // acots  需要乘以店铺销售额
                if (param.getAcotsMax() != null) {
                    double a = vo.getAcots() != null ? Double.parseDouble(vo.getAcots()) : 0;
                    double b = param.getAcotsMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // asots 需要乘以店铺销售额
                if (param.getAsotsMin() != null) {
                    double a = vo.getAsots() != null ? Double.parseDouble(vo.getAsots()) : 0;
                    double b = param.getAsotsMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // asots  需要乘以店铺销售额
                if (param.getAsotsMax() != null) {
                    double a = vo.getAsots() != null ? Double.parseDouble(vo.getAsots()) : 0;
                    double b = param.getAsotsMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                    }
                }


                /***********************************高级搜索新增查询指标字段**********************************/

                //本廣告产品销量过滤
                Integer adSelfSaleNum = vo.getAdSelfSaleNum() != null ? vo.getAdSelfSaleNum().intValue() : 0;
                if (param.getAdSelfSaleNumMin() != null) {
                    if (!(adSelfSaleNum >= param.getAdSelfSaleNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdSelfSaleNumMax() != null) {
                    if (!(adSelfSaleNum <= param.getAdSelfSaleNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                Integer viewImpressions = vo.getViewImpressions() != null ? vo.getViewImpressions().intValue() : 0;
                if (param.getViewImpressionsMin() != null) {
                    if (!(viewImpressions >= param.getViewImpressionsMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getViewImpressionsMax() != null) {
                    if (!(viewImpressions <= param.getViewImpressionsMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //cpa过滤
                if (param.getCpaMin() != null) {
                    double a = vo.getCpa() != null ? Double.parseDouble(vo.getCpa()) : 0;
                    double b = param.getCpaMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getCpaMax() != null) {
                    double a = vo.getCpa() != null ? Double.parseDouble(vo.getCpa()) : 0;
                    double b = param.getCpaMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //vcpm过滤
                if (param.getVcpmMin() != null) {
                    double a = vo.getVcpm() != null ? Double.parseDouble(vo.getVcpm()) : 0;
                    double b = param.getVcpmMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getVcpmMax() != null) {
                    double a = vo.getVcpm() != null ? Double.parseDouble(vo.getVcpm()) : 0;
                    double b = param.getVcpmMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //本广告产品订单量过滤
                Integer adSaleNum = vo.getAdSaleNum() != null ? vo.getAdSaleNum().intValue() : 0;
                if (param.getAdSaleNumMin() != null) {
                    if (!(adSaleNum >= param.getAdSaleNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdSaleNumMax() != null) {
                    if (!(adSaleNum <= param.getAdSaleNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //其他产品广告订单量过滤
                Integer adOtherOrderNum = vo.getAdOtherOrderNum() != null ? vo.getAdOtherOrderNum().intValue() : 0;
                if (param.getAdOtherOrderNumMin() != null) {
                    if (!(adOtherOrderNum >= param.getAdOtherOrderNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdOtherOrderNumMax() != null) {
                    if (!(adOtherOrderNum <= param.getAdOtherOrderNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //本广告产品销售额过滤
                if (param.getAdSalesMin() != null) {
                    double a = vo.getAdSales() != null ? Double.parseDouble(vo.getAdSales()) : 0;
                    double b = param.getAdSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdSalesMax() != null) {
                    double a = vo.getAdSales() != null ? Double.parseDouble(vo.getAdSales()) : 0;
                    double b = param.getAdSalesMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //其他产品广告销售额 过滤
                if (param.getAdOtherSalesMin() != null) {
                    double a = vo.getAdOtherSales() != null ? Double.parseDouble(vo.getAdOtherSales()) : 0;
                    double b = param.getAdOtherSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdOtherSalesMax() != null) {
                    double a = vo.getAdOtherSales() != null ? Double.parseDouble(vo.getAdOtherSales()) : 0;
                    double b = param.getAdOtherSalesMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //其他产品广告销量 过滤
                Integer adOtherSaleNum = vo.getAdOtherSaleNum() != null ? vo.getAdOtherSaleNum().intValue() : 0;
                if (param.getAdOtherSaleNumMin() != null) {
                    if (!(adOtherSaleNum >= param.getAdOtherSaleNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdOtherSaleNumMax() != null) {
                    if (!(adOtherSaleNum <= param.getAdOtherSaleNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”订单量过滤
                Integer ordersNewToBrandFTD = vo.getOrdersNewToBrandFTD() != null ? vo.getOrdersNewToBrandFTD().intValue() : 0;
                if (param.getOrdersNewToBrandFTDMin() != null) {
                    if (!(ordersNewToBrandFTD >= param.getOrdersNewToBrandFTDMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getOrdersNewToBrandFTDMax() != null) {
                    if (!(ordersNewToBrandFTD <= param.getOrdersNewToBrandFTDMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //“品牌新买家”订单百分比过滤
                if (param.getOrderRateNewToBrandFTDMin() != null) {
                    double a = vo.getOrderRateNewToBrandFTD() != null ? Double.parseDouble(vo.getOrderRateNewToBrandFTD()) : 0;
                    double b = param.getOrderRateNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getOrderRateNewToBrandFTDMax() != null) {
                    double a = vo.getOrderRateNewToBrandFTD() != null ? Double.parseDouble(vo.getOrderRateNewToBrandFTD()) : 0;
                    double b = param.getOrderRateNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”销售额 过滤
                if (param.getSalesNewToBrandFTDMin() != null) {
                    double a = vo.getSalesNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesNewToBrandFTD()) : 0;
                    double b = param.getSalesNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getSalesNewToBrandFTDMax() != null) {
                    double a = vo.getSalesNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesNewToBrandFTD()) : 0;
                    double b = param.getSalesNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”销售额百分比 过滤salesRateNewToBrandFTDMin
                if (param.getSalesRateNewToBrandFTDMin() != null) {
                    double a = vo.getSalesRateNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesRateNewToBrandFTD()) : 0;
                    double b = param.getSalesRateNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getSalesRateNewToBrandFTDMax() != null) {
                    double a = vo.getSalesRateNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesRateNewToBrandFTD()) : 0;
                    double b = param.getSalesRateNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”销量 过滤
                Integer unitsOrderedNewToBrandFTD = vo.getUnitsOrderedNewToBrandFTD() != null ? vo.getUnitsOrderedNewToBrandFTD().intValue() : 0;
                if (param.getUnitsOrderedNewToBrandFTDMin() != null) {
                    if (!(unitsOrderedNewToBrandFTD >= param.getUnitsOrderedNewToBrandFTDMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getUnitsOrderedNewToBrandFTDMax() != null) {
                    if (!(unitsOrderedNewToBrandFTD <= param.getUnitsOrderedNewToBrandFTDMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //“品牌新买家”销量百分比
                if (param.getUnitsOrderedRateNewToBrandFTDMin() != null) {
                    double a = vo.getUnitsOrderedRateNewToBrandFTD() != null ? Double.parseDouble(vo.getUnitsOrderedRateNewToBrandFTD()) : 0;
                    double b = param.getUnitsOrderedRateNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getUnitsOrderedRateNewToBrandFTDMax() != null) {
                    double a = vo.getUnitsOrderedRateNewToBrandFTD() != null ? Double.parseDouble(vo.getUnitsOrderedRateNewToBrandFTD()) : 0;
                    double b = param.getUnitsOrderedRateNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //广告销量筛选过滤
                Integer voAdSalesTotal = vo.getOrderNum() != null ? vo.getOrderNum().intValue() : 0;
                if (param.getAdSalesTotalMin() != null) {
                    if (!(voAdSalesTotal >= param.getAdSalesTotalMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdSalesTotalMax() != null) {
                    if (!(voAdSalesTotal <= param.getAdSalesTotalMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                // 广告笔单价 筛选
                BigDecimal advertisingUnitPrice = Optional.ofNullable(vo.getAdvertisingUnitPrice()).orElse(BigDecimal.ZERO);
                if (param.getAdvertisingUnitPriceMin() != null && advertisingUnitPrice.compareTo(param.getAdvertisingUnitPriceMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getAdvertisingUnitPriceMax() != null && advertisingUnitPrice.compareTo(param.getAdvertisingUnitPriceMax()) > 0) {
                    it.remove();
                    continue;
                }


            }
        }
    }

    /**
     * 过滤广告产品高级筛选数据
     */
    public void filterProductAdVanceData(List<AdProductPageVo> voList, AdProductPageParam param) {
        if (CollectionUtils.isNotEmpty(voList)) {
            Iterator<AdProductPageVo> it = voList.iterator();
            AdProductPageVo vo;
            while (it.hasNext()) {
                vo = it.next();

                Integer orderNum = vo.getAdOrderNum() != null ? vo.getAdOrderNum() : 0;
                vo.setAdOrderNum(orderNum);
                Integer impressions = vo.getImpressions() != null ? vo.getImpressions() : 0;
                vo.setImpressions(impressions);
                Integer clicks = vo.getClicks() != null ? vo.getClicks() : 0;
                vo.setClicks(clicks);

                //展示量
                if (param.getImpressionsMin() != null) {
                    if (!(vo.getImpressions() >= param.getImpressionsMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getImpressionsMax() != null) {
                    if (!(vo.getImpressions() <= param.getImpressionsMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //点击量
                if (param.getClicksMin() != null) {
                    if (!(vo.getClicks() >= param.getClicksMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getClicksMax() != null) {
                    if (!(vo.getClicks() <= param.getClicksMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //点击率（clicks/impressions）
                if (param.getClickRateMin() != null) {
                    double a = vo.getCtr() != null ? Double.parseDouble(vo.getCtr()) : 0;
                    double b = param.getClickRateMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getClickRateMax() != null) {
                    double a = vo.getCtr() != null ? Double.parseDouble(vo.getCtr()) : 0;
                    double b = param.getClickRateMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //花费
                if (param.getCostMin() != null) {
                    double a = vo.getAdCost() != null ? Double.parseDouble(vo.getAdCost()) : 0;
                    double b = param.getCostMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getCostMax() != null) {
                    double a = vo.getAdCost() != null ? Double.parseDouble(vo.getAdCost()) : 0;
                    double b = param.getCostMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //cpc  平均点击费用
                if (param.getCpcMin() != null) {
                    double a = vo.getAdCostPerClick() != null ? Double.parseDouble(vo.getAdCostPerClick()) : 0;
                    double b = param.getCpcMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getCpcMax() != null) {
                    double a = vo.getAdCostPerClick() != null ? Double.parseDouble(vo.getAdCostPerClick()) : 0;
                    double b = param.getCpcMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //广告订单量
                if (param.getOrderNumMin() != null) {
                    if (!(vo.getAdOrderNum() >= param.getOrderNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getOrderNumMax() != null) {
                    if (!(vo.getAdOrderNum() <= param.getOrderNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //广告销售额
                if (param.getSalesMin() != null) {
                    double a = vo.getAdSale() != null ? Double.parseDouble(vo.getAdSale()) : 0;
                    double b = param.getSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getSalesMax() != null) {
                    double a = vo.getAdSale() != null ? Double.parseDouble(vo.getAdSale()) : 0;
                    double b = param.getSalesMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //订单转化率
                if (param.getSalesConversionRateMin() != null) {
                    double a = vo.getCvr() != null ? Double.parseDouble(vo.getCvr()) : 0;
                    double b = param.getSalesConversionRateMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getSalesConversionRateMax() != null) {
                    double a = vo.getCvr() != null ? Double.parseDouble(vo.getCvr()) : 0;
                    double b = param.getSalesConversionRateMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //acos
                if (param.getAcosMin() != null) {
                    double a = vo.getAcos() != null ? Double.parseDouble(vo.getAcos()) : 0;
                    double b = param.getAcosMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAcosMax() != null) {
                    double a = vo.getAcos() != null ? Double.parseDouble(vo.getAcos()) : 0;
                    double b = param.getAcosMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // roas
                if (param.getRoasMin() != null) {
                    double a = vo.getRoas() != null ? Double.parseDouble(vo.getRoas()) : 0;
                    double b = param.getRoasMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // roas
                if (param.getRoasMax() != null) {
                    double a = vo.getRoas() != null ? Double.parseDouble(vo.getRoas()) : 0;
                    double b = param.getRoasMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // acots  需要乘以店铺销售额
                if (param.getAcotsMin() != null) {
                    double a = vo.getAcots() != null ? Double.parseDouble(vo.getAcots()) : 0;
                    double b = param.getAcotsMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // acots  需要乘以店铺销售额
                if (param.getAcotsMax() != null) {
                    double a = vo.getAcots() != null ? Double.parseDouble(vo.getAcots()) : 0;
                    double b = param.getAcotsMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // asots 需要乘以店铺销售额
                if (param.getAsotsMin() != null) {
                    double a = vo.getAsots() != null ? Double.parseDouble(vo.getAsots()) : 0;
                    double b = param.getAsotsMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // asots  需要乘以店铺销售额
                if (param.getAsotsMax() != null) {
                    double a = vo.getAsots() != null ? Double.parseDouble(vo.getAsots()) : 0;
                    double b = param.getAsotsMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                /*******************广告管理高级筛选新增查询指标********************/
                //可见展示次数过滤
                Integer viewImpressions = vo.getViewImpressions() != null ? vo.getViewImpressions().intValue() : 0;
                if (param.getViewImpressionsMin() != null) {
                    if (!(viewImpressions >= param.getViewImpressionsMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;

                    }
                }
                if (param.getViewImpressionsMax() != null) {
                    if (!(viewImpressions <= param.getViewImpressionsMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //cpa过滤
                if (param.getCpaMin() != null) {
                    double a = vo.getCpa() != null ? Double.parseDouble(vo.getCpa()) : 0;
                    double b = param.getCpaMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getCpaMax() != null) {
                    double a = vo.getCpa() != null ? Double.parseDouble(vo.getCpa()) : 0;
                    double b = param.getCpaMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //vcpm过滤
                if (param.getVcpmMin() != null) {
                    double a = vo.getVcpm() != null ? Double.parseDouble(vo.getVcpm()) : 0;
                    double b = param.getVcpmMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                    if(!SBCampaignCostTypeEnum.VCPM.getCode().equals(vo.getCostType())){
                        it.remove();  //花费类型不是vcpm的过滤掉,过滤掉
                        continue;
                    }
                }

                if (param.getVcpmMax() != null) {
                    double a = vo.getVcpm() != null ? Double.parseDouble(vo.getVcpm()) : 0;
                    double b = param.getVcpmMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                    if(!SBCampaignCostTypeEnum.VCPM.getCode().equals(vo.getCostType())){
                        it.remove();  //花费类型不是vcpm的过滤掉,过滤掉
                        continue;
                    }
                }

                //本广告产品订单量过滤
                Integer voAdSaleNum = vo.getAdSaleNum() != null ? vo.getAdSaleNum().intValue() : 0;
                if (param.getAdSaleNumMin() != null) {
                    if (!(voAdSaleNum >= param.getAdSaleNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdSaleNumMax() != null) {
                    if (!(voAdSaleNum <= param.getAdSaleNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //其他产品广告订单量过滤
                Integer voAdOtherOrderNum = vo.getAdOtherOrderNum() != null ? vo.getAdOtherOrderNum().intValue() : 0;
                if (param.getAdOtherOrderNumMin() != null) {
                    if (!(voAdOtherOrderNum >= param.getAdOtherOrderNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdOtherOrderNumMax() != null) {
                    if (!(voAdOtherOrderNum <= param.getAdOtherOrderNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //本广告产品销售额过滤
                if (param.getAdSalesMin() != null) {
                    double a = vo.getAdSales() != null ? Double.parseDouble(vo.getAdSales()) : 0;
                    double b = param.getAdSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdSalesMax() != null) {
                    double a = vo.getAdSales() != null ? Double.parseDouble(vo.getAdSales()) : 0;
                    double b = param.getAdSalesMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //其他产品广告销售额 过滤
                if (param.getAdOtherSalesMin() != null) {
                    double a = vo.getAdOtherSales() != null ? Double.parseDouble(vo.getAdOtherSales()) : 0;
                    double b = param.getAdOtherSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdOtherSalesMax() != null) {
                    double a = vo.getAdOtherSales() != null ? Double.parseDouble(vo.getAdOtherSales()) : 0;
                    double b = param.getAdOtherSalesMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //本廣告产品销量过滤
                Integer adSelfSaleNum = vo.getAdSelfSaleNum() != null ? vo.getAdSelfSaleNum().intValue() : 0;
                if (param.getAdSelfSaleNumMin() != null) {
                    if (!(adSelfSaleNum >= param.getAdSelfSaleNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdSelfSaleNumMax() != null) {
                    if (!(adSelfSaleNum <= param.getAdSelfSaleNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //其他产品广告销量 过滤
                Integer voAdOtherSaleNum = vo.getAdOtherSaleNum() != null ? vo.getAdOtherSaleNum().intValue() : 0;
                if (param.getAdOtherSaleNumMin() != null) {
                    if (!(voAdOtherSaleNum >= param.getAdOtherSaleNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAdOtherSaleNumMax() != null) {
                    if (!(voAdOtherSaleNum <= param.getAdOtherSaleNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”订单量过滤
                Integer voOrdersNewToBrandFTD = vo.getOrdersNewToBrandFTD() != null ? vo.getOrdersNewToBrandFTD().intValue() : 0;
                if (param.getOrdersNewToBrandFTDMin() != null) {
                    if (!(voOrdersNewToBrandFTD >= param.getOrdersNewToBrandFTDMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getOrdersNewToBrandFTDMax() != null) {
                    if (!(voOrdersNewToBrandFTD <= param.getOrdersNewToBrandFTDMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //“品牌新买家”订单百分比过滤
                if (param.getOrderRateNewToBrandFTDMin() != null) {
                    double a = vo.getOrderRateNewToBrandFTD() != null ? Double.parseDouble(vo.getOrderRateNewToBrandFTD()) : 0;
                    double b = param.getOrderRateNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getOrderRateNewToBrandFTDMax() != null) {
                    double a = vo.getOrderRateNewToBrandFTD() != null ? Double.parseDouble(vo.getOrderRateNewToBrandFTD()) : 0;
                    double b = param.getOrderRateNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”销售额 过滤
                if (param.getSalesNewToBrandFTDMin() != null) {
                    double a = vo.getSalesNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesNewToBrandFTD()) : 0;
                    double b = param.getSalesNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getSalesNewToBrandFTDMax() != null) {
                    double a = vo.getSalesNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesNewToBrandFTD()) : 0;
                    double b = param.getSalesNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”销售额百分比 过滤salesRateNewToBrandFTDMin
                if (param.getSalesRateNewToBrandFTDMin() != null) {
                    double a = vo.getSalesRateNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesRateNewToBrandFTD()) : 0;
                    double b = param.getSalesRateNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getSalesRateNewToBrandFTDMax() != null) {
                    double a = vo.getSalesRateNewToBrandFTD() != null ? Double.parseDouble(vo.getSalesRateNewToBrandFTD()) : 0;
                    double b = param.getSalesRateNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //“品牌新买家”销量 过滤
                Integer voUnitsOrderedNewToBrandFTD = vo.getUnitsOrderedNewToBrandFTD() != null ? vo.getUnitsOrderedNewToBrandFTD().intValue() : 0;
                if (param.getUnitsOrderedNewToBrandFTDMin() != null) {
                    if (!(voUnitsOrderedNewToBrandFTD >= param.getUnitsOrderedNewToBrandFTDMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getUnitsOrderedNewToBrandFTDMax() != null) {
                    if (!(voUnitsOrderedNewToBrandFTD <= param.getUnitsOrderedNewToBrandFTDMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }


                //“品牌新买家”销量百分比
                if (param.getUnitsOrderedRateNewToBrandFTDMin() != null) {
                    double a = vo.getUnitsOrderedRateNewToBrandFTD() != null ? Double.parseDouble(vo.getUnitsOrderedRateNewToBrandFTD()) : 0;
                    double b = param.getUnitsOrderedRateNewToBrandFTDMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getUnitsOrderedRateNewToBrandFTDMax() != null) {
                    double a = vo.getUnitsOrderedRateNewToBrandFTD() != null ? Double.parseDouble(vo.getUnitsOrderedRateNewToBrandFTD()) : 0;
                    double b = param.getUnitsOrderedRateNewToBrandFTDMax().doubleValue();
                    if (!(a <= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                //广告销量筛选过滤
                Integer voAdSalesTotal = vo.getOrderNum() != null ? vo.getOrderNum().intValue() : 0;
                if (param.getAdSalesTotalMin() != null) {
                    if (!(voAdSalesTotal >= param.getAdSalesTotalMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }

                if (param.getAdSalesTotalMax() != null) {
                    if (!(voAdSalesTotal <= param.getAdSalesTotalMax())) {
                        it.remove();  //不符合,过滤掉
                    }
                }

                // 加购次数 筛选
                Integer addToCart = Optional.ofNullable(vo.getAddToCart()).orElse(0);
                if (param.getAddToCartMin() != null && addToCart < param.getAddToCartMin()) {
                    it.remove();
                    continue;
                }
                if (param.getAddToCartMax() != null && addToCart > param.getAddToCartMax()) {
                    it.remove();
                    continue;
                }

                // 视频完整播放次数 筛选
                Integer videoCompleteViews = Optional.ofNullable(vo.getVideoCompleteViews()).orElse(0);
                if (param.getVideoCompleteViewsMin() != null && videoCompleteViews < param.getVideoCompleteViewsMin()) {
                    it.remove();
                    continue;
                }
                if (param.getVideoCompleteViewsMax() != null && videoCompleteViews > param.getVideoCompleteViewsMax()) {
                    it.remove();
                    continue;
                }

                // 观看率 筛选
                BigDecimal viewabilityRate = Optional.ofNullable(vo.getViewabilityRate()).orElse(BigDecimal.ZERO);
                if (param.getViewabilityRateMin() != null && viewabilityRate.compareTo(param.getViewabilityRateMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getViewabilityRateMax() != null && viewabilityRate.compareTo(param.getViewabilityRateMax()) > 0) {
                    it.remove();
                    continue;
                }

                // 观看点击率 筛选
                BigDecimal viewClickThroughRate = Optional.ofNullable(vo.getViewClickThroughRate()).orElse(BigDecimal.ZERO);
                if (param.getViewClickThroughRateMin() != null && viewClickThroughRate.compareTo(param.getViewClickThroughRateMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getViewClickThroughRateMax() != null && viewClickThroughRate.compareTo(param.getViewClickThroughRateMax()) > 0) {
                    it.remove();
                    continue;
                }

                // 品牌搜索次数 筛选
                Integer brandedSearches = Optional.ofNullable(vo.getBrandedSearches()).orElse(0);
                if (param.getBrandedSearchesMin() != null && brandedSearches < param.getBrandedSearchesMin()) {
                    it.remove();
                    continue;
                }
                if (param.getBrandedSearchesMax() != null && brandedSearches > param.getBrandedSearchesMax()) {
                    it.remove();
                    continue;
                }

                // 累计触达用户 筛选
                Integer cumulativeReach = Optional.ofNullable(vo.getCumulativeReach()).orElse(0);
                if (param.getCumulativeReachMin() != null && cumulativeReach < param.getCumulativeReachMin()) {
                    it.remove();
                    continue;
                }
                if (param.getCumulativeReachMax() != null && cumulativeReach > param.getCumulativeReachMax()) {
                    it.remove();
                    continue;
                }

                // 平均触达次数 筛选
                BigDecimal impressionsFrequencyAverage = Optional.ofNullable(vo.getImpressionsFrequencyAverage()).orElse(BigDecimal.ZERO);
                if (param.getImpressionsFrequencyAverageMin() != null && impressionsFrequencyAverage.compareTo(param.getImpressionsFrequencyAverageMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getImpressionsFrequencyAverageMax() != null && impressionsFrequencyAverage.compareTo(param.getImpressionsFrequencyAverageMax()) > 0) {
                    it.remove();
                    continue;
                }

                // 广告笔单价 筛选
                BigDecimal advertisingUnitPrice = Optional.ofNullable(vo.getAdvertisingUnitPrice()).orElse(BigDecimal.ZERO);
                if (param.getAdvertisingUnitPriceMin() != null && advertisingUnitPrice.compareTo(param.getAdvertisingUnitPriceMin()) < 0) {
                    it.remove();
                    continue;
                }
                if (param.getAdvertisingUnitPriceMax() != null && advertisingUnitPrice.compareTo(param.getAdvertisingUnitPriceMax()) > 0) {
                    it.remove();
                    continue;
                }

            }
        }
    }


    /**
     * 过滤广告产品高级筛选数据
     */
    public void filterAdsAdVanceData(List<AdAdsPageVo> voList, AdAdsPageParam param) {
        if (CollectionUtils.isNotEmpty(voList)) {
            Iterator<AdAdsPageVo> it = voList.iterator();
            AdAdsPageVo vo;
            while (it.hasNext()) {
                vo = it.next();

                Integer orderNum = vo.getAdOrderNum() != null ? vo.getAdOrderNum() : 0;
                vo.setAdOrderNum(orderNum);
                Integer impressions = vo.getImpressions() != null ? vo.getImpressions() : 0;
                vo.setImpressions(impressions);
                Integer clicks = vo.getClicks() != null ? vo.getClicks() : 0;
                vo.setClicks(clicks);

                //展示量
                if (param.getImpressionsMin() != null) {
                    if (!(vo.getImpressions() >= param.getImpressionsMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getImpressionsMax() != null) {
                    if (!(vo.getImpressions() <= param.getImpressionsMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //点击量
                if (param.getClicksMin() != null) {
                    if (!(vo.getClicks() >= param.getClicksMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getClicksMax() != null) {
                    if (!(vo.getClicks() <= param.getClicksMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //点击率（clicks/impressions）
                if (param.getClickRateMin() != null) {
                    double a = vo.getCtr() != null ? Double.parseDouble(vo.getCtr()) : 0;
                    double b = param.getClickRateMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getClickRateMax() != null) {
                    double a = vo.getCtr() != null ? Double.parseDouble(vo.getCtr()) : 0;
                    double b = param.getClickRateMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //花费
                if (param.getCostMin() != null) {
                    double a = vo.getAdCost() != null ? Double.parseDouble(vo.getAdCost()) : 0;
                    double b = param.getCostMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getCostMax() != null) {
                    double a = vo.getAdCost() != null ? Double.parseDouble(vo.getAdCost()) : 0;
                    double b = param.getCostMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //cpc  平均点击费用
                if (param.getCpcMin() != null) {
                    double a = vo.getAdCostPerClick() != null ? Double.parseDouble(vo.getAdCostPerClick()) : 0;
                    double b = param.getCpcMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getCpcMax() != null) {
                    double a = vo.getAdCostPerClick() != null ? Double.parseDouble(vo.getAdCostPerClick()) : 0;
                    double b = param.getCpcMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //广告订单量
                if (param.getOrderNumMin() != null) {
                    if (!(vo.getAdOrderNum() >= param.getOrderNumMin())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getOrderNumMax() != null) {
                    if (!(vo.getAdOrderNum() <= param.getOrderNumMax())) {
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //广告销售额
                if (param.getSalesMin() != null) {
                    double a = vo.getAdSale() != null ? Double.parseDouble(vo.getAdSale()) : 0;
                    double b = param.getSalesMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getSalesMax() != null) {
                    double a = vo.getAdSale() != null ? Double.parseDouble(vo.getAdSale()) : 0;
                    double b = param.getSalesMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //订单转化率
                if (param.getSalesConversionRateMin() != null) {
                    double a = vo.getCvr() != null ? Double.parseDouble(vo.getCvr()) : 0;
                    double b = param.getSalesConversionRateMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getSalesConversionRateMax() != null) {
                    double a = vo.getCvr() != null ? Double.parseDouble(vo.getCvr()) : 0;
                    double b = param.getSalesConversionRateMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                //acos
                if (param.getAcosMin() != null) {
                    double a = vo.getAcos() != null ? Double.parseDouble(vo.getAcos()) : 0;
                    double b = param.getAcosMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                if (param.getAcosMax() != null) {
                    double a = vo.getAcos() != null ? Double.parseDouble(vo.getAcos()) : 0;
                    double b = param.getAcosMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // roas
                if (param.getRoasMin() != null) {
                    double a = vo.getRoas() != null ? Double.parseDouble(vo.getRoas()) : 0;
                    double b = param.getRoasMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // roas
                if (param.getRoasMax() != null) {
                    double a = vo.getRoas() != null ? Double.parseDouble(vo.getRoas()) : 0;
                    double b = param.getRoasMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // acots  需要乘以店铺销售额
                if (param.getAcotsMin() != null) {
                    double a = vo.getAcots() != null ? Double.parseDouble(vo.getAcots()) : 0;
                    double b = param.getAcotsMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // acots  需要乘以店铺销售额
                if (param.getAcotsMax() != null) {
                    double a = vo.getAcots() != null ? Double.parseDouble(vo.getAcots()) : 0;
                    double b = param.getAcotsMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // asots 需要乘以店铺销售额
                if (param.getAsotsMin() != null) {
                    double a = vo.getAsots() != null ? Double.parseDouble(vo.getAsots()) : 0;
                    double b = param.getAsotsMin().doubleValue();
                    if (!(a >= b)) {  //a大于等于b
                        it.remove();  //不符合,过滤掉
                        continue;
                    }
                }
                // asots  需要乘以店铺销售额
                if (param.getAsotsMax() != null) {
                    double a = vo.getAsots() != null ? Double.parseDouble(vo.getAsots()) : 0;
                    double b = param.getAsotsMax().doubleValue();
                    if (!(a <= b)) {  //a小于等于b
                        it.remove();  //不符合,过滤掉
                    }
                }
            }
        }
    }


    // 填充指标占比数据
    public void filterMetricData(CpcCommPageVo vo, AdMetricDto adMetricDto) {
        if (adMetricDto == null) {
            vo.setAdCostPercentage("0");
            vo.setAdSalePercentage("0");
            vo.setAdOrderNumPercentage("0");
            vo.setOrderNumPercentage("0");
            return;
        }
        this.computeMetricData(adMetricDto, vo);
    }

    private void computeMetricData(AdMetricDto adMetricDto, CpcCommPageVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(MathUtil.multiply(MathUtil.divide(vo.getAdCost(), adMetricDto.getSumCost().toString()), "100"));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(MathUtil.multiply(MathUtil.divide(vo.getAdSale(), adMetricDto.getSumAdSale().toString()), "100"));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100"));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getOrderNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(MathUtil.multiply(MathUtil.divide(vo.getOrderNum().toString(), adMetricDto.getSumOrderNum().toString()), "100"));
        }
    }
}
