package com.meiyunji.sponsored.service.autoRule.vo;

import com.meiyunji.sellfox.aadras.types.message.task.AutoRuleScheduleTriggerTaskMessage;
import lombok.Data;

import java.util.List;

/**
 * @author: sun<PERSON><PERSON>
 * @email: sun<PERSON><PERSON>@dianxiaomi.com
 * @date: 2024-06-17  14:44
 */

@Data
public class AutoRuleScheduleTaskDto {

    private String itemType;

    private String targetType;

    private String itemId;

    private String childrenItemType;

    //group_concat taskId,逗号分隔
    private String taskId;

    //taskId集合，由taskId切割
    private List<Long> taskIdList;

    private String adType;

}
