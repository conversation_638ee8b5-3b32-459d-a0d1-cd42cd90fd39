package com.meiyunji.sponsored.service.multiPlatform.tiktok.model.response;

import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.ShopProduct;
import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
public class ShopProductResp {

    private List<ShopProduct> productList;
    private Integer pageNo;
    private Integer pageSize;
    private Integer totalPage;
    private Integer totalSize;

    public static ShopProductResp emptyInstance(int pageNum, int pageSize) {
        ShopProductResp resp = new ShopProductResp();
        resp.setProductList(Collections.emptyList());
        resp.setPageNo(pageNum);
        resp.setPageSize(pageSize);
        resp.setTotalPage(0);
        resp.setTotalSize(0);
        return resp;
    }

}
