package com.meiyunji.sponsored.service.enums;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2025/4/9 13:45
 */
public enum ShopTypeEnum {
    SC("SC","SC店铺"),
    VC("VC","VC店铺"),
    ;
    private String code;
    private String desc;

    ShopTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
