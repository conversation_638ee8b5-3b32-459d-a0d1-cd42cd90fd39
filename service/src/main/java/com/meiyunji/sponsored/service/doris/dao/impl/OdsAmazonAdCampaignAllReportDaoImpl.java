package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Maps;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.adProductRight.service.impl.AdProductRightService;
import com.meiyunji.sponsored.service.adTagSystem.dto.CampaignDto;
import com.meiyunji.sponsored.service.adTagSystem.enums.AdManageTagTypeEnum;
import com.meiyunji.sponsored.service.adTagSystem.param.AdvanceFilter;
import com.meiyunji.sponsored.service.adTagSystem.param.CampaignTagDataParam;
import com.meiyunji.sponsored.service.adTagSystem.dto.CampaignTagDataReportDto;
import com.meiyunji.sponsored.service.cpc.dto.AdBaseReportDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.doris.bo.AmazonAdCampaignAllReportBo;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.newDashboard.bo.ShopEffectDataBo;
import com.meiyunji.sponsored.service.newDashboard.dto.CampaignOrGroupOrPortfolioDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTopDataDto;
import com.meiyunji.sponsored.service.newDashboard.dto.ShopRankDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByRateEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardRankQueryFieldEnum;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AsinOrderNumDto;
import com.meiyunji.sponsored.service.permission.util.PermissionSqlBuilder;
import com.meiyunji.sponsored.service.util.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.newDashboard.bo.EffectDataBo;


import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Stream;

/**
 * 广告所有活动报告(OdsAmazonAdCampaignAllReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:17
 */
@Repository
@Slf4j
public class OdsAmazonAdCampaignAllReportDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdCampaignAllReport> implements IOdsAmazonAdCampaignAllReportDao {

    @Autowired
    private AdProductRightService adProductRightService;

    //约定字段名称和数据库表对应取值map
    private static final Map<String, String> campaignFieldSumMap = Maps.newHashMap();

    static {
        campaignFieldSumMap.put(DashboardDataFieldEnum.COST.getCode(), "ifnull(sum(r.cost * c.rate), 0)");
        campaignFieldSumMap.put(DashboardDataFieldEnum.TOTAL_SALES.getCode(), "ifnull(sum(if (r.type = 'sp', r.sales7d, r.sales14d)  * c.rate), 0)");
        campaignFieldSumMap.put(DashboardDataFieldEnum.IMPRESSIONS.getCode(), "ifnull(sum(r.impressions), 0)");
        campaignFieldSumMap.put(DashboardDataFieldEnum.CLICKS.getCode(), "ifnull(sum(r.clicks), 0)");
        campaignFieldSumMap.put(DashboardDataFieldEnum.ORDER_NUM.getCode(), "ifnull(sum(if (r.type = 'sp' , r.conversions7d, r.conversions14d)), 0)");
        campaignFieldSumMap.put(DashboardDataFieldEnum.SALE_NUM.getCode(), "ifnull(sum(if (r.type = 'sp' , r.units_ordered7d, if (r.type = 'sb' , r.units_sold14d, r.units_ordered14d))), 0)");
        campaignFieldSumMap.put(DashboardDataFieldEnum.ACOS.getCode(), " ifnull(ROUND(ifnull(sum(r.cost * c.rate), 0)/ ifnull(sum(if(r.type = 'sp', r.sales7d, r.sales14d) * c.rate), 0), 4), 0)");
        campaignFieldSumMap.put(DashboardDataFieldEnum.ROAS.getCode(), " ifnull(ROUND(ifnull(sum(if(r.type = 'sp', r.sales7d, r.sales14d) * c.rate), 0)/ ifnull(sum(r.cost * c.rate), 0), 4), 0)");
        campaignFieldSumMap.put(DashboardDataFieldEnum.CLICK_RATE.getCode(), " ifnull(ROUND(ifnull(sum(r.clicks)/ sum(r.impressions), 0), 4), 0)");//点击率
        campaignFieldSumMap.put(DashboardDataFieldEnum.CONVERSION_RATE.getCode(), " ifnull(ROUND(ifnull(sum(if (r.type = 'sp', r.conversions7d, conversions14d))/ sum(r.clicks), 0), 4), 0)");//转化率
        campaignFieldSumMap.put(DashboardDataFieldEnum.CPC.getCode(), " ifnull(ROUND(ifnull(sum(r.cost * c.rate)/ sum(r.clicks), 0), 4), 0) ");//cpc
    }

    @Override
    public List<CampaignOrGroupOrPortfolioDto> queryAdCampaignCharts(Integer puid, List<Integer> shopIdList,
                                                                     List<String> marketplaceIdList, List<String> campaignIdList,
                                                                     String currency, String startDate,
                                                                     String endDate) {
        List<Object> argsList = Lists.newArrayList();
        String querySql = campaignQuerySql(puid, shopIdList, marketplaceIdList, campaignIdList, currency, startDate, endDate, argsList, null, null, null, null, null);
        return getJdbcTemplate().query(querySql, new ObjectMapper<>(CampaignOrGroupOrPortfolioDto.class), argsList.toArray());
    }

    @Override
    public List<ShopRankDto> queryAdRankData(Integer puid, List<Integer> shopIdList,
                                             List<String> marketplaceIdList, List<String> idList,
                                             String currency, String startDate,
                                             String endDate, Integer queryField) {
        List<Object> argsList = Lists.newArrayList();
        String querySql = this.rankQuerySql(puid, shopIdList, marketplaceIdList, idList, currency, startDate, endDate, argsList, queryField, null, null, null, null, null, null);
        return getJdbcTemplate().query(querySql, new ObjectMapper<>(ShopRankDto.class), argsList.toArray());
    }

    @Override
    public List<DashboardAdTopDataDto> queryAdCampaignYoyOrMomTop(String subSqlA, String subSqlB,
                                                                     List<Object> queryParam, DashboardDataFieldEnum dataField,
                                                                     DashboardOrderByRateEnum orderField, String orderBy,
                                                                     Integer limit, Boolean noZero) {
        List<Object> argsList = Lists.newArrayList();
        argsList.addAll(queryParam);
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT  subSqlA.campaignId as campaignId, ifnull(subSqlA.cost, 0) as cost, ");
        sb.append(" ifnull(subSqlA.totalSales, 0) as totalSales, ifnull(subSqlA.impressions, 0) as impressions,  ");
        sb.append(" ifnull(subSqlA.clicks, 0) as clicks, ifnull(subSqlA.orderNum, 0) as orderNum, ");
        sb.append(" ifnull(subSqlA.saleNum, 0) as saleNum, ifnull(subSqlA.acos, 0) as acos, ifnull(subSqlA.roas, 0) as roas, ");
        sb.append(" ifnull(subSqlA.clickRate, 0) as clickRate, ifnull(subSqlA.conversionRate, 0) as conversionRate, ifnull(subSqlA.cpc, 0) as cpc, ifnull(subSqlA.cpa, 0) as cpa, ");
        sb.append(" ifnull(subSqlB.cost, 0) as subCost, ");
        sb.append(" ifnull(subSqlB.totalSales, 0) as subTotalSales, ifnull(subSqlB.impressions, 0) as subImpressions,  ");
        sb.append(" ifnull(subSqlB.clicks, 0) as subClicks, ifnull(subSqlB.orderNum, 0) as subOrderNum, ");
        sb.append(" ifnull(subSqlB.saleNum, 0) as subSaleNum, ifnull(subSqlB.acos, 0) as subAcos, ifnull(subSqlB.roas, 0) as subRoas, ");
        sb.append(" ifnull(subSqlB.clickRate, 0) as subClickRate, ifnull(subSqlB.conversionRate, 0) as subConversionRate, ifnull(subSqlB.cpc, 0) as subCpc, ifnull(subSqlB.cpa, 0) as subCpa, ");
        sb.append(" SUM(subSqlA.cost) OVER () as allCost, ");
        sb.append(" SUM(subSqlA.totalSales) OVER () as allTotalSales, ");
        sb.append(" SUM(subSqlA.impressions) OVER () as allImpressions, ");
        sb.append(" SUM(subSqlA.clicks) OVER () as allClicks, ");
        sb.append(" SUM(subSqlA.orderNum) OVER () as allOrderNum, ");
        sb.append(" SUM(subSqlA.saleNum) OVER () as allSaleNum ");
        sb.append(" From ");
        sb.append(" (").append(subSqlA).append(")").append(" subSqlA ");
        sb.append(" left join ");
        sb.append(" (").append(subSqlB).append(")").append(" subSqlB ");
        sb.append(" on subSqlA.campaignId = subSqlB.campaignId ");
        if (Boolean.TRUE.equals(noZero)) {
            sb.append(" where " + getColumnSelect(dataField.getCode()) );
        }
        if (Objects.nonNull(orderField) && DashboardOrderByRateEnum.PERCENT == orderField) {
            //以上几个计算占比时，是按绝对值进行排序的
            sb.append(" ORDER BY ").append("ifnull(subSqlA.").append(dataField.getCode()).append(", 0)");
        }  else if (Objects.nonNull(orderField) && Stream.of(DashboardOrderByRateEnum.YOY_VALUE, DashboardOrderByRateEnum.MOM_VALUE)
                .anyMatch(d -> d == orderField)) {//计算增长值
            sb.append(" ORDER BY ").append(" (");
            sb.append("ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" - ").append(" ifnull(subSqlB.").append(dataField.getCode()).append(", 0)").append(") ");
        }else {
            sb.append(" ORDER BY ").append(" (");
            //计算增长率
            sb.append("if(ifnull(subSqlB.").append(dataField.getCode()).append(", 0)").append(" = 0 ").append(", if(ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" = 0 ").append(", 0").append(", 1)");
            sb.append(", (ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" - ").append(" subSqlB.").append(dataField.getCode()).append(" ) ");
            sb.append(" / ").append(" subSqlB.").append(dataField.getCode()).append(" )");
            sb.append(" )");
        }
        if (StringUtils.isNotEmpty(orderBy)) {
            sb.append(" ").append(orderBy);
        }
        sb.append(", ifnull(subSqlA.").append(dataField.getCode()).append(", 0)");
        if (StringUtils.isNotEmpty(orderBy)) {
            sb.append(" ").append(orderBy);
        }
        if (Objects.nonNull(limit) && limit != 0) {
            sb.append(" LIMIT ").append(limit);
        }
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(DashboardAdTopDataDto.class), argsList.toArray());
    }

    @Override
    public List<DashboardAdTopDataDto> queryAdRankYoyOrMomTop(String subSqlA, String subSqlB,
                                                                  List<Object> queryParam, DashboardDataFieldEnum dataField,
                                                                  DashboardOrderByRateEnum orderField, String orderBy,
                                                                  Integer queryField, Integer limit, boolean isSelAllSub, Boolean noZero) {
        List<Object> argsList = Lists.newArrayList();
        argsList.addAll(queryParam);
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT  subSqlA.id, ");
        sb.append(" ifnull(subSqlA.cost, 0) as cost, ");
        sb.append(" ifnull(subSqlA.totalSales, 0) as totalSales, ifnull(subSqlA.impressions, 0) as impressions,  ");
        sb.append(" ifnull(subSqlA.clicks, 0) as clicks, ifnull(subSqlA.orderNum, 0) as orderNum, ");
        sb.append(" ifnull(subSqlA.saleNum, 0) as saleNum, ifnull(subSqlA.acos, 0) as acos, ifnull(subSqlA.roas, 0) as roas, ");
        sb.append(" ifnull(subSqlA.clickRate, 0) as clickRate, ifnull(subSqlA.conversionRate, 0) as conversionRate, ifnull(subSqlA.cpc, 0) as cpc, ifnull(subSqlA.cpa, 0) as cpa, ");
        sb.append(" ifnull(subSqlB.cost, 0) as subCost, ");
        sb.append(" ifnull(subSqlB.totalSales, 0) as subTotalSales, ifnull(subSqlB.impressions, 0) as subImpressions,  ");
        sb.append(" ifnull(subSqlB.clicks, 0) as subClicks, ifnull(subSqlB.orderNum, 0) as subOrderNum, ");
        sb.append(" ifnull(subSqlB.saleNum, 0) as subSaleNum, ifnull(subSqlB.acos, 0) as subAcos, ifnull(subSqlB.roas, 0) as subRoas, ");
        sb.append(" ifnull(subSqlB.clickRate, 0) as subClickRate, ifnull(subSqlB.conversionRate, 0) as subConversionRate, ifnull(subSqlB.cpc, 0) as subCpc, ifnull(subSqlB.cpa, 0) as subCpa, ");
        sb.append(" SUM(subSqlA.cost) OVER () as allCost, ");
        sb.append(" SUM(subSqlA.totalSales) OVER () as allTotalSales, ");
        sb.append(" SUM(subSqlA.impressions) OVER () as allImpressions, ");
        sb.append(" SUM(subSqlA.clicks) OVER () as allClicks, ");
        sb.append(" SUM(subSqlA.orderNum) OVER () as allOrderNum, ");
        sb.append(" SUM(subSqlA.saleNum) OVER () as allSaleNum ");
        if (isSelAllSub) {
            sb.append(" ,SUM(ifnull(subSqlB.").append(dataField.getCode()).append(", 0)) OVER () as allSubNum ");
        }
        sb.append(" From ");
        sb.append(" (").append(subSqlA).append(")").append(" subSqlA ");
        sb.append(" left join ");
        sb.append(" (").append(subSqlB).append(")").append(" subSqlB ");
        sb.append(" on subSqlA.id = subSqlB.id");
        if (Boolean.TRUE.equals(noZero)) {
            sb.append(" where " + getColumnSelect(dataField.getCode()) );
        }
        if (Objects.nonNull(orderField) && DashboardOrderByRateEnum.PERCENT == orderField) {
            //以上几个计算占比时，是按绝对值进行排序的
            sb.append(" ORDER BY ").append("ifnull(subSqlA.").append(dataField.getCode()).append(", 0)");
        }  else if (Objects.nonNull(orderField) && Stream.of(DashboardOrderByRateEnum.YOY_VALUE, DashboardOrderByRateEnum.MOM_VALUE)
                .anyMatch(d -> d == orderField)) {//计算增长值
            sb.append(" ORDER BY ").append(" (");
            sb.append("ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" - ").append(" ifnull(subSqlB.").append(dataField.getCode()).append(", 0)").append(") ");
        }else {
            sb.append(" ORDER BY ").append(" (");//计算增长率
            sb.append("if(ifnull(subSqlB.").append(dataField.getCode()).append(", 0)").append(" = 0 ").append(", if(ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" = 0 ").append(", 0").append(", 1)");
            sb.append(", (ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" - ").append(" subSqlB.").append(dataField.getCode()).append(" ) ");
            sb.append(" / ").append(" subSqlB.").append(dataField.getCode()).append(" )");
            sb.append(" )");
        }
        if (StringUtils.isNotEmpty(orderBy)) {
            sb.append(" ").append(orderBy);
        }
        sb.append(", ifnull(subSqlA.").append(dataField.getCode()).append(", 0)");
        if (StringUtils.isNotEmpty(orderBy)) {
            sb.append(" ").append(orderBy);
        }
        if (Objects.nonNull(limit) && limit != 0) {
            sb.append(" LIMIT ").append(limit);
        }
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(DashboardAdTopDataDto.class), argsList.toArray());
    }

    @Override
    public List<EffectDataBo> getEffectData(Integer puid, List<Integer> shopIdList, List<String> marketplaceIdList, String currency,
                                            String startDate, String endDate, String modelType, List<String> siteToday, Boolean isSiteToday,
                                            List<String> portfolioIds, List<String> campaignIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = getEffectDataSql(puid, shopIdList, marketplaceIdList, currency, startDate, endDate, argsList, false,
                siteToday, isSiteToday, portfolioIds, campaignIds, false);
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(EffectDataBo.class));
    }

    @Override
    public EffectDataBo getEffectAggregateData(Integer puid, List<Integer> shopIdList, List<String> marketplaceIdList, String currency, String startDate, String endDate,
                                               String modelType, List<String> siteToday, Boolean isSiteToday,
                                               List<String> portfolioIds, List<String> campaignIds) {

        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = getEffectDataSql(puid, shopIdList, marketplaceIdList, currency, startDate, endDate, argsList, true,
                siteToday, isSiteToday, portfolioIds, campaignIds, false);
        List<EffectDataBo> list = getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(EffectDataBo.class));
        return CollectionUtils.isNotEmpty(list) && list.size() == 1 ? list.get(0) : new EffectDataBo();
    }

    @Override
    public List<AmazonAdCampaignAllReportBo> campaignIdListByInsufficientBudget(Integer puid, List<Integer> shopIdList, List<String> marketplaceIdList,
                                                                                String startDate, String endDate, String currency, String orderBy,
                                                                                Integer limit, List<String> siteToday, Boolean isSiteToday,
                                                                                List<String> portfolioIds, List<String> campaignIdList, Boolean noZero) {
        StringBuilder sb = new StringBuilder("select campaign_id, ");
        sb.append("ifnull(sum(r.cost), 0)").append(" cost,");
        sb.append("ifnull(sum(if (r.type = 'sp', r.sales7d, r.sales14d)), 0)").append(" totalSales,");
        sb.append(campaignFieldSumMap.get(DashboardDataFieldEnum.IMPRESSIONS.getCode())).append(" impressions,");
        sb.append(campaignFieldSumMap.get(DashboardDataFieldEnum.CLICKS.getCode())).append(" clicks,");
        sb.append(campaignFieldSumMap.get(DashboardDataFieldEnum.ORDER_NUM.getCode())).append(" orderNum,");
        sb.append(campaignFieldSumMap.get(DashboardDataFieldEnum.SALE_NUM.getCode())).append(" saleNum, ");
        sb.append("ifnull(ROUND(ifnull(sum(r.cost), 0)/ ifnull(sum(if(r.type = 'sp', r.sales7d, r.sales14d)), 0), 4), 0)").append(" acos, ");
        sb.append("ifnull(ROUND(ifnull(sum(if(r.type = 'sp', r.sales7d, r.sales14d)), 0)/ ifnull(sum(r.cost), 0), 4), 0)").append(" roas, ");
        sb.append(campaignFieldSumMap.get(DashboardDataFieldEnum.CLICK_RATE.getCode())).append(" clickRate, ");
        sb.append(campaignFieldSumMap.get(DashboardDataFieldEnum.CONVERSION_RATE.getCode())).append(" conversionRate, ");
        sb.append(" ifnull(ROUND(ifnull(sum(r.cost)/ sum(r.clicks), 0), 4), 0) ").append(" cpc ");//cpc
        sb.append(" from ");
        sb.append(this.getJdbcHelper().getTable()).append(" r ");
        sb.append(" join (select * from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency and puid = ? and `to` = ? ");
        sb.append(" and c.month >= ").append(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
        sb.append(" and c.month <= ").append(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(currency);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append(SqlStringUtil.dealInList("m.marketplace_id", marketplaceIdList, argsList));
        }
        sb.append(") c");
        sb.append(" on c.marketplace_id = r.marketplace_id ");
        sb.append(" and r.puid = ? ");
        argsList.add(puid);
        sb.append(" and r.count_month = c.month ");
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("r.shop_id", shopIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append(SqlStringUtil.dealInList("r.marketplace_id", marketplaceIdList, argsList));
        }
        sb.append(" and r.is_summary = 1 ");

        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', r.marketplace_id, r.count_day)", siteToday, argsList));
            sb.append(" and r.count_day >= ? and r.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and r.count_day >= ? and r.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            sb.append(" and r.campaign_id in ('").append(StringUtils.join(campaignIdList, "','")).append("') ");
        }
        if (Boolean.TRUE.equals(noZero)) {
            sb.append(" and ").append(campaignFieldSumMap.get(orderBy)).append(" <> 0 ");
        }

        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" where r.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }

        sb.append(" group by campaign_id ");
        if (StringUtil.isNotEmpty(orderBy) && campaignFieldSumMap.containsKey(orderBy)) {
            sb.append(" order by ").append(campaignFieldSumMap.get(orderBy)).append(" desc ");
        }
        if (limit != null) {
            sb.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AmazonAdCampaignAllReportBo.class));
    }

    @Override
    public List<AmazonAdCampaignAllReportBo> campaignAllReportBoByIds(Integer puid, List<Integer> shopIdList, List<String> campaignIdList, String startDate, String endDate) {
        StringBuilder sb = new StringBuilder();
        sb.append(" select any(campaign_id) campaignId, ")
                .append(campaignFieldSumMap.get(DashboardDataFieldEnum.COST.getCode())).append(" cost,")
                .append(campaignFieldSumMap.get(DashboardDataFieldEnum.CLICKS.getCode())).append(" clicks,")
                .append(campaignFieldSumMap.get(DashboardDataFieldEnum.IMPRESSIONS.getCode())).append(" impressions,")
                .append(campaignFieldSumMap.get(DashboardDataFieldEnum.TOTAL_SALES.getCode())).append(" totalSales,")
                .append(campaignFieldSumMap.get(DashboardDataFieldEnum.ORDER_NUM.getCode())).append(" orderNum,")
                .append(campaignFieldSumMap.get(DashboardDataFieldEnum.SALE_NUM.getCode())).append(" saleNum ")
                .append(" from ").append(this.getJdbcHelper().getTable()).append(" r ");
        List<Object> argsList = new ArrayList<>();
        sb.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            sb.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
        }
        if (startDate != null) {
            sb.append(" and count_day >= ? ");
            argsList.add(startDate);
        }
        if (endDate != null) {
            sb.append(" and count_day <= ? ");
            argsList.add(endDate);
        }
        sb.append(" and is_summary = 1 ");
        sb.append(" group by campaign_id ");
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AmazonAdCampaignAllReportBo.class));
    }

    private StringBuilder getEffectDataSql(Integer puid, List<Integer> shopIdList, List<String> marketplaceIdList, String currency,
                                           String startDate, String endDate, List<Object> argsList, boolean isAggregate, List<String> siteToday, Boolean isSiteToday,
                                           List<String> portfolioIds, List<String> campaignIds, boolean grouByShop) {
        StringBuilder sb = new StringBuilder("select ");
        if (!isAggregate) {
            sb.append(" r.count_day date, ");
            if (grouByShop) {
                sb.append(" r.shop_id shopId, ");
            }
        }

        if (grouByShop) {
            sb.append(" ifnull(sum(r.cost), 0) origCost, ifnull(sum(if(r.type='sp', r.sales7d, r.sales14d)), 0) origTotalSales, ");
        }

        sb.append(" ifnull(sum(r.cost * c.rate), 0) cost, ifnull(sum(if(r.type='sp', r.sales7d, r.sales14d) * c.rate), 0) totalSales, ")
                .append(" ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ifnull(sum(if (type = 'sp' , conversions7d,conversions14d)), 0) orderNum ");
        // 增加广告销量字段
        sb.append(",IFNULL(sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))),0) saleNum ");

        sb.append(" from ").append(this.getJdbcHelper().getTable()).append(" r ");
        sb.append(" join (select m.marketplace_id,c.month,c.rate from dim_currency_rate c join dim_marketplace_info m ");
        sb.append(" on c.`from` = m.currency and c.puid= ? and `to` = ? and month >= ? and month <= ? ");
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and m.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        sb.append(" ) c ");
        argsList.add(puid);
        argsList.add(currency);
        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            LocalDate now = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
            argsList.add(now.plusDays(-3).format(formatter));
            argsList.add(now.plusDays(1).format(formatter));
        } else {
            argsList.add(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
            argsList.add(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        }
        sb.append(" on r.marketplace_id = c.marketplace_id and r.count_month = c.month and r.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and r.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("r.shop_id", shopIdList, argsList));
        }


        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', r.marketplace_id, r.count_day)", siteToday, argsList));
            sb.append(" and r.count_day >= ? and r.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-3).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and r.count_day >= ? and r.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("r.campaign_id", campaignIds, argsList));
        }

        sb.append(" and r.is_summary = 1 ");


        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" where r.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }
        if (!isAggregate) {
            sb.append(" group by r.count_day  ");
            if (grouByShop) {
                sb.append(" ,r.shop_id ");
            }
            sb.append(" order by r.count_day ");
        }

        return sb;
    }

    @Override
    public String campaignQuerySql(Integer puid, List<Integer> shopIdList,
                                              List<String> marketplaceIdList, List<String> campaignIdList,
                                              String currency,String startDate, String endDate,
                                              List<Object> argsList, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum) {
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT report.campaign_id as campaignId, ");
        sb.append(" ifnull(sum(cost * c.rate), 0) cost, ifnull(sum(if(type='sp', sales7d, sales14d) * c.rate), 0) totalSales, ");
        sb.append(" ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ");
        sb.append(" ifnull(sum(if (type = 'sp', conversions7d,conversions14d)), 0) orderNum, ");
        sb.append(" ifnull(sum(if (type = 'sp' , units_ordered7d, if (type = 'sb' , units_sold14d, units_ordered14d))), 0) saleNum, ");
        sb.append(" ifnull(ROUND(ifnull(sum(cost * c.rate), 0)/ ifnull(sum(if(type = 'sp', sales7d, sales14d) * c.rate), 0), 4), 0) acos, ");
        sb.append(" ifnull(ROUND(ifnull(sum(if(type = 'sp', sales7d, sales14d) * c.rate), 0)/ ifnull(sum(cost * c.rate), 0), 4), 0) roas, ");
        sb.append(" ifnull(ROUND(ifnull(sum(clicks)/ sum(impressions), 0), 4), 0) clickRate, ");//点击率
        sb.append(" ifnull(ROUND(ifnull(sum(if (type = 'sp', conversions7d, conversions14d))/ sum(clicks), 0), 4), 0) conversionRate, ");//转化率
        sb.append(" ifnull(ROUND(ifnull(sum(cost * c.rate)/ sum(clicks), 0), 4), 0) cpc, ");//cpc
        sb.append(" ifnull(ROUND(ifnull(sum(cost * c.rate)/ sum(if (type = 'sp', conversions7d, conversions14d)), 0), 4), 0) cpa ");//cpa
        sb.append(" from ").append(getJdbcHelper().getTable()).append("  report ");

        sb.append(" join (select m.marketplace_id,c.month,c.rate from dim_currency_rate c join dim_marketplace_info m ");
        sb.append(" on c.`from` = m.currency and c.puid= ? and `to` = ? and month >= ? and month <= ? ");
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and m.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        sb.append(" ) c ");
        argsList.add(puid);
        argsList.add(currency);
        argsList.add(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        argsList.add(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        sb.append(" on report.marketplace_id = c.marketplace_id and report.count_month = c.month and report.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and report.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and report.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        sb.append(" and report.is_summary = 1 ");
        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', report.marketplace_id, report.count_day)", siteToday, argsList));
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            sb.append(" and report.campaign_id in ('").append(StringUtils.join(campaignIdList, "','")).append("') ");
        }else {
            PermissionSqlBuilder.addPermissionFilter(sb, argsList, "report.campaign_id" , true);
        }

        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" where report.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }
        sb.append(" group by report.campaign_id");
        return sb.toString();
    }

    @Override
    public String rankQuerySql(Integer puid, List<Integer> shopIdList,
                                   List<String> marketplaceIdList, List<String> idList,
                                   String currency,String startDate, String endDate,
                                   List<Object> argsList, Integer queryField, List<String> siteToday,
                               Boolean isSiteToday, List<String> portfolioIds , List<String> campaignIds,Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum) {
        String groupByField = "";
        if (DashboardRankQueryFieldEnum.SHOP_QUERY_TYPE.getCode().equals(queryField)) {
            groupByField = "shop_id";
        } else if (DashboardRankQueryFieldEnum.MARKETPLACE_QUERY_TYPE.getCode().equals(queryField)) {
            groupByField = "marketplace_id";
        }
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT report.").append(groupByField).append(" id, ");
        sb.append(" ifnull(sum(cost * c.rate), 0) cost, ifnull(sum(if(type='sp', sales7d, sales14d) * c.rate), 0) totalSales, ");
        sb.append(" ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ");
        sb.append(" ifnull(sum(if (type = 'sp', conversions7d,conversions14d)), 0) orderNum, ");
        sb.append(" ifnull(sum(if (type = 'sp' , units_ordered7d, if (type = 'sb' , units_sold14d, units_ordered14d))), 0) saleNum, ");
        sb.append(" ifnull(ROUND(ifnull(sum(cost * c.rate), 0)/ ifnull(sum(if(type = 'sp', sales7d, sales14d) * c.rate), 0), 4), 0) acos, ");
        sb.append(" ifnull(ROUND(ifnull(sum(if(type = 'sp', sales7d, sales14d) * c.rate), 0)/ ifnull(sum(cost * c.rate), 0), 4), 0) roas, ");
        sb.append(" ifnull(ROUND(ifnull(sum(clicks)/ sum(impressions), 0), 4), 0) clickRate, ");//点击率
        sb.append(" ifnull(ROUND(ifnull(sum(if (type = 'sp', conversions7d, conversions14d))/ sum(clicks), 0), 4), 0) conversionRate, ");//转化率
        sb.append(" ifnull(ROUND(ifnull(sum(cost * c.rate)/ sum(clicks), 0), 4), 0) cpc, ");//cpc
        sb.append(" ifnull(ROUND(ifnull(sum(cost * c.rate)/ sum(if (type = 'sp', conversions7d, conversions14d)), 0), 4), 0) cpa ");//cpa
        sb.append(" from ").append(getJdbcHelper().getTable()).append("  report ");
        sb.append(" join (select m.marketplace_id,c.month,c.rate from dim_currency_rate c join dim_marketplace_info m ");
        sb.append(" on c.`from` = m.currency and c.puid= ? and `to` = ? and month >= ? and month <= ? ");
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and m.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        sb.append(" ) c ");
        argsList.add(puid);
        argsList.add(currency);
        argsList.add(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        argsList.add(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        sb.append(" on report.marketplace_id = c.marketplace_id and DATE_FORMAT(report.count_date, '%Y%m') = c.month and report.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and report.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and report.shop_id in (").append(StringUtils.join(shopIdList, ",")).append(") ");
        }


        sb.append(" and report.is_summary = 1 ");
        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws(',', report.marketplace_id, report.count_day)", siteToday, argsList));
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(idList)) {
            sb.append(SqlStringUtil.dealInList("report." + groupByField, idList, argsList));
        }

//        if (CollectionUtils.isNotEmpty(campaignIds)) {
//            sb.append(SqlStringUtil.dealDorisInList("report.campaign_Id", campaignIds, argsList));
//        }
//
//        if (CollectionUtils.isNotEmpty(portfolioIds)) {
//            sb.append(" where report.campaign_id in ( ");
//            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
//            argsList.add(puid);
//            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
//                if (portfolioIds.size() == 1) {
//                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
//                } else {
//                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
//                    pr.add("");
//                    sb.append(" and ( ").append(" portfolio_id is null ")
//                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
//                            .append( " )  ");
//                }
//            } else {
//                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
//            }
//            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
//                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
//            }
//            if (CollectionUtils.isNotEmpty(shopIdList)) {
//                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
//            }
//
//            sb.append(" ) ");
//        }

        sb.append(" group by report.").append(groupByField);
        return sb.toString();
    }


    /**
     * 用户排除为0 的字段处理
     * @param orderByField
     * @return
     */
    private String getColumn(String orderByField) {

        switch (orderByField) {
            case "totalSales":
                return " sales7d <> 0 and sales14d <> 0 ";
            case "orderNum":
                return " conversions7d <> 0 and conversions14d <> 0)";
            case "saleNum":
                return " units_sold14d <> 0 and units_ordered14d <> 0)";
            default:
                return orderByField + " <> 0 " ;
        }
    }



    /**
     * 用户排除为0 的字段处理
     *
     * @param orderByField
     * @return
     */
    private String getColumnSelect(String orderByField) {

        switch (orderByField) {
            case "impressions":
                return " ifnull(subSqlA.impressions, 0) <> 0 ";
            case "clicks":
                return " ifnull(subSqlA.clicks, 0) <> 0 ";
            case "cost":
                return " ifnull(subSqlA.cost, 0) <> 0 ";
            case "roas":
                return " ifnull(subSqlA.roas, 0) <> 0 ";
            case "acos":
                return " ifnull(subSqlA.acos, 0) <> 0 ";
            case "clickRate":
                return " ifnull(subSqlA.clickRate, 0) <> 0 ";
            case "conversionRate":
                return " ifnull(subSqlA.conversionRate, 0.0) <> 0 ";
            case "cpc":
                return " ifnull(subSqlA.cpc, 0) <> 0 ";
            case "cpa":
                return " ifnull(subSqlA.cpa, 0) <> 0 ";
            case "totalSales":
                return " ifnull(subSqlA.totalSales, 0) <> 0 ";
            case "orderNum":
                return " ifnull(subSqlA.orderNum, 0) <> 0 ";
            case "saleNum":
                return " ifnull(subSqlA.saleNum, 0) <> 0 ";
            default:
                return orderByField + " <> 0 ";
        }
    }


    @Override
    public List<AmazonAdCampaignAllReportBo> getAdOrderNumGroupByCountDayList(Integer puid, List<Integer> shopIdList,
                                                                              List<String> marketplaceIdList, List<String> campaignIdList,
                                                                              String startDate, String endDate, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds) {
        StringBuilder sb = new StringBuilder();
        List<Object> argsList = new ArrayList<>();
        sb.append(" SELECT count_day, ");
        sb.append(" ifnull(sum(if (type = 'sp', conversions7d,conversions14d)), 0) orderNum ");
        sb.append(" from ").append(getJdbcHelper().getTable()).append("  report ");
        sb.append(" where report.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and report.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and report.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        sb.append(" and report.is_summary = 1 ");
        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', report.marketplace_id, report.count_day)", siteToday, argsList));
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            sb.append(" and report.campaign_id in ('").append(StringUtils.join(campaignIdList, "','")).append("') ");
        }

        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" and report.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }
        sb.append(" group by report.count_day");
        return getJdbcTemplate().query(sb.toString(), new BeanPropertyRowMapper<>(AmazonAdCampaignAllReportBo.class),argsList.toArray());
    }

    @Override
    public List<AmazonAdCampaignAllReport> getReportByCampaignIdListAll(Integer puid, List<Integer> shopIdList, String startDate,
                                                                        String endDate, List<String> campaignIdList, Boolean changeRate) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder selectSql = new StringBuilder("SELECT cost_type,shop_id,count_date,marketplace_id,type,campaign_id,campaign_name,")
                .append(" clicks, ")
                .append(" `impressions`, ")
                .append(" if (type = 'sp' , conversions7d,conversions14d) order_num, ")
                .append(" if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`) ad_order_num, ")
                .append(" if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`)) sale_num, ")
                .append(" if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`) ad_sale_num, ")
                .append(" view_impressions, detail_page_view14d,")
                .append(" orders_new_to_brand14d, orders_new_to_brand_percentage14d,order_rate_new_to_brand14d,")
                .append(" sales_new_to_brand_percentage14d,units_ordered_new_to_brand14d, units_ordered_new_to_brand_percentage14d ,")
                .append(" vctr, video5second_view_rate, video5second_views, video_first_quartile_views,")
                .append(" video_midpoint_views, video_third_quartile_views, video_unmutes, viewable_impressions,")
                .append(" video_complete_views, vtr,");
        if(changeRate){
            selectSql.append(" cost*d.rate cost, ")
                    .append(" if (type = 'sp', sales7d,sales14d)*d.rate total_sales, ")
                    .append(" if (type = 'sp', sales7d_same_sku,sales14d_same_sku)*d.rate ad_sales, ")
                    .append(" sales_new_to_brand14d*d.rate sales_new_to_brand14d ");
        }else{
            selectSql.append(" cost, ")
                    .append(" if (type = 'sp', sales7d,sales14d) total_sales, ")
                    .append(" if (type = 'sp', sales7d_same_sku,sales14d_same_sku) ad_sales, ")
                    .append(" sales_new_to_brand14d ");
        }
        selectSql.append(" FROM ods_t_amazon_ad_campaign_all_report r ");
        if(changeRate){
            String start = DateUtil.dateToStrWithFormat(DateUtil.strToDate(startDate, "yyyy-MM-dd"), "yyyyMM");
            String end = DateUtil.dateToStrWithFormat(DateUtil.strToDate(endDate, "yyyy-MM-dd"), "yyyyMM");
            // 关联汇率表
            selectSql.append(" join (select rate,month,`from`,puid from dim_currency_rate ");
            selectSql.append(" where puid = ? and `to` = 'USD' and month >= ? and month <= ?  ) d ");
            selectSql.append(" on d.puid = r.puid and d.month = r.count_month and  d.`from` = r.currency ");
            argsList.add(puid);
            argsList.add(start);
            argsList.add(end);
        }
        StringBuilder whereSql = new StringBuilder(" where r.puid=?  and r.count_day >=? and r.count_day <= ? and r.is_summary = 1 ");
        argsList.add(puid);
        argsList.add(startDate);
        argsList.add(endDate);
        whereSql.append(SqlStringUtil.dealDorisInList("r.shop_id", shopIdList, argsList));
        if(CollectionUtils.isNotEmpty(campaignIdList)){
            whereSql.append(SqlStringUtil.dealDorisInList("r.campaign_id", campaignIdList, argsList));
        }
        selectSql.append(whereSql);
        return getJdbcTemplate().query(selectSql.toString(), new BeanPropertyRowMapper<>(AmazonAdCampaignAllReport.class),argsList.toArray());
    }
    @Override
    public List<CampaignTagDataReportDto> getCampaignTagData(Integer puid, CampaignTagDataParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select t.id tagId, any(t.group_id) groupId, GROUP_CONCAT(DISTINCT CAST(tr.shop_id AS CHAR)) shopIds, count(distinct tr.relation_id) campaignCount, ")
                .append(this.getCampaignTagReportCurrencyRateDataSelectSql());
        sb.append(" from ods_t_ad_manage_tag t ");
        //连接关联表
        sb.append(" left join ods_t_ad_manage_tag_relation tr  on tr.puid = t.puid and tr.tag_id = t.id ");
        sb.append(" and tr.puid = ? ");
        argsList.add(puid);
        sb.append(SqlStringUtil.dealInList("tr.shop_id", param.getShopIdList(), argsList));
        sb.append(" and tr.type = ? and tr.del_flag = 0 ");
        argsList.add(AdManageTagTypeEnum.CAMPAIGN.getCode());
        if (CollectionUtils.isNotEmpty(param.getGroupIds())) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("tr.group_id", param.getGroupIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getTagIdList())) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("tr.tag_id", param.getTagIdList(), argsList));
        }
        //连接报告表
        sb.append(" left join ");
        sb.append(" (select any(puid) puid, any(marketplace_id) marketplace_id, campaign_id, count_month, any(type) type, sum(cost) cost, sum(impressions) impressions, sum(clicks) clicks, sum(conversions7d) conversions7d, sum(conversions14d) conversions14d, sum(conversions7d_same_sku) conversions7d_same_sku, sum(conversions14d_same_sku) conversions14d_same_sku, sum(sales7d) sales7d, sum(sales14d) sales14d, sum(sales7d_same_sku) sales7d_same_sku, sum(sales14d_same_sku) sales14d_same_sku, sum(units_ordered7d) units_ordered7d, sum(units_sold14d) units_sold14d, sum(units_ordered14d) units_ordered14d, sum(units_ordered7d_same_sku) units_ordered7d_same_sku, sum(orders_new_to_brand14d) orders_new_to_brand14d, sum(sales_new_to_brand14d) sales_new_to_brand14d, sum(units_ordered_new_to_brand14d) units_ordered_new_to_brand14d ")
                .append(" from ods_t_amazon_ad_campaign_all_report ");
        sb.append(" where puid = ? ");
        argsList.add(puid);
        sb.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), argsList));
        if (CollectionUtils.isNotEmpty(param.getMarketplaceIdList())) {
            sb.append(SqlStringUtil.dealInList("marketplace_id", param.getMarketplaceIdList(), argsList));
        }
        sb.append(" and count_day >= ? and count_day <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        sb.append(" and is_summary = 1 ");
        sb.append(" group by campaign_id, count_month) r ");
        sb.append(" on r.puid = t.puid and r.campaign_id = tr.relation_id");
        //关联货币汇率表
        sb.append(this.getCampaignTagDataJoinCurreryRateSql(puid, param, argsList));
        //标签组表条件
        sb.append(" where t.puid = ? and t.type = ? and t.del_flag = 0 ");
        argsList.add(puid);
        argsList.add(AdManageTagTypeEnum.CAMPAIGN.getCode());
        if (CollectionUtils.isNotEmpty(param.getGroupIds())) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("t.group_id", param.getGroupIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getTagIdList())) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("t.id", param.getTagIdList(), argsList));
        }
        sb.append(" group by t.id ");
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {
            sb.append(this.getCampaignTagDataHavingSql(param.getShopSales(), param.getAdvanceFilter(), argsList));
        }
        sb.append(" order by any(t.sort) ");
        return getJdbcTemplate().query(sb.toString(), new BeanPropertyRowMapper<>(CampaignTagDataReportDto.class), argsList.toArray());
    }

    @Override
    public List<CampaignTagDataReportDto> getCampaignTagDataByTagIdList(Integer puid, CampaignTagDataParam param, List<Long> tagIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select t.tag_id tagId, any(t.group_id) groupId, count(distinct t.relation_id) campaignCount, ")
                .append(this.getCampaignTagReportCurrencyRateDataSelectSql());
        sb.append(" from ods_t_ad_manage_tag_relation t ");
        //连接报告表
        sb.append(" left join ods_t_amazon_ad_campaign_all_report r on r.puid = t.puid and r.campaign_id = t.relation_id");
        sb.append(" and r.puid = ? ");
        argsList.add(puid);
        sb.append(SqlStringUtil.dealInList("r.shop_id", param.getShopIdList(), argsList));
        if (CollectionUtils.isNotEmpty(param.getMarketplaceIdList())) {
            sb.append(SqlStringUtil.dealInList("r.marketplace_id", param.getMarketplaceIdList(), argsList));
        }
        sb.append(" and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        sb.append(" and is_summary = 1 ");
        //关联货币汇率表
        sb.append(this.getCampaignTagDataJoinCurreryRateSql(puid, param, argsList));
        //标签组表条件
        sb.append(" where t.puid = ? ");
        argsList.add(puid);
        sb.append(SqlStringUtil.dealInList("t.shop_id", param.getShopIdList(), argsList));
        sb.append(" and t.type = ? and t.del_flag = 0 ");
        argsList.add(AdManageTagTypeEnum.CAMPAIGN.getCode());
        if (CollectionUtils.isNotEmpty(tagIdList)) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("t.tag_id", tagIdList, argsList));
        }
        sb.append(" group by t.tag_id ");
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(CampaignTagDataReportDto.class));
    }

    @Override
    public List<AdBaseReportDto> getCampaignTagChartData(Integer puid, CampaignTagDataParam param, List<Long> tagIdList, int distinctCount) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select any(count_date) countDate,")
                .append(this.getCampaignTagReportCurrencyRateDataSelectSql())
                .append(" from ods_t_amazon_ad_campaign_all_report r ");
        //关联货币汇率表
        sb.append(this.getCampaignTagDataJoinCurreryRateSql(puid, param, argsList));
        sb.append(" where r.puid = ? ");
        argsList.add(puid);
        sb.append(SqlStringUtil.dealInList("r.shop_id", param.getShopIdList(), argsList));
        if (CollectionUtils.isNotEmpty(param.getMarketplaceIdList())) {
            sb.append(SqlStringUtil.dealInList("r.marketplace_id", param.getMarketplaceIdList(), argsList));
        }
        sb.append(" and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        sb.append(" and r.campaign_id in (");
        //子查询根据tag_id获取广告活动id
        sb.append("select relation_id from ods_t_ad_manage_tag_relation ")
                .append(" where puid = ? and type = ? and del_flag = 0 ");
        argsList.add(puid);
        argsList.add(AdManageTagTypeEnum.CAMPAIGN.getCode());
        if (CollectionUtils.isNotEmpty(tagIdList)) {
            sb.append(SqlStringUtil.dealInList("tag_id", tagIdList, argsList));
        }
        sb.append(" group by relation_id having count(tag_id) = ? ) ");

        argsList.add(distinctCount);
        sb.append(" and is_summary = 1 ");
        sb.append(" group by r.count_day ");
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AdBaseReportDto.class));
    }

    @Override
    public List<Integer> selectValidShopIds(Integer puid, List<Integer> shopIds, DateTime startDateTime, DateTime endTime) {
        if (CollectionUtils.isEmpty(shopIds)) {
            return new ArrayList<>();
        }
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopIds.toArray(new Integer[]{}))
                .greaterThanOrEqualTo("count_day", startDateTime.toString("yyyy-MM-dd"))
                .lessThanOrEqualTo("count_day", endTime.toString("yyyy-MM-dd"))
                .greaterThan("impressions", 0);
        return listDistinctFieldByCondition("shop_id", builder.build(), Integer.class);
    }

    private String getCampaignTagDataJoinCurreryRateSql(Integer puid, CampaignTagDataParam param, List<Object> argsList) {
        StringBuilder sb = new StringBuilder();
        //关联币种汇率表
        sb.append(" left join ( select m.marketplace_id,c.month,c.rate from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency ")
                .append(" and c.puid= ? and `to` = ? and month >= ? and month <= ? ");
        argsList.add(puid);
        argsList.add(param.getCurrency());
        argsList.add(DateUtil.dateStringFormat(param.getStartDate(), DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        argsList.add(DateUtil.dateStringFormat(param.getEndDate(), DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        if (CollectionUtils.isNotEmpty(param.getMarketplaceIdList())) {
            sb.append(SqlStringUtil.dealInList("marketplace_id", param.getMarketplaceIdList(), argsList));
        }
        sb.append(" ) c on r.marketplace_id = c.marketplace_id and r.count_month = c.month ");
        return sb.toString();
    }

    private String getCampaignTagReportCurrencyRateDataSelectSql() {
        StringBuilder sb = new StringBuilder();
        sb.append(" ifnull(sum(r.cost * c.rate), 0) sumAdCost, ifnull(sum(impressions), 0) sumImpressions, ifnull(sum(clicks), 0) sumClicks,")
                .append(" ifnull(sum(if (r.type = 'sp', conversions7d, conversions14d)), 0) sumAdOrderNum,")
                .append(" ifnull(sum(if (r.type = 'sp', conversions7d_same_sku, conversions14d_same_sku)), 0) sumSelfAdOrderNum,")
                .append(" ifnull(sum(if (r.type = 'sp', r.sales7d, r.sales14d) * c.rate), 0) sumAdSale,")
                .append(" ifnull(sum(if (r.type = 'sp', sales7d_same_sku, sales14d_same_sku) * c.rate), 0) sumAdSelfSale,")
                .append(" ifnull(sum(if (r.type = 'sp', units_ordered7d,if (r.type = 'sb' ,units_sold14d,units_ordered14d))), 0) sumAdSaleNum,")
                .append(" ifnull(sum(if (r.type = 'sp', units_ordered7d_same_sku, 0)), 0) sumAdSelfSaleNum,")
                .append(" ifnull(sum(if (r.type = 'sp', units_ordered7d - units_ordered7d_same_sku, 0)), 0) sumAdOtherSaleNum,")
                .append(" ifnull(sum(orders_new_to_brand14d), 0) sumOrdersNewToBrandFTD,")
                .append(" ifnull(sum(sales_new_to_brand14d * c.rate), 0) sumSalesNewToBrandFTD,")
                .append(" ifnull(sum(units_ordered_new_to_brand14d), 0) sumUnitsOrderedNewToBrandFTD");
        return sb.toString();
    }

    private String getCampaignTagDataOrderField(String field) {
        switch (field) {
            case "campaignCount":
                return "campaignCount";
            case "adCost": //花费
            case "acots":
                return "adCost";
            case "impressions":  //曝光量
                return "impressions";
            case "clicks":  //点击量
                return "clicks";
            case "cpa": //cpa
                return "adCost/adOrderNum";
            case "adCostPerClick":  //cpc
                return "adCost/clicks";
            case "ctr":  //广告点击率
                return "clicks/impressions";
            case "cvr":  //订单转化率
                return "adOrderNum/clicks";
            case "acos":  //acos
                return "adCost/adSale";
            case "roas":  //roas
                return "adSale/adCost";
            case "advertisingUnitPrice":  //广告笔单价
                return "adSale/adOrderNum";
            case "adOrderNum":  //广告订单量
                return "adOrderNum";
            case "adSale":  //广告销售额
            case "asots":
                return "adSale";
            case "adSaleNum": //广告销量
                return "adSaleNum";
            case "selfAdOrderNum": //本广告产品订单量
                return "selfAdOrderNum";
            case "otherAdOrderNum": //其他产品广告订单量
                return "adOrderNum - selfAdOrderNum";
            case "adSelfSale": //本广告产品销售额
                return "adSelfSale";
            case "adOtherSales": //其他产品广告销售额
                return "adSelfSale - adOtherSales";
            case "adSelfSaleNum": //本产品广告销量
                return "adSelfSaleNum";
            case "adOtherSaleNum": //其他产品广告销量
                return "adSaleNum - adOtherSaleNum";
            case "ordersNewToBrandFTD": //“品牌新买家”订单量
                return "ordersNewToBrandFTD";
            case "orderRateNewToBrandFTD": //“品牌新买家”订单百分比
                return "ordersNewToBrandFTD/adOrderNum";
            case "salesNewToBrandFTD": //“品牌新买家”销售额
                return "salesNewToBrandFTD";
            case "salesRateNewToBrandFTD": //“品牌新买家”销售额百分比
                return "salesNewToBrandFTD/adSale";
            case "unitsOrderedNewToBrandFTD": //“品牌新买家”销量
                return "unitsOrderedNewToBrandFTD";
            case "unitsOrderedRateNewToBrandFTD": //“品牌新买家”销量百分比
                return "unitsOrderedNewToBrandFTD/adSaleNum";
            default:
                return "adCost";
        }
    }

    private String getCampaignTagDataHavingSql(BigDecimal shopSales, AdvanceFilter advanceFilter, List<Object> argsList) {
        StringBuilder sb = new StringBuilder(" having 1=1 ");
        if (shopSales == null) {
            shopSales = BigDecimal.ZERO;
        }
        //花费
        if (advanceFilter.getAdCostMin() != null) {
            sb.append(" and sumAdCost >= ?");
            argsList.add(advanceFilter.getAdCostMin());
        }
        if (advanceFilter.getAdCostMax() != null) {
            sb.append(" and sumAdCost <= ?");
            argsList.add(advanceFilter.getAdCostMax());
        }
        //曝光量
        if (advanceFilter.getImpressionsMin() != null) {
            sb.append(" and sumImpressions >= ?");
            argsList.add(advanceFilter.getImpressionsMin());
        }
        if (advanceFilter.getImpressionsMax() != null) {
            sb.append(" and sumImpressions <= ?");
            argsList.add(advanceFilter.getImpressionsMax());
        }
        //点击量
        if (advanceFilter.getClicksMin() != null) {
            sb.append(" and sumClicks >= ?");
            argsList.add(advanceFilter.getClicksMin());
        }
        if (advanceFilter.getClicksMax() != null) {
            sb.append(" and sumClicks <= ?");
            argsList.add(advanceFilter.getClicksMax());
        }
        //CPA
        if (advanceFilter.getCpaMin() != null) {
            sb.append(" and ROUND(ifnull(sumAdCost/sumAdOrderNum, 0), 2) >= ? ");
            argsList.add(advanceFilter.getCpaMin());
        }
        if (advanceFilter.getCpaMax() != null) {
            sb.append(" and ROUND(ifnull(sumAdCost/sumAdOrderNum, 0), 2) <= ? ");
            argsList.add(advanceFilter.getCpaMax());
        }
        //cpc  平均点击费用
        if (advanceFilter.getAdCostPerClickMin() != null) {
            sb.append(" and ROUND(ifnull(sumAdCost/sumClicks,0),2) >= ?");
            argsList.add(advanceFilter.getAdCostPerClickMin());
        }
        if (advanceFilter.getAdCostPerClickMax() != null) {
            sb.append(" and ROUND(ifnull(sumAdCost/sumClicks,0),2) <= ?");
            argsList.add(advanceFilter.getAdCostPerClickMax());
        }
        //点击率（clicks/impressions）
        if (advanceFilter.getCtrMin() != null) {
            sb.append(" and ROUND(ifnull(sumClicks/sumImpressions * 100,0),2) >= ?");
            argsList.add(advanceFilter.getCtrMin());
        }
        if (advanceFilter.getCtrMax() != null) {
            sb.append(" and ROUND(ifnull(sumClicks/sumImpressions * 100,0),2) <= ?");
            argsList.add(advanceFilter.getCtrMax());
        }
        //订单转化率
        if (advanceFilter.getCvrMin() != null) {
            sb.append(" and ROUND(ifnull(sumAdOrderNum/sumClicks * 100,0),2) >= ?");
            argsList.add(advanceFilter.getCvrMin());
        }
        if (advanceFilter.getCvrMax() != null) {
            sb.append(" and ROUND(ifnull(sumAdOrderNum/sumClicks * 100,0),2) <= ?");
            argsList.add(advanceFilter.getCvrMax());
        }
        //acos
        if (advanceFilter.getAcosMin() != null) {
            sb.append(" and ROUND(ifnull(sumAdCost/sumAdSale * 100,0),2) >= ?");
            argsList.add(advanceFilter.getAcosMin());
        }
        if (advanceFilter.getAcosMax() != null) {
            sb.append(" and ROUND(ifnull(sumAdCost/sumAdSale * 100,0),2) <= ?");
            argsList.add(advanceFilter.getAcosMax());
        }
        // roas
        if (advanceFilter.getRoasMin() != null) {
            sb.append(" and ROUND(ifnull(sumAdSale/sumAdCost,0),2) >= ?");
            argsList.add(advanceFilter.getRoasMin());
        }
        // roas
        if (advanceFilter.getRoasMax() != null) {
            sb.append(" and ROUND(ifnull(sumAdSale/sumAdCost,0),2) <= ?");
            argsList.add(advanceFilter.getRoasMax());
        }
        // acots  需要乘以店铺销售额
        if (advanceFilter.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sumAdCost,0) / ").append(shopSales).append(" * 100 ),2) >= ? ");
                argsList.add(advanceFilter.getAcotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(advanceFilter.getAcotsMin());
            }
        }
        // acots  需要乘以店铺销售额
        if (advanceFilter.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sumAdCost,0) / ").append(shopSales).append(" * 100 ),2) <= ? ");
                argsList.add(advanceFilter.getAcotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(advanceFilter.getAcotsMin());
            }
        }
        // asots 需要乘以店铺销售额
        if (advanceFilter.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sumAdSale,0) / ").append(shopSales).append(" * 100 ),2) >= ? ");
                argsList.add(advanceFilter.getAsotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(advanceFilter.getAcotsMin());
            }
        }
        // asots  需要乘以店铺销售额
        if (advanceFilter.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sumAdSale,0) / ").append(shopSales).append(" * 100 ),2) <= ? ");
                argsList.add(advanceFilter.getAsotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(advanceFilter.getAcotsMin());
            }
        }
        // 广告笔单价 筛选
        if (advanceFilter.getAdvertisingUnitPriceMin() != null) {
            sb.append(" and ROUND(ifnull(sumAdSale/sumAdOrderNum, 0), 2) >= ?");
            argsList.add(advanceFilter.getAdvertisingUnitPriceMin());
        }
        if (advanceFilter.getAdvertisingUnitPriceMax() != null) {
            sb.append(" and ROUND(ifnull(sumAdSale/sumAdOrderNum, 0), 2) <= ?");
            argsList.add(advanceFilter.getAdvertisingUnitPriceMax());
        }
        //广告订单量
        if (advanceFilter.getAdOrderNumMin() != null) {
            sb.append(" and sumAdOrderNum >= ?");
            argsList.add(advanceFilter.getAdOrderNumMin());
        }
        if (advanceFilter.getAdOrderNumMax() != null) {
            sb.append(" and sumAdOrderNum <= ?");
            argsList.add(advanceFilter.getAdOrderNumMax());
        }
        //广告销售额
        if (advanceFilter.getAdSaleMin() != null) {
            sb.append(" and sumAdSale >= ?");
            argsList.add(advanceFilter.getAdSaleMin());
        }
        if (advanceFilter.getAdSaleMax() != null) {
            sb.append(" and sumAdSale <= ?");
            argsList.add(advanceFilter.getAdSaleMax());
        }
        //广告销量
        if (advanceFilter.getAdSaleNumMin() != null) {
            sb.append(" and sumAdSaleNum >= ? ");
            argsList.add(advanceFilter.getAdSaleNumMin());
        }
        if (advanceFilter.getAdSaleNumMax() != null) {
            sb.append(" and sumAdSaleNum <= ? ");
            argsList.add(advanceFilter.getAdSaleNumMax());
        }
        //本广告产品订单量（绝对值）
        if (advanceFilter.getSelfAdOrderNumMin() != null) {
            sb.append(" and ifnull(sumSelfAdOrderNum, 0) >= ? ");
            argsList.add(advanceFilter.getSelfAdOrderNumMin());
        }
        if (advanceFilter.getSelfAdOrderNumMax() != null) {
            sb.append(" and ifnull(sumSelfAdOrderNum, 0) <= ? ");
            argsList.add(advanceFilter.getSelfAdOrderNumMax());
        }
        //其他产品广告订单量（绝对值）
        if (advanceFilter.getOtherAdOrderNumMin() != null) {
            sb.append(" and ifnull(sumAdOrderNum - sumSelfAdOrderNum, 0) >= ? ");
            argsList.add(advanceFilter.getOtherAdOrderNumMin());
        }

        if (advanceFilter.getOtherAdOrderNumMax() != null) {
            sb.append(" and ifnull(sumAdOrderNum - sumSelfAdOrderNum, 0) <= ? ");
            argsList.add(advanceFilter.getOtherAdOrderNumMax());
        }
        //本广告产品销售额（绝对值）
        if (advanceFilter.getAdSelfSaleMin() != null) {
            sb.append(" and ifnull(sumAdSelfSale, 0) >= ? ");
            argsList.add(advanceFilter.getAdSelfSaleMin());
        }
        if (advanceFilter.getAdSelfSaleMax() != null) {
            sb.append(" and ifnull(sumAdSelfSale, 0) <= ? ");
            argsList.add(advanceFilter.getAdSelfSaleMax());
        }
        //其他产品广告销售额（绝对值）
        if (advanceFilter.getAdOtherSalesMin() != null) {
            sb.append(" and ifnull(sumAdSale - sumAdSelfSale, 0) >= ? ");
            argsList.add(advanceFilter.getAdOtherSalesMin());
        }
        if (advanceFilter.getAdOtherSalesMax() != null) {
            sb.append(" and ifnull(sumAdSale - sumAdSelfSale, 0) <= ? ");
            argsList.add(advanceFilter.getAdOtherSalesMax());
        }
        //本广告产品销量（绝对值）
        if (advanceFilter.getAdSelfSaleNumMin() != null) {
            sb.append(" and ifnull(sumAdSelfSaleNum, 0) >= ? ");
            argsList.add(advanceFilter.getAdSelfSaleNumMin());
        }
        if (advanceFilter.getAdSelfSaleNumMax() != null) {
            sb.append(" and ifnull(sumAdSelfSaleNum, 0) <= ? ");
            argsList.add(advanceFilter.getAdSelfSaleNumMax());
        }
        //其他产品广告销量（绝对值）
        if (advanceFilter.getAdOtherSaleNumMin() != null) {
            sb.append(" and ifnull(sumAdOtherSaleNum, 0) >= ? ");
            argsList.add(advanceFilter.getAdOtherSaleNumMin());
        }
        if (advanceFilter.getAdOtherSaleNumMax() != null) {
            sb.append(" and ifnull(sumAdOtherSaleNum, 0) <= ? ");
            argsList.add(advanceFilter.getAdOtherSaleNumMax());
        }
        //“品牌新买家”订单量（绝对值）
        if (advanceFilter.getOrdersNewToBrandFTDMin() != null) {
            sb.append(" and ifnull(sumOrdersNewToBrandFTD, 0) >= ? ");
            argsList.add(advanceFilter.getOrdersNewToBrandFTDMin());
        }
        if (advanceFilter.getOrdersNewToBrandFTDMax() != null) {
            sb.append(" and ifnull(sumOrdersNewToBrandFTD, 0) <= ? ");
            argsList.add(advanceFilter.getOrdersNewToBrandFTDMax());
        }
        //“品牌新买家”订单百分比（百分比）
        if (advanceFilter.getOrderRateNewToBrandFTDMin() != null) {
            sb.append(" and ROUND(ifnull((sumOrdersNewToBrandFTD/sumAdOrderNum) * 100, 0), 2) >= ? ");
            argsList.add(advanceFilter.getOrderRateNewToBrandFTDMin());
        }
        if (advanceFilter.getOrderRateNewToBrandFTDMax() != null) {
            sb.append(" and ROUND(ifnull((sumOrdersNewToBrandFTD/sumAdOrderNum) * 100, 0), 2) <= ? ");
            argsList.add(advanceFilter.getOrderRateNewToBrandFTDMax());
        }
        //“品牌新买家”销售额（绝对值）
        if (advanceFilter.getSalesNewToBrandFTDMin() != null) {
            sb.append(" and ifnull(sumSalesNewToBrandFTD, 0) >= ? ");
            argsList.add(advanceFilter.getSalesNewToBrandFTDMin());
        }
        if (advanceFilter.getSalesNewToBrandFTDMax() != null) {
            sb.append(" and ifnull(sumSalesNewToBrandFTD, 0) <= ? ");
            argsList.add(advanceFilter.getSalesNewToBrandFTDMax());
        }
        //“品牌新买家”销售额百分比（百分比）
        if (advanceFilter.getSalesRateNewToBrandFTDMin() != null) {
            sb.append(" and ROUND(ifnull((sumSalesNewToBrandFTD/sumAdSale) * 100, 0), 2) >= ? ");
            argsList.add(advanceFilter.getSalesRateNewToBrandFTDMin());
        }
        if (advanceFilter.getSalesRateNewToBrandFTDMax() != null) {
            sb.append(" and ROUND(ifnull((sumSalesNewToBrandFTD/sumAdSale) * 100, 0), 2) <= ? ");
            argsList.add(advanceFilter.getSalesRateNewToBrandFTDMax());
        }
        //“品牌新买家”销量（绝对值）
        if (advanceFilter.getUnitsOrderedNewToBrandFTDMin() != null) {
            sb.append(" and ifnull(sumUnitsOrderedNewToBrandFTD, 0) >= ? ");
            argsList.add(advanceFilter.getUnitsOrderedNewToBrandFTDMin());
        }
        if (advanceFilter.getUnitsOrderedNewToBrandFTDMax() != null) {
            sb.append(" and ifnull(sumUnitsOrderedNewToBrandFTD, 0) <= ? ");
            argsList.add(advanceFilter.getUnitsOrderedNewToBrandFTDMax());
        }
        //“品牌新买家”销量百分比（百分比）
        if (advanceFilter.getUnitsOrderedRateNewToBrandFTDMin() != null) {
            sb.append(" and ROUND(ifnull((sumUnitsOrderedNewToBrandFTD/sumAdSaleNum) * 100, 0), 2) >= ? ");
            argsList.add(advanceFilter.getUnitsOrderedRateNewToBrandFTDMin());
        }
        if (advanceFilter.getUnitsOrderedRateNewToBrandFTDMax() != null) {
            sb.append(" and ROUND(ifnull((sumUnitsOrderedNewToBrandFTD/sumAdSaleNum) * 100, 0), 2) <= ? ");
            argsList.add(advanceFilter.getUnitsOrderedRateNewToBrandFTDMax());
        }
        return sb.toString();
    }


    @Override
    public List<ShopEffectDataBo> getEffectDataGroupByShop(Integer puid, List<Integer> shopIdList, List<String> marketplaceIdList, String currency, String startDate, String endDate,
                                                           String modelType, List<String> siteToday, Boolean isSiteToday,
                                                           List<String> portfolioIds, List<String> campaignIds) {

        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = getEffectDataSql(puid, shopIdList, marketplaceIdList, currency, startDate, endDate, argsList, false,
                siteToday, isSiteToday, portfolioIds, campaignIds, true);
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(ShopEffectDataBo.class));
    }

    @Override
    public List<CampaignDto> getShopIdsAndCampaignIds(Integer puid, List<String> shopIdList, List<String> tagIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder("select t.id tagId, GROUP_CONCAT(DISTINCT CAST(tr.shop_id AS CHAR)) shopIds, GROUP_CONCAT(DISTINCT CAST(tr.relation_id AS CHAR)) campaignIds ")
                .append(" from ods_t_ad_manage_tag t ");
        //连接关联表
        selectSql.append(" left join ods_t_ad_manage_tag_relation tr on t.puid = tr.puid and tr.tag_id = t.id and tr.puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("tr.shop_id", shopIdList, argsList));
        selectSql.append(" and tr.type = ? and tr.del_flag = 0 ");
        argsList.add(AdManageTagTypeEnum.CAMPAIGN.getCode());
        if (CollectionUtils.isNotEmpty(tagIdList)) {
            selectSql.append(SqlStringUtil.dealInList("tr.tag_id", tagIdList, argsList));
        }
        selectSql.append(" where t.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(tagIdList)) {
            selectSql.append(SqlStringUtil.dealInList("t.id", tagIdList, argsList));
        }
        selectSql.append(" group by t.id ");
        return getJdbcTemplate().query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(CampaignDto.class));
    }



    @Override
    public List<String> queryProductRightSqlGetCampaignIds(List<Object> args, String sql) {
        return getJdbcTemplate().queryForList(sql, args.toArray(), String.class);
    }

    @Override
    public List<AsinOrderNumDto> queryTopSumData4ShopByCountDay(Integer puid, List<Integer> shopIdList, String startDate, String endDate, String fieldName) {
        StringBuilder selectSql = new StringBuilder();
        List<Object> argsList = new ArrayList<>();
        selectSql.append(" select shop_id shopId, sum(").append(fieldName).append(") adOrderNum ");
        selectSql.append(" from ods_t_amazon_ad_campaign_all_report where puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        selectSql.append(" and count_day >= ? and count_day <= ? ");
        argsList.add(startDate);
        argsList.add(endDate);
        selectSql.append(adProductRightService.getProductRightCampaignIdsSqlAnd("campaign_id", puid, shopIdList, Arrays.asList(CampaignTypeEnum.sp), argsList));
        selectSql.append(" group by shop_id order by sum(").append(fieldName).append(") desc limit 1 ");
        return getJdbcTemplate().query(selectSql.toString(), new BeanPropertyRowMapper<>(AsinOrderNumDto.class), argsList.toArray());
    }


}

