package com.meiyunji.sponsored.service.category.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IAdBaseDao;
import com.meiyunji.sponsored.common.springjdbc.IBaseDao;
import com.meiyunji.sponsored.service.category.entity.AmazonAdTargetCategories;
import com.meiyunji.sponsored.service.category.vo.TargetCategoryParam;
import com.meiyunji.sponsored.service.category.vo.TargetCategoryVo;

import java.util.List;

public interface AmazonAdTargetCategoriesDao extends IAdBaseDao<AmazonAdTargetCategories> {
    void batchAdd(List<AmazonAdTargetCategories> list);

    void batchUpdate(List<AmazonAdTargetCategories> list);

    List<AmazonAdTargetCategories> listCategoryByCategoryIds(String marketplaceId, List<Long> categoryIds);

    Page<TargetCategoryVo> listcategoryByKeyword(TargetCategoryParam param);
}
