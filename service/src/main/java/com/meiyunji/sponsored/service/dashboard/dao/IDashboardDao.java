package com.meiyunji.sponsored.service.dashboard.dao;

import com.meiyunji.sponsored.service.dashboard.dto.*;

import java.time.LocalDate;
import java.util.List;

public interface IDashboardDao {


    List<AdContributesDto> getAdContributes(
            Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate,
            LocalDate endDate,  List<String> campaigns,String portfolioId,String status);

    List<AdContributesDto> getAdContributes(
            Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate,
            LocalDate endDate, List<CampaignInfoDto> campaigns);

    List<DashBoardCampaignDto> listCampaignsByPuidAndShopId(
            Integer puid, List<Integer> shopIds, String adType, String name, String portfolioId, String status,
            Integer pageNo, Integer pageSize);

    List<DashBoardCampaignDto> listCampaignsByCompidAndPortfidAndState(
            Integer puid, List<Integer> shopIds, List<String> campaignIds, String portfolioId, String status);

    Integer countAllCampaigns(Integer puid, List<Integer> shopIds, String adType, String name, String portfolioId, String status);

    List<AdContributesDto> getShopSales(Integer puid, List<Integer> shopIds, String currency, LocalDate startDate, LocalDate endDate);

    List<AdvertisingDataDto> getAdvertisingData(
            Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate,
            LocalDate endDate, List<String> campaigns, String portfolioId, String status);

    List<AdvertisingDataDto> getAdvertisingData(Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate, LocalDate endDate, List<CampaignInfoDto> campaigns);

    List<AdPerformanceDto> listCampaignReport(Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate, LocalDate endDate, List<CampaignInfoDto> campaigns);

    List<AdPerformanceDto> listCampaignReport(
            Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate,
            LocalDate endDate, List<String> campaigns, String portfolioId, String status);

    List<AdPerformanceDto> sumCampaignReport(Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate, LocalDate endDate, List<CampaignInfoDto> campaigns);

    List<AdPerformanceDto> sumCampaignReport(
            Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate, LocalDate endDate,
            List<String> campaigns, String portfolioId, String status);

    List<CampaignTopTenDto> getCampaignTopTen(Integer puid, List<Integer> shopIds, String adType, List<String> campaignIds, String currency, String orderBy, LocalDate startDate, LocalDate endDate);

    List<CampaignTopTenDto> getCampaignTopTen(
            Integer puid, List<Integer> shopIds, String adType, List<String> campaigns, String portfolioId, String status, String currency,
            String orderBy, LocalDate startDate, LocalDate endDate);

    List<CampaignTopTenDto> listSumCampaignPerformance(Integer puid, List<Integer> shopIds, String currency, LocalDate startDate, LocalDate endDate);

    List<UserCurrencyRateDto> listCurrencyRateByPuidAndMonth(int puid, String startMonth, String endMonth);

    List<AdQueryWordDto> listSpQueryWord(Integer puid, LocalDate start, LocalDate end, String currency, String orderByField, List<AdQueryTop5Dto> list);

    List<AdQueryWordDto> listSpQueryTargeting(Integer puid, LocalDate start, LocalDate end, String currency, String orderByField, List<AdQueryTop5Dto> list);

    List<AdQueryWordDto> listSbQueryWord(Integer puid, LocalDate start, LocalDate end, String currency, String orderByField, List<AdQueryTop5Dto> list);

    List<UserCurrencyRateDto> listCurrencyRateByPuidAndMonthAndFromAndTo(int puid, String startMonth, String endMonth,List<String> from, String to);
    List<WxCampaignTopTenDto> getWxCampaignTopTen(Integer puid, List<Integer> shopIds, String adType, String currency, String orderBy, LocalDate startDate, LocalDate endDate);

    List<UserCurrencyRateDto> coverRateListByCurrency(int puid, String startMonth, String endMonth, String currency);
}
