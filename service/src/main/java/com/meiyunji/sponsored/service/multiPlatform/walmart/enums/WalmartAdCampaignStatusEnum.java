package com.meiyunji.sponsored.service.multiPlatform.walmart.enums;

import lombok.Getter;

/**
 * @author: ys
 * @date: 2025/2/24 15:28
 * @describe:
 */
@Getter
public enum WalmartAdCampaignStatusEnum {
    ENABLED("enabled", "开启"),//开启
    DISABLED("disabled", "关闭"),//开启
    PAUSED("paused", "暂停"),//暂停
    SCHEDULED("scheduled", "已安排"),//已安排
    LIVE("live", "有效"),//有效
    RESCHEDULED("rescheduled", "重新启动"),
    COMPLETED("completed", "完成"),//完成
    EXTEND("extend", "延期"),//延期
    PROPOSAL("proposal", "草稿")//建议
    ;

    private String status;
    private String msg;

    WalmartAdCampaignStatusEnum(String status, String msg) {
        this.status = status;
        this.msg = msg;
    }

    public static WalmartAdCampaignStatusEnum getWalmartAdCampaignStatusEnumByStatus(String status) {
            WalmartAdCampaignStatusEnum[] values = values();
            for (WalmartAdCampaignStatusEnum value : values) {
                if (value.getStatus().equalsIgnoreCase(status)) {
                    return value;
                }
            }
            return null;
    }
}
