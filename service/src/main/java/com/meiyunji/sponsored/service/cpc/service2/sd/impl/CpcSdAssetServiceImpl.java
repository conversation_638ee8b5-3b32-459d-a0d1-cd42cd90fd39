package com.meiyunji.sponsored.service.cpc.service2.sd.impl;

import com.amazon.advertising.assets.entity.searchAssets.SearchAssetsResult;
import com.amazon.advertising.assets.enums.AssetTypeEnum;
import com.amazon.advertising.assets.enums.FilterFieldEnum;
import com.amazon.advertising.assets.enums.SortFieldEnum;
import com.amazon.advertising.assets.enums.SortOrderEnum;
import com.amazon.advertising.sb.entity.stores.StoreAssetResult;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.assets.CpcAssetsApiService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbStoreApiService;
import com.meiyunji.sponsored.service.cpc.service2.sd.CpcSdAssetService;
import com.meiyunji.sponsored.service.cpc.vo.assets.ImageAssetVo;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2024/9/2 15:53
 * @describe:
 */
@Service
public class CpcSdAssetServiceImpl implements CpcSdAssetService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private CpcAssetsApiService cpcAssetsApiService;

    @Override
    public Result<List<ImageAssetVo>> getAssetImage(Integer puid, Integer shopId,
                                                    String subType, String filterType) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Map<String, List<String>> valueFilters = new HashMap<>();
        valueFilters.put(FilterFieldEnum.ASSET_TYPE.name(), Collections.singletonList(AssetTypeEnum.IMAGE.name()));
        valueFilters.put(FilterFieldEnum.ASSET_SUB_TYPE.name(), Collections.singletonList(subType));
        valueFilters.put(FilterFieldEnum.TAG.name(), Collections.singletonList(CampaignTypeEnum.sd.getCampaignType()));
        Map<String, List<Map<String, String>>> rangeFilters = new HashMap<>();
        Result<SearchAssetsResult> searchResult = cpcAssetsApiService.searchAssets(shop, profile, null, valueFilters, rangeFilters,
                SortFieldEnum.CREATED_TIME.name(), SortOrderEnum.DESC.name());
        SearchAssetsResult data = searchResult.getData();
        List<ImageAssetVo> results = new ArrayList<>();
        if (data != null && CollectionUtils.isNotEmpty(data.getAssetList())) {
            List<com.amazon.advertising.assets.entity.searchAssets.Asset> assetList = data.getAssetList();
            results = assetList.stream().map(asset -> {
                        ImageAssetVo result = new ImageAssetVo();
                        result.setAssetId(asset.getAssetId());
                        result.setVersionId(asset.getVersion());
                        result.setName(asset.getName());
                        result.setUrl(asset.getStorageLocationUrls().getDefaultUrl());
                        result.setMediaType(asset.getFileMetadata().getContentType());
                        result.setHeight(asset.getFileMetadata().getHeight());
                        result.setWidth(asset.getFileMetadata().getWidth());
                        return result;
                    }).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(results) && StringUtils.isNotEmpty(filterType) && filterType.split(",").length != 0) {
            results = results.stream().filter(r -> {
                if (StringUtils.isEmpty(r.getName()) || r.getName().trim().split("\\.").length != 2) {
                    return false;
                }
                return Arrays.asList(filterType.split(",")).parallelStream().anyMatch(f -> r.getName().trim().split("\\.")[1].equalsIgnoreCase(f));
            }).collect(Collectors.toList());

        }
        return ResultUtil.success(results);
    }
}
