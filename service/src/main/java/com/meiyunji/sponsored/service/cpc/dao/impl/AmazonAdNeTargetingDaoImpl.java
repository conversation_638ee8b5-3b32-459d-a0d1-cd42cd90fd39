package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadras.types.schedule.AdvertiseRuleTaskExecuteRecordV2Message;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingSphereDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.LogicType;
import com.meiyunji.sponsored.common.util.SelectBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.autoRule.vo.AdKeywordTargetAutoRuleParam;
import com.meiyunji.sponsored.service.cpc.bo.AdOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdTargetingTypeBo;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdNeTargetingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdNeTargeting;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.po.SearchQueryTagParam;
import com.meiyunji.sponsored.service.cpc.po.TargetTypeEnum;
import com.meiyunji.sponsored.service.cpc.qo.TargetSuggestBidBatchQo;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.pricing.ScheduleTaskFinishedVo;
import com.meiyunji.sponsored.service.log.enums.TargetingTypeEnum;
import com.meiyunji.sponsored.service.negative.request.NegativeArchiveRequest;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.DiagnoseCountParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.TargetViewParam;
import com.meiyunji.sponsored.service.strategy.vo.AdTargetStrategyParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.SingleColumnRowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AmazonAdTargeting
 * <AUTHOR>
 */
@Repository
public class AmazonAdNeTargetingDaoImpl extends BaseShardingSphereDaoImpl<AmazonAdNeTargeting> implements IAmazonAdNeTargetingDao {

    private final int titleLimit = 1000;
    private final int imgLimit = 1000;

    @Override
    public void insertOnDuplicateKeyUpdate(Integer puid, List<AmazonAdNeTargeting> amazonAdTargetings) {
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return;
        }

        long s = System.currentTimeMillis();

        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_ad_ne_targeting` (`puid`,`shop_id`,`marketplace_id`,`target_id`,")
                .append("`ad_group_id`,`campaign_id`,`profile_id`,`expression_type`,`expression`,`resolved_expression`,")
                .append(" `bid`,`type`,`targeting_value`,`title`,`img_url`,suggested,range_start,range_end,`category_path`," +
                        "`state`,`serving_status`,`create_id`,`update_id`,`select_type`,`creation_date`,`create_time`,`update_time`) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for(AmazonAdNeTargeting amazonAdTargeting : amazonAdTargetings){
            sql.append("(?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?,?, ?,?,?,?, now(3),now(3)),");
            argsList.add(amazonAdTargeting.getPuid());
            argsList.add(amazonAdTargeting.getShopId());
            argsList.add(amazonAdTargeting.getMarketplaceId());
            argsList.add(amazonAdTargeting.getTargetId());
            argsList.add(amazonAdTargeting.getAdGroupId());
            argsList.add(amazonAdTargeting.getCampaignId());
            argsList.add(amazonAdTargeting.getProfileId());
            argsList.add(amazonAdTargeting.getExpressionType());
            argsList.add(amazonAdTargeting.getExpression());
            argsList.add(amazonAdTargeting.getResolvedExpression());
            argsList.add(amazonAdTargeting.getBid());
            argsList.add(amazonAdTargeting.getType());
            argsList.add(amazonAdTargeting.getTargetingValue());
            argsList.add(amazonAdTargeting.getTitle() != null
                    && amazonAdTargeting.getTitle().length() > titleLimit ?
                    amazonAdTargeting.getTitle().substring(0, titleLimit) : amazonAdTargeting.getTitle());
            argsList.add(amazonAdTargeting.getImgUrl() != null
                    && amazonAdTargeting.getImgUrl().length() > imgLimit?
                    amazonAdTargeting.getImgUrl().substring(0, imgLimit) : amazonAdTargeting.getImgUrl());
            argsList.add(amazonAdTargeting.getSuggested());
            argsList.add(amazonAdTargeting.getRangeStart());
            argsList.add(amazonAdTargeting.getRangeEnd());
            argsList.add(amazonAdTargeting.getCategoryPath());
            argsList.add(amazonAdTargeting.getState());
            argsList.add(amazonAdTargeting.getServingStatus());
            argsList.add(amazonAdTargeting.getCreateId());
            argsList.add(amazonAdTargeting.getUpdateId());
            argsList.add(amazonAdTargeting.getSelectType() == null ? "" : amazonAdTargeting.getSelectType());
            argsList.add(amazonAdTargeting.getCreationDate());
        }
        sql.deleteCharAt(sql.length()-1);
        sql.append(" on duplicate key update expression_type=values(expression_type),resolved_expression=values(resolved_expression)," +
                "targeting_value=values(targeting_value),bid=values(bid),`type`=values(`type`),`state`=values(state),`serving_status`=values(serving_status),`select_type`=values(select_type),`creation_date`=values(creation_date)");



        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(),argsList.toArray());
        } finally {
            hintManager.close();
        }

        logger.info("target批量入库用时:{}", System.currentTimeMillis() - s);
    }

    @Override
    public Integer countByAdGroupId(int puid, Integer shopId, String adGroupId) {
        String sql = "SELECT COUNT(*) FROM `t_amazon_ad_ne_targeting` WHERE puid=? AND shop_id=?" +
                " AND ad_group_id=? AND `expression_type`='manual' and type in ('category','asin')";

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForObject(sql,new Object[]{puid,shopId,adGroupId},Integer.class);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> getTargetIds(Integer puid, Integer shopId, String marketPlaceId, List<String> onlineTargetId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .equalTo("marketplace_id",marketPlaceId)
                .inStrList("target_id",onlineTargetId.toArray(new String[]{}))
                .build();
        return listDistinctFieldByCondition(puid,"target_id",builder,String.class);
    }

    @Override
    public void updateList(Integer puid, List<AmazonAdTargeting> list) {
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_ne_targeting` set expression=?,resolved_expression=?,bid=?,`state`=?,")
                .append("`update_time`= now(),update_id=? where puid=? and shop_id=? and campaign_id=? and target_id=?");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonAdTargeting amazonAdTargeting : list) {
            batchArg = new Object[]{
                    amazonAdTargeting.getExpression(),
                    amazonAdTargeting.getResolvedExpression(),
                    amazonAdTargeting.getBid(),
                    amazonAdTargeting.getState(),
                    amazonAdTargeting.getUpdateId(),
                    amazonAdTargeting.getPuid(),
                    amazonAdTargeting.getShopId(),
                    amazonAdTargeting.getCampaignId(),
                    amazonAdTargeting.getTargetId()
            };
            batchArgs.add(batchArg);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).batchUpdate(sql.toString(), batchArgs);
        } finally {
            hintManager.close();
        }


    }

    @Override
    public String getProfileId(int puid, Integer shopId, String marketPlaceId, String targetId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .equalTo("marketplace_id",marketPlaceId)
                .equalTo("target_id", targetId)
                .build();
        return getByCondition(puid,"profile_id",String.class,builder);
    }

    @Override
    public void updateState(int puid, Integer shopId, String targetId, String state, int updateId) {
        String sql = "update t_amazon_ad_ne_targeting set state=?,update_id=?,update_time=now() where puid = ? and shop_id = ? and `target_id`=?";
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql,new Object[]{state,updateId,puid,shopId,targetId});
        } finally {
            hintManager.close();
        }
    }

    @Override
    public void updateBid(Integer puid, Integer updateId, List<AmazonAdTargeting> list) {
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_ne_targeting` set bid=?,")
                .append("`update_time`= now(),update_id=? where id=? and puid=?");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonAdTargeting amazonAdTargeting : list) {
            batchArg = new Object[]{
                    amazonAdTargeting.getBid(),
                    amazonAdTargeting.getUpdateId(),
                    amazonAdTargeting.getId(),
                    puid
            };
            batchArgs.add(batchArg);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).batchUpdate(sql.toString(), batchArgs);
        } finally {
            hintManager.close();
        }


    }

    @Override
    public List<AmazonAdNeTargeting> listByAdGroupId(Integer puid, Integer shopId, String adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .equalTo("ad_group_id", adGroupId)
                .build();
        return listByCondition(puid,builder);
    }

    @Override
    public List<AmazonAdNeTargeting> listAutoByAdGroupId(Integer puid, Integer shopId, String adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .equalTo("ad_group_id", adGroupId)
                .equalTo("expression_type", "auto")
                .build();
        return listByCondition(puid,builder);
    }

    @Override
    public void updateTargetId(Integer puid,List<AmazonAdTargeting> list) {
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_ne_targeting` set target_id=?,ad_group_id=?,")
                .append("`update_time`= now() where id=?");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonAdTargeting amazonAdTargeting : list) {
            batchArg = new Object[]{
                    amazonAdTargeting.getTargetId(),
                    amazonAdTargeting.getAdGroupId(),
                    amazonAdTargeting.getId(),
            };
            batchArgs.add(batchArg);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).batchUpdate(sql.toString(), batchArgs);
        } finally {
            hintManager.close();
        }


    }

    @Override
    public int deleteByGroupId(int puid, Integer shopId, String adGroupId) {
        String sql = "delete from t_amazon_ad_ne_targeting where puid=? and shop_id=? and ad_group_id=?";
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).update(sql,puid,shopId,adGroupId);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public int deleteFailByGroupId(Integer puid, Integer shopId, String adGroupId) {
        String sql = "delete from t_amazon_ad_ne_targeting where puid=? and shop_id=? and ad_group_id=? and target_id is null";
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).update(sql,puid,shopId,adGroupId);
        } finally {
            hintManager.close();
        }

    }

    @Override
    public AmazonAdNeTargeting getByAdTargetId(int puid, Integer shopId, String marketplaceId, String targetId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .equalTo("target_id", targetId)
                .equalTo("marketplace_id",marketplaceId)
                .build();
        return getByCondition(puid,builder);
    }

    @Override
    public AmazonAdNeTargeting getByAdTargetId(int puid, Integer shopId, String targetId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .equalTo("target_id", targetId)
                .build();
        return getByCondition(puid,builder);
    }

    @Override
    public Page pageListForTask(int puid, CpcTaskSearchDto dto, Page page) {
        StringBuilder selectSql = new StringBuilder("select target_id,ad_group_id,campaign_id,type,targeting_value,expression,resolved_expression,create_time from t_amazon_ad_ne_targeting");
        StringBuilder countSql = new StringBuilder("select count(*) from t_amazon_ad_ne_targeting ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and state in ('enabled','paused') and type in ('category','asin') and target_id is not null");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        if(StringUtils.isNotEmpty(dto.getCampaignId())){
            whereSql.append(" and campaign_id = ? ");
            argsList.add(dto.getCampaignId());
        }
        if(StringUtils.isNotEmpty(dto.getGroupId())){
            whereSql.append(" and ad_group_id = ? ");
            argsList.add(dto.getGroupId());
        }
        if(Constants.ITEM_TYPE_TARGET.equals(dto.getSearchField())&&StringUtils.isNotEmpty(dto.getSearchValue())){
            String searchValue = SqlStringUtil.dealLikeSql(dto.getSearchValue());
            whereSql.append(" and targeting_value like ? ");
            argsList.add("%" + searchValue + "%");
        }
        selectSql.append(whereSql);
        countSql.append(whereSql);
        selectSql.append(" order by id desc");
        Object[] args = argsList.toArray();
        return this.getPageResult(puid,page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdNeTargeting.class);
    }

    @Override
    public List<AmazonAdNeTargeting> listTargets(int puid, List<String> targetIdList) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .in("target_id",targetIdList.toArray())
                .build();
        return listByCondition(puid,builder);
    }

    @Override
    public AmazonAdNeTargeting getByTargetId(int puid, Integer shopId, String marketplaceId, String targetId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .equalTo("target_id", targetId)
                .equalTo("marketplace_id",marketplaceId)
                .build();
        return getByCondition(puid,builder);
    }

    @Override
    public List<AmazonAdNeTargeting> listNoAsinInfo(Integer puid, Integer shopId, Long offset, Integer limit) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("type", new Object[]{ TargetTypeEnum.negativeAsin.name()})
                .greaterThan("id", offset)
                .appendSql(" and (title is null or title = '' or img_url is null or img_url = '') ", true)
                .orderBy("id")
                .limit(limit)
                .build();

        String sql = "select * from t_amazon_ad_ne_targeting where " + conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }
    }
    @Override
    public void batchSetAsinInfo(Integer puid,List<AmazonAdNeTargeting> needUpdateList) {
        if (CollectionUtils.isNotEmpty(needUpdateList)) {
            String sql = "update t_amazon_ad_ne_targeting set title=?, img_url=?, update_time=now(3) where id=? and puid = ? ";
            List<Object[]> argsList = new ArrayList<>();
            List<Object> args;
            for (AmazonAdNeTargeting amazonAdTargeting : needUpdateList) {
                args = new ArrayList<>();
                args.add(amazonAdTargeting.getTitle() != null
                        && amazonAdTargeting.getTitle().length() > titleLimit ?
                        amazonAdTargeting.getTitle().substring(0, titleLimit) : amazonAdTargeting.getTitle());
                args.add(amazonAdTargeting.getImgUrl() != null
                        && amazonAdTargeting.getImgUrl().length() > imgLimit?
                        amazonAdTargeting.getImgUrl().substring(0, imgLimit) : amazonAdTargeting.getImgUrl());
                args.add(amazonAdTargeting.getId());
                args.add(puid);
                argsList.add(args.toArray());
            }
            HintManager hintManager = HintManager.getInstance();
            try {
                getJdbcTemplate(puid, hintManager).batchUpdate(sql, argsList);
            } finally {
                hintManager.close();
            }


        }
    }

    @Override
    public int countByGroup(Integer puid, Integer shopId, String adGroupId) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId)
                .build();

        return getCountByCondition(puid,conditionBuilder);
    }

    @Override
    public List<AmazonAdNeTargeting> getByAdTargetIds(int puid, Integer shopId, List<String> targetIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("target_id", targetIds.toArray())
                .build();
        return listByCondition(puid,conditionBuilder);
    }

    @Override
    public List<AmazonAdNeTargeting> getByTargetSuggestBidBatchQo(int puid, List<TargetSuggestBidBatchQo> targetList) {
        StringBuilder sb = new StringBuilder("select * from " + getJdbcHelper().getTable() + " where puid = ? and (shop_id,target_id) in (");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        for (TargetSuggestBidBatchQo qo : targetList) {
            sb.append("(?,?),");
            argsList.add(qo.getShopId());
            argsList.add(qo.getTargetId());
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(")");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sb.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }


    }

    @Override
    public List<AmazonAdNeTargeting> getByAdTargetIds(int puid, List<String> targetIds, String putType) {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.equalTo("puid", puid);
        conditionBuilder.in("target_id", targetIds.toArray());
        if (putType.equals(com.meiyunji.sponsored.common.base.Constants.SP_AUTO_TARGET)) {
            conditionBuilder.equalTo("expression_type","auto");
        } else if (putType.equals(com.meiyunji.sponsored.common.base.Constants.SP_PRODUCT_TARGET)) {
            conditionBuilder.notEqualTo("type","negativeAsin");
        }

        return listByCondition(puid,conditionBuilder.build());
    }

    @Override
    public void batchUpdateSuggestValue(Integer puid,Collection<AmazonAdTargeting> targetings) {
        if (CollectionUtils.isEmpty(targetings)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(targetings.size());
        List<Object> arg;
        for (AmazonAdTargeting po : targetings) {
            arg = new ArrayList<>();
            arg.add(po.getSuggested());
            arg.add(po.getRangeStart());
            arg.add(po.getRangeEnd());
            arg.add(po.getId());
            argList.add(arg.toArray());
        }

        String sql = "update t_amazon_ad_ne_targeting set suggested=?, range_start=?, range_end=?, update_time=now(3) where id=?";
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).batchUpdate(sql, argList);
        } finally {
            hintManager.close();
        }

    }

    /**
     * 查询所有广告投放
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AmazonAdTargetingDto> listAllTargetingByType(int puid, TargetingPageParam param) {
        SelectBuilder selectBuilder = getTargetingSelectSql(puid, param);

        StringBuilder sql = new StringBuilder("select * from ( ").append(selectBuilder.toSql()).append(" ) c ");

        //默认排序
        String orderSql = " order by id desc ";

        sql.append(orderSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), selectBuilder.getQueryValues(), new RowMapper<AmazonAdTargetingDto>() {
                @Override
                public AmazonAdTargetingDto mapRow(ResultSet res, int i) throws SQLException {
                    return AmazonAdTargetingDto.builder()
                            .id(res.getLong("id"))
                            .adGroupId(res.getString("ad_group_id"))
                            .campaignId(res.getString("campaign_id"))
                            .targetId(res.getString("target_id"))
                            .bid(res.getString("bid"))
                            .createId(res.getInt("create_id"))
                            .updateId(res.getString("update_id"))
                            .createTime(res.getDate("create_time"))
                            .updateTime(res.getDate("update_time"))
                            .expression(res.getString("expression"))
                            .imgUrl(res.getString("img_url"))
                            .marketplaceId(res.getString("marketplace_id"))
                            .puid(res.getInt("puid"))
                            .shopId(res.getInt("shop_id"))
                            .rangeEnd(res.getBigDecimal("range_end"))
                            .rangeStart(res.getBigDecimal("range_start"))
                            .resolvedExpression(res.getString("resolved_expression"))
                            .state(res.getString("state"))
                            .suggested(res.getBigDecimal("suggested"))
                            .targetingText(res.getString("target_text"))
                            .title(res.getString("title"))
                            .type(res.getString("type"))
                            .atype("sp")
                            .isPricing(res.getInt("is_pricing"))
                            .pricingState(res.getInt("pricing_state"))
                            .build();
                }
            });
        } finally {
            hintManager.close();
        }


    }

    @Override
    public Page<AmazonAdNeTargetingDto> listAllNeTargetingByType(int puid, NeTargetingPageParam param) {
        SelectBuilder selectBuilder = getNeTargetingSelectSql(puid, param);

        StringBuilder sql = new StringBuilder("select id,target_text,target_id,ad_group_id,campaign_id,create_time,img_url,shop_id,state,title,type,atype ,expression ");
        if (Constants.SP.equalsIgnoreCase(param.getType()) || Constants.SD.equalsIgnoreCase(param.getType())) {
            sql.append(" ,creation_date ");
        }
        sql.append(" from ( ").append(selectBuilder.toSql()).append(" ) c ");

        //默认排序
        String orderSql = " order by id desc ";

        sql.append(orderSql);

        StringBuilder countSql = new StringBuilder("select count(*) from ( ").append(selectBuilder.toSql()).append(" ) c");

        return getPageByMapper(puid,param.getPageNo(),param.getPageSize(),countSql.toString(),selectBuilder.getQueryValues(),sql.toString(), selectBuilder.getQueryValues(), new RowMapper<AmazonAdNeTargetingDto>() {
            @Override
            public AmazonAdNeTargetingDto mapRow(ResultSet res, int i) throws SQLException {
                return AmazonAdNeTargetingDto.builder()
                        .id(res.getLong("id"))
                        .targetText(res.getString("target_text"))
                        .targetId(res.getString("target_id"))
                        .groupId(res.getString("ad_group_id"))
                        .campaignId(res.getString("campaign_id"))
                        .createTime(res.getString("create_time"))
                        .imgUrl(res.getString("img_url"))
                        .shopId(res.getInt("shop_id"))
                        .state(res.getString("state"))
                        .title(res.getString("title"))
                        .atype(res.getString("atype"))
                        .type(res.getString("type"))
                        .expression(res.getString("expression"))
                        .creationDate((Constants.SP.equalsIgnoreCase(param.getType()) || Constants.SD.equalsIgnoreCase(param.getType())) ?
                                res.getObject("creation_date", LocalDateTime.class) : null)
                        .build();
            }
        });
    }

    @Override
    public void updatePricing(Integer puid, Integer shopId, String targetId, Integer isPricing, Integer pricingState, int updateId) {
        String sql = "update t_amazon_ad_ne_targeting set is_pricing=?,pricing_state=?,update_id=?,update_time=now() where puid = ? and shop_id = ? and `target_id`=?";

        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql,new Object[]{isPricing,pricingState,updateId,puid,shopId,targetId});
        } finally {
            hintManager.close();
        }
    }

    @Override
    public Page getPageList(Integer puid, TargetingPageParam param, Page page) {
        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_ne_targeting ");
        StringBuilder countSql = new StringBuilder("select count(*)  FROM `t_amazon_ad_ne_targeting` ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and type != 'negativeAsin' ");
        argsList.add(puid);

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            List<String> list = StringUtil.splitStr(param.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> list = StringUtil.splitStr(param.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));

        }
        //标签管理
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            whereSql.append(SqlStringUtil.dealInList("target_id", param.getTargetIds(), argsList));
        }

        if (StringUtils.isNotBlank(param.getChosenTargetType())) {
            if ("auto".equalsIgnoreCase(param.getChosenTargetType())) {
                whereSql.append(" and type = 'auto' ");
            } else {
                whereSql.append(" and type in ('asin','category') ");
            }
        }

        if (StringUtils.isNotBlank(param.getFilterTargetType())) {
            if ("asin".equalsIgnoreCase(param.getFilterTargetType())) {
                whereSql.append(" and type='asin' ");
            } else if ("category".equalsIgnoreCase(param.getFilterTargetType())) {
                whereSql.append(" and type='category'  ");
            } else {
                whereSql.append(" and targeting_value = ? and type = 'auto' ");
                argsList.add(param.getFilterTargetType());
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                whereSql.append(" and targeting_value = ? and type = 'asin' ");
                argsList.add(param.getSearchValue());
            } else if ("category".equalsIgnoreCase(param.getSearchField())) {
                whereSql.append(" and targeting_value like ? and type = 'category' ");
                argsList.add("%" + param.getSearchValue() + "%");
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, argsList));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ? ");
                argsList.add(AmazonAdTargeting.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }

        }

        selectSql.append(whereSql);
        countSql.append(whereSql);
        selectSql.append(" order by data_update_time desc, id desc ");

        Object[] args = argsList.toArray();
        return getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdNeTargeting.class);
    }

    @Override
    public List<AmazonAdNeTargeting> getList(Integer puid, TargetingPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .notIn("type",new Object[]{TargetTypeEnum.negativeAsin.name()});

        if(StringUtils.isNotBlank(param.getSelectType())){
            builder.equalTo("select_type",param.getSelectType());
        }
        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> list = StringUtil.splitStr(param.getCampaignId());
            builder.inStrList("campaign_id",list.toArray(new String[]{}));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            builder.inStrList("campaign_id",param.getCampaignIdList().toArray(new String[]{}));
        }
        //广告组ID搜索
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> list = StringUtil.splitStr(param.getGroupId());
            builder.inStrList("ad_group_id",list.toArray(new String[]{}));
        }
        //标签管理
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            builder.inStrList("target_id",param.getTargetIds().toArray(new String[]{}));
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            builder.inStrList("state", statusList.toArray(new String[]{}));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                builder.equalTo("serving_status", AmazonAdTargeting.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                builder.inStrList("serving_status", list.toArray(list.toArray(new String[]{})));
            }

        }

        if (StringUtils.isNotBlank(param.getChosenTargetType())) {
            if ("auto".equalsIgnoreCase(param.getChosenTargetType())) {
                builder.equalTo("type","auto");
            } else {
                builder.inStrList("type",new String[]{"asin","category"});
            }
        }

        if (StringUtils.isNotBlank(param.getFilterTargetType())) {
            if ("asin".equalsIgnoreCase(param.getFilterTargetType())) {
                builder.equalTo("type", "asin");
            } else if ("category".equalsIgnoreCase(param.getFilterTargetType())) {
                builder.equalTo("type", "category");
            } else {
                builder.equalTo("targeting_value", param.getFilterTargetType());
                builder.equalTo("type", "auto");
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                builder.equalTo("targeting_value", param.getSearchValue());
                builder.equalTo("type", "asin");
            } else if ("category".equalsIgnoreCase(param.getSearchField())) {
                builder.like("targeting_value", param.getSearchValue());
                builder.equalTo("type", "category");
            }
        }
        // 开启了高级搜索 竞价值
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {
            if (param.getBidMin() != null) {
                builder.greaterThanOrEqualTo("bid", param.getBidMin());
            }
            if (param.getBidMax() != null) {
                builder.lessThanOrEqualTo("bid", param.getBidMax());
            }
        }


        builder.orderByDesc("data_update_time", "id");
        builder.limit(Constants.TOTALSIZELIMIT);  // 限制10万

        return listByCondition(puid,builder.build());
    }

    @Override
    public List<AmazonAdNeTargeting> getTargetViewList(Integer puid, TargetViewParam param, boolean isAuto) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", param.getShopIdList().toArray());

        if (isAuto) {
            builder.equalTo("type", "auto");
        } else {
            if (StringUtils.isNotBlank(param.getTargetType())) {
                List<String> targetTypeList = StringUtil.splitStr(param.getTargetType(), ",");
                builder.inStrList("type", targetTypeList.toArray(new String[]{}));
            } else {
                builder.inStrList("type", new String[]{"asin", "category"});
            }
        }

        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> list = StringUtil.splitStr(param.getCampaignId());
            builder.inStrList("campaign_id", list.toArray(new String[]{}));
        }
        //广告组ID查询
        if (CollectionUtils.isNotEmpty(param.getAdGroupIdList())) {
            builder.in("ad_group_id", param.getAdGroupIdList().toArray());
        }
        //匹配方式
        if (StringUtils.isNotBlank(param.getTargetType()) && isAuto) {
            List<String> targetTypeList = StringUtil.splitStr(param.getTargetType(), ",");
            builder.inStrList("targeting_value", targetTypeList.toArray(new String[]{}));
        }
        //状态
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            builder.inStrList("state", statusList.toArray(new String[]{}));
        }
        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                builder.equalTo("serving_status", AmazonAdTargeting.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                builder.inStrList("serving_status", list.toArray(list.toArray(new String[]{})));
            }

        }
        if (!isAuto) {
            //筛选状态
            if (StringUtils.isNotBlank(param.getSelectType())) {
                List<String> selectTypeList = StringUtil.splitStr(param.getSelectType(), ",");
                builder.inStrList("select_type", selectTypeList.toArray(new String[]{}));
            }
            //商品投放精准
            builder.equalTo("targeting_value", param.getTargetText());
        }
        builder.orderByDesc("data_update_time", "id");
        builder.limit(Constants.TOTALSIZELIMIT);  // 限制10万

        return listByCondition(puid, builder.build());
    }

    @Override
    public void updateDataUpdateTime(Integer puid, Integer shopId, String targetId, LocalDate localDate) {
        String sql = "update t_amazon_ad_ne_targeting set data_update_time=?,update_time=now() where puid = ? and shop_id = ? and `target_id`=?";
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql,new Object[]{localDate,puid,shopId,targetId});
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdNeTargeting> listByGroupIdList(Integer puid, Integer shopId, List<String> groupIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .inStrList("ad_group_id", groupIds.toArray(new String[]{}))
                .equalTo("type", Constants.TARGETING_TYPE_NEGATIVEASIN)
                .in("state", new Object[]{CpcStatusEnum.enabled.name(),CpcStatusEnum.paused.name()})
                .build();
        return listByCondition(puid,builder);
    }

    private SelectBuilder getNeTargetingSelectSql(Integer puid, NeTargetingPageParam param) {
        SelectBuilder selectBuilder = new SelectBuilder();

        StringBuilder spSql = new StringBuilder();
        //未指定类型 或 指定sp
        if (StringUtils.isBlank(param.getType()) || Constants.SP.equalsIgnoreCase(param.getType())) {

            spSql.append("  'sp' as atype,`id`,shop_id,  campaign_id, `ad_group_id`,  `target_id`,`state`, `type`, `expression`, `targeting_value` as target_text, ");
            spSql.append(" `img_url`,`title`, `create_time`, `creation_date` from t_amazon_ad_ne_targeting  where ");

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", param.getShopId())
                    .equalTo("type",TargetTypeEnum.negativeAsin.name());

            //状态查询
            if (StringUtils.isNotBlank(param.getState())) {
                builder.equalTo("state", param.getState());
            }
            if (StringUtils.isNotBlank(param.getCampaignId())) {
                builder.inStrList("campaign_id", StringUtil.splitStr(param.getCampaignId()).toArray(new String[]{}));
            }
            if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
                builder.inStrList("campaign_id",param.getCampaignIdList().toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(param.getGroupId())) {
                builder.inStrList("ad_group_id", StringUtil.splitStr(param.getGroupId()).toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
                builder.like("targeting_value", param.getSearchValue());
            }

            spSql.append(builder.build().getSql());

            selectBuilder.appendSql(spSql.toString());
            selectBuilder.appendValue(builder.build().getValues());
        }

        //未指定类型 或 指定sb
        StringBuilder sbSql = new StringBuilder();
        if (StringUtils.isBlank(param.getType()) || Constants.SB.equalsIgnoreCase(param.getType())) {

            sbSql.append("  'sb' as atype, `id`, shop_id, campaign_id, `ad_group_id`, `target_id`,`state`,  `type`, `expression`, target_text,");
            sbSql.append("  `img_url`, `title`,`create_time` from t_amazon_ad_netargeting_sb where ");

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", param.getShopId());
            //状态查询
            if (StringUtils.isNotBlank(param.getState())) {
                builder.equalTo("state", param.getState());
            }
            if (StringUtils.isNotBlank(param.getCampaignId())) {
                builder.inStrList("campaign_id", StringUtil.splitStr(param.getCampaignId()).toArray(new String[]{}));
            }
            if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
                builder.inStrList("campaign_id",param.getCampaignIdList().toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(param.getGroupId())) {
                builder.inStrList("ad_group_id", StringUtil.splitStr(param.getGroupId()).toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
                builder.like("target_text", param.getSearchValue());
            }
            sbSql.append(builder.build().getSql());

            if (StringUtils.isNotBlank(spSql.toString())) {
                selectBuilder.appendSql(" union all select ");
            }
            selectBuilder.appendSql(sbSql.toString());
            selectBuilder.appendValue(builder.build().getValues());
        }


        //未指定类型 或 指定sd
        StringBuilder sdSql = new StringBuilder();
        if (StringUtils.isBlank(param.getType()) || Constants.SD.equalsIgnoreCase(param.getType())) {
            //表中无campaign_id
            sdSql.append("  'sd' as atype, `id`,shop_id, '-1' as campaign_id,`ad_group_id`, `target_id`,`state`,  `type`, `expression`,target_text, ");
            sdSql.append(" `img_url`, `title`,`create_time`, `creation_date` from t_amazon_ad_netargeting_sd where ");

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", param.getShopId());
            //状态查询
            if (StringUtils.isNotBlank(param.getState())) {
                builder.equalTo("state", param.getState());
            }
            if (StringUtils.isNotBlank(param.getGroupId())) {
                builder.in("ad_group_id", param.getGroupId().split(","));
            }
            if (CollectionUtils.isNotEmpty(param.getGroupIdList())) { //广告组合查询
                builder.inStrList("ad_group_id",param.getGroupIdList().toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
                builder.like("target_text", param.getSearchValue());
            }

            if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
                builder.in("campaign_id", param.getCampaignIdList().toArray());
            }

            sdSql.append(builder.build().getSql());

            if (StringUtils.isNotBlank(spSql.toString())) {
                selectBuilder.appendSql(" union all select ");
            }


            selectBuilder.appendSql(sdSql.toString());
            selectBuilder.appendValue(builder.build().getValues());
        }
        return selectBuilder;
    }

    private SelectBuilder getTargetingSelectSql(Integer puid, TargetingPageParam param) {
        SelectBuilder selectBuilder = new SelectBuilder();

        StringBuilder spSql = new StringBuilder();
        //未指定类型 或 指定sp
        if (StringUtils.isBlank(param.getType()) || Constants.SP.equalsIgnoreCase(param.getType())) {

            spSql.append("  'sp' as a_type,`id`, `puid`, `shop_id`, `marketplace_id`, campaign_id, `ad_group_id`,  `target_id`,`state`, `type`, `targeting_value` as target_text, `bid`,  `expression`,  `is_pricing`,  `pricing_state`, ");
            spSql.append("`resolved_expression`,  `img_url`, `suggested`, `range_start`  ,`range_end`,`title`,`create_id`, `update_id`, `create_time`, `update_time` from t_amazon_ad_ne_targeting  where ");

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", param.getShopId())
                    .notIn("type",new Object[]{TargetTypeEnum.negativeAsin.name()});

            //状态查询
            if (StringUtils.isNotBlank(param.getStatus())) {
                builder.equalTo("state", param.getStatus());
            }
            if (StringUtils.isNotBlank(param.getCampaignId())) {
                builder.equalTo("campaign_id", param.getCampaignId());
            }
            if (StringUtils.isNotBlank(param.getGroupId())) {
                builder.equalTo("ad_group_id", param.getGroupId());
            }
            //自动投放时(仅sp),搜索处理  临时处理
            if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
                List<String> autoTargetList = new ArrayList<>();
                if ("紧密匹配".contains(param.getSearchValue())) {
                    autoTargetList.add("queryHighRelMatches");
                }
                if ("宽泛匹配".contains(param.getSearchValue())) {
                    autoTargetList.add("queryBroadRelMatches");
                }
                if ("同类产品".contains(param.getSearchValue())) {
                    autoTargetList.add("asinSubstituteRelated");
                }
                if ("关联产品".contains(param.getSearchValue())) {
                    autoTargetList.add("asinAccessoryRelated");
                }
                if (CollectionUtils.isEmpty(autoTargetList)) {
                    builder.customSql(LogicType.AND," (( type = 'asin' or type = 'category') and targeting_value like ? ) ",new String[] {"%"+param.getSearchValue()+"%"});
                } else {
                    builder.inStrList("targeting_value",autoTargetList.toArray(new String[] {}));
                    builder.equalTo("type","auto");
                }
            }


            spSql.append(builder.build().getSql());

            selectBuilder.appendSql(spSql.toString());
            selectBuilder.appendValue(builder.build().getValues());
        }

        //未指定类型 或 指定sb
        StringBuilder sbSql = new StringBuilder();
        if (StringUtils.isBlank(param.getType()) || Constants.SB.equalsIgnoreCase(param.getType())) {

            sbSql.append("  'sb' as a_type, `id`, `puid`, `shop_id`, `marketplace_id`,campaign_id, `ad_group_id`, `target_id`,`state`,  `type`, `target_text`, `bid`, `expression`, '0' as `is_pricing`, '0' as `pricing_state`,");
            sbSql.append("`resolved_expression`,  `img_url`, `suggested`, `range_start`, `range_end`,`title`,`create_id`, `update_id`,`create_time`, `update_time` from t_amazon_ad_targeting_sb where ");

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", param.getShopId());
            //状态查询
            if (StringUtils.isNotBlank(param.getStatus())) {
                builder.equalTo("state", param.getStatus());
            }
            if (StringUtils.isNotBlank(param.getCampaignId())) {
                builder.equalTo("campaign_id", param.getCampaignId());
            }
            if (StringUtils.isNotBlank(param.getGroupId())) {
                builder.equalTo("ad_group_id", param.getGroupId());
            }
            if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
                builder.like("target_text", param.getSearchValue());
            }
            sbSql.append(builder.build().getSql());

            if (StringUtils.isNotBlank(spSql.toString())) {
                selectBuilder.appendSql(" union all select ");
            }
            selectBuilder.appendSql(sbSql.toString());
            selectBuilder.appendValue(builder.build().getValues());
        }


        //未指定类型 或 指定sd
        StringBuilder sdSql = new StringBuilder();
        if (StringUtils.isBlank(param.getType()) || Constants.SD.equalsIgnoreCase(param.getType())) {
            //sd表无campaign_id
            sdSql.append("  'sd' as a_type, `id`, `puid`, `shop_id`,`marketplace_id`, '-1' as  campaign_id,`ad_group_id`, `target_id`,`state`,  `type`, `target_text`, `bid`, `expression`, '0' as `is_pricing`, '0' as `pricing_state`,");
            sdSql.append("`resolved_expression`,  `img_url`, `suggested`, `range_start`, `range_end`,`title`,`create_id`, `update_id`,`create_time`, `update_time` from t_amazon_ad_targeting_sd where ");

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", param.getShopId());
            //状态查询
            if (StringUtils.isNotBlank(param.getStatus())) {
                builder.equalTo("state", param.getStatus());
            } else {
                //草稿不显示
                List<String> stateList = new ArrayList<>();
                stateList.add("enabled");
                stateList.add("paused");
                stateList.add("archived");
                stateList.add("pending");
                builder.inStrList("state", stateList.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(param.getGroupId())) {
                builder.in("ad_group_id", param.getGroupId().split(","));
            }
            if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
                builder.like("target_text", param.getSearchValue());
            }
            sdSql.append(builder.build().getSql());

            if (StringUtils.isNotBlank(spSql.toString())) {
                selectBuilder.appendSql(" union all select ");
            }

            selectBuilder.appendSql(sdSql.toString());
            selectBuilder.appendValue(builder.build().getValues());
        }
        return selectBuilder;
    }

    @Override
    public Page<AmazonAdNeTargeting> pageList(Integer puid, TargetingPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("ad_group_id", param.getGroupId())
                .in("type", new Object[]{TargetTypeEnum.asin.name(), TargetTypeEnum.category.name()});

        if (StringUtils.isNotBlank(param.getStatus())) {
            builder.equalTo("state", param.getStatus());
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            builder.like("targeting_value", param.getSearchValue());
        }

        String orderBySql = " order by create_time desc";

        return page(puid,param.getPageNo(), param.getPageSize(), orderBySql, builder.build());
    }

    @Override
    public List<AmazonAdNeTargeting> listByCondition(Integer puid, TargetingPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("ad_group_id", param.getGroupId())
                .in("type", new Object[]{TargetTypeEnum.asin.name(), TargetTypeEnum.category.name()});

        if (StringUtils.isNotBlank(param.getStatus())) {
            builder.equalTo("state", param.getStatus());
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            builder.like("targeting_value", param.getSearchValue());
        }

        return listByCondition(puid,builder.build());
    }

    @Override
    public Page<AmazonAdNeTargeting> neTargetingPageList(Integer puid, NeTargetingPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("ad_group_id", param.getGroupId())
                .equalTo("type", TargetTypeEnum.negativeAsin.name())
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.archived.name()});

        if (StringUtils.isNotBlank(param.getStatus())) {
            builder.equalTo("state", param.getStatus());
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            builder.like("targeting_value", param.getSearchValue());
        }

        String orderBySql = " order by create_time desc";

        return page(puid,param.getPageNo(), param.getPageSize(), orderBySql, builder.build());
    }

    @Override
    public Map<String, Integer> statCountByAdGroup(Integer puid, Integer shopId, List<String> status, List<String> adGroupIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("ad_group_id", adGroupIds.toArray())
                .in("state", status.toArray())
                .and()
                .leftBracket()
                .in(LogicType.EPT, "type", new Object[]{TargetTypeEnum.category.name(), TargetTypeEnum.asin.name()})
                .or()
                .equalTo(LogicType.EPT, "expression_type", "auto")
                .rightBracket()
                .groupBy("ad_group_id").build();

        String sql = "select ad_group_id adGroupId, count(*) c from t_amazon_ad_ne_targeting where " + conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql, conditionBuilder.getValues())
                    .stream().collect(Collectors.toMap(e -> e.get("adGroupId").toString(), e -> Integer.parseInt(e.get("c").toString())));
        } finally {
            hintManager.close();
        }

    }

    @Override
    public void updateList(Integer puid, List<AmazonAdTargeting> list, String type) {
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_ne_targeting` set update_id=?,update_time=now() ");
        if (Constants.CPC_SP_TARGET_BATCH_UPDATE_BID.equals(type)) {
            sql.append(",`bid` = ? ");
        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            sql.append(",`state`=? ");
        }
        sql.append(" where puid=? and shop_id=? and campaign_id=? and target_id=? and id = ?");
        List<Object[]> batchArgs = Lists.newArrayList();
        List<Object> batchArg = new ArrayList<>();
        for (AmazonAdTargeting amazonAdTargeting : list) {
            batchArg.add(amazonAdTargeting.getUpdateId());
            if (Constants.CPC_SP_KEYWORD_BATCH_UPDATE_BID.equals(type)) {
                batchArg.add(amazonAdTargeting.getBid());
            } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
                batchArg.add(amazonAdTargeting.getState());

            }
            batchArg.add(amazonAdTargeting.getPuid());
            batchArg.add(amazonAdTargeting.getShopId());
            batchArg.add(amazonAdTargeting.getCampaignId());
            batchArg.add(amazonAdTargeting.getTargetId());
            batchArg.add(amazonAdTargeting.getId());
            batchArgs.add(batchArg.toArray());
            batchArg.clear();
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).batchUpdate(sql.toString(), batchArgs);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> getAsinsByCampaignId(Integer puid, Integer shopId, String campaignId) {
        StringBuilder sqlBuilder = new StringBuilder("select distinct(targeting_value) from t_amazon_ad_ne_targeting where puid = ? and shop_id = ? and campaign_id = ? and type = 'asin'  and state in('enabled','paused')");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(shopId);
        args.add(campaignId);
        HintManager hintManager = HintManager.getInstance();
        try {
            return  getJdbcTemplate(puid, hintManager).queryForList(sqlBuilder.toString(),String.class, args.toArray());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<String> getArchivedItems(Integer puid, Integer shopId) {
        String sql = "select target_id from t_amazon_ad_ne_targeting where ";
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id",shopId)
                .equalTo("state", "archived")
                .build();
        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql, String.class, conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }


    }

    @Override
    public List<String> getUpdateAfterReportSyncTimeItems(Integer puid, Integer shopId, LocalDateTime syncAt) {
        String sql = "select target_id from t_amazon_ad_ne_targeting where ";
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id",shopId)
                .greaterThan("update_time", syncAt)
                .build();
        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql, String.class, conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }



    }

    @Override
    public List<AmazonAdNeTargeting> getListByGroupIds(Integer puid, Integer shopId, List<String> adGroupIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
            .equalTo("puid", puid)
            .equalTo("shop_id", shopId)
            .in("ad_group_id", adGroupIds.toArray())
            .in("state", new Object[] {"enabled", "paused"})
            .build();
        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonAdTargetingTypeBo> listTargetTypeBoByGroupIds(Integer puid, Integer shopId, List<String> adGroupIds) {
        StringBuilder sb = new StringBuilder("select ad_group_id adGroupId, expression expression, target_id targetId ")
                .append(" from ").append(this.getJdbcHelper().getTable()).append(" where ");
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("ad_group_id", adGroupIds.toArray()).build();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sb + builder.getSql(), builder.getValues(), new BeanPropertyRowMapper<>(AmazonAdTargetingTypeBo.class));
        } finally {
            hintManager.close();
        }


    }

    @Override
    public List<String> getTargetListsByKeyword(Integer puid,CpcQueryWordDto dto){
        List argIdList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder(" select w.target_id from t_amazon_ad_ne_targeting w where w.puid= ? ");
        argIdList.add(puid);
        if(dto.getShopId()!=null) {
            sql.append(" and w.shop_id=? ");
            argIdList.add(dto.getShopId());
        }
        if (StringUtils.isNotBlank(dto.getState())) {
            sql.append(" and w.state = ? ");
            argIdList.add(dto.getState());
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            List<String> targetIds = getJdbcTemplate(puid, hintManager).queryForList(sql.toString(),argIdList.toArray(),String.class);
            return targetIds;
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<String> getTargetListsByTarget(Integer puid, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder whereSql = new StringBuilder();

        //联表拆分成单表查询先查关联targetId条件
        StringBuilder findIdSql = new StringBuilder(" select c.target_id from t_amazon_ad_ne_targeting c where c.puid= ? ");

        argsList.add(puid);
        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }

        whereSql.append(" and c.type != 'negativeAsin' ");

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            whereSql.append(" and c.campaign_id = ? ");
            argsList.add(param.getCampaignId());
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            whereSql.append(" and c.ad_group_id = ? ");
            argsList.add(param.getGroupId());
        }

        //标签管理
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.target_id", param.getTargetIds(), argsList));
        }

        if (StringUtils.isNotBlank(param.getChosenTargetType())) {
            if ("auto".equalsIgnoreCase(param.getChosenTargetType())) {
                whereSql.append(" and c.type='auto' ");
            } else {
                whereSql.append(" and c.type in ('asin', 'category') ");
            }
        }

        if (StringUtils.isNotBlank(param.getFilterTargetType())) {
            if ("asin".equalsIgnoreCase(param.getFilterTargetType())) {
                whereSql.append(" and c.type='asin' ");
            } else if ("category".equalsIgnoreCase(param.getFilterTargetType())) {
                whereSql.append(" and c.type='category'  ");
            } else {
                whereSql.append(" and c.targeting_value = ? and c.type = 'auto' ");
                argsList.add(param.getFilterTargetType());
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and c.serving_status = ? ");
                argsList.add(AmazonAdTargeting.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }

        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                whereSql.append(" and c.targeting_value = ? and c.type = 'asin' ");
                argsList.add(param.getSearchValue());
            } else if ("category".equalsIgnoreCase(param.getSearchField())) {
                whereSql.append(" and c.targeting_value like ? and c.type = 'category' ");
                argsList.add("%" + param.getSearchValue() + "%");
            }
        }

        // 开启了高级搜索 竞价值
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {
            if (param.getBidMin() != null) {
                whereSql.append(" and c.bid >= ? ");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and c.bid <= ? ");
                argsList.add(param.getBidMax());
            }
        }

        findIdSql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            List<String> targetIds = getJdbcTemplate(puid, hintManager).queryForList(findIdSql.toString(), argsList.toArray(), String.class);
            return targetIds;
        } finally {
            hintManager.close();
        }


    }

    @Override
    public Integer statSumCountByAdGroupId(Integer puid, Integer shopId, List<String> status, List<String> adGroupIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("ad_group_id", adGroupIds.toArray())
                .in("state", status.toArray())
                .and()
                .leftBracket()
                .in(LogicType.EPT, "type", new Object[]{TargetTypeEnum.category.name(), TargetTypeEnum.asin.name()})
                .or()
                .equalTo(LogicType.EPT, "expression_type", "auto")
                .rightBracket()
                .build();

        String sql = "select  count(*) c from t_amazon_ad_ne_targeting where " + conditionBuilder.getSql();

        HintManager hintManager = HintManager.getInstance();
        try {
            List<Integer> list = getJdbcTemplate(puid, hintManager).queryForList(sql,Integer.class, conditionBuilder.getValues());
            return CollectionUtils.isNotEmpty(list) ? list.get(0) : 0 ;
        } finally {
            hintManager.close();
        }

    }

    @Override
    public Integer statSumCountByAdGroupPage(Integer puid, Integer shopId, List<String> status, GroupPageParam param) {
        StringBuilder builder = new StringBuilder("select count(*) c from t_amazon_ad_ne_targeting where puid = ? and shop_id = ? ");
        List<Object> args = Lists.newArrayList(puid,shopId);
        builder.append(SqlStringUtil.dealInList("state" ,status,args));
        builder.append(" and ad_group_id in ( ").append(getGroupPageSql(puid,param,args)).append(" )");
        builder.append(" and ( ").append(" type in (?,?) or expression_type = 'auto' )");
        args.add(TargetTypeEnum.category.name());
        args.add(TargetTypeEnum.asin.name());
        HintManager hintManager = HintManager.getInstance();
        try {
            List<Integer> list = getJdbcTemplate(puid, hintManager).queryForList(builder.toString(),Integer.class,args.toArray());
            return CollectionUtils.isNotEmpty(list) ? list.get(0) : 0 ;
        } finally {
            hintManager.close();
        }


    }

    @Override
    public List<String> getByTargetIdList(Integer puid, Integer shopId,
                                                     List<String> campaignIds,List<String> groupIds,String state,String targetType,List<String> targetIdList) {
        StringBuilder sql = new StringBuilder("SELECT target_id" +
                " FROM t_amazon_ad_ne_targeting ");
        List<Object> args = new ArrayList<>();
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? ");
        args.add(puid);
        args.add(shopId);
        whereSql.append(SqlStringUtil.dealInList("target_id", targetIdList, args));
        if ("autoTarget".equals(targetType)) {
            whereSql.append(" and type = 'auto'");
        } else {
            whereSql.append(" and type in ('asin','category')");
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) { //广告活动查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, args));
        }
        if (CollectionUtils.isNotEmpty(groupIds)) {  //广告组查询
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, args));

        }
        if (StringUtils.isNotBlank(state)) {//状态
            if (state.equals("all")) {
                whereSql.append(" and state in ('enabled','paused')");
            } else {
                whereSql.append(" and state = ?");
                args.add(state);
            }
        }
        sql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql.toString(),args.toArray(),String.class);
        } finally {
            hintManager.close();
        }

    }

    @Override
    public Page<AmazonAdNeTargeting> queryAdSpAutoTarget(AdTargetStrategyParam param) {
        StringBuilder sql = new StringBuilder("SELECT t.puid, t.shop_id, t.marketplace_id, t.campaign_id, t.target_id, t.ad_group_id, t.expression_type, t.bid, t.range_start, t.range_end, t.state, t.targeting_value" +
                " FROM t_amazon_ad_ne_targeting t left join t_amazon_ad_campaign_all c on (t.puid = c.puid and t.shop_id = c.shop_id and t.marketplace_id = c.marketplace_id and t.campaign_id = c.campaign_id and c.campaign_id is not null and c.type = 'sp')" +
                " left join t_amazon_ad_group g on (t.puid = g.puid and t.shop_id = g.shop_id and t.marketplace_id = g.marketplace_id and t.ad_group_id = g.ad_group_id)");
        StringBuilder countSql = new StringBuilder("SELECT count(t.id) FROM t_amazon_ad_ne_targeting t left join t_amazon_ad_campaign_all c on (t.puid = c.puid and t.shop_id = c.shop_id and t.marketplace_id = c.marketplace_id and t.campaign_id = c.campaign_id and c.campaign_id is not null and c.type = 'sp')"
                +" left join t_amazon_ad_group g on (t.puid = g.puid and t.shop_id = g.shop_id and t.marketplace_id = g.marketplace_id and t.ad_group_id = g.ad_group_id)");
        List<Object> args = new ArrayList<>();
        StringBuilder whereSql = new StringBuilder(" where t.puid = ? and c.state not in ('archived','pendingReview','rejected') and g.state not in ('archived','pendingReview','rejected')");
        args.add(param.getPuid());
        if (param.getShopId() != null) {
            whereSql.append(" and t.shop_id = ?");
            args.add(param.getShopId());
        }
        whereSql.append(" and t.type = 'auto' and t.is_pricing = 0");
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告活动查询
            whereSql.append(SqlStringUtil.dealInList("t.campaign_id", param.getCampaignIdList(), args));
        }
        if (CollectionUtils.isNotEmpty(param.getGroupIdList())) {  //广告组查询
            whereSql.append(SqlStringUtil.dealInList("t.ad_group_id", param.getGroupIdList(), args));

        }
        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {  //服务状态查询
            whereSql.append(SqlStringUtil.dealInList("t.serving_status", param.getServingStatusList(), args));

        }
        if (StringUtils.isNotBlank(param.getState())) {//状态
            if (param.getState().equals("all")) {
                whereSql.append(" and t.state in ('enabled','paused')");
            } else {
                whereSql.append(" and t.state = ?");
                args.add(param.getState());
            }
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            whereSql.append(" and t.targeting_value like ?");
            args.add("%"+param.getSearchValue()+"%");
        }
        sql.append(whereSql);
        countSql.append(whereSql);

        return getPageResult(param.getPuid(),param.getPageNo(),param.getPageSize(),
                countSql.toString(),args.toArray(),sql.toString(),args.toArray(), AmazonAdNeTargeting.class);
    }

    @Override
    public Page<AmazonAdNeTargeting> queryAdSpCommodityTarget(AdTargetStrategyParam param) {
        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_ne_targeting t left join t_amazon_ad_campaign_all c on (t.puid = c.puid and t.shop_id = c.shop_id and t.marketplace_id = c.marketplace_id and t.campaign_id = c.campaign_id and c.campaign_id is not null and c.type = 'sp')" +
                " left join t_amazon_ad_group g on (t.puid = g.puid and t.shop_id = g.shop_id and t.marketplace_id = g.marketplace_id and t.ad_group_id = g.ad_group_id)");
        StringBuilder countSql = new StringBuilder("SELECT count(t.id) FROM t_amazon_ad_ne_targeting t left join t_amazon_ad_campaign_all c on (t.puid = c.puid and t.shop_id = c.shop_id and t.marketplace_id = c.marketplace_id and t.campaign_id = c.campaign_id and c.campaign_id is not null and c.type = 'sp')"
                +" left join t_amazon_ad_group g on (t.puid = g.puid and t.shop_id = g.shop_id and t.marketplace_id = g.marketplace_id and t.ad_group_id = g.ad_group_id)");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where t.puid=? and c.state not in ('archived','pendingReview','rejected') and g.state not in ('archived','pendingReview','rejected')");
        argsList.add(param.getPuid());

        if (param.getShopId() != null) {
            whereSql.append(" and t.shop_id = ?");
            argsList.add(param.getShopId());
        }

        whereSql.append(" and t.type in ('asin','category') and is_pricing = 0");
        if (StringUtils.isNotBlank(param.getState())) {//状态
            if (param.getState().equals("all")) {
                whereSql.append(" and t.state in ('enabled','paused')");
            } else {
                whereSql.append(" and t.state = ?");
                argsList.add(param.getState());
            }
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("t.campaign_id",
                    param.getCampaignIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {  //服务状态查询
            whereSql.append(SqlStringUtil.dealInList("t.serving_status", param.getServingStatusList(), argsList));

        }
        if (CollectionUtils.isNotEmpty(param.getGroupIdList())) {  //广告组id
            whereSql.append(SqlStringUtil.dealInList("t.ad_group_id", param.getGroupIdList(), argsList));

        }

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            whereSql.append(" and t.targeting_value like ?");
            argsList.add("%"+param.getSearchValue()+"%");
        }

        selectSql.append(whereSql);
        countSql.append(whereSql);

        Object[] args = argsList.toArray();

        return getPageResult(param.getPuid(),param.getPageNo(),param.getPageSize(),
                countSql.toString(),args,selectSql.toString(),args, AmazonAdNeTargeting.class);
    }


    private String getGroupPageSql(Integer puid, GroupPageParam param,List<Object> args) {
        StringBuilder selectSql = new StringBuilder("select ad_group_id from t_amazon_ad_group ");

        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        args.add(puid);

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            args.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            whereSql.append(" and campaign_id = ? ");
            args.add(param.getCampaignId());
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), args));
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIds())) {  // 广告标签
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", param.getGroupIds(), args));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and name like ? ");
            args.add("%" + param.getSearchValue().trim() + "%");
        }
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (param.getBidMin() != null) {   //默认竞价
                whereSql.append(" and default_bid >= ? ");
                args.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and default_bid <= ? ");
                args.add(param.getBidMax());
            }
        }
        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, args));
        }
        if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
            whereSql.append(" and serving_status = 'AD_GROUP_STATUS_ENABLED' ");
        }
        selectSql.append(whereSql);
        return selectSql.toString();
    }

    @Override
    public AmazonAdNeTargeting getAutoByTargetId(int puid, Integer shopId, String marketplaceId, String targetId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .equalTo("target_id", targetId)
                .equalTo("marketplace_id",marketplaceId)
                .equalTo("type","auto")
                .build();
        return getByCondition(puid,builder);
    }

    @Override
    public void strategyUpdate(int puid, int shopId, ScheduleTaskFinishedVo message) {
        String sql = "update `t_amazon_ad_ne_targeting` set bid=? where puid = ? and shop_id = ? and target_id=?";
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql, message.getModifiedValue(),message.getPuid(),message.getShopId(),message.getItemId());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public void autoUpdate(int puid, int shopId, AdvertiseRuleTaskExecuteRecordV2Message message) {
        StringBuilder updateSql = new StringBuilder("update t_amazon_ad_ne_targeting ");
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? and target_id = ?");
        List<Object> args = Lists.newArrayList();
        if ("restore".equals(message.getOperation().name())) {
            updateSql.append(" set bid=?");
            args.add(message.getRecoveryAdjustment().getExecuteValue());
        } else {
            if ("stateClose".equals(message.getPerformOperation().get(0).getRuleAction().name())) {
                updateSql.append(" set state=?");
                args.add(message.getPerformOperation().get(0).getExecuteValue());
            } else {
                updateSql.append(" set bid=?");
                args.add(message.getPerformOperation().get(0).getExecuteValue());
            }
        }
        updateSql.append(whereSql);
        args.add(message.getPuid());
        args.add(message.getShopId());
        args.add(message.getItemId());
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(message.getPuid(), hintManager).update(updateSql.toString(),args.toArray());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdNeTargeting> getAutoTargetsByGroupIds(Integer puid, List<String> adGroupIds) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inStrList("ad_group_id", adGroupIds.toArray(new String[0]))
                .inStrList("type", new String[] {"auto"})
                .build());
    }

    @Override
    public List<AmazonAdNeTargeting> listByGroupIdsAndTargetText(Integer puid, List<String> groupIds, List<String> targetTexts) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inStrList("ad_group_id", groupIds.toArray(new String[0]))
                .inStrList("type", new String[] {"category","asin"})
                .inStrList("targeting_value", targetTexts.toArray(new String[0]))
                .build());
    }
    @Override
    public List<AmazonAdNeTargeting> listAllTargetByGroupIdsAndTargetText(Integer puid, List<String> groupIds, List<String> targetTexts) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inStrList("ad_group_id", groupIds.toArray(new String[0]))
                .inStrList("type", new String[] {"category","asin", "auto"})
                .inStrList("targeting_value", targetTexts.toArray(new String[0]))
                .build());
    }

    @Override
    public List<String> listTargetNameByTargetIds(Integer puid, Integer shopId, List<String> targetIds) {
        StringBuilder sql = new StringBuilder("select name from t_amazon_ad_ne_targeting where puid=? and shop_id=? ");
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(shopId);
        sql.append(SqlStringUtil.dealInList("target_id",targetIds,args));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql.toString(),args.toArray(), String.class);
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<String> getDiagnoseCountTargetId(DiagnoseCountParam param, boolean auto) {
        StringBuilder selectSql = new StringBuilder("select target_id from " + getJdbcHelper().getTable());
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", param.getPuid())
                .in("shop_id", param.getShopIdList().toArray());

        if (auto) {
            builder.equalTo("type", "auto");
        } else {
            builder.inStrList("type", new String[]{"asin", "category"});
        }
        //广告组ID查询
        if (CollectionUtils.isNotEmpty(param.getAdGroupIdList())) {
            builder.in("ad_group_id", param.getAdGroupIdList().toArray());
        }

        // 限制10万
        builder.limit(Constants.TOTALSIZELIMIT);

        ConditionBuilder build = builder.build();
        selectSql.append(" where ").append(build.getSql());
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(param.getPuid(), hintManager).query(selectSql.toString(), new SingleColumnRowMapper<String>(), build.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdNeTargeting> getListTargetByQuery(Integer puid, Integer shopId, String adGroupId, String targetValue, String selectType, String type) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId)
                .equalTo("select_type", selectType)
                .equalTo("type", type)
                .equalTo("targeting_value", targetValue)
                .in("state", new Object[]{"enabled","paused"})
                .build());
    }

    @Override
    public List<AmazonAdNeTargeting> autoRuleTarget(int puid, Integer shopId, List<String> campaignIds, List<String> adGroupIds, String state, String matchType, String keywordText, List<String> keywordIds, List<String> servingStatus) {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.equalTo("puid", puid);
        conditionBuilder.equalTo("shop_id", shopId);
        conditionBuilder.in("target_id",keywordIds.toArray());
        conditionBuilder.in("type", new String[] {"category","asin", "auto"});
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            conditionBuilder.in("campaign_id",campaignIds.toArray());
        }
        if (CollectionUtils.isNotEmpty(adGroupIds)) {
            conditionBuilder.in("ad_group_id",adGroupIds.toArray());
        }
        if (StringUtils.isNotBlank(state)) {
            conditionBuilder.equalTo("state",state);
        }
        if (StringUtils.isNotBlank(keywordText)) {
            conditionBuilder.like("targeting_value",keywordText);
        }
        if (CollectionUtils.isNotEmpty(servingStatus)) {
            conditionBuilder.in("serving_status",servingStatus.toArray());
        }
        return listByCondition(puid, conditionBuilder.build());
    }


    @Override
    public List<AmazonAdNeTargeting> getListTargetByTargetIds(Integer puid, List<Integer> shopIds, List<String> targetIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inIntList("shop_id", shopIds.toArray(shopIds.toArray(new Integer[]{})))
                .inStrList("target_id", targetIds.toArray(new String[]{}))
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        if (shopId != null) {
            sql.append("and shop_id = ?");
            argsList.add(shopId);
        }
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AdGroupTargetCountDto> countByGroupIdSet(Integer puid, Integer shopId, Set<String> groupIdSet) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("ad_group_id", groupIdSet.toArray(new String[]{}))
                .in("type", new String[]{TargetingTypeEnum.asin.getTagetType(), TargetingTypeEnum.category_target.getTagetType()})
                .groupBy("ad_group_id")
                .build();
        String sql = "select ad_group_id adGroupId, count(*) targetCount from " + getJdbcHelper().getTable() + " where " + conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, new BeanPropertyRowMapper<>(AdGroupTargetCountDto.class), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public Page<TargetPageVo> getTargetPage(Integer puid, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlCountSb = new StringBuilder(" select count(*) from ( select target_id ");
        StringBuilder sqlSb = new StringBuilder("select ")
                .append("t.id, t.puid, t.shop_id shopId, t.marketplace_id marketplaceId, t.target_id targetId, t.ad_group_id adGroupId, t.campaign_id campaignId,")
                .append("t.expression, t.resolved_expression resolvedExpression, t.bid, t.type, t.targeting_value targetingValue, t.title,")
                .append("t.img_url imgUrl, t.category_path categoryPath, t.state, t.serving_status servingStatus, t.range_start rangeStart, t.range_end rangeEnd, t.suggested,")
                .append("t.is_pricing isPricing, t.pricing_state pricingState, t.select_type selectType");
        StringBuilder sb = new StringBuilder(" from ").append(this.getJdbcHelper().getTable()).append(" t ");
        if ((StringUtils.isNotBlank(param.getOrderType()) && SqlStringReportUtil.BID.equals(param.getOrderField()))
                || (param.getUseAdvanced() && (param.getBidMin() != null || param.getBidMax() != null))) {
            sb.append(" join t_amazon_ad_group g on t.puid = g.puid and t.shop_id = g.shop_id and t.ad_group_id = g.ad_group_id ");
        }
        sb.append(this.getTargetPageWhereSql(puid, param, null, argsList));

        sqlSb.append(sb);
        sqlCountSb.append(sb).append(" ) c ");

        if (StringUtils.isNotBlank(param.getOrderType()) && SqlStringReportUtil.BID.equals(param.getOrderField())) {
            sqlSb.append(" order by IFNULL(t.bid, g.default_bid) ").append(OrderTypeEnum.desc.getType().equals(param.getOrderType()) ? "desc" : "").append(" ,t.id desc ");
        } else {
            sqlSb.append(" order by t.id desc ");
        }

        Object[] args = argsList.toArray();
        return getPageResultByClass(puid, param.getPageNo(), param.getPageSize(), sqlCountSb.toString(), args, sqlSb.toString(), args, TargetPageVo.class);


    }

    @Override
    public List<TargetPageVo> getTargetPageVoListByTargetIdList(Integer puid, TargetingPageParam param, List<String> targetIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ")
                .append("t.id, t.puid, t.shop_id shopId, t.marketplace_id marketplaceId, t.target_id targetId, t.ad_group_id adGroupId, t.campaign_id campaignId,")
                .append("t.expression, t.resolved_expression resolvedExpression, t.bid, t.type, t.targeting_value targetingValue, t.title,")
                .append("t.img_url imgUrl, t.category_path categoryPath, t.state, t.serving_status servingStatus, t.range_start rangeStart, t.range_end rangeEnd, t.suggested,")
                .append("t.is_pricing isPricing, t.pricing_state pricingState, t.select_type selectType")
                .append(" from ").append(this.getJdbcHelper().getTable()).append(" t ");
        sb.append(" where t.puid = ? and t.shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (CollectionUtils.isNotEmpty(targetIdList)) {
            sb.append(SqlStringUtil.dealInList("t.target_id", targetIdList, argsList))
                    .append(" order by field(target_id, ").append(StringUtil.joinString(targetIdList)).append(")");
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(TargetPageVo.class));
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<String> getTargetIdListByParam(Integer puid, TargetingPageParam param, List<String> targetIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select target_id from ").append(this.getJdbcHelper().getTable()).append(" t ");
        if (param.getUseAdvanced() && (param.getBidMax() != null || param.getBidMin() != null)) {
            sb.append(" join t_amazon_ad_group g on t.puid = g.puid and t.shop_id = g.shop_id and t.ad_group_id = g.ad_group_id ");
        }
        sb.append(this.getTargetPageWhereSql(puid, param, targetIdList, argsList));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sb.toString(), argsList.toArray(), String.class);
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AdOrderBo> getTargetIdAndOrderFieldList(Integer puid, TargetingPageParam param, List<String> targetIdList, String orderField) {
        String orderByField = "";
        if (SqlStringReportUtil.BID.equals(orderField)) {
            orderByField = "IFNULL(t.bid, g.default_bid)";
        } else if (SqlStringReportUtil.ID.equals(orderField)) {
            orderByField = "id";
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select target_id id")
                .append(StringUtils.isNotBlank(orderByField) ? "," + orderByField + " orderField" : "")
                .append(" from ").append(this.getJdbcHelper().getTable()).append(" t ");
        if (SqlStringReportUtil.BID.equals(orderField) || (param.getUseAdvanced() && (param.getBidMax() != null || param.getBidMin() != null))) {
            sb.append(" join t_amazon_ad_group g on t.puid = g.puid and t.shop_id = g.shop_id and t.ad_group_id = g.ad_group_id ");
        }
        sb.append(this.getTargetPageWhereSql(puid, param, targetIdList, argsList));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AdOrderBo.class));
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<SearchQueryTagParam> getSearchQueryTag(Integer puid, Integer shopId, List<String> matchTypeList,List<SearchQueryTagParam> queryTagParams) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ")
                .append(" k.ad_group_id as adGroupId, k.targeting_value as query")
                .append(" from t_amazon_ad_ne_targeting k ");
        sb.append(" where k.puid = ? and k.shop_id = ? and k.state != 'archived'  ");
        argsList.add(puid);
        argsList.add(shopId);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(matchTypeList)) {
            sb.append(SqlStringUtil.dealInList("k.type", matchTypeList, argsList));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(queryTagParams)) {
            sb.append(" and (k.ad_group_id, k.targeting_value) in ");
            StringBuilder stringBuilder = new StringBuilder(" ( ");
            for (SearchQueryTagParam param : queryTagParams) {
                stringBuilder.append("( ?,? ),");
                argsList.add(param.getAdGroupId());
                argsList.add(param.getQuery());
            }
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
            stringBuilder.append(" )");
            sb.append(stringBuilder);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(SearchQueryTagParam.class));
        } finally {
            hintManager.close();
        }
    }

    /**
     * 投放列表页where条件sql拼接
     */
    private String getTargetPageWhereSql(Integer puid, TargetingPageParam param, List<String> targetIdList, List<Object> argsList) {
        StringBuilder sb = new StringBuilder();
        sb.append(" where t.puid = ? and t.shop_id = ? and type != 'negativeAsin' ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (CollectionUtils.isNotEmpty(targetIdList)) {
            sb.append(SqlStringUtil.dealInList("t.target_id", targetIdList, argsList));
        }
        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            List<String> list = StringUtil.splitStr(param.getCampaignId());
            sb.append(SqlStringUtil.dealInList("t.campaign_id", list, argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            sb.append(SqlStringUtil.dealInList("t.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> list = StringUtil.splitStr(param.getGroupId());
            sb.append(SqlStringUtil.dealInList("t.ad_group_id", list, argsList));

        }
        //标签管理
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            sb.append(SqlStringUtil.dealInList("t.target_id", param.getTargetIds(), argsList));
        }

        if (StringUtils.isNotBlank(param.getChosenTargetType())) {
            if ("auto".equalsIgnoreCase(param.getChosenTargetType())) {
                sb.append(" and t.type = 'auto' ");
            } else {
                sb.append(" and t.type in ('asin','category') ");
            }
        }

        if (StringUtils.isNotBlank(param.getFilterTargetType())) {
            if ("asin".equalsIgnoreCase(param.getFilterTargetType())) {
                sb.append(" and t.type='asin' ");
            } else if ("category".equalsIgnoreCase(param.getFilterTargetType())) {
                sb.append(" and t.type='category'  ");
            } else {
                sb.append(" and t.targeting_value = ? and t.type = 'auto' ");
                argsList.add(param.getFilterTargetType());
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                sb.append(" and t.targeting_value = ? and t.type = 'asin' ");
                argsList.add(param.getSearchValue());
            } else if ("category".equalsIgnoreCase(param.getSearchField())) {
                sb.append(" and t.targeting_value like ? and t.type = 'category' ");
                argsList.add("%" + param.getSearchValue() + "%");
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            sb.append(SqlStringUtil.dealInList("t.state", statusList, argsList));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                sb.append(" and t.serving_status = ? ");
                argsList.add(AmazonAdTargeting.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                sb.append(SqlStringUtil.dealInList("t.serving_status", list, argsList));
            }
        }

        //竞价高级筛选
        if (param.getUseAdvanced()) {
            if (param.getBidMin() != null) {
                sb.append(" and IFNULL(t.bid, g.default_bid) >= ? ");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                sb.append(" and IFNULL(t.bid, g.default_bid) <= ? ");
                argsList.add(param.getBidMax());
            }
        }
        return sb.toString();
    }

    @Override
    public List<AmazonAdNeTargeting> listByTargetingValue(Integer puid, Integer shopId, List<NegativeArchiveRequest.NegativeInfo> infoList) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select id, target_id, targeting_value, campaign_id, ad_group_id, type," +
                " state from " + getJdbcHelper().getTable() + " where state != 'archived' and puid = ? and shop_id = ? ");
        args.add(puid);
        args.add(shopId);
        sql.append(SqlStringUtil.dealMultiColumnIn(Lists.newArrayList("ad_group_id", "targeting_value"), infoList,
                Lists.newArrayList(
                        NegativeArchiveRequest.NegativeInfo::getAdGroupId,
                        NegativeArchiveRequest.NegativeInfo::getNegativeText
                ),
                args));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), args.toArray(), getMapper());
        } finally {
            hintManager.close();
        }
    }
}