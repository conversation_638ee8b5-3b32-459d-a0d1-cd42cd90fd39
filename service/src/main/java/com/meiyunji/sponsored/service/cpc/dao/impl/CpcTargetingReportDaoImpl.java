package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.enums.AmazonTargetingReportTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingSphereDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.syncReport.AdTypeEnum;
import com.meiyunji.sponsored.service.cpc.bo.AdOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.ReportMonitorBo;
import com.meiyunji.sponsored.service.cpc.dao.ICpcTargetingReportDao;
import com.meiyunji.sponsored.service.cpc.dto.AdReportData;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.po.CpcTargetingReport;
import com.meiyunji.sponsored.service.cpc.qo.ReportAdvancedFilterBaseQo;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.TargetFirstPlaceIsDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.DiagnoseCountParam;
import com.meiyunji.sponsored.service.reportDiffMonitor.dto.ShopDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.SingleColumnRowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * CpcTargetingReport
 * <AUTHOR>
 */
@Repository
public class  CpcTargetingReportDaoImpl extends BaseShardingSphereDaoImpl<CpcTargetingReport> implements ICpcTargetingReportDao {

    @Autowired
    private DynamicRefreshNacosConfiguration nacosConfiguration;

    @Override
    public void insertList(Integer puid, List<CpcTargetingReport> list) {
        //插入原表
        insertListOriginAndHotTable(puid, list, getJdbcHelper().getTable());

        //写入开关开启且数据是95天内的，就插入热表
        if (nacosConfiguration.isHotTableWritePhase2Enable()) {
            //筛选出95天内的数据
            List<CpcTargetingReport> hotList = list.stream()
                .filter(k -> (StringUtils.isNotBlank(k.getCountDate())))
                .filter(k -> DateUtil.getDayBetween(DateUtil.strToDate(k.getCountDate(), DateUtil.PATTERN_YYYYMMDD), new Date()) <= com.meiyunji.sponsored.common.base.Constants.HOT_SAVE_DAYS)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hotList)) {
                //插入热表
                insertListOriginAndHotTable(puid, hotList, getHotTableName());
            }
        }

    }

    private void insertListOriginAndHotTable(Integer puid, List<CpcTargetingReport> list, String tableName) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(tableName);
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`campaign_id`,")
                .append("`ad_group_id`,`target_id`,`count_date`,main_image,`targeting_expression`,`targeting_text`,")
                .append("`targeting_type`,`ad_group_name`,`campaign_name`,`cost`,`cost_rmb`, ")
                .append("`cost_usd`,`total_sales`,`total_sales_rmb`,`total_sales_usd`,`ad_sales`,`ad_sales_rmb`,")
                .append("`ad_sales_usd`,`impressions`,`clicks`,`order_num`,`ad_order_num`,`sale_num`,`ad_sale_num`, `top_of_search_is`,`create_time`,`update_time`) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for (CpcTargetingReport report : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getCampaignId());
            argsList.add(report.getAdGroupId());
            argsList.add(report.getTargetId());
            argsList.add(report.getCountDate());
            argsList.add(report.getMainImage());
            argsList.add(report.getTargetingExpression());
            argsList.add(report.getTargetingText());
            argsList.add(report.getTargetingType());
            argsList.add(report.getAdGroupName());
            argsList.add(report.getCampaignName());
            argsList.add(report.getCost());
            argsList.add(report.getCostRmb());
            argsList.add(report.getCostUsd());
            argsList.add(report.getTotalSales());
            argsList.add(report.getTotalSalesRmb());
            argsList.add(report.getTotalSalesUsd());
            argsList.add(report.getAdSales());
            argsList.add(report.getAdSalesRmb());
            argsList.add(report.getAdSalesUsd());
            argsList.add(report.getImpressions());
            argsList.add(report.getClicks());
            argsList.add(report.getOrderNum());
            argsList.add(report.getAdOrderNum());
            argsList.add(report.getSaleNum());
            argsList.add(report.getAdSaleNum());
            argsList.add(report.getTopOfSearchIs());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `ad_group_name`=values(ad_group_name),`campaign_name`=values(campaign_name),main_image=values(main_image),")
                .append("`cost`=values(cost),cost_rmb=values(cost_rmb),cost_usd=values(cost_usd),`total_sales`=values(total_sales),`total_sales_rmb`=values(total_sales_rmb),`total_sales_usd`=values(total_sales_usd),")
                .append("`ad_sales`=values(ad_sales),`ad_sales_rmb`=values(ad_sales_rmb),`ad_sales_usd`=values(ad_sales_usd),`impressions`=values(impressions),`clicks`=values(clicks),")
                .append("`order_num`=values(order_num),`ad_order_num`=values(ad_order_num),`sale_num`=values(sale_num),`ad_sale_num`=values(ad_sale_num),`top_of_search_is`=values(top_of_search_is)");
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<CpcTargetingReport> listSumReports(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, List<String> targetingIds) {
        String sql = "SELECT target_id,sum(`cost`) cost, sum(`cost_rmb`) cost_rmb, sum(`cost_usd`) cost_usd," +
                "sum(`total_sales`) total_sales, sum(`total_sales_rmb`) total_sales_rmb, sum(`total_sales_usd`) total_sales_usd," +
                "sum(`ad_sales`) ad_sales,sum(`ad_sales_rmb`) ad_sales_rmb,sum(`ad_sales_usd`) ad_sales_usd," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`sale_num`) order_num, sum(`ad_order_num`) ad_order_num, sum(`sale_num`) sale_num," +
                "sum(`ad_sale_num`) ad_sale_num " +
                "FROM " + getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketplaceId)
                .greaterThanOrEqualTo("count_date", startStr)
                .lessThanOrEqualTo("count_date", endStr)
                .in("target_id", targetingIds.toArray())
                .groupBy("target_id")
                .build();

        sql += conditionBuilder.getSql();

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }


    }

    @Override
    public List<CpcTargetingReport> listReports(Integer puid, Integer shopId, String startDate, String endDate, String targetId) {
        String sql = "SELECT count_date,campaign_id,ad_group_id,target_id,shop_id,sum(`cost`) cost, sum(`cost_rmb`) cost_rmb, sum(`cost_usd`) cost_usd," +
                "sum(`total_sales`) total_sales, sum(`total_sales_rmb`) total_sales_rmb, sum(`total_sales_usd`) total_sales_usd," +
                "sum(`ad_sales`) ad_sales,sum(`ad_sales_rmb`) ad_sales_rmb,sum(`ad_sales_usd`) ad_sales_usd," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`order_num`) order_num, sum(`ad_order_num`) ad_order_num, sum(`sale_num`) sale_num," +
                "sum(`ad_sale_num`) ad_sale_num " +
                "FROM " + getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate)
                .equalTo("target_id", targetId)
                .groupBy("count_date")
                .build();

        sql += conditionBuilder.getSql();

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }


    }

    @Override
    public Page getPageList(Integer puid, SearchVo search, Page page) {
        StringBuilder sql = new StringBuilder("select `puid`,`shop_id`,`marketplace_id`,`count_date`,`type`, " +
                "keyword_id,target_id, keyword_text,match_type,targeting_expression," +
                "targeting_type,targeting_text,ad_group_id,ad_group_name,campaign_id,campaign_name,impressions," +
                "clicks,cost,sale_num,ad_order_num,total_sales,`ad_sales`,`ad_sale_num`,order_num from ( ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");

        String unionTableName = getTableNameByStartDateAndTableName(search.getStart(), "t_amazon_ad_keyword_report");
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectKeywordSql = new StringBuilder("SELECT `puid`,`shop_id`,`marketplace_id`,`count_date`,'keyword' as `type`, keyword_id, '' as target_id,")
                .append(" keyword_text,match_type,'' as targeting_expression,'' as targeting_type,'' as targeting_text,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ")
                .append(unionTableName)
                .append(" ");
        List<Object> argList = new ArrayList<>();
        String whereSql = getWhereSqlCountByKeyword(puid,search,argList);
        selectKeywordSql.append(whereSql);

        String tableName = getTableNameByStartDate(search.getStart());
        StringBuilder selectTargetSql = new StringBuilder("SELECT `puid`,`shop_id`,`marketplace_id`,`count_date`,'target' as `type`,'' as keyword_id,  target_id, ")
                .append(" '' as keyword_text,'' as match_type,targeting_expression,targeting_type,targeting_text,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ")
                .append(tableName)
                .append(" ");

        String whereSqlTarget = getWhereSqlCountByTargetId(puid,search,argList);
        selectTargetSql.append(whereSqlTarget);


        sql.append(selectKeywordSql);
        sql.append(" UNION ALL ");
        sql.append(selectTargetSql);
        sql.append(" ) p");

        countSql.append(selectKeywordSql);
        countSql.append(" UNION ALL ");
        countSql.append(selectTargetSql);
        countSql.append(" ) p");

        return getPageByMapper(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), argList.toArray(), sql.toString(), argList.toArray(), new RowMapper<AdQueryTargetVo>() {
                @Override
                public AdQueryTargetVo mapRow(ResultSet res, int i) throws SQLException {
                    AdQueryTargetVo optionVo = AdQueryTargetVo.builder()
                            .type(res.getString("type"))
                            .keywordId(res.getString("keyword_id"))
                            .targetId(res.getString("target_id"))
                            .keywordText(res.getString("keyword_text"))
                            .matchType(res.getString("match_type"))
                            .targetingExpression(res.getString("targeting_expression"))
                            .targetingType(res.getString("targeting_type"))
                            .adGroupId(res.getString("ad_group_id"))
                            .adGroupName(res.getString("ad_group_name"))
                            .campaignId(res.getString("campaign_id"))
                            .campaignName(res.getString("campaign_name"))
                            .impressions(res.getInt("impressions"))
                            .clicks(res.getInt("clicks"))
                            .cost(res.getBigDecimal("cost")!= null? res.getBigDecimal("cost"):BigDecimal.ZERO)
                            /**
                             * TODO 广告报告下载中心
                             * 本广告产品销量
                             */
                            .puid(res.getInt("puid"))
                            .orderNum(res.getInt("order_num"))
                            .shopId(res.getInt("shop_id"))
                            .marketplaceId(res.getString("marketplace_id"))
                            .targetingText(res.getString("targeting_text"))
                            .adOrderNum(res.getInt("ad_order_num"))
                            .totalSales(res.getBigDecimal("total_sales")!= null? res.getBigDecimal("total_sales"):BigDecimal.ZERO)
                            //本广告产品销售额
                            .adSaleNum(Optional.ofNullable(res.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(res.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .saleNum(Optional.ofNullable(res.getInt("sale_num")).orElse(0))
                            .countDate(res.getString("count_date"))
                            .build();
                    return optionVo;
                }
            });

    }

    private String getWhereSqlCountByKeyword(int puid,SearchVo searchVo,List<Object> argsList){
        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" where puid=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(DateUtil.dateToStrWithFormat(searchVo.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(searchVo.getEnd(),"yyyyMMdd"));
        if (CollectionUtils.isNotEmpty(searchVo.getShopIds())) {
            whereSql.append(" and shop_id in ('").append(StringUtils.join(searchVo.getShopIds(),"','")).append("') ");
        }
        whereSql.append(" group by shop_id,keyword_id");
        if ("daily".equals(searchVo.getTabType())) {
            whereSql.append(", count_date");
        }
        return whereSql.toString();
    }

    private String getWhereSqlCountByTargetId(int puid,SearchVo searchVo,List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" where puid=? and count_date>=? and count_date<=?");
        argsList.add(puid);
        argsList.add(DateUtil.dateToStrWithFormat(searchVo.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(searchVo.getEnd(),"yyyyMMdd"));
        if (CollectionUtils.isNotEmpty(searchVo.getShopIds())) {
            whereSql.append(" and shop_id in ('").append(StringUtils.join(searchVo.getShopIds(),"','")).append("') ");
        }
        whereSql.append(" group by shop_id,`target_id`");
        if ("daily".equals(searchVo.getTabType())) {
            whereSql.append(", count_date");
        }
        return whereSql.toString();
    }


    @Override
    public CpcTargetingReport getDetailInfo(int puid, Integer shopId, String marketplaceId, String targetId) {
        String sql = "select * from t_cpc_targeting_report where  puid=? and shop_id=? and target_id=? order by count_date desc limit 1 ";

        HintManager hintManager = HintManager.getInstance();
        try {
            List<CpcTargetingReport> list = getJdbcTemplate(puid, hintManager).query(sql,new Object[]{puid,shopId,marketplaceId,targetId},getMapper());
            return list!= null && list.size() > 0 ? list.get(0) : null;
        } finally {
            hintManager.close();
        }
    }

    @Override
    public CpcTargetingReport getSumReportByTargetId(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String targetId) {
        StringBuilder sql = new StringBuilder("SELECT sum(`cost`) cost,sum(`cost_rmb`) cost_rmb,sum(`cost_usd`) cost_usd,sum(`total_sales`) total_sales,")
                .append("sum(`total_sales_rmb`) total_sales_rmb,sum(`total_sales_usd`) total_sales_usd,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`order_num`) order_num,")
                .append("sum(`sale_num`) sale_num,sum(`ad_sales`) ad_sales,sum(`ad_sales_rmb`) ad_sales_rmb, sum(`ad_sales_usd`) ad_sales_usd,sum(`ad_sale_num`) ad_sale_num FROM ")
                .append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)))
                .append(" where `puid`=? and `shop_id`=? and `marketplace_id`=?")
                .append(" and target_id=? and `count_date`>=? and count_date<=? ");

        HintManager hintManager = HintManager.getInstance();
        try {
            List<CpcTargetingReport> list = getJdbcTemplate(puid, hintManager).query(sql.toString(),new Object[]{puid,shopId,marketplaceId,targetId,startStr,endStr},getMapper());
            return list!=null && list.size()>0?list.get(0):null;
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<CpcTargetingReport> getChartList(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String targetId) {
        StringBuilder sql = new StringBuilder(" SELECT `count_date`,`cost`,`cost_rmb`,`cost_usd`,`total_sales`,`total_sales_rmb`, ")
                .append("`total_sales_usd`,`impressions`,`clicks`,`order_num`,`sale_num`,`ad_sales`,`ad_sales_rmb`,`ad_sales_usd`,`ad_sale_num` FROM ")
                .append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)))
                .append(" where puid=? and shop_id=? and marketplace_id=? and target_id=? and count_date>=? and count_date<=? order by count_date");

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(),new Object[]{puid,shopId,marketplaceId,targetId,startStr,endStr},getMapper());

        } finally {
            hintManager.close();
        }
    }

    @Override
    public Page detailPageList(Integer puid, Integer shopId, String marketplaceId, ReportParam param, Page page) {
        String tableName = getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD));
        StringBuilder selectSql = new StringBuilder(" SELECT shop_id,`targeting_text`,`ad_group_name`,`campaign_name`,`count_date`,`cost`,`cost_rmb`,`cost_usd`,`total_sales`,`total_sales_rmb`, ")
                .append("`total_sales_usd`,`impressions`,`clicks`,`order_num`,`sale_num`,`ad_sales`,`ad_sales_rmb`,`ad_sales_usd`,`ad_sale_num` FROM ")
                .append(tableName).append(" ");
        StringBuilder countSql = new StringBuilder("select count(*)").append(" FROM ").append(tableName).append(" ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and target_id=? and count_date>=? and count_date<=? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);
        argsList.add(param.getTargetId());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        selectSql.append(whereSql);
        countSql.append(whereSql);
        if(StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderValue())){
            String orderField = ReportService.getOrderField(param.getOrderField(),false);
            if(StringUtils.isNotBlank(orderField)){
                selectSql.append(" order by ").append(orderField);
                if("desc".equals(param.getOrderValue())){
                    selectSql.append("desc");
                }
            }
        }else{
            selectSql.append(" order by count_date desc");
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args,CpcTargetingReport.class);
    }

    @Override
    public List<Map<String, Object>> getCampaignOrAdGroupNames(int puid, Integer shopId, String marketplaceId, String campaignId) {
        String sql;
        List<Object> arg = Lists.newArrayList();
        arg.add(puid);
        arg.add(shopId);
        arg.add(marketplaceId);
        if (StringUtils.isBlank(campaignId)) {
            sql = "select campaign_id id,campaign_name name from t_cpc_targeting_report where puid=? and shop_id=? and marketplace_id=? GROUP BY campaign_id";
        } else {
            sql = "select ad_group_id id,ad_group_name name from t_cpc_targeting_report where puid=? and shop_id=? and marketplace_id=? and campaign_id=? GROUP BY ad_group_id";
            arg.add(campaignId);
        }

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql,arg.toArray());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AdHomePerformancedto> listSumReportByTargetIds(Integer puid, Integer shopId, String startStr, String endStr, TargetingPageParam param, List<String> targetIds) {

        if (CollectionUtils.isEmpty(targetIds)) {
            return null;
        }
        List<Object> argsList = new ArrayList<>();

        StringBuilder sql = new StringBuilder(" select 'sp' as type, `target_id`, sum(impressions) `impressions`,sum(clicks) `clicks`, sum(cost) `cost`, sum(total_sales) `total_sales`, sum(ad_sales)  `ad_sales`,sum(sale_num)  `sale_num`, ");
        sql.append(" sum(ad_order_num)  `ad_order_num`,sum(ad_sale_num)  `ad_sale_num`,sum(order_num) sales_num, max(top_of_search_is) max_top_is, min(top_of_search_is) min_top_is FROM ")
                .append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)))
                .append(" where puid = ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }
        whereSql.append(SqlStringUtil.dealInList("target_id", targetIds, argsList));

            whereSql.append("  and count_date >= ? and count_date <= ? group by target_id ");
        argsList.add(startStr);
        argsList.add(endStr);

        sql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .type(re.getString("type"))
                            .targetId(re.getString("target_id"))
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .maxTopIs(Optional.ofNullable(re.getBigDecimal("max_top_is")).orElse(null))
                            .minTopIs(Optional.ofNullable(re.getBigDecimal("min_top_is")).orElse(null))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdHomePerformancedto> getSpReportByDate(Integer puid, Integer shopId, String startStr, String endStr, TargetingPageParam param) {

        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sp' as type,c.target_id target_id, `count_date`, sum(impressions) `impressions`,sum(clicks) `clicks`, sum(cost) `cost`, sum(total_sales) `total_sales`, sum(ad_sales)  `ad_sales`,sum(sale_num)  `sale_num`, ");
        sql.append(" sum(ad_order_num)  `ad_order_num`,sum(ad_sale_num)  `ad_sale_num`,sum(order_num) sales_num from  ");
        sql.append(" t_amazon_ad_targeting c join ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" r on r.puid=c.puid and r.shop_id=c.shop_id and r.target_id=c.target_id where c.puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }

        whereSql.append(" and c.type != 'negativeAsin' ");

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id

            List<String> list = StringUtil.splitStr(param.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", list, argsList));

        }
        if(StringUtils.isNotBlank(param.getSelectType())){
            whereSql.append(" and c.select_type = ? ");
            argsList.add(param.getSelectType());
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> list = StringUtil.splitStr(param.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", list, argsList));
        }

        //标签管理
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.target_id", param.getTargetIds(), argsList));
        }

        if (StringUtils.isNotBlank(param.getChosenTargetType())) {
            if ("auto".equalsIgnoreCase(param.getChosenTargetType())) {
                whereSql.append(" and c.type='auto' ");
            } else {
                whereSql.append(" and c.type in ('asin', 'category') ");
            }
        }

        if (StringUtils.isNotBlank(param.getFilterTargetType())) {
            if ("asin".equalsIgnoreCase(param.getFilterTargetType())) {
                whereSql.append(" and c.type='asin' ");
            } else if ("category".equalsIgnoreCase(param.getFilterTargetType())) {
                whereSql.append(" and c.type='category'  ");
            } else {
                whereSql.append(" and c.targeting_value = ? and c.type = 'auto' ");
                argsList.add(param.getFilterTargetType());
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append("and c.serving_status = ? ");
                argsList.add(AmazonAdTargeting.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("c.serving_status", list, argsList));
            }

        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                whereSql.append(" and c.targeting_value = ? and c.type = 'asin' ");
                argsList.add(param.getSearchValue());
            } else if ("category".equalsIgnoreCase(param.getSearchField())) {
                whereSql.append(" and c.targeting_value like ? and c.type = 'category' ");
                argsList.add("%" + param.getSearchValue() + "%");
            }
        }

        // 开启了高级搜索 竞价值
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {
            if (param.getBidMin() != null) {
                whereSql.append(" and c.bid >= ? ");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and c.bid <= ? ");
                argsList.add(param.getBidMax());
            }
        }

        whereSql.append("  and r.count_date >= ? and r.count_date <= ? group by c.target_id  ");
        argsList.add(startStr);
        argsList.add(endStr);

        whereSql.append(subWhereSql(param, argsList)); // 高级筛选
        sql.append(whereSql);


        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .targetId(re.getString("target_id"))
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .type(re.getString("type"))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdHomePerformancedto> getSpReportByTargetIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> targetIdList, boolean isGroupBy) {
        if (CollectionUtils.isEmpty(targetIdList)) {
            return new ArrayList<>();
        }

        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sp' as type,`count_date`, sum(impressions) `impressions`,sum(clicks) `clicks`, sum(cost) `cost`, sum(total_sales) `total_sales`, sum(ad_sales)  `ad_sales`,sum(sale_num)  `sale_num`, ");
        sql.append("  sum(ad_order_num)  `ad_order_num`,sum(ad_sale_num)  `ad_sale_num`,sum(order_num) sales_num from  ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where puid= ? and shop_id= ? ");

        argsList.add(puid);
        argsList.add(shopId);

        sql.append(SqlStringUtil.dealInList("target_id", targetIdList, argsList));

        sql.append("  and count_date >= ? and count_date <= ? ");
        if (isGroupBy) {
            sql.append(" group by count_date ");
        }
        argsList.add(startStr);
        argsList.add(endStr);


        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .type(re.getString("type"))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdReportData> getAllReportByTargetIdsAndDate(Integer puid, Integer shopId, String startStr, String endStr, List<String> targetIdList, List<String> adGroupIds) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder selectSql = new StringBuilder("select target_id,count_date,ad_group_id,")
                .append("IFNULL(SUM(cost), 0) cost,")
                .append("IFNULL(SUM(total_sales), 0) adSale,")
                .append("IFNULL(SUM(ad_sales), 0) adSelfSale,")
                .append("IFNULL(SUM(impressions), 0) impressions,")
                .append("IFNULL(SUM(clicks), 0) clicks,")
                .append("IFNULL(SUM(sale_num),0) adOrderNum,")
                .append("IFNULL(SUM(ad_order_num),0) adSelfSaleNum,")
                .append("IFNULL(SUM(order_num),0) adSaleNum,")
                .append("IFNULL(SUM(ad_sale_num),0) adSelfOrderNum FROM ");

        startStr = startStr.contains("-") ? startStr.replaceAll("-", "") : startStr;
        endStr = endStr.contains("-") ? endStr.replaceAll("-", "") : endStr;
        selectSql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
//        selectSql.append(" t_cpc_targeting_report ");
        selectSql.append(" WHERE puid = ? ");
        argsList.add(puid);
        if (Objects.nonNull(shopId)) {
            selectSql.append(" and shop_id = ?");
            argsList.add(shopId);
        }
        if (CollectionUtils.isNotEmpty(targetIdList)) {
            selectSql.append(SqlStringUtil.dealInList("target_id", targetIdList, argsList));
        }

        if (CollectionUtils.isNotEmpty(adGroupIds)) {
            selectSql.append(SqlStringUtil.dealInList("ad_group_id", adGroupIds, argsList));
        }

        selectSql.append(" and count_date >= ?");
        argsList.add(startStr);
        selectSql.append(" and count_date <= ?");
        argsList.add(endStr);
        selectSql.append(" group by target_id,count_date ");
        HintManager hintManager = HintManager.getInstance();
        try {
            // 也可以直接在对象 写注解
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), (re, i) -> {
                AdReportData dto = new AdReportData();
                dto.setOtherId(re.getString("ad_group_id"));
                dto.setCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
                dto.setItemId(re.getString("target_id"));
                dto.setImpressions(Optional.of(re.getLong("impressions")).orElse(0L));
                dto.setClicks(Optional.of(re.getLong("clicks")).orElse(0L));
                dto.setAdOrderNum(Optional.of(re.getInt("adOrderNum")).orElse(0));
                dto.setAdSelfOrderNum(Optional.of(re.getInt("adSelfOrderNum")).orElse(0));
                dto.setAdSale(Optional.ofNullable(re.getBigDecimal("adSale")).orElse(BigDecimal.ZERO));
                dto.setAdSelfSale(Optional.ofNullable(re.getBigDecimal("adSelfSale")).orElse(BigDecimal.ZERO));
                dto.setAdSaleNum(Optional.of(re.getInt("adSaleNum")).orElse(0));
                dto.setAdSelfSaleNum(Optional.of(re.getInt("adSelfSaleNum")).orElse(0));
                dto.setType(Constants.SP);
                dto.setCountDate(re.getString("count_date"));
                return dto;
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public AdMetricDto getSumAdMetric(Integer puid, Integer shopId, String startStr, String endStr, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();

        StringBuilder sql = new StringBuilder(" select 'sp' as type, sum(cost) `cost`, sum(total_sales) `total_sales`,sum(sale_num)  `sale_num`, sum(order_num) sales_num ");
        sql.append(" from t_amazon_ad_targeting c join ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" r on r.puid=c.puid and r.shop_id=c.shop_id and r.target_id=c.target_id where c.puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }

        whereSql.append(" and c.type != 'negativeAsin' ");

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id

            List<String> list = StringUtil.splitStr(param.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", list, argsList));

        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> list = StringUtil.splitStr(param.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", list, argsList));
        }

        //标签管理
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.target_id", param.getTargetIds(), argsList));
        }

        if (StringUtils.isNotBlank(param.getChosenTargetType())) {
            if ("auto".equalsIgnoreCase(param.getChosenTargetType())) {
                whereSql.append(" and c.type='auto' ");
            } else {
                whereSql.append(" and c.type in ('asin', 'category') ");
            }
        }

        if (StringUtils.isNotBlank(param.getFilterTargetType())) {
            if ("asin".equalsIgnoreCase(param.getFilterTargetType())) {
                whereSql.append(" and c.type='asin' ");
            } else if ("category".equalsIgnoreCase(param.getFilterTargetType())) {
                whereSql.append(" and c.type='category'  ");
            } else {
                whereSql.append(" and c.targeting_value = ? and c.type = 'auto' ");
                argsList.add(param.getFilterTargetType());
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }

        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append("and c.serving_status = ? ");
                argsList.add(AmazonAdTargeting.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("c.serving_status", list, argsList));
            }

        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                whereSql.append(" and c.targeting_value = ? and c.type = 'asin' ");
                argsList.add(param.getSearchValue());
            } else if ("category".equalsIgnoreCase(param.getSearchField())) {
                whereSql.append(" and c.targeting_value like ? and c.type = 'category' ");
                argsList.add("%" + param.getSearchValue() + "%");
            }
        }

        // 开启了高级搜索 竞价值
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {
            if (param.getBidMin() != null) {
                whereSql.append(" and c.bid >= ? ");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and c.bid <= ? ");
                argsList.add(param.getBidMax());
            }
        }

        whereSql.append("  and r.count_date >= ? and r.count_date <= ? ");
        argsList.add(startStr);
        argsList.add(endStr);

        whereSql.append(subWhereSql(param, argsList)); // 高级筛选
        sql.append(whereSql);


        HintManager hintManager = HintManager.getInstance();

        try {
            List<AdMetricDto> list = getJdbcTemplate(puid, hintManager).query(sql.toString(), (re, i) -> {
                AdMetricDto dto = AdMetricDto.builder()
                        .sumCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .sumAdSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("sale_num")).orElse(BigDecimal.ZERO))
                        .sumOrderNum(Optional.ofNullable(re.getBigDecimal("sales_num")).orElse(BigDecimal.ZERO))
                        .build();
                return dto;
            }, argsList.toArray());
            return list!= null && list.size() > 0 ? list.get(0) : null;
        } finally {
            hintManager.close();
        }
    }


    @Override
    public List<String> getTargetListByUpdateTime(Integer puid, Integer shopId, Date date) {
        String sql = "select target_id from (select sum(`impressions`) impressions, target_id from t_cpc_targeting_report where " +
                " puid = ? and shop_id=?  and update_time > ? group by target_id having impressions > 0) a ";

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql, new Object[]{puid,shopId,date}, String.class);
        } finally {
            hintManager.close();
        }


    }

    @Override
    public List<CpcTargetingReport> getReportVoListByGroupIds(Integer puid, List<String> spGroupIds, TargetReportSearchVo searchVo) {
        String sql = "SELECT campaign_id,ad_group_id,campaign_name,ad_group_name,target_id,shop_id,marketplace_id,targeting_text,sum(`cost`) cost," +
                " sum(`total_sales`) total_sales, " +
                " sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`sale_num`) sale_num " +
                " FROM " + getTableNameByStartDate(searchVo.getStart()) + " where ";
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", searchVo.getShopId())
                .greaterThanOrEqualTo("count_date", DateUtil.dateToStrWithFormat(searchVo.getStart(),"yyyyMMdd"))
                .lessThanOrEqualTo("count_date", DateUtil.dateToStrWithFormat(searchVo.getEnd(),"yyyyMMdd"))
                .inStrList("ad_group_id", spGroupIds.toArray(new String[]{}))
                .groupBy("target_id")
                .build();

        sql += conditionBuilder.getSql();

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }


    }

    @Override
    public CpcTargetingReport getReportVoByTargetId(Integer puid, String targetId, QueryTargetReportSearchVo vo) {
        String sql = "SELECT campaign_id,ad_group_id,campaign_name,ad_group_name,target_id,shop_id,marketplace_id,targeting_text,sum(`cost`) cost," +
                " sum(`total_sales`) total_sales, " +
                " sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`sale_num`) sale_num " +
                " FROM " + getTableNameByStartDate(vo.getStart()) + " where ";
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", vo.getShopId())
                .greaterThanOrEqualTo("count_date", DateUtil.dateToStrWithFormat(vo.getStart(),"yyyyMMdd"))
                .lessThanOrEqualTo("count_date", DateUtil.dateToStrWithFormat(vo.getEnd(),"yyyyMMdd"))
                .equalTo("target_id", targetId)
                .build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            List<CpcTargetingReport> query = getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
            if (CollectionUtils.size(query) == 1 && StringUtils.isEmpty(query.get(0).getTargetId())) {
                return null;
            }
            return CollectionUtils.isNotEmpty(query) ? query.get(0) : null;
        } finally {
            hintManager.close();
        }
    }

    @Override
    public CpcTargetingReport getDetailsSumVo(Integer puid, TargetReportDetailsVo detailsVo) {
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,marketplace_id, ")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`sale_num`) sale_num,sum(`cost`) cost,")
                .append("sum(`total_sales`) total_sales  FROM ")
                .append(getTableNameByStartDate(DateUtil.strToDate(detailsVo.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));

        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and target_id= ? ");
        argsList.add(puid);
        argsList.add(detailsVo.getShopId());
        argsList.add(detailsVo.getTargetId());


        whereSql.append("and count_date>=? and count_date<=? ");
        argsList.add(detailsVo.getStartDate());
        argsList.add(detailsVo.getEndDate());
        selectSql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            List<CpcTargetingReport> reportList = getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
            return CollectionUtils.isNotEmpty(reportList) ? reportList.get(0) : null;
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<CpcTargetingReport> getListTargetDetailsDay(Integer puid, TargetReportDetailsVo detailsVo) {
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,marketplace_id,count_date, ")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`sale_num`) sale_num,sum(`cost`) cost,")
                .append("sum(`total_sales`) total_sales  FROM ")
                .append(getTableNameByStartDate(detailsVo.getStart()));

        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and target_id= ? ");
        argsList.add(puid);
        argsList.add(detailsVo.getShopId());
        argsList.add(detailsVo.getTargetId());

        whereSql.append("and count_date>=? and count_date<=? group by count_date ");
        argsList.add(DateUtil.dateToStrWithFormat(detailsVo.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(detailsVo.getEnd(),"yyyyMMdd"));
        selectSql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public CpcTargetingReport getQueryDetailsSumVo(Integer puid, QueryTargetReportDetailsVo detailsVo) {
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,marketplace_id, ")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`sale_num`) sale_num,sum(`cost`) cost,")
                .append("sum(`total_sales`) total_sales  FROM ")
                .append(getTableNameByStartDate(DateUtil.strToDate(detailsVo.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));

        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and target_id= ? ");
        argsList.add(puid);
        argsList.add(detailsVo.getShopId());
        argsList.add(detailsVo.getTargetId());


        whereSql.append("and count_date>=? and count_date<=? ");
        argsList.add(detailsVo.getStartDate());
        argsList.add(detailsVo.getEndDate());
        selectSql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            List<CpcTargetingReport> reportList = getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
            return CollectionUtils.isNotEmpty(reportList) ? reportList.get(0) : null;
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<CpcTargetingReport> getQueryTargetDetailsDay(Integer puid, QueryTargetReportDetailsVo detailsVo) {
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,marketplace_id,count_date, ")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`sale_num`) sale_num,sum(`cost`) cost,")
                .append("sum(`total_sales`) total_sales  FROM ")
                .append(getTableNameByStartDate(detailsVo.getStart()));

        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and target_id= ? ");
        argsList.add(puid);
        argsList.add(detailsVo.getShopId());
        argsList.add(detailsVo.getTargetId());

        whereSql.append("and count_date>=? and count_date<=? group by count_date ");
        argsList.add(DateUtil.dateToStrWithFormat(detailsVo.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(detailsVo.getEnd(),"yyyyMMdd"));
        selectSql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<CpcTargetingReport> getReportByTargetId(Integer puid, Integer shopId, String start, String end, String marketplaceId, String targetId) {

        String sql = "SELECT * FROM " + getTableNameByStartDate(DateUtil.strToDate(start, DateUtil.PATTERN_YYYYMMDD)) + " where ";
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketplaceId)
                .equalTo("target_id", targetId)
                .greaterThanOrEqualTo("count_date", start)
                .lessThanOrEqualTo("count_date", end).build();
        sql += " " + builder.getSql();

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, builder.getValues(), getRowMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> getDiagnoseCountTargetId(DiagnoseCountParam param, boolean auto) {
        StringBuilder selectSql = new StringBuilder("select target_id from " + getJdbcHelper().getTable());
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", param.getPuid())
                .in("shop_id", param.getShopIdList().toArray())
                .equalTo("marketplace_id", param.getMarketplaceId())
                .greaterThanOrEqualTo("count_date", DateUtil.format(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN), DateUtil.PATTERN_YYYYMMDD))
                .lessThanOrEqualTo("count_date", DateUtil.format(DateUtil.strToDate(param.getEndDate(), DateUtil.PATTERN), DateUtil.PATTERN_YYYYMMDD));

        if (auto) {
            builder.equalTo("targeting_type", AmazonTargetingReportTypeEnum.AUTO.getTargetingType());
        } else {
            builder.equalTo("targeting_type", AmazonTargetingReportTypeEnum.MANUAL.getTargetingType());
        }
        //广告组ID查询
        if (CollectionUtils.isNotEmpty(param.getAdGroupIdList())) {
            builder.in("ad_group_id", param.getAdGroupIdList().toArray());
        }

        // 限制10万
        builder.limit(Constants.TOTALSIZELIMIT);

        ConditionBuilder build = builder.build();
        selectSql.append(" where ").append(build.getSql());

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(param.getPuid(), hintManager).query(selectSql.toString(), new SingleColumnRowMapper<String>(), build.getValues());
        } finally {
            hintManager.close();
        }
    }

    private StringBuilder subWhereSql(TargetingPageParam param, List<Object> argsList) {
        StringBuilder subWhereSql = new StringBuilder();
        //高级筛选
        if(param.getUseAdvanced()){
            BigDecimal shopSales = param.getShopSales() != null ? param.getShopSales() : BigDecimal.valueOf(0);

            subWhereSql.append(" having 1=1 ");
            //展示量
            if(param.getImpressionsMin() != null){
                subWhereSql.append(" and impressions >= ?");
                argsList.add(param.getImpressionsMin());
            }
            if(param.getImpressionsMax() != null){
                subWhereSql.append(" and impressions <= ?");
                argsList.add(param.getImpressionsMax());
            }
            //点击量
            if(param.getClicksMin() != null){
                subWhereSql.append(" and clicks >= ?");
                argsList.add(param.getClicksMin());
            }
            if(param.getClicksMax() != null){
                subWhereSql.append(" and clicks <= ?");
                argsList.add(param.getClicksMax());
            }
            //点击率（clicks/impressions）
            if(param.getClickRateMin() != null){
                subWhereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(param.getClickRateMin());
            }
            if(param.getClickRateMax() != null){
                subWhereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(param.getClickRateMax());
            }
            //花费
            if(param.getCostMin() != null){
                subWhereSql.append(" and cost >= ?");
                argsList.add(param.getCostMin());
            }
            if(param.getCostMax() != null){
                subWhereSql.append(" and cost <= ?");
                argsList.add(param.getCostMax());
            }
            //cpc  平均点击费用
            if(param.getCpcMin() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(param.getCpcMin());
            }
            if(param.getCpcMax() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(param.getCpcMax());
            }
            //广告订单量
            if(param.getOrderNumMin() != null){
                subWhereSql.append(" and sale_num >= ?");
                argsList.add(param.getOrderNumMin());
            }
            if(param.getOrderNumMax() != null){
                subWhereSql.append(" and sale_num <= ?");
                argsList.add(param.getOrderNumMax());
            }
            //广告销售额
            if(param.getSalesMin() != null){
                subWhereSql.append(" and total_sales >= ?");
                argsList.add(param.getSalesMin());
            }
            if(param.getSalesMax() != null){
                subWhereSql.append(" and total_sales <= ?");
                argsList.add(param.getSalesMax());
            }
            //订单转化率
            if(param.getSalesConversionRateMin() != null){
                subWhereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(param.getSalesConversionRateMin());
            }
            if(param.getSalesConversionRateMax() != null){
                subWhereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(param.getSalesConversionRateMax());
            }
            //acos
            if(param.getAcosMin() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(param.getAcosMin());
            }
            if(param.getAcosMax() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(param.getAcosMax());
            }
            // roas
            if (param.getRoasMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(param.getRoasMin());
            }
            // roas
            if (param.getRoasMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(param.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAcotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAcotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (param.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAsotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (param.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAsotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }

            /**************************高级筛选新增查询指标******************************/
            //cpa
            if (param.getCpaMin() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) >= ?");
                argsList.add(param.getCpaMin());
            }
            if (param.getCpaMax() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) <= ?");
                argsList.add(param.getCpaMax());
            }
            //本广告产品订单量
            if (param.getAdSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(ad_sale_num, 0) >= ?");
                argsList.add(param.getAdSaleNumMin());
            }
            if (param.getAdSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(ad_sale_num, 0) <= ?");
                argsList.add(param.getAdSaleNumMax());
            }
            //其他产品广告订单量
            if (param.getAdOtherOrderNumMin() != null) {
                subWhereSql.append(" and ifnull(sale_num - ad_sale_num, 0) >= ?");
                argsList.add(param.getAdOtherOrderNumMin());
            }
            if (param.getAdOtherOrderNumMax() != null) {
                subWhereSql.append(" and ifnull(sale_num - ad_sale_num, 0) <= ?");
                argsList.add(param.getAdOtherOrderNumMax());
            }
            //本广告产品销售额
            if (param.getAdSalesMin() != null) {
                subWhereSql.append(" and ifnull(ad_sales, 0) >= ?");
                argsList.add(param.getAdSalesMin());
            }
            if (param.getAdSalesMax() != null) {
                subWhereSql.append(" and ifnull(ad_sales, 0) <= ?");
                argsList.add(param.getAdSalesMax());
            }
            //其他产品广告销售额
            if (param.getAdOtherSalesMin() != null) {
                subWhereSql.append(" and ifnull(total_sales - ad_sales, 0) >= ?");
                argsList.add(param.getAdOtherSalesMin());
            }
            if (param.getAdOtherSalesMax() != null) {
                subWhereSql.append(" and ifnull(total_sales - ad_sales, 0) <= ?");
                argsList.add(param.getAdOtherSalesMax());
            }
            //广告销量
            if (param.getAdSalesTotalMin() != null) {
                subWhereSql.append(" and ifnull(sales_num, 0) >= ?");
                argsList.add(param.getAdSalesTotalMin());
            }
            if (param.getAdSalesTotalMax() != null) {
                subWhereSql.append(" and ifnull(sales_num, 0) <= ?");
                argsList.add(param.getAdSalesTotalMax());
            }
            //本广告产品销量
            if (param.getAdSelfSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(ad_order_num, 0) >= ?");
                argsList.add(param.getAdSelfSaleNumMin());
            }
            if (param.getAdSelfSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(ad_order_num, 0) <= ?");
                argsList.add(param.getAdSelfSaleNumMax());
            }
            //其他广告产品销量
            if (param.getAdOtherSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(sales_num - ad_order_num, 0) >= ?");
                argsList.add(param.getAdOtherSaleNumMin());
            }
            if (param.getAdOtherSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(sales_num - ad_order_num, 0) <= ?");
                argsList.add(param.getAdOtherSaleNumMax());
            }

            // 广告笔单价 广告销售额÷广告订单量×100%
            if (param.getAdvertisingUnitPriceMin() != null){
                subWhereSql.append(" and ROUND(ifnull(ad_sales/sale_num, 0), 2) >= ?");
                argsList.add(param.getAdvertisingUnitPriceMin());
            }
            if (param.getAdvertisingUnitPriceMax() != null){
                subWhereSql.append(" and ROUND(ifnull(ad_sales/sale_num, 0), 2) <= ?");
                argsList.add(param.getAdvertisingUnitPriceMax());
            }
            // 搜索结果首页首位IS
            if (param.getTopImpressionShareMin() != null){
                subWhereSql.append(" and max(top_of_search_is) >= ?");
                argsList.add(param.getTopImpressionShareMin());
            }
            if (param.getTopImpressionShareMax() != null){
                subWhereSql.append(" and min(top_of_search_is) <= ?");
                argsList.add(param.getTopImpressionShareMax());
            }
        }
        return subWhereSql;
    }


    @Override
    public List<CpcTargetingReport> getSumReportByTargetIdsGroupByCountDate(int puid, Integer shopId, String startStr, String endStr,String marketplaceId, List<String> targetIds) {
        StringBuilder sql = new StringBuilder("SELECT shop_id,marketplace_id,count_date, puid,  sum(`cost`) cost,sum(`cost_rmb`) cost_rmb,sum(`cost_usd`) cost_usd,sum(`total_sales`) total_sales,")
                .append("sum(`total_sales_rmb`) total_sales_rmb,sum(`total_sales_usd`) total_sales_usd,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`order_num`) order_num, SUM(ad_order_num) ad_order_num,")
                .append("sum(`sale_num`) sale_num,sum(`ad_sales`) ad_sales,sum(`ad_sales_rmb`) ad_sales_rmb, sum(`ad_sales_usd`) ad_sales_usd,sum(`ad_sale_num`) ad_sale_num FROM `t_cpc_targeting_report` where`puid`=? and`shop_id`=?")
                .append(" and marketplace_id = ?   and `count_date`>=? and count_date<=? ");
        List<Object> args = Lists.newArrayList(puid, shopId, marketplaceId, startStr, endStr);
        sql.append(SqlStringUtil.dealInList("target_id", targetIds, args));
        sql.append(" group by count_date ");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), args.toArray(), getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> getTargetIdsByTargetIds(int puid, int shopId, String start, List<String> targetIds) {
        StringBuilder sql = new StringBuilder(" select distinct target_id from `t_cpc_targeting_report` ");
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? and count_date >= ? and impressions > 0 ");
        List<Object> argsList = Lists.newArrayList(puid, shopId, start);
        whereSql.append(SqlStringUtil.dealInList("target_id", targetIds, argsList));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql.append(whereSql).toString(), argsList.toArray(), String.class);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<CpcTargetingReport> getFirstPlaceIsByTargetId(int puid, int shopId, String marketId, String startDate, String endDate, String targetId) {
        StringBuilder sql = new StringBuilder("SELECT count_date, sum(`top_of_search_is`) top_of_search_is")
                .append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN)));
        sql.append(" where `puid`=? and`shop_id`=? and `target_id`=? and `count_date`>= ? and `count_date`<= ? ");
        List<Object> args = Lists.newArrayList(puid, shopId, targetId, DateUtil.dateToStrWithFormat(DateUtil.strToDate(startDate, DateUtil.PATTERN),"yyyyMMdd"), DateUtil.dateToStrWithFormat(DateUtil.strToDate(endDate, DateUtil.PATTERN),"yyyyMMdd"));
        sql.append(" group by count_date ");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(),args.toArray(),getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<CpcTargetingReport> getFirstPlaceIsByTargetId(int puid, List<Integer> shopIdList, String startDate, String endDate, List<String> targetIdList) {
        StringBuilder sql = new StringBuilder("SELECT target_id, max(top_of_search_is) max_top_is, min(top_of_search_is) min_top_is")
                .append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN)));
        sql.append(" where `puid`=? and `count_date`>= ? and `count_date`<= ? ");
        List<Object> args = Lists.newArrayList(puid, DateUtil.dateToStrWithFormat(DateUtil.strToDate(startDate, DateUtil.PATTERN),"yyyyMMdd"), DateUtil.dateToStrWithFormat(DateUtil.strToDate(endDate, DateUtil.PATTERN),"yyyyMMdd"));
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealInList("shop_id",shopIdList,args));
        }
        if (CollectionUtils.isNotEmpty(targetIdList)) {
            sql.append(SqlStringUtil.dealInList("target_id",targetIdList,args));
        }
        sql.append(" group by target_id ");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(),args.toArray(),getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<TargetFirstPlaceIsDto> getFirstPlaceIsDtoByKeywordId(int puid, List<Integer> shopIdList, String startDate, String endDate, List<String> targetIdList) {
        StringBuilder sql = new StringBuilder("SELECT target_id targetId, max(top_of_search_is) maxTopIs, min(top_of_search_is) minTopIs")
                .append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN)));
        sql.append(" where `puid`=? and `count_date`>= ? and `count_date`<= ? ");
        List<Object> args = Lists.newArrayList(puid, DateUtil.dateToStrWithFormat(DateUtil.strToDate(startDate, DateUtil.PATTERN), "yyyyMMdd"), DateUtil.dateToStrWithFormat(DateUtil.strToDate(endDate, DateUtil.PATTERN), "yyyyMMdd"));
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
        }
        if (CollectionUtils.isNotEmpty(targetIdList)) {
            sql.append(SqlStringUtil.dealInList("target_id", targetIdList, args));
        }
        sql.append(" group by target_id ");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), args.toArray(), new BeanPropertyRowMapper<>(TargetFirstPlaceIsDto.class));
        } finally {
            hintManager.close();
        }
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append(" and shop_id = ? ");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<TargetPageVo> getReportDataByTargetIdList(Integer puid, TargetingPageParam param, List<String> TargetIdList) {
        List<Object> argsList = new ArrayList<>();

        StringBuilder sb = new StringBuilder("select target_id targetId, SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost, SUM(sale_num) sale_num, SUM(ad_order_num) ad_order_num, SUM(total_sales) total_sales, SUM(ad_sales) `ad_sales`,SUM(ad_sale_num) `ad_sale_num`, SUM(order_num) order_num, MAX(top_of_search_is) max_top_is, MIN(top_of_search_is) min_top_is")
                .append(" from ").append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD))).append(" r ")
                .append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (CollectionUtils.isNotEmpty(TargetIdList)) {
            sb.append(SqlStringUtil.dealInList("target_id", TargetIdList, argsList));
        }
        sb.append(" and r.count_date >= ? and r.count_date <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());

        sb.append(" group by target_id ");
        if (param.getUseAdvanced()) {
            sb.append(this.getTargetPageHavingSql(param, argsList));
        }

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(TargetPageVo.class));
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdOrderBo> getTargetIdAndIndexList(Integer puid, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select target_id id, ")
                .append(SqlStringReportUtil.getSpOrderField(param.getOrderField())).append(" orderField ")
                .append(" from ").append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));

        sb.append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        sb.append(" and count_date >= ? and count_date <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            sb.append(SqlStringUtil.dealInList("target_id", param.getTargetIds(), argsList));
        }
        sb.append(" group by target_id ");
        if (param.getUseAdvanced()) {
            sb.append(this.getTargetPageHavingSql(param, argsList));
        }
        sb.append(" limit " + Constants.TARGET_PAGE_QUERY_REPORT_ID_LIMIT);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AdOrderBo.class));
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> getTargetIdListByParam(Integer puid, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select target_id id from ")
                .append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));

        sb.append(" where puid = ? and shop_id = ? and count_date >= ? and count_date <= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            sb.append(SqlStringUtil.dealInList("target_id", param.getTargetIds(), argsList));
        }
        sb.append(" group by target_id ");
        if (param.getUseAdvanced()) {
            sb.append(this.getTargetPageHavingSql(param, argsList));
        }
        sb.append(" limit " + Constants.TARGET_PAGE_QUERY_REPORT_ID_LIMIT);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sb.toString(), argsList.toArray(), String.class);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public AdMetricDto getTargetPageSumMetricDataByTargetIdList(Integer puid, TargetingPageParam param, List<String> targetIdList) {
        if (CollectionUtils.isEmpty(targetIdList)) {
            return new AdMetricDto();
        }
        List<Object> argsList = new ArrayList<>();

        StringBuilder sqlSb = new StringBuilder("select SUM(cost) sumCost,SUM(sale_num) sumAdOrderNum,SUM(total_sales) sumAdSale, SUM(order_num) sumOrderNum from ")
                .append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)))
                .append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        sqlSb.append(SqlStringUtil.dealInList("target_id", targetIdList, argsList));
        sqlSb.append(" and count_date >= ? and count_date <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        HintManager hintManager = HintManager.getInstance();
        try {
            List<AdMetricDto> list = getJdbcTemplate(puid, hintManager).query(sqlSb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AdMetricDto.class));
            return list.size() == 1 ? list.get(0) : null;
        } finally {
            hintManager.close();
        }
    }

    /**
     * 投放投放列表页having条件sql拼接
     */
    private String getTargetPageHavingSql(TargetingPageParam param, List<Object> argsList) {
        ReportAdvancedFilterBaseQo qo = new ReportAdvancedFilterBaseQo();
        BeanUtils.copyProperties(param, qo);
        return SqlStringReportUtil.getSpTargetPageHavingSql(qo, argsList);
    }

    @Override
    public List<ReportMonitorBo> getReportLevelMonitorBoList(List<ShopDTO> shopDTOS, AdTypeEnum adType, String startCountDate, String endCountDate) {
        List<ReportMonitorBo> resultBolist = new ArrayList<>();
        String targetingTable;
        //通过广告类型得到对应表名
        switch (adType) {
            case sp: {
                targetingTable = getTableNameByStartDateAndTableName(DateUtil.strToDate(startCountDate, DateUtil.PATTERN_YYYYMMDD), "t_cpc_targeting_report");
            } break;
            case sb: {
                targetingTable = getTableNameByStartDateAndTableName(DateUtil.strToDate(startCountDate, DateUtil.PATTERN_YYYYMMDD), "t_amazon_ad_sb_targeting_report");
            } break;
            case sd: {
                targetingTable = getTableNameByStartDateAndTableName(DateUtil.strToDate(startCountDate, DateUtil.PATTERN_YYYYMMDD), "t_amazon_ad_sd_targeting_report");
            } break;
            default:
                return resultBolist;
        }
        Map<Integer, List<Integer>> puidShopIdsMap = shopDTOS.stream().collect(Collectors.groupingBy(ShopDTO::getPuid, Collectors.mapping(ShopDTO::getShopId, Collectors.toList())));
        String finalTargetingTable = targetingTable;
        puidShopIdsMap.forEach((puid, shopIds) -> {
            if (CollectionUtils.isEmpty(shopIds)) {
                return;
            }
            StringBuilder sql = new StringBuilder();
            sql.append("select sum(cost) total_cost,sum(clicks) total_clicks,sum(impressions) total_impressions, puid, shop_id, 'target' type  from ")
                .append(finalTargetingTable)
                .append(" where puid = ? and count_date between ? and ? ");
            List<Object> args = Lists.newArrayList(puid, startCountDate, endCountDate);
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args))
                .append(" group by shop_id ");
            HintManager hintManager = HintManager.getInstance();
            try {
                List<ReportMonitorBo> reportMonitorBoList = getJdbcTemplate(puid, hintManager).query(sql.toString(), args.toArray(), (row, i) -> ReportMonitorBo.builder()
                    .totalCost(row.getBigDecimal("total_cost"))
                    .totalClicks(row.getLong("total_clicks"))
                    .totalImpressions(row.getLong("total_impressions"))
                    .puid(row.getInt("puid"))
                    .shopId(row.getInt("shop_id"))
                    .type(row.getString("type"))
                    .build());
                resultBolist.addAll(reportMonitorBoList);
            } finally {
                hintManager.close();
            }
        });
        return resultBolist;
    }
}