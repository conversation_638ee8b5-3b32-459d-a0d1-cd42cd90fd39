package com.meiyunji.sponsored.service.cpc.service2.sd.impl;

import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.sd.neTargeting.BatchSdDataResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdNeTargetingDao;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service2.sd.ICpcSdNeTargetingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;

import com.meiyunji.sponsored.service.doris.po.OdsAmazonSdAdNeTargeting;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.MarketplaceTimeZoneEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2021/7/8
 */
@Service
@Slf4j
public class CpcSdNeTargetingServiceImpl implements ICpcSdNeTargetingService {

    @Autowired
    private IAmazonSdAdNeTargetingDao amazonSdAdNeTargetingDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private CpcSdNeTargetingApiService cpcSdNeTargetingApiService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAdManageOperationLogService adOperationLogService;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Autowired
    private IAdManageOperationLogService operationAdGroupLogService;

    @Autowired
    private IDorisService dorisService;
    @Override
    public Result pageList(TargetingPageParam param) {
        Page<AmazonSdAdNeTargeting> page = amazonSdAdNeTargetingDao.pageList(param.getPuid(), param);
        Page<SdNeTargetingPageVo> voPage = new Page<>();
        BeanUtils.copyProperties(page, voPage);

        if (CollectionUtils.isNotEmpty(page.getRows())) {
            String domain = AmznEndpoint.getByMarketplaceId(page.getRows().get(0).getMarketplaceId()).getDomain();

            List<SdNeTargetingPageVo> list = new ArrayList<>(page.getRows().size());
            voPage.setRows(list);
            SdNeTargetingPageVo vo;
            for (AmazonSdAdNeTargeting amazonAdTargeting : page.getRows()) {
                vo = new SdNeTargetingPageVo();
                list.add(vo);
                vo.setShopId(amazonAdTargeting.getShopId());
                vo.setTargetId(amazonAdTargeting.getTargetId());
                vo.setState(amazonAdTargeting.getState());
                vo.setType(amazonAdTargeting.getType());
                vo.setCreateTime(DateUtil.dateToStrWithTime(amazonAdTargeting.getCreateTime(), DateUtil.PATTERN_DATE_TIME));
                vo.setDomain(domain);

                // asin图片
                if (TargetTypeEnum.asin.name().equals(amazonAdTargeting.getType())) {
                    vo.setAsin(amazonAdTargeting.getTargetText());
                    vo.setImgUrl(amazonAdTargeting.getImgUrl());
                    vo.setTitle(amazonAdTargeting.getTitle());
                } else if (TargetTypeEnum.category.name().equals(amazonAdTargeting.getType())) {
                    vo.setCategory(amazonAdTargeting.getTargetText());
                }
            }
        }

        return ResultUtil.returnSucc(voPage);
    }

    @Override
    public Result create(AddSdNeTargetingVo vo,String loginIp) {
        // 校验数据
        if (StringUtils.isNotBlank(checkAddTargetingVo(vo.getNeTargetings()))) {
            return ResultUtil.error("请求参数错误");
        }

        AmazonSdAdGroup amazonAdGroup = amazonSdAdGroupDao.getByGroupId(vo.getPuid(), vo.getShopId(), vo.getGroupId());
        if (amazonAdGroup == null) {
            return ResultUtil.error("没有广告组信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(vo.getShopId(), vo.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(vo.getPuid(), vo.getShopId());
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        List<SdBatchNeTargetingVo> sdNeTargetVoList = Lists.newArrayList();
        List<AmazonSdAdNeTargeting> amazonAdTargetings = new ArrayList<>(vo.getNeTargetings().size());
        for (SdNeTargetingVo sdNeTargetingVo : vo.getNeTargetings()) {
            SdBatchNeTargetingVo sdBatchNeTargetingVo = covertSdNeTargetingVo(sdNeTargetingVo, shop.getId(), shop.getMarketplaceId(),
                    amazonAdGroup.getCampaignId(), amazonAdGroup.getAdGroupId(), profile.getProfileId(), vo.getPuid(), vo.getUid());
            sdNeTargetVoList.add(sdBatchNeTargetingVo);
        }

        Result result = cpcSdNeTargetingApiService.create(shop, profile, sdNeTargetVoList);

        if (!result.success()) {
            return result;
        }
        if (CollectionUtils.isNotEmpty(sdNeTargetVoList)) {
            sdNeTargetVoList.forEach(neTargetingVo -> {
                AmazonSdAdNeTargeting adNeTargeting = new AmazonSdAdNeTargeting();
                BeanUtils.copyProperties(neTargetingVo, adNeTargeting);
                adNeTargeting.setCreateId(vo.getUid());
                amazonAdTargetings.add(adNeTargeting);
            });
        }
        logSdNeTargetingCreate(amazonAdTargetings, loginIp, result);
        List<AmazonSdAdNeTargeting> succList = amazonAdTargetings.stream().filter(e -> StringUtils.isNotBlank(e.getTargetId())).collect(Collectors.toList());
        if (succList.size() > 0) {
            // 有可能已经添加过了: 重复添加同一个否定asin或者品牌，接口不会报错，会返回其对应的targetId
            List<String> existInDB = amazonSdAdNeTargetingDao.listByTargetId(vo.getPuid(), vo.getShopId(), succList.stream()
                    .map(AmazonSdAdNeTargeting::getTargetId).collect(Collectors.toList())).stream().map(AmazonSdAdNeTargeting::getTargetId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getTargetId())).collect(Collectors.toList());
            }

            // 入库
            try {
                amazonSdAdNeTargetingDao.batchAdd(vo.getPuid(), succList);
                List<String> collect = succList.stream().map(AmazonSdAdNeTargeting::getTargetId).collect(Collectors.toList());
                saveDoris(shop.getPuid(), shop.getId(), collect);
            } catch (Exception e) {
                log.error("createSdTargeting:", e);
                return ResultUtil.returnErr("系统异常");
            }
        }

        // 没有失败的就代表全部成功了
        List<AmazonSdAdNeTargeting> failList = amazonAdTargetings.stream().filter(e -> StringUtils.isBlank(e.getTargetId())).collect(Collectors.toList());
        if (failList.size() == 0) {
            return ResultUtil.success();
        }

        Map<String, String> errMap = new HashMap<>();
        failList.forEach((e) -> errMap.put(e.getExpression(), e.getErrMsg()));

        return ResultUtil.returnErr("以下投放创建失败了: " + JSONUtil.objectToJson(errMap));
    }

    @Override
    public NewCreateResultResultVo<SBCommonErrorVo> sdCreateNeTargeting(List<SdNeTargetingVo> neTargetingList, String campaignId,
                                                                        String adGroupId, ShopAuth shop,
                                                                        AmazonAdProfile profile, Integer uid,
                                                                        String loginId) {
        // 校验数据
        String msg = checkAddTargetingVo(neTargetingList);
        if (StringUtils.isNotBlank(msg)) {
            log.error("check param error, msg:{}", msg);
            throw new ServiceException(msg);
        }

        List<SdBatchNeTargetingVo> sdNeTargetVoList = Lists.newArrayList();
        for (SdNeTargetingVo sdNeTargetingVo : neTargetingList) {
            SdBatchNeTargetingVo sdBatchNeTargetingVo = covertSdNeTargetingVo(sdNeTargetingVo, shop.getId(), shop.getMarketplaceId(),
                    campaignId, adGroupId, profile.getProfileId(), shop.getPuid(), uid);
            sdNeTargetVoList.add(sdBatchNeTargetingVo);
        }

        Result result = cpcSdNeTargetingApiService.createNew(shop, profile, adGroupId, sdNeTargetVoList);

        if (!result.success()) {
            NewCreateResultResultVo<SBCommonErrorVo> neTargetingResult = new NewCreateResultResultVo();
            neTargetingResult.setCampaignId(campaignId);
            neTargetingResult.setAdGroupId(adGroupId);
            neTargetingResult.setErrInfoList(Collections.singletonList(SBCommonErrorVo.getErrorVo("否定投放", String.valueOf(result.getData()))));
            return neTargetingResult;
        }

        List<AmazonSdAdNeTargeting> amazonAdNeTargetings = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sdNeTargetVoList)) {
            sdNeTargetVoList.forEach(neTargetingVo -> {
                AmazonSdAdNeTargeting adNeTargeting = new AmazonSdAdNeTargeting();
                BeanUtils.copyProperties(neTargetingVo, adNeTargeting);
                adNeTargeting.setCreateId(uid);
                amazonAdNeTargetings.add(adNeTargeting);
            });
        }
        logSdNeTargetingCreate(amazonAdNeTargetings, loginId, result);
        List<AmazonSdAdNeTargeting> succList = amazonAdNeTargetings.stream().filter(e -> StringUtils.isNotBlank(e.getTargetId())).collect(Collectors.toList());
        if (succList.size() > 0) {
            // 有可能已经添加过了: 重复添加同一个否定asin或者品牌，接口不会报错，会返回其对应的targetId
            List<String> existInDB = amazonSdAdNeTargetingDao.listByTargetId(shop.getPuid(), shop.getId(), succList.stream()
                    .map(AmazonSdAdNeTargeting::getTargetId).collect(Collectors.toList())).stream().map(AmazonSdAdNeTargeting::getTargetId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getTargetId())).collect(Collectors.toList());
            }

            // 入库
            try {
                amazonSdAdNeTargetingDao.batchAdd(shop.getPuid(), succList);
                List<String> collect = succList.stream().map(AmazonSdAdNeTargeting::getTargetId).collect(Collectors.toList());
                saveDoris(shop.getPuid(), shop.getId(), collect);
            } catch (Exception e) {
                log.error("createSdTargeting:", e);
                throw new ServiceException("系统异常");
            }
        }
        NewCreateResultResultVo<SBCommonErrorVo> neTargetResult = NewCreateResultResultVo.<SBCommonErrorVo>builder()
                .campaignId(campaignId)
                .adGroupId(adGroupId)
                .neTargetingIdList(amazonAdNeTargetings.stream().map(neTarget -> {
                    NewCreateNeTargetResultVo vo = new NewCreateNeTargetResultVo();
                    Optional.ofNullable(neTarget.getTargetId()).ifPresent(vo::setNeTargetId);
                    Optional.ofNullable(neTarget.getTargetText()).ifPresent(vo::setNeTargetText);
                    Optional.ofNullable(neTarget.getType()).ifPresent(vo::setNeMatchType);
                    return vo;
                }).collect(Collectors.toList()))
                .build();

        // 没有失败的就代表全部成功了
        List<AmazonSdAdNeTargeting> failList = amazonAdNeTargetings.stream().filter(e -> StringUtils.isBlank(e.getTargetId())).collect(Collectors.toList());
        if (failList.size() == 0) {
            return neTargetResult;
        }

        //返回异常否定投放
        List<SBCommonErrorVo> errorList = new ArrayList<>();
        failList.forEach((e) -> {
            if (org.apache.commons.lang.StringUtils.isNotBlank(e.getErrMsg())) {
                errorList.add(SBCommonErrorVo.getErrorVo(e.getTargetText(), e.getErrMsg()));
            }
        });
        neTargetResult.setErrInfoList(errorList);
        return neTargetResult;
    }

    private void logSdNeTargetingCreate(List<AmazonSdAdNeTargeting> neTargetings, String ip, Result result) {
        try {
            List<AdManageOperationLog> operationLogs = Lists.newArrayListWithExpectedSize(2);
            for (AmazonSdAdNeTargeting sdNeTargeting : neTargetings) {
                AdManageOperationLog operationLog = adOperationLogService.getSdNeTargetsLog(null, sdNeTargeting);
                operationLog.setIp(ip);
                if (StringUtils.isNotBlank(sdNeTargeting.getTargetId())) {
                    operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    String errMsg = "";
                    if (StringUtils.isNotBlank(sdNeTargeting.getErrMsg())) {
                        errMsg = "targetValue:" + sdNeTargeting.getTargetText() + ", desc:" + sdNeTargeting.getErrMsg();
                    } else {
                        errMsg = result.getMsg();
                    }
                    operationLog.setResultInfo(errMsg);
                }
                operationLogs.add(operationLog);
            }
            operationAdGroupLogService.batchLogsMergeByAdGroup(operationLogs);
        } catch (Exception e) {
            log.error("logSdNeTargetingCreate error", e);
        }
    }

    @Override
    public Result archive(Integer puid, Integer shopId, Integer uid, String targetId, String loginIp) {
        AmazonSdAdNeTargeting amazonSdAdTargeting = amazonSdAdNeTargetingDao.getbyTargetId(puid, shopId, targetId);
        if (amazonSdAdTargeting == null) {
            return ResultUtil.returnErr("没有投放定位信息");
        }

        Result result = cpcSdNeTargetingApiService.archive(amazonSdAdTargeting);

        AmazonSdAdNeTargeting old = new AmazonSdAdNeTargeting();
        BeanUtils.copyProperties(amazonSdAdTargeting, old);

        amazonSdAdTargeting.setUpdateId(uid);
        amazonSdAdTargeting.setState(CpcStatusEnum.archived.name());
        AmazonSdAdGroup amazonAdGroup = amazonSdAdGroupDao.getByGroupId(puid, shopId, amazonSdAdTargeting.getAdGroupId());
        if (Objects.nonNull(amazonAdGroup)){
            amazonSdAdTargeting.setCampaignId(amazonAdGroup.getCampaignId());
        }
        logSdNeTargetingUpdate(old, amazonSdAdTargeting, loginIp, result);

        if (result.success()) {
            amazonSdAdNeTargetingDao.updateByIdAndPuid(puid, amazonSdAdTargeting);
            saveDoris(puid, shopId, Lists.newArrayList(amazonSdAdTargeting.getTargetId()));
        }
        return result;
    }

    private void logSdNeTargetingUpdate(AmazonSdAdNeTargeting old, AmazonSdAdNeTargeting amazonSdAdTargeting, String ip, Result result) {
        try {
            AdManageOperationLog operationLog = adOperationLogService.getSdNeTargetsLog(old, amazonSdAdTargeting);
            operationLog.setIp(ip);
            if (result.success()) {
                operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                operationLog.setResultInfo(result.getMsg());
            }
            operationAdGroupLogService.printAdOperationLog(Lists.newArrayList(operationLog));
        } catch (Exception e) {
            log.error("logSdNeTargetingUpdate error", e);
        }
    }

    @Override
    public Result<BatchResponseVo<BatchNeTargetVo, AmazonSdAdNeTargeting>> batchArchive(Integer puid, Integer shopId, Integer uid, List<String> idList, String ip) {
        List<AmazonSdAdNeTargeting> neSdTargetingList = amazonSdAdNeTargetingDao.listByTargetId(puid, shopId, idList);
        if (neSdTargetingList == null) {
            return ResultUtil.returnErr("没有否定投放信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Map<String, AmazonSdAdNeTargeting> amazonSdAdNeTargetingMap = neSdTargetingList.stream().collect(Collectors.toMap(AmazonSdAdNeTargeting::getTargetId, e -> e));
        List<BatchNeTargetVo> errorList = new ArrayList<>();
        List<AmazonSdAdNeTargeting> archiveList = new ArrayList<>();

        List<String> adGroupIds = neSdTargetingList.stream().map(AmazonSdAdNeTargeting::getAdGroupId).distinct().collect(Collectors.toList());
        List<AmazonSdAdGroup> amazonAdGroups = amazonSdAdGroupDao.getByGroupIds(puid, shopId, adGroupIds);
        Map<String, AmazonSdAdGroup> adGroupMap = amazonAdGroups.stream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, e -> e, (v1, v2) -> v1));

        for (String targetId : idList) {
            BatchNeTargetVo vo = new BatchNeTargetVo();
            AmazonSdAdNeTargeting neTarget = amazonSdAdNeTargetingMap.get(targetId);
            vo.setIsFail(false);
            vo.setUid(uid);
            if (neTarget == null) {
                vo.setIsFail(true);
                vo.setFailReason("否定投放信息不存在");
                errorList.add(vo);
                continue;
            }
            AmazonSdAdNeTargeting amazonSdAdNeTarget = new AmazonSdAdNeTargeting();
            BeanUtils.copyProperties(neTarget, amazonSdAdNeTarget);
            convertVoToBatchUpdatePo(amazonSdAdNeTarget, vo);
            AmazonSdAdGroup sdAdGroup = adGroupMap.get(neTarget.getAdGroupId());
            if (Objects.nonNull(sdAdGroup)) {
                amazonSdAdNeTarget.setCampaignId(sdAdGroup.getCampaignId());
            }
            archiveList.add(amazonSdAdNeTarget);
        }

        if (CollectionUtils.isEmpty(archiveList)) {
            BatchResponseVo<BatchNeTargetVo, AmazonSdAdNeTargeting> data = new BatchResponseVo<>();
            data.setErrorList(errorList);
            data.setSuccessNum(0);
            data.setCountNum(idList.size());
            return ResultUtil.success(data);
        }

        Result<BatchResponseVo<BatchNeTargetVo, AmazonSdAdNeTargeting>> result = cpcSdNeTargetingApiService.update(shop, profile, archiveList);

        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayList();
        for (AmazonSdAdNeTargeting neTargeting : archiveList) {
            AmazonSdAdNeTargeting oldNeTargeting = amazonSdAdNeTargetingMap.get(neTargeting.getTargetId());
            AdManageOperationLog adManageOperationLog = adOperationLogService.getSdNeTargetsLog(oldNeTargeting, neTargeting);
            adManageOperationLog.setIp(ip);
            adManageOperationLogs.add(adManageOperationLog);
        }

        if (result.success()) {
            BatchResponseVo<BatchNeTargetVo, AmazonSdAdNeTargeting> data = result.getData();
            List<BatchNeTargetVo> sdUpdateNeTargetVoError = data.getErrorList();
            if (CollectionUtils.isNotEmpty(errorList)) {
                sdUpdateNeTargetVoError.addAll(errorList);
                data.setFailNum(data.getErrorList().size());
                data.setCountNum((data.getErrorList() == null? 0 : data.getErrorList().size())+(data.getSuccessList() == null? 0 : data.getSuccessList().size()));
            }
            List<AmazonSdAdNeTargeting> successList = data.getSuccessList();

            Map<String, AmazonSdAdNeTargeting> successDataMap = successList.stream().collect(Collectors.toMap(AmazonSdAdNeTargeting::getTargetId, e -> e));
            Map<String, BatchNeTargetVo> errorDataMap = sdUpdateNeTargetVoError.stream().collect(Collectors.toMap(BatchNeTargetVo::getTargetId, e -> e));
            for (AdManageOperationLog adManageOperationLog : adManageOperationLogs) {
                if (!StringUtil.isEmptyObject(successDataMap.get(adManageOperationLog.getTargetId()))) {
                    adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                }
                if (!StringUtil.isEmptyObject(errorDataMap.get(adManageOperationLog.getTargetId()))) {
                    adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    adManageOperationLog.setResultInfo(errorDataMap.get(adManageOperationLog.getTargetId()).getFailReason());
                }
            }

            if (CollectionUtils.isNotEmpty(successList)) {
                amazonSdAdNeTargetingDao.batchUpdate(puid, successList);
                List<String> collect = successList.stream().map(AmazonSdAdNeTargeting::getTargetId).collect(Collectors.toList());
                saveDoris(shop.getPuid(), shop.getId(), collect);
                data.getSuccessList().clear();
            }
        } else {
            for (AdManageOperationLog targetsLog : adManageOperationLogs) {
                targetsLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                targetsLog.setResultInfo(result.getMsg());
            }
        }
        adOperationLogService.batchLogsMergeByAdGroup(adManageOperationLogs);
        return result;
    }

    private void convertVoToBatchUpdatePo(AmazonSdAdNeTargeting amazonSdAdNeTargeting, BatchNeTargetVo vo) {
        amazonSdAdNeTargeting.setCreateInAmzup(Constants.UPDATE_IN_AMZUP);
        amazonSdAdNeTargeting.setUpdateId(vo.getUid());
    }

    private String checkAddTargetingVo(List<SdNeTargetingVo> targetings) {
        if (CollectionUtils.isEmpty(targetings)) {
            return "对象不存在";
        }

        for (SdNeTargetingVo vo : targetings) {
            if (!TargetTypeEnum.asin.name().equals(vo.getType()) && !TargetTypeEnum.category.name().equals(vo.getType())) {
                return "cpc.type.not.support";
            }
            if ((TargetTypeEnum.asin.name().equals(vo.getType()) && StringUtils.isBlank(vo.getAsin()))
                    || (TargetTypeEnum.category.name().equals(vo.getType()) && StringUtils.isBlank(vo.getCategoryId()))) {
                return "cpc.none.targeting";
            }
        }
        return null;
    }

    @Override
    public Result<BatchSdDataResponse> batchAddNeTarget(AddSdNeTargetingVo vo) {
        Result<BatchSdDataResponse> data = ResultUtil.success();
        StringBuilder errorMsg = new StringBuilder();
        List<Int32Value> errorList = Lists.newArrayList();
        List<Int32Value> successList;

        List<SdNeTargetingVo> sdNeTargetingVos = vo.getNeTargetings();
        // 校验数据
        if (StringUtils.isNotBlank(checkAddTargetingVo(vo.getNeTargetings()))) {
            return ResultUtil.error("请求参数错误");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(vo.getShopId(), vo.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(vo.getPuid(), vo.getShopId());
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        List<String> adGroupIds = sdNeTargetingVos.stream().filter(item -> StringUtils.isNotBlank(item.getGroupId()))
                .map(item -> item.getGroupId()).collect(Collectors.toList());
        Map<String, AmazonSdAdGroup> adGroupMap = amazonSdAdGroupDao.getByGroupIds(vo.getPuid(), vo.getShopId(), adGroupIds)
                .stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, Function.identity()));
        SdNeTargetingVo next;
        Iterator<SdNeTargetingVo> iterator = sdNeTargetingVos.iterator();
        while (iterator.hasNext()) {
            next = iterator.next();
            if (adGroupMap.get(next.getGroupId()) == null) {
                iterator.remove();
                errorMsg.append("targetValue:" + (StringUtils.isNotBlank(next.getAsin()) ? next.getAsin() : next.getCategory()) + ",desc: 该广告组不存在</br>");
                errorList.add(Int32Value.of(next.getIndex()));
            }
        }
        if (CollectionUtils.isEmpty(sdNeTargetingVos)) {
            if (StringUtils.isNotBlank(errorMsg)) {
                return ResultUtil.returnErr(errorMsg.toString());
            } else {
                return ResultUtil.returnErr("request param error");
            }
        }
        List<SdBatchNeTargetingVo> voList = new ArrayList<>(sdNeTargetingVos.size());
        List<AmazonSdAdNeTargeting> amazonAdTargetings = new ArrayList<>(sdNeTargetingVos.size());
        for (SdNeTargetingVo sdNeTargetingVo : sdNeTargetingVos) {
            AmazonSdAdGroup amazonAdGroup = adGroupMap.get(sdNeTargetingVo.getGroupId());
            SdBatchNeTargetingVo sdBatchNeTargetingVo = covertSdNeTargetingVo(sdNeTargetingVo, shop.getId(), shop.getMarketplaceId(),
                    amazonAdGroup.getCampaignId(), amazonAdGroup.getAdGroupId(),profile.getProfileId(), shop.getPuid(), vo.getUid());
            sdBatchNeTargetingVo.setIndex(sdNeTargetingVo.getIndex());
            voList.add(sdBatchNeTargetingVo);
        }

        Result result = cpcSdNeTargetingApiService.create(shop, profile, voList);
        if (!result.success()) {
            return result;
        }

        if (CollectionUtils.isNotEmpty(voList)) {
            voList.forEach(neTargetingVo -> {
                AmazonSdAdNeTargeting adNeTargeting = new AmazonSdAdNeTargeting();
                BeanUtils.copyProperties(neTargetingVo, adNeTargeting);
                amazonAdTargetings.add(adNeTargeting);
            });
        }
        List<AmazonSdAdNeTargeting> succList = amazonAdTargetings.stream().filter(e -> StringUtils.isNotBlank(e.getTargetId())).collect(Collectors.toList());
        if (succList.size() > 0) {
            // 有可能已经添加过了: 重复添加同一个否定asin或者品牌，接口不会报错，会返回其对应的targetId
            List<String> existInDB = amazonSdAdNeTargetingDao.listByTargetId(vo.getPuid(), vo.getShopId(), succList.stream()
                    .map(AmazonSdAdNeTargeting::getTargetId).collect(Collectors.toList())).stream().map(AmazonSdAdNeTargeting::getTargetId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getTargetId())).collect(Collectors.toList());
            }

            // 入库
            try {
                amazonSdAdNeTargetingDao.batchAdd(vo.getPuid(), succList);
                List<String> collect = succList.stream().map(AmazonSdAdNeTargeting::getTargetId).collect(Collectors.toList());
                saveDoris(shop.getPuid(), shop.getId(), collect);
            } catch (Exception e) {
                log.error("createSdTargeting:", e);
                return ResultUtil.returnErr("系统异常");
            }
        }

        // 没有失败的就代表全部成功了
        List<SdBatchNeTargetingVo> failList = voList.stream().filter(e -> StringUtils.isBlank(e.getTargetId())).collect(Collectors.toList());

        String errMsg = failList.stream().filter(item -> StringUtils.isNotBlank(item.getErrMsg())).map(item ->
                        "targetValue:" + item.getTargetText() + ",desc: " + item.getErrMsg() + "</br>")
                .collect(Collectors.joining(";"));
        if (StringUtils.isNotBlank(errMsg)) {
            errorMsg.append(errMsg);
        }
        List<Int32Value> errList = failList.stream().filter(item -> StringUtils.isBlank(item.getTargetId()))
                .map(item -> Int32Value.of(item.getIndex())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(errList)) {
            errorList.addAll(errList);
        }
        successList = voList.stream().filter(item -> StringUtils.isNotBlank(item.getTargetId()))
                .map(item -> Int32Value.of(item.getIndex())).collect(Collectors.toList());
        data.setData(BatchSdDataResponse.newBuilder()
                .setFailMsg(errorMsg.toString())
                .addAllFail(errorList)
                .addAllSuccess(successList).build());

        return data;
    }

    private SdBatchNeTargetingVo covertSdNeTargetingVo(SdNeTargetingVo sourceVo, Integer shopId,
                                                       String marketplaceId, String campaignId,
                                                       String adGroupId, String profileId,
                                                       Integer puid, Integer uid) {
        SdBatchNeTargetingVo targetVo = new SdBatchNeTargetingVo();
        targetVo.setPuid(puid);
        targetVo.setShopId(shopId);
        targetVo.setMarketplaceId(marketplaceId);
        targetVo.setAdGroupId(adGroupId);
        targetVo.setProfileId(profileId);
        targetVo.setCampaignId(campaignId);
        targetVo.setExpressionType(Constants.MANUAL);
        targetVo.setState(CpcStatusEnum.enabled.name());
        targetVo.setType(sourceVo.getType());
        targetVo.setUid(uid);
        targetVo.setCreateInAmzup(1);
        if (TargetTypeEnum.asin.name().equals(sourceVo.getType())) {
            targetVo.setTargetText(sourceVo.getAsin());
            targetVo.setTitle(sourceVo.getTitle());
            targetVo.setImgUrl(sourceVo.getImgUrl());
        } else {
            targetVo.setTargetText(sourceVo.getCategory());
        }
        List<Expression> expressions = new ArrayList<>(1);
        Expression expression = new Expression();
        if (TargetTypeEnum.asin.name().equals(sourceVo.getType())) {
            expression.setType(ExpressionEnum.asinSameAs.value());
            expression.setValue(sourceVo.getAsin());
        } else {
            expression.setType(ExpressionEnum.asinCategorySameAs.value());
            expression.setValue(sourceVo.getCategoryId());
        }
        expressions.add(expression);
        targetVo.setExpression(JSONUtil.objectToJson(expressions));
        return targetVo;
    }

    /**
     * 写入doris
     * @param targetingList
     */
    @Override
    public void saveDoris(List<AmazonSdAdNeTargeting> targetingList) {
        try {
            List<OdsAmazonSdAdNeTargeting> collect = targetingList.stream().map(x -> {
                OdsAmazonSdAdNeTargeting po = new OdsAmazonSdAdNeTargeting();
                BeanUtils.copyProperties(x, po);

                MarketplaceTimeZoneEnum marketplaceTimeZoneEnum = MarketplaceTimeZoneEnum.map.get(x.getMarketplaceId());
                if (po.getCreationDate() == null && po.getCreateTime() != null) {
                    po.setCreationDate(x.getCreateTime());
                    po.setAmazonCreateTime(LocalDateTimeUtil.convertDateToLDT(x.getCreateTime(), TimeZone.getTimeZone(marketplaceTimeZoneEnum.getZone_id()).toZoneId()));
                }
                if (po.getCreationDate() != null) {
                    po.setAmazonCreateTime(LocalDateTimeUtil.convertDateToLDT(po.getCreationDate(), TimeZone.getTimeZone(marketplaceTimeZoneEnum.getZone_id()).toZoneId()));
                    po.setCreationAfterDate(po.getAmazonCreateTime().plusDays(30L).toLocalDate());
                    po.setCreationBeforeDate(po.getAmazonCreateTime().plusDays(-30L).toLocalDate());
                }
                if (StringUtils.isNotBlank(po.getState())) {
                    po.setState(po.getState().toLowerCase());
                }
                return po;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                dorisService.saveDoris(collect);
            }
        } catch (Exception e) {
            log.error("sb ne targeting save doris error", e);
        }
    }

    /**
     * 写入doris
     * @param puid
     * @param shopId
     * @param targetIdList
     */
    @Override
    public void saveDoris(Integer puid, Integer shopId, List<String> targetIdList) {
        try {
            if (CollectionUtils.isNotEmpty(targetIdList)) {
                List<AmazonSdAdNeTargeting> amazonSdAdNeTargetings = amazonSdAdNeTargetingDao.listByTargetId(puid, shopId, targetIdList);
                saveDoris(amazonSdAdNeTargetings);
            }
        } catch (Exception e) {
            log.error("sd ne targeting save doris error", e);
        }
    }
}