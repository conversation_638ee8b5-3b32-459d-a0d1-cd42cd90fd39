package com.meiyunji.sponsored.service.cpc.service2.impl.targettask;

import com.amazon.advertising.mode.StateEnum;
import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.amazon.advertising.spV3.campaign.CampaignNegativeTargetSpV3Client;
import com.amazon.advertising.spV3.campaign.CreateSpCampaignNegativeTargetV3Response;
import com.amazon.advertising.spV3.campaign.entity.CampaignNegativeTargetApiResponseV3;
import com.amazon.advertising.spV3.campaign.entity.CampaignNegativeTargetSuccessResultV3;
import com.amazon.advertising.spV3.campaign.entity.CreateCampaignNegativeTargetEntityV3;
import com.amazon.advertising.spV3.negativetargeting.CreateSpNegativeTargetV3Response;
import com.amazon.advertising.spV3.negativetargeting.NegativeTargetSpV3Client;
import com.amazon.advertising.spV3.negativetargeting.entity.CreateNegativeTargetEntityV3;
import com.amazon.advertising.spV3.negativetargeting.entity.NegativeTargetApiResponseV3;
import com.amazon.advertising.spV3.negativetargeting.entity.NegativeTargetSuccessResultV3;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.constants.*;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignNetargetingSpDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdNeTargetingDao;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetDetailDto;
import com.meiyunji.sponsored.service.cpc.dto.CommonAmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.ICommonAmazonAdTargetingService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcSpCampaignService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcTargetingService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCampaignNeTargetingApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcTargetingApiService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.SpNeTargetingVo;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-05-08 19:21
 */
@Service(AdTargetTaskConstant.SP_NE_TARGET_HANDLER)
@Slf4j
public class SpNeTargetHandler implements TargetTaskHandler {

    @Autowired
    private TargetTaskComponent targetTaskComponent;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private CpcTargetingApiService cpcTargetingApiService;
    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Autowired
    private ICommonAmazonAdTargetingService commonAmazonAdTargetingService;
    @Autowired
    private ICpcTargetingService cpcTargetingService;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private CpcCampaignNeTargetingApiService cpcCampaignNeTargetingApiService;
    @Autowired
    private IAmazonAdNeTargetingDao amazonAdNeTargetingDao;
    @Autowired
    private IAmazonAdCampaignNetargetingSpDao campaignNetargetingSpDao;
    @Autowired
    private ICpcSpCampaignService cpcSpCampaignService;

    @Override
    public void handle(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        //处理业务返回结果
        createTargeting(adTargetTask, adTargetTaskDetails);
    }

    public void createTargeting(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        List<AdTargetTaskDetail> needUpdateDetailList = new ArrayList<>();
        if (CollectionUtils.isEmpty(adTargetTaskDetails)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        // 排除已存在的关键词
        Set<String> adCampaignId = new HashSet<>();
        Set<String> adGroupIdSet = new HashSet<>();
        Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap = new HashMap<>();
        for (AdTargetTaskDetail targetTaskDetail : adTargetTaskDetails) {
            adCampaignId.add(targetTaskDetail.getAdCampaignId());
            Optional.ofNullable(targetTaskDetail.getAdGroupId()).filter(StringUtils::isNotBlank).ifPresent(adGroupIdSet::add);
            adTargetTaskDetailMap.put(targetTaskDetail.getId(), targetTaskDetail);
        }

        List<SpNeTargetingVo> amazonAdTargetings = new ArrayList<>();
        int originalTaskDetailSize;
        if (CollectionUtils.isNotEmpty(adGroupIdSet)) {
            List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.getAdGroupByIds(adTargetTask.getPuid(), adTargetTask.getShopId(), new ArrayList<>(adGroupIdSet));
            Map<String, AmazonAdGroup> amazonAdGroupMap = amazonAdGroups.stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity(), (newVal, oldVal) -> newVal));
            amazonAdTargetings = convertAddNeTargetingVoToPO(adTargetTask.getUid(), amazonAdGroupMap,
                    adTargetTaskDetails, needUpdateDetailList, adTargetTask.getTargetingType());

        } else if (CollectionUtils.isNotEmpty(adCampaignId)) {
            List<AmazonAdCampaignAll> campaignList = amazonAdCampaignAllDao.listByCampaignIdNoType(adTargetTask.getPuid(), adTargetTask.getShopId(), new ArrayList<>(adCampaignId));
            Map<String, AmazonAdCampaignAll> campaginMap = campaignList.parallelStream()
                    .collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (newVal, oldVal) -> newVal));
            amazonAdTargetings = convertNeTargetingWithCampaign(adTargetTask.getUid(), campaginMap,
                    adTargetTaskDetails, needUpdateDetailList, adTargetTask.getTargetingType());
        }
        originalTaskDetailSize = adTargetTaskDetails.size();

        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(adTargetTask.getShopId(), adTargetTask.getPuid());
        if (shop == null) {
            adTargetTaskDetails.forEach(each -> {
                each.setFailureReason("店铺不存在");
                each.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                needUpdateDetailList.add(each);
            });
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        int failureNum = 0;
        Result result;
        List<List<SpNeTargetingVo>> amazonAdTargetingPartition = Lists.partition(amazonAdTargetings, AdTargetTaskConstant.MAX_SP_TARGET_SIZE);
        for (List<SpNeTargetingVo> spNeTargetingList : amazonAdTargetingPartition) {
            if (CollectionUtils.isNotEmpty(spNeTargetingList) && StringUtils.isNotEmpty(spNeTargetingList.get(0).getAdGroupId())) {
                 result = createNeTargetV3(spNeTargetingList, adTargetTaskDetailMap, shop);
            } else {
                result = createCampaignNeTargetV3(spNeTargetingList, adTargetTaskDetailMap, shop);
            }

            List<AmazonAdTargeting> amazonAdTargetingList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(spNeTargetingList)) {
                for (SpNeTargetingVo spNeTargeting : spNeTargetingList) {
                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(spNeTargeting.getTargetTaskDetailId());
                    needUpdateDetailList.add(adTargetTaskDetail);
                    AmazonAdTargeting amazonAdTargeting = new AmazonAdTargeting();
                    BeanUtils.copyProperties(spNeTargeting, amazonAdTargeting);
                    amazonAdTargeting.setCreationDate(LocalDateTime.now());
                    amazonAdTargetingList.add(amazonAdTargeting);
                    if (AdTargetTaskStatusEnum.FAILURE.getCode() == adTargetTaskDetail.getStatus()) {
                        failureNum++;
                    }
                }
            }

            List<AdManageOperationLog> operationLogs = Lists.newArrayListWithExpectedSize(2);
            for (AmazonAdTargeting targeting : amazonAdTargetingList) {
                AdManageOperationLog operationLog = adManageOperationLogService.getTargetsLog(null, targeting);
                operationLog.setIp(adTargetTask.getLoginIp());
                if (StringUtils.isNotBlank(targeting.getTargetId())) {
                    operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    String errMsg;
                    if (StringUtils.isNotBlank(targeting.getError())) {
                        errMsg = "targetValue:" + targeting.getTargetingValue() + ", desc:" + targeting.getError();
                    } else {
                        errMsg = result.getMsg();
                    }
                    operationLog.setResultInfo(errMsg);
                }
                operationLogs.add(operationLog);
            }
            adManageOperationLogService.batchLogsMergeByAdGroup(operationLogs);
            if (result.success()) {
                amazonAdTargetingList = amazonAdTargetingList.stream().filter(e -> StringUtils.isNotBlank(e.getTargetId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(spNeTargetingList) && StringUtils.isNotEmpty(spNeTargetingList.get(0).getAdGroupId())) {
                    List<AmazonAdNeTargeting> nes = amazonAdTargetingList.stream().map(e -> {
                        AmazonAdNeTargeting ne = amazonAdTargetDaoRoutingService.converToNe(e);
                        return ne;
                    }).collect(Collectors.toList());
                    amazonAdNeTargetingDao.insertOnDuplicateKeyUpdate(adTargetTask.getPuid(), nes);
                    AmazonAdTargeting amazonAdTargeting = amazonAdTargetingList.get(0);
                    List<String> targetIds = amazonAdTargetingList.stream().map(AmazonAdTargeting::getTargetId).collect(Collectors.toList());
                    cpcTargetingService.saveDorisNe(amazonAdTargeting.getPuid(), amazonAdTargeting.getShopId(), targetIds);
                } else {
                    List<AmazonAdCampaignNetargetingSp> list = convertBatchAddNeTargetVoToPo(amazonAdTargetingList, shop);
                    campaignNetargetingSpDao.batchSave(adTargetTask.getPuid(), list);
                    List<String> collect = list.stream().map(AmazonAdCampaignNetargetingSp::getTargetId).collect(Collectors.toList());
                    cpcSpCampaignService.saveCampaignNetargetDoris(adTargetTask.getPuid(), shop.getId(), collect);
                }

            }
            targetTaskComponent.updateTaskDetailStatus(adTargetTask, needUpdateDetailList);
            needUpdateDetailList.clear();
        }

        failureNum += originalTaskDetailSize - amazonAdTargetings.size();
        int adTargetTaskStatus = targetTaskComponent.getAdTargetTaskStatus(originalTaskDetailSize, failureNum);
        targetTaskComponent.updateTaskStatus(adTargetTask, adTargetTaskStatus);
    }

    private Result createNeTargetV3(List<SpNeTargetingVo> targetings, Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap, ShopAuth shop) {
        SpNeTargetingVo one = targetings.get(0);
        List<CreateNegativeTargetEntityV3> targetingList = cpcTargetingApiService.makeCreateNegativeTargetingClausesV3(targetings);
        CreateSpNegativeTargetV3Response response = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createNegativeTargets(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), targetingList, Boolean.TRUE);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createNegativeTargets(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), targetingList, Boolean.TRUE);
        }
        if (response == null) {
            for (SpNeTargetingVo amazonAdTargeting : targetings) {
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
                adTargetTaskDetail.setFailureReason("网络延迟，请稍后重试");
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
            }
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        if (response.getData() != null) {
            boolean hasSucc = false;
            StringBuilder error = new StringBuilder();
            NegativeTargetApiResponseV3 data = response.getData();
            if (CollectionUtils.isNotEmpty(data.getNegativeTargetingClauses().getSuccess())) {
                for (NegativeTargetSuccessResultV3 successResultV3 : data.getNegativeTargetingClauses().getSuccess()) {
                    SpNeTargetingVo amazonAdKeyword = targetings.get(successResultV3.getIndex());
                    amazonAdKeyword.setTargetId(successResultV3.getTargetId());
                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.SUCCESS.getCode());
                }

                hasSucc = true;
            }

            if (CollectionUtils.isNotEmpty(data.getNegativeTargetingClauses().getError())) {
                for (ErrorItemResultV3 errorItemResultV3 : data.getNegativeTargetingClauses().getError()) {
                    String errorMsg = AmazonErrorUtils.getError(errorItemResultV3.getErrors().get(0).getErrorMessage());

                    error.append("targetValue:").append(targetings.get(errorItemResultV3.getIndex()).getTargetingValue()).append(",desc:").append(errorMsg).append(";");

                    SpNeTargetingVo amazonAdKeyword = targetings.get(errorItemResultV3.getIndex());
                    amazonAdKeyword.setError(AmazonErrorUtils.getError(errorMsg));

                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
                    adTargetTaskDetail.setFailureReason(targetTaskComponent.getError(errorMsg, errorItemResultV3.getErrors().get(0).getErrorMessage()));
                    adTargetTaskDetail.setFailureReasonDetail(errorItemResultV3.getErrors().get(0).getErrorMessage());
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                }
            }
            if (hasSucc) {
                return ResultUtil.success(error.toString());
            }

            return ResultUtil.error(error.toString());
        }

        String msg = "网络延迟，请稍后重试";
        String formatMsg = msg;
        if (response.getError() != null) {
            msg = AmazonErrorUtils.getError(response.getError().getMessage());
            formatMsg = targetTaskComponent.getError(msg, response.getError().getMessage());
        }
        for (SpNeTargetingVo amazonAdTargeting : targetings) {
            AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
            adTargetTaskDetail.setFailureReason(formatMsg);
            adTargetTaskDetail.setFailureReasonDetail(msg);
            adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
        }
        return ResultUtil.error(msg);
    }

    private Result createCampaignNeTargetV3(List<SpNeTargetingVo> targetings, Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap, ShopAuth shop) {
        SpNeTargetingVo one = targetings.get(0);
        List<CreateCampaignNegativeTargetEntityV3> targetingList = cpcCampaignNeTargetingApiService.makeCampaignNegativeKeywordList(targetings);
        CreateSpCampaignNegativeTargetV3Response response = CampaignNegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createCampaignNegativeTargets(shopAuthService.getAdToken(shop)
                , one.getProfileId(), shop.getMarketplaceId(), targetingList,true);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = CampaignNegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createCampaignNegativeTargets(shopAuthService.getAdToken(shop)
                    , one.getProfileId(), shop.getMarketplaceId(), targetingList,true);
        }
        if (response == null) {
            for (SpNeTargetingVo amazonAdTargeting : targetings) {
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
                adTargetTaskDetail.setFailureReason("网络延迟，请稍后重试");
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
            }
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        if (response.getData() != null) {
            boolean hasSucc = false;
            StringBuilder error = new StringBuilder();
            CampaignNegativeTargetApiResponseV3 data = response.getData();
            if (CollectionUtils.isNotEmpty(data.getCampaignNegativeTargetingClauses().getSuccess())) {
                for (CampaignNegativeTargetSuccessResultV3 successResultV3 : data.getCampaignNegativeTargetingClauses().getSuccess()) {
                    SpNeTargetingVo amazonTargeting = targetings.get(successResultV3.getIndex());
                    amazonTargeting.setTargetId(successResultV3.getCampaignNegativeTargetingClauseId());
                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonTargeting.getTargetTaskDetailId());
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.SUCCESS.getCode());
                }

                hasSucc = true;
            }

            if (CollectionUtils.isNotEmpty(data.getCampaignNegativeTargetingClauses().getError())) {
                for (ErrorItemResultV3 errorItemResultV3 : data.getCampaignNegativeTargetingClauses().getError()) {
                    String errorMsg = AmazonErrorUtils.getError(errorItemResultV3.getErrors().get(0).getErrorMessage());
                    error.append("targetValue:").append(targetings.get(errorItemResultV3.getIndex()).getTargetingValue()).append(",desc:").append(errorMsg).append(";");
                    SpNeTargetingVo amazonAdKeyword = targetings.get(errorItemResultV3.getIndex());
                    amazonAdKeyword.setError(AmazonErrorUtils.getError(errorMsg));
                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
                    adTargetTaskDetail.setFailureReason(targetTaskComponent.getError(errorMsg, errorItemResultV3.getErrors().get(0).getErrorMessage()));
                    adTargetTaskDetail.setFailureReasonDetail(errorItemResultV3.getErrors().get(0).getErrorMessage());
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                }
            }
            if (hasSucc) {
                return ResultUtil.success(error.toString());
            }

            return ResultUtil.error(error.toString());
        }

        String msg = "网络延迟，请稍后重试";
        String formatMsg = msg;
        if (response.getError() != null) {
            msg = AmazonErrorUtils.getError(response.getError().getMessage());
            formatMsg = targetTaskComponent.getError(msg, response.getError().getMessage());
        }
        for (SpNeTargetingVo amazonAdTargeting : targetings) {
            AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
            adTargetTaskDetail.setFailureReason(formatMsg);
            adTargetTaskDetail.setFailureReasonDetail(msg);
            adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
        }
        return ResultUtil.error(msg);
    }

    private List<SpNeTargetingVo> convertAddNeTargetingVoToPO(Integer uid, Map<String, AmazonAdGroup> amazonAdGroupMap, List<AdTargetTaskDetail> adTargetTaskDetails, List<AdTargetTaskDetail> needUpdateDetailList, String targetingType) {
        List<SpNeTargetingVo> amazonAdTargetings = new ArrayList<>(adTargetTaskDetails.size());
        SpNeTargetingVo amazonAdTargetingVo;
        Iterator<AdTargetTaskDetail> it = adTargetTaskDetails.iterator();
        AdTargetTaskDetail adTargetTaskDetail;
        List<String> targetIds = adTargetTaskDetails.stream().map(AdTargetTaskDetail::getTargetId).distinct().collect(Collectors.toList());
        AdTargetTaskDetail first = adTargetTaskDetails.get(0);
        List<Integer> sourceShopIds = adTargetTaskDetails.stream().map(AdTargetTaskDetail::getSourceShopId).distinct().collect(Collectors.toList());
        Map<String, AdTargetDetailDto> targetDetailMap = buildTargetDetailMap(first.getPuid(), targetIds, sourceShopIds, targetingType);
        while (it.hasNext()) {
            adTargetTaskDetail = it.next();
            AmazonAdGroup amazonAdGroup = amazonAdGroupMap.get(adTargetTaskDetail.getAdGroupId());
            if (amazonAdGroup == null) {
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                adTargetTaskDetail.setFailureReason("广告组不存在");
                needUpdateDetailList.add(adTargetTaskDetail);
                it.remove();
            } else {
                amazonAdTargetingVo = new SpNeTargetingVo();
                amazonAdTargetingVo.setPuid(amazonAdGroup.getPuid());
                amazonAdTargetingVo.setShopId(amazonAdGroup.getShopId());
                amazonAdTargetingVo.setMarketplaceId(amazonAdGroup.getMarketplaceId());
                amazonAdTargetingVo.setAdGroupId(amazonAdGroup.getAdGroupId());
                amazonAdTargetingVo.setDxmGroupId(amazonAdGroup.getId());
                amazonAdTargetingVo.setCampaignId(amazonAdGroup.getCampaignId());
                amazonAdTargetingVo.setProfileId(amazonAdGroup.getProfileId());
                amazonAdTargetingVo.setState(CpcStatusEnum.enabled.name());
                amazonAdTargetingVo.setType(Constants.TARGETING_TYPE_NEGATIVEASIN);
                amazonAdTargetingVo.setCreateId(uid);
                amazonAdTargetingVo.setTargetTaskDetailId(adTargetTaskDetail.getId());

                List<Expression> expressions = new ArrayList<>();
                Expression expression = new Expression();
                String type = AdTargetObjectTypeEnum.getTargetTypeByCode(adTargetTaskDetail.getTargetObjectType());
                if (TargetTypeEnum.asin.name().equals(type)) {
                    expression.setType(ExpressionEnum.asinSameAs.value());
                    expression.setValue(adTargetTaskDetail.getTargetObject());
                    amazonAdTargetingVo.setTargetingValue(adTargetTaskDetail.getTargetObject());
                    if (StringUtils.isBlank(adTargetTaskDetail.getTargetId())) {
                        amazonAdTargetingVo.setTitle(adTargetTaskDetail.getTargetObjectDesc());
                        amazonAdTargetingVo.setImgUrl(adTargetTaskDetail.getImgUrl());
                    } else {
                        AdTargetDetailDto adTargetDetail = targetDetailMap.get(adTargetTaskDetail.getTargetId());
                        amazonAdTargetingVo.setTitle(adTargetDetail.getTargetObjectDesc());
                        amazonAdTargetingVo.setImgUrl(adTargetDetail.getImgUrl());
                    }
                } else if (TargetTypeEnum.brand.name().equals(type)) {
                    // 品牌否定投放任务肯定包含targetId
                    AdTargetDetailDto adTargetDetail = targetDetailMap.get(adTargetTaskDetail.getTargetId());
                    expression.setType(ExpressionEnum.asinBrandSameAs.value());
                    expression.setValue(adTargetDetail.getBrandId());
                    amazonAdTargetingVo.setTargetingValue(adTargetDetail.getBrandName());
                }
                expressions.add(expression);
                amazonAdTargetingVo.setExpression(JSONUtil.objectToJson(expressions));
                amazonAdTargetings.add(amazonAdTargetingVo);
            }
        }
        return amazonAdTargetings;
    }

    private List<SpNeTargetingVo> convertNeTargetingWithCampaign(Integer uid, Map<String, AmazonAdCampaignAll> campaignMap, List<AdTargetTaskDetail> adTargetTaskDetails, List<AdTargetTaskDetail> needUpdateDetailList, String targetingType) {
        List<SpNeTargetingVo> amazonAdTargetings = new ArrayList<>(adTargetTaskDetails.size());
        SpNeTargetingVo amazonAdTargetingVo;
        Iterator<AdTargetTaskDetail> it = adTargetTaskDetails.iterator();
        AdTargetTaskDetail adTargetTaskDetail;
        List<String> targetIds = adTargetTaskDetails.stream().map(AdTargetTaskDetail::getTargetId).distinct().collect(Collectors.toList());
        AdTargetTaskDetail first = adTargetTaskDetails.get(0);
        List<Integer> sourceShopIds = adTargetTaskDetails.stream().map(AdTargetTaskDetail::getSourceShopId).distinct().collect(Collectors.toList());
        Map<String, AdTargetDetailDto> targetDetailMap = buildTargetDetailMap(first.getPuid(), targetIds, sourceShopIds, targetingType);
        while (it.hasNext()) {
            adTargetTaskDetail = it.next();
            AmazonAdCampaignAll campaign = campaignMap.get(adTargetTaskDetail.getAdCampaignId());
            if (campaign == null) {
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                adTargetTaskDetail.setFailureReason("广告活动不存在");
                needUpdateDetailList.add(adTargetTaskDetail);
                it.remove();
            } else {
                amazonAdTargetingVo = new SpNeTargetingVo();
                amazonAdTargetingVo.setPuid(campaign.getPuid());
                amazonAdTargetingVo.setShopId(campaign.getShopId());
                amazonAdTargetingVo.setMarketplaceId(campaign.getMarketplaceId());
                amazonAdTargetingVo.setCampaignId(campaign.getCampaignId());
                amazonAdTargetingVo.setProfileId(campaign.getProfileId());
                amazonAdTargetingVo.setState(CpcStatusEnum.enabled.name());
                amazonAdTargetingVo.setType(Constants.TARGETING_TYPE_NEGATIVEASIN);
                amazonAdTargetingVo.setCreateId(uid);
                amazonAdTargetingVo.setTargetTaskDetailId(adTargetTaskDetail.getId());
                amazonAdTargetingVo.setAsin(adTargetTaskDetail.getTargetObject());
                List<Expression> expressions = new ArrayList<>();
                Expression expression = new Expression();
                String type = AdTargetObjectTypeEnum.getTargetTypeByCode(adTargetTaskDetail.getTargetObjectType());
                if (TargetTypeEnum.asin.name().equals(type)) {
                    expression.setType(ExpressionEnum.asinSameAs.value());
                    expression.setValue(adTargetTaskDetail.getTargetObject());
                    amazonAdTargetingVo.setTargetingValue(adTargetTaskDetail.getTargetObject());
                    if (StringUtils.isBlank(adTargetTaskDetail.getTargetId())) {
                        amazonAdTargetingVo.setTitle(adTargetTaskDetail.getTargetObjectDesc());
                        amazonAdTargetingVo.setImgUrl(adTargetTaskDetail.getImgUrl());
                    } else {
                        AdTargetDetailDto adTargetDetail = targetDetailMap.get(adTargetTaskDetail.getTargetId());
                        amazonAdTargetingVo.setTitle(adTargetDetail.getTargetObjectDesc());
                        amazonAdTargetingVo.setImgUrl(adTargetDetail.getImgUrl());
                    }
                } else if (TargetTypeEnum.brand.name().equals(type)) {
                    // 品牌否定投放任务肯定包含targetId
                    AdTargetDetailDto adTargetDetail = targetDetailMap.get(adTargetTaskDetail.getTargetId());
                    expression.setType(ExpressionEnum.asinBrandSameAs.value());
                    expression.setValue(adTargetDetail.getBrandId());
                    amazonAdTargetingVo.setTargetingValue(adTargetDetail.getBrandName());
                }
                expressions.add(expression);
                amazonAdTargetingVo.setExpression(JSONUtil.objectToJson(expressions));
                amazonAdTargetings.add(amazonAdTargetingVo);
            }
        }
        return amazonAdTargetings;
    }

    @Override
    public Map<String, AdTargetDetailDto> buildTargetDetailMap(Integer puid, List<String> targetIds, List<Integer> sourceShopIds, String targetingType) {
        List<CommonAmazonAdTargeting> list = commonAmazonAdTargetingService.listByTargetIds(targetingType, puid, sourceShopIds, targetIds);
        return list.stream().map(each -> {
            AdTargetDetailDto adTargetDetail = new AdTargetDetailDto();
            adTargetDetail.setTargetId(each.getTargetId());
            boolean isSp = TargetingTypeEnum.SP_TARGETING_CODE_LIST.contains(targetingType);
            String type = each.getType();
            if (TargetTypeEnum.asin.name().equals(type)) {
                adTargetDetail.setTargetObject(each.getTargetingValue());
                adTargetDetail.setTargetObjectDesc(each.getTitle());
                adTargetDetail.setImgUrl(each.getImgUrl());
            } else if (TargetTypeEnum.brand.name().equals(type)) {
                targetTaskComponent.fillNeTargetDetail(each, adTargetDetail, isSp);
            }
            return adTargetDetail;
        }).collect(Collectors.toMap(AdTargetDetailDto::getTargetId, Function.identity(), (newVal, oldVal) -> newVal));
    }

    private List<AmazonAdCampaignNetargetingSp> convertBatchAddNeTargetVoToPo(List<AmazonAdTargeting> amazonAdTargetingList, ShopAuth shopAuth) {
        List<AmazonAdCampaignNetargetingSp> amazonAdTargets = new ArrayList<>(amazonAdTargetingList.size());
        amazonAdTargetingList.forEach( re->{
            AmazonAdCampaignNetargetingSp campaignNetargetingSp = AmazonAdCampaignNetargetingSp.builder()
                    .createId(re.getCreateId())
                    .campaignId(re.getCampaignId())
                    .state(StateEnum.ENABLED.value())
                    .createState(StateEnum.ENABLED.value().equals(re.getState())? "SUCCESS":"")
                    .createInAmzup(1)
                    .targetText(re.getTargetingValue())
                    .targetId(re.getTargetId())
                    .title(re.getTitle())
                    .imgUrl(re.getImgUrl())
                    .puid(shopAuth.getPuid())
                    .shopId(shopAuth.getId())
                    .marketplaceId(shopAuth.getMarketplaceId())
                    .expression(re.getExpression())
                    .resolvedExpression(re.getExpression())
                    .type("asin").build();
            amazonAdTargets.add(campaignNetargetingSp);
        });
        return amazonAdTargets;
    }
}
