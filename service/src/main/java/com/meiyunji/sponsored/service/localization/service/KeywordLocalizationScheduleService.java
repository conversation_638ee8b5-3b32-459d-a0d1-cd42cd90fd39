package com.meiyunji.sponsored.service.localization.service;

import com.amazon.advertising.localization.keyword.KeywordsLocalizationClient;
import com.amazon.advertising.localization.keyword.KeywordsLocalizationResponse;
import com.amazon.advertising.localization.keyword.SourceDetail;
import com.amazon.advertising.localization.keyword.TargetDetail;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.ICpcQueryKeywordReportDao;
import com.meiyunji.sponsored.service.cpc.dao.ICpcQueryTargetingReportDao;
import com.meiyunji.sponsored.service.cpc.dao.ICpcSbQueryKeywordReportDao;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryKeywordReport;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryTargetingReport;
import com.meiyunji.sponsored.service.cpc.po.CpcSbQueryKeywordReport;
import com.meiyunji.sponsored.service.localization.dao.IAmazonKeywordLocalizationScheduleDao;
import com.meiyunji.sponsored.service.localization.po.AmazonKeywordLocalizationSchedule;
import com.meiyunji.sponsored.service.taskGrpcApi.AadasApiFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

@Service
@Slf4j
public class KeywordLocalizationScheduleService {

    private final IScVcShopAuthDao shopAuthDao;
    private final ICpcQueryKeywordReportDao cpcQueryKeywordReportDao;
    private final ICpcQueryTargetingReportDao cpcQueryTargetingReportDao;
    private final ICpcSbQueryKeywordReportDao cpcSbQueryKeywordReportDao;
    private final AadasApiFactory aadasApiFactory;
    private IAmazonKeywordLocalizationScheduleDao amazonKeywordLocalizationScheduleDao;
    private static final KeywordsLocalizationClient CLIENT = KeywordsLocalizationClient.getInstance();
    private static final Integer LIMIT = 1000;
    private static Random RANDOM = new Random();
    private final IShopAuthService shopAuthService;

    public KeywordLocalizationScheduleService(
            IScVcShopAuthDao shopAuthDao,
            ICpcQueryKeywordReportDao cpcQueryKeywordReportDao,
            ICpcQueryTargetingReportDao cpcQueryTargetingReportDao, ICpcSbQueryKeywordReportDao cpcSbQueryKeywordReportDao,
            AadasApiFactory aadasApiFactory, IAmazonKeywordLocalizationScheduleDao amazonKeywordLocalizationScheduleDao,
            IShopAuthService shopAuthService) {
        this.shopAuthDao = shopAuthDao;
        this.cpcQueryKeywordReportDao = cpcQueryKeywordReportDao;
        this.cpcQueryTargetingReportDao = cpcQueryTargetingReportDao;
        this.cpcSbQueryKeywordReportDao = cpcSbQueryKeywordReportDao;
        this.aadasApiFactory = aadasApiFactory;
        this.amazonKeywordLocalizationScheduleDao = amazonKeywordLocalizationScheduleDao;
        this.shopAuthService = shopAuthService;
    }


    public void execute(List<AmazonKeywordLocalizationSchedule> needsSyncSchedules) {

        LocalDateTime now = LocalDateTime.now();
        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getKeywordLocalizationPool();
        for (AmazonKeywordLocalizationSchedule schedule : needsSyncSchedules) {
            threadExecutor.execute(() -> {
                if (schedule.getSpNextSyncAt().isBefore(now)) {
                    dealSpKeywordAndTargetQueryLocalization(schedule);
                }
                if (schedule.getSbNextSyncAt().isBefore(now)) {
                    dealSbKeywordQueryLocalization(schedule);
                }
            });
        }
        ThreadPoolUtil.waitingFinish(threadExecutor);
    }

    public void dealSpKeywordAndTargetQueryLocalization(AmazonKeywordLocalizationSchedule schedule) {
        //开始同步sp
        List<CpcQueryKeywordReport> queryKeywordReports =
                cpcQueryKeywordReportDao.getNeedsLocalizationKeywords(
                        schedule.getPuid(), schedule.getShopId(), LIMIT);
        Set<String> keywords = queryKeywordReports.stream().filter(o -> StringUtils.isNotBlank(o.getQuery()))
                .map(item -> StringUtils.trim(item.getQuery())).collect(Collectors.toSet());

        int cnt = LIMIT - keywords.size();
        List<CpcQueryTargetingReport> queryTargetReports = new ArrayList<>();
        if (cnt > 0) {
            queryTargetReports =
                    cpcQueryTargetingReportDao.getNeedsLocalizationKeywords(
                            schedule.getPuid(), schedule.getShopId(), cnt);
            Set<String> targetQuery = queryTargetReports.stream().filter(o -> StringUtils.isNotBlank(o.getQuery()))
                    .map(item -> StringUtils.trim(item.getQuery())).collect(Collectors.toSet());
            keywords.addAll(targetQuery);
        }

        log.info("{}@{} execute sponsored product query keyword localization task,sp keywords size: {} sp targets size: {}",
                schedule.getPuid(), schedule.getShopId(), queryKeywordReports.size(), queryTargetReports.size());
        boolean hasError = false;
        if (CollectionUtils.isNotEmpty(keywords)) {
            ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(schedule.getShopId(), schedule.getPuid());
            KeywordsLocalizationResponse response = getKeywordsLocalize(schedule, keywords, shopAuth);
            if (response.getStatusCode() != null && response.getStatusCode() == 200) {
                Map<String, String> map = response.getResult().getLocalizedKeywordResponses().stream()
                        .filter(k -> k != null && k.getSourceKeyword() != null && k.getLocalizedKeywordResults() != null && k.getLocalizedKeywordResults().get("zh_CN") != null && k.getLocalizedKeywordResults().get("zh_CN").getKeyword() != null)
                        .collect(Collectors.toMap(k -> k.getSourceKeyword().getKeyword(),
                                v -> v.getLocalizedKeywordResults().get("zh_CN").getKeyword().getKeyword(), (a, b) -> a));
                //更新翻译
                for (CpcQueryKeywordReport report : queryKeywordReports) {
                    String query = StringUtils.trim(report.getQuery());
                    if (map.containsKey(query)) {
                        String string = map.get(query);
                        report.setQueryCn(string != null ? string : "  ");
                        //更新关键词翻译
                        cpcQueryKeywordReportDao.updateQueryCnById(shopAuth.getPuid(), report);
                    } else {
                        report.setQueryCn("  ");
                        cpcQueryKeywordReportDao.updateQueryCnById(shopAuth.getPuid(), report);
                    }
                }
                for (CpcQueryTargetingReport report : queryTargetReports) {
                    String query = StringUtils.trim(report.getQuery());
                    if (map.containsKey(query)) {
                        report.setQueryCn(map.get(query) != null ? map.get(query) : "  ");
                        //更新关键词翻译
                        cpcQueryTargetingReportDao.updateQueryCnById(shopAuth.getPuid(), report);
                    } else {
                        report.setQueryCn("  ");
                        //更新关键词翻译
                        cpcQueryTargetingReportDao.updateQueryCnById(shopAuth.getPuid(), report);
                    }
                }
            } else {
                log.info("Request amazon sponsored product keyword localization api with an error. {}@{} status: {} message: {}",
                        schedule.getPuid(), schedule.getShopId(), response.getStatusCode(), response.getStatusMessage());
                hasError = true;
            }
        }

        LocalDateTime now = LocalDateTime.now();
        schedule.setSpNextSyncAt(!hasError && queryKeywordReports.size() < LIMIT
                && queryTargetReports.size() < LIMIT ? now.plusDays(1)
                .plusMinutes(RANDOM.nextInt(120)) :
                now);
        amazonKeywordLocalizationScheduleDao.updateSpNextSyncAtById(schedule);
    }

    private void dealSbKeywordQueryLocalization(AmazonKeywordLocalizationSchedule schedule) {
        List<CpcSbQueryKeywordReport> sbQueryKeywordReports =
                cpcSbQueryKeywordReportDao.getNeedsLocalizationKeywords(
                        schedule.getPuid(), schedule.getShopId(), LIMIT);
        Set<String> sbKeywords = sbQueryKeywordReports.stream().filter(o -> StringUtils.isNotBlank(o.getQuery()))
                .map(item -> StringUtils.trim(item.getQuery())).collect(Collectors.toSet());
        log.info("{}@{} execute sponsored brand query keyword localization task, keywords size: {}",
                schedule.getPuid(), schedule.getShopId(), sbQueryKeywordReports.size());
        boolean hasError = false;
        if (CollectionUtils.isNotEmpty(sbKeywords)) {
            ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(schedule.getShopId(), schedule.getPuid());
            KeywordsLocalizationResponse response = getKeywordsLocalize(schedule, sbKeywords, shopAuth);
            if (response.getStatusCode() != null && response.getStatusCode() == 200) {
                Map<String, String> map = response.getResult().getLocalizedKeywordResponses().stream()
                        .filter(k -> k != null && k.getSourceKeyword() != null && k.getLocalizedKeywordResults() != null && k.getLocalizedKeywordResults().get("zh_CN") != null && k.getLocalizedKeywordResults().get("zh_CN").getKeyword() != null)
                        .collect(Collectors.toMap(k -> k.getSourceKeyword().getKeyword(),
                                v -> v.getLocalizedKeywordResults().get("zh_CN").getKeyword().getKeyword(), (a, b) -> a));
                //更新翻译
                for (CpcSbQueryKeywordReport report : sbQueryKeywordReports) {
                    String query = StringUtils.trim(report.getQuery());
                    if (map.containsKey(query)) {
                        report.setQueryCn(map.get(query) != null ? map.get(query) : "  ");
                        //更新关键词翻译
                        cpcSbQueryKeywordReportDao.updateQueryCnById(shopAuth.getPuid(), report);
                    } else {
                        report.setQueryCn("  ");
                        //更新关键词翻译
                        cpcSbQueryKeywordReportDao.updateQueryCnById(shopAuth.getPuid(), report);
                    }
                }
            } else {
                log.info("Request amazon sponsored brand keyword localization api with an error. {}@{} status: {} message: {}",
                        schedule.getPuid(), schedule.getShopId(), response.getStatusCode(), response.getStatusMessage());
                hasError = true;
            }
        }

        LocalDateTime now = LocalDateTime.now();
        schedule.setSbNextSyncAt(!hasError && sbQueryKeywordReports.size() < LIMIT ? now.plusDays(1)
                .plusMinutes(RANDOM.nextInt(120)) : now);
        amazonKeywordLocalizationScheduleDao.updateSbNextSyncAtById(schedule);
    }

    private KeywordsLocalizationResponse getKeywordsLocalize(AmazonKeywordLocalizationSchedule schedule,
                                                             Set<String> keywords, ShopAuth shopAuth) {

        String accessToken = shopAuthService.getAdToken(shopAuth);
        SourceDetail sourceDetail = new SourceDetail();
        sourceDetail.setMarketplaceId(shopAuth.getMarketplaceId());
        TargetDetail targetDetail = new TargetDetail();
        targetDetail.setLocales(Collections.singletonList("zh_CN"));
        return CLIENT.getKeywordsLocalize(accessToken,
                schedule.getProfileId(), shopAuth.getMarketplaceId(), new ArrayList<>(keywords),
                sourceDetail, targetDetail);
    }
}
