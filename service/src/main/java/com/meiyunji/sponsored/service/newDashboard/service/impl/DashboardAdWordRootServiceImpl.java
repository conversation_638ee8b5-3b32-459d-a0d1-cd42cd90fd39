package com.meiyunji.sponsored.service.newDashboard.service.impl;

import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdWordRootResponseVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonWordRootQueryDao;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdWordRootDataDto;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdWordRootService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardWordRootReqVo;
import com.meiyunji.sponsored.service.wordFrequency.service.helper.WordRootCalculateServiceHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-04-02 18:41
 */
@Service
@Slf4j
public class DashboardAdWordRootServiceImpl implements IDashboardAdWordRootService {
    @Autowired
    private IOdsAmazonWordRootQueryDao odsAmazonWordRootQueryDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    private static final List<String> VALID_MARKETPLACE_IDS = Arrays.asList("ATVPDKIKX0DER", "A2EUQ1WTGCTBG2", "A1F83G8C2ARO7P", "A21TJRUUN4KGV", "A39IBJ37TRP1C6", "A19VAU5U5O7RUS");

    @Override
    public List<DashboardAdWordRootResponseVo> queryAdWordRootCharts(DashboardWordRootReqVo reqVo) {
        List<ShopAuth> shopAuths;
        if (CollectionUtils.isEmpty(reqVo.getShopIdList())) {
            return Collections.emptyList();
        } else {
            shopAuths = shopAuthDao.listAllByIds(reqVo.getPuid(), reqVo.getShopIdList());
        }
        //词频二期的灰度
        Set<String> whitePuidSet = dynamicRefreshConfiguration.getWordFrequencyWhitePuidSet();
        if (dynamicRefreshConfiguration.verifyDorisPageByPuid(reqVo.getPuid(), whitePuidSet)) {
            // 只展示词频二期下的词频数据
            shopAuths = shopAuths.stream().filter(each -> WordRootCalculateServiceHelper.openWordRootMarketplaceList().contains(each.getMarketplaceId())).collect(Collectors.toList());
        } else {
            // 只展示英文站点下的词频数据
            shopAuths = shopAuths.stream().filter(each -> VALID_MARKETPLACE_IDS.contains(each.getMarketplaceId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(shopAuths)) {
            return Collections.emptyList();
        }
        List<Integer> shopIds = shopAuths.stream().map(ShopAuth::getId).collect(Collectors.toList());
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(reqVo.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(reqVo.getMarketplaceIdList());
        }
        List<DashboardAdWordRootDataDto> baseDataList = odsAmazonWordRootQueryDao.queryAdWordRootCharts(
                reqVo.getPuid(), reqVo.getMarketplaceIdList(), shopIds, reqVo.getCurrency(),
                reqVo.getStartDate(), reqVo.getEndDate(), reqVo.getWordFrequencyTypeList(), reqVo.getOrderBy(), reqVo.getLimit(),
                siteToday, reqVo.getSiteToday(), reqVo.getPortfolioIds(), reqVo.getCampaignIds(), reqVo.getNoZero());
        return baseDataList.stream().map(each -> {
            CalculateAdDataUtil.calAdCalData(each);
            return buildGrpcResponseVo(each).build();
        }).collect(Collectors.toList());
    }

    /**
     * 单条数据构建grpc响应对象
     *
     * @param dataDto
     * @return
     */
    private DashboardAdWordRootResponseVo.Builder buildGrpcResponseVo(DashboardAdWordRootDataDto dataDto) {
        DashboardAdWordRootResponseVo.Builder builder = DashboardAdWordRootResponseVo.newBuilder();
        //String类型直接拷贝
        BeanUtils.copyProperties(dataDto, builder);
        //手动设置的类型
        builder.setFrequency(String.valueOf(dataDto.getFrequency()));
        builder.setCost(CalculateUtil.formatDecimal(dataDto.getCost()));
        builder.setTotalSales(CalculateUtil.formatDecimal(dataDto.getTotalSales()));
        builder.setImpressions(String.valueOf(dataDto.getImpressions()));
        builder.setClicks(String.valueOf(dataDto.getClicks()));
        builder.setOrderNum(String.valueOf(dataDto.getOrderNum()));
        builder.setSaleNum(String.valueOf(dataDto.getSaleNum()));
        builder.setAcos(CalculateUtil.formatPercent(dataDto.getAcos()));
        builder.setRoas(CalculateUtil.formatDecimal(dataDto.getRoas()));
        builder.setClickRate(CalculateUtil.formatPercent(dataDto.getClickRate()));
        builder.setConversionRate(CalculateUtil.formatPercent(dataDto.getConversionRate()));
        builder.setCpc(CalculateUtil.formatDecimal(dataDto.getCpc()));
        builder.setCpa(CalculateUtil.formatDecimal(dataDto.getCpa()));
        return builder;
    }
}
