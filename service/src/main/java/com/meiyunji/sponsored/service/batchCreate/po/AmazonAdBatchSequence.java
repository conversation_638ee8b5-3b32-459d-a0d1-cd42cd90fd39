package com.meiyunji.sponsored.service.batchCreate.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-11-16  09:24
 */

@Data
@DbTable(value = "t_amazon_ad_batch_sequence")
public class AmazonAdBatchSequence implements Serializable {

    @DbColumn(value = "id", key = true, autoIncrement = true)
    private Long id;

    @DbColumn(value = "stub")
    private String stub;

    @DbColumn(value = "create_time")
    private Date createTime;

}
