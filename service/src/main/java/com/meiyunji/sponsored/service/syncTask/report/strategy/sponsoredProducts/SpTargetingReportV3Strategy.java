package com.meiyunji.sponsored.service.syncTask.report.strategy.sponsoredProducts;

import com.alibaba.fastjson.JSONReader;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportV3Type;
import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;
import com.meiyunji.sponsored.common.springjdbc.PartitionSqlUtil;
import com.meiyunji.sponsored.service.account.dao.ISlaveVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdKeywordReportDao;
import com.meiyunji.sponsored.service.cpc.dao.ICpcTargetingReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeywordReport;
import com.meiyunji.sponsored.service.cpc.po.CpcTargetingReport;
import com.meiyunji.sponsored.service.enums.ShopTypeEnum;
import com.meiyunji.sponsored.service.enums.SpKeywordGroupValueEnum;
import com.meiyunji.sponsored.service.syncTask.entity.SponsoredProductTargeting;
import com.meiyunji.sponsored.service.syncTask.report.strategy.AbstractReportProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

@Component
@Slf4j
@ConditionalOnProperty(name = "aadas.scheduler.executors.reports-consumer.enabled", havingValue = "true")
public class SpTargetingReportV3Strategy extends AbstractReportProcessStrategy {
    private final IAmazonAdKeywordReportDao amazonAdKeywordReportDao;
    private final ICpcTargetingReportDao cpcTargetingReportDao;
    private final PartitionSqlUtil partitionSqlUtil;
    @Resource
    private ISlaveVcShopAuthDao vcShopAuthDao;

    public SpTargetingReportV3Strategy(
            CosBucketClient dataBucketClient,
            IAmazonAdKeywordReportDao amazonAdKeywordReportDao,
            ICpcTargetingReportDao cpcTargetingReportDao, PartitionSqlUtil partitionSqlUtil) {
        super(dataBucketClient);
        this.amazonAdKeywordReportDao = amazonAdKeywordReportDao;
        this.cpcTargetingReportDao = cpcTargetingReportDao;
        this.partitionSqlUtil = partitionSqlUtil;
    }

    @Override
    public Boolean checkValid(ReportReadyNotification notification) {
        return notification.getVersion() == 3 &&
                notification.getV3Type() == AmazonReportV3Type.sp_keyword_target;
    }

    @Override
    public void processReport(ReportReadyNotification notification) throws Exception {
        try (InputStreamReader inputStreamReader = new InputStreamReader(new GZIPInputStream(new ByteArrayInputStream(dataBucketClient.getObjectToBytes(notification.getPath()))));JSONReader jsonReader = new JSONReader(inputStreamReader)) {
            jsonReader.startArray();
            List<SponsoredProductTargeting> reports = Lists.newArrayListWithExpectedSize(batchSize);
            VcShopAuth byIdAndPuid = vcShopAuthDao.getByIdAndPuid(notification.getMarketplaceIdentifier(), notification.getSellerIdentifier());
            String shopType = ShopTypeEnum.SC.getCode();
            if (byIdAndPuid != null && byIdAndPuid.getId() != null) {
                shopType = ShopTypeEnum.VC.getCode();
            }
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                SponsoredProductTargeting report = new SponsoredProductTargeting();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.getImpressions() != 0) {
                    reports.add(report);
                }
                if (reports.size() >= batchSize) {
                    dealReports(notification, reports, shopType);
                    reports = Lists.newArrayListWithExpectedSize(batchSize);
                }
            }
            jsonReader.endArray();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReports(notification, reports, shopType);
            }
        } catch (Exception e) {
            log.info("报告处理发生错误{}@{} reportType={} countDate={}", notification.getSellerIdentifier()
                    , notification.getMarketplaceIdentifier(), notification.getType(), notification.getDate(), e);
            throw e;
        }
    }

    private void dealReports(ReportReadyNotification notification, List<SponsoredProductTargeting> reports, String shopType) {
        //处理target报告
        List<SponsoredProductTargeting> targetReports = reports.stream().filter(o -> o.getKeywordType()
                != null && Arrays.asList("TARGETING_EXPRESSION", "TARGETING_EXPRESSION_PREDEFINED")
                .contains(o.getKeywordType()) && !o.getTargeting().toLowerCase().contains(SpKeywordGroupValueEnum.getKeywordGroupKey())).collect(Collectors.toList());
        dealTargetReports(notification, targetReports, shopType);
        //处理keyword报告
        List<SponsoredProductTargeting> keywordReports = reports.stream().filter(o -> o.getKeywordType()
                != null && (Arrays.asList("BROAD", "PHRASE", "EXACT")
                .contains(o.getKeywordType()) || (o.getKeywordType().equals("TARGETING_EXPRESSION") && o.getTargeting().toLowerCase().contains(SpKeywordGroupValueEnum.getKeywordGroupKey())))).collect(Collectors.toList());


        dealKeywordReports(notification, keywordReports);
    }

    private void dealTargetReports(ReportReadyNotification notification, List<SponsoredProductTargeting> targetReports, String shopType) {
        List<CpcTargetingReport> poList =
                getPoByReportTargeting(notification, targetReports, shopType);
        List<List<CpcTargetingReport>> partition = Lists.partition(poList, batchSize);
        for (List<CpcTargetingReport> cpcTargetingReports : partition) {
            partitionSqlUtil.save(notification.getSellerIdentifier(), cpcTargetingReports, 0, cpcTargetingReportDao::insertList);
        }
    }


    private void dealKeywordReports(ReportReadyNotification notification, List<SponsoredProductTargeting> keywordReports) {
        List<AmazonAdKeywordReport> poList =
                getPoByReportAdKeyword(notification, keywordReports);
        List<List<AmazonAdKeywordReport>> partition = Lists.partition(poList, batchSize);
        for (List<AmazonAdKeywordReport> amazonAdKeywordReports : partition) {
            partitionSqlUtil.save(notification.getSellerIdentifier(), amazonAdKeywordReports, 0, amazonAdKeywordReportDao::insertList);
        }
    }

    private List<CpcTargetingReport> getPoByReportTargeting(ReportReadyNotification notification, List<SponsoredProductTargeting> reports, String shopType) {
        List<CpcTargetingReport> list = Lists.newArrayList();
        CpcTargetingReport po;
        for (SponsoredProductTargeting report : reports) {
            po = new CpcTargetingReport();
            po.setCountDate(report.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            po.setPuid(notification.getSellerIdentifier());
            po.setShopId(notification.getMarketplaceIdentifier());
            po.setMarketplaceId(notification.getMarketplace().getId());
            po.setCampaignId(String.valueOf(report.getCampaignId()));
            po.setAdGroupId(String.valueOf(report.getAdGroupId()));
            po.setTargetId(String.valueOf(report.getKeywordId()));
            po.setTargetingExpression(report.getTargeting());
            po.setTargetingText(report.getTargeting());
            po.setTargetingType(report.getKeywordType());
            po.setAdGroupName(report.getAdGroupName());
            po.setCampaignName(report.getCampaignName());

            po.setImpressions(report.getImpressions());
            po.setClicks(report.getClicks());
            po.setCost(report.getCost());
            if (ShopTypeEnum.VC.getCode().equalsIgnoreCase(shopType)) {
                po.setTotalSales(report.getSales14d().setScale(2, BigDecimal.ROUND_HALF_UP));
                po.setAdSales(report.getAttributedSalesSameSku14d().setScale(2, BigDecimal.ROUND_HALF_UP));
                po.setOrderNum(report.getUnitsSoldClicks14d());
                po.setAdOrderNum(report.getUnitsSoldSameSku14d());
                po.setSaleNum(report.getPurchases14d());
                po.setAdSaleNum(report.getPurchasesSameSku14d());
            } else {
                po.setTotalSales(report.getSales7d().setScale(2, BigDecimal.ROUND_HALF_UP));
                po.setAdSales(report.getAttributedSalesSameSku7d().setScale(2, BigDecimal.ROUND_HALF_UP));
                po.setOrderNum(report.getUnitsSoldClicks7d());
                po.setAdOrderNum(report.getUnitsSoldSameSku7d());
                po.setSaleNum(report.getPurchases7d());
                po.setAdSaleNum(report.getPurchasesSameSku7d());
            }
            po.setTopOfSearchIs(report.getTopOfSearchImpressionShare());
            list.add(po);
        }
        return list;
    }

    private List<AmazonAdKeywordReport> getPoByReportAdKeyword(
            ReportReadyNotification notification, List<SponsoredProductTargeting> reports) {
        List<AmazonAdKeywordReport> list = Lists.newArrayList();
        AmazonAdKeywordReport amazonAdKeywordReport;

        for (SponsoredProductTargeting report : reports) {
            amazonAdKeywordReport = new AmazonAdKeywordReport();
            amazonAdKeywordReport.setCountDate(report.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            amazonAdKeywordReport.setPuid(notification.getSellerIdentifier());
            amazonAdKeywordReport.setShopId(notification.getMarketplaceIdentifier());
            amazonAdKeywordReport.setMarketplaceId(notification.getMarketplace().getId());
            amazonAdKeywordReport.setCampaignId(String.valueOf(report.getCampaignId()));
            amazonAdKeywordReport.setAdGroupId(String.valueOf(report.getAdGroupId()));
            amazonAdKeywordReport.setKeywordId(String.valueOf(report.getKeywordId()));

            if ("TARGETING_EXPRESSION".equals(report.getMatchType())) {
                amazonAdKeywordReport.setMatchType(SpKeywordGroupValueEnum.getTheme());
                amazonAdKeywordReport.setKeywordText(report.getKeyword());
            } else {
                amazonAdKeywordReport.setMatchType(report.getMatchType());
                amazonAdKeywordReport.setKeywordText(report.getKeyword());
            }
            amazonAdKeywordReport.setAdGroupName(report.getAdGroupName());
            amazonAdKeywordReport.setCampaignName(report.getCampaignName());

            amazonAdKeywordReport.setImpressions(report.getImpressions());
            amazonAdKeywordReport.setClicks(report.getClicks());
            amazonAdKeywordReport.setCost(report.getCost());

            amazonAdKeywordReport.setTotalSales(report.getSales7d().setScale(2, BigDecimal.ROUND_HALF_UP));
            amazonAdKeywordReport.setAdSales(report.getAttributedSalesSameSku7d().setScale(2, BigDecimal.ROUND_HALF_UP));
            amazonAdKeywordReport.setOrderNum(report.getUnitsSoldClicks7d());
            amazonAdKeywordReport.setAdOrderNum(report.getUnitsSoldSameSku7d());
            amazonAdKeywordReport.setSaleNum(report.getPurchases7d());
            amazonAdKeywordReport.setAdSaleNum(report.getPurchasesSameSku7d());
            amazonAdKeywordReport.setTopOfSearchIs(report.getTopOfSearchImpressionShare());
            list.add(amazonAdKeywordReport);
        }
        return list;
    }
}
