package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by xp on 2021/4/14.
 * 广告产品列表页
 */
@Data
@ApiModel
public class AdProductPageParam {

    @ApiModelProperty("类型 sp sb sd")
    private String type;
    private String currency;

    @ApiModelProperty("pageNo")
    private Integer pageNo;

    @ApiModelProperty("pageSize")
    private Integer pageSize;

    @ApiModelProperty(value = "店铺ID",required = true)
    private Integer shopId;

    @ApiModelProperty(value = "商户ID",required = true)
    private Integer puid;

    private Integer uid;


    @ApiModelProperty("广告活动ID")
    private String campaignId;

    @ApiModelProperty("广告组ID")
    private String groupId;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("搜索字段")
    private String searchField;

    @ApiModelProperty("搜索内容")
    private String searchValue;

    @ApiModelProperty("排序字段")
    private String orderField;

    @ApiModelProperty("排序方式 desc asc")
    private String orderType;

    @ApiModelProperty("广告组合ID")
    private String portfolioId;

    @ApiModelProperty(value = "广告组合下的活动id")
    private List<String> campaignIdList;
    @ApiModelProperty(value = "广告标签id")
    private Long adTagId;
    private List<Long> adTagIdList;
    private List<String> adIds;
    @ApiModelProperty(value = "用于仅展示正在投放字段 勾选后传值 enabled")
    private String servingStatus;

    //高级搜索
    @ApiModelProperty(value = "是否开启高级搜索")
    private Boolean useAdvanced;

    //高级搜索值
    @ApiModelProperty(value = "高级搜索展示量最小值")
    private Integer impressionsMin;  //展示量
    @ApiModelProperty(value = "高级搜索展示量最大值")
    private Integer impressionsMax;
    @ApiModelProperty(value = "高级搜索点击量小值")
    private Integer clicksMin;  //点击量
    @ApiModelProperty(value = "高级搜索点击量大值")
    private Integer clicksMax;
    @ApiModelProperty(value = "高级搜索点击率小值")
    private BigDecimal clickRateMin;  //点击率
    @ApiModelProperty(value = "高级搜索点击率大值")
    private BigDecimal clickRateMax;
    @ApiModelProperty(value = "高级搜索花费小值")
    private BigDecimal costMin; //花费
    @ApiModelProperty(value = "高级搜索花费大值")
    private BigDecimal costMax;
    @ApiModelProperty(value = "高级搜索cpc小值")
    private BigDecimal cpcMin;  //cpc
    @ApiModelProperty(value = "高级搜索cpc大值")
    private BigDecimal cpcMax;
    @ApiModelProperty(value = "高级搜索广告订单量小值")
    private Integer orderNumMin;  //广告订单量
    @ApiModelProperty(value = "高级搜索广告订单量大值")
    private Integer orderNumMax;
    @ApiModelProperty(value = "高级搜索sales小值")
    private BigDecimal salesMin;  //sales
    @ApiModelProperty(value = "高级搜索sales大值")
    private BigDecimal salesMax;
    @ApiModelProperty(value = "高级搜索acos小值")
    private BigDecimal acosMin;  //acos
    @ApiModelProperty(value = "高级搜索acos大值")
    private BigDecimal acosMax;
    @ApiModelProperty(value = "高级搜索roas小值")
    private BigDecimal roasMin;   //roas
    @ApiModelProperty(value = "高级搜索roas大值")
    private BigDecimal roasMax;
    @ApiModelProperty(value = "高级搜索广告订单转化率小值")
    private BigDecimal salesConversionRateMin;  //订单转化率
    @ApiModelProperty(value = "高级搜索广告订单转化率大值")
    private BigDecimal salesConversionRateMax;
    @ApiModelProperty(value = "高级搜索acots小值")
    private BigDecimal acotsMin;  //acots
    @ApiModelProperty(value = "高级搜索acots大值")
    private BigDecimal acotsMax;
    @ApiModelProperty(value = "高级搜索asots小值")
    private BigDecimal asotsMin;  //acos
    @ApiModelProperty(value = "高级搜索asots大值")
    private BigDecimal asotsMax;


    @ApiModelProperty(value = "高级搜索投放类型")
    private String filterTargetType;
    @ApiModelProperty(value = "高级搜索产品价格最小")
    private BigDecimal productPriceMin;
    @ApiModelProperty(value = "高级搜索产品价格最大")
    private BigDecimal productPriceMax;


    @ApiModelProperty(value = "高级搜索店铺销售额")
    private BigDecimal shopSales;


    //环比相关参数
    private Boolean isCompare;
    private String compareStartDate;
    private String compareEndDate;
    private String pageSign;


    /*******************高级搜索新增查询字段***************************/
    @ApiModelProperty(value = "可见展示次数最小值")
    private Integer viewImpressionsMin;
    @ApiModelProperty(value = "可见展示次数最大值")
    private Integer viewImpressionsMax;


    @ApiModelProperty(value = "cpa最小值")
    private BigDecimal cpaMin;
    @ApiModelProperty(value = "cpa最大值")
    private BigDecimal cpaMax;


    @ApiModelProperty(value = "vcpm最小值")
    private BigDecimal vcpmMin;
    @ApiModelProperty(value = "vcpm最大值")
    private BigDecimal vcpmMax;


    @ApiModelProperty(value = "本广告产品订单量最小值")
    private Integer adSaleNumMin;
    @ApiModelProperty(value = "本广告产品订单量最大值")
    private Integer adSaleNumMax;

    private String uuid;
    @ApiModelProperty(value = "其他产品广告订单量最小值")
    private Integer adOtherOrderNumMin;
    @ApiModelProperty(value = "其他产品广告订单量最大值")
    private Integer adOtherOrderNumMax;


    @ApiModelProperty(value = "本广告产品销售额最小值")
    private BigDecimal adSalesMin;
    @ApiModelProperty(value = "本广告产品销售额最大值")
    private BigDecimal adSalesMax;


    @ApiModelProperty(value = "其他产品广告销售额最小值")
    private BigDecimal adOtherSalesMin;
    @ApiModelProperty(value = "其他产品广告销售额最大值")
    private BigDecimal adOtherSalesMax;


    @ApiModelProperty(value = "本廣告产品销量最小值")
    private Integer adSelfSaleNumMin;
    @ApiModelProperty(value = "本廣告产品销量最大值")
    private Integer adSelfSaleNumMax;


    @ApiModelProperty(value = "其他产品广告销量最小值")
    private Integer adOtherSaleNumMin;
    @ApiModelProperty(value = "其他产品广告销量最大值")
    private Integer adOtherSaleNumMax;

    @ApiModelProperty(value = "广告销量最小值")
    private Integer adSalesTotalMin;
    @ApiModelProperty(value = "广告销量最大值")
    private Integer adSalesTotalMax;


    @ApiModelProperty(value = "“品牌新买家”订单量最小值")
    private Integer ordersNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”订单量最大值")
    private Integer ordersNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”订单百分比最小值")
    private BigDecimal orderRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”订单百分比最大值")
    private BigDecimal orderRateNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销售额最小值")
    private BigDecimal salesNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销售额最大值")
    private BigDecimal salesNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销售额百分比最小值")
    private BigDecimal salesRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销售额百分比最大值")
    private BigDecimal salesRateNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销量最小值")
    private Integer unitsOrderedNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销量最大值")
    private Integer unitsOrderedNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销量百分比最小值")
    private BigDecimal unitsOrderedRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销量百分比最大值")
    private BigDecimal unitsOrderedRateNewToBrandFTDMax;


    @ApiModelProperty("加购次数最小值")
    private Integer addToCartMin;
    @ApiModelProperty("加购次数最大值")
    private Integer addToCartMax;

    @ApiModelProperty("视频完整播放次数最小值")
    private Integer videoCompleteViewsMin;
    @ApiModelProperty("视频完整播放次数最大值")
    private Integer videoCompleteViewsMax;

    @ApiModelProperty("观看率最小值")
    private BigDecimal viewabilityRateMin;
    @ApiModelProperty("观看率最大值")
    private BigDecimal viewabilityRateMax;

    @ApiModelProperty("观看点击率最小值")
    private BigDecimal viewClickThroughRateMin;
    @ApiModelProperty("观看点击率最大值")
    private BigDecimal viewClickThroughRateMax;

    @ApiModelProperty("品牌搜索次数最小值")
    private Integer brandedSearchesMin;
    @ApiModelProperty("品牌搜索次数最大值")
    private Integer brandedSearchesMax;

    @ApiModelProperty("累计触达用户最小值")
    private Integer cumulativeReachMin;
    @ApiModelProperty("累计触达用户最大值")
    private Integer cumulativeReachMax;

    @ApiModelProperty("平均触达次数最小值")
    private BigDecimal impressionsFrequencyAverageMin;
    @ApiModelProperty("平均触达次数最大值")
    private BigDecimal impressionsFrequencyAverageMax;

    @ApiModelProperty("广告笔单价最小值")
    private BigDecimal advertisingUnitPriceMin;
    @ApiModelProperty("广告笔单价最大值")
    private BigDecimal advertisingUnitPriceMax;

    /** 查询数据类型 {@link com.meiyunji.sponsored.service.enums.SearchDataTypeEnum}*/
    @ApiModelProperty("查询数据类型")
    private Integer searchDataType;

    /**
     *  只查询数量 简化查询
     */
    private Boolean onlyCount;
    private Pair<Boolean, List<String>> checkProductRightPair;

    private Boolean isAdmin;


    public List<String> getListSearchValue(){
        if(StringUtils.isNotBlank(this.searchValue)){
            return StringUtil.stringToList(this.searchValue.trim(),",");
        }
        return new ArrayList<>();
    }


    /**
     * 新的传参分隔符  %±%
     * @return 返回String类型参数数组
     */
    public List<String> getListSearchValueNew(){
        if (StringUtils.isNotBlank(this.searchValue)) {
            return StringUtil.stringToList(this.searchValue.trim(),"%±%");
        }
        return new ArrayList<>();
    }

}
