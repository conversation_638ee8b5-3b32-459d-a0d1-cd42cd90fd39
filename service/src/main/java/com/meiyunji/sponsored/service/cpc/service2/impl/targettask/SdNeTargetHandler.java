package com.meiyunji.sponsored.service.cpc.service2.impl.targettask;

import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.amazon.advertising.mode.targeting.TargetingClause;
import com.amazon.advertising.mode.targeting.TargetingClauseResult;
import com.amazon.advertising.sb.mode.targeting.SBResolvedExpression;
import com.amazon.advertising.sd.entity.neTargeting.CreateNegativeTargetingClausesResponse;
import com.amazon.advertising.sd.entity.neTargeting.NeProductTargetingClient;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetObjectTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskConstant;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskStatusEnum;
import com.meiyunji.sponsored.service.cpc.constants.TargetingTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdNeTargetingDao;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetDetailDto;
import com.meiyunji.sponsored.service.cpc.dto.CommonAmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.ICommonAmazonAdTargetingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.SdBatchNeTargetingVo;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-05-08 19:23
 */
@Service(AdTargetTaskConstant.SD_NE_TARGET_HANDLER)
@Slf4j
public class SdNeTargetHandler implements TargetTaskHandler {

    @Autowired
    private TargetTaskComponent targetTaskComponent;
    @Autowired
    private IAmazonSdAdNeTargetingDao amazonSdAdNeTargetingDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private ICommonAmazonAdTargetingService commonAmazonAdTargetingService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Override
    public void handle(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        //处理业务返回结果
        createTargeting(adTargetTask, adTargetTaskDetails);
    }

    private void createTargeting(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        List<AdTargetTaskDetail> needUpdateDetailList = new ArrayList<>();
        if (CollectionUtils.isEmpty(adTargetTaskDetails)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        int originalTaskDetailSize = adTargetTaskDetails.size();
        // 排除已存在的关键词
        Set<String> adGroupIdSet = new HashSet<>();
        Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap = new HashMap<>();
        for (AdTargetTaskDetail targetTaskDetail : adTargetTaskDetails) {
            adGroupIdSet.add(targetTaskDetail.getAdGroupId());
            adTargetTaskDetailMap.put(targetTaskDetail.getId(), targetTaskDetail);
        }

        if (CollectionUtils.isEmpty(adGroupIdSet)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        List<AmazonSdAdGroup> amazonSdAdGroups = amazonSdAdGroupDao.listByGroupId(adTargetTask.getPuid(), adTargetTask.getShopId(), new ArrayList<>(adGroupIdSet));
        Map<String, AmazonSdAdGroup> amazonAdGroupMap = amazonSdAdGroups.stream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, Function.identity(), (newVal, oldVal) -> newVal));

        List<SdBatchNeTargetingVo> amazonAdTargetings = covertSdNeTargetingVo(adTargetTask.getUid(), amazonAdGroupMap, adTargetTaskDetails, needUpdateDetailList, adTargetTask.getTargetingType());
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(adTargetTask.getShopId(), adTargetTask.getPuid());
        if (shop == null) {
            adTargetTaskDetails.forEach(each -> {
                each.setFailureReason("没有CPC授权");
                each.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                needUpdateDetailList.add(each);
            });
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(adTargetTask.getPuid(), adTargetTask.getShopId());
        if (profile == null) {
            adTargetTaskDetails.forEach(each -> {
                each.setFailureReason("没有站点对应的配置信息");
                each.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                needUpdateDetailList.add(each);
            });
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        int failureNum = 0;
        List<List<SdBatchNeTargetingVo>> amazonAdTargetingPartition = Lists.partition(amazonAdTargetings, AdTargetTaskConstant.MAX_SD_TARGET_SIZE);
        for (List<SdBatchNeTargetingVo> sdNeTargetVoList : amazonAdTargetingPartition) {
            Result result = create(sdNeTargetVoList, adTargetTaskDetailMap, shop, profile);
            for (SdBatchNeTargetingVo amazonSdAdTargeting : sdNeTargetVoList) {
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonSdAdTargeting.getTargetTaskDetailId());
                needUpdateDetailList.add(adTargetTaskDetail);
                if (AdTargetTaskStatusEnum.FAILURE.getCode() == adTargetTaskDetail.getStatus()) {
                    failureNum++;
                }
            }
            if (result.success()) {
                List<AmazonSdAdNeTargeting> amazonAdTargetingList = new ArrayList<>(sdNeTargetVoList.size());
                if (CollectionUtils.isNotEmpty(sdNeTargetVoList)) {
                    sdNeTargetVoList.forEach(neTargetingVo -> {
                        AmazonSdAdNeTargeting adNeTargeting = new AmazonSdAdNeTargeting();
                        BeanUtils.copyProperties(neTargetingVo, adNeTargeting);
                        adNeTargeting.setCreateId(adTargetTask.getUid());
                        amazonAdTargetingList.add(adNeTargeting);
                    });
                }

                logSdNeTargetingCreate(amazonAdTargetingList, adTargetTask.getLoginIp(), result);

                List<AmazonSdAdNeTargeting> succList = amazonAdTargetingList.stream().filter(e -> StringUtils.isNotBlank(e.getTargetId())).collect(Collectors.toList());
                if (succList.size() > 0) {

                    // 有可能已经添加过了: 重复添加同一个否定asin或者品牌，接口不会报错，会返回其对应的targetId
                    List<String> existInDB = amazonSdAdNeTargetingDao.listByTargetId(adTargetTask.getPuid(), adTargetTask.getShopId(), succList.stream()
                            .map(AmazonSdAdNeTargeting::getTargetId).collect(Collectors.toList())).stream().map(AmazonSdAdNeTargeting::getTargetId).collect(Collectors.toList());

                    // 排除掉已有的
                    if (CollectionUtils.isNotEmpty(existInDB)) {
                        succList = succList.stream().filter(e -> !existInDB.contains(e.getTargetId())).collect(Collectors.toList());
                    }

                    // 入库
                    try {
                        amazonSdAdNeTargetingDao.batchAdd(adTargetTask.getPuid(), succList);
                    } catch (Exception e) {
                        log.error("createSdTargeting:", e);
                    }
                }
            }
            targetTaskComponent.updateTaskDetailStatus(adTargetTask, needUpdateDetailList);
            needUpdateDetailList.clear();
        }

        failureNum += originalTaskDetailSize - amazonAdTargetings.size();
        int adTargetTaskStatus = targetTaskComponent.getAdTargetTaskStatus(originalTaskDetailSize, failureNum);
        targetTaskComponent.updateTaskStatus(adTargetTask, adTargetTaskStatus);
    }

    private void logSdNeTargetingCreate(List<AmazonSdAdNeTargeting> neTargetings, String ip, Result result) {
        try {
            List<AdManageOperationLog> operationLogs = Lists.newArrayListWithExpectedSize(2);
            for (AmazonSdAdNeTargeting sdNeTargeting : neTargetings) {
                AdManageOperationLog operationLog = adManageOperationLogService.getSdNeTargetsLog(null, sdNeTargeting);
                operationLog.setIp(ip);
                if (StringUtils.isNotBlank(sdNeTargeting.getTargetId())) {
                    operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    String errMsg = "";
                    if (StringUtils.isNotBlank(sdNeTargeting.getErrMsg())) {
                        errMsg = "targetValue:" + sdNeTargeting.getTargetText() + ", desc:" + sdNeTargeting.getErrMsg();
                    } else {
                        errMsg = result.getMsg();
                    }
                    operationLog.setResultInfo(errMsg);
                }
                operationLogs.add(operationLog);
            }
            adManageOperationLogService.batchLogsMergeByAdGroup(operationLogs);
        } catch (Exception e) {
            log.error("logSdNeTargetingCreate error", e);
        }
    }

    private Result create(List<SdBatchNeTargetingVo> amazonSdAdTargetings, Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap, ShopAuth shop, AmazonAdProfile profile) {
        List<TargetingClause> targetingClauses = amazonSdAdTargetings.stream().map(e -> {
            TargetingClause targetingClause = new TargetingClause();
            if (StringUtils.isNotBlank(e.getAdGroupId())) {
                targetingClause.setAdGroupId(Long.valueOf(e.getAdGroupId()));
            }
            targetingClause.setExpressionType(e.getExpressionType());
            targetingClause.setState(e.getState());
            if (StringUtils.isNotBlank(e.getExpression())) {
                targetingClause.setExpressions(JSONUtil.jsonToArray(e.getExpression(), Expression.class));
            }
            return targetingClause;
        }).collect(Collectors.toList());

        CreateNegativeTargetingClausesResponse response = cpcApiHelper.call(shop, () -> NeProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).create(shopAuthService.getAdToken(shop), profile.getProfileId(),
                shop.getMarketplaceId(), targetingClauses));

        if (response == null) {
            for (SdBatchNeTargetingVo amazonAdTargeting : amazonSdAdTargetings) {
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
                adTargetTaskDetail.setFailureReason("网络延迟，请稍后重试");
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
            }
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "网络延迟，请稍后重试";
        String formatErrMsg = errMsg;
        if (CollectionUtils.isNotEmpty(response.getResultList())) {
            List<TargetingClauseResult> resultList = response.getResultList();
            int index = 0;
            for (TargetingClauseResult productAdResult : resultList) {
                if ("SUCCESS".equals(productAdResult.getCode())) {
                    SdBatchNeTargetingVo amazonAdTargeting = amazonSdAdTargetings.get(index);
                    amazonAdTargeting.setTargetId(String.valueOf(productAdResult.getTargetId()));
                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.SUCCESS.getCode());
                } else {
                    SdBatchNeTargetingVo amazonAdTargeting = amazonSdAdTargetings.get(index);
                    String returnError = StringUtils.defaultIfBlank(productAdResult.getDetails(), productAdResult.getDescription());
                    String error = AmazonErrorUtils.getError(returnError);
                    amazonAdTargeting.setErrMsg(error);

                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
                    adTargetTaskDetail.setFailureReason(targetTaskComponent.getError(error, returnError));
                    adTargetTaskDetail.setFailureReasonDetail(returnError);
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                }
                index++;
            }

            return ResultUtil.success();
        } else if (response.getError() != null) {
            errMsg = AmazonErrorUtils.getError(response.getError().getDetails());
            formatErrMsg = targetTaskComponent.getError(errMsg, response.getError().getMessage());
        }
        for (SdBatchNeTargetingVo amazonAdTargeting : amazonSdAdTargetings) {
            AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
            adTargetTaskDetail.setFailureReason(formatErrMsg);
            adTargetTaskDetail.setFailureReason(errMsg);
            adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
        }

        return ResultUtil.error(errMsg);
    }

    private List<SdBatchNeTargetingVo> covertSdNeTargetingVo(Integer uid, Map<String, AmazonSdAdGroup> amazonAdGroupMap, List<AdTargetTaskDetail> adTargetTaskDetails, List<AdTargetTaskDetail> needUpdateDetailList, String targetingType) {
        List<SdBatchNeTargetingVo> amazonAdTargetings = new ArrayList<>(adTargetTaskDetails.size());
        SdBatchNeTargetingVo targetVo;
        Iterator<AdTargetTaskDetail> it = adTargetTaskDetails.iterator();
        AdTargetTaskDetail adTargetTaskDetail;
//        AdTargetTaskDetail first = adTargetTaskDetails.get(0);
//        List<String> targetIds = adTargetTaskDetails.stream().map(AdTargetTaskDetail::getTargetId).distinct().collect(Collectors.toList());
//        Map<String, AdTargetDetailDto> targetDetailMap = buildTargetDetailMap(first.getPuid(), targetIds, first.getSourceShopId(), targetingType);
        while (it.hasNext()) {
            adTargetTaskDetail = it.next();
            AmazonSdAdGroup amazonAdGroup = amazonAdGroupMap.get(adTargetTaskDetail.getAdGroupId());
            if (amazonAdGroup == null) {
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                adTargetTaskDetail.setFailureReason("广告组不存在");
                needUpdateDetailList.add(adTargetTaskDetail);
                it.remove();
            } else {
                String type = AdTargetObjectTypeEnum.getTargetTypeByCode(adTargetTaskDetail.getTargetObjectType());
                targetVo = new SdBatchNeTargetingVo();
                targetVo.setPuid(adTargetTaskDetail.getPuid());
                targetVo.setShopId(amazonAdGroup.getShopId());
                targetVo.setMarketplaceId(amazonAdGroup.getMarketplaceId());
                targetVo.setAdGroupId(amazonAdGroup.getAdGroupId());
                targetVo.setProfileId(amazonAdGroup.getProfileId());
                targetVo.setCampaignId(amazonAdGroup.getCampaignId());
                targetVo.setExpressionType(Constants.MANUAL);
                targetVo.setState(CpcStatusEnum.enabled.name());
                targetVo.setType(type);
                targetVo.setUid(uid);
                targetVo.setCreateInAmzup(1);
                targetVo.setTitle(adTargetTaskDetail.getTargetObjectDesc());
                targetVo.setImgUrl(adTargetTaskDetail.getImgUrl());
                targetVo.setTargetTaskDetailId(adTargetTaskDetail.getId());
                List<Expression> expressions = new ArrayList<>(1);
                if (SdTargetTypeEnum.asin.name().equals(type)) {
                    Expression expression = new Expression();
                    expression.setType(ExpressionEnum.asinSameAs.value());
                    expression.setValue(adTargetTaskDetail.getTargetObject());
                    targetVo.setTargetText(adTargetTaskDetail.getTargetObject());
                    expressions.add(expression);
                } else if (SdTargetTypeEnum.brand.name().equals(type)) {
                    // 目前赛狐sd还不支持品牌否定,产品侧决定先直接报错处理
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                    adTargetTaskDetail.setFailureReason("接口异常");
                    needUpdateDetailList.add(adTargetTaskDetail);
                    it.remove();
                    continue;
//                    AdTargetDetailDto adTargetDetail = targetDetailMap.get(adTargetTaskDetail.getTargetId());
//                    Expression expression = new Expression();
//                    expression.setType(ExpressionEnum.asinBrandSameAs.value());
//                    expression.setValue(adTargetDetail.getBrandId());
//                    targetVo.setTargetText(adTargetDetail.getBrandName());
//                    expressions.add(expression);
                }
                targetVo.setExpression(JSONUtil.objectToJson(expressions));
                amazonAdTargetings.add(targetVo);
            }
        }
        return amazonAdTargetings;
    }

    @Override
    public Map<String, AdTargetDetailDto> buildTargetDetailMap(Integer puid, List<String> targetIds, List<Integer> sourceShopIds, String targetingType) {
        List<CommonAmazonAdTargeting> list = commonAmazonAdTargetingService.listByTargetIds(targetingType, puid, sourceShopIds, targetIds);
        return list.stream().map(each -> {
            AdTargetDetailDto adTargetDetail = new AdTargetDetailDto();
            adTargetDetail.setTargetId(each.getTargetId());
            boolean isSp = TargetingTypeEnum.SP_TARGETING_CODE_LIST.contains(targetingType);
            String type = each.getType();
            if (TargetTypeEnum.asin.name().equals(type)) {
                adTargetDetail.setTargetObject(each.getTargetingValue());
                adTargetDetail.setTargetObjectDesc(each.getTitle());
                adTargetDetail.setImgUrl(each.getImgUrl());
            } else if (TargetTypeEnum.brand.name().equals(type)) {
                targetTaskComponent.fillNeTargetDetail(each, adTargetDetail, isSp);
            }
            return adTargetDetail;
        }).collect(Collectors.toMap(AdTargetDetailDto::getTargetId, Function.identity(), (newVal, oldVal) -> newVal));
    }
}
