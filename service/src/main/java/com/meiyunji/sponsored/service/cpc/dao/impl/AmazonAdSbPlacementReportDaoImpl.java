package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSbPlacementReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaign;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSbPlacementReport;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.enums.AmazonAdvertisePredicateEnum;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/10/20
 */
@Repository
public class AmazonAdSbPlacementReportDaoImpl extends BaseShardingDaoImpl<AmazonAdSbPlacementReport> implements IAmazonAdSbPlacementReportDao {

    @Override
    public void insertOrUpdateList(Integer puid, List<AmazonAdSbPlacementReport> list) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_ad_sb_placement_report` (`puid`, `shop_id`, `marketplace_id`, `count_date`, `ad_format`, `campaign_name`, `campaign_id`, `campaign_status`, `placement`, ")
                .append("`campaign_budget`, `campaign_budget_type`, `campaign_rule_based_budget`, `applicable_budget_rule_id`, `applicable_budget_rule_name`, ")
                .append("`impressions`, `clicks`, `currency`, `cost`, `sales14d`, `sales14d_same_sku`, `conversions14d`, `conversions14d_same_sku`, `branded_searches14d`, ")
                .append("`detail_page_views_clicks14d`, `orders_new_to_brand14d`, `orders_new_to_brand_percentage14d`, `order_rate_new_to_brand14d`, `sales_new_to_brand14d`, ")
                .append("`sales_new_to_brand_percentage14d`, `units_ordered_new_to_brand14d`, `units_ordered_new_to_brand_percentage14d`, `units_sold14d`, ")
                .append("`dpv14d`, `vctr`, `vtr`, `video5second_view_rate`, `video5second_views`, `video_complete_views`, `video_first_quartile_views`, ")
                .append("`video_midpoint_views`, `video_third_quartile_views`, `viewable_impressions`, `video_unmutes`,`create_time`,`update_time` ) values");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdSbPlacementReport report : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getCountDate());
            argsList.add(report.getAdFormat());
            argsList.add(report.getCampaignName() == null ? "": report.getCampaignName());
            argsList.add(report.getCampaignId());
            argsList.add(report.getCampaignStatus());
            argsList.add(report.getPlacement());
            argsList.add(report.getCampaignBudget());
            argsList.add(report.getCampaignBudgetType());
            argsList.add(report.getCampaignRuleBasedBudget() );
            argsList.add(report.getApplicableBudgetRuleId() );
            argsList.add(report.getApplicableBudgetRuleName() );
            argsList.add(report.getImpressions());
            argsList.add(report.getClicks());
            argsList.add(report.getCurrency());
            argsList.add(report.getCost());
            argsList.add(report.getSales14d());
            argsList.add(report.getSales14dSameSKU());
            argsList.add(report.getConversions14d());
            argsList.add(report.getConversions14dSameSKU());
            argsList.add(report.getBrandedSearches14d());
            argsList.add(report.getDetailPageViewsClicks14d() );
            argsList.add(report.getOrdersNewToBrand14d());
            argsList.add(report.getOrdersNewToBrandPercentage14d());
            argsList.add(report.getOrderRateNewToBrand14d());
            argsList.add(report.getSalesNewToBrand14d() );
            argsList.add(report.getSalesNewToBrandPercentage14d() );
            argsList.add(report.getUnitsOrderedNewToBrand14d());
            argsList.add(report.getUnitsOrderedNewToBrandPercentage14d());
            argsList.add(report.getUnitsSold14d());
            argsList.add(report.getDpv14d());
            argsList.add(report.getVctr());
            argsList.add(report.getVtr());
            argsList.add(report.getVideo5SecondViewRate());
            argsList.add(report.getVideo5SecondViews());
            argsList.add(report.getVideoCompleteViews());
            argsList.add(report.getVideoFirstQuartileViews());
            argsList.add(report.getVideoMidpointViews());
            argsList.add(report.getVideoThirdQuartileViews());
            argsList.add(report.getViewableImpressions());
            argsList.add(report.getVideoUnmutes());
        }

        sql.deleteCharAt(sql.length()-1);
        sql.append(" on duplicate key update `campaign_name`=values(campaign_name),`campaign_status`=values(campaign_status),`campaign_budget`=values(campaign_budget),`campaign_budget_type`=values(campaign_budget_type),`campaign_rule_based_budget`=values(campaign_rule_based_budget),");
        sql.append("`applicable_budget_rule_id`=values(applicable_budget_rule_id),`applicable_budget_rule_name`=values(applicable_budget_rule_name),");
        sql.append("`impressions`=values(impressions),`clicks`=values(clicks),`currency`=values(currency),`cost`=values(cost),`sales14d`=values(sales14d),");
        sql.append("`sales14d_same_sku`=values(sales14d_same_sku),`conversions14d`=values(conversions14d),`conversions14d_same_sku`=values(conversions14d_same_sku),");
        sql.append("`branded_searches14d`=values(branded_searches14d),`detail_page_views_clicks14d`=values(detail_page_views_clicks14d),`orders_new_to_brand14d`=values(orders_new_to_brand14d),");
        sql.append("`orders_new_to_brand_percentage14d`=values(orders_new_to_brand_percentage14d),`order_rate_new_to_brand14d`=values(order_rate_new_to_brand14d),");
        sql.append("`sales_new_to_brand14d`=values(sales_new_to_brand14d),`sales_new_to_brand_percentage14d`=values(sales_new_to_brand_percentage14d),`units_ordered_new_to_brand14d`=values(units_ordered_new_to_brand14d),`units_ordered_new_to_brand_percentage14d`=values(units_ordered_new_to_brand_percentage14d),");
        sql.append("`units_sold14d`=values(units_sold14d),`dpv14d`=values(dpv14d),`vctr`=values(vctr),`vtr`=values(vtr),");
        sql.append("`video5second_view_rate`=values(video5second_view_rate),`video5second_views`=values(video5second_views),`video_complete_views`=values(video_complete_views),`video_first_quartile_views`=values(video_first_quartile_views),");
        sql.append("`video_midpoint_views`=values(video_midpoint_views),`video_third_quartile_views`=values(video_third_quartile_views),");
        sql.append("`viewable_impressions`=values(viewable_impressions),`video_unmutes`=values(video_unmutes)");
        getJdbcTemplate(puid).update(sql.toString(),argsList.toArray());
    }

    @Override
    public Page getPageSpaceList(int puid, SearchVo search, Page page) {
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,marketplace_id,count_date,campaign_id,campaign_name,ad_format,placement,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`cost`) cost,")
                .append("sum(`sales14d`) sales14d,sum(`sales14d_same_sku`) sales14d_same_sku,sum(`conversions14d`) conversions14d, sum(`conversions14d_same_sku`) conversions14d_same_sku,sum(`branded_searches14d`) branded_searches14d,")
                .append("sum(`detail_page_views_clicks14d`) detail_page_views_clicks14d,sum(`orders_new_to_brand14d`) orders_new_to_brand14d,sum(`sales_new_to_brand14d`) sales_new_to_brand14d,")
                .append("sum(`units_ordered_new_to_brand14d`) units_ordered_new_to_brand14d,sum(`vctr`) vctr,sum(`vtr`) vtr,")
                .append("sum(`video5second_view_rate`) video5second_view_rate,sum(`video5second_views`) video5second_views,sum(`video_first_quartile_views`) video_first_quartile_views,")
                .append("sum(`video_midpoint_views`) video_midpoint_views,sum(`video_third_quartile_views`) video_third_quartile_views,sum(`video_unmutes`) video_unmutes,sum(`viewable_impressions`) viewable_impressions,")
                .append("sum(`video_complete_views`) video_complete_views FROM `t_amazon_ad_sb_placement_report` ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( select campaign_id FROM `t_amazon_ad_sb_placement_report` ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        if (search.getShopId() != null) {
            whereSql.append("and shop_id=? ");
            argsList.add(search.getShopId());
        } else if (CollectionUtils.isNotEmpty(search.getShopIds())) {
            whereSql.append("and shop_id in ('").append(StringUtils.join(search.getShopIds(),"','")).append("') ");
        }
        if (StringUtils.isNotBlank(search.getMarketplaceId())) {
            whereSql.append("and marketplace_id = ? ");
        }
        whereSql.append("and count_date>=? and count_date<=? ");
        argsList.add(DateUtil.dateToStrWithFormat(search.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(search.getEnd(),"yyyyMMdd"));
        whereSql.append("group by campaign_id, placement ");
        if ("daily".equals(search.getTabType())) {
            whereSql.append(",count_date ");
        }
        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdSbPlacementReport.class);
    }
}
