package com.meiyunji.sponsored.service.strategyTask.dao;

import com.meiyunji.sponsored.common.springjdbc.IAdBaseDao;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyTaskSequence;

import java.util.List;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-08  09:36
 */
public interface AdvertiseStrategyTaskSequenceDao extends IAdBaseDao<AdvertiseStrategyTaskSequence> {
    Long genId();

    List<Long> batchGenId(Integer size);
}
