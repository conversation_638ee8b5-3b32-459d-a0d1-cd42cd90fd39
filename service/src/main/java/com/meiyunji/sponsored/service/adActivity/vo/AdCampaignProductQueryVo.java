package com.meiyunji.sponsored.service.adActivity.vo;

import com.meiyunji.sponsored.service.cpc.dto.AdProductDetailDto;
import com.meiyunji.sponsored.service.cpc.dto.CpcProductDto;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class AdCampaignProductQueryVo {


    /**
     * 产品id
     */
    private Long id;

    /**
     * asin
     */
    private String asin;

    /**
     * sku
     */
    private String sku;

    /**
     * 主图
     */
    private String imgUrl;

    /**
     * 名称
     */
    private String title;

    /**
     * 在线状态
     */
    private String onlineStatus;

    /**
     * 站点
     */
    private String marketplaceId;


    /**
     * 是否符合接口添加条件
     */
//    private Boolean isMeetConditions;
//
//    /**
//     * 合格状态
//     */
//    private String eligibilityStatus;



    /**
     * 域名
     */
    private String domain;


    /**
     * 参数转换
     * @param item
     * @return
     */
    public static AdCampaignProductQueryVo fromDto(AdProductDetailDto item) {
        return AdCampaignProductQueryVo.builder()
                .id(item.getId())
                .asin(item.getAsin())
                .sku(item.getSku())
                .imgUrl(item.getMainImage())
                .title(item.getTitle())
                .onlineStatus(item.getOnlineStatus())
                .marketplaceId(item.getMarketplaceId())
                .build();
    }

    public static CpcProductDto toCpcProductDto(AdProductDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return new CpcProductDto(dto.getAsin(), dto.getSku());
    }
}
