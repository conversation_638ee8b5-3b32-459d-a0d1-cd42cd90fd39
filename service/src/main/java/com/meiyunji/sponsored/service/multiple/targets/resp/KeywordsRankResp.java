package com.meiyunji.sponsored.service.multiple.targets.resp;

import com.meiyunji.sponsored.service.cpc.vo.ProductVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * 投放层级多店铺实时排名 响应参数
 * @Author: zzh
 * @Date: 2025/4/17 13:24
 */
@Data
@ApiModel
public class KeywordsRankResp {

    // 关键词主键id
    private String id;

    // 站点名称
    private String siteName;

    // 站点ID
    private String siteId;

    // 在线产品
    private List<ProductVo> products;

    // 关键词
    private String keyword;

    // 关键词ID
    private String keywordId;

    // 关键词查询url
    private String url;

    // 店铺id
    private Integer shopId;
}
