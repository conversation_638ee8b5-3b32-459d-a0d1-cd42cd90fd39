package com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-08-29  20:10
 */
@Data
public class PlacementViewVo extends StreamDataViewVo {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("店铺id")
    private Integer shopId;

    @ApiModelProperty("站点id")
    private String marketplaceId;

    @ApiModelProperty("位置")
    private String predicate;

    @ApiModelProperty("广告活动类型")
    private String type;

    @ApiModelProperty("广告活动ID")
    private String campaignId;

    @ApiModelProperty("名称")
    private String campaignName;

    @ApiModelProperty("广告活动竞价策略")
    private String strategy;

    @ApiModelProperty("广告组合id")
    private String portfolioId;

    @ApiModelProperty("广告组合名称")
    private String portfolioName;

    @ApiModelProperty("广告组合隐藏状态")
    private Integer isHidden;

    @ApiModelProperty("竞价")
    private String percentage;

    @ApiModelProperty("竞价日志")
    private DataLogVo bidLog;

    @ApiModelProperty("竞价日志更新标识")
    private Boolean isUpdateBid;
}
