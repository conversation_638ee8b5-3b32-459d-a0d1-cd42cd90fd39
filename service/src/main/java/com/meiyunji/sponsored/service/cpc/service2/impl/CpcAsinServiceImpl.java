package com.meiyunji.sponsored.service.cpc.service2.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.AssertUtil;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.PageUtil;
import com.meiyunji.sponsored.rpc.adCommon.AdAsinHomeAggregateDataRpcVo;
import com.meiyunji.sponsored.rpc.adCommon.AllAsinAggregateDataResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllAsinDataResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllAssociationAsinDataResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdSdAsinReportService;
import com.meiyunji.sponsored.service.cpc.service2.ICpcAsinService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAsinReportService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/03
 */
@Service
@Slf4j
public class CpcAsinServiceImpl implements ICpcAsinService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private ICpcAsinReportService cpcAsinReportService;
    @Autowired
    private IAmazonAdSdAsinReportService amazonAdSdAsinReportService;
    @Autowired
    private IAmazonAdAsinReportDao amazonAdAsinReportDao;
    @Autowired
    private IAmazonAdProductReportDao amazonAdProductReportDao;
    @Autowired
    private IAmazonAdSdAsinReportDao asinReportDao;
    @Autowired
    private IAmazonAdSdProductReportDao amazonAdSdProductReportDao;



    @Override
    public AllAsinDataResponse.AsinHomeVo getAllAsinData(Integer puid, AdAsinPageParam param) {
        long t = Instant.now().toEpochMilli();
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        // 分页处理
        Page<AdAsinPageVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());

        //获取不同类型数据 sp、sd
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            getSpAsinVoList(puid, param, voPage, false);
        } else {
            getSdAsinVoList(puid, param, voPage, false);
        }

        AllAsinDataResponse.AsinHomeVo.Builder builder = AllAsinDataResponse.AsinHomeVo.newBuilder();
        AllAsinDataResponse.AsinHomeVo.Page.Builder pageBuilder = AllAsinDataResponse.AsinHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(voPage.getPageNo());
        pageBuilder.setPageSize(voPage.getPageSize());
        pageBuilder.setTotalPage(voPage.getTotalPage());
        pageBuilder.setTotalSize(voPage.getTotalSize());
        List<AdAsinPageVo> rows = voPage.getRows();

        if (CollectionUtils.isNotEmpty(rows)) {
            List<AllAsinDataResponse.AsinHomeVo.Page.AsinHomePageVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                AllAsinDataResponse.AsinHomeVo.Page.AsinHomePageVo.Builder voBuilder = AllAsinDataResponse.AsinHomeVo.Page.AsinHomePageVo.newBuilder();

                voBuilder.setType(item.getType());

                if (StringUtils.isNotBlank(item.getAsin())) {
                    voBuilder.setAsin(item.getAsin());
                }
                if (StringUtils.isNotBlank(item.getSku())) {
                    voBuilder.setSku(item.getSku());
                }
                if (StringUtils.isNotBlank(item.getImgUrl())) {
                    voBuilder.setImgUrl(item.getImgUrl());
                }
                if (StringUtils.isNotBlank(item.getParentAsin())) {
                    voBuilder.setParentAsin(item.getParentAsin());
                }
                if (StringUtils.isNotBlank(item.getPortfolioId())) {
                    voBuilder.setPortfolioId(item.getPortfolioId());
                }
                if (StringUtils.isNotBlank(item.getPortfolioName())) {
                    voBuilder.setPortfolioName(item.getPortfolioName());
                }
                if (StringUtils.isNotBlank(item.getCampaignId())) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (StringUtils.isNotBlank(item.getCampaignName())) {
                    voBuilder.setCampaignName(item.getCampaignName());
                }
                if (StringUtils.isNotBlank(item.getAdGroupId())) {
                    voBuilder.setAdGroupId(item.getAdGroupId());
                }
                if (StringUtils.isNotBlank(item.getAdGroupName())) {
                    voBuilder.setAdGroupName(item.getAdGroupName());
                }
                if (StringUtils.isNotBlank(item.getAdOtherAsinNo1())) {
                    voBuilder.setAdOtherAsinNo1(item.getAdOtherAsinNo1());
                }
                if (StringUtils.isNotBlank(item.getAdOtherAsinNo2())) {
                    voBuilder.setAdOtherAsinNo2(item.getAdOtherAsinNo2());
                }
                if (StringUtils.isNotBlank(item.getAdOtherAsinNo3())) {
                    voBuilder.setAdOtherAsinNo3(item.getAdOtherAsinNo3());
                }
                if (StringUtils.isNotBlank(item.getAdOtherImgUrlNo1())) {
                    voBuilder.setAdOtherImgUrlNo1(item.getAdOtherImgUrlNo1());
                }
                if (StringUtils.isNotBlank(item.getAdOtherImgUrlNo2())) {
                    voBuilder.setAdOtherImgUrlNo2(item.getAdOtherImgUrlNo2());
                }
                if (StringUtils.isNotBlank(item.getAdOtherImgUrlNo3())) {
                    voBuilder.setAdOtherImgUrlNo3(item.getAdOtherImgUrlNo3());
                }

                voBuilder.setOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
                voBuilder.setAdSelfSaleNum(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0));
                voBuilder.setAdOtherSaleNum(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0));
                voBuilder.setAdOtherSaleNumNo1(Optional.ofNullable(item.getAdOtherSaleNumNo1()).orElse(0));
                voBuilder.setAdOtherSaleNumNo2(Optional.ofNullable(item.getAdOtherSaleNumNo2()).orElse(0));
                voBuilder.setAdOtherSaleNumNo3(Optional.ofNullable(item.getAdOtherSaleNumNo3()).orElse(0));
                voBuilder.setPercentage(StringUtils.isNotBlank(String.valueOf(item.getPercentage())) ? String.valueOf(item.getPercentage()) : "0.00");

                return voBuilder.build();

            }).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);


        }

        builder.setPage(pageBuilder.build());
        log.info("已购买Asin页面排序分页花费时间 {}", Instant.now().toEpochMilli() - t);

        return builder.build();
    }


    @Override
    public AllAsinAggregateDataResponse.AsinHomeVo getAllAsinAggregateData(Integer puid, AdAsinPageParam param) {
        long t = Instant.now().toEpochMilli();
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        List<String> campaignIds;
        // 查询的数据为空
        boolean isNull = false;
        if (StringUtils.isNotBlank(param.getPortfolioId()) && !param.getPortfolioId().equals("-1")) {
            campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(),param.getType(), null, null);
            if (CollectionUtils.isEmpty(campaignIds)) {
                isNull = true;
            }
            param.setCampaignIdList(campaignIds);
        }

        List<AdAsinPageVo> voList = Lists.newArrayList();
        if (! isNull) {
            //获取不同类型数据 sp、sd
            if (Constants.SP.equalsIgnoreCase(param.getType())) {
                voList = cpcAsinReportService.getSpAsinPageVoList(puid, param);
            } else {
                voList = amazonAdSdAsinReportService.getSdAsinPageVoList(puid, param);
            }
        }
        AdAsinHomeAggregateDataRpcVo asinHomeAggregateDataRpcVo = getAsinHomeAggregateDataRpcVo(voList);
        AllAsinAggregateDataResponse.AsinHomeVo.Builder builder = AllAsinAggregateDataResponse.AsinHomeVo.newBuilder();
        builder.setAggregateDataVo(asinHomeAggregateDataRpcVo);
        log.info("已购买Asin页面数据汇总花费时间 {}", Instant.now().toEpochMilli() - t);
        return builder.build();
    }


    @Override
    public AllAssociationAsinDataResponse.AsinHomeVo getAllAssociationAsinData(Integer puid, AdAsinPageParam param) {

        long t = Instant.now().toEpochMilli();
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        // 分页处理
        Page<AdAsinPageVo> voPage = new Page<>();
        voPage.setPageNo(1);
        voPage.setPageSize(Constants.DEFLAUTPAGESIZE);

        //获取不同类型数据 sp、sd
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            getSpAsinAssociationVoList(puid, param, voPage, false);
        } else {
            getSdAsinAssociationVoList(puid, param, voPage, false);
        }

        AllAssociationAsinDataResponse.AsinHomeVo.Builder builder = AllAssociationAsinDataResponse.AsinHomeVo.newBuilder();
        AllAssociationAsinDataResponse.AsinHomeVo.Page.Builder pageBuilder = AllAssociationAsinDataResponse.AsinHomeVo.Page.newBuilder();
        AdAsinHomeAggregateDataRpcVo.Builder rpcVoBuilder = AdAsinHomeAggregateDataRpcVo.newBuilder();
        pageBuilder.setPageNo(voPage.getPageNo());
        pageBuilder.setPageSize(voPage.getPageSize());
        pageBuilder.setTotalPage(voPage.getTotalPage());
        pageBuilder.setTotalSize(voPage.getTotalSize());
        List<AdAsinPageVo> rows = voPage.getRows();

        if (CollectionUtils.isNotEmpty(rows)) {
            int sumOrderNum = rows.stream().filter(item -> item != null && item.getAdSelfSaleNum() != null).mapToInt(AdAsinPageVo::getAdSelfSaleNum).sum();
            List<AllAssociationAsinDataResponse.AsinHomeVo.Page.AsinHomePageVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                AllAssociationAsinDataResponse.AsinHomeVo.Page.AsinHomePageVo.Builder voBuilder = AllAssociationAsinDataResponse.AsinHomeVo.Page.AsinHomePageVo.newBuilder();

                voBuilder.setAsin(item.getAsin());
                voBuilder.setType(item.getType());

                if (StringUtils.isNotBlank(item.getSku())) {
                    voBuilder.setSku(item.getSku());
                }
                if (StringUtils.isNotBlank(item.getImgUrl())) {
                    voBuilder.setImgUrl(item.getImgUrl());
                }
                if (StringUtils.isNotBlank(item.getParentAsin())) {
                    voBuilder.setParentAsin(item.getParentAsin());
                }

                voBuilder.setPercentage(String.valueOf(item.getAdSaleNumPercentage(sumOrderNum)));
                voBuilder.setAdSelfSaleNum(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0));
                return voBuilder.build();

            }).collect(Collectors.toList());

            pageBuilder.addAllRows(rpcVos);
            rpcVoBuilder.setOrderNum(sumOrderNum);

        }

        builder.setPage(pageBuilder.build());
        builder.setAggregateDataVo(rpcVoBuilder.build());
        log.info("已购买Asin关联分析页面排序分页花费时间 {}", Instant.now().toEpochMilli() - t);
        return builder.build();
    }

    @Override
    public List<AdAsinPageVo> getSpAsinVoList(Integer puid, AdAsinPageParam param, Page<AdAsinPageVo> voPage, boolean isExport) {

        List<AdAsinPageVo> voList;

        if (StringUtils.isNotBlank(param.getPortfolioId()) && !"-1".equals(param.getPortfolioId())) {
            //广告组合id不为空
            List<String> campaignIds  = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(),param.getType(), null, null);
            // 为空直接返回结果
            if (CollectionUtils.isEmpty(campaignIds)) {
                return new ArrayList<>();
            }
            param.setCampaignIdList(campaignIds);
        }

        if ((StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType()))) {
            voList = cpcAsinReportService.getSpAsinPageVoList(puid, param);
            boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADOrderField(param.getOrderField(), AdAsinPageVo.class);
            if (isSorted) {
                if ("asin".equals(param.getOrderField()) || "sku".equals(param.getOrderField()) || "portfolioName".equals(param.getOrderField()) || "campaignName".equals(param.getOrderField()) || "adGroupName".equals(param.getOrderField())) {
                    PageUtil.sortedByOrderField(voList, param.getOrderField(), param.getOrderType());
                } else {
                    voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
                }
            }
            //分页
            PageUtil.getPage(voPage, voList);
        } else {
            Page page = new Page(voPage.getPageNo(), voPage.getPageSize());
            page = cpcAsinReportService.getSpAsinPageVoPageList(puid, param, page);
            if (page.getTotalSize() > Constants.TOTALSIZELIMIT) {
                int totalPage = Constants.TOTALSIZELIMIT / voPage.getPageSize();
                voPage.setTotalPage(totalPage);
                voPage.setTotalSize(Constants.TOTALSIZELIMIT);
            } else {
                voPage.setTotalPage(page.getTotalPage());
                voPage.setTotalSize(page.getTotalSize());
            }
            voPage.setRows(page.getRows());
        }
        return null;
    }

    @Override
    public List<AdAsinPageVo> getSdAsinVoList(Integer puid, AdAsinPageParam param, Page<AdAsinPageVo> voPage, boolean isExport) {
        List<AdAsinPageVo> voList;

        if (StringUtils.isNotBlank(param.getPortfolioId()) && !"-1".equals(param.getPortfolioId())) {
            //广告组合id不为空
            List<String> campaignIds  = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(),param.getType(), null, null);
            // 为空直接返回结果
            if (CollectionUtils.isEmpty(campaignIds)) {
                return new ArrayList<>();
            }
            param.setCampaignIdList(campaignIds);
        }


        if ((StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType()))) {
            voList = amazonAdSdAsinReportService.getSdAsinPageVoList(puid, param);
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADOrderField(param.getOrderField(), AdAsinPageVo.class);
                if (isSorted) {
                    if ("asin".equals(param.getOrderField()) || "sku".equals(param.getOrderField()) || "portfolioName".equals(param.getOrderField()) || "campaignName".equals(param.getOrderField()) || "adGroupName".equals(param.getOrderField())) {
                        PageUtil.sortedByOrderField(voList, param.getOrderField(), param.getOrderType());
                    } else {
                        voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
                    }
                }
            }
            //分页
            PageUtil.getPage(voPage, voList);
        } else {
            Page page = new Page(voPage.getPageNo(), voPage.getPageSize());
            page = amazonAdSdAsinReportService.getSdAsinPageVoPageList(puid, param, page);
            if (page.getTotalSize() > Constants.TOTALSIZELIMIT) {
                int totalPage = Constants.TOTALSIZELIMIT / voPage.getPageSize();
                voPage.setTotalPage(totalPage);
                voPage.setTotalSize(Constants.TOTALSIZELIMIT);
            } else {
                voPage.setTotalPage(page.getTotalPage());
                voPage.setTotalSize(page.getTotalSize());
            }
            voPage.setRows(page.getRows());
        }

        return null;
    }


    @Override
    public List<AdAsinPageVo> getSpAsinAssociationVoList(Integer puid, AdAsinPageParam param, Page<AdAsinPageVo> voPage, boolean isExport) {

        List<AdAsinPageVo> voList;

        if (StringUtils.isBlank(param.getAsin())) {
            return new ArrayList<>();
        }

        voList = cpcAsinReportService.getSpAsinAssociationPageVoList(puid, param);
        if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
            boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADperformanceOrderField(param.getOrderField());
            if (isSorted) {
                voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
            }
        }
        //分页
        PageUtil.getPage(voPage, voList);
        return null;
    }


    @Override
    public List<AdAsinPageVo> getSdAsinAssociationVoList(Integer puid, AdAsinPageParam param, Page<AdAsinPageVo> voPage, boolean isExport) {
        List<AdAsinPageVo> voList;

        voList = amazonAdSdAsinReportService.getSdAsinAssociationPageVoList(puid, param);
        if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
            boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADperformanceOrderField(param.getOrderField());
            if (isSorted) {
                voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
            }
        }
        //分页
        PageUtil.getPage(voPage, voList);
        return null;
    }

    private AdAsinHomeAggregateDataRpcVo getAsinHomeAggregateDataRpcVo(List<AdAsinPageVo> voList) {
        if (voList.isEmpty()) {
            return AdAsinHomeAggregateDataRpcVo.newBuilder()
                    .setAdSelfSaleNum(0)
                    .setAdOtherSaleNum(0)
                    .setAdSelfSaleNumAsinOne(0)
                    .setAdSelfSaleNumAsinTwo(0)
                    .setAdSelfSaleNumAsinThree(0)
                    .setTotal(0).build();
        }
        return AdAsinHomeAggregateDataRpcVo.newBuilder()
                .setAdSelfSaleNum(Optional.ofNullable(voList.stream().mapToInt(item -> item.getAdSelfSaleNum() == null ? 0 : item.getAdSelfSaleNum()).sum()).orElse(0))
                .setAdOtherSaleNum(Optional.ofNullable(voList.stream().mapToInt(item -> item.getAdOtherSaleNum() == null ? 0 : item.getAdOtherSaleNum()).sum()).orElse(0))
                .setAdSelfSaleNumAsinOne(Optional.ofNullable(voList.stream().mapToInt(item -> item.getAdOtherSaleNumNo1() == null ? 0 : item.getAdOtherSaleNumNo1()).sum()).orElse(0))
                .setAdSelfSaleNumAsinTwo(Optional.ofNullable(voList.stream().mapToInt(item -> item.getAdOtherSaleNumNo2() == null ? 0 : item.getAdOtherSaleNumNo2()).sum()).orElse(0))
                .setAdSelfSaleNumAsinThree(Optional.ofNullable(voList.stream().mapToInt(item -> item.getAdOtherSaleNumNo3() == null ? 0 : item.getAdOtherSaleNumNo3()).sum()).orElse(0))
                .setTotal(voList.size())
                .build();
    }


}
