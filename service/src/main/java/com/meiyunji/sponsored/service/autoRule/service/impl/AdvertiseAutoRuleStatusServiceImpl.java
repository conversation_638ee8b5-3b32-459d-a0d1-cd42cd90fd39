package com.meiyunji.sponsored.service.autoRule.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazon.advertising.mode.Adjustment;
import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sellfox.aadras.api.enumeration.AdvertiseRuleTaskTypePb;
import com.meiyunji.sellfox.aadras.api.enumeration.RuleIndexTypePb;
import com.meiyunji.sellfox.aadras.types.enumeration.AutoRuleScheduleTriggerRuleTaskItemType;
import com.meiyunji.sellfox.aadras.types.enumeration.SpV3ExpressionEnum;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.enums.AmazonAd;
import com.meiyunji.sponsored.common.exception.AutoRuleException;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.autorule.status.CheckOperationVo;
import com.meiyunji.sponsored.service.aadrasGrpcApi.AadrasApiFactory;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.autoRule.dao.*;
import com.meiyunji.sponsored.service.autoRule.enums.*;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleTemplate;
import com.meiyunji.sponsored.service.autoRule.service.AdvertiseAutoRuleStatusService;
import com.meiyunji.sponsored.service.autoRule.vo.*;
import com.meiyunji.sponsored.service.autoRule.vo.rule.*;
import com.meiyunji.sponsored.service.autoRuleTask.api.AutoRuleTaskApi;
import com.meiyunji.sponsored.service.autoRuleTask.api.strategy.AutoRuleTaskAllApi;
import com.meiyunji.sponsored.service.autoRuleTask.dao.AutoRuleTaskDao;
import com.meiyunji.sponsored.service.autoRuleTask.enums.ChildrenItemType;
import com.meiyunji.sponsored.service.autoRuleTask.vo.UpdateAutoRuleResponseVo;
import com.meiyunji.sponsored.service.cpc.constants.BrandMessageConstants;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdProductMetadataService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.ResolvedExpressionParseHelper;
import com.meiyunji.sponsored.service.cpc.vo.AutoRuleAdGroup;
import com.meiyunji.sponsored.service.cpc.vo.AutoRuleQueryKeyword;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.log.enums.MatchTypeEnum;
import com.meiyunji.sponsored.service.log.enums.TargetingTypeEnum;
import com.meiyunji.sponsored.service.product.po.AsinImage;
import com.meiyunji.sponsored.service.product.service.ISyncAsinImageService;
import com.meiyunji.sponsored.service.sellfoxApi.IProductApi;
import com.meiyunji.sponsored.service.strategy.vo.*;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import com.meiyunji.sponsored.service.util.ZoneUtil;
import com.meiyunji.sponsored.service.vo.ProductAdReportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.cpc.util.Constants.ASIN_REGEX;

@SuppressWarnings("AlibabaMethodTooLong")
@Service
@Slf4j
public class AdvertiseAutoRuleStatusServiceImpl implements AdvertiseAutoRuleStatusService {

    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;
    @Autowired
    private IAdvertiseAutoRuleTemplateDao advertiseAutoRuleTemplateDao;
    @Autowired
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;
    @Autowired
    private IAmazonAdTargetingShardingDao amazonAdTargetingShardingDao;
    @Autowired
    private IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private IAmazonAdPortfolioDao amazonAdPortfolioDao;
    @Autowired
    private AdvertiseAutoRuleStatusSequenceDao advertiseAutoRuleStatusSequenceDao;
    @Autowired
    private AadrasApiFactory aadrasApiFactory;
    @Autowired
    private IProductApi productDao;
    @Autowired
    private IAmazonAdProductMetadataService amazonAdProductMetadataService;
    @Autowired
    private IAdvertiseAutoRuleStatusDeleteDao advertiseAutoRuleStatusDeleteDao;
    @Resource
    private ICpcQueryKeywordReportDao iCpcQueryKeywordReportDao;
    @Resource
    private ICpcQueryTargetingReportDao iCpcQueryTargetingReportDao;
    @Resource
    private ICpcSbQueryKeywordReportDao iCpcSbQueryKeywordReportDao;
    @Autowired
    private IAmazonAdGroupReportDao amazonAdGroupReportDao;
    @Autowired
    private IAmazonAdSbGroupReportDao amazonAdSbGroupReportDao;
    @Autowired
    private AutoRuleTaskDao autoRuleTaskDao;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Autowired
    private ISyncAsinImageService syncAsinImageService;
    @Autowired
    private AutoRuleTaskApi autoRuleTaskApi;
    @Autowired
    private IAdvertiseAutoRuleExecuteRecordDao advertiseAutoRuleExecuteRecordDao;
    @Autowired
    private IUserDao userDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private AutoRuleTaskAllApi autoRuleTaskAllApi;

    @Override
    public Result<Page<AmazonAdCampaignAll>> pageCampaignList(AdCampaignAutoRuleParam param) {
        Result<Page<AmazonAdCampaignAll>> result = new Result<>();
        try {

            //广告类型为空说明用户选了广告类型且不支持，那么直接不返回数据
            if (CollectionUtils.isEmpty(param.getAdTypeList())) {
                Page<AmazonAdCampaignAll> page = new Page<>();
                result.setCode(Result.SUCCESS);
                result.setData(page);
                return result;
            }

            List<String> itemIds = advertiseAutoRuleStatusDao.getLisByItemIdList(param.getPuid(), param.getShopId(), param.getTemplateId(), param.getHasSimilarRule(), null);
            List<String> similarRuleItemIdList = new ArrayList<>();
            if (param.getHasSimilarRule() != null) {
                similarRuleItemIdList = advertiseAutoRuleStatusDao.getLisBySimilarRuleItemIdList(param.getPuid(), param.getShopId(), param.getTemplateId(), param.getOperationType());
                if (CollectionUtils.isEmpty(similarRuleItemIdList) && param.getHasSimilarRule() == 1) {
                    Page<AmazonAdCampaignAll> page = new Page<>();
                    result.setCode(Result.SUCCESS);
                    result.setData(page);
                    return result;
                }
            }

            Page<AmazonAdCampaignAll> adCampaignAllPage = amazonAdCampaignAllDao.pageAutoRuleCampaigns(param, itemIds, similarRuleItemIdList);
            result.setCode(Result.SUCCESS);
            result.setData(adCampaignAllPage);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("自动化规则查询活动异常");
            log.error("traceId:{} puid:{} shopId:{} 自动化规则查询活动异常:", param.getTraceId(), param.getPuid(), param.getShopId(), e);
        }
        return result;
    }

    @Override
    public Result<Page<AdKeywordTargetAutoRuleVo>> pageKeywordTargetList(AdKeywordTargetAutoRuleParam param) {
        Result<Page<AdKeywordTargetAutoRuleVo>> result = new Result<>();
        Page<AdKeywordTargetAutoRuleVo> page = new Page<>(param.getPageNo(), param.getPageSize());
        try {
            boolean emptyData = checkParam4EmptyData(param);
            if (emptyData) {
                result.setCode(Result.SUCCESS);
                result.setData(page);
                return result;
            }

            //广告组合
            if (CollectionUtils.isEmpty(param.getCampaignIdList())) {
                if (CollectionUtils.isNotEmpty(param.getPortfolioIds())) {
                    List<String> campaignIdList = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(param.getPuid(),
                            param.getShopId(), param.getPortfolioIds(), Arrays.asList(param.getAdType()));
                    if (CollectionUtils.isNotEmpty(campaignIdList)) {
                        param.setCampaignIdList(campaignIdList);
                    } else {
                        result.setCode(Result.SUCCESS);
                        result.setData(page);
                        return result;
                    }
                }
            }

            //相似规则
            List<String> itemIds = advertiseAutoRuleStatusDao.getLisByItemIdList(param.getPuid(), param.getShopId(), param.getTemplateId(), param.getHasSimilarRule(), null);
            List<String> similarRuleItemIdList = null;
            if (param.getHasSimilarRule() != null) {
                similarRuleItemIdList = advertiseAutoRuleStatusDao.getLisBySimilarRuleItemIdList(param.getPuid(), param.getShopId(), param.getTemplateId(), param.getOperationType());
                if (CollectionUtils.isEmpty(similarRuleItemIdList) && param.getHasSimilarRule() == 1) {
                    result.setCode(Result.SUCCESS);
                    result.setData(page);
                    return result;
                }
            }

            //查询数据:sp(关键词,商品,自动),sb(关键词,商品),sd(商品,受众)
            AutoRuleTargetTypeEnum targetTypeEnum = AutoRuleTargetTypeEnum.map.get(param.getTargetType());
            if (CampaignTypeEnum.sp.getCampaignType().equals(param.getAdType())) {
                switch (targetTypeEnum) {
                    case keywordTarget:
                        pageKeywordList(param, itemIds, similarRuleItemIdList, page);
                        break;
                    case productTarget:
                        pageProductTargetList(param, itemIds, similarRuleItemIdList, page);
                        fillAsinAndBrandAndAudienceInfo(param.getPuid(), param.getMarketplaceId(), page.getRows());
                        break;
                    case autoTarget:
                        pageAutoTargetList(param, itemIds, similarRuleItemIdList, page);
                        break;
                    default:
                        break;
                }
            } else if (CampaignTypeEnum.sb.getCampaignType().equals(param.getAdType())) {
                switch (targetTypeEnum) {
                    case keywordTarget:
                        pageKeywordList4Sb(param, itemIds, similarRuleItemIdList, page);
                        break;
                    case productTarget:
                        pageProductTargetList4Sb(param, itemIds, similarRuleItemIdList, page);
                        fillAsinAndBrandAndAudienceInfo(param.getPuid(), param.getMarketplaceId(), page.getRows());
                        break;
                    default:
                        break;
                }
            } else if (CampaignTypeEnum.sd.getCampaignType().equals(param.getAdType())) {
                pageProductTargetList4Sd(param, itemIds, similarRuleItemIdList, page, targetTypeEnum);
                fillAsinAndBrandAndAudienceInfo(param.getPuid(), param.getMarketplaceId(), page.getRows());
            }

            result.setCode(Result.SUCCESS);
            result.setData(page);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("自动化规则查询投放异常");
            log.error("traceId:{} puid:{} shopId:{} 自动化规则查询投放异常:", param.getTraceId(), param.getPuid(), param.getShopId(), e);
        }
        return result;
    }


    /**
     * 拦截查询，排除用户各种组合条件一定没数据的情况，需要根据adType，matchType，targetType，operationType 4个字段综合判断
     *
     * @param param
     * @return
     */
    private boolean checkParam4EmptyData(AdKeywordTargetAutoRuleParam param) {

        //sp
        if (CampaignTypeEnum.sp.getCampaignType().equals(param.getAdType())) {
            //没有受众投放
            if (AutoRuleTargetTypeEnum.audienceTarget.getTargetType().equals(param.getTargetType())) {
                return true;
            }
            //匹配类型和投放类型不匹配
            if (StringUtils.isNotBlank(param.getMatchType()) && !AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(param.getTargetType())) {
                return true;
            }
            //自动投放不支持添加到否投
            if (Objects.nonNull(param.getOperationType()) && AutoRuleOperationTypeEnum.addNotTarget.getOperationType() == param.getOperationType()) {
                if (AutoRuleTargetTypeEnum.autoTarget.getTargetType().equals(param.getTargetType())) {
                    return true;
                }
            }
        }

        //sb
        if (CampaignTypeEnum.sb.getCampaignType().equals(param.getAdType())) {
            //如果是广告销量，那么不支持sb，还是要查出来展示，不过滤掉
            /*if (CollectionUtils.isNotEmpty(param.getRuleIndexList()) && param.getRuleIndexList().contains(AutoRuleIndexEnum.orderNum.getCode())) {
                return true;
            }*/

            //没有自动投放
            if (AutoRuleTargetTypeEnum.autoTarget.getTargetType().equals(param.getTargetType())) {
                return true;
            }
            //匹配类型和投放类型不匹配
            if (StringUtils.isNotBlank(param.getMatchType()) && !AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(param.getTargetType())) {
                return true;
            }
            //sb没有服务状态
            if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
                return true;
            }
        }

        //sd
        if (CampaignTypeEnum.sd.getCampaignType().equals(param.getAdType())) {
            //没有自动投放和关键词投放
            if (StringUtils.equalsAny(param.getTargetType(), AutoRuleTargetTypeEnum.keywordTarget.getTargetType(), AutoRuleTargetTypeEnum.autoTarget.getTargetType())) {
                return true;
            }
            //没有关键词匹配类型
            if (StringUtils.isNotBlank(param.getMatchType())) {
                return true;
            }
            //受众投放不支持添加到否投
            if (Objects.nonNull(param.getOperationType()) && AutoRuleOperationTypeEnum.addNotTarget.getOperationType() == param.getOperationType()) {
                if (AutoRuleTargetTypeEnum.audienceTarget.getTargetType().equals(param.getTargetType())) {
                    return true;
                }
            }
        }

        return false;
    }

    private void pageAutoTargetList(AdKeywordTargetAutoRuleParam param, List<String> itemIds, List<String> similarItemIdList, Page<AdKeywordTargetAutoRuleVo> page) {
        try {
            Page<AmazonAdTargeting> amazonAdTargetingPage = amazonAdTargetDaoRoutingService.queryAutoRuleAdSpAutoTarget(param, itemIds, similarItemIdList);
            if (Objects.isNull(amazonAdTargetingPage) || CollectionUtils.isEmpty(amazonAdTargetingPage.getRows())) {
                return;
            }

            List<AdKeywordTargetAutoRuleVo> adKeywordTargetAutoRuleVoList = Lists.newArrayList();
            page.setTotalPage(amazonAdTargetingPage.getTotalPage());
            page.setTotalSize(amazonAdTargetingPage.getTotalSize());
            page.setPageNo(amazonAdTargetingPage.getPageNo());
            page.setPageSize(amazonAdTargetingPage.getPageSize());

            amazonAdTargetingPage.getRows().forEach(e -> {
                AdKeywordTargetAutoRuleVo vo = new AdKeywordTargetAutoRuleVo();
                vo.setId(e.getId());
                vo.setPuid(e.getPuid());
                vo.setShopId(e.getShopId());
                vo.setMarketplaceId(e.getMarketplaceId());
                vo.setState(e.getState());
                vo.setKeywordId(e.getTargetId());
                vo.setKeywordText(AutoTargetTypeEnum.getAutoTargetValue(e.getTargetingValue()));
                vo.setCampaignId(e.getCampaignId());
                vo.setAdGroupId(e.getAdGroupId());
                vo.setBiddingValue(e.getBid());
                vo.setServingStatus(e.getServingStatus());
                vo.setTargetType(AutoRuleTargetTypeEnum.autoTarget.getTargetType());
                vo.setAdType(CampaignTypeEnum.sp.getCampaignType());
                vo.setType(QueryPageAdKeywordRespTypeEnum.auto.name());
                if (StringUtils.isNotBlank(e.getServingStatus())) {
                    AmazonAdTargeting.servingStatusEnum byCode = UCommonUtil.getByCode(vo.getServingStatus(), AmazonAdTargeting.servingStatusEnum.class);
                    if (byCode != null) {
                        vo.setServingStatusName(byCode.getName());
                    } else {
                        vo.setServingStatusName(vo.getServingStatus());
                    }
                }
                adKeywordTargetAutoRuleVoList.add(vo);
            });
            page.setRows(adKeywordTargetAutoRuleVoList);

        } catch (Exception e) {
            log.error("traceId:{} puid:{} shopId:{} 自动化规则查询sp自动投放异常:", param.getTraceId(), param.getPuid(), param.getShopId(), e);
        }
    }

    private void pageProductTargetList(AdKeywordTargetAutoRuleParam param, List<String> itemIds, List<String> similarItemIdList, Page<AdKeywordTargetAutoRuleVo> page) {
        try {
            Page<AmazonAdTargeting> amazonAdTargetingPage = amazonAdTargetDaoRoutingService.queryAutoRuleAdSpCommodityTarget(param, itemIds, similarItemIdList);
            if (Objects.isNull(amazonAdTargetingPage) || CollectionUtils.isEmpty(amazonAdTargetingPage.getRows())) {
                return;
            }

            List<AdKeywordTargetAutoRuleVo> adKeywordTargetAutoRuleVoList = Lists.newArrayList();
            page.setTotalPage(amazonAdTargetingPage.getTotalPage());
            page.setTotalSize(amazonAdTargetingPage.getTotalSize());
            page.setPageNo(amazonAdTargetingPage.getPageNo());
            page.setPageSize(amazonAdTargetingPage.getPageSize());
            for (AmazonAdTargeting e : amazonAdTargetingPage.getRows()) {
                AdKeywordTargetAutoRuleVo vo = new AdKeywordTargetAutoRuleVo();
                vo.setId(e.getId());
                vo.setPuid(e.getPuid());
                vo.setShopId(e.getShopId());
                vo.setMarketplaceId(e.getMarketplaceId());
                vo.setState(e.getState());
                vo.setKeywordId(e.getTargetId());
                vo.setKeywordText(e.getTargetingValue());
                vo.setCampaignId(e.getCampaignId());
                vo.setAdGroupId(e.getAdGroupId());
                vo.setBiddingValue(e.getBid());
                vo.setServingStatus(e.getServingStatus());
                vo.setTargetType(AutoRuleTargetTypeEnum.productTarget.getTargetType());
                vo.setAdType(CampaignTypeEnum.sp.getCampaignType());
                //asin,category
                vo.setType(e.getType());
                vo.setResolvedExpression(e.getResolvedExpression());
                if (StringUtils.isNotBlank(e.getServingStatus())) {
                    AmazonAdTargeting.servingStatusEnum byCode = UCommonUtil.getByCode(vo.getServingStatus(), AmazonAdTargeting.servingStatusEnum.class);
                    if (byCode != null) {
                        vo.setServingStatusName(byCode.getName());
                        vo.setServingStatusDec(byCode.getDescription());
                    } else {
                        vo.setServingStatusDec(vo.getServingStatus());
                        vo.setServingStatusName(vo.getServingStatus());
                    }
                }

                adKeywordTargetAutoRuleVoList.add(vo);
            }
            page.setRows(adKeywordTargetAutoRuleVoList);
        } catch (Exception e) {
            log.error("traceId:{} puid:{} shopId:{} 自动化规则查询sp商品投放异常:", param.getTraceId(), param.getPuid(), param.getShopId(), e);
        }
    }

    private void pageKeywordList(AdKeywordTargetAutoRuleParam param, List<String> itemIds, List<String> similarItemIdList, Page<AdKeywordTargetAutoRuleVo> page) {
        try {
            Page<AmazonAdKeyword> amazonAdKeywordPage = amazonAdKeywordDaoRoutingService.pageAutoRuleSpKeyword(param, itemIds, similarItemIdList);
            if (Objects.isNull(amazonAdKeywordPage) || CollectionUtils.isEmpty(amazonAdKeywordPage.getRows())) {
                return;
            }

            List<AdKeywordTargetAutoRuleVo> adKeywordTargetAutoRuleVoList = Lists.newArrayList();
            page.setTotalPage(amazonAdKeywordPage.getTotalPage());
            page.setTotalSize(amazonAdKeywordPage.getTotalSize());
            page.setPageNo(amazonAdKeywordPage.getPageNo());
            page.setPageSize(amazonAdKeywordPage.getPageSize());
            amazonAdKeywordPage.getRows().forEach(e -> {
                AdKeywordTargetAutoRuleVo vo = new AdKeywordTargetAutoRuleVo();
                vo.setId(e.getId());
                vo.setPuid(e.getPuid());
                vo.setShopId(e.getShopId());
                vo.setMarketplaceId(e.getMarketplaceId());
                vo.setState(e.getState());
                vo.setKeywordId(e.getKeywordId());
                vo.setKeywordText(e.getKeywordText());
                vo.setCampaignId(e.getCampaignId());
                vo.setAdGroupId(e.getAdGroupId());
                vo.setBiddingValue(e.getBid());
                vo.setMatchType(e.getMatchType());
                vo.setServingStatus(e.getServingStatus());
                vo.setTargetType(AutoRuleTargetTypeEnum.keywordTarget.getTargetType());
                vo.setAdType(CampaignTypeEnum.sp.getCampaignType());
                vo.setType(QueryPageAdKeywordRespTypeEnum.keyword.name());
                if (StringUtils.isNotBlank(e.getServingStatus())) {
                    AmazonAdKeyword.servingStatusEnum byCode = UCommonUtil.getByCode(vo.getServingStatus(), AmazonAdKeyword.servingStatusEnum.class);
                    if (byCode != null) {
                        vo.setServingStatusName(byCode.getName());
                        vo.setServingStatusDec(byCode.getDescription());
                    } else {
                        vo.setServingStatusDec(vo.getServingStatus());
                        vo.setServingStatusName(vo.getServingStatus());
                    }
                }
                adKeywordTargetAutoRuleVoList.add(vo);
            });
            page.setRows(adKeywordTargetAutoRuleVoList);
        } catch (Exception e) {
            log.error("traceId:{} puid:{} shopId:{} 自动化规则查询sp关键词异常:", param.getTraceId(), param.getPuid(), param.getShopId(), e);
        }
    }


    private void pageKeywordList4Sb(AdKeywordTargetAutoRuleParam param, List<String> itemIds, List<String> similarItemIdList, Page<AdKeywordTargetAutoRuleVo> page) {
        try {
            Page<AmazonSbAdKeyword> amazonAdKeywordPage = amazonSbAdKeywordDao.pageAutoRuleSbKeyword(param, itemIds, similarItemIdList);
            if (Objects.isNull(amazonAdKeywordPage) || CollectionUtils.isEmpty(amazonAdKeywordPage.getRows())) {
                return;
            }

            List<AdKeywordTargetAutoRuleVo> adKeywordTargetAutoRuleVoList = Lists.newArrayList();
            page.setTotalPage(amazonAdKeywordPage.getTotalPage());
            page.setTotalSize(amazonAdKeywordPage.getTotalSize());
            page.setPageNo(amazonAdKeywordPage.getPageNo());
            page.setPageSize(amazonAdKeywordPage.getPageSize());
            amazonAdKeywordPage.getRows().forEach(e -> {
                AdKeywordTargetAutoRuleVo vo = new AdKeywordTargetAutoRuleVo();
                vo.setId(e.getId());
                vo.setPuid(e.getPuid());
                vo.setShopId(e.getShopId());
                vo.setMarketplaceId(e.getMarketplaceId());
                vo.setState(e.getState());
                vo.setKeywordId(e.getKeywordId());
                vo.setKeywordText(e.getKeywordText());
                vo.setCampaignId(e.getCampaignId());
                vo.setAdGroupId(e.getAdGroupId());
                vo.setBiddingValue(Objects.isNull(e.getBid()) ? null : e.getBid().doubleValue());
                vo.setMatchType(e.getMatchType());
                vo.setTargetType(AutoRuleTargetTypeEnum.keywordTarget.getTargetType());
                vo.setAdType(CampaignTypeEnum.sb.getCampaignType());
                vo.setType(QueryPageAdKeywordRespTypeEnum.keyword.name());
                adKeywordTargetAutoRuleVoList.add(vo);
            });
            page.setRows(adKeywordTargetAutoRuleVoList);
        } catch (Exception e) {
            log.error("traceId:{} puid:{} shopId:{} 自动化规则查询sb关键词异常:", param.getTraceId(), param.getPuid(), param.getShopId(), e);
        }
    }


    private void pageProductTargetList4Sb(AdKeywordTargetAutoRuleParam param, List<String> itemIds, List<String> similarItemIdList, Page<AdKeywordTargetAutoRuleVo> page) {
        try {
            Page<AmazonSbAdTargeting> amazonAdTargetingPage = amazonSbAdTargetingDao.queryAutoRuleAdSbTarget(param, itemIds, similarItemIdList);
            if (Objects.isNull(amazonAdTargetingPage) || CollectionUtils.isEmpty(amazonAdTargetingPage.getRows())) {
                return;
            }

            List<AdKeywordTargetAutoRuleVo> adKeywordTargetAutoRuleVoList = Lists.newArrayList();
            page.setTotalPage(amazonAdTargetingPage.getTotalPage());
            page.setTotalSize(amazonAdTargetingPage.getTotalSize());
            page.setPageNo(amazonAdTargetingPage.getPageNo());
            page.setPageSize(amazonAdTargetingPage.getPageSize());
            amazonAdTargetingPage.getRows().forEach(e -> {
                AdKeywordTargetAutoRuleVo vo = new AdKeywordTargetAutoRuleVo();
                vo.setId(e.getId());
                vo.setPuid(e.getPuid());
                vo.setShopId(e.getShopId());
                vo.setMarketplaceId(e.getMarketplaceId());
                vo.setState(e.getState());
                vo.setKeywordId(e.getTargetId());
                vo.setKeywordText(e.getTargetText());
                vo.setCampaignId(e.getCampaignId());
                vo.setAdGroupId(e.getAdGroupId());
                vo.setBiddingValue(Objects.isNull(e.getBid()) ? null : e.getBid().doubleValue());
                vo.setTargetType(AutoRuleTargetTypeEnum.productTarget.getTargetType());
                vo.setAdType(CampaignTypeEnum.sb.getCampaignType());
                vo.setType(e.getType());
                vo.setResolvedExpression(e.getResolvedExpression());
                adKeywordTargetAutoRuleVoList.add(vo);
            });
            page.setRows(adKeywordTargetAutoRuleVoList);
        } catch (Exception e) {
            log.error("traceId:{} puid:{} shopId:{} 自动化规则查询sb商品投放异常:", param.getTraceId(), param.getPuid(), param.getShopId(), e);
        }
    }


    private void pageProductTargetList4Sd(AdKeywordTargetAutoRuleParam param, List<String> itemIds, List<String> similarItemIdList, Page<AdKeywordTargetAutoRuleVo> page, AutoRuleTargetTypeEnum targetTypeEnum) {
        try {
            Page<AmazonSdAdTargeting> amazonAdTargetingPage = amazonSdAdTargetingDao.queryAutoRuleAdSdTarget(param, itemIds, similarItemIdList, targetTypeEnum);
            if (Objects.isNull(amazonAdTargetingPage) || CollectionUtils.isEmpty(amazonAdTargetingPage.getRows())) {
                return;
            }

            List<AdKeywordTargetAutoRuleVo> adKeywordTargetAutoRuleVoList = Lists.newArrayList();
            page.setTotalPage(amazonAdTargetingPage.getTotalPage());
            page.setTotalSize(amazonAdTargetingPage.getTotalSize());
            page.setPageNo(amazonAdTargetingPage.getPageNo());
            page.setPageSize(amazonAdTargetingPage.getPageSize());
            amazonAdTargetingPage.getRows().forEach(e -> {
                AdKeywordTargetAutoRuleVo vo = new AdKeywordTargetAutoRuleVo();
                vo.setId(e.getId());
                vo.setPuid(e.getPuid());
                vo.setShopId(e.getShopId());
                vo.setMarketplaceId(e.getMarketplaceId());
                vo.setState(e.getState());
                vo.setKeywordId(e.getTargetId());
                vo.setKeywordText(e.getTargetText());
                vo.setCampaignId(e.getCampaignId());
                vo.setAdGroupId(e.getAdGroupId());
                vo.setBiddingValue(Objects.isNull(e.getBid()) ? null : e.getBid().doubleValue());
                vo.setServingStatus(e.getServingStatus());
                vo.setTargetType(targetTypeEnum.getTargetType());
                vo.setAdType(CampaignTypeEnum.sd.getCampaignType());
                vo.setType(e.getType());
                vo.setResolvedExpression(e.getResolvedExpression());
                //投放类型，用于解析title
                vo.setDetailTargetType(e.getTargetType());
                if (StringUtils.isNotBlank(e.getServingStatus())) {
                    AmazonSdAdTargeting.servingStatusEnum byCode = UCommonUtil.getByCode(vo.getServingStatus(), AmazonSdAdTargeting.servingStatusEnum.class);
                    if (byCode != null) {
                        vo.setServingStatusName(byCode.getName());
                        vo.setServingStatusDec(byCode.getDescription());
                    } else {
                        vo.setServingStatusDec(vo.getServingStatus());
                        vo.setServingStatusName(vo.getServingStatus());
                    }
                }
                adKeywordTargetAutoRuleVoList.add(vo);
            });
            page.setRows(adKeywordTargetAutoRuleVoList);
        } catch (Exception e) {
            log.error("traceId:{} puid:{} shopId:{} 自动化规则查询sd投放异常:", param.getTraceId(), param.getPuid(), param.getShopId(), e);
        }
    }

    /**
     * 添加受控对象时填充asin标题，品牌，受众信息
     *
     * @param puid
     * @param marketplaceId
     * @param voList
     */
    private void fillAsinAndBrandAndAudienceInfo(Integer puid, String marketplaceId, List<AdKeywordTargetAutoRuleVo> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }

        //收集asin填充图片标题
        Map<String, AsinImage> asinMap = new HashMap<>();
        Set<String> asins = voList.stream()
                .filter(x -> TargetingTypeEnum.asin.getTagetType().equals(x.getType()) && StringUtils.isAnyBlank(x.getTitle(), x.getImgUrl()))
                .map(x -> x.getKeywordText()).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(asins)) {
            List<AsinImage> listByAsins = syncAsinImageService.getListByAsinsNoSave(puid, marketplaceId, new ArrayList<>(asins));
            asinMap = listByAsins.stream().filter(e -> StringUtils.isNotBlank(e.getAsin())).collect(Collectors.toMap(e -> e.getAsin().toUpperCase(), e1 -> e1, (e2, e3) -> e3));
        }

        //遍历处理
        for (AdKeywordTargetAutoRuleVo e : voList) {
            e.setAsin(e.getKeywordText());
            //asin信息
            if (TargetingTypeEnum.asin.getTagetType().equals(e.getType())) {
                if (StringUtils.isAnyBlank(e.getTitle(), e.getImgUrl())) {
                    AsinImage asinImage = asinMap.get(e.getKeywordText());
                    if (StringUtils.isBlank(e.getTitle()) && Objects.nonNull(asinImage) && StringUtils.isNotBlank(asinImage.getTitle())) {
                        e.setTitle(asinImage.getTitle());
                    }
                    if (StringUtils.isBlank(e.getImgUrl()) && Objects.nonNull(asinImage) && StringUtils.isNotBlank(asinImage.getImage())) {
                        e.setImgUrl(asinImage.getImage());
                    }
                }
            } else {
                //如果为数字ID,表明类目或品牌已经被amazon删除
                if (StringUtils.isNumeric(e.getKeywordText())) {
                    e.setKeywordText("此类目亚马逊已删除");
                }

                //类目和受众，从resolvedExpression中解析
                if (StringUtils.isNotBlank(e.getResolvedExpression())) {
                    fillBrandMessage(e, e.getResolvedExpression());
                } else {
                    if (TargetingTypeEnum.category_target.getTagetType().equals(e.getType())) {
                        e.setCategory(e.getKeywordText());
                    }
                }


                //受众类型title
                if (StringUtils.isBlank(e.getTitle())) {
                    SBTargetingAudienceTypeEnum detailTargetType = SBTargetingAudienceTypeEnum.fromValue(e.getDetailTargetType());
                    e.setTitle(detailTargetType != null ? detailTargetType.getDesc() : null);
                }

            }
        }
    }

    private void fillBrandMessage(AdvertiseAutoRuleStatus vo, JSONArray jsonArray) {
        if (jsonArray == null || jsonArray.isEmpty()) return;
        try {
            if (CollectionUtils.isEmpty(jsonArray)) {
                return;
            }
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String value = jsonObject.getString("type");
                // T00030 TargetingPredicateNested
                if (SBTargetingAudienceTypeEnum.fromValue(value) != null) {
                    JSONArray valueArray = ResolvedExpressionParseHelper.getJsonValueAsArray(jsonObject);
                    fillBrandMessage(vo, valueArray);
                }
                if (ExpressionEnum.asinCategorySameAs.value().equals(value) || SpV3ExpressionEnum.asinCategorySameAs.getValueV3().equals(value)) {
                    vo.setCategory(jsonObject.getString("value"));
                    //如果为数字ID,表明类目或品牌已经被amazon删除
                    if (StringUtils.isNumeric(vo.getCategory())) {
                        vo.setCategory("此类目亚马逊已删除");
                    }
                }
                if (ExpressionEnum.asinBrandSameAs.value().equals(value) || SpV3ExpressionEnum.asinBrandSameAs.getValueV3().equals(value)) {
                    vo.setBrandName(jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinPriceBetween.value().equals(value) || SpV3ExpressionEnum.asinPriceBetween.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value").replace("-", ","));
                }
                if (ExpressionEnum.asinPriceLessThan.value().equals(value) || SpV3ExpressionEnum.asinPriceLessThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及以下");
                }
                if (ExpressionEnum.asinPriceGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinPriceGreaterThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及更高");
                }
                if (ExpressionEnum.asinReviewRatingBetween.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingBetween.getValueV3().equals(value)) {
                    vo.setRating(jsonObject.getString("value").replace('-', '~'));
                }
                if (ExpressionEnum.asinReviewRatingLessThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingLessThan.getValueV3().equals(value)) {
                    vo.setRating("1~" + jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinReviewRatingGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingGreaterThan.getValueV3().equals(value)) {
                    vo.setRating(jsonObject.getString("value") + "~5");
                }
                if (ExpressionEnum.asinIsPrimeShippingEligible.value().equals(value) || SpV3ExpressionEnum.asinIsPrimeShippingEligible.getValueV3().equals(value)) {
                    if ("true".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("具有Prime资格");
                    } else if ("false".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("不具有Prime资格");
                    }
                }
                if (ExpressionEnum.lookback.value().equals(value)) {
                    vo.setLookback(jsonObject.getString("value"));
                }

                if (SdTargetTypeNewEnum.CONTENT_CATEGORY.getCode().equals(value)) {
                    vo.setTitle("");
                }
            }

            String brandName = StringUtils.isNotBlank(vo.getBrandName()) ? vo.getBrandName() : BrandMessageConstants.DEFAULT_BRAND_NAME;
            String commodityPriceRange = StringUtils.isNotBlank(vo.getCommodityPriceRange()) ? vo.getCommodityPriceRange() : BrandMessageConstants.DEFAULT_COMMODITY_PRICE_RANGE;
            String rating = StringUtils.isNotBlank(vo.getRating()) ? vo.getRating() : BrandMessageConstants.DEFAULT_RATING;
            String distribution = StringUtils.isNotBlank(vo.getDistribution()) ? vo.getDistribution() : BrandMessageConstants.DEFAULT_DISTRIBUTION;

            vo.setBrandName(brandName);
            vo.setCommodityPriceRange(commodityPriceRange);
            vo.setRating(rating);
            vo.setDistribution(distribution);


        } catch (Exception e) {
            log.error("解析json失败", e);
        }
    }


    private void fillBrandMessage(AdKeywordTargetAutoRuleVo vo, String resolvedExpression) {
        try {
            JSONArray jsonArray = JSONObject.parseArray(resolvedExpression);
            if (CollectionUtils.isEmpty(jsonArray)) {
                return;
            }
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String value = jsonObject.getString("type");
                // T00030 TargetingPredicateNested
                if (SBTargetingAudienceTypeEnum.fromValue(value) != null) {
                    JSONArray valueArray = ResolvedExpressionParseHelper.getJsonValueAsArray(jsonObject);
                    fillBrandMessage(vo, valueArray.toJSONString());
                }
                if (ExpressionEnum.asinCategorySameAs.value().equals(value) || SpV3ExpressionEnum.asinCategorySameAs.getValueV3().equals(value)) {
                    vo.setCategory(jsonObject.getString("value"));
                    //如果为数字ID,表明类目或品牌已经被amazon删除
                    if (StringUtils.isNumeric(vo.getCategory())) {
                        vo.setCategory("此类目亚马逊已删除");
                    }
                }
                if (ExpressionEnum.asinBrandSameAs.value().equals(value) || SpV3ExpressionEnum.asinBrandSameAs.getValueV3().equals(value)) {
                    vo.setBrandName(jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinPriceBetween.value().equals(value) || SpV3ExpressionEnum.asinPriceBetween.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value").replace("-", ","));
                }
                if (ExpressionEnum.asinPriceLessThan.value().equals(value) || SpV3ExpressionEnum.asinPriceLessThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及以下");
                }
                if (ExpressionEnum.asinPriceGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinPriceGreaterThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及更高");
                }
                if (ExpressionEnum.asinReviewRatingBetween.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingBetween.getValueV3().equals(value)) {
                    vo.setRating(jsonObject.getString("value").replace('-', '~'));
                }
                if (ExpressionEnum.asinReviewRatingLessThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingLessThan.getValueV3().equals(value)) {
                    vo.setRating("1~" + jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinReviewRatingGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingGreaterThan.getValueV3().equals(value)) {
                    vo.setRating(jsonObject.getString("value") + "~5");
                }
                if (ExpressionEnum.asinIsPrimeShippingEligible.value().equals(value) || SpV3ExpressionEnum.asinIsPrimeShippingEligible.getValueV3().equals(value)) {
                    if ("true".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("具有Prime资格");
                    } else if ("false".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("不具有Prime资格");
                    }
                }
                if (ExpressionEnum.lookback.value().equals(value)) {
                    vo.setLookback(jsonObject.getString("value"));
                }

                if (SdTargetTypeNewEnum.CONTENT_CATEGORY.getCode().equals(value)) {
                    vo.setTitle("");
                }
            }

            String brandName = StringUtils.isNotBlank(vo.getBrandName()) ? vo.getBrandName() : BrandMessageConstants.DEFAULT_BRAND_NAME;
            String commodityPriceRange = StringUtils.isNotBlank(vo.getCommodityPriceRange()) ? vo.getCommodityPriceRange() : BrandMessageConstants.DEFAULT_COMMODITY_PRICE_RANGE;
            String rating = StringUtils.isNotBlank(vo.getRating()) ? vo.getRating() : BrandMessageConstants.DEFAULT_RATING;
            String distribution = StringUtils.isNotBlank(vo.getDistribution()) ? vo.getDistribution() : BrandMessageConstants.DEFAULT_DISTRIBUTION;

            vo.setBrandName(brandName);
            vo.setCommodityPriceRange(commodityPriceRange);
            vo.setRating(rating);
            vo.setDistribution(distribution);


        } catch (Exception e) {
            log.error("解析json失败", e);
        }
    }


    @Override
    public Result<Page<AutoRuleAdGroup>> pageAdGroupList(AdGroupAutoRuleParam adGroupAutoRuleParam) {
        Result<Page<AutoRuleAdGroup>> result = new Result<>();
        Page<AutoRuleAdGroup> page = new Page<>();
        page.setPageNo(adGroupAutoRuleParam.getPageNo());
        page.setPageSize(adGroupAutoRuleParam.getPageSize());

        //广告类型为空说明用户选了广告类型且不支持，那么直接不返回数据
        if (CollectionUtils.isEmpty(adGroupAutoRuleParam.getAdTypes())) {
            Page<AutoRuleAdGroup> autoRuleAdGroupPage = new Page<>();
            result.setCode(Result.SUCCESS);
            result.setData(autoRuleAdGroupPage);
            return result;
        }

        try {
            //查询组合id对应的活动id
            if (CollectionUtils.isEmpty(adGroupAutoRuleParam.getCampaignIdList())) {
                if (CollectionUtils.isNotEmpty(adGroupAutoRuleParam.getPortfolioIds())) {
                    //该方法可以通过传入的adType查询sp、sb、sd的
                    List<String> campaignIdList = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(adGroupAutoRuleParam.getPuid(),
                            adGroupAutoRuleParam.getShopId(), adGroupAutoRuleParam.getPortfolioIds(), adGroupAutoRuleParam.getAdTypes());
                    if (CollectionUtils.isNotEmpty(campaignIdList)) {
                        adGroupAutoRuleParam.setCampaignIdList(campaignIdList);
                    } else {
                        Page<AutoRuleAdGroup> autoRuleAdGroupPage = new Page<>();
                        result.setCode(Result.SUCCESS);
                        result.setData(autoRuleAdGroupPage);
                        return result;
                    }
                }
            }

            //相似规则处理
            List<AutoRuleAdGroup> autoRuleAdGroups = Lists.newArrayList();
            List<String> itemIds = advertiseAutoRuleStatusDao.getLisByItemIdList(adGroupAutoRuleParam.getPuid(), adGroupAutoRuleParam.getShopId(), adGroupAutoRuleParam.getTemplateId(), adGroupAutoRuleParam.getHasSimilarRule(), null);
            List<String> similarRuleItemIdList = null;
            if (adGroupAutoRuleParam.getHasSimilarRule() != null) {
                similarRuleItemIdList = advertiseAutoRuleStatusDao.getLisBySimilarRuleItemIdList(adGroupAutoRuleParam.getPuid(), adGroupAutoRuleParam.getShopId(), adGroupAutoRuleParam.getTemplateId(), adGroupAutoRuleParam.getOperationType());
                if (CollectionUtils.isEmpty(similarRuleItemIdList) && adGroupAutoRuleParam.getHasSimilarRule() == 1) {
                    Page<AutoRuleAdGroup> autoRuleAdGroupPage = new Page<>();
                    result.setCode(Result.SUCCESS);
                    result.setData(autoRuleAdGroupPage);
                    return result;
                }
            }

            //查询广告组
            Page<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.listByAutoRule(adGroupAutoRuleParam, itemIds, similarRuleItemIdList);
            //sb没有默认竞价，取得组下随机一个投放的竞价值作为默认原始竞价返回
            List<String> sbKeywordGroupIdList = new ArrayList<>();
            List<String> sbTargetGroupIdList = new ArrayList<>();
            amazonAdGroups.getRows().forEach(x -> {
                if (CampaignTypeEnum.sb.getCampaignType().equals(x.getAdType())) {
                    if (SbAdGroupTypeEnum.product.getAdGroupType().equals(x.getAdGroupType())) {
                        sbTargetGroupIdList.add(x.getAdGroupId());
                    } else if (SbAdGroupTypeEnum.keyword.getAdGroupType().equals(x.getAdGroupType())) {
                        sbKeywordGroupIdList.add(x.getAdGroupId());
                    }
                }
            });
            //随机查询一个投放
            Map<String, BigDecimal> keywordGroupBidMap = new HashMap<>();
            Map<String, BigDecimal> targetGroupBidMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(sbKeywordGroupIdList)) {
                List<SbRandomTargetBid> keywordGroupBidList = amazonSbAdKeywordDao.listByGroupIdListRandomOne(adGroupAutoRuleParam.getPuid(), adGroupAutoRuleParam.getShopId(), sbKeywordGroupIdList);
                if (CollectionUtils.isNotEmpty(keywordGroupBidList)) {
                    keywordGroupBidMap = keywordGroupBidList.stream().collect(Collectors.toMap(SbRandomTargetBid::getAdGroupId, SbRandomTargetBid::getBid, (e1, e2) -> e2));
                }
            }
            if (CollectionUtils.isNotEmpty(sbTargetGroupIdList)) {
                List<SbRandomTargetBid> targetGroupBidList = amazonSbAdTargetingDao.listByGroupIdListRandomOne(adGroupAutoRuleParam.getPuid(), adGroupAutoRuleParam.getShopId(), sbTargetGroupIdList);
                if (CollectionUtils.isNotEmpty(targetGroupBidList)) {
                    targetGroupBidMap = targetGroupBidList.stream().collect(Collectors.toMap(SbRandomTargetBid::getAdGroupId, SbRandomTargetBid::getBid, (e1, e2) -> e2));
                }
            }

            for (AmazonAdGroup e : amazonAdGroups.getRows()) {
                AutoRuleAdGroup autoRuleAdGroup = new AutoRuleAdGroup();
                autoRuleAdGroup.setId(e.getId());
                autoRuleAdGroup.setCampaignId(e.getCampaignId());
                autoRuleAdGroup.setAdGroupId(e.getAdGroupId());
                autoRuleAdGroup.setAdGroupType(e.getAdGroupType());
                autoRuleAdGroup.setPuid(adGroupAutoRuleParam.getPuid());
                autoRuleAdGroup.setShopId(adGroupAutoRuleParam.getShopId());
                autoRuleAdGroup.setState(e.getState().toLowerCase(Locale.ROOT));
                if (CampaignTypeEnum.sb.getCampaignType().equals(e.getAdType())) {
                    BigDecimal bid = null;
                    if (SbAdGroupTypeEnum.product.getAdGroupType().equals(e.getAdGroupType())) {
                        bid = targetGroupBidMap.get(e.getAdGroupId());
                    } else if (SbAdGroupTypeEnum.keyword.getAdGroupType().equals(e.getAdGroupType())) {
                        bid = keywordGroupBidMap.get(e.getAdGroupId());
                    }
                    if (Objects.nonNull(bid)) {
                        autoRuleAdGroup.setDefaultBid(bid.doubleValue());
                    }
                    Optional.ofNullable(e.getAdFormat()).filter(StringUtils::isNotEmpty).ifPresent(autoRuleAdGroup::setAdFormat);
                } else {
                    autoRuleAdGroup.setDefaultBid(e.getDefaultBid());
                }
                autoRuleAdGroup.setName(e.getName());
                autoRuleAdGroup.setMarketplaceId(e.getMarketplaceId());
                autoRuleAdGroup.setAdType(e.getAdType());
                if (StringUtils.isNotBlank(e.getServingStatus())) {
                    if (CampaignTypeEnum.sp.getCampaignType().equals(e.getAdType())) {
                        AmazonAdGroup.servingStatusEnum byCode = UCommonUtil.getByCode(e.getServingStatus(), AmazonAdGroup.servingStatusEnum.class);
                        if (byCode != null) {
                            autoRuleAdGroup.setServingStatusName(byCode.getName());
                            autoRuleAdGroup.setServingStatusDec(byCode.getDescription());
                        } else {
                            autoRuleAdGroup.setServingStatusDec(e.getServingStatus());
                            autoRuleAdGroup.setServingStatusName(e.getServingStatus());
                        }
                    } else if (CampaignTypeEnum.sb.getCampaignType().equals(e.getAdType())) {
                        AmazonSbAdGroup.servingStatusEnum byCode = UCommonUtil.getByCode(e.getServingStatus(), AmazonSbAdGroup.servingStatusEnum.class);
                        if (byCode != null) {
                            autoRuleAdGroup.setServingStatusName(byCode.getName());
                            autoRuleAdGroup.setServingStatusDec(byCode.getDescription());
                        } else {
                            autoRuleAdGroup.setServingStatusDec(e.getServingStatus());
                            autoRuleAdGroup.setServingStatusName(e.getServingStatus());
                        }
                    } else if (CampaignTypeEnum.sd.getCampaignType().equals(e.getAdType())) {
                        AmazonSdAdGroup.servingStatusEnum byCode = UCommonUtil.getByCode(e.getServingStatus(), AmazonSdAdGroup.servingStatusEnum.class);
                        if (byCode != null) {
                            autoRuleAdGroup.setServingStatusName(byCode.getName());
                            autoRuleAdGroup.setServingStatusDec(byCode.getDescription());
                        } else {
                            autoRuleAdGroup.setServingStatusDec(e.getServingStatus());
                            autoRuleAdGroup.setServingStatusName(e.getServingStatus());
                        }
                    }
                }
                autoRuleAdGroups.add(autoRuleAdGroup);
            }
            Page<AutoRuleAdGroup> autoRuleAdGroupPages = new Page<>();
            autoRuleAdGroupPages.setPageNo(amazonAdGroups.getPageNo());
            autoRuleAdGroupPages.setPageSize(amazonAdGroups.getPageSize());
            autoRuleAdGroupPages.setRows(autoRuleAdGroups);
            autoRuleAdGroupPages.setTotalPage(amazonAdGroups.getTotalPage());
            autoRuleAdGroupPages.setTotalSize(amazonAdGroups.getTotalSize());
            result.setCode(Result.SUCCESS);
            result.setData(autoRuleAdGroupPages);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("自动化规则查询广告组异常");
            log.error("traceId:{} puid:{} shopId:{} 自动化规则查询广告组异常:", adGroupAutoRuleParam.getTraceId(), adGroupAutoRuleParam.getPuid(), adGroupAutoRuleParam.getShopId(), e);
        }
        return result;
    }

    @Override
    public Result<Page<AutoRuleQueryKeyword>> pageAdQueryKeywordList(AdQueryKeywordAutoRuleParam adQueryKeywordAutoRuleParam) {
        Result<Page<AutoRuleQueryKeyword>> result = new Result<>();
        try {
            if (CollectionUtils.isNotEmpty(adQueryKeywordAutoRuleParam.getPortfolioIds())) {
                List<String> campaignIdList = amazonAdCampaignAllDao.getCampaignIdListByPortfolioId(adQueryKeywordAutoRuleParam.getPuid(),
                        adQueryKeywordAutoRuleParam.getShopId(), adQueryKeywordAutoRuleParam.getPortfolioIds(), adQueryKeywordAutoRuleParam.getAdType());
                if (CollectionUtils.isNotEmpty(campaignIdList)) {
                    if (CollectionUtils.isNotEmpty(adQueryKeywordAutoRuleParam.getCampaignIdList())) {
                        // 和通过广告组合查询出来的活动列表取交集
                        List<String> campaignIdIntersectionList = campaignIdList.stream()
                                .filter(adQueryKeywordAutoRuleParam.getCampaignIdList()::contains)
                                .collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(campaignIdIntersectionList)) {
                            Page<AutoRuleQueryKeyword> autoRuleAdGroupPage = new Page<>();
                            result.setCode(Result.SUCCESS);
                            result.setData(autoRuleAdGroupPage);
                            return result;
                        }
                        adQueryKeywordAutoRuleParam.setCampaignIdList(campaignIdIntersectionList);
                    } else {
                        adQueryKeywordAutoRuleParam.setCampaignIdList(campaignIdList);
                    }
                } else {
                    Page<AutoRuleQueryKeyword> autoRuleAdGroupPage = new Page<>();
                    result.setCode(Result.SUCCESS);
                    result.setData(autoRuleAdGroupPage);
                    return result;
                }
            }
            List<String> itemIdSimilarRuleList = new ArrayList<>();
            if (StringUtils.isNotBlank(adQueryKeywordAutoRuleParam.getHasSimilarRule())) {
                itemIdSimilarRuleList = advertiseAutoRuleStatusDao.getItemIdListBySimilarRule(adQueryKeywordAutoRuleParam.getPuid(), adQueryKeywordAutoRuleParam.getShopId(), adQueryKeywordAutoRuleParam.getOperationType(), AmazonAd.HasSimilarRuleEnum.YES.getHasSimilarRule());
                if (Integer.parseInt(adQueryKeywordAutoRuleParam.getHasSimilarRule()) == AmazonAd.HasSimilarRuleEnum.YES.getHasSimilarRule() && CollectionUtils.isEmpty(itemIdSimilarRuleList)) {
                    Page<AutoRuleQueryKeyword> autoRuleAdGroupPage = new Page<>();
                    result.setCode(Result.SUCCESS);
                    result.setData(autoRuleAdGroupPage);
                    return result;
                }
                adQueryKeywordAutoRuleParam.setQueryIdList(itemIdSimilarRuleList);
            }
            List<AutoRuleQueryKeyword> autoRuleQueryKeywords = Lists.newArrayList();
            List<String> itemIds = advertiseAutoRuleStatusDao.getListByItemIdList(adQueryKeywordAutoRuleParam.getPuid(), adQueryKeywordAutoRuleParam.getShopId(), adQueryKeywordAutoRuleParam.getTemplateId());
            if (StringUtils.isNotBlank(adQueryKeywordAutoRuleParam.getHasSimilarRule()) && Integer.parseInt(adQueryKeywordAutoRuleParam.getHasSimilarRule()) == AmazonAd.HasSimilarRuleEnum.NO.getHasSimilarRule().intValue()) {
                // 合并queryIdList
                itemIds.addAll(itemIdSimilarRuleList);
            }
            // 当操作状态为添加到投放并且目标广告组为指定广告组时，去掉自动投放的广告组
            if (AmazonAd.AutoRuleOperationTypeEnum.addTarget.getOperationType().intValue() == adQueryKeywordAutoRuleParam.getOperationType().intValue()) {
                AdvertiseAutoRuleTemplate template = advertiseAutoRuleTemplateDao.selectByPrimaryKey(adQueryKeywordAutoRuleParam.getPuid(), adQueryKeywordAutoRuleParam.getTemplateId());
                List<PerformOperationJson> performOperationJsonList = JSONUtil.jsonToArray(template.getPerformOperation(), PerformOperationJson.class);
                if (CollectionUtils.isNotEmpty(performOperationJsonList) && PerformOperationJson.AppointAdGroupTypeEnum.QUERY_KEYWORD_AD_GROUP.getValue().equals(performOperationJsonList.get(0).getAppointAdGroupType())) {
                    List<String> notInAdGroupIdList = amazonAdGroupDao.getGroupIdsByGroupType(adQueryKeywordAutoRuleParam.getPuid(), adQueryKeywordAutoRuleParam.getShopId(), AmazonAd.AdGroupTypeEnum.AUTO.getType());
                    adQueryKeywordAutoRuleParam.setNotInAdGroupIdList(notInAdGroupIdList);
                }
            }
            Page<CpcQueryKeywordReport> cpcQueryKeywordReportPage = iCpcQueryKeywordReportDao.listByAuoRule(adQueryKeywordAutoRuleParam, itemIds);
            List<String> targetIds = new ArrayList<>();
            Map<String, AmazonAdTargeting> targetMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(cpcQueryKeywordReportPage.getRows())) {
                cpcQueryKeywordReportPage.getRows().forEach(e -> {
                    AutoRuleQueryKeyword autoRuleQueryKeyword = new AutoRuleQueryKeyword();
                    autoRuleQueryKeyword.setId(e.getId());
                    autoRuleQueryKeyword.setCampaignId(e.getCampaignId());
                    autoRuleQueryKeyword.setAdGroupId(e.getAdGroupId());
                    autoRuleQueryKeyword.setPuid(adQueryKeywordAutoRuleParam.getPuid());
                    autoRuleQueryKeyword.setShopId(adQueryKeywordAutoRuleParam.getShopId());
                    autoRuleQueryKeyword.setKeywordText(e.getTargetKeywordText());
                    if (AmazonAd.AdQueryTypeEnum.SP_TARGETING.getQueryType().equals(adQueryKeywordAutoRuleParam.getQueryType())) {
                        autoRuleQueryKeyword.setQuery(e.getQuery().toUpperCase());
                        if (Constants.TARGETING_EXPRESSION_PREDEFINED.equals(e.getTargetingType())) {
                            autoRuleQueryKeyword.setKeywordText("自动投放组");
                        } else {
                            //非自动投放
                            targetIds.add(e.getTargetId());
                            String expression = e.getTargetKeywordText();
                            if (expression.startsWith("asin=")) {
                                String keywords = expression.substring("asin=".length());
                                autoRuleQueryKeyword.setKeywordText(keywords);
                                // 去除首尾双引号
                                if (autoRuleQueryKeyword.getKeywordText().startsWith("\"") && autoRuleQueryKeyword.getKeywordText().endsWith("\"")) {
                                    autoRuleQueryKeyword.setKeywordText(autoRuleQueryKeyword.getKeywordText().substring(1, autoRuleQueryKeyword.getKeywordText().length() - 1));
                                }
                            } else if (expression.startsWith("asin-expanded=")) {
                                String keywords = expression.substring("asin-expanded=".length());
                                autoRuleQueryKeyword.setKeywordText(keywords);
                                // 去除首尾双引号
                                if (autoRuleQueryKeyword.getKeywordText().startsWith("\"") && autoRuleQueryKeyword.getKeywordText().endsWith("\"")) {
                                    autoRuleQueryKeyword.setKeywordText(autoRuleQueryKeyword.getKeywordText().substring(1, autoRuleQueryKeyword.getKeywordText().length() - 1));
                                }
                            } else {
                                autoRuleQueryKeyword.setKeywordText(expression);
                            }
                            autoRuleQueryKeyword.setKeywordText(e.getTargetKeywordText());
                        }
                    } else {
                        autoRuleQueryKeyword.setQuery(e.getQuery());
                    }
                    if (StringUtils.isNotBlank(e.getTargetId())) {
                        autoRuleQueryKeyword.setTargetId(e.getTargetId());
                    }
                    autoRuleQueryKeyword.setQueryId(e.getQueryId());
                    autoRuleQueryKeyword.setMarketplaceId(e.getMarketplaceId());
                    autoRuleQueryKeyword.setAdType(e.getAdType());
                    autoRuleQueryKeyword.setQueryType(e.getQueryType());
                    autoRuleQueryKeyword.setHasSimilarRule(e.getHasSimilarRule());
                    // 投放值
                    if ("close-match".equalsIgnoreCase(e.getMatchType())) {
                        autoRuleQueryKeyword.setMatchType("close_match");
                    } else if ("loose-match".equalsIgnoreCase(e.getMatchType())) {
                        autoRuleQueryKeyword.setMatchType("loose_match");
                    } else if ("substitutes".equalsIgnoreCase(e.getMatchType())) {
                        autoRuleQueryKeyword.setMatchType("substitutes");
                    } else if ("complements".equalsIgnoreCase(e.getMatchType())) {
                        autoRuleQueryKeyword.setMatchType("complements");
                    } else {
                        autoRuleQueryKeyword.setMatchType(e.getMatchType());
                    }
                    autoRuleQueryKeyword.setServingStatus(e.getServingStatus());
                    autoRuleQueryKeyword.setState(e.getState());
                    autoRuleQueryKeyword.setHasSimilarRule(StringUtils.isNotBlank(adQueryKeywordAutoRuleParam.getHasSimilarRule()) ? Integer.valueOf(adQueryKeywordAutoRuleParam.getHasSimilarRule()) : AmazonAd.HasSimilarRuleEnum.NO.getHasSimilarRule());
                    autoRuleQueryKeywords.add(autoRuleQueryKeyword);
                });
            }
            ShopAuth shopAuth = shopAuthDao.getScAndVcById(adQueryKeywordAutoRuleParam.getShopId());
            //查询商品投放的图片和title信息/类目的相关信息
            if (AmazonAd.AdQueryTypeEnum.SP_TARGETING.getQueryType().equals(adQueryKeywordAutoRuleParam.getQueryType()) && CollectionUtils.isNotEmpty(targetIds)) {
                targetMap = amazonAdTargetingShardingDao.getByAdTargetIds(adQueryKeywordAutoRuleParam.getPuid(),
                                adQueryKeywordAutoRuleParam.getShopId(), targetIds)
                        .stream().collect(Collectors.toMap(AmazonAdTargeting::getTargetId, Function.identity()));
            }
            List<String> asins = autoRuleQueryKeywords.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordText()) && Pattern.compile(Constants.ASIN_REGEX).matcher(e.getKeywordText()).matches() && StringUtils.isBlank(e.getImgUrl())).map(e -> e.getKeywordText().toUpperCase()).distinct().collect(Collectors.toList());
            Map<String, AsinImage> asinMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(asins)) {
                List<AsinImage> listByAsins = syncAsinImageService.getListByAsinsNoSave(shopAuth.getPuid(), shopAuth.getMarketplaceId(), asins);
                asinMap = listByAsins.stream().filter(e -> StringUtils.isNotBlank(e.getAsin())).collect(Collectors.toMap(e -> e.getAsin().toUpperCase(), e1 -> e1, (e2, e3) -> e3));
            }
            //设置商品投放的图片和title信息/类目的相关信息
            for (AutoRuleQueryKeyword vo : autoRuleQueryKeywords) {
                if (targetMap.containsKey(vo.getTargetId())) {
                    AmazonAdTargeting amazonAdTargeting = targetMap.get(vo.getTargetId());
                    //类目投放则补充类目投放信息
                    if (Objects.nonNull(amazonAdTargeting) && TargetTypeEnum.category.name().equals(amazonAdTargeting.getType())) {
                        vo.setMatchType(amazonAdTargeting.getType());
                        vo.setCategory(amazonAdTargeting.getTargetingValue());
                        vo.setKeywordText(amazonAdTargeting.getTargetingValue());
                        // 改版前路径是存在这个字段里的，因为要支持列表页模糊搜索改到了targeting_value
                        if (StringUtils.isNotBlank(amazonAdTargeting.getCategoryPath())) {
                            vo.setCategory(amazonAdTargeting.getCategoryPath());
                        }
                        //如果为数字ID,表明类目或品牌已经被amazon删除
                        if (StringUtils.isNumeric(vo.getCategory())) {
                            vo.setCategory("此类目亚马逊已删除");
                        }
                    }
                    //商品投放切为非自动投放则补ASIN的相关信息
                    if (Objects.nonNull(amazonAdTargeting) && TargetTypeEnum.asin.name().equals(amazonAdTargeting.getType())) {
                        vo.setMatchType(amazonAdTargeting.getSelectType());
                        vo.setKeywordText(amazonAdTargeting.getTargetingValue());
                        vo.setImgUrl(amazonAdTargeting.getImgUrl());
                        vo.setTargetTitle(amazonAdTargeting.getTitle());
                        if (StringUtils.isNotBlank(vo.getKeywordText()) && (StringUtils.isBlank(vo.getTargetTitle()) || StringUtils.isBlank(vo.getImgUrl()))) {
                            AsinImage asinImage = asinMap.get(vo.getKeywordText().toUpperCase());
                            if (asinImage != null) {
                                Optional.ofNullable(asinImage.getTitle()).filter(StringUtils::isNotEmpty).ifPresent(t -> {
                                    if (StringUtils.isEmpty(vo.getTargetTitle())) {
                                        vo.setTargetTitle(t);
                                    }
                                });
                                Optional.ofNullable(asinImage.getImage()).filter(StringUtils::isNotEmpty).ifPresent(m -> {
                                    if (StringUtils.isEmpty(vo.getImgUrl())) {
                                        vo.setImgUrl(m);
                                    }
                                });
                            }
                        }
                    }
                    if (amazonAdTargeting != null && StringUtils.isNotBlank(amazonAdTargeting.getResolvedExpression()) && TargetTypeEnum.category.name().equalsIgnoreCase(amazonAdTargeting.getType())) {
                        JSONArray jsonArray = JSONArray.parseArray(amazonAdTargeting.getResolvedExpression());
                        if (jsonArray != null && !jsonArray.isEmpty()) {
                            this.fillBrandMessage(vo, jsonArray);
                        }
                    }
                }

            }
            Page<AutoRuleQueryKeyword> autoRuleQueryPages = new Page<>();
            autoRuleQueryPages.setPageNo(cpcQueryKeywordReportPage.getPageNo());
            autoRuleQueryPages.setPageSize(cpcQueryKeywordReportPage.getPageSize());
            autoRuleQueryPages.setRows(autoRuleQueryKeywords);
            autoRuleQueryPages.setTotalPage(cpcQueryKeywordReportPage.getTotalPage());
            autoRuleQueryPages.setTotalSize(cpcQueryKeywordReportPage.getTotalSize());
            result.setCode(Result.SUCCESS);
            result.setData(autoRuleQueryPages);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("自动化规则查询搜索词异常");
            log.error("traceId:{}, puid:{}, shopId:{}, 自动化规则查询广告组异常: {}", adQueryKeywordAutoRuleParam.getTraceId(), adQueryKeywordAutoRuleParam.getPuid(), adQueryKeywordAutoRuleParam.getShopId(), e);
        }
        return result;
    }

    /**
     * 填充品牌细节信息
     *
     * @param vo
     * @param jsonArray
     */
    private void fillBrandMessage(AutoRuleQueryKeyword vo, JSONArray jsonArray) {
        if (jsonArray == null || jsonArray.isEmpty()) return;
        try {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String value = jsonObject.getString("type");
                // T00030 TargetingPredicateNested
                if (SBTargetingAudienceTypeEnum.fromValue(value) != null) {
                    JSONArray valueArray = ResolvedExpressionParseHelper.getJsonValueAsArray(jsonObject);
                    fillBrandMessage(vo, valueArray);
                }
                if (ExpressionEnum.asinCategorySameAs.value().equals(value) || SpV3ExpressionEnum.asinCategorySameAs.getValueV3().equals(value)) {
                    vo.setCategory(jsonObject.getString("value"));
                    //如果为数字ID,表明类目或品牌已经被amazon删除
                    if (StringUtils.isNumeric(vo.getCategory())) {
                        vo.setCategory("此类目亚马逊已删除");
                    }
                }
                if (ExpressionEnum.asinBrandSameAs.value().equals(value) || SpV3ExpressionEnum.asinBrandSameAs.getValueV3().equals(value)) {
                    vo.setBrandName(jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinPriceBetween.value().equals(value) || SpV3ExpressionEnum.asinPriceBetween.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value").replace("-", ","));
                }
                if (ExpressionEnum.asinPriceLessThan.value().equals(value) || SpV3ExpressionEnum.asinPriceLessThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及以下");
                }
                if (ExpressionEnum.asinPriceGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinPriceGreaterThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及更高");
                }
                if (ExpressionEnum.asinReviewRatingBetween.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingBetween.getValueV3().equals(value)) {
                    vo.setCategoryRating(jsonObject.getString("value").replace('-', '~'));
                }
                if (ExpressionEnum.asinReviewRatingLessThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingLessThan.getValueV3().equals(value)) {
                    vo.setCategoryRating("1~" + jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinReviewRatingGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingGreaterThan.getValueV3().equals(value)) {
                    vo.setCategoryRating(jsonObject.getString("value") + "~5");
                }
                if (ExpressionEnum.asinIsPrimeShippingEligible.value().equals(value) || SpV3ExpressionEnum.asinIsPrimeShippingEligible.getValueV3().equals(value)) {
                    if ("true".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("具有Prime资格");
                    } else if ("false".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("不具有Prime资格");
                    }
                }
                if (ExpressionEnum.lookback.value().equals(value)) {
                    vo.setLookBack(jsonObject.getString("value"));
                }
            }
        } catch (Exception e) {
            log.error("解析json失败", e);
        }
    }

    @Override
    public Result<Page<AmazonAdProduct>> getAdProductList(AdProductAutoRuleParam adProductAutoRuleParam) {
        Result<Page<AmazonAdProduct>> result = new Result<>();
        try {
            Page<AmazonAdProduct> page = new Page<>();
            page.setPageSize(adProductAutoRuleParam.getPageSize());
            page.setPageNo(adProductAutoRuleParam.getPageNo());
            List<String> adGroupIds = amazonAdGroupDao.getAdGroupByIds(adProductAutoRuleParam.getPuid(), adProductAutoRuleParam.getShopId());
            List<AmazonAdProduct> amazonAdProductList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(adGroupIds)) {
                Page<AmazonAdProduct> amazonAdProductPage = amazonAdProductDao.getAdProductList(adProductAutoRuleParam, adGroupIds);
                if (amazonAdProductPage != null && CollectionUtils.isNotEmpty(amazonAdProductPage.getRows())) {
                    page.setTotalSize(amazonAdProductPage.getTotalSize());
                    page.setTotalPage(amazonAdProductPage.getTotalPage());
                    Map<String, List<ProductAdReportVo>> asinImageMap = null;
                    Map<String, List<AmazonAdProductMetadata>> metadataMap = null;
                    List<String> skus = amazonAdProductPage.getRows().stream().filter(Objects::nonNull).map(AmazonAdProduct::getSku).distinct().collect(Collectors.toList());
                    Map<String, List<ProductAdReportVo>> hashMap = new HashMap<>();
                    Map<String, List<AmazonAdProductMetadata>> map = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(skus)) {
                        List<AmazonAdProductMetadata> amazonAdProductMetadataList = amazonAdProductMetadataService.getAsinBySkus(adProductAutoRuleParam.getPuid(), adProductAutoRuleParam.getShopId(), skus, null);
                        if (CollectionUtils.isNotEmpty(amazonAdProductMetadataList)) {
                            map.putAll(amazonAdProductMetadataList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AmazonAdProductMetadata::getSku)));
                            metadataMap = map;
                        }
                        // 兼容老代码逻辑
                        List<List<String>> partition = com.google.common.collect.Lists.partition(skus, 100);
                        for (List<String> batchSkus : partition) {
                            List<ProductAdReportVo> asinBySkus = productDao.getAsinBySkus(adProductAutoRuleParam.getPuid(), adProductAutoRuleParam.getShopId(), "", batchSkus);
                            if (CollectionUtils.isNotEmpty(asinBySkus)) {
                                hashMap.putAll(asinBySkus.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(ProductAdReportVo::getSku)));
                            }
                        }
                        asinImageMap = hashMap;
                    }
                    Map<String, List<AmazonAdProductMetadata>> finalMetadataMap = metadataMap;
                    Map<String, List<ProductAdReportVo>> finalAsinImageMap = asinImageMap;
                    amazonAdProductPage.getRows().forEach(e -> {
                        AmazonAdProduct amazonAdProduct = new AmazonAdProduct();
                        if (MapUtils.isNotEmpty(finalMetadataMap) && finalMetadataMap.containsKey(e.getSku())) {
                            amazonAdProduct.setImageUrl(finalMetadataMap.get(e.getSku()).get(0).getImageUrl());
                            amazonAdProduct.setTitle(finalMetadataMap.get(e.getSku()).get(0).getTitle());
                        } else if (MapUtils.isNotEmpty(finalAsinImageMap) && finalAsinImageMap.containsKey(e.getSku())) {
                            amazonAdProduct.setImageUrl(finalAsinImageMap.get(e.getSku()).get(0).getMainImage());
                            amazonAdProduct.setTitle(finalAsinImageMap.get(e.getSku()).get(0).getTitle());
                        }
                        amazonAdProduct.setAsin(e.getAsin());
                        amazonAdProduct.setSku(e.getSku());
                        amazonAdProductList.add(amazonAdProduct);
                    });
                    page.setRows(amazonAdProductList);
                }
            }
            result.setData(page);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("自动化规则查询广告目标商品");
            log.error("traceId:{} puid:{} shopId:{} 自动化规则查询广告目标商品:", adProductAutoRuleParam.getTraceId(), adProductAutoRuleParam.getPuid(), adProductAutoRuleParam.getShopId(), e);
        }
        return result;
    }

    @Override
    public Result<Page<AmazonAdKeyword>> getAdKeywordCardList(AdKeywordCardAutoRuleParam param) {
        Result<Page<AmazonAdKeyword>> result = new Result<>();
        try {
            if (CollectionUtils.isEmpty(param.getCampaignIdList())) {
                if (CollectionUtils.isNotEmpty(param.getPortfolioIdList())) {
                    List<String> campaignIdList = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(param.getPuid(),
                            param.getShopId(), param.getPortfolioIdList(), null);
                    if (CollectionUtils.isNotEmpty(campaignIdList)) {
                        param.setCampaignIdList(campaignIdList);
                    } else {
                        Page<AmazonAdKeyword> autoRuleAdKeyword = new Page<>();
                        result.setCode(Result.SUCCESS);
                        result.setData(autoRuleAdKeyword);
                        return result;
                    }
                }
            }
            List<String> itemIds = advertiseAutoRuleStatusDao.getListByItemIdList(param.getPuid(), param.getShopId());
            List<String> adGroupIds = amazonAdProductDao.getGroupIdByAsin(param.getPuid(), param.getShopId(), param.getSkuList());
            if (CollectionUtils.isEmpty(adGroupIds)) {
                return result;
            }
            if (CollectionUtils.isNotEmpty(param.getGroupIdList())) {
                //如果用户传的参数和msku关联的广告产品组id不存在交集，可以直接返回
                Set<String> intersection = com.meiyunji.sponsored.common.util.CollectionUtil.findIntersection(param.getGroupIdList(), adGroupIds);
                if (CollectionUtils.isEmpty(intersection)) {
                    return result;
                } else {
                    param.setGroupIdList(Lists.newArrayList(intersection));
                }
            } else {
                param.setGroupIdList(adGroupIds);
            }
            Page<AmazonAdKeyword> adKeywordPage = amazonAdKeywordDaoRoutingService.pageKeywordCardSpKeyword(param, itemIds);
            result.setCode(Result.SUCCESS);
            result.setData(adKeywordPage);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("关键词卡位查询关键词异常");
            log.error("traceId:{} puid:{} shopId:{} 关键词卡位查询关键词异常:", param.getTraceId(), param.getPuid(), param.getShopId(), e);
        }
        return result;
    }

    @Override
    public Result<Page<AdvertiseAutoRuleStatus>> pageAdControlledCampaign(AutoRuleObjectParam param) {
        Result<Page<AdvertiseAutoRuleStatus>> pageResult = new Result<>();
        try {
            //查询模板和受控对象
            AdvertiseAutoRuleTemplate advertiseAutoRuleTemplate = advertiseAutoRuleTemplateDao.selectByPrimaryKey(param.getPuid(), param.getTemplateId());
            List<AdvertiseAutoRuleStatus> advertiseAutoRuleStatusList = advertiseAutoRuleStatusDao.selectAdControlledList(param);
            Page<AdvertiseAutoRuleStatus> voPage = new Page<>(param.getPageNo(), param.getPageSize());

            //无数据
            if (CollectionUtils.isEmpty(advertiseAutoRuleStatusList)) {
                pageResult.setCode(Result.SUCCESS);
                pageResult.setData(voPage);
                return pageResult;
            }

            //填充基础数据
            List<AdvertiseAutoRuleStatus> list = Lists.newArrayListWithExpectedSize(advertiseAutoRuleStatusList.size());
            List<String> campaignIds = advertiseAutoRuleStatusList.stream().map(AdvertiseAutoRuleStatus::getItemId).collect(Collectors.toList());
            //查询广告活动
            List<AmazonAdCampaignAll> campaignAlls = amazonAdCampaignAllDao.autoRuleCampaign(param.getPuid(),
                    param.getShopId(),
                    param.getSearchValue(),
                    param.getPortfolioIds(),
                    param.getAdTypeList(),
                    campaignIds,
                    param.getState(),
                    param.getServingStatusList());
            //无数据
            if (CollectionUtils.isEmpty(campaignAlls)) {
                pageResult.setCode(Result.SUCCESS);
                pageResult.setData(voPage);
                return pageResult;
            }

            //转map
            Map<String, AmazonAdCampaignAll> campaignMap = campaignAlls.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
            Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>();
            List<String> portfolioIds = campaignAlls.stream().filter(Objects::nonNull).map(AmazonAdCampaignAll::getPortfolioId).distinct().collect(Collectors.toList());
            List<AmazonAdPortfolio> amazonAdPortfolios = amazonAdPortfolioDao.getPortfolioList(param.getPuid(), param.getShopId(), portfolioIds);
            if (CollectionUtils.isNotEmpty(amazonAdPortfolios)) {
                portfolioMap = amazonAdPortfolios.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
            }

            //填充数据
            for (AdvertiseAutoRuleStatus vo : advertiseAutoRuleStatusList) {
                MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(vo.getMarketplaceId());
                if (Objects.nonNull(m)) {
                    vo.setCurrency(m.getCurrencyCode());
                }
                //模板更新
                if (vo.getVersion() >= advertiseAutoRuleTemplate.getVersion()) {
                    vo.setUpdateStatus(AutoRuleUpdateStatusEnum.UPDATE.getCode());
                } else {
                    vo.setUpdateStatus(AutoRuleUpdateStatusEnum.NOT_UPDATE.getCode());
                }

                AmazonAdCampaignAll campaignAll = campaignMap.get(vo.getItemId());
                if (Objects.isNull(campaignAll)) {
                    continue;
                }

                //填充活动名称
                vo.setItemName(campaignAll.getName());
                vo.setCampaignName(campaignAll.getName());
                vo.setCampaignId(vo.getItemId());
                //设置原始预算
                OriginValueVo originValueVo = null;
                if (StringUtils.isNotBlank(vo.getOriginValue())) {
                    originValueVo = JSONUtil.jsonToObject(vo.getOriginValue(), OriginValueVo.class);
                }
                if (originValueVo != null && originValueVo.getBudgetValue() != null) {
                    vo.setBudgetValue(originValueVo.getBudgetValue());
                } else {
                    vo.setBudgetValue(campaignAll.getBudget());
                }
                Optional.ofNullable(campaignAll.getBudgetType()).filter(StringUtils::isNotBlank).ifPresent(vo::setBudgetType);
                //状态，服务状态
                vo.setState(campaignAll.getState());
                vo.setAdType(vo.getAdType());
                if (StringUtils.isNotBlank(campaignAll.getServingStatus())) {
                    vo.setServingStatus(campaignAll.getServingStatus());
                    AmazonAdCampaignAll.servingStatusEnum byCode = UCommonUtil.getByCode(campaignAll.getServingStatus(), AmazonAdCampaignAll.servingStatusEnum.class);
                    if (byCode != null) {
                        vo.setServingStatusName(byCode.getName());
                        vo.setServingStatusDec(byCode.getDescription());
                    } else {
                        vo.setServingStatusDec(campaignAll.getServingStatus());
                        vo.setServingStatusName(campaignAll.getServingStatus());
                    }
                }

                //广告组合
                if (MapUtils.isNotEmpty(portfolioMap)) {
                    AmazonAdPortfolio portfolio = portfolioMap.get(campaignAll.getPortfolioId());
                    if (Objects.nonNull(portfolio)) {
                        vo.setPortfolioId(campaignAll.getPortfolioId());
                        vo.setPortfolioName(portfolio.getName());
                        vo.setPortfolioIsHidden(portfolio.getIsHidden());
                    }
                }
                list.add(vo);
            }

            //内存分页，待优化
            if (CollectionUtils.isNotEmpty(list)) {
                voPage = PageUtil.getPage(voPage, list);
            }

            pageResult.setCode(Result.SUCCESS);
            pageResult.setData(voPage);
        } catch (Exception e) {
            pageResult.setCode(Result.ERROR);
            pageResult.setMsg("查询活动受控对象异常");
            log.error("traceId:{} puid:{} shopId:{} 查询活动受控对象异常", param.getTraceId(), param.getPuid(), param.getShopId(), e);
        }
        return pageResult;
    }

    @Override
    public Result<Page<AdvertiseAutoRuleStatus>> pageAdControlledTarget(AutoRuleObjectParam param) {
        Result<Page<AdvertiseAutoRuleStatus>> pageResult = new Result<>();
        Page<AdvertiseAutoRuleStatus> voPage = new Page<>(param.getPageNo(), param.getPageSize());
        try {
            //查询为空的情况直接返回
            if (StringUtils.isNotBlank(param.getMatchType())) {
                if (StringUtils.isBlank(param.getTargetType())) {
                    //只能查关键词投放
                    param.setTargetType(AutoRuleTargetTypeEnum.keywordTarget.getTargetType());
                }
                //非关键词投放，直接返回空
                if (!AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(param.getTargetType())) {
                    pageResult.setCode(Result.SUCCESS);
                    pageResult.setData(voPage);
                    return pageResult;
                }
            }

            //组合id转活动id
            if (CollectionUtils.isEmpty(param.getCampaignIds())) {
                if (CollectionUtils.isNotEmpty(param.getPortfolioIds())) {
                    List<String> campaignIdList = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdList(param.getPuid(), param.getShopId(), param.getPortfolioIds());
                    if (CollectionUtils.isNotEmpty(campaignIdList)) {
                        param.setCampaignIds(campaignIdList);
                    } else {
                        pageResult.setCode(Result.SUCCESS);
                        pageResult.setData(voPage);
                        return pageResult;
                    }
                }
            }

            //查询模板和受控对象
            AdvertiseAutoRuleTemplate template = advertiseAutoRuleTemplateDao.selectByPrimaryKey(param.getPuid(), param.getTemplateId());
            List<QueryTargetTypeVo> queryTargetTypeVoList = advertiseAutoRuleStatusDao.queryListItemIdAndTargetType(param);

            //无数据
            if (CollectionUtils.isEmpty(queryTargetTypeVoList)) {
                pageResult.setCode(Result.SUCCESS);
                pageResult.setData(voPage);
                return pageResult;
            }

            //数据分组
            List<String> spTargetList = new ArrayList<>();
            List<String> spKeywordList = new ArrayList<>();
            List<String> sbTargetList = new ArrayList<>();
            List<String> sbKeywordList = new ArrayList<>();
            List<String> sdTargetList = new ArrayList<>();
            queryTargetTypeVoList.forEach(x -> {
                if (AutoRuleSubmitAdTypeEnum.SP.getCode().equals(x.getAdType())) {
                    if (AutoRuleTargetTypeEnum.autoTarget.getTargetType().equals(x.getTargetType()) || AutoRuleTargetTypeEnum.productTarget.getTargetType().equals(x.getTargetType())) {
                        spTargetList.add(x.getItemId());
                    } else if (AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(x.getTargetType())) {
                        spKeywordList.add(x.getItemId());
                    }
                } else if (AutoRuleSubmitAdTypeEnum.SB.getCode().equals(x.getAdType())) {
                    if (AutoRuleTargetTypeEnum.productTarget.getTargetType().equals(x.getTargetType())) {
                        sbTargetList.add(x.getItemId());
                    } else if (AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(x.getTargetType())) {
                        sbKeywordList.add(x.getItemId());
                    }
                } else if (AutoRuleSubmitAdTypeEnum.SD.getCode().equals(x.getAdType())) {
                    sdTargetList.add(x.getItemId());
                }
            });

            //定义收集变量
            PageAdControlledTargetProcessDto processDto = new PageAdControlledTargetProcessDto();
            //查询投放数据,记录到processDto
            queryTargetBaseInfo(param, spKeywordList, spTargetList, sbKeywordList, sbTargetList, sdTargetList, processDto);

            //无数据
            if (CollectionUtils.isEmpty(processDto.getTargetIdSet())) {
                pageResult.setData(voPage);
                return pageResult;
            }

            //再次查询满足条件的受控对象
            param.setItemIdList(new ArrayList<>(processDto.getTargetIdSet()));
            voPage = advertiseAutoRuleStatusDao.selectAdControlledPage(param);
            if (voPage != null && CollectionUtils.isNotEmpty(voPage.getRows())) {
                //组装数据
                assemblyTargetData(template.getPuid(), template.getShopId(), template.getVersion(), voPage, processDto);
            }
            pageResult.setCode(Result.SUCCESS);
            pageResult.setData(voPage);
        } catch (Exception e) {
            pageResult.setCode(Result.ERROR);
            pageResult.setMsg("查询关键词受控对象异常");
            log.error("traceId:{} puid:{} shopId:{} 查询投放受控对象异常", param.getTraceId(), param.getPuid(), param.getShopId(), e);
        }
        return pageResult;
    }

    private void queryTargetBaseInfo(AutoRuleObjectParam param,
                                     List<String> spKeywordList,
                                     List<String> spTargetList,
                                     List<String> sbKeywordList,
                                     List<String> sbTargetList,
                                     List<String> sdTargetList,
                                     PageAdControlledTargetProcessDto processDto) {

        boolean needQueryKeyword = StringUtils.isBlank(param.getTargetType()) || AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(param.getTargetType());
        boolean needQuerySpTarget = StringUtils.isBlank(param.getTargetType()) || StringUtils.equalsAny(param.getTargetType(), AutoRuleTargetTypeEnum.productTarget.getTargetType(), AutoRuleTargetTypeEnum.autoTarget.getTargetType());
        boolean needQuerySbTarget = StringUtils.isBlank(param.getTargetType()) || AutoRuleTargetTypeEnum.productTarget.getTargetType().equals(param.getTargetType());
        boolean needQuerySdTarget = StringUtils.isBlank(param.getTargetType()) || StringUtils.equalsAny(param.getTargetType(), AutoRuleTargetTypeEnum.productTarget.getTargetType(), AutoRuleTargetTypeEnum.audienceTarget.getTargetType());

        //sp关键词
        if (CollectionUtils.isNotEmpty(spKeywordList) && needQueryKeyword) {
            List<AmazonAdKeyword> list = amazonAdKeywordShardingDao.autoRuleKeywordIdList(param.getPuid(),
                    param.getShopId(),
                    param.getCampaignIds(),
                    param.getGroupIds(),
                    param.getState(),
                    param.getMatchType(),
                    param.getSearchValue(),
                    spKeywordList,
                    param.getServingStatusList());
            if (CollectionUtils.isNotEmpty(list)) {
                for (AmazonAdKeyword x : list) {
                    processDto.getTargetIdSet().add(x.getKeywordId());
                    processDto.getSpkeywordMap().put(x.getKeywordId(), x);
                }
            }
        }
        //sp投放
        if (CollectionUtils.isNotEmpty(spTargetList) && needQuerySpTarget) {
            List<AmazonAdTargeting> list = amazonAdTargetingShardingDao.autoRuleTargetIdList(param.getPuid(),
                    param.getShopId(),
                    param.getCampaignIds(),
                    param.getGroupIds(),
                    param.getState(),
                    param.getMatchType(),
                    param.getSearchValue(),
                    spTargetList,
                    param.getServingStatusList());
            if (CollectionUtils.isNotEmpty(list)) {
                for (AmazonAdTargeting x : list) {
                    processDto.getTargetIdSet().add(x.getTargetId());
                    processDto.getSpTargetMap().put(x.getTargetId(), x);
                }
            }
        }
        //sb关键词
        if (CollectionUtils.isNotEmpty(sbKeywordList) && needQueryKeyword) {
            List<AmazonSbAdKeyword> list = new ArrayList<>();
            if (CollectionUtils.isEmpty(param.getServingStatusList())) {
                list = amazonSbAdKeywordDao.autoRuleKeywordIdList(param.getPuid(),
                        param.getShopId(),
                        param.getCampaignIds(),
                        param.getGroupIds(),
                        param.getState(),
                        param.getMatchType(),
                        param.getSearchValue(),
                        sbKeywordList);
            }

            if (CollectionUtils.isNotEmpty(list)) {
                for (AmazonSbAdKeyword x : list) {
                    processDto.getTargetIdSet().add(x.getKeywordId());
                    processDto.getSbkeywordMap().put(x.getKeywordId(), x);
                }
            }
        }
        //sb投放
        if (CollectionUtils.isNotEmpty(sbTargetList) && needQuerySbTarget) {
            List<AmazonSbAdTargeting> list = new ArrayList<>();
            if (CollectionUtils.isEmpty(param.getServingStatusList())) {
                list = amazonSbAdTargetingDao.autoRuleTargetIdList(param.getPuid(),
                        param.getShopId(),
                        param.getCampaignIds(),
                        param.getGroupIds(),
                        param.getState(),
                        param.getMatchType(),
                        param.getSearchValue(),
                        sbTargetList);
            }
            if (CollectionUtils.isNotEmpty(list)) {
                for (AmazonSbAdTargeting x : list) {
                    processDto.getTargetIdSet().add(x.getTargetId());
                    processDto.getSbTargetMap().put(x.getTargetId(), x);
                }
            }
        }
        //sd投放
        if (CollectionUtils.isNotEmpty(sdTargetList) && needQuerySdTarget) {
            List<AmazonSdAdTargeting> list = amazonSdAdTargetingDao.autoRuleTargetIdList(param.getPuid(),
                    param.getShopId(),
                    param.getCampaignIds(),
                    param.getGroupIds(),
                    param.getState(),
                    param.getMatchType(),
                    param.getTargetType(),
                    param.getSearchValue(),
                    sdTargetList,
                    param.getServingStatusList());
            if (CollectionUtils.isNotEmpty(list)) {
                for (AmazonSdAdTargeting x : list) {
                    processDto.getTargetIdSet().add(x.getTargetId());
                    processDto.getSdTargetMap().put(x.getTargetId(), x);
                }
            }
        }
    }


    //组装投放受控查询数据
    private void assemblyTargetData(Integer puid,
                                    Integer shopId,
                                    Integer templateVersion,
                                    Page<AdvertiseAutoRuleStatus> voPage,
                                    PageAdControlledTargetProcessDto processDto) {
        //无数据
        if (CollectionUtils.isEmpty(voPage.getRows())) {
            return;
        }

        //收集当前页的活动和组id
        List<AdvertiseAutoRuleStatus> statusList = voPage.getRows();
        statusList.forEach(x -> {
            if (AutoRuleSubmitAdTypeEnum.SP.getCode().equals(x.getAdType())) {
                if (AutoRuleTargetTypeEnum.autoTarget.getTargetType().equals(x.getTargetType()) || AutoRuleTargetTypeEnum.productTarget.getTargetType().equals(x.getTargetType())) {
                    AmazonAdTargeting targeting = processDto.getSpTargetMap().get(x.getItemId());
                    if (Objects.nonNull(targeting)) {
                        processDto.getSpGroupIdSet().add(targeting.getAdGroupId());
                        processDto.getCampaignIdSet().add(targeting.getCampaignId());
                    }
                } else if (AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(x.getTargetType())) {
                    AmazonAdKeyword keyword = processDto.getSpkeywordMap().get(x.getItemId());
                    if (Objects.nonNull(keyword)) {
                        processDto.getSpGroupIdSet().add(keyword.getAdGroupId());
                        processDto.getCampaignIdSet().add(keyword.getCampaignId());
                    }
                }
            } else if (AutoRuleSubmitAdTypeEnum.SB.getCode().equals(x.getAdType())) {
                if (AutoRuleTargetTypeEnum.productTarget.getTargetType().equals(x.getTargetType())) {
                    AmazonSbAdTargeting targeting = processDto.getSbTargetMap().get(x.getItemId());
                    if (Objects.nonNull(targeting)) {
                        processDto.getSbGroupIdSet().add(targeting.getAdGroupId());
                        processDto.getCampaignIdSet().add(targeting.getCampaignId());
                    }
                } else if (AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(x.getTargetType())) {
                    AmazonSbAdKeyword keyword = processDto.getSbkeywordMap().get(x.getItemId());
                    if (Objects.nonNull(keyword)) {
                        processDto.getSbGroupIdSet().add(keyword.getAdGroupId());
                        processDto.getCampaignIdSet().add(keyword.getCampaignId());
                    }
                }
            } else if (AutoRuleSubmitAdTypeEnum.SD.getCode().equals(x.getAdType())) {
                AmazonSdAdTargeting targeting = processDto.getSdTargetMap().get(x.getItemId());
                if (Objects.nonNull(targeting)) {
                    processDto.getSdGroupIdSet().add(targeting.getAdGroupId());
                    //sd投放表没有广告活动id，需要在下边通过组去拿活动id
                }
            }
        });

        //查询sp广告组信息
        if (CollectionUtils.isNotEmpty(processDto.getSpGroupIdSet())) {
            List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.getAdGroupByIds(puid, shopId, null, new ArrayList<>(processDto.getSpGroupIdSet()));
            if (CollectionUtils.isNotEmpty(amazonAdGroups)) {
                processDto.setSpGroupMap(amazonAdGroups.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e)));
            }
        }

        //查询sb广告组信息
        if (CollectionUtils.isNotEmpty(processDto.getSbGroupIdSet())) {
            List<AmazonSbAdGroup> amazonAdGroups = amazonSbAdGroupDao.getAdGroupByIds(puid, shopId, new ArrayList<>(processDto.getSbGroupIdSet()));
            if (CollectionUtils.isNotEmpty(amazonAdGroups)) {
                processDto.setSbGroupMap(amazonAdGroups.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, e -> e)));
            }
        }

        //查询sd广告组信息
        if (CollectionUtils.isNotEmpty(processDto.getSdGroupIdSet())) {
            List<AmazonSdAdGroup> amazonAdGroups = amazonSdAdGroupDao.getByGroupIds(puid, shopId, new ArrayList<>(processDto.getSdGroupIdSet()));
            if (CollectionUtils.isNotEmpty(amazonAdGroups)) {
                amazonAdGroups.forEach(x -> {
                    processDto.getSdGroupMap().put(x.getAdGroupId(), x);
                    //sd补充活动id
                    processDto.getCampaignIdSet().add(x.getCampaignId());
                });
            }
        }

        //查询广告活动信息和组合信息
        if (CollectionUtils.isNotEmpty(processDto.getCampaignIdSet())) {
            List<AmazonAdCampaignAll> campaignAlls = amazonAdCampaignAllDao.listByCampaignIdNoType(puid, shopId, new ArrayList<>(processDto.getCampaignIdSet()));
            if (CollectionUtils.isNotEmpty(campaignAlls)) {
                campaignAlls.forEach(x -> {
                    processDto.getCampaignMap().put(x.getCampaignId(), x);
                    if (StringUtils.isNotBlank(x.getPortfolioId())) {
                        processDto.getPortfolioIdSet().add(x.getPortfolioId());
                    }
                });

                //广告组合
                if (CollectionUtils.isNotEmpty(processDto.getPortfolioIdSet())) {
                    List<AmazonAdPortfolio> portfolioList = amazonAdPortfolioDao.getPortfolioList(puid, shopId, new ArrayList<>(processDto.getPortfolioIdSet()));
                    if (CollectionUtils.isNotEmpty(portfolioList)) {
                        processDto.setPortfolioMap(portfolioList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e)));
                    }
                }
            }
        }

        //遍历受控对象set基础数据信息
        for (AdvertiseAutoRuleStatus vo : statusList) {
            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(vo.getMarketplaceId());
            if (Objects.nonNull(m)) {
                vo.setCurrency(m.getCurrencyCode());
            }
            //模板更新状态
            if (vo.getVersion() >= templateVersion) {
                vo.setUpdateStatus(AutoRuleUpdateStatusEnum.UPDATE.getCode());
            } else {
                vo.setUpdateStatus(AutoRuleUpdateStatusEnum.NOT_UPDATE.getCode());
            }

            //记录活动id
            String campaignId = "";
            //记录投放竞价
            BigDecimal bid = null;
            //记录组默认竞价
            BigDecimal defaultBid = null;

            //基础数据set
            //类目投放详情等set
            if (AutoRuleSubmitAdTypeEnum.SP.getCode().equals(vo.getAdType())) {
                //记录广告组id
                String groupId = "";
                //sp投放数据
                if (AutoRuleTargetTypeEnum.autoTarget.getTargetType().equals(vo.getTargetType()) || AutoRuleTargetTypeEnum.productTarget.getTargetType().equals(vo.getTargetType())) {
                    AmazonAdTargeting targeting = processDto.getSpTargetMap().get(vo.getItemId());
                    if (Objects.isNull(targeting)) {
                        continue;
                    }
                    if (AutoRuleTargetTypeEnum.autoTarget.getTargetType().equals(vo.getTargetType())) {
                        vo.setItemName(AutoTargetTypeEnum.getAutoTargetValue(targeting.getTargetingValue()));
                        vo.setKeywordText(AutoTargetTypeEnum.getAutoTargetValue(targeting.getTargetingValue()));
                        vo.setType(QueryPageAdKeywordRespTypeEnum.auto.name());
                    } else {
                        vo.setItemName(targeting.getTargetingValue());
                        vo.setKeywordText(targeting.getTargetingValue());
                        vo.setType(targeting.getType());
                        //填充类目和asin
                        fillAsinAndBrandAndAudienceInfo4ControlledTarget(vo, targeting.getType(), targeting.getTitle(), targeting.getImgUrl(), targeting.getResolvedExpression());
                    }
                    vo.setKeywordId(targeting.getTargetId());
                    vo.setState(targeting.getState());
                    if (StringUtils.isNotBlank(targeting.getServingStatus())) {
                        vo.setServingStatus(targeting.getServingStatus());
                        AmazonAdTargeting.servingStatusEnum byCode = UCommonUtil.getByCode(targeting.getServingStatus(), AmazonAdTargeting.servingStatusEnum.class);
                        if (byCode != null) {
                            vo.setServingStatusName(byCode.getName());
                            vo.setServingStatusDec(byCode.getDescription());
                        } else {
                            vo.setServingStatusDec(targeting.getServingStatus());
                            vo.setServingStatusName(targeting.getServingStatus());
                        }
                    }
                    campaignId = targeting.getCampaignId();
                    groupId = targeting.getAdGroupId();
                    bid = Objects.nonNull(targeting.getBid()) ? BigDecimal.valueOf(targeting.getBid()).setScale(2, RoundingMode.HALF_UP) : null;
                } else if (AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(vo.getTargetType())) {
                    AmazonAdKeyword keyword = processDto.getSpkeywordMap().get(vo.getItemId());
                    if (Objects.isNull(keyword)) {
                        continue;
                    }
                    vo.setItemName(keyword.getKeywordText());
                    vo.setKeywordText(keyword.getKeywordText());
                    vo.setKeywordId(keyword.getKeywordId());
                    vo.setType(QueryPageAdKeywordRespTypeEnum.keyword.name());
                    vo.setState(keyword.getState());
                    if (StringUtils.isNotBlank(keyword.getServingStatus())) {
                        vo.setServingStatus(keyword.getServingStatus());
                        AmazonAdKeyword.servingStatusEnum byCode = UCommonUtil.getByCode(keyword.getServingStatus(), AmazonAdKeyword.servingStatusEnum.class);
                        if (byCode != null) {
                            vo.setServingStatusName(byCode.getName());
                            vo.setServingStatusDec(byCode.getDescription());
                        } else {
                            vo.setServingStatusDec(keyword.getServingStatus());
                            vo.setServingStatusName(keyword.getServingStatus());
                        }
                    }

                    if (StringUtils.isNotBlank(keyword.getMatchType())) {
                        vo.setMatchName((MatchTypeEnum.getMatchValue(keyword.getMatchType())));
                        vo.setMatchType(keyword.getMatchType());
                    }

                    campaignId = keyword.getCampaignId();
                    groupId = keyword.getAdGroupId();
                    bid = Objects.nonNull(keyword.getBid()) ? BigDecimal.valueOf(keyword.getBid()).setScale(2, RoundingMode.HALF_UP) : null;
                }

                //sp广告组数据
                if (StringUtils.isNotBlank(groupId)) {
                    AmazonAdGroup adGroup = processDto.getSpGroupMap().get(groupId);
                    if (Objects.nonNull(adGroup)) {
                        vo.setAdGroupName(adGroup.getName());
                        vo.setAdGroupId(adGroup.getAdGroupId());
                        defaultBid = Objects.nonNull(adGroup.getDefaultBid()) ? BigDecimal.valueOf(adGroup.getDefaultBid()).setScale(2, RoundingMode.HALF_UP) : null;
                    }
                }
            } else if (AutoRuleSubmitAdTypeEnum.SB.getCode().equals(vo.getAdType())) {
                //记录广告组id
                String groupId = "";
                if (AutoRuleTargetTypeEnum.productTarget.getTargetType().equals(vo.getTargetType())) {
                    AmazonSbAdTargeting targeting = processDto.getSbTargetMap().get(vo.getItemId());
                    if (Objects.isNull(targeting)) {
                        continue;
                    }

                    vo.setItemName(targeting.getTargetText());
                    vo.setKeywordText(targeting.getTargetText());
                    vo.setType(targeting.getType());
                    //填充类目和asin
                    fillAsinAndBrandAndAudienceInfo4ControlledTarget(vo, targeting.getType(), targeting.getTitle(), targeting.getImgUrl(), targeting.getResolvedExpression());

                    vo.setKeywordId(targeting.getTargetId());
                    vo.setState(targeting.getState());
                    //没有服务状态
                    campaignId = targeting.getCampaignId();
                    groupId = targeting.getAdGroupId();
                    bid = Objects.nonNull(targeting.getBid()) ? targeting.getBid().setScale(2, RoundingMode.HALF_UP) : null;
                } else if (AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(vo.getTargetType())) {
                    AmazonSbAdKeyword keyword = processDto.getSbkeywordMap().get(vo.getItemId());
                    if (Objects.isNull(keyword)) {
                        continue;
                    }

                    vo.setItemName(keyword.getKeywordText());
                    vo.setKeywordText(keyword.getKeywordText());
                    vo.setKeywordId(keyword.getKeywordId());
                    vo.setType(QueryPageAdKeywordRespTypeEnum.keyword.name());
                    vo.setState(keyword.getState());
                    //没有服务状态
                    if (StringUtils.isNotBlank(keyword.getMatchType())) {
                        vo.setMatchName((MatchTypeEnum.getMatchValue(keyword.getMatchType())));
                        vo.setMatchType(keyword.getMatchType());
                    }

                    campaignId = keyword.getCampaignId();
                    groupId = keyword.getAdGroupId();
                    bid = Objects.nonNull(keyword.getBid()) ? keyword.getBid().setScale(2, RoundingMode.HALF_UP) : null;
                }

                //sb广告组数据
                if (StringUtils.isNotBlank(groupId)) {
                    AmazonSbAdGroup adGroup = processDto.getSbGroupMap().get(groupId);
                    if (Objects.nonNull(adGroup)) {
                        vo.setAdGroupName(adGroup.getName());
                        vo.setAdGroupId(adGroup.getAdGroupId());
                        //sb没有默认竞价
                        //defaultBid = Objects.nonNull(adGroup.getBid()) ? adGroup.getBid().setScale(2, RoundingMode.HALF_UP) : null;
                        Optional.ofNullable(adGroup.getAdFormat()).filter(StringUtils::isNotEmpty).ifPresent(vo::setAdFormat);
                    }
                }
            } else if (AutoRuleSubmitAdTypeEnum.SD.getCode().equals(vo.getAdType())) {
                AmazonSdAdTargeting targeting = processDto.getSdTargetMap().get(vo.getItemId());
                if (Objects.isNull(targeting)) {
                    continue;
                }

                vo.setItemName(targeting.getTargetText());
                vo.setKeywordText(targeting.getTargetText());
                vo.setType(targeting.getType());
                //填充类目和asin
                vo.setDetailTargetType(targeting.getTargetType());
                fillAsinAndBrandAndAudienceInfo4ControlledTarget(vo, targeting.getType(), targeting.getTitle(), targeting.getImgUrl(), targeting.getResolvedExpression());

                vo.setKeywordId(targeting.getTargetId());
                vo.setState(targeting.getState());
                if (StringUtils.isNotBlank(targeting.getServingStatus())) {
                    vo.setServingStatus(targeting.getServingStatus());
                    AmazonSdAdTargeting.servingStatusEnum byCode = UCommonUtil.getByCode(targeting.getServingStatus(), AmazonSdAdTargeting.servingStatusEnum.class);
                    if (byCode != null) {
                        vo.setServingStatusName(byCode.getName());
                        vo.setServingStatusDec(byCode.getDescription());
                    } else {
                        vo.setServingStatusDec(targeting.getServingStatus());
                        vo.setServingStatusName(targeting.getServingStatus());
                    }
                }
                bid = Objects.nonNull(targeting.getBid()) ? targeting.getBid().setScale(2, RoundingMode.HALF_UP) : null;

                //sd广告组数据
                if (StringUtils.isNotBlank(targeting.getAdGroupId())) {
                    AmazonSdAdGroup adGroup = processDto.getSdGroupMap().get(targeting.getAdGroupId());
                    if (Objects.nonNull(adGroup)) {
                        vo.setAdGroupName(adGroup.getName());
                        vo.setAdGroupId(adGroup.getAdGroupId());
                        defaultBid = Objects.nonNull(adGroup.getDefaultBid()) ? adGroup.getDefaultBid().setScale(2, RoundingMode.HALF_UP) : null;
                        //sd的活动id需要从广告组拿
                        campaignId = adGroup.getCampaignId();
                        targeting.setCampaignId(campaignId);
                    }
                }
            }

            //统一处理活动和组合
            if (StringUtils.isNotBlank(campaignId)) {
                AmazonAdCampaignAll campaignAll = processDto.getCampaignMap().get(campaignId);
                if (Objects.nonNull(campaignAll)) {
                    vo.setCampaignName(campaignAll.getName());
                    vo.setCampaignId(campaignAll.getCampaignId());
                    Optional.ofNullable(campaignAll.getCostType()).filter(StringUtils::isNotEmpty).ifPresent(vo::setCostType);
                    Optional.ofNullable(campaignAll.getAdGoal()).filter(StringUtils::isNotEmpty)
                            .map(SBCampaignGoalEnum::getSBCampaignGoalEnumByType)
                            .map(SBCampaignGoalEnum::getCode)
                            .map(String::valueOf)
                            .ifPresent(vo::setAdGoal);
                    if (StringUtils.isNotBlank(campaignAll.getPortfolioId())) {
                        AmazonAdPortfolio portfolio = processDto.getPortfolioMap().get(campaignAll.getPortfolioId());
                        if (Objects.nonNull(portfolio)) {
                            vo.setPortfolioId(portfolio.getPortfolioId());
                            vo.setPortfolioName(portfolio.getName());
                            vo.setPortfolioIsHidden(portfolio.getIsHidden());
                        }
                    }
                }
            }

            //统一处理竞价
            OriginValueVo originValueVo = null;
            if (StringUtils.isNotBlank(vo.getOriginValue())) {
                originValueVo = JSONUtil.jsonToObject(vo.getOriginValue(), OriginValueVo.class);
            }
            if (originValueVo != null && originValueVo.getBiddingValue() != null) {
                vo.setBiddingValue(originValueVo.getBiddingValue());
            } else {
                if (Objects.nonNull(bid)) {
                    vo.setBiddingValue(bid);
                } else if (Objects.nonNull(defaultBid)) {
                    vo.setBiddingValue(defaultBid);
                }
            }
        }
    }


    /**
     * 查询已添加的受控对象时填充asin标题，品牌，受众信息
     */
    private void fillAsinAndBrandAndAudienceInfo4ControlledTarget(AdvertiseAutoRuleStatus vo,
                                                                  String targetType,
                                                                  String title,
                                                                  String imgUrl,
                                                                  String resolvedExpression) {
        vo.setAsin(vo.getKeywordText());
        //asin信息
        if (TargetingTypeEnum.asin.getTagetType().equals(targetType)) {
            vo.setAsinTarget(true);
            vo.setTitle(title);
            vo.setImgUrl(imgUrl);
        } else {
            //如果为数字ID,表明类目或品牌已经被amazon删除
            if (StringUtils.isNumeric(vo.getKeywordText())) {
                vo.setKeywordText("此类目亚马逊已删除");
                vo.setItemName("此类目亚马逊已删除");
                vo.setAsin("此类目亚马逊已删除");
            }

            //类目和受众，从resolvedExpression中解析
            if (StringUtils.isNotBlank(resolvedExpression)) {
                fillBrandMessage4ControlledTarget(vo, resolvedExpression);
            } else {
                if (TargetingTypeEnum.category_target.getTagetType().equals(vo.getType())) {
                    vo.setCategory(vo.getKeywordText());
                }
            }

            //受众类型title
            SBTargetingAudienceTypeEnum detailTargetType = SBTargetingAudienceTypeEnum.fromValue(vo.getDetailTargetType());
            vo.setTitle(detailTargetType != null ? detailTargetType.getDesc() : null);
        }
    }


    private void fillBrandMessage4ControlledTarget(AdvertiseAutoRuleStatus vo, String resolvedExpression) {
        try {
            JSONArray jsonArray = JSONObject.parseArray(resolvedExpression);
            if (CollectionUtils.isEmpty(jsonArray)) {
                return;
            }
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String value = jsonObject.getString("type");
                // T00030 TargetingPredicateNested
                if (SBTargetingAudienceTypeEnum.fromValue(value) != null) {
                    JSONArray valueArray = ResolvedExpressionParseHelper.getJsonValueAsArray(jsonObject);
                    fillBrandMessage4ControlledTarget(vo, valueArray.toJSONString());
                }
                if (ExpressionEnum.asinCategorySameAs.value().equals(value) || SpV3ExpressionEnum.asinCategorySameAs.getValueV3().equals(value)) {
                    vo.setCategory(jsonObject.getString("value"));
                    //如果为数字ID,表明类目或品牌已经被amazon删除
                    if (StringUtils.isNumeric(vo.getCategory())) {
                        vo.setCategory("此类目亚马逊已删除");
                    }
                }
                if (ExpressionEnum.asinBrandSameAs.value().equals(value) || SpV3ExpressionEnum.asinBrandSameAs.getValueV3().equals(value)) {
                    vo.setBrandName(jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinPriceBetween.value().equals(value) || SpV3ExpressionEnum.asinPriceBetween.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value").replace("-", ","));
                }
                if (ExpressionEnum.asinPriceLessThan.value().equals(value) || SpV3ExpressionEnum.asinPriceLessThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及以下");
                }
                if (ExpressionEnum.asinPriceGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinPriceGreaterThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及更高");
                }
                if (ExpressionEnum.asinReviewRatingBetween.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingBetween.getValueV3().equals(value)) {
                    vo.setRating(jsonObject.getString("value").replace('-', '~'));
                }
                if (ExpressionEnum.asinReviewRatingLessThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingLessThan.getValueV3().equals(value)) {
                    vo.setRating("1~" + jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinReviewRatingGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingGreaterThan.getValueV3().equals(value)) {
                    vo.setRating(jsonObject.getString("value") + "~5");
                }
                if (ExpressionEnum.asinIsPrimeShippingEligible.value().equals(value) || SpV3ExpressionEnum.asinIsPrimeShippingEligible.getValueV3().equals(value)) {
                    if ("true".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("具有Prime资格");
                    } else if ("false".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("不具有Prime资格");
                    }
                }
                if (ExpressionEnum.lookback.value().equals(value)) {
                    vo.setLookback(jsonObject.getString("value"));
                }
            }

            String brandName = StringUtils.isNotBlank(vo.getBrandName()) ? vo.getBrandName() : BrandMessageConstants.DEFAULT_BRAND_NAME;
            String commodityPriceRange = StringUtils.isNotBlank(vo.getCommodityPriceRange()) ? vo.getCommodityPriceRange() : BrandMessageConstants.DEFAULT_COMMODITY_PRICE_RANGE;
            String rating = StringUtils.isNotBlank(vo.getRating()) ? vo.getRating() : BrandMessageConstants.DEFAULT_RATING;
            String distribution = StringUtils.isNotBlank(vo.getDistribution()) ? vo.getDistribution() : BrandMessageConstants.DEFAULT_DISTRIBUTION;

            vo.setBrandName(brandName);
            vo.setCommodityPriceRange(commodityPriceRange);
            vo.setRating(rating);
            vo.setDistribution(distribution);


        } catch (Exception e) {
            log.error("解析json失败", e);
        }
    }


    @Override
    public Result<Page<AdvertiseAutoRuleStatus>> pageAdControlledSearchQuery(AutoRuleObjectParam param) {
        Result<Page<AdvertiseAutoRuleStatus>> pageResult = new Result<>();
        List<AdvertiseAutoRuleStatus> list = Lists.newArrayList();
        Page<AdvertiseAutoRuleStatus> voPage = new Page<>(param.getPageNo(), param.getPageSize());
        try {
            if (CollectionUtils.isEmpty(param.getCampaignIds())) {
                if (CollectionUtils.isNotEmpty(param.getPortfolioIds())) {
                    List<String> campaignIdList = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdList(param.getPuid(), param.getShopId(), param.getPortfolioIds());
                    if (CollectionUtils.isNotEmpty(campaignIdList)) {
                        param.setCampaignIds(campaignIdList);
                    } else {
                        pageResult.setCode(Result.SUCCESS);
                        pageResult.setData(voPage);
                        return pageResult;
                    }
                }
            }
            AdvertiseAutoRuleTemplate advertiseAutoRuleTemplate = advertiseAutoRuleTemplateDao.selectByPrimaryKey(param.getPuid(), param.getTemplateId());
            List<AdvertiseAutoRuleStatus> advertiseAutoRuleStatusList = advertiseAutoRuleStatusDao.selectAdControlledList(param);
            if (CollectionUtils.isNotEmpty(advertiseAutoRuleStatusList)) {
                List<String> itemIdList = advertiseAutoRuleStatusList.stream().map(AdvertiseAutoRuleStatus::getItemId).collect(Collectors.toList());
                List<CpcQueryKeywordReport> adQueryAutoRuleVoList = null;
                List<CpcQueryTargetingReport> cpcQueryTargetingReportList = null;
                List<CpcSbQueryKeywordReport> cpcSbQueryKeywordReportList = null;
                adQueryAutoRuleVoList = iCpcQueryKeywordReportDao.listSpByKeywordRule(param, itemIdList);
                cpcQueryTargetingReportList = iCpcQueryTargetingReportDao.listSpByTargetRule(param, itemIdList);
                if (CollectionUtils.isEmpty(param.getServingStatusList())) {
                    cpcSbQueryKeywordReportList = iCpcSbQueryKeywordReportDao.listSbByKeywordRule(param, itemIdList);
                }
                Map<String, CpcQueryKeywordReport> adQueryAutoRuleVoMap = null;
                Map<String, CpcQueryTargetingReport> cpcQueryTargetingReportMap = null;
                Map<String, CpcSbQueryKeywordReport> cpcSbQueryKeywordReportMap = null;
                Map<String, AmazonAdCampaignAll> campaignSpKeywordMap = null;
                Map<String, AmazonAdGroup> amazonAdSpKeywordGroupMap = null;
                Map<String, AmazonAdCampaignAll> campaignSpTargetMap = null;
                Map<String, AmazonAdGroup> amazonAdGroupSpTargetMap = null;
                Map<String, AmazonAdCampaignAll> campaignSbKeywordMap = null;
                Map<String, AmazonSbAdGroup> amazonSbAdGroupMap = null;
                Map<String, AmazonAdPortfolio> adQueryPortfolioMap = null;
                Map<String, AmazonAdPortfolio> adTargetPortfolioMap = null;
                Map<String, AmazonAdPortfolio> adKeywordPortfolioMap = null;
                List<String> targetIds = new ArrayList<>();
                Map<String, AmazonAdTargeting> targetMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(adQueryAutoRuleVoList)) {
                    adQueryAutoRuleVoMap = adQueryAutoRuleVoList.stream().filter(Objects::nonNull).
                            collect(Collectors.toMap(s -> s.getQueryId(), e -> e, (e1, e2) -> e1));
                    //提取活动id
                    List<String> campaignIds = adQueryAutoRuleVoList.stream().
                            map(CpcQueryKeywordReport::getCampaignId).collect(Collectors.toList());
                    //提取广告组id
                    List<String> groupIds = adQueryAutoRuleVoList.stream().
                            map(CpcQueryKeywordReport::getAdGroupId).collect(Collectors.toList());
                    //批量获取广告活动信息
                    List<AmazonAdCampaignAll> campaignAlls = amazonAdCampaignAllDao.
                            listByCampaignIdNoType(param.getPuid(), param.getShopId(), campaignIds);
                    if (CollectionUtils.isNotEmpty(campaignAlls)) {
                        campaignSpKeywordMap = campaignAlls.stream().filter(Objects::nonNull).
                                collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                        List<String> portfolioIds = campaignAlls.stream().map(AmazonAdCampaignAll::getPortfolioId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                        List<AmazonAdPortfolio> amazonAdPortfolios = amazonAdPortfolioDao.getPortfolioList(param.getPuid(), param.getShopId(), portfolioIds);
                        if (CollectionUtils.isNotEmpty(amazonAdPortfolios)) {
                            adQueryPortfolioMap = amazonAdPortfolios.stream().filter(Objects::nonNull).
                                    collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                        }
                    }
                    //批量获取广告组信息
                    List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.getAdGroupByIds
                            (param.getPuid(), param.getShopId(), null, groupIds);
                    if (CollectionUtils.isNotEmpty(amazonAdGroups)) {
                        amazonAdSpKeywordGroupMap = amazonAdGroups.stream().filter(Objects::nonNull).
                                collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e));
                    }
                }
                if (CollectionUtils.isNotEmpty(cpcQueryTargetingReportList)) {
                    cpcQueryTargetingReportMap = cpcQueryTargetingReportList.stream().filter(Objects::nonNull).
                            collect(Collectors.toMap(CpcQueryTargetingReport::getQueryId, e -> e, (e1, e2) -> e1));
                    //提取活动id
                    List<String> campaignIds = cpcQueryTargetingReportList.stream().
                            map(CpcQueryTargetingReport::getCampaignId).collect(Collectors.toList());
                    //提取广告组id
                    List<String> groupIds = cpcQueryTargetingReportList.stream().
                            map(CpcQueryTargetingReport::getAdGroupId).collect(Collectors.toList());
                    //批量获取广告活动信息
                    List<AmazonAdCampaignAll> campaignAlls = amazonAdCampaignAllDao.
                            listByCampaignIdNoType(param.getPuid(), param.getShopId(), campaignIds);
                    if (CollectionUtils.isNotEmpty(campaignAlls)) {
                        campaignSpTargetMap = campaignAlls.stream().filter(Objects::nonNull).
                                collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                        List<String> portfolioIds = campaignAlls.stream().map(AmazonAdCampaignAll::getPortfolioId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                        List<AmazonAdPortfolio> amazonAdPortfolios = amazonAdPortfolioDao.getPortfolioList(param.getPuid(), param.getShopId(), portfolioIds);
                        if (CollectionUtils.isNotEmpty(amazonAdPortfolios)) {
                            adTargetPortfolioMap = amazonAdPortfolios.stream().filter(Objects::nonNull).
                                    collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                        }
                    }
                    //批量获取广告组信息
                    List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.getAdGroupByIds
                            (param.getPuid(), param.getShopId(), null, groupIds);
                    if (CollectionUtils.isNotEmpty(amazonAdGroups)) {
                        amazonAdGroupSpTargetMap = amazonAdGroups.stream().filter(Objects::nonNull).
                                collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e));
                    }
                }
                if (CollectionUtils.isNotEmpty(cpcSbQueryKeywordReportList)) {
                    cpcSbQueryKeywordReportMap = cpcSbQueryKeywordReportList.stream().filter(Objects::nonNull).
                            collect(Collectors.toMap(s -> s.getQueryId(), e -> e, (e1, e2) -> e1));
                    //提取活动id
                    List<String> campaignIds = cpcSbQueryKeywordReportList.stream().
                            map(CpcSbQueryKeywordReport::getCampaignId).collect(Collectors.toList());
                    //提取广告组id
                    List<String> groupIds = cpcSbQueryKeywordReportList.stream().
                            map(CpcSbQueryKeywordReport::getAdGroupId).collect(Collectors.toList());
                    //批量获取广告活动信息
                    List<AmazonAdCampaignAll> campaignAlls = amazonAdCampaignAllDao.
                            listByCampaignIdNoType(param.getPuid(), param.getShopId(), campaignIds);
                    if (CollectionUtils.isNotEmpty(campaignAlls)) {
                        campaignSbKeywordMap = campaignAlls.stream().filter(Objects::nonNull).
                                collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                        List<String> portfolioIds = campaignAlls.stream().map(AmazonAdCampaignAll::getPortfolioId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                        List<AmazonAdPortfolio> amazonAdPortfolios = amazonAdPortfolioDao.getPortfolioList(param.getPuid(), param.getShopId(), portfolioIds);
                        if (CollectionUtils.isNotEmpty(amazonAdPortfolios)) {
                            adKeywordPortfolioMap = amazonAdPortfolios.stream().filter(Objects::nonNull).
                                    collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                        }
                    }
                    //批量获取广告组信息
                    List<AmazonSbAdGroup> amazonSbAdGroups = amazonSbAdGroupDao.getAdGroupByIds
                            (param.getPuid(), param.getShopId(), groupIds);
                    if (CollectionUtils.isNotEmpty(amazonSbAdGroups)) {
                        amazonSbAdGroupMap = amazonSbAdGroups.stream().filter(Objects::nonNull).
                                collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, e -> e));
                    }
                }
                //循环处理数据
                for (AdvertiseAutoRuleStatus vo : advertiseAutoRuleStatusList) {
                    AdvertiseAutoRuleStatus advertiseAutoRuleStatus = vo;
                    MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(vo.getMarketplaceId());
                    if (null != m) {
                        advertiseAutoRuleStatus.setCurrency(m.getCurrencyCode());
                    }
                    if (vo.getVersion() >= advertiseAutoRuleTemplate.getVersion()) {
                        vo.setUpdateStatus("update");
                    } else {
                        vo.setUpdateStatus("notUpdate");
                    }
                    if (MapUtils.isNotEmpty(adQueryAutoRuleVoMap) && adQueryAutoRuleVoMap.containsKey(advertiseAutoRuleStatus.getItemId())) {
                        CpcQueryKeywordReport adQueryAutoRuleVo = adQueryAutoRuleVoMap.get(advertiseAutoRuleStatus.getItemId());
                        advertiseAutoRuleStatus.setItemName(adQueryAutoRuleVo.getQuery());
                        advertiseAutoRuleStatus.setQuery(adQueryAutoRuleVo.getQuery());
                        advertiseAutoRuleStatus.setQueryId(adQueryAutoRuleVo.getQueryId());
                        advertiseAutoRuleStatus.setAdType(vo.getAdType());
                        advertiseAutoRuleStatus.setState(adQueryAutoRuleVo.getState());
                        if (StringUtils.isNotBlank(adQueryAutoRuleVo.getTargetKeywordText())) {
                            String keywordText = AsinMatchValueEnum.getAsinMatchValue(adQueryAutoRuleVo.getTargetKeywordText());
                            if (StringUtils.isNotBlank(keywordText)) {
                                advertiseAutoRuleStatus.setKeywordText(keywordText);
                            } else {
                                advertiseAutoRuleStatus.setKeywordText(adQueryAutoRuleVo.getTargetKeywordText());
                            }
                        } else {
                            advertiseAutoRuleStatus.setKeywordText("-");
                        }
                        if (MapUtils.isNotEmpty(campaignSpKeywordMap)) {
                            AmazonAdCampaignAll campaignAll = campaignSpKeywordMap.get(adQueryAutoRuleVo.getCampaignId());
                            if (Objects.nonNull(campaignAll)) {
                                advertiseAutoRuleStatus.setCampaignName(campaignAll.getName());
                                advertiseAutoRuleStatus.setCampaignId(adQueryAutoRuleVo.getCampaignId());
                                if (MapUtils.isNotEmpty(adQueryPortfolioMap) && StringUtils.isNotBlank(campaignAll.getPortfolioId())) {
                                    AmazonAdPortfolio portfolio = adQueryPortfolioMap.get(campaignAll.getPortfolioId());
                                    if (Objects.nonNull(portfolio)) {
                                        advertiseAutoRuleStatus.setPortfolioId(campaignAll.getPortfolioId());
                                        advertiseAutoRuleStatus.setPortfolioName(portfolio.getName());
                                        advertiseAutoRuleStatus.setPortfolioIsHidden(portfolio.getIsHidden());
                                    }
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(amazonAdSpKeywordGroupMap) && amazonAdSpKeywordGroupMap.containsKey(adQueryAutoRuleVo.getAdGroupId())) {
                            advertiseAutoRuleStatus.setAdGroupName(amazonAdSpKeywordGroupMap.get(adQueryAutoRuleVo.getAdGroupId()).getName());
                            advertiseAutoRuleStatus.setAdGroupId(adQueryAutoRuleVo.getAdGroupId());
                        }
                        if (StringUtils.isNotBlank(adQueryAutoRuleVo.getMatchType())) {
                            advertiseAutoRuleStatus.setMatchName((MatchTypeEnum.getMatchValue(adQueryAutoRuleVo.getMatchType())));
                            advertiseAutoRuleStatus.setMatchType(adQueryAutoRuleVo.getMatchType());
                        }
                        if (StringUtils.isNotBlank(adQueryAutoRuleVo.getServingStatus())) {
                            advertiseAutoRuleStatus.setServingStatus(adQueryAutoRuleVo.getServingStatus());
                            AmazonAdKeyword.servingStatusEnum byCode = UCommonUtil.getByCode(adQueryAutoRuleVo.getServingStatus(), AmazonAdKeyword.servingStatusEnum.class);
                            if (byCode != null) {
                                advertiseAutoRuleStatus.setServingStatusName(byCode.getName());
                                vo.setServingStatusDec(byCode.getDescription());
                            } else {
                                advertiseAutoRuleStatus.setServingStatusDec(adQueryAutoRuleVo.getServingStatus());
                                advertiseAutoRuleStatus.setServingStatusName(adQueryAutoRuleVo.getServingStatus());
                            }
                        }
                    } else if (MapUtils.isNotEmpty(cpcQueryTargetingReportMap) && cpcQueryTargetingReportMap.containsKey(advertiseAutoRuleStatus.getItemId())) {
                        CpcQueryTargetingReport cpcQueryTargetingReport = cpcQueryTargetingReportMap.get(advertiseAutoRuleStatus.getItemId());
                        advertiseAutoRuleStatus.setItemName(cpcQueryTargetingReport.getQuery());
                        advertiseAutoRuleStatus.setAdType(vo.getAdType());
                        if (StringUtils.isNotBlank(cpcQueryTargetingReport.getQuery())) {
                            if (cpcQueryTargetingReport.getQuery().matches(ASIN_REGEX)) {
                                if (Constants.TARGETING_EXPRESSION_PREDEFINED.equals(cpcQueryTargetingReport.getTargetingType())) {
                                    // 投放值
                                    if ("close-match".equalsIgnoreCase(cpcQueryTargetingReport.getMatchType())) {
                                        advertiseAutoRuleStatus.setMatchType("close_match");
                                    } else if ("loose-match".equalsIgnoreCase(cpcQueryTargetingReport.getMatchType())) {
                                        advertiseAutoRuleStatus.setMatchType("loose_match");
                                    } else if ("substitutes".equalsIgnoreCase(cpcQueryTargetingReport.getMatchType())) {
                                        advertiseAutoRuleStatus.setMatchType("substitutes");
                                    } else if ("complements".equalsIgnoreCase(cpcQueryTargetingReport.getMatchType())) {
                                        advertiseAutoRuleStatus.setMatchType("complements");
                                    } else {
                                        advertiseAutoRuleStatus.setMatchType(cpcQueryTargetingReport.getMatchType());
                                    }
                                    advertiseAutoRuleStatus.setKeywordText("自动投放组");
                                } else {
                                    targetIds.add(cpcQueryTargetingReport.getTargetId());
                                    advertiseAutoRuleStatus.setTargetId(cpcQueryTargetingReport.getTargetId());
                                    String expression = cpcQueryTargetingReport.getTargetingExpression();
                                    if (expression.startsWith("asin=")) {
                                        advertiseAutoRuleStatus.setKeywordText(expression.substring("asin=".length()));
                                    } else if (expression.startsWith("asin-expanded=")) {
                                        String keywords = expression.substring("asin-expanded=".length());
                                        if (keywords.startsWith("\"") && keywords.endsWith("\"")) {
                                            advertiseAutoRuleStatus.setKeywordText(keywords.substring(1, keywords.length() - 1).toUpperCase());
                                        }
                                    } else {
                                        advertiseAutoRuleStatus.setKeywordText(expression);
                                    }
                                }
//                                if (StringUtils.isNotBlank(cpcQueryTargetingReport.getTargetingExpression())) {
//                                    advertiseAutoRuleStatus.setKeywordText(cpcQueryTargetingReport.getTargetingExpression());
//                                } else {
//                                    advertiseAutoRuleStatus.setKeywordText("-");
//                                }
                            } else {
                                if (StringUtils.isNotBlank(cpcQueryTargetingReport.getMatchType())) {
                                    advertiseAutoRuleStatus.setMatchName(AsinMatchValueEnum.getAsinMatchValue(cpcQueryTargetingReport.getMatchType()));
                                    advertiseAutoRuleStatus.setMatchType(cpcQueryTargetingReport.getMatchType());
                                }
                                advertiseAutoRuleStatus.setKeywordText("自动投放组");
                            }
                        }
                        advertiseAutoRuleStatus.setQuery(cpcQueryTargetingReport.getQuery());
                        advertiseAutoRuleStatus.setQueryId(cpcQueryTargetingReport.getQueryId());
                        advertiseAutoRuleStatus.setState(cpcQueryTargetingReport.getState());
                        if (MapUtils.isNotEmpty(campaignSpTargetMap) && campaignSpTargetMap.containsKey(cpcQueryTargetingReport.getCampaignId())) {
                            advertiseAutoRuleStatus.setCampaignName(campaignSpTargetMap.get(cpcQueryTargetingReport.getCampaignId()).getName());
                            advertiseAutoRuleStatus.setCampaignId(cpcQueryTargetingReport.getCampaignId());
                            if (MapUtils.isNotEmpty(adTargetPortfolioMap) && StringUtils.isNotBlank(campaignSpTargetMap.get(cpcQueryTargetingReport.getCampaignId()).getPortfolioId()) && adTargetPortfolioMap.containsKey(campaignSpTargetMap.get(cpcQueryTargetingReport.getCampaignId()).getPortfolioId())) {
                                advertiseAutoRuleStatus.setPortfolioId(campaignSpTargetMap.get(cpcQueryTargetingReport.getCampaignId()).getPortfolioId());
                                advertiseAutoRuleStatus.setPortfolioName(adTargetPortfolioMap.get(campaignSpTargetMap.get(cpcQueryTargetingReport.getCampaignId()).getPortfolioId()).getName());
                            }
                        }
                        if (MapUtils.isNotEmpty(amazonAdGroupSpTargetMap) && amazonAdGroupSpTargetMap.containsKey(cpcQueryTargetingReport.getAdGroupId())) {
                            advertiseAutoRuleStatus.setAdGroupName(amazonAdGroupSpTargetMap.get(cpcQueryTargetingReport.getAdGroupId()).getName());
                            advertiseAutoRuleStatus.setAdGroupId(cpcQueryTargetingReport.getAdGroupId());
                        }
                        if (StringUtils.isNotBlank(cpcQueryTargetingReport.getServingStatus())) {
                            advertiseAutoRuleStatus.setServingStatus(cpcQueryTargetingReport.getServingStatus());
                            AmazonAdTargeting.servingStatusEnum byCode = UCommonUtil.getByCode(cpcQueryTargetingReport.getServingStatus(), AmazonAdTargeting.servingStatusEnum.class);
                            if (byCode != null) {
                                advertiseAutoRuleStatus.setServingStatusName(byCode.getName());
                                advertiseAutoRuleStatus.setServingStatusDec(byCode.getDescription());
                            } else {
                                advertiseAutoRuleStatus.setServingStatusDec(cpcQueryTargetingReport.getServingStatus());
                                advertiseAutoRuleStatus.setServingStatusName(cpcQueryTargetingReport.getServingStatus());
                            }
                        }
                        if (StringUtils.isNotBlank(vo.getMatchType())) {
                            advertiseAutoRuleStatus.setMatchName(AmazonAd.MatchTypeEnum.getMatchName(vo.getMatchType().toUpperCase()));
                        }
                    } else if (MapUtils.isNotEmpty(cpcSbQueryKeywordReportMap) && cpcSbQueryKeywordReportMap.containsKey(advertiseAutoRuleStatus.getItemId())) {
                        CpcSbQueryKeywordReport cpcSbQueryKeywordReport = cpcSbQueryKeywordReportMap.get(advertiseAutoRuleStatus.getItemId());
                        advertiseAutoRuleStatus.setItemName(cpcSbQueryKeywordReport.getQuery());
                        advertiseAutoRuleStatus.setAdType(vo.getAdType());
                        if (StringUtils.isNotBlank(cpcSbQueryKeywordReport.getTargetKeywordText())) {
                            advertiseAutoRuleStatus.setKeywordText(cpcSbQueryKeywordReport.getTargetKeywordText());
                        } else {
                            advertiseAutoRuleStatus.setKeywordText("-");
                        }
                        advertiseAutoRuleStatus.setQuery(cpcSbQueryKeywordReport.getQuery());
                        advertiseAutoRuleStatus.setQueryId(cpcSbQueryKeywordReport.getQueryId());
                        advertiseAutoRuleStatus.setState(cpcSbQueryKeywordReport.getState());
                        if (MapUtils.isNotEmpty(campaignSbKeywordMap) && campaignSbKeywordMap.containsKey(cpcSbQueryKeywordReport.getCampaignId())) {
                            advertiseAutoRuleStatus.setCampaignName(campaignSbKeywordMap.get(cpcSbQueryKeywordReport.getCampaignId()).getName());
                            advertiseAutoRuleStatus.setCampaignId(cpcSbQueryKeywordReport.getCampaignId());
                            if (MapUtils.isNotEmpty(adKeywordPortfolioMap) && StringUtils.isNotBlank(campaignSbKeywordMap.get(cpcSbQueryKeywordReport.getCampaignId()).getPortfolioId()) && adKeywordPortfolioMap.containsKey(campaignSbKeywordMap.get(cpcSbQueryKeywordReport.getCampaignId()).getPortfolioId())) {
                                advertiseAutoRuleStatus.setPortfolioId(campaignSbKeywordMap.get(cpcSbQueryKeywordReport.getCampaignId()).getPortfolioId());
                                advertiseAutoRuleStatus.setPortfolioName(adKeywordPortfolioMap.get(campaignSbKeywordMap.get(cpcSbQueryKeywordReport.getCampaignId()).getPortfolioId()).getName());
                            }
                        }
                        if (MapUtils.isNotEmpty(amazonSbAdGroupMap) && amazonSbAdGroupMap.containsKey(cpcSbQueryKeywordReport.getAdGroupId())) {
                            advertiseAutoRuleStatus.setAdGroupName(amazonSbAdGroupMap.get(cpcSbQueryKeywordReport.getAdGroupId()).getName());
                            advertiseAutoRuleStatus.setAdGroupId(cpcSbQueryKeywordReport.getAdGroupId());
                        }
                        if (StringUtils.isNotBlank(cpcSbQueryKeywordReport.getMatchType())) {
                            advertiseAutoRuleStatus.setMatchName((MatchTypeEnum.getMatchValue(cpcSbQueryKeywordReport.getMatchType())));
                            advertiseAutoRuleStatus.setMatchType(cpcSbQueryKeywordReport.getMatchType());
                        }
                        if (StringUtils.isNotBlank(cpcSbQueryKeywordReport.getServingStatus())) {
                            advertiseAutoRuleStatus.setServingStatus(cpcSbQueryKeywordReport.getServingStatus());
                            AmazonAdKeyword.servingStatusEnum byCode = UCommonUtil.getByCode(cpcSbQueryKeywordReport.getServingStatus(), AmazonAdKeyword.servingStatusEnum.class);
                            if (byCode != null) {
                                advertiseAutoRuleStatus.setServingStatusName(byCode.getName());
                                vo.setServingStatusDec(byCode.getDescription());
                            } else {
                                advertiseAutoRuleStatus.setServingStatusDec(cpcSbQueryKeywordReport.getServingStatus());
                                advertiseAutoRuleStatus.setServingStatusName(cpcSbQueryKeywordReport.getServingStatus());
                            }
                        }
                    } else {
                        continue;
                    }
                    list.add(advertiseAutoRuleStatus);
                }
                if (CollectionUtils.isNotEmpty(targetIds)) {
                    targetMap = amazonAdTargetingShardingDao.getByAdTargetIds(param.getPuid(),
                                    param.getShopId(), targetIds)
                            .stream().collect(Collectors.toMap(AmazonAdTargeting::getTargetId, Function.identity()));
                }
                List<String> asins = list.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordText()) && Pattern.compile(Constants.ASIN_REGEX).matcher(e.getKeywordText()).matches() && StringUtils.isBlank(e.getImgUrl())).map(e -> e.getKeywordText().toUpperCase()).distinct().collect(Collectors.toList());
                Map<String, AsinImage> asinMap = new HashMap<>();
                ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
                if (CollectionUtils.isNotEmpty(asins)) {
                    List<AsinImage> listByAsins = syncAsinImageService.getListByAsinsNoSave(shopAuth.getPuid(), shopAuth.getMarketplaceId(), asins);
                    asinMap = listByAsins.stream().filter(e -> StringUtils.isNotBlank(e.getAsin())).collect(Collectors.toMap(e -> e.getAsin().toUpperCase(), e1 -> e1, (e2, e3) -> e3));
                }
                //设置商品投放的图片和title信息/类目的相关信息
                for (AdvertiseAutoRuleStatus vo : list) {
                    if (targetMap.containsKey(vo.getTargetId())) {
                        AmazonAdTargeting amazonAdTargeting = targetMap.get(vo.getTargetId());
                        //类目投放则补充类目投放信息
                        if (Objects.nonNull(amazonAdTargeting) && TargetTypeEnum.category.name().equals(amazonAdTargeting.getType())) {
                            vo.setMatchType(amazonAdTargeting.getType());
                            vo.setMatchName("类目");
                            vo.setCategory(amazonAdTargeting.getTargetingValue());
                            vo.setKeywordText(amazonAdTargeting.getTargetingValue());
                            // 改版前路径是存在这个字段里的，因为要支持列表页模糊搜索改到了targeting_value
                            if (StringUtils.isNotBlank(amazonAdTargeting.getCategoryPath())) {
                                vo.setCategory(amazonAdTargeting.getCategoryPath());
                            }
                            //如果为数字ID,表明类目或品牌已经被amazon删除
                            if (StringUtils.isNumeric(vo.getCategory())) {
                                vo.setCategory("此类目亚马逊已删除");
                            }
                        }
                        //商品投放切为非自动投放则补ASIN的相关信息
                        if (Objects.nonNull(amazonAdTargeting) && TargetTypeEnum.asin.name().equals(amazonAdTargeting.getType())) {
                            vo.setMatchType(amazonAdTargeting.getSelectType());
                            vo.setMatchName(AmazonAd.MatchTypeEnum.getMatchName(amazonAdTargeting.getSelectType().toUpperCase()));
                            vo.setKeywordText(amazonAdTargeting.getTargetingValue().toUpperCase());
                            vo.setImgUrl(amazonAdTargeting.getImgUrl());
                            vo.setTitle(amazonAdTargeting.getTitle());
                            if (StringUtils.isNotBlank(vo.getKeywordText()) && (StringUtils.isBlank(vo.getTitle()) || StringUtils.isBlank(vo.getImgUrl()))) {
                                AsinImage asinImage = asinMap.get(vo.getKeywordText().toUpperCase());
                                if (asinImage != null) {
                                    Optional.ofNullable(asinImage.getTitle()).filter(StringUtils::isNotEmpty).ifPresent(t -> {
                                        if (StringUtils.isEmpty(vo.getTitle())) {
                                            vo.setTitle(t);
                                        }
                                    });
                                    Optional.ofNullable(asinImage.getImage()).filter(StringUtils::isNotEmpty).ifPresent(m -> {
                                        if (StringUtils.isEmpty(vo.getImgUrl())) {
                                            vo.setImgUrl(m);
                                        }
                                    });
                                }
                            }
                        }
                        if (amazonAdTargeting != null && StringUtils.isNotBlank(amazonAdTargeting.getResolvedExpression()) && TargetTypeEnum.category.name().equalsIgnoreCase(amazonAdTargeting.getType())) {
                            JSONArray jsonArray = JSONArray.parseArray(amazonAdTargeting.getResolvedExpression());
                            if (jsonArray != null && !jsonArray.isEmpty()) {
                                this.fillBrandMessage(vo, jsonArray);
                            }
                        }
                        if (StringUtils.isNotBlank(vo.getMatchType())) {
                            vo.setMatchName(AmazonAd.MatchTypeEnum.getMatchName(vo.getMatchType().toUpperCase()));
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(list)) {
                voPage = PageUtil.getPage(voPage, list);
            }
            pageResult.setCode(Result.SUCCESS);
            pageResult.setData(voPage);
        } catch (Exception e) {
            pageResult.setCode(Result.ERROR);
            pageResult.setMsg("查询关键词受控对象异常");
            log.error("traceId:{} puid:{} shopId:{} 查询搜索词受控对象异常", param.getTraceId(), param.getPuid(), param.getShopId(), e);
        }
        return pageResult;
    }


    private List<AdvertiseAutoRuleStatus> pageAdControlledKeyword(AutoRuleObjectParam param) {
        AdvertiseAutoRuleTemplate advertiseAutoRuleTemplate = advertiseAutoRuleTemplateDao.selectByPrimaryKey(param.getPuid(), param.getTemplateId());
        List<AdvertiseAutoRuleStatus> advertiseAutoRuleStatusList = advertiseAutoRuleStatusDao.selectAdControlledList(param);
        List<AdvertiseAutoRuleStatus> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(advertiseAutoRuleStatusList)) {
            List<String> keywordIds = advertiseAutoRuleStatusList.stream().map(AdvertiseAutoRuleStatus::getItemId).collect(Collectors.toList());
            //根据关键ID查询出关键词信息
            List<AmazonAdKeyword> amazonAdKeywords = amazonAdKeywordDaoRoutingService.
                    autoRuleKeyword(param.getPuid(), param.getShopId(), param.getCampaignIds(), param.getGroupIds(), param.getState(), param.getMatchType(), param.getSearchValue(), keywordIds, param.getServingStatusList());
            Map<String, AmazonAdKeyword> amazonAdKeywordMap = null;
            Map<String, AmazonAdCampaignAll> campaignMap = null;
            Map<String, AmazonAdGroup> amazonAdGroupMap = null;
            Map<String, AmazonAdPortfolio> amazonAdPortfolioMap = null;
            if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                amazonAdKeywordMap = amazonAdKeywords.stream().filter(Objects::nonNull).
                        collect(Collectors.toMap(AmazonAdKeyword::getKeywordId, e -> e));
                //提取活动id
                List<String> campaignIds = amazonAdKeywords.stream().
                        map(AmazonAdKeyword::getCampaignId).collect(Collectors.toList());
                //提取广告组id
                List<String> groupIds = amazonAdKeywords.stream().
                        map(AmazonAdKeyword::getAdGroupId).collect(Collectors.toList());
                //批量获取广告活动信息
                List<AmazonAdCampaignAll> campaignAlls = amazonAdCampaignAllDao.
                        listByCampaignIdNoType(param.getPuid(), param.getShopId(), campaignIds);
                if (CollectionUtils.isNotEmpty(campaignAlls)) {
                    campaignMap = campaignAlls.stream().filter(Objects::nonNull).
                            collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                    List<String> portfolioIds = campaignAlls.stream().map(AmazonAdCampaignAll::getPortfolioId).
                            distinct().collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(portfolioIds)) {
                        List<AmazonAdPortfolio> portfolioList = amazonAdPortfolioDao.getPortfolioList(param.getPuid(), param.getShopId(), portfolioIds);
                        if (CollectionUtils.isNotEmpty(portfolioList)) {
                            amazonAdPortfolioMap = portfolioList.stream().filter(Objects::nonNull).
                                    collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                        }
                    }
                }
                //批量获取广告组信息
                List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.getAdGroupByIds
                        (param.getPuid(), param.getShopId(), null, groupIds);
                if (CollectionUtils.isNotEmpty(amazonAdGroups)) {
                    amazonAdGroupMap = amazonAdGroups.stream().filter(Objects::nonNull).
                            collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e));
                }
                //循环处理数据
                for (AdvertiseAutoRuleStatus vo : advertiseAutoRuleStatusList) {
                    AdvertiseAutoRuleStatus advertiseAutoRuleStatus = vo;
                    MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(vo.getMarketplaceId());
                    if (null != m) {
                        advertiseAutoRuleStatus.setCurrency(m.getCurrencyCode());
                    }
                    if (vo.getVersion() >= advertiseAutoRuleTemplate.getVersion()) {
                        vo.setUpdateStatus("update");
                    } else {
                        vo.setUpdateStatus("notUpdate");
                    }
                    if (MapUtils.isNotEmpty(amazonAdKeywordMap) && amazonAdKeywordMap.containsKey(advertiseAutoRuleStatus.getItemId())) {
                        AmazonAdKeyword amazonAdKeyword = amazonAdKeywordMap.get(advertiseAutoRuleStatus.getItemId());
                        advertiseAutoRuleStatus.setItemName(amazonAdKeyword.getKeywordText());
                        advertiseAutoRuleStatus.setKeywordText(amazonAdKeyword.getKeywordText());
                        advertiseAutoRuleStatus.setKeywordId(amazonAdKeyword.getKeywordId());
                        advertiseAutoRuleStatus.setState(amazonAdKeyword.getState());
                        advertiseAutoRuleStatus.setAdType(vo.getAdType());
                        if (StringUtils.isNotBlank(amazonAdKeyword.getServingStatus())) {
                            advertiseAutoRuleStatus.setServingStatus(amazonAdKeyword.getServingStatus());
                            AmazonAdKeyword.servingStatusEnum byCode = UCommonUtil.getByCode(amazonAdKeyword.getServingStatus(), AmazonAdKeyword.servingStatusEnum.class);
                            if (byCode != null) {
                                advertiseAutoRuleStatus.setServingStatusName(byCode.getName());
                                advertiseAutoRuleStatus.setServingStatusDec(byCode.getDescription());
                            } else {
                                advertiseAutoRuleStatus.setServingStatusDec(amazonAdKeyword.getServingStatus());
                                advertiseAutoRuleStatus.setServingStatusName(amazonAdKeyword.getServingStatus());
                            }
                        }
                        if (MapUtils.isNotEmpty(campaignMap) && campaignMap.containsKey(amazonAdKeyword.getCampaignId())) {
                            advertiseAutoRuleStatus.setCampaignName(campaignMap.get(amazonAdKeyword.getCampaignId()).getName());
                            advertiseAutoRuleStatus.setCampaignId(amazonAdKeyword.getCampaignId());
                            if (MapUtils.isNotEmpty(amazonAdPortfolioMap) && amazonAdPortfolioMap.containsKey(campaignMap.
                                    get(amazonAdKeyword.getCampaignId()).getPortfolioId())) {
                                advertiseAutoRuleStatus.setPortfolioId(campaignMap.get(amazonAdKeyword.getCampaignId()).getPortfolioId());
                                advertiseAutoRuleStatus.setPortfolioName(amazonAdPortfolioMap.get(campaignMap.get(amazonAdKeyword.getCampaignId()).getPortfolioId()).getName());
                            }
                        }
                        if (MapUtils.isNotEmpty(amazonAdGroupMap) && amazonAdGroupMap.containsKey(amazonAdKeyword.getAdGroupId())) {
                            advertiseAutoRuleStatus.setAdGroupName(amazonAdGroupMap.get(amazonAdKeyword.getAdGroupId()).getName());
                            advertiseAutoRuleStatus.setAdGroupId(amazonAdKeyword.getAdGroupId());
                            OriginValueVo originValueVo = null;
                            if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getOriginValue())) {
                                originValueVo = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getOriginValue(), OriginValueVo.class);
                            }
                            if (originValueVo != null && originValueVo.getBiddingValue() != null) {
                                advertiseAutoRuleStatus.setBiddingValue(originValueVo.getBiddingValue());
                            } else {
                                if (amazonAdKeyword.getBid() != null) {
                                    advertiseAutoRuleStatus.setBiddingValue(BigDecimal.valueOf(amazonAdKeyword.getBid()).
                                            setScale(2, RoundingMode.HALF_UP));
                                } else {
                                    if (amazonAdGroupMap.get(amazonAdKeyword.getAdGroupId()).getDefaultBid() != null) {
                                        advertiseAutoRuleStatus.setBiddingValue(BigDecimal.valueOf(amazonAdGroupMap.get(amazonAdKeyword.getAdGroupId()).getDefaultBid()).
                                                setScale(2, RoundingMode.HALF_UP));
                                    }
                                }
                            }
                        }
                        if (StringUtils.isNotBlank(amazonAdKeyword.getMatchType())) {
                            advertiseAutoRuleStatus.setMatchName((MatchTypeEnum.getMatchValue(amazonAdKeyword.getMatchType())));
                            advertiseAutoRuleStatus.setMatchType(amazonAdKeyword.getMatchType());
                        }
                    } else {
                        continue;
                    }
                    list.add(advertiseAutoRuleStatus);
                }
            }
        }
        return list;
    }

    private List<AdvertiseAutoRuleStatus> pageAdControlledAutoTarget(AutoRuleObjectParam param) {
        AdvertiseAutoRuleTemplate advertiseAutoRuleTemplate = advertiseAutoRuleTemplateDao.selectByPrimaryKey(param.getPuid(), param.getTemplateId());
        List<AdvertiseAutoRuleStatus> advertiseAutoRuleStatusList = advertiseAutoRuleStatusDao.selectAdControlledList(param);
        List<AdvertiseAutoRuleStatus> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(advertiseAutoRuleStatusList)) {
            List<String> targetIds = advertiseAutoRuleStatusList.stream().map(AdvertiseAutoRuleStatus::getItemId).collect(Collectors.toList());
            //根据关键ID查询出关键词信息
            List<AmazonAdTargeting> amazonAdTargetingList = amazonAdTargetDaoRoutingService.
                    autoRuleTarget(param.getPuid(), param.getShopId(), param.getCampaignIds(), param.getGroupIds(), param.getState(), param.getMatchType(), param.getSearchValue(), targetIds, param.getServingStatusList());
            Map<String, AmazonAdTargeting> amazonAdTargetingMap = null;
            Map<String, AmazonAdCampaignAll> campaignMap = null;
            Map<String, AmazonAdGroup> amazonAdGroupMap = null;
            Map<String, AmazonAdPortfolio> amazonAdPortfolioMap = null;
            if (CollectionUtils.isNotEmpty(amazonAdTargetingList)) {
                amazonAdTargetingMap = amazonAdTargetingList.stream().filter(Objects::nonNull).
                        collect(Collectors.toMap(AmazonAdTargeting::getTargetId, e -> e));
                //提取活动id
                List<String> campaignIds = amazonAdTargetingList.stream().
                        map(AmazonAdTargeting::getCampaignId).collect(Collectors.toList());
                //提取广告组id
                List<String> groupIds = amazonAdTargetingList.stream().
                        map(AmazonAdTargeting::getAdGroupId).collect(Collectors.toList());
                //批量获取广告活动信息
                List<AmazonAdCampaignAll> campaignAlls = amazonAdCampaignAllDao.
                        listByCampaignIdNoType(param.getPuid(), param.getShopId(), campaignIds);
                if (CollectionUtils.isNotEmpty(campaignAlls)) {
                    campaignMap = campaignAlls.stream().filter(Objects::nonNull).
                            collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                    List<String> portfolioIds = campaignAlls.stream().map(AmazonAdCampaignAll::getPortfolioId).
                            distinct().collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(portfolioIds)) {
                        List<AmazonAdPortfolio> portfolioList = amazonAdPortfolioDao.getPortfolioList(param.getPuid(), param.getShopId(), portfolioIds);
                        if (CollectionUtils.isNotEmpty(portfolioList)) {
                            amazonAdPortfolioMap = portfolioList.stream().filter(Objects::nonNull).
                                    collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                        }
                    }
                }
                //批量获取广告组信息
                List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.getAdGroupByIds
                        (param.getPuid(), param.getShopId(), null, groupIds);
                if (CollectionUtils.isNotEmpty(amazonAdGroups)) {
                    amazonAdGroupMap = amazonAdGroups.stream().filter(Objects::nonNull).
                            collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e));
                }
                //循环处理数据
                for (AdvertiseAutoRuleStatus vo : advertiseAutoRuleStatusList) {
                    AdvertiseAutoRuleStatus advertiseAutoRuleStatus = vo;
                    MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(vo.getMarketplaceId());
                    if (null != m) {
                        advertiseAutoRuleStatus.setCurrency(m.getCurrencyCode());
                    }
                    if (vo.getVersion() >= advertiseAutoRuleTemplate.getVersion()) {
                        vo.setUpdateStatus("update");
                    } else {
                        vo.setUpdateStatus("notUpdate");
                    }
                    if (MapUtils.isNotEmpty(amazonAdTargetingMap) && amazonAdTargetingMap.containsKey(advertiseAutoRuleStatus.getItemId())) {
                        AmazonAdTargeting amazonAdTargeting = amazonAdTargetingMap.get(advertiseAutoRuleStatus.getItemId());
                        advertiseAutoRuleStatus.setItemName(AutoTargetTypeEnum.getAutoTargetValue(amazonAdTargeting.getTargetingValue()));
                        advertiseAutoRuleStatus.setKeywordText(AutoTargetTypeEnum.getAutoTargetValue(amazonAdTargeting.getTargetingValue()));
                        advertiseAutoRuleStatus.setKeywordId(amazonAdTargeting.getTargetId());
                        advertiseAutoRuleStatus.setState(amazonAdTargeting.getState());
                        advertiseAutoRuleStatus.setAdType(vo.getAdType());
                        if (StringUtils.isNotBlank(amazonAdTargeting.getServingStatus())) {
                            advertiseAutoRuleStatus.setServingStatus(amazonAdTargeting.getServingStatus());
                            AmazonAdTargeting.servingStatusEnum byCode = UCommonUtil.getByCode(amazonAdTargeting.getServingStatus(), AmazonAdTargeting.servingStatusEnum.class);
                            if (byCode != null) {
                                advertiseAutoRuleStatus.setServingStatusName(byCode.getName());
                                advertiseAutoRuleStatus.setServingStatusDec(byCode.getDescription());
                            } else {
                                advertiseAutoRuleStatus.setServingStatusDec(amazonAdTargeting.getServingStatus());
                                advertiseAutoRuleStatus.setServingStatusName(amazonAdTargeting.getServingStatus());
                            }
                        }
                        if (MapUtils.isNotEmpty(campaignMap) && campaignMap.containsKey(amazonAdTargeting.getCampaignId())) {
                            advertiseAutoRuleStatus.setCampaignName(campaignMap.get(amazonAdTargeting.getCampaignId()).getName());
                            advertiseAutoRuleStatus.setCampaignId(amazonAdTargeting.getCampaignId());
                            if (MapUtils.isNotEmpty(amazonAdPortfolioMap) && amazonAdPortfolioMap.containsKey(campaignMap.
                                    get(amazonAdTargeting.getCampaignId()).getPortfolioId())) {
                                advertiseAutoRuleStatus.setPortfolioId(campaignMap.get(amazonAdTargeting.getCampaignId()).getPortfolioId());
                                advertiseAutoRuleStatus.setPortfolioName(amazonAdPortfolioMap.get(campaignMap.get(amazonAdTargeting.getCampaignId()).getPortfolioId()).getName());
                            }
                        }
                        if (MapUtils.isNotEmpty(amazonAdGroupMap) && amazonAdGroupMap.containsKey(amazonAdTargeting.getAdGroupId())) {
                            advertiseAutoRuleStatus.setAdGroupName(amazonAdGroupMap.get(amazonAdTargeting.getAdGroupId()).getName());
                            advertiseAutoRuleStatus.setAdGroupId(amazonAdTargeting.getAdGroupId());
                            OriginValueVo originValueVo = null;
                            if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getOriginValue())) {
                                originValueVo = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getOriginValue(), OriginValueVo.class);
                            }

                            if (originValueVo != null && originValueVo.getBiddingValue() != null) {
                                advertiseAutoRuleStatus.setBiddingValue(originValueVo.getBiddingValue());
                            } else {
                                if (amazonAdTargeting.getBid() != null) {
                                    advertiseAutoRuleStatus.setBiddingValue(BigDecimal.valueOf(amazonAdTargeting.getBid()).
                                            setScale(2, RoundingMode.HALF_UP));
                                } else {
                                    if (amazonAdGroupMap.get(amazonAdTargeting.getAdGroupId()).getDefaultBid() != null) {
                                        advertiseAutoRuleStatus.setBiddingValue(BigDecimal.valueOf(amazonAdGroupMap.get(amazonAdTargeting.getAdGroupId()).getDefaultBid()).
                                                setScale(2, RoundingMode.HALF_UP));
                                    }
                                }
                            }
                        }
                    } else {
                        continue;
                    }
                    list.add(advertiseAutoRuleStatus);
                }
            }
        }
        return list;
    }

    private List<AdvertiseAutoRuleStatus> pageAdControlledProductTarget(AutoRuleObjectParam param) {
        AdvertiseAutoRuleTemplate advertiseAutoRuleTemplate = advertiseAutoRuleTemplateDao.selectByPrimaryKey(param.getPuid(), param.getTemplateId());
        List<AdvertiseAutoRuleStatus> advertiseAutoRuleStatusList = advertiseAutoRuleStatusDao.selectAdControlledList(param);
        List<AdvertiseAutoRuleStatus> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(advertiseAutoRuleStatusList)) {
            List<String> targetIds = advertiseAutoRuleStatusList.stream().map(AdvertiseAutoRuleStatus::getItemId).collect(Collectors.toList());
            //根据关键ID查询出关键词信息
            List<AmazonAdTargeting> amazonAdTargetingList = amazonAdTargetDaoRoutingService.
                    autoRuleTarget(param.getPuid(), param.getShopId(), param.getCampaignIds(), param.getGroupIds(), param.getState(), param.getMatchType(), param.getSearchValue(), targetIds, param.getServingStatusList());
            Map<String, AmazonAdTargeting> amazonAdTargetingMap = null;
            Map<String, AmazonAdCampaignAll> campaignMap = null;
            Map<String, AmazonAdGroup> amazonAdGroupMap = null;
            Map<String, AmazonAdPortfolio> amazonAdPortfolioMap = null;
            if (CollectionUtils.isNotEmpty(amazonAdTargetingList)) {
                amazonAdTargetingMap = amazonAdTargetingList.stream().filter(Objects::nonNull).
                        collect(Collectors.toMap(AmazonAdTargeting::getTargetId, e -> e));
                //提取活动id
                List<String> campaignIds = amazonAdTargetingList.stream().
                        map(AmazonAdTargeting::getCampaignId).collect(Collectors.toList());
                //提取广告组id
                List<String> groupIds = amazonAdTargetingList.stream().
                        map(AmazonAdTargeting::getAdGroupId).collect(Collectors.toList());
                //批量获取广告活动信息
                List<AmazonAdCampaignAll> campaignAlls = amazonAdCampaignAllDao.
                        listByCampaignIdNoType(param.getPuid(), param.getShopId(), campaignIds);
                if (CollectionUtils.isNotEmpty(campaignAlls)) {
                    campaignMap = campaignAlls.stream().filter(Objects::nonNull).
                            collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                    List<String> portfolioIds = campaignAlls.stream().map(AmazonAdCampaignAll::getPortfolioId).
                            distinct().collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(portfolioIds)) {
                        List<AmazonAdPortfolio> portfolioList = amazonAdPortfolioDao.getPortfolioList(param.getPuid(), param.getShopId(), portfolioIds);
                        if (CollectionUtils.isNotEmpty(portfolioList)) {
                            amazonAdPortfolioMap = portfolioList.stream().filter(Objects::nonNull).
                                    collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                        }
                    }
                }
                //批量获取广告组信息
                List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.getAdGroupByIds
                        (param.getPuid(), param.getShopId(), null, groupIds);
                if (CollectionUtils.isNotEmpty(amazonAdGroups)) {
                    amazonAdGroupMap = amazonAdGroups.stream().filter(Objects::nonNull).
                            collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e));
                }
                //循环处理数据
                for (AdvertiseAutoRuleStatus vo : advertiseAutoRuleStatusList) {
                    AdvertiseAutoRuleStatus advertiseAutoRuleStatus = vo;
                    MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(vo.getMarketplaceId());
                    if (null != m) {
                        advertiseAutoRuleStatus.setCurrency(m.getCurrencyCode());
                    }
                    if (vo.getVersion() >= advertiseAutoRuleTemplate.getVersion()) {
                        vo.setUpdateStatus("update");
                    } else {
                        vo.setUpdateStatus("notUpdate");
                    }
                    if (MapUtils.isNotEmpty(amazonAdTargetingMap) && amazonAdTargetingMap.containsKey(advertiseAutoRuleStatus.getItemId())) {
                        AmazonAdTargeting amazonAdTargeting = amazonAdTargetingMap.get(advertiseAutoRuleStatus.getItemId());
                        advertiseAutoRuleStatus.setItemName(amazonAdTargeting.getTargetingValue());
                        advertiseAutoRuleStatus.setKeywordText(amazonAdTargeting.getTargetingValue());
                        advertiseAutoRuleStatus.setKeywordId(amazonAdTargeting.getTargetId());
                        advertiseAutoRuleStatus.setState(amazonAdTargeting.getState());
                        advertiseAutoRuleStatus.setAdType(vo.getAdType());
                        if (StringUtils.isNotBlank(amazonAdTargeting.getServingStatus())) {
                            advertiseAutoRuleStatus.setServingStatus(amazonAdTargeting.getServingStatus());
                            AmazonAdTargeting.servingStatusEnum byCode = UCommonUtil.getByCode(amazonAdTargeting.getServingStatus(), AmazonAdTargeting.servingStatusEnum.class);
                            if (byCode != null) {
                                advertiseAutoRuleStatus.setServingStatusName(byCode.getName());
                                advertiseAutoRuleStatus.setServingStatusDec(byCode.getDescription());
                            } else {
                                advertiseAutoRuleStatus.setServingStatusDec(amazonAdTargeting.getServingStatus());
                                advertiseAutoRuleStatus.setServingStatusName(amazonAdTargeting.getServingStatus());
                            }
                        }
                        if (MapUtils.isNotEmpty(campaignMap) && campaignMap.containsKey(amazonAdTargeting.getCampaignId())) {
                            advertiseAutoRuleStatus.setCampaignName(campaignMap.get(amazonAdTargeting.getCampaignId()).getName());
                            advertiseAutoRuleStatus.setCampaignId(amazonAdTargeting.getCampaignId());
                            if (MapUtils.isNotEmpty(amazonAdPortfolioMap) && amazonAdPortfolioMap.containsKey(campaignMap.
                                    get(amazonAdTargeting.getCampaignId()).getPortfolioId())) {
                                advertiseAutoRuleStatus.setPortfolioId(campaignMap.get(amazonAdTargeting.getCampaignId()).getPortfolioId());
                                advertiseAutoRuleStatus.setPortfolioName(amazonAdPortfolioMap.get(campaignMap.get(amazonAdTargeting.getCampaignId()).getPortfolioId()).getName());
                            }
                        }
                        if (MapUtils.isNotEmpty(amazonAdGroupMap) && amazonAdGroupMap.containsKey(amazonAdTargeting.getAdGroupId())) {
                            advertiseAutoRuleStatus.setAdGroupName(amazonAdGroupMap.get(amazonAdTargeting.getAdGroupId()).getName());
                            advertiseAutoRuleStatus.setAdGroupId(amazonAdTargeting.getAdGroupId());
                            OriginValueVo originValueVo = null;
                            if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getOriginValue())) {
                                originValueVo = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getOriginValue(), OriginValueVo.class);
                            }

                            if (originValueVo != null && originValueVo.getBiddingValue() != null) {
                                advertiseAutoRuleStatus.setBiddingValue(originValueVo.getBiddingValue());
                            } else {
                                if (amazonAdTargeting.getBid() != null) {
                                    advertiseAutoRuleStatus.setBiddingValue(BigDecimal.valueOf(amazonAdTargeting.getBid()).
                                            setScale(2, RoundingMode.HALF_UP));
                                } else {
                                    if (amazonAdGroupMap.get(amazonAdTargeting.getAdGroupId()).getDefaultBid() != null) {
                                        advertiseAutoRuleStatus.setBiddingValue(BigDecimal.valueOf(amazonAdGroupMap.get(amazonAdTargeting.getAdGroupId()).getDefaultBid()).
                                                setScale(2, RoundingMode.HALF_UP));
                                    }
                                }
                            }
                        }
                    } else {
                        continue;
                    }
                    list.add(advertiseAutoRuleStatus);
                }
            }
        }
        return list;
    }

    @Override
    public Result<Page<AdvertiseAutoRuleStatus>> pageAdControlledGroup(AutoRuleObjectParam param) {
        Result<Page<AdvertiseAutoRuleStatus>> pageResult = new Result<>();
        try {
            Page<AdvertiseAutoRuleStatus> voPage = new Page<>(param.getPageNo(), param.getPageSize());
            //组合id转活动id
            if (CollectionUtils.isEmpty(param.getCampaignIds())) {
                if (CollectionUtils.isNotEmpty(param.getPortfolioIds())) {
                    List<String> campaignIdList = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdList(param.getPuid(), param.getShopId(), param.getPortfolioIds());
                    if (CollectionUtils.isNotEmpty(campaignIdList)) {
                        param.setCampaignIds(campaignIdList);
                    } else {
                        pageResult.setCode(Result.SUCCESS);
                        pageResult.setData(voPage);
                        return pageResult;
                    }
                }
            }

            //查询模板和受控对象
            AdvertiseAutoRuleTemplate advertiseAutoRuleTemplate = advertiseAutoRuleTemplateDao.selectByPrimaryKey(param.getPuid(), param.getTemplateId());
            List<AdvertiseAutoRuleStatus> statusList = advertiseAutoRuleStatusDao.selectAdControlledList(param);

            //无数据
            if (CollectionUtils.isEmpty(statusList)) {
                pageResult.setCode(Result.SUCCESS);
                pageResult.setData(voPage);
                return pageResult;
            }

            //查询基础数据
            //数据分组
            Map<String, List<AdvertiseAutoRuleStatus>> statusMapByAdType = statusList.stream().collect(Collectors.groupingBy(AdvertiseAutoRuleStatus::getAdType));

            //map变量
            Map<String, AmazonAdGroup> spGroupMap = new HashMap<>();
            Map<String, AmazonSbAdGroup> sbGroupMap = new HashMap<>();
            Map<String, AmazonSdAdGroup> sdGroupMap = new HashMap<>();
            Map<String, AmazonAdCampaignAll> campaignMap = new HashMap<>();
            Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>();
            Set<String> campaignIdSet = new HashSet<>();


            //查询广告组信息
            queryAndComputeGroupInfo(param, statusMapByAdType, spGroupMap, sbGroupMap, sdGroupMap, campaignIdSet);

            //无数据
            if (MapUtils.isEmpty(spGroupMap) && MapUtils.isEmpty(sbGroupMap) && MapUtils.isEmpty(sdGroupMap) && CollectionUtils.isEmpty(campaignIdSet)) {
                pageResult.setCode(Result.SUCCESS);
                pageResult.setData(voPage);
                return pageResult;
            }

            //查询广告活动和组合
            List<AmazonAdCampaignAll> campaignAlls = amazonAdCampaignAllDao.listByCampaignIdNoType(param.getPuid(), param.getShopId(), new ArrayList<>(campaignIdSet));
            if (CollectionUtils.isNotEmpty(campaignAlls)) {
                campaignMap = campaignAlls.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                Set<String> portfolioIdSet = campaignAlls.stream().map(AmazonAdCampaignAll::getPortfolioId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(portfolioIdSet)) {
                    List<AmazonAdPortfolio> amazonAdPortfolios = amazonAdPortfolioDao.getPortfolioList(param.getPuid(), param.getShopId(), new ArrayList<>(portfolioIdSet));
                    if (CollectionUtils.isNotEmpty(amazonAdPortfolios)) {
                        portfolioMap = amazonAdPortfolios.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                    }
                }
            }

            //遍历处理数据
            List<AdvertiseAutoRuleStatus> list = Lists.newArrayListWithExpectedSize(statusList.size());
            for (AdvertiseAutoRuleStatus vo : statusList) {
                vo.setAdType(vo.getAdType());
                MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(vo.getMarketplaceId());
                if (Objects.nonNull(m)) {
                    vo.setCurrency(m.getCurrencyCode());
                }

                //模板更新状态
                if (vo.getVersion() >= advertiseAutoRuleTemplate.getVersion()) {
                    vo.setUpdateStatus(AutoRuleUpdateStatusEnum.UPDATE.getCode());
                } else {
                    vo.setUpdateStatus(AutoRuleUpdateStatusEnum.NOT_UPDATE.getCode());
                }

                //填充基础信息
                AdvertiseAutoRuleStatus fillVo = fillGroupBaseInfo(vo, spGroupMap, sbGroupMap, sdGroupMap, campaignMap, portfolioMap);
                if (Objects.isNull(fillVo)) {
                    continue;
                }
                list.add(vo);
            }

            //内存分页，待优化
            if (CollectionUtils.isNotEmpty(list)) {
                voPage = PageUtil.getPage(voPage, list);
            }
            pageResult.setCode(Result.SUCCESS);
            pageResult.setData(voPage);
        } catch (Exception e) {
            pageResult.setCode(Result.ERROR);
            pageResult.setMsg("查询广告组受控对象异常");
            log.error("traceId:{} puid={} shopId={} 查询广告组受控对象异常", param.getTraceId(), param.getPuid(), param.getShopId(), e);
        }
        return pageResult;
    }

    private void queryAndComputeGroupInfo(AutoRuleObjectParam param,
                                          Map<String, List<AdvertiseAutoRuleStatus>> statusMapByAdType,
                                          Map<String, AmazonAdGroup> spGroupMap,
                                          Map<String, AmazonSbAdGroup> sbGroupMap,
                                          Map<String, AmazonSdAdGroup> sdGroupMap,
                                          Set<String> campaignIdSet) {
        for (Map.Entry<String, List<AdvertiseAutoRuleStatus>> entry : statusMapByAdType.entrySet()) {
            List<String> groupIdList = entry.getValue().stream().map(x -> x.getItemId()).collect(Collectors.toList());
            if (AutoRuleSubmitAdTypeEnum.SP.getCode().equals(entry.getKey())) {
                List<AmazonAdGroup> groupList = amazonAdGroupDao.autoRuleAdGroup(param.getPuid(),
                        param.getShopId(),
                        param.getCampaignIds(),
                        groupIdList,
                        param.getState(),
                        param.getSearchValue(),
                        param.getServingStatusList());
                groupList.forEach(x -> {
                    spGroupMap.put(x.getAdGroupId(), x);
                    campaignIdSet.add(x.getCampaignId());
                });
            } else if (AutoRuleSubmitAdTypeEnum.SB.getCode().equals(entry.getKey())) {
                List<AmazonSbAdGroup> groupList = amazonSbAdGroupDao.autoRuleAdGroup(param.getPuid(),
                        param.getShopId(),
                        param.getCampaignIds(),
                        groupIdList,
                        param.getState(),
                        param.getSearchValue(),
                        param.getServingStatusList());
                groupList.forEach(x -> {
                    sbGroupMap.put(x.getAdGroupId(), x);
                    campaignIdSet.add(x.getCampaignId());
                });
            } else if (AutoRuleSubmitAdTypeEnum.SD.getCode().equals(entry.getKey())) {
                List<AmazonSdAdGroup> groupList = amazonSdAdGroupDao.autoRuleAdGroup(param.getPuid(),
                        param.getShopId(),
                        param.getCampaignIds(),
                        groupIdList,
                        param.getState(),
                        param.getSearchValue(),
                        param.getServingStatusList());
                groupList.forEach(x -> {
                    sdGroupMap.put(x.getAdGroupId(), x);
                    campaignIdSet.add(x.getCampaignId());
                });
            }
        }
    }


    private AdvertiseAutoRuleStatus fillGroupBaseInfo(AdvertiseAutoRuleStatus vo,
                                                      Map<String, AmazonAdGroup> spGroupMap,
                                                      Map<String, AmazonSbAdGroup> sbGroupMap,
                                                      Map<String, AmazonSdAdGroup> sdGroupMap,
                                                      Map<String, AmazonAdCampaignAll> campaignMap,
                                                      Map<String, AmazonAdPortfolio> portfolioMap) {
        if (AutoRuleSubmitAdTypeEnum.SP.getCode().equals(vo.getAdType())) {
            AmazonAdGroup amazonAdGroup = spGroupMap.get(vo.getItemId());
            if (Objects.isNull(amazonAdGroup)) {
                return null;
            }
            vo.setItemName(amazonAdGroup.getName());
            vo.setAdGroupName(amazonAdGroup.getName());
            vo.setAdGroupId(amazonAdGroup.getAdGroupId());
            vo.setState(amazonAdGroup.getState());
            OriginValueVo originValueVo = null;
            if (StringUtils.isNotBlank(vo.getOriginValue())) {
                originValueVo = JSONUtil.jsonToObject(vo.getOriginValue(), OriginValueVo.class);
            }
            if (originValueVo != null && originValueVo.getDefaultBiddingValue() != null) {
                vo.setDefaultBid(originValueVo.getDefaultBiddingValue());
            } else {
                vo.setDefaultBid(BigDecimal.valueOf(amazonAdGroup.getDefaultBid()));
            }

            //服务状态
            if (StringUtils.isNotBlank(amazonAdGroup.getServingStatus())) {
                vo.setServingStatus(amazonAdGroup.getServingStatus());
                AmazonAdGroup.servingStatusEnum byCode = UCommonUtil.getByCode(amazonAdGroup.getServingStatus(), AmazonAdGroup.servingStatusEnum.class);
                if (byCode != null) {
                    vo.setServingStatusDec(byCode.getDescription());
                    vo.setServingStatusName(byCode.getName());
                } else {
                    vo.setServingStatusDec(amazonAdGroup.getServingStatus());
                    vo.setServingStatusName(amazonAdGroup.getServingStatus());
                }
            }

            //广告活动信息
            AmazonAdCampaignAll campaignAll = campaignMap.get(amazonAdGroup.getCampaignId());
            if (Objects.nonNull(campaignAll)) {
                vo.setCampaignName(campaignMap.get(amazonAdGroup.getCampaignId()).getName());
                vo.setCampaignId(amazonAdGroup.getCampaignId());
                //广告组合信息
                if (StringUtils.isNotBlank(campaignAll.getPortfolioId())) {
                    AmazonAdPortfolio portfolio = portfolioMap.get(campaignAll.getPortfolioId());
                    if (Objects.nonNull(portfolio)) {
                        vo.setPortfolioId(campaignAll.getPortfolioId());
                        vo.setPortfolioName(portfolio.getName());
                        vo.setPortfolioIsHidden(portfolio.getIsHidden());
                    }
                }
            }
        } else if (AutoRuleSubmitAdTypeEnum.SB.getCode().equals(vo.getAdType())) {
            AmazonSbAdGroup amazonAdGroup = sbGroupMap.get(vo.getItemId());
            if (Objects.isNull(amazonAdGroup)) {
                return null;
            }
            vo.setItemName(amazonAdGroup.getName());
            vo.setAdGroupName(amazonAdGroup.getName());
            vo.setAdGroupId(amazonAdGroup.getAdGroupId());
            vo.setState(amazonAdGroup.getState());
            Optional.ofNullable(amazonAdGroup.getAdFormat()).filter(StringUtils::isNotEmpty).ifPresent(vo::setAdFormat);
            OriginValueVo originValueVo = null;
            if (StringUtils.isNotBlank(vo.getOriginValue())) {
                originValueVo = JSONUtil.jsonToObject(vo.getOriginValue(), OriginValueVo.class);
            }
            if (originValueVo != null && originValueVo.getDefaultBiddingValue() != null) {
                vo.setDefaultBid(originValueVo.getDefaultBiddingValue());
            } else {
                vo.setDefaultBid(amazonAdGroup.getBid());
            }

            //服务状态
            if (StringUtils.isNotBlank(amazonAdGroup.getServingStatus())) {
                vo.setServingStatus(amazonAdGroup.getServingStatus());
                AmazonSbAdGroup.servingStatusEnum byCode = UCommonUtil.getByCode(amazonAdGroup.getServingStatus(), AmazonSbAdGroup.servingStatusEnum.class);
                if (byCode != null) {
                    vo.setServingStatusDec(byCode.getDescription());
                    vo.setServingStatusName(byCode.getName());
                } else {
                    vo.setServingStatusDec(amazonAdGroup.getServingStatus());
                    vo.setServingStatusName(amazonAdGroup.getServingStatus());
                }
            }

            //广告活动信息
            AmazonAdCampaignAll campaignAll = campaignMap.get(amazonAdGroup.getCampaignId());
            if (Objects.nonNull(campaignAll)) {
                vo.setCampaignName(campaignMap.get(amazonAdGroup.getCampaignId()).getName());
                vo.setCampaignId(amazonAdGroup.getCampaignId());
                //广告组合信息
                if (StringUtils.isNotBlank(campaignAll.getPortfolioId())) {
                    AmazonAdPortfolio portfolio = portfolioMap.get(campaignAll.getPortfolioId());
                    if (Objects.nonNull(portfolio)) {
                        vo.setPortfolioId(campaignAll.getPortfolioId());
                        vo.setPortfolioName(portfolio.getName());
                    }
                }
                Optional.ofNullable(campaignAll.getCostType()).filter(StringUtils::isNotEmpty).ifPresent(vo::setCostType);
                Optional.ofNullable(campaignAll.getAdGoal())
                        .filter(StringUtils::isNotEmpty)
                        .map(SBCampaignGoalEnum::getSBCampaignGoalEnumByType)
                        .map(SBCampaignGoalEnum::getCode)
                        .map(String::valueOf)
                        .ifPresent(vo::setAdGoal);
            }
        } else if (AutoRuleSubmitAdTypeEnum.SD.getCode().equals(vo.getAdType())) {
            AmazonSdAdGroup amazonAdGroup = sdGroupMap.get(vo.getItemId());
            if (Objects.isNull(amazonAdGroup)) {
                return null;
            }
            vo.setItemName(amazonAdGroup.getName());
            vo.setAdGroupName(amazonAdGroup.getName());
            vo.setAdGroupId(amazonAdGroup.getAdGroupId());
            vo.setState(amazonAdGroup.getState());
            OriginValueVo originValueVo = null;
            if (StringUtils.isNotBlank(vo.getOriginValue())) {
                originValueVo = JSONUtil.jsonToObject(vo.getOriginValue(), OriginValueVo.class);
            }
            if (originValueVo != null && originValueVo.getDefaultBiddingValue() != null) {
                vo.setDefaultBid(originValueVo.getDefaultBiddingValue());
            } else {
                vo.setDefaultBid(amazonAdGroup.getDefaultBid());
            }

            //服务状态
            if (StringUtils.isNotBlank(amazonAdGroup.getServingStatus())) {
                vo.setServingStatus(amazonAdGroup.getServingStatus());
                AmazonSdAdGroup.servingStatusEnum byCode = UCommonUtil.getByCode(amazonAdGroup.getServingStatus(), AmazonSdAdGroup.servingStatusEnum.class);
                if (byCode != null) {
                    vo.setServingStatusDec(byCode.getDescription());
                    vo.setServingStatusName(byCode.getName());
                } else {
                    vo.setServingStatusDec(amazonAdGroup.getServingStatus());
                    vo.setServingStatusName(amazonAdGroup.getServingStatus());
                }
            }

            //广告活动信息
            AmazonAdCampaignAll campaignAll = campaignMap.get(amazonAdGroup.getCampaignId());
            if (Objects.nonNull(campaignAll)) {
                vo.setCampaignName(campaignMap.get(amazonAdGroup.getCampaignId()).getName());
                vo.setCampaignId(amazonAdGroup.getCampaignId());
                //广告组合信息
                if (StringUtils.isNotBlank(campaignAll.getPortfolioId())) {
                    AmazonAdPortfolio portfolio = portfolioMap.get(campaignAll.getPortfolioId());
                    if (Objects.nonNull(portfolio)) {
                        vo.setPortfolioId(campaignAll.getPortfolioId());
                        vo.setPortfolioName(portfolio.getName());
                    }
                }
                Optional.ofNullable(campaignAll.getCostType()).filter(StringUtils::isNotEmpty).ifPresent(vo::setCostType);
            }
        }
        return vo;
    }

    @Override
    public Result<Page<AdvertiseAutoRuleStatus>> pageAdControlledKeywordCard(AutoRuleObjectParam param) {
        Result<Page<AdvertiseAutoRuleStatus>> pageResult = new Result<>();
        try {
            Page<AdvertiseAutoRuleStatus> voPage = new Page<>(param.getPageNo(), param.getPageSize());
            if (CollectionUtils.isEmpty(param.getCampaignIds())) {
                if (CollectionUtils.isNotEmpty(param.getPortfolioIds())) {
                    List<String> campaignIdList = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdList(param.getPuid(), param.getShopId(), param.getPortfolioIds());
                    if (CollectionUtils.isNotEmpty(campaignIdList)) {
                        param.setCampaignIds(campaignIdList);
                    } else {
                        pageResult.setCode(Result.SUCCESS);
                        pageResult.setData(voPage);
                        return pageResult;
                    }

                }
            }
            AdvertiseAutoRuleTemplate advertiseAutoRuleTemplate = advertiseAutoRuleTemplateDao.selectByPrimaryKey(param.getPuid(), param.getTemplateId());
            List<AdvertiseAutoRuleStatus> advertiseAutoRuleStatusList = advertiseAutoRuleStatusDao.selectAdControlledList(param);
            if (CollectionUtils.isNotEmpty(advertiseAutoRuleStatusList)) {
                List<AdvertiseAutoRuleStatus> list = Lists.newArrayListWithExpectedSize(advertiseAutoRuleStatusList.size());
                List<String> keywordIds = advertiseAutoRuleStatusList.stream().map(AdvertiseAutoRuleStatus::getItemId).collect(Collectors.toList());
                //根据关键ID查询出关键词信息
                List<AmazonAdKeyword> amazonAdKeywords = amazonAdKeywordDaoRoutingService.
                        autoRuleKeyword(param.getPuid(), param.getShopId(), param.getCampaignIds(), param.getGroupIds(), param.getState(), param.getMatchType(), param.getSearchValue(), keywordIds, param.getServingStatusList());
                Map<String, AmazonAdKeyword> amazonAdKeywordMap = null;
                Map<String, AmazonAdCampaignAll> campaignMap = null;
                Map<String, AmazonAdGroup> amazonAdGroupMap = null;
                if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                    amazonAdKeywordMap = amazonAdKeywords.stream().filter(Objects::nonNull).
                            collect(Collectors.toMap(AmazonAdKeyword::getKeywordId, e -> e));
                    //提取活动id
                    List<String> campaignIds = amazonAdKeywords.stream().
                            map(AmazonAdKeyword::getCampaignId).collect(Collectors.toList());
                    //提取广告组id
                    List<String> groupIds = amazonAdKeywords.stream().
                            map(AmazonAdKeyword::getAdGroupId).collect(Collectors.toList());
                    //批量获取广告活动信息
                    List<AmazonAdCampaignAll> campaignAlls = amazonAdCampaignAllDao.
                            listByCampaignIdNoType(param.getPuid(), param.getShopId(), campaignIds);
                    if (CollectionUtils.isNotEmpty(campaignAlls)) {
                        campaignMap = campaignAlls.stream().filter(Objects::nonNull).
                                collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                    }
                    //批量获取广告组信息
                    List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.getAdGroupByIds
                            (param.getPuid(), param.getShopId(), null, groupIds);
                    if (CollectionUtils.isNotEmpty(amazonAdGroups)) {
                        amazonAdGroupMap = amazonAdGroups.stream().filter(Objects::nonNull).
                                collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e));
                    }
                    //循环处理数据
                    for (AdvertiseAutoRuleStatus vo : advertiseAutoRuleStatusList) {
                        AdvertiseAutoRuleStatus advertiseAutoRuleStatus = vo;
                        MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(vo.getMarketplaceId());
                        if (null != m) {
                            advertiseAutoRuleStatus.setCurrency(m.getCurrencyCode());
                        }
                        if (vo.getVersion() >= advertiseAutoRuleTemplate.getVersion()) {
                            vo.setUpdateStatus("update");
                        } else {
                            vo.setUpdateStatus("notUpdate");
                        }
                        if (MapUtils.isNotEmpty(amazonAdKeywordMap) && amazonAdKeywordMap.containsKey(advertiseAutoRuleStatus.getItemId())) {
                            AmazonAdKeyword amazonAdKeyword = amazonAdKeywordMap.get(advertiseAutoRuleStatus.getItemId());
                            advertiseAutoRuleStatus.setItemName(amazonAdKeyword.getKeywordText());
                            advertiseAutoRuleStatus.setKeywordText(amazonAdKeyword.getKeywordText());
                            advertiseAutoRuleStatus.setKeywordId(amazonAdKeyword.getKeywordId());
                            advertiseAutoRuleStatus.setState(amazonAdKeyword.getState());
                            advertiseAutoRuleStatus.setAdType(vo.getAdType());
                            OriginValueVo originValueVo = null;
                            if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getOriginValue())) {
                                originValueVo = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getOriginValue(), OriginValueVo.class);
                            }

                            if (originValueVo != null) {
                                if (originValueVo.getBiddingValue() != null) {
                                    advertiseAutoRuleStatus.setBiddingValue(originValueVo.getBiddingValue().
                                            setScale(2, RoundingMode.HALF_UP));
                                }
                                if (originValueVo.getPlacementTopBidRatio()!= null) {
                                    advertiseAutoRuleStatus.setPlacementTopBidRatio(originValueVo.getPlacementTopBidRatio());
                                }
                            }
                            if (MapUtils.isNotEmpty(campaignMap) && campaignMap.containsKey(amazonAdKeyword.getCampaignId())) {
                                AmazonAdCampaignAll campaignAll = campaignMap.get(amazonAdKeyword.getCampaignId());
                                advertiseAutoRuleStatus.setCampaignName(campaignAll.getName());
                                advertiseAutoRuleStatus.setStrategyType(campaignAll.getStrategy());
                                advertiseAutoRuleStatus.setCampaignId(amazonAdKeyword.getCampaignId());
                            }
                            if (MapUtils.isNotEmpty(amazonAdGroupMap) && amazonAdGroupMap.containsKey(amazonAdKeyword.getAdGroupId())) {
                                advertiseAutoRuleStatus.setAdGroupName(amazonAdGroupMap.get(amazonAdKeyword.getAdGroupId()).getName());
                                advertiseAutoRuleStatus.setAdGroupId(amazonAdKeyword.getAdGroupId());
                            }
                            if (StringUtils.isNotBlank(amazonAdKeyword.getMatchType())) {
                                advertiseAutoRuleStatus.setMatchName((MatchTypeEnum.getMatchValue(amazonAdKeyword.getMatchType())));
                                advertiseAutoRuleStatus.setMatchType(amazonAdKeyword.getMatchType());
                            }
                            if (StringUtils.isNotBlank(amazonAdKeyword.getServingStatus())) {
                                advertiseAutoRuleStatus.setServingStatus(amazonAdKeyword.getServingStatus());
                                AmazonAdKeyword.servingStatusEnum byCode = UCommonUtil.getByCode(amazonAdKeyword.getServingStatus(), AmazonAdKeyword.servingStatusEnum.class);
                                if (byCode != null) {
                                    advertiseAutoRuleStatus.setServingStatusName(byCode.getName());
                                    advertiseAutoRuleStatus.setServingStatusDec(byCode.getDescription());
                                } else {
                                    advertiseAutoRuleStatus.setServingStatusDec(amazonAdKeyword.getServingStatus());
                                    advertiseAutoRuleStatus.setServingStatusName(amazonAdKeyword.getServingStatus());
                                }
                            }
                        } else {
                            continue;
                        }
                        list.add(advertiseAutoRuleStatus);
                    }
                }
                //内存分页
                if (CollectionUtils.isNotEmpty(list)) {
                    //通过竞价策略先进行筛选过滤
                    if (StringUtils.isNotBlank(param.getStrategyType())) {
                        list = list.stream()
                                .filter(i -> i.getStrategyType().equalsIgnoreCase(param.getStrategyType())).collect(Collectors.toList());
                    }
                    if (CollectionUtils.isNotEmpty(list)) {
                        voPage = PageUtil.getPage(voPage, list);
                    }
                }
            }
            pageResult.setCode(Result.SUCCESS);
            pageResult.setData(voPage);
        } catch (Exception e) {
            pageResult.setCode(Result.ERROR);
            pageResult.setMsg("查询投放受控对象异常");
            log.error("traceId:{} puid:{} shopId:{} 查询投放受控对象异常", param.getTraceId(), param.getPuid(),
                    param.getShopId(), e);
        }
        return pageResult;
    }

    @Override
    public Result<List<AddAutoRuleVo>> submitAutoRule(Integer puid, List<SubmitAutoRuleVo> submitAutoRuleVos, Long templateId, AdvertiseAutoRuleTemplate template, Integer updateId, String traceId) {
        Result<List<AddAutoRuleVo>> result = new Result<>();
        try {
            //查询模板下的受控对象id
            List<String> existItemIdList = advertiseAutoRuleStatusDao.queryItemIdByTemplateId(puid, templateId);

            //定义itemType，用于判断调用aadras的api
            String itemType = template.getItemType();

            //定义受控对象变量集合
            List<AdvertiseAutoRuleStatus> statusList = null;

            //抢排名
            if (AutoRuleItemTypeEnum.KEYWORD_TARGET.getName().equals(template.getItemType())) {
                statusList = submitAutoRule4KeywordTarget(puid, updateId, submitAutoRuleVos, template, existItemIdList);
            }

            //搜索词
            if (AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName().equals(template.getItemType())) {
                statusList = submitAutoRule4GroupSearchQuery(puid, updateId, submitAutoRuleVos, template, existItemIdList);
                //如果是子受控，则赋值为SEARCH_QUERY
                if (ChildrenItemType.CHILDREN_SEARCH_QUERY.name().equals(submitAutoRuleVos.get(0).getChildrenItemType())) {
                    itemType = AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.SEARCH_QUERY.name();
                }
            }

            //活动
            if (AutoRuleItemTypeEnum.CAMPAIGN.getName().equals(template.getItemType())) {
                statusList = submitAutoRule4Campaign(puid, updateId, submitAutoRuleVos, template, existItemIdList);
            }

            //广告组
            if (AutoRuleItemTypeEnum.AD_GROUP.getName().equals(template.getItemType())) {
                statusList = submitAutoRule4Group(puid, updateId, submitAutoRuleVos, template, existItemIdList);
            }

            //投放
            if (AutoRuleItemTypeEnum.TARGET.getName().equals(template.getItemType())) {
                statusList = submitAutoRule4Target(puid, updateId, submitAutoRuleVos, template, existItemIdList);
            }

            //插入status表
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            if (CollectionUtils.isEmpty(statusList)) {
                return result;
            }
            advertiseAutoRuleStatusDao.batchInsert(puid, statusList);
            stopWatch.stop();
            log.info("自动化规则插入数据时间=======================:{}", stopWatch.getTotalTimeSeconds());

            ShopAuth shopAuth = shopAuthDao.getScAndVcById(template.getShopId());
            if (Objects.isNull(shopAuth)) {
                return result;
            }

            //发送到aadras
            StopWatch stopWatchs = new StopWatch();
            stopWatchs.start();
            Vector<String> errorMsgList = new Vector<>();
            List<List<AdvertiseAutoRuleStatus>> advertiseAutoRuleStatusPartition = Lists.partition(statusList, Constants.AUTO_RULE_PARTITION_SIZE);
            ThreadPoolExecutor threadPoolExecutor = ThreadPoolUtil.getSubmitAutoRuleThreadPool();
            CountDownLatch countDownLatch = new CountDownLatch(advertiseAutoRuleStatusPartition.size());
            for (List<AdvertiseAutoRuleStatus> advertiseAutoRuleStatuses : advertiseAutoRuleStatusPartition) {
                String finalItemType = itemType;
                threadPoolExecutor.execute(() -> {
                    try {
                        aadrasApiFactory.getAadrasApi(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.valueOf(finalItemType)).batchSetAutoRuleTask(advertiseAutoRuleStatuses, shopAuth);
                    } catch (Exception e) {
                        errorMsgList.add("自动化规则计算服务异常:" + e.getMessage());
                        log.error("traceId:{} puid:{} shopId:{} 自动化规则计算服务异常:", traceId, puid, template.getShopId(), e);
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }
            countDownLatch.await();
            stopWatchs.stop();
            log.info("aadras插入数据时间=======================:{}", stopWatchs.getTotalTimeSeconds());
            if (CollectionUtils.isNotEmpty(errorMsgList)) {
                result.setCode(Result.ERROR);
                result.setMsg(errorMsgList.get(0));
            }
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("广告自动化规则提交受控对象异常:" + e);
            log.error("puid={} 广告自动化规则提交受控对象异常", puid, e);
        }
        return result;
    }

    /**
     * 投放
     *
     * @param puid
     * @param submitAutoRuleVos
     * @param template
     * @param existItemIdList
     * @return
     */
    private List<AdvertiseAutoRuleStatus> submitAutoRule4Target(Integer puid, Integer updateId, List<SubmitAutoRuleVo> submitAutoRuleVos, AdvertiseAutoRuleTemplate template, List<String> existItemIdList) {

        //受控类型
        String childrenItemType = submitAutoRuleVos.get(0).getChildrenItemType();
        // 校验子受控对象类型与当前模板的子受控对象类型是否一致
        List<AdvertiseAutoRuleStatus> childrenItemTypeList = advertiseAutoRuleStatusDao.selectChildrenItemType(puid, template.getShopId(), template.getId());
        if(StringUtil.isNotEmpty(childrenItemType) && CollectionUtil.isNotEmpty(childrenItemTypeList) && !childrenItemTypeList.get(0).getChildrenItemType().equals(childrenItemType)){
            throw new AutoRuleException("子受控对象类型错误");
        }

        //最终构建的受控对象集合
        List<AdvertiseAutoRuleStatus> statusList = new ArrayList<>();

        //组受控
        if (ChildrenItemType.CHILDREN_TARGET_GROUP.name().equals(childrenItemType)) {
            //定义变量
            Map<String, AmazonAdGroup> spGroupMap = new HashMap<>();
            Map<String, AmazonSbAdGroup> sbGroupMap = new HashMap<>();
            Map<String, AmazonSdAdGroup> sdGroupMap = new HashMap<>();

            //数据分组
            Map<String, List<SubmitAutoRuleVo>> autoRuleVosByAdTypeMap = submitAutoRuleVos.stream().collect(Collectors.groupingBy(SubmitAutoRuleVo::getAdType));

            //查询基础数据
            for (Map.Entry<String, List<SubmitAutoRuleVo>> entry : autoRuleVosByAdTypeMap.entrySet()) {
                List<String> itemIdList = entry.getValue().stream().map(x -> x.getItemId()).collect(Collectors.toList());
                if (AutoRuleSubmitAdTypeEnum.SP.getCode().equals(entry.getKey())) {
                    List<AmazonAdGroup> amazonAdGroupList = amazonAdGroupDao.getAdGroupByIds(template.getPuid(), template.getShopId(), itemIdList);
                    if (CollectionUtils.isNotEmpty(amazonAdGroupList)) {
                        spGroupMap = amazonAdGroupList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e, (v1, v2) -> v1));
                    }
                } else if (AutoRuleSubmitAdTypeEnum.SB.getCode().equals(entry.getKey())) {
                    List<AmazonSbAdGroup> amazonAdGroupList = amazonSbAdGroupDao.getAdGroupByIds(template.getPuid(), template.getShopId(), itemIdList);
                    if (CollectionUtils.isNotEmpty(amazonAdGroupList)) {
                        sbGroupMap = amazonAdGroupList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, e -> e, (v1, v2) -> v1));
                    }
                } else if (AutoRuleSubmitAdTypeEnum.SD.getCode().equals(entry.getKey())) {
                    List<AmazonSdAdGroup> amazonAdGroupList = amazonSdAdGroupDao.getByGroupIds(template.getPuid(), template.getShopId(), itemIdList);
                    if (CollectionUtils.isNotEmpty(amazonAdGroupList)) {
                        sdGroupMap = amazonAdGroupList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, e -> e, (v1, v2) -> v1));
                    }
                }
            }

            //构建status集合
            statusList = buildAutoRuleStatusList(puid, updateId, submitAutoRuleVos, template, existItemIdList);

            //组受控其他属性set
            for (AdvertiseAutoRuleStatus advertiseAutoRuleStatus : statusList) {
                if (AutoRuleSubmitAdTypeEnum.SP.getCode().equals(advertiseAutoRuleStatus.getAdType())) {
                    AmazonAdGroup amazonAdGroup = spGroupMap.get(advertiseAutoRuleStatus.getItemId());
                    if (Objects.nonNull(amazonAdGroup)) {
                        //设置这个组受控的TargetType
                        advertiseAutoRuleStatus.setGroupType(amazonAdGroup.getAdGroupType());
                        if (AmazonAd.AdGroupTypeEnum.AUTO.getType().equals(amazonAdGroup.getAdGroupType())) {
                            advertiseAutoRuleStatus.setTargetType(AutoRuleTargetTypeEnum.autoTarget.getTargetType());
                        } else if (AmazonAd.AdGroupTypeEnum.KEYWORD.getType().equals(amazonAdGroup.getAdGroupType())) {
                            advertiseAutoRuleStatus.setTargetType(AutoRuleTargetTypeEnum.keywordTarget.getTargetType());
                        } else if (AmazonAd.AdGroupTypeEnum.TARGETING.getType().equals(amazonAdGroup.getAdGroupType())) {
                            advertiseAutoRuleStatus.setTargetType(AutoRuleTargetTypeEnum.productTarget.getTargetType());
                        }
                        //设置活动id和组id
                        advertiseAutoRuleStatus.setCampaignId(amazonAdGroup.getCampaignId());
                        advertiseAutoRuleStatus.setAdGroupId(amazonAdGroup.getAdGroupId());
                    }
                } else if (AutoRuleSubmitAdTypeEnum.SB.getCode().equals(advertiseAutoRuleStatus.getAdType())) {
                    AmazonSbAdGroup amazonAdGroup = sbGroupMap.get(advertiseAutoRuleStatus.getItemId());
                    if (Objects.nonNull(amazonAdGroup)) {
                        //设置这个组受控的TargetType
                        advertiseAutoRuleStatus.setGroupType(amazonAdGroup.getAdGroupType());
                        if (SbAdGroupTypeEnum.keyword.getAdGroupType().equals(amazonAdGroup.getAdGroupType())) {
                            advertiseAutoRuleStatus.setTargetType(AutoRuleTargetTypeEnum.keywordTarget.getTargetType());
                        } else if (SbAdGroupTypeEnum.product.getAdGroupType().equals(amazonAdGroup.getAdGroupType())) {
                            advertiseAutoRuleStatus.setTargetType(AutoRuleTargetTypeEnum.productTarget.getTargetType());
                        }
                        //设置活动id和组id
                        advertiseAutoRuleStatus.setCampaignId(amazonAdGroup.getCampaignId());
                        advertiseAutoRuleStatus.setAdGroupId(amazonAdGroup.getAdGroupId());
                    }
                } else if (AutoRuleSubmitAdTypeEnum.SD.getCode().equals(advertiseAutoRuleStatus.getAdType())) {
                    AmazonSdAdGroup amazonAdGroup = sdGroupMap.get(advertiseAutoRuleStatus.getItemId());
                    if (Objects.nonNull(amazonAdGroup)) {
                        //设置这个组受控的TargetType
                        //sd商品和受众融合了，一个组下可以共存，所以对于组受控都设置为商品投放
                        advertiseAutoRuleStatus.setGroupType(AutoRuleTargetTypeEnum.productTarget.getTargetType());
                        advertiseAutoRuleStatus.setTargetType(AutoRuleTargetTypeEnum.productTarget.getTargetType());
                        //设置活动id和组id
                        advertiseAutoRuleStatus.setCampaignId(amazonAdGroup.getCampaignId());
                        advertiseAutoRuleStatus.setAdGroupId(amazonAdGroup.getAdGroupId());
                    }
                }
                // 获取不到广告组记录则说明传参有问题，直接报错
                if(StringUtil.isEmpty(advertiseAutoRuleStatus.getCampaignId())){
                    throw new AutoRuleException("根据广告组id获取广告组信息异常,itemId："+advertiseAutoRuleStatus.getItemId());
                }
            }

        } else {
            //定义变量
            Map<String, AmazonAdKeyword> spkeywordMap = new HashMap<>();
            Map<String, AmazonAdTargeting> spTargetMap = new HashMap<>();
            Map<String, AmazonSbAdKeyword> sbkeywordMap = new HashMap<>();
            Map<String, AmazonSbAdTargeting> sbTargetMap = new HashMap<>();
            Map<String, AmazonSdAdTargeting> sdTargetMap = new HashMap<>();

            //数据分组，对应5张表
            Map<String, List<SubmitAutoRuleVo>> autoRuleVosByAdTypeMap = new HashMap<>();
            submitAutoRuleVos.forEach(x -> {
                if (AutoRuleSubmitAdTypeEnum.SD.getCode().equals(x.getAdType())) {
                    autoRuleVosByAdTypeMap.computeIfAbsent(x.getAdType(), k -> new ArrayList<>()).add(x);
                } else if (AutoRuleSubmitAdTypeEnum.SB.getCode().equals(x.getAdType())) {
                    if (AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(x.getTargetType())) {
                        autoRuleVosByAdTypeMap.computeIfAbsent(x.getAdType() + x.getTargetType(), k -> new ArrayList<>()).add(x);
                    } else if (AutoRuleTargetTypeEnum.productTarget.getTargetType().equals(x.getTargetType())) {
                        autoRuleVosByAdTypeMap.computeIfAbsent(x.getAdType() + x.getTargetType(), k -> new ArrayList<>()).add(x);
                    }
                } else if (AutoRuleSubmitAdTypeEnum.SP.getCode().equals(x.getAdType())) {
                    if (AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(x.getTargetType())) {
                        autoRuleVosByAdTypeMap.computeIfAbsent(x.getAdType() + x.getTargetType(), k -> new ArrayList<>()).add(x);
                    } else if (AutoRuleTargetTypeEnum.productTarget.getTargetType().equals(x.getTargetType()) || AutoRuleTargetTypeEnum.autoTarget.getTargetType().equals(x.getTargetType())) {
                        autoRuleVosByAdTypeMap.computeIfAbsent(x.getAdType() + AutoRuleTargetTypeEnum.productTarget.getTargetType(), k -> new ArrayList<>()).add(x);
                    }
                }
            });

            //查询基础数据，对应5张表
            for (Map.Entry<String, List<SubmitAutoRuleVo>> entry : autoRuleVosByAdTypeMap.entrySet()) {
                List<String> itemIdList = entry.getValue().stream().map(x -> x.getItemId()).collect(Collectors.toList());
                if (AutoRuleSubmitAdTypeEnum.SD.getCode().equals(entry.getKey())) {
                    //SD
                    List<AmazonSdAdTargeting> targetingList = amazonSdAdTargetingDao.listByTargetId(template.getPuid(), template.getShopId(), itemIdList);
                    if (CollectionUtils.isNotEmpty(targetingList)) {
                        sdTargetMap = targetingList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSdAdTargeting::getTargetId, e -> e, (v1, v2) -> v1));
                    }
                } else if ((AutoRuleSubmitAdTypeEnum.SB.getCode() + AutoRuleTargetTypeEnum.keywordTarget.getTargetType()).equals(entry.getKey())) {
                    //SB关键词
                    List<AmazonSbAdKeyword> targetingList = amazonSbAdKeywordDao.listByKeywordId(template.getPuid(), template.getShopId(), itemIdList);
                    if (CollectionUtils.isNotEmpty(targetingList)) {
                        sbkeywordMap = targetingList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdKeyword::getKeywordId, e -> e, (v1, v2) -> v1));
                    }
                } else if ((AutoRuleSubmitAdTypeEnum.SB.getCode() + AutoRuleTargetTypeEnum.productTarget.getTargetType()).equals(entry.getKey())) {
                    //SB商品
                    List<AmazonSbAdTargeting> targetingList = amazonSbAdTargetingDao.listByTargetId(template.getPuid(), template.getShopId(), itemIdList);
                    if (CollectionUtils.isNotEmpty(targetingList)) {
                        sbTargetMap = targetingList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdTargeting::getTargetId, e -> e, (v1, v2) -> v1));
                    }
                } else if ((AutoRuleSubmitAdTypeEnum.SP.getCode() + AutoRuleTargetTypeEnum.keywordTarget.getTargetType()).equals(entry.getKey())) {
                    //SP关键词
                    List<AmazonAdKeyword> targetingList = amazonAdKeywordShardingDao.getByKeywordIds(template.getPuid(), template.getShopId(), itemIdList);
                    if (CollectionUtils.isNotEmpty(targetingList)) {
                        spkeywordMap = targetingList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdKeyword::getKeywordId, e -> e, (v1, v2) -> v1));
                    }
                } else if ((AutoRuleSubmitAdTypeEnum.SP.getCode() + AutoRuleTargetTypeEnum.productTarget.getTargetType()).equals(entry.getKey())) {
                    //SP商品+自动
                    List<AmazonAdTargeting> targetingList = amazonAdTargetingShardingDao.getByAdTargetIds(template.getPuid(), template.getShopId(), itemIdList);
                    if (CollectionUtils.isNotEmpty(targetingList)) {
                        spTargetMap = targetingList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdTargeting::getTargetId, e -> e, (v1, v2) -> v1));
                    }
                }
            }

            //构建status集合
            statusList = buildAutoRuleStatusList(puid, updateId, submitAutoRuleVos, template, existItemIdList);

            //单个投放作为受控对象的属性set
            for (AdvertiseAutoRuleStatus status : statusList) {
                if (AutoRuleTypeEnum.keywordAcosRaisePrice.getValue().equals(status.getRuleType()) ||
                        (AutoRuleTypeEnum.keywordAcosLowerPrice.getValue().equals(status.getRuleType()))) {
                    // 预置模板前端没传子受控对象类型，默认赋值子受控
                    status.setChildrenItemType(AutoRuleChildrenItemTypeEnum.CHILDREN_TARGET.toString());
                }
                if (AutoRuleSubmitAdTypeEnum.SD.getCode().equals(status.getAdType())) {
                    AmazonSdAdTargeting target = sdTargetMap.get(status.getItemId());
                    if (Objects.nonNull(target)) {
                        status.setKeywordText(target.getTargetText());
                        status.setCampaignId(target.getCampaignId());
                        status.setAdGroupId(target.getAdGroupId());
                    }
                } else if (AutoRuleSubmitAdTypeEnum.SB.getCode().equals(status.getAdType())) {
                    if (AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(status.getTargetType())) {
                        AmazonSbAdKeyword keyword = sbkeywordMap.get(status.getItemId());
                        if (Objects.nonNull(keyword)) {
                            status.setKeywordText(keyword.getKeywordText());
                            status.setCampaignId(keyword.getCampaignId());
                            status.setAdGroupId(keyword.getAdGroupId());
                        }
                    } else if (AutoRuleTargetTypeEnum.productTarget.getTargetType().equals(status.getTargetType())) {
                        AmazonSbAdTargeting target = sbTargetMap.get(status.getItemId());
                        if (Objects.nonNull(target)) {
                            status.setKeywordText(target.getTargetText());
                            status.setCampaignId(target.getCampaignId());
                            status.setAdGroupId(target.getAdGroupId());
                        }
                    }
                } else if (AutoRuleSubmitAdTypeEnum.SP.getCode().equals(status.getAdType())) {
                    if (AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(status.getTargetType())) {
                        AmazonAdKeyword keyword = spkeywordMap.get(status.getItemId());
                        if (Objects.nonNull(keyword)) {
                            status.setKeywordText(keyword.getKeywordText());
                            status.setCampaignId(keyword.getCampaignId());
                            status.setAdGroupId(keyword.getAdGroupId());
                        }
                    } else if (AutoRuleTargetTypeEnum.productTarget.getTargetType().equals(status.getTargetType()) || AutoRuleTargetTypeEnum.autoTarget.getTargetType().equals(status.getTargetType())) {
                        AmazonAdTargeting target = spTargetMap.get(status.getItemId());
                        if (Objects.nonNull(target)) {
                            status.setKeywordText(target.getTargetingValue());
                            status.setCampaignId(target.getCampaignId());
                            status.setAdGroupId(target.getAdGroupId());
                        }
                    }
                }
                // 获取不到投放记录则说明传参有问题，直接报错
                if(StringUtil.isEmpty(status.getCampaignId())){
                    throw new AutoRuleException("根据投放id获取投放信息异常,itemId："+status.getItemId());
                }
            }
        }

        return statusList;

    }

    /**
     * 广告组
     *
     * @param puid
     * @param submitAutoRuleVos
     * @param template
     * @param existItemIdList
     * @return
     */
    private List<AdvertiseAutoRuleStatus> submitAutoRule4Group(Integer puid, Integer updateUid, List<SubmitAutoRuleVo> submitAutoRuleVos, AdvertiseAutoRuleTemplate template, List<String> existItemIdList) {
        List<AdvertiseAutoRuleStatus> statusList = buildAutoRuleStatusList(puid, updateUid, submitAutoRuleVos, template, existItemIdList);
        return statusList;
    }

    /**
     * 广告活动
     *
     * @param puid
     * @param submitAutoRuleVos
     * @param template
     * @param existItemIdList
     * @return
     */
    private List<AdvertiseAutoRuleStatus> submitAutoRule4Campaign(Integer puid, Integer updateUid, List<SubmitAutoRuleVo> submitAutoRuleVos, AdvertiseAutoRuleTemplate template, List<String> existItemIdList) {
        List<AdvertiseAutoRuleStatus> statusList = buildAutoRuleStatusList(puid, updateUid, submitAutoRuleVos, template, existItemIdList);
        return statusList;
    }

    /**
     * 搜索词
     *
     * @param puid
     * @param submitAutoRuleVos
     * @param template
     * @param existItemIdList
     * @return
     */
    private List<AdvertiseAutoRuleStatus> submitAutoRule4GroupSearchQuery(Integer puid, Integer updateUid, List<SubmitAutoRuleVo> submitAutoRuleVos, AdvertiseAutoRuleTemplate template, List<String> existItemIdList) {
        //校验广告销量指标与SB广告
        if (StringUtils.isNotBlank(template.getRule())) {
            List<AutoRuleJson> autoRuleJsonList = JSONUtil.jsonToArray(template.getRule(), AutoRuleJson.class);
            if (CollectionUtils.isNotEmpty(autoRuleJsonList)) {
                boolean orderNumBool = autoRuleJsonList.stream().anyMatch(e -> RuleIndexTypePb.RuleIndexType.orderNum.name().equals(e.getRuleIndex()));
                if (orderNumBool && submitAutoRuleVos.stream().anyMatch(e -> Constants.SB.equals(e.getAdType()))) {
                    throw new AutoRuleException("广告销量添加受控对象不支持品牌广告(SB)");
                }
            }
        }

        //搜索词执行的操作，把词添加到某个指定或自己所在的组下，把这个组对象查询出来
        AmazonAdGroup queryAmazonAdGroup = null;
        AmazonSbAdGroup queryAmazonSbAdGroup = null;
        List<PerformOperationJson> performOperationJsonList = JSONUtil.jsonToArray(template.getPerformOperation(), PerformOperationJson.class);
        if (CollectionUtils.isNotEmpty(performOperationJsonList) && StringUtils.isNotBlank(performOperationJsonList.get(0).getAdGroupId())) {
            queryAmazonAdGroup = amazonAdGroupDao.getByAdGroupId(puid, submitAutoRuleVos.get(0).getShopId(), performOperationJsonList.get(0).getAdGroupId());
            queryAmazonSbAdGroup = amazonSbAdGroupDao.getByAdGroupId(puid, submitAutoRuleVos.get(0).getShopId(), performOperationJsonList.get(0).getAdGroupId());
        }

        //如果是单独的搜索词受控 CHILDREN_SEARCH_QUERY
        Map<String, AmazonAdGroup> amazonAdGroupMap = null;
        Map<String, AmazonSbAdGroup> amazonSbAdGroupMap = null;
        List<String> adGroupIds;
        if (ChildrenItemType.CHILDREN_SEARCH_QUERY.name().equals(submitAutoRuleVos.get(0).getChildrenItemType())) {
            //查询组基础信息，并转成map后续使用，收集受控对象中的adGroupId去查
            adGroupIds = submitAutoRuleVos.stream().map(SubmitAutoRuleVo::getAdGroupId).collect(Collectors.toList());
        } else {
            //如果是组受控的搜索词，itemId就是组id，查询组基础信息
            adGroupIds = submitAutoRuleVos.stream().map(SubmitAutoRuleVo::getItemId).collect(Collectors.toList());
        }

        List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.getAdGroupByIds(puid, submitAutoRuleVos.get(0).getShopId(), adGroupIds);
        List<AmazonSbAdGroup> amazonSbAdGroups = amazonSbAdGroupDao.getAdGroupByIds(puid, submitAutoRuleVos.get(0).getShopId(), adGroupIds);
        if (CollectionUtils.isNotEmpty(amazonAdGroups)) {
            amazonAdGroupMap = amazonAdGroups.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e, (v1, v2) -> v1));
        }
        if (CollectionUtils.isNotEmpty(amazonSbAdGroups)) {
            amazonSbAdGroupMap = amazonSbAdGroups.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, e -> e, (v1, v2) -> v1));
        }

        // 获取模板对应的子受控对象类型
        List<AdvertiseAutoRuleStatus> childrenItemTypeList = advertiseAutoRuleStatusDao.selectChildrenItemType(puid, template.getShopId(), template.getId());

        List<AdvertiseAutoRuleStatus> statusList = buildAutoRuleStatusList(puid, updateUid, submitAutoRuleVos, template, existItemIdList);

        //提交的受控对象数据转map,搜索词类型的受控对象属性set时还需取对应vo的字段
        Map<String, SubmitAutoRuleVo> submitAutoRuleVoMap = submitAutoRuleVos.stream().collect(Collectors.toMap(SubmitAutoRuleVo::getItemId, e -> e, (v1, v2) -> v1));

        //搜索词类型的受控对象属性set
        for (AdvertiseAutoRuleStatus advertiseAutoRuleStatus : statusList) {
            //取得该status对应的提交数据
            SubmitAutoRuleVo submitAutoRuleVo = submitAutoRuleVoMap.get(advertiseAutoRuleStatus.getItemId());
            if (Objects.isNull(submitAutoRuleVo)) {
                log.error("受控对象数据错误, {}", advertiseAutoRuleStatus);
                throw new AutoRuleException("受控对象数据错误");
            }
            // 子受控对象类型与当前模板的子受控对象类型不一致
            if(StringUtil.isNotEmpty(advertiseAutoRuleStatus.getChildrenItemType()) && CollectionUtil.isNotEmpty(childrenItemTypeList) && !childrenItemTypeList.get(0).getChildrenItemType().equals(advertiseAutoRuleStatus.getChildrenItemType())){
                throw new AutoRuleException("子受控对象类型错误");
            }
            if (AutoRuleTypeEnum.searchQueryAutoUpdateBidding.getValue().equals(advertiseAutoRuleStatus.getRuleType())) {
                // 预置模板前端没传子受控对象类型，默认赋值组受控
                advertiseAutoRuleStatus.setChildrenItemType(AutoRuleChildrenItemTypeEnum.CHILDREN_SEARCH_QUERY_GROUP.toString());
            }
            //搜索词单独受控
            if (ChildrenItemType.CHILDREN_SEARCH_QUERY.name().equals(advertiseAutoRuleStatus.getChildrenItemType())) {
                // 单独受控itemId对应的是queryId，md5生成32位字符串，如果传纯数字说明是异常数据
                if(StringUtils.isNumeric(advertiseAutoRuleStatus.getItemId())){
                    throw new AutoRuleException("搜索词单独受控受控对象id异常,itemId："+advertiseAutoRuleStatus.getItemId());
                }
                //搜索词单独受控
                advertiseAutoRuleStatus.setQueryAdGroupId(submitAutoRuleVo.getAdGroupId());
                //sp搜索词
                if (AutoRuleSubmitAdTypeEnum.SP.getCode().equals(advertiseAutoRuleStatus.getAdType()) && MapUtils.isNotEmpty(amazonAdGroupMap) && amazonAdGroupMap.containsKey(submitAutoRuleVo.getAdGroupId())) {
                    advertiseAutoRuleStatus.setGroupType(amazonAdGroupMap.get(submitAutoRuleVo.getAdGroupId()).getAdGroupType());
                    advertiseAutoRuleStatus.setCampaignId(amazonAdGroupMap.get(submitAutoRuleVo.getAdGroupId()).getCampaignId());
                    advertiseAutoRuleStatus.setAdGroupId(submitAutoRuleVo.getAdGroupId());
                    advertiseAutoRuleStatus.setTargetAdType(AutoRuleSubmitAdTypeEnum.SP.getCode());
                    advertiseAutoRuleStatus.setGroupType(amazonAdGroupMap.get(submitAutoRuleVo.getAdGroupId()).getAdGroupType());
                    advertiseAutoRuleStatus.setDefaultBid(BigDecimal.valueOf(amazonAdGroupMap.get(submitAutoRuleVo.getAdGroupId()).getDefaultBid()));
                } else if (AutoRuleSubmitAdTypeEnum.SB.getCode().equals(advertiseAutoRuleStatus.getAdType()) && MapUtils.isNotEmpty(amazonSbAdGroupMap) && amazonSbAdGroupMap.containsKey(submitAutoRuleVo.getAdGroupId())) {
                    //sb搜索词
                    advertiseAutoRuleStatus.setGroupType(amazonSbAdGroupMap.get(submitAutoRuleVo.getAdGroupId()).getAdGroupType());
                    advertiseAutoRuleStatus.setCampaignId(amazonSbAdGroupMap.get(submitAutoRuleVo.getAdGroupId()).getCampaignId());
                    advertiseAutoRuleStatus.setAdGroupId(submitAutoRuleVo.getAdGroupId());
                    advertiseAutoRuleStatus.setTargetAdType(AutoRuleSubmitAdTypeEnum.SB.getCode());
                    advertiseAutoRuleStatus.setGroupType(amazonSbAdGroupMap.get(submitAutoRuleVo.getAdGroupId()).getAdGroupType());
                    advertiseAutoRuleStatus.setDefaultBid(amazonSbAdGroupMap.get(submitAutoRuleVo.getAdGroupId()).getBid());
                }
                if (StringUtils.isNotBlank(template.getPerformOperation())) {
                    //执行操作，添加投放或否投
                    PerformOperationJson performOperationJson = JSONUtil.jsonToArray(template.getPerformOperation(), PerformOperationJson.class).get(0);
                    if (AutoRuleOperationTypeEnum.addTarget.getRuleAction().equals(performOperationJson.getRuleAction())
                            && PerformOperationJson.AppointAdGroupTypeEnum.DESIGNATED_AD_GROUP.getValue().equals(performOperationJson.getAppointAdGroupType())) {
                        if (queryAmazonAdGroup != null) {
                            advertiseAutoRuleStatus.setTargetAdType(AutoRuleSubmitAdTypeEnum.SP.getCode());
                            advertiseAutoRuleStatus.setTarGroupType(queryAmazonAdGroup.getAdGroupType());
                            advertiseAutoRuleStatus.setDefaultBid(BigDecimal.valueOf(queryAmazonAdGroup.getDefaultBid()));
                        } else if (queryAmazonSbAdGroup != null) {
                            advertiseAutoRuleStatus.setTargetAdType(AutoRuleSubmitAdTypeEnum.SB.getCode());
                            advertiseAutoRuleStatus.setTarGroupType(queryAmazonSbAdGroup.getAdGroupType());
                            advertiseAutoRuleStatus.setDefaultBid(queryAmazonSbAdGroup.getBid());
                        }
                    }
                }
            } else {
                //搜索词组受控
                advertiseAutoRuleStatus.setQueryAdGroupId(submitAutoRuleVo.getAdGroupId());
                //sp
                if (AutoRuleSubmitAdTypeEnum.SP.getCode().equals(advertiseAutoRuleStatus.getAdType()) && MapUtils.isNotEmpty(amazonAdGroupMap) && amazonAdGroupMap.containsKey(submitAutoRuleVo.getItemId())) {
                    advertiseAutoRuleStatus.setTargetAdType(AutoRuleSubmitAdTypeEnum.SP.getCode());
                    advertiseAutoRuleStatus.setGroupType(amazonAdGroupMap.get(advertiseAutoRuleStatus.getItemId()).getAdGroupType());
                    advertiseAutoRuleStatus.setTarGroupType(amazonAdGroupMap.get(advertiseAutoRuleStatus.getItemId()).getAdGroupType());
                    advertiseAutoRuleStatus.setAdGroupId(submitAutoRuleVo.getAdGroupId());
                    advertiseAutoRuleStatus.setCampaignId(amazonAdGroupMap.get(advertiseAutoRuleStatus.getItemId()).getCampaignId());
                    advertiseAutoRuleStatus.setDefaultBid(BigDecimal.valueOf(amazonAdGroupMap.get(advertiseAutoRuleStatus.getItemId()).getDefaultBid()));
                }else if (AutoRuleSubmitAdTypeEnum.SB.getCode().equals(advertiseAutoRuleStatus.getAdType()) && MapUtils.isNotEmpty(amazonSbAdGroupMap) && amazonSbAdGroupMap.containsKey(submitAutoRuleVo.getItemId())) {
                    advertiseAutoRuleStatus.setTargetAdType(AutoRuleSubmitAdTypeEnum.SB.getCode());
                    advertiseAutoRuleStatus.setGroupType(amazonSbAdGroupMap.get(advertiseAutoRuleStatus.getItemId()).getAdGroupType());
                    advertiseAutoRuleStatus.setTarGroupType(amazonSbAdGroupMap.get(advertiseAutoRuleStatus.getItemId()).getAdGroupType());
                    advertiseAutoRuleStatus.setCampaignId(amazonSbAdGroupMap.get(advertiseAutoRuleStatus.getItemId()).getCampaignId());
                    advertiseAutoRuleStatus.setAdGroupId(submitAutoRuleVo.getAdGroupId());
                    advertiseAutoRuleStatus.setDefaultBid(amazonSbAdGroupMap.get(submitAutoRuleVo.getAdGroupId()).getBid());
                }else{
                    // 组受控获取不到广告组 说明是异常数据
                    throw new AutoRuleException("根据广告组id获取广告组信息异常,itemId："+advertiseAutoRuleStatus.getItemId());
                }

                if (StringUtils.isNotBlank(template.getPerformOperation())) {
                    //执行操作，添加投放或否投
                    PerformOperationJson performOperationJson = JSONUtil.jsonToArray(template.getPerformOperation(), PerformOperationJson.class).get(0);
                    advertiseAutoRuleStatus.setOperationType(AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction()));
                    if (AutoRuleOperationTypeEnum.addTarget.getRuleAction().equals(performOperationJson.getRuleAction())
                            && PerformOperationJson.AppointAdGroupTypeEnum.DESIGNATED_AD_GROUP.getValue().equals(performOperationJson.getAppointAdGroupType())) {
                        if (queryAmazonAdGroup != null) {
                            advertiseAutoRuleStatus.setTargetAdType(AutoRuleSubmitAdTypeEnum.SP.getCode());
                            advertiseAutoRuleStatus.setTarGroupType(queryAmazonAdGroup.getAdGroupType());
                            advertiseAutoRuleStatus.setDefaultBid(BigDecimal.valueOf(queryAmazonAdGroup.getDefaultBid()));
                        } else if (queryAmazonSbAdGroup != null) {
                            advertiseAutoRuleStatus.setTargetAdType(AutoRuleSubmitAdTypeEnum.SB.getCode());
                            advertiseAutoRuleStatus.setTarGroupType(queryAmazonSbAdGroup.getAdGroupType());
                            advertiseAutoRuleStatus.setDefaultBid(queryAmazonSbAdGroup.getBid());
                        }
                    }
                }
            }
        }

        return statusList;
    }

    /**
     * 构建受控对象status集合
     *
     * @param puid
     * @param submitAutoRuleVos
     * @param template
     * @param existItemIdList
     * @return
     */
    private List<AdvertiseAutoRuleStatus> buildAutoRuleStatusList(Integer puid, Integer updateUid, List<SubmitAutoRuleVo> submitAutoRuleVos, AdvertiseAutoRuleTemplate template, List<String> existItemIdList) {
        //批量生成id
        List<Long> taskIdList = advertiseAutoRuleStatusSequenceDao.batchGenId(submitAutoRuleVos.size());
        List<AdvertiseAutoRuleStatus> autoRuleStatusList = Lists.newArrayList();
        //遍历处理
        for (int i = 0; i < submitAutoRuleVos.size(); i++) {
            SubmitAutoRuleVo submitAutoRuleVo = submitAutoRuleVos.get(i);
            if (!template.getShopId().equals(submitAutoRuleVo.getShopId())) {
                log.error("受控对象店铺id异常 templateShopId={} statusShopId={} itemId={} itemType={}", template.getShopId(), submitAutoRuleVo.getShopId(), submitAutoRuleVo.getItemId(), template.getItemType());
                throw new AutoRuleException("受控对象店铺id异常");
            }
            if (!template.getMarketplaceId().equals(submitAutoRuleVo.getMarketplaceId())) {
                log.error("受控对象站点id异常 templateMarketplaceId={} statusMarketplaceId={} itemId={} itemType={}", template.getMarketplaceId(), submitAutoRuleVo.getMarketplaceId(), submitAutoRuleVo.getItemId(), template.getItemType());
                throw new AutoRuleException("受控对象站点id异常");
            }

            //防止添加重复受控对象数据
            if (CollectionUtils.isNotEmpty(existItemIdList) && existItemIdList.contains(submitAutoRuleVo.getItemId())) {
                continue;
            }

            AdvertiseAutoRuleStatus advertiseAutoRuleStatus = new AdvertiseAutoRuleStatus();
            Long taskId = taskIdList.get(i);
            advertiseAutoRuleStatus.setId(taskId);
            advertiseAutoRuleStatus.setTaskId(taskId);
            advertiseAutoRuleStatus.setPuid(puid);
            advertiseAutoRuleStatus.setCreateUid(updateUid);
            advertiseAutoRuleStatus.setUpdateUid(updateUid);
            advertiseAutoRuleStatus.setShopId(submitAutoRuleVo.getShopId());
            advertiseAutoRuleStatus.setMarketplaceId(submitAutoRuleVo.getMarketplaceId());
            advertiseAutoRuleStatus.setAdType(submitAutoRuleVo.getAdType().toUpperCase());
            advertiseAutoRuleStatus.setVersion(template.getVersion());
            advertiseAutoRuleStatus.setTemplateId(template.getId());
            advertiseAutoRuleStatus.setTimeType(template.getTimeType());
            advertiseAutoRuleStatus.setChooseTimeType(template.getChooseTimeType());
            advertiseAutoRuleStatus.setTimeRule(template.getTimeRule());
            advertiseAutoRuleStatus.setStartDate(template.getStartDate());
            advertiseAutoRuleStatus.setEndDate(template.getEndDate());
            advertiseAutoRuleStatus.setItemId(submitAutoRuleVo.getItemId());
            advertiseAutoRuleStatus.setItemType(template.getItemType());
            advertiseAutoRuleStatus.setRule(template.getRule());
            advertiseAutoRuleStatus.setTargetType(submitAutoRuleVo.getTargetType());
            advertiseAutoRuleStatus.setStatus(submitAutoRuleVo.getStatus());
            advertiseAutoRuleStatus.setProfileId(template.getProfileId());
            advertiseAutoRuleStatus.setExecuteType(template.getExecuteType());
            advertiseAutoRuleStatus.setPerformOperation(template.getPerformOperation());
            advertiseAutoRuleStatus.setItemName(submitAutoRuleVo.getItemName());
            advertiseAutoRuleStatus.setQueryType(submitAutoRuleVo.getQueryType());
            if (submitAutoRuleVo.getBiddingValue() != null || submitAutoRuleVo.getPlacementTopBidRatio()!= null) {
                OriginValueVo originValueVo = new OriginValueVo();
                if (submitAutoRuleVo.getBiddingValue() != null) {
                    originValueVo.setBiddingValue(submitAutoRuleVo.getBiddingValue());
                }
                if (submitAutoRuleVo.getPlacementTopBidRatio()!= null) {
                    originValueVo.setPlacementTopBidRatio(submitAutoRuleVo.getPlacementTopBidRatio());
                }
                advertiseAutoRuleStatus.setOriginValue(JSONUtil.objectToJson(originValueVo));
            }
            if (submitAutoRuleVo.getBudgetValue() != null) {
                OriginValueVo originValueVo = new OriginValueVo();
                originValueVo.setBudgetValue(submitAutoRuleVo.getBudgetValue());
                advertiseAutoRuleStatus.setOriginValue(JSONUtil.objectToJson(originValueVo));
            }
            if (submitAutoRuleVo.getDefaultBiddingValue() != null) {
                OriginValueVo originValueVo = new OriginValueVo();
                originValueVo.setDefaultBiddingValue(submitAutoRuleVo.getDefaultBiddingValue());
                advertiseAutoRuleStatus.setOriginValue(JSONUtil.objectToJson(originValueVo));
            }
            advertiseAutoRuleStatus.setSetRelation(template.getSetRelation());
            advertiseAutoRuleStatus.setCallbackOperate(template.getCallbackOperate());
            advertiseAutoRuleStatus.setCallbackState(template.getCallbackState());
            advertiseAutoRuleStatus.setExecuteTimeSpaceUnit(template.getExecuteTimeSpaceUnit());
            advertiseAutoRuleStatus.setExecuteTimeSpaceValue(template.getExecuteTimeSpaceValue());
            advertiseAutoRuleStatus.setMessageReminderType(template.getMessageReminderType());

            //维护子受控对象类型，前端预设模板传值为null，所以数据库中该字段可能为null，但是null根据模板不同可能表示意思不同
            //其中活动、广告组、抢排名，需要忽略该字段的值(数据库可能有其他值)，这三种受控对象都没有子受控类型
            //如果受控对象是投放/关键词，那么：
            //自定义规则中，有单个受控和组受控，children_item_type值，组受控为CHILDREN_TARGET_GROUP，单个受控为CHILDREN_TARGET
            //预设模板中，只有单个受控，children_item_type值为null，即null表示单个受控
            //如果受控对象是搜索词，那么：
            //自定义规则中，有单个受控和组受控，children_item_type值，组受控为CHILDREN_SEARCH_QUERY_GROUP，单个受控为CHILDREN_SEARCH_QUERY
            //预设模板中，只有组受控，children_item_type值为null，即null表示组个受控
            advertiseAutoRuleStatus.setChildrenItemType(submitAutoRuleVo.getChildrenItemType());
            advertiseAutoRuleStatus.setRuleType(template.getRuleType());
            advertiseAutoRuleStatus.setNextExecuteTime(new Date());
            advertiseAutoRuleStatus.setArchivedState(AutoRuleBooleanEnum.FALSE.getCode());
            // 解析操作类型
            if (StringUtils.isNotEmpty(template.getPerformOperation()) && !AutoRuleItemTypeEnum.KEYWORD_TARGET.getName().equals(advertiseAutoRuleStatus.getItemType())) {
                PerformOperationJson performOperationJson = JSONUtil.jsonToArray(template.getPerformOperation(), PerformOperationJson.class).get(0);
                advertiseAutoRuleStatus.setOperationType(AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction()));
            }
            autoRuleStatusList.add(advertiseAutoRuleStatus);
        }

        return autoRuleStatusList;
    }

    /**
     * 抢排名
     *
     * @param puid
     * @param submitAutoRuleVos
     * @param template
     * @param existItemIdList
     * @return
     */
    private List<AdvertiseAutoRuleStatus> submitAutoRule4KeywordTarget(Integer puid, Integer updateUid, List<SubmitAutoRuleVo> submitAutoRuleVos, AdvertiseAutoRuleTemplate template, List<String> existItemIdList) {

        Map<String, AmazonAdKeyword> keywordMap = null;

        List<String> targetItemIdList = submitAutoRuleVos.stream().map(SubmitAutoRuleVo::getItemId).collect(Collectors.toList());
        List<AmazonAdKeyword> amazonAdKeywords = amazonAdKeywordDaoRoutingService.getByKeywordIds(template.getPuid(), template.getShopId(), targetItemIdList, Constants.BIDDABLE);
        if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
            keywordMap = amazonAdKeywords.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdKeyword::getKeywordId, e -> e, (v1, v2) -> v1));
        }

        List<AdvertiseAutoRuleStatus> statusList = buildAutoRuleStatusList(puid, updateUid, submitAutoRuleVos, template, existItemIdList);

        //抢排名类型的受控对象属性set
        for (AdvertiseAutoRuleStatus advertiseAutoRuleStatus : statusList) {
            if (MapUtils.isNotEmpty(keywordMap) && keywordMap.containsKey(advertiseAutoRuleStatus.getItemId())) {
                AmazonAdKeyword amazonAdKeyword = keywordMap.get(advertiseAutoRuleStatus.getItemId());
                advertiseAutoRuleStatus.setKeywordText(amazonAdKeyword.getKeywordText());
                advertiseAutoRuleStatus.setCampaignId(amazonAdKeyword.getCampaignId());
            }
            advertiseAutoRuleStatus.setAsin(template.getAsin());
            advertiseAutoRuleStatus.setSku(template.getSku());
            advertiseAutoRuleStatus.setAdDataRule(template.getAdDataRule());
            advertiseAutoRuleStatus.setDesiredPosition(template.getDesiredPosition());
            advertiseAutoRuleStatus.setAdDataOperate(template.getAdDataOperate());
            advertiseAutoRuleStatus.setAutoPriceRule(template.getAutoPriceRule());
            advertiseAutoRuleStatus.setAutoPriceOperate(template.getAutoPriceOperate());
            advertiseAutoRuleStatus.setBiddingCallbackOperate(template.getBiddingCallbackOperate());
            advertiseAutoRuleStatus.setCheckFrequency(template.getCheckFrequency());
            advertiseAutoRuleStatus.setPostalCodeSettings(template.getPostalCodeSettings());
        }

        return statusList;
    }

    /**
     * 此同步更新受控对象方法不再维护。
     * {@link com.meiyunji.sponsored.service.autoRuleTask.api.strategy.AutoRuleTaskAllApi#updateAutoRule}
     */
    @Deprecated
    @Override
    public Result<String> updateAutoRule(Integer puid, Long templateId, List<UpdateAutoRuleVo> updateAutoRuleVoList, AdvertiseAutoRuleTemplate vo, String traceId) {
        Result<String> result = new Result<>();
        try {

            List<Long> statusIdList = new ArrayList<>(updateAutoRuleVoList.size());
            updateAutoRuleVoList.forEach(x -> statusIdList.add(x.getStatusId()));
            List<AdvertiseAutoRuleStatus> statusList = advertiseAutoRuleStatusDao.getByStatusIds(puid, statusIdList);
            //转map
            Map<Long, AdvertiseAutoRuleStatus> statusMap = new HashMap<>();
            statusList.forEach(x -> statusMap.put(x.getId(), x));

            ShopAuth shopAuth = shopAuthDao.getScAndVcById(vo.getShopId());

            for (UpdateAutoRuleVo updateAutoRuleVo : updateAutoRuleVoList) {
                AdvertiseAutoRuleStatus advertiseAutoRuleStatus = statusMap.get(updateAutoRuleVo.getStatusId());
                if (advertiseAutoRuleStatus == null) {
                    continue;
                }
                String itemType = advertiseAutoRuleStatus.getItemType();
                //如果当前受控对象状态是启用，则需要推送到aadras
                if ("ENABLED".equals(updateAutoRuleVo.getStatus())) {
                    if ("KEYWORD_TARGET".equals(vo.getItemType())) {
                        AmazonAdKeyword amazonAdKeyword = amazonAdKeywordDaoRoutingService.getByKeywordId(puid, advertiseAutoRuleStatus.getShopId(), advertiseAutoRuleStatus.getItemId());
                        if (amazonAdKeyword != null) {
                            advertiseAutoRuleStatus.setKeywordText(amazonAdKeyword.getKeywordText());
                        }
                        advertiseAutoRuleStatus.setAdDataRule(vo.getAdDataRule());
                        advertiseAutoRuleStatus.setDesiredPosition(vo.getDesiredPosition());
                        advertiseAutoRuleStatus.setAdDataOperate(vo.getAdDataOperate());
                        advertiseAutoRuleStatus.setAutoPriceRule(vo.getAutoPriceRule());
                        advertiseAutoRuleStatus.setAutoPriceOperate(vo.getAutoPriceOperate());
                        advertiseAutoRuleStatus.setBiddingCallbackOperate(vo.getBiddingCallbackOperate());
                        advertiseAutoRuleStatus.setCheckFrequency(vo.getCheckFrequency());
                        advertiseAutoRuleStatus.setPostalCodeSettings(vo.getPostalCodeSettings());
                        if (updateAutoRuleVo.getBiddingValue() != null) {
                            OriginValueVo originValueVo = new OriginValueVo();
                            originValueVo.setBiddingValue(updateAutoRuleVo.getBiddingValue());
                            advertiseAutoRuleStatus.setOriginValue(JSONUtil.objectToJson(originValueVo));
                        }
                        advertiseAutoRuleStatus.setVersion(vo.getVersion());
                        advertiseAutoRuleStatusDao.updateKeywordCardStatus(templateId, advertiseAutoRuleStatus);
                    } else {
                        if (StringUtils.isNotBlank(vo.getPerformOperation())) {
                            PerformOperationJson performOperationJson = JSONUtil.jsonToArray(vo.getPerformOperation(), PerformOperationJson.class).get(0);
                            advertiseAutoRuleStatus.setOperationType(AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction()));
                        }
                        if (advertiseAutoRuleStatus.getOperationType() != null) {
                            if (advertiseAutoRuleStatusDao.isSimilarRule(puid, advertiseAutoRuleStatus.getItemId(), advertiseAutoRuleStatus.getOperationType(), templateId) > 0) {
                                advertiseAutoRuleStatus.setHasSimilarRule(1);
                            } else {
                                advertiseAutoRuleStatus.setHasSimilarRule(0);
                            }
                        }
                        advertiseAutoRuleStatusDao.updateStatus(puid, templateId,
                                updateAutoRuleVo.getStatusId(), updateAutoRuleVo.getStatus(),
                                advertiseAutoRuleStatus.getOperationType(), advertiseAutoRuleStatus.getHasSimilarRule(), vo, null);
                    }

                    if ("TARGET".equals(vo.getItemType())) {
                        AmazonAdKeyword keyword = amazonAdKeywordDaoRoutingService.getByKeywordId(advertiseAutoRuleStatus.getPuid(),
                                advertiseAutoRuleStatus.getShopId(), advertiseAutoRuleStatus.getItemId());
                        AmazonAdTargeting amazonAdTargeting = amazonAdTargetDaoRoutingService.getByAdTargetId(advertiseAutoRuleStatus.getPuid(),
                                advertiseAutoRuleStatus.getShopId(), advertiseAutoRuleStatus.getItemId());
                        if (keyword != null) {
                            advertiseAutoRuleStatus.setKeywordText(keyword.getKeywordText());
                            advertiseAutoRuleStatus.setCampaignId(keyword.getCampaignId());
                            advertiseAutoRuleStatus.setAdGroupId(keyword.getAdGroupId());
                        } else if (amazonAdTargeting != null) {
                            advertiseAutoRuleStatus.setKeywordText(amazonAdTargeting.getTargetingValue());
                            advertiseAutoRuleStatus.setCampaignId(amazonAdTargeting.getCampaignId());
                            advertiseAutoRuleStatus.setAdGroupId(amazonAdTargeting.getAdGroupId());
                        }
                    }
                    if ("GROUP_SEARCH_QUERY".equals(advertiseAutoRuleStatus.getItemType())) {
                        if ("CHILDREN_SEARCH_QUERY".equals(advertiseAutoRuleStatus.getChildrenItemType())) {
                            itemType = AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.SEARCH_QUERY.name();
                            if ("SP".equals(advertiseAutoRuleStatus.getAdType())) {
                                AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByAdGroupId(advertiseAutoRuleStatus.getPuid(), advertiseAutoRuleStatus.getShopId(),
                                        advertiseAutoRuleStatus.getMarketplaceId(), advertiseAutoRuleStatus.getQueryAdGroupId());
                                if (amazonAdGroup != null) {
                                    advertiseAutoRuleStatus.setGroupType(amazonAdGroup.getAdGroupType());
                                    advertiseAutoRuleStatus.setTarGroupType(amazonAdGroup.getAdGroupType());
                                    advertiseAutoRuleStatus.setCampaignId(amazonAdGroup.getCampaignId());
                                    advertiseAutoRuleStatus.setAdGroupId(amazonAdGroup.getAdGroupId());
                                    advertiseAutoRuleStatus.setTargetAdType("SP");
                                    advertiseAutoRuleStatus.setDefaultBid(BigDecimal.valueOf(amazonAdGroup.getDefaultBid()));
                                }
                            }
                            if ("SB".equals(advertiseAutoRuleStatus.getAdType())) {
                                AmazonSbAdGroup amazonSbAdGroup = amazonSbAdGroupDao.getByGroupId(advertiseAutoRuleStatus.getPuid(), advertiseAutoRuleStatus.getShopId(), advertiseAutoRuleStatus.getQueryAdGroupId());
                                if (amazonSbAdGroup != null) {
                                    advertiseAutoRuleStatus.setGroupType(amazonSbAdGroup.getAdGroupType());
                                    advertiseAutoRuleStatus.setTarGroupType(amazonSbAdGroup.getAdGroupType());
                                    advertiseAutoRuleStatus.setCampaignId(amazonSbAdGroup.getCampaignId());
                                    advertiseAutoRuleStatus.setAdGroupId(amazonSbAdGroup.getAdGroupId());
                                    advertiseAutoRuleStatus.setTargetAdType("SB");
                                    advertiseAutoRuleStatus.setDefaultBid(amazonSbAdGroup.getBid());
                                }
                            }
                            if (StringUtils.isNotBlank(vo.getPerformOperation())) {
                                PerformOperationJson performOperationJson = JSONUtil.jsonToArray(vo.getPerformOperation(), PerformOperationJson.class).get(0);
                                advertiseAutoRuleStatus.setOperationType(AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction()));
                                if ("addTarget".equals(performOperationJson.getRuleAction()) && "1".equals(performOperationJson.getAppointAdGroupType())) {
                                    AmazonAdGroup queryAmazonAdGroup = amazonAdGroupDao.getByAdGroupId(advertiseAutoRuleStatus.getPuid(), advertiseAutoRuleStatus.getShopId(),
                                            advertiseAutoRuleStatus.getMarketplaceId(), performOperationJson.getAdGroupId());
                                    if (queryAmazonAdGroup != null) {
                                        advertiseAutoRuleStatus.setTargetAdType("SP");
                                        advertiseAutoRuleStatus.setTarGroupType(queryAmazonAdGroup.getAdGroupType());
                                        advertiseAutoRuleStatus.setDefaultBid(BigDecimal.valueOf(queryAmazonAdGroup.getDefaultBid()));
                                    }
                                    AmazonSbAdGroup queryAmazonSbAdGroup = amazonSbAdGroupDao.getByGroupId(advertiseAutoRuleStatus.getPuid(), advertiseAutoRuleStatus.getShopId(), performOperationJson.getAdGroupId());
                                    if (queryAmazonSbAdGroup != null) {
                                        advertiseAutoRuleStatus.setTargetAdType("SB");
                                        advertiseAutoRuleStatus.setTarGroupType(queryAmazonSbAdGroup.getAdGroupType());
                                        advertiseAutoRuleStatus.setDefaultBid(queryAmazonSbAdGroup.getBid());
                                    }
                                }
                            }
                        } else {
                            if ("SP".equals(advertiseAutoRuleStatus.getAdType())) {
                                AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByAdGroupId(advertiseAutoRuleStatus.getPuid(), advertiseAutoRuleStatus.getShopId(),
                                        advertiseAutoRuleStatus.getMarketplaceId(), advertiseAutoRuleStatus.getItemId());
                                if (amazonAdGroup != null) {
                                    advertiseAutoRuleStatus.setTargetAdType("SP");
                                    advertiseAutoRuleStatus.setGroupType(amazonAdGroup.getAdGroupType());
                                    advertiseAutoRuleStatus.setTarGroupType(amazonAdGroup.getAdGroupType());
                                    advertiseAutoRuleStatus.setCampaignId(amazonAdGroup.getCampaignId());
                                    advertiseAutoRuleStatus.setAdGroupId(advertiseAutoRuleStatus.getQueryAdGroupId());
                                    advertiseAutoRuleStatus.setDefaultBid(BigDecimal.valueOf(amazonAdGroup.getDefaultBid()));
                                }
                            } else if ("SB".equals(advertiseAutoRuleStatus.getAdType())) {
                                AmazonSbAdGroup amazonSbAdGroup = amazonSbAdGroupDao.getByGroupId(advertiseAutoRuleStatus.getPuid(), advertiseAutoRuleStatus.getShopId(), advertiseAutoRuleStatus.getItemId());
                                if (amazonSbAdGroup != null) {
                                    advertiseAutoRuleStatus.setTargetAdType("SB");
                                    advertiseAutoRuleStatus.setGroupType(amazonSbAdGroup.getAdGroupType());
                                    advertiseAutoRuleStatus.setTarGroupType(amazonSbAdGroup.getAdGroupType());
                                    advertiseAutoRuleStatus.setCampaignId(amazonSbAdGroup.getCampaignId());
                                    advertiseAutoRuleStatus.setAdGroupId(advertiseAutoRuleStatus.getQueryAdGroupId());
                                    advertiseAutoRuleStatus.setDefaultBid(amazonSbAdGroup.getBid());
                                }
                            }
                            if (StringUtils.isNotBlank(vo.getPerformOperation())) {
                                PerformOperationJson performOperationJson = JSONUtil.jsonToArray(vo.getPerformOperation(), PerformOperationJson.class).get(0);
                                advertiseAutoRuleStatus.setOperationType(AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction()));
                                if ("addTarget".equals(performOperationJson.getRuleAction()) && "1".equals(performOperationJson.getAppointAdGroupType())) {
                                    AmazonAdGroup queryAmazonAdGroup = amazonAdGroupDao.getByAdGroupId(advertiseAutoRuleStatus.getPuid(), advertiseAutoRuleStatus.getShopId(),
                                            advertiseAutoRuleStatus.getMarketplaceId(), performOperationJson.getAdGroupId());
                                    if (queryAmazonAdGroup != null) {
                                        advertiseAutoRuleStatus.setTargetAdType("SP");
                                        advertiseAutoRuleStatus.setTarGroupType(queryAmazonAdGroup.getAdGroupType());
                                        advertiseAutoRuleStatus.setDefaultBid(BigDecimal.valueOf(queryAmazonAdGroup.getDefaultBid()));
                                    }
                                    AmazonSbAdGroup queryAmazonSbAdGroup = amazonSbAdGroupDao.getByGroupId(advertiseAutoRuleStatus.getPuid(), advertiseAutoRuleStatus.getShopId(), performOperationJson.getAdGroupId());
                                    if (queryAmazonSbAdGroup != null) {
                                        advertiseAutoRuleStatus.setTargetAdType("SB");
                                        advertiseAutoRuleStatus.setTarGroupType(queryAmazonSbAdGroup.getAdGroupType());
                                        advertiseAutoRuleStatus.setDefaultBid(queryAmazonSbAdGroup.getBid());
                                    }
                                }
                            }
                        }
                    }
                    advertiseAutoRuleStatus.setTimeType(vo.getTimeType());
                    advertiseAutoRuleStatus.setTimeRule(vo.getTimeRule());
                    advertiseAutoRuleStatus.setStartDate(vo.getStartDate());
                    advertiseAutoRuleStatus.setEndDate(vo.getEndDate());
                    advertiseAutoRuleStatus.setRule(vo.getRule());
                    advertiseAutoRuleStatus.setPerformOperation(vo.getPerformOperation());
                    advertiseAutoRuleStatus.setExecuteType(vo.getExecuteType());
                    advertiseAutoRuleStatus.setSetRelation(vo.getSetRelation());
                    advertiseAutoRuleStatus.setCallbackState(vo.getCallbackState());
                    advertiseAutoRuleStatus.setCallbackOperate(vo.getCallbackOperate());
                    advertiseAutoRuleStatus.setExecuteTimeSpaceUnit(vo.getExecuteTimeSpaceUnit());
                    advertiseAutoRuleStatus.setExecuteTimeSpaceValue(vo.getExecuteTimeSpaceValue());
                    advertiseAutoRuleStatus.setMessageReminderType(vo.getMessageReminderType());
                    //推送至计算服务更新
                    aadrasApiFactory.getAadrasApi(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.
                            valueOf(itemType)).setAutoRuleTask(advertiseAutoRuleStatus.getTaskId(), advertiseAutoRuleStatus, shopAuth);
                }
                // 如果状态是暂停，那么无需推送
                else if ("DISABLED".equals(updateAutoRuleVo.getStatus())) {
                    if ("KEYWORD_TARGET".equals(vo.getItemType())) {
                        advertiseAutoRuleStatus.setTimeType(vo.getTimeType());
                        advertiseAutoRuleStatus.setTimeRule(vo.getTimeRule());
                        advertiseAutoRuleStatus.setStartDate(vo.getStartDate());
                        advertiseAutoRuleStatus.setEndDate(vo.getEndDate());
                        advertiseAutoRuleStatus.setAdDataRule(vo.getAdDataRule());
                        advertiseAutoRuleStatus.setDesiredPosition(vo.getDesiredPosition());
                        advertiseAutoRuleStatus.setAdDataOperate(vo.getAdDataOperate());
                        advertiseAutoRuleStatus.setAutoPriceRule(vo.getAutoPriceRule());
                        advertiseAutoRuleStatus.setAutoPriceOperate(vo.getAutoPriceOperate());
                        advertiseAutoRuleStatus.setBiddingCallbackOperate(vo.getBiddingCallbackOperate());
                        advertiseAutoRuleStatus.setCheckFrequency(vo.getCheckFrequency());
                        advertiseAutoRuleStatus.setPostalCodeSettings(vo.getPostalCodeSettings());
                        if (updateAutoRuleVo.getBiddingValue() != null) {
                            OriginValueVo originValueVo = new OriginValueVo();
                            originValueVo.setBiddingValue(updateAutoRuleVo.getBiddingValue());
                            advertiseAutoRuleStatus.setOriginValue(JSONUtil.objectToJson(originValueVo));
                        }
                        advertiseAutoRuleStatus.setVersion(vo.getVersion());
                        advertiseAutoRuleStatusDao.updateKeywordCardStatus(templateId, advertiseAutoRuleStatus);
                    } else {
                        if (StringUtils.isNotBlank(vo.getPerformOperation())) {
                            PerformOperationJson performOperationJson = JSONUtil.jsonToArray(vo.getPerformOperation(), PerformOperationJson.class).get(0);
                            advertiseAutoRuleStatus.setOperationType(AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction()));
                        }
                        if (advertiseAutoRuleStatus.getOperationType() != null) {
                            if (advertiseAutoRuleStatusDao.isSimilarRule(puid, advertiseAutoRuleStatus.getItemId(), advertiseAutoRuleStatus.getOperationType(), templateId) > 0) {
                                advertiseAutoRuleStatus.setHasSimilarRule(1);
                            } else {
                                advertiseAutoRuleStatus.setHasSimilarRule(0);
                            }
                        }
                        advertiseAutoRuleStatusDao.updateStatus(puid, templateId,
                                updateAutoRuleVo.getStatusId(), updateAutoRuleVo.getStatus(),
                                advertiseAutoRuleStatus.getOperationType(), advertiseAutoRuleStatus.getHasSimilarRule(), vo, null);
                    }
                }
            }
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("自动化规则修改异常:" + e);
            log.error("traceId:{} puid={} 分时调价任务修改异常", traceId, puid, e);
        }
        return result;
    }

    @Override
    public Result<String> updateAutoRuleBid(Integer puid, Integer updateId, Long templateId, List<UpdateAutoRuleVo> updateAutoRuleVoList, String traceId) {
        Result<String> result = new Result<>();
        try {
            List<Long> statusIdList = new ArrayList<>(updateAutoRuleVoList.size());
            updateAutoRuleVoList.forEach(x -> statusIdList.add(x.getStatusId()));
            List<AdvertiseAutoRuleStatus> statusList = advertiseAutoRuleStatusDao.getByStatusIds(puid, statusIdList);
            //转map和收集shopId
            Map<Long, AdvertiseAutoRuleStatus> statusMap = new HashMap<>();
            Set<Integer> shopIdSet = new HashSet<>();
            statusList.forEach(x -> {
                statusMap.put(x.getId(), x);
                shopIdSet.add(x.getShopId());
            });
            List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, new ArrayList<>(shopIdSet));
            Map<Integer, ShopAuth> shopMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(shopAuthList)) {
                shopMap = shopAuthList.stream().filter(Objects::nonNull).collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
            }

            for (UpdateAutoRuleVo updateAutoRuleVo : updateAutoRuleVoList) {
                AdvertiseAutoRuleStatus advertiseAutoRuleStatus = statusMap.get(updateAutoRuleVo.getStatusId());
                if (advertiseAutoRuleStatus == null) {
                    continue;
                }
                ShopAuth shopAuth = shopMap.get(advertiseAutoRuleStatus.getShopId());
                if (Objects.isNull(shopAuth)) {
                    continue;
                }

                //受控对象状态为启用
                if (AutoRuleEnableStatusEnum.ENABLED.getCode().equals(updateAutoRuleVo.getStatus())) {
                    //设置keywordText? 没搞懂为什么要set keywordText,但是targeting又没set
                    if (AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(advertiseAutoRuleStatus.getTargetType())) {
                        if (AutoRuleSubmitAdTypeEnum.SP.getCode().equals(advertiseAutoRuleStatus.getAdType())) {
                            AmazonAdKeyword amazonAdKeyword = amazonAdKeywordShardingDao.getByKeywordId(puid, advertiseAutoRuleStatus.getShopId(), advertiseAutoRuleStatus.getItemId());
                            if (amazonAdKeyword != null) {
                                advertiseAutoRuleStatus.setKeywordText(amazonAdKeyword.getKeywordText());
                                advertiseAutoRuleStatus.setCampaignId(amazonAdKeyword.getCampaignId());
                            }
                        } else if (AutoRuleSubmitAdTypeEnum.SB.getCode().equals(advertiseAutoRuleStatus.getAdType())) {
                            AmazonSbAdKeyword amazonAdKeyword = amazonSbAdKeywordDao.getByKeywordId(puid, advertiseAutoRuleStatus.getShopId(), advertiseAutoRuleStatus.getItemId());
                            if (amazonAdKeyword != null) {
                                advertiseAutoRuleStatus.setKeywordText(amazonAdKeyword.getKeywordText());
                                advertiseAutoRuleStatus.setCampaignId(amazonAdKeyword.getCampaignId());
                            }
                        }
                    }
                    if (AutoRuleTypeEnum.keywordCard.getValue().equals(advertiseAutoRuleStatus.getRuleType())) {
                        AmazonAdKeyword amazonAdKeyword = amazonAdKeywordShardingDao.getByKeywordId(puid, advertiseAutoRuleStatus.getShopId(), advertiseAutoRuleStatus.getItemId());
                        if (amazonAdKeyword != null) {
                            advertiseAutoRuleStatus.setKeywordText(amazonAdKeyword.getKeywordText());
                            advertiseAutoRuleStatus.setCampaignId(amazonAdKeyword.getCampaignId());
                        }
                    }
                    OriginValueVo originValueVo = new OriginValueVo();
                    originValueVo.setBiddingValue(updateAutoRuleVo.getBiddingValue());
                    originValueVo.setBudgetValue(updateAutoRuleVo.getBudgetValue());
                    originValueVo.setDefaultBiddingValue(updateAutoRuleVo.getDefaultBiddingValue());
                    originValueVo.setPlacementTopBidRatio(updateAutoRuleVo.getPlacementTopBidRatio());
                    // 抢排名涉及 竞价 和 竞价比例，改接口只传一个值，需要将没传的原值设置避免丢失
                    if (StringUtils.equalsIgnoreCase(advertiseAutoRuleStatus.getItemType(), AutoRuleItemTypeEnum.KEYWORD_TARGET.getName())) {
                        OriginValueVo oldOriginValue = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getOriginValue(),OriginValueVo.class);
                        if (Objects.isNull(updateAutoRuleVo.getBiddingValue())){
                            originValueVo.setBiddingValue(oldOriginValue.getBiddingValue());
                        }
                        if (Objects.isNull(updateAutoRuleVo.getPlacementTopBidRatio())){
                            originValueVo.setPlacementTopBidRatio(oldOriginValue.getPlacementTopBidRatio());
                        }
                    }
                    advertiseAutoRuleStatus.setOriginValue(JSONUtil.objectToJson(originValueVo));
                    advertiseAutoRuleStatus.setUpdateUid(updateId);
                    advertiseAutoRuleStatusDao.updateKeywordCardBid(templateId, advertiseAutoRuleStatus);
                    //推送至计算服务更新
                    aadrasApiFactory.getAadrasApi(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.valueOf(advertiseAutoRuleStatus.getItemType()))
                            .setAutoRuleTask(advertiseAutoRuleStatus.getTaskId(), advertiseAutoRuleStatus, shopAuth);
                } else if (AutoRuleEnableStatusEnum.DISABLED.getCode().equals(updateAutoRuleVo.getStatus())) {
                    //受控对象状态为禁用
                    OriginValueVo originValueVo = new OriginValueVo();
                    originValueVo.setBiddingValue(updateAutoRuleVo.getBiddingValue());
                    originValueVo.setBudgetValue(updateAutoRuleVo.getBudgetValue());
                    originValueVo.setDefaultBiddingValue(updateAutoRuleVo.getDefaultBiddingValue());
                    originValueVo.setPlacementTopBidRatio(updateAutoRuleVo.getPlacementTopBidRatio());
                    // 抢排名涉及 竞价 和 竞价比例，改接口只传一个值，需要将没传的原值设置避免丢失
                    if (StringUtils.equalsIgnoreCase(advertiseAutoRuleStatus.getItemType(), AutoRuleItemTypeEnum.KEYWORD_TARGET.getName())) {
                        OriginValueVo oldOriginValue = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getOriginValue(),OriginValueVo.class);
                        if (Objects.isNull(updateAutoRuleVo.getBiddingValue())){
                            originValueVo.setBiddingValue(oldOriginValue.getBiddingValue());
                        }
                        if (Objects.isNull(updateAutoRuleVo.getPlacementTopBidRatio())){
                            originValueVo.setPlacementTopBidRatio(oldOriginValue.getPlacementTopBidRatio());
                        }
                    }
                    advertiseAutoRuleStatus.setOriginValue(JSONUtil.objectToJson(originValueVo));
                    advertiseAutoRuleStatus.setUpdateUid(updateId);
                    advertiseAutoRuleStatusDao.updateKeywordCardBid(templateId, advertiseAutoRuleStatus);
                }
            }
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("分时调价任务修改原始竞价值异常:" + e);
            log.error("traceId:{} puid={} 分时调价任务修改异常", traceId, puid, e);
        }
        return result;
    }

    @Override
    public Result<String> updateAutoRuleStatus(Integer puid, Long statusId, String enableStatus, Integer updateId, String traceId, AdvertiseAutoRuleStatus autoRuleStatus) {
        Result<String> result = new Result<>();
        try {

            //调用AutoRuleTaskAllApi，异步更新的方法
            List<UpdateAutoRuleResponseVo> responseVoList = autoRuleTaskApi.updateAutoRuleStatus(puid, updateId,
                    autoRuleStatus.getTemplateId(),
                    Arrays.asList(statusId),
                    enableStatus, null, null, null);

            if (CollectionUtils.isNotEmpty(responseVoList)) {
                log.error("自动化规则修改状态异常: {}", responseVoList);
                throw new AutoRuleException("自动化规则修改状态异常");
            }

            result.setCode(Result.SUCCESS);
            result.setMsg("修改成功");
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("自动化规则修改状态异常:" + e);
            log.error("traceId:{} puid:{} statusId:{} 自动化规则修改状态异常", traceId, puid, statusId, e);
        }
        return result;
    }

    @Override
    public Result<String> removeAutoRule(Integer puid, List<RemoveAutoRuleVo> removeAutoRuleVoList, String traceId, List<Integer> authedShopIdList) {
        Result<String> result = new Result<>();
        try {

            if (CollectionUtils.isEmpty(removeAutoRuleVoList)) {
                result.setCode(Result.SUCCESS);
                result.setMsg("移除自动化规则成功");
                return result;
            }

            List<Long> statusIdList = new ArrayList<>(removeAutoRuleVoList.size());
            removeAutoRuleVoList.forEach(x -> statusIdList.add(x.getStatusId()));
            List<AdvertiseAutoRuleStatus> statusList = advertiseAutoRuleStatusDao.getByStatusIds(puid, statusIdList);
            //转map和收集shopId
            Map<Long, AdvertiseAutoRuleStatus> statusMap = new HashMap<>();
            Set<Integer> shopIdSet = new HashSet<>();
            statusList.forEach(x -> {
                statusMap.put(x.getId(), x);
                shopIdSet.add(x.getShopId());
            });

            List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, new ArrayList<>(shopIdSet));
            Map<Integer, ShopAuth> shopMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(shopAuthList)) {
                shopMap = shopAuthList.stream().filter(Objects::nonNull).collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
            }

            for (RemoveAutoRuleVo removeAutoRuleVo : removeAutoRuleVoList) {
                AdvertiseAutoRuleStatus status = statusMap.get(removeAutoRuleVo.getStatusId());
                if (Objects.isNull(status)) {
                    result.setCode(Result.ERROR);
                    result.setMsg("当前受控对象已移除");
                    return result;
                }

                if (CollectionUtils.isEmpty(authedShopIdList) || !authedShopIdList.contains(status.getShopId())) {
                    result.setCode(Result.ERROR);
                    result.setMsg("未授权");
                    return result;
                }

                ShopAuth shopAuth = shopMap.get(status.getShopId());
                if (Objects.isNull(shopAuth)) {
                    result.setCode(Result.ERROR);
                    result.setMsg("店铺信息不存在");
                    return result;
                }

                Integer count = autoRuleTaskDao.queryCountByTemplateId(puid, status.getTemplateId());
                if (count > 0) {
                    result.setCode(Result.ERROR);
                    result.setMsg("批量任务未完成，请稍后提交");
                    return result;
                }

                String itemType = status.getItemType();
                if (AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName().equals(status.getItemType())
                        && ChildrenItemType.CHILDREN_SEARCH_QUERY.name().equals(status.getChildrenItemType())) {
                    itemType = AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.SEARCH_QUERY.name();
                }

                //广独删除受控对象
                advertiseAutoRuleStatusDao.deleteAutoRuleStatus(puid, removeAutoRuleVo.getStatusId());

                //插入删除表
                advertiseAutoRuleStatusDeleteDao.insetStrategyStatus(puid, status);

                //如果是启用，需要删除aadras的受控对象
                if (AutoRuleEnableStatusEnum.ENABLED.getCode().equals(status.getStatus())) {
                    //推送至计算服务删除
                    aadrasApiFactory.getAadrasApi(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.valueOf(itemType))
                            .removeAutoRuleTask(status.getTaskId(), status.getTargetType(), "DELETE", shopAuth);
                }
            }
            result.setCode(Result.SUCCESS);
            result.setMsg("移除自动化规则成功");
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("自动化规则移除异常:" + e);
            log.error("traceId:{} puid{} 自动化规则移除异常:", traceId, puid, e);
        }
        return result;
    }

    @Override
    public void removeArchiveRecord(Integer puid, Integer shopId, Long taskId) {
        // 删除受控对象表
        advertiseAutoRuleStatusDao.deleteStrategyByTaskId(puid, shopId, taskId);
    }

    @Override
    public Result<Page<SimilarRulePageVo>> pageSimilarRuleList(SimilarRulePageParam param) {
        Result<Page<SimilarRulePageVo>> result = new Result<>();
        try {
            AdvertiseAutoRuleTemplate advertiseAutoRuleTemplate = advertiseAutoRuleTemplateDao.selectByPrimaryKey(param.getPuid(), param.getId());
            if (advertiseAutoRuleTemplate != null && StringUtils.isNotBlank(advertiseAutoRuleTemplate.getPerformOperation())) {
                PerformOperationJson performOperationJson = JSONUtil.jsonToArray(advertiseAutoRuleTemplate.getPerformOperation(), PerformOperationJson.class).get(0);
                param.setOperationType(AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction()));
            }
            //查询相似规则的受控对象
            Page<AdvertiseAutoRuleStatus> similarRulePage = advertiseAutoRuleStatusDao.pageSimilarRuleTemplateIdList(param);
            //查询相似规则名称
            List<AdvertiseAutoRuleStatus> ruleList = similarRulePage.getRows();
            if (CollectionUtils.isNotEmpty(ruleList)) {
                List<Long> templateIdList = ruleList.stream().map(AdvertiseAutoRuleStatus::getTemplateId).collect(Collectors.toList());
                List<SimilarRulePageVo> similarRulePageVoList = advertiseAutoRuleTemplateDao.listSimilarRuleVoByTemplateId(param.getPuid(), templateIdList);
                Page<SimilarRulePageVo> voPage = new Page<>(similarRulePage.getPageNo(), similarRulePage.getPageSize(),
                        similarRulePage.getTotalPage(), similarRulePage.getTotalSize());
                voPage.setRows(similarRulePageVoList);
                result.setData(voPage);
            }
            result.setCode(Result.SUCCESS);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("相似规则查询异常");
            log.error("puid:{}, itemId:{}, operationType:{}, itemType:{} 相似规则查询异常:", param.getPuid(), param.getItemId(), param.getOperationType(), param.getItemType(), e);
        }
        return result;
    }

    @Override
    public Result<Page<SimilarRuleVo>> pageAllSimilarRuleList(Integer puid, Integer shopId, Long templateId, List<String> itemIdList, List<Long> excludedStatusIdList, Integer pageNo, Integer pageSize) {
        Result<Page<SimilarRuleVo>> result = new Result<>();
        try {
            AdvertiseAutoRuleTemplate advertiseAutoRuleTemplate = advertiseAutoRuleTemplateDao.selectByPrimaryKey(puid, templateId);
            Integer operationType = 0;
            if (advertiseAutoRuleTemplate != null && StringUtils.isNotBlank(advertiseAutoRuleTemplate.getPerformOperation())) {
                PerformOperationJson performOperationJson = JSONUtil.jsonToArray(advertiseAutoRuleTemplate.getPerformOperation(), PerformOperationJson.class).get(0);
                operationType = AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction());
            }
            //查询相似规则的受控对象
            Page<SimilarRuleVo> page = new Page<>();
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            List<SimilarRuleVo> similarRuleVoList = Lists.newArrayList();
            if (CollectionUtils.isEmpty(itemIdList)) {
                itemIdList = advertiseAutoRuleStatusDao.getLisByItemIdList(puid, shopId, templateId, null, excludedStatusIdList);
            }
            List<AdvertiseAutoRuleStatus> autoRuleStatusList = advertiseAutoRuleStatusDao.allSimilarRuleList(puid, shopId, operationType, templateId, itemIdList, advertiseAutoRuleTemplate.getItemType());
            //查询相似规则名称
            if (CollectionUtils.isNotEmpty(autoRuleStatusList)) {
                Map<String, List<AdvertiseAutoRuleStatus>> statusMap = autoRuleStatusList.stream().collect(Collectors.groupingBy(AdvertiseAutoRuleStatus::getItemId));
                Map<Long, AdvertiseAutoRuleTemplate> advertiseAutoRuleTemplateMap = null;
                List<Long> templateIdList = autoRuleStatusList.stream().map(AdvertiseAutoRuleStatus::getTemplateId).distinct().collect(Collectors.toList());
                List<AdvertiseAutoRuleTemplate> templateList = advertiseAutoRuleTemplateDao.getListByLongIdList(puid, templateIdList);
                if (CollectionUtils.isNotEmpty(templateList)) {
                    advertiseAutoRuleTemplateMap = templateList.stream().collect(Collectors.
                            toMap(AdvertiseAutoRuleTemplate::getId, Function.identity(), (s1, s2) -> s1));
                }
                List<SimilarRuleVo> similarRuleVos = Lists.newArrayList();
                Map<Long, AdvertiseAutoRuleTemplate> finalAdvertiseAutoRuleTemplateMap = advertiseAutoRuleTemplateMap;
                statusMap.forEach((k, v) -> {
                    SimilarRuleVo similarRuleVo = new SimilarRuleVo();
                    List<TemplateRuleVo> templateRuleVoList = Lists.newArrayList();
                    similarRuleVo.setItemId(k);
                    v.forEach(e -> {
                        if (StringUtils.isNotBlank(e.getItemName())) {
                            similarRuleVo.setItemName(e.getItemName());
                        }
                        TemplateRuleVo templateRuleVo = new TemplateRuleVo();
                        if (MapUtils.isNotEmpty(finalAdvertiseAutoRuleTemplateMap) && finalAdvertiseAutoRuleTemplateMap.containsKey(e.getTemplateId())) {
                            templateRuleVo.setTemplateId(e.getTemplateId());
                            templateRuleVo.setTemplateName(finalAdvertiseAutoRuleTemplateMap.get(e.getTemplateId()).getTemplateName());
                            templateRuleVoList.add(templateRuleVo);
                        }
                    });
                    similarRuleVo.setTemplateRuleVoList(templateRuleVoList);
                    similarRuleVoList.add(similarRuleVo);
                });
            }
            if (CollectionUtils.isNotEmpty(similarRuleVoList)) {
                page = PageUtil.getPage(page, similarRuleVoList);
            }
            result.setData(page);
            result.setCode(Result.SUCCESS);
            result.setMsg("查询成功");
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("相似规则查询异常");
            log.error("puid:{}, templateId:{} 相似规则查询异常:", puid, templateId, e);
        }
        return result;
    }

    @Override
    public Result<String> queryGoalAdGroupCpc(Integer puid, Integer shopId, String marketplaceId, String adType, String adGroupId) {
        Result<String> result = new Result<>();
        String cpc = "0";
        try {
            //获取当前站点时区
            ZoneId zoneId = ZoneUtil.getZoneIdByAmzSite(marketplaceId);
            LocalDate startDate = LocalDateTimeUtil.getZoneDate(LocalDateTime.now().minusDays(6), TimeZone.getTimeZone("Asia/Shanghai").toZoneId(), zoneId);
            LocalDate endDate = LocalDateTimeUtil.getZoneDate(LocalDateTime.now(), TimeZone.getTimeZone("Asia/Shanghai").toZoneId(), zoneId);
            //获取时间段的报告数据汇总
            String startStr = LocalDateTimeUtil.formatDate(startDate, "yyyyMMdd");
            String endStr = LocalDateTimeUtil.formatDate(endDate, "yyyyMMdd");
            AmazonAdGroupReport amazonAdGroupReport = amazonAdGroupReportDao.getSumReportByAdGroupId(puid, shopId, marketplaceId, startStr, endStr, adGroupId);
            if (amazonAdGroupReport != null && amazonAdGroupReport.getCpc() != null) {
                cpc = amazonAdGroupReport.getCpc().toString();
            }
            if ("0".equals(cpc)) {
                AmazonAdSbGroupReport amazonAdSbGroupReport = amazonAdSbGroupReportDao.getSumReportByGroupId(puid, shopId, marketplaceId, startStr, endStr, adGroupId);
                if (amazonAdSbGroupReport != null) {
                    if (amazonAdSbGroupReport.getCost() == null || amazonAdSbGroupReport.getClicks() == null) {
                        cpc = "0";
                    } else {
                        cpc = calculationRateBigDecimal(amazonAdSbGroupReport.getCost(), BigDecimal.valueOf(amazonAdSbGroupReport.getClicks()), false).toString();
                    }
                }
            }
            result.setMsg("查询成功");
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("自动化规则cpc指标查询异常");
            log.error("puid:{}, adGroupId:{} 相似规则查询异常:", puid, adGroupId, e);
        }
        result.setData(cpc);
        return result;
    }

    @Override
    public void updateNextExecuteTime(int puid, int shopId, String itemType, List<UpdateNextExecuteTimeVo> nextExecuteTimeVoList) {

        if (StringUtils.equalsAny(itemType, AutoRuleScheduleTriggerRuleTaskItemType.CAMPAIGN.name(), AutoRuleScheduleTriggerRuleTaskItemType.AD_GROUP.name())) {
            advertiseAutoRuleStatusDao.batchUpdateNextExecuteTime(puid, shopId, itemType, nextExecuteTimeVoList);
        } else {
            advertiseAutoRuleStatusDao.batchUpdateNextExecuteTime(puid, shopId, AutoRuleItemTypeEnum.TARGET.getName(), nextExecuteTimeVoList);
        }

    }

    @Override
    public Result<CheckOperationVo> checkOperation4Grpc(int puid, List<Integer> shopIdList, long templateId, String ruleAction) {
        Result<CheckOperationVo> result = new Result<>();
        AdvertiseAutoRuleTemplate template = advertiseAutoRuleTemplateDao.selectByPrimaryKey(puid, templateId);
        if (Objects.isNull(template) || !shopIdList.contains(template.getShopId())) {
            throw new AutoRuleException("模板不存在");
        }

        //校验执行动作
        AutoRuleOperationTypeEnum operationType = AutoRuleOperationTypeEnum.map.get(ruleAction);
        if (Objects.isNull(operationType)) {
            throw new AutoRuleException("执行动作不存在");
        }

        //查询adType和targetType
        List<AutoRuleAdAndTargetTypeVo> voList = advertiseAutoRuleStatusDao.distinctAdAndTargetTypeByTemplateId(puid, templateId, null);
        Set<String> adTypeSet = new HashSet<>();
        Set<String> targetTypeSet = new HashSet<>();
        voList.forEach(x -> {
            adTypeSet.add(x.getAdType());
            targetTypeSet.add(x.getAdType());
        });
        AutoRuleItemTypeEnum itemTypeEnum = AutoRuleItemTypeEnum.map.get(template.getItemType());
        CheckRuleIndexAndActionStatusVo checkVo = checkOperationBase(itemTypeEnum, adTypeSet, targetTypeSet, ruleAction);

        CheckOperationVo.Builder builder = CheckOperationVo.newBuilder();
        builder.setCheckStatus(checkVo.getCheckStatus());
        if (StringUtils.isNotBlank(checkVo.getCheckMsg())) {
            builder.setCheckMsg(checkVo.getCheckMsg());
        }

        result.setCode(Result.SUCCESS);
        result.setMsg("");
        result.setData(builder.build());

        return result;
    }

    @Override
    public CheckRuleIndexAndActionStatusVo checkOperation4Template(AdvertiseAutoRuleTemplate template, String ruleAction) {
        //校验执行动作
        AutoRuleOperationTypeEnum operationType = AutoRuleOperationTypeEnum.map.get(ruleAction);
        if (Objects.isNull(operationType)) {
            throw new AutoRuleException("执行动作不存在");
        }

        //查询adType和targetType
        List<AutoRuleAdAndTargetTypeVo> voList = advertiseAutoRuleStatusDao.distinctAdAndTargetTypeByTemplateId(template.getPuid(), template.getId(), null);
        Set<String> adTypeSet = new HashSet<>();
        Set<String> targetTypeSet = new HashSet<>();
        voList.forEach(x -> {
            adTypeSet.add(x.getAdType());
            targetTypeSet.add(x.getTargetType());
        });
        AutoRuleItemTypeEnum itemTypeEnum = AutoRuleItemTypeEnum.map.get(template.getItemType());
        return checkOperationBase(itemTypeEnum, adTypeSet, targetTypeSet, ruleAction);
    }


    @Override
    public CheckRuleIndexAndActionStatusVo checkOperationBase(AutoRuleItemTypeEnum itemType,
                                                              Set<String> adTypeSet,
                                                              Set<String> targetTypeSet,
                                                              String ruleAction) {
        CheckRuleIndexAndActionStatusVo checkVo = new CheckRuleIndexAndActionStatusVo();

        if (Objects.isNull(itemType) || StringUtils.isBlank(ruleAction)) {
            return checkVo;
        }

        //广告活动，调整竞价策略和广告位竞价不支持SB、SD
        if (AutoRuleItemTypeEnum.CAMPAIGN.equals(itemType) && (StringUtils.equalsAny(ruleAction, AutoRuleOperationTypeEnum.editBidStrategy.getRuleAction(), AutoRuleOperationTypeEnum.editCampaignPlacement.getRuleAction()))) {
            if (CollectionUtils.isNotEmpty(adTypeSet) && CollectionUtils.containsAny(adTypeSet, AutoRuleSubmitAdTypeEnum.SB.getCode(), AutoRuleSubmitAdTypeEnum.SD.getCode())) {
                checkVo.setCheckStatus(false);
                if (AutoRuleOperationTypeEnum.editBidStrategy.getRuleAction().equals(ruleAction)) {
                    checkVo.setCheckMsg(CheckAutoRuleIndexAndOperationEnum.EDIT_BID_STRATEGY.getDesc());
                } else {
                    checkVo.setCheckMsg(CheckAutoRuleIndexAndOperationEnum.EDIT_CAMPAIGN_PLACEMENT.getDesc());
                }
                return checkVo;
            }
        }

        //广告组，调整默认竞价不支持SB
        if (AutoRuleItemTypeEnum.AD_GROUP.equals(itemType) && (StringUtils.equalsAny(ruleAction, AutoRuleOperationTypeEnum.defaultBidAdd.getRuleAction(), AutoRuleOperationTypeEnum.defaultBidReduce.getRuleAction()))) {
            if (CollectionUtils.isNotEmpty(adTypeSet) && adTypeSet.contains(AutoRuleSubmitAdTypeEnum.SB.getCode())) {
                checkVo.setCheckStatus(false);
                checkVo.setCheckMsg(CheckAutoRuleIndexAndOperationEnum.EDIT_DEFAULT_BID.getDesc());
                return checkVo;
            }
        }

        //投放，自动投放和受众投放不支持添加到否投
        if (AutoRuleItemTypeEnum.TARGET.equals(itemType) && AutoRuleOperationTypeEnum.addNotTarget.getRuleAction().equals(ruleAction)) {
            if (CollectionUtils.isNotEmpty(targetTypeSet) && CollectionUtils.containsAny(targetTypeSet, AutoRuleTargetTypeEnum.autoTarget.getTargetType(), AutoRuleTargetTypeEnum.audienceTarget.getTargetType())) {
                checkVo.setCheckStatus(false);
                if (targetTypeSet.contains(AutoRuleTargetTypeEnum.autoTarget.getTargetType()) && targetTypeSet.contains(AutoRuleTargetTypeEnum.audienceTarget.getTargetType())) {
                    checkVo.setCheckMsg(CheckAutoRuleIndexAndOperationEnum.ADD_NOT_TARGET_ALL.getDesc());
                } else if (targetTypeSet.contains(AutoRuleTargetTypeEnum.autoTarget.getTargetType())) {
                    checkVo.setCheckMsg(CheckAutoRuleIndexAndOperationEnum.ADD_NOT_TARGET_AUTO.getDesc());
                } else if (targetTypeSet.contains(AutoRuleTargetTypeEnum.audienceTarget.getTargetType())) {
                    checkVo.setCheckMsg(CheckAutoRuleIndexAndOperationEnum.ADD_NOT_TARGET_AUDIENCE.getDesc());
                }
                return checkVo;
            }
        }

        return checkVo;
    }

    @Override
    public CheckRuleIndexAndActionStatusVo checkRuleIndexBase(List<String> ruleIndexList, Set<String> adTypeSet) {
        CheckRuleIndexAndActionStatusVo vo = new CheckRuleIndexAndActionStatusVo();
        vo.setCheckStatus(true);
        if (CollectionUtils.isNotEmpty(ruleIndexList) && ruleIndexList.contains(AutoRuleIndexEnum.orderNum.getCode())) {
            if (CollectionUtils.isNotEmpty(adTypeSet) && adTypeSet.contains(AutoRuleSubmitAdTypeEnum.SB.getCode())) {
                vo.setCheckStatus(false);
                vo.setCheckMsg(CheckAutoRuleIndexAndOperationEnum.RULEINDEX_ADORDERNUM.getDesc());
            }
        }
        return vo;
    }

    @Override
    public Page<AdAutoRuleStatusVo> pageAutoRuleStatus(PageAutoRuleStatusParam param) {
        // 参数校验
        checkParam(param);
        // 分页获取数据
        Page<AdvertiseAutoRuleStatus> page = advertiseAutoRuleStatusDao.pageAutoRuleStatus(param);
        if (CollectionUtils.isEmpty(page.getRows())) {
            return new Page<>(param.getPageNo(), param.getPageSize());
        }
        // 组装响应参数
        return buildStatusPageVo(param, page);
    }

    @Override
    public AdAutoRuleStatusVo getStatusByStatusId(int puid, Long statusId) {
        // 获取受控对象记录
        AdvertiseAutoRuleStatus status = advertiseAutoRuleStatusDao.getByStatusId(puid, statusId);
        if(status == null){
            throw new SponsoredBizException("自动化规则不存在！");
        }
        // 获取模板记录
        AdvertiseAutoRuleTemplate template = advertiseAutoRuleTemplateDao.selectByPrimaryKey(puid, status.getTemplateId());
        // 构建基础信息
        AdAutoRuleStatusVo vo = buildStatusVo(status, template);
        // 获取店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(status.getShopId());
        vo.setShopName(shopAuth.getName());
        return vo;
    }

    @Override
    public Boolean editAutoRuleStatus(EditAutoRuleStatusParam param) {
        // 参数校验
        checkParam(param);
        // 获取受控对象
        AdvertiseAutoRuleStatus status = advertiseAutoRuleStatusDao.getByStatusId(param.getPuid(), param.getStatusId());
        if(status == null){
            throw new SponsoredBizException("自动化规则不存在！");
        }
        // 校验规则、执行动作
        checkRuleOperation(status, param);
        // 封装模板数据
        AdvertiseAutoRuleTemplate template = buildTemplate(param);
        // 封装自动化规则数据
        UpdateAutoRuleVo vo = new UpdateAutoRuleVo();
        vo.setStatusId(param.getStatusId());
        vo.setStatus(status.getStatus());
        // 更新受控对象
        List<UpdateAutoRuleResponseVo> responseVoList = autoRuleTaskAllApi.updateAutoRule(param.getPuid(), param.getUid(), param.getTemplateId(), CollectionUtil.newArrayList(vo), template, null);
        if (CollectionUtils.isNotEmpty(responseVoList)) {
            log.info("编辑受控对象失败，原因：{}", responseVoList.get(0).getMsg());
            throw new SponsoredBizException(responseVoList.get(0).getMsg());
        }
        return Boolean.TRUE;
    }

    /**
     * 校验规则、执行动作
     */
    private void checkRuleOperation(AdvertiseAutoRuleStatus status, EditAutoRuleStatusParam param) {
        // 规则列表
        List<String> ruleIndexList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(param.getRuleList())) {
            ruleIndexList = param.getRuleList().stream().map(AutoRuleVo::getRuleIndex).collect(Collectors.toList());
        }
        // 1.广告类型和规则指标校验
        CheckRuleIndexAndActionStatusVo ruleCheckResult = checkRuleIndexBase(ruleIndexList, CollectionUtil.newHashSet(status.getAdType()));
        if (!ruleCheckResult.getCheckStatus()) {
            throw new SponsoredBizException(ruleCheckResult.getCheckMsg());
        }
        // 2.执行动作与广告类型校验
        Set<String> targetTypeSet = new HashSet<>();
        if (StringUtils.isNotEmpty(status.getTargetType())) {
            targetTypeSet.add(status.getTargetType());
        }
        // 执行动作
        String ruleAction = null;
        if (CollectionUtils.isNotEmpty(param.getPerformOperationList())) {
            ruleAction = param.getPerformOperationList().get(0).getRuleAction();
        }
        AutoRuleItemTypeEnum itemTypeEnum = AutoRuleItemTypeEnum.map.get(status.getItemType());
        CheckRuleIndexAndActionStatusVo operationCheckResult = checkOperationBase(itemTypeEnum, CollectionUtil.newHashSet(status.getAdType()), targetTypeSet, ruleAction);
        if (!operationCheckResult.getCheckStatus()) {
            throw new SponsoredBizException(operationCheckResult.getCheckMsg());
        }
        // 3.执行动作与投放类型校验
        CheckRuleIndexAndActionStatusVo checkResult = checkTargetType(param.getPuid(), status.getItemId(), status.getShopId(), itemTypeEnum, status.getAdType(), status.getTargetType(), ruleAction);
        if (!checkResult.getCheckStatus()) {
            throw new SponsoredBizException(checkResult.getCheckMsg());
        }
    }

    /**
     * 构建模板数据
     */
    private static AdvertiseAutoRuleTemplate buildTemplate(EditAutoRuleStatusParam param) {
        AdvertiseAutoRuleTemplate template = BeanUtil.copyProperties(param, AdvertiseAutoRuleTemplate.class);
        template.setId(param.getTemplateId());
        template.setVersion(-1);// 版本号设置为-1区分模板版本
        List<TimeRuleJson> timeRuleJsons = buildTimeRule(param.getTimeRules(), param.getChooseTimeType());
        if (StringUtils.isNotEmpty(param.getStartDate())) {
            template.setStartDate(LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(com.meiyunji.sponsored.common.util.DateUtil.PATTERN)));
        }
        if (StringUtils.isNotEmpty(param.getEndDate())) {
            template.setEndDate(LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(com.meiyunji.sponsored.common.util.DateUtil.PATTERN)));
        }
        if (CollectionUtils.isNotEmpty(timeRuleJsons)) {
            template.setTimeRule(JSONUtil.objectToJson(timeRuleJsons));
        }
        if (CollectionUtils.isNotEmpty(param.getRuleList())) {
            template.setRule(JSONUtil.objectToJson(buildRule(param.getRuleList(), param.getRuleType())));
        }
        if (CollectionUtils.isNotEmpty(param.getPerformOperationList())) {
            template.setPerformOperation(JSONUtil.objectToJson(buildOperation(param.getPerformOperationList())));
        }
        if (param.getCallbackOperate() != null) {
            CallbackOperateJson operateJson = BeanUtil.copyProperties(param.getCallbackOperate(), CallbackOperateJson.class);
            if (!param.getCallbackState()) {
                operateJson.setAdjustType("adjustFixed");
            }
            operateJson.setAdJustValue(param.getCallbackOperate().getAdjustValue());
            template.setCallbackOperate(JSONUtil.objectToJson(operateJson));
        }
        if (param.getDesiredPosition() != null) {
            DesiredPositionJson desiredPositionJson = BeanUtil.copyProperties(param.getDesiredPosition(), DesiredPositionJson.class);
            template.setDesiredPosition(JSONUtil.objectToJson(desiredPositionJson));
        }
        if (CollectionUtils.isNotEmpty(param.getAdDataRules())) {
            template.setAdDataRule(JSONUtil.objectToJson(buildAdDataRule(param.getAdDataRules())));
        }
        if (param.getAdDataOperate() != null) {
            AdDataOperateJson operateJson = BeanUtil.copyProperties(param.getAdDataOperate(), AdDataOperateJson.class);
            operateJson.setAdJustValue(param.getAdDataOperate().getAdjustValue());
            if (param.getAdDataOperate().getBid() != null) {
                operateJson.getBid().setAdJustValue(param.getAdDataOperate().getBid().getAdJustValue());
            }
            if (param.getAdDataOperate().getPlacementTopBidRatio()!= null) {
                operateJson.getPlacementTopBidRatio().setAdJustValue(param.getAdDataOperate().getPlacementTopBidRatio().getAdJustValue());
            }
            template.setAdDataOperate(JSONUtil.objectToJson(operateJson));
        }
        if (param.getAutoPriceRule() != null) {
            AutoPriceRuleJson ruleJson = BeanUtil.copyProperties(param.getAutoPriceRule(), AutoPriceRuleJson.class);
            template.setAutoPriceRule(JSONUtil.objectToJson(ruleJson));
        }
        if (param.getAutoPriceOperate() != null) {
            AutoPriceOperateJson operateJson = BeanUtil.copyProperties(param.getAutoPriceOperate(), AutoPriceOperateJson.class);
            operateJson.setAdJustValue(param.getAutoPriceOperate().getAdjustValue());
            if (param.getAdDataOperate().getBid() != null) {
                operateJson.getBid().setAdJustValue(param.getAdDataOperate().getBid().getAdJustValue());
            }
            if (param.getAdDataOperate().getPlacementTopBidRatio()!= null) {
                operateJson.getPlacementTopBidRatio().setAdJustValue(param.getAdDataOperate().getPlacementTopBidRatio().getAdJustValue());
            }
            template.setAutoPriceOperate(JSONUtil.objectToJson(operateJson));
        }
        if (param.getBiddingCallbackOperate() != null) {
            BiddingCallbackOperateJson operateJson = BeanUtil.copyProperties(param.getBiddingCallbackOperate(), BiddingCallbackOperateJson.class);
            operateJson.setAdJustValue(param.getBiddingCallbackOperate().getAdjustValue());
            if (param.getAdDataOperate().getBid() != null) {
                operateJson.getBid().setAdJustValue(param.getAdDataOperate().getBid().getAdJustValue());
            }
            if (param.getAdDataOperate().getPlacementTopBidRatio()!= null) {
                operateJson.getPlacementTopBidRatio().setAdJustValue(param.getAdDataOperate().getPlacementTopBidRatio().getAdJustValue());
            }
            template.setBiddingCallbackOperate(JSONUtil.objectToJson(operateJson));
        }
        return template;
    }

    private static List<AdDataRuleJson> buildAdDataRule(List<AdDataRuleVo> adDataRules) {
        List<AdDataRuleJson> adDataRuleJsons = new ArrayList<>();
        for (AdDataRuleVo vo : adDataRules) {
            AdDataRuleJson adDataRuleJson = BeanUtil.copyProperties(vo, AdDataRuleJson.class);
            adDataRuleJson.setRuleOperator(vo.getRuleOperatorType());
            String[] days = vo.getDay().split("-");
            if (days.length > 1) {
                adDataRuleJson.setDay(Integer.valueOf(days[0]));
                adDataRuleJson.setExcludeDay(Integer.valueOf(days[1]));
            }
            adDataRuleJsons.add(adDataRuleJson);
        }
        return adDataRuleJsons;
    }

    private static List<TimeRuleJson> buildTimeRule(List<TimeRuleVo> timeRules, String chooseTimeType) {
        if ("all".equals(chooseTimeType)) {
            List<TimeRuleJson> timeRuleJsons = new ArrayList<>();
            for (int i = 1; i <= 7; i++) {
                TimeRuleJson timeRuleJson = new TimeRuleJson();
                timeRuleJson.setSiteDate(i);
                timeRuleJson.setStartTimeSite(0);
                timeRuleJson.setEndTimeSite(24);
                timeRuleJsons.add(timeRuleJson);
            }
            return timeRuleJsons;
        } else if (CollectionUtils.isNotEmpty(timeRules)) {
            List<TimeRuleJson> timeRuleJsons = new ArrayList<>();
            for (TimeRuleVo timeRule : timeRules) {
                for (TimeVo time : timeRule.getTimes()) {
                    TimeRuleJson timeRuleJson = BeanUtil.copyProperties(time, TimeRuleJson.class);
                    timeRuleJson.setSiteDate(timeRule.getSiteDate());
                    timeRuleJsons.add(timeRuleJson);
                }
            }
            return timeRuleJsons;
        }
        return null;
    }

    private static List<PerformOperationJson> buildOperation(List<PerformOperationVo> performOperationList) {
        List<PerformOperationJson> operationJsons = new ArrayList<>();
        for (PerformOperationVo e : performOperationList) {
            PerformOperationJson performOperationJson = new PerformOperationJson();
            performOperationJson.setRuleAction(e.getRuleAction());
            performOperationJson.setRuleAdjust(e.getRuleAdjust());
            performOperationJson.setAdJustValue(e.getAdjustValue());
            performOperationJson.setLimitValue(e.getLimitValue());
            if (CollectionUtils.isNotEmpty(e.getExpressionTypes())) {
                performOperationJson.setExpressionType(StringUtils.join(e.getExpressionTypes(), ","));
            }
            if (StringUtils.isNotBlank(e.getNegativeType())) {
                performOperationJson.setNegativeType(e.getNegativeType());
            }
            if (StringUtils.isNotBlank(e.getBaseValueType())) {
                performOperationJson.setBaseValueType(e.getBaseValueType());
            }
            if (StringUtils.isNotBlank(e.getAdPlaceTopValue())) {
                performOperationJson.setAdPlaceTopValue(e.getAdPlaceTopValue());
            }
            if (StringUtils.isNotBlank(e.getAdPlaceProductValue())) {
                performOperationJson.setAdPlaceProductValue(e.getAdPlaceProductValue());
            }
            if (StringUtils.isNotBlank(e.getAdOtherValue())) {
                performOperationJson.setAdOtherValue(e.getAdOtherValue());
            }
            if (StringUtils.isNotBlank(e.getAppointAdGroupType())) {
                performOperationJson.setAppointAdGroupType(e.getAppointAdGroupType());
            }
            if (StringUtils.isNotBlank(e.getPortfolioId())) {
                performOperationJson.setPortfolioId(e.getPortfolioId());
            }
            if (StringUtils.isNotBlank(e.getCampaignId())) {
                performOperationJson.setCampaignId(e.getCampaignId());
            }
            if (StringUtils.isNotBlank(e.getAdGroupId())) {
                performOperationJson.setAdGroupId(e.getAdGroupId());
            }
            if (StringUtils.isNotBlank(e.getBidType())) {
                performOperationJson.setBidType(e.getBidType());
            }
            if (StringUtils.isNotBlank(e.getBidValue())) {
                performOperationJson.setBidValue(e.getBidValue());
            }
            if (StringUtils.isNotBlank(e.getMatchType())) {
                performOperationJson.setMatchType(e.getMatchType());
            }
            operationJsons.add(performOperationJson);
        }
        return operationJsons;
    }

    private static List<AutoRuleJson> buildRule(List<AutoRuleVo> autoRuleVoList, String ruleType) {
        List<AutoRuleJson> ruleJsonList = new ArrayList<>();
        for (AutoRuleVo vo : autoRuleVoList) {
            AutoRuleJson ruleJson = BeanUtil.copyProperties(vo, AutoRuleJson.class);
            if (!AutoRuleTypeEnum.customRule.getValue().equals(ruleType)) {
                String[] days = vo.getDay().split("-");
                if (days.length > 1) {
                    ruleJson.setDay(Integer.valueOf(days[0]));
                    ruleJson.setExcludeDay(Integer.valueOf(days[1]));
                }
            }
            ruleJson.setRuleOperator(vo.getRuleOperatorType());
            ruleJsonList.add(ruleJson);
        }
        return ruleJsonList;
    }

    /**
     * 参数校验
     */
    private void checkParam(EditAutoRuleStatusParam param) {
        if (StringUtils.isBlank(param.getExecuteType())) {
            param.setExecuteType("manual");
        }
        if (StringUtils.isBlank(param.getItemType())) {
            throw new SponsoredBizException("受控对象类型为空");
        }
        if (param.getTemplateId() == null) {
            throw new SponsoredBizException("模板id为空");
        }
        if (param.getStatusId() == null) {
            throw new SponsoredBizException("受控对象主键id为空");
        }
        if (StringUtils.isNotBlank(param.getEndDate())) {
            LocalDate endDate = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDate startDate = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            if (endDate.isBefore(startDate)) {
                throw new SponsoredBizException("结束时间不能小于开始时间");
            }
        }
        if (StringUtils.isNotBlank(param.getCheckFrequency()) && !"1".equals(param.getCheckFrequency())
                && !"2".equals(param.getCheckFrequency()) && !"0.5".equals(param.getCheckFrequency())) {
            throw new SponsoredBizException("检查频率参数设置违法");
        }
        if ("select".equals(param.getChooseTimeType())) {
            if (CollectionUtils.isEmpty(param.getTimeRules())) {
                throw new SponsoredBizException("请选择指定时间段");
            }
        }
//        // 自动化规则校验
//        if (CollectionUtils.isNotEmpty(param.getRuleList())) {
//            Map<String, List<AutoRuleVo>> autoRuleMap = param.getRuleList().stream().collect(Collectors.groupingBy(AutoRuleVo::getRuleIndex));
//            for (String ruleIndex : autoRuleMap.keySet()) {
//                List<String> list = autoRuleMap.get(ruleIndex).stream().map(AutoRuleVo::getRuleOperatorType).collect(Collectors.toList());
//                List<String> list1 = list.stream().distinct().collect(Collectors.toList());
//                if (list1.size() < list.size()) {
//                    throw new SponsoredBizException("一个规则内,一个指标+一个运算符只能使用一次");
//                }
//                BigDecimal maxValue = null;
//                BigDecimal minValue = null;
//                for (AutoRuleVo autoRuleVo : autoRuleMap.get(ruleIndex)) {
//                    if ("GE".equals(autoRuleVo.getRuleOperatorType()) || "GT".equals(autoRuleVo.getRuleOperatorType()))
//                        minValue = new BigDecimal(autoRuleVo.getRuleValue());
//                    if ("LE".equals(autoRuleVo.getRuleOperatorType()) || "LT".equals(autoRuleVo.getRuleOperatorType()))
//                        maxValue = new BigDecimal(autoRuleVo.getRuleValue());
//                }
//                if (maxValue != null && minValue != null) {
//                    if (maxValue.compareTo(minValue) < 1) {
//                        throw new SponsoredBizException("规则条件输入值不合理，请检查输入值");
//                    }
//                }
//            }
//        }
//        // 抢排名规则校验
//        if (CollectionUtils.isNotEmpty(param.getAdDataRules())) {
//            Map<String, List<AdDataRuleVo>> adDataRuleMap = param.getAdDataRules().stream().collect(Collectors.groupingBy(AdDataRuleVo::getRuleIndex));
//            for (String ruleIndex : adDataRuleMap.keySet()) {
//                List<String> list = adDataRuleMap.get(ruleIndex).stream().map(AdDataRuleVo::getRuleOperatorType).collect(Collectors.toList());
//                List<String> list1 = list.stream().distinct().collect(Collectors.toList());
//                if (list1.size() < list.size()) {
//                    throw new SponsoredBizException("一个规则内,一个指标+一个运算符只能使用一次");
//                }
//                BigDecimal maxValue = null;
//                BigDecimal minValue = null;
//                for (AdDataRuleVo adDataRuleVo : adDataRuleMap.get(ruleIndex)) {
//                    if ("GE".equals(adDataRuleVo.getRuleOperatorType()))
//                        minValue = new BigDecimal(adDataRuleVo.getRuleValue());
//                    if ("LE".equals(adDataRuleVo.getRuleOperatorType()))
//                        maxValue = new BigDecimal(adDataRuleVo.getRuleValue());
//                }
//                if (maxValue != null && minValue != null) {
//                    if (maxValue.compareTo(minValue) < 1) {
//                        throw new SponsoredBizException("规则条件输入值不合理，请检查输入值");
//                    }
//                }
//            }
//        }
    }

    @Override
    public List<CheckItemTemplateVo> checkItemTemplate(CheckItemTemplateParam param) {
        // 参数校验
        checkParam(param);
        // 获取已存在的受控对象
        List<String> itemIdList = StreamUtil.toList(param.getItemList(), CheckItemTemplateParam.ItemParam::getItemId);
        List<String> existItemIdList = advertiseAutoRuleStatusDao.getExistItemId(param.getPuid(), itemIdList, param.getTemplateId());
        // 获取模板数据
        AdvertiseAutoRuleTemplate template = advertiseAutoRuleTemplateDao.selectByPrimaryKey(param.getPuid(), param.getTemplateId());
        // 受控类型枚举
        AutoRuleItemTypeEnum itemTypeEnum = AutoRuleItemTypeEnum.map.get(param.getItemType());
        // 执行动作
        String ruleAction = null;
        List<PerformOperationJson> jsonList = JSONUtil.jsonToArray(template.getPerformOperation(), PerformOperationJson.class);
        if (CollectionUtils.isNotEmpty(jsonList)) {
            ruleAction = jsonList.get(0).getRuleAction();
        }
        // 规则列表
        List<String> ruleIndexList = new ArrayList<>();
        List<AutoRuleJson> ruleJsonList = JSONUtil.jsonToArray(template.getRule(), AutoRuleJson.class);
        if (CollectionUtils.isNotEmpty(ruleJsonList)) {
            ruleIndexList = ruleJsonList.stream().map(AutoRuleJson::getRuleIndex).collect(Collectors.toList());
        }
        // 校验并构建响应参数
        return buildCheckVo(param, ruleIndexList, itemTypeEnum, ruleAction, existItemIdList);
    }

    /**
     * 构建响应参数
     */
    private List<CheckItemTemplateVo> buildCheckVo(CheckItemTemplateParam param, List<String> ruleIndexList, AutoRuleItemTypeEnum itemTypeEnum, String ruleAction, List<String> existItemIdList) {
        List<CheckItemTemplateVo> voList = new ArrayList<>();
        for (CheckItemTemplateParam.ItemParam item : param.getItemList()) {
            List<String> errorMsgList = new ArrayList<>();
            // 1.广告类型和规则指标校验
            CheckRuleIndexAndActionStatusVo ruleCheckResult = checkRuleIndexBase(ruleIndexList, CollectionUtil.newHashSet(item.getAdType()));
            if (!ruleCheckResult.getCheckStatus()) {
                errorMsgList.add(ruleCheckResult.getCheckMsg());
            }
            // 2.执行动作与广告类型校验
            Set<String> targetTypeSet = new HashSet<>();
            if (StringUtils.isNotEmpty(item.getTargetType())) {
                targetTypeSet.add(item.getTargetType());
            }
            CheckRuleIndexAndActionStatusVo operationCheckResult = checkOperationBase(itemTypeEnum, CollectionUtil.newHashSet(item.getAdType()), targetTypeSet, ruleAction);
            if (!operationCheckResult.getCheckStatus()) {
                errorMsgList.add(operationCheckResult.getCheckMsg());
            }
            // 3.规则内是否已存在该广告活动
            if (existItemIdList.contains(item.getItemId())) {
                errorMsgList.add("规则内已有该" + itemTypeEnum.getDesc());
            }
            // 4.执行动作与投放类型校验
            CheckRuleIndexAndActionStatusVo checkResult = checkTargetType(param.getPuid(), item.getItemId(), item.getShopId(), itemTypeEnum, item.getAdType(), item.getTargetType(), ruleAction);
            if (!checkResult.getCheckStatus()) {
                errorMsgList.add(checkResult.getCheckMsg());
            }
            if (CollectionUtil.isNotEmpty(errorMsgList)) {
                CheckItemTemplateVo vo = new CheckItemTemplateVo();
                vo.setItemId(item.getItemId());
                vo.setItemName(item.getItemName());
                vo.setErrorMsgList(errorMsgList);
                voList.add(vo);
            }
        }
        return voList;
    }

    public CheckRuleIndexAndActionStatusVo checkTargetType(Integer puid, String itemId, Integer shopId, AutoRuleItemTypeEnum itemType, String adType, String targetType, String ruleAction){
        CheckRuleIndexAndActionStatusVo checkVo = new CheckRuleIndexAndActionStatusVo();
        // 商品投放 类目定位不能添加到否定投放
        if (AutoRuleItemTypeEnum.TARGET.equals(itemType) && AutoRuleOperationTypeEnum.addNotTarget.getRuleAction().equals(ruleAction)
            && AutoRuleTargetTypeEnum.productTarget.getTargetType().equals(targetType)){
            // 根据不同类型获取投放详情
            if (AutoRuleSubmitAdTypeEnum.SP.getCode().equals(adType)) {
                AmazonAdTargeting targeting = amazonAdTargetingShardingDao.getByAdTargetId(puid, shopId, itemId);
                if(!SdTargetTypeEnum.asin.getValue().equals(targeting.getType())){
                    checkVo.setCheckStatus(false);
                    checkVo.setCheckMsg(CheckAutoRuleIndexAndOperationEnum.ADD_NOT_TARGET_PRODUCT.getDesc());
                }
            }else if (AutoRuleSubmitAdTypeEnum.SB.getCode().equals(adType)) {
                AmazonSbAdTargeting targeting = amazonSbAdTargetingDao.getByTargetId(puid, shopId, itemId);
                if(!SdTargetTypeEnum.asin.getValue().equals(targeting.getType())){
                    checkVo.setCheckStatus(false);
                    checkVo.setCheckMsg(CheckAutoRuleIndexAndOperationEnum.ADD_NOT_TARGET_PRODUCT.getDesc());
                }
            }else{
                AmazonSdAdTargeting targeting = amazonSdAdTargetingDao.getbyTargetId(puid, shopId, itemId);
                if(!SdTargetTypeEnum.asin.getValue().equals(targeting.getType())){
                    checkVo.setCheckStatus(false);
                    checkVo.setCheckMsg(CheckAutoRuleIndexAndOperationEnum.ADD_NOT_TARGET_PRODUCT.getDesc());
                }
            }
        }
        return checkVo;
    }

    private static void checkParam(CheckItemTemplateParam param) {
        if (CollectionUtils.isEmpty(param.getItemList())) {
            throw new SponsoredBizException("受控对象集合不能为空");
        }
        if (StringUtils.isEmpty(param.getItemType())) {
            throw new SponsoredBizException("受控对象类型不能为空");
        }
        if (param.getTemplateId() == null) {
            throw new SponsoredBizException("模版id不能为空");
        }
        for (CheckItemTemplateParam.ItemParam item : param.getItemList()) {
            if (StringUtils.isEmpty(item.getItemId())) {
                throw new SponsoredBizException("受控对象id不能为空");
            }
            if (StringUtils.isEmpty(item.getItemName())) {
                throw new SponsoredBizException("受控对象名称不能为空");
            }
            if (StringUtils.isEmpty(item.getAdType())) {
                throw new SponsoredBizException("受控对象广告类型不能为空");
            }else{
                // 前端传过来的是小写需要转换成大写
                item.setAdType(item.getAdType().toUpperCase(Locale.ROOT));
            }
            if (StringUtils.isEmpty(item.getTargetType()) && AutoRuleItemTypeEnum.TARGET.getName().equals(param.getItemType())) {
                throw new SponsoredBizException("受控对象投放类型不能为空");
            }
        }
    }

    /**
     * 消息推送类型
     */
    private static List<String> getPushMessageTypeList(String pushMessageType) {
        List<String> pushMessageTypeList = new ArrayList<>();
        // 消息推送类型
        if (PushMessageTypeEnum.ALL.getCode().equals(pushMessageType)) {
            pushMessageTypeList.add(PushMessageTypeEnum.WX.getCode());
            pushMessageTypeList.add(PushMessageTypeEnum.INSTATION.getCode());
        } else if (PushMessageTypeEnum.WX.getCode().equals(pushMessageType)) {
            pushMessageTypeList.add(PushMessageTypeEnum.WX.getCode());
        } else if (PushMessageTypeEnum.INSTATION.getCode().equals(pushMessageType)) {
            pushMessageTypeList.add(PushMessageTypeEnum.INSTATION.getCode());
        }
        return pushMessageTypeList;
    }

    /**
     * 组装响应参数
     */
    private Page<AdAutoRuleStatusVo> buildStatusPageVo(PageAutoRuleStatusParam param, Page<AdvertiseAutoRuleStatus> page) {
        // 获取优化次数
        List<Long> statusIdList = StreamUtil.toList(page.getRows(), AdvertiseAutoRuleStatus::getId);
        List<QueryOptimizedTimesVo> queryOptimizedTimesVos = advertiseAutoRuleExecuteRecordDao.queryOptimizedTimesByTaskIdList(param.getPuid(), statusIdList);
        Map<Long, Integer> optimizedTimesMap = StreamUtil.toMap(queryOptimizedTimesVos, QueryOptimizedTimesVo::getId, QueryOptimizedTimesVo::getQueryOptimizedTimes);
        // 获取模版名称
        List<Long> templateIdList = StreamUtil.toList(page.getRows(), AdvertiseAutoRuleStatus::getTemplateId);
        List<AdvertiseAutoRuleTemplate> templateList = advertiseAutoRuleTemplateDao.listByIdList(param.getPuid(), templateIdList);
        Map<Long, AdvertiseAutoRuleTemplate> templateMap = StreamUtil.toMap(templateList, AdvertiseAutoRuleTemplate::getId);
        // 获取更新人信息
        List<Integer> userIdList = StreamUtil.toList(page.getRows(), AdvertiseAutoRuleStatus::getUpdateUid);
        userIdList.addAll(StreamUtil.toList(page.getRows(), AdvertiseAutoRuleStatus::getCreateUid));
        List<User> users = userDao.listByIds(param.getPuid(), userIdList);
        Map<Integer, String> userMap = StreamUtil.toMap(users, User::getId, User::getNickname);
        List<AdAutoRuleStatusVo> voList = new ArrayList<>();
        for (AdvertiseAutoRuleStatus status : page.getRows()) {
            AdvertiseAutoRuleTemplate template = templateMap.get(status.getTemplateId());
            // 构建基础信息
            AdAutoRuleStatusVo vo = buildStatusVo(status, template);
            vo.setOptimizedTimes(optimizedTimesMap.get(status.getId()) == null ? 0 : optimizedTimesMap.get(status.getId()));
            if (status.getCreateUid() != null) {
                vo.setCreateName(userMap.get(status.getCreateUid()));
            }
            if (status.getUpdateUid() != null) {
                vo.setUpdateName(userMap.get(status.getUpdateUid()));
            }
            voList.add(vo);
        }
        return new Page<>(param.getPageNo(), param.getPageSize(), page.getTotalPage(), page.getTotalSize(), voList);
    }

    /**
     * 构建基础信息
     */
    private static AdAutoRuleStatusVo buildStatusVo(AdvertiseAutoRuleStatus status, AdvertiseAutoRuleTemplate template) {
        AdAutoRuleStatusVo vo = BeanUtil.copyProperties(status, AdAutoRuleStatusVo.class);
        vo.setStatusId(status.getId());
        vo.setLastUpdateAt(DateUtil.format(status.getUpdateTime(), DatePattern.NORM_DATETIME_PATTERN));
        vo.setMarketplaceName(AmznEndpoint.getByMarketplaceId(vo.getMarketplaceId()).getMarketplaceCN());
        if (status.getStartDate() != null) {
            vo.setStartDate(status.getStartDate().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
        }
        if (status.getEndDate() != null) {
            vo.setEndDate(status.getEndDate().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
        }
        // 模板名称
        vo.setTemplateName(template.getTemplateName());
        // 版本号对比
        if (status.getVersion() >= template.getVersion()) {
            vo.setUpdateStatus(AutoRuleUpdateStatusEnum.UPDATE.getCode());
        } else {
            vo.setUpdateStatus(AutoRuleUpdateStatusEnum.NOT_UPDATE.getCode());
        }
        // 消息推送类型
        vo.setMessageReminderType(template.getMessageReminderType());
        // 特殊处理 如果模板需要通知 则根据执行方式赋值不同的通知类型
        if("advise".equals(template.getMessageReminderType()) || "executeFailAdvise".equals(template.getMessageReminderType())){
            if("auto".equals(vo.getExecuteType())){
                vo.setMessageReminderType("executeFailAdvise");
            }else{
                vo.setMessageReminderType("advise");
            }
        }
        vo.setPushMessageTypeStr(getPushMessageTypeList(template.getPushMessageType()));
        return vo;
    }

    /**
     * 参数校验
     */
    private void checkParam(PageAutoRuleStatusParam param) {
        if (CollectionUtils.isEmpty(param.getItemTypeList())) {
            throw new SponsoredBizException("受控对象类型不能为空！");
        }
        if (param.getItemId() == null) {
            throw new SponsoredBizException("受控对象id不能为空！");
        }
    }


    /**
     * @param value1 被除数
     * @param value2 除数
     * @return
     */
    protected BigDecimal calculationRateBigDecimal(BigDecimal value1, BigDecimal value2, Boolean per) {
        BigDecimal rate = value2.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(value1, value2);
        if (per) {
            rate = MathUtil.multiply(rate, BigDecimal.valueOf(100));
        }
        return rate.setScale(2, BigDecimal.ROUND_HALF_UP);
    }
}
