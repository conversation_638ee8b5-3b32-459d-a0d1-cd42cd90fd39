package com.meiyunji.sponsored.service.multiPlatform.walmart.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingCampaign;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingPlacement;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dto.WalmartAdvertisingPlacementUpdateDTO;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dto.WalmartAdvertisingPlacementUpdateStatusDTO;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dto.WalmartAdvertisingSyncDTO;

import com.meiyunji.sponsored.service.multiPlatform.walmart.qo.*;
import com.meiyunji.sponsored.service.multiPlatform.walmart.resp.WalmartAdPlacementListCreateResp;
import com.meiyunji.sponsored.service.multiPlatform.walmart.vo.PlacementPageAggregateVo;
import com.meiyunji.sponsored.service.multiPlatform.walmart.vo.PlacementPageVo;
import com.meiyunji.sponsored.service.multiPlatform.walmart.vo.WalmartAdPlacementCreateVo;
import com.walmart.oms.advertiser.WalmartAdvertiserClient;

import java.util.List;
import java.util.Map;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description:
 */
public interface IWalmartAdvertisingPlacementService {

    /**
     * 新增
     */
    void add(WalmartAdvertisingPlacement placement);

    /**
     * 删除
     */
    int delete(Integer puid, Long id);

    /**
     * 更新
     */
    int update(WalmartAdvertisingPlacement placement);

    Result<String> updatePlacementStatus(int puid, UpdatePlacementQo dto);
    Result<WalmartAdPlacementListCreateResp> updatePlacement(int puid, Integer shopId, String marketplaceCode, Integer operationId,
                                                             String campaignId, List<WalmartAdPlacementCreateVo> placementList);

    /**
     * 广告位列表页
     */
    Page<PlacementPageVo> placementPageList(Integer puid, PlacementPageQo qo);

    /**
     * 广告位列表汇总
     */
    PlacementPageAggregateVo placementPageAggregate(Integer puid, PlacementPageAggregateQo qo);

    int deleteByCampaignId(Integer puid, Integer shopId, String campaignId);

    List<WalmartAdvertisingPlacement> getByCampaignId(Integer puid, Integer shopId, String campaignId);

    int syncPlacementByCampaigns(Integer puid, List<Integer> shopIdList,
                                 WalmartAdvertiserClient advertiserClient, List<WalmartAdvertisingCampaign> campaigns) throws ServiceException;
}
