package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.po.AdTargetTaskDetail;
import com.meiyunji.sponsored.service.cpc.po.AdTargetTaskSuccessDetail;

import java.util.List;

public interface IAdTargetTaskDetailDao extends IBaseShardingDao<AdTargetTaskDetail> {
    /**
     * 失败任务详情列表
     *
     * @param taskId
     * @param puid
     * @param pageNo
     * @param pageSize
     * @return
     */
    Page<AdTargetTaskDetail> getFailurePage(long taskId, int puid, int pageNo, int pageSize);

    /**
     * 投放对象列表
     *
     * @param taskId
     * @param puid
     * @param pageNo
     * @param pageSize
     * @return
     */
    Page<AdTargetTaskDetail> getTargetObjectDetailPage(long taskId, int puid, int pageNo, int pageSize);

    /**
     * 任务详情列表
     *
     * @param taskId
     * @param puid
     * @return
     */
    List<AdTargetTaskDetail> getListByTaskId(long taskId, int puid);

    /**
     * 获取运行中的任务进度
     *
     * @param taskIds
     * @param puid
     * @return
     */
    List<AdTargetTaskSuccessDetail> getRunningTaskProcess(List<Long> taskIds, int puid);

    /**
     * 批量插入任务详情
     *
     * @param dataList
     * @param puid
     */
    void batchInsert(List<AdTargetTaskDetail> dataList, int puid);

    /**
     * 批量更新任务详情状态
     *
     * @param ids
     * @param puid
     */
    void batchUpdateSuccessStatus(List<Long> ids, int puid);

    /**
     * 批量更新任务详情状态
     *
     * @param puid
     */
    void batchUpdateFailureStatus(List<AdTargetTaskDetail> dataList, String failureReason, int puid);

    /**
     * 删除任务详情
     *
     * @param taskIds
     * @param puid
     */
    void deleteByTaskIds(List<Long> taskIds, int puid);
}
