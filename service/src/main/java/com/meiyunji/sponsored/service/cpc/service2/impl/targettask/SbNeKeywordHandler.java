package com.meiyunji.sponsored.service.cpc.service2.impl.targettask;

import com.amazon.advertising.sb.entity.nekeyword.CreateNeKeywordResponse;
import com.amazon.advertising.sb.entity.nekeyword.NeKeywordClient;
import com.amazon.advertising.sb.entity.nekeyword.SbNeKeyWordResult;
import com.amazon.advertising.sb.mode.keyword.Keyword;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskConstant;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskStatusEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdNeKeywordDao;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbNeKeywordService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbNeKeywordApiService;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-05-08 19:22
 */
@Service(AdTargetTaskConstant.SB_NE_KEYWORD_HANDLER)
@Slf4j
public class SbNeKeywordHandler implements TargetTaskHandler {

    @Autowired
    private TargetTaskComponent targetTaskComponent;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonSbAdNeKeywordDao amazonSbAdNeKeywordDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private CpcSbNeKeywordApiService cpcSbNeKeywordApiService;
    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private ICpcSbNeKeywordService cpcSbNeKeywordService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Override
    public void handle(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        //处理业务返回结果
        addKeywords(adTargetTask, adTargetTaskDetails);
    }

    public void addKeywords(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        List<AdTargetTaskDetail> needUpdateDetailList = new ArrayList<>();
        if (CollectionUtils.isEmpty(adTargetTaskDetails)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        // 排除已存在的关键词
        Set<String> adGroupIdSet = new HashSet<>();
        Set<String> keywordTextSet = new HashSet<>();
        Set<String> matchTypeSet = new HashSet<>();
        Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap = new HashMap<>();
        for (AdTargetTaskDetail targetTaskDetail : adTargetTaskDetails) {
            adGroupIdSet.add(targetTaskDetail.getAdGroupId());
            keywordTextSet.add(targetTaskDetail.getTargetObject().trim());
            matchTypeSet.add(targetTaskDetail.getMatchType());
            adTargetTaskDetailMap.put(targetTaskDetail.getId(), targetTaskDetail);
        }

        List<AmazonSbAdNeKeyword> repeatedAmazonAdKeywords = amazonSbAdNeKeywordDao.getListByTargetTaskCondition(adTargetTask.getPuid(), adTargetTask.getShopId(), adGroupIdSet, keywordTextSet, matchTypeSet);
        Map<String, AmazonSbAdNeKeyword> amazonAdKeywordMap = repeatedAmazonAdKeywords.stream()
                .collect(Collectors.toMap(each -> String.join("-", each.getAdGroupId(), each.getKeywordText(), each.getMatchType()), Function.identity(), (newVal, oldVal) -> newVal));

        int originalTaskDetailSize = adTargetTaskDetails.size();
        Iterator<AdTargetTaskDetail> it = adTargetTaskDetails.iterator();
        AdTargetTaskDetail next;
        while (it.hasNext()) {
            next = it.next();
            if (amazonAdKeywordMap.containsKey(String.join("-", next.getAdGroupId(), next.getTargetObject().trim(), next.getMatchType()))) {
                if (next.getStatus() <= AdTargetTaskStatusEnum.FAILURE.getCode()) {
                    next.setFailureReason("历史已存在相同的投放，请检查");
                    next.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                    needUpdateDetailList.add(next);
                }
                it.remove();
            }
        }

        if (CollectionUtils.isEmpty(adTargetTaskDetails)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        List<AmazonSbAdGroup> amazonAdGroups = amazonSbAdGroupDao.getAdGroupByIds(adTargetTask.getPuid(), adTargetTask.getShopId(), new ArrayList<>(adGroupIdSet));
        Map<String, AmazonSbAdGroup> amazonAdGroupMap = amazonAdGroups.stream().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, Function.identity(), (newVal, oldVal) -> newVal));

        List<AmazonSbAdNeKeyword> amazonAdKeywords = convertAddNeKeywordsVoToPo(adTargetTask.getUid(), amazonAdGroupMap, adTargetTaskDetails, needUpdateDetailList);
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(adTargetTask.getShopId(), adTargetTask.getPuid());
        if (shop == null) {
            adTargetTaskDetails.forEach(each -> {
                each.setFailureReason("店铺不存在");
                each.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                needUpdateDetailList.add(each);
            });
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(adTargetTask.getPuid(), adTargetTask.getShopId());
        if (profile == null) {
            adTargetTaskDetails.forEach(each -> {
                each.setFailureReason("没有站点对应的配置信息");
                each.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                needUpdateDetailList.add(each);
            });
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        int failureNum = 0;
        List<List<AmazonSbAdNeKeyword>> amazonAdKeywordPartition = Lists.partition(amazonAdKeywords, AdTargetTaskConstant.MAX_SB_TARGET_SIZE);
        for (List<AmazonSbAdNeKeyword> amazonAdKeywordList : amazonAdKeywordPartition) {
            Result result = createKeywords(amazonAdKeywordList, adTargetTaskDetailMap, shop, profile, adTargetTask);
            List<AdManageOperationLog> operationLogs = Lists.newArrayList();
            for (AmazonSbAdNeKeyword amazonSbAdKeyword : amazonAdKeywordList) {
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonSbAdKeyword.getTargetTaskDetailId());
                needUpdateDetailList.add(adTargetTaskDetail);
                if (AdTargetTaskStatusEnum.FAILURE.getCode() == adTargetTaskDetail.getStatus()) {
                    failureNum++;
                }
                AdManageOperationLog operationLog = adManageOperationLogService.getSbNeKeywordLog(null, amazonSbAdKeyword);
                operationLog.setIp(adTargetTask.getLoginIp());
                if (StringUtils.isNotBlank(amazonSbAdKeyword.getKeywordId())) {
                    operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    String errMsg;
                    if (StringUtils.isNotBlank(amazonSbAdKeyword.getErrMsg())) {
                        errMsg = "targetValue:" + amazonSbAdKeyword.getKeywordText() + ",desc:" + amazonSbAdKeyword.getErrMsg();
                    } else {
                        errMsg = result.getMsg();
                    }
                    operationLog.setResultInfo(errMsg);
                }
                operationLogs.add(operationLog);
            }
            adManageOperationLogService.batchLogsMergeByAdGroup(operationLogs);
            if (result.success()) {
                List<AmazonSbAdNeKeyword> succList = amazonAdKeywordList.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(succList)) {
                    // 有可能已经添加过了
                    List<String> existInDB = amazonSbAdNeKeywordDao.listByKeywordId(adTargetTask.getPuid(), adTargetTask.getShopId(), succList.stream()
                                    .map(AmazonSbAdNeKeyword::getKeywordId).collect(Collectors.toList()))
                            .stream()
                            .map(AmazonSbAdNeKeyword::getKeywordId).collect(Collectors.toList());

                    // 排除掉已有的
                    if (CollectionUtils.isNotEmpty(existInDB)) {
                        succList = succList.stream().filter(e -> !existInDB.contains(e.getKeywordId())).collect(Collectors.toList());
                    }
                    // 入库
                    try {
                        amazonSbAdNeKeywordDao.batchAdd(adTargetTask.getPuid(), succList);
                        List<String> keywordIds = succList.stream().map(AmazonSbAdNeKeyword::getKeywordId).collect(Collectors.toList());
                        cpcSbNeKeywordService.saveDoris(adTargetTask.getPuid(), adTargetTask.getShopId(), keywordIds);
                    } catch (Exception e) {
                        log.error("createSbKeyword:", e);
                    }

                    List<String> keywordIds = succList.stream().map(AmazonSbAdNeKeyword::getKeywordId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                    //创建成功, 需要在同步 获取否定关键词状态
                    ThreadPoolUtil.getNegativeSyncPool().execute(() -> {
                        try {
                            cpcSbNeKeywordApiService.syncNeKeywords(shop, null, keywordIds);
                        } catch (Exception e) {
                            log.info("添加成功后同步否定异常", e);
                        }
                    });
                    //同步广告组投放类型字段
                    try {
                        List<AmazonSbAdGroup> sbAdGroups = succList.stream().map(AmazonSbAdNeKeyword::getAdGroupId).distinct().map(amazonAdGroupMap::get)
                                .filter(each -> StringUtils.isNotBlank(each.getAdGroupType())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(sbAdGroups)) {
                            sbAdGroups.forEach(each -> each.setAdGroupType("keyword"));
                            amazonSbAdGroupDao.batchUpdateAdGroupType(adTargetTask.getPuid(), sbAdGroups);
                        }
                    } catch (Exception e) {
                        log.error("updateGroupTargetType:", e);
                    }
                }
            }
            targetTaskComponent.updateTaskDetailStatus(adTargetTask, needUpdateDetailList);
            needUpdateDetailList.clear();
        }

        failureNum += originalTaskDetailSize - amazonAdKeywords.size();
        int adTargetTaskStatus = targetTaskComponent.getAdTargetTaskStatus(originalTaskDetailSize, failureNum);
        targetTaskComponent.updateTaskStatus(adTargetTask, adTargetTaskStatus);
    }

    private Result createKeywords(List<AmazonSbAdNeKeyword> amazonAdNeKeywords, Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap, ShopAuth shop, AmazonAdProfile amazonAdProfile, AdTargetTask adTargetTask) {
        List<Keyword> keywordList = cpcSbNeKeywordApiService.makeNeKeywords(amazonAdNeKeywords);
        CreateNeKeywordResponse response = cpcApiHelper.call(shop, () -> NeKeywordClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).create(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), keywordList));

        if (response == null) {
            for (AmazonSbAdNeKeyword amazonSbAdNeKeyword : amazonAdNeKeywords) {
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonSbAdNeKeyword.getTargetTaskDetailId());
                adTargetTaskDetail.setFailureReason("网络延迟，请稍后重试");
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
            }
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "网络延迟，请稍后重试";
        String formatErrMsg = errMsg;
        if (CollectionUtils.isNotEmpty(response.getResultList())) {
            List<SbNeKeyWordResult> resultList = response.getResultList();
            int index = 0;
            for (SbNeKeyWordResult result : resultList) {
                if ("SUCCESS".equals(result.getCode())) {
                    AmazonSbAdNeKeyword amazonSbAdNeKeyword = amazonAdNeKeywords.get(index);
                    amazonSbAdNeKeyword.setKeywordId(String.valueOf(result.getKeywordId()));
                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonSbAdNeKeyword.getTargetTaskDetailId());
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.SUCCESS.getCode());
                } else {
                    AmazonSbAdNeKeyword amazonSbAdNeKeyword = amazonAdNeKeywords.get(index);
                    String returnError = StringUtils.isNotBlank(result.getDetails()) ? result.getDetails() : result.getDescription();
                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonSbAdNeKeyword.getTargetTaskDetailId());
                    String[] errorMsgPartition = targetTaskComponent.getNeKeywordErrorMsgPartition(adTargetTask, adTargetTaskDetail);
                    String error = AmazonErrorUtils.getErrorWithPlaceHolder(returnError,  adTargetTaskDetail.getTargetObject(), errorMsgPartition);
                    adTargetTaskDetail.setFailureReason(targetTaskComponent.getError(error, returnError));
                    adTargetTaskDetail.setFailureReasonDetail(returnError);
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                    amazonSbAdNeKeyword.setErrMsg("第" + (index + 1) + "个:" + error);
                }
                index++;
            }
            return ResultUtil.success();
        } else if (response.getError() != null) {
            String returnErrMsg = errMsg;
            if ("403".equals(response.getError().getCode())) {
                returnErrMsg = "店铺没有SB广告权限，请到Amazon后台开通SB广告管理";
            } else if (StringUtils.isNotBlank(response.getError().getDetails())) {
                returnErrMsg = response.getError().getDetails();
            } else {
                returnErrMsg = response.getError().getDescription();
            }
            errMsg = AmazonErrorUtils.getError(returnErrMsg);
            formatErrMsg = targetTaskComponent.getError(errMsg, returnErrMsg);
        }
        for (AmazonSbAdNeKeyword amazonSbAdNeKeyword : amazonAdNeKeywords) {
            AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonSbAdNeKeyword.getTargetTaskDetailId());
            adTargetTaskDetail.setFailureReason(formatErrMsg);
            adTargetTaskDetail.setFailureReasonDetail(errMsg);
            adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
        }
        return ResultUtil.error(errMsg);
    }

    private List<AmazonSbAdNeKeyword> convertAddNeKeywordsVoToPo(Integer uid, Map<String, AmazonSbAdGroup> amazonAdGroupMap, List<AdTargetTaskDetail> adTargetTaskDetails, List<AdTargetTaskDetail> needUpdateDetailList) {
        List<AmazonSbAdNeKeyword> keywordList = new ArrayList<>(adTargetTaskDetails.size());
        AmazonSbAdNeKeyword neKeyword;
        Iterator<AdTargetTaskDetail> it = adTargetTaskDetails.iterator();
        AdTargetTaskDetail adTargetTaskDetail;
        while (it.hasNext()) {
            adTargetTaskDetail = it.next();
            AmazonSbAdGroup adGroup = amazonAdGroupMap.get(adTargetTaskDetail.getAdGroupId());
            if (adGroup == null) {
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                adTargetTaskDetail.setFailureReason("广告组不存在");
                needUpdateDetailList.add(adTargetTaskDetail);
                it.remove();
            } else {
                neKeyword = new AmazonSbAdNeKeyword();
                neKeyword.setPuid(adGroup.getPuid());
                neKeyword.setShopId(adGroup.getShopId());
                neKeyword.setMarketplaceId(adGroup.getMarketplaceId());
                neKeyword.setProfileId(adGroup.getProfileId());
                neKeyword.setAdGroupId(adGroup.getAdGroupId());
                neKeyword.setCampaignId(adGroup.getCampaignId());
                neKeyword.setKeywordText(adTargetTaskDetail.getTargetObject());
                neKeyword.setMatchType(adTargetTaskDetail.getMatchType());
                neKeyword.setState("enabled");
                neKeyword.setCreateId(uid);
                neKeyword.setCreateInAmzup(1);
                neKeyword.setTargetTaskDetailId(adTargetTaskDetail.getId());
                keywordList.add(neKeyword);
            }
        }

        return keywordList;
    }
}
