package com.meiyunji.sponsored.service.reportImport.processor;

import com.alibaba.excel.EasyExcel;
import com.amazonaws.util.json.Jackson;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.reportImport.dao.ICpcReportsImportPlatformDao;
import com.meiyunji.sponsored.service.reportImport.dao.ICpcReportsImportTaskScheduleDao;
import com.meiyunji.sponsored.service.reportImport.entity.CpcReportsImportPlatform;
import com.meiyunji.sponsored.service.reportImport.entity.CpcReportsImportTaskSchedule;
import com.meiyunji.sponsored.service.reportImport.enums.LxReportImportType;
import com.meiyunji.sponsored.service.reportImport.enums.LxReportType;
import com.meiyunji.sponsored.service.reportImport.enums.ReportImportErrType;
import com.meiyunji.sponsored.service.reportImport.enums.ReportImportStatus;
import com.meiyunji.sponsored.service.reportImport.listener.AbstractLxReportReadListener;
import com.meiyunji.sponsored.service.reportImport.listener.converter.CustomStringNumberConverter;
import com.meiyunji.sponsored.service.reportImport.message.AdReportImportMessage;
import com.meiyunji.sponsored.service.reportImport.vo.ReportImportDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 导入lx报告处理类
 *
 * <AUTHOR>
 * @date 2023/05/26
 */
@Service
@Slf4j
public class LxReportImportFromExcelHolder implements ApplicationContextAware {

    private ApplicationContext applicationContext;
    private final ICpcReportsImportPlatformDao cpcReportsImportPlatformDao;
    private final ICpcReportsImportTaskScheduleDao cpcReportsImportTaskScheduleDao;
    private final IScVcShopAuthDao shopAuthDao;
    private final CosBucketClient tempBucketClient;

    public LxReportImportFromExcelHolder(
            ICpcReportsImportPlatformDao cpcReportsImportPlatformDao,
            ICpcReportsImportTaskScheduleDao cpcReportsImportTaskScheduleDao,
            IScVcShopAuthDao shopAuthDao,
            CosBucketClient tempBucketClient) {
        this.cpcReportsImportPlatformDao = cpcReportsImportPlatformDao;
        this.cpcReportsImportTaskScheduleDao = cpcReportsImportTaskScheduleDao;
        this.shopAuthDao = shopAuthDao;
        this.tempBucketClient = tempBucketClient;
    }

    public void executor(AdReportImportMessage message) {
        //把任务状态修改为处理中,修改失败,直接丢弃任务
        int res = cpcReportsImportTaskScheduleDao.updateStatusById(message.getPuid(), message.getScheduleId(),
                ReportImportStatus.PROCESSING, "",  ReportImportStatus.WAITING);

        if (res == 0) {
            log.error("wade-import-report puid: {} scheduleId: {} 修改状态修改,任务可能已删除或已处理", message.getPuid(), message.getScheduleId());
            return;
        }

        LxReportImportType importType = LxReportImportType.getByReportType(LxReportType.valueOf(message.getReportType()));
        AbstractLxReportReadListener importReadListener = applicationContext.getBean(importType.getReadListenerClass());
        //把消息和可用店铺集合传入listener中
        importReadListener.setAdReportImportMessage(message);
        importReadListener.setShopNameMap(getShopMapByTaskId(message.getPuid(), message.getTaskId()));

        //读取数据
        try {
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(tempBucketClient.
                    getObjectToBytes(message.getFileId().replaceFirst("/", "")));
            EasyExcel.read(byteArrayInputStream, importType.getDataClass(), importReadListener).sheet().doRead();

        } catch (Exception e) {
            log.error("wade-report-import puid: {} taskId: {} report-type: {} read file with error: ",
                    message.getPuid(), message.getScheduleId(), message.getReportType(), e);
            CpcReportsImportTaskSchedule schedule = cpcReportsImportTaskScheduleDao.getById(message.getPuid(), message.getScheduleId());
            ReportImportDetailVo fileVo = new ReportImportDetailVo();
            fileVo.setStatus(ReportImportStatus.FATAL.name());
            fileVo.setFileName(schedule.getFileName());
            fileVo.setRowNumber(0);
            fileVo.setErrType(ReportImportErrType.EXCEPTION.name());
            fileVo.setErrInfos(Collections.singletonList("读取文件失败"));

            cpcReportsImportTaskScheduleDao.updateStatusById(message.getPuid(), message.getScheduleId(),
                    ReportImportStatus.FATAL, Jackson.toJsonString(fileVo),
                    ReportImportStatus.PROCESSING);
        }

    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * 通过任务id查询店铺信息
     *
     * @param puid   puid
     * @param taskId 任务id
     * @return {@link List}<{@link ShopAuth}>
     */
    public List<ShopAuth> getShopsByTaskId(int puid, Long taskId) {
        List<CpcReportsImportPlatform> list = cpcReportsImportPlatformDao.getListByTaskId(puid, taskId);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayListWithExpectedSize(0);
        }
        return shopAuthDao.listAllByIds(puid, list.stream().map(CpcReportsImportPlatform::getShopId).collect(Collectors.toList()));
    }

    /**
     * 通过任务id查询店铺信息Map
     *
     * @param puid   puid
     * @param taskId 任务id
     * @return {@link Map}<{@link String}, {@link ShopAuth}>
     */
    public Map<String, ShopAuth> getShopMapByTaskId(int puid, Long taskId) {
        List<ShopAuth> shopAuths = getShopsByTaskId(puid, taskId);
        return shopAuths.stream().collect(Collectors.toMap(ShopAuth::getName, Function.identity(), (a, b) -> a));
    }
}
