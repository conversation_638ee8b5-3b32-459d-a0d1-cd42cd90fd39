package com.meiyunji.sponsored.service.doris.dao.globalView.impl;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.doris.dao.globalView.IGlobalViewDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.newDashboard.dto.globalView.base.GlobalViewBaseDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.globalView.GlobalViewOrderByEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.globalView.GlobalViewProcessorEnum;
import com.meiyunji.sponsored.service.newDashboard.vo.globalView.GlobalViewBaseReqVo;
import com.meiyunji.sponsored.service.util.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 全局概览-dao实现类
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Repository
@Slf4j
public class GlobalViewDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdCampaignAllReport> implements IGlobalViewDao {

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Override
    public Page<GlobalViewBaseDataDto> getPageData(GlobalViewBaseReqVo req, GlobalViewProcessorEnum processorEnum) {
        List<Object> argsList = new ArrayList<>();
        // 获取排序枚举
        GlobalViewOrderByEnum orderByEnum = GlobalViewOrderByEnum.getEnumByCode(req.getOrderByField());
        // 获取查询sql语句
        String selectSql = getSelectSql(req, argsList, processorEnum, req.getStartDate(), req.getEndDate(), orderByEnum, false);
        if (orderByEnum.getShopData()) {
            // 店铺指标排序需要关联店铺表后排序
            selectSql = getShopOrderSql(req, processorEnum, selectSql, argsList);
        }
        // 获取countSql
        String countSql = getCountSql(selectSql, req, processorEnum);
        // 获取pageSql
        String pageSql = getPageSql(req, selectSql, orderByEnum, processorEnum);
        return getPageResultByClass(req.getPageNo(), req.getPageSize(), countSql, argsList.toArray(), pageSql, argsList.toArray(), GlobalViewBaseDataDto.class);
    }

    @Override
    public List<GlobalViewBaseDataDto> getListData(GlobalViewBaseReqVo req, GlobalViewProcessorEnum processorEnum,
                                                   String startDate, String endDate) {
        List<Object> argsList = new ArrayList<>();
        String selectSql = getSelectSql(req, argsList, processorEnum, startDate, endDate, null, false);
        // 日期列表需要使用临时表
        if (GlobalViewProcessorEnum.DATE.equals(processorEnum)) {
            String temporaryTable = temporaryTable(startDate, endDate);
            selectSql = temporaryTable + selectSql;
        }
        return getJdbcTemplate().query(selectSql, new ObjectMapper<>(GlobalViewBaseDataDto.class), argsList.toArray());
    }

    @Override
    public GlobalViewBaseDataDto getSumData(GlobalViewBaseReqVo req, GlobalViewProcessorEnum processorEnum) {
        List<Object> argsList = new ArrayList<>();
        String selectSql = getSelectSql(req, argsList, processorEnum, req.getStartDate(), req.getEndDate(), null, true);
        return getJdbcTemplate().queryForObject(selectSql, new ObjectMapper<>(GlobalViewBaseDataDto.class), argsList.toArray());
    }

    @Override
    public List<GlobalViewBaseDataDto> getShopSaleList(GlobalViewBaseReqVo req, GlobalViewProcessorEnum processorEnum,
                                                       String startDate, String endDate) {
        List<Object> argsList = new ArrayList<>();
        String sql = getShopSaleSql(req, argsList, processorEnum, startDate, endDate);
        return getJdbcTemplate().query(sql, new ObjectMapper<>(GlobalViewBaseDataDto.class), argsList.toArray());
    }

    @Override
    public GlobalViewBaseDataDto getShopSumData(GlobalViewBaseReqVo req) {
        List<Object> argsList = new ArrayList<>();
        String sql = getShopSaleSql(req, argsList, null, req.getStartDate(), req.getEndDate());
        return getJdbcTemplate().queryForObject(sql, new ObjectMapper<>(GlobalViewBaseDataDto.class), argsList.toArray());
    }

    /**
     * 分页列表获取统计sql
     */
    private String getCountSql(String selectSql, GlobalViewBaseReqVo req, GlobalViewProcessorEnum processorEnum) {
        StringBuilder countSql = new StringBuilder();
        countSql.append(" select count(*) from( ");
        countSql.append(selectSql);
        countSql.append(" ) a ");
        // 日期列表需要使用临时表
        if (GlobalViewProcessorEnum.DATE.equals(processorEnum)) {
            String temporaryTable = temporaryTable(req.getStartDate(), req.getEndDate());
            countSql.insert(0, temporaryTable);
        }
        return countSql.toString();
    }

    /**
     * 分页列表获取查询sql
     */
    private String getPageSql(GlobalViewBaseReqVo req, String selectSql, GlobalViewOrderByEnum orderByEnum, GlobalViewProcessorEnum processorEnum) {
        StringBuilder pageSql = new StringBuilder();
        pageSql.append(selectSql);
        // 排序
        pageSql.append(" order by ").append(orderByEnum.getCode()).append(" ").append(req.getOrderBy());
        if (orderByEnum.getShopData()) {
            pageSql.append(" ,a.`key` desc");
        } else {
            pageSql.append(" ,`key` desc");
        }
        // 日期列表需要使用临时表
        if (GlobalViewProcessorEnum.DATE.equals(processorEnum)) {
            String temporaryTable = temporaryTable(req.getStartDate(), req.getEndDate());
            pageSql.insert(0, temporaryTable);
        }
        return pageSql.toString();
    }

    /**
     * 根据店铺销售额排序字段sql
     */
    private String getShopOrderSql(GlobalViewBaseReqVo req, GlobalViewProcessorEnum processorEnum, String selectSql, List<Object> argsList) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select a.*,t.shopSaleNum,t.shopSales,ifnull(a.adCost/t.shopSales, 0) as acots ,ifnull(a.adSale/t.shopSales, 0) as asots");
        String shopSql = getShopSaleSql(req, argsList, processorEnum, req.getStartDate(), req.getEndDate());
        sql.append(" from( ")
                .append(selectSql)
                .append(" ) a left join ( ")
                .append(shopSql)
                .append(" ) t ON a.")
                .append(processorEnum.getShopJoin())
                .append(" = t.`key`");
        return sql.toString();
    }

    /**
     * 获取select sql
     *
     * @param processorEnum 列表类型枚举
     * @param orderByEnum   排序枚举
     * @param sum           是否汇总sql 汇总sql不用返回key值
     * @return select sql
     */
    private String getSelectSql(GlobalViewBaseReqVo req, List<Object> argsList, GlobalViewProcessorEnum processorEnum,
                                String startDate, String endDate, GlobalViewOrderByEnum orderByEnum, Boolean sum) {
        String selectSql;
        if (GlobalViewProcessorEnum.SALESMAN.equals(processorEnum)) {
            // 业务员列表sql 从产品报告表汇总数据
            selectSql = getSelectSalesman(req, argsList, processorEnum, startDate, endDate, orderByEnum, sum);
        } else if (GlobalViewProcessorEnum.DATE.equals(processorEnum) && !sum) {
            // 日期列表sql 需要临时表因此单独封装
            selectSql = getDateColumn(req, argsList, processorEnum, startDate, endDate, orderByEnum, sum);
        } else if (GlobalViewProcessorEnum.PLACEMENT.equals(processorEnum)){
            // 广告位 从广告位报告表汇总数据
            selectSql = getSelectPlacementSql(req, argsList, processorEnum, startDate, endDate, orderByEnum, sum);
        }else {
            // 其他列表页 从活动报告表汇总数据
            selectSql = getSelectCommonSql(req, argsList, processorEnum, startDate, endDate, orderByEnum, sum);
        }
        return selectSql;
    }

    /**
     * 店铺指标统计-sql
     */
    private String getShopSaleSql(GlobalViewBaseReqVo req, List<Object> argsList, GlobalViewProcessorEnum processorEnum,
                                  String startDate, String endDate) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select ");
        if (processorEnum != null && StringUtil.isNotEmpty(processorEnum.getShopGroupBy())) {
            sql.append(" s.").append(processorEnum.getShopGroupBy()).append(" as `key`,");
        }
        sql.append(" ifnull(sum(s.sale_price * c.rate), 0) shopSales, ");
        sql.append(" ifnull(sum(s.sale_num), 0) shopSaleNum ");
        sql.append(" from dws_sale_profit_shop_day s ");
        sql.append(" join ( select rate,month,`from`,puid ");
        sql.append(" from dim_currency_rate where puid = ? and `to` = ? and month >= ? and month <= ? ");
        argsList.add(req.getPuid());
        argsList.add(req.getCurrency());
        argsList.add(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        argsList.add(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        sql.append(" ) c on s.puid = c.puid and DATE_FORMAT(s.now_date, '%Y%m') = c.month and s.currency = c.`from` ");
        sql.append(" where s.puid =? and s.now_date >= ? and s.now_date <= ? ");
        argsList.add(req.getPuid());
        argsList.add(startDate);
        argsList.add(endDate);
        if (CollectionUtils.isNotEmpty(req.getMarketplaceIdList())) {
            sql.append(SqlStringUtil.dealDorisInList("s.marketplace_id", req.getMarketplaceIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(req.getShopIdList())) {
            sql.append(SqlStringUtil.dealDorisInList("s.shop_id", req.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(req.getDateList())) {
            sql.append(SqlStringUtil.dealDorisInList("s.now_date", req.getDateList(), argsList));
        }
        if (processorEnum != null && StringUtil.isNotEmpty(processorEnum.getShopGroupBy())) {
            sql.append(" group by s.").append(processorEnum.getShopGroupBy());
        }
        return sql.toString();
    }

    /**
     * 列表页select-sql
     */
    private String getSelectCommonSql(GlobalViewBaseReqVo req, List<Object> argsList, GlobalViewProcessorEnum processorEnum,
                                      String startDate, String endDate, GlobalViewOrderByEnum orderByEnum, Boolean sum) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select ");
        if (!sum && processorEnum != null) {
            sql.append(processorEnum.getKey()).append(" as `key`,");
            sql.append(processorEnum.getGroupBy()).append(",");
        }
        sql.append(" ifnull(sum(cost * c.rate), 0) adCost, ");
        sql.append(" ifnull(sum(if(type = 'sp', sales7d, sales14d) * c.rate), 0) adSale, ");
        sql.append(" ifnull(sum(impressions), 0) impressions, ");
        sql.append(" ifnull(sum(clicks), 0) clicks, ");
        sql.append(" ifnull(sum(if (type = 'sp', conversions7d, conversions14d)), 0) adOrderNum, ");
        sql.append(" ifnull(sum(if (type = 'sp' , units_ordered7d, if (type = 'sb' , units_sold14d, units_ordered14d))), 0) adSaleNum ");
        if (orderByEnum != null && StringUtil.isNotEmpty(orderByEnum.getColumn())) {
            sql.append(orderByEnum.getColumn());
        }
        sql.append(" from ods_t_amazon_ad_campaign_all_report report ");
        sql.append(" join (select m.marketplace_id, c.month, c.rate ");
        sql.append(" from dim_currency_rate c  join dim_marketplace_info m on ");
        sql.append(" c.`from` = m.currency and c.puid = ? and `to` = ? and month >= ? and month <= ? ");
        argsList.add(req.getPuid());
        argsList.add(req.getCurrency());
        argsList.add(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        argsList.add(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        sql.append(" ) c on report.marketplace_id = c.marketplace_id and report.count_month = c.month ");
        sql.append(" where report.puid = ? and report.count_day >= ? and report.count_day <= ? ");
        argsList.add(req.getPuid());
        argsList.add(startDate);
        argsList.add(endDate);
        if (GlobalViewProcessorEnum.PLACEMENT.equals(processorEnum)) {
            sql.append(" and report.is_summary = 0 and report.type = 'sp' ");
        } else {
            sql.append(" and report.is_summary = 1 ");
        }
        if (CollectionUtils.isNotEmpty(req.getMarketplaceIdList())) {
            sql.append(SqlStringUtil.dealDorisInList("report.marketplace_id", req.getMarketplaceIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(req.getShopIdList())) {
            sql.append(SqlStringUtil.dealDorisInList("report.shop_id", req.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(req.getPlacementList())) {
            sql.append(SqlStringUtil.dealDorisInList("report.campaign_type", req.getPlacementList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(req.getCampaignIds())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("report.campaign_id", req.getCampaignIds(), argsList));
        }
        if(CollectionUtils.isNotEmpty(req.getPortfolioIds()) && CollectionUtils.isEmpty(req.getCampaignIds())){
            // 广告组合子查询筛选
            wherePortfolio(req, argsList, sql);
        }
        if (!sum && processorEnum != null) {
            sql.append(" group by ").append(processorEnum.getGroupBy());
        }
        return sql.toString();
    }


    /**
     * 获取灰度表名
     * @param puid
     * @return
     */
    private String getGrayTableName(Integer puid) {
        String tableName = "ods_t_amazon_ad_campaign_all_report";
        if (dynamicRefreshConfiguration.verifyDorisPage(puid, dynamicRefreshConfiguration.getDorisPagePlacementReport())) {
            tableName = "ods_t_amazon_ad_campaign_placement_report";
        }
        return tableName;
    }

    /**
     * 列表页select-sql
     */
    private String getSelectPlacementSql(GlobalViewBaseReqVo req, List<Object> argsList, GlobalViewProcessorEnum processorEnum,
                                      String startDate, String endDate, GlobalViewOrderByEnum orderByEnum, Boolean sum) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select ");
        if (!sum && processorEnum != null) {
            sql.append(processorEnum.getKey()).append(" as `key`,");
            sql.append(processorEnum.getGroupBy()).append(",");
        }
        sql.append(" ifnull(sum(cost * c.rate), 0) adCost, ");
        sql.append(" ifnull(sum(sales7d * c.rate), 0) adSale, ");
        sql.append(" ifnull(sum(impressions), 0) impressions, ");
        sql.append(" ifnull(sum(clicks), 0) clicks, ");
        sql.append(" ifnull(sum(conversions7d), 0) adOrderNum, ");
        sql.append(" ifnull(sum(units_ordered7d), 0) adSaleNum ");
        if (orderByEnum != null && StringUtil.isNotEmpty(orderByEnum.getColumn())) {
            sql.append(orderByEnum.getColumn());
        }
        sql.append(" from ");
        sql.append(getGrayTableName(req.getPuid()));
        sql.append(" report ");
        sql.append(" join (select m.marketplace_id, c.month, c.rate ");
        sql.append(" from dim_currency_rate c  join dim_marketplace_info m on ");
        sql.append(" c.`from` = m.currency and c.puid = ? and `to` = ? and month >= ? and month <= ? ");
        argsList.add(req.getPuid());
        argsList.add(req.getCurrency());
        argsList.add(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        argsList.add(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        sql.append(" ) c on report.marketplace_id = c.marketplace_id and report.count_month = c.month ");
        sql.append(" where report.puid = ? and report.count_day >= ? and report.count_day <= ? ");
        argsList.add(req.getPuid());
        argsList.add(startDate);
        argsList.add(endDate);
        sql.append(" and report.is_summary = 0 and report.type = 'sp' ");
        if (CollectionUtils.isNotEmpty(req.getMarketplaceIdList())) {
            sql.append(SqlStringUtil.dealDorisInList("report.marketplace_id", req.getMarketplaceIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(req.getShopIdList())) {
            sql.append(SqlStringUtil.dealDorisInList("report.shop_id", req.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(req.getPlacementList())) {
            sql.append(SqlStringUtil.dealDorisInList("report.campaign_type", req.getPlacementList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(req.getCampaignIds())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("report.campaign_id", req.getCampaignIds(), argsList));
        }
        if(CollectionUtils.isNotEmpty(req.getPortfolioIds()) && CollectionUtils.isEmpty(req.getCampaignIds())){
            // 广告组合子查询刷选
            wherePortfolio(req, argsList, sql);
        }
        if (!sum && processorEnum != null) {
            sql.append(" group by ").append(processorEnum.getGroupBy());
        }
        return sql.toString();
    }

    /**
     * 业务员列表页select-sql
     */
    private String getSelectSalesman(GlobalViewBaseReqVo req, List<Object> argsList, GlobalViewProcessorEnum processorEnum,
                                     String startDate, String endDate, GlobalViewOrderByEnum orderByEnum, Boolean sum) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select ");
        if (!sum && processorEnum != null) {
            sql.append(processorEnum.getKey()).append(" as `key`,");
            sql.append(processorEnum.getGroupBy()).append(",");
        }
        sql.append(" ifnull(sum(report.cost * c.rate), 0) adCost, ");
        sql.append(" ifnull(sum(report.total_sales * c.rate), 0) adSale, ");
        sql.append(" ifnull(sum(report.impressions), 0) impressions, ");
        sql.append(" ifnull(sum(report.clicks), 0) clicks, ");
        sql.append(" ifnull(sum(report.order_num), 0) adOrderNum, ");
        sql.append(" ifnull(sum(report.sale_num), 0) adSaleNum ");
        if (orderByEnum != null && StringUtil.isNotEmpty(orderByEnum.getSalesmanColumn())) {
            sql.append(orderByEnum.getSalesmanColumn());
        }
        sql.append(" from( ");
        sql.append(" SELECT puid, shop_id, marketplace_id , count_day, cost,total_sales,impressions,clicks,order_num as sale_num,sale_num as order_num,asin,sku, count_month ");
        sql.append(" from ods_t_amazon_ad_product_report ");
        sql.append(" where puid = ? and count_day >= ? and count_day <= ? ");
        argsList.add(req.getPuid());
        argsList.add(startDate);
        argsList.add(endDate);
        if (CollectionUtils.isNotEmpty(req.getMarketplaceIdList())) {
            sql.append(SqlStringUtil.dealDorisInList("marketplace_id", req.getMarketplaceIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(req.getShopIdList())) {
            sql.append(SqlStringUtil.dealDorisInList("shop_id", req.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(req.getCampaignIds())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", req.getCampaignIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(req.getPortfolioIds()) && CollectionUtils.isEmpty(req.getCampaignIds())) {
            // 广告组合子查询筛选
            wherePortfolio(req, argsList, sql);
        }
        sql.append(" union all ");
        sql.append(" SELECT puid, shop_id, marketplace_id , count_day, cost,sales14d total_sales,impressions,clicks,conversions14d order_num,units_ordered14d sale_num,asin,sku, count_month ");
        sql.append(" from ods_t_amazon_ad_sd_product_report ");
        sql.append(" where puid = ? and count_day >= ? and count_day <= ? ");
        argsList.add(req.getPuid());
        argsList.add(startDate);
        argsList.add(endDate);
        if (CollectionUtils.isNotEmpty(req.getMarketplaceIdList())) {
            sql.append(SqlStringUtil.dealDorisInList("marketplace_id", req.getMarketplaceIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(req.getShopIdList())) {
            sql.append(SqlStringUtil.dealDorisInList("shop_id", req.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(req.getCampaignIds())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", req.getCampaignIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(req.getPortfolioIds()) && CollectionUtils.isEmpty(req.getCampaignIds())) {
            // 广告组合子查询筛选
            wherePortfolio(req, argsList, sql);
        }
        sql.append(" ) as report ");
        sql.append(" JOIN ods_t_product p on p.puid = report.puid and p.shop_id = report.shop_id  and p.asin = report.asin and p.sku = report.sku  and p.dev_id is not null and p.dev_id != '' and p.dev_id != ' ' ");
        sql.append(" and p.puid = ? ");
        argsList.add(req.getPuid());
        if (CollectionUtils.isNotEmpty(req.getShopIdList())) {
            sql.append(SqlStringUtil.dealDorisInList("p.shop_id", req.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(req.getDevIdList())) {
            sql.append(SqlStringUtil.dealDorisInList("p.dev_id", req.getDevIdList(), argsList));
        }
        sql.append(" join ( select m.marketplace_id, c.month, c.rate ");
        sql.append(" from dim_currency_rate c join dim_marketplace_info m on");
        sql.append(" c.`from` = m.currency and c.puid = ? and `to` = ? and month >= ? and month <= ?) c on ");
        argsList.add(req.getPuid());
        argsList.add(req.getCurrency());
        argsList.add(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        argsList.add(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        sql.append(" report.marketplace_id = c.marketplace_id and report.count_month = c.month ");
        if (!sum && processorEnum != null) {
            sql.append(" group by ").append(processorEnum.getGroupBy());
        }
        return sql.toString();
    }

    private static void wherePortfolio(GlobalViewBaseReqVo req, List<Object> argsList, StringBuilder sql) {
        sql.append(" and campaign_id in ( ");
        sql.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
        argsList.add(req.getPuid());
        if (req.getPortfolioIds().contains(Constant.NON_PORTFOLIO_ID)) {
            sql.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ");
            if (req.getPortfolioIds().size() > 1) {
                sql.append(SqlStringUtil.dealInListOr("portfolio_id", req.getPortfolioIds(), argsList));
            }
            sql.append(" ) ");
        } else {
            sql.append(SqlStringUtil.dealDorisInList("portfolio_id", req.getPortfolioIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(req.getMarketplaceIdList())) {
            sql.append(SqlStringUtil.dealDorisInList("marketplace_id", req.getMarketplaceIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(req.getShopIdList())) {
            sql.append(SqlStringUtil.dealDorisInList("shop_id", req.getShopIdList(), argsList));
        }
        sql.append(" ) ");
    }

    /**
     * 日期列表页select-sql
     */
    private String getDateColumn(GlobalViewBaseReqVo req, List<Object> argsList, GlobalViewProcessorEnum processorEnum,
                                 String startDate, String endDate, GlobalViewOrderByEnum orderByEnum, Boolean sum) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select ");
        if (!sum && processorEnum != null) {
            sql.append(" a.count_day as `key`,");
            sql.append(" a.count_day countDay,");
        }
        sql.append(" ifnull(adCost, 0) adCost, ");
        sql.append(" ifnull(adSale, 0) adSale, ");
        sql.append(" ifnull(impressions, 0) impressions, ");
        sql.append(" ifnull(clicks, 0) clicks, ");
        sql.append(" ifnull(adOrderNum, 0) adOrderNum, ");
        sql.append(" ifnull(adSaleNum, 0) adSaleNum ");
        if (orderByEnum != null && StringUtil.isNotEmpty(orderByEnum.getDateColumn())) {
            sql.append(orderByEnum.getDateColumn());
        }
        sql.append(" from temporary_date_table a left join (");
        String commonSql = getSelectCommonSql(req, argsList, processorEnum, startDate, endDate, orderByEnum, sum);
        sql.append(commonSql);
        sql.append(") b on a.count_day = b.count_day");
        return sql.toString();
    }

    /**
     * 创建临时表
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 临时表
     */
    private String temporaryTable(String startDate, String endDate) {
        StringBuilder sql = new StringBuilder();
        sql.append(" WITH temporary_date_table AS ( ");
        List<String> datesInRange = getDatesInRange(startDate, endDate);
        String last = datesInRange.get(datesInRange.size() - 1);
        for (String date : datesInRange) {
            if (!date.equals(last)) {
                sql.append(" SELECT '").append(date).append("' AS count_day UNION ALL ");
            } else {
                sql.append(" SELECT '").append(date).append("' AS count_day ) ");
            }
        }
        return sql.toString();
    }

    /**
     * 根据开始结束日期获取中间日期值
     */
    public static List<String> getDatesInRange(String startDateStr, String endDateStr) {
        List<String> dates = new ArrayList<>();
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Calendar startDate = Calendar.getInstance();
            startDate.setTime(sdf.parse(startDateStr));
            Calendar endDate = Calendar.getInstance();
            endDate.setTime(sdf.parse(endDateStr));

            while (!startDate.after(endDate)) {
                dates.add(sdf.format(startDate.getTime()));
                startDate.add(Calendar.DAY_OF_MONTH, 1);
            }
        } catch (Exception e) {
            log.error("get between dateList error startDateStr:{},endDateStr:{}", startDateStr, endDateStr, e);
        }
        return dates;
    }
}
