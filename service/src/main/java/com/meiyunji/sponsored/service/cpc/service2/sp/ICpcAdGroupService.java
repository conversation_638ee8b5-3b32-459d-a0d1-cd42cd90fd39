package com.meiyunji.sponsored.service.cpc.service2.sp;

import com.amazon.advertising.spV3.group.entity.GroupEntityV3;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.grpc.common.AdHourReportRequest;
import com.meiyunji.sponsored.rpc.adCommon.AllGroupAggregateDataResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllGroupDataResponse;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroupDorisAllReport;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdProduct;
import com.meiyunji.sponsored.service.reportHour.vo.AdReportHourlyVO;

import java.util.List;

/**
 * Created by xp on 2021/3/30.
 * 广告组业务
 */
public interface ICpcAdGroupService {

    /**
     * 广告活动下的广告组-列表页
     * @param puid ：
     * @param param ：
     * @return :
     */
    AllGroupDataResponse.GroupHomeVo getAllGroupData(Integer puid, GroupPageParam param);
    AllGroupDataResponse.GroupHomeVo getNewAllGroupData(Integer puid, GroupPageParam param);
    /**
     * sp 广告组 查询doris
     */
    AllGroupDataResponse.GroupHomeVo getSpGroupDorisData(Integer puid, GroupPageParam param);

    /**
     *填充广告策略标签
     */
    void fillAdStrategy(GroupPageParam param,List<GroupPageVo> rows);

    /**
     * sp 广告组列表页、导出通用方法
     * @param puid
     * @param param
     * @param voPage
     * @return
     */
    Page<GroupPageVo> getSpDorisPageList(Integer puid, GroupPageParam param, Page<GroupPageVo> voPage, boolean isExport);

    AllGroupAggregateDataResponse.GroupHomeVo getAllGroupAggregateDataNew(Integer puid, GroupPageParam param);

    AllGroupAggregateDataResponse.GroupHomeVo getAllGroupAggregateData(Integer puid, GroupPageParam param);

    AllGroupAggregateDataResponse.GroupHomeVo getSpGroupDorisAggregateData(Integer puid, GroupPageParam param);

    List<GroupPageVo> getSpGroupVoListNew(int puid, GroupPageParam param, Page<GroupPageVo> voPage, boolean isCompare, boolean isExport);

    List<GroupPageVo> getSdGroupVoListNew(int puid, GroupPageParam param, Page<GroupPageVo> voPage, boolean isCompare, boolean isExport);

    List<GroupPageVo> getSbGroupVoListNew(int puid, GroupPageParam param, Page<GroupPageVo> voPage, boolean isCompare, boolean isExport);

    List<GroupPageVo> getSpGroupVoList(Integer puid, GroupPageParam param, Page<GroupPageVo> voPage, boolean isExport);

    List<GroupPageVo> getSdGroupVoList(Integer puid, GroupPageParam param, Page<GroupPageVo> voPage, boolean isExport);

    List<GroupPageVo> getSbGroupVoList(Integer puid, GroupPageParam param, Page<GroupPageVo> voPage, boolean isExport);

    /**
     * 创建广告组
     *
     * @param vo ：
     * @return ：返回广告组id
     */
    Result<String> createAdGroup(SPadGroupVo vo);

    /**
     * 创建广告组
     *
     * @param vo ：
     * @return ：返回广告组id
     */
    Result<String> createAdGroupWithAuthed(SPadGroupVo vo, ShopAuth shop);

    /**
     * 编辑广告组
     *
     * @param vo ：
     * @return ：
     */
    Result updateAdGroup(SPadGroupVo vo);

    /**
     * 改状态
     *
     * @param puid
     * @param uid
     * @param loginIp
     * @param id      ：主键id
     * @param state   ：
     * @return ：
     */
    Result updateStatus(Integer puid, Integer uid, String loginIp, Long id, String state, List<Integer> authedShopIdList);

    /**
     * 改名字
     *
     * @param puid
     * @param uid
     * @param loginIp
     * @param id ：主键id
     * @param name ：
     * @return ：
     */
    Result updateName(Integer puid, Integer uid, String loginIp, Long id, String name);

    /**
     * 改竞价
     *
     * @param puid
     * @param uid
     * @param loginIp
     * @param id      ：主键id
     * @param bid     ：
     * @return ：
     */
    Result updateBid(Integer puid, Integer uid, String loginIp, Long id, Double bid, List<Integer> authedShopIdList);

    /**
     * 广告组-归档
     *
     * @param puid
     * @param uid
     * @param loginIp
     * @param id ：
     * @return ：
     */
    Result archive(Integer puid, Integer uid, String loginIp, Long id);

    /**
     * 广告活动下的广告组
     * @param puid:
     * @param shopId:
     * @param campaignId:
     * @param adaptType: keyword, neKeyword, targeting, neTargeting
     * @return :
     */
    Result<List<GroupNameVo>> listNames(int puid, Integer shopId, String campaignId, String adaptType);

    /**
     * 获取广告组表现
     * @param puid:
     * @param param:
     * @return :
     */
    Result<AdPerformanceVo> showGroupPerformance(int puid, AdPerformanceParam param);

    // 组装接口数据
    GroupEntityV3 makeAdGroupByAdGroupPo(AmazonAdGroup amazonAdGroup);

    Result batchUpdateAdGroup(List<SPadGroupVo> vos, Integer puid, Integer uid, Integer shopId, String loginIp, String type);

    AllGroupAggregateDataResponse.GroupHomeVo getWxAllGroupAggregateDataNew(Integer puid, GroupPageParam param);
    AllGroupAggregateDataResponse.GroupHomeVo getOldWxAllGroupAggregateData(Integer puid, GroupPageParam param);

    AllGroupDataResponse.GroupHomeVo getAllWxGroupData(Integer puid, GroupPageParam param);

    List<AdReportHourlyVO> getAdGroupDailyReport(int puid, AdHourReportRequest param, boolean isCompare);

    GroupPageMetricVo<GroupInfoPageVo> getAllSpGroupPageNoFilterAndOrder(Integer puid, GroupPageParam param, boolean isExport);

    GroupPageVo getInfo(Integer puid, Integer shopId,
                        String campaignId, String adGroupId,
                        String type);

    Page<MultiShopGroupListVo> getMultiShopGroupList(MultiShopGroupListParam param);

    List<MultiShopGroupListVo> getByShopGroupIdPair(Integer puid, List<MultiShopGroupListParam> paramList);

    void saveDoris(List<AmazonAdGroup> amazonAdGroups, boolean create, boolean update);

    List<AmazonAdGroupDorisAllReport> getGroupClickData(MultiShopGroupByProductParam param, List<OdsAmazonAdProduct> productGroup);

    /**
     * 根据活动id获取广告组投放类型
     */
    List<String> getAdGroupTypeByCampaignId(Integer puid, Integer shopId, String campaignId);

    /**
     * 复制广告活动-根据活动id获取广告组广告产品列表
     */
    GroupProductVo getGroupProductList(Integer puid, Integer sourceShopId, Integer targetShopId, String campaignId);
}
