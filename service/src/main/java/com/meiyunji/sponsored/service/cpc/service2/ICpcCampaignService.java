package com.meiyunji.sponsored.service.cpc.service2;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.permission.annotation.AdProductPermissionFilter;
import com.meiyunji.sponsored.common.permission.enums.PermissionFilterStrategy;
import com.meiyunji.sponsored.common.permission.enums.PermissionFilterType;
import com.meiyunji.sponsored.rpc.adCommon.*;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dto.SyncBasicDto;
import com.meiyunji.sponsored.service.cpc.vo.MultiShopCampaignListParam;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.monitor.SaveMonitor;
import com.meiyunji.sponsored.service.monitor.enums.MonitorPageFunctionEnum;
import com.meiyunji.sponsored.service.monitor.enums.MonitorTypeEnum;
import io.grpc.stub.StreamObserver;


import java.util.List;

/**
 * Created by xp on 2021/3/29.
 * 广告活动业务
 */
public interface ICpcCampaignService {


    /**
     * 所有广告活动数据(包含sp sb sd)
     * @param puid ：
     * @param param ：
     * @return ：
     */
    AllCampaignDataResponse.CampaignHomeVo getOldCampaignData(Integer puid, CampaignPageParam param);

    AllCampaignDataResponse.CampaignHomeVo getDorisCampaignData(Integer puid, CampaignPageParam param);

    List<CampaignPageVo> getDorisCampaignDataExport(Integer puid, CampaignPageParam param);

    AllCampaignDataResponse.CampaignHomeVo getCampaignListPage(Integer puid, CampaignPageParam param);

    AllCampaignAggregateDataResponse.CampaignHomeVo getOldAllCampaignAggregateData(Integer puid, CampaignPageParam param);
    AllCampaignAggregateDataResponse.CampaignHomeVo getAllCampaignAggregateData(Integer puid, CampaignPageParam param);
    AllCampaignAggregateDataResponse.CampaignHomeVo getDorisAllCampaignAggregateData(Integer puid, CampaignPageParam param);

    void getAllCampaignAggregateHour(Integer puid, GetCampaignHourReportRequest request,  StreamObserver<GetCampaignHourReportResponse> responseObserver);

    /**
     * 查询所有广告位(不分页)
     * @param puid
     * @param param
     * @return
     */
    List<PlacementPageVo> getAllPlacementVoList(int puid, PlacementPageParam param, ShopAuth shopAuth);

    /**
     * 获取未分页前活动数据(包含sp sb sd)
     * @param puid
     * @param param
     * @return
     */
    List<CampaignPageVo> getExportAllCampaignVoList(Integer puid, CampaignPageParam param);


    List<CampaignPageVo> getOldAllCampaignVoPageList(Integer puid, CampaignPageParam param, Page<CampaignPageVo> voPage, boolean isExport);
    List<CampaignPageVo> getAllCampaignVoPageList(Integer puid, CampaignPageParam param, Page<CampaignPageVo> voPage, boolean isExport);

    /**
     * 手动同步广告活动数据
     * @param puid
     * @param shopId
     * @param campaignIds
     * @return
     */
    boolean syncCampaign(int puid, Integer shopId, List<SyncCampaignsRequest.CampaignId> campaignIds);

    /**
     * 同步基础信息
     * @param puid
     * @param shopId
     * @param campaignIds
     * @return
     */
    Result<String> syncBasicInfo(int puid, Integer shopId, List<SyncCampaignsRequest.CampaignId> campaignIds);

    /**
     * 多店铺同步服务状态
     * @param puid
     * @param campaignIds
     * @return
     */
    Result<String> syncMultipleBasicInfo(int puid, List<SyncBasicDto> campaignIds);

    /**
     * 自动化规则执行成功后同步广告活动数据
     * @param puid
     * @param shopId
     * @param type
     * @param campaignId
     * @return
     */
    boolean autoRuleSyncCampaign(int puid, Integer shopId,String type,String campaignId);

    /**
     * 自动化规则执行成功后同步广告组数据
     * @param puid
     * @param shopId
     * @param type
     * @param campaignId
     * @param adGroupId
     * @return
     */
    boolean autoRuleSyncAdGroup(int puid, Integer shopId,String type,String campaignId,String adGroupId);

    /**
     * 自动化规则执行成功后同步关键词数据
     * @param puid
     * @param shopId
     * @param type
     * @param keywordId
     * @return
     */
    boolean autoRuleSyncKeyword(int puid, Integer shopId,String type,String keywordId);

    boolean autoRuleSyncTarget(int puid, Integer shopId,String type,String targetId);

    /**
     * 广告位首页数据(分页,拆线图,指标数据)
     * @param puid
     * @param param
     * @return
     */
    AllPlacementDataResponse.AdPlacementHomeVo getAllPlacementData(int puid, PlacementPageParam param);

    /**
     * 广告位首页数据 doris
     * @param puid
     * @param param
     * @return
     */
    AllPlacementDataResponse.AdPlacementHomeVo getAllDorisPlacementData(int puid, PlacementPageParam param);

    Page<PlacementPageVo> getAllDorisPlacementVoList(Integer puid, PlacementPageParam param, ShopAuth shopAuth);

    AllPlacementAggregateDataResponse.AdPlacementHomeVo getAllPlacementAggregateData(Integer puid, PlacementPageParam param);

    AllPlacementAggregateDataResponse.AdPlacementHomeVo getAllDorisPlacementAggregateData(Integer puid, PlacementPageParam param);

    List<CampaignPageVo> getOldCampaignVoList(Integer puid, CampaignPageParam param, Page<CampaignPageVo> voPage, boolean isExport);
//    List<CampaignPageVo> getAllCampaignVoList(Integer puid, CampaignPageParam param, Page<CampaignPageVo> voPage, boolean isExport);

    boolean syncShop(int puid, Integer shopId, String type);

    AllCampaignDataResponse.CampaignHomeVo getAllWxCampaignData(Integer puid, CampaignPageParam param);

    AllCampaignAggregateDataResponse.CampaignHomeVo getOldAllWxCampaignAggregateData(Integer puid, CampaignPageParam param);

    AllCampaignAggregateDataResponse.CampaignHomeVo getAllWxCampaignAggregateData(Integer puid, CampaignPageParam param);

    /**
     * 根据店铺id同步店铺广告数据
     * @param puid
     * @param shopId
     */
    void syncAmazonAdByShopId(int puid, int shopId, String uuid);

    Page<AmazonAdCampaignAll> getMultiShopCampaignListDoris(MultiShopCampaignListParam param);

    CampaignPageMetricVo<CampaignInfoPageVo> getAllCampaignPageNoFilterAndOrder(Integer puid, CampaignPageParam param, boolean isExport);

    AmazonAdCampaignAll getCampaignInfo(Integer puid, Integer shopId, String campaignId);

    Page<AmazonAdCampaignAll> getMultiShopCampaignList(MultiShopCampaignListParam param);
}
