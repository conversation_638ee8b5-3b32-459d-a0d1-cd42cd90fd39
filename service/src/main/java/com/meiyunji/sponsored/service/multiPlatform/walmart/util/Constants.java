package com.meiyunji.sponsored.service.multiPlatform.walmart.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description walmart广告相关的常量
 */
public class Constants {
    //开放广告模块的站点
    public static final Set<String> WALMART_AD_MARKETPLACEID = Sets.newHashSet(
            "US"
    );

    //活动定向策略
    public static final String TARGETING_TYPE_AUTO = "auto";//自动
    public static final String TARGETING_TYPE_MANUAL = "manual";//手动

    //活动类型
    public static final String CAMPAIGN_TYPE_SPONSOREDPRODUCTS = "sponsoredProducts";
    public static final String CAMPAIGN_TYPE_SBA = "sba";

    //api访问权限类型
    public static final String API_ACCESS_TYPE_READ = "Read";
    public static final String API_ACCESS_TYPE_WRITE = "Write";

    //广告组、产品状态
    public static final String STATUS_ENABLED = "enabled";//开启
    public static final String STATUS_DISABLED = "disabled";//关闭
    public static final String STATUS_DELETED = "deleted";//删除


    //关键字状态
    public static final String KEYWORD_STATE_ENABLED = "enabled"; //开启
    public static final String KEYWORD_STATE_PAUSED = "paused"; //暂停
    public static final String KEYWORD_STATE_ARCHIVED = "archived";//归档

    //关键字匹配类型
    public static final String MATCH_TYPE_EXACT = "exact";
    public static final String MATCH_TYPE_PHRASE = "phrase";
    public static final String MATCH_TYPE_BROAD = "broad";

    //广告位状态
    public static final String STATUS_EXCLUDED = "excluded";
    public static final String STATUS_INCLUDED = "included";
    //广告位
    public static final String PLACEMENT_SEARCH_INGRID = "Search Ingrid";
    public static final String PLACEMENT_SEARCH_CAROUSEL = "Search Carousel";
    public static final String PLACEMENT_ITEM_BUYBOX = "Item Buybox";
    public static final String PLACEMENT_ITEM_CAROUSEL = "Item Carousel";
    //广告位报告
    public static final String PLACEMENT_REPORT_SEARCH_INGRID = "Search In-grid";
    public static final String PLACEMENT_REPORT_CAROUSEL = "Carousel";
    public static final String PLACEMENT_REPORT_BUYBOX = "Buybox";
    public static final String PLACEMENT_REPORT_OTHERS = "others";
    public static final String PLACEMENT_REPORT_STOCK_UP = "Stock Up";
    public static final String PLACEMENT_REPORT_HOME_PAGE = "Home Page";

    //广告位与报告位对应关系
    public static Map<String, String> PLACEMENT_REPORT_RELATION = new HashMap<>();
    static {
        PLACEMENT_REPORT_RELATION.put(PLACEMENT_SEARCH_INGRID, PLACEMENT_REPORT_SEARCH_INGRID);
        PLACEMENT_REPORT_RELATION.put(PLACEMENT_SEARCH_CAROUSEL, PLACEMENT_REPORT_CAROUSEL);
        PLACEMENT_REPORT_RELATION.put(PLACEMENT_ITEM_BUYBOX, PLACEMENT_REPORT_BUYBOX);
        PLACEMENT_REPORT_RELATION.put(PLACEMENT_ITEM_CAROUSEL, PLACEMENT_REPORT_CAROUSEL);
    }

    //广告位竞价乘数
    public static final String PLACEMENT_MULTIPLIER_BUYBOX = "Buy-Box";
    public static final String PLACEMENT_MULTIPLIER_SEARCH_INGRID = "Search Ingrid";
    public static final String PLACEMENT_MULTIPLIER_STOCK_UP = "Stock Up";
    public static final String PLACEMENT_MULTIPLIER_HOME_PAGE = "Home Page";

    //广告位报告与竞价乘数对应关系
    public static Map<String, String> PLACEMENT_REPORT_MULIPLIER_RELATION = new HashMap<>();
    static {
        PLACEMENT_REPORT_MULIPLIER_RELATION.put(PLACEMENT_MULTIPLIER_BUYBOX, PLACEMENT_REPORT_BUYBOX);
        PLACEMENT_REPORT_MULIPLIER_RELATION.put(PLACEMENT_MULTIPLIER_SEARCH_INGRID, PLACEMENT_REPORT_SEARCH_INGRID);
        PLACEMENT_REPORT_MULIPLIER_RELATION.put(PLACEMENT_MULTIPLIER_STOCK_UP, PLACEMENT_REPORT_STOCK_UP);
        PLACEMENT_REPORT_MULIPLIER_RELATION.put(PLACEMENT_MULTIPLIER_HOME_PAGE, PLACEMENT_REPORT_HOME_PAGE);
    }

    //广告位竞价乘数与报告对应关系
    public static Map<String, String> PLACEMENT_MULIPLIER_REPORT_RELATION = new HashMap<>();
    static {
        PLACEMENT_MULIPLIER_REPORT_RELATION.put(PLACEMENT_REPORT_BUYBOX, PLACEMENT_MULTIPLIER_BUYBOX);
        PLACEMENT_MULIPLIER_REPORT_RELATION.put(PLACEMENT_REPORT_SEARCH_INGRID, PLACEMENT_MULTIPLIER_SEARCH_INGRID);
        PLACEMENT_MULIPLIER_REPORT_RELATION.put(PLACEMENT_REPORT_STOCK_UP, PLACEMENT_MULTIPLIER_STOCK_UP);
        PLACEMENT_MULIPLIER_REPORT_RELATION.put(PLACEMENT_REPORT_HOME_PAGE, PLACEMENT_MULTIPLIER_HOME_PAGE);
    }

    //同步广告活动产品高级洞察数据列
    public static final List<String> DATA_COLUMNS = Lists.newArrayList();
    static {
        DATA_COLUMNS.add("reportDate");
        DATA_COLUMNS.add("catalogItemId");
        DATA_COLUMNS.add("availabilityStatus");
        DATA_COLUMNS.add("averageRating");
        DATA_COLUMNS.add("buyboxWinnerPrice");
        DATA_COLUMNS.add("pageViewBasedAvailabilityRate");
        DATA_COLUMNS.add("pageViewBasedBuyboxWinRate");
        DATA_COLUMNS.add("reviewCount");
        DATA_COLUMNS.add("itemImageUrl");
    }

    //广告快照推荐类型
    public static final String RECOMMENDATION_TYPE_ITEMRECOMMENDATIONS = "itemRecommendations";
    public static final String RECOMMENDATION_TYPE_KEYWORDRECOMMENDATIONS = "keywordRecommendations";
        public static final String RECOMMENDATION_TYPE_MATCHTYPEKEYWORDRECOMMENDATIONS = "matchTypeKeywordRecommendations";

    //快照队列类型
    public static final Integer SNAPSHORT_TYPE_ATTRIBUTE = 1;     //用户属性
    public static final Integer SNAPSHORT_TYPE_KEYWORD_RECOMMENDATIONS = 2;//关键词推荐
    public static final Integer SNAPSHORT_TYPE_ITEM_RECOMMENDATIONS = 3;//产品推荐
    public static final Integer SNAPSHORT_TYPE_ITEM_INSIGHTS = 4;//产品高级洞察
    public static final Integer SNAPSHORT_TYPE_CAMPAIGN_ENTITY = 5;//活动实体数据
    public static final Integer SNAPSHORT_TYPE_REPORT_GROUP = 6;//分组报告
    public static final Integer SNAPSHORT_TYPE_REPORT_ITEM = 7;//产品报告
    public static final Integer SNAPSHORT_TYPE_REPORT_KEYWORD = 8;//关键词报告
    public static final Integer SNAPSHORT_TYPE_REPORT_PLACEMENT = 9;//广告位报告
    public static final Integer SNAPSHORT_TYPE_REPORT_OUT = 10;//超预算报告
    public static final Integer SNAPSHORT_TYPE_SEARCH_IMPRESSION = 11;//搜索词报告
    //快照状态
    public static final Integer STATE_PENDING = 0;
    public static final Integer STATE_PROCESS = 1;
    public static final Integer STATE_FINISH = 2;

    //广告活动更新状态
    public static final String UPDATE_STATUS_ENABLED = "enabled";//开启
    public static final String UPDATE_STATUS_PAUSED = "paused";//暂停
    public static final String UPDATE_STATUS_COMPLETED = "completed";//完成
    public static final String UPDATE_STATUS_EXTEND = "extend";//延期
    public static final String UPDATE_STATUS_PROPOSAL = "proposal";//建议

    public static final String UPDATE_STATUS_DISABLED = "disabled";//禁用

    //广告组，广告产品归档状态
    public static final String UPDATE_STATUS_DELETE = "deleted";//归档


    //活动实体快照状态
    public static final String ENTITY_STATUS_ALL = "all";//全部数据
    public static final String ENTITY_STATUS_ENABLED = "enabled";//开启
    public static final String ENTITY_STATUS_DISABLED = "disabled";//关闭

    //实体类型全部
    public static final List<String> ENTITY_TYPES_ALL = Lists.newArrayList();
    static {
        ENTITY_TYPES_ALL.add("campaign");
        ENTITY_TYPES_ALL.add("adGroup");
        ENTITY_TYPES_ALL.add("keyword");
        ENTITY_TYPES_ALL.add("adItem");
        ENTITY_TYPES_ALL.add("bidMultiplier");
        ENTITY_TYPES_ALL.add("placement");
    }
    //报告维度
    public static final String REPORT_TYPE_GROUP = "adGroup";//广告组维度
    public static final String REPORT_TYPE_ITEM = "adItem";//广告产品维度
    public static final String REPORT_TYPE_KEYWORD = "keyword";//广告关键词维度
    public static final String REPORT_TYPE_PLACEMENT = "placement";//广告位维度
    public static final String REPORT_TYPE_OUT = "outOfBudgetRecommendations";//超预算建议维度
    public static final String REPORT_SEARCH_IMPRESSION = "searchImpression";//搜索词维度
    public static final String AD_CAMPAIGN = "广告活动";
    public static final String AD_CAMPAIGN_NAME = "广告活动名称";
    public static final String AD_CAMPAIGN_ADTYPE = "广告活动类型";
    public static final String AD_CAMPAIGN_START_TIME = "开始时间";
    public static final String AD_CAMPAIGN_TOTAL_BUDGET = "总预算";
    public static final String AD_CAMPAIGN_DAILY_BUDGET = "每日预算";

    public static final String AD_GROUP = "广告组";
    public static final String AD_GROUP_NAME = "广告组名称";
    public static final String AD_ITEM = "广告组产品";
    public static final String AD_KEYWORD = "关键词投放";
    public static final String AD_SUGGEST_KEYWORD = "建议关键词";
    public static final String AD_DEFAULT = "-";
    public static final String AD_BID = "竞价";


    public static final List<String> REPORT_METRICS_BASE = Lists.newArrayList();
    static {
        REPORT_METRICS_BASE.add("adSpend");
        REPORT_METRICS_BASE.add("numAdsClicks");
        REPORT_METRICS_BASE.add("numAdsShown");
        REPORT_METRICS_BASE.add("advertisedSkuSales3days");
        REPORT_METRICS_BASE.add("advertisedSkuSales14days");
        REPORT_METRICS_BASE.add("advertisedSkuSales30days");
        REPORT_METRICS_BASE.add("advertisedSkuUnits3days");
        REPORT_METRICS_BASE.add("advertisedSkuUnits14days");
        REPORT_METRICS_BASE.add("advertisedSkuUnits30days");
        REPORT_METRICS_BASE.add("attributedOrders3days");
        REPORT_METRICS_BASE.add("attributedOrders14days");
        REPORT_METRICS_BASE.add("attributedOrders30days");
        REPORT_METRICS_BASE.add("attributedUnits3days");
        REPORT_METRICS_BASE.add("attributedUnits14days");
        REPORT_METRICS_BASE.add("attributedUnits30days");
        REPORT_METRICS_BASE.add("attributedSales3days");
        REPORT_METRICS_BASE.add("attributedSales14days");
        REPORT_METRICS_BASE.add("attributedSales30days");
        REPORT_METRICS_BASE.add("otherSkuSales3days");
        REPORT_METRICS_BASE.add("otherSkuSales14days");
        REPORT_METRICS_BASE.add("otherSkuSales30days");
        REPORT_METRICS_BASE.add("otherSkuUnits3days");
        REPORT_METRICS_BASE.add("otherSkuUnits14days");
        REPORT_METRICS_BASE.add("otherSkuUnits30days");
    }
    public static final List<String> REPORT_METRICS_IN_STORE_BASE = Lists.newArrayList();
    static {
        REPORT_METRICS_IN_STORE_BASE.add("inStoreAdvertisedSales3days");
        REPORT_METRICS_IN_STORE_BASE.add("inStoreAdvertisedSales14days");
        REPORT_METRICS_IN_STORE_BASE.add("inStoreAdvertisedSales30days");
        REPORT_METRICS_IN_STORE_BASE.add("inStoreAttributedSales3days");
        REPORT_METRICS_IN_STORE_BASE.add("inStoreAttributedSales14days");
        REPORT_METRICS_IN_STORE_BASE.add("inStoreAttributedSales30days");
        REPORT_METRICS_IN_STORE_BASE.add("inStoreOrders3days");
        REPORT_METRICS_IN_STORE_BASE.add("inStoreOrders14days");
        REPORT_METRICS_IN_STORE_BASE.add("inStoreOrders30days");
        REPORT_METRICS_IN_STORE_BASE.add("inStoreOtherSales3days");
        REPORT_METRICS_IN_STORE_BASE.add("inStoreOtherSales14days");
        REPORT_METRICS_IN_STORE_BASE.add("inStoreOtherSales30days");
        REPORT_METRICS_IN_STORE_BASE.add("inStoreUnitsSold3days");
        REPORT_METRICS_IN_STORE_BASE.add("inStoreUnitsSold14days");
        REPORT_METRICS_IN_STORE_BASE.add("inStoreUnitsSold30days");
    }

    //报告度量
    public static final List<String> REPORT_METRICS_GROUP = Lists.newArrayList();
    static {
        REPORT_METRICS_GROUP.add("date");
        REPORT_METRICS_GROUP.add("campaignId");
        REPORT_METRICS_GROUP.add("adGroupId");
        REPORT_METRICS_GROUP.addAll(REPORT_METRICS_BASE);
    }
    //报告度量
    public static final List<String> REPORT_METRICS_ITEM = Lists.newArrayList();
    static {
        REPORT_METRICS_ITEM.add("date");
        REPORT_METRICS_ITEM.add("campaignId");
        REPORT_METRICS_ITEM.add("adGroupId");
        REPORT_METRICS_ITEM.add("itemId");
        REPORT_METRICS_ITEM.addAll(REPORT_METRICS_BASE);
        REPORT_METRICS_ITEM.addAll(REPORT_METRICS_IN_STORE_BASE);
    }
    //报告度量
    public static final List<String> REPORT_METRICS_KEYWORD = Lists.newArrayList();
    static {
        REPORT_METRICS_KEYWORD.add("date");
        REPORT_METRICS_KEYWORD.add("campaignId");
        REPORT_METRICS_KEYWORD.add("adGroupId");
        REPORT_METRICS_KEYWORD.add("keywordId");
        REPORT_METRICS_KEYWORD.addAll(REPORT_METRICS_BASE);
        REPORT_METRICS_KEYWORD.addAll(REPORT_METRICS_IN_STORE_BASE);
    }
    //报告度量
    public static final List<String> REPORT_METRICS_PLACEMENT = Lists.newArrayList();
    static {
        REPORT_METRICS_PLACEMENT.add("date");
        REPORT_METRICS_PLACEMENT.add("campaignId");
        REPORT_METRICS_PLACEMENT.add("placement");
        REPORT_METRICS_PLACEMENT.addAll(REPORT_METRICS_BASE);
        REPORT_METRICS_PLACEMENT.addAll(REPORT_METRICS_IN_STORE_BASE);
    }

    //报告度量
    public static final List<String> REPORT_METRICS_OUT = Lists.newArrayList();
    static {
        REPORT_METRICS_OUT.add("startDate");
        REPORT_METRICS_OUT.add("endDate");
        REPORT_METRICS_OUT.add("campaignId");
        REPORT_METRICS_OUT.add("suggestedLatestDailyBudget");
        REPORT_METRICS_OUT.add("suggestedLatestTotalBudget");
    }


    //搜索词
    public static final List<String> REPORT_METRICS_SEARCH = Lists.newArrayList();
    static {
        REPORT_METRICS_SEARCH.addAll(Lists.newArrayList(  "date", "searchedKeyword", "campaignId", "numAdsShown", "searchedKeywordImpressionShare", "searchedKeywordImpressionRank", "tosSearchedKeywordImpressionShare", "tosSearchedKeywordImpressionRank", "numAdsClicks", "adSpend", "attributedOrders3days", "attributedOrders14days", "attributedOrders30days", "advertisedSkuSales3days", "advertisedSkuSales14days", "advertisedSkuSales30days", "otherSkuSales3days", "otherSkuSales14days", "otherSkuSales30days", "advertisedSkuUnits3days", "advertisedSkuUnits14days", "advertisedSkuUnits30days", "otherSkuUnits3days", "otherSkuUnits14days", "otherSkuUnits30days"));
    }


}
