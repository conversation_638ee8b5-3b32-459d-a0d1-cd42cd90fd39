package com.meiyunji.sponsored.service.newDashboard.service.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdCampaignOrGroupOrPortfolioResponseVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdPortfolioDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolio;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdPortfolioDao;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.newDashboard.dto.CampaignOrGroupOrPortfolioDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTopDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByRateEnum;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdPortfolioService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.newDashboard.util.OrderByUtil;
import com.meiyunji.sponsored.service.newDashboard.util.PageUtils;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardCampaignOrGroupOrPortfolioReqVo;
import com.meiyunji.sponsored.service.util.SummaryReportUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: ys
 * @date: 2024/4/15 14:41
 * @describe: 广告组合图表Service实现类
 */
@Service
@Slf4j
public class DashboardAdPortfolioServiceImpl implements IDashboardAdPortfolioService {

    @Autowired
    private IOdsAmazonAdPortfolioDao odsAmazonAdPortfolioDao;

    @Autowired
    private IAmazonAdPortfolioDao amazonAdPortfolioDao;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IExcelService excelService;
    @Override
    public DashboardAdCampaignOrGroupOrPortfolioResponseVo.Page queryPortfolioCharts(DashboardCampaignOrGroupOrPortfolioReqVo req) {
        List<CampaignOrGroupOrPortfolioDto> resultList = getPortfolioList(req);
        if (CollectionUtils.isEmpty(resultList)) {
            log.info("get top portfolio chars data list is null, queryField:{}, dataField:{}", req.getQueryField(), req.getDataField());
            return null;
        }
        //组装数据返回
        List<DashboardAdCampaignOrGroupOrPortfolioResponseVo.Page.TopList> topList = resultList.stream().map(d -> {
            DashboardAdCampaignOrGroupOrPortfolioResponseVo.Page.TopList.Builder vo = DashboardAdCampaignOrGroupOrPortfolioResponseVo
                    .Page.TopList.newBuilder();
            BeanUtils.copyProperties(d, vo, ParamCopyUtil.checkPropertiesNullOrEmptySuper(d));
            vo.setCost(CalculateUtil.formatDecimal(d.getCost()));
            Optional.ofNullable(d.getShopId()).map(Integer::valueOf).ifPresent(vo::setShopId);
            Optional.ofNullable(d.getTotalSales()).map(CalculateUtil::formatDecimal).ifPresent(vo::setTotalSales);
            Optional.ofNullable(d.getImpressions()).map(String::valueOf).ifPresent(vo::setImpressions);
            Optional.ofNullable(d.getClicks()).map(String::valueOf).ifPresent(vo::setClicks);
            Optional.ofNullable(d.getOrderNum()).map(String::valueOf).ifPresent(vo::setOrderNum);
            Optional.ofNullable(d.getSaleNum()).map(String::valueOf).ifPresent(vo::setSaleNum);
            Optional.ofNullable(d.getAcos()).map(CalculateUtil::formatPercent).ifPresent(vo::setAcos);
            Optional.ofNullable(d.getRoas()).map(String::valueOf).ifPresent(vo::setRoas);
            Optional.ofNullable(d.getClickRate()).map(CalculateUtil::formatPercent).ifPresent(vo::setClickRate);
            Optional.ofNullable(d.getConversionRate()).map(CalculateUtil::formatPercent).ifPresent(vo::setConversionRate);
            Optional.ofNullable(d.getCpc()).map(CalculateUtil::formatDecimal).ifPresent(vo::setCpc);
            Optional.ofNullable(d.getCpa()).map(CalculateUtil::formatDecimal).ifPresent(vo::setCpa);
            Optional.ofNullable(d.getCostPercent()).ifPresent(vo::setCostPercent);
            Optional.ofNullable(d.getTotalSalesPercent()).ifPresent(vo::setTotalSalesPercent);
            Optional.ofNullable(d.getImpressionsPercent()).ifPresent(vo::setImpressionsPercent);
            Optional.ofNullable(d.getClicksPercent()).ifPresent(vo::setClicksPercent);
            Optional.ofNullable(d.getOrderNumPercent()).ifPresent(vo::setOrderNumPercent);
            Optional.ofNullable(d.getSaleNumPercent()).ifPresent(vo::setSaleNumPercent);
            return vo.build();
        }).collect(Collectors.toList());
        return PageUtils.getPageInfo(topList, req.getPageSize(), req.getPageNo());
    }

    private List<CampaignOrGroupOrPortfolioDto> getPortfolioList(DashboardCampaignOrGroupOrPortfolioReqVo req) {
        Integer puid = req.getPuid();
        List<String> marketplaceIdList = req.getMarketplaceIdList();
        List<Integer> shopIdList = req.getShopIdList();
        String currency = req.getCurrency();
        DashboardOrderByRateEnum orderField = DashboardOrderByRateEnum.rateMap.get(req.getOrderByField());
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(req.getDataField());
        int limit = Optional.ofNullable(req.getLimit()).orElse(0);
        //查询当前日期时间段内广告活动图表信息
        List<Object> argsList = Lists.newArrayList();
        //当前时间段查询sql
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(req.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(marketplaceIdList);
        }
        String subSqlA = odsAmazonAdPortfolioDao.
                portfolioQuerySql(puid, shopIdList, marketplaceIdList, null, currency, req.getStartDate(),
                        req.getEndDate(), argsList, siteToday, req.getSiteToday(), req.getPortfolioIds(), req.getCampaignIds(), req.getNoZero(), dataField);
        String subSqlB;
        List<DashboardAdTopDataDto> currentAndSubList;
        List<CampaignOrGroupOrPortfolioDto> resultList = Lists.newArrayList();
        //如果是按占比排序，即当前时间段值/汇总值,先计算同比,或按同比排序，即当前时间段值/同比时间段值
        if (DashboardOrderByRateEnum.PERCENT == orderField || Stream.of(DashboardOrderByRateEnum.YOY_RATE,
                DashboardOrderByRateEnum.YOY_VALUE).anyMatch(r -> r == orderField)) {
            //同比sql
            subSqlB = odsAmazonAdPortfolioDao
                    .portfolioQuerySql(puid, shopIdList, marketplaceIdList, null, currency, req.getYoyStartDate(),
                            req.getYoyEndDate(), argsList, null, null, req.getPortfolioIds(), req.getCampaignIds(), null, null);
            currentAndSubList = odsAmazonAdPortfolioDao.queryAdPortfolioYoyOrMomTop(subSqlA, subSqlB,
                    argsList, dataField, orderField, req.getOrderBy(), limit, req.getNoZero());
            List<String> portfolioIdList = currentAndSubList.parallelStream()
                    .map(DashboardAdTopDataDto::getPortfolioId).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(portfolioIdList)) {
                //还需要查询环比
                List<CampaignOrGroupOrPortfolioDto> momPortfolioList = odsAmazonAdPortfolioDao
                        .queryAdPortfolioCharts(puid, shopIdList, marketplaceIdList, portfolioIdList, currency, req.getMomStartDate(),
                                req.getMomEndDate());
                Map<String, CampaignOrGroupOrPortfolioDto> momPortfolioMap = momPortfolioList.parallelStream()
                        .collect(Collectors.toMap(CampaignOrGroupOrPortfolioDto::getPortfolioId, v1 -> v1, (old, current) -> current));
                resultList = currentAndSubList.stream().map(c -> {
                    //当前查询list已经包含了当期及同比的计算属性和汇总属性
                    //还需要计算环比占比，同比占比
                    CampaignOrGroupOrPortfolioDto momPortfolio = Optional.ofNullable(momPortfolioMap.get(c.getPortfolioId())).orElseGet(() -> {
                        CampaignOrGroupOrPortfolioDto tempDto = new CampaignOrGroupOrPortfolioDto();
                        CalculateAdDataUtil.buildZeroAdCalData(tempDto);
                        return tempDto;
                    });
                    CampaignOrGroupOrPortfolioDto currentDto = new CampaignOrGroupOrPortfolioDto();
                    BeanUtils.copyProperties(c, currentDto, ParamCopyUtil.checkPropertiesNullOrEmptySuper(c));
                    CampaignOrGroupOrPortfolioDto yoyDto = convertBasicAndCalData(c);
                    CampaignOrGroupOrPortfolioDto summaryDto = convertSummaryData(c);
                    computeCampaignData(currentDto, yoyDto, momPortfolio, summaryDto, req.getYoyOverLimit());
                    return currentDto;
                }).collect(Collectors.toList());
            }
        }

        //如果是按环比排序，即当前时间段值/环比时间段值
        if (Stream.of(DashboardOrderByRateEnum.MOM_RATE,
                DashboardOrderByRateEnum.MOM_VALUE).anyMatch(r -> r == orderField)) {
            subSqlB = odsAmazonAdPortfolioDao
                    .portfolioQuerySql(puid, shopIdList, marketplaceIdList, null, currency, req.getMomStartDate(),
                            req.getMomEndDate(), argsList, null, null, req.getPortfolioIds(), req.getCampaignIds(), null, null);
            currentAndSubList = odsAmazonAdPortfolioDao.queryAdPortfolioYoyOrMomTop(subSqlA, subSqlB,
                    argsList, dataField, orderField, req.getOrderBy(), limit, req.getNoZero());
            List<String> portfolioIdList = currentAndSubList.parallelStream().map(DashboardAdTopDataDto::getPortfolioId)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            //还需要查同比
            List<CampaignOrGroupOrPortfolioDto> yoyList = odsAmazonAdPortfolioDao
                    .queryAdPortfolioCharts(puid, shopIdList, marketplaceIdList, portfolioIdList, currency,
                            req.getYoyStartDate(), req.getYoyEndDate());
            Map<String, CampaignOrGroupOrPortfolioDto> yoyMap = yoyList.parallelStream()
                    .collect(Collectors.toMap(CampaignOrGroupOrPortfolioDto::getPortfolioId, v1 -> v1, (oldVal, newVal) -> newVal));
            resultList = currentAndSubList.stream().filter(c -> Objects.nonNull(c.getPortfolioId())).map(c -> {
                CampaignOrGroupOrPortfolioDto yoyPortfolio = Optional.ofNullable(yoyMap.get(c.getPortfolioId())).orElseGet(() -> {
                    CampaignOrGroupOrPortfolioDto tempDto = new CampaignOrGroupOrPortfolioDto();
                    CalculateAdDataUtil.buildZeroAdCalData(tempDto);
                    return tempDto;
                });
                CampaignOrGroupOrPortfolioDto currentDto = new CampaignOrGroupOrPortfolioDto();
                BeanUtils.copyProperties(c, currentDto, ParamCopyUtil.checkPropertiesNullOrEmptySuper(c));
                CampaignOrGroupOrPortfolioDto monDto = convertBasicAndCalData(c);
                CampaignOrGroupOrPortfolioDto summaryDto = convertSummaryData(c);
                computeCampaignData(currentDto, yoyPortfolio, monDto, summaryDto, req.getYoyOverLimit());
                return currentDto;
            }).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }
        //统一添加广告组合名称
        List<String> portfolioIdList = resultList.parallelStream().map(CampaignOrGroupOrPortfolioDto::getPortfolioId).collect(Collectors.toList());
        List<AmazonAdPortfolio> portfolioList = amazonAdPortfolioDao.listByShopId(req.getPuid(), req.getShopIdList(), portfolioIdList);
        if (CollectionUtils.isEmpty(portfolioList)) {
            return Collections.emptyList();
        }
        Map<String, AmazonAdPortfolio> portfolioInfoMap = portfolioList.parallelStream().collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, v1 -> v1, (oldVal, newVal) -> newVal));
        //填充结果中的店铺名称和站点名称
        Set<Integer> shopIds = portfolioInfoMap.values().parallelStream().map(AmazonAdPortfolio::getShopId).collect(Collectors.toSet());
        List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, new ArrayList<>(shopIds));
        Map<Integer, String> shopNameMap = shopAuthList.parallelStream().collect(Collectors.toMap(ShopAuth::getId, ShopAuth::getName));
        resultList.forEach(r -> {
            AmazonAdPortfolio info = portfolioInfoMap.get(r.getPortfolioId());
            if (Objects.nonNull(info)) {
                Optional.ofNullable(shopNameMap.get(info.getShopId())).ifPresent(r::setShopName);
                Optional.ofNullable(info.getShopId()).map(String::valueOf).ifPresent(r::setShopId);
                Optional.ofNullable(SummaryReportUtil.getByMarketplaceId(info.getMarketplaceId()))
                        .map(SummaryReportUtil::getMarketplaceCN).ifPresent(r::setMarketplaceName);
                Optional.ofNullable(info.getMarketplaceId()).ifPresent(r::setMarketplaceId);

                Optional.ofNullable(info.getName()).ifPresent(r::setPortfolioName);
            }
        });

        //是否需要排序
        if (StringUtils.isNotBlank(req.getListOrderField()) || StringUtils.isNotBlank(req.getListOrderType())) {
            OrderByUtil.sortedByOrderField(resultList, req.getListOrderField(), req.getListOrderType(), "portfolioId");
        }

        return resultList;
    }

    @Override
    public List<String> exportPortfolioCharts(DashboardCampaignOrGroupOrPortfolioReqVo reqVo) {
        List<CampaignOrGroupOrPortfolioDto> portfolioList = getPortfolioList(reqVo);
        String url = writeExcelAndUpload(portfolioList, reqVo);
        if (StringUtils.isBlank(url)) {
            return null;
        }
        log.info("dashboard export portfolio charts, puid: {}, url: {}", reqVo.getPuid(), url);
        return Collections.singletonList(url);
    }

    private String writeExcelAndUpload(List<CampaignOrGroupOrPortfolioDto> portfolioList, DashboardCampaignOrGroupOrPortfolioReqVo reqVo) {
        portfolioList.forEach(dataDto -> CalculateAdDataUtil.fillDisplay4Export(dataDto, reqVo.getCurrency()));
        List<String> headers = new ArrayList<>(baseHeaderList);
        //计算导出列
        if (reqVo.getPercent() != 1) {
            headers = headers.stream().filter(x -> !x.contains("Percent")).collect(Collectors.toList());
        }
        if (reqVo.getMom() != 1) {
            headers = headers.stream().filter(x -> !x.contains("MomRate")).collect(Collectors.toList());
        }
        if (reqVo.getYoy() != 1) {
            headers = headers.stream().filter(x -> !x.contains("YoyRate")).collect(Collectors.toList());
        }

        String orderBy = Optional.ofNullable(DashboardOrderByEnum.orderByMap.get(reqVo.getOrderBy()))
                .map(DashboardOrderByEnum::getExportDesc).orElse("");
        String limitCnt = Optional.ofNullable(reqVo.getLimit()).map(String::valueOf).orElse("");
        //写excel并上传
        return excelService.easyExcelHandlerDownload(reqVo.getPuid(), portfolioList, fileName + orderBy + limitCnt,
                CampaignOrGroupOrPortfolioDto.class, headers, true);

    }

    private List<String> baseHeaderList = Arrays.asList("portfolioName","marketplaceName","shopName",
            "displayCost", "costPercent", "costMomRate", "costYoyRate",
            "displayTotalSales", "totalSalesPercent", "totalSalesMomRate", "totalSalesYoyRate",
            "displayAcos", "acosMomRate", "acosYoyRate", "displayRoas", "roasMomRate", "roasYoyRate",
            "impressions", "impressionsPercent", "impressionsMomRate", "impressionsYoyRate",
            "clicks", "clicksPercent", "clicksMomRate", "clicksYoyRate",
            "orderNum", "orderNumPercent", "orderNumMomRate", "orderNumYoyRate",
            "displayClickRate", "clickRateMomRate", "clickRateYoyRate",
            "displayConversionRate", "conversionRateMomRate", "conversionRateYoyRate",
            "saleNum", "saleNumPercent", "saleNumMomRate", "saleNumYoyRate",
            "displayCpc", "cpcMomRate", "cpcYoyRate",
            "displayCpa", "cpaMomRate", "cpaYoyRate");

    private static final String fileName = "广告组合";

    private CampaignOrGroupOrPortfolioDto convertBasicAndCalData(DashboardAdTopDataDto subInfo) {
        CampaignOrGroupOrPortfolioDto dto = new CampaignOrGroupOrPortfolioDto();
        dto.setCost(subInfo.getSubCost());
        dto.setTotalSales(subInfo.getSubTotalSales());
        dto.setImpressions(subInfo.getSubImpressions());
        dto.setClicks(subInfo.getSubClicks());
        dto.setOrderNum(subInfo.getSubOrderNum());
        dto.setSaleNum(subInfo.getSubSaleNum());
        dto.setAcos(subInfo.getSubAcos());
        dto.setRoas(subInfo.getSubRoas());
        dto.setClickRate(subInfo.getSubClickRate());
        dto.setConversionRate(subInfo.getSubConversionRate());
        dto.setCpc(subInfo.getSubCpc());
        dto.setCpa(subInfo.getSubCpa());
        return dto;
    }

    private CampaignOrGroupOrPortfolioDto convertSummaryData(DashboardAdTopDataDto subInfo) {
        CampaignOrGroupOrPortfolioDto dto = new CampaignOrGroupOrPortfolioDto();
        dto.setCost(subInfo.getAllCost());
        dto.setTotalSales(subInfo.getAllTotalSales());
        Optional.ofNullable(subInfo.getAllImpressions()).map(BigDecimal::longValue).ifPresent(dto::setImpressions);
        Optional.ofNullable(subInfo.getAllClicks()).map(BigDecimal::intValue).ifPresent(dto::setClicks);
        Optional.ofNullable(subInfo.getAllOrderNum()).map(BigDecimal::intValue).ifPresent(dto::setOrderNum);
        Optional.ofNullable(subInfo.getAllSaleNum()).map(BigDecimal::intValue).ifPresent(dto::setSaleNum);
        return dto;
    }

    private void computeCampaignData(CampaignOrGroupOrPortfolioDto dto, CampaignOrGroupOrPortfolioDto yoyDto,
                                     CampaignOrGroupOrPortfolioDto momDto, CampaignOrGroupOrPortfolioDto summary, boolean yoyOverLimit) {
        if (Objects.isNull(dto)) {
            return;
        }

        if (yoyOverLimit) {
            CalculateAdDataUtil.calAdYoyData(dto, null);
        }else if (Objects.nonNull(yoyDto)) {
            CalculateAdDataUtil.calAdCalData(yoyDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            CalculateAdDataUtil.calAdYoyValueReflex(dto, yoyDto);//填充同比增长值
            CalculateAdDataUtil.calAdYoyDataReflex(dto, yoyDto);//填充同比增长率
        }
        if (Objects.nonNull(momDto)) {
            CalculateAdDataUtil.calAdCalData(momDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            CalculateAdDataUtil.calAdMomData(dto, momDto);//填充环比增长率
            CalculateAdDataUtil.calAdMomValueReflex(dto, momDto);//填充环比增长值
        }
        CalculateAdDataUtil.calAdPercentData(dto, summary);
    }
}
