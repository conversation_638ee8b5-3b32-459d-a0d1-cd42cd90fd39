package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdNeTargetingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdNeTargeting;
import com.meiyunji.sponsored.service.cpc.po.SdTargetTypeEnum;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.TargetingPageParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 2021/7/6
 */
@Repository
public class AmazonSdAdNeTargetingDaoImpl extends BaseShardingDaoImpl<AmazonSdAdNeTargeting> implements IAmazonSdAdNeTargetingDao {

    private final int imgLimit = 1000;
    private final int titleLimit = 1000;

    @Override
    public void batchAdd(int puid, List<AmazonSdAdNeTargeting> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;
        for (AmazonSdAdNeTargeting po : list) {
            if (po.getPuid() == null || po.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(po.getPuid());
            arg.add(po.getShopId());
            arg.add(po.getMarketplaceId());
            arg.add(po.getProfileId());
            arg.add(po.getAdGroupId());
            arg.add(po.getTargetId());
            arg.add(po.getState());
            arg.add(po.getType());
            arg.add(po.getTargetText());
            arg.add(po.getExpressionType());
            arg.add(po.getExpression());
            arg.add(po.getResolvedExpression());
            arg.add(po.getTitle() == null ? "" : (po.getTitle().length() > titleLimit ? po.getTitle().substring(0, titleLimit) : po.getTitle()));
            arg.add(po.getImgUrl() == null ? "" : (po.getImgUrl().length() > imgLimit ? po.getImgUrl().substring(0, imgLimit) : po.getImgUrl()));
            arg.add(po.getServingStatus());
            arg.add(po.getCreationDate());
            arg.add(po.getLastUpdatedDate());
            arg.add(po.getCreateInAmzup());
            arg.add(po.getCreateId());
            arg.add(po.getUpdateId());
            argList.add(arg.toArray());
        }

        String sql = "insert into t_amazon_ad_netargeting_sd (`puid`,`shop_id`,`marketplace_id`,`profile_id`,`ad_group_id`,`target_id`," +
                "`state`,`type`,`target_text`,`expression_type`,`expression`,`resolved_expression`,`title`,`img_url`," +
                "`serving_status`,`creation_date`,`last_updated_date`,`create_in_amzup`," +
                "`create_id`,`update_id`,`create_time`,`update_time`) values (?,?, ?,?, ?,?, ?,?," +
                "?,?, ?,?, ?,?, ?,?, ?,?, ?,?, now(3), now(3))";

        getJdbcTemplate(puid).batchUpdate(sql, argList);
    }

    @Override
    public void batchUpdate(int puid, List<AmazonSdAdNeTargeting> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;
        for (AmazonSdAdNeTargeting po : list) {
            if (po.getPuid() == null || po.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(po.getState());
            arg.add(po.getType());
            arg.add(po.getTargetText());
            arg.add(po.getExpressionType());
            arg.add(po.getExpression());
            arg.add(po.getResolvedExpression());
            arg.add(po.getServingStatus());
            arg.add(po.getCreationDate());
            arg.add(po.getLastUpdatedDate());
            arg.add(po.getUpdateId());
            arg.add(po.getPuid());
            arg.add(po.getShopId());
            arg.add(po.getTargetId());
            argList.add(arg.toArray());
        }

        String sql = "update t_amazon_ad_netargeting_sd set `state`=?,`type`=?,`target_text`=?,`expression_type`=?," +
                "`expression`=?,`resolved_expression`=?,`serving_status`=?,`creation_date`=?,`last_updated_date`=?," +
                "`update_id`=? " +
                " where puid=? and shop_id=? and target_id=?";

        getJdbcTemplate(puid).batchUpdate(sql, argList);
    }

    @Override
    public List<AmazonSdAdNeTargeting> listByTargetId(int puid, int shopId, List<String> targetIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("target_id", targetIds.toArray())
                .build();

        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonSdAdNeTargeting> listByTargetId(int puid, List<Integer> shopIds, List<String> targetIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopIds.toArray())
                .in("target_id", targetIds.toArray())
                .build();

        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public AmazonSdAdNeTargeting getbyTargetId(Integer puid, Integer shopId, String targetId) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .equalToWithoutCheck("target_id", targetId)
                .build();

        return getByCondition(puid, conditionBuilder);
    }

    @Override
    public Page<AmazonSdAdNeTargeting> pageList(int puid, TargetingPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("ad_group_id", param.getGroupId())
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.archived.name()});

        if (StringUtils.isNotBlank(param.getStatus())) {
            builder.equalToWithoutCheck("state", param.getStatus());
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            builder.like("target_text", param.getSearchValue());
        }

        String orderBySql = " order by create_time desc";

        return page(puid, param.getPageNo(), param.getPageSize(), orderBySql, builder.build());
    }

    @Override
    public List<AmazonSdAdNeTargeting> listNoAsinImage(Integer puid, Integer shopId, long offset, int limit) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .equalToWithoutCheck("type", SdTargetTypeEnum.asin.getValue())
                .appendSql(" and (img_url='' or `title`='')", true);

        builder.greaterThan("id", offset);
        builder.orderBy("id");
        builder.limit(limit);

        return listByCondition(puid, builder.build());
    }

    @Override
    public void batchSetAsinImage(Integer puid, List<AmazonSdAdNeTargeting> needUpdateList) {
        if (CollectionUtils.isNotEmpty(needUpdateList)) {
            String sql = "update t_amazon_ad_netargeting_sd set img_url=?, title=?, update_time=now(3) where id=? and puid=?";
            List<Object[]> argsList = new ArrayList<>();
            List<Object> args;
            for (AmazonSdAdNeTargeting t : needUpdateList) {
                args = new ArrayList<>(4);
                args.add(t.getImgUrl() == null ? ""
                        : (t.getImgUrl().length() > imgLimit ? t.getImgUrl().substring(0, imgLimit) : t.getImgUrl()));
                args.add(t.getTitle() == null ? ""
                        : (t.getTitle().length() > titleLimit ? t.getTitle().substring(0, titleLimit) : t.getTitle()));
                args.add(t.getId());
                args.add(t.getPuid());
                argsList.add(args.toArray());
            }
            getJdbcTemplate(puid).batchUpdate(sql, argsList);
        }
    }

    @Override
    public List<AmazonSdAdNeTargeting> getListTargetByQuery(Integer puid, Integer shopId, String adGroupId, String targetValue, String type) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId)
                .equalTo("type", type)
                .equalTo("target_text", targetValue)
                .in("state", new Object[]{"enabled","paused"})
                .build());
    }
}