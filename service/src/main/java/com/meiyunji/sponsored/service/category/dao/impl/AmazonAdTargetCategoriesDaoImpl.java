package com.meiyunji.sponsored.service.category.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.category.dao.AmazonAdTargetCategoriesDao;
import com.meiyunji.sponsored.service.category.entity.AmazonAdTargetCategories;
import com.meiyunji.sponsored.service.category.vo.TargetCategoryParam;
import com.meiyunji.sponsored.service.category.vo.TargetCategoryVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AmazonAdTargetCategoriesDaoImpl extends AdBaseDaoImpl<AmazonAdTargetCategories> implements AmazonAdTargetCategoriesDao {
    @Override
    public void batchAdd(List<AmazonAdTargetCategories> list) {
        if (list.isEmpty()) {
            return;
        }
        List<Object[]> argsList = Lists.newArrayList();
        Object[] args;
        for (AmazonAdTargetCategories category : list) {
            args = new Object[]{
                    category.getMarketplaceId(),
                    category.getCategoryId(),
                    category.getName(),
                    category.getPath(),
                    category.getAsinCountRange(),
                    category.getParentId(),
                    category.getCreateId(),
                    category.getUpdateId()
            };
            argsList.add(args);
        }
        String sql = "insert into `t_amazon_ad_target_categories` (`marketplace_id`, `category_id`, `name`, " +
                "`path`, `asin_count_range`, `parent_id`, `create_id`, `update_id`, `create_time`, `update_time`)" +
                " values (?,?,?,?,?,?,?,?,NOW(3), NOW(3))";
        getJdbcTemplate().batchUpdate(sql, argsList);
    }

    @Override
    public void batchUpdate(List<AmazonAdTargetCategories> list) {
        if (list.isEmpty()) {
            return;
        }
        List<Object[]> argsList = Lists.newArrayList();
        Object[] args;
        for (AmazonAdTargetCategories category : list) {
            args = new Object[]{
                    category.getName(),
                    category.getPath(),
                    category.getAsinCountRange(),
                    category.getParentId(),
                    category.getUpdateId(),
                    category.getMarketplaceId(),
                    category.getCategoryId()
            };
            argsList.add(args);
        }
        String sql = "update `t_amazon_ad_target_categories` set `name`= ?, `path` = ?, `asin_count_range` = ?, " +
                "`parent_id` = ?, `update_id` = ?, `update_time` = NOW(3) where `marketplace_id` = ? and `category_id` = ? ";

        getJdbcTemplate().batchUpdate(sql, argsList);
    }

    public List<AmazonAdTargetCategories> listCategoryByCategoryIds(String marketplaceId, List<Long> categoryIds) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("marketplace_id", marketplaceId)
                .in("category_id", categoryIds.toArray(new Long[categoryIds.size()])).build();
        return listByCondition(condition);
    }

    @Override
    public Page<TargetCategoryVo> listcategoryByKeyword(TargetCategoryParam param) {
        StringBuilder sql = new StringBuilder("select category_id, name, path from t_amazon_ad_target_categories where marketplace_id = ? ");
        List<Object> args = Lists.newArrayList(param.getMarketplaceId());

        if (StringUtils.isNotBlank(param.getKeyword())) {
            sql.append(" and name like ?");
            args.add("%" + param.getKeyword().trim() + "%");
        }
        sql.append(" order by update_time desc");
        StringBuilder selectSql = new StringBuilder("select * from ( " + sql + " ) t");
        StringBuilder countSql = new StringBuilder("select count(*) from ( " + sql + " ) t");
        return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), args.toArray(), selectSql.toString(), args.toArray(), (res, i) -> TargetCategoryVo.builder()
                .id(res.getLong("category_id"))
                .name(res.getString("name"))
                .path(res.getString("path"))
                .build());
    }
}
