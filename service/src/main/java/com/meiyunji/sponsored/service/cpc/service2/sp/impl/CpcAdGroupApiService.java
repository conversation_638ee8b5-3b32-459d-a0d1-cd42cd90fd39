package com.meiyunji.sponsored.service.cpc.service2.sp.impl;

import com.amazon.advertising.spV3.enumeration.SpV3State;
import com.amazon.advertising.spV3.group.GroupSpV3Client;
import com.amazon.advertising.spV3.group.ListSpGroupV3Response;
import com.amazon.advertising.spV3.group.UpdateSpGroupV3Response;
import com.amazon.advertising.spV3.group.entity.GroupExtendEntityV3;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.AmazonResponseUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dto.AmazonServingStatusDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;

import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * Created by xp on 2021/4/15.
 * 对接口二次封装，直接和广告接口交互
 */
@Component
@Slf4j
public class CpcAdGroupApiService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private IDorisService dorisService;

    /**
     * 同步所有的广告组
     *
     * @param shop：
     */
    public void syncAdGroups(ShopAuth shop, String campaignId, String groupId, List<SpV3State> stateList, boolean isThrow, Consumer<List<AmazonAdGroup>> callback, boolean isProxy) {
        if (shop == null) {
            return;
        }
        if (StringUtils.isBlank(shop.getAdRefreshToken())) {
            log.error("syncAdGroups:店铺{}没有授权广告", shop.getId());
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncAdGroups--配置信息为空");
            return;
        }

        //获取活动的基本信息
        GroupSpV3Client client = GroupSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        if (isProxy) {
            client = GroupSpV3Client.getInstance(true);
        }

        int startIndex = 0;
        int count = 5000;
        ListSpGroupV3Response response;
        boolean refreshedToken = false; // 是否刷新了token，保证只刷新一次
        List<String> campaignIdList = null;
        
        if(StringUtils.isNotBlank(campaignId)){
            campaignIdList = StringUtil.stringToList(campaignId,",");
        }
        List<String> groupIdList = null;
        if(StringUtils.isNotBlank(groupId)){
            groupIdList = StringUtil.stringToList(groupId,",");
        }
        String nextToken = null;
        while (true) {
            response = client.listGroups(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    campaignIdList ,groupIdList, stateList, null, null, true,nextToken, count);
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SP adGroup rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = client.listGroups(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        campaignIdList ,groupIdList, stateList, null, null, true,nextToken, count);
                retry++;
            }
            if (response != null
                    && response.getStatusCode() != null
                    && response.getStatusCode() == 401
                    && !refreshedToken) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                refreshedToken = true;
                continue;
            }

            if (AmazonResponseUtil.isError(response) && isThrow) {
                throw new ServiceException("请求接口错误");
            }
            
            if (response == null ||response.getData() == null || CollectionUtils.isEmpty(response.getData().getAdGroups())) {
                break;
            }

            int size = response.getData().getAdGroups().size();
            AmazonAdGroup amazonAdGroup;
            List<AmazonAdGroup> amazonAdGroups = new ArrayList<>(size);
            for (GroupExtendEntityV3 adGroup : response.getData().getAdGroups()) {
                amazonAdGroup = turnToPO(adGroup);
                if (StringUtils.isNotBlank(amazonAdGroup.getAdGroupId())) {
                    amazonAdGroup.setPuid(shop.getPuid());
                    amazonAdGroup.setShopId(shop.getId());
                    amazonAdGroup.setMarketplaceId(shop.getMarketplaceId());
                    amazonAdGroup.setProfileId(amazonAdProfile.getProfileId());
                    amazonAdGroups.add(amazonAdGroup);
                }
            }

            amazonAdGroupDao.insertOnDuplicateKeyUpdate(shop.getPuid(), amazonAdGroups);

            //执行回调
            if (callback != null) {
                callback.accept(amazonAdGroups);
            }

            if(StringUtils.isNotBlank(response.getData().getNextToken())){
                nextToken = response.getData().getNextToken();
            } else {
                break;
            }
        }
    }

    public void syncAdGroups(ShopAuth shop, String campaignId, String groupId, boolean isThrow) {
        syncAdGroups(shop, campaignId, groupId, null, isThrow, null);
    }

    public void syncAdGroups(ShopAuth shop, String campaignId, String groupId) {
        syncAdGroups(shop, campaignId, groupId, false);
    }


    public void syncAdGroups(ShopAuth shop, String campaignId, String groupId, List<SpV3State> stateList, boolean isThrow, Consumer<List<AmazonAdGroup>> callback) {
        syncAdGroups(shop, campaignId, groupId, stateList, isThrow, callback, false);
    }

    public List<AmazonServingStatusDto> listByIds(Integer puid, Integer shopId, String groupIds){
        List<AmazonAdGroup> amazons = amazonAdGroupDao.getAdGroupByIds(puid, shopId, StringUtil.stringToList(groupIds, ","));
        amazons.forEach(i -> {
            if (Objects.nonNull(i)) {
                if (StringUtils.isNotBlank(i.getState())) {
                    i.setState(i.getState().toLowerCase());
                }
            }
        });
        dorisService.saveDorisByRoutineLoad4MysqlDto(amazons);
        return CollectionUtils.isEmpty(amazons) ? new ArrayList<>() : amazons.stream().map(key -> {
                    key.setServingStatus(key.getServingStatus());
                    return AmazonServingStatusDto.build(key.getAdGroupId(), key.getServingStatus(), key.getServingStatusName(), key.getServingStatusDec());
                }

        ).collect(Collectors.toList());
    }

    /**
     * 归档
     *
     * @param amazonAdGroup：
     * @return ：Result
     */
    Result archive(AmazonAdGroup amazonAdGroup) {
        if (amazonAdGroup == null) {
            return ResultUtil.error("请求参数错误");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdGroup.getShopId(), amazonAdGroup.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        UpdateSpGroupV3Response response = GroupSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delGroups(shopAuthService.getAdToken(shop),
                amazonAdGroup.getProfileId(), shop.getMarketplaceId(), Lists.newArrayList(amazonAdGroup.getAdGroupId()),true);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = GroupSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delGroups(shopAuthService.getAdToken(shop),
                    amazonAdGroup.getProfileId(), shop.getMarketplaceId(), Lists.newArrayList(amazonAdGroup.getAdGroupId()),true);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getData() != null && response.getData().getAdGroups() != null && CollectionUtils.isNotEmpty(response.getData().getAdGroups().getSuccess())) {
            return ResultUtil.success();
        }
        String msg = "网络延迟，请稍后重试";
        if(response.getData() != null && response.getData().getAdGroups() != null && CollectionUtils.isNotEmpty(response.getData().getAdGroups().getError())){
            String errorMessage = response.getData().getAdGroups().getError().get(0).getErrors().get(0).getErrorMessage();
            if(StringUtils.isNotBlank(errorMessage)){
                msg = AmazonErrorUtils.getError(errorMessage);
            }
        }


        if (response.getError() != null ) {
            if(StringUtils.isNotBlank(response.getError().getMessage())){
                msg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if(CollectionUtils.isNotEmpty(response.getError().getErrors())){
                msg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }
        }
        return ResultUtil.error(msg);
    }

    // 接口返回的dto转成po
    private AmazonAdGroup turnToPO(GroupExtendEntityV3 adGroup) {
        AmazonAdGroup amazonAdGroup = new AmazonAdGroup();
        if (adGroup.getCampaignId() != null) {
            amazonAdGroup.setCampaignId(adGroup.getCampaignId());
        }
        if (adGroup.getAdGroupId() != null) {
            amazonAdGroup.setAdGroupId(adGroup.getAdGroupId());
        }
        amazonAdGroup.setName(adGroup.getName());
        amazonAdGroup.setDefaultBid(adGroup.getDefaultBid());
        amazonAdGroup.setState(adGroup.getState().toLowerCase());
        amazonAdGroup.setServingStatus(adGroup.getExtendedData().getServingStatus());
        return amazonAdGroup;
    }

    /**
     * 同步所有的广告组
     *
     * @param shop：
     */
    public List<AmazonAdGroup> syncSpAdGroups(ShopAuth shop, String campaignId, String groupId) {
        List<AmazonAdGroup> amazonAdGroups = new ArrayList<>();
        if (shop == null) {
            return new ArrayList<>();
        }
        if (StringUtils.isBlank(shop.getAdRefreshToken())) {
            log.error("syncAdGroups:店铺{}没有授权广告", shop.getId());
            return new ArrayList<>();
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncAdGroups--配置信息为空");
            return new ArrayList<>();
        }

        //获取活动的基本信息
        GroupSpV3Client client = GroupSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        int startIndex = 0;
        int count = 5000;
        ListSpGroupV3Response response;
        boolean refreshedToken = false; // 是否刷新了token，保证只刷新一次
        List<String> campaignIdList = null;

        if(StringUtils.isNotBlank(campaignId)){
            campaignIdList = StringUtil.stringToList(campaignId,",");
        }
        List<String> groupIdList = null;
        if(StringUtils.isNotBlank(groupId)){
            groupIdList = StringUtil.stringToList(groupId,",");
        }
        String nextToken = null;
        while (true) {
            response = client.listGroups(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    campaignIdList ,groupIdList, null, null, null, true,nextToken, count);
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SP adGroup rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = client.listGroups(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        campaignIdList ,groupIdList, null, null, null, true,nextToken, count);
                retry++;
            }
            if (response != null
                    && response.getStatusCode() != null
                    && response.getStatusCode() == 401
                    && !refreshedToken) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                refreshedToken = true;
                continue;
            }

            if (response == null ||response.getData() == null || CollectionUtils.isEmpty(response.getData().getAdGroups())) {
                break;
            }

            AmazonAdGroup amazonAdGroup;
            for (GroupExtendEntityV3 adGroup : response.getData().getAdGroups()) {
                amazonAdGroup = turnToPO(adGroup);
                if (StringUtils.isNotBlank(amazonAdGroup.getAdGroupId())) {
                    amazonAdGroup.setPuid(shop.getPuid());
                    amazonAdGroup.setShopId(shop.getId());
                    amazonAdGroup.setMarketplaceId(shop.getMarketplaceId());
                    amazonAdGroup.setProfileId(amazonAdProfile.getProfileId());
                    amazonAdGroups.add(amazonAdGroup);
                }
            }

            if(StringUtils.isNotBlank(response.getData().getNextToken())){
                nextToken = response.getData().getNextToken();
            } else {
                break;
            }
        }
        return amazonAdGroups;
    }
}
