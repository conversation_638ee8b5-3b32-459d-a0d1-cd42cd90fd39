package com.meiyunji.sponsored.service.cpc.service2.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.service.account.bo.ShopAuthBo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.cpc.constants.RepeatTargetingTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdPortfolioService;
import com.meiyunji.sponsored.service.cpc.service2.ICpcRepeatTargetingService;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.doris.dao.*;
import com.meiyunji.sponsored.service.doris.po.OdsWeekSearchTermsAnalysis;
import com.meiyunji.sponsored.service.enums.AdMarkupTargetTypeEnum;
import com.meiyunji.sponsored.service.enums.AdTagTypeEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.ThemeKeywordTextEnum;
import com.meiyunji.sponsored.service.searchTermsAnalysis.WeekSearchTermsAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.common.util.ResultUtil.returnSucc;

@Service
@Slf4j
public class CpcRepeatTargetingServiceImpl implements ICpcRepeatTargetingService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IOdsAmazonAdKeywordDao odsAmazonAdKeywordDao;
    @Autowired
    private IOdsAmazonAdKeywordSbDao odsAmazonAdKeywordSbDao;
    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;
    @Autowired
    private IOdsAmazonAdKeywordReportDao odsAmazonAdKeywordReportDao;
    @Autowired
    private IOdsAmazonAdSbKeywordReportDao odsAmazonAdSbKeywordReportDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private CpcPageIdsHandler cpcPageIdsHandler;
    @Autowired
    private IOdsAmazonAdNeKeywordDao odsAmazonAdNeKeywordDao;
    @Autowired
    private IOdsAmazonAdNeKeywordSbDao odsAmazonAdNeKeywordSbDao;
    @Autowired
    private IOdsProductDao odsProductDao;
    @Autowired
    private IAmazonAdPortfolioService portfolioService;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAdMarkupTagDao adMarkupTagDao;
    @Autowired
    private IAdTagDao adTagDao;
    @Autowired
    private WeekSearchTermsAnalysisService weekSearchTermsAnalysisService;
    @Autowired
    private IAmazonSbAdsDao amazonSbAdsDao;
    @Autowired
    private IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;

    @Override
    public Result<RepeatTargetingCountPageListVo> countPageList(RepeatTargetingCountPageVo vo) {
        RepeatTargetingCountPageListVo countPageListVo = new RepeatTargetingCountPageListVo();
        Page<RepeatTargetingCountVo> voPage = new Page<>(vo.getPageNo(), vo.getPageSize());
        long start = System.currentTimeMillis();
        try {
            //根据asin、msku、父asin筛选出ad_group_id，把ad_group_id按类型分组
            if (CollectionUtils.isNotEmpty(vo.getProductValue()) && StringUtils.isNotBlank(vo.getProductType())) {
                List<String> adGroupIdList = new ArrayList<>();
                if (RepeatTargetingTypeEnum.SP.getCode().equals(vo.getAdType())) {
                    adGroupIdList = amazonAdProductDao.getSpAdGroupIdListByAsinAndMsku(vo.getPuid(), vo.getShopIdList(), vo.getProductValue(), vo.getProductType());
                    if (CollectionUtils.isEmpty(adGroupIdList)) {
                        countPageListVo.setPage(voPage);
                        return returnSucc(countPageListVo);
                    }
                    vo.setSpAdGroupId(adGroupIdList);
                } else if (RepeatTargetingTypeEnum.SB.getCode().equals(vo.getAdType())) {
                    adGroupIdList = amazonAdProductDao.getSbAdGroupIdListByAsinAndMsku(vo.getPuid(), vo.getShopIdList(), vo.getProductValue(), vo.getProductType());
                    if (CollectionUtils.isEmpty(adGroupIdList)) {
                        countPageListVo.setPage(voPage);
                        return returnSucc(countPageListVo);
                    }
                    vo.setSbAdGroupId(adGroupIdList);
                }
            }
            //计算重复投放次数
            List<RepeatTargetingCountVo> reportData = new ArrayList<>();
            List<RepeatTargetingCountVo> compReportData = new ArrayList<>();
            //报告数据转换为map
            Map<String, RepeatTargetingCountVo> reportDataMap = new HashMap<>();
            Map<String, RepeatTargetingCountVo> reportCompDataMap = new HashMap<>();
            if (RepeatTargetingTypeEnum.TARGET_NUM.getCode().equals(vo.getOrderField()) && StringUtils.isNotBlank(vo.getOrderType())) {
                //按重复投放次数排序
                //根据广告层级筛选，查询对应表
                if (StringUtils.isNotBlank(vo.getAdType()) && RepeatTargetingTypeEnum.SP.getCode().equals(vo.getAdType())) {
                    voPage = odsAmazonAdKeywordDao.getKeywordTargeting(vo.getPuid(), vo);
                    reportData = odsAmazonAdKeywordReportDao.getSpKeywordReportData(vo.getPuid(), vo, voPage.getRows());
                    for (RepeatTargetingCountVo data : reportData) {
                        reportDataMap.put(data.getKeywordText().toLowerCase() + data.getMatchType().toLowerCase(), data);
                    }
                    if (vo.getIsCompare()) {
                        compReportData = odsAmazonAdKeywordReportDao.getSpCompKeywordReportData(vo.getPuid(), vo, voPage.getRows());
                        for (RepeatTargetingCountVo data : compReportData) {
                            reportCompDataMap.put(data.getKeywordText().toLowerCase() + data.getMatchType().toLowerCase(), data);
                        }
                    }
                } else if (StringUtils.isNotBlank(vo.getAdType()) && RepeatTargetingTypeEnum.SB.getCode().equals(vo.getAdType())) {
                    voPage = odsAmazonAdKeywordSbDao.getKeywordTargeting(vo.getPuid(), vo);
                    reportData = odsAmazonAdSbKeywordReportDao.getSbKeywordReportData(vo.getPuid(), vo, voPage.getRows());
                    for (RepeatTargetingCountVo dataVo : reportData) {
                        String keywordText = dataVo.getKeywordText();
                        if (ThemeKeywordTextEnum.getCodeByText(keywordText) != null)  {
                            dataVo.setKeywordText(ThemeKeywordTextEnum.getCodeByText(keywordText));
                        }
                    }
                    for (RepeatTargetingCountVo data : reportData) {
                        reportDataMap.put(data.getKeywordText().toLowerCase() + data.getMatchType().toLowerCase(), data);
                    }
                    if (vo.getIsCompare()) {
                        compReportData = odsAmazonAdSbKeywordReportDao.getSbCompKeywordReportData(vo.getPuid(), vo, voPage.getRows());
                        for (RepeatTargetingCountVo dataVo : compReportData) {
                            String keywordText = dataVo.getKeywordText();
                            if (ThemeKeywordTextEnum.getCodeByText(keywordText) != null)  {
                                dataVo.setKeywordText(ThemeKeywordTextEnum.getCodeByText(keywordText));
                            }
                        }
                        for (RepeatTargetingCountVo data : compReportData) {
                            reportCompDataMap.put(data.getKeywordText().toLowerCase() + data.getMatchType().toLowerCase(), data);
                        }
                    }
                } else {
                    countPageListVo.setPage(voPage);
                    return returnSucc(countPageListVo);
                }
                this.mergeData(voPage, reportDataMap, reportCompDataMap, vo);
            } else {
                //若是报告指标排序或无排序（默认广告花费降序）
                //查出所有的keywordId
                List<RepeatTargetingCountVo> keywordTarget = new ArrayList<>();
                Page<RepeatTargetingCountVo> reportDataByIndex = null;
                HashMap<String, Integer> map = new HashMap<>();
                //存放查询基础数据后的关键词Id
                if (StringUtils.isNotBlank(vo.getAdType()) && RepeatTargetingTypeEnum.SP.getCode().equals(vo.getAdType())) {
                    reportDataByIndex = odsAmazonAdKeywordReportDao.getSpKeywordReportDataOrderByIndex(vo.getPuid(), vo);
                    List<RepeatTargetingCountVo> rows = reportDataByIndex.getRows();
                    for (RepeatTargetingCountVo data : rows) {
                        reportDataMap.put(data.getKeywordText().toLowerCase() + data.getMatchType().toLowerCase(), data);
                    }
                    if (vo.getIsCompare()) {
                        compReportData = odsAmazonAdKeywordReportDao.getSpCompKeywordReportDataOrderByIndex(vo.getPuid(), vo, rows);
                        for (RepeatTargetingCountVo data : compReportData) {
                            reportCompDataMap.put(data.getKeywordText().toLowerCase() + data.getMatchType().toLowerCase(), data);
                        }
                    }
                    if (CollectionUtils.isEmpty(rows)) {
                        countPageListVo.setPage(voPage);
                        return returnSucc(countPageListVo);
                    } else {
                        keywordTarget = odsAmazonAdKeywordDao.getKeywordTargetingByReportingIndex(vo.getPuid(), vo, rows);
                    }
                    //设置重复投放次数
                    for (RepeatTargetingCountVo v : keywordTarget) {
                        map.put(v.getKeywordText() + v.getMatchType().toLowerCase(), v.getTargetNum());
                    }
                    rows.forEach(i -> {
                        String key = i.getKeywordText() + i.getMatchType().toLowerCase();
                        if (map.get(key) != null) {
                            i.setTargetNum(map.get(key));
                        }
                    });
                    reportDataByIndex.setRows(rows);
                    voPage = reportDataByIndex;
                } else if (StringUtils.isNotBlank(vo.getAdType()) && RepeatTargetingTypeEnum.SB.getCode().equals(vo.getAdType())) {
                    reportDataByIndex = odsAmazonAdSbKeywordReportDao.getSbKeywordReportDataOrderByIndex(vo.getPuid(), vo);
                    List<RepeatTargetingCountVo> rows = reportDataByIndex.getRows();
                    for (RepeatTargetingCountVo data : rows) {
                        reportDataMap.put(data.getKeywordText().toLowerCase() + data.getMatchType().toLowerCase(), data);
                    }
                    if (vo.getIsCompare()) {
                        compReportData = odsAmazonAdSbKeywordReportDao.getSbCompKeywordReportDataOrderByIndex(vo.getPuid(), vo, rows);
                        for (RepeatTargetingCountVo data : compReportData) {
                            reportCompDataMap.put(data.getKeywordText().toLowerCase() + data.getMatchType().toLowerCase(), data);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(rows)) {
                        keywordTarget = odsAmazonAdKeywordSbDao.getKeywordTargetingByReportingIndex(vo.getPuid(), vo, rows);
                    } else {
                        countPageListVo.setPage(voPage);
                        return returnSucc(countPageListVo);
                    }
                    //设置重复投放次数
                    for (RepeatTargetingCountVo v : keywordTarget) {
                        map.put(v.getKeywordText() + v.getMatchType().toLowerCase(), v.getTargetNum());
                    }
                    rows.forEach(i -> {
                        String key = i.getKeywordText() + i.getMatchType().toLowerCase();
                        if (map.get(key) != null) {
                            i.setTargetNum(map.get(key));
                        }
                    });
                    reportDataByIndex.setRows(rows);
                    voPage = reportDataByIndex;
                } else {
                    countPageListVo.setPage(voPage);
                    return returnSucc(countPageListVo);
                }
                this.mergeData(voPage, reportDataMap, reportCompDataMap, vo);
            }
        } catch (Exception e) {
            log.error("query repeatTargeting data error, puid:{}, e", vo.getPuid(), e);
        }
        countPageListVo.setPage(voPage);
        log.info(" ==============================重复投放查询并处理分页数据花费时间 {} ==============================", System.currentTimeMillis() - start);
        return returnSucc(countPageListVo);
    }

    /**
     * 合并所有的基础数据和报告数据
     *
     * @param voPage
     * @param reportDataMap
     * @param reportCompDataMap
     * @param vo
     */
    private void mergeData(Page<RepeatTargetingCountVo> voPage, Map<String, RepeatTargetingCountVo> reportDataMap, Map<String, RepeatTargetingCountVo> reportCompDataMap, RepeatTargetingCountPageVo vo) {
        voPage.getRows().forEach(row -> {
            if (reportDataMap != null) {
                if (reportDataMap.get(row.getKeywordText().toLowerCase() + row.getMatchType().toLowerCase()) != null) {
                    row.setTargetValue(reportDataMap.get(row.getKeywordText().toLowerCase() + row.getMatchType().toLowerCase()).getTargetValue());
                }
            }
           if (reportCompDataMap != null) {
               if (reportCompDataMap.get(row.getKeywordText().toLowerCase() + row.getMatchType().toLowerCase()) != null) {
                   BigDecimal compareDate = reportCompDataMap.get(row.getKeywordText().toLowerCase() + row.getMatchType().toLowerCase()).getTargetValue();
                   BigDecimal diffData = row.getTargetValue().subtract(compareDate);
                   row.setCompTargetValue(compareDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : diffData.multiply(new BigDecimal(100))
                           .divide(compareDate, 2, RoundingMode.HALF_UP));
               }
           }
        });
    }

    @Override
    public Result<RepeatTargetingDetailPageListVo> detailPageList(RepeatTargetingDetailPageVo detailPageVo) {
        RepeatTargetingDetailPageListVo vo = new RepeatTargetingDetailPageListVo();
        Page<RepeatTargetingDetailVo> voPage = new Page<>(detailPageVo.getPageNo(), detailPageVo.getPageSize());
        try {
            long t = System.currentTimeMillis();
            //根据asin、msku、父asin筛选出ad_group_id，把ad_group_id按类型分组
            if (CollectionUtils.isNotEmpty(detailPageVo.getProductValue()) && StringUtils.isNotBlank(detailPageVo.getProductType())) {
                List<String> adGroupIdList = new ArrayList<>();
                if (RepeatTargetingTypeEnum.SP.getCode().equals(detailPageVo.getAdType())) {
                    adGroupIdList = amazonAdProductDao.getSpAdGroupIdListByAsinAndMsku(detailPageVo.getPuid(), detailPageVo.getShopIdList(), detailPageVo.getProductValue(), detailPageVo.getProductType());
                    if (CollectionUtils.isEmpty(adGroupIdList)) {
                        vo.setPage(voPage);
                        return returnSucc(vo);
                    }
                    detailPageVo.setSpAdGroupId(adGroupIdList);
                } else if (RepeatTargetingTypeEnum.SB.getCode().equals(detailPageVo.getAdType())) {
                    adGroupIdList = amazonAdProductDao.getSbAdGroupIdListByAsinAndMsku(detailPageVo.getPuid(), detailPageVo.getShopIdList(), detailPageVo.getProductValue(), detailPageVo.getProductType());
                    if (CollectionUtils.isEmpty(adGroupIdList)) {
                        vo.setPage(voPage);
                        return returnSucc(vo);
                    }
                    detailPageVo.setSbAdGroupId(adGroupIdList);
                }
            }
            //根据广告组合Id获取广告活动Id
            if (CollectionUtils.isNotEmpty(detailPageVo.getPortfolioList())) {
                List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdList(detailPageVo.getPuid(), detailPageVo.getShopIdList(), detailPageVo.getPortfolioList());
                if (CollectionUtils.isEmpty(campaignIds)) {
                    vo.setPage(voPage);
                    return returnSucc(vo);
                }
                if (CollectionUtils.isEmpty(detailPageVo.getCampaignIdList())) {
                    detailPageVo.setCampaignIdList(campaignIds);
                }
                campaignIds.retainAll(detailPageVo.getCampaignIdList());
                if (CollectionUtils.isEmpty(campaignIds)) {
                    vo.setPage(voPage);
                    return returnSucc(vo);
                }
            }
            //广告组筛选 与ASIN筛选完后的广告组取交集
            if (CollectionUtils.isNotEmpty(detailPageVo.getAdGroupIdList())) {
                List<String> adSpGroupIdList = new ArrayList<>();
                List<String> adSbGroupIdList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(detailPageVo.getSpAdGroupId())) {
                    adSpGroupIdList = detailPageVo.getAdGroupIdList().stream().filter(detailPageVo.getSpAdGroupId()::contains).collect(Collectors.toList());
                }
                if (CollectionUtils.isNotEmpty(detailPageVo.getSbAdGroupId())) {
                    adSbGroupIdList = detailPageVo.getAdGroupIdList().stream().filter(detailPageVo.getSbAdGroupId()::contains).collect(Collectors.toList());
                }
                if (CollectionUtils.isNotEmpty(adSpGroupIdList)) {
                    detailPageVo.setSpAdGroupId(adSpGroupIdList);
                } else {
                    detailPageVo.setSpAdGroupId(detailPageVo.getAdGroupIdList());
                }
                if (CollectionUtils.isNotEmpty(adSbGroupIdList)) {
                    detailPageVo.setSbAdGroupId(adSbGroupIdList);
                } else {
                    detailPageVo.setSbAdGroupId(detailPageVo.getAdGroupIdList());
                }
            }
            List<String> type = StringUtil.splitStr(detailPageVo.getAdType());
            //先根据pageSign去查询数据库里是否保存有对应keywordId
            List<String> keywordIds = new ArrayList<>();
            if (detailPageVo.getPageSign() != null) {
                keywordIds = cpcPageIdsHandler.getTemporaryAggregateIds(detailPageVo.getPageSign(), "");
            }
            if (CollectionUtils.isEmpty(keywordIds)) {
                //无对应keywordId
                //根据筛选条件查询keywordId
                //广告层级筛选
                if (detailPageVo.getAdType() != null) {
                    keywordIds = this.getKeywordIds(detailPageVo, type, detailPageVo.getPuid());
                }
            }
            // 筛选模板
            if (Boolean.TRUE.equals(detailPageVo.getOnlyCount())) {
                int count = 0;
                if(CollectionUtils.isNotEmpty(keywordIds)) {
                    count = keywordIds.size();
                }
                Page<RepeatTargetingDetailVo> page = new Page<>(detailPageVo.getPageNo(), detailPageVo.getPageSize(), 0, count);
                vo.setPage(page);
                return returnSucc(vo);
            }
            //暂存keywordId
            if (detailPageVo.getPageSign() != null) {
                cpcPageIdsHandler.addIdsTemporarySynchronize(detailPageVo.getPuid(), keywordIds, detailPageVo.getPageSign(), "");
            }
            //根据上述筛选过后的keywordId去查询基础数据
            List<RepeatTargetingDetailVo> detailData = new ArrayList<>();
            List<RepeatTargetingDetailVo> idList = new ArrayList<>();
            List<RepeatTargetingDetailVo> reportDataList = new ArrayList<>();
            List<RepeatTargetingDetailVo> compareReportDataList = new ArrayList<>();
            List<RepeatTargetingProductVo> productDataList = new ArrayList<>();
            //兼容前端排序
            String orderType = "null";
            if (RepeatTargetingTypeEnum.BID.getCode().equals(detailPageVo.getOrderField()) || StringUtils.isBlank(detailPageVo.getOrderType()) || orderType.equals(detailPageVo.getOrderType())) {
                //无排序 按keywordId排序分页
                //竞价排序
                Page<RepeatTargetingDetailVo> pageDetailData = null;
                if (RepeatTargetingTypeEnum.SP.getCode().equals(detailPageVo.getAdType()) && CollectionUtils.isNotEmpty(keywordIds)) {
                    pageDetailData = odsAmazonAdKeywordDao.getDetailKeywordData(detailPageVo.getPuid(), detailPageVo, keywordIds);
                    voPage.setTotalSize(pageDetailData.getTotalSize());
                    voPage.setTotalPage(pageDetailData.getTotalPage());
                    detailData = pageDetailData.getRows();
                    keywordIds = detailData.stream().map(RepeatTargetingDetailVo::getKeywordId).collect(Collectors.toList());
                    idList = amazonAdKeywordShardingDao.getIdByKeywordId(detailPageVo, keywordIds);
                } else if (RepeatTargetingTypeEnum.SB.getCode().equals(detailPageVo.getAdType()) && CollectionUtils.isNotEmpty(keywordIds)) {
                    pageDetailData = odsAmazonAdKeywordSbDao.getDetailKeywordData(detailPageVo.getPuid(), detailPageVo, keywordIds);
                    voPage.setTotalSize(pageDetailData.getTotalSize());
                    voPage.setTotalPage(pageDetailData.getTotalPage());
                    detailData = pageDetailData.getRows();
                    keywordIds = detailData.stream().map(RepeatTargetingDetailVo::getKeywordId).collect(Collectors.toList());
                    idList = amazonSbAdKeywordDao.getIdByKeywordId(detailPageVo, keywordIds);
                }
                Map<String, Long> map = StreamUtil.toMap(idList, RepeatTargetingDetailVo::getKeywordId, RepeatTargetingDetailVo::getId);
                detailData.forEach(i -> {
                    Long id = map.get(i.getKeywordId());
                    if (id != null) {
                        i.setId(id);
                    }
                });
            } else {
                //指标排序
                //根据keywordId去查询，对该指标值进行排序分页
                if (RepeatTargetingTypeEnum.SP.getCode().equals(detailPageVo.getAdType()) && CollectionUtils.isNotEmpty(keywordIds)) {
                    Page<RepeatTargetingDetailVo> indexReportDataList = odsAmazonAdKeywordReportDao.getIndexReportData(detailPageVo.getPuid(), detailPageVo, keywordIds);
                    voPage.setTotalSize(indexReportDataList.getTotalSize());
                    voPage.setTotalPage(indexReportDataList.getTotalPage());
                    keywordIds = indexReportDataList.getRows().stream().map(RepeatTargetingDetailVo::getKeywordId).collect(Collectors.toList());
                    idList = amazonAdKeywordShardingDao.getIdByKeywordId(detailPageVo, keywordIds);
                    //查询基础数据
                    detailData = odsAmazonAdKeywordDao.getSpKeywordDataOrderByIndex(detailPageVo.getPuid(), detailPageVo, keywordIds);
                } else if (RepeatTargetingTypeEnum.SB.getCode().equals(detailPageVo.getAdType()) && CollectionUtils.isNotEmpty(keywordIds)) {
                    Page<RepeatTargetingDetailVo> indexReportDataList = odsAmazonAdSbKeywordReportDao.getIndexReportData(detailPageVo.getPuid(), detailPageVo, keywordIds);
                    voPage.setTotalSize(indexReportDataList.getTotalSize());
                    voPage.setTotalPage(indexReportDataList.getTotalPage());
                    keywordIds = indexReportDataList.getRows().stream().map(RepeatTargetingDetailVo::getKeywordId).collect(Collectors.toList());
                    idList = amazonSbAdKeywordDao.getIdByKeywordId(detailPageVo, keywordIds);
                    //查询基础数据
                    detailData = odsAmazonAdKeywordSbDao.getSbKeywordDataOrderByIndex(detailPageVo.getPuid(), detailPageVo, keywordIds);
                }
                Map<String, RepeatTargetingDetailVo> groupedMap = detailData.stream().collect(Collectors.toMap(v -> String.valueOf(v.getKeywordId()), v -> v, (existingValue, newValue) -> existingValue));
                Map<String, Long> map = StreamUtil.toMap(idList, RepeatTargetingDetailVo::getKeywordId, RepeatTargetingDetailVo::getId);
                detailData = keywordIds.stream().map(groupedMap::get).collect(Collectors.toList());
                detailData.forEach(i -> {
                    Long id = map.get(i.getKeywordId());
                    if (id != null) {
                        i.setId(id);
                    }
                });
            }
            //批量获取标签
            List<AdMarkupTagVo> relationVos = adMarkupTagDao.getRelationVosByShopIdList(detailPageVo.getPuid(), detailPageVo.getShopIdList(), AdTagTypeEnum.TARGET.getType(), "", AdMarkupTargetTypeEnum.KEYWORD.getType(), null, keywordIds);
            List<Long> tagIdList = relationVos.stream().map(AdMarkupTagVo::getTagIds).flatMap(Collection::stream).distinct().collect(Collectors.toList());
            Map<Long, AdTag> adTagMap = new HashMap<>();
            Map<String, AdMarkupTagVo> adMarkupTagVoMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(tagIdList)) {
                List<AdTag> byLongIdList = adTagDao.getListByLongIdList(detailPageVo.getPuid(), tagIdList);
                adTagMap = byLongIdList.stream().collect(Collectors.toMap(AdTag::getId, e -> e, (e1, e2) -> e2));
                adMarkupTagVoMap = relationVos.stream().collect(Collectors.toMap(AdMarkupTagVo::getRelationId, e -> e, (e1, e2) -> e2));
            }
            //根据上述筛选过后的keywordId去查询报告数据
            if (RepeatTargetingTypeEnum.SP.getCode().equals(detailPageVo.getAdType()) && CollectionUtils.isNotEmpty(keywordIds)) {
                reportDataList = odsAmazonAdKeywordReportDao.getSpReportData(detailPageVo.getPuid(), detailPageVo, keywordIds);
            } else if (RepeatTargetingTypeEnum.SB.getCode().equals(detailPageVo.getAdType()) && CollectionUtils.isNotEmpty(keywordIds)) {
                reportDataList = odsAmazonAdSbKeywordReportDao.getSbReportData(detailPageVo.getPuid(), detailPageVo, keywordIds);
            }
            Map<String, RepeatTargetingDetailVo> reportDataMap = new HashMap<>();
            for (RepeatTargetingDetailVo data : reportDataList) {
                reportDataMap.put(data.getKeywordId(), data);
            }
            //查询环比数据
            Map<String, RepeatTargetingDetailVo> reportCompareDataMap;
            if (detailPageVo.getIsCompare()) {
                if (RepeatTargetingTypeEnum.SP.getCode().equals(detailPageVo.getAdType()) && CollectionUtils.isNotEmpty(keywordIds)) {
                    compareReportDataList = odsAmazonAdKeywordReportDao.getSpCompReportData(detailPageVo.getPuid(), detailPageVo, keywordIds);
                } else if (RepeatTargetingTypeEnum.SB.getCode().equals(detailPageVo.getAdType()) && CollectionUtils.isNotEmpty(keywordIds)) {
                    compareReportDataList = odsAmazonAdSbKeywordReportDao.getSbCompReportData(detailPageVo.getPuid(), detailPageVo, keywordIds);
                }
                reportCompareDataMap = new HashMap<>();
                for (RepeatTargetingDetailVo spReportData : compareReportDataList) {
                    reportCompareDataMap.put(spReportData.getKeywordId(), spReportData);
                }
            } else {
                reportCompareDataMap = new HashMap<>();
            }
            //查询广告组合、广告活动、广告组名称
            if (CollectionUtils.isNotEmpty(keywordIds)) {
                this.processData(detailPageVo, detailData, keywordIds, productDataList, adMarkupTagVoMap, adTagMap, reportDataMap, reportCompareDataMap);
            }
            voPage.setRows(detailData);
            log.info(" ==============================重复投放-详情列表页数据{}查询与组装花费时间 {} ==============================",detailPageVo.getKeywordText() , System.currentTimeMillis() - t);
        } catch (Exception e) {
            log.error("query repeatTargetingDetailData error, puid:{}, keyword_text:{}, e", detailPageVo.getPuid(), detailPageVo.getKeywordText(), e);
        }
        vo.setPage(voPage);
        return returnSucc(vo);
    }

    private void processData(RepeatTargetingDetailPageVo detailPageVo, List<RepeatTargetingDetailVo> detailData, List<String> keywordIds, List<RepeatTargetingProductVo> productDataList, Map<String, AdMarkupTagVo> adMarkupTagVoMap, Map<Long, AdTag> adTagMap, Map<String, RepeatTargetingDetailVo> reportDataMap, Map<String, RepeatTargetingDetailVo> reportCompareDataMap) {
        //获取广告活动名称
        List<String> campaignIdList = detailData.stream().map(RepeatTargetingDetailVo::getCampaignId).collect(Collectors.toList());
        Map<String, AmazonAdCampaignAll> campaignAllMap = amazonAdCampaignAllDao.getNameAndStateByShopIdsAndCampaignIds(detailPageVo.getPuid(), detailPageVo.getShopIdList(), campaignIdList, null, null)
                .parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity()));
        //获取广告组合名称
        List<String> portfolioIdList = campaignAllMap.values().stream().map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());
        List<KeywordLibsPortfolioListVO> portfolioListVo = portfolioService.getAllPortfolioName(detailPageVo.getPuid(), detailPageVo.getShopIdList(), portfolioIdList);
        Map<String, KeywordLibsPortfolioListVO> portfolioMap = portfolioListVo.parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(KeywordLibsPortfolioListVO::getPortfolioId, Function.identity()));
        //获取广告组名称
        Map<String, AmazonAdGroup> adGroupMap = new HashMap<>();
        Map<String, AmazonSbAdGroup> sbAdGroupMap = new HashMap<>();
        List<String> spGroupIdList = detailData.stream().filter(item -> RepeatTargetingTypeEnum.SP.getCode().equals(item.getType()))
                .map(RepeatTargetingDetailVo::getAdGroupId).collect(Collectors.toList());
        List<String> sbGroupIdList = detailData.stream().filter(item -> RepeatTargetingTypeEnum.SB.getCode().equals(item.getType()))
                .map(RepeatTargetingDetailVo::getAdGroupId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(spGroupIdList)) {
            //获取sp广告组名称
            adGroupMap.putAll(amazonAdGroupDao.getNameAndTypeAndStateByShopIdsAndGroupIds(detailPageVo.getPuid(), detailPageVo.getShopIdList(), spGroupIdList, null).
                    parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity())));
        }
        if (CollectionUtils.isNotEmpty(sbGroupIdList)) {
            //获取sb广告组名称
            sbAdGroupMap.putAll(amazonSbAdGroupDao.getNameAndTypeByShopIdsAndGroupIds(detailPageVo.getPuid(), detailPageVo.getShopIdList(), campaignIdList, sbGroupIdList, null).
                    parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, Function.identity())));
        }
        //根据上述筛选过后的每个keywordId对应的adGroupId去查询广告组下面的所有广告产品
        if (RepeatTargetingTypeEnum.SP.getCode().equals(detailPageVo.getAdType()) && CollectionUtils.isNotEmpty(keywordIds)) {
            productDataList = odsProductDao.getSpProductByGroupId(detailPageVo.getPuid(), detailPageVo.getShopIdList(), spGroupIdList);
        }
        //根据广告组Id拿取产品数据进行分组
        Map<String, List<RepeatTargetingProductVo>> productMap = productDataList.stream()
                .collect(Collectors.groupingBy(RepeatTargetingProductVo::getAdGroupId));
        //查询该词对应的店铺名称
        List<Integer> shopId = detailData.stream().map(RepeatTargetingDetailVo::getShopId).collect(Collectors.toList());
        List<ShopAuthBo> shopAuths = shopAuthDao.getShopAuthBoByIds(detailPageVo.getPuid(), shopId);
        Map<Integer, String> shop = shopAuths.stream()
                .collect(Collectors.toMap(ShopAuthBo::getId, ShopAuthBo::getName, (existingValue, newValue) -> existingValue));
        detailData.forEach(i -> {
            String name = shop.get(i.getShopId());
            if (name != null) {
                i.setShopName(name);
            }
        });
        //查询该词的ABA搜索词排名
        List<OdsWeekSearchTermsAnalysis> termsAnalyses = weekSearchTermsAnalysisService.queryRanks(Collections.singletonList(detailPageVo.getKeywordText()), detailPageVo.getMarketplaceId());
        //4、聚合基础数据、报告数据、广告产品数据、ABA搜索词排名
        filledData(detailData, adMarkupTagVoMap, adTagMap, campaignAllMap, portfolioMap, adGroupMap, sbAdGroupMap, reportDataMap, reportCompareDataMap, productMap, termsAnalyses, detailPageVo);
    }

    @Override
    public Result<RepeatTargetingTotalVo> getRepeatTargetingTotalData(RepeatTargetingDetailPageVo totalDataVo) {
        RepeatTargetingTotalVo totalVo = new RepeatTargetingTotalVo();
        List<RepeatTargetingTotalVo> voList = new ArrayList<>();
        Integer puid = totalDataVo.getPuid();
        try {
            //根据asin、msku、父asin筛选出ad_group_id，把ad_group_id按类型分组
            if (CollectionUtils.isNotEmpty(totalDataVo.getProductValue()) && StringUtils.isNotBlank(totalDataVo.getProductType())) {
                List<String> adGroupIdList = new ArrayList<>();
                if (RepeatTargetingTypeEnum.SP.getCode().equals(totalDataVo.getAdType())) {
                    adGroupIdList = amazonAdProductDao.getSpAdGroupIdListByAsinAndMsku(totalDataVo.getPuid(), totalDataVo.getShopIdList(), totalDataVo.getProductValue(), totalDataVo.getProductType());
                    if (CollectionUtils.isEmpty(adGroupIdList)) {
                        return returnSucc(totalVo);
                    }
                    totalDataVo.setSpAdGroupId(adGroupIdList);
                } else if (RepeatTargetingTypeEnum.SB.getCode().equals(totalDataVo.getAdType())) {
                    adGroupIdList = amazonAdProductDao.getSbAdGroupIdListByAsinAndMsku(totalDataVo.getPuid(), totalDataVo.getShopIdList(), totalDataVo.getProductValue(), totalDataVo.getProductType());
                    if (CollectionUtils.isEmpty(adGroupIdList)) {
                        return returnSucc(totalVo);
                    }
                    totalDataVo.setSbAdGroupId(adGroupIdList);
                }
            }
            long t = System.currentTimeMillis();
            if (CollectionUtils.isNotEmpty(totalDataVo.getPortfolioList())) {
                List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdList(puid, totalDataVo.getShopIdList(), totalDataVo.getPortfolioList());
                if (CollectionUtils.isEmpty(campaignIds)) {
                    return returnSucc(totalVo);
                }
                if (CollectionUtils.isEmpty(totalDataVo.getCampaignIdList())) {
                    totalDataVo.setCampaignIdList(campaignIds);
                } else {
                    campaignIds.retainAll(totalDataVo.getCampaignIdList());
                    if (CollectionUtils.isEmpty(campaignIds)) {
                        return returnSucc(totalVo);
                    }
                }
            }
            //广告组筛选 与ASIN筛选完后的广告组取交集
            if (CollectionUtils.isNotEmpty(totalDataVo.getAdGroupIdList())) {
                List<String> adSpGroupIdList = totalDataVo.getAdGroupIdList().stream().filter(adGroupId -> {
                            List<String> spAdGroupIdList = totalDataVo.getSpAdGroupId();
                            return spAdGroupIdList!= null && spAdGroupIdList.contains(adGroupId);
                        }).collect(Collectors.toList());
                List<String> adSbGroupIdList = totalDataVo.getAdGroupIdList().stream().filter(adGroupId -> {
                            List<String> sbAdGroupIdList = totalDataVo.getSbAdGroupId();
                            return sbAdGroupIdList!= null && sbAdGroupIdList.contains(adGroupId);
                        }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(adSpGroupIdList)) {
                    totalDataVo.setSpAdGroupId(adSpGroupIdList);
                } else {
                    totalDataVo.setSpAdGroupId(totalDataVo.getAdGroupIdList());
                }
                if (CollectionUtils.isNotEmpty(adSbGroupIdList)) {
                    totalDataVo.setSbAdGroupId(adSbGroupIdList);
                } else {
                    totalDataVo.setSbAdGroupId(totalDataVo.getAdGroupIdList());
                }
            }
            //先根据pageSign去查询数据库里是否保存有对应keywordId
            List<String> keywordIds = new ArrayList<>();
            if (totalDataVo.getPageSign() != null) {
                keywordIds = cpcPageIdsHandler.getTemporaryAggregateIds(totalDataVo.getPageSign(), "");
            }
            //广告层级筛选
            List<String> type = StringUtil.splitStr(totalDataVo.getAdType());
            if (CollectionUtils.isEmpty(keywordIds)) {
                //无对应keywordId
                //根据筛选条件查询keywordId
                keywordIds = this.getKeywordIds(totalDataVo, type, puid);
            }
            if (CollectionUtils.isEmpty(keywordIds)) {
                return returnSucc(totalVo);
            }
            //查询报告数据
            if (RepeatTargetingTypeEnum.SP.getCode().equals(type.get(0))) {
                voList = odsAmazonAdKeywordReportDao.getRepeatTargetingReportTotalData(puid, totalDataVo, keywordIds);
            } else if (RepeatTargetingTypeEnum.SB.getCode().equals(type.get(0))) {
                voList = odsAmazonAdSbKeywordReportDao.getRepeatTargetingReportTotalData(puid, totalDataVo, keywordIds);
            }
            //汇总报告数据--聚合成汇总数据
            List<String> finalKeywordIds = keywordIds;
            voList.forEach(item -> {
                //重复投放数量
                totalVo.setTotalNum(finalKeywordIds.size());
                //曝光量
                totalVo.setImpressions(totalVo.getImpressions() + item.getImpressions());
                //点击量
                totalVo.setClicks(totalVo.getClicks() + item.getClicks());
                //广告花费
                totalVo.setCost(totalVo.getCost().add(item.getCost()));
                //广告销售额
                totalVo.setTotalSales(totalVo.getTotalSales().add(item.getTotalSales()));
                //广告订单数
                totalVo.setSaleNum(totalVo.getSaleNum() + item.getSaleNum());
                //CPC
                totalVo.setCpc(item.getCpc().setScale(2, RoundingMode.HALF_UP));
                //CPA
                totalVo.setCpa(item.getCpa().setScale(2, RoundingMode.HALF_UP));
                //ACoS
                totalVo.setAcos(item.getAcos().multiply(ONE_HUNDRED).setScale(2, RoundingMode.HALF_UP));
                //ROAS
                totalVo.setRoas(item.getRoas().setScale(2, RoundingMode.HALF_UP));
                //转化率
                totalVo.setSalesConversionRate(item.getSalesConversionRate().multiply(ONE_HUNDRED).setScale(2, RoundingMode.HALF_UP));
                //点击率
                totalVo.setClickRate(item.getClickRate().multiply(ONE_HUNDRED).setScale(2, RoundingMode.HALF_UP));
            });
            //对比
            if (totalDataVo.getIsCompare()) {
                RepeatTargetingDetailPageVo compareTotalDataVo = this.buildRepeatTargetingDetailPageCompareVo(totalDataVo);
                RepeatTargetingTotalVo compareAggregateVo = new RepeatTargetingTotalVo();
                if (RepeatTargetingTypeEnum.SP.getCode().equals(type.get(0))) {
                    compareAggregateVo = odsAmazonAdKeywordReportDao.getRepeatTargetingReportTotalData(puid, compareTotalDataVo, keywordIds).get(0);
                } else if (RepeatTargetingTypeEnum.SB.getCode().equals(type.get(0))) {
                    compareAggregateVo = odsAmazonAdSbKeywordReportDao.getRepeatTargetingReportTotalData(puid, compareTotalDataVo, keywordIds).get(0);
                }
                if (compareAggregateVo != null) {
                    this.fillRepeatTargetingTotalVoCompareData(totalVo, compareAggregateVo);
                }
            }
            //暂存keywordId
            if (totalDataVo.getPageSign() != null) {
                cpcPageIdsHandler.addIdsTemporarySynchronize(totalDataVo.getPuid(), keywordIds, totalDataVo.getPageSign(), "");
            }
            log.info(" ==============================重复投放-详情列表页汇总数据查询与组装花费时间 {} ==============================", System.currentTimeMillis() - t);
        } catch (Exception e) {
            log.error("query repeatTargetingDetailTotalData error, puid:{}, keyword_text:{}, e", puid, totalDataVo.getKeywordText(), e);
        }
        return returnSucc(totalVo);
    }

    /**
     * 重复投放列表页复制对比请求vo
     * @param totalDataVo
     * @return
     */
    private RepeatTargetingDetailPageVo buildRepeatTargetingDetailPageCompareVo(RepeatTargetingDetailPageVo totalDataVo) {
        RepeatTargetingDetailPageVo compareTotalDataVo = new RepeatTargetingDetailPageVo();
        BeanUtils.copyProperties(totalDataVo, compareTotalDataVo);
        compareTotalDataVo.setStartDate(compareTotalDataVo.getCompareStartDate());
        compareTotalDataVo.setEndDate(compareTotalDataVo.getCompareEndDate());
        return compareTotalDataVo;
    }

    /**
     * 重复投放列表页汇总填充对比值
     * @param totalVo
     * @param compareAggregateVo
     */
    private void fillRepeatTargetingTotalVoCompareData(RepeatTargetingTotalVo totalVo, RepeatTargetingTotalVo compareAggregateVo) {
        //对比值
        totalVo.setCompareImpressions(compareAggregateVo.getImpressions());
        totalVo.setCompareClicks(compareAggregateVo.getClicks());
        totalVo.setCompareCost(compareAggregateVo.getCost());
        totalVo.setCompareTotalSales(compareAggregateVo.getTotalSales());
        totalVo.setCompareSaleNum(compareAggregateVo.getSaleNum());
        totalVo.setCompareCpc(compareAggregateVo.getCpc().setScale(2, RoundingMode.HALF_UP));
        totalVo.setCompareCpa(compareAggregateVo.getCpa().setScale(2, RoundingMode.HALF_UP));
        totalVo.setCompareAcos(compareAggregateVo.getAcos().multiply(ONE_HUNDRED).setScale(2, RoundingMode.HALF_UP));
        totalVo.setCompareRoas(compareAggregateVo.getRoas().setScale(2, RoundingMode.HALF_UP));
        totalVo.setCompareSalesConversionRate(compareAggregateVo.getSalesConversionRate().multiply(ONE_HUNDRED).setScale(2, RoundingMode.HALF_UP));
        totalVo.setCompareClickRate(compareAggregateVo.getClickRate().multiply(ONE_HUNDRED).setScale(2, RoundingMode.HALF_UP));
        //对比增长率
        totalVo.setCompareImpressionsRate(MathUtil.calculateCompareRateWithLine(new BigDecimal(totalVo.getImpressions()), new BigDecimal(totalVo.getCompareImpressions())));
        totalVo.setCompareClicksRate(MathUtil.calculateCompareRateWithLine(new BigDecimal(totalVo.getClicks()), new BigDecimal(totalVo.getCompareClicks())));
        totalVo.setCompareCostRate(MathUtil.calculateCompareRateWithLine(totalVo.getCost(), totalVo.getCompareCost()));
        totalVo.setCompareTotalSalesRate(MathUtil.calculateCompareRateWithLine(totalVo.getTotalSales(), totalVo.getCompareTotalSales()));
        totalVo.setCompareSaleNumRate(MathUtil.calculateCompareRateWithLine(new BigDecimal(totalVo.getSaleNum()), new BigDecimal(totalVo.getCompareSaleNum())));
        totalVo.setCompareCpcRate(MathUtil.calculateCompareRateWithLine(totalVo.getCpc(), totalVo.getCompareCpc()));
        totalVo.setCompareCpaRate(MathUtil.calculateCompareRateWithLine(totalVo.getCpa(), totalVo.getCompareCpa()));
        totalVo.setCompareAcosRate(MathUtil.calculateCompareRateWithLine(totalVo.getAcos(), totalVo.getCompareAcos()));
        totalVo.setCompareRoasRate(MathUtil.calculateCompareRateWithLine(totalVo.getRoas(), totalVo.getCompareRoas()));
        totalVo.setCompareSalesConversionRateRate(MathUtil.calculateCompareRateWithLine(totalVo.getSalesConversionRate(), totalVo.getCompareSalesConversionRate()));
        totalVo.setCompareClickRateRate(MathUtil.calculateCompareRateWithLine(totalVo.getClickRate(), totalVo.getCompareClickRate()));
    }

    private List<String> getKeywordIds(RepeatTargetingDetailPageVo totalDataVo, List<String> type, Integer puid) {
        List<String> keywordIds = Collections.emptyList();
        List<String> spKeywordIds;
        List<RepeatTargetingDetailVo> spKeyword;
        List<String> sbKeywordIds;
        List<RepeatTargetingDetailVo> sbKeyword;
        List<String> spNeAdGroupIds;
        List<String> sbNeAdGroupIds;
        if (RepeatTargetingTypeEnum.SP.getCode().equals(type.get(0))) {
            spKeyword = odsAmazonAdKeywordDao.getKeywordIds(puid, totalDataVo);
            spKeywordIds = spKeyword.stream().map(RepeatTargetingDetailVo::getKeywordId).collect(Collectors.toList());
            Map<String, String> map = StreamUtil.toMap(spKeyword, RepeatTargetingDetailVo::getAdGroupId, RepeatTargetingDetailVo::getKeywordId);
            //过滤否投
            if (totalDataVo.getIsFilterNeTargeting()) {
                spNeAdGroupIds = odsAmazonAdNeKeywordDao.filterKeywordId(puid, totalDataVo);
                for (String adGroupId : spNeAdGroupIds) {
                    if (map.get(adGroupId) != null) {
                        spKeywordIds.remove(map.get(adGroupId));
                    }
                }
            }
            keywordIds = spKeywordIds;
        } else if (RepeatTargetingTypeEnum.SB.getCode().equals(type.get(0))) {
            sbKeyword = odsAmazonAdKeywordSbDao.getKeywordIds(puid, totalDataVo);
            sbKeywordIds = sbKeyword.stream().map(RepeatTargetingDetailVo::getKeywordId).collect(Collectors.toList());
            Map<String, String> map = StreamUtil.toMap(sbKeyword, RepeatTargetingDetailVo::getAdGroupId, RepeatTargetingDetailVo::getKeywordId);
            //过滤否投
            if (totalDataVo.getIsFilterNeTargeting()) {
                sbNeAdGroupIds = odsAmazonAdNeKeywordSbDao.filterKeywordId(puid, totalDataVo);
                for (String adGroupId : sbNeAdGroupIds) {
                    if (map.get(adGroupId) != null) {
                        sbKeywordIds.remove(map.get(adGroupId));
                    }
                }
            }
            keywordIds = sbKeywordIds;
        }
        return keywordIds;
    }

    @Override
    public Result<List<RepeatTargetingDetailVo>> exportList(RepeatTargetingDetailPageVo exportParam) {
        exportParam.setPageNo(1);
        exportParam.setPageSize(Constants.EXPORT_MAX_SIZE);
        Result<RepeatTargetingDetailPageListVo> result = detailPageList(exportParam);
        List<RepeatTargetingDetailVo> rows = result.getData().getPage().getRows();
        return returnSucc(rows);
    }

    private static BigDecimal ONE_HUNDRED = new BigDecimal("100");

    /**
     * 聚合数据
     *
     * @param detailData
     * @param finalAdMarkupTagVoMap
     * @param finalAdTagMap
     * @param campaignAllMap
     * @param portfolioMap
     * @param adGroupMap
     * @param sbAdGroupMap
     * @param reportDataMap
     * @param reportCompareDataMap
     * @param productMap
     * @param termsAnalyses
     * @param detailPageVo
     */
    private static void filledData(List<RepeatTargetingDetailVo> detailData, Map<String, AdMarkupTagVo> finalAdMarkupTagVoMap,
                                   Map<Long, AdTag> finalAdTagMap, Map<String, AmazonAdCampaignAll> campaignAllMap,
                                   Map<String, KeywordLibsPortfolioListVO> portfolioMap, Map<String, AmazonAdGroup> adGroupMap,
                                   Map<String, AmazonSbAdGroup> sbAdGroupMap, Map<String, RepeatTargetingDetailVo> reportDataMap,
                                   Map<String, RepeatTargetingDetailVo> reportCompareDataMap, Map<String, List<RepeatTargetingProductVo>> productMap,
                                   List<OdsWeekSearchTermsAnalysis> termsAnalyses, RepeatTargetingDetailPageVo detailPageVo) {
        detailData.stream().filter(Objects::nonNull).forEach(detail -> {
            //填充关键词 该值不需要进行数据库查询
            detail.setKeywordText(detailPageVo.getKeywordText());
            //填充标签
            if (finalAdMarkupTagVoMap.containsKey(detail.getKeywordId())) {
                AdMarkupTagVo adMarkupTagVo = finalAdMarkupTagVoMap.get(detail.getKeywordId());
                if (CollectionUtils.isNotEmpty(adMarkupTagVo.getTagIds())) {
                    List<AdTag> adTags = new ArrayList<>();
                    for (Long tagId : adMarkupTagVo.getTagIds()) {
                        if (finalAdTagMap.containsKey(tagId)) {
                            adTags.add(finalAdTagMap.get(tagId));
                        }
                    }
                    detail.setAdTags(adTags);
                }
            }
            //设置广告活动名称和状态
            AmazonAdCampaignAll campaign = campaignAllMap.get(detail.getCampaignId());
            if (campaign != null && org.apache.commons.lang3.StringUtils.isNotEmpty(campaign.getName())) {
                detail.setCampaignName(campaign.getName());
                if ("archived".equals(campaign.getState())) {
                    detail.setAdCampaignIsArchived(true);
                } else {
                    detail.setAdCampaignIsArchived(false);
                }
                detail.setCampaignBid(campaign.getBudget());
                Optional.ofNullable(campaign.getCostType()).filter(StringUtils::isNotEmpty).ifPresent(detail::setCostType);
            }
            //设置广告组合名称
            if (campaign != null && campaign.getPortfolioId() != null) {
                KeywordLibsPortfolioListVO portfolioListVO = portfolioMap.get(campaign.getPortfolioId());
                if (portfolioListVO != null && org.apache.commons.lang3.StringUtils.isNotEmpty(portfolioListVO.getPortfolioName())) {
                    detail.setPortfolioName(portfolioListVO.getPortfolioName());
                    detail.setPortfolioId(portfolioListVO.getPortfolioId());
                    detail.setIsHidden(portfolioListVO.getIsHidden());
                }
            }
            //设置广告组名称和类型
            if (detail.getAdGroupId() != null) {
                AmazonAdGroup adGroup = adGroupMap.get(detail.getAdGroupId());
                if (adGroup != null && StringUtils.isNotEmpty(adGroup.getName())) {
                    detail.setAdGroupName(adGroup.getName());
                    detail.setGroupType(adGroup.getAdGroupType());
                    if ("archived".equals(adGroup.getState())) {
                        detail.setAdGroupIsArchived(true);
                    } else {
                        detail.setAdGroupIsArchived(false);
                    }
                }
                AmazonSbAdGroup adSbGroup = sbAdGroupMap.get(detail.getAdGroupId());
                if (adSbGroup != null && StringUtils.isNotEmpty(adSbGroup.getName())) {
                    detail.setAdGroupName(adSbGroup.getName());
                    detail.setGroupType(adSbGroup.getAdGroupType());
                    if ("archived".equals(adSbGroup.getState())) {
                        detail.setAdGroupIsArchived(true);
                    } else {
                        detail.setAdGroupIsArchived(false);
                    }
                    Optional.ofNullable(adSbGroup.getAdFormat()).filter(StringUtils::isNotEmpty).ifPresent(detail::setAdFormat);
                }
            }
            //报告数据
            RepeatTargetingDetailVo reportData = reportDataMap.get(detail.getKeywordId());
            if (reportData != null) {
                detail.setImpressions(reportData.getImpressions());
                detail.setClicks(reportData.getClicks());
                detail.setClickRate(reportData.getClickRate().multiply(ONE_HUNDRED));
                detail.setSaleNum(reportData.getSaleNum());
                detail.setSalesConversionRate(reportData.getSalesConversionRate().multiply(ONE_HUNDRED));
                detail.setTotalSales(reportData.getTotalSales());
                detail.setCost(reportData.getCost());
                detail.setCpc(reportData.getCpc());
                detail.setCpa(reportData.getCpa());
                detail.setAcos(reportData.getAcos().multiply(ONE_HUNDRED));
                detail.setRoas(reportData.getRoas());
            }
            //环比数据
            RepeatTargetingDetailVo compareReportData = reportCompareDataMap.get(detail.getKeywordId());
            if (compareReportData != null) {
                detail.setCompareImpressions(compareReportData.getImpressions());
                detail.setCompareClicks(compareReportData.getClicks());
                detail.setCompareClickRate(compareReportData.getClickRate().multiply(ONE_HUNDRED));
                detail.setCompareSaleNum(compareReportData.getSaleNum());
                detail.setCompareSalesConversionRate(compareReportData.getSalesConversionRate().multiply(ONE_HUNDRED));
                detail.setCompareTotalSales(compareReportData.getTotalSales());
                detail.setCompareCost(compareReportData.getCost());
                detail.setCompareCpc(compareReportData.getCpc());
                detail.setCompareCpa(compareReportData.getCpa());
                detail.setCompareAcos(compareReportData.getAcos().multiply(ONE_HUNDRED));
                detail.setCompareRoas(compareReportData.getRoas());
            }
            //差异数据报告
            if (compareReportData != null) {
                detail.setDiffImpressions(new BigDecimal(detail.getCompareImpressions()).compareTo(BigDecimal.ZERO) == 0 ? "-" : new BigDecimal(detail.getImpressions()).subtract(new BigDecimal(detail.getCompareImpressions())).multiply(new BigDecimal(100))
                        .divide(new BigDecimal(detail.getCompareImpressions()), 2, RoundingMode.HALF_UP).toPlainString());
                detail.setDiffClicks(new BigDecimal(detail.getCompareClicks()).compareTo(BigDecimal.ZERO) == 0 ? "-" : new BigDecimal(detail.getClicks()).subtract(new BigDecimal(detail.getCompareClicks())).multiply(new BigDecimal(100))
                        .divide(new BigDecimal(detail.getCompareClicks()), 2, RoundingMode.HALF_UP).toPlainString());
                detail.setDiffClickRate(detail.getCompareClickRate().compareTo(BigDecimal.ZERO) == 0 ? "-" : detail.getClickRate().subtract(detail.getCompareClickRate()).multiply(new BigDecimal(100))
                        .divide(detail.getCompareClickRate(), 2, RoundingMode.HALF_UP).toPlainString());
                detail.setDiffSaleNum(new BigDecimal(detail.getCompareSaleNum()).compareTo(BigDecimal.ZERO) == 0 ? "-" : new BigDecimal(detail.getSaleNum()).subtract(new BigDecimal(detail.getCompareSaleNum())).multiply(new BigDecimal(100))
                        .divide(new BigDecimal(detail.getCompareSaleNum()), 2, RoundingMode.HALF_UP).toPlainString());
                detail.setDiffSalesConversionRate(detail.getCompareSalesConversionRate().compareTo(BigDecimal.ZERO) == 0 ? "-" : detail.getSalesConversionRate().subtract(detail.getCompareSalesConversionRate()).multiply(new BigDecimal(100))
                        .divide(detail.getCompareSalesConversionRate(), 2, RoundingMode.HALF_UP).toPlainString());
                detail.setDiffTotalSales(detail.getCompareTotalSales().compareTo(BigDecimal.ZERO) == 0 ? "-" : detail.getTotalSales().subtract(detail.getCompareTotalSales()).multiply(new BigDecimal(100))
                        .divide(detail.getCompareTotalSales(), 2, RoundingMode.HALF_UP).toPlainString());
                detail.setDiffCost(detail.getCompareCost().compareTo(BigDecimal.ZERO) == 0 ? "-" : detail.getCost().subtract(detail.getCompareCost()).multiply(new BigDecimal(100))
                        .divide(detail.getCompareCost(), 2, RoundingMode.HALF_UP).toPlainString());
                detail.setDiffCpc(detail.getCompareCpc().compareTo(BigDecimal.ZERO) == 0 ? "-" : detail.getCpc().subtract(detail.getCompareCpc()).multiply(new BigDecimal(100))
                        .divide(detail.getCompareCpc(), 2, RoundingMode.HALF_UP).toPlainString());
                detail.setDiffCpa(detail.getCompareCpa().compareTo(BigDecimal.ZERO) == 0 ? "-" : detail.getCpa().subtract(detail.getCompareCpa()).multiply(new BigDecimal(100))
                        .divide(detail.getCompareCpa(), 2, RoundingMode.HALF_UP).toPlainString());
                detail.setDiffAcos(detail.getCompareAcos().compareTo(BigDecimal.ZERO) == 0 ? "-" : detail.getAcos().subtract(detail.getCompareAcos()).multiply(new BigDecimal(100))
                        .divide(detail.getCompareAcos(), 2, RoundingMode.HALF_UP).toPlainString());
                detail.setDiffRoas(detail.getCompareRoas().compareTo(BigDecimal.ZERO) == 0 ? "-" : detail.getRoas().subtract(detail.getCompareRoas()).multiply(new BigDecimal(100))
                        .divide(detail.getCompareRoas(), 2, RoundingMode.HALF_UP).toPlainString());
            }
            //广告产品
            List<RepeatTargetingProductVo> product = productMap.get(detail.getAdGroupId());
            if (product != null) {
                detail.setProducts(product);
            }
            //ABA搜索词排名,所有词的排名均一样，拿第一个即可
            if (CollectionUtils.isNotEmpty(termsAnalyses)) {
                OdsWeekSearchTermsAnalysis termsAnalysis = termsAnalyses.get(0);
                detail.setSearchFrequencyRank(termsAnalysis.getSearchFrequencyRank());
                detail.setWeekRatio(termsAnalysis.getWeekRatio());
            }
        });
    }

}
