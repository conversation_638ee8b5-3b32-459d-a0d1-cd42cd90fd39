package com.meiyunji.sponsored.service.strategyTask.api;

import com.meiyunji.sponsored.common.base.Constants;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.MD5Util;
import com.meiyunji.sponsored.service.config.IndexStrategyConfig;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.enums.AutoTargetTypeEnum;
import com.meiyunji.sponsored.service.log.enums.*;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.po.OperationContent;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.strategy.dao.*;

import com.meiyunji.sponsored.service.strategy.enums.StartStopItemTypeEnum;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatus;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.strategy.service.helper.AdvertiseStrategyGroupTargetBidHelper;
import com.meiyunji.sponsored.service.strategy.vo.UpdateStrategyVo;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyRealTimeBidDao;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyTaskRecordDao;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyTaskRecordSequenceDao;
import com.meiyunji.sponsored.service.strategyTask.enums.ItemType;
import com.meiyunji.sponsored.service.strategyTask.enums.TaskAdType;
import com.meiyunji.sponsored.service.strategyTask.enums.TaskTargetType;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyTaskRecord;
import com.meiyunji.sponsored.service.strategyTask.vo.UpdateStrategyResponseVo;
import com.meiyunji.sponsored.service.taskGrpcApi.AadasApiFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-11  13:27
 */
@Slf4j
public abstract class StrategyCommonApi {

    protected final AdvertiseStrategyTemplateDao advertiseStrategyTemplateDao;
    protected final AdvertiseStrategyStatusDao advertiseStrategyStatusDao;
    protected final AdvertiseStrategyStatusDeleteDao advertiseStrategyStatusDeleteDao;
    protected final AdvertiseStrategyScheduleDao advertiseStrategyScheduleDao;
    protected final IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    protected final IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    protected final IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    protected final IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    protected final AadasApiFactory aadasApiFactory;
    protected final AdvertiseStrategyStatusSequenceDao advertiseStrategyStatusSequenceDao;
    protected final AdvertiseStrategyScheduleSequenceDao advertiseStrategyScheduleSequenceDao;
    protected final IAmazonAdGroupDao amazonAdGroupDao;
    protected final IAmazonSbAdGroupDao amazonSbAdGroupDao;
    protected final IAmazonSdAdGroupDao amazonSdAdGroupDao;
    protected final IAdManageOperationLogService adManageOperationLogService;
    protected final IAmazonAdPortfolioDao portfolioDao;
    protected final AdvertiseStrategyTemplateSequenceDao advertiseStrategyTemplateSequenceDao;
    protected final RedisService redisService;
    protected final IndexStrategyConfig indexStrategyConfig;
    protected final AdvertiseStrategyAdGroupDao advertiseStrategyAdGroupDao;
    protected final AdvertiseStrategyRealTimeBidDao advertiseStrategyRealTimeBidDao;
    protected final AdvertiseStrategyTaskRecordSequenceDao advertiseStrategyTaskRecordSequenceDao;
    protected final RedissonClient redissonClient;
    @Resource
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Resource
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    protected final AdvertiseStrategyGroupTargetBidHelper advertiseStrategyGroupTargetBidHelper;

    protected StrategyCommonApi(AdvertiseStrategyTemplateDao advertiseStrategyTemplateDao,
                                AdvertiseStrategyStatusDao advertiseStrategyStatusDao,
                                AdvertiseStrategyStatusDeleteDao advertiseStrategyStatusDeleteDao,
                                AdvertiseStrategyScheduleDao advertiseStrategyScheduleDao,
                                IAmazonAdCampaignAllDao amazonAdCampaignAllDao,
                                IAmazonSbAdKeywordDao amazonSbAdKeywordDao,
                                IAmazonSbAdTargetingDao amazonSbAdTargetingDao,
                                IAmazonSdAdTargetingDao amazonSdAdTargetingDao,
                                AadasApiFactory aadasApiFactory,
                                AdvertiseStrategyStatusSequenceDao advertiseStrategyStatusSequenceDao,
                                AdvertiseStrategyScheduleSequenceDao advertiseStrategyScheduleSequenceDao,
                                IAmazonAdGroupDao amazonAdGroupDao,
                                IAmazonSbAdGroupDao amazonSbAdGroupDao,
                                IAmazonSdAdGroupDao amazonSdAdGroupDao,
                                IAdManageOperationLogService adManageOperationLogService,
                                IAmazonAdPortfolioDao portfolioDao,
                                AdvertiseStrategyTemplateSequenceDao advertiseStrategyTemplateSequenceDao,
                                RedisService redisService,
                                IndexStrategyConfig indexStrategyConfig,
                                AdvertiseStrategyAdGroupDao advertiseStrategyAdGroupDao,
                                AdvertiseStrategyRealTimeBidDao advertiseStrategyRealTimeBidDao,
                                AdvertiseStrategyTaskRecordSequenceDao advertiseStrategyTaskRecordSequenceDao,
                                RedissonClient redissonClient,
                                AdvertiseStrategyGroupTargetBidHelper advertiseStrategyGroupTargetBidHelper) {
        this.advertiseStrategyTemplateDao = advertiseStrategyTemplateDao;
        this.advertiseStrategyStatusDao = advertiseStrategyStatusDao;
        this.advertiseStrategyStatusDeleteDao = advertiseStrategyStatusDeleteDao;
        this.advertiseStrategyScheduleDao = advertiseStrategyScheduleDao;
        this.amazonAdCampaignAllDao = amazonAdCampaignAllDao;
        this.amazonSbAdKeywordDao = amazonSbAdKeywordDao;
        this.amazonSbAdTargetingDao = amazonSbAdTargetingDao;
        this.amazonSdAdTargetingDao = amazonSdAdTargetingDao;
        this.aadasApiFactory = aadasApiFactory;
        this.advertiseStrategyStatusSequenceDao = advertiseStrategyStatusSequenceDao;
        this.advertiseStrategyScheduleSequenceDao = advertiseStrategyScheduleSequenceDao;
        this.amazonAdGroupDao = amazonAdGroupDao;
        this.amazonSbAdGroupDao = amazonSbAdGroupDao;
        this.amazonSdAdGroupDao = amazonSdAdGroupDao;
        this.adManageOperationLogService = adManageOperationLogService;
        this.portfolioDao = portfolioDao;
        this.advertiseStrategyTemplateSequenceDao = advertiseStrategyTemplateSequenceDao;
        this.redisService = redisService;
        this.indexStrategyConfig = indexStrategyConfig;
        this.advertiseStrategyAdGroupDao = advertiseStrategyAdGroupDao;
        this.advertiseStrategyRealTimeBidDao = advertiseStrategyRealTimeBidDao;
        this.advertiseStrategyTaskRecordSequenceDao = advertiseStrategyTaskRecordSequenceDao;
        this.redissonClient = redissonClient;
        this.advertiseStrategyGroupTargetBidHelper = advertiseStrategyGroupTargetBidHelper;
    }

    protected void addControlledObjectsLog(Integer puid, Integer uid, String loginIp, String itemType, Long templateId, AdManageOperationLog adManageOperationLog, Boolean flag) {
        addControlledObjectsLog(puid, uid, loginIp, itemType, templateId, adManageOperationLog, flag, null);
    }

    // (通用信息)及受控对象操作增加日志
    protected void addControlledObjectsLog(Integer puid, Integer uid, String loginIp, String itemType, Long templateId, AdManageOperationLog adManageOperationLog, Boolean flag, StartStopItemTypeEnum startStopItemType) {
        adManageOperationLog.setAction(OperationLogActionEnum.EDIT.getOperationValue());
        adManageOperationLog.setPuid(puid);
        adManageOperationLog.setUid(uid);
        adManageOperationLog.setIp(loginIp);
        adManageOperationLog.setTemplateId(templateId);
        if (flag) {
            if (ItemType.CAMPAIGN.name().equals(itemType)) {
                adManageOperationLog.setOperationObject(OperationLogTargetEnum.BUDGET_TEMPLATE.getTargetValue());
            }
            if (ItemType.CAMPAIGN_PLACEMENT.name().equals(itemType)) {
                adManageOperationLog.setOperationObject(OperationLogTargetEnum.PLACEMENT_TEMPLATE.getTargetValue());
            }
            if (ItemType.TARGET.name().equals(itemType)) {
                adManageOperationLog.setOperationObject(OperationLogTargetEnum.BIDDING_TEMPLATE.getTargetValue());
            }
            if (ItemTypeEnum.START_STOP.getItemType().equals(itemType)) {
                adManageOperationLog.setOperationObject(OperationLogTargetEnum.SWITCH_TEMPLATE.getTargetValue());
            }
            if (ItemTypeEnum.PORTFOLIO.getItemType().equals(itemType)) {
                adManageOperationLog.setOperationObject(OperationLogTargetEnum.PORTFOLIO_TEMPLATE.getTargetValue());
            }
            if (ItemType.AD_GROUP_TARGET.name().equals(itemType)) {
                adManageOperationLog.setOperationObject(OperationLogTargetEnum.BIDDING_TEMPLATE.getTargetValue());
            }
        } else {
            if (ItemType.CAMPAIGN.name().equals(itemType)) {
                adManageOperationLog.setOperationObject(OperationLogTargetEnum.BUDGET_STRATEGY.getTargetValue());
            }
            if (ItemType.CAMPAIGN_PLACEMENT.name().equals(itemType)) {
                adManageOperationLog.setOperationObject(OperationLogTargetEnum.PLACEMENT_STRATEGY.getTargetValue());
            }
            if (ItemType.TARGET.name().equals(itemType)) {
                adManageOperationLog.setOperationObject(OperationLogTargetEnum.BIDDING_STRATEGY.getTargetValue());
            }
            if (ItemTypeEnum.START_STOP.getItemType().equals(itemType)) {
                if (startStopItemType.equals(StartStopItemTypeEnum.CAMPAIGN)) {
                    adManageOperationLog.setOperationObject(OperationLogTargetEnum.SWITCH_STRATEGY.getTargetValue());
                } else if (startStopItemType.equals(StartStopItemTypeEnum.PRODUCT)) {
                    adManageOperationLog.setOperationObject(OperationLogTargetEnum.SWITCH_PRODUCT_STRATEGY.getTargetValue());
                }
            }
            if (ItemTypeEnum.PORTFOLIO.getItemType().equals(itemType)) {
                adManageOperationLog.setOperationObject(OperationLogTargetEnum.PORTFOLIO_STRATEGY.getTargetValue());
            }
            if (ItemType.AD_GROUP_TARGET.name().equals(itemType)) {
                adManageOperationLog.setOperationObject(OperationLogTargetEnum.BIDDING_STRATEGY.getTargetValue());
            }
        }
    }

    // 执行成功通用处理方法
    protected void filterBaseSuccess(AdManageOperationLog adManageOperationLog, List<AdManageOperationLog> adManageOperationLogList, Boolean flag) {
        adManageOperationLog.setResult(flag ? 0 : 1);
        adManageOperationLogList.add(adManageOperationLog);
        adManageOperationLogService.printAdOtherOperationLog(adManageOperationLogList);
    }

    // 填充基本信息
    protected void filterBaseMessage(String adType, String marketplaceId, String campaignId, String adGroupId, String portfolioId,AdManageOperationLog adManageOperationLog, String targetId) {
        adManageOperationLog.setAdType(adType.toLowerCase());
        adManageOperationLog.setCampaignId(campaignId);
        adManageOperationLog.setAdGroupId(StringUtils.isNotBlank(adGroupId) ? adGroupId : null);
        adManageOperationLog.setPortfolioId(StringUtils.isNotBlank(portfolioId) ? portfolioId : null);
        adManageOperationLog.setMarketplaceId(marketplaceId);
        adManageOperationLog.setModule(OperationLogModuleEnum.AD.getModuleValue());
        adManageOperationLog.setSubModule(OperationLogSubModuleEnum.AD_MANAGE.getSubModuleValue());
        adManageOperationLog.setTargetId(targetId);
    }


    // 操作内容处理
    protected OperationContent processContent(AdManageOperationLog adManageOperationLog, List<String> targetNames, List<String> campaignNames, List<String> targetTypes, String itemType, OperationContent operationContent) {
        if (CollectionUtils.isNotEmpty(targetNames)) {
            adManageOperationLog.setTargetName(StringUtils.join(targetNames, "、"));
        }
        if (CollectionUtils.isNotEmpty(targetTypes)) {
            adManageOperationLog.setTargetType(StringUtils.join(targetTypes, "、"));
        }
        if (ItemTypeEnum.TARGET.getItemType().equals(itemType) && CollectionUtils.isNotEmpty(targetNames)) {
            operationContent.setNewValue(StringUtils.join(targetNames, "、"));
        } else {
            operationContent.setNewValue(StringUtils.join(campaignNames, "、"));
        }
        return operationContent;
    }

    protected void removeArchiveRecord(Integer puid, Integer shopId, Long taskId,String itemType) {
        // 删除任务调度表
        advertiseStrategyScheduleDao.deleteStrategySchedule(puid, shopId,taskId,itemType);
        // 删除受控对象表
        advertiseStrategyStatusDao.deleteStrategyByTaskId(puid, shopId, taskId);
    }

    protected void updateBaseData(Integer puid, Integer updateId, AdvertiseStrategyStatus strategyStatus, List<String> spKeywordIds, List<String> sbKeywordIds) {
        // 回写受控对象表中分时调价状态(0,1)
        if (ItemType.CAMPAIGN.name().equals(strategyStatus.getItemType())) {
            amazonAdCampaignAllDao.updateBudgetPricing(puid, strategyStatus.getShopId(),
                    strategyStatus.getCampaignId(), 1, 1, updateId, strategyStatus.getAdType());
        }
        if (ItemType.CAMPAIGN_PLACEMENT.name().equals(strategyStatus.getItemType())) {
            amazonAdCampaignAllDao.updateSpacePricing(puid, strategyStatus.getShopId(),
                    strategyStatus.getCampaignId(), 1, 1, updateId, strategyStatus.getAdType());
        }
        if (ItemType.TARGET.name().equals(strategyStatus.getItemType())) {
            if (TaskAdType.SP.name().equals(strategyStatus.getAdType())) {
                if (TaskTargetType.autoTarget.name().equals(strategyStatus.getTargetType())) {
                    amazonAdTargetDaoRoutingService.updatePricing(puid, strategyStatus.getShopId(),
                            strategyStatus.getItemId(), 1, 1, updateId);
                } else if (TaskTargetType.productTarget.name().equals(strategyStatus.getTargetType())) {
                    amazonAdTargetDaoRoutingService.updatePricing(puid, strategyStatus.getShopId(),
                            strategyStatus.getItemId(), 1, 1, updateId);
                } else if (TaskTargetType.keywordTarget.name().equals(strategyStatus.getTargetType())) {
                    amazonAdKeywordDaoRoutingService.updatePricing(puid, strategyStatus.getShopId(),
                            strategyStatus.getItemId(), 1, 1, updateId);
                    spKeywordIds.add(strategyStatus.getItemId());
                }
            } else if (TaskAdType.SB.name().equals(strategyStatus.getAdType())) {
                if (TaskTargetType.productTarget.name().equals(strategyStatus.getTargetType())) {
                    amazonSbAdTargetingDao.updatePricing(puid, strategyStatus.getShopId(),
                            strategyStatus.getItemId(), 1, 1, updateId);
                } else if (TaskTargetType.keywordTarget.name().equals(strategyStatus.getTargetType())) {
                    amazonSbAdKeywordDao.updatePricing(puid, strategyStatus.getShopId(),
                            strategyStatus.getItemId(), 1, 1, updateId);
                    sbKeywordIds.add(strategyStatus.getItemId());
                }
            } else if (TaskAdType.SD.name().equals(strategyStatus.getAdType())) {
                amazonSdAdTargetingDao.updatePricing(puid, strategyStatus.getShopId(),
                        strategyStatus.getItemId(), 1, 1, updateId);
            }
        }
    }

    protected void filterUpdateResponse (List<UpdateStrategyResponseVo> responseVoList) {
        List<String> campaignId = responseVoList.stream().filter(e->e.getItemType().equals("CAMPAIGN")
                || e.getItemType().equals("CAMPAIGN_PLACEMENT") || e.getItemType().equals(ItemType.START_STOP.name())).map(UpdateStrategyResponseVo::getItemId).collect(Collectors.toList());
        List<String> autoTargetSpIds = responseVoList.stream().filter(s->s.getAdType().equals("SP")
                && ("autoTarget".equals(s.getTargetType()) || "productTarget".equals(s.getTargetType()))).map(UpdateStrategyResponseVo::getItemId).collect(Collectors.toList());
        List<String> keywordTargetSpIds = responseVoList.stream().filter(s->s.getAdType().equals("SP")
                && "keywordTarget".equals(s.getTargetType())).map(UpdateStrategyResponseVo::getItemId).collect(Collectors.toList());
        List<String> productTargetSbIds = responseVoList.stream().filter(s->s.getAdType().equals("SB")
                && "productTarget".equals(s.getTargetType())).map(UpdateStrategyResponseVo::getItemId).collect(Collectors.toList());
        List<String> keywordTargetSbIds = responseVoList.stream().filter(s->s.getAdType().equals("SB")
                && "keywordTarget".equals(s.getTargetType())).map(UpdateStrategyResponseVo::getItemId).collect(Collectors.toList());
        List<String> targetSdIds = responseVoList.stream().filter(s->s.getAdType().equals("SD")
                && s.getItemType().equals("TARGET")).map(UpdateStrategyResponseVo::getItemId).collect(Collectors.toList());
        List<String> portfolioIds = responseVoList.stream().filter(e -> e.getItemType().equals("PORTFOLIO")).map(UpdateStrategyResponseVo::getItemId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            Map<String,AmazonAdPortfolio> portfolioMap = null;
            List<AmazonAdPortfolio> amazonAdPortfolioList = portfolioDao.getPortfolioList(responseVoList.
                    get(0).getPuid(), responseVoList.get(0).getShopId(), portfolioIds);
            if (CollectionUtils.isNotEmpty(amazonAdPortfolioList)) {
                portfolioMap = amazonAdPortfolioList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, item -> item, (a, b) -> a));
                Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;
                responseVoList.forEach(e->{
                    if (MapUtils.isNotEmpty(finalPortfolioMap) && finalPortfolioMap.containsKey(e.getItemId())) {
                        e.setPortfolioId(e.getItemId());
                        e.setPortfolioName(finalPortfolioMap.get(e.getPortfolioId()).getName());
                        e.setItemName(finalPortfolioMap.get(e.getPortfolioId()).getName());
                    }
                });
            }
        }
        if (CollectionUtils.isNotEmpty(campaignId)) {
            Map<String, AmazonAdCampaignAll> campaignAllMap = null;
            Map<String, AmazonAdPortfolio> portfolioMap = null;
            List<AmazonAdCampaignAll> amazonAdCampaignAllList = amazonAdCampaignAllDao.listByCampaignIdNoType(responseVoList.
                    get(0).getPuid(), responseVoList.get(0).getShopId(),campaignId);
            if (CollectionUtils.isNotEmpty(amazonAdCampaignAllList)) {
                List<String> portfolioIdList = amazonAdCampaignAllList.stream().map(AmazonAdCampaignAll::getPortfolioId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(portfolioIdList)) {
                    List<AmazonAdPortfolio> amazonAdPortfolioList = portfolioDao.getPortfolioList(responseVoList.
                            get(0).getPuid(), responseVoList.get(0).getShopId(), portfolioIdList);
                    if (CollectionUtils.isNotEmpty(amazonAdPortfolioList)) {
                        portfolioMap = amazonAdPortfolioList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, item -> item, (a, b) -> a));
                    }
                }
                campaignAllMap = amazonAdCampaignAllList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item -> item, (a, b) -> a));
                Map<String, AmazonAdCampaignAll> finalCampaignAllMap = campaignAllMap;
                Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;
                responseVoList.forEach(e->{
                    if (MapUtils.isNotEmpty(finalCampaignAllMap) && finalCampaignAllMap.containsKey(e.getItemId())) {
                        AmazonAdCampaignAll campaignAll = finalCampaignAllMap.get(e.getItemId());
                        e.setItemName(campaignAll.getName());
                        e.setCampaignName(campaignAll.getName());
                        e.setCampaignId(campaignAll.getCampaignId());
                        if (MapUtils.isNotEmpty(finalPortfolioMap) && finalPortfolioMap.containsKey(campaignAll.getPortfolioId())) {
                            e.setPortfolioId(campaignAll.getPortfolioId());
                            e.setPortfolioName(finalPortfolioMap.get(campaignAll.getPortfolioId()).getName());
                        }
                    }
                });
            }
        }

        if (CollectionUtils.isNotEmpty(autoTargetSpIds)) {
            Map<String, AmazonAdTargeting> amazonAdTargetingMap = null;
            Map<String,AmazonAdCampaignAll> amazonAdCampaignAllMap = null;
            Map<String, AmazonAdGroup> amazonAdGroupMap = null;
            List<AmazonAdTargeting> targetingList = amazonAdTargetDaoRoutingService.listTargets(responseVoList.get(0).getPuid(),autoTargetSpIds);
            if (CollectionUtils.isNotEmpty(targetingList)) {
                amazonAdTargetingMap = targetingList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdTargeting::getTargetId, item -> item, (a, b) -> a));
                List<String> campaignIdList = targetingList.stream().map(AmazonAdTargeting::getCampaignId).distinct().collect(Collectors.toList());
                List<String> adGroupIdList = targetingList.stream().map(AmazonAdTargeting::getAdGroupId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(campaignIdList)) {
                    List<AmazonAdCampaignAll> amazonAdCampaignAllList = amazonAdCampaignAllDao.listByCampaignIdNoType(responseVoList.
                            get(0).getPuid(), responseVoList.get(0).getShopId(),campaignIdList);
                    if (CollectionUtils.isNotEmpty(amazonAdCampaignAllList)) {
                        amazonAdCampaignAllMap = amazonAdCampaignAllList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item -> item, (a, b) -> a));
                    }
                }
                if (CollectionUtils.isNotEmpty(adGroupIdList)) {
                    List<AmazonAdGroup> amazonAdGroupList = amazonAdGroupDao.getAdGroupByIds(responseVoList.
                            get(0).getPuid(),adGroupIdList);
                    if (CollectionUtils.isNotEmpty(amazonAdGroupList)) {
                        amazonAdGroupMap = amazonAdGroupList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, item -> item, (a, b) -> a));
                    }
                }
                Map<String, AmazonAdTargeting> finalAmazonAdTargetingMap = amazonAdTargetingMap;
                Map<String, AmazonAdCampaignAll> finalAmazonAdCampaignAllMap = amazonAdCampaignAllMap;
                Map<String, AmazonAdGroup> finalAmazonAdGroupMap = amazonAdGroupMap;
                responseVoList.forEach(e->{
                    if (MapUtils.isNotEmpty(finalAmazonAdTargetingMap) && finalAmazonAdTargetingMap.containsKey(e.getItemId())) {
                        AmazonAdTargeting amazonAdTargeting = finalAmazonAdTargetingMap.get(e.getItemId());
                        String name = AutoTargetTypeEnum.getAutoTargetValue(amazonAdTargeting.getTargetingValue());
                        if (StringUtils.isNotBlank(name)) {
                            e.setItemName(name);
                            e.setTargetName(name);
                        } else {
                            e.setItemName(amazonAdTargeting.getTargetingValue());
                            e.setTargetName(amazonAdTargeting.getTargetingValue());
                        }
                        e.setTargetId(amazonAdTargeting.getTargetId());
                        if (MapUtils.isNotEmpty(finalAmazonAdCampaignAllMap) && finalAmazonAdCampaignAllMap.containsKey(amazonAdTargeting.getCampaignId())) {
                            e.setCampaignId(amazonAdTargeting.getCampaignId());
                            e.setCampaignName(finalAmazonAdCampaignAllMap.get(amazonAdTargeting.getCampaignId()).getName());
                        }
                        if (MapUtils.isNotEmpty(finalAmazonAdGroupMap) && finalAmazonAdGroupMap.containsKey(amazonAdTargeting.getAdGroupId())) {
                            e.setAdGroupId(amazonAdTargeting.getAdGroupId());
                            e.setAdGroupName(finalAmazonAdGroupMap.get(amazonAdTargeting.getAdGroupId()).getName());
                        }
                    }
                });
            }
        }

        if (CollectionUtils.isNotEmpty(keywordTargetSpIds)) {
            Map<String, AmazonAdKeyword> amazonAdKeywordMap = null;
            Map<String,AmazonAdCampaignAll> amazonAdCampaignAllMap = null;
            Map<String,AmazonAdGroup> amazonAdGroupMap = null;
            List<AmazonAdKeyword> keywordList = amazonAdKeywordDaoRoutingService.getByKeywordIds(responseVoList.get(0).getPuid(),keywordTargetSpIds);
            if (CollectionUtils.isNotEmpty(keywordList)) {
                amazonAdKeywordMap = keywordList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdKeyword::getKeywordId, item -> item, (a, b) -> a));
                List<String> campaignIdList = keywordList.stream().map(AmazonAdKeyword::getCampaignId).distinct().collect(Collectors.toList());
                List<String> adGroupIdList = keywordList.stream().map(AmazonAdKeyword::getAdGroupId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(campaignIdList)) {
                    List<AmazonAdCampaignAll> amazonAdCampaignAllList = amazonAdCampaignAllDao.listByCampaignIdNoType(responseVoList.
                            get(0).getPuid(), responseVoList.get(0).getShopId(),campaignIdList);
                    if (CollectionUtils.isNotEmpty(amazonAdCampaignAllList)) {
                        amazonAdCampaignAllMap = amazonAdCampaignAllList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item -> item, (a, b) -> a));
                    }
                }
                if (CollectionUtils.isNotEmpty(adGroupIdList)) {
                    List<AmazonAdGroup> amazonAdGroupList = amazonAdGroupDao.getAdGroupByIds(responseVoList.
                            get(0).getPuid(),adGroupIdList);
                    if (CollectionUtils.isNotEmpty(amazonAdGroupList)) {
                        amazonAdGroupMap = amazonAdGroupList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, item -> item, (a, b) -> a));
                    }
                }
                Map<String, AmazonAdKeyword> finalAmazonAdKeywordMap = amazonAdKeywordMap;
                Map<String, AmazonAdCampaignAll> finalAmazonAdCampaignAllMap = amazonAdCampaignAllMap;
                Map<String, AmazonAdGroup> finalAmazonAdGroupMap = amazonAdGroupMap;
                responseVoList.forEach(e->{
                    if (MapUtils.isNotEmpty(finalAmazonAdKeywordMap) && finalAmazonAdKeywordMap.containsKey(e.getItemId())) {
                        AmazonAdKeyword amazonAdKeyword = finalAmazonAdKeywordMap.get(e.getItemId());
                        e.setItemName(amazonAdKeyword.getKeywordText());
                        e.setTargetName(amazonAdKeyword.getKeywordText());
                        e.setTargetId(amazonAdKeyword.getKeywordId());
                        if (MapUtils.isNotEmpty(finalAmazonAdCampaignAllMap) && finalAmazonAdCampaignAllMap.containsKey(amazonAdKeyword.getCampaignId())) {
                            e.setCampaignId(amazonAdKeyword.getCampaignId());
                            e.setCampaignName(finalAmazonAdCampaignAllMap.get(amazonAdKeyword.getCampaignId()).getName());
                        }
                        if (MapUtils.isNotEmpty(finalAmazonAdGroupMap) && finalAmazonAdGroupMap.containsKey(amazonAdKeyword.getAdGroupId())) {
                            e.setAdGroupId(amazonAdKeyword.getAdGroupId());
                            e.setAdGroupName(finalAmazonAdGroupMap.get(amazonAdKeyword.getAdGroupId()).getName());
                        }
                    }
                });
            }
        }

        if (CollectionUtils.isNotEmpty(productTargetSbIds)) {
            Map<String,AmazonSbAdTargeting> amazonSbAdTargetingMap = null;
            Map<String,AmazonAdCampaignAll> amazonAdCampaignAllMap = null;
            Map<String,AmazonSbAdGroup> amazonSbAdGroupMap = null;
            List<AmazonSbAdTargeting> targetingList = amazonSbAdTargetingDao.listByTargetId(responseVoList.get(0).getPuid(),responseVoList.get(0).getShopId(),productTargetSbIds);
            if (CollectionUtils.isNotEmpty(targetingList)) {
                amazonSbAdTargetingMap = targetingList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdTargeting::getTargetId, item -> item, (a, b) -> a));
                List<String> campaignIdList = targetingList.stream().map(AmazonSbAdTargeting::getCampaignId).distinct().collect(Collectors.toList());
                List<String> adGroupIdList = targetingList.stream().map(AmazonSbAdTargeting::getAdGroupId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(campaignIdList)) {
                    List<AmazonAdCampaignAll> amazonAdCampaignAllList = amazonAdCampaignAllDao.listByCampaignIdNoType(responseVoList.
                            get(0).getPuid(), responseVoList.get(0).getShopId(),campaignIdList);
                    if (CollectionUtils.isNotEmpty(amazonAdCampaignAllList)) {
                        amazonAdCampaignAllMap = amazonAdCampaignAllList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item -> item, (a, b) -> a));
                    }
                }
                if (CollectionUtils.isNotEmpty(adGroupIdList)) {
                    List<AmazonSbAdGroup> amazonSbAdGroupList = amazonSbAdGroupDao.getAdGroupByIds(responseVoList.
                            get(0).getPuid(),adGroupIdList);
                    if (CollectionUtils.isNotEmpty(amazonSbAdGroupList)) {
                        amazonSbAdGroupMap = amazonSbAdGroupList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, item -> item, (a, b) -> a));
                    }
                }
                Map<String, AmazonAdCampaignAll> finalAmazonAdCampaignAllMap = amazonAdCampaignAllMap;
                Map<String, AmazonSbAdGroup> finalAmazonAdGroupMap = amazonSbAdGroupMap;
                Map<String, AmazonSbAdTargeting> finalAmazonSbAdTargetingMap = amazonSbAdTargetingMap;
                responseVoList.forEach(e->{
                    if (MapUtils.isNotEmpty(finalAmazonSbAdTargetingMap) && finalAmazonSbAdTargetingMap.containsKey(e.getItemId())) {
                        AmazonSbAdTargeting amazonSbAdTargeting = finalAmazonSbAdTargetingMap.get(e.getItemId());
                        e.setItemName(amazonSbAdTargeting.getTargetText());
                        e.setTargetName(amazonSbAdTargeting.getTargetText());
                        e.setTargetId(amazonSbAdTargeting.getTargetId());
                        if (MapUtils.isNotEmpty(finalAmazonAdCampaignAllMap) && finalAmazonAdCampaignAllMap.containsKey(amazonSbAdTargeting.getCampaignId())) {
                            e.setCampaignId(amazonSbAdTargeting.getCampaignId());
                            e.setCampaignName(finalAmazonAdCampaignAllMap.get(amazonSbAdTargeting.getCampaignId()).getName());
                        }
                        if (MapUtils.isNotEmpty(finalAmazonAdGroupMap) && finalAmazonAdGroupMap.containsKey(amazonSbAdTargeting.getAdGroupId())) {
                            e.setAdGroupId(amazonSbAdTargeting.getAdGroupId());
                            e.setAdGroupName(finalAmazonAdGroupMap.get(amazonSbAdTargeting.getAdGroupId()).getName());
                        }
                    }
                });
            }
        }

        if (CollectionUtils.isNotEmpty(keywordTargetSbIds)) {
            Map<String,AmazonSbAdKeyword> amazonSbAdKeywordMap = null;
            Map<String,AmazonAdCampaignAll> amazonAdCampaignAllMap = null;
            Map<String,AmazonSbAdGroup> amazonSbAdGroupMap = null;
            List<AmazonSbAdKeyword> amazonSbAdKeywords = amazonSbAdKeywordDao.listByKeywordId(responseVoList.get(0).getPuid(),responseVoList.get(0).getShopId(),keywordTargetSbIds);
            if (CollectionUtils.isNotEmpty(amazonSbAdKeywords)) {
                amazonSbAdKeywordMap = amazonSbAdKeywords.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdKeyword::getKeywordId, item -> item, (a, b) -> a));
                List<String> campaignIdList = amazonSbAdKeywords.stream().map(AmazonSbAdKeyword::getCampaignId).distinct().collect(Collectors.toList());
                List<String> adGroupIdList = amazonSbAdKeywords.stream().map(AmazonSbAdKeyword::getAdGroupId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(campaignIdList)) {
                    List<AmazonAdCampaignAll> amazonAdCampaignAllList = amazonAdCampaignAllDao.listByCampaignIdNoType(responseVoList.
                            get(0).getPuid(), responseVoList.get(0).getShopId(),campaignIdList);
                    if (CollectionUtils.isNotEmpty(amazonAdCampaignAllList)) {
                        amazonAdCampaignAllMap = amazonAdCampaignAllList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item -> item, (a, b) -> a));
                    }
                }
                if (CollectionUtils.isNotEmpty(adGroupIdList)) {
                    List<AmazonSbAdGroup> amazonSbAdGroupList = amazonSbAdGroupDao.getAdGroupByIds(responseVoList.
                            get(0).getPuid(),adGroupIdList);
                    if (CollectionUtils.isNotEmpty(amazonSbAdGroupList)) {
                        amazonSbAdGroupMap = amazonSbAdGroupList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, item -> item, (a, b) -> a));
                    }
                }
                Map<String, AmazonAdCampaignAll> finalAmazonAdCampaignAllMap = amazonAdCampaignAllMap;
                Map<String, AmazonSbAdGroup> finalAmazonAdGroupMap = amazonSbAdGroupMap;
                Map<String, AmazonSbAdKeyword> finalAmazonSbAdkeywordMap = amazonSbAdKeywordMap;
                responseVoList.forEach(e->{
                    if (MapUtils.isNotEmpty(finalAmazonSbAdkeywordMap) && finalAmazonSbAdkeywordMap.containsKey(e.getItemId())) {
                        AmazonSbAdKeyword amazonSbAdKeyword = finalAmazonSbAdkeywordMap.get(e.getItemId());
                        e.setItemName(amazonSbAdKeyword.getKeywordId());
                        e.setTargetName(amazonSbAdKeyword.getKeywordText());
                        e.setTargetId(amazonSbAdKeyword.getKeywordId());
                        if (MapUtils.isNotEmpty(finalAmazonAdCampaignAllMap) && finalAmazonAdCampaignAllMap.containsKey(amazonSbAdKeyword.getCampaignId())) {
                            e.setCampaignId(amazonSbAdKeyword.getCampaignId());
                            e.setCampaignName(finalAmazonAdCampaignAllMap.get(amazonSbAdKeyword.getCampaignId()).getName());
                        }
                        if (MapUtils.isNotEmpty(finalAmazonAdGroupMap) && finalAmazonAdGroupMap.containsKey(amazonSbAdKeyword.getAdGroupId())) {
                            e.setAdGroupId(amazonSbAdKeyword.getAdGroupId());
                            e.setAdGroupName(finalAmazonAdGroupMap.get(amazonSbAdKeyword.getAdGroupId()).getName());
                        }
                    }
                });
            }
        }

        if (CollectionUtils.isNotEmpty(targetSdIds)) {
            Map<String,AmazonSdAdTargeting> amazonSdAdTargetingMap = null;
            Map<String,AmazonAdCampaignAll> amazonAdCampaignAllMap = null;
            Map<String,AmazonSdAdGroup> amazonSdAdGroupMap = null;
            List<AmazonSdAdTargeting> amazonSdAdTargetingList = amazonSdAdTargetingDao.listByTargetId(responseVoList.get(0).getPuid(),responseVoList.get(0).getShopId(),targetSdIds);
            if (CollectionUtils.isNotEmpty(amazonSdAdTargetingList)) {
                amazonSdAdTargetingMap = amazonSdAdTargetingList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSdAdTargeting::getTargetId, item -> item, (a, b) -> a));
                List<String> adGroupIdList = amazonSdAdTargetingList.stream().map(AmazonSdAdTargeting::getAdGroupId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(adGroupIdList)) {
                    List<AmazonSdAdGroup> amazonSdAdGroupList = amazonSdAdGroupDao.listByGroupId(responseVoList.
                            get(0).getPuid(),responseVoList.get(0).getShopId(),adGroupIdList);
                    if (CollectionUtils.isNotEmpty(amazonSdAdGroupList)) {
                        amazonSdAdGroupMap = amazonSdAdGroupList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, item -> item, (a, b) -> a));
                        List<String> campaignIdList = amazonSdAdGroupList.stream().map(AmazonSdAdGroup::getCampaignId).distinct().collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(campaignIdList)) {
                            List<AmazonAdCampaignAll> amazonAdCampaignAllList = amazonAdCampaignAllDao.listByCampaignIdNoType(responseVoList.
                                    get(0).getPuid(), responseVoList.get(0).getShopId(),campaignIdList);
                            if (CollectionUtils.isNotEmpty(amazonAdCampaignAllList)) {
                                amazonAdCampaignAllMap = amazonAdCampaignAllList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item -> item, (a, b) -> a));
                            }
                        }
                    }
                }
                Map<String, AmazonAdCampaignAll> finalAmazonAdCampaignAllMap = amazonAdCampaignAllMap;
                Map<String, AmazonSdAdGroup> finalAmazonAdGroupMap = amazonSdAdGroupMap;
                Map<String, AmazonSdAdTargeting> finalAmazonSbAdkeywordMap = amazonSdAdTargetingMap;
                responseVoList.forEach(e->{
                    if (MapUtils.isNotEmpty(finalAmazonSbAdkeywordMap) && finalAmazonSbAdkeywordMap.containsKey(e.getItemId())) {
                        AmazonSdAdTargeting amazonSdAdTargeting = finalAmazonSbAdkeywordMap.get(e.getItemId());
                        e.setItemName(amazonSdAdTargeting.getTargetText());
                        e.setTargetName(amazonSdAdTargeting.getTargetText());
                        e.setTargetId(amazonSdAdTargeting.getTargetId());
                        if (MapUtils.isNotEmpty(finalAmazonAdGroupMap) && finalAmazonAdGroupMap.containsKey(amazonSdAdTargeting.getAdGroupId())) {
                            e.setAdGroupId(amazonSdAdTargeting.getAdGroupId());
                            e.setAdGroupName(finalAmazonAdGroupMap.get(amazonSdAdTargeting.getAdGroupId()).getName());
                            if (MapUtils.isNotEmpty(finalAmazonAdCampaignAllMap) && finalAmazonAdCampaignAllMap.containsKey(finalAmazonAdGroupMap.get(amazonSdAdTargeting.getAdGroupId()).getCampaignId())) {
                                e.setCampaignId(finalAmazonAdGroupMap.get(amazonSdAdTargeting.getAdGroupId()).getCampaignId());
                                e.setCampaignName(finalAmazonAdCampaignAllMap.get(finalAmazonAdGroupMap.get(amazonSdAdTargeting.getAdGroupId()).getCampaignId()).getName());
                            }
                        }
                    }
                });
            }
        }
    }

    // 填充受控对象信息
    protected void filterMatchData(Integer puid, List<UpdateStrategyVo> updateStrategyVoList, List<AdManageOperationLog> list, List<UpdateStrategyResponseVo> responseVoList, Integer updateId) {
        if (CollectionUtils.isEmpty(updateStrategyVoList)) {
            return;
        }
        List<Long> statusIdList = updateStrategyVoList.stream().map(UpdateStrategyVo::getStatusId).collect(Collectors.toList());
        List<String> errorList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(responseVoList)) {
            errorList = responseVoList.stream().map(UpdateStrategyResponseVo::getItemId).collect(Collectors.toList());
        }
        List<AdvertiseStrategyStatus> strategyStatuses = advertiseStrategyStatusDao.getListByLongIdList(puid, statusIdList);
        Map<Long,AdvertiseStrategyStatus> advertiseStrategyStatusMap = strategyStatuses.stream().collect(Collectors.toMap(AdvertiseStrategyStatus::getId, Function.identity(), (a, b)-> a));
        List<String> finalErrorList = errorList;
        if (ItemTypeEnum.CAMPAIGN.getItemType().equals(strategyStatuses.get(0).getItemType())) {
            updateStrategyVoList.forEach(e->{
                OperationContent operationContent = new OperationContent();
                if (CollectionUtils.isNotEmpty(finalErrorList) && finalErrorList.contains(e.getItemId())) {
                    return;
                }
                AdvertiseStrategyStatus advertiseStrategyStatus = advertiseStrategyStatusMap.get(e.getStatusId());
                AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
                adManageOperationLog.setAction(OperationLogActionEnum.EDIT.getOperationValue());
                adManageOperationLog.setPuid(puid);
                adManageOperationLog.setUid(updateId);
                adManageOperationLog.setShopId(advertiseStrategyStatus.getShopId());
                adManageOperationLog.setMarketplaceId(advertiseStrategyStatus.getMarketplaceId());
                adManageOperationLog.setTargetId(advertiseStrategyStatus.getItemId());
                adManageOperationLog.setCampaignId(advertiseStrategyStatus.getCampaignId());
                adManageOperationLog.setAdType(advertiseStrategyStatus.getAdType().toLowerCase());
                adManageOperationLog.setOperationObject(OperationLogTargetEnum.BUDGET_STRATEGY.getTargetValue());
                operationContent.setTitle(Constants.BUDGET_VALUE_TITLE);
                operationContent.setPreviousValue(e.getOriginBudgetValue().toString());
                operationContent.setNewValue(e.getBudgetValue().toString());
                adManageOperationLog.setOperationContent(JSONUtil.objectToJson(operationContent));
                list.add(adManageOperationLog);
            });
        } else if (ItemTypeEnum.CAMPAIGN_PLACEMENT.getItemType().equals(strategyStatuses.get(0).getItemType())) {
            updateStrategyVoList.forEach(e->{
                if (CollectionUtils.isNotEmpty(finalErrorList) && finalErrorList.contains(e.getItemId())) {
                    return;
                }
                OperationContent operationContent = new OperationContent();
                AdvertiseStrategyStatus advertiseStrategyStatus = advertiseStrategyStatusMap.get(e.getStatusId());
                AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
                adManageOperationLog.setAction(OperationLogActionEnum.EDIT.getOperationValue());
                adManageOperationLog.setPuid(puid);
                adManageOperationLog.setUid(updateId);
                adManageOperationLog.setShopId(advertiseStrategyStatus.getShopId());
                adManageOperationLog.setMarketplaceId(advertiseStrategyStatus.getMarketplaceId());
                adManageOperationLog.setTargetId(advertiseStrategyStatus.getItemId());
                adManageOperationLog.setCampaignId(advertiseStrategyStatus.getCampaignId());
                adManageOperationLog.setAdType(advertiseStrategyStatus.getAdType().toLowerCase());
                adManageOperationLog.setOperationObject(OperationLogTargetEnum.PLACEMENT_STRATEGY.getTargetValue());
                operationContent.setTitle(Constants.PLACEMENT_VALUE_TITLE);
                if (e.getAdPlaceTopValue() != null) {
                    operationContent.setPreviousValue(e.getOriginAdPlaceTopValue().toString());
                    operationContent.setNewValue(e.getAdPlaceTopValue().toString());
                } else if (e.getAdPlaceProductValue() != null) {
                    operationContent.setPreviousValue(e.getOriginAdPlaceProductValue().toString());
                    operationContent.setNewValue(e.getAdPlaceProductValue().toString());
                } else if (StringUtils.isNotBlank(e.getStrategy())) {
                    operationContent.setPreviousValue(e.getOriginStrategy());
                    operationContent.setNewValue(e.getStrategy());
                } else if (e.getAdOtherValue() != null) {
                    if (e.getOriginAdOtherValue() != null) {
                        operationContent.setPreviousValue(e.getOriginAdOtherValue().toString());
                    } else {
                        operationContent.setPreviousValue("0");
                    }
                    operationContent.setNewValue(e.getAdOtherValue().toString());
                }
                adManageOperationLog.setOperationContent(JSONUtil.objectToJson(operationContent));
                list.add(adManageOperationLog);
            });
        } else if (ItemTypeEnum.TARGET.getItemType().equals(strategyStatuses.get(0).getItemType())) {
            updateStrategyVoList.forEach(e->{
                if (CollectionUtils.isNotEmpty(finalErrorList) && finalErrorList.contains(e.getItemId())) {
                    return;
                }
                OperationContent operationContent = new OperationContent();
                AdvertiseStrategyStatus advertiseStrategyStatus = advertiseStrategyStatusMap.get(e.getStatusId());
                AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
                adManageOperationLog.setAction(OperationLogActionEnum.EDIT.getOperationValue());
                adManageOperationLog.setPuid(puid);
                adManageOperationLog.setUid(updateId);
                adManageOperationLog.setShopId(advertiseStrategyStatus.getShopId());
                adManageOperationLog.setMarketplaceId(advertiseStrategyStatus.getMarketplaceId());
                adManageOperationLog.setTargetId(advertiseStrategyStatus.getItemId());
                adManageOperationLog.setTargetType(advertiseStrategyStatus.getTargetType());
                adManageOperationLog.setTargetName(advertiseStrategyStatus.getTargetName());
                adManageOperationLog.setAdType(advertiseStrategyStatus.getAdType().toLowerCase());
                adManageOperationLog.setOperationObject(OperationLogTargetEnum.BIDDING_STRATEGY.getTargetValue());
                operationContent.setTitle(Constants.BIDDING_VALUE_TITLE);
                operationContent.setPreviousValue(e.getOriginBiddingValue().toString());
                operationContent.setNewValue(e.getBiddingValue().toString());
                adManageOperationLog.setOperationContent(JSONUtil.objectToJson(operationContent));
                list.add(adManageOperationLog);
            });
        } else if (ItemTypeEnum.START_STOP.getItemType().equals(strategyStatuses.get(0).getItemType())) {
            String startStopItemType = strategyStatuses.get(0).getStartStopItemType();
            updateStrategyVoList.forEach(e -> {
                OperationContent operationContent = new OperationContent();
                AdvertiseStrategyStatus advertiseStrategyStatus = advertiseStrategyStatusMap.get(e.getStatusId());
                AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
                adManageOperationLog.setAction(OperationLogActionEnum.EDIT.getOperationValue());
                adManageOperationLog.setPuid(puid);
                adManageOperationLog.setShopId(advertiseStrategyStatus.getShopId());
                adManageOperationLog.setUid(updateId);
                adManageOperationLog.setMarketplaceId(advertiseStrategyStatus.getMarketplaceId());
                adManageOperationLog.setTargetId(advertiseStrategyStatus.getItemId());
                adManageOperationLog.setCampaignId(advertiseStrategyStatus.getCampaignId());
                adManageOperationLog.setAdType(advertiseStrategyStatus.getType());
                adManageOperationLog.setAsin(advertiseStrategyStatus.getAsin());
                adManageOperationLog.setSku(advertiseStrategyStatus.getSku());
                if (StartStopItemTypeEnum.CAMPAIGN.getValue().equals(startStopItemType)) {
                    adManageOperationLog.setOperationObject(OperationLogTargetEnum.SWITCH_STRATEGY.getTargetValue());
                } else if (StartStopItemTypeEnum.PRODUCT.getValue().equals(startStopItemType)) {
                    adManageOperationLog.setOperationObject(OperationLogTargetEnum.SWITCH_PRODUCT_STRATEGY.getTargetValue());
                }
                operationContent.setTitle(Constants.STATE_VALUE_TITLE);
                operationContent.setPreviousValue(e.getOriginState());
                operationContent.setNewValue(e.getState());
                adManageOperationLog.setOperationContent(JSONUtil.objectToJson(operationContent));
                list.add(adManageOperationLog);
            });
        }
        filterBaseSuccess(new AdManageOperationLog(), list, true);
    }

    /**
     * 同步更新模板日志记录
     * @param puid
     * @param template
     * @param updateStrategyVoList
     */
    protected void addSyncTemplateOperationLog(Integer puid, AdvertiseStrategyTemplate template, List<UpdateStrategyVo> updateStrategyVoList, Integer updateId) {
        List<AdManageOperationLog> list = new ArrayList<>();
        List<OperationContent> contentList = new ArrayList<>();
        List<Long> statusIdList = updateStrategyVoList.stream().map(UpdateStrategyVo::getStatusId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(statusIdList)) {
            return;
        }
        List<AdvertiseStrategyStatus> strategyStatuses = advertiseStrategyStatusDao.getListByLongIdList(puid, statusIdList);
        OperationContent operationContent = new OperationContent();
        operationContent.setTitle("同步更新为最新模板");
        contentList.add(operationContent);
        String content = JSONUtil.objectToJson(operationContent);

        for (AdvertiseStrategyStatus strategyStatus : strategyStatuses) {
            AdManageOperationLog adManageOperationLog = new AdManageOperationLog();

            adManageOperationLog.setPuid(puid);
            adManageOperationLog.setUid(updateId);
            adManageOperationLog.setShopId(template.getShopId());
            adManageOperationLog.setMarketplaceId(template.getMarketplaceId());
            adManageOperationLog.setTemplateId(template.getId());
            if (ItemTypeEnum.PORTFOLIO.getItemType().equals(strategyStatus.getItemType())) {
                adManageOperationLog.setPortfolioId(strategyStatus.getItemId());
            }
            adManageOperationLog.setCampaignId(strategyStatus.getCampaignId());
            adManageOperationLog.setAdType(strategyStatus.getAdType().toLowerCase());
            adManageOperationLog.setAdGroupId(strategyStatus.getAdGroupId());
            adManageOperationLog.setTargetName(strategyStatus.getTargetName());
            adManageOperationLog.setTargetType(strategyStatus.getTargetType());
            adManageOperationLog.setTargetId(strategyStatus.getItemId());
            adManageOperationLog.setAsin(strategyStatus.getAsin());
            adManageOperationLog.setSku(strategyStatus.getSku());

            adManageOperationLog.setAdjustmentRange(OperationLogAdjustmentRangeEnum.SYNC_LASTED_TEMPLATED.getAdjustmentType());
            adManageOperationLog.setOperationContent(content);
            adManageOperationLog.setResult(0);
            adManageOperationLog.setUnixTimestamp(System.currentTimeMillis());
            adManageOperationLog.setId(MD5Util.getMD5(adManageOperationLog.deDeduplicationId()));
            adManageOperationLog.setMessage(adManageOperationLog.handleMessage(contentList));
            list.add(adManageOperationLog);
        }
        adManageOperationLogService.printAdOperationLog(list);
    }

}
