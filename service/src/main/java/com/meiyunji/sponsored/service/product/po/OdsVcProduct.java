package com.meiyunji.sponsored.service.product.po;

import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.util.Date;

@Data
@DbTable(value = "ods_t_vc_product")
public class OdsVcProduct extends BasePo {

    @DbColumn(value = "id", key = true, autoIncrement = true)
    private Long id;

    @DbColumn(value = "puid")
    private Integer puid;
    /**
     * VC店铺id
     */
    @DbColumn(value = "shop_id")
    private Integer vcShopId;
    /**
     * 站点id
     */
    @DbColumn(value = "marketplace_id")
    private String marketplaceId;
    /**
     * asin
     */
    @DbColumn(value = "asin")
    private String asin;
    /**
     * 父asin
     */
    @DbColumn(value = "parent_asin")
    private String parentAsin;
    /**
     * 变种
     */
    @DbColumn(value = "child_asins")
    private String childAsins;
    /**
     * 图片
     */
    @DbColumn(value = "main_image")
    private String mainImage;

    /**
     * msku
     */
    @DbColumn(value = "msku")
    private String msku;

    /**
     * 商品ID
     */
    @DbColumn(value = "commodity_id")
    private Long commodityId;

    /**
     * 配对时间：用于记录配对商品或取消配对的时间
     */
    @DbColumn(value = "match_commodity_time")
    private Date mathCommodityTime;

    /**
     * 产品名称
     */
    @DbColumn(value = "title")
    private String title;
    /**
     * 品牌
     */
    @DbColumn(value = "brand")
    private String brand;
    /**
     * manufacturer
     */
    @DbColumn(value = "manufacturer")
    private String manufacturer;
    /**
     * 供应商代码
     */
    @DbColumn(value = "manufacturer_code")
    private String manufacturerCode;
    /**
     * 变种属性字符串
     */
    @DbColumn(value = "variation_child_str")
    private String variationChildStr;
    /**
     * partNumber
     */
    @DbColumn(value = "part_number")
    private String partNumber;
    /**
     * 是否虚拟捆绑产品 0:不是 1:是
     */
    @DbColumn(value = "product_bundle")
    private Integer productBundle;
    /**
     * 是否时变种,0单，1组合sku，2多品
     */
    @DbColumn(value = "is_variation")
    private Integer isVariation;
    /**
     * 商品编码
     */
    @DbColumn(value = "standard_product_id")
    private String standardProductId;
    /**
     * 商品类型
     */
    @DbColumn(value = "standard_product_type")
    private String standardProductType;
    /**
     * 标准价格
     */
    @DbColumn(value = "standard_price")
    private Double standardPrice;
    /**
     * 价格币种
     */
    @DbColumn(value = "standard_price_currency")
    private String standardPriceCurrency;

    /**
     * 成本价
     */
    @DbColumn(value = "net_cost_amount")
    private Double netCostAmount;
    /**
     * 成本价币种
     */
    @DbColumn(value = "net_cost_amount_currency")
    private String netCostAmountCurrency;

    /**
     * 产品状态 -1：不完整（仅有ASIN，用户不可见） 0：删除 1：正常
     */
    @DbColumn(value = "status")
    private Integer status;
    /**
     * 大类目排名(排序用)
     */
    @DbColumn(value = "display_group_rank")
    private Integer displayGroupRank;
    /**
     * 大类目排名
     */
    @DbColumn(value = "display_group_ranks")
    private String displayGroupRanks;
    /**
     * 小类目排名(排序用)
     */
    @DbColumn(value = "classification_rank")
    private Integer classificationRank;
    /**
     * 小类目排名
     */
    @DbColumn(value = "classification_ranks")
    private String classificationRanks;
    /**
     * 同步时间
     */
    @DbColumn(value = "last_sync_time")
    private Date lastSyncTime;

    /**
     * 创建人id
     */
    @DbColumn(value = "create_id")
    private Integer createId;
    /**
     * 修改人id
     */
    @DbColumn(value = "update_id")
    private Integer updateId;
}
