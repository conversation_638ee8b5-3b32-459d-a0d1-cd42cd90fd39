package com.meiyunji.sponsored.service.account.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseDao;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.post.response.GetUserInfoResponse;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface IUserDao extends IBaseDao<User> {

    /**
     * 查询所有主账号puid
     * @return
     */
    List<User> listAllMainUser();


    List<Integer> getAllPuid();

    List<User> listByIds(Integer puid, List<Integer> ids);

    /**
     * 查询所有免费用户
     * 根据条件planType = 0作为判断条件
     * @return
     */
    List<User> listAllFreeUser();

    List<User> batchGetUser(Date startTime, Date endTime, int baseId, int limit);

    /**
     * 查询主用户
     */
    List<User> batchGetMainUser(Date startTime, Date endTime, int baseId, int limit);

    List<User> batchGetUserNOTRepatePuid(Date startTime, Date endTime, int baseId, int limit);

    List<User> listNoDeleteUser(int puid);

    List<User> listNoDeleteUserByCreateTime(Date createTime);

    List<User> getUserIdByName(Integer puid, String searchType, List<String> otherSearchValues);

    List<GetUserInfoResponse.UserInfo> getUserInfoByIds(Integer puid, Set<Integer> uids);

    List<User> listAllUserInfo(Integer puid, String searchValue, String searchType, List<Integer> postUid);
}