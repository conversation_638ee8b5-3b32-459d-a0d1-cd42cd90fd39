package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadras.types.schedule.AdvertiseRuleTaskExecuteRecordV2Message;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.AmazonAd;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.LogicType;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.SelectBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.adGroup.AdGroupDefaultOrderEnum;
import com.meiyunji.sponsored.service.autoRule.vo.AdGroupAutoRuleParam;
import com.meiyunji.sponsored.service.autoRule.vo.AllUpdateAutoRuleParam;
import com.meiyunji.sponsored.service.autoRule.vo.PerformOperationJson;
import com.meiyunji.sponsored.service.cpc.bo.AllGroupOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdGroupTypeBO;
import com.meiyunji.sponsored.service.enums.GroupTypeEnum;
import com.meiyunji.sponsored.service.export.dto.DownloadCenterGroupBaseDataBO;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.qo.ReportAdvancedFilterBaseQo;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.enums.AutoRuleItemTypeEnum;
import com.meiyunji.sponsored.service.enums.AutoRuleOperationTypeEnum;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.gps.vo.GpsAdGroup;
import com.meiyunji.sponsored.service.strategy.vo.AdStrategyGroupParam;
import com.meiyunji.sponsored.service.strategy.vo.ControlledObjectParam;
import com.meiyunji.sponsored.service.strategyTask.vo.ProcessTaskParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.SingleColumnRowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * AmazonAdGroup
 *
 * <AUTHOR>
 */
@Repository
public class AmazonAdGroupDaoImpl extends BaseShardingDaoImpl<AmazonAdGroup> implements IAmazonAdGroupDao {

    @Override
    public boolean exist(Integer puid, Integer shopId, String campainId, String name) {
        String sql = "select count(*) from t_amazon_ad_group where puid = ? and shop_id = ? and campaign_id=? and `name`=? and state in ('enabled','paused')";
        return getJdbcTemplate(puid).queryForObject(sql, Boolean.class, puid, shopId, campainId, name);
    }

    @Override
    public boolean exist(Integer puid, Integer shopId, String adGroupId) {
        String sql = "select count(*) from t_amazon_ad_group where puid = ? and shop_id = ? and ad_group_id=?";
        return getJdbcTemplate(puid).queryForObject(sql, Boolean.class, puid, shopId, adGroupId);
    }

    @Override
    public List<AmazonAdGroup> getListByCampaignId(int puid, Integer shopId, String marketPlaceId, List<String> campaignIds, String orderField, String orderValue, String countDays) {
       /* StringBuilder selectSql = new StringBuilder(" SELECT t1.`id` id,t1.`ad_group_id` ad_group_id,t1.`campaign_id` campaign_id,t1.`state` state,")
                .append("t1.`name` `name`,t1.`default_bid` default_bid,t1.`ad_group_type` ad_group_type,t1.publish_state publish_state,t1.error_msg error_msg,t2.`cpc` cpc,t2.`cpc_rmb` cpcRmb,t2.`cpc_usd` cpcUsd,t2.`cost` cost,")
                .append("t2.`cost_rmb` costRmb,t2.`cost_usd` costUsd,t2.`sales` sales,t2.`sales_rmb` salesRmb,t2.`sales_usd` salesUsd,t2.`acos` `acos`,t2.`impressions` impressions,")
                .append("t2.`clicks` clicks,t2.`order_num` orderNum,t2.`sale_num` saleNum,t2.`click_rate` clickRate,t2.`sales_conversion_rate` salesConversionRate ")
                .append(" FROM `t_amazon_ad_group` t1 LEFT JOIN `t_amazon_ad_list_report` t2 ON t1.puid = t2.`puid` AND t1.`shop_id` = t2.`shop_id` ")
                .append("AND t1.`marketplace_id` = t2.`marketplace_id` AND t1.`ad_group_id` = t2.`item_id` ");
        if (Constants.SUM_REPORT_WEEK.equals(countDays)) {
            selectSql.append(" AND t2.`days`='week' ");
        } else if (Constants.SUM_REPORT_MONTH.equals(countDays)) {
            selectSql.append(" AND t2.`days`='month' ");
        } else { //默认取昨天
            selectSql.append(" AND t2.`days`='yesterday' ");
        }
        selectSql.append(" where  t1.puid=:puid and t1.shop_id=:shopId and t1.marketplace_id=:marketPlaceId and t1.campaign_id in (:campaignIds)");
        orderField = Constants.getOrderField(orderField);
        if (StringUtils.isNotBlank(orderField)) {
            selectSql.append(" order by ").append(orderField);
            if ("desc".equals(orderField)) {
                selectSql.append(" desc ");
            }
        } else {
            selectSql.append(" order by t1.update_time desc");
        }
        NamedParameterJdbcTemplate named = new NamedParameterJdbcTemplate(getJdbcTemplate(puid));
        return named.query(selectSql.toString(), new MapSqlParameterSource() {{
            addValue("puid", puid);
            addValue("shopId", shopId);
            addValue("marketPlaceId", marketPlaceId);
            addValue("campaignIds", campaignIds);
        }}, getMapper());*/
        return null;
    }

    @Override
    public List<AmazonAdGroup> getListByAdGroupIds(Integer puid, Integer shopId, String adGroupIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inString("ad_group_id", adGroupIds)
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonAdGroup> getListByAdGroupIds(Integer puid, Integer shopId, List<String> adGroupIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("ad_group_id", adGroupIds.toArray())
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonAdGroup> getListByCampaignIds(Integer puid, Integer shopId, List<String> campaignIdList) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("campaign_id", campaignIdList.toArray(new String[]{}))
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonAdGroup> getGroupsByCampaignIdGroupId(Integer puid, String marketplaceId, List<Integer> shopIds, List<String> campaignIds, List<String> groupIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select ad_group_id from ").append(this.getJdbcHelper().getTable())
                .append(" where puid=? and marketplace_id=? ");
        argsList.add(puid);
        argsList.add(marketplaceId);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        sql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
        sql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, argsList));
        return getJdbcTemplate(puid).query(sql.toString(), argsList.toArray(), new ObjectMapper<>(AmazonAdGroup.class));
    }

    @Override
    public List<String> getAdGroupIds(Integer puid, Integer shopId, String marketPlaceId, List<String> adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketPlaceId)
                .inStrList("ad_group_id", adGroupId.toArray(new String[]{}))
                .build();
        return listDistinctFieldByCondition(puid, "ad_group_id", builder, String.class);
    }

    @Override
    public List<AmazonAdGroup> getAdGroupByIds(Integer puid, Integer shopId, String marketPlaceId, List<String> adGroupIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("ad_group_id", adGroupIds.toArray(new String[]{}))
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonAdGroup> listByGroupIdsAndShopIdList(Integer puid, List<Integer> shopIdList, List<String> adGroupIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopIdList.toArray())
                .inStrList("ad_group_id", adGroupIds.toArray(new String[]{}))
                .build();
        return listByCondition(puid, builder);
    }

    public List<AmazonAdGroup> listInfoByGroupInfoAndShopIdList(Integer puid, List<Integer> shopIdList, List<String> adGroupIds) {
        StringBuilder sb = new StringBuilder();
        sb.append(" select 'sp' as `type`, ad_group_id, campaign_id, name, shop_id, marketplace_id, default_bid ")
                .append(" from ").append(this.getJdbcHelper().getTable()).append(" r ");
        List<Object> argsList = new ArrayList<>();
        sb.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(adGroupIds)) {
            sb.append(SqlStringUtil.dealInList("ad_group_id", adGroupIds, argsList));
        }
        return getJdbcTemplate(puid).query(sb.toString(), argsList.toArray(), new ObjectMapper<>(AmazonAdGroup.class));
    }

    @Override
    public List<AmazonAdGroup> getAdGroupByIdsAndShopIdList(Integer puid, List<Integer> shopIdList, List<String> adGroupId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inIntList("shop_id", shopIdList.toArray(new Integer[shopIdList.size()]))
                .inStrList("ad_group_id", adGroupId.toArray(new String[adGroupId.size()]));
        builder.groupBy("puid", "shop_id", "campaign_id");
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdGroup> getAdGroupByIds(Integer puid, List<String> adGroupIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inStrList("ad_group_id", adGroupIds.toArray(new String[]{}))
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public void insertOnDuplicateKeyUpdate(Integer puid, List<AmazonAdGroup> list) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_ad_group` (`puid`,`shop_id`,`marketplace_id`,`ad_group_id`,")
                .append("`campaign_id`,`profile_id`,`name`,`default_bid`,`state`,`serving_status`,create_time, update_time) VALUES ");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdGroup amazonAdGroup : list) {
            sql.append("(?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(amazonAdGroup.getShopId());
            argsList.add(amazonAdGroup.getMarketplaceId());
            argsList.add(amazonAdGroup.getAdGroupId());
            argsList.add(amazonAdGroup.getCampaignId());
            argsList.add(amazonAdGroup.getProfileId());
            argsList.add(amazonAdGroup.getName());
            argsList.add(amazonAdGroup.getDefaultBid());
            argsList.add(amazonAdGroup.getState());
            argsList.add(amazonAdGroup.getServingStatus());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `name`=values(name), `default_bid`=values(default_bid), `state`=values(state),`serving_status`=values(serving_status)");
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public void batchInsert(Integer puid, List<AmazonAdGroup> list) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_ad_group` (`puid`,`shop_id`,`marketplace_id`,`ad_group_id`,")
                .append("`campaign_id`,`profile_id`,`ad_group_type`,`name`,`default_bid`,`state`,create_time, update_time) VALUES ");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdGroup amazonAdGroup : list) {
            sql.append("(?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(amazonAdGroup.getShopId());
            argsList.add(amazonAdGroup.getMarketplaceId());
            argsList.add(amazonAdGroup.getAdGroupId());
            argsList.add(amazonAdGroup.getCampaignId());
            argsList.add(amazonAdGroup.getProfileId());
            argsList.add(amazonAdGroup.getAdGroupType());
            argsList.add(amazonAdGroup.getName());
            argsList.add(amazonAdGroup.getDefaultBid());
            argsList.add(amazonAdGroup.getState());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `ad_group_type`=values(ad_group_type),`name`=values(name), `default_bid`=values(default_bid), `state`=values(state)");
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public void updateList(Integer puid, List<AmazonAdGroup> list) {
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_group` set `unique_key` = ?,`name`=?,`default_bid`=?,`state`=? ")
                .append(" where puid=? and shop_id=? and ad_group_id=?");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonAdGroup amazonAdGroup : list) {
            batchArg = new Object[]{
                    amazonAdGroup.getUniqueKey(),
                    amazonAdGroup.getName(),
                    amazonAdGroup.getDefaultBid(),
                    amazonAdGroup.getState(),
                    amazonAdGroup.getPuid(),
                    amazonAdGroup.getShopId(),
                    amazonAdGroup.getAdGroupId()
            };
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate(sql.toString(), batchArgs);
    }

    @Override
    public void updateAdGroupType(Integer puid, Integer shopId, String marketPlaceId, Set<String> adGroupIds, String adGroupType) {
        String sql = "update t_amazon_ad_group set ad_group_type=:adGroupType where puid=:puid and shop_id=:shopId and marketplace_id=:marketPlaceId and ad_group_id in (:adGroupIds)";
        NamedParameterJdbcTemplate named = new NamedParameterJdbcTemplate(getJdbcTemplate(puid));
        named.update(sql, new MapSqlParameterSource() {{
            addValue("adGroupType", adGroupType);
            addValue("puid", puid);
            addValue("shopId", shopId);
            addValue("marketPlaceId", marketPlaceId);
            addValue("adGroupIds", adGroupIds);
        }});
    }

    @Override
    public boolean existPublishing(Integer puid, Integer shopId, String campaignId) {
        String sql = "select count(*) from t_amazon_ad_group where puid = ? and shop_id = ? and `campaign_id`=? AND publish_state = 'publishing'";
        return getJdbcTemplate(puid).queryForObject(sql, Boolean.class, new Object[]{puid, shopId, campaignId});
    }

    @Override
    public String getProfileId(int puid, Integer shopId, String marketPlaceId, String adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_Id", adGroupId)
                .equalTo("marketplace_id", marketPlaceId)
                .build();
        return getByCondition(puid, "profile_id", String.class, builder);
    }

    @Override
    public void updateState(int puid, Integer shopId, String adGroupId, String state, int updateId) {
        String sql = "update t_amazon_ad_group set state=?,update_id=?,update_time=now() where puid = ? and shop_id = ? and `ad_group_Id`=?";
        getJdbcTemplate(puid).update(sql, new Object[]{state, updateId, puid, shopId, adGroupId});
    }

    @Override
    public void updateDefaultBid(int puid, Integer shopId, String adGroupId, Double defaultBid, int updateId) {
        String sql = "update t_amazon_ad_group set default_bid=?,update_id=?,update_time=now() where puid = ? and shop_id = ? and `ad_group_Id`=?";
        getJdbcTemplate(puid).update(sql, new Object[]{defaultBid, updateId, puid, shopId, adGroupId});
    }

    @Override
    public void updateInfo(Integer id, Integer puid, Integer updateId) {
        String sql = "update t_amazon_ad_group set update_id=?,update_time=now(),error_msg='' where id=? and puid = ?";
        getJdbcTemplate(puid).update(sql, new Object[]{updateId, id, puid});
    }

    @Override
    public List<Long> getIdByDraft(Integer puid, Integer shopId, String marketPlaceId) {
        StringBuilder sql = new StringBuilder("select id from t_amazon_ad_group where publish_state='draft' ");
        List<Object> args = Lists.newArrayListWithCapacity(2);
        if (shopId != null) {
            sql.append(" and shop_id=? ");
            args.add(shopId);
        }
        if (StringUtils.isNotBlank(marketPlaceId)) {
            sql.append(" and marketplace_id=? ");
            args.add(marketPlaceId);
        }
        sql.append(" order by update_time");
        return getJdbcTemplate(puid).queryForList(sql.toString(), args.toArray(), Long.class);
    }

    @Override
    public void updatePublishState(Integer puid, Integer id, String publishState, String msg) {
        StringBuilder sql = new StringBuilder("update t_amazon_ad_group set publish_state=?,update_time=now()");
        List<Object> args = Lists.newArrayListWithCapacity(3);
        args.add(publishState);
        if (StringUtils.isNotBlank(msg)) {
            sql.append(",error_msg=?");
            args.add(msg);
        }
        sql.append(" where id=?");
        args.add(id);
        getJdbcTemplate(puid).update(sql.toString(), args.toArray());
    }

    @Override
    public void updateAdGroupId(Integer puid, Integer id, String adGroupId, String uniqueKey) {
        String sql = "update t_amazon_ad_group set unique_key=?,ad_group_id=?,publish_state='success',update_time=now() where id=?";
        getJdbcTemplate(puid).update(sql, new Object[]{uniqueKey, adGroupId, id});
    }

    @Override
    public void updateMsg(Integer puid, Integer id, String msg) {
        String sql = "update t_amazon_ad_group set error_msg=?,update_time=now() where id=?";
        getJdbcTemplate(puid).update(sql, new Object[]{msg, id});
    }

    @Override
    public AmazonAdGroup getByAdGroupId(int puid, Integer shopId, String marketPlaceId, String adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId)
                .equalTo("marketplace_id", marketPlaceId)
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public AmazonAdGroup getByAdGroupId(int puid, Integer shopId, String adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId)
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public List<String> getCampaignIds(int puid, Integer shopId, String marketplaceId, String name) {
        String sql = "select campaign_id from t_amazon_ad_group where puid=? and shop_id=? and marketplace_id=? and name like ?";
        List<String> list = getJdbcTemplate(puid).queryForList(sql, new Object[]{puid, shopId, marketplaceId, "%" + name + "%"}, String.class);
        if (list != null) {
            list = list.stream().distinct().collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public int updateMsgNull(int puid, Integer updateId, Integer dxmGroupId) {
        String sql = "update t_amazon_ad_group set error_msg='',update_id=?,update_time=now() where id=? and puid=?";
        return getJdbcTemplate(puid).update(sql, new Object[]{updateId, dxmGroupId, puid});
    }

    @Override
    public String getNameByAdGroupId(Integer puid, Integer shopId, String marketplaceId, String adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId)
                .equalTo("marketplace_id", marketplaceId)
                .build();
        return getByCondition(puid, "name", String.class, builder);
    }

    @Override
    public String getGroupTypeByAdGroupId(Integer puid, Integer shopId, String marketplaceId, String adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId)
                .equalTo("marketplace_id", marketplaceId)
                .build();
        return getByCondition(puid, "ad_group_type", String.class, builder);
    }

    @Override
    public Page pageListForTask(int puid, CpcTaskSearchDto dto, Page page) {
        StringBuilder selectSql = new StringBuilder("select ad_group_id,campaign_id,name,create_time from t_amazon_ad_group");
        StringBuilder countSql = new StringBuilder("select count(*) from t_amazon_ad_group ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and state in ('enabled','paused') and ad_group_id is not null");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        if (StringUtils.isNotEmpty(dto.getCampaignId())) {
            whereSql.append(" and campaign_id = ? ");
            argsList.add(dto.getCampaignId());
        }
        if (Constants.ITEM_TYPE_GROUP.equals(dto.getSearchField()) && StringUtils.isNotEmpty(dto.getSearchValue())) {
            String searchValue = SqlStringUtil.dealLikeSql(dto.getSearchValue());
            whereSql.append(" and name like ? ");
            argsList.add(searchValue + "%");
        }
        selectSql.append(whereSql);
        countSql.append(whereSql);
        selectSql.append(" order by id desc");
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdGroup.class);
    }

    @Override
    public List<Map<String, Object>> getAdGroupNames(int puid, Integer shopId, String marketplaceId, String campaignId, String searchValue, Integer pageNo, Integer pageSize) {
        StringBuilder sql = new StringBuilder("select `name`,ad_group_id id from t_amazon_ad_group where puid=? and shop_id=? and marketplace_id=?  and state in ('enabled','paused') ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(shopId);
        args.add(marketplaceId);
        if (StringUtils.isNotEmpty(campaignId)) {
            sql.append(" and campaign_id=?");
            args.add(campaignId);
        }
        if (StringUtils.isNotBlank(searchValue)) {
            sql.append(" and `name` like ? ");
            args.add("%" + searchValue.trim() + "%");
        }
        sql.append(" limit ?, ? ");
        int start = pageSize * (pageNo - 1);
        args.add(start);
        args.add(pageSize);

        return getJdbcTemplate(puid).queryForList(sql.toString(), args.toArray());
    }

    @Override
    public Integer getAdGroupNamesTotalSize(int puid, Integer shopId, String campaignId, String searchValue) {
        StringBuilder sql = new StringBuilder("select count(*) from t_amazon_ad_group where puid=? and shop_id=?  and state in ('enabled','paused') ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(shopId);
        if (StringUtils.isNotEmpty(campaignId)) {
            sql.append(" and campaign_id=?");
            args.add(campaignId);
        }
        if (StringUtils.isNotBlank(searchValue)) {
            sql.append(" and `name` like ? ");
            args.add("%" + searchValue.trim() + "%");
        }
        return getJdbcTemplate(puid).queryForObject(sql.toString(), Integer.class, args.toArray());
    }

    @Override
    public List<Map<String, Object>> getAdGroupNames(Integer puid, GetNamesDto dto) {
        StringBuilder sql = new StringBuilder("select `name`,ad_group_id id from t_amazon_ad_group where puid=? and shop_id=? and marketplace_id=?" +
                " and state in ('enabled','paused') ");
        List<Object> args = Lists.newArrayListWithExpectedSize(4);
        args.add(puid);
        args.add(dto.getShopId());
        args.add(dto.getMarketplaceId());
        if (StringUtils.isNotEmpty(dto.getCampaignId())) {
            sql.append(" and campaign_id=?");
            args.add(dto.getCampaignId());
        }
        if (Constants.GROUP_TYPE_KEYWORD.equals(dto.getAdGroupType())) {
            sql.append(" and ad_group_type=?");
            args.add(dto.getAdGroupType());
        } else if (Constants.GROUP_TYPE_TARGETING.equals(dto.getAdGroupType())) {
            sql.append(" and ad_group_type=?");
            args.add(dto.getAdGroupType());
        } else if (Constants.GROUP_TYPE_AUTO.equals(dto.getAdGroupType())) {
            sql.append(" and ad_group_type=?");
            args.add(dto.getAdGroupType());
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), args.toArray());
    }

    @Override
    public AmazonAdGroup getById(Integer dxmGroupId, int puid, List<Integer> shopList) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("id", dxmGroupId);
        builder.equalTo("puid", puid);
        if (CollectionUtils.isNotEmpty(shopList)) {
            builder.in("shop_id", shopList.toArray());
        }
        return getByCondition(puid, builder.build());
    }


    @Override
    public Page<AmazonAdGroup> pageList(Integer puid, GroupPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("campaign_id", param.getCampaignIds().split(","))
                .in("ad_group_type", new Object[]{Constants.GROUP_TYPE_AUTO, Constants.GROUP_TYPE_KEYWORD, Constants.GROUP_TYPE_TARGETING});


        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if ("name".equals(param.getSearchField())) {
                builder.like(param.getSearchField(), param.getSearchValue());
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            builder.equalTo("state", param.getStatus());
        }

        String orderBySql = " order by update_time desc";

        return page(puid, param.getPageNo(), param.getPageSize(), orderBySql, builder.build());
    }

    @Override
    public List<AmazonAdGroup> listByCondition(Integer puid, GroupPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("campaign_id", param.getCampaignId())
                .in("ad_group_type", new Object[]{Constants.GROUP_TYPE_AUTO, Constants.GROUP_TYPE_KEYWORD, Constants.GROUP_TYPE_TARGETING});

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if ("name".equals(param.getSearchField())) {
                builder.like(param.getSearchField(), param.getSearchValue());
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            builder.equalTo("state", param.getStatus());
        }

        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdGroup> getListByCampaignId(int puid, Integer shopId, String campaignId, List<String> adGroupTypes) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId);

        if (CollectionUtils.isNotEmpty(adGroupTypes)) {
            builder.in("ad_group_type", adGroupTypes.toArray());
        }

        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdGroup> getListByType(int puid, Integer shopId, List<String> adGroupTypes) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("ad_group_type", adGroupTypes.toArray());

        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdGroup> listNoAdGroupType(Integer puid, Integer shopId, String campaignId, String groupId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId);

        if (StringUtils.isNotBlank(campaignId)) {
            builder.equalTo("campaign_id", campaignId);
        }

        if (StringUtils.isNotBlank(groupId)) {
            builder.equalTo("ad_group_id", groupId);
        }

        ConditionBuilder conditionBuilder = builder.build();
        String sql = "select * from t_amazon_ad_group where (ad_group_type is null or ad_group_type='') and " + conditionBuilder.getSql();

        return getJdbcTemplate(puid).query(sql, getMapper(), conditionBuilder.getValues());
    }

    @Override
    public List<AmazonAdGroup> listNoAdGroupType(Integer puid, Integer shopId, Set<String> campaignIdSet, Set<String> groupIdSet) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId);

        if (CollectionUtils.isNotEmpty(campaignIdSet)) {
            builder.in("campaign_id", campaignIdSet.toArray(new String[]{}));
        }

        if (CollectionUtils.isNotEmpty(groupIdSet)) {
            builder.in("ad_group_id", groupIdSet.toArray(new String[]{}));
        }

        ConditionBuilder conditionBuilder = builder.build();
        String sql = "select * from t_amazon_ad_group where (ad_group_type is null or ad_group_type='') and " + conditionBuilder.getSql();

        return getJdbcTemplate(puid).query(sql, getMapper(), conditionBuilder.getValues());
    }

    @Override
    public void batchUpdateAdGrpupType(Integer puid, List<AmazonAdGroup> amazonAdGroups) {
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonAdGroup amazonAdGroup : amazonAdGroups) {
            batchArg = new Object[]{
                    amazonAdGroup.getAdGroupType(),
                    amazonAdGroup.getId()
            };
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate("update `t_amazon_ad_group` set `ad_group_type` = ?, `update_time`=now(3) where id=?", batchArgs);
    }


    @Override
    public String getGroupNameByGroupId(int puid, String groupId, Integer shopId) {
        String sql = "SELECT `name` FROM `t_amazon_ad_group` WHERE `puid`=?  AND  `ad_group_id` =? AND `shop_id`=?";
        return getJdbcTemplate(puid).queryForObject(sql, String.class, puid, groupId, shopId);
    }


    @Override
    public List<AmazonAdGroupDto> ListAllGroupByIdsAndDate(Integer puid, GroupPageParam param) {
        SelectBuilder selectBuilder = getSelectSql(puid, param);

        StringBuilder sql = new StringBuilder("select * from ( ").append(selectBuilder.toSql()).append(" ) c ");

        //默认排序
        String orderSql = " order by c.id  desc ";

        sql.append(orderSql);

        return getJdbcTemplate(puid).query(sql.toString(), selectBuilder.getQueryValues(), new RowMapper<AmazonAdGroupDto>() {
            @Override
            public AmazonAdGroupDto mapRow(ResultSet res, int i) throws SQLException {
                return AmazonAdGroupDto.builder()
                        .adGroupId(res.getString("ad_group_id"))
                        .adGroupType(res.getString("ad_group_type"))
                        .bid(res.getBigDecimal("bid"))
                        .campaignId(res.getString("campaign_id"))
                        .createId(res.getInt("create_id"))
                        .createTime(res.getDate("create_time"))
                        .id(res.getLong("id"))
                        .marketplaceId(res.getString("marketplace_id"))
                        .name(res.getString("name"))
                        .puid(res.getInt("puid"))
                        .shopId(res.getInt("shop_id"))
                        .state(res.getString("state"))
                        .type(res.getString("type"))
                        .updateId(res.getInt("update_id"))
                        .updateTime(res.getDate("update_time"))
                        .build();
            }
        });
    }

    @Override
    public List<AdGroupOptionVo> getAllGroupsByType(Integer puid, Integer shopId, List<String> typeAList, String groupType, String name, String campaignIds) {


        List<Object> argList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select * from ( ");
        StringBuilder spSql = new StringBuilder();
        //sb
        if (CollectionUtils.isEmpty(typeAList) || typeAList.contains(Constants.SP)) {

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", shopId);
            if (StringUtils.isNotBlank(campaignIds)) {
                List<String> list = StringUtil.splitStr(campaignIds);
                builder.inStrList("campaign_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(name)) {
                builder.like("name", name);
            }
            if (StringUtils.isNotBlank(groupType)) {
                builder.and().leftBracket();
                builder.equalToWithoutCheck(LogicType.EPT, "ad_group_type", "");
                builder.isNull(LogicType.OR, "ad_group_type");
                builder.in(LogicType.OR, "ad_group_type", new String[]{"auto", groupType});
                builder.rightBracket();
            }
            ConditionBuilder conditionBuilder = builder.build();
            spSql.append(" select 'sp' as type,id,campaign_id, ad_group_id,name,state,create_time, '' as ad_format from t_amazon_ad_group where ");
            spSql.append(conditionBuilder.getSql());
            sql.append(spSql);
            argList.addAll(Stream.of(conditionBuilder.getValues()).collect(Collectors.toList()));
        }
        //sd
        StringBuilder sdSql = new StringBuilder();
        if (CollectionUtils.isEmpty(typeAList) || typeAList.contains(Constants.SD)) {

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", shopId);
            if (StringUtils.isNotBlank(campaignIds)) {
                List<String> list = StringUtil.splitStr(campaignIds);
                builder.inStrList("campaign_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(name)) {
                builder.like("name", name);
            }
            ConditionBuilder conditionBuilder = builder.build();
            if (StringUtils.isNotBlank(spSql)) {
                sdSql.append(" union all ");
            }
            sdSql.append(" select 'sd' as type,id,campaign_id, ad_group_id,name,state ,create_time, '' ad_format from   t_amazon_ad_group_sd where ");
            sdSql.append(conditionBuilder.getSql());
            sql.append(sdSql);
            argList.addAll(Stream.of(conditionBuilder.getValues()).collect(Collectors.toList()));
        }

        //sb
        StringBuilder sbSql = new StringBuilder();
        if (CollectionUtils.isEmpty(typeAList) || typeAList.contains(Constants.SB)) {

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", shopId);
            if (StringUtils.isNotBlank(campaignIds)) {
                List<String> list = StringUtil.splitStr(campaignIds);
                builder.inStrList("campaign_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(name)) {
                builder.like("name", name);
            }
            if (StringUtils.isNotBlank(groupType)) {
                builder.and().leftBracket();
                builder.equalToWithoutCheck(LogicType.EPT, "ad_group_type", "");
                builder.isNull(LogicType.OR, "ad_group_type");
                builder.in(LogicType.OR, "ad_group_type", new String[]{groupType});
                builder.rightBracket();
            }
            ConditionBuilder conditionBuilder = builder.build();
            if (StringUtils.isNotBlank(spSql)) {
                sbSql.append(" union all ");
            }
            sbSql.append(" select 'sb' as type,id,campaign_id, ad_group_id,name,state ,create_time, ad_format from   t_amazon_ad_group_sb where ");
            sbSql.append(conditionBuilder.getSql());
            sql.append(sbSql);
            argList.addAll(Stream.of(conditionBuilder.getValues()).collect(Collectors.toList()));
        }

        sql.append(" ) t order by t.id desc ");

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdGroupOptionVo>() {
            @Override
            public AdGroupOptionVo mapRow(ResultSet res, int i) throws SQLException {
                AdGroupOptionVo optionVo = AdGroupOptionVo.builder()
                        .name(res.getString("name"))
                        .groupId(res.getString("ad_group_id"))
                        .campaignId(res.getString("campaign_id"))
                        .type(res.getString("type"))
                        .state(res.getString("state"))
                        .targetingType(res.getString("ad_format"))
                        .build();
                return optionVo;
            }
        }, argList.stream().toArray());
    }

    @Override
    public List<AdGroupOptionVo> getSpGroupsByType(Integer puid, Integer shopId, String campaignIds, String name) {
        StringBuilder sql = new StringBuilder();
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
            .equalTo("puid", puid)
            .equalTo("shop_id", shopId)
            .notEqualTo("state", "archived")
            .notEqualTo("ad_group_type", "auto");
        if (StringUtils.isNotBlank(campaignIds)) {
            List<String> list = StringUtil.splitStr(campaignIds);
            builder.inStrList("campaign_id", list.toArray(new String[]{}));
        }
        if (StringUtils.isNotBlank(name)) {
            builder.like("name", "%" + name + "%");
        }
        ConditionBuilder conditionBuilder = builder.build();
        sql.append(" select 'sp' as type,id,campaign_id, ad_group_id,name,state,create_time, '' as ad_format from t_amazon_ad_group where ");
        sql.append(conditionBuilder.getSql());
        return getJdbcTemplate(puid).query(sql.toString(), (res, i) -> AdGroupOptionVo.builder()
            .name(res.getString("name"))
            .groupId(res.getString("ad_group_id"))
            .campaignId(res.getString("campaign_id"))
            .type(res.getString("type"))
            .state(res.getString("state"))
            .targetingType(res.getString("ad_format"))
            .build(), Stream.of(conditionBuilder.getValues()).toArray());
    }

    @Override
    public List<AdGroupOptionVo> getAllGroupsByType(Integer puid, Integer shopId, List<String> typeAList, String groupType, String name, String campaignIds, String groupIds, boolean queryAuto) {
        List<Object> argList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select * from ( ");
        StringBuilder spSql = new StringBuilder();
        //sb
        if (CollectionUtils.isEmpty(typeAList) || typeAList.contains(Constants.SP)) {

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", shopId);
            if (StringUtils.isNotBlank(campaignIds)) {
                List<String> list = StringUtil.splitStr(campaignIds);
                builder.inStrList("campaign_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(groupIds)) {
                List<String> list = StringUtil.splitStr(groupIds);
                builder.inStrList("ad_group_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(name)) {
                builder.like("name", name);
            }
            if (StringUtils.isNotBlank(groupType)) {
                builder.and().leftBracket();
                builder.equalToWithoutCheck(LogicType.EPT, "ad_group_type", "");
                builder.isNull(LogicType.OR, "ad_group_type");
                if (queryAuto) {
                    builder.in(LogicType.OR, "ad_group_type", new String[]{"auto", groupType});
                } else {
                    builder.in(LogicType.OR, "ad_group_type", new String[]{groupType});
                }
                builder.rightBracket();
            }
            ConditionBuilder conditionBuilder = builder.build();
            spSql.append(" select 'sp' as type,id,campaign_id, ad_group_id,name,state,create_time, '' as ad_format from t_amazon_ad_group where ");
            spSql.append(conditionBuilder.getSql());
            sql.append(spSql);
            argList.addAll(Stream.of(conditionBuilder.getValues()).collect(Collectors.toList()));
        }
        //sd
        StringBuilder sdSql = new StringBuilder();
        if (CollectionUtils.isEmpty(typeAList) || typeAList.contains(Constants.SD)) {

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", shopId);
            if (StringUtils.isNotBlank(campaignIds)) {
                List<String> list = StringUtil.splitStr(campaignIds);
                builder.inStrList("campaign_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(groupIds)) {
                List<String> list = StringUtil.splitStr(groupIds);
                builder.inStrList("ad_group_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(name)) {
                builder.like("name", name);
            }
            ConditionBuilder conditionBuilder = builder.build();
            if (StringUtils.isNotBlank(spSql)) {
                sdSql.append(" union all ");
            }
            sdSql.append(" select 'sd' as type,id,campaign_id, ad_group_id,name,state,tactic ,create_time, '' ad_format from   t_amazon_ad_group_sd where ");
            sdSql.append(conditionBuilder.getSql());
            sql.append(sdSql);
            argList.addAll(Stream.of(conditionBuilder.getValues()).collect(Collectors.toList()));
        }

        //sb
        StringBuilder sbSql = new StringBuilder();
        if (CollectionUtils.isEmpty(typeAList) || typeAList.contains(Constants.SB)) {

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", shopId);
            if (StringUtils.isNotBlank(campaignIds)) {
                List<String> list = StringUtil.splitStr(campaignIds);
                builder.inStrList("campaign_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(groupIds)) {
                List<String> list = StringUtil.splitStr(groupIds);
                builder.inStrList("ad_group_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(name)) {
                builder.like("name", name);
            }
            if (StringUtils.isNotBlank(groupType)) {
                builder.and().leftBracket();
                builder.equalToWithoutCheck(LogicType.EPT, "ad_group_type", "");
                builder.isNull(LogicType.OR, "ad_group_type");
                builder.in(LogicType.OR, "ad_group_type", new String[]{groupType});
                builder.rightBracket();
            }
            ConditionBuilder conditionBuilder = builder.build();
            if (StringUtils.isNotBlank(spSql)) {
                sbSql.append(" union all ");
            }
            sbSql.append(" select 'sb' as type,id,campaign_id, ad_group_id,name,state ,create_time, ad_format from   t_amazon_ad_group_sb where ");
            sbSql.append(conditionBuilder.getSql());
            sql.append(sbSql);
            argList.addAll(Stream.of(conditionBuilder.getValues()).collect(Collectors.toList()));
        }

        sql.append(" ) t order by t.id desc ");

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdGroupOptionVo>() {
            @Override
            public AdGroupOptionVo mapRow(ResultSet res, int i) throws SQLException {
                AdGroupOptionVo optionVo = AdGroupOptionVo.builder()
                        .name(res.getString("name"))
                        .groupId(res.getString("ad_group_id"))
                        .campaignId(res.getString("campaign_id"))
                        .type(res.getString("type"))
                        .state(res.getString("state"))
                        .targetingType(res.getString("ad_format"))
                        .build();
                if (CollectionUtils.isEmpty(typeAList) || typeAList.contains(Constants.SD)) {
                    Optional.ofNullable(res.getString("tactic")).filter(StringUtils::isNotEmpty).ifPresent(optionVo::setTactic);
                }
                if (CollectionUtils.isEmpty(typeAList) || typeAList.contains(Constants.SB)) {
                    Optional.ofNullable(res.getString("ad_format")).filter(StringUtils::isNotEmpty).ifPresent(optionVo::setAdFormat);
                }
                return optionVo;
            }
        }, argList.stream().toArray());
    }

    @Override
    public List<AdGroupOptionVo> getAllGroupsByType(Integer puid, String shopId, List<String> typeAList, String groupType, String name, String campaignIds, String groupIds) {
        List<Object> argList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select * from ( ");
        StringBuilder spSql = new StringBuilder();
        //sb
        if (CollectionUtils.isEmpty(typeAList) || typeAList.contains(Constants.SP)) {

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid);
            if (StringUtils.isNotBlank(shopId)) {
                List<String> list = StringUtil.splitStr(shopId);
                builder.inStrList("shop_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(campaignIds)) {
                List<String> list = StringUtil.splitStr(campaignIds);
                builder.inStrList("campaign_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(groupIds)) {
                List<String> list = StringUtil.splitStr(groupIds);
                builder.inStrList("ad_group_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(name)) {
                builder.like("name", name);
            }
            if (StringUtils.isNotBlank(groupType)) {
                builder.and().leftBracket();
                builder.equalToWithoutCheck(LogicType.EPT, "ad_group_type", "");
                builder.isNull(LogicType.OR, "ad_group_type");
                builder.in(LogicType.OR, "ad_group_type", new String[]{"auto", groupType});
                builder.rightBracket();
            }
            ConditionBuilder conditionBuilder = builder.build();
            spSql.append(" select 'sp' as type,id,campaign_id, ad_group_id,name,state,create_time, '' as ad_format from t_amazon_ad_group where ");
            spSql.append(conditionBuilder.getSql());
            sql.append(spSql);
            argList.addAll(Stream.of(conditionBuilder.getValues()).collect(Collectors.toList()));
        }
        //sd
        StringBuilder sdSql = new StringBuilder();
        if (CollectionUtils.isEmpty(typeAList) || typeAList.contains(Constants.SD)) {

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid);
            if (StringUtils.isNotBlank(shopId)) {
                List<String> list = StringUtil.splitStr(shopId);
                builder.inStrList("shop_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(campaignIds)) {
                List<String> list = StringUtil.splitStr(campaignIds);
                builder.inStrList("campaign_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(groupIds)) {
                List<String> list = StringUtil.splitStr(groupIds);
                builder.inStrList("ad_group_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(name)) {
                builder.like("name", name);
            }
            ConditionBuilder conditionBuilder = builder.build();
            if (StringUtils.isNotBlank(spSql)) {
                sdSql.append(" union all ");
            }
            sdSql.append(" select 'sd' as type,id,campaign_id, ad_group_id,name,state ,create_time, '' ad_format from   t_amazon_ad_group_sd where ");
            sdSql.append(conditionBuilder.getSql());
            sql.append(sdSql);
            argList.addAll(Stream.of(conditionBuilder.getValues()).collect(Collectors.toList()));
        }

        //sb
        StringBuilder sbSql = new StringBuilder();
        if (CollectionUtils.isEmpty(typeAList) || typeAList.contains(Constants.SB)) {

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid);
            if (StringUtils.isNotBlank(shopId)) {
                List<String> list = StringUtil.splitStr(shopId);
                builder.inStrList("shop_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(campaignIds)) {
                List<String> list = StringUtil.splitStr(campaignIds);
                builder.inStrList("campaign_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(groupIds)) {
                List<String> list = StringUtil.splitStr(groupIds);
                builder.inStrList("ad_group_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(name)) {
                builder.like("name", name);
            }
            if (StringUtils.isNotBlank(groupType)) {
                builder.and().leftBracket();
                builder.equalToWithoutCheck(LogicType.EPT, "ad_group_type", "");
                builder.isNull(LogicType.OR, "ad_group_type");
                builder.in(LogicType.OR, "ad_group_type", new String[]{groupType});
                builder.rightBracket();
            }
            ConditionBuilder conditionBuilder = builder.build();
            if (StringUtils.isNotBlank(spSql)) {
                sbSql.append(" union all ");
            }
            sbSql.append(" select 'sb' as type,id,campaign_id, ad_group_id,name,state ,create_time, ad_format from   t_amazon_ad_group_sb where ");
            sbSql.append(conditionBuilder.getSql());
            sql.append(sbSql);
            argList.addAll(Stream.of(conditionBuilder.getValues()).collect(Collectors.toList()));
        }

        sql.append(" ) t order by t.id desc ");
        //限制大数据量查询
        sql.append(" limit 100000 ");

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdGroupOptionVo>() {
            @Override
            public AdGroupOptionVo mapRow(ResultSet res, int i) throws SQLException {
                AdGroupOptionVo optionVo = AdGroupOptionVo.builder()
                        .name(res.getString("name"))
                        .groupId(res.getString("ad_group_id"))
                        .campaignId(res.getString("campaign_id"))
                        .type(res.getString("type"))
                        .state(res.getString("state"))
                        .targetingType(res.getString("ad_format"))
                        .build();
                return optionVo;
            }
        }, argList.stream().toArray());
    }

    @Override
    public List<String> getGroupIdsByGroupType(Integer puid, Integer shopId, String groupType) {
        String sql = "select ad_group_id from t_amazon_ad_group where puid=? and shop_id=? and ad_group_type=?";
        return getJdbcTemplate(puid).queryForList(sql, new Object[]{puid, shopId, groupType}, String.class);
    }


    private SelectBuilder getSelectSql(Integer puid, GroupPageParam param) {
        SelectBuilder selectBuilder = new SelectBuilder();

        StringBuilder spSql = new StringBuilder();
        //未指定类型 或 指定sp
        if (StringUtils.isBlank(param.getType()) || Constants.SP.equalsIgnoreCase(param.getType())) {

            spSql.append("  'sp' as type,id,puid,shop_id,marketplace_id,campaign_id,ad_group_id,name,default_bid as bid,ad_group_type,state ");
            spSql.append(",create_id,update_id,create_time,update_time  from t_amazon_ad_group  where ");

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", param.getShopId());
            //广告活动ID查询
            if (StringUtils.isNotBlank(param.getCampaignId())) {
                builder.equalTo("campaign_id", param.getCampaignId());
            }
            //状态查询
            if (StringUtils.isNotBlank(param.getStatus())) {
                builder.equalTo("state", param.getStatus());
            }
            //广告组查询
            if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
                builder.like(param.getSearchField(), "%" + param.getSearchValue() + "%");
            }

            if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
                if (param.getBidMin() != null) {   //默认竞价
                    builder.greaterThanOrEqualTo("default_bid", param.getBidMin());
                }
                if (param.getBidMax() != null) {
                    builder.lessThanOrEqualTo("default_bid", param.getBidMax());
                }
            }

            spSql.append(builder.build().getSql());

            selectBuilder.appendSql(spSql.toString());
            selectBuilder.appendValue(builder.build().getValues());
        }


        //未指定类型 或 指定sd
        StringBuilder sdSql = new StringBuilder();
        if (StringUtils.isBlank(param.getType()) || Constants.SD.equalsIgnoreCase(param.getType())) {

            sdSql.append("  'sd' as type, id,puid,shop_id,marketplace_id,campaign_id,ad_group_id,name,default_bid as bid,");
            sdSql.append("tactic as ad_group_type ,state,create_id,update_id,create_time,update_time from t_amazon_ad_group_sd where ");

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", param.getShopId());

            //广告活动ID查询
            if (StringUtils.isNotBlank(param.getCampaignId())) {
                builder.equalTo("campaign_id", param.getCampaignId());
            }
            //状态查询
            if (StringUtils.isNotBlank(param.getStatus())) {
                builder.equalTo("state", param.getStatus());
            }
            //广告组查询
            if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
                builder.like(param.getSearchField(), "%" + param.getSearchValue() + "%");
            }
            if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
                if (param.getBidMin() != null) {   //默认竞价
                    builder.greaterThanOrEqualTo("default_bid", param.getBidMin());
                }
                if (param.getBidMax() != null) {
                    builder.lessThanOrEqualTo("default_bid", param.getBidMax());
                }
            }

            sdSql.append(builder.build().getSql());

            if (StringUtils.isNotBlank(spSql.toString())) {
                selectBuilder.appendSql(" union all select ");
            }
            selectBuilder.appendSql(sdSql.toString());
            selectBuilder.appendValue(builder.build().getValues());
        }
        return selectBuilder;
    }

    @Override
    public List<Map<String, Object>> getAdGroupNames(int puid, Integer shopId, String marketplaceId, String campaignId) {
        StringBuilder sql = new StringBuilder("select `name`,ad_group_id id from t_amazon_ad_group where puid=? and shop_id=? and marketplace_id=?");
        List<Object> args = Lists.newArrayListWithExpectedSize(4);
        args.add(puid);
        args.add(shopId);
        args.add(marketplaceId);
        if (StringUtils.isNotEmpty(campaignId)) {
            sql.append(" and campaign_id=?");
            args.add(campaignId);
        }

        return getJdbcTemplate(puid).queryForList(sql.toString(), args.toArray());
    }

    @Override
    public List<Map<String, Object>> getGroupNameByGroupIdsList(int puid, Integer shopId, List<String> groupIds) {
        String sql = "select `name`,ad_group_id id from t_amazon_ad_group where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("ad_group_id", groupIds.toArray())
                .build();
        sql += conditionBuilder.getSql();

        return getJdbcTemplate(puid).queryForList(sql, conditionBuilder.getValues());

    }

    @Override
    public Page getPageList(Integer puid, GroupPageParam param, Page page) {
        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_group ");
        StringBuilder countSql = new StringBuilder("select count(*)  FROM `t_amazon_ad_group` ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }

        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId(), ",");
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
        }

        // 广告组Id查询
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getGroupId(), ",");
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, argsList));

        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and name like ? ");
            argsList.add("%" + param.getSearchValue().trim() + "%");
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, argsList));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ? ");
                argsList.add(AmazonAdGroup.servingStatusEnum.AD_GROUP_STATUS_ENABLED.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }

        }

        if (CollectionUtils.isNotEmpty(param.getGroupIds())) {
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", param.getGroupIds(), argsList));
        }

        selectSql.append(whereSql);
        countSql.append(whereSql);
        selectSql.append(" order by data_update_time desc, id desc ");

        Object[] args = argsList.toArray();
        return getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdGroup.class);
    }

    @Override
    public List<AmazonAdGroup> getList(Integer puid, GroupPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId());

        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId(), ",");
            builder.inStrList("campaign_id", campaignIds.toArray(new String[]{}));
        }

        // 广告组Id查询
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getGroupId(), ",");
            builder.inStrList("ad_group_id", groupIds.toArray(new String[]{}));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            builder.inStrList("campaign_id", param.getCampaignIdList().toArray(new String[]{}));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if (StringUtils.isNotBlank(param.getSearchType()) && SearchTypeEnum.EXACT.getValue().equals(param.getSearchType())) {
                builder.equalTo("name", param.getSearchValue().trim());
            } else {
                builder.like(param.getSearchField(), param.getSearchValue());
            }
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            builder.inStrList("name", param.getSearchValueList().toArray(new String[]{}));
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            builder.inStrList("state", statusList.toArray(new String[]{}));
        }


        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                builder.equalTo("serving_status", AmazonAdGroup.servingStatusEnum.AD_GROUP_STATUS_ENABLED.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                builder.inStrList("serving_status", list.toArray(new String[]{}));
            }

        }

        if (CollectionUtils.isNotEmpty(param.getGroupIds())) {
            builder.inStrList("ad_group_id", param.getGroupIds().toArray(new String[]{}));
        }

        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (param.getBidMin() != null) {   //默认竞价
                builder.greaterThanOrEqualTo("default_bid", param.getBidMin());
            }
            if (param.getBidMax() != null) {
                builder.lessThanOrEqualTo("default_bid", param.getBidMax());
            }
        }

        builder.orderByDesc("data_update_time", "id");
        builder.limit(Constants.TOTALSIZELIMIT);  // 限制6万

        return listByCondition(puid, builder.build());
    }

    @Override
    public void updateDataUpdateTime(Integer puid, Integer shopId, String groupId, LocalDate localDate) {
        String sql = "update t_amazon_ad_group set data_update_time=?,update_time=now() where puid = ? and shop_id = ? and `ad_group_id`=?";
        getJdbcTemplate(puid).update(sql, new Object[]{localDate, puid, shopId, groupId});
    }


    @Override
    public void batchUpdateAmazonAdGroup(Integer puid, List<AmazonAdGroup> list, String type) {
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_group` set update_id=?,update_time=now() ");
        if (Constants.CPC_SP_GROUP_BATCH_UPDATE_BID.equals(type)) {
            sql.append(",`default_bid` = ? ");
        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            sql.append(",`state`=? ");
        } else {
            return;
        }
        sql.append(" where id = ? and puid=? and shop_id=? and ad_group_id=?");
        List<Object[]> batchArgs = Lists.newArrayList();
        List<Object> batchArg = new ArrayList<>();
        for (AmazonAdGroup amazonAdGroup : list) {
            batchArg.add(amazonAdGroup.getUpdateId());
            if (Constants.CPC_SP_GROUP_BATCH_UPDATE_BID.equals(type)) {
                batchArg.add(amazonAdGroup.getDefaultBid());
            } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
                batchArg.add(amazonAdGroup.getState());

            }
            batchArg.add(amazonAdGroup.getId());
            batchArg.add(amazonAdGroup.getPuid());
            batchArg.add(amazonAdGroup.getShopId());
            batchArg.add(amazonAdGroup.getAdGroupId());
            batchArgs.add(batchArg.toArray());
            batchArg.clear();
        }
        getJdbcTemplate(puid).batchUpdate(sql.toString(), batchArgs);
    }

    @Override
    public List<Map<String, Object>> getGroupNameByShopIdsAndGroupIdsList(int puid, List<Integer> shopIds, List<String> groupIds) {
        String sql = "select `name`,ad_group_id id from t_amazon_ad_group where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopIds.toArray())
                .in("ad_group_id", groupIds.toArray())
                .build();
        sql += conditionBuilder.getSql();

        return getJdbcTemplate(puid).queryForList(sql, conditionBuilder.getValues());

    }

    @Override
    public List<String> getArchivedItems(Integer puid, Integer shopId) {
        String sql = "select ad_group_id from t_amazon_ad_group where ";
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("state", "archived")
                .build();
        sql += conditionBuilder.getSql();
        return getJdbcTemplate(puid).queryForList(sql, String.class, conditionBuilder.getValues());
    }

    @Override
    public List<String> getUpdateAfterReportSyncTimeItems(Integer puid, Integer shopId, LocalDateTime syncAt) {
        String sql = "select ad_group_id from t_amazon_ad_group where ";
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .greaterThan("update_time", syncAt)
                .build();
        sql += conditionBuilder.getSql();
        return getJdbcTemplate(puid).queryForList(sql, String.class, conditionBuilder.getValues());
    }

    @Override
    public List<String> getAdGroupIdsByGroup(int puid, GroupPageParam param) {

        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder(" select c.ad_group_id from t_amazon_ad_group c ");
        StringBuilder whereSql = new StringBuilder(" where c.puid = ? ");
        argsList.add(puid);

        if (param.getShopId() != null) {
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }

        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            whereSql.append(" and c.campaign_id = ? ");
            argsList.add(param.getCampaignId());
        }

        // 广告组Id查询
        if (StringUtils.isNotBlank(param.getGroupId())) {
            whereSql.append(" and c.ad_group_id = ? ");
            argsList.add(param.getGroupId());
        }


        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }

        //标签筛选
        if (CollectionUtils.isNotEmpty(param.getGroupIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", param.getGroupIds(), argsList));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and c.name like ? ");
            argsList.add("%" + param.getSearchValue().trim() + "%");
        }
        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and c.serving_status = ? ");
                argsList.add(AmazonAdGroup.servingStatusEnum.AD_GROUP_STATUS_ENABLED.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("c.serving_status", list, argsList));
            }

        }

        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (param.getBidMin() != null) {   //默认竞价
                whereSql.append(" and c.default_bid >= ?");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and c.default_bid <= ?");
                argsList.add(param.getBidMax());
            }
        }
        sql.append(whereSql);
        List<String> adGroupIds = getJdbcTemplate(puid).queryForList(sql.toString(), argsList.toArray(), String.class);
        return adGroupIds;
    }

    /**
     * 关联关键词库查询
     *
     * @param puid
     * @param shopIds
     * @param groupIds
     * @return
     */
    @Override
    public List<AmazonAdGroup> getListByShopIdsAndGroupIds(int puid, List<Integer> shopIds, List<String> groupIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inIntList("shop_id", shopIds.toArray(new Integer[0]))
                .inStrList("ad_group_id", groupIds.toArray(new String[0]))
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonAdGroup> getNameByShopIdsAndGroupIds(int puid, List<Integer> shopIds, List<String> groupIds,
                                                           String groupName) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inIntList("shop_id", shopIds.toArray(new Integer[0]))
                .inStrList("ad_group_id", groupIds.toArray(new String[0]));
        if (StringUtils.isNotEmpty(groupName)) {
            builder.like("name", groupName);
        }
        return listByConditionWithFields(puid, Arrays.asList("ad_group_id", "default_bid" ,"name"), builder.build());
    }

    @Override
    public List<AmazonAdGroup> getAdGroupByIds(Integer puid, Integer shopId, List<String> groupIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("ad_group_id", groupIds.toArray(new String[]{}))
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public Page<AmazonAdGroup> listByAutoRule(AdGroupAutoRuleParam param, List<String> itemIds, List<String> similarRuleItemIdList) {

        StringBuilder sql = new StringBuilder("select adType, id, campaign_id, ad_group_id, ad_group_type, puid, shop_id, state, name, marketplace_id, default_bid, serving_status from ( ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        List<Object> argsList = new ArrayList<>();

        List<String> groupSqlList = new ArrayList<>(3);

        param.getAdTypes().forEach(x -> {
            String groupSql = getQueryGroupSql4ListByAutoRule(param, itemIds, similarRuleItemIdList, x, argsList);
            groupSqlList.add(groupSql);
        });

        sql.append(StringUtils.join(groupSqlList, " union all "));
        countSql.append(StringUtils.join(groupSqlList, " union all "));

        sql.append(" ) t ");
        countSql.append(" ) t ");

        return getPageResult(param.getPuid(), param.getPageNo(), param.getPageSize(), String.valueOf(countSql), argsList.toArray(), String.valueOf(sql), argsList.toArray(), null);
    }

    private String getQueryGroupSql4ListByAutoRule(AdGroupAutoRuleParam param, List<String> itemIds, List<String> similarRuleItemIdList, String adType, List<Object> argsList) {
        String tableName = getJdbcHelper().getTable();
        StringBuilder sql = new StringBuilder();

        if (CampaignTypeEnum.sp.getCampaignType().equals(adType)) {
            sql.append(" select 'sp' adType, id, campaign_id, ad_group_id, ad_group_type, puid, shop_id, state, name, marketplace_id, default_bid, serving_status from ");
            sql.append(tableName);
        } else if (CampaignTypeEnum.sb.getCampaignType().equals(adType)) {
            tableName += ("_" + adType);
            sql.append(" select 'sb' adType, id, campaign_id, ad_group_id, ad_group_type, puid, shop_id, state, name, marketplace_id, bid as default_bid, serving_status from ");
            sql.append(tableName);
        } else {
            sql.append(" select 'sd' adType, id, campaign_id, ad_group_id, '' as ad_group_type, puid, shop_id, state, name, marketplace_id, default_bid, serving_status from ");
            tableName += ("_" + adType);
            sql.append(tableName);
        }

        StringBuilder whereSql = new StringBuilder(" where puid = ? ");
        argsList.add(param.getPuid());
        if (param.getShopId() != null) {
            whereSql.append(" and shop_id = ?");
            argsList.add(param.getShopId());
        }

        //过滤模板关系
        if (CollectionUtils.isNotEmpty(itemIds)) {
            whereSql.append(SqlStringUtil.dealNotInList("ad_group_id", itemIds, argsList));
        }

        //相似规则处理
        if (param.getHasSimilarRule() != null) {
            if (param.getHasSimilarRule() == 0) {
                if (CollectionUtils.isNotEmpty(similarRuleItemIdList)) {
                    whereSql.append(SqlStringUtil.dealNotInList("ad_group_id", similarRuleItemIdList, argsList));
                }
            } else if (param.getHasSimilarRule() == 1) {
                if (CollectionUtils.isNotEmpty(similarRuleItemIdList)) {
                    whereSql.append(SqlStringUtil.dealInList("ad_group_id", similarRuleItemIdList, argsList));
                }
            }
        }

        //sp
        if (CampaignTypeEnum.sp.getCampaignType().equals(adType)) {
            //搜索词，添加到投放，所在广告组
            if (param.getOperationType() == AutoRuleOperationTypeEnum.addTarget.getOperationType()
                    && PerformOperationJson.AppointAdGroupTypeEnum.QUERY_KEYWORD_AD_GROUP.getValue().equals(param.getAppointAdGroupType())
                    && AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName().equals(param.getItemType())) {
                whereSql.append(" and ad_group_type in ('keyword','targeting') ");
                //投放，添加到否投
            } else if (param.getOperationType() == AutoRuleOperationTypeEnum.addNotTarget.getOperationType()
                    && AutoRuleItemTypeEnum.TARGET.getName().equals(param.getItemType())) {
                whereSql.append(" and ad_group_type in ('keyword','targeting') ");
                //其他
            } else {
                whereSql.append(" and ad_group_type in ('keyword','targeting','auto') ");
            }
        }

        //sb
        if (CampaignTypeEnum.sb.getCampaignType().equals(adType)) {
            //搜索词，添加到投放，所在广告组
            if (param.getOperationType() == AutoRuleOperationTypeEnum.addTarget.getOperationType()
                    && PerformOperationJson.AppointAdGroupTypeEnum.QUERY_KEYWORD_AD_GROUP.getValue().equals(param.getAppointAdGroupType())
                    && AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName().equals(param.getItemType())) {
                //没有投放产生asin
                whereSql.append(" and ad_group_type in ('keyword') ");
                //投放，添加到否投
            } else if (param.getOperationType() == AutoRuleOperationTypeEnum.addNotTarget.getOperationType()
                    && AutoRuleItemTypeEnum.TARGET.getName().equals(param.getItemType())) {
                whereSql.append(" and ad_group_type in ('keyword','product') ");
                //其他
            } else {
                whereSql.append(" and ad_group_type in ('keyword','product') ");
            }
        }

        //sd 没有搜索词
        //sd 添加到否投，商品和受众已融合，一个广告组下可以同时有商品和受众，无法区分

        //服务状态
        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            whereSql.append(SqlStringUtil.dealInList("serving_status", param.getServingStatusList(), argsList));
        }

        //状态
        if (CollectionUtils.isNotEmpty(param.getStateList())) {
            whereSql.append(SqlStringUtil.dealInList("state", param.getStateList(), argsList));
        }

        //广告活动查询
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getAdGroupName())) {
            whereSql.append(" and name like ? ");
            argsList.add("%" + param.getAdGroupName() + "%");
        }

        sql.append(whereSql);

        return sql.toString();
    }

    @Override
    public List<AmazonAdGroup> autoRuleAdGroup(int puid, int shopId, List<String> campaignIds, List<String> adGroupIds, String state, String searchValue, List<String> servingStatus) {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.equalTo("puid", puid);
        conditionBuilder.equalTo("shop_id", shopId);
        conditionBuilder.in("ad_group_id", adGroupIds.toArray());
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            conditionBuilder.in("campaign_id", campaignIds.toArray());
        }
        if (CollectionUtils.isNotEmpty(servingStatus)) {
            conditionBuilder.in("serving_status", servingStatus.toArray());
        }
        if (StringUtils.isNotBlank(state)) {
            conditionBuilder.equalTo("state", state);
        }
        if (StringUtils.isNotBlank(searchValue)) {
            conditionBuilder.like("name", searchValue);
        }
        return listByCondition(puid, conditionBuilder.build());
    }

    @Override
    public Page<AmazonAdGroup> queryGroup(Integer puid, Integer shopId, List<String> types, String groupName, List<String> campaignIds, List<String> adGroupTypes, Integer pageSize, Integer pageNo, List<String> statusList) {
        //搜索词添加到投放，指定广告组（查询sb和sp的ad_group_type in "keyword,targeting,product"的广告组）
        if (CollectionUtils.isNotEmpty(adGroupTypes)) {
            types = Arrays.asList(CampaignTypeEnum.sp.getCampaignType(), CampaignTypeEnum.sb.getCampaignType());
        } else {
            if (CollectionUtils.isEmpty(types)) {
                types = Arrays.asList(CampaignTypeEnum.sp.getCampaignType(), CampaignTypeEnum.sb.getCampaignType(), CampaignTypeEnum.sd.getCampaignType());
            }
        }

        StringBuilder sql = new StringBuilder("select adType, id, campaign_id, ad_group_id, ad_group_type, puid, shop_id, state, name, marketplace_id,default_bid,serving_status from ( ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        List<Object> argsList = new ArrayList<>();

        List<String> groupSqlList = new ArrayList<>(3);

        types.forEach(x -> {
            String groupSql = getQueryGroupSql(puid, shopId, groupName, campaignIds, adGroupTypes, statusList, x, argsList);
            groupSqlList.add(groupSql);
        });

        sql.append(StringUtils.join(groupSqlList, " union all "));
        countSql.append(StringUtils.join(groupSqlList, " union all "));

        sql.append(" ) t ");
        sql.append(" ORDER BY CASE state WHEN 'enabled' THEN 1 WHEN 'paused' THEN 2 WHEN null THEN 3 WHEN 'archived' THEN 4 ELSE 5 END ");
        countSql.append(" ) t ");

        return getPageResult(puid, pageNo, pageSize, String.valueOf(countSql), argsList.toArray(), String.valueOf(sql), argsList.toArray(), null);
    }


    private String getQueryGroupSql(Integer puid, Integer shopId, String groupName, List<String> campaignIds, List<String> adGroupTypes, List<String> statusList, String adType, List<Object> args) {
        String tableName = getJdbcHelper().getTable();
        StringBuilder sql = new StringBuilder();
        if (CampaignTypeEnum.sp.getCampaignType().equals(adType)) {
            sql.append(" select 'sp' adType, id, campaign_id, ad_group_id, ad_group_type, puid, shop_id, state, name, marketplace_id, default_bid, serving_status from ");
            sql.append(tableName);
        } else if (CampaignTypeEnum.sb.getCampaignType().equals(adType)) {
            tableName += ("_" + adType);
            sql.append(" select 'sb' adType, id, campaign_id, ad_group_id, ad_group_type, puid, shop_id, state, name, marketplace_id, bid as default_bid, serving_status from ");
            sql.append(tableName);
        } else {
            sql.append(" select 'sd' adType, id, campaign_id, ad_group_id, '' as ad_group_type, puid, shop_id, state, name, marketplace_id, default_bid, serving_status from ");
            tableName += ("_" + adType);
            sql.append(tableName);
        }

        sql.append(" where puid = ? and shop_id = ?");
        args.add(puid);
        args.add(shopId);

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, args));
        }

        if (StringUtils.isNotBlank(groupName)) {
            sql.append(" and name like ?");
            args.add('%' + groupName + '%');
        }

        if (CollectionUtils.isNotEmpty(statusList)) {
            sql.append(SqlStringUtil.dealInList("state", statusList, args));
        }

        if (CollectionUtils.isNotEmpty(adGroupTypes) &&
                (CampaignTypeEnum.sp.getCampaignType().equals(adType)
                        || CampaignTypeEnum.sb.getCampaignType().equals(adType))) {
            sql.append(SqlStringUtil.dealInList("ad_group_type", adGroupTypes, args));
        }

        return sql.toString();
    }

    @Override
    public List<AmazonAdGroup> queryGroupList(AllUpdateAutoRuleParam param) {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.equalTo("puid", param.getPuid());
        conditionBuilder.equalTo("shop_id", param.getShopId());
        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) {
            conditionBuilder.in("campaign_id", param.getCampaignIds().toArray());
        }
        if (StringUtils.isNotBlank(param.getState())) {
            conditionBuilder.equalTo("state", param.getState());
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            conditionBuilder.like("name", param.getSearchValue());
        }
        return listByCondition(param.getPuid(), conditionBuilder.build());
    }

    @Override
    public AmazonAdGroup getByAdGroupName(int puid, Integer shopId, String campaignId, String name) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("name", name)
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public List<AmazonAdGroup> listByPuidAndCampaignIds(int puid, List<String> campaignIds) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inStrList("campaign_id", campaignIds.toArray(new String[0]))
                .build());
    }

    @Override
    public List<String> getAdGroupByIds(Integer puid, Integer shopId) {
        String sql = "select ad_group_id id from t_amazon_ad_group where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("state", new Object[]{"enabled", "paused"})
                .build();
        sql += conditionBuilder.getSql();
        return getJdbcTemplate(puid).queryForList(sql, String.class, conditionBuilder.getValues());
    }

    @Override
    public List<AmazonAdGroup> listByPuidAndCampaignId(int puid, int shopId, String campaignId) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .build());
    }

    @Override
    public List<AmazonAdGroupTypeBO> getTAdGroupTypeByAdGroupIdList(Integer puid, Integer shopId, List<String> adGroupIdList) {
        StringBuilder sql = new StringBuilder("select ad_group_id, ad_group_type from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? ");
        sql.append(" and shop_id = ? ");
        sql.append(" and ad_group_id in ('").append(StringUtils.join(adGroupIdList, "','")).append("')");
        return getJdbcTemplate(puid).query(sql.toString(), new BeanPropertyRowMapper<>(AmazonAdGroupTypeBO.class), puid, shopId);
    }

    @Override
    public void autoRuleUpdate(AdvertiseRuleTaskExecuteRecordV2Message message) {
        StringBuilder updateSql = new StringBuilder("update t_amazon_ad_group ");
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? and ad_group_id = ?");
        List<Object> args = Lists.newArrayList();
        if ("restore".equals(message.getOperation().name())) {
            updateSql.append(" set default_bid=?");
            args.add(message.getRecoveryAdjustment().getExecuteValue());
        } else {
            if ("stateClose".equals(message.getPerformOperation().get(0).getRuleAction().name())) {
                updateSql.append(" set state=?");
                args.add(message.getPerformOperation().get(0).getExecuteValue());
            } else {
                updateSql.append(" set default_bid=?");
                args.add(message.getPerformOperation().get(0).getExecuteValue());
            }
        }
        updateSql.append(whereSql);
        args.add(message.getPuid());
        args.add(message.getShopId());
        args.add(message.getItemId());
        getJdbcTemplate(message.getPuid()).update(updateSql.toString(), args.toArray());
    }

    @Override
    public void insertList4BatchCreate(Integer puid, List<AmazonAdGroup> adGroupList) {
        StringBuilder sql = new StringBuilder(" INSERT INTO ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" (puid, shop_id, profile_id, marketplace_id, name, state, ad_group_type, ");
        sql.append(" default_bid, campaign_id, ad_group_id, create_time, create_id) values ");
        List<Object> argsList = new ArrayList<>(adGroupList.size());
        for (AmazonAdGroup campaign : adGroupList) {
            sql.append(" (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),");
            argsList.add(campaign.getPuid());
            argsList.add(campaign.getShopId());
            argsList.add(campaign.getProfileId());
            argsList.add(campaign.getMarketplaceId());
            argsList.add(campaign.getName());
            argsList.add(campaign.getState());
            argsList.add(campaign.getAdGroupType());
            argsList.add(campaign.getDefaultBid());
            argsList.add(campaign.getCampaignId());
            argsList.add(campaign.getAdGroupId());
            argsList.add(campaign.getCreateTime());
            argsList.add(campaign.getCreateId());
        }
        sql.deleteCharAt(sql.length() - 1);
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public Page<AmazonAdGroup> getPageListByStrategy(AdStrategyGroupParam param) {
        StringBuilder selectSql = new StringBuilder("select g.puid,g.shop_id,g.marketplace_id,g.ad_group_id,g.campaign_id,g.name,g.default_bid,g.serving_status,g.state,g.create_time " +
                " from t_amazon_ad_group g left join t_amazon_ad_campaign_all c on (g.puid = c.puid and g.shop_id and g.marketplace_id = c.marketplace_id and g.campaign_id = c.campaign_id) ");
        StringBuilder countSql = new StringBuilder("select count(g.id) from t_amazon_ad_group g left join t_amazon_ad_campaign_all c on (g.puid = c.puid and g.shop_id and g.marketplace_id = c.marketplace_id and g.campaign_id = c.campaign_id) ");
        StringBuilder whereSql = new StringBuilder(" where g.puid=? and g.shop_id=? and g.ad_group_id is not null  and g.is_state_bidding = 0 and c.state in ('enabled','paused')");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(param.getPuid());
        argsList.add(param.getShopId());
        if (StringUtils.isNotBlank(param.getState())) {
            whereSql.append(" and g.state = ? ");
            argsList.add(param.getState());
        } else {
            whereSql.append(" and g.state in ('enabled','paused')");
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            whereSql.append(SqlStringUtil.dealInList("g.campaign_id", param.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(param.getAdGroupName())) {
            whereSql.append(" and g.name like ? ");
            argsList.add("%" + param.getAdGroupName() + "%");
        }
        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            whereSql.append(SqlStringUtil.dealInList("g.serving_status", param.getServingStatusList(), argsList));
        }
        selectSql.append(whereSql);
        countSql.append(whereSql);
        Object[] args = argsList.toArray();
        return this.getPageResult(param.getPuid(), param.getPageNo(), param.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdGroup.class);
    }

    @Override
    public List<String> queryAdGroupIdList(ControlledObjectParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", param.getPuid());
        builder.equalTo("shop_id", param.getShopId());
        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) {
            builder.in("campaign_id", param.getCampaignIds().toArray());
        }
        if (StringUtils.isNotBlank(param.getState())) {
            builder.equalTo("state", param.getState());
        }
        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            builder.in("serving_status", param.getServingStatusList().toArray());
        }
        if (StringUtils.isNotEmpty(param.getSearchValue())) {
            builder.like("name", param.getSearchValue());
        }
        return listDistinctFieldByCondition(param.getPuid(), "ad_group_id", builder.build(), String.class);
    }

    @Override
    public void updatePricing(Integer puid, Integer shopId, String adGroupId, Integer isPricing, Integer pricingState, int updateId) {
        String sql = "update t_amazon_ad_group set is_state_bidding=?,pricing_state_bidding=?,update_id=?,update_time=now() where puid = ? and shop_id = ? and `ad_group_id`=?";
        getJdbcTemplate(puid).update(sql, new Object[]{isPricing, pricingState, updateId, puid, shopId, adGroupId});
    }

    @Override
    public Page<GroupInfoPageVo> getAllGroupPage(Integer puid, GroupPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuffer countSql = new StringBuffer("SELECT COUNT(*) FROM t_amazon_ad_group ");
        StringBuffer selectSql = new StringBuffer("SELECT id, 'sp' as type, puid, shop_id shopId, marketplace_id marketplaceId, ad_group_id adGroupId, campaign_id campaignId, name name, profile_id profileId, state state, serving_status servingStatus, ");
        selectSql.append(" default_bid defaultBid, ad_group_type adGroupType, publish_state publishState, create_id createId ");
        selectSql.append(" FROM t_amazon_ad_group ");
        String whereSql = getSpGroupPageWhereSql(puid, param, null, argsList);
        selectSql.append(whereSql);
        AdGroupDefaultOrderEnum orderEn = AdGroupDefaultOrderEnum.getAdGroupDefaultOrderEnumByKey(param.getOrderField());
        if (StringUtils.isNotBlank(param.getOrderType()) && Objects.nonNull(orderEn)) {
            selectSql.append(" order by ").append(orderEn.getOrderField()).append(OrderTypeEnum.desc.getType().equals(param.getOrderType()) ? " desc" : "").append(" ,id desc ");
        } else {
            selectSql.append(" order by id desc ");
        }
        countSql.append(whereSql);//check 右括号
        Object[] args = argsList.toArray();
        return getPageResultByClass(puid, param.getPageNo(), param.getPageSize(), countSql.toString(), args, selectSql.toString(), args, GroupInfoPageVo.class);
    }

    @Override
    public List<String> getGroupIdListByParamAndIds(Integer puid, GroupPageParam param, List<String> adGroupIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ad_group_id from t_amazon_ad_group ");
        sb.append(this.getSpGroupPageWhereSql(puid, param, adGroupIdList, argsList));
        return getJdbcTemplate(puid).queryForList(sb.toString(), argsList.toArray(), String.class);
    }

    @Override
    public List<AllGroupOrderBo> getGroupIdAndOrderFieldList(Integer puid, GroupPageParam param, List<String> adGroupIdList, String orderField) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ad_group_id id")
                .append(StringUtils.isNotBlank(orderField) ? "," + orderField + " orderField" : "")
                .append(" from t_amazon_ad_group ");
        sb.append(this.getSpGroupPageWhereSql(puid, param, adGroupIdList, argsList));
        return getJdbcTemplate(puid).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AllGroupOrderBo.class));
    }

    @Override
    public List<GroupInfoPageVo> getGroupPageVoListByGroupIdList(Integer puid, GroupPageParam param, List<String> adGroupIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuffer selectSql = new StringBuffer("SELECT id, 'sp' as type, puid, shop_id shopId, marketplace_id marketplaceId, ad_group_id adGroupId, campaign_id campaignId, name name, profile_id profileId, state state, serving_status servingStatus, ");
        selectSql.append(" default_bid defaultBid, ad_group_type adGroupType, publish_state publishState, create_id createId ");
        selectSql.append(" FROM t_amazon_ad_group g ");
        selectSql.append(" where g.puid = ? and g.shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (CollectionUtils.isNotEmpty(adGroupIdList)) {
            selectSql.append(SqlStringUtil.dealInList("g.ad_group_id", adGroupIdList, argsList));
            selectSql.append(" order by field(ad_group_id, ").append(StringUtil.joinString(adGroupIdList)).append(")");
        }
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(GroupInfoPageVo.class));
    }

    @Override
    public List<AllGroupOrderBo> getGroupIdAndIndexList(Integer puid, GroupPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ad_group_id id, ")
                .append(SqlStringReportUtil.getGroupSpOrderField(param.getOrderField())).append(" orderField ")
                .append(" from t_amazon_ad_group c ");
        sb.append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        sb.append(" and count_date >= ? and count_date <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (CollectionUtils.isNotEmpty(param.getGroupIds())) {
            sb.append(SqlStringUtil.dealInList("c.ad_group_id", param.getCampaignIdList(), argsList));
        }
        sb.append(" group by c.ad_group_id ");
        if (param.getUseAdvanced()) {
            sb.append(this.getSpGroupPageHavingSql(param, argsList));
        }
        sb.append(" limit " + Constants.GROUP_PAGE_SUM_METRIC_TIME);
        return getJdbcTemplate(puid).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AllGroupOrderBo.class));
    }

    private String getSpGroupPageHavingSql(GroupPageParam param, List<Object> argsList) {
        ReportAdvancedFilterBaseQo qo = new ReportAdvancedFilterBaseQo();
        BeanUtils.copyProperties(param, qo);
        return SqlStringReportUtil.getSpGroupPageHavingSql(qo, argsList);
    }

    private String getSpGroupPageWhereSql(Integer puid, GroupPageParam param, List<String> groupIdList, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid = ? ");
        argsList.add(puid);
        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }
        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId(), ",");
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
        }
        if (StringUtils.isNotBlank(param.getMultiCampaignId())) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", StringUtil.splitStr(param.getMultiCampaignId()), argsList));
        }
        // 广告组Id查询
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getGroupId(), ",");
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, argsList));
        }
        if (StringUtils.isNotBlank(param.getMultiGroupId())){
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", StringUtil.splitStr(param.getMultiGroupId()), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(groupIdList)) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIdList, argsList));
        }
        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> status = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", status, argsList));
        }
        //广告组查询
        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if (StringUtils.isNotBlank(param.getSearchType()) && SearchTypeEnum.EXACT.getValue().equals(param.getSearchType())) {
                whereSql.append(" and name = ? ");
                argsList.add(param.getSearchValue().trim());
            } else {
                whereSql.append(" and ").append(param.getSearchField()).append(" like ? ");
                argsList.add("%" + param.getSearchValue().trim() + "%");
            }
        }

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            whereSql.append(SqlStringUtil.dealInList("name", param.getSearchValueList(), argsList));
        }
        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ? ");
                argsList.add(AmazonAdGroup.servingStatusEnum.AD_GROUP_STATUS_ENABLED.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }
        }
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (param.getBidMin() != null) {   //默认竞价
                whereSql.append(" and default_bid >= ?");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and default_bid <= ?");
                argsList.add(param.getBidMax());
            }
        }
        return whereSql.toString();
    }

    @Override
    public AmazonAdGroup getByCampaignIdAndAdGroupId(Integer puid, Integer shopId, String campaignId, String adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("ad_group_id", adGroupId)
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public List<AmazonAdGroup> getInfoByCampaignIdAndGroupId(Integer puid, Integer shopId, String campaignId, String adGroupId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId);
        if (StringUtils.isNotEmpty(adGroupId)) {
            builder.equalTo("ad_group_id", adGroupId);
        }
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<String> queryAdGroupIdList(ProcessTaskParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", param.getPuid());
        builder.equalTo("shop_id", param.getShopId());
        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) {
            builder.in("campaign_id", param.getCampaignIds().toArray());
        }
        if (StringUtils.isNotBlank(param.getState())) {
            builder.equalTo("state", param.getState());
        }
        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            builder.in("serving_status", param.getServingStatusList().toArray());
        }
        if (StringUtils.isNotEmpty(param.getSearchValue())) {
            builder.like("name", param.getSearchValue());
        }
        return listDistinctFieldByCondition(param.getPuid(), "ad_group_id", builder.build(), String.class);
    }

    @Override
    public List<String> queryAutoRuleAdGroupIdList(com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", param.getPuid());
        builder.equalTo("shop_id", param.getShopId());
        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) {
            builder.in("campaign_id", param.getCampaignIds().toArray());
        }
        if (CollectionUtils.isNotEmpty(param.getItemIdList())) {
            builder.in("ad_group_id", param.getItemIdList().toArray());
        }
        if (StringUtils.isNotBlank(param.getState())) {
            builder.equalTo("state", param.getState());
        }
        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            builder.in("serving_status", param.getServingStatusList().toArray());
        }
        if (StringUtils.isNotEmpty(param.getSearchValue())) {
            builder.like("name", param.getSearchValue());
        }
        return listDistinctFieldByCondition(param.getPuid(), "ad_group_id", builder.build(), String.class);
    }

    @Override
    public List<GpsAdGroup> getGpsAdGroupByName(int puid, List<String> campaignIdList, List<String> nameList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select ad_group_id as adGroupId, name as adGroupName, campaign_id as campaignId")
                .append(" from t_amazon_ad_group where puid = ? and state != 'archived' ");
        argsList.add(puid);
        sql.append(SqlStringUtil.dealInList("name", nameList, argsList));
        return getJdbcTemplate(puid).query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(GpsAdGroup.class));
    }

    @Override
    public Page<MultiShopGroupListVo> getMultiShopGroupList(Integer puid, MultiShopGroupListParam param) {
        StringBuilder sql = new StringBuilder("SELECT shop_id shopId, campaign_id campaignId, ad_group_id adGroupId, name, state, ad_type adType FROM ( ");
        StringBuilder countSql = new StringBuilder("SELECT count(*) FROM ( ");
        List<Object> args = new ArrayList<>();

        List<String> groupSqlList = new ArrayList<>(3);
        if (MapUtils.isEmpty(param.getCampaignIdMap())) {
            if (CollectionUtils.isEmpty(param.getAdTypeList())) {
                param.setAdTypeList(Arrays.asList(CampaignTypeEnum.sp.getCampaignType(),
                        CampaignTypeEnum.sb.getCampaignType(),
                        CampaignTypeEnum.sd.getCampaignType()));
            }
            param.getAdTypeList().forEach(x -> {
                String groupSql = getMultiShopGroupSqlByType(param, x, args);
                if (StringUtils.isNotBlank(groupSql)) {
                    groupSqlList.add(groupSql);
                }
            });
        } else {
            param.getCampaignIdMap().forEach((k, v) -> {
                String groupSql = getMultiShopGroupSqlByType(param, k, args);
                if (StringUtils.isNotBlank(groupSql)) {
                    groupSqlList.add(groupSql);
                }
            });
        }

        if (CollectionUtils.isEmpty(groupSqlList)) {
            return new Page<MultiShopGroupListVo>(param.getPageNo(), param.getPageSize(), 0, 0, Lists.newArrayList());
        }

        sql.append(StringUtils.join(groupSqlList, " union all "));
        countSql.append(StringUtils.join(groupSqlList, " union all "));

        sql.append(" ) t");
        if (Boolean.TRUE.equals(param.getNeedOrder())) {
            sql.append(" order by")
                    .append(" case state")
                    .append(" when 'enabled' then 1")
                    .append(" when 'paused' then 2")
                    .append(" else 3")
                    .append(" end ");
        }

        countSql.append(" ) t");

        return this.getPageResultByClass(puid,
                param.getPageNo(),
                param.getPageSize(),
                countSql.toString(),
                args.toArray(),
                sql.toString(),
                args.toArray(),
                MultiShopGroupListVo.class);
    }

    private String getMultiShopGroupSqlByType(MultiShopGroupListParam param, String adType, List<Object> args) {
        //sd没有关键词投放，不生成sql
        if (CampaignTypeEnum.sd.getCampaignType().equals(adType) && Constants.GROUP_TYPE_KEYWORD.equals(param.getGroupType())) {
            return "";
        }

        String tableName = getJdbcHelper().getTable();
        if (CampaignTypeEnum.sb.getCampaignType().equals(adType) || CampaignTypeEnum.sd.getCampaignType().equals(adType)) {
            tableName += ("_" + adType);
        }
        StringBuilder sql = new StringBuilder("SELECT shop_id, campaign_id, ad_group_id, name, state, ");
        sql.append("'" + adType + "' as ad_type from ");
        sql.append(tableName);
        sql.append(" where puid = ? ");
        args.add(param.getPuid());
        sql.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), args));

        if (CollectionUtils.isNotEmpty(param.getMarketplaceId())) {
            sql.append(SqlStringUtil.dealInList("marketplace_id", param.getMarketplaceId(), args));
        }

        if (MapUtils.isNotEmpty(param.getCampaignIdMap())) {
            if (CollectionUtils.isNotEmpty(param.getCampaignIdMap().get(adType))) {
                sql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdMap().get(adType), args));
            }
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIdList())) {
            sql.append(SqlStringUtil.dealInList("ad_group_id", param.getGroupIdList(), args));
        }

        if (CollectionUtils.isNotEmpty(param.getStateList())) {
            sql.append(SqlStringUtil.dealInList("state", param.getStateList(), args));
        }

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            if ("exact".equals(param.getSearchType())) {
                sql.append(" and name = ? ");
                args.add(param.getSearchValue().trim());
            }else {
                sql.append(" and name like ? ");
                args.add("%" + param.getSearchValue().trim() + "%");
            }
        }
        if (StringUtils.isNotBlank(param.getAdGroupType()) && !CampaignTypeEnum.sd.getCampaignType().equals(adType)) {
            sql.append(" and (ad_group_type = ? or ad_group_type is null or ad_group_type = '' )");
            args.add(param.getAdGroupType());
        }

        if (CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            sql.append(SqlStringUtil.dealInList("name", param.getSearchValueList(), args));
        }

        //广告组类型
        if (StringUtils.isNotBlank(param.getGroupType())) {
            if (CampaignTypeEnum.sp.getCampaignType().equals(adType)) {
                List<String> spTypeList = Lists.newArrayList(Constants.GROUP_TYPE_AUTO, param.getGroupType());
                sql.append(" and (ad_group_type = '' or ad_group_type is null or ");
                sql.append(SqlStringUtil.dealInListNotAnd("ad_group_type", spTypeList, args));
                sql.append(" ) ");
            } else if (CampaignTypeEnum.sb.getCampaignType().equals(adType)) {
                String sbAdgroupType = Constants.GROUP_TYPE_TARGETING.equals(param.getGroupType()) ? Constants.GROUP_TYPE_PRODUCT : param.getGroupType();
                sql.append(" and (ad_group_type = '' or ad_group_type is null or ad_group_type = ? ) ");
                args.add(sbAdgroupType);
            }
        }
        return sql.toString();
    }

    @Override
    public List<MultiShopGroupListVo> getByShopGroupIdPair(Integer puid, List<MultiShopGroupListParam> paramList) {
        StringBuilder sql = new StringBuilder("SELECT shop_id shopId, campaign_id campaignId, ad_group_id adGroupId, name, state, ad_type adType FROM ( ");
        List<Object> args = new ArrayList<>();

        List<String> groupSqlList = new ArrayList<>(3);
        Arrays.asList(CampaignTypeEnum.sp.getCampaignType(), CampaignTypeEnum.sb.getCampaignType(), CampaignTypeEnum.sd.getCampaignType())
                .forEach(type -> {
                    String groupSql = getGroupSqlByType(puid,paramList, type, args);
                    groupSqlList.add(groupSql);
                });

        sql.append(StringUtils.join(groupSqlList, " union all "));
        sql.append(" ) t");
        return getJdbcTemplate(puid).query(sql.toString(), args.toArray(), new BeanPropertyRowMapper<>(MultiShopGroupListVo.class));
    }

    private String getGroupSqlByType(Integer puid, List<MultiShopGroupListParam> paramList, String adType, List<Object> args) {
        String tableName = getJdbcHelper().getTable();
        if (CampaignTypeEnum.sb.getCampaignType().equals(adType) || CampaignTypeEnum.sd.getCampaignType().equals(adType)) {
            tableName += ("_" + adType);
        }
        StringBuilder sql = new StringBuilder("SELECT shop_id, campaign_id, ad_group_id, name, state, ");
        sql.append("'" + adType + "' as ad_type from ");
        sql.append(tableName);
        sql.append(" where puid = ? ");
        args.add(puid);

        String multiIn = SqlStringUtil.dealMultiInList(Arrays.asList("shop_id", "ad_group_id"), paramList, args,
                Arrays.asList(i-> String.valueOf(i.getShopId()), MultiShopGroupListParam::getAdGroupId));
        sql.append(multiIn);

        return sql.toString();
    }

    @Override
    public List<String> queryArchivedByGroupIdList(Integer puid, Integer shopId, List<String> groupIdList) {
        StringBuilder sql = new StringBuilder();
        sql.append("select ad_group_id itemId from t_amazon_ad_group ");
        List<Object> args = new ArrayList<>(2 + groupIdList.size());
        sql.append(" where puid = ? and shop_id = ? and state = 'archived' ");
        args.add(puid);
        args.add(shopId);
        sql.append(SqlStringUtil.dealInList("ad_group_id", groupIdList, args));
        return getJdbcTemplate(puid).query(sql.toString(), new SingleColumnRowMapper<>(String.class), args.toArray());
    }

    @Override
    public List<String> getAdGroupTypeByCampaignId(Integer puid, Integer shopId, String campaignId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("shop_id", shopId);
        builder.equalTo("campaign_id", campaignId);
        builder.isNotNull("ad_group_type");
        builder.in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()});
        return listDistinctFieldByCondition(puid, "ad_group_type", builder.build(), String.class);
    }

    @Override
    public List<AmazonAdGroup> listByCampaignIdLimit(Integer puid, Integer shopId, String campaignId, Integer limit) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id",shopId)
                .equalTo("campaign_id",campaignId)
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()})
                .limit(limit);
        return listByCondition(puid,builder.build());
    }

    @Override
    public List<AmazonAdGroup> getNameAndTypeAndStateByShopIdsAndGroupIds(Integer puid, List<Integer> shopIdList, List<String> spGroupIdList, String groupName) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inIntList("shop_id", shopIdList.toArray(new Integer[0]))
                .inStrList("ad_group_id", spGroupIdList.toArray(new String[0]));
        if (StringUtils.isNotEmpty(groupName)) {
            builder.like("name", groupName);
        }
        return listByConditionWithFields(puid, Arrays.asList("ad_group_id", "name", "ad_group_type", "state"), builder.build());
    }

    @Override
    public Collection<String> getByShopIdsAndCampaignIds(Integer puid, List<Integer> shopIdList, List<String> campaignList) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(shopIdList)) {
            builder.in("shop_id", shopIdList.toArray());
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(campaignList)) {
            builder.in("campaign_id", campaignList.toArray());
        }
        return listDistinctFieldByCondition(puid, "ad_group_id", builder.build(), String.class);
    }

    @Override
    public List<DownloadCenterGroupBaseDataBO> queryBaseData4DownloadByShopAdGroup(Integer puid, Integer shopId, List<String> adGroupIdList) {
        StringBuilder sql = new StringBuilder("select ad_group_id id, ad_group_type adGroupType, state from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? ");
        sql.append(" and shop_id = ? ");
        sql.append(" and ad_group_id in ('").append(StringUtils.join(adGroupIdList, "','")).append("')");
        return getJdbcTemplate(puid).query(sql.toString(), new BeanPropertyRowMapper<>(DownloadCenterGroupBaseDataBO.class), puid, shopId);
    }
}