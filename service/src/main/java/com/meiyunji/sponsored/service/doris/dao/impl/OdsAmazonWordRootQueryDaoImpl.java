package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryKeywordReport;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryTargetingReport;
import com.meiyunji.sponsored.service.cpc.po.CpcSbQueryKeywordReport;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.CpcQueryWordDto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonWordRootQueryDao;
import com.meiyunji.sponsored.service.doris.dao.helper.SearchQueryTagSqlHelper;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonWordRootQuery;
import com.meiyunji.sponsored.service.enums.MatchValueEnum;
import com.meiyunji.sponsored.service.enums.QueryMatchTypeEnum;
import com.meiyunji.sponsored.service.enums.TargetMatchTypeEnum;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdWordRootDataDto;
import com.meiyunji.sponsored.service.util.Constant;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordRootSbPageBo;
import com.meiyunji.sponsored.service.wordFrequency.dto.WordFrequencyAdMetricDto;
import com.meiyunji.sponsored.service.wordFrequency.enums.WordRoot;
import com.meiyunji.sponsored.service.wordFrequency.qo.GetWordRootAggregateDataQo;
import com.meiyunji.sponsored.service.wordFrequency.qo.GetWordRootDataQo;
import com.meiyunji.sponsored.service.wordFrequency.qo.QueryWordTopQo;
import com.meiyunji.sponsored.service.wordFrequency.vo.GetWordRootAggregateDataVo;
import com.meiyunji.sponsored.service.wordFrequency.vo.GetWordRootDataVo;
import com.meiyunji.sponsored.service.wordFrequency.vo.WordRootTopVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-05-23  20:17
 */
@Repository
@Slf4j
public class OdsAmazonWordRootQueryDaoImpl extends DorisBaseDaoImpl<OdsAmazonWordRootQuery> implements IOdsAmazonWordRootQueryDao {
    @Override
    public List<DashboardAdWordRootDataDto> queryAdWordRootCharts(Integer puid, List<String> marketplaceIdList, List<Integer> shopIdList,
                                                                  String currency, String startDate, String endDate, List<Integer> wordFrequencyTypeList,
                                                                  String orderBy, Integer limit, List<String> siteToday, Boolean isSiteToday,
                                                                  List<String> portfolioIds, List<String> campaignIds, Boolean noZero) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("select word_root wordRoot, count(distinct query_id) `frequency`, ifnull(sum(cost * c.rate), 0) cost, ifnull(sum(total_sales * c.rate), 0) totalSales, ifnull(sum(impressions),0) impressions, ");
        sql.append("ifnull(sum(clicks),0) clicks, ifnull(sum(order_num),0) orderNum, ifnull(sum(sale_num),0) saleNum from ");
        sql.append(getJdbcHelper().getTable()).append(" w ");

        sql.append("join (select m.marketplace_id,c.month,c.rate from dim_currency_rate join dim_marketplace_info m ");
        sql.append("on c.`from` = m.currency and c.puid=? and `to` = ? and month >= ? and month <= ?) c ");
        sql.append("on w.marketplace_id = c.marketplace_id and w.count_month = c.month and ");
        sql.append("w.puid = ? and w.query not REGEXP '^[bB]{1}0[A-Za-z0-9]{8}$' ");

        argsList.add(puid);
        argsList.add(currency);
        argsList.add(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        argsList.add(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        argsList.add(puid);

        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sql.append("and w.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append("and w.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        sql.append("and w.word_frequency_type in ('").append(StringUtils.join(wordFrequencyTypeList, "','")).append("') ");


        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sql.append(SqlStringUtil.dealDorisInList("concat_ws('|', w.marketplace_id, w.count_day)", siteToday, argsList));
            sql.append(" and w.count_day >= ? and w.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sql.append(" and w.count_day >= ? and w.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealDorisInList("w.campaign_id", campaignIds, argsList));
        }
        if (Boolean.TRUE.equals(noZero)) {
            sql.append(" and frequency <> 0 ");
        }

        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sql.append(" where w.campaign_id in ( ");
            sql.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sql.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sql.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sql.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sql.append(" ) ");
        }
        sql.append("group by wordRoot order by `frequency` ");
        sql.append(orderBy);
        sql.append(" limit ? ");
        argsList.add(limit);
        log.info(sql.toString());
        return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(DashboardAdWordRootDataDto.class), argsList.toArray());
    }

    @Override
    public Page<GetWordRootDataVo> pageList(Integer puid, GetWordRootDataQo qo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder countSqlSb = new StringBuilder("select count(*) from ( ");
        StringBuilder sqlSb = new StringBuilder("select word_root wordRoot, count(distinct query_id) `frequency` ")
                .append(" from ").append(this.getJdbcHelper().getTable())
                .append(this.getWhereSqlByPageList(qo, argsList, true))
                .append(" group by word_root ")
                .append(this.getSpHavingSqlByPageList(qo, argsList));
        countSqlSb.append(sqlSb).append(" ) s ");
        //排序
        this.getOrderByPageList(qo, sqlSb, true);

        Object[] array = argsList.toArray();
        return getPageResultByClass(qo.getPageNo(), qo.getPageSize(), countSqlSb.toString(), array, sqlSb.toString(), array, GetWordRootDataVo.class);
    }

    @Override
    public List<GetWordRootDataVo> pageListByWordRoots(Integer puid, GetWordRootDataQo qo, List<String> wordRoots) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select word_root wordRoot, count(distinct query_id) `frequency`, ")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) adCost,SUM(sale_num) adOrderNum,SUM(ad_order_num) adSelfSaleNum,")
                .append("SUM(total_sales) adSale, SUM(ad_sales) `adSales`,SUM(ad_sale_num) `adSaleNum`, SUM(order_num) orderNum")
                .append(" from ").append(this.getJdbcHelper().getTable())
                .append(this.getWhereSqlByPageList(qo, argsList, true))
                .append(SqlStringUtil.dealInList("word_root", wordRoots, argsList))
                .append(" group by word_root ")
                .append(SqlStringUtil.orderByField("word_root", wordRoots, argsList));

        Object[] array = argsList.toArray();
        return getJdbcTemplate().query(sqlSb.toString(), array, new BeanPropertyRowMapper<>(GetWordRootDataVo.class));
    }

    @Override
    public WordFrequencyAdMetricDto getPageListAdMetricDto(Integer puid, GetWordRootDataQo qo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select SUM(frequency) sumFrequency, SUM(adCost) sumCost, SUM(adSale) sumAdSale, SUM(adOrderNum) sumAdOrderNum, SUM(orderNum) sumOrderNum from (");
        sqlSb.append("select word_root wordRoot, count(distinct query_id) `frequency`, ")
                .append(" SUM(cost) adCost, SUM(total_sales) adSale, SUM(sale_num) adOrderNum, SUM(order_num) orderNum ")
                .append(" from ").append(this.getJdbcHelper().getTable())
                .append(this.getWhereSqlByPageList(qo, argsList, true))
                .append(" group by word_root ")
                .append(this.getSpHavingSqlByPageList(qo, argsList));
        sqlSb.append(" ) s");
        List<WordFrequencyAdMetricDto> list = getJdbcTemplate().query(sqlSb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordFrequencyAdMetricDto.class));
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    @Override
    public GetWordRootAggregateDataVo getPageListAggregateData(Integer puid, GetWordRootAggregateDataQo qo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select SUM(`frequency`) `frequency`, ")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(adCost) adCost,SUM(adOrderNum) adOrderNum,SUM(adSelfSaleNum) adSelfSaleNum,")
                .append("SUM(adSale) adSale, SUM(adSales) `adSales`,SUM(adSaleNum) `adSaleNum`, SUM(orderNum) orderNum")
                .append(" from (");
        sqlSb.append("select word_root wordRoot, count(distinct query_id) `frequency`, ")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) adCost,SUM(sale_num) adOrderNum,SUM(ad_order_num) adSelfSaleNum,")
                .append("SUM(total_sales) adSale, SUM(ad_sales) `adSales`,SUM(ad_sale_num) `adSaleNum`, SUM(order_num) orderNum ")
                .append(" from ").append(this.getJdbcHelper().getTable())
                .append(this.getWhereSqlByPageList(qo, argsList, true))
                .append(" group by word_root ")
                .append(this.getSpHavingSqlByPageList(qo, argsList));
        sqlSb.append(" ) s");
        List<GetWordRootAggregateDataVo> list = getJdbcTemplate().query(sqlSb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(GetWordRootAggregateDataVo.class));
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : new GetWordRootAggregateDataVo();
    }

    @Override
    public Page<WordRootSbPageBo> sbPageList(Integer puid, GetWordRootDataQo qo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder countSqlSb = new StringBuilder("select count(*) from ( ");
        StringBuilder sqlSb = new StringBuilder("select word_root wordRoot, count(distinct query_id) `frequency` ")
                .append(" from ").append(this.getJdbcHelper().getTable())
                .append(this.getWhereSqlByPageList(qo, argsList, false))
                .append(" group by word_root ")
                .append(this.getSbHavingSqlByPageList(qo, argsList));
        countSqlSb.append(sqlSb).append(" ) s ");
        //排序
        this.getOrderByPageList(qo, sqlSb, false);

        Object[] array = argsList.toArray();
        return getPageResultByClass(qo.getPageNo(), qo.getPageSize(), countSqlSb.toString(), array, sqlSb.toString(), array, WordRootSbPageBo.class);
    }

    @Override
    public List<WordRootSbPageBo> sbPageListByWordRoots(Integer puid, GetWordRootDataQo qo, List<String> wordRoots) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select word_root wordRoot, count(distinct query_id) `frequency`, ")
                .append(" sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`conversions14d`) conversions14d ")
                .append(" from ").append(this.getJdbcHelper().getTable())
                .append(this.getWhereSqlByPageList(qo, argsList, false))
                .append(SqlStringUtil.dealInList("word_root", wordRoots, argsList))
                .append(" group by word_root ")
                .append(SqlStringUtil.orderByField("word_root", wordRoots, argsList));

        Object[] array = argsList.toArray();
        return getJdbcTemplate().query(sqlSb.toString(), array, new BeanPropertyRowMapper<>(WordRootSbPageBo.class));
    }

    @Override
    public WordFrequencyAdMetricDto getSbPageListAdMetricDto(Integer puid, GetWordRootDataQo qo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select SUM(frequency) sumFrequency, SUM(cost) sumCost, SUM(sales14d) sumAdSale, SUM(conversions14d) sumAdOrderNum from (");
        sqlSb.append("select word_root wordRoot, count(distinct query_id) `frequency`, ")
                .append(" sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`conversions14d`) conversions14d ")
                .append(" from ").append(this.getJdbcHelper().getTable())
                .append(this.getWhereSqlByPageList(qo, argsList, false))
                .append(" group by word_root ")
                .append(this.getSbHavingSqlByPageList(qo, argsList));
        sqlSb.append(" ) s");
        List<WordFrequencyAdMetricDto> list = getJdbcTemplate().query(sqlSb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordFrequencyAdMetricDto.class));
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    @Override
    public WordRootSbPageBo getSbPageListAggregateData(Integer puid, GetWordRootAggregateDataQo qo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select SUM(`frequency`) `frequency`, ")
                .append(" sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`impressions`) impressions,sum(`clicks`) clicks, sum(`conversions14d`) conversions14d ")
                .append(" from (");
        sqlSb.append("select word_root wordRoot, count(distinct query_id) `frequency`, ")
                .append(" sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`impressions`) impressions,sum(`clicks`) clicks, sum(`conversions14d`) conversions14d ")
                .append(" from ").append(this.getJdbcHelper().getTable())
                .append(this.getWhereSqlByPageList(qo, argsList, false))
                .append(" group by word_root ")
                .append(this.getSbHavingSqlByPageList(qo, argsList));
        sqlSb.append(" ) s");
        List<WordRootSbPageBo> list = getJdbcTemplate().query(sqlSb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordRootSbPageBo.class));
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : new WordRootSbPageBo();
    }

    private String getWhereSqlByPageList(GetWordRootAggregateDataQo qo, List<Object> argsList, boolean isSp) {
        StringBuilder sb = new StringBuilder();
        sb.append(" where puid = ? and shop_id = ? and count_day >= ? and count_day <= ? ");
        argsList.add(qo.getPuid());
        argsList.add(qo.getShopId());
        argsList.add(qo.getStartDate());
        argsList.add(qo.getEndDate());
        //搜索词来源类型
        if (isSp) {
            sb.append(" and query_type in (?, ?) ");
            argsList.add(WordRoot.QueryType.SP_QUERY.getType());
            argsList.add(WordRoot.QueryType.SP_TARGETING.getType());
        } else {
            sb.append(" and query_type = ? ");
            argsList.add(WordRoot.QueryType.SB_QUERY.getType());
        }

        sb.append(" and query not REGEXP '" + Constants.ASIN_REGEX + "' ");
        List<String> matchTypeList = StringUtil.stringToList(qo.getMatchType(), StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        //不带匹配类型条件查询不走下面逻辑
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                matchType = matchType.replace("=", "");
                if (StringUtils.isNotBlank(QueryMatchTypeEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            //如果匹配条件不是('紧密匹配'，'宽泛匹配'),则不查询数据
            if (CollectionUtils.isNotEmpty(matchTypes)) {
                sb.append(SqlStringUtil.dealInList("lower(match_type)", matchTypes, argsList));
            }
        }
        if (CollectionUtils.isNotEmpty(qo.getCampaignIdList())) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", qo.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(qo.getGroupIds())) {
            List<String> list = StringUtil.splitStr(qo.getGroupIds());
            sb.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(qo.getGroupIdList())) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", qo.getGroupIdList(), argsList));
        }
        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchField()) && StringUtils.isNotBlank(qo.getSearchType())) {
            GetWordRootDataQo.SearchFieldEnum searchFieldEnum = GetWordRootDataQo.SearchFieldEnum.getSearchField(qo.getSearchField());
            if (searchFieldEnum != null) {
                if (SearchTypeEnum.BLUR.getValue().equals(qo.getSearchType())) { //模糊搜索
                    sb.append(" and ").append(searchFieldEnum.getColumn()).append(" like ? ");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(qo.getSearchValue().trim()) + "%");
                } else {//默认精确
                    List<String> searchValueList = StringUtil.splitStr(qo.getSearchValue().trim(), StringUtil.SPECIAL_COMMA);
                    if (searchValueList.size() > 1) {
                        sb.append(SqlStringUtil.dealInList(searchFieldEnum.getColumn(), searchValueList, argsList));
                    } else {
                        sb.append(" and ").append(searchFieldEnum.getColumn()).append(" = ?");
                        argsList.add(qo.getSearchValue().trim());
                    }
                }
            }
        }

        if (StringUtils.isNotBlank(qo.getWordFrequencyType())) {
            List<Integer> wordFrequencyTypeList = new ArrayList<>();
            for (String wordFrequencyType : qo.getWordFrequencyType().split(StringUtil.SPLIT_COMMA)) {
                WordRoot.WordFrequencyType wordFrequencyTypeEnum = WordRoot.WordFrequencyType.getWordFrequencyType(Integer.valueOf(wordFrequencyType));
                if (wordFrequencyTypeEnum != null) {
                    wordFrequencyTypeList.add(wordFrequencyTypeEnum.getType());
                }
            }
            if (CollectionUtils.isNotEmpty(wordFrequencyTypeList)) {
                sb.append(SqlStringUtil.dealInList("word_frequency_type", wordFrequencyTypeList, argsList));
            }
        }
        return sb.toString();
    }

    private String getSpHavingSqlByPageList(GetWordRootAggregateDataQo qo, List<Object> argsList) {
        StringBuilder sb = new StringBuilder();
        if (!qo.getUseAdvanced()) {
            return "";
        }

        BigDecimal shopSales = qo.getShopSales() != null ? qo.getShopSales() : BigDecimal.valueOf(0);

        sb.append(" having 1=1 ");
        //频率
        if (qo.getFrequencyMin() != null) {
            sb.append(" and frequency >= ? ");
            argsList.add(qo.getFrequencyMin());
        }
        if (qo.getFrequencyMax() != null) {
            sb.append(" and frequency <= ? ");
            argsList.add(qo.getFrequencyMax());
        }
        //花费
        if (qo.getCostMin() != null) {
            sb.append(" and SUM(cost) >= ? ");
            argsList.add(qo.getCostMin());
        }
        if (qo.getCostMax() != null) {
            sb.append(" and SUM(cost) <= ? ");
            argsList.add(qo.getCostMax());
        }
        //展示量
        if (qo.getImpressionsMin() != null) {
            sb.append(" and SUM(impressions) >= ? ");
            argsList.add(qo.getImpressionsMin());
        }
        if (qo.getImpressionsMax() != null) {
            sb.append(" and SUM(impressions) <= ? ");
            argsList.add(qo.getImpressionsMax());
        }
        //点击量
        if (qo.getClicksMin() != null) {
            sb.append(" and SUM(clicks) >= ? ");
            argsList.add(qo.getClicksMin());
        }
        if (qo.getClicksMax() != null) {
            sb.append(" and SUM(clicks) <= ? ");
            argsList.add(qo.getClicksMax());
        }
        //广告订单量
        if (qo.getOrderNumMin() != null) {
            sb.append(" and SUM(sale_num) >= ? ");
            argsList.add(qo.getOrderNumMin());
        }
        if (qo.getOrderNumMax() != null) {
            sb.append(" and SUM(sale_num) <= ? ");
            argsList.add(qo.getOrderNumMax());
        }
        //本广告产品订单量
        if (qo.getAdSaleNumMin() != null) {
            sb.append(" and ifnull(SUM(ad_sale_num), 0) >= ? ");
            argsList.add(qo.getAdSaleNumMin());
        }
        if (qo.getAdSaleNumMax() != null) {
            sb.append(" and ifnull(SUM(ad_sale_num), 0) <= ? ");
            argsList.add(qo.getAdSaleNumMax());
        }
        //广告销售额
        if (qo.getSalesMin() != null) {
            sb.append(" and SUM(total_sales) >= ? ");
            argsList.add(qo.getSalesMin());
        }
        if (qo.getSalesMax() != null) {
            sb.append(" and SUM(total_sales) <= ? ");
            argsList.add(qo.getSalesMax());
        }
        //本广告产品销售额
        if (qo.getAdSalesMin() != null) {
            sb.append(" and ifnull(SUM(ad_sales), 0) >= ? ");
            argsList.add(qo.getAdSalesMin());
        }
        if (qo.getAdSalesMax() != null) {
            sb.append(" and ifnull(SUM(ad_sales), 0) <= ? ");
            argsList.add(qo.getAdSalesMax());
        }
        //广告销量
        if (qo.getAdSalesTotalMin() != null) {
            sb.append(" and ifnull(SUM(order_num), 0) >= ? ");
            argsList.add(qo.getAdSalesTotalMin());
        }
        if (qo.getAdSalesTotalMax() != null) {
            sb.append(" and ifnull(SUM(order_num), 0) <= ? ");
            argsList.add(qo.getAdSalesTotalMax());
        }
        //本广告产品销量
        if (qo.getAdSelfSaleNumMin() != null) {
            sb.append(" and ifnull(SUM(ad_order_num), 0) >= ? ");
            argsList.add(qo.getAdSelfSaleNumMin());
        }
        if (qo.getAdSelfSaleNumMax() != null) {
            sb.append(" and ifnull(SUM(ad_order_num), 0) <= ? ");
            argsList.add(qo.getAdSelfSaleNumMax());
        }
        //ctr点击率（clicks/impressions）
        if (qo.getClickRateMin() != null) {
            sb.append(" and ROUND(ifnull(SUM(clicks)/SUM(impressions),0),4) >= ?");
            argsList.add(qo.getClickRateMin());
        }
        if (qo.getClickRateMax() != null) {
            sb.append(" and ROUND(ifnull(SUM(clicks)/SUM(impressions),0),4) <= ?");
            argsList.add(qo.getClickRateMax());
        }
        //cvr订单转化率
        if (qo.getSalesConversionRateMin() != null) {
            sb.append(" and ROUND(ifnull(SUM(sale_num)/SUM(clicks),0),4) >= ?");
            argsList.add(qo.getSalesConversionRateMin());
        }
        if (qo.getSalesConversionRateMax() != null) {
            sb.append(" and ROUND(ifnull(SUM(sale_num)/SUM(clicks),0),4) <= ?");
            argsList.add(qo.getSalesConversionRateMax());
        }
        //CPA
        if (qo.getCpaMin() != null) {
            sb.append(" and ROUND(ifnull(SUM(cost)/SUM(sale_num),0), 2) >= ?");
            argsList.add(qo.getCpaMin());
        }
        if (qo.getCpaMax() != null) {
            sb.append(" and ROUND(ifnull(SUM(cost)/SUM(sale_num),0), 2) <= ?");
            argsList.add(qo.getCpaMax());
        }
        //cpc  平均点击费用
        if (qo.getCpcMin() != null) {
            sb.append(" and ROUND(ifnull(SUM(cost)/SUM(clicks),0),2) >= ?");
            argsList.add(qo.getCpcMin());
        }
        if (qo.getCpcMax() != null) {
            sb.append(" and ROUND(ifnull(SUM(cost)/SUM(clicks),0),2) <= ?");
            argsList.add(qo.getCpcMax());
        }
        //acos
        if (qo.getAcosMin() != null) {
            sb.append(" and ROUND(ifnull(SUM(cost)/SUM(total_sales),0),4) >= ?");
            argsList.add(qo.getAcosMin());
        }
        if (qo.getAcosMax() != null) {
            sb.append(" and ROUND(ifnull(SUM(cost)/SUM(total_sales),0),4) <= ?");
            argsList.add(qo.getAcosMax());
        }
        // roas
        if (qo.getRoasMin() != null) {
            sb.append(" and ROUND(ifnull(SUM(total_sales)/SUM(cost),0),2) >= ?");
            argsList.add(qo.getRoasMin());
        }
        if (qo.getRoasMax() != null) {
            sb.append(" and ROUND(ifnull(SUM(total_sales)/SUM(cost),0),2) <= ?");
            argsList.add(qo.getRoasMax());
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(SUM(cost),0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(qo.getAcotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(SUM(cost),0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(qo.getAcotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        // asots 需要乘以店铺销售额
        if (qo.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(SUM(total_sales),0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(qo.getAsotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        // asots  需要乘以店铺销售额
        if (qo.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(SUM(total_sales),0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(qo.getAsotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        //其他产品广告订单量
        if (qo.getAdOtherOrderNumMin() != null) {
            sb.append(" and ifnull(SUM(sale_num) - SUM(ad_sale_num), 0) >= ?");
            argsList.add(qo.getAdOtherOrderNumMin());
        }
        if (qo.getAdOtherOrderNumMax() != null) {
            sb.append(" and ifnull(SUM(sale_num) - SUM(ad_sale_num), 0) <= ?");
            argsList.add(qo.getAdOtherOrderNumMax());
        }
        //其他产品广告销售额
        if (qo.getAdOtherSalesMin() != null) {
            sb.append(" and ifnull(SUM(total_sales) - SUM(ad_sales), 0) >= ?");
            argsList.add(qo.getAdOtherSalesMin());
        }
        if (qo.getAdOtherSalesMax() != null) {
            sb.append(" and ifnull(SUM(total_sales) - SUM(ad_sales), 0) <= ?");
            argsList.add(qo.getAdOtherSalesMax());
        }
        //其他产品广告销量
        if (qo.getAdOtherSaleNumMin() != null) {
            sb.append(" and ifnull(SUM(order_num) - SUM(ad_order_num), 0) >= ?");
            argsList.add(qo.getAdOtherSaleNumMin());
        }
        if (qo.getAdOtherSaleNumMax() != null) {
            sb.append(" and ifnull(SUM(order_num) - SUM(ad_order_num), 0) <= ?");
            argsList.add(qo.getAdOtherSaleNumMax());
        }
        return sb.toString();
    }

    private String getSbHavingSqlByPageList(GetWordRootAggregateDataQo qo, List<Object> argsList) {
        StringBuilder sb = new StringBuilder();
        if (!qo.getUseAdvanced()) {
            return "";
        }

        BigDecimal shopSales = qo.getShopSales() != null ? qo.getShopSales() : BigDecimal.valueOf(0);

        sb.append(" having 1=1 ");
        //频率
        if (qo.getFrequencyMin() != null) {
            sb.append(" and frequency >= ? ");
            argsList.add(qo.getFrequencyMin());
        }
        if (qo.getFrequencyMax() != null) {
            sb.append(" and frequency <= ? ");
            argsList.add(qo.getFrequencyMax());
        }
        //花费
        if (qo.getCostMin() != null) {
            sb.append(" and SUM(cost) >= ?");
            argsList.add(qo.getCostMin());
        }
        if (qo.getCostMax() != null) {
            sb.append(" and SUM(cost) <= ?");
            argsList.add(qo.getCostMax());
        }
        //展示量
        if (qo.getImpressionsMin() != null) {
            sb.append(" and SUM(impressions) >= ?");
            argsList.add(qo.getImpressionsMin());
        }
        if (qo.getImpressionsMax() != null) {
            sb.append(" and SUM(impressions) <= ?");
            argsList.add(qo.getImpressionsMax());
        }
        //点击量
        if (qo.getClicksMin() != null) {
            sb.append(" and SUM(clicks) >= ?");
            argsList.add(qo.getClicksMin());
        }
        if (qo.getClicksMax() != null) {
            sb.append(" and SUM(clicks) <= ?");
            argsList.add(qo.getClicksMax());
        }
        //广告订单量
        if (qo.getOrderNumMin() != null) {
            sb.append(" and SUM(conversions14d) >= ?");
            argsList.add(qo.getOrderNumMin());
        }
        if (qo.getOrderNumMax() != null) {
            sb.append(" and SUM(conversions14d) <= ?");
            argsList.add(qo.getOrderNumMax());
        }
        //广告销售额
        if (qo.getSalesMin() != null) {
            sb.append(" and SUM(sales14d) >= ?");
            argsList.add(qo.getSalesMin());
        }
        if (qo.getSalesMax() != null) {
            sb.append(" and SUM(sales14d) <= ?");
            argsList.add(qo.getSalesMax());
        }
        //点击率（clicks/impressions）
        if (qo.getClickRateMin() != null) {
            sb.append(" and ROUND(ifnull(SUM(clicks)/SUM(impressions),0),4) >= ?");
            argsList.add(qo.getClickRateMin());
        }
        if (qo.getClickRateMax() != null) {
            sb.append(" and ROUND(ifnull(SUM(clicks)/SUM(impressions),0),4) <= ?");
            argsList.add(qo.getClickRateMax());
        }
        //订单转化率
        if (qo.getSalesConversionRateMin() != null) {
            sb.append(" and ROUND(ifnull(SUM(conversions14d)/SUM(clicks),0),4) >= ?");
            argsList.add(qo.getSalesConversionRateMin());
        }
        if (qo.getSalesConversionRateMax() != null) {
            sb.append(" and ROUND(ifnull(SUM(conversions14d)/SUM(clicks),0),4) <= ?");
            argsList.add(qo.getSalesConversionRateMax());
        }
        //CPA:广告花费除以广告订单量
        if (qo.getCpaMin() != null) {
            sb.append(" and ROUND(ifnull(SUM(cost)/SUM(conversions14d),0), 4) >= ?");
            argsList.add(qo.getCpaMin());
        }
        if (qo.getCpaMax() != null) {
            sb.append(" and ROUND(ifnull(SUM(cost)/SUM(conversions14d),0), 4) <= ?");
            argsList.add(qo.getCpaMax());
        }
        //cpc  平均点击费用
        if (qo.getCpcMin() != null) {
            sb.append(" and ROUND(ifnull(SUM(cost)/SUM(clicks),0),2) >= ?");
            argsList.add(qo.getCpcMin());
        }
        if (qo.getCpcMax() != null) {
            sb.append(" and ROUND(ifnull(SUM(cost)/SUM(clicks),0),2) <= ?");
            argsList.add(qo.getCpcMax());
        }
        //acos
        if (qo.getAcosMin() != null) {
            sb.append(" and ROUND(ifnull(SUM(cost)/SUM(sales14d),0),4) >= ?");
            argsList.add(qo.getAcosMin());
        }
        if (qo.getAcosMax() != null) {
            sb.append(" and ROUND(ifnull(SUM(cost)/SUM(sales14d),0),4) <= ?");
            argsList.add(qo.getAcosMax());
        }
        // roas
        if (qo.getRoasMin() != null) {
            sb.append(" and ROUND(ifnull(SUM(sales14d)/SUM(cost),0),2) >= ?");
            argsList.add(qo.getRoasMin());
        }
        // roas
        if (qo.getRoasMax() != null) {
            sb.append(" and ROUND(ifnull(SUM(sales14d)/SUM(cost),0),2) <= ?");
            argsList.add(qo.getRoasMax());
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(SUM(cost),0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(qo.getAcotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(SUM(cost),0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(qo.getAcotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAcotsMax());
            }
        }
        // asots 需要乘以店铺销售额
        if (qo.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(SUM(sales14d),0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(qo.getAsotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAsotsMin());
            }
        }
        // asots  需要乘以店铺销售额
        if (qo.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(SUM(sales14d),0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(qo.getAsotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAsotsMax());
            }
        }
        return sb.toString();
    }

    private void getOrderByPageList(GetWordRootDataQo qo, StringBuilder sb, boolean isSp) {
        if (StringUtils.isNotBlank(qo.getOrderField()) && StringUtils.isNotBlank(qo.getOrderValue())) {
            String orderField;
            if (isSp) {
                orderField = this.getSpOrderField(qo.getOrderField());
            } else {
                orderField = this.getSbOrderField(qo.getOrderField());
            }
            if (StringUtils.isNotBlank(orderField)) {
                sb.append(" order by ").append(orderField);
                if (OrderTypeEnum.desc.getType().equals(qo.getOrderValue())) {
                    sb.append(" desc ");
                }
                sb.append(" , word_root desc ");
            }
        } else {
            sb.append(" order by frequency desc, word_root desc ");
        }
    }

    public String getSpOrderField(String field) {
        switch (field) {
            case "wordRoot":
                return " wordRoot ";
            case "adCost":
                return " SUM(cost) ";
            case "impressions":
                return " SUM(impressions) ";
            case "clicks":
                return " SUM(clicks) ";
            //广告订单量
            case "adOrderNum":
                return " SUM(sale_num) ";
            //本广告产品订单量
            case "adSaleNum":
                return " SUM(ad_sale_num) ";
            //广告销售额
            case "adSale":
                return " SUM(total_sales) ";
            //本广告产品销售额
            case "adSales":
                return " SUM(ad_sales) ";
            //广告销量
            case "orderNum":
                return " SUM(order_num) ";
            //本广告产品销量
            case "adSelfSaleNum":
                return " SUM(ad_order_num) ";
            //广告点击率ctr
            case "ctr":
                return " ifnull(SUM(clicks)/SUM(impressions),0) ";
            //广告转化率cvr
            case "cvr":
                return " ifnull(SUM(sale_num)/SUM(clicks),0) ";
            case "cpa":
                return " ifnull(SUM(cost)/SUM(sale_num),0) ";
            case "adCostPerClick":
                return " ifnull(SUM(cost)/SUM(clicks),0) ";
            case "acos":
                return " ifnull(SUM(cost)/SUM(total_sales),0) ";
            case "roas":
                return " ifnull(SUM(total_sales)/SUM(cost),0) ";
            case "acots":
                return " SUM(cost) ";
            case "asots":
                return " SUM(total_sales) ";
            //其他产品广告订单量
            case "adOtherOrderNum":
                return " ifnull(SUM(sale_num) - SUM(ad_sale_num),0) ";
            //其他产品广告销售额
            case "adOtherSales":
                return " ifnull(SUM(total_sales) - SUM(ad_sales),0) ";
            //其他产品广告销量
            case "adOtherSaleNum":
                return " ifnull(SUM(order_num) - SUM(ad_order_num),0) ";
            default:
                return " frequency ";
        }
    }

    public String getSbOrderField(String field) {
        switch (field) {
            case "wordRoot":
                return " wordRoot ";
            case "adCost":
                return " sum(cost) ";
            case "impressions":
                return " sum(impressions) ";
            case "clicks":
                return " sum(clicks) ";
            //广告订单量
            case "adOrderNum":
                return " sum(conversions14d) ";
            //广告销售额
            case "adSale":
                return " sum(sales14d) ";
            //ctr
            case "ctr":
                return " ifnull(sum(clicks)/sum(impressions),0) ";
            //cvr
            case "cvr":
                return " ifnull(sum(conversions14d)/sum(clicks),0) ";
            case "cpa":
                return " ifnull(sum(cost)/sum(conversions14d),0) ";
            case "adCostPerClick":
                return " ifnull(sum(cost)/sum(clicks),0) ";
            case "acos":
                return " ifnull(sum(cost)/sum(sales14d),0) ";
            case "roas":
                return " ifnull(sum(sales14d)/sum(cost),0) ";
            case "acots":
                return " sum(cost) ";
            case "asots":
                return " sum(sales14d) ";
            default:
                return " frequency ";
        }
    }

    @Override
    public List<WordRootTopVo> getAllQueryWordTopList(QueryWordTopQo dto) {
        if (StringUtils.equalsIgnoreCase(dto.getSearchField(), CpcQueryWordDto.SearchFieldEnum.TARGETING_EXPRESSION.getValue())
                && StringUtils.isNotBlank(dto.getSearchValue())) {
            return Lists.newArrayList();
        }

        List<Object> argsList = new ArrayList<>();

        List<String> unionAllSql = Lists.newArrayList();
        buildSpKeywordQueryIdSql(dto, argsList, unionAllSql);
        buildSpTargetQueryIdSql(dto, argsList, unionAllSql);
        buildSbQueryIdSql(dto, argsList, unionAllSql);
        if (CollectionUtils.isEmpty(unionAllSql)) {
            return Lists.newArrayList();
        }
        StringBuilder querySql = new StringBuilder();
        querySql.append("select query, any_value(marketplace_id) marketplace_id,")
                .append(" group_concat(distinct cast(query_type as char), ',') query_type_str")
                .append(" from (").append(StringUtils.join(unionAllSql, " union all ")).append(") t")
                .append(" group by query ").append(getHavingSql(dto, argsList));

        StringBuilder allQuerySql = new StringBuilder("select query, query_type_str ")
                .append(" from (").append(querySql).append(") c");
        if (dto.isQueryJoinSearchTermsRank()) {
            allQuerySql.append(" left join ods_t_week_search_terms_analysis a on ")
                    .append("lower(query) = a.search_term and c.marketplace_id = a.marketplace_id and a.start_date=? and a.marketplace_id=? ");
            argsList.add(dto.getLastWeekSearchTermsRankDate());
            argsList.add(dto.getMarketplaceId());

            if (dto.getSearchFrequencyRankMin() != null) {
                allQuerySql.append(" and ifnull(search_frequency_rank, 2147483647) >= ? ");
                argsList.add(dto.getSearchFrequencyRankMin());
            }
            if (dto.getSearchFrequencyRankMax() != null) {
                allQuerySql.append(" and ifnull(search_frequency_rank, 2147483647) <= ? ");
                argsList.add(dto.getSearchFrequencyRankMax());
            }
            allQuerySql.append(" where 1=1 ");
            allQuerySql.append(" and search_frequency_rank is not null ");
            if (dto.getSearchFrequencyRankMin() != null) {
                allQuerySql.append(" and search_frequency_rank >= ?");
                argsList.add(dto.getSearchFrequencyRankMin());
            }
            if (dto.getSearchFrequencyRankMax() != null) {
                allQuerySql.append(" and search_frequency_rank <= ?");
                argsList.add(dto.getSearchFrequencyRankMax());
            }
        }

        StringBuilder sql = new StringBuilder("select word_root, count(distinct wt.query) count")
                .append(" from ods_t_amazon_word_root_query wt join ")
                .append("(").append(allQuerySql).append(") q")
                .append(" on wt.query=q.query and find_in_set(wt.query_type, q.query_type_str) > 0 ")
                .append(" where puid=? and marketplace_id=? and count_day>=? and count_day<=? ");
        argsList.add(dto.getPuid());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        sql.append(SqlStringUtil.dealInList("shop_id", dto.getShopIdList(), argsList))
                .append(" group by word_root order by count desc limit 10 ");

        log.info("sp sb 词频查询 sql {}", SqlStringUtil.exactSql(sql.toString(), argsList));
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordRootTopVo.class));
    }

    private void buildSpKeywordQueryIdSql(QueryWordTopQo dto, List<Object> argsList, List<String> unionAllSql) {
        // 选择了类型查询，但是没有选sp的
        if (CollectionUtils.isNotEmpty(dto.getSpMatchTypes()) || CollectionUtils.isNotEmpty(dto.getSbMatchTypes())) {
            if (CollectionUtils.isEmpty(dto.getSpMatchTypes())) {
                return;
            }
        }
        List<String> matchTypeList = dto.getSpMatchTypes();
        List<String> matchTypes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isNotBlank(MatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            if (CollectionUtils.isEmpty(matchTypes)) {
                return;
            }
        }

        StringBuilder sql = new StringBuilder(" select")
                .append(" query, 1 query_type, ANY(marketplace_id) marketplace_id,")
                .append(" SUM(impressions) impressions, SUM(clicks) clicks, SUM(cost) cost, SUM(sale_num) sale_num,")
                .append(" SUM(total_sales) total_sales, SUM(ad_sales) `ad_sales`, SUM(total_sales) - SUM(ad_sales) ad_other_sales")
                .append(" from ods_t_cpc_query_keyword_report where puid=? and marketplace_id=? and count_day>=? and count_day<=?");
        argsList.add(dto.getPuid());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        sql.append(SqlStringUtil.dealInList("shop_id", dto.getShopIdList(), argsList));
        sql.append(" and query not REGEXP '" + Constants.ASIN_REGEX + "' ");

        if (CollectionUtils.isNotEmpty(matchTypes)) {
            sql.append(SqlStringUtil.dealInList("lower(match_type)", matchTypes, argsList));
        }
        sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", dto.getGroupIdList(), argsList));

        // 仅展示未投放 or 仅展示已投放
        if (StringUtils.equalsAny(dto.getQueryWordTargetType(), Constants.QUERY_TARGET, Constants.QUERY_NOT_TARGET)) {
            sql.append(" and concat_ws(',', ad_group_id, query) ");
            if (StringUtils.equals(dto.getQueryWordTargetType(), Constants.QUERY_NOT_TARGET)) {
                sql.append("not");
            }
            sql.append(" in ( ")
                    .append(SearchQueryTagSqlHelper.getKeywordSearchQueryTagInSql(dto.getPuid(), dto, argsList, CpcQueryWordDto.QueryWordTagTypeEnum.getKeywordSearchField()))
                    .append(" ) ");
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryKeywordReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                //将搜索词转小写和数据库中函数小写匹配
                field = "lower(" + field + ")";
                dto.setSearchValue(dto.getSearchValue().toLowerCase());
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    sql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    if (dto.getListSearchValue().size() > 1) {
                        sql.append(SqlStringUtil.dealInList(field, dto.getListSearchValue(), argsList));
                    } else {
                        sql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getListSearchValue().get(0));
                    }
                }
            }
        }
        sql.append(" group by query ");
        unionAllSql.add(sql.toString());
    }

    private void buildSpTargetQueryIdSql(QueryWordTopQo dto, List<Object> argsList, List<String> unionAllSql) {
        // 选择了类型查询，但是没有选sp的
        if (CollectionUtils.isNotEmpty(dto.getSpMatchTypes()) || CollectionUtils.isNotEmpty(dto.getSbMatchTypes())) {
            if (CollectionUtils.isEmpty(dto.getSpMatchTypes())) {
                return;
            }
        }
        List<String> matchTypeList = dto.getSpMatchTypes();
        List<String> matchTypes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                matchType = matchType.replace("=", "");
                if (StringUtils.isNotBlank(TargetMatchTypeEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            if (CollectionUtils.isEmpty(matchTypes)) {
                return;
            }
        }

        StringBuilder sql = new StringBuilder(" select ")
                .append(" query, 2 query_type, ANY(marketplace_id) marketplace_id,")
                .append(" SUM(impressions) impressions, SUM(clicks) clicks, SUM(cost) cost, SUM(sale_num) sale_num,")
                .append(" SUM(total_sales) total_sales, SUM(ad_sales) `ad_sales`, SUM(total_sales) - SUM(ad_sales) ad_other_sales")
                .append(" from ods_t_cpc_query_targeting_report where puid=? and marketplace_id=? and count_day>=? and count_day<=?");
        argsList.add(dto.getPuid());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        sql.append(SqlStringUtil.dealInList("shop_id", dto.getShopIdList(), argsList));
        sql.append(" and query not REGEXP '" + Constants.ASIN_REGEX + "' ");
        if (CollectionUtils.isNotEmpty(matchTypes)) {
            sql.append(SqlStringUtil.dealInList("lower(match_type)", matchTypes, argsList));
        }
        sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", dto.getGroupIdList(), argsList));

        // 仅展示未投放 or 仅展示已投放
        if (StringUtils.equalsAny(dto.getQueryWordTargetType(), Constants.QUERY_TARGET, Constants.QUERY_NOT_TARGET)) {
            sql.append(" and concat_ws(',', ad_group_id, query) ");
            if (StringUtils.equals(dto.getQueryWordTargetType(), Constants.QUERY_NOT_TARGET)) {
                sql.append("not");
            }
            sql.append(" in ( ")
                    .append(SearchQueryTagSqlHelper.getKeywordSearchQueryTagInSql(dto.getPuid(), dto, argsList, CpcQueryWordDto.QueryWordTagTypeEnum.getKeywordSearchField()))
                    .append(" ) ");
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryTargetingReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                //将搜索词转小写和数据库中函数小写匹配
                field = "lower(" + field + ")";
                dto.setSearchValue(dto.getSearchValue().toLowerCase());
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    sql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    if (dto.getListSearchValue().size() > 1) {
                        sql.append(SqlStringUtil.dealInList(field, dto.getListSearchValue(), argsList));
                    } else {
                        sql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getListSearchValue().get(0));
                    }
                }
            } else if ("keywordText".equalsIgnoreCase(dto.getSearchField())) {
                if (!dto.getSearchValue().equalsIgnoreCase("自动投放组")) {
                    sql.append(" and targeting_text = ? ");  // 需要查不出这个表的数据
                    argsList.add(dto.getSearchValue());
                }
            }
        }
        sql.append(" group by query ");
        unionAllSql.add(sql.toString());
    }

    private String getHavingSql(QueryWordTopQo dto, List<Object> argsList) {
        StringBuilder havingSql = new StringBuilder(" having 1=1 ");

        //广告订单量
        if (dto.getOrderNumMin() != null) {
            havingSql.append(" and SUM(sale_num) >= ?");
            argsList.add(dto.getOrderNumMin());
        }
        if (dto.getOrderNumMax() != null) {
            havingSql.append(" and SUM(sale_num) <= ?");
            argsList.add(dto.getOrderNumMax());
        }
        //订单转化率
        if (dto.getSalesConversionRateMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(sale_num)/SUM(clicks),0),4) >= ?");
            argsList.add(dto.getSalesConversionRateMin());
        }
        if (dto.getSalesConversionRateMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(sale_num)/SUM(clicks),0),4) <= ?");
            argsList.add(dto.getSalesConversionRateMax());
        }
        //花费
        if (dto.getCostMin() != null) {
            havingSql.append(" and SUM(cost) >= ?");
            argsList.add(dto.getCostMin());
        }
        if (dto.getCostMax() != null) {
            havingSql.append(" and SUM(cost) <= ?");
            argsList.add(dto.getCostMax());
        }
        //曝光量
        if (dto.getImpressionsMin() != null) {
            havingSql.append(" and SUM(impressions) >= ?");
            argsList.add(dto.getImpressionsMin());
        }
        if (dto.getImpressionsMax() != null) {
            havingSql.append(" and SUM(impressions) <= ?");
            argsList.add(dto.getImpressionsMax());
        }
        //点击量
        if (dto.getClicksMin() != null) {
            havingSql.append(" and SUM(clicks) >= ?");
            argsList.add(dto.getClicksMin());
        }
        if (dto.getClicksMax() != null) {
            havingSql.append(" and SUM(clicks) <= ?");
            argsList.add(dto.getClicksMax());
        }
        //cpc  平均点击费用
        if (dto.getCpcMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(clicks),0),2) >= ?");
            argsList.add(dto.getCpcMin());
        }
        if (dto.getCpcMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(clicks),0),2) <= ?");
            argsList.add(dto.getCpcMax());
        }
        //CPA:广告花费除以广告订单量
        if (dto.getCpaMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(sale_num),0), 4) >= ?");
            argsList.add(dto.getCpaMin());
        }
        if (dto.getCpaMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(sale_num),0), 4) <= ?");
            argsList.add(dto.getCpaMax());
        }
        //acos
        if (dto.getAcosMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(total_sales),0),4) >= ?");
            argsList.add(dto.getAcosMin());
        }
        if (dto.getAcosMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(cost)/SUM(total_sales),0),4) <= ?");
            argsList.add(dto.getAcosMax());
        }
        //roas
        if (dto.getRoasMin() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(total_sales)/SUM(cost),0),2) >= ?");
            argsList.add(dto.getRoasMin());
        }
        if (dto.getRoasMax() != null) {
            havingSql.append(" and ROUND(ifnull(SUM(total_sales)/SUM(cost),0),2) <= ?");
            argsList.add(dto.getRoasMax());
        }
        //广告销售额
        if (dto.getSalesMin() != null) {
            havingSql.append(" and SUM(total_sales) >= ?");
            argsList.add(dto.getSalesMin());
        }
        if (dto.getSalesMax() != null) {
            havingSql.append(" and SUM(total_sales) <= ?");
            argsList.add(dto.getSalesMax());
        }
        //本广告产品销售额
        if (dto.getAdSalesMin() != null) {
            havingSql.append(" and ifnull(SUM(ad_sales), 0) >= ?");
            argsList.add(dto.getAdSalesMin());
        }
        if (dto.getAdSalesMax() != null) {
            havingSql.append(" and ifnull(SUM(ad_sales), 0) <= ?");
            argsList.add(dto.getAdSalesMax());
        }
        //其他产品广告销售额
        if (dto.getAdOtherSalesMin() != null) {
            havingSql.append(" and ifnull(SUM(total_sales) - SUM(ad_sales), 0) >= ?");
            argsList.add(dto.getAdOtherSalesMin());
        }
        if (dto.getAdOtherSalesMax() != null) {
            havingSql.append(" and ifnull(SUM(total_sales) - SUM(ad_sales), 0) <= ?");
            argsList.add(dto.getAdOtherSalesMax());
        }
        return havingSql.toString();
    }

    private void buildSbQueryIdSql(QueryWordTopQo dto, List<Object> argsList, List<String> unionAllSql) {
        // 选择了类型查询，但是没有选sb的
        if (CollectionUtils.isNotEmpty(dto.getSpMatchTypes()) || CollectionUtils.isNotEmpty(dto.getSbMatchTypes())) {
            if (CollectionUtils.isEmpty(dto.getSbMatchTypes())) {
                return;
            }
        }
        List<String> matchTypeList = dto.getSbMatchTypes();
        List<String> matchTypes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isNotBlank(MatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            if (CollectionUtils.isEmpty(matchTypes)) {
                return;
            }
        }

        int puid = dto.getPuid();
        StringBuilder sql = new StringBuilder(" select ")
                .append(" query, 3 query_type, ANY(marketplace_id) marketplace_id,")
                .append(" SUM(impressions) impressions, SUM(clicks) clicks, SUM(cost) cost, SUM(conversions14d) sale_num,")
                .append(" SUM(sales14d) total_sales, 0 `ad_sales`, 0 ad_other_sales")
                .append(" from ods_t_cpc_sb_query_keyword_report")
                .append(" where puid=? and marketplace_id=? and count_day>=? and count_day<=? ");
        argsList.add(puid);
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        sql.append(SqlStringUtil.dealInList("shop_id", dto.getShopIdList(), argsList));

        if (CollectionUtils.isNotEmpty(matchTypes)) {
            sql.append(SqlStringUtil.dealInList("lower(match_type)", matchTypes, argsList));
        }
        sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", dto.getGroupIdList(), argsList));
        // 仅展示未投放 or 仅展示已投放
        if (StringUtils.equalsAny(dto.getQueryWordTargetType(), Constants.QUERY_TARGET, Constants.QUERY_NOT_TARGET)) {
            sql.append(" and concat_ws(',', ad_group_id, query) ");
            if (StringUtils.equals(dto.getQueryWordTargetType(), Constants.QUERY_NOT_TARGET)) {
                sql.append("not");
            }
            sql.append(" in ( ")
                    .append(SearchQueryTagSqlHelper.getSbKeywordSearchQueryTagInSql(dto.getPuid(), dto, argsList, CpcQueryWordDto.QueryWordTagTypeEnum.getKeywordSearchField()))
                    .append(" ) ");
        }
        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcSbQueryKeywordReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                //将搜索词转小写和数据库中函数小写匹配
                field = "lower(" + field + ")";
                dto.setSearchValue(dto.getSearchValue().toLowerCase());
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    sql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    if (dto.getListSearchValue().size() > 1) {
                        sql.append(SqlStringUtil.dealInList(field, dto.getListSearchValue(), argsList));
                    } else {
                        sql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getListSearchValue().get(0));
                    }
                }
            }
        }
        sql.append(" group by query ");
        unionAllSql.add(sql.toString());
    }

}
