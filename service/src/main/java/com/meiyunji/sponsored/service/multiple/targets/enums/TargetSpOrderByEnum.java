package com.meiyunji.sponsored.service.multiple.targets.enums;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Getter;

import java.util.HashSet;
import java.util.Set;

/**
 * 投放sp列表页-排序高级刷新字段枚举
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Getter
public enum TargetSpOrderByEnum {
    CREATE_TIME("createTime", " t.create_time ", new HashSet<>()),
    BID("bid", " IFNULL(bid, g.default_bid) ", new HashSet<>()),
    SHOP_NAME("shopName", " CONVERT(s.`name` USING gbk) COLLATE gbk_chinese_ci ", new HashSet<>()),
    AD_COST("adCost", " ifnull(costDoris,0) ", CollectionUtil.newHashSet("costDoris")),
    AD_COST_PERCENTAGE("adCostPercentage", " ifnull(costDoris,0) ", CollectionUtil.newHashSet("costDoris")),
    TOTAL_SALES("adSale", " ifnull(totalSalesDoris,0) ", CollectionUtil.newHashSet("totalSalesDoris")),
    TOTAL_SALES_PERCENTAGE("adSalePercentage", " ifnull(totalSalesDoris,0) ", CollectionUtil.newHashSet("totalSalesDoris")),
    AD_SALES("adSales", " ifnull(adSalesDoris,0) ", CollectionUtil.newHashSet("adSalesDoris")),
    IMPRESSIONS("impressions", " ifnull(impressionsDoris,0) ", CollectionUtil.newHashSet("impressionsDoris")),
    CLICKS("clicks", " ifnull(clicksDoris,0) ", CollectionUtil.newHashSet("clicksDoris")),
    ORDER_NUM("adOrderNum", " ifnull(orderNumDoris,0) ", CollectionUtil.newHashSet("orderNumDoris")),
    ORDER_NUM_PERCENTAGE("adOrderNumPercentage", " ifnull(orderNumDoris,0) ", CollectionUtil.newHashSet("orderNumDoris")),
    AD_ORDER_NUM("adSaleNum", " ifnull(adOrderNumDoris,0) ", CollectionUtil.newHashSet("adOrderNumDoris")),
    SALE_NUM("orderNum", " ifnull(saleNumDoris,0) ", CollectionUtil.newHashSet("saleNumDoris")),
    SALE_NUM_PERCENTAGE("orderNumPercentage", " ifnull(saleNumDoris,0) ", CollectionUtil.newHashSet("saleNumDoris")),
    AD_SALE_NUM("adSelfSaleNum", " ifnull(adSaleNumDoris,0) ", CollectionUtil.newHashSet("adSaleNumDoris")),
    TOP_IMPRESSION_SHARE("topImpressionShare", " maxTopIsDoris ", CollectionUtil.newHashSet("maxTopIsDoris")),
    CPA("cpa", " ifnull(costDoris/orderNumDoris ,0) ", CollectionUtil.newHashSet("costDoris", "orderNumDoris")),
    AD_COST_PER_CLICK("adCostPerClick", " ifnull(costDoris/clicksDoris ,0) ", CollectionUtil.newHashSet("costDoris", "clicksDoris")),
    CTR("ctr", " ifnull(clicksDoris/impressionsDoris ,0) ", CollectionUtil.newHashSet("clicksDoris", "impressionsDoris")),
    CVR("cvr", " ifnull(orderNumDoris/clicksDoris ,0) ", CollectionUtil.newHashSet("orderNumDoris", "clicksDoris")),
    ACOS("acos", " ifnull(costDoris/totalSalesDoris ,0) ", CollectionUtil.newHashSet("costDoris", "totalSalesDoris")),
    ROAS("roas", " ifnull(totalSalesDoris/costDoris ,0) ", CollectionUtil.newHashSet("totalSalesDoris", "costDoris")),
    ACOTS("acots", " ifnull(costDoris/shopSalesDoris ,0) ", CollectionUtil.newHashSet("costDoris")),
    ASOTS("asots", " ifnull(totalSalesDoris/shopSalesDoris ,0) ", CollectionUtil.newHashSet("totalSalesDoris")),
    ADVERTISING_UNIT_PRICE("advertisingUnitPrice", " ifnull(totalSalesDoris/orderNumDoris ,0) ", CollectionUtil.newHashSet("totalSalesDoris", "orderNumDoris")),
    AD_OTHER_SALES("adOtherSales", " ifnull(totalSalesDoris-adSalesDoris ,0) ", CollectionUtil.newHashSet("totalSalesDoris", "adSalesDoris")),
    AD_OTHER_ORDER_NUM("adOtherOrderNum", " ifnull(orderNumDoris-adOrderNumDoris ,0) ", CollectionUtil.newHashSet("orderNumDoris", "adOrderNumDoris")),
    AD_OTHER_SALE_NUM("adOtherSaleNum", " ifnull(saleNumDoris-adSaleNumDoris,0) ", CollectionUtil.newHashSet("saleNumDoris","adSaleNumDoris")),
    SEARCH_FREQUENCY_RANK("searchFrequencyRank", " ifnull(search_frequency_rank, 2147483647) ", new HashSet<>()),
    WEEK_RATIO("weekRatio", " ifnull(round(week_ratio*100,2), -2147483648) ", new HashSet<>()),
    ;

    // 排序字段
    private final String code;
    // 字段
    private final String orderBy;
    // 字段
    private final Set<String> columnList;

    TargetSpOrderByEnum(String code, String orderBy, Set<String> columnList) {
        this.code = code;
        this.orderBy = orderBy;
        this.columnList = columnList;
    }

    /**
     * 根据code获取枚举
     */
    public static TargetSpOrderByEnum getEnumByCode(String code) {
        for (TargetSpOrderByEnum orderByEnum : TargetSpOrderByEnum.values()) {
            if (orderByEnum.getCode().equals(code)) {
                return orderByEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取统计字段集合
     */
    public static Set<String> getSetByCode(String code) {
        for (TargetSpOrderByEnum orderByEnum : TargetSpOrderByEnum.values()) {
            if (orderByEnum.getCode().equals(code)) {
                return orderByEnum.getColumnList();
            }
        }
        return new HashSet<>();
    }

    /**
     * 根据code获取排序
     */
    public static String getOrderByByCode(String code, String targetType) {
        // 前端缓存兼容
        if(!"keyword".equals(targetType) && ("searchFrequencyRank".equals(code) || "weekRatio".equals(code))) {
            return TargetSbOrderByEnum.CREATE_TIME.getOrderBy();
        }
        for (TargetSpOrderByEnum orderByEnum : TargetSpOrderByEnum.values()) {
            if (orderByEnum.getCode().equals(code)) {
                return orderByEnum.getOrderBy();
            }
        }
        return TargetSpOrderByEnum.CREATE_TIME.getOrderBy();
    }
}
