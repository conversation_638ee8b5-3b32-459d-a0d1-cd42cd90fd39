package com.meiyunji.sponsored.service.syncTask.report.strategy.sponsoredProducts;

import com.alibaba.fastjson.JSONReader;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportV3Type;
import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;
import com.meiyunji.sponsored.common.springjdbc.PartitionSqlUtil;
import com.meiyunji.sponsored.service.account.dao.ISlaveVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignPlacementReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignPlacementReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignReport;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.ShopTypeEnum;
import com.meiyunji.sponsored.service.syncTask.entity.SponsoredProductCampaigns;
import com.meiyunji.sponsored.service.syncTask.report.strategy.AbstractReportProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.zip.GZIPInputStream;

@Component
@Slf4j
@ConditionalOnProperty(name = "aadas.scheduler.executors.reports-consumer.enabled", havingValue = "true")
public class SpCampaignsPlacementReportV3Strategy extends AbstractReportProcessStrategy {
    private final IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao;
    private final IAmazonAdCampaignPlacementReportDao amazonAdCampaignPlacementReportDao;
    private final PartitionSqlUtil partitionSqlUtil;
    @Resource
    private ISlaveVcShopAuthDao vcShopAuthDao;

    public SpCampaignsPlacementReportV3Strategy(CosBucketClient dataBucketClient, IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao,IAmazonAdCampaignPlacementReportDao amazonAdCampaignPlacementReportDao, PartitionSqlUtil partitionSqlUtil) {
        super(dataBucketClient);
        this.amazonAdCampaignAllReportDao = amazonAdCampaignAllReportDao;
        this.amazonAdCampaignPlacementReportDao = amazonAdCampaignPlacementReportDao;
        this.partitionSqlUtil = partitionSqlUtil;
    }


    @Override
    public Boolean checkValid(ReportReadyNotification notification) {
        return notification.getVersion() == 3 &&
                notification.getV3Type() == AmazonReportV3Type.sp_campaigns_placement;
    }

    @Override
    public void processReport(ReportReadyNotification notification) throws Exception {
        try (InputStreamReader inputStreamReader = new InputStreamReader(new GZIPInputStream(new ByteArrayInputStream(dataBucketClient.getObjectToBytes(notification.getPath())))); JSONReader jsonReader = new JSONReader(inputStreamReader)) {
            jsonReader.startArray();
            List<SponsoredProductCampaigns> reports = Lists.newArrayListWithExpectedSize(batchSize);
            VcShopAuth byIdAndPuid = vcShopAuthDao.getByIdAndPuid(notification.getMarketplaceIdentifier(), notification.getSellerIdentifier());
            String shopType = ShopTypeEnum.SC.getCode();
            if (byIdAndPuid != null && byIdAndPuid.getId() != null) {
                shopType = ShopTypeEnum.VC.getCode();
            }
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                SponsoredProductCampaigns report = new SponsoredProductCampaigns();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                reports.add(report);
                if (report.getImpressions() != 0) {
                    reports.add(report);
                }
                if (reports.size() >= batchSize) {
                    dealReports(notification, reports, shopType);
                    reports = Lists.newArrayListWithExpectedSize(batchSize);
                }
            }
            jsonReader.endArray();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReports(notification, reports, shopType);
            }
        } catch (Exception e) {
            log.info("报告处理发生错误{}@{} reportType={} countDate={}", notification.getSellerIdentifier()
                    , notification.getMarketplaceIdentifier(), notification.getType(), notification.getDate(), e);
            throw e;
        }
    }

    private void dealReports(ReportReadyNotification notification, List<SponsoredProductCampaigns> reports, String shopType) {
        // 注释入库活动报告表，后续删除该代码
//        //入库新表
//        List<AmazonAdCampaignAllReport> poAllList = getPoByAllReportCampaign(notification, reports);
//        //分批入库
//        List<List<AmazonAdCampaignAllReport>> partitionAll = Lists.partition(poAllList, batchSize);
//        for (List<AmazonAdCampaignAllReport> campaignReports : partitionAll) {
//            partitionSqlUtil.save(notification.getSellerIdentifier(), campaignReports, 0, amazonAdCampaignAllReportDao::insertOrUpdateList);
//        }
        try {
            //入库广告位报告表
            List<AmazonAdCampaignPlacementReport> poPlacementList = getPoByPlacementReportCampaign(notification, reports);
            //分批入库
            List<List<AmazonAdCampaignPlacementReport>> partitionPlacement = Lists.partition(poPlacementList, batchSize);
            for (List<AmazonAdCampaignPlacementReport> campaignReports : partitionPlacement) {
                partitionSqlUtil.save(notification.getSellerIdentifier(), campaignReports, 0, amazonAdCampaignPlacementReportDao::insertOrUpdateList);
            }
        } catch (Exception e) {
            log.error("广告位报告表落库异常！",e);
        }
    }


    private List<AmazonAdCampaignAllReport> getPoByAllReportCampaign(ReportReadyNotification notification, List<SponsoredProductCampaigns> reports, String shopType) {
        List<AmazonAdCampaignAllReport> list = Lists.newArrayListWithExpectedSize(reports.size());
        AmazonAdCampaignAllReport amazonAdCampaignReport;
        for (SponsoredProductCampaigns report : reports) {
            amazonAdCampaignReport = new AmazonAdCampaignAllReport();
            amazonAdCampaignReport.setPuid(notification.getSellerIdentifier());
            amazonAdCampaignReport.setShopId(notification.getMarketplaceIdentifier());
            amazonAdCampaignReport.setMarketplaceId(notification.getMarketplace().getId());
            amazonAdCampaignReport.setCountDate(report.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            amazonAdCampaignReport.setCampaignId(String.valueOf(report.getCampaignId()));
            amazonAdCampaignReport.setType(CampaignTypeEnum.sp.getCampaignType());
            amazonAdCampaignReport.setCampaignType(report.getPlacementClassification()); //All表示汇总信息
            amazonAdCampaignReport.setPlacement(report.getPlacementClassification());
            amazonAdCampaignReport.setIsSummary(0);
            amazonAdCampaignReport.setCampaignName(report.getCampaignName());
            amazonAdCampaignReport.setCampaignStatus(report.getCampaignStatus().toLowerCase());
            amazonAdCampaignReport.setCampaignBudget(report.getCampaignBudgetAmount());

            //由于vc店铺都是取14天归因数，为了不改变查询代码，所以这里将入库字段进行对调
            if (ShopTypeEnum.VC.getCode().equalsIgnoreCase(shopType)) {
                amazonAdCampaignReport.setConversions7d(report.getPurchases14d());
                amazonAdCampaignReport.setConversions7dSameSKU(report.getPurchasesSameSku14d());
                amazonAdCampaignReport.setConversions14d(report.getPurchases7d());
                amazonAdCampaignReport.setConversions14dSameSKU(report.getPurchasesSameSku7d());
                amazonAdCampaignReport.setSales7d(report.getSales14d() == null ? null : report.getSales14d());
                amazonAdCampaignReport.setSales7dSameSKU(report.getAttributedSalesSameSku14d() == null ? null : report.getAttributedSalesSameSku14d());
                amazonAdCampaignReport.setSales14d(report.getSales7d() == null ? null : report.getSales7d());
                amazonAdCampaignReport.setSales14dSameSKU(report.getAttributedSalesSameSku7d() == null ? null : report.getAttributedSalesSameSku7d());
                amazonAdCampaignReport.setUnitsOrdered7d(report.getUnitsSoldClicks14d());
                amazonAdCampaignReport.setUnitsOrdered7dSameSKU(report.getUnitsSoldSameSku14d());
                amazonAdCampaignReport.setUnitsOrdered14d(report.getUnitsSoldClicks7d());
                amazonAdCampaignReport.setUnitsOrdered14dSameSKU(report.getUnitsSoldSameSku7d());
            } else {
                amazonAdCampaignReport.setConversions14d(report.getPurchases14d());
                amazonAdCampaignReport.setConversions14dSameSKU(report.getPurchasesSameSku14d());
                amazonAdCampaignReport.setConversions7d(report.getPurchases7d());
                amazonAdCampaignReport.setConversions7dSameSKU(report.getPurchasesSameSku7d());
                amazonAdCampaignReport.setSales14d(report.getSales14d() == null ? null : report.getSales14d());
                amazonAdCampaignReport.setSales14dSameSKU(report.getAttributedSalesSameSku14d() == null ? null : report.getAttributedSalesSameSku14d());
                amazonAdCampaignReport.setSales7d(report.getSales7d() == null ? null : report.getSales7d());
                amazonAdCampaignReport.setSales7dSameSKU(report.getAttributedSalesSameSku7d() == null ? null : report.getAttributedSalesSameSku7d());
                amazonAdCampaignReport.setUnitsOrdered14d(report.getUnitsSoldClicks14d());
                amazonAdCampaignReport.setUnitsOrdered14dSameSKU(report.getUnitsSoldSameSku14d());
                amazonAdCampaignReport.setUnitsOrdered7d(report.getUnitsSoldClicks7d());
                amazonAdCampaignReport.setUnitsOrdered7dSameSKU(report.getUnitsSoldSameSku7d());
            }


            amazonAdCampaignReport.setConversions1d(report.getPurchases1d());
            amazonAdCampaignReport.setConversions1dSameSKU(report.getPurchasesSameSku1d());
            amazonAdCampaignReport.setConversions30d(report.getPurchases30d());
            amazonAdCampaignReport.setConversions30dSameSKU(report.getPurchasesSameSku30d());
            amazonAdCampaignReport.setSales1d(report.getSales1d() == null ? null : report.getSales1d());
            amazonAdCampaignReport.setSales1dSameSKU(report.getAttributedSalesSameSku1d() == null ? null : report.getAttributedSalesSameSku1d());
            amazonAdCampaignReport.setSales30d(report.getSales30d() == null ? null : report.getSales30d());
            amazonAdCampaignReport.setSales30dSameSKU(report.getAttributedSalesSameSku30d() == null ? null : report.getAttributedSalesSameSku30d());
            amazonAdCampaignReport.setUnitsOrdered1d(report.getUnitsSoldClicks1d());
            amazonAdCampaignReport.setUnitsOrdered1dSameSKU(report.getUnitsSoldSameSku1d());
            amazonAdCampaignReport.setUnitsOrdered30d(report.getUnitsSoldClicks30d());
            amazonAdCampaignReport.setUnitsOrdered30dSameSKU(report.getUnitsSoldSameSku30d());
            amazonAdCampaignReport.setUnitsOrdered7d(report.getUnitsSoldClicks7d());
            amazonAdCampaignReport.setUnitsOrdered7dSameSKU(report.getUnitsSoldSameSku7d());
            amazonAdCampaignReport.setClicks(report.getClicks());
            amazonAdCampaignReport.setCost(report.getCost());
            amazonAdCampaignReport.setImpressions(report.getImpressions());

            list.add(amazonAdCampaignReport);
        }
        return list;
    }

    private List<AmazonAdCampaignPlacementReport> getPoByPlacementReportCampaign(ReportReadyNotification notification, List<SponsoredProductCampaigns> reports) {
        List<AmazonAdCampaignPlacementReport> list = Lists.newArrayListWithExpectedSize(reports.size());
        AmazonAdCampaignPlacementReport amazonAdCampaignPlacementReport;
        for (SponsoredProductCampaigns report : reports) {
            amazonAdCampaignPlacementReport = new AmazonAdCampaignPlacementReport();
            amazonAdCampaignPlacementReport.setPuid(notification.getSellerIdentifier());
            amazonAdCampaignPlacementReport.setShopId(notification.getMarketplaceIdentifier());
            amazonAdCampaignPlacementReport.setMarketplaceId(notification.getMarketplace().getId());
            amazonAdCampaignPlacementReport.setCountDate(report.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            amazonAdCampaignPlacementReport.setCampaignId(String.valueOf(report.getCampaignId()));
            amazonAdCampaignPlacementReport.setType(CampaignTypeEnum.sp.getCampaignType());
            amazonAdCampaignPlacementReport.setCampaignType(report.getPlacementClassification()); //All表示汇总信息
            amazonAdCampaignPlacementReport.setPlacement(report.getPlacementClassification());
            amazonAdCampaignPlacementReport.setIsSummary(0);
            amazonAdCampaignPlacementReport.setCampaignName(report.getCampaignName());
            amazonAdCampaignPlacementReport.setCampaignStatus(report.getCampaignStatus().toLowerCase());
            amazonAdCampaignPlacementReport.setCampaignBudget(report.getCampaignBudgetAmount());
            amazonAdCampaignPlacementReport.setConversions14d(report.getPurchases14d());
            amazonAdCampaignPlacementReport.setConversions14dSameSKU(report.getPurchasesSameSku14d());
            amazonAdCampaignPlacementReport.setConversions1d(report.getPurchases1d());
            amazonAdCampaignPlacementReport.setConversions1dSameSKU(report.getPurchasesSameSku1d());
            amazonAdCampaignPlacementReport.setConversions30d(report.getPurchases30d());
            amazonAdCampaignPlacementReport.setConversions30dSameSKU(report.getPurchasesSameSku30d());
            amazonAdCampaignPlacementReport.setConversions7d(report.getPurchases7d());
            amazonAdCampaignPlacementReport.setConversions7dSameSKU(report.getPurchasesSameSku7d());
            amazonAdCampaignPlacementReport.setSales14d(report.getSales14d() == null ? null : report.getSales14d());
            amazonAdCampaignPlacementReport.setSales14dSameSKU(report.getAttributedSalesSameSku14d() == null ? null : report.getAttributedSalesSameSku14d());
            amazonAdCampaignPlacementReport.setSales1d(report.getSales1d() == null ? null : report.getSales1d());
            amazonAdCampaignPlacementReport.setSales1dSameSKU(report.getAttributedSalesSameSku1d() == null ? null : report.getAttributedSalesSameSku1d());
            amazonAdCampaignPlacementReport.setSales30d(report.getSales30d() == null ? null : report.getSales30d());
            amazonAdCampaignPlacementReport.setSales30dSameSKU(report.getAttributedSalesSameSku30d() == null ? null : report.getAttributedSalesSameSku30d());
            amazonAdCampaignPlacementReport.setSales7d(report.getSales7d() == null ? null : report.getSales7d());
            amazonAdCampaignPlacementReport.setSales7dSameSKU(report.getAttributedSalesSameSku7d() == null ? null : report.getAttributedSalesSameSku7d());
            amazonAdCampaignPlacementReport.setUnitsOrdered14d(report.getUnitsSoldClicks14d());
            amazonAdCampaignPlacementReport.setUnitsOrdered14dSameSKU(report.getUnitsSoldSameSku14d());
            amazonAdCampaignPlacementReport.setUnitsOrdered1d(report.getUnitsSoldClicks1d());
            amazonAdCampaignPlacementReport.setUnitsOrdered1dSameSKU(report.getUnitsSoldSameSku1d());
            amazonAdCampaignPlacementReport.setUnitsOrdered30d(report.getUnitsSoldClicks30d());
            amazonAdCampaignPlacementReport.setUnitsOrdered30dSameSKU(report.getUnitsSoldSameSku30d());
            amazonAdCampaignPlacementReport.setUnitsOrdered7d(report.getUnitsSoldClicks7d());
            amazonAdCampaignPlacementReport.setUnitsOrdered7dSameSKU(report.getUnitsSoldSameSku7d());
            amazonAdCampaignPlacementReport.setClicks(report.getClicks());
            amazonAdCampaignPlacementReport.setCost(report.getCost());
            amazonAdCampaignPlacementReport.setImpressions(report.getImpressions());

            list.add(amazonAdCampaignPlacementReport);
        }
        return list;
    }
}
