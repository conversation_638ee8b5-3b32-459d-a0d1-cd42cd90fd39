package com.meiyunji.sponsored.service.cpc.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.LogicType;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.adProductRight.service.IAdProductRightService;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdGroupStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDorisDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroupDorisAllReport;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdProduct;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.permission.util.PermissionSqlBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Repository
public class AmazonAdGroupDorisDaoImpl extends DorisBaseDaoImpl<AmazonAdGroupDorisAllReport> implements IAmazonAdGroupDorisDao {


    @Resource
    private IAdProductRightService adProductRightService;

    private static final Set<String> ORDER_FIELD_SET = Sets.newHashSet(
            "adSalesDoris", "impressionsDoris", "clicksDoris",
             "nameDoris", "adCostPerClickDoris", "ctrDoris", "cvrDoris", "cpcDoris", "acosDoris", "roasDoris",
            "cpaDoris", "adOtherOrderNumDoris", "adOtherSalesDoris", "adOtherSaleNumDoris", "advertisingUnitPriceDoris");

    @Override
    public Page<AmazonAdGroupDorisAllReport> listAmazonAdGroupPage(Integer puid, GroupPageParam param) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        StringBuilder selectSql = new StringBuilder(" select c.ad_group_id ad_group_id, ")
                .append(String.join(",", SQL_MAP.values()));
        boolean bool = StringUtils.isNotBlank(param.getOrderType()) && !"null".equalsIgnoreCase(param.getOrderType());
        selectSql.append(",").append(getSqlField(bool ? param.getOrderField() : null)).append("  ");
        StringBuilder countSql = new StringBuilder("select count(*) from (select c.ad_group_id ad_group_id");
        if (param.getUseAdvanced()) {
            Set<String> strings = getSelectKey(param);
            if (CollectionUtils.isNotEmpty(strings)) {
                Set<String> fieldKey = strings.stream().filter(SQL_MAP::containsKey).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(fieldKey)) {
                    countSql.append(",");
                    countSql.append(fieldKey.stream().map(SQL_MAP::get).collect(Collectors.joining(",")));
                }
            }
        }
        selectSql.append(" from ods_t_amazon_ad_group c left join ods_t_amazon_ad_group_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.ad_group_id=c.ad_group_id and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? where c.puid= ? ");
        countSql.append(" from ods_t_amazon_ad_group c left join ods_t_amazon_ad_group_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.ad_group_id=c.ad_group_id  and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? where c.puid= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(puid);
        StringBuilder whereBuilder = buildAdProductWhere(param, argsList, " group by ad_group_id ", false);
        selectSql.append(whereBuilder);
        countSql.append(whereBuilder);
        countSql.append(" ) r");
        // 查看是否排序
        selectSql.append(" order by ").append(getOrderField(bool ? param.getOrderField() : null)).append(" ");
        if (StringUtils.isBlank(param.getOrderType()) || "desc".equalsIgnoreCase(param.getOrderType())) {
            selectSql.append(" desc");
        }
        selectSql.append(" , ad_group_id desc");
        Object[] args = argsList.toArray();
        return getPageResult(param.getPageNo(), param.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdGroupDorisAllReport.class);
    }

    @Override
    public List<String> idListByAmazonAdGroup(Integer puid, GroupPageParam param) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        StringBuilder selectSql = new StringBuilder(" select c.ad_group_id ad_group_id");
        boolean bool = StringUtils.isNotBlank(param.getOrderType()) && !"null".equalsIgnoreCase(param.getOrderType());
        selectSql.append(",").append(getSqlAllOrderField(bool ? param.getOrderField() : null)).append("  ");
        if (param.getUseAdvanced()) {
            Set<String> strings = getSelectKey(param);
            if (CollectionUtils.isNotEmpty(strings)) {
                Set<String> fieldKey = strings.stream().filter(SQL_MAP::containsKey).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(fieldKey)) {
                    selectSql.append(",");
                    selectSql.append(fieldKey.stream().map(SQL_MAP::get).collect(Collectors.joining(",")));
                }
            }
        }
        selectSql.append(" from ods_t_amazon_ad_group c left join ods_t_amazon_ad_group_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.ad_group_id=c.ad_group_id and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? where c.puid= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(puid);
        StringBuilder whereBuilder = buildAdProductWhere(param, argsList, " group by ad_group_id ", false);
        selectSql.append(whereBuilder);
        // 查看是否排序
        selectSql.append(" order by ").append(getOrderField(bool ? param.getOrderField() : null)).append(" ");
        if (StringUtils.isBlank(param.getOrderType())) {
            selectSql.append(" desc");
        } else {
            if ("desc".equalsIgnoreCase(param.getOrderType())) {
                selectSql.append(" desc");
            }
        }
        selectSql.append(" , ad_group_id desc");
        selectSql.append(" limit ").append(Constants.EXPORT_MAX_SIZE);
        Object[] args = argsList.toArray();
        return getJdbcTemplate().query(selectSql.toString(), args, (rs, i) -> rs.getString("ad_group_id"));
    }

    @Override
    public List<String> idListByGroupPageParamSelAmazonAdGroup(Integer puid, GroupPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder(" select c.ad_group_id ad_group_id from ods_t_amazon_ad_group c where c.puid= ? ");
        argsList.add(puid);
        selectSql.append(this.subJoinAndSql(param, argsList));
        Object[] args = argsList.toArray();
        return getJdbcTemplate().queryForList(selectSql.toString(), String.class, args);
    }

    @Override
    public int listAmazonAdGroupAllCount(Integer puid, GroupPageParam param) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        StringBuilder countSql = new StringBuilder("select count(*) from (select c.ad_group_id ad_group_id");
        if (param.getUseAdvanced()) {
            Set<String> strings = getSelectKey(param);
            if (CollectionUtils.isNotEmpty(strings)) {
                Set<String> fieldKey = strings.stream().filter(SQL_MAP::containsKey).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(fieldKey)) {
                    countSql.append(",");
                    countSql.append(fieldKey.stream().map(SQL_MAP::get).collect(Collectors.joining(",")));
                }
            }
        }
        countSql.append(" from ods_t_amazon_ad_group c left join ods_t_amazon_ad_group_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.ad_group_id=c.ad_group_id  and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? where c.puid= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(puid);
        StringBuilder whereBuilder = buildAdProductWhere(param, argsList, " group by ad_group_id ", false);
        countSql.append(whereBuilder);
        countSql.append(" ) r");
        // 查看是否排序
        Object[] args = argsList.toArray();
        return countPageResult(puid, countSql.toString(), args);
    }

    @Override
    public List<AmazonAdGroup> listByAdGroupIds(Integer puid, Integer shopId, List<String> adGroupIds) {
        StringBuilder sql = new StringBuilder(" select * from ods_t_amazon_ad_group where puid = ? AND shop_id = ? ");
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", adGroupIds, argsList));
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(AmazonAdGroup.class), argsList.toArray());
    }

    @Override
    public AdMetricDto getSumAdMetric(Integer puid, GroupPageParam param) {
        List<Object> argsList = new ArrayList<>();
        Set<String> keySet = new HashSet<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        keySet.add("costDoris");
        keySet.add("totalSalesDoris");
        keySet.add("orderNumDoris");
        keySet.add("saleNumDoris");
        boolean useAdvanced = param.getUseAdvanced() != null && param.getUseAdvanced();
        if (useAdvanced) {
            Set<String> strings = getSelectKey(param);
            if (CollectionUtils.isNotEmpty(strings)) {
                Set<String> fieldKey = strings.stream().filter(SQL_MAP::containsKey).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(fieldKey)) {
                    keySet.addAll(fieldKey);
                }
            }
        }
        StringBuilder sumSql = new StringBuilder(" select ").append(useAdvanced ? " c.ad_group_id ad_group_id," : " ")
                .append(keySet.stream().map(SQL_MAP::get).collect(Collectors.joining(",")));
        sumSql.append(" from ods_t_amazon_ad_group c join ods_t_amazon_ad_group_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.ad_group_id=c.ad_group_id and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? where c.puid= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(puid);
        StringBuilder whereBuilder = buildAdProductWhere(param, argsList, useAdvanced ? " group by ad_group_id " : null, true);
        sumSql.append(whereBuilder);
//        logger.info("sum sql = {}", sumSql);
        List<AdMetricDto> list = getJdbcTemplate().query(sumSql.toString(), (re, i) -> AdMetricDto.builder()
                .sumCost(Optional.ofNullable(re.getBigDecimal("costDoris")).orElse(BigDecimal.ZERO))
                .sumAdSale(Optional.ofNullable(re.getBigDecimal("totalSalesDoris")).orElse(BigDecimal.ZERO))
                .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("orderNumDoris")).orElse(BigDecimal.ZERO))
                .sumOrderNum(Optional.ofNullable(re.getBigDecimal("saleNumDoris")).orElse(BigDecimal.ZERO))
                .build(), argsList.toArray());
        if (useAdvanced) {
            if (CollectionUtils.isEmpty(list)) {
                return null;
            }
            return AdMetricDto.builder()
                    .sumCost(list.stream().map(AdMetricDto::getSumCost).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .sumAdSale(list.stream().map(AdMetricDto::getSumAdSale).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .sumAdOrderNum(list.stream().map(AdMetricDto::getSumAdOrderNum).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .sumOrderNum(list.stream().map(AdMetricDto::getSumOrderNum).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .build();
        } else {
            return list.size() > 0 ? list.get(0) : null;
        }
    }

    @Override
    public List<AdHomePerformancedto> listTotalAmazonAdGroupGroupByAdGroupId(Integer puid, GroupPageParam param) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        StringBuilder selectSql = new StringBuilder(" select c.ad_group_id ad_group_id, ")
                .append(String.join(",", SQL_MAP.values()));
        selectSql.append(" from ods_t_amazon_ad_group c join ods_t_amazon_ad_group_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.ad_group_id=c.ad_group_id and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? where c.puid= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(puid);
        StringBuilder whereBuilder = buildAdProductWhere(param, argsList, " group by ad_group_id ", true);
        selectSql.append(whereBuilder);
        PermissionSqlBuilder.addPermissionFilter(selectSql , argsList , "c.campaign_id" , true);
        return getJdbcTemplate().query(selectSql.toString(), (re, i) -> AdHomePerformancedto.builder()
                .groupId(re.getString("ad_group_id"))
                .adCost(Optional.ofNullable(re.getBigDecimal("costDoris")).orElse(BigDecimal.ZERO))
                .adOrderNum(Optional.of(re.getInt("orderNumDoris")).orElse(0))  //销量字段订单
                .adSale(Optional.ofNullable(re.getBigDecimal("totalSalesDoris")).orElse(BigDecimal.ZERO))
                .clicks(Optional.of(re.getInt("clicksDoris")).orElse(0))
                .impressions(Optional.of(re.getInt("impressionsDoris")).orElse(0))
                .countDate("")
                /**
                 * TODO 广告报告重构
                 * 本广告产品订单量
                 */
                .adSaleNum(Optional.of(re.getInt("adSaleNumDoris")).orElse(0))
                //本广告产品销售额
                .adSales(Optional.ofNullable(re.getBigDecimal("adSalesDoris")).orElse(BigDecimal.ZERO))
                //广告销量
                .salesNum(Optional.of(re.getInt("saleNumDoris")).orElse(0))
                //本广告产品销量
                .orderNum(Optional.of(re.getInt("adOrderNumDoris")).orElse(0))
                .type("sp")
                .build(), argsList.toArray());

    }

    @Override
    public AdHomePerformancedto getTotalAmazonAdGroupCompareData(Integer puid, GroupPageParam param, String start, String end) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(start);
        String endDate = DateUtil.getDateSqlFormat(end);
        StringBuilder selectSql = new StringBuilder(" select ").append(String.join(",", SQL_MAP.values()));
        selectSql.append(" from ods_t_amazon_ad_group_report r where puid = ? and shop_id = ? and count_day >= ? and count_day <= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        selectSql.append(" and ad_group_id in ( select ad_group_id from ( ").append(this.getIdListAmazonAdGroupPageSql(puid, param, argsList)).append(") a ) ");
        List<AdHomePerformancedto> list = getJdbcTemplate().query(selectSql.toString(), (re, i) -> AdHomePerformancedto.builder()
                .adCost(Optional.ofNullable(re.getBigDecimal("costDoris")).orElse(BigDecimal.ZERO))
                .adOrderNum(Optional.of(re.getInt("orderNumDoris")).orElse(0))  //销量字段订单
                .adSale(Optional.ofNullable(re.getBigDecimal("totalSalesDoris")).orElse(BigDecimal.ZERO))
                .clicks(Optional.of(re.getInt("clicksDoris")).orElse(0))
                .impressions(Optional.of(re.getInt("impressionsDoris")).orElse(0))
                .countDate("")
                /**
                 * TODO 广告报告重构
                 * 本广告产品订单量
                 */
                .adSaleNum(Optional.of(re.getInt("adSaleNumDoris")).orElse(0))
                //本广告产品销售额
                .adSales(Optional.ofNullable(re.getBigDecimal("adSalesDoris")).orElse(BigDecimal.ZERO))
                //广告销量
                .salesNum(Optional.of(re.getInt("saleNumDoris")).orElse(0))
                //本广告产品销量
                .orderNum(Optional.of(re.getInt("adOrderNumDoris")).orElse(0))
                .type("sp")
                .build(), argsList.toArray());
        return list.size() == 1 ? list.get(0) : new AdHomePerformancedto();
    }

    public String getIdListAmazonAdGroupPageSql(Integer puid, GroupPageParam param, List<Object> argsList) {
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        StringBuilder selectSql = new StringBuilder(" select c.ad_group_id ad_group_id");
        if (param.getUseAdvanced()) {
            Set<String> strings = getSelectKey(param);
            if (CollectionUtils.isNotEmpty(strings)) {
                Set<String> fieldKey = strings.stream().filter(SQL_MAP::containsKey).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(fieldKey)) {
                    selectSql.append(",");
                    selectSql.append(fieldKey.stream().map(SQL_MAP::get).collect(Collectors.joining(",")));
                }
            }
        }
        selectSql.append(" from ods_t_amazon_ad_group c left join ods_t_amazon_ad_group_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.ad_group_id=c.ad_group_id and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? where c.puid= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(puid);
        StringBuilder whereBuilder = buildAdProductWhere(param, argsList, " group by ad_group_id ", false);
        selectSql.append(whereBuilder);
        return selectSql.toString();
    }

    @Override
    public List<AdHomePerformancedto> listTotalAmazonSpAdGroupGroupDateById(Integer puid, GroupPageParam param, List<String> groupId) {
        if (CollectionUtils.isEmpty(groupId)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        //按天聚合
        StringBuilder sql = new StringBuilder(" select count_date,sum(cost) cost, sum(total_sales) total_sales,sum(ad_sales) ad_sales,sum(impressions) impressions,");
        sql.append(" sum(clicks)  clicks,sum(order_num)  sales_num,sum(ad_order_num)  ad_order_num,sum(sale_num)  sale_num,sum(ad_sale_num) ad_sale_num from  ");
        sql.append(" ods_t_amazon_ad_group_report ");
        sql.append(" where puid= ? and shop_id=? ");
        argsList.add(puid);
        argsList.add(param.getShopId());

        if (groupId.size() > 10000) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", groupId, argsList));
        } else {
            sql.append(SqlStringUtil.dealInList("ad_group_id", groupId, argsList));
        }

        sql.append("  and count_day >= ? and count_day <= ? group by count_date ");
        argsList.add(startDate);
        argsList.add(endDate);
        return getJdbcTemplate().query(sql.toString(), (re, i) -> AdHomePerformancedto.builder()
                .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                .adOrderNum(Optional.of(re.getInt("sale_num")).orElse(0))  //销量字段订单
                .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                .clicks(Optional.of(re.getInt("clicks")).orElse(0))
                .impressions(Optional.of(re.getInt("impressions")).orElse(0))
                .countDate(re.getString("count_date"))
                /**
                 * TODO 广告报告重构
                 * 本广告产品订单量
                 */
                .adSaleNum(Optional.of(re.getInt("ad_sale_num")).orElse(0))
                //本广告产品销售额
                .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                //广告销量
                .salesNum(Optional.of(re.getInt("sales_num")).orElse(0))
                //本广告产品销量
                .orderNum(Optional.of(re.getInt("ad_order_num")).orElse(0))
                .build(), argsList.toArray());
    }

    /**
     *  构建主表的where语句
     */
    private StringBuilder buildAdProductWhere(GroupPageParam param, List<Object> argsList, String groupSql, boolean isNull) {
        StringBuilder whereSql = new StringBuilder();
        whereSql.append(this.subJoinAndSql(param, argsList));

        if (isNull) {
            whereSql.append(" and r.puid = ? ");
            argsList.add(param.getPuid());
        }

        if (StringUtils.isNotBlank(groupSql)) {
            whereSql.append(groupSql);
        }
        whereSql.append(subWhereSql(param, argsList));
        return whereSql;
    }

    private String subJoinAndSql(GroupPageParam param, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }

        if (StringUtils.isNotEmpty(param.getCampaignId())) {
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId());
            if (campaignIds.size() <= 10000) {
                whereSql.append(SqlStringUtil.dealInList("c.campaign_id", campaignIds, argsList));
            } else {
                whereSql.append(SqlStringUtil.dealBitMapDorisInList("c.campaign_id", campaignIds, argsList));
            }
        }
        if (StringUtils.isNotBlank(param.getMultiCampaignId())) {
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", StringUtil.splitStr(param.getMultiCampaignId()), argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            if (param.getCampaignIdList().size() <= 10000) {
                whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
            } else {
                whereSql.append(SqlStringUtil.dealBitMapDorisInList("c.campaign_id", param.getCampaignIdList(), argsList));
            }
        }
        // 广告组
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getGroupId());
            if (groupIds.size() <= 10000) {
                whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", groupIds, argsList));
            } else {
                whereSql.append(SqlStringUtil.dealBitMapDorisInList("c.ad_group_id", groupIds, argsList));
            }
        }
        if (StringUtils.isNotBlank(param.getMultiGroupId())){
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", StringUtil.splitStr(param.getMultiGroupId()), argsList));
        }
        // 标签
        if (CollectionUtils.isNotEmpty(param.getGroupIds())){
            if (param.getGroupIds().size() <= 10000) {
                whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", param.getGroupIds(), argsList));
            } else {
                whereSql.append(SqlStringUtil.dealBitMapDorisInList("c.ad_group_id", param.getGroupIds(), argsList));
            }
        }
        // 广告策略筛选
        if(CollectionUtil.isNotEmpty(param.getAdStrategyTypeList())) {
            String sql = AdGroupStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(), argsList, "c.ad_group_id", true);
            if(StringUtils.isNotEmpty(sql)){
                whereSql.append(sql);
            }
        }
        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if (StringUtils.isNotBlank(param.getSearchType()) && SearchTypeEnum.EXACT.getValue().equals(param.getSearchType())) {
                whereSql.append(" and lower(c.name) = ? ");
                argsList.add(param.getSearchValue().trim().toLowerCase());
            } else {
                whereSql.append(" and lower(c.name) like ? ");
                argsList.add("%" + param.getSearchValue().trim().toLowerCase() + "%");
            }
        }

        List<String> valueList = new ArrayList<>();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            for (String value : param.getSearchValueList()) {
                valueList.add(value.toLowerCase());
            }
        }

        if (CollectionUtils.isNotEmpty(valueList)) {
            whereSql.append(SqlStringUtil.dealInList("lower(c.name)", valueList, argsList));
        }

        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }

        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and c.serving_status = ? ");
                argsList.add(AmazonAdGroup.servingStatusEnum.AD_GROUP_STATUS_ENABLED.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("c.serving_status", list, argsList));
            }
        }

        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (param.getBidMin() != null) {   //默认竞价
                whereSql.append(" and c.default_bid >= ?");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and c.default_bid <= ?");
                argsList.add(param.getBidMax());
            }
        }
        return whereSql.toString();
    }

    /**
     *  字段对象
     */
    public static final Map<String, String> SQL_MAP = Collections.unmodifiableMap(new HashMap<String, String>() {
        {
            put("costDoris", "IFNULL(sum(r.cost),0) `costDoris`");
            put("totalSalesDoris", "IFNULL(sum(r.total_sales),0) totalSalesDoris");
            put("adSalesDoris", "IFNULL(sum(r.ad_sales),0) adSalesDoris");
            put("impressionsDoris", "IFNULL(sum(`impressions`),0) impressionsDoris");
            put("clicksDoris", "IFNULL(sum(`clicks`),0) clicksDoris");

            put("orderNumDoris", "IFNULL(sum(r.sale_num),0) orderNumDoris");
            put("adOrderNumDoris", "IFNULL(sum(r.ad_order_num),0) adOrderNumDoris");
            put("saleNumDoris", "IFNULL(sum(r.order_num),0) saleNumDoris");
            put("adSaleNumDoris", "IFNULL(sum(r.ad_sale_num),0) adSaleNumDoris");
        }
    });

    /**
     * 获取排序字段
     */
    private String getSqlAllOrderField(String field) {
        if (StringUtils.isBlank(field)) {
            return " any(c.create_time) dataUpdateTime ";
        }

        switch (field) {
            case "impressions":
                return " IFNULL(sum(r.impressions),0) impressionsDoris ";
            case "clicks":
                return " IFNULL(sum(r.clicks),0) clicksDoris ";
            case "adCost":
            case "adCostPercentage":
            case "acots":
                return " IFNULL(sum(r.cost),0) costDoris ";
            case "adSale":
            case "adSalePercentage":
            case "asots":
                return " IFNULL(sum(r.total_sales),0) totalSalesDoris ";
            case "adSales":
                return " IFNULL(sum(r.ad_sales),0) adSalesDoris ";
            // 广告订单量
            case "adOrderNum":
            case "adOrderNumPercentage":
                return " IFNULL(sum(r.sale_num),0) orderNumDoris ";
            //本广告产品订单量
            case "adSaleNum":
                return " IFNULL(sum(r.ad_sale_num),0) adSaleNumDoris ";
            //广告销量
            case "orderNum":
            case "orderNumPercentage":
                return " IFNULL(sum(r.order_num),0) saleNumDoris ";
            //本广告产品销量
            case "adSelfSaleNum":
                return " IFNULL(sum(r.ad_order_num),0) adOrderNumDoris ";
            case "name":
                return " any(c.name) nameDoris ";
            case "adCostPerClick":
                return " ifnull(sum(r.cost)/sum(`clicks`),0) as adCostPerClickDoris ";
//            case "vcpm":
//                return " ifnull((sum(r.cost)/sum(if (r.type = 'sb', `viewable_impressions`, `view_impressions`))) * 1000, 0) as vcpmDoris ";
            case "ctr":
                return " ifnull(sum(`clicks`)/sum(`impressions`),0) as ctrDoris ";
            case "cvr":
                return " ifnull(sum(sale_num)/sum(`clicks`),0) as cvrDoris ";
            case "cpc":
                return " ifnull(sum(r.cost)/sum(`clicks`),0) as cpcDoris ";
            case "acos":
                return " ifnull(sum(r.cost)/sum(total_sales),0) as acosDoris ";
            case "roas":
                return " ifnull(sum(total_sales)/sum(r.cost),0) as roasDoris ";
            case "cpa":
                return " ifnull(sum(r.cost)/sum(sale_num),0) as cpaDoris ";
            //其他产品广告订单量
            case "adOtherOrderNum":
                return " ifnull(sum(sale_num - ad_sale_num),0) as adOtherOrderNumDoris ";
            //其他产品广告销售额
            case "adOtherSales":
                return "ifnull(sum(total_sales)-sum(ad_sales),0) as adOtherSalesDoris ";
            //广告销量
            case "adOtherSaleNum":
                return " ifnull(sum(order_num - ad_order_num),0) as adOtherSaleNumDoris ";
            case  "advertisingUnitPrice":
                return " ifnull(sum(total_sales)/sum(sale_num),0) as advertisingUnitPriceDoris";
            default:
                return " any(c.create_time) dataUpdateTime ";
        }
    }

    /**
     * 获取排序字段
     */
    private String getOrderField(String field) {
        if (StringUtils.isBlank(field)) {
            return " dataUpdateTime ";
        }
        if (ORDER_FIELD_SET.contains(field + "Doris")){
            return " " + field + "Doris ";
        }
        Set<String> set = Sets.newHashSet("adCost", "adCostPercentage",
                "acots", "adSale", "adSalePercentage", "asots", "adOrderNum", "adOrderNumPercentage",
                "adSaleNum", "saleNum", "orderNum", "orderNumPercentage", "adSelfSaleNum");
        if (set.contains(field)) {
            switch (field) {
                case "adCost":
                case "adCostPercentage":
                case "acots":
                    return " costDoris ";
                case "adSale":
                case "adSalePercentage":
                case "asots":
                    return " totalSalesDoris ";
                // 广告订单量
                case "adOrderNum":
                    return " orderNumDoris ";
                case "adOrderNumPercentage":
                    return " orderNumDoris ";
                //本广告产品订单量
                case "adSaleNum":
                    return " adSaleNumDoris ";
                //广告销量
                case "saleNum":
                    return " orderNumDoris ";
                case "orderNum":
                case "orderNumPercentage":
                    return " saleNumDoris ";
                //本广告产品销量
                case "adSelfSaleNum":
                    return " adOrderNumDoris ";
                //其他产品广告销量
                default:
                    return " " + field + "Doris ";
            }
        } else {
            return " dataUpdateTime ";
        }
    }

    /**
     * 高级排序
     */
    private StringBuilder subWhereSql(GroupPageParam param, List<Object> argsList) {
        StringBuilder subWhereSql = new StringBuilder();
        //高级筛选
        if (param.getUseAdvanced()) {
            BigDecimal shopSales = param.getShopSales() != null ? param.getShopSales() : BigDecimal.ZERO;

            subWhereSql.append(" having 1=1 ");
            //展示量
            if (param.getImpressionsMin() != null) {
                subWhereSql.append(" and impressionsDoris >= ?");
                argsList.add(param.getImpressionsMin());
            }
            if (param.getImpressionsMax() != null) {
                subWhereSql.append(" and impressionsDoris <= ?");
                argsList.add(param.getImpressionsMax());
            }
            //点击量
            if (param.getClicksMin() != null) {
                subWhereSql.append(" and clicksDoris >= ?");
                argsList.add(param.getClicksMin());
            }
            if (param.getClicksMax() != null) {
                subWhereSql.append(" and clicksDoris <= ?");
                argsList.add(param.getClicksMax());
            }
            //点击率（clicks/impressions）
            if (param.getClickRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(clicksDoris/impressionsDoris,0),4) >= ?");
                argsList.add(param.getClickRateMin());
            }
            if (param.getClickRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(clicksDoris/impressionsDoris,0),4) <= ?");
                argsList.add(param.getClickRateMax());
            }
            //花费
            if (param.getCostMin() != null) {
                subWhereSql.append(" and costDoris >= ?");
                argsList.add(param.getCostMin());
            }
            if (param.getCostMax() != null) {
                subWhereSql.append(" and costDoris <= ?");
                argsList.add(param.getCostMax());
            }
            //cpc  平均点击费用
            if (param.getCpcMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/clicksDoris,0),2) >= ?");
                argsList.add(param.getCpcMin());
            }
            if (param.getCpcMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/clicksDoris,0),2) <= ?");
                argsList.add(param.getCpcMax());
            }
            //广告订单量
            if (param.getOrderNumMin() != null) {
                subWhereSql.append(" and orderNumDoris >= ?");
                argsList.add(param.getOrderNumMin());
            }
            if (param.getOrderNumMax() != null) {
                subWhereSql.append(" and orderNumDoris <= ?");
                argsList.add(param.getOrderNumMax());
            }
            //广告销售额
            if (param.getSalesMin() != null) {
                subWhereSql.append(" and totalSalesDoris >= ?");
                argsList.add(param.getSalesMin());
            }
            if (param.getSalesMax() != null) {
                subWhereSql.append(" and totalSalesDoris <= ?");
                argsList.add(param.getSalesMax());
            }
            //订单转化率
            if (param.getSalesConversionRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(orderNumDoris/clicksDoris,0),4) >= ?");
                argsList.add(param.getSalesConversionRateMin());
            }
            if (param.getSalesConversionRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(orderNumDoris/clicksDoris,0),4) <= ?");
                argsList.add(param.getSalesConversionRateMax());
            }
            //acos
            if (param.getAcosMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/totalSalesDoris,0),4) >= ?");
                argsList.add(param.getAcosMin());
            }
            if (param.getAcosMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/totalSalesDoris,0),4) <= ?");
                argsList.add(param.getAcosMax());
            }
            // roas
            if (param.getRoasMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/costDoris,0),2) >= ?");
                argsList.add(param.getRoasMin());
            }
            // roas
            if (param.getRoasMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/costDoris,0),2) <= ?");
                argsList.add(param.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(costDoris,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAcotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(costDoris,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAcotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (param.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(totalSalesDoris,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAsotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (param.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(totalSalesDoris,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAsotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            //广告销量
            if (param.getAdSalesTotalMin() != null) {
                subWhereSql.append(" and saleNumDoris >= ? ");
                argsList.add(param.getAdSalesTotalMin());
            }

            if (param.getAdSalesTotalMax() != null) {
                subWhereSql.append(" and saleNumDoris <= ? ");
                argsList.add(param.getAdSalesTotalMax());
            }
            //CPA
            if (param.getCpaMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/orderNumDoris, 0), 2) >= ? ");
                argsList.add(param.getCpaMin());
            }
            if (param.getCpaMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/orderNumDoris, 0), 2) <= ? ");
                argsList.add(param.getCpaMax());
            }
            //本广告产品订单量（绝对值）adSaleNumMin
            if (param.getAdSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(adSaleNumDoris, 0) >= ? ");
                argsList.add(param.getAdSaleNumMin());
            }
            if (param.getAdSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(adSaleNumDoris, 0) <= ? ");
                argsList.add(param.getAdSaleNumMax());
            }

            //其他产品广告订单量（绝对值） adOtherOrderNumMin
            if (param.getAdOtherOrderNumMin() != null) {
                subWhereSql.append(" and ifnull(orderNumDoris - adSaleNumDoris, 0) >= ? ");
                argsList.add(param.getAdOtherOrderNumMin());
            }

            if (param.getAdOtherOrderNumMax() != null) {
                subWhereSql.append(" and ifnull(orderNumDoris - adSaleNumDoris, 0) <= ? ");
                argsList.add(param.getAdOtherOrderNumMax());
            }

            //本广告产品销售额（绝对值） adSalesMin
            if (param.getAdSalesMin() != null) {
                subWhereSql.append(" and ifnull(adSalesDoris, 0) >= ? ");
                argsList.add(param.getAdSalesMin());
            }

            if (param.getAdSalesMax() != null) {
                subWhereSql.append(" and ifnull(adSalesDoris, 0) <= ? ");
                argsList.add(param.getAdSalesMax());
            }

            //其他产品广告销售额（绝对值）adOtherSalesMin
            if (param.getAdOtherSalesMin() != null) {
                subWhereSql.append(" and ifnull(totalSalesDoris - adSalesDoris, 0) >= ? ");
                argsList.add(param.getAdOtherSalesMin());
            }

            if (param.getAdOtherSalesMax() != null) {
                subWhereSql.append(" and ifnull(totalSalesDoris - adSalesDoris, 0) <= ? ");
                argsList.add(param.getAdOtherSalesMax());
            }

            //本广告产品销量（绝对值）adSelfSaleNumMin units_ordered7d_same_sku
            if (param.getAdSelfSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(adOrderNumDoris, 0) >= ? ");
                argsList.add(param.getAdSelfSaleNumMin());
            }
            if (param.getAdSelfSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(adOrderNumDoris, 0) <= ? ");
                argsList.add(param.getAdSelfSaleNumMax());
            }

            //其他产品广告销量（绝对值）adOtherSaleNumMin
            if (param.getAdOtherSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(saleNumDoris - adOrderNumDoris, 0) >= ? ");
                argsList.add(param.getAdOtherSaleNumMin());
            }
            if (param.getAdOtherSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(saleNumDoris - adOrderNumDoris, 0) <= ? ");
                argsList.add(param.getAdOtherSaleNumMax());
            }

            // 广告笔单价 筛选
            if (param.getAdvertisingUnitPriceMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/orderNumDoris, 0), 2) >= ?");
                argsList.add(param.getAdvertisingUnitPriceMin());
            }
            if (param.getAdvertisingUnitPriceMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/orderNumDoris, 0), 2) <= ?");
                argsList.add(param.getAdvertisingUnitPriceMax());
            }
        }
        return subWhereSql;
    }

    /**
     * 获取排序字段
     */
    private String getSqlField(String field) {
        if (StringUtils.isBlank(field)) {
            return " any(c.create_time) dataUpdateTime ";
        }
        switch (field) {
            case "name":
                return " any(c.name) nameDoris ";
            case "adCostPerClick":
                return " ifnull(sum(r.cost)/sum(`clicks`),0) as adCostPerClickDoris ";
//            case "vcpm":
//                return " ifnull((sum(r.cost)/sum(if (r.type = 'sb', `viewable_impressions`, `view_impressions`))) * 1000, 0) as vcpmDoris ";
            case "ctr":
                return " ifnull(sum(`clicks`)/sum(`impressions`),0) as ctrDoris ";
            case "cvr":
                return " ifnull(sum(sale_num)/sum(`clicks`),0) as cvrDoris ";
            case "cpc":
                return " ifnull(sum(r.cost)/sum(`clicks`),0) as cpcDoris ";
            case "acos":
                return " ifnull(sum(r.cost)/sum(total_sales),0) as acosDoris ";
            case "roas":
                return " ifnull(sum(total_sales)/sum(r.cost),0) as roasDoris ";
            case "cpa":
                return " ifnull(sum(r.cost)/sum(sale_num),0) as cpaDoris ";
            //其他产品广告订单量
            case "adOtherOrderNum":
                return " ifnull(sum(sale_num - ad_sale_num),0) as adOtherOrderNumDoris ";
            //其他产品广告销售额
            case "adOtherSales":
                return "ifnull(sum(total_sales)-sum(ad_sales),0) as adOtherSalesDoris ";
            //广告销量
            case "adOtherSaleNum":
                return " ifnull(sum(order_num - ad_order_num),0) as adOtherSaleNumDoris ";
            case  "advertisingUnitPrice":
                return " ifnull(sum(total_sales)/sum(sale_num),0) as advertisingUnitPriceDoris";
            default:
                return " any(c.create_time) dataUpdateTime ";
        }
    }

    /**
     * 获取高级排序 需要的字段
     */
    private Set<String> getSelectKey(GroupPageParam param) {
        Set<String> keySet = new HashSet<>();
        if (param.getImpressionsMin() != null) {
            keySet.add("impressionsDoris");
        }
        if (param.getImpressionsMax() != null) {
            keySet.add("impressionsDoris");
        }
        //点击量
        if (param.getClicksMin() != null) {
            keySet.add("clicksDoris");
        }
        if (param.getClicksMax() != null) {
            keySet.add("clicksDoris");
        }
        //点击率（clicks/impressions）
        if (param.getClickRateMin() != null) {
            keySet.add("clicksDoris");
            keySet.add("impressionsDoris");
        }
        if (param.getClickRateMax() != null) {
            keySet.add("clicksDoris");
            keySet.add("impressionsDoris");
        }
        //花费
        if (param.getCostMin() != null) {
            keySet.add("costDoris");
        }
        if (param.getCostMax() != null) {
            keySet.add("costDoris");
        }
        //cpc  平均点击费用
        if (param.getCpcMin() != null) {
            keySet.add("costDoris");
            keySet.add("clicksDoris");
        }
        if (param.getCpcMax() != null) {
            keySet.add("costDoris");
            keySet.add("clicksDoris");
        }
        //广告订单量
        if (param.getOrderNumMin() != null) {
            keySet.add("orderNumDoris");
        }
        if (param.getOrderNumMax() != null) {
            keySet.add("orderNumDoris");
        }
        //广告销售额
        if (param.getSalesMin() != null) {
            keySet.add("totalSalesDoris");
        }
        if (param.getSalesMax() != null) {
            keySet.add("totalSalesDoris");
        }
        //订单转化率
        if (param.getSalesConversionRateMin() != null) {
            keySet.add("orderNumDoris");
            keySet.add("clicksDoris");
        }
        if (param.getSalesConversionRateMax() != null) {
            keySet.add("orderNumDoris");
            keySet.add("clicksDoris");
        }
        //acos
        if (param.getAcosMin() != null) {
            keySet.add("costDoris");
            keySet.add("totalSalesDoris");
        }
        if (param.getAcosMax() != null) {
            keySet.add("costDoris");
            keySet.add("totalSalesDoris");
        }
        // roas
        if (param.getRoasMin() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("costDoris");
        }
        // roas
        if (param.getRoasMax() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("costDoris");
        }
        // acots  需要乘以店铺销售额
        if (param.getAcotsMin() != null) {
            keySet.add("costDoris");
        }
        // acots  需要乘以店铺销售额
        if (param.getAcotsMax() != null) {
            keySet.add("costDoris");
        }
        // asots 需要乘以店铺销售额
        if (param.getAsotsMin() != null) {
            keySet.add("totalSalesDoris");
        }
        // asots  需要乘以店铺销售额
        if (param.getAsotsMax() != null) {
            keySet.add("totalSalesDoris");
        }

//        //可见展示次数
//        if (param.getViewImpressionsMin() != null) {
//            keySet.add("viewImpressionsDoris");
//        }
//        if (param.getViewImpressionsMax() != null) {
//            keySet.add("viewImpressionsDoris");
//        }

        //广告销量
        if (param.getAdSalesTotalMin() != null) {
            keySet.add("saleNumDoris");
        }

        if (param.getAdSalesTotalMax() != null) {
            keySet.add("saleNumDoris");
        }
        //CPA
        if (param.getCpaMin() != null) {
            keySet.add("costDoris");
            keySet.add("orderNumDoris");
        }
        if (param.getCpaMax() != null) {
            keySet.add("costDoris");
            keySet.add("orderNumDoris");
        }
//        //VCPM= 每千次展现费用=广告花费/可见展现次数x1000
//        if (param.getVcpmMin() != null) {
//            keySet.add("costDoris");
//            keySet.add("viewImpressionsDoris");
//        }
//        if (param.getVcpmMax() != null) {
//            keySet.add("costDoris");
//            keySet.add("viewImpressionsDoris");
//        }
        //本广告产品订单量（绝对值）adSaleNumMin
        if (param.getAdSaleNumMin() != null) {
            keySet.add("adSaleNumDoris");
        }
        if (param.getAdSaleNumMax() != null) {
            keySet.add("adSaleNumDoris");
        }

        //其他产品广告订单量（绝对值） adOtherOrderNumMin
        if (param.getAdOtherOrderNumMin() != null) {
            keySet.add("orderNumDoris");
            keySet.add("adSaleNumDoris");
        }

        if (param.getAdOtherOrderNumMax() != null) {
            keySet.add("orderNumDoris");
            keySet.add("adSaleNumDoris");
        }

        //本广告产品销售额（绝对值） adSalesMin
        if (param.getAdSalesMin() != null) {
            keySet.add("adSalesDoris");
        }

        if (param.getAdSalesMax() != null) {
            keySet.add("adSalesDoris");
        }

        //其他产品广告销售额（绝对值）adOtherSalesMin
        if (param.getAdOtherSalesMin() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("adSalesDoris");
        }

        if (param.getAdOtherSalesMax() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("adSalesDoris");
        }

        //本广告产品销量（绝对值）adSelfSaleNumMin units_ordered7d_same_sku
        if (param.getAdSelfSaleNumMin() != null) {
            keySet.add("adOrderNumDoris");
        }
        if (param.getAdSelfSaleNumMax() != null) {
            keySet.add("adOrderNumDoris");
        }

        //其他产品广告销量（绝对值）adOtherSaleNumMin
        if (param.getAdOtherSaleNumMin() != null) {
            keySet.add("saleNumDoris");
            keySet.add("adOrderNumDoris");
        }
        if (param.getAdOtherSaleNumMax() != null) {
            keySet.add("saleNumDoris");
            keySet.add("adOrderNumDoris");
        }

        //“品牌新买家”订单量（绝对值） orders_new_to_brand14d
        if (param.getOrdersNewToBrandFTDMin() != null) {
            keySet.add("ordersNewToBrand14dDoris");
        }
        if (param.getOrdersNewToBrandFTDMax() != null) {
            keySet.add("ordersNewToBrand14dDoris");
        }

        // 广告笔单价 筛选
        if (param.getAdvertisingUnitPriceMin() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("orderNumDoris");
        }
        if (param.getAdvertisingUnitPriceMax() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("orderNumDoris");
        }
        return keySet;
    }

    @Override
    public List<AmazonAdGroupDorisAllReport> getGroupClickData(MultiShopGroupByProductParam param, List<OdsAmazonAdProduct> productGroup) {
        List<Object> args = new ArrayList<>();
        String startDate = param.getStartDate();
        String endDate = param.getEndDate();

        List<String> adGroupIds = productGroup.stream().map(OdsAmazonAdProduct::getAdGroupId).distinct().collect(Collectors.toList());
        List<Integer> shopIds = productGroup.stream().map(OdsAmazonAdProduct::getShopId).distinct().collect(Collectors.toList());

        StringBuilder selectSql = new StringBuilder();
        selectSql.append("select")
                .append(" c.ad_group_id ad_group_id,")
                .append(" SUM(IFNULL(`clicks`, 0)) clicksDoris")
                .append(" from ods_t_amazon_ad_group c left join ods_t_amazon_ad_group_report r")
                .append(" on c.puid=r.puid and c.marketplace_id=r.marketplace_id and c.shop_id=r.shop_id and c.ad_group_id=r.ad_group_id")
                .append(" and r.puid=? and r.marketplace_id=? and r.count_day>=? and r.count_day<=?");
        args.add(param.getPuid());
        args.add(param.getMarketplaceId());
        args.add(startDate);
        args.add(endDate);
        selectSql.append(SqlStringUtil.dealInList("r.shop_id", shopIds, args));
        if (adGroupIds.size() < 10000) {
            selectSql.append(SqlStringUtil.dealInList("r.ad_group_id", adGroupIds, args));
        } else {
            selectSql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", adGroupIds, args));
        }

        selectSql.append(" where c.puid=? and c.marketplace_id=?");
        args.add(param.getPuid());
        args.add(param.getMarketplaceId());
        selectSql.append(SqlStringUtil.dealInList("c.shop_id", shopIds, args));
        if (adGroupIds.size() < 10000) {
            selectSql.append(SqlStringUtil.dealInList("c.ad_group_id", adGroupIds, args));
        } else {
            selectSql.append(SqlStringUtil.dealBitMapDorisInList("c.ad_group_id", adGroupIds, args));
        }

        selectSql.append(" group by c.ad_group_id ");

        return getJdbcTemplate().query(selectSql.toString(), (re, i) -> {
            AmazonAdGroupDorisAllReport o = new AmazonAdGroupDorisAllReport();
            o.setAdGroupId(re.getString("ad_group_id"));
            o.setClicks(re.getInt("clicksDoris"));
            return o;
        }, args.toArray());
    }


    @Override
    public Page<AdGroupOptionVo> pageAllGroupsByType(Integer puid, String shopId, List<String> typeAList, String groupType, String name, String campaignIds, String groupIds, String portfolioId, int pageNo, int pageSize){
        List<Object> argList = new ArrayList<>();
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        StringBuilder selectSql = new StringBuilder("select type,campaign_id, ad_group_id,name,state,create_time,  ad_format, CASE LOWER (state) " +
                "  WHEN 'enabled' THEN 3 " +
                "  WHEN 'paused' THEN 2 " +
                "  WHEN 'archived' THEN 1 " +
                "  ELSE 0 " +
                "  END AS sort_id from ( ");
        StringBuilder sql = new StringBuilder();
        StringBuilder sqlCount = new StringBuilder();
        StringBuilder spSql = new StringBuilder();
        StringBuilder spCountSql = new StringBuilder();
        //sb
        if (CollectionUtils.isEmpty(typeAList) || typeAList.contains(Constants.SP)) {

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid);
            if (StringUtils.isNotBlank(shopId)) {
                List<String> list = StringUtil.splitStr(shopId);
                builder.inStrList("shop_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(campaignIds)) {
                List<String> list = StringUtil.splitStr(campaignIds);
                builder.inStrList("campaign_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(groupIds)) {
                List<String> list = StringUtil.splitStr(groupIds);
                builder.inStrList("ad_group_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(name)) {
                builder.like("name", name);
            }
            if (StringUtils.isNotBlank(groupType)) {
                builder.and().leftBracket();
                builder.equalToWithoutCheck(LogicType.EPT, "ad_group_type", "");
                builder.isNull(LogicType.OR, "ad_group_type");
                builder.in(LogicType.OR, "ad_group_type", new String[]{"auto", groupType});
                builder.rightBracket();
            }
            ConditionBuilder conditionBuilder = builder.build();
            spSql.append(" select 'sp' as type,campaign_id, ad_group_id,name,state,create_time, '' as ad_format from ods_t_amazon_ad_group where ");
            spSql.append(conditionBuilder.getSql());
            spCountSql.append(" select campaign_id from ods_t_amazon_ad_group where ");
            spCountSql.append(conditionBuilder.getSql());
            sql.append(spSql);
            sqlCount.append(spCountSql);
            argList.addAll(Stream.of(conditionBuilder.getValues()).collect(Collectors.toList()));
        }
        //sd
        StringBuilder sdSql = new StringBuilder();
        StringBuilder sdCountSql = new StringBuilder();
        if (CollectionUtils.isEmpty(typeAList) || typeAList.contains(Constants.SD)) {

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid);
            if (StringUtils.isNotBlank(shopId)) {
                List<String> list = StringUtil.splitStr(shopId);
                builder.inStrList("shop_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(campaignIds)) {
                List<String> list = StringUtil.splitStr(campaignIds);
                builder.inStrList("campaign_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(groupIds)) {
                List<String> list = StringUtil.splitStr(groupIds);
                builder.inStrList("ad_group_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(name)) {
                builder.like("name", name);
            }
            ConditionBuilder conditionBuilder = builder.build();
            if (StringUtils.isNotBlank(spSql)) {
                sdSql.append(" union all ");
            }
            sdSql.append(" select 'sd' as type,campaign_id, ad_group_id,name,state ,create_time, '' ad_format from   ods_t_amazon_ad_group_sd where ");
            sdSql.append(conditionBuilder.getSql());
            sql.append(sdSql);
            if (StringUtils.isNotBlank(spSql)) {
                sdCountSql.append(" union all ");
            }
            sdCountSql.append(" select campaign_id from ods_t_amazon_ad_group_sd where ");
            sdCountSql.append(conditionBuilder.getSql());
            sqlCount.append(spCountSql);
            argList.addAll(Stream.of(conditionBuilder.getValues()).collect(Collectors.toList()));
        }

        //sb
        StringBuilder sbSql = new StringBuilder();
        StringBuilder sbCountSql = new StringBuilder();
        if (CollectionUtils.isEmpty(typeAList) || typeAList.contains(Constants.SB)) {

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid);
            if (StringUtils.isNotBlank(shopId)) {
                List<String> list = StringUtil.splitStr(shopId);
                builder.inStrList("shop_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(campaignIds)) {
                List<String> list = StringUtil.splitStr(campaignIds);
                builder.inStrList("campaign_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(groupIds)) {
                List<String> list = StringUtil.splitStr(groupIds);
                builder.inStrList("ad_group_id", list.toArray(new String[]{}));
            }
            if (StringUtils.isNotBlank(name)) {
                builder.like("name", name);
            }
            if (StringUtils.isNotBlank(groupType)) {
                builder.and().leftBracket();
                builder.equalToWithoutCheck(LogicType.EPT, "ad_group_type", "");
                builder.isNull(LogicType.OR, "ad_group_type");
                builder.in(LogicType.OR, "ad_group_type", new String[]{groupType});
                builder.rightBracket();
            }
            ConditionBuilder conditionBuilder = builder.build();
            if (StringUtils.isNotBlank(spSql)) {
                sbSql.append(" union all ");
            }
            sbSql.append(" select 'sb' as type,campaign_id, ad_group_id,name,state ,create_time, ad_format from   ods_t_amazon_ad_group_sb where ");
            sbSql.append(conditionBuilder.getSql());
            sql.append(sbSql);
            if (StringUtils.isNotBlank(spSql)) {
                sbCountSql.append(" union all ");
            }
            sbCountSql.append(" select campaign_id from ods_t_amazon_ad_group_sb where ");
            sbCountSql.append(conditionBuilder.getSql());
            sqlCount.append(sbCountSql);
            argList.addAll(Stream.of(conditionBuilder.getValues()).collect(Collectors.toList()));
        }
        selectSql.append(sql);
        selectSql.append(") t ");
        StringBuilder where = new StringBuilder(" where 1 = 1 ");
        // portfolioIds筛选掉-1的情况
        List<String> portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                .filter(item -> !item.equals("-1")).collect(Collectors.toList());
        if ("-1".equals(portfolioId)) {
            where.append(" and portfolio_id is null ");
        } else if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioId.contains("-1")) {
                where.append(" and ( ");
                where.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, argList));
                where.append(" or portfolio_id is null ");
                where.append(" ) ");
            } else {
                where.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, argList));
            }
        }
        PermissionSqlBuilder.addPermissionFilter(where, argList, "campaign_id" ,true);
        selectSql.append(where);
        selectSql.append(" order by sort_id,create_time,ad_group_id  ");
        countSql.append(sqlCount);
        countSql.append(") t ");

        return getPageResultByRowMapper(pageNo, pageSize, countSql.toString(), argList.toArray(), selectSql.toString(), argList.toArray(), (res, i) -> {
                AdGroupOptionVo optionVo = AdGroupOptionVo.builder()
                        .name(res.getString("name"))
                        .groupId(res.getString("ad_group_id"))
                        .campaignId(res.getString("campaign_id"))
                        .type(res.getString("type"))
                        .state(res.getString("state"))
                        .targetingType(res.getString("ad_format"))
                        .build();
                return optionVo;
        });
    }

    @Override
    public Page<MultiShopGroupListVo> getMultiShopGroupList(Integer puid, MultiShopGroupListParam param) {
        StringBuilder sql = new StringBuilder("SELECT shop_id shopId, campaign_id campaignId, ad_group_id adGroupId, name, state, ad_type adType FROM ( ");
        StringBuilder countSql = new StringBuilder("SELECT count(*) FROM ( ");
        List<Object> args = new ArrayList<>();

        List<String> groupSqlList = new ArrayList<>(3);
        if (MapUtils.isEmpty(param.getCampaignIdMap())) {
            if (CollectionUtils.isEmpty(param.getAdTypeList())) {
                param.setAdTypeList(Arrays.asList(CampaignTypeEnum.sp.getCampaignType(),
                        CampaignTypeEnum.sb.getCampaignType(),
                        CampaignTypeEnum.sd.getCampaignType()));
            }
            param.getAdTypeList().forEach(x -> {
                String groupSql = getMultiShopGroupSqlByType(param, x, args);
                if (StringUtils.isNotBlank(groupSql)) {
                    groupSqlList.add(groupSql);
                }
            });
        } else {
            param.getCampaignIdMap().forEach((k, v) -> {
                String groupSql = getMultiShopGroupSqlByType(param, k, args);
                if (StringUtils.isNotBlank(groupSql)) {
                    groupSqlList.add(groupSql);
                }
            });
        }

        if (CollectionUtils.isEmpty(groupSqlList)) {
            return new Page<MultiShopGroupListVo>(param.getPageNo(), param.getPageSize(), 0, 0, Lists.newArrayList());
        }

        sql.append(StringUtils.join(groupSqlList, " union all "));
        countSql.append(StringUtils.join(groupSqlList, " union all "));

        sql.append(" ) t");


        List<CampaignTypeEnum> types = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(param.getAdTypeList())) {
            types = CampaignTypeEnum.campaignTypeEnumList.stream().filter(e -> param.getAdTypeList().contains(e.getCampaignType())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(types)) {
            types = CampaignTypeEnum.campaignTypeEnumList;
        }


        if (Boolean.TRUE.equals(param.getNeedOrder())) {
            sql.append(" order by")
                    .append(" case state")
                    .append(" when 'enabled' then 1")
                    .append(" when 'paused' then 2")
                    .append(" else 3")
                    .append(" end ");
        }

        countSql.append(" ) t");


        String productRightCampaignIdsSqlFromGrpc = adProductRightService.getProductRightCampaignIdsSqlFromGrpc(puid, param.getShopIdList(), types, args, "campaign_id");
        if (StringUtils.isNotBlank(productRightCampaignIdsSqlFromGrpc)) {
            countSql.append(" where  ").append(productRightCampaignIdsSqlFromGrpc);
        }

        if (StringUtils.isNotBlank(productRightCampaignIdsSqlFromGrpc)) {
            sql.append(" where  ").append(productRightCampaignIdsSqlFromGrpc);
        }

        return this.getPageResultByClass(
                param.getPageNo(),
                param.getPageSize(),
                countSql.toString(),
                args.toArray(),
                sql.toString(),
                args.toArray(),
                MultiShopGroupListVo.class);
    }


    private String getMultiShopGroupSqlByType(MultiShopGroupListParam param, String adType, List<Object> args) {
        //sd没有关键词投放，不生成sql
        if (CampaignTypeEnum.sd.getCampaignType().equals(adType) && Constants.GROUP_TYPE_KEYWORD.equals(param.getGroupType())) {
            return "";
        }

        String tableName = getJdbcHelper().getTable();
        if (CampaignTypeEnum.sb.getCampaignType().equals(adType) || CampaignTypeEnum.sd.getCampaignType().equals(adType)) {
            tableName += ("_" + adType);
        }
        StringBuilder sql = new StringBuilder("SELECT shop_id, campaign_id, ad_group_id, name, state, ");
        sql.append("'" + adType + "' as ad_type from ");
        sql.append(tableName);
        sql.append(" where puid = ? ");
        args.add(param.getPuid());
        sql.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), args));

        if (CollectionUtils.isNotEmpty(param.getMarketplaceId())) {
            sql.append(SqlStringUtil.dealInList("marketplace_id", param.getMarketplaceId(), args));
        }

        if (MapUtils.isNotEmpty(param.getCampaignIdMap())) {
            if (CollectionUtils.isNotEmpty(param.getCampaignIdMap().get(adType))) {
                sql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdMap().get(adType), args));
            }
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIdList())) {
            sql.append(SqlStringUtil.dealInList("ad_group_id", param.getGroupIdList(), args));
        }

        if (CollectionUtils.isNotEmpty(param.getStateList())) {
            sql.append(SqlStringUtil.dealInList("state", param.getStateList(), args));
        }

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            if ("exact".equals(param.getSearchType())) {
                sql.append(" and name = ? ");
                args.add(param.getSearchValue().trim());
            }else {
                sql.append(" and name like ? ");
                args.add("%" + param.getSearchValue().trim() + "%");
            }
        }
        if (StringUtils.isNotBlank(param.getAdGroupType()) && !CampaignTypeEnum.sd.getCampaignType().equals(adType)) {
            sql.append(" and (ad_group_type = ? or ad_group_type is null or ad_group_type = '' )");
            args.add(param.getAdGroupType());
        }

        if (CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            sql.append(SqlStringUtil.dealInList("name", param.getSearchValueList(), args));
        }

        //广告组类型
        if (StringUtils.isNotBlank(param.getGroupType())) {
            if (CampaignTypeEnum.sp.getCampaignType().equals(adType)) {
                List<String> spTypeList = Lists.newArrayList(Constants.GROUP_TYPE_AUTO, param.getGroupType());
                sql.append(" and (ad_group_type = '' or ad_group_type is null or ");
                sql.append(SqlStringUtil.dealInListNotAnd("ad_group_type", spTypeList, args));
                sql.append(" ) ");
            } else if (CampaignTypeEnum.sb.getCampaignType().equals(adType)) {
                String sbAdgroupType = Constants.GROUP_TYPE_TARGETING.equals(param.getGroupType()) ? Constants.GROUP_TYPE_PRODUCT : param.getGroupType();
                sql.append(" and (ad_group_type = '' or ad_group_type is null or ad_group_type = ? ) ");
                args.add(sbAdgroupType);
            }
        }
        return sql.toString();
    }


}
