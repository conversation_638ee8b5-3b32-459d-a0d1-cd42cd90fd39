package com.meiyunji.sponsored.service.multiPlatform.walmart.enums;

import lombok.Getter;

/**
 * @author: ys
 * @date: 2025/4/18 17:56
 * @describe:
 */
@Getter
public enum WalmartAdPlacementEnum {
    SEARCH_INGRID("Search Ingrid", "网页搜索"),
    SEARCH_CAROUSEL("Search Carousel", "搜索轮播位"),
    ITEM_BUYBOX("Item BuyBox", "商品详情页"),
    ITEM_CAROUSEL("Item Carousel", "商品详情轮播位"),

    ;
    private String code;
    private String msg;

    WalmartAdPlacementEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static WalmartAdPlacementEnum getWalmartAdPlacementEnumByCode (String code) {
        for (WalmartAdPlacementEnum en : WalmartAdPlacementEnum.values()) {
            if (en.getCode().equalsIgnoreCase(code)) {
                return en;
            }
        }
        return null;
    }
}
