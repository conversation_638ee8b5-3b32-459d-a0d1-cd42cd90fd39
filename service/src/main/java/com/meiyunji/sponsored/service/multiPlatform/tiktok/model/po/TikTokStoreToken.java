package com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po;

import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2025-05-19  10:50
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@DbTable(value = "t_tiktok_store_token")
public class TikTokStoreToken extends BasePo {
    @DbColumn(value = "id", autoIncrement = true, key = true)
    private Long id;

    /**
     * 用户puid
     */
    @DbColumn(value = "puid")
    private Integer puid;

    /**
     * 店铺id
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;

    /**
     * 访问令牌
     */
    @DbColumn(value = "access_token")
    private String accessToken;
}
