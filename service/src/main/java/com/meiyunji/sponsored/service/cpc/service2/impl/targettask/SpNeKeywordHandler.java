package com.meiyunji.sponsored.service.cpc.service2.impl.targettask;

import com.amazon.advertising.spV3.campaign.CampaignNegativeKeywordSpV3Client;
import com.amazon.advertising.spV3.campaign.CreateSpCampaignNegativeKeywordV3Response;
import com.amazon.advertising.spV3.campaign.entity.CampaignNegativeKeywordApiResponseV3;
import com.amazon.advertising.spV3.campaign.entity.CampaignNegativeKeywordEntityV3;
import com.amazon.advertising.spV3.campaign.entity.CampaignNegativeKeywordSuccessResultV3;
import com.amazon.advertising.spV3.negativekeyword.CreateSpNegativeKeywordV3Response;
import com.amazon.advertising.spV3.negativekeyword.NegativeKeywordSpV3Client;
import com.amazon.advertising.spV3.negativekeyword.entity.CreateNegativeKeywordEntityV3;
import com.amazon.advertising.spV3.negativekeyword.entity.NegativeKeywordApiResponseV3;
import com.amazon.advertising.spV3.negativekeyword.entity.NegativeKeywordSuccessResultV3;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.rpc.sp.neKeyword.NeKeywordResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskConstant;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskStatusEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignNeKeywordsDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdNeKeywordDao;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcNeKeywordsService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcSpCampaignService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcNeKeywordsApiService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-05-08 19:21
 */
@Service(AdTargetTaskConstant.SP_NE_KEYWORD_HANDLER)
@Slf4j
public class SpNeKeywordHandler implements TargetTaskHandler {

    @Autowired
    private TargetTaskComponent targetTaskComponent;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private CpcNeKeywordsApiService cpcNeKeywordsApiService;
    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private ICpcNeKeywordsService cpcNeKeywordsService;
    @Autowired
    private ICpcSpCampaignService cpcSpCampaignService;
    @Autowired
    private IAmazonAdNeKeywordDao amazonAdNeKeywordDao;
    @Autowired
    private IAmazonAdCampaignNeKeywordsDao amazonAdCampaignNeKeywordsDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Override
    public void handle(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        //处理业务返回结果
        addNeKeywords(adTargetTask, adTargetTaskDetails);
    }

    private void addNeKeywords(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        List<AdTargetTaskDetail> needUpdateDetailList = new ArrayList<>();
        if (CollectionUtils.isEmpty(adTargetTaskDetails)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        // 排除已存在的关键词
        Set<String> camapginIdSet = new HashSet<>();
        Set<String> adGroupIdSet = new HashSet<>();
        Set<String> keywordTextSet = new HashSet<>();
        Set<String> matchTypeSet = new HashSet<>();
        Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap = new HashMap<>();
        for (AdTargetTaskDetail targetTaskDetail : adTargetTaskDetails) {
            camapginIdSet.add(targetTaskDetail.getAdCampaignId());
            Optional.ofNullable(targetTaskDetail.getAdGroupId()).filter(StringUtils::isNotBlank).ifPresent(adGroupIdSet::add);
            keywordTextSet.add(targetTaskDetail.getTargetObject().trim());
            matchTypeSet.add(targetTaskDetail.getMatchType());
            adTargetTaskDetailMap.put(targetTaskDetail.getId(), targetTaskDetail);
        }
        int originalTaskDetailSize = adTargetTaskDetails.size();

        //如果groupId为空，代表这批量添加的是广告活动层级的关键词否定
        String type;
        if (CollectionUtils.isNotEmpty(adGroupIdSet)) {
            type = Constants.NEGATIVE;
            checkGroupNeKeywordExist(adTargetTask, adTargetTaskDetails, adGroupIdSet, keywordTextSet, matchTypeSet, needUpdateDetailList);
        } else {
            type = Constants.CAMPAIGN_NEGATIVE;
            checkCampaignNeKeywordExist(adTargetTask, adTargetTaskDetails, camapginIdSet, keywordTextSet, matchTypeSet, needUpdateDetailList);
        }

        if (CollectionUtils.isEmpty(adTargetTaskDetails)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        List<AmazonAdKeyword> amazonAdKeywords = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(adGroupIdSet)) {
            List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.getAdGroupByIds(adTargetTask.getPuid(), adTargetTask.getShopId(), new ArrayList<>(adGroupIdSet));
            Map<String, AmazonAdGroup> amazonAdGroupMap = amazonAdGroups.stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity(), (newVal, oldVal) -> newVal));
            amazonAdKeywords = convertAddNeKeywordsVoToPo(adTargetTask.getUid(), amazonAdGroupMap, adTargetTaskDetails, needUpdateDetailList);
        } else if (CollectionUtils.isNotEmpty(camapginIdSet)) {
            List<AmazonAdCampaignAll> campaignList = amazonAdCampaignAllDao.listByCampaignIdNoType(adTargetTask.getPuid(), adTargetTask.getShopId(), new ArrayList<>(camapginIdSet));
            Map<String, AmazonAdCampaignAll> camapginMap = campaignList.parallelStream()
                    .collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (newVal, oldVal) -> newVal));
            amazonAdKeywords = convertAddNeKeywordsVoToPoWithCampaign(adTargetTask.getUid(), camapginMap, adTargetTaskDetails, needUpdateDetailList);
        }

        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(adTargetTask.getShopId(), adTargetTask.getPuid());
        if (shop == null) {
            adTargetTaskDetails.forEach(each -> {
                each.setFailureReason("店铺不存在");
                each.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                needUpdateDetailList.add(each);
            });
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        List<List<AmazonAdKeyword>> amazonAdKeywordPartition = Lists.partition(amazonAdKeywords, AdTargetTaskConstant.MAX_SP_TARGET_SIZE);
        int failureNum = 0;
        for (List<AmazonAdKeyword> amazonAdKeywordList : amazonAdKeywordPartition) {
            Result result;
            if (CollectionUtils.isNotEmpty(adGroupIdSet)) {
                result = createNegativeKeywordV3(amazonAdKeywordList, adTargetTaskDetailMap, shop, adTargetTask);
            } else {
                //调用广告活动层级创建关键词否定投放
                result = createCampaignNegativeKeyword(amazonAdKeywordList, adTargetTaskDetailMap, shop, adTargetTask);
            }
            List<AdManageOperationLog> operationLogs = Lists.newArrayListWithExpectedSize(2);
            List<NeKeywordResponse.Data> errorList = new ArrayList<>();
            for (AmazonAdKeyword keyword : amazonAdKeywordList) {
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(keyword.getTargetTaskDetailId());
                needUpdateDetailList.add(adTargetTaskDetail);
                if (AdTargetTaskStatusEnum.FAILURE.getCode() == adTargetTaskDetail.getStatus()) {
                    failureNum++;
                }
                AdManageOperationLog operationLog = adManageOperationLogService.getkeywordsLog(null, keyword);
                operationLog.setIp(adTargetTask.getLoginIp());
                if (StringUtils.isNotBlank(keyword.getKeywordId())) {
                    operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    String errMsg = "";
                    if (StringUtils.isNotBlank(keyword.getError())) {
                        errMsg = "targetValue:" + keyword.getKeywordText() + ",desc:" + keyword.getError();
                    } else {
                        errMsg = result.getMsg();
                    }
                    NeKeywordResponse.Data.Builder builder = NeKeywordResponse.Data.newBuilder();
                    builder.setKeywordText(keyword.getKeywordText());
                    builder.setMatchType(keyword.getMatchType());
                    errorList.add(builder.build());
                    operationLog.setResultInfo(errMsg);
                }
                operationLogs.add(operationLog);
            }

            adManageOperationLogService.batchLogsMergeByAdGroup(operationLogs);

            if (result.success()) {
                amazonAdKeywordList = amazonAdKeywordList.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId())).collect(Collectors.toList());
                if (Constants.NEGATIVE.equalsIgnoreCase(type)) {
                    List<AmazonAdNeKeyword> amazonAdNeKeywords = amazonAdKeywordList
                            .stream().map(e -> amazonAdKeywordDaoRoutingService.converToNe(e)).collect(Collectors.toList());
                    amazonAdNeKeywordDao.insertOnDuplicateKeyUpdate(adTargetTask.getPuid(), amazonAdNeKeywords);
                    AmazonAdKeyword keyword = amazonAdKeywordList.get(0);
                    List<String> keywordIds = amazonAdKeywordList.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId())).map(AmazonAdKeyword::getKeywordId).collect(Collectors.toList());
                    cpcNeKeywordsService.saveDoris(keyword.getPuid(), keyword.getShopId(), keywordIds, true);
                } else {
                    //批量插入广告活动层级否定
                    List<AmazonAdCampaignNeKeywords> campaignNeKeywordList = convertBatchAddNeKeywordsVoToPo(amazonAdKeywordList);
                    amazonAdCampaignNeKeywordsDao.insertOnDuplicateKeyUpdate(adTargetTask.getPuid(), campaignNeKeywordList);
                    cpcSpCampaignService.saveCampaignNekeywordDoris(campaignNeKeywordList, true, false);
                }
            }
            targetTaskComponent.updateTaskDetailStatus(adTargetTask, needUpdateDetailList);
            needUpdateDetailList.clear();
            failureNum += originalTaskDetailSize - amazonAdKeywords.size();
            int adTargetTaskStatus = targetTaskComponent.getAdTargetTaskStatus(originalTaskDetailSize, failureNum);
            targetTaskComponent.updateTaskStatus(adTargetTask, adTargetTaskStatus);
        }
    }

    private Result createNegativeKeywordV3(List<AmazonAdKeyword> amazonAdKeywords, Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap, ShopAuth shop, AdTargetTask adTargetTask) {
        AmazonAdKeyword one = amazonAdKeywords.get(0);

        List<CreateNegativeKeywordEntityV3> keywords = cpcNeKeywordsApiService.makeCreateNegativeKeywordsV3(amazonAdKeywords);
        CreateSpNegativeKeywordV3Response response = NegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createNegativeKeywords(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), keywords, Boolean.TRUE);
        if (response != null
                && response.getStatusCode() != null
                && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = NegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createNegativeKeywords(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), keywords, Boolean.TRUE);
        }
        if (response == null) {
            for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywords) {
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
                adTargetTaskDetail.setFailureReason("网络延迟，请稍后重试");
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
            }
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        if (response.getData() != null) {
            boolean hasSucc = false;
            StringBuilder error = new StringBuilder();
            NegativeKeywordApiResponseV3 data = response.getData();
            if (CollectionUtils.isNotEmpty(data.getNegativeKeywords().getSuccess())) {
                for (NegativeKeywordSuccessResultV3 successResultV3 : data.getNegativeKeywords().getSuccess()) {
                    AmazonAdKeyword amazonAdKeyword = amazonAdKeywords.get(successResultV3.getIndex());
                    amazonAdKeyword.setKeywordId(successResultV3.getNegativeKeywordId());
                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.SUCCESS.getCode());
                }

                hasSucc = true;
            }

            if (CollectionUtils.isNotEmpty(data.getNegativeKeywords().getError())) {
                for (ErrorItemResultV3 errorItemResultV3 : data.getNegativeKeywords().getError()) {
                    error.append("targetValue:").append(amazonAdKeywords.get(errorItemResultV3.getIndex()).getKeywordText()).append(",desc:").append(AmazonErrorUtils.getError(errorItemResultV3.getErrors().get(0).getErrorMessage())).append(";");
                    AmazonAdKeyword amazonAdKeyword = amazonAdKeywords.get(errorItemResultV3.getIndex());
                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
                    String[] errorMsgPartition = targetTaskComponent.getNeKeywordErrorMsgPartition(adTargetTask, adTargetTaskDetail);
                    String errorMessage = AmazonErrorUtils.getErrorWithPlaceHolder(errorItemResultV3.getErrors().get(0).getErrorMessage(), adTargetTaskDetail.getTargetObject(), errorMsgPartition);

                    amazonAdKeyword.setError(errorMessage);
                    adTargetTaskDetail.setFailureReason(targetTaskComponent.getError(errorMessage, errorItemResultV3.getErrors().get(0).getErrorMessage()));
                    adTargetTaskDetail.setFailureReasonDetail(errorItemResultV3.getErrors().get(0).getErrorMessage());
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                }
            }
            if (hasSucc) {
                return ResultUtil.success(error.toString());
            }

            return ResultUtil.error(error.toString());
        }

        String msg = "网络延迟，请稍后重试";
        String formatMsg = msg;
        if (response.getError() != null) {
            msg = AmazonErrorUtils.getError(response.getError().getMessage());
            formatMsg = targetTaskComponent.getError(msg, response.getError().getMessage());
        }
        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywords) {
            AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
            adTargetTaskDetail.setFailureReason(formatMsg);
            adTargetTaskDetail.setFailureReasonDetail(msg);
            adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
        }
        return ResultUtil.error(msg);

    }

    private Result createCampaignNegativeKeyword(List<AmazonAdKeyword> amazonAdKeywords, Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap, ShopAuth shop, AdTargetTask adTargetTask) {
        AmazonAdKeyword one = amazonAdKeywords.get(0);

        List<CampaignNegativeKeywordEntityV3> keywords = cpcNeKeywordsApiService.makeCampaignNegativeKeywordList(amazonAdKeywords);
        CreateSpCampaignNegativeKeywordV3Response response = CampaignNegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createCampaignNegativeKeyword(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), keywords,true);
        if (response != null
                && response.getStatusCode() != null
                && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = CampaignNegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createCampaignNegativeKeyword(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), keywords,true);
        }
        if (response == null) {
            for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywords) {
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
                adTargetTaskDetail.setFailureReason("网络延迟，请稍后重试");
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
            }
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        if (response.getData() != null) {
            boolean hasSucc = false;
            StringBuilder error = new StringBuilder();
            CampaignNegativeKeywordApiResponseV3 data = response.getData();
            if (CollectionUtils.isNotEmpty(data.getCampaignNegativeKeywords().getSuccess())) {
                for (CampaignNegativeKeywordSuccessResultV3 successResultV3 : data.getCampaignNegativeKeywords().getSuccess()) {
                    AmazonAdKeyword amazonAdKeyword = amazonAdKeywords.get(successResultV3.getIndex());
                    amazonAdKeyword.setKeywordId(successResultV3.getCampaignNegativeKeywordId());
                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.SUCCESS.getCode());
                }

                hasSucc = true;
            }

            if (CollectionUtils.isNotEmpty(data.getCampaignNegativeKeywords().getError())) {
                for (ErrorItemResultV3 errorItemResultV3 : data.getCampaignNegativeKeywords().getError()) {
                    error.append("targetValue:").append(amazonAdKeywords.get(errorItemResultV3.getIndex()).getKeywordText()).append(",desc:").append(AmazonErrorUtils.getError(errorItemResultV3.getErrors().get(0).getErrorMessage())).append(";");
                    AmazonAdKeyword amazonAdKeyword = amazonAdKeywords.get(errorItemResultV3.getIndex());
                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
                    String[] errorMsgPartition = targetTaskComponent.getNeKeywordErrorMsgPartition(adTargetTask, adTargetTaskDetail);
                    String errorMessage = AmazonErrorUtils.getErrorWithPlaceHolder(errorItemResultV3.getErrors().get(0).getErrorMessage(), adTargetTaskDetail.getTargetObject(), errorMsgPartition);

                    amazonAdKeyword.setError(errorMessage);
                    adTargetTaskDetail.setFailureReason(targetTaskComponent.getError(errorMessage, errorItemResultV3.getErrors().get(0).getErrorMessage()));
                    adTargetTaskDetail.setFailureReasonDetail(errorItemResultV3.getErrors().get(0).getErrorMessage());
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                }
            }
            if (hasSucc) {
                return ResultUtil.success(error.toString());
            }

            return ResultUtil.error(error.toString());
        }

        String msg = "网络延迟，请稍后重试";
        String formatMsg = msg;
        if (response.getError() != null) {
            msg = AmazonErrorUtils.getError(response.getError().getMessage());
            formatMsg = targetTaskComponent.getError(msg, response.getError().getMessage());
        }
        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywords) {
            AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
            adTargetTaskDetail.setFailureReason(formatMsg);
            adTargetTaskDetail.setFailureReasonDetail(msg);
            adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
        }
        return ResultUtil.error(msg);

    }

    private List<AmazonAdKeyword> convertAddNeKeywordsVoToPo(Integer uid, Map<String, AmazonAdGroup> amazonAdGroupMap, List<AdTargetTaskDetail> adTargetTaskDetails, List<AdTargetTaskDetail> needUpdateDetailList) {
        List<AmazonAdKeyword> amazonAdKeywords = new ArrayList<>(adTargetTaskDetails.size());
        AmazonAdKeyword amazonAdKeyword;
        Iterator<AdTargetTaskDetail> it = adTargetTaskDetails.iterator();
        AdTargetTaskDetail adTargetTaskDetail;
        while (it.hasNext()) {
            adTargetTaskDetail = it.next();
            AmazonAdGroup amazonAdGroup;
            if ((amazonAdGroup = amazonAdGroupMap.get(adTargetTaskDetail.getAdGroupId()))  == null) {
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                adTargetTaskDetail.setFailureReason("广告组不存在");
                needUpdateDetailList.add(adTargetTaskDetail);
                it.remove();
            } else {
                amazonAdKeyword = new AmazonAdKeyword();
                amazonAdKeyword.setPuid(amazonAdGroup.getPuid());
                amazonAdKeyword.setShopId(amazonAdGroup.getShopId());
                amazonAdKeyword.setMarketplaceId(amazonAdGroup.getMarketplaceId());
                amazonAdKeyword.setProfileId(amazonAdGroup.getProfileId());
                Optional.of(amazonAdGroup).map(AmazonAdGroup::getAdGroupId).ifPresent(amazonAdKeyword::setAdGroupId);
                amazonAdKeyword.setDxmGroupId(amazonAdGroup.getId());
                amazonAdKeyword.setCampaignId(amazonAdGroup.getCampaignId());
                amazonAdKeyword.setKeywordText(adTargetTaskDetail.getTargetObject());
                amazonAdKeyword.setMatchType(adTargetTaskDetail.getMatchType());
                amazonAdKeyword.setType(Constants.NEGATIVE);
                amazonAdKeyword.setState(CpcStatusEnum.enabled.name());
                amazonAdKeyword.setCreateId(uid);
                amazonAdKeyword.setTargetTaskDetailId(adTargetTaskDetail.getId());
                // 否定投放需要creationDate来清洗数据
                amazonAdKeyword.setCreationDate(LocalDateTime.now());
                amazonAdKeywords.add(amazonAdKeyword);
            }
        }
        return amazonAdKeywords;
    }

    private List<AmazonAdKeyword> convertAddNeKeywordsVoToPoWithCampaign(Integer uid, Map<String, AmazonAdCampaignAll> amazonAdCampaignMap,
                                                                         List<AdTargetTaskDetail> adTargetTaskDetails, List<AdTargetTaskDetail> needUpdateDetailList) {
        List<AmazonAdKeyword> amazonAdKeywords = new ArrayList<>(adTargetTaskDetails.size());
        AmazonAdKeyword amazonAdKeyword;
        Iterator<AdTargetTaskDetail> it = adTargetTaskDetails.iterator();
        AdTargetTaskDetail adTargetTaskDetail;
        while (it.hasNext()) {
            adTargetTaskDetail = it.next();
            AmazonAdCampaignAll amazonAdCampaign;
            if ((amazonAdCampaign = amazonAdCampaignMap.get(adTargetTaskDetail.getAdCampaignId()))  == null) {
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                adTargetTaskDetail.setFailureReason("广告活动不存在");
                needUpdateDetailList.add(adTargetTaskDetail);
                it.remove();
            } else {
                amazonAdKeyword = new AmazonAdKeyword();
                amazonAdKeyword.setPuid(amazonAdCampaign.getPuid());
                amazonAdKeyword.setShopId(amazonAdCampaign.getShopId());
                amazonAdKeyword.setMarketplaceId(amazonAdCampaign.getMarketplaceId());
                amazonAdKeyword.setProfileId(amazonAdCampaign.getProfileId());
                amazonAdKeyword.setCampaignId(amazonAdCampaign.getCampaignId());
                amazonAdKeyword.setKeywordText(adTargetTaskDetail.getTargetObject());
                amazonAdKeyword.setMatchType(adTargetTaskDetail.getMatchType());
                amazonAdKeyword.setType(Constants.CAMPAIGN_NEGATIVE);
                amazonAdKeyword.setState(CpcStatusEnum.enabled.name());
                amazonAdKeyword.setCreateId(uid);
                amazonAdKeyword.setTargetTaskDetailId(adTargetTaskDetail.getId());
                // 否定投放需要creationDate来清洗数据
                amazonAdKeyword.setCreationDate(LocalDateTime.now());
                amazonAdKeywords.add(amazonAdKeyword);
            }
        }
        return amazonAdKeywords;
    }

    private void checkCampaignNeKeywordExist(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails,
                                          Set<String> campaignIdSet, Set<String> keywordTextSet,
                                          Set<String> matchTypeSet, List<AdTargetTaskDetail> needUpdateDetailList) {
        List<AmazonAdCampaignNeKeywords> repeatedAmazonAdKeywords = amazonAdKeywordDaoRoutingService.getCampaignNeListByTargetCondition(
                adTargetTask.getPuid(), adTargetTask.getShopId(), campaignIdSet, keywordTextSet, matchTypeSet);
        Map<String, AmazonAdCampaignNeKeywords> amazonAdKeywordMap = repeatedAmazonAdKeywords.stream()
                .collect(Collectors.toMap(each -> String.join("-", each.getCampaignId(), each.getKeywordText(), each.getMatchType()), Function.identity(), (newVal, oldVal) -> newVal));

        Iterator<AdTargetTaskDetail> it = adTargetTaskDetails.iterator();
        AdTargetTaskDetail next;
        while (it.hasNext()) {
            next = it.next();
            if (amazonAdKeywordMap.containsKey(String.join("-", next.getAdCampaignId(), next.getTargetObject().trim(), next.getMatchType()))) {
                if (next.getStatus() <= AdTargetTaskStatusEnum.FAILURE.getCode()) {
                    next.setFailureReason("历史已存在相同的投放，请检查");
                    next.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                    needUpdateDetailList.add(next);
                }
                it.remove();
            }
        }

    }

    private void checkGroupNeKeywordExist(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails,
                                   Set<String> adGroupIdSet, Set<String> keywordTextSet,
                                   Set<String> matchTypeSet, List<AdTargetTaskDetail> needUpdateDetailList) {
        List<AmazonAdNeKeyword> repeatedAmazonAdKeywords = amazonAdKeywordDaoRoutingService.getNeListByTargetTaskCondition(
                adTargetTask.getPuid(), adTargetTask.getShopId(), adGroupIdSet, keywordTextSet, matchTypeSet);
        Map<String, AmazonAdNeKeyword> amazonAdKeywordMap = repeatedAmazonAdKeywords.stream()
                .collect(Collectors.toMap(each -> String.join("-", each.getAdGroupId(), each.getKeywordText(), each.getMatchType()), Function.identity(), (newVal, oldVal) -> newVal));

        Iterator<AdTargetTaskDetail> it = adTargetTaskDetails.iterator();
        AdTargetTaskDetail next;
        while (it.hasNext()) {
            next = it.next();
            if (amazonAdKeywordMap.containsKey(String.join("-", next.getAdGroupId(), next.getTargetObject().trim(), next.getMatchType()))) {
                if (next.getStatus() <= AdTargetTaskStatusEnum.FAILURE.getCode()) {
                    next.setFailureReason("历史已存在相同的投放，请检查");
                    next.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                    needUpdateDetailList.add(next);
                }
                it.remove();
            }
        }
    }

    private List<AmazonAdCampaignNeKeywords> convertBatchAddNeKeywordsVoToPo(List<AmazonAdKeyword> neKeywords) {
        List<AmazonAdCampaignNeKeywords> amazonAdKeywords = new ArrayList<>(neKeywords.size());
        AmazonAdCampaignNeKeywords amazonAdKeyword;
        for (AmazonAdKeyword vo : neKeywords) {
            amazonAdKeyword = new AmazonAdCampaignNeKeywords();
            amazonAdKeyword.setPuid(vo.getPuid());
            amazonAdKeyword.setShopId(vo.getShopId());
            amazonAdKeyword.setMarketplaceId(vo.getMarketplaceId());
            amazonAdKeyword.setProfileId(vo.getProfileId());
            amazonAdKeyword.setCampaignId(vo.getCampaignId());
            if (StringUtils.isNotBlank(vo.getKeywordId())) {
                amazonAdKeyword.setKeywordId(vo.getKeywordId());
            }
            amazonAdKeyword.setKeywordText(vo.getKeywordText());
            amazonAdKeyword.setMatchType(vo.getMatchType());
            amazonAdKeyword.setState(vo.getState());
            amazonAdKeyword.setCreateId(vo.getCreateId());
            amazonAdKeyword.setServingStatus(vo.getServingStatus());
            amazonAdKeyword.setCreationDate(vo.getCreationDate());
            amazonAdKeywords.add(amazonAdKeyword);
        }
        return amazonAdKeywords;
    }
}
