package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportFilterDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by xp on 2021/4/14.
 * 广告活动下的否定关键词列表页入参
 */
@Data
@ApiModel
public class CampaignNeKeywordsPageParam {

    private Integer pageNo;
    private Integer pageSize;
    private Integer uid;
    private String uuid;
    @ApiModelProperty(value = "店铺ID",required = true)
    private Integer shopId;
    @ApiModelProperty(value = "puid",required = true)
    private Integer puid;
    @ApiModelProperty("广告活动ID 不传则查询所有")
    private String campaignId;

    @ApiModelProperty("搜索字段,目前只支持 name")
    private String searchField;

    @ApiModelProperty("搜索字段值")
    private String searchValue;

    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("操作状态")
    private String state;
    @ApiModelProperty("服务状态")
    private String servingStatus;
    @ApiModelProperty("匹配类型")
    private String matchType;

    private String startDate;
    private String endDate;

    @ApiModelProperty("广告组合ID")
    private String portfolioId;
    @ApiModelProperty(value = "广告组合下的活动id")
    private List<String> campaignIdList;
    private String searchType;

    /**
     * 否定投放报告筛选dto
     */
    private NeTargetReportFilterDto neTargetReportFilterDto;

    //关键词投放-》增加批量搜索
    public List<String> getListSearchValue(){
        if(StringUtils.isNotBlank(this.searchValue)){
            return com.meiyunji.sponsored.common.util.StringUtil.splitStr(this.searchValue.trim(),"%±%");
        }
        return new ArrayList<>();
    }

}
