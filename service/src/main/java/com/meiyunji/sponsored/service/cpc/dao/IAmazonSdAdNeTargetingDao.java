package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdNeTargeting;
import com.meiyunji.sponsored.service.cpc.vo.TargetingPageParam;

import java.util.List;

/**
 * <AUTHOR> on 2021/7/6
 */
public interface IAmazonSdAdNeTargetingDao extends IBaseShardingDao<AmazonSdAdNeTargeting> {

    void batchAdd(int puid, List<AmazonSdAdNeTargeting> list);

    void batchUpdate(int puid, List<AmazonSdAdNeTargeting> list);

    List<AmazonSdAdNeTargeting> listByTargetId(int puid, int shopId, List<String> targetIds);

    List<AmazonSdAdNeTargeting> listByTargetId(int puid, List<Integer> shopIds, List<String> targetIds);

    AmazonSdAdNeTargeting getbyTargetId(Integer puid, Integer shopId, String targetId);

    Page<AmazonSdAdNeTargeting> pageList(int puid, TargetingPageParam param);

    List<AmazonSdAdNeTargeting> listNoAsinImage(Integer puid, Integer shopId, long offset, int limit);

    void batchSetAsinImage(Integer puid, List<AmazonSdAdNeTargeting> needUpdateList);

    List<AmazonSdAdNeTargeting> getListTargetByQuery(Integer puid, Integer shopId, String adGroupId, String targetValue, String type);

}