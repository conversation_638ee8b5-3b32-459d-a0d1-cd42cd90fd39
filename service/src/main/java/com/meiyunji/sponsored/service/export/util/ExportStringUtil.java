package com.meiyunji.sponsored.service.export.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-07-18  16:41
 */
@Slf4j
public class ExportStringUtil {
    /**
     * 保留原数据，无数据为空
     */
    public static String getSuggest(String obj, String icon) {
        if (obj != null) {
            return icon + obj;
        } else {
            return " ";
        }
    }

    /**
     * 拼接竞价范围
     */
    public static String getSuggestBidScope(String rangeStart, String rangeEnd, String icon) {
        if (rangeStart != null && rangeEnd != null && icon != null) {
            return icon + rangeStart + "~" + icon + rangeEnd;
        } else {
            return null;
        }
    }

    /**
     * 保留两位小数+%
     */
    public static String modifyFormat(String obj) {
        if (StringUtils.isNotBlank(obj) && !"-".equals(obj)) {
            BigDecimal bd = new BigDecimal(obj);
            DecimalFormat df = new DecimalFormat("#0.00");
            return df.format(bd) + "%";
        } else {
            return null;
        }
    }

    public static String modifyFormat(BigDecimal obj) {
        if (obj != null) {
            DecimalFormat df = new DecimalFormat("#0.00");
            return df.format(obj) + "%";
        } else {
            return null;
        }
    }

    /**
     * 保留两位小数+% 默认返回0
     */
    public static String modifyFormatDefaultZero(String obj) {
        if (StringUtils.isNotBlank(obj)) {
            return modifyFormat(obj);
        } else {
            return modifyFormat("0.00");
        }
    }

    /**
     * 保留两位小数
     */
    public static String formatToNumber(String obj) {
        if (StringUtils.isNotBlank(obj)) {
            BigDecimal bd = new BigDecimal(obj);
            DecimalFormat df = new DecimalFormat("#.00");
            if (bd.compareTo(BigDecimal.ZERO) > 0 && bd.compareTo(new BigDecimal(1)) < 0) {
                return "0" + df.format(bd);
            } else if (bd.compareTo(BigDecimal.ZERO) == 0) {
                return "0.00";
            } else {
                return df.format(bd);
            }
        } else {
            return "0.00";
        }
    }

    /**
     * 保留两位小数，四舍五入
     */
    public static String getAdCostPerClick(String obj) {
        if (StringUtils.isNotBlank(obj)) {
            return BigDecimal.valueOf(Double.parseDouble(obj)).setScale(2, RoundingMode.HALF_UP).toString();
        } else {
            return "0.00";
        }
    }

    /**
     * 字符串数字取整+%
     */
    public static String getPlacementProductPage(String placement) {
        if (StringUtils.isNotBlank(placement)) {
            double placementNum = Double.parseDouble(placement);
            int placementNumType = (int) placementNum;
            return placementNumType + "%";
        } else {
            return "0%";
        }
    }

    /**
     * 字符串数字取整+%
     */
    public static String getPlacementRestOfSearchPage(String placement) {
        if (StringUtils.isNotBlank(placement)) {
            double placementNum = Double.parseDouble(placement);
            int placementNumType = (int) placementNum;
            return placementNumType + "%";
        } else {
            return "0%";
        }
    }

    public static String getDateState(String endDate) {
        if (endDate != null) {
            return endDate;
        } else {
            return "无结束日期";
        }
    }

    public static String parseTopImpressionShare(String input) {
        if (StringUtils.isBlank(input)) {
            return "";
        }
        try {
            if ("-".equals(input)) {
                return "-";
            } else if ("0".equals(input)) {
                return "0%";
            } else if (input.contains("~")) {
                String[] parts = input.split("~");
                BigDecimal min = new BigDecimal(parts[0]).setScale(2, RoundingMode.HALF_UP);
                BigDecimal max = new BigDecimal(parts[1]).setScale(2, RoundingMode.HALF_UP);
                if (min.compareTo(max) == 0) {
                    return parts[0] + "%";
                } else {
                    return parts[0] + "%" + "~" + parts[1] + "%";
                }
            }
            return input;
        } catch (Exception e) {
            log.error("format export max value error", e);
        }
        return "";
    }

}
