package com.meiyunji.sponsored.service.taskGrpcApi.strategy.impl;

import com.meiyunji.sellfox.aadas.api.enumeration.ScheduleModePb;
import com.meiyunji.sellfox.aadas.api.service.*;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sellfox.aadas.types.enumeration.TaskTimeType;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.impl.ShopAuthServiceImpl;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTemplateDao;
import com.meiyunji.sponsored.service.strategy.enums.AdStrategyEnableStatusEnum;
import com.meiyunji.sponsored.service.strategy.enums.AdStrategyType;
import com.meiyunji.sponsored.service.strategy.enums.PolicyTypeEnum;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategySchedule;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.taskGrpcApi.AbstractAdvertiseStrategyApi;
import com.meiyunji.sponsored.service.util.GrpcExceptionUtil;
import com.meiyunji.sponsored.service.util.PbUtil;
import com.meiyunji.sponsored.service.util.ProtoBufUtil;
import io.grpc.ManagedChannel;
import io.grpc.StatusRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.Producer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class PortfolioHourStrategyApi extends AbstractAdvertiseStrategyApi {

    @Autowired
    private AdvertiseStrategyTemplateDao advertiseStrategyTemplateDao;

    protected PortfolioHourStrategyApi(ShopAuthServiceImpl shopAuthService, ManagedChannel taskManagedChannel, Producer<byte[]> compensationTaskProducer, DynamicRefreshConfiguration dynamicRefreshConfiguration) {
        super(shopAuthService, taskManagedChannel, compensationTaskProducer, dynamicRefreshConfiguration);
    }

    @Override
    public boolean checkValid(TaskTimeType taskType) {
        return taskType == TaskTimeType.portfolioHourAmount;
    }

    @Override
    public void setSchedule(Long taskId, Long templateId, List<AdvertiseStrategySchedule> strategySchedule, boolean triggerNow) throws Exception {
        AdvertiseStrategySchedule schedule = strategySchedule.get(0);
        ShopAuth shopAuth = shopAuthService.getByIdAndPuid(schedule.getShopId(), schedule.getPuid());
        SetPortfolioHourTaskRequestPb.SetPortfolioHourTaskRequest request = builderRequest(taskId, shopAuth, strategySchedule, triggerNow, templateId);
        try {
            log.info("广告组合小时请求参数: {}", request);
            getStub().setPortfolioHourTask(request);
        } catch (StatusRuntimeException e) {
            throw GrpcExceptionUtil.unWrapException(e);
        } finally {
            sendCompensationMessage(taskId, getTaskTimeType(), shopAuth, 5);
        }
    }
    private SetPortfolioHourTaskRequestPb.PortfolioHour.Item build(AdvertiseStrategySchedule advertiseStrategySchedule) {
        SetPortfolioHourTaskRequestPb.PortfolioHour.Item.Builder item = SetPortfolioHourTaskRequestPb.PortfolioHour.Item.newBuilder();
        item.setDay(advertiseStrategySchedule.getDay())
                .setStart(advertiseStrategySchedule.getStart())
                .setEnd(advertiseStrategySchedule.getEnd())
                .setBudget(advertiseStrategySchedule.getNewAmountValue().doubleValue())
                .setPolicyType(PolicyTypeEnum.valueOf(advertiseStrategySchedule.getNewPolicyValue()).getPolicyType());
        return item.build();
    }

    @Override
    public void removeSchedule(Integer puid, Integer shopId, Long taskId, boolean triggerNow) throws Exception {
        if (triggerNow) {
            AdvertiseStrategyTemplate template = advertiseStrategyTemplateDao.getTemplateByTaskId(puid, shopId, taskId);
            if (Objects.isNull(template) || AdStrategyEnableStatusEnum.DISABLED.getCode().equals(template.getStatus())) {
                triggerNow = false;
                log.info("分时策略拦截回调, puid: {}, shopId: {}, taskId: {}", puid, shopId, taskId);
            }
        }
        ShopAuth shopAuth = shopAuthService.getByIdAndPuid(shopId, puid);
        try {
            RemovePortfolioHourTaskRequestPb.RemovePortfolioHourTaskRequest.Builder builder =
                    RemovePortfolioHourTaskRequestPb.RemovePortfolioHourTaskRequest.newBuilder();
            builder.setSellerId(shopAuth.getSellingPartnerId());
            builder.setMarketplace(PbUtil.toPb(Marketplace.fromId(shopAuth.getMarketplaceId())));
            builder.setTaskId(taskId);
            builder.setTriggerNow(triggerNow);
            getStub().removePortfolioHourTask(builder.build());
        } finally {
            sendCompensationMessage(taskId, getTaskTimeType(), shopAuth, 5);
        }
    }

    @Override
    public String builderScheduleRequestJson(Long taskId, List<AdvertiseStrategySchedule> strategySchedule, boolean triggerNow, Long templateId) throws Exception {
        AdvertiseStrategySchedule schedule = strategySchedule.get(0);
        ShopAuth shopAuth = shopAuthService.getByIdAndPuid(schedule.getShopId(), schedule.getPuid());
        return ProtoBufUtil.toJsonStr(builderRequest(taskId, shopAuth, strategySchedule, triggerNow, templateId));
    }

    @Override
    public TaskTimeType getTaskTimeType() {
        return TaskTimeType.portfolioHourAmount;
    }



    public SetPortfolioHourTaskRequestPb.SetPortfolioHourTaskRequest builderRequest(Long taskId, ShopAuth shopAuth, List<AdvertiseStrategySchedule> strategySchedule, boolean triggerNow, Long templateId){
        AdvertiseStrategySchedule schedule = strategySchedule.get(0);
        ScheduleModePb.ScheduleMode scheduleMode = AdStrategyType.valueOf(schedule.getType()).getScheduleMode();
        SetPortfolioHourTaskRequestPb.SetPortfolioHourTaskRequest.Builder list =
                SetPortfolioHourTaskRequestPb.SetPortfolioHourTaskRequest.newBuilder();
        SetPortfolioHourTaskRequestPb.PortfolioHour.Builder builder = SetPortfolioHourTaskRequestPb.PortfolioHour.newBuilder();
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setMarketplace(PbUtil.toPb(Marketplace.fromId(shopAuth.getMarketplaceId())));
        builder.setTaskId(taskId);
        builder.setTemplateId(templateId);
        builder.setSchedulerMode(scheduleMode);
        builder.setPortfolioId(schedule.getItemId());
        builder.setOriginValue(String.valueOf(schedule.getReturnAmountValue()));
        builder.setPolicyType(PolicyTypeEnum.valueOf(schedule.getReturnPolicyValue()).getPolicyType());
        builder.addAllItem(strategySchedule.stream().map(this::build).collect(Collectors.toList()));
        list.addList(builder.build());
        SetPortfolioHourTaskRequestPb.SetPortfolioHourTaskRequest request = list.build();
        return request;
    }
}
