package com.meiyunji.sponsored.service.dataWarehouse.po;

import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.util.CurrencyConversion;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * StatPerformanceShop
 *
 * <AUTHOR>
 */
@DbTable(value = "t_stat_performance_shop")
public class StatPerformanceShop extends BasePo {
    private static final long serialVersionUID = -7658055923076621631L;
    /**
     * 主键
     */
    @DbColumn(value = "id", key = true, autoIncrement = true)
    private Integer id;
    /**
     * puid
     */
    @DbColumn(value = "puid")
    private Integer puid;
    /**
     * shopid
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;
    /**
     * 站点id
     */
    @DbColumn(value = "marketplace_id")
    private String marketplaceId;
    /**
     * 销量  订单产品数
     */
    @DbColumn(value = "product_total_num")
    private Integer productTotalNum;
    /**
     * 订单量
     */
    @DbColumn(value = "order_num")
    private Integer orderNum;
    /**
     * 销售额
     */
    @CurrencyConversion
    @DbColumn(value = "sale_price")
    private BigDecimal salePrice;
    /**
     * 销售额人民币
     */
    @DbColumn(value = "sales_price_CNY")
    private BigDecimal salesPriceCny;
//    /**
//     * 销售额美元
//     */
//    @DbColumn(value = "sales_price_USD")
//    private BigDecimal salesPriceUsd;
    /**
     * 退款量
     */
    @DbColumn(value = "refund_num")
    private Integer refundNum;
    /**
     * 退款率
     */
    @DbColumn(value = "refund_rate")
    private BigDecimal refundRate;
    /**
     * 退货量
     */
    @DbColumn(value = "return_sale_num")
    private Integer returnSaleNum;
    /**
     * 退货率
     */
    @DbColumn(value = "return_sale_rate")
    private BigDecimal returnSaleRate;
    /**
     * 评论数
     */
    @DbColumn(value = "comment")
    private Integer comment;
    /**
     * Sessions
     */
    @DbColumn(value = "sessions")
    private Integer sessions;
    /**
     * PV
     */
    @DbColumn(value = "page_view")
    private Integer pageView;
    /**
     * Conversion Rate（转化率=订单量/流量PV）
     */
    @DbColumn(value = "conversion_rate")
    private BigDecimal conversionRate;
    /**
     * 广告展示 曝光量
     */
    @DbColumn(value = "ad_impressions")
    private Integer adImpressions;
    /**
     * 广告点击
     */
    @DbColumn(value = "ad_clicks")
    private Integer adClicks;
    /**
     * 广告点击率  点击率=点击量/展示量
     */
    @DbColumn(value = "ad_click_rate")
    private BigDecimal adClickRate;
    /**
     * 广告花费 原币种
     */
    @CurrencyConversion
    @DbColumn(value = "ad_cost")
    private BigDecimal adCost;
    /**
     * 广告花费人民币
     */
    @DbColumn(value = "ad_cost_CNY")
    private BigDecimal adCostCny;
//    /**
//     * 广告花费美元
//     */
//    @DbColumn(value = "ad_cost_USD")
//    private BigDecimal adCostUsd;
    /**
     * 广告销售额 原币种
     */
    @CurrencyConversion
    @DbColumn(value = "ad_total_sales")
    private BigDecimal adTotalSales;
    /**
     * 广告销售额人民币
     */
    @DbColumn(value = "ad_total_sales_CNY")
    private BigDecimal adTotalSalesCny;
//    /**
//     * 广告销售额美元
//     */
//    @DbColumn(value = "ad_total_sales_USD")
//    private BigDecimal adTotalSalesUsd;
    /**
     * 每次点击花费=花费/点击量
     */
    @DbColumn(value = "cpc")
    private BigDecimal cpc;
//    /**
//     * cpc人民币
//     */
//    @DbColumn(value = "cpc_CNY")
//    private BigDecimal cpcCny;
//    /**
//     * cpc美元
//     */
//    @DbColumn(value = "cpc_USD")
//    private BigDecimal cpcUsd;
    /**
     * 每次点击花费=花费/订单量
     */
//    @DbColumn(value = "cpa")
    private BigDecimal cpa;
//    /**
//     * cpa人民币
//     */
//    @DbColumn(value = "cpa_CNY")
//    private BigDecimal cpaCny;
//    /**
//     * cpa美元
//     */
//    @DbColumn(value = "cpa_USD")
//    private BigDecimal cpaUsd;
    /**
     * ACOS=广告花费/广告销售额
     */
    @DbColumn(value = "acos")
    private BigDecimal acos;
    /**
     * ACoAS=广告花费/总销售额
     */
    @DbColumn(value = "acoas")
    private BigDecimal acoas;
    /**
     * ASoAS=广告销售额/总销售额
     */
    @DbColumn(value = "asoas")
    private BigDecimal asoas;
    /**
     * 广告订单量
     */
    @DbColumn(value = "ad_order_num")
    private Integer adOrderNum;
    /**
     * 广告 Conversion Rate（广告转化率=广告订单量/点击）
     */
    @DbColumn(value = "ad_conversion_rate")
    private BigDecimal adConversionRate;
    /**
     * 广告订单量占比=广告订单量/总订单量
     */
    @DbColumn(value = "ad_order_rate")
    private BigDecimal adOrderRate;
    /**
     * 当前时间
     */
    @DbColumn(value = "now_date")
    private LocalDate nowDate;
    /**
     * 默认币种
     */
    @DbColumn(value = "currency")
    private String currency;
    /**
     * 备注
     */
    @DbColumn(value = "remark")
    private String remark;

    @DbColumn(value = "has_business")
    private Boolean hasBusiness;

    private String dateStr;

    private Integer fbaAvailable;
    private String shopName;

    public StatPerformanceShop() {
    }

    public StatPerformanceShop(boolean zoerDefault) {
        if (!zoerDefault) return;

        this.productTotalNum = 0;
        this.orderNum = 0;
        this.salePrice = BigDecimal.ZERO;
        this.refundNum = 0;
        this.refundRate = BigDecimal.ZERO;
        this.returnSaleNum = 0;
        this.returnSaleRate =  BigDecimal.ZERO;
        this.fbaAvailable = 0;
        this.comment = 0;
        this.sessions = 0;
        this.pageView = 0;
        this.conversionRate = BigDecimal.ZERO;
        this.adImpressions = 0;
        this.adClicks = 0;
        this.adClickRate = BigDecimal.ZERO;
        this.adCost = BigDecimal.ZERO;
        this.adTotalSales = BigDecimal.ZERO;
        this.cpc = BigDecimal.ZERO;
        this.acos = BigDecimal.ZERO;
        this.acoas = BigDecimal.ZERO;
        this.asoas = BigDecimal.ZERO;
        this.adOrderNum = 0;
        this.adConversionRate = BigDecimal.ZERO;
        this.adOrderRate = BigDecimal.ZERO;
        this.salesPriceCny = BigDecimal.ZERO;
        this.adCostCny = BigDecimal.ZERO;
        this.adTotalSalesCny = BigDecimal.ZERO;
    }

    public void setId(Integer value) {
        this.id = value;
    }

    public Integer getId() {
        return this.id;
    }

    public void setPuid(Integer value) {
        this.puid = value;
    }

    public Integer getPuid() {
        return this.puid;
    }

    public void setShopId(Integer value) {
        this.shopId = value;
    }

    public Integer getShopId() {
        return this.shopId;
    }

    public void setMarketplaceId(String value) {
        this.marketplaceId = value;
    }

    public String getMarketplaceId() {
        return this.marketplaceId;
    }

    public void setProductTotalNum(Integer value) {
        this.productTotalNum = value;
    }

    public Integer getProductTotalNum() {
        return this.productTotalNum;
    }

    public void setOrderNum(Integer value) {
        this.orderNum = value;
    }

    public Integer getOrderNum() {
        return this.orderNum;
    }

    public void setSalePrice(BigDecimal value) {
        this.salePrice = value;
    }

    public BigDecimal getSalePrice() {
        return this.salePrice;
    }

    public void setRefundNum(Integer value) {
        this.refundNum = value;
    }

    public Integer getRefundNum() {
        return this.refundNum;
    }

    public void setRefundRate(BigDecimal value) {
        this.refundRate = value;
    }

    public BigDecimal getRefundRate() {
        if (this.refundNum != null && this.productTotalNum != null) {
            this.refundRate = this.getProductTotalNum() == 0 ? BigDecimal.ZERO :
                    MathUtil.divide(BigDecimal.valueOf(this.refundNum), BigDecimal.valueOf(this.productTotalNum)).multiply(BigDecimal.valueOf(100));
        }
        return this.refundRate;
    }

    public Integer getReturnSaleNum() {
        return returnSaleNum;
    }

    public void setReturnSaleNum(Integer returnSaleNum) {
        this.returnSaleNum = returnSaleNum;
    }

    public BigDecimal getReturnSaleRate() {
        if (this.returnSaleNum != null && this.productTotalNum != null) {
            this.returnSaleRate = this.getProductTotalNum() == 0 ? BigDecimal.ZERO :
                    MathUtil.divide(BigDecimal.valueOf(this.returnSaleNum), BigDecimal.valueOf(this.productTotalNum)).multiply(BigDecimal.valueOf(100));
        }
        return this.returnSaleRate;
    }

    public void setReturnSaleRate(BigDecimal returnSaleRate) {
        this.returnSaleRate = returnSaleRate;
    }

    public void setComment(Integer value) {
        this.comment = value;
    }

    public Integer getComment() {
        return this.comment;
    }

    public void setSessions(Integer value) {
        this.sessions = value;
    }

    public Integer getSessions() {
        return this.sessions;
    }

    public void setPageView(Integer value) {
        this.pageView = value;
    }

    public Integer getPageView() {
        return this.pageView;
    }

    public void setConversionRate(BigDecimal value) {
        this.conversionRate = value;
    }

    public BigDecimal getConversionRate() {

//        if (this.sessions != null) {
//            return this.sessions == 0 ? BigDecimal.ZERO : MathUtil.divide(BigDecimal.valueOf(orderNum), BigDecimal.valueOf(sessions)).multiply(BigDecimal.valueOf(100));
//        }

        return this.conversionRate;
    }

    public void setAdImpressions(Integer value) {
        this.adImpressions = value;
    }

    public Integer getAdImpressions() {
        return this.adImpressions;
    }

    public void setAdClicks(Integer value) {
        this.adClicks = value;
    }

    public Integer getAdClicks() {
        return this.adClicks;
    }

    public void setAdClickRate(BigDecimal value) {
        this.adClickRate = value;
    }

    public BigDecimal getAdClickRate() {
        if (this.adClicks != null && this.adImpressions != null) {
            this.adClickRate = this.adImpressions == 0 ? BigDecimal.ZERO :
                    MathUtil.divide(BigDecimal.valueOf(this.adClicks), BigDecimal.valueOf(this.adImpressions)).multiply(BigDecimal.valueOf(100));
        }
        return this.adClickRate;
    }

    public void setAdCost(BigDecimal value) {
        this.adCost = value;
    }

    public BigDecimal getAdCost() {
        return this.adCost;
    }

    public void setAdTotalSales(BigDecimal value) {
        this.adTotalSales = value;
    }

    public BigDecimal getAdTotalSales() {
        return this.adTotalSales;
    }

    public void setCpc(BigDecimal value) {
        this.cpc = value;
    }

    public BigDecimal getCpc() {
        if (this.adCost != null && this.adClicks != null) {
            this.cpc = this.adClicks == 0 ? BigDecimal.ZERO : MathUtil.divide(adCost, BigDecimal.valueOf(adClicks));
        }
        return this.cpc;
    }

    public void setAcos(BigDecimal value) {
        this.acos = value;
    }

    public BigDecimal getAcos() {
        if (this.adCost != null && this.adTotalSales != null) {
            this.acos = this.adTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(adCost, adTotalSales, 6).multiply(BigDecimal.valueOf(100));
        }
        return this.acos;
    }

    public void setAcoas(BigDecimal value) {
        this.acoas = value;
    }

    public BigDecimal getAcoas() {
        if (this.adCost != null && this.salePrice != null) {
            this.acoas = this.salePrice.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(adCost, salePrice, 6).multiply(BigDecimal.valueOf(100));
        }
        return this.acoas;
    }

    public void setAsoas(BigDecimal value) {
        this.asoas = value;
    }

    public BigDecimal getAsoas() {
        if (this.adTotalSales != null && this.salePrice != null) {
            this.asoas = this.salePrice.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(adTotalSales, salePrice, 6).multiply(BigDecimal.valueOf(100));
        }
        return this.asoas;
    }

    public void setAdOrderNum(Integer value) {
        this.adOrderNum = value;
    }

    public Integer getAdOrderNum() {
        return this.adOrderNum;
    }

    public void setAdConversionRate(BigDecimal value) {
        this.adConversionRate = value;
    }

    public BigDecimal getAdConversionRate() {
        if (this.adOrderNum != null && this.adClicks != null) {
            this.adConversionRate = this.adClicks == 0 ? BigDecimal.ZERO :
                    MathUtil.divide(BigDecimal.valueOf(this.adOrderNum), BigDecimal.valueOf(this.adClicks)).multiply(BigDecimal.valueOf(100));
        }
        return this.adConversionRate;
    }

    public void setAdOrderRate(BigDecimal value) {
        this.adOrderRate = value;
    }

    public BigDecimal getAdOrderRate() {
        if (this.adOrderNum != null && this.orderNum != null) {
            this.adOrderRate = this.orderNum == 0 ? BigDecimal.ZERO :
                    MathUtil.divide(BigDecimal.valueOf(this.adOrderNum), BigDecimal.valueOf(this.orderNum)).multiply(BigDecimal.valueOf(100));
        }
        return this.adOrderRate;
    }

    public void setCurrency(String value) {
        this.currency = value;
    }

    public String getCurrency() {
        return this.currency;
    }

    public void setRemark(String value) {
        this.remark = value;
    }

    public String getRemark() {
        return this.remark;
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public BigDecimal getCpa() {
        if (this.adCost != null && this.orderNum != null) {
            this.cpa = this.orderNum == 0 ? BigDecimal.ZERO : MathUtil.divide(adCost, BigDecimal.valueOf(orderNum));
        }
        return cpa;
    }

    public void setCpa(BigDecimal cpa) {
        this.cpa = cpa;
    }

    public Integer getFbaAvailable() {
        return fbaAvailable;
    }

    public void setFbaAvailable(Integer fbaAvailable) {
        this.fbaAvailable = fbaAvailable;
    }

    public LocalDate getNowDate() {
        return nowDate;
    }

    public void setNowDate(LocalDate nowDate) {
        this.nowDate = nowDate;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Boolean getHasBusiness() {
        return hasBusiness;
    }

    public void setHasBusiness(Boolean hasBusiness) {
        this.hasBusiness = hasBusiness;
    }

    public BigDecimal getSalesPriceCny() {
        return salesPriceCny;
    }

    public void setSalesPriceCny(BigDecimal salesPriceCny) {
        this.salesPriceCny = salesPriceCny;
    }

    public BigDecimal getAdCostCny() {
        return adCostCny;
    }

    public void setAdCostCny(BigDecimal adCostCny) {
        this.adCostCny = adCostCny;
    }

    public BigDecimal getAdTotalSalesCny() {
        return adTotalSalesCny;
    }

    public void setAdTotalSalesCny(BigDecimal adTotalSalesCny) {
        this.adTotalSalesCny = adTotalSalesCny;
    }

    public void merge(StatPerformanceShop obj) {
        if (obj == null) return;

        this.productTotalNum = MathUtil.add(this.productTotalNum, obj.getProductTotalNum());
        this.orderNum = MathUtil.add(this.orderNum, obj.getOrderNum());
        this.salePrice = MathUtil.add(this.salePrice, obj.getSalePrice());
        this.refundNum = MathUtil.add(this.refundNum, obj.getRefundNum());
        this.returnSaleNum =  MathUtil.add(this.returnSaleNum, obj.getReturnSaleNum());
        this.fbaAvailable = MathUtil.add(this.fbaAvailable, obj.getFbaAvailable());
        this.adImpressions = MathUtil.add(this.adImpressions, obj.getAdImpressions());
        this.adClicks = MathUtil.add(this.adClicks, obj.getAdClicks());
        this.adCost = MathUtil.add(this.adCost, obj.getAdCost());
        this.adTotalSales = MathUtil.add(this.adTotalSales, obj.getAdTotalSales());
        this.adOrderNum = MathUtil.add(this.adOrderNum, obj.getAdOrderNum());
        this.sessions = ifNullInt(this.sessions) + ifNullInt(obj.getSessions());
        this.pageView = ifNullInt(this.pageView) + ifNullInt(obj.getPageView());

        this.salesPriceCny = MathUtil.add(this.salesPriceCny, obj.getSalesPriceCny());
        this.adTotalSalesCny = MathUtil.add(this.adTotalSalesCny, obj.getAdTotalSalesCny());
        this.adCostCny = MathUtil.add(this.adCostCny, obj.getAdCostCny());
    }

    private Integer ifNullInt(Integer v) {
        return v == null ? 0 : v;
    }
}