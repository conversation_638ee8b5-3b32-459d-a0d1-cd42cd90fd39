package com.meiyunji.sponsored.service.attribution.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.BaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.attribution.dao.IAmazonAdAttributionPublisherDao;
import com.meiyunji.sponsored.service.attribution.entity.AmazonAdAttributionPublisher;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: wade
 * @date: 2022/3/12 11:55
 * @describe:
 */
@Repository
public class AmazonAdAttributionPublisherDao extends AdBaseDaoImpl<AmazonAdAttributionPublisher> implements IAmazonAdAttributionPublisherDao {
    @Override
    public Integer insertOrUpdate(List<AmazonAdAttributionPublisher> publishers) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_ad_attribution_publisher`(" +
                "`publisher_id`, `publisher_name`,`macro_enable`, `create_time`, `update_time`) VALUES ");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdAttributionPublisher publisher : publishers) {
            sql.append("(?,?,?,now(),now()),");
            argsList.add(publisher.getPublisherId());
            argsList.add(publisher.getPublisherName());
            argsList.add(publisher.getMacroEnable());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update ")
                .append("`publisher_name`=values(publisher_name),`macro_enable`=values(macro_enable)," +
                        "`create_time`=now(),`update_time`=now()");
        return getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<AmazonAdAttributionPublisher> list(String publishName, Boolean macroEnable) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        if (StringUtils.isNotBlank(publishName)) {
            builder.equalTo("publisher_name", publishName);
        }
        if (macroEnable != null) {
            builder.equalTo("macro_enable", macroEnable);
        }
        return listByCondition(builder.build());
    }

    @Override
    public AmazonAdAttributionPublisher getPublisherById(String publisherId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("publisher_id", publisherId);
        builder.limit(1);
        return getByCondition(builder.build());
    }

    @Override
    public AmazonAdAttributionPublisher getPublisherByName(String name) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("publisher_name", name);
        builder.limit(1);
        return getByCondition(builder.build());
    }
}
