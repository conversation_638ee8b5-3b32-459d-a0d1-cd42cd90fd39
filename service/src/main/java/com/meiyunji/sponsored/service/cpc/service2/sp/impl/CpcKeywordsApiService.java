package com.meiyunji.sponsored.service.cpc.service2.sp.impl;

import com.amazon.advertising.mode.StateEnum;
import com.amazon.advertising.mode.keywords.Keyword;
import com.amazon.advertising.spV3.enumeration.SpV3State;
import com.amazon.advertising.spV3.keyword.*;
import com.amazon.advertising.spV3.keyword.entity.*;
import com.amazon.advertising.spV3.response.ErrorItemV3;
import com.amazon.advertising.spV3.enumeration.SpV3MatchTypeEnum;
import com.amazon.advertising.spV3.enumeration.SpV3StateEnum;
import com.amazon.advertising.spV3.keyword.KeywordSpV3Client;
import com.amazon.advertising.spV3.keyword.entity.KeywordSuccessResultV3;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.AmazonResponseUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dto.AmazonServingStatusDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeyword;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.BatchResponseVo;
import com.meiyunji.sponsored.service.cpc.vo.SpKeywordsVo;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.AdStateV3;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by xp on 2021/4/15.
 * 对接口二次封装，直接和广告接口交互
 */
@Component
@Slf4j
public class CpcKeywordsApiService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Autowired
    private IDorisService dorisService;

    /**
     * 同步所有的关键词
     *
     * @param shop：
     */
    public void syncKeywords(ShopAuth shop, String campaignId, String groupId, String keywordId) {
        syncKeywords(shop, campaignId, groupId, keywordId, false);
    }

    public void syncKeywords(ShopAuth shop, String campaignId, String groupId, String keywordId, boolean nullResThrowException) {
        syncKeywords(shop, campaignId, groupId, keywordId ,null, nullResThrowException);
    }

    public void syncKeywords(ShopAuth shop, String campaignId, String groupId, String keywordId, List<SpV3State> stateList, boolean nullResThrowException) {
        syncKeywords(shop, campaignId, groupId, keywordId, stateList, nullResThrowException, false);
    }

    public List<AmazonServingStatusDto> listByIds(Integer puid, Integer shopId, String ids){
        List<AmazonAdKeyword> amazons = amazonAdKeywordDaoRoutingService.getByKeywordIds(puid, shopId, StringUtil.stringToList(ids,","), Constants.BIDDABLE);
        amazons.forEach(i -> {
            if (Objects.nonNull(i)) {
                if (StringUtils.isNotBlank(i.getState())) {
                    i.setState(i.getState().toLowerCase());
                }
            }
        });
        dorisService.saveDorisByRoutineLoad4MysqlDto(amazons);
        return CollectionUtils.isEmpty(amazons) ? new ArrayList<>() : amazons.stream().map(key -> {
                    key.setServingStatus(key.getServingStatus());
            return AmazonServingStatusDto.build(key.getKeywordId(), key.getServingStatus(), key.getServingStatusName(), key.getServingStatusDec());
                }
                ).collect(Collectors.toList());
    }


    /**
     * 同步所有的关键词
     *
     * @param shop：
     */
    public void syncKeywords(ShopAuth shop, String campaignId, String groupId, String keywordId, List<SpV3State> stateList, boolean nullResThrowException, boolean isProxy) {
        if (shop == null) {
            return;
        }
        if (StringUtils.isBlank(shop.getAdRefreshToken())) {
            log.error("syncAdGroups:店铺{}没有授权广告", shop.getId());
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncAdGroups--配置信息为空");
            return;
        }

        //获取活动的基本信息
        KeywordSpV3Client client = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        if (isProxy) {
            client = KeywordSpV3Client.getInstance(true);
        }
        List<String> campaignIdList = null;
        List<String> groupIdList = null;
        List<String> keywordIdList = null;
        if(StringUtils.isNotBlank(campaignId)){
            campaignIdList = StringUtil.splitStr(campaignId,",");
        }
        if(StringUtils.isNotBlank(groupId)){
            groupIdList = StringUtil.splitStr(groupId,",");
        }
        if(StringUtils.isNotBlank(keywordId)){
            keywordIdList = StringUtil.splitStr(keywordId,",");
        }

        int count = 4000;
        ListSpKeywordV3Response response;
        boolean refreshedToken = false; // 是否刷新了token，保证只刷新一次
        String nextToken = null;
        while (true) {
            response = client.listKeyword(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    campaignIdList, groupIdList, keywordIdList, stateList, null, null, null, true, nextToken, count);
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SP keywords rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = client.listKeyword(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        campaignIdList, groupIdList, keywordIdList, stateList, null, null, null, true, nextToken, count);
                retry++;
            }
            if (response != null
                    && response.getStatusCode() != null
                    && response.getStatusCode() == 401
                    && !refreshedToken) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                refreshedToken = true;
                continue;
            }

            if (AmazonResponseUtil.isError(response) && nullResThrowException) {
                log.error("keyword amazon api error:{}", response.getError());
                throw new ServiceException("sp syncKeywords error");
            }

            if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getKeywords())) {
                break;
            }

            int size = response.getData().getKeywords().size();
            AmazonAdKeyword amazonAdKeyword;
            List<AmazonAdKeyword> amazonAdKeywords = new ArrayList<>(size);
            for (KeywordExtendEntityV3 keyword : response.getData().getKeywords()) {
                amazonAdKeyword = turnToPO(keyword);
                if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordId())) {
                    amazonAdKeyword.setPuid(shop.getPuid());
                    amazonAdKeyword.setShopId(shop.getId());
                    amazonAdKeyword.setMarketplaceId(shop.getMarketplaceId());
                    amazonAdKeyword.setProfileId(amazonAdProfile.getProfileId());
                    amazonAdKeywords.add(amazonAdKeyword);
                }
            }
            //每500条一批入库
            List<List<AmazonAdKeyword>> partition = Lists.partition(amazonAdKeywords, 500);
            for (List<AmazonAdKeyword> adKeywords : partition) {
                amazonAdKeywordDaoRoutingService.insertOnDuplicateKeyUpdate(shop.getPuid(), adKeywords, Constants.BIDDABLE);
            }
            if(StringUtils.isNotBlank(response.getData().getNextToken())){
                nextToken = response.getData().getNextToken();
            } else {
                break;
            }

        }
    }



    public Result<List<AmazonAdKeyword>> updateKeywordBid(List<AmazonAdKeyword> amazonAdKeywords) {
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdKeyword one = amazonAdKeywords.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        List<PutKeywordEntityV3> keywords = makePutKeywordsBidV3(amazonAdKeywords);
        UpdateSpKeywordV3Response response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putKeywords(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), keywords, true);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putKeywords(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), keywords, true);
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }


        if (response.getData() != null) {
            StringBuilder error = new StringBuilder();
            List<KeywordSuccessResultV3> successList = response.getData().getKeywords().getSuccess();
            List<ErrorItemResultV3> errorList = response.getData().getKeywords().getError();

            List<AmazonAdKeyword> succList = new ArrayList<>(successList.size() + errorList.size());
            //处理成功结果
            for (KeywordSuccessResultV3 KeywordSuccessResultV3 : successList) {
                succList.add(amazonAdKeywords.get(KeywordSuccessResultV3.getIndex()));
            }
            //处理失败结果
            for (ErrorItemResultV3 errorItemResultV3 : errorList) {
                error.append("targetValue:").append(amazonAdKeywords.get(errorItemResultV3.getIndex()).getKeywordText()).append(",desc:")
                        .append(errorItemResultV3.getErrors().get(0).getErrorMessage()).append(";");
            }

            if (succList.size() > 0) {
                Result<List<AmazonAdKeyword>> result = ResultUtil.returnSucc(succList);
                result.setMsg(error.toString());
                return result;
            }

            return ResultUtil.returnErr(error.toString());
        }

        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null) {
            msg = response.getError().getMessage();
        }
        return ResultUtil.returnErr(msg);
    }

    /**
     * 更新关键词状态
     * @param amazonAdKeywords
     * @return
     */
    public Result<List<AmazonAdKeyword>> updateKeywordState(List<AmazonAdKeyword> amazonAdKeywords) {
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdKeyword one = amazonAdKeywords.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        List<PutKeywordEntityV3> keywords = makePutKeywordsStateV3(amazonAdKeywords);
        UpdateSpKeywordV3Response response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putKeywords(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), keywords, true);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putKeywords(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), keywords, true);
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }


        if (response.getData() != null) {
            StringBuilder error = new StringBuilder();
            List<KeywordSuccessResultV3> successList = response.getData().getKeywords().getSuccess();
            List<ErrorItemResultV3> errorList = response.getData().getKeywords().getError();

            List<AmazonAdKeyword> succList = new ArrayList<>(successList.size() + errorList.size());
            //处理成功结果
            for (KeywordSuccessResultV3 KeywordSuccessResultV3 : successList) {
                succList.add(amazonAdKeywords.get(KeywordSuccessResultV3.getIndex()));
            }
            //处理失败结果
            for (ErrorItemResultV3 errorItemResultV3 : errorList) {
                error.append("targetValue:").append(amazonAdKeywords.get(errorItemResultV3.getIndex()).getKeywordText()).append(",desc:")
                        .append(errorItemResultV3.getErrors().get(0).getErrorMessage()).append(";");
            }

            if (succList.size() > 0) {
                Result<List<AmazonAdKeyword>> result = ResultUtil.returnSucc(succList);
                result.setMsg(error.toString());
                return result;
            }

            return ResultUtil.returnErr(error.toString());
        }

        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null) {
            msg = response.getError().getMessage();
        }
        return ResultUtil.returnErr(msg);
    }

    public Result archive(AmazonAdKeyword amazonAdKeyword) {
        if (amazonAdKeyword == null) {
            return ResultUtil.error("请求参数错误");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdKeyword.getShopId(), amazonAdKeyword.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        UpdateSpKeywordV3Response response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delKeywords(shopAuthService.getAdToken(shop),
                amazonAdKeyword.getProfileId(), shop.getMarketplaceId(), 
                Collections.singletonList(amazonAdKeyword.getKeywordId()), false);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delKeywords(shopAuthService.getAdToken(shop),
                    amazonAdKeyword.getProfileId(), shop.getMarketplaceId(),
                    Collections.singletonList(amazonAdKeyword.getKeywordId()), false);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getKeywords().getSuccess())) {
            return ResultUtil.success();
        } else if(response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getKeywords().getError()) &&   CollectionUtils.isNotEmpty(response.getData().getKeywords().getError().get(0).getErrors())) {
            String errorMessage = response.getData().getKeywords().getError().get(0).getErrors().get(0).getErrorMessage();
            if(StringUtils.isNotBlank(errorMessage)){
                return ResultUtil.error(AmazonErrorUtils.getError(errorMessage));
            }
        }

        //处理返回结果中的错误信息
        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null) {
            msg = AmazonErrorUtils.getError(response.getError().getMessage());
        }
        return ResultUtil.error(msg);
    }

    private List<Keyword> makeKeywords(List<AmazonAdKeyword> amazonAdKeywordList) {
        List<Keyword> list = Lists.newArrayListWithCapacity(amazonAdKeywordList.size());
        Keyword keyword;
        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywordList) {
            keyword = new Keyword();
            if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordId())) {
                keyword.setKeywordId(Long.valueOf(amazonAdKeyword.getKeywordId()));
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getCampaignId())) {
                keyword.setCampaignId(Long.valueOf(amazonAdKeyword.getCampaignId()));
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getAdGroupId())) {
                keyword.setAdGroupId(Long.valueOf(amazonAdKeyword.getAdGroupId()));
            }
            keyword.setKeywordText(amazonAdKeyword.getKeywordText());
            keyword.setMatchType(amazonAdKeyword.getMatchType());
            keyword.setState(amazonAdKeyword.getState());
            keyword.setBid(amazonAdKeyword.getBid());
            list.add(keyword);
        }
        return list;
    }

    private List<PutKeywordEntityV3> makePutKeywordsStateV3(List<AmazonAdKeyword> amazonAdKeywordList) {
        List<PutKeywordEntityV3> list = Lists.newArrayListWithCapacity(amazonAdKeywordList.size());
        PutKeywordEntityV3 keyword;
        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywordList) {
            keyword = new PutKeywordEntityV3();
            if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordId())) {
                keyword.setKeywordId(amazonAdKeyword.getKeywordId());
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getState())) {
                keyword.setState(AdStateV3.fromOldValue(amazonAdKeyword.getState()).getValue());
            }
            list.add(keyword);
        }
        return list;
    }


  private List<PutKeywordEntityV3> makePutKeywordsBidV3(List<AmazonAdKeyword> amazonAdKeywordList) {
        List<PutKeywordEntityV3> list = Lists.newArrayListWithCapacity(amazonAdKeywordList.size());
        PutKeywordEntityV3 keyword;
        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywordList) {
            keyword = new PutKeywordEntityV3();
            if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordId())) {
                keyword.setKeywordId(amazonAdKeyword.getKeywordId());
            }
            if (amazonAdKeyword.getBid() != null) {
                keyword.setBid(amazonAdKeyword.getBid());
            }

            list.add(keyword);
        }
        return list;
    }

    private AmazonAdKeyword turnToPO(KeywordExtendEntityV3 keyword) {
        AmazonAdKeyword amazonAdKeyword = new AmazonAdKeyword();
        if (keyword.getKeywordId() != null) {
            amazonAdKeyword.setKeywordId(keyword.getKeywordId());
        }
        if (keyword.getCampaignId() != null) {
            amazonAdKeyword.setCampaignId(keyword.getCampaignId());
        }
        if (keyword.getAdGroupId() != null) {
            amazonAdKeyword.setAdGroupId(keyword.getAdGroupId());
        }
        SpV3MatchTypeEnum spV3MatchTypeEnumByValueV3 = SpV3MatchTypeEnum.getSpV3MatchTypeEnumByValueV3(keyword.getMatchType());
        amazonAdKeyword.setMatchType(spV3MatchTypeEnumByValueV3 != null ? spV3MatchTypeEnumByValueV3.value() : keyword.getMatchType());
        amazonAdKeyword.setKeywordText(keyword.getKeywordText());
        amazonAdKeyword.setKeywordSize(Math.toIntExact(Arrays.stream(keyword.getKeywordText().trim().split(" ")).filter(StringUtils::isNotBlank).count()));
        amazonAdKeyword.setState(keyword.getState().toLowerCase());
        amazonAdKeyword.setBid(keyword.getBid());
        amazonAdKeyword.setType(Constants.BIDDABLE);
        amazonAdKeyword.setServingStatus(keyword.getExtendedData().getServingStatus());
        return amazonAdKeyword;
    }



    public Result<BatchResponseVo<SpKeywordsVo,AmazonAdKeyword>> update(List<AmazonAdKeyword> amazonAdKeywords,String type) {
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdKeyword one = amazonAdKeywords.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        UpdateSpKeywordV3Response response;
        if (StateEnum.ARCHIVED.value().equalsIgnoreCase(one.getState())) {
            //v3接口 归档操作,走指定接口
             response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delKeywords(shopAuthService.getAdToken(shop),
                     one.getProfileId(), shop.getMarketplaceId(),
                     amazonAdKeywords.stream().map(AmazonAdKeyword::getKeywordId)
                             .collect(Collectors.toList()), false);
            if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
                // 刷新token重试一次
                shopAuthService.refreshCpcAuth(shop);
                response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delKeywords(shopAuthService.getAdToken(shop),
                        one.getProfileId(), shop.getMarketplaceId(),
                        amazonAdKeywords.stream().map(AmazonAdKeyword::getKeywordId)
                                .collect(Collectors.toList()), false);
            }
        } else {
            List<PutKeywordEntityV3> keywords = makePutKeywordsV3(amazonAdKeywords,type);

            response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putKeywords(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), keywords, true);
            if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
                // 刷新token重试一次
                shopAuthService.refreshCpcAuth(shop);
                response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putKeywords(shopAuthService.getAdToken(shop),
                        one.getProfileId(), one.getMarketplaceId(), keywords, true);
            }
        }


        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }


        Map<String, AmazonAdKeyword> amazonAdKeywordMap = amazonAdKeywords.stream().collect(Collectors.toMap(AmazonAdKeyword::getKeywordId, e -> e));
        BatchResponseVo<SpKeywordsVo,AmazonAdKeyword> batchResponseVo = new BatchResponseVo<SpKeywordsVo,AmazonAdKeyword>();
        List<SpKeywordsVo> errorList = Lists.newArrayList();
        List<AmazonAdKeyword> successList = Lists.newArrayList();
        if (response.getData() != null) {
            List<KeywordSuccessResultV3> success = response.getData().getKeywords().getSuccess();
            List<ErrorItemResultV3> error = response.getData().getKeywords().getError();
            //处理成功结果
            for (KeywordSuccessResultV3 KeywordSuccessResultV3 : success) {
                AmazonAdKeyword amazonAdKeywordSuccess = amazonAdKeywordMap.remove(KeywordSuccessResultV3.getKeywordId());
                if (amazonAdKeywordSuccess != null) {
                    successList.add(amazonAdKeywordSuccess);
                }
            }

            //处理失败结果
            for (ErrorItemResultV3 errorItemResultV3 : error) {
                //通过index获取keywordId
                String keywordId = amazonAdKeywords.get(errorItemResultV3.getIndex()).getKeywordId();

                AmazonAdKeyword amazonAdKeywordFail = amazonAdKeywordMap.remove(keywordId);
                if (amazonAdKeywordFail != null) {
                    SpKeywordsVo spKeywordsVoError = new SpKeywordsVo();
                    spKeywordsVoError.setDxmKeywordId(amazonAdKeywordFail.getId());
                    spKeywordsVoError.setId(amazonAdKeywordFail.getId());
                    spKeywordsVoError.setKeywordText(amazonAdKeywordFail.getKeywordText());
                    //更新失败数据处理
                    if (CollectionUtils.isNotEmpty(errorItemResultV3.getErrors())) {
                        ErrorItemV3 errorItemV3 = errorItemResultV3.getErrors().get(0);
                        spKeywordsVoError.setFailReason(String.valueOf(errorItemV3.getErrorMessage()));
                    } else {
                        spKeywordsVoError.setFailReason("更新失败，请稍后重试");
                    }
                    spKeywordsVoError.setKeywordId(amazonAdKeywordFail.getKeywordId());
                    errorList.add(spKeywordsVoError);
                }
            }

            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonAdKeywordMap)) {
                amazonAdKeywordMap.forEach((k, v) -> {
                    SpKeywordsVo spKeywordsVoError = new SpKeywordsVo();
                    spKeywordsVoError.setDxmKeywordId(v.getId());
                    spKeywordsVoError.setId(v.getId());
                    spKeywordsVoError.setKeywordText(v.getKeywordText());
                    spKeywordsVoError.setFailReason("更新失败，请稍后重试");
                    spKeywordsVoError.setKeywordId(v.getKeywordId());
                    errorList.add(spKeywordsVoError);
                });
            }

        } else if (response.getError() != null && StringUtils.isNotBlank(response.getError().getMessage())) {
            //授权失败
            if (MapUtils.isNotEmpty(amazonAdKeywordMap)) {
                String message = response.getError().getMessage();
                amazonAdKeywordMap.forEach((k, v) -> {
                    SpKeywordsVo spKeywordsVoError = new SpKeywordsVo();
                    spKeywordsVoError.setDxmKeywordId(v.getId());
                    spKeywordsVoError.setKeywordText(v.getKeywordText());
                    spKeywordsVoError.setId(v.getId());
                    spKeywordsVoError.setFailReason(message);
                    spKeywordsVoError.setKeywordId(v.getKeywordId());
                    errorList.add(spKeywordsVoError);
                });
            }
        }else {
            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonAdKeywordMap)) {
                amazonAdKeywordMap.forEach((k, v) -> {
                    SpKeywordsVo spKeywordsVoError = new SpKeywordsVo();
                    spKeywordsVoError.setDxmKeywordId(v.getId());
                    spKeywordsVoError.setKeywordText(v.getKeywordText());
                    spKeywordsVoError.setId(v.getId());
                    spKeywordsVoError.setFailReason("更新失败，请稍后重试");
                    spKeywordsVoError.setKeywordId(v.getKeywordId());
                    errorList.add(spKeywordsVoError);
                });
            }
        }
        batchResponseVo.setCountNum(amazonAdKeywords.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);
        return ResultUtil.success(batchResponseVo);


    }


    private List<Keyword> makeKeywords(List<AmazonAdKeyword> amazonAdKeywordList,String type) {
        List<Keyword> list = Lists.newArrayListWithCapacity(amazonAdKeywordList.size());

        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywordList) {
            Keyword keyword = new Keyword();

            if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordId())) {
                keyword.setKeywordId(Long.valueOf(amazonAdKeyword.getKeywordId()));
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getCampaignId())) {
                keyword.setCampaignId(Long.valueOf(amazonAdKeyword.getCampaignId()));
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getAdGroupId())) {
                keyword.setAdGroupId(Long.valueOf(amazonAdKeyword.getAdGroupId()));
            }

            if(Constants.CPC_SP_KEYWORD_BATCH_UPDATE_BID.equals(type)){
                keyword.setBid(amazonAdKeyword.getBid());
            }
            if(Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
                keyword.setState(amazonAdKeyword.getState());
            }
            list.add(keyword);
        }
        return list;
    }


    private List<PutKeywordEntityV3> makePutKeywordsV3(List<AmazonAdKeyword> amazonAdKeywordList, String type) {
        List<PutKeywordEntityV3> list = Lists.newArrayListWithCapacity(amazonAdKeywordList.size());

        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywordList) {
            PutKeywordEntityV3 keyword = new PutKeywordEntityV3();

            if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordId())) {
                keyword.setKeywordId(amazonAdKeyword.getKeywordId());
            }
            if (Constants.CPC_SP_KEYWORD_BATCH_UPDATE_BID.equals(type)) {
                keyword.setBid(amazonAdKeyword.getBid());
            }
            if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
                keyword.setState(AdStateV3.fromOldValue(amazonAdKeyword.getState()).getValue());
            }
            list.add(keyword);
        }
        return list;
    }

    /**
     * 创建否定关键词
     *
     * @param amazonAdKeywords：
     * @return ：Result
     */
    Result createKeywordsV3(List<AmazonAdKeyword> amazonAdKeywords) {
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return ResultUtil.error("请求参数错误");
        }

        AmazonAdKeyword one = amazonAdKeywords.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        List<CreateKeywordEntityV3> keywords = makeCreateKeywordsV3(amazonAdKeywords);
        CreateSpKeywordV3Response response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createKeywords(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), keywords, Boolean.TRUE);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createKeywords(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), keywords, Boolean.TRUE);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }


        //处理返回结果中的错误信息
        if (response.getData() != null) {
            boolean hasSucc = false;
            StringBuilder error = new StringBuilder();
            KeywordApiResponseV3 data = response.getData();
            if(CollectionUtils.isNotEmpty(data.getKeywords().getSuccess())){
                for (KeywordSuccessResultV3 keywordSuccessResultV3 :data.getKeywords().getSuccess()){
                    AmazonAdKeyword amazonAdKeyword = amazonAdKeywords.get(keywordSuccessResultV3.getIndex());
                    amazonAdKeyword.setKeywordId(keywordSuccessResultV3.getKeywordId());
                }

                hasSucc = true;
            }

            if (CollectionUtils.isNotEmpty(data.getKeywords().getError())){
                for (ErrorItemResultV3 errorItemResultV3 : data.getKeywords().getError()){
                    error.append("targetValue:").append(amazonAdKeywords.get(errorItemResultV3.getIndex()).getKeywordText()).append(",desc:").append(AmazonErrorUtils.getError(errorItemResultV3.getErrors().get(0).getErrorMessage())).append(";");
                }
            }
            if (hasSucc) {
                return ResultUtil.success(error.toString());
            }

            return ResultUtil.error(error.toString());
        }



        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null) {
            msg = AmazonErrorUtils.getError(response.getError().getMessage());
        }
        return ResultUtil.error(msg);
    }

    /**
     * 同步所有的关键词
     *
     * @param shop：
     */
    public List<AmazonAdKeyword> syncKeyword(ShopAuth shop, String campaignId, String groupId, String keywordId) {
        List<AmazonAdKeyword> amazonAdKeywords = new ArrayList<>();
        if (shop == null) {
            return amazonAdKeywords;
        }
        if (StringUtils.isBlank(shop.getAdRefreshToken())) {
            log.error("syncAdGroups:店铺{}没有授权广告", shop.getId());
            return amazonAdKeywords;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncAdGroups--配置信息为空");
            return amazonAdKeywords;
        }

        //获取活动的基本信息
        KeywordSpV3Client client = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        List<String> campaignIdList = null;
        List<String> groupIdList = null;
        List<String> keywordIdList = null;
        if(StringUtils.isNotBlank(campaignId)){
            campaignIdList = StringUtil.splitStr(campaignId,",");
        }
        if(StringUtils.isNotBlank(groupId)){
            groupIdList = StringUtil.splitStr(groupId,",");
        }
        if(StringUtils.isNotBlank(keywordId)){
            keywordIdList = StringUtil.splitStr(keywordId,",");
        }

        int count = 4000;
        ListSpKeywordV3Response response;
        boolean refreshedToken = false; // 是否刷新了token，保证只刷新一次
        String nextToken = null;
        while (true) {
            response = client.listKeyword(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    campaignIdList, groupIdList, keywordIdList, null, null, null, null, true, nextToken, count);
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SP keywords rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = client.listKeyword(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        campaignIdList, groupIdList, keywordIdList, null, null, null, null, true, nextToken, count);
                retry++;
            }
            if (response != null
                    && response.getStatusCode() != null
                    && response.getStatusCode() == 401
                    && !refreshedToken) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                refreshedToken = true;
                continue;
            }

            if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getKeywords())) {
                break;
            }

            AmazonAdKeyword amazonAdKeyword;
            for (KeywordExtendEntityV3 keyword : response.getData().getKeywords()) {
                amazonAdKeyword = turnToPO(keyword);
                if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordId())) {
                    amazonAdKeyword.setPuid(shop.getPuid());
                    amazonAdKeyword.setShopId(shop.getId());
                    amazonAdKeyword.setMarketplaceId(shop.getMarketplaceId());
                    amazonAdKeyword.setProfileId(amazonAdProfile.getProfileId());
                    amazonAdKeywords.add(amazonAdKeyword);
                }
            }
            if(StringUtils.isNotBlank(response.getData().getNextToken())){
                nextToken = response.getData().getNextToken();
            } else {
                break;
            }
        }
        return amazonAdKeywords;
    }

    public List<CreateKeywordEntityV3> makeCreateKeywordsV3(List<AmazonAdKeyword> amazonAdKeywordList) {
        List<CreateKeywordEntityV3> list = Lists.newArrayListWithCapacity(amazonAdKeywordList.size());
        CreateKeywordEntityV3 keyword;
        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywordList) {
            keyword = new CreateKeywordEntityV3();
            keyword.setCampaignId(amazonAdKeyword.getCampaignId());
            keyword.setAdGroupId(amazonAdKeyword.getAdGroupId());
            keyword.setKeywordText(amazonAdKeyword.getKeywordText());
            keyword.setMatchType(SpV3MatchTypeEnum.getSpV3MatchTypeEnumByValue(amazonAdKeyword.getMatchType()).valueV3());
            keyword.setState(SpV3StateEnum.getSpV3StateEnumByValue(amazonAdKeyword.getState()).valueV3());
            keyword.setBid(amazonAdKeyword.getBid());
            list.add(keyword);
        }
        return list;
    }
}
