package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.rpc.asins.PageListAsinsRequest;
import com.meiyunji.sponsored.service.cpc.bo.AdAsinOrderBo;
import com.meiyunji.sponsored.service.cpc.dto.TargetReportHourlyDTO;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdSdTargetingReport;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTargetingMatrixTopDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;

import java.util.List;

/**
 * sd商品投放报告(OdsAmazonAdSdTargetingReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:20
 */
public interface IOdsAmazonAdSdTargetingReportDao extends IDorisBaseDao<OdsAmazonAdSdTargetingReport> {

    List<DashboardAdTargetingMatrixTopDto> getTargetingTopList(Integer puid, List<String> marketplaceIdList,
                                                             List<Integer> shopIdList, String currency,
                                                             String startDate, String endDate,
                                                             DashboardDataFieldEnum dataField, List<String> targetIdList,
                                                             Integer limit, DashboardOrderByEnum orderBy, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds,
                                                               List<String> campaignIds, Boolean noZero);

    List<DashboardAdTargetingMatrixTopDto> getTargetingTopInfoList(Integer puid, List<String> marketplaceIdList,
                                                               List<Integer> shopIdList, String currency,
                                                               String startDate, String endDate,
                                                               DashboardDataFieldEnum dataField, List<String> targetIdList,
                                                               DashboardOrderByEnum orderBy, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds,
                                                                   List<String> campaignIds, Boolean noZero);

    /**
     * 根据投放Id查询对应的报告数据
     * @param puid
     * @param param
     * @param sdTargetIds
     * @return
     */
    List<AsinLibsDetailVo> getSdAsinReportDataByTargetId(Integer puid, AsinLibsDetailParam param, List<String> sdTargetIds);

    List<AdAsinOrderBo> getOrderFieldByAsins(Integer puid, List<String> shopIds, PageListAsinsRequest param, List<String> asinList);

    List<AsinLibsDetailVo> getAsinReportByAsins(Integer puid, PageListAsinsRequest param,
                                               String startDate, String endDate,
                                               List<String> asinList, String currency);

    int getTargetAllCount(Integer puid, TargetingPageParam param);

    Page<TargetPageVo> getTargetPage(TargetingPageParam param);

    List<TargetPageVo> getReportListByTargetIds(Integer puid, Integer shopId, List<String> targetIdList, String startDate, String endDate);

    AdMetricDto getTargetPageSumMetricData(Integer puid, TargetingPageParam param);

    /**
     * 商品投放汇总查询所有keywordId
     */
    List<String> getTargetIdListByPage(Integer puid, TargetingPageParam param);

    /**
     * 商品投放汇总根据keywordId查询按天维度数据
     */
    List<AdHomePerformancedto> getReportAggregateByTargetIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> targetIdList, boolean isGroupByDate);

    AdHomePerformancedto getReportAggregateCompareData(Integer puid, TargetingPageParam param, String startStr, String endStr);

    List<TargetReportHourlyDTO> getReportByTargetId(Integer puid, List<Integer> shopIds, List<String> marketplaceIds, String startDate, String endDate,
                                                    List<String> targetIds, boolean changeRate, String currency);
}

