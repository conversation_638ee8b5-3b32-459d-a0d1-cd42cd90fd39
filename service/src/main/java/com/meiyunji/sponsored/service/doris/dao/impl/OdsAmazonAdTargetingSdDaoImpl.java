package com.meiyunji.sponsored.service.doris.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.rpc.asins.PageListAsinsRequest;
import com.meiyunji.sponsored.service.cpc.vo.AsinLibsVo;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdTargetingSdDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdTargetingSd;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * amazon SD广告投放定位表(OdsAmazonAdTargetingSd)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:21
 */
@Repository
public class OdsAmazonAdTargetingSdDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdTargetingSd> implements IOdsAmazonAdTargetingSdDao {

    @Override
    public List<OdsAmazonAdTargetingSd> getByTargetingIds(int puid, List<Integer> shopIds, List<String> targetingIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuffer selectSql = new StringBuffer("SELECT * ");
        selectSql.append(" from ").append(getJdbcHelper().getTable()).append(" t ");
        selectSql.append(" where t.puid = ? ");
        argsList.add(puid);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(shopIds)) {
            selectSql.append(SqlStringUtil.dealInList("t.shop_id", shopIds, argsList));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(targetingIds)) {
            selectSql.append(SqlStringUtil.dealInList("t.target_id", targetingIds, argsList));
        }
        return getJdbcTemplate().query(selectSql.toString(), argsList.toArray(), new ObjectMapper<>(OdsAmazonAdTargetingSd.class));
    }

    @Override
    public List<OdsAmazonAdTargetingSd> getByTargetingIds(int puid, Integer shopId, List<String> targetIds) {
        StringBuilder sb = new StringBuilder("select * from ").append(this.getJdbcHelper().getTable())
                .append(" where puid = ? and shop_id = ? ");
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        sb.append(SqlStringUtil.dealBitMapDorisInList("target_id", targetIds, argsList));
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(OdsAmazonAdTargetingSd.class));
    }

    @Override
    public List<String> getTargetIdsByAdGroupId(int puid, List<Integer> shopIdList, List<String> sdAdGroupIdList) {
        StringBuilder sqlBuilder = new StringBuilder("select target_id from "+ this.getJdbcHelper().getTable() +" where puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        sqlBuilder.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
        sqlBuilder.append(SqlStringUtil.dealInList("ad_group_id", sdAdGroupIdList, args));
        return getJdbcTemplate().query(sqlBuilder.toString(), (rs, rowNum) -> rs.getString("target_id"), args.toArray());
    }

    @Override
    public List<AsinLibsVo> getCountByAsin(Integer puid, PageListAsinsRequest param, List<Integer> shopIdList, List<String> asinList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select target_text asin, count(*) targetNum from ods_t_amazon_ad_targeting_sd " +
                "where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)){
            sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        }
        //通过来源站点筛选
        if(CollectionUtils.isNotEmpty(param.getContryList())){
            sql.append(SqlStringUtil.dealInList("marketplace_id", param.getContryList(), argsList));
        }

        sql.append(" and `type` = 'asin' ");

        if (CollectionUtils.isNotEmpty(asinList)){
            // 创建一个新的列表来存储转换后的字符串
            List<String> lowerCaseList = new ArrayList<>();
            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
            for (String asin : asinList) {
                lowerCaseList.add(asin.toLowerCase());
            }
            sql.append(SqlStringUtil.dealInList("lower(target_text)",lowerCaseList,argsList));
        }
        sql.append(" group by target_text");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(AsinLibsVo.class), argsList.toArray());
    }
}

