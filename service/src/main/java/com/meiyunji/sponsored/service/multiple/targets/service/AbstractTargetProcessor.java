package com.meiyunji.sponsored.service.multiple.targets.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sellfox.aadras.types.enumeration.SpV3ExpressionEnum;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeChartRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDao;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.common.qo.WordTranslateQo;
import com.meiyunji.sponsored.service.common.service.IWordTranslateService;
import com.meiyunji.sponsored.service.config.nacos.AdManageLimitConfig;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdOperationLogBO;
import com.meiyunji.sponsored.service.cpc.constants.*;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdTargetStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAdMarkupTagDao;
import com.meiyunji.sponsored.service.cpc.dao.IAdTagDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdPortfolioDao;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.impl.AdChartDataProcessNew;
import com.meiyunji.sponsored.service.cpc.service2.IAdManagePageExportTaskService;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdOperationLogService;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCommService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.ResolvedExpressionParseHelper;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformanceNewDto;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.dao.IDwsSaleProfitShopDayDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdPortfolioDao;
import com.meiyunji.sponsored.service.doris.dao.admanage.IAdManageTargetDorisDao;
import com.meiyunji.sponsored.service.doris.po.DwsSaleProfitShopDay;
import com.meiyunji.sponsored.service.doris.po.OdsWeekSearchTermsAnalysis;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.export.handler.TargetMultiplePageExportTaskHandler;
import com.meiyunji.sponsored.service.log.enums.ItemTypeEnum;
import com.meiyunji.sponsored.service.log.po.OperationContent;
import com.meiyunji.sponsored.service.monitor.SaveMonitor;
import com.meiyunji.sponsored.service.monitor.enums.MonitorPageFunctionEnum;
import com.meiyunji.sponsored.service.monitor.enums.MonitorTypeEnum;
import com.meiyunji.sponsored.service.multiple.common.dto.ReportChartBase;
import com.meiyunji.sponsored.service.multiple.common.dto.ReportDorisBase;
import com.meiyunji.sponsored.service.multiple.common.resp.AdvanceCountResp;
import com.meiyunji.sponsored.service.multiple.common.utils.MultipleUtils;
import com.meiyunji.sponsored.service.multiple.common.utils.ReportUtils;
import com.meiyunji.sponsored.service.multiple.common.vo.CommonCompareReportRate;
import com.meiyunji.sponsored.service.multiple.targets.dto.*;
import com.meiyunji.sponsored.service.multiple.targets.enums.TargetExportFieldEnum;
import com.meiyunji.sponsored.service.multiple.targets.resp.BidLog;
import com.meiyunji.sponsored.service.multiple.targets.resp.TargetAggregateResp;
import com.meiyunji.sponsored.service.multiple.targets.resp.TargetResp;
import com.meiyunji.sponsored.service.product.po.AsinImage;
import com.meiyunji.sponsored.service.product.service.ISyncAsinImageService;
import com.meiyunji.sponsored.service.searchTermsAnalysis.WeekSearchTermsAnalysisService;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusDao;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatus;
import com.meiyunji.sponsored.service.wordFrequency.vo.WordRootTopVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.stream.Collectors;


/**
 * 广告管理多店铺投放层级-模版抽象类
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Slf4j
public abstract class AbstractTargetProcessor {

    @Resource
    private IAdMarkupTagDao adMarkupTagDao;

    @Resource
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;

    @Resource
    private AdvertiseStrategyStatusDao advertiseStrategyStatusDao;

    @Resource
    public IAdManageTargetDorisDao adManageTargetDorisDao;

    @Resource
    private IScVcShopAuthDao shopAuthDao;

    @Resource
    private WeekSearchTermsAnalysisService weekSearchTermsAnalysisService;

    @Resource
    private IDwsSaleProfitShopDayDao dwsSaleProfitShopDayDao;

    @Resource
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;

    @Resource
    private IAmazonAdPortfolioDao portfolioDao;

    @Resource
    private IAdTagDao adTagDao;

    @Resource
    private IWordTranslateService wordTranslateService;

    @Resource
    private IAmazonAdOperationLogService amazonAdOperationLogService;

    @Resource
    private ISyncAsinImageService syncAsinImageService;

    @Resource
    private StringRedisService stringRedisService;

    @Resource
    private CpcCommService cpcCommService;

    @Resource
    private AdManageLimitConfig adManageLimitConfig;

    @Resource
    private AdChartDataProcessNew adChartDataProcessNew;

    @Resource
    private CpcPageIdsHandler cpcPageIdsHandler;

    @Resource
    private IAdManagePageExportTaskService adManagePageExportTaskService;

    @Resource
    private ThreadPoolTaskExecutor targetAdPageExecutor;

    @Resource
    private ThreadPoolTaskExecutor adExportExecutor;

    @Resource
    private IOdsAmazonAdPortfolioDao odsAmazonAdPortfolioDao;

    /**
     * 广告管理多店铺投放层级-查询词根频次接口，获取topN词根
     *
     * @param req 请求参数
     * @return topN词根
     */
    public List<WordRootTopVo> getKeywordTopList(TargetReqDto req) {
        // 1.请求参数校验
        checkParam(req);
        // 清空上一次词根搜索
        req.setWordRoot("");
        // 2.参数赋值
        setParam(req);
        // 3.前置过滤
        if (filterTargetIds(req)) {
            return new ArrayList<>();
        }
        // 4.获取topN词根
        return adManageTargetDorisDao.getKeywordTopList(req);
    }

    /**
     * 广告管理多店铺投放层级-高级筛选模板统计个数接口
     *
     * @param req 请求参数
     * @return 统计个数
     */
    public AdvanceCountResp getAllTargetCount(TargetReqDto req) {
        AdvanceCountResp resp = new AdvanceCountResp();
        // 1.请求参数校验
        checkParam(req);
        // 2.参数赋值
        setParam(req);
        // 3.前置过滤
        if (filterTargetIds(req)) {
            return resp;
        }
        // 4.获取统计个数
        Integer count = adManageTargetDorisDao.countTarget(req);
        resp.setCount(count);
        return resp;
    }

    /**
     * 广告管理多店铺投放层级-获取分页列表
     *
     * @param req 请求参数
     * @return 分页列表
     */
    @SaveMonitor(type = MonitorTypeEnum.LIST, tableName = MonitorPageFunctionEnum.TARGET, monitorAnalysis = false)
    public Page<TargetResp> getAllTargetData(TargetReqDto req, Boolean export) {
        // 1.请求参数校验
        checkParam(req);
        // 2.参数赋值
        setParam(req);
        // 3.前置过滤
        if (filterTargetIds(req)) {
            log.info("前置过滤获取targetId为空过滤");
            return new Page<>(req.getPageNo(), req.getPageSize(), 0, 0, new ArrayList<>());
        }
        // 4.获取分页数据
        Page<TargetResp> targetPage = adManageTargetDorisDao.getTargetPage(req);
        if (CollectionUtils.isEmpty(targetPage.getRows())) {
            return new Page<>(req.getPageNo(), req.getPageSize(), 0, 0, new ArrayList<>());
        }
        // 5.组装响应数据
        return buildTargetResp(req, targetPage, export);
    }

    /**
     * 广告管理多店铺投放层级-汇总接口
     *
     * @param req 请求参数
     * @return 汇总数据
     */
    @SaveMonitor(type = MonitorTypeEnum.SUM, tableName = MonitorPageFunctionEnum.TARGET, monitorAnalysis = false)
    public TargetAggregateResp getAllTargetAggregateData(TargetReqDto req) {
        // 1.请求参数校验
        checkParam(req);
        // 2.参数赋值
        setParam(req);
        // 3.前置过滤
        if (filterTargetIds(req)) {
            req.setTargetIds(CollectionUtil.newArrayList("-1")); // 赋值-1
        }
        TargetAggregateResp resp = new TargetAggregateResp();
        // 4.判断是否超过限制
        if (overLimit(req)) {
            resp.setOverLimit(true);
            return resp;
        }
        // 5.汇总店铺销售额
        req.setShopSale(dwsSaleProfitShopDayDao.sumShopSaleByDateRange(req.getPuid(), req.getShopIdList(), req.getStartDate(), req.getEndDate(), req.getChangeRate()));
        // 6.组装图表数据
        List<ReportChartBase> reportDays = buildChartData(req, resp);
        // 7.组装汇总数据
        buildSumReport(req, resp, reportDays);
        return resp;
    }

    /**
     * 是否超过限制
     */
    private Boolean overLimit(TargetReqDto req) {
        // 统计报告数量 超过限制不统计，否则会导致doris cpu过高
        Integer count = adManageTargetDorisDao.countReport(req);
        if (count >= adManageLimitConfig.getTargetAggregateLimit()) {
            return true;
        }
        // 统计对比报告数量 超过限制不统计，否则会导致doris cpu过高
        if (req.getIsCompare()) {
            TargetReqDto compareReq = new TargetReqDto();
            BeanUtils.copyProperties(req, compareReq);
            compareReq.setStartDate(req.getCompareStartDate());
            compareReq.setEndDate(req.getCompareEndDate());
            count = adManageTargetDorisDao.countReport(req);
            if (count >= adManageLimitConfig.getTargetAggregateLimit()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 广告管理多店铺投放层级-导出接口
     * 异步导出方法入口:{@link TargetMultiplePageExportTaskHandler#export}
     *
     * @param req 公共请求参数
     * @return 唯一uuid
     */
    public String exportAllTargetData(TargetReqDto req) {
        // 参数校验
        checkParam(req);
        // 校验自定义导出字段
        if (StringUtils.isNotBlank(req.getExportSortField())) {
            //排序字段校验
            String[] split = req.getExportSortField().split(",");
            for (String s : split) {
                if (!TargetExportFieldEnum.getPoParamKeyList().contains(s)) {
                    throw new SponsoredBizException("存在非法的自定义导出字段！");
                }
            }
        }
        // 放入缓存
        String uuid = UUID.randomUUID().toString().replace("-", "");
        stringRedisService.set(uuid, new ProcessMsg(0, 0, "正在同步中"));
        req.setUuid(uuid);
        // 插入异步任务
        adManagePageExportTaskService.saveExportTask(req.getPuid(), req.getUid(), 0, AdManagePageExportTaskTypeEnum.TARGET_MULTIPLE, req.getStartDate(), req.getEndDate(), req);
        return uuid;
    }

    /**
     * 组装图表数据
     */
    private List<ReportChartBase> buildChartData(TargetReqDto req, TargetAggregateResp resp) {
        // 查询图表数据
        List<ReportChartBase> sumReportDayList = adManageTargetDorisDao.getSumReportGroupByCountDay(req);
        // 获取币种
        String currency = MultipleUtils.getCurrency(req.getShopAuthList());
        List<AdHomePerformanceNewDto> dtoList = ReportUtils.getChartDtoList(sumReportDayList);
        // 获取chart数据
        boolean isVc = req.getShopAuthList().stream().anyMatch(e -> ShopTypeEnum.VC.getCode().equals(e.getType()));
        List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcessNew.getDayPerformanceVos(currency, dtoList, req.getShopSale(), isVc);
        List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcessNew.getWeekPerformanceVos(currency, req.getStartDate(), req.getEndDate(), dtoList, req.getShopSale(), isVc);
        List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcessNew.getMonthPerformanceVos(currency, dtoList, req.getShopSale(), isVc);
        // grpc -> vo
        resp.setDay(ReportUtils.chartGrpcToVo(dayPerformanceVos));
        resp.setWeek(ReportUtils.chartGrpcToVo(weekPerformanceVos));
        resp.setMonth(ReportUtils.chartGrpcToVo(monthPerformanceVos));
        resp.setCurrency(currency);
        // 根据pageSign缓存活动id集合，小时级汇总数据取去，不需要再查询一遍
        filterAndSaveIdTemporary(req.getPageSign(), req.getPuid(), req.getShopIdList(), req);
        return sumReportDayList;
    }

    /**
     * 根据pageSign缓存投放id集合，小时级汇总数据取去使用，不需要再查询一遍
     */
    private void filterAndSaveIdTemporary(String pageSign, Integer puid, List<Integer> shopIdList, TargetReqDto req) {
        // 需要将得到的Id进行过滤，过滤掉近63天没有报告的活动Id
        ThreadPoolUtil.getCpcAggregateIdsSyncPool().execute(() -> {
            List<String> validRecordIdList = new ArrayList<>();
            try {
                validRecordIdList = adManageTargetDorisDao.getValidRecordByDate(puid, shopIdList
                        , LocalDate.now().minusDays(63).format(DateTimeFormatter.BASIC_ISO_DATE), req);
            } catch (Exception e) {
                log.error("query valid campaign id error, puid:{}, shopIdList:{}, pageSign:{}, e:{}", puid, shopIdList, pageSign, e.getMessage());
            }
            cpcPageIdsHandler.addIdsTemporarySynchronize(puid, validRecordIdList, pageSign, "");
        });
    }

    /**
     * 组装汇总数据
     */
    private void buildSumReport(TargetReqDto req, TargetAggregateResp resp, List<ReportChartBase> reportDays) {
        CommonCompareReportRate aggregateDataVo = new CommonCompareReportRate();
        // 获取报告汇总数据 根据天累加
        ReportDorisBase sumReport = buildSumReport(reportDays);
        // 构建报告指标数据
        CpcCommPageNewVo commonReport = buildTargetReport(req, false, sumReport);
        // 填充报告数据
        ReportUtils.buildReport(aggregateDataVo, commonReport);
        // 环比数据
        if (req.getIsCompare()) {
            // 获取环比报告汇总数据
            ReportDorisBase compareSumReport = adManageTargetDorisDao.getSumReport(req, Boolean.TRUE);
            // 获取环比报告数据
            CpcCommPageNewVo compareVo = buildTargetReport(req, true, compareSumReport);
            // 填充环比值
            ReportUtils.buildCompareValue(aggregateDataVo, compareVo);
            // 填充环比增长率
            ReportUtils.buildCompareRate(aggregateDataVo);
        }
        // 是否包含vc店铺
        boolean isVc = req.getShopAuthList().stream().anyMatch(e -> ShopTypeEnum.VC.getCode().equals(e.getType()));
        buildReportExtra(req, true, null, aggregateDataVo, isVc);
        resp.setAggregateDataVo(aggregateDataVo);
    }

    /**
     * 构建报告指标数据
     */
    private CpcCommPageNewVo buildTargetReport(TargetReqDto req, boolean selCompareDate, ReportDorisBase sumReport) {
        // 查询店铺汇总数据
        BigDecimal shopSales;
        if (selCompareDate) {
            shopSales = dwsSaleProfitShopDayDao.sumShopSaleByDateRange(req.getPuid(), req.getShopIdList(), req.getCompareStartDate(), req.getCompareEndDate(), req.getChangeRate());
        } else {
            shopSales = req.getShopSale();
        }
        // 店铺销售额
        ShopSaleDto shopSaleDto = new ShopSaleDto();
        shopSaleDto.setSumRange(shopSales);
        // 填充汇总数据
        CpcCommPageNewVo campaignVo = new CpcCommPageNewVo();
        campaignVo.setSumCost(sumReport.getCostDoris() == null ? BigDecimal.ZERO : sumReport.getCostDoris());
        campaignVo.setSumAdSale(sumReport.getTotalSalesDoris() == null ? BigDecimal.ZERO : sumReport.getTotalSalesDoris());
        campaignVo.setSumAdOrderNum(sumReport.getOrderNumDoris() == null ? BigDecimal.ZERO : BigDecimal.valueOf(sumReport.getOrderNumDoris()));
        campaignVo.setSumOrderNum(sumReport.getSaleNumDoris() == null ? BigDecimal.ZERO : BigDecimal.valueOf(sumReport.getSaleNumDoris()));
        // 转公用报告基础数据对象
        ReportNewBase reportBase = ReportUtils.buildReportBase(sumReport, null);
        // 使用公共方法填充报告基础指标+计算指标数据
        cpcCommService.fillReportDataIntoPageNewVo(campaignVo, reportBase, shopSaleDto);
        return campaignVo;
    }

    /**
     * 按日累加得到汇总数据
     */
    private static ReportDorisBase buildSumReport(List<ReportChartBase> reportDays) {
        ReportDorisBase sumReport = new ReportChartBase();
        for (ReportChartBase reportDay : reportDays) {
            sumReport.setCostDoris(MathUtil.add(reportDay.getCostDoris(), sumReport.getCostDoris()));
            sumReport.setTotalSalesDoris(MathUtil.add(reportDay.getTotalSalesDoris(), sumReport.getTotalSalesDoris()));
            sumReport.setAdSalesDoris(MathUtil.add(reportDay.getAdSalesDoris(), sumReport.getAdSalesDoris()));
            sumReport.setImpressionsDoris(MathUtil.add(reportDay.getImpressionsDoris(), sumReport.getImpressionsDoris()));
            sumReport.setOrderNumDoris(MathUtil.add(reportDay.getOrderNumDoris(), sumReport.getOrderNumDoris()));
            sumReport.setClicksDoris(MathUtil.add(reportDay.getClicksDoris(), sumReport.getClicksDoris()));
            sumReport.setAdOrderNumDoris(MathUtil.add(reportDay.getAdOrderNumDoris(), sumReport.getAdOrderNumDoris()));
            sumReport.setSaleNumDoris(MathUtil.add(reportDay.getSaleNumDoris(), sumReport.getSaleNumDoris()));
            sumReport.setAdSaleNumDoris(MathUtil.add(reportDay.getAdSaleNumDoris(), sumReport.getAdSaleNumDoris()));
            sumReport.setViewImpressionsDoris(MathUtil.add(reportDay.getViewImpressionsDoris(), sumReport.getViewImpressionsDoris()));
            sumReport.setOrdersNewToBrand14dDoris(MathUtil.add(reportDay.getOrdersNewToBrand14dDoris(), sumReport.getOrdersNewToBrand14dDoris()));
            sumReport.setSalesNewToBrand14dDoris(MathUtil.add(reportDay.getSalesNewToBrand14dDoris(), sumReport.getSalesNewToBrand14dDoris()));
            sumReport.setUnitsOrderedNewToBrand14dDoris(MathUtil.add(reportDay.getUnitsOrderedNewToBrand14dDoris(), sumReport.getUnitsOrderedNewToBrand14dDoris()));
            sumReport.setNewToBrandDetailPageViewsDoris(MathUtil.add(reportDay.getNewToBrandDetailPageViewsDoris(), sumReport.getNewToBrandDetailPageViewsDoris()));
            sumReport.setAddToCartDoris(MathUtil.add(reportDay.getAddToCartDoris(), sumReport.getAddToCartDoris()));
            sumReport.setVideoFirstQuartileViewsDoris(MathUtil.add(reportDay.getVideoFirstQuartileViewsDoris(), sumReport.getVideoFirstQuartileViewsDoris()));
            sumReport.setVideo5secondViewsDoris(MathUtil.add(reportDay.getVideo5secondViewsDoris(), sumReport.getVideo5secondViewsDoris()));
            sumReport.setVideoMidpointViewsDoris(MathUtil.add(reportDay.getVideoMidpointViewsDoris(), sumReport.getVideoMidpointViewsDoris()));
            sumReport.setVideoThirdQuartileViewsDoris(MathUtil.add(reportDay.getVideoThirdQuartileViewsDoris(), sumReport.getVideoThirdQuartileViewsDoris()));
            sumReport.setVideoCompleteViewsDoris(MathUtil.add(reportDay.getVideoCompleteViewsDoris(), sumReport.getVideoCompleteViewsDoris()));
            sumReport.setVideoUnmutesDoris(MathUtil.add(reportDay.getVideoUnmutesDoris(), sumReport.getVideoUnmutesDoris()));
            sumReport.setBrandedSearches14dDoris(MathUtil.add(reportDay.getBrandedSearches14dDoris(), sumReport.getBrandedSearches14dDoris()));
            sumReport.setDetailPageView14dDoris(MathUtil.add(reportDay.getDetailPageView14dDoris(), sumReport.getDetailPageView14dDoris()));
            sumReport.setCumulativeReachDoris(MathUtil.add(reportDay.getCumulativeReachDoris(), sumReport.getCumulativeReachDoris()));
            sumReport.setImpressionsFrequencyAverageDoris(MathUtil.add(reportDay.getImpressionsFrequencyAverageDoris(), sumReport.getImpressionsFrequencyAverageDoris()));
        }
        sumReport.setMaxTopIsDoris(null);
        sumReport.setMinTopIsDoris(null);
        return sumReport;
    }

    /**
     * 组装列表响应参数
     */
    private Page<TargetResp> buildTargetResp(TargetReqDto req, Page<TargetResp> targetPage, Boolean export) {
        // 数据准备
        TargetDataDto dto = prepareData(req, targetPage, export);
        List<TargetResp> respList = new ArrayList<>();
        for (TargetResp row : targetPage.getRows()) {
            // 组装基础信息
            buildTargetBaseInfo(row, dto, req);
            // 各子层级构建响应参数
            abstractBuildParam(row, dto, req);
            // 组装额外信息
            buildTargetExtraInfo(row, dto, req);
            // 组装报告信息
            buildTargetReport(req, row, dto, export);
            respList.add(row);
        }
        return new Page<>(req.getPageNo(), req.getPageSize(), targetPage.getTotalPage(), targetPage.getTotalSize(), respList);
    }

    /**
     * 组装列表报告数据
     */
    private void buildTargetReport(TargetReqDto req, TargetResp row, TargetDataDto dto, Boolean export) {
        // 统计指标数据
        CpcCommPageNewVo commonReport = getCommonReport(row, dto.getAdMetricDto(), dto.getReportMap(), dto.getShopSalesMap());
        // 填充报告数据
        ReportUtils.buildReport(row, commonReport);
        // 列表查询切勾选对比才需要构建对比数据、导出不需要
        if (req.getIsCompare() && !export) {
            // 统计对比指标数据
            CpcCommPageNewVo compareVo = getCommonReport(row, null, dto.getCompareReportMap(), dto.getCompareShopSalesMap());
            // 填充环比值
            ReportUtils.buildCompareValue(row, compareVo);
            // 填充环比增长率
            ReportUtils.buildCompareRate(row);
        }
        ShopAuth shopAuth = dto.getShopAuthMap().get(row.getShopId());
        // 判断是否是vc店铺
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        // 填充报告额外参数
        buildReportExtra(req, false, row.getCostType(), row, isVc);
    }

    /**
     * // 填充报告额外参数
     */
    private <E extends CommonCompareReportRate> void buildReportExtra(TargetReqDto req, Boolean sum, String costType, CommonCompareReportRate row, boolean isVc) {
        // vcpm值特殊处理
        if (!SBCampaignCostTypeEnum.VCPM.getCode().equals(costType) && !sum) {
            row.setVcpm("-");
            if (req.getIsCompare()) {
                row.setCompareVcpmRate("-");
            }
        }
        // vc店铺字段特殊处理
        if(isVc){
            row.setAcots("-");
            row.setAsots("-");
            if (req.getIsCompare()) {
                row.setCompareAcotsRate("-");
                row.setCompareAcots("-");
                row.setCompareAsotsRate("-");
                row.setCompareAsots("-");
            }
        }
        // 多币种百分比字段展示“-”
        if (req.getChangeRate()) {
            row.setAdCostPercentage("-");
            row.setAdSalePercentage("-");
        }
    }

    /**
     * 统计指标数据
     */
    private CpcCommPageNewVo getCommonReport(TargetResp row, AdMetricDto adMetricDto, Map<String, TargetReportDoris> reportMap, Map<Integer, BigDecimal> shopSalesMap) {
        TargetReportDoris report = reportMap.getOrDefault(row.getTargetId(), new TargetReportDoris());
        BigDecimal shopSale = shopSalesMap.get(row.getShopId());
        CpcCommPageNewVo commPageNewVo = new CpcCommPageNewVo();
        ReportUtils.filterAdMetricData(adMetricDto, commPageNewVo);
        // 转公用报告基础数据对象
        ReportNewBase reportBase = ReportUtils.buildReportBase(report, row.getAdType());
        // 店铺销售额
        ShopSaleDto shopSaleDto = new ShopSaleDto();
        shopSaleDto.setSumRange(shopSale == null ? BigDecimal.ZERO : shopSale);
        // 使用公共方法填充报告基础指标+计算指标数据
        cpcCommService.fillReportDataIntoPageNewVo(commPageNewVo, reportBase, shopSaleDto);
        return commPageNewVo;
    }

    /**
     * 组装投放列表额外信息
     */
    private void buildTargetExtraInfo(TargetResp row, TargetDataDto dto, TargetReqDto req) {
        AmazonAdCampaignAll campaign = dto.getCampaignMap().get(row.getCampaignId());
        // 广告组合信息
        buildPortfolio(row, dto, campaign);
        // 广告活动信息
        buildCampaign(row, req, campaign);
        // 广告组信息
        buildGroupInfo(row, dto, req);
        // ABA搜索词排名信息
        buildAbaRank(row, dto);
        // 广告标签信息
        buildTagInfo(row, dto);
        // 广告策略信息
        buildAdStrategy(row, dto);
        // asin信息
        buildAsin(row, dto);
        // 构建竞价日志
        buildBidLog(row, dto, req);
    }

    /**
     * 构建竞价日志
     */
    private void buildBidLog(TargetResp row, TargetDataDto dto, TargetReqDto req) {
        // 竞价日志
        String logKey = null;
        if ("sp".equals(row.getAdType())) {
            if (CpcTargetiongHomeChosenTypeEnum.AUTO.getType().equalsIgnoreCase(req.getTargetType())) {
                logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.PREDEFINED_BID_AMOUNT.getCode(), row.getTargetId());
            } else if (CpcTargetiongHomeChosenTypeEnum.TARGETING.getType().equalsIgnoreCase(req.getTargetType())) {
                logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.PRODUCT_BID_AMOUNT.getCode(), row.getTargetId());
            } else if ("keyword".equalsIgnoreCase(req.getTargetType())) {
                logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.KEYWORD_BID_AMOUNT.getCode(), row.getTargetId());
            }
        }
        if (StringUtil.isNotEmpty(logKey) && dto.getAmazonAdOperationLogMap().containsKey(logKey)) {
            AmazonAdOperationLogBO logBO = dto.getAmazonAdOperationLogMap().get(logKey);
            if (StringUtils.isNotBlank(logBO.getContent())) {
                OperationContent operationContent = JSONUtil.jsonToObject(logBO.getContent(), OperationContent.class);
                if (operationContent != null) {
                    BidLog bidLog = new BidLog();
                    bidLog.setCount(logBO.getCount());
                    if (StringUtils.isNotBlank(operationContent.getPreviousValue())) {
                        bidLog.setPreviousValue(operationContent.getPreviousValue());
                    }
                    if (StringUtils.isNotBlank(operationContent.getNewValue())) {
                        bidLog.setNewValue(operationContent.getNewValue());
                    }
                    bidLog.setSiteOperationTime(logBO.getSiteOperationTime());
                    row.setBidLog(bidLog);
                }
            }
            row.setIsUpdateBid(Boolean.TRUE.toString());
        } else {
            if (StringUtil.isNotEmpty(logKey) && stringRedisService.get(logKey) != null) {
                row.setIsUpdateBid(Boolean.TRUE.toString());
            } else {
                row.setIsUpdateBid(Boolean.FALSE.toString());
            }
        }
    }

    /**
     * 构建asin信息
     */
    private static void buildAsin(TargetResp row, TargetDataDto dto) {
        if (StringUtil.isNotEmpty(row.getAsin()) && dto.getAsinMap().containsKey(row.getAsin().toUpperCase())) {
            AsinImage asinImage = dto.getAsinMap().get(row.getAsin());
            if (StringUtils.isEmpty(row.getTitle()) && asinImage != null) {
                row.setTitle(asinImage.getTitle());
            }
            if (StringUtils.isEmpty(row.getImgUrl()) && asinImage != null) {
                row.setImgUrl(asinImage.getImage());
            }
        }
    }

    /**
     * ABA搜索词排名信息
     */
    private static void buildAbaRank(TargetResp row, TargetDataDto dto) {
        if (StringUtil.isNotEmpty(row.getKeywordText())) {
            // ABA搜索词排名信息
            OdsWeekSearchTermsAnalysis termsAnalysis = dto.getAbaRankMap().get(row.getKeywordText().toLowerCase() + row.getMarketplaceId());
            if (termsAnalysis != null) {
                row.setSearchFrequencyRank(Optional.of(termsAnalysis).map(OdsWeekSearchTermsAnalysis::getSearchFrequencyRank).orElse(0));
                if (row.getSearchFrequencyRank() > 0) {
                    row.setWeekRatio(Optional.of(termsAnalysis).map(OdsWeekSearchTermsAnalysis::getWeekRatio).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP));
                }
            }
        }
    }

    /**
     * 广告组信息
     */
    private static void buildGroupInfo(TargetResp row, TargetDataDto dto, TargetReqDto req) {
        GroupInfo groupInfo = dto.getGroupInfoMap().get(row.getAdGroupId());
        if (groupInfo == null) {
            return;
        }
        // 广告组信息
        row.setAdGroupName(groupInfo.getName());
        row.setAdGroupState(groupInfo.getState());
        row.setGroupType(groupInfo.getAdGroupType());
        row.setIsAdGroupBidding(groupInfo.getIsStateBidding());
        row.setPricingAdGroupBidding(groupInfo.getPricingStateBidding());
        row.setDefaultBid(groupInfo.getDefaultBid());
        row.setAdFormat(groupInfo.getAdFormat());
        row.setCreativeType(groupInfo.getCreativeType());
        if (Constants.SB.equals(req.getAdType())) {
            row.setCampaignTargetingType(groupInfo.getAdFormat());
        }
    }

    /**
     * 广告组合信息
     */
    private static void buildPortfolio(TargetResp row, TargetDataDto dto, AmazonAdCampaignAll campaign) {
        if (campaign == null) {
            return;
        }
        // 广告组合信息
        AmazonAdPortfolio portfolio = dto.getPortfolioMap().get(campaign.getPortfolioId());
        if (portfolio != null) {
            row.setPortfolioId(portfolio.getPortfolioId());
            row.setPortfolioName(portfolio.getName());
            row.setIsHidden(portfolio.getIsHidden());
        } else {
            row.setPortfolioName("-");
        }
    }

    /**
     * 广告活动信息
     */
    private static void buildCampaign(TargetResp row, TargetReqDto req, AmazonAdCampaignAll campaign) {
        if (campaign == null) {
            return;
        }
        // 广告活动信息
        row.setCampaignName(campaign.getName());
        row.setCampaignState(campaign.getState());
        if (Constants.SD.equals(req.getAdType())) {
            row.setCampaignTargetingType(campaign.getTactic());
        } else {
            row.setCampaignTargetingType(campaign.getTargetingType());
        }
        row.setDailyBudget(campaign.getBudget() != null ? campaign.getBudget().toString() : null);
        row.setCostType(campaign.getCostType());
        if (Constants.SB.equals(req.getAdType())) {
            row.setSbType(campaign.getIsMultiAdGroupsEnabled());
            Optional.ofNullable(campaign.getAdGoal())
                    .filter(StringUtils::isNotEmpty)
                    .map(SBCampaignGoalEnum::getSBCampaignGoalEnumByType)
                    .map(SBCampaignGoalEnum::getCode)
                    .map(String::valueOf)
                    .ifPresent(row::setAdGoal);
        }
    }

    /**
     * 广告标签信息
     */
    private static void buildTagInfo(TargetResp row, TargetDataDto dto) {
        AdMarkupTagVo markupTagVo = dto.getAdMarkupTagVoMap().get(row.getTargetId());
        // 广告标签信息
        List<AdTag> adTags = new ArrayList<>();
        if (markupTagVo != null) {
            List<Long> tagIds = markupTagVo.getTagIds();
            if (CollectionUtil.isNotEmpty(tagIds)) {
                for (Long tagId : tagIds) {
                    AdTag adTag = dto.getAdTagMap().get(tagId);
                    adTags.add(adTag);
                }
            }
        }
        row.setAdTags(adTags);
    }

    /**
     * 广告策略信息
     */
    private static void buildAdStrategy(TargetResp row, TargetDataDto dto) {
        List<AdStrategyVo> adstrategyList = new ArrayList<>();
        // key:标签 value:状态集合
        Map<String, List<String>> strategyMap = new HashMap<>();
        if (dto.getAutoRuleMap().containsKey(row.getTargetId())) {
            // key 标签策略 status 状态集合  一个标签存在多种状态 如果存在已开启则为开启状态 否则为暂停状态
            Map<Integer, List<AdvertiseAutoRuleStatus>> autoRuleOperationMap = StreamUtil.groupingBy(dto.getAutoRuleMap().get(row.getTargetId()), AdvertiseAutoRuleStatus::getOperationType);
            for (Integer operationType : autoRuleOperationMap.keySet()) {
                List<String> statusList = StreamUtil.toListDistinct(autoRuleOperationMap.get(operationType), AdvertiseAutoRuleStatus::getStatus);
                String strategy = AdTargetStrategyTypeEnum.getStrategyMap().get(operationType);
                if (StringUtil.isNotEmpty(strategy)) {
                    List<String> statusAllList = strategyMap.getOrDefault(strategy, new ArrayList<>());
                    statusAllList.addAll(statusList);
                    strategyMap.put(strategy, statusAllList);
                }
            }
        }
        if (dto.getAutoRuleGroupMap().containsKey(row.getAdGroupId())) {
            // key 标签策略 status 状态集合  一个标签存在多种状态 如果存在已开启则为开启状态 否则为暂停状态
            Map<Integer, List<AdvertiseAutoRuleStatus>> autoRuleOperationMap = StreamUtil.groupingBy(dto.getAutoRuleGroupMap().get(row.getAdGroupId()), AdvertiseAutoRuleStatus::getOperationType);
            for (Integer operationType : autoRuleOperationMap.keySet()) {
                List<String> statusList = StreamUtil.toListDistinct(autoRuleOperationMap.get(operationType), AdvertiseAutoRuleStatus::getStatus);
                String strategy = AdTargetStrategyTypeEnum.getStrategyMap().get(operationType);
                if (StringUtil.isNotEmpty(strategy)) {
                    List<String> statusAllList = strategyMap.getOrDefault(strategy, new ArrayList<>());
                    statusAllList.addAll(statusList);
                    strategyMap.put(strategy, statusAllList);
                }
            }
        }
        if (dto.getStrategyStatusMap().containsKey(row.getTargetId())) {
            List<AdvertiseStrategyStatus> advertiseStatusList = dto.getStrategyStatusMap().get(row.getTargetId());
            List<String> statusList = StreamUtil.toListDistinct(advertiseStatusList, AdvertiseStrategyStatus::getStatus);
            strategyMap.put(AdTargetStrategyTypeEnum.BID_PRICING.getCode(), statusList);
        }
        // 广告策略标签
        for (String strategy : strategyMap.keySet()) {
            int status = 0;
            List<String> statusList = strategyMap.get(strategy);
            if (statusList.contains("ENABLED")) {
                status = 1;
            }
            AdStrategyVo strategyVo = new AdStrategyVo();
            strategyVo.setAdStrategyType(strategy);
            strategyVo.setStatus(status);
            adstrategyList.add(strategyVo);
        }
        if (SbMatchValueEnum.THEME.getMatchType().equalsIgnoreCase(row.getMatchType())) {
            // 过滤主题投放
            row.setAdStrategys(new ArrayList<>());
        } else {
            row.setAdStrategys(adstrategyList);
        }
    }

    /**
     * 组装投放列表基础信息
     */
    private void buildTargetBaseInfo(TargetResp row, TargetDataDto dto, TargetReqDto req) {
        ShopAuth shopAuth = dto.getShopAuthMap().get(row.getShopId());
        TargetInfo targetInfo = dto.getTargetMap().get(row.getTargetId());
        TargetExtendInfo targetExtendInfo = dto.getTargetExtendInfoMap().get(row.getTargetId());
        TargetInfo targetInfoDoris = dto.getTargetDorisMap().get(row.getTargetId());
        GroupInfo groupInfo = dto.getGroupInfoMap().get(targetInfo.getAdGroupId());
        row.setShopName(shopAuth.getName());
        // doris
        row.setMarketplaceId(targetInfo.getMarketplaceId());
        row.setAdType(req.getAdType());
        row.setTargetType(req.getTargetType());
        row.setProductTargetType(targetInfoDoris.getProductTargetType());
        row.setCampaignId(targetInfoDoris.getCampaignId());
        row.setAdGroupId(targetInfoDoris.getAdGroupId());
        row.setSelectType(targetInfoDoris.getSelectType());
        // 没有竞价则去广告组默认竞价
        row.setBid(targetInfoDoris.getBid() != null ? String.valueOf(targetInfoDoris.getBid().setScale(2, RoundingMode.HALF_UP)) : String.valueOf(groupInfo.getDefaultBid().setScale(2, RoundingMode.HALF_UP)));
        // mysql
        row.setSuggestBid(targetInfo.getSuggested() == null ? null : String.valueOf(DataFormatUtil.scale(targetInfo.getSuggested(), 2)));
        row.setRangeStart(targetInfo.getRangeStart() == null ? null : String.valueOf(DataFormatUtil.scale(targetInfo.getRangeStart(), 2)));
        row.setRangeEnd(targetInfo.getRangeEnd() == null ? null : String.valueOf(DataFormatUtil.scale(targetInfo.getRangeEnd(), 2)));
        row.setId(targetInfo.getId().toString());
        //扩展信息
        if(targetExtendInfo != null){
            row.setEstimatedImpressionUpper(targetExtendInfo.getEstimatedImpressionUpper() == null ? null : String.valueOf(targetExtendInfo.getEstimatedImpressionUpper()));
            row.setEstimatedImpressionLower(targetExtendInfo.getEstimatedImpressionLower() == null ? null : String.valueOf(targetExtendInfo.getEstimatedImpressionLower()));
        }
        // 服务状态与运行状态，若有作为条件查询，则取doris的，若无则取mysql的最新值
        row.setState(CollectionUtil.isNotEmpty(req.getStatusList()) ? targetInfoDoris.getState() : targetInfo.getState());
        row.setServingStatus(CollectionUtil.isNotEmpty(req.getServingStatusList()) ? targetInfoDoris.getServingStatus() : targetInfo.getServingStatus());
        row.setUpdateTime(targetInfo.getUpdateTime());
        // 币种
        row.setCurrency(AmznEndpoint.getByMarketplaceId(row.getMarketplaceId()).getCurrencyCode().value());
        // 域名
        row.setDomain(AmznEndpoint.getByMarketplaceId(targetInfoDoris.getMarketplaceId()).getDomain());
        // sd受众投放
        row.setAudienceTargetType(targetInfoDoris.getTargetType());
        if ("keyword".equals(row.getTargetType())) {
            // 关键词投放特殊处理
            buildKeywordInfo(row, dto, targetInfoDoris);
        } else {
            // 商品投放特殊处理
            buildTargetInfo(row, targetInfoDoris);
        }
    }

    public void buildTargetInfo(TargetResp row, TargetInfo targetInfoDoris) {
        row.setTargetText(targetInfoDoris.getTargetText());
        if (TargetTypeEnum.category.name().equals(row.getProductTargetType())) {
            row.setCategory(row.getTargetText());
            // 改版前路径是存在这个字段里的，因为要支持列表页模糊搜索改到了targeting_value
            if (StringUtils.isNotBlank(targetInfoDoris.getCategoryPath())) {
                row.setCategory(targetInfoDoris.getCategoryPath());
            }
            //如果为数字ID,表明类目或品牌已经被amazon删除
            if (StringUtils.isNumeric(row.getCategory())) {
                row.setCategory("此类目亚马逊已删除");
            }
        }
        // asin图片
        if (TargetTypeEnum.asin.name().equals(row.getProductTargetType())) {
            row.setAsin(targetInfoDoris.getTargetText());
            row.setTitle(targetInfoDoris.getTitle());
            row.setImgUrl(targetInfoDoris.getImgUrl());
        }
        // 品牌信息
        if (StringUtils.isNotBlank(targetInfoDoris.getResolvedExpression())
                && (TargetTypeEnum.category.name().equalsIgnoreCase(targetInfoDoris.getProductTargetType())
                || SBTargetingAudienceTypeEnum.fromValue(targetInfoDoris.getTargetType()) != null)) {
            JSONArray jsonArray = JSONArray.parseArray(targetInfoDoris.getResolvedExpression());
            if (jsonArray != null && !jsonArray.isEmpty()) {
                this.fillBrandMessage(row, jsonArray);
                if (TargetTypeEnum.category.name().equals(row.getProductTargetType())) {
                    row.setBrandName(StringUtils.isNotBlank(row.getBrandName()) ? row.getBrandName() : BrandMessageConstants.DEFAULT_BRAND_NAME);
                    row.setCommodityPriceRange(StringUtils.isNotBlank(row.getCommodityPriceRange()) ? row.getCommodityPriceRange() : BrandMessageConstants.DEFAULT_COMMODITY_PRICE_RANGE);
                    row.setRating(StringUtils.isNotBlank(row.getRating()) ? row.getRating() : BrandMessageConstants.DEFAULT_RATING);
                    row.setDistribution(StringUtils.isNotBlank(row.getDistribution()) ? row.getDistribution() : BrandMessageConstants.DEFAULT_DISTRIBUTION);
                }
            }
        }
    }

    public void buildKeywordInfo(TargetResp row, TargetDataDto dto, TargetInfo targetInfoDoris) {
        row.setKeywordId(row.getTargetId());
        row.setMatchType(targetInfoDoris.getMatchType());
        row.setKeywordText(targetInfoDoris.getKeywordText());
        row.setAdvRank(targetInfoDoris.getAdvRank());
        // 翻译词
        if (!MatchValueEnum.theme.getMatchType().equalsIgnoreCase(row.getMatchType())) {
            row.setKeywordTextCn(dto.getWordTranslateMap().get(wordTranslateService.getWordTranslateKey(row.getMarketplaceId(), row.getKeywordText())));
        }
    }

    /**
     * 填充品牌细节信息
     */
    private void fillBrandMessage(TargetResp row, JSONArray jsonArray) {
        if (jsonArray == null || jsonArray.isEmpty()) return;
        try {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String value = jsonObject.getString("type");
                // T00030 TargetingPredicateNested
                if (SBTargetingAudienceTypeEnum.fromValue(value) != null) {
                    JSONArray valueArray = ResolvedExpressionParseHelper.getJsonValueAsArray(jsonObject);
                    fillBrandMessage(row, valueArray);
                }
                if (ExpressionEnum.asinBrandSameAs.value().equals(value) || SpV3ExpressionEnum.asinBrandSameAs.getValueV3().equals(value)) {
                    row.setBrandName(jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinPriceBetween.value().equals(value) || SpV3ExpressionEnum.asinPriceBetween.getValueV3().equals(value)) {
                    row.setCommodityPriceRange(jsonObject.getString("value").replace("-", ","));
                }
                if (ExpressionEnum.asinPriceLessThan.value().equals(value) || SpV3ExpressionEnum.asinPriceLessThan.getValueV3().equals(value)) {
                    row.setCommodityPriceRange(jsonObject.getString("value") + " 及以下");
                }
                if (ExpressionEnum.asinPriceGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinPriceGreaterThan.getValueV3().equals(value)) {
                    row.setCommodityPriceRange(jsonObject.getString("value") + " 及更高");
                }
                if (ExpressionEnum.asinReviewRatingBetween.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingBetween.getValueV3().equals(value)) {
                    row.setRating(jsonObject.getString("value").replace('-', '~'));
                }
                if (ExpressionEnum.asinReviewRatingLessThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingLessThan.getValueV3().equals(value)) {
                    row.setRating("1~" + jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinReviewRatingGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingGreaterThan.getValueV3().equals(value)) {
                    row.setRating(jsonObject.getString("value") + "~5");
                }
                if (ExpressionEnum.asinIsPrimeShippingEligible.value().equals(value) || SpV3ExpressionEnum.asinIsPrimeShippingEligible.getValueV3().equals(value)) {
                    if ("true".equalsIgnoreCase(jsonObject.getString("value"))) {
                        row.setDistribution("具有Prime资格");
                    } else if ("false".equalsIgnoreCase(jsonObject.getString("value"))) {
                        row.setDistribution("不具有Prime资格");
                    }
                }
                if (ExpressionEnum.lookback.value().equals(value)) {
                    row.setLookback(jsonObject.getString("value"));
                }
            }
        } catch (Exception e) {
            log.error("解析json失败", e);
        }
    }

    /**
     * 数据准备
     * 存在性能瓶颈时 此步骤可以开启多线程并行获取
     */
    private TargetDataDto prepareData(TargetReqDto req, Page<TargetResp> targetPage, Boolean export) {
        TargetDataDto dto = new TargetDataDto();
        // 获取汇总信息
        dto.setAdMetricDto(adManageTargetDorisDao.getSumAdMetricMultiple(req));
        // 分页后的投放id集合
        req.setTargetIds(StreamUtil.toList(targetPage.getRows(), TargetResp::getTargetId));
        // 分页的店铺id集合
        req.setShopIdList(StreamUtil.toListDistinct(targetPage.getRows(), TargetResp::getShopId));
        // 店铺信息
        dto.setShopAuthMap(StreamUtil.toMap(req.getShopAuthList(), ShopAuth::getId));
        // mysql投放基础信息
        List<TargetInfo> targetMysqlList = abstractMysqlTargetList(req);
        dto.setTargetMap(StreamUtil.toMap(targetMysqlList, TargetInfo::getTargetId));
        // 广告组信息
        List<String> groupIdList = StreamUtil.toListDistinct(targetMysqlList, TargetInfo::getAdGroupId);
        List<GroupInfo> groupInfoList = abstractGroupInfoList(req, groupIdList);
        dto.setGroupInfoMap(StreamUtil.toMap(groupInfoList, GroupInfo::getAdGroupId));
        // 异步获取数据
        asyncPrepareData(req, export, dto, targetMysqlList);
        return dto;
    }

    /**
     * 异步多线程获取数据
     */
    private void asyncPrepareData(TargetReqDto req, Boolean export, TargetDataDto dto, List<TargetInfo> targetMysqlList) {
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        // 导出限制6w条数据分批获取
        List<List<String>> targetIdsList = Lists.partition(req.getTargetIds(), 10000);
        // 异步获取报告
        setReportFeature(req, export, dto, futureList);
        // 异步获取店铺销售额
        setShopSaleFeature(req, export, dto, futureList);
        // 异步获取doris投放基础信息
        setDorisFeature(req, export, dto, futureList);
        // 异步获取广告活动、广告组信息
        setCampaignPortfolioFeature(req, export, targetMysqlList, dto, futureList);
        // 异步获取自动化规则策略信息
        setAutoRuleFeature(req, export, targetIdsList, dto, futureList);
        // 异步获取自动化规则组受控策略信息信息
        setAutoRuleGroupFeature(req, export, dto, futureList);
        // 异步获取分时策略信息
        setStrategyFeature(req, export, targetIdsList, dto, futureList);
        // 异步获取广告标签信息
        setTagFeature(req, export, targetIdsList, dto, futureList);
        // 异步获取子类数据准备
        setAbstractFeature(req, export, dto, futureList);
        if (!export && req.getIsCompare()) {
            // 异步获取对比报告数据
            setCompareReportFeature(req, export, dto, futureList);
            // 获取对比店铺销售额
            setCompareShopSaleFeature(req, export, dto, futureList);
        }
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        try {
            // 等待所有 CompletableFuture 完成
            allOf.join();
        } catch (CompletionException e) {
            log.error("Error occurred while waiting for CompletableFuture completion", e);
        }
    }

    /**
     * 获取对比店铺销售额
     */
    private void setCompareShopSaleFeature(TargetReqDto req, Boolean export, TargetDataDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            TargetReqDto compareReq = BeanUtil.copyProperties(req, TargetReqDto.class);
            compareReq.setStartDate(req.getCompareStartDate());
            compareReq.setEndDate(req.getCompareEndDate());
            List<DwsSaleProfitShopDay> compareShopSalesList = dwsSaleProfitShopDayDao.listShopSaleByDateRange(compareReq.getPuid(), compareReq.getShopIdList(), compareReq.getCompareStartDate(), compareReq.getCompareEndDate());
            dto.setCompareShopSalesMap(StreamUtil.toMap(compareShopSalesList, DwsSaleProfitShopDay::getShopId, DwsSaleProfitShopDay::getSalePrice));
            return null;
        }, getExecutor(export));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取对比报告数据
     */
    private void setCompareReportFeature(TargetReqDto req, Boolean export, TargetDataDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            TargetReqDto compareReq = BeanUtil.copyProperties(req, TargetReqDto.class);
            compareReq.setStartDate(req.getCompareStartDate());
            compareReq.setEndDate(req.getCompareEndDate());
            List<TargetReportDoris> compareReportList = adManageTargetDorisDao.listTargetReport(compareReq, false);
            dto.setCompareReportMap(StreamUtil.toMap(compareReportList, TargetReportDoris::getTargetId));
            return null;
        }, getExecutor(export));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取子类数据准备
     */
    private void setAbstractFeature(TargetReqDto req, Boolean export, TargetDataDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            abstractPrepareData(req, export, dto);
            return null;
        }, getExecutor(export));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取广告标签信息
     */
    private void setTagFeature(TargetReqDto req, Boolean export, List<List<String>> targetIdsList, TargetDataDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            List<AdMarkupTagVo> relationVos = new ArrayList<>();
            for (List<String> targetIds : targetIdsList) {
                relationVos.addAll(adMarkupTagDao.getRelationVosByShopIdList(req.getPuid(), req.getShopIdList(), AdTagTypeEnum.TARGET.getType(), req.getAdType(), req.getTargetTypeEnum().getTargetTagEnum().getType(), null, targetIds));
            }
            List<Long> tagIdList = relationVos.stream().map(AdMarkupTagVo::getTagIds).flatMap(Collection::stream).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(tagIdList)) {
                List<List<Long>> tagIdsList = Lists.partition(tagIdList, 10000);
                List<AdTag> tagList = new ArrayList<>();
                for (List<Long> tagIds : tagIdsList) {
                    tagList.addAll(adTagDao.getListByLongIdList(req.getPuid(), tagIds));
                }
                dto.setAdTagMap(StreamUtil.toMap(tagList, AdTag::getId));
                dto.setAdMarkupTagVoMap(StreamUtil.toMap(relationVos, AdMarkupTagVo::getRelationId));
            }
            return null;
        }, getExecutor(export));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取分时策略信息
     */
    private void setStrategyFeature(TargetReqDto req, Boolean export, List<List<String>> targetIdsList, TargetDataDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            List<AdvertiseStrategyStatus> strategyStatuses = new ArrayList<>();
            for (List<String> targetIds : targetIdsList) {
                strategyStatuses.addAll(advertiseStrategyStatusDao.getByShopIdAndItemIds(req.getPuid(), req.getShopIdList(), ItemTypeEnum.TARGET.getItemType(), targetIds, req.getTargetTypeEnum().getAutoRuleTargetTypeEnum().getTargetType(), req.getAdType().toUpperCase()));
            }
            dto.setStrategyStatusMap(StreamUtil.groupingBy(strategyStatuses, AdvertiseStrategyStatus::getItemId));
            return null;
        }, getExecutor(export));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取自动化规则策略信息
     */
    private void setAutoRuleFeature(TargetReqDto req, Boolean export, List<List<String>> targetIdsList, TargetDataDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            List<AdvertiseAutoRuleStatus> autoRuleStatuses = new ArrayList<>();
            for (List<String> targetIds : targetIdsList) {
                autoRuleStatuses.addAll(advertiseAutoRuleStatusDao.listByItemIdMutiple(req.getPuid(), req.getShopIdList(), AutoRuleItemTypeEnum.TARGET.getName(), targetIds, AutoRuleChildrenItemTypeEnum.CHILDREN_TARGET.toString()));
            }
            dto.setAutoRuleMap(StreamUtil.groupingBy(autoRuleStatuses, AdvertiseAutoRuleStatus::getItemId));
            return null;
        }, getExecutor(export));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取广告组、自动化规则组受控策略信息信息
     */
    private void setAutoRuleGroupFeature(TargetReqDto req, Boolean export, TargetDataDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            // 自动化规则组受控策略信息
            List<String> groupIdList = StreamUtil.toList(CollectionUtil.newArrayList(dto.getGroupInfoMap().values()), GroupInfo::getAdGroupId);
            List<AdvertiseAutoRuleStatus> autoRuleGroupStatuses = advertiseAutoRuleStatusDao.listByItemIdMutiple(req.getPuid(), req.getShopIdList(), AutoRuleItemTypeEnum.TARGET.getName(), groupIdList, AutoRuleChildrenItemTypeEnum.CHILDREN_TARGET_GROUP.toString());
            dto.setAutoRuleGroupMap(StreamUtil.groupingBy(autoRuleGroupStatuses, AdvertiseAutoRuleStatus::getItemId));
            return null;
        }, getExecutor(export));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取广告活动、广告组信息
     */
    private void setCampaignPortfolioFeature(TargetReqDto req, Boolean export, List<TargetInfo> targetMysqlList, TargetDataDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            // 广告活动信息
            List<String> campaignIdList = StreamUtil.toListDistinct(targetMysqlList, TargetInfo::getCampaignId);
            List<AmazonAdCampaignAll> campaignList = amazonAdCampaignAllDao.listByShopIdAndCampaignIds(req.getPuid(), req.getShopIdList(), campaignIdList);
            dto.setCampaignMap(StreamUtil.toMap(campaignList, AmazonAdCampaignAll::getCampaignId));
            // 广告组合信息
            List<String> portfolioIds = campaignList.stream().map(AmazonAdCampaignAll::getPortfolioId).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                dto.setPortfolioMap(portfolioDao.listByShopId(req.getPuid(), req.getShopIdList(), portfolioIds).stream()
                        .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e)));
            }
            return null;
        }, getExecutor(export));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取doris投放数据
     */
    private void setDorisFeature(TargetReqDto req, Boolean export, TargetDataDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            List<TargetInfo> targetDorisList = abstractDorisTargetList(req);
            dto.setTargetDorisMap(StreamUtil.toMap(targetDorisList, TargetInfo::getTargetId));
            return null;
        }, getExecutor(export));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取店铺销售额数据
     */
    private void setShopSaleFeature(TargetReqDto req, Boolean export, TargetDataDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            List<DwsSaleProfitShopDay> shopSalesList = dwsSaleProfitShopDayDao.listShopSaleByDateRange(req.getPuid(), req.getShopIdList(), req.getStartDate(), req.getEndDate());
            dto.setShopSalesMap(StreamUtil.toMap(shopSalesList, DwsSaleProfitShopDay::getShopId, DwsSaleProfitShopDay::getSalePrice));
            return null;
        }, getExecutor(export));
        futureList.add(reportFuture);
    }

    /**
     * 异步获取报告数据
     */
    private void setReportFeature(TargetReqDto req, Boolean export, TargetDataDto dto, List<CompletableFuture<Void>> futureList) {
        CompletableFuture<Void> reportFuture = CompletableFuture.supplyAsync(() -> {
            List<TargetReportDoris> reportList = adManageTargetDorisDao.listTargetReport(req, export);
            dto.setReportMap(StreamUtil.toMap(reportList, TargetReportDoris::getTargetId));
            return null;
        }, getExecutor(export));
        futureList.add(reportFuture);
    }

    /**
     * 获取异步线程池
     */
    private ThreadPoolTaskExecutor getExecutor(Boolean export) {
        if (export) {
            return adExportExecutor;
        } else {
            return targetAdPageExecutor;
        }
    }

    /**
     * 准备ABA排名数据
     */
    public void prepareAbaRank(TargetReqDto req, Boolean export, List<TargetInfo> targetMysqlList, TargetDataDto dto) {
        List<String> searchTerms = StreamUtil.toListDistinct(targetMysqlList, TargetInfo::getKeywordText);
        List<String> marketplaceIdList = StreamUtil.toListDistinct(targetMysqlList, TargetInfo::getMarketplaceId);
        List<OdsWeekSearchTermsAnalysis> analysisList = weekSearchTermsAnalysisService.queryRanks(searchTerms, marketplaceIdList, req.getAbaRankDateList(), req.getMarketplaceAbaRankDateList());
        dto.setAbaRankMap(StreamUtil.toMap(analysisList, it -> it.getSearchTerm() + it.getMarketplaceId()));
        // 获取实时翻译词
        List<WordTranslateQo> wordTranslateQos = targetMysqlList.stream().map(e -> new WordTranslateQo(e.getMarketplaceId(), e.getKeywordText())).collect(Collectors.toList());
        dto.setWordTranslateMap(wordTranslateService.getWordTranslateMap(req.getPuid(), wordTranslateQos, export));
    }

    /**
     * 获取asin信息填充asin、图片信息
     */
    public Map<String, AsinImage> getAsinMap(TargetReqDto req, List<TargetInfo> targetMysqlList) {
        Map<String, List<String>> asinMarketplaceMap = StreamUtil.groupingBy(targetMysqlList, TargetInfo::getMarketplaceId, it -> {
            if (TargetTypeEnum.asin.name().equals(it.getProductTargetType())) {
                return it.getTargetText();
            }
            return null;
        });
        List<AsinImage> asinImageList = new ArrayList<>();
        for (String marketplaceId : asinMarketplaceMap.keySet()) {
            List<String> asinList = asinMarketplaceMap.get(marketplaceId);
            if (CollectionUtil.isNotEmpty(asinList)) {
                List<AsinImage> listByAsins = syncAsinImageService.getListByAsinsNoSave(req.getPuid(), marketplaceId, asinList);
                asinImageList.addAll(listByAsins);
            }
        }
        return asinImageList.stream().filter(e -> StringUtils.isNotBlank(e.getAsin())).collect(Collectors.toMap(e -> e.getAsin().toUpperCase(), e1 -> e1, (e2, e3) -> e3));
    }

    /**
     * 获取竞价日志
     */
    public void getOperationLogMap(TargetReqDto req, TargetDataDto dto) {
        // 24小时前的日志
        Date lastDate = DateUtil.addDay(new Date(), -1);
        Integer entityType = AmazonAdOperationLogEntityTypeEnum.PRODUCT_TARGETING.getCode();
        Integer changeType = null;
        if ("keyword".equals(req.getTargetType())) {
            entityType = AmazonAdOperationLogEntityTypeEnum.KEYWORD.getCode();
            changeType = AmazonAdOperationLogChangeTypeEnum.KEYWORD_BID_AMOUNT.getCode();
        }
        Map<String, AmazonAdOperationLogBO> operationLogBOMap = amazonAdOperationLogService.listGroupByTypeAndIdentifyIdAndShopIdList(req.getPuid(), req.getShopIdList(), Constants.SP, entityType, changeType, req.getTargetIds(), lastDate);
        dto.setAmazonAdOperationLogMap(operationLogBOMap);
    }

    /**
     * 请求参数校验
     */
    private void checkParam(TargetReqDto req) {
        // 店铺校验
        if (CollectionUtil.isEmpty(req.getShopIdList())) {
            throw new SponsoredBizException("您没有店铺访问权限，请联系管理员");
        }
        // 获取店铺信息
        List<ShopAuth> shopAuths = shopAuthDao.listValidShopByIds(req.getPuid(), req.getShopIdList());
        req.setShopAuthList(shopAuths);
        if (CollectionUtils.isEmpty(shopAuths)) {
            throw new SponsoredBizException("您没有店铺访问权限，请联系管理员");
        }
        // 过滤已删除无效店铺
        req.setShopIdList(StreamUtil.toList(shopAuths, ShopAuth::getId));
        // 对于大量刷投放数据的puid增加黑名单配置，只能单选店铺，保护doris资源
        if (CollectionUtil.isNotEmpty(adManageLimitConfig.getTargetBlackList())
                && adManageLimitConfig.getTargetBlackList().contains(req.getPuid().toString()) && req.getShopIdList().size() > 1) {
            throw new SponsoredBizException("当前所选数据量过大，仅支持单选店铺");
        }
        // 子类参数校验
        abstractCheckParam(req);
    }

    /**
     * 参数赋值
     */
    private void setParam(TargetReqDto req) {
        // 环比
        req.setIsCompare(req.getIsCompare() != null && req.getIsCompare() && StringUtil.isNotEmpty(req.getCompareStartDate()) && StringUtil.isNotEmpty(req.getCompareEndDate()));
        // 站点id集合
        req.setMarketplaceIdList(StreamUtil.toListDistinct(req.getShopAuthList(), ShopAuth::getMarketplaceId));
        // 判断是否是多店铺且币种不同需要换币种
        req.setChangeRate(MultipleUtils.changeRate(req.getShopAuthList()));
        // 列表页处理高级筛选百分比参数
        if (req.getUseAdvanced() && req.getAdvanceFilter() != null) {
            // 高级刷选转成列表集合
            ReportUtils.handlePercentParam(req.getAdvanceFilter());
            // 高级筛选反射转换成集合 方便后续通过枚举获取去掉if else判断
            req.setAdvanceFilterVoList(ReportUtils.advanceFilterChange(req.getAdvanceFilter()));
        }
        // 兼容前端异常场景 传的广告组合在店铺下不存在时清空广告组合筛选
        if (CollectionUtil.isNotEmpty(req.getPortfolioIdList())) {
            if(!req.getPortfolioIdList().contains("-1")){
                Integer count = odsAmazonAdPortfolioDao.countPortfolioList(req.getPuid(), req.getShopIdList(), req.getPortfolioIdList());
                if(count == 0){
                    req.setPortfolioIdList(null);
                }
            }
        }
        // 子类参数赋值
        abstractSetParam(req);
        // 多店铺下获取投放数是否超过限制
        if (req.getShopIdList().size() > 1) {
            Integer count = adManageTargetDorisDao.countTarget(req);
            if (count > adManageLimitConfig.getTargetBaseLimit()) {
                throw new SponsoredBizException("当前所选数据量过大，请减少店铺后查询");
            }
        }
    }

    /**
     * 设置ABA排名信息
     */
    public void setAbaRankInfo(TargetReqDto req) {
        Map<String, String> latestDateMap = weekSearchTermsAnalysisService.getLatestDateList(req.getMarketplaceIdList());
        // 站点最近的周排名日期
        req.setAbaRankDateList((CollectionUtil.newArrayList(latestDateMap.values())));
        List<String> marketplaceAbaRankDateList = new ArrayList<>();
        for (String marketplaceId : latestDateMap.keySet()) {
            String abaRankDate = latestDateMap.get(marketplaceId);
            String marketplaceAbaRankDate = marketplaceId + "|" + abaRankDate;
            marketplaceAbaRankDateList.add(marketplaceAbaRankDate);
        }
        // 站点id+|+周排名日期拼接 用于筛选过滤
        req.setMarketplaceAbaRankDateList(marketplaceAbaRankDateList);
    }

    /**
     * 前置过滤投放id集合
     */
    private Boolean filterTargetIds(TargetReqDto req) {
        // 1.标签刷选
        if (CollectionUtils.isNotEmpty(req.getAdTagIdList())) {
            List<String> relationIds = adMarkupTagDao.getMultipleRelationIds(req.getPuid(), req.getShopIdList(), AdTagTypeEnum.TARGET.getType(),
                    req.getAdTagIdList(), req.getAdType(), req.getTargetTypeEnum().getAdMarkupTargetTypeEnum().getType());
            if (CollectionUtils.isNotEmpty(relationIds)) {
                req.setTargetIds(relationIds);
            } else {
                return true;
            }
        }
        // 2.自动化规则筛选投放id集合、组id集合
        List<Integer> operationTypeList = AdTargetStrategyTypeEnum.operationTypeList(req.getAdStrategyTypeList());
        if (CollectionUtils.isNotEmpty(operationTypeList)) {
            List<String> targetIdList = advertiseAutoRuleStatusDao.listItemIdByAdManage(req.getPuid(), req.getShopIdList(), AutoRuleItemTypeEnum.TARGET.getName(),
                    operationTypeList, req.getAdType(), AutoRuleChildrenItemTypeEnum.CHILDREN_TARGET.toString(), req.getTargetTypeEnum().getAutoRuleTargetTypeEnum().getTargetType(), null);
            req.setAutoRuleIds(targetIdList);
            List<String> groupIdList = advertiseAutoRuleStatusDao.listItemIdByAdManage(req.getPuid(), req.getShopIdList(), AutoRuleItemTypeEnum.TARGET.getName(),
                    operationTypeList, req.getAdType(), AutoRuleChildrenItemTypeEnum.CHILDREN_TARGET_GROUP.toString(), null, null);
            req.setAutoRuleGroupIds(groupIdList);
            if (CollectionUtils.isEmpty(targetIdList) && CollectionUtils.isEmpty(groupIdList) &&
                    !req.getAdStrategyTypeList().contains(AdTargetStrategyTypeEnum.NONE.getCode()) &&
                    !req.getAdStrategyTypeList().contains(AdTargetStrategyTypeEnum.BID_PRICING.getCode())) {
                // 只存在自动化规则筛选没数据时返回
                return true;
            }
        }
        // 3.分时策略筛选关键词id集合
        if (CollectionUtil.isNotEmpty(req.getAdStrategyTypeList()) && (req.getAdStrategyTypeList().contains(AdTargetStrategyTypeEnum.BID_PRICING.getCode()) ||
                req.getAdStrategyTypeList().contains(AdTargetStrategyTypeEnum.NONE.getCode())) &&
                req.getAdStrategyTypeList().size() != AdTargetStrategyTypeEnum.values().length) {
            List<String> itemIdList = advertiseStrategyStatusDao.listAllItemId(req.getPuid(), req.getShopIdList(), ItemTypeEnum.TARGET.getItemType(),
                    req.getTargetTypeEnum().getAutoRuleTargetTypeEnum().getTargetType(), req.getAdType().toUpperCase());
            if (CollectionUtil.isNotEmpty(req.getAutoRuleIds())) {
                itemIdList.addAll(req.getAutoRuleIds());
                itemIdList = itemIdList.stream().distinct().collect(Collectors.toList());
            }
            if (CollectionUtil.isNotEmpty(itemIdList)) {
                req.setAutoRuleIds(itemIdList);
            } else if (!req.getAdStrategyTypeList().contains(AdTargetStrategyTypeEnum.NONE.getCode())) {
                return true;
            }
        }
        // 4.子类特殊筛选
        if (abstractFilterTargetIds(req)) {
            return true;
        }
        return false;
    }

    /**
     * 抽象方法，子类参数校验
     *
     * @param req
     */
    public abstract void abstractCheckParam(TargetReqDto req);

    /**
     * 抽象方法，子类参数赋值
     *
     * @param req
     */
    public abstract void abstractSetParam(TargetReqDto req);

    /**
     * 抽象方法，子类前置过滤投放id
     *
     * @param req
     */
    public abstract Boolean abstractFilterTargetIds(TargetReqDto req);

    /**
     * 抽象方法，查询where条件
     *
     * @param req
     */
    public abstract void abstractBuildWhereSql(TargetReqDto req, StringBuilder whereSql, List<Object> argsList);

    /**
     * 抽象方法，数据准备
     *
     * @param req
     */
    public abstract void abstractPrepareData(TargetReqDto req, Boolean export, TargetDataDto dto);

    /**
     * 抽象方法，子类参数赋值
     *
     * @param req
     */
    public abstract void abstractBuildParam(TargetResp row, TargetDataDto dto, TargetReqDto req);

    /**
     * 抽象方法，根据投放id获取投放详情
     *
     * @param req 请求参数
     */
    public abstract List<TargetInfo> abstractMysqlTargetList(TargetReqDto req);

    /**
     * 抽象方法，根据投放id获取投放详情
     *
     * @param req 请求参数
     */
    public abstract List<TargetInfo> abstractDorisTargetList(TargetReqDto req);

    /**
     * 抽象方法，根据组id获取组详情
     *
     * @param req 请求参数
     */
    public abstract List<GroupInfo> abstractGroupInfoList(TargetReqDto req, List<String> adGroupIdList);

    /**
     * 抽象方法，导出过滤字段*
     */
    public abstract List<String> excludeFiledList(TargetReqDto req);
}
