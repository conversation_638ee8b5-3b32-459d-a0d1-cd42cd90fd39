package com.meiyunji.sponsored.service.cpc.service2.sb.impl;

import com.alibaba.fastjson.JSONObject;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.amazon.advertising.sb.mode.targeting.SBResolvedExpression;
import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.sb.neTarget.BatchSbDataResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdNeTargetingDao;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbGroupService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbNeTargetService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;

import com.meiyunji.sponsored.service.enums.MarketplaceTimeZoneEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdSbNeTargeting;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.SBCreateErrorEnum;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by lm on 2021/8/5.
 */
@Service
@Slf4j
public class CpcSbNeTargetServiceImpl implements ICpcSbNeTargetService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private CpcSbNeTargetApiService cpcSbNeTargetApiService;
    @Autowired
    private IAmazonSbAdGroupDao groupDao;
    @Autowired
    private IAmazonSbAdNeTargetingDao neTargetingDao;
    @Autowired
    private ICpcSbGroupService cpcSbGroupService;
    @Autowired
    private IAdManageOperationLogService adOperationLogService;
    @Autowired
    private IDorisService dorisService;

    @Override
    public Result pageList(int puid, SbTargetingPageParam param) {
        Page<AmazonSbAdNeTargeting> page = neTargetingDao.pageList(puid, param);
        Page<SdNeTargetingPageVo> voPage = new Page<>();
        BeanUtils.copyProperties(page, voPage);
        if (CollectionUtils.isNotEmpty(page.getRows())) {
            String domain = AmznEndpoint.getByMarketplaceId(page.getRows().get(0).getMarketplaceId()).getDomain();

            List<SdNeTargetingPageVo> list = new ArrayList<>(page.getRows().size());
            voPage.setRows(list);
            SdNeTargetingPageVo vo;
            for (AmazonSbAdNeTargeting amazonAdTargeting : page.getRows()) {
                vo = new SdNeTargetingPageVo();
                list.add(vo);
                vo.setShopId(amazonAdTargeting.getShopId());
                vo.setTargetId(amazonAdTargeting.getTargetId());
                vo.setState(amazonAdTargeting.getState());
                vo.setType(amazonAdTargeting.getType());
                vo.setCreateTime(DateUtil.dateToStrWithTime(amazonAdTargeting.getCreateTime(), DateUtil.PATTERN_DATE_TIME));
                vo.setDomain(domain);

                // asin图片
                if (TargetTypeEnum.asin.name().equals(amazonAdTargeting.getType())) {
                    vo.setAsin(amazonAdTargeting.getTargetText());
                    vo.setImgUrl(amazonAdTargeting.getImgUrl());
                    vo.setTitle(amazonAdTargeting.getTitle());
                }
            }
        }

        return ResultUtil.returnSucc(voPage);
    }

    @Override
    public Result create(AddSbNeTargetingVo neTargetingVo) {

        Integer puid = neTargetingVo.getPuid();
        Integer shopId = neTargetingVo.getShopId();
        String campaignId = neTargetingVo.getCampaignId();
        String groupId = neTargetingVo.getGroupId();
        List<SbNeTargetsVo> neTargetsVos = neTargetingVo.getNeTargetsVos();
        // 校验参数
        String msg = checkAddNeTargetingVo(neTargetsVos);
        if (StringUtils.isNotBlank(msg)) {
            return ResultUtil.error(msg);
        }

        AmazonSbAdGroup adGroup = groupDao.getByGroupId(puid, shopId, groupId);
        if (adGroup == null) {
            return ResultUtil.returnErr("没有广告组信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        List<SbBatchNeTargetsVo> batchNeTargetsVos = new ArrayList<>(neTargetsVos.size());
        List<AmazonSbAdNeTargeting> neTargetList = new ArrayList<>(neTargetsVos.size());
        SbBatchNeTargetsVo batchNeTargetsVo;
        for (SbNeTargetsVo neTargetVo : neTargetsVos) {
            batchNeTargetsVo = new SbBatchNeTargetsVo();
            batchNeTargetsVo = convertBatchAddNeTargetingVo(neTargetVo, neTargetingVo.getUid(), adGroup);
            batchNeTargetsVos.add(batchNeTargetsVo);
        }

        Result result = cpcSbNeTargetApiService.create(shop, profile, batchNeTargetsVos);
        if (!result.success()) {
            return result;
        }

        if (CollectionUtils.isNotEmpty(batchNeTargetsVos)) {
            batchNeTargetsVos.forEach(item -> {
                AmazonSbAdNeTargeting sbAdNeTargeting = new AmazonSbAdNeTargeting();
                BeanUtils.copyProperties(item, sbAdNeTargeting);
                sbAdNeTargeting.setState("enabled");
                neTargetList.add(sbAdNeTargeting);
            });
        }

        logSbNeTargetCreate(neTargetList, neTargetingVo.getIp(), result);

        List<AmazonSbAdNeTargeting> succList = neTargetList.stream().filter(e -> StringUtils.isNotBlank(e.getTargetId()))
                .collect(Collectors.toList());
        if (succList.size() > 0) {
            // 有可能已经添加过了
            List<String> existInDB = neTargetingDao.listByTargetId(neTargetingVo.getPuid(), shopId, succList.stream()
                    .map(AmazonSbAdNeTargeting::getTargetId).collect(Collectors.toList())).stream()
                    .map(AmazonSbAdNeTargeting::getTargetId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getTargetId())).collect(Collectors.toList());
            }

            // 入库
            try {
                neTargetingDao.batchAdd(neTargetingVo.getPuid(), succList);
                List<String> targetIds = succList.stream().map(AmazonSbAdNeTargeting::getTargetId).collect(Collectors.toList());
                saveDoris(neTargetingVo.getPuid(), neTargetingVo.getShopId(), targetIds);
            } catch (Exception e) {
                log.error("createSbNeTargeting:", e);
            }

            List<AmazonSbAdNeTargeting> finalSuccList = succList;
            ThreadPoolUtil.getNegativeSyncPool().execute(()->{
                try {
                    //创建成功, 需要在同步 获取投放状态
                    cpcSbNeTargetApiService.syncNeTargets(shop, campaignId);
                    List<String> targetIds = finalSuccList.stream().map(AmazonSbAdNeTargeting::getTargetId).collect(Collectors.toList());
                    saveDoris(neTargetingVo.getPuid(), neTargetingVo.getShopId(), targetIds);
                } catch (Exception e) {
                    log.info("添加成功后同步否定异常", e);
                }
            });

            //同步广告组投放类型字段
            try {
                AmazonSbAdGroup sbAdGroup = groupDao.getByGroupId(puid, shopId, groupId);
                if (StringUtils.isBlank(sbAdGroup.getAdGroupType())) {
                    sbAdGroup.setAdGroupType("product");
                    groupDao.updateByIdAndPuid(puid, sbAdGroup);
                    cpcSbGroupService.saveDoris(null, Collections.singletonList(sbAdGroup));
                }
            } catch (Exception e) {
                log.error("updateGroupTargetType:", e);
            }
        }

        // 没有失败的就代表全部成功了
        List<AmazonSbAdNeTargeting> failList = neTargetList.stream().filter(e -> StringUtils.isBlank(e.getTargetId()))
                .collect(Collectors.toList());
        if (failList.size() == 0) {
            return ResultUtil.success();
        }

        StringBuilder errMsg = new StringBuilder("创建失败的否定投放原因：");
        failList.forEach((e) -> {
            if (StringUtils.isNotBlank(e.getErrMsg())) {
                errMsg.append(e.getErrMsg());
            }
        });

        return ResultUtil.returnErr(errMsg.toString());
    }

    private void logSbNeTargetCreate(List<AmazonSbAdNeTargeting> neTargetList, String ip, Result result) {
        try {
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(neTargetList)) {
                return;
            }
            List<AdManageOperationLog> operationLogs = Lists.newArrayList();
            for (AmazonSbAdNeTargeting neTargeting : neTargetList) {
                AdManageOperationLog targetLog = adOperationLogService.getSbNeTargetLog(null, neTargeting);
                targetLog.setIp(ip);
                if (StringUtils.isNotBlank(neTargeting.getTargetId())) {
                    targetLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    targetLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    if (StringUtils.isNotBlank(neTargeting.getErrMsg())) {
                        targetLog.setResultInfo(neTargeting.getErrMsg());
                    } else {
                        targetLog.setResultInfo(result.getMsg());
                    }
                }
                operationLogs.add(targetLog);
            }
            adOperationLogService.batchLogsMergeByAdGroup(operationLogs);
        } catch (Exception e) {
            log.error("SB否定商品投放创建日志异常", e);
        }
    }

    @Override
    public NewCreateResultResultVo<SBCommonErrorVo> create(AddSbNeTargetingVo neTargetingVo, ShopAuth shop, AmazonAdProfile profile) {
        Integer puid = neTargetingVo.getPuid();
        Integer shopId = neTargetingVo.getShopId();
        String campaignId = neTargetingVo.getCampaignId();
        String groupId = neTargetingVo.getGroupId();
        List<SbNeTargetsVo> neTargetsVos = neTargetingVo.getNeTargetsVos();
        // 校验参数
        String msg = checkAddNeTargetingVo(neTargetsVos);
        if (StringUtils.isNotBlank(msg)) {
            throw new ServiceException(msg);
        }

        AmazonSbAdGroup adGroup = groupDao.getByGroupId(puid, shopId, groupId);
        if (adGroup == null) {
            throw new ServiceException(SBCreateErrorEnum.GROUP_NOT_EXIST.getMsg());
        }

        List<SbBatchNeTargetsVo> batchNeTargetsVos = new ArrayList<>(neTargetsVos.size());
        List<AmazonSbAdNeTargeting> neTargetList = new ArrayList<>(neTargetsVos.size());
        SbBatchNeTargetsVo batchNeTargetsVo;
        for (SbNeTargetsVo neTargetVo : neTargetsVos) {
            batchNeTargetsVo = new SbBatchNeTargetsVo();
            batchNeTargetsVo = convertBatchAddNeTargetingVo(neTargetVo, neTargetingVo.getUid(), adGroup);
            batchNeTargetsVos.add(batchNeTargetsVo);
        }

        Result result = cpcSbNeTargetApiService.createNew(shop, profile, batchNeTargetsVos);
        if (!result.success()) {
            return NewCreateResultResultVo.<SBCommonErrorVo>builder()
                    .campaignId(campaignId)
                    .adGroupId(groupId)
                    .build();
        }

        if (CollectionUtils.isNotEmpty(batchNeTargetsVos)) {
            batchNeTargetsVos.forEach(item -> {
                AmazonSbAdNeTargeting sbAdNeTargeting = new AmazonSbAdNeTargeting();
                BeanUtils.copyProperties(item, sbAdNeTargeting);
                sbAdNeTargeting.setState("enabled");
                neTargetList.add(sbAdNeTargeting);
            });
        }

        List<AmazonSbAdNeTargeting> succList = neTargetList.stream().filter(e -> StringUtils.isNotBlank(e.getTargetId()))
                .collect(Collectors.toList());
        if (!succList.isEmpty()) {
            // 有可能已经添加过了
            List<String> existInDB = neTargetingDao.listByTargetId(neTargetingVo.getPuid(), shopId, succList.stream()
                            .map(AmazonSbAdNeTargeting::getTargetId).collect(Collectors.toList())).stream()
                    .map(AmazonSbAdNeTargeting::getTargetId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getTargetId())).collect(Collectors.toList());
            }

            // 入库
            try {
                neTargetingDao.batchAdd(neTargetingVo.getPuid(), succList);
                List<String> targetIds = succList.stream().map(AmazonSbAdNeTargeting::getTargetId).collect(Collectors.toList());
                saveDoris(neTargetingVo.getPuid(), neTargetingVo.getShopId(), targetIds);
            } catch (Exception e) {
                log.error("createSbNeTargeting:", e);
                throw new ServiceException(e.getMessage());
            }

            ThreadPoolUtil.getNegativeSyncPool().execute(()->{
                try {
                    //创建成功, 需要在同步 获取投放状态
                    cpcSbNeTargetApiService.syncNeTargets(shop, campaignId);
                } catch (Exception e) {
                    log.info("添加成功后同步否定异常", e);
                    throw new ServiceException(e.getMessage());
                }
            });

            //同步广告组投放类型字段
            try {
                AmazonSbAdGroup sbAdGroup = groupDao.getByGroupId(puid, shopId, groupId);
                if (StringUtils.isBlank(sbAdGroup.getAdGroupType())) {
                    sbAdGroup.setAdGroupType("product");
                    groupDao.updateByIdAndPuid(puid, sbAdGroup);
                    cpcSbGroupService.saveDoris(null, Collections.singletonList(sbAdGroup));
                }
            } catch (Exception e) {
                log.error("updateGroupTargetType:", e);
            }
        }

        NewCreateResultResultVo<SBCommonErrorVo> neTargetResult = NewCreateResultResultVo.<SBCommonErrorVo>builder()
                .campaignId(campaignId)
                .adGroupId(groupId)
                .neTargetingIdList(neTargetList.stream().map(neTarget -> {
                    NewCreateNeTargetResultVo vo = new NewCreateNeTargetResultVo();
                    Optional.ofNullable(neTarget.getTargetId()).ifPresent(vo::setNeTargetId);
                    Optional.ofNullable(neTarget.getTargetText()).ifPresent(vo::setNeTargetText);
                    Optional.ofNullable(neTarget.getType()).ifPresent(vo::setNeMatchType);
                    return vo;
                }).collect(Collectors.toList()))
                .build();
        // 没有失败的就代表全部成功了
        List<AmazonSbAdNeTargeting> failList = neTargetList.stream().filter(e -> StringUtils.isBlank(e.getTargetId()))
                .collect(Collectors.toList());
        if (failList.size() == 0) {
            return neTargetResult;
        }
        List<SBCommonErrorVo> errorList = new ArrayList<>();
        failList.forEach((e) -> {
            if (org.apache.commons.lang.StringUtils.isNotBlank(e.getErrMsg())) {
                errorList.add(SBCommonErrorVo.getErrorVo(e.getTargetText(), e.getErrMsg()));
            }
        });
        neTargetResult.setErrInfoList(errorList);
        return neTargetResult;
    }

    @Override
    public Result archive(Integer puid, Integer shopId, Integer uid, String targetId, String ip) {
        AmazonSbAdNeTargeting sbAdNeTargeting = neTargetingDao.getByTargetId(puid, shopId, targetId);
        if (sbAdNeTargeting == null) {
            return ResultUtil.returnErr("没有投放定位信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Result result = cpcSbNeTargetApiService.archive(shop, profile, targetId);
        AmazonSbAdNeTargeting oldNeTargeting = new AmazonSbAdNeTargeting();
        BeanUtils.copyProperties(sbAdNeTargeting, oldNeTargeting);

        sbAdNeTargeting.setUpdateId(uid);
        sbAdNeTargeting.setState(CpcStatusEnum.archived.name());

        logSbNeTargetArchive(oldNeTargeting, sbAdNeTargeting, ip, result);

        if (result.success()) {
            neTargetingDao.updateByIdAndPuid(puid, sbAdNeTargeting);
            saveDoris(sbAdNeTargeting.getPuid(), sbAdNeTargeting.getShopId(), Lists.newArrayList(sbAdNeTargeting.getTargetId()));
            return ResultUtil.success();
        }
        return ResultUtil.error(result.getMsg());
    }

    private void logSbNeTargetArchive(AmazonSbAdNeTargeting oldNeTarget, AmazonSbAdNeTargeting neTarget, String ip, Result result) {
        try {
            AdManageOperationLog targetLog = adOperationLogService.getSbNeTargetLog(oldNeTarget, neTarget);
            targetLog.setIp(ip);
            if (result.success()){
                targetLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            }else {
                targetLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    targetLog.setResultInfo(result.getMsg());
            }
            adOperationLogService.printAdOperationLog(Lists.newArrayList(targetLog));
        } catch (Exception e) {
            log.error("SB否定商品投放归档日志异常", e);
        }
    }

    @Override
    public Result<BatchResponseVo<BatchNeTargetVo, AmazonSbAdNeTargeting>> batchArchiveNeTargeting(Integer puid, Integer shopId, Integer uid, List<String> targetIdList, String ip) {
        List<AmazonSbAdNeTargeting> neSbTargetingList = neTargetingDao.listByTargetId(puid, shopId, targetIdList);
        if (neSbTargetingList == null) {
            return ResultUtil.returnErr("没有否定投放信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Map<String, AmazonSbAdNeTargeting> amazonSbAdNeTargetingMap = neSbTargetingList.stream().collect(Collectors.toMap(AmazonSbAdNeTargeting::getTargetId, e -> e));
        List<BatchNeTargetVo> errorList = new ArrayList<>();
        List<AmazonSbAdNeTargeting> archiveList = new ArrayList<>();

        for (String targetId : targetIdList) {
            BatchNeTargetVo vo = new BatchNeTargetVo();
            AmazonSbAdNeTargeting neTarget = amazonSbAdNeTargetingMap.get(targetId);
            vo.setIsFail(false);
            vo.setUid(uid);
            if (neTarget == null) {
                vo.setIsFail(true);
                vo.setFailReason("否定投放信息不存在");
                errorList.add(vo);
                continue;
            }
            AmazonSbAdNeTargeting amazonSbAdNeTarget = new AmazonSbAdNeTargeting();
            BeanUtils.copyProperties(neTarget, amazonSbAdNeTarget);
            convertVoToBatchUpdatePo(amazonSbAdNeTarget, vo);
            archiveList.add(amazonSbAdNeTarget);
        }

        if (CollectionUtils.isEmpty(archiveList)) {
            BatchResponseVo<BatchNeTargetVo, AmazonSbAdNeTargeting> data = new BatchResponseVo<>();
            data.setErrorList(errorList);
            data.setSuccessNum(0);
            data.setCountNum(targetIdList.size());
            return ResultUtil.success(data);
        }

        Result<BatchResponseVo<BatchNeTargetVo, AmazonSbAdNeTargeting>> result = cpcSbNeTargetApiService.update(shop, profile, archiveList);

        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayList();
        for (AmazonSbAdNeTargeting neTargeting : archiveList) {
            AmazonSbAdNeTargeting oldNeTargeting = amazonSbAdNeTargetingMap.get(neTargeting.getTargetId());
            AdManageOperationLog adManageOperationLog = adOperationLogService.getSbNeTargetLog(oldNeTargeting, neTargeting);
            adManageOperationLog.setIp(ip);
            adManageOperationLogs.add(adManageOperationLog);
        }

        if (result.success()) {
            BatchResponseVo<BatchNeTargetVo, AmazonSbAdNeTargeting> data = result.getData();
            List<BatchNeTargetVo> sdUpdateNeTargetVoError = data.getErrorList();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(errorList)) {
                sdUpdateNeTargetVoError.addAll(errorList);
                data.setFailNum(data.getErrorList().size());
                data.setCountNum((data.getErrorList() == null ? 0 : data.getErrorList().size()) + (data.getSuccessList() == null ? 0 : data.getSuccessList().size()));
            }
            List<AmazonSbAdNeTargeting> successList = data.getSuccessList();

            Map<String, AmazonSbAdNeTargeting> successDataMap = successList.stream().collect(Collectors.toMap(AmazonSbAdNeTargeting::getTargetId, e -> e));
            Map<String, BatchNeTargetVo> errorDataMap = sdUpdateNeTargetVoError.stream().collect(Collectors.toMap(BatchNeTargetVo::getTargetId, e -> e));
            for (AdManageOperationLog adManageOperationLog : adManageOperationLogs) {
                if (!StringUtil.isEmptyObject(successDataMap.get(adManageOperationLog.getTargetId()))) {
                    adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                }
                if (!StringUtil.isEmptyObject(errorDataMap.get(adManageOperationLog.getTargetId()))) {
                    adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    adManageOperationLog.setResultInfo(errorDataMap.get(adManageOperationLog.getTargetId()).getFailReason());
                }
            }

            if (CollectionUtils.isNotEmpty(successList)) {
                neTargetingDao.batchUpdate(puid, successList);
                List<String> targetIds = successList.stream().map(AmazonSbAdNeTargeting::getTargetId).collect(Collectors.toList());
                saveDoris(puid, shopId, targetIds);
                data.getSuccessList().clear();
            }
        } else {
            for (AdManageOperationLog targetsLog : adManageOperationLogs) {
                targetsLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                targetsLog.setResultInfo(result.getMsg());
            }
        }
        adOperationLogService.batchLogsMergeByAdGroup(adManageOperationLogs);
        return result;
    }

    private String checkAddNeTargetingVo(List<SbNeTargetsVo> neTargetsVos) {
        if (CollectionUtils.isEmpty(neTargetsVos)) {
            return SBCreateErrorEnum.NETARGETING_REQ_PARAM_IS_NULL.getMsg();
        }

        for (SbNeTargetsVo vo : neTargetsVos) {
            if (TargetTypeEnum.asin.name().equals(vo.getType()) && StringUtils.isBlank(vo.getAsin())) {
                return SBCreateErrorEnum.NETARGETING_TYPE_IS_NULL.getMsg();
            }
            if (TargetTypeEnum.brand.name().equals(vo.getType()) && StringUtils.isBlank(vo.getBrandId())) {
                return SBCreateErrorEnum.NETARGETING_CONTENT_IS_NULL.getMsg();
            }
        }
        return null;
    }

    private void convertVoToBatchUpdatePo(AmazonSbAdNeTargeting amazonSbAdNeTargeting, BatchNeTargetVo vo) {
        amazonSbAdNeTargeting.setCreateInAmzup(Constants.UPDATE_IN_AMZUP);
        amazonSbAdNeTargeting.setUpdateId(vo.getUid());
    }

    @Override
    public Result<BatchSbDataResponse> batchAddNeTargeting(AddSbNeTargetingVo neTargetingVo) {
        Result<BatchSbDataResponse> data = ResultUtil.success();
        StringBuilder errorMsg = new StringBuilder();
        List<Int32Value> errorList = Lists.newArrayList();
        List<Int32Value> successList = Lists.newArrayList();

        Integer puid = neTargetingVo.getPuid();
        Integer shopId = neTargetingVo.getShopId();
        List<SbNeTargetsVo> neTargetsVos = neTargetingVo.getNeTargetsVos();
        // 校验参数
        String msg = checkAddNeTargetingVo(neTargetsVos);
        if (StringUtils.isNotBlank(msg)) {
            return ResultUtil.error(msg);
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        List<String> adGroupIds = neTargetsVos.stream().filter(item -> StringUtils.isNotBlank(item.getGroupId()))
                .map(SbNeTargetsVo::getGroupId).collect(Collectors.toList());
        Map<String, AmazonSbAdGroup> adGroupMap = groupDao.getAdGroupByIds(puid, shopId, adGroupIds).stream()
                .filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, Function.identity()));

        Iterator<SbNeTargetsVo> iterator = neTargetsVos.iterator();
        SbNeTargetsVo next;
        while (iterator.hasNext()) {
            next = iterator.next();
            if (adGroupMap.get(next.getGroupId()) == null) {
                iterator.remove();
                errorMsg.append("targetValue:" + (StringUtils.isNotBlank(next.getAsin()) ? next.getAsin() : next.getBrandName()) + ",desc: 该广告组不存在;</br>");
                errorList.add(Int32Value.of(next.getIndex()));
            }
        }

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(neTargetsVos)) {
            if (StringUtils.isNotBlank(errorMsg)) {
                return ResultUtil.returnErr(errorMsg.toString());
            } else {
                return ResultUtil.returnErr("request param error");
            }
        }

        List<SbBatchNeTargetsVo> batchNeTargetsVos = new ArrayList<>(neTargetsVos.size());
        List<AmazonSbAdNeTargeting> neTargetList = new ArrayList<>(neTargetsVos.size());
        SbBatchNeTargetsVo batchNeTargetsVo;
        for (SbNeTargetsVo neTargetVo : neTargetsVos) {
            batchNeTargetsVo = new SbBatchNeTargetsVo();
            AmazonSbAdGroup adGroup = adGroupMap.get(neTargetVo.getGroupId());
            batchNeTargetsVo = convertBatchAddNeTargetingVo(neTargetVo, neTargetingVo.getUid(), adGroup);
            batchNeTargetsVo.setIndex(neTargetVo.getIndex());
            batchNeTargetsVos.add(batchNeTargetsVo);
        }

        Result result = cpcSbNeTargetApiService.create(shop, profile, batchNeTargetsVos);
        if (!result.success()) {
            return result;
        }

        if (CollectionUtils.isNotEmpty(batchNeTargetsVos)) {
            batchNeTargetsVos.forEach(item -> {
                AmazonSbAdNeTargeting sbAdNeTargeting = new AmazonSbAdNeTargeting();
                BeanUtils.copyProperties(item, sbAdNeTargeting);
                neTargetList.add(sbAdNeTargeting);
            });
        }

        List<AmazonSbAdNeTargeting> succList = neTargetList.stream().filter(e -> StringUtils.isNotBlank(e.getTargetId()))
                .collect(Collectors.toList());
        if (succList.size() > 0) {
            // 有可能已经添加过了
            List<String> existInDB = neTargetingDao.listByTargetId(neTargetingVo.getPuid(), shopId, succList.stream()
                            .map(AmazonSbAdNeTargeting::getTargetId).collect(Collectors.toList())).stream()
                    .map(AmazonSbAdNeTargeting::getTargetId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getTargetId())).collect(Collectors.toList());
            }

            // 入库
            try {
                neTargetingDao.batchAdd(neTargetingVo.getPuid(), succList);
                List<String> targetIds = succList.stream().map(AmazonSbAdNeTargeting::getTargetId).collect(Collectors.toList());
                saveDoris(puid, shopId, targetIds);
            } catch (Exception e) {
                log.error("createSbNeTargeting:", e);
            }
        }

        // 没有失败的就代表全部成功了
        List<AmazonSbAdNeTargeting> failList = neTargetList.stream().filter(e -> StringUtils.isBlank(e.getTargetId()))
                .collect(Collectors.toList());

        String errMsg = failList.stream().filter(item -> StringUtils.isNotBlank(item.getErrMsg())).map(item ->
                        "targetValue:" + item.getTargetText() + ",desc: " + item.getErrMsg())
                .collect(Collectors.joining(";</br>"));
        if (StringUtils.isNotBlank(errMsg)) {
            errorMsg.append(errMsg);
        }
        List<Int32Value> errList = batchNeTargetsVos.stream().filter(item -> StringUtils.isBlank(item.getTargetId()))
                .map(item -> Int32Value.of(item.getIndex())).collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(errList)) {
            errorList.addAll(errList);
        }
        successList = batchNeTargetsVos.stream().filter(item -> StringUtils.isNotBlank(item.getTargetId()))
                .map(item -> Int32Value.of(item.getIndex())).collect(Collectors.toList());
        data.setData(BatchSbDataResponse.newBuilder()
                .setFailMsg(errorMsg.toString())
                .addAllFail(errorList)
                .addAllSuccess(successList).build());

        return data;
    }

    private SbBatchNeTargetsVo convertBatchAddNeTargetingVo(SbNeTargetsVo source, Integer uid, AmazonSbAdGroup adGroup) {
        SbBatchNeTargetsVo batchNeTargetsVo = new SbBatchNeTargetsVo();
        batchNeTargetsVo.setPuid(adGroup.getPuid());
        batchNeTargetsVo.setShopId(adGroup.getShopId());
        batchNeTargetsVo.setMarketplaceId(adGroup.getMarketplaceId());
        batchNeTargetsVo.setProfileId(adGroup.getProfileId());
        batchNeTargetsVo.setAdGroupId(adGroup.getAdGroupId());
        batchNeTargetsVo.setCampaignId(adGroup.getCampaignId());
        batchNeTargetsVo.setType(source.getType());
        batchNeTargetsVo.setCreateId(uid);
        batchNeTargetsVo.setCreateInAmzup(1);
        List<SBResolvedExpression> expressions = new ArrayList<>();
        if (SdTargetTypeEnum.asin.name().equals(source.getType())) {  //现在只有否asin功能, 否品牌暂不支持
            SBResolvedExpression expression = new SBResolvedExpression();
            expression.setType(ExpressionEnum.asinSameAs.value());
            expression.setValue(source.getAsin());
            expressions.add(expression);

            batchNeTargetsVo.setTargetText(source.getAsin());
            batchNeTargetsVo.setImgUrl(source.getImgUrl());
            batchNeTargetsVo.setTitle(source.getTitle());
        }
        if (SdTargetTypeEnum.brand.name().equals(source.getType())) {
            SBResolvedExpression expression = new SBResolvedExpression();
            expression.setType(ExpressionEnum.asinBrandSameAs.value());
            expression.setValue(source.getBrandId());
            batchNeTargetsVo.setTargetText(source.getBrandName());
            expressions.add(expression);
        }
        batchNeTargetsVo.setExpression(JSONUtil.objectToJson(expressions));
        return batchNeTargetsVo;
    }



    /**
     * 写入doris
     * @param targetingList
     */
    @Override
    public void saveDoris(List<AmazonSbAdNeTargeting> targetingList) {
        try {
            List<OdsAmazonAdSbNeTargeting> collect = targetingList.stream().map(x -> {
                OdsAmazonAdSbNeTargeting po = new OdsAmazonAdSbNeTargeting();
                BeanUtils.copyProperties(x, po);
                if (x.getCreateTime() != null) {
                    //sb 没有亚马逊创建时间创建时间使用 create_time 时间
                    LocalDateTime localDateTime = LocalDateTimeUtil.convertDateToLDT(x.getCreateTime());
                    po.setCreationDate(localDateTime);
                    //产品要求还是按北京时间转换，这个地方先不改，后面再说
                    po.setAmazonCreateTime(localDateTime);
                    po.setCreationAfterDate(localDateTime.plusDays(30L).toLocalDate());
                    po.setCreationBeforeDate(localDateTime.plusDays(-30L).toLocalDate());
                }
                if (StringUtils.isNotBlank(po.getState())) {
                    po.setState(po.getState().toLowerCase());
                }
                return po;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sb ne targeting save doris error", e);
        }
    }

    /**
     * 写入doris
     * @param puid
     * @param shopId
     * @param targetIdList
     */
    @Override
    public void saveDoris(Integer puid, Integer shopId, List<String> targetIdList) {
        try {
            if (CollectionUtils.isEmpty(targetIdList)) {
                return;
            }
            List<AmazonSbAdNeTargeting> amazonSbAdNeTargetings = neTargetingDao.listByTargetId(puid, shopId, targetIdList);
            saveDoris(amazonSbAdNeTargetings);
        } catch (Exception e) {
            log.error("sb ne targeting save doris error", e);
        }
    }
}
