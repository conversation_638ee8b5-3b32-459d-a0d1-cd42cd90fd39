package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdProductPerspectiveBO;
import com.meiyunji.sponsored.service.autoRule.vo.AdProductAutoRuleParam;
import com.meiyunji.sponsored.service.export.dto.DownloadCenterBaseDataBO;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProduct;
import com.meiyunji.sponsored.service.cpc.vo.AdProductByGroupVo;
import com.meiyunji.sponsored.service.cpc.vo.AdProductPageParam;
import com.meiyunji.sponsored.service.cpc.vo.CpcTaskSearchDto;
import com.meiyunji.sponsored.service.cpc.vo.GroupPageParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AsinListDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AsinListReqVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.InitAsinVo;
import com.meiyunji.sponsored.service.strategy.vo.AdProductStrategyParam;
import com.meiyunji.sponsored.service.strategy.vo.AdProductStrategyVo;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * AmazonAdProduct
 * <AUTHOR>
 */
public interface IAmazonAdProductDao extends IBaseShardingDao<AmazonAdProduct> {
    /**
     * 批量创建广告产品
     * @param puid
     * @param amazonAdProducts
     */
    void insertOnDuplicateKeyUpdate(Integer puid, List<AmazonAdProduct> amazonAdProducts);

    /**
     * 获取广告组产品的个数
     * @param puid
     * @param shopId
     * @param adGroupId
     * @return
     */
    Integer countByAdGroupId(int puid, Integer shopId, String adGroupId);

    /**
     * 获取广告id
     * @param puid
     * @param shopId
     * @param marketPlaceId
     * @param onlineAdId
     * @return
     */
    List<String> getAdIds(Integer puid, Integer shopId, String marketPlaceId, List<String> onlineAdId);

    /**
     * 批量更新
     * @param puid
     * @param updateList
     */
    void updateList(Integer puid,List<AmazonAdProduct> updateList);

    /**
     * 获取profileId
     * @param puid
     * @param shopId
     * @param marketPlaceId
     * @param adId
     * @return
     */
    String getProfileId(int puid, Integer shopId, String marketPlaceId, String adId);

    /**
     * 修改状态
     * @param puid
     * @param shopId
     * @param adId
     * @param state
     * @param updateId
     */
    void updateState(int puid, Integer shopId, String adId, String state, int updateId);

    /**
     * 获取广告产品信息
     * @param puid
     * @param page
     * @return
     */
    Page getPageList(Integer puid, AdProductPageParam param, Page page);

    /**
     * 获取asin
     * @param puid
     * @param shopId
     * @param campaignId
     * @param adGroupId
     * @return
     */
    List<String> getAsinByGroup(int puid, Integer shopId, String campaignId, String adGroupId);

    /**
     * @param puid
     * @param shopId
     * @param adGroupId
     * @return
     */
    List<AmazonAdProduct> getListByGroup(Integer puid, Integer shopId, String adGroupId);


    /**
     * 获取已存在的sku
     * @param puid
     * @param shopId
     * @param campaignId
     * @param adGroupId
     * @param skus
     * @return
     */
    List<String> checkRepeatedSkus(Integer puid, Integer shopId, String campaignId, String adGroupId, List<String> skus);

    List<String> checkRepeatedAsins(Integer puid, Integer shopId, String campaignId, String adGroupId, List<String> asins);

    /**
     * 获取广告产品信息
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param adId
     * @return
     */
    AmazonAdProduct getByAdId(int puid, Integer shopId, String marketplaceId, String adId);

    /**
     * 获取广告产品信息
     * @param puid
     * @param shopId
     * @param adId
     * @return
     */
    AmazonAdProduct getByAdId(int puid, Integer shopId, String adId);

    /**
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    Page pageListForTask(int puid, CpcTaskSearchDto dto, Page page);

    /**
     * 统计广告产品的数量
     * @param puid
     * @param shopId
     * @return
     */
    int count(Integer puid, Integer shopId);

    /**
     * 新版列表页
     * @param puid：
     * @param param：
     * @return ：
     */
    Page<AmazonAdProduct> pageList(Integer puid, AdProductPageParam param);

    List<AmazonAdProduct> listByCondition(Integer puid, AdProductPageParam param);

    /**
     * 查有效的
     * @param puid:
     * @param shopId:
     * @param adGroupId:
     * @return : list
     */
    List<AmazonAdProduct> listValidByGroupId(Integer puid, Integer shopId, String adGroupId);

    List<AmazonAdProduct> listValidByGroupIds(Integer puid, Integer shopId, List<String> adGroupIds);

    List<AmazonAdProduct> listValidByGroupIdsAndShopIds(Integer puid, List<Integer> shopIdList, List<String> adGroupIds);

    List<String> listSkus(Integer puid, Integer shopId, String adGroupId, List<String> stateList);

    /**
     * 广告组下的广告数
     */
    Map<String,Integer> statCountByAdGroup(Integer puid, Integer shopId, List<String> status, List<String> adGroupIds);


    /**
     * 获取SP广告活动/广告组下的Asin集合
     * @param puid
     * @param shopId
     * @param campaignId
     * @param groupId
     * @return
     */
    List<String> getListAsinByCampaignIdAndGroupId(Integer puid, Integer shopId, String campaignId, String groupId);

    List<AmazonAdProduct> getList(Integer puid, AdProductPageParam param);

    /**
     * 修改报告数据最新更新时间
     * @param puid
     * @param shopId
     * @param adId
     * @param localDate
     */
    void updateDataUpdateTime(Integer puid, Integer shopId, String adId, LocalDate localDate);

    List<String> getAsinByCampaignId(int puid, Integer shopId, String campaignId);

    List<String> getArchivedItems(Integer puid, Integer shopId);

    List<String> getUpdateAfterReportSyncTimeItems(Integer puid, Integer shopId, LocalDateTime syncAt);

    List<String> getAdIdsByProduct(int puid, AdProductPageParam param);

    Integer statSumCountByAdGroup(Integer puid, Integer shopId, List<String> status, List<String> adGroupIds);

    Integer statSumCountGroupProduct(Integer puid, Integer shopId, List<String> status, GroupPageParam param);

    List<AmazonAdProduct> getByAdIds(int puid, Integer shopId, List<String> adIds);

    List<AmazonAdProduct> getByAdIds(int puid, List<Integer> shopId, List<String> adIds);

    List<AmazonAdProduct> getByAdIds(int puid, Integer shopId, String marketplaceId, List<String> adIds);

    List<String> getCampaignIdByAsinOrMsku(Integer puid, Integer shopId, String type, List<String> value, String adType,List<String> campaignIds);

    /**
     * 多店铺查询
     */
    List<String> getCampaignIdByAsinOrMskuShopList(Integer puid, List<Integer> shopId, List<String> marketPlaceId, String type, List<String> value, String adType,List<String> campaignIds);

    List<String> getCampaignIdByProduct(Integer puid, List<Integer> shopIds, String type, List<String> value, List<String> adTypes, List<String> campaignIds);

    List<String> getSkuList(Integer puid, Integer shopId);

    AmazonAdProduct getProductList(Integer puid,Integer shopId,String asin,String adId);

    AmazonAdProduct getProductList(Integer puid,String asin,String adId);

    void updateRowAdId(int puid, Integer shopId, String adId, String campaignId, String adGroupId,String sku);

    List<AmazonAdProduct> getBySkus(int puid, List<String> skus);

    Page<AmazonAdProduct> getAdProductList(AdProductAutoRuleParam param, List<String> adGroupIds);

    List<String> getGroupIdByAsin(Integer puid, Integer shopId,List<String> skuList);

    List<AmazonAdProductPerspectiveBO> productPerspectiveBoListByAsin(Integer puid, List<Integer> shopIdList, String marketPlaceId, String asin);

    List<String> adIdListByAsin(Integer puid, List<Integer> shopIdList, String marketPlaceId, String asin);

    List<InitAsinVo> getAsinByPuidAndShopId(Integer puid, List<Integer> shopIdList, String marketplaceId);

    Page<AsinListDto> getAsinByPuidAndShopId(AsinListReqVo reqVo);

    List<AdProductByGroupVo> getAdProductByAdGroupIdList(Integer puid, Integer shopId, List<String> campaignIds, List<String> adGroupIds);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

    List<AmazonAdProduct> getGpsProduct(Integer puid, List<String> campaignIdList, List<String> adGroupIdList, List<String> skuList);

    void insertOnDuplicateKeyUpdateGps(Integer puid, List<AmazonAdProduct> amazonAdProducts);

    /**
     * 根据asin、父asin、msku查询sp广告组Id
     * @param puid
     * @param shopIdList
     * @param productValue
     * @param productType
     * @return
     */
    List<String> getSpAdGroupIdListByAsinAndMsku(Integer puid, List<Integer> shopIdList, List<String> productValue, String productType);

    /**
     * 根据asin、父asin、msku查询sb广告组Id
     * @param puid
     * @param shopIdList
     * @param productValue
     * @param productType
     * @return
     */
    List<String> getSbAdGroupIdListByAsinAndMsku(Integer puid, List<Integer> shopIdList, List<String> productValue, String productType);

    Page<AdProductStrategyVo> queryAdProductStrategy(AdProductStrategyParam param);

    void updateStatePricing(Integer puid, Integer shopId, String adId, int isPricing, int pricingState, Integer updateId);

    void updateState(int puid, Integer shopId, String adId, String state);

    List<String> getListAsinByCampaignIdAndGroupIdAndEnabled(Integer puid, Integer shopId, String campaignId, String groupId);

    List<DownloadCenterBaseDataBO> queryBaseData4DownloadByProductIdList(Integer puid, Integer shopId, List<String> adIdList);
}