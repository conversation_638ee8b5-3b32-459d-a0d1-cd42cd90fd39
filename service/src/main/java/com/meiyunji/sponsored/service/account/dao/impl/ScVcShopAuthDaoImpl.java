package com.meiyunji.sponsored.service.account.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.account.bo.ShopAuthBo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ScAndVcShopAuth;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.enums.ShopAdStatusEnum;
import com.meiyunji.sponsored.service.enums.ShopStatusEnum;
import com.meiyunji.sponsored.service.vo.ShopAuthSellerInfoVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;


/**
 * ShopAuth
 *
 * <AUTHOR>
 */
@Repository
public class ScVcShopAuthDaoImpl extends BaseDaoImpl<ScAndVcShopAuth> implements IScVcShopAuthDao {


    private final static String vcShopTableName = "t_vc_shop_auth";
    private final static String scShopTableName = "t_shop_auth";

    @Override
    public ShopAuth getVcAndScByIdAndPuid(Object id, Integer puid) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " id = ? and puid = ? ");

        args.add(id);
        args.add(puid);
        StringBuilder scSql = new StringBuilder(
                getScSql() + " id = ? and puid = ? ");
        args.add(id);
        args.add(puid);
        List<ShopAuth> query = getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
        if (CollectionUtils.isNotEmpty(query) && query.size() == 1) {
            return query.get(0);
        } else {
            return null;
        }
    }




    @Override
    public List<Integer> getShopByMid(int puid, String marketplaceId) {

        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id  from " + scShopTableName + "  where puid = ? ");
        args.add(puid);
        if (StringUtils.isNotBlank(marketplaceId)) {
            scSql.append(" and marketplace_id = ? ");
            args.add(marketplaceId);
        }
        scSql.append(" and ad_status = 'auth'");
        StringBuilder vcSql = new StringBuilder("select id from " + vcShopTableName + "  where puid = ? ");
        args.add(puid);
        if (StringUtils.isNotBlank(marketplaceId)) {
            vcSql.append(" and marketplace_id = ? ");
            args.add(marketplaceId);
        }
        vcSql.append(" and ad_status = 'auth'");
        return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, Integer.class, args.toArray());

    }

    @Override
    public List<ShopAuth> getListByPuid(int puid) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ?  ");
        args.add(puid);
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }

    @Override
    public List<ShopAuth> getAllId() {
        StringBuilder scSql = new StringBuilder("select id,marketplace_id marketplaceId,puid, 'SC' type  from " + scShopTableName );
        StringBuilder vcSql = new StringBuilder("select id,marketplace_id marketplaceId,puid, 'VC' type from " + vcShopTableName );
        return getJdbcTemplate().query(scSql + " union all  " + vcSql + " order by id ", new BeanPropertyRowMapper<>(ShopAuth.class));
    }

    @Override
    public List<Integer> getAllShopId() {
        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id from " + scShopTableName);
        StringBuilder vcSql = new StringBuilder("select id from " + vcShopTableName);
        return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, Integer.class, args.toArray());
    }

    @Override
    public List<Integer> getAllShopId(int puid) {
        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id  from " + scShopTableName + "  where puid = ? ");
        args.add(puid);
        StringBuilder vcSql = new StringBuilder("select id from " + vcShopTableName + "  where puid = ? ");
        args.add(puid);
        return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, Integer.class, args.toArray());
    }

    @Override
    public List<Integer> getAllAdShopId(int puid) {
        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id  from " + scShopTableName + "  where puid = ? ");
        args.add(puid);
        scSql.append(" and ad_status = 'auth'");
        StringBuilder vcSql = new StringBuilder("select id from " + vcShopTableName + "  where puid = ? ");
        args.add(puid);
        vcSql.append(" and ad_status = 'auth'");
        return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, Integer.class, args.toArray());
    }

    @Override
    public List<String> getAllSellerId(Integer puid) {

        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select DISTINCT selling_partner_id  from " + scShopTableName + " ");
        StringBuilder vcSql = new StringBuilder("select DISTINCT selling_partner_id from " + vcShopTableName + " ");
        if (puid != null && puid > 0) {
            scSql.append(" where puid = ? ");
            args.add(puid);
            vcSql.append(" where puid = ? ");
            args.add(puid);
            return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, String.class, args.toArray());
        } else {
            return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, String.class);
        }
    }



    @Override
    public List<String> getSellerIdByPuid(Integer puid) {
        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select distinct selling_partner_id  from " + scShopTableName + " ");
        StringBuilder vcSql = new StringBuilder("select distinct selling_partner_id from " + vcShopTableName + " ");
        scSql.append(" where puid = ? ");
        args.add(puid);
        vcSql.append(" where puid = ? ");
        args.add(puid);
        return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, String.class, args.toArray());
    }



    @Override
    public List<ShopAuth> listByPuidAndMarketplace(int puid, List<String> mkList, List<Integer> shopList) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);


        if (CollectionUtils.isNotEmpty(mkList)) {
            vcSql.append(SqlStringUtil.dealInList("marketplace_id", mkList, args));
        }

        if (CollectionUtils.isNotEmpty(shopList)) {
            vcSql.append(SqlStringUtil.dealInList("id", shopList, args));
        }

        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ?  ");
        args.add(puid);

        if (CollectionUtils.isNotEmpty(mkList)) {
            scSql.append(SqlStringUtil.dealInList("marketplace_id", mkList, args));
        }

        if (CollectionUtils.isNotEmpty(shopList)) {
            scSql.append(SqlStringUtil.dealInList("id", shopList, args));
        }
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());

    }



    @Override
    public List<ShopAuth> listValidByIds(Integer puid, List<Integer> shopIds) {

        if (CollectionUtils.isEmpty(shopIds)) {
            return Lists.newArrayList();
        }
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        vcSql.append(" and status = ? ");
        args.add(ShopStatusEnum.NORMAL.value());
        vcSql.append(SqlStringUtil.dealInList("id", shopIds, args));
        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ?  ");
        args.add(puid);
        scSql.append(" and status = ? ");
        args.add(ShopStatusEnum.NORMAL.value());
        scSql.append(SqlStringUtil.dealInList("id", shopIds, args));
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }





    @Override
    public List<ShopAuth> listAllValidAdShop(Integer puid) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        vcSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());
        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ?  ");
        args.add(puid);
        scSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }

    @Override
    public List<ShopAuth> getAllValidAdShopByLimit(Integer puid, Integer shopId, int start, int limit) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");
        if (puid != null) {
            vcSql.append(" and puid = ? ");
            args.add(puid);
        }

        vcSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());

        if (shopId != null) {
            vcSql.append(" and id = ? ");
            args.add(shopId);
        }

        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1=1  ");
        if (puid != null) {
            scSql.append(" and puid = ? ");
            args.add(puid);
        }
        scSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());


        if (shopId != null) {
            scSql.append(" and id = ? ");
            args.add(shopId);
        }
        String s = scSql + " union all " + vcSql + " limit ? , ? ";
        args.add(start);
        args.add(limit);

        return getJdbcTemplate().query(s, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }

    /**
     * 获取店铺信息
     *
     * @param puid
     * @param marketplaceIds
     * @return
     */
    @Override
    public List<ShopAuth> getByPuidAndMarketplaceIds(int puid, String[] marketplaceIds) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        if (ArrayUtils.isNotEmpty(marketplaceIds)) {
            vcSql.append(SqlStringUtil.dealInList("marketplace_id", Lists.newArrayList(marketplaceIds), args));
        }

        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ? ");
        args.add(puid);
        if (ArrayUtils.isNotEmpty(marketplaceIds)) {
            scSql.append(SqlStringUtil.dealInList("marketplace_id", Lists.newArrayList(marketplaceIds), args));
        }
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }


    @Override
    public List<String> marketplaceIdListByShopIds(List<Integer> shopIdList) {

        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select distinct marketplace_id  from " + scShopTableName + "  where 1=1 ");
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            scSql.append(SqlStringUtil.dealInList("id", shopIdList, args));
        }
        StringBuilder vcSql = new StringBuilder("select distinct marketplace_id  from " + vcShopTableName + "  where 1=1 ");
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            vcSql.append(SqlStringUtil.dealInList("id", shopIdList, args));
        }
        return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, String.class, args.toArray());
    }

    @Override
    public List<Integer> IdListByShopIds(List<Integer> shopIdList) {

        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id from " + scShopTableName + "  where 1=1 ");
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            scSql.append(SqlStringUtil.dealInList("id", shopIdList, args));
        }
        StringBuilder vcSql = new StringBuilder("select id from " + vcShopTableName + "  where 1=1 ");
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            vcSql.append(SqlStringUtil.dealInList("id", shopIdList, args));
        }
        return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, Integer.class, args.toArray());
    }

    @Override
    public List<ShopAuth> getAuthShopByShopIdList(int puid, List<Integer> shopIdList) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        vcSql.append(SqlStringUtil.dealInList("id", shopIdList, args));

        vcSql.append(" and ad_status <> ? ");
        args.add(ShopAdStatusEnum.UNAUTH.name());

        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ? ");
        args.add(puid);
        scSql.append(SqlStringUtil.dealInList("id", shopIdList, args));
        scSql.append(" and ad_status <> ? ");
        args.add(ShopAdStatusEnum.UNAUTH.name());
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }


    /**
     * 根据 sellerPartnerId 和 marketplaceId 找到店铺
     * 理论上：同一个sellerPartnerId ，在同一个marketplaceId 下只会有一个店铺
     *
     * @param sellerIds
     * @param marketplaceIds
     * @return
     */
    @Override
    public List<ShopAuth> getBySellerIdsAndMarketplaceIds(List<String> sellerIds, List<String> marketplaceIds) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");
        vcSql.append(SqlStringUtil.dealInList("selling_partner_id", sellerIds, args));
        vcSql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIds, args));
        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1 = 1 ");
        scSql.append(SqlStringUtil.dealInList("selling_partner_id", sellerIds, args));
        scSql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIds, args));

        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }

    @Override
    public List<ShopAuth> getAdAuthShopByShopIdList(List<Integer> puids) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");
        if (CollectionUtils.isNotEmpty(puids)) {
            vcSql.append(SqlStringUtil.dealInList("puid", puids, args));
        }
        vcSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());
        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1 = 1 ");
        if (CollectionUtils.isNotEmpty(puids)) {
            scSql.append(SqlStringUtil.dealInList("puid", puids, args));
        }
        scSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }

    @Override
    public List<ShopAuth> getAdAuthShopByShopId(List<Integer> puid, List<Integer> shopId) {


        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");
        if (CollectionUtils.isNotEmpty(puid)) {
            vcSql.append(SqlStringUtil.dealInList("puid", puid, args));
        }
        if (CollectionUtils.isNotEmpty(shopId)) {
            vcSql.append(SqlStringUtil.dealInList("id", shopId, args));
        }
        vcSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());
        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1 = 1 ");
        if (CollectionUtils.isNotEmpty(puid)) {
            scSql.append(SqlStringUtil.dealInList("puid", puid, args));
        }
        if (CollectionUtils.isNotEmpty(shopId)) {
            scSql.append(SqlStringUtil.dealInList("id", shopId, args));
        }
        scSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }

    @Override
    public List<ShopAuthBo> getShopAuthBoByIds(int puid, List<Integer> idList) {

        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id, marketplace_id marketplaceId, name, 'SC' as `type` from " + scShopTableName + "  where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(idList)) {
            scSql.append(SqlStringUtil.dealInList("id", idList, args));
        }

        StringBuilder vcSql = new StringBuilder("select id, marketplace_id marketplaceId, name, 'VC' as `type` from " + vcShopTableName + "  where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(idList)) {
            vcSql.append(SqlStringUtil.dealInList("id", idList, args));
        }
        return getJdbcTemplate().query(scSql + " union all " + vcSql, args.toArray(), new BeanPropertyRowMapper<>(ShopAuthBo.class));
    }



    @Override
    public List<ShopAuthBo> getAuthShopBoByMarketPlaceAndIds(int puid, List<Integer> idList, List<String> marketplaceIdList) {

        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id, marketplace_id marketplaceId, name, 'SC' as `type` from " + scShopTableName + "  where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(idList)) {
            scSql.append(SqlStringUtil.dealInList("id", idList, args));
        }
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            scSql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIdList, args));
        }
        scSql.append(" and ad_status <> ? ");
        args.add(ShopAdStatusEnum.UNAUTH.name());

        StringBuilder vcSql = new StringBuilder("select id, marketplace_id marketplaceId, name, 'VC' as `type` from " + vcShopTableName + "  where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(idList)) {
            vcSql.append(SqlStringUtil.dealInList("id", idList, args));
        }

        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            vcSql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIdList, args));
        }

        vcSql.append(" and ad_status <> ? ");
        args.add(ShopAdStatusEnum.UNAUTH.name());

        return getJdbcTemplate().query(scSql + " union all " + vcSql, args.toArray(), new BeanPropertyRowMapper<>(ShopAuthBo.class));

    }

    @Override
    public List<Integer> queryRandomSequentList() {

        String sql = "select min(id) from (select min(id) id from t_shop_auth union  select min(id) id from t_vc_shop_auth) s  union select max(id) from  (select max(id) id from t_shop_auth union  select max(id) id from t_vc_shop_auth) v ";
        return getJdbcTemplate().queryForList(sql, Integer.class);
    }


    @Override
    public List<ShopAuthSellerInfoVo> getSellerInfoByIdList(ArrayList<Integer> idList) {

        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id, puid, selling_partner_id as sellerId, marketplace_id as marketplaceId, 'SC' as `type` from " + scShopTableName + "  where  ad_status = 'auth' ");
        scSql.append(SqlStringUtil.dealInList("id", idList, args));
        StringBuilder vcSql = new StringBuilder("select id, puid, selling_partner_id as sellerId, marketplace_id as marketplaceId, 'VC' as `type` from " + vcShopTableName + "  where  ad_status = 'auth' ");
        vcSql.append(SqlStringUtil.dealInList("id", idList, args));
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuthSellerInfoVo.class), args.toArray());
    }




    /**
     * 获取sc和vc店铺
     * @param puid
     * @param shopIds
     * @param adStatus
     * @return
     */
    @Override
    public List<ShopAuth> getScAndVcShopList(int puid, List<Integer> shopIds, String adStatus) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        if (StringUtils.isNotBlank(adStatus)) {
            vcSql.append(" and ad_status = ? ");
            args.add(adStatus);
        } else {
            vcSql.append(" and ad_status <> ? ");
            args.add(ShopAdStatusEnum.UNAUTH.name());
        }

        if (CollectionUtils.isNotEmpty(shopIds)) {
            vcSql.append(SqlStringUtil.dealInList("id", shopIds, args));
        }
        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ? ");
        args.add(puid);
        if (StringUtils.isNotBlank(adStatus)) {
            scSql.append(" and ad_status  ? ");
            args.add(adStatus);
        } else {
            scSql.append(" and ad_status <> ? ");
            args.add(ShopAdStatusEnum.UNAUTH.name());
        }

        if (CollectionUtils.isNotEmpty(shopIds)) {
            scSql.append(SqlStringUtil.dealInList("id", shopIds, args));
        }
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }



    /**
     * 获取sc和vc店铺
     * 根据 sellerPartnerId 和 marketplaceId 找到店铺
     * 理论上：同一个sellerPartnerId ，在同一个marketplaceId 下只会有一个店铺
     *
     * @param sellerIds
     * @param marketplaceIds
     * @return
     */
    @Override
    public List<ShopAuth> getScAndVcBySellerIdsAndMarketplaceIds(List<String> sellerIds, List<String> marketplaceIds) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1=1 ");

        vcSql.append(SqlStringUtil.dealInList("selling_partner_id", sellerIds, args) + SqlStringUtil.dealInList("marketplace_id", marketplaceIds, args));
        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1=1 ");
        scSql.append(SqlStringUtil.dealInList("selling_partner_id", sellerIds, args) + SqlStringUtil.dealInList("marketplace_id", marketplaceIds, args));
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }


    /**
     * SC 和 VC 查询时共用同一个实体返回，要不然改的地方太多了，实在很难受
     *
     * @param id
     * @return
     */
    @Override
    public ShopAuth getScAndVcById(int id) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " id = ? ");

        args.add(id);
        StringBuilder scSql = new StringBuilder(
                 getScSql() + " id = ? ");
        args.add(id);
        List<ShopAuth> query = getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
        if (CollectionUtils.isNotEmpty(query) && query.size() == 1) {
            return query.get(0);
        } else {
            return null;
        }
    }

    private String getScSql() {

        return "SELECT id, puid, name, selling_partner_id sellingPartnerId, region, marketplace_id marketplaceId, sp_refresh_token spRefreshToken, sp_access_token spAccessToken, sp_auth_time spAuthTime,"
                + " status, create_id createId, update_id updateId, create_time createTime, update_time updateTime, ad_refresh_token adRefreshToken, ad_access_token adAccessToken, ad_auth_time adAuthTime, ad_status adStatus, 'SC' as `type` "
                + " FROM t_shop_auth "
                + " where ";
    }

    private String getVcSql() {

        return "SELECT id, puid, name, selling_partner_id sellingPartnerId, region, marketplace_id marketplaceId, refresh_token spRefreshToken, access_token spAccessToken, auth_time spAuthTime,"
                + " status, create_id createId, update_id updateId, create_time createTime, update_time updateTime, ad_refresh_token adRefreshToken, ad_access_token adAccessToken, ad_auth_time adAuthTime, ad_status adStatus, 'VC' as `type` "
                + " FROM t_vc_shop_auth "
                + " where ";
    }


    /**
     * 获取sc和vc店铺
     *
     * @param shopIds
     * @param adStatus
     * @return
     */
    @Override
    public List<ShopAuth> getScAndVcShopListByShopIdsAndAdStatus(List<Integer> shopIds, String adStatus) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");

        if (StringUtils.isNotBlank(adStatus)) {
            vcSql.append(" and ad_status = ? ");
            args.add(adStatus);
        } else {
            vcSql.append(" and ad_status <> ? ");
            args.add(ShopAdStatusEnum.UNAUTH.name());
        }

        vcSql.append(SqlStringUtil.dealInList("id", shopIds, args));

        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1 = 1 ");

        if (StringUtils.isNotBlank(adStatus)) {
            scSql.append(" and ad_status  ? ");
            args.add(adStatus);
        } else {
            scSql.append(" and ad_status <> ? ");
            args.add(ShopAdStatusEnum.UNAUTH.name());
        }

        scSql.append(SqlStringUtil.dealInList("id", shopIds, args));

        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }


    /**
     * SC 和 VC 查询时共用同一个实体返回，要不然改的地方太多了，实在很难受
     *
     * @param ids
     * @return
     */
    @Override
    public List<ShopAuth> getScAndVcByIds(List<Integer> ids) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");
        vcSql.append(SqlStringUtil.dealInList("id", ids, args));
        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1 = 1 ");
        scSql.append(SqlStringUtil.dealInList("id", ids, args));
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }


    @Override
    public ShopAuth getScAndVcByIdAndPuid(int id, int puid) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " id = ? and puid = ? ");

        args.add(id);
        args.add(puid);
        StringBuilder scSql = new StringBuilder(
                getScSql() + " id = ? and puid = ? ");
        args.add(id);
        args.add(puid);
        List<ShopAuth> query = getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
        if (CollectionUtils.isNotEmpty(query) && query.size() == 1) {
            return query.get(0);
        } else {
            return null;
        }
    }

    @Override
    public List<ShopAuthBo> getScAndVcShopAuthBoByMarketPlaceAndIds(int puid, List<Integer> idList, List<String> marketplaceIdList) {
        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id, marketplace_id marketplaceId, name, 'SC' type from " + scShopTableName + "  where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(idList)) {
            scSql.append(SqlStringUtil.dealInList("id", idList, args));
        }
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            scSql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIdList, args));
        }
        StringBuilder vcSql = new StringBuilder("select id, marketplace_id marketplaceId, name, 'VC' type from " + vcShopTableName + "  where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(idList)) {
            vcSql.append(SqlStringUtil.dealInList("id", idList, args));
        }
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            vcSql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIdList, args));
        }

        return getJdbcTemplate().query(scSql + " union all " + vcSql, args.toArray(), new BeanPropertyRowMapper<>(ShopAuthBo.class));
    }


    @Override
    public List<ShopAuthBo> getShopAuthBoByMarketPlaceAndIds(int puid, List<Integer> idList, List<String> marketplaceIdList) {
        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id, marketplace_id marketplaceId, name, 'SC' type  from " + scShopTableName + "  where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(idList)) {
            scSql.append(SqlStringUtil.dealInList("id", idList, args));
        }
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            scSql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIdList, args));
        }
        StringBuilder vcSql = new StringBuilder("select id, marketplace_id marketplaceId, name, 'VC' type  from " + vcShopTableName + "  where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(idList)) {
            vcSql.append(SqlStringUtil.dealInList("id", idList, args));
        }
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            vcSql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIdList, args));
        }

        return getJdbcTemplate().query(scSql + " union all " + vcSql, args.toArray(), new BeanPropertyRowMapper<>(ShopAuthBo.class));
    }



    @Override
    public List<ShopAuth> listAllByIds(int puid, List<Integer> shopIds) {


        if (CollectionUtils.isEmpty(shopIds)) {
            return Lists.newArrayList();
        }

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        vcSql.append(SqlStringUtil.dealInList("id", shopIds, args));
        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ? ");
        args.add(puid);
        scSql.append(SqlStringUtil.dealInList("id", shopIds, args));
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }


    @Override
    public List<ShopAuth> getScAndVcAuthShopByShopIdList(int puid, List<Integer> shopIdList) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        vcSql.append(" and ad_status <> ? ");
        args.add(ShopAdStatusEnum.UNAUTH.name());

        if (CollectionUtils.isNotEmpty(shopIdList)) {
            vcSql.append(SqlStringUtil.dealInList("id", shopIdList, args));
        }

        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ?  ");
        args.add(puid);
        scSql.append(" and ad_status <> ? ");
        args.add(ShopAdStatusEnum.UNAUTH.name());

        if (CollectionUtils.isNotEmpty(shopIdList)) {
            scSql.append(SqlStringUtil.dealInList("id", shopIdList, args));
        }
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }


    @Override
    public ShopAuth getBySellerIdAndMarketplaceId(String sellerId, String marketplaceId) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");


        vcSql.append(" and selling_partner_id = ? ");
        args.add(sellerId);

        vcSql.append(" and marketplace_id = ? ");
        args.add(marketplaceId);


        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1 = 1 ");

        scSql.append(" and selling_partner_id = ? ");
        args.add(sellerId);

        scSql.append(" and marketplace_id = ? ");
        args.add(marketplaceId);

        List<ShopAuth> query = getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
        return query.size() == 1 ? query.get(0) : null;
    }


    @Override
    public List<ShopAuth> listScAndVcAllByPuid(int puid) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ? ");
        args.add(puid);
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }

    @Override
    public List<ShopAuth> listValidShopByIds(Integer puid, List<Integer> shopIds) {
        if (CollectionUtils.isEmpty(shopIds)) {
            return Lists.newArrayList();
        }

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? and ad_status in ('auth', 'expire') ");
        args.add(puid);
        vcSql.append(SqlStringUtil.dealInList("id", shopIds, args));
        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ? and ad_status in ('auth', 'expire') ");
        args.add(puid);
        scSql.append(SqlStringUtil.dealInList("id", shopIds, args));
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }

}