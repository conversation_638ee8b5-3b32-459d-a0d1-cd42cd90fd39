package com.meiyunji.sponsored.service.cpc.service2.impl;


import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.PageUtil;
import com.meiyunji.sponsored.rpc.adCommon.*;
import com.meiyunji.sponsored.rpc.vo.CampaignNeKeywordsPageRpcVo;
import com.meiyunji.sponsored.rpc.vo.CampaignNeTargetingSpRpcVo;
import com.meiyunji.sponsored.rpc.vo.NeKeywordsPageRpcVo;
import com.meiyunji.sponsored.rpc.vo.NeTargetingPageRpcVo;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.service.IWxCpcQueryKeywordReportService;
import com.meiyunji.sponsored.service.cpc.service.IWxCpcQueryTargetingReportService;
import com.meiyunji.sponsored.service.cpc.service.IWxCpcSbQueryKeywordReportService;
import com.meiyunji.sponsored.service.cpc.service2.*;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.enums.AdModularEnum;
import com.meiyunji.sponsored.service.enums.GroupTypeEnum;
import com.meiyunji.sponsored.service.enums.TargetingEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * @author: DXM_0685
 * @date: 2023/1/3
 * @describe: wx端广告通用接口(联合查询sp sb sd) 主要包含列表页面和指标数据和chart图数据接口
 */
@Service
public class WxCpcCommonServiceImpl implements IWxCpcCommonService {


    @Autowired
    private IWxCpcQueryTargetingReportService wxCpcQueryTargetingReportService;

    @Autowired
    private IWxCpcQueryKeywordReportService wxCpcQueryKeywordReportService;

    @Autowired
    private IWxCpcSbQueryKeywordReportService wxCpcSbQueryKeywordReportService;

    @Autowired
    private IAmazonAdCampaignDao amazonAdCampaignDao;

    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;

    @Autowired
    private IWxCpcCampaignService wxCpcCampaignService;

    @Autowired
    private IWxCpcGroupService wxCpcGroupService;


    /**
     * 所有搜索词(投放)数据
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    public AllQueryTargetDataResponse.AdQueryTargetingHomeVo getAllQueryTargetData(Integer puid, CpcQueryWordDto dto, Page page){
        return wxCpcQueryTargetingReportService.getAllQueryTargetData(puid,dto,page);
    }

    @Override
    public AllQueryTargetAggregateDataResponse.AdQueryTargetingHomeVo getAllQueryTargetAggregateData(Integer puid, CpcQueryWordDto dto) {
        return wxCpcQueryTargetingReportService.getAllQueryTargetAggregateData(puid,dto);
    }

    /**
     * 所有搜索词(关键词)数据
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    public AllQueryWordDataResponse.AdQueryWordsHomeVo getAllQueryWordData(Integer puid, CpcQueryWordDto dto, Page page){
        if(Constants.SB.equalsIgnoreCase(dto.getType())){
            return wxCpcSbQueryKeywordReportService.getAllQueryWordData(puid, dto, page);
        }else {
            return wxCpcQueryKeywordReportService.getAllQueryWordData(puid, dto, page);
        }
    }

    @Override
    public AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo getAllQueryWordAggregateData(Integer puid, CpcQueryWordDto dto) {
        if(Constants.SB.equalsIgnoreCase(dto.getType())){
            return wxCpcSbQueryKeywordReportService.getAllQueryWordAggregateData(puid, dto);
        }else {
            return wxCpcQueryKeywordReportService.getAllQueryWordAggregateData(puid, dto);
        }

    }



    @Override
    public AllPlacementDataResponse.AdPlacementHomeVo getAllPlacementData(Integer puid, PlacementPageParam param) {
        return wxCpcCampaignService.getAllPlacementData(puid, param);
    }
    @Override
    public AllPlacementAggregateDataResponse.AdPlacementHomeVo getAllPlacementAggregateData(Integer puid, PlacementPageParam param) {
        return wxCpcCampaignService.getAllPlacementAggregateData(puid, param);
    }

    public AllProductDataResponse.AdProductHomeVo getAllProductData(Integer puid, AdProductPageParam param) {
        return wxCpcGroupService.getAllProductData(puid, param);
    }
    @Override
    public AllProductAggregateDataResponse.AdProductHomeVo getAllProductAggregateData(Integer puid, AdProductPageParam param) {
        return wxCpcGroupService.getAllProductAggregateData(puid, param);
    }


    @Override
    public Page<CampaignNeKeywordsPageRpcVo> getCampaignNeKeywordsPageList(Integer puid, CampaignNeKeywordsPageParam param) {
        return wxCpcCampaignService.getNeKeywordsPageList(puid, param);
    }

    @Override
    public Page<NeKeywordsPageRpcVo> getGroupNeKeywordPageList(NeKeywordsPageParam param) {
        return wxCpcGroupService.getAllNeKeywordPageList(param);
    }


    @Override
    public Page<CampaignNeTargetingSpRpcVo> getCampaignNeTargetingPageList(CampaignNeTargetingSpParam param) {
        return wxCpcCampaignService.getNeTargetingPageList(param);
    }

    @Override
    public Page<NeTargetingPageRpcVo> getGroupNeTargetingPageList(NeTargetingPageParam param) {
        return wxCpcGroupService.getAllNeTargetingPageList(param.getPuid(), param);
    }

    @Override
    public Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllTypeCampAndGroup(CampaignAndGroupPageListParam param) {

        Integer puid = param.getPuid();
        Integer shopId = param.getShopId();
        String type = param.getType();
        String modular = param.getModular();
        List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos = Lists.newArrayList();
        //分多次查询,组合一起
        if (AdModularEnum.keywordTarget.getCode().equalsIgnoreCase(param.getModular())) {
            // 1.关键词投放：  SP手动（关键词类型）、SB（关键词类型）
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(param.getType()) || Constants.SP.equalsIgnoreCase(param.getType())) {
                spCampaignAndGroupVos = getCampAndGroupVos(param.getPuid(),param.getShopId(), Constants.SP, Constants.MANUAL, GroupTypeEnum.keyword.getCode(),param.getSearchValue(),param.getItemType(),param.getCampaignIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(param.getType()) || Constants.SB.equalsIgnoreCase(param.getType())) {
                sbCampaignAndGroupVos = getCampAndGroupVos(param.getPuid(), param.getShopId(), Constants.SB, null, GroupTypeEnum.keyword.getCode(),param.getSearchValue(),param.getItemType(),param.getCampaignIds());
            }
            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());

        }  else if (AdModularEnum.neKeyword.getCode().equalsIgnoreCase(param.getModular())) {
            // 2.否定关键词：  SP（关键词类型）、SB（关键词类型）
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(param.getType()) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid,shopId, Constants.SP, null, GroupTypeEnum.keyword.getCode(),param.getSearchValue(),param.getItemType(),param.getCampaignIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.keyword.getCode(),param.getSearchValue(),param.getItemType(),param.getCampaignIds());
            }
            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());

        } else if (AdModularEnum.productTarget.getCode().equalsIgnoreCase(modular)) {
            // 3.商品投放：  SP自动  手动（商品投放类型）、SB（商品投放类型）、SD（全部）
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos1 = Lists.newArrayList();
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos2 = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                if("campaign".equalsIgnoreCase(param.getItemType())) {
                    spCampaignAndGroupVos1 = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.MANUAL, GroupTypeEnum.targeting.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds());
                    spCampaignAndGroupVos2 = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.AUTO, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds());
                } else {
                    spCampaignAndGroupVos1 = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.MANUAL, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds());
                }
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.product.getCode(),param.getSearchValue(),param.getItemType(),param.getCampaignIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SD, null, null,param.getSearchValue(),param.getItemType(),param.getCampaignIds());
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos1, spCampaignAndGroupVos2, sbCampaignAndGroupVos,sdCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());
        } else if (AdModularEnum.neTarget.getCode().equalsIgnoreCase(modular)) {
            // 4.否定商品：  SP（商品投放类型）、SB（商品投放类型）、SD（商品）
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid,shopId, Constants.SP, null, GroupTypeEnum.targeting.getCode(),param.getSearchValue(),param.getItemType(),param.getCampaignIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.product.getCode(),param.getSearchValue(),param.getItemType(),param.getCampaignIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SD, TargetingEnum.Product.getTargetingType(), null,param.getSearchValue(),param.getItemType(),param.getCampaignIds());
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos,sdCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());
        }  else if (AdModularEnum.adProduct.getCode().equalsIgnoreCase(modular)) {
            // 5.广告：  Sp sd
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid,shopId, Constants.SP, null, null,param.getSearchValue(),param.getItemType(),param.getCampaignIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SD, null, null,param.getSearchValue(),param.getItemType(),param.getCampaignIds());
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos,sdCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());
        } else if (AdModularEnum.queryWord.getCode().equalsIgnoreCase(modular)) {
            // 6.搜索词： 关键词产生：  SP手动（关键词类型）--  (全部)
            campAndGroupVos = getCampAndGroupVos(puid,shopId, Constants.SP,null, null,param.getSearchValue(),param.getItemType(),param.getCampaignIds());
        } else if (AdModularEnum.queryTarget.getCode().equalsIgnoreCase(modular)) {
            // 7.搜索词： 商品投放产生：  SP自动、SP手动（商品投放类型）
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos1 = new ArrayList<>();
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos2 = new ArrayList<>();
            if("campaign".equalsIgnoreCase(param.getItemType())){
                campAndGroupVos1 = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.MANUAL, null,param.getSearchValue(),param.getItemType(),param.getCampaignIds());
                campAndGroupVos2 = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.AUTO, null,param.getSearchValue(),param.getItemType(),param.getCampaignIds());
            } else {
                campAndGroupVos1 = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.MANUAL, null,param.getSearchValue(),param.getItemType(),param.getCampaignIds());
            }
            campAndGroupVos = Stream.of(campAndGroupVos1, campAndGroupVos2).flatMap(Collection::stream).collect(Collectors.toList());
        } else if (AdModularEnum.campaignNeKeyword.getCode().equalsIgnoreCase(modular)) {
            // 8.活动否定关键词:  SP（全部）
            campAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, null,null,param.getSearchValue(),param.getItemType(),param.getCampaignIds());
        } else if (AdModularEnum.campaignNeTarget.getCode().equalsIgnoreCase(modular)) {
            // 9.活动否定商品：  SP自动
            campAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.AUTO,null,param.getSearchValue(),param.getItemType(),param.getCampaignIds());
        } else if (AdModularEnum.group.getCode().equalsIgnoreCase(modular)) {
            // 10.广告组  sp sd
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid,shopId, Constants.SP, null, null,param.getSearchValue(),param.getItemType(),param.getCampaignIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SD, null, null,param.getSearchValue(),param.getItemType(),param.getCampaignIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SB, null, null,param.getSearchValue(),param.getItemType(),param.getCampaignIds());
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos,sdCampaignAndGroupVos,sbCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());

        } else {
            campAndGroupVos = getCampAndGroupVos(puid, shopId, type, param.getCampaignType(), param.getGroupType(),param.getSearchValue(),param.getItemType(),param.getCampaignIds());
        }

        //分页
        Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> voPage = new Page<>();
        voPage.setPageNo(1);
        voPage.setPageSize(Constants.TOTALSIZELIMIT);
        return PageUtil.getPage(voPage, campAndGroupVos);
    }


    private List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getCampAndGroupVos(Integer puid, Integer shopId, String type, String campaignType, String groupType,String name,String itemType,String campaignIds) {
        //查询所有类型的广告活动(sp sb sd)
        List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos = new ArrayList<>();
        if("campaign".equalsIgnoreCase(itemType)){
            List<AdCampaignOptionVo> allCampaigns = amazonAdCampaignDao.getCampaignsByType(puid, shopId, type, campaignType, groupType, name, null, null);
            allCampaigns.stream().filter(Objects::nonNull).forEach(item-> {
                AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo.Builder builder = AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo.newBuilder()
                        .setCampaignId(item.getCampaignId())
                        .setCampaignName(item.getName())
                        .setState(item.getState())
                        .setType(item.getType());
                if(StringUtils.isNotBlank(item.getTargetingType())){
                    builder.setCostType(item.getTargetingType());
                }
                if(StringUtils.isNotBlank(item.getCostType())){
                    builder.setCostType(item.getCostType());
                }
                campAndGroupVos.add(builder.build());
            });
        } else if ("group".equalsIgnoreCase(itemType)){
            //类型为sb 则不查询广告组 (sb无广告组概念)
            if (StringUtils.isBlank(type) || type.equalsIgnoreCase(Constants.SP) || type.equalsIgnoreCase(Constants.SD) || type.equalsIgnoreCase(Constants.SB)) {
                List<String> typeList = StringUtils.isBlank(type) ? null : Stream.of(type).collect(Collectors.toList());
                List<AdGroupOptionVo> allGroups = amazonAdGroupDao.getAllGroupsByType(puid, shopId, typeList,groupType,name,campaignIds);
                allGroups.stream().filter(Objects::nonNull).forEach(item-> {
                    AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo.Builder builder = AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo.newBuilder()
                            .setCampaignId(item.getCampaignId())
                            .setGroupName(item.getName())
                            .setGroupId(item.getGroupId())
                            .setState(item.getState() == null ? "":item.getState())
                            .setType(item.getType());
                    campAndGroupVos.add(builder.build());
                });
            }
        }
        return campAndGroupVos;
    }



}
