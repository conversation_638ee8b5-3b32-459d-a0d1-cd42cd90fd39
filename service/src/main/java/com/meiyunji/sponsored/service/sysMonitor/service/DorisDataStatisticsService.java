package com.meiyunji.sponsored.service.sysMonitor.service;


import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.sysMonitor.dao.IDorisDataStatisticsDao;
import com.meiyunji.sponsored.service.sysMonitor.po.DorisDataStatistics;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class DorisDataStatisticsService {

    @Resource(name = "jdbcTemplateFeedDorisDb")
    private JdbcTemplate jdbcTemplateFeedDorisDb;

    @Autowired
    private IDorisDataStatisticsDao dorisDataStatisticsDao;

    private static final String STAT_SQL = "select table_schema tableSchema, table_name tableName, table_rows tableRows, avg_row_length avgRowLength, data_length dataLength, table_comment tableComment from information_schema.tables where table_schema != 'information_schema' order by data_length desc";

    private static final BigDecimal GB_FACTOR = BigDecimal.valueOf(1024).pow(3);
    private static final BigDecimal TB_FACTOR = BigDecimal.valueOf(1024).pow(4);

    public void statDorisData() {
        //执行查询
        List<DorisDataStatistics> statList = jdbcTemplateFeedDorisDb.query(STAT_SQL, new BeanPropertyRowMapper<>(DorisDataStatistics.class));
        if (CollectionUtils.isEmpty(statList)) {
            log.info("doris 数据统计, 查询无数据");
            return;
        }

        Date date = new Date();
        String dateStr = DateUtil.dateToStrNoTime(date);
        statList.forEach(x -> {
            if (Objects.isNull(x.getDataLength())) {
                x.setDataLength(0L);
            }
            BigDecimal bytes = BigDecimal.valueOf(x.getDataLength());
            x.setDataLengthGb(bytes.divide(GB_FACTOR, 2, RoundingMode.HALF_UP));
            x.setDataLengthTb(bytes.divide(TB_FACTOR, 4, RoundingMode.HALF_UP));
            x.setStatDate(dateStr);
            x.setCreateTime(date);
            x.setUpdateTime(date);
        });

        dorisDataStatisticsDao.insert(statList);
    }
}
