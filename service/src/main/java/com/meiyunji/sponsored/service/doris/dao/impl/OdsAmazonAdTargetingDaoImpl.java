package com.meiyunji.sponsored.service.doris.dao.impl;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.rpc.asins.PageListAsinsRequest;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdTargetingDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdKeyword;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdTargeting;
import com.meiyunji.sponsored.service.newDashboard.enums.AsinLibDataEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * amazon广告投放定位表(OdsAmazonAdTargeting)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:20
 */
@Repository
public class OdsAmazonAdTargetingDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdTargeting> implements IOdsAmazonAdTargetingDao {

    @Override
    public List<OdsAmazonAdTargeting> getByAdTargetIds(int puid, Integer shopId, List<String> targetIds) {
        StringBuilder sb = new StringBuilder("select * from ").append(this.getJdbcHelper().getTable())
                .append(" where puid = ? and shop_id = ? ");
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        sb.append(SqlStringUtil.dealBitMapDorisInList("target_id", targetIds, argsList));
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(OdsAmazonAdTargeting.class));
    }

    @Override
    public List<OdsAmazonAdTargeting> getByTargetingIds(int puid, List<Integer> shopIds, List<String> targetingList) {
        List<Object> argsList = new ArrayList<>();
        StringBuffer selectSql = new StringBuffer("SELECT * ");
        selectSql.append(" from ").append(getJdbcHelper().getTable()).append(" t ");
        selectSql.append(" where t.puid = ? ");
        argsList.add(puid);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(shopIds)) {
            selectSql.append(SqlStringUtil.dealInList("t.shop_id", shopIds, argsList));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(targetingList)) {
            selectSql.append(SqlStringUtil.dealInList("t.target_id", targetingList, argsList));
        }
        return getJdbcTemplate().query(selectSql.toString(), argsList.toArray(), new ObjectMapper<>(OdsAmazonAdTargeting.class));
    }

    @Override
    public List<OdsAmazonAdTargeting> listByGroupIdList(Integer puid, Integer shopId, List<String> groupIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("ad_group_id", groupIds.toArray(new String[]{}))
                .notEqualTo("type", Constants.TARGETING_TYPE_NEGATIVEASIN)
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()})
                .build();
        String sql = "select ad_group_id, type, targeting_value from " + this.getJdbcHelper().getTable() + " where " + builder.getSql();
        return getJdbcTemplate().query(sql, builder.getValues(), new BeanPropertyRowMapper<>(OdsAmazonAdTargeting.class));
    }

    @Override
    public List<String> getTargetIdsByAdGroupId(int puid, List<Integer> shopIdList, String spCampaignId, List<String> spAdGroupIds) {
        StringBuilder sqlBuilder = new StringBuilder("select target_id from "+ this.getJdbcHelper().getTable() +" where puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        sqlBuilder.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
        if (StringUtils.isNotBlank(spCampaignId)) {
            sqlBuilder.append(" and campaign_id = ? ");
            args.add(spCampaignId);
        }
        sqlBuilder.append(SqlStringUtil.dealInList("ad_group_id", spAdGroupIds, args));
        return getJdbcTemplate().query(sqlBuilder.toString(), (rs, rowNum) -> rs.getString("target_id"), args.toArray());
    }

    @Override
    public Page<AsinLibsDetailVo> getPageTargetIds(Integer puid, AsinLibsDetailParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlCount = new StringBuilder("select count(*) from ( ");
        AsinLibDataEnum field = null;
        if (StringUtils.isNotBlank(param.getOrderField()) && !"null".equals(param.getOrderType())) {
            field = AsinLibDataEnum.fieldMap.get(param.getOrderField());
        }
        StringBuffer selectSql = new StringBuffer();
        selectSql.append("select shopId, targetId, type ");
        if (field != null) {
            selectSql.append(",").append(field.getOrderSide());
        }
        selectSql.append(" from (select shop_id shopId, target_id targetId, type ");
        //指标排序
        if (field != null) {
            selectSql.append(field.getOutSide());
        }
        selectSql.append(" from (select t.shop_id , t.target_id, 'sp' as type ");
        if (field != null) {
            selectSql.append(field.getInSide());
        }
        selectSql.append(" from ods_t_amazon_ad_targeting t ");
        //如果是指标排序则连接报告表，查询需要排序的指标值，否则不连表，直接查基础表即可
        if (field != null) {
            selectSql.append(" left join (select r.puid, shop_id, target_id ");
            selectSql.append(field.getSpSide());
            selectSql.append(" from ods_t_cpc_targeting_report r ");
            joinReportOnSql(puid, param, selectSql, argsList);
        }
        selectSql.append(" where t.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
            selectSql.append(SqlStringUtil.dealInList("t.shop_id", param.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCountry())) {
            selectSql.append(SqlStringUtil.dealInList("t.marketplace_id", param.getCountry(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchMatchTypeList())) {
            selectSql.append(SqlStringUtil.dealInList("t.select_type", param.getSearchMatchTypeList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchPortfolioList())) {
            selectSql.append(" and t.campaign_id in ( ");
            selectSql.append(this.getCampaignIds(puid, param, argsList));
            selectSql.append(" )");
        }
        if (CollectionUtils.isNotEmpty(param.getSearchAdGroupList())) {
            selectSql.append(SqlStringUtil.dealBitMapDorisInList("t.ad_group_id", param.getSearchAdGroupList(), argsList));
        }
        selectSql.append(" and lower(t.targeting_value) = ? ) z ");
        argsList.add(param.getAsin().toLowerCase());
        selectSql.append(" group by shop_id, target_id, type ");
        if (CollectionUtils.isEmpty(param.getSearchMatchTypeList())) {
            selectSql.append(" UNION ALL ");
            selectSql.append("select shop_id shopId, target_id targetId, type ");
            //指标排序
            if (field != null) {
                selectSql.append(field.getOutSide());
            }
            selectSql.append(" from (select t.shop_id, t.target_id, 'sb' as type ");
            if (field != null) {
                selectSql.append(field.getInSide());
            }
            selectSql.append(" from ods_t_amazon_ad_targeting_sb t ");
            //如果是指标排序则连接报告表，查询需要排序的指标值，否则不连表，直接查基础表即可
            if (field != null) {
                selectSql.append(" left join (select r.puid, shop_id, target_id  ");
                selectSql.append(field.getSbSide());
                selectSql.append(" from ods_t_amazon_ad_sb_targeting_report r ");
                joinReportOnSql(puid, param, selectSql, argsList);
            }
            selectSql.append(" where t.puid = ? ");
            argsList.add(puid);
            if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
                selectSql.append(SqlStringUtil.dealInList("t.shop_id", param.getShopIdList(), argsList));
            }
            if (CollectionUtils.isNotEmpty(param.getCountry())) {
                selectSql.append(SqlStringUtil.dealInList("t.marketplace_id", param.getCountry(), argsList));
            }
            if (CollectionUtils.isNotEmpty(param.getSearchPortfolioList())) {
                selectSql.append(" and t.campaign_id in ( ");
                selectSql.append(this.getCampaignIds(puid, param, argsList));
                selectSql.append(" )");
            }
            if (CollectionUtils.isNotEmpty(param.getSearchAdGroupList())) {
                selectSql.append(SqlStringUtil.dealBitMapDorisInList("t.ad_group_id", param.getSearchAdGroupList(), argsList));
            }
            selectSql.append(" and lower(t.target_text) = ? ) z ");
            argsList.add(param.getAsin().toLowerCase());
            selectSql.append(" group by shop_id, target_id, type ");
            selectSql.append(" UNION ALL ");
            selectSql.append("select shop_id shopId, target_id targetId, type ");
            //指标排序
            if (field != null) {
                selectSql.append(field.getOutSide());
            }
            selectSql.append(" from (select t.shop_id, t.target_id, 'sd' as type ");
            if (field != null) {
                //sd与sb要查询的指标值字段名一致
                selectSql.append(field.getInSide());
            }
            selectSql.append(" from ods_t_amazon_ad_targeting_sd t ");
            //如果是指标排序则连接报告表，查询需要排序的指标值，否则不连表，直接查基础表即可
            if (field != null) {
                selectSql.append(" left join (select r.puid, shop_id, target_id  ");
                selectSql.append(field.getSbSide());
                selectSql.append(" from ods_t_amazon_ad_sd_targeting_report r ");
                joinReportOnSql(puid, param, selectSql, argsList);
            }
            selectSql.append(" where t.puid = ? ");
            argsList.add(puid);
            if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
                selectSql.append(SqlStringUtil.dealInList("t.shop_id", param.getShopIdList(), argsList));
            }
            if (CollectionUtils.isNotEmpty(param.getCountry())) {
                selectSql.append(SqlStringUtil.dealInList("t.marketplace_id", param.getCountry(), argsList));
            }
            if (CollectionUtils.isNotEmpty(param.getSearchPortfolioList())) {
                selectSql.append(" and t.campaign_id in ( ");
                selectSql.append(this.getCampaignIds(puid, param, argsList));
                selectSql.append(" )");
            }
            if (CollectionUtils.isNotEmpty(param.getSearchAdGroupList())) {
                selectSql.append(SqlStringUtil.dealBitMapDorisInList("t.ad_group_id", param.getSearchAdGroupList(), argsList));
            }
            selectSql.append(" and lower(t.target_text) = ? ) z");
            argsList.add(param.getAsin().toLowerCase());
            selectSql.append(" group by shop_id, target_id, type) e ");
        } else {
            selectSql.append(" ) e ");
        }
        //排序分页
        if (field != null && !"null".equals(param.getOrderField())) {
            //指标排序
            selectSql.append(" order by ").append(field.getOrderSide());
            selectSql.append(param.getOrderType());
            selectSql.append(" , targetId ");
            selectSql.append(param.getOrderType());
        } else {
            //未指定排序，按照target_id降序排序
            selectSql.append(" order by targetId desc ");
        }
        sqlCount.append(selectSql).append(" ) s");
        String sql = SqlStringUtil.exactSql(selectSql.toString(), argsList);
        String sql2 = SqlStringUtil.exactSql(sqlCount.toString(), argsList);
        return getPageResultByClass(param.getPageNo(), param.getPageSize(), sqlCount.toString(), argsList.toArray(), selectSql.toString(), argsList.toArray(), AsinLibsDetailVo.class);
    }

    private String getCampaignIds(Integer puid, AsinLibsDetailParam param, List<Object> argsList) {
        StringBuilder sql = new StringBuilder(" select DISTINCT campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), argsList));
        }
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(param.getSearchPortfolioList())){
            sql.append("and ( ");
            sql.append(SqlStringUtil.dealInListNotAnd("portfolio_id", param.getSearchPortfolioList(), argsList));
            if (param.getSearchPortfolioList().contains("-1")) {
                sql.append(" or portfolio_id is null ");
            }
            sql.append(" ) ");
        }
        return sql.toString();
    }

    private static void joinReportOnSql(Integer puid, AsinLibsDetailParam param, StringBuffer selectSql, List<Object> argsList) {
        selectSql.append(" join dim_marketplace_info m on m.marketplace_id = r.marketplace_id ");
        selectSql.append(" join (select * from dim_currency_rate where puid = ? and `to` = ? ) c on r.puid = c.puid ");
        argsList.add(puid);
        argsList.add(param.getTo());
        selectSql.append(" and r.count_month = c.month and c.`from` = m.currency ");
        selectSql.append(" where r.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
            selectSql.append(SqlStringUtil.dealInList("r.shop_id", param.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCountry())) {
            selectSql.append(SqlStringUtil.dealInList("r.marketplace_id", param.getCountry(), argsList));
        }
        selectSql.append(" and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        selectSql.append(" and INSTR(LOWER(r.targeting_text), ?) > 0 ");
        argsList.add(param.getAsin().toLowerCase());
        selectSql.append(" group by puid, shop_id, target_id) x on t.puid = x.puid and t.shop_id = x.shop_id and t.target_id = x.target_id ");
    }

    @Override
    public KeywordLibsTotalVo getAsinDetailAggregateData(Integer puid, AsinLibsDetailParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuffer selectSql = new StringBuffer();
        selectSql.append("select sum(count) putNum, sum(cost) cost, sum(impressions) impressions, sum(clicks) clicks, sum(saleNum) saleNum, sum(totalSales) totalSales, " +
                "ifnull(ROUND(ifnull(sum(clicks) / sum(impressions), 0), 4), 0) clickRate, ifnull(ROUND(ifnull(sum(saleNum) / sum(clicks), 0), 4), 0) salesConversionRate, ifnull(sum(cost) / sum(clicks), 0) cpc, " +
                "ifnull(sum(cost) / sum(saleNum), 0) cpa, ifnull(ROUND(ifnull(sum(cost), 0) / ifnull(sum(totalSales), 0), 4), 0) acos, ifnull(ROUND(ifnull(sum(totalSales), 0) / ifnull(sum(cost), 0), 4), 0) roas ");
        selectSql.append(" from (select count(distinct t.target_id) count, ifnull(sum(cost), 0) cost, ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ifnull(sum(sale_num), 0) saleNum, " +
                "ifnull(sum(total_sales), 0) totalSales ");
        selectSql.append(" from ods_t_amazon_ad_targeting t left join ");
        // 在报告表里进行子查询分组减少数据量
        selectSql.append("(select r.puid, shop_id, target_id, " +
                "ifnull(sum(cost * c.rate), 0) cost, ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, " +
                "ifnull(sum(sale_num), 0) sale_num, ifnull(sum(total_sales * c.rate), 0) total_sales from ods_t_cpc_targeting_report r ");
        selectSql.append(" left join dim_marketplace_info m on m.marketplace_id = r.marketplace_id left join (select * from dim_currency_rate where puid = ? and `to` = ?) c ");
        argsList.add(puid);
        argsList.add(param.getTo());
        selectSql.append("on r.puid = c.puid and r.count_month = c.month and c.`from` = m.currency where r.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
            selectSql.append(SqlStringUtil.dealInList("r.shop_id", param.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCountry())) {
            selectSql.append(SqlStringUtil.dealInList("r.marketplace_id", param.getCountry(), argsList));
        }
        selectSql.append(" and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        selectSql.append(" and INSTR(LOWER(r.targeting_text), ?) > 0 ");
        argsList.add(param.getAsin().toLowerCase());
        selectSql.append(" group by puid, shop_id, target_id) r on t.puid = r.puid and t.shop_id = r.shop_id and t.target_id = r.target_id ");
        selectSql.append(" where t.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
            selectSql.append(SqlStringUtil.dealInList("t.shop_id", param.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCountry())) {
            selectSql.append(SqlStringUtil.dealInList("t.marketplace_id", param.getCountry(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchMatchTypeList())) {
            selectSql.append(SqlStringUtil.dealInList("t.select_type", param.getSearchMatchTypeList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchAdGroupList())) {
            selectSql.append(SqlStringUtil.dealBitMapDorisInList("t.ad_group_id", param.getSearchAdGroupList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchPortfolioList())) {
            selectSql.append(" and t.campaign_id in ( ");
            selectSql.append(this.getCampaignIds(puid, param, argsList));
            selectSql.append(" )");
        }
        selectSql.append(" and lower(t.targeting_value) = ?");
        argsList.add(param.getAsin().toLowerCase());
        if (CollectionUtils.isEmpty(param.getSearchMatchTypeList())) {
            selectSql.append(" UNION ALL ");
            selectSql.append(" select count(distinct t.target_id) count, ifnull(sum(cost), 0) cost, ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ifnull(sum(conversions14d), 0) saleNum," +
                    "ifnull(sum(sales14d), 0) totalSales ");
            selectSql.append(" from ods_t_amazon_ad_targeting_sb t left join ");
            // 在报告表里进行子查询分组减少数据量
            selectSql.append("(select r.puid, shop_id, target_id, " +
                    "ifnull(sum(cost * c.rate), 0) cost, ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, " +
                    "ifnull(sum(conversions14d), 0) conversions14d, ifnull(sum(sales14d * c.rate), 0) sales14d from ods_t_amazon_ad_sb_targeting_report r ");
            selectSql.append(" left join dim_marketplace_info m on m.marketplace_id = r.marketplace_id left join (select * from dim_currency_rate where puid = ? and `to` = ?) c ");
            argsList.add(puid);
            argsList.add(param.getTo());
            selectSql.append("on r.puid = c.puid and r.count_month = c.month and c.`from` = m.currency where r.puid = ? ");
            argsList.add(puid);
            if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
                selectSql.append(SqlStringUtil.dealInList("r.shop_id", param.getShopIdList(), argsList));
            }
            if (CollectionUtils.isNotEmpty(param.getCountry())) {
                selectSql.append(SqlStringUtil.dealInList("r.marketplace_id", param.getCountry(), argsList));
            }
            selectSql.append(" and r.count_day >= ? and r.count_day <= ? ");
            argsList.add(param.getStartDate());
            argsList.add(param.getEndDate());
            selectSql.append(" and INSTR(LOWER(r.targeting_text), ?) > 0 ");
            argsList.add(param.getAsin().toLowerCase());
            selectSql.append(" group by puid, shop_id, target_id) r on t.puid = r.puid and t.shop_id = r.shop_id and t.target_id = r.target_id ");
            selectSql.append(" where t.puid = ? ");
            argsList.add(puid);
            if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
                selectSql.append(SqlStringUtil.dealInList("t.shop_id", param.getShopIdList(), argsList));
            }
            if (CollectionUtils.isNotEmpty(param.getCountry())) {
                selectSql.append(SqlStringUtil.dealInList("t.marketplace_id", param.getCountry(), argsList));
            }
            if (CollectionUtils.isNotEmpty(param.getSearchAdGroupList())) {
                selectSql.append(SqlStringUtil.dealBitMapDorisInList("t.ad_group_id", param.getSearchAdGroupList(), argsList));
            }
            if (CollectionUtils.isNotEmpty(param.getSearchPortfolioList())) {
                selectSql.append(" and t.campaign_id in ( ");
                selectSql.append(this.getCampaignIds(puid, param, argsList));
                selectSql.append(" )");
            }
            selectSql.append(" and lower(t.target_text) = ? ");
            argsList.add(param.getAsin().toLowerCase());
            selectSql.append(" UNION ALL ");
            selectSql.append(" select count(distinct t.target_id) count, ifnull(sum(cost), 0) cost, ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ifnull(sum(conversions14d), 0) saleNum," +
                    "ifnull(sum(sales14d), 0) totalSales ");
            selectSql.append(" from ods_t_amazon_ad_targeting_sd t left join ");
            // 在报告表里进行子查询分组减少数据量
            selectSql.append("(select r.puid, shop_id, target_id, " +
                    "ifnull(sum(cost * c.rate), 0) cost, ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, " +
                    "ifnull(sum(conversions14d), 0) conversions14d, ifnull(sum(sales14d * c.rate), 0) sales14d from ods_t_amazon_ad_sd_targeting_report r ");
            selectSql.append(" left join dim_marketplace_info m on m.marketplace_id = r.marketplace_id left join (select * from dim_currency_rate where puid = ? and `to` = ?) c ");
            argsList.add(puid);
            argsList.add(param.getTo());
            selectSql.append("on r.puid = c.puid and r.count_month = c.month and c.`from` = m.currency where r.puid = ? ");
            argsList.add(puid);
            if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
                selectSql.append(SqlStringUtil.dealInList("r.shop_id", param.getShopIdList(), argsList));
            }
            if (CollectionUtils.isNotEmpty(param.getCountry())) {
                selectSql.append(SqlStringUtil.dealInList("r.marketplace_id", param.getCountry(), argsList));
            }
            selectSql.append(" and r.count_day >= ? and r.count_day <= ? ");
            argsList.add(param.getStartDate());
            argsList.add(param.getEndDate());
            selectSql.append(" and INSTR(LOWER(r.targeting_text), ?) > 0 ");
            argsList.add(param.getAsin().toLowerCase());
            selectSql.append(" group by puid, shop_id, target_id) r on t.puid = r.puid and t.shop_id = r.shop_id and t.target_id = r.target_id ");
            selectSql.append(" where t.puid = ? ");
            argsList.add(puid);
            if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
                selectSql.append(SqlStringUtil.dealInList("t.shop_id", param.getShopIdList(), argsList));
            }
            if (CollectionUtils.isNotEmpty(param.getCountry())) {
                selectSql.append(SqlStringUtil.dealInList("t.marketplace_id", param.getCountry(), argsList));
            }
            if (CollectionUtils.isNotEmpty(param.getSearchAdGroupList())) {
                selectSql.append(SqlStringUtil.dealBitMapDorisInList("t.ad_group_id", param.getSearchAdGroupList(), argsList));
            }
            if (CollectionUtils.isNotEmpty(param.getSearchPortfolioList())) {
                selectSql.append(" and t.campaign_id in ( ");
                selectSql.append(this.getCampaignIds(puid, param, argsList));
                selectSql.append(" )");
            }
            selectSql.append(" and lower(t.target_text) = ? ");
            argsList.add(param.getAsin().toLowerCase());
        }
        selectSql.append(" ) s");
        String sql = SqlStringUtil.exactSql(selectSql.toString(), argsList);
        return getJdbcTemplate().queryForObject(selectSql.toString(), argsList.toArray(), new ObjectMapper<>(KeywordLibsTotalVo.class));
    }

    @Override
    public List<AsinLibsVo> getCountByAsin(Integer puid, PageListAsinsRequest param, List<Integer> shopIdList, List<String> asinList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select targeting_value asin, count(*) targetNum from ods_t_amazon_ad_targeting " +
                "where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)){
            sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        }
        //通过来源站点筛选
        if(CollectionUtils.isNotEmpty(param.getContryList())){
            sql.append(SqlStringUtil.dealInList("marketplace_id", param.getContryList(), argsList));
        }

        sql.append(" and `type` = 'asin' ");

        if (CollectionUtils.isNotEmpty(asinList)){
            // 创建一个新的列表来存储转换后的字符串
            List<String> lowerCaseList = new ArrayList<>();
            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
            for (String asin : asinList) {
                lowerCaseList.add(asin.toLowerCase());
            }
            sql.append(SqlStringUtil.dealInList("lower(targeting_value)",lowerCaseList,argsList));
        }
        sql.append(" group by targeting_value");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(AsinLibsVo.class), argsList.toArray());
    }

    @Override
    public Page<OdsAmazonAdTargeting> listPageByCondition(Integer puid, List<Integer> shopIds, AsinLibsParam param) {
        List<Object> args = new ArrayList<>();
        String spSql = buildSpSelectSql(puid, shopIds, param, "ods_t_amazon_ad_targeting", args);
        String sbSql = buildSelectSql(puid, shopIds, param, "ods_t_amazon_ad_targeting_sb", args);
        String sdSql = buildSelectSql(puid, shopIds, param, "ods_t_amazon_ad_targeting_sd", args);
        String totalSql = spSql + " union all " + sbSql + " union all " + sdSql;
        String countSql = " select count(*)  from ( " + totalSql + " ) r";
        String selectSql = " select shop_id, campaign_id, create_time from ( " + totalSql + " ) r order by create_time desc";
        Object[] arg = args.toArray();
        String sql = SqlStringUtil.exactSql(selectSql, args);
        return getPageResult(param.getPageNo(), param.getPageSize(), countSql, arg, selectSql, arg, OdsAmazonAdTargeting.class);
    }

    @Override
    public List<OdsAmazonAdTargeting> listAllGroupByCondition(Integer puid, List<Integer> shopIds, AsinLibsParam param) {
        List<Object> args = new ArrayList<>();
        String spSql = buildGroupSpSql(puid, shopIds, param, "ods_t_amazon_ad_targeting", args);
        String sbSql = buildGroupSql(puid, shopIds, param, "ods_t_amazon_ad_targeting_sb", args);
        String sdSql = buildGroupSql(puid, shopIds, param, "ods_t_amazon_ad_targeting_sd", args);
        String totalSql = spSql + " union all " + sbSql + " union all " + sdSql;
        String selectSql = " select shop_id , ad_group_id  from ( " + totalSql + " ) r ";
        Object[] arg = args.toArray();
        String sql = SqlStringUtil.exactSql(selectSql, args);
        return getJdbcTemplate().query(selectSql, arg, new ObjectMapper<>(OdsAmazonAdTargeting.class));
    }

    private String buildGroupSql(Integer puid, List<Integer> shopIds, AsinLibsParam param, String tableName, List<Object> args) {
        StringBuilder sbSql = new StringBuilder("select shop_id, ad_group_id from " + tableName +
                " where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sbSql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignList())) {
            sbSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", param.getCampaignList(), args));
        }
        if (StringUtils.isNotEmpty(param.getAsin())) {
            sbSql.append(" and lower(target_text) = ? ");
            args.add(param.getAsin().toLowerCase());
        }
        sbSql.append(" group by ad_group_id,shop_id ");
        return sbSql.toString();
    }

    private String buildGroupSpSql(Integer puid, List<Integer> shopIds, AsinLibsParam param, String tableName, List<Object> args) {
        StringBuilder sbSql = new StringBuilder("select shop_id, ad_group_id from " + tableName +
                " where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sbSql.append(SqlStringUtil.dealInList("shop_id",shopIds, args));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignList())) {
            sbSql.append(SqlStringUtil.dealInList("campaign_id",param.getCampaignList(), args));
        }
        if (StringUtils.isNotEmpty(param.getAsin())) {
            sbSql.append(" and lower(targeting_value) = ? ");
            args.add(param.getAsin().toLowerCase());
        }
        sbSql.append(" group by ad_group_id,shop_id ");
        return sbSql.toString();
    }

    private String buildSpSelectSql(Integer puid, List<Integer> shopIds, AsinLibsParam param, String tableName, List<Object> args) {
        StringBuilder sbSql = new StringBuilder("select shop_id, campaign_id, any_value(create_time) create_time from " + tableName +
                " where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sbSql.append(SqlStringUtil.dealInList("shop_id",shopIds, args));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignList())) {
            sbSql.append(SqlStringUtil.dealInList("campaign_id",param.getCampaignList(), args));
        }
        if (StringUtils.isNotEmpty(param.getAsin())) {
            sbSql.append(" and lower(targeting_value) = ? ");
            args.add(param.getAsin().toLowerCase());
        }
        sbSql.append(" and campaign_id is not null ");
        sbSql.append(" group by campaign_id,shop_id ");
        return sbSql.toString();
    }

    private String buildSelectSql(Integer puid, List<Integer> shopIds, AsinLibsParam param, String tableName, List<Object> args) {
        StringBuilder sbSql = new StringBuilder("select shop_id, campaign_id, any_value(create_time) create_time from " + tableName +
                " where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sbSql.append(SqlStringUtil.dealInList("shop_id",shopIds, args));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignList())) {
            sbSql.append(SqlStringUtil.dealInList("campaign_id",param.getCampaignList(), args));
        }
        if (StringUtils.isNotEmpty(param.getAsin())) {
            sbSql.append(" and lower(target_text) = ? ");
            args.add(param.getAsin().toLowerCase());
        }
        sbSql.append(" and campaign_id is not null ");
        sbSql.append(" group by campaign_id,shop_id ");
        return sbSql.toString();
    }
}

