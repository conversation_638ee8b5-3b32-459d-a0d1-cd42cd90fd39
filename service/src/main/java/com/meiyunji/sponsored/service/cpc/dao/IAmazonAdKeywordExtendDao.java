package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingSphereDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeywordExtend;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetExtendInfo;

import java.util.List;

/**
 * AmazonAdKeywordReport
 * <AUTHOR>
 */
public interface IAmazonAdKeywordExtendDao extends IBaseShardingSphereDao<AmazonAdKeywordExtend> {
    /**
     * 批量插入数据
     * @param puid
     * @param list
     */
    void insertList(Integer puid, List<AmazonAdKeywordExtend> list);

    /**
     * 单店铺查询扩展数据
     * @param puid
     * @param shopId
     * @param keywordIdList
     * @return
     */
    List<AmazonAdKeywordExtend> selectByShopIdAndKeywordIdList(Integer puid, Integer shopId, List<String> keywordIdList);

    /**
     * 多店铺查询扩展数据
     * @param puid
     * @param shopIdList
     * @param keywordIdList
     * @return
     */
    List<TargetExtendInfo> selectByShopIdAndKeywordIdList(Integer puid, List<Integer> shopIdList, List<String> keywordIdList);

}