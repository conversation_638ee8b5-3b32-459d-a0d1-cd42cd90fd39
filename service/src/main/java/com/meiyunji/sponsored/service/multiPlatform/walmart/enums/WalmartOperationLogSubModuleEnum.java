package com.meiyunji.sponsored.service.multiPlatform.walmart.enums;

import lombok.Getter;

@Getter
public enum WalmartOperationLogSubModuleEnum {

    /**
     * 二级模块
     */
    AD_MANAGE("AD_MANAGE","广告管理")
    ;


    private String subModuleType;
    private String subModuleValue;

    WalmartOperationLogSubModuleEnum(String subModuleType, String subModuleValue) {
        this.subModuleType = subModuleType;
        this.subModuleValue = subModuleValue;
    }

    public static String getOperationLogSubModuleEnumSubModuleValue(String subModuleType){
        WalmartOperationLogSubModuleEnum[] values = values();
        for (WalmartOperationLogSubModuleEnum value : values) {
            if(subModuleType.equalsIgnoreCase(value.getSubModuleType())){
                return value.getSubModuleValue();
            }
        }
        return null;
    }
}
