package com.meiyunji.sponsored.service.audiences.service;

import com.amazon.advertising.audiences.AudiencesClient;
import com.amazon.advertising.audiences.entity.*;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.audiences.entity.AmazonAdAudience;
import com.meiyunji.sponsored.service.audiences.entity.AmazonAdAudienceFee;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 亚马逊 Audiences Api 服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/20
 */
@Service
@Slf4j
public class AmazonAudiencesApiService {

    @Resource
    private IShopAuthService shopAuthService;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private AmazonAudiencesService amazonAudiencesService;

    private final AudiencesClient client = AudiencesClient.getInstance();

    /**
     * 同步 audience
     *
     * @param shopId
     * @param profileId
     * @param marketPlaceId
     * @return
     */
    public Result<List<AudienceV1>> syncAudiences(Integer shopId, String profileId, String marketPlaceId) {
        Result<List<AudienceV1>> result = ResultUtil.error("网络延迟，请稍后重试");
        ShopAuth shop = shopAuthDao.getScAndVcById(shopId);
        String token = shopAuthService.getAdToken(shop);
        String nextToken = "";
        String adType = "SD";
        Integer batchSize = 250;
        boolean terminated = false;
        // 重试次数
        int rateLimitRetries = 5;
        int tokenRetries = 1;
        while (!terminated) {
            AudiencesListResponse response;
            response = client.getAudiencesList(token, profileId, marketPlaceId, null, null, nextToken, batchSize, adType, null);
            if (response == null) {
                return ResultUtil.error("网络延迟，请稍后重试");
            }
            // 出现429，使用指数回避策略重试
            if (response.getStatusCode() == AmazonAdUtils.rateLimitingCode) {
                if (rateLimitRetries > 0) {
                    rateLimitRetries--;
                    log.info("syncAudiences|getAudiencesList|{}|{}|residual retries：{}", response.getStatusCode(), response.getStatusMessage(), rateLimitRetries);
                    continue;
                } else {
                    break;
                }
            }
            // token过期,刷新token,再请求一次
            if (response.getStatusCode() != null && response.getStatusCode() == 401) {
                if (tokenRetries > 0) {
                    tokenRetries--;
                    token = shopAuthService.refreshCpcAuth(shop);
                    continue;
                } else {
                    break;
                }
            }
            if (response.getError() != null) {
                AudienceError error = response.getError();
                log.error(JSONUtil.objectToJson(error));
                return ResultUtil.error(error.getMessage());
            }

            AudiencesList audiencesVo = response.getAudiencesVo();
            if (audiencesVo == null) {
                break;
            }
            List<AudienceV1> audiences = audiencesVo.getAudiences();
            if (CollectionUtils.isEmpty(audiences)) {
                break;
            }
            terminated = StringUtils.isBlank((nextToken = audiencesVo.getNextToken()));
            result = ResultUtil.success(audiences);
            // audienceV1 -> AmazonAdAudience
            List<AmazonAdAudience> amazonAdAudiences = new ArrayList<>();
            for (AudienceV1 audienceV1 : audiences) {
                amazonAdAudiences.add(convertAmazonAudienceV1ToAdAudienceTo(audienceV1));
            }

            // 入库
            List<String> audienceIds = amazonAdAudiences.stream().filter(Objects::nonNull).map(AmazonAdAudience::getAudienceId).collect(Collectors.toList());
            Map<String, AmazonAdAudience> audienceMap = amazonAudiencesService.listByAudienceIds(audienceIds).stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdAudience::getAudienceId, Function.identity()));
            ArrayList<AmazonAdAudience> addList = new ArrayList<>();
            ArrayList<AmazonAdAudience> updateList = new ArrayList<>();
            amazonAdAudiences.forEach(amazonAdAudience -> {
                AmazonAdAudience value = audienceMap.get(amazonAdAudience.getAudienceId());
                if (value == null) {
                    addList.add(amazonAdAudience);
                } else if (!Objects.equals(value, amazonAdAudience)) {
                    updateList.add(amazonAdAudience);
                }
            });
            amazonAudiencesService.batchAdd(addList);
            amazonAudiencesService.batchUpdate(updateList);
            // 重置重试次数
            rateLimitRetries = 5;
            tokenRetries = 1;
        }

        return result;
    }

    /**
     * AudienceV1 -> AmazonAdAudience
     *
     * @param audienceV1
     * @return
     */
    private AmazonAdAudience convertAmazonAudienceV1ToAdAudienceTo(AudienceV1 audienceV1) {
        AmazonAdAudience amazonAdAudience = new AmazonAdAudience();
        BeanUtils.copyProperties(audienceV1, amazonAdAudience);
        // fees
        if (audienceV1.getFees() != null) {
            List<AmazonAdAudienceFee> fees = new ArrayList<>();
            for (AudienceFee audienceFee : audienceV1.getFees()) {
                AmazonAdAudienceFee fee = new AmazonAdAudienceFee();
                BeanUtils.copyProperties(audienceFee, fee);
                fees.add(fee);
            }
            amazonAdAudience.setFees(fees);
            amazonAdAudience.setFeesString(JSONUtil.objectToJson(fees));
        }
        // forecasts
        AudienceForecast.SDInventoryForecastV1 all;
        if (audienceV1.getForecasts() != null
                && audienceV1.getForecasts().getInventoryForecasts() != null
                && audienceV1.getForecasts().getInventoryForecasts().getAll() != null
                && (all = audienceV1.getForecasts().getInventoryForecasts().getAll()) != null) {
            AudienceForecast.ForecastBucketV1 dailyReach = all.getDailyReach();
            if (dailyReach != null) {
                amazonAdAudience.setDailyReach(dailyReach.getLowerBoundInclusive() + "," + dailyReach.getUpperBoundExclusive());
            }
            AudienceForecast.ForecastBucketV1 dailyImpressions;
            if (all instanceof AudienceForecast.DSPInventoryForecastV1
                    && (dailyImpressions = ((AudienceForecast.DSPInventoryForecastV1) all).getDailyImpressions()) != null) { // DSP
                amazonAdAudience.setDailyReach(dailyImpressions.getLowerBoundInclusive() + "," + dailyImpressions.getUpperBoundExclusive());
            }
        }

        return amazonAdAudience;
    }
}
