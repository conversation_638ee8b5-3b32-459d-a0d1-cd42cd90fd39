package com.meiyunji.sponsored.service.cpc.vo;


import com.meiyunji.sponsored.common.enums.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@Data
@ApiModel
public class TargetHourParam {

    @ApiModelProperty(value = "shopId",required = true)
    private Integer shopId;

    //多店铺
    private List<Integer> shopIdList;
    private Integer puid;
    @ApiModelProperty("开始时间")
    private String startDate;
    @ApiModelProperty("结束时间")
    private String endDate;
    @ApiModelProperty("是否对比")
    private Integer isCompare;
    @ApiModelProperty("对比开始时间")
    private String startDateCompare;
    @ApiModelProperty("对比结束时间")
    private String endDateCompare;
    @ApiModelProperty("结束时间")
    private String weeks;
    @ApiModelProperty("targetId")
    private String targetId;

    @ApiModelProperty("排序字段")
    private String orderField;

    @ApiModelProperty("排序类型")
    private String orderType;

    @ApiModelProperty("pageNo")
    private Integer pageNo;

    @ApiModelProperty("pageSize")
    private Integer pageSize;



    private Integer uid;

    @ApiModelProperty("搜索字段名称")
    private String searchField;

    @ApiModelProperty("搜索内容--对应字段")
    private String searchValue;

    @ApiModelProperty("状态")
    private String status;


    @ApiModelProperty("广告类型 sp, sd, sb")
    private String type; // sp, sd, sb

    //StrategyEnum枚举类
    @ApiModelProperty("竞价策略类型 legacyForSales,autoForSales,manual->sp广告  none->sd sb广告")
    private String strategyType;

    @ApiModelProperty("预算状态 under->未超 exceeded->已超")
    private String budgetState;

    @ApiModelProperty("ids")
    private List<String> ids;

    @ApiModelProperty(value = "广告组合id")
    private String portfolioId;

    @ApiModelProperty("asin、msku、parentAsin（用于产品透视分析页面）")
    private String findType;
    private String findValue;

    //高级搜索
    @ApiModelProperty(value = "是否开启高级搜索")
    private Boolean useAdvanced;

    //高级搜索值
    @ApiModelProperty(value = "高级搜索展示量最小值")
    private Integer impressionsMin;  //展示量
    @ApiModelProperty(value = "高级搜索展示量最大值")
    private Integer impressionsMax;
    @ApiModelProperty(value = "高级搜索点击量小值")
    private Integer clicksMin;  //点击量
    @ApiModelProperty(value = "高级搜索点击量大值")
    private Integer clicksMax;
    @ApiModelProperty(value = "高级搜索点击率小值")
    private BigDecimal clickRateMin;  //点击率
    @ApiModelProperty(value = "高级搜索点击率大值")
    private BigDecimal clickRateMax;
    @ApiModelProperty(value = "高级搜索花费小值")
    private BigDecimal costMin; //花费
    @ApiModelProperty(value = "高级搜索花费大值")
    private BigDecimal costMax;
    @ApiModelProperty(value = "高级搜索cpc小值")
    private BigDecimal cpcMin;  //cpc
    @ApiModelProperty(value = "高级搜索cpc大值")
    private BigDecimal cpcMax;
    @ApiModelProperty(value = "高级搜索广告订单量小值")
    private Integer orderNumMin;  //广告订单量
    @ApiModelProperty(value = "高级搜索广告订单量大值")
    private Integer orderNumMax;
    @ApiModelProperty(value = "高级搜索sales小值")
    private BigDecimal salesMin;  //sales
    @ApiModelProperty(value = "高级搜索sales大值")
    private BigDecimal salesMax;
    @ApiModelProperty(value = "高级搜索acos小值")
    private BigDecimal acosMin;  //acos
    @ApiModelProperty(value = "高级搜索acos大值")
    private BigDecimal acosMax;
    @ApiModelProperty(value = "高级搜索roas小值")
    private BigDecimal roasMin;   //roas
    @ApiModelProperty(value = "高级搜索roas大值")
    private BigDecimal roasMax;
    @ApiModelProperty(value = "高级搜索广告订单转化率小值")
    private BigDecimal salesConversionRateMin;  //订单转化率
    @ApiModelProperty(value = "高级搜索广告订单转化率大值")
    private BigDecimal salesConversionRateMax;
    @ApiModelProperty(value = "高级搜索acots小值")
    private BigDecimal acotsMin;  //acots
    @ApiModelProperty(value = "高级搜索acots大值")
    private BigDecimal acotsMax;
    @ApiModelProperty(value = "高级搜索asots小值")
    private BigDecimal asotsMin;  //acos
    @ApiModelProperty(value = "高级搜索asots大值")
    private BigDecimal asotsMax;

    @ApiModelProperty(value = "高级搜索投放类型")
    private String filterTargetType;
    @ApiModelProperty(value = "高级搜索每日预算最小")
    private BigDecimal dailyBudgetMin;
    @ApiModelProperty(value = "高级搜索每日预算最大")
    private BigDecimal dailyBudgetMax;
    @ApiModelProperty(value = "高级搜索开始日期")
    private String filterStartDate;
    @ApiModelProperty(value = "高级搜索结束日期")
    private String filterEndDate;
    @ApiModelProperty(value = "高级搜索广告位顶部最小")
    private BigDecimal placementTopMin;
    @ApiModelProperty(value = "高级搜索广告位顶部最大")
    private BigDecimal placementTopMax;
    @ApiModelProperty(value = "高级搜索广告位产品页面最小")
    private BigDecimal placementProductMin;
    @ApiModelProperty(value = "高级搜索广告位产品页面最大")
    private BigDecimal placementProductMax;


    @ApiModelProperty(value = "高级搜索店铺销售额")
    private BigDecimal shopSales;

    @ApiModelProperty(value = "广告组合下的活动id")
    private List<String> campaignIdList;
    @ApiModelProperty(value = "付费方式")
    private String costType;

    @ApiModelProperty(value = "用于仅展示正在投放字段 勾选后传值 enabled")
    private String servingStatus;

    @ApiModelProperty("targetIds")
    private List<String> targetIds;

    private String adType;

    /**
     * 预算状态枚举类
     */
    public enum BudgetStateEnum implements BaseEnum {

        underBudget("under","未超过预算"),
        budgetExceeded("exceeded","已超预算"),
        ;

        BudgetStateEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private String code;
        private String desc;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getDescription() {
            return desc;
        }
    }


    /**
     * 搜索字段枚举
     */
    public enum SearchFieldEnum implements BaseEnum {

        Name("name","name","广告活动名称");

        ;
        private String field;
        private String column;
        private String desc;

        SearchFieldEnum(String field, String column, String desc) {
            this.field = field;
            this.column = column;
            this.desc = desc;
        }

        public String getColumn() {
            return column;
        }

        public String getField() {
            return field;
        }

        public String getDesc() {
            return desc;
        }

        @Override
        public String getCode() {
            return field;
        }

        @Override
        public String getDescription() {
            return desc;
        }
    }

    /**
     * 竞价策略类型
     */
    public enum StrategyEnum implements BaseEnum {
        legacyForSales("legacyForSales","动态竞价-只降低"),
        autoForSales("autoForSales","动态竞价-提高和降低"),
        manual("manual","固定竞价"),
        none("none","无策略,对应sb sd广告类型");
        ;
        private String code;
        private String desc;

        StrategyEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getDescription() {
            return desc;
        }
    }


}
