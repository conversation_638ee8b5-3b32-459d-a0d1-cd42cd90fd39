package com.meiyunji.sponsored.service.multiPlatform.walmart.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dao.IWalmartAdvertisingKeywordDao;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dto.WalmartAdGroupPageDTO;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingGroup;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingKeyword;

import com.meiyunji.sponsored.service.multiPlatform.walmart.qo.KeywordPageAggregateQo;
import com.meiyunji.sponsored.service.multiPlatform.walmart.qo.KeywordPageQo;
import com.meiyunji.sponsored.service.multiPlatform.walmart.util.WalmartAdDaoUtil;
import com.meiyunji.sponsored.service.multiPlatform.walmart.vo.KeywordPageAggregateVo;
import com.meiyunji.sponsored.service.multiPlatform.walmart.vo.KeywordPageVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description: walmart广告活动关键字Dao
 */

@Repository
public class WalmartAdvertisingKeywordDaoImpl extends BaseShardingDaoImpl<WalmartAdvertisingKeyword> implements IWalmartAdvertisingKeywordDao {
    @Override
    public void add(WalmartAdvertisingKeyword keyword) {
        StringBuilder sb = new StringBuilder("insert into t_walmart_advertising_keyword (puid,shop_id,marketplace_code,campaign_id,ad_group_id,keyword_id,state,keyword_text,match_type,bid,status,create_time,update_time )");
        sb.append(" values (?,?,?,?,?,?,?,?,?,?,?,now(),now() )");
        List<Object> argsList = new ArrayList<>();
        argsList.add(keyword.getPuid());
        argsList.add(keyword.getShopId());
        argsList.add(keyword.getMarketplaceCode());
        argsList.add(keyword.getCampaignId());
        argsList.add(keyword.getAdGroupId());
        argsList.add(keyword.getKeywordId());
        argsList.add(StringUtils.isBlank(keyword.getState()) ? "" : keyword.getState().toLowerCase());
        argsList.add(StringUtil.cutLimitedStr(keyword.getKeywordText(), 500));
        argsList.add(keyword.getMatchType());
        argsList.add(keyword.getBid());
        argsList.add(StringUtils.isBlank(keyword.getStatus()) ? "" : keyword.getStatus().toLowerCase());
        getJdbcTemplate(keyword.getPuid()).update(sb.toString(), argsList.toArray());
    }


    @Override
    public int delete(Integer puid, Long id) {
        return 0;
    }

    @Override
    public WalmartAdvertisingKeyword getById(Integer puid, Long id) {
        return null;
    }

    @Override
    public WalmartAdvertisingKeyword getByKeywordId(Integer puid, Integer shopId, String keywordId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("keyword_id", keywordId);
        return this.getByCondition(puid, builder.build());
    }

    @Override
    public int updateState(Integer puid, Integer shopId, String keywordId, String state) {
        String sb = "update " + this.getJdbcHelper().getTable() + " set state=?, update_time=now() where puid = ? and shop_id = ? and `keyword_id`=? ";
        List<Object> argsList = Lists.newArrayList();
        argsList.add(state);
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(keywordId);
        return getJdbcTemplate(puid).update(sb, argsList.toArray());
    }

    @Override
    public int updateBid(Integer puid, Integer shopId, String keywordId, String bid) {
        String sb = "update " + this.getJdbcHelper().getTable() + " set bid=?, update_time=now() where puid = ? and shop_id = ? and `keyword_id`=? ";
        List<Object> argsList = Lists.newArrayList();
        argsList.add(bid);
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(keywordId);
        return getJdbcTemplate(puid).update(sb, argsList.toArray());
    }

    @Override
    public List<WalmartAdvertisingKeyword> getByCampaignId(Integer puid, Long shopId, Long campaignId) {
        return null;
    }

    @Override
    public List<WalmartAdvertisingKeyword> getByShopIdListAndCampaignIdList(Integer puid, List<Integer> shopIds, List<String> campaignIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select keyword_id, update_time from ").append(this.getJdbcHelper().getTable());
        sql.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            sql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
        }
        return getJdbcTemplate(puid).query(sql.toString(), new BeanPropertyRowMapper<>(WalmartAdvertisingKeyword.class), argsList.toArray());
    }

    @Override
    public List<WalmartAdvertisingKeyword> getByGroupId(Integer puid, Long shopId, Long campaignId, Long adGroupId) {
        return null;
    }

    @Override
    public Page getPageList(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
        return null;
    }

    @Override
    public Page getPageListWithReport(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
        return null;
    }

    @Override
    public Page<KeywordPageVo> getPageList(int puid, KeywordPageQo qo) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder selectSql = new StringBuilder("select k.shop_id shopId, k.keyword_id keywordId, k.keyword_text keywordText, k.match_type matchType, k.bid bid, k.state state, k.status reviewStatus, k.campaign_id campaignId, k.ad_group_id adGroupId, ").append(WalmartAdDaoUtil.REPORT_FIELDS).append(" from ").append(this.getJdbcHelper().getTable()).append(" k ");
        StringBuilder countSql = new StringBuilder("SELECT count(*) FROM ").append(this.getJdbcHelper().getTable()).append(" k ");
        //连接sql
        StringBuilder sql = new StringBuilder(" left join ").append(this.getPageJoinReportSql(puid, qo, argsList));
        //拼接关键词条件sql
        sql.append(this.getPageListWhereSql(puid, qo, argsList));
        selectSql.append(sql);
        countSql.append(sql);
        //排序
        selectSql.append(WalmartAdDaoUtil.getOrderBySql(qo.getOrderField(), qo.getOrderType(), "k.keyword_id"));
        return getPageResultByClass(puid, qo.getPageNo(), qo.getPageSize(), countSql.toString(), argsList.toArray(), selectSql.toString(), argsList.toArray(), KeywordPageVo.class);
    }

    @Override
    public KeywordPageAggregateVo getPageAggregate(int puid, KeywordPageAggregateQo qo) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder selectSql = new StringBuilder("select ").append(WalmartAdDaoUtil.REPORT_FIELDS_SUM).append(" from ").append(this.getJdbcHelper().getTable()).append(" k ");
        //连接sql
        StringBuilder sql = new StringBuilder(" join ").append(this.getPageJoinReportSql(puid, qo, argsList));
        //拼接关键词条件sql
        sql.append(this.getPageListWhereSql(puid, qo, argsList));
        selectSql.append(sql);
        List<KeywordPageAggregateVo> list = getJdbcTemplate(puid).query(selectSql.toString(), new BeanPropertyRowMapper<>(KeywordPageAggregateVo.class), argsList.toArray());
        return list.size() == 1 ? list.get(0) : new KeywordPageAggregateVo();
    }

    /**
     * 列表页关键词表条件sql
     */
    private String getPageListWhereSql(int puid, KeywordPageAggregateQo qo, List<Object> argsList) {
        StringBuilder sql = new StringBuilder(" where k.puid = ? ");
        argsList.add(puid);
        sql.append(SqlStringUtil.dealInList("k.shop_id", qo.getShopIdList(), argsList));
        if (CollectionUtils.isNotEmpty(qo.getCampaignIdList())) {
            sql.append(SqlStringUtil.dealInList("k.campaign_id", qo.getCampaignIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(qo.getGroupIdList())) {
            sql.append(SqlStringUtil.dealInList("k.ad_group_id", qo.getGroupIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(qo.getState())) {
            sql.append(SqlStringUtil.dealInList("k.state", qo.getState(), argsList));
        }
        if (CollectionUtils.isNotEmpty(qo.getReviewStatus())) {
            sql.append(SqlStringUtil.dealInList("k.status", qo.getReviewStatus(), argsList));
        }
        if (CollectionUtils.isNotEmpty(qo.getMatchType())) {
            sql.append(SqlStringUtil.dealInList("k.match_type", qo.getMatchType(), argsList));
        }
        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
            if ("blur".equals(qo.getSearchType())) { //模糊搜索
                sql.append(" and lower(k.keyword_text) like ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(qo.getSearchValue().toLowerCase()) + "%");
            } else {//默认精确
                List<String> valueList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA);
                if (CollectionUtils.isNotEmpty(valueList)) {
                    if (valueList.size() > 1) {
                        List<String> lowerCaseValue = new ArrayList<>();
                        for (String value : valueList) {
                            lowerCaseValue.add(value.trim().toLowerCase());
                        }
                        sql.append(SqlStringUtil.dealInList("lower(k.keyword_text)", lowerCaseValue, argsList));
                    } else {
                        sql.append("and lower(k.keyword_text) = ? ");
                        argsList.add(qo.getSearchValue().trim().toLowerCase());
                    }
                }
            }
        }
        return sql.toString();
    }

    /**
     * 关键词列表页连接报告数据sql
     */
    private String getPageJoinReportSql(int puid, KeywordPageAggregateQo qo, List<Object> argsList) {
        StringBuilder sb = new StringBuilder(" ( select `puid`,`shop_id`,`campaign_id`,`ad_group_id`,`keyword_id`, ")
                .append(WalmartAdDaoUtil.reportFieldsSum(qo.getAttributeDayType()))
                .append(" from `t_walmart_advertising_keyword_report` ");
        sb.append(" where puid = ? ");
        argsList.add(puid);
        sb.append(SqlStringUtil.dealInList("shop_id", qo.getShopIdList(), argsList));
        sb.append(" and report_date >= ? and report_date <= ? ");
        argsList.add(qo.getStartDate());
        argsList.add(qo.getEndDate());
        if (CollectionUtils.isNotEmpty(qo.getCampaignIdList())) {
            sb.append(SqlStringUtil.dealInList("campaign_id", qo.getCampaignIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(qo.getGroupIdList())) {
            sb.append(SqlStringUtil.dealInList("ad_group_id", qo.getGroupIdList(), argsList));
        }
        sb.append(" group by puid, shop_id, campaign_id, ad_group_id, keyword_id ) r ");
        sb.append(" on k.puid = r.puid and k.shop_id = r.shop_id and k.campaign_id = r.campaign_id ")
                .append(" and k.ad_group_id = r.ad_group_id and k.keyword_id = r.keyword_id ");
        return sb.toString();
    }

    @Override
    public int batchInsert(List<WalmartAdvertisingKeyword> list) {
        StringBuilder sql = new StringBuilder("insert into `t_walmart_advertising_keyword` (`puid`, `shop_id`, `campaign_id`, `ad_group_id`, " +
                "`keyword_id`, `state`, `keyword_text`, `match_type`, `bid`, `status`) values");
        List<Object> argsList = Lists.newArrayList();
        for (WalmartAdvertisingKeyword e : list) {
            sql.append(" (?, ?, ?, ?, ?, ?, ?, ?, ?, ?),");
            argsList.add(e.getPuid());
            argsList.add(e.getShopId());
            argsList.add(e.getCampaignId());
            argsList.add(e.getAdGroupId());
            argsList.add(e.getKeywordId());
            argsList.add(e.getState());
            argsList.add(e.getKeywordText());
            argsList.add(e.getMatchType());
            argsList.add(e.getBid());
            argsList.add(e.getStatus());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `state`=values(state), `status`=values(status), `bid`=values(bid), `update_time`=now()");
        return getJdbcTemplate(list.get(0).getPuid()).update(sql.toString(), argsList.toArray());
    }

    @Override
    public int deleteByCampaignId(Integer puid, Long shopId, Long campaignId) {
        return 0;
    }

    @Override
    public int deleteByCampaignIdAndGroupIdBatch(Integer puid, List<Integer> shopIdList,
                                                 List<String> delCampaignIdList, List<String> delGroupIdList,
                                                 List<String> keywordIdList) {
        if (CollectionUtils.isEmpty(keywordIdList)) {
            return 0;
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("DELETE from t_walmart_advertising_keyword where puid = ?");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(delCampaignIdList)) {
            sql.append(SqlStringUtil.dealInList("campaign_id", delCampaignIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(delGroupIdList)) {
            sql.append(SqlStringUtil.dealInList("ad_group_id", delGroupIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(keywordIdList)) {
            sql.append(SqlStringUtil.dealInList("keyword_id", keywordIdList, argsList));
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public int getCountByAdGroupId(Integer puid, Long shopId, Long campaignId, Long adGroupId) {
        return 0;
    }

    @Override
    public WalmartAdvertisingKeyword getByKeywordId(Integer puid, Long shopId, Long keywrodId) {
        return null;
    }

    @Override
    public WalmartAdvertisingKeyword getByKeywordId(Integer puid, Long shopId, Long campaignId, Long adGroupId, Long keywrodId) {
        return null;
    }
//    private static Logger logger = LoggerFactory.getLogger(WalmartAdvertisingKeywordDaoImpl.class);
//    private static final WalmartAdvertisingKeywordMapper mapper = new WalmartAdvertisingKeywordMapper();
//    private static final WalmartAdReportMapper<WalmartAdvertisingKeyword, WalmartAdvertisingKeywordMapper>
//            listWithReportMapper = new WalmartAdReportMapper<>(mapper);
//
//    @Override
//    protected String getSeqTable() {
//        return "t_dxm_walmart_advertising_item_seq";
//    }
//
//    @Override
//    public Long add(WalmartAdvertisingKeyword keyword) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate(Long.valueOf(keyword.getPuid()));
//        Long id = getId(keyword.getPuid());
//        StringBuilder sb = new StringBuilder();
//        sb.append("insert into t_walmart_advertising_keyword (id,puid,shop_id,campaign_id,ad_group_id,keyword_id,state,keyword_text,match_type,bid,status,create_time,update_time )");
//        sb.append(" values (?,?,?,?,?,?,?,?,?,?,?,now(),now() )");
//        List<Object> argsList = new ArrayList<>();
//        argsList.add(id);
//        argsList.add(keyword.getPuid());
//        argsList.add(keyword.getShopId());
//        argsList.add(keyword.getCampaignId());
//        argsList.add(keyword.getAdGroupId());
//        argsList.add(keyword.getKeywordId());
//        argsList.add(StringUtils.isBlank(keyword.getState()) ? "" : keyword.getState().toLowerCase());
//        argsList.add(StringUtil.cutLimitedStr(keyword.getKeywordText(),500));
//        argsList.add(keyword.getMatchType());
//        argsList.add(keyword.getBid());
//        argsList.add(StringUtils.isBlank(keyword.getStatus()) ? "" : keyword.getStatus().toLowerCase());
//        int t = update(jdbcTemplate, sb.toString(), argsList.toArray());
//        return t > 0 ? id : 0L;
//    }
//
//    @Override
//    public int update(WalmartAdvertisingKeyword keyword) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate(Long.valueOf(keyword.getPuid()));
//        Map<String, Object> params = getParamMap(keyword);
//        return updateTable(jdbcTemplate, "t_walmart_advertising_keyword", params, "where `id` = " + keyword.getId());
//    }
//
//

//
//    @Override
//    public int delete(Integer puid, Long id) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate(Long.valueOf(puid));
//        return jdbcTemplate.update("DELETE from t_walmart_advertising_keyword where puid = ? and id = ?", puid, id);
//    }
//
//    @Override
//    public WalmartAdvertisingKeyword getById(Integer puid, Long id) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate((long) puid);
//        String sql = "select * from `t_walmart_advertising_keyword` where `id`=? and puid = ?";
//        List<WalmartAdvertisingKeyword> list = jdbcTemplate.query(sql, new Object[]{id, puid}, mapper);
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    @Override
//    public List<WalmartAdvertisingKeyword> getByCampaignId(Integer puid, Long shopId, Long campaignId) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate((long) puid);
//        String sql = "select * from `t_walmart_advertising_keyword` where `puid`=? and shop_id = ? and campaign_id = ?";
//        return jdbcTemplate.query(sql, new Object[]{puid, shopId, campaignId}, mapper);
//    }
//
//    @Override
//    public List<WalmartAdvertisingKeyword> getByGroupId(Integer puid, Long shopId, Long campaignId, Long adGroupId) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate((long) puid);
//        String sql = "select * from `t_walmart_advertising_keyword` where `puid`=? and shop_id = ? and campaign_id = ? and ad_group_id = ?";
//        return jdbcTemplate.query(sql, new Object[]{puid, shopId, campaignId, adGroupId}, mapper);
//    }
//
//    @Override
//    public Page getPageList(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate((long) puid);
//
//        String startSql = "select * from t_walmart_advertising_keyword p INNER JOIN (";
//
//        String selectStr = "select t1.id from ";
//        String countStr = "select count(*) from ";
//
//        StringBuilder tableSql = new StringBuilder();
//        tableSql.append("t_walmart_advertising_keyword t1");
//
//        List<Object> argsList = new ArrayList<>();
//
//        StringBuilder whereSql = new StringBuilder(" where t1.puid = " + puid);
//
//        // 组合Sql
//        combinationSqlString(queryParams, argsList, whereSql);
//
//        StringBuilder countSql = new StringBuilder();
//        countSql.append(countStr);
//        countSql.append(tableSql);
//        countSql.append(whereSql);
//
//        StringBuilder selectSql = new StringBuilder();
//        selectSql.append(selectStr);
//        selectSql.append(tableSql);
//        selectSql.append(whereSql);
//
//        String endSql = ") a ON p.id=a.id ";
//
//        Object[] args = argsList.toArray();
//
//        logger.info("{}", countSql);
//        logger.info("{}", selectSql);
//        logger.info(Arrays.toString(args));
//        return getNewPageResult(jdbcTemplate, pageNo, pageSize, countSql.toString(), args, selectSql.toString(),
//                startSql, endSql, args, mapper);
//    }
//
//    @Override
//    public Page getPageListWithReport(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate((long) puid);
//        StringBuilder selectSql = new StringBuilder("SELECT t1.*," + WalmartAdDaoUtil.REPORT_FIELDS + " FROM `t_walmart_advertising_keyword` t1 ");
//        StringBuilder countSql = new StringBuilder("SELECT count(*) FROM `t_walmart_advertising_keyword` t1 ");
//        // 条件搜索
//        List<Object> argsList = Lists.newArrayList();
//        // 联表查询，统计指定时间段内的报告数据
//        StringBuilder reportSql = new StringBuilder("select `puid`,`shop_id`,`campaign_id`,`ad_group_id`,`keyword_id`," + WalmartAdDaoUtil.reportFieldsSum(MapUtils.getInteger(queryParams, "attributeDay")));
//        reportSql.append(" from `t_walmart_advertising_keyword_report`").append(" where puid = ? ");
//        argsList.add(puid);
//        WalmartAdDaoUtil.setLeftJoinConditions(queryParams, reportSql, argsList);
//        reportSql.append(" group by puid, shop_id, campaign_id, ad_group_id,keyword_id ");
//        String leftJoinSql = String.format(" left join ( %s ) t2 " +
//                " on t1.puid = t2.puid and t1.shop_id = t2.shop_id and t1.campaign_id = t2.campaign_id " +
//                "and t1.ad_group_id = t2.ad_group_id and t1.keyword_id = t2.keyword_id", reportSql);
//        StringBuilder whereSql = new StringBuilder(leftJoinSql + " where t1.puid = ? ");
//        argsList.add(puid);
//
//        combinationSqlString(queryParams, argsList, whereSql);
//        String sortName = MapUtils.getString(queryParams, "sortName");
//        Integer sortValue = MapUtils.getIntValue(queryParams, "sortValue");
//        selectSql.append(whereSql).append(WalmartAdDaoUtil.getOrderByState(sortName, sortValue));
//        countSql.append(whereSql);
//
//        logger.info("{}", countSql);
//        logger.info("{}", selectSql);
//        Object[] args = argsList.toArray();
//        logger.info(Arrays.toString(args));
//        return getPageResult(jdbcTemplate, pageNo, pageSize, countSql.toString(), args, selectSql.toString(), args, listWithReportMapper);
//    }
//
//    private void combinationSqlString(Map<String, Object> queryParams,
//                                      List<Object> argsList, StringBuilder whereSql) {
//        int matchType = MapUtils.getIntValue(queryParams, "matchType");
//        String match = matchType == 2 ? null : matchType == 1 ? "" : "%";
//        Object shopIds = MapUtils.getObject(queryParams, "shopId");
//        if (Objects.nonNull(shopIds)) {
//            whereSql.append(" and t1.shop_id in (").append(Joiner.on(",").join((List<Long>) shopIds)).append(") ");
//        }
//        String keywordText = MapUtils.getString(queryParams, "keywordText");
//        if (StringUtils.isNotBlank(keywordText)) {
//            DaoUtil.dealMultiSearchCondition(queryParams, "keywordText", "t1", "keyword_text", match, whereSql, argsList);
//        }
//        String campaignId = MapUtils.getString(queryParams, "campaignId");
//        if (StringUtils.isNotBlank(campaignId)) {
//            whereSql.append(" and t1.campaign_id = ?");
//            argsList.add(campaignId);
//        }
//        String keywordId = MapUtils.getString(queryParams, "keywordId");
//        if (StringUtils.isNotBlank(keywordId)) {
//            whereSql.append(" and t1.keyword_id = ?");
//            argsList.add(keywordId);
//        }
//        Object campaignIds = MapUtils.getObject(queryParams, "campaignIds");
//        if (Objects.nonNull(campaignIds)) {
//            whereSql.append(" and t1.campaign_id in (").append(Joiner.on(",").join((List<Long>) campaignIds)).append(") ");
//        }
//        String adGroupId = MapUtils.getString(queryParams, "adGroupId");
//        if (StringUtils.isNotBlank(adGroupId)) {
//            whereSql.append(" and t1.ad_group_id = ?");
//            argsList.add(adGroupId);
//        }
//
//        String status = MapUtils.getString(queryParams, "keywordStatus");
//        if (StringUtils.isNotBlank(status)) {
//            whereSql.append(" and t1.status = ?");
//            argsList.add(status);
//        }
//        String state = MapUtils.getString(queryParams, "state");
//        if (StringUtils.isNotBlank(state)) {
//            whereSql.append(" and t1.state = ?");
//            argsList.add(state);
//        }
//    }
//

//
//    @Override
//    public int getCountByAdGroupId(Integer puid, Long shopId, Long campaignId, Long adGroupId) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate((long) puid);
//        String sql = "select count(*) from t_walmart_advertising_keyword where puid = ? and shop_id = ? and campaign_id = ? and ad_group_id = ?";
//        return jdbcTemplate.queryForObject(sql, new Object[]{puid, shopId, campaignId, adGroupId}, Integer.class);
//    }
//
//    @Override
//    public WalmartAdvertisingKeyword getByKeywordId(Integer puid, Long shopId, Long keywrodId) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate((long) puid);
//        String sql = "select * from `t_walmart_advertising_keyword` where `puid`=? and shop_id = ? and keyword_id= ?";
//        List<WalmartAdvertisingKeyword> list = jdbcTemplate.query(sql, new Object[]{puid, shopId, keywrodId}, mapper);
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    @Override
//    public WalmartAdvertisingKeyword getByKeywordId(Integer puid, Long shopId, Long campaignId, Long adGroupId, Long keywrodId) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate((long) puid);
//        String sql = "select * from `t_walmart_advertising_keyword` where `puid`=? and shop_id = ? and campaign_id = ? and ad_group_id = ? and keyword_id= ?";
//        List<WalmartAdvertisingKeyword> list = jdbcTemplate.query(sql, new Object[]{puid, shopId, campaignId, adGroupId, keywrodId}, mapper);
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }


    @Override
    public int deleteByCampaignId(Integer puid, Integer shopId, String campaignId) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(puid);
        return jdbcTemplate.update("DELETE from t_walmart_advertising_keyword where puid = ? and shop_id = ? and campaign_id = ?", puid, shopId, campaignId);
    }

    @Override
    public List<WalmartAdvertisingKeyword> getByCampaignId(Integer puid, Integer shopId, String campaignId) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(puid);
        String sql = "select * from `t_walmart_advertising_keyword` where `puid`=? and shop_id = ? and campaign_id = ?";
        return jdbcTemplate.query(sql, new Object[]{puid, shopId, campaignId}, getRowMapper());
    }


    @Override
    public int update(WalmartAdvertisingKeyword keyword) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(keyword.getPuid());
        String sb = "update " + this.getJdbcHelper().getTable() + " set  ad_group_id= ? , " +
                " keyword_text = ? , match_type = ? , bid = ? , status = ? , state = ?   where puid = ? and shop_id = ? and `keyword_id`=? ";
        List<Object> argsList = Lists.newArrayList();
        argsList.add(keyword.getAdGroupId());
        argsList.add(keyword.getKeywordText());
        argsList.add(keyword.getMatchType());
        argsList.add(keyword.getBid());
        argsList.add(StringUtils.isBlank(keyword.getStatus()) ? "" : keyword.getStatus().toLowerCase());
        argsList.add(StringUtils.isBlank(keyword.getState()) ? "" : keyword.getState().toLowerCase());
        argsList.add(keyword.getPuid());
        argsList.add(keyword.getShopId());
        argsList.add(keyword.getKeywordId());
        return jdbcTemplate.update(sb, argsList.toArray());
    }


    @Override
    public int deleteByKeywordId(Integer puid, Integer shopId, String keywordId) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(puid);
        return jdbcTemplate.update("DELETE from t_walmart_advertising_keyword where puid = ? and shop_id = ? and keyword_id = ?", puid, shopId, keywordId);
    }

}
