package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportFilterDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created by xp on 2021/4/14.
 * 广告组下的否定投放列表页入参
 */
@Data
@ApiModel
public class NeTargetingPageParam {
    private Integer uid;
    private String uuid;
    @ApiModelProperty("类型 sp sb sd")
    private String type;
    @ApiModelProperty
    private Integer pageNo;
    @ApiModelProperty
    private Integer pageSize;
    @ApiModelProperty(value = "店铺ID",required = true)
    private Integer shopId;
    @ApiModelProperty(value = "店铺ID",required = true)
    private Integer puid;
    @ApiModelProperty("广告活动ID")
    private String campaignId;
    @ApiModelProperty("广告组ID")
    private String groupId;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("操作状态")
    private String state;
    @ApiModelProperty("服务状态")
    private String servingStatus;
    @ApiModelProperty("搜索值")
    private String searchValue;
    @ApiModelProperty("搜索字段")
    private String searchField;
    private Integer dxmCampaignId;
    private String startDate;
    private String endDate;

    @ApiModelProperty("广告组合ID")
    private String portfolioId;
    @ApiModelProperty(value = "广告组合下的活动id")
    private List<String> campaignIdList;
    @ApiModelProperty("广告组合下的活动的广告组ID")
    private List<String> groupIdList;

    /**
     * 否定投放报告筛选dto
     */
    private NeTargetReportFilterDto neTargetReportFilterDto;

}
