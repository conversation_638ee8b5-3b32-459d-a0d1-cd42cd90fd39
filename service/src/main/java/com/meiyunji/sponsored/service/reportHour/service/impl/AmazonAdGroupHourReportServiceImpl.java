package com.meiyunji.sponsored.service.reportHour.service.impl;

import com.google.api.client.util.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.sellfox.ams.api.entry.HourlyReportDataPb;
import com.meiyunji.sellfox.ams.api.service.*;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.grpc.common.GetGroupHourReportResponsePb;
import com.meiyunji.sponsored.grpc.entry.ReportDateModelPb;
import com.meiyunji.sponsored.rpc.adAggregateHour.AdPageBasicData;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSbGroupReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSdGroupReportDao;
import com.meiyunji.sponsored.service.cpc.dto.AdGroupReportHourlyDTO;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdFeedReportService;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.dashboard.dto.MultiThreadQueryParamDto;
import com.meiyunji.sponsored.service.reportHour.constants.HourConvert;
import com.meiyunji.sponsored.service.reportHour.service.IAmazonAdGroupHourReportService;
import com.meiyunji.sponsored.service.reportHour.utils.AggregationDataUtil;
import com.meiyunji.sponsored.service.reportHour.utils.ReportChartUtil;
import com.meiyunji.sponsored.service.reportHour.vo.*;
import com.meiyunji.sponsored.service.util.PbUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2023/12/27 21:08
 * @describe:
 */
@Service
@Slf4j
public class AmazonAdGroupHourReportServiceImpl implements IAmazonAdGroupHourReportService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Qualifier("adFeedBlockingStub")
    @Autowired
    private AmsApiGrpc.AmsApiBlockingStub adFeedBlockingStub;

    @Resource
    private MultiThreadQueryAndMergeUtil multiThreadQueryAndMergeUtil;

    @Autowired
    private IAmazonAdGroupReportDao amazonAdGroupReportDao;

    @Autowired
    private CpcPageIdsHandler cpcPageIdsHandler;

    @Autowired
    private IAmazonAdSbGroupReportDao amazonAdSbGroupReportDao;

    @Autowired
    private IAmazonAdSdGroupReportDao amazonAdSdGroupReportDao;

    @Autowired
    private IAmazonAdFeedReportService amazonAdFeedReportService;

    @Autowired
    private CpcShopDataService cpcShopDataService;

    @Override
    public GetGroupHourReportResponsePb.GetGroupHourReportResponse.GroupHour getAggregateList(int puid, List<String> idList, GroupAggregateHourVo param, ReportDateModelPb.ReportDateModel dateModel) {
        AdPageBasicData pageBasic = param.getAdPageBasicData();
        Integer shopId = Optional.of(pageBasic.getShopId()).map(Int32Value::getValue).orElseGet(null);
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shopAuth == null) {
            return null;
        }

        //取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(shopAuth.getId(), pageBasic.getStartDate().replace("-",""), pageBasic.getEndDate().replace("-",""));
        List<AdGroupHourVo> list = Lists.newArrayList();
        List<AdGroupHourVo> compares = Lists.newArrayList();
        if (dateModel == ReportDateModelPb.ReportDateModel.HOURLY) {
            list = getAggregateHourList(shopAuth, puid, idList, param);
        } else if (dateModel == ReportDateModelPb.ReportDateModel.DAILY) {
            list = getAggregateHourDayList(puid, idList, param, compares);
        } else if (dateModel == ReportDateModelPb.ReportDateModel.WEEKLY) {
            list = getAggregateHourWeekList(puid, idList, param, compares);
        } else if (dateModel == ReportDateModelPb.ReportDateModel.MONTHLY) {
            list = getAggregateHourMonthList(puid, idList, param, compares);
        }

        GetGroupHourReportResponsePb.GetGroupHourReportResponse.GroupHour.Builder groupHourBuilder = GetGroupHourReportResponsePb
                .GetGroupHourReportResponse.GroupHour.newBuilder();
        if (CollectionUtils.isNotEmpty(list)) {
            for (AdGroupHourVo vo : list) {
                if (Constants.SB.equalsIgnoreCase(param.getType()) || Constants.SD.equalsIgnoreCase(param.getType())) {
                    vo.setAdOtherSaleNum(0);
                    vo.setAdOtherSaleNumCompare(0);
                    vo.setAdOtherSaleNumCompareRate(BigDecimal.ZERO);
                }
                vo.setAcots(shopSalesByDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(shopSalesByDate, 4, RoundingMode.HALF_UP));
                vo.setAsots(shopSalesByDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().multiply(new BigDecimal("100")).divide(shopSalesByDate, 4, RoundingMode.HALF_UP));
            }
            boolean isSorted = StringUtils.isNotBlank(pageBasic.getOrderField()) &&
                    Constants.isADOrderField(pageBasic.getOrderField(), AdGroupHourVo.class);
            if (isSorted) {
                PageUtil.sortedByOrderField(list, pageBasic.getOrderField(), pageBasic.getOrderType());
            }
        }
        groupHourBuilder.addAllList(list.stream().filter(Objects::nonNull)
                .map(e -> (PbUtil.toGroupHourReportPb(e, shopSalesByDate))).collect(Collectors.toList()));
        AdGroupHourVo summaryVO = summary(list, shopSalesByDate);
        if (pageBasic.getIsCompare().getValue() == 1 && dateModel != ReportDateModelPb.ReportDateModel.HOURLY) {
            AdGroupHourVo compareVO = summary(compares, shopSalesByDate);
            summaryVO.compareDataSet(compareVO);
        }
        groupHourBuilder.setSummary(PbUtil.toGroupHourReportPb(summaryVO, shopSalesByDate));
        groupHourBuilder.addAllChart(ReportChartUtil.getGroupHourChartData(list, false));

        //对比数据,chart图数据
        if (pageBasic.getIsCompare().getValue() == 1) {
            List<AdGroupHourVo> groupHourVos = list.stream().map(item -> {
                AdGroupHourVo vo = new AdGroupHourVo();
                vo.setLabel(item.getLabel());
                vo.setAdCost(item.getAdCostCompare());
                vo.setImpressions(item.getImpressionsCompare());
                vo.setClicks(item.getClicksCompare());
                vo.setCpa(item.getCpaCompare());
                vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                vo.setCtr(item.getCtrCompare());
                vo.setCvr(item.getCvrCompare());
                vo.setAcots(item.getAcotsCompare());
                vo.setAcos(item.getAcosCompare());
                vo.setRoas(item.getRoasCompare());
                vo.setAdOrderNum(item.getAdOrderNumCompare());
                vo.setSelfAdOrderNum(item.getSelfAdOrderNumCompare());
                vo.setOtherAdOrderNum(item.getOtherAdOrderNumCompare());
                vo.setAdSale(item.getAdSaleCompare());
                vo.setAdSelfSale(item.getAdSelfSaleCompare());
                vo.setAdOtherSale(item.getAdOtherSaleCompare());
                vo.setAdSaleNum(item.getAdSaleNumCompare());
                vo.setAdSelfSaleNum(item.getAdSelfSaleNumCompare());
                vo.setAdOtherSaleNum(item.getAdOtherSaleNumCompare());
                return vo;
            }).collect(Collectors.toList());
            groupHourBuilder.addAllChart(ReportChartUtil.getGroupHourChartData(groupHourVos, true));
        }

        return groupHourBuilder.build();
    }

    @Override
    public List<AdGroupHourVo> getAggregateHourList(ShopAuth shopAuth, int puid, List<String> idsList, GroupAggregateHourVo param) {
        if (shopAuth == null) {
            return null;
        }
        return amazonAdFeedReportService.listAggregateGroupHourList(shopAuth, idsList, param);

//        AdPageBasicData pageBasicInfo = param.getAdPageBasicData();
//        //获取小时级数据
//        GroupHourlyRequestPb.GroupHourlyRequest.Builder builder = GroupHourlyRequestPb.GroupHourlyRequest.newBuilder();
//        builder.setSellerId(shopAuth.getSellingPartnerId());
//        builder.setMarketplaceId(shopAuth.getMarketplaceId());
//        builder.setStartDate(pageBasicInfo.getStartDate());
//        builder.setEndDate(pageBasicInfo.getEndDate());
//        Optional.ofNullable(param.getWeeks()).filter(StringUtils::isNotBlank).map(w -> StringUtil.splitInt(w, ",")).ifPresent(builder::addAllWeekday);
//        builder.setType(Optional.ofNullable(pageBasicInfo.getType()).filter(StringUtils::isNotBlank).orElse(Constants.SP));
//        List<GroupHourlyReportResponsePb.GroupHourlyReportResponse> resultList = multiThreadQueryAndMergeUtil.
//                multiThreadQuery(ThreadPoolUtil.getAggregatePlacementSyncPool(), () -> idsList, builder, this::multiQueryGroupResult);
//        //merge result
//        GroupHourlyReportResponsePb.GroupHourlyReportResponse mergeResult = mergeGroupHourResultResp(resultList);
//
//        //获取小时对比数据
//        Map<Integer, HourlyReportDataPb.HourlyReportData> hourlyReportDataMap = new HashMap<>();
//        Map<Integer, HourlyReportDataPb.HourlyReportData> hourlyReportDataCompareMap = new HashMap<>();
//        if (PlacementHourParam.CompareEnum.TRUE.getCode().equals(pageBasicInfo.getIsCompare().getValue())) {
//            builder.setStartDate(pageBasicInfo.getStartDateCompare());
//            builder.setEndDate(pageBasicInfo.getEndDateCompare());
//
//            List<GroupHourlyReportResponsePb.GroupHourlyReportResponse> compareResultList = multiThreadQueryAndMergeUtil.
//                    multiThreadQuery(() -> idsList, builder, this::multiQueryGroupResult);
//            GroupHourlyReportResponsePb.GroupHourlyReportResponse compareMergeResult = mergeGroupHourResultResp(compareResultList);
//            hourlyReportDataCompareMap = getIntegerHourlyReportDataMap(compareMergeResult, hourlyReportDataCompareMap);
//        }
//
//        //组装数据
//        hourlyReportDataMap = getIntegerHourlyReportDataMap(mergeResult, hourlyReportDataMap);
//        List<AdGroupHourVo> voList = new ArrayList<>(24);
//        //按24小时返回数据
//        for (Integer hour : HourConvert.hourMap.keySet()) {
//            AdGroupHourVo adCampaignHourVo = handleVo(hour, hourlyReportDataMap.get(hour),
//                    hourlyReportDataCompareMap.get(hour));
//            voList.add(adCampaignHourVo);
//        }
//        if (CollectionUtils.isNotEmpty(voList)) {
//            //新增占比数据指标
//            AdMetricDto adMetricDto = new AdMetricDto();
//            filterSumMetricData(voList, adMetricDto);
//            filterMetricData(voList, adMetricDto);
//        }
//        return voList;
    }

    @Override
    public List<AdGroupHourVo> getAggregateHourDayList(int puid, List<String> idsList, GroupAggregateHourVo param, List<AdGroupHourVo> compares) {
        List<AdGroupHourVo> reports = getAggregateGroupDailyReports(puid, idsList, param, false);
        reports = reports.stream().filter(Objects::nonNull).sorted((r1, r2) ->
                        DateUtil.compareDate(DateUtil.strToDate(r1.getDate(), "yyyy-MM-dd"), DateUtil.strToDate(r2.getDate(), "yyyy-MM-dd")))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }
        AdPageBasicData basicPageInfo = param.getAdPageBasicData();
        if (PlacementHourParam.CompareEnum.TRUE.getCode().equals(basicPageInfo.getIsCompare().getValue())) {
            List<AdGroupHourVo> compareList = getAggregateGroupDailyReports(puid, idsList, param, true);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = compareList.stream().filter(Objects::nonNull).sorted((r1, r2) ->
                            DateUtil.compareDate(DateUtil.strToDate(r1.getDate(), "yyyy-MM-dd"), DateUtil.strToDate(r2.getDate(), "yyyy-MM-dd")))
                    .collect(Collectors.toList());
            return paddingDayCompare(basicPageInfo, reports, compareList);
        } else {
            return reports;
        }
    }

    private List<AdGroupHourVo> paddingDayCompare(AdPageBasicData pageBasic, List<AdGroupHourVo> reports, List<AdGroupHourVo> compareList) {
        LocalDate start = LocalDate.parse(pageBasic.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate end = LocalDate.parse(pageBasic.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate startCompare = LocalDate.parse(pageBasic.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
        List<AdGroupHourVo> resList = new ArrayList<>();
        Map<String, AdGroupHourVo> map = StreamUtil.toMap(reports, AdGroupHourVo::getLabel);
        Map<String, AdGroupHourVo> compareMap = StreamUtil.toMap(compareList, AdGroupHourVo::getLabel);
        for (; !start.isAfter(end); start = start.plusDays(1), startCompare = startCompare.plusDays(1)) {
            AdGroupHourVo report = map.get(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
            AdGroupHourVo compareReport = compareMap.get(startCompare.format(DateTimeFormatter.ISO_LOCAL_DATE));
            if (Objects.isNull(report) && Objects.isNull(compareReport)) {
                continue;
            }
            AdGroupHourVo vo;
            if (Objects.isNull(report)) {
                vo = new AdGroupHourVo();
                vo.setLabel(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
            } else {
                vo = report;
            }
            vo.compareDataSet(compareReport);
            resList.add(vo);
        }
        return resList;
    }

    @Override
    public List<AdGroupHourVo> getAggregateHourWeekList(int puid, List<String> idsList, GroupAggregateHourVo param, List<AdGroupHourVo> compares) {
        List<AdGroupHourVo> reports = getAggregateGroupDailyReports(puid, idsList, param, false);
        AdPageBasicData basicPageInfo = param.getAdPageBasicData();
        reports = ReportChartUtil.getGroupWeekReportVos(basicPageInfo.getStartDate(), basicPageInfo.getEndDate(), reports);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }
        if (PlacementHourParam.CompareEnum.TRUE.getCode().equals(basicPageInfo.getIsCompare().getValue())) {
            List<AdGroupHourVo> compareList = getAggregateGroupDailyReports(puid, idsList, param, true);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = ReportChartUtil.getGroupWeekReportVos(basicPageInfo.getStartDateCompare(), basicPageInfo.getEndDateCompare(), compareList);
            return paddingWeekCompare(reports, compareList);
        }
        return reports;
    }

    private List<AdGroupHourVo> paddingWeekCompare(List<AdGroupHourVo> reports, List<AdGroupHourVo> compareList) {
        //日周月已经按照时间全部填充，我们只需要一一对应对比就可以了
        if (CollectionUtils.isEmpty(reports)) {
            return reports;
        }
        for (int i = 0; i < reports.size() && i < compareList.size(); i++) {
            reports.get(i).compareDataSet(compareList.get(i));
        }
        return reports;
    }

    @Override
    public List<AdGroupHourVo> getAggregateHourMonthList(int puid, List<String> idsList, GroupAggregateHourVo param, List<AdGroupHourVo> compares) {
        return getDailyWeeklyOrMonth(puid, param, idsList, ReportChartUtil::getGroupMonthReportVos, compares);
    }

    @Override
    public List<AdGroupWeekDayVo> getGroupWeeklySuperpositionList(int puid, GroupHourParam param) {
        //在这里通过pageSign获取idList
        if(StringUtils.isEmpty(param.getPageSign())){
            log.info("placement weekly superposition pageSign is null, param:{}",param);
        }
        List<String> idList = cpcPageIdsHandler.getCampaignIdsTemporary(param.getPuid(), param.getPageSign(),
                "", param.getShopId(), new GroupHourParam[]{param});
        if(CollectionUtils.isEmpty(idList)){
            log.info("placement weekly superposition idList is null, param:{}",param);
        }
        return convertToHourOfWeekDayVos(getGroupWeeklySuperpositionDailyList(puid, idList, param));
    }

    private List<AdGroupWeekDayVo> convertToHourOfWeekDayVos(List<AdGroupHourVo> hourVos) {
        //按周汇总数据
        Map<Integer, List<AdGroupHourVo>> hourVoMap =
                hourVos.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AdGroupHourVo::getWeekDay));

        List<AdGroupWeekDayVo> adGroupWeekDayVos = new ArrayList<>(7);

        for (Map.Entry<Integer, List<AdGroupHourVo>> entry : hourVoMap.entrySet()) {
            AdGroupWeekDayVo adGroupWeekDayVo = new AdGroupWeekDayVo();
            List<AdGroupHourVo> asinHourVos = entry.getValue();
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(asinHourVos, adMetricDto);
            filterMetricData(asinHourVos, adMetricDto);
            adGroupWeekDayVo.setDetails(asinHourVos);
            adGroupWeekDayVo.staticsFromHourVos(asinHourVos);
            adGroupWeekDayVo.setWeekDay(entry.getKey());
            adGroupWeekDayVos.add(adGroupWeekDayVo);
        }
        AdMetricDto adMetricDto1 = new AdMetricDto();
        sumMetricData(adGroupWeekDayVos, adMetricDto1);
        metricData(adGroupWeekDayVos, adMetricDto1);
        return adGroupWeekDayVos.stream().collect(Collectors.toList());
    }

    private void sumMetricData(List<? extends AdGroupWeekDayVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        adMetricDto.setSumCost(voList.stream().filter(item -> item != null && item.getAdCost() != null).map(item -> item.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdSale(voList.stream().filter(item -> item != null && item.getAdSale() != null).map(item -> item.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdGroupHourBaseVo::getAdOrderNum).sum()));
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdGroupHourBaseVo::getAdSaleNum).sum()));
    }

    // 填充指标占比数据
    private void metricData(List<? extends AdGroupWeekDayVo> voList, AdMetricDto adMetricDto) {
        for (AdGroupWeekDayVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                continue;
            }
            metricData(adMetricDto, vo);
        }
    }

    private void metricData(AdMetricDto adMetricDto, AdGroupWeekDayVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdCost().toString(), adMetricDto.getSumCost().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSale().toString(), adMetricDto.getSumAdSale().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getAdSaleNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSaleNum().toString(), adMetricDto.getSumOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }
    }

    private List<AdGroupHourVo> getDailyWeeklyOrMonth(int puid, GroupAggregateHourVo param, List<String> idsList,
                                                      Function<List<AdGroupHourVo>, List<AdGroupHourVo>> handleFuc, List<AdGroupHourVo> compares) {
        List<AdGroupHourVo> reports = getAggregateGroupDailyReports(puid, idsList, param, false);
        reports = handleFuc.apply(reports);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }
        AdPageBasicData basicPageInfo = param.getAdPageBasicData();
        if (PlacementHourParam.CompareEnum.TRUE.getCode().equals(basicPageInfo.getIsCompare().getValue())) {
            List<AdGroupHourVo> compareList = getAggregateGroupDailyReports(puid, idsList, param, true);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = handleFuc.apply(compareList);
            return paddingMonthCompare(basicPageInfo, reports, compareList);
        } else {
            return reports;
        }
    }

    private List<AdGroupHourVo> paddingMonthCompare(AdPageBasicData pageBasic, List<AdGroupHourVo> reports, List<AdGroupHourVo> compareList) {
        LocalDate start = LocalDate.parse(pageBasic.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate end = LocalDate.parse(pageBasic.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate startCompare = LocalDate.parse(pageBasic.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
        List<AdGroupHourVo> resList = new ArrayList<>();
        Map<String, AdGroupHourVo> map = StreamUtil.toMap(reports, AdGroupHourVo::getLabel);
        Map<String, AdGroupHourVo> compareMap = StreamUtil.toMap(compareList, AdGroupHourVo::getLabel);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        for (; !YearMonth.from(start).isAfter(YearMonth.from(end)); start = start.plusMonths(1), startCompare = startCompare.plusMonths(1)) {
            AdGroupHourVo report = map.get(start.format(formatter));
            AdGroupHourVo compareReport = compareMap.get(startCompare.format(formatter));
            if (Objects.isNull(report) && Objects.isNull(compareReport)) {
                continue;
            }
            AdGroupHourVo vo;
            if (Objects.isNull(report)) {
                vo = new AdGroupHourVo();
                vo.setLabel(start.format(formatter));
                vo.setDate(vo.getLabel());
            } else {
                vo = report;
            }
            vo.compareDataSet(compareReport);
            resList.add(vo);
        }
        return resList;
    }

    private GroupHourlyReportResponsePb.GroupHourlyReportResponse multiQueryGroupResult(List<String> idList,
                                                                                                GroupHourlyRequestPb.GroupHourlyRequest.Builder builder) {
        GroupHourlyRequestPb.GroupHourlyRequest.Builder queryBuilder = GroupHourlyRequestPb.GroupHourlyRequest.newBuilder();
        BeanUtils.copyProperties(builder, queryBuilder);
        Optional.ofNullable(builder.getWeekdayList()).map(queryBuilder::addAllWeekday);
        Optional.ofNullable(builder.getCampaignIdList()).map(queryBuilder::addAllCampaignId);
        Optional.ofNullable(idList).filter(CollectionUtils::isNotEmpty).ifPresent(queryBuilder::addAllAdGroupId);
        return adFeedBlockingStub.statisticsGroupHourlyReport(queryBuilder.build());
    }

    private GroupHourlyReportResponsePb.GroupHourlyReportResponse mergeGroupHourResultResp(List<GroupHourlyReportResponsePb.GroupHourlyReportResponse> resultList) {
        GroupHourlyReportResponsePb.GroupHourlyReportResponse.Builder mergeResult = GroupHourlyReportResponsePb.GroupHourlyReportResponse.newBuilder();
        if(CollectionUtils.isEmpty(resultList)) return mergeResult.build();
        List<HourlyReportDataPb.HourlyReportData> result = new ArrayList<>();
        //聚合数据
        Collection<HourlyReportDataPb.HourlyReportData> values = resultList.stream().filter(Objects::nonNull).filter(e -> e.getDataCount() > 0)
                .map(GroupHourlyReportResponsePb.GroupHourlyReportResponse::getDataList).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                    LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                    return localTime.getHour();
                }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport)).values();
        if (CollectionUtils.isNotEmpty(values)) {
            result = com.google.common.collect.Lists.newArrayList(values);
        }
        mergeResult.addAllData(result);
        return mergeResult.build();
    }

    private Map<Integer, HourlyReportDataPb.HourlyReportData> getIntegerHourlyReportDataMap(GroupHourlyReportResponsePb.GroupHourlyReportResponse statisticsByHourResponse, Map<Integer, HourlyReportDataPb.HourlyReportData> hourlyReportDataMap) {
        if (statisticsByHourResponse != null && statisticsByHourResponse.getDataCount() > 0) {
            hourlyReportDataMap = statisticsByHourResponse.getDataList().stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, (e1, e2) -> e2));
        }
        return hourlyReportDataMap;
    }

    private AdGroupHourVo handleVo(Integer hour, HourlyReportDataPb.HourlyReportData data,
                                       HourlyReportDataPb.HourlyReportData dataCompare) {
        AdGroupHourVo adGroupHourVoCompare = convertHourlyReportDataToVo(dataCompare);
        AdGroupHourVo adGroupHourVo = convertHourlyReportDataToVo(data);
        AdGroupHourVo vo = AdGroupHourVo.builder()
                .label(HourConvert.hourMap.get(hour))
                .hour(hour)
                .adCost(adGroupHourVo.getAdCost())
                .impressions(adGroupHourVo.getImpressions())
                .clicks(adGroupHourVo.getClicks())
                .cpa(adGroupHourVo.getCpa())
                .adCostPerClick(adGroupHourVo.getAdCostPerClick())
                .ctr(adGroupHourVo.getCtr())
                .cvr(adGroupHourVo.getCvr())
                .acos(adGroupHourVo.getAcos())
                .roas(adGroupHourVo.getRoas())
                .adOrderNum(adGroupHourVo.getAdOrderNum())
                .selfAdOrderNum(adGroupHourVo.getSelfAdOrderNum())
                .otherAdOrderNum(adGroupHourVo.getOtherAdOrderNum())
                .adSale(adGroupHourVo.getAdSale())
                .adSelfSale(adGroupHourVo.getAdSelfSale())
                .adOtherSale(adGroupHourVo.getAdOtherSale())
                .adSaleNum(adGroupHourVo.getAdSaleNum())
                .adSelfSaleNum(adGroupHourVo.getAdSelfSaleNum())
                .adOtherSaleNum(adGroupHourVo.getAdOtherSaleNum())
                .acots(adGroupHourVo.getAcots())
                .build();
        //对比值
        if (adGroupHourVoCompare != null) {
            vo.setAdCostCompare(adGroupHourVoCompare.getAdCost());
            vo.setImpressionsCompare(adGroupHourVoCompare.getImpressions());
            vo.setClicksCompare(adGroupHourVoCompare.getClicks());
            vo.setCpaCompare(adGroupHourVoCompare.getCpa());
            vo.setAdCostPerClickCompare(adGroupHourVoCompare.getAdCostPerClick());
            vo.setCtrCompare(adGroupHourVoCompare.getCtr());
            vo.setCvrCompare(adGroupHourVoCompare.getCvr());
            vo.setAcosCompare(adGroupHourVoCompare.getAcos());
            vo.setRoasCompare(adGroupHourVoCompare.getRoas());
            vo.setAdOrderNumCompare(adGroupHourVoCompare.getAdOrderNum());
            vo.setSelfAdOrderNumCompare(adGroupHourVoCompare.getSelfAdOrderNum());
            vo.setOtherAdOrderNumCompare(adGroupHourVoCompare.getOtherAdOrderNum());
            vo.setAdSaleCompare(adGroupHourVoCompare.getAdSale());
            vo.setAdSelfSaleCompare(adGroupHourVoCompare.getAdSelfSale());
            vo.setAdOtherSaleCompare(adGroupHourVoCompare.getAdOtherSale());
            vo.setAdSaleNumCompare(adGroupHourVoCompare.getAdSaleNum());
            vo.setAdSelfSaleNumCompare(adGroupHourVoCompare.getAdSelfSaleNum());
            vo.setAdOtherSaleNumCompare(adGroupHourVoCompare.getAdOtherSaleNum());
            vo.setAcotsCompare(adGroupHourVoCompare.getAcots());
            vo.setAsotsCompare(adGroupHourVoCompare.getAsots());
            vo.afterPropertiesSet();
        }
        return vo;
    }

    private AdGroupHourVo convertHourlyReportDataToVo(HourlyReportDataPb.HourlyReportData data) {
        AdGroupHourVo vo = new AdGroupHourVo();
        if (data == null) {
            return vo;
        }
        vo.setWeekDay(data.getWeekday());
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7D());
        vo.setSelfAdOrderNum(data.getAttributedConversions7DSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7D(), data.getAttributedConversions7DSameSku()));
        vo.setAdSale(BigDecimal.valueOf(data.getAttributedSales7D()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(BigDecimal.valueOf(data.getAttributedSales7DSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(BigDecimal.valueOf(data.getAttributedSales7D()), BigDecimal.valueOf(data.getAttributedSales7DSameSku())).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7D());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7DSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7D(), data.getAttributedUnitsOrdered7DSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }

    private void filterSumMetricData(List<AdGroupHourVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        adMetricDto.setSumCost(voList.stream().filter(item -> item != null && item.getAdCost() != null).map(item -> item.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdSale(voList.stream().filter(item -> item != null && item.getAdSale() != null).map(item -> item.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdGroupHourVo::getAdOrderNum).sum()));
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdGroupHourVo::getAdSaleNum).sum()));
    }

    // 填充指标占比数据
    private void filterMetricData(List<AdGroupHourVo> voList, AdMetricDto adMetricDto) {
        for (AdGroupHourVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                continue;
            }
            computeMetricData(adMetricDto, vo);
        }
    }

    private void computeMetricData(AdMetricDto adMetricDto, AdGroupHourVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdCost().toString(), adMetricDto.getSumCost().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSale().toString(), adMetricDto.getSumAdSale().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getAdSaleNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSaleNum().toString(), adMetricDto.getSumOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }
    }

    private List<AdGroupHourVo> getAggregateGroupDailyReports(int puid, List<String> idList, GroupAggregateHourVo param, boolean isCompare) {
        AdPageBasicData pageBasic = param.getAdPageBasicData();
        LocalDate start = Optional.ofNullable(pageBasic).map(AdPageBasicData::getStartDate).filter(StringUtils::isNotBlank).map(LocalDate::parse).orElse(null);
        LocalDate end = Optional.ofNullable(pageBasic).map(AdPageBasicData::getEndDate).filter(StringUtils::isNotBlank).map(LocalDate::parse).orElse(null);
        if (isCompare) {
            start = Optional.ofNullable(pageBasic).map(AdPageBasicData::getStartDateCompare).filter(StringUtils::isNotBlank).map(LocalDate::parse).orElse(null);
            end = Optional.ofNullable(pageBasic).map(AdPageBasicData::getEndDateCompare).filter(StringUtils::isNotBlank).map(LocalDate::parse).orElse(null);
        }
        if (Objects.isNull(start)) {
            return null;
        }
        Integer shopId = Optional.of(pageBasic.getShopId()).map(Int32Value::getValue).orElse(null);
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        MultiThreadQueryParamDto multiParam = MultiThreadQueryParamDto.builder()
                .shopIds(Collections.singletonList(shopId))
                .puid(puid)
                .marketplaceId(Optional.ofNullable(shopAuth).map(ShopAuth::getMarketplaceId).orElse(""))
                .startDate(start)
                .endDate(end)
                .campaignType(Optional.of(pageBasic.getType()).orElse(Constants.SP))
                .build();

        List<List<AdGroupReportHourlyDTO>> multiResult = multiThreadQueryAndMergeUtil.multiThreadQuery(ThreadPoolUtil.getAggregatePlacementSyncPool(),
                () -> idList, multiParam, this::multiQueryPlacement);
        List<AdGroupReportHourlyDTO> reports = mergerDailyQueryGroup(multiResult);
        //日,周,月报告数据
        return reports.stream().map(item -> {
            AdGroupHourVo vo = new AdGroupHourVo();
            vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                    .format(DateTimeFormatter.ISO_LOCAL_DATE));
            vo.setDate(vo.getLabel());
            vo.setAdSale(Optional.ofNullable(item.getTotalSales()).orElse(BigDecimal.ZERO));
            vo.setAdSelfSale(Optional.ofNullable(item.getAdSales()).orElse(BigDecimal.ZERO));
            vo.setAdOtherSale(Optional.ofNullable(item.getAdOtherSales()).orElse(BigDecimal.ZERO));
            vo.setAdOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
            vo.setSelfAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
            //sub other
            vo.setOtherAdOrderNum(MathUtil.subtractInteger(Optional.ofNullable(item.getOrderNum()).orElse(0),
                    Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
            vo.setAdSaleNum(Optional.ofNullable(item.getSaleNum()).orElse(0));
            vo.setAdSelfSaleNum(Optional.ofNullable(item.getAdSaleNum()).orElse(0));
            if (Constants.SD.equalsIgnoreCase(param.getType()) || Constants.SB.equalsIgnoreCase(param.getType())) {
                vo.setAdOtherSaleNum(0);
            } else {
                vo.setAdOtherSaleNum(MathUtil.subtractInteger(vo.getAdSaleNum(), vo.getAdSelfSaleNum()));
            }

            vo.setAdCost(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO));
            vo.setClicks(Long.valueOf(item.getClicks()));
            vo.setImpressions(Long.valueOf(item.getImpressions()));
            vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
            vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
            vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
            vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
            vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));

            vo.setVcpm(MathUtil.divideByThousand(item.getVcpmCost(), Long.parseLong(item.getVcpmImpressions().toString())));
            vo.setVcpmCost(Optional.ofNullable(item.getVcpmCost()).orElse(BigDecimal.ZERO));
            vo.setVcpmImpressions(Optional.ofNullable(item.getVcpmImpressions()).orElse(0));
            vo.setViewableImpressions(Optional.ofNullable(item.getViewableImpressions()).orElse(0));
            // 新加字段
            vo.setOrdersNewToBrand(Optional.ofNullable(item.getOrdersNewToBrand14d()).orElse(0));
            vo.setUnitsOrderedNewToBrand(Optional.ofNullable(item.getUnitsOrderedNewToBrand14d()).orElse(0));
            vo.setSalesNewToBrand(Optional.ofNullable(item.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO));

            vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
            vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));

            vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
            vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getAdOrderNum() - vo.getSelfAdOrderNum())));

            vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(item.getOrdersNewToBrand14d()), BigDecimal.valueOf(100)),BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(item.getUnitsOrderedNewToBrand14d()), BigDecimal.valueOf(100)),BigDecimal.valueOf(vo.getAdSaleNum())));
            vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));


            return vo;
        }).collect(Collectors.toList());
    }

    private List<AdGroupReportHourlyDTO> multiQueryPlacement(List<String> idsList, MultiThreadQueryParamDto dto) {
        //日,周,月报告数据
        //查询广告组报告数据，需要根据类型SP,SB,SD
        List<AdGroupReportHourlyDTO> reports = Collections.emptyList();
        if (Constants.SP.equalsIgnoreCase(dto.getCampaignType())) {
            reports = amazonAdGroupReportDao.getGroupReportByGroupId(dto.getPuid(), dto.getShopIds().get(0),
                    dto.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                    dto.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")), idsList);
        }
        if (Constants.SB.equalsIgnoreCase(dto.getCampaignType())) {
            reports = amazonAdSbGroupReportDao.getSbGroupReportByGroupId(dto.getPuid(), dto.getShopIds().get(0),
                    dto.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                    dto.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")), idsList);
        }
        if (Constants.SD.equalsIgnoreCase(dto.getCampaignType())) {
            reports = amazonAdSdGroupReportDao.getSdGroupReportByGroupId(dto.getPuid(), dto.getShopIds().get(0),
                    dto.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                    dto.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")), idsList);
        }
        return reports;
    }

    private List<AdGroupReportHourlyDTO> mergerDailyQueryGroup(List<List<AdGroupReportHourlyDTO>> reports) {
        List<AdGroupReportHourlyDTO> result = new ArrayList<>();
        //需要按日聚合
        Map<String, List<AdGroupReportHourlyDTO>> resultMap = reports.stream().flatMap(Collection::stream).
                collect(Collectors.groupingBy(AdGroupReportHourlyDTO::getCountDate));
        for(List<AdGroupReportHourlyDTO> dataList : resultMap.values()){
            AdGroupReportHourlyDTO reducePO = new AdGroupReportHourlyDTO();
            if (CollectionUtils.isEmpty(dataList)) continue;
            reducePO.setCostType(dataList.get(0).getCostType());
            reducePO.setShopId(dataList.get(0).getShopId());
            reducePO.setCountDate(dataList.get(0).getCountDate());
            reducePO.setMarketplaceId(dataList.get(0).getMarketplaceId());
            reducePO.setType(dataList.get(0).getType());
            reducePO.setCampaignId(dataList.get(0).getCampaignId());
            reducePO.setCampaignName(dataList.get(0).getCampaignName());
            result.add(dataList.stream().reduce(reducePO, AggregationDataUtil::getAdReportDailyReduce));
        }
        return result;
    }

    private AdGroupHourVo summary(List<AdGroupHourVo> list, BigDecimal shopSales) {
        AdGroupHourVo vo = new AdGroupHourVo();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        for (AdGroupHourVo ad : list) {
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
            vo.setAdCostCompare(MathUtil.add(vo.getAdCostCompare(), ad.getAdCostCompare()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setImpressionsCompare(MathUtil.add(vo.getImpressionsCompare(), ad.getImpressionsCompare()));
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setClicksCompare(MathUtil.add(vo.getClicksCompare(), ad.getClicksCompare()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setAdOrderNumCompare(MathUtil.add(vo.getAdOrderNumCompare(), ad.getAdOrderNumCompare()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setSelfAdOrderNumCompare(MathUtil.add(ad.getSelfAdOrderNumCompare(), vo.getSelfAdOrderNumCompare()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setOtherAdOrderNumCompare(MathUtil.add(ad.getOtherAdOrderNumCompare(), vo.getOtherAdOrderNumCompare()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSaleCompare(MathUtil.add(vo.getAdSaleCompare(), ad.getAdSaleCompare()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdSelfSaleCompare(MathUtil.add(ad.getAdSelfSaleCompare(), vo.getAdSelfSaleCompare()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdOtherSaleCompare(MathUtil.add(ad.getAdOtherSaleCompare(), vo.getAdOtherSaleCompare()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSaleNumCompare(MathUtil.add(vo.getAdSaleNumCompare(), ad.getAdSaleNumCompare()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdSelfSaleNumCompare(MathUtil.add(ad.getAdSelfSaleNumCompare(), vo.getAdSelfSaleNumCompare()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setAdOtherSaleNumCompare(MathUtil.add(ad.getAdOtherSaleNumCompare(), vo.getAdOtherSaleNumCompare()));

            // 新加字段
            vo.setViewableImpressions(MathUtil.add(ad.getViewableImpressions(), vo.getViewableImpressions()));

            vo.setOrdersNewToBrand(MathUtil.add(ad.getOrdersNewToBrand(), vo.getOrdersNewToBrand()));
            vo.setUnitsOrderedNewToBrand(MathUtil.add(ad.getUnitsOrderedNewToBrand(), vo.getUnitsOrderedNewToBrand()));
            vo.setSalesNewToBrand(MathUtil.add(ad.getSalesNewToBrand(), vo.getSalesNewToBrand()));
            vo.setVcpmCost(MathUtil.add(ad.getVcpmCost(), vo.getVcpmCost()));
            vo.setVcpmImpressions(MathUtil.add(ad.getVcpmImpressions(), vo.getVcpmImpressions()));
            vo.setTotalAdSale(MathUtil.add(ad.getTotalAdSale(), vo.getTotalAdSale()));
            vo.setTotalAdSelfSale(MathUtil.add(ad.getTotalAdSelfSale(), vo.getTotalAdSelfSale()));
            vo.setTotalClicks(MathUtil.add(ad.getTotalClicks(), vo.getTotalClicks()));
            vo.setTotalImpressions(MathUtil.add(ad.getTotalImpressions(), vo.getTotalImpressions()));

        }



        vo.setVcpm(MathUtil.divideByThousand(vo.getVcpmCost(), Long.valueOf(Optional.ofNullable(vo.getVcpmImpressions()).orElse(0))));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getTotalAdSale().subtract(vo.getTotalAdSelfSale()), BigDecimal.valueOf(vo.getOtherAdOrderNum())));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)),BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)),BigDecimal.valueOf(vo.getAdSaleNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));

        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 2, RoundingMode.HALF_UP));
        vo.setCpaCompare(vo.getAdOrderNumCompare() == 0 ? BigDecimal.ZERO : vo.getAdCostCompare().divide(BigDecimal.valueOf(vo.getAdOrderNumCompare()), 2, RoundingMode.HALF_UP));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks()), 2));
        vo.setAdCostPerClickCompare(MathUtil.divideByZero(vo.getAdCostCompare(), BigDecimal.valueOf(vo.getClicksCompare()), 2));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions()), 2));
        vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicksCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressionsCompare()), 2));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks()), 2));
        vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNumCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicksCompare()), 2));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 2, RoundingMode.HALF_UP));
        vo.setAcosCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCostCompare().multiply(new BigDecimal("100")).divide(vo.getAdSaleCompare(), 2, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 2, RoundingMode.HALF_UP));
        vo.setRoasCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSaleCompare().divide(vo.getAdCostCompare(), 2, RoundingMode.HALF_UP));
        vo.afterPropertiesSet();//为各对比率属性设值

        vo.setAsots(shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().multiply(new BigDecimal("100")).divide(shopSales, 4, BigDecimal.ROUND_HALF_UP));
        vo.setAcots(shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(shopSales, 4, RoundingMode.HALF_UP));

        vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getTotalImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getTotalClicks(), vo.getViewableImpressions()));
        vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getTotalAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));

        vo.setAdCostPercentage(BigDecimal.valueOf(100).setScale(2));


        vo.setAdSalePercentage(BigDecimal.valueOf(100).setScale(2));


        vo.setAdOrderNumPercentage(BigDecimal.valueOf(100).setScale(2));


        vo.setOrderNumPercentage(BigDecimal.valueOf(100).setScale(2));


        return vo;
    }

    private List<AdGroupHourVo> getGroupWeeklySuperpositionDailyList(int puid, List<String> idList, GroupHourParam param) {
        //校验店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        return amazonAdFeedReportService.listAggregateAdGroupWeekList(shopAuth, idList, param);
//        //获取小时级数据
//        GroupWeeklyRequestPb.GroupWeeklyRequest.Builder builder =
//                GroupWeeklyRequestPb.GroupWeeklyRequest.newBuilder();
//        builder.setSellerId(shopAuth.getSellingPartnerId());
//        builder.setMarketplaceId(shopAuth.getMarketplaceId());
//        builder.setStartDate(param.getStartDate());
//        builder.setEndDate(param.getEndDate());
//
//        //需要分片查询数据,这个地方需要将param中获取的idList进行分片
//        List<GroupWeeklyReportResponsePb.GroupWeeklyReportResponse> multiQueryResult = multiThreadQueryAndMergeUtil.
//                multiThreadQuery(ThreadPoolUtil.getAggregatePlacementSyncPool(),
//                        () -> idList, builder, this::multiQueryGroupWeeklySuperposition);
//        GroupWeeklyReportResponsePb.GroupWeeklyReportResponse response = mergeMultiQueryResult(multiQueryResult);
//
//        //pb对象转vo对象
//        List<HourlyReportDataPb.HourlyReportData> dataList = response.getDataList();
//        List<AdGroupHourVo> voList = dataList.stream().map(this::convertHourlyReportDataToVo).collect(Collectors.toList());
//        //填充无数据的时间段
//        List<Integer> allWeeks = HourConvert.weeKs;
//        List<Integer> weekList = voList.stream().map(AdGroupHourVo::getWeekDay).collect(Collectors.toList());
//        Map<Integer, List<AdGroupHourVo>> voMap =
//                voList.stream().collect(Collectors.groupingBy(AdGroupHourVo::getWeekDay));
//        List<Integer> needFilledWeek = allWeeks.stream().filter(item -> !weekList.contains(item)).collect(Collectors.toList());
//        needFilledWeek.forEach(e -> {
//            List<AdGroupHourVo> adCampaignHourVos = new ArrayList<>();
//            voMap.put(e, adCampaignHourVos);
//        });
//        //所有时间
//        Collection<String> allHours = HourConvert.hourMap.values();
//
//        voMap.forEach((k, v) -> {
//            List<String> hourList = v.stream().map(AdGroupHourVo::getLabel).collect(Collectors.toList());
//            List<String> needFilledHour = allHours.stream()
//                    .filter(item -> !hourList.contains(item)).collect(Collectors.toList());
//            for (String hour : needFilledHour) {
//                AdGroupHourVo vo = new AdGroupHourVo();
//                vo.setLabel(hour);
//                vo.setHour(Integer.valueOf(hour.split("-")[0]));
//                vo.setWeekDay(k);
//                voList.add(vo);
//            }
//        });
//
//        return voList.stream().sorted(Comparator.comparingInt(AdGroupHourVo::getWeekDay)
//                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
//                .collect(Collectors.toList());
//    }
//
//    private GroupWeeklyReportResponsePb.GroupWeeklyReportResponse mergeMultiQueryResult(List<GroupWeeklyReportResponsePb.GroupWeeklyReportResponse> partitionResult) {
//        GroupWeeklyReportResponsePb.GroupWeeklyReportResponse.Builder builder = GroupWeeklyReportResponsePb.GroupWeeklyReportResponse.newBuilder();
//        if(CollectionUtils.isEmpty(partitionResult)) return builder.build();
//        List<HourlyReportDataPb.HourlyReportData> result = new ArrayList<>();
//        //聚合数据
//        Collection<HourlyReportDataPb.HourlyReportData> values = partitionResult.stream().filter(Objects::nonNull).filter(e -> e.getDataCount() > 0)
//                .map(GroupWeeklyReportResponsePb.GroupWeeklyReportResponse::getDataList).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
//                    LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
//                    return localTime.getHour() + "&&" + e1.getWeekday();
//                }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport)).values();
//        if (CollectionUtils.isNotEmpty(values)) {
//            result = com.google.common.collect.Lists.newArrayList(values);
//        }
//        builder.addAllData(result);
//        return builder.build();
    }

    private GroupWeeklyReportResponsePb.GroupWeeklyReportResponse multiQueryGroupWeeklySuperposition(List<String> idList, GroupWeeklyRequestPb.GroupWeeklyRequest.Builder builder) {
        GroupWeeklyRequestPb.GroupWeeklyRequest.Builder queryBuilder = GroupWeeklyRequestPb.GroupWeeklyRequest.newBuilder();
        BeanUtils.copyProperties(builder, queryBuilder);
        Optional.ofNullable(builder.getWeekdayList()).map(queryBuilder::addAllWeekday);
        Optional.ofNullable(builder.getCampaignIdList()).map(queryBuilder::addAllCampaignId);
        queryBuilder.addAllAdGroupId(idList);
        GroupWeeklyReportResponsePb.GroupWeeklyReportResponse response =
                adFeedBlockingStub.statisticsGroupWeeklyReport(queryBuilder.build());
        return response;
    }
}
