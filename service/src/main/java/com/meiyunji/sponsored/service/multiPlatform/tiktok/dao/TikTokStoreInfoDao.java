package com.meiyunji.sponsored.service.multiPlatform.tiktok.dao;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokAdvertiserAccount;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokStoreInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2025-05-19  11:30
 */
@Slf4j
@Component
public class TikTokStoreInfoDao extends AdBaseDaoImpl<TikTokStoreInfo> {

    public Integer batchInsertOrUpDate(Integer puid, List<TikTokStoreInfo> list) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_tiktok_store_info`(`puid`,`shop_id`,`advertiser_id`,`store_id`,")
                .append("`store_authorized_bc_id`,`store_type`,`store_name`,`store_code`,`is_del`,`create_time`,`update_time`) values");
        List<Object> argsList = Lists.newArrayList();
        for (TikTokStoreInfo tikTokStoreInfo : list) {
            sql.append("(?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(tikTokStoreInfo.getShopId());
            argsList.add(tikTokStoreInfo.getAdvertiserId());
            argsList.add(tikTokStoreInfo.getStoreId());
            argsList.add(tikTokStoreInfo.getStoreAuthorizedBcId());
            argsList.add(tikTokStoreInfo.getStoreType());
            argsList.add(tikTokStoreInfo.getStoreName());
            argsList.add(tikTokStoreInfo.getStoreCode());
            argsList.add(tikTokStoreInfo.getIsDel());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update store_authorized_bc_id=values(store_authorized_bc_id),store_type=values(store_type),store_name=values(store_name),store_code=values(store_code),is_del=values(is_del) ");
        return getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }

    public List<TikTokStoreInfo> getList(Integer puid, List<String> advertiserIds, List<Integer> shopIds, List<String> storeIds) {
        StringBuilder sql = new StringBuilder("SELECT * FROM `t_tiktok_store_info` WHERE puid = ?");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        sql.append(SqlStringUtil.dealInList("advertiser_id", advertiserIds, argsList));
        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        sql.append(SqlStringUtil.dealInList("store_id", storeIds, argsList));
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), getRowMapper());
    }

    public List<TikTokStoreInfo> getStoreInfo(Integer puid, List<Integer> shopIds, List<String> advertiserIds) {
        StringBuilder sql = new StringBuilder("SELECT * FROM `t_tiktok_store_info` WHERE puid = ?");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(advertiserIds)) {
            sql.append(SqlStringUtil.dealInList("advertiser_id", advertiserIds, argsList));
        }
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), getRowMapper());
    }

    public List<TikTokStoreInfo> listByAdvertiserIds(Integer puid, List<String> advertiserIds) {
        StringBuilder sql = new StringBuilder("SELECT * FROM `t_tiktok_store_info` WHERE puid = ?");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        sql.append(SqlStringUtil.dealInList("advertiser_id", advertiserIds, argsList));
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), getRowMapper());
    }

    public List<TikTokStoreInfo> listByAdvertiserId(Integer puid, String advertiserId) {
        StringBuilder sql = new StringBuilder("SELECT * FROM `t_tiktok_store_info` WHERE puid = ? and advertiser_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(advertiserId);
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), getRowMapper());
    }

    public List<TikTokStoreInfo> listByStoreIds(Integer puid, String advertiserId, List<String> storeIds) {
        StringBuilder sql = new StringBuilder("SELECT * FROM `t_tiktok_store_info` WHERE puid = ? and advertiser_id =? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(advertiserId);
        sql.append(SqlStringUtil.dealInList("store_id", storeIds, argsList));
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), getRowMapper());
    }

    public TikTokStoreInfo getStore(Integer puid, Integer shopId, String advertiserId) {
        StringBuilder sql = new StringBuilder("SELECT * FROM `t_tiktok_store_info` WHERE puid = ? and shop_id =? and advertiser_id =? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(advertiserId);
        return getJdbcTemplate().queryForObject(sql.toString(), argsList.toArray(), getRowMapper());
    }

    public TikTokStoreInfo getStore(Integer puid, String storeId, String advertiserId) {
        StringBuilder sql = new StringBuilder("SELECT * FROM `t_tiktok_store_info` WHERE puid = ? and store_id =? and advertiser_id =? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(storeId);
        argsList.add(advertiserId);
        return getJdbcTemplate().queryForObject(sql.toString(), argsList.toArray(), getRowMapper());
    }

    public List<TikTokStoreInfo> getStoreInfoToSync(int offset, int limit) {
        String sql = "SELECT * FROM `t_tiktok_store_info` where is_del = 0 order by puid, shop_id, advertiser_id, store_id limit ?,?";
        List<Object> argsList = Lists.newArrayList();
        argsList.add(offset);
        argsList.add(limit);
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), getRowMapper());
    }

    public List<TikTokStoreInfo> listByPuidAndShopId(Integer puid, Integer shopId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid)
                .equalTo("shop_id", shopId);
        return listByCondition(builder.build());
    }

    public void deleteByIds(Integer puid, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder("update ").append(getJdbcHelper().getTable()).append(" set is_del = 1 ")
                .append(" where puid = ? ");
        argsList.add(puid);
        sql.append(SqlStringUtil.dealInList("id", ids, argsList));
        getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }

    public void deleteByShopIdAndAdvertiserId(Integer puid, Integer shopId, String advertiserId) {
        String sql = "update " + this.getJdbcHelper().getTable() + " set is_del = 1 WHERE `puid` = ? AND `shop_id` = ? AND `advertiser_id` = ? ";
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(advertiserId);
        getJdbcTemplate().update(sql, argsList.toArray());
    }
}
