package com.meiyunji.sponsored.service.cpc.service2.sb.impl;

import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.amazon.advertising.sb.entity.targeting.*;
import com.amazon.advertising.sb.mode.targeting.SBExpression;
import com.amazon.advertising.sb.mode.targeting.SBResolvedExpression;
import com.amazon.advertising.sb.mode.targeting.Targeting;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.AmazonResponseUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdTargetingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdTargeting;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;

import com.meiyunji.sponsored.service.cpc.vo.BatchResponseVo;
import com.meiyunji.sponsored.service.cpc.vo.UpdateBatchTargetVo;
import com.meiyunji.sponsored.service.enums.AdSbTargetingStateEnum;
import com.meiyunji.sponsored.service.enums.AllAdStateEnum;
import com.meiyunji.sponsored.service.stream.enums.ManagementStreamResponseStatusEnum;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by lm on 2021/7/30.
 * 对接口二次封装，直接和广告接口交互
 */
@Component
@Slf4j
public class CpcSbTargetApiService {

    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    @Resource
    private IShopAuthService shopAuthService;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    /**
     * 同步所有的投放
     * @param shop：
     */
    public void syncTargets(ShopAuth shop, String campaignId) {
        syncTargets(shop, campaignId, null, null, false);
    }

    public void syncTargets(ShopAuth shop, String campaignId, String groupId, boolean nullResThrowException) {
        syncTargets(shop, campaignId, groupId, null, nullResThrowException);
    }

    public void syncTargets(ShopAuth shop, String campaignId, String groupId, List<AdSbTargetingStateEnum> stateList, boolean nullResThrowException) {
        syncTargets(shop, campaignId, groupId, null, nullResThrowException, false);
    }

    /**
     * 同步所有的投放
     * @param shop：
     */
    public void syncTargets(ShopAuth shop, String campaignId, String groupId, List<AdSbTargetingStateEnum> stateList, boolean nullResThrowException, boolean isProxy) {
        if (shop == null) {
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncSbTargets--配置信息为空");
            return;
        }

        TargetClient client = client = TargetClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        if (isProxy) {
            client = TargetClient.getInstance(true);
        }
        Integer maxResults = 500;
        String nextToken = "";
        List<Filters> filtersList = new ArrayList<>();
        if (StringUtils.isNotBlank(campaignId)) {
            Filters filters = new Filters();
            filters.setFilterType("CAMPAIGN_ID");
            List<String> asinList = Arrays.stream(campaignId.split(",")).distinct().collect(Collectors.toList());
            filters.setValues(asinList);
            filtersList.add(filters);
        }

        if (StringUtils.isNotBlank(groupId)) {
            Filters filters = new Filters();
            filters.setFilterType("AD_GROUP_ID");
            List<String> groupIdList = Arrays.stream(groupId.split(",")).distinct().collect(Collectors.toList());
            filters.setValues(groupIdList);
            filtersList.add(filters);
        }

        List<String> stateFilter;
        if (CollectionUtils.isEmpty(stateList)) {
            stateFilter = Arrays.stream(AdSbTargetingStateEnum.values()).map(AdSbTargetingStateEnum::getStateType).collect(Collectors.toList());
        } else {
            stateFilter = stateList.stream().map(AdSbTargetingStateEnum::getStateType).collect(Collectors.toList());
        }
        Filters filtersState = new Filters();
        filtersState.setFilterType("TARGETING_STATE");
        filtersState.setValues(stateFilter);
        filtersList.add(filtersState);

        ListTargetResponse response;

        do {
            List<Filters> finalFiltersList = filtersList;
            String finalNextToken = nextToken;

            TargetClient finalClient = client;
            response = cpcApiHelper.call(shop, () -> finalClient.getList(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    finalNextToken, maxResults, finalFiltersList));
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SB targeting rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                TargetClient finalClient1 = client;
                response = cpcApiHelper.call(shop, () -> finalClient1.getList(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        finalNextToken, maxResults, finalFiltersList));
                retry++;
            }

            if (AmazonResponseUtil.isError(response) && nullResThrowException) {
                throw new ServiceException("sb syncTargets error");
            }

            if (response == null || response.getResult() == null || CollectionUtils.isEmpty(response.getResult().getTargets())) {
                break;
            }

            // 入库
            List<Targeting> targets = response.getResult().getTargets();

            List<AmazonSbAdTargeting> amazonSbAdTargetings = new ArrayList<>(targets.size());
            AmazonSbAdTargeting amazonSbAdTargeting;
            for (Targeting targeting : targets) {
                amazonSbAdTargeting = turnEntityToPO(targeting);
                if (StringUtils.isNotBlank(amazonSbAdTargeting.getTargetId())) {
                    amazonSbAdTargeting.setPuid(shop.getPuid());
                    amazonSbAdTargeting.setShopId(shop.getId());
                    amazonSbAdTargeting.setMarketplaceId(shop.getMarketplaceId());
                    amazonSbAdTargeting.setProfileId(amazonAdProfile.getProfileId());
                    amazonSbAdTargetings.add(amazonSbAdTargeting);
                }
            }

            if (amazonSbAdTargetings.size() > 0) {
                Map<String, AmazonSbAdTargeting> targetMap = amazonSbAdTargetingDao.listByTargetId(shop.getPuid(), shop.getId(),
                        amazonSbAdTargetings.stream().map(AmazonSbAdTargeting::getTargetId).collect(Collectors.toList()))
                        .stream().collect(Collectors.toMap(AmazonSbAdTargeting::getTargetId, Function.identity()));

                List<AmazonSbAdTargeting> insertList = new ArrayList<>();
                List<AmazonSbAdTargeting> updateList = new ArrayList<>();
                AmazonSbAdTargeting old;

                for (AmazonSbAdTargeting newTarget : amazonSbAdTargetings) {
                    if (targetMap.containsKey(newTarget.getTargetId())) {
                        old = targetMap.get(newTarget.getTargetId());
                        if (StringUtils.isNotBlank(newTarget.getState())) {
                            old.setState(newTarget.getState());
                        }
                        if (StringUtils.isNotBlank(newTarget.getType())) {
                            old.setType(newTarget.getType());
                        }
                        if (StringUtils.isNotBlank(newTarget.getTargetText())) {
                            old.setTargetText(newTarget.getTargetText());
                        }
                        if (newTarget.getBid() != null) {
                            old.setBid(newTarget.getBid());
                        }
                        if (StringUtils.isNotBlank(newTarget.getExpression())) {
                            old.setExpression(newTarget.getExpression());
                        }
                        if (StringUtils.isNotBlank(newTarget.getResolvedExpression())) {
                            old.setResolvedExpression(newTarget.getResolvedExpression());
                        }
                        updateList.add(old);
                    } else {
                        newTarget.setCreateInAmzup(0);
                        insertList.add(newTarget);
                    }
                }

                try {
                    amazonSbAdTargetingDao.batchAdd(shop.getPuid(), insertList);
                    amazonSbAdTargetingDao.batchUpdate(shop.getPuid(), updateList);
                } catch (Exception e) {
                    log.error("syncSbTargeting:", e);
                }
            }

            nextToken =  response.getResult().getNextToken();
        } while (StringUtils.isNotBlank(nextToken));

    }

    public Result create(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonSbAdTargeting> amazonSbAdTargetings) {

        List<Targeting> targetingList = amazonSbAdTargetings.stream().map(e -> {
            Targeting targeting = new Targeting();
            if (StringUtils.isNotBlank(e.getAdGroupId())) {
                targeting.setAdGroupId(Long.valueOf(e.getAdGroupId()));
            }
            if (StringUtils.isNotBlank(e.getCampaignId())) {
                targeting.setCampaignId(Long.valueOf(e.getCampaignId()));
            }
            if (e.getBid() != null) {
                targeting.setBid(e.getBid().doubleValue());
            }
            if (StringUtils.isNotBlank(e.getExpression())) {
                targeting.setExpressions(JSONUtil.jsonToArray(e.getExpression(), SBExpression.class));
            }
            return targeting;
        }).collect(Collectors.toList());

        CreateTargetResponse response = cpcApiHelper.call(shop, () -> TargetClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).create(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), targetingList));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "网络延迟，请稍后重试";
        SbCreateTargetResult result = response.getResult();
        if (result != null) {
            List<CreateTargetSuccessResults> succList = result.getCreateTargetSuccessResults();
            List<CreateTargetErrorResults> failList = result.getCreateTargetErrorResults();

            if (CollectionUtils.isEmpty(succList) && CollectionUtils.isEmpty(failList)) {
                if (StringUtils.isNotBlank(result.getDetails())) {
                    errMsg = response.getResult().getDetails();
                } else if (StringUtils.isNotBlank(response.getResult().getDescription())) {
                    errMsg = response.getResult().getDescription();
                }
                errMsg = AmazonErrorUtils.getError(errMsg);
            } else {
                if (CollectionUtils.isNotEmpty(succList)) {
                    for (CreateTargetSuccessResults results : succList) {
                        Integer index = results.getTargetRequestIndex();
                        amazonSbAdTargetings.get(index).setTargetId(String.valueOf(results.getTargetId()));
                    }
                }
                if (CollectionUtils.isNotEmpty(failList)) {
                    for (CreateTargetErrorResults results : failList) {
                        Integer index = results.getTargetRequestIndex();
                        if (StringUtils.isNotBlank(results.getDetails())) {
                            amazonSbAdTargetings.get(index).setErrMsg("第" + (index+1) +"个:" + AmazonErrorUtils.getError(results.getDetails()));
                        } else {
                            amazonSbAdTargetings.get(index).setErrMsg("第" + (index+1) +"个:" + AmazonErrorUtils.getError(results.getDescription()));
                        }
                    }
                }
                return ResultUtil.success();
            }

        }

        return ResultUtil.returnErr(errMsg);
    }

    public Result update(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<Targeting> targetingList) {

        UpdateTargetResponse response = cpcApiHelper.call(shop, () -> TargetClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), targetingList));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "网络延迟，请稍后重试";
        SbUpdateTargetResult result = response.getResult();
        if (result != null) {
            List<UpdateTargetSuccessResults> succList = result.getUpdateTargetSuccessResultses();
            List<UpdateTargetErrorResults> failList = result.getUpdateTargetErrorResultses();

            if (CollectionUtils.isEmpty(succList) && CollectionUtils.isEmpty(failList)) {
                if (StringUtils.isNotBlank(result.getDetails())) {
                    errMsg = response.getResult().getDetails();
                } else if (StringUtils.isNotBlank(response.getResult().getDescription())) {
                    errMsg = response.getResult().getDescription();
                }
            } else {
                if (CollectionUtils.isNotEmpty(succList)) {
                   return ResultUtil.returnSucc(succList.get(0));
                }
                if (CollectionUtils.isNotEmpty(failList)) {
                    UpdateTargetErrorResults updateTargetErrorResults = failList.get(0);
                    errMsg = StringUtils.isNotBlank(updateTargetErrorResults.getDetails())
                            ? updateTargetErrorResults.getDetails() : updateTargetErrorResults.getDescription();
                }

            }
        }

        return ResultUtil.returnErr(AmazonErrorUtils.getError(errMsg));

    }


    public Result archive(ShopAuth shop, AmazonAdProfile amazonAdProfile, String targetId) {

        ArchiveTargetResponse response = cpcApiHelper.call(shop, () -> TargetClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).archive(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), Long.valueOf(targetId)));

        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getResult() != null && response.getResult().getTargetId() != null) {
            if ("SUCCESS".equalsIgnoreCase(response.getResult().getCode())) {
                return ResultUtil.success();
            }
        }

        //处理返回结果中的错误信息
        String msg = "网络延迟，请稍后重试";
        if (response.getResult() != null) {
            if (StringUtils.isNotBlank(response.getResult().getDetails())) {
                msg = response.getResult().getDetails();
            } else {
                msg = response.getResult().getDescription();
            }
        }
        return ResultUtil.error(AmazonErrorUtils.getError(msg));
    }

    // 把接口返回的dto转换成po
    private AmazonSbAdTargeting turnEntityToPO(Targeting targeting) {
        AmazonSbAdTargeting amazonSbAdTargeting = new AmazonSbAdTargeting();
        if (targeting.getTargetId() != null) {
            amazonSbAdTargeting.setTargetId(targeting.getTargetId().toString());
        }
        if (targeting.getCampaignId() != null) {
            amazonSbAdTargeting.setCampaignId(targeting.getCampaignId().toString());
        }
        if (targeting.getAdGroupId() != null) {
            amazonSbAdTargeting.setAdGroupId(targeting.getAdGroupId().toString());
        }
        if (CollectionUtils.isNotEmpty(targeting.getExpressions())) {
            amazonSbAdTargeting.setExpression(JSONUtil.objectToJson(targeting.getExpressions()));
        }
        if (CollectionUtils.isNotEmpty(targeting.getResolvedExpressions())) {
            amazonSbAdTargeting.setResolvedExpression(JSONUtil.objectToJson(targeting.getResolvedExpressions()));
        }
        if (StringUtils.isNotBlank(targeting.getState())) {
            amazonSbAdTargeting.setState(targeting.getState());
        }
        if (targeting.getBid() != null) {
            amazonSbAdTargeting.setBid(BigDecimal.valueOf(targeting.getBid()));
        }

        // 确定投放的类型
        if (CollectionUtils.isNotEmpty(targeting.getResolvedExpressions())) {
            for (SBResolvedExpression expression : targeting.getResolvedExpressions()) {
                if (ExpressionEnum.asinSameAs.value().equalsIgnoreCase(expression.getType())) {
                    amazonSbAdTargeting.setType(Constants.TARGETING_TYPE_ASIN);
                    amazonSbAdTargeting.setTargetText(expression.getValue());
                    break;
                }
                if (ExpressionEnum.asinCategorySameAs.value().equalsIgnoreCase(expression.getType())) {
                    amazonSbAdTargeting.setType(Constants.TARGETING_TYPE_CATEGORY);
                    amazonSbAdTargeting.setTargetText(expression.getValue());
                    break;
                }
            }
        }


        return amazonSbAdTargeting;
    }

    /**
     * 修改
     *
     * @param amazonAdTargetings:
     * @return :
     */
    public Result<BatchResponseVo<UpdateBatchTargetVo, AmazonSbAdTargeting>> update(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonSbAdTargeting> amazonAdTargetings , String type) {
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return ResultUtil.returnErr("请求参数错误");
        }



        List<Targeting> targetingList = makeTargetings(amazonAdTargetings,type);
        UpdateTargetResponse response = cpcApiHelper.call(shop, () -> TargetClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), targetingList));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }


        Map<String, AmazonSbAdTargeting> amazonAdTargetingMap = amazonAdTargetings.stream().collect(Collectors.toMap(AmazonSbAdTargeting::getTargetId, e -> e));
        BatchResponseVo<UpdateBatchTargetVo,AmazonSbAdTargeting> batchResponseVo = new BatchResponseVo<>();
        List<UpdateBatchTargetVo> errorList = Lists.newArrayList();
        List<AmazonSbAdTargeting> successList = Lists.newArrayList();
        String errMsg = "";
        SbUpdateTargetResult result = response.getResult();
        List<Long> successId = Lists.newArrayList();
        if (result != null) {
            List<UpdateTargetSuccessResults> succList = result.getUpdateTargetSuccessResultses();
            List<UpdateTargetErrorResults> failList = result.getUpdateTargetErrorResultses();

            if (CollectionUtils.isEmpty(succList) && CollectionUtils.isEmpty(failList)) {
                if (StringUtils.isNotBlank(result.getDetails())) {
                    errMsg = response.getResult().getDetails();
                } else if (StringUtils.isNotBlank(response.getResult().getDescription())) {
                    errMsg = response.getResult().getDescription();
                }
                AmazonErrorUtils.getError(errMsg);
            } else {

                if (CollectionUtils.isNotEmpty(succList)) {
                    for (UpdateTargetSuccessResults succ:succList) {
                        AmazonSbAdTargeting amazonAdTargetingSuccess = amazonAdTargetingMap.remove(String.valueOf(succ.getTargetId()));
                        if (amazonAdTargetingSuccess != null) {
                            successList.add(amazonAdTargetingSuccess);
                        }
                        successId.add(amazonAdTargetingSuccess.getId());
                    }

                }
                if (CollectionUtils.isNotEmpty(failList)) {
                    for (UpdateTargetErrorResults fail: failList) {
                        AmazonSbAdTargeting amazonAdTargetingFail = amazonAdTargetingMap.remove(String.valueOf(fail.getTargetId()));
                        if (amazonAdTargetingFail != null) {
                            UpdateBatchTargetVo spKeywordsVoError = new UpdateBatchTargetVo();
                            spKeywordsVoError.setId(amazonAdTargetingFail.getId());
                            spKeywordsVoError.setTargetId(amazonAdTargetingFail.getTargetId());
                            //更新失败数据处理
                            if (org.apache.commons.lang3.StringUtils.isNotBlank(fail.getDescription())) {
                                spKeywordsVoError.setFailReason(AmazonErrorUtils.getError(fail.getDescription()));
                            } else if (StringUtils.isNotBlank(fail.getDetails())) {
                                spKeywordsVoError.setFailReason(AmazonErrorUtils.getError(fail.getDetails()));
                            } else {
                                spKeywordsVoError.setFailReason("更新失败，请稍后重试");
                            }
                            errorList.add(spKeywordsVoError);
                        }

                    }
                }

            }
        }
        //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
        if (MapUtils.isNotEmpty(amazonAdTargetingMap)) {
            String finalErrMsg = errMsg;
            amazonAdTargetingMap.forEach((k, v) -> {
                UpdateBatchTargetVo spKeywordsVoError = new UpdateBatchTargetVo();
                spKeywordsVoError.setId(Long.valueOf(v.getId()));
                spKeywordsVoError.setFailReason(StringUtils.isBlank(finalErrMsg) ? "更新失败，请稍后重试"  : finalErrMsg);
                errorList.add(spKeywordsVoError);
            });
        }


        batchResponseVo.setCountNum(amazonAdTargetings.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);
        return ResultUtil.success(batchResponseVo);


    }

    /**
     * 同步所有的投放
     * @param shop：
     */
    public List<AmazonSbAdTargeting> syncTarget(ShopAuth shop, String campaignId) {
        List<AmazonSbAdTargeting> amazonSbAdTargetings = new ArrayList<>();
        if (shop == null) {
            return amazonSbAdTargetings;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncSbTargets--配置信息为空");
            return amazonSbAdTargetings;
        }

        TargetClient client = TargetClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        Integer maxResults = 500;
        String nextToken = "";
        List<Filters> filtersList = null;
        if (StringUtils.isNotBlank(campaignId)) {
            filtersList = new ArrayList<>();
            Filters filters = new Filters();
            filters.setFilterType("CAMPAIGN_ID");
            List<String> asinList = Arrays.stream(campaignId.split(",")).distinct().collect(Collectors.toList());
            filters.setValues(asinList);
            filtersList.add(filters);
        }

        if (CollectionUtils.isNotEmpty(filtersList)) {
            Filters filtersState = new Filters();
            filtersState.setFilterType("TARGETING_STATE");
            List<String> values = new ArrayList<>();
            values.add("enabled");
            values.add("pending");
            values.add("archived");
            values.add("paused");
            filtersState.setValues(values);
            filtersList.add(filtersState);
        }

        ListTargetResponse response;

        do {
            List<Filters> finalFiltersList = filtersList;
            String finalNextToken = nextToken;

            response = cpcApiHelper.call(shop, () -> client.getList(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    finalNextToken, maxResults, finalFiltersList));
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SB targeting rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = cpcApiHelper.call(shop, () -> client.getList(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        finalNextToken, maxResults, finalFiltersList));
                retry++;
            }
            if (response == null || response.getResult() == null || CollectionUtils.isEmpty(response.getResult().getTargets())) {
                break;
            }

            // 入库
            List<Targeting> targets = response.getResult().getTargets();

            AmazonSbAdTargeting amazonSbAdTargeting;
            for (Targeting targeting : targets) {
                amazonSbAdTargeting = turnEntityToPO(targeting);
                if (StringUtils.isNotBlank(amazonSbAdTargeting.getTargetId())) {
                    amazonSbAdTargeting.setPuid(shop.getPuid());
                    amazonSbAdTargeting.setShopId(shop.getId());
                    amazonSbAdTargeting.setMarketplaceId(shop.getMarketplaceId());
                    amazonSbAdTargeting.setProfileId(amazonAdProfile.getProfileId());
                    amazonSbAdTargetings.add(amazonSbAdTargeting);
                }
            }


            nextToken =  response.getResult().getNextToken();
        } while (StringUtils.isNotBlank(nextToken));

        return amazonSbAdTargetings;
    }

    private List<Targeting> makeTargetings(List<AmazonSbAdTargeting> amazonAdTargetingList, String type) {
        List<Targeting> list = Lists.newArrayListWithCapacity(amazonAdTargetingList.size());

        for (AmazonSbAdTargeting amazonAdTargeting : amazonAdTargetingList) {
            Targeting targetingClause = new Targeting();

            if (StringUtils.isNotBlank(amazonAdTargeting.getTargetId())) {
                targetingClause.setTargetId(Long.valueOf(amazonAdTargeting.getTargetId()));
            }

            if (StringUtils.isNotBlank(amazonAdTargeting.getAdGroupId())) {
                targetingClause.setAdGroupId(Long.valueOf(amazonAdTargeting.getAdGroupId()));
            }

            if(Constants.CPC_SP_TARGET_BATCH_UPDATE_BID.equals(type)){
                targetingClause.setBid(amazonAdTargeting.getBid().doubleValue());
            }
            if(Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
                targetingClause.setState(amazonAdTargeting.getState());
            }
            list.add(targetingClause);
        }
        return list;
    }
}
