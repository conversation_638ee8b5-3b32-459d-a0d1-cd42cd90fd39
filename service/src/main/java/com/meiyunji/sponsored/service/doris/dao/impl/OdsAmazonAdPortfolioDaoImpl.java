package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.adProductRight.service.IAdProductRightService;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolioDorisSumReport;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.cpc.vo.MultiShopPortfolioListParam;
import com.meiyunji.sponsored.service.cpc.vo.PortfolioPageParam;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdPortfolioDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdPortfolio;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.newDashboard.dto.CampaignOrGroupOrPortfolioDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTopDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByRateEnum;
import com.meiyunji.sponsored.service.permission.util.PermissionSqlBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Stream;

/**
 * 广告组合(OdsAmazonAdPortfolioDaoImpl)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:12
 */
@Repository
public class OdsAmazonAdPortfolioDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdPortfolio> implements IOdsAmazonAdPortfolioDao {

    @Resource
    private IAdProductRightService adProductRightService;


    @Override
    public String portfolioQuerySql(Integer puid, List<Integer> shopIdList, List<String> marketplaceIdList, List<String> portfolioIdList, String currency, String startDate, String endDate, List<Object> argsList, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds, List<String> campaignIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum) {
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT a.portfolio_id portfolioId, ");
        sb.append(" ifnull(sum(report.cost * c.rate), 0) cost, ifnull(sum(if(report.`type` = 'sp', report.sales7d, report.sales14d) * c.rate), 0) totalSales, ");
        sb.append(" ifnull(sum(report.impressions), 0) impressions, ifnull(sum(report.clicks), 0) clicks, ");
        sb.append(" ifnull(sum(if (report.`type` = 'sp', report.conversions7d, report.conversions14d)), 0) orderNum, ");
        sb.append(" ifnull(sum(if (report.`type` = 'sp' , report.units_ordered7d, if (report.`type` = 'sb' , report.units_sold14d, report.units_ordered14d))), 0) saleNum, ");
        sb.append(" ifnull(ROUND(ifnull(sum(report.cost * c.rate), 0)/ ifnull(sum(if(report.`type` = 'sp', report.sales7d, report.sales14d) * c.rate), 0), 4), 0) acos, ");
        sb.append(" ifnull(ROUND(ifnull(sum(if(report.`type` = 'sp', report.sales7d, report.sales14d) * c.rate), 0)/ ifnull(sum(report.cost * c.rate), 0), 4), 0) roas, ");
        sb.append(" ifnull(ROUND(ifnull(sum(report.clicks)/ sum(report.impressions), 0), 4), 0) clickRate, ");//点击率
        sb.append(" ifnull(ROUND(ifnull(sum(if (report.`type` = 'sp', report.conversions7d, report.conversions14d))/ sum(report.clicks), 0), 4), 0) conversionRate, ");//转化率
        sb.append(" ifnull(ROUND(ifnull(sum(report.cost * c.rate)/ sum(report.clicks), 0), 4), 0) cpc, ");//cpc
        sb.append(" ifnull(ROUND(ifnull(sum(report.cost * c.rate)/ sum(if (report.`type` = 'sp', report.conversions7d, report.conversions14d)), 0), 4), 0) cpa ");//cpa
        sb.append(" from ods_t_amazon_ad_campaign_all_report report ");
        sb.append(" join (select * from dim_currency_rate where puid = ? and `to` = ? ) c on report.puid = c.puid and DATE_FORMAT(report.count_date, '%Y%m') = c.month ");
        sb.append(" join dim_marketplace_info m on m.marketplace_id = report.marketplace_id and c.`from` = m.currency ");
        sb.append(" join ods_t_amazon_ad_campaign_all a on a.campaign_id = report.campaign_id and a.puid = report.puid and a.shop_id = report.shop_id ");

        sb.append(" where report.puid = ? ");
        argsList.add(puid);
        argsList.add(currency);
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and report.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and report.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }

        sb.append(" and report.is_summary = 1 and a.portfolio_id is not null ");

        if (CollectionUtils.isNotEmpty(portfolioIdList)) {
            sb.append("and a.portfolio_id in ('").append(StringUtils.join(portfolioIdList, "','")).append("') ");
        }

        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', report.marketplace_id, report.count_day)", siteToday, argsList));
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(SqlStringUtil.dealDorisInList("a.portfolio_id", portfolioIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("report.campaign_id", campaignIds, argsList));
        }else {
            PermissionSqlBuilder.addPermissionFilter(sb, argsList , "report.campaign_id" ,true);
        }

        sb.append(" group by a.portfolio_id");
        return sb.toString();
    }

    @Override
    public List<CampaignOrGroupOrPortfolioDto> queryAdPortfolioCharts(Integer puid, List<Integer> shopIdList, List<String> marketplaceIdList, List<String> portfolioIdList, String currency, String startDate, String endDate) {
        List<Object> argsList = Lists.newArrayList();
        String querySql = portfolioQuerySql(puid, shopIdList, marketplaceIdList, portfolioIdList, currency, startDate, endDate, argsList, null, null, null, null, null, null);
        return getJdbcTemplate().query(querySql, new ObjectMapper<>(CampaignOrGroupOrPortfolioDto.class), argsList.toArray());    }

    @Override
    public List<DashboardAdTopDataDto> queryAdPortfolioYoyOrMomTop(String subSqlA, String subSqlB,
                                                                   List<Object> queryParam, DashboardDataFieldEnum dataField,
                                                                   DashboardOrderByRateEnum orderField, String orderBy,
                                                                   Integer limit, Boolean noZero) {
        List<Object> argsList = Lists.newArrayList();
        argsList.addAll(queryParam);
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT  subSqlA.portfolioId as portfolioId, ifnull(subSqlA.cost, 0) as cost, ");
        sb.append(" ifnull(subSqlA.totalSales, 0) as totalSales, ifnull(subSqlA.impressions, 0) as impressions,  ");
        sb.append(" ifnull(subSqlA.clicks, 0) as clicks, ifnull(subSqlA.orderNum, 0) as orderNum, ");
        sb.append(" ifnull(subSqlA.saleNum, 0) as saleNum, ifnull(subSqlA.acos, 0) as acos, ifnull(subSqlA.roas, 0) as roas, ");
        sb.append(" ifnull(subSqlA.clickRate, 0) as clickRate, ifnull(subSqlA.conversionRate, 0) as conversionRate, ifnull(subSqlA.cpc, 0) as cpc, ifnull(subSqlA.cpa, 0) as cpa, ");
        sb.append(" ifnull(subSqlB.cost, 0) as subCost, ");
        sb.append(" ifnull(subSqlB.totalSales, 0) as subTotalSales, ifnull(subSqlB.impressions, 0) as subImpressions,  ");
        sb.append(" ifnull(subSqlB.clicks, 0) as subClicks, ifnull(subSqlB.orderNum, 0) as subOrderNum, ");
        sb.append(" ifnull(subSqlB.saleNum, 0) as subSaleNum, ifnull(subSqlB.acos, 0) as subAcos, ifnull(subSqlB.roas, 0) as subRoas, ");
        sb.append(" ifnull(subSqlB.clickRate, 0) as subClickRate, ifnull(subSqlB.conversionRate, 0) as subConversionRate, ifnull(subSqlB.cpc, 0) as subCpc, ifnull(subSqlB.cpa, 0) as subCpa, ");
        sb.append(" SUM(subSqlA.cost) OVER () as allCost, ");
        sb.append(" SUM(subSqlA.totalSales) OVER () as allTotalSales, ");
        sb.append(" SUM(subSqlA.impressions) OVER () as allImpressions, ");
        sb.append(" SUM(subSqlA.clicks) OVER () as allClicks, ");
        sb.append(" SUM(subSqlA.orderNum) OVER () as allOrderNum, ");
        sb.append(" SUM(subSqlA.saleNum) OVER () as allSaleNum ");
        sb.append(" From ");
        sb.append(" (").append(subSqlA).append(")").append(" subSqlA ");
        sb.append(" left join ");
        sb.append(" (").append(subSqlB).append(")").append(" subSqlB ");
        sb.append(" on subSqlA.portfolioId = subSqlB.portfolioId ");
        if (Boolean.TRUE.equals(noZero)) {
            sb.append(" where " + getColumnSelect(dataField.getCode()) );
        }
        if (Objects.nonNull(orderField) && DashboardOrderByRateEnum.PERCENT == orderField) {
            //以上几个计算占比时，是按绝对值进行排序的
            sb.append(" ORDER BY ").append("ifnull(subSqlA.").append(dataField.getCode()).append(", 0)");
        }  else if (Objects.nonNull(orderField) && Stream.of(DashboardOrderByRateEnum.YOY_VALUE, DashboardOrderByRateEnum.MOM_VALUE)
                .anyMatch(d -> d == orderField)) {//计算增长值
            sb.append(" ORDER BY ").append(" (");
            sb.append("ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" - ").append(" ifnull(subSqlB.").append(dataField.getCode()).append(", 0)").append(") ");
        }else {
            sb.append(" ORDER BY ").append(" (");//计算增长率
            sb.append("if(ifnull(subSqlB.").append(dataField.getCode()).append(", 0)").append(" = 0 ").append(", if(ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" = 0 ").append(", 0").append(", 1)");
            sb.append(", (ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" - ").append(" subSqlB.").append(dataField.getCode()).append(" ) ");
            sb.append(" / ").append(" subSqlB.").append(dataField.getCode()).append(" )");
            sb.append(" )");
        }
        if (StringUtils.isNotEmpty(orderBy)) {
            sb.append(" ").append(orderBy);
        }
        sb.append(", ifnull(subSqlA.").append(dataField.getCode()).append(", 0)");
        if (StringUtils.isNotEmpty(orderBy)) {
            sb.append(" ").append(orderBy);
        }
        if (Objects.nonNull(limit) && limit != 0) {
            sb.append(" LIMIT ").append(limit);
        }
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(DashboardAdTopDataDto.class), argsList.toArray());
    }

    /**
     * 用户排除为0 的字段处理
     */
    private String getColumnSelect(String orderByField) {

        switch (orderByField) {
            case "impressions":
                return " ifnull(subSqlA.impressions, 0) <> 0 ";
            case "clicks":
                return " ifnull(subSqlA.clicks, 0) <> 0 ";
            case "cost":
                return " ifnull(subSqlA.cost, 0) <> 0 ";
            case "roas":
                return " ifnull(subSqlA.roas, 0) <> 0 ";
            case "acos":
                return " ifnull(subSqlA.acos, 0) <> 0 ";
            case "clickRate":
                return " ifnull(subSqlA.clickRate, 0) <> 0 ";
            case "conversionRate":
                return " ifnull(subSqlA.conversionRate, 0.0) <> 0 ";
            case "cpc":
                return " ifnull(subSqlA.cpc, 0) <> 0 ";
            case "cpa":
                return " ifnull(subSqlA.cpa, 0) <> 0 ";
            case "totalSales":
                return " ifnull(subSqlA.totalSales, 0) <> 0 ";
            case "orderNum":
                return " ifnull(subSqlA.orderNum, 0) <> 0 ";
            case "saleNum":
                return " ifnull(subSqlA.saleNum, 0) <> 0 ";
            default:
                return orderByField + " <> 0 ";
        }
    }


    @Override
    public List<Integer> getShopIdsByPortfolioIds(int puid, List<Integer> shopIdList, List<String> portfolioIds) {

        StringBuilder sb = new StringBuilder();
        sb.append(" select shop_id ")
                .append(" from ").append(this.getJdbcHelper().getTable());
        List<Object> argsList = new ArrayList<>();
        sb.append(" where puid = ? ");
        argsList.add(puid);
        sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
        sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
        sb.append(" group by puid, shop_id ");
        return getJdbcTemplate().queryForList(sb.toString(), Integer.class, argsList.toArray());
    }

    @Override
    public Integer countPortfolioList(int puid, List<Integer> shopIdList, List<String> portfolioIds) {
        StringBuilder sb = new StringBuilder();
        sb.append(" select count(*) ")
                .append(" from ").append(this.getJdbcHelper().getTable());
        List<Object> argsList = new ArrayList<>();
        sb.append(" where puid = ? ");
        argsList.add(puid);
        sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
        sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
        return getJdbcTemplate().queryForObject(sb.toString(), Integer.class, argsList.toArray());
    }


    @Override
    public List<String> listPortfolioIdByName(Integer puid, PortfolioPageParam param) {
        StringBuilder sb = new StringBuilder();
        sb.append(" select portfolio_id ")
                .append(" from ods_t_amazon_ad_portfolio ");
        List<Object> argsList = new ArrayList<>();
        sb.append(" where puid = ? ");
        argsList.add(puid);
        sb.append(SqlStringUtil.dealInList(" shop_id", param.getShopIdList(), argsList));
        if (param.getIsHidden() != null) {
            sb.append(" and is_hidden = ? ");
            argsList.add(param.getIsHidden());
        }

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            if ("exact".equalsIgnoreCase(param.getSearchType())) {
                sb.append(" and lower(name) = ? ");
                argsList.add(param.getSearchValue().toLowerCase());
            } else {
                sb.append(" and lower(name) like ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(param.getSearchValue().toLowerCase()) + "%");
            }
        }

        List<String> searchValueList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            for (String value : param.getSearchValueList()) {
                searchValueList.add(value.toLowerCase());
            }
        }

        if (CollectionUtils.isNotEmpty(searchValueList)) {
            sb.append(SqlStringUtil.dealDorisInList("lower(p.name)", searchValueList, argsList));
        }
        String sql = SqlStringUtil.exactSql(sb.toString(), argsList);
        return getJdbcTemplate().queryForList(sb.toString(), String.class, argsList.toArray());
    }

    @Override
    public List<OdsAmazonAdPortfolio> listByPortfolioId(Integer puid, List<Integer> shopIdList, List<String> portfolioIds) {
        List<Object> args = Lists.newArrayList(puid);
        String sql = "select * from ods_t_amazon_ad_portfolio where puid=?";
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql += SqlStringUtil.dealInList("shop_id", shopIdList, args);
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) { //广告组合查询
            sql += SqlStringUtil.dealInList("portfolio_id", portfolioIds, args);
        }
        return getJdbcTemplate().query(sql, args.toArray(), getMapper());
    }

    @Override
    public Page<OdsAmazonAdPortfolio> listMultiplePortfolioPage(Integer puid, PortfolioPageParam param) {
        // 设置时间
        param.setStartDate(DateUtil.getDateSqlFormat(param.getStartDate()));
        param.setEndDate(DateUtil.getDateSqlFormat(param.getEndDate()));

        List<Object> argsList = new ArrayList<>();
        StringBuilder countSql = new StringBuilder("select count(*) from ( select p.portfolio_id portfolio_id ");
        // 增加shop_id 方便缩小范围
        StringBuilder selectSql = new StringBuilder(" select p.portfolio_id portfolio_id, p.shop_id shop_id ");
        boolean bool = StringUtils.isNotBlank(param.getOrderType()) && !"null".equalsIgnoreCase(param.getOrderType()) && StringUtils.isNotBlank(param.getOrderField());
        // 处理是否需要排序字段
        selectSql.append(getSqlField(bool ? param.getOrderField() : null));
        selectSql.append(" from ods_t_amazon_ad_portfolio p ");
        countSql.append(" from ods_t_amazon_ad_portfolio p ");
        String joinSql = leftJoinSql(param, argsList, bool);
        selectSql.append(joinSql);
        countSql.append(joinSql);
        StringBuilder whereSql = buildWhere(param, argsList);
        selectSql.append(whereSql);
        countSql.append(whereSql);
        countSql.append(" ) r");
        selectSql.append(" order by ").append(getOrderField(bool ? param.getOrderField() : null, param.getOrderType())).append(" ");
        Object[] args = argsList.toArray();
        return getPageResult(param.getPageNo(), param.getPageSize(), countSql.toString(), args, selectSql.toString(), args, OdsAmazonAdPortfolio.class);
    }

    @Override
    public Integer getPortfolioCampaignIdNum(Integer puid, PortfolioPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder(" select count(distinct c.campaign_id) from ods_t_amazon_ad_portfolio p join ods_t_amazon_ad_campaign_all c on p.puid = c.puid and p.shop_id = p.shop_id and p.portfolio_id = c.portfolio_id and c.puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("c.shop_id", param.getShopIdList(), argsList));
        StringBuilder whereSql = buildWhere(param, argsList);
        selectSql.append(whereSql);
        return getJdbcTemplate().queryForObject(selectSql.toString(), Integer.class, argsList.toArray());
    }

    @Override
    public AdMetricDto getSumAdMetric(Integer puid, PortfolioPageParam param) {
        // 设置时间
        param.setStartDate(DateUtil.getDateSqlFormat(param.getStartDate()));
        param.setEndDate(DateUtil.getDateSqlFormat(param.getEndDate()));
        List<Object> argsList = new ArrayList<>();
        String selectSql = " select sum(pr.adSaleDoris) adSale, sum(pr.adCostDoris) adCost, sum(pr.adOrderNumDoris) adOrderNum,sum(pr.adSaleNumDoris) adSaleNum from ods_t_amazon_ad_portfolio p " +
                leftJoinSumSql(param, argsList) +
                buildWhere(param, argsList);
        List<AdMetricDto> list = getJdbcTemplate().query(selectSql, (re, i) -> AdMetricDto.builder()
                .sumCost(Optional.ofNullable(re.getBigDecimal("adCost")).orElse(BigDecimal.ZERO))
                .sumAdSale(Optional.ofNullable(re.getBigDecimal("adSale")).orElse(BigDecimal.ZERO))
                .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("adOrderNum")).orElse(BigDecimal.ZERO))
                .sumOrderNum(Optional.ofNullable(re.getBigDecimal("adSaleNum")).orElse(BigDecimal.ZERO))
                .build(), argsList.toArray());
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : new AdMetricDto();
    }

    @Override
    public List<AmazonAdPortfolioDorisSumReport> getAllPortfolioAggregateByDate(Integer puid, PortfolioPageParam param, Boolean rate) {
        param.setStartDate(DateUtil.getDateSqlFormat(param.getStartDate()));
        param.setEndDate(DateUtil.getDateSqlFormat(param.getEndDate()));
        List<Object> argsList = new ArrayList<>();
        String selectSql = " select pr.count_day count_day, sum(pr.adSaleDoris) totalSales, sum(pr.adCostDoris) adCost, sum(pr.adOrderNumDoris) adOrderNum,sum(pr.adSaleNumDoris) adSaleNum, sum(pr.clicksDoris) clicks, sum(pr.impressionsDoris) impressions  from ods_t_amazon_ad_portfolio p " +
                leftJoinAggregateSql(param, argsList, rate) +
                buildWhere(param, argsList) + " group by pr.count_day ";

        return getJdbcTemplate().query(selectSql, (re, i) -> {
            AmazonAdPortfolioDorisSumReport amazonAdPortfolioDorisSumReport = new AmazonAdPortfolioDorisSumReport();
            amazonAdPortfolioDorisSumReport.setCountDate(re.getString("count_day"));
            amazonAdPortfolioDorisSumReport.setCost(Optional.ofNullable(re.getBigDecimal("adCost")).orElse(BigDecimal.ZERO));
            amazonAdPortfolioDorisSumReport.setTotalSales(Optional.ofNullable(re.getBigDecimal("totalSales")).orElse(BigDecimal.ZERO));
            amazonAdPortfolioDorisSumReport.setOrderNum(Optional.of(re.getLong("adOrderNum")).orElse(0L));
            amazonAdPortfolioDorisSumReport.setSaleNum(Optional.of(re.getLong("adSaleNum")).orElse(0L));
            amazonAdPortfolioDorisSumReport.setClicks(Optional.of(re.getLong("clicks")).orElse(0L));
            // 这个地方存在可能超过int最大值 21亿
            amazonAdPortfolioDorisSumReport.setImpressions(Optional.of(re.getLong("impressions")).orElse(0L));
            return amazonAdPortfolioDorisSumReport;
        }, argsList.toArray());
    }

    private String getOrderField(String field, String orderType) {
        if (StringUtils.isBlank(field)) {
            return " createTimeDoris " + getOrderTypeSql(orderType) + ", portfolio_id desc ";
        }
        switch (field) {
            case "adCostPercentage":
                return " adCostDoris " + getOrderTypeSql(orderType) + ", portfolio_id desc ";
            case "adSalePercentage":
                return " adSaleDoris " + getOrderTypeSql(orderType) + ", portfolio_id desc ";
            case "adOrderNumPercentage":
                return " adOrderNumDoris " + getOrderTypeSql(orderType) + ", portfolio_id desc ";
            case "adSaleNumPercentage":
                return " adSaleNumDoris " + getOrderTypeSql(orderType) + ", portfolio_id desc ";
            case "shopName":
            case "adCost":
            case "impressions":
            case "clicks":
            case "adOrderNum":
            case "ctr":
            case "cvr":
            case "adSaleNum":
            case "adSale":
            case "acos":
            case "roas":
            case "adCostPerClick":
            case "cpa":
            case "acots":
            case "asots":
            case "advertisingUnitPrice":
                return field + "Doris " + getOrderTypeSql(orderType) + ", portfolio_id desc ";
            case "portfolioName":
                return " name " + getOrderTypeSql(orderType) + ", portfolio_id desc ";
            default:
                return " createTimeDoris " + getOrderTypeSql(orderType) + ", portfolio_id desc ";
        }
    }

    /**
     * 获取排序的sql
     */
    private String getOrderTypeSql(String orderType) {
        if (StringUtils.isBlank(orderType) || OrderTypeEnum.desc.name().equalsIgnoreCase(orderType)) {
            return " desc ";
        }
        return "";
    }

    private StringBuilder buildWhere(PortfolioPageParam param, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" where p.puid = ? ");
        argsList.add(param.getPuid());
        // 过滤店铺Id
        whereSql.append(SqlStringUtil.dealInList("p.shop_id", param.getShopIdList(), argsList));
        if (param.getIsHidden() != null) {
            whereSql.append(" and p.is_hidden = ? ");
            argsList.add(param.getIsHidden());
        }

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            if (StringUtils.isNotBlank(param.getSearchType()) && SearchTypeEnum.EXACT.getValue().equals(param.getSearchType())) {
                whereSql.append(" and lower(p.name) = ? ");
                argsList.add(param.getSearchValue().toLowerCase());
            } else {
                whereSql.append(" and lower(p.name) like ? ");
                argsList.add("%" + param.getSearchValue().toLowerCase() + "%");
            }
        }

        List<String> searchValueList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            for (String value : param.getSearchValueList()) {
                searchValueList.add(value.toLowerCase());
            }
        }

        if (CollectionUtils.isNotEmpty(searchValueList)) {
            whereSql.append(SqlStringUtil.dealDorisInList("lower(p.name)", searchValueList, argsList));
        }

        String portfolioRightSqlFromGrpc = adProductRightService.getPortfolioRightSqlFromGrpc(param.getPuid(), "p.shop_id", "p.portfolio_id", param.getShopIdList(), CampaignTypeEnum.campaignTypeEnumList, argsList);

        if (StringUtils.isNotBlank(portfolioRightSqlFromGrpc)) {
            whereSql.append(" and ").append(portfolioRightSqlFromGrpc);
        }

        if (CollectionUtils.isNotEmpty(param.getPortfolioIdList())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("p.portfolio_id", param.getPortfolioIdList(), argsList));
        }
        return whereSql;
    }

    private String leftJoinSql(PortfolioPageParam param, List<Object> argsList, boolean bool) {
        if (!bool) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        // 店铺名称 需要连店铺表
        if ("shopName".equals(param.getOrderField())) {
            stringBuilder.append(" left join dim_t_shop_auth sp on p.puid = sp.puid and p.shop_id = sp.shop_id and sp.puid = ? ");
            argsList.add(param.getPuid());
            stringBuilder.append(SqlStringUtil.dealInList("sp.shop_id", param.getShopIdList(), argsList));
            return stringBuilder.toString();
        }

        stringBuilder.append(" left join ( select  ac.portfolio_id portfolio_id, any(ac.puid) puid, any(ac.shop_id) shop_id ");
        stringBuilder.append(getReportSqlField(param.getOrderField()));
        stringBuilder.append(" from ods_t_amazon_ad_campaign_all ac join ods_t_amazon_ad_campaign_all_report acr on ac.puid = acr.puid and ac.shop_id = acr.shop_id and ac.campaign_id = acr.campaign_id and acr.puid = ?  and acr.count_day >= ? and acr.count_day <= ? and acr.is_summary = 1  ");
        argsList.add(param.getPuid());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        // 过滤店铺Id
        stringBuilder.append(SqlStringUtil.dealInList("acr.shop_id", param.getShopIdList(), argsList));


        stringBuilder.append(" where ac.puid = ? ");
        argsList.add(param.getPuid());
        stringBuilder.append(SqlStringUtil.dealInList("ac.shop_id", param.getShopIdList(), argsList));
        String productRightCampaignIdsSqlFromGrpc = adProductRightService.getProductRightCampaignIdsSqlFromGrpc(param.getPuid(), param.getShopIdList(), CampaignTypeEnum.campaignTypeEnumList, argsList, "ac.campaign_id");
        if (StringUtils.isNotEmpty(productRightCampaignIdsSqlFromGrpc)) {
            stringBuilder.append(" and ").append(productRightCampaignIdsSqlFromGrpc);
        }
        if (CollectionUtils.isNotEmpty(param.getPortfolioIdList())) {
            stringBuilder.append(SqlStringUtil.dealBitMapDorisInList("ac.portfolio_id", param.getPortfolioIdList(), argsList));
        } else {
            stringBuilder.append(" and (ac.portfolio_id is not null or ac.portfolio_id != '' ) ");
        }

        // 里面
        stringBuilder.append(" group by ac.portfolio_id ) pr on p.puid = pr.puid and p.shop_id = pr.shop_id and p.portfolio_id = pr.portfolio_id ");

        // 需要店铺销售额
        if ("asots".equals(param.getOrderField()) || "acots".equals(param.getOrderField())) {
            stringBuilder.append(" left join ( select ifnull(sum(sale_price), 0) salePrice, any(puid) puid, shop_id  from dws_sale_profit_shop_day where puid = ? and now_date >= ? and now_date <= ? ");
            argsList.add(param.getPuid());
            argsList.add(param.getStartDate());
            argsList.add(param.getEndDate());
            stringBuilder.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), argsList));
            stringBuilder.append(" group by shop_id ) sps on p.puid = sps.puid and p.shop_id = sps.shop_id ");
        }
        return stringBuilder.toString();
    }

    private String leftJoinSumSql(PortfolioPageParam param, List<Object> argsList) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" join ( select  ac.portfolio_id portfolio_id, any(ac.puid) puid, any(ac.shop_id) shop_id , IFNULL(sum(if (acr.type = 'sp' ,acr.`units_ordered7d`,if (acr.type = 'sb' ,acr.`units_sold14d`,acr.`units_ordered14d`))),0) adSaleNumDoris, IFNULL(sum(if (acr.type = 'sp' , acr.conversions7d, acr.conversions14d)), 0) adOrderNumDoris, IFNULL(sum(acr.cost) , 0) adCostDoris , IFNULL(sum(if (acr.type = 'sp', acr.sales7d , acr.sales14d )),0) adSaleDoris ");
        stringBuilder.append(" from ods_t_amazon_ad_campaign_all ac join ods_t_amazon_ad_campaign_all_report acr on ac.puid = acr.puid and ac.shop_id = acr.shop_id and ac.campaign_id = acr.campaign_id and acr.puid = ?  and acr.count_day >= ? and acr.count_day <= ? and acr.is_summary = 1  ");
        argsList.add(param.getPuid());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        // 过滤店铺Id
        stringBuilder.append(SqlStringUtil.dealInList("acr.shop_id", param.getShopIdList(), argsList));
        stringBuilder.append(" where ac.puid = ? ");
        argsList.add(param.getPuid());
        stringBuilder.append(SqlStringUtil.dealInList("ac.shop_id", param.getShopIdList(), argsList));

        if (CollectionUtils.isNotEmpty(param.getPortfolioIdList())) {
            stringBuilder.append(SqlStringUtil.dealBitMapDorisInList("ac.portfolio_id", param.getPortfolioIdList(), argsList));
        } else {
            stringBuilder.append(" and (ac.portfolio_id is not null or ac.portfolio_id != '' ) ");
        }
        stringBuilder.append(" group by ac.portfolio_id ) pr on p.puid = pr.puid and p.shop_id = pr.shop_id and p.portfolio_id = pr.portfolio_id ");
        return stringBuilder.toString();
    }

    /**
     * 无汇率表
     */
    private String leftJoinAggregateSql(PortfolioPageParam param, List<Object> argsList, boolean rate) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" join ( select  ac.portfolio_id portfolio_id,acr.count_day count_day , any(ac.puid) puid, any(ac.shop_id) shop_id , IFNULL(sum(if (acr.type = 'sp' ,acr.`units_ordered7d`,if (acr.type = 'sb' ,acr.`units_sold14d`,acr.`units_ordered14d`))),0) adSaleNumDoris, IFNULL(sum(if (acr.type = 'sp' , acr.conversions7d, acr.conversions14d)), 0) adOrderNumDoris, IFNULL(sum(acr.impressions),0) impressionsDoris , IFNULL(sum(acr.clicks),0) clicksDoris, ");
        if (rate) {
            stringBuilder.append(" IFNULL(sum(acr.cost * dcr.rate ) , 0) adCostDoris , IFNULL(sum(if (acr.type = 'sp', acr.sales7d * dcr.rate , acr.sales14d * dcr.rate) ),0) adSaleDoris ");
        } else {
            stringBuilder.append(" IFNULL(sum(acr.cost) , 0) adCostDoris , IFNULL(sum(if (acr.type = 'sp', acr.sales7d, acr.sales14d)),0) adSaleDoris ");
        }
        stringBuilder.append(" from ods_t_amazon_ad_campaign_all ac join ods_t_amazon_ad_campaign_all_report acr on ac.puid = acr.puid and ac.shop_id = acr.shop_id and ac.campaign_id = acr.campaign_id and acr.puid = ?  and acr.count_day >= ? and acr.count_day <= ? and acr.is_summary = 1  ");
        argsList.add(param.getPuid());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        // 过滤店铺Id
        stringBuilder.append(SqlStringUtil.dealInList("acr.shop_id", param.getShopIdList(), argsList));
        if (rate) {
            stringBuilder.append(" left join (select * from dim_currency_rate where puid = ? and `to` = 'USD' ) dcr on acr.puid = dcr.puid and acr.currency = dcr.`from` and acr.count_month = dcr.month ");
            argsList.add(param.getPuid());
        }
        stringBuilder.append(" where ac.puid = ? ");
        argsList.add(param.getPuid());
        stringBuilder.append(SqlStringUtil.dealInList("ac.shop_id", param.getShopIdList(), argsList));

        if (CollectionUtils.isNotEmpty(param.getPortfolioIdList())) {
            stringBuilder.append(SqlStringUtil.dealBitMapDorisInList("ac.portfolio_id", param.getPortfolioIdList(), argsList));
        } else {
            stringBuilder.append(" and (ac.portfolio_id is not null or ac.portfolio_id != '' ) ");
        }
        stringBuilder.append(" group by ac.portfolio_id,acr.count_day ) pr on p.puid = pr.puid and p.shop_id = pr.shop_id and p.portfolio_id = pr.portfolio_id ");
        return stringBuilder.toString();
    }


    private String getReportSqlField(String field) {
        if (StringUtils.isBlank(field)) {
            return " , IFNULL(acr.cost , 0) adCostDoris ";
        }
        // 主表 po
        // 广告活动 ac
        // 报告表 acr
        // 店铺表 sp
        // 店铺销售额表 sps
        switch (field) {
            case "impressions":
                return " , IFNULL(sum(acr.impressions),0) impressionsDoris  ";
            case "clicks":
                return " , IFNULL(sum(acr.clicks),0) clicksDoris  ";
            case "adOrderNum":
            case "adOrderNumPercentage":
                return " , IFNULL(sum(if (acr.type = 'sp' , acr.conversions7d, acr.conversions14d)), 0) adOrderNumDoris ";
            case "ctr":
                return " , ifnull(sum(acr.`clicks`)/sum(acr.`impressions`),0)  ctrDoris ";
            case "cvr":
                return " , ifnull(sum(if (acr.type = 'sp' , acr.conversions7d, acr.conversions14d))/sum(`clicks`),0) cvrDoris ";
            case "adSaleNum":
            case "adSaleNumPercentage":
                return " , IFNULL(sum(if (acr.type = 'sp' ,acr.`units_ordered7d`,if (acr.type = 'sb' ,acr.`units_sold14d`,acr.`units_ordered14d`))),0) adSaleNumDoris ";
            case "asots":
            case "adSale":
            case "adSalePercentage":
                return " , IFNULL(sum(if (acr.type = 'sp', acr.sales7d, acr.sales14d)), 0)  adSaleDoris ";
            case "acos":
                return " , ifnull(sum(acr.cost)/sum(if (acr.type = 'sp', acr.sales7d, acr.sales14d)),0) acosDoris ";
            case "roas":
                return " , ifnull(sum(if (acr.type = 'sp', acr.sales7d, acr.sales14d))/sum(acr.cost),0) roasDoris ";
            case "adCostPerClick":
                return " , ifnull(sum(acr.cost)/sum(acr.`clicks`),0)  adCostPerClickDoris ";
            case "cpa":
                return " , ifnull(sum(acr.cost)/sum(if (acr.type = 'sp' , acr.conversions7d,acr.conversions14d)),0)  cpaDoris ";
            case "advertisingUnitPrice":
                return " , ifnull(sum(if (acr.type = 'sp', acr.sales7d, acr.sales14d)) / sum(if (acr.type = 'sp' , acr.conversions7d, acr.conversions14d)), 0)  advertisingUnitPriceDoris ";
            default:
                return " , IFNULL(sum(acr.cost) , 0) adCostDoris ";
        }
    }

    private String getSqlField(String field) {
        if (StringUtils.isBlank(field)) {
            return " , p.create_time  createTimeDoris ";
        }
        // 主表 po

        // 广告活动 ac
        // 报告表 acr
        // 上两张表 合并未一张表
        // 广告组合报告表 pr

        // 店铺表 sp
        // 店铺销售额表 sps
        String dorisField = field + "Doris";
        switch (field) {
            case "adCostPercentage":
                return " , pr.adCostDoris adCostDoris ";
            case "adSalePercentage":
                return " , pr.adSaleDoris adSaleDoris ";
            case "adOrderNumPercentage":
                return " , pr.adOrderNumDoris adOrderNumDoris ";
            case "adSaleNumPercentage":
                return " , pr.adSaleNumDoris adSaleNumDoris ";
            case "shopName":
                return " , sp.name shopNameDoris ";
            case "adCost":
            case "impressions":
            case "clicks":
            case "adOrderNum":
            case "ctr":
            case "cvr":
            case "adSaleNum":
            case "adSale":
            case "acos":
            case "roas":
            case "adCostPerClick":
            case "advertisingUnitPrice":
            case "cpa":
                return " , pr." + dorisField + " " + dorisField + " ";
            case "acots":
                return " , IFNULL( pr.adCostDoris / sps.salePrice ,0)  acotsDoris ";
            case "asots":
                return " , IFNULL( pr.adSaleDoris / sps.salePrice ,0)  asotsDoris ";
            case "portfolioName":
                return " , name portfolioName ";
            default:
                return " , p.create_time  createTimeDoris ";
        }
    }


    @Override
    public Page<OdsAmazonAdPortfolio> getMultiShopPortfolioList(Integer puid, MultiShopPortfolioListParam param) {
        StringBuilder selectSql = new StringBuilder("select * from `ods_t_amazon_ad_portfolio` ");
        StringBuilder countSql = new StringBuilder("select count(*) FROM `ods_t_amazon_ad_portfolio` ");
        StringBuilder whereSql = new StringBuilder("where puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        whereSql.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), args));

        if (CollectionUtils.isNotEmpty(param.getMarketplaceId())) {
            whereSql.append(SqlStringUtil.dealInList("marketplace_id", param.getMarketplaceId(), args));
        }

        if (CollectionUtils.isNotEmpty(param.getPortfolioIdList())) {
            whereSql.append(SqlStringUtil.dealInList("portfolio_id", param.getPortfolioIdList(), args));
        }

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            if ("exact".equals(param.getSearchType())) {
                whereSql.append(" and lower(name) = ? ");
                args.add(param.getSearchValue().trim().toLowerCase());
            }else {
                whereSql.append(" and lower(name) like ? ");
                args.add("%" + param.getSearchValue().trim().toLowerCase() + "%");
            }
        }

        List<String> searchValueList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            for (String value : param.getSearchValueList()) {
                searchValueList.add(value.toLowerCase());
            }
        }
        if (CollectionUtils.isNotEmpty(searchValueList)) {
            whereSql.append(SqlStringUtil.dealInList("lower(name)", searchValueList, args));
        }

        String portfolioRightSqlFromGrpc = adProductRightService.getPortfolioRightSqlFromGrpc(puid, "shop_id", "portfolio_id", param.getShopIdList(), CampaignTypeEnum.campaignTypeEnumList, args);
        if (StringUtils.isNotBlank(portfolioRightSqlFromGrpc)) {
            whereSql.append(" and ").append(portfolioRightSqlFromGrpc);
        }


        selectSql.append(whereSql);
        countSql.append(whereSql);
        if("left".equals(param.getPosition())){
            selectSql.append(" order by shop_id asc ,`rank` asc, last_updated_date desc");
        }else{
            selectSql.append(" order by last_updated_date desc");
        }
        Object[] argsArr = args.toArray();
        return this.getPageResult(param.getPageNo(), param.getPageSize(), countSql.toString(), argsArr, selectSql.toString(), argsArr, OdsAmazonAdPortfolio.class);
    }


    @Override
    public List<String> queryProductRightSqlGetPortfolioIds(List<Object> args, String sql) {
        return getJdbcTemplate().queryForList(sql, args.toArray(), String.class);
    }

}
