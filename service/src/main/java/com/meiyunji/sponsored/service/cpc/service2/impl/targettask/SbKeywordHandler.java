package com.meiyunji.sponsored.service.cpc.service2.impl.targettask;

import com.amazon.advertising.sb.entity.keyword.CreateKeywordResponse;
import com.amazon.advertising.sb.entity.keyword.KeywordClient;
import com.amazon.advertising.sb.entity.keyword.SbKeywordResult;
import com.amazon.advertising.sb.entity.theme.CreateThemesResponse;
import com.amazon.advertising.sb.entity.theme.SBThemesErrorResult;
import com.amazon.advertising.sb.entity.theme.SBThemesResult;
import com.amazon.advertising.sb.entity.theme.ThemesClient;
import com.amazon.advertising.sb.mode.keyword.Keyword;
import com.amazon.advertising.sb.mode.theme.Theme;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskConstant;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskStatusEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdKeywordDao;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbGroupService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbKeywordService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbKeywordApiService;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-05-08 19:22
 */
@Service(AdTargetTaskConstant.SB_KEYWORD_HANDLER)
@Slf4j
public class SbKeywordHandler implements TargetTaskHandler {

    @Autowired
    private TargetTaskComponent targetTaskComponent;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private CpcSbKeywordApiService cpcSbKeywordApiService;
    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private ICpcSbKeywordService cpcSbKeywordService;
    @Autowired
    private ICpcSbGroupService cpcSbGroupService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;

    @Override
    public void handle(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        //处理业务返回结果
        addKeywords(adTargetTask, adTargetTaskDetails);
    }

    private boolean check(List<AdTargetTaskDetail> needUpdateDetailList, List<AdTargetTaskDetail> adTargetTaskDetails, AdTargetTask adTargetTask, AmazonAdProfile profile, ShopAuth shop){
        if (CollectionUtils.isEmpty(adTargetTaskDetails)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return false;
        }
        if (shop == null) {
            adTargetTaskDetails.forEach(each -> {
                each.setFailureReason("店铺不存在");
                each.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                needUpdateDetailList.add(each);
            });
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return false;
        }
        if (profile == null) {
            adTargetTaskDetails.forEach(each -> {
                each.setFailureReason("没有站点对应的配置信息");
                each.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                needUpdateDetailList.add(each);
            });
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return false;
        }
        return true;
    }

    public void addKeywords(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        Integer puid = adTargetTask.getPuid();
        Integer shopId = adTargetTask.getShopId();
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);

        List<AdTargetTaskDetail> needUpdateDetailList = new ArrayList<>();
       if (!check(needUpdateDetailList, adTargetTaskDetails, adTargetTask, profile, shop)) {
           return;
       }
        int originalTaskDetailSize = adTargetTaskDetails.size();

        // 排除已存在的关键词
        Set<String> adGroupIdSet = new HashSet<>();
        Set<String> keywordTextSet = new HashSet<>();
        Set<String> matchTypeSet = new HashSet<>();
        Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap = new HashMap<>();
        for (AdTargetTaskDetail targetTaskDetail : adTargetTaskDetails) {
            adGroupIdSet.add(targetTaskDetail.getAdGroupId());
            keywordTextSet.add(targetTaskDetail.getTargetObject().trim());
            matchTypeSet.add(targetTaskDetail.getMatchType());
            adTargetTaskDetailMap.put(targetTaskDetail.getId(), targetTaskDetail);
        }
        List<AmazonSbAdKeyword> repeatedAmazonAdKeywords = amazonSbAdKeywordDao.getListByTargetTaskCondition(puid, shopId, adGroupIdSet, keywordTextSet, matchTypeSet);
        Map<String, AmazonSbAdKeyword> amazonAdKeywordMap = repeatedAmazonAdKeywords.stream()
                .collect(Collectors.toMap(each -> String.join("-", each.getAdGroupId(), each.getKeywordText(), each.getMatchType()), Function.identity(), (newVal, oldVal) -> newVal));
        Iterator<AdTargetTaskDetail> it = adTargetTaskDetails.iterator();
        AdTargetTaskDetail next;
        while (it.hasNext()) {
            next = it.next();
            if (amazonAdKeywordMap.containsKey(String.join("-", next.getAdGroupId(), next.getTargetObject().trim(), next.getMatchType()))) {
                if (next.getStatus() <= AdTargetTaskStatusEnum.FAILURE.getCode()) {
                    next.setFailureReason("历史已存在相同的投放，请检查");
                    next.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                    needUpdateDetailList.add(next);
                }
                it.remove();
            }
        }
        if (CollectionUtils.isEmpty(adTargetTaskDetails)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        //构建关键词列表
        List<AmazonSbAdGroup> amazonAdGroups = amazonSbAdGroupDao.getAdGroupByIds(puid, shopId, new ArrayList<>(adGroupIdSet));
        Map<String, AmazonSbAdGroup> amazonAdGroupMap = amazonAdGroups.stream().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, Function.identity(), (newVal, oldVal) -> newVal));
        List<AmazonSbAdKeyword> amazonAdKeywords = convertAddKeywordsVoToPo(adTargetTask.getUid(), amazonAdGroupMap, adTargetTaskDetails, needUpdateDetailList);
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        int failureNum = 0;
        List<List<AmazonSbAdKeyword>> amazonAdKeywordPartition = Lists.partition(amazonAdKeywords, AdTargetTaskConstant.MAX_SB_TARGET_SIZE);
        for (List<AmazonSbAdKeyword> amazonAdKeywordList : amazonAdKeywordPartition) {
            Result result = createKeywordsAndTheme(amazonAdKeywordList, adTargetTaskDetailMap, shop, profile);
            List<AdManageOperationLog> operationLogs = Lists.newArrayList();
            for (AmazonSbAdKeyword amazonSbAdKeyword : amazonAdKeywordList) {
                AdManageOperationLog operationLog = adManageOperationLogService.getSbKeywordLog(null, amazonSbAdKeyword);
                operationLog.setIp(adTargetTask.getLoginIp());
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonSbAdKeyword.getTargetTaskDetailId());
                needUpdateDetailList.add(adTargetTaskDetail);
                if (StringUtils.isNotBlank(amazonSbAdKeyword.getKeywordId())) {
                    operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    operationLog.setResultInfo(adTargetTaskDetail.getFailureReason());
                }
                operationLogs.add(operationLog);
                if (AdTargetTaskStatusEnum.FAILURE.getCode() == adTargetTaskDetail.getStatus()) {
                    failureNum++;
                }
            }
            //更新任务状态
            targetTaskComponent.updateTaskDetailStatus(adTargetTask, needUpdateDetailList);
            needUpdateDetailList.clear();
            //记录日志
            adManageOperationLogService.batchLogsMergeByAdGroup(operationLogs);
            if (result.success()) {
                List<AmazonSbAdKeyword> succList = amazonAdKeywordList.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(succList)) {
                    // 有可能已经添加过了
                    List<String> existInDB = amazonSbAdKeywordDao.listByKeywordId(puid, shopId, succList.stream()
                                    .map(AmazonSbAdKeyword::getKeywordId).collect(Collectors.toList()))
                            .stream()
                            .map(AmazonSbAdKeyword::getKeywordId).collect(Collectors.toList());

                    // 排除掉已有的
                    if (CollectionUtils.isNotEmpty(existInDB)) {
                        succList = succList.stream().filter(e -> !existInDB.contains(e.getKeywordId())).collect(Collectors.toList());
                    }
                    // 入库
                    try {
                        amazonSbAdKeywordDao.batchAdd(puid, succList);
                    } catch (Exception e) {
                        log.error("createSbKeyword:", e);
                    }

                    //创建成功, 需要在同步 获取关键词状态
                    cpcSbKeywordApiService.syncKeywordsIds(shop, succList.stream().map(AmazonSbAdKeyword::getKeywordId).collect(Collectors.joining(",")));
                    //写入doris
                    cpcSbKeywordService.saveDoris(puid, shopId, succList.stream().map(AmazonSbAdKeyword::getKeywordId).collect(Collectors.toList()));
                    //同步广告组投放类型字段
                    try {
                        List<AmazonSbAdGroup> sbAdGroups = succList.stream().map(AmazonSbAdKeyword::getAdGroupId).distinct().map(amazonAdGroupMap::get)
                                .filter(each -> StringUtils.isBlank(each.getAdGroupType())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(sbAdGroups)) {
                            sbAdGroups.forEach(each -> each.setAdGroupType("keyword"));
                            amazonSbAdGroupDao.batchUpdateAdGroupType(puid, sbAdGroups);
                            cpcSbGroupService.saveDoris(null, sbAdGroups);
                        }
                    } catch (Exception e) {
                        log.error("updateGroupTargetType:", e);
                    }
                }
            }
        }

        failureNum += originalTaskDetailSize - amazonAdKeywords.size();
        int adTargetTaskStatus = targetTaskComponent.getAdTargetTaskStatus(originalTaskDetailSize, failureNum);
        targetTaskComponent.updateTaskStatus(adTargetTask, adTargetTaskStatus);
    }

    private Result createKeywordsAndTheme(List<AmazonSbAdKeyword> amazonAdKeywords, Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap, ShopAuth shop, AmazonAdProfile amazonAdProfile) {

        List<AmazonSbAdKeyword> resultThemeList = amazonAdKeywords.stream().filter(k -> "theme".equalsIgnoreCase(k.getMatchType())).collect(Collectors.toList());
        List<AmazonSbAdKeyword> resultKeywordList = amazonAdKeywords.stream().filter(k -> !"theme".equalsIgnoreCase(k.getMatchType())).collect(Collectors.toList());
        Result keywords = createKeywords(resultKeywordList, adTargetTaskDetailMap, shop, amazonAdProfile);
        Result themes = createThemes(resultThemeList, adTargetTaskDetailMap, shop, amazonAdProfile);
        Map<String, AmazonSbAdKeyword> amazonAdKeywordMap = resultThemeList.stream()
            .collect(Collectors.toMap(each -> String.join("-", each.getAdGroupId(), each.getKeywordText(), each.getMatchType()), Function.identity(), (newVal, oldVal) -> newVal));
        Map<String, AmazonSbAdKeyword> amazonAdThemeMap = resultKeywordList.stream()
            .collect(Collectors.toMap(each -> String.join("-", each.getAdGroupId(), each.getKeywordText(), each.getMatchType()), Function.identity(), (newVal, oldVal) -> newVal));
        Map<String, AmazonSbAdKeyword> mergedMap = new HashMap<>();
        mergedMap.putAll(amazonAdKeywordMap); // 将第一个 Map 的内容放入新 Map
        mergedMap.putAll(amazonAdThemeMap); // 将第二个 Map 的内容放入新 Map
        for (AmazonSbAdKeyword each: amazonAdKeywords) {
            AmazonSbAdKeyword keyword = mergedMap.get(String.join("-", each.getAdGroupId(), each.getKeywordText(), each.getMatchType()));
            if (keyword != null) {
                each.setKeywordId(keyword.getKeywordId());
                each.setErrMsg(keyword.getErrMsg());
            }
        }
        if (keywords.success() || themes.success()) {
            return ResultUtil.success();
        }else {
            return ResultUtil.error("网络延迟，请稍后重试");
        }
    }


    private Result createKeywords(List<AmazonSbAdKeyword> amazonAdKeywords, Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap, ShopAuth shop, AmazonAdProfile amazonAdProfile) {
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return ResultUtil.success();
        }
        List<Keyword> keywordList = cpcSbKeywordApiService.makeKeywords(amazonAdKeywords);

        CreateKeywordResponse response = cpcApiHelper.call(shop, () -> KeywordClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).create(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
            shop.getMarketplaceId(), keywordList));

        if (response == null) {
            for (AmazonSbAdKeyword amazonAdKeyword : amazonAdKeywords) {
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
                adTargetTaskDetail.setFailureReason("网络延迟，请稍后重试");
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
            }
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "网络延迟，请稍后重试";
        String formatErrMsg = errMsg;
        if (CollectionUtils.isNotEmpty(response.getResultList())) {
            List<SbKeywordResult> resultList = response.getResultList();
            int index = 0;
            for (SbKeywordResult result : resultList) {
                if ("SUCCESS".equals(result.getCode())) {
                    AmazonSbAdKeyword amazonAdKeyword = amazonAdKeywords.get(index);
                    amazonAdKeyword.setKeywordId(String.valueOf(result.getKeywordId()));
                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.SUCCESS.getCode());
                } else {
                    AmazonSbAdKeyword amazonAdKeyword = amazonAdKeywords.get(index);
                    String returnError = StringUtils.isNotBlank(result.getDetails()) ? result.getDetails() : result.getDescription();
                    String error = AmazonErrorUtils.getKeywordTargetError(returnError, amazonAdKeyword.getKeywordText());
                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
                    adTargetTaskDetail.setFailureReason(targetTaskComponent.getError(error, returnError));
                    adTargetTaskDetail.setFailureReasonDetail(returnError);
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                    amazonAdKeyword.setErrMsg("第" + (index + 1) + "个:" + error);
                }
                index++;
            }
            return ResultUtil.success();
        } else if (response.getError() != null) {
            String returnErrMsg ;
            if ("403".equals(response.getError().getCode())) {
                returnErrMsg = "店铺没有SB广告权限，请到Amazon后台开通SB广告管理";
            } else if (org.apache.commons.lang.StringUtils.isNotBlank(response.getError().getDetails())) {
                returnErrMsg = response.getError().getDetails();
            } else {
                returnErrMsg = response.getError().getDescription();
            }
            errMsg = AmazonErrorUtils.getError(returnErrMsg);
            formatErrMsg = targetTaskComponent.getError(errMsg, returnErrMsg);
        }
        for (AmazonSbAdKeyword amazonAdKeyword : amazonAdKeywords) {
            AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
            adTargetTaskDetail.setFailureReason(formatErrMsg);
            adTargetTaskDetail.setFailureReasonDetail(errMsg);
            adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
        }
        return ResultUtil.error(errMsg);
    }

    private Result createThemes(List<AmazonSbAdKeyword> amazonAdKeywords, Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap, ShopAuth shop, AmazonAdProfile amazonAdProfile) {
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return ResultUtil.success();
        }
        List<Theme> keywordList = makeThemes(amazonAdKeywords);

        CreateThemesResponse response = cpcApiHelper.call(shop, () -> ThemesClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).create(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
            shop.getMarketplaceId(), keywordList));

        if (response == null) {
            for (AmazonSbAdKeyword amazonAdKeyword : amazonAdKeywords) {
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
                adTargetTaskDetail.setFailureReason("网络延迟，请稍后重试");
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
            }
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        List<SBThemesResult> success = response.getSuccess();
        for (SBThemesResult sbThemeResult: success) {
            AmazonSbAdKeyword amazonAdKeyword = amazonAdKeywords.get(sbThemeResult.getIndex());
            amazonAdKeyword.setKeywordId(sbThemeResult.getThemeId());
            AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
            adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.SUCCESS.getCode());
        }
        List<SBThemesErrorResult> errorResults = response.getError();
        for (SBThemesErrorResult sbThemesResult: errorResults) {
            AmazonSbAdKeyword amazonAdKeyword = amazonAdKeywords.get(sbThemesResult.getIndex());
            String error = AmazonErrorUtils.getKeywordTargetError(sbThemesResult.getDetails(), amazonAdKeyword.getKeywordText());
            AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
            adTargetTaskDetail.setFailureReason(error);
            adTargetTaskDetail.setFailureReasonDetail(error);
            adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
            amazonAdKeyword.setErrMsg("第" + (sbThemesResult.getIndex() + 1) + "个:" + error);
        }
        return ResultUtil.success();
    }

    private List<Theme> makeThemes(List<AmazonSbAdKeyword> amazonAdKeywords) {
        List<Theme> list = Lists.newArrayListWithCapacity(amazonAdKeywords.size());
        Theme theme;
        for (AmazonSbAdKeyword sbAdKeyword : amazonAdKeywords) {
            theme = new Theme();
            if (org.apache.commons.lang.StringUtils.isNotBlank(sbAdKeyword.getAdGroupId())) {
                theme.setAdGroupId(sbAdKeyword.getAdGroupId());
            }
            if (org.apache.commons.lang.StringUtils.isNotBlank(sbAdKeyword.getCampaignId())) {
                theme.setCampaignId(sbAdKeyword.getCampaignId());
            }
            theme.setThemeType(sbAdKeyword.getKeywordText());
            if (sbAdKeyword.getBid() != null) {
                theme.setBid(sbAdKeyword.getBid().doubleValue());
            }
            list.add(theme);
        }
        return list;
    }

    private List<AmazonSbAdKeyword> convertAddKeywordsVoToPo(Integer uid, Map<String, AmazonSbAdGroup> amazonAdGroupMap, List<AdTargetTaskDetail> adTargetTaskDetails, List<AdTargetTaskDetail> needUpdateDetailList) {
        List<AmazonSbAdKeyword> keywordList = new ArrayList<>(adTargetTaskDetails.size());
        AmazonSbAdKeyword amazonAdKeyword;
        Iterator<AdTargetTaskDetail> it = adTargetTaskDetails.iterator();
        AdTargetTaskDetail adTargetTaskDetail;
        while (it.hasNext()) {
            adTargetTaskDetail = it.next();
            AmazonSbAdGroup amazonAdGroup = amazonAdGroupMap.get(adTargetTaskDetail.getAdGroupId());
            if (amazonAdGroup == null) {
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                adTargetTaskDetail.setFailureReason("广告组不存在");
                needUpdateDetailList.add(adTargetTaskDetail);
                it.remove();
            } else {
                amazonAdKeyword = new AmazonSbAdKeyword();
                amazonAdKeyword.setPuid(amazonAdGroup.getPuid());
                amazonAdKeyword.setShopId(amazonAdGroup.getShopId());
                amazonAdKeyword.setMarketplaceId(amazonAdGroup.getMarketplaceId());
                amazonAdKeyword.setProfileId(amazonAdGroup.getProfileId());
                amazonAdKeyword.setAdGroupId(amazonAdGroup.getAdGroupId());
                amazonAdKeyword.setCampaignId(amazonAdGroup.getCampaignId());
                amazonAdKeyword.setKeywordText(adTargetTaskDetail.getTargetObject().trim());
                amazonAdKeyword.setMatchType(adTargetTaskDetail.getMatchType());
                amazonAdKeyword.setBid(adTargetTaskDetail.getBid());

                if (adTargetTaskDetail.getSuggested() != null) {
                    amazonAdKeyword.setSuggested(adTargetTaskDetail.getSuggested());
                }
                if (adTargetTaskDetail.getRangeStart() != null) {
                    amazonAdKeyword.setRangeStart(adTargetTaskDetail.getRangeStart());
                }
                if (adTargetTaskDetail.getRangeEnd() != null) {
                    amazonAdKeyword.setRangeEnd(adTargetTaskDetail.getRangeEnd());
                }
                amazonAdKeyword.setCreateId(uid);
                amazonAdKeyword.setCreateInAmzup(1);
                amazonAdKeyword.setTargetTaskDetailId(adTargetTaskDetail.getId());
                keywordList.add(amazonAdKeyword);
            }
        }

        return keywordList;
    }
}
