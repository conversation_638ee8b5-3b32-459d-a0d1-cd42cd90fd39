package com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class GmvMaxCampaignListParam {

    private Integer shopId;

    private List<String> advertiserId;
    private List<String> primaryStatus;
    private String campaignName;
    private String searchType;
//    @NotBlank(message = "开始时间不能为空")
//    private String startTime;
//    private String endTime;
    @NotEmpty(message = "时间范围不能为空")
    private List<String> dateRange;
    private String orderField; // 排序的字段
    private String orderValue; // 升降序
    private Integer pageNo;
    private Integer pageSize;

}
