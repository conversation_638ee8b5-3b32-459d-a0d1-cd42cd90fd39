package com.meiyunji.sponsored.service.multiple.common.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 活动报告指标数据
 *
 * @author: zzh
 * @create: 2024-11-21 10:05
 */
@Data
public class CommonReport {

    /**
     * 曝光量:广告被展示的次数
     */
    private Long impressions;

    /**
     * 点击量:广告被点击的次数
     */
    private Long clicks;

    /**
     * 点击率（CTR）:广告展示时被买家点击的比率。该值由点击量除以曝光量计算得出
     */
    private String ctr;

    /**
     * 订单转化率:此值由总广告订单数除以点击量计算得出
     */
    private String cvr;

    /**
     * ACoS是在指定时间范围内，广告花费与广告销售额的百分比，此值由广告花费除以广告销售额计算得出
     */
    private String acos;

    /**
     * ROAS=广告销售额/广告费
     */
    private String roas;

    /**
     * ACoTS是在指定时间范围内，广告总花费与总销售额的百分比，此值由广告总花费除以总销售额计算得出。
     */
    private String acots;

    /**
     * ASoTS是在指定时间范围内，广告销售额与原价销售额的百分比，此值由广告销售额除以原价销售额计算得出。
     */
    private String asots;

    /**
     * 广告订单数:是指通过点击广告售出产品的订单数量
     */
    private Long adOrderNum;

    /**
     * 广告花费:广告活动的广告总花费
     */
    private String adCost;

    /**
     * 平均点击费：广告的每次点击费用
     */
    private String adCostPerClick;

    /**
     * 广告销售额:广告销售额是指通过广告售出产品的销售额
     */
    private String adSale;

    /**
     * 广告花费占比:单个广告组合花费/查询结果广告组合花费之和*100%
     */
    private String adCostPercentage;

    /**
     * 广告销售额占比:单个广告组合销售额/查询结果广告组合销售额之和*100%
     */
    private String adSalePercentage;

    /**
     * 广告订单量占比:单个广告组合订单数/查询结果广告组合订单数之和*100%
     */
    private String adOrderNumPercentage;

    /**
     * 广告销量占比:单个广告组合广告销量/查询结果广告组合广告销量之和*100%
     */
    private String orderNumPercentage;

    /**
     * 每笔订单花费：每笔订单花费=广告花费/广告订单量
     */
    private String cpa;

    /**
     * 本广告产品订单量:开通广告的商品在对应广告活动中产生的订单量量
     */
    private Long adSaleNum;

    /**
     * 其他产品广告订单量:广告订单量 – 本广告产品订单量
     */
    private Long adOtherOrderNum;

    /**
     * 本广告产品销售额:开通广告的商品在对应广告活动中产生的销售额
     */
    private String adSales;

    /**
     * 其他产品广告销售额:广告销售额 – 本广告产品销售额
     */
    private String adOtherSales;

    /**
     * 广告销量:7天内购买的广告商品及库存中其他商品的销量总额
     */
    private Long orderNum;

    /**
     * 本广告产品销量:开通广告的商品在对应广告活动中产生的销量
     */
    private Long adSelfSaleNum;

    /**
     * 其他产品广告销量:广告销量 – 本广告产品销量
     */
    private Long adOtherSaleNum;

    /**
     * 每千次展现费用=广告花费/可见展现次数x1000，仅针对于VCPM类型广告
     */
    private String vcpm;

    /**
     * “品牌新买家”订单量指1年内首次购买品牌产品的订单量
     */
    private Long ordersNewToBrandFTD;

    /**
     * “品牌新买家”订单百分比=“品牌新买家”订单量/广告订单量
     */
    private String orderRateNewToBrandFTD;

    /**
     * “品牌新买家”销售额:“品牌新买家”订单产生的广告销售额
     */
    private String salesNewToBrandFTD;

    /**
     * “品牌新买家”销售额占比=“品牌新买家”销售额/广告销售额
     */
    private String salesRateNewToBrandFTD;

    /**
     * “品牌新买家”销量:“品牌新买家”订单产生的广告销量
     */
    private Long unitsOrderedNewToBrandFTD;

    /**
     * “品牌新买家”销量百分比:“品牌新买家”销量/广告销量
     */
    private String unitsOrderedRateNewToBrandFTD;

    /**
     * 可见广告展示总数:至少50％的广告应在顾客的可视区中展示至少1秒钟，归为一次可见展示曝光
     */
    private Long viewImpressions;

    /**
     * “品牌新买家”订单率=“品牌新买家”订单量/广告点击量
     */
    private String ordersNewToBrandPercentageFTD;

    /**
     * 搜索词首页首位曝光量区间,最小值~最大值
     */
    private String topImpressionShare;

    /**
     * “品牌新买家”观看量
     */
    private String newToBrandDetailPageViews;

    /**
     * 加购次数
     */
    private String addToCart;

    /**
     * 加购率
     */
    private String addToCartRate;

    /**
     * 单次加购花费
     */
    @JsonProperty("eCPAddToCart")
    private String eCPAddToCart;

    /**
     * 5秒观看次数
     */
    private String video5SecondViews;

    /**
     * 5秒观看率
     */
    private String video5SecondViewRate;

    /**
     * 视频播至1/4次数
     */
    private String videoFirstQuartileViews;

    /**
     * 视频播至1/2次数
     */
    private String videoMidpointViews;

    /**
     * 视频播至3/4次数
     */
    private String videoThirdQuartileViews;

    /**
     * 视频完整播放次数
     */
    private String videoCompleteViews;

    /**
     * 视频取消静音
     */
    private String videoUnmutes;

    /**
     * 观看率
     */
    private String viewabilityRate;

    /**
     * 观看点击率
     */
    private String viewClickThroughRate;

    /**
     * 品牌搜索次数
     */
    private String brandedSearches;

    /**
     * DPV
     */
    private String detailPageViews;

    /**
     * 累计触达用户
     */
    private String cumulativeReach;

    /**
     * 平均触达次数
     */
    private String impressionsFrequencyAverage;

    /**
     * 广告笔单价,广告销售额/广告订单量*100%
     */
    private String advertisingUnitPrice;
}
