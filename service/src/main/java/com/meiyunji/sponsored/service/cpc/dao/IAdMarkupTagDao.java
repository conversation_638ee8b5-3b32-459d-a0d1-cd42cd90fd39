package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.po.AdMarkupTag;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.cpc.vo.AdMarkupTagVo;

import java.util.List;

/**
 * AmazonAdCampaign
 * <AUTHOR>
 */
public interface IAdMarkupTagDao extends IBaseShardingDao<AdMarkupTag> {


    void insertList(List<AdMarkupTag> list);

    List<String> getRelationIds(Integer puid, Integer shopId, String type, Long tagId, String adType,String targetType);

    List<String> getRelationIds(Integer puid, Integer shopId, String type, List<Long> tagIds, String adType,String targetType);

    List<String> getMultipleRelationIds(Integer puid, List<Integer> shopIdIdList, String type, List<Long> tagIds, String adType,String targetType);

    int deleteByTagId(Integer puid, Long tagId);


    int deleteByRelationId(Integer puid, Integer shopId, String type, String adType, String targetType, List<String> relationId, Long adTagId);

    List<AdMarkupTagVo> getRelationVos(Integer puid, Integer shopId, String type, String adType, String targetType, Long tagId, List<String> relationIds);

    List<AdMarkupTagVo> getRelationVosByShopIdList(Integer puid, List<Integer> shopIdList, String type, String adType, String targetType, Long tagId, List<String> relationIds);

    List<AdMarkupTagVo> getRelationVosByShopIdList(Integer puid, List<Integer> shopIdList, String type, List<String> adTypeList, String targetType, Long tagId, List<String> relationIds);

    List<Long> getAdTagId(Integer puid,  String type, List<String> relationIds);

    List<String> getCampaignIds(Integer puid, Integer shopId, String type, List<Long> tagIds, List<String> campaignIdList);

    List<AdMarkupTag> listAllByPuidAndType(Integer puid, String type);

}