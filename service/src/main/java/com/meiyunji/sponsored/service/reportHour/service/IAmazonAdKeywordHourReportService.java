package com.meiyunji.sponsored.service.reportHour.service;

import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.vo.KeywordHourParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.KeywordViewHourParam;
import com.meiyunji.sponsored.service.reportHour.vo.AdKeywordTargetHourOfAdVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdKeywordTargetHourOfPlacementVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdKeywordAndTargetHourVo;

import java.util.List;

public interface IAmazonAdKeywordHourReportService {

    /**
     * 查询关键词小时级数据
     *
     * @param puid
     * @param param
     * @return
     */
    List<AdKeywordAndTargetHourVo> getHourList(int puid, KeywordHourParam param);

    /**
     * 查询竞价关键词小时级数据
     *
     * @param puid
     * @param param
     * @return
     */
    List<AdKeywordAndTargetHourVo> getBidHourList(int puid, KeywordHourParam param);

    /**
     * 查询关键词月数据
     *
     * @param puid
     * @param adType
     * @param param
     * @return
     */
    List<AdKeywordAndTargetHourVo> getMonthlyList(int puid, String adType, KeywordHourParam param, List<AdKeywordAndTargetHourVo> compares);

    /**
     * 查询关键词周数据
     *
     * @param puid
     * @param adType
     * @param param
     * @return
     */
    List<AdKeywordAndTargetHourVo> getWeeklyList(int puid, String adType, KeywordHourParam param, List<AdKeywordAndTargetHourVo> compares);

    /**
     * 查询关键词天数据
     *
     * @param puid
     * @param adType
     * @param param
     * @return
     */
    List<AdKeywordAndTargetHourVo> getDailyList(int puid, String adType, KeywordHourParam param, List<AdKeywordAndTargetHourVo> compares);

    /**
     * 查询产品维度关键词小时级数据
     *
     * @param puid
     * @param param
     * @return
     */
    List<AdKeywordTargetHourOfAdVo> getListOfAd(int puid, KeywordHourParam param);

    /**
     * 查询产品维度关键词小时级明细数据
     *
     * @param puid
     * @param param
     * @return
     */
    List<AdKeywordAndTargetHourVo> getDetailListOfAd(int puid, KeywordHourParam param);

    /**
     * 查询广告位维度关键词小时级数据
     *
     * @param puid
     * @param param
     * @return
     */
    List<AdKeywordTargetHourOfPlacementVo> getListOfPlacement(int puid, KeywordHourParam param);

    /**
     * 查询广告位维度关键词小时级明细数据
     *
     * @param puid
     * @param param
     * @return
     */
    List<AdKeywordAndTargetHourVo> getDetailListOfPlacement(int puid, KeywordHourParam param);

    List<AdKeywordAndTargetHourVo> getHourListAll(int puid, List<ShopAuth> shopAuths, KeywordHourParam param);

    List<AdKeywordAndTargetHourVo> getDailyListAll(int puid, List<ShopAuth> shopAuths, String adType, KeywordHourParam param, List<AdKeywordAndTargetHourVo> compares);

    List<AdKeywordAndTargetHourVo> getWeeklyAllList(int puid, List<ShopAuth> shopAuths, String adType, KeywordHourParam param, List<AdKeywordAndTargetHourVo> compares);

    List<AdKeywordAndTargetHourVo> getMonthlyAllList(int puid, List<ShopAuth> shopAuths, String adType, KeywordHourParam param, List<AdKeywordAndTargetHourVo> compares);

    List<AdKeywordTargetHourOfPlacementVo> getListOfPlacementAll(int puid, List<ShopAuth> shopAuths, KeywordHourParam param);

    List<AdKeywordAndTargetHourVo> getDetailListOfPlacementAll(int puid, List<ShopAuth> shopAuths, KeywordHourParam param);

    List<AdKeywordAndTargetHourVo> getViewHourListAll(int puid, KeywordViewHourParam param);

    List<AdKeywordAndTargetHourVo> getViewHourListAllType(int puid, KeywordViewHourParam param);

    List<AdKeywordAndTargetHourVo> getSbHourList(int puid, KeywordHourParam param);

    List<AdKeywordAndTargetHourVo> getSbHourListAll(int puid, List<ShopAuth> shopAuths, KeywordHourParam param);

    List<AdKeywordTargetHourOfPlacementVo> getViewHourListAllTypeOfPlacement(KeywordViewHourParam param);

    List<AdKeywordTargetHourOfAdVo> getViewHourListAllTypeOfAd(KeywordViewHourParam param);

}
