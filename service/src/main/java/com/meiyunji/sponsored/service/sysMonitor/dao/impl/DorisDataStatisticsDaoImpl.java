package com.meiyunji.sponsored.service.sysMonitor.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.autoRule.dao.IAdAutoRuleTemplateDisabledDao;
import com.meiyunji.sponsored.service.autoRule.po.AdAutoRuleTemplateDisabled;
import com.meiyunji.sponsored.service.sysMonitor.dao.IDorisDataStatisticsDao;
import com.meiyunji.sponsored.service.sysMonitor.po.DorisDataStatistics;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;


/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2024-01-22  10:52
 */

@Repository
public class DorisDataStatisticsDaoImpl extends AdBaseDaoImpl<DorisDataStatistics> implements IDorisDataStatisticsDao {

    @Override
    public void insert(List<DorisDataStatistics> list) {
        StringBuilder sql = new StringBuilder("insert into `t_doris_data_statistics` ");
        sql.append("(`table_schema`, `table_name`, `table_comment`, `table_rows`, `avg_row_length`, `data_length`, ");
        sql.append("`data_length_gb`, `data_length_tb`, `stat_date`, `create_time`, `update_time`) values ");
        List<Object> argsList = Lists.newArrayList();
        for (DorisDataStatistics e : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?),");
            argsList.add(e.getTableSchema());
            argsList.add(e.getTableName());
            argsList.add(e.getTableComment());
            argsList.add(e.getTableRows());
            argsList.add(e.getAvgRowLength());
            argsList.add(e.getDataLength());
            argsList.add(e.getDataLengthGb());
            argsList.add(e.getDataLengthTb());
            argsList.add(e.getStatDate());
            argsList.add(e.getCreateTime());
            argsList.add(e.getUpdateTime());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update update_time = now(3)");
        getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }

}
