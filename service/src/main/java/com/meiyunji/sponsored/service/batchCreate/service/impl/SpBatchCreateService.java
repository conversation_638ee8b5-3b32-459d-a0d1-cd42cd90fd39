package com.meiyunji.sponsored.service.batchCreate.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.google.api.client.util.Lists;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.batchCreate.adStructure.AdStructure;
import com.meiyunji.sponsored.service.batchCreate.dao.*;
import com.meiyunji.sponsored.service.batchCreate.dto.group.GroupInfoInTaskDTO;
import com.meiyunji.sponsored.service.batchCreate.dto.submit.BaseInfoNekeywordDto;
import com.meiyunji.sponsored.service.batchCreate.dto.submit.BaseInfoNetargetingDto;
import com.meiyunji.sponsored.service.batchCreate.dto.submit.BaseInfoProductDto;
import com.meiyunji.sponsored.service.batchCreate.enums.AdStructureEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.KeywordAndTargetingEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateTaskStatusEnum;
import com.meiyunji.sponsored.service.batchCreate.po.*;
import com.meiyunji.sponsored.service.batchCreate.service.ISpBatchCreateService;
import com.meiyunji.sponsored.service.batchCreate.task.SaveBatchDataTask;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdPortfolioDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolio;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.TargetingEnum;
import com.meiyunji.sponsored.service.util.ZoneUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-11-13  11:23
 */

@Service
@Slf4j
public class SpBatchCreateService implements ISpBatchCreateService {


    @Autowired
    private Map<String, AdStructure> adStructureMap;

    @Autowired
    private IAmazonAdBatchSequenceDao batchSequenceDao;

    @Autowired
    private IAmazonAdBatchTaskDao amazonAdBatchTaskDao;

    @Autowired
    private SaveBatchDataTask saveBatchDataTask;

    @Autowired
    private IAmazonAdBatchBaseInfoDao amazonAdBatchBaseInfoDao;

    @Autowired
    private IAmazonAdBatchNekeywordDao amazonAdBatchNekeywordDao;

    @Autowired
    private IAmazonAdBatchNetargetingDao amazonAdBatchNetargetingDao;

    @Autowired
    private IAmazonAdBatchTargetingDao amazonAdBatchTargetingDao;

    @Autowired
    private IAmazonAdBatchKeywordDao amazonAdBatchKeywordDao;

    @Autowired
    private IAmazonAdBatchGroupDao amazonAdBatchGroupDao;

    @Autowired
    private IAmazonAdBatchCampaignDao amazonAdBatchCampaignDao;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IAmazonAdPortfolioDao amazonAdPortfolioDao;

    @Override
    public List<SpBatchCreatePreviewVo> generatePreview(SpBatchCreatePreviewRequest request) {
        AdStructure adStructure = adStructureMap.get(AdStructureEnum.structureMap.get(request.getAdStructure()).getBeanId());
        return adStructure.generatePreview(request.getBatchDataList());
    }

    @Override
    public void submit(SpBatchCreateSubmitRequest request) {
        //保存任务数据
        List<Long> idList = batchSequenceDao.batchGenId(request.getBatchDataList().size());
        List<AmazonAdBatchTask> taskList = new ArrayList<>(idList.size());
        for (int i = 0; i < request.getBatchDataList().size(); i++) {
            SpBatchCreateSubmitTaskRequest taskRequest = request.getBatchDataList().get(i);
            AmazonAdBatchTask task = new AmazonAdBatchTask();
            task.setId(idList.get(i));
            task.setPuid(request.getPuid());
            task.setShopId(taskRequest.getBaseInfo().getShopId());
            task.setMarketplaceId(taskRequest.getBaseInfo().getMarketplaceId());
            task.setTaskName(taskRequest.getTaskInfo().getTaskName());
            task.setAdStructure(request.getAdStructure());
            task.setCampaignType(CampaignTypeEnum.sp.getCampaignType());
            task.setStatus(SpBatchCreateTaskStatusEnum.DOING.getCode());
            task.setSourceId(request.getSourceTaskId());
            task.setLoginIp(request.getLoginIp());
            task.setCreateTime(new Date());
            task.setCreateId(request.getUid());
            taskList.add(task);
        }

        //批量保存任务
        amazonAdBatchTaskDao.insertList(request.getPuid(), taskList);

        //异步保存数据到数据库任务
        Map<Integer, Long> shopTaskIdMap = taskList.stream()
                .collect(Collectors.groupingBy(AmazonAdBatchTask::getShopId,
                        Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0).getId())));

        CompletableFuture.runAsync(() -> saveBatchDataTask.run(shopTaskIdMap, request), ThreadPoolUtil.getSpBatchCreateSaveData2DBPool());
    }

    @Override
    public List<SpBatchCreatePreviewBaseInfoRequest> multiShop(SpBatchCreateMultiShopFilterRequest request) {
        if (Objects.isNull(request)) {
            throw new ServiceException("批量新建任务的基本信息不能为空");
        }
        List<SpBatchCreatePreviewBaseInfoRequest> multiShopResult = Lists.newArrayList();
        //将基本信息中的竞价币种，开始结束时间，放到需要新建的店铺ID中进行校验
        SpBatchCreatePreviewBaseInfoRequest basicInfo = request.getBaseInfo();
        List<Integer> multiShopIds = request.getMultiShopIdsList();
        String startDateStr = basicInfo.getStartDateStr();
        String endDateStr = basicInfo.getEndDateStr();
        LocalDate starDate = LocalDate.parse(startDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate endDate = Optional.of(endDateStr).filter(StringUtils::isNotEmpty)
                .map(e -> LocalDate.parse(e, DateTimeFormatter.ofPattern("yyyy-MM-dd"))).orElse(null);
        String bid = basicInfo.getDefaultBid();
        String budget = basicInfo.getDailyBudget();
        String marketplaceId = basicInfo.getMarketplaceId();
        String srcCurrency = shopCurrencyHandler(marketplaceId);
        String srcStrategy = basicInfo.getStrategy();
        String srcPlacementTop = basicInfo.getPlacementTop();
        String srcPlacementProductPage = basicInfo.getPlacementProductPage();
        String srcPlacementRestOfSearch = basicInfo.getPlacementRestOfSearch();
        List<SpBatchCreatePreviewProductRequest> product = basicInfo.getProductsList();
        List<SpBatchCreatePreviewNekeywordRequest> neKeyword = basicInfo.getNekeywordsList();
        List<SpBatchCreatePreviewNetargetingRequest> neTargeting = basicInfo.getNetargetingsList();
        String campaignNameJson = basicInfo.getCampaignNameJson();
        String groupNameJson = basicInfo.getGroupNameJson();

        //根据shopId获取店铺权限
        List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(request.getPuid(), multiShopIds);
        Map<Integer, ShopAuth> shopAuthMap = shopAuthList.stream().collect(Collectors.toMap(ShopAuth::getId, v1 -> v1, (old, current) -> current));
        //取当前店铺时间获取时区
        for (Integer targetShopId : multiShopIds) {
            ShopAuth auth = shopAuthMap.get(targetShopId);
            if (Objects.isNull(auth)) continue;
            String targetMarketplaceId = auth.getMarketplaceId();
            String targetBid = bid;
            String targetBudget = budget;
            LocalDate beginTime, endTime;
            //以服务器时间为基准判断选择的时间在当前站点的时间和复制站点是否已经过去，如果已经过去，则置为对应站点的时间
            beginTime = shopBeginAndEndTimeHandler(LocalDateTime.now(), null, targetMarketplaceId);
            if (StringUtils.isEmpty(startDateStr) ||
                    starDate.isBefore(beginTime)) {
                endTime = null;
            }else {
                beginTime = starDate;
                endTime = endDate;
            }
            //校验竞价和预算的币种
            String tarCurrency = shopCurrencyHandler(targetMarketplaceId);
            if (Objects.nonNull(srcCurrency) && !srcCurrency.equalsIgnoreCase(tarCurrency)) {
                targetBid = "";
                targetBudget = "";
            }

            SpBatchCreatePreviewBaseInfoRequest.Builder multiTargetBuilder = SpBatchCreatePreviewBaseInfoRequest.newBuilder();
            multiTargetBuilder.setShopId(targetShopId);
            multiTargetBuilder.setMarketplaceId(targetMarketplaceId);
            multiTargetBuilder.setDefaultBid(targetBid);
            multiTargetBuilder.setDailyBudget(targetBudget);
            multiTargetBuilder.setStartDateStr(Optional.ofNullable(beginTime).map(LocalDate::toString).orElse(""));
            multiTargetBuilder.setEndDateStr(Optional.ofNullable(endTime).map(LocalDate::toString).orElse(""));
            multiTargetBuilder.setStrategy(srcStrategy);
            multiTargetBuilder.setPlacementTop(srcPlacementTop);
            multiTargetBuilder.setPlacementProductPage(srcPlacementProductPage);
            multiTargetBuilder.setPlacementRestOfSearch(srcPlacementRestOfSearch);
            multiTargetBuilder.addAllProducts(product);//填充在线产品,失效的产品在主站过滤
            multiTargetBuilder.addAllNekeywords(neKeyword);//填充否定关键词投放
            multiTargetBuilder.addAllNetargetings(neTargeting);//填充否定商品投放
            multiTargetBuilder.setCampaignNameJson(Optional.of(campaignNameJson).orElse(""));
            multiTargetBuilder.setGroupNameJson(Optional.of(groupNameJson).orElse(""));
            multiShopResult.add(multiTargetBuilder.build());
        }
        return multiShopResult;
    }


    @Override
    public SpBatchCreateCopyResult copyTask(Integer puid, Integer uid, Integer shopId, Long taskId) {
        //先判断要复制的任务状态是否合法
        AmazonAdBatchTask task = amazonAdBatchTaskDao.getByPuidAndId(puid, taskId);
        if (Objects.isNull(task) || (!SpBatchCreateTaskStatusEnum.FINISH_SUCCESS.getCode().equals(task.getStatus())
                && !SpBatchCreateTaskStatusEnum.FINISH_FAILURE.getCode().equals(task.getStatus()))) {
            log.error("copy task status error,shopId:{}. taskId:{}, task:{}", shopId, taskId, task);
            throw new ServiceException("该任务不存在或状态不支持复制");
        }
        //获取批量任务基本信息
        AmazonAdBatchBaseInfo baseInfo = amazonAdBatchBaseInfoDao.getByPuidAndTaskId(puid, taskId);
        if (Objects.isNull(baseInfo) || Objects.isNull(baseInfo.getShopId()) || Objects.isNull(baseInfo.getTaskId())) {
            log.error("copy task base info not exist, shopId:{}, taskId:{}, task:{}", shopId, taskId, task);
            return null;
        }
        //获取要复制任务的广告活动及其下各层级的信息
        List<SpBatchCreatePreviewCampaignVo> taskCampaigns = getTaskPreviewCampaign(puid, baseInfo.getShopId(), baseInfo.getTaskId());

        SpBatchCreateSubmitTaskInfoRequest.Builder taskInfo = SpBatchCreateSubmitTaskInfoRequest.newBuilder();
        Optional.ofNullable(task.getTaskName()).ifPresent(taskInfo::setTaskName);
        return SpBatchCreateCopyResult.newBuilder()
                .setPuid(Optional.ofNullable(baseInfo.getPuid()).orElse(0))
                .setAdStructure(Optional.ofNullable(task.getAdStructure()).orElse(""))
                .setBaseInfo(getTaskPreviewBaseInfo(baseInfo, puid, taskId))
                .addAllCampaigns(Optional.ofNullable(taskCampaigns)
                        .filter(c -> !CollectionUtils.isEmpty(c)).orElse(Collections.emptyList()))
                .setTaskInfo(taskInfo.build())
                .build();
    }

    private List<BaseInfoProductDto> getTaskProduct(AmazonAdBatchBaseInfo baseInfo, Integer puid, Long taskId) {
        String taskProduct = baseInfo.getProductJson();
        if (StringUtils.isEmpty(taskProduct)) {
            return Collections.emptyList();
        }
        List<BaseInfoProductDto> productList = Lists.newArrayList();
        try {
            productList = JSONObject.parseArray(taskProduct, BaseInfoProductDto.class);
        } catch (Exception e) {
            log.error("format error, validate product , task product:{}, puid:{}, taskId:{}", taskProduct, puid, taskId);
        }
        return productList;
    }

    private List<BaseInfoNekeywordDto> getTaskNeKeyword(AmazonAdBatchBaseInfo baseInfo, Integer puid, Long taskId) {
        String neKeyword = baseInfo.getNekeywordJson();
        if (StringUtils.isEmpty(neKeyword)) {
            return Collections.emptyList();
        }
        List<BaseInfoNekeywordDto> neKeywordList = Lists.newArrayList();
        try {
            neKeywordList = JSONObject.parseArray(neKeyword, BaseInfoNekeywordDto.class);
        } catch (Exception e) {
            log.error("format error, validate neKeyword, task neKeyword:{}, puid:{}, taskId:{}", neKeyword, puid, taskId);
        }
        return neKeywordList;
    }

    private List<BaseInfoNetargetingDto> getTaskNeTargeting(AmazonAdBatchBaseInfo baseInfo, Integer puid, Long taskId) {
        String neTargeting = baseInfo.getNetargetingJson();
        if (StringUtils.isEmpty(neTargeting)) {
            return Collections.emptyList();
        }
        List<BaseInfoNetargetingDto> neTargetingList = Lists.newArrayList();
        try {
            neTargetingList = JSONObject.parseArray(neTargeting, BaseInfoNetargetingDto.class);
        } catch (Exception e) {
            log.error("format error, validate neTargeting , task neTargeting:{}, puid:{}, taskId:{}", neTargeting, puid, taskId);
        }
        return neTargetingList;
    }

//    private Table<String, String, List<KeywordAndTargetingInfoInTaskDTO>> getTaskKeywordAndTargeting(Integer puid, Integer shopId, Long taskId) {
//        //从详情表中查出对应的投放数据
//        //按照广告活动和广告组进行分组
//        List<KeywordAndTargetingInfoInTaskDTO> keywordAndTargeting = amazonAdBatchKeywordAndTargetingDao.
//                getKeywordAndTargetingListByTaskIdAndCampaign(puid, shopId, taskId, null);
//        if (CollectionUtils.isEmpty(keywordAndTargeting)) {
//            return null;
//        }
//        Table<String, String, List<KeywordAndTargetingInfoInTaskDTO>> result = HashBasedTable.create();
//        for (KeywordAndTargetingInfoInTaskDTO dto : keywordAndTargeting) {
//            if (Objects.nonNull(dto) && StringUtils.isNotEmpty(dto.getCampaignId()) && Objects.nonNull(dto.getGroupId())) {
//                List<KeywordAndTargetingInfoInTaskDTO> dtoList = result.get(dto.getCampaignId(), String.valueOf(dto.getGroupId()));
//                if (Objects.isNull(dtoList)) {
//                    result.put(dto.getCampaignId(), String.valueOf(dto.getGroupId()), Arrays.asList(dto));
//                } else {
//                    dtoList.add(dto);
//                    result.put(dto.getCampaignId(), String.valueOf(dto.getGroupId()), dtoList);
//                }
//            }
//        }
//        return result;
//    }

    private List<BaseInfoNekeywordDto> getGroupNeKeyword(Integer puid, Integer shopId,
                                                         Long taskId, Long groupId) {
        List<BaseInfoNekeywordDto> resultList = Lists.newArrayList();
        try {
            List<AmazonAdBatchNekeyword> neKeywordList = amazonAdBatchNekeywordDao.listByGroupIdList(puid, shopId, taskId, Arrays.asList(groupId), null);
            if (CollectionUtils.isEmpty(neKeywordList)) {
                return Collections.emptyList();
            }
            resultList = neKeywordList.stream().map(k -> BaseInfoNekeywordDto.builder()
                    .keywordText(k.getKeywordText())
                    .matchType(k.getMatchType())
                    .build()).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("format error, get group nekeyword, puid:{}, taskId:{}, groupId:{}", puid, taskId, groupId);
        }
        return resultList;
    }

    private List<BaseInfoNetargetingDto> getGroupNeTargeting(Integer puid, Integer shopId,
                                                             Long taskId, Long groupId) {
        List<BaseInfoNetargetingDto> resultList = Lists.newArrayList();
        try {
            List<AmazonAdBatchNetargeting> neKeywordList = amazonAdBatchNetargetingDao.listByGroupIdList(puid, shopId, Long.valueOf(taskId),
                    Collections.singletonList(groupId), null);
            if (CollectionUtils.isEmpty(neKeywordList)) {
                return Collections.emptyList();
            }
            resultList = neKeywordList.stream().map(t -> {
                BaseInfoNetargetingDto dto = BaseInfoNetargetingDto.builder()
                        .build();
                BeanUtils.copyProperties(t, dto, ParamCopyUtil.checkPropertiesNullOrEmpty(t));
                return dto;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("format error, get group neTargeting, puid:{}, taskId:{}, groupId:{}", puid, taskId, groupId);
        }
        return resultList;
    }

    private MutablePair<Map<Long, List<SpBatchCreateSubmitTargetingVo>>, Map<Long, SpBatchCreatePreviewAutoTargetingVo>> getGroupAllTargeting(Integer puid, Integer shopId,
                                                                                                                                   Long taskId, List<Long> groupId) {
        //查询投放详情表，其中包含商品投放和自动投放
        List<AmazonAdBatchTargeting> groupTargeting = amazonAdBatchTargetingDao.
                listByGroupIdList(puid, shopId, taskId, groupId, null, null);
        if (CollectionUtils.isEmpty(groupTargeting)) {
            return null;
        }
        //处理自动投放
        List<AmazonAdBatchTargeting> autoTargeting = groupTargeting.stream().
                filter(t -> TargetingEnum.auto.getTargetingType().equalsIgnoreCase(t.getExpressionType()))
                .collect(Collectors.toList());
        Map<Long, List<AmazonAdBatchTargeting>> autoTargetingMap = autoTargeting.stream().collect(Collectors.groupingBy(AmazonAdBatchTargeting::getGroupId));
        Map<Long, SpBatchCreatePreviewAutoTargetingVo> autoTargetingResult = new HashMap<>();
        for(Map.Entry<Long, List<AmazonAdBatchTargeting>> entry : autoTargetingMap.entrySet()) {
            Long gId = entry.getKey();
            if (CollectionUtils.isEmpty(entry.getValue())) continue;
            SpBatchCreatePreviewAutoTargetingVo.Builder autoBuilder = SpBatchCreatePreviewAutoTargetingVo.newBuilder();
            entry.getValue().forEach(a -> {
                boolean state = !Constants.AUTO_TARGETING_STATE_PAUSE.equals(a.getState());
                if (KeywordAndTargetingEnum.AUTO_ASIN_ACCESSORY_RELATED.getCode().equalsIgnoreCase(a.getTargetingValue())) {
                    autoBuilder.setAsinAccessoryRelatedState(state);
                    Optional.ofNullable(a.getBid()).map(String::valueOf).ifPresent(autoBuilder::setAsinAccessoryRelatedBid);
                }
                if (KeywordAndTargetingEnum.AUTO_ASIN_SUBSTITUTE_RELATED.getCode().equalsIgnoreCase(a.getTargetingValue())) {
                    autoBuilder.setAsinSubstituteRelatedState(state);
                    Optional.ofNullable(a.getBid()).map(String::valueOf).ifPresent(autoBuilder::setAsinSubstituteRelatedBid);
                }
                if (KeywordAndTargetingEnum.AUTO_QUERY_BROADREL_MATCHES.getCode().equalsIgnoreCase(a.getTargetingValue())) {
                    autoBuilder.setQueryBroadRelMatchesState(state);
                    Optional.ofNullable(a.getBid()).map(String::valueOf).ifPresent(autoBuilder::setQueryBroadRelMatchesBid);
                }
                if (KeywordAndTargetingEnum.AUTO_QUERY_HIGHREL_MATCHES.getCode().equalsIgnoreCase(a.getTargetingValue())) {
                    autoBuilder.setQueryHighRelMatchesState(state);
                    Optional.ofNullable(a.getBid()).map(String::valueOf).ifPresent(autoBuilder::setQueryHighRelMatchesBid);
                }
            });
            autoTargetingResult.put(gId, autoBuilder.build());
        }

        //处理手动商品投放
        List<AmazonAdBatchTargeting> manualTargeting = groupTargeting.stream().
                filter(t -> TargetingEnum.manual.getTargetingType().equalsIgnoreCase(t.getExpressionType()))
                .collect(Collectors.toList());
        Map<Long, List<AmazonAdBatchTargeting>> manualTargetingMap = manualTargeting.stream().collect(Collectors.groupingBy(AmazonAdBatchTargeting::getGroupId));
        Map<Long, List<SpBatchCreateSubmitTargetingVo>> manualTargetingResult = new HashMap<>();
        for (Map.Entry<Long, List<AmazonAdBatchTargeting>> mEntry : manualTargetingMap.entrySet()) {
            Long mId = mEntry.getKey();
            if (CollectionUtils.isEmpty(mEntry.getValue())) continue;
            List<SpBatchCreateSubmitTargetingVo> targetingList = mEntry.getValue().stream().map(m -> {
                SpBatchCreateSubmitTargetingVo.Builder vo = SpBatchCreateSubmitTargetingVo.newBuilder();
                vo.setType(m.getType());
                vo.setAsin(m.getAsin());
                vo.setTitle(m.getTitle());
                vo.setImgUrl(m.getImgUrl());
                vo.setExpressionType(m.getSelectType());
                vo.setCategoryId(m.getCategoryId());
                vo.setCategory(m.getCategory());
                vo.setName(m.getCategoryName());
                Optional.ofNullable(m.getBrand()).ifPresent(vo::setBrand);
                Optional.ofNullable(m.getBrandName()).ifPresent(vo::setBrandName);
                Optional.ofNullable(m.getMinPrice()).map(BigDecimal::toString).ifPresent(vo::setMinPrice);
                Optional.ofNullable(m.getMaxPrice()).map(BigDecimal::toString).ifPresent(vo::setMaxPrice);
                Optional.ofNullable(m.getMinReviewRating()).ifPresent(vo::setMinReviewRating);
                Optional.ofNullable(m.getMaxReviewRating()).ifPresent(vo::setMaxReviewRating);
                Optional.ofNullable(m.getPrimeShippingEligible()).ifPresent(vo::setPrimeShippingEligible);
                Optional.ofNullable(m.getBid()).map(BigDecimal::toString).ifPresent(vo::setBid);
                Optional.ofNullable(m.getSuggested()).map(BigDecimal::toString).ifPresent(vo::setSuggested);
                Optional.ofNullable(m.getRangeStart()).map(BigDecimal::toString).ifPresent(vo::setRangeStart);
                Optional.ofNullable(m.getRangeEnd()).map(BigDecimal::toString).ifPresent(vo::setRangeEnd);
                return vo.build();
            }).collect(Collectors.toList());
            manualTargetingResult.put(mId, targetingList);
        }

        //为广告组自动投放和投放数据进行赋值
        MutablePair<Map<Long, List<SpBatchCreateSubmitTargetingVo>>, Map<Long, SpBatchCreatePreviewAutoTargetingVo>> result = new MutablePair<>();
        result.setLeft(manualTargetingResult);
        result.setRight(autoTargetingResult);
        return result;
    }

    private List<SpBatchCreateSubmitKeywordVo> getGroupKeyword(Integer puid, Integer shopId,
                                 Long taskId, List<Long> groupId) {
        List<AmazonAdBatchKeyword> keywordList = amazonAdBatchKeywordDao.listByGroupIdList(puid, shopId, Long.valueOf(taskId), groupId, null);
        return keywordList.stream().map(k -> {
            SpBatchCreateSubmitKeywordVo.Builder builder = SpBatchCreateSubmitKeywordVo.newBuilder();
            Optional.ofNullable(k.getKeywordText()).ifPresent(builder::setKeywordText);
            Optional.ofNullable(k.getMatchType()).ifPresent(builder::setMatchType);
            Optional.ofNullable(k.getBid()).map(b -> b.setScale(2, RoundingMode.HALF_UP)).map(String::valueOf).ifPresent(builder::setBid);
            Optional.ofNullable(k.getSuggested()).map(BigDecimal::toString).ifPresent(builder::setSuggested);
            Optional.ofNullable(k.getRangeStart()).map(BigDecimal::toString).ifPresent(builder::setRangeStart);
            Optional.ofNullable(k.getRangeEnd()).map(BigDecimal::toString).ifPresent(builder::setRangeEnd);
            Optional.ofNullable(k.getGroupId()).ifPresent(builder::setGroupId);
            return builder.build();
        }).collect(Collectors.toList());
    }

    private List<SpBatchCreatePreviewProductRequest> getTaskPreviewProduct(AmazonAdBatchBaseInfo baseInfo, Integer puid, Long taskId) {
        List<BaseInfoProductDto> product = getTaskProduct(baseInfo, puid, taskId);
        return product.stream().map(p -> {
            SpBatchCreatePreviewProductRequest.Builder builder = SpBatchCreatePreviewProductRequest.newBuilder();
            builder.setId(p.getId());
            builder.setSku(p.getSku());
            builder.setAsin(p.getAsin());
            if (StringUtils.isNotBlank(p.getImageUrl())) {
                builder.setImgUrl(p.getImageUrl());
            }
            if (StringUtils.isNotBlank(p.getTitle())) {
                builder.setTitle(p.getTitle());
            }
            if (StringUtils.isNotBlank(p.getDomain())) {
                builder.setDomain(p.getDomain());
            }
            return builder.build();
        }).collect(Collectors.toList());
    }

    private List<SpBatchCreatePreviewNekeywordRequest> getTaskPreviewNekeyword(AmazonAdBatchBaseInfo baseInfo, Integer puid, Long taskId) {
        List<BaseInfoNekeywordDto> keyword = getTaskNeKeyword(baseInfo, puid, taskId);
        return keyword.stream().map(k -> {
            SpBatchCreatePreviewNekeywordRequest.Builder builder = SpBatchCreatePreviewNekeywordRequest.newBuilder();
            builder.setKeywordText(k.getKeywordText());
            builder.setMatchType(k.getMatchType());
            return builder.build();
        }).collect(Collectors.toList());
    }

    private List<SpBatchCreatePreviewNetargetingRequest> getTaskPreviewNeTargeting(AmazonAdBatchBaseInfo baseInfo, Integer puid, Long taskId) {
        List<BaseInfoNetargetingDto> targeting = getTaskNeTargeting(baseInfo, puid, taskId);
        return targeting.stream().map(t -> {
            SpBatchCreatePreviewNetargetingRequest.Builder builder = SpBatchCreatePreviewNetargetingRequest.newBuilder();
            BeanUtils.copyProperties(t, builder, ParamCopyUtil.checkPropertiesNullOrEmpty(t));
            return builder.build();
        }).collect(Collectors.toList());
    }

    private SpBatchCreatePreviewBaseInfoRequest getTaskPreviewBaseInfo(AmazonAdBatchBaseInfo baseInfo, Integer puid, Long taskId) {
        SpBatchCreatePreviewBaseInfoRequest.Builder builder = SpBatchCreatePreviewBaseInfoRequest.newBuilder();
        //先获取基本信息
        if (Objects.isNull(baseInfo)) {
            return builder.build();//为空直接返回
        }
        Optional.ofNullable(baseInfo.getShopId()).ifPresent(builder::setShopId);
        Optional.ofNullable(baseInfo.getMarketplaceId()).ifPresent(builder::setMarketplaceId);
        Optional.ofNullable(baseInfo.getEndDate()).map(d -> {
            SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
            return fmt.format(d);
        }).ifPresent(builder::setEndDateStr);
        Optional.ofNullable(baseInfo.getStartDate()).map(d -> {
            SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
            return fmt.format(d);
        }).ifPresent(builder::setStartDateStr);
        Optional.ofNullable(baseInfo.getBudget()).map(b -> b.setScale(2, RoundingMode.HALF_UP)).map(String::valueOf).ifPresent(builder::setDailyBudget);
        Optional.ofNullable(baseInfo.getStrategy()).ifPresent(builder::setStrategy);
        Optional.ofNullable(baseInfo.getAdjustmentsTop()).map(String::valueOf).ifPresent(builder::setPlacementTop);
        Optional.ofNullable(baseInfo.getAdjustmentsProduct()).map(String::valueOf).ifPresent(builder::setPlacementProductPage);
        Optional.ofNullable(baseInfo.getAdjustmentsOther()).map(String::valueOf).ifPresent(builder::setPlacementRestOfSearch);
        Optional.ofNullable(baseInfo.getDefaultBid()).map(b -> b.setScale(2, RoundingMode.HALF_UP)).map(String::valueOf).ifPresent(builder::setDefaultBid);

        //获取广告产品
        List<SpBatchCreatePreviewProductRequest> product = getTaskPreviewProduct(baseInfo, puid, taskId);
        List<SpBatchCreatePreviewNekeywordRequest> neKeyword = getTaskPreviewNekeyword(baseInfo, puid, taskId);
        List<SpBatchCreatePreviewNetargetingRequest> neTargeting = getTaskPreviewNeTargeting(baseInfo, puid, taskId);
        Optional.ofNullable(product).ifPresent(builder::addAllProducts);
        Optional.ofNullable(neKeyword).ifPresent(builder::addAllNekeywords);
        Optional.ofNullable(neTargeting).ifPresent(builder::addAllNetargetings);
        Optional.ofNullable(baseInfo.getCampaignNameTemplateJson()).ifPresent(builder::setCampaignNameJson);
        Optional.ofNullable(baseInfo.getGroupNameTemplateJson()).ifPresent(builder::setGroupNameJson);
        return builder.build();
    }

    private List<SpBatchCreatePreviewCampaignVo> getTaskPreviewCampaign(Integer puid, Integer shopId, Long taskId) {
        List<AmazonAdBatchCampaign> taskCampaigns = amazonAdBatchCampaignDao.listByTaskId(puid, shopId, taskId);
        if (CollectionUtils.isEmpty(taskCampaigns)) {
            return Collections.emptyList();
        }
        List<Long> campaignIdList = taskCampaigns.stream().map(AmazonAdBatchCampaign::getId).collect(Collectors.toList());
        List<String> portfolioIdList = taskCampaigns.parallelStream().map(AmazonAdBatchCampaign::getPortfolioId)
                .collect(Collectors.toList());
        List<AmazonAdPortfolio> portfolioInfoList = amazonAdPortfolioDao.getPortfolioList(puid, shopId, portfolioIdList);
        Map<String, String> portfolioNameMap = portfolioInfoList.parallelStream().collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, AmazonAdPortfolio::getName));
        List<SpBatchCreatePreviewGroupVo> groupList = getPreviewGroup(puid, shopId, taskId, campaignIdList);
        Map<Long, List<SpBatchCreatePreviewGroupVo>> groupMap = groupList.parallelStream().collect(Collectors.groupingBy(SpBatchCreatePreviewGroupVo::getCampaignId));
        return taskCampaigns.stream().map(c -> {
            SpBatchCreatePreviewCampaignVo.Builder builder = SpBatchCreatePreviewCampaignVo.newBuilder();
            builder.setName(c.getName());
            Optional.ofNullable(c.getPortfolioId()).ifPresent(builder::setPortfolioId);
            Optional.ofNullable(portfolioNameMap.get(c.getPortfolioId())).ifPresent(builder::setPortfolioName);
            Optional.ofNullable(c.getMarketplaceId()).ifPresent(builder::setMarketplaceId);
            Optional.ofNullable(c.getStartDate()).map(d -> {
                SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
                return fmt.format(d);
            }).ifPresent(builder::setStartDateStr);
            Optional.ofNullable(c.getEndDate()).map(d -> {
                SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
                return fmt.format(d);
            }).ifPresent(builder::setEndDateStr);
            Optional.ofNullable(c.getBudget()).map(b -> b.setScale(2, RoundingMode.HALF_UP)).map(String::valueOf).ifPresent(builder::setDailyBudget);
            Optional.ofNullable(c.getStrategy()).ifPresent(builder::setStrategy);
            Optional.ofNullable(c.getAdjustmentsTop()).map(String::valueOf).ifPresent(builder::setPlacementTop);
            Optional.ofNullable(c.getAdjustmentsProduct()).map(String::valueOf).ifPresent(builder::setPlacementProductPage);
            Optional.ofNullable(c.getAdjustmentsOther()).map(String::valueOf).ifPresent(builder::setPlacementRestOfSearch);
            Optional.ofNullable(c.getTargetingType()).ifPresent(builder::setTargetingType);
            Optional.ofNullable(groupMap.get(c.getId())).ifPresent(builder::addAllGroups);
            return builder.build();
        }).collect(Collectors.toList());
    }

    private List<SpBatchCreatePreviewGroupVo> getPreviewGroup(Integer puid, Integer shopId,
                                                              Long taskId, List<Long> campaignIdList) {
        //获取广告组信息
        List<GroupInfoInTaskDTO> groupList = amazonAdBatchGroupDao.getGroupInfoByTaskIdAndCampaignId(puid, shopId, taskId, campaignIdList);
        List<Long> groupIdList = groupList.stream().map(GroupInfoInTaskDTO::getId).collect(Collectors.toList());
        MutablePair<Map<Long, List<SpBatchCreateSubmitTargetingVo>>, Map<Long, SpBatchCreatePreviewAutoTargetingVo>> targetingResult =
                getGroupAllTargeting(puid, shopId, taskId, groupIdList);
        Map<Long, List<SpBatchCreateSubmitTargetingVo>> manualTargetingnMap = Optional.ofNullable(targetingResult)
                .map(MutablePair::getLeft).orElse(new HashMap<>());
        Map<Long, SpBatchCreatePreviewAutoTargetingVo> autoTargetingMap = Optional.ofNullable(targetingResult)
                .map(MutablePair::getRight).orElse(new HashMap<>());
        List<SpBatchCreateSubmitKeywordVo> keywordResult = getGroupKeyword(puid, shopId, taskId, groupIdList);
        Map<Long, List<SpBatchCreateSubmitKeywordVo>> keywordMap = keywordResult.stream().collect(Collectors.groupingBy(SpBatchCreateSubmitKeywordVo::getGroupId));
        return groupList.stream().map(g -> {
            SpBatchCreatePreviewGroupVo.Builder builder = SpBatchCreatePreviewGroupVo.newBuilder();
            Optional.ofNullable(g.getName()).ifPresent(builder::setName);
            Optional.ofNullable(g.getMarketplaceId()).ifPresent(builder::setMarketplaceId);
            Optional.ofNullable(g.getDefaultBid()).map(String::valueOf).ifPresent(builder::setDefaultBid);
            Optional.ofNullable(g.getType()).ifPresent(builder::setType);
//            Optional.ofNullable(g.getType()).ifPresent(builder::setMatchType);
            List<BaseInfoNekeywordDto> neKeyword = getGroupNeKeyword(puid, shopId, taskId, g.getId());
            if (!CollectionUtils.isEmpty(neKeyword)) {
                builder.addAllNekeywords(neKeyword.stream().map(n -> {
                    SpBatchCreatePreviewNekeywordVo.Builder neKeywordBuilder = SpBatchCreatePreviewNekeywordVo.newBuilder();
                    BeanUtils.copyProperties(n, neKeywordBuilder, ParamCopyUtil.checkPropertiesNullOrEmpty(n));
                    return neKeywordBuilder.build();
                }).collect(Collectors.toList()));
            }
            List<BaseInfoNetargetingDto> neTargeting = getGroupNeTargeting(puid, shopId, taskId, g.getId());
            if (!CollectionUtils.isEmpty(neTargeting)) {
                builder.addAllNetargetings(neTargeting.stream().map(n -> {
                    SpBatchCreatePreviewNetargetingVo.Builder neTargetingBuilder = SpBatchCreatePreviewNetargetingVo.newBuilder();
                    BeanUtils.copyProperties(n, neTargetingBuilder, ParamCopyUtil.checkPropertiesNullOrEmpty(n));
                    return neTargetingBuilder.build();
                }).collect(Collectors.toList()));
            }
            Optional.ofNullable(g.getCampaignId()).ifPresent(builder::setCampaignId);
            Optional.ofNullable(autoTargetingMap.get(g.getId())).ifPresent(builder::setAutoTargetings);
            Optional.ofNullable(manualTargetingnMap.get(g.getId())).ifPresent(builder::addAllTargetings);
            Optional.ofNullable(keywordMap.get(g.getId())).ifPresent(builder::addAllKeywords);
            return builder.build();
        }).collect(Collectors.toList());
    }

    private LocalDate shopBeginAndEndTimeHandler(LocalDateTime beginTime, String srcMarketplaceId, String targetMarketplaceId) {
        ZoneId srcZoneId = ZoneUtil.getZoneIdByAmzSite(srcMarketplaceId);
        return ZoneUtil.getTargetZoneTime(targetMarketplaceId, beginTime, srcZoneId);
    }

    private String shopCurrencyHandler(String tarMarketplaceId) {
        return MarketTimezoneAndCurrencyEnum.getByMarketplaceId(tarMarketplaceId).getCurrencyCode();
    }
}
