package com.meiyunji.sponsored.service.multiple.campagin.vo;

import com.meiyunji.sponsored.rpc.adCommon.AdHomeChartRpcVo;
import com.meiyunji.sponsored.service.multiple.common.vo.CommonCompareReportRate;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 广告活动汇总响应数据
 *
 * @author: zzh
 * @create: 2024-11-21 10:05
 */
@Data
public class MultipleCampaignAggregateVo {

    @ApiModelProperty("报告汇总数据")
    private CommonCompareReportRate report;

    @ApiModelProperty("每日预算汇总")
    private String dailyBudgetSum;

    @ApiModelProperty("多店铺展示币种")
    private String currency;

    @ApiModelProperty("是否超过限制")
    private Boolean overLimit = false;

    @ApiModelProperty("日图表数据")
    private List<AdHomeChartRpcVo> day;

    @ApiModelProperty("周图表数据")
    private List<AdHomeChartRpcVo> week;

    @ApiModelProperty("月图表数据")
    private List<AdHomeChartRpcVo> month;

}
