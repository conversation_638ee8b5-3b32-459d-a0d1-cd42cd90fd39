package com.meiyunji.sponsored.service.cpc.service2.impl;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.cpc.bo.KeywordLibMarkupAsinBo;
import com.meiyunji.sponsored.service.cpc.bo.KeywordsLibBo;
import com.meiyunji.sponsored.service.cpc.dao.IAdKeywordLibMarkupAsinDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdKeywordsLibDao;
import com.meiyunji.sponsored.service.cpc.po.AdKeywordLibMarkupAsin;
import com.meiyunji.sponsored.service.cpc.qo.KeywordLibAsinPageListQo;
import com.meiyunji.sponsored.service.cpc.qo.KeywordLibMarkupAsinQo;
import com.meiyunji.sponsored.service.cpc.qo.KeywordLibUnMarkAsinQo;
import com.meiyunji.sponsored.service.cpc.service2.IAdKeywordLibMarkupAsinService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.ErrorMsgVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-10-24  16:15
 */
@Service
public class AdKeywordLibMarkupAsinServiceImpl implements IAdKeywordLibMarkupAsinService {
    @Resource
    private IAdKeywordLibMarkupAsinDao adKeywordLibMarkupAsinDao;
    @Resource
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdKeywordsLibDao amazonAdKeywordLibDao;

    @Override
    public Page<KeywordLibMarkupAsinBo> getAsinPageList(Integer puid, List<Integer> uidList, List<Integer> shopIdList, KeywordLibAsinPageListQo qo) {
        List<String> marketplaceIdList = shopAuthDao.marketplaceIdListByShopIds(shopIdList);
        if (CollectionUtils.isEmpty(marketplaceIdList)) {
            return new Page<>(qo.getPageNo(), qo.getPageSize());
        }
        return adKeywordLibMarkupAsinDao.getAsinPageList(puid, uidList, marketplaceIdList, qo);
    }

    @Override
    public Result<List<ErrorMsgVo>> markupAsin(Integer puid, Integer uid, List<Integer> shopIdList, KeywordLibMarkupAsinQo qo) {
        List<Long> idList = qo.getIdList().stream().distinct().collect(Collectors.toList());
        List<KeywordsLibBo> boList = amazonAdKeywordLibDao.getKeywordsLibBoByIds(puid, idList, qo.getUidList());
        if (boList.size() != idList.size()) {
            return ResultUtil.returnErr("标记对象不存在，请刷新页面重试");
        }
        List<AdKeywordLibMarkupAsin> idMap = adKeywordLibMarkupAsinDao.getListByKeywordsLibId(puid, qo.getUidList(), idList);
        // 找到各个Id下的词
        Map<Long, Set<String>> setMap = idMap.stream().collect(Collectors.groupingBy(AdKeywordLibMarkupAsin::getKeywordsLibId, Collectors.mapping(k -> k.getAsin() + StringUtil.SPECIAL_COMMA + k.getMarketplaceId(), Collectors.toSet())));
        Map<String, List<KeywordsLibBo>> map = boList.stream().collect(Collectors.groupingBy(k -> k.getKeywordText().toLowerCase()));
        List<ErrorMsgVo> errorVoList = new ArrayList<>();
        List<AdKeywordLibMarkupAsin> insertList = new ArrayList<>();
        // 便利要添加的asin
        Set<String> addAsinSet = qo.getMarkupAsinList().stream().map(k -> k.getAsin() + StringUtil.SPECIAL_COMMA + k.getMarketplaceId()).collect(Collectors.toSet());
        map.forEach((key, value) -> {
            Set<String> asinSet = new HashSet<>();
            // 相关关键词的标签聚合在一起
            for (KeywordsLibBo libBo : value) {
                Set<String> set = setMap.get(libBo.getId());
                if (set != null) {
                    asinSet.addAll(set);
                }
            }
            // 添加当前标签到里面取
            asinSet.addAll(addAsinSet);
            if (asinSet.size() > Constants.KEYWORD_LIB_TAG_MAX_SIZE) {
                ErrorMsgVo errorVo = new ErrorMsgVo();
                errorVo.setId(0L);
                errorVo.setName(key);
                errorVo.setError("部分关键词添加的标签数量超过限制");
                errorVoList.add(errorVo);
                return;
            }
            for (KeywordsLibBo libBo : value) {
                for (KeywordLibMarkupAsinQo.MarkupAsin markupAsin : qo.getMarkupAsinList()) {
                    AdKeywordLibMarkupAsin adKeywordLibMarkupAsin = new AdKeywordLibMarkupAsin();
                    adKeywordLibMarkupAsin.setPuid(puid);
                    adKeywordLibMarkupAsin.setUid(uid);
                    adKeywordLibMarkupAsin.setKeywordsLibId(libBo.getId());
                    adKeywordLibMarkupAsin.setMarketplaceId(markupAsin.getMarketplaceId());
                    adKeywordLibMarkupAsin.setAsin(markupAsin.getAsin());
                    adKeywordLibMarkupAsin.setCreateId(uid);
                    adKeywordLibMarkupAsin.setUpdateId(uid);
                    insertList.add(adKeywordLibMarkupAsin);
                }
            }
        });

        if (CollectionUtils.isNotEmpty(insertList)) {
            adKeywordLibMarkupAsinDao.batchInsert(insertList);
        } else {
            return ResultUtil.returnErr("添加的标签数量超过限制");
        }
        if (CollectionUtils.isNotEmpty(errorVoList)) {
            return ResultUtil.success(errorVoList);
        }
        return ResultUtil.success();
    }

    @Override
    public Result unMarkAsin(int puid, List<Integer> uid, List<Integer> shopIdList, KeywordLibUnMarkAsinQo qo) {
        List<String> marketplaceIdList = shopAuthDao.marketplaceIdListByShopIds(shopIdList);
        if (!marketplaceIdList.contains(qo.getMarketplaceId())) {
            return ResultUtil.returnErr("没有该ASIN店铺权限");
        }
        adKeywordLibMarkupAsinDao.deleteByKeywordsLibIdAndAsin(puid, uid, qo.getIdList(), qo.getMarketplaceId(), qo.getAsin());
        return ResultUtil.success();
    }
}
