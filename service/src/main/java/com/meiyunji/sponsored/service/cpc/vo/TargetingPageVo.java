package com.meiyunji.sponsored.service.cpc.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Created by xp on 2021/4/14.
 * 广告组下的投放列表页显示字段
 */
@Data
@ApiModel
public class TargetingPageVo extends CpcCommPageVo {

    @ApiModelProperty("类型 sp sb sd")
    private String aType;
    private Long id;
    @ApiModelProperty("店铺ID")
    private Integer shopId;
    private Long dxmAdGroupId;
    @ApiModelProperty("活动ID")
    private String campaignId;

    @ApiModelProperty("活动名称")
    private String campaignName;
    @ApiModelProperty("活动状态")
    private String campaignState;
    @ApiModelProperty("活动表中的 投放类型")
    private String campaignTargetingType;
    @ApiModelProperty("投放表中的 投放类型")
    private String tacticType;
    @ApiModelProperty("组ID")
    private String adGroupId;
    @ApiModelProperty("组名称")
    private String adGroupName;
    @ApiModelProperty("组状态")
    private String adGroupState;
    @ApiModelProperty("sb 投放类型 product keyword")
    private String groupType;
    @ApiModelProperty("targetId")
    private String targetId;
    @ApiModelProperty("状态")
    private String state;
    @ApiModelProperty("类型 asin、category、exactProduct、similarProduct")
    private String type; // asin、category、exactProduct、similarProduct
    @ApiModelProperty("asin")
    private String asin;
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("目录")
    private String category;
    @ApiModelProperty("受众类型")
    private String targetType;
    @ApiModelProperty("投放文本")
    private String targetText;
    @ApiModelProperty("图片url")
    private String imgUrl;
    @ApiModelProperty("domain")
    private String domain;
    @ApiModelProperty("竞价")
    private String bid;
    @ApiModelProperty("建议竞价")
    private String suggestBid;
    @ApiModelProperty("建议竞价范围开始")
    private String rangeStart;
    @ApiModelProperty("建议竞价范围结束")
    private String rangeEnd;
    @ApiModelProperty("是否开启分时调价")
    private Integer isPricing;
    @ApiModelProperty("分时调价任务状态")
    private Integer pricingState;
    @ApiModelProperty("站点")
    private String marketplaceId;
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;
    @ApiModelProperty("广告组合ID")
    private String portfolioId;

    @ApiModelProperty("广告组合名称")
    private String portfolioName;

    @ApiModelProperty("广告组合隐藏状态 1:隐藏  0:可见")
    private Integer isHidden;

    @ApiModelProperty("广告预算")
    private String dailyBudget;

    @ApiModelProperty("二级状态")
    private String servingStatus;

    @ApiModelProperty("二级状态描述")
    private String servingStatusDec;

    @ApiModelProperty("二级状态名称")
    private String servingStatusName;

    @ApiModelProperty("标签")
    private List<AdTag> adTags;

    @ApiModelProperty("cpc,vcpm区分标识")
    private String costType;

    @ApiModelProperty("品牌名：brandName")
    private String brandName;

    @ApiModelProperty("商品价格范围:commodityPriceRange")
    private String commodityPriceRange;

    @ApiModelProperty("星级:rating")
    private String rating;

    @ApiModelProperty("配送:distribution")
    private String distribution;

    @ApiModelProperty("回溯期:lookback")
    private String lookback;

    @ApiModelProperty("筛选类型")
    private String selectType;

    @ApiModelProperty("搜索词首页首位曝光率")
    private String topImpressionShare;
    private Integer isStateBidding;
    private Integer pricingStateBidding;
    private Boolean sbType;
    @ApiModelProperty("默认竞价")
    private String defaultBid;
    private String adFormat;
    private String creativeType;
    private String adGoal;
    @ApiModelProperty("广告策略集合")
    private List<AdStrategyVo> strategyList;
}
