package com.meiyunji.sponsored.service.syncTask;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbAdsApiService;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdProductApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcAdProductApiService;
import com.meiyunji.sponsored.service.stream.enums.AmazonStreamTaskTypeEnum;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamLogService;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamRedisCountService;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamTaskRetryService;
import com.meiyunji.sponsored.service.syncTask.entity.monitor.MonitorStreamTypeEnum;
import com.meiyunji.sponsored.service.syncTask.entity.monitor.MonitorTypeEnum;
import com.meiyunji.sponsored.service.syncTask.management.strgtegy.enume.AdProductEnum;
import com.meiyunji.sponsored.service.syncTask.message.ManagementAdStreamMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2024/1/12 9:36
 * @describe:
 */
@Component
@Slf4j
public class ManagementAdConsumer {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private CpcAdProductApiService cpcAdProductApiService;

    @Autowired
    private CpcSbAdsApiService cpcSbAdsApiService;

    @Autowired
    private CpcSdProductApiService cpcSdProductApiService;

    @Autowired
    private IAmazonManagementStreamTaskRetryService managementStreamTaskRetryService;

    @Autowired
    private IAmazonManagementStreamLogService amazonManagementStreamLogService;
    @Autowired
    private IAmazonManagementStreamRedisCountService amazonManagementStreamRedisCountService;

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    public void process(List<ManagementAdStreamMessage> messages) throws Exception {
        log.info("adproduct stream data, {}", JSON.toJSONString(messages));
        Set<String> sellerIds = new HashSet<>();
        Set<String> marketplaceIds = new HashSet<>();
        Map<String, List<ManagementAdStreamMessage>> collect = new HashMap<>();
        if (CollectionUtils.isEmpty(messages)) {
            log.info("ad stream ad message empty");
            return;
        }
        for (ManagementAdStreamMessage message : messages) {
            sellerIds.add(message.getAdvertiseId());
            marketplaceIds.add(message.getMarketplaceId());
            collect.computeIfAbsent(getKey(message), key -> new ArrayList<>()).add(message);
        }
        Date nowDate = new Date();
        LocalDateTime localDateTime = LocalDateTime.now();
        amazonManagementStreamLogService.printAllManagementStreamCount(MonitorTypeEnum.all, MonitorStreamTypeEnum.ad_product, nowDate, messages.size());
//        amazonManagementStreamRedisCountService.countAllAmazonManagementStreamHour(localDateTime, messages.size());
        List<ShopAuth> shopAuths = shopAuthDao.getBySellerIdsAndMarketplaceIds(Lists.newArrayList(sellerIds), Lists.newArrayList(marketplaceIds));
        if (CollectionUtils.isEmpty(shopAuths)) {
            log.info("ad stream not fund shopAuths,sellerId:{},marketplaceId:{}", StringUtils.join(sellerIds, ","), StringUtils.join(marketplaceIds, ","));
            return;
        }

        Map<String, ShopAuth> shopAuthMap = shopAuths.stream().collect(Collectors.toMap(this::getKey, Function.identity()));
        for (Map.Entry<String, List<ManagementAdStreamMessage>> entry : collect.entrySet()) {
            ShopAuth shopAuth = shopAuthMap.get(entry.getKey());
            if (shopAuth == null) {
                continue;
            }
            List<ManagementAdStreamMessage> managementAdStreamMessages = entry.getValue();
            amazonManagementStreamLogService.printPuidManagementStreamCount(shopAuth.getPuid(), shopAuth.getId(), MonitorTypeEnum.puid, MonitorStreamTypeEnum.ad_product, nowDate, managementAdStreamMessages.size());
//            List<String> adIds = new ArrayList<>();
            Map<String, List<ManagementAdStreamMessage>> stringListMap = managementAdStreamMessages.stream()
                    .collect(Collectors.groupingBy(ManagementAdStreamMessage::getAdProduct));
            //缺失字段比较多，广告活动层级数据量比较小，先尝试直接使同步的方式取回来
            //即使入库了也如需同步一遍活动服务状态，保持一直，可以考虑用异步；
            //查询profile和店铺逻辑可以考虑加缓存提供公共服务
            for (Map.Entry<String, List<ManagementAdStreamMessage>> e : stringListMap.entrySet()) {
                String k = e.getKey();
                List<ManagementAdStreamMessage> v = e.getValue();
                AdProductEnum adProductEnum = AdProductEnum.getAdProductEnum(k);
                if (adProductEnum == null) {
                    continue;
                }
                String ids = v.stream().map(ManagementAdStreamMessage::getAdId).collect(Collectors.joining(","));
                List<String> adIdList = v.stream().map(ManagementAdStreamMessage::getAdId).collect(Collectors.toList());
                if (AdProductEnum.SPONSORED_PRODUCTS == adProductEnum) {
                    try {
                        cpcAdProductApiService.syncAds(shopAuth, null, null, ids, null,true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
                        amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(localDateTime, adIdList.size());
                    } catch (Exception error) {
                        log.error("ad stream data, sp ad stream error", error);
                        managementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), AdProductEnum.SPONSORED_PRODUCTS, AmazonStreamTaskTypeEnum.AD, adIdList);
                    }
                } else if (AdProductEnum.SPONSORED_BRANDS == adProductEnum) {
                    try {
                        cpcSbAdsApiService.syncAds(shopAuth, null, ids, true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
                        amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(localDateTime, adIdList.size());
                    } catch (Exception error) {
                        log.error("ad stream data, sb ad stream error", error);
                        managementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), AdProductEnum.SPONSORED_BRANDS, AmazonStreamTaskTypeEnum.AD, adIdList);
                    }

                } else if (AdProductEnum.SPONSORED_DISPLAY == adProductEnum) {
                    try {
                        cpcSdProductApiService.syncProductAds(shopAuth, null, null, ids, null,true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
                        amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(localDateTime, adIdList.size());
                    } catch (Exception error) {
                        log.error("ad stream data, sd ad stream error", error);
                        managementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), AdProductEnum.SPONSORED_DISPLAY, AmazonStreamTaskTypeEnum.AD, adIdList);
                    }
                }
            }

        }
    }
    private String getKey(ManagementAdStreamMessage adStreamMessage) {
        return adStreamMessage.getAdvertiseId() + "#" + adStreamMessage.getMarketplaceId();
    }

    private String getKey(ShopAuth shopAuth) {
        return shopAuth.getSellingPartnerId() + "#" + shopAuth.getMarketplaceId();
    }
}
