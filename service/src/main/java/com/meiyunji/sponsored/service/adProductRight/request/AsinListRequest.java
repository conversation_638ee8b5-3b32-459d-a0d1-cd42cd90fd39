package com.meiyunji.sponsored.service.adProductRight.request;

import lombok.Data;

import java.util.List;

/**
 * 获取asin详情信息请求req
 * @Author: heqiwen
 * @Date: 2025/05/21 19:23
 */
@Data
public class AsinListRequest {
    /**
     * 店铺id
     */
    private List<Integer> shopIdList;

    private List<String> marketplaceIdList;
    /**
     * VC店铺或SC店铺
     */
    private String type;
    /**
     * 业务员id
     */
    private List<Integer> uidList;

    /**
     * 编辑页面：传编辑的子账号
     */
    private Integer uid;

    private Boolean isEdit;
    /**
     * 搜索内容
     */
    private String searchValue;

    private List<String> searchValueList;

    /**
     * 搜索方式：精确：exact 模糊：blur
     */
    private String searchType;
    /**
     * 搜索类型：asin（后续可迭代升级：parentAsin、msku）
     */
    private String searchField;

    /**
     * 创建时间
     */
    private String orderField;

    private String onlineStatus;

    /**
     * 排序方式
     */
    private String orderType;

    private Integer pageNo;

    private Integer pageSize;

}
