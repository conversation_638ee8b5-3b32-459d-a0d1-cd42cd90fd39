package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.base.Joiner;
import com.meiyunji.sponsored.common.springjdbc.BaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.UpdateBuilder;
import com.meiyunji.sponsored.service.cpc.dao.IKeywordDao;
import com.meiyunji.sponsored.service.cpc.po.Keyword;
import com.meiyunji.sponsored.service.cpc.vo.MonitorKeywordVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * @ClassName TrackProductDaoImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/8 10:55
 **/
@Repository
public class KeywordDaoImpl extends BaseDaoImpl<Keyword> implements IKeywordDao {

    @Override
    public Integer getCountByCondition(ConditionBuilder condition) {
        String sql = "SELECT count(1) FROM t_rank_monitor_keyword WHERE ";
        try {
            return this.getJdbcTemplate().queryForObject(sql + condition.getSql(), condition.getValues(), Integer.class);
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public List<Map<String,Object>> getCountByConditionIds(Integer puid, List<Integer> idsList) {
        String sql = "SELECT `mid`,count(`mid`) as `keywordCount` FROM t_rank_monitor_keyword WHERE puid = ? and `mid` in (" + Joiner.on(",").skipNulls().join(idsList) + ") GROUP BY `mid` ";
        try {
            return this.getJdbcTemplate().queryForList(sql,new Object[]{puid});
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Keyword> listByMonitorIdAndPuid(Integer monitorId, Integer puid) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("mid", monitorId)
                .equalTo("puid", puid)
                .build();
        return listByCondition(condition);
    }

    @Override
    public List<Keyword> listByMonitorIdAndPuidOrKeyword(Integer monitorId, Integer puid, String keyword) {
        ConditionBuilder.Builder condition = new ConditionBuilder.Builder();
        condition.equalTo("mid", monitorId);
        condition.equalTo("puid", puid);
        if (StringUtils.isNotBlank(keyword)) {
            condition.like("keyword", keyword);
        }

        return listByCondition(condition.build());
    }

    @Override
    public int deleteByMonitorIdAndPuid(Integer monitorId, Integer puid) {
        String sql = "delete from t_rank_monitor_keyword where mid =? and puid = ?";
        return this.getJdbcTemplate().update(sql, monitorId, puid);
    }

    @Override
    public Integer updateRankDataById(Keyword keyword) {
        String sql = "update t_rank_monitor_keyword set ranks = ?, page_num = ?, update_time = ?, crawl_time = ?, ad_ranks = ?, ad_page_num = ?, total_position = ?, ad_total_position = ?, status = 1 where id = ? and puid = ?";
        return this.getJdbcTemplate().update(sql, keyword.getRanks(), keyword.getPageNum(), keyword.getUpdateTime(), keyword.getCrawlTime(), keyword.getAdRanks(), keyword.getAdPageNum(), keyword.getTotalPosition(), keyword.getAdTotalPosition(), keyword.getId(), keyword.getPuid());
    }

    @Override
    public List<Keyword> listByMontors(List<Integer> monitorIds) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .in("mid", monitorIds.toArray())
                .build();
        return listByCondition(condition);
    }

    @Override
    public Keyword getFirstKeyword(Integer puid, Integer id) {

        String sql = "select * from t_rank_monitor_keyword where puid = ? and mid = ? order by ranks desc limit 1";
        //if(getJdbcTemplate().queryForObject(sql,new Object[]{puid,id},getMapper())==true)
        try {
            return getJdbcTemplate().queryForObject(sql, new Object[]{puid, id}, getMapper());
        } catch (DataAccessException e) {
            return null;
        }
    }

    @Override
    public int updateStatusByPuid(Integer puid, Long keywordId, Integer keyStatus) {
        String sql = "update t_rank_monitor_keyword set keyword_type = ? where id = ? and puid = ?";
        return getJdbcTemplate().update(sql, keyStatus, keywordId, puid);
    }

    @Override
    public int updateSomeStatus(Integer puid, List<Long> keywordIds, Integer status) {
        //       String sql = "update t_rank_monitor_keyword set keyword_type = ? where puid = ? and id in (?)";
        UpdateBuilder ub = new UpdateBuilder("t_rank_monitor_keyword");
        ub.forColumn("keyword_type", status);
        ub.where("puid", "=", puid);
        ub.andIn("id", keywordIds);
        System.out.println(ub.getQueryValues());
        return getJdbcTemplate().update(ub.toSql(), ub.getQueryValues());
        //return getJdbcTemplate().update(sql,status,puid,keywordIds.toArray());
    }

    @Override
    public List<Keyword> listByMonitorIdsAndPuid(List<Integer> monitorIds, Integer puid) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("mid",monitorIds.toArray())
                .orderByAsc("ranks")
                .build();

        return listByCondition(condition);
    }

    @Override
    public List<Keyword> listByMonitorIdsAndPuidAndKeyword(List<Integer> monitorIds, Integer puid, String keyword) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("mid",monitorIds.toArray())
                .equalTo("keyword", keyword)
                .build();
        return listByCondition(condition);
    }

    @Override
    public List<Keyword> listByKeywordIdsAndPuid(Set<Long> KeywordIds, Integer puid) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("id",KeywordIds.toArray())
                .orderBy("id")
                .build();
        return listByCondition(condition);
    }

    @Override
    public int updateComment(int puid, Long id, String remark, String remarkColor) {
        String sql = "update t_rank_monitor_keyword set remark = ?, remark_color = ?, update_time = now() " +
                "where id = ? and puid = ?";
        return getJdbcTemplate().update(sql,new Object[]{remark, remarkColor, id, puid});
    }

    /**
     * TODO 关联关键词库查询
     * 以puid,keywordText条件获取总数
     * @param puid
     * @param keywordTexts
     */
    @Override
    public List<MonitorKeywordVo> getCountByPuidAndKeywordText(Integer puid, List<String> keywordTexts) {

        StringBuilder sql = new StringBuilder("select puid, keyword, count(*) count from t_rank_monitor_keyword where puid = ? ");
        List<Object> args = new ArrayList<>(puid);
        args.add(puid);

        if (CollectionUtils.isNotEmpty(keywordTexts)) {
            sql.append(SqlStringUtil.dealInList("keyword", keywordTexts, args));
        }

        sql.append(" group by puid, keyword ");

        return getJdbcTemplate().query(sql.toString(), new RowMapper<MonitorKeywordVo>() {
            @Override
            public MonitorKeywordVo mapRow(ResultSet res, int i) throws SQLException {
                MonitorKeywordVo keywordVo = MonitorKeywordVo.builder()
                        .puid(res.getInt("puid"))
                        .keywordText(res.getString("keyword"))
                        .count(Optional.ofNullable(res.getInt("count")).orElse(0))
                        .build();
                return keywordVo;
            }
        }, args.stream().toArray());
    }
}
