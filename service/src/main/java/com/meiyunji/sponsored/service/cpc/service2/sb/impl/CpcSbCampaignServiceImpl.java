package com.meiyunji.sponsored.service.cpc.service2.sb.impl;

import com.alibaba.fastjson.JSONObject;
import com.amazon.advertising.sb.entity.campaign.V4SbCampaignResult;
import com.amazon.advertising.sb.entity.campaign.sbV4CampaignResult.success.SbV4CampaignInfo;
import com.amazon.advertising.sb.entity.campaign.sbV4CampaignResult.success.SbV4CampaignSuccessResult;
import com.amazon.advertising.sb.mode.campaigm.Campaign;
import com.amazon.advertising.sb.mode.campaigm.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.sb.campaign.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.service2.sb.*;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdSyncService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCommService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdCampaignAll;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.product.po.Product;
import com.meiyunji.sponsored.service.strategy.enums.BudgetValueEnum;
import com.meiyunji.sponsored.service.strategy.enums.SBBudgetTypeEnum;
import com.meiyunji.sponsored.service.strategy.enums.SBBudgetValueEnum;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import com.meiyunji.sponsored.service.util.Constant;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import com.meiyunji.sponsored.service.util.ZoneUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * Created by lm on 2021/7/30.
 */
@Service
@Slf4j
public class CpcSbCampaignServiceImpl implements ICpcSbCampaignService {

    @Autowired
    private IAmazonAdCampaignAllDao amazonSbAdCampaignDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private CpcSbCampaignApiService cpcSbCampaignApiService;
    @Autowired
    private ICpcAdSyncService cpcAdSyncService;
    @Autowired
    private IUserDao userDao;
    @Autowired
    private IAmazonAdCampaignAllReportDao amazonAdSbCampaignReportDao;
    @Autowired
    private CpcCommService cpcCommService;

    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private CpcShopDataService CpCShopDataService;

    @Autowired
    private IDorisService dorisService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;

    private ThreadPoolExecutor threadPool = ThreadPoolUtil.getSbNewCreateCampaignThreadPool();

    @Autowired
    private ICpcSbGroupService cpcSbGroupService;

    @Autowired
    private ICpcSbKeywordService cpcSbKeywordService;

    @Autowired
    private ICpcSbTargetService cpcSbTargetService;

    @Autowired
    private ICpcSbNeKeywordService cpcSbNeKeywordService;

    @Autowired
    private ICpcSbNeTargetService cpcSbNeTargetService;

    @Autowired
    private CpcSbAdsApiService cpcSbAdsApiService;

    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;

    @Autowired
    private IAmazonSbAdsDao amazonSbAdsDao;

    @Override
    public List<CampaignPageVo> getList(int puid, CampaignPageParam param) {
        long t = Instant.now().toEpochMilli();
        List<AmazonAdCampaignAll> poList = amazonSbAdCampaignDao.getList(puid, param);
        log.info("广告管理--广告活动接口调用-获取sb广告管理- 花费时间 {} ,params: {}", (Instant.now().toEpochMilli() - t), JSONUtil.objectToJson(param));

        List<CampaignPageVo> voList = new ArrayList<>(poList.size());
        if (CollectionUtils.isNotEmpty(poList)) {
            List<String> portfolioIds = poList.stream().filter(p -> p.getPortfolioId() != null).map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());

            Map<String, AmazonAdPortfolio> portfolioMap = null;
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                portfolioMap = portfolioDao.getPortfolioList(puid, param.getShopId(), portfolioIds).stream()
                        .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
            }


            Map<Integer, User> userMap = userDao.listByPuid(puid).stream().collect(Collectors.toMap(User::getId, e -> e));
            long t1 = Instant.now().toEpochMilli();
            // 按活动分组获取活动的汇总数据
            Map<String, AmazonAdCampaignAllReport> campaignReportMap = Maps.newHashMapWithExpectedSize(2);

            campaignReportMap = amazonAdSbCampaignReportDao.listSumReports(puid, param.getShopId(), null, param.getStartDate(), param.getEndDate(),
                            poList.stream().map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList())).stream()
                    .collect(Collectors.toMap(AmazonAdCampaignAllReport::getCampaignId, e -> e));

            log.info("广告管理--广告活动接口调用-按campaignId获取sb广告活动报告- 花费时间 {} ,params: {}", (Instant.now().toEpochMilli() - t1), JSONUtil.objectToJson(param));

            long t2 = Instant.now().toEpochMilli();
            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();
            if (param.getShopSales() != null) {  // 最外层查了一次了
                shopSaleDto.setSumRange(param.getShopSales());
            }
            Map<String, Long> outOfBudgetTime = new HashMap<>();
            CampaignPageVo vo;
            for (AmazonAdCampaignAll amazonAdCampaign : poList) {
                vo = new CampaignPageVo();
                convertPoToPageVo(amazonAdCampaign, vo, outOfBudgetTime);

                if (StringUtils.isNotBlank(amazonAdCampaign.getPortfolioId())) {
                    if (portfolioMap != null && portfolioMap.containsKey(amazonAdCampaign.getPortfolioId())) {
                        vo.setPortfolioName(portfolioMap.get(amazonAdCampaign.getPortfolioId()).getName());
                    } else {
                        vo.setPortfolioName("广告组合待同步");
                    }
                } else {
                    vo.setPortfolioName("-");
                }

                // 创建人
                if (userMap.containsKey(amazonAdCampaign.getCreateId())) {
                    User user = userMap.get(amazonAdCampaign.getCreateId());
                    if (StringUtils.isNotBlank(user.getNickname())) {
                        vo.setCreator(user.getNickname());
                    }
                }
                voList.add(vo);
                // 填充报告数据
                if (campaignReportMap.get(amazonAdCampaign.getCampaignId()) != null) {
                    cpcCommService.fillReportDataIntoPageVo(vo, campaignReportMap.get(amazonAdCampaign.getCampaignId()).getReportBase(), shopSaleDto);
                }
            }

            if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索,需要过滤
                cpcCommService.filterCampaignAdvanceData(voList, param);
            }
            log.info("广告管理--广告活动接口调用-广告活动填充报告数据等- 花费时间 {} ,params: {}", (Instant.now().toEpochMilli() - t2), JSONUtil.objectToJson(param));
        }

        return voList;
    }

    @Override
    public Result showCampaignPerformance(int puid, AdPerformanceParam param) {
        if (param.getShopId() == null || StringUtils.isBlank(param.getCampaignId())) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdCampaignAll amazonAdCampaign = amazonSbAdCampaignDao.getByCampaignId(puid, param.getShopId(), null, param.getCampaignId(), CampaignTypeEnum.sb.getCampaignType());
        if (amazonAdCampaign == null) {
            return ResultUtil.returnErr("没有活动信息");
        }

        // 拼装返回的数据VO
        AdPerformanceVo adPerformanceVo = new AdPerformanceVo();
        adPerformanceVo.setShopId(amazonAdCampaign.getShopId());
        adPerformanceVo.setCampaignId(amazonAdCampaign.getCampaignId());

        // 初始化每天数据，应前端要求保证日期是连续的
        Map<String, CpcCommPageVo> map = new LinkedHashMap<>();
        adPerformanceVo.setMap(map);
        LocalDate startLocalDate = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        LocalDate endLocalDate = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        while (startLocalDate.equals(endLocalDate) || startLocalDate.isBefore(endLocalDate)) {
            map.put(startLocalDate.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)), new CpcCommPageVo());
            startLocalDate = startLocalDate.plusDays(1);
        }

        List<AmazonAdCampaignAllReport> reports = null;

        reports = amazonAdSbCampaignReportDao.listReports(puid, param.getShopId(),
                param.getStartDate(), param.getEndDate(), param.getCampaignId(), CampaignTypeEnum.sb.getCampaignType());

        if (CollectionUtils.isNotEmpty(reports)) {
            // 取店铺销售额
            String start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            String end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            BigDecimal shopSalesByDate = CpCShopDataService.getShopSalesByDate(param.getShopId(), param.getStartDate(), param.getEndDate());
            ShopSaleDto shopSaleDto = new ShopSaleDto();
            if (shopSalesByDate != null) {
                shopSaleDto.setSumRange(shopSalesByDate);
            }

            Map<String, CpcCommPageVo> resultMap = reports.stream().collect(Collectors.toMap(
                    e -> LocalDate.parse(e.getCountDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)),
                    e -> {

                        CpcCommPageVo campaignPageVo = new CpcCommPageVo();
                        e.setSaleNum(e.getOrderNum());
                        e.setAdSaleNum(e.getAdOrderNum());
                        // 填充报告数据
                        cpcCommService.fillReportDataIntoPageVo(campaignPageVo, e, shopSaleDto);
                        return campaignPageVo;

                    }, (p1, p2) -> p1));

            adPerformanceVo.getMap().putAll(resultMap);
        }

        return ResultUtil.returnSucc(adPerformanceVo);
    }

    @Override
    public Result<String> createCampaign(SbCampaignVo vo) throws InterruptedException {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();

        //判断活动名称是否存在
        if (amazonSbAdCampaignDao.exist(puid, shopId, vo.getName(), CampaignTypeEnum.sb.getCampaignType())) {
            return ResultUtil.returnErr("名称已存在");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        // 旧接口方法create(),v4方法为v4Create()
        Result result = cpcSbCampaignApiService.v4Create(shop, profile, vo);

        if (!result.success()) {
            return ResultUtil.returnErr(result.getMsg());
        }

        V4SbCampaignResult campaignResult = (V4SbCampaignResult) result.getData();
        SbV4CampaignSuccessResult successResult = campaignResult.getCampaigns().getSuccessResultList().get(0);
        String campaignId = successResult.getCampaignId();

        String errMsg = null;
        // 入库
        try {
            errMsg = save(vo, successResult, profile);
        } catch (Exception e) {
            log.error("createCampaign save fail:", e);
        }

        // 创建成功 同步这个活动
        cpcAdSyncService.asyncSbByShop(shop, campaignId, null);

        if (StringUtils.isNotBlank(errMsg)) {
            return ResultUtil.returnErr(errMsg);
        }
        return ResultUtil.returnSucc(campaignId);
    }

    @Override
    public NewCreateResultResultVo createCampaign(SbCampaignVo vo, ShopAuth shop, AmazonAdProfile profile) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();

        //判断活动名称是否存在
        if (amazonSbAdCampaignDao.exist(puid, shopId, vo.getName(), CampaignTypeEnum.sb.getCampaignType())) {
            throw new ServiceException(SBCreateErrorEnum.CAMPAIGN_NAME_EXISTS.getMsg());
        }

        // 旧接口方法create(),v4方法为v4Create()
        Result result = cpcSbCampaignApiService.v4Create(shop, profile, vo);

        if (!result.success()) {
            throw new ServiceException(result.getMsg());
        }

        V4SbCampaignResult campaignResult = (V4SbCampaignResult) result.getData();
        SbV4CampaignSuccessResult successResult = campaignResult.getCampaigns().getSuccessResultList().get(0);
        String campaignId = successResult.getCampaignId();

        String errMsg;
        // 入库
        try {
            errMsg = save(vo, successResult, profile);
        } catch (Exception e) {
            log.error("createCampaign save fail:", e);
            throw new ServiceException(SBCreateErrorEnum.CAMPAIGN_SAVE_FAIL.getMsg());
        }

        ThreadPoolUtil.getCampaignCreateSyncPool().execute(() -> {
            try {
                // 创建成功 同步这个活动
                cpcAdSyncService.asyncSbByShop(shop, campaignId, null);
            } catch (Exception e) {
                log.info("添加成功后同步广告活动异常", e);
                throw new ServiceException(e.getMessage());
            }
        });

        if (StringUtils.isNotBlank(errMsg)) {
            throw new ServiceException(errMsg);
        }
        return NewCreateResultResultVo.builder()
                .campaignId(campaignId)
                .build();
    }

    @Override
    public NewCreateInfoResponse submitTogetherCreateCampaign(NewCreateCampaignRequest request) {
        NewCreateInfoResponse.Builder responseInfo = NewCreateInfoResponse.newBuilder();
        int shopId = request.getShopId();
        int puid = request.getPuid();
        responseInfo.setShopId(shopId);
        //拼装各service需要使用的参数vo
        //组装广告活动参数vo
        SbCampaignVo campaignVo = buildCampaignVo(request);
        //组装广告组vo
        SbAdGroupVo groupVo = buildGroupVo(request);

        //根据各层级id，判断各层级是新建，还是重新提交
        //入库
        //同步创建各层级，只要出现异常，立即返回，投放及否定投放层级放在最后进行创建
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            responseInfo.setCommonErrMsg(SBCreateErrorEnum.NOT_BEEN_AUTHORIZED.getMsg());
            return responseInfo.build();
        }
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            responseInfo.setCommonErrMsg(SBCreateErrorEnum.NOT_HAVE_PROFILE.getMsg());
            return responseInfo.build();
        }

        //如果campaignId不存在，直接从活动层级开始创建
        if (StringUtils.isEmpty(request.getCampaignId())) {
            responseInfo = createCampaignStep(campaignVo, shop, profile, responseInfo);
            if (StringUtils.isNotEmpty(responseInfo.getCampaignResponse().getCampaignErrMsg())) {
                return responseInfo.build();
            }
            responseInfo = createGroupStep(groupVo, shop, profile, responseInfo, responseInfo.getCampaignResponse().getCampaignId());
            if (StringUtils.isNotEmpty(responseInfo.getGroupResponse().getGroupErrMsg())) {
                return responseInfo.build();
            }
            responseInfo = createAdsStep(request, shop, profile, responseInfo, responseInfo.getGroupResponse().getAdGroupId());
            if (StringUtils.isNotEmpty(responseInfo.getAdsResponse().getAdErrMsg())) {
                return responseInfo.build();
            }
            //异步创建投放和否投
            createTargetAndNeTargetAsync(responseInfo, request, shop, profile);

            return responseInfo.build();
        }

        //广告活动已创建，从广告组开始创建
        if (StringUtils.isNotEmpty(request.getCampaignId()) && StringUtils.isEmpty(request.getAdGroupId())) {
            //check campaignId
            List<AmazonAdCampaignAll> campaignInfoList = amazonSbAdCampaignDao.getAllStateSbAsinByCampaignId(puid, shop.getId(), request.getCampaignId());
            if (CollectionUtils.isEmpty(campaignInfoList)) {
                CampaignResponse.Builder campaignResBuilder = CampaignResponse.newBuilder();
                campaignResBuilder.setCampaignErrMsg(SBCommonErrorVo.getErrorListByRaw(SBCreateErrorEnum.CAMPAIGN_NOT_EXIST.getMsg()));
                responseInfo.setCampaignResponse(campaignResBuilder.build());
                return responseInfo.build();
            } else {
                AmazonAdCampaignAll amazonAdCampaignAll = campaignInfoList.get(0);
                if (CpcStatusEnum.archived.name().equalsIgnoreCase(amazonAdCampaignAll.getState())) {
                    CampaignResponse.Builder campaignResBuilder = CampaignResponse.newBuilder();
                    campaignResBuilder.setCampaignErrMsg(SBCommonErrorVo.getErrorListByRaw(SBCreateErrorEnum.ARCHIVED_ENTITY_CANNOT_MODIFIED.getMsg()));
                    responseInfo.setCampaignResponse(campaignResBuilder.build());
                    return responseInfo.build();
                }
                //set campaign info
                fillCampaignInfo(responseInfo, amazonAdCampaignAll);
            }

            responseInfo = createGroupStep(groupVo, shop, profile, responseInfo, request.getCampaignId());
            if (StringUtils.isNotEmpty(responseInfo.getGroupResponse().getGroupErrMsg())) {
                return responseInfo.build();
            }
            responseInfo = createAdsStep(request, shop, profile, responseInfo, responseInfo.getGroupResponse().getAdGroupId());
            if (StringUtils.isNotEmpty(responseInfo.getAdsResponse().getAdErrMsg())) {
                return responseInfo.build();
            }
            //异步创建投放和否投
            createTargetAndNeTargetAsync(responseInfo, request, shop, profile);
            return responseInfo.build();
        }

        //广告活动和广告组都已存在，从广告格式开始创建
        if (StringUtils.isNotEmpty(request.getCampaignId()) && StringUtils.isNotEmpty(request.getAdGroupId())
                && StringUtils.isEmpty(request.getAdId()) && StringUtils.isEmpty(request.getTargetId()) && StringUtils.isEmpty(request.getNeTargetId())) {

            //check campaignId
            List<AmazonAdCampaignAll> campaignInfoList = amazonSbAdCampaignDao.getAllStateSbAsinByCampaignId(puid, shop.getId(), request.getCampaignId());
            if (CollectionUtils.isEmpty(campaignInfoList)) {
                CampaignResponse.Builder campaignResBuilder = CampaignResponse.newBuilder();
                campaignResBuilder.setCampaignErrMsg(SBCommonErrorVo.getErrorListByRaw(SBCreateErrorEnum.CAMPAIGN_NOT_EXIST.getMsg()));
                responseInfo.setCampaignResponse(campaignResBuilder.build());
                return responseInfo.build();
            } else {
                AmazonAdCampaignAll amazonAdCampaignAll = campaignInfoList.get(0);
                if (CpcStatusEnum.archived.name().equalsIgnoreCase(amazonAdCampaignAll.getState())) {
                    CampaignResponse.Builder campaignResBuilder = CampaignResponse.newBuilder();
                    campaignResBuilder.setCampaignErrMsg(SBCommonErrorVo.getErrorListByRaw(SBCreateErrorEnum.ARCHIVED_ENTITY_CANNOT_MODIFIED.getMsg()));
                    responseInfo.setCampaignResponse(campaignResBuilder.build());
                    return responseInfo.build();
                }
                //set campaign info
                fillCampaignInfo(responseInfo, amazonAdCampaignAll);
            }

            //check adGroupId
            AmazonSbAdGroup groupInfo = amazonSbAdGroupDao.getByGroupId(puid, shop.getId(), request.getAdGroupId());
            if (Objects.isNull(groupInfo)) {
                GroupResponse.Builder groupResBuilder = GroupResponse.newBuilder();
                groupResBuilder.setGroupErrMsg(SBCommonErrorVo.getErrorListByRaw(SBCreateErrorEnum.GROUP_NOT_EXIST.getMsg()));
                responseInfo.setGroupResponse(groupResBuilder.build());
                return responseInfo.build();
            }
            fillAdGroupInfo(responseInfo, groupInfo);

            responseInfo = createAdsStep(request, shop, profile, responseInfo, request.getAdGroupId());
            if (StringUtils.isNotEmpty(responseInfo.getAdsResponse().getAdErrMsg())) {
                return responseInfo.build();
            }
            //异步创建投放和否投
            createTargetAndNeTargetAsync(responseInfo, request, shop, profile);
            return responseInfo.build();
        }

        //广告活动，广告组，广告格式都已经存在，创建投放
        if (StringUtils.isNotEmpty(request.getCampaignId()) && StringUtils.isNotEmpty(request.getAdGroupId())
                && StringUtils.isNotEmpty(request.getAdId())) {
            //check campaignId
            List<AmazonAdCampaignAll> campaignInfoList = amazonSbAdCampaignDao.getAllStateSbAsinByCampaignId(puid, shop.getId(), request.getCampaignId());
            if (CollectionUtils.isEmpty(campaignInfoList)) {
                CampaignResponse.Builder campaignResBuilder = CampaignResponse.newBuilder();
                campaignResBuilder.setCampaignErrMsg(SBCommonErrorVo.getErrorListByRaw(SBCreateErrorEnum.CAMPAIGN_NOT_EXIST.getMsg()));
                responseInfo.setCampaignResponse(campaignResBuilder.build());
                return responseInfo.build();
            } else {
                AmazonAdCampaignAll amazonAdCampaignAll = campaignInfoList.get(0);
                if (CpcStatusEnum.archived.name().equalsIgnoreCase(amazonAdCampaignAll.getState())) {
                    CampaignResponse.Builder campaignResBuilder = CampaignResponse.newBuilder();
                    campaignResBuilder.setCampaignErrMsg(SBCommonErrorVo.getErrorListByRaw(SBCreateErrorEnum.ARCHIVED_ENTITY_CANNOT_MODIFIED.getMsg()));
                    responseInfo.setCampaignResponse(campaignResBuilder.build());
                    return responseInfo.build();
                }
                //set campaign info
                fillCampaignInfo(responseInfo, amazonAdCampaignAll);
            }

            //check adGroupId
            AmazonSbAdGroup groupInfo = amazonSbAdGroupDao.getByGroupId(puid, shop.getId(), request.getAdGroupId());
            if (Objects.isNull(groupInfo)) {
                GroupResponse.Builder groupResBuilder = GroupResponse.newBuilder();
                groupResBuilder.setGroupErrMsg(SBCommonErrorVo.getErrorListByRaw(SBCreateErrorEnum.GROUP_NOT_EXIST.getMsg()));
                responseInfo.setGroupResponse(groupResBuilder.build());
                return responseInfo.build();
            }
            fillAdGroupInfo(responseInfo, groupInfo);

            //check adid
            AmazonSbAds adInfo = amazonSbAdsDao.getAdsByAdId(puid, shop.getId(), request.getAdId());
            if (Objects.isNull(adInfo)) {
                AdsResponse.Builder adsResBuilder = AdsResponse.newBuilder();
                adsResBuilder.setAdErrMsg(SBCommonErrorVo.getErrorListByRaw(SBCreateErrorEnum.ADS_NOT_EXIST.getMsg()));
                responseInfo.setAdsResponse(adsResBuilder.build());
                return responseInfo.build();
            }
            fillAdsInfo(responseInfo, adInfo);

            //异步创建投放和否投
            createTargetAndNeTargetAsync(responseInfo, request, shop, profile);
            return responseInfo.build();
        }

        return responseInfo.build();
    }

    @Override
    public AdsResponse submitAddAdsCreative(AddAdsCreativeRequest request, ShopAuth shop, AmazonAdProfile profile) {
        AdsResponse.Builder adsBuilder = AdsResponse.newBuilder();
        try {
            SbAdVo sbAdVo = buildAdsVo(request);
            adsBuilder.setAdId(cpcSbAdsApiService.createSbAdNew(shop, profile, sbAdVo).getAdId());
        } catch (Exception e) {
            adsBuilder.setAdErrMsg(SBCommonErrorVo.getErrorListByRaw(e.getMessage()));
        }
        return adsBuilder.build();
    }
    private SbAdVo buildAdsVo(AddAdsCreativeRequest request) {
        SbAdVo sbAdVo = new SbAdVo();
        sbAdVo.setAdGroupId(request.getAdGroupId());
        Optional.of(request.getAdFormat()).map(SBAdFormatEnum::getSBAdFormatEnumByCode)
            .map(SBAdFormatEnum::getValue).ifPresent(sbAdVo::setAdFormat);
        sbAdVo.setName(request.getAdsName());
        sbAdVo.setState(request.getAdsState());
        sbAdVo.setLandingPage(request.getLandingPageVo());
        sbAdVo.setCreative(request.getCreativeVo());
        return sbAdVo;
    }

    @Override
    public TargetResponse submitAddTarget(AddTargetRequest request, ShopAuth shop, AmazonAdProfile profile) {
        TargetResponse.Builder targetResponse = TargetResponse.newBuilder();
        try {
            NewCreateCampaignRequest newCreateCampaignRequest = buildNewCreateCampaignRequest(request);
            targetResponse = createTargetStep(newCreateCampaignRequest, shop, profile, request.getCampaignId(), request.getAdGroupId());
        } catch (Exception e) {
            targetResponse.setTargetErrMsg(e.getMessage());
        }
        return targetResponse.build();
    }
    private NewCreateCampaignRequest buildNewCreateCampaignRequest(AddTargetRequest request) {
        NewCreateCampaignRequest.Builder builder = NewCreateCampaignRequest.newBuilder();
        //todo:测试下如果为null是什么样的场景
        builder.setPuid(request.getPuid());
        builder.setUid(request.getUid());
        builder.setShopId(request.getShopId());
        builder.setCampaignId(request.getCampaignId());
        builder.setAdGroupId(request.getAdGroupId());
        builder.setAdFormat(request.getAdFormat());
        builder.addAllKeywordsVo(request.getKeywordsVoList());
        builder.addAllThemesVo(request.getThemesVoList());
        builder.addAllTargetsVo(request.getTargetsVoList());
        return builder.build();
    }

    private NewCreateInfoResponse.Builder createCampaignStep(SbCampaignVo campaignVo, ShopAuth shop, AmazonAdProfile profile, NewCreateInfoResponse.Builder responseInfo) {
        //这里得到的结果是广告活动的创建结果
        NewCreateResultResultVo campaignResult;
        CampaignResponse.Builder campaignResBuilder = CampaignResponse.newBuilder();
        try {
            campaignResult = this.createCampaign(campaignVo, shop, profile);
            campaignResBuilder.setCampaignId(campaignResult.getCampaignId());
            responseInfo.setCampaignResponse(campaignResBuilder.build());
        } catch (Exception e) {
            campaignResBuilder.setCampaignErrMsg(SBCommonErrorVo.getErrorListByRaw(e.getMessage()));
            responseInfo.setCampaignResponse(campaignResBuilder.build());
            return responseInfo;
        }
        return responseInfo;
    }

    private NewCreateInfoResponse.Builder createGroupStep(SbAdGroupVo groupVo, ShopAuth shop,
                                                          AmazonAdProfile profile, NewCreateInfoResponse.Builder responseInfo,
                                                          String campaignId) {
        NewCreateResultResultVo groupResult;
        GroupResponse.Builder groupResBuilder = GroupResponse.newBuilder();
        try {
            groupVo.setCampaignId(campaignId);
            groupResult = cpcSbGroupService.createGroup(groupVo, shop, profile);
            groupResBuilder.setAdGroupId(groupResult.getAdGroupId());
            responseInfo.setGroupResponse(groupResBuilder.build());
        } catch (Exception e) {
            groupResBuilder.setGroupErrMsg(e.getMessage());
            responseInfo.setGroupResponse(groupResBuilder.build());
            return responseInfo;
        }
        return responseInfo;
    }

    private NewCreateInfoResponse.Builder createAdsStep(NewCreateCampaignRequest request, ShopAuth shop,
                                                        AmazonAdProfile profile, NewCreateInfoResponse.Builder responseInfo,
                                                        String adGroupId) {
        NewCreateResultResultVo adsResult;
        AdsResponse.Builder adsBuilder = AdsResponse.newBuilder();
        try {
            SbAdVo sbAdVo = buildAdsVo(request);
            sbAdVo.setAdGroupId(adGroupId);
            adsResult = cpcSbAdsApiService.createSbAdNew(shop, profile, sbAdVo);
            adsBuilder.setAdId(adsResult.getAdId());
            responseInfo.setAdsResponse(adsBuilder.build());
        } catch (Exception e) {
            adsBuilder.setAdErrMsg(SBCommonErrorVo.getErrorListByRaw(e.getMessage()));
            responseInfo.setAdsResponse(adsBuilder.build());
            return responseInfo;
        }
        return responseInfo;
    }

    private TargetResponse.Builder createTargetStep(NewCreateCampaignRequest request, ShopAuth shop,
                                                    AmazonAdProfile profile,String campaignId,
                                                    String adGroupId) {
        TargetResponse.Builder targetBuilder = TargetResponse.newBuilder();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(request.getKeywordsVoList())) {
            //创建关键词投放
            try {
                NewCreateResultResultVo<SBCommonErrorVo> keywordResult = this.submitTogetherKeyword(request, campaignId, adGroupId, shop, profile);
                if (Objects.nonNull(keywordResult) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(keywordResult.getKeywordList())) {
                    List<TargetInfoResponse> keywordList = keywordResult.getKeywordList().stream().map(k -> {
                        TargetInfoResponse.Builder t = TargetInfoResponse.newBuilder();
                        Optional.ofNullable(k.getTargetId()).ifPresent(t::setTargetId);
                        Optional.ofNullable(k.getTargetText()).ifPresent(t::setTargetText);
                        Optional.ofNullable(k.getMatchType()).ifPresent(t::setMatchType);
                        return t.build();
                    }).collect(Collectors.toList());
                    targetBuilder.addAllTargetList(keywordList);
                }
                if (Objects.nonNull(keywordResult) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(keywordResult.getErrInfoList())) {
                    targetBuilder.setTargetErrMsg(JSONObject.toJSONString(keywordResult.getErrInfoList()));
                }
            } catch (Exception e) {
                log.error("get response vo error:{}", e.getMessage());
                targetBuilder.setTargetErrMsg(e.getMessage());
            }
        }

        //主题投放在亚马逊被作为一种独立的投放类型，但是其报告是与关键词报告在一起，同时主题投放基础数据也保存在关键词表中,
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(request.getThemesVoList())) {
            if (request.getThemesVoList().size() > 2) {
                throw new ServiceException(SBCreateErrorEnum.THEME_TYPE_CREATE_LIMIT.getMsg());
            }
            //创建主题投放
            try {
                NewCreateResultResultVo<SBCommonErrorVo> themesResult = this.submitTogetherTheme(request, campaignId, adGroupId, shop, profile);
                if (Objects.nonNull(themesResult) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(themesResult.getThemesList())) {
                    List<ThemesInfoResponse> themesList = themesResult.getThemesList().stream().map(k -> {
                        ThemesInfoResponse.Builder t = ThemesInfoResponse.newBuilder();
                        Optional.ofNullable(k.getThemesId()).ifPresent(t::setThemesId);
                        Optional.ofNullable(k.getThemesText()).ifPresent(t::setThemesText);
                        return t.build();
                    }).collect(Collectors.toList());
                    targetBuilder.addAllThemesList(themesList);
                }
                if (Objects.nonNull(themesResult) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(themesResult.getErrInfoList())) {
                    if (StringUtils.isNotEmpty(targetBuilder.getTargetErrMsg())) {
                        List<SBCommonErrorVo> targetErrorList;
                        try {
                            targetErrorList = JSONObject.parseArray(targetBuilder.getTargetErrMsg(), SBCommonErrorVo.class);
                        } catch (Exception ex) {
                            log.error("parse target error info error:{}, keywordError:{}, themesError:{}", ex.getMessage(), targetBuilder.getTargetErrMsg(), themesResult.getErrInfoList());
                            throw new RuntimeException(ex);
                        }
                        targetErrorList.addAll(themesResult.getErrInfoList());
                        targetBuilder.setTargetErrMsg(JSONObject.toJSONString(targetErrorList));
                    } else {
                        targetBuilder.setTargetErrMsg(JSONObject.toJSONString(themesResult.getErrInfoList()));
                    }
                }
            } catch (Exception e) {
                if (StringUtils.isNotEmpty(targetBuilder.getTargetErrMsg())) {
                    List<SBCommonErrorVo> targetErrorList;
                    List<SBCommonErrorVo> themesErrorList;
                    try {
                        targetErrorList = JSONObject.parseArray(targetBuilder.getTargetErrMsg(), SBCommonErrorVo.class);
                        themesErrorList = JSONObject.parseArray(e.getMessage(), SBCommonErrorVo.class);
                    } catch (Exception ex) {
                        log.error("parse target error info error:{}, keywordError:{}, themesError:{}", e.getMessage(), targetBuilder.getTargetErrMsg(), e.getMessage());
                        throw new RuntimeException(ex);
                    }
                    targetErrorList.addAll(themesErrorList);
                    targetBuilder.setTargetErrMsg(JSONObject.toJSONString(targetErrorList));
                } else {
                    targetBuilder.setTargetErrMsg(SBCommonErrorVo.getErrorListByRaw(e.getMessage()));
                }
            }
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(request.getTargetsVoList())) {
            //创建商品投放和广告格式
            //创建关键词投放
            try {
                NewCreateResultResultVo<SBCommonErrorVo> targetingResult = this.submitTogetherTargeting(request, campaignId, adGroupId, shop, profile);
                if (Objects.nonNull(targetingResult) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(targetingResult.getTargetingList())) {
                        List<TargetInfoResponse> targetingList = targetingResult.getTargetingList().stream().map(k -> {
                            TargetInfoResponse.Builder t = TargetInfoResponse.newBuilder();
                            Optional.ofNullable(k.getTargetId()).ifPresent(t::setTargetId);
                            Optional.ofNullable(k.getTargetText()).ifPresent(t::setTargetText);
                            Optional.ofNullable(k.getMatchType()).ifPresent(t::setMatchType);
                            return t.build();
                        }).collect(Collectors.toList());
                        targetBuilder.addAllTargetList(targetingList);
                }
                if (Objects.nonNull(targetingResult) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(targetingResult.getErrInfoList())) {
                    targetBuilder.setTargetErrMsg(JSONObject.toJSONString(targetingResult.getErrInfoList()));
                }
            } catch (Exception e) {
                targetBuilder.setTargetErrMsg(SBCommonErrorVo.getErrorListByRaw(e.getMessage()));
                return targetBuilder;
            }
        }
        return targetBuilder;
    }

    private NeTargetResponse.Builder createNeTargetStep(NewCreateCampaignRequest request, ShopAuth shop,
                                                             AmazonAdProfile profile, String campaignId,
                                                             String adGroupId) {
        NeTargetResponse.Builder neTargetBuilder = NeTargetResponse.newBuilder();

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(request.getNeKeywordVoList())) {
            //创建关键词否定投放
            try {
                NewCreateResultResultVo<SBCommonErrorVo> neKeywordResult = this.submitTogetherNeKeyword(request, campaignId, adGroupId, shop, profile);
                if (Objects.nonNull(neKeywordResult) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(neKeywordResult.getNeKeywordIdList())) {
                    List<NeTargetInfoResponse> neKeywordResultList = neKeywordResult.getNeKeywordIdList().stream().map(k -> {
                        NeTargetInfoResponse.Builder neKeyword = NeTargetInfoResponse.newBuilder();
                        Optional.ofNullable(k.getNeTargetId()).ifPresent(neKeyword::setNeTargetId);
                        Optional.ofNullable(k.getNeTargetText()).ifPresent(neKeyword::setNeTargetText);
                        Optional.ofNullable(k.getNeMatchType()).ifPresent(neKeyword::setNeMatchType);
                        return neKeyword.build();
                    }).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(neKeywordResultList)) neTargetBuilder.addAllNeTargetList(neKeywordResultList);
                }
                if (Objects.nonNull(neKeywordResult) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(neKeywordResult.getErrInfoList())) {
                    neTargetBuilder.setNeTargetErrMsg(JSONObject.toJSONString(neKeywordResult.getErrInfoList()));
                }
            } catch (Exception e) {
                neTargetBuilder.setNeTargetErrMsg(SBCommonErrorVo.getErrorListByRaw(e.getMessage()));
                return neTargetBuilder;
            }
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(request.getNeTargetsVoList())) {
            //创建商品否定投放
            //创建关键词否定投放
            try {
                NewCreateResultResultVo<SBCommonErrorVo> neTargetingResult = this.submitTogetherNeTargeting(request, campaignId, adGroupId, shop, profile);
                if (Objects.nonNull(neTargetingResult) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(neTargetingResult.getNeTargetingIdList())) {
                    List<NeTargetInfoResponse> neTargetResultList = neTargetingResult.getNeTargetingIdList().stream().map(t -> {
                        NeTargetInfoResponse.Builder neKeyword = NeTargetInfoResponse.newBuilder();
                        Optional.ofNullable(t.getNeTargetId()).ifPresent(neKeyword::setNeTargetId);
                        Optional.ofNullable(t.getNeTargetText()).ifPresent(neKeyword::setNeTargetText);
                        Optional.ofNullable(t.getNeMatchType()).ifPresent(neKeyword::setNeMatchType);
                        return neKeyword.build();
                    }).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(neTargetResultList)) neTargetBuilder.addAllNeTargetList(neTargetResultList);
                }
                if (Objects.nonNull(neTargetingResult) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(neTargetingResult.getErrInfoList())) {
                    neTargetBuilder.setNeTargetErrMsg(JSONObject.toJSONString(neTargetingResult.getErrInfoList()));
                }
            } catch (Exception e) {
                neTargetBuilder.setNeTargetErrMsg(SBCommonErrorVo.getErrorListByRaw(e.getMessage()));
                return neTargetBuilder;
            }
        }
        return neTargetBuilder;
    }

    @Override
    public Result verifyName(Integer puid, Integer shopId, String name) {
        //判断活动名称是否存在
        if (amazonSbAdCampaignDao.exist(puid, shopId, name.trim(), CampaignTypeEnum.sb.getCampaignType())) {
            return ResultUtil.error("名称已存在");
        }
        return ResultUtil.success();
    }

    @Override
    public Result updateCampaign(SbUpdateCampaignVo vo) throws InterruptedException {
        int puid = vo.getPuid();
        Integer shopId = vo.getShopId();
        String campaignId = vo.getCampaignId();

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.error("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.error("没有站点对应的配置信息");
        }

        AmazonAdCampaignAll campaign = amazonSbAdCampaignDao.getByCampaignId(puid, shopId, null, campaignId, CampaignTypeEnum.sb.getCampaignType());
        if (campaign == null) {
            return ResultUtil.error("没有活动信息");
        }

        AmazonAdCampaignAll oldCampaign = new AmazonAdCampaignAll();
        BeanUtils.copyProperties(campaign, oldCampaign);

        List<Campaign> campaignList = new ArrayList<>();
        Campaign cam = new Campaign();
        cam.setCampaignId(Long.valueOf(campaignId));
        if (StringUtils.isNotBlank(vo.getName())) {
            //判断活动名称是否存在
            if (!campaign.getName().equals(vo.getName()) && amazonSbAdCampaignDao.exist(puid, shopId, vo.getName().trim(), CampaignTypeEnum.sb.getCampaignType())) {
                return ResultUtil.error("名称已存在");
            }
            cam.setName(vo.getName());
            campaign.setName(vo.getName());
        }
        if (StringUtils.isNotBlank(vo.getState())) {
            cam.setState(vo.getState());
            campaign.setState(vo.getState());
        }
        if (vo.getBudget() != null) {
            cam.setBudget(Double.valueOf(vo.getBudget()));
            campaign.setBudget(new BigDecimal(vo.getBudget()));
        }
        if (vo.getStartDateStr() != null) {
            cam.setStartDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate4(vo.getStartDateStr()), "yyyyMMdd"));
            campaign.setStartDate(DateUtil.strToDate4(vo.getStartDateStr()));
        }
        if (vo.getEndDateStr() != null) {
            if ("".equals(vo.getEndDateStr())) {
                cam.setEndDate("");
                campaign.setEndDate(null);
            } else {
                cam.setEndDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate4(vo.getEndDateStr()), "yyyyMMdd"));
                campaign.setEndDate(DateUtil.strToDate4(vo.getEndDateStr()));
            }
        }

        if (vo.getPortfolioId() != null) {
            if ("".equals(vo.getPortfolioId())) {
                cam.setPortfolioId(0L);
                campaign.setPortfolioId(vo.getPortfolioId());
            } else {
                cam.setPortfolioId(Long.valueOf(vo.getPortfolioId()));
                campaign.setPortfolioId(vo.getPortfolioId());
            }
        }
        //校验站点广告最大值最小值
        if (StringUtils.isNotBlank(campaign.getMarketplaceId()) && vo.getBudget() != null) {
            Double maxValue = 0D;
            Double minValue = 0D;
            if (SBBudgetTypeEnum.DAILY.getCode().equalsIgnoreCase(campaign.getBudgetType())) {
            maxValue = SBBudgetValueEnum.getDailyMaxValue(campaign.getMarketplaceId());
            minValue = BudgetValueEnum.getMinValue(campaign.getMarketplaceId());
            } else if (SBBudgetTypeEnum.LIFETIME.getCode().equalsIgnoreCase(campaign.getBudgetType())) {
                maxValue = SBBudgetValueEnum.getLifetimeMaxValue(campaign.getMarketplaceId());
                minValue = SBBudgetValueEnum.getLifetimeMinValue(campaign.getMarketplaceId());
            }

            if (maxValue != null) {
                if (new BigDecimal(vo.getBudget()).compareTo(BigDecimal.valueOf(maxValue)) > 0) {
                    return ResultUtil.error("每日预算填写错误，最大值：" + maxValue);
                }
            }
            if (minValue != null) {
                if (new BigDecimal(vo.getBudget()).compareTo(BigDecimal.valueOf(minValue)) < 0) {
                    return ResultUtil.error("每日预算填写错误，最小值：" + minValue);
                }
            }
        }

        if (vo.getBidOptimization() != null) {
            cam.setBidOptimization(vo.getBidOptimization());
            campaign.setBidOptimization(vo.getBidOptimization());
            if (!vo.getBidOptimization() && vo.getBidAdjustmentPercent() != null) {
                List<BidAdjustment> bidAdjustmentList = Lists.newArrayList();
                for (BidAdjustment.PlacementEnum value : BidAdjustment.PlacementEnum.values()) {
                    BidAdjustment adjustment = new BidAdjustment();
                    adjustment.setBidAdjustmentPredicate(value.getPlacement());
                    adjustment.setBidAdjustmentPercent(vo.getBidAdjustmentPercent());
                    bidAdjustmentList.add(adjustment);
                }
                cam.setBidAdjustment(bidAdjustmentList);
                campaign.setBidAdjustmentsByPlacement(JSONUtil.objectToJson(bidAdjustmentList));
                campaign.setBidMultiplier(BigDecimal.valueOf(bidAdjustmentList.get(0).getBidAdjustmentPercent()));
            }
        }

        campaignList.add(cam);
//        Result result = cpcSbCampaignApiService.update(shop, profile, campaignList);
        Result result = cpcSbCampaignApiService.v4update(shop, profile, campaignList.stream().map(this::convertV4Campaign).collect(Collectors.toList()));
        campaign.setUpdateId(vo.getUid());
        logSbCampaignUpdate(oldCampaign, campaign, vo.getIp(), result);
        if (result.success()) {
            amazonSbAdCampaignDao.updateById(vo.getPuid(), campaign);
            if (campaign.getEndDate() == null) {
                //结束时间为null时,为不影响现在功能,重新更新一次数据库,后期重构吧
                amazonSbAdCampaignDao.setEndDateNull(campaign);
                campaign.setEndDate(null);
                campaign.setEndTimeStr("");
            }

            //写入doris
            saveDoris(Collections.singletonList(campaign), false);

            // 修改成功 同步这个活动
            cpcAdSyncService.asyncSbByShop(shop, campaignId, null);
            return ResultUtil.success();
        }

        return ResultUtil.error(result.getMsg());
    }

    private void logSbCampaignUpdate(AmazonAdCampaignAll oldCampaign, AmazonAdCampaignAll campaign, String ip, Result result) {
        AdManageOperationLog sbCampaignOperationLog = adManageOperationLogService.getSbCampaignOperationLog(oldCampaign, campaign);
        sbCampaignOperationLog.setIp(ip);
        if (result.success()) {
            sbCampaignOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
        } else {
            sbCampaignOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            sbCampaignOperationLog.setResultInfo(StringUtils.isNotBlank(result.getMsg()) ? result.getMsg() : "更新失败");
        }
        adManageOperationLogService.printAdOperationLog(Lists.newArrayList(sbCampaignOperationLog));
    }


    /**
     * 转换v4格式
     *
     * @param campaign
     * @return
     */
    public V4Campaign convertV4Campaign(Campaign campaign) {
        V4Campaign v4Campaign = new V4Campaign();
        v4Campaign.setCampaignId(campaign.getCampaignId().toString());
        if (StringUtils.isNotBlank(campaign.getName())) {
            v4Campaign.setName(campaign.getName());
        }
        if (StringUtils.isNotBlank(campaign.getState())) {
            v4Campaign.setState(campaign.getState());
        }
        if (campaign.getBudget() != null) {
            v4Campaign.setBudget(campaign.getBudget());
        }
        if (campaign.getStartDate() != null) {
            v4Campaign.setStartDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(campaign.getStartDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        }
        if (campaign.getEndDate() != null) {
            if (StringUtils.EMPTY.equals(campaign.getEndDate())) {
                v4Campaign.setEndDate(StringUtils.EMPTY);
            } else {
                v4Campaign.setEndDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(campaign.getEndDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
            }
            // yyyyMMdd 转换未 yyyy-MM-dd
        }
        if (campaign.getPortfolioId() != null) {
            if (campaign.getPortfolioId() == 0L) {
                v4Campaign.setPortfolioId(StringUtils.EMPTY);
            } else {
                v4Campaign.setPortfolioId(campaign.getPortfolioId().toString());
            }
        }

        if (campaign.getBidOptimization() != null) {
            Bidding bidding = new Bidding();
            bidding.setBidOptimization(campaign.getBidOptimization());
            if (!campaign.getBidOptimization() && CollectionUtils.isNotEmpty(campaign.getBidAdjustment())) {
                List<BidAdjustmentByPlacement> bidAdjustmentByPlacements = new ArrayList<>(BidAdjustmentByPlacement.PlacementEnum.values().length);
                Double percentage = campaign.getBidAdjustment().get(0).getBidAdjustmentPercent();
                for (BidAdjustmentByPlacement.PlacementEnum value : BidAdjustmentByPlacement.PlacementEnum.values()) {
                    BidAdjustmentByPlacement bidAdjustmentByPlacement = new BidAdjustmentByPlacement();
                    bidAdjustmentByPlacement.setPlacement(value.getPlacement());
                    bidAdjustmentByPlacement.setPercentage(percentage);
                    bidAdjustmentByPlacements.add(bidAdjustmentByPlacement);
                }
                bidding.setBidAdjustmentsByPlacement(bidAdjustmentByPlacements);
            }
            v4Campaign.setBidding(bidding);
        }
        return v4Campaign;
    }

    @Override
    public Result archive(Integer puid, Integer shopId, Integer uid, String campaignId, String ip) throws InterruptedException {

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.error("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.error("没有站点对应的配置信息");
        }

        AmazonAdCampaignAll campaign = amazonSbAdCampaignDao.getByCampaignId(puid, shopId, null, campaignId, CampaignTypeEnum.sb.getCampaignType());
        if (campaign == null) {
            return ResultUtil.error("没有活动信息");
        }
        Result result = cpcSbCampaignApiService.v4archive(shop, profile, campaignId);

        AmazonAdCampaignAll oldCampaign = new AmazonAdCampaignAll();
        BeanUtils.copyProperties(campaign, oldCampaign);
        campaign.setUpdateId(uid);
        campaign.setState(CpcStatusEnum.archived.name());
        logSbCampaignUpdate(oldCampaign, campaign, ip, result);

        if (result.success()) {
            amazonSbAdCampaignDao.updateByIdAndPuid(puid, campaign);
            //写入doris
            saveDoris(Collections.singletonList(campaign), false);
            // 修改成功 同步这个活动
            cpcAdSyncService.asyncSbByShop(shop, campaignId, null);
            return ResultUtil.success();
        }else {
            if (result.getMsg().contains(AmazonErrorUtils.ARCHIVED_MESSAGE)){
                amazonSbAdCampaignDao.updateByIdAndPuid(puid, campaign);
                //写入doris
                saveDoris(Collections.singletonList(campaign), false);
                // 修改成功 同步这个活动
                cpcAdSyncService.asyncSbByShop(shop, campaignId, null);
            }
        }
        return ResultUtil.error(result.getMsg());
    }


    @Override
    public Page getPageList(Integer puid, CampaignPageParam param, Page page) {

        List<CampaignPageVo> voList = new ArrayList<>();

        page = amazonSbAdCampaignDao.getPageList(puid, param, page);

        List<AmazonAdCampaignAll> poList = page.getRows();
        if (CollectionUtils.isNotEmpty(poList)) {
            List<String> portfolioIds = poList.stream().filter(p -> p.getPortfolioId() != null).map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());

            Map<String, AmazonAdPortfolio> portfolioMap = null;
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(portfolioIds)) {
                portfolioMap = portfolioDao.getPortfolioList(puid, param.getShopId(), portfolioIds).stream()
                        .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
            }


            Map<Integer, User> userMap = userDao.listByPuid(puid).stream().collect(Collectors.toMap(User::getId, e -> e));
            long t1 = Instant.now().toEpochMilli();
            // 按活动分组获取活动的汇总数据
            Map<String, AmazonAdCampaignAllReport> campaignReportMap = Maps.newHashMapWithExpectedSize(2);

            campaignReportMap = amazonAdSbCampaignReportDao.listSumReports(puid, param.getShopId(), null, param.getStartDate(), param.getEndDate(),
                            poList.stream().map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList())).stream()
                    .collect(Collectors.toMap(AmazonAdCampaignAllReport::getCampaignId, e -> e));

            log.info("广告管理--广告活动接口调用-按campaignId获取sb广告活动报告- 花费时间 {} ,params: {}", (Instant.now().toEpochMilli() - t1), JSONUtil.objectToJson(param));

            long t2 = Instant.now().toEpochMilli();
            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();
            if (param.getShopSales() != null) {  // 最外层查了一次了
                shopSaleDto.setSumRange(param.getShopSales());
            }
            Map<String, Long> outOfBudgetTime = new HashMap<>();

            page.setRows(voList);
            CampaignPageVo vo;
            for (AmazonAdCampaignAll amazonAdCampaign : poList) {
                vo = new CampaignPageVo();
                convertPoToPageVo(amazonAdCampaign, vo, outOfBudgetTime);

                if (StringUtils.isNotBlank(amazonAdCampaign.getPortfolioId())) {
                    if (portfolioMap != null && portfolioMap.containsKey(amazonAdCampaign.getPortfolioId())) {
                        vo.setPortfolioName(portfolioMap.get(amazonAdCampaign.getPortfolioId()).getName());
                    } else {
                        vo.setPortfolioName("广告组合待同步");
                    }
                } else {
                    vo.setPortfolioName("-");
                }

                // 创建人
                if (userMap.containsKey(amazonAdCampaign.getCreateId())) {
                    User user = userMap.get(amazonAdCampaign.getCreateId());
                    if (StringUtils.isNotBlank(user.getNickname())) {
                        vo.setCreator(user.getNickname());
                    }
                }
                voList.add(vo);
                // 填充报告数据
                if (campaignReportMap.get(amazonAdCampaign.getCampaignId()) != null) {
                    cpcCommService.fillReportDataIntoPageVo(vo, campaignReportMap.get(amazonAdCampaign.getCampaignId()).getReportBase(), shopSaleDto);
                }
            }
            log.info("广告管理--广告活动接口调用-广告活动填充报告数据等- 花费时间 {} ,params: {}", (Instant.now().toEpochMilli() - t2), JSONUtil.objectToJson(param));
        }

        return page;
    }

    private void convertPoToSBPageVo(Product product, SbPageResponse.Page.SBPageVo.Builder builder) {
        if (product.getId() != null) {
            builder.setId(Int32Value.of(product.getId()));
        }
        if (product.getShopId() != null) {
            builder.setShopId(Int32Value.of(product.getShopId()));
        }
        if (product.getSku() != null) {
            builder.setSku(product.getSku());
        }
        if (product.getAsin() != null) {
            builder.setAsin(product.getAsin());
        }
        if (product.getOnlineStatus() != null) {
            builder.setOnlineStatus(product.getOnlineStatus());
        }
        if (product.getTitle() != null) {
            builder.setTitle(product.getTitle());
        }
        if (product.getMainImage() != null) {
            builder.setImgUrl(product.getMainImage());
        }
    }

    private String save(SbCampaignVo vo, SbV4CampaignSuccessResult campaignResult, AmazonAdProfile profile) {
        SbV4CampaignInfo campaignInfo = campaignResult.getCampaign();
        String errMsg = "";
        int puid = vo.getPuid();
        int shopId = profile.getShopId();
        String marketplaceId = profile.getMarketplaceId();
        String profileId = profile.getProfileId();
        String campaignId = campaignResult.getCampaignId();

        AmazonAdCampaignAll sbAdCampaign = new AmazonAdCampaignAll();
        sbAdCampaign.setPuid(puid);
        sbAdCampaign.setShopId(shopId);
        sbAdCampaign.setMarketplaceId(marketplaceId);
        sbAdCampaign.setProfileId(profileId);
        sbAdCampaign.setCampaignId(campaignId);

        sbAdCampaign.setName(campaignInfo.getName());
        sbAdCampaign.setBrandEntityId(vo.getBrandEntityId());

        if (StringUtils.isNotBlank(vo.getStartDateStr())) {
            sbAdCampaign.setStartDate(DateUtil.strToDate4(vo.getStartDateStr()));
        }
        if (StringUtils.isNotBlank(vo.getEndDateStr())) {
            sbAdCampaign.setEndDate(DateUtil.strToDate4(vo.getEndDateStr()));
        }

        if (StringUtils.isNotBlank(vo.getPortfolioId())) {
            sbAdCampaign.setPortfolioId(vo.getPortfolioId());
        }
        if (campaignInfo.getExtendedData() != null) {
            sbAdCampaign.setServingStatus(campaignInfo.getExtendedData().getServingStatus());
            if (StringUtils.isNotBlank(campaignInfo.getExtendedData().getCreationDate())) {
                LocalDateTime localDateTime = LocalDateTimeUtil.timestampToDateTime(Long.valueOf(campaignInfo.getExtendedData().getCreationDate()));
                sbAdCampaign.setCreationDate(localDateTime);
            }
            if (StringUtils.isNotBlank(campaignInfo.getExtendedData().getLastUpdatedDate())) {
                LocalDateTime localDateTime = LocalDateTimeUtil.timestampToDateTime(Long.valueOf(campaignInfo.getExtendedData().getLastUpdatedDate()));
                sbAdCampaign.setCreationDate(localDateTime);
            }
        }
        if (campaignInfo.getBidding() != null) {
            Bidding bidding = campaignInfo.getBidding();
            if (bidding.getBidOptimization() != null) {
                sbAdCampaign.setBidOptimization(bidding.getBidOptimization());
            }
            if (bidding.getBidAdjustmentsByShopperSegment() != null) {
                List<BidAdjustmentByShopperSegment> segment = bidding.getBidAdjustmentsByShopperSegment();
                sbAdCampaign.setBidAdjustmentsByShopperSegment(JSONUtil.objectToJson(segment));
            }
            if (bidding.getBidAdjustmentsByPlacement() != null) {
                List<BidAdjustmentByPlacement> adjustmentByPlacementList = bidding.getBidAdjustmentsByPlacement();
                sbAdCampaign.setBidAdjustmentsByPlacement(JSONUtil.objectToJson(adjustmentByPlacementList));
            }
            // 只要关闭了自动竞价(BidOptimization = false),必须调整竞价百分比
            if (!bidding.getBidOptimization()) {
                sbAdCampaign.setBidMultiplier(BigDecimal.valueOf(bidding.getBidAdjustmentsByPlacement().get(0).getPercentage()));
            }
            if (StringUtils.isNotBlank(bidding.getBidOptimizationStrategy())) {
                sbAdCampaign.setBidOptimizationStrategy(bidding.getBidOptimizationStrategy());
            }
        }
        if (campaignInfo.getMultiAdGroupsEnabled() != null) {
            sbAdCampaign.setIsMultiAdGroupsEnabled(campaignInfo.getMultiAdGroupsEnabled());
        }
        sbAdCampaign.setState(campaignInfo.getState().toLowerCase());
        sbAdCampaign.setBudget(new BigDecimal(String.valueOf(campaignInfo.getBudget())));
        sbAdCampaign.setBudgetType(campaignInfo.getBudgetType().toLowerCase());
        sbAdCampaign.setCreateId(vo.getUid());
        sbAdCampaign.setCreateInAmzup(Constants.CREATE_IN_AMZUP);
        sbAdCampaign.setType(CampaignTypeEnum.sb.getCampaignType());
        sbAdCampaign.setAdFormat(Constants.SB_CAMPAIGN_MANUAL);
        sbAdCampaign.setAdTargetType(Constants.SB_CAMPAIGN_MANUAL_NEW);
        // 创意和着陆页 以同步过来的数据为准

        //广告目标字段
        Optional.ofNullable(vo.getGoal()).ifPresent(sbAdCampaign::setAdGoal);
        //花费类型字段
        Optional.ofNullable(vo.getCostType()).ifPresent(sbAdCampaign::setCostType);
        try {
            amazonSbAdCampaignDao.save(puid, sbAdCampaign);
            //写入doris
            saveDoris(Collections.singletonList(sbAdCampaign), true);
        } catch (Exception e) {
            log.error("createSbCampaign:", e);
        }

        return errMsg;
    }

    // po -> 列表页vo
    private void convertPoToPageVo(AmazonAdCampaignAll amazonAdCampaign, CampaignPageVo vo, Map<String, Long> outOfBudgetTime) {
        vo.setId(amazonAdCampaign.getId());
        vo.setShopId(amazonAdCampaign.getShopId());
        vo.setCampaignId(amazonAdCampaign.getCampaignId());
        vo.setName(amazonAdCampaign.getName());
        vo.setState(amazonAdCampaign.getState());
        amazonAdCampaign.setServingStatus(amazonAdCampaign.getServingStatus());
        vo.setServingStatus(amazonAdCampaign.getServingStatus());
        vo.setPortfolioId(amazonAdCampaign.getPortfolioId());
        vo.setServingStatusName(amazonAdCampaign.getServingStatusName());
        vo.setServingStatusDec(amazonAdCampaign.getServingStatusDec());
        if (AmazonAdCampaign.stateEnum.enabled.getCode().equals(amazonAdCampaign.getState())) {
            if (StringUtils.isNotBlank(amazonAdCampaign.getServingStatus())) {
                AmazonAdCampaignAll.servingStatusEnum servingStatusEnum = UCommonUtil.getByCode(amazonAdCampaign.getServingStatus(), AmazonAdCampaignAll.servingStatusEnum.class);
                //sb 预算不足也转成  CAMPAIGN_OUT_OF_BUDGET  保持与sp和sd一致
                if (AmazonAdCampaignAll.servingStatusEnum.outOfBudget.getCode().equalsIgnoreCase(amazonAdCampaign.getServingStatus())) {
                    vo.setServingStatus("CAMPAIGN_OUT_OF_BUDGET");
                }
                vo.setServingStatusDec(null == servingStatusEnum ? StringUtils.EMPTY : servingStatusEnum.getDescription());
            }
            if (AmazonAdCampaignAll.servingStatusEnum.outOfBudget.getCode().equalsIgnoreCase(amazonAdCampaign.getServingStatus()) || AmazonAdCampaignAll.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode().equalsIgnoreCase(amazonAdCampaign.getServingStatus())) {
                //如果状态是超过预算
                String outOfTimeStr = "";
                if (amazonAdCampaign.getOutOfBudgetTime() != null) {
                    try {
                        LocalDateTime localDateTime = LocalDateTimeUtil.ofEpochSecondToDateTime(amazonAdCampaign.getOutOfBudgetTime());
                        ZoneId zoneId = ZoneUtil.getZoneIdByAmzSite(amazonAdCampaign.getMarketplaceId());
                        localDateTime = LocalDateTimeUtil.getZoneTime(localDateTime, ZoneId.systemDefault(), zoneId);
                        Date date = LocalDateTimeUtil.convertLDTToDate(localDateTime);
                        outOfTimeStr = DateUtil.dateToStrWithFormat(date, "HH:mm");
                    } catch (Exception e) {
                        log.error("转换超预算时间错误", e);
                    }
                }
                vo.setServingStatusName(vo.getServingStatusName() + " " + outOfTimeStr);
                vo.setOutOfBudget(true);

            }
        }
        vo.setTargetType(amazonAdCampaign.getTargetType());
        vo.setAdFormat(amazonAdCampaign.getAdFormat());
        vo.setBudgetType(amazonAdCampaign.getBudgetType());
        vo.setDailyBudget(String.valueOf(amazonAdCampaign.getBudget()));
        vo.setCreateTime(amazonAdCampaign.getCreateTime());
        vo.setUpdateTime(amazonAdCampaign.getUpdateTime());
        vo.setTargetingType(Constants.MANUAL);
        vo.setCampaignType(CampaignEnum.sponsoredBrands.getCampaignType());
        vo.setType(Constants.SB);
        vo.setCampaignTargetingType(amazonAdCampaign.getAdFormat());


        if (amazonAdCampaign.getStartDate() != null) {
            vo.setStartDate(DateUtil.format(amazonAdCampaign.getStartDate(), DateUtil.PATTERN));
        }
        if (amazonAdCampaign.getEndDate() != null) {
            vo.setEndDate(DateUtil.format(amazonAdCampaign.getEndDate(), DateUtil.PATTERN));
        }
    }

    @Override
    public Result<BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll>> updateBatchCampaign(List<BatchCampaignVo> vos, String type) throws InterruptedException {
        if (CollectionUtils.isEmpty(vos)) {
            return ResultUtil.error("请求参数错误");
        }

        int puid = vos.get(0).getPuid();
        Integer shopId = vos.get(0).getShopId();
        int uid = vos.get(0).getUid();
        String ip = vos.get(0).getLoginIp();


        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.error("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.error("没有站点对应的配置信息");
        }

        List<String> campaignIds = vos.stream().map(BatchCampaignVo::getCampaignId).collect(Collectors.toList());
        List<AmazonAdCampaignAll> campaigns = amazonSbAdCampaignDao.getByCampaignIds(puid, shopId, null, campaignIds, CampaignTypeEnum.sb.getCampaignType());
        if (CollectionUtils.isEmpty(campaigns)) {
            return ResultUtil.error("没有活动信息");
        }
        Map<String, AmazonAdCampaignAll> amazonSbAdCampaignMap = campaigns.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
        List<BatchCampaignVo> errorList = new ArrayList<>();
        List<AmazonAdCampaignAll> updateList = new ArrayList<>();
        List<AmazonAdCampaignAll> archiveList = new ArrayList<>();
        for (BatchCampaignVo vo : vos) {
            checkParam(vo, type);
            if (vo.getDxmCampaignId() != null) {
                vo.setId(vo.getDxmCampaignId());
            }
            AmazonAdCampaignAll oldAmazonSbAdCampaign = amazonSbAdCampaignMap.get(vo.getCampaignId());
            if (oldAmazonSbAdCampaign == null) {

                vo.setFailReason("活动不存在");
                errorList.add(vo);
                continue;
            }

            if (StringUtils.isNotBlank(vo.getFailReason())) {
                vo.setId(oldAmazonSbAdCampaign.getId());
                vo.setName(oldAmazonSbAdCampaign.getName());
                vo.setFailReason("请求参数错误");
                errorList.add(vo);
                continue;
            }
            AmazonAdCampaignAll amazonSbAdCampaign = new AmazonAdCampaignAll();
            BeanUtils.copyProperties(oldAmazonSbAdCampaign, amazonSbAdCampaign);
            convertAmazonSbCampaign(amazonSbAdCampaign, vo, type);
            updateList.add(amazonSbAdCampaign);
        }

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(updateList)) {
            BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll> data = new BatchResponseVo<>();
            data.setErrorList(errorList);
            data.setSuccessNum(0);
            data.setCountNum(vos.size());
            data.setFailNum(errorList.size());
            return ResultUtil.success(data);
        }

//        Result<BatchResponseVo<BatchCampaignVo,AmazonAdCampaignAll>> result = cpcSbCampaignApiService.update(shop, profile, updateList,type);
        Result<BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll>> result;
        List<Result<BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll>>> resultList = Collections.synchronizedList(new ArrayList<>());
        if (updateList.size() >= Constants.SB_CAMPAIGN_UPDATE_MAX_PARTITION) {
            ThreadPoolExecutor sbCampaignUpdatePool = ThreadPoolUtil.getSbCampaignBatchUpdateThreadPool();
            List<List<AmazonAdCampaignAll>> sbCampaignUpdatePartition = Lists.partition(updateList, Constants.SB_CAMPAIGN_UPDATE_PARTITION);
            try {
                List<CompletableFuture<Result<BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll>>>> reportFutures = new ArrayList<>(sbCampaignUpdatePartition.size());
                sbCampaignUpdatePartition.forEach(k -> reportFutures.add(CompletableFuture.supplyAsync(() -> cpcSbCampaignApiService.v4updateAndArchive(shop, profile, k, type), sbCampaignUpdatePool)));
                CompletableFuture<Void> all = CompletableFuture.allOf(reportFutures.toArray(new CompletableFuture[0]));
                CompletableFuture<List<Result<BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll>>>> results = all.thenApply(v -> reportFutures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
                results.get().stream().filter(Objects::nonNull).forEach(resultList::add);
            } catch (Exception e) {
                log.error("sb campaign batch update error", e);
                throw new BizServiceException("更新广告活动异常，请联系管理员");
            }
        } else {
            List<List<AmazonAdCampaignAll>> sbCampaignUpdatePartition = Lists.partition(updateList, Constants.SB_CAMPAIGN_UPDATE_PARTITION);
            for (List<AmazonAdCampaignAll> updateCampaignList : sbCampaignUpdatePartition) {
                resultList.add(cpcSbCampaignApiService.v4updateAndArchive(shop, profile, updateCampaignList, type));
            }
        }
        //合并结果
        result = this.buildBatchResponseVo(resultList);

        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayList();
        for (AmazonAdCampaignAll newAdCampaign : updateList) {
            AmazonAdCampaignAll oldAdCampaign = amazonSbAdCampaignMap.get(newAdCampaign.getCampaignId());
            AdManageOperationLog adManageOperationLog = adManageOperationLogService.getSbCampaignOperationLog(oldAdCampaign, newAdCampaign);
            adManageOperationLog.setIp(ip);
            adManageOperationLogs.add(adManageOperationLog);
        }

        if (result.success()) {
            BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll> data = result.getData();
            List<AmazonAdCampaignAll> successList = data.getSuccessList();
            Map<String, AmazonAdCampaignAll> successDataMap = successList.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
            Map<String, BatchCampaignVo> errorDataMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(errorList)) {
                List<BatchCampaignVo> amazonAdProductError = data.getErrorList();
                amazonAdProductError.addAll(errorList);
                errorDataMap = amazonAdProductError.stream().collect(Collectors.toMap(BatchCampaignVo::getCampaignId, e -> e));
                data.setFailNum(data.getErrorList().size());
                data.setCountNum((data.getErrorList() == null ? 0 : data.getErrorList().size()) + (data.getSuccessList() == null ? 0 : data.getSuccessList().size()));
            }

            for (AdManageOperationLog adManageOperationLog : adManageOperationLogs) {
                if (!StringUtil.isEmptyObject(successDataMap.get(adManageOperationLog.getCampaignId()))) {
                    adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                }
                if (!StringUtil.isEmptyObject(errorDataMap.get(adManageOperationLog.getCampaignId()))) {
                    adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    adManageOperationLog.setResultInfo(errorDataMap.get(adManageOperationLog.getCampaignId()).getFailReason());
                }
            }
            adManageOperationLogService.printAdOperationLog(adManageOperationLogs);

            if (CollectionUtils.isNotEmpty(successList)) {
                amazonSbAdCampaignDao.batchUpdateAmazonAdCampaign(puid, successList, type);
                List<String> campaignIdList = successList.stream().map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
                //写入doris
                saveDoris(puid, shopId, campaignIdList);
                // 修改成功 同步这个活动
                String successCampaignIds = StringUtils.join(campaignIdList, ",");
                cpcAdSyncService.asyncSbByShop(shop, successCampaignIds, null);
                //更新成功数据打日志

                data.getSuccessList().clear();
            }
            return result;
        } else {
            for (AdManageOperationLog adManageOperationLog : adManageOperationLogs) {
                adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                adManageOperationLog.setResultInfo(result.getMsg());
            }
            adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
        }

        return result;
    }

    private Result<BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll>> buildBatchResponseVo(List<Result<BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll>>> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return new Result<>();
        }
        BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll> batchResponseVo = new BatchResponseVo<>();
        batchResponseVo.setCountNum(0);
        batchResponseVo.setFailNum(0);
        batchResponseVo.setErrorList(new ArrayList<>());
        batchResponseVo.setSuccessNum(0);
        batchResponseVo.setSuccessList(new ArrayList<>());
        for (Result<BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll>> result : resultList) {
            BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll> responseVos = result.getData();
            if (responseVos.getCountNum() != null) {
                batchResponseVo.setCountNum(batchResponseVo.getCountNum() + responseVos.getCountNum());
            }
            if (CollectionUtils.isNotEmpty(responseVos.getErrorList())) {
                batchResponseVo.setFailNum(batchResponseVo.getErrorList().size() + responseVos.getErrorList().size());
                batchResponseVo.getErrorList().addAll(responseVos.getErrorList());
            }
            if (CollectionUtils.isNotEmpty(responseVos.getSuccessList())) {
                batchResponseVo.setSuccessNum(batchResponseVo.getSuccessList().size() + responseVos.getSuccessList().size());
                batchResponseVo.getSuccessList().addAll(responseVos.getSuccessList());
            }
        }
        return ResultUtil.success(batchResponseVo);
    }

    private BatchCampaignVo checkParam(BatchCampaignVo vo, String type) {
        if (StringUtils.isBlank(vo.getCampaignId())) {
            vo.setFailReason("请求参数错误");
            return vo;
        }
        if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            if (StringUtils.isBlank(vo.getState())) {
                vo.setFailReason("请求参数错误");
                return vo;
            }
        } else if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_BUDGET.equals(type)) {
            if (vo.getDailyBudget() == null) {
                vo.setFailReason("预算费用填写错误");
                return vo;
            }
        } else {
            vo.setFailReason("请求参数错误");
            return vo;
        }

        return vo;
    }


    private AmazonAdCampaignAll convertAmazonSbCampaign(AmazonAdCampaignAll campaign, BatchCampaignVo vo, String type) {
        campaign.setUpdateId(vo.getUid());
        if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            campaign.setState(vo.getState());
        } else if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_BUDGET.equals(type)) {
            campaign.setBudget(BigDecimal.valueOf(vo.getDailyBudget()));
        }
        return campaign;
    }

    private List<Object> buildUpLogMessage(Map<String, AmazonAdCampaignAll> oldList, List<AmazonAdCampaignAll> newList, String type) {
        List<Object> dataList = new ArrayList<>();
        StringBuilder builder = new StringBuilder();
        if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_BUDGET.equals(type)) {
            dataList.add("SB广告活动批量修改每日预算");
        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            builder.append("SB广告活动批量修改状态");
        }

        newList.forEach(e -> {
            AmazonAdCampaignAll old = oldList.get(e.getCampaignId());

            if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_BUDGET.equals(type)) {

                builder.append("campaignId:").append(e.getCampaignId());
                if (old != null) {
                    builder.append(",旧值:").append(old.getBudget());
                }
                builder.append(",新值:").append(e.getBudget());
            } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
                builder.append("campaignId:").append(e.getCampaignId());
                if (old != null) {
                    builder.append(",旧值:").append(old.getState());
                }
                builder.append(",新值:").append(e.getState());
            }
            dataList.add(builder.toString());
            builder.delete(0, builder.length());
        });
        return dataList;
    }


    @Override
    public List<String> getAsinByCampaignsId(Integer puid, Integer shopId, String campaignsId) {
        AmazonAdCampaignAll byCampaignId = amazonSbAdCampaignDao.getByCampaignId(puid, shopId, null, campaignsId, CampaignTypeEnum.sb.getCampaignType());
        List<String> asinList = new ArrayList<>();
        if (byCampaignId != null && StringUtils.isNotBlank(byCampaignId.getCreative())) {
            Creative creativeVo = JSONUtil.jsonToObject(byCampaignId.getCreative(), Creative.class);
            if (creativeVo != null) {
                asinList = creativeVo.getAsins();
            }
        }

        return asinList;
    }


    /**
     * 写入doris
     * @param amazonAdCampaignList
     */
    public void saveDoris(List<AmazonAdCampaignAll> amazonAdCampaignList, boolean create) {
        try {
            Date date = new Date();
            List<OdsAmazonAdCampaignAll> collect = amazonAdCampaignList.stream().map(x -> {
                OdsAmazonAdCampaignAll odsAmazonAdCampaignAll = new OdsAmazonAdCampaignAll();
                BeanUtils.copyProperties(x, odsAmazonAdCampaignAll);
                if (create) {
                    odsAmazonAdCampaignAll.setCreateTime(date);
                }
                odsAmazonAdCampaignAll.setUpdateTime(date);
                if (StringUtils.isNotBlank(odsAmazonAdCampaignAll.getState())) {
                    odsAmazonAdCampaignAll.setState(odsAmazonAdCampaignAll.getState().toLowerCase());
                }
                return odsAmazonAdCampaignAll;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sb campaign save doris error", e);
        }
    }

    /**
     * 写入doris
     * @param campaignIdList
     */
    private void saveDoris(Integer puid, Integer shopId, List<String> campaignIdList) {
        try {
            if (CollectionUtils.isEmpty(campaignIdList)) {
                return;
            }
            List<AmazonAdCampaignAll> campaignAllList = amazonSbAdCampaignDao.listByCampaignId(puid, shopId, campaignIdList, CampaignTypeEnum.sb.getCampaignType());
            saveDoris(campaignAllList, false);
        } catch (Exception e) {
            log.error("sb campaign save doris error", e);
        }
    }

    private SbCampaignVo buildCampaignVo(NewCreateCampaignRequest request) {
        SbCampaignVo vo = new SbCampaignVo();
        vo.setPuid(request.getPuid());
        vo.setShopId(request.getShopId());
        vo.setUid(request.getUid());
        vo.setName(request.getCampaignName().trim());
        vo.setState(request.getCampaignState());
        vo.setBudgetType(request.getBudgetType());
        vo.setBudget(Optional.of(request.getBudget()).map(String::trim).filter(b -> !("0").equals(b)).map(String::valueOf).orElse(""));
        vo.setBrandEntityId(request.getBrandEntityId());
        if (request.hasPortfolioId() && StringUtils.isNotBlank(request.getPortfolioId()) && !Constant.NON_PORTFOLIO_ID.equals(request.getPortfolioId())) {
            vo.setPortfolioId(request.getPortfolioId());
        }
        vo.setStartDateStr(request.getStartDateStr());
        vo.setEndDateStr(request.getEndDateStr());
        //自动按位置进行加价
        if (request.hasBidOptimization()) {
            vo.setBidOptimization(request.getBidOptimization());
        }
        //手动指定按位置进行加价
        if (!request.hasBidOptimization() || !request.getBidOptimization()) {
            Optional.of(request.getPlacementPercentage()).map(Double::valueOf).ifPresent(vo::setPlacementPercentage);
        }

        //goal和costType两个属性有依赖关系，要不同时设置，要不都不设置
        SBCampaignGoalEnum goalEn = SBCampaignGoalEnum.getSBCampaignGoalEnumByCode(request.getGoal());
        if (Objects.nonNull(goalEn)) {
            vo.setGoal(goalEn.getType());
            if (SBCampaignGoalEnum.PAGE_VISIT == goalEn) {
                vo.setCostType(SBCampaignCostTypeEnum.CPC.getCode());
            }
            if (SBCampaignGoalEnum.BRAND_IMPRESSION_SHARE == goalEn) {
                vo.setCostType(SBCampaignCostTypeEnum.VCPM.getCode());
                vo.setBidOptimization(null);
                vo.setPlacementPercentage(null);
            }
        }
        return vo;
    }

    private SbAdGroupVo buildGroupVo(NewCreateCampaignRequest request) {
        SbAdGroupVo vo = new SbAdGroupVo();
        vo.setPuid(request.getPuid());
        vo.setShopId(request.getShopId());
        vo.setUid(request.getUid());
        vo.setName(request.getGroupName());
        vo.setState(request.getGroupState());
        vo.setCampaignId(request.getCampaignId());
        vo.setAdFormat(Optional.ofNullable(SBAdFormatEnum.getSBAdFormatEnumByCode(request.getAdFormat()))
                .map(SBAdFormatEnum::getValue).orElseThrow(() -> new ServiceException("广告格式不存在")));
        SbAdVo sbAdVo = new SbAdVo();
        sbAdVo.setName(request.getAdsName());
        sbAdVo.setState(request.getAdsState());
        sbAdVo.setLandingPage(request.getLandingPageVo());
        sbAdVo.setCreative(request.getCreativeVo());
        vo.setSbAdVo(sbAdVo);
        return vo;
    }

    private SbAdVo buildAdsVo(NewCreateCampaignRequest request) {
        SbAdVo sbAdVo = new SbAdVo();
        Optional.of(request.getAdFormat()).map(SBAdFormatEnum::getSBAdFormatEnumByCode)
                .map(SBAdFormatEnum::getValue).ifPresent(sbAdVo::setAdFormat);
        sbAdVo.setName(request.getAdsName());
        sbAdVo.setState(request.getAdsState());
        sbAdVo.setLandingPage(request.getLandingPageVo());
        sbAdVo.setCreative(request.getCreativeVo());
        return sbAdVo;
    }

    private AddSbKeywordsVo buildKeywordVo(NewCreateCampaignRequest request) {
        List<com.meiyunji.sponsored.rpc.sb.campaign.SbKeywordsVo> keywordList = request.getKeywordsVoList();
        AddSbKeywordsVo vo = new AddSbKeywordsVo();
        vo.setPuid(request.getPuid());
        vo.setUid(request.getUid());
        vo.setShopId(request.getShopId());
        vo.setCampaignId(request.getCampaignId());
        vo.setGroupId(request.getAdGroupId());
        List<com.meiyunji.sponsored.service.cpc.vo.KeywordsVo> voList = new ArrayList<>(keywordList.size());
        KeywordsVo keywordsVo;
        for (com.meiyunji.sponsored.rpc.sb.campaign.SbKeywordsVo keywordV : keywordList) {
            keywordsVo = new KeywordsVo();
            if (StringUtils.isNotBlank(keywordV.getKeywordText())) {
                keywordsVo.setKeywordText(StringUtil.replaceSpecialSymbol(keywordV.getKeywordText()));
            }
            if (StringUtils.isNotBlank(keywordV.getMatchType())) {
                keywordsVo.setMatchType(keywordV.getMatchType());
            }
            if (StringUtils.isNotBlank(Optional.of(keywordV.getBid()).map(String::valueOf).orElse(""))) {
                keywordsVo.setBid(Optional.of(keywordV.getBid()).map(String::valueOf).orElse(""));
            }
            if (StringUtils.isNotBlank(keywordV.getSuggested())) {
                keywordsVo.setSuggested(keywordV.getSuggested());
            }
            if (StringUtils.isNotBlank(keywordV.getRangeStart())) {
                keywordsVo.setRangeStart(keywordV.getRangeStart());
            }
            if (StringUtils.isNotBlank(keywordV.getRangeEnd())) {
                keywordsVo.setRangeEnd(keywordV.getRangeEnd());
            }
            voList.add(keywordsVo);
        }
        vo.setKeywords(voList);
        return vo;
    }

    private AddSbThemesVo buildThemesVo(NewCreateCampaignRequest request) {
        AddSbThemesVo vo = new AddSbThemesVo();
        List<com.meiyunji.sponsored.rpc.sb.campaign.Themes> ThemesList = request.getThemesVoList();
        List<com.meiyunji.sponsored.service.cpc.vo.ThemesVo> voList = new ArrayList<>(ThemesList.size());
        vo.setPuid(request.getPuid());
        vo.setShopId(request.getShopId());
        vo.setCampaignId(request.getCampaignId());
        vo.setGroupId(request.getAdGroupId());
        for (com.meiyunji.sponsored.rpc.sb.campaign.Themes t : ThemesList) {
            ThemesVo theme = new ThemesVo();
            Optional.of(t.getThemeType()).map(SBThemesEnum::getSBThemesEnumByCode).map(SBThemesEnum::getValue).ifPresent(theme::setThemeType);
            Optional.of(t.getBid()).ifPresent(theme::setBid);
            voList.add(theme);
        }
        vo.setThemes(voList);
        return vo;
    }

    private AddSbTargetingVo buildTargetingVo(NewCreateCampaignRequest request) {
        List<com.meiyunji.sponsored.rpc.sb.campaign.SbTargetsVo> targetingVoList = request.getTargetsVoList();
        AddSbTargetingVo addSbTargetingVo = new AddSbTargetingVo();
        addSbTargetingVo.setPuid(request.getPuid());
        addSbTargetingVo.setShopId(request.getShopId());
        addSbTargetingVo.setUid(request.getUid());
        addSbTargetingVo.setCampaignId(request.getCampaignId());
        addSbTargetingVo.setGroupId(request.getAdGroupId());
        if (StringUtils.isNotBlank(Optional.ofNullable(SBAdFormatEnum.getSBAdFormatEnumByCode(request.getAdFormat()))
                .map(SBAdFormatEnum::getValue).orElseThrow(() -> new ServiceException("广告格式不存在")))) {
            addSbTargetingVo.setAdFormat(Optional.ofNullable(SBAdFormatEnum.getSBAdFormatEnumByCode(request.getAdFormat()))
                    .map(SBAdFormatEnum::getValue).orElseThrow(() -> new ServiceException("广告格式不存在")));
        }

        List<com.meiyunji.sponsored.service.cpc.vo.TargetingVo> voList = new ArrayList<>(targetingVoList.size());
        com.meiyunji.sponsored.service.cpc.vo.TargetingVo targetingVo;
        for (com.meiyunji.sponsored.rpc.sb.campaign.SbTargetsVo rpcVo : targetingVoList) {
            targetingVo = new com.meiyunji.sponsored.service.cpc.vo.TargetingVo();
            if (StringUtils.isNotBlank(rpcVo.getType())) {
                targetingVo.setType(rpcVo.getType());
            }
            if (StringUtils.isNotBlank(rpcVo.getAsin())) {
                targetingVo.setAsin(rpcVo.getAsin());
            }
            if (StringUtils.isNotBlank(rpcVo.getTitle())) {
                targetingVo.setTitle(rpcVo.getTitle());
            }
            if (StringUtils.isNotBlank(rpcVo.getImgUrl())) {
                targetingVo.setImgUrl(rpcVo.getImgUrl());
            }
            if (StringUtils.isNotBlank(rpcVo.getCategoryId())) {
                targetingVo.setCategoryId(rpcVo.getCategoryId());
            }
            if (StringUtils.isNotBlank(rpcVo.getCategory())) {
                targetingVo.setCategory(rpcVo.getCategory());
            }
            if (StringUtils.isNotBlank(Optional.of(rpcVo.getBid()).map(String::valueOf).orElse(""))) {
                targetingVo.setBid(Optional.of(rpcVo.getBid()).map(String::valueOf).orElse(""));
            }
            if (StringUtils.isNotBlank(rpcVo.getBrand())) {
                targetingVo.setBrand(rpcVo.getBrand());
            }
            if (StringUtils.isNotBlank(rpcVo.getMinPrice())) {
                targetingVo.setMinPrice(rpcVo.getMinPrice());
            }
            if (StringUtils.isNotBlank(rpcVo.getMaxPrice())) {
                targetingVo.setMaxPrice(rpcVo.getMaxPrice());
            }
            if (rpcVo.hasMinReviewRating()) {
                targetingVo.setMinReviewRating(rpcVo.getMinReviewRating());
            }
            if (rpcVo.hasMaxReviewRating()) {
                targetingVo.setMaxReviewRating(rpcVo.getMaxReviewRating());
            }
            if (rpcVo.hasPrimeShippingEligible()) {
                targetingVo.setPrimeShippingEligible(rpcVo.getPrimeShippingEligible());
            }
            if (StringUtils.isNotBlank(rpcVo.getSuggested())) {
                targetingVo.setSuggested(rpcVo.getSuggested());
            }
            if (StringUtils.isNotBlank(rpcVo.getRangeStart())) {
                targetingVo.setRangeStart(rpcVo.getRangeStart());
            }
            if (StringUtils.isNotBlank(rpcVo.getRangeEnd())) {
                targetingVo.setRangeEnd(rpcVo.getRangeEnd());
            }
            voList.add(targetingVo);
        }

        addSbTargetingVo.setTargetings(voList);
        return addSbTargetingVo;
    }

    private AddSbNeKeywordsVo buildNeKeywordVo(NewCreateCampaignRequest request) {
        List<com.meiyunji.sponsored.rpc.sb.campaign.SbNeKeywordsVo> neKeywordVoList = request.getNeKeywordVoList();
        AddSbNeKeywordsVo vo = new AddSbNeKeywordsVo();
        vo.setShopId(request.getShopId());
        vo.setPuid(request.getPuid());
        vo.setUid(request.getUid());
        vo.setCampaignId(request.getCampaignId());
        vo.setGroupId(request.getAdGroupId());

        List<com.meiyunji.sponsored.service.cpc.vo.NeKeywordsVo> list = new ArrayList<>(neKeywordVoList.size());
        com.meiyunji.sponsored.service.cpc.vo.NeKeywordsVo neKeywordsVo;
        for (com.meiyunji.sponsored.rpc.sb.campaign.SbNeKeywordsVo v : neKeywordVoList) {
            neKeywordsVo = new com.meiyunji.sponsored.service.cpc.vo.NeKeywordsVo();
            if (StringUtils.isNotBlank(v.getKeywordText())) {
                neKeywordsVo.setKeywordText(StringUtil.replaceSpecialSymbol(v.getKeywordText()));
            }
            if (StringUtils.isNotBlank(v.getMatchType())) {
                neKeywordsVo.setMatchType(v.getMatchType());
            }
            list.add(neKeywordsVo);
        }
        vo.setNeKeywords(list);
        return vo;
    }

    private AddSbNeTargetingVo buildNeTargetingVo(NewCreateCampaignRequest request) {
        List<com.meiyunji.sponsored.rpc.sb.campaign.SbNeTargetsVo> neTargetingVoList = request.getNeTargetsVoList();
        AddSbNeTargetingVo vo = new AddSbNeTargetingVo();
        vo.setShopId(request.getShopId());
        vo.setPuid(request.getPuid());
        vo.setUid(request.getUid());
        vo.setCampaignId(request.getCampaignId());
        vo.setGroupId(request.getAdGroupId());

        List<com.meiyunji.sponsored.service.cpc.vo.SbNeTargetsVo> voList = new ArrayList<>(neTargetingVoList.size());
        com.meiyunji.sponsored.service.cpc.vo.SbNeTargetsVo sbNeTargetsVo;
        for (com.meiyunji.sponsored.rpc.sb.campaign.SbNeTargetsVo targetsVo : neTargetingVoList)  {
            sbNeTargetsVo = new com.meiyunji.sponsored.service.cpc.vo.SbNeTargetsVo();
            if (StringUtils.isNotBlank(targetsVo.getTargetId())) {
                sbNeTargetsVo.setTargetId(targetsVo.getTargetId());
            }
            if (StringUtils.isNotBlank(targetsVo.getErrMsg())) {
                sbNeTargetsVo.setErrMsg(targetsVo.getErrMsg());
            }
            if (StringUtils.isNotBlank(targetsVo.getType())) {
                sbNeTargetsVo.setType(targetsVo.getType());
            }
            if (StringUtils.isNotBlank(targetsVo.getAsin())) {
                sbNeTargetsVo.setAsin(targetsVo.getAsin());
            }
            if (StringUtils.isNotBlank(targetsVo.getTitle())) {
                sbNeTargetsVo.setTitle(targetsVo.getTitle());
            }
            if (StringUtils.isNotBlank(targetsVo.getImgUrl())) {
                sbNeTargetsVo.setImgUrl(targetsVo.getImgUrl());
            }
            if (StringUtils.isNotBlank(targetsVo.getBrandName())) {
                sbNeTargetsVo.setBrandName(targetsVo.getBrandName());
            }
            if (StringUtils.isNotBlank(targetsVo.getBrandId())) {
                sbNeTargetsVo.setBrandId(targetsVo.getBrandId());
            }
            voList.add(sbNeTargetsVo);
        }
        vo.setNeTargetsVos(voList);
        return vo;
    }

    private NewCreateResultResultVo<SBCommonErrorVo> submitTogetherKeyword(NewCreateCampaignRequest request, String campaignId,
                                                          String adGroupId, ShopAuth shop,
                                                          AmazonAdProfile profile) {
        if (StringUtils.isEmpty(request.getTargetId()) && CollectionUtils.isNotEmpty(request.getKeywordsVoList())) {
            //组装关键词vo
            AddSbKeywordsVo keywordVo = buildKeywordVo(request);
            if (StringUtils.isEmpty(campaignId) || StringUtils.isEmpty(adGroupId)) {
                log.error("创建关键词投放campaignId或adGroupId不能为空");
                return null;
            }
            Optional.of(campaignId).ifPresent(keywordVo::setCampaignId);
            Optional.of(adGroupId).ifPresent(keywordVo::setGroupId);
            return cpcSbKeywordService.createKeywords(keywordVo, shop, profile);
        }
        return null;
    }

    private NewCreateResultResultVo<SBCommonErrorVo> submitTogetherTheme(NewCreateCampaignRequest request, String campaignId,
                                                          String adGroupId, ShopAuth shop,
                                                          AmazonAdProfile profile) {
        if (StringUtils.isEmpty(request.getTargetId()) && CollectionUtils.isNotEmpty(request.getThemesVoList())) {
            //组装关键词vo
            AddSbThemesVo themesVo = buildThemesVo(request);
            if (StringUtils.isEmpty(campaignId) || StringUtils.isEmpty(adGroupId)) {
                log.error("创建主题投放campaignId或adGroupId不能为空");
                return null;
            }
            Optional.of(campaignId).ifPresent(themesVo::setCampaignId);
            Optional.of(adGroupId).ifPresent(themesVo::setGroupId);
            return cpcSbKeywordService.createThemes(themesVo, shop, profile);
        }
        return null;
    }

    private NewCreateResultResultVo<SBCommonErrorVo> submitTogetherTargeting(NewCreateCampaignRequest request, String campaignId,
                                                            String adGroupId, ShopAuth shop, AmazonAdProfile profile) {
        if (StringUtils.isEmpty(request.getTargetId()) && CollectionUtils.isNotEmpty(request.getTargetsVoList())) {
            //组装商品投放vo
            AddSbTargetingVo targetingVo = buildTargetingVo(request);
            Optional.ofNullable(campaignId).ifPresent(targetingVo::setCampaignId);
            Optional.ofNullable(adGroupId).ifPresent(targetingVo::setGroupId);
            return cpcSbTargetService.createTargeting(targetingVo, shop, profile);
        }
        return null;
    }

    private NewCreateResultResultVo<SBCommonErrorVo> submitTogetherNeKeyword(NewCreateCampaignRequest request, String campaignId,
                                                            String adGroupId, ShopAuth shop,
                                                            AmazonAdProfile profile) {
        //创建否投
        if (StringUtils.isEmpty(request.getNeTargetId()) && CollectionUtils.isNotEmpty(request.getNeKeywordVoList())) {
            //组装关键词否投
            AddSbNeKeywordsVo neKeywordVo = buildNeKeywordVo(request);
            Optional.ofNullable(campaignId).ifPresent(neKeywordVo::setCampaignId);
            Optional.ofNullable(adGroupId).ifPresent(neKeywordVo::setGroupId);
            return cpcSbNeKeywordService.createNeKeywords(neKeywordVo, shop, profile);
        }
        return null;
    }

    private NewCreateResultResultVo<SBCommonErrorVo> submitTogetherNeTargeting(NewCreateCampaignRequest request, String campaignId,
                                                              String adGroupId, ShopAuth shop,
                                                              AmazonAdProfile profile) {
        if (StringUtils.isEmpty(request.getNeTargetId()) && CollectionUtils.isNotEmpty(request.getNeTargetsVoList())) {
            //组装商品否投
            AddSbNeTargetingVo neTargetingVo = buildNeTargetingVo(request);
            Optional.ofNullable(campaignId).ifPresent(neTargetingVo::setCampaignId);
            Optional.ofNullable(adGroupId).ifPresent(neTargetingVo::setGroupId);
            return cpcSbNeTargetService.create(neTargetingVo, shop, profile);
        }
        return null;
    }

    private void createTargetAndNeTargetAsync(NewCreateInfoResponse.Builder responseInfo, NewCreateCampaignRequest request,
                                              ShopAuth shop, AmazonAdProfile profile) {
        String campaignId = responseInfo.getCampaignResponse().getCampaignId();
        String adGroupId = responseInfo.getGroupResponse().getAdGroupId();
        CompletableFuture<TargetResponse.Builder> targetFuture = CompletableFuture.supplyAsync(() -> createTargetStep(request, shop, profile, campaignId,
                adGroupId), threadPool).handle((builder, throwable) -> {
            if (Objects.nonNull(throwable)) {
                TargetResponse.Builder targetBuilder = TargetResponse.newBuilder();
                targetBuilder.setTargetErrMsg(throwable.getMessage());
                return targetBuilder;
            }
            return builder;
        });
        CompletableFuture<NeTargetResponse.Builder> neTargetFuture = CompletableFuture.supplyAsync(() -> createNeTargetStep(request, shop, profile, campaignId,
                adGroupId), threadPool).handle((builder, throwable) -> {
            if (Objects.nonNull(throwable)) {
                NeTargetResponse.Builder neTargetBuilder = NeTargetResponse.newBuilder();
                neTargetBuilder.setNeTargetErrMsg(throwable.getMessage());
                return neTargetBuilder;
            }
            return builder;
        });
        CompletableFuture<Void> targetAndNeTargetFuture = CompletableFuture.allOf(targetFuture, neTargetFuture);
        targetAndNeTargetFuture.join();
        try {
            TargetResponse.Builder targetBuild = targetFuture.get();
            NeTargetResponse.Builder neTargetBuild = neTargetFuture.get();
            responseInfo.setTargetResponse(targetBuild.build());
            responseInfo.setNeTargetResponse(neTargetBuild.build());
        } catch (InterruptedException | ExecutionException e) {
            log.error("sb create target and neTarget async create error:{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    private void fillCampaignInfo(NewCreateInfoResponse.Builder responseInfo, AmazonAdCampaignAll campaignInfo) {
        CampaignResponse.Builder campaignResBuilder = CampaignResponse.newBuilder();
        campaignResBuilder.setCampaignId(campaignInfo.getCampaignId());
        responseInfo.setCampaignResponse(campaignResBuilder.build());
    }

    private void fillAdGroupInfo(NewCreateInfoResponse.Builder responseInfo, AmazonSbAdGroup groupInfo) {
        GroupResponse.Builder groupResBuilder = GroupResponse.newBuilder();
        groupResBuilder.setAdGroupId(groupInfo.getAdGroupId());
        responseInfo.setGroupResponse(groupResBuilder.build());
    }
    private void fillAdsInfo(NewCreateInfoResponse.Builder responseInfo, AmazonSbAds adInfo) {
        AdsResponse.Builder adsBuilder = AdsResponse.newBuilder();
        adsBuilder.setAdId(adInfo.getAdId());
        responseInfo.setAdsResponse(adsBuilder.build());
    }
}
