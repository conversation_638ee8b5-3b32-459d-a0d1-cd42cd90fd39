package com.meiyunji.sponsored.service.multiple.targets.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdKeywordExtendDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdKeywordShardingDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeyword;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeywordExtend;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProduct;
import com.meiyunji.sponsored.service.cpc.vo.KeywordsPageParam;
import com.meiyunji.sponsored.service.cpc.vo.KeywordsRankParamVo;
import com.meiyunji.sponsored.service.cpc.vo.ProductVo;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdKeywordDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdKeyword;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.enums.MatchValueEnum;
import com.meiyunji.sponsored.service.enums.SpKeywordGroupValueEnum;
import com.meiyunji.sponsored.service.multiple.targets.dto.*;
import com.meiyunji.sponsored.service.multiple.targets.resp.KeywordsRankResp;
import com.meiyunji.sponsored.service.multiple.targets.resp.TargetResp;
import com.meiyunji.sponsored.service.sellfoxApi.IProductApi;
import com.meiyunji.sponsored.service.vo.ProductAdReportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * sp关键词投放子类
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Slf4j
@Service
public class SpKeywordProcessor extends AbstractTargetProcessor {

    @Resource
    private IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao;

    @Resource
    private IOdsAmazonAdKeywordDao odsAmazonAdKeywordDao;

    @Resource
    private IAmazonAdGroupDao amazonAdGroupDao;

    @Resource
    private IAmazonAdProductDao amazonAdProductDao;

    @Resource
    private IProductApi productDao;

    @Resource
    private IOdsProductDao odsProductDao;

    @Resource
    private IAmazonAdKeywordExtendDao amazonAdKeywordExtendDao;

    @Override
    public void abstractCheckParam(TargetReqDto req) {
        if (StringUtils.isNotBlank(req.getMatchType())) {
            if (StringUtils.isBlank(MatchValueEnum.getMatchValue(req.getMatchType()))) {
                throw new SponsoredBizException("非法的匹配类型！");
            }
        }
        if (StringUtils.isNotBlank(req.getSearchField())) {
            KeywordsPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(req.getSearchField(), KeywordsPageParam.SearchFieldEnum.class);
            if (searchFieldEnum == null) {
                throw new SponsoredBizException("非法的搜索词字段");
            }
        }
    }

    @Override
    public void abstractSetParam(TargetReqDto req) {
        // sp主题类型中英文转换
        if (StringUtils.isNotBlank(req.getSearchValue())) {
            SpKeywordGroupValueEnum keywordGroupValueEnumByTextCn = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByTextCn(req.getSearchValue());
            if (keywordGroupValueEnumByTextCn != null) {
                req.setSearchValue(keywordGroupValueEnumByTextCn.getKeywordText());
            }
        }
        // 设置ABA排名信息
        setAbaRankInfo(req);
    }

    @Override
    public Boolean abstractFilterTargetIds(TargetReqDto req) {
        if (StringUtils.isNotBlank(req.getWordRoot())) {
            List<String> keywordIdList = adManageTargetDorisDao.getKeywordIdListByWordRoot(req);
            if (CollectionUtils.isEmpty(keywordIdList)) {
                return true;
            }else{
                req.setWordRootKeywordIds(keywordIdList);
            }
        }
        return false;
    }

    @Override
    public void abstractBuildWhereSql(TargetReqDto req, StringBuilder whereSql, List<Object> argsList) {
        whereSql.append(" and t.type = 'biddable' "); // sp否定关键词和关键词在同一张表
        // 词根id筛选
        if (CollectionUtils.isNotEmpty(req.getWordRootKeywordIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("t.keyword_id", req.getWordRootKeywordIds(), argsList));
        }
        // 搜索词查询
        if (StringUtils.isNotBlank(req.getSearchField()) && StringUtils.isNotBlank(req.getSearchValue())) {
            //该批量搜索条件默认为：name,不需要条件判断searchField值。
            if ("blur".equals(req.getSearchType())) { //模糊搜索
                whereSql.append(" and lower(t.keyword_text) like ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(req.getSearchValue().toLowerCase()) + "%");
            } else {//默认精确
                List<String> listSearchValue = StringUtil.splitStr(req.getSearchValue().trim(), "%±%");
                if (listSearchValue.size() > 1) {
                    List<String> lowerCaseValue = new ArrayList<>();
                    for (String value : listSearchValue) {
                        lowerCaseValue.add(value.trim().toLowerCase());
                    }
                    whereSql.append(SqlStringUtil.dealInList("lower(t.keyword_text)", lowerCaseValue, argsList));
                } else {
                    whereSql.append("and lower(t.keyword_text) = ? ");
                    argsList.add(req.getSearchValue().trim().toLowerCase());
                }
            }
        }
        // 匹配类型搜索
        if (StringUtils.isNotBlank(req.getMatchType())) {
            whereSql.append(" and t.match_type = ? ");
            argsList.add(req.getMatchType());
        }
    }

    @Override
    public void abstractPrepareData(TargetReqDto req, Boolean export, TargetDataDto dto) {
        // 获取ABA搜索词排名
        prepareAbaRank(req, export, CollectionUtil.newArrayList(dto.getTargetMap().values()), dto);
        // 封装实时排名参数
        if (!export) {
            // 24小时竞价日志
            getOperationLogMap(req, dto);
            // 在线产品信息
            getAdProductMap(req, dto);
            //扩展信息-预估曝光量
            List<TargetExtendInfo> targetExtendInfoList = getTargetExtendInfoList(req);
            dto.setTargetExtendInfoMap(StreamUtil.toMap(targetExtendInfoList, TargetExtendInfo::getTargetId));
        }
    }

    /**
     * 在线产品信息
     */
    private void getAdProductMap(TargetReqDto req, TargetDataDto dto) {
        List<String> adGroupIds = StreamUtil.toList(CollectionUtil.newArrayList(dto.getGroupInfoMap().values()), GroupInfo::getAdGroupId);
        List<AmazonAdProduct> amazonAdProducts = amazonAdProductDao.listValidByGroupIdsAndShopIds(req.getPuid(), req.getShopIdList(), adGroupIds);
        if(CollectionUtil.isNotEmpty(amazonAdProducts)){
            Map<String, OdsProduct> asinImageMap = new HashMap<>();
            Map<String, List<AmazonAdProduct>> productMap = StreamUtil.groupingBy(amazonAdProducts, AmazonAdProduct::getAdGroupId);
            Map<Integer, List<String>> shopSkuMap = StreamUtil.groupingBy(amazonAdProducts, AmazonAdProduct::getShopId, AmazonAdProduct::getSku);
            for (Integer shopId : shopSkuMap.keySet()) {
                List<String> skuList = shopSkuMap.get(shopId);
                List<OdsProduct> odsProducts = odsProductDao.listProductBySkus(req.getPuid(), CollectionUtil.newArrayList(shopId), skuList);
                asinImageMap.putAll(StreamUtil.toMap(odsProducts, it -> it.getShopId() + it.getSku()));
            }
            dto.setAsinImageMap(asinImageMap);
            dto.setGroupProductMap(productMap);
        }
    }

    @Override
    public void abstractBuildParam(TargetResp row, TargetDataDto dto, TargetReqDto req) {
        // 服务状态
        if (StringUtils.isNotBlank(row.getServingStatus())) {
            AmazonAdKeyword.servingStatusEnum byCode = UCommonUtil.getByCode(row.getServingStatus(), AmazonAdKeyword.servingStatusEnum.class);
            if (byCode != null) {
                row.setServingStatusDec(byCode.getDescription());
                row.setServingStatusName(byCode.getName());
            } else {
                row.setServingStatusDec(row.getServingStatus());
                row.setServingStatusName(row.getServingStatus());
            }
        }
        // 关键词实时排名参数封装
        buildRankVo(row, dto);
        // sp主题投放转换
        if (MatchValueEnum.theme.getMatchType().equalsIgnoreCase(row.getMatchType())) {
            SpKeywordGroupValueEnum keywordGroupValueEnumByTextCn = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByText(row.getKeywordText());
            if(keywordGroupValueEnumByTextCn != null){
                row.setKeywordText(keywordGroupValueEnumByTextCn.getTextCn());
            }
        }
    }

    private static void buildRankVo(TargetResp row, TargetDataDto dto) {
        AmznEndpoint endpoint = AmznEndpoint.getByMarketplaceId(row.getMarketplaceId());
        KeywordsRankResp rankVo = new KeywordsRankResp();
        rankVo.setId(row.getId());
        rankVo.setSiteName(endpoint.getMarketplaceCN());
        rankVo.setSiteId(endpoint.getMarketplace());
        rankVo.setKeywordId(row.getKeywordId());
        rankVo.setKeyword(row.getKeywordText());
        String searchUrl = "http://" + endpoint.getDomain() + "/s?k=" + row.getKeywordText();
        rankVo.setUrl(searchUrl);
        Map<String, OdsProduct> asinImageMap = dto.getAsinImageMap();
        Map<String, List<AmazonAdProduct>> groupProductMap = dto.getGroupProductMap();
        List<AmazonAdProduct> amazonAdProducts = groupProductMap.get(row.getAdGroupId());
        List<ProductVo> productVos = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(amazonAdProducts)){
            for (AmazonAdProduct product : amazonAdProducts) {
                ProductVo productVo = new ProductVo();
                OdsProduct odsProduct = asinImageMap.get(product.getShopId() + product.getSku());
                if (StringUtils.isNotBlank(product.getAsin())) {
                    productVo.setAsin(product.getAsin());
                    productVo.setAsinUrl(StringUtil.getAmzProductUrlBySite(product.getAsin(), row.getMarketplaceId()));
                }
                if(odsProduct != null){
                    productVo.setMainImage(odsProduct.getMainImage());
                }
                productVos.add(productVo);
            }
        }
        rankVo.setProducts(productVos);
        row.setRankVo(rankVo);
    }

    @Override
    public List<TargetInfo> abstractMysqlTargetList(TargetReqDto req) {
        List<TargetInfo> targetList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(req.getTargetIds())){
            // 分批获取
            List<List<String>> targetIdsList = Lists.partition(req.getTargetIds(), 10000);
            for (List<String> targetIds : targetIdsList) {
                List<AmazonAdKeyword> keywordList = amazonAdKeywordShardingDao.getListKeywordByKeywordIds(req.getPuid(), req.getShopIdList(), targetIds);
                for (AmazonAdKeyword amazonAdKeyword : keywordList) {
                    TargetInfo targetInfo = BeanUtil.copyProperties(amazonAdKeyword, TargetInfo.class);
                    targetInfo.setTargetId(amazonAdKeyword.getKeywordId());
                    targetList.add(targetInfo);
                }
            }
        }
        return targetList;
    }

    private List<TargetExtendInfo> getTargetExtendInfoList(TargetReqDto req) {
        List<TargetExtendInfo> targetExtendInfoList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(req.getTargetIds())){
            // 分批获取
            List<List<String>> targetIdsList = Lists.partition(req.getTargetIds(), 10000);
            for (List<String> targetIds : targetIdsList) {
                List<TargetExtendInfo> keywordExtendList = amazonAdKeywordExtendDao.selectByShopIdAndKeywordIdList(req.getPuid(), req.getShopIdList(), targetIds);
                if (CollectionUtils.isNotEmpty(keywordExtendList)) {
                    targetExtendInfoList.addAll(keywordExtendList);
                }
            }
        }
        return targetExtendInfoList;
    }

    public List<TargetInfo> abstractDorisTargetList(TargetReqDto req) {
        List<TargetInfo> targetList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(req.getTargetIds())){
            // 分批获取
            List<List<String>> targetIdsList = Lists.partition(req.getTargetIds(), 9000);
            for (List<String> targetIds : targetIdsList) {
                List<OdsAmazonAdKeyword> keywordList = odsAmazonAdKeywordDao.getByKeywordIdsAndShopIds(req.getPuid(), req.getShopIdList(), targetIds);
                for (OdsAmazonAdKeyword amazonAdKeyword : keywordList) {
                    TargetInfo targetInfo = BeanUtil.copyProperties(amazonAdKeyword, TargetInfo.class);
                    targetInfo.setTargetId(amazonAdKeyword.getKeywordId());
                    targetList.add(targetInfo);
                }
            }
        }
        return targetList;
    }

    @Override
    public List<GroupInfo> abstractGroupInfoList(TargetReqDto req, List<String> adGroupIdList) {
        List<GroupInfo> groupInfoList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(adGroupIdList)){
            // 分批获取
            List<List<String>> adGroupIdsList = Lists.partition(adGroupIdList, 10000);
            for (List<String> adGroupIds : adGroupIdsList) {
                List<AmazonAdGroup> adGroupInfoList = amazonAdGroupDao.getListByShopIdsAndGroupIds(req.getPuid(), req.getShopIdList(), adGroupIds);
                for (AmazonAdGroup adGroup : adGroupInfoList) {
                    GroupInfo groupInfo = BeanUtil.copyProperties(adGroup, GroupInfo.class);
                    groupInfoList.add(groupInfo);
                }
            }
        }
        return groupInfoList;
    }

    @Override
    public List<String> excludeFiledList(TargetReqDto req) {
        return Lists.newArrayList("ordersNewToBrandFTD", "orderRateNewToBrandFTD", "salesNewToBrandFTD", "salesRateNewToBrandFTD", "ordersNewToBrandPercentageFTD",
                "Video5SecondViews", "Video5SecondViewRate", "VideoFirstQuartileViews", "VideoMidpointViews", "VideoThirdQuartileViews", "VideoCompleteViews", "VideoUnmutes", "ViewabilityRate",
                "ViewClickThroughRate", "BrandedSearches", "ViewImpressions", "target","selectType","vcpm","newToBrandDetailPageViews","addToCart","addToCartRate","ecpAddToCart","detailPageViews");
    }

}
