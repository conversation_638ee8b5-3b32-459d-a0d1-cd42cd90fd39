package com.meiyunji.sponsored.service.cpc.service.impl;

import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeChartRpcVo;
import com.meiyunji.sponsored.service.cpc.util.SplitDateByWeekUtil;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformanceNewVo;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformanceNewDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2021/8/31 15:18
 * @describe: chart图数据处理统一处理类
 */
@Component
@Slf4j
public class AdChartDataProcessNew {


    //按日汇总数据
    public List<AdHomeChartRpcVo> getDayPerformanceVos(String currency, List<AdHomePerformanceNewDto> list,BigDecimal shopSales, boolean isVc) {

        long startTime = System.currentTimeMillis();

        Map<String, Long> dayClick = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingLong(AdHomePerformanceNewDto::getClicks)));
        Map<String, Long> dayImpressions = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingLong(AdHomePerformanceNewDto::getImpressions)));
        Map<String, Long> dayAdOrderNum = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingLong(AdHomePerformanceNewDto::getAdOrderNum)));
        Map<String, Double> dayAdCost = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingDouble(item -> (item.getAdCost().doubleValue()))));
        Map<String, Double> dayAdSale = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingDouble(item -> (item.getAdSale().doubleValue()))));
        Map<String, Long> dayAdSaleNum = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingLong(item -> (item.getAdSaleNum() != null ? item.getAdSaleNum() : 0))));
        Map<String, Double> dayAdSales = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingDouble(item -> (item.getAdSales() != null ? item.getAdSales().doubleValue() : 0))));
        Map<String, Long> dayOrderNum = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingLong(item -> (item.getSalesNum() != null ? item.getSalesNum() : 0))));
        Map<String, Long> dayAdSelfSaleNum = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingLong(item -> (item.getOrderNum() != null ? item.getOrderNum() : 0))));
        Map<String, Long> dayViewImpressions = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingLong(item -> (item.getViewImpressions() != null ? item.getViewImpressions() : 0))));
        Map<String, Long> dayOrdersNewToBrand14d = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingLong(item -> (item.getOrdersNewToBrand14d() != null ? item.getOrdersNewToBrand14d() : 0))));
        Map<String, Double> daySalesNewToBrand14d = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingDouble(item -> (item.getSalesNewToBrand14d() != null ? item.getSalesNewToBrand14d().doubleValue() : 0.0))));
        Map<String, Long> dayUnitsOrderedNewToBrand14d = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingLong(item -> (item.getUnitsOrderedNewToBrand14d() != null ? item.getUnitsOrderedNewToBrand14d() : 0))));
        Map<String, Long> dayImpressionRank = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingLong(item -> (item.getImpressionRank() != null ? item.getImpressionRank() : 0))));
        Map<String, Double> dayImpressionShare = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingDouble(item -> (item.getImpressionShare() != null ? item.getImpressionShare() : 0))));

        List<String> dateList = list.stream().filter(Objects::nonNull).map(AdHomePerformanceNewDto::getCountDate).distinct().sorted().collect(Collectors.toList());
        List<AdHomePerformanceNewVo> campaignPerformanceVos = new ArrayList<>();

        dateList.stream().filter(Objects::nonNull).forEach(item -> {
            Double adSale = Optional.ofNullable(dayAdSale.get(item)).orElse(0.0);
            Double adCost = Optional.ofNullable(dayAdCost.get(item)).orElse(0.0);
            Long clicks = Optional.ofNullable(dayClick.get(item)).orElse(0L);
            Long adOrderNum = Optional.ofNullable(dayAdOrderNum.get(item)).orElse(0L);
            Long impressions = Optional.ofNullable(dayImpressions.get(item)).orElse(0L);
            Long viewImpressions = Optional.ofNullable(dayViewImpressions.get(item)).orElse(0L);
            Long adSaleNum = Optional.ofNullable(dayAdSaleNum.get(item)).orElse(0L);
            Double adSales = Optional.ofNullable(dayAdSales.get(item)).orElse(0.0);
            Long orderNum = Optional.ofNullable(dayOrderNum.get(item)).orElse(0L);
            Long adSelfSaleNum = Optional.ofNullable(dayAdSelfSaleNum.get(item)).orElse(0L);
            Long ordersNewToBrand14d = Optional.ofNullable(dayOrdersNewToBrand14d.get(item)).orElse(0L);
            Double salesNewToBrand14d = Optional.ofNullable(daySalesNewToBrand14d.get(item)).orElse(0.0);
            Long unitsOrderedNewToBrand14d = Optional.ofNullable(dayUnitsOrderedNewToBrand14d.get(item)).orElse(0L);
            Long impressionRank = Optional.ofNullable(dayImpressionRank.get(item)).orElse(0L);
            Double impressionShare = Optional.ofNullable(dayImpressionShare.get(item)).orElse(0.0);
            AdHomePerformanceNewVo campaignPerformanceVo = AdHomePerformanceNewVo.builder()
                    .date(DateUtil.dateToStrWithFormat(DateUtil.strToDate(item, DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN))
                    .clicks(clicks)
                    .impressions(impressions)
                    .adOrderNum(adOrderNum)
                    .acots(shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adCost).multiply(new BigDecimal("100")).divide(shopSales, 4, BigDecimal.ROUND_HALF_UP))
                    .asots(shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adSale).multiply(new BigDecimal("100")).divide(shopSales, 4, BigDecimal.ROUND_HALF_UP))
                    .acos(BigDecimal.valueOf(adSale).compareTo(BigDecimal.ZERO) == 0 || BigDecimal.valueOf(adCost).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adCost).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(adSale), 4, BigDecimal.ROUND_HALF_UP))
                    .roas(BigDecimal.valueOf(adSale).compareTo(BigDecimal.ZERO) == 0 || BigDecimal.valueOf(adCost).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adSale).divide(BigDecimal.valueOf(adCost), 4, BigDecimal.ROUND_HALF_UP))
                    .adCostPerClick(clicks == 0 || BigDecimal.valueOf(adCost).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adCost).divide(BigDecimal.valueOf(clicks), 4, BigDecimal.ROUND_HALF_UP))
                    .adSale(BigDecimal.valueOf(adSale))
                    .adCost(BigDecimal.valueOf(adCost))
                    .ctr(impressions == 0 || clicks == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(clicks).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(impressions), 4, BigDecimal.ROUND_HALF_UP))
                    .cvr(clicks == 0 || adOrderNum == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adOrderNum).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(clicks), 4, BigDecimal.ROUND_HALF_UP))
                    .viewImpressions(viewImpressions)
                    .cpa(adOrderNum == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adCost).divide(BigDecimal.valueOf(adOrderNum), 4, BigDecimal.ROUND_HALF_UP))
                    .vcpm(viewImpressions == 0 ? BigDecimal.ZERO : new BigDecimal(adCost).multiply(new BigDecimal("1000")).divide(new BigDecimal(viewImpressions), 4, BigDecimal.ROUND_HALF_UP))
                    .adSaleNum(adSaleNum)
                    .adOtherOrderNum(adOrderNum - adSaleNum)
                    .adSales(BigDecimal.valueOf(adSales))
                    .adOtherSales(BigDecimal.valueOf(adSale).subtract(BigDecimal.valueOf(adSales)))
                    .orderNum(orderNum)
                    .adSelfSaleNum(adSelfSaleNum)
                    .adOtherSaleNum(orderNum - adSelfSaleNum)
                    .ordersNewToBrand14d(ordersNewToBrand14d)
                    .orderRateNewToBrand14d(adOrderNum == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(ordersNewToBrand14d).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(adOrderNum), 4 , BigDecimal.ROUND_HALF_UP))
                    .salesNewToBrand14d(BigDecimal.valueOf(salesNewToBrand14d))
                    .salesRateNewToBrand14d(BigDecimal.valueOf(adSale).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(salesNewToBrand14d).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(adSale), 4, BigDecimal.ROUND_HALF_UP))
                    .unitsOrderedNewToBrand14d(unitsOrderedNewToBrand14d)
                    .unitsOrderedRateNewToBrand14d(orderNum == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(unitsOrderedNewToBrand14d).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(orderNum), 4, BigDecimal.ROUND_HALF_UP))
                    .ordersNewToBrandPercentage14d(clicks == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(ordersNewToBrand14d).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(clicks), 4 ,BigDecimal.ROUND_HALF_UP))
                    .impressionRank(impressionRank)
                    .impressionShare(impressionShare)
                    .build();
            campaignPerformanceVos.add(campaignPerformanceVo);
        });

        log.info("============================== 处理图表日数据花费 {} ==============================", System.currentTimeMillis() - startTime);
        return getChartData(list.size(), currency, campaignPerformanceVos, isVc);
    }


    //按月汇总数据
    public List<AdHomeChartRpcVo> getMonthPerformanceVos(String currency, List<AdHomePerformanceNewDto> list,BigDecimal shopSales, boolean isVc) {

        long startTime = System.currentTimeMillis();
        Map<String, Long> monthClick = list.parallelStream().collect(Collectors.groupingBy(item -> DateUtil.dateToStrWithFormat(DateUtil.strToDate(item.getCountDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN_MONTH), Collectors.summingLong(AdHomePerformanceNewDto::getClicks)));
        Map<String, Long> monthImpressions = list.parallelStream().collect(Collectors.groupingBy(item -> DateUtil.dateToStrWithFormat(DateUtil.strToDate(item.getCountDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN_MONTH), Collectors.summingLong(AdHomePerformanceNewDto::getImpressions)));
        Map<String, Long> monthAdOrderNum = list.parallelStream().collect(Collectors.groupingBy(item -> DateUtil.dateToStrWithFormat(DateUtil.strToDate(item.getCountDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN_MONTH), Collectors.summingLong(AdHomePerformanceNewDto::getAdOrderNum)));
        Map<String, Double> monthAdCost = list.parallelStream().collect(Collectors.groupingBy(item -> DateUtil.dateToStrWithFormat(DateUtil.strToDate(item.getCountDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN_MONTH), Collectors.summingDouble(item -> (item.getAdCost().doubleValue()))));
        Map<String, Double> monthAdSale = list.parallelStream().collect(Collectors.groupingBy(item -> DateUtil.dateToStrWithFormat(DateUtil.strToDate(item.getCountDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN_MONTH), Collectors.summingDouble(item -> (item.getAdSale().doubleValue()))));
        Map<String, Long> monthAdSaleNum = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingLong(item -> (item.getAdSaleNum() != null ? item.getAdSaleNum() : 0))));
        Map<String, Double> monthAdSales = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingDouble(item -> (item.getAdSales() != null ? item.getAdSales().doubleValue() : 0))));
        Map<String, Long> monthOrderNum = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingLong(item -> (item.getSalesNum() != null ? item.getSalesNum() : 0))));
        Map<String, Long> monthAdSelfSaleNum = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingLong(item -> (item.getOrderNum() != null ? item.getOrderNum() : 0))));
        Map<String, Long> monthViewImpressions = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingLong(item -> (item.getViewImpressions() != null ? item.getViewImpressions() : 0))));
        Map<String, Long> monthOrdersNewToBrand14d = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingLong(item -> (item.getOrdersNewToBrand14d() != null ? item.getOrdersNewToBrand14d() : 0))));
        Map<String, Double> monthSalesNewToBrand14d = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingDouble(item -> (item.getSalesNewToBrand14d() != null ? item.getSalesNewToBrand14d().doubleValue() : 0.0))));
        Map<String, Long> monthUnitsOrderedNewToBrand14d = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingLong(item -> (item.getUnitsOrderedNewToBrand14d() != null ? item.getUnitsOrderedNewToBrand14d() : 0))));
        Map<String, Long> monthImpressionRank = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingLong(item -> (item.getImpressionRank() != null ? item.getImpressionRank() : 0))));
        Map<String, Double> monthImpressionShare = list.parallelStream().collect(Collectors.groupingBy(AdHomePerformanceNewDto::getCountDate, Collectors.summingDouble(item -> (item.getImpressionShare() != null ? item.getImpressionShare() : 0))));

        List<String> dateList = list.stream().filter(Objects::nonNull).map(item -> {
            return DateUtil.dateToStrWithFormat(DateUtil.strToDate(item.getCountDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN_MONTH);
        }).distinct().sorted().collect(Collectors.toList());

        List<AdHomePerformanceNewVo> campaignPerformanceVos = new ArrayList<>();

        dateList.stream().filter(Objects::nonNull).forEach(item -> {
            Double adSale = Optional.ofNullable(monthAdSale.get(item)).orElse(0.0);
            Double adCost = Optional.ofNullable(monthAdCost.get(item)).orElse(0.0);
            Long clicks = Optional.ofNullable(monthClick.get(item)).orElse(0L);
            Long adOrderNum = Optional.ofNullable(monthAdOrderNum.get(item)).orElse(0L);
            Long impressions = Optional.ofNullable(monthImpressions.get(item)).orElse(0L);
            Long viewImpressions = Optional.ofNullable(monthViewImpressions.get(item)).orElse(0L);
            Long adSaleNum = Optional.ofNullable(monthAdSaleNum.get(item)).orElse(0L);
            Double adSales = Optional.ofNullable(monthAdSales.get(item)).orElse(0.0);
            Long orderNum = Optional.ofNullable(monthOrderNum.get(item)).orElse(0L);
            Long adSelfSaleNum = Optional.ofNullable(monthAdSelfSaleNum.get(item)).orElse(0L);
            Long ordersNewToBrand14d = Optional.ofNullable(monthOrdersNewToBrand14d.get(item)).orElse(0L);
            Double salesNewToBrand14d = Optional.ofNullable(monthSalesNewToBrand14d.get(item)).orElse(0.0);
            Long unitsOrderedNewToBrand14d = Optional.ofNullable(monthUnitsOrderedNewToBrand14d.get(item)).orElse(0L);
            Long impressionRank = Optional.ofNullable(monthImpressionRank.get(item)).orElse(0L);
            Double impressionShare = Optional.ofNullable(monthImpressionShare.get(item)).orElse(0.0);
            AdHomePerformanceNewVo campaignPerformanceVo = AdHomePerformanceNewVo.builder()
                    .date(item)
                    .clicks(clicks)
                    .impressions(impressions)
                    .adOrderNum(adOrderNum)
                    .acots(shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adCost).multiply(new BigDecimal("100")).divide(shopSales, 4, BigDecimal.ROUND_HALF_UP))
                    .asots(shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adSale).multiply(new BigDecimal("100")).divide(shopSales, 4, BigDecimal.ROUND_HALF_UP))
                    .acos(BigDecimal.valueOf(adSale).compareTo(BigDecimal.ZERO) == 0 || BigDecimal.valueOf(adCost).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adCost).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(adSale), 4, BigDecimal.ROUND_HALF_UP))
                    .roas(BigDecimal.valueOf(adSale).compareTo(BigDecimal.ZERO) == 0 || BigDecimal.valueOf(adCost).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adSale).divide(BigDecimal.valueOf(adCost), 4, BigDecimal.ROUND_HALF_UP))
                    .adCostPerClick(clicks == 0 || BigDecimal.valueOf(adCost).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adCost).divide(BigDecimal.valueOf(clicks), 4, BigDecimal.ROUND_HALF_UP))
                    .adSale(BigDecimal.valueOf(adSale))
                    .adCost(BigDecimal.valueOf(adCost))
                    .ctr(impressions == 0 || clicks == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(clicks).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(impressions), 4, BigDecimal.ROUND_HALF_UP))
                    .cvr(clicks == 0 || adOrderNum == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adOrderNum).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(clicks), 4, BigDecimal.ROUND_HALF_UP))
                    .viewImpressions(viewImpressions)
                    .cpa(adOrderNum == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adCost).divide(BigDecimal.valueOf(adOrderNum), 4, BigDecimal.ROUND_HALF_UP))
                    .vcpm(viewImpressions == 0 ? BigDecimal.ZERO : new BigDecimal(adCost).multiply(new BigDecimal("1000")).divide(new BigDecimal(viewImpressions), 4, BigDecimal.ROUND_HALF_UP))
                    .adSaleNum(adSaleNum)
                    .adOtherOrderNum(adOrderNum - adSaleNum)
                    .adSales(BigDecimal.valueOf(adSales))
                    .adOtherSales(BigDecimal.valueOf(adSale).subtract(BigDecimal.valueOf(adSales)))
                    .orderNum(orderNum)
                    .adSelfSaleNum(adSelfSaleNum)
                    .adOtherSaleNum(orderNum - adSelfSaleNum)
                    .ordersNewToBrand14d(ordersNewToBrand14d)
                    .orderRateNewToBrand14d(adOrderNum == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(ordersNewToBrand14d).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(adOrderNum), 4 , BigDecimal.ROUND_HALF_UP))
                    .salesNewToBrand14d(BigDecimal.valueOf(salesNewToBrand14d))
                    .salesRateNewToBrand14d(BigDecimal.valueOf(adSale).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(salesNewToBrand14d).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(adSale), 4, BigDecimal.ROUND_HALF_UP))
                    .unitsOrderedNewToBrand14d(unitsOrderedNewToBrand14d)
                    .unitsOrderedRateNewToBrand14d(orderNum == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(unitsOrderedNewToBrand14d).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(orderNum), 4, BigDecimal.ROUND_HALF_UP))
                    .ordersNewToBrandPercentage14d(clicks == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(ordersNewToBrand14d).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(clicks), 4 ,BigDecimal.ROUND_HALF_UP))
                    .impressionRank(impressionRank)
                    .impressionShare(impressionShare)
                    .build();
            campaignPerformanceVos.add(campaignPerformanceVo);
        });

        log.info("============================== 处理图表月数据花费 {} ==============================", System.currentTimeMillis() - startTime);
        return getChartData(list.size(), currency, campaignPerformanceVos, isVc);
    }


    //按周汇总数据
    public List<AdHomeChartRpcVo> getWeekPerformanceVos(String currency, String startDate, String endDate, List<AdHomePerformanceNewDto> list,BigDecimal shopSales, boolean isVc) {
        long startTime = System.currentTimeMillis();

        List<List<String>> weekDate = SplitDateByWeekUtil.split(DateUtil.stringToDate(startDate), DateUtil.stringToDate(endDate));
        Map<String, Long> weekClick = new ConcurrentHashMap<>();
        Map<String, Long> weekImpressions = new ConcurrentHashMap<>();
        Map<String, Long> weekAdOrderNum = new ConcurrentHashMap<>();
        Map<String, Double> weekAdCost = new ConcurrentHashMap<>();
        Map<String, Double> weekAdSale = new ConcurrentHashMap<>();
        Map<String, Long> weekViewImpressions = new ConcurrentHashMap<>();
        Map<String, Long> weekAdSaleNum = new ConcurrentHashMap<>();
        Map<String, Double> weekAdSales = new ConcurrentHashMap<>();
        Map<String, Long> weekOrderNum = new ConcurrentHashMap<>();
        Map<String, Long> weekAdSelfSaleNum = new ConcurrentHashMap<>();
        Map<String, Long> weekOrdersNewToBrand14d = new ConcurrentHashMap<>();
        Map<String, Double> weekSalesNewToBrand14d = new ConcurrentHashMap<>();
        Map<String, Long> weekUnitsOrderedNewToBrand14d = new ConcurrentHashMap<>();
        Map<String, Long> weekImpressionRank = new ConcurrentHashMap<>();
        Map<String, Double> weekImpressionShare = new ConcurrentHashMap<>();

        weekDate.parallelStream().filter(Objects::nonNull).forEach(week -> {
            String weekDay = week.stream().collect(Collectors.joining("~"));

            long wClicks = 0;
            long wImpressions = 0;
            long wAdOrderNum = 0;
            double wAdCost = 0;
            double wAdSale = 0;
            long wViewImpressions = 0;
            long wAdSaleNum = 0;
            double wAdSales = 0;
            long wOrderNum = 0;
            long wAdSelfSaleNum = 0;
            long wOrdersNewToBrand14d = 0;
            double wSalesNewToBrand14d = 0;
            long wUnitsOrderedNewToBrand14d = 0;
            long wImpressionRank = 0;
            double wImpressionShare = 0;
            List<AdHomePerformanceNewDto> wList = list.parallelStream().filter(Objects::nonNull).
                    filter(item -> DateUtil.compareDate(DateUtil.strToDate(item.getCountDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.strToDate4(week.get(0))) >= 0
                            && DateUtil.compareDate(DateUtil.strToDate(item.getCountDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.strToDate4(week.get(1))) <= 0)
                    .collect(Collectors.toList());


            for (AdHomePerformanceNewDto dto : wList) {
                wClicks += dto.getClicks();
                wImpressions += dto.getImpressions();
                wAdOrderNum += dto.getAdOrderNum();
                wAdCost += dto.getAdCost().doubleValue();
                wAdSale += dto.getAdSale().doubleValue();
                wAdSaleNum += dto.getAdSaleNum() != null ? dto.getAdSaleNum() : 0;
                wAdSales += dto.getAdSales() != null ? dto.getAdSales().doubleValue() : 0;
                wAdSelfSaleNum += dto.getOrderNum() != null ? dto.getOrderNum() : 0;
                wOrderNum += dto.getSalesNum() != null ? dto.getSalesNum() : 0;
                wViewImpressions += dto.getViewImpressions() != null ? dto.getViewImpressions() : 0;
                wOrdersNewToBrand14d += dto.getOrdersNewToBrand14d() != null ? dto.getOrdersNewToBrand14d() : 0;
                wSalesNewToBrand14d += dto.getSalesNewToBrand14d() != null ? dto.getSalesNewToBrand14d().doubleValue() : 0;
                wUnitsOrderedNewToBrand14d += dto.getUnitsOrderedNewToBrand14d() != null ? dto.getUnitsOrderedNewToBrand14d() : 0;
                wImpressionRank += dto.getImpressionRank() != null ? dto.getImpressionRank() : 0;
                wImpressionShare += dto.getImpressionShare() != null ? dto.getImpressionShare().doubleValue() : 0;
            }

            weekClick.put(weekDay, wClicks);
            weekAdOrderNum.put(weekDay, wAdOrderNum);
            weekAdCost.put(weekDay, wAdCost);
            weekAdSale.put(weekDay, wAdSale);
            weekImpressions.put(weekDay, wImpressions);
            weekViewImpressions.put(weekDay, wViewImpressions);
            weekAdSaleNum.put(weekDay, wAdSaleNum);
            weekAdSales.put(weekDay, wAdSales);
            weekOrderNum.put(weekDay, wOrderNum);
            weekAdSelfSaleNum.put(weekDay, wAdSelfSaleNum);
            weekOrdersNewToBrand14d.put(weekDay, wOrdersNewToBrand14d);
            weekSalesNewToBrand14d.put(weekDay, wSalesNewToBrand14d);
            weekUnitsOrderedNewToBrand14d.put(weekDay, wUnitsOrderedNewToBrand14d);
            weekImpressionRank.put(weekDay, wImpressionRank);
            weekImpressionShare.put(weekDay, wImpressionShare);
        });

        List<String> weekList = weekDate.stream().map(item -> {
            return item.stream().collect(Collectors.joining("~"));
        }).collect(Collectors.toList());

        List<AdHomePerformanceNewVo> campaignPerformanceVos = new ArrayList<>();

        weekList.stream().filter(Objects::nonNull).forEach(item -> {
            Double adSale = Optional.ofNullable(weekAdSale.get(item)).orElse(0.0);
            Double adCost = Optional.ofNullable(weekAdCost.get(item)).orElse(0.0);
            Long clicks = Optional.ofNullable(weekClick.get(item)).orElse(0L);
            Long adOrderNum = Optional.ofNullable(weekAdOrderNum.get(item)).orElse(0L);
            Long impressions = Optional.ofNullable(weekImpressions.get(item)).orElse(0L);
            Long viewImpressions = Optional.ofNullable(weekViewImpressions.get(item)).orElse(0L);
            Long adSaleNum = Optional.ofNullable(weekAdSaleNum.get(item)).orElse(0L);
            Double adSales = Optional.ofNullable(weekAdSales.get(item)).orElse(0.0);
            Long orderNum = Optional.ofNullable(weekOrderNum.get(item)).orElse(0L);
            Long adSelfSaleNum = Optional.ofNullable(weekAdSelfSaleNum.get(item)).orElse(0L);
            Long ordersNewToBrand14d = Optional.ofNullable(weekOrdersNewToBrand14d.get(item)).orElse(0L);
            Double salesNewToBrand14d = Optional.ofNullable(weekSalesNewToBrand14d.get(item)).orElse(0.0);
            Long unitsOrderedNewToBrand14d = Optional.ofNullable(weekUnitsOrderedNewToBrand14d.get(item)).orElse(0L);
            Long impressionRank = Optional.ofNullable(weekImpressionRank.get(item)).orElse(0L);
            Double impressionShare = Optional.ofNullable(weekImpressionShare.get(item)).orElse(0.0);
            AdHomePerformanceNewVo campaignPerformanceVo = AdHomePerformanceNewVo.builder()
                    .date(item)
                    .clicks(clicks)
                    .impressions(impressions)
                    .adOrderNum(adOrderNum)
                    .acots(shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adCost).multiply(new BigDecimal("100")).divide(shopSales, 4, BigDecimal.ROUND_HALF_UP))
                    .asots(shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adSale).multiply(new BigDecimal("100")).divide(shopSales, 4, BigDecimal.ROUND_HALF_UP))
                    .acos(BigDecimal.valueOf(adSale).compareTo(BigDecimal.ZERO) == 0 || BigDecimal.valueOf(adCost).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adCost).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(adSale), 4, BigDecimal.ROUND_HALF_UP))
                    .roas(BigDecimal.valueOf(adSale).compareTo(BigDecimal.ZERO) == 0 || BigDecimal.valueOf(adCost).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adSale).divide(BigDecimal.valueOf(adCost), 4, BigDecimal.ROUND_HALF_UP))
                    .adCostPerClick(clicks == 0 || BigDecimal.valueOf(adCost).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adCost).divide(BigDecimal.valueOf(clicks), 4, BigDecimal.ROUND_HALF_UP))
                    .adSale(BigDecimal.valueOf(adSale))
                    .adCost(BigDecimal.valueOf(adCost))
                    .ctr(impressions == 0 || clicks == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(clicks).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(impressions), 4, BigDecimal.ROUND_HALF_UP))
                    .cvr(clicks == 0 || adOrderNum == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adOrderNum).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(clicks), 4, BigDecimal.ROUND_HALF_UP))
                    .viewImpressions(viewImpressions)
                    .cpa(adOrderNum == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(adCost).divide(BigDecimal.valueOf(adOrderNum), 4, BigDecimal.ROUND_HALF_UP))
                    .vcpm(viewImpressions == 0 ? BigDecimal.ZERO : new BigDecimal(adCost).multiply(new BigDecimal("1000")).divide(new BigDecimal(viewImpressions), 4, BigDecimal.ROUND_HALF_UP))
                    .adSaleNum(adSaleNum)
                    .adOtherOrderNum(adOrderNum - adSaleNum)
                    .adSales(BigDecimal.valueOf(adSales))
                    .adOtherSales(BigDecimal.valueOf(adSale).subtract(BigDecimal.valueOf(adSales)))
                    .orderNum(orderNum)
                    .adSelfSaleNum(adSelfSaleNum)
                    .adOtherSaleNum(orderNum - adSelfSaleNum)
                    .ordersNewToBrand14d(ordersNewToBrand14d)
                    .orderRateNewToBrand14d(adOrderNum == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(ordersNewToBrand14d).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(adOrderNum), 4 , BigDecimal.ROUND_HALF_UP))
                    .salesNewToBrand14d(BigDecimal.valueOf(salesNewToBrand14d))
                    .salesRateNewToBrand14d(BigDecimal.valueOf(adSale).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(salesNewToBrand14d).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(adSale), 4, BigDecimal.ROUND_HALF_UP))
                    .unitsOrderedNewToBrand14d(unitsOrderedNewToBrand14d)
                    .unitsOrderedRateNewToBrand14d(orderNum == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(unitsOrderedNewToBrand14d).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(orderNum), 4, BigDecimal.ROUND_HALF_UP))
                    .ordersNewToBrandPercentage14d(clicks == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(ordersNewToBrand14d).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(clicks), 4 ,BigDecimal.ROUND_HALF_UP))
                    .impressionRank(impressionRank)
                    .impressionShare(impressionShare)
                    .build();
            campaignPerformanceVos.add(campaignPerformanceVo);
        });

        List<AdHomePerformanceNewVo> sortVo = campaignPerformanceVos.stream().sorted(Comparator.comparing(AdHomePerformanceNewVo::getDate)).collect(Collectors.toList());

        log.info("============================== 处理图表周数据花费 {} ==============================", System.currentTimeMillis() - startTime);
        return getChartData(list.size(), currency, sortVo, isVc);
    }


    /**
     * 转成曲线图标数据
     *
     * @param currency
     * @param campaignPerformanceVos
     * @return
     */
    public static List<AdHomeChartRpcVo> getChartData(Integer size, String currency, List<AdHomePerformanceNewVo> campaignPerformanceVos, boolean isVc) {


        List<AdHomeChartRpcVo> adHomeChartVos = new ArrayList<>();
        //封装数据
        List<AdHomeChartRpcVo.ChartRpcRecord> ctrList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> clicksList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> impressionsList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> cvrList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adSaleList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adCostPerClickList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adCostList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adOrderNumList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> acosList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> roasList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> acotsList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> asotsList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> viewImpressionsList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> cpaList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> vcpmList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adSaleNumList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adOtherOrderNumList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adSalesList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adOtherSalesList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> orderNumList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adSelfSaleNumList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adOtherSaleNumList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> ordersNewToBrand14dList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> orderRateNewToBrand14dList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> salesNewToBrand14dList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> salesRateNewToBrand14dList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> unitsOrderedNewToBrand14dList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> unitsOrderedRateNewToBrand14dList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> ordersNewToBrandPercentage14dList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> impressionRankList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> impressionShareList = Lists.newCopyOnWriteArrayList();

        campaignPerformanceVos.parallelStream().filter(Objects::nonNull).forEachOrdered(item -> {
            AdHomeChartRpcVo.ChartRpcRecord.Builder ctr = AdHomeChartRpcVo.ChartRpcRecord.newBuilder();
            ctr.setDate(item.getDate());
            ctr.setValue(String.valueOf(item.getCtr()));

            ctrList.add(ctr.build());

            AdHomeChartRpcVo.ChartRpcRecord clicks = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getClicks()))
                    .build();
            clicksList.add(clicks);

            AdHomeChartRpcVo.ChartRpcRecord impression = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getImpressions()))
                    .build();
            impressionsList.add(impression);

            AdHomeChartRpcVo.ChartRpcRecord cvr = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getCvr()))
                    .build();
            cvrList.add(cvr);

            AdHomeChartRpcVo.ChartRpcRecord adSale = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getAdSale()))
                    .build();
            adSaleList.add(adSale);

            AdHomeChartRpcVo.ChartRpcRecord adCostPerClick = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getAdCostPerClick()))
                    .build();
            adCostPerClickList.add(adCostPerClick);

            AdHomeChartRpcVo.ChartRpcRecord adCost = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getAdCost()))
                    .build();
            adCostList.add(adCost);

            AdHomeChartRpcVo.ChartRpcRecord acos = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getAcos()))
                    .build();
            acosList.add(acos);

            AdHomeChartRpcVo.ChartRpcRecord roas = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getRoas()))
                    .build();
            roasList.add(roas);


            AdHomeChartRpcVo.ChartRpcRecord acots = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getAcots()))
                    .build();
            acotsList.add(acots);

            AdHomeChartRpcVo.ChartRpcRecord asots = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getAsots()))
                    .build();
            asotsList.add(asots);


            AdHomeChartRpcVo.ChartRpcRecord adOrderNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getAdOrderNum()))
                    .build();
            adOrderNumList.add(adOrderNum);

            AdHomeChartRpcVo.ChartRpcRecord viewImpressions = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getViewImpressions()))
                    .build();
            viewImpressionsList.add(viewImpressions);

            AdHomeChartRpcVo.ChartRpcRecord cpa = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getCpa()))
                    .build();
            cpaList.add(cpa);

            AdHomeChartRpcVo.ChartRpcRecord vcpm = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getVcpm()))
                    .build();
            vcpmList.add(vcpm);

            AdHomeChartRpcVo.ChartRpcRecord adSaleNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getAdSaleNum()))
                    .build();
            adSaleNumList.add(adSaleNum);

            AdHomeChartRpcVo.ChartRpcRecord adOtherOrderNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getAdOtherOrderNum()))
                    .build();
            adOtherOrderNumList.add(adOtherOrderNum);

            AdHomeChartRpcVo.ChartRpcRecord adSales = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getAdSales()))
                    .build();
            adSalesList.add(adSales);

            AdHomeChartRpcVo.ChartRpcRecord adOtherSales = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getAdOtherSales()))
                    .build();
            adOtherSalesList.add(adOtherSales);

            AdHomeChartRpcVo.ChartRpcRecord orderNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getOrderNum()))
                    .build();
            orderNumList.add(orderNum);

            AdHomeChartRpcVo.ChartRpcRecord adSelfSaleNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getAdSelfSaleNum()))
                    .build();
            adSelfSaleNumList.add(adSelfSaleNum);

            AdHomeChartRpcVo.ChartRpcRecord adOtherSaleNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getAdOtherSaleNum()))
                    .build();
            adOtherSaleNumList.add(adOtherSaleNum);

            AdHomeChartRpcVo.ChartRpcRecord ordersNewToBrand14d = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getOrdersNewToBrand14d()))
                    .build();
            ordersNewToBrand14dList.add(ordersNewToBrand14d);

            AdHomeChartRpcVo.ChartRpcRecord orderRateNewToBrand14d = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getOrderRateNewToBrand14d()))
                    .build();
            orderRateNewToBrand14dList.add(orderRateNewToBrand14d);

            AdHomeChartRpcVo.ChartRpcRecord salesNewToBrand14d = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getSalesNewToBrand14d()))
                    .build();
            salesNewToBrand14dList.add(salesNewToBrand14d);

            AdHomeChartRpcVo.ChartRpcRecord salesRateNewToBrand14d = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getSalesRateNewToBrand14d()))
                    .build();
            salesRateNewToBrand14dList.add(salesRateNewToBrand14d);

            AdHomeChartRpcVo.ChartRpcRecord unitsOrderedNewToBrand14d = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getUnitsOrderedNewToBrand14d()))
                    .build();
            unitsOrderedNewToBrand14dList.add(unitsOrderedNewToBrand14d);

            AdHomeChartRpcVo.ChartRpcRecord unitsOrderedRateNewToBrand14d = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getUnitsOrderedRateNewToBrand14d()))
                    .build();
            unitsOrderedRateNewToBrand14dList.add(unitsOrderedRateNewToBrand14d);

            AdHomeChartRpcVo.ChartRpcRecord ordersNewToBrandPercentage14d = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getOrdersNewToBrandPercentage14d()))
                    .build();
            ordersNewToBrandPercentage14dList.add(ordersNewToBrandPercentage14d);

            AdHomeChartRpcVo.ChartRpcRecord impressionRank = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getImpressionRank()))
                    .build();
            impressionRankList.add(impressionRank);

            AdHomeChartRpcVo.ChartRpcRecord impressionShare = AdHomeChartRpcVo.ChartRpcRecord.newBuilder()
                    .setDate(item.getDate())
                    .setValue(String.valueOf(item.getImpressionShare()))
                    .build();
            impressionShareList.add(impressionShare);
        });

        adHomeChartVos.add(
                AdHomeChartRpcVo.newBuilder().setDescription("点击率")
                        .setName("ctr")
                        .setTotal(Int32Value.of(size))
                        .setCurrency(currency)
                        .addAllRecords(ctrList)
                        .build());


        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("点击量")
                .setName("clicks")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(clicksList)
                .build());


        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("曝光量")
                .setName("impressions")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(impressionsList)
                .build());


        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("订单转化率")
                .setName("cvr")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(cvrList)
                .build());

        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("广告销售额")
                .setName("adSale")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(adSaleList)
                .build());

        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("平均点击费")
                .setName("adCostPerClick")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(adCostPerClickList)
                .build());


        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("广告花费")
                .setName("adCost")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(adCostList)
                .build());


        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("广告订单数")
                .setName("adOrderNum")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(adOrderNumList)
                .build());

        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("Acos")
                .setName("acos")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(acosList)
                .build());

        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("Roas")
                .setName("roas")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(roasList)
                .build());

        if (!isVc) {
            adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("Acots")
                    .setName("acots")
                    .setTotal(Int32Value.of(size))
                    .setCurrency(currency)
                    .addAllRecords(acotsList)
                    .build());

            adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                    .setDescription("Asots")
                    .setName("asots")
                    .setTotal(Int32Value.of(size))
                    .setCurrency(currency)
                    .addAllRecords(asotsList)
                    .build());
        }

        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("可见展示次数")
                .setName("viewImpressions")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(viewImpressionsList)
                .build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("每笔订单花费")
                .setName("cpa")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(cpaList)
                .build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("VCPM")
                .setName("vcpm")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(vcpmList)
                .build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("本广告产品订单量")
                .setName("adSaleNum")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(adSaleNumList)
                .build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("其他产品广告订单量")
                .setName("adOtherOrderNum")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(adOtherOrderNumList)
                .build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("本广告产品销售额")
                .setName("adSales")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(adSalesList)
                .build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("其他产品广告销售额")
                .setName("adOtherSales")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(adOtherSalesList)
                .build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("广告销量")
                .setName("orderNum")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(orderNumList)
                .build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("本广告产品销量")
                .setName("adSelfSaleNum")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(adSelfSaleNumList)
                .build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("其他产品广告销量")
                .setName("adOtherSaleNum")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(adOtherSaleNumList)
                .build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("“品牌新买家”订单量")
                .setName("ordersNewToBrand14d")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(ordersNewToBrand14dList)
                .build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("“品牌新买家”订单百分比")
                .setName("orderRateNewToBrand14d")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(orderRateNewToBrand14dList)
                .build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("“品牌新买家”销售额")
                .setName("salesNewToBrand14d")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(salesNewToBrand14dList)
                .build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("“品牌新买家”销售额百分比")
                .setName("salesRateNewToBrand14d")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(salesRateNewToBrand14dList)
                .build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("“品牌新买家”销量")
                .setName("unitsOrderedNewToBrand14d")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(unitsOrderedNewToBrand14dList)
                .build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("“品牌新买家”销量百分比")
                .setName("unitsOrderedRateNewToBrand14d")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(unitsOrderedRateNewToBrand14dList)
                .build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("“品牌新买家”订单转化率")
                .setName("ordersNewToBrandPercentage14d")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(ordersNewToBrandPercentage14dList)
                .build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("搜索词展示量排名")
                .setName("impressionRank")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(impressionRankList)
                .build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder()
                .setDescription("搜索词展示份额")
                .setName("impressionShare")
                .setTotal(Int32Value.of(size))
                .setCurrency(currency)
                .addAllRecords(impressionShareList)
                .build());
        return adHomeChartVos;
    }
}
