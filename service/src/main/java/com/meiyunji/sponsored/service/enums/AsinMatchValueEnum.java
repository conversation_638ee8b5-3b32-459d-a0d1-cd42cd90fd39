package com.meiyunji.sponsored.service.enums;


public enum AsinMatchValueEnum {
    /**
     * asin搜索词导出 投放类型
     * */

    complements("complements", "关联商品"),
    substitutes("substitutes", "同类商品"),
    loosematch("loose-match", "宽泛匹配"),
    closematch("close-match", "紧密匹配");

    AsinMatchValueEnum(String matchType, String matchValue) {
        this.matchType = matchType;
        this.matchValue = matchValue;
    }

    private String matchType;
    private String matchValue;

    public static String getAsinMatchValue(String matchType) {
        AsinMatchValueEnum[] values = values();
        for (AsinMatchValueEnum value : values) {
            if (value.getMatchType().equals(matchType)) {
                return value.getMatchValue();
            }
        }
        return matchType;
    }

    public String getMatchType() {
        return matchType;
    }

    public void setMatchType(String matchType) {
        this.matchType = matchType;
    }

    public String getMatchValue() {
        return matchValue;
    }

    public void setMatchValue(String matchValue) {
        this.matchValue = matchValue;
    }
}
