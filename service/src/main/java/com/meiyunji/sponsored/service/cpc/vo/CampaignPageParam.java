package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.common.enums.BaseEnum;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by xp on 2021/4/9.
 */
@Data
@ApiModel
public class CampaignPageParam {

    @ApiModelProperty("pageNo")
    private Integer pageNo;

    @ApiModelProperty("pageSize")
    private Integer pageSize;

    @ApiModelProperty(value = "shopId",required = true)
    private Integer shopId;

    @ApiModelProperty("店铺id集合")
    private List<Integer> shopIdList;

    @ApiModelProperty("店铺集合")
    private List<ShopAuth> shopAuthList;

    @ApiModelProperty("店铺Map集合")
    private Map<Integer, ShopAuth> shopAuthMap;


    private Integer puid;

    private String marketplaceId;
    //子用户id
    private Integer uid;
    //任务redis存储的id
    private String uuid;

    private Boolean isAdmin;

    @ApiModelProperty("搜索字段名称")
    private String searchField;

    @ApiModelProperty("搜索内容--对应字段")
    private String searchValue;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("排序字段")
    private String orderField;

    @ApiModelProperty("排序类型")
    private String orderType;

    @ApiModelProperty("广告类型 sp, sd, sb")
    private String type; // sp, sd, sb

   //StrategyEnum枚举类
    @ApiModelProperty("竞价策略类型 legacyForSales,autoForSales,manual->sp广告  none->sd sb广告")
    private String strategyType;

    @ApiModelProperty("预算状态 under->未超 exceeded->已超")
    private String budgetState;

    @ApiModelProperty("ids")
    private List<String> ids;

    @ApiModelProperty("经过sb creative筛选后得到的ids")
    private List<String> creativeIds;

    @ApiModelProperty("导出排序字段")
    private String exportSortField;

    @ApiModelProperty("冻结前num列")
    private Integer freezeNum;

    @ApiModelProperty(value = "广告组合id")
    private String portfolioId;

    @ApiModelProperty(value = "广告组合id集合")
    private List<String> portfolioIdList;

    //高级搜索
    @ApiModelProperty(value = "是否开启高级搜索")
    private Boolean useAdvanced;

    //高级搜索值
    @ApiModelProperty(value = "高级搜索展示量最小值")
    private Integer impressionsMin;  //展示量
    @ApiModelProperty(value = "高级搜索展示量最大值")
    private Integer impressionsMax;
    @ApiModelProperty(value = "高级搜索点击量小值")
    private Integer clicksMin;  //点击量
    @ApiModelProperty(value = "高级搜索点击量大值")
    private Integer clicksMax;
    @ApiModelProperty(value = "高级搜索点击率小值")
    private BigDecimal clickRateMin;  //点击率
    @ApiModelProperty(value = "高级搜索点击率大值")
    private BigDecimal clickRateMax;
    @ApiModelProperty(value = "高级搜索花费小值")
    private BigDecimal costMin; //花费
    @ApiModelProperty(value = "高级搜索花费大值")
    private BigDecimal costMax;
    @ApiModelProperty(value = "高级搜索cpc小值")
    private BigDecimal cpcMin;  //cpc
    @ApiModelProperty(value = "高级搜索cpc大值")
    private BigDecimal cpcMax;
    @ApiModelProperty(value = "高级搜索广告订单量小值")
    private Integer orderNumMin;  //广告订单量
    @ApiModelProperty(value = "高级搜索广告订单量大值")
    private Integer orderNumMax;
    @ApiModelProperty(value = "高级搜索sales小值")
    private BigDecimal salesMin;  //sales
    @ApiModelProperty(value = "高级搜索sales大值")
    private BigDecimal salesMax;
    @ApiModelProperty(value = "高级搜索acos小值")
    private BigDecimal acosMin;  //acos
    @ApiModelProperty(value = "高级搜索acos大值")
    private BigDecimal acosMax;
    @ApiModelProperty(value = "高级搜索roas小值")
    private BigDecimal roasMin;   //roas
    @ApiModelProperty(value = "高级搜索roas大值")
    private BigDecimal roasMax;
    @ApiModelProperty(value = "高级搜索广告订单转化率小值")
    private BigDecimal salesConversionRateMin;  //订单转化率
    @ApiModelProperty(value = "高级搜索广告订单转化率大值")
    private BigDecimal salesConversionRateMax;
    @ApiModelProperty(value = "高级搜索acots小值")
    private BigDecimal acotsMin;  //acots
    @ApiModelProperty(value = "高级搜索acots大值")
    private BigDecimal acotsMax;
    @ApiModelProperty(value = "高级搜索asots小值")
    private BigDecimal asotsMin;  //acos
    @ApiModelProperty(value = "高级搜索asots大值")
    private BigDecimal asotsMax;

    @ApiModelProperty(value = "高级搜索投放类型")
    private String filterTargetType;
    @ApiModelProperty(value = "高级搜索投放类型")
    private String deliveryType;
    @ApiModelProperty(value = "高级搜索每日预算最小")
    private BigDecimal dailyBudgetMin;
    @ApiModelProperty(value = "高级搜索每日预算最大")
    private BigDecimal dailyBudgetMax;
    @ApiModelProperty(value = "高级搜索开始日期")
    private String filterStartDate;
    @ApiModelProperty(value = "高级搜索结束日期")
    private String filterEndDate;
    @ApiModelProperty(value = "高级搜索广告位顶部最小")
    private BigDecimal placementTopMin;
    @ApiModelProperty(value = "高级搜索广告位顶部最大")
    private BigDecimal placementTopMax;
    @ApiModelProperty(value = "高级搜索广告位产品页面最小")
    private BigDecimal placementProductMin;
    @ApiModelProperty(value = "高级搜索广告位产品页面最大")
    private BigDecimal placementProductMax;


    /*******************高级搜索新增查询字段***************************/
    @ApiModelProperty(value = "可见展示次数最小值")
    private Integer viewImpressionsMin;
    @ApiModelProperty(value = "可见展示次数最大值")
    private Integer viewImpressionsMax;


    @ApiModelProperty(value = "cpa最小值")
    private BigDecimal cpaMin;
    @ApiModelProperty(value = "cpa最大值")
    private BigDecimal cpaMax;


    @ApiModelProperty(value = "vcpm最小值")
    private BigDecimal vcpmMin;
    @ApiModelProperty(value = "vcpm最大值")
    private BigDecimal vcpmMax;


    @ApiModelProperty(value = "本广告产品订单量最小值")
    private Integer adSaleNumMin;
    @ApiModelProperty(value = "本广告产品订单量最大值")
    private Integer adSaleNumMax;


    @ApiModelProperty(value = "其他产品广告订单量最小值")
    private Integer adOtherOrderNumMin;
    @ApiModelProperty(value = "其他产品广告订单量最大值")
    private Integer adOtherOrderNumMax;


    @ApiModelProperty(value = "本广告产品销售额最小值")
    private BigDecimal adSalesMin;
    @ApiModelProperty(value = "本广告产品销售额最大值")
    private BigDecimal adSalesMax;


    @ApiModelProperty(value = "其他产品广告销售额最小值")
    private BigDecimal adOtherSalesMin;
    @ApiModelProperty(value = "其他产品广告销售额最大值")
    private BigDecimal adOtherSalesMax;


    @ApiModelProperty(value = "本廣告产品销量最小值")
    private Integer adSelfSaleNumMin;
    @ApiModelProperty(value = "本廣告产品销量最大值")
    private Integer adSelfSaleNumMax;


    @ApiModelProperty(value = "其他产品广告销量最小值")
    private Integer adOtherSaleNumMin;
    @ApiModelProperty(value = "其他产品广告销量最大值")
    private Integer adOtherSaleNumMax;


    @ApiModelProperty(value = "“品牌新买家”订单量最小值")
    private Integer ordersNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”订单量最大值")
    private Integer ordersNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”订单百分比最小值")
    private BigDecimal orderRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”订单百分比最大值")
    private BigDecimal orderRateNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销售额最小值")
    private BigDecimal salesNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销售额最大值")
    private BigDecimal salesNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销售额百分比最小值")
    private BigDecimal salesRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销售额百分比最大值")
    private BigDecimal salesRateNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销量最小值")
    private Integer unitsOrderedNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销量最大值")
    private Integer unitsOrderedNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销量百分比最小值")
    private BigDecimal unitsOrderedRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销量百分比最大值")
    private BigDecimal unitsOrderedRateNewToBrandFTDMax;


    @ApiModelProperty(value = "广告销量最小值")
    private Integer adSalesTotalMin;
    @ApiModelProperty(value = "广告销量最大值")
    private Integer adSalesTotalMax;

    @ApiModelProperty(value = "高级搜索店铺销售额")
    private BigDecimal shopSales;

    @ApiModelProperty(value = "广告组合下的活动id")
    private List<String> campaignIdList;
    @ApiModelProperty(value = "付费方式")
    private String costType;
    @ApiModelProperty(value = "广告活动id")
    private String campaignId;
    private String campaignIds;
    @ApiModelProperty(value = "用于仅展示正在投放字段 勾选后传值 enabled")
    private String servingStatus;
    @ApiModelProperty(value = "高级搜索广告花费占比最小")
    private BigDecimal adCostPercentageMin;
    @ApiModelProperty(value = "高级搜索广告花费占比最大")
    private BigDecimal adCostPercentageMax;
    @ApiModelProperty(value = "高级搜索广告销售额占比最小")
    private BigDecimal adSalePercentageMin;
    @ApiModelProperty(value = "高级搜索广告销售额占比最大")
    private BigDecimal adSalePercentageMax;
    @ApiModelProperty(value = "高级搜索广告订单量占比最小")
    private BigDecimal adOrderNumPercentageMin;
    @ApiModelProperty(value = "高级搜索广告订单量占比最大")
    private BigDecimal adOrderNumPercentageMax;
    @ApiModelProperty(value = "高级搜索广告销量占比最小")
    private BigDecimal orderNumPercentageMin;
    @ApiModelProperty(value = "高级搜索广告销量占比最大")
    private BigDecimal orderNumPercentageMax;

    @ApiModelProperty("加购次数最小值")
    private Integer addToCartMin;
    @ApiModelProperty("加购次数最大值")
    private Integer addToCartMax;

    @ApiModelProperty("5秒观看次数最小值")
    private Integer video5SecondViewsMin;
    @ApiModelProperty("5秒观看次数最大值")
    private Integer video5SecondViewsMax;

    @ApiModelProperty("视频完整播放次数最小值")
    private Integer videoCompleteViewsMin;
    @ApiModelProperty("视频完整播放次数最大值")
    private Integer videoCompleteViewsMax;

    @ApiModelProperty("观看率最小值")
    private BigDecimal viewabilityRateMin;
    @ApiModelProperty("观看率最大值")
    private BigDecimal viewabilityRateMax;

    @ApiModelProperty("观看点击率最小值")
    private BigDecimal viewClickThroughRateMin;
    @ApiModelProperty("观看点击率最大值")
    private BigDecimal viewClickThroughRateMax;

    @ApiModelProperty("品牌搜索次数最小值")
    private Integer brandedSearchesMin;
    @ApiModelProperty("品牌搜索次数最大值")
    private Integer brandedSearchesMax;

    @ApiModelProperty("累计触达用户最小值")
    private Integer cumulativeReachMin;
    @ApiModelProperty("累计触达用户最大值")
    private Integer cumulativeReachMax;

    @ApiModelProperty("平均触达次数最小值")
    private BigDecimal impressionsFrequencyAverageMin;
    @ApiModelProperty("平均触达次数最大值")
    private BigDecimal impressionsFrequencyAverageMax;

    @ApiModelProperty("广告笔单价最小值")
    private BigDecimal advertisingUnitPriceMin;
    @ApiModelProperty("广告笔单价最大值")
    private BigDecimal advertisingUnitPriceMax;

    @ApiModelProperty("搜索结果首页首位IS最小值")
    private BigDecimal topImpressionShareMin;
    @ApiModelProperty("搜索结果首页首位IS最大值")
    private BigDecimal topImpressionShareMax;

    private String productType;
    private String productValue;

    //环比相关参数
    private Boolean isCompare;
    private String compareStartDate;
    private String compareEndDate;

    private String pageSign;

    private List<Long> adTagIdList;

    private List<Long> adTagIds;

    /** 查询数据类型 {@link com.meiyunji.sponsored.service.enums.SearchDataTypeEnum}*/
    @ApiModelProperty("查询数据类型")
    private Integer searchDataType;

    /**
     *  只查询数量 简化查询
     */
    private Boolean onlyCount;

    @ApiModelProperty("多店铺判断是否需要汇率换算（多店铺币种不同时为true）")
    private Boolean changeRate;

    private String searchType;

    private List<String> searchValueList;

    /**
     * 广告策略类型
     */
    private List<String> adStrategyTypeList;

    /**
     * 经过广告策略筛选后的广告活动id集合
     */
    private List<String> autoRuleIds;

    /**
     * 预算状态枚举类
     */
    public enum BudgetStateEnum implements BaseEnum {

        underBudget("under","未超过预算"),
        budgetExceeded("exceeded","已超预算"),
        ;

        BudgetStateEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private String code;
        private String desc;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getDescription() {
            return desc;
        }
    }


    /**
     * 搜索字段枚举
     */
    public enum SearchFieldEnum implements BaseEnum {

        Name("name","name","广告活动名称");

        ;
        private String field;
        private String column;
        private String desc;

        SearchFieldEnum(String field, String column, String desc) {
            this.field = field;
            this.column = column;
            this.desc = desc;
        }

        public String getColumn() {
            return column;
        }

        public String getField() {
            return field;
        }

        public String getDesc() {
            return desc;
        }

        @Override
        public String getCode() {
            return field;
        }

        @Override
        public String getDescription() {
            return desc;
        }
    }

    /**
     * 新的传参分隔符  %±%
     * @return 返回String类型参数数组
     */
    public List<String> getListProductValues(){
        if (StringUtils.isNotBlank(this.productValue)) {
            return StringUtil.stringToList(this.productValue.trim(),"%±%");
        }
        return new ArrayList<>();
    }


    @Override
    public String toString() {
        return "CampaignPageParam{" +
                "shopId=" + shopId +
                ", puid=" + puid +
                ", uid=" + uid +
                ", searchField='" + searchField + '\'' +
                ", searchValue='" + searchValue + '\'' +
                ", status='" + status + '\'' +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                ", orderField='" + orderField + '\'' +
                ", orderType='" + orderType + '\'' +
                ", type='" + type + '\'' +
                ", strategyType='" + strategyType + '\'' +
                ", budgetState='" + budgetState + '\'' +
                ", ids=" + ids +
                ", portfolioId='" + portfolioId + '\'' +
                ", useAdvanced=" + useAdvanced +
                ", impressionsMin=" + impressionsMin +
                ", impressionsMax=" + impressionsMax +
                ", clicksMin=" + clicksMin +
                ", clicksMax=" + clicksMax +
                ", clickRateMin=" + clickRateMin +
                ", clickRateMax=" + clickRateMax +
                ", costMin=" + costMin +
                ", costMax=" + costMax +
                ", cpcMin=" + cpcMin +
                ", cpcMax=" + cpcMax +
                ", orderNumMin=" + orderNumMin +
                ", orderNumMax=" + orderNumMax +
                ", salesMin=" + salesMin +
                ", salesMax=" + salesMax +
                ", acosMin=" + acosMin +
                ", acosMax=" + acosMax +
                ", roasMin=" + roasMin +
                ", roasMax=" + roasMax +
                ", salesConversionRateMin=" + salesConversionRateMin +
                ", salesConversionRateMax=" + salesConversionRateMax +
                ", acotsMin=" + acotsMin +
                ", acotsMax=" + acotsMax +
                ", asotsMin=" + asotsMin +
                ", asotsMax=" + asotsMax +
                ", filterTargetType='" + filterTargetType + '\'' +
                ", dailyBudgetMin=" + dailyBudgetMin +
                ", dailyBudgetMax=" + dailyBudgetMax +
                ", filterStartDate='" + filterStartDate + '\'' +
                ", filterEndDate='" + filterEndDate + '\'' +
                ", placementTopMin=" + placementTopMin +
                ", placementTopMax=" + placementTopMax +
                ", placementProductMin=" + placementProductMin +
                ", placementProductMax=" + placementProductMax +
                ", viewImpressionsMin=" + viewImpressionsMin +
                ", viewImpressionsMax=" + viewImpressionsMax +
                ", cpaMin=" + cpaMin +
                ", cpaMax=" + cpaMax +
                ", vcpmMin=" + vcpmMin +
                ", vcpmMax=" + vcpmMax +
                ", adSaleNumMin=" + adSaleNumMin +
                ", adSaleNumMax=" + adSaleNumMax +
                ", adOtherOrderNumMin=" + adOtherOrderNumMin +
                ", adOtherOrderNumMax=" + adOtherOrderNumMax +
                ", adSalesMin=" + adSalesMin +
                ", adSalesMax=" + adSalesMax +
                ", adOtherSalesMin=" + adOtherSalesMin +
                ", adOtherSalesMax=" + adOtherSalesMax +
                ", adSelfSaleNumMin=" + adSelfSaleNumMin +
                ", adSelfSaleNumMax=" + adSelfSaleNumMax +
                ", adOtherSaleNumMin=" + adOtherSaleNumMin +
                ", adOtherSaleNumMax=" + adOtherSaleNumMax +
                ", ordersNewToBrandFTDMin=" + ordersNewToBrandFTDMin +
                ", ordersNewToBrandFTDMax=" + ordersNewToBrandFTDMax +
                ", orderRateNewToBrandFTDMin=" + orderRateNewToBrandFTDMin +
                ", orderRateNewToBrandFTDMax=" + orderRateNewToBrandFTDMax +
                ", salesNewToBrandFTDMin=" + salesNewToBrandFTDMin +
                ", salesNewToBrandFTDMax=" + salesNewToBrandFTDMax +
                ", salesRateNewToBrandFTDMin=" + salesRateNewToBrandFTDMin +
                ", salesRateNewToBrandFTDMax=" + salesRateNewToBrandFTDMax +
                ", unitsOrderedNewToBrandFTDMin=" + unitsOrderedNewToBrandFTDMin +
                ", unitsOrderedNewToBrandFTDMax=" + unitsOrderedNewToBrandFTDMax +
                ", unitsOrderedRateNewToBrandFTDMin=" + unitsOrderedRateNewToBrandFTDMin +
                ", unitsOrderedRateNewToBrandFTDMax=" + unitsOrderedRateNewToBrandFTDMax +
                ", adSalesTotalMin=" + adSalesTotalMin +
                ", adSalesTotalMax=" + adSalesTotalMax +
                ", shopSales=" + shopSales +
                ", campaignIdList=" + campaignIdList +
                ", costType='" + costType + '\'' +
                ", campaignId='" + campaignId + '\'' +
                ", servingStatus='" + servingStatus + '\'' +
                ", adCostPercentageMin=" + adCostPercentageMin +
                ", adCostPercentageMax=" + adCostPercentageMax +
                ", adSalePercentageMin=" + adSalePercentageMin +
                ", adSalePercentageMax=" + adSalePercentageMax +
                ", adOrderNumPercentageMin=" + adOrderNumPercentageMin +
                ", adOrderNumPercentageMax=" + adOrderNumPercentageMax +
                ", orderNumPercentageMin=" + orderNumPercentageMin +
                ", orderNumPercentageMax=" + orderNumPercentageMax +
                ", productType='" + productType + '\'' +
                ", productValue='" + productValue + '\'' +
                ", isCompare=" + isCompare +
                ", compareStartDate='" + compareStartDate + '\'' +
                ", compareEndDate='" + compareEndDate + '\'' +
                '}';
    }
}
