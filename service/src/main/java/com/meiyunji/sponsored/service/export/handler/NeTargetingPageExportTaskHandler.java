package com.meiyunji.sponsored.service.export.handler;

import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.vo.NeTargetingPageRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcNeKeywordsService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcTargetingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.NeTargetingPageParam;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.StateEnum;
import com.meiyunji.sponsored.service.enums.TargetingEnum;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.vo.NeSbTargetPageVo;
import com.meiyunji.sponsored.service.vo.NeTargetPageVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service(AdManagePageExportTaskConstant.NE_TARGET)
@Slf4j
public class NeTargetingPageExportTaskHandler implements AdManagePageExportTaskHandler {
    @Autowired
    private ICpcNeKeywordsService cpcNeKeywordsService;
    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private ICpcTargetingService cpcTargetingService;
    @Autowired
    private DynamicRefreshConfiguration configuration;
    /**
     * 导出
     * @param task
     */
    @Override
    public void export(AdManagePageExportTask task) {
        NeTargetingPageParam param = JSONUtil.jsonToObject(task.getParam(), NeTargetingPageParam.class);
        if (param == null) {
            log.error(String.format("neTarget export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        ShopAuth shop = shopAuthDao.getScAndVcById(param.getShopId());
        //站点币种
        String currency = AmznEndpoint.getByMarketplaceId(shop.getMarketplaceId()).getCurrencyCode().value();
        Page<NeTargetingPageRpcVo> voPage;
        if (configuration.getContainsReport()) {
            voPage = cpcTargetingService.getAllNeTargetingPageListV2(param);
        } else {
            voPage = cpcTargetingService.getAllNeTargetingPageList(param.getPuid(), param);
        }
        List<NeTargetingPageRpcVo> voList = voPage.getRows();
        List<String> urlList = new ArrayList<>();
        int count = 0;
        WriteHandlerBuild build = new WriteHandlerBuild().rate();
        if (CollectionUtils.isEmpty(voList)) {
            //修改任务状态
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            //修改状态，前端收到后转圈效果停止
            stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }
        String fileName = shop.getName() + "_否定商品定位" + "_" + param.getStartDate() + "_" + param.getEndDate();
        List<List<NeTargetingPageRpcVo>> partition = Lists.partition(voList, Constants.EXPORT_MAX_SIZE);
        for (List<NeTargetingPageRpcVo> partitionList : partition) {
            List<NeTargetPageVo> neTargetPageVoList = partitionList.stream().map(k ->buildExportVo(k, currency)).collect(Collectors.toList());
            if (!neTargetPageVoList.isEmpty()) {
                Class clazz = NeTargetPageVo.class;
                if (Constants.SB.equalsIgnoreCase(param.getType())) {
                    clazz = NeSbTargetPageVo.class;
                }
                urlList.add(excelService.easyExcelHandlerExport(param.getPuid(), neTargetPageVoList, fileName + "(" + count++ + ")", clazz, build.currencyNew(clazz)));
            }
        }
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(param.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }
      private static NeTargetPageVo buildExportVo(NeTargetingPageRpcVo ckVo, String currency) {
          NeTargetPageVo nkVo = new NeTargetPageVo();
          nkVo.setAsin(ckVo.getAsin());
          nkVo.setCreateTime(ckVo.getCreateTime());
          //状态
          nkVo.setState(StateEnum.getStateValue(ckVo.getState()));
          //广告活动
          nkVo.setAdvertisingActivities(ckVo.getCampaignName());
          //广告位
          nkVo.setAdvertisingGroup(ckVo.getAdGroupName());

          nkVo.setCampaignTargetingType(TargetingEnum.getTargetingValue(ckVo.getCampaignTargetingType()));

          nkVo.setType(CampaignTypeEnum.getCampaignValue(ckVo.getType()));

          nkVo.setPortfolioName(ckVo.getPortfolioName());

          if (ckVo.hasCreateName()) {
              nkVo.setCreateName(ckVo.getCreateName());
          }
          if (ckVo.hasAcos()) {
              nkVo.setAcos(CalculateUtil.formatDecimal(BigDecimal.valueOf(ckVo.getAcos())) + "%");
          }
          if (ckVo.hasAdOrderNum()) {
              nkVo.setAdOrderNum(ckVo.getAdOrderNum());
          }
          if (ckVo.hasClicks()) {
              nkVo.setClicks(ckVo.getClicks());
          }
          if (ckVo.hasImpressions()) {
              nkVo.setImpressions(ckVo.getImpressions());
          }
          if (ckVo.hasAdCost()) {
              nkVo.setAdCost(currency + CalculateUtil.formatDecimal(BigDecimal.valueOf(ckVo.getAdCost())));
          }
          if (ckVo.hasAdSale()) {
              nkVo.setAdSale(currency + CalculateUtil.formatDecimal(BigDecimal.valueOf(ckVo.getAdSale())));
          }
          if (ckVo.hasCtr()) {
              nkVo.setCtr(CalculateUtil.formatDecimal(BigDecimal.valueOf(ckVo.getCtr())) + "%");
          }
          if (ckVo.hasCvr()) {
              nkVo.setCvr(CalculateUtil.formatDecimal(BigDecimal.valueOf(ckVo.getCvr())) + "%");
          }
          if (ckVo.hasRoas()) {
              nkVo.setRoas(CalculateUtil.formatDecimal(BigDecimal.valueOf(ckVo.getRoas())));
          }
          if (ckVo.hasAdCostPerClick()) {
              nkVo.setCpc(CalculateUtil.formatDecimal(BigDecimal.valueOf(ckVo.getAdCostPerClick())));
          }
          if (ckVo.hasCpa()) {
              nkVo.setCpa(CalculateUtil.formatDecimal(BigDecimal.valueOf(ckVo.getCpa())));
          }

          return nkVo;
      }
}
