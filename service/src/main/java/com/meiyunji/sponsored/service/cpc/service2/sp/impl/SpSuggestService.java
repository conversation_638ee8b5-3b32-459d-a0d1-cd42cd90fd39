package com.meiyunji.sponsored.service.cpc.service2.sp.impl;

import com.alibaba.fastjson.JSON;
import com.amazon.advertising.mode.AdjustmentForTarget;
import com.amazon.advertising.mode.BiddingForTarget;
import com.amazon.advertising.mode.targeting.CategoryRecommendations;
import com.amazon.advertising.targeting.mode.*;
import com.amazon.advertising.targeting.mode.TargetingExpression;
import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznBaseCurrencyCode;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.amazon.sellerpartner.seller.base.Marketplace;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.rpc.sp.keyword.*;
import com.meiyunji.sponsored.rpc.sp.keyword.SuggestedBidImpactMetrics;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.manager.CpcSpTargetingManager;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcKeywordsService;
import com.meiyunji.sponsored.service.cpc.vo.SuggestedKeywordVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

/**
 * SpSuggestService
 *
 * @Author: hejh
 * @Date: 2024/7/12 13:29
 */
@Service
@Slf4j
public class SpSuggestService {

    @Autowired
    private CpcTargetingApiService cpcTargetingApiService;
    @Autowired
    private ICpcKeywordsService cpcKeywordsService;
    @Autowired
    private CpcSpTargetingManager spTargetingManager;
    private final static int MAX_QUERY_COUNT = 100;

    /**
     * 通过asin分页查询建议分类
     *
     * @param pageNo   pageNo
     * @param pageSize pageSize
     * @param asinList asin列表
     * @param shop     shop
     * @param profile  profile
     * @return PageSuggestCategoriesByAsinInfo
     */
    public PageSuggestCategoriesByAsinInfo pageSuggestCategoriesByAsin(int pageNo, int pageSize, List<String> asinList, ShopAuth shop, AmazonAdProfile profile) {
        PageSuggestCategoriesByAsinInfo.Builder respBuilder = PageSuggestCategoriesByAsinInfo.newBuilder();
        Result<List<CategoryRecommendations>> result = cpcTargetingApiService.getCategoriesByAsin(shop.getId(), profile.getProfileId(), asinList);
        if (!result.success() || CollectionUtils.isEmpty(result.getData())) {
            log.info("getCategoriesByAsin fail, result: {}", JSON.toJSONString(result));
            respBuilder.setPageNo(pageNo);
            respBuilder.setPageSize(pageSize);
            respBuilder.setTotalSize(0);
            respBuilder.setHasNextPage(false);
            respBuilder.addAllCategoryList(Collections.emptyList());
            return respBuilder.build();
        }
        List<CategoryRecommendations> dataList = result.getData();
        respBuilder.setPageNo(pageNo);
        respBuilder.setPageSize(pageSize);
        respBuilder.setTotalSize(dataList.size());
        respBuilder.setHasNextPage(hasNextPage(dataList, pageNo, pageSize));
        List<CategoryRecommendations> categoryRecommendationsList = getPage(dataList, pageNo, pageSize);
        List<TargetCategory> targetCategories = new ArrayList<>(categoryRecommendationsList.size());
        for (CategoryRecommendations categoryRecommendations : categoryRecommendationsList) {
            TargetCategory.Builder targetCategory = TargetCategory.newBuilder();
            targetCategory.setId(categoryRecommendations.getId());
            targetCategory.setName(categoryRecommendations.getName());
            targetCategory.setPath(categoryRecommendations.getPath());
            targetCategories.add(targetCategory.build());
        }
        respBuilder.addAllCategoryList(targetCategories);
        return respBuilder.build();
    }

    /**
     * 通过asin分页查询建议关键词
     *
     * @param pageNo    pageNo
     * @param pageSize  pageSize
     * @param asinList  asin列表
     * @param shop      shop
     * @param profile   profile
     * @param searchVal 搜索值
     * @return PageSuggestKeywordsByAsinInfo
     */
    public PageSuggestKeywordsByAsinInfo pageSuggestKeywordsByAsin(int pageNo, int pageSize, List<String> asinList, ShopAuth shop, AmazonAdProfile profile, String searchVal) {
        //1，调用
        Result<List<SuggestedKeywordVo>> result = cpcKeywordsService.searchSuggestKeywordsByAsin(shop.getPuid(), shop.getId(), asinList);
        if (!result.success() || CollectionUtils.isEmpty(result.getData())) {
            log.info("searchSuggestKeywordsByAsin fail, result: {}", JSON.toJSONString(result));
            PageSuggestKeywordsByAsinInfo.Builder respBuilder = PageSuggestKeywordsByAsinInfo.newBuilder();
            respBuilder.setPageNo(pageNo);
            respBuilder.setPageSize(pageSize);
            respBuilder.setTotalSize(0);
            respBuilder.setHasNextPage(false);
            respBuilder.addAllKeywordTypeList(Collections.emptyList());
            return respBuilder.build();
        }
        //2，处理搜索
        List<SuggestedKeywordVo> dataList = result.getData();
        //通过searchVal进行搜索：都转化为小写，然后通过String的contains方法判断
        if (StringUtils.isNotBlank(searchVal)) {
            List<SuggestedKeywordVo> searchedKeywordVos = new ArrayList<>(dataList.size());
            searchVal = searchVal.trim().toLowerCase();
            for (SuggestedKeywordVo keywordVo : dataList) {
                if (StringUtils.isNotBlank(keywordVo.getKeywordText())) {
                    if (keywordVo.getKeywordText().trim().toLowerCase().contains(searchVal)) {
                        searchedKeywordVos.add(keywordVo);
                    }
                }
            }
            dataList = searchedKeywordVos;
        }
        //3，分页
        PageSuggestKeywordsByAsinInfo.Builder respBuilder = PageSuggestKeywordsByAsinInfo.newBuilder();
        respBuilder.setPageNo(pageNo);
        respBuilder.setPageSize(pageSize);
        respBuilder.setTotalSize(dataList.size());
        respBuilder.setHasNextPage(hasNextPage(dataList, pageNo, pageSize));
        List<SuggestedKeywordVo> suggestedKeywordVos = getPage(dataList, pageNo, pageSize);
        List<KeywordType> keywordTypes = new ArrayList<>(suggestedKeywordVos.size());
        for (SuggestedKeywordVo suggestedKeywordVo : suggestedKeywordVos) {
            KeywordType.Builder builder = KeywordType.newBuilder();
            builder.setMatchType(suggestedKeywordVo.getMatchType());
            builder.setKeywordText(suggestedKeywordVo.getKeywordText());
            if (StringUtils.isNotBlank(suggestedKeywordVo.getKeywordTextCn())) {
                builder.setKeywordTextCn(suggestedKeywordVo.getKeywordTextCn());
            }
            keywordTypes.add(builder.build());
        }
        respBuilder.addAllKeywordTypeList(keywordTypes);
        return respBuilder.build();
    }

    /**
     * 查询建议竞价
     *
     * @param asinList                asin列表
     * @param placementProductPage    产品页面广告位百分比：0-900
     * @param placementTop            搜索结果顶部广告位百分比：0-900
     * @param placementRestOfSearch   搜索结果其余位置广告位百分比：0-900
     * @param shop                    shop
     * @param profile                 profile
     * @param biddingStrategy         竞价策略："LEGACY_FOR_SALES" "AUTO_FOR_SALES" "MANUAL" "RULE_BASED"
     * @param targetingExpressionList 投放表达式
     * @return GetTargetBidRecommendationsInfo
     */
    public GetTargetBidRecommendationsInfo getTargetBidRecommendations(List<String> asinList, int placementProductPage, int placementTop, int placementRestOfSearch,
        ShopAuth shop, AmazonAdProfile profile, String biddingStrategy, String campaignId, String adGroupId,
        List<com.meiyunji.sponsored.rpc.sp.keyword.TargetingExpression> targetingExpressionList) {
        //1，构建请求参数
        List<TargetingExpression> targetingExpressions = new ArrayList<>();
        targetingExpressionList.forEach((targetingExpression) -> {
            TargetingExpression expression = new TargetingExpression();
            expression.setType(targetingExpression.getType());
            expression.setValue(targetingExpression.getValue());
            targetingExpressions.add(expression);
        });
        BiddingForTarget bidding = new BiddingForTarget();
        bidding.setStrategy(biddingStrategy);
        List<AdjustmentForTarget> adjustments = new ArrayList<>();
        if (placementProductPage != -1) {
            AdjustmentForTarget adjustment = new AdjustmentForTarget();
            adjustment.setPredicate("PLACEMENT_PRODUCT_PAGE");
            adjustment.setPercentage(placementProductPage);
            adjustments.add(adjustment);
        }
        if (placementTop != -1) {
            AdjustmentForTarget adjustment = new AdjustmentForTarget();
            adjustment.setPredicate("PLACEMENT_TOP");
            adjustment.setPercentage(placementTop);
            adjustments.add(adjustment);
        }
        if (placementRestOfSearch != -1) {
            AdjustmentForTarget adjustment = new AdjustmentForTarget();
            adjustment.setPredicate("PLACEMENT_REST_OF_SEARCH");
            adjustment.setPercentage(placementRestOfSearch);
            adjustments.add(adjustment);
        }
        bidding.setAdjustments(adjustments);

        //2，调用
        Vector<BidRecommendation> bidRecommendationsForTargeting = new Vector<>();
        List<List<TargetingExpression>> partition = Lists.partition(targetingExpressions, MAX_QUERY_COUNT);
        List<CompletableFuture<Void>> futureList = new ArrayList<>(partition.size());
        for (List<TargetingExpression> list : partition) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                List<BidRecommendation> partitionBidRecommendations;
                if (StringUtils.isNotBlank(campaignId) && StringUtils.isNotBlank(adGroupId)) {
                    if (AmznEndpoint.US.getMarketplaceId().equals(shop.getMarketplaceId())) {
                        partitionBidRecommendations = spTargetingManager.getBidRecommendationsWithExistedGroupV5(shop, profile, list, campaignId, adGroupId);
                    } else {
                        partitionBidRecommendations = spTargetingManager.getBidRecommendationsWithExistedGroup(shop, profile, list, campaignId, adGroupId);
                    }
                } else {
                    partitionBidRecommendations = spTargetingManager.getBidRecommendationsWithNewGroup(shop, profile, asinList, list, bidding);
                }
                bidRecommendationsForTargeting.addAll(partitionBidRecommendations);
            }, ThreadPoolUtil.getQueryBidRecommendationsForSpAdsExecutor()).exceptionally((e) -> {
                log.error("getBidRecommendationsWithExistedGroup error", e);
                return null;
            });
            futureList.add(future);
        }
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
        try {
            // 等待所有 CompletableFuture 完成
            allOf.join();
        } catch (CompletionException e) {
            log.error("Error occurred while get CompletableFuture bidRecommendations", e);
        }

        GetTargetBidRecommendationsInfo.Builder respBuilder = GetTargetBidRecommendationsInfo.newBuilder();
        if (CollectionUtils.isEmpty(bidRecommendationsForTargeting) || CollectionUtils.isEmpty(bidRecommendationsForTargeting.get(0).getBidRecommendationsForTargetingExpressions())) {
            respBuilder.addAllBidInfo(Collections.emptyList());
            return respBuilder.build();
        }

        //3，处理结果
        List<BidInfo> bidInfos = new ArrayList<>();
        for (BidRecommendation bidRecommendation : bidRecommendationsForTargeting) {
            if (Objects.isNull(bidRecommendation)) {
                continue;
            }
            for (BidRecommendationsForTargetingExpression expression : bidRecommendation.getBidRecommendationsForTargetingExpressions()) {
                if (Objects.isNull(expression)) {
                    continue;
                }
                BidInfo.Builder bidInfoBuilder = BidInfo.newBuilder();
                bidInfoBuilder.setType(expression.getTargetingExpression().getType());
                bidInfoBuilder.setValue(expression.getTargetingExpression().getValue());
                List<String> bids = new ArrayList<>();
                for (BidValue bid : expression.getBidValues()) {
                    bids.add(bid.getSuggestedBid().toString());
                }
                bidInfoBuilder.addAllSuggestedBids(bids);
                //已存在的美国站广告组，set预估曝光量
                com.amazon.advertising.targeting.mode.SuggestedBidImpactMetrics metrics = expression.getSuggestedBidImpactMetrics();
                if (StringUtils.isNotBlank(campaignId) && StringUtils.isNotBlank(adGroupId)
                        && AmznEndpoint.US.getMarketplaceId().equals(shop.getMarketplaceId())
                        && Objects.nonNull(metrics)) {
                    SuggestedBidImpactMetrics.Builder metricsBuilder = SuggestedBidImpactMetrics.newBuilder();
                    if (Objects.nonNull(metrics.getEstimatedImpressionLower())) {
                        metricsBuilder.setEstimatedImpressionLower(metrics.getEstimatedImpressionLower());
                    }
                    if (Objects.nonNull(metrics.getEstimatedImpressionUpper())) {
                        metricsBuilder.setEstimatedImpressionUpper(metrics.getEstimatedImpressionUpper());
                    }
                    bidInfoBuilder.setSuggestedBidImpactMetrics(metricsBuilder.build());
                }

                bidInfos.add(bidInfoBuilder.build());
            }
        }
        respBuilder.addAllBidInfo(bidInfos);
        return respBuilder.build();
    }

    private <T> List<T> getPage(List<T> list, int pageNo, int pageSize) {
        List<T> page = new ArrayList<>();

        if (pageNo <= 0 || pageSize <= 0) {
            throw new IllegalArgumentException("Page number and page size must be greater than zero.");
        }

        int totalSize = list.size();
        int totalPage = totalSize == 0 ? 0 : totalSize / pageSize + 1;
        //如果当前页大于总页数,则显示最后一页
        pageNo = Math.min(pageNo, totalPage);
        //如果当前页小于0,则显示第一页
        pageNo = (Math.max(pageNo, 1));

        int start = (pageNo - 1) * pageSize;
        int end = Math.min(totalSize, start + pageSize);

        if (start >= totalSize) {
            return page; // Return empty list if start index is beyond the end of the list
        }

        page.addAll(list.subList(start, end));
        return page;
    }

    public <T> boolean hasNextPage(List<T> list, int pageNo, int pageSize) {
        if (pageNo <= 0 || pageSize <= 0) {
            throw new IllegalArgumentException("Page number and page size must be greater than zero.");
        }

        int totalSize = list.size();
        int start = (pageNo - 1) * pageSize;
        return start + pageSize < totalSize;
    }

}
