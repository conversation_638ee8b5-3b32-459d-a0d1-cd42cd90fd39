package com.meiyunji.sponsored.service.multiPlatform.shop.service;

import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.service.multiPlatform.shop.po.MultiPlatformShopAuth;

import java.util.List;
import java.util.Map;

/**
 * @author: ys
 * @date: 2025/2/24 11:31
 * @describe:
 */
public interface IMultiPlatformShopService {

    List<MultiPlatformShopAuth> getWalmartAdShopList(Integer puid);

    MultiPlatformShopAuth getTiktokAdShopAuth(Integer puid, Integer shopId);

    List<Integer> getAdAuthShopIdList(Integer puid, List<Integer> idList, String multiPlatformType);

    List<Integer> getTiktokAdShopAuthList(Integer puid, List<Integer> shopIdList);

    MultiPlatformShopAuth getMultiPlatformShopById(Integer puid, Integer shopId);

    /**
     * 根据id获取店铺名称，方便用于列表页反查
     * @param puid
     * @param shopIds
     * @return
     */
    Map<Integer, String> getShopName(Integer puid, List<Integer> shopIds);

    MultiPlatformShopAuth checkAdShopAuth(Integer puid, Integer shopId) throws ServiceException;

    List<Integer> getWalmartAdAuthByShopIdList(Integer puid, List<Integer> shopIdList);

    List<MultiPlatformShopAuth> listAdAuthByPuidAndPlatform(Integer puid, List<Integer> shopId, String multiPlatformType);
}
