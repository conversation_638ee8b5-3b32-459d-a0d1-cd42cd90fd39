package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.common.enums.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * Created by xp on 2021/4/14.
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class GroupPageParam extends AdPageSearchParam{
    @ApiModelProperty("广告组Id")
    private String groupId;
    private List<String> groupIds;
    private List<String> tagGroupIds;
    private String campaignId;
    private Long dxmCampaignId;
    private Long adTagId;
    private String campaignIds;
    private String marketplaceId;
    private List<Long> adTagIdList;
    private String uuid;
    private String currency;
    private Integer uid;

    private String multiCampaignId;
    private String multiGroupId;
    /**
     * 广告策略类型
     */
    private List<String> adStrategyTypeList;
    /**
     * 经过广告策略筛选后的广告活动id集合
     */
    private List<String> autoRuleIds;

    /** 查询数据类型 {@link com.meiyunji.sponsored.service.enums.SearchDataTypeEnum}*/
    @ApiModelProperty("查询数据类型")
    private Integer searchDataType;

    private String searchType;

    private List<String> searchValueList;

    private Boolean isAdmin;
    private Pair<Boolean, List<String>> checkProductRightPair;

    public enum SearchFieldEnum implements BaseEnum {

        NAME("name","name");
        ;

        private String field;
        private String column;

        SearchFieldEnum(String field, String column) {
            this.field = field;
            this.column = column;
        }

        public String getColumn() {
            return column;
        }

        public String getField() {
            return field;
        }


        @Override
        public String getCode() {
            return field;
        }

        @Override
        public String getDescription() {
            return column;
        }
    }
}
