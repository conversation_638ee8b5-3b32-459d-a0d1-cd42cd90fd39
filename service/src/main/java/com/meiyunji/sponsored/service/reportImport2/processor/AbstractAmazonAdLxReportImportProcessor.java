package com.meiyunji.sponsored.service.reportImport2.processor;

import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.reportImport2.vo.AmazonAdReportImportMessage;
import com.meiyunji.sponsored.service.reportImport2.modle.BaseAmazonAdLxReport;

import java.io.IOException;
import java.util.List;

/**
 * 些抽象类,主要封装一些公共查询方法,比如查询活动和广告组
 * 继承此接口,自定义importReport处理逻辑
 */

public abstract class AbstractAmazonAdLxReportImportProcessor<T extends BaseAmazonAdLxReport> {


    protected final IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    protected final IAmazonAdGroupDao amazonAdGroupDao;
    protected final IAmazonSdAdGroupDao amazonSdAdGroupDao;
    protected final IAmazonSbAdGroupDao amazonSbAdGroupDao;
    protected final IScVcShopAuthDao shopAuthDao;
    protected final CosBucketClient tempBucketClient;

    protected AbstractAmazonAdLxReportImportProcessor(
            IAmazonAdCampaignAllDao amazonAdCampaignAllDao,
            IAmazonAdGroupDao amazonAdGroupDao,
            IAmazonSdAdGroupDao amazonSdAdGroupDao,
            IAmazonSbAdGroupDao amazonSbAdGroupDao,
            IScVcShopAuthDao shopAuthDao,
            CosBucketClient tempBucketClient) {
        this.amazonAdCampaignAllDao = amazonAdCampaignAllDao;
        this.amazonAdGroupDao = amazonAdGroupDao;
        this.amazonSdAdGroupDao = amazonSdAdGroupDao;
        this.amazonSbAdGroupDao = amazonSbAdGroupDao;
        this.shopAuthDao = shopAuthDao;
        this.tempBucketClient = tempBucketClient;
    }


    /**
     * 执行lx导入报告
     *
     * @param importMessage 导入报告任务消息
     */
    public abstract void importReport(AmazonAdReportImportMessage importMessage);

}
